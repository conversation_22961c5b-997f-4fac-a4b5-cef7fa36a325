<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <script src="https://multiverse.vothing.com/js/comlink.min.js"></script>
    <script src="https://multiverse.vothing.com/multiverse.js"></script>
    <style>
        htme, body {
            width: 100%;
            height: 100%;
            padding: 0;
            margin: 0;
            overflow: hidden;
        }

        #renderDom1 {
            height: 100vh;
        }

        :root {
            --anchor-rotate: 0deg;
        }

        .twoDimensionCamera {
            width: 30px;
            position: relative;
            transform: rotate(var(--anchor-rotate))
        }

        .twoDimensionCamera .operationArea {
            width: 100%;
            height: 30px;
            display: flex;
            flex-direction: column;
            position: relative;
            z-index: 1;
        }

        .twoDimensionCamera .operationArea .rotate {
            flex-grow: 1;
            cursor: ew-resize;
        }

        .twoDimensionCamera .operationArea .translate {
            flex-grow: 1;
            cursor: move;
            pointer-events: auto;
        }

        .twoDimensionCamera img {
            position: absolute;
            left: 0;
            top: 0
        }
        .twoDimensionCamera .make-weight {
            width: 100%;
            height: 30px;
            pointer-events: none;
        }
    </style>

</head>
<body>
<div id='renderDom1'></div>
</body>
<script>
    window.onload = function () {

        GetQueryString = (name) => {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]);
            return null;
        }
        const parentWindow = window.parent;

        const vaultID = GetQueryString('projectID') || 'MV'
        const isProduct = GetQueryString('isProduct')
        let tiltNum = 0
        let cameraPosZ = 0

        // 获取承载场景画布dom
        let renderDom1 = window.document.getElementById('renderDom1')
        // 初始化场景
        ml1 = new window.multiverse.mvCore(renderDom1)
        ml1.path = parentWindow.IP_CONFIG.SOURCES_URL

        let scene1 = null
        window.scene1 = null


        //鼠标移入三维场景，初始化监听相机变化
        parentWindow.document.querySelector('#renderDom').onmouseenter = function () {
            parentWindow.scene.mv.events.cameraChanged.on('change', leftCameraMainly)
        }

        //鼠标移出三维场景，注销相机变化监听
        //目的是为了在操作二维视图时，防止事件进入无限循环
        parentWindow.document.querySelector('#renderDom').onmouseleave = function () {
            parentWindow.scene.mv.events.cameraChanged.off('change', leftCameraMainly)
        }

        scene1 = ml1.initialize().then(s => {
            // 返回场景对象
            window.scene1 = scene1 = s;
            window.document.querySelector('#viewCube').style.display = 'none'


            /**
             * @function addFeature
             * @description 添加场景元素
             * @param type {String} 元素类型
             * @param id {String} 元素id(可选项)
             * @return Map 返回元素对象，Map类型
             * @example scene.addFeature(type,id):Map
             */
            let serverhttp = window.location.origin + '/MODEL_URL'
            if (isProduct) {
                serverhttp = parentWindow.IP_CONFIG.MODEL_URL
            }
            let model = window.scene1.addFeature('model');
            const interactiveData = parentWindow.interactiveData
            //指定模型服务地址
            model.server = serverhttp;
            // 指定vaultID
            model.vaultID = vaultID;//项目id

            model.modelID = interactiveData.modelID
            // 模型版本
            model.version = 0;
            // 基点的经纬度坐标
            model.origin = interactiveData.origin;
            // 基点的高程
            model.altitude = interactiveData.altitude;
            // 基点的正北旋转角度
            model.rotation = interactiveData.rotation;
            // 相对于基点的XYZ偏移
            model.offset = interactiveData.offset;
            // 加载模型
            model.load().then(() => {
                //加载指定二维视图
                model.activeView(interactiveData.id).then(() => {
                    // 加载成功后，定位到当前模型
                    window.scene1.fit2Feature(model);
                    cameraPosZ = model.AABBWorld.realMin.z + 1.5
                })
            });

            // 添加锚点
            addAnchor()
            // 禁止场景旋转
            scene1.mv.controller.enableRotate = false
            scene1.mv.status.rotatable = false

            // 初始化二维对象点击事件
            selectedSynchronization()

            leftCameraMainly()
        });


        // 拾取同步
        selectedSynchronization = () => {
            //开启二维视图对象单击选中监听，返回被选中的对象的ID。
            window.scene1.mv.events.pickFinished.on('default', selectedFn)
        };

        selectedFn = (e) => {
            // 清除选中效果
            parentWindow.scene.clearSelection()
            parentWindow.scene.render()
            if (e != undefined) {
                // 根据id获取构件对象
                const objectId = e.split('^')[1]
                const _id = `${parentWindow.interactiveData.parentID}^${objectId}`
                let selected = parentWindow.scene.findObject(_id)
                // 如果拾取了二维注释，比如轴网、标高
                if (selected == undefined) {
                    parentWindow.interactiveNotice()
                    return false;
                }

                // 使三维模型构件选中
                parentWindow.scene.findObject(_id).selected = true
                // 聚焦构件
                parentWindow.scene.fit([_id])
                parentWindow.toggleElementMenu('model')
            }

        }

        // 拖动左侧场景时
        leftCameraMainly = () => {
            let pos = parentWindow.scene.mv.THREE_Camera.position
            //传入三维坐标，返回地理坐标（经纬度）
            let geographical = parentWindow.scene.mv.tools.coordinate.vector2mercator(pos, false)

            // 改变锚点位置
            annotion.origin = [geographical[0], geographical[1]]; //经纬度

            // 根据相机的弧度，转换为角度
            let angle = parentWindow.scene.mv.controller.azimuthAngle * 180 / Math.PI

            // 设置二维相机锚点的旋转角度
            document.documentElement.style.setProperty('--anchor-rotate',`${-1 * angle}deg`);

            RotateParams.resultAngle = -1 * angle
        }

        // 拖动锚点时，会不断触发该方法，并传入锚点的left和top值
        rightCameraMainly = (clientX, clientY) => {

            //传入屏幕坐标，返回三维坐标
            let position = scene1.mv.tools.coordinate.screen2world(
                [clientX, clientY],
                false
            )

            // 控制左侧三维场景相机移动
            let THREE = parentWindow.scene.mv._THREE;
            let direction = parentWindow.scene.mv.THREE_Camera.getWorldDirection(new THREE.Vector3());

            // 相机的高度
            // position.y += 5;
            position.y = cameraPosZ;
            let pp = new THREE.Vector3();
            pp.set(position.x, position.y, position.z)
            let newTarget = pp.add(direction);
            newTarget.y = position.y;
            //获取当前底图是否已经加载
            let sceneUnderlayState = parentWindow.scene.features.has('underlay')
            if (sceneUnderlayState) {
                tiltNum = 0.05
            } else {
                tiltNum = 0
            }

            parentWindow.scene.mv.controller.setFocalOffset(0, 0, 0, false);
            parentWindow.scene.mv.controller.setLookAt(position.x, position.y, position.z, newTarget.x, newTarget.y - tiltNum, newTarget
                .z, false);
            parentWindow.scene.mv.controller.update(0);
            parentWindow.scene.mv.THREE_Camera.updateMatrixWorld();
            parentWindow.scene.mv.events.cameraChanged.emit('change');
            parentWindow.scene.render();
        }

        // 传入三维坐标，返回屏幕坐标
        getScreenCoords = (position) => {
            let camera = parentWindow.scene.mv.THREE_Camera
            let rect = parentWindow.scene.mv.mainCanvas.getBoundingClientRect();
            let widthHalf = rect.width / 2;
            let heightHalf = rect.height / 2;
            let vector = new parentWindow.scene.mv._THREE.Vector3().copy(position);
            vector.project(camera);

            return new parentWindow.scene.mv._THREE.Vector2((vector.x * widthHalf) + widthHalf + rect.left,
                -(vector.y * heightHalf) + heightHalf + rect.top);
        }


        let annotion = null
        addAnchor = () => {
            //将经纬度转换为三维坐标
            annotion = window.scene1.addFeature('annotation');
            annotion.origin = [0, 0]; //经纬度
            annotion.altitude = 0; //高程
            annotion.offset = [0, 0, 0];

            let data = {
                content: '',
                htmlCode: `
                <div class="twoDimensionCamera">
                    <div class="operationArea">
                        <div class="rotate"></div>
                        <div class="translate"></div>
                    </div>
                    <img style="width:100%;height:30px;" id="anchor-cyb" src="http://example.vothing.com/img/CameraAngleView.png">
                    <div class="make-weight"></div>
                </div>
            `,
                cssCode: '',
                jsCode: '',
                // visibleDistance: 1 / 0,
                minDistance: 0,
                maxDistance: 1 / 0,
                setType: {
                    anchorType: "custom",
                    annotationRadio: "0",
                    checkedImg: 1,
                    checkedImgSize: {
                        width: 30,
                        height: 30
                    },
                    type: "panel"
                }
            }

            annotion.dataKey = 'annotation-' + annotion.id
            scene1.postData(data, annotion.dataKey)
            annotion.load();

            setTimeout(() => {
                let pos = parentWindow.scene.mv.THREE_Camera.position
                //传入三维坐标，返回地理坐标（经纬度）
                let geographical = parentWindow.scene.mv.tools.coordinate.vector2mercator(pos, false)

                // 改变锚点位置
                annotion.origin = [geographical[0], geographical[1]]; //经纬度
                setAnchorEvent()
            }, 1000)

        }


        let parentDom = document.querySelector('#renderDom1')
        // 锚点平移相关变量
        let TranslateParams = {
            boundaryLeft: parentDom.offsetLeft,
            boundaryTop: parentDom.offsetTop, // 获取二维视图dom的边界
            originStartPos: null,
            startPos: null, // 开始拖动时的真实坐标
            endMoveClient: [0, 0],
        }

        // 锚点旋转相关变量
        let RotateParams = {
            pointA: {
                X: 0, // 元素中心点 元素1/2自身宽高 + 元素的定位
                Y: 0
            }, //中心点坐标
            pointB: {}, //起始点坐标
            pointC: {}, //结束点坐标
            count: 0,
            anchorAngle: 0,
            resultAngle: 0, //最终旋转角度
            endMoveOffset: {
                x: 0, y: 0
            },//每次旋转完毕后，锚点的offset
        }
        // 设置锚点的拖动事件
        setAnchorEvent = () => {

            let movement = ''; //当前操作的类型：平移。旋转。
            parentDom.onmousedown = function (ev) {
                if (ev.target.className == 'translate') {
                    movement = 'translate'
                    // 锚点拖拽平移
                    setAnchorMoveTranslate(ev, TranslateParams)
                }

                if (ev.target.className == 'rotate') {
                    movement = 'rotate'
                    document.documentElement.style.setProperty('--anchor-rotate',`${RotateParams.resultAngle}deg`);
                    //锚点拖拽旋转
                    setAnchorMoveRotate(ev, RotateParams)
                }
            }

            parentDom.onmouseup = function (ev) {
                parentDom.onmousemove = null;

                if (movement == 'translate') {
                    // 方法1、取锚点的二维中心点坐标，转换为经纬度，重新给锚点赋值
                    // // 获取鼠标抬起的那一刻的位置。根据屏幕坐标 返回三维坐标
                    // let originEndPos = scene1.mv.tools.coordinate.screen2world(
                    //  TranslateParams.endMoveClient,
                    //  false
                    // )

                    // 方法二，取左侧三维相机的位置，转换为经纬度，重新给锚点赋值
                    let pos = parentWindow.scene.mv.THREE_Camera.position
                    //传入三维坐标，返回地理坐标（经纬度）
                    let geographical = parentWindow.scene.mv.tools.coordinate.vector2mercator(pos, false)

                    // 改变锚点位置
                    annotion.origin = [geographical[0], geographical[1]]; //经纬度
                    scene1.render()

                    document.documentElement.style.setProperty('--anchor-rotate',`${RotateParams.resultAngle}deg`);
                }

                if (movement == 'rotate') {
                    // 旋转完毕记录当前锚点的位置(因为锚点可能发生移动，坐标发生变化)
                    let box = document.querySelector('.twoDimensionCamera')
                    RotateParams.endMoveOffset.x = box.parentElement.offsetLeft
                    RotateParams.endMoveOffset.y = box.parentElement.offsetTop
                }

                movement = ''
            }
        }

        //锚点拖拽平移事件
        setAnchorMoveTranslate = (ev, TranslateParams) => {
            // 更新一下相机位置
            scene1.mv.THREE_Camera.updateMatrixWorld();

            // 获取鼠标按下的那一刻的位置。根据屏幕坐标 返回三维坐标
            TranslateParams.originStartPos = scene1.mv.tools.coordinate.screen2world(
                [ev.clientX, ev.clientY],
                false
            )
            // 转换为真实坐标
            TranslateParams.startPos = scene1.mv.tools.coordinate.realPosition(TranslateParams.originStartPos)

            let anchorDom = document.querySelector('.twoDimensionCamera').parentElement;
            ev = window.event || ev;
            //获取左部和顶部的偏移量
            let originX = ev.clientX - anchorDom.offsetLeft
            let originY = ev.clientY - anchorDom.offsetTop

            parentDom.onmousemove = function (ev) {
                ev = window.event || ev;

                // 鼠标拖动过程中，如果移出了二维视图，则给图片设置为边界值
                let offsetX = TranslateParams.boundaryLeft
                let offsetY = TranslateParams.boundaryTop

                // 在二维视图内拖动，改变图片的top值
                if (ev.clientX - TranslateParams.boundaryLeft > 0) {
                    offsetX = ev.clientX - originX
                }

                // 在二维视图内拖动，改变图片的left值
                if (ev.clientY - TranslateParams.boundaryTop > 0) {
                    offsetY = ev.clientY - originY
                }


                let anchorDom = document.querySelector('.twoDimensionCamera').parentElement;
                anchorDom.style.left = offsetX + 'px'
                anchorDom.style.top = offsetY + 'px'
                TranslateParams.endMoveClient = [offsetX + 15, offsetY + 25]

                rightCameraMainly(offsetX + 15, offsetY + 26)
            }
        }

        //锚点拖拽旋转事件
        setAnchorMoveRotate = (ev, RotateParams) => {
            let box = document.querySelector('.twoDimensionCamera')
            // 鼠标按下，重新计算中心点位置(因为锚点可能发生移动，坐标发生变化)
            RotateParams.pointA.X = box.parentElement.clientWidth / 2 + box.parentElement.offsetLeft
            RotateParams.pointA.Y = box.parentElement.clientHeight / 2 + box.parentElement.offsetTop

            // 以鼠标第一次落下的点为起点
            if (RotateParams.count < 1) {
                RotateParams.pointB.X = ev.pageX;
                RotateParams.pointB.Y = ev.pageY;
                RotateParams.count++
            } else {
                // 重新计算旋转点参考量(因为锚点可能发生移动，坐标发生变化)
                RotateParams.pointB.X = RotateParams.pointB.X + (box.parentElement.offsetLeft - RotateParams.endMoveOffset.x);
                RotateParams.pointB.Y = RotateParams.pointB.Y + (box.parentElement.offsetTop - RotateParams.endMoveOffset.y);
            }

            parentDom.onmousemove = function (ev) {
                RotateParams.pointC.X = ev.pageX;
                RotateParams.pointC.Y = ev.pageY; // 获取结束点坐标

                // 计算出旋转角度
                let AB = {};
                let AC = {};
                AB.X = (RotateParams.pointB.X - RotateParams.pointA.X);
                AB.Y = (RotateParams.pointB.Y - RotateParams.pointA.Y);
                AC.X = (RotateParams.pointC.X - RotateParams.pointA.X);
                AC.Y = (RotateParams.pointC.Y - RotateParams.pointA.Y); // 分别求出AB,AC的向量坐标表示
                let direct = (AB.X * AC.Y) - (AB.Y * AC.X); // AB与AC叉乘求出逆时针还是顺时针旋转
                let lengthAB = Math.sqrt(Math.pow(RotateParams.pointA.X - RotateParams.pointB.X, 2) +
                    Math.pow(RotateParams.pointA.Y - RotateParams.pointB.Y, 2));
                let lengthAC = Math.sqrt(Math.pow(RotateParams.pointA.X - RotateParams.pointC.X, 2) +
                    Math.pow(RotateParams.pointA.Y - RotateParams.pointC.Y, 2));
                let lengthBC = Math.sqrt(Math.pow(RotateParams.pointB.X - RotateParams.pointC.X, 2) +
                    Math.pow(RotateParams.pointB.Y - RotateParams.pointC.Y, 2));
                let cosA = (Math.pow(lengthAB, 2) + Math.pow(lengthAC, 2) - Math.pow(lengthBC, 2)) /
                    (2 * lengthAB * lengthAC); //   余弦定理求出旋转角
                let angleA = Math.round(Math.acos(cosA) * 180 / Math.PI);
                if (direct < 0) {
                    RotateParams.resultAngle = -angleA; //叉乘结果为负表示逆时针旋转， 逆时针旋转减度数
                } else {
                    RotateParams.resultAngle = angleA; //叉乘结果为正表示顺时针旋转，顺时针旋转加度数
                }

                RotateParams.resultAngle += RotateParams.anchorAngle
                document.documentElement.style.setProperty('--anchor-rotate',`${RotateParams.resultAngle}deg`);

                // 将角度转换为弧度 传给三维相机
                parentWindow.scene.mv.controller.azimuthAngle = -1 * (Math.PI / 180) * RotateParams.resultAngle
            }

        }
    }
</script>
</html>