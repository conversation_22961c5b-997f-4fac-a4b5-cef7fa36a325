.-rotate {
	cursor: url('./icon/Rotate.png'), pointer !important;
}
.-rotating {
	cursor: url('./icon/RotateActive.png'), pointer !important;
}
.-dragging {
	cursor: url('./icon/Pan_active.png'), pointer !important;
}
.-moving {
	cursor: url('./icon/Pan.png'), pointer !important;
}
.-zoomIn {
	cursor:url('./icon/motion-zoom.png') ,auto !important;
}
.measure-length:hover.measure-length.enable-hover {
  box-shadow: 0 0 16px 3px rgba(0, 117, 210, .7);
  -webkit-box-shadow: 0 0 16px 3px rgba(0, 117, 210, .7);
  -moz-box-shadow: 0 0 16px 3px rgba(0, 117, 210, .7)
}

.measure-length {
  display: none;
  position: absolute;
  height: 19px;
  left: 0;
  top: 0;
  padding: 2px 0 2px 0;
  background-color: #009bff;
  box-shadow: 0 1px 3px 1px rgba(0, 0, 0, .25);
  color: #f4f4f4;
  font-size: 12px;
  text-align: center;
  line-height: 1.2;
  white-space: nowrap;
  border-radius: 10px;
  pointer-events: none;
  cursor: pointer;
  z-index: 2;
  -webkit-tap-highlight-color: transparent
}

.measure-length.visible {
  display: block
}

.measure-length.fetching-topology {
  background-color: rgba(34, 34, 34, .8);
  box-shadow: 0 .5px 1.5px 0 rgba(0, 0, 0, .3)
}

.measure-length-text {
  display: inline-block;
  margin: 0 5px 0 5px;
  vertical-align: middle;
  font-size: 16px;
  pointer-events: none
}

.measure-delta-text {
  margin: 0 5px 0 5px;
  align-self: center;
  font-size: 14px;
  overflow: hidden;
  white-space: nowrap;
  height: 16px;
  line-height: 16px;
  vertical-align: middle;
  pointer-events: none
}

.measure-length-button {
  display: inline-block;
  width: 16px;
  height: 16px;
  font-size: 16px;
  float: right;
  color: #fff;
  opacity: .7
}

.measure-length-button:hover {
  background-color: rgba(166, 194, 255, .7);
  -webkit-transition: all .2s ease;
  -moz-transition: all .2s ease;
  -ms-transition: all .2s ease;
  -o-transition: all .2s ease;
  transition: all .2s ease
}

.measure-label.on-edit, .measure-label:hover.measure-label.enable-hover {
  box-shadow: 0 0 4px 4px #0d69ca;
  -webkit-box-shadow: 0 0 4px 4px #0d69ca;
  -moz-box-shadow: 0 0 4px 4px #0d69ca
}

.measure-label.editable {
  pointer-events: all
}

.measure-label {
  display: none;
  position: absolute;
  width: 7px;
  height: 7px;
  cursor: move;
  pointer-events: none;
  border-radius: 100px
}

.measure-label-hit-area {
  display: inline-block;
  position: absolute;
  width: 44px;
  height: 44px;
  left: -22px;
  top: -22px;
  border-radius: 100px;
  z-index: 2
}

.measure-label-icon {
  display: inline-block;
  position: absolute;
  width: 7px;
  height: 7px;
  left: -2px;
  top: -2px;
  background-color: #009bff;
  border-style: solid;
  border-width: 2px;
  border-color: #fff;
  border-radius: 100px;
  box-shadow: 0 2px 5px 0 #182a3d;
  z-index: 2
}

.measure-label.visible {
  display: block
}

.measure-label-text {
  display: inline-block;
  margin: 0 5px 0 5px;
  vertical-align: middle
}

.measure-horizontal-divider {
  height: 1px;
  border-top: 1px solid rgba(200, 200, 200, .8)
}

.measure-submenu-select {
  display: block;
  position: relative;
  width: calc(100% - 40px);
  padding: 10px 20px 0 20px;
  opacity: .3
}

.option-drop-down:disabled {
  opacity: .3
}

.measure-submenu-selectlabel {
  position: relative;
  display: inline-block;
  padding-right: 20px;
  padding-bottom: 4px
}

.measure-results {
  position: relative;
  display: block;
  padding: 5px 0 10px 30px
}

.measure-result-label {
  display: inline-block;
  padding-right: 10px
}

.measure-result-number {
  display: inline-block
}

.measure-delta-collapse {
  position: absolute;
  right: 20px;
  display: inline-block;
  background-color: rgba(255, 255, 255, .12);
  margin: 0 4px 0 4px;
  padding: 0 4px 0 4px;
  cursor: pointer;
  text-align: center
}

.measure-delta-collapse:hover {
  background-color: rgba(166, 194, 255, .7);
  -webkit-transition: all .2s ease;
  -moz-transition: all .2s ease;
  -ms-transition: all .2s ease;
  -o-transition: all .2s ease;
  transition: all .2s ease
}

.measure-selection-repick {
  position: absolute;
  right: 20px;
  display: inline-block;
  background-color: rgba(255, 255, 255, .12);
  margin: 4px 4px 4px 4px;
  padding: 4px 4px 4px 4px;
  cursor: pointer;
  text-align: center
}

.measure-selection-repick:hover {
  background-color: rgba(166, 194, 255, .7);
  -webkit-transition: all .2s ease;
  -moz-transition: all .2s ease;
  -ms-transition: all .2s ease;
  -o-transition: all .2s ease;
  transition: all .2s ease
}

.measure-restart {
  margin: 10px 20px 15px 20px;
  padding: 6px 10px 6px 10px;
  width: calc(100% - 55px);
  cursor: pointer;
  text-align: center;
  border-style: solid;
  border-width: 1px;
  border-color: rgba(255, 255, 255, .2);
  border-radius: 3px
}

.docking-panel:hover .measure-restart {
  background-color: rgba(255, 255, 255, .12)
}

.docking-panel .measure-restart:hover {
  background-color: rgba(166, 194, 255, .7);
  -webkit-transition: all .2s ease;
  -moz-transition: all .2s ease;
  -ms-transition: all .2s ease;
  -o-transition: all .2s ease;
  transition: all .2s ease
}

.measure-panel-title {
  padding-top: 0;
  padding-left: 20px;
  margin-top: 10px
}

.measure-panel-title:hover {
  cursor: pointer
}

.measure-panel-title.collapsed {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyBpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMC1jMDYwIDYxLjEzNDc3NywgMjAxMC8wMi8xMi0xNzozMjowMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNSBXaW5kb3dzIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOkEwMDhBMzY4QTYyODExRTM5REY0Q0MwMTM3MTVFOTMzIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOkEwMDhBMzY5QTYyODExRTM5REY0Q0MwMTM3MTVFOTMzIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6QTAwOEEzNjZBNjI4MTFFMzlERjRDQzAxMzcxNUU5MzMiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6QTAwOEEzNjdBNjI4MTFFMzlERjRDQzAxMzcxNUU5MzMiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz6WHWu4AAAAcElEQVR42mL8//8/AzmAiYFMgEtjGSFDcUmGAfEEIOYix6lWQLwUiGXJ8SNI00IgNiIncPiAeCoQ+5ITqsxQA+CAhQhN34C4AoiPkaLxGRAXAfEdUvx4DohjsGkCA1CSw4LrgZgVhxwYM9I9rQIEGACZNDs01yUXlAAAAABJRU5ErkJggg==);
  background-repeat: no-repeat
}

.measure-panel-title.expanded {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyBpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMC1jMDYwIDYxLjEzNDc3NywgMjAxMC8wMi8xMi0xNzozMjowMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNSBXaW5kb3dzIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOkE3QzFFQUQ1QTYyODExRTM5NzE2RjcyN0QxQjg0QTREIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOkE3QzFFQUQ2QTYyODExRTM5NzE2RjcyN0QxQjg0QTREIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6QTdDMUVBRDNBNjI4MTFFMzk3MTZGNzI3RDFCODRBNEQiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6QTdDMUVBRDRBNjI4MTFFMzk3MTZGNzI3RDFCODRBNEQiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz6XuY60AAAARklEQVR42mL8//8/AzmAiYFMQBeNqSg8kB+JwKlAfAZZjIlIm9JJdSpWTYQ04tSETyNeTbg0EtSETSNRmkCAcegkOYAAAwDGjDZHvY373AAAAABJRU5ErkJggg==);
  background-repeat: no-repeat
}

.measure-table td {
  padding: 10px 0 10px 20px
}

.measure-selection-one-label {
  display: inline-block;
  margin: 18px 18px 18px 18px;
  width: 16px;
  height: 16px;
  font-size: 16px;
  float: left;
  padding: 2px;
  background-color: #03f;
  border-radius: 10px;
  opacity: .6
}

.measure-selection-two-label {
  display: inline-block;
  margin: 18px 18px 18px 18px;
  width: 16px;
  height: 16px;
  font-size: 16px;
  float: left;
  padding: 2px;
  background-color: #03f;
  border-radius: 10px;
  opacity: .6
}

.measure-repick {
  display: inline-block;
  cursor: default;
  margin: 20px 20px 20px 20px;
  width: 16px;
  height: 16px;
  font-size: 16px;
  float: right;
  opacity: .5
}

.measure-repick:hover {
  opacity: 1
}

.measure-selection-result {
  display: inline-block;
  margin: 21px 0 21px 0
}

.measure-distance-icon {
  display: inline-block;
  margin: 20px 2px 20px 2px;
  font-size: 52px;
  color: #f90;
  float: left
}

.measure-angle-icon {
  display: inline-block;
  margin: 20px 2px 20px 2px;
  font-size: 52px;
  color: #f90;
  float: left
}

.measure-result {
  font-size: 20px;
  color: #f90;
  display: inline-block;
  margin: 38px 0 38px 0
}

.measure-initial {
  font-size: 20px;
  display: inline-block;
  margin: 38px 0 38px 56px
}

.measure-settings-button {
  cursor: default;
  position: relative;
  top: 20px;
  right: 20px;
  width: 16px;
  height: 16px;
  font-size: 18px;
  float: right;
  opacity: .5
}

.measure-settings-button:hover {
  opacity: 1
}

.measure-delta-button {
  position: relative;
  top: 56px;
  right: 4px;
  width: 16px;
  height: 16px;
  font-size: 16px;
  float: right;
  opacity: .5;
  -webkit-transform: rotate(-90deg);
  -moz-transform: rotate(-90deg);
  -ms-transform: rotate(-90deg);
  -o-transform: rotate(-90deg);
  transform: rotate(-90deg)
}

.measure-delta-button.rotated {
  -webkit-transform: rotate(90deg);
  -moz-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  -o-transform: rotate(90deg);
  transform: rotate(90deg)
}

.measure-delta-button:hover {
  opacity: 1
}

.measure-delta-result {
  display: inline-block;
  margin: 0 0 20px 0;
  padding: 0 0 0 56px
}

.measure-label-axis {
  display: none;
  position: absolute;
  width: 16px;
  height: 16px;
  left: 0;
  top: 0;
  background-color: #f4f4f4;
  box-shadow: 0px 2px 4px 0px;
  border-radius: 10px;
  pointer-events: none
}

.measure-label-axis-icon.X {
  background-color: #ec4545
}

.measure-label-axis-icon.Y {
  background-color: #45c345
}

.measure-label-axis-icon.Z {
  background-color: #5757da
}

.measure-label-axis-icon {
  /*width: 20px;*/
  height: 18px;
  color: #f4f4f4;
  border-radius: 6px 0 0 8px;
  font-size: 12px;
  padding-top: 4px;
  line-height: 15px;
  text-align: center
}

.measure-label-axis-delta.enableTransition {
  transition: opacity .1s linear
}

.measure-label-axis-delta {
  display: none;
  position: absolute;
  font-size: 18px;
  left: 0;
  top: 0;
  background-color: #fff;
  pointer-events: none;
  opacity: 0;
  border-radius: 8px;
  box-shadow: 0 1px 3px 1px rgba(0, 0, 0, .25)
}

.measure-label-axis.visible {
  display: block
}

.measure-label-axis-delta.visible {
  display: flex
}

.measure-label-axis-x {
  color: #ec4545
}

.measure-label-axis-y {
  color: #45c345
}

.measure-label-axis-z {
  color: #5757da
}

.magnifying-glass.visible {
  display: block
}

.magnifying-glass {
  position: absolute;
  display: none;
  -moz-border-radius: 100%;
  -webkit-border-radius: 100%;
  border-radius: 100%;
  background: 0 0;
  box-shadow: 2px 2px 4px rgba(0, 0, 0, .5), inset 2px 2px 4px rgba(0, 0, 0, .5);
  -webkit-box-shadow: 2px 2px 4px rgba(0, 0, 0, .5), inset 2px 2px 4px rgba(0, 0, 0, .5);
  -moz-box-shadow: 2px 2px 4px rgba(0, 0, 0, .5), inset 2px 2px 4px rgba(0, 0, 0, .5);
  z-index: 4
}

.measure-fetching-topology {
  display: inline-block;
  width: 10px;
  height: 10px;
  background: url(res/icon-spinner-sm.svg);
  margin: 3px 0 -2px 3px;
  animation: measure-topology-fetching-anim 1.4s infinite linear;
  -webkit-animation: measure-topology-fetching-anim 1.4s infinite linear
}

@keyframes measure-topology-fetching-anim {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0)
  }

  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg)
  }
}

@-webkit-keyframes measure-topology-fetching-anim {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0)
  }

  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg)
  }
}

.docking-panel.measure-settings-popup {
  width: 328px;
  height: 150px;
  display: block;
  bottom: 120%;
  box-sizing: border-box;
  text-align: left;
  position: absolute
}

.docking-panel.measure-settings-popup-small {
  height: 105px
}

.measure-settings-popup tbody {
  top: 15px;
  position: absolute
}

.measure-toolbar-seperator {
  height: 18px;
  width: 1px;
  background-color: #999;
  float: left;
  margin: 16px 5px
}

.measure-selection-area {
  display: block;
  position: absolute;
  cursor: pointer;
  z-index: 1;
  -webkit-tap-highlight-color: transparent
}

@font-face {
  font-family: calibration-icon;
  src: url(data:application/octet-stream;base64,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) format('woff');
  font-weight: 400;
  font-style: normal
}

[class*=" calibration-icon"], [class^=calibration-icon] {
  font-family: calibration-icon;
  font-style: normal;
  font-weight: 400;
  font-variant: normal;
  line-height: 1;
  padding-top: 2px;
  speak: none;
  text-transform: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale
}

.calibration-icon:before {
  content: 'a'
}

.calibration-panel {
  left: calc(50% - 190px);
  top: calc(50% - 145px);
  width: 300px;
  height: auto
}

.calibration-panel .option-drop-down, .measure-settings-popup .option-drop-down {
  width: calc(100% - 33px);
  margin-left: 18px
}

.calibration-panel .adsk-lmv-tftable tr td, .measure-settings-popup .adsk-lmv-tftable tr td {
  width: auto
}

.calibration-textbox::-webkit-inner-spin-button, .calibration-textbox::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0
}

.calibration-text {
  text-align: left;
  padding: 6px 10px 6px 10px;
  margin: 20px 20px 14px 20px;
  font-size: 14px;
  -webkit-font-smoothing: antialiased
}

.calibration-button {
  margin-top: 15px
}

.calibration-button-left {
  width: calc(50% - 15px);
  margin-right: 5px !important;
  display: inline-block
}

.calibration-button-right {
  width: calc(50% - 15px);
  margin-left: 5px !important;
  display: inline-block
}

.calibration-line {
  position: absolute;
  display: block;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 0
}

.calibration-label {
  display: none;
  position: absolute;
  pointer-events: none;
  text-align: center;
  vertical-align: middle;
  cursor: default;
  padding: 2px;
  color: #000;
  background: #fff;
  border-radius: 10px;
  -webkit-transform-origin: 50% 53%
}

.calibration-label-text {
  display: inline-block;
  pointer-events: none;
  text-align: center;
  vertical-align: middle;
  font-size: 12px;
  cursor: default;
  padding: 2px;
  color: #000;
  background: #fff;
  border-bottom: 1px dashed #000;
  text-decoration: none;
  text-transform: uppercase;
  -webkit-font-smoothing: antialiased
}

.calibration-label-text:hover {
  color: #039be5;
  border-bottom: 1px dashed #039be5
}

.calibration-label.visible {
  display: block
}

.calibration-endpoint.edit, .calibration-endpoint:hover.calibration-endpoint.enable-hover {
  background-color: #039be5
}

.calibration-endpoint.edit > *, .calibration-endpoint:hover.calibration-endpoint.enable-hover > * {
  color: #fff
}

.calibration-endpoint.editable {
  pointer-events: all
}

.calibration-endpoint {
  display: none;
  position: absolute;
  cursor: move;
  pointer-events: none;
  width: 30px;
  height: 30px;
  background-color: #fff;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, .24), 0 0 8px 0 rgba(0, 0, 0, .12);
  -moz-border-radius: 50px;
  -webkit-border-radius: 50px;
  border-radius: 50px;
  justify-content: center;
  align-items: center;
  text-align: center
}

.calibration-endpoint.visible {
  display: block
}

.calibration-endpoint-text {
  display: inline-block;
  font-size: 14px;
  vertical-align: middle;
  cursor: default;
  margin: 5px 5px 5px 5px;
  vertical-align: middle;
  text-align: center;
  color: #039be5;
  text-decoration: none;
  text-transform: uppercase;
  pointer-events: none
}

.calibration-buttons-wrapper {
  text-align: center
}

.deckcontainer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.deckcontainer > * {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
