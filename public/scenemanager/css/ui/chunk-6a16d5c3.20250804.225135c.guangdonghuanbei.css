.monaco-editor .peekview-widget .head .peekview-title .severity-icon{display:inline-block;vertical-align:text-top;margin-right:.020833rem}.monaco-editor .marker-widget{text-overflow:ellipsis;white-space:nowrap}.monaco-editor .marker-widget>.stale{opacity:.6;font-style:italic}.monaco-editor .marker-widget .title{display:inline-block;padding-right:.026042rem}.monaco-editor .marker-widget .descriptioncontainer{position:absolute;white-space:pre;user-select:text;-webkit-user-select:text;-ms-user-select:text;padding:.041667rem .0625rem 0 .104167rem}.monaco-editor .marker-widget .descriptioncontainer .message{display:flex;flex-direction:column}.monaco-editor .marker-widget .descriptioncontainer .message .details{padding-left:.03125rem}.monaco-editor .marker-widget .descriptioncontainer .message .source,.monaco-editor .marker-widget .descriptioncontainer .message span.code{opacity:.6}.monaco-editor .marker-widget .descriptioncontainer .message a.code-link{opacity:.6;color:inherit}.monaco-editor .marker-widget .descriptioncontainer .message a.code-link:before{content:"("}.monaco-editor .marker-widget .descriptioncontainer .message a.code-link:after{content:")"}.monaco-editor .marker-widget .descriptioncontainer .message a.code-link>span{text-decoration:underline;border-bottom:.005208rem solid transparent;text-underline-position:under}.monaco-editor .marker-widget .descriptioncontainer .filename{cursor:pointer}.monaco-editor .zone-widget .zone-widget-container.reference-zone-widget{border-top-width:.005208rem;border-bottom-width:.005208rem}.monaco-editor .reference-zone-widget .inline{display:inline-block;vertical-align:top}.monaco-editor .reference-zone-widget .messages{height:100%;width:100%;text-align:center;padding:3em 0}.monaco-editor .reference-zone-widget .ref-tree{line-height:.119792rem}.monaco-editor .reference-zone-widget .ref-tree .reference{text-overflow:ellipsis;overflow:hidden}.monaco-editor .reference-zone-widget .ref-tree .reference-file{display:inline-flex;width:100%;height:100%}.monaco-editor .reference-zone-widget .ref-tree .monaco-list:focus .selected .reference-file{color:inherit!important}.monaco-editor .reference-zone-widget .ref-tree .reference-file .count{margin-right:.0625rem;margin-left:auto}.monaco-editor.hc-black .reference-zone-widget .ref-tree .reference-file{font-weight:700}.monaco-editor .goto-definition-link{text-decoration:underline;cursor:pointer}.monaco-editor-hover{cursor:default;position:absolute;overflow:hidden;z-index:50;user-select:text;-webkit-user-select:text;-ms-user-select:text;box-sizing:initial;animation:fadein .1s linear;line-height:1.5em}.monaco-editor-hover.hidden{display:none}.monaco-editor-hover .hover-contents{padding:.020833rem .041667rem}.monaco-editor-hover .markdown-hover>.hover-contents:not(.code-hover-contents){max-width:2.604167rem;word-wrap:break-word}.monaco-editor-hover .markdown-hover>.hover-contents:not(.code-hover-contents) hr{min-width:100vw}.monaco-editor-hover p,.monaco-editor-hover ul{margin:.041667rem 0}.monaco-editor-hover code{font-family:var(--monaco-monospace-font)}.monaco-editor-hover hr{margin-top:.020833rem;margin-bottom:-.03125rem;margin-left:-.052083rem;margin-right:-.052083rem;height:.005208rem}.monaco-editor-hover p:first-child,.monaco-editor-hover ul:first-child{margin-top:0}.monaco-editor-hover p:last-child,.monaco-editor-hover ul:last-child{margin-bottom:0}.monaco-editor-hover ol,.monaco-editor-hover ul{padding-left:.104167rem}.monaco-editor-hover li>p{margin-bottom:0}.monaco-editor-hover li>ul{margin-top:0}.monaco-editor-hover code{border-radius:.015625rem;padding:0 .4em}.monaco-editor-hover .monaco-tokenized-source{white-space:pre-wrap;word-break:break-all}.monaco-editor-hover .hover-row.status-bar{font-size:.0625rem;line-height:.114583rem}.monaco-editor-hover .hover-row.status-bar .actions{display:flex;padding:0 .041667rem}.monaco-editor-hover .hover-row.status-bar .actions .action-container{margin-right:.083333rem;cursor:pointer}.monaco-editor-hover .hover-row.status-bar .actions .action-container .action .icon{padding-right:.020833rem}.monaco-editor-hover .markdown-hover .hover-contents .codicon{color:inherit;font-size:inherit;vertical-align:middle}.monaco-editor-hover .hover-contents a.code-link:before{content:"("}.monaco-editor-hover .hover-contents a.code-link:after{content:")"}.monaco-editor-hover .hover-contents a.code-link{color:inherit}.monaco-editor-hover .hover-contents a.code-link>span{text-decoration:underline;border-bottom:.005208rem solid transparent;text-underline-position:under}