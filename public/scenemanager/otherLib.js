var Ei=(P,c,F)=>{if(!c.has(P))throw TypeError("Cannot "+F)};var De=(P,c,F)=>(Ei(P,c,"read from private field"),F?F.call(P):c.get(P)),Ue=(P,c,F)=>{if(c.has(P))throw TypeError("Cannot add the same private member more than once");c instanceof WeakSet?c.add(P):c.set(P,F)},Ve=(P,c,F,W)=>(Ei(P,c,"write to private field"),W?W.call(P,F):c.set(P,F),F);var oe=(P,c,F)=>(Ei(P,c,"access private method"),F);mvDefine(["exports","./three","./three_ext","./mapbox","./vendor"],function(P,c,F,W,L){var Re,Xe,Ye,rt,ie,Me,st,Ii;"use strict";const U=class U{static setConfigUrl(e,t){U.MODEL_SERVER=e,U.MULTIVERSE_RESOURCE=t}static get MAX_GEOMETRY_MEMORY(){return this.MOBILE_DEVICE==!0?1024:8192}static get MAX_RAYTRACER_FACE(){return this._MAX_RAYTRACER_FACE==-1&&(this.MOBILE_DEVICE==!0?this._MAX_RAYTRACER_FACE=5e4:this._MAX_RAYTRACER_FACE=1e5),this._MAX_RAYTRACER_FACE}static set MAX_RAYTRACER_FACE(e){this._MAX_RAYTRACER_FACE=e}static get GEOMETRY_MEMORY_IN_PORGRESS(){return this.MOBILE_DEVICE==!0?128:1024}};U.MOBILE_DEVICE=!1,U.AMBIENTLIGHT={castShadow:!1,color:16777215,intensity:.8,distance:300,position:[1.75,1.75,1.75]},U.DIRECTIONALLIGHT={castShadow:!1,shadowBias:-1e-4,color:16777215,intensity:.4,shadowMapSizeWidth:512,shadowMapSizeHeight:512,shadowCameraLeft:-64,shadowCameraRight:64,shadowCameraBottom:-64,shadowCameraTop:64,shadowCameraFar:2e3,shadowCameraNear:-.1},U.POINTLIGHT={castShadow:!1,shadowBias:.01,position:[-100,150,300],color:16777215,distance:3e3,intensity:.6},U.MODEL_SERVER="",U.MULTIVERSE_RESOURCE="./",U.LOADING_MODELS=0,U.MAX_LOADING_MODELS=2,U._MAX_RAYTRACER_FACE=-1,U.BASE_FRAMERATE=10,U.PROJECTAREA_RATIO=0,U.MESHAREA_RATIO=.001,U.MAX_GROUP_IN_PROGRESS=750,U.BACKGROUND_COLOR=15658734,U.MOUSE_CLICK_TOLERANCE=2,U.CAMERA_NEAR_INNER=.1,U.CAMERA_NEAR_INNER_2D=.01,U.CAMERA_NEAR_OUTER=5,U.CAMERA_FAR=2e5,U.CAMERA_NEAR_POST_MODE=.1,U.CAMERA_FAR_POST_MODE=2e5,U.RENDERBOUNDARY=99999,U.REFINE_COUNT_LIMIT=20;let Q=U;const $a=1,er=2,tr=3;var ir=c.THREE.Light;class ar extends ir{constructor(e,...t){super(...t),this.map=e,this.isEnvironmentLight=!0}copy(e){super.copy(e),this.map=e.map}}function kt(r,e){const t={};for(const i of e)t[i]=r.getExtension(i);return t}function rr(r,e,t){const i=r.createShader(e);if(r.shaderSource(i,t),r.compileShader(i),r.getShaderParameter(i,r.COMPILE_STATUS))return i;const s=t.split(`
`).map((o,n)=>`${n+1}: ${o}`).join(`
`);throw console.log(s),r.getShaderInfoLog(i)}function sr(r,e,t,i,a){const s=r.createProgram();if(r.attachShader(s,e),r.attachShader(s,t),i&&r.transformFeedbackVaryings(s,i,a),r.linkProgram(s),r.detachShader(s,e),r.detachShader(s,t),r.getProgramParameter(s,r.LINK_STATUS))return s;throw r.getProgramInfoLog(s)}function or(r,e){const t={},i=r.getProgramParameter(e,r.ACTIVE_UNIFORMS);for(let a=0;a<i;a++){const{name:s,type:o}=r.getActiveUniform(e,a),n=r.getUniformLocation(e,s);n&&(t[s]={type:o,location:n})}return t}function nr(r,e){const t={},i=r.getProgramParameter(e,r.ACTIVE_ATTRIBUTES);for(let a=0;a<i;a++){const{name:s}=r.getActiveAttrib(e,a);s&&(t[s]=r.getAttribLocation(e,s))}return t}function lr(r){const e=[],t=[],i=[],a=[];return r.traverse(o=>{o.isMesh?o.geometry?o.material.isMeshStandardMaterial?e.push(o):console.warn(o,"must use MeshStandardMaterial in order to be rendered."):console.warn(o,"must have a geometry property"):o.isDirectionalLight?t.push(o):o.isAmbientLight?i.push(o):o.isEnvironmentLight&&(a.length>1&&console.warn(a,"only one environment light can be used per scene"),cr(o)?a.push(o):console.warn(o,"environment light does not use color value or map with THREE.RGBEEncoding"))}),{background:r.background,meshes:e,directionalLights:t,ambientLights:i,environmentLights:a}}function cr(r){return r.map&&r.map.image&&(r.map.encoding===c.THREE.RGBEEncoding||r.map.encoding===c.THREE.LinearEncoding)}function ot(r,{color:e,depth:t}){const i=r.createFramebuffer();function a(){r.bindFramebuffer(r.FRAMEBUFFER,i)}function s(){r.bindFramebuffer(r.FRAMEBUFFER,null)}function o(){a();const n=[];for(let l in e){l=Number(l),l===void 0&&console.error("invalid location");const h=e[l];r.framebufferTexture2D(r.FRAMEBUFFER,r.COLOR_ATTACHMENT0+l,h.target,h.texture,0),n.push(r.COLOR_ATTACHMENT0+l)}r.drawBuffers(n),t&&r.framebufferRenderbuffer(r.FRAMEBUFFER,r.DEPTH_ATTACHMENT,t.target,t.texture),s()}return o(),{color:e,bind:a,unbind:s}}const hr={source:`
  layout(location = 0) in vec2 a_position;

  out vec2 vCoord;

  void main() {
    vCoord = a_position;
    gl_Position = vec4(2. * a_position - 1., 0, 1);
  }
`};let Ht;function ur(r,e){const t=or(r,e),i={},a=[];for(let l in t){const{type:h,location:u}=t[l],d={type:h,location:u,v0:0,v1:0,v2:0,v3:0};i[l]=d}const s=new Set;function o(l,h,u,d,f){const m=i[l];if(!m){s.has(l)||(console.warn(`Uniform "${l}" does not exist in shader`),s.add(l));return}m.v0=h,m.v1=u,m.v2=d,m.v3=f,a.push(m)}Ht=Ht||dr(r);function n(){for(;a.length>0;){const{type:l,location:h,v0:u,v1:d,v2:f,v3:m}=a.pop(),p=Ht[l];if(u.length)if(p.matrix){const v=u,g=d||!1;r[p.matrix](h,g,v)}else r[p.array](h,u);else r[p.values](h,u,d,f,m)}}return{setUniform:o,upload:n}}function dr(r){return{[r.FLOAT]:$(1,"f"),[r.FLOAT_VEC2]:$(2,"f"),[r.FLOAT_VEC3]:$(3,"f"),[r.FLOAT_VEC4]:$(4,"f"),[r.INT]:$(1,"i"),[r.INT_VEC2]:$(2,"i"),[r.INT_VEC3]:$(3,"i"),[r.INT_VEC4]:$(4,"i"),[r.SAMPLER_2D]:$(1,"i"),[r.SAMPLER_2D_ARRAY]:$(1,"i"),[r.FLOAT_MAT2]:Gt(2,2),[r.FLOAT_MAT3]:Gt(3,3),[r.FLOAT_MAT4]:Gt(4,4)}}function $(r,e){return{values:`uniform${r}${e}`,array:`uniform${r}${e}v`}}function Gt(r,e){return{matrix:r===e?`uniformMatrix${r}fv`:`uniformMatrix${r}x${e}fv`}}function Oe(r,e){const{fragment:t,vertex:i}=e,a=i instanceof WebGLShader?i:Li(r,e),s=t instanceof WebGLShader?t:fr(r,e),o=sr(r,a,s);return{...pr(r,o),outputLocs:t.outputs?Ci(t.outputs):{}}}function Li(r,{defines:e,vertex:t}){return Ri(r,r.VERTEX_SHADER,t,e)}function fr(r,{defines:e,fragment:t}){return Ri(r,r.FRAGMENT_SHADER,t,e)}function pr(r,e){const t=ur(r,e),i={};let a=1;function s(h,u){if(u)if(i[h])i[h].tex=u;else{const d=a++;t.setUniform(h,d),i[h]={unit:d,tex:u}}}function o(){for(let h in i){const{tex:u,unit:d}=i[h];r.activeTexture(r.TEXTURE0+d),r.bindTexture(u.target,u.texture)}}function n(){for(let h in i){const{tex:u}=i[h];r.bindTexture(u.target,null)}}function l(h=!0){r.useProgram(e),t.upload(),h&&o()}return{attribLocs:nr(r,e),bindTextures:o,unbindTextures:n,program:e,setTexture:s,setUniform:t.setUniform,textures:i,useProgram:l}}function Ri(r,e,t,i){let a=`#version 300 es
precision mediump float;
precision mediump int;
`;return i&&(a+=mr(i)),e===r.FRAGMENT_SHADER&&t.outputs&&(a+=vr(t.outputs)),t.includes&&(a+=gr(t.includes,i)),typeof t.source=="function"?a+=t.source(i):a+=t.source,rr(r,e,a)}function mr(r){let e="";for(const t in r){const i=r[t];i&&(e+=`#define ${t} ${i}
`)}return e}function vr(r){let e="";const t=Ci(r);for(let i in t){const a=t[i];e+=`layout(location = ${a}) out vec4 out_${i};
`}return e}function gr(r,e){let t="";for(let i of r)typeof i=="function"?t+=i(e):t+=i;return t}function Ci(r){let e={};for(let t=0;t<r.length;t++)e[r[t]]=t;return e}function xr(r){const e=r.createVertexArray();r.bindVertexArray(e),r.bindBuffer(r.ARRAY_BUFFER,r.createBuffer()),r.bufferData(r.ARRAY_BUFFER,new Float32Array([0,0,1,0,0,1,0,1,1,0,1,1]),r.STATIC_DRAW);const t=0;r.enableVertexAttribArray(t),r.vertexAttribPointer(t,2,r.FLOAT,!1,0,0),r.bindVertexArray(null);const i=Li(r,{vertex:hr});function a(){r.bindVertexArray(e),r.bindBuffer(r.ARRAY_BUFFER,r.createBuffer()),r.bufferData(r.ARRAY_BUFFER,new Float32Array([0,0,1,0,0,1,0,1,1,0,1,1]),r.STATIC_DRAW),r.enableVertexAttribArray(t),r.vertexAttribPointer(t,2,r.FLOAT,!1,0,0),r.drawArrays(r.TRIANGLES,0,6)}return{draw:a,vertexShader:i}}const yr={source:`
  in vec3 aPosition;
  in vec3 aNormal;
  in vec2 aUv;
  in ivec2 aMaterialMeshIndex;

  uniform mat4 projView;
  uniform vec3 basePosition;

  out vec3 vPosition;
  out vec3 vNormal;
  out vec2 vUv;
  flat out ivec2 vMaterialMeshIndex;

  void main() {
    //vec3 pos=vec3(basePosition.x+aPosition.x,basePosition.y+aPosition.y,basePosition.z+aPosition.z);
    vPosition = aPosition;
    vNormal = aNormal;
    vUv = aUv;
    vMaterialMeshIndex = aMaterialMeshIndex;
    gl_Position = projView * vec4(aPosition, 1);
  }
`},Bi=`
  #define PI 3.14159265359
  #define TWOPI 6.28318530718
  #define INVPI 0.31830988618
  #define INVPI2 0.10132118364
  #define EPS 0.0005
  #define INF 1.0e999

  #define ROUGHNESS_MIN 0.03
`,Wt=`

uniform Materials {
  vec4 colorAndMaterialType[NUM_MATERIALS];
  vec4 roughnessMetalnessNormalScale[NUM_MATERIALS];

  #if defined(NUM_DIFFUSE_MAPS) || defined(NUM_NORMAL_MAPS) || defined(NUM_PBR_MAPS)
    ivec4 diffuseNormalRoughnessMetalnessMapIndex[NUM_MATERIALS];
  #endif

  #if defined(NUM_DIFFUSE_MAPS) || defined(NUM_NORMAL_MAPS)
    vec4 diffuseNormalMapSize[NUM_DIFFUSE_NORMAL_MAPS];
  #endif

  #if defined(NUM_PBR_MAPS)
    vec2 pbrMapSize[NUM_PBR_MAPS];
  #endif
} materials;

#ifdef NUM_DIFFUSE_MAPS
  uniform mediump sampler2DArray diffuseMap;
#endif

#ifdef NUM_NORMAL_MAPS
  uniform mediump sampler2DArray normalMap;
#endif

#ifdef NUM_PBR_MAPS
  uniform mediump sampler2DArray pbrMap;
#endif

float getMatType(int materialIndex) {
  return materials.colorAndMaterialType[materialIndex].w;
}

vec3 getMatColor(int materialIndex, vec2 uv) {
  vec3 color = materials.colorAndMaterialType[materialIndex].rgb;

  #ifdef NUM_DIFFUSE_MAPS
    int diffuseMapIndex = materials.diffuseNormalRoughnessMetalnessMapIndex[materialIndex].x;
    if (diffuseMapIndex >= 0) {
      color *= texture(diffuseMap, vec3(uv * materials.diffuseNormalMapSize[diffuseMapIndex].xy, diffuseMapIndex)).rgb;
    }
  #endif

  return color;
}

float getMatRoughness(int materialIndex, vec2 uv) {
  float roughness = materials.roughnessMetalnessNormalScale[materialIndex].x;

  #ifdef NUM_PBR_MAPS
    int roughnessMapIndex = materials.diffuseNormalRoughnessMetalnessMapIndex[materialIndex].z;
    if (roughnessMapIndex >= 0) {
      roughness *= texture(pbrMap, vec3(uv * materials.pbrMapSize[roughnessMapIndex].xy, roughnessMapIndex)).g;
    }
  #endif

  return roughness;
}

float getMatMetalness(int materialIndex, vec2 uv) {
  float metalness = materials.roughnessMetalnessNormalScale[materialIndex].y;

  #ifdef NUM_PBR_MAPS
    int metalnessMapIndex = materials.diffuseNormalRoughnessMetalnessMapIndex[materialIndex].w;
    if (metalnessMapIndex >= 0) {
      metalness *= texture(pbrMap, vec3(uv * materials.pbrMapSize[metalnessMapIndex].xy, metalnessMapIndex)).b;
    }
  #endif

  return metalness;
}

#ifdef NUM_NORMAL_MAPS
vec3 getMatNormal(int materialIndex, vec2 uv, vec3 normal, vec3 dp1, vec3 dp2, vec2 duv1, vec2 duv2) {
  int normalMapIndex = materials.diffuseNormalRoughnessMetalnessMapIndex[materialIndex].y;
  if (normalMapIndex >= 0) {
    // http://www.thetenthplanet.de/archives/1180
    // Compute co-tangent and co-bitangent vectors
    vec3 dp2perp = cross(dp2, normal);
    vec3 dp1perp = cross(normal, dp1);
    vec3 dpdu = dp2perp * duv1.x + dp1perp * duv2.x;
    vec3 dpdv = dp2perp * duv1.y + dp1perp * duv2.y;
    float invmax = inversesqrt(max(dot(dpdu, dpdu), dot(dpdv, dpdv)));
    dpdu *= invmax;
    dpdv *= invmax;

    vec3 n = 2.0 * texture(normalMap, vec3(uv * materials.diffuseNormalMapSize[normalMapIndex].zw, normalMapIndex)).rgb - 1.0;
    n.xy *= materials.roughnessMetalnessNormalScale[materialIndex].zw;

    mat3 tbn = mat3(dpdu, dpdv, normal);

    return normalize(tbn * n);
  } else {
    return normal;
  }
}
#endif
`,_r={outputs:["position","normal","faceNormal","color","matProps"],includes:[Bi,Wt],source:`
  in vec3 vPosition;
  in vec3 vNormal;
  in vec2 vUv;
  flat in ivec2 vMaterialMeshIndex;
  uniform vec3 basePosition;

  vec3 faceNormals(vec3 pos) {
    vec3 fdx = dFdx(pos);
    vec3 fdy = dFdy(pos);
    return cross(fdx, fdy);
  }

  void main() {
    int materialIndex = vMaterialMeshIndex.x;
    int meshIndex = vMaterialMeshIndex.y;

    vec2 uv = fract(vUv);

    vec3 color = getMatColor(materialIndex, uv);
    float roughness = getMatRoughness(materialIndex, uv);
    float metalness = getMatMetalness(materialIndex, uv);
    float materialType = getMatType(materialIndex);

    roughness = clamp(roughness, ROUGHNESS_MIN, 1.0);
    metalness = clamp(metalness, 0.0, 1.0);

    //vec3 bigPos=vPosition+basePosition;
    vec3 bigPos=vPosition;

    vec3 normal = normalize(vNormal);
    vec3 faceNormal = normalize(faceNormals(bigPos));
    normal *= sign(dot(normal, faceNormal));

    #ifdef NUM_NORMAL_MAPS
      vec3 dp1 = dFdx(bigPos);
      vec3 dp2 = dFdy(bigPos);
      vec2 duv1 = dFdx(vUv);
      vec2 duv2 = dFdy(vUv);
      normal = getMatNormal(materialIndex, uv, normal, dp1, dp2, duv1, duv2);
    #endif

    out_position = vec4(bigPos, float(meshIndex) + EPS);
    out_normal = vec4(normal, materialType);
    out_faceNormal = vec4(faceNormal, 0);
    out_color = vec4(color, 0);
    out_matProps = vec4(roughness, metalness, 0, 0);
  }
`};var br=c.THREE.Matrix4,Mr=c.THREE.Vector3;function wr(r,{materialBuffer:e,mergedMesh:t}){const i=Oe(r,{defines:e.defines,vertex:yr,fragment:_r});i.setTexture("diffuseMap",e.textures.diffuseMap),i.setTexture("normalMap",e.textures.normalMap),i.setTexture("pbrMap",e.textures.pbrMap);const a=t.geometry,s=a.getIndex().count,o=r.createVertexArray();r.bindVertexArray(o),Sr(r,i,a),r.bindVertexArray(null);let n=0,l=0;function h(x,_){n=x,l=_}let u;function d(x){u=x}function f(x){x!=null&&v.set(x.x,x.y,x.z)}function m(){p.copy(u.projectionMatrix),p.elements[8]+=2*n,p.elements[9]+=2*l,p.multiply(u.matrixWorldInverse),i.setUniform("projView",p.elements)}let p=new br,v=new Mr;function g(){m(),r.bindVertexArray(o),i.useProgram(),r.enable(r.DEPTH_TEST),r.drawElements(r.TRIANGLES,s,r.UNSIGNED_INT,0),r.disable(r.DEPTH_TEST)}return{draw:g,outputLocs:i.outputLocs,setCamera:d,setJitter:h,setBasePosition:f}}function Sr(r,e,t){nt(r,e.attribLocs.aPosition,t.getAttribute("position")),nt(r,e.attribLocs.aNormal,t.getAttribute("normal")),nt(r,e.attribLocs.aUv,t.getAttribute("uv")),nt(r,e.attribLocs.aMaterialMeshIndex,t.getAttribute("materialMeshIndex"));let i=new Uint32Array(t.getIndex().array);r.bindBuffer(r.ELEMENT_ARRAY_BUFFER,r.createBuffer()),r.bufferData(r.ELEMENT_ARRAY_BUFFER,i,r.STATIC_DRAW)}function nt(r,e,t){if(e===void 0)return;const{itemSize:i,array:a}=t;if(r.enableVertexAttribArray(e),r.bindBuffer(r.ARRAY_BUFFER,r.createBuffer()),r.bufferData(r.ARRAY_BUFFER,a,r.STATIC_DRAW),a instanceof Float32Array)r.vertexAttribPointer(e,i,r.FLOAT,!1,0,0);else if(a instanceof Int32Array)r.vertexAttribIPointer(e,i,r.INT,0,0);else throw"Unsupported buffer type"}function Tr(r,e,t){const i=r.getUniformBlockIndex(e,t),a=r.getActiveUniformBlockParameter(e,i,r.UNIFORM_BLOCK_DATA_SIZE),s=Ar(r,e,i),o=r.createBuffer();r.bindBuffer(r.UNIFORM_BUFFER,o),r.bufferData(r.UNIFORM_BUFFER,a,r.STATIC_DRAW);const n=new DataView(new ArrayBuffer(a));function l(u,d){if(!s[u])return;const{type:f,size:m,offset:p,stride:v}=s[u];switch(f){case r.FLOAT:ne(n,"setFloat32",m,p,v,1,d);break;case r.FLOAT_VEC2:ne(n,"setFloat32",m,p,v,2,d);break;case r.FLOAT_VEC3:ne(n,"setFloat32",m,p,v,3,d);break;case r.FLOAT_VEC4:ne(n,"setFloat32",m,p,v,4,d);break;case r.INT:ne(n,"setInt32",m,p,v,1,d);break;case r.INT_VEC2:ne(n,"setInt32",m,p,v,2,d);break;case r.INT_VEC3:ne(n,"setInt32",m,p,v,3,d);break;case r.INT_VEC4:ne(n,"setInt32",m,p,v,4,d);break;case r.BOOL:ne(n,"setUint32",m,p,v,1,d);break;default:console.warn("UniformBuffer: Unsupported type")}}function h(u){r.bindBuffer(r.UNIFORM_BUFFER,o),r.bufferSubData(r.UNIFORM_BUFFER,0,n),r.bindBufferBase(r.UNIFORM_BUFFER,u,o)}return{set:l,bind:h}}function Ar(r,e,t){const i=r.getActiveUniformBlockParameter(e,t,r.UNIFORM_BLOCK_ACTIVE_UNIFORM_INDICES),a=r.getActiveUniforms(e,i,r.UNIFORM_OFFSET),s=r.getActiveUniforms(e,i,r.UNIFORM_ARRAY_STRIDE),o={};for(let n=0;n<i.length;n++){const{name:l,type:h,size:u}=r.getActiveUniform(e,i[n]);o[l]={type:h,size:u,offset:a[n],stride:s[n]}}return o}function ne(r,e,t,i,a,s,o){const n=Math.min(o.length/s,t);for(let l=0;l<n;l++)for(let h=0;h<s;h++)r[e](i+l*a+h*4,o[s*l+h],!0)}function ue(r,e,t){return Math.min(Math.max(r,e),t)}function Pr(r){for(let e=r.length-1;e>0;e--){const t=Math.floor(Math.random()*(e+1)),i=r[e];r[e]=r[t],r[t]=i}return r}function Er(r,e,t=1e-4){for(let i=0;i<r.length;i++)if(Math.abs(r[i]-e[i])>t)return!1;return!0}function X(r,e){let{width:t=null,height:i=null,data:a=null,length:s=1,channels:o=null,storage:n=null,flipY:l=!1,gammaCorrection:h=!1,wrapS:u=r.CLAMP_TO_EDGE,wrapT:d=r.CLAMP_TO_EDGE,minFilter:f=r.NEAREST,magFilter:m=r.NEAREST}=e;t=t||a.width||0,i=i||a.height||0;const p=r.createTexture();let v,g;Array.isArray(a)&&(g=a,a=g[0]),v=g||s>1?r.TEXTURE_2D_ARRAY:r.TEXTURE_2D,r.activeTexture(r.TEXTURE0),r.bindTexture(v,p),r.texParameteri(v,r.TEXTURE_WRAP_S,u),r.texParameteri(v,r.TEXTURE_WRAP_T,d),r.texParameteri(v,r.TEXTURE_MIN_FILTER,f),r.texParameteri(v,r.TEXTURE_MAG_FILTER,m),o||(a&&a.length?o=a.length/(t*i):o=4),o=ue(o,1,4);const{type:x,format:_,internalFormat:y}=Rr(r,o,n,a,h);if(g){r.texStorage3D(v,1,y,t,i,g.length);for(let M=0;M<g.length;M++){const b=g[M].width||t,w=g[M].height||i;r.pixelStorei(r.UNPACK_FLIP_Y_WEBGL,Array.isArray(l)?l[M]:l),r.texSubImage3D(v,0,0,0,M,b,w,1,_,x,g[M])}}else s>1?r.texStorage3D(v,1,y,t,i,s):(r.pixelStorei(r.UNPACK_FLIP_Y_WEBGL,l),r.texStorage2D(v,1,y,t,i),a&&r.texSubImage2D(v,0,0,0,t,i,_,x,a));return r.pixelStorei(r.UNPACK_FLIP_Y_WEBGL,!1),{target:v,texture:p}}function Ir(r,e,t){const i=r.createRenderbuffer(),a=r.RENDERBUFFER;return r.bindRenderbuffer(a,i),r.renderbufferStorage(r.RENDERBUFFER,r.DEPTH_COMPONENT24,e,t),r.bindRenderbuffer(a,null),{target:a,texture:i}}function Lr(r,e){return{1:r.RED,2:r.RG,3:r.RGB,4:r.RGBA}[e]}function Rr(r,e,t,i,a){let s,o;const n=i instanceof Uint8Array||i instanceof HTMLImageElement||i instanceof HTMLCanvasElement||i instanceof ImageBitmap||i instanceof ImageData,l=i instanceof Float32Array;return t==="byte"||!t&&n?(o={1:r.R8,2:r.RG8,3:a?r.SRGB8:r.RGB8,4:a?r.SRGB8_ALPHA8:r.RGBA8}[e],s=r.UNSIGNED_BYTE):t==="float"||!t&&l?(o={1:r.R32F,2:r.RG32F,3:r.RGB32F,4:r.RGBA32F}[e],s=r.FLOAT):t==="halfFloat"?(o={1:r.R16F,2:r.RG16F,3:r.RGB16F,4:r.RGBA16F}[e],s=r.FLOAT):t==="snorm"&&(o={1:r.R8_SNORM,2:r.RG8_SNORM,3:r.RGB8_SNORM,4:r.RGBA8_SNORM}[e],s=r.UNSIGNED_BYTE),{format:Lr(r,e),internalFormat:o,type:s}}function Cr(r,e){const t={};for(const i of e){const a=[];t[i]={indices:Di(r,i,a),textures:a}}return t}function Br(r,e){const t={textures:[],indices:{}};for(const i of e)t.indices[i]=Di(r,i,t.textures);return t}function Di(r,e,t){const i=[];for(const a of r)if(!(a[e]&&a[e].image))i.push(-1);else{let o=t.length;for(let n=0;n<t.length;n++)if(t[n]===a[e]){o=n;break}o===t.length&&t.push(a[e]),i.push(o)}return i}function Dr(r,e){const t=Cr(e,["map","normalMap"]),i=Br(e,["roughnessMap","metalnessMap"]),a={},s={};if(s.color=e.map(l=>l.color),s.roughness=e.map(l=>l.roughness),s.metalness=e.map(l=>l.metalness),s.normalScale=e.map(l=>l.normalScale),s.type=e.map(l=>{if(l.shadowCatcher)return tr;if(l.transparent)return l.solid?er:$a}),t.map.textures.length>0){const{relativeSizes:l,texture:h}=jt(r,t.map.textures,!0);a.diffuseMap=h,s.diffuseMapSize=l,s.diffuseMapIndex=t.map.indices}if(t.normalMap.textures.length>0){const{relativeSizes:l,texture:h}=jt(r,t.normalMap.textures,!1);a.normalMap=h,s.normalMapSize=l,s.normalMapIndex=t.normalMap.indices}if(i.textures.length>0){const{relativeSizes:l,texture:h}=jt(r,i.textures,!1);a.pbrMap=h,s.pbrMapSize=l,s.roughnessMapIndex=i.indices.roughnessMap,s.metalnessMapIndex=i.indices.metalnessMap}const o={NUM_MATERIALS:e.length,NUM_DIFFUSE_MAPS:t.map.textures.length,NUM_NORMAL_MAPS:t.normalMap.textures.length,NUM_DIFFUSE_NORMAL_MAPS:Math.max(t.map.textures.length,t.normalMap.textures.length),NUM_PBR_MAPS:i.textures.length},n=Oe(r,{vertex:{source:"void main() {}"},fragment:{includes:[Wt],source:"void main() {}"},defines:o});return Vr(r,n.program,s),{defines:o,textures:a}}function jt(r,e,t=!1){const i=e.map(l=>l.image),a=e.map(l=>l.flipY),{maxSize:s,relativeSizes:o}=Ur(i);return{texture:X(r,{width:s.width,height:s.height,gammaCorrection:t,data:i,flipY:a,channels:3,minFilter:r.LINEAR,magFilter:r.LINEAR}),relativeSizes:o}}function Ur(r){const e={width:0,height:0};for(const i of r)e.width=Math.max(e.width,i.width),e.height=Math.max(e.height,i.height);const t=[];for(const i of r)t.push(i.width/e.width),t.push(i.height/e.height);return{maxSize:e,relativeSizes:t}}function Vr(r,e,t){const i=Tr(r,e,"Materials");i.set("Materials.colorAndMaterialType[0]",lt({data:[].concat(...t.color.map(a=>a.toArray())),channels:3},{data:t.type,channels:1})),i.set("Materials.roughnessMetalnessNormalScale[0]",lt({data:t.roughness,channels:1},{data:t.metalness,channels:1},{data:[].concat(...t.normalScale.map(a=>a.toArray())),channels:2})),i.set("Materials.diffuseNormalRoughnessMetalnessMapIndex[0]",lt({data:t.diffuseMapIndex,channels:1},{data:t.normalMapIndex,channels:1},{data:t.roughnessMapIndex,channels:1},{data:t.metalnessMapIndex,channels:1})),i.set("Materials.diffuseNormalMapSize[0]",lt({data:t.diffuseMapSize,channels:2},{data:t.normalMapSize,channels:2})),i.set("Materials.pbrMapSize[0]",t.pbrMapSize),i.bind(0)}function lt(...r){let e=0;for(let i=0;i<r.length;i++){const a=r[i],s=a.data?a.data.length/a.channels:0;e=Math.max(e,s)}const t=[];for(let i=0;i<e;i++)for(let a=0;a<r.length;a++){const{data:s=[],channels:o}=r[a];for(let n=0;n<o;n++)t.push(s[i*o+n])}return t}var qt=c.THREE.BufferGeometry,Ne=c.THREE.BufferAttribute;function Or(r){let e=0,t=0;const i=[],a=new Map;for(const n of r){const l=n.geometry.isBufferGeometry?zr(n.geometry,["position","normal","uv"]):new qt().fromGeometry(n.geometry);if(l.getIndex()||kr(l),l.applyMatrix4(n.userData.rayMatrix),l.getAttribute("normal")?l.normalizeNormals():l.computeVertexNormals(),window.mlCameraChange==!0)break;e+=l.getAttribute("position").count,t+=l.getIndex().count;const u=n.material;let d=a.get(u);d===void 0&&(d=a.size,a.set(u,d)),i.push({geometry:l,materialIndex:d})}const s=Fr(i,e,t);window.bigGeometry=s;const o=Nr();return{geometry:s,materials:Array.from(a.keys()),indicesSectionMap:o}}function Nr(r){return new Map}function Fr(r,e,t){const i=new Ne(new Float32Array(3*e),3,!1),a=new Ne(new Float32Array(3*e),3,!1),s=new Ne(new Float32Array(2*e),2,!1),o=new Ne(new Int32Array(2*e),2,!1),n=new Ne(new Uint32Array(t),1,!1),l=new qt;l.setAttribute("position",i),l.setAttribute("normal",a),l.setAttribute("uv",s),l.setAttribute("materialMeshIndex",o),l.setIndex(n);let h=0,u=0,d=1,f=[];for(const{geometry:p,materialIndex:v}of r){const g=p.getAttribute("position").count;f.push(p);const x=p.getIndex();let _=new Float32Array(g*2);_.fill(0),p.getAttribute("uv")==null&&p.setAttribute("uv",new c.THREE.Float32BufferAttribute(_,2));for(let y=0;y<g;y++)o.setXY(h+y,v,d);h+=g,u+=x.count,d++}let m=F.mergeGeometries(f);return m.setAttribute("materialMeshIndex",o),m}function zr(r,e){const t=new qt;for(const a of e){const s=r.getAttribute(a);s&&t.setAttribute(a,s.clone())}const i=r.getIndex();return i&&t.setIndex(i),t}function kr(r){const e=r.getAttribute("position");if(!e){console.warn("No position attribute");return}const t=new Uint32Array(e.count);for(let i=0;i<t.length;i++)t[i]=i;return r.setIndex(new Ne(t,1,!1)),r}function Hr(r,e,t=0,i=r.length){for(;t!==i;){for(;e(r[t]);)if(t++,t===i)return t;do if(i--,t===i)return t;while(!e(r[i]));Ui(r,t,i),t++}return t}function Gr(r,e,t=0,i=r.length,a=Math.floor((t+i)/2)){for(let s=t;s<=a;s++){let o=s,n=r[s];for(let l=s+1;l<i;l++)e(n,r[l])||(o=l,n=r[l],Ui(r,s,o))}}function Ui(r,e,t){const i=r[t];r[t]=r[e],r[e]=i}let we=c.THREE.Box3,de=c.THREE.Vector3;const Y=new de;function Wr(r,e,t){let i=Date.now();const a=qr(r);console.log("数据准备耗时",Date.now()-i);const s=Xt(a,0,a.length);return console.log("bvh划分耗时",Date.now()-i),s}function jr(r){const e=[],t=[],i={x:0,y:1,z:2};let a=1;const s=(h,u=1)=>{if(a=Math.max(u,a),h.primitives)for(let d=0;d<h.primitives.length;d++){const f=h.primitives[d];e.push(f.indices[0],f.indices[1],f.indices[2],h.primitives.length,f.faceNormal.x,f.faceNormal.y,f.faceNormal.z,f.materialIndex),t.push(!1)}else{const d=h.bounds;e.push(d.min.x,d.min.y,d.min.z,i[h.splitAxis],d.max.x,d.max.y,d.max.z,null);const f=e.length-1;t.push(!0),s(h.child0,u+1),e[f]=e.length/4,s(h.child1,u+1)}};s(r);const o=new ArrayBuffer(4*e.length),n=new Float32Array(o),l=new Int32Array(o);for(let h=0;h<t.length;h++){let u=8*h;t[h]?(n[u]=e[u],n[u+1]=e[u+1],n[u+2]=e[u+2],l[u+3]=e[u+3]):(l[u]=e[u],l[u+1]=e[u+1],l[u+2]=e[u+2],l[u+3]=-e[u+3]),n[u+4]=e[u+4],n[u+5]=e[u+5],n[u+6]=e[u+6],l[u+7]=e[u+7]}return{maxDepth:a,count:e.length/4,buffer:n}}function qr(r,e,t){const i=[],a=r.getIndex().array,s=r.getAttribute("position"),o=r.getAttribute("materialMeshIndex"),n=new de,l=new de,h=new de,u=new de,d=new de;for(let f=0;f<a.length;f+=3){const m=a[f],p=a[f+1],v=a[f+2];n.fromBufferAttribute(s,m),l.fromBufferAttribute(s,p),h.fromBufferAttribute(s,v);const g=new we;u.subVectors(h,n),d.subVectors(l,n),g.expandByPoint(n),g.expandByPoint(l),g.expandByPoint(h);const x={bounds:g,center:g.getCenter(new de),indices:[m,p,v],faceNormal:new de().crossVectors(d,u).normalize(),materialIndex:o.getX(m)};i.push(x)}return console.log("primitive count ",i.length),i}function Xt(r,e,t){const i=new we;for(let s=e;s<t;s++)i.union(r[s].bounds);const a=t-e;if(a===1)return Vi(r.slice(e,t),i);{const s=new we;for(let l=e;l<t;l++)s.expandByPoint(r[l].center);const o=Yr(s);let n=Math.floor((e+t)/2);if(a<=4)Gr(r,(l,h)=>l.center[o]<h.center[o],e,t,n);else{if(s.max[o]===s.min[o])return Vi(r.slice(e,t),i);{const l=[];for(let f=0;f<12;f++)l.push({bounds:new we,count:0});for(let f=e;f<t;f++){let m=Math.floor(l.length*Oi(s,o,r[f].center));m===l.length&&(m=l.length-1),l[m].count++,l[m].bounds.union(r[f].bounds)}const h=[];for(let f=0;f<l.length-1;f++){const m=new we,p=new we;let v=0,g=0;for(let x=0;x<=f;x++)m.union(l[x].bounds),v+=l[x].count;for(let x=f+1;x<l.length;x++)p.union(l[x].bounds),g+=l[x].count;h.push(.1+(v*Yt(m)+g*Yt(p))/Yt(i))}let u=h[0],d=0;for(let f=1;f<h.length;f++)h[f]<u&&(u=h[f],d=f);n=Hr(r,f=>{let m=Math.floor(l.length*Oi(s,o,f.center));return m===l.length&&(m=l.length-1),m<=d},e,t)}}return Xr(o,Xt(r,e,n),Xt(r,n,t))}}function Vi(r,e){return{primitives:r,bounds:e}}function Xr(r,e,t){return{child0:e,child1:t,bounds:new we().union(e.bounds).union(t.bounds),splitAxis:r}}function Yr(r){return r.getSize(Y),Y.x>Y.z?Y.x>Y.y?"x":"y":Y.z>Y.y?"z":"y"}function Oi(r,e,t){let i=t[e]-r.min[e];return r.max[e]>r.min[e]&&(i/=r.max[e]-r.min[e]),i}function Yt(r){return r.getSize(Y),2*(Y.x*Y.z+Y.x*Y.y+Y.z*Y.y)}function Ni(r,e=1){const t=r.length/4,i=new Float32Array(t*3),a=[];for(let s=0;s<255;s++)a[s]=e*Math.pow(2,s-128)/255;for(let s=0;s<t;s++){const o=r[4*s],n=r[4*s+1],l=r[4*s+2],h=r[4*s+3],u=a[h];i[3*s]=o*u,i[3*s+1]=n*u,i[3*s+2]=l*u}return i}const Fi={width:2048,height:1024};function Zr(r){let e;return r.isColor?e=zi(1,1,r):r.encoding===c.THREE.RGBEEncoding&&(e={width:r.image.width,height:r.image.height,data:r.image.data},e.data=Ni(e.data)),e}function Jr(r,e,t){let i=Kr(t);return e.forEach(a=>{$r(a,i)}),r.forEach(a=>{i.data=es(a,i)}),i}function Kr(r){let e;if(r.length>0){const t=r[0];e={width:t.map.image.width,height:t.map.image.height,data:t.map.image.data},e.data=Ni(e.data,t.intensity)}else e=zi(Fi.width,Fi.height);return e}function zi(r,e,t,i){const a=r*e,s=new Float32Array(a*3);return t&&t.isColor&&Qr(s,t,i),{width:r,height:e,data:s}}function Qr(r,e,t=1){return r.forEach(function(i,a){const s=a%3;s===0?r[a]=e.r*t:s===1?r[a]=e.g*t:s===2&&(r[a]=e.b*t)}),r}function $r(r,e){const t=r.color;e.data.forEach(function(i,a){const s=a%3;s===0?e.data[a]+=t.r*r.intensity:s===1?e.data[a]+=t.g*r.intensity:s===2&&(e.data[a]+=t.b*r.intensity)})}function es(r,e){const t=new c.THREE.Spherical,i=r.position.clone().sub(r.target.position);return t.setFromVector3(i),t.theta=Math.PI*3/2-t.theta,t.makeSafe(),ts(r,e,t)}function ts(r,e,t){const i=e.data,a=e.width,s=e.height,o=i.length/(3*s),n=i.length/(3*a),l=r.softness||.01,h=is(l),d=h<Math.PI/5?as:rs;let f=!1,m=new c.THREE.Spherical;for(let p=0;p<o;p++){let v=!1;for(let g=0;g<n;g++){const x=g*a+p;m=ss(p,g,a,s,m);const _=d(t,m,l,h);_>0&&(v=!0,f=!0);const y=r.intensity*_;i[x*3]+=y*r.color.r,i[x*3+1]+=y*r.color.g,i[x*3+2]+=y*r.color.b}if(!v&&f)return i}return i}function is(r){const e=Math.PI/128,t=2*Math.PI/e;for(let i=0;i<t;i++){const a=i*e;if(Zt(a,r)<=1e-4)return a}}function as(r,e,t,i){const a=ki(r.phi,e.phi);if(ki(r.theta,e.theta)>i&&a>i)return 0;const o=Hi(r,e);return Zt(o,t)}function rs(r,e,t){const i=Hi(r,e);return Zt(i,t)}function ki(r,e){const t=Math.abs(r-e)%(2*Math.PI);return t>Math.PI?2*Math.PI-t:t}const Hi=function(){const r=new c.THREE.Vector3,e=new c.THREE.Vector3;return(t,i)=>(r.setFromSpherical(t),e.setFromSpherical(i),r.angleTo(e))}();function Zt(r,e){const t=Math.pow(2,14.5*Math.max(.001,1-ue(e,0,1)));return Math.pow(t,1.1)*Math.pow(8,-t*Math.pow(r,1.8))}function ss(r,e,t,i,a){return a.phi=Math.PI*e/i,a.theta=2*Math.PI*r/t,a}function os(r){const e=r.data,t={width:r.width+2,height:r.height+1},i=ns(t.width,t.height,2);for(let s=0;s<r.height;s++){const o=Math.sin(Math.PI*(s+.5)/r.height);for(let l=0;l<r.width;l++){const h=3*(s*r.width+l);let u=e[h],d=e[h+1],f=e[h+2],m=.2126*u+.7152*d+.0722*f;m*=o,i.set(l+2,s,0,i.get(l+1,s,0)+m/r.width),i.set(l+1,s,1,m)}const n=i.get(t.width-1,s,0);for(let l=1;l<i.width;l++)i.set(l,s,0,i.get(l,s,0)/n),i.set(l,s,1,i.get(l,s,1)/n);i.set(0,s+1,0,i.get(0,s,0)+n/r.height),i.set(0,s,1,n)}const a=i.get(0,i.height-1,0);for(let s=0;s<i.height;s++)i.set(0,s,0,i.get(0,s,0)/a),i.set(0,s,1,i.get(0,s,1)/a);return t.data=i.array,t}function ns(r,e,t){const i=new Float32Array(t*r*e);return{set(a,s,o,n){i[t*(s*r+a)+o]=n},get(a,s,o){return i[t*(s*r+a)+o]},width:r,height:e,channels:t,array:i}}function ls(r,e,t,i,a){let s=`int ${r};
`;for(let o=e;i>0&&o<t||i<0&&o>t;o+=i)s+=`${r} = ${o};
`,s+=a;return s}const cs=`
  #define STANDARD 0
  #define THIN_GLASS 1
  #define THICK_GLASS 2
  #define SHADOW_CATCHER 3

  const float IOR = 1.5;
  const float INV_IOR = 1.0 / IOR;

  const float IOR_THIN = 1.015;
  const float INV_IOR_THIN = 1.0 / IOR_THIN;

  const float R0 = (1.0 - IOR) * (1.0 - IOR)  / ((1.0 + IOR) * (1.0 + IOR));

  // https://www.w3.org/WAI/GL/wiki/Relative_luminance
  const vec3 luminance = vec3(0.2126, 0.7152, 0.0722);

  #define RAY_MAX_DISTANCE 9999.0

  struct Ray {
    vec3 o;
    vec3 d;
    vec3 invD;
    float tMax;
  };

  struct SurfaceInteraction {
    bool hit;
    vec3 position;
    vec3 normal; // smoothed normal from the three triangle vertices
    vec3 faceNormal; // normal of the triangle
    vec3 color;
    float roughness;
    float metalness;
    int materialType;
  };

  struct Camera {
    mat4 transform;
    float aspect;
    float fov;
    float focus;
    float aperture;
  };

  void initRay(inout Ray ray, vec3 origin, vec3 direction) {
    ray.o = origin;
    ray.d = direction;
    ray.invD = 1.0 / ray.d;
    ray.tMax = RAY_MAX_DISTANCE;
  }

  // given the index from a 1D array, retrieve corresponding position from packed 2D texture
  ivec2 unpackTexel(int i, int columnsLog2) {
    ivec2 u;
    u.y = i >> columnsLog2; // equivalent to (i / 2^columnsLog2)
    u.x = i - (u.y << columnsLog2); // equivalent to (i % 2^columnsLog2)
    return u;
  }

  vec4 fetchData(sampler2D s, int i, int columnsLog2) {
    return texelFetch(s, unpackTexel(i, columnsLog2), 0);
  }

  // ivec4 fetchData(isampler2D s, int i, int columnsLog2) {
  //   return texelFetch(s, unpackTexel(i, columnsLog2), 0);
  // }

  struct Path {
    Ray ray;
    vec3 li;
    float alpha;
    vec3 beta;
    bool specularBounce;
    bool abort;
    float misWeight;
  };

  uniform Camera camera;
  uniform vec2 pixelSize; // 1 / screenResolution
  uniform vec2 jitter;

  in vec2 vCoord;
`,Jt=`
vec4 textureLinear(sampler2D map, vec2 uv) {
  #ifdef OES_texture_float_linear
    return texture(map, uv);
  #else
    vec2 size = vec2(textureSize(map, 0));
    vec2 texelSize = 1.0 / size;

    uv = uv * size - 0.5;
    vec2 f = fract(uv);
    uv = floor(uv) + 0.5;

    vec4 s1 = texture(map, (uv + vec2(0, 0)) * texelSize);
    vec4 s2 = texture(map, (uv + vec2(1, 0)) * texelSize);
    vec4 s3 = texture(map, (uv + vec2(0, 1)) * texelSize);
    vec4 s4 = texture(map, (uv + vec2(1, 1)) * texelSize);

    return mix(mix(s1, s2, f.x), mix(s3, s4, f.x), f.y);
  #endif
}
`,hs={includes:[Bi,cs,Jt,Wt,`

uniform sampler2D positionBuffer;
uniform sampler2D normalBuffer;
uniform sampler2D uvBuffer;
uniform sampler2D bvhBuffer;
uniform vec3 basePosition;

struct Triangle {
  vec3 p0;
  vec3 p1;
  vec3 p2;
};

void surfaceInteractionFromBVH(inout SurfaceInteraction si, Triangle tri, vec3 barycentric, ivec3 index, vec3 faceNormal, int materialIndex) {
  si.hit = true;
  si.faceNormal = faceNormal;
  si.position = barycentric.x * tri.p0 + barycentric.y * tri.p1 + barycentric.z * tri.p2;
  ivec2 i0 = unpackTexel(index.x, VERTEX_COLUMNS);
  ivec2 i1 = unpackTexel(index.y, VERTEX_COLUMNS);
  ivec2 i2 = unpackTexel(index.z, VERTEX_COLUMNS);

  vec3 n0 = texelFetch(normalBuffer, i0, 0).xyz;
  vec3 n1 = texelFetch(normalBuffer, i1, 0).xyz;
  vec3 n2 = texelFetch(normalBuffer, i2, 0).xyz;
  vec3 normal = normalize(barycentric.x * n0 + barycentric.y * n1 + barycentric.z * n2);

  #if defined(NUM_DIFFUSE_MAPS) || defined(NUM_NORMAL_MAPS) || defined(NUM_PBR_MAPS)
    vec2 uv0 = texelFetch(uvBuffer, i0, 0).xy;
    vec2 uv1 = texelFetch(uvBuffer, i1, 0).xy;
    vec2 uv2 = texelFetch(uvBuffer, i2, 0).xy;
    vec2 uv = fract(barycentric.x * uv0 + barycentric.y * uv1 + barycentric.z * uv2);
  #else
    vec2 uv = vec2(0.0);
  #endif

  si.materialType = int(getMatType(materialIndex));
  si.color = getMatColor(materialIndex, uv);
  si.roughness = getMatRoughness(materialIndex, uv);
  si.metalness = getMatMetalness(materialIndex, uv);

  #ifdef NUM_NORMAL_MAPS
    vec3 dp1 = tri.p0 - tri.p2;
    vec3 dp2 = tri.p1 - tri.p2;
    vec2 duv1 = uv0 - uv2;
    vec2 duv2 = uv1 - uv2;
    si.normal = getMatNormal(materialIndex, uv, normal, dp1, dp2, duv1, duv2);
  #else
    si.normal = normal;
  #endif
}

struct TriangleIntersect {
  float t;
  vec3 barycentric;
};

// Triangle-ray intersection
// Faster than the classic Möller–Trumbore intersection algorithm
// http://www.pbr-book.org/3ed-2018/Shapes/Triangle_Meshes.html#TriangleIntersection
TriangleIntersect intersectTriangle(Ray r, Triangle tri, int maxDim, vec3 shear) {
  TriangleIntersect ti;
  vec3 d = r.d;

  // translate vertices based on ray origin
  vec3 p0t = tri.p0 - r.o;
  vec3 p1t = tri.p1 - r.o;
  vec3 p2t = tri.p2 - r.o;

  // permute components of triangle vertices
  if (maxDim == 0) {
    p0t = p0t.yzx;
    p1t = p1t.yzx;
    p2t = p2t.yzx;
  } else if (maxDim == 1) {
    p0t = p0t.zxy;
    p1t = p1t.zxy;
    p2t = p2t.zxy;
  }

  // apply shear transformation to translated vertex positions
  p0t.xy += shear.xy * p0t.z;
  p1t.xy += shear.xy * p1t.z;
  p2t.xy += shear.xy * p2t.z;

  // compute edge function coefficients
  vec3 e = vec3(
    p1t.x * p2t.y - p1t.y * p2t.x,
    p2t.x * p0t.y - p2t.y * p0t.x,
    p0t.x * p1t.y - p0t.y * p1t.x
  );

  // check if intersection is inside triangle
  if (any(lessThan(e, vec3(0))) && any(greaterThan(e, vec3(0)))) {
    return ti;
  }

  float det = e.x + e.y + e.z;

  // not needed?
  // if (det == 0.) {
  //   return ti;
  // }

  p0t.z *= shear.z;
  p1t.z *= shear.z;
  p2t.z *= shear.z;
  float tScaled = (e.x * p0t.z + e.y * p1t.z + e.z * p2t.z);

  // not needed?
  // if (sign(det) != sign(tScaled)) {
  //   return ti;
  // }

  // check if closer intersection already exists
  if (abs(tScaled) > abs(r.tMax * det)) {
    return ti;
  }

  float invDet = 1. / det;
  ti.t = tScaled * invDet;
  ti.barycentric = e * invDet;

  return ti;
}

struct Box {
  vec3 min;
  vec3 max;
};

// Branchless ray/box intersection
// https://tavianator.com/fast-branchless-raybounding-box-intersections/
float intersectBox(Ray r, Box b) {
  vec3 tBot = (b.min - r.o) * r.invD;
  vec3 tTop = (b.max - r.o) * r.invD;
  vec3 tNear = min(tBot, tTop);
  vec3 tFar = max(tBot, tTop);
  float t0 = max(tNear.x, max(tNear.y, tNear.z));
  float t1 = min(tFar.x, min(tFar.y, tFar.z));

  return (t0 > t1 || t0 > r.tMax) ? -1.0 : (t0 > 0.0 ? t0 : t1);
}

int maxDimension(vec3 v) {
  return v.x > v.y ? (v.x > v.z ? 0 : 2) : (v.y > v.z ? 1 : 2);
}

// Traverse BVH, find closest triangle intersection, and return surface information
void intersectScene(inout Ray ray, inout SurfaceInteraction si) {
  si.hit = false;

  int maxDim = maxDimension(abs(ray.d));

  // Permute space so that the z dimension is the one where the absolute value of the ray's direction is largest.
  // Then create a shear transformation that aligns ray direction with the +z axis
  vec3 shear;
  if (maxDim == 0) {
    shear = vec3(-ray.d.y, -ray.d.z, 1.0) * ray.invD.x;
  } else if (maxDim == 1) {
    shear = vec3(-ray.d.z, -ray.d.x, 1.0) * ray.invD.y;
  } else {
    shear = vec3(-ray.d.x, -ray.d.y, 1.0) * ray.invD.z;
  }

  int nodesToVisit[STACK_SIZE];
  int stack = 0;

  nodesToVisit[0] = 0;

  while(stack >= 0) {
    int i = nodesToVisit[stack--];

    vec4 r1 = fetchData(bvhBuffer, i, BVH_COLUMNS);
    vec4 r2 = fetchData(bvhBuffer, i + 1, BVH_COLUMNS);

    int splitAxisOrNumPrimitives = floatBitsToInt(r1.w);

    if (splitAxisOrNumPrimitives >= 0) {
      // Intersection is a bounding box. Test for box intersection and keep traversing BVH
      int splitAxis = splitAxisOrNumPrimitives;

      Box bbox = Box(r1.xyz, r2.xyz);

      if (intersectBox(ray, bbox) > 0.0) {
        // traverse near node to ray first, and far node to ray last
        if (ray.d[splitAxis] > 0.0) {
          nodesToVisit[++stack] = floatBitsToInt(r2.w);
          nodesToVisit[++stack] = i + 2;
        } else {
          nodesToVisit[++stack] = i + 2;
          nodesToVisit[++stack] = floatBitsToInt(r2.w);
        }
      }
    } else {
      ivec3 index = floatBitsToInt(r1.xyz);
      // vec3 vv1=fetchData(positionBuffer, index.x, VERTEX_COLUMNS).xyz;
      // vec3 vv2=fetchData(positionBuffer, index.y, VERTEX_COLUMNS).xyz;
      // vec3 vv3=fetchData(positionBuffer, index.z, VERTEX_COLUMNS).xyz;
      // vec3 m1=vv1+basePosition;
      // vec3 m2=vv2+basePosition;
      // vec3 m3=vv3+basePosition;
      // Triangle tri = Triangle(
      //   m1,
      //   m2,
      //   m3
      // );
      Triangle tri = Triangle(
        fetchData(positionBuffer, index.x, VERTEX_COLUMNS).xyz,
        fetchData(positionBuffer, index.y, VERTEX_COLUMNS).xyz,
        fetchData(positionBuffer, index.z, VERTEX_COLUMNS).xyz
      );
      TriangleIntersect hit = intersectTriangle(ray, tri, maxDim, shear);

      if (hit.t > 0.0) {
        ray.tMax = hit.t;
        int materialIndex = floatBitsToInt(r2.w);
        vec3 faceNormal = r2.xyz;
        surfaceInteractionFromBVH(si, tri, hit.barycentric, index, faceNormal, materialIndex);
      }
    }
  }

  // Values must be clamped outside of intersection loop. Clamping inside the loop produces incorrect numbers on some devices.
  si.roughness = clamp(si.roughness, ROUGHNESS_MIN, 1.0);
  si.metalness = clamp(si.metalness, 0.0, 1.0);
}

bool intersectSceneShadow(inout Ray ray) {
  int maxDim = maxDimension(abs(ray.d));

  // Permute space so that the z dimension is the one where the absolute value of the ray's direction is largest.
  // Then create a shear transformation that aligns ray direction with the +z axis
  vec3 shear;
  if (maxDim == 0) {
    shear = vec3(-ray.d.y, -ray.d.z, 1.0) * ray.invD.x;
  } else if (maxDim == 1) {
    shear = vec3(-ray.d.z, -ray.d.x, 1.0) * ray.invD.y;
  } else {
    shear = vec3(-ray.d.x, -ray.d.y, 1.0) * ray.invD.z;
  }

  int nodesToVisit[STACK_SIZE];
  int stack = 0;

  nodesToVisit[0] = 0;

  while(stack >= 0) {
    int i = nodesToVisit[stack--];

    vec4 r1 = fetchData(bvhBuffer, i, BVH_COLUMNS);
    vec4 r2 = fetchData(bvhBuffer, i + 1, BVH_COLUMNS);

    int splitAxisOrNumPrimitives = floatBitsToInt(r1.w);

    if (splitAxisOrNumPrimitives >= 0) {
      int splitAxis = splitAxisOrNumPrimitives;

      Box bbox = Box(r1.xyz, r2.xyz);

      if (intersectBox(ray, bbox) > 0.0) {
        if (ray.d[splitAxis] > 0.0) {
          nodesToVisit[++stack] = floatBitsToInt(r2.w);
          nodesToVisit[++stack] = i + 2;
        } else {
          nodesToVisit[++stack] = i + 2;
          nodesToVisit[++stack] = floatBitsToInt(r2.w);
        }
      }
    } else {
      ivec3 index = floatBitsToInt(r1.xyz);
      // vec3 vv1=fetchData(positionBuffer, index.x, VERTEX_COLUMNS).xyz;
      // vec3 vv2=fetchData(positionBuffer, index.y, VERTEX_COLUMNS).xyz;
      // vec3 vv3=fetchData(positionBuffer, index.z, VERTEX_COLUMNS).xyz;
      // vec3 m1=vv1+basePosition;
      // vec3 m2=vv2+basePosition;
      // vec3 m3=vv3+basePosition;
      // Triangle tri = Triangle(
      //   m1,
      //   m2,
      //   m3
      // );
      Triangle tri = Triangle(
        fetchData(positionBuffer, index.x, VERTEX_COLUMNS).xyz,
        fetchData(positionBuffer, index.y, VERTEX_COLUMNS).xyz,
        fetchData(positionBuffer, index.z, VERTEX_COLUMNS).xyz
      );

      if (intersectTriangle(ray, tri, maxDim, shear).t > 0.0) {
        return true;
      }
    }
  }

  return false;
}

`,`

  uniform sampler2D gPosition;
  uniform sampler2D gNormal;
  uniform sampler2D gFaceNormal;
  uniform sampler2D gColor;
  uniform sampler2D gMatProps;

  void surfaceInteractionDirect(vec2 coord, inout SurfaceInteraction si) {
    vec4 positionAndMeshIndex = texture(gPosition, coord);

    si.position = positionAndMeshIndex.xyz;

    float meshIndex = positionAndMeshIndex.w;

    vec4 normalMaterialType = texture(gNormal, coord);

    si.normal = normalize(normalMaterialType.xyz);
    si.materialType = int(normalMaterialType.w);

    si.faceNormal = normalize(texture(gFaceNormal, coord).xyz);

    si.color = texture(gColor, coord).rgb;

    vec4 matProps = texture(gMatProps, coord);
    si.roughness = matProps.x;
    si.metalness = matProps.y;

    si.hit = meshIndex > 0.0 ? true : false;
  }
`,`

// Noise texture used to generate a different random number for each pixel.
// We use blue noise in particular, but any type of noise will work.
uniform sampler2D noiseTex;

uniform float stratifiedSamples[SAMPLING_DIMENSIONS];
uniform float strataSize;

// Every time we call randomSample() in the shader, and for every call to render,
// we want that specific bit of the shader to fetch a sample from the same position in stratifiedSamples
// This allows us to use stratified sampling for each random variable in our path tracing
int sampleIndex = 0;

float pixelSeed;

void initRandom() {
  vec2 noiseSize = vec2(textureSize(noiseTex, 0));

  // tile the small noise texture across the entire screen
  pixelSeed = texture(noiseTex, vCoord / (pixelSize * noiseSize)).r;
}

float randomSample() {
  float stratifiedSample = stratifiedSamples[sampleIndex++];

  float random = fract((stratifiedSample + pixelSeed) * strataSize); // blue noise + stratified samples

  // transform random number between [0, 1] to (0, 1)
  return EPS + (1.0 - 2.0 * EPS) * random;
}

vec2 randomSampleVec2() {
  return vec2(randomSample(), randomSample());
}

struct MaterialSamples {
  vec2 s1;
  vec2 s2;
  vec2 s3;
};

MaterialSamples getRandomMaterialSamples() {
  MaterialSamples samples;

  samples.s1 = randomSampleVec2();
  samples.s2 = randomSampleVec2();
  samples.s3 = randomSampleVec2();

  return samples;
}
`,`

uniform sampler2D envMap;
uniform sampler2D envMapDistribution;
uniform sampler2D backgroundMap;

vec2 cartesianToEquirect(vec3 pointOnSphere) {
  float phi = mod(atan(-pointOnSphere.z, -pointOnSphere.x), TWOPI);
  float theta = acos(pointOnSphere.y);
  return vec2(phi * 0.5 * INVPI, theta * INVPI);
}

float getEnvmapV(float u, out int vOffset, out float pdf) {
  ivec2 size = textureSize(envMap, 0);

  int left = 0;
  int right = size.y + 1; // cdf length is the length of the env map + 1
  while (left < right) {
    int mid = (left + right) >> 1;
    float s = texelFetch(envMapDistribution, ivec2(0, mid), 0).x;
    if (s <= u) {
      left = mid + 1;
    } else {
      right = mid;
    }
  }
  vOffset = left - 1;

  // x channel is cumulative distribution of env map luminance
  // y channel is partial probability density of env map luminance
  vec2 s0 = texelFetch(envMapDistribution, ivec2(0, vOffset), 0).xy;
  vec2 s1 = texelFetch(envMapDistribution, ivec2(0, vOffset + 1), 0).xy;

  pdf = s0.y;

  return (float(vOffset) +  (u - s0.x) / (s1.x - s0.x)) / float(size.y);
}

float getEnvmapU(float u, int vOffset, out float pdf) {
  ivec2 size = textureSize(envMap, 0);

  int left = 0;
  int right = size.x + 1; // cdf length is the length of the env map + 1
  while (left < right) {
    int mid = (left + right) >> 1;
    float s = texelFetch(envMapDistribution, ivec2(1 + mid, vOffset), 0).x;
    if (s <= u) {
      left = mid + 1;
    } else {
      right = mid;
    }
  }
  int uOffset = left - 1;

  // x channel is cumulative distribution of env map luminance
  // y channel is partial probability density of env map luminance
  vec2 s0 = texelFetch(envMapDistribution, ivec2(1 + uOffset, vOffset), 0).xy;
  vec2 s1 = texelFetch(envMapDistribution, ivec2(1 + uOffset + 1, vOffset), 0).xy;

  pdf = s0.y;

  return (float(uOffset) + (u - s0.x) / (s1.x - s0.x)) / float(size.x);
}

// Perform two binary searches to find light direction.
vec3 sampleEnvmap(vec2 random, out vec2 uv, out float pdf) {
  vec2 partialPdf;
  int vOffset;

  uv.y = getEnvmapV(random.x, vOffset, partialPdf.y);
  uv.x = getEnvmapU(random.y, vOffset, partialPdf.x);

  float phi = uv.x * TWOPI;
  float theta = uv.y * PI;
  float cosTheta = cos(theta);
  float sinTheta = sin(theta);
  float cosPhi = cos(phi);
  float sinPhi = sin(phi);

  vec3 dir = vec3(-sinTheta * cosPhi, cosTheta, -sinTheta * sinPhi);

  pdf = partialPdf.x * partialPdf.y * INVPI2 / (2.0 * sinTheta);

  return dir;
}

float envMapPdf(vec2 uv) {
  vec2 size = vec2(textureSize(envMap, 0));

  float sinTheta = sin(uv.y * PI);

  uv *= size;

  float partialX = texelFetch(envMapDistribution, ivec2(1.0 + uv.x, uv.y), 0).y;
  float partialY = texelFetch(envMapDistribution, ivec2(0, uv.y), 0).y;

  return partialX * partialY * INVPI2 / (2.0 * sinTheta);
}

vec3 sampleEnvmapFromDirection(vec3 d) {
  vec2 uv = cartesianToEquirect(d);
  return textureLinear(envMap, uv).rgb;
}

vec3 sampleBackgroundFromDirection(vec3 d) {
  vec2 uv = cartesianToEquirect(d);
  return textureLinear(backgroundMap, uv).rgb;
}

`,`

// Computes the exact value of the Fresnel factor
// https://seblagarde.wordpress.com/2013/04/29/memo-on-fresnel-equations/
float fresnel(float cosTheta, float eta, float invEta) {
  eta = cosTheta > 0.0 ? eta : invEta;
  cosTheta = abs(cosTheta);

  float gSquared = eta * eta + cosTheta * cosTheta - 1.0;

  if (gSquared < 0.0) {
    return 1.0;
  }

  float g = sqrt(gSquared);

  float a = (g - cosTheta) / (g + cosTheta);
  float b = (cosTheta * (g + cosTheta) - 1.0) / (cosTheta * (g - cosTheta) + 1.0);

  return 0.5 * a * a * (1.0 + b * b);
}

float fresnelSchlickWeight(float cosTheta) {
  float w = 1.0 - cosTheta;
  return (w * w) * (w * w) * w;
}

// Computes Schlick's approximation of the Fresnel factor
// Assumes ray is moving from a less dense to a more dense medium
float fresnelSchlick(float cosTheta, float r0) {
  return mix(fresnelSchlickWeight(cosTheta), 1.0, r0);
}

// Computes Schlick's approximation of Fresnel factor
// Accounts for total internal reflection if ray is moving from a more dense to a less dense medium
float fresnelSchlickTIR(float cosTheta, float r0, float ni) {

  // moving from a more dense to a less dense medium
  if (cosTheta < 0.0) {
    float inv_eta = ni;
    float SinT2 = inv_eta * inv_eta * (1.0f - cosTheta * cosTheta);
    if (SinT2 > 1.0) {
        return 1.0; // total internal reflection
    }
    cosTheta = sqrt(1.0f - SinT2);
  }

  return mix(fresnelSchlickWeight(cosTheta), 1.0, r0);
}

float trowbridgeReitzD(float cosTheta, float alpha2) {
  float e = cosTheta * cosTheta * (alpha2 - 1.0) + 1.0;
  return alpha2 / (PI * e * e);
}

float trowbridgeReitzLambda(float cosTheta, float alpha2) {
  float cos2Theta = cosTheta * cosTheta;
  float tan2Theta = (1.0 - cos2Theta) / cos2Theta;
  return 0.5 * (-1.0 + sqrt(1.0 + alpha2 * tan2Theta));
}

// An implementation of Disney's principled BRDF
// https://disney-animation.s3.amazonaws.com/library/s2012_pbs_disney_brdf_notes_v2.pdf
vec3 materialBrdf(SurfaceInteraction si, vec3 viewDir, vec3 lightDir, float cosThetaL, float diffuseWeight, out float pdf) {
  vec3 halfVector = normalize(viewDir + lightDir);

  cosThetaL = abs(cosThetaL);
  float cosThetaV = abs(dot(si.normal, viewDir));
  float cosThetaH = abs(dot(si.normal, halfVector));
  float cosThetaD = abs(dot(lightDir, halfVector));

  float alpha2 = (si.roughness * si.roughness) * (si.roughness * si.roughness);

  float F = fresnelSchlick(cosThetaD, mix(R0, 0.6, si.metalness));
  float D = trowbridgeReitzD(cosThetaH, alpha2);

  float roughnessRemapped = 0.5 + 0.5 * si.roughness;
  float alpha2Remapped = (roughnessRemapped * roughnessRemapped) * (roughnessRemapped * roughnessRemapped);

  float G = 1.0 / (1.0 + trowbridgeReitzLambda(cosThetaV, alpha2Remapped) + trowbridgeReitzLambda(cosThetaL, alpha2Remapped));

  float specular = F * D * G / (4.0 * cosThetaV * cosThetaL);
  float specularPdf = D * cosThetaH / (4.0 * cosThetaD);

  float f = -0.5 + 2.0 * cosThetaD * cosThetaD * si.roughness;
  float diffuse = diffuseWeight * INVPI * (1.0 + f * fresnelSchlickWeight(cosThetaL)) * (1.0 + f * fresnelSchlickWeight(cosThetaV));
  float diffusePdf = cosThetaL * INVPI;

  pdf = mix(0.5 * (specularPdf + diffusePdf), specularPdf, si.metalness);

  return mix(si.color * diffuse + specular, si.color * specular, si.metalness);
}

`,`

// https://graphics.pixar.com/library/OrthonormalB/paper.pdf
mat3 orthonormalBasis(vec3 n) {
  float zsign = n.z >= 0.0 ? 1.0 : -1.0;
  float a = -1.0 / (zsign + n.z);
  float b = n.x * n.y * a;
  vec3 s = vec3(1.0 + zsign * n.x * n.x * a, zsign * b, -zsign * n.x);
  vec3 t = vec3(b, zsign + n.y * n.y * a, -n.y);
  return mat3(s, t, n);
}

// http://www.pbr-book.org/3ed-2018/Monte_Carlo_Integration/2D_Sampling_with_Multidimensional_Transformations.html#SamplingaUnitDisk
vec2 sampleCircle(vec2 p) {
  p = 2.0 * p - 1.0;

  bool greater = abs(p.x) > abs(p.y);

  float r = greater ? p.x : p.y;
  float theta = greater ? 0.25 * PI * p.y / p.x : PI * (0.5 - 0.25 * p.x / p.y);

  return r * vec2(cos(theta), sin(theta));
}

// http://www.pbr-book.org/3ed-2018/Monte_Carlo_Integration/2D_Sampling_with_Multidimensional_Transformations.html#Cosine-WeightedHemisphereSampling
vec3 cosineSampleHemisphere(vec2 p) {
  vec2 h = sampleCircle(p);
  float z = sqrt(max(0.0, 1.0 - h.x * h.x - h.y * h.y));
  return vec3(h, z);
}


// http://www.pbr-book.org/3ed-2018/Light_Transport_I_Surface_Reflection/Sampling_Reflection_Functions.html#MicrofacetBxDFs
// Instead of Beckmann distrubtion, we use the GTR2 (GGX) distrubtion as covered in Disney's Principled BRDF paper
vec3 lightDirSpecular(vec3 faceNormal, vec3 viewDir, mat3 basis, float roughness, vec2 random) {
  float phi = TWOPI * random.y;
  float alpha = roughness * roughness;
  float cosTheta = sqrt((1.0 - random.x) / (1.0 + (alpha * alpha - 1.0) * random.x));
  float sinTheta = sqrt(1.0 - cosTheta * cosTheta);

  vec3 halfVector = basis * sign(dot(faceNormal, viewDir)) * vec3(sinTheta * cos(phi), sinTheta * sin(phi), cosTheta);

  vec3 lightDir = reflect(-viewDir, halfVector);

  return lightDir;
}

vec3 lightDirDiffuse(vec3 faceNormal, vec3 viewDir, mat3 basis, vec2 random) {
  return basis * sign(dot(faceNormal, viewDir)) * cosineSampleHemisphere(random);
}

float powerHeuristic(float f, float g) {
  return (f * f) / (f * f + g * g);
}

`,`

void sampleMaterial(SurfaceInteraction si, int bounce, inout Path path) {
  bool lastBounce = bounce == BOUNCES;
  mat3 basis = orthonormalBasis(si.normal);
  vec3 viewDir = -path.ray.d;

  MaterialSamples samples = getRandomMaterialSamples();

  vec2 diffuseOrSpecular = samples.s1;
  vec2 lightDirSample = samples.s2;
  vec2 bounceDirSample = samples.s3;

  // Step 1: Add direct illumination of the light source (the hdr map)
  // On every bounce but the last, importance sample the light source
  // On the last bounce, multiple importance sample the brdf AND the light source, determined by random var

  vec3 lightDir;
  vec2 uv;
  float lightPdf;
  bool brdfSample = false;

  if (lastBounce && diffuseOrSpecular.x < 0.5) {
    // reuse this sample by multiplying by 2 to bring sample from [0, 0.5), to [0, 1)
    lightDir = 2.0 * diffuseOrSpecular.x < mix(0.5, 0.0, si.metalness) ?
      lightDirDiffuse(si.faceNormal, viewDir, basis, lightDirSample) :
      lightDirSpecular(si.faceNormal, viewDir, basis, si.roughness, lightDirSample);

    uv = cartesianToEquirect(lightDir);
    lightPdf = envMapPdf(uv);
    brdfSample = true;
  } else {
    lightDir = sampleEnvmap(lightDirSample, uv, lightPdf);
  }

  float cosThetaL = dot(si.normal, lightDir);

  float occluded = 1.0;

  float orientation = dot(si.faceNormal, viewDir) * cosThetaL;
  if (orientation < 0.0) {
    // light dir points towards surface. invalid dir.
    occluded = 0.0;
  }

  float diffuseWeight = 1.0;

  initRay(path.ray, si.position + EPS * lightDir, lightDir);
  if (intersectSceneShadow(path.ray)) {
    if (lastBounce) {
      diffuseWeight = 0.0;
    } else {
      occluded = 0.0;
    }
  }

  vec3 irr = textureLinear(envMap, uv).rgb;

  float scatteringPdf;
  vec3 brdf = materialBrdf(si, viewDir, lightDir, cosThetaL, diffuseWeight, scatteringPdf);

  float weight;
  if (lastBounce) {
    weight = brdfSample ?
      2.0 * powerHeuristic(scatteringPdf, lightPdf) / scatteringPdf :
      2.0 * powerHeuristic(lightPdf, scatteringPdf) / lightPdf;
  } else {
    weight = powerHeuristic(lightPdf, scatteringPdf) / lightPdf;
  }

  path.li += path.beta * occluded * brdf * irr * abs(cosThetaL) * weight;;

  // Step 2: Setup ray direction for next bounce by importance sampling the BRDF

  if (lastBounce) {
    return;
  }

  lightDir = diffuseOrSpecular.y < mix(0.5, 0.0, si.metalness) ?
    lightDirDiffuse(si.faceNormal, viewDir, basis, bounceDirSample) :
    lightDirSpecular(si.faceNormal, viewDir, basis, si.roughness, bounceDirSample);

  cosThetaL = dot(si.normal, lightDir);

  orientation = dot(si.faceNormal, viewDir) * cosThetaL;
  path.abort = orientation < 0.0;

  if (path.abort) {
    return;
  }

  brdf = materialBrdf(si, viewDir, lightDir, cosThetaL, 1.0, scatteringPdf);

  uv = cartesianToEquirect(lightDir);
  lightPdf = envMapPdf(uv);

  path.misWeight = powerHeuristic(scatteringPdf, lightPdf);

  path.beta *= abs(cosThetaL) * brdf / scatteringPdf;

  path.specularBounce = false;

  initRay(path.ray, si.position + EPS * lightDir, lightDir);
}
`,`

#ifdef USE_GLASS

void sampleGlassSpecular(SurfaceInteraction si, int bounce, inout Path path) {
  bool lastBounce = bounce == BOUNCES;
  vec3 viewDir = -path.ray.d;
  float cosTheta = dot(si.normal, viewDir);

  MaterialSamples samples = getRandomMaterialSamples();

  float reflectionOrRefraction = samples.s1.x;

  float F = si.materialType == THIN_GLASS ?
    fresnelSchlick(abs(cosTheta), R0) : // thin glass
    fresnelSchlickTIR(cosTheta, R0, IOR); // thick glass

  vec3 lightDir;

  if (reflectionOrRefraction < F) {
    lightDir = reflect(-viewDir, si.normal);
  } else {
    lightDir = si.materialType == THIN_GLASS ?
      refract(-viewDir, sign(cosTheta) * si.normal, INV_IOR_THIN) : // thin glass
      refract(-viewDir, sign(cosTheta) * si.normal, cosTheta < 0.0 ? IOR : INV_IOR); // thick glass
    path.beta *= si.color;
  }

  path.misWeight = 1.0;

  initRay(path.ray, si.position + EPS * lightDir, lightDir);

  path.li += lastBounce ? path.beta * sampleBackgroundFromDirection(lightDir) : vec3(0.0);

  path.specularBounce = true;
}

#endif

`,`

#ifdef USE_SHADOW_CATCHER

void sampleShadowCatcher(SurfaceInteraction si, int bounce, inout Path path) {
  bool lastBounce = bounce == BOUNCES;
  mat3 basis = orthonormalBasis(si.normal);
  vec3 viewDir = -path.ray.d;
  vec3 color = bounce == 1  || path.specularBounce ? sampleBackgroundFromDirection(-viewDir) : sampleEnvmapFromDirection(-viewDir);

  si.color = vec3(1, 1, 1);

  MaterialSamples samples = getRandomMaterialSamples();

  vec2 diffuseOrSpecular = samples.s1;
  vec2 lightDirSample = samples.s2;
  vec2 bounceDirSample = samples.s3;

  vec3 lightDir;
  vec2 uv;
  float lightPdf;
  bool brdfSample = false;

  if (diffuseOrSpecular.x < 0.5) {
    lightDir = 2.0 * diffuseOrSpecular.x < mix(0.5, 0.0, si.metalness) ?
      lightDirDiffuse(si.faceNormal, viewDir, basis, lightDirSample) :
      lightDirSpecular(si.faceNormal, viewDir, basis, si.roughness, lightDirSample);
    uv = cartesianToEquirect(lightDir);
    lightPdf = envMapPdf(uv);
    brdfSample = true;
  } else {
    lightDir = sampleEnvmap(lightDirSample, uv, lightPdf);
  }

  float cosThetaL = dot(si.normal, lightDir);

  float liContrib = 1.0;

  float orientation = dot(si.faceNormal, viewDir) * cosThetaL;
  if (orientation < 0.0) {
    liContrib = 0.0;
  }

  float occluded = 1.0;
  initRay(path.ray, si.position + EPS * lightDir, lightDir);
  if (intersectSceneShadow(path.ray)) {
    occluded = 0.0;
  }

  float irr = dot(luminance, textureLinear(envMap, uv).rgb);

  float scatteringPdf;
  vec3 brdf = materialBrdf(si, viewDir, lightDir, cosThetaL, 1.0, scatteringPdf);

  float weight = brdfSample ?
    2.0 * powerHeuristic(scatteringPdf, lightPdf) / scatteringPdf :
    2.0 * powerHeuristic(lightPdf, scatteringPdf) / lightPdf;

  float liEq = liContrib * brdf.r * irr * abs(cosThetaL) * weight;

  float alpha = liEq;
  path.alpha *= alpha;
  path.li *= alpha;

  path.li += occluded * path.beta * color * liEq;

  if (lastBounce) {
    return;
  }

  lightDir = diffuseOrSpecular.y < mix(0.5, 0.0, si.metalness) ?
    lightDirDiffuse(si.faceNormal, viewDir, basis, bounceDirSample) :
    lightDirSpecular(si.faceNormal, viewDir, basis, si.roughness, bounceDirSample);

  cosThetaL = dot(si.normal, lightDir);

  orientation = dot(si.faceNormal, viewDir) * cosThetaL;
  path.abort = orientation < 0.0;

  if (path.abort) {
    return;
  }

  brdf = materialBrdf(si, viewDir, lightDir, cosThetaL, 1.0, scatteringPdf);

  uv = cartesianToEquirect(lightDir);
  lightPdf = envMapPdf(uv);

  path.misWeight = 0.0;

  path.beta = color * abs(cosThetaL) * brdf.r / scatteringPdf;

  path.specularBounce = false;

  initRay(path.ray, si.position + EPS * lightDir, lightDir);
}

#endif

`],outputs:["light"],source:r=>`
  void bounce(inout Path path, int i, inout SurfaceInteraction si) {

    if (!si.hit) {
      vec3 irr = path.specularBounce ? sampleBackgroundFromDirection(path.ray.d) : sampleEnvmapFromDirection(path.ray.d);

      // hit a light source (the hdr map)
      // add contribution from light source
      // path.misWeight is the multiple importance sampled weight of this light source
      path.li += path.misWeight * path.beta * irr;
      path.abort = true;
      return;
    }

    #ifdef USE_GLASS
      if (si.materialType == THIN_GLASS || si.materialType == THICK_GLASS) {
        sampleGlassSpecular(si, i, path);
      }
    #endif
    #ifdef USE_SHADOW_CATCHER
      if (si.materialType == SHADOW_CATCHER) {
        sampleShadowCatcher(si, i, path);
      }
    #endif
    if (si.materialType == STANDARD) {
      sampleMaterial(si, i, path);
    }

    // Russian Roulette sampling
    if (i >= 2) {
      float q = 1.0 - dot(path.beta, luminance);
      if (randomSample() < q) {
        path.abort = true;
      }
      path.beta /= 1.0 - q;
    }

  }

  // Path tracing integrator as described in
  // http://www.pbr-book.org/3ed-2018/Light_Transport_I_Surface_Reflection/Path_Tracing.html#
  vec4 integrator(inout Ray ray) {
    Path path;
    path.ray = ray;
    path.li = vec3(0);
    path.alpha = 1.0;
    path.beta = vec3(1.0);
    path.specularBounce = true;
    path.abort = false;
    path.misWeight = 1.0;

    SurfaceInteraction si;

    // first surface interaction from g-buffer
    surfaceInteractionDirect(vCoord, si);

    // first surface interaction from ray interesction
    // intersectScene(path.ray, si);

    bounce(path, 1, si);

    // Manually unroll for loop.
    // Some hardware fails to iterate over a GLSL loop, so we provide this workaround
    // for (int i = 1; i < defines.bounces + 1, i += 1)
    // equivelant to
    ${ls("i",2,r.BOUNCES+1,1,`
      if (path.abort) {
        return vec4(path.li, path.alpha);
      }
      intersectScene(path.ray, si);
      bounce(path, i, si);
    `)}

    return vec4(path.li, path.alpha);
  }

  void main() {
    initRandom();

    vec2 vCoordAntiAlias = vCoord + jitter;

    vec3 direction = normalize(vec3(vCoordAntiAlias - 0.5, -1.0) * vec3(camera.aspect, 1.0, camera.fov));

    // Thin lens model with depth-of-field
    // http://www.pbr-book.org/3ed-2018/Camera_Models/Projective_Camera_Models.html#TheThinLensModelandDepthofField
    // vec2 lensPoint = camera.aperture * sampleCircle(randomSampleVec2());
    // vec3 focusPoint = -direction * camera.focus / direction.z; // intersect ray direction with focus plane

    // vec3 origin = vec3(lensPoint, 0.0);
    // direction = normalize(focusPoint - origin);

    // origin = vec3(camera.transform * vec4(origin, 1.0));
    // direction = mat3(camera.transform) * direction;

    vec3 origin = camera.transform[3].xyz;
    direction = mat3(camera.transform) * direction;

    Ray cam;
    initRay(cam, origin, direction);

    vec4 liAndAlpha = integrator(cam);

    if (!(liAndAlpha.x < INF && liAndAlpha.x > -EPS)) {
      liAndAlpha = vec4(0, 0, 0, 1);
    }

    out_light = liAndAlpha;

    out_light=clamp(out_light,vec4(0.),vec4(1.));

    // Stratified Sampling Sample Count Test
    // ---------------
    // Uncomment the following code
    // Then observe the colors of the image
    // If:
    // * The resulting image is pure black
    //   Extra samples are being passed to the shader that aren't being used.
    // * The resulting image contains red
    //   Not enough samples are being passed to the shader
    // * The resulting image contains only white with some black
    //   All samples are used by the shader. Correct result!

    // out_light = vec4(0, 0, 0, 1);
    // if (sampleIndex == SAMPLING_DIMENSIONS) {
    //   out_light = vec4(1, 1, 1, 1);
    // } else if (sampleIndex > SAMPLING_DIMENSIONS) {
    //   out_light = vec4(1, 0, 0, 1);
    // }
}
`};function us(r,e){const t=[],i=r**e;for(let l=0;l<i;l++)t[l]=l;let a=t.length;const s=[];function o(){a=0}function n(){a>=t.length&&(Pr(t),o());let l=t[a++];for(let h=0;h<e;h++)s[h]=l%r+Math.random(),l=Math.floor(l/r);return s}return{next:n,restart:o,strataCount:r}}function Gi(r,e){const t=[];for(const o of e)t.push(us(r,o));const i=[];function a(){let o=0;for(const n of t){const l=n.next();for(const h of l)i[o++]=h}return i}function s(){for(const o of t)o.restart()}return{next:a,restart:s,strataCount:r}}function ds(r,{bounces:e,decomposedScene:t,fullscreenQuad:i,materialBuffer:a,mergedMesh:s,optionalExtensions:o,basePosition:n}){e=ue(e,1,6);const l=[];for(let b=1;b<=e;b++)l.push(2,2,2),b>=2&&l.push(1);let h;const u=fs({bounces:e,decomposedScene:t,fullscreenQuad:i,gl:r,materialBuffer:a,mergedMesh:s,optionalExtensions:o,samplingDimensions:l,basePosition:n});if(u==null)return;function d(b,w){u.setUniform("pixelSize",1/b,1/w)}function f(b){u.setTexture("noiseTex",X(r,{data:b,wrapS:r.REPEAT,wrapT:r.REPEAT,storage:"halfFloat"})),window.changeToThree=!0}function m(b){u.setUniform("camera.transform",b.matrixWorld.elements),u.setUniform("camera.aspect",b.aspect),u.setUniform("camera.fov",.5/Math.tan(.5*Math.PI*b.fov/180))}function p(b,w){u.setUniform("jitter",b,w)}function v({position:b,normal:w,faceNormal:T,color:I,matProps:S}){u.setTexture("gPosition",b),u.setTexture("gNormal",w),u.setTexture("gFaceNormal",T),u.setTexture("gColor",I),u.setTexture("gMatProps",S)}function g(){u.setUniform("stratifiedSamples[0]",h.next())}function x(b){b>1&&b!==h.strataCount?h=Gi(b,l):h.restart(),u.setUniform("strataSize",1/b),g()}function _(){u.bindTextures()}function y(){u.unbindTextures()}function M(){u.useProgram(!1),i.draw()}return h=Gi(1,l),{unbindTextures:y,bindTextures:_,draw:M,nextSeed:g,outputLocs:u.outputLocs,setCamera:m,setJitter:p,setGBuffers:v,setNoise:f,setSize:d,setStrataCount:x}}function fs({bounces:r,decomposedScene:e,fullscreenQuad:t,gl:i,materialBuffer:a,mergedMesh:s,optionalExtensions:o,samplingDimensions:n,basePosition:l}){const{OES_texture_float_linear:h}=o,{background:u,directionalLights:d,ambientLights:f,environmentLights:m,meshes:p}=e,{geometry:v,materials:g,materialIndices:x,indicesSectionMap:_}=s,y=Wr(v);if(y==null){console.log("bvh exit");return}else console.log("bvh continue");const M=jr(y);if(M==null){console.log("flattenedbvh exit");return}window.currentTime=Date.now();const b=v.index.count/3,w=Oe(i,{defines:{OES_texture_float_linear:h,BVH_COLUMNS:ct(M.count).columnsLog,INDEX_COLUMNS:ct(b).columnsLog,VERTEX_COLUMNS:ct(v.attributes.position.count).columnsLog,STACK_SIZE:M.maxDepth,BOUNCES:r,USE_GLASS:g.some(z=>z.transparent),USE_SHADOW_CATCHER:g.some(z=>z.shadowCatcher),SAMPLING_DIMENSIONS:n.reduce((z,C)=>z+C),...a.defines},fragment:hs,vertex:t.vertexShader});w.setTexture("diffuseMap",a.textures.diffuseMap),w.setTexture("normalMap",a.textures.normalMap),w.setTexture("pbrMap",a.textures.pbrMap);let T=ps(v.getAttribute("position").array);w.setTexture("positionBuffer",ht(i,T,3)),w.setTexture("normalBuffer",ht(i,v.getAttribute("normal").array,3)),w.setTexture("uvBuffer",ht(i,v.getAttribute("uv").array,2)),w.setTexture("bvhBuffer",ht(i,M.buffer,4));const I=Jr(d,f,m),S=X(i,{data:I.data,storage:"halfFloat",minFilter:h?i.LINEAR:i.NEAREST,magFilter:h?i.LINEAR:i.NEAREST,width:I.width,height:I.height});w.setTexture("envMap",S);let R;if(u){const z=Zr(u);R=X(i,{data:z.data,storage:"halfFloat",minFilter:h?i.LINEAR:i.NEAREST,magFilter:h?i.LINEAR:i.NEAREST,width:z.width,height:z.height})}else R=S;w.setTexture("backgroundMap",R);const V=os(I);return w.setTexture("envMapDistribution",X(i,{data:V.data,storage:"halfFloat",width:V.width,height:V.height})),w}function ps(r,e){return r}function ct(r){const e=Math.round(Math.log2(Math.sqrt(r))),t=2**e,i=Math.ceil(r/t);return{columnsLog:e,columns:t,rows:i,size:i*t}}function ht(r,e,t){const i=ct(e.length/t);return X(r,{data:ms(e,t*i.size),width:i.columns,height:i.rows})}function ms(r,e){const t=new r.constructor(e);return t.set(r),t}var vs=c.THREE.Vector2;function gs(r){let t,i,a,s,o=new vs(1,1),n=xs(r);function l(d,f){t=d,i=f,h()}function h(){const d=t/i;a=Math.round(ue(Math.sqrt(n*d),1,t)),s=Math.round(ue(a/d,1,i)),o.set(a/t,s/i)}function u(d){if(!d)return;const f=600,m=20-d;n+=f*m,n=ue(n,8192,t*i),h()}return{adjustSize:u,setSize:l,scale:o,get width(){return a},get height(){return s}}}function xs(r){const e=r.getParameter(r.MAX_RENDERBUFFER_SIZE);if(e<=8192)return 8e4;if(e===16384)return 15e4;if(e>=32768)return 4e5}const ys={outputs:["light"],includes:[Jt],source:`
  in vec2 vCoord;

  uniform mediump sampler2D lightTex;
  uniform mediump sampler2D positionTex;
  uniform vec2 lightScale;
  uniform vec2 previousLightScale;

  uniform mediump sampler2D previousLightTex;
  uniform mediump sampler2D previousPositionTex;

  uniform mat4 historyCamera;
  uniform float blendAmount;
  uniform vec2 jitter;
  uniform vec3 basePosition;

  vec2 reproject(vec3 position) {
    vec4 historyCoord = historyCamera * vec4(position, 1.0);
    return 0.5 * historyCoord.xy / historyCoord.w + 0.5;
  }

  float getMeshId(sampler2D meshIdTex, vec2 vCoord) {
    return floor(texture(meshIdTex, vCoord).w);
  }

  void main() {
    vec3 currentPosition = textureLinear(positionTex, vCoord).xyz;
    float currentMeshId = getMeshId(positionTex, vCoord);

    vec4 currentLight = texture(lightTex, lightScale * vCoord);

    if (currentMeshId == 0.0) {
      out_light = currentLight;
      return;
    }

    vec2 hCoord = reproject(currentPosition) - jitter;

    vec2 hSizef = previousLightScale * vec2(textureSize(previousLightTex, 0));
    vec2 hSizeInv = 1.0 / hSizef;
    ivec2 hSize = ivec2(hSizef);

    vec2 hTexelf = hCoord * hSizef - 0.5;
    ivec2 hTexel = ivec2(hTexelf);
    vec2 f = fract(hTexelf);

    ivec2 texel[] = ivec2[](
      hTexel + ivec2(0, 0),
      hTexel + ivec2(1, 0),
      hTexel + ivec2(0, 1),
      hTexel + ivec2(1, 1)
    );

    float weights[] = float[](
      (1.0 - f.x) * (1.0 - f.y),
      f.x * (1.0 - f.y),
      (1.0 - f.x) * f.y,
      f.x * f.y
    );

    vec4 history;
    float sum;

    // bilinear sampling, rejecting samples that don't have a matching mesh id
    for (int i = 0; i < 4; i++) {
      vec2 gCoord = (vec2(texel[i]) + 0.5) * hSizeInv;

      float histMeshId = getMeshId(previousPositionTex, gCoord);

      float isValid = histMeshId != currentMeshId || any(greaterThanEqual(texel[i], hSize)) ? 0.0 : 1.0;

      float weight = isValid * weights[i];
      history += weight * texelFetch(previousLightTex, texel[i], 0);
      sum += weight;
    }

    if (sum > 0.0) {
      history /= sum;
    } else {
      // If all samples of bilinear fail, try a 3x3 box filter
      hTexel = ivec2(hTexelf + 0.5);

      for (int x = -1; x <= 1; x++) {
        for (int y = -1; y <= 1; y++) {
          ivec2 texel = hTexel + ivec2(x, y);
          vec2 gCoord = (vec2(texel) + 0.5) * hSizeInv;

          float histMeshId = getMeshId(previousPositionTex, gCoord);

          float isValid = histMeshId != currentMeshId || any(greaterThanEqual(texel, hSize)) ? 0.0 : 1.0;

          float weight = isValid;
          vec4 h = texelFetch(previousLightTex, texel, 0);
          history += weight * h;
          sum += weight;
        }
      }
      history = sum > 0.0 ? history / sum : history;
    }

    if (history.w > MAX_SAMPLES) {
      history.xyz *= MAX_SAMPLES / history.w;
      history.w = MAX_SAMPLES;
    }

    out_light = blendAmount * history + currentLight;
    //不能加0-1的限制，否则模型显示异常，因为真实值是可以大于1的
    //out_light=clamp(out_light,vec4(0.),vec4(1.));
  }
`};function _s(r,e){const{fullscreenQuad:t,maxReprojectedSamples:i}=e,a=Oe(r,{defines:{MAX_SAMPLES:i.toFixed(1)},vertex:t.vertexShader,fragment:ys}),s=new c.THREE.Matrix4;function o(h){s.multiplyMatrices(h.projectionMatrix,h.matrixWorldInverse),a.setUniform("historyCamera",s.elements)}function n(h,u){a.setUniform("jitter",h,u)}function l(h){const{blendAmount:u,light:d,lightScale:f,position:m,previousLight:p,previousLightScale:v,previousPosition:g}=h;a.setUniform("blendAmount",u),a.setUniform("lightScale",f.x,f.y),a.setUniform("previousLightScale",v.x,v.y),a.setTexture("lightTex",d),a.setTexture("positionTex",m),a.setTexture("previousLightTex",p),a.setTexture("previousPositionTex",g),a.useProgram(),t.draw()}return{draw:l,setJitter:n,setPreviousCamera:o}}const bs={includes:[Jt],outputs:["color"],source:`
  in vec2 vCoord;

  uniform sampler2D lightTex;
  uniform sampler2D positionTex;

  uniform vec2 lightScale;

  // Tonemapping functions from THREE.js

  vec3 linear(vec3 color) {
    return color;
  }
  // https://www.cs.utah.edu/~reinhard/cdrom/
  vec3 reinhard(vec3 color) {
    return clamp(color / (vec3(1.0) + color), vec3(0.0), vec3(1.0));
  }
  // http://filmicworlds.com/blog/filmic-tonemapping-operators/
  #define uncharted2Helper(x) max(((x * (0.15 * x + 0.10 * 0.50) + 0.20 * 0.02) / (x * (0.15 * x + 0.50) + 0.20 * 0.30)) - 0.02 / 0.30, vec3(0.0))
  const vec3 uncharted2WhitePoint = 1.0 / uncharted2Helper(vec3(WHITE_POINT));
  vec3 uncharted2( vec3 color ) {
    // John Hable's filmic operator from Uncharted 2 video game
    return clamp(uncharted2Helper(color) * uncharted2WhitePoint, vec3(0.0), vec3(1.0));
  }
  // http://filmicworlds.com/blog/filmic-tonemapping-operators/
  vec3 cineon( vec3 color ) {
    // optimized filmic operator by Jim Hejl and Richard Burgess-Dawson
    color = max(vec3( 0.0 ), color - 0.004);
    return pow((color * (6.2 * color + 0.5)) / (color * (6.2 * color + 1.7) + 0.06), vec3(2.2));
  }
  // https://knarkowicz.wordpress.com/2016/01/06/aces-filmic-tone-mapping-curve/
  vec3 acesFilmic( vec3 color ) {
    return clamp((color * (2.51 * color + 0.03)) / (color * (2.43 * color + 0.59) + 0.14), vec3(0.0), vec3(1.0));
  }

  #ifdef EDGE_PRESERVING_UPSCALE

  float getMeshId(sampler2D meshIdTex, vec2 vCoord) {
    return floor(texture(meshIdTex, vCoord).w);
  }

  vec4 getUpscaledLight(vec2 coord) {
    float meshId = getMeshId(positionTex, coord);

    vec2 sizef = lightScale * vec2(textureSize(positionTex, 0));
    vec2 texelf = coord * sizef - 0.5;
    ivec2 texel = ivec2(texelf);
    vec2 f = fract(texelf);

    ivec2 texels[] = ivec2[](
      texel + ivec2(0, 0),
      texel + ivec2(1, 0),
      texel + ivec2(0, 1),
      texel + ivec2(1, 1)
    );

    float weights[] = float[](
      (1.0 - f.x) * (1.0 - f.y),
      f.x * (1.0 - f.y),
      (1.0 - f.x) * f.y,
      f.x * f.y
    );

    vec4 upscaledLight;
    float sum;
    for (int i = 0; i < 4; i++) {
      vec2 pCoord = (vec2(texels[i]) + 0.5) / sizef;
      float isValid = getMeshId(positionTex, pCoord) == meshId ? 1.0 : 0.0;
      float weight = isValid * weights[i];
      upscaledLight += weight * texelFetch(lightTex, texels[i], 0);
      sum += weight;
    }

    if (sum > 0.0) {
      upscaledLight /= sum;
    } else {
      upscaledLight = texture(lightTex, lightScale * coord);
    }

    return upscaledLight;
  }
  #endif

  void main() {
    #ifdef EDGE_PRESERVING_UPSCALE
      vec4 upscaledLight = getUpscaledLight(vCoord);
    #else
      vec4 upscaledLight = texture(lightTex, lightScale * vCoord);
    #endif

    // alpha channel stores the number of samples progressively rendered
    // divide the sum of light by alpha to obtain average contribution of light

    // in addition, alpha contains a scale factor for the shadow catcher material
    // dividing by alpha normalizes the brightness of the shadow catcher to match the background env map.
    vec3 light = upscaledLight.rgb / upscaledLight.a;

    light *= EXPOSURE;

    light = TONE_MAPPING(light);

    light = pow(light, vec3(1.0 / 2.2)); // gamma correction

    out_color = vec4(light, 1.0);

    out_color=clamp(out_color,0.,1.);

    //out_color=vec4(1.0,0.0,0.0,1.0);

  }
`},Ms={[c.THREE.LinearToneMapping]:"linear",[c.THREE.ReinhardToneMapping]:"reinhard",[c.THREE.Uncharted2ToneMapping]:"uncharted2",[c.THREE.CineonToneMapping]:"cineon",[c.THREE.ACESFilmicToneMapping]:"acesFilmic"};function ws(r,e){const{fullscreenQuad:t,toneMappingParams:i}=e,a={gl:r,defines:{TONE_MAPPING:Ms[i.toneMapping]||"linear",WHITE_POINT:i.whitePoint.toExponential(),EXPOSURE:i.exposure.toExponential()},vertex:t.vertexShader,fragment:bs};a.defines.EDGE_PRESERVING_UPSCALE=!0;const s=Oe(r,a);a.defines.EDGE_PRESERVING_UPSCALE=!1;const o=Oe(r,a);function n(l){const{light:h,lightScale:u,position:d}=l,f=u.x!==1&&u.y!==1?s:o;f.setUniform("lightScale",u.x,u.y),f.setTexture("lightTex",h),f.setTexture("positionTex",d),f.useProgram(),t.draw()}return{draw:n}}function Ss(r){let t=-1,i=1,a,s,o,n,l=0,h=0,u,d=Ts(r);function f(){t=-1,u=NaN}function m(x,_){l=x,h=_,f(),p()}function p(){const x=l/h;a=Math.ceil(l/Math.round(l/Math.sqrt(d*x))),s=Math.ceil(a/x),o=Math.ceil(l/a),n=Math.ceil(h/s),i=o*n}function v(){const _=21-u/i;d+=5e3*Math.sign(_)*Math.sqrt(Math.abs(_)),d=ue(d,8192,l*h)}function g(x){t++,u+=x,t%i===0&&(u&&(v(),p()),u=0,t=0);const _=t===i-1,y=t%o,M=Math.floor(t/o)%n;return{x:y*a,y:M*s,tileWidth:a,tileHeight:s,isFirstTile:t===0,isLastTile:_}}return{nextTile:g,reset:f,setSize:m}}function Ts(r){const e=r.getParameter(r.MAX_RENDERBUFFER_SIZE);if(e<=8192)return 2e5;if(e===16384)return 4e5;if(e>=32768)return 6e5}const As="data:image/png;base64,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";var Ps=c.THREE.PerspectiveCamera,Es=c.THREE.Vector2;let Is;function Ls({gl:r,optionalExtensions:e,scene:t,toneMappingParams:i,bounces:a,basePosition:s}){const u=Ss(r),d=gs(r),f=lr(t),m=Or(f.meshes);window.currentTime=Date.now();const p=Dr(r,m.materials);window.currentTime=Date.now();const v=xr(r),g=ds(r,{bounces:a,decomposedScene:f,fullscreenQuad:v,materialBuffer:p,mergedMesh:m,optionalExtensions:e,scene:t,basePosition:s}),x=_s(r,{fullscreenQuad:v,maxReprojectedSamples:20}),_=ws(r,{fullscreenQuad:v,toneMappingParams:i}),y=wr(r,{materialBuffer:p,mergedMesh:m});let M=!1;const b=new Image;b.src=As,b.onload=()=>{g.setNoise(b),M=!0};let w,T,I,S=0,R=0,V=!0,z=()=>{};const C=new Ps;C.position.set(1,1,1),C.updateMatrixWorld();let E=0,H=0;const k=new Es(1,1);let G=k,O,ae,B,Ce,j,ye,_e,be;function Be(A,D){const J=()=>ot(r,{color:{0:X(r,{width:A,height:D,storage:"float",magFilter:r.LINEAR,minFilter:r.LINEAR})}}),re=()=>ot(r,{color:{0:X(r,{width:A,height:D,storage:"float",magFilter:r.LINEAR,minFilter:r.LINEAR})}}),se=()=>ot(r,{color:{0:X(r,{width:A,height:D,storage:"byte",magFilter:r.LINEAR,minFilter:r.LINEAR})}});O=J(),ae=J(),B=re(),Ce=re(),window.offScreenBuffer?_e=window.offScreenBuffer:(_e=se(),window.offScreenBuffer=_e),se();const Pi=X(r,{width:A,height:D,storage:"halfFloat"}),Ze=X(r,{width:A,height:D,storage:"halfFloat"}),en=X(r,{width:A,height:D,storage:"byte",channels:3}),tn=X(r,{width:A,height:D,storage:"byte",channels:2}),an=Ir(r,A,D),Qa=()=>ot(r,{color:{[y.outputLocs.position]:X(r,{width:A,height:D,storage:"float"}),[y.outputLocs.normal]:Pi,[y.outputLocs.faceNormal]:Ze,[y.outputLocs.color]:en,[y.outputLocs.matProps]:tn},depth:an});j=Qa(),ye=Qa(),be=O.color[g.outputLocs.light]}function Nt(){let A=B;B=Ce,Ce=A}function Ft(){let A=j;j=ye,ye=A}function Si(){let A=O;O=ae,ae=A}function Xa(){Nt(),Ft(),Si()}function Go(A,D){E=A,H=D,u.setSize(A,D),d.setSize(A,D),Be(A,D),V=!0}function Wo(A){T=A-w,w=A}function Ya(A,D){return Er(A.matrixWorld.elements,D.matrixWorld.elements)&&A.aspect===D.aspect&&A.fov===D.fov}function Ti(A,D,J=!0){g.setSize(A,D);const re=J?(Math.random()-.5)/A:0,se=J?(Math.random()-.5)/D:0;y.setJitter(re,se),g.setJitter(re,se),x.setJitter(re,se),S===0?g.setStrataCount(1):S===4?g.setStrataCount(6):g.nextSeed()}function Za(A){A.bind(),r.clear(r.COLOR_BUFFER_BIT),A.unbind()}function Ja(A,D,J){A.bind(),window.kkBuffer=A,r.blendEquation(r.FUNC_ADD),r.blendFunc(r.ONE,r.ONE),r.enable(r.BLEND),r.viewport(0,0,D,J),g.draw(),r.disable(r.BLEND),g.unbindTextures(),A.unbind()}function jo(A,D,J){A.bind(),r.viewport(0,0,D,J),g.draw(),A.unbind()}function zt(A,D){_e.bind(),r.clear(r.COLOR_BUFFER_BIT|r.DEPTH_BUFFER_BIT),r.viewport(0,0,r.drawingBufferWidth,r.drawingBufferHeight),_.draw({light:A,lightScale:D,position:j.color[y.outputLocs.position]}),be=A,G=D.clone()}function Ai(){j.bind(),r.disable(r.CULL_FACE),r.clear(r.COLOR_BUFFER_BIT|r.DEPTH_BUFFER_BIT),r.viewport(0,0,E,H),y.draw(),j.unbind(),g.setGBuffers({position:j.color[y.outputLocs.position],normal:j.color[y.outputLocs.normal],faceNormal:j.color[y.outputLocs.faceNormal],color:j.color[y.outputLocs.color],matProps:j.color[y.outputLocs.matProps]})}function qo(A,D,J,re,se){r.scissor(D,J,re,se),r.enable(r.SCISSOR_TEST),Ja(A,E,H),r.disable(r.SCISSOR_TEST)}function Ka(A,D){g.setCamera(A),y.setCamera(A),x.setPreviousCamera(D),D.copy(A)}function Xo(){S>0&&Xa(),R>=2&&d.adjustSize(T),Ti(d.width,d.height,!1),Ai(),g.bindTextures(),jo(O,d.width,d.height),B.bind(),r.viewport(0,0,d.width,d.height),x.draw({blendAmount:1,light:O.color[0],lightScale:d.scale,position:j.color[y.outputLocs.position],previousLight:be,previousLightScale:G,previousPosition:ye.color[y.outputLocs.position]}),B.unbind(),zt(B.color[0],d.scale),Xa()}function Yo(){const{x:A,y:D,tileWidth:J,tileHeight:re,isFirstTile:se,isLastTile:Pi}=u.nextTile(T);if(se&&(S===0?(Za(O),x.setPreviousCamera(C)):(z(S,w-I||NaN),I=w),Ti(E,H,!0),Ai(),g.bindTextures()),qo(O,A,D,J,re),Pi){S++;let Ze=ue(1-S/20,0,1);Ze*=Ze,Ze>0?(B.bind(),r.viewport(0,0,E,H),x.draw({blendAmount:Ze,light:O.color[0],lightScale:k,position:j.color[y.outputLocs.position],previousLight:Ce.color[0],previousLightScale:d.scale,previousPosition:ye.color[y.outputLocs.position]}),B.unbind(),zt(B.color[0],k)):zt(O.color[0],k)}}function Zo(A){M&&(Ya(A,C)?(Yo(),R=0):(Ka(A,C),V?V=!1:(Xo(),R++),u.reset(),S=0))}function Jo(A,D){M&&(Ft(),Nt(),Ya(A,C)?S++:(S=0,Za(O)),Ka(A,C),Ti(E,H,!1),Ai(),g.bindTextures(),Ja(O,E,H),B.bind(),r.viewport(0,0,E,H),x.draw({blendAmount:1,light:O.color[0],lightScale:k,position:j.color[y.outputLocs.position],previousLight:be,previousLightScale:G,previousPosition:ye.color[y.outputLocs.position]}),B.unbind(),zt(B.color[0],k))}function Ko(){return Is}function Qo(){}function $o(){m&&m.geometry&&(m.geometry.dispose(),m.geometry=null)}return window.currentTime=Date.now(),{resetLastTime:Qo,getScreenImageData:Ko,draw:Zo,drawFull:Jo,setSize:Go,time:Wo,disposeGeometry:$o,getTotalSamplesRendered(){return S},set onSampleRendered(A){z=A},get onSampleRendered(){return z}}}const Wi=["EXT_color_buffer_float","EXT_float_blend"],Rs=["OES_texture_float_linear"];function ji(r={}){let e=r.canvas||document.createElement("canvas"),t=r.gl;e=t.canvas,kt(t,Wi);const i=kt(t,Rs);let a=null;const s=new c.THREE.Vector2(1920,947);let o=1;const n={bounces:2,domElement:e,maxHardwareUsage:!1,needsUpdate:!0,onSampleRendered:null,renderWhenOffFocus:!0,toneMapping:c.THREE.LinearToneMapping,toneMappingExposure:1,toneMappingWhitePoint:1,gl:t};function l(p,v){p.updateMatrixWorld();const g={exposure:n.toneMappingExposure,whitePoint:n.toneMappingWhitePoint,toneMapping:n.toneMapping},x=n.bounces;a=Ls({gl:t,optionalExtensions:i,scene:p,toneMappingParams:g,bounces:x}),a!=null&&(n.pipeline=a,a.onSampleRendered=(..._)=>{n.onSampleRendered&&n.onSampleRendered(..._)},n.setSize(s.width,s.height),n.needsUpdate=!1)}n.setSize=(p,v,g=!0)=>{s.set(p,v),e.width=s.width*o,e.height=s.height*o,g&&(e.style.width=`${s.width}px`,e.style.height=`${s.height}px`),a&&a.setSize(s.width*o,s.height*o)},n.getSize=p=>(p||(p=new c.THREE.Vector2),p.copy(s)),n.setPixelRatio=p=>{p&&(o=p,n.setSize(s.width,s.height,!1))},n.getPixelRatio=()=>o,n.getTotalSamplesRendered=()=>{if(a)return a.getTotalSamplesRendered()};let h=1,u=NaN,d=!1;function f(){h=NaN}n.sync=p=>{u=p||performance.now()},n.disposeGeometry=()=>{a&&a.disposeGeometry()};let m=!1;return n.render=(p,v)=>{if(!n.renderWhenOffFocus){const g=document.hasFocus();if(g)g&&!m&&(m=g,f());else{m=g;return}}n.needsUpdate&&(l(p),window.currentTime=Date.now()),isNaN(u)&&(d||(console.warn("Ray Tracing Renderer warning: For improved performance, please call renderer.sync(time) before render.render(scene, camera), with the time parameter equalling the parameter passed to the callback of requestAnimationFrame"),d=!0),u=performance.now()),a.time(h*u),h=1,u=NaN,v.updateMatrixWorld(),n.maxHardwareUsage?a.drawFull(v):a.draw(v)},document.addEventListener("visibilitychange",f),n.dispose=()=>{document.removeEventListener("visibilitychange",f),a=null},n}ji.isSupported=()=>{const r=document.createElement("canvas").getContext("webgl2",{failIfMajorPerformanceCaveat:!0});if(!r)return!1;const e=kt(r,Wi);for(let t in e)if(!e[t])return!1;return!0};function ut(r,e,t,i,a,s){this.a=r,this.b=e,this.c=t,this.normal=i&&i.isVector3?i:new c.Vector3,this.vertexNormals=Array.isArray(i)?i:[],this.color=a&&a.isColor?a:new c.Color,this.vertexColors=Array.isArray(a)?a:[],this.materialIndex=s!==void 0?s:0}Object.assign(ut.prototype,{clone:function(){return new this.constructor().copy(this)},copy:function(r){this.a=r.a,this.b=r.b,this.c=r.c,this.normal.copy(r.normal),this.color.copy(r.color),this.materialIndex=r.materialIndex;for(var e=0,t=r.vertexNormals.length;e<t;e++)this.vertexNormals[e]=r.vertexNormals[e].clone();for(var e=0,t=r.vertexColors.length;e<t;e++)this.vertexColors[e]=r.vertexColors[e].clone();return this}});var Cs=0,ee=new c.Matrix4,Kt=new c.Object3D,dt=new c.Vector3;function Fe(){Object.defineProperty(this,"id",{value:Cs+=2}),this.uuid=c.MathUtils.generateUUID(),this.name="",this.type="Geometry",this.vertices=[],this.colors=[],this.faces=[],this.faceVertexUvs=[[]],this.morphTargets=[],this.morphNormals=[],this.skinWeights=[],this.skinIndices=[],this.lineDistances=[],this.boundingBox=null,this.boundingSphere=null,this.elementsNeedUpdate=!1,this.verticesNeedUpdate=!1,this.uvsNeedUpdate=!1,this.normalsNeedUpdate=!1,this.colorsNeedUpdate=!1,this.lineDistancesNeedUpdate=!1,this.groupsNeedUpdate=!1}Fe.prototype=Object.assign(Object.create(c.EventDispatcher.prototype),{constructor:Fe,isGeometry:!0,applyMatrix:function(r){for(var e=new c.Matrix3().getNormalMatrix(r),t=0,i=this.vertices.length;t<i;t++){var a=this.vertices[t];a.applyMatrix4(r)}for(var t=0,i=this.faces.length;t<i;t++){var s=this.faces[t];s.normal.applyMatrix3(e).normalize();for(var o=0,n=s.vertexNormals.length;o<n;o++)s.vertexNormals[o].applyMatrix3(e).normalize()}return this.boundingBox!==null&&this.computeBoundingBox(),this.boundingSphere!==null&&this.computeBoundingSphere(),this.verticesNeedUpdate=!0,this.normalsNeedUpdate=!0,this},rotateX:function(r){return ee.makeRotationX(r),this.applyMatrix(ee),this},rotateY:function(r){return ee.makeRotationY(r),this.applyMatrix(ee),this},rotateZ:function(r){return ee.makeRotationZ(r),this.applyMatrix(ee),this},translate:function(r,e,t){return ee.makeTranslation(r,e,t),this.applyMatrix(ee),this},scale:function(r,e,t){return ee.makeScale(r,e,t),this.applyMatrix(ee),this},lookAt:function(r){return Kt.lookAt(r),Kt.updateMatrix(),this.applyMatrix(Kt.matrix),this},fromBufferGeometry:function(r){var e=this,t=r.index!==null?r.index.array:void 0,i=r.attributes;if(i.position===void 0)return console.error("THREE.Geometry.fromBufferGeometry(): Position attribute required for conversion."),this;var a=i.position.array,s=i.normal!==void 0?i.normal.array:void 0,o=i.color!==void 0?i.color.array:void 0,n=i.uv!==void 0?i.uv.array:void 0,l=i.uv2!==void 0?i.uv2.array:void 0;l!==void 0&&(this.faceVertexUvs[1]=[]);for(var h=0;h<a.length;h+=3)e.vertices.push(new c.Vector3().fromArray(a,h)),o!==void 0&&e.colors.push(new c.Color().fromArray(o,h));function u(x,_,y,M=-1){var b=o===void 0?[]:[e.colors[x].clone(),e.colors[_].clone(),e.colors[y].clone()],w=s===void 0?[]:[new c.Vector3().fromArray(s,x*3),new c.Vector3().fromArray(s,_*3),new c.Vector3().fromArray(s,y*3)],T=new ut(x,_,y,w,b,M);e.faces.push(T),n!==void 0&&e.faceVertexUvs[0].push([new c.Vector2().fromArray(n,x*2),new c.Vector2().fromArray(n,_*2),new c.Vector2().fromArray(n,y*2)]),l!==void 0&&e.faceVertexUvs[1].push([new c.Vector2().fromArray(l,x*2),new c.Vector2().fromArray(l,_*2),new c.Vector2().fromArray(l,y*2)])}var d=r.groups;if(d.length>0)for(var h=0;h<d.length;h++)for(var f=d[h],m=f.start,p=f.count,v=m,g=m+p;v<g;v+=3)t!==void 0?u(t[v],t[v+1],t[v+2],f.materialIndex):u(v,v+1,v+2,f.materialIndex);else if(t!==void 0)for(var h=0;h<t.length;h+=3)u(t[h],t[h+1],t[h+2]);else for(var h=0;h<a.length/3;h+=3)u(h,h+1,h+2);return this.computeFaceNormals(),r.boundingBox!==null&&(this.boundingBox=r.boundingBox.clone()),r.boundingSphere!==null&&(this.boundingSphere=r.boundingSphere.clone()),this},center:function(){return this.computeBoundingBox(),this.boundingBox.getCenter(dt).negate(),this.translate(dt.x,dt.y,dt.z),this},normalize:function(){this.computeBoundingSphere();var r=this.boundingSphere.center,e=this.boundingSphere.radius,t=e===0?1:1/e,i=new c.Matrix4;return i.set(t,0,0,-t*r.x,0,t,0,-t*r.y,0,0,t,-t*r.z,0,0,0,1),this.applyMatrix(i),this},computeFaceNormals:function(){for(var r=new c.Vector3,e=new c.Vector3,t=0,i=this.faces.length;t<i;t++){var a=this.faces[t],s=this.vertices[a.a],o=this.vertices[a.b],n=this.vertices[a.c];r.subVectors(n,o),e.subVectors(s,o),r.cross(e),r.normalize(),a.normal.copy(r)}},computeVertexNormals:function(r){r===void 0&&(r=!0);var e,t,i,a,s,o;for(o=new Array(this.vertices.length),e=0,t=this.vertices.length;e<t;e++)o[e]=new c.Vector3;if(r){var n,l,h,u=new c.Vector3,d=new c.Vector3;for(i=0,a=this.faces.length;i<a;i++)s=this.faces[i],n=this.vertices[s.a],l=this.vertices[s.b],h=this.vertices[s.c],u.subVectors(h,l),d.subVectors(n,l),u.cross(d),o[s.a].add(u),o[s.b].add(u),o[s.c].add(u)}else for(this.computeFaceNormals(),i=0,a=this.faces.length;i<a;i++)s=this.faces[i],o[s.a].add(s.normal),o[s.b].add(s.normal),o[s.c].add(s.normal);for(e=0,t=this.vertices.length;e<t;e++)o[e].normalize();for(i=0,a=this.faces.length;i<a;i++){s=this.faces[i];var f=s.vertexNormals;f.length===3?(f[0].copy(o[s.a]),f[1].copy(o[s.b]),f[2].copy(o[s.c])):(f[0]=o[s.a].clone(),f[1]=o[s.b].clone(),f[2]=o[s.c].clone())}this.faces.length>0&&(this.normalsNeedUpdate=!0)},computeFlatVertexNormals:function(){var r,e,t;for(this.computeFaceNormals(),r=0,e=this.faces.length;r<e;r++){t=this.faces[r];var i=t.vertexNormals;i.length===3?(i[0].copy(t.normal),i[1].copy(t.normal),i[2].copy(t.normal)):(i[0]=t.normal.clone(),i[1]=t.normal.clone(),i[2]=t.normal.clone())}this.faces.length>0&&(this.normalsNeedUpdate=!0)},computeMorphNormals:function(){var r,e,t,i,a;for(t=0,i=this.faces.length;t<i;t++)for(a=this.faces[t],a.__originalFaceNormal?a.__originalFaceNormal.copy(a.normal):a.__originalFaceNormal=a.normal.clone(),a.__originalVertexNormals||(a.__originalVertexNormals=[]),r=0,e=a.vertexNormals.length;r<e;r++)a.__originalVertexNormals[r]?a.__originalVertexNormals[r].copy(a.vertexNormals[r]):a.__originalVertexNormals[r]=a.vertexNormals[r].clone();var s=new Fe;for(s.faces=this.faces,r=0,e=this.morphTargets.length;r<e;r++){if(!this.morphNormals[r]){this.morphNormals[r]={},this.morphNormals[r].faceNormals=[],this.morphNormals[r].vertexNormals=[];var o=this.morphNormals[r].faceNormals,n=this.morphNormals[r].vertexNormals,h,u;for(t=0,i=this.faces.length;t<i;t++)h=new c.Vector3,u={a:new c.Vector3,b:new c.Vector3,c:new c.Vector3},o.push(h),n.push(u)}var l=this.morphNormals[r];s.vertices=this.morphTargets[r].vertices,s.computeFaceNormals(),s.computeVertexNormals();var h,u;for(t=0,i=this.faces.length;t<i;t++)a=this.faces[t],h=l.faceNormals[t],u=l.vertexNormals[t],h.copy(a.normal),u.a.copy(a.vertexNormals[0]),u.b.copy(a.vertexNormals[1]),u.c.copy(a.vertexNormals[2])}for(t=0,i=this.faces.length;t<i;t++)a=this.faces[t],a.normal=a.__originalFaceNormal,a.vertexNormals=a.__originalVertexNormals},computeBoundingBox:function(){this.boundingBox===null&&(this.boundingBox=new c.Box3),this.boundingBox.setFromPoints(this.vertices)},computeBoundingSphere:function(){this.boundingSphere===null&&(this.boundingSphere=new c.Sphere),this.boundingSphere.setFromPoints(this.vertices)},merge:function(r,e,t){if(!(r&&r.isGeometry)){console.error("THREE.Geometry.merge(): geometry not an instance of THREE.Geometry.",r);return}var i,a=this.vertices.length,s=this.vertices,o=r.vertices,n=this.faces,l=r.faces,h=this.colors,u=r.colors;t===void 0&&(t=0),e!==void 0&&(i=new c.Matrix3().getNormalMatrix(e));for(var d=0,f=o.length;d<f;d++){var m=o[d],p=m.clone();e!==void 0&&p.applyMatrix4(e),s.push(p)}for(var d=0,f=u.length;d<f;d++)h.push(u[d].clone());for(d=0,f=l.length;d<f;d++){var v=l[d],g,x,_,y=v.vertexNormals,M=v.vertexColors;g=new ut(v.a+a,v.b+a,v.c+a),g.normal.copy(v.normal),i!==void 0&&g.normal.applyMatrix3(i).normalize();for(var b=0,w=y.length;b<w;b++)x=y[b].clone(),i!==void 0&&x.applyMatrix3(i).normalize(),g.vertexNormals.push(x);g.color.copy(v.color);for(var b=0,w=M.length;b<w;b++)_=M[b],g.vertexColors.push(_.clone());g.materialIndex=v.materialIndex+t,n.push(g)}for(var d=0,f=r.faceVertexUvs.length;d<f;d++){var T=r.faceVertexUvs[d];this.faceVertexUvs[d]===void 0&&(this.faceVertexUvs[d]=[]);for(var b=0,w=T.length;b<w;b++){for(var I=T[b],S=[],R=0,V=I.length;R<V;R++)S.push(I[R].clone());this.faceVertexUvs[d].push(S)}}},mergeMesh:function(r){if(!(r&&r.isMesh)){console.error("THREE.Geometry.mergeMesh(): mesh not an instance of THREE.Mesh.",r);return}r.matrixAutoUpdate&&r.updateMatrix(),this.merge(r.geometry,r.matrix)},mergeVertices:function(){var r={},e=[],t=[],i,a,s=4,o=Math.pow(10,s),n,l,h,u,d,f;for(n=0,l=this.vertices.length;n<l;n++)i=this.vertices[n],a=Math.round(i.x*o)+"_"+Math.round(i.y*o)+"_"+Math.round(i.z*o),r[a]===void 0?(r[a]=n,e.push(this.vertices[n]),t[n]=e.length-1):t[n]=t[r[a]];var m=[];for(n=0,l=this.faces.length;n<l;n++){h=this.faces[n],h.a=t[h.a],h.b=t[h.b],h.c=t[h.c],u=[h.a,h.b,h.c];for(var p=0;p<3;p++)if(u[p]===u[(p+1)%3]){m.push(n);break}}for(n=m.length-1;n>=0;n--){var v=m[n];for(this.faces.splice(v,1),d=0,f=this.faceVertexUvs.length;d<f;d++)this.faceVertexUvs[d].splice(v,1)}var g=this.vertices.length-e.length;return this.vertices=e,g},setFromPoints:function(r){this.vertices=[];for(var e=0,t=r.length;e<t;e++){var i=r[e];this.vertices.push(new c.Vector3(i.x,i.y,i.z||0))}return this},sortFacesByMaterialIndex:function(){for(var r=this.faces,e=r.length,t=0;t<e;t++)r[t]._id=t;function i(h,u){return h.materialIndex-u.materialIndex}r.sort(i);var a=this.faceVertexUvs[0],s=this.faceVertexUvs[1],o,n;a&&a.length===e&&(o=[]),s&&s.length===e&&(n=[]);for(var t=0;t<e;t++){var l=r[t]._id;o&&o.push(a[l]),n&&n.push(s[l])}o&&(this.faceVertexUvs[0]=o),n&&(this.faceVertexUvs[1]=n)},toJSON:function(){var r={metadata:{version:4.5,type:"Geometry",generator:"Geometry.toJSON"}};if(r.uuid=this.uuid,r.type=this.type,this.name!==""&&(r.name=this.name),this.parameters!==void 0){var e=this.parameters;for(var t in e)e[t]!==void 0&&(r[t]=e[t]);return r}for(var i=[],a=0;a<this.vertices.length;a++){var s=this.vertices[a];i.push(s.x,s.y,s.z)}for(var o=[],n=[],l={},h=[],u={},d=[],f={},a=0;a<this.faces.length;a++){var m=this.faces[a],p=!0,v=!1,g=this.faceVertexUvs[0][a]!==void 0,x=m.normal.length()>0,_=m.vertexNormals.length>0,y=m.color.r!==1||m.color.g!==1||m.color.b!==1,M=m.vertexColors.length>0,b=0;if(b=S(b,0,0),b=S(b,1,p),b=S(b,2,v),b=S(b,3,g),b=S(b,4,x),b=S(b,5,_),b=S(b,6,y),b=S(b,7,M),o.push(b),o.push(m.a,m.b,m.c),o.push(m.materialIndex),g){var w=this.faceVertexUvs[0][a];o.push(z(w[0]),z(w[1]),z(w[2]))}if(x&&o.push(R(m.normal)),_){var T=m.vertexNormals;o.push(R(T[0]),R(T[1]),R(T[2]))}if(y&&o.push(V(m.color)),M){var I=m.vertexColors;o.push(V(I[0]),V(I[1]),V(I[2]))}}function S(C,E,H){return H?C|1<<E:C&~(1<<E)}function R(C){var E=C.x.toString()+C.y.toString()+C.z.toString();return l[E]!==void 0||(l[E]=n.length/3,n.push(C.x,C.y,C.z)),l[E]}function V(C){var E=C.r.toString()+C.g.toString()+C.b.toString();return u[E]!==void 0||(u[E]=h.length,h.push(C.getHex())),u[E]}function z(C){var E=C.x.toString()+C.y.toString();return f[E]!==void 0||(f[E]=d.length/2,d.push(C.x,C.y)),f[E]}return r.data={},r.data.vertices=i,r.data.normals=n,h.length>0&&(r.data.colors=h),d.length>0&&(r.data.uvs=[d]),r.data.faces=o,r},clone:function(){return new Fe().copy(this)},copy:function(r){var e,t,i,a,s,o;this.vertices=[],this.colors=[],this.faces=[],this.faceVertexUvs=[[]],this.morphTargets=[],this.morphNormals=[],this.skinWeights=[],this.skinIndices=[],this.lineDistances=[],this.boundingBox=null,this.boundingSphere=null,this.name=r.name;var n=r.vertices;for(e=0,t=n.length;e<t;e++)this.vertices.push(n[e].clone());var l=r.colors;for(e=0,t=l.length;e<t;e++)this.colors.push(l[e].clone());var h=r.faces;for(e=0,t=h.length;e<t;e++)this.faces.push(h[e].clone());for(e=0,t=r.faceVertexUvs.length;e<t;e++){var u=r.faceVertexUvs[e];for(this.faceVertexUvs[e]===void 0&&(this.faceVertexUvs[e]=[]),i=0,a=u.length;i<a;i++){var d=u[i],f=[];for(s=0,o=d.length;s<o;s++){var m=d[s];f.push(m.clone())}this.faceVertexUvs[e].push(f)}}var p=r.morphTargets;for(e=0,t=p.length;e<t;e++){var v={};if(v.name=p[e].name,p[e].vertices!==void 0)for(v.vertices=[],i=0,a=p[e].vertices.length;i<a;i++)v.vertices.push(p[e].vertices[i].clone());if(p[e].normals!==void 0)for(v.normals=[],i=0,a=p[e].normals.length;i<a;i++)v.normals.push(p[e].normals[i].clone());this.morphTargets.push(v)}var g=r.morphNormals;for(e=0,t=g.length;e<t;e++){var x={};if(g[e].vertexNormals!==void 0)for(x.vertexNormals=[],i=0,a=g[e].vertexNormals.length;i<a;i++){var _=g[e].vertexNormals[i],y={};y.a=_.a.clone(),y.b=_.b.clone(),y.c=_.c.clone(),x.vertexNormals.push(y)}if(g[e].faceNormals!==void 0)for(x.faceNormals=[],i=0,a=g[e].faceNormals.length;i<a;i++)x.faceNormals.push(g[e].faceNormals[i].clone());this.morphNormals.push(x)}var M=r.skinWeights;for(e=0,t=M.length;e<t;e++)this.skinWeights.push(M[e].clone());var b=r.skinIndices;for(e=0,t=b.length;e<t;e++)this.skinIndices.push(b[e].clone());var w=r.lineDistances;for(e=0,t=w.length;e<t;e++)this.lineDistances.push(w[e]);var T=r.boundingBox;T!==null&&(this.boundingBox=T.clone());var I=r.boundingSphere;return I!==null&&(this.boundingSphere=I.clone()),this.elementsNeedUpdate=r.elementsNeedUpdate,this.verticesNeedUpdate=r.verticesNeedUpdate,this.uvsNeedUpdate=r.uvsNeedUpdate,this.normalsNeedUpdate=r.normalsNeedUpdate,this.colorsNeedUpdate=r.colorsNeedUpdate,this.lineDistancesNeedUpdate=r.lineDistancesNeedUpdate,this.groupsNeedUpdate=r.groupsNeedUpdate,this},dispose:function(){this.dispatchEvent({type:"dispose"})}});const Ot=class Ot{static GUID(){let e="";for(let t=1;t<=8;t++){let i=Math.floor(Math.random()*16).toString(16);e+=i}return e}static narrowImage(e,t=256){return new Promise((i,a)=>{let s=document.createElement("img");s.src=e;let o=new Image;o.src=e,o.onload=function(){let n=document.createElement("canvas"),l=o.width/o.height,h=parseInt(t/l);n.width=t,n.height=h;let u=n.getContext("2d");u.drawImage(s,0,0,o.width,o.height,0,0,t,h);let d=n.toDataURL("image/png",.9);u.clearRect(0,0,n.width,n.height),i(d)}})}static narrowImage2(e,t=256){return new Promise((i,a)=>{let s=document.createElement("img");s.src=e;let o=new Image;o.src=e,o.onload=function(){let n=document.createElement("canvas"),l=o.width/o.height,h=parseInt(t/l);n.width=t,n.height=h,n.getContext("2d").drawImage(s,0,0,o.width,o.height,0,0,t,h);let d=n.toDataURL("image/png",.9);i({image:d,height:h})}})}static Str2Bytes(e){let t=0,i=e.length;if(i%2!==0)return null;i/=2;let a=new Array;for(let s=0;s<i;s++){let o=e.substr(t,2),n=parseInt(o,16);a.push(n),t+=2}return a}static colorRGB2Hex(e){var t="#"+(16777216+(e[0]<<16)+(e[1]<<8)+e[0]).toString(16).slice(1);return t}static decode(e){let t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";var i="",a,s,o,n,l,h,u,d=0;for(e=e.replace(/[^A-Za-z0-9\+\/\=]/g,"");d<e.length;)n=t.indexOf(e.charAt(d++)),l=t.indexOf(e.charAt(d++)),h=t.indexOf(e.charAt(d++)),u=t.indexOf(e.charAt(d++)),a=n<<2|l>>4,s=(l&15)<<4|h>>2,o=(h&3)<<6|u,i=i+String.fromCharCode(a),h!==64&&(i=i+String.fromCharCode(s)),u!==64&&(i=i+String.fromCharCode(o));return i}static get rotateMatrix(){return this._rotateMatrix||(this._rotateMatrix=this.getRotateMatrix()),this._rotateMatrix}static get inverse_RotateMatrix(){return this._inverse_rotateMatrix||(this._inverse_rotateMatrix=new c.Matrix4().copy(this.rotateMatrix).invert()),this._inverse_rotateMatrix}static getMatrix4FromStringWith_Z_Rotate(e){let t=new c.Matrix4,i=e.split(",");t.set(i[0],i[1],i[2],i[3],i[4],i[5],i[6],i[7],i[8],i[9],i[10],i[11],i[12],i[13],i[14],i[15]);let a=new c.Matrix4;return a.makeRotationAxis(new c.Vector3(1,0,0),-Math.PI/2),t.premultiply(a),t}static getMatrix4FromString(e){let t=new c.Matrix4,i=e.split(",");return t.set(parseFloat(i[0]),parseFloat(i[1]),parseFloat(i[2]),parseFloat(i[3]),parseFloat(i[4]),parseFloat(i[5]),parseFloat(i[6]),parseFloat(i[7]),parseFloat(i[8]),parseFloat(i[9]),parseFloat(i[10]),parseFloat(i[11]),parseFloat(i[12]),parseFloat(i[13]),parseFloat(i[14]),parseFloat(i[15])),t}static getVector3FromString(e){let t=new c.Vector3,i=e.split(",");return t.set(parseFloat(i[0]),parseFloat(i[1]),parseFloat(i[2])),t}static getRotateMatrixWithBPT(e,t){return this.internalMatrix.identity(),this.internalMatrix.makeRotationAxis(new c.Vector3(1,0,0),-Math.PI/2),e.premultiply(t),e.premultiply(this.internalMatrix),e}static getRotateMatrix(e){return e||(e=new c.Matrix4),this.internalMatrix.identity(),this.internalMatrix.makeRotationAxis(this.vector,-Math.PI/2),e.premultiply(this.internalMatrix),e}static getOcclusionCullingMeshFromGroup(e){const t=e.nodeBox.min,i=e.nodeBox.max,a=new c.BoxGeometry(i.x-t.x,i.y-t.y,i.z-t.z);a.translate(t.x,t.y,t.z);const s=a.attributes.position;let o=new c.Color;o.setHex(e.nodeIndex+100);let n=[];for(let u=0;u<s.count;u++)n.push(o.r,o.g,o.b);a.setAttribute("color",new c.Float32BufferAttribute(n,3));let l=new c.MeshBasicMaterial({vertexColors:!0});return new c.Mesh(a,l)}static getPartGeometry(e,t,i,a,s){let o=e.index.array,n=Array.from(e.attributes.position.array),l=[];for(let p=t;p<i;p++)l.push(o[p]-a);let h=[],u=a*3,d=(a+s)*3;h=n.slice(u,d);let f=new c.BufferGeometry,m=new Float32Array(h);return f.setIndex(l),f.setAttribute("position",new c.Float32BufferAttribute(m,3)),f.computeVertexNormals(),f}static randomColor(e){var t=e||"#",i=function(){var s=Math.floor(Math.random()*16777215).toString(16);return s.length==6?s:i()},a=i();switch(e){case"#":t="#"+a;break;case"X":t="0x"+a;break;case"HSL":t="hsl("+Math.round(Math.random()*360)+","+Math.round(Math.random()*100)+"%,"+Math.round(Math.random()*100)+"%)";break;case"RGB":t="rgb("+Math.round(Math.random()*255)+","+Math.round(Math.random()*255)+","+Math.round(Math.random()*10)+")";break;default:console.error("%c【Multiverse】:Color type required","color:YellowGreen");break}return t}};Ot.internalMatrix=new c.Matrix4,Ot.vector=new c.Vector3(1,0,0);let N=Ot;class Bs{constructor(){this.skybox="noon",this.lazyUpdateCount=20}toJSON(){return JSON.stringify({defaultViewpoint:this.defaultViewpoint,skybox:this.skybox,lazyUpdateCount:this.lazyUpdateCount})}fromJSON(e){let t=JSON.parse(e);this.defaultViewpoint=t.defaultViewpoint,this.skybox=t.skybox,this.lazyUpdateCount=t.lazyUpdateCount?t.lazyUpdate:20}}class Z extends c.Box3{constructor(e,t){super(e,t)}get area(){return this.getBoxArea()}get realMin(){return this.min.clone().applyMatrix4(N.inverse_RotateMatrix)}get realMax(){return this.max.clone().applyMatrix4(N.inverse_RotateMatrix)}get realCenter(){let e=new c.Vector3;return e=this.getCenter(e),e.applyMatrix4(N.inverse_RotateMatrix)}get realBox(){return this.clone().applyMatrix4(N.inverse_RotateMatrix)}get id(){return this._id||(this._id=N.GUID()),this._id}set id(e){this._id=e}setupPoints(e,t){this.points||(this.points=[new c.Vector3,new c.Vector3,new c.Vector3,new c.Vector3,new c.Vector3,new c.Vector3,new c.Vector3,new c.Vector3]),t!=null&&e!=null&&(this.points[0].set(e.x,e.y,e.z),this.points[1].set(e.x,e.y,t.z),this.points[2].set(e.x,t.y,e.z),this.points[3].set(e.x,t.y,t.z),this.points[4].set(t.x,e.y,e.z),this.points[5].set(t.x,e.y,t.z),this.points[6].set(t.x,t.y,e.z),this.points[7].set(t.x,t.y,t.z))}getBoxArea(){let e=this.max.x-this.min.x,t=this.max.y-this.min.y,i=this.max.z-this.min.z;return e<0||t<0||i<0?0:2*(e*t+t*i+i*e)}setFromRealPoints(e,t){let i,a;return i=new c.Vector3().fromArray([e[0],t[1],e[2]]),a=new c.Vector3().fromArray([t[0],e[1],t[2]]),i.applyMatrix4(N.rotateMatrix),a.applyMatrix4(N.rotateMatrix),this.setFromPoints([i,a]),this}setFromObjects(e){const t=e.length;new c.Vector3,this.makeEmpty();for(let i=0;i<t;i++)this.expandByObject(e[i]);return this}expandByBox(e){this.expandByPoint(e.min),this.expandByPoint(e.max)}get valid(){return!!(isFinite(this.min.x)&&isFinite(this.min.y)&&isFinite(this.min.z)&&isFinite(this.max.x)&&isFinite(this.max.y)&&isFinite(this.max.z))}getTopPlane(){let e=this.min.clone(),t=this.max.clone(),i=new c.Vector3(e.x,t.y,e.z).applyMatrix4(N.inverse_RotateMatrix),a=new c.Vector3(t.x,t.y,e.z).applyMatrix4(N.inverse_RotateMatrix),s=new c.Vector3(t.x,t.y,t.z).applyMatrix4(N.inverse_RotateMatrix),o=new c.Vector3(e.x,t.y,t.z).applyMatrix4(N.inverse_RotateMatrix);return[i.toArray(),a.toArray(),s.toArray(),o.toArray()]}}const Ds=parseInt(c.REVISION.replace("dev",""));function te(r,e,t){return Ds>109?r.setAttribute(e,t):r.addAttribute(e,t),r}class Us extends c.InstancedBufferGeometry{constructor(){super(),this.isLineSegmentsGeometry=!0,this.type="LineSegmentsGeometry";var e=[-1,2,0,1,2,0,-1,1,0,1,1,0,-1,0,0,1,0,0,-1,-1,0,1,-1,0],t=[-1,2,1,2,-1,1,1,1,-1,-1,1,-1,-1,-2,1,-2],i=[0,2,1,2,3,1,2,4,3,4,5,3,4,6,5,6,7,5];this.setIndex(i),te(this,"position",new c.Float32BufferAttribute(e,3)),te(this,"uv",new c.Float32BufferAttribute(t,2))}applyMatrix(e){var t=this.attributes.instanceStart,i=this.attributes.instanceEnd;return t!==void 0&&(t.applyMatrix4(e),i.applyMatrix4(e),t.data.needsUpdate=!0),this.boundingBox!==null&&this.computeBoundingBox(),this.boundingSphere!==null&&this.computeBoundingSphere(),this}setInstanceU(e){var t;e instanceof Float32Array?t=e:Array.isArray(e)&&(t=new Float32Array(e)),this.instanceUv=t;var i=new c.InstancedInterleavedBuffer(t,2,1);te(this,"instanceUStart",new c.InterleavedBufferAttribute(i,1,0)),te(this,"instanceUEnd",new c.InterleavedBufferAttribute(i,1,1))}setPositions(e){var t;e instanceof Float32Array?t=e:Array.isArray(e)&&(t=new Float32Array(e)),this.positions=t;var i=new c.InstancedInterleavedBuffer(t,6,1);return te(this,"instanceStart",new c.InterleavedBufferAttribute(i,3,0)),te(this,"instanceEnd",new c.InterleavedBufferAttribute(i,3,3)),this.computeBoundingBox(),this.computeBoundingSphere(),this}setColors(e){var t;e instanceof Float32Array?t=e:Array.isArray(e)&&(t=new Float32Array(e)),this.colors=t;var i=new c.InstancedInterleavedBuffer(t,6,1);return te(this,"instanceColorStart",new c.InterleavedBufferAttribute(i,3,0)),te(this,"instanceColorEnd",new c.InterleavedBufferAttribute(i,3,3)),this}fromWireframeGeometry(e){return this.setPositions(e.attributes.position.array),this}fromEdgesGeometry(e){return this.setPositions(e.attributes.position.array),this}fromMesh(e){return this.fromWireframeGeometry(new c.WireframeGeometry(e.geometry)),this}fromLineSegements(e){var t=e.geometry;return t.isGeometry?this.setPositions(t.vertices):t.isBufferGeometry&&this.setPositions(t.position.array),this}computeBoundingBox(){var e=new c.Box3;this.boundingBox===null&&(this.boundingBox=new c.Box3);var t=this.attributes.instanceStart,i=this.attributes.instanceEnd;t!==void 0&&i!==void 0&&(this.boundingBox.setFromBufferAttribute(t),e.setFromBufferAttribute(i),this.boundingBox.union(e))}computeBoundingSphere(){var e=new c.Vector3;this.boundingSphere===null&&(this.boundingSphere=new c.Sphere),this.boundingBox===null&&this.computeBoundingBox();var t=this.attributes.instanceStart,i=this.attributes.instanceEnd;if(t!==void 0&&i!==void 0){var a=this.boundingSphere.center;this.boundingBox.getCenter(a);for(var s=0,o=0,n=t.count;o<n;o++)e.fromBufferAttribute(t,o),s=Math.max(s,a.distanceToSquared(e)),e.fromBufferAttribute(i,o),s=Math.max(s,a.distanceToSquared(e));this.boundingSphere.radius=Math.sqrt(s),isNaN(this.boundingSphere.radius)&&console.error("%c【Multiverse】:THREE.LineSegmentsGeometry.computeBoundingSphere(): Computed radius is NaN. The instanced position data is likely to have NaN values.",this)}}toJSON(){return super.toJSON()}clone(){let e=super.clone();this.type="LineSegmentsGeometry";var t=[-1,2,0,1,2,0,-1,1,0,1,1,0,-1,0,0,1,0,0,-1,-1,0,1,-1,0],i=[-1,2,1,2,-1,1,1,1,-1,-1,1,-1,-1,-2,1,-2],a=[0,2,1,2,3,1,2,4,3,4,5,3,4,6,5,6,7,5];return this.setIndex(a),te(this,"position",new c.Float32BufferAttribute(t,3)),te(this,"uv",new c.Float32BufferAttribute(i,2)),this.positions&&e.setPositions(this.positions),this.colors&&e.setColors(this.colors),e}copy(e){return this}}class Je extends Us{constructor(){super(),this.isLineGeometry=!0,this.type="LineGeometry"}fromLine(e){var t=e.geometry;return t.isGeometry?this.setPositions(t.vertices):t.isBufferGeometry&&this.setPositions(t.position.array),this}}let Qt=new c.Vector3;class Vs{constructor(e){this.ft=e,this.toPrj=L.proj4("EPSG:4326")}correct(e,t){let i;t?(this.baseVector=this.ft.parent.mv.tools.coordinate.mercator2vector(this.ft.origin[0],this.ft.origin[1],0,!1),i=this.ft.parent.mv.tools.coordinate.defProj4System(t.coordinateSystem,t.centralMeridian)):(this.baseVector=this.ft.parent.mv.tools.coordinate.mercator2vector(this.ft.origin[0],this.ft.origin[1],0,!1),i=this.ft.parent.mv.tools.coordinate.defProj4System(this.ft.correction.coordinateSystem,this.ft.correction.centralMeridian)),this.fromPrj=L.proj4(i),this.baseXY=L.proj4(this.toPrj,this.fromPrj,this.ft.origin);let a;e instanceof Je?a=Array.from(e.attributes.instanceStart.data.array):e instanceof c.BufferGeometry?a=Array.from(e.attributes.position.array):a=Array.from(e.position);let s=a.length/3;for(let o=0;o<s;o++){let n=[a[o*3],a[o*3+1]],l=this.correctPosition(n);a[o*3]=l[0],a[o*3+1]=-l[2]}return e instanceof Je?e.setPositions(a):e instanceof c.BufferGeometry?e.setAttribute("position",new c.Float32BufferAttribute(a,3)):e.position=a,e}correctPosition(e){let t=[this.baseXY[0]+e[0],this.baseXY[1]+e[1]],i=L.proj4(this.fromPrj,this.toPrj,t),a=this.ft.parent.mv.tools.coordinate.mercator2vector(i[0],i[1],0,!1).sub(this.baseVector);return[a.x,a.y,a.z]}restore(e,t){let i;t?(this.baseVector=this.ft.parent.mv.tools.coordinate.mercator2vector(this.ft.origin[0],this.ft.origin[1],0,!1),i=this.ft.parent.mv.tools.coordinate.defProj4System(t.coordinateSystem,t.centralMeridian)):(this.baseVector=this.ft.parent.mv.tools.coordinate.mercator2vector(this.ft.origin[0],this.ft.origin[1],0,!1),i=this.ft.parent.mv.tools.coordinate.defProj4System(this.ft.correction.coordinateSystem,this.ft.correction.centralMeridian)),this.fromPrj=L.proj4(i),this.baseXY=L.proj4(this.toPrj,this.fromPrj,this.ft.origin);let a;e instanceof Je?a=Array.from(e.attributes.instanceStart.data.array):e instanceof c.BufferGeometry?a=Array.from(e.attributes.position.array):a=Array.from(e.position);let s=a.length/3;for(let o=0;o<s;o++){let n=[a[o*3],a[o*3+1]],l=this.restorePosition(n);a[o*3]=l[0],a[o*3+1]=l[1]}return e instanceof Je?e.setPositions(a):e instanceof c.BufferGeometry?e.setAttribute("position",new c.Float32BufferAttribute(a,3)):e.position=a,e}restorePosition(e){Qt.set(e[0],0,-e[1]),Qt.add(this.baseVector);let t=this.ft.parent.mv.tools.coordinate.vector2mercator(Qt,!1),i=L.proj4(this.toPrj,this.fromPrj,[t[0],t[1]]),a=i[0]-this.baseXY[0],s=i[1]-this.baseXY[1];return[a,s]}}const Os=new c.Plane;class Ns{constructor(e){this.ft=e,this.culling=!1,this.isMClipState=!1,this.clipUniforms={u_clipPoints:[],u_clipPointIndex:[],u_distanceRange:[],u_clipPlane:[],u_clipPrjType:[]},this.modelPolygonPlanes=[],this.model2dPoints=[],this.polygons=[],this.clipInfoMap=new Map}addPolygon(e){this.polygons.push(e),e.id==null&&(e.id=N.GUID());let t=[];e.points.forEach(s=>{let o=this.ft.parent.mv.tools.coordinate.scenePosition(s);t.push(o)});let i;Array.isArray(e.depth)?i=e.depth:e.depth>0?i=[-.001,e.depth]:i=[e.depth,.001];let a={id:e.id,polygon:t,distanceRange:i,lastCameraMatrix:new c.Matrix4,lastOffset:new c.Vector3};return this.clipInfoMap.set(a.id,a),this.isMClipState=!0,this.update(),e.id}removePolygon(e){let t=this.polygons.findIndex(i=>i.id==e);t!=-1&&(this.polygons.splice(t,1),this.clipInfoMap.delete(e),this.update())}update(e=new c.Vector3){if(!(this.ft.loaded===!1||this.isMClipState==!1)){switch(this.ft.type){case fe.heatmap:this.updateHeatmap(e);break;case fe.model:this.updateModel(e);break}this.clipInfoMap.size==0?this.isMClipState=!1:this.isMClipState=!0}}updateHeatmap(e){let{pointsCount:t,polygonCount:i}=this.computeClipInfoPerFrame(e),s=this.ft.material;this.setParamsToModelOneMaterial(s,t,i),s.needsUpdate=!0}updateModel(e=new c.Vector3){let{pointsCount:t,polygonCount:i}=this.computeClipInfoPerFrame(e),a=this.ft.parent.mv.materialService.highlightEdgeMaterialMap.get(this.ft.id);a&&this.setParamsToModelOneMaterial(a,t,i),this.ft.materialsWithTexture.forEach(s=>{this.setParamsToModelOneMaterial(s,t,i)}),this.ft.materialsNoTexture.forEach(s=>{this.setParamsToModelOneMaterial(s,t,i)})}computeClipInfoPerFrame(e){this.clipUniforms.u_clipPoints.length=0,this.clipUniforms.u_clipPointIndex.length=0,this.clipUniforms.u_distanceRange.length=0,this.clipUniforms.u_clipPlane.length=0,this.clipUniforms.u_clipPrjType.length=0;let t=0,i=0,a=0;for(const s of this.clipInfoMap){let o=s[1].polygon;o.forEach(h=>{h.applyMatrix4(s[1].lastCameraMatrix).add(s[1].lastOffset).sub(e).applyMatrix4(this.ft.parent.mv.THREE_Camera.matrixWorldInverse)}),s[1].lastCameraMatrix.copy(this.ft.parent.mv.THREE_Camera.matrixWorld),s[1].lastOffset.copy(e);let n=Os.setFromCoplanarPoints(o[0],o[1],o[2]),l=0;n.normal.x==0&&n.normal.z==0?l=0:n.normal.x==0||Math.abs(Math.abs(n.normal.z)-1)<1e-6?l=2:n.normal.y==0&&(l=1),i+=o.length,t==0&&this.clipUniforms.u_clipPointIndex.push(0),this.clipUniforms.u_clipPointIndex.push(i),this.clipUniforms.u_distanceRange.push(...s[1].distanceRange),this.modelPolygonPlanes[t]==null?this.modelPolygonPlanes.push(new c.Vector4(n.normal.x,n.normal.y,n.normal.z,n.constant)):this.modelPolygonPlanes[t].set(n.normal.x,n.normal.y,n.normal.z,n.constant),this.clipUniforms.u_clipPlane.push(this.modelPolygonPlanes[t]),this.clipUniforms.u_clipPrjType.push(l);for(let h=0;h<o.length;h++)this.model2dPoints[a]==null&&(this.model2dPoints[a]=new c.Vector2),l==0?this.clipUniforms.u_clipPoints.push(this.model2dPoints[a].set(o[h].x,o[h].z)):l==2?this.clipUniforms.u_clipPoints.push(this.model2dPoints[a].set(o[h].x,o[h].y)):l==1&&this.clipUniforms.u_clipPoints.push(this.model2dPoints[a].set(o[h].y,o[h].z)),a++;t++}return{pointsCount:i,polygonCount:t}}setParamsToModelOneMaterial(e,t,i){let a=e.uniforms;a&&(t!=0?(a.u_clipPoints={value:this.clipUniforms.u_clipPoints},a.u_clipPointIndex={value:this.clipUniforms.u_clipPointIndex},a.u_clipDistanceRange={value:this.clipUniforms.u_distanceRange},a.u_clipPlane={value:this.clipUniforms.u_clipPlane},a.u_clipPrjType={value:this.clipUniforms.u_clipPrjType},this.culling?delete e.defines.RESERVE_TYPE:e.defines.RESERVE_TYPE="",(e.defines.MCLIP==null||e.defines.MCLIP!=this.clipUniforms.u_clipPoints.length)&&(e.needsUpdate=!0),e.defines.MCLIP=this.clipUniforms.u_clipPoints.length,e.defines.CLIP_START_END=i+1,e.defines.CLIP_POLYGON_COUNT=i):(e.defines.MCLIP!=null&&(delete e.defines.MCLIP,e.needsUpdate=!0),a.u_basePoint={value:null},a.u_clipPoints={value:null},a.u_clipPointIndex={value:null},a.u_clipDistanceRange={value:null},a.u_clipPlane={value:null},a.u_clipPrjType={value:null}))}}var fe=(r=>(r.underlay="underlay",r.skybox="skybox",r.dem="dem",r.wmts="wmts",r.wms="wms",r.tms="tms",r.geoJSON="geoJSON",r.shp="shp",r.kml="kml",r._3dTiles="_3dTiles",r._3dBuilding="_3dBuilding",r.model="model",r.glTF="gltf",r.panorama="panorama",r.video="video",r.projector="projector",r.audio="audio",r.shield="shield",r.flame="flame",r.heatmap="heatmap",r._2dHeatmap="_2dheatmap",r.ripplewall="ripplewall",r.ring="ring",r.annotation="annotation",r.billboard="billboard",r.bloom="bloom",r.fbx="fbx",r.extrudePolygon="fill-extrusion",r.polygon="polygon",r.polyline="polyline",r.batchextrude="batch-extrude",r.radar="radar",r.smoke="smoke",r.vectorExtrude="vectorextrude",r.vectorField="vectorField",r.waterfall="waterfall",r.camera="camera",r.scalar="scalar",r.referenceplane="referenceplane",r.snow="snow",r.tailLine="tailline",r))(fe||{}),ft=(r=>(r.THREE="0",r.MAPBOX="1",r))(ft||{});class $t{constructor(){this.id="",this.name="",this.type="model",this.lib="0",this.version="",this._url="",this.phase="",this.immediate=!0,this.lazyUpdate=!1,this.counter=0,this._needUpdate=!1,this._origin=[0,0],this._altitude=0,this._rotation=0,this._offset=[0,0,0],this.exceedPrecision=!1,this._rotationMatrix=new c.Matrix4,this.meshIDs=[],this._visible=!0,this._loaded=!1,this.objects=new Map,this._data={},this.forceRepaint=!1,this.dynamicLoad=!0,this.priority=0,this._animating=!1,this.loading=!1,this.pending=!1,this._selected=!1,this.followType="feature",this.always=!1,this.v3=new c.Vector3,this._roller=!1,this.materials=[],this.config=new Bs,this.config.feature=this}get url(){return this._url}set url(e){this._url=e}get Prj4(){return this._prj4||(this._prj4=new Vs(this)),this._prj4}get correction(){return this._correction}set correction(e){this._correction=e,this.updateBPT(),this.loaded==!0&&(this.needUpdate=!0)}set needUpdate(e){this._needUpdate!=e&&(this._needUpdate=e,e==!0&&this.immediate==!0&&(this.parent.forceRepaint=!0))}get needUpdate(){return this._needUpdate}get origin(){return this._origin}set origin(e){this._origin=e,this.updateBPT(),this.loaded==!0&&(this.needUpdate=!0)}get altitude(){return this._altitude}set altitude(e){this._altitude=e,this.updateBPT(),this.loaded==!0&&(this.needUpdate=!0)}get rotation(){return this._rotation}set rotation(e){this._rotation=e,this.updateBPT(),this.loaded==!0&&(this.needUpdate=!0)}get offset(){return this._offset}set offset(e){this._offset=e,this.updateBPT(),this.loaded==!0&&(this.needUpdate=!0)}get scale(){return this._scale}set scale(e){e[0]<=0||e[1]<=0||e[2]<=0||(this._scale=e,this.updateBPT(),this.loaded==!0&&(this.needUpdate=!0))}updateBPT(){this._bpt=this._bpt?this._bpt.identity():new c.Matrix4;let e=W.mapboxgl.MercatorCoordinate.fromLngLat(this._origin,this._altitude);this._position=new c.Vector3(e.x,e.y,e.z),this._position.applyMatrix4(new c.Matrix4().fromArray([4003022888407185e-8,0,0,0,0,0,4003022888407185e-8,0,0,4003022888407185e-8,0,0,-20015114442035925e-9,0,-20015114442035925e-9,1]));let t=new c.Vector3;if(this._offset&&this._offset.length==3&&t.set(this._offset[0],this._offset[1],this._offset[2]),t.applyMatrix4(N.rotateMatrix),this._position.add(t),this._bpt.makeTranslation(this._position.x,this._position.y,this._position.z),this.rotation){let i=new c.Matrix4;Array.isArray(this.rotation)==!1?t.set(.001,0,this.rotation):t.set(this.rotation[0]+.001,this.rotation[1],this.rotation[2]),t.applyMatrix4(N.rotateMatrix);let a=new c.Matrix4,s=new c.Matrix4,o=new c.Matrix4;a.makeRotationX(Math.PI*(t.x/180)),s.makeRotationY(Math.PI*(t.y/180)),o.makeRotationZ(Math.PI*(t.z/180)),i.multiplyMatrices(a,s),i.multiply(o),this._bpt.multiply(i)}else{let i=new c.Matrix4;t.set(.001,0,0),i.makeRotationX(Math.PI*(t.x/180)),this._bpt.multiply(i)}if(this._correction)this._scale&&console.warn("%c【Multiverse】:Large scale feature cannot be applied scale! Because we will need to correct each vertex projection on globe. "+this.id+"|"+this.name,"color:Green");else{this._scale&&(t.set(this._scale[0],this._scale[2],this._scale[1]),this._bpt.multiply(new c.Matrix4().makeScale(t.x,t.y,t.z)));let i=this.parent.mv.tools.coordinate.vector2mercator(this._position,!1);e=W.mapboxgl.MercatorCoordinate.fromLngLat([i[0],i[1]],i[2]);let a=e.meterInMercatorCoordinateUnits(),s=24981121214570498e-24;this.mercatorScale=a/s,this._bpt.multiply(new c.Matrix4().makeScale(this.mercatorScale,this.mercatorScale,this.mercatorScale))}Math.abs(this._bpt.elements[12])>5e4||Math.abs(this._bpt.elements[14])>5e4?this.exceedPrecision=!0:this.exceedPrecision=!1,this._rotationMatrix.extractRotation(this._bpt).invert()}get rotationMatrix(){return this._rotationMatrix}get bpt(){return this._bpt||this.updateBPT(),this._bpt}set bpt(e){this._bpt=e,this.loaded==!0&&(this.needUpdate=!0)}get meshes(){let e=[];return this.meshIDs.forEach(t=>{let i=this.parent.allMeshes.get(t);e.push(i)}),e}set visible(e){this._visible!=e&&(this._visible=e,this.parent.mv.events.featureVisibleChanged.emit("default",{featureID:this.id,visible:e}),this.immediate&&(this.parent.forceRepaint=!0))}get visible(){return this._visible}get loaded(){return this._loaded}get AABB(){return this._box||(this._box=new Z),this._box}set AABB(e){this._box||(this._box=new Z),this._box.copy(e)}get AABBWorld(){return this._bbox||(this._bbox=new Z),this._animating||this.follow?this._bbox.copy(this.AABB).applyMatrix4(this.container.matrix):this._bbox.copy(this.AABB).applyMatrix4(this.bpt)}get position(){if(this._animating||this.follow){let e=this.container.position;return[e.x,-e.z,e.y]}else return[this._position.x,-this._position.z,this._position.y]}get dataKey(){return this._dataKey}set dataKey(e){this._dataKey=e,this.parent&&this.parent.data.has(e)&&(this.data=this.parent.data.get(e))}get data(){return this._data}set data(e){e&&this._data!=e&&(this._data=e,this._needUpdate=!0)}get container(){return this._container||(this._container=new qi),this._container}set animating(e){this._animating=e,e==!1&&(this.container.position.set(0,0,0),this.container.quaternion.set(0,0,0,1),this.container.scale.set(1,1,1),this.container.applyMatrix4(this.bpt),this._position.copy(this.container.position))}get selected(){return this._selected}set selected(e){this._selected!=e&&(this._selected=e,this.type!="model"&&this.drawAABB(),e==!0?this.parent.features.forEach(t=>{t.loaded&&(t.id!=this.id?(t.selected=!1,t.clearAABB()):this.parent.selectedFeature=t)}):this.clearAABB())}get follow(){return this._follow}set follow(e){e!=this._follow&&(this._follow=e,this.needUpdate=!0)}get roller(){return this._roller}set roller(e){this._roller!=e&&(this._roller=e,this.updateRange())}get boundary(){return this._boundary||(this._boundary=new Ns(this)),this._boundary}set boundary(e){this._boundary=e,this.needUpdate=!0}load(e){if(this._loaded==!0)throw new Error("Feature:"+this.id+" has been loaded, Can not load it again!");if(!this.parent)throw new Error("Feature:"+this.id+" has not been added to a valid scene!");this._needUpdate=!1,this._loaded=!0,e!="restore"&&(this.parent.inited=!0),this.parent.refresh();let t=this.parent.object2animaton.get(this.id);t&&t.forEach(i=>{i.activeOne(this.id)}),this.dataKey||(this.dataKey=this.parent.postData({})),this.loading=!1,this.pending=!1,this.immediate&&this.parent.render(!1)}addObject(e){e.parent=this,this.objects.set(e.id,e),this.parent.allObjects.set(e.id,e)}removeObject(e){let t;typeof e=="string"?t=this.parent.findObject(e):t=e,t&&(this.objects.delete(t.id),this.parent.allObjects.delete(t.id))}fit(e=.5,t=!0){switch(this.type){case"model":if(this.parent.fitter.fit2FeatureBox(this.AABBWorld,e,t),this._2DMode)this.parent.mv.controller.rotateTo(0,-Math.PI,!0);else if(this.parent.mixedMode)if(this.parent.mv.mapbox.terrain)this.parent.mv.controller.rotateTo(0,60/180*Math.PI,!1);else{let a=this.parent.mv.mapbox.map.getMaxPitch();this.parent.mv.controller.rotateTo(0,a/180*Math.PI,!1)}else this.parent.mv.controller.rotateTo(0,Math.PI*.5,!0);break;case"ripplewall":case"polygon":case"polyline":case"fbx":case"gltf":case"smoke":case"flame":case"heatmap":case"waterfall":case"_3dTiles":case"projector":case"snow":case"scalar":case"tailline":this.parent.fitter.fit2FeatureBox(this.AABBWorld,e,t);break;case"batch-extrude":case"geoJSON":case"shp":case"wmts":case"wms":case"kml":case"tms":case"vectorextrude":case"dem":this.sphere&&this.parent.fitter.fit2FeatureCenter(this.sphere.center,this.sphere.radius);break;case"billboard":let i=new c.Sphere;i=this.AABBWorld.getBoundingSphere(i),this.parent.fitter.fit2FeatureCenter(new c.Vector3().applyMatrix4(this.bpt),i.radius/e);break;case"referenceplane":this.parent.fitter.fitToPlane(this.plane);break;default:this.parent.fitter.fit2FeatureCenter(new c.Vector3().applyMatrix4(this.bpt),50);break}}snap(){return JSON.stringify({origin:this.origin,rotation:this.rotation,altitude:this.altitude,offset:this.offset,bpt:this.bpt,scale:this.scale,visible:this.visible,loaded:this.loaded})}recover(e){let t=JSON.parse(e);this._origin=t.origin,this._altitude=t.altitude,this._rotation=t.rotation,this._scale=t.scale?t.scale:[1,1,1],this._offset=t.offset,this.updateBPT(),t.bpt&&(this._bpt=new c.Matrix4,this._bpt.elements=t.bpt.elements),this.visible=t.visible,t.loaded==!1&&(this.visible=!1),this._needUpdate=!0,this.update()}toJSON(){return JSON.stringify({id:this.id,name:this.name,type:this.type,lib:this.lib,version:this.version,url:this.url,phase:this.phase,origin:this.origin,rotation:this.rotation,altitude:this.altitude,offset:this.offset,scale:this.scale,bpt:this.bpt,visible:this.visible,AABB:this._box,dataKey:this.dataKey,followType:this.followType,follow:this.follow,config:this.config.toJSON(),dynamicLoad:this.dynamicLoad,correction:this.correction,priority:this.priority,always:this.always,immediate:this.immediate,userData:this.userData,materials:this.materials})}fromJSON(e){let t=JSON.parse(e);if(this.parent.features.delete(this.id),this.id=t.id,this.parent.features.set(this.id,this),this.name=t.name,this.type=t.type,this.lib=t.lib,this.version=t.version,this.url=t.url,this.phase=t.phase,this.origin=t.origin,this.altitude=t.altitude,this.rotation=t.rotation,this.offset=t.offset,t.offset?this.offset=t.offset:this.offset=[0,0,0],this.scale=t.scale?t.scale:[1,1,1],this.followType=t.followType?t.followType:"feature",this.follow=t.follow,t.bpt&&(this.bpt=new c.Matrix4,this.bpt.elements=t.bpt.elements),this.visible=t.visible,t.AABB&&t.AABB.min.x!=1/0&&t.AABB.min.x!=null){let i=new c.Vector3(t.AABB.min.x,t.AABB.min.y,t.AABB.min.z),a=new c.Vector3(t.AABB.max.x,t.AABB.max.y,t.AABB.max.z);this.AABB=new Z(i,a)}this.dataKey=t.dataKey,this.dynamicLoad=t.dynamicLoad?t.dynamicLoad:!0,this.correction=t.correction,this.priority=t.priority?t.priority:0,this.always=t.always?t.always:!1,this.immediate=t.immediate?t.immediate:!0,this.userData=t.userData?t.userData:void 0,this.materials=t.materials?t.materials:[],(!this.parent.version||parseFloat(this.parent.version)<1.4)&&this.materials.forEach(i=>{i.color&&(i.color=parseInt(i.color)),i.sheenColor&&(i.sheenColor=parseInt(i.sheenColor)),i.emissiveColor&&(i.emissiveColor=parseInt(i.emissiveColor)),i.specularColor&&(i.specularColor=parseInt(i.specularColor))}),this.config.fromJSON(t.config)}update(){if(this.parent.inited!=!1){if(this.loaded==!1)if(this.dynamicLoad){if(this.AABBWorld&&this.AABBWorld.valid&&!this.loading){let e=this.parent.frustum.projectedBoxArea(this.AABBWorld);(this.always||e>this.parent.config.cullingRatio/100)&&this.load(this.parent.mode)}}else this.load(this.parent.mode);if(this._needUpdate&&(this._needUpdate=!1,this.parent.refresh()),this._animating){this.v3.copy(this.container.animationPosition);let e=this.v3.applyMatrix4(N.rotateMatrix);e.x!=0||e.y!=0||e.z!=0?this.container.position.addVectors(e,this.container.animationOffset.applyMatrix4(N.rotateMatrix)):this.container.position.addVectors(this._position,this.container.animationOffset.applyMatrix4(N.rotateMatrix));let i=new c.Vector3(this.container.animationRotation.x,this.container.animationRotation.y,this.container.animationRotation.z).applyMatrix4(N.rotateMatrix);this.container.rotation.set(Math.PI*i.x/180,Math.PI*i.y/180,Math.PI*i.z/180),this.container.scale.set(this.mercatorScale,this.mercatorScale,this.mercatorScale),this._scale&&this.container.scale.multiply(new c.Vector3().fromArray(this._scale));let a=this.v3.set(this.container.animationScale.x,this.container.animationScale.z,this.container.animationScale.y);this.container.scale.multiply(a),this.container.updateMatrix()}else if(this.follow)if(this.followType=="feature"){let e=this.parent.findFeature(this.follow);e&&(this.container.position.set(e.position[0]+this.offset[0],e.position[2]+this.offset[1],-e.position[1]+this.offset[2]),this.container.updateMatrix())}else{let e=this.parent.findObject(this.follow);e&&e.position&&(this.container.position.set(e.position[0]+this.offset[0],e.position[2]+this.offset[1],-e.position[1]+this.offset[2]),this.container.updateMatrix())}}}updateRange(){}updateBoundries(){}drawAABB(e="red",t,i){let a;if(t&&i){let s=new Z().setFromRealPoints(t,i);a=new c.Box3Helper(s,new c.Color(e))}else a=new c.Box3Helper(this.AABBWorld,new c.Color(e));this.helper=new c.Group,this.helper.name="helper",this.helper.projectArea=1,this.helper.add(a)}clearAABB(){this.helper&&(this.helper.children=[])}dispose(e=!0){this.objects.forEach((t,i)=>{this.parent.allObjects.delete(i),t.dispose()}),this.meshIDs=[],this.parent.animations.forEach(t=>{t.rootType=="feature"&&t.actions.has(this.id)&&t.disposeOne(this.id)}),this.parent.animations.forEach(t=>{if(t.rootType=="object"){let i=[];Array.isArray(t.rootObjectID)?i.push(...t.rootObjectID):i.push(t.rootObjectID),i.forEach(a=>{this.objects.has(a)&&t.disposeOne(a)})}}),this.parent.removeFeature(this.id),this._loaded=!1,e&&this.parent.render()}}class qi extends c.Group{constructor(){super(...arguments),this.animationOffset=new c.Vector3,this.animationPosition=new c.Vector3(0,0,0),this.animationScale=new c.Vector3(1,1,1),this.animationColor="",this.animationRotation=new c.Euler,this.postRight=!1,this._shadow=!1}set shadow(e){e!=this._shadow&&(this.traverse(t=>{t.isMesh&&(t.castShadow=e,t.receiveShadow=e)}),this._shadow=e)}}var Xi=(r=>(r.mesh="mesh",r.line="line",r.shape="shape",r.voxel="voxel",r))(Xi||{}),Se=(r=>(r.normal="normal",r.changed="changed",r.hidden="hidden",r))(Se||{});class Fs{constructor(){this.status="normal",this.start=-1,this.end=-1,this.positionStart=-1,this.positionCount=-1,this.type="mesh",this.projectArea=0,this.culled=!1,this._needsUpdateTransform=!1,this.isTransformParamInit=!1}get AABBWorld(){return this._bbox||(this._bbox=new Z),this.AABB&&this._bbox.copy(this.AABB),this._bbox.applyMatrix4(this.bpt)}get needsUpdateTransform(){return this._needsUpdateTransform}set needsUpdateTransform(e){this._needsUpdateTransform=e,e&&this.isTransformParamInit==!1&&this.initTransformParams()}contain(e){let t;switch(this.type){case"mesh":e>=this.positionStart&&e<this.positionStart+this.positionCount&&(t=this.elementID);break;case"line":(e>=this.positionStart&&e<this.positionCount||e==this.positionStart)&&(t=this.elementID);break;case"voxel":if(e>=this.positionStart&&e<this.positionStart+this.positionCount&&this.children){for(let i=0;i<this.children.length;i++)if(e>=this.children[i].positionStart&&e<this.children[i].positionStart+this.children[i].positionCount){t=this.children[i].elementID;break}}break}return t}dispose(){this.AABBWorld==null,this.blockID=null,this.type=null,this.id=null,this.transform=null}initTransformParams(){this.isTransformParamInit=!0,this.currentCenter=new c.Vector3,this.currentOffset=new c.Vector3,this.currentScale=new c.Vector3(1,1,1),this.currentRotation=new c.Matrix4,this.lastCenter=new c.Vector3,this.lastOffset=new c.Vector3,this.lastScale=new c.Vector3(1,1,1),this.lastRotationInvert=new c.Matrix4,this.currentExplosion=new c.Vector3,this.lastExplosion=new c.Vector3}}var Yi=1;function ei(r){var e={};for(var t in r){e[t]={};for(var i in r[t]){var a=r[t][i];a&&(a.isColor||a.isMatrix3||a.isMatrix4||a.isVector2||a.isVector3||a.isVector4||a.isTexture)?e[t][i]=a.clone():Array.isArray(a)?e[t][i]=a.slice():e[t][i]=a}}return e}function ti(r,e,t){r.opacity.value=e.opacity,e.color&&r.diffuse.value.copy(e.color),e.emissive&&r.emissive.value.copy(e.emissive).multiplyScalar(e.emissiveIntensity),e.map&&(r.map.value=e.map),e.alphaMap&&(r.alphaMap.value=e.alphaMap),e.specularMap&&(r.specularMap.value=e.specularMap);var i=e.envMap||t;i&&(r.envMap.value=i,r.flipEnvMap.value=i.isCubeTexture?-1:1,r.reflectivity.value=e.reflectivity,r.refractionRatio.value=e.refractionRatio),e.lightMap&&(r.lightMap.value=e.lightMap,r.lightMapIntensity.value=e.lightMapIntensity),e.aoMap&&(r.aoMap.value=e.aoMap,r.aoMapIntensity.value=e.aoMapIntensity);var a;e.map?a=e.map:e.specularMap?a=e.specularMap:e.displacementMap?a=e.displacementMap:e.normalMap?a=e.normalMap:e.bumpMap?a=e.bumpMap:e.roughnessMap?a=e.roughnessMap:e.metalnessMap?a=e.metalnessMap:e.alphaMap?a=e.alphaMap:e.emissiveMap&&(a=e.emissiveMap),a!==void 0&&(a.isWebGLRenderTarget&&(a=a.texture),a.matrixAutoUpdate===!0&&a.updateMatrix(),r.uvTransform.value.copy(a.matrix));var s;e.aoMap?s=e.aoMap:e.lightMap&&(s=e.lightMap),s!==void 0&&(s.isWebGLRenderTarget&&(s=s.texture),s.matrixAutoUpdate===!0&&s.updateMatrix(),r.uv2Transform.value.copy(s.matrix))}function zs(r,e){r.diffuse.value.copy(e.color),r.opacity.value=e.opacity}function ks(r,e){r.specular.value.copy(e.specular),r.shininess.value=Math.max(e.shininess,1e-4),e.emissiveMap&&(r.emissiveMap.value=e.emissiveMap),e.bumpMap&&(r.bumpMap.value=e.bumpMap,r.bumpScale.value=e.bumpScale,e.side===Yi&&(r.bumpScale.value*=-1)),e.normalMap&&(r.normalMap.value=e.normalMap,r.normalScale.value.copy(e.normalScale),e.side===Yi&&r.normalScale.value.negate()),e.displacementMap&&(r.displacementMap.value=e.displacementMap,r.displacementScale.value=e.displacementScale,r.displacementBias.value=e.displacementBias)}class Ke extends c.ShaderMaterial{constructor(e){if(super(e),this.uniforms.renderBoundary={value:Q.RENDERBOUNDARY},this.uniforms.rollDirection={value:1},e)for(const t in e)Object.prototype.hasOwnProperty.call(e,t)&&(this.uniforms[t]!=null?this.uniforms[t].value=e[t]:this[t]=e[t])}updateSceneInfo(){this.scene==null||this.scene.mv==null||this.scene.mv.status.section&&(this.clippingPlanes=this.scene.mv.materialService.commonMaterialInfo.clip.planes,this.clipShadows=!0,this.clipping=!0,this.needsUpdate=!0)}}class ze extends Ke{constructor(e){let{uniforms:t,vertexShader:i,fragmentShader:a}=ze.getMaterialInfo();e.uniforms=t,e.vertexShader=i,e.fragmentShader=a,e.side==null&&(e.side=c.DoubleSide),super(e)}static getMaterialInfo(){let e=ei(c.ShaderLib.basic.uniforms);e.diffuse.value=new c.Color(1,1,1),e.u_clipPoints={value:[]},e.u_basePoint={value:new c.Vector2},e.u_clipPointIndex={value:[]},e.u_clipAltitude={value:[]},e.lightIntensity={value:1};let t=new c.MeshBasicMaterial;return ti(e,t,void 0),{uniforms:e,vertexShader:this.getVert(),fragmentShader:this.getFrag()}}static getVert(){return`
        #include <common>
        #include <uv_pars_vertex>
        #include <envmap_pars_vertex>
        #include <color_pars_vertex>
        #include <fog_pars_vertex>
        #include <morphtarget_pars_vertex>
        #include <skinning_pars_vertex>
        #include <logdepthbuf_pars_vertex>
        #include <clipping_planes_pars_vertex>
        #include <mv/boxclip_par_vertex>
        #include <mv/clip_par_vertex>
        #include <mv/clip_model_par_vertex>
        void main() {
        
            #include <uv_vertex>
            #include <color_vertex>
            #include <skinbase_vertex>
        
            #ifdef USE_ENVMAP
        
            #include <beginnormal_vertex>
            #include <morphnormal_vertex>
            #include <skinnormal_vertex>
            #include <defaultnormal_vertex>
        
            #endif
        
            #include <begin_vertex>
            #include <morphtarget_vertex>
            #include <skinning_vertex>
            #include <project_vertex>
            #include <logdepthbuf_vertex>
        
            #include <worldpos_vertex>
            #include <clipping_planes_vertex>
            #include <envmap_vertex>
            #include <fog_vertex>
  
            #include <mv/clip_vertex>
            #include <mv/boxclip_vertex>
            #include <mv/clip_model_vertex>
        }`}static getFrag(){return`
        uniform float renderBoundary;
        uniform int rollDirection;
        uniform vec3 diffuse;
        uniform float opacity;
        uniform float lightIntensity;

        #ifndef FLAT_SHADED

            varying vec3 vNormal;

        #endif

        #include <common>
        #include <color_pars_fragment>
        #include <uv_pars_fragment>
        #include <map_pars_fragment>
        #include <alphamap_pars_fragment>
        #include <aomap_pars_fragment>
        #include <lightmap_pars_fragment>
        #include <envmap_common_pars_fragment>
        #include <envmap_pars_fragment>
        #include <cube_uv_reflection_fragment>
        #include <fog_pars_fragment>
        #include <specularmap_pars_fragment>
        #include <logdepthbuf_pars_fragment>
        #include <clipping_planes_pars_fragment>
        #include <mv/boxclip_par_fragment>

        #include <mv/clip_par_fragment>       
        #include <mv/clip_model_par_fragment>
        void main() {

            if(rollDirection==1&&gl_FragCoord.x>renderBoundary){
                discard;
            }
            if(rollDirection==2&&gl_FragCoord.y<renderBoundary){
                discard;
            }

            #include <mv/boxclip_fragment>
            #include <mv/clip_fragment>

            #include <clipping_planes_fragment>
            #include <mv/clip_model_fragment>

            vec4 diffuseColor = vec4( diffuse, opacity );

            #include <logdepthbuf_fragment>
            #include <map_fragment>
            #include <color_fragment>
            #include <alphamap_fragment>
            #include <alphatest_fragment>
            #include <specularmap_fragment>

            ReflectedLight reflectedLight = ReflectedLight( vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ) );

            // accumulation (baked indirect lighting only)
            #ifdef USE_LIGHTMAP

            vec4 lightMapTexel = texture2D( lightMap, vLightMapUv );
            reflectedLight.indirectDiffuse += lightMapTexel.rgb * lightMapIntensity * RECIPROCAL_PI;

	        #else

		    reflectedLight.indirectDiffuse += vec3( 1.0 );

	        #endif

            // modulation
            #include <aomap_fragment>

            reflectedLight.indirectDiffuse *= diffuseColor.rgb;

            vec3 outgoingLight = reflectedLight.indirectDiffuse;

            #include <envmap_fragment>

            // outgoingLight*=lightIntensity;

            gl_FragColor = vec4( outgoingLight, diffuseColor.a);//

            #include <premultiplied_alpha_fragment>
            #include <tonemapping_fragment>
            #include <encodings_fragment>
            #include <fog_fragment>

            // gl_FragColor=vec4(diffuseColor.rgb,1.0);

        }
        `}}class pt extends Ke{constructor(e){let{uniforms:t,vertexShader:i,fragmentShader:a}=pt.getMaterialInfo();e.uniforms=t,e.vertexShader=i,e.fragmentShader=a,e.side==null&&(e.side=c.DoubleSide),super(e)}static getMaterialInfo(){let e=ei(c.ShaderLib.basic.uniforms);e.diffuse.value=new c.Color(0,0,0);let t=[];for(let a=0;a<200;a++)t.push(new c.Vector2);e.matrixWorld={value:new c.Matrix4},e.u_clipPoints={value:t},e.lightIntensity={value:1};let i=new c.LineBasicMaterial;return ti(e,i,void 0),zs(e,i),{uniforms:e,vertexShader:ze.getVert(),fragmentShader:ze.getFrag()}}}class mt extends Ke{constructor(e){let{uniforms:t,vertexShader:i,fragmentShader:a}=mt.getMaterialInfo();e.uniforms=t,e.vertexShader=i,e.fragmentShader=a,e.side==null&&(e.side=c.DoubleSide),super(e)}static getMaterialInfo(){let e=ei(c.ShaderLib.phong.uniforms),t=new c.MeshPhongMaterial;ti(e,t,void 0),ks(e,t);for(let i=0;i<9;i++)e.lightProbe.value.push(new c.Vector3);return e.hemisphereLights.value=[{direction:new c.Vector3(0,1,0),groundColor:new c.Vector3(1,1,1),skyColor:new c.Vector3(1,1,1)}],{uniforms:e,vertexShader:this.getVert(),fragmentShader:this.getFrag()}}static getVert(){return`
                #define PHONG

                varying vec3 vViewPosition;

                #ifndef FLAT_SHADED

                    varying vec3 vNormal;

                #endif

                #include <common>
                #include <uv_pars_vertex>
                #include <displacementmap_pars_vertex>
                #include <envmap_pars_vertex>
                #include <color_pars_vertex>
                #include <fog_pars_vertex>
                #include <morphtarget_pars_vertex>
                #include <skinning_pars_vertex>
                #include <shadowmap_pars_vertex>
                #include <logdepthbuf_pars_vertex>
                #include <clipping_planes_pars_vertex>
                #include <mv/boxclip_par_vertex>

                void main() {

                    #include <uv_vertex>
                    #include <color_vertex>

                    #include <beginnormal_vertex>
                    #include <morphnormal_vertex>
                    #include <skinbase_vertex>
                    #include <skinnormal_vertex>
                    #include <defaultnormal_vertex>

                #ifndef FLAT_SHADED // Normal computed with derivatives when FLAT_SHADED

                    vNormal = normalize( transformedNormal );

                #endif

                    #include <begin_vertex>
                    #include <morphtarget_vertex>
                    #include <skinning_vertex>
                    #include <displacementmap_vertex>
                    #include <project_vertex>
                    #include <logdepthbuf_vertex>
                    #include <clipping_planes_vertex>

                    vViewPosition = - mvPosition.xyz;

                    #include <worldpos_vertex>
                    #include <envmap_vertex>
                    #include <shadowmap_vertex>
                    #include <fog_vertex>

                    #include <mv/boxclip_vertex>

                }
                `}static getFrag(){return`
                #define PHONG

                uniform float renderBoundary;

                uniform vec3 diffuse;
                uniform vec3 emissive;
                uniform vec3 specular;
                uniform float shininess;
                uniform float opacity;

                #include <common>
                #include <packing>
                #include <dithering_pars_fragment>
                #include <color_pars_fragment>
                #include <uv_pars_fragment>
                #include <map_pars_fragment>
                #include <alphamap_pars_fragment>
                #include <aomap_pars_fragment>
                #include <lightmap_pars_fragment>
                #include <emissivemap_pars_fragment>
                #include <envmap_common_pars_fragment>
                #include <envmap_pars_fragment>
                #include <cube_uv_reflection_fragment>
                #include <fog_pars_fragment>
                #include <bsdfs>
                #include <lights_pars_begin>
                #include <lights_phong_pars_fragment>
                #include <shadowmap_pars_fragment>
                #include <bumpmap_pars_fragment>
                #include <normalmap_pars_fragment>
                #include <specularmap_pars_fragment>
                #include <logdepthbuf_pars_fragment>
                #include <clipping_planes_pars_fragment>
                #include <mv/boxclip_par_fragment>

                void main() {

                    if(gl_FragCoord.x>renderBoundary){
                        discard;
                    }

                    #include <mv/boxclip_fragment>

                    #include <clipping_planes_fragment>

                    vec4 diffuseColor = vec4( diffuse, opacity );
                    ReflectedLight reflectedLight = ReflectedLight( vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ) );
                    vec3 totalEmissiveRadiance = emissive;

                    #include <logdepthbuf_fragment>
                    #include <map_fragment>
                    #include <color_fragment>
                    #include <alphamap_fragment>
                    #include <alphatest_fragment>
                    #include <specularmap_fragment>
                    #include <normal_fragment_begin>
                    #include <normal_fragment_maps>
                    #include <emissivemap_fragment>

                    // accumulation
                    #include <lights_phong_fragment>
                    #include <lights_fragment_begin>
                    #include <lights_fragment_maps>
                    #include <lights_fragment_end>

                    // modulation
                    #include <aomap_fragment>

                    vec3 outgoingLight = reflectedLight.directDiffuse + reflectedLight.indirectDiffuse + reflectedLight.directSpecular + reflectedLight.indirectSpecular + totalEmissiveRadiance;

                    #include <envmap_fragment>

                    gl_FragColor = vec4( outgoingLight, diffuseColor.a );

                    #include <tonemapping_fragment>
                    #include <encodings_fragment>
                    #include <fog_fragment>
                    #include <premultiplied_alpha_fragment>
                    #include <dithering_fragment>

                }
                `}}var ii=(r=>(r[r.mv2d=0]="mv2d",r[r.mv3d=1]="mv3d",r[r.mv3dnotexture=2]="mv3dnotexture",r[r.mv3duser=3]="mv3duser",r[r.meshbasic=4]="meshbasic",r[r.meshphong=5]="meshphong",r[r.linebasic=6]="linebasic",r))(ii||{});class Zi{static createMaterial(e,t={}){let i;switch(e){case 4:i=ze.getMaterialInfo();break;case 5:i=mt.getMaterialInfo();break;case 6:i=pt.getMaterialInfo();break}let{uniforms:a,vertexShader:s,fragmentShader:o}=i;a.renderBoundary={value:Q.RENDERBOUNDARY},a.rollDirection={value:1},a.lightIntensity={value:1};const n=new Ke({uniforms:a,vertexShader:s,fragmentShader:o,side:c.DoubleSide,transparent:!0,opacity:.5});t.color&&(a.diffuse.value=new c.Color(t.color));for(const l in t)Object.prototype.hasOwnProperty.call(t,l)&&(a[l]!=null?a[l].value=t[l]:n[l]=t[l]);return n}}class Ji extends c.Mesh{constructor(){super(...arguments),this.meshID="",this.materialID="",this.materials=[],this.merged=!1,this.primitives=[],this.filled=!1,this._box=new Z,this._needsUpdateBoundingBox=!1}get AABB(){return this.bbox||this.geometry&&(this.bbox=new Z,this.geometry.boundingBox===null&&this.geometry.computeBoundingBox(),this.updateMatrix(),this.bbox.copy(this.geometry.boundingBox).applyMatrix4(this.matrix)),this.bbox}get AABBWorld(){return this._box.copy(this.AABB).applyMatrix4(this.matrixWorld)}get needsUpdateBoundingBox(){return this._needsUpdateBoundingBox}set needsUpdateBoundingBox(e){this._needsUpdateBoundingBox=e,this.parent&&e==!0&&(this.parent.needsUpdateAABB=e)}updateAABB(){this.geometry&&(this.bbox||(this.bbox=new Z,this.updateMatrix()),this.geometry.computeBoundingBox(),this.geometry.computeBoundingSphere(),this.bbox.copy(this.geometry.boundingBox).applyMatrix4(this.matrix),this._needsUpdateBoundingBox=!1)}update(){this.primitives.forEach(e=>{e.clipBoxID&&this.feature.parent.viewClipBoxHelper.addClip2Primitive(e)})}addPart(e,t,i=!0){}deletePart(e){}addVisiblePart(e){}deleteVisiblePart(e){}addGhostPart(e){}deleteGhostPart(e){}addIsolatePart(e,t){}deleteIsolatePart(e){}addHighligthPart(e,t){}deleteHighlightPart(e){}addExplosionPart(e,t){}deleteExplosionPart(e){}}class vt extends Ji{constructor(e,t,i,a){super(e,t),this.matInfo=new Map,this.highlightInfo=new Map,this.isolateInfo=new Map,this.visibleInfo=new Map,this.sumMatInfo=new Map,this.ghostInfo=new Map,this.explosionInfo=new Map,this.elementTransformMatInfo=new Map,this.raytraceInfo=new Map,this.edgeMeshes=new Map,this.explosionIndexMap=new Map,this.batchOffsetInfo=new Map,this.v3=new c.Vector3,this.type="mvMesh",t&&(this.materials.push(t),i&&this.materials.push(i),a&&this.materials.push(a))}addHighligthPart(e,t=!0){this.highlightInfo.set(e,1),t&&this.update()}deleteHighlightPart(e){this.highlightInfo.delete(e),this.update()}addIsolatePart(e,t){let i=-1;if(t){for(let a=0;a<this.materials.length;a++)this.materials[a].name==t.name&&(i=a);i==-1&&(this.materials.push(t),i=this.materials.length-1)}this.isolateInfo.set(e,i),this.update()}deleteIsolatePart(e){this.isolateInfo.delete(e),this.update()}addPart(e,t,i=!0){let a=-1;if(t){for(let s=0;s<this.materials.length;s++)this.materials[s].name==t.name&&(a=s);a==-1&&(this.materials.push(t),a=this.materials.length-1)}this.matInfo.set(e,a),i&&this.update()}deletePart(e){this.matInfo.delete(e),this.update()}addGhostPart(e){if(this.ghostInfo.has(e))return;let t=this.primitives.find(s=>s.id==e),i;this.elementTransformMatInfo.has(t.elementID)?i=this.elementTransformMatInfo.get(t.elementID):(i=this.feature.parent.mv.materialService.clone3DMaterial(this.materials[0]),i.userData.originColor=i.color.clone(),i.userData.originOpacity=i.opacity,i.name="ghost_"+t.elementID,this.materials.push(i),this.elementTransformMatInfo.set(t.elementID,i));let a=this.materials.findIndex(s=>s.uuid==i.uuid);this.ghostInfo.set(e,a),this.update()}deleteGhostPart(e){this.ghostInfo.delete(e),this.update()}addExplosionPart(e,t){let i=this.materials.findIndex(a=>a.name==t.name);i==-1&&(this.materials.push(t),i=this.materials.length-1),this.explosionInfo.set(e,i),this.update()}deleteExplosionPart(e){this.explosionInfo.delete(e),this.update()}addVisiblePart(e){this.visibleInfo.set(e,-1),this.update()}deleteVisiblePart(e){this.visibleInfo.delete(e),this.update()}clearRayTracerPart(){this.raytraceInfo.clear()}deleteRayTracerPart(e){this.raytraceInfo.delete(e)}addRayTracerPart(e){this.raytraceInfo.set(e,2)}update(){let e=this.geometry;if(!(!e||!e.index)){if(e.clearGroups(),e.setDrawRange(0,1/0),this.sumMatInfo.clear(),this.primitives.forEach(t=>{t.status=Se.normal;let i=this.edgeMeshes.get(t.id);i&&(i.visible=!0)}),this.visibleInfo.forEach((t,i)=>{this.sumMatInfo.set(i,t)}),this.raytraceInfo.forEach((t,i)=>{this.sumMatInfo.get(i)!=-1&&this.sumMatInfo.set(i,t)}),this.matInfo.forEach((t,i)=>{this.sumMatInfo.get(i)!=-1&&this.sumMatInfo.set(i,t)}),this.isolateInfo.forEach((t,i)=>{this.sumMatInfo.get(i)!=-1&&this.sumMatInfo.set(i,t)}),this.highlightInfo.forEach((t,i)=>{this.sumMatInfo.get(i)!=-1&&this.sumMatInfo.set(i,t)}),this.ghostInfo.forEach((t,i)=>{this.sumMatInfo.get(i)!=-1&&this.sumMatInfo.set(i,t)}),this.explosionInfo.forEach((t,i)=>{this.sumMatInfo.get(i)!=-1&&this.sumMatInfo.set(i,t)}),this.material=this.materials,this.sumMatInfo.size>0)if(this.primitives.length==1){let t=this.sumMatInfo.get(this.primitives[0].id)?this.sumMatInfo.get(this.primitives[0].id):0;if(t!=0&&t!=2)if(t==-1){this.primitives[0].status=Se.hidden;let i=this.edgeMeshes.get(this.primitives[0].id);i&&(i.visible=!1)}else this.primitives[0].status=Se.changed;t==-1?e.setDrawRange(0,0):e.addGroup(0,e.index.count,t)}else{let t=0,i=0,a=this.sumMatInfo.get(this.primitives[0].id)?this.sumMatInfo.get(this.primitives[0].id):0;this.primitives.sort((s,o)=>s.start-o.start),this.primitives.forEach(s=>{let o=this.sumMatInfo.get(s.id)?this.sumMatInfo.get(s.id):0;if(o!=0&&o!=2)if(o==-1){s.status=Se.hidden;let n=this.edgeMeshes.get(s.id);n&&(n.visible=!1)}else s.status=Se.changed;o!=a&&(a!=-1&&i>0&&e.addGroup(t,i,a),t+=i,i=0,a=o),i+=s.end-s.start}),i>0&&a!=-1&&e.addGroup(t,i,a)}else this.material=this.materials[0];if(this.batchOffsetInfo.size>0){this.materials[0].defines.BATCHTRANSFORM==null&&(this.materials[0].defines.BATCHTRANSFORM=1,this.materials[0].needsUpdate=!0);let t;this.geometry.attributes.a_transform?t=this.geometry.attributes.a_transform.array:t=new Int8Array(this.geometry.attributes.position.count),this.primitives.forEach(i=>{this.batchOffsetInfo.has(i.elementID)&&t.fill(this.batchOffsetInfo.get(i.elementID),i.positionStart,i.positionStart+i.positionCount)}),this.geometry.setAttribute("a_transform",new c.Int8BufferAttribute(t,1))}super.update()}}addPrimitiveEdge(e,t){this.edgeMeshes.set(e,t),this.add(t)}deletePrimitiveEdge(e){let t=this.edgeMeshes.get(e);t&&(this.remove(t),this.edgeMeshes.delete(e),t.geometry.dispose())}glFunction(e,t){this.parent&&(this.parent.isTransparent==!1?e.setValue(t,"useAO",this.parent.useAO):e.setValue(t,"useAO",0))}updateGeometry4Explosion(){this.primitives.forEach(e=>{if(e.needsUpdateTransform==!1)return;e.needsUpdateTransform=!1;let t=e.positionStart+e.positionCount,i=this.geometry.attributes.position.array;for(let n=e.positionStart;n<t;n++)i[n*3]+=e.currentExplosion.x-e.lastExplosion.x,i[n*3+1]+=e.currentExplosion.y-e.lastExplosion.y,i[n*3+2]+=e.currentExplosion.z-e.lastExplosion.z;this.geometry.attributes.position.needsUpdate=!0,this.needsUpdateBoundingBox=!0;let a=[e.AABB.min.x,e.AABB.max.x],s=[e.AABB.min.y,e.AABB.max.y],o=[e.AABB.min.z,e.AABB.max.z];e.AABB.makeEmpty();for(let n=0;n<2;n++)for(let l=0;l<2;l++)for(let h=0;h<2;h++)this.v3.set(a[n],s[l],o[h]),this.v3.sub(e.lastExplosion).add(e.currentExplosion),e.AABB.expandByPoint(this.v3)})}updateGeometry(){this.primitives.forEach(e=>{if(this.filled==!1||e.needsUpdateTransform==!1)return;e.needsUpdateTransform=!1;let t=e.positionStart+e.positionCount,i=this.geometry.attributes.position.array;for(let n=e.positionStart;n<t;n++)this.v3.set(i[n*3],i[n*3+1],i[n*3+2]),this.transformVertice(this.v3,e),i[n*3]=this.v3.x,i[n*3+1]=this.v3.y,i[n*3+2]=this.v3.z;this.geometry.attributes.position.needsUpdate=!0,this.needsUpdateBoundingBox=!0,this.updatePrimBox(e);let a=this.edgeMeshes.get(e.id+"_h");if(a==null)return;let s=a.geometry,o=s.attributes.position.array;for(let n=0;n<o.length;n+=3)this.v3.set(o[n],o[n+1],o[n+2]),this.transformVertice(this.v3,e),o[n]=this.v3.x,o[n+1]=this.v3.y,o[n+2]=this.v3.z;s.attributes.position.needsUpdate=!0})}updatePrimBox(e){let t=[e.AABB.min.x,e.AABB.max.x],i=[e.AABB.min.y,e.AABB.max.y],a=[e.AABB.min.z,e.AABB.max.z];e.AABB.makeEmpty();for(let s=0;s<2;s++)for(let o=0;o<2;o++)for(let n=0;n<2;n++)this.transformVertice(this.v3.set(t[s],i[o],a[n]),e),e.AABB.expandByPoint(this.v3)}transformVertice(e,t){e.sub(t.lastCenter),e.applyMatrix4(t.lastRotationInvert),e.divide(t.lastScale),e.add(t.lastCenter),e.sub(t.lastOffset),e.add(t.currentOffset),e.sub(t.currentCenter),e.multiply(t.currentScale),e.applyMatrix4(t.currentRotation),e.add(t.currentCenter)}}class ai{static getRasterBeforeLayer(e){let t=e.style.order,i;for(let a=0;a<t.length;a++){const s=t[a];if(!(s=="skybox"||s=="background")&&e.getLayer(s).type!="raster"){if(e.getLayer(s).type=="symbol"||e.getLayer(s).type=="line"){i=s;break}if(e.getLayer(s).type=="fill"&&s.indexOf("-Polygon")!=-1){i=s;break}}}return i}static getFillLayerBeforeLayer(e){let t=e.style.order,i;for(let a=t.length-1;a>0;a--)if(e.getLayer(t[a]).type=="symbol"){i=t[a];break}return i==null&&(i=t.find(a=>e.getLayer(a).type=="line")),i}static getLineLayerBeforeLayer(e){let t=e.style.order,i;for(let a=t.length-1;a>0;a--)if(e.getLayer(t[a]).type=="symbol"){i=t[a];break}return i}}var gt=(r=>(r[r.common=0]="common",r[r.useTerrainOpacity=1]="useTerrainOpacity",r))(gt||{});class Hs{constructor(e){this.domID=e,this.layers=new Map,this.sources=new Map,this._terrain=!1,this._visible=!1,this._style="",this.needSyncCamera=!0,this._needUpdate=!1,this._opacity=1,this._isCorrectProjectMatrix=!1,this.isStyleLoaded=!1,this.map=new W.mapboxgl.Map({container:this.domID,style:"",zoom:18,maxZoom:30,center:[0,0],maxTileCacheSize:5e3,pitch:0,antialias:!0,doubleClickZoom:!1,useWebGL2:!0}),this.map._silenceAuthErrors=!0,["mapboxgl-ctrl-logo","mapboxgl-ctrl-bottom-right"].forEach(o=>{let n=document.getElementsByClassName(o);n!=null&&n!=null&&(n[0].style.visibility="hidden")});const i=new c.Matrix4,a={id:"THREE",type:"custom",renderingMode:"3d",onAdd:(o,n)=>{},render:(o,n)=>{this.matrix=n,this.mv.THREE_Camera.updateMatrixWorld(),(this._isCorrectProjectMatrix||this.terrain)&&(i.fromArray(n),this.mv.THREE_Camera.projectionMatrix=i.multiply(this.mv.cameraSync.baseMatrix).multiply(this.mv.THREE_Camera.matrixWorld),this.mv.THREE_Camera.projectionMatrixInverse.copy(this.mv.THREE_Camera.projectionMatrix).invert()),this.needSyncCamera&&this.terrain&&(this.map.getSource("dem")&&this.map.isSourceLoaded("dem")==!0&&(this.needSyncCamera=!1),this.mv.cameraSync.syncTHREEToMapbox()),this.scene.config.shadow&&this.scene.mv.shadow.update(),this.raster.render(this.scene),this.render.state.reset(),this.render.resetState()}};a.visible=!0,this.layers.set("THREE",a);const s={id:"skybox",type:"custom",renderingMode:"3d",onAdd:(o,n)=>{},render:(o,n)=>{this.raster.renderExtras(this.scene)}};s.visible=!0,this.layers.set("skybox",s),this.map.handlers._handlers.forEach(o=>{o.handlerName!="mapEvent"&&o.handler.disable()}),this.map.on("load",o=>{this.scene.mv.post.resetSSGIPass(!0),this.isStyleLoaded=!0}),this.map.on("style.load",()=>{this.isStyleLoaded=!0,this.restoreSources(),this.restoreCustomerLayers();let o=this.scene.config.fogRange;if(o){let n=this.map.getFog();n?n.range=o:n={range:o},this.map.setFog(n)}this.scene.render(),this.needUpdate=!1}),this.map.on("render",()=>{this._needUpdate&&this.map.isStyleLoaded()&&(this.restoreSources(),this.restoreCustomerLayers(),this.scene.render(),this.needUpdate=!1)})}get terrain(){return this._terrain}set terrain(e){e!=this._terrain&&(e==!1&&this.mv.THREE_Camera.updateProjectionMatrix(),this.needSyncCamera=!0,this._terrain=e)}get visible(){return this._visible}set visible(e){this._visible!=e&&(e==!0?this.map.getTerrain()&&(this.terrain=!0):this.terrain=!1,this._visible=e)}get style(){return this._style}set style(e){typeof e=="object"?e.layers.length==0||e.layers.length==1&&e.layers[0].type=="background"?this.isStyleLoaded=!0:this.isStyleLoaded=!1:this.isStyleLoaded=!1,this.map.setStyle(e,{diff:!0}),this.needUpdate=!0,this._style=e}set needUpdate(e){this._needUpdate=e}get opacity(){return this._opacity}set opacity(e){this._opacity=e>1?1:e,this.needUpdate=!0}get customCameraNearFarZ(){return this.map.transform.customCameraNearFarZ==null&&(this.map.transform.customCameraNearFarZ=!1),this.map.transform.customCameraNearFarZ}set customCameraNearFarZ(e){this.map.transform.customCameraNearFarZ!=e&&(this.map.transform.customCameraNearFarZ=e,this.mv.cameraSync.syncMapboxToTHREE())}get cameraNearZ(){return this.map.transform._nearZ?this.map.transform._nearZ:2}set cameraNearZ(e){this.map.transform._nearZ=e}get cameraFarZ(){return this.map.transform._farZ?this.map.transform._farZ:2e5}set cameraFarZ(e){this.map.transform._farZ=e}get isCorrectProjectMatrix(){return this._isCorrectProjectMatrix}set isCorrectProjectMatrix(e){this._isCorrectProjectMatrix!=e&&(this._isCorrectProjectMatrix=e,e==!1&&this.mv.THREE_Camera.updateProjectionMatrix())}setMapTransformRequesetFunction(e){this.map._requestManager._transformRequestFn=e}restoreCustomerLayers(){this.layers.forEach((e,t)=>{if(!this.map.getLayer(t)){let a;if(e.type=="fill-extrusion"){let s=this.map.getStyle().layers;for(var i=0;i<s.length;i++)if(s[i].type==="symbol"&&s[i].layout["text-field"]){a=s[i].id;break}this.map.addLayer(e,a)}else if(e.type=="raster"&&this.mv.scene.features.has(e.id)&&this.mv.scene.features.get(e.id).type!=fe.video){let s=ai.getRasterBeforeLayer(this.map);this.map.addLayer(e,s)}else if(e.type=="fill"&&this.mv.scene.features.has(e.id.replace("-Polygon",""))){let s=ai.getFillLayerBeforeLayer(this.map);this.map.addLayer(e,s)}else if(e.type=="line"&&(this.mv.scene.features.has(e.id.replace("-Outline",""))||this.mv.scene.features.has(e.id.replace("-Line","")))){let s=ai.getLineLayerBeforeLayer(this.map);this.map.addLayer(e,s)}else this.map.addLayer(e)}e.visible||this.map.setLayoutProperty(t,"visibility","none")}),this.resetLayerOrder()}resetLayerOrder(){if(this.isStyleLoaded==!1)return;if(this._opacity<1||this.layerOrderState==1)if(this.map.getLayer("background")&&this.map.setLayoutProperty("background","visibility","none"),this.map._optimizeForTerrain=!1,this.terrain){let i=this.map.style._order[0];this.map.moveLayer("THREE",i)}else this.map.moveLayer("THREE");else this.map.getLayer("background")&&this.map.setLayoutProperty("background","visibility","visible"),this.map._optimizeForTerrain=!0,this.map.moveLayer("THREE");let e=this.map.getLayer("3DBuilding");e&&e.sourceLayer=="building"&&(this.map.moveLayer("3DBuilding","THREE"),this.map.moveLayer("THREE","3DBuilding"));let t=this.map.style.order;this.map.moveLayer("skybox",t[0])}restoreSources(){this.sources.forEach((e,t)=>{this.map.getSource(t)||(this.map.addSource(t,e),e.visible&&e.type=="raster-dem"&&(this.map.setTerrain({source:t}),this.terrain=!0))})}hide(e){if(this._visible==!1)return;let t=this.layers.get(e);t&&(t.visible=!1,this.needUpdate=!0)}show(e){if(this._visible==!1)return;let t=this.layers.get(e);t&&this.map.style._layers[e]&&(this.isStyleLoaded&&this.map.setLayoutProperty(e,"visibility","visible"),t.visible=!0)}addLayer(e){this.layers.set(e.id,e),this.needUpdate=!0}removeLayer(e){this.isStyleLoaded&&this.map.removeLayer(e),this.layers.delete(e)}addSource(e,t){this.sources.set(e,t),this.map.isStyleLoaded()&&this.map.style.stylesheet&&this.map.getStyle()&&(this.map.addSource(e,t),t.visible&&t.type=="raster-dem"&&(this.map.setTerrain({source:e}),this.terrain=!0))}removeSource(e){if(this.sources.delete(e),this.map.isStyleLoaded()&&this.map.style.stylesheet&&this.map.getStyle()){let t=this.map.getSource(e);t&&t.type=="raster-dem"&&(this.terrain=!1),t&&this.map.removeSource(e)}}}function Ki(r){let e;try{e=new URL(r,"http://fakehost.com/")}catch{return null}const t=e.pathname.split("/").pop(),i=t.lastIndexOf(".");return i===-1||i===t.length-1?null:t.substring(i+1)}function Gs(r){Promise.resolve().then(r)}class Ws{constructor(){this.maxSize=2e5,this.minSize=6e4,this.unloadPercent=.05,this.itemSet=new Map,this.itemList=[],this.usedSet=new Set,this.callbacks=new Map,this.unloadPriorityCallback=null;const e=this.itemSet;this.defaultPriorityCallback=t=>e.get(t)}isFull(){return this.itemSet.size>=this.maxSize}add(e,t){const i=this.itemSet;if(i.has(e)||this.isFull())return!1;const a=this.usedSet,s=this.itemList,o=this.callbacks;return s.push(e),a.add(e),i.set(e,Date.now()),o.set(e,t),!0}remove(e){const t=this.usedSet,i=this.itemSet,a=this.itemList,s=this.callbacks;if(i.has(e)){s.get(e)(e);const o=a.indexOf(e);return a.splice(o,1),t.delete(e),i.delete(e),s.delete(e),!0}return!1}markUsed(e,t){const i=this.itemSet,a=this.usedSet;i.has(e)&&!a.has(e)&&(i.set(e,Date.now()),a.add(e))}markAllUnused(){this.usedSet.clear()}unloadUnusedContent(){}recursionTile(e,t,i){e.isTop==null?e.children&&e.children.forEach(a=>{this.recursionTile(a,t,i)}):e.isTop==!1&&(t.get(e)&&t.get(e)(e),i.delete(e),t.delete(e))}scheduleUnload(e=!0){this.scheduled||(this.scheduled=!0,Gs(()=>{this.scheduled=!1,this.unloadUnusedContent(),e&&this.markAllUnused()}))}}class Qi{constructor(){this.maxJobs=6,this.items=[],this.callbacks=new Map,this.currJobs=0,this.scheduled=!1,this.autoUpdate=!0,this.priorityCallback=()=>{throw new Error("PriorityQueue: PriorityCallback function not defined.")},this.schedulingCallback=e=>{requestAnimationFrame(e)},this._runjobs=()=>{this.tryRunJobs(),this.scheduled=!1}}sort(){const e=this.priorityCallback;this.items.sort(e)}add(e,t){return new Promise((i,a)=>{const s=(...l)=>t(...l).then(i).catch(a),o=this.items,n=this.callbacks;o.push(e),n.set(e,s),this.autoUpdate&&this.scheduleJobRun()})}remove(e){const t=this.items,i=this.callbacks,a=t.indexOf(e);a!==-1&&(t.splice(a,1),i.delete(e))}tryRunJobs(){if(this.sort(),this.mvStatus.controllerActive){this.scheduled=!1,this.scheduleJobRun();return}const e=this.items,t=this.callbacks,i=this.maxJobs;let a=this.currJobs;for(;i>a&&e.length>0;){a++;const s=e.pop(),o=t.get(s);if(t.delete(s),o==null){this.currJobs--,this.autoUpdate&&this.scheduleJobRun();continue}o(s).then(()=>{this.currJobs--,this.autoUpdate&&this.scheduleJobRun()}).catch(()=>{this.currJobs--,this.autoUpdate&&this.scheduleJobRun()})}this.currJobs=a}scheduleJobRun(){this.scheduled||(this.schedulingCallback(this._runjobs),this.scheduled=!0)}}const xt=0,yt=1,ri=2,ke=3,_t=4,bt=6378137,js=-(1/298.257223563*bt-bt);function Mt(r){return r===ke||r===_t}function Te(r,e){return r.__lastFrameVisited===e&&r.__used}function $i(r,e){r.__lastFrameVisited!==e&&(r.__lastFrameVisited=e,r.__used=!1,r.__inFrustum=!1,r.__isLeaf=!1,r.__visible=!1,r.__active=!1,r.__error=1/0,r.__distanceFromCamera=1/0,r.__childrenWereVisible=!1,r.__allChildrenLoaded=!1)}function ea(r,e,t,i){if(i.ensureChildrenArePreprocessed(r),$i(r,e),r.__used=!0,t.markUsed(r),r.__contentEmpty){const a=r.children;for(let s=0,o=a.length;s<o;s++)ea(a[s],e,t,i)}}function ta(r,e,t){if(t.ensureChildrenArePreprocessed(r),r.__contentEmpty&&(!r.__externalTileSet||Mt(r.__loadingState))){const a=r.children;for(let s=0,o=a.length;s<o;s++){const n=a[s];n.__depthFromRenderedParent=e,ta(n,e,t)}}else t.requestTileContents(r)}function ia(r,e=null,t=null,i=null,a=0){if(e&&e(r,i,a)){t&&t(r,i,a);return}const s=r.children;for(let o=0,n=s.length;o<n;o++)ia(s[o],e,t,r,a+1);t&&t(r,i,a)}function aa(r,e){e.ensureChildrenArePreprocessed(r);const t=e.stats,i=e.frameCount,a=e.errorTarget,s=e.maxDepth,o=e.loadSiblings,n=e.lruCache,l=e.stopAtEmptyTiles;if($i(r,i),e.tileInView(r)===!1)return!1;if(r.__used=!0,n.markUsed(r),r.__inFrustum=!0,t.inFrustum++,(l||!r.__contentEmpty)&&!r.__externalTileSet&&(e.calculateError(r),r.__error<=a||e.maxDepth>0&&r.__depth+1>=s))return!0;let u=!1;const d=r.children;for(let f=0,m=d.length;f<m;f++){const p=d[f],v=aa(p,e);u=u||v}if(u&&o)for(let f=0,m=d.length;f<m;f++){const p=d[f];ea(p,i,n,e)}return!0}function ra(r,e){const t=e.stats,i=e.frameCount;if(!Te(r,i))return;t.used++;const a=r.children;let s=!1;for(let o=0,n=a.length;o<n;o++){const l=a[o];s=s||Te(l,i)}if(!s)r.__isLeaf=!0;else{let o=!1,n=!0;for(let l=0,h=a.length;l<h;l++){const u=a[l];if(ra(u,e),o=o||u.__wasSetVisible||u.__childrenWereVisible,Te(u,i)){const d=u.__allChildrenLoaded||!u.__contentEmpty&&Mt(u.__loadingState)||u.__externalTileSet&&u.__loadingState===_t;n=n&&d}}r.__childrenWereVisible=o,r.__allChildrenLoaded=n}}function sa(r,e){const t=e.stats,i=e.frameCount;if(!Te(r,i))return;const a=r.parent,s=a?a.__depthFromRenderedParent:-1;r.__depthFromRenderedParent=s;const o=e.lruCache;if(r.__isLeaf){r.__depthFromRenderedParent++,r.__loadingState===ke?(r.__inFrustum&&(r.__visible=!0,t.visible++),r.__active=!0,t.active++):!o.isFull()&&(!r.__contentEmpty||r.__externalTileSet)&&(e.requestTileContents(r),oa(r,t));return}const n=(e.errorTarget+1)*e.errorThreshold,l=r.__error<=n,h=l||r.refine==="ADD",u=!r.__contentEmpty,d=u||r.__externalTileSet,f=Mt(r.__loadingState)&&d,m=r.__childrenWereVisible,p=r.children,v=r.__allChildrenLoaded;if(h&&u&&r.__depthFromRenderedParent++,h&&!f&&!o.isFull()&&d&&e.requestTileContents(r),(l&&!v&&!m&&f||r.refine==="ADD"&&f)&&(r.__inFrustum&&(r.__visible=!0,t.visible++),r.__active=!0,t.active++),r.refine!=="ADD"&&l&&!v&&f)for(let g=0,x=p.length;g<x;g++){const _=p[g];Te(_,i)&&!o.isFull()&&(_.__depthFromRenderedParent=r.__depthFromRenderedParent+1,ta(_,_.__depthFromRenderedParent,e))}else for(let g=0,x=p.length;g<x;g++){const _=p[g];Te(_,i)&&sa(_,e)}}function oa(r,e){if(r.parent){const t=r.parent,a=!t.__contentEmpty||t.__externalTileSet;Mt(t.__loadingState)&&a?(t.__visible=!0,e.visible++,t.__active=!0,e.active++):oa(t,e)}}function na(r,e){const t=e.frameCount,i=Te(r,t);if(i||r.__usedLastFrame){let a=!1,s=!1;i&&(a=r.__active,e.displayActiveTiles?s=r.__active||r.__visible:s=r.__visible),!r.__contentEmpty&&r.__loadingState===ke&&(r.__wasSetActive!==a&&e.setTileActive(r,a),r.__wasSetVisible!==s&&e.setTileVisible(r,s)),r.__wasSetActive=a,r.__wasSetVisible=s,r.__usedLastFrame=i;const o=r.children;for(let n=0,l=o.length;n<l;n++){const h=o[n];na(h,e)}}}const la=r=>1/(r.__depthFromRenderedParent+6),qs=r=>1/(r.__depthFromRenderedParent+6);class Xs{get rootTileSet(){const e=this.tileSets[this.rootURL];return!e||e instanceof Promise?null:e}get root(){const e=this.rootTileSet;return e?e.root:null}constructor(e){this.tileSets={},this.rootURL=e,this.fetchOptions={},this.preprocessURL=null;const t=new Ws;t.unloadPriorityCallback=qs;const i=new Qi;i.maxJobs=50,i.priorityCallback=la;const a=new Qi;a.maxJobs=100,a.priorityCallback=la,this.lruCache=t,this.downloadQueue=i,this.parseQueue=a,this.stats={parsing:0,downloading:0,failed:0,inFrustum:0,used:0,active:0,visible:0},this.frameCount=0,this.errorTarget=6,this.errorThreshold=1/0,this.loadSiblings=!0,this.displayActiveTiles=!1,this.maxDepth=1/0,this.stopAtEmptyTiles=!0}traverse(e,t){const a=this.tileSets[this.rootURL];!a||!a.root||ia(a.root,(s,...o)=>(this.ensureChildrenArePreprocessed(s),e?e(s,...o):!1),t)}update(){const e=this.stats,t=this.lruCache,i=this.tileSets,a=i[this.rootURL];if(this.rootURL in i){if(!a||!a.root)return}else{this.loadRootTileSet(this.rootURL);return}const s=a.root;e.inFrustum=0,e.used=0,e.active=0,e.visible=0,this.frameCount++,aa(s,this),ra(s,this),sa(s,this),na(s,this),t.scheduleUnload()}parseTile(e,t,i){return null}disposeTile(e){}preprocessNode(e,t,i=null){if(e.content&&(!("uri"in e.content)&&"url"in e.content&&(e.content.uri=e.content.url,delete e.content.url),e.content.uri&&(e.content.uri=new URL(e.content.uri,t+"/").toString()),e.content.boundingVolume&&!("box"in e.content.boundingVolume||"sphere"in e.content.boundingVolume||"region"in e.content.boundingVolume)&&delete e.content.boundingVolume),e.parent=i,e.children=e.children||[],e.content&&e.content.uri){const s=Ki(e.content.uri),o=!!(s&&s.toLowerCase()==="json");e.__externalTileSet=o,e.__contentEmpty=o}else e.__externalTileSet=!1,e.__contentEmpty=!0;e.__distanceFromCamera=1/0,e.__error=1/0,e.__inFrustum=!1,e.__isLeaf=!1,e.__usedLastFrame=!1,e.__used=!1,e.__wasSetVisible=!1,e.__visible=!1,e.__childrenWereVisible=!1,e.__allChildrenLoaded=!1,e.__wasSetActive=!1,e.__active=!1,e.__loadingState=xt,e.__loadIndex=0,e.__loadAbort=null,e.__depthFromRenderedParent=-1,i===null?(e.__depth=0,e.refine=e.refine||"REPLACE"):(e.__depth=i.__depth+1,e.refine=e.refine||i.refine),e.__basePath=t}setTileActive(e,t){}setTileVisible(e,t){}calculateError(e){return 0}tileInView(e){return!0}ensureChildrenArePreprocessed(e){const t=e.children,i=t.length;let a=!1;for(let s=0;s<i;s++){const o=t[s];if("__depth"in o)break;this.preprocessNode(o,e.__basePath,e),o.isDiscard&&(a=!0)}a&&(e.children=e.children.filter(s=>s.isDiscard==null))}resetFailedTiles(){const e=this.stats;e.failed!==0&&(this.traverse(t=>{t.__loadingState===_t&&(t.__loadingState=xt)}),e.failed=0)}fetchTileSet(e,t,i=null){return fetch(e,t).then(a=>{if(a.ok)return a.json();throw new Error(`TilesRenderer: Failed to load tileset "${e}" with status ${a.status} : ${a.statusText}`)}).then(a=>{const s=a.asset.version;console.assert(s==="1.0"||s==="0.0"||s=="1.1",'asset.version is expected to be a string of "1.0" or "0.0" or "1.1"');let o=e.replace(/\/[^\/]*\/?$/,"");return o=new URL(o,window.location.href).toString(),this.preprocessNode(a.root,o,i),a})}loadRootTileSet(e){const t=this.tileSets;if(e in t)return t[e]instanceof Error?Promise.reject(t[e]):Promise.resolve(t[e]);{const i=this.fetchTileSet(this.preprocessURL?this.preprocessURL(e):e,this.fetchOptions).then(a=>{t[e]=a});return i.catch(a=>{console.error(a),t[e]=a}),t[e]=i,i}}requestTileContents(e){if(e.__loadingState!==xt)return;const t=this.stats,i=this.lruCache,a=this.downloadQueue,s=this.parseQueue,o=e.__externalTileSet;i.add(e,d=>{d.__loadingState===yt?(d.__loadAbort.abort(),d.__loadAbort=null):o?d.children.length=0:this.disposeTile(d),d.__loadingState===yt?t.downloading--:d.__loadingState===ri&&t.parsing--,d.__loadingState=xt,d.__loadIndex++,s.remove(d),a.remove(d)}),e.__loadIndex++;const n=e.__loadIndex,l=new AbortController,h=l.signal;t.downloading++,e.__loadAbort=l,e.__loadingState=yt;const u=d=>{e.__loadIndex===n&&(d.name!=="AbortError"?(s.remove(e),a.remove(e),e.__loadingState===ri?t.parsing--:e.__loadingState===yt&&t.downloading--,t.failed++,console.error(`TilesRenderer : Failed to load tile at url "${e.content.uri}".`),console.error(d),e.__loadingState=_t):i.remove(e))};o?a.add(e,d=>{if(d.__loadIndex!==n)return Promise.resolve();const f=this.preprocessURL?this.preprocessURL(d.content.uri):d.content.uri;return this.fetchTileSet(f,Object.assign({signal:h},this.fetchOptions),d)}).then(d=>{e.__loadIndex===n&&(t.downloading--,e.__loadAbort=null,e.__loadingState=ke,e.children.push(d.root))}).catch(u):a.add(e,d=>{if(d.__loadIndex!==n)return Promise.resolve();const f=this.preprocessURL?this.preprocessURL(d.content.uri):d.content.uri;return fetch(f,Object.assign({signal:h},this.fetchOptions))}).then(d=>{if(e.__loadIndex===n){if(d.ok)return d.arrayBuffer();throw new Error(`Failed to load model with error code ${d.status}`)}}).then(d=>{if(e.__loadIndex===n)return t.downloading--,t.parsing++,e.__loadAbort=null,e.__loadingState=ri,s.add(e,f=>{if(f.__loadIndex!==n)return Promise.resolve();const m=f.content.uri,p=Ki(m);return this.parseTile(d,f,p)})}).then(()=>{e.__loadIndex===n&&(t.parsing--,e.__loadingState=ke,e.__wasSetVisible&&this.setTileVisible(e,!0),e.__wasSetActive&&this.setTileActive(e,!0))}).catch(u)}dispose(){const e=this.lruCache,t=[];this.traverse(i=>(t.push(i),!1));for(let i=0,a=t.length;i<a;i++)e.remove(t[i]);this.stats={parsing:0,downloading:0,failed:0,inFrustum:0,used:0,active:0,visible:0},this.frameCount=0}}function ca(r){return new TextDecoder().decode(r)}class wt{constructor(e,t,i,a){this.buffer=e,this.binOffset=t+i,this.binLength=a;let s=null;if(i!==0){const o=new Uint8Array(e,t,i);s=JSON.parse(ca(o))}else s={};this.header=s}getKeys(){return Object.keys(this.header)}getData(e,t,i=null,a=null){const s=this.header;if(!(e in s))return null;const o=s[e];if(o instanceof Object){if(Array.isArray(o))return o;{const{buffer:n,binOffset:l,binLength:h}=this,u=o.byteOffset||0,d=o.type||a,f=o.componentType||i;if("type"in o&&a&&o.type!==a)throw new Error("FeatureTable: Specified type does not match expected type.");let m;switch(d){case"SCALAR":m=1;break;case"VEC2":m=2;break;case"VEC3":m=3;break;case"VEC4":m=4;break;default:throw new Error(`FeatureTable : Feature type not provided for "${e}".`)}let p;const v=l+u,g=t*m;switch(f){case"BYTE":p=new Int8Array(n,v,g);break;case"UNSIGNED_BYTE":p=new Uint8Array(n,v,g);break;case"SHORT":p=new Int16Array(n,v,g);break;case"UNSIGNED_SHORT":p=new Uint16Array(n,v,g);break;case"INT":p=new Int32Array(n,v,g);break;case"UNSIGNED_INT":p=new Uint32Array(n,v,g);break;case"FLOAT":p=new Float32Array(n,v,g);break;case"DOUBLE":p=new Float64Array(n,v,g);break;default:throw new Error(`FeatureTable : Feature component type not provided for "${e}".`)}if(v+g*p.BYTES_PER_ELEMENT>l+h)throw new Error("FeatureTable: Feature data read outside binary body length.");return p}}else return o}getBuffer(e,t){const{buffer:i,binOffset:a}=this;return i.slice(a+e,a+e+t)}}class si extends wt{constructor(e,t,i,a,s){super(e,i,a,s),this.batchSize=t}getData(e,t=null,i=null){return super.getData(e,this.batchSize,t,i)}}class Qe{constructor(){this.fetchOptions={},this.workingPath=""}load(e){return fetch(e,this.fetchOptions).then(t=>{if(!t.ok)throw new Error(`Failed to load file "${e}" with status ${t.status} : ${t.statusText}`);return t.arrayBuffer()}).then(t=>(this.workingPath===""&&(this.workingPath=this.workingPathForURL(e)),this.parse(t)))}resolveExternalURL(e){return/^[^\\/]/.test(e)?this.workingPath+"/"+e:e}workingPathForURL(e){const t=e.split(/[\\/]/g);return t.pop(),t.join("/")+"/"}parse(e){throw new Error("LoaderBase: Parse not implemented.")}}function He(r){let e;if(r instanceof DataView?e=r:e=new DataView(r),String.fromCharCode(e.getUint8(0))==="{")return null;let t="";for(let i=0;i<4;i++)t+=String.fromCharCode(e.getUint8(i));return t}class Ys extends Qe{parse(e){const t=new DataView(e),i=He(t);console.assert(i==="b3dm");const a=t.getUint32(4,!0);console.assert(a===1);const s=t.getUint32(8,!0);console.assert(s===e.byteLength);const o=t.getUint32(12,!0),n=t.getUint32(16,!0),l=t.getUint32(20,!0),h=t.getUint32(24,!0),u=28,d=e.slice(u,u+o+n),f=new wt(d,0,o,n),m=u+o+n,p=e.slice(m,m+l+h),v=new si(p,f.getData("BATCH_LENGTH"),0,l,h),g=m+l+h,x=new Uint8Array(e,g,s-g);return{version:a,featureTable:f,batchTable:v,glbBytes:x}}}class oi extends Ys{constructor(e=c.DefaultLoadingManager){super(),this.manager=e,this.adjustmentTransform=new c.Matrix4}parse(e){const t=super.parse(e),i=t.glbBytes.slice().buffer;return new Promise((a,s)=>{const o=this.manager,n=this.fetchOptions,l=o.getHandler("path.gltf")||new F.GLTFLoader(o);n.credentials==="include"&&n.mode==="cors"&&l.setCrossOrigin("use-credentials"),"credentials"in n&&l.setWithCredentials(n.credentials==="include"),n.headers&&l.setRequestHeader(n.headers);let h=this.workingPath;!/[\\/]$/.test(h)&&h.length&&(h+="/");const u=this.adjustmentTransform;l.parse(i,h,d=>{const{batchTable:f,featureTable:m}=t,{scene:p}=d,v=m.getData("RTC_CENTER");if(v&&(p.position.x+=v[0],p.position.y+=v[1],p.position.z+=v[2]),d.scene.updateMatrix(),d.scene.matrix.multiply(u),d.scene.matrix.decompose(d.scene.position,d.scene.quaternion,d.scene.scale),d.batchTable=f,d.featureTable=m,p.batchTable=f,p.featureTable=m,d.userData.gltfExtensions&&d.userData.gltfExtensions.CESIUM_RTC){let g=d.userData.gltfExtensions.CESIUM_RTC.center;p.position.set(g[0],g[1],g[2])}a(d)},s)})}}class Zs extends Qe{parse(e){const t=new DataView(e),i=He(t);console.assert(i==="pnts");const a=t.getUint32(4,!0);console.assert(a===1);const s=t.getUint32(8,!0);console.assert(s===e.byteLength);const o=t.getUint32(12,!0),n=t.getUint32(16,!0),l=t.getUint32(20,!0),h=t.getUint32(24,!0),u=28,d=e.slice(u,u+o+n),f=new wt(d,0,o,n),m=u+o+n,p=e.slice(m,m+l+h),v=new si(p,f.getData("BATCH_LENGTH")||f.getData("POINTS_LENGTH"),0,l,h);return Promise.resolve({version:a,featureTable:f,batchTable:v})}}function Js(r){const e=r>>11,t=r>>5&63,i=r&31,a=Math.round(e/31*255),s=Math.round(t/63*255),o=Math.round(i/31*255);return[a,s,o]}const ha={RGB:"color",POSITION:"position"};class ni extends Zs{constructor(e=c.DefaultLoadingManager){super(),this.manager=e}parse(e,t={size:1,onBeforeCompile:null,sizeAttenuation:!1}){return super.parse(e).then(async i=>{const{featureTable:a}=i,s=new c.PointsMaterial({size:t.size,sizeAttenuation:t.sizeAttenuation}),o=a.header.extensions,n=new c.Vector3;let l;if(o&&o["3DTILES_draco_point_compression"]){const{byteOffset:d,byteLength:f,properties:m}=o["3DTILES_draco_point_compression"],p=this.manager.getHandler("draco.drc");if(p==null)throw new Error("PNTSLoader: dracoLoader not available.");const v={};for(const _ in m)if(_ in ha&&_ in m){const y=ha[_];v[y]=m[_]}const g={attributeIDs:v,attributeTypes:{position:"Float32Array",color:"Uint8Array"},useUniqueIDs:!0},x=a.getBuffer(d,f);l=await p.decodeGeometry(x,g),l.attributes.color&&(s.vertexColors=!0)}else{const d=a.getData("POINTS_LENGTH"),f=a.getData("POSITION",d,"FLOAT","VEC3"),m=a.getData("RGB",d,"UNSIGNED_BYTE","VEC3"),p=a.getData("RGBA",d,"UNSIGNED_BYTE","VEC4"),v=a.getData("RGB565",d,"UNSIGNED_SHORT","SCALAR"),g=a.getData("CONSTANT_RGBA",d,"UNSIGNED_BYTE","VEC4"),x=a.getData("POSITION_QUANTIZED",d,"UNSIGNED_SHORT","VEC3"),_=a.getData("QUANTIZED_VOLUME_SCALE",d,"FLOAT","VEC3"),y=a.getData("QUANTIZED_VOLUME_OFFSET",d,"FLOAT","VEC3");if(l=new c.BufferGeometry,x){const M=new Float32Array(d*3);for(let b=0;b<d;b++)for(let w=0;w<3;w++){const T=3*b+w;M[T]=x[T]/65535*_[w]}n.x=y[0],n.y=y[1],n.z=y[2],l.setAttribute("position",new c.BufferAttribute(M,3,!1))}else l.setAttribute("position",new c.BufferAttribute(f,3,!1));if(p!==null)l.setAttribute("color",new c.BufferAttribute(p,4,!0)),s.vertexColors=!0,s.transparent=!0,s.depthWrite=!1;else if(m!==null)l.setAttribute("color",new c.BufferAttribute(m,3,!0)),s.vertexColors=!0;else if(v!==null){const M=new Uint8Array(d*3);for(let b=0;b<d;b++){const w=Js(v[b]);for(let T=0;T<3;T++){const I=3*b+T;M[I]=w[T]}}l.setAttribute("color",new c.BufferAttribute(M,3,!0)),s.vertexColors=!0}else if(g!==null){const M=new c.Color(g[0],g[1],g[2]);s.color=M;const b=g[3]/255;b<1&&(s.opacity=b,s.transparent=!0,s.depthWrite=!1)}}["BATCH_LENGTH","NORMAL","NORMAL_OCT16P"].forEach(d=>{d in a.header&&console.warn(`PNTSLoader: Unsupported FeatureTable feature "${d}" detected.`)});const h=new c.Points(l,s);h.position.copy(n),i.scene=h,i.scene.featureTable=a;const u=a.getData("RTC_CENTER");return u&&(i.scene.position.x+=u[0],i.scene.position.y+=u[1],i.scene.position.z+=u[2]),i})}}class Ks extends Qe{parse(e){const t=new DataView(e),i=He(t);console.assert(i==="i3dm");const a=t.getUint32(4,!0);console.assert(a===1);const s=t.getUint32(8,!0);console.assert(s===e.byteLength);const o=t.getUint32(12,!0),n=t.getUint32(16,!0),l=t.getUint32(20,!0),h=t.getUint32(24,!0),u=t.getUint32(28,!0),d=32,f=e.slice(d,d+o+n),m=new wt(f,0,o,n),p=d+o+n,v=e.slice(p,p+l+h),g=new si(v,m.getData("INSTANCES_LENGTH"),0,l,h),x=p+l+h,_=new Uint8Array(e,x,s-x);let y=null,M=null;if(u)y=_,M=Promise.resolve();else{const b=this.resolveExternalURL(ca(_));M=fetch(b,this.fetchOptions).then(w=>{if(!w.ok)throw new Error(`I3DMLoaderBase : Failed to load file "${b}" with status ${w.status} : ${w.statusText}`);return w.arrayBuffer()}).then(w=>{y=new Uint8Array(w)})}return M.then(()=>({version:a,featureTable:m,batchTable:g,glbBytes:y}))}}const ua=new c.Vector3,li=new c.Vector3,ci=new c.Vector3,da=new c.Vector3,hi=new c.Quaternion,St=new c.Vector3,Tt=new c.Matrix4;class ui extends Ks{constructor(e=c.DefaultLoadingManager){super(),this.manager=e,this.adjustmentTransform=new c.Matrix4}resolveExternalURL(e){return this.manager.resolveURL(super.resolveExternalURL(e))}parse(e){return super.parse(e).then(t=>{const{featureTable:i,batchTable:a}=t,s=t.glbBytes.slice().buffer;return new Promise((o,n)=>{const l=this.fetchOptions,h=this.manager,u=h.getHandler("path.gltf")||new F.GLTFLoader(h);l.credentials==="include"&&l.mode==="cors"&&u.setCrossOrigin("use-credentials"),"credentials"in l&&u.setWithCredentials(l.credentials==="include"),l.headers&&u.setRequestHeader(l.headers);let d=this.workingPath;/[\\/]$/.test(d)||(d+="/");const f=this.adjustmentTransform;u.parse(s,d,m=>{const p=i.getData("INSTANCES_LENGTH"),v=i.getData("POSITION",p,"FLOAT","VEC3"),g=i.getData("NORMAL_UP",p,"FLOAT","VEC3"),x=i.getData("NORMAL_RIGHT",p,"FLOAT","VEC3"),_=i.getData("SCALE_NON_UNIFORM",p,"FLOAT","VEC3"),y=i.getData("SCALE",p,"FLOAT","SCALAR");["RTC_CENTER","QUANTIZED_VOLUME_OFFSET","QUANTIZED_VOLUME_SCALE","EAST_NORTH_UP","POSITION_QUANTIZED","NORMAL_UP_OCT32P","NORMAL_RIGHT_OCT32P"].forEach(T=>{T in i.header&&console.warn(`I3DMLoader: Unsupported FeatureTable feature "${T}" detected.`)});const M=new Map,b=[];m.scene.traverse(T=>{if(T.isMesh){const{geometry:I,material:S}=T,R=new c.InstancedMesh(I,S,p);R.position.copy(T.position),R.rotation.copy(T.rotation),R.scale.copy(T.scale),b.push(R),M.set(T,R)}});const w=new c.Vector3;for(let T=0;T<p;T++)w.x+=v[T*3+0]/p,w.y+=v[T*3+1]/p,w.z+=v[T*3+2]/p;M.forEach((T,I)=>{const S=I.parent;S&&(S.remove(I),S.add(T),T.updateMatrixWorld(),T.position.copy(w).applyMatrix4(T.matrixWorld))});for(let T=0;T<p;T++){da.set(v[T*3+0]-w.x,v[T*3+1]-w.y,v[T*3+2]-w.z),g?(li.set(g[T*3+0],g[T*3+1],g[T*3+2]),ci.set(x[T*3+0],x[T*3+1],x[T*3+2]),ua.crossVectors(ci,li).normalize(),Tt.makeBasis(ci,li,ua),hi.setFromRotationMatrix(Tt)):hi.set(0,0,0,1),y?St.setScalar(y[T]):_?St.set(_[T*3+0],_[T*3+1],_[T*3+2]):St.set(1,1,1),Tt.compose(da,hi,St).multiply(f);for(let I=0,S=b.length;I<S;I++)b[I].setMatrixAt(T,Tt)}m.batchTable=a,m.featureTable=i,m.scene.batchTable=a,m.scene.featureTable=i,o(m)},n)})})}}class Qs extends Qe{parse(e){const t=new DataView(e),i=He(t);console.assert(i==="cmpt",'CMPTLoader: The magic bytes equal "cmpt".');const a=t.getUint32(4,!0);console.assert(a===1,'CMPTLoader: The version listed in the header is "1".');const s=t.getUint32(8,!0);console.assert(s===e.byteLength,"CMPTLoader: The contents buffer length listed in the header matches the file.");const o=t.getUint32(12,!0),n=[];let l=16;for(let h=0;h<o;h++){const u=new DataView(e,l,12),d=He(u),f=u.getUint32(4,!0),m=u.getUint32(8,!0),p=new Uint8Array(e,l,m);n.push({type:d,buffer:p,version:f}),l+=m}return{version:a,tiles:n}}}class fa extends Qs{constructor(e=c.DefaultLoadingManager){super(),this.manager=e,this.adjustmentTransform=new c.Matrix4}parse(e){const t=super.parse(e),i=this.manager,a=this.adjustmentTransform,s=[];for(const o in t.tiles){const{type:n,buffer:l}=t.tiles[o];switch(n){case"b3dm":{const h=l.slice(),u=new oi(i);u.workingPath=this.workingPath,u.fetchOptions=this.fetchOptions,u.adjustmentTransform.copy(a);const d=u.parse(h.buffer);s.push(d);break}case"pnts":{const h=l.slice(),u=new ni(i);u.workingPath=this.workingPath,u.fetchOptions=this.fetchOptions;const d=u.parse(h.buffer);s.push(d);break}case"i3dm":{const h=l.slice(),u=new ui(i);u.workingPath=this.workingPath,u.fetchOptions=this.fetchOptions,u.adjustmentTransform.copy(a);const d=u.parse(h.buffer);s.push(d);break}}}return Promise.all(s).then(o=>{const n=new c.Group;return o.forEach(l=>{n.add(l.scene)}),{tiles:o,scene:n}})}}class $s{constructor(){this.name="CESIUM_RTC"}afterRoot(e){if(e.parser.json.extensions&&e.parser.json.extensions.CESIUM_RTC){const{center:t}=e.parser.json.extensions.CESIUM_RTC;t&&(e.scene.position.x+=t[0],e.scene.position.y+=t[1],e.scene.position.z+=t[2])}}}class pa extends Qe{constructor(e=c.DefaultLoadingManager){super(),this.manager=e}parse(e){return new Promise((t,i)=>{const a=this.manager,s=this.fetchOptions;let o=a.getHandler("path.gltf")||a.getHandler("path.glb");o||(o=new F.GLTFLoader(a),o.register(()=>new $s),s.credentials==="include"&&s.mode==="cors"&&o.setCrossOrigin("use-credentials"),"credentials"in s&&o.setWithCredentials(s.credentials==="include"),s.headers&&o.setRequestHeader(s.headers));let n=o.resourcePath||o.path||this.workingPath;!/[\\/]$/.test(n)&&n.length&&(n+="/"),o.parse(e,n,l=>{t(l)},i)})}}const At=new c.Matrix4;class eo extends c.Group{constructor(e){super(),this.name="TilesRenderer.TilesGroup",this.tilesRenderer=e}raycast(e,t){this.tilesRenderer.optimizeRaycast&&this.tilesRenderer.raycast(e,t)}updateMatrixWorld(e){if(this.matrixAutoUpdate&&this.updateMatrix(),this.matrixWorldNeedsUpdate||e){this.parent===null?At.copy(this.matrix):At.multiplyMatrices(this.parent.matrixWorld,this.matrix),this.matrixWorldNeedsUpdate=!1;const t=At.elements,i=this.matrixWorld.elements;let a=!1;for(let s=0;s<16;s++){const o=t[s],n=i[s];if(Math.abs(o-n)>Number.EPSILON){a=!0;break}}if(a){this.matrixWorld.copy(At);const s=this.children;for(let o=0,n=s.length;o<n;o++)s[o].updateMatrixWorld()}}}}const Pt=new c.Matrix4,ma=new c.Ray,di=new c.Vector3,Et=[];function va(r,e){return r.distance-e.distance}function ga(r,e,t){r.traverse(i=>{Object.getPrototypeOf(i).raycast.call(i,e,t)})}function to(r,e){ga(r,e,Et),Et.sort(va);const t=Et[0]||null;return Et.length=0,t}function xa(r,e,t,i=null){const{group:a,activeTiles:s}=r;r.ensureChildrenArePreprocessed(e),i===null&&(i=ma,Pt.copy(a.matrixWorld).invert(),i.copy(t.ray).applyMatrix4(Pt));const o=[],n=e.children;for(let u=0,d=n.length;u<d;u++){const f=n[u];if(!f.__used)continue;f.cached.boundingVolume.intersectRay(i,di)!==null&&(di.applyMatrix4(a.matrixWorld),o.push({distance:di.distanceToSquared(t.ray.origin),tile:f}))}o.sort(va);let l=null,h=1/0;if(s.has(e)){const u=to(e.cached.scene,t);u&&(l=u,h=u.distance*u.distance)}for(let u=0,d=o.length;u<d;u++){const f=o[u],m=f.distance,p=f.tile;if(m>h)break;const v=xa(r,p,t,i);if(v){const g=v.distance*v.distance;g<h&&(l=v,h=g)}}return l}function ya(r,e,t,i,a=null){const{group:s,activeTiles:o}=r,{scene:n,boundingVolume:l}=e.cached;if(r.ensureChildrenArePreprocessed(e),a===null&&(a=ma,Pt.copy(s.matrixWorld).invert(),a.copy(t.ray).applyMatrix4(Pt)),!e.__used||!l.intersectsRay(a))return;o.has(e)&&ga(n,t,i);const h=e.children;for(let u=0,d=h.length;u<d;u++)ya(r,h[u],t,i,a)}let q=new c.Vector3,io=new c.Vector3,pe=[],me=new c.Vector2,Ge=new c.Vector2,$e=new c.Plane,Ae=new c.Vector3,It=new c.Vector3,Lt=new c.Vector3,Rt=new c.Vector3,fi=new c.Vector3,_a=new c.Vector3;class et{constructor(e){this.scene=e}mercator2vector(e,t,i,a=!0){const s=W.mapboxgl.MercatorCoordinate.fromLngLat([e,t],i);let o=new c.Vector3(s.x,s.y,s.z);return o.applyMatrix4(new c.Matrix4().fromArray([4003022888407185e-8,0,0,0,0,0,4003022888407185e-8,0,0,4003022888407185e-8,0,0,-20015114442035925e-9,0,-20015114442035925e-9,1])),a==!0&&(o=this.realPosition(o)),o}vector2mercator(e,t=!0){let i;Array.isArray(e)?i=new c.Vector3(e[0],e[1],e[2]):i=e.clone(),t==!0&&(i=this.scenePosition(i)),i.applyMatrix4(new c.Matrix4().fromArray([24981121214570498e-24,0,0,0,0,-152965250672679e-38,24981121214570498e-24,0,0,24981121214570498e-24,152965250672679e-38,0,.5,.5,0,1]));let a=new W.mapboxgl.MercatorCoordinate(i.x,i.y,i.z),s=a.toLngLat(),o=a.toAltitude();return[s.lng,s.lat,o]}realPosition(e){let t;return Array.isArray(e)?t=new c.Vector3(e[0],e[1],e[2]):t=e.clone(),t.applyMatrix4(N.inverse_RotateMatrix)}scenePosition(e){let t;return Array.isArray(e)?t=new c.Vector3(e[0],e[1],e[2]):t=e.clone(),t.applyMatrix4(N.rotateMatrix)}getBoxRegion(e,t,i){i&&e.applyMatrix4(N.rotateMatrix),e.applyMatrix4(t);let a=this.vector2mercator(e.min,!1),s=this.vector2mercator(e.max,!1),o=Math.min(s[0],a[0]),n=Math.min(s[1],a[1]),l=Math.max(s[0],a[0]),h=Math.max(s[1],a[1]);return{type:"Feature",properties:{},geometry:{type:"Polygon",coordinates:[[[o,n],[l,n],[l,h],[o,h],[o,n]]]}}}getBasePointScale(e,t){return W.mapboxgl.MercatorCoordinate.fromLngLat([e,t],0).meterInMercatorCoordinateUnits()/24981121214570498e-24}getBaseMatrix(e){let t=this.vector2mercator(e.clone()),i=new c.Matrix4;const a=W.mapboxgl.MercatorCoordinate.fromLngLat([t[0],t[1]],t[2]);let s=new c.Vector3(a.x,a.y,a.z);s.applyMatrix4(new c.Matrix4().fromArray([4003022888407185e-8,0,0,0,0,0,4003022888407185e-8,0,0,4003022888407185e-8,0,0,-20015114442035925e-9,0,-20015114442035925e-9,1])),i.makeTranslation(s.x,s.y,s.z);let l=a.meterInMercatorCoordinateUnits()/24981121214570498e-24;return i.multiply(new c.Matrix4().makeScale(l,l,l)),i}getDistance(e,t){return L.distance(e,t,{units:"meters"})}screen2world(e,t=!0){let i,a,s=this.scene.mv.THREE_Camera;Array.isArray(e)?a=new c.Vector2(e[0],e[1]):a=e;let o=this.scene.mv.mainCanvas.getBoundingClientRect(),n=new c.Vector2((a.x-o.left)/o.width,(a.y-o.top)/o.height),l=new c.Vector2(n.x*2-1,-(n.y*2)+1),u=new c.Vector3(l.x,l.y,0).unproject(s);u.sub(s.position).normalize();let d=-s.position.y/u.y;return i=new c.Vector3().copy(s.position).add(u.multiplyScalar(d)),t&&(i=this.realPosition(i)),i}distance(e,t){const i=W.mapboxgl.MercatorCoordinate.fromLngLat(e),a=W.mapboxgl.MercatorCoordinate.fromLngLat(t);return i.toLngLat().distanceTo(a.toLngLat())}defProj4System(e,t){let i=e+t;return e.toLocaleLowerCase()=="cgcs2000"?L.proj4.defs(i,`+title=${i} +proj=tmerc +lat_0=0 +lon_0=${t} +k=1 +x_0=500000 +y_0=0 +a=6378137.0 +b=6356752.314140356 +units=m +no_defs`):e.toLocaleLowerCase()=="xian1980"&&L.proj4.defs(i,`+title=${i} +proj=tmerc +lat_0=0 +lon_0=${t} +k=1 +x_0=500000 +y_0=0 +a=6378140.0 +b=6356755.288157528 +units=m +no_defs`),i}world2screen(e,t=!0){let i;if(Array.isArray(e)?i=new c.Vector3(e[0],e[1],e[2]):i=new c.Vector3().set(e.x,e.y,e.z),t&&(i=this.scene.mv.tools.coordinate.scenePosition(i)),this.scene.mixedMode&&this.scene.mv.mapbox.map.getZoom()<6){let a=this.scene.mv.tools.coordinate.vector2mercator(e,!1),s=this.scene.mv.mapbox.map.project(a);return this.scene.mv.mapbox.map._showingGlobe()&&this.scene.mv.mapboxgl.Lib.isLngLatBehindGlobe(this.scene.mv.mapbox.map.transform,{lng:a[0],lat:a[1]})?new c.Vector2(-1,-1):new c.Vector2(s.x,s.y)}else{let a=this.scene.mv.mainCanvas.getBoundingClientRect(),s=a.width/2,o=a.height/2,n=new c.Vector3().copy(i);return n.project(this.scene.mv.THREE_Camera),new c.Vector2(n.x*s+s+a.left,-(n.y*o)+o+a.top)}}transformPosition(e,t,i,a,s){return q.x=(e[0]+i[0]-t[0])*s[0],q.y=(e[1]+i[1]-t[1])*s[1],q.z=(e[2]+i[2]-t[2])*s[2],q.applyMatrix4(a),pe[0]=q.x+t[0],pe[1]=q.y+t[1],pe[2]=q.z+t[2],pe}transformToOriginPosition(e,t,i,a,s){return q.x=e[0]-t[0],q.y=e[1]-t[1],q.z=e[2]-t[2],q.applyMatrix4(a),pe[0]=q.x/s[0]+t[0]-i[0],pe[1]=q.y/s[1]+t[1]-i[1],pe[2]=q.z/s[2]+t[2]-i[2],pe}transform(e,t,i){let a=this.defProj4System(e,t);return L.proj4(L.proj4(a),L.proj4("EPSG:4326"),i)}getPointInfoInLineArray(e,t,i=.1){let a=t.length-1,s=-1,o=Array.isArray(e)?{x:e[0],y:e[1],z:e[2]}:e,n;for(let l=0;l<a;l++){const h=Array.isArray(t[l])?{x:t[l][0],y:t[l][1],z:t[l][2]}:t[l],u=Array.isArray(t[l+1])?{x:t[l+1][0],y:t[l+1][1],z:t[l+1][2]}:t[l+1];let d=this.computeVerticalPoint(o,h,u);if(d&&d.distance<i){s=l+1,n=d.foot;break}}return{linePoint:n,index:s}}computeVerticalPoint(e,t,i){const a={x:i.x-t.x,y:i.y-t.y,z:i.z-t.z},s={x:e.x-t.x,y:e.y-t.y,z:e.z-t.z},o=s.x*a.x+s.y*a.y+s.z*a.z,n=a.x*a.x+a.y*a.y+a.z*a.z;let l=o/n;if(l<0||l>1)return;const h=[t.x+l*a.x,t.y+l*a.y,t.z+l*a.z];let u=Math.sqrt((e.x-h[0])**2+(e.y-h[1])**2+(e.z-h[2])**2);return{foot:h,distance:u}}distancePointToLine3D(e,t,i){const a=i.map((n,l)=>(e[l]-t[l])*n).reduce((n,l)=>n+l,0)/i.map((n,l)=>n*n).reduce((n,l)=>n+l,0),s=i.map((n,l)=>t[l]+a*n);return Math.sqrt(e.map((n,l)=>(n-s[l])**2).reduce((n,l)=>n+l,0))}isInPolygon(e,t,i=0,a=t.length){let s=!1;me.set(0,0),Ge.set(0,0);for(let o=i;o<a;o++)o==i?(me.copy(t[i]),Ge.copy(t[a-1])):(me.copy(t[o]),Ge.copy(t[o-1])),me.x>e.x!=Ge.x>e.x&&e.y<(Ge.y-me.y)*(e.x-me.x)/(Ge.x-me.x)+me.y&&(s=!s);return s}prjPointToPlane(e,t,i){let a=t.normal.dot(e)+t.constant,s=q.copy(e).add(io.copy(t.normal).multiplyScalar(-a)),o=new c.Vector2(s.x,s.z);return i==1?o.set(s.y,s.z):i==2&&o.set(s.x,s.y),o}crossProduct(e,t,i,a){let s=t.x-e.x,o=t.z-e.z,n=i.x-t.x,l=i.z-t.z;a==1?(s=t.y-e.y,o=t.z-e.z,n=i.y-t.y,l=i.z-t.z):a==2&&(s=t.x-e.x,o=t.y-e.y,n=i.x-t.x,l=i.y-t.y);let h=s*l-o*n;return h>0?1:h<0?-1:0}isConvex(e){$e.setFromCoplanarPoints(e[0],e[1],e[2]);let t=0;$e.normal.x==0&&$e.normal.z==0?t=0:$e.normal.x==0?t=2:$e.normal.y==0&&(t=1);let i,a=e.length,s=!1,o=!1;for(let n=2;n<a+2;n++)i=this.crossProduct(e[(n-2)%a],e[(n-1)%a],e[n%a],t),i==1?o=!0:i==-1&&(s=!0);return!(o&&s||!o&&!s)}isSegmentsIntersecting(e,t,i,a){Ae.subVectors(t,e),It.subVectors(a,i),Lt.subVectors(i,e),Rt.crossVectors(Ae,It),fi.crossVectors(Lt,It),_a.crossVectors(Lt,Ae);let s=Rt.lengthSq();if(s===0){if(fi.lengthSq()===0){let l=Lt.dot(Ae)/Ae.lengthSq(),h=l+It.dot(Ae)/Ae.lengthSq();if(l>=0&&l<=1||h>=0&&h<=1||l<=0&&h>=1||l>=1&&h<=0)return!0}return!1}let o=fi.dot(Rt)/s,n=_a.dot(Rt)/s;return o>=0&&o<=1&&n>=0&&n<=1}}const ao=`
uniform float size;
uniform float scale;

#include <common>
#include <color_pars_vertex>
#include <fog_pars_vertex>
#include <morphtarget_pars_vertex>
#include <logdepthbuf_pars_vertex>
#include <clipping_planes_pars_vertex>
#include <mv/clip_par_vertex>

#ifdef USE_POINTS_UV

	varying vec2 vUv;
	uniform mat3 uvTransform;

#endif

void main() {

	#ifdef USE_POINTS_UV

		vUv = ( uvTransform * vec3( uv, 1 ) ).xy;

	#endif

	#include <color_vertex>
	#include <morphcolor_vertex>
	#include <begin_vertex>
	#include <morphtarget_vertex>
	#include <project_vertex>

	gl_PointSize = size;

	#ifdef USE_SIZEATTENUATION

		bool isPerspective = isPerspectiveMatrix( projectionMatrix );

		if ( isPerspective ) gl_PointSize *= ( scale / - mvPosition.z );

	#endif

	#include <logdepthbuf_vertex>
	#include <clipping_planes_vertex>
	#include <worldpos_vertex>
	#include <fog_vertex>
    #include <mv/clip_vertex>
}
`,ro=`
uniform float renderBoundary;
uniform int rollDirection;
uniform vec3 diffuse;
uniform float opacity;
uniform float lightIntensity;

#include <common>
#include <color_pars_fragment>
#include <map_particle_pars_fragment>
#include <alphatest_pars_fragment>
#include <fog_pars_fragment>
#include <logdepthbuf_pars_fragment>
#include <clipping_planes_pars_fragment>
#include <mv/clip_par_fragment>
void main() {

    if(rollDirection==1&&gl_FragCoord.x>renderBoundary){
        discard;
    }
    if(rollDirection==2&&gl_FragCoord.y<renderBoundary){
        discard;
    }

	#include <clipping_planes_fragment>

    #include <mv/clip_fragment>

	vec3 outgoingLight = vec3( 0.0 );
	vec4 diffuseColor = vec4( diffuse, opacity );

	#include <logdepthbuf_fragment>
	#include <map_particle_fragment>
	#include <color_fragment>
	#include <alphatest_fragment>

	outgoingLight = diffuseColor.rgb;
    outgoingLight*=lightIntensity;

	#include <output_fragment>
	#include <tonemapping_fragment>
	#include <encodings_fragment>
	#include <fog_fragment>
	#include <premultiplied_alpha_fragment>

}
`;class so extends c.ShaderMaterial{constructor(e={}){let t={vertexShader:ao,fragmentShader:ro,uniforms:{diffuse:{value:new c.Color(16777215)},map:{value:null},alphaMap:{value:null},size:{value:1},sizeAttenuation:{value:!0},rollDirection:{value:1},opacity:{value:1},renderBoundary:{value:99999},scale:{value:1},lightIntensity:{value:1}}};t={...t,...e},super(t),this.type="mvPointMaterial";let i=this.uniforms;i.u_clipPoints={value:[]},i.u_basePoint={value:new c.Vector2},i.u_clipPointIndex={value:[]},i.u_clipAltitude={value:[]}}get color(){return this.uniforms.diffuse.value}set color(e){this.uniforms.diffuse.value=e}get map(){return this.uniforms.map.value}set map(e){this.uniforms.map.value=e}get alphaMap(){return this.uniforms.alphaMap.value}set alphaMap(e){this.uniforms.alphaMap.value=e}get size(){return this.uniforms.size.value}set size(e){this.uniforms.size.value=e}get sizeAttenuation(){return this.uniforms.sizeAttenuation.value}set sizeAttenuation(e){this.uniforms.sizeAttenuation.value=e}get scale(){return this.uniforms.scale.value}set scale(e){this.uniforms.scale.value=e}copy(e){return this.vertexColors=e.vertexColors,this.color=e.color,this.map=e.map,this.alphaMap=e.alphaMap,this.size=e.size,this.sizeAttenuation=e.sizeAttenuation,this}}class ba{get transform(){return this._transform}constructor(e=new c.Box3,t=new c.Matrix4){this.box=e.clone(),this._transform=t.clone(),this.inverseTransform=new c.Matrix4,this.points=new Array(8).fill().map(()=>new c.Vector3),this.coordinateTransform=new et,this.mecatorPoints=new Array(8).fill().map(()=>new c.Vector3),this.originBox=e.clone(),this.center=new c.Vector3}update(){const{points:e,inverseTransform:t,transform:i,box:a}=this;t.copy(i).invert();const{min:s,max:o}=a;let n=0;for(let l=-1;l<=1;l+=2)for(let h=-1;h<=1;h+=2)for(let u=-1;u<=1;u+=2)e[n].set(l<0?s.x:o.x,h<0?s.y:o.y,u<0?s.z:o.z).applyMatrix4(i),this.center.add(e[n]),n++;this.center.divideScalar(8),this.transformToMecator()}transformToMecator(){const{points:e,inverseTransform:t,transform:i,box:a}=this;t.copy(i).invert();const{min:s,max:o}=a;s.applyMatrix4(i),o.applyMatrix4(i),this.originBox.setFromPoints([s,o]);let n=L.Ellipsoid.WGS84.cartesianToCartographic(s.toArray(),new L.Vector3),l=this.coordinateTransform.mercator2vector(...n,!1),h=L.Ellipsoid.WGS84.cartesianToCartographic(o.toArray(),new L.Vector3),u=this.coordinateTransform.mercator2vector(...h,!1);this.box.setFromPoints([l,u]);for(let d=0;d<e.length;d++){let f=L.Ellipsoid.WGS84.cartesianToCartographic(e[d].toArray(),new L.Vector3),m=this.coordinateTransform.mercator2vector(...f,!1);this.mecatorPoints[d].copy(m)}i.identity(),t.identity()}intersectsFrustum(e){const{mecatorPoints:t}=this;let i=t;const{planes:a}=e;for(let s=0;s<6;s++){const o=a[s];let n=-1/0;for(let l=0;l<8;l++){const h=i[l],u=o.distanceToPoint(h);n=n<u?u:n}if(n<0)return!1}return!0}}new c.Spherical,new c.Vector3;function oo(r){const{x:e,y:t,z:i}=r;r.x=i,r.y=e,r.z=t}function no(r){return-r+Math.PI/2}const Ma=new c.Spherical,ve=new c.Vector3,K=new c.Vector3,pi=new c.Vector3,lo=new c.Vector3,wa=new c.Vector3,mi=new c.Vector3,vi=new c.Vector3,Sa=new c.Vector3,co=1e-12,ho=.1;class uo{constructor(e=1,t=1,i=1){this.radius=new c.Vector3(e,t,i)}constructLatLonFrame(e,t,i){return this.getCartographicToPosition(e,t,0,Sa),this.getCartographicToNormal(e,t,vi),this.getNorthernTangent(e,t,mi),wa.crossVectors(mi,vi),i.makeBasis(wa,mi,vi).setPosition(Sa)}getNorthernTangent(e,t,i,a=lo){let s=1,o=e+1e-7;e>Math.PI/4&&(s=-1,o=e-1e-7);const n=this.getCartographicToNormal(e,t,K).normalize(),l=this.getCartographicToNormal(o,t,pi).normalize();return a.crossVectors(n,l).normalize().multiplyScalar(s),i.crossVectors(a,n).normalize()}getCartographicToPosition(e,t,i,a){this.getCartographicToNormal(e,t,ve);const s=this.radius;K.copy(ve),K.x*=s.x**2,K.y*=s.y**2,K.z*=s.z**2;const o=Math.sqrt(ve.dot(K));return K.divideScalar(o),a.copy(K).addScaledVector(ve,i)}getPositionToCartographic(e,t){this.getPositionToSurfacePoint(e,K),this.getPositionToNormal(e,ve);const i=pi.subVectors(e,K);return t.lon=Math.atan2(ve.y,ve.x),t.lat=Math.asin(ve.z),t.height=Math.sign(i.dot(e))*i.length(),t}getCartographicToNormal(e,t,i){return Ma.set(1,no(e),t),i.setFromSpherical(Ma).normalize(),oo(i),i}getPositionToNormal(e,t){const i=this.radius;return t.copy(e),t.x/=i.x**2,t.y/=i.y**2,t.z/=i.z**2,t.normalize(),t}getPositionToSurfacePoint(e,t){const i=this.radius,a=1/i.x**2,s=1/i.y**2,o=1/i.z**2,n=e.x*e.x*a,l=e.y*e.y*s,h=e.z*e.z*o,u=n+l+h,d=Math.sqrt(1/u),f=K.copy(e).multiplyScalar(d);if(u<ho)return isFinite(d)?t.copy(f):null;const m=pi.set(f.x*a*2,f.y*s*2,f.z*o*2);let p=(1-d)*e.length()/(.5*m.length()),v=0,g,x,_,y,M,b,w,T,I,S,R;do{p-=v,_=1/(1+p*a),y=1/(1+p*s),M=1/(1+p*o),b=_*_,w=y*y,T=M*M,I=b*_,S=w*y,R=T*M,g=n*b+l*w+h*T-1,x=n*I*a+l*S*s+h*R*o;const V=-2*x;v=g/V}while(Math.abs(g)>co);return t.set(e.x*_,e.y*y,e.z*M)}}const ge=Math.PI,Ct=ge/2,tt=new c.Vector3,We=new c.Vector3,je=new c.Vector3,Ta=new c.Matrix4;let it=0;const gi=[];function fo(r=!1){return r?(gi[it]||(gi[it]=new c.Vector3),it++,gi[it-1]):new c.Vector3}function Aa(){it=0}class po extends uo{constructor(e,t,i,a=-Ct,s=Ct,o=0,n=2*ge,l=0,h=0){super(e,t,i),this.latStart=a,this.latEnd=s,this.lonStart=o,this.lonEnd=n,this.heightStart=l,this.heightEnd=h}_getPoints(e=!1){const{latStart:t,latEnd:i,lonStart:a,lonEnd:s,heightStart:o,heightEnd:n}=this,l=c.MathUtils.mapLinear(.5,0,1,t,i),h=c.MathUtils.mapLinear(.5,0,1,a,s),u=Math.floor(a/Ct)*Ct,d=[[-ge/2,0],[ge/2,0],[0,u],[0,u+ge/2],[0,u+ge],[0,u+3*ge/2],[t,s],[i,s],[t,a],[i,a],[0,a],[0,s],[l,h],[t,h],[i,h],[l,a],[l,s]],f=[],m=d.length;for(let p=0;p<=1;p++){const v=c.MathUtils.mapLinear(p,0,1,o,n);for(let g=0,x=m;g<x;g++){const[_,y]=d[g];if(_>=t&&_<=i&&y>=a&&y<=s){const M=fo(e);f.push(M),this.getCartographicToPosition(_,y,v,M)}}}return f}getBoundingBox(e,t){Aa();const{latStart:i,latEnd:a,lonStart:s,lonEnd:o}=this;if(a-i<ge/2){const h=c.MathUtils.mapLinear(.5,0,1,i,a),u=c.MathUtils.mapLinear(.5,0,1,s,o);this.getCartographicToNormal(h,u,je),We.set(0,0,1),tt.crossVectors(We,je),We.crossVectors(tt,je),t.makeBasis(tt,We,je)}else tt.set(1,0,0),We.set(0,1,0),je.set(0,0,1),t.makeBasis(tt,We,je);Ta.copy(t).invert();const l=this._getPoints(!0);for(let h=0,u=l.length;h<u;h++)l[h].applyMatrix4(Ta);e.makeEmpty(),e.setFromPoints(l)}getBoundingSphere(e,t){Aa();const i=this._getPoints(!0);e.makeEmpty(),e.setFromPoints(i,t)}}const le=new c.Vector3,ce=new c.Vector3,he=new c.Vector3,Pa=new c.Vector3,Ea=new c.Vector3,Ia=new c.Vector3,qe=new c.Ray,Bt=new c.Vector3;class mo{constructor(){this.sphere=null,this.obb=null,this.region=null,this.regionObb=null,this.coordinateTransform=new et}intersectsRay(e){const t=this.sphere,i=this.obb||this.regionObb;return!(t&&!e.intersectsSphere(t)||i&&(qe.copy(e).applyMatrix4(i.inverseTransform),!qe.intersectsBox(i.box)))}intersectRay(e,t=null){const i=this.sphere,a=this.obb||this.regionObb;let s=-1/0,o=-1/0;i&&e.intersectSphere(i,Ea)&&(s=i.containsPoint(e.origin)?0:e.origin.distanceToSquared(Ea)),a&&(qe.copy(e).applyMatrix4(a.inverseTransform),qe.intersectBox(a.box,Ia)&&(o=a.box.containsPoint(qe.origin)?0:qe.origin.distanceToSquared(Ia)));const n=Math.max(s,o);return n===-1/0?null:(e.at(Math.sqrt(n),t),t)}distanceToPoint(e){const t=this.sphere,i=this.obb||this.regionObb;let a=-1/0,s=-1/0;return t&&(a=Math.max(t.distanceToPoint(e),0)),i&&(Pa.copy(e).applyMatrix4(i.inverseTransform),s=i.box.distanceToPoint(Pa)),a>s?a:s}intersectsFrustum(e){const t=this.obb||this.regionObb,i=this.sphere;return i&&!e.intersectsSphere(i)||t&&!t.intersectsFrustum(e)?!1:!!(i||t)}getOBB(e,t){const i=this.obb||this.regionObb;i?(e.copy(i.box),t.copy(i.transform)):(this.getAABB(e),t.identity())}getAABB(e){if(this.sphere)this.sphere.getBoundingBox(e);else{const t=this.obb||this.regionObb;e.copy(t.box).applyMatrix4(t.transform)}}getSphere(e){if(this.sphere)e.copy(this.sphere);else if(this.region)this.region.getBoundingSphere(e),this.transformSphereToMecatorCoord(e);else{const t=this.obb||this.regionObb;t.box.getBoundingSphere(e),e.applyMatrix4(t.transform)}}setObbData(e,t){const i=new ba;le.set(e[3],e[4],e[5]),ce.set(e[6],e[7],e[8]),he.set(e[9],e[10],e[11]);const a=le.length(),s=ce.length(),o=he.length();le.normalize(),ce.normalize(),he.normalize(),a===0&&le.crossVectors(ce,he),s===0&&ce.crossVectors(le,he),o===0&&he.crossVectors(le,ce),i.transform.set(le.x,ce.x,he.x,e[0],le.y,ce.y,he.y,e[1],le.z,ce.z,he.z,e[2],0,0,0,1).premultiply(t),i.box.min.set(-a,-s,-o),i.box.max.set(a,s,o),i.update(),this.obb=i}setSphereData(e,t,i,a,s){const o=new c.Sphere;o.center.set(e,t,i),o.radius=a,o.applyMatrix4(s),this.sphere=o,this.transformSphereToMecatorCoord(o),o.originCenter=o.center.clone()}transform(e,t){this.sphere&&(Bt.copy(this.sphere.originCenter),Bt.applyMatrix4(e),Bt.applyMatrix4(t),this.sphere.center.copy(Bt));let i=this.obb||this.regionObb;if(i){for(let u=0;u<i.points.length;u++){let d=L.Ellipsoid.WGS84.cartesianToCartographic(i.points[u].toArray(),new L.Vector3),f=this.coordinateTransform.mercator2vector(...d,!1);f.applyMatrix4(e),f.applyMatrix4(t),i.mecatorPoints[u].copy(f)}let a=i.originBox.min,s=i.originBox.max,o=L.Ellipsoid.WGS84.cartesianToCartographic(a.toArray(),new L.Vector3),n=this.coordinateTransform.mercator2vector(...o,!1);n.applyMatrix4(e),n.applyMatrix4(t);let l=L.Ellipsoid.WGS84.cartesianToCartographic(s.toArray(),new L.Vector3),h=this.coordinateTransform.mercator2vector(...l,!1);h.applyMatrix4(e),h.applyMatrix4(t),i.box.setFromPoints([n,h])}}transformSphereToMecatorCoord(e){let t=L.Ellipsoid.WGS84.cartesianToCartographic(e.center,new L.Vector3);(t==null||e.radius>=1e6)&&(t=[0,0,0]);let i=this.coordinateTransform.getBasePointScale(t[0],t[1]);e.radius*=i,e.center=this.coordinateTransform.mercator2vector(t[0],t[1],t[2],!1)}setRegionData(e,t,i,a,s,o){const n=new po(bt,bt,js,t,a,e,i,s,o),l=new ba;n.getBoundingBox(l.box,l.transform),l.update(),this.region=n,this.regionObb=l}}const La=Symbol("INITIAL_FRUSTUM_CULLED"),Dt=new c.Matrix4,xi=new c.Matrix4,Pe=new c.Vector3,vo=new c.Vector3(1,0,0),go=new c.Vector3(0,1,0);function Ra(r,e){r.traverse(t=>{t.frustumCulled=t[La]&&e})}class xo extends Xs{get autoDisableRendererCulling(){return this._autoDisableRendererCulling}set autoDisableRendererCulling(e){this._autoDisableRendererCulling!==e&&(super._autoDisableRendererCulling=e,this.forEachLoadedModel(t=>{Ra(t,!e)}))}constructor(...e){super(...e),this.group=new eo(this),this.cameras=[],this.cameraMap=new Map,this.cameraInfo=[],this.activeTiles=new Set,this.visibleTiles=new Set,this._autoDisableRendererCulling=!0,this.optimizeRaycast=!0,this.onLoadTileSet=null,this.onLoadModel=null,this.onDisposeModel=null,this.onTileVisibilityChange=null;const t=new c.LoadingManager;t.setURLModifier(a=>this.preprocessURL?this.preprocessURL(a):a),this.manager=t;const i=this;this._overridenRaycast=function(a,s){i.optimizeRaycast||Object.getPrototypeOf(this).raycast.call(this,a,s)}}getBounds(e){if(!this.root)return!1;const t=this.root.cached.boundingVolume;return t&&t.getAABB(e),!0}getOrientedBounds(e,t){if(!this.root)return!1;const i=this.root.cached.boundingVolume;return i&&i.getOBB(e,t),!0}getBoundingSphere(e){if(!this.root)return!1;const t=this.root.cached.boundingVolume;return t?(t.getSphere(e),!0):!1}forEachLoadedModel(e){this.traverse(t=>{const i=t.cached.scene;i&&e(i,t)})}raycast(e,t){if(this.root)if(e.firstHitOnly){const i=xa(this,this.root,e);i&&t.push(i)}else ya(this,this.root,e,t)}hasCamera(e){return this.cameraMap.has(e)}setCamera(e){const t=this.cameras,i=this.cameraMap;return i.has(e)?!1:(i.set(e,new c.Vector2),t.push(e),!0)}setResolution(e,t,i){const a=this.cameraMap;return a.has(e)?(t instanceof c.Vector2?a.get(e).copy(t):a.get(e).set(t,i),!0):!1}setResolutionFromRenderer(e,t){const i=this.cameraMap;if(!i.has(e))return!1;const a=i.get(e);return t.getSize(a),a.multiplyScalar(t.getPixelRatio()),!0}deleteCamera(e){const t=this.cameras,i=this.cameraMap;if(i.has(e)){const a=t.indexOf(e);return t.splice(a,1),i.delete(e),!0}return!1}fetchTileSet(e,...t){const i=super.fetchTileSet(e,...t);return i.then(a=>{this.onLoadTileSet&&Promise.resolve().then(()=>{this.onLoadTileSet(a,e)})}),i}update(){const e=this.group,t=this.cameras,i=this.cameraMap,a=this.cameraInfo;if(t.length===0){console.warn("TilesRenderer: no cameras defined. Cannot update 3d tiles.");return}for(;a.length>t.length;)a.pop();for(;a.length<t.length;)a.push({frustum:new c.Frustum,isOrthographic:!1,sseDenominator:-1,position:new c.Vector3,invScale:-1,pixelSize:0});xi.copy(e.matrixWorld).invert(),Pe.setFromMatrixScale(xi);const s=Pe.x;Math.abs(Math.max(Pe.x-Pe.y,Pe.x-Pe.z))>1e-6&&console.warn("ThreeTilesRenderer : Non uniform scale used for tile which may cause issues when calculating screen space error. ",Pe.toArray());for(let o=0,n=a.length;o<n;o++){const l=t[o],h=a[o],u=h.frustum,d=h.position,f=i.get(l);(f.width===0||f.height===0)&&console.warn("TilesRenderer: resolution for camera error calculation is not set.");const m=l.projectionMatrix.elements;if(h.isOrthographic=m[15]===1,h.isOrthographic){const p=2/m[0],v=2/m[5];h.pixelSize=Math.max(v/f.height,p/f.width)}else h.sseDenominator=2/m[5]/f.height;h.invScale=s,Dt.copy(e.matrixWorld),Dt.premultiply(l.matrixWorldInverse),Dt.premultiply(l.projectionMatrix),u.setFromProjectionMatrix(Dt),d.set(0,0,0),d.applyMatrix4(l.matrixWorld),d.applyMatrix4(xi)}this.updateFrustumAndInfoPosition(),super.update(),this.tile_cache.scheduleUnload()}preprocessNode(e,t,i=null){super.preprocessNode(e,t,i);const a=new c.Matrix4;if(e.transform){const n=e.transform;for(let l=0;l<16;l++)a.elements[l]=n[l]}else a.identity();i&&a.premultiply(i.cached.transform);const s=new c.Matrix4().copy(a).invert(),o=new mo;"sphere"in e.boundingVolume&&(e.boundingVolume.sphere[3]>1e6&&(e.isDiscard=!0),o.setSphereData(...e.boundingVolume.sphere,a)),"box"in e.boundingVolume&&o.setObbData(e.boundingVolume.box,a),"region"in e.boundingVolume&&o.setRegionData(...e.boundingVolume.region),e.cached={loadIndex:0,transform:a,transformInverse:s,active:!1,inFrustum:[],boundingVolume:o,scene:null,geometry:null,material:null}}parseTile(e,t,i){t._loadIndex=t._loadIndex||0,t._loadIndex++;const s=t.content.uri.split(/[\\\/]/g);s.pop();const o=s.join("/"),n=this.fetchOptions,l=this.manager,h=t._loadIndex;let u=null;const d=this.rootTileSet.asset&&this.rootTileSet.asset.gltfUpAxis||"y",f=t.cached,m=f.transform,p=new c.Matrix4;switch(d.toLowerCase()){case"x":p.makeRotationAxis(go,-Math.PI/2);break;case"y":p.makeRotationAxis(vo,Math.PI/2);break;case"z":p.identity();break}const v=(He(e)||i).toLowerCase();switch(v){case"b3dm":{const x=new oi(l);x.workingPath=o,x.fetchOptions=n,x.adjustmentTransform.copy(p),u=x.parse(e);break}case"pnts":{const x=new ni(l);x.workingPath=o,x.fetchOptions=n,u=x.parse(e);break}case"i3dm":{const x=new ui(l);x.workingPath=o,x.fetchOptions=n,x.adjustmentTransform.copy(p),u=x.parse(e);break}case"cmpt":{const x=new fa(l);x.workingPath=o,x.fetchOptions=n,x.adjustmentTransform.copy(p),u=x.parse(e).then(_=>_.scene);break}case"gltf":case"glb":const g=new pa(l);g.workingPath=o,g.fetchOptions=n,u=g.parse(e);break;default:console.warn(`TilesRenderer: Content type "${v}" not supported.`),u=Promise.resolve(null);break}return u.then(g=>{let x,_;if(g.isObject3D?(x=g,_=null):(x=g.scene,_=g),t._loadIndex!==h)return;x.updateMatrix(),(v==="glb"||v==="gltf")&&x.matrix.multiply(p),x.matrix.premultiply(m),x.matrix.decompose(x.position,x.quaternion,x.scale),x.traverse(w=>{w[La]=w.frustumCulled}),Ra(x,!this.autoDisableRendererCulling),x.traverse(w=>{w.raycast=this._overridenRaycast});const y=[],M=[],b=[];x.traverse(w=>{if(w.geometry&&M.push(w.geometry),w.material){const T=w.material;y.push(w.material);for(const I in T){const S=T[I];S&&S.isTexture&&b.push(S)}}}),f.materials=y,f.geometry=M,f.textures=b,f.scene=x,f.metadata=_,this.onLoadModel&&this.onLoadModel(x,t)})}disposeTile(e){const t=e.cached;if(t.scene){const i=t.materials,a=t.geometry,s=t.textures,o=t.scene.parent;for(let n=0,l=a.length;n<l;n++)a[n].dispose(),this.mvFeature.current_tiles_primitive_count--;for(let n=0,l=i.length;n<l;n++)i[n].dispose();for(let n=0,l=s.length;n<l;n++)s[n].dispose();o&&o.remove(t.scene),this.onDisposeModel&&this.onDisposeModel(t.scene,e),t.scene=null,t.materials=null,t.textures=null,t.geometry=null,t.metadata=null}this.activeTiles.delete(e),this.visibleTiles.delete(e),e._loadIndex++}setTileVisible(e,t){const i=e.cached.scene,a=this.visibleTiles,s=this.group;t?(i&&(s.add(i),i.updateMatrixWorld(!0)),a.add(e)):(s.remove(i),a.delete(e)),this.onTileVisibilityChange&&this.onTileVisibilityChange(i,e,t)}setTileActive(e,t){const i=this.activeTiles;t?i.add(e):i.delete(e)}create_sprite(e){const t=document.createElement("canvas"),i=t.getContext("2d");t.width=50,t.height=30,i.fillStyle="#000000",i.fillRect(0,0,t.width,t.height),i.font="32 Arial",i.fillStyle="red",i.textAlign="left",i.fillText(""+e.__depth,15,20,600);const a=new THREE.CanvasTexture(t),s=new THREE.SpriteMaterial({map:a,depthTest:!1}),o=new THREE.Sprite(s),n=e.cached.sphere.center;return o.position.set(n.x,n.y+10,n.z),o.scale.set(5,5,5),o}get_box_wireframe(e){let t=e.cached.boundingVolume.obb.mecatorPoints,i=new THREE.Vector3().copy(t[0]);const a=new THREE.MeshBasicMaterial({color:new THREE.Color(1,0,0),side:THREE.DoubleSide}),s=new THREE.BufferGeometry;let o=[];t.forEach(l=>{o.push(new THREE.Vector3().copy(l).sub(i))}),s.setFromPoints(o),s.setIndex([0,1,2,1,2,3,4,6,7,4,7,5,2,6,7,2,7,3,0,4,5,0,5,1]);let n=new THREE.Mesh(s,a);return n.position.copy(i),n}calculateError(e){const t=e.cached,i=t.inFrustum,a=this.cameras,s=this.cameraInfo,o=t.boundingVolume;let n=-1/0,l=1/0;for(let h=0,u=a.length;h<u;h++){if(!i[h])continue;const d=s[h],f=d.invScale;let m;if(d.isOrthographic){const p=d.pixelSize;m=e.geometricError/(p*f)}else{const v=o.distanceToPoint(d.position)*f,g=d.sseDenominator;m=e.geometricError/(v*g),l=Math.min(l,v)}n=Math.max(n,m)}e.__distanceFromCamera=l,e.__error=n}tileInView(e){const t=e.cached,i=t.boundingVolume,a=t.inFrustum,s=this.cameraInfo;let o=!1;for(let n=0,l=s.length;n<l;n++){const h=s[n].frustum;i.intersectsFrustum(h)?(o=!0,a[n]=!0):a[n]=!1}return o}dispose(){super.dispose(),Ca(this.root,this)}}function Ca(r,e){r.cached&&r.cached.scene?e.disposeTile(r):r.children&&r.children.forEach(t=>{Ca(t,e)})}class yo{constructor(e){this.tile_renderer=e,this.tiles=[],this.tile_map=new Map,this.threshold_tile_number=50}add_tile(e){this.tile_map.has(e.id)||(this.tile_map.set(e.id,e),this.tiles.push(e))}scheduleUnload(){if(this.tile_renderer.activeTiles.forEach(i=>{i.timestamp=Date.now()}),this.tile_renderer.mvFeature.current_tiles_primitive_count<this.tile_renderer.mvFeature.threshold_primitive_count)return;const e=this.tiles.filter(i=>i.__loadingState==ke&&!this.tile_renderer.tileInView(i)),t=this.tile_renderer.mvScene.mv.THREE_Camera.position;this.tiles.forEach(i=>i.distance=i.cached.sphere.center.distanceTo(t)-i.cached.sphere.radius),e.sort((i,a)=>-(i.distance-a.distance));for(let i=0,a=e.length-this.threshold_tile_number;i<a;i++){const s=e[i];s.delay_factor=s.delay_factor||1;const o=6e3*s.delay_factor;if(Date.now()-s.timestamp>o&&(s.allow_unload=!0,s.unload_count=0,s.delay_factor++),!(s.allow_unload==!1||Date.now()-s.timestamp<3e3)&&(s.allow_unload=!0,this.tile_renderer.disposeTile(s),s.__loadingState=0,Date.now()-s.timestamp<1e4&&(s.unload_count=s.unload_count==null?1:s.unload_count+=1,s.timestamp=Date.now()),s.unload_count>=2&&(s.allow_unload=!1),this.tile_renderer.mvFeature.current_tiles_primitive_count<this.tile_renderer.mvFeature.threshold_primitive_count))break}}}const _o=c.MathUtils.DEG2RAD,bo=Symbol("INITIAL_FRUSTUM_CULLED"),Ee=new c.Matrix4,Mo=new c.Vector3(1,0,0),wo=new c.Vector3(0,1,0);class So extends xo{constructor(...e){super(...e),this.allTile=new Map,this.coordinateTransform=new et,this.geo_scale=1,this.tile_cache=new yo(this)}preprocessNode(e,t,i){super.preprocessNode(e,t,i);let a=new c.Sphere;e.cached.boundingVolume.getSphere(a),a.originCenter=a.center.clone();let s=this.coordinateTransform.vector2mercator(a.center,!1),o=a.center;o.applyMatrix4(this.accurateInvertBpt),o.applyMatrix4(this.featureBpt),e.cached.boundingVolume.transform(this.accurateInvertBpt,this.featureBpt),e.cached.absolute=!!e.transform,e.cached.sphere=a,e.cached.cartoCenter=s,e.cached.isRegion=!!e.boundingVolume.region,e.id==null&&(e.id=Date.now()+Math.random()),this.allTile.has(e.id)||this.allTile.set(e.id,e)}parseTile(e,t,i,a){t._loadIndex=t._loadIndex||0,t._loadIndex++;const o=t.content.uri.split(/[\\\/]/g);o.pop();const n=o.join("/"),l=this.fetchOptions,h=this.manager,u=t._loadIndex;let d=null;switch(i){case"b3dm":const f=new oi(h);f.workingPath=n,f.fetchOptions=l,d=f.parse(e).then(m=>m.scene);break;case"pnts":{const m=new ni(h);m.fetchOptions=l,d=m.parse(e,a||void 0).then(p=>p.scene);break}case"i3dm":{const m=new ui(h);m.workingPath=n,m.fetchOptions=l,d=m.parse(e).then(p=>p.scene);break}case"cmpt":{const m=new fa(h);m.workingPath=n,m.fetchOptions=l,d=m.parse(e).then(p=>p.scene);break}case"gltf":case"glb":{const m=new pa(h);m.workingPath=n,m.fetchOptions=l,d=m.parse(e).then(p=>p.scene);break}default:console.warn(`TilesRenderer: Content type "${i}" not supported.`),d=Promise.resolve(null);break}return d.then(f=>{if(t._loadIndex!==u)return;const m=this.rootTileSet.asset&&this.rootTileSet.asset.gltfUpAxis||"y",p=t.cached;let v=p.transform;switch(m.toLowerCase()){case"x":Ee.makeRotationAxis(wo,-Math.PI/2);break;case"y":Ee.makeRotationAxis(Mo,Math.PI/2);break;case"z":Ee.identity();break}let g=!1;f.updateMatrix(),f.userData.base=f.position.clone();let x=new c.Vector3().copy(f.position);x.applyMatrix4(v);let _;p.isRegion?_=p.cartoCenter:(_=L.Ellipsoid.WGS84.cartesianToCartographic(x,new L.Vector3),_==null&&(_=p.cartoCenter,g=!0),_&&(f.userData.originCartoCenter=[_[0],_[1],_[2]]));let y=this.coordinateTransform.mercator2vector(_[0],_[1],_[2],!1),M=new c.Matrix4().makeTranslation(y.x,y.y,y.z);if(this.absoluteCheck==null&&Math.abs(v.elements[12])>1e5&&(this.absoluteCoord=!1),g)this.transformGeometryBaseOnMesh(f),f.position.set(0,0,0),f.quaternion.set(0,0,0,1),f.scale.set(1,1,1),f.applyMatrix4(Ee),f.updateMatrix();else if(this.absoluteCoord){let S=new c.Matrix4().makeTranslation(x.x,x.y,x.z);S.multiply(Ee),this.reComputeGeometry(S,f),f.position.set(0,0,0),f.quaternion.set(0,0,0,1),f.scale.set(1,1,1),f.updateMatrix()}else{f.position.set(0,0,0),f.updateMatrix();let S=new c.Matrix4().makeRotationX(-Math.PI/2);this.rootTileSet.asset&&this.rootTileSet.asset.gltfUpAxis&&this.rootTileSet.asset.gltfUpAxis.toLowerCase()=="y"&&this.changeCloudPointsYZ(f),f.matrix.multiply(Ee).multiply(S),f.userData.originMatrix=f.matrix.clone(),f.matrix.premultiply(M),f.matrix.decompose(f.position,f.quaternion,f.scale),f.position.applyMatrix4(this.accurateInvertBpt)}f.traverse(S=>{if(S[bo]=S.frustumCulled,S instanceof c.Mesh){S.feature=this.mvFeature;let R=Zi.createMaterial(ii.meshbasic,{color:S.material.color.clone()});R.uniforms.lightIntensity.value=this.mvFeature.lightIntensity;let V=S.material.map;R.map=V,R.uniforms.map.value=V,S.material=R,S.userData.gltfUpAxis=this.mvFeature.gltfUpAxis,S.material.userData.tile=t,S.frustumCulled=!1,S.material.map&&(S.material.map.onUpdate=function(){S.material.map.img=null,S.material.map.source.data=null},S.material.userData.create_time=Date.now())}else if(S instanceof c.Points){S.feature=this.mvFeature;let R=new so;R.uniforms.lightIntensity.value=this.mvFeature.lightIntensity,R.copy(S.material),R.originColor=S.material.color,S.material=R,S.userData.gltfUpAxis=this.mvFeature.gltfUpAxis}}),window.mv_not_render=!0,this.mvFeature.parent.mv.THREE_Renderer.render(f,this.mvFeature.parent.mv.THREE_Camera),window.mv_not_render=!1,p.scene=f,this.mvFeature.current_tiles_primitive_count==null&&(this.mvFeature.current_tiles_primitive_count=0);const b=[],w=[],T=[];let I=0;f.traverse(S=>{if(S.frustumCulled=!0,S.geometry&&(w.push(S.geometry),I++),S.material){let R=S.material;b.push(S.material);for(const V in R){const z=R[V];z&&z.isTexture&&T.push(z)}}}),Ba(t,t.parent),t.is_ceil==!1?(this.tile_cache.add_tile(t),t.timestamp=Date.now(),this.mvFeature.current_tiles_primitive_count+=I):(window.base_tile==null&&(window.base_tile=[]),window.base_tile.push(t)),p.materials=b,p.geometry=w,p.textures=T,this.onLoadModel&&this.onLoadModel(f,t)})}setTileVisible(e,t){if(super.setTileVisible(e,t),t){const i=e.cached.scene;this.updateSceneDataCondition(i)}}reComputeGeometry(e,t){if(t instanceof c.Mesh||t instanceof c.Points){let i=t.geometry.attributes.position.array,a=[],s=t.geometry.attributes.position.data?t.geometry.attributes.position.data.stride:3,o=t instanceof c.Mesh;for(let n=0;n<i.length;n+=s){let l=new c.Vector3(i[n],i[n+1],i[n+2]);l.applyMatrix4(t.matrix),o&&l.applyMatrix4(e);let h=L.Ellipsoid.WGS84.cartesianToCartographic([l.x,l.y,l.z],new L.Vector3),u=this.coordinateTransform.mercator2vector(h[0],h[1],h[2],!1);u.applyMatrix4(this.accurateInvertBpt),a.push(u.x,u.y,u.z)}t.position.set(0,0,0),t.quaternion.set(0,0,0,1),t.scale.set(1,1,1),t.updateMatrixWorld(),t.geometry.setAttribute("position",new c.Float32BufferAttribute(a,3)),t.geometry.computeBoundingSphere(),t.geometry.computeBoundingBox()}else t.children.forEach(i=>{this.reComputeGeometry(e,i)})}transformGeometryBaseOnMesh(e){if(e instanceof c.Mesh){let t=e.geometry.attributes.position.array,i=[],a=e.geometry.attributes.position.data?e.geometry.attributes.position.data.stride:3;e.updateMatrixWorld();for(let s=0;s<t.length;s+=a){let o=new c.Vector3(t[s],t[s+1],t[s+2]);o.applyMatrix4(e.matrixWorld);let n=L.Ellipsoid.WGS84.cartesianToCartographic([o.x,o.y,o.z],new L.Vector3),l=this.coordinateTransform.mercator2vector(n[0],n[1],n[2],!1);l.applyMatrix4(this.accurateInvertBpt),i.push(l.x,l.y,l.z)}e.position.set(0,0,0),e.quaternion.set(0,0,0,1),e.scale.set(1,1,1),e.updateMatrixWorld(),e.geometry.setAttribute("position",new c.Float32BufferAttribute(i,3)),e.geometry.computeBoundingSphere(),e.geometry.computeBoundingBox()}else e.children.forEach(t=>{this.transformGeometryBaseOnMesh(t)})}changeCloudPointsYZ(e){if(e instanceof c.Points){let t=e.geometry.attributes.position.array,i=[],a=e.geometry.attributes.position.data?e.geometry.attributes.position.data.stride:3;for(let s=0;s<t.length;s+=a)i.push(t[s],t[s+2],-t[s+1]);e.geometry.setAttribute("position",new c.Float32BufferAttribute(i,3)),e.geometry.computeBoundingSphere(),e.geometry.computeBoundingBox()}else e.children.forEach(t=>{this.changeCloudPointsYZ(t)})}updateSceneDataCondition(e){this.mvScene.mv.mapbox.map.painter.context.tileFlatten&&this.mvScene.mv.mapbox.map.painter.context.tileFlatten.update();let t=this;e!=null&&e.traverse(i=>{i.material&&i.material.uniforms&&(i.material.uniforms.rollDirection.value=t.mvFeature.parent.config.rollerDirection=="H"?1:2,t.mvFeature.roller?i.material.uniforms.renderBoundary.value=Q.RENDERBOUNDARY:i.material.uniforms.renderBoundary.value=t.mvFeature.parent.config.rollerDirection=="H"?999999:0,i.material.uniforms.opacity.value=t.mvFeature.data.opacity,i.material.transparent=!(t.mvFeature.data.opacity>=1),i.userData.gltfUpAxis=t.mvFeature.gltfUpAxis)})}cameraDistanceSort(e){const t=this.cameraInfo;let i=null;if(t.length==1)t[0].frustum,i=t[0].position;else return e;for(var a=0;a<e.length;a++){const s=e[a],n=s.cached.sphere;if(n){const l=n.center;let h=i.distanceTo(l);s.PB_cameraDistance=h}}return e.sort(function(s,o){return o.PB_cameraDistance-s.PB_cameraDistance}),e}updateFrustumAndInfoPosition(){const e=this.cameras,t=this.cameraInfo,i=this.cameraMap;for(let a=0;a<t.length;a++){const s=t[a],o=e[a],n=s.frustum,l=s.position;n.setFromProjectionMatrix(Ee.multiplyMatrices(o.projectionMatrix,o.matrixWorldInverse)),l.set(o.position.x,o.position.y,o.position.z);const h=i.get(o),u=o.projectionMatrix.elements;s.isOrthographic=u[15]===1,s.isOrthographic==!1&&(s.sseDenominator=2*Math.tan(.5*o.fov*_o)/h.height)}}}const To=/(b3dm|i3dm|pnts|cmpt|gltf|glb)$/i;function Ba(r,e){e==null||e.content==null?r.is_ceil=!0:To.test(e.content.uri)?r.is_ceil=!1:Ba(r,e.parent)}class Ao{constructor(){}createCommonMaterial_old(){let e=`
            varying vec2 vUv;
			varying vec4 pos;
            uniform mat4 matrixWorld;
			void main()
			{
				pos=matrixWorld* vec4( position, 1.0 );
                
				vUv = uv;
				vec4 mvPosition = modelViewMatrix * vec4( position, 1.0 );
				gl_Position = projectionMatrix * mvPosition;
			}
        `,t=`
        varying vec2 vUv;
        varying vec4 pos;
        uniform mediump vec2 u_clipPoints[200];
        uniform sampler2D colorTexture;
        uniform float bottom;
        bool contains(vec3 pos);
        void main(void) {

           if(contains(pos.xyz)){//&&pos.z>=40.0 //&&pos.y>=bottom
                discard;
             }
            vec3 color = texture2D( colorTexture, vUv ).rgb;
            gl_FragColor=vec4(color,1.0);
        }

        bool contains(vec3 pos){
            vec2 pt=pos.xz;
            const int maxPointCount=200;
            bool c=false;

            mediump vec2 lastPoint=vec2(0,0);
            for(int i=0;i<maxPointCount;i++){                
            mediump vec2 iPoint=vec2(0.0);
            mediump vec2 jPoint=vec2(0.0);
            float value=0.5;
            if(i%20==0){
                iPoint=u_clipPoints[i];jPoint=u_clipPoints[i+19];
            }else if(i%20==19){
                iPoint=lastPoint;jPoint=u_clipPoints[i];
            }else{
                iPoint=u_clipPoints[i];jPoint=u_clipPoints[i-1];
            }

            if(iPoint.x<1.0){
                continue;
            }else{
                lastPoint=iPoint;
            }
            float dx=mod(float(i),20.0);
            if(dx==19.0){
               lastPoint=vec2(0.0,0.0);
            }
            
            if(((iPoint.x>pt.x)!=(jPoint.x>pt.x))&&(pt.y < (jPoint.y - iPoint.y) * (pt.x - iPoint.x) / (jPoint.x - iPoint.x) + iPoint.y))
                {c=!c;}

                if(dx==19.0&&c==true){
                    if(pos.y<u_clipPoints[i-1].y){
                        c=false;
                    }else{
                        return c;
                    }
                }

            }
            return c;
        }
        `,i=[];for(let o=0;o<200;o++)i.push(new c.Vector2);let a={matrixWorld:{value:new c.Matrix4},u_clipPoints:{value:i},colorTexture:{value:new c.Texture},bottom:{value:0}};return new c.ShaderMaterial({uniforms:a,vertexShader:e,fragmentShader:t,side:c.DoubleSide})}createCommonMaterial(){let e=Po(c.ShaderLib.basic.uniforms);e.diffuse.value=new c.Color(1,1,1);let t=`#include <common>
        #include <uv_pars_vertex>
        #include <uv2_pars_vertex>
        #include <envmap_pars_vertex>
        #include <color_pars_vertex>
        #include <fog_pars_vertex>
        #include <morphtarget_pars_vertex>
        #include <skinning_pars_vertex>
        #include <logdepthbuf_pars_vertex>
        #include <clipping_planes_pars_vertex>

        varying vec4 pos;
        uniform mat4 matrixWorld;
        
        void main() {
        
            #include <uv_vertex>
            #include <uv2_vertex>
            #include <color_vertex>
            #include <skinbase_vertex>
        
            #ifdef USE_ENVMAP
        
            #include <beginnormal_vertex>
            #include <morphnormal_vertex>
            #include <skinnormal_vertex>
            #include <defaultnormal_vertex>
        
            #endif
        
            #include <begin_vertex>
            #include <morphtarget_vertex>
            #include <skinning_vertex>
            #include <project_vertex>
            #include <logdepthbuf_vertex>
        
            #include <worldpos_vertex>
            #include <clipping_planes_vertex>
            #include <envmap_vertex>
            #include <fog_vertex>

            pos=matrixWorld* vec4( position, 1.0 );               
        
        }`,i=`
        uniform vec3 diffuse;
uniform float opacity;

#ifndef FLAT_SHADED

	varying vec3 vNormal;

#endif

#include <common>
#include <color_pars_fragment>
#include <uv_pars_fragment>
#include <uv2_pars_fragment>
#include <map_pars_fragment>
#include <alphamap_pars_fragment>
#include <aomap_pars_fragment>
#include <lightmap_pars_fragment>
#include <envmap_common_pars_fragment>
#include <envmap_pars_fragment>
#include <cube_uv_reflection_fragment>
#include <fog_pars_fragment>
#include <specularmap_pars_fragment>
#include <logdepthbuf_pars_fragment>
#include <clipping_planes_pars_fragment>

varying vec4 pos;
        uniform mediump vec2 u_clipPoints[200];
        uniform sampler2D colorTexture;
        uniform float bottom;
        bool contains(vec3 pos);

void main() {

    if(contains(pos.xyz)){
        discard;
    }

	#include <clipping_planes_fragment>

	vec4 diffuseColor = vec4( diffuse, opacity );

	#include <logdepthbuf_fragment>
	#include <map_fragment>
	#include <color_fragment>
	#include <alphamap_fragment>
	#include <alphatest_fragment>
	#include <specularmap_fragment>

	ReflectedLight reflectedLight = ReflectedLight( vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ) );

	// accumulation (baked indirect lighting only)
	#ifdef USE_LIGHTMAP
	
		vec4 lightMapTexel= texture2D( lightMap, vUv2 );
		reflectedLight.indirectDiffuse += lightMapTexelToLinear( lightMapTexel ).rgb * lightMapIntensity;

	#else

		reflectedLight.indirectDiffuse += vec3( 1.0 );

	#endif

	// modulation
	#include <aomap_fragment>

	reflectedLight.indirectDiffuse *= diffuseColor.rgb;

	vec3 outgoingLight = reflectedLight.indirectDiffuse;

	#include <envmap_fragment>

	gl_FragColor = vec4( outgoingLight, diffuseColor.a );

	#include <premultiplied_alpha_fragment>
	#include <tonemapping_fragment>
	#include <encodings_fragment>
	#include <fog_fragment>

}

bool contains(vec3 pos){
    vec2 pt=pos.xz;
    const int maxPointCount=200;
    bool c=false;

    mediump vec2 lastPoint=vec2(0,0);
    for(int i=0;i<maxPointCount;i++){                
    mediump vec2 iPoint=vec2(0.0);
    mediump vec2 jPoint=vec2(0.0);
    float value=0.5;
    if(i%20==0){
        iPoint=u_clipPoints[i];jPoint=u_clipPoints[i+19];
    }else if(i%20==19){
        iPoint=lastPoint;jPoint=u_clipPoints[i];
    }else{
        iPoint=u_clipPoints[i];jPoint=u_clipPoints[i-1];
    }

    if(iPoint.x<1.0){
        continue;
    }else{
        lastPoint=iPoint;
    }
    float dx=mod(float(i),20.0);
    if(dx==19.0){
       lastPoint=vec2(0.0,0.0);
    }
    
    if(((iPoint.x>pt.x)!=(jPoint.x>pt.x))&&(pt.y < (jPoint.y - iPoint.y) * (pt.x - iPoint.x) / (jPoint.x - iPoint.x) + iPoint.y))
        {c=!c;}

        if(dx==19.0&&c==true){
            if(pos.y<u_clipPoints[i-1].y){
                c=false;
            }else{
                return c;
            }
        }

    }
    return c;
}

        `,a=[];for(let o=0;o<200;o++)a.push(new c.Vector2);return e.matrixWorld={value:new c.Matrix4},e.u_clipPoints={value:a},new c.ShaderMaterial({uniforms:e,vertexShader:t,fragmentShader:i,side:c.DoubleSide})}test(e){for(const t of e.allMeshMap){let i=t[1],a=this.createCommonMaterial(),s=i.material.map;i.material=a,a.uniforms.colorTexture.value=s,a.uniforms.matrixWorld.value=i.matrixWorld,a.uniforms.u_clipPoints.value[0]=new c.Vector2(12616921520841908e-9,-2611725692008741e-9),a.uniforms.u_clipPoints.value[1]=new c.Vector2(12616921520841908e-9+200,-2611725692008741e-9),a.uniforms.u_clipPoints.value[2]=new c.Vector2(12616921520841908e-9+200,-2611725692008741e-9+200),a.uniforms.u_clipPoints.value[19]=new c.Vector2(12616921520841908e-9,-2611725692008741e-9+200),a.userData.clipMode=!0}}update(e){let t=3.3;setInterval(()=>{for(const i of e.allMeshMap)t-=.01,i[1].material.uniforms.bottom.value=t},1e3)}}function Po(r){var e={};for(var t in r){e[t]={};for(var i in r[t]){var a=r[t][i];a&&(a.isColor||a.isMatrix3||a.isMatrix4||a.isVector2||a.isVector3||a.isVector4||a.isTexture)?e[t][i]=a.clone():Array.isArray(a)?e[t][i]=a.slice():e[t][i]=a}}return e}let Ut=new c.Vector3;class yi extends $t{constructor(e,t){super(),this.includePoints=!1,this.maxDepth=60,this.loadSiblings=!0,this.stopAtEmptyTiles=!0,this.displayActiveTiles=!1,this.box=new Z,this.allMeshMap=new Map,this.isReady=!1,this.allScene=new Map,this.isSetOrigin=!1,this.isSetAltitude=!1,this.accurateBpt=new c.Matrix4,this.accurateInvertBpt=new c.Matrix4,this._bottom=0,this._excavation=!1,this._excavationRange=[],this.gltfUpAxis="y",this._lightIntensity=1,this.threshold_primitive_count=500,this.id=e,this.url=t,this.type=fe._3dTiles,this.lib=ft.THREE,this.forceRepaint=!0,this._customMaterial=new Ao}get data(){return super.data.opacity==null&&(super.data.opacity=1),super.data.errorTarget==null&&(super.data.errorTarget=15),super.data.errorThreshold==null&&(super.data.errorThreshold=60),super.data.includePoints==null&&(super.data.includePoints=!1,this.includePoints=!1),super.data.pntsSize==null&&(super.data.pntsSize=2),super.data}set data(e){if(e&&e.opacity){super.data.opacity=e.opacity>1?1:e.opacity;let t=this.data.opacity<1,i=this.data.opacity;for(const a of this.allMeshMap)a[1].material.transparent=t,a[1].material.uniforms.opacity.value=i,a[1].material.needsUpdate=!0}e&&e.errorTarget&&(super.data.errorTarget=e.errorTarget),e&&e.errorThreshold&&(super.data.errorThreshold=e.errorThreshold),e&&e.pntsSize&&(super.data.pntsSize=e.pntsSize,this.updateAllPointMaterialSize(super.data.pntsSize)),e&&this.data.pntColor!=e.pntColor&&(e.pntColor?this.pntColor=new c.Color(e.pntColor):this.pntColor=void 0,super.data.pntColor=e.pntColor,this.updateAllPointMaterialColor(this.pntColor)),e&&e.includePoints!=null&&(super.data.includePoints=e.includePoints,this.includePoints=e.includePoints)}updateAllPointMaterialSize(e){this.allScene.forEach(t=>{let i=t;i.type==="Points"&&i.material&&i.material.type==="mvPointMaterial"&&(i.material.size=e)})}updateAllPointMaterialColor(e){this.allScene.forEach(t=>{let i=t;if(i.type==="Points"&&i.material&&i.material.type==="mvPointMaterial"){let a=!0;e?(i.material.color=e,a=!1):i.material.color=i.material.originColor,i.geometry.attributes.color&&(i.material.vertexColors=a,i.material.needsUpdate=!0)}})}get origin(){return super.origin}set origin(e){if(this.loaded==!1){this._tmpOrigin=e;return}super.origin=e}get altitude(){return super.altitude}set altitude(e){if(this.loaded==!1){this._tmpAltitude=e;return}super.altitude=e}get offset(){return super.offset}set offset(e){if(this.loaded==!1){this._tmpOffset=e;return}super.offset=e}get scale(){return super.scale}set scale(e){if(this.loaded==!1){this._tmpScale=e;return}super.scale=e}get rotation(){return super.rotation}set rotation(e){if(this.loaded==!1){this._tmpRotation=e;return}super.rotation=e}get bottom(){return this._bottom}set bottom(e){this._bottom=e,this.updateMaterial(),this.updateExcavationRange(),this.updateExcavationAltitude()}get excavation(){return this._excavation}set excavation(e){this._excavation=e,this.updateMaterial(),this.updateExcavationRange(),this.updateExcavationAltitude()}get excavationRange(){return this._excavationRange}set excavationRange(e){this._excavationRange=e,this.updateMaterial(),this.updateExcavationRange(),this.updateExcavationAltitude()}get lightIntensity(){return this._lightIntensity}set lightIntensity(e){(e<0||e>=10)&&(e=1),this._lightIntensity=e,this.data.includePoints||this.allMeshMap.size==0?this.allScene.forEach(t=>{t.material.uniforms.lightIntensity&&(t.material.uniforms.lightIntensity.value=e)}):this.allMeshMap.forEach(t=>{t.material.uniforms.lightIntensity&&(t.material.uniforms.lightIntensity.value=e)}),this.parent.forceRepaint=!0}get visible(){return super.visible}set visible(e){this.forceRepaint=e,super.visible=e}onTileLoad(e,t){this.allScene.set(e.uuid,e),e.type=="Points"&&(e.material.originColor=e.material.color.clone(),this.pntColor&&(e.material.color=this.pntColor,e.geometry.attributes.color&&(e.material.vertexColors=!1,e.material.needsUpdate=!0)),e.material.size=this.data.pntsSize);let i=[];this.searchMesh(e,i),i.forEach(a=>{this.allMeshMap.set(a.uuid,a),a.material.transparent=this.data.opacity<1,a.material.opacity=this.data.opacity}),this.excavation&&(this.updateMaterial(),this.updateExcavationRange(),this.updateExcavationAltitude())}onDisposeTile(e,t){this.allScene.delete(e.uuid);let i=[];this.searchMesh(e,i),i.forEach(a=>{this.allMeshMap.delete(a.uuid),a.geometry.dispose(),a.material.dispose()})}searchMesh(e,t){e instanceof c.Mesh?t.push(e):e.children.forEach(i=>{this.searchMesh(i,t)})}load(e){return new Promise((t,i)=>{if(this.url)if(super.loaded==!1){this.container.name="Tile_"+this.id,this.tiles&&(this.container.remove(this.tiles.group),this.tiles.dispose()),this.tiles=new So(this.url),this.tiles.onLoadModel=this.onTileLoad.bind(this),this.tiles.onDisposeModel=this.onDisposeTile.bind(this),this.tiles.lruCache.maxSize=2e5,this.tiles.lruCache.mv_feature=this;const a=new F.DRACOLoader(this.tiles.manager);a.setDecoderPath("./js/draco/");const s=new F.GLTFLoader(this.tiles.manager);s.setDRACOLoader(a);const o=new F.KTX2Loader().setTranscoderPath("./js/basis/").detectSupport(this.parent.mv.THREE_Renderer);s.setKTX2Loader(o),this.tiles.fetchOptions.mode="cors",this.tiles.manager.addHandler(/\.gltf$/,s),this.tiles.manager.addHandler(/draco\.drc/i,a),this.container.add(this.tiles.group),this.tiles.parseQueue.autoUpdate=!0,this.tiles.downloadQueue.autoUpdate=!0,this.tiles.parseQueue.scheduleJobRun(),this.tiles.downloadQueue.scheduleJobRun(),this.getTileLayerBaseInfo(this.url).then(n=>{let l=n.jsonArray[0],h=l.root;this.gltfUpAxis=l.asset.gltfUpAxis==null?"y":l.asset.gltfUpAxis;let u=h.transform,d=new c.Matrix4,f=!1;u==null?f=!0:(d.fromArray(u),Math.abs(d.elements[12])<1e4&&(f=!0));let{box:m,sphere:p,cartographicCenter:v}=this.computeRegionInfo(h,d);this.box=m,this.sphere=p,this.accurateCoordinate=Array.from(v),this.accuratePosition=this.parent.mv.tools.coordinate.mercator2vector(v[0],v[1],v[2],!1),this.computeAccurateBptInfo(),this.origin=[v[0],v[1]],this.altitude=v[2],this.parent.mv.events.featureInited.emit("default"),this.container.applyMatrix4(this.bpt),this.container.updateMatrixWorld(),this.AABB=this.box.clone().applyMatrix4(new c.Matrix4().copy(this.bpt).invert()),this._tmpOrigin&&(this.origin=this._tmpOrigin),this._tmpAltitude!=null&&(this.altitude=this._tmpAltitude),this._tmpOffset&&(this.offset=this._tmpOffset),this._tmpRotation!=null&&(this.rotation=this._tmpRotation),this._tmpScale&&(this.scale=this._tmpScale),this.tiles.absoluteCoord=f,this.isReady=!0,this.tiles.mvScene=this.parent,this.tiles.mvFeature=this,this.needUpdate=!0,t(!0)}),super.load(e),this.tiles.parseQueue.mvStatus=this.parent.mv.status,this.tiles.downloadQueue.mvStatus=this.parent.mv.status,this.parent.mv.events.featureLoaded.emit("default",{featureID:this.id})}else t(!0);else console.error("%c【Multiverse】:url can not be null","color:YellowGreen"),t(!1)})}computeRegionInfo(e,t){let i,a=new c.Sphere,s;if("box"in e.boundingVolume){let l=null;const h=e.boundingVolume.box;i=new Z,l=new c.Matrix4;let u=new c.Vector3(h[3],h[4],h[5]),d=new c.Vector3(h[6],h[7],h[8]),f=new c.Vector3(h[9],h[10],h[11]);const m=u.length(),p=d.length(),v=f.length();u.normalize(),d.normalize(),f.normalize(),l.set(u.x,d.x,f.x,h[0],u.y,d.y,f.y,h[1],u.z,d.z,f.z,h[2],0,0,0,1),l.premultiply(t),i.min.set(-m,-p,-v),i.max.set(m,p,v),i.getBoundingSphere(a),a.center.set(h[0],h[1],h[2]),a.applyMatrix4(t),s=L.Ellipsoid.WGS84.cartesianToCartographic([a.center.x,a.center.y,a.center.z],new L.Vector3)}else if("sphere"in e.boundingVolume){const l=e.boundingVolume.sphere;a.center.set(l[0],l[1],l[2]),a.radius=l[3],a.applyMatrix4(t),s=L.Ellipsoid.WGS84.cartesianToCartographic([a.center.x,a.center.y,a.center.z],new L.Vector3)}else if("region"in e.boundingVolume){const l=e.boundingVolume.region;let h=l[5]-l[4],u=this.parent.mv.tools.coordinate.getDistance([l[0]/Math.PI*180,l[1]/Math.PI*180],[l[2]/Math.PI*180,l[3]/Math.PI*180]);a.radius=h>u?h/2:u/2,s=[180*(l[0]+l[2])/(2*Math.PI),180*(l[1]+l[3])/(2*Math.PI),(l[4]+l[5])/2]}let o=this.parent.mv.tools.coordinate.mercator2vector(s[0],s[1],s[2],!1),n=this.parent.mv.tools.coordinate.getBasePointScale(s[0],s[1]);return a.center.copy(o),a.radius*=n,i=new Z,a.getBoundingBox(i),{box:i,sphere:a,cartographicCenter:s}}update(){if(this.isReady==!1)return;if(this.tiles.errorThreshold=this.data.errorThreshold,this.tiles.loadSiblings=this.loadSiblings,this.tiles.stopAtEmptyTiles=this.stopAtEmptyTiles,this.tiles.displayActiveTiles=this.displayActiveTiles,this.tiles.maxDepth=this.maxDepth,this.tiles.geo_scale=this.bpt.elements[0],this.needUpdate){this.container.position.set(0,0,0),this.container.quaternion.set(0,0,0,1),this.container.scale.set(1,1,1),this.container.updateMatrixWorld(),this.container.applyMatrix4(this.bpt),this.container.updateMatrixWorld();let t=this.bpt.clone();if(this.rotation){let i=new c.Matrix4;if(Array.isArray(this.rotation)==!1)i.makeRotationY(Math.PI/(180/this.rotation));else{let s=new c.Matrix4;s.makeRotationX(Math.PI/(180/this.rotation[0])),i.multiply(s),s=new c.Matrix4,s.makeRotationZ(Math.PI/(180/this.rotation[1])),i.multiply(s),s=new c.Matrix4,s.makeRotationY(Math.PI/(180/this.rotation[2])),i.multiply(s)}let a=new c.Matrix4().copy(i).invert();t.multiply(a)}this.tiles.accuratePosition=this.accuratePosition,this.tiles.accurateInvertBpt=this.accurateInvertBpt,this.tiles.featureBpt=this.bpt,this.updateTileCenter(this.tiles.root),this.tiles.root&&this.tiles.root.cached&&(this.tiles.root.cached.sphere.getBoundingBox(this.box),this.sphere=this.tiles.root.cached.sphere),this.region2d=this.parent.mv.tools.coordinate.getBoxRegion(this.box,new c.Matrix4,!1)}let e=this.parent.mv.mapbox.map.getZoom();e>17?(this.tiles.errorTarget=this.data.errorTarget-(e-17),this.tiles.errorTarget=this.tiles.errorTarget<=0?0:this.tiles.errorTarget):this.tiles.errorTarget=this.data.errorThreshold,this.tiles.setCamera(this.parent.mv.THREE_Camera),this.tiles.setResolutionFromRenderer(this.parent.mv.THREE_Camera,this.parent.mv.THREE_Renderer),this.tiles.update(),super.update()}getTileLayerBaseInfo(e){return new Promise((t,i)=>{fetch(e).then(a=>a.json()).then(a=>{t({jsonArray:[a]})})})}dispose(){this.forceRepaint=!1,this.tiles.dispose(),super.dispose()}computeAccurateBptInfo(){let e=this.parent.mv.tools.coordinate.getBasePointScale(this.accurateCoordinate[0],this.accurateCoordinate[1]);this.accurateBpt.makeTranslation(this.accuratePosition.x,this.accuratePosition.y,this.accuratePosition.z),this.accurateBpt.multiply(new c.Matrix4().makeScale(e,e,e)),this.accurateInvertBpt.copy(this.accurateBpt).invert()}updateTileCenter(e){e!=null&&(e.cached&&e.cached.sphere.center&&(Ut.copy(e.cached.sphere.originCenter),Ut.applyMatrix4(this.accurateInvertBpt),Ut.applyMatrix4(this.bpt),e.cached.sphere.center.copy(Ut),e.cached.boundingVolume.transform(this.accurateInvertBpt,this.bpt)),e.children&&e.children.forEach(t=>{this.updateTileCenter(t)}))}updateMaterial(){if(this.excavation)for(const e of this.allMeshMap){let t=e[1];if(t.userData.oldMaterial==null&&(t.userData.oldMaterial=t.material),t.material.userData.clipMode==!0)continue;let i=this._customMaterial.createCommonMaterial(),a=t.material.map;t.material=i,i.uniforms.colorTexture.value=a,i.uniforms.matrixWorld.value=t.matrixWorld,i.userData.clipMode=!0}else for(const e of this.allMeshMap){let t=e[1];t.userData.oldMaterial!==void 0&&(t.material=t.userData.oldMaterial)}}updateExcavationAltitude(){if(this.excavation!=!1)for(const e of this.allMeshMap)e[1].material.uniforms.bottom.value=this._bottom}updateExcavationRange(){if(this.excavation!=!1)for(const e of this.allMeshMap){let t=e[1],i=t.material;i.uniforms.matrixWorld.value=t.matrixWorld;for(let a=0;a<this.excavationRange.length;a++){const s=this.excavationRange[a];let o=s.length<20?s.length:20;for(let l=0;l<o;l++){let h=new c.Vector3(s[l][0],s[l][1],0);h=this.parent.mv.tools.coordinate.scenePosition(h),i.uniforms.u_clipPoints.value[l+a*20]=new c.Vector2(h.x,h.z)}let n=new c.Vector3(s[s.length-1][0],s[s.length-1][1],0);n=this.parent.mv.tools.coordinate.scenePosition(n),i.uniforms.u_clipPoints.value[19+a*20]=new c.Vector2(n.x,n.z)}i.userData.clipMode=!0}}updateRange(){let e=this.roller==!0?Q.RENDERBOUNDARY:this.parent.config.rollerDirection=="H"?999999:0,t=this.parent.config.rollerDirection=="H"?1:2;for(const i of this.allMeshMap)i[1].material.uniforms.renderBoundary.value=e,i[1].material.uniforms.rollDirection.value=t}}class Eo{constructor(){}static convertMeshIdToColor(e){let t=127,i=16129,a=2048383,s=0,o=0,n=0,l=e;return l<=t?(s=0,o=0,n=l):l<i?(s=0,o=(l-l%127)/127,n=l%127):l<a&&(n=l%127,s=(l-l%i)/i,o=(l-n-s*i)/127),[s*2/255,o*2/255,n*2/255]}static addMeshIdToAttribute(e){let t=127,i=16129,a=2048383,s=e.geometry;if(s.attributes.position==null)return;let o=s.attributes.position.count,n=0,l=0,h=0,u=e.id;u<=t?(n=0,l=0,h=u):u<i?(n=0,l=(u-u%127)/127,h=u%127):u<a&&(h=u%127,n=(u-u%i)/i,l=(u-h-n*i)/127);let d=new Int8Array(o*3),f=[n,l,h];for(let m=0;m<o;m++)d.set(f,m*3);s.setAttribute("meshId",new c.Int8BufferAttribute(d,3))}}new c.Vector2;class Da{constructor(e){this.mvScene=e,this.mvScene.mv.events.polygonChange.on("default",(t,i)=>{this.update(t,i),this.mvScene.render(!0)})}update(e,t=!1){let i=[],a=[];if(e){a.push(this.mvScene.features.get(e));for(const n of this.mvScene.features)n[1]instanceof yi&&i.push(n[1])}else for(const n of this.mvScene.features)n[1]instanceof yi?i.push(n[1]):n[1]instanceof Oa&&a.push(n[1]);a.forEach(n=>{n.loaded!=!1&&i.forEach(l=>{if(n.visible==!1||n.tileFlatten==!1||n.data==null)this.resetMeshHeight(l.container,n.id);else{if(l.loaded==!1||l.region2d==null||n.region2d==null)return;L.intersect(l.region2d,n.region2d)!=null&&this.modifyMesh(l.container,n,t)}})});let s=[],o=[];a.forEach(n=>{if(!n.loaded||!n.visible||n.region2d==null||n.data.positions==null)return;let l=[];n.data.positions.forEach(h=>{let u=this.mvScene.mv.tools.coordinate.scenePosition(new c.Vector3(h[0],h[1],0));l.push(new c.Vector2(u.x,u.z))}),s.push(l),o.push(n)}),i.forEach(n=>{this.modifyMeshExcavationInfo(n.container,o,s)})}modifyMeshExcavationInfo(e,t,i){if(e instanceof c.Mesh||e instanceof c.Points){e.userData.region2d==null&&(e.geometry.boundingBox==null&&e.geometry.computeBoundingBox(),e.userData.region2d=this.mvScene.mv.tools.coordinate.getBoxRegion(e.geometry.boundingBox.clone(),e.matrixWorld,!1));let a=[];for(let o=0;o<t.length;o++){const n=t[o];n.tileExcavation==!1||L.intersect(n.region2d,e.userData.region2d)==null||a.push({positions:i[o],altitude:n.topY})}let s=e.material;if(a.length>0){let o=e.matrixWorld.elements[12],n=e.matrixWorld.elements[14];s.uniforms.u_basePoint.value.set(o,n);let l=[],h=[],u=[],d=0;for(let f=0;f<a.length;f++){const m=a[f].positions;for(let p=0;p<m.length;p++)l.push(new c.Vector2(m[p].x-o,m[p].y-n));h.push(d,l.length),d+=l.length,u.push(a[f].altitude)}s.uniforms.u_clipPoints.value=l,s.uniforms.u_clipPointIndex.value=h,s.uniforms.u_clipAltitude.value=u,s.defines.CLIP=l.length,s.defines.CLIP_START_END=h.length,s.defines.CLIP_ALTITUDE=u.length,s.needsUpdate=!0}else s.defines.CLIP&&(delete s.defines.CLIP,s.needsUpdate=!0)}else e.children.forEach(a=>{this.modifyMeshExcavationInfo(a,t,i)})}resetMeshHeightByPolygon(e){this.mvScene.features.forEach(t=>{t.type==fe._3dTiles&&t.allScene.forEach((i,a)=>{this.resetMeshHeight(i,e)})})}resetMeshHeight(e,t){if(e!=null)if(e instanceof c.Mesh||e instanceof c.Points){if(e.userData.featureid_vertexY_Map==null||e.userData.featureid_vertexY_Map.has(t)==!1)return;let i=e.userData.featureid_vertexY_Map.get(t);if(e.geometry.attributes.position instanceof c.Float32BufferAttribute){let a=e.geometry.attributes.position.array;for(const s in i)Object.prototype.hasOwnProperty.call(i,s)&&(a[s]=i[s]);e.geometry.setAttribute("position",new c.Float32BufferAttribute(a,3,!1))}else if(e.geometry.attributes.position instanceof c.InterleavedBufferAttribute){let s=e.geometry.attributes.position.data.array;for(const n in i)Object.prototype.hasOwnProperty.call(i,n)&&(s[n]=i[n]);const o=new c.InterleavedBuffer(s,5);e.geometry.setAttribute("position",new c.InterleavedBufferAttribute(o,3,0,!1)),e.geometry.setAttribute("uv",new c.InterleavedBufferAttribute(o,2,3,!1))}e.geometry.computeBoundingBox(),i=void 0,e.userData.featureid_vertexY_Map.delete(t)}else e.children.forEach(i=>{this.resetMeshHeight(i,t)})}modifyMesh(e,t,i){if(e instanceof c.Mesh||e instanceof c.Points){if(i==!1&&e.userData.featureid_vertexY_Map&&e.userData.featureid_vertexY_Map.has(t.id)||(i&&this.resetMeshHeight(e,t.id),e.userData.region2d==null&&(e.geometry.boundingBox==null&&e.geometry.computeBoundingBox(),e.userData.region2d=this.mvScene.mv.tools.coordinate.getBoxRegion(e.geometry.boundingBox.clone(),e.matrixWorld,!1)),L.intersect(t.region2d,e.userData.region2d)==null))return;e.userData.featureid_vertexY_Map==null&&(e.userData.featureid_vertexY_Map=new Map);let s=new c.Vector3;e.updateMatrixWorld(),s.applyMatrix4(e.matrixWorld);let o=new c.Matrix4().copy(e.matrixWorld).invert(),n=this.mvScene.mv.mapbox.map.painter.context.flattenExvacationCore.correctValue,l;l=new c.Vector3(s.x,t.data.top-n,s.z);let h=this.mvScene.mv.tools.coordinate.vector2mercator(l,!1);l=this.mvScene.mv.tools.coordinate.mercator2vector(h[0],h[1],t.data.top-n,!1),l.applyMatrix4(o);let u=[],d=t.data.positions.length;Math.abs(t.data.positions[0][0]-t.data.positions[d-1][0])<=1e-4&&Math.abs(t.data.positions[0][1]-t.data.positions[d-1][1])<=1e-4&&(d-=1);let f=0,m=0;for(let _=0;_<d;_++){const y=t.data.positions[_];let M=new c.Vector3(y[0],y[1],0);if(M=this.mvScene.mv.tools.coordinate.scenePosition(M),M.applyMatrix4(o),u.push(M),_>0){let b=Math.abs(u[_].y-u[_-1].y),w=Math.abs(u[_].z-u[_-1].z);b>f&&(f=b),w>m&&(m=w)}}let p=1,v=2,g="z";f<m&&(u.forEach(_=>{_.y=_.z}),p=2,v=1,g="y");let x=e.userData.featureid_vertexY_Map.get(t.id);if(x==null&&(x={},e.userData.featureid_vertexY_Map.set(t.id,x)),e.geometry.attributes.position instanceof c.Float32BufferAttribute||e.geometry.attributes.position instanceof c.BufferAttribute){let _=e.geometry.attributes.position.array;for(let y=0;y<_.length;y+=3)this.isContain(u,{x:_[y],y:_[y+p]})&&(x[y+v]==null&&(x[y+v]=_[y+v]),_[y+v]=l[g]);e.geometry.setAttribute("position",new c.Float32BufferAttribute(_,3,!1))}else if(e.geometry.attributes.position instanceof c.InterleavedBufferAttribute){let _=e.geometry.attributes.position.data,y=e.geometry.attributes.position.offset,M=_.array;for(let w=0;w<M.length;w+=_.stride)this.isContain(u,{x:M[w+y],y:M[w+p+y]})&&(x[w+v]==null&&(x[w+v]=M[w+v]),M[w+v+y]=l[g]);const b=new c.InterleavedBuffer(M,5);e.geometry.setAttribute("position",new c.InterleavedBufferAttribute(b,3,0,!1)),e.geometry.setAttribute("uv",new c.InterleavedBufferAttribute(b,2,3,!1))}e.geometry.computeBoundingBox()}else e.children.forEach(a=>{this.modifyMesh(a,t,i)})}isContain(e,t){if(e.length<3)return!1;var i,a,s=e.length<=4?e.length:4,o=!1;for(i=0,a=s-1;i<s;a=i++)e[i].y>t.y!=e[a].y>t.y&&t.x<(e[a].x-e[i].x)*(t.y-e[i].y)/(e[a].y-e[i].y)+e[i].x&&(o=!o);return o}updateExcavationInfo(e,t,i){if(e instanceof c.Mesh){if(i==!1&&e.userData.featureid_vertexY_Map&&e.userData.featureid_vertexY_Map.has(t.id)||(i&&this.resetMeshHeight(e,t.id),e.userData.region2d==null&&(e.userData.region2d=this.mvScene.mv.tools.coordinate.getBoxRegion(e.geometry.boundingBox.clone(),e.matrixWorld,!1)),L.intersect(t.region2d,e.userData.region2d)==null))return;e.userData.featureid_vertexY_Map==null&&(e.userData.featureid_vertexY_Map=new Map);let s=new c.Vector3;s.applyMatrix4(e.matrixWorld);let o=new c.Matrix4().copy(e.matrixWorld).invert(),n=this.mvScene.mv.mapbox.map.painter.context.flattenExvacationCore.correctValue,l=new c.Vector3(s.x,t.data.top-n,s.z);l.applyMatrix4(o);let h=[],u=t.data.positions.length;Math.abs(t.data.positions[0][0]-t.data.positions[u-1][0])<=1e-4&&Math.abs(t.data.positions[0][1]-t.data.positions[u-1][1])<=1e-4&&(u-=1);for(let f=0;f<u;f++){const m=t.data.positions[f];let p=new c.Vector3(m[0],m[1],0);p=this.mvScene.mv.tools.coordinate.scenePosition(p),p.applyMatrix4(o),h.push({x:p.x,y:p.z})}let d=e.userData.featureid_vertexY_Map.get(t.id);if(d==null&&(d={},e.userData.featureid_vertexY_Map.set(t.id,d)),e.geometry.attributes.position instanceof c.Float32BufferAttribute||e.geometry.attributes.position instanceof c.BufferAttribute){let f=e.geometry.attributes.position.array;for(let m=0;m<f.length;m+=3)this.isContain(h,{x:f[m],y:f[m+2]})&&(d[m+1]==null&&(d[m+1]=f[m+1]),f[m+1]=l.y);e.geometry.setAttribute("position",new c.Float32BufferAttribute(f,3,!1))}else if(e.geometry.attributes.position instanceof c.InterleavedBufferAttribute){let f=e.geometry.attributes.position.data,m=e.geometry.attributes.position.offset,p=f.array;for(let g=0;g<p.length;g+=f.stride)this.isContain(h,{x:p[g+m],y:p[g+2+m]})&&(d[g+1]==null&&(d[g+1]=p[g+1]),p[g+1+m]=l.y);const v=new c.InterleavedBuffer(p,5);e.geometry.setAttribute("position",new c.InterleavedBufferAttribute(v,3,0,!1)),e.geometry.setAttribute("uv",new c.InterleavedBufferAttribute(v,2,3,!1))}e.geometry.computeBoundingBox()}else e.children.forEach(a=>{this.updateExcavationInfo(a,t,i)})}}class Io{constructor(e){this.map=e}getAllOverscaledTile(){let e=null;const t=[];for(const i in this.map.painter.style._layers)Object.prototype.hasOwnProperty.call(this.map.painter.style._layers,i)&&t.push(this.map.painter.style._layers[i]);return t.forEach(i=>{const a=this.map.painter.style._getLayerSourceCache(i);a&&!i.isHidden(this.map.transform.zoom)&&(!e||e.getSource().maxzoom<a.getSource().maxzoom)&&(e=a)}),e?e.getVisibleCoordinates():[]}computeOverscaledTileRange(e){let t=new c.Matrix4().fromArray(this.map.transform.customLayerMatrix()),i=new c.Matrix4;i.copy(t).invert(),e.forEach(a=>{if(a.geographicRang==null){let s=new c.Matrix4().fromArray(a.projMatrix),o=new c.Vector3(0,0,0);o.applyMatrix4(s),o.applyMatrix4(i);let l=new W.mapboxgl.MercatorCoordinate(o.x,o.y).toLngLat();o=new c.Vector3(8192,8192,0),o.applyMatrix4(s),o.applyMatrix4(i);let u=new W.mapboxgl.MercatorCoordinate(o.x,o.y).toLngLat(),d=Math.min(l.lng,u.lng),f=Math.max(l.lng,u.lng),m=Math.min(l.lat,u.lat),p=Math.max(l.lat,u.lat);a.geographicRange={min:[d,m],max:[f,p]}}})}intersectPolygon(e,t){t.geographicRange==null&&this.computeOverscaledTileRange([t]);let i=Array.from(e);i[i.length-1]!=i[0]&&(i[i.length]=i[0]);let a=this.getPolygonObj(i),s=t.geographicRange.max,o=t.geographicRange.min,n=this.getPolygonObj([o,[s[0],o[1]],s,[o[0],s[1]],o]);var l=L.intersect(a,n);return l!=null&&(l.geometry.type=="Polygon"||l.geometry.type=="MultiPolygon")?l.geometry.coordinates[0]:[]}getPolygonObj(e){return{type:"Feature",properties:{},geometry:{type:"Polygon",coordinates:[e]}}}getLocationInTile(e,t){let i=new c.Matrix4().fromArray(this.map.transform.customLayerMatrix()),a=W.mapboxgl.MercatorCoordinate.fromLngLat(t),s=new c.Vector3(a.x,a.y,a.z);s.applyMatrix4(i);let o=new c.Matrix4().fromArray(e.projMatrix),n=new c.Matrix4;return n.copy(o).invert(),s.applyMatrix4(n),{x:s.x,y:s.y}}}class _i{constructor(e){this.scene=e,this.correctValue=0,this._flattenData=[],this._excavationData=[],this.opacityData=[],this.gisTile==null&&(this.gisTile=new Io(this.scene.mv.mapbox.map)),this.coordinate=new et(this.scene)}set flattenData(e){this._flattenData=e}get flattenData(){return this._flattenData}set excavationData(e){this._excavationData=e}get excavationData(){return this._excavationData}setOpacityParams(e,t,i){let a=e.getUniformLocation(t,"u_alpha");e.uniform1f(a,this.scene.mv.mapbox.opacity);let s=e.getUniformLocation(t,"u_canPartOpacity");if(this.opacityData.length==0){e.uniform1i(s,0);return}let o=this.getZoneDataInTile(this.opacityData,i),{hasData:n,tileCoords:l,pointsIndexArray:h,opacityArray:u}=o;if(n==!1){e.uniform1i(s,0);return}let d=e.getUniformLocation(t,"u_opacityRangePoint");e.uniform2fv(d,l);let f=e.getUniformLocation(t,"u_opacityRangePointIndex");e.uniform1iv(f,h);let m=e.getUniformLocation(t,"u_opacityRange");e.uniform1fv(m,u),e.uniform1i(s,1);let p=e.getUniformLocation(t,"u_opacityPolygonCount");e.uniform1i(p,h.length/2)}setFlattenParams(e,t,i){let a=e.getUniformLocation(t,"u_canFlatten");if(this.flattenData.length==0){e.uniform1i(a,0);return}let s=this.flattenData.filter(v=>v.terrainFlatten==!0),o=this.getZoneDataInTile(s,i),{hasData:n,tileCoords:l,pointsIndexArray:h,heightArray:u}=o;if(n==!1){e.uniform1i(a,0);return}let d=e.getUniformLocation(t,"u_flattenPoints");e.uniform2fv(d,l);let f=e.getUniformLocation(t,"u_flattenPointsIndex");e.uniform1iv(f,h);let m=e.getUniformLocation(t,"u_flattenAltitudes");e.uniform1fv(m,u),e.uniform1i(a,1);let p=e.getUniformLocation(t,"u_flattenPolygonCount");e.uniform1i(p,h.length/2)}setExvacationParams(e,t,i){let a=e.getUniformLocation(t,"u_canClip");if(this.excavationData.length==0){e.uniform1i(a,0);return}let s=this.excavationData.filter(v=>v.terrainExcavation==!0),o=this.getZoneDataInTile(s,i),{hasData:n,tileCoords:l,pointsIndexArray:h,heightArray:u}=o;if(n==!1){e.uniform1i(a,0);return}let d=e.getUniformLocation(t,"u_clipPoints");e.uniform2fv(d,l);let f=e.getUniformLocation(t,"u_clipPointsIndex");e.uniform1iv(f,h);let m=e.getUniformLocation(t,"u_clipAltitudes");e.uniform1fv(m,u),e.uniform1i(a,1);let p=e.getUniformLocation(t,"u_clipPolygonCount");e.uniform1i(p,h.length/2)}getZoneDataInTile(e,t){let i=[];if(e.forEach(l=>{let h=[];l.positions.forEach(d=>{let f=new c.Vector3(d[0],d[1],0),m=this.coordinate.vector2mercator(f);h.push([m[0],m[1]])}),this.gisTile.intersectPolygon(h,t).length>0&&i.push(l)}),i.length==0)return{hasData:!1};let a=[],s=[],o=[],n=[];try{let l=0,h=0;i.forEach(u=>{l!=0?h=1:h=0,s.push(l+h,l+u.positions.length),u.terrainOpacity<1?n.push(u.terrainOpacity):o.push(u.top-this.correctValue),l+=u.positions.length,u.positions.forEach(d=>{let f=this.coordinate.vector2mercator(new c.Vector3(d[0],d[1],0)),m=this.gisTile.getLocationInTile(t,[f[0],f[1]]);a.push(m.x,m.y)})})}catch{return{hasData:!1}}return{hasData:!0,tileCoords:a,pointsIndexArray:s,heightArray:o,opacityArray:n}}}class Ua{constructor(e){this.mvScene=e,this.mvScene.mv.mapbox.map.painter.context.flattenExvacationCore==null&&(this.mvScene.mv.mapbox.map.painter.context.flattenExvacationCore=new _i(this.mvScene)),this.mvScene.mv.mapbox.map.painter.context.tileFlatten==null&&(this.mvScene.mv.mapbox.map.painter.context.tileFlatten=new Da(this.mvScene))}set data(e){this._data=e}get data(){return this._data}update(){this.data&&this.mvScene.mv.mapbox.map.painter.context.flattenExvacationCore.flattenData.push(this.data)}dispose(){this._data=void 0}}const bi=new W.mapboxgl.LngLat(0,0),Mi=new W.mapboxgl.LngLat(0,0);class Va{constructor(e){this.scene=e,this.firstPtAltitude=0,this._visible=!0,this.isInit=!1,this.group=new c.Group,this.scene.widgets.add(this.group),this.scene.mv.mapbox.map.painter.context.flattenExvacationCore==null&&(this.scene.mv.mapbox.map.painter.context.flattenExvacationCore=new _i(this.scene)),this.scene.mv.mapbox.map.painter.context.tileFlatten==null&&(this.scene.mv.mapbox.map.painter.context.tileFlatten=new Da(this.scene)),this.wallMaterial=new c.MeshBasicMaterial({side:c.DoubleSide})}set data(e){let t=[];if(e.positions.forEach(i=>{let a=this.scene.mv.tools.coordinate.vector2mercator(new c.Vector3(i[0],i[1],0));t.push([a[0],a[1]])}),this._data=Object.assign({},e,{coords:t}),this._data!=null){let{wallHeight:i,maxHeight:a}=this.getMaxAndWallHeight();this.maxHeight=a,this.wallHeight=i}else this.wallHeight=0,this.maxHeight=0;this.isInit?(this.updateWallMesh(),this.updateBottomMesh()):(this.init(),this.isInit=!0),this.wallMaterial.opacity=e.wallOpacity,this.wallMaterial.transparent=!(e.wallOpacity>=1)}get data(){return this._data}set visible(e){this._visible=e,this.group.visible=e}get visible(){return this._visible}init(){let e=new c.TextureLoader,t=this;e.load(Q.MULTIVERSE_RESOURCE+"/css/img/dig_wall.png",function(i){t.wallMaterial.map=i;let a=new c.BufferGeometry;t.wallMesh=new c.Mesh(a,t.wallMaterial),t.group.add(t.wallMesh),t.data&&t.updateWallMesh()}),e.load(Q.MULTIVERSE_RESOURCE+"/css/img/dig_bottom.jpg",function(i){t.bottomMaterial=new c.MeshBasicMaterial({map:i,side:c.DoubleSide});let a=new c.BufferGeometry;t.bottomMesh=new c.Mesh(a,t.bottomMaterial),t.group.add(t.bottomMesh),t.data&&t.updateBottomMesh()})}getMaxAndWallHeight(){let e=99999,t=-9999;for(let i=0;i<this.data.coords.length;i++){const a=this.data.coords[i];let s=this.scene.mv.mapbox.map.queryTerrainElevation(a);s<e&&(e=s),t<s&&(t=s)}return{maxHeight:e,wallHeight:t}}updateWallMesh(){if(this.wallMesh==null)return;if(this._data==null){this.wallMesh.geometry.setIndex([]),this.wallMesh.geometry.setAttribute("uv",new c.Float32BufferAttribute([],2)),this.wallMesh.geometry.setAttribute("position",new c.Float32BufferAttribute([],3));return}let e=0;this.data.top>=this.maxHeight?e=this.maxHeight-.1:e=this.data.top;let t=this.wallHeight,i=[];this.data.coords.push(this.data.coords[0]),e-=50,this.firstPtAltitude=this.scene.mv.mapbox.map.queryTerrainElevation(this.data.coords[0]);let a=2e3,s=[],o=t-e;for(let p=0;p<this.data.coords.length-1;p++){let v=this.data.coords[p],g=this.data.coords[p+1];bi.lng=v[0],bi.lat=v[1],Mi.lng=g[0],Mi.lat=g[1],a=Math.ceil(bi.distanceTo(Mi)),a>2e3&&(a=2e3);let x=(g[0]-v[0])/a,_=(g[1]-v[1])/a;for(let y=0;y<=a;y++){let M=[v[0]+y*x,v[1]+y*_],b=this.scene.mv.mapbox.map.queryTerrainElevation(M),w=this.scene.mv.tools.coordinate.mercator2vector(M[0],M[1],e,!1),T=this.scene.mv.tools.coordinate.mercator2vector(M[0],M[1],b,!1);i.push(w),i.push(T),s.push(y/a,0),s.push(y/a,(b-e)/o)}}let n=[0,1,3,0,3,2],l=this.data.coords.length-1;l=i.length/2-1;for(let p=1;p<l;p++)n.push(0+p*2,1+p*2,3+p*2,0+p*2,3+p*2,2+p*2);this.data.coords.length=this.data.coords.length-1;let h=[],u;for(let p=0;p<i.length;p++){const v=i[p];p==0?(u=v.clone(),h.push(new c.Vector3)):h.push(new c.Vector3(v.x-u.x,v.y-u.y,v.z-u.z))}this.wallMesh.geometry.setIndex(n),this.wallMesh.geometry.setAttribute("uv",new c.Float32BufferAttribute(s,2)),this.wallMesh.geometry.setFromPoints(h);let d=this.getMinDistance(this.data.coords);d=Number(d.toFixed(0));let f=this.wallMaterial.map;f.wrapS=f.wrapT=c.RepeatWrapping;let m=t-e;d>m?f.repeat.set(Number((d/m).toFixed(0)),1):f.repeat.set(1,Number((m/d).toFixed(0))),this.wallMesh.position.copy(u),this.wallMesh.frustumCulled=!1}getMinDistance(e){let t=99999999999;for(let i=0;i<e.length-1;i++){let a=L.distance(e[i],e[i+1])*1e3;a<t&&(t=a)}return t}updateBottomMesh(){if(this.bottomMesh==null)return;if(this._data==null){this.wallMesh.geometry.setIndex([]),this.wallMesh.geometry.setAttribute("uv",new c.Float32BufferAttribute([],2)),this.wallMesh.geometry.setAttribute("position",new c.Float32BufferAttribute([],3));return}let e=0;this.data.top>=this.maxHeight?e=this.maxHeight-.1:e=this.data.top;let t,i=new c.Shape,a=this.bottomMesh.geometry,s=[],o=[],n=[],l=new c.Vector2(9999999,9999999),h=new c.Vector2(-9999999,-9999999),u={x:0,y:0};for(let v=0;v<this.data.coords.length;v++){const g=this.data.coords[v];let x=this.scene.mv.tools.coordinate.mercator2vector(g[0],g[1],e,!1);v==0?(t=x.clone(),i.moveTo(0,0),s.push(new c.Vector3(0,0,0))):(i.lineTo(x.x-t.x,x.z-t.z),u={x:x.x-t.x,y:x.z-t.z},s.push(new c.Vector3(x.x-t.x,x.y-t.y,x.z-t.z))),n.push(u),l.x=Math.min(l.x,u.x),l.y=Math.min(l.y,u.y),h.x=Math.max(h.x,u.x),h.y=Math.max(h.y,u.y)}let d=h.x-l.x,f=h.y-l.y;n.forEach(v=>{o.push((v.x-l.x)/d,(v.y-l.y)/f)});let m=new c.ShapeGeometry(i);a.setAttribute("position",new c.Float32BufferAttribute(m.attributes.position.array,3));let p=Array.from(m.index.array);a.setIndex(p),a.setAttribute("uv",new c.Float32BufferAttribute(o,2)),a.setFromPoints(s),this.bottomMesh.position.copy(t)}update(){this.data&&this.scene.mv.mapbox.map.painter.context.flattenExvacationCore.excavationData.push(this.data),this.scene.mv.mapbox.terrain?this.visible=!0:this.visible=!1,(this.wallHeight==null||this.maxHeight==null)&&(this.data=this._data,this.scene.render()),this.isUpdateWall()&&this.updateWallMesh()}isUpdateWall(){if(this.isInit==!1||this.data.coords.length==0||this.visible==!1)return!1;let e=this.scene.mv.mapbox.map.queryTerrainElevation(this.data.coords[0]),t=1;return(e==null||Math.abs(this.firstPtAltitude-e)>t)&&(this.scene.forceRepaint=!0),Math.abs(this.firstPtAltitude-e)>t}dispose(){this._data=void 0,this.group.children.forEach(e=>{e.geometry.dispose(),e.material.dispose()}),this.group.children.length=0}}class Oa extends $t{constructor(e,t){super(),this.relativePoints=[],this.operateState=8,this.flowSpeed=new c.Vector2(2e-4,0),this.type=fe.polygon,this.lib=ft.THREE,this.id=e,this.url=t}set data(e){super.data.tileFlatten=super.data.terrainFlatten=super.data.terrainExcavation=super.data.tileExcavation=super.data.extrude=super.data.water=!1,super.data.image=void 0,super.data.terrainOpacity=1;let t={};if(Object.assign(t,this.data,e),!t.positions||t.positions.length==0)return;super.data=t;let i=!1;super.data.image!=null&&super.data.image!=""&&(i=!0);let a=!1;if(super.data.terrainOpacity<1&&(a=!0),super.data.tileFlatten+super.data.terrainFlatten+super.data.tileExcavation+super.data.terrainExcavation+super.data.extrude+super.data.water+i+a>1)throw new Error(' "tileFlattern,tileExcavation,terrainFlattern,terrainExcavation,Extrude,Water,Image,TerrainOpacity" can not be actived at same time!');if(this.data.flow){let o=this.data.direction.indexOf("-")==0?-1:1;this.data.direction.toLocaleLowerCase().indexOf("x")!=-1?this.flowSpeed.set(1/this.data.interval*o,0):this.flowSpeed.set(0,1/this.data.interval*o)}this.checkAltitude()}get data(){return super.data.color==null&&(super.data.color="rgb(0,153,255)"),super.data.extrudeLineColor==null&&(super.data.extrudeLineColor="rgb(0,255,255)"),super.data.showExtrudeLine==null&&(super.data.showExtrudeLine=!0),super.data.opacity==null&&(super.data.opacity=.5),super.data.base==null&&(super.data.base=0),super.data.top==null&&(super.data.top=super.data.base+1),super.data.tileFlatten==null&&(super.data.tileFlatten=!1),super.data.terrainFlatten==null&&(super.data.terrainFlatten=!1),super.data.tileExcavation==null&&(super.data.tileExcavation=!1),super.data.terrainExcavation==null&&(super.data.terrainExcavation=!1),super.data.water==null&&(super.data.water=!1),super.data.extrude==null&&(super.data.extrude=!1),super.data.fill==null&&(super.data.fill=!0),super.data.showline==null&&(super.data.showline=!0),super.data.interval==null&&(super.data.interval=5),super.data.direction==null&&(super.data.direction="x"),super.data.imageRepeat==null&&(super.data.imageRepeat=new c.Vector2(1,1)),super.data.flow==null&&(super.data.flow=!1),super.data.terrainOpacity==null&&(super.data.terrainOpacity=1),super.data.lineColor==null&&(super.data.lineColor=65493),super.data.lineWidth==null&&(super.data.lineWidth=4),super.data.lineDashed==null&&(super.data.lineDashed=!1),super.data.wallOpacity==null&&(super.data.wallOpacity=1),super.data}set visible(e){super.visible=e,super.visible==!1?this.hideAllEffectMesh():this.needUpdate=!0,(this.data.tileFlatten||this.data.terrainFlatten||this.data.tileExcavation||this.data.terrainExcavation)&&this.parent.mv.events.polygonChange.emit("default",this.id,!1)}get visible(){return super.visible}get AABB(){return this.data.positions&&(this._pBox||(this._pBox=new Z,this.data.positions.forEach(e=>{let t=new c.Vector3(e[0],e[1],0),i=this.parent.mv.tools.coordinate.vector2mercator(t,!0);if((this.data.fill||this.data.extrude||this.data.water||this.data.image!=null&&this.data.image!=""||this.data.tileExcavation||this.data.terrainExcavation||this.data.terrainFlatten||this.data.tileFlatten)&&(t.z=this.data.base),t=this.parent.mv.tools.coordinate.mercator2vector(i[0],i[1],t.z,!1),this._pBox.expandByPoint(t),this.data.extrude){let a=this.parent.mv.tools.coordinate.mercator2vector(i[0],i[1],this.data.top,!1);this._pBox.expandByPoint(a)}})),super.AABB=this._pBox),super.AABB}set AABB(e){super.AABB=e}get AABBWorld(){return this.AABB}get sphere(){let e=new c.Sphere;return this.AABB.getBoundingSphere(e),e}set fill(e){this.polygonMesh&&(this.polygonMesh.visible=e)}get fill(){return this.polygonMesh?this.polygonMesh.visible:!1}set showline(e){this.outLineMesh&&(this.outLineMesh.visible=e)}get showline(){return this.outLineMesh?this.outLineMesh.visible:!1}set image(e){this.hideAllEffectMesh(),e==!0?(this.imageMaterial=this.getImageMaterial(),this.polygonMesh.material=this.imageMaterial,this.polygonMesh.visible=!0,this.operateState=6):(this.polygonMesh.material=this.polygonMaterial,this.data.image=void 0,this.operateState=8),this.afterOperateStateChange()}get image(){return this.operateState==6}set tileFlatten(e){this.hideAllEffectMesh(),e==!0?(this.flattenFeature==null&&(this.flattenFeature=new Ua(this.parent)),this.flattenFeature.data=this.data,this.operateState=0,this.parent.mv.events.polygonChange.emit("default",this.id,!1)):this.operateState==0&&(this.operateState=8),this.afterOperateStateChange()}get tileFlatten(){return this.operateState==0}set terrainFlatten(e){this.hideAllEffectMesh(),e==!0?(this.flattenFeature==null&&(this.flattenFeature=new Ua(this.parent)),this.flattenFeature.data=this.data,this.operateState=1,this.parent.mv.events.polygonChange.emit("default",this.id,!1)):this.operateState==1&&(this.operateState=8),this.afterOperateStateChange()}get terrainFlatten(){return this.operateState==1}set tileExcavation(e){this.hideAllEffectMesh(),e==!0?(this.excavationFeature==null&&(this.excavationFeature=new Va(this.parent)),this.excavationFeature.visible=!0,this.excavationFeature.data=this.data,this.operateState=2,this.parent.mv.events.polygonChange.emit("default",this.id,!1)):this.operateState==2&&(this.operateState=8),this.afterOperateStateChange()}get tileExcavation(){return this.operateState==2}set terrainExcavation(e){this.hideAllEffectMesh(),e==!0?(this.excavationFeature==null&&(this.excavationFeature=new Va(this.parent)),this.excavationFeature.visible=!0,this.excavationFeature.data=this.data,this.operateState=3,this.parent.mv.events.polygonChange.emit("default",this.id,!1)):this.operateState==3&&(this.operateState=8),this.afterOperateStateChange()}get terrainExcavation(){return this.operateState==3}get terrainOpacity(){return this.operateState==7}set terrainOpacity(e){this.hideAllEffectMesh(),e==!0?(this.operateState=7,this.parent.mv.mapbox.layerOrderState=gt.useTerrainOpacity):this.operateState==7&&(this.operateState=8,this.parent.mv.mapbox.layerOrderState=gt.common),this.parent.mv.mapbox.map.getTerrain()!=null&&this.parent.mv.mapbox.resetLayerOrder(),this.afterOperateStateChange()}set extrude(e){this.hideAllEffectMesh(),e==!0?(this.operateState=4,this.createOrUpdateExtrudePolygon(),this.extrudePolygonMesh.visible=!0,this.extrudeOutLine.visible=!0):this.operateState==4&&(this.operateState=8),this.afterOperateStateChange()}get extrude(){return this.operateState==4}get water(){return this.operateState==5}set water(e){this.hideAllEffectMesh(),e==!0?(this.operateState=5,this.waterMaterial==null?this.waterMaterial=this.getWaterMaterial():(this.waterMaterial.opacity=this.data.opacity,this.waterMaterial.transparent=!(this.data.opacity>=1)),this.polygonMesh&&(this.polygonMesh.material=this.waterMaterial),this.forceRepaint=!0):this.operateState=8,this.afterOperateStateChange()}set polygonMaterial(e){this._polygonMaterial=e}get polygonMaterial(){return this._polygonMaterial}set outLineMaterial(e){this._outLineMaterial=e}get outLineMaterial(){return this._outLineMaterial}set extrudeMaterial(e){this._extrudeMaterial=e}get extrudeMaterial(){return this._extrudeMaterial}load(e){super.load(e),this.parent.mv.events.featureLoaded.emit("default",{featureID:this.id}),this.data.positions&&this.data.positions.length>0&&(this.updatePolygonData(),this.parent.render(!0))}updatePolygonData(){if(this.createOrUpdatePolygon(),this.createOrUpdateOutLine(),this.data.tileFlatten?this.tileFlatten=this.data.tileFlatten:this.data.terrainFlatten?this.terrainFlatten=this.data.terrainFlatten:this.data.tileExcavation?this.tileExcavation=this.data.tileExcavation:this.data.terrainExcavation?this.terrainExcavation=this.data.terrainExcavation:this.data.water?this.water=this.data.water:this.data.extrude?this.extrude=this.data.extrude:this.data.image?this.image=!0:this.data.terrainOpacity<1?this.terrainOpacity=!0:(this.tileFlatten=!1,this.terrainFlatten=!1,this.tileExcavation=!1,this.terrainExcavation=!1,this.water=!1,this.extrude=!1,this.image=!1,this.terrainOpacity=!1),this.fill=this.data.fill,this.showline=this.data.showline,this.data!=null&&this.data.positions){let e=[];this.data.positions.forEach(t=>{e.push(this.parent.mv.tools.coordinate.vector2mercator(new c.Vector3(t[0],t[1],0)))}),this.region2d={type:"Feature",geometry:{type:"Polygon",coordinates:[e]}}}else this.region2d=void 0;switch(this.operateState){case 0:case 1:this.flattenFeature.data=this.data,this.parent.mv.events.polygonChange.emit("default",this.id,!0);break;case 2:case 3:this.excavationFeature.data=this.data;break;case 4:this.createOrUpdatePolygon(),this.createOrUpdateExtrudePolygon();break;case 5:this.createOrUpdatePolygon(),this.waterMaterial==null&&(this.waterMaterial=this.getWaterMaterial()),this.polygonMesh&&(this.polygonMesh.material=this.waterMaterial,this.polygonMesh.visible=!0),this.forceRepaint=!0;break;case 6:this.createOrUpdatePolygon(),this.imageMaterial=this.getImageMaterial(),this.polygonMesh&&(this.polygonMesh.material=this.imageMaterial,this.polygonMesh.visible=!0),this.forceRepaint=!0;break}}update(){if(this.loaded!=!1&&!(!this.data.positions||this.data.positions.length==0))switch((this.data.terrainExcavation||this.data.tileExcavation)&&this.parent.mv.mapbox.terrain&&this.parent.mv.mapbox.needSyncCamera&&(this.needUpdate=!0),this.needUpdate&&(this.needUpdate=!1,this.updatePolygonData(),this._pBox=void 0),super.update(),this.operateState){case 0:case 1:this.flattenFeature.update();break;case 2:case 3:this.excavationFeature.visible=!0,this.excavationFeature.update();break;case 7:this.parent.mv.mapbox.map.painter.context.flattenExvacationCore==null&&(this.parent.mv.mapbox.map.painter.context.flattenExvacationCore=new _i(this.parent)),this.parent.mv.mapbox.map.painter.context.flattenExvacationCore.opacityData.push(this.data);break;case 4:break;case 5:this.lastFlowTime==null&&(this.lastFlowTime=Date.now());let e=(Date.now()-this.lastFlowTime)/1e3;this.waterMaterial.map.offset.x-=this.flowSpeed.x*e,this.waterMaterial.map.repeat.x=1,this.lastFlowTime=Date.now();break;case 6:if(this.data.flow){this.lastFlowTime==null&&(this.lastFlowTime=Date.now());let t=(Date.now()-this.lastFlowTime)/1e3;this.imageMaterial.map.offset.x-=this.flowSpeed.x*t,this.imageMaterial.map.offset.y-=this.flowSpeed.y*t,this.forceRepaint=!0,this.lastFlowTime=Date.now()}break}}dispose(){(this.data.tileFlatten||this.data.terrainFlatten)&&(this.operateState==8,this.parent.mv.mapbox.map.painter.context.tileFlatten.resetMeshHeightByPolygon(this.id)),super.dispose(),this.container.children.forEach(e=>{e instanceof vt&&(e.geometry&&e.geometry.dispose&&e.geometry.dispose(),e.material&&e.material.dispose&&e.material.dispose())}),this.excavationFeature&&this.excavationFeature.dispose(),this.data={},this.container.children.length=0}createOrUpdateOutLine(){if(this.createOrUpdateLineMaterial(),this.outLineMesh==null){let e=new F.LineGeometry;this.outLineMesh=new F.Line2(e,this.outLineMaterial),this.outLineMesh.frustumCulled=!1,this.outLineMesh.feature=this,this.container.add(this.outLineMesh)}if(this.data.positions!=null){let e=[];for(let t=0;t<this.relativePoints.length;t++){const i=this.relativePoints[t];t==this.relativePoints.length-1?(e.push(i.x,i.y,i.z,i.x,i.y,i.z),e.push(e[0],e[1],e[2])):e.push(i.x,i.y,i.z)}this.outLineMesh.geometry.setPositions(e),this.outLineMesh.computeLineDistances()}this.outLineMesh.position.copy(this.polygonMesh.position)}createOrUpdateLineMaterial(){this.outLineMaterial==null&&(this.outLineMaterial=new F.LineMaterial({resolution:new c.Vector2(this.parent.mv.mainCanvas.width,this.parent.mv.mainCanvas.height)})),this.outLineMaterial.color=new c.Color(this.data.lineColor).convertSRGBToLinear(),this.outLineMaterial.linewidth=this.data.lineWidth,this.outLineMaterial.dashed=this.data.lineDashed}createOrUpdatePolygon(){this.currentShape=new c.Shape;let e;if(this.polygonMesh?e=this.polygonMesh.geometry:(this.polygonMaterial==null&&(this.polygonMaterial=new c.MeshBasicMaterial({color:this.data.color,transparent:!1,side:c.DoubleSide,opacity:this.data.opacity})),e=new c.BufferGeometry,this.polygonMesh=new vt(e,this.polygonMaterial),this.polygonMesh.feature=this,this.container.add(this.polygonMesh)),this.polygonMaterial.opacity=this.data.opacity,this.data.opacity<1?this.polygonMaterial.transparent=!0:this.polygonMaterial.transparent=!1,this.polygonMaterial.color=new c.Color(this.data.color),this.data.positions==null){this.polygonMesh.geometry.setAttribute("position",new c.Float32BufferAttribute([],3)),this.polygonMesh.geometry.setAttribute("uv",new c.Float32BufferAttribute([],2)),this.polygonMesh.geometry.setIndex([]);return}const t=this.data.positions[0];let i=this.parent.mv.tools.coordinate.vector2mercator([t[0],t[1],0],!0),a=this.parent.mv.tools.coordinate.mercator2vector(i[0],i[1],this.data.base,!1);this.baseY=a.y,a=this.parent.mv.tools.coordinate.mercator2vector(i[0],i[1],this.data.top,!1),this.topY=a.y,this.relativePoints=[];let s=[],o=[],n=new c.Vector2(9999999,9999999),l=new c.Vector2(-9999999,-9999999),h={x:0,y:0},u;for(let v=0;v<this.data.positions.length;v++){const g=this.data.positions[v];let x=this.parent.mv.tools.coordinate.vector2mercator([g[0],g[1],0],!0),_=this.parent.mv.tools.coordinate.mercator2vector(x[0],x[1],this.data.base,!1);v==0?(u=_.clone(),this.currentShape.moveTo(0,0),this.relativePoints.push(new c.Vector3(0,0,0))):(this.currentShape.lineTo(_.x-u.x,_.z-u.z),h={x:_.x-u.x,y:_.z-u.z},this.relativePoints.push(new c.Vector3(_.x-u.x,_.y-u.y,_.z-u.z))),o.push(h),n.x=Math.min(n.x,h.x),n.y=Math.min(n.y,h.y),l.x=Math.max(l.x,h.x),l.y=Math.max(l.y,h.y)}let d=l.x-n.x,f=l.y-n.y;o.forEach(v=>{s.push((v.x-n.x)/d,(v.y-n.y)/f)});let m=new c.ShapeGeometry(this.currentShape);e.setAttribute("position",new c.Float32BufferAttribute(m.attributes.position.array,3));let p=Array.from(m.index.array);e.setIndex(p),e.setAttribute("uv",new c.Float32BufferAttribute(s,2)),e.setFromPoints(this.relativePoints),this.polygonMesh.frustumCulled=!0,this.polygonMesh.position.copy(u)}createOrUpdateExtrudePolygon(){let e=this.polygonMesh.position.clone(),t=this.parent.mv.tools.coordinate.vector2mercator(this.polygonMesh.position,!1),i=this.parent.mv.tools.coordinate.mercator2vector(t[0],t[1],this.data.top,!1);e.y=i.y;let a=e.y-this.polygonMesh.position.y-.001,s={steps:2,depth:this.data!=null?a:0,bevelEnabled:!0,bevelThickness:0,bevelSize:0,bevelOffset:0,bevelSegments:10},o=this.data.positions?this.currentShape:new c.Shape;if(this.extrudePolygonMesh==null){let n=new c.ExtrudeGeometry(o,s);this._extrudeMaterial==null&&(this._extrudeMaterial=new c.MeshPhongMaterial({color:this.data.color,transparent:!0})),this.extrudePolygonMesh=new vt(n,this._extrudeMaterial),this.extrudePolygonMesh.feature=this,this.extrudePolygonMesh.rotateX(Math.PI/2),this.container.add(this.extrudePolygonMesh);let l=this.getExtrudeOutLine(),h=new c.LineBasicMaterial({color:this.data.extrudeLineColor,side:c.DoubleSide});this.extrudeOutLine=new c.LineSegments(l.geometry,h),this.extrudeOutLine.renderOrder=5,this.extrudeOutLine.position.copy(l.basePosition),this.container.add(this.extrudeOutLine)}else{this.extrudePolygonMesh.geometry=new c.ExtrudeGeometry(o,s),this._extrudeMaterial.color.set(this.data.color),this._extrudeMaterial.opacity=this.data.opacity,this.extrudeOutLine.material.color.set(this.data.extrudeLineColor);let n=this.getExtrudeOutLine();this.extrudeOutLine.geometry=n.geometry,this.extrudeOutLine.position.copy(n.basePosition)}this.extrudeOutLine.visible=this.data.showExtrudeLine,this._extrudeMaterial.opacity=this.data.opacity,this._extrudeMaterial.color,this.extrudePolygonMesh.position.copy(e)}getExtrudeOutLine(){let e=new c.BufferGeometry,t=this.data.positions,i,a=[],s,o;for(let n=0;n<t.length;n++){s=n,n==t.length-1?o=0:o=n+1;let l=this.getScenePosition(t[s],this.data.top),h=this.getScenePosition(t[o],this.data.top);i==null&&(i=l.clone()),l.sub(i),h.sub(i),a.push(l.x,l.y,l.z,h.x,h.y,h.z);let u=this.getScenePosition(t[s],this.data.base),d=this.getScenePosition(t[o],this.data.base);u.sub(i),d.sub(i),a.push(u.x,u.y,u.z,d.x,d.y,d.z),a.push(u.x,u.y,u.z,l.x,l.y,l.z),a.push(h.x,h.y,h.z,d.x,d.y,d.z)}return e.setAttribute("position",new c.Float32BufferAttribute(a,3)),{geometry:e,basePosition:i}}getScenePosition(e,t){let i=new c.Vector3(e[0],e[1],t),a=this.parent.mv.tools.coordinate.vector2mercator(i);return i=this.parent.mv.tools.coordinate.mercator2vector(a[0],a[1],t,!1),i}getImageMaterial(){let e=new c.MeshBasicMaterial({color:new c.Color(1,1,1),depthWrite:!0,transparent:!0,opacity:this.data.opacity,side:c.DoubleSide}),t=new c.TextureLoader().load(this.data.image);return t.wrapS=c.RepeatWrapping,t.wrapT=c.RepeatWrapping,t.repeat.x=this.data.imageRepeat.x,t.repeat.y=this.data.imageRepeat.y,t.anisotropy=this.parent.mv.THREE_Renderer.capabilities.getMaxAnisotropy(),e.map=t,e.color=new c.Color(1,1,1),e}getWaterMaterial(){let e=new c.MeshBasicMaterial({color:5824222,depthWrite:!0,transparent:!1,opacity:1,side:c.DoubleSide}),t;this.data.waterImage!=null&&this.data.waterImage!=""?t=this.data.waterImage:t=Q.MULTIVERSE_RESOURCE+"/css/img/movingRiver.png";let i=new c.TextureLoader().load(t);return i.wrapS=c.RepeatWrapping,i.wrapT=c.RepeatWrapping,i.anisotropy=this.parent.mv.THREE_Renderer.capabilities.getMaxAnisotropy(),e.map=i,e.opacity=this.data.opacity,e.transparent=!(e.opacity>=1),e}hideAllEffectMesh(){this.excavationFeature&&(this.excavationFeature.visible=!1),this.extrudePolygonMesh&&(this.extrudePolygonMesh.visible=!1),this.extrudeOutLine&&(this.extrudeOutLine.visible=!1),this.polygonMesh&&this.polygonMesh.material==this.waterMaterial&&(this.polygonMesh.material=this.polygonMaterial),this.forceRepaint=!1}afterOperateStateChange(){this.operateState!=0&&this.parent.mv.events.polygonChange.emit("default",this.id,!1)}checkAltitude(){if(this.data.extrude&&this.data.top<=this.data.base)throw new Error("when used as a extrude feature ,the top value must be greater than base");if((this.data.tileExcavation||this.data.terrainExcavation||this.data.tileFlatten||this.data.terrainFlatten)&&this.data.top>this.data.base)throw new Error("when used as a flattening or excavation,the top value must be less than the base value")}}class Na{constructor(){this.pos=new c.Vector3,this.dir=new c.Vector3,this.right=new c.Vector3,this.up=new c.Vector3,this.dist=0,this.widthScale=1,this.sharp=!1}lerpPathPoints(e,t,i){this.pos.lerpVectors(e.pos,t.pos,i),this.dir.lerpVectors(e.dir,t.dir,i),this.up.lerpVectors(e.up,t.up,i),this.right.lerpVectors(e.right,t.right,i),this.dist=(t.dist-e.dist)*i+e.dist,this.widthScale=(t.widthScale-e.widthScale)*i+e.widthScale}copy(e){this.pos.copy(e.pos),this.dir.copy(e.dir),this.up.copy(e.up),this.right.copy(e.right),this.dist=e.dist,this.widthScale=e.widthScale}}const wi=new c.Vector3,Fa=new c.Vector3,Lo=new c.Vector3,za=new c.Matrix4,Ro=new c.QuadraticBezierCurve3;function Co(r,e,t,i,a,s){const o=wi.subVectors(e,r),n=Fa.subVectors(t,e),l=o.length(),h=n.length();o.normalize(),n.normalize();const u=Math.min((a?l/2:l)*.999999,i);s.v0.copy(e).sub(o.multiplyScalar(u)),s.v1.copy(e);const d=Math.min(h/2*.999999,i);return s.v2.copy(e).add(n.multiplyScalar(d)),s}class Bo{constructor(){this.array=[],this.count=0}set(e,t=.1,i=10,a=null,s=!1){if(e=e.slice(0),e.length<2){console.warn("PathPointList: points length less than 2."),this.count=0;return}s&&!e[0].equals(e[e.length-1])&&e.push(new c.Vector3().copy(e[0]));for(let o=0,n=e.length;o<n;o++)if(o===0)this._start(e[o],e[o+1],a);else if(o===n-1)if(s){this._corner(e[o],e[1],t,i,a);const l=this.array[0].dist;this.array[0].copy(this.array[this.count-1]),this.array[0].dist=l}else this._end(e[o]);else this._corner(e[o],e[o+1],t,i,a)}distance(){return this.count>0?this.array[this.count-1].dist:0}_getByIndex(e){return this.array[e]||(this.array[e]=new Na),this.array[e]}_start(e,t,i){this.count=0;const a=this._getByIndex(this.count);if(a.pos.copy(e),a.dir.subVectors(t,e),i)a.up.copy(i);else{let s=Number.MAX_VALUE;const o=Math.abs(a.dir.x),n=Math.abs(a.dir.y),l=Math.abs(a.dir.z);o<s&&(s=o,a.up.set(1,0,0)),n<s&&(s=n,a.up.set(0,1,0)),l<s&&a.up.set(0,0,1)}a.right.crossVectors(a.dir,a.up).normalize(),a.up.crossVectors(a.right,a.dir).normalize(),a.dist=0,a.widthScale=1,a.sharp=!1,a.dir.normalize(),this.count++}_end(e){const t=this.array[this.count-1],i=this._getByIndex(this.count);i.pos.copy(e),i.dir.subVectors(e,t.pos);const a=i.dir.length();i.dir.normalize(),i.up.copy(t.up);const s=wi.crossVectors(t.dir,i.dir);if(s.length()>Number.EPSILON){s.normalize();const o=Math.acos(Math.min(Math.max(t.dir.dot(i.dir),-1),1));i.up.applyMatrix4(za.makeRotationAxis(s,o))}i.right.crossVectors(i.dir,i.up).normalize(),i.dist=t.dist+a,i.widthScale=1,i.sharp=!1,this.count++}_corner(e,t,i,a,s){if(i>0&&a>0){const o=this.array[this.count-1],l=Co(o.pos,e,t,i,this.count-1===0,Ro).getPoints(a);for(let h=0;h<a;h++)this._sharpCorner(l[h],l[h+1],s,h===0?1:0);l[a].equals(t)||this._sharpCorner(l[a],t,s,2)}else this._sharpCorner(e,t,s,0,!0)}_sharpCorner(e,t,i,a=0,s=!1){const o=this.array[this.count-1],n=this._getByIndex(this.count),l=wi.subVectors(e,o.pos),h=Fa.subVectors(t,e),u=l.length();if(l.normalize(),h.normalize(),n.pos.copy(e),a===1?n.dir.copy(l):a===2?n.dir.copy(h):(n.dir.addVectors(l,h),n.dir.normalize()),i)n.dir.dot(i)===1?n.right.crossVectors(h,i).normalize():n.right.crossVectors(n.dir,i).normalize(),n.up.crossVectors(n.right,n.dir).normalize();else{n.up.copy(o.up);const f=Lo.crossVectors(o.dir,n.dir);if(f.length()>Number.EPSILON){f.normalize();const m=Math.acos(Math.min(Math.max(o.dir.dot(n.dir),-1),1));n.up.applyMatrix4(za.makeRotationAxis(f,m))}n.right.crossVectors(n.dir,n.up).normalize()}n.dist=o.dist+u;const d=l.dot(h);n.widthScale=Math.min(1/Math.sqrt((1+d)/2),1.415)||1,n.sharp=Math.abs(d-1)>.05&&s,this.count++}}class Do extends c.BufferGeometry{constructor(e=3e3,t=!1){super(),isNaN(e)?this._initByData(e.pathPointList,e.options,e.usage,t):this._initByMaxVertex(e,t)}_initByMaxVertex(e,t){this.setAttribute("position",new c.BufferAttribute(new Float32Array(e*3),3).setUsage(c.DynamicDrawUsage)),this.setAttribute("normal",new c.BufferAttribute(new Float32Array(e*3),3).setUsage(c.DynamicDrawUsage)),this.setAttribute("uv",new c.BufferAttribute(new Float32Array(e*2),2).setUsage(c.DynamicDrawUsage)),t&&this.setAttribute("uv2",new c.BufferAttribute(new Float32Array(e*2),2).setUsage(c.DynamicDrawUsage)),this.drawRange.start=0,this.drawRange.count=0,this.setIndex(e>65536?new c.Uint32BufferAttribute(e*3,1):new c.Uint16BufferAttribute(e*3,1))}_initByData(e,t={},i,a){const s=ka(e,t,a);s&&s.count!==0?(this.setAttribute("position",new c.BufferAttribute(new Float32Array(s.position),3).setUsage(i||c.StaticDrawUsage)),this.setAttribute("normal",new c.BufferAttribute(new Float32Array(s.normal),3).setUsage(i||c.StaticDrawUsage)),this.setAttribute("uv",new c.BufferAttribute(new Float32Array(s.uv),2).setUsage(i||c.StaticDrawUsage)),a&&this.setAttribute("uv2",new c.BufferAttribute(new Float32Array(s.uv2),2).setUsage(i||c.StaticDrawUsage)),this.setIndex(s.position.length/3>65536?new c.Uint32BufferAttribute(s.indices,1):new c.Uint16BufferAttribute(s.indices,1))):this._initByMaxVertex(2,a)}update(e,t={}){const i=!!this.getAttribute("uv2"),a=ka(e,t,i);a?(this._updateAttributes(a.position,a.normal,a.uv,i?a.uv2:null,a.indices),this.drawRange.count=a.count):this.drawRange.count=0}_resizeAttribute(e,t){let i=this.getAttribute(e);for(;i.array.length<t;){const a=i.array.length,s=new c.BufferAttribute(new Float32Array(a*2),i.itemSize,i.normalized);s.name=i.name,s.usage=i.usage,this.setAttribute(e,s),i=s}}_resizeIndex(e){let t=this.getIndex();for(;t.array.length<e;){const i=t.array.length,a=new c.BufferAttribute(i*2>65535?new Uint32Array(i*2):new Uint16Array(i*2),1);a.name=t.name,a.usage=t.usage,this.setIndex(a),t=a}}_updateAttributes(e,t,i,a,s){this._resizeAttribute("position",e.length);const o=this.getAttribute("position");o.array.set(e,0),o.updateRange.count=e.length,o.needsUpdate=!0,this._resizeAttribute("normal",t.length);const n=this.getAttribute("normal");n.array.set(t,0),n.updateRange.count=t.length,n.needsUpdate=!0,this._resizeAttribute("uv",i.length);const l=this.getAttribute("uv");if(l.array.set(i,0),l.updateRange.count=i.length,l.needsUpdate=!0,a){this._resizeAttribute("uv2",a.length);const u=this.getAttribute("uv2");u.array.set(a,0),u.updateRange.count=a.length,u.needsUpdate=!0}this._resizeIndex(s.length);const h=this.getIndex();h.set(s,0),h.updateRange.count=s.length,h.needsUpdate=!0}}function ka(r,e,t=!1){const i=e.width||.1,a=e.progress!==void 0?e.progress:1,s=e.arrow!==void 0?e.arrow:!0,o=e.side!==void 0?e.side:"both",n=i/2,l=o!=="both"?i/2:i,h=r.distance(),u=a*h;if(h==0)return null;const d=n/l,f=n/h;let m=0;const p=[],v=[],g=[],x=[],_=[];let y=0;const M=new c.Vector3,b=new c.Vector3,w=new c.Vector3,T=new c.Vector3,I=new c.Vector3,S=new c.Vector3;function R(E){const H=p.length===0,k=E.sharp&&!H;let G;e.useImage?G=E.dist/e.imageUnit:G=E.dist/l;const O=E.dist/h,ae=E.dir,B=E.up,Ce=E.right;if(o!=="left"?M.copy(Ce).multiplyScalar(n*E.widthScale):M.set(0,0,0),o!=="right"?b.copy(Ce).multiplyScalar(-n*E.widthScale):b.set(0,0,0),M.add(E.pos),b.add(E.pos),k){w.fromArray(p,p.length-6).sub(b),T.fromArray(p,p.length-3).sub(M);const j=w.length(),ye=T.length(),_e=j-ye;let be,Be;_e>0?(be=w,Be=b):(be=T,Be=M),I.copy(be).setLength(Math.abs(_e)).add(Be);let Nt=S.copy(Be).sub(I).normalize().dot(ae),Ft=S.copy(Be).sub(I).length(),Si=Nt*Ft*2;S.copy(ae).setLength(Si).add(I),_e>0?(p.push(I.x,I.y,I.z,M.x,M.y,M.z,b.x,b.y,b.z,M.x,M.y,M.z,S.x,S.y,S.z,M.x,M.y,M.z),y+=6,_.push(y-6,y-8,y-7,y-6,y-7,y-5,y-4,y-6,y-5,y-2,y-4,y-1),m+=12):(p.push(b.x,b.y,b.z,I.x,I.y,I.z,b.x,b.y,b.z,M.x,M.y,M.z,b.x,b.y,b.z,S.x,S.y,S.z),y+=6,_.push(y-6,y-8,y-7,y-6,y-7,y-5,y-6,y-5,y-3,y-2,y-3,y-1),m+=12),v.push(B.x,B.y,B.z,B.x,B.y,B.z,B.x,B.y,B.z,B.x,B.y,B.z,B.x,B.y,B.z,B.x,B.y,B.z),g.push(G-d,0,G-d,1,G,0,G,1,G+d,0,G+d,1),t&&x.push(O-f,0,O-f,1,O,0,O,1,O+f,0,O+f,1)}else p.push(b.x,b.y,b.z,M.x,M.y,M.z),v.push(B.x,B.y,B.z,B.x,B.y,B.z),g.push(G,0,G,1),t&&x.push(O,0,O,1),y+=2,H||(_.push(y-2,y-4,y-3,y-2,y-3,y-1),m+=6)}const V=new c.Vector3;function z(E){const H=E.dir,k=E.up,G=E.right,O=E.dist/l,ae=E.dist/h;o!=="left"?M.copy(G).multiplyScalar(n*2):M.set(0,0,0),o!=="right"?b.copy(G).multiplyScalar(-n*2):b.set(0,0,0),V.copy(H).setLength(n*3),M.add(E.pos),b.add(E.pos),V.add(E.pos),p.push(b.x,b.y,b.z,M.x,M.y,M.z,V.x,V.y,V.z),v.push(k.x,k.y,k.z,k.x,k.y,k.z,k.x,k.y,k.z),g.push(O,o!=="both"?o!=="right"?-2:0:-.5,O,o!=="both"?o!=="left"?2:0:1.5,O+1.5,o!=="both"?0:.5),t&&x.push(ae,o!=="both"?o!=="right"?-2:0:-.5,ae,o!=="both"?o!=="left"?2:0:1.5,ae+1.5*i/h,o!=="both"?0:.5),y+=3,_.push(y-1,y-3,y-2),m+=3}let C;if(u>0)for(let E=0;E<r.count;E++){const H=r.array[E];if(H.dist>u){const k=r.array[E-1];C=new Na;const G=(u-k.dist)/(H.dist-k.dist);C.lerpPathPoints(k,H,G),R(C);break}else R(H)}else C=r.array[0];return s&&(C=C||r.array[r.count-1],z(C)),{position:p,normal:v,uv:g,uv2:x,indices:_,count:m}}let Ha=["attribute vec3  customColor;","attribute float customOpacity;","attribute float customSize;","attribute float customAngle;","attribute float customVisible;","varying vec4  vColor;","varying float vAngle;","void main()","{","if ( customVisible > 0.5 )","vColor = vec4( customColor, customOpacity );","else","vColor = vec4(0.0, 0.0, 0.0, 0.0);","vAngle = customAngle;","vec4 mvPosition = modelViewMatrix * vec4( position, 1.0 );","gl_PointSize = customSize * ( 300.0 / length( mvPosition.xyz ) );","gl_Position = projectionMatrix * mvPosition;","}"].join(`
`),Ga=["uniform sampler2D pTexture;","uniform float useCustomColor;","uniform vec3 u_color;","varying vec4 vColor;","varying float vAngle;","void main()","{","if(useCustomColor<0.5){","gl_FragColor = vColor;","}","else {","gl_FragColor =vec4(u_color,vColor.a);","}","float c = cos(vAngle);","float s = sin(vAngle);","vec2 rotatedUV = vec2(c * (gl_PointCoord.x - 0.5) + s * (gl_PointCoord.y - 0.5) + 0.5,","c * (gl_PointCoord.y - 0.5) - s * (gl_PointCoord.x - 0.5) + 0.5);","vec4 rotatedTexture = texture2D( pTexture,  rotatedUV );","gl_FragColor = gl_FragColor * rotatedTexture;","}"].join(`
`);function xe(r,e){this.times=r||[],this.values=e||[]}xe.prototype.lerp=function(r){for(var e=0,t=this.times.length;e<t&&r>this.times[e];)e++;if(e==0)return this.values[0];if(e==t)return this.values[t-1];var i=(r-this.times[e-1])/(this.times[e]-this.times[e-1]);return this.values[0]instanceof c.Vector3?this.values[e-1].clone().lerp(this.values[e],i):this.values[e-1]+i*(this.values[e]-this.values[e-1])};function at(){this.position=new c.Vector3,this.velocity=new c.Vector3,this.acceleration=new c.Vector3,this.angle=0,this.angleVelocity=0,this.angleAcceleration=0,this.size=16,this.color=new c.Color,this.opacity=1,this.age=0,this.alive=0}at.prototype.update=function(r){if(this.position.add(this.velocity.clone().multiplyScalar(r)),this.velocity.add(this.acceleration.clone().multiplyScalar(r)),this.angle+=this.angleVelocity*.01745329251*r,this.angleVelocity+=this.angleAcceleration*.01745329251*r,this.age+=r,this.sizeTween.times.length>0&&(this.size=this.sizeTween.lerp(this.age)),this.colorTween.times.length>0){var e=this.colorTween.lerp(this.age);this.color=new c.Color().setHSL(e.x,e.y,e.z)}this.opacityTween.times.length>0&&(this.opacity=this.opacityTween.lerp(this.age))};let Ie=Object.freeze({CUBE:1,SPHERE:2});function Le(){this.positionStyle=Ie.CUBE,this.positionBase=new c.Vector3,this.positionSpread=new c.Vector3,this.positionRadius=0,this.velocityStyle=Ie.CUBE,this.velocityBase=new c.Vector3,this.velocitySpread=new c.Vector3,this.speedBase=0,this.speedSpread=0,this.accelerationBase=new c.Vector3,this.accelerationSpread=new c.Vector3,this.angleBase=0,this.angleSpread=0,this.angleVelocityBase=0,this.angleVelocitySpread=0,this.angleAccelerationBase=0,this.angleAccelerationSpread=0,this.sizeBase=0,this.sizeSpread=0,this.sizeTween=new xe,this.colorBase=new c.Vector3(0,1,.5),this.colorSpread=new c.Vector3(0,0,0),this.colorTween=new xe,this.opacityBase=1,this.opacitySpread=0,this.opacityTween=new xe,this.blendStyle=c.NormalBlending,this.particleArray=[],this.particlesPerSecond=100,this.particleDeathAge=1,this.emitterAge=0,this.emitterAlive=!0,this.emitterDeathAge=60,this.particleCount=this.particlesPerSecond*Math.min(this.particleDeathAge,this.emitterDeathAge),this.particleGeometry=new c.BufferGeometry,this.particleTexture=null,this.particleMaterial=new c.ShaderMaterial({uniforms:{texture:{type:"t",value:this.particleTexture},useCustomColor:{value:0},u_color:{value:new c.Color(1,0,0)}},vertexShader:Ha,fragmentShader:Ga,side:c.DoubleSide,transparent:!0,opacity:1,depthWrite:!1,blending:c.AdditiveBlending}),this.particleMesh=new c.Points}Le.prototype.setValues=function(r){if(r!==void 0){this.sizeTween=new xe,this.colorTween=new xe,this.opacityTween=new xe;for(var e in r)this[e]=r[e];at.prototype.sizeTween=this.sizeTween,at.prototype.colorTween=this.colorTween,at.prototype.opacityTween=this.opacityTween,this.particleArray=[],this.emitterAge=0,this.emitterAlive=!0,this.particleCount=this.particlesPerSecond*Math.min(this.particleDeathAge,this.emitterDeathAge),this.particleGeometry=new Fe,this.particleMaterial=new c.ShaderMaterial({uniforms:{pTexture:{type:"t",value:this.particleTexture},useCustomColor:{value:r.useCustomColor},u_color:{value:r.color}},vertexShader:Ha,fragmentShader:Ga,side:c.DoubleSide,transparent:!0,opacity:1,depthWrite:!1,side:c.DoubleSide,blending:c.AdditiveBlending}),this.particleMesh=new c.Points}},Le.prototype.randomValue=function(r,e){return r+e*(Math.random()-.5)},Le.prototype.randomVector3=function(r,e){var t=new c.Vector3(Math.random()-.5,Math.random()-.5,Math.random()-.5);return new c.Vector3().addVectors(r,new c.Vector3().multiplyVectors(e,t))},Le.prototype.createParticle=function(){var r=new at;if(this.positionStyle==Ie.CUBE&&(r.position=this.randomVector3(this.positionBase,this.positionSpread)),this.positionStyle==Ie.SPHERE){var e=2*Math.random()-1,t=6.2832*Math.random(),i=Math.sqrt(1-e*e),a=new c.Vector3(i*Math.cos(t),i*Math.sin(t),e);r.position=new c.Vector3().addVectors(this.positionBase,a.multiplyScalar(this.positionRadius))}if(this.velocityStyle==Ie.CUBE&&(r.velocity=this.randomVector3(this.velocityBase,this.velocitySpread)),this.velocityStyle==Ie.SPHERE){var s=new c.Vector3().subVectors(r.position,this.positionBase),o=this.randomValue(this.speedBase,this.speedSpread);r.velocity=s.normalize().multiplyScalar(o)}r.acceleration=this.randomVector3(this.accelerationBase,this.accelerationSpread),r.angle=this.randomValue(this.angleBase,this.angleSpread),r.angleVelocity=this.randomValue(this.angleVelocityBase,this.angleVelocitySpread),r.angleAcceleration=this.randomValue(this.angleAccelerationBase,this.angleAccelerationSpread),r.size=this.randomValue(this.sizeBase,this.sizeSpread);var n=this.randomVector3(this.colorBase,this.colorSpread);return r.color=new c.Color().setHSL(n.x,n.y,n.z),r.opacity=this.randomValue(this.opacityBase,this.opacitySpread),r.age=0,r.alive=0,r},Le.prototype.initialize=function(){let r=[],e=[],t=[],i=[],a=[],s=[],o=[];for(var n=0;n<this.particleCount;n++)this.particleArray[n]=this.createParticle(),this.particleGeometry.vertices[n]=this.particleArray[n].position,s.push(this.particleArray[n].position.x,this.particleArray[n].position.y,this.particleArray[n].position.z),r.push(this.particleArray[n].alive),e.push(this.particleArray[n].color),t.push(this.particleArray[n].opacity),i.push(this.particleArray[n].size),a.push(this.particleArray[n].angle);for(var n=0;n<this.particleCount-2;n+=3){let u=new ut(n,n+1,n+2);this.particleGeometry.faces.push(u),o.push(n,n+1,n+2)}this.bufferGeometry=new c.BufferGeometry,this.bufferGeometry.setAttribute("position",new c.Float32BufferAttribute(s,3)),this.bufferGeometry.setIndex(o);let l=new Float32Array(e.length*3);l.set(e),this.bufferGeometry.setAttribute("customVisible",new c.BufferAttribute(new Float32Array(r),1)),this.bufferGeometry.setAttribute("customColor",new c.BufferAttribute(l,3)),this.bufferGeometry.setAttribute("customOpacity",new c.BufferAttribute(new Float32Array(t),1)),this.bufferGeometry.setAttribute("customSize",new c.BufferAttribute(new Float32Array(i),1)),this.bufferGeometry.setAttribute("customAngle",new c.BufferAttribute(new Float32Array(a),1)),this.particleMaterial.blending=this.blendStyle,this.particleMesh=new c.Points(this.bufferGeometry,this.particleMaterial),this.particleMesh.dynamic=!0,this.particleMesh.sortParticles=!0,this.isShow=!0},Le.prototype.update=function(r){var e=[];let t=[],i=[],a=[],s=[],o=[],n=[];for(var l=0;l<this.particleCount;l++)this.particleArray[l].alive&&(this.particleArray[l].update(r),this.particleGeometry.vertices[l]=this.particleArray[l].position,n.push(this.particleArray[l].position.x,this.particleArray[l].position.y,this.particleArray[l].position.z),this.particleArray[l].age>this.particleDeathAge&&(this.particleArray[l].alive=0,e.push(l)),t.push(this.particleArray[l].alive),i.push(this.particleArray[l].color),a.push(this.particleArray[l].opacity),s.push(this.particleArray[l].size),o.push(this.particleArray[l].angle));if(this.bufferGeometry.setAttribute("position",new c.Float32BufferAttribute(n,3)),this.bufferGeometry.getAttribute("customVisible").copyArray(t),this.bufferGeometry.getAttribute("customColor").array.set(i),this.bufferGeometry.getAttribute("customOpacity").copyArray(a),this.bufferGeometry.getAttribute("customSize").copyArray(s),this.bufferGeometry.getAttribute("customAngle").copyArray(o),this.bufferGeometry.getAttribute("customVisible").needsUpdate=!0,this.bufferGeometry.getAttribute("customColor").needsUpdate=!0,this.bufferGeometry.getAttribute("customOpacity").needsUpdate=!0,this.bufferGeometry.getAttribute("customSize").needsUpdate=!0,this.bufferGeometry.getAttribute("customAngle").needsUpdate=!0,!!this.emitterAlive){if(this.emitterAge<this.particleDeathAge){var h=Math.round(this.particlesPerSecond*(this.emitterAge+0)),u=Math.round(this.particlesPerSecond*(this.emitterAge+r));u>this.particleCount&&(u=this.particleCount);for(var l=h;l<u;l++)this.particleArray[l].alive=1}for(var d=0;d<e.length;d++){var l=e[d];this.particleArray[l]=this.createParticle(),this.particleArray[l].alive=1,this.particleGeometry.vertices[l]=this.particleArray[l].position}this.emitterAge+=r,this.emitterAge>this.emitterDeathAge&&(this.emitterAlive=!1)}};class Uo{constructor(){}static initLib(){var e={boxclip_par_vertex:`
                #ifdef BOXCLIP
                attribute float a_boxclipstate;
                uniform vec3 u_basePoint;
                varying vec3 v_relativePos;
                varying float v_boxclipstate;
                #endif
            `,boxclip_vertex:`
                #ifdef BOXCLIP
                vec4 pos=modelMatrix* vec4( transformed, 1.0 );
                v_relativePos=vec3(pos.x-u_basePoint.x,pos.y-u_basePoint.y,pos.z-u_basePoint.z);
                v_boxclipstate=a_boxclipstate;
                #endif
            `,boxclip_par_fragment:`
                #ifdef BOXCLIP
                varying vec3 v_relativePos;
                varying float v_boxclipstate;
                uniform mediump vec3 boxLimitBox[BOXCLIP*2];
                #endif
            `,boxclip_fragment:`
                #ifdef BOXCLIP
                if(v_boxclipstate>0.0){   
                highp int j=int(v_boxclipstate-1.0+0.001); 
                vec3 min=boxLimitBox[j*2];
                vec3 max=boxLimitBox[j*2+1];
                if(v_relativePos.x<min.x||v_relativePos.y<min.y||v_relativePos.z<min.z||
                    v_relativePos.x>max.x||v_relativePos.y>max.y||v_relativePos.z>max.z){
                        discard;       
                }    
                }
                #endif
            `};let t=this.createClipLib(),i=this.extensionShader(),a=[t,i];for(let s=0;s<a.length;s++)for(const o in a[s])Object.prototype.hasOwnProperty.call(a[s],o)&&(e[o]=a[s][o]);Vo(c.ShaderChunk,e,"mv/")}static extensionShader(){return{wave_par_vertex:`
                #ifdef WAVE
                varying float waveZ;
                #endif
            `,wave_vertex:`
                #ifdef WAVE
                vec4 wPosition=modelMatrix*vec4(transformed,1.0);
                waveZ=wPosition.y;
                #endif
            `,wave_par_fragment:`
                #ifdef WAVE
                varying float waveZ;
                uniform vec2 waveRange;
                #endif
            `,wave_fragment:`
                #ifdef WAVE
                float mid=(waveRange.x+waveRange.y)/2.;
                float wLen=(waveRange.y-waveRange.x)*0.5;
                if(waveZ>waveRange.x&&waveZ<waveRange.y){                   
                    outgoingLight+=vec3(1.-abs(waveZ-mid)/wLen)*0.3;                    
                }
                #endif
            `,cube_uv_reflection_fragment:`
            #ifdef ENVMAP_TYPE_CUBE_UV
            #define cubeUV_textureSize (1024.0)
            int getFaceFromDirection(vec3 direction) {
            vec3 absDirection = abs(direction);
            int face = -1;
            if( absDirection.x > absDirection.z ) {
                if(absDirection.x > absDirection.y )
                face = direction.x > 0.0 ? 0 : 3;
                else
                face = direction.y > 0.0 ? 1 : 4;
            }
            else {
                if(absDirection.z > absDirection.y )
                face = direction.z > 0.0 ? 2 : 5;
                else
                face = direction.y > 0.0 ? 1 : 4;
            }
            return face;
            }
            #define cubeUV_maxLods1  (log2(cubeUV_textureSize*0.25) - 1.0)
            #define cubeUV_rangeClamp (exp2((6.0 - 1.0) * 2.0))
            vec2 MipLevelInfo( vec3 vec, float roughnessLevel, float roughness ) {
            float scale = exp2(cubeUV_maxLods1 - roughnessLevel);
            float dxRoughness = dFdx(roughness);
            float dyRoughness = dFdy(roughness);
            vec3 dx = dFdx( vec * scale * dxRoughness );
            vec3 dy = dFdy( vec * scale * dyRoughness );
            float d = max( dot( dx, dx ), dot( dy, dy ) );
            d = clamp(d, 1.0, cubeUV_rangeClamp);
            float mipLevel = 0.5 * log2(d);
            return vec2(floor(mipLevel), fract(mipLevel));
            }
            #define cubeUV_maxLods2 (log2(cubeUV_textureSize*0.25) - 2.0)
            #define cubeUV_rcpTextureSize (1.0 / cubeUV_textureSize)
            vec2 getCubeUV(vec3 direction, float roughnessLevel, float mipLevel) {
            mipLevel = roughnessLevel > cubeUV_maxLods2 - 3.0 ? 0.0 : mipLevel;
            float a = 16.0 * cubeUV_rcpTextureSize;
            vec2 exp2_packed = exp2( vec2( roughnessLevel, mipLevel ) );
            vec2 rcp_exp2_packed = vec2( 1.0 ) / exp2_packed;
            float powScale = exp2_packed.x * exp2_packed.y;
            float scale = rcp_exp2_packed.x * rcp_exp2_packed.y * 0.25;
            float mipOffset = 0.75*(1.0 - rcp_exp2_packed.y) * rcp_exp2_packed.x;
            bool bRes = mipLevel == 0.0;
            scale =  bRes && (scale < a) ? a : scale;
            vec3 r;
            vec2 offset;
            int face = getFaceFromDirection(direction);
            float rcpPowScale = 1.0 / powScale;
            if( face == 0) {
                r = vec3(direction.x, -direction.z, direction.y);
                offset = vec2(0.0+mipOffset,0.75 * rcpPowScale);
                offset.y = bRes && (offset.y < 2.0*a) ? a : offset.y;
            }
            else if( face == 1) {
                r = vec3(direction.y, direction.x, direction.z);
                offset = vec2(scale+mipOffset, 0.75 * rcpPowScale);
                offset.y = bRes && (offset.y < 2.0*a) ? a : offset.y;
            }
            else if( face == 2) {
                r = vec3(direction.z, direction.x, direction.y);
                offset = vec2(2.0*scale+mipOffset, 0.75 * rcpPowScale);
                offset.y = bRes && (offset.y < 2.0*a) ? a : offset.y;
            }
            else if( face == 3) {
                r = vec3(direction.x, direction.z, direction.y);
                offset = vec2(0.0+mipOffset,0.5 * rcpPowScale);
                offset.y = bRes && (offset.y < 2.0*a) ? 0.0 : offset.y;
            }
            else if( face == 4) {
                r = vec3(direction.y, direction.x, -direction.z);
                offset = vec2(scale+mipOffset, 0.5 * rcpPowScale);
                offset.y = bRes && (offset.y < 2.0*a) ? 0.0 : offset.y;
            }
            else {
                r = vec3(direction.z, -direction.x, direction.y);
                offset = vec2(2.0*scale+mipOffset, 0.5 * rcpPowScale);
                offset.y = bRes && (offset.y < 2.0*a) ? 0.0 : offset.y;
            }
            r = normalize(r);
            float texelOffset = 0.5 * cubeUV_rcpTextureSize;
            vec2 s = ( r.yz / abs( r.x ) + vec2( 1.0 ) ) * 0.5;
            vec2 base = offset + vec2( texelOffset );
            return base + s * ( scale - 2.0 * texelOffset );
            }
            #define cubeUV_maxLods3 (log2(cubeUV_textureSize*0.25) - 3.0)
            vec4 textureCubeUV( sampler2D envMap, vec3 reflectedDirection, float roughness ) {
            float roughnessVal = roughness* cubeUV_maxLods3;
            float r1 = floor(roughnessVal);
            float r2 = r1 + 1.0;
            float t = fract(roughnessVal);
            vec2 mipInfo = MipLevelInfo(reflectedDirection, r1, roughness);
            float s = mipInfo.y;
            float level0 = mipInfo.x;
            float level1 = level0 + 1.0;
            level1 = level1 > 5.0 ? 5.0 : level1;
            level0 += min( floor( s + 0.5 ), 5.0 );
            vec2 uv_10 = getCubeUV(reflectedDirection, r1, level0);
            vec4 color10 = texture2D(envMap, uv_10);
            vec2 uv_20 = getCubeUV(reflectedDirection, r2, level0);
            vec4 color20 = texture2D(envMap, uv_20);
            vec4 result = mix(color10, color20, t);
            return vec4(result.rgb, 1.0);
            }
            #endif
            `,iblRadianceLib:`
            uniform mat3 matEnvMapRotY;
            vec3 getLightProbeIrradiance_mv( const in vec3 lightProbe[ 9 ], const in vec3 normal ) {

                vec3 worldNormal = inverseTransformDirection( normal, viewMatrix );;

                vec3 irradiance = shGetIrradianceAt( matEnvMapRotY * worldNormal, lightProbe );

                return irradiance;

            }
            vec3 getIBLRadiance_mv( const in vec3 viewDir, const in vec3 normal, const in float roughness ) {

                #if defined( ENVMAP_TYPE_CUBE_UV )

                    vec3 reflectVec = reflect( - viewDir, normal );

                    // Mixing the reflection with the normal is more accurate and keeps rough objects from gathering light from behind their tangent plane.
                    reflectVec = normalize( mix( reflectVec, normal, roughness * roughness) );

                    reflectVec = inverseTransformDirection( reflectVec, viewMatrix );

                    vec4 envMapColor = textureCubeUV( envMap, matEnvMapRotY*reflectVec, roughness );

                    return envMapColor.rgb * envMapIntensity;

                #else

                    return vec3( 0.0 );

                #endif

            }
            `,lights_fragment_begin:`
            GeometricContext geometry;

            geometry.position = - vViewPosition;
            geometry.normal = normal;
            geometry.viewDir = ( isOrthographic ) ? vec3( 0, 0, 1 ) : normalize( vViewPosition );

            #ifdef USE_CLEARCOAT

                geometry.clearcoatNormal = clearcoatNormal;

            #endif

            #ifdef USE_IRIDESCENCE

                float dotNVi = saturate( dot( normal, geometry.viewDir ) );

                if ( material.iridescenceThickness == 0.0 ) {

                    material.iridescence = 0.0;

                } else {

                    material.iridescence = saturate( material.iridescence );

                }

                if ( material.iridescence > 0.0 ) {

                    material.iridescenceFresnel = evalIridescence( 1.0, material.iridescenceIOR, dotNVi, material.iridescenceThickness, material.specularColor );

                    // Iridescence F0 approximation
                    material.iridescenceF0 = Schlick_to_F0( material.iridescenceFresnel, 1.0, dotNVi );

                }

            #endif

            IncidentLight directLight;

            #if ( NUM_POINT_LIGHTS > 0 ) && defined( RE_Direct )

                PointLight pointLight;
                #if defined( USE_SHADOWMAP ) && NUM_POINT_LIGHT_SHADOWS > 0
                PointLightShadow pointLightShadow;
                #endif

                #pragma unroll_loop_start
                for ( int i = 0; i < NUM_POINT_LIGHTS; i ++ ) {

                    pointLight = pointLights[ i ];

                    getPointLightInfo( pointLight, geometry, directLight );

                    #if defined( USE_SHADOWMAP ) && ( UNROLLED_LOOP_INDEX < NUM_POINT_LIGHT_SHADOWS )
                    pointLightShadow = pointLightShadows[ i ];
                    directLight.color *= ( directLight.visible && receiveShadow ) ? getPointShadow( pointShadowMap[ i ], pointLightShadow.shadowMapSize, pointLightShadow.shadowBias, pointLightShadow.shadowRadius, vPointShadowCoord[ i ], pointLightShadow.shadowCameraNear, pointLightShadow.shadowCameraFar ) : 1.0;
                    #endif

                    RE_Direct( directLight, geometry, material, reflectedLight );

                }
                #pragma unroll_loop_end

            #endif

            #if ( NUM_SPOT_LIGHTS > 0 ) && defined( RE_Direct )

                SpotLight spotLight;
                vec4 spotColor;
                vec3 spotLightCoord;
                bool inSpotLightMap;

                #if defined( USE_SHADOWMAP ) && NUM_SPOT_LIGHT_SHADOWS > 0
                SpotLightShadow spotLightShadow;
                #endif

                #pragma unroll_loop_start
                for ( int i = 0; i < NUM_SPOT_LIGHTS; i ++ ) {

                    spotLight = spotLights[ i ];

                    getSpotLightInfo( spotLight, geometry, directLight );

                    // spot lights are ordered [shadows with maps, shadows without maps, maps without shadows, none]
                    #if ( UNROLLED_LOOP_INDEX < NUM_SPOT_LIGHT_SHADOWS_WITH_MAPS )
                    #define SPOT_LIGHT_MAP_INDEX UNROLLED_LOOP_INDEX
                    #elif ( UNROLLED_LOOP_INDEX < NUM_SPOT_LIGHT_SHADOWS )
                    #define SPOT_LIGHT_MAP_INDEX NUM_SPOT_LIGHT_MAPS
                    #else
                    #define SPOT_LIGHT_MAP_INDEX ( UNROLLED_LOOP_INDEX - NUM_SPOT_LIGHT_SHADOWS + NUM_SPOT_LIGHT_SHADOWS_WITH_MAPS )
                    #endif

                    #if ( SPOT_LIGHT_MAP_INDEX < NUM_SPOT_LIGHT_MAPS )
                        spotLightCoord = vSpotLightCoord[ i ].xyz / vSpotLightCoord[ i ].w;
                        inSpotLightMap = all( lessThan( abs( spotLightCoord * 2. - 1. ), vec3( 1.0 ) ) );
                        spotColor = texture2D( spotLightMap[ SPOT_LIGHT_MAP_INDEX ], spotLightCoord.xy );
                        directLight.color = inSpotLightMap ? directLight.color * spotColor.rgb : directLight.color;
                    #endif

                    #undef SPOT_LIGHT_MAP_INDEX

                    #if defined( USE_SHADOWMAP ) && ( UNROLLED_LOOP_INDEX < NUM_SPOT_LIGHT_SHADOWS )
                    spotLightShadow = spotLightShadows[ i ];
                    directLight.color *= ( directLight.visible && receiveShadow ) ? getShadow( spotShadowMap[ i ], spotLightShadow.shadowMapSize, spotLightShadow.shadowBias, spotLightShadow.shadowRadius, vSpotLightCoord[ i ] ) : 1.0;
                    #endif

                    RE_Direct( directLight, geometry, material, reflectedLight );

                }
                #pragma unroll_loop_end

            #endif

            #if ( NUM_DIR_LIGHTS > 0 ) && defined( RE_Direct )

                DirectionalLight directionalLight;
                #if defined( USE_SHADOWMAP ) && NUM_DIR_LIGHT_SHADOWS > 0
                DirectionalLightShadow directionalLightShadow;
                #endif

                #pragma unroll_loop_start
                for ( int i = 0; i < NUM_DIR_LIGHTS; i ++ ) {

                    directionalLight = directionalLights[ i ];

                    getDirectionalLightInfo( directionalLight, geometry, directLight );

                    #if defined( USE_SHADOWMAP ) && ( UNROLLED_LOOP_INDEX < NUM_DIR_LIGHT_SHADOWS )
                    directionalLightShadow = directionalLightShadows[ i ];
                    directLight.color *= ( directLight.visible && receiveShadow ) ? getShadow( directionalShadowMap[ i ], directionalLightShadow.shadowMapSize, directionalLightShadow.shadowBias, directionalLightShadow.shadowRadius, vDirectionalShadowCoord[ i ] ) : 1.0;
                    #endif

                    RE_Direct( directLight, geometry, material, reflectedLight );

                }
                #pragma unroll_loop_end

            #endif

            #if ( NUM_RECT_AREA_LIGHTS > 0 ) && defined( RE_Direct_RectArea )

                RectAreaLight rectAreaLight;

                #pragma unroll_loop_start
                for ( int i = 0; i < NUM_RECT_AREA_LIGHTS; i ++ ) {

                    rectAreaLight = rectAreaLights[ i ];
                    RE_Direct_RectArea( rectAreaLight, geometry, material, reflectedLight );

                }
                #pragma unroll_loop_end

            #endif

            #if defined( RE_IndirectDiffuse )

                vec3 iblIrradiance = vec3( 0.0 );

                vec3 irradiance = getAmbientLightIrradiance( ambientLightColor );

                irradiance += getLightProbeIrradiance_mv( lightProbe, geometry.normal );

                #if ( NUM_HEMI_LIGHTS > 0 )

                    #pragma unroll_loop_start
                    for ( int i = 0; i < NUM_HEMI_LIGHTS; i ++ ) {

                        irradiance += getHemisphereLightIrradiance( hemisphereLights[ i ], geometry.normal );

                    }
                    #pragma unroll_loop_end

                #endif

            #endif

            #if defined( RE_IndirectSpecular )

                vec3 radiance = vec3( 0.0 );
                vec3 clearcoatRadiance = vec3( 0.0 );

            #endif
            `,lights_fragment_maps:`
            #if defined( RE_IndirectDiffuse )

            #ifdef USE_LIGHTMAP
        
                vec4 lightMapTexel = texture2D( lightMap, vLightMapUv );
                vec3 lightMapIrradiance = lightMapTexel.rgb * lightMapIntensity;
        
                irradiance += lightMapIrradiance;
        
            #endif
        
            #if defined( USE_ENVMAP ) && defined( STANDARD ) && defined( ENVMAP_TYPE_CUBE_UV )
        
                iblIrradiance += getIBLIrradiance( geometry.normal );
        
            #endif
        
            #endif
            
            #if defined( USE_ENVMAP ) && defined( RE_IndirectSpecular )
            
                radiance += getIBLRadiance_mv( geometry.viewDir, geometry.normal, material.roughness );
            
                #ifdef USE_CLEARCOAT
            
                    clearcoatRadiance += getIBLRadiance_mv( geometry.viewDir, geometry.clearcoatNormal, material.clearcoatRoughness );
            
                #endif
            
            #endif
            `}}static createClipLib(){return{clip_par_vertex:`
            #ifdef CLIP
            varying vec4 pos;  
            uniform vec2 u_basePoint;   
            #endif
            `,clip_vertex:`
            #ifdef CLIP
            pos=modelMatrix* vec4( transformed, 1.0 );  
            pos=vec4(pos.x-u_basePoint.x,pos.y,pos.z-u_basePoint.y,pos.w);  
            #endif
            `,clip_model_par_vertex:`
            #ifdef MCLIP
            varying vec4 pos;  
            uniform vec3 u_basePoint;
            uniform vec3 basePosition; 
            #endif
            `,clip_model_vertex:`
            #ifdef MCLIP
            //pos=modelMatrix* vec4( transformed, 1.0 ); 
            //pos.xyz+=basePosition;           
            //pos=vec4(pos.x-u_basePoint.x,pos.y-u_basePoint.y,pos.z-u_basePoint.z,pos.w); 
            pos=mvPosition; 
            #endif
            `,clip_par_fragment:`
            #ifdef CLIP
            varying vec4 pos;  
            uniform mediump vec2 u_clipPoints[CLIP];
            uniform mediump int u_clipPointIndex[CLIP_START_END];
            #ifdef CLIP_ALTITUDE
            uniform mediump float u_clipAltitude[CLIP_ALTITUDE];
            #endif

            bool contains(vec3 pos,int startIndex,int endIndex){
                vec2 pt=pos.xz;
                bool c=false;
                mediump vec2 iPoint=vec2(0.0);
                mediump vec2 jPoint=vec2(0.0);
                for(int i=startIndex;i<endIndex;i++){
                    if(i==startIndex){
                        iPoint=u_clipPoints[startIndex];
                        jPoint=u_clipPoints[endIndex-1];
                    }else{
                        iPoint=u_clipPoints[i];
                        jPoint=u_clipPoints[i-1];
                    }
                    if(((iPoint.x>pt.x)!=(jPoint.x>pt.x))&&(pt.y < (jPoint.y - iPoint.y) * (pt.x - iPoint.x) / (jPoint.x - iPoint.x) + iPoint.y)){
                        c=!c;
                    }
                }          
                return c;
            }
            bool contains(vec3 pos,int start,int end);
            #endif
            `,clip_model_par_fragment:`
            #ifdef MCLIP
            varying vec4 pos;  
            uniform vec3 u_basePoint;
            uniform mediump vec2 u_clipPoints[MCLIP];
            uniform mediump int u_clipPointIndex[CLIP_START_END];
            uniform mediump vec4 u_clipPlane[CLIP_POLYGON_COUNT];
            uniform mediump int u_clipPrjType[CLIP_POLYGON_COUNT];
            uniform mediump float u_clipDistanceRange[CLIP_POLYGON_COUNT*2];

            bool contains(vec2 pt,int startIndex,int endIndex){
                bool c=false;
                mediump vec2 iPoint=vec2(0.0);
                mediump vec2 jPoint=vec2(0.0);
                for(int i=startIndex;i<endIndex;i++){
                    if(i==startIndex){
                        iPoint=u_clipPoints[startIndex];
                        jPoint=u_clipPoints[endIndex-1];
                    }else{
                        iPoint=u_clipPoints[i];
                        jPoint=u_clipPoints[i-1];
                    }
                    if(((iPoint.x>pt.x)!=(jPoint.x>pt.x))&&(pt.y < (jPoint.y - iPoint.y) * (pt.x - iPoint.x) / (jPoint.x - iPoint.x) + iPoint.y)){
                        c=!c;
                    }
                }          
                return c;
            }
            vec2 transformPt(float distance,vec4 plane,int i){
                vec3 prjPoint=pos.xyz;
                prjPoint+=plane.xyz*(-distance);
                vec2 pt=prjPoint.xz;
                if(u_clipPrjType[i]==1){
                    pt=prjPoint.yz;
                }else if(u_clipPrjType[i]==2){
                    pt=prjPoint.xy;
                }
                return pt;
            }
            vec2 transformPt(float distance,vec4 plane,int i);
            bool contains(vec2 pt,int start,int end);
            #endif
            `,clip_fragment:`
            #ifdef CLIP
             #ifdef CLIP_INNER
                int canVisible=0;
                for(int i=0;i<CLIP_START_END;i+=2){
                    if(contains(pos.xyz,u_clipPointIndex[i],u_clipPointIndex[i+1])){
                        canVisible=1;
                        break;
                    }
                }
                if(canVisible==0){
                    discard;
                }
             #else
                for(int i=0;i<CLIP_START_END;i+=2){
                    if(contains(pos.xyz,u_clipPointIndex[i],u_clipPointIndex[i+1])){
                        #ifdef CLIP_ALTITUDE
                        if(pos.y>=u_clipAltitude[i/2]){//
                            discard;
                        }
                        #else
                        discard;
                        #endif
                    }
                }
             #endif
            #endif
            `,clip_model_fragment:`
            #ifdef MCLIP
                #ifdef RESERVE_TYPE
                    int isReserve=0;
                    for(int i=0;i<CLIP_POLYGON_COUNT;i+=1){
                        //投影
                        vec4 plane=u_clipPlane[i];
                        float distance=dot(plane.xyz,pos.xyz)+plane.w;

                        vec2 pt=transformPt(distance,plane,i);
                        
                        if(contains(pt,u_clipPointIndex[i],u_clipPointIndex[i+1])){                                                   
                            if(distance>u_clipDistanceRange[i*2]&&distance<u_clipDistanceRange[i*2+1]){
                                isReserve=1;
                                break;
                            }
                        }
                    }
                    if(isReserve==0){
                        discard;
                    }
                #else
                    for(int i=0;i<CLIP_POLYGON_COUNT;i+=1){

                        //投影
                        vec4 plane=u_clipPlane[i];
                        float distance=dot(plane.xyz,pos.xyz)+plane.w;

                        vec2 pt=transformPt(distance,plane,i);

                        if(contains(pt,u_clipPointIndex[i],u_clipPointIndex[i+1])){                                                   
                            if(distance>u_clipDistanceRange[i*2]&&distance<u_clipDistanceRange[i*2+1]){
                                discard;
                            }
                        }
                    }
                #endif
            #endif
            `,transform_par_vertex:`
                #ifdef BOXCLIP
                attribute float a_boxclipstate;
                uniform vec3 u_basePoint;
                varying vec3 v_relativePos;
                varying float v_boxclipstate;
                #endif

                #ifdef GHOST
                uniform vec3 offset;
                uniform vec3 scale;
                uniform vec3 transformCenter;
                uniform mat4 rotation;
                uniform float onlyOffset;
                #endif

                #ifdef EXPLOSION
                uniform vec3 explosionValue;
                #endif

                #ifdef BATCHTRANSFORM
                attribute float a_transform;
                uniform vec3 batchOffset[128];
                #endif

                vec3 transformPosition(vec3 position){
                    #ifdef EXPLOSION
                    position+=explosionValue;
                    #endif
                    #ifdef GHOST
                        vec4 srcPosition=vec4(position, 1.0);
                        srcPosition+=vec4(offset,0.0);
                        vec3 dValue=srcPosition.xyz-transformCenter;
                        dValue*=scale;
                        vec4 fxyzw=rotation*vec4(dValue,1.0);
                        position=vec4(transformCenter+fxyzw.xyz,1.0).rgb;
                    #elif defined(BATCHTRANSFORM)
                    position=a_transform>0.0?position+batchOffset[int(a_transform)-1]:position;
                    #endif
                    return position;
                }
            `,transform_vertex:`
                #ifdef EXPLOSION
                transformed+=explosionValue;
                #endif

                #ifdef GHOST
                vec4 srcPosition=vec4(transformed, 1.0);
                srcPosition+=vec4(offset,0.0);
                vec4 realWorldPosition=srcPosition;
                vec3 dValue=srcPosition.xyz-transformCenter;
                dValue*=scale;
                vec4 fxyzw=rotation*vec4(dValue,1.0);
                realWorldPosition=vec4(transformCenter+fxyzw.xyz,1.0);
                transformed=realWorldPosition.xyz;
                vec4 mvPosition=modelViewMatrix*realWorldPosition;
                #elif defined(BATCHTRANSFORM)
                    vec3 finalPos=a_transform>0.0?transformed+batchOffset[int(a_transform)-1]:transformed;
                    transformed=finalPos;
                    vec4 mvPosition = modelViewMatrix * vec4( finalPos, 1.0 );
                #else
                    vec4 mvPosition = modelViewMatrix * vec4( transformed, 1.0 );
                #endif
            `,transform_worldPosition_vertex:`
                #if defined( USE_ENVMAP ) || defined( DISTANCE ) || defined ( USE_SHADOWMAP ) || defined ( USE_TRANSMISSION ) || NUM_SPOT_LIGHT_COORDS > 0

                #ifdef GHOST
                    vec4 worldPosition=modelMatrix*realWorldPosition;;
                #elif defined(BATCHTRANSFORM)
                    vec4 worldPosition = modelMatrix * vec4( finalPos, 1.0 );
                #else
                    vec4 worldPosition = modelMatrix * vec4( transformed, 1.0 );
                #endif
        
                #endif
            `}}}function Vo(r,e,t){return t?(Object.keys(e).forEach(i=>{i!="install"&&(r[t+i]=e[i])}),r):Object.assign(r,this)}function Wa(r,{defines:e="",header:t="",main:i="",...a}){let s=r;const o=(l,h,u)=>l.split(h).join(u);return Object.keys(a).forEach(l=>{s=o(s,l,a[l])}),s=s.replace("void main() {",`
    ${t}
    void main() {
      ${i}
    `),`
    ${Object.keys(e).map(l=>`#define ${l} ${e[l]}`).join(`
`)}
    ${s}
  `}function ja(r,e){if(r.image&&r.image.videoWidth!==0&&r.image.videoHeight!==0)return;const t=setInterval(()=>{if(r.image&&r.image.videoWidth!==0&&r.image.videoHeight!==0)return clearInterval(t),e(r)},16)}class Oo extends c.MeshPhysicalMaterial{constructor({camera:t=new c.PerspectiveCamera,texture:i=new c.Texture,textureScale:a=1,textureOffset:s=new c.Vector2,backgroundOpacity:o=1,cover:n=!1,...l}={}){if(!i.isTexture)throw new Error("Invalid texture passed to the ProjectedMaterial");if(!t.isCamera)throw new Error("Invalid camera passed to the ProjectedMaterial");o<1&&!l.transparent&&console.warn('You have to pass "transparent: true" to the ProjectedMaterial for the backgroundOpacity option to work');super(l);Ue(this,ie);Ue(this,st);Ue(this,Re,void 0);Ue(this,Xe,void 0);Ue(this,Ye,void 0);Ue(this,rt,()=>{this.uniforms.projectionMatrixCamera.value.copy(this.camera.projectionMatrix),oe(this,ie,Me).call(this)});Object.defineProperty(this,"isProjectedMaterial",{value:!0}),Ve(this,Re,t),Ve(this,Xe,n),Ve(this,Ye,a);const[h,u]=qa(i,t,a,n);this.uniforms={projectedTexture:{value:i},isTextureLoaded:{value:!!i.image},isTextureProjected:{value:!1},backgroundOpacity:{value:o},viewMatrixCamera:{value:new c.Matrix4},projectionMatrixCamera:{value:new c.Matrix4},projPosition:{value:new c.Vector3},projDirection:{value:new c.Vector3(0,0,-1)},savedModelMatrix:{value:new c.Matrix4},widthScaled:{value:h},heightScaled:{value:u},textureOffset:{value:s}},this.onBeforeCompile=d=>{Object.assign(this.uniforms,d.uniforms),d.uniforms=this.uniforms,this.camera.isOrthographicCamera&&(d.defines.ORTHOGRAPHIC=""),d.vertexShader=Wa(d.vertexShader,{header:`
          uniform mat4 viewMatrixCamera;
          uniform mat4 projectionMatrixCamera;

          #ifdef USE_INSTANCING
          attribute vec4 savedModelMatrix0;
          attribute vec4 savedModelMatrix1;
          attribute vec4 savedModelMatrix2;
          attribute vec4 savedModelMatrix3;
          #else
          uniform mat4 savedModelMatrix;
          #endif

          varying vec3 vSavedNormal;
          varying vec4 vTexCoords;
          #ifndef ORTHOGRAPHIC
          varying vec4 vWorldPosition;
          #endif
        `,main:`
          #ifdef USE_INSTANCING
          mat4 savedModelMatrix = mat4(
            savedModelMatrix0,
            savedModelMatrix1,
            savedModelMatrix2,
            savedModelMatrix3
          );
          #endif

          vSavedNormal = mat3(savedModelMatrix) * normal;
          vTexCoords = projectionMatrixCamera * viewMatrixCamera * savedModelMatrix * vec4(position, 1.0);
          #ifndef ORTHOGRAPHIC
          vWorldPosition = savedModelMatrix * vec4(position, 1.0);
          #endif
        `}),d.fragmentShader=Wa(d.fragmentShader,{header:`
          uniform sampler2D projectedTexture;
          uniform bool isTextureLoaded;
          uniform bool isTextureProjected;
          uniform float backgroundOpacity;
          uniform vec3 projPosition;
          uniform vec3 projDirection;
          uniform float widthScaled;
          uniform float heightScaled;
          uniform vec2 textureOffset;

          varying vec3 vSavedNormal;
          varying vec4 vTexCoords;
          #ifndef ORTHOGRAPHIC
          varying vec4 vWorldPosition;
          #endif

          float mapRange(float value, float min1, float max1, float min2, float max2) {
            return min2 + (value - min1) * (max2 - min2) / (max1 - min1);
          }
        `,"vec4 diffuseColor = vec4( diffuse, opacity );":`
          // clamp the w to make sure we don't project behind
          float w = max(vTexCoords.w, 0.0);

          vec2 uv = (vTexCoords.xy / w) * 0.5 + 0.5;

          uv += textureOffset;

          // apply the corrected width and height
          uv.x = mapRange(uv.x, 0.0, 1.0, 0.5 - widthScaled / 2.0, 0.5 + widthScaled / 2.0);
          uv.y = mapRange(uv.y, 0.0, 1.0, 0.5 - heightScaled / 2.0, 0.5 + heightScaled / 2.0);

          // this makes sure we don't sample out of the texture
          bool isInTexture = (max(uv.x, uv.y) <= 1.0 && min(uv.x, uv.y) >= 0.0);

          // this makes sure we don't render also the back of the object
          #ifdef ORTHOGRAPHIC
          vec3 projectorDirection = projDirection;
          #else
          vec3 projectorDirection = normalize(projPosition - vWorldPosition.xyz);
          #endif
          float dotProduct = dot(vSavedNormal, projectorDirection);
          bool isFacingProjector = dotProduct > 0.0000001;


          vec4 diffuseColor = vec4(diffuse, opacity * backgroundOpacity);

          if (isFacingProjector && isInTexture && isTextureLoaded && isTextureProjected) {
            vec4 textureColor = texture2D(projectedTexture, uv);

            // apply the material opacity
            textureColor.a *= opacity;

            // https://learnopengl.com/Advanced-OpenGL/Blending
            diffuseColor = textureColor * textureColor.a + diffuseColor * (1.0 - textureColor.a);
          }
        `})},window.addEventListener("resize",De(this,rt)),ja(i,()=>{this.uniforms.isTextureLoaded.value=!0,this.dispatchEvent({type:"textureload"}),oe(this,ie,Me).call(this)})}get camera(){return De(this,Re)}set camera(t){if(!t||!t.isCamera)throw new Error("Invalid camera set to the ProjectedMaterial");if(t.type!==De(this,Re).type)throw new Error("Cannot change camera type after the material has been created. Use another material.");Ve(this,Re,t),oe(this,ie,Me).call(this)}get texture(){return this.uniforms.projectedTexture.value}set texture(t){if(!(t!=null&&t.isTexture))throw new Error("Invalid texture set to the ProjectedMaterial");this.uniforms.projectedTexture.value=t,this.uniforms.isTextureLoaded.value=!!t.image,this.uniforms.isTextureLoaded.value?oe(this,ie,Me).call(this):ja(t,()=>{this.uniforms.isTextureLoaded.value=!0,this.dispatchEvent({type:"textureload"}),oe(this,ie,Me).call(this)})}get textureScale(){return De(this,Ye)}set textureScale(t){Ve(this,Ye,t),oe(this,ie,Me).call(this)}get textureOffset(){return this.uniforms.textureOffset.value}set textureOffset(t){this.uniforms.textureOffset.value=t}get backgroundOpacity(){return this.uniforms.backgroundOpacity.value}set backgroundOpacity(t){this.uniforms.backgroundOpacity.value=t,t<1&&!this.transparent&&console.warn('You have to pass "transparent: true" to the ProjectedMaterial for the backgroundOpacity option to work')}get cover(){return De(this,Xe)}set cover(t){Ve(this,Xe,t),oe(this,ie,Me).call(this)}project(t){if(!(Array.isArray(t.material)?t.material.some(i=>i.isProjectedMaterial):t.material.isProjectedMaterial))throw new Error("The mesh material must be a ProjectedMaterial");if(!(Array.isArray(t.material)?t.material.some(i=>i===this):t.material===this))throw new Error("The provided mesh doesn't have the same material as where project() has been called from");if(t.updateWorldMatrix(!0,!1),this.uniforms.savedModelMatrix.value.copy(t.matrixWorld),Array.isArray(t.material)){const i=t.material.indexOf(this);t.material[i].transparent||console.warn(`You have to pass "transparent: true" to the ProjectedMaterial if you're working with multiple materials.`),i>0&&(this.uniforms.backgroundOpacity.value=0)}oe(this,st,Ii).call(this)}projectInstanceAt(t,i,a,{forceCameraSave:s=!1}={}){if(!i.isInstancedMesh)throw new Error("The provided mesh is not an InstancedMesh");if(!(Array.isArray(i.material)?i.material.every(o=>o.isProjectedMaterial):i.material.isProjectedMaterial))throw new Error("The InstancedMesh material must be a ProjectedMaterial");if(!(Array.isArray(i.material)?i.material.some(o=>o===this):i.material===this))throw new Error("The provided InstancedMeshhave't i samenclude thas e material where project() has been called from");if(!i.geometry.attributes.savedModelMatrix0||!i.geometry.attributes.savedModelMatrix1||!i.geometry.attributes.savedModelMatrix2||!i.geometry.attributes.savedModelMatrix3)throw new Error("No allocated data found on the geometry, please call 'allocateProjectionData(geometry, instancesCount)'");if(i.geometry.attributes.savedModelMatrix0.setXYZW(t,a.elements[0],a.elements[1],a.elements[2],a.elements[3]),i.geometry.attributes.savedModelMatrix1.setXYZW(t,a.elements[4],a.elements[5],a.elements[6],a.elements[7]),i.geometry.attributes.savedModelMatrix2.setXYZW(t,a.elements[8],a.elements[9],a.elements[10],a.elements[11]),i.geometry.attributes.savedModelMatrix3.setXYZW(t,a.elements[12],a.elements[13],a.elements[14],a.elements[15]),Array.isArray(i.material)){const o=i.material.indexOf(this);i.material[o].transparent||console.warn(`You have to pass "transparent: true" to the ProjectedMaterial if you're working with multiple materials.`),o>0&&(this.uniforms.backgroundOpacity.value=0)}(t===0||s)&&oe(this,st,Ii).call(this)}copy(t){return super.copy(t),this.camera=t.camera,this.texture=t.texture,this.textureScale=t.textureScale,this.textureOffset=t.textureOffset,this.cover=t.cover,this}dispose(){super.dispose(),window.removeEventListener("resize",De(this,rt))}}Re=new WeakMap,Xe=new WeakMap,Ye=new WeakMap,rt=new WeakMap,ie=new WeakSet,Me=function(){const[t,i]=qa(this.texture,this.camera,this.textureScale,this.cover);this.uniforms.widthScaled.value=t,this.uniforms.heightScaled.value=i},st=new WeakSet,Ii=function(){this.camera.updateProjectionMatrix(),this.camera.updateMatrixWorld(),this.camera.updateWorldMatrix();const t=this.camera.matrixWorldInverse,i=this.camera.projectionMatrix,a=this.camera.matrixWorld;this.uniforms.viewMatrixCamera.value.copy(t),this.uniforms.projectionMatrixCamera.value.copy(i),this.uniforms.projPosition.value.setFromMatrixPosition(a),this.uniforms.projDirection.value.set(0,0,1).applyMatrix4(a),this.uniforms.isTextureProjected.value=!0};function No(r){switch(r.type){case"PerspectiveCamera":return r.aspect;case"OrthographicCamera":{const e=Math.abs(r.right-r.left),t=Math.abs(r.top-r.bottom);return e/t}default:throw new Error(`${r.type} is currently not supported in ProjectedMaterial`)}}function qa(r,e,t,i){if(!r.image)return[1,1];if(r.image.videoWidth===0&&r.image.videoHeight===0)return[1,1];const a=r.image.naturalWidth||r.image.videoWidth||r.image.clientWidth,s=r.image.naturalHeight||r.image.videoHeight||r.image.clientHeight,o=a/s,n=No(e),l=1,h=l*(1/n);let u,d;return(i?o>n:o<n)?(u=1/(h*o/l*t),d=1/t):(d=1/(l*(1/o)/h*t),u=1/t),[u,d]}class Fo{constructor(){this._listeners={}}addEventListener(e,t){const i=this._listeners;i[e]===void 0&&(i[e]=[]),i[e].indexOf(t)===-1&&i[e].push(t)}removeEventListener(e,t){const a=this._listeners[e];if(a!==void 0){const s=a.indexOf(t);s!==-1&&a.splice(s,1)}}dispatchEvent(e){const i=this._listeners[e.type];if(i!==void 0){e.target=this;const a=i.slice(0);for(let s=0,o=a.length;s<o;s++)a[s].call(this,e)}}}var Vt=(r=>(r.HOLD_START="holdStart",r.HOLD_END="holdEnd",r.HOLDING="holding",r))(Vt||{});class zo extends Fo{constructor(e){super(),this._enabled=!0,this._holding=!1,this._intervalId=-1,this._deltaTime=0,this._elapsedTime=0,this._lastTime=0,this._holdStart=t=>{if(!this._enabled||this._holding)return;this._deltaTime=0,this._elapsedTime=0,this._lastTime=performance.now(),this.dispatchEvent({type:Vt.HOLD_START,deltaTime:this._deltaTime,elapsedTime:this._elapsedTime,originalEvent:t}),this._holding=!0;const i=()=>{this._intervalId=this.holdIntervalDelay?window.setTimeout(i,this.holdIntervalDelay):window.requestAnimationFrame(i);const a=performance.now();this._deltaTime=a-this._lastTime,this._elapsedTime+=this._deltaTime,this._lastTime=performance.now(),this.dispatchEvent({type:Vt.HOLDING,deltaTime:this._deltaTime,elapsedTime:this._elapsedTime})};this._intervalId=this.holdIntervalDelay?window.setTimeout(i,this.holdIntervalDelay):window.requestAnimationFrame(i)},this._holdEnd=t=>{if(!this._enabled||!this._holding)return;const i=performance.now();this._deltaTime=i-this._lastTime,this._elapsedTime+=this._deltaTime,this._lastTime=performance.now(),this.dispatchEvent({type:Vt.HOLD_END,deltaTime:this._deltaTime,elapsedTime:this._elapsedTime,originalEvent:t}),window.clearTimeout(this._intervalId),window.cancelAnimationFrame(this._intervalId),this._holding=!1},this.holdIntervalDelay=e}get enabled(){return this._enabled}set enabled(e){this._enabled!==e&&(this._enabled=e,this._enabled||this._holdEnd())}}class ko extends zo{constructor(e,t){super(t),this._holdStart=this._holdStart.bind(this),this._holdEnd=this._holdEnd.bind(this);const i=s=>{Ho(s)||s.keyCode===e&&this._holdStart(s)},a=s=>{s.keyCode===e&&this._holdEnd(s)};document.addEventListener("keydown",i),document.addEventListener("keyup",a),window.addEventListener("blur",this._holdEnd)}}function Ho(r){const e=r.target;return e.tagName==="INPUT"||e.tagName==="SELECT"||e.tagName==="TEXTAREA"||e.isContentEditable}P.BaseMatType=ii,P.Coordinate=et,P.EnvironmentLight=ar,P.Feature=$t,P.FeatureContainer=qi,P.FeatureType=fe,P.Global=Q,P.KeyboardKeyHold=ko,P.LayerOrderState=gt,P.LineGeometry=Je,P.Mapbox=Hs,P.MeshAttribute=Eo,P.ParticleEngine=Le,P.ParticleTween=xe,P.PathGeometry=Do,P.PathPointList=Bo,P.Polygon=Oa,P.PrimitiveStatus=Se,P.PrimitiveType=Xi,P.ProjectedMaterial=Oo,P.RayTracingRenderer=ji,P.RenderLib=ft,P.ThreeBaseMaterial=Zi,P.Type=Ie,P.Utility=N,P._3dTiles=yi,P.mvBox3=Z,P.mvGeometry=Fe,P.mvLineBasicMaterial=pt,P.mvMaterial=Ke,P.mvMesh=vt,P.mvMeshBasicMaterial=ze,P.mvMeshPhongMaterial=mt,P.mvObject3D=Ji,P.mvPrimitive=Fs,P.mvShaderLib=Uo});
