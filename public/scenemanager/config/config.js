// 发布环境配置
(function () {
    window.IP_CONFIG = {
        UPFRONT_URL: window.location.origin, // 线上前端地址，自动获取地址（协议、域名、端口号）
        BASE_URL: '//www.probim.cn:3232', // 基础api baseUrl
        WEBSOCKET_URL: '//www.probim.cn:3232/vothing', // ws baseUrl
        SOURCES_URL: '//www.probim.cn:3230/',// multiverse.js所在的路径,用于获取静态资源文件
        MODEL_URL: '//www.probim.cn:3231', // 模型服务 数据请求地址
        RESOURCES_URL: '//resources.vothing.com', //静态远程资源
        PROJECT_ID: '',//项目id
        PANO_URL: '//', // 全景图
        DGCC_URL: "//**************:3000/api/v1/scene/GetBaiduMapLocation", // 搜索定位服务
        MapboxToken: 'YOUR_MAPBOX_ACCESS_TOKEN'
    }
    window.PROJECT_TITLE = '场景管理器', // 项目名
        window.LBS_ENABLED = true //搜索定位功能是否启用，根据部署环境修改
    window.UI_CONFIG = {
        lang: 'cn'
    }
})()