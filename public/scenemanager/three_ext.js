mvDefine(["exports","./three"],function(Z,r){"use strict";function bn(c,e=!1){const t=c[0].index!==null,i=new Set(Object.keys(c[0].attributes)),n=new Set(Object.keys(c[0].morphAttributes)),s={},A={},o=c[0].morphTargetsRelative,a=new r.BufferGeometry;let l=0;for(let g=0;g<c.length;++g){const u=c[g];let f=0;if(t!==(u.index!==null))return console.error("THREE.BufferGeometryUtils: .mergeGeometries() failed with geometry at index "+g+". All geometries must have compatible attributes; make sure index attribute exists among all geometries, or in none of them."),null;for(const h in u.attributes){if(!i.has(h))return console.error("THREE.BufferGeometryUtils: .mergeGeometries() failed with geometry at index "+g+'. All geometries must have compatible attributes; make sure "'+h+'" attribute exists among all geometries, or in none of them.'),null;s[h]===void 0&&(s[h]=[]),s[h].push(u.attributes[h]),f++}if(f!==i.size)return console.error("THREE.BufferGeometryUtils: .mergeGeometries() failed with geometry at index "+g+". Make sure all geometries have the same number of attributes."),null;if(o!==u.morphTargetsRelative)return console.error("THREE.BufferGeometryUtils: .mergeGeometries() failed with geometry at index "+g+". .morphTargetsRelative must be consistent throughout all geometries."),null;for(const h in u.morphAttributes){if(!n.has(h))return console.error("THREE.BufferGeometryUtils: .mergeGeometries() failed with geometry at index "+g+".  .morphAttributes must be consistent throughout all geometries."),null;A[h]===void 0&&(A[h]=[]),A[h].push(u.morphAttributes[h])}if(e){let h;if(t)h=u.index.count;else if(u.attributes.position!==void 0)h=u.attributes.position.count;else return console.error("THREE.BufferGeometryUtils: .mergeGeometries() failed with geometry at index "+g+". The geometry must have either an index or a position attribute"),null;a.addGroup(l,h,g),l+=h}}if(t){let g=0;const u=[];for(let f=0;f<c.length;++f){const h=c[f].index;for(let p=0;p<h.count;++p)u.push(h.getX(p)+g);g+=c[f].attributes.position.count}a.setIndex(u)}for(const g in s){const u=Tt(s[g]);if(!u)return console.error("THREE.BufferGeometryUtils: .mergeGeometries() failed while trying to merge the "+g+" attribute."),null;a.setAttribute(g,u)}for(const g in A){const u=A[g][0].length;if(u===0)break;a.morphAttributes=a.morphAttributes||{},a.morphAttributes[g]=[];for(let f=0;f<u;++f){const h=[];for(let I=0;I<A[g].length;++I)h.push(A[g][I][f]);const p=Tt(h);if(!p)return console.error("THREE.BufferGeometryUtils: .mergeGeometries() failed while trying to merge the "+g+" morphAttribute."),null;a.morphAttributes[g].push(p)}}return a}function Tt(c){let e,t,i,n=0;for(let o=0;o<c.length;++o){const a=c[o];if(a.isInterleavedBufferAttribute)return console.error("THREE.BufferGeometryUtils: .mergeAttributes() failed. InterleavedBufferAttributes are not supported."),null;if(e===void 0&&(e=a.array.constructor),e!==a.array.constructor)return console.error("THREE.BufferGeometryUtils: .mergeAttributes() failed. BufferAttribute.array must be of consistent array types across matching attributes."),null;if(t===void 0&&(t=a.itemSize),t!==a.itemSize)return console.error("THREE.BufferGeometryUtils: .mergeAttributes() failed. BufferAttribute.itemSize must be consistent across matching attributes."),null;if(i===void 0&&(i=a.normalized),i!==a.normalized)return console.error("THREE.BufferGeometryUtils: .mergeAttributes() failed. BufferAttribute.normalized must be consistent across matching attributes."),null;n+=a.array.length}const s=new e(n);let A=0;for(let o=0;o<c.length;++o)s.set(c[o].array,A),A+=c[o].array.length;return new r.BufferAttribute(s,t,i)}function St(c,e){if(e===r.TrianglesDrawMode)return console.warn("THREE.BufferGeometryUtils.toTrianglesDrawMode(): Geometry already defined as triangles."),c;if(e===r.TriangleFanDrawMode||e===r.TriangleStripDrawMode){let t=c.getIndex();if(t===null){const A=[],o=c.getAttribute("position");if(o!==void 0){for(let a=0;a<o.count;a++)A.push(a);c.setIndex(A),t=c.getIndex()}else return console.error("THREE.BufferGeometryUtils.toTrianglesDrawMode(): Undefined position attribute. Processing not possible."),c}const i=t.count-2,n=[];if(e===r.TriangleFanDrawMode)for(let A=1;A<=i;A++)n.push(t.getX(0)),n.push(t.getX(A)),n.push(t.getX(A+1));else for(let A=0;A<i;A++)A%2===0?(n.push(t.getX(A)),n.push(t.getX(A+1)),n.push(t.getX(A+2))):(n.push(t.getX(A+2)),n.push(t.getX(A+1)),n.push(t.getX(A)));n.length/3!==i&&console.error("THREE.BufferGeometryUtils.toTrianglesDrawMode(): Unable to generate correct amount of triangles.");const s=c.clone();return s.setIndex(n),s.clearGroups(),s}else return console.error("THREE.BufferGeometryUtils.toTrianglesDrawMode(): Unknown draw mode:",e),c}class ve{constructor(){this.isPass=!0,this.enabled=!0,this.needsSwap=!0,this.clear=!1,this.renderToScreen=!1}setSize(){}render(){console.error("THREE.Pass: .render() must be implemented in derived pass.")}dispose(){}}const _n=new r.OrthographicCamera(-1,1,1,-1,0,1),je=new r.BufferGeometry;je.setAttribute("position",new r.Float32BufferAttribute([-1,3,0,-1,-1,0,3,-1,0],3)),je.setAttribute("uv",new r.Float32BufferAttribute([0,2,0,0,2,0],2));class Mt{constructor(e){this._mesh=new r.Mesh(je,e)}dispose(){this._mesh.geometry.dispose()}render(e){e.render(this._mesh,_n)}get material(){return this._mesh.material}set material(e){this._mesh.material=e}}class Gn extends r.DataTextureLoader{constructor(e){super(e),this.type=r.HalfFloatType}parse(e){const o=function(d,C){switch(d){case 1:console.error("THREE.RGBELoader Read Error: "+(C||""));break;case 2:console.error("THREE.RGBELoader Write Error: "+(C||""));break;case 3:console.error("THREE.RGBELoader Bad File Format: "+(C||""));break;default:case 4:console.error("THREE.RGBELoader: Error: "+(C||""))}return-1},u=`
`,f=function(d,C,x){C=C||1024;let S=d.pos,w=-1,T=0,M="",R=String.fromCharCode.apply(null,new Uint16Array(d.subarray(S,S+128)));for(;0>(w=R.indexOf(u))&&T<C&&S<d.byteLength;)M+=R,T+=R.length,S+=128,R+=String.fromCharCode.apply(null,new Uint16Array(d.subarray(S,S+128)));return-1<w?(x!==!1&&(d.pos+=T+w+1),M+R.slice(0,w)):!1},h=function(d){const C=/^#\?(\S+)/,x=/^\s*GAMMA\s*=\s*(\d+(\.\d+)?)\s*$/,m=/^\s*EXPOSURE\s*=\s*(\d+(\.\d+)?)\s*$/,S=/^\s*FORMAT=(\S+)\s*$/,w=/^\s*\-Y\s+(\d+)\s+\+X\s+(\d+)\s*$/,T={valid:0,string:"",comments:"",programtype:"RGBE",format:"",gamma:1,exposure:1,width:0,height:0};let M,R;if(d.pos>=d.byteLength||!(M=f(d)))return o(1,"no header found");if(!(R=M.match(C)))return o(3,"bad initial token");for(T.valid|=1,T.programtype=R[1],T.string+=M+`
`;M=f(d),M!==!1;){if(T.string+=M+`
`,M.charAt(0)==="#"){T.comments+=M+`
`;continue}if((R=M.match(x))&&(T.gamma=parseFloat(R[1])),(R=M.match(m))&&(T.exposure=parseFloat(R[1])),(R=M.match(S))&&(T.valid|=2,T.format=R[1]),(R=M.match(w))&&(T.valid|=4,T.height=parseInt(R[1],10),T.width=parseInt(R[2],10)),T.valid&2&&T.valid&4)break}return T.valid&2?T.valid&4?T:o(3,"missing image size specifier"):o(3,"missing format specifier")},p=function(d,C,x){const m=C;if(m<8||m>32767||d[0]!==2||d[1]!==2||d[2]&128)return new Uint8Array(d);if(m!==(d[2]<<8|d[3]))return o(3,"wrong scanline width");const S=new Uint8Array(4*C*x);if(!S.length)return o(4,"unable to allocate buffer space");let w=0,T=0;const M=4*m,R=new Uint8Array(4),U=new Uint8Array(M);let V=x;for(;V>0&&T<d.byteLength;){if(T+4>d.byteLength)return o(1);if(R[0]=d[T++],R[1]=d[T++],R[2]=d[T++],R[3]=d[T++],R[0]!=2||R[1]!=2||(R[2]<<8|R[3])!=m)return o(3,"bad rgbe scanline format");let O=0,P;for(;O<M&&T<d.byteLength;){P=d[T++];const L=P>128;if(L&&(P-=128),P===0||O+P>M)return o(3,"bad scanline data");if(L){const D=d[T++];for(let k=0;k<P;k++)U[O++]=D}else U.set(d.subarray(T,T+P),O),O+=P,T+=P}const y=m;for(let L=0;L<y;L++){let D=0;S[w]=U[L+D],D+=m,S[w+1]=U[L+D],D+=m,S[w+2]=U[L+D],D+=m,S[w+3]=U[L+D],w+=4}V--}return S},I=function(d,C,x,m){const S=d[C+3],w=Math.pow(2,S-128)/255;x[m+0]=d[C+0]*w,x[m+1]=d[C+1]*w,x[m+2]=d[C+2]*w,x[m+3]=1},E=function(d,C,x,m){const S=d[C+3],w=Math.pow(2,S-128)/255;x[m+0]=r.DataUtils.toHalfFloat(Math.min(d[C+0]*w,65504)),x[m+1]=r.DataUtils.toHalfFloat(Math.min(d[C+1]*w,65504)),x[m+2]=r.DataUtils.toHalfFloat(Math.min(d[C+2]*w,65504)),x[m+3]=r.DataUtils.toHalfFloat(1)},B=new Uint8Array(e);B.pos=0;const Q=h(B);if(Q!==-1){const d=Q.width,C=Q.height,x=p(B.subarray(B.pos),d,C);if(x!==-1){let m,S,w;switch(this.type){case r.UnsignedByteType:m=x,S=r.UnsignedByteType;break;case r.FloatType:w=x.length/4;const T=new Float32Array(w*4);for(let R=0;R<w;R++)I(x,R*4,T,R*4);m=T,S=r.FloatType;break;case r.HalfFloatType:w=x.length/4;const M=new Uint16Array(w*4);for(let R=0;R<w;R++)E(x,R*4,M,R*4);m=M,S=r.HalfFloatType;break;default:console.error("THREE.RGBELoader: unsupported type: ",this.type);break}return{width:d,height:C,data:m,header:Q.string,gamma:Q.gamma,exposure:Q.exposure,type:S}}}return null}setDataType(e){return this.type=e,this}load(e,t,i,n){function s(A,o){switch(A.type){case r.FloatType:case r.HalfFloatType:A.colorSpace=r.LinearSRGBColorSpace,A.minFilter=r.LinearFilter,A.magFilter=r.LinearFilter,A.generateMipmaps=!1,A.flipY=!0;break}t&&t(A,o)}return super.load(e,s,i,n)}}var Un=3007,kn=function(){var c=s(),e=new r.OrthographicCamera(-1,1,1,-1,0,1e3),t=new r.Scene,i=new r.Mesh(new r.PlaneGeometry(2,2),c);i.material.side=r.DoubleSide,t.add(i),t.add(e);var n=function(A,o,a){this.sourceTexture=A,this.resolution=a!==void 0?a:256,this.samplesPerLevel=o!==void 0?o:32;var l=this.sourceTexture.colorSpace===r.LinearSRGBColorSpace||this.sourceTexture.colorSpace===Un||this.sourceTexture.colorSpace===r.SRGBColorSpace;this.sourceTexture.minFilter=l?r.LinearFilter:r.NearestFilter,this.sourceTexture.magFilter=l?r.LinearFilter:r.NearestFilter,this.sourceTexture.generateMipmaps=this.sourceTexture.generateMipmaps&&l,this.cubeLods=[];var g=this.resolution,u={format:this.sourceTexture.format,magFilter:this.sourceTexture.magFilter,minFilter:this.sourceTexture.minFilter,type:this.sourceTexture.type,generateMipmaps:this.sourceTexture.generateMipmaps,anisotropy:this.sourceTexture.anisotropy,colorSpace:this.sourceTexture.colorSpace};this.numLods=Math.log(g)/Math.log(2)-2;for(var f=0;f<this.numLods;f++){var h=new r.WebGLCubeRenderTarget(g,u);h.texture.name="PMREMGenerator.cube"+f,this.cubeLods.push(h),g=Math.max(16,g/2)}};n.prototype={constructor:n,update:function(A){var o=this.sourceTexture.isCubeTexture?-1:1;c.defines.SAMPLES_PER_LEVEL=this.samplesPerLevel,c.uniforms.faceIndex.value=0,c.uniforms.envMap.value=this.sourceTexture,c.envMap=this.sourceTexture,c.needsUpdate=!0;var a=A.outputColorSpace,l=A.toneMapping,g=A.toneMappingExposure,u=A.getRenderTarget();A.toneMapping=r.LinearToneMapping,A.toneMappingExposure=1.5,A.outputColorSpace="";for(var f=0;f<this.numLods;f++){var h=f/(this.numLods-1);c.uniforms.roughness.value=h*.9,c.uniforms.tFlip.value=f==0?o:1;var p=this.cubeLods[f].width;c.uniforms.mapSize.value=p,this.renderToCubeMapTarget(A,this.cubeLods[f]),f<5&&(c.uniforms.envMap.value=this.cubeLods[f].texture)}A.setRenderTarget(u),A.toneMapping=l,A.toneMappingExposure=g,A.outputColorSpace=a},renderToCubeMapTarget:function(A,o){for(var a=0;a<6;a++)this.renderToCubeMapTargetFace(A,o,a)},renderToCubeMapTargetFace:function(A,o,a){c.uniforms.faceIndex.value=a,A.setRenderTarget(o,a),A.clear(),A.render(t,e)},dispose:function(){for(var A=0,o=this.cubeLods.length;A<o;A++)this.cubeLods[A].dispose();c.dispose()}};function s(){var A=new r.ShaderMaterial({defines:{SAMPLES_PER_LEVEL:20},uniforms:{faceIndex:{value:0},roughness:{value:.5},mapSize:{value:.5},envMap:{value:null},tFlip:{value:-1}},vertexShader:`varying vec2 vUv;
				void main() {
					vUv = uv;
					gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );
				}`,fragmentShader:`#include <common>
				varying vec2 vUv;
				uniform int faceIndex;
				uniform float roughness;
				uniform samplerCube envMap;
				uniform float mapSize;
				uniform float tFlip;
				
				float GGXRoughnessToBlinnExponent( const in float ggxRoughness ) {
					float a = ggxRoughness + 0.0001;
					a *= a;
					return ( 2.0 / a - 2.0 );
				}
				vec3 ImportanceSamplePhong(vec2 uv, mat3 vecSpace, float specPow) {
					float phi = uv.y * 2.0 * PI;
					float cosTheta = pow(1.0 - uv.x, 1.0 / (specPow + 1.0));
					float sinTheta = sqrt(1.0 - cosTheta * cosTheta);
					vec3 sampleDir = vec3(cos(phi) * sinTheta, sin(phi) * sinTheta, cosTheta);
					return vecSpace * sampleDir;
				}
				vec3 ImportanceSampleGGX( vec2 uv, mat3 vecSpace, float Roughness )
				{
					float a = Roughness * Roughness;
					float Phi = 2.0 * PI * uv.x;
					float CosTheta = sqrt( (1.0 - uv.y) / ( 1.0 + (a*a - 1.0) * uv.y ) );
					float SinTheta = sqrt( 1.0 - CosTheta * CosTheta );
					return vecSpace * vec3(SinTheta * cos( Phi ), SinTheta * sin( Phi ), CosTheta);
				}
				mat3 matrixFromVector(vec3 n) {
					float a = 1.0 / (1.0 + n.z);
					float b = -n.x * n.y * a;
					vec3 b1 = vec3(1.0 - n.x * n.x * a, b, -n.x);
					vec3 b2 = vec3(b, 1.0 - n.y * n.y * a, -n.y);
					return mat3(b1, b2, n);
				}
				
				vec4 testColorMap(float Roughness) {
					vec4 color;
					if(faceIndex == 0)
						color = vec4(1.0,0.0,0.0,1.0);
					else if(faceIndex == 1)
						color = vec4(0.0,1.0,0.0,1.0);
					else if(faceIndex == 2)
						color = vec4(0.0,0.0,1.0,1.0);
					else if(faceIndex == 3)
						color = vec4(1.0,1.0,0.0,1.0);
					else if(faceIndex == 4)
						color = vec4(0.0,1.0,1.0,1.0);
					else
						color = vec4(1.0,0.0,1.0,1.0);
					color *= ( 1.0 - Roughness );
					return color;
				}
				void main() {
					vec3 sampleDirection;
					vec2 uv = vUv*2.0 - 1.0;
					float offset = -1.0/mapSize;
					const float a = -1.0;
					const float b = 1.0;
					float c = -1.0 + offset;
					float d = 1.0 - offset;
					float bminusa = b - a;
					uv.x = (uv.x - a)/bminusa * d - (uv.x - b)/bminusa * c;
					uv.y = (uv.y - a)/bminusa * d - (uv.y - b)/bminusa * c;
					if (faceIndex==0) {
						sampleDirection = vec3(1.0, -uv.y, -uv.x);
					} else if (faceIndex==1) {
						sampleDirection = vec3(-1.0, -uv.y, uv.x);
					} else if (faceIndex==2) {
						sampleDirection = vec3(uv.x, 1.0, uv.y);
					} else if (faceIndex==3) {
						sampleDirection = vec3(uv.x, -1.0, -uv.y);
					} else if (faceIndex==4) {
						sampleDirection = vec3(uv.x, -uv.y, 1.0);
					} else {
						sampleDirection = vec3(-uv.x, -uv.y, -1.0);
					}
					vec3 correctedDirection = vec3( tFlip * sampleDirection.x, sampleDirection.yz );
					mat3 vecSpace = matrixFromVector( normalize( correctedDirection ) );
					vec3 rgbColor = vec3(0.0);
					const int NumSamples = SAMPLES_PER_LEVEL;
					vec3 vect;
					float weight = 0.0;
					for( int i = 0; i < NumSamples; i ++ ) {
						float sini = sin(float(i));
						float cosi = cos(float(i));
						float r = rand(vec2(sini, cosi));
						vect = ImportanceSampleGGX(vec2(float(i) / float(NumSamples), r), vecSpace, roughness);
						float dotProd = dot(vect, normalize(sampleDirection));
						weight += dotProd;
						//vec3 color = envMapTexelToLinear(textureCube(envMap, vect)).rgb;
						vec3 color = textureCube(envMap, vect).rgb;
						rgbColor.rgb += color;
					}
					rgbColor /= float(NumSamples);
					//rgbColor = testColorMap( roughness ).rgb;
					gl_FragColor = linearToOutputTexel( vec4( rgbColor, 1.0 ) );
				}`,blending:r.NoBlending});return A.type="PMREMGenerator",A}return n}(),Nn=function(){var c=new r.OrthographicCamera,e=new r.Scene,t=n(),i=function(s){this.cubeLods=s;var A=s[0].width*4,o=s[0].texture,a={format:o.format,magFilter:o.magFilter,minFilter:o.minFilter,type:o.type,generateMipmaps:o.generateMipmaps,anisotropy:o.anisotropy,colorSpace:o.colorSpace};a.colorSpace===r.LinearSRGBColorSpace&&(a.magFilter=r.LinearFilter,a.minFilter=r.LinearFilter),this.CubeUVRenderTarget=new r.WebGLRenderTarget(A,A,a),this.CubeUVRenderTarget.texture.name="PMREMCubeUVPacker.cubeUv",this.CubeUVRenderTarget.texture.mapping=r.CubeUVReflectionMapping,this.objects=[];var l=new r.PlaneGeometry(1,1),g=[];g.push(new r.Vector2(0,0)),g.push(new r.Vector2(1,0)),g.push(new r.Vector2(2,0)),g.push(new r.Vector2(0,1)),g.push(new r.Vector2(1,1)),g.push(new r.Vector2(2,1));var u=A;A=s[0].width;var f=0,h=4;this.numLods=Math.log(s[0].width)/Math.log(2)-2;for(var p=0;p<this.numLods;p++){var I=(u-u/h)*.5;A>16&&(h*=2);for(var E=A>16?6:1,B=0,Q=0,d=A,C=0;C<E;C++){for(var x=0;x<6;x++){var m=t.clone();m.uniforms.envMap.value=this.cubeLods[p].texture,m.envMap=this.cubeLods[p].texture,m.uniforms.faceIndex.value=x,m.uniforms.mapSize.value=d;var S=new r.Mesh(l,m);S.position.x=g[x].x*d-I+B,S.position.y=g[x].y*d-I+f+Q,S.material.side=r.BackSide,S.scale.setScalar(d),this.objects.push(S)}Q+=1.75*d,B+=1.25*d,d/=2}f+=2*A,A>16&&(A/=2)}};i.prototype={constructor:i,update:function(s){var A=this.cubeLods[0].width*4;c.left=-A*.5,c.right=A*.5,c.top=-A*.5,c.bottom=A*.5,c.near=0,c.far=1,c.updateProjectionMatrix();for(var o=0;o<this.objects.length;o++)e.add(this.objects[o]);var a=s.outputColorSpace,l=s.toneMapping,g=s.toneMappingExposure,u=s.getRenderTarget();s.outputColorSpace=!1,s.toneMapping=r.LinearToneMapping,s.toneMappingExposure=1,s.setRenderTarget(this.CubeUVRenderTarget),s.render(e,c),s.setRenderTarget(u),s.toneMapping=l,s.toneMappingExposure=g,s.outputColorSpace=a;for(var o=0;o<this.objects.length;o++)e.remove(this.objects[o])},dispose:function(){for(var s=0,A=this.objects.length;s<A;s++)this.objects[s].material.dispose();this.objects[0].geometry.dispose()}};function n(){var s=new r.ShaderMaterial({uniforms:{faceIndex:{value:0},mapSize:{value:0},envMap:{value:null},testColor:{value:new r.Vector3(1,1,1)}},vertexShader:"precision highp float;        varying vec2 vUv;        void main() {          vUv = uv;          gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );        }",fragmentShader:"precision highp float;        varying vec2 vUv;        uniform samplerCube envMap;        uniform float mapSize;        uniform vec3 testColor;        uniform int faceIndex;                void main() {          vec3 sampleDirection;          vec2 uv = vUv;          uv = uv * 2.0 - 1.0;          uv.y *= -1.0;          if(faceIndex == 0) {            sampleDirection = normalize(vec3(1.0, uv.y, -uv.x));          } else if(faceIndex == 1) {            sampleDirection = normalize(vec3(uv.x, 1.0, uv.y));          } else if(faceIndex == 2) {            sampleDirection = normalize(vec3(uv.x, uv.y, 1.0));          } else if(faceIndex == 3) {            sampleDirection = normalize(vec3(-1.0, uv.y, uv.x));          } else if(faceIndex == 4) {            sampleDirection = normalize(vec3(uv.x, -1.0, -uv.y));          } else {            sampleDirection = normalize(vec3(-uv.x, uv.y, -1.0));          }		  vec4 color = textureCube( envMap, sampleDirection ) ;          gl_FragColor = linearToOutputTexel( color );        }",blending:r.NoBlending});return s.type="PMREMCubeUVPacker",s}return i}();class Pn extends r.Scene{constructor(){super();const e=new r.BoxGeometry;e.deleteAttribute("uv");const t=new r.MeshStandardMaterial({side:r.BackSide}),i=new r.MeshStandardMaterial,n=new r.PointLight(16777215,5,28,2);n.position.set(.418,16.199,.3),this.add(n);const s=new r.Mesh(e,t);s.position.set(-.757,13.219,.717),s.scale.set(31.713,28.305,28.591),this.add(s);const A=new r.Mesh(e,i);A.position.set(-10.906,2.009,1.846),A.rotation.set(0,-.195,0),A.scale.set(2.328,7.905,4.651),this.add(A);const o=new r.Mesh(e,i);o.position.set(-5.607,-.754,-.758),o.rotation.set(0,.994,0),o.scale.set(1.97,1.534,3.955),this.add(o);const a=new r.Mesh(e,i);a.position.set(6.167,.857,7.803),a.rotation.set(0,.561,0),a.scale.set(3.927,6.285,3.687),this.add(a);const l=new r.Mesh(e,i);l.position.set(-2.017,.018,6.124),l.rotation.set(0,.333,0),l.scale.set(2.002,4.566,2.064),this.add(l);const g=new r.Mesh(e,i);g.position.set(2.291,-.756,-2.621),g.rotation.set(0,-.286,0),g.scale.set(1.546,1.552,1.496),this.add(g);const u=new r.Mesh(e,i);u.position.set(-2.193,-.369,-5.547),u.rotation.set(0,.516,0),u.scale.set(3.875,3.487,2.986),this.add(u);const f=new r.Mesh(e,Te(50));f.position.set(-16.116,14.37,8.208),f.scale.set(.1,2.428,2.739),this.add(f);const h=new r.Mesh(e,Te(50));h.position.set(-16.109,18.021,-8.207),h.scale.set(.1,2.425,2.751),this.add(h);const p=new r.Mesh(e,Te(17));p.position.set(14.904,12.198,-1.832),p.scale.set(.15,4.265,6.331),this.add(p);const I=new r.Mesh(e,Te(43));I.position.set(-.462,8.89,14.52),I.scale.set(4.38,5.441,.088),this.add(I);const E=new r.Mesh(e,Te(20));E.position.set(3.235,11.486,-12.541),E.scale.set(2.5,2,.1),this.add(E);const B=new r.Mesh(e,Te(100));B.position.set(0,20,0),B.scale.set(1,.1,1),this.add(B)}dispose(){const e=new Set;this.traverse(t=>{t.isMesh&&(e.add(t.geometry),e.add(t.material))});for(const t of e)t.dispose()}}function Te(c){const e=new r.MeshBasicMaterial;return e.color.setScalar(c),e}class On extends r.Loader{constructor(e){super(e),this.dracoLoader=null,this.ktx2Loader=null,this.meshoptDecoder=null,this.pluginCallbacks=[],this.register(function(t){return new Kn(t)}),this.register(function(t){return new $n(t)}),this.register(function(t){return new ei(t)}),this.register(function(t){return new ti(t)}),this.register(function(t){return new zn(t)}),this.register(function(t){return new Xn(t)}),this.register(function(t){return new jn(t)}),this.register(function(t){return new Wn(t)}),this.register(function(t){return new Yn(t)}),this.register(function(t){return new Zn(t)}),this.register(function(t){return new Jn(t)}),this.register(function(t){return new Vn(t)}),this.register(function(t){return new ni(t)}),this.register(function(t){return new ii(t)})}load(e,t,i,n){const s=this;let A;this.resourcePath!==""?A=this.resourcePath:this.path!==""?A=this.path:A=r.LoaderUtils.extractUrlBase(e),this.manager.itemStart(e);const o=function(l){n?n(l):console.error(l),s.manager.itemError(e),s.manager.itemEnd(e)},a=new r.FileLoader(this.manager);a.setPath(this.path),a.setResponseType("arraybuffer"),a.setRequestHeader(this.requestHeader),a.setWithCredentials(this.withCredentials),a.load(e,function(l){try{s.parse(l,A,function(g){t(g),s.manager.itemEnd(e)},o)}catch(g){o(g)}},i,o)}setDRACOLoader(e){return this.dracoLoader=e,this}setDDSLoader(){throw new Error('THREE.GLTFLoader: "MSFT_texture_dds" no longer supported. Please update to "KHR_texture_basisu".')}setKTX2Loader(e){return this.ktx2Loader=e,this}setMeshoptDecoder(e){return this.meshoptDecoder=e,this}register(e){return this.pluginCallbacks.indexOf(e)===-1&&this.pluginCallbacks.push(e),this}unregister(e){return this.pluginCallbacks.indexOf(e)!==-1&&this.pluginCallbacks.splice(this.pluginCallbacks.indexOf(e),1),this}parse(e,t,i,n){let s;const A={},o={},a=new TextDecoder;if(typeof e=="string")s=JSON.parse(e);else if(e instanceof ArrayBuffer)if(a.decode(new Uint8Array(e,0,4))===vt){try{A[J.KHR_BINARY_GLTF]=new si(e)}catch(u){n&&n(u);return}s=JSON.parse(A[J.KHR_BINARY_GLTF].content)}else s=JSON.parse(a.decode(e));else s=e;if(s.asset===void 0||s.asset.version[0]<2){n&&n(new Error("THREE.GLTFLoader: Unsupported asset. glTF versions >=2.0 are supported."));return}const l=new Ii(s,{path:t||this.resourcePath||"",crossOrigin:this.crossOrigin,requestHeader:this.requestHeader,manager:this.manager,ktx2Loader:this.ktx2Loader,meshoptDecoder:this.meshoptDecoder});l.fileLoader.setRequestHeader(this.requestHeader);for(let g=0;g<this.pluginCallbacks.length;g++){const u=this.pluginCallbacks[g](l);o[u.name]=u,A[u.name]=!0}if(s.extensionsUsed){const g=["CESIUM_RTC"];for(let u=0;u<s.extensionsUsed.length;++u){const f=s.extensionsUsed[u],h=s.extensionsRequired||[];if(g.indexOf(f)==-1)switch(f){case J.KHR_MATERIALS_UNLIT:A[f]=new qn;break;case J.KHR_DRACO_MESH_COMPRESSION:A[f]=new Ai(s,this.dracoLoader);break;case J.KHR_TEXTURE_TRANSFORM:A[f]=new oi;break;case J.KHR_MESH_QUANTIZATION:A[f]=new ri;break;default:h.indexOf(f)>=0&&o[f]===void 0&&console.warn('THREE.GLTFLoader: Unknown extension "'+f+'".')}}}l.setExtensions(A),l.setPlugins(o),l.parse(i,n)}parseAsync(e,t){const i=this;return new Promise(function(n,s){i.parse(e,t,n,s)})}}function Hn(){let c={};return{get:function(e){return c[e]},add:function(e,t){c[e]=t},remove:function(e){delete c[e]},removeAll:function(){c={}}}}const J={KHR_BINARY_GLTF:"KHR_binary_glTF",KHR_DRACO_MESH_COMPRESSION:"KHR_draco_mesh_compression",KHR_LIGHTS_PUNCTUAL:"KHR_lights_punctual",KHR_MATERIALS_CLEARCOAT:"KHR_materials_clearcoat",KHR_MATERIALS_IOR:"KHR_materials_ior",KHR_MATERIALS_SHEEN:"KHR_materials_sheen",KHR_MATERIALS_SPECULAR:"KHR_materials_specular",KHR_MATERIALS_TRANSMISSION:"KHR_materials_transmission",KHR_MATERIALS_IRIDESCENCE:"KHR_materials_iridescence",KHR_MATERIALS_UNLIT:"KHR_materials_unlit",KHR_MATERIALS_VOLUME:"KHR_materials_volume",KHR_TEXTURE_BASISU:"KHR_texture_basisu",KHR_TEXTURE_TRANSFORM:"KHR_texture_transform",KHR_MESH_QUANTIZATION:"KHR_mesh_quantization",KHR_MATERIALS_EMISSIVE_STRENGTH:"KHR_materials_emissive_strength",EXT_TEXTURE_WEBP:"EXT_texture_webp",EXT_TEXTURE_AVIF:"EXT_texture_avif",EXT_MESHOPT_COMPRESSION:"EXT_meshopt_compression",EXT_MESH_GPU_INSTANCING:"EXT_mesh_gpu_instancing"};class Vn{constructor(e){this.parser=e,this.name=J.KHR_LIGHTS_PUNCTUAL,this.cache={refs:{},uses:{}}}_markDefs(){const e=this.parser,t=this.parser.json.nodes||[];for(let i=0,n=t.length;i<n;i++){const s=t[i];s.extensions&&s.extensions[this.name]&&s.extensions[this.name].light!==void 0&&e._addNodeRef(this.cache,s.extensions[this.name].light)}}_loadLight(e){const t=this.parser,i="light:"+e;let n=t.cache.get(i);if(n)return n;const s=t.json,a=((s.extensions&&s.extensions[this.name]||{}).lights||[])[e];let l;const g=new r.Color(16777215);a.color!==void 0&&g.fromArray(a.color);const u=a.range!==void 0?a.range:0;switch(a.type){case"directional":l=new r.DirectionalLight(g),l.target.position.set(0,0,-1),l.add(l.target);break;case"point":l=new r.PointLight(g),l.distance=u;break;case"spot":l=new r.SpotLight(g),l.distance=u,a.spot=a.spot||{},a.spot.innerConeAngle=a.spot.innerConeAngle!==void 0?a.spot.innerConeAngle:0,a.spot.outerConeAngle=a.spot.outerConeAngle!==void 0?a.spot.outerConeAngle:Math.PI/4,l.angle=a.spot.outerConeAngle,l.penumbra=1-a.spot.innerConeAngle/a.spot.outerConeAngle,l.target.position.set(0,0,-1),l.add(l.target);break;default:throw new Error("THREE.GLTFLoader: Unexpected light type: "+a.type)}return l.position.set(0,0,0),l.decay=2,Ee(l,a),a.intensity!==void 0&&(l.intensity=a.intensity),l.name=t.createUniqueName(a.name||"light_"+e),n=Promise.resolve(l),t.cache.add(i,n),n}getDependency(e,t){if(e==="light")return this._loadLight(t)}createNodeAttachment(e){const t=this,i=this.parser,s=i.json.nodes[e],o=(s.extensions&&s.extensions[this.name]||{}).light;return o===void 0?null:this._loadLight(o).then(function(a){return i._getNodeRef(t.cache,o,a)})}}let qn=class{constructor(){this.name=J.KHR_MATERIALS_UNLIT}getMaterialType(){return r.MeshBasicMaterial}extendParams(e,t,i){const n=[];e.color=new r.Color(1,1,1),e.opacity=1;const s=t.pbrMetallicRoughness;if(s){if(Array.isArray(s.baseColorFactor)){const A=s.baseColorFactor;e.color.fromArray(A),e.opacity=A[3]}s.baseColorTexture!==void 0&&n.push(i.assignTexture(e,"map",s.baseColorTexture,r.SRGBColorSpace))}return Promise.all(n)}},Yn=class{constructor(e){this.parser=e,this.name=J.KHR_MATERIALS_EMISSIVE_STRENGTH}extendMaterialParams(e,t){const n=this.parser.json.materials[e];if(!n.extensions||!n.extensions[this.name])return Promise.resolve();const s=n.extensions[this.name].emissiveStrength;return s!==void 0&&(t.emissiveIntensity=s),Promise.resolve()}},Kn=class{constructor(e){this.parser=e,this.name=J.KHR_MATERIALS_CLEARCOAT}getMaterialType(e){const i=this.parser.json.materials[e];return!i.extensions||!i.extensions[this.name]?null:r.MeshPhysicalMaterial}extendMaterialParams(e,t){const i=this.parser,n=i.json.materials[e];if(!n.extensions||!n.extensions[this.name])return Promise.resolve();const s=[],A=n.extensions[this.name];if(A.clearcoatFactor!==void 0&&(t.clearcoat=A.clearcoatFactor),A.clearcoatTexture!==void 0&&s.push(i.assignTexture(t,"clearcoatMap",A.clearcoatTexture)),A.clearcoatRoughnessFactor!==void 0&&(t.clearcoatRoughness=A.clearcoatRoughnessFactor),A.clearcoatRoughnessTexture!==void 0&&s.push(i.assignTexture(t,"clearcoatRoughnessMap",A.clearcoatRoughnessTexture)),A.clearcoatNormalTexture!==void 0&&(s.push(i.assignTexture(t,"clearcoatNormalMap",A.clearcoatNormalTexture)),A.clearcoatNormalTexture.scale!==void 0)){const o=A.clearcoatNormalTexture.scale;t.clearcoatNormalScale=new r.Vector2(o,o)}return Promise.all(s)}},Jn=class{constructor(e){this.parser=e,this.name=J.KHR_MATERIALS_IRIDESCENCE}getMaterialType(e){const i=this.parser.json.materials[e];return!i.extensions||!i.extensions[this.name]?null:r.MeshPhysicalMaterial}extendMaterialParams(e,t){const i=this.parser,n=i.json.materials[e];if(!n.extensions||!n.extensions[this.name])return Promise.resolve();const s=[],A=n.extensions[this.name];return A.iridescenceFactor!==void 0&&(t.iridescence=A.iridescenceFactor),A.iridescenceTexture!==void 0&&s.push(i.assignTexture(t,"iridescenceMap",A.iridescenceTexture)),A.iridescenceIor!==void 0&&(t.iridescenceIOR=A.iridescenceIor),t.iridescenceThicknessRange===void 0&&(t.iridescenceThicknessRange=[100,400]),A.iridescenceThicknessMinimum!==void 0&&(t.iridescenceThicknessRange[0]=A.iridescenceThicknessMinimum),A.iridescenceThicknessMaximum!==void 0&&(t.iridescenceThicknessRange[1]=A.iridescenceThicknessMaximum),A.iridescenceThicknessTexture!==void 0&&s.push(i.assignTexture(t,"iridescenceThicknessMap",A.iridescenceThicknessTexture)),Promise.all(s)}},zn=class{constructor(e){this.parser=e,this.name=J.KHR_MATERIALS_SHEEN}getMaterialType(e){const i=this.parser.json.materials[e];return!i.extensions||!i.extensions[this.name]?null:r.MeshPhysicalMaterial}extendMaterialParams(e,t){const i=this.parser,n=i.json.materials[e];if(!n.extensions||!n.extensions[this.name])return Promise.resolve();const s=[];t.sheenColor=new r.Color(0,0,0),t.sheenRoughness=0,t.sheen=1;const A=n.extensions[this.name];return A.sheenColorFactor!==void 0&&t.sheenColor.fromArray(A.sheenColorFactor),A.sheenRoughnessFactor!==void 0&&(t.sheenRoughness=A.sheenRoughnessFactor),A.sheenColorTexture!==void 0&&s.push(i.assignTexture(t,"sheenColorMap",A.sheenColorTexture,r.SRGBColorSpace)),A.sheenRoughnessTexture!==void 0&&s.push(i.assignTexture(t,"sheenRoughnessMap",A.sheenRoughnessTexture)),Promise.all(s)}},Xn=class{constructor(e){this.parser=e,this.name=J.KHR_MATERIALS_TRANSMISSION}getMaterialType(e){const i=this.parser.json.materials[e];return!i.extensions||!i.extensions[this.name]?null:r.MeshPhysicalMaterial}extendMaterialParams(e,t){const i=this.parser,n=i.json.materials[e];if(!n.extensions||!n.extensions[this.name])return Promise.resolve();const s=[],A=n.extensions[this.name];return A.transmissionFactor!==void 0&&(t.transmission=A.transmissionFactor),A.transmissionTexture!==void 0&&s.push(i.assignTexture(t,"transmissionMap",A.transmissionTexture)),Promise.all(s)}},jn=class{constructor(e){this.parser=e,this.name=J.KHR_MATERIALS_VOLUME}getMaterialType(e){const i=this.parser.json.materials[e];return!i.extensions||!i.extensions[this.name]?null:r.MeshPhysicalMaterial}extendMaterialParams(e,t){const i=this.parser,n=i.json.materials[e];if(!n.extensions||!n.extensions[this.name])return Promise.resolve();const s=[],A=n.extensions[this.name];t.thickness=A.thicknessFactor!==void 0?A.thicknessFactor:0,A.thicknessTexture!==void 0&&s.push(i.assignTexture(t,"thicknessMap",A.thicknessTexture)),t.attenuationDistance=A.attenuationDistance||1/0;const o=A.attenuationColor||[1,1,1];return t.attenuationColor=new r.Color(o[0],o[1],o[2]),Promise.all(s)}},Wn=class{constructor(e){this.parser=e,this.name=J.KHR_MATERIALS_IOR}getMaterialType(e){const i=this.parser.json.materials[e];return!i.extensions||!i.extensions[this.name]?null:r.MeshPhysicalMaterial}extendMaterialParams(e,t){const n=this.parser.json.materials[e];if(!n.extensions||!n.extensions[this.name])return Promise.resolve();const s=n.extensions[this.name];return t.ior=s.ior!==void 0?s.ior:1.5,Promise.resolve()}},Zn=class{constructor(e){this.parser=e,this.name=J.KHR_MATERIALS_SPECULAR}getMaterialType(e){const i=this.parser.json.materials[e];return!i.extensions||!i.extensions[this.name]?null:r.MeshPhysicalMaterial}extendMaterialParams(e,t){const i=this.parser,n=i.json.materials[e];if(!n.extensions||!n.extensions[this.name])return Promise.resolve();const s=[],A=n.extensions[this.name];t.specularIntensity=A.specularFactor!==void 0?A.specularFactor:1,A.specularTexture!==void 0&&s.push(i.assignTexture(t,"specularIntensityMap",A.specularTexture));const o=A.specularColorFactor||[1,1,1];return t.specularColor=new r.Color(o[0],o[1],o[2]),A.specularColorTexture!==void 0&&s.push(i.assignTexture(t,"specularColorMap",A.specularColorTexture,r.SRGBColorSpace)),Promise.all(s)}};class $n{constructor(e){this.parser=e,this.name=J.KHR_TEXTURE_BASISU}loadTexture(e){const t=this.parser,i=t.json,n=i.textures[e];let s;if(n.extensions&&n.extensions[this.name])s=n.extensions[this.name];else if(n.source!=null&&i.images&&i.images[n.source]&&i.images[n.source].mimeType&&i.images[n.source].mimeType.toLowerCase()=="image/ktx2")s=n;else return null;const A=t.options.ktx2Loader;if(!A){if(i.extensionsRequired&&i.extensionsRequired.indexOf(this.name)>=0)throw new Error("THREE.GLTFLoader: setKTX2Loader must be called before loading KTX2 textures");return null}return t.loadTextureImage(e,s.source,A)}}class ei{constructor(e){this.parser=e,this.name=J.EXT_TEXTURE_WEBP,this.isSupported=null}loadTexture(e){const t=this.name,i=this.parser,n=i.json,s=n.textures[e];if(!s.extensions||!s.extensions[t])return null;const A=s.extensions[t],o=n.images[A.source];let a=i.textureLoader;if(o.uri){const l=i.options.manager.getHandler(o.uri);l!==null&&(a=l)}return this.detectSupport().then(function(l){if(l)return i.loadTextureImage(e,A.source,a);if(n.extensionsRequired&&n.extensionsRequired.indexOf(t)>=0)throw new Error("THREE.GLTFLoader: WebP required by asset but unsupported.");return i.loadTexture(e)})}detectSupport(){return this.isSupported||(this.isSupported=new Promise(function(e){const t=new Image;t.src="data:image/webp;base64,UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA",t.onload=t.onerror=function(){e(t.height===1)}})),this.isSupported}}class ti{constructor(e){this.parser=e,this.name=J.EXT_TEXTURE_AVIF,this.isSupported=null}loadTexture(e){const t=this.name,i=this.parser,n=i.json,s=n.textures[e];if(!s.extensions||!s.extensions[t])return null;const A=s.extensions[t],o=n.images[A.source];let a=i.textureLoader;if(o.uri){const l=i.options.manager.getHandler(o.uri);l!==null&&(a=l)}return this.detectSupport().then(function(l){if(l)return i.loadTextureImage(e,A.source,a);if(n.extensionsRequired&&n.extensionsRequired.indexOf(t)>=0)throw new Error("THREE.GLTFLoader: AVIF required by asset but unsupported.");return i.loadTexture(e)})}detectSupport(){return this.isSupported||(this.isSupported=new Promise(function(e){const t=new Image;t.src="data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAABcAAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAEAAAABAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQAMAAAAABNjb2xybmNseAACAAIABoAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAAB9tZGF0EgAKCBgABogQEDQgMgkQAAAAB8dSLfI=",t.onload=t.onerror=function(){e(t.height===1)}})),this.isSupported}}class ni{constructor(e){this.name=J.EXT_MESHOPT_COMPRESSION,this.parser=e}loadBufferView(e){const t=this.parser.json,i=t.bufferViews[e];if(i.extensions&&i.extensions[this.name]){const n=i.extensions[this.name],s=this.parser.getDependency("buffer",n.buffer),A=this.parser.options.meshoptDecoder;if(!A||!A.supported){if(t.extensionsRequired&&t.extensionsRequired.indexOf(this.name)>=0)throw new Error("THREE.GLTFLoader: setMeshoptDecoder must be called before loading compressed files");return null}return s.then(function(o){const a=n.byteOffset||0,l=n.byteLength||0,g=n.count,u=n.byteStride,f=new Uint8Array(o,a,l);return A.decodeGltfBufferAsync?A.decodeGltfBufferAsync(g,u,f,n.mode,n.filter).then(function(h){return h.buffer}):A.ready.then(function(){const h=new ArrayBuffer(g*u);return A.decodeGltfBuffer(new Uint8Array(h),g,u,f,n.mode,n.filter),h})})}else return null}}class ii{constructor(e){this.name=J.EXT_MESH_GPU_INSTANCING,this.parser=e}createNodeMesh(e){const t=this.parser.json,i=t.nodes[e];if(!i.extensions||!i.extensions[this.name]||i.mesh===void 0)return null;const n=t.meshes[i.mesh];for(const l of n.primitives)if(l.mode!==re.TRIANGLES&&l.mode!==re.TRIANGLE_STRIP&&l.mode!==re.TRIANGLE_FAN&&l.mode!==void 0)return null;const A=i.extensions[this.name].attributes,o=[],a={};for(const l in A)o.push(this.parser.getDependency("accessor",A[l]).then(g=>(a[l]=g,a[l])));return o.length<1?null:(o.push(this.parser.createNodeMesh(e)),Promise.all(o).then(l=>{const g=l.pop(),u=g.isGroup?g.children:[g],f=l[0].count,h=[];for(const p of u){const I=new r.Matrix4,E=new r.Vector3,B=new r.Quaternion,Q=new r.Vector3(1,1,1),d=new r.InstancedMesh(p.geometry,p.material,f);for(let C=0;C<f;C++)a.TRANSLATION&&E.fromBufferAttribute(a.TRANSLATION,C),a.ROTATION&&B.fromBufferAttribute(a.ROTATION,C),a.SCALE&&Q.fromBufferAttribute(a.SCALE,C),d.setMatrixAt(C,I.compose(E,B,Q));for(const C in a)C!=="TRANSLATION"&&C!=="ROTATION"&&C!=="SCALE"&&p.geometry.setAttribute(C,a[C]);r.Object3D.prototype.copy.call(d,p),this.parser.assignFinalMaterial(d),h.push(d)}return g.isGroup?(g.clear(),g.add(...h),g):h[0]}))}}const vt="glTF",Re=12,Rt={JSON:1313821514,BIN:5130562};class si{constructor(e){this.name=J.KHR_BINARY_GLTF,this.content=null,this.body=null;const t=new DataView(e,0,Re),i=new TextDecoder;if(this.header={magic:i.decode(new Uint8Array(e.slice(0,4))),version:t.getUint32(4,!0),length:t.getUint32(8,!0)},this.header.magic!==vt)throw new Error("THREE.GLTFLoader: Unsupported glTF-Binary header.");if(this.header.version<2)throw new Error("THREE.GLTFLoader: Legacy binary file detected.");const n=this.header.length-Re,s=new DataView(e,Re);let A=0;for(;A<n;){const o=s.getUint32(A,!0);A+=4;const a=s.getUint32(A,!0);if(A+=4,a===Rt.JSON){const l=new Uint8Array(e,Re+A,o);this.content=i.decode(l)}else if(a===Rt.BIN){const l=Re+A;this.body=e.slice(l,l+o)}A+=o}if(this.content===null)throw new Error("THREE.GLTFLoader: JSON content not found.")}}class Ai{constructor(e,t){if(!t)throw new Error("THREE.GLTFLoader: No DRACOLoader instance provided.");this.name=J.KHR_DRACO_MESH_COMPRESSION,this.json=e,this.dracoLoader=t,this.dracoLoader.preload()}decodePrimitive(e,t){const i=this.json,n=this.dracoLoader,s=e.extensions[this.name].bufferView,A=e.extensions[this.name].attributes,o={},a={},l={};for(const g in A){const u=Ze[g]||g.toLowerCase();o[u]=A[g]}for(const g in e.attributes){const u=Ze[g]||g.toLowerCase();if(A[g]!==void 0){const f=i.accessors[e.attributes[g]],h=Se[f.componentType];l[u]=h.name,a[u]=f.normalized===!0}}return t.getDependency("bufferView",s).then(function(g){return new Promise(function(u){n.decodeDracoFile(g,function(f){for(const h in f.attributes){const p=f.attributes[h],I=a[h];I!==void 0&&(p.normalized=I)}u(f)},o,l)})})}}class oi{constructor(){this.name=J.KHR_TEXTURE_TRANSFORM}extendTexture(e,t){return(t.texCoord===void 0||t.texCoord===e.channel)&&t.offset===void 0&&t.rotation===void 0&&t.scale===void 0||(e=e.clone(),t.texCoord!==void 0&&(e.channel=t.texCoord),t.offset!==void 0&&e.offset.fromArray(t.offset),t.rotation!==void 0&&(e.rotation=t.rotation),t.scale!==void 0&&e.repeat.fromArray(t.scale),e.needsUpdate=!0),e}}class ri{constructor(){this.name=J.KHR_MESH_QUANTIZATION}}class Dt extends r.Interpolant{constructor(e,t,i,n){super(e,t,i,n)}copySampleValue_(e){const t=this.resultBuffer,i=this.sampleValues,n=this.valueSize,s=e*n*3+n;for(let A=0;A!==n;A++)t[A]=i[s+A];return t}interpolate_(e,t,i,n){const s=this.resultBuffer,A=this.sampleValues,o=this.valueSize,a=o*2,l=o*3,g=n-t,u=(i-t)/g,f=u*u,h=f*u,p=e*l,I=p-l,E=-2*h+3*f,B=h-f,Q=1-E,d=B-f+u;for(let C=0;C!==o;C++){const x=A[I+C+o],m=A[I+C+a]*g,S=A[p+C+o],w=A[p+C]*g;s[C]=Q*x+d*m+E*S+B*w}return s}}const ai=new r.Quaternion;class ci extends Dt{interpolate_(e,t,i,n){const s=super.interpolate_(e,t,i,n);return ai.fromArray(s).normalize().toArray(s),s}}const re={FLOAT:5126,FLOAT_MAT3:35675,FLOAT_MAT4:35676,FLOAT_VEC2:35664,FLOAT_VEC3:35665,FLOAT_VEC4:35666,LINEAR:9729,REPEAT:10497,SAMPLER_2D:35678,POINTS:0,LINES:1,LINE_LOOP:2,LINE_STRIP:3,TRIANGLES:4,TRIANGLE_STRIP:5,TRIANGLE_FAN:6,UNSIGNED_BYTE:5121,UNSIGNED_SHORT:5123},Se={5120:Int8Array,5121:Uint8Array,5122:Int16Array,5123:Uint16Array,5125:Uint32Array,5126:Float32Array},Lt={9728:r.NearestFilter,9729:r.LinearFilter,9984:r.NearestMipmapNearestFilter,9985:r.LinearMipmapNearestFilter,9986:r.NearestMipmapLinearFilter,9987:r.LinearMipmapLinearFilter},Ft={33071:r.ClampToEdgeWrapping,33648:r.MirroredRepeatWrapping,10497:r.RepeatWrapping},We={SCALAR:1,VEC2:2,VEC3:3,VEC4:4,MAT2:4,MAT3:9,MAT4:16},Ze={POSITION:"position",NORMAL:"normal",TANGENT:"tangent",TEXCOORD_0:"uv",TEXCOORD_1:"uv1",TEXCOORD_2:"uv2",TEXCOORD_3:"uv3",COLOR_0:"color",WEIGHTS_0:"skinWeight",JOINTS_0:"skinIndex"},Ie={scale:"scale",translation:"position",rotation:"quaternion",weights:"morphTargetInfluences"},li={CUBICSPLINE:void 0,LINEAR:r.InterpolateLinear,STEP:r.InterpolateDiscrete},$e={OPAQUE:"OPAQUE",MASK:"MASK",BLEND:"BLEND"};function ui(c){return c.DefaultMaterial===void 0&&(c.DefaultMaterial=new r.MeshStandardMaterial({color:16777215,emissive:0,metalness:1,roughness:1,transparent:!1,depthTest:!0,side:r.FrontSide})),c.DefaultMaterial}function De(c,e,t){for(const i in t.extensions)c[i]===void 0&&(e.userData.gltfExtensions=e.userData.gltfExtensions||{},e.userData.gltfExtensions[i]=t.extensions[i])}function Ee(c,e){e.extras!==void 0&&(typeof e.extras=="object"?Object.assign(c.userData,e.extras):console.warn("THREE.GLTFLoader: Ignoring primitive type .extras, "+e.extras))}function gi(c,e,t){let i=!1,n=!1,s=!1;for(let l=0,g=e.length;l<g;l++){const u=e[l];if(u.POSITION!==void 0&&(i=!0),u.NORMAL!==void 0&&(n=!0),u.COLOR_0!==void 0&&(s=!0),i&&n&&s)break}if(!i&&!n&&!s)return Promise.resolve(c);const A=[],o=[],a=[];for(let l=0,g=e.length;l<g;l++){const u=e[l];if(i){const f=u.POSITION!==void 0?t.getDependency("accessor",u.POSITION):c.attributes.position;A.push(f)}if(n){const f=u.NORMAL!==void 0?t.getDependency("accessor",u.NORMAL):c.attributes.normal;o.push(f)}if(s){const f=u.COLOR_0!==void 0?t.getDependency("accessor",u.COLOR_0):c.attributes.color;a.push(f)}}return Promise.all([Promise.all(A),Promise.all(o),Promise.all(a)]).then(function(l){const g=l[0],u=l[1],f=l[2];return i&&(c.morphAttributes.position=g),n&&(c.morphAttributes.normal=u),s&&(c.morphAttributes.color=f),c.morphTargetsRelative=!0,c})}function fi(c,e){if(c.updateMorphTargets(),e.weights!==void 0)for(let t=0,i=e.weights.length;t<i;t++)c.morphTargetInfluences[t]=e.weights[t];if(e.extras&&Array.isArray(e.extras.targetNames)){const t=e.extras.targetNames;if(c.morphTargetInfluences.length===t.length){c.morphTargetDictionary={};for(let i=0,n=t.length;i<n;i++)c.morphTargetDictionary[t[i]]=i}else console.warn("THREE.GLTFLoader: Invalid extras.targetNames length. Ignoring names.")}}function hi(c){const e=c.extensions&&c.extensions[J.KHR_DRACO_MESH_COMPRESSION];let t;return e?t="draco:"+e.bufferView+":"+e.indices+":"+bt(e.attributes):t=c.indices+":"+bt(c.attributes)+":"+c.mode,t}function bt(c){let e="";const t=Object.keys(c).sort();for(let i=0,n=t.length;i<n;i++)e+=t[i]+":"+c[t[i]]+";";return e}function et(c){switch(c){case Int8Array:return 1/127;case Uint8Array:return 1/255;case Int16Array:return 1/32767;case Uint16Array:return 1/65535;default:throw new Error("THREE.GLTFLoader: Unsupported normalized accessor component type.")}}function di(c){return c.search(/\.jpe?g($|\?)/i)>0||c.search(/^data\:image\/jpeg/)===0?"image/jpeg":c.search(/\.webp($|\?)/i)>0||c.search(/^data\:image\/webp/)===0?"image/webp":"image/png"}const pi=new r.Matrix4;class Ii{constructor(e={},t={}){this.json=e,this.extensions={},this.plugins={},this.options=t,this.cache=new Hn,this.associations=new Map,this.primitiveCache={},this.nodeCache={},this.meshCache={refs:{},uses:{}},this.cameraCache={refs:{},uses:{}},this.lightCache={refs:{},uses:{}},this.sourceCache={},this.textureCache={},this.nodeNamesUsed={};let i=!1,n=!1,s=-1;typeof navigator<"u"&&(i=/^((?!chrome|android).)*safari/i.test(navigator.userAgent)===!0,n=navigator.userAgent.indexOf("Firefox")>-1,s=n?navigator.userAgent.match(/Firefox\/([0-9]+)\./)[1]:-1),typeof createImageBitmap>"u"||i||n&&s<98?this.textureLoader=new r.TextureLoader(this.options.manager):this.textureLoader=new r.ImageBitmapLoader(this.options.manager),this.textureLoader.setCrossOrigin(this.options.crossOrigin),this.textureLoader.setRequestHeader(this.options.requestHeader),this.fileLoader=new r.FileLoader(this.options.manager),this.fileLoader.setResponseType("arraybuffer"),this.options.crossOrigin==="use-credentials"&&this.fileLoader.setWithCredentials(!0)}setExtensions(e){this.extensions=e}setPlugins(e){this.plugins=e}parse(e,t){const i=this,n=this.json,s=this.extensions;this.cache.removeAll(),this.nodeCache={},this._invokeAll(function(A){return A._markDefs&&A._markDefs()}),Promise.all(this._invokeAll(function(A){return A.beforeRoot&&A.beforeRoot()})).then(function(){return Promise.all([i.getDependencies("scene"),i.getDependencies("animation"),i.getDependencies("camera")])}).then(function(A){const o={scene:A[0][n.scene||0],scenes:A[0],animations:A[1],cameras:A[2],asset:n.asset,parser:i,userData:{}};De(s,o,n),Ee(o,n),Promise.all(i._invokeAll(function(a){return a.afterRoot&&a.afterRoot(o)})).then(function(){e(o)})}).catch(t)}_markDefs(){const e=this.json.nodes||[],t=this.json.skins||[],i=this.json.meshes||[];for(let n=0,s=t.length;n<s;n++){const A=t[n].joints;for(let o=0,a=A.length;o<a;o++)e[A[o]].isBone=!0}for(let n=0,s=e.length;n<s;n++){const A=e[n];A.mesh!==void 0&&(this._addNodeRef(this.meshCache,A.mesh),A.skin!==void 0&&(i[A.mesh].isSkinnedMesh=!0)),A.camera!==void 0&&this._addNodeRef(this.cameraCache,A.camera)}}_addNodeRef(e,t){t!==void 0&&(e.refs[t]===void 0&&(e.refs[t]=e.uses[t]=0),e.refs[t]++)}_getNodeRef(e,t,i){if(e.refs[t]<=1)return i;const n=i.clone(),s=(A,o)=>{const a=this.associations.get(A);a!=null&&this.associations.set(o,a);for(const[l,g]of A.children.entries())s(g,o.children[l])};return s(i,n),n.name+="_instance_"+e.uses[t]++,n}_invokeOne(e){const t=Object.values(this.plugins);t.push(this);for(let i=0;i<t.length;i++){const n=e(t[i]);if(n)return n}return null}_invokeAll(e){const t=Object.values(this.plugins);t.unshift(this);const i=[];for(let n=0;n<t.length;n++){const s=e(t[n]);s&&i.push(s)}return i}getDependency(e,t){const i=e+":"+t;let n=this.cache.get(i);if(!n){switch(e){case"scene":n=this.loadScene(t);break;case"node":n=this._invokeOne(function(s){return s.loadNode&&s.loadNode(t)});break;case"mesh":n=this._invokeOne(function(s){return s.loadMesh&&s.loadMesh(t)});break;case"accessor":n=this.loadAccessor(t);break;case"bufferView":n=this._invokeOne(function(s){return s.loadBufferView&&s.loadBufferView(t)});break;case"buffer":n=this.loadBuffer(t);break;case"material":n=this._invokeOne(function(s){return s.loadMaterial&&s.loadMaterial(t)});break;case"texture":n=this._invokeOne(function(s){return s.loadTexture&&s.loadTexture(t)});break;case"skin":n=this.loadSkin(t);break;case"animation":n=this._invokeOne(function(s){return s.loadAnimation&&s.loadAnimation(t)});break;case"camera":n=this.loadCamera(t);break;default:if(n=this._invokeOne(function(s){return s!=this&&s.getDependency&&s.getDependency(e,t)}),!n)throw new Error("Unknown type: "+e);break}this.cache.add(i,n)}return n}getDependencies(e){let t=this.cache.get(e);if(!t){const i=this,n=this.json[e+(e==="mesh"?"es":"s")]||[];t=Promise.all(n.map(function(s,A){return i.getDependency(e,A)})),this.cache.add(e,t)}return t}loadBuffer(e){const t=this.json.buffers[e],i=this.fileLoader;if(t.type&&t.type!=="arraybuffer")throw new Error("THREE.GLTFLoader: "+t.type+" buffer type is not supported.");if(t.uri===void 0&&e===0)return Promise.resolve(this.extensions[J.KHR_BINARY_GLTF].body);const n=this.options;return new Promise(function(s,A){i.load(r.LoaderUtils.resolveURL(t.uri,n.path),s,void 0,function(){A(new Error('THREE.GLTFLoader: Failed to load buffer "'+t.uri+'".'))})})}loadBufferView(e){const t=this.json.bufferViews[e];return this.getDependency("buffer",t.buffer).then(function(i){const n=t.byteLength||0,s=t.byteOffset||0;return i.slice(s,s+n)})}loadAccessor(e){const t=this,i=this.json,n=this.json.accessors[e];if(n.bufferView===void 0&&n.sparse===void 0){const A=We[n.type],o=Se[n.componentType],a=n.normalized===!0,l=new o(n.count*A);return Promise.resolve(new r.BufferAttribute(l,A,a))}const s=[];return n.bufferView!==void 0?s.push(this.getDependency("bufferView",n.bufferView)):s.push(null),n.sparse!==void 0&&(s.push(this.getDependency("bufferView",n.sparse.indices.bufferView)),s.push(this.getDependency("bufferView",n.sparse.values.bufferView))),Promise.all(s).then(function(A){const o=A[0],a=We[n.type],l=Se[n.componentType],g=l.BYTES_PER_ELEMENT,u=g*a,f=n.byteOffset||0,h=n.bufferView!==void 0?i.bufferViews[n.bufferView].byteStride:void 0,p=n.normalized===!0;let I,E;if(h&&h!==u){const B=Math.floor(f/h),Q="InterleavedBuffer:"+n.bufferView+":"+n.componentType+":"+B+":"+n.count;let d=t.cache.get(Q);d||(I=new l(o,B*h,n.count*h/g),d=new r.InterleavedBuffer(I,h/g),t.cache.add(Q,d)),E=new r.InterleavedBufferAttribute(d,a,f%h/g,p)}else o===null?I=new l(n.count*a):I=new l(o,f,n.count*a),E=new r.BufferAttribute(I,a,p);if(n.sparse!==void 0){const B=We.SCALAR,Q=Se[n.sparse.indices.componentType],d=n.sparse.indices.byteOffset||0,C=n.sparse.values.byteOffset||0,x=new Q(A[1],d,n.sparse.count*B),m=new l(A[2],C,n.sparse.count*a);o!==null&&(E=new r.BufferAttribute(E.array.slice(),E.itemSize,E.normalized));for(let S=0,w=x.length;S<w;S++){const T=x[S];if(E.setX(T,m[S*a]),a>=2&&E.setY(T,m[S*a+1]),a>=3&&E.setZ(T,m[S*a+2]),a>=4&&E.setW(T,m[S*a+3]),a>=5)throw new Error("THREE.GLTFLoader: Unsupported itemSize in sparse BufferAttribute.")}}return E})}loadTexture(e){const t=this.json,i=this.options,s=t.textures[e].source,A=t.images[s];let o=this.textureLoader;if(A.uri){const a=i.manager.getHandler(A.uri);a!==null&&(o=a)}return this.loadTextureImage(e,s,o)}loadTextureImage(e,t,i){const n=this,s=this.json,A=s.textures[e],o=s.images[t],a=(o.uri||o.bufferView)+":"+A.sampler;if(this.textureCache[a])return this.textureCache[a];const l=this.loadImageSource(t,i).then(function(g){g.flipY=!1,g.name=A.name||o.name||"",g.name===""&&typeof o.uri=="string"&&o.uri.startsWith("data:image/")===!1&&(g.name=o.uri);const f=(s.samplers||{})[A.sampler]||{};return g.magFilter=Lt[f.magFilter]||r.LinearFilter,g.minFilter=Lt[f.minFilter]||r.LinearMipmapLinearFilter,g.wrapS=Ft[f.wrapS]||r.RepeatWrapping,g.wrapT=Ft[f.wrapT]||r.RepeatWrapping,n.associations.set(g,{textures:e}),g}).catch(function(){return null});return this.textureCache[a]=l,l}loadImageSource(e,t){const i=this,n=this.json,s=this.options;if(this.sourceCache[e]!==void 0)return this.sourceCache[e].then(u=>u.clone());const A=n.images[e],o=self.URL||self.webkitURL;let a=A.uri||"",l=!1;if(A.bufferView!==void 0)a=i.getDependency("bufferView",A.bufferView).then(function(u){l=!0;const f=new Blob([u],{type:A.mimeType});return a=o.createObjectURL(f),a});else if(A.uri===void 0)throw new Error("THREE.GLTFLoader: Image "+e+" is missing URI and bufferView");const g=Promise.resolve(a).then(function(u){return new Promise(function(f,h){let p=f;t.isImageBitmapLoader===!0&&(p=function(I){const E=new r.Texture(I);E.needsUpdate=!0,f(E)}),t.load(r.LoaderUtils.resolveURL(u,s.path),p,void 0,h)})}).then(function(u){return l===!0&&o.revokeObjectURL(a),u.userData.mimeType=A.mimeType||di(A.uri),u}).catch(function(u){throw console.error("THREE.GLTFLoader: Couldn't load texture",a),u});return this.sourceCache[e]=g,g}assignTexture(e,t,i,n){const s=this;return this.getDependency("texture",i.index).then(function(A){if(!A)return null;if(i.texCoord!==void 0&&i.texCoord>0&&(A=A.clone(),A.channel=i.texCoord),s.extensions[J.KHR_TEXTURE_TRANSFORM]){const o=i.extensions!==void 0?i.extensions[J.KHR_TEXTURE_TRANSFORM]:void 0;if(o){const a=s.associations.get(A);A=s.extensions[J.KHR_TEXTURE_TRANSFORM].extendTexture(A,o),s.associations.set(A,a)}}return n!==void 0&&(A.colorSpace=n),e[t]=A,A})}assignFinalMaterial(e){const t=e.geometry;let i=e.material;const n=t.attributes.tangent===void 0,s=t.attributes.color!==void 0,A=t.attributes.normal===void 0;if(e.isPoints){const o="PointsMaterial:"+i.uuid;let a=this.cache.get(o);a||(a=new r.PointsMaterial,r.Material.prototype.copy.call(a,i),a.color.copy(i.color),a.map=i.map,a.sizeAttenuation=!1,this.cache.add(o,a)),i=a}else if(e.isLine){const o="LineBasicMaterial:"+i.uuid;let a=this.cache.get(o);a||(a=new r.LineBasicMaterial,r.Material.prototype.copy.call(a,i),a.color.copy(i.color),a.map=i.map,this.cache.add(o,a)),i=a}if(n||s||A){let o="ClonedMaterial:"+i.uuid+":";n&&(o+="derivative-tangents:"),s&&(o+="vertex-colors:"),A&&(o+="flat-shading:");let a=this.cache.get(o);a||(a=i.clone(),s&&(a.vertexColors=!0),A&&(a.flatShading=!0),n&&(a.normalScale&&(a.normalScale.y*=-1),a.clearcoatNormalScale&&(a.clearcoatNormalScale.y*=-1)),this.cache.add(o,a),this.associations.set(a,this.associations.get(i))),i=a}e.material=i}getMaterialType(){return r.MeshStandardMaterial}loadMaterial(e){const t=this,i=this.json,n=this.extensions,s=i.materials[e];let A;const o={},a=s.extensions||{},l=[];if(a[J.KHR_MATERIALS_UNLIT]){const u=n[J.KHR_MATERIALS_UNLIT];A=u.getMaterialType(),l.push(u.extendParams(o,s,t))}else{const u=s.pbrMetallicRoughness||{};if(o.color=new r.Color(1,1,1),o.opacity=1,Array.isArray(u.baseColorFactor)){const f=u.baseColorFactor;o.color.fromArray(f),o.opacity=f[3]}u.baseColorTexture!==void 0&&l.push(t.assignTexture(o,"map",u.baseColorTexture,r.SRGBColorSpace)),o.metalness=u.metallicFactor!==void 0?u.metallicFactor:1,o.roughness=u.roughnessFactor!==void 0?u.roughnessFactor:1,u.metallicRoughnessTexture!==void 0&&(l.push(t.assignTexture(o,"metalnessMap",u.metallicRoughnessTexture)),l.push(t.assignTexture(o,"roughnessMap",u.metallicRoughnessTexture))),A=this._invokeOne(function(f){return f.getMaterialType&&f.getMaterialType(e)}),l.push(Promise.all(this._invokeAll(function(f){return f.extendMaterialParams&&f.extendMaterialParams(e,o)})))}s.doubleSided===!0&&(o.side=r.DoubleSide);const g=s.alphaMode||$e.OPAQUE;if(g===$e.BLEND?(o.transparent=!0,o.depthWrite=!1):(o.transparent=!1,g===$e.MASK&&(o.alphaTest=s.alphaCutoff!==void 0?s.alphaCutoff:.5)),s.normalTexture!==void 0&&A!==r.MeshBasicMaterial&&(l.push(t.assignTexture(o,"normalMap",s.normalTexture)),o.normalScale=new r.Vector2(1,1),s.normalTexture.scale!==void 0)){const u=s.normalTexture.scale;o.normalScale.set(u,u)}return s.occlusionTexture!==void 0&&A!==r.MeshBasicMaterial&&(l.push(t.assignTexture(o,"aoMap",s.occlusionTexture)),s.occlusionTexture.strength!==void 0&&(o.aoMapIntensity=s.occlusionTexture.strength)),s.emissiveFactor!==void 0&&A!==r.MeshBasicMaterial&&(o.emissive=new r.Color().fromArray(s.emissiveFactor)),s.emissiveTexture!==void 0&&A!==r.MeshBasicMaterial&&l.push(t.assignTexture(o,"emissiveMap",s.emissiveTexture,r.SRGBColorSpace)),Promise.all(l).then(function(){const u=new A(o);return s.name&&(u.name=s.name),Ee(u,s),t.associations.set(u,{materials:e}),s.extensions&&De(n,u,s),u})}createUniqueName(e){const t=r.PropertyBinding.sanitizeNodeName(e||"");let i=t;for(let n=1;this.nodeNamesUsed[i];++n)i=t+"_"+n;return this.nodeNamesUsed[i]=!0,i}loadGeometries(e){const t=this,i=this.extensions,n=this.primitiveCache;function s(o){return i[J.KHR_DRACO_MESH_COMPRESSION].decodePrimitive(o,t).then(function(a){return _t(a,o,t)})}const A=[];for(let o=0,a=e.length;o<a;o++){const l=e[o],g=hi(l),u=n[g];if(u)A.push(u.promise);else{let f;l.extensions&&l.extensions[J.KHR_DRACO_MESH_COMPRESSION]?f=s(l):f=_t(new r.BufferGeometry,l,t),n[g]={primitive:l,promise:f},A.push(f)}}return Promise.all(A)}loadMesh(e){const t=this,i=this.json,n=this.extensions,s=i.meshes[e],A=s.primitives,o=[];for(let a=0,l=A.length;a<l;a++){const g=A[a].material===void 0?ui(this.cache):this.getDependency("material",A[a].material);o.push(g)}return o.push(t.loadGeometries(A)),Promise.all(o).then(function(a){const l=a.slice(0,a.length-1),g=a[a.length-1],u=[];for(let h=0,p=g.length;h<p;h++){const I=g[h],E=A[h];let B;const Q=l[h];if(E.mode===re.TRIANGLES||E.mode===re.TRIANGLE_STRIP||E.mode===re.TRIANGLE_FAN||E.mode===void 0)B=s.isSkinnedMesh===!0?new r.SkinnedMesh(I,Q):new r.Mesh(I,Q),B.isSkinnedMesh===!0&&B.normalizeSkinWeights(),E.mode===re.TRIANGLE_STRIP?B.geometry=St(B.geometry,r.TriangleStripDrawMode):E.mode===re.TRIANGLE_FAN&&(B.geometry=St(B.geometry,r.TriangleFanDrawMode));else if(E.mode===re.LINES)B=new r.LineSegments(I,Q);else if(E.mode===re.LINE_STRIP)B=new r.Line(I,Q);else if(E.mode===re.LINE_LOOP)B=new r.LineLoop(I,Q);else if(E.mode===re.POINTS)B=new r.Points(I,Q);else throw new Error("THREE.GLTFLoader: Primitive mode unsupported: "+E.mode);Object.keys(B.geometry.morphAttributes).length>0&&fi(B,s),B.name=t.createUniqueName(s.name||"mesh_"+e),Ee(B,s),E.extensions&&De(n,B,E),t.assignFinalMaterial(B),u.push(B)}for(let h=0,p=u.length;h<p;h++)t.associations.set(u[h],{meshes:e,primitives:h});if(u.length===1)return u[0];const f=new r.Group;t.associations.set(f,{meshes:e});for(let h=0,p=u.length;h<p;h++)f.add(u[h]);return f})}loadCamera(e){let t;const i=this.json.cameras[e],n=i[i.type];if(!n){console.warn("THREE.GLTFLoader: Missing camera parameters.");return}return i.type==="perspective"?t=new r.PerspectiveCamera(r.MathUtils.radToDeg(n.yfov),n.aspectRatio||1,n.znear||1,n.zfar||2e6):i.type==="orthographic"&&(t=new r.OrthographicCamera(-n.xmag,n.xmag,n.ymag,-n.ymag,n.znear,n.zfar)),i.name&&(t.name=this.createUniqueName(i.name)),Ee(t,i),Promise.resolve(t)}loadSkin(e){const t=this.json.skins[e],i=[];for(let n=0,s=t.joints.length;n<s;n++)i.push(this._loadNodeShallow(t.joints[n]));return t.inverseBindMatrices!==void 0?i.push(this.getDependency("accessor",t.inverseBindMatrices)):i.push(null),Promise.all(i).then(function(n){const s=n.pop(),A=n,o=[],a=[];for(let l=0,g=A.length;l<g;l++){const u=A[l];if(u){o.push(u);const f=new r.Matrix4;s!==null&&f.fromArray(s.array,l*16),a.push(f)}else console.warn('THREE.GLTFLoader: Joint "%s" could not be found.',t.joints[l])}return new r.Skeleton(o,a)})}loadAnimation(e){const i=this.json.animations[e],n=i.name?i.name:"animation_"+e,s=[],A=[],o=[],a=[],l=[];for(let g=0,u=i.channels.length;g<u;g++){const f=i.channels[g],h=i.samplers[f.sampler],p=f.target,I=p.node,E=i.parameters!==void 0?i.parameters[h.input]:h.input,B=i.parameters!==void 0?i.parameters[h.output]:h.output;p.node!==void 0&&(s.push(this.getDependency("node",I)),A.push(this.getDependency("accessor",E)),o.push(this.getDependency("accessor",B)),a.push(h),l.push(p))}return Promise.all([Promise.all(s),Promise.all(A),Promise.all(o),Promise.all(a),Promise.all(l)]).then(function(g){const u=g[0],f=g[1],h=g[2],p=g[3],I=g[4],E=[];for(let B=0,Q=u.length;B<Q;B++){const d=u[B],C=f[B],x=h[B],m=p[B],S=I[B];if(d===void 0)continue;d.updateMatrix();let w;switch(Ie[S.path]){case Ie.weights:w=r.NumberKeyframeTrack;break;case Ie.rotation:w=r.QuaternionKeyframeTrack;break;case Ie.position:case Ie.scale:default:w=r.VectorKeyframeTrack;break}const T=d.name?d.name:d.uuid,M=m.interpolation!==void 0?li[m.interpolation]:r.InterpolateLinear,R=[];Ie[S.path]===Ie.weights?d.traverse(function(V){V.morphTargetInfluences&&R.push(V.name?V.name:V.uuid)}):R.push(T);let U=x.array;if(x.normalized){const V=et(U.constructor),O=new Float32Array(U.length);for(let P=0,y=U.length;P<y;P++)O[P]=U[P]*V;U=O}for(let V=0,O=R.length;V<O;V++){const P=new w(R[V]+"."+Ie[S.path],C.array,U,M);m.interpolation==="CUBICSPLINE"&&(P.createInterpolant=function(L){const D=this instanceof r.QuaternionKeyframeTrack?ci:Dt;return new D(this.times,this.values,this.getValueSize()/3,L)},P.createInterpolant.isInterpolantFactoryMethodGLTFCubicSpline=!0),E.push(P)}}return new r.AnimationClip(n,void 0,E)})}createNodeMesh(e){const t=this.json,i=this,n=t.nodes[e];return n.mesh===void 0?null:i.getDependency("mesh",n.mesh).then(function(s){const A=i._getNodeRef(i.meshCache,n.mesh,s);return n.weights!==void 0&&A.traverse(function(o){if(o.isMesh)for(let a=0,l=n.weights.length;a<l;a++)o.morphTargetInfluences[a]=n.weights[a]}),A})}loadNode(e){const t=this.json,i=this,n=t.nodes[e],s=i._loadNodeShallow(e),A=[],o=n.children||[];for(let l=0,g=o.length;l<g;l++)A.push(i.getDependency("node",o[l]));const a=n.skin===void 0?Promise.resolve(null):i.getDependency("skin",n.skin);return Promise.all([s,Promise.all(A),a]).then(function(l){const g=l[0],u=l[1],f=l[2];f!==null&&g.traverse(function(h){h.isSkinnedMesh&&h.bind(f,pi)});for(let h=0,p=u.length;h<p;h++)g.add(u[h]);return g})}_loadNodeShallow(e){const t=this.json,i=this.extensions,n=this;if(this.nodeCache[e]!==void 0)return this.nodeCache[e];const s=t.nodes[e],A=s.name?n.createUniqueName(s.name):"",o=[],a=n._invokeOne(function(l){return l.createNodeMesh&&l.createNodeMesh(e)});return a&&o.push(a),s.camera!==void 0&&o.push(n.getDependency("camera",s.camera).then(function(l){return n._getNodeRef(n.cameraCache,s.camera,l)})),n._invokeAll(function(l){return l.createNodeAttachment&&l.createNodeAttachment(e)}).forEach(function(l){o.push(l)}),this.nodeCache[e]=Promise.all(o).then(function(l){let g;if(s.isBone===!0?g=new r.Bone:l.length>1?g=new r.Group:l.length===1?g=l[0]:g=new r.Object3D,g!==l[0])for(let u=0,f=l.length;u<f;u++)g.add(l[u]);if(s.name&&(g.userData.name=s.name,g.name=A),Ee(g,s),s.extensions&&De(i,g,s),s.matrix!==void 0){const u=new r.Matrix4;u.fromArray(s.matrix),g.applyMatrix4(u)}else s.translation!==void 0&&g.position.fromArray(s.translation),s.rotation!==void 0&&g.quaternion.fromArray(s.rotation),s.scale!==void 0&&g.scale.fromArray(s.scale);return n.associations.has(g)||n.associations.set(g,{}),n.associations.get(g).nodes=e,g}),this.nodeCache[e]}loadScene(e){const t=this.extensions,i=this.json.scenes[e],n=this,s=new r.Group;i.name&&(s.name=n.createUniqueName(i.name)),Ee(s,i),i.extensions&&De(t,s,i);const A=i.nodes||[],o=[];for(let a=0,l=A.length;a<l;a++)o.push(n.getDependency("node",A[a]));return Promise.all(o).then(function(a){for(let g=0,u=a.length;g<u;g++)s.add(a[g]);const l=g=>{const u=new Map;for(const[f,h]of n.associations)(f instanceof r.Material||f instanceof r.Texture)&&u.set(f,h);return g.traverse(f=>{const h=n.associations.get(f);h!=null&&u.set(f,h)}),u};return n.associations=l(s),s})}}function Ei(c,e,t){const i=e.attributes,n=new r.Box3;if(i.POSITION!==void 0){const o=t.json.accessors[i.POSITION],a=o.min,l=o.max;if(a!==void 0&&l!==void 0){if(n.set(new r.Vector3(a[0],a[1],a[2]),new r.Vector3(l[0],l[1],l[2])),o.normalized){const g=et(Se[o.componentType]);n.min.multiplyScalar(g),n.max.multiplyScalar(g)}}else{console.warn("THREE.GLTFLoader: Missing min/max properties for accessor POSITION.");return}}else return;const s=e.targets;if(s!==void 0){const o=new r.Vector3,a=new r.Vector3;for(let l=0,g=s.length;l<g;l++){const u=s[l];if(u.POSITION!==void 0){const f=t.json.accessors[u.POSITION],h=f.min,p=f.max;if(h!==void 0&&p!==void 0){if(a.setX(Math.max(Math.abs(h[0]),Math.abs(p[0]))),a.setY(Math.max(Math.abs(h[1]),Math.abs(p[1]))),a.setZ(Math.max(Math.abs(h[2]),Math.abs(p[2]))),f.normalized){const I=et(Se[f.componentType]);a.multiplyScalar(I)}o.max(a)}else console.warn("THREE.GLTFLoader: Missing min/max properties for accessor POSITION.")}}n.expandByVector(o)}c.boundingBox=n;const A=new r.Sphere;n.getCenter(A.center),A.radius=n.min.distanceTo(n.max)/2,c.boundingSphere=A}function _t(c,e,t){const i=e.attributes,n=[];function s(A,o){return t.getDependency("accessor",A).then(function(a){c.setAttribute(o,a)})}for(const A in i){const o=Ze[A]||A.toLowerCase();o in c.attributes||n.push(s(i[A],o))}if(e.indices!==void 0&&!c.index){const A=t.getDependency("accessor",e.indices).then(function(o){c.setIndex(o)});n.push(A)}return Ee(c,e),Ei(c,e,t),Promise.all(n).then(function(){return e.targets!==void 0?gi(c,e.targets,t):c})}const tt=new WeakMap;class Bi extends r.Loader{constructor(e){super(e),this.decoderPath="",this.decoderConfig={},this.decoderBinary=null,this.decoderPending=null,this.workerLimit=4,this.workerPool=[],this.workerNextTaskID=1,this.workerSourceURL="",this.defaultAttributeIDs={position:"POSITION",normal:"NORMAL",color:"COLOR",uv:"TEX_COORD"},this.defaultAttributeTypes={position:"Float32Array",normal:"Float32Array",color:"Float32Array",uv:"Float32Array"}}setDecoderPath(e){return this.decoderPath=e,this}setDecoderConfig(e){return this.decoderConfig=e,this}setWorkerLimit(e){return this.workerLimit=e,this}load(e,t,i,n){const s=new r.FileLoader(this.manager);s.setPath(this.path),s.setResponseType("arraybuffer"),s.setRequestHeader(this.requestHeader),s.setWithCredentials(this.withCredentials),s.load(e,A=>{this.parse(A,t,n)},i,n)}parse(e,t,i){this.decodeDracoFile(e,t,null,null,r.SRGBColorSpace).catch(i)}decodeDracoFile(e,t,i,n,s=r.LinearSRGBColorSpace){const A={attributeIDs:i||this.defaultAttributeIDs,attributeTypes:n||this.defaultAttributeTypes,useUniqueIDs:!!i,vertexColorSpace:s};return this.decodeGeometry(e,A).then(t)}decodeGeometry(e,t){const i=JSON.stringify(t);if(tt.has(e)){const a=tt.get(e);if(a.key===i)return a.promise;if(e.byteLength===0)throw new Error("THREE.DRACOLoader: Unable to re-decode a buffer with different settings. Buffer has already been transferred.")}let n;const s=this.workerNextTaskID++,A=e.byteLength,o=this._getWorker(s,A).then(a=>(n=a,new Promise((l,g)=>{n._callbacks[s]={resolve:l,reject:g},n.postMessage({type:"decode",id:s,taskConfig:t,buffer:e},[e])}))).then(a=>this._createGeometry(a.geometry));return o.catch(()=>!0).then(()=>{n&&s&&this._releaseTask(n,s)}),tt.set(e,{key:i,promise:o}),o}_createGeometry(e){const t=new r.BufferGeometry;e.index&&t.setIndex(new r.BufferAttribute(e.index.array,1));for(let i=0;i<e.attributes.length;i++){const n=e.attributes[i],s=n.name,A=n.array,o=n.itemSize,a=new r.BufferAttribute(A,o);s==="color"&&this._assignVertexColorSpace(a,n.vertexColorSpace),t.setAttribute(s,a)}return t}_assignVertexColorSpace(e,t){if(t!==r.SRGBColorSpace)return;const i=new r.Color;for(let n=0,s=e.count;n<s;n++)i.fromBufferAttribute(e,n).convertSRGBToLinear(),e.setXYZ(n,i.r,i.g,i.b)}_loadLibrary(e,t){const i=new r.FileLoader(this.manager);return i.setPath(this.decoderPath),i.setResponseType(t),i.setWithCredentials(this.withCredentials),new Promise((n,s)=>{i.load(e,n,void 0,s)})}preload(){return this._initDecoder(),this}_initDecoder(){if(this.decoderPending)return this.decoderPending;const e=typeof WebAssembly!="object"||this.decoderConfig.type==="js",t=[];return e?t.push(this._loadLibrary("draco_decoder.js","text")):(t.push(this._loadLibrary("draco_wasm_wrapper.js","text")),t.push(this._loadLibrary("draco_decoder.wasm","arraybuffer"))),this.decoderPending=Promise.all(t).then(i=>{const n=i[0];e||(this.decoderConfig.wasmBinary=i[1]);const s=Ci.toString(),A=["/* draco decoder */",n,"","/* worker */",s.substring(s.indexOf("{")+1,s.lastIndexOf("}"))].join(`
`);this.workerSourceURL=URL.createObjectURL(new Blob([A]))}),this.decoderPending}_getWorker(e,t){return this._initDecoder().then(()=>{if(this.workerPool.length<this.workerLimit){const n=new Worker(this.workerSourceURL);n._callbacks={},n._taskCosts={},n._taskLoad=0,n.postMessage({type:"init",decoderConfig:this.decoderConfig}),n.onmessage=function(s){const A=s.data;switch(A.type){case"decode":n._callbacks[A.id].resolve(A);break;case"error":n._callbacks[A.id].reject(A);break;default:console.error('THREE.DRACOLoader: Unexpected message, "'+A.type+'"')}},this.workerPool.push(n)}else this.workerPool.sort(function(n,s){return n._taskLoad>s._taskLoad?-1:1});const i=this.workerPool[this.workerPool.length-1];return i._taskCosts[e]=t,i._taskLoad+=t,i})}_releaseTask(e,t){e._taskLoad-=e._taskCosts[t],delete e._callbacks[t],delete e._taskCosts[t]}debug(){console.log("Task load: ",this.workerPool.map(e=>e._taskLoad))}dispose(){for(let e=0;e<this.workerPool.length;++e)this.workerPool[e].terminate();return this.workerPool.length=0,this.workerSourceURL!==""&&URL.revokeObjectURL(this.workerSourceURL),this}}function Ci(){let c,e;onmessage=function(A){const o=A.data;switch(o.type){case"init":c=o.decoderConfig,e=new Promise(function(g){c.onModuleLoaded=function(u){g({draco:u})},DracoDecoderModule(c)});break;case"decode":const a=o.buffer,l=o.taskConfig;e.then(g=>{const u=g.draco,f=new u.Decoder;try{const h=t(u,f,new Int8Array(a),l),p=h.attributes.map(I=>I.array.buffer);h.index&&p.push(h.index.array.buffer),self.postMessage({type:"decode",id:o.id,geometry:h},p)}catch(h){console.error(h),self.postMessage({type:"error",id:o.id,error:h.message})}finally{u.destroy(f)}});break}};function t(A,o,a,l){const g=l.attributeIDs,u=l.attributeTypes;let f,h;const p=o.GetEncodedGeometryType(a);if(p===A.TRIANGULAR_MESH)f=new A.Mesh,h=o.DecodeArrayToMesh(a,a.byteLength,f);else if(p===A.POINT_CLOUD)f=new A.PointCloud,h=o.DecodeArrayToPointCloud(a,a.byteLength,f);else throw new Error("THREE.DRACOLoader: Unexpected geometry type.");if(!h.ok()||f.ptr===0)throw new Error("THREE.DRACOLoader: Decoding failed: "+h.error_msg());const I={index:null,attributes:[]};for(const E in g){const B=self[u[E]];let Q,d;if(l.useUniqueIDs)d=g[E],Q=o.GetAttributeByUniqueId(f,d);else{if(d=o.GetAttributeId(f,A[g[E]]),d===-1)continue;Q=o.GetAttribute(f,d)}const C=n(A,o,f,E,B,Q);E==="color"&&(C.vertexColorSpace=l.vertexColorSpace),I.attributes.push(C)}return p===A.TRIANGULAR_MESH&&(I.index=i(A,o,f)),A.destroy(f),I}function i(A,o,a){const g=a.num_faces()*3,u=g*4,f=A._malloc(u);o.GetTrianglesUInt32Array(a,u,f);const h=new Uint32Array(A.HEAPF32.buffer,f,g).slice();return A._free(f),{array:h,itemSize:1}}function n(A,o,a,l,g,u){const f=u.num_components(),p=a.num_points()*f,I=p*g.BYTES_PER_ELEMENT,E=s(A,g),B=A._malloc(I);o.GetAttributeDataArrayForAllPoints(a,u,E,I,B);const Q=new g(A.HEAPF32.buffer,B,p).slice();return A._free(B),{name:l,array:Q,itemSize:f}}function s(A,o){switch(o){case Float32Array:return A.DT_FLOAT32;case Int8Array:return A.DT_INT8;case Int16Array:return A.DT_INT16;case Int32Array:return A.DT_INT32;case Uint8Array:return A.DT_UINT8;case Uint16Array:return A.DT_UINT16;case Uint32Array:return A.DT_UINT32}}}class mi{constructor(e=4){this.pool=e,this.queue=[],this.workers=[],this.workersResolve=[],this.workerStatus=0}_initWorker(e){if(!this.workers[e]){const t=this.workerCreator();t.addEventListener("message",this._onMessage.bind(this,e)),this.workers[e]=t}}_getIdleWorker(){for(let e=0;e<this.pool;e++)if(!(this.workerStatus&1<<e))return e;return-1}_onMessage(e,t){const i=this.workersResolve[e];if(i&&i(t),this.queue.length){const{resolve:n,msg:s,transfer:A}=this.queue.shift();this.workersResolve[e]=n,this.workers[e].postMessage(s,A)}else this.workerStatus^=1<<e}setWorkerCreator(e){this.workerCreator=e}setWorkerLimit(e){this.pool=e}postMessage(e,t){return new Promise(i=>{const n=this._getIdleWorker();n!==-1?(this._initWorker(n),this.workerStatus|=1<<n,this.workersResolve[n]=i,this.workers[n].postMessage(e,t)):this.queue.push({resolve:i,msg:e,transfer:t})})}dispose(){this.workers.forEach(e=>e.terminate()),this.workersResolve.length=0,this.workers.length=0,this.queue.length=0,this.workerStatus=0}}const Qi=0,yi=2,wi=1,xi=2,Ti=0,Gt=9,nt=15,Ut=16,it=22,kt=37,st=43,Nt=76,Pt=83,Ot=97,Ht=100,Vt=103,qt=109;class Si{constructor(){this.vkFormat=0,this.typeSize=1,this.pixelWidth=0,this.pixelHeight=0,this.pixelDepth=0,this.layerCount=0,this.faceCount=1,this.supercompressionScheme=0,this.levels=[],this.dataFormatDescriptor=[{vendorId:0,descriptorType:0,descriptorBlockSize:0,versionNumber:2,colorModel:0,colorPrimaries:1,transferFunction:2,flags:0,texelBlockDimension:[0,0,0,0],bytesPlane:[0,0,0,0,0,0,0,0],samples:[]}],this.keyValue={},this.globalData=null}}class Le{constructor(e,t,i,n){this._dataView=new DataView(e.buffer,e.byteOffset+t,i),this._littleEndian=n,this._offset=0}_nextUint8(){const e=this._dataView.getUint8(this._offset);return this._offset+=1,e}_nextUint16(){const e=this._dataView.getUint16(this._offset,this._littleEndian);return this._offset+=2,e}_nextUint32(){const e=this._dataView.getUint32(this._offset,this._littleEndian);return this._offset+=4,e}_nextUint64(){const e=this._dataView.getUint32(this._offset,this._littleEndian)+4294967296*this._dataView.getUint32(this._offset+4,this._littleEndian);return this._offset+=8,e}_nextInt32(){const e=this._dataView.getInt32(this._offset,this._littleEndian);return this._offset+=4,e}_skip(e){return this._offset+=e,this}_scan(e,t=0){const i=this._offset;let n=0;for(;this._dataView.getUint8(this._offset)!==t&&n<e;)n++,this._offset++;return n<e&&this._offset++,new Uint8Array(this._dataView.buffer,this._dataView.byteOffset+i,n)}}const ie=[171,75,84,88,32,50,48,187,13,10,26,10];function Yt(c){return typeof TextDecoder<"u"?new TextDecoder().decode(c):Buffer.from(c).toString("utf8")}function Mi(c){const e=new Uint8Array(c.buffer,c.byteOffset,ie.length);if(e[0]!==ie[0]||e[1]!==ie[1]||e[2]!==ie[2]||e[3]!==ie[3]||e[4]!==ie[4]||e[5]!==ie[5]||e[6]!==ie[6]||e[7]!==ie[7]||e[8]!==ie[8]||e[9]!==ie[9]||e[10]!==ie[10]||e[11]!==ie[11])throw new Error("Missing KTX 2.0 identifier.");const t=new Si,i=17*Uint32Array.BYTES_PER_ELEMENT,n=new Le(c,ie.length,i,!0);t.vkFormat=n._nextUint32(),t.typeSize=n._nextUint32(),t.pixelWidth=n._nextUint32(),t.pixelHeight=n._nextUint32(),t.pixelDepth=n._nextUint32(),t.layerCount=n._nextUint32(),t.faceCount=n._nextUint32();const s=n._nextUint32();t.supercompressionScheme=n._nextUint32();const A=n._nextUint32(),o=n._nextUint32(),a=n._nextUint32(),l=n._nextUint32(),g=n._nextUint64(),u=n._nextUint64(),f=new Le(c,ie.length+i,3*s*8,!0);for(let L=0;L<s;L++)t.levels.push({levelData:new Uint8Array(c.buffer,c.byteOffset+f._nextUint64(),f._nextUint64()),uncompressedByteLength:f._nextUint64()});const h=new Le(c,A,o,!0),p={vendorId:h._skip(4)._nextUint16(),descriptorType:h._nextUint16(),versionNumber:h._nextUint16(),descriptorBlockSize:h._nextUint16(),colorModel:h._nextUint8(),colorPrimaries:h._nextUint8(),transferFunction:h._nextUint8(),flags:h._nextUint8(),texelBlockDimension:[h._nextUint8(),h._nextUint8(),h._nextUint8(),h._nextUint8()],bytesPlane:[h._nextUint8(),h._nextUint8(),h._nextUint8(),h._nextUint8(),h._nextUint8(),h._nextUint8(),h._nextUint8(),h._nextUint8()],samples:[]},I=(p.descriptorBlockSize/4-6)/4;for(let L=0;L<I;L++){const D={bitOffset:h._nextUint16(),bitLength:h._nextUint8(),channelType:h._nextUint8(),samplePosition:[h._nextUint8(),h._nextUint8(),h._nextUint8(),h._nextUint8()],sampleLower:-1/0,sampleUpper:1/0};64&D.channelType?(D.sampleLower=h._nextInt32(),D.sampleUpper=h._nextInt32()):(D.sampleLower=h._nextUint32(),D.sampleUpper=h._nextUint32()),p.samples[L]=D}t.dataFormatDescriptor.length=0,t.dataFormatDescriptor.push(p);const E=new Le(c,a,l,!0);for(;E._offset<l;){const L=E._nextUint32(),D=E._scan(L),k=Yt(D),N=E._scan(L-D.byteLength);t.keyValue[k]=k.match(/^ktx/i)?Yt(N):N,E._offset%4&&E._skip(4-E._offset%4)}if(u<=0)return t;const B=new Le(c,g,u,!0),Q=B._nextUint16(),d=B._nextUint16(),C=B._nextUint32(),x=B._nextUint32(),m=B._nextUint32(),S=B._nextUint32(),w=[];for(let L=0;L<s;L++)w.push({imageFlags:B._nextUint32(),rgbSliceByteOffset:B._nextUint32(),rgbSliceByteLength:B._nextUint32(),alphaSliceByteOffset:B._nextUint32(),alphaSliceByteLength:B._nextUint32()});const T=g+B._offset,M=T+C,R=M+x,U=R+m,V=new Uint8Array(c.buffer,c.byteOffset+T,C),O=new Uint8Array(c.buffer,c.byteOffset+M,x),P=new Uint8Array(c.buffer,c.byteOffset+R,m),y=new Uint8Array(c.buffer,c.byteOffset+U,S);return t.globalData={endpointCount:Q,selectorCount:d,imageDescs:w,endpointsData:V,selectorsData:O,tablesData:P,extendedData:y},t}let At,pe,ot;const rt={env:{emscripten_notify_memory_growth:function(c){ot=new Uint8Array(pe.exports.memory.buffer)}}};class vi{init(){return At||(At=typeof fetch<"u"?fetch("data:application/wasm;base64,"+Kt).then(e=>e.arrayBuffer()).then(e=>WebAssembly.instantiate(e,rt)).then(this._init):WebAssembly.instantiate(Buffer.from(Kt,"base64"),rt).then(this._init),At)}_init(e){pe=e.instance,rt.env.emscripten_notify_memory_growth(0)}decode(e,t=0){if(!pe)throw new Error("ZSTDDecoder: Await .init() before decoding.");const i=e.byteLength,n=pe.exports.malloc(i);ot.set(e,n),t=t||Number(pe.exports.ZSTD_findDecompressedSize(n,i));const s=pe.exports.malloc(t),A=pe.exports.ZSTD_decompress(s,t,n,i),o=ot.slice(s,s+A);return pe.exports.free(n),pe.exports.free(s),o}}const Kt="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",at=new WeakMap;let ct=0,lt;class le extends r.Loader{constructor(e){super(e),this.transcoderPath="",this.transcoderBinary=null,this.transcoderPending=null,this.workerPool=new mi,this.workerSourceURL="",this.workerConfig=null,typeof MSC_TRANSCODER<"u"&&console.warn('THREE.KTX2Loader: Please update to latest "basis_transcoder". "msc_basis_transcoder" is no longer supported in three.js r125+.')}setTranscoderPath(e){return this.transcoderPath=e,this}setWorkerLimit(e){return this.workerPool.setWorkerLimit(e),this}detectSupport(e){return e.isWebGPURenderer===!0?this.workerConfig={astcSupported:e.hasFeature("texture-compression-astc"),etc1Supported:!1,etc2Supported:e.hasFeature("texture-compression-etc2"),dxtSupported:e.hasFeature("texture-compression-bc"),bptcSupported:!1,pvrtcSupported:!1}:(this.workerConfig={astcSupported:e.extensions.has("WEBGL_compressed_texture_astc"),etc1Supported:e.extensions.has("WEBGL_compressed_texture_etc1"),etc2Supported:e.extensions.has("WEBGL_compressed_texture_etc"),dxtSupported:e.extensions.has("WEBGL_compressed_texture_s3tc"),bptcSupported:e.extensions.has("EXT_texture_compression_bptc"),pvrtcSupported:e.extensions.has("WEBGL_compressed_texture_pvrtc")||e.extensions.has("WEBKIT_WEBGL_compressed_texture_pvrtc")},e.capabilities.isWebGL2&&(this.workerConfig.etc1Supported=!1)),this}init(){if(!this.transcoderPending){const e=new r.FileLoader(this.manager);e.setPath(this.transcoderPath),e.setWithCredentials(this.withCredentials);const t=e.loadAsync("basis_transcoder.js"),i=new r.FileLoader(this.manager);i.setPath(this.transcoderPath),i.setResponseType("arraybuffer"),i.setWithCredentials(this.withCredentials);const n=i.loadAsync("basis_transcoder.wasm");this.transcoderPending=Promise.all([t,n]).then(([s,A])=>{const o=le.BasisWorker.toString(),a=["/* constants */","let _EngineFormat = "+JSON.stringify(le.EngineFormat),"let _TranscoderFormat = "+JSON.stringify(le.TranscoderFormat),"let _BasisFormat = "+JSON.stringify(le.BasisFormat),"/* basis_transcoder.js */",s,"/* worker */",o.substring(o.indexOf("{")+1,o.lastIndexOf("}"))].join(`
`);this.workerSourceURL=URL.createObjectURL(new Blob([a])),this.transcoderBinary=A,this.workerPool.setWorkerCreator(()=>{const l=new Worker(this.workerSourceURL),g=this.transcoderBinary.slice(0);return l.postMessage({type:"init",config:this.workerConfig,transcoderBinary:g},[g]),l})}),ct>0&&console.warn("THREE.KTX2Loader: Multiple active KTX2 loaders may cause performance issues. Use a single KTX2Loader instance, or call .dispose() on old instances."),ct++}return this.transcoderPending}load(e,t,i,n){if(this.workerConfig===null)throw new Error("THREE.KTX2Loader: Missing initialization with `.detectSupport( renderer )`.");const s=new r.FileLoader(this.manager);s.setResponseType("arraybuffer"),s.setWithCredentials(this.withCredentials),s.load(e,A=>{if(at.has(A))return at.get(A).promise.then(t).catch(n);this._createTexture(A).then(o=>t?t(o):null).catch(n)},i,n)}_createTextureFrom(e,t){const{mipmaps:i,width:n,height:s,format:A,type:o,error:a,dfdTransferFn:l,dfdFlags:g}=e;if(o==="error")return Promise.reject(a);const u=t.layerCount>1?new r.CompressedArrayTexture(i,n,s,t.layerCount,A,r.UnsignedByteType):new r.CompressedTexture(i,n,s,A,r.UnsignedByteType);return u.minFilter=i.length===1?r.LinearFilter:r.LinearMipmapLinearFilter,u.magFilter=r.LinearFilter,u.generateMipmaps=!1,u.needsUpdate=!0,u.colorSpace=l===xi?r.SRGBColorSpace:r.NoColorSpace,u.premultiplyAlpha=!!(g&wi),u}async _createTexture(e,t={}){const i=Mi(new Uint8Array(e));if(i.vkFormat!==Ti)return Di(i);const n=t,s=this.init().then(()=>this.workerPool.postMessage({type:"transcode",buffer:e,taskConfig:n},[e])).then(A=>this._createTextureFrom(A.data,i));return at.set(e,{promise:s}),s}dispose(){return this.workerPool.dispose(),this.workerSourceURL&&URL.revokeObjectURL(this.workerSourceURL),ct--,this}}le.BasisFormat={ETC1S:0,UASTC_4x4:1},le.TranscoderFormat={ETC1:0,ETC2:1,BC1:2,BC3:3,BC4:4,BC5:5,BC7_M6_OPAQUE_ONLY:6,BC7_M5:7,PVRTC1_4_RGB:8,PVRTC1_4_RGBA:9,ASTC_4x4:10,ATC_RGB:11,ATC_RGBA_INTERPOLATED_ALPHA:12,RGBA32:13,RGB565:14,BGR565:15,RGBA4444:16},le.EngineFormat={RGBAFormat:r.RGBAFormat,RGBA_ASTC_4x4_Format:r.RGBA_ASTC_4x4_Format,RGBA_BPTC_Format:r.RGBA_BPTC_Format,RGBA_ETC2_EAC_Format:r.RGBA_ETC2_EAC_Format,RGBA_PVRTC_4BPPV1_Format:r.RGBA_PVRTC_4BPPV1_Format,RGBA_S3TC_DXT5_Format:r.RGBA_S3TC_DXT5_Format,RGB_ETC1_Format:r.RGB_ETC1_Format,RGB_ETC2_Format:r.RGB_ETC2_Format,RGB_PVRTC_4BPPV1_Format:r.RGB_PVRTC_4BPPV1_Format,RGB_S3TC_DXT1_Format:r.RGB_S3TC_DXT1_Format},le.BasisWorker=function(){let c,e,t;const i=_EngineFormat,n=_TranscoderFormat,s=_BasisFormat;self.addEventListener("message",function(p){const I=p.data;switch(I.type){case"init":c=I.config,A(I.transcoderBinary);break;case"transcode":e.then(()=>{try{const{width:E,height:B,hasAlpha:Q,mipmaps:d,format:C,dfdTransferFn:x,dfdFlags:m}=o(I.buffer),S=[];for(let w=0;w<d.length;++w)S.push(d[w].data.buffer);self.postMessage({type:"transcode",id:I.id,width:E,height:B,hasAlpha:Q,mipmaps:d,format:C,dfdTransferFn:x,dfdFlags:m},S)}catch(E){console.error(E),self.postMessage({type:"error",id:I.id,error:E.message})}});break}});function A(p){e=new Promise(I=>{t={wasmBinary:p,onRuntimeInitialized:I},BASIS(t)}).then(()=>{t.initializeBasis(),t.KTX2File===void 0&&console.warn("THREE.KTX2Loader: Please update Basis Universal transcoder.")})}function o(p){const I=new t.KTX2File(new Uint8Array(p));function E(){I.close(),I.delete()}if(!I.isValid())throw E(),new Error("THREE.KTX2Loader:	Invalid or unsupported .ktx2 file");const B=I.isUASTC()?s.UASTC_4x4:s.ETC1S,Q=I.getWidth(),d=I.getHeight(),C=I.getLayers()||1,x=I.getLevels(),m=I.getHasAlpha(),S=I.getDFDTransferFunc(),w=I.getDFDFlags(),{transcoderFormat:T,engineFormat:M}=u(B,Q,d,m);if(!Q||!d||!x)throw E(),new Error("THREE.KTX2Loader:	Invalid texture");if(!I.startTranscoding())throw E(),new Error("THREE.KTX2Loader: .startTranscoding failed");const R=[];for(let U=0;U<x;U++){const V=[];let O,P;for(let y=0;y<C;y++){const L=I.getImageLevelInfo(U,y,0);O=L.origWidth<4?L.origWidth:L.width,P=L.origHeight<4?L.origHeight:L.height;const D=new Uint8Array(I.getImageTranscodedSizeInBytes(U,y,0,T));if(!I.transcodeImage(D,U,y,0,T,0,-1,-1))throw E(),new Error("THREE.KTX2Loader: .transcodeImage failed.");V.push(D)}R.push({data:h(V),width:O,height:P})}return E(),{width:Q,height:d,hasAlpha:m,mipmaps:R,format:M,dfdTransferFn:S,dfdFlags:w}}const a=[{if:"astcSupported",basisFormat:[s.UASTC_4x4],transcoderFormat:[n.ASTC_4x4,n.ASTC_4x4],engineFormat:[i.RGBA_ASTC_4x4_Format,i.RGBA_ASTC_4x4_Format],priorityETC1S:1/0,priorityUASTC:1,needsPowerOfTwo:!1},{if:"bptcSupported",basisFormat:[s.ETC1S,s.UASTC_4x4],transcoderFormat:[n.BC7_M5,n.BC7_M5],engineFormat:[i.RGBA_BPTC_Format,i.RGBA_BPTC_Format],priorityETC1S:3,priorityUASTC:2,needsPowerOfTwo:!1},{if:"dxtSupported",basisFormat:[s.ETC1S,s.UASTC_4x4],transcoderFormat:[n.BC1,n.BC3],engineFormat:[i.RGB_S3TC_DXT1_Format,i.RGBA_S3TC_DXT5_Format],priorityETC1S:4,priorityUASTC:5,needsPowerOfTwo:!1},{if:"etc2Supported",basisFormat:[s.ETC1S,s.UASTC_4x4],transcoderFormat:[n.ETC1,n.ETC2],engineFormat:[i.RGB_ETC2_Format,i.RGBA_ETC2_EAC_Format],priorityETC1S:1,priorityUASTC:3,needsPowerOfTwo:!1},{if:"etc1Supported",basisFormat:[s.ETC1S,s.UASTC_4x4],transcoderFormat:[n.ETC1],engineFormat:[i.RGB_ETC1_Format],priorityETC1S:2,priorityUASTC:4,needsPowerOfTwo:!1},{if:"pvrtcSupported",basisFormat:[s.ETC1S,s.UASTC_4x4],transcoderFormat:[n.PVRTC1_4_RGB,n.PVRTC1_4_RGBA],engineFormat:[i.RGB_PVRTC_4BPPV1_Format,i.RGBA_PVRTC_4BPPV1_Format],priorityETC1S:5,priorityUASTC:6,needsPowerOfTwo:!0}],l=a.sort(function(p,I){return p.priorityETC1S-I.priorityETC1S}),g=a.sort(function(p,I){return p.priorityUASTC-I.priorityUASTC});function u(p,I,E,B){let Q,d;const C=p===s.ETC1S?l:g;for(let x=0;x<C.length;x++){const m=C[x];if(c[m.if]&&m.basisFormat.includes(p)&&!(B&&m.transcoderFormat.length<2)&&!(m.needsPowerOfTwo&&!(f(I)&&f(E))))return Q=m.transcoderFormat[B?1:0],d=m.engineFormat[B?1:0],{transcoderFormat:Q,engineFormat:d}}return console.warn("THREE.KTX2Loader: No suitable compressed texture format found. Decoding to RGBA32."),Q=n.RGBA32,d=i.RGBAFormat,{transcoderFormat:Q,engineFormat:d}}function f(p){return p<=2?!0:(p&p-1)===0&&p!==0}function h(p){let I=0;for(let Q=0;Q<p.length;Q++){const d=p[Q];I+=d.byteLength}const E=new Uint8Array(I);let B=0;for(let Q=0;Q<p.length;Q++){const d=p[Q];E.set(d,B),B+=d.byteLength}return E}};const Jt={[qt]:r.RGBAFormat,[Ot]:r.RGBAFormat,[kt]:r.RGBAFormat,[st]:r.RGBAFormat,[Vt]:r.RGFormat,[Pt]:r.RGFormat,[Ut]:r.RGFormat,[it]:r.RGFormat,[Ht]:r.RedFormat,[Nt]:r.RedFormat,[nt]:r.RedFormat,[Gt]:r.RedFormat},ut={[qt]:r.FloatType,[Ot]:r.HalfFloatType,[kt]:r.UnsignedByteType,[st]:r.UnsignedByteType,[Vt]:r.FloatType,[Pt]:r.HalfFloatType,[Ut]:r.UnsignedByteType,[it]:r.UnsignedByteType,[Ht]:r.FloatType,[Nt]:r.HalfFloatType,[nt]:r.UnsignedByteType,[Gt]:r.UnsignedByteType},Ri={[st]:r.SRGBColorSpace,[it]:r.SRGBColorSpace,[nt]:r.SRGBColorSpace};async function Di(c){const{vkFormat:e,pixelWidth:t,pixelHeight:i,pixelDepth:n}=c;if(Jt[e]===void 0)throw new Error("THREE.KTX2Loader: Unsupported vkFormat.");const s=c.levels[0];let A,o;if(c.supercompressionScheme===Qi)A=s.levelData;else if(c.supercompressionScheme===yi)lt||(lt=new Promise(async l=>{const g=new vi;await g.init(),l(g)})),A=(await lt).decode(s.levelData,s.uncompressedByteLength);else throw new Error("THREE.KTX2Loader: Unsupported supercompressionScheme.");ut[e]===r.FloatType?o=new Float32Array(A.buffer,A.byteOffset,A.byteLength/Float32Array.BYTES_PER_ELEMENT):ut[e]===r.HalfFloatType?o=new Uint16Array(A.buffer,A.byteOffset,A.byteLength/Uint16Array.BYTES_PER_ELEMENT):o=A;const a=n===0?new r.DataTexture(o,t,i):new r.Data3DTexture(o,t,i,n);return a.type=ut[e],a.format=Jt[e],a.colorSpace=Ri[e]||r.NoColorSpace,a.needsUpdate=!0,Promise.resolve(a)}var Pe=function(){var c=Pe.SkyShader,e=new r.ShaderMaterial({fragmentShader:c.fragmentShader,vertexShader:c.vertexShader,uniforms:r.UniformsUtils.clone(c.uniforms),side:r.BackSide});return new r.Mesh(new r.BoxGeometry(1,1,1),e)};Pe.prototype=Object.create(r.Mesh.prototype),Pe.SkyShader={uniforms:{luminance:{value:1},turbidity:{value:2},rayleigh:{value:1},mieCoefficient:{value:.005},mieDirectionalG:{value:.8},sunPosition:{value:new r.Vector3},up:{value:new r.Vector3(0,1,0)}},vertexShader:["uniform vec3 sunPosition;","uniform float rayleigh;","uniform float turbidity;","uniform float mieCoefficient;","uniform vec3 up;","varying vec3 vWorldPosition;","varying vec3 vSunDirection;","varying float vSunfade;","varying vec3 vBetaR;","varying vec3 vBetaM;","varying float vSunE;","const float e = 2.71828182845904523536028747135266249775724709369995957;","const float pi = 3.141592653589793238462643383279502884197169;","const vec3 lambda = vec3( 680E-9, 550E-9, 450E-9 );","const vec3 totalRayleigh = vec3( 5.804542996261093E-6, 1.3562911419845635E-5, 3.0265902468824876E-5 );","const float v = 4.0;","const vec3 K = vec3( 0.686, 0.678, 0.666 );","const vec3 MieConst = vec3( 1.8399918514433978E14, 2.7798023919660528E14, 4.0790479543861094E14 );","const float cutoffAngle = 1.6110731556870734;","const float steepness = 1.5;","const float EE = 1000.0;","float sunIntensity( float zenithAngleCos ) {","	zenithAngleCos = clamp( zenithAngleCos, -1.0, 1.0 );","	return EE * max( 0.0, 1.0 - pow( e, -( ( cutoffAngle - acos( zenithAngleCos ) ) / steepness ) ) );","}","vec3 totalMie( float T ) {","	float c = ( 0.2 * T ) * 10E-18;","	return 0.434 * c * MieConst;","}","void main() {","	vec4 worldPosition = modelMatrix * vec4( position, 1.0 );","	vWorldPosition = worldPosition.xyz;","	gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );","	gl_Position.z = gl_Position.w;","	vSunDirection = normalize( sunPosition );","	vSunE = sunIntensity( dot( vSunDirection, up ) );","	vSunfade = 1.0 - clamp( 1.0 - exp( ( sunPosition.y / 450000.0 ) ), 0.0, 1.0 );","	float rayleighCoefficient = rayleigh - ( 1.0 * ( 1.0 - vSunfade ) );","	vBetaR = totalRayleigh * rayleighCoefficient;","	vBetaM = totalMie( turbidity ) * mieCoefficient;","}"].join(`
`),fragmentShader:["varying vec3 vWorldPosition;","varying vec3 vSunDirection;","varying float vSunfade;","varying vec3 vBetaR;","varying vec3 vBetaM;","varying float vSunE;","uniform float luminance;","uniform float mieDirectionalG;","uniform vec3 up;","const vec3 cameraPos = vec3( 0.0, 0.0, 0.0 );","const float pi = 3.141592653589793238462643383279502884197169;","const float n = 1.0003;","const float N = 2.545E25;","const float rayleighZenithLength = 8.4E3;","const float mieZenithLength = 1.25E3;","const float sunAngularDiameterCos = 0.999956676946448443553574619906976478926848692873900859324;","const float THREE_OVER_SIXTEENPI = 0.05968310365946075;","const float ONE_OVER_FOURPI = 0.07957747154594767;","float rayleighPhase( float cosTheta ) {","	return THREE_OVER_SIXTEENPI * ( 1.0 + pow( cosTheta, 2.0 ) );","}","float hgPhase( float cosTheta, float g ) {","	float g2 = pow( g, 2.0 );","	float inverse = 1.0 / pow( 1.0 - 2.0 * g * cosTheta + g2, 1.5 );","	return ONE_OVER_FOURPI * ( ( 1.0 - g2 ) * inverse );","}","const float A = 0.15;","const float B = 0.50;","const float C = 0.10;","const float D = 0.20;","const float E = 0.02;","const float F = 0.30;","const float whiteScale = 1.0748724675633854;","vec3 Uncharted2Tonemap( vec3 x ) {","	return ( ( x * ( A * x + C * B ) + D * E ) / ( x * ( A * x + B ) + D * F ) ) - E / F;","}","void main() {","	float zenithAngle = acos( max( 0.0, dot( up, normalize( vWorldPosition - cameraPos ) ) ) );","	float inverse = 1.0 / ( cos( zenithAngle ) + 0.15 * pow( 93.885 - ( ( zenithAngle * 180.0 ) / pi ), -1.253 ) );","	float sR = rayleighZenithLength * inverse;","	float sM = mieZenithLength * inverse;","	vec3 Fex = exp( -( vBetaR * sR + vBetaM * sM ) );","	float cosTheta = dot( normalize( vWorldPosition - cameraPos ), vSunDirection );","	float rPhase = rayleighPhase( cosTheta * 0.5 + 0.5 );","	vec3 betaRTheta = vBetaR * rPhase;","	float mPhase = hgPhase( cosTheta, mieDirectionalG );","	vec3 betaMTheta = vBetaM * mPhase;","	vec3 Lin = pow( vSunE * ( ( betaRTheta + betaMTheta ) / ( vBetaR + vBetaM ) ) * ( 1.0 - Fex ), vec3( 1.5 ) );","	Lin *= mix( vec3( 1.0 ), pow( vSunE * ( ( betaRTheta + betaMTheta ) / ( vBetaR + vBetaM ) ) * Fex, vec3( 1.0 / 2.0 ) ), clamp( pow( 1.0 - dot( up, vSunDirection ), 5.0 ), 0.0, 1.0 ) );","	vec3 direction = normalize( vWorldPosition - cameraPos );","	float theta = acos( direction.y ); // elevation --> y-axis, [-pi/2, pi/2]","	float phi = atan( direction.z, direction.x ); // azimuth --> x-axis [-pi/2, pi/2]","	vec2 uv = vec2( phi, theta ) / vec2( 2.0 * pi, pi ) + vec2( 0.5, 0.0 );","	vec3 L0 = vec3( 0.1 ) * Fex;","	float sundisk = smoothstep( sunAngularDiameterCos, sunAngularDiameterCos + 0.00002, cosTheta );","	L0 += ( vSunE * 19000.0 * Fex ) * sundisk;","	vec3 texColor = ( Lin + L0 ) * 0.04 + vec3( 0.0, 0.0003, 0.00075 );","	vec3 curr = Uncharted2Tonemap( ( log2( 2.0 / pow( luminance, 4.0 ) ) ) * texColor );","	vec3 color = curr * whiteScale;","	vec3 retColor = pow( color, vec3( 1.0 / ( 1.2 + ( 1.2 * vSunfade ) ) ) );","	gl_FragColor = vec4( retColor, 1.0 );","}"].join(`
`)};/*!
fflate - fast JavaScript compression/decompression
<https://101arrowz.github.io/fflate>
Licensed under MIT. https://github.com/101arrowz/fflate/blob/master/LICENSE
version 0.6.9
*/var zt=function(c){return URL.createObjectURL(new Blob([c],{type:"text/javascript"}))};try{URL.revokeObjectURL(zt(""))}catch{zt=function(e){return"data:application/javascript;charset=UTF-8,"+encodeURI(e)}}var ae=Uint8Array,Be=Uint16Array,gt=Uint32Array,Xt=new ae([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),jt=new ae([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),Li=new ae([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),Wt=function(c,e){for(var t=new Be(31),i=0;i<31;++i)t[i]=e+=1<<c[i-1];for(var n=new gt(t[30]),i=1;i<30;++i)for(var s=t[i];s<t[i+1];++s)n[s]=s-t[i]<<5|i;return[t,n]},Zt=Wt(Xt,2),$t=Zt[0],Fi=Zt[1];$t[28]=258,Fi[258]=28;for(var bi=Wt(jt,0),_i=bi[0],ft=new Be(32768),j=0;j<32768;++j){var Ce=(j&43690)>>>1|(j&21845)<<1;Ce=(Ce&52428)>>>2|(Ce&13107)<<2,Ce=(Ce&61680)>>>4|(Ce&3855)<<4,ft[j]=((Ce&65280)>>>8|(Ce&255)<<8)>>>1}for(var Fe=function(c,e,t){for(var i=c.length,n=0,s=new Be(e);n<i;++n)++s[c[n]-1];var A=new Be(e);for(n=0;n<e;++n)A[n]=A[n-1]+s[n-1]<<1;var o;if(t){o=new Be(1<<e);var a=15-e;for(n=0;n<i;++n)if(c[n])for(var l=n<<4|c[n],g=e-c[n],u=A[c[n]-1]++<<g,f=u|(1<<g)-1;u<=f;++u)o[ft[u]>>>a]=l}else for(o=new Be(i),n=0;n<i;++n)c[n]&&(o[n]=ft[A[c[n]-1]++]>>>15-c[n]);return o},be=new ae(288),j=0;j<144;++j)be[j]=8;for(var j=144;j<256;++j)be[j]=9;for(var j=256;j<280;++j)be[j]=7;for(var j=280;j<288;++j)be[j]=8;for(var en=new ae(32),j=0;j<32;++j)en[j]=5;var Gi=Fe(be,9,1),Ui=Fe(en,5,1),ht=function(c){for(var e=c[0],t=1;t<c.length;++t)c[t]>e&&(e=c[t]);return e},ce=function(c,e,t){var i=e/8|0;return(c[i]|c[i+1]<<8)>>(e&7)&t},dt=function(c,e){var t=e/8|0;return(c[t]|c[t+1]<<8|c[t+2]<<16)>>(e&7)},ki=function(c){return(c/8|0)+(c&7&&1)},Ni=function(c,e,t){(e==null||e<0)&&(e=0),(t==null||t>c.length)&&(t=c.length);var i=new(c instanceof Be?Be:c instanceof gt?gt:ae)(t-e);return i.set(c.subarray(e,t)),i},Pi=function(c,e,t){var i=c.length;if(!i||t&&!t.l&&i<5)return e||new ae(0);var n=!e||t,s=!t||t.i;t||(t={}),e||(e=new ae(i*3));var A=function(v){var _=e.length;if(v>_){var X=new ae(Math.max(_*2,v));X.set(e),e=X}},o=t.f||0,a=t.p||0,l=t.b||0,g=t.l,u=t.d,f=t.m,h=t.n,p=i*8;do{if(!g){t.f=o=ce(c,a,1);var I=ce(c,a+1,3);if(a+=3,I)if(I==1)g=Gi,u=Ui,f=9,h=5;else if(I==2){var d=ce(c,a,31)+257,C=ce(c,a+10,15)+4,x=d+ce(c,a+5,31)+1;a+=14;for(var m=new ae(x),S=new ae(19),w=0;w<C;++w)S[Li[w]]=ce(c,a+w*3,7);a+=C*3;for(var T=ht(S),M=(1<<T)-1,R=Fe(S,T,1),w=0;w<x;){var U=R[ce(c,a,M)];a+=U&15;var E=U>>>4;if(E<16)m[w++]=E;else{var V=0,O=0;for(E==16?(O=3+ce(c,a,3),a+=2,V=m[w-1]):E==17?(O=3+ce(c,a,7),a+=3):E==18&&(O=11+ce(c,a,127),a+=7);O--;)m[w++]=V}}var P=m.subarray(0,d),y=m.subarray(d);f=ht(P),h=ht(y),g=Fe(P,f,1),u=Fe(y,h,1)}else throw"invalid block type";else{var E=ki(a)+4,B=c[E-4]|c[E-3]<<8,Q=E+B;if(Q>i){if(s)throw"unexpected EOF";break}n&&A(l+B),e.set(c.subarray(E,Q),l),t.b=l+=B,t.p=a=Q*8;continue}if(a>p){if(s)throw"unexpected EOF";break}}n&&A(l+131072);for(var L=(1<<f)-1,D=(1<<h)-1,k=a;;k=a){var V=g[dt(c,a)&L],N=V>>>4;if(a+=V&15,a>p){if(s)throw"unexpected EOF";break}if(!V)throw"invalid length/literal";if(N<256)e[l++]=N;else if(N==256){k=a,g=null;break}else{var Y=N-254;if(N>264){var w=N-257,K=Xt[w];Y=ce(c,a,(1<<K)-1)+$t[w],a+=K}var G=u[dt(c,a)&D],b=G>>>4;if(!G)throw"invalid distance";a+=G&15;var y=_i[b];if(b>3){var K=jt[b];y+=dt(c,a)&(1<<K)-1,a+=K}if(a>p){if(s)throw"unexpected EOF";break}n&&A(l+131072);for(var F=l+Y;l<F;l+=4)e[l]=e[l-y],e[l+1]=e[l+1-y],e[l+2]=e[l+2-y],e[l+3]=e[l+3-y];l=F}}t.l=g,t.p=k,t.b=l,g&&(o=1,t.m=f,t.d=u,t.n=h)}while(!o);return l==e.length?e:Ni(e,0,l)},Oi=new ae(0),Hi=function(c){if((c[0]&15)!=8||c[0]>>>4>7||(c[0]<<8|c[1])%31)throw"invalid zlib data";if(c[1]&32)throw"invalid zlib data: preset dictionaries not supported"};function Vi(c,e){return Pi((Hi(c),c.subarray(2,-4)),e)}var qi=typeof TextDecoder<"u"&&new TextDecoder,Yi=0;try{qi.decode(Oi,{stream:!0}),Yi=1}catch{}function tn(c,e,t){const i=t.length-c-1;if(e>=t[i])return i-1;if(e<=t[c])return c;let n=c,s=i,A=Math.floor((n+s)/2);for(;e<t[A]||e>=t[A+1];)e<t[A]?s=A:n=A,A=Math.floor((n+s)/2);return A}function Ki(c,e,t,i){const n=[],s=[],A=[];n[0]=1;for(let o=1;o<=t;++o){s[o]=e-i[c+1-o],A[o]=i[c+o]-e;let a=0;for(let l=0;l<o;++l){const g=A[l+1],u=s[o-l],f=n[l]/(g+u);n[l]=a+g*f,a=u*f}n[o]=a}return n}function Ji(c,e,t,i){const n=tn(c,i,e),s=Ki(n,i,c,e),A=new r.Vector4(0,0,0,0);for(let o=0;o<=c;++o){const a=t[n-c+o],l=s[o],g=a.w*l;A.x+=a.x*g,A.y+=a.y*g,A.z+=a.z*g,A.w+=a.w*l}return A}function zi(c,e,t,i,n){const s=[];for(let u=0;u<=t;++u)s[u]=0;const A=[];for(let u=0;u<=i;++u)A[u]=s.slice(0);const o=[];for(let u=0;u<=t;++u)o[u]=s.slice(0);o[0][0]=1;const a=s.slice(0),l=s.slice(0);for(let u=1;u<=t;++u){a[u]=e-n[c+1-u],l[u]=n[c+u]-e;let f=0;for(let h=0;h<u;++h){const p=l[h+1],I=a[u-h];o[u][h]=p+I;const E=o[h][u-1]/o[u][h];o[h][u]=f+p*E,f=I*E}o[u][u]=f}for(let u=0;u<=t;++u)A[0][u]=o[u][t];for(let u=0;u<=t;++u){let f=0,h=1;const p=[];for(let I=0;I<=t;++I)p[I]=s.slice(0);p[0][0]=1;for(let I=1;I<=i;++I){let E=0;const B=u-I,Q=t-I;u>=I&&(p[h][0]=p[f][0]/o[Q+1][B],E=p[h][0]*o[B][Q]);const d=B>=-1?1:-B,C=u-1<=Q?I-1:t-u;for(let m=d;m<=C;++m)p[h][m]=(p[f][m]-p[f][m-1])/o[Q+1][B+m],E+=p[h][m]*o[B+m][Q];u<=Q&&(p[h][I]=-p[f][I-1]/o[Q+1][u],E+=p[h][I]*o[u][Q]),A[I][u]=E;const x=f;f=h,h=x}}let g=t;for(let u=1;u<=i;++u){for(let f=0;f<=t;++f)A[u][f]*=g;g*=t-u}return A}function Xi(c,e,t,i,n){const s=n<c?n:c,A=[],o=tn(c,i,e),a=zi(o,i,c,s,e),l=[];for(let g=0;g<t.length;++g){const u=t[g].clone(),f=u.w;u.x*=f,u.y*=f,u.z*=f,l[g]=u}for(let g=0;g<=s;++g){const u=l[o-c].clone().multiplyScalar(a[g][0]);for(let f=1;f<=c;++f)u.add(l[o-c+f].clone().multiplyScalar(a[g][f]));A[g]=u}for(let g=s+1;g<=n+1;++g)A[g]=new r.Vector4(0,0,0);return A}function ji(c,e){let t=1;for(let n=2;n<=c;++n)t*=n;let i=1;for(let n=2;n<=e;++n)i*=n;for(let n=2;n<=c-e;++n)i*=n;return t/i}function Wi(c){const e=c.length,t=[],i=[];for(let s=0;s<e;++s){const A=c[s];t[s]=new r.Vector3(A.x,A.y,A.z),i[s]=A.w}const n=[];for(let s=0;s<e;++s){const A=t[s].clone();for(let o=1;o<=s;++o)A.sub(n[s-o].clone().multiplyScalar(ji(s,o)*i[o]));n[s]=A.divideScalar(i[0])}return n}function Zi(c,e,t,i,n){const s=Xi(c,e,t,i,n);return Wi(s)}class $i extends r.Curve{constructor(e,t,i,n,s){super(),this.degree=e,this.knots=t,this.controlPoints=[],this.startKnot=n||0,this.endKnot=s||this.knots.length-1;for(let A=0;A<i.length;++A){const o=i[A];this.controlPoints[A]=new r.Vector4(o.x,o.y,o.z,o.w)}}getPoint(e,t=new r.Vector3){const i=t,n=this.knots[this.startKnot]+e*(this.knots[this.endKnot]-this.knots[this.startKnot]),s=Ji(this.degree,this.knots,this.controlPoints,n);return s.w!==1&&s.divideScalar(s.w),i.set(s.x,s.y,s.z)}getTangent(e,t=new r.Vector3){const i=t,n=this.knots[0]+e*(this.knots[this.knots.length-1]-this.knots[0]),s=Zi(this.degree,this.knots,this.controlPoints,n,1);return i.copy(s[1]).normalize(),i}}let H,$,se;class es extends r.Loader{constructor(e){super(e)}load(e,t,i,n){const s=this,A=s.path===""?r.LoaderUtils.extractUrlBase(e):s.path,o=new r.FileLoader(this.manager);o.setPath(s.path),o.setResponseType("arraybuffer"),o.setRequestHeader(s.requestHeader),o.setWithCredentials(s.withCredentials),o.load(e,function(a){try{t(s.parse(a,A))}catch(l){n?n(l):console.error(l),s.manager.itemError(e)}},i,n)}parse(e,t){if(os(e))H=new As().parse(e);else{const n=an(e);if(!rs(n))throw new Error("THREE.FBXLoader: Unknown format.");if(An(n)<7e3)throw new Error("THREE.FBXLoader: FBX version not supported, FileVersion: "+An(n));H=new ss().parse(n)}const i=new r.TextureLoader(this.manager).setPath(this.resourcePath||t).setCrossOrigin(this.crossOrigin);return new ts(i,this.manager).parse(H)}}class ts{constructor(e,t){this.textureLoader=e,this.manager=t}parse(){$=this.parseConnections();const e=this.parseImages(),t=this.parseTextures(e),i=this.parseMaterials(t),n=this.parseDeformers(),s=new ns().parse(n);return this.parseScene(n,s,i),se}parseConnections(){const e=new Map;return"Connections"in H&&H.Connections.connections.forEach(function(i){const n=i[0],s=i[1],A=i[2];e.has(n)||e.set(n,{parents:[],children:[]});const o={ID:s,relationship:A};e.get(n).parents.push(o),e.has(s)||e.set(s,{parents:[],children:[]});const a={ID:n,relationship:A};e.get(s).children.push(a)}),e}parseImages(){const e={},t={};if("Video"in H.Objects){const i=H.Objects.Video;for(const n in i){const s=i[n],A=parseInt(n);if(e[A]=s.RelativeFilename||s.Filename,"Content"in s){const o=s.Content instanceof ArrayBuffer&&s.Content.byteLength>0,a=typeof s.Content=="string"&&s.Content!=="";if(o||a){const l=this.parseImage(i[n]);t[s.RelativeFilename||s.Filename]=l}}}}for(const i in e){const n=e[i];t[n]!==void 0?e[i]=t[n]:e[i]=e[i].split("\\").pop()}return e}parseImage(e){const t=e.Content,i=e.RelativeFilename||e.Filename,n=i.slice(i.lastIndexOf(".")+1).toLowerCase();let s;switch(n){case"bmp":s="image/bmp";break;case"jpg":case"jpeg":s="image/jpeg";break;case"png":s="image/png";break;case"tif":s="image/tiff";break;case"tga":this.manager.getHandler(".tga")===null&&console.warn("FBXLoader: TGA loader not found, skipping ",i),s="image/tga";break;default:console.warn('FBXLoader: Image type "'+n+'" is not supported.');return}if(typeof t=="string")return"data:"+s+";base64,"+t;{const A=new Uint8Array(t);return window.URL.createObjectURL(new Blob([A],{type:s}))}}parseTextures(e){const t=new Map;if("Texture"in H.Objects){const i=H.Objects.Texture;for(const n in i){const s=this.parseTexture(i[n],e);t.set(parseInt(n),s)}}return t}parseTexture(e,t){const i=this.loadTexture(e,t);i.ID=e.id,i.name=e.attrName;const n=e.WrapModeU,s=e.WrapModeV,A=n!==void 0?n.value:0,o=s!==void 0?s.value:0;if(i.wrapS=A===0?r.RepeatWrapping:r.ClampToEdgeWrapping,i.wrapT=o===0?r.RepeatWrapping:r.ClampToEdgeWrapping,"Scaling"in e){const a=e.Scaling.value;i.repeat.x=a[0],i.repeat.y=a[1]}if("Translation"in e){const a=e.Translation.value;i.offset.x=a[0],i.offset.y=a[1]}return i}loadTexture(e,t){let i;const n=this.textureLoader.path,s=$.get(e.id).children;s!==void 0&&s.length>0&&t[s[0].ID]!==void 0&&(i=t[s[0].ID],(i.indexOf("blob:")===0||i.indexOf("data:")===0)&&this.textureLoader.setPath(void 0));let A;const o=e.FileName.slice(-3).toLowerCase();if(o==="tga"){const a=this.manager.getHandler(".tga");a===null?(console.warn("FBXLoader: TGA loader not found, creating placeholder texture for",e.RelativeFilename),A=new r.Texture):(a.setPath(this.textureLoader.path),A=a.load(i))}else o==="psd"?(console.warn("FBXLoader: PSD textures are not supported, creating placeholder texture for",e.RelativeFilename),A=new r.Texture):A=this.textureLoader.load(i);return this.textureLoader.setPath(n),A}parseMaterials(e){const t=new Map;if("Material"in H.Objects){const i=H.Objects.Material;for(const n in i){const s=this.parseMaterial(i[n],e);s!==null&&t.set(parseInt(n),s)}}return t}parseMaterial(e,t){const i=e.id,n=e.attrName;let s=e.ShadingModel;if(typeof s=="object"&&(s=s.value),!$.has(i))return null;const A=this.parseParameters(e,t,i);let o;switch(s.toLowerCase()){case"phong":o=new r.MeshPhongMaterial;break;case"lambert":o=new r.MeshLambertMaterial;break;default:console.warn('THREE.FBXLoader: unknown material type "%s". Defaulting to MeshPhongMaterial.',s),o=new r.MeshPhongMaterial;break}return o.setValues(A),o.name=n,o}parseParameters(e,t,i){const n={};e.BumpFactor&&(n.bumpScale=e.BumpFactor.value),e.Diffuse?n.color=new r.Color().fromArray(e.Diffuse.value).convertSRGBToLinear():e.DiffuseColor&&(e.DiffuseColor.type==="Color"||e.DiffuseColor.type==="ColorRGB")&&(n.color=new r.Color().fromArray(e.DiffuseColor.value).convertSRGBToLinear()),e.DisplacementFactor&&(n.displacementScale=e.DisplacementFactor.value),e.Emissive?n.emissive=new r.Color().fromArray(e.Emissive.value).convertSRGBToLinear():e.EmissiveColor&&(e.EmissiveColor.type==="Color"||e.EmissiveColor.type==="ColorRGB")&&(n.emissive=new r.Color().fromArray(e.EmissiveColor.value).convertSRGBToLinear()),e.EmissiveFactor&&(n.emissiveIntensity=parseFloat(e.EmissiveFactor.value)),e.Opacity&&(n.opacity=parseFloat(e.Opacity.value)),n.opacity<1&&(n.transparent=!0),e.ReflectionFactor&&(n.reflectivity=e.ReflectionFactor.value),e.Shininess&&(n.shininess=e.Shininess.value),e.Specular?n.specular=new r.Color().fromArray(e.Specular.value).convertSRGBToLinear():e.SpecularColor&&e.SpecularColor.type==="Color"&&(n.specular=new r.Color().fromArray(e.SpecularColor.value).convertSRGBToLinear());const s=this;return $.get(i).children.forEach(function(A){const o=A.relationship;switch(o){case"Bump":n.bumpMap=s.getTexture(t,A.ID);break;case"Maya|TEX_ao_map":n.aoMap=s.getTexture(t,A.ID);break;case"DiffuseColor":case"Maya|TEX_color_map":n.map=s.getTexture(t,A.ID),n.map!==void 0&&(n.map.colorSpace=r.SRGBColorSpace);break;case"DisplacementColor":n.displacementMap=s.getTexture(t,A.ID);break;case"EmissiveColor":n.emissiveMap=s.getTexture(t,A.ID),n.emissiveMap!==void 0&&(n.emissiveMap.colorSpace=r.SRGBColorSpace);break;case"NormalMap":case"Maya|TEX_normal_map":n.normalMap=s.getTexture(t,A.ID);break;case"ReflectionColor":n.envMap=s.getTexture(t,A.ID),n.envMap!==void 0&&(n.envMap.mapping=r.EquirectangularReflectionMapping,n.envMap.colorSpace=r.SRGBColorSpace);break;case"SpecularColor":n.specularMap=s.getTexture(t,A.ID),n.specularMap!==void 0&&(n.specularMap.colorSpace=r.SRGBColorSpace);break;case"TransparentColor":case"TransparencyFactor":n.alphaMap=s.getTexture(t,A.ID),n.transparent=!0;break;case"AmbientColor":case"ShininessExponent":case"SpecularFactor":case"VectorDisplacementColor":default:console.warn("THREE.FBXLoader: %s map is not supported in three.js, skipping texture.",o);break}}),n}getTexture(e,t){return"LayeredTexture"in H.Objects&&t in H.Objects.LayeredTexture&&(console.warn("THREE.FBXLoader: layered textures are not supported in three.js. Discarding all but first layer."),t=$.get(t).children[0].ID),e.get(t)}parseDeformers(){const e={},t={};if("Deformer"in H.Objects){const i=H.Objects.Deformer;for(const n in i){const s=i[n],A=$.get(parseInt(n));if(s.attrType==="Skin"){const o=this.parseSkeleton(A,i);o.ID=n,A.parents.length>1&&console.warn("THREE.FBXLoader: skeleton attached to more than one geometry is not supported."),o.geometryID=A.parents[0].ID,e[n]=o}else if(s.attrType==="BlendShape"){const o={id:n};o.rawTargets=this.parseMorphTargets(A,i),o.id=n,A.parents.length>1&&console.warn("THREE.FBXLoader: morph target attached to more than one geometry is not supported."),t[n]=o}}}return{skeletons:e,morphTargets:t}}parseSkeleton(e,t){const i=[];return e.children.forEach(function(n){const s=t[n.ID];if(s.attrType!=="Cluster")return;const A={ID:n.ID,indices:[],weights:[],transformLink:new r.Matrix4().fromArray(s.TransformLink.a)};"Indexes"in s&&(A.indices=s.Indexes.a,A.weights=s.Weights.a),i.push(A)}),{rawBones:i,bones:[]}}parseMorphTargets(e,t){const i=[];for(let n=0;n<e.children.length;n++){const s=e.children[n],A=t[s.ID],o={name:A.attrName,initialWeight:A.DeformPercent,id:A.id,fullWeights:A.FullWeights.a};if(A.attrType!=="BlendShapeChannel")return;o.geoID=$.get(parseInt(s.ID)).children.filter(function(a){return a.relationship===void 0})[0].ID,i.push(o)}return i}parseScene(e,t,i){se=new r.Group;const n=this.parseModels(e.skeletons,t,i),s=H.Objects.Model,A=this;n.forEach(function(a){const l=s[a.ID];A.setLookAtProperties(a,l),$.get(a.ID).parents.forEach(function(u){const f=n.get(u.ID);f!==void 0&&f.add(a)}),a.parent===null&&se.add(a)}),this.bindSkeleton(e.skeletons,t,n),this.createAmbientLight(),se.traverse(function(a){if(a.userData.transformData){a.parent&&(a.userData.transformData.parentMatrix=a.parent.matrix,a.userData.transformData.parentMatrixWorld=a.parent.matrixWorld);const l=on(a.userData.transformData);a.applyMatrix4(l),a.updateWorldMatrix()}});const o=new is().parse();se.children.length===1&&se.children[0].isGroup&&(se.children[0].animations=o,se=se.children[0]),se.animations=o}parseModels(e,t,i){const n=new Map,s=H.Objects.Model;for(const A in s){const o=parseInt(A),a=s[A],l=$.get(o);let g=this.buildSkeleton(l,e,o,a.attrName);if(!g){switch(a.attrType){case"Camera":g=this.createCamera(l);break;case"Light":g=this.createLight(l);break;case"Mesh":g=this.createMesh(l,t,i);break;case"NurbsCurve":g=this.createCurve(l,t);break;case"LimbNode":case"Root":g=new r.Bone;break;case"Null":default:g=new r.Group;break}g.name=a.attrName?r.PropertyBinding.sanitizeNodeName(a.attrName):"",g.ID=o}this.getTransformData(g,a),n.set(o,g)}return n}buildSkeleton(e,t,i,n){let s=null;return e.parents.forEach(function(A){for(const o in t){const a=t[o];a.rawBones.forEach(function(l,g){if(l.ID===A.ID){const u=s;s=new r.Bone,s.matrixWorld.copy(l.transformLink),s.name=n?r.PropertyBinding.sanitizeNodeName(n):"",s.ID=i,a.bones[g]=s,u!==null&&s.add(u)}})}}),s}createCamera(e){let t,i;if(e.children.forEach(function(n){const s=H.Objects.NodeAttribute[n.ID];s!==void 0&&(i=s)}),i===void 0)t=new r.Object3D;else{let n=0;i.CameraProjectionType!==void 0&&i.CameraProjectionType.value===1&&(n=1);let s=1;i.NearPlane!==void 0&&(s=i.NearPlane.value/1e3);let A=1e3;i.FarPlane!==void 0&&(A=i.FarPlane.value/1e3);let o=window.innerWidth,a=window.innerHeight;i.AspectWidth!==void 0&&i.AspectHeight!==void 0&&(o=i.AspectWidth.value,a=i.AspectHeight.value);const l=o/a;let g=45;i.FieldOfView!==void 0&&(g=i.FieldOfView.value);const u=i.FocalLength?i.FocalLength.value:null;switch(n){case 0:t=new r.PerspectiveCamera(g,l,s,A),u!==null&&t.setFocalLength(u);break;case 1:t=new r.OrthographicCamera(-o/2,o/2,a/2,-a/2,s,A);break;default:console.warn("THREE.FBXLoader: Unknown camera type "+n+"."),t=new r.Object3D;break}}return t}createLight(e){let t,i;if(e.children.forEach(function(n){const s=H.Objects.NodeAttribute[n.ID];s!==void 0&&(i=s)}),i===void 0)t=new r.Object3D;else{let n;i.LightType===void 0?n=0:n=i.LightType.value;let s=16777215;i.Color!==void 0&&(s=new r.Color().fromArray(i.Color.value).convertSRGBToLinear());let A=i.Intensity===void 0?1:i.Intensity.value/100;i.CastLightOnObject!==void 0&&i.CastLightOnObject.value===0&&(A=0);let o=0;i.FarAttenuationEnd!==void 0&&(i.EnableFarAttenuation!==void 0&&i.EnableFarAttenuation.value===0?o=0:o=i.FarAttenuationEnd.value);const a=1;switch(n){case 0:t=new r.PointLight(s,A,o,a);break;case 1:t=new r.DirectionalLight(s,A);break;case 2:let l=Math.PI/3;i.InnerAngle!==void 0&&(l=r.MathUtils.degToRad(i.InnerAngle.value));let g=0;i.OuterAngle!==void 0&&(g=r.MathUtils.degToRad(i.OuterAngle.value),g=Math.max(g,1)),t=new r.SpotLight(s,A,o,l,g,a);break;default:console.warn("THREE.FBXLoader: Unknown light type "+i.LightType.value+", defaulting to a PointLight."),t=new r.PointLight(s,A);break}i.CastShadows!==void 0&&i.CastShadows.value===1&&(t.castShadow=!0)}return t}createMesh(e,t,i){let n,s=null,A=null;const o=[];return e.children.forEach(function(a){t.has(a.ID)&&(s=t.get(a.ID)),i.has(a.ID)&&o.push(i.get(a.ID))}),o.length>1?A=o:o.length>0?A=o[0]:(A=new r.MeshPhongMaterial({color:13421772}),o.push(A)),"color"in s.attributes&&o.forEach(function(a){a.vertexColors=!0}),s.FBX_Deformer?(n=new r.SkinnedMesh(s,A),n.normalizeSkinWeights()):n=new r.Mesh(s,A),n}createCurve(e,t){const i=e.children.reduce(function(s,A){return t.has(A.ID)&&(s=t.get(A.ID)),s},null),n=new r.LineBasicMaterial({color:3342591,linewidth:1});return new r.Line(i,n)}getTransformData(e,t){const i={};"InheritType"in t&&(i.inheritType=parseInt(t.InheritType.value)),"RotationOrder"in t?i.eulerOrder=rn(t.RotationOrder.value):i.eulerOrder="ZYX","Lcl_Translation"in t&&(i.translation=t.Lcl_Translation.value),"PreRotation"in t&&(i.preRotation=t.PreRotation.value),"Lcl_Rotation"in t&&(i.rotation=t.Lcl_Rotation.value),"PostRotation"in t&&(i.postRotation=t.PostRotation.value),"Lcl_Scaling"in t&&(i.scale=t.Lcl_Scaling.value),"ScalingOffset"in t&&(i.scalingOffset=t.ScalingOffset.value),"ScalingPivot"in t&&(i.scalingPivot=t.ScalingPivot.value),"RotationOffset"in t&&(i.rotationOffset=t.RotationOffset.value),"RotationPivot"in t&&(i.rotationPivot=t.RotationPivot.value),e.userData.transformData=i}setLookAtProperties(e,t){"LookAtProperty"in t&&$.get(e.ID).children.forEach(function(n){if(n.relationship==="LookAtProperty"){const s=H.Objects.Model[n.ID];if("Lcl_Translation"in s){const A=s.Lcl_Translation.value;e.target!==void 0?(e.target.position.fromArray(A),se.add(e.target)):e.lookAt(new r.Vector3().fromArray(A))}}})}bindSkeleton(e,t,i){const n=this.parsePoseNodes();for(const s in e){const A=e[s];$.get(parseInt(A.ID)).parents.forEach(function(a){if(t.has(a.ID)){const l=a.ID;$.get(l).parents.forEach(function(u){i.has(u.ID)&&i.get(u.ID).bind(new r.Skeleton(A.bones),n[u.ID])})}})}}parsePoseNodes(){const e={};if("Pose"in H.Objects){const t=H.Objects.Pose;for(const i in t)if(t[i].attrType==="BindPose"&&t[i].NbPoseNodes>0){const n=t[i].PoseNode;Array.isArray(n)?n.forEach(function(s){e[s.Node]=new r.Matrix4().fromArray(s.Matrix.a)}):e[n.Node]=new r.Matrix4().fromArray(n.Matrix.a)}}return e}createAmbientLight(){if("GlobalSettings"in H&&"AmbientColor"in H.GlobalSettings){const e=H.GlobalSettings.AmbientColor.value,t=e[0],i=e[1],n=e[2];if(t!==0||i!==0||n!==0){const s=new r.Color(t,i,n).convertSRGBToLinear();se.add(new r.AmbientLight(s,1))}}}}class ns{constructor(){this.negativeMaterialIndices=!1}parse(e){const t=new Map;if("Geometry"in H.Objects){const i=H.Objects.Geometry;for(const n in i){const s=$.get(parseInt(n)),A=this.parseGeometry(s,i[n],e);t.set(parseInt(n),A)}}return this.negativeMaterialIndices===!0&&console.warn("THREE.FBXLoader: The FBX file contains invalid (negative) material indices. The asset might not render as expected."),t}parseGeometry(e,t,i){switch(t.attrType){case"Mesh":return this.parseMeshGeometry(e,t,i);case"NurbsCurve":return this.parseNurbsGeometry(t)}}parseMeshGeometry(e,t,i){const n=i.skeletons,s=[],A=e.parents.map(function(u){return H.Objects.Model[u.ID]});if(A.length===0)return;const o=e.children.reduce(function(u,f){return n[f.ID]!==void 0&&(u=n[f.ID]),u},null);e.children.forEach(function(u){i.morphTargets[u.ID]!==void 0&&s.push(i.morphTargets[u.ID])});const a=A[0],l={};"RotationOrder"in a&&(l.eulerOrder=rn(a.RotationOrder.value)),"InheritType"in a&&(l.inheritType=parseInt(a.InheritType.value)),"GeometricTranslation"in a&&(l.translation=a.GeometricTranslation.value),"GeometricRotation"in a&&(l.rotation=a.GeometricRotation.value),"GeometricScaling"in a&&(l.scale=a.GeometricScaling.value);const g=on(l);return this.genGeometry(t,o,s,g)}genGeometry(e,t,i,n){const s=new r.BufferGeometry;e.attrName&&(s.name=e.attrName);const A=this.parseGeoNode(e,t),o=this.genBuffers(A),a=new r.Float32BufferAttribute(o.vertex,3);if(a.applyMatrix4(n),s.setAttribute("position",a),o.colors.length>0&&s.setAttribute("color",new r.Float32BufferAttribute(o.colors,3)),t&&(s.setAttribute("skinIndex",new r.Uint16BufferAttribute(o.weightsIndices,4)),s.setAttribute("skinWeight",new r.Float32BufferAttribute(o.vertexWeights,4)),s.FBX_Deformer=t),o.normal.length>0){const l=new r.Matrix3().getNormalMatrix(n),g=new r.Float32BufferAttribute(o.normal,3);g.applyNormalMatrix(l),s.setAttribute("normal",g)}if(o.uvs.forEach(function(l,g){const u=g===0?"uv":`uv${g}`;s.setAttribute(u,new r.Float32BufferAttribute(o.uvs[g],2))}),A.material&&A.material.mappingType!=="AllSame"){let l=o.materialIndex[0],g=0;if(o.materialIndex.forEach(function(u,f){u!==l&&(s.addGroup(g,f-g,l),l=u,g=f)}),s.groups.length>0){const u=s.groups[s.groups.length-1],f=u.start+u.count;f!==o.materialIndex.length&&s.addGroup(f,o.materialIndex.length-f,l)}s.groups.length===0&&s.addGroup(0,o.materialIndex.length,o.materialIndex[0])}return this.addMorphTargets(s,e,i,n),s}parseGeoNode(e,t){const i={};if(i.vertexPositions=e.Vertices!==void 0?e.Vertices.a:[],i.vertexIndices=e.PolygonVertexIndex!==void 0?e.PolygonVertexIndex.a:[],e.LayerElementColor&&(i.color=this.parseVertexColors(e.LayerElementColor[0])),e.LayerElementMaterial&&(i.material=this.parseMaterialIndices(e.LayerElementMaterial[0])),e.LayerElementNormal&&(i.normal=this.parseNormals(e.LayerElementNormal[0])),e.LayerElementUV){i.uv=[];let n=0;for(;e.LayerElementUV[n];)e.LayerElementUV[n].UV&&i.uv.push(this.parseUVs(e.LayerElementUV[n])),n++}return i.weightTable={},t!==null&&(i.skeleton=t,t.rawBones.forEach(function(n,s){n.indices.forEach(function(A,o){i.weightTable[A]===void 0&&(i.weightTable[A]=[]),i.weightTable[A].push({id:s,weight:n.weights[o]})})})),i}genBuffers(e){const t={vertex:[],normal:[],colors:[],uvs:[],materialIndex:[],vertexWeights:[],weightsIndices:[]};let i=0,n=0,s=!1,A=[],o=[],a=[],l=[],g=[],u=[];const f=this;return e.vertexIndices.forEach(function(h,p){let I,E=!1;h<0&&(h=h^-1,E=!0);let B=[],Q=[];if(A.push(h*3,h*3+1,h*3+2),e.color){const d=Oe(p,i,h,e.color);a.push(d[0],d[1],d[2])}if(e.skeleton){if(e.weightTable[h]!==void 0&&e.weightTable[h].forEach(function(d){Q.push(d.weight),B.push(d.id)}),Q.length>4){s||(console.warn("THREE.FBXLoader: Vertex has more than 4 skinning weights assigned to vertex. Deleting additional weights."),s=!0);const d=[0,0,0,0],C=[0,0,0,0];Q.forEach(function(x,m){let S=x,w=B[m];C.forEach(function(T,M,R){if(S>T){R[M]=S,S=T;const U=d[M];d[M]=w,w=U}})}),B=d,Q=C}for(;Q.length<4;)Q.push(0),B.push(0);for(let d=0;d<4;++d)g.push(Q[d]),u.push(B[d])}if(e.normal){const d=Oe(p,i,h,e.normal);o.push(d[0],d[1],d[2])}e.material&&e.material.mappingType!=="AllSame"&&(I=Oe(p,i,h,e.material)[0],I<0&&(f.negativeMaterialIndices=!0,I=0)),e.uv&&e.uv.forEach(function(d,C){const x=Oe(p,i,h,d);l[C]===void 0&&(l[C]=[]),l[C].push(x[0]),l[C].push(x[1])}),n++,E&&(n>4&&console.warn("THREE.FBXLoader: Polygons with more than four sides are not supported. Make sure to triangulate the geometry during export."),f.genFace(t,e,A,I,o,a,l,g,u,n),i++,n=0,A=[],o=[],a=[],l=[],g=[],u=[])}),t}genFace(e,t,i,n,s,A,o,a,l,g){for(let u=2;u<g;u++)e.vertex.push(t.vertexPositions[i[0]]),e.vertex.push(t.vertexPositions[i[1]]),e.vertex.push(t.vertexPositions[i[2]]),e.vertex.push(t.vertexPositions[i[(u-1)*3]]),e.vertex.push(t.vertexPositions[i[(u-1)*3+1]]),e.vertex.push(t.vertexPositions[i[(u-1)*3+2]]),e.vertex.push(t.vertexPositions[i[u*3]]),e.vertex.push(t.vertexPositions[i[u*3+1]]),e.vertex.push(t.vertexPositions[i[u*3+2]]),t.skeleton&&(e.vertexWeights.push(a[0]),e.vertexWeights.push(a[1]),e.vertexWeights.push(a[2]),e.vertexWeights.push(a[3]),e.vertexWeights.push(a[(u-1)*4]),e.vertexWeights.push(a[(u-1)*4+1]),e.vertexWeights.push(a[(u-1)*4+2]),e.vertexWeights.push(a[(u-1)*4+3]),e.vertexWeights.push(a[u*4]),e.vertexWeights.push(a[u*4+1]),e.vertexWeights.push(a[u*4+2]),e.vertexWeights.push(a[u*4+3]),e.weightsIndices.push(l[0]),e.weightsIndices.push(l[1]),e.weightsIndices.push(l[2]),e.weightsIndices.push(l[3]),e.weightsIndices.push(l[(u-1)*4]),e.weightsIndices.push(l[(u-1)*4+1]),e.weightsIndices.push(l[(u-1)*4+2]),e.weightsIndices.push(l[(u-1)*4+3]),e.weightsIndices.push(l[u*4]),e.weightsIndices.push(l[u*4+1]),e.weightsIndices.push(l[u*4+2]),e.weightsIndices.push(l[u*4+3])),t.color&&(e.colors.push(A[0]),e.colors.push(A[1]),e.colors.push(A[2]),e.colors.push(A[(u-1)*3]),e.colors.push(A[(u-1)*3+1]),e.colors.push(A[(u-1)*3+2]),e.colors.push(A[u*3]),e.colors.push(A[u*3+1]),e.colors.push(A[u*3+2])),t.material&&t.material.mappingType!=="AllSame"&&(e.materialIndex.push(n),e.materialIndex.push(n),e.materialIndex.push(n)),t.normal&&(e.normal.push(s[0]),e.normal.push(s[1]),e.normal.push(s[2]),e.normal.push(s[(u-1)*3]),e.normal.push(s[(u-1)*3+1]),e.normal.push(s[(u-1)*3+2]),e.normal.push(s[u*3]),e.normal.push(s[u*3+1]),e.normal.push(s[u*3+2])),t.uv&&t.uv.forEach(function(f,h){e.uvs[h]===void 0&&(e.uvs[h]=[]),e.uvs[h].push(o[h][0]),e.uvs[h].push(o[h][1]),e.uvs[h].push(o[h][(u-1)*2]),e.uvs[h].push(o[h][(u-1)*2+1]),e.uvs[h].push(o[h][u*2]),e.uvs[h].push(o[h][u*2+1])})}addMorphTargets(e,t,i,n){if(i.length===0)return;e.morphTargetsRelative=!0,e.morphAttributes.position=[];const s=this;i.forEach(function(A){A.rawTargets.forEach(function(o){const a=H.Objects.Geometry[o.geoID];a!==void 0&&s.genMorphGeometry(e,t,a,n,o.name)})})}genMorphGeometry(e,t,i,n,s){const A=t.PolygonVertexIndex!==void 0?t.PolygonVertexIndex.a:[],o=i.Vertices!==void 0?i.Vertices.a:[],a=i.Indexes!==void 0?i.Indexes.a:[],l=e.attributes.position.count*3,g=new Float32Array(l);for(let p=0;p<a.length;p++){const I=a[p]*3;g[I]=o[p*3],g[I+1]=o[p*3+1],g[I+2]=o[p*3+2]}const u={vertexIndices:A,vertexPositions:g},f=this.genBuffers(u),h=new r.Float32BufferAttribute(f.vertex,3);h.name=s||i.attrName,h.applyMatrix4(n),e.morphAttributes.position.push(h)}parseNormals(e){const t=e.MappingInformationType,i=e.ReferenceInformationType,n=e.Normals.a;let s=[];return i==="IndexToDirect"&&("NormalIndex"in e?s=e.NormalIndex.a:"NormalsIndex"in e&&(s=e.NormalsIndex.a)),{dataSize:3,buffer:n,indices:s,mappingType:t,referenceType:i}}parseUVs(e){const t=e.MappingInformationType,i=e.ReferenceInformationType,n=e.UV.a;let s=[];return i==="IndexToDirect"&&(s=e.UVIndex.a),{dataSize:2,buffer:n,indices:s,mappingType:t,referenceType:i}}parseVertexColors(e){const t=e.MappingInformationType,i=e.ReferenceInformationType,n=e.Colors.a;let s=[];i==="IndexToDirect"&&(s=e.ColorIndex.a);for(let A=0,o=new r.Color;A<n.length;A+=4)o.fromArray(n,A).convertSRGBToLinear().toArray(n,A);return{dataSize:4,buffer:n,indices:s,mappingType:t,referenceType:i}}parseMaterialIndices(e){const t=e.MappingInformationType,i=e.ReferenceInformationType;if(t==="NoMappingInformation")return{dataSize:1,buffer:[0],indices:[0],mappingType:"AllSame",referenceType:i};const n=e.Materials.a,s=[];for(let A=0;A<n.length;++A)s.push(A);return{dataSize:1,buffer:n,indices:s,mappingType:t,referenceType:i}}parseNurbsGeometry(e){const t=parseInt(e.Order);if(isNaN(t))return console.error("THREE.FBXLoader: Invalid Order %s given for geometry ID: %s",e.Order,e.id),new r.BufferGeometry;const i=t-1,n=e.KnotVector.a,s=[],A=e.Points.a;for(let u=0,f=A.length;u<f;u+=4)s.push(new r.Vector4().fromArray(A,u));let o,a;if(e.Form==="Closed")s.push(s[0]);else if(e.Form==="Periodic"){o=i,a=n.length-1-o;for(let u=0;u<i;++u)s.push(s[u])}const g=new $i(i,n,s,o,a).getPoints(s.length*12);return new r.BufferGeometry().setFromPoints(g)}}class is{parse(){const e=[],t=this.parseClips();if(t!==void 0)for(const i in t){const n=t[i],s=this.addClip(n);e.push(s)}return e}parseClips(){if(H.Objects.AnimationCurve===void 0)return;const e=this.parseAnimationCurveNodes();this.parseAnimationCurves(e);const t=this.parseAnimationLayers(e);return this.parseAnimStacks(t)}parseAnimationCurveNodes(){const e=H.Objects.AnimationCurveNode,t=new Map;for(const i in e){const n=e[i];if(n.attrName.match(/S|R|T|DeformPercent/)!==null){const s={id:n.id,attr:n.attrName,curves:{}};t.set(s.id,s)}}return t}parseAnimationCurves(e){const t=H.Objects.AnimationCurve;for(const i in t){const n={id:t[i].id,times:t[i].KeyTime.a.map(as),values:t[i].KeyValueFloat.a},s=$.get(n.id);if(s!==void 0){const A=s.parents[0].ID,o=s.parents[0].relationship;o.match(/X/)?e.get(A).curves.x=n:o.match(/Y/)?e.get(A).curves.y=n:o.match(/Z/)?e.get(A).curves.z=n:o.match(/DeformPercent/)&&e.has(A)&&(e.get(A).curves.morph=n)}}}parseAnimationLayers(e){const t=H.Objects.AnimationLayer,i=new Map;for(const n in t){const s=[],A=$.get(parseInt(n));A!==void 0&&(A.children.forEach(function(a,l){if(e.has(a.ID)){const g=e.get(a.ID);if(g.curves.x!==void 0||g.curves.y!==void 0||g.curves.z!==void 0){if(s[l]===void 0){const u=$.get(a.ID).parents.filter(function(f){return f.relationship!==void 0})[0].ID;if(u!==void 0){const f=H.Objects.Model[u.toString()];if(f===void 0){console.warn("THREE.FBXLoader: Encountered a unused curve.",a);return}const h={modelName:f.attrName?r.PropertyBinding.sanitizeNodeName(f.attrName):"",ID:f.id,initialPosition:[0,0,0],initialRotation:[0,0,0],initialScale:[1,1,1]};se.traverse(function(p){p.ID===f.id&&(h.transform=p.matrix,p.userData.transformData&&(h.eulerOrder=p.userData.transformData.eulerOrder))}),h.transform||(h.transform=new r.Matrix4),"PreRotation"in f&&(h.preRotation=f.PreRotation.value),"PostRotation"in f&&(h.postRotation=f.PostRotation.value),s[l]=h}}s[l]&&(s[l][g.attr]=g)}else if(g.curves.morph!==void 0){if(s[l]===void 0){const u=$.get(a.ID).parents.filter(function(B){return B.relationship!==void 0})[0].ID,f=$.get(u).parents[0].ID,h=$.get(f).parents[0].ID,p=$.get(h).parents[0].ID,I=H.Objects.Model[p],E={modelName:I.attrName?r.PropertyBinding.sanitizeNodeName(I.attrName):"",morphName:H.Objects.Deformer[u].attrName};s[l]=E}s[l][g.attr]=g}}}),i.set(parseInt(n),s))}return i}parseAnimStacks(e){const t=H.Objects.AnimationStack,i={};for(const n in t){const s=$.get(parseInt(n)).children;s.length>1&&console.warn("THREE.FBXLoader: Encountered an animation stack with multiple layers, this is currently not supported. Ignoring subsequent layers.");const A=e.get(s[0].ID);i[n]={name:t[n].attrName,layer:A}}return i}addClip(e){let t=[];const i=this;return e.layer.forEach(function(n){t=t.concat(i.generateTracks(n))}),new r.AnimationClip(e.name,-1,t)}generateTracks(e){const t=[];let i=new r.Vector3,n=new r.Quaternion,s=new r.Vector3;if(e.transform&&e.transform.decompose(i,n,s),i=i.toArray(),n=new r.Euler().setFromQuaternion(n,e.eulerOrder).toArray(),s=s.toArray(),e.T!==void 0&&Object.keys(e.T.curves).length>0){const A=this.generateVectorTrack(e.modelName,e.T.curves,i,"position");A!==void 0&&t.push(A)}if(e.R!==void 0&&Object.keys(e.R.curves).length>0){const A=this.generateRotationTrack(e.modelName,e.R.curves,n,e.preRotation,e.postRotation,e.eulerOrder);A!==void 0&&t.push(A)}if(e.S!==void 0&&Object.keys(e.S.curves).length>0){const A=this.generateVectorTrack(e.modelName,e.S.curves,s,"scale");A!==void 0&&t.push(A)}if(e.DeformPercent!==void 0){const A=this.generateMorphTrack(e);A!==void 0&&t.push(A)}return t}generateVectorTrack(e,t,i,n){const s=this.getTimesForAllAxes(t),A=this.getKeyframeTrackValues(s,t,i);return new r.VectorKeyframeTrack(e+"."+n,s,A)}generateRotationTrack(e,t,i,n,s,A){t.x!==void 0&&(this.interpolateRotations(t.x),t.x.values=t.x.values.map(r.MathUtils.degToRad)),t.y!==void 0&&(this.interpolateRotations(t.y),t.y.values=t.y.values.map(r.MathUtils.degToRad)),t.z!==void 0&&(this.interpolateRotations(t.z),t.z.values=t.z.values.map(r.MathUtils.degToRad));const o=this.getTimesForAllAxes(t),a=this.getKeyframeTrackValues(o,t,i);n!==void 0&&(n=n.map(r.MathUtils.degToRad),n.push(A),n=new r.Euler().fromArray(n),n=new r.Quaternion().setFromEuler(n)),s!==void 0&&(s=s.map(r.MathUtils.degToRad),s.push(A),s=new r.Euler().fromArray(s),s=new r.Quaternion().setFromEuler(s).invert());const l=new r.Quaternion,g=new r.Euler,u=[];for(let f=0;f<a.length;f+=3)g.set(a[f],a[f+1],a[f+2],A),l.setFromEuler(g),n!==void 0&&l.premultiply(n),s!==void 0&&l.multiply(s),l.toArray(u,f/3*4);return new r.QuaternionKeyframeTrack(e+".quaternion",o,u)}generateMorphTrack(e){const t=e.DeformPercent.curves.morph,i=t.values.map(function(s){return s/100}),n=se.getObjectByName(e.modelName).morphTargetDictionary[e.morphName];return new r.NumberKeyframeTrack(e.modelName+".morphTargetInfluences["+n+"]",t.times,i)}getTimesForAllAxes(e){let t=[];if(e.x!==void 0&&(t=t.concat(e.x.times)),e.y!==void 0&&(t=t.concat(e.y.times)),e.z!==void 0&&(t=t.concat(e.z.times)),t=t.sort(function(i,n){return i-n}),t.length>1){let i=1,n=t[0];for(let s=1;s<t.length;s++){const A=t[s];A!==n&&(t[i]=A,n=A,i++)}t=t.slice(0,i)}return t}getKeyframeTrackValues(e,t,i){const n=i,s=[];let A=-1,o=-1,a=-1;return e.forEach(function(l){if(t.x&&(A=t.x.times.indexOf(l)),t.y&&(o=t.y.times.indexOf(l)),t.z&&(a=t.z.times.indexOf(l)),A!==-1){const g=t.x.values[A];s.push(g),n[0]=g}else s.push(n[0]);if(o!==-1){const g=t.y.values[o];s.push(g),n[1]=g}else s.push(n[1]);if(a!==-1){const g=t.z.values[a];s.push(g),n[2]=g}else s.push(n[2])}),s}interpolateRotations(e){for(let t=1;t<e.values.length;t++){const i=e.values[t-1],n=e.values[t]-i,s=Math.abs(n);if(s>=180){const A=s/180,o=n/A;let a=i+o;const l=e.times[t-1],u=(e.times[t]-l)/A;let f=l+u;const h=[],p=[];for(;f<e.times[t];)h.push(f),f+=u,p.push(a),a+=o;e.times=cn(e.times,t,h),e.values=cn(e.values,t,p)}}}}class ss{getPrevNode(){return this.nodeStack[this.currentIndent-2]}getCurrentNode(){return this.nodeStack[this.currentIndent-1]}getCurrentProp(){return this.currentProp}pushStack(e){this.nodeStack.push(e),this.currentIndent+=1}popStack(){this.nodeStack.pop(),this.currentIndent-=1}setCurrentProp(e,t){this.currentProp=e,this.currentPropName=t}parse(e){this.currentIndent=0,this.allNodes=new sn,this.nodeStack=[],this.currentProp=[],this.currentPropName="";const t=this,i=e.split(/[\r\n]+/);return i.forEach(function(n,s){const A=n.match(/^[\s\t]*;/),o=n.match(/^[\s\t]*$/);if(A||o)return;const a=n.match("^\\t{"+t.currentIndent+"}(\\w+):(.*){",""),l=n.match("^\\t{"+t.currentIndent+"}(\\w+):[\\s\\t\\r\\n](.*)"),g=n.match("^\\t{"+(t.currentIndent-1)+"}}");a?t.parseNodeBegin(n,a):l?t.parseNodeProperty(n,l,i[++s]):g?t.popStack():n.match(/^[^\s\t}]/)&&t.parseNodePropertyContinued(n)}),this.allNodes}parseNodeBegin(e,t){const i=t[1].trim().replace(/^"/,"").replace(/"$/,""),n=t[2].split(",").map(function(a){return a.trim().replace(/^"/,"").replace(/"$/,"")}),s={name:i},A=this.parseNodeAttr(n),o=this.getCurrentNode();this.currentIndent===0?this.allNodes.add(i,s):i in o?(i==="PoseNode"?o.PoseNode.push(s):o[i].id!==void 0&&(o[i]={},o[i][o[i].id]=o[i]),A.id!==""&&(o[i][A.id]=s)):typeof A.id=="number"?(o[i]={},o[i][A.id]=s):i!=="Properties70"&&(i==="PoseNode"?o[i]=[s]:o[i]=s),typeof A.id=="number"&&(s.id=A.id),A.name!==""&&(s.attrName=A.name),A.type!==""&&(s.attrType=A.type),this.pushStack(s)}parseNodeAttr(e){let t=e[0];e[0]!==""&&(t=parseInt(e[0]),isNaN(t)&&(t=e[0]));let i="",n="";return e.length>1&&(i=e[1].replace(/^(\w+)::/,""),n=e[2]),{id:t,name:i,type:n}}parseNodeProperty(e,t,i){let n=t[1].replace(/^"/,"").replace(/"$/,"").trim(),s=t[2].replace(/^"/,"").replace(/"$/,"").trim();n==="Content"&&s===","&&(s=i.replace(/"/g,"").replace(/,$/,"").trim());const A=this.getCurrentNode();if(A.name==="Properties70"){this.parseNodeSpecialProperty(e,n,s);return}if(n==="C"){const a=s.split(",").slice(1),l=parseInt(a[0]),g=parseInt(a[1]);let u=s.split(",").slice(3);u=u.map(function(f){return f.trim().replace(/^"/,"")}),n="connections",s=[l,g],ls(s,u),A[n]===void 0&&(A[n]=[])}n==="Node"&&(A.id=s),n in A&&Array.isArray(A[n])?A[n].push(s):n!=="a"?A[n]=s:A.a=s,this.setCurrentProp(A,n),n==="a"&&s.slice(-1)!==","&&(A.a=It(s))}parseNodePropertyContinued(e){const t=this.getCurrentNode();t.a+=e,e.slice(-1)!==","&&(t.a=It(t.a))}parseNodeSpecialProperty(e,t,i){const n=i.split('",').map(function(g){return g.trim().replace(/^\"/,"").replace(/\s/,"_")}),s=n[0],A=n[1],o=n[2],a=n[3];let l=n[4];switch(A){case"int":case"enum":case"bool":case"ULongLong":case"double":case"Number":case"FieldOfView":l=parseFloat(l);break;case"Color":case"ColorRGB":case"Vector3D":case"Lcl_Translation":case"Lcl_Rotation":case"Lcl_Scaling":l=It(l);break}this.getPrevNode()[s]={type:A,type2:o,flag:a,value:l},this.setCurrentProp(this.getPrevNode(),s)}}class As{parse(e){const t=new nn(e);t.skip(23);const i=t.getUint32();if(i<6400)throw new Error("THREE.FBXLoader: FBX version not supported, FileVersion: "+i);const n=new sn;for(;!this.endOfContent(t);){const s=this.parseNode(t,i);s!==null&&n.add(s.name,s)}return n}endOfContent(e){return e.size()%16===0?(e.getOffset()+160+16&-16)>=e.size():e.getOffset()+160+16>=e.size()}parseNode(e,t){const i={},n=t>=7500?e.getUint64():e.getUint32(),s=t>=7500?e.getUint64():e.getUint32();t>=7500?e.getUint64():e.getUint32();const A=e.getUint8(),o=e.getString(A);if(n===0)return null;const a=[];for(let f=0;f<s;f++)a.push(this.parseProperty(e));const l=a.length>0?a[0]:"",g=a.length>1?a[1]:"",u=a.length>2?a[2]:"";for(i.singleProperty=s===1&&e.getOffset()===n;n>e.getOffset();){const f=this.parseNode(e,t);f!==null&&this.parseSubNode(o,i,f)}return i.propertyList=a,typeof l=="number"&&(i.id=l),g!==""&&(i.attrName=g),u!==""&&(i.attrType=u),o!==""&&(i.name=o),i}parseSubNode(e,t,i){if(i.singleProperty===!0){const n=i.propertyList[0];Array.isArray(n)?(t[i.name]=i,i.a=n):t[i.name]=n}else if(e==="Connections"&&i.name==="C"){const n=[];i.propertyList.forEach(function(s,A){A!==0&&n.push(s)}),t.connections===void 0&&(t.connections=[]),t.connections.push(n)}else if(i.name==="Properties70")Object.keys(i).forEach(function(s){t[s]=i[s]});else if(e==="Properties70"&&i.name==="P"){let n=i.propertyList[0],s=i.propertyList[1];const A=i.propertyList[2],o=i.propertyList[3];let a;n.indexOf("Lcl ")===0&&(n=n.replace("Lcl ","Lcl_")),s.indexOf("Lcl ")===0&&(s=s.replace("Lcl ","Lcl_")),s==="Color"||s==="ColorRGB"||s==="Vector"||s==="Vector3D"||s.indexOf("Lcl_")===0?a=[i.propertyList[4],i.propertyList[5],i.propertyList[6]]:a=i.propertyList[4],t[n]={type:s,type2:A,flag:o,value:a}}else t[i.name]===void 0?typeof i.id=="number"?(t[i.name]={},t[i.name][i.id]=i):t[i.name]=i:i.name==="PoseNode"?(Array.isArray(t[i.name])||(t[i.name]=[t[i.name]]),t[i.name].push(i)):t[i.name][i.id]===void 0&&(t[i.name][i.id]=i)}parseProperty(e){const t=e.getString(1);let i;switch(t){case"C":return e.getBoolean();case"D":return e.getFloat64();case"F":return e.getFloat32();case"I":return e.getInt32();case"L":return e.getInt64();case"R":return i=e.getUint32(),e.getArrayBuffer(i);case"S":return i=e.getUint32(),e.getString(i);case"Y":return e.getInt16();case"b":case"c":case"d":case"f":case"i":case"l":const n=e.getUint32(),s=e.getUint32(),A=e.getUint32();if(s===0)switch(t){case"b":case"c":return e.getBooleanArray(n);case"d":return e.getFloat64Array(n);case"f":return e.getFloat32Array(n);case"i":return e.getInt32Array(n);case"l":return e.getInt64Array(n)}const o=Vi(new Uint8Array(e.getArrayBuffer(A))),a=new nn(o.buffer);switch(t){case"b":case"c":return a.getBooleanArray(n);case"d":return a.getFloat64Array(n);case"f":return a.getFloat32Array(n);case"i":return a.getInt32Array(n);case"l":return a.getInt64Array(n)}break;default:throw new Error("THREE.FBXLoader: Unknown property type "+t)}}}class nn{constructor(e,t){this.dv=new DataView(e),this.offset=0,this.littleEndian=t!==void 0?t:!0,this._textDecoder=new TextDecoder}getOffset(){return this.offset}size(){return this.dv.buffer.byteLength}skip(e){this.offset+=e}getBoolean(){return(this.getUint8()&1)===1}getBooleanArray(e){const t=[];for(let i=0;i<e;i++)t.push(this.getBoolean());return t}getUint8(){const e=this.dv.getUint8(this.offset);return this.offset+=1,e}getInt16(){const e=this.dv.getInt16(this.offset,this.littleEndian);return this.offset+=2,e}getInt32(){const e=this.dv.getInt32(this.offset,this.littleEndian);return this.offset+=4,e}getInt32Array(e){const t=[];for(let i=0;i<e;i++)t.push(this.getInt32());return t}getUint32(){const e=this.dv.getUint32(this.offset,this.littleEndian);return this.offset+=4,e}getInt64(){let e,t;return this.littleEndian?(e=this.getUint32(),t=this.getUint32()):(t=this.getUint32(),e=this.getUint32()),t&2147483648?(t=~t&4294967295,e=~e&4294967295,e===4294967295&&(t=t+1&4294967295),e=e+1&4294967295,-(t*4294967296+e)):t*4294967296+e}getInt64Array(e){const t=[];for(let i=0;i<e;i++)t.push(this.getInt64());return t}getUint64(){let e,t;return this.littleEndian?(e=this.getUint32(),t=this.getUint32()):(t=this.getUint32(),e=this.getUint32()),t*4294967296+e}getFloat32(){const e=this.dv.getFloat32(this.offset,this.littleEndian);return this.offset+=4,e}getFloat32Array(e){const t=[];for(let i=0;i<e;i++)t.push(this.getFloat32());return t}getFloat64(){const e=this.dv.getFloat64(this.offset,this.littleEndian);return this.offset+=8,e}getFloat64Array(e){const t=[];for(let i=0;i<e;i++)t.push(this.getFloat64());return t}getArrayBuffer(e){const t=this.dv.buffer.slice(this.offset,this.offset+e);return this.offset+=e,t}getString(e){const t=this.offset;let i=new Uint8Array(this.dv.buffer,t,e);this.skip(e);const n=i.indexOf(0);return n>=0&&(i=new Uint8Array(this.dv.buffer,t,n)),this._textDecoder.decode(i)}}class sn{add(e,t){this[e]=t}}function os(c){const e="Kaydara FBX Binary  \0";return c.byteLength>=e.length&&e===an(c,0,e.length)}function rs(c){const e=["K","a","y","d","a","r","a","\\","F","B","X","\\","B","i","n","a","r","y","\\","\\"];let t=0;function i(n){const s=c[n-1];return c=c.slice(t+n),t++,s}for(let n=0;n<e.length;++n)if(i(1)===e[n])return!1;return!0}function An(c){const e=/FBXVersion: (\d+)/,t=c.match(e);if(t)return parseInt(t[1]);throw new Error("THREE.FBXLoader: Cannot find the version number for the file given.")}function as(c){return c/46186158e3}const cs=[];function Oe(c,e,t,i){let n;switch(i.mappingType){case"ByPolygonVertex":n=c;break;case"ByPolygon":n=e;break;case"ByVertice":n=t;break;case"AllSame":n=i.indices[0];break;default:console.warn("THREE.FBXLoader: unknown attribute mapping type "+i.mappingType)}i.referenceType==="IndexToDirect"&&(n=i.indices[n]);const s=n*i.dataSize,A=s+i.dataSize;return us(cs,i.buffer,s,A)}const pt=new r.Euler,Me=new r.Vector3;function on(c){const e=new r.Matrix4,t=new r.Matrix4,i=new r.Matrix4,n=new r.Matrix4,s=new r.Matrix4,A=new r.Matrix4,o=new r.Matrix4,a=new r.Matrix4,l=new r.Matrix4,g=new r.Matrix4,u=new r.Matrix4,f=new r.Matrix4,h=c.inheritType?c.inheritType:0;if(c.translation&&e.setPosition(Me.fromArray(c.translation)),c.preRotation){const M=c.preRotation.map(r.MathUtils.degToRad);M.push(c.eulerOrder||r.Euler.DEFAULT_ORDER),t.makeRotationFromEuler(pt.fromArray(M))}if(c.rotation){const M=c.rotation.map(r.MathUtils.degToRad);M.push(c.eulerOrder||r.Euler.DEFAULT_ORDER),i.makeRotationFromEuler(pt.fromArray(M))}if(c.postRotation){const M=c.postRotation.map(r.MathUtils.degToRad);M.push(c.eulerOrder||r.Euler.DEFAULT_ORDER),n.makeRotationFromEuler(pt.fromArray(M)),n.invert()}c.scale&&s.scale(Me.fromArray(c.scale)),c.scalingOffset&&o.setPosition(Me.fromArray(c.scalingOffset)),c.scalingPivot&&A.setPosition(Me.fromArray(c.scalingPivot)),c.rotationOffset&&a.setPosition(Me.fromArray(c.rotationOffset)),c.rotationPivot&&l.setPosition(Me.fromArray(c.rotationPivot)),c.parentMatrixWorld&&(u.copy(c.parentMatrix),g.copy(c.parentMatrixWorld));const p=t.clone().multiply(i).multiply(n),I=new r.Matrix4;I.extractRotation(g);const E=new r.Matrix4;E.copyPosition(g);const B=E.clone().invert().multiply(g),Q=I.clone().invert().multiply(B),d=s,C=new r.Matrix4;if(h===0)C.copy(I).multiply(p).multiply(Q).multiply(d);else if(h===1)C.copy(I).multiply(Q).multiply(p).multiply(d);else{const R=new r.Matrix4().scale(new r.Vector3().setFromMatrixScale(u)).clone().invert(),U=Q.clone().multiply(R);C.copy(I).multiply(p).multiply(U).multiply(d)}const x=l.clone().invert(),m=A.clone().invert();let S=e.clone().multiply(a).multiply(l).multiply(t).multiply(i).multiply(n).multiply(x).multiply(o).multiply(A).multiply(s).multiply(m);const w=new r.Matrix4().copyPosition(S),T=g.clone().multiply(w);return f.copyPosition(T),S=f.clone().multiply(C),S.premultiply(g.invert()),S}function rn(c){c=c||0;const e=["ZYX","YZX","XZY","ZXY","YXZ","XYZ"];return c===6?(console.warn("THREE.FBXLoader: unsupported Euler Order: Spherical XYZ. Animations and rotations may be incorrect."),e[0]):e[c]}function It(c){return c.split(",").map(function(t){return parseFloat(t)})}function an(c,e,t){return e===void 0&&(e=0),t===void 0&&(t=c.byteLength),new TextDecoder().decode(new Uint8Array(c,e,t))}function ls(c,e){for(let t=0,i=c.length,n=e.length;t<n;t++,i++)c[i]=e[t]}function us(c,e,t,i){for(let n=t,s=0;n<i;n++,s++)c[s]=e[n];return c}function cn(c,e,t){return c.slice(0,e).concat(t).concat(c.slice(e))}class gs extends r.DataTextureLoader{constructor(e){super(e)}parse(e){function t(y){switch(y.image_type){case f:case I:(y.colormap_length>256||y.colormap_size!==24||y.colormap_type!==1)&&console.error("THREE.TGALoader: Invalid type colormap data for indexed type.");break;case h:case p:case E:case B:y.colormap_type&&console.error("THREE.TGALoader: Invalid type colormap data for colormap type.");break;case u:console.error("THREE.TGALoader: No data.");default:console.error('THREE.TGALoader: Invalid type "%s".',y.image_type)}(y.width<=0||y.height<=0)&&console.error("THREE.TGALoader: Invalid image size."),y.pixel_size!==8&&y.pixel_size!==16&&y.pixel_size!==24&&y.pixel_size!==32&&console.error('THREE.TGALoader: Invalid pixel size "%s".',y.pixel_size)}function i(y,L,D,k,N){let Y,K;const G=D.pixel_size>>3,b=D.width*D.height*G;if(L&&(K=N.subarray(k,k+=D.colormap_length*(D.colormap_size>>3))),y){Y=new Uint8Array(b);let F,v,_,X=0;const de=new Uint8Array(G);for(;X<b;)if(F=N[k++],v=(F&127)+1,F&128){for(_=0;_<G;++_)de[_]=N[k++];for(_=0;_<v;++_)Y.set(de,X+_*G);X+=G*v}else{for(v*=G,_=0;_<v;++_)Y[X+_]=N[k++];X+=v}}else Y=N.subarray(k,k+=L?D.width*D.height:b);return{pixel_data:Y,palettes:K}}function n(y,L,D,k,N,Y,K,G,b){const F=b;let v,_=0,X,de;const Xe=M.width;for(de=L;de!==k;de+=D)for(X=N;X!==K;X+=Y,_++)v=G[_],y[(X+Xe*de)*4+3]=255,y[(X+Xe*de)*4+2]=F[v*3+0],y[(X+Xe*de)*4+1]=F[v*3+1],y[(X+Xe*de)*4+0]=F[v*3+2];return y}function s(y,L,D,k,N,Y,K,G){let b,F=0,v,_;const X=M.width;for(_=L;_!==k;_+=D)for(v=N;v!==K;v+=Y,F+=2)b=G[F+0]+(G[F+1]<<8),y[(v+X*_)*4+0]=(b&31744)>>7,y[(v+X*_)*4+1]=(b&992)>>2,y[(v+X*_)*4+2]=(b&31)<<3,y[(v+X*_)*4+3]=b&32768?0:255;return y}function A(y,L,D,k,N,Y,K,G){let b=0,F,v;const _=M.width;for(v=L;v!==k;v+=D)for(F=N;F!==K;F+=Y,b+=3)y[(F+_*v)*4+3]=255,y[(F+_*v)*4+2]=G[b+0],y[(F+_*v)*4+1]=G[b+1],y[(F+_*v)*4+0]=G[b+2];return y}function o(y,L,D,k,N,Y,K,G){let b=0,F,v;const _=M.width;for(v=L;v!==k;v+=D)for(F=N;F!==K;F+=Y,b+=4)y[(F+_*v)*4+2]=G[b+0],y[(F+_*v)*4+1]=G[b+1],y[(F+_*v)*4+0]=G[b+2],y[(F+_*v)*4+3]=G[b+3];return y}function a(y,L,D,k,N,Y,K,G){let b,F=0,v,_;const X=M.width;for(_=L;_!==k;_+=D)for(v=N;v!==K;v+=Y,F++)b=G[F],y[(v+X*_)*4+0]=b,y[(v+X*_)*4+1]=b,y[(v+X*_)*4+2]=b,y[(v+X*_)*4+3]=255;return y}function l(y,L,D,k,N,Y,K,G){let b=0,F,v;const _=M.width;for(v=L;v!==k;v+=D)for(F=N;F!==K;F+=Y,b+=2)y[(F+_*v)*4+0]=G[b+0],y[(F+_*v)*4+1]=G[b+0],y[(F+_*v)*4+2]=G[b+0],y[(F+_*v)*4+3]=G[b+1];return y}function g(y,L,D,k,N){let Y,K,G,b,F,v;switch((M.flags&Q)>>d){default:case m:Y=0,G=1,F=L,K=0,b=1,v=D;break;case C:Y=0,G=1,F=L,K=D-1,b=-1,v=-1;break;case S:Y=L-1,G=-1,F=-1,K=0,b=1,v=D;break;case x:Y=L-1,G=-1,F=-1,K=D-1,b=-1,v=-1;break}if(V)switch(M.pixel_size){case 8:a(y,K,b,v,Y,G,F,k);break;case 16:l(y,K,b,v,Y,G,F,k);break;default:console.error("THREE.TGALoader: Format not supported.");break}else switch(M.pixel_size){case 8:n(y,K,b,v,Y,G,F,k,N);break;case 16:s(y,K,b,v,Y,G,F,k);break;case 24:A(y,K,b,v,Y,G,F,k);break;case 32:o(y,K,b,v,Y,G,F,k);break;default:console.error("THREE.TGALoader: Format not supported.");break}return y}const u=0,f=1,h=2,p=3,I=9,E=10,B=11,Q=48,d=4,C=0,x=1,m=2,S=3;e.length<19&&console.error("THREE.TGALoader: Not enough data to contain header.");let w=0;const T=new Uint8Array(e),M={id_length:T[w++],colormap_type:T[w++],image_type:T[w++],colormap_index:T[w++]|T[w++]<<8,colormap_length:T[w++]|T[w++]<<8,colormap_size:T[w++],origin:[T[w++]|T[w++]<<8,T[w++]|T[w++]<<8],width:T[w++]|T[w++]<<8,height:T[w++]|T[w++]<<8,pixel_size:T[w++],flags:T[w++]};t(M),M.id_length+w>e.length&&console.error("THREE.TGALoader: No data."),w+=M.id_length;let R=!1,U=!1,V=!1;switch(M.image_type){case I:R=!0,U=!0;break;case f:U=!0;break;case E:R=!0;break;case h:break;case B:R=!0,V=!0;break;case p:V=!0;break}const O=new Uint8Array(M.width*M.height*4),P=i(R,U,M,w,T);return g(O,M.width,M.height,P.pixel_data,P.palettes),{data:O,width:M.width,height:M.height,flipY:!0,generateMipmaps:!0,minFilter:r.LinearMipmapLinearFilter}}}const ln=new r.Box3,He=new r.Vector3;class un extends r.InstancedBufferGeometry{constructor(){super(),this.isLineSegmentsGeometry=!0,this.type="LineSegmentsGeometry";const e=[-1,2,0,1,2,0,-1,1,0,1,1,0,-1,0,0,1,0,0,-1,-1,0,1,-1,0],t=[-1,2,1,2,-1,1,1,1,-1,-1,1,-1,-1,-2,1,-2],i=[0,2,1,2,3,1,2,4,3,4,5,3,4,6,5,6,7,5];this.setIndex(i),this.setAttribute("position",new r.Float32BufferAttribute(e,3)),this.setAttribute("uv",new r.Float32BufferAttribute(t,2))}applyMatrix4(e){const t=this.attributes.instanceStart,i=this.attributes.instanceEnd;return t!==void 0&&(t.applyMatrix4(e),i.applyMatrix4(e),t.needsUpdate=!0),this.boundingBox!==null&&this.computeBoundingBox(),this.boundingSphere!==null&&this.computeBoundingSphere(),this}setPositions(e){let t;e instanceof Float32Array?t=e:Array.isArray(e)&&(t=new Float32Array(e));const i=new r.InstancedInterleavedBuffer(t,6,1);return this.setAttribute("instanceStart",new r.InterleavedBufferAttribute(i,3,0)),this.setAttribute("instanceEnd",new r.InterleavedBufferAttribute(i,3,3)),this.computeBoundingBox(),this.computeBoundingSphere(),this}setColors(e){let t;e instanceof Float32Array?t=e:Array.isArray(e)&&(t=new Float32Array(e));const i=new r.InstancedInterleavedBuffer(t,6,1);return this.setAttribute("instanceColorStart",new r.InterleavedBufferAttribute(i,3,0)),this.setAttribute("instanceColorEnd",new r.InterleavedBufferAttribute(i,3,3)),this}fromWireframeGeometry(e){return this.setPositions(e.attributes.position.array),this}fromEdgesGeometry(e){return this.setPositions(e.attributes.position.array),this}fromMesh(e){return this.fromWireframeGeometry(new r.WireframeGeometry(e.geometry)),this}fromLineSegments(e){const t=e.geometry;return this.setPositions(t.attributes.position.array),this}computeBoundingBox(){this.boundingBox===null&&(this.boundingBox=new r.Box3);const e=this.attributes.instanceStart,t=this.attributes.instanceEnd;e!==void 0&&t!==void 0&&(this.boundingBox.setFromBufferAttribute(e),ln.setFromBufferAttribute(t),this.boundingBox.union(ln))}computeBoundingSphere(){this.boundingSphere===null&&(this.boundingSphere=new r.Sphere),this.boundingBox===null&&this.computeBoundingBox();const e=this.attributes.instanceStart,t=this.attributes.instanceEnd;if(e!==void 0&&t!==void 0){const i=this.boundingSphere.center;this.boundingBox.getCenter(i);let n=0;for(let s=0,A=e.count;s<A;s++)He.fromBufferAttribute(e,s),n=Math.max(n,i.distanceToSquared(He)),He.fromBufferAttribute(t,s),n=Math.max(n,i.distanceToSquared(He));this.boundingSphere.radius=Math.sqrt(n),isNaN(this.boundingSphere.radius)&&console.error("THREE.LineSegmentsGeometry.computeBoundingSphere(): Computed radius is NaN. The instanced position data is likely to have NaN values.",this)}}toJSON(){}applyMatrix(e){return console.warn("THREE.LineSegmentsGeometry: applyMatrix() has been renamed to applyMatrix4()."),this.applyMatrix4(e)}}r.UniformsLib.line={worldUnits:{value:1},linewidth:{value:1},resolution:{value:new r.Vector2(1,1)},dashOffset:{value:0},dashScale:{value:1},dashSize:{value:1},gapSize:{value:1}},r.ShaderLib.line={uniforms:r.UniformsUtils.merge([r.UniformsLib.common,r.UniformsLib.fog,r.UniformsLib.line]),vertexShader:`
		#include <common>
		#include <color_pars_vertex>
		#include <fog_pars_vertex>
		#include <logdepthbuf_pars_vertex>
		#include <clipping_planes_pars_vertex>

		uniform float linewidth;
		uniform vec2 resolution;

		attribute vec3 instanceStart;
		attribute vec3 instanceEnd;

		attribute vec3 instanceColorStart;
		attribute vec3 instanceColorEnd;

		#ifdef WORLD_UNITS

			varying vec4 worldPos;
			varying vec3 worldStart;
			varying vec3 worldEnd;

			#ifdef USE_DASH

				varying vec2 vUv;

			#endif

		#else

			varying vec2 vUv;

		#endif

		#ifdef USE_DASH

			uniform float dashScale;
			attribute float instanceDistanceStart;
			attribute float instanceDistanceEnd;
			varying float vLineDistance;

		#endif

		void trimSegment( const in vec4 start, inout vec4 end ) {

			// trim end segment so it terminates between the camera plane and the near plane

			// conservative estimate of the near plane
			float a = projectionMatrix[ 2 ][ 2 ]; // 3nd entry in 3th column
			float b = projectionMatrix[ 3 ][ 2 ]; // 3nd entry in 4th column
			float nearEstimate = - 0.5 * b / a;

			float alpha = ( nearEstimate - start.z ) / ( end.z - start.z );

			end.xyz = mix( start.xyz, end.xyz, alpha );

		}

		void main() {

			#ifdef USE_COLOR

				vColor.xyz = ( position.y < 0.5 ) ? instanceColorStart : instanceColorEnd;

			#endif

			#ifdef USE_DASH

				vLineDistance = ( position.y < 0.5 ) ? dashScale * instanceDistanceStart : dashScale * instanceDistanceEnd;
				vUv = uv;

			#endif

			float aspect = resolution.x / resolution.y;

			// camera space
			vec4 start = modelViewMatrix * vec4( instanceStart, 1.0 );
			vec4 end = modelViewMatrix * vec4( instanceEnd, 1.0 );

			#ifdef WORLD_UNITS

				worldStart = start.xyz;
				worldEnd = end.xyz;

			#else

				vUv = uv;

			#endif

			// special case for perspective projection, and segments that terminate either in, or behind, the camera plane
			// clearly the gpu firmware has a way of addressing this issue when projecting into ndc space
			// but we need to perform ndc-space calculations in the shader, so we must address this issue directly
			// perhaps there is a more elegant solution -- WestLangley

			bool perspective = ( projectionMatrix[ 2 ][ 3 ] == - 1.0 ); // 4th entry in the 3rd column

			if ( perspective ) {

				if ( start.z < 0.0 && end.z >= 0.0 ) {

					trimSegment( start, end );

				} else if ( end.z < 0.0 && start.z >= 0.0 ) {

					trimSegment( end, start );

				}

			}

			// clip space
			vec4 clipStart = projectionMatrix * start;
			vec4 clipEnd = projectionMatrix * end;

			// ndc space
			vec3 ndcStart = clipStart.xyz / clipStart.w;
			vec3 ndcEnd = clipEnd.xyz / clipEnd.w;

			// direction
			vec2 dir = ndcEnd.xy - ndcStart.xy;

			// account for clip-space aspect ratio
			dir.x *= aspect;
			dir = normalize( dir );

			#ifdef WORLD_UNITS

				// get the offset direction as perpendicular to the view vector
				vec3 worldDir = normalize( end.xyz - start.xyz );
				vec3 offset;
				if ( position.y < 0.5 ) {

					offset = normalize( cross( start.xyz, worldDir ) );

				} else {

					offset = normalize( cross( end.xyz, worldDir ) );

				}

				// sign flip
				if ( position.x < 0.0 ) offset *= - 1.0;

				float forwardOffset = dot( worldDir, vec3( 0.0, 0.0, 1.0 ) );

				// don't extend the line if we're rendering dashes because we
				// won't be rendering the endcaps
				#ifndef USE_DASH

					// extend the line bounds to encompass  endcaps
					start.xyz += - worldDir * linewidth * 0.5;
					end.xyz += worldDir * linewidth * 0.5;

					// shift the position of the quad so it hugs the forward edge of the line
					offset.xy -= dir * forwardOffset;
					offset.z += 0.5;

				#endif

				// endcaps
				if ( position.y > 1.0 || position.y < 0.0 ) {

					offset.xy += dir * 2.0 * forwardOffset;

				}

				// adjust for linewidth
				offset *= linewidth * 0.5;

				// set the world position
				worldPos = ( position.y < 0.5 ) ? start : end;
				worldPos.xyz += offset;

				// project the worldpos
				vec4 clip = projectionMatrix * worldPos;

				// shift the depth of the projected points so the line
				// segments overlap neatly
				vec3 clipPose = ( position.y < 0.5 ) ? ndcStart : ndcEnd;
				clip.z = clipPose.z * clip.w;

			#else

				vec2 offset = vec2( dir.y, - dir.x );
				// undo aspect ratio adjustment
				dir.x /= aspect;
				offset.x /= aspect;

				// sign flip
				if ( position.x < 0.0 ) offset *= - 1.0;

				// endcaps
				if ( position.y < 0.0 ) {

					offset += - dir;

				} else if ( position.y > 1.0 ) {

					offset += dir;

				}

				// adjust for linewidth
				offset *= linewidth;

				// adjust for clip-space to screen-space conversion // maybe resolution should be based on viewport ...
				offset /= resolution.y;

				// select end
				vec4 clip = ( position.y < 0.5 ) ? clipStart : clipEnd;

				// back to clip space
				offset *= clip.w;

				clip.xy += offset;

			#endif

			gl_Position = clip;

			vec4 mvPosition = ( position.y < 0.5 ) ? start : end; // this is an approximation

			#include <logdepthbuf_vertex>
			#include <clipping_planes_vertex>
			#include <fog_vertex>

		}
		`,fragmentShader:`
		uniform vec3 diffuse;
		uniform float opacity;
		uniform float linewidth;

		#ifdef USE_DASH

			uniform float dashOffset;
			uniform float dashSize;
			uniform float gapSize;

		#endif

		varying float vLineDistance;

		#ifdef WORLD_UNITS

			varying vec4 worldPos;
			varying vec3 worldStart;
			varying vec3 worldEnd;

			#ifdef USE_DASH

				varying vec2 vUv;

			#endif

		#else

			varying vec2 vUv;

		#endif

		#include <common>
		#include <color_pars_fragment>
		#include <fog_pars_fragment>
		#include <logdepthbuf_pars_fragment>
		#include <clipping_planes_pars_fragment>

		vec2 closestLineToLine(vec3 p1, vec3 p2, vec3 p3, vec3 p4) {

			float mua;
			float mub;

			vec3 p13 = p1 - p3;
			vec3 p43 = p4 - p3;

			vec3 p21 = p2 - p1;

			float d1343 = dot( p13, p43 );
			float d4321 = dot( p43, p21 );
			float d1321 = dot( p13, p21 );
			float d4343 = dot( p43, p43 );
			float d2121 = dot( p21, p21 );

			float denom = d2121 * d4343 - d4321 * d4321;

			float numer = d1343 * d4321 - d1321 * d4343;

			mua = numer / denom;
			mua = clamp( mua, 0.0, 1.0 );
			mub = ( d1343 + d4321 * ( mua ) ) / d4343;
			mub = clamp( mub, 0.0, 1.0 );

			return vec2( mua, mub );

		}

		void main() {

			#include <clipping_planes_fragment>

			#ifdef USE_DASH

				if ( vUv.y < - 1.0 || vUv.y > 1.0 ) discard; // discard endcaps

				if ( mod( vLineDistance + dashOffset, dashSize + gapSize ) > dashSize ) discard; // todo - FIX

			#endif

			float alpha = opacity;

			#ifdef WORLD_UNITS

				// Find the closest points on the view ray and the line segment
				vec3 rayEnd = normalize( worldPos.xyz ) * 1e5;
				vec3 lineDir = worldEnd - worldStart;
				vec2 params = closestLineToLine( worldStart, worldEnd, vec3( 0.0, 0.0, 0.0 ), rayEnd );

				vec3 p1 = worldStart + lineDir * params.x;
				vec3 p2 = rayEnd * params.y;
				vec3 delta = p1 - p2;
				float len = length( delta );
				float norm = len / linewidth;

				#ifndef USE_DASH

					#ifdef USE_ALPHA_TO_COVERAGE

						float dnorm = fwidth( norm );
						alpha = 1.0 - smoothstep( 0.5 - dnorm, 0.5 + dnorm, norm );

					#else

						if ( norm > 0.5 ) {

							discard;

						}

					#endif

				#endif

			#else

				#ifdef USE_ALPHA_TO_COVERAGE

					// artifacts appear on some hardware if a derivative is taken within a conditional
					float a = vUv.x;
					float b = ( vUv.y > 0.0 ) ? vUv.y - 1.0 : vUv.y + 1.0;
					float len2 = a * a + b * b;
					float dlen = fwidth( len2 );

					if ( abs( vUv.y ) > 1.0 ) {

						alpha = 1.0 - smoothstep( 1.0 - dlen, 1.0 + dlen, len2 );

					}

				#else

					if ( abs( vUv.y ) > 1.0 ) {

						float a = vUv.x;
						float b = ( vUv.y > 0.0 ) ? vUv.y - 1.0 : vUv.y + 1.0;
						float len2 = a * a + b * b;

						if ( len2 > 1.0 ) discard;

					}

				#endif

			#endif

			vec4 diffuseColor = vec4( diffuse, alpha );

			#include <logdepthbuf_fragment>
			#include <color_fragment>

			gl_FragColor = vec4( diffuseColor.rgb, alpha );

			#include <tonemapping_fragment>
			#include <encodings_fragment>
			#include <fog_fragment>
			#include <premultiplied_alpha_fragment>

		}
		`};class Et extends r.ShaderMaterial{constructor(e){super({type:"LineMaterial",uniforms:r.UniformsUtils.clone(r.ShaderLib.line.uniforms),vertexShader:r.ShaderLib.line.vertexShader,fragmentShader:r.ShaderLib.line.fragmentShader,clipping:!0}),this.isLineMaterial=!0,Object.defineProperties(this,{color:{enumerable:!0,get:function(){return this.uniforms.diffuse.value},set:function(t){this.uniforms.diffuse.value=t}},worldUnits:{enumerable:!0,get:function(){return"WORLD_UNITS"in this.defines},set:function(t){t===!0?this.defines.WORLD_UNITS="":delete this.defines.WORLD_UNITS}},linewidth:{enumerable:!0,get:function(){return this.uniforms.linewidth.value},set:function(t){this.uniforms.linewidth.value=t}},dashed:{enumerable:!0,get:function(){return"USE_DASH"in this.defines},set(t){!!t!="USE_DASH"in this.defines&&(this.needsUpdate=!0),t===!0?this.defines.USE_DASH="":delete this.defines.USE_DASH}},dashScale:{enumerable:!0,get:function(){return this.uniforms.dashScale.value},set:function(t){this.uniforms.dashScale.value=t}},dashSize:{enumerable:!0,get:function(){return this.uniforms.dashSize.value},set:function(t){this.uniforms.dashSize.value=t}},dashOffset:{enumerable:!0,get:function(){return this.uniforms.dashOffset.value},set:function(t){this.uniforms.dashOffset.value=t}},gapSize:{enumerable:!0,get:function(){return this.uniforms.gapSize.value},set:function(t){this.uniforms.gapSize.value=t}},opacity:{enumerable:!0,get:function(){return this.uniforms.opacity.value},set:function(t){this.uniforms.opacity.value=t}},resolution:{enumerable:!0,get:function(){return this.uniforms.resolution.value},set:function(t){this.uniforms.resolution.value.copy(t)}},alphaToCoverage:{enumerable:!0,get:function(){return"USE_ALPHA_TO_COVERAGE"in this.defines},set:function(t){!!t!="USE_ALPHA_TO_COVERAGE"in this.defines&&(this.needsUpdate=!0),t===!0?(this.defines.USE_ALPHA_TO_COVERAGE="",this.extensions.derivatives=!0):(delete this.defines.USE_ALPHA_TO_COVERAGE,this.extensions.derivatives=!1)}}}),this.setValues(e)}}const gn=new r.Vector3,fn=new r.Vector3,ee=new r.Vector4,te=new r.Vector4,ue=new r.Vector4,Bt=new r.Vector3,Ct=new r.Matrix4,ne=new r.Line3,hn=new r.Vector3,Ve=new r.Box3,qe=new r.Sphere,ge=new r.Vector4;let fe,ye;function dn(c,e,t){return ge.set(0,0,-e,1).applyMatrix4(c.projectionMatrix),ge.multiplyScalar(1/ge.w),ge.x=ye/t.width,ge.y=ye/t.height,ge.applyMatrix4(c.projectionMatrixInverse),ge.multiplyScalar(1/ge.w),Math.abs(Math.max(ge.x,ge.y))}function fs(c,e){const t=c.matrixWorld,i=c.geometry,n=i.attributes.instanceStart,s=i.attributes.instanceEnd,A=Math.min(i.instanceCount,n.count);for(let o=0,a=A;o<a;o++){ne.start.fromBufferAttribute(n,o),ne.end.fromBufferAttribute(s,o),ne.applyMatrix4(t);const l=new r.Vector3,g=new r.Vector3;fe.distanceSqToSegment(ne.start,ne.end,g,l),g.distanceTo(l)<ye*.5&&e.push({point:g,pointOnLine:l,distance:fe.origin.distanceTo(g),object:c,face:null,faceIndex:o,uv:null,uv1:null})}}function hs(c,e,t){const i=e.projectionMatrix,s=c.material.resolution,A=c.matrixWorld,o=c.geometry,a=o.attributes.instanceStart,l=o.attributes.instanceEnd,g=Math.min(o.instanceCount,a.count),u=-e.near;fe.at(1,ue),ue.w=1,ue.applyMatrix4(e.matrixWorldInverse),ue.applyMatrix4(i),ue.multiplyScalar(1/ue.w),ue.x*=s.x/2,ue.y*=s.y/2,ue.z=0,Bt.copy(ue),Ct.multiplyMatrices(e.matrixWorldInverse,A);for(let f=0,h=g;f<h;f++){if(ee.fromBufferAttribute(a,f),te.fromBufferAttribute(l,f),ee.w=1,te.w=1,ee.applyMatrix4(Ct),te.applyMatrix4(Ct),ee.z>u&&te.z>u)continue;if(ee.z>u){const d=ee.z-te.z,C=(ee.z-u)/d;ee.lerp(te,C)}else if(te.z>u){const d=te.z-ee.z,C=(te.z-u)/d;te.lerp(ee,C)}ee.applyMatrix4(i),te.applyMatrix4(i),ee.multiplyScalar(1/ee.w),te.multiplyScalar(1/te.w),ee.x*=s.x/2,ee.y*=s.y/2,te.x*=s.x/2,te.y*=s.y/2,ne.start.copy(ee),ne.start.z=0,ne.end.copy(te),ne.end.z=0;const I=ne.closestPointToPointParameter(Bt,!0);ne.at(I,hn);const E=r.MathUtils.lerp(ee.z,te.z,I),B=E>=-1&&E<=1,Q=Bt.distanceTo(hn)<ye*.5;if(B&&Q){ne.start.fromBufferAttribute(a,f),ne.end.fromBufferAttribute(l,f),ne.start.applyMatrix4(A),ne.end.applyMatrix4(A);const d=new r.Vector3,C=new r.Vector3;fe.distanceSqToSegment(ne.start,ne.end,C,d),t.push({point:C,pointOnLine:d,distance:fe.origin.distanceTo(C),object:c,face:null,faceIndex:f,uv:null,uv1:null})}}}class ds extends r.Mesh{constructor(e=new un,t=new Et({color:Math.random()*16777215})){super(e,t),this.isLineSegments2=!0,this.type="LineSegments2"}computeLineDistances(){const e=this.geometry,t=e.attributes.instanceStart,i=e.attributes.instanceEnd,n=new Float32Array(2*t.count);for(let A=0,o=0,a=t.count;A<a;A++,o+=2)gn.fromBufferAttribute(t,A),fn.fromBufferAttribute(i,A),n[o]=o===0?0:n[o-1],n[o+1]=n[o]+gn.distanceTo(fn);const s=new r.InstancedInterleavedBuffer(n,2,1);return e.setAttribute("instanceDistanceStart",new r.InterleavedBufferAttribute(s,1,0)),e.setAttribute("instanceDistanceEnd",new r.InterleavedBufferAttribute(s,1,1)),this}raycast(e,t){const i=this.material.worldUnits,n=e.camera;n===null&&!i&&console.error('LineSegments2: "Raycaster.camera" needs to be set in order to raycast against LineSegments2 while worldUnits is set to false.');const s=e.params.Line2!==void 0&&e.params.Line2.threshold||0;fe=e.ray;const A=this.matrixWorld,o=this.geometry,a=this.material;ye=a.linewidth+s,o.boundingSphere===null&&o.computeBoundingSphere(),qe.copy(o.boundingSphere).applyMatrix4(A);let l;if(i)l=ye*.5;else{const u=Math.max(n.near,qe.distanceToPoint(fe.origin));l=dn(n,u,a.resolution)}if(qe.radius+=l,fe.intersectsSphere(qe)===!1)return;o.boundingBox===null&&o.computeBoundingBox(),Ve.copy(o.boundingBox).applyMatrix4(A);let g;if(i)g=ye*.5;else{const u=Math.max(n.near,Ve.distanceToPoint(fe.origin));g=dn(n,u,a.resolution)}Ve.expandByScalar(g),fe.intersectsBox(Ve)!==!1&&(i?fs(this,t):hs(this,n,t))}}class pn extends un{constructor(){super(),this.isLineGeometry=!0,this.type="LineGeometry"}setPositions(e){const t=e.length-3,i=new Float32Array(2*t);for(let n=0;n<t;n+=3)i[2*n]=e[n],i[2*n+1]=e[n+1],i[2*n+2]=e[n+2],i[2*n+3]=e[n+3],i[2*n+4]=e[n+4],i[2*n+5]=e[n+5];return super.setPositions(i),this}setColors(e){const t=e.length-3,i=new Float32Array(2*t);for(let n=0;n<t;n+=3)i[2*n]=e[n],i[2*n+1]=e[n+1],i[2*n+2]=e[n+2],i[2*n+3]=e[n+3],i[2*n+4]=e[n+4],i[2*n+5]=e[n+5];return super.setColors(i),this}fromLine(e){const t=e.geometry;return this.setPositions(t.attributes.position.array),this}}class ps extends ds{constructor(e=new pn,t=new Et({color:Math.random()*16777215})){super(e,t),this.isLine2=!0,this.type="Line2"}}const In={POSITION:["byte","byte normalized","unsigned byte","unsigned byte normalized","short","short normalized","unsigned short","unsigned short normalized"],NORMAL:["byte normalized","short normalized"],TANGENT:["byte normalized","short normalized"],TEXCOORD:["byte","byte normalized","unsigned byte","short","short normalized","unsigned short"]};class mt{constructor(){this.pluginCallbacks=[],this.register(function(e){return new Ts(e)}),this.register(function(e){return new Ss(e)}),this.register(function(e){return new Rs(e)}),this.register(function(e){return new Ds(e)}),this.register(function(e){return new Ls(e)}),this.register(function(e){return new Fs(e)}),this.register(function(e){return new Ms(e)}),this.register(function(e){return new vs(e)}),this.register(function(e){return new bs(e)}),this.register(function(e){return new _s(e)})}register(e){return this.pluginCallbacks.indexOf(e)===-1&&this.pluginCallbacks.push(e),this}unregister(e){return this.pluginCallbacks.indexOf(e)!==-1&&this.pluginCallbacks.splice(this.pluginCallbacks.indexOf(e),1),this}parse(e,t,i,n){const s=new xs,A=[];for(let o=0,a=this.pluginCallbacks.length;o<a;o++)A.push(this.pluginCallbacks[o](s));s.setPlugins(A),s.write(e,t,n).catch(i)}parseAsync(e,t){const i=this;return new Promise(function(n,s){i.parse(e,n,s,t)})}}const q={POINTS:0,LINES:1,LINE_LOOP:2,LINE_STRIP:3,TRIANGLES:4,TRIANGLE_STRIP:5,TRIANGLE_FAN:6,BYTE:5120,UNSIGNED_BYTE:5121,SHORT:5122,UNSIGNED_SHORT:5123,INT:5124,UNSIGNED_INT:5125,FLOAT:5126,ARRAY_BUFFER:34962,ELEMENT_ARRAY_BUFFER:34963,NEAREST:9728,LINEAR:9729,NEAREST_MIPMAP_NEAREST:9984,LINEAR_MIPMAP_NEAREST:9985,NEAREST_MIPMAP_LINEAR:9986,LINEAR_MIPMAP_LINEAR:9987,CLAMP_TO_EDGE:33071,MIRRORED_REPEAT:33648,REPEAT:10497},Qt="KHR_mesh_quantization",oe={};oe[r.NearestFilter]=q.NEAREST,oe[r.NearestMipmapNearestFilter]=q.NEAREST_MIPMAP_NEAREST,oe[r.NearestMipmapLinearFilter]=q.NEAREST_MIPMAP_LINEAR,oe[r.LinearFilter]=q.LINEAR,oe[r.LinearMipmapNearestFilter]=q.LINEAR_MIPMAP_NEAREST,oe[r.LinearMipmapLinearFilter]=q.LINEAR_MIPMAP_LINEAR,oe[r.ClampToEdgeWrapping]=q.CLAMP_TO_EDGE,oe[r.RepeatWrapping]=q.REPEAT,oe[r.MirroredRepeatWrapping]=q.MIRRORED_REPEAT;const En={scale:"scale",position:"translation",quaternion:"rotation",morphTargetInfluences:"weights"},Is=new r.Color,Bn=12,Es=1179937895,Bs=2,Cn=8,Cs=1313821514,ms=5130562;function _e(c,e){return c.length===e.length&&c.every(function(t,i){return t===e[i]})}function Qs(c){return new TextEncoder().encode(c).buffer}function ys(c){return _e(c.elements,[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1])}function ws(c,e,t){const i={min:new Array(c.itemSize).fill(Number.POSITIVE_INFINITY),max:new Array(c.itemSize).fill(Number.NEGATIVE_INFINITY)};for(let n=e;n<e+t;n++)for(let s=0;s<c.itemSize;s++){let A;c.itemSize>4?A=c.array[n*c.itemSize+s]:(s===0?A=c.getX(n):s===1?A=c.getY(n):s===2?A=c.getZ(n):s===3&&(A=c.getW(n)),c.normalized===!0&&(A=r.MathUtils.normalize(A,c.array))),i.min[s]=Math.min(i.min[s],A),i.max[s]=Math.max(i.max[s],A)}return i}function mn(c){return Math.ceil(c/4)*4}function yt(c,e=0){const t=mn(c.byteLength);if(t!==c.byteLength){const i=new Uint8Array(t);if(i.set(new Uint8Array(c)),e!==0)for(let n=c.byteLength;n<t;n++)i[n]=e;return i.buffer}return c}function Qn(){return typeof document>"u"&&typeof OffscreenCanvas<"u"?new OffscreenCanvas(1,1):document.createElement("canvas")}function yn(c,e){if(c.toBlob!==void 0)return new Promise(i=>c.toBlob(i,e));let t;return e==="image/jpeg"?t=.92:e==="image/webp"&&(t=.8),c.convertToBlob({type:e,quality:t})}class xs{constructor(){this.plugins=[],this.options={},this.pending=[],this.buffers=[],this.byteOffset=0,this.buffers=[],this.nodeMap=new Map,this.skins=[],this.extensionsUsed={},this.extensionsRequired={},this.uids=new Map,this.uid=0,this.json={asset:{version:"2.0",generator:"THREE.GLTFExporter"}},this.cache={meshes:new Map,attributes:new Map,attributesNormalized:new Map,materials:new Map,textures:new Map,images:new Map}}setPlugins(e){this.plugins=e}async write(e,t,i={}){this.options=Object.assign({binary:!1,trs:!1,onlyVisible:!0,maxTextureSize:1/0,animations:[],includeCustomExtensions:!1},i),this.options.animations.length>0&&(this.options.trs=!0),this.processInput(e),await Promise.all(this.pending);const n=this,s=n.buffers,A=n.json;i=n.options;const o=n.extensionsUsed,a=n.extensionsRequired,l=new Blob(s,{type:"application/octet-stream"}),g=Object.keys(o),u=Object.keys(a);if(g.length>0&&(A.extensionsUsed=g),u.length>0&&(A.extensionsRequired=u),A.buffers&&A.buffers.length>0&&(A.buffers[0].byteLength=l.size),i.binary===!0){const f=new FileReader;f.readAsArrayBuffer(l),f.onloadend=function(){const h=yt(f.result),p=new DataView(new ArrayBuffer(Cn));p.setUint32(0,h.byteLength,!0),p.setUint32(4,ms,!0);const I=yt(Qs(JSON.stringify(A)),32),E=new DataView(new ArrayBuffer(Cn));E.setUint32(0,I.byteLength,!0),E.setUint32(4,Cs,!0);const B=new ArrayBuffer(Bn),Q=new DataView(B);Q.setUint32(0,Es,!0),Q.setUint32(4,Bs,!0);const d=Bn+E.byteLength+I.byteLength+p.byteLength+h.byteLength;Q.setUint32(8,d,!0);const C=new Blob([B,E,I,p,h],{type:"application/octet-stream"}),x=new FileReader;x.readAsArrayBuffer(C),x.onloadend=function(){t(x.result)}}}else if(A.buffers&&A.buffers.length>0){const f=new FileReader;f.readAsDataURL(l),f.onloadend=function(){const h=f.result;A.buffers[0].uri=h,t(A)}}else t(A)}serializeUserData(e,t){if(Object.keys(e.userData).length===0)return;const i=this.options,n=this.extensionsUsed;try{const s=JSON.parse(JSON.stringify(e.userData));if(i.includeCustomExtensions&&s.gltfExtensions){t.extensions===void 0&&(t.extensions={});for(const A in s.gltfExtensions)t.extensions[A]=s.gltfExtensions[A],n[A]=!0;delete s.gltfExtensions}Object.keys(s).length>0&&(t.extras=s)}catch(s){console.warn("THREE.GLTFExporter: userData of '"+e.name+"' won't be serialized because of JSON.stringify error - "+s.message)}}getUID(e,t=!1){if(this.uids.has(e)===!1){const n=new Map;n.set(!0,this.uid++),n.set(!1,this.uid++),this.uids.set(e,n)}return this.uids.get(e).get(t)}isNormalizedNormalAttribute(e){if(this.cache.attributesNormalized.has(e))return!1;const i=new r.Vector3;for(let n=0,s=e.count;n<s;n++)if(Math.abs(i.fromBufferAttribute(e,n).length()-1)>5e-4)return!1;return!0}createNormalizedNormalAttribute(e){const t=this.cache;if(t.attributesNormalized.has(e))return t.attributesNormalized.get(e);const i=e.clone(),n=new r.Vector3;for(let s=0,A=i.count;s<A;s++)n.fromBufferAttribute(i,s),n.x===0&&n.y===0&&n.z===0?n.setX(1):n.normalize(),i.setXYZ(s,n.x,n.y,n.z);return t.attributesNormalized.set(e,i),i}applyTextureTransform(e,t){let i=!1;const n={};(t.offset.x!==0||t.offset.y!==0)&&(n.offset=t.offset.toArray(),i=!0),t.rotation!==0&&(n.rotation=t.rotation,i=!0),(t.repeat.x!==1||t.repeat.y!==1)&&(n.scale=t.repeat.toArray(),i=!0),i&&(e.extensions=e.extensions||{},e.extensions.KHR_texture_transform=n,this.extensionsUsed.KHR_texture_transform=!0)}buildMetalRoughTexture(e,t){if(e===t)return e;function i(h){return h.colorSpace===r.SRGBColorSpace?function(I){return I<.04045?I*.0773993808:Math.pow(I*.9478672986+.0521327014,2.4)}:function(I){return I}}console.warn("THREE.GLTFExporter: Merged metalnessMap and roughnessMap textures.");const n=e?e.image:null,s=t?t.image:null,A=Math.max(n?n.width:0,s?s.width:0),o=Math.max(n?n.height:0,s?s.height:0),a=Qn();a.width=A,a.height=o;const l=a.getContext("2d");l.fillStyle="#00ffff",l.fillRect(0,0,A,o);const g=l.getImageData(0,0,A,o);if(n){l.drawImage(n,0,0,A,o);const h=i(e),p=l.getImageData(0,0,A,o).data;for(let I=2;I<p.length;I+=4)g.data[I]=h(p[I]/256)*256}if(s){l.drawImage(s,0,0,A,o);const h=i(t),p=l.getImageData(0,0,A,o).data;for(let I=1;I<p.length;I+=4)g.data[I]=h(p[I]/256)*256}l.putImageData(g,0,0);const f=(e||t).clone();return f.source=new r.Source(a),f.colorSpace=r.NoColorSpace,f.channel=(e||t).channel,e&&t&&e.channel!==t.channel&&console.warn("THREE.GLTFExporter: UV channels for metalnessMap and roughnessMap textures must match."),f}processBuffer(e){const t=this.json,i=this.buffers;return t.buffers||(t.buffers=[{byteLength:0}]),i.push(e),0}processBufferView(e,t,i,n,s){const A=this.json;A.bufferViews||(A.bufferViews=[]);let o;switch(t){case q.BYTE:case q.UNSIGNED_BYTE:o=1;break;case q.SHORT:case q.UNSIGNED_SHORT:o=2;break;default:o=4}const a=mn(n*e.itemSize*o),l=new DataView(new ArrayBuffer(a));let g=0;for(let h=i;h<i+n;h++)for(let p=0;p<e.itemSize;p++){let I;e.itemSize>4?I=e.array[h*e.itemSize+p]:(p===0?I=e.getX(h):p===1?I=e.getY(h):p===2?I=e.getZ(h):p===3&&(I=e.getW(h)),e.normalized===!0&&(I=r.MathUtils.normalize(I,e.array))),t===q.FLOAT?l.setFloat32(g,I,!0):t===q.INT?l.setInt32(g,I,!0):t===q.UNSIGNED_INT?l.setUint32(g,I,!0):t===q.SHORT?l.setInt16(g,I,!0):t===q.UNSIGNED_SHORT?l.setUint16(g,I,!0):t===q.BYTE?l.setInt8(g,I):t===q.UNSIGNED_BYTE&&l.setUint8(g,I),g+=o}const u={buffer:this.processBuffer(l.buffer),byteOffset:this.byteOffset,byteLength:a};return s!==void 0&&(u.target=s),s===q.ARRAY_BUFFER&&(u.byteStride=e.itemSize*o),this.byteOffset+=a,A.bufferViews.push(u),{id:A.bufferViews.length-1,byteLength:0}}processBufferViewImage(e){const t=this,i=t.json;return i.bufferViews||(i.bufferViews=[]),new Promise(function(n){const s=new FileReader;s.readAsArrayBuffer(e),s.onloadend=function(){const A=yt(s.result),o={buffer:t.processBuffer(A),byteOffset:t.byteOffset,byteLength:A.byteLength};t.byteOffset+=A.byteLength,n(i.bufferViews.push(o)-1)}})}processAccessor(e,t,i,n){const s=this.json,A={1:"SCALAR",2:"VEC2",3:"VEC3",4:"VEC4",9:"MAT3",16:"MAT4"};let o;if(e.array.constructor===Float32Array)o=q.FLOAT;else if(e.array.constructor===Int32Array)o=q.INT;else if(e.array.constructor===Uint32Array)o=q.UNSIGNED_INT;else if(e.array.constructor===Int16Array)o=q.SHORT;else if(e.array.constructor===Uint16Array)o=q.UNSIGNED_SHORT;else if(e.array.constructor===Int8Array)o=q.BYTE;else if(e.array.constructor===Uint8Array)o=q.UNSIGNED_BYTE;else throw new Error("THREE.GLTFExporter: Unsupported bufferAttribute component type.");if(i===void 0&&(i=0),n===void 0&&(n=e.count),n===0)return null;const a=ws(e,i,n);let l;t!==void 0&&(l=e===t.index?q.ELEMENT_ARRAY_BUFFER:q.ARRAY_BUFFER);const g=this.processBufferView(e,o,i,n,l),u={bufferView:g.id,byteOffset:g.byteOffset,componentType:o,count:n,max:a.max,min:a.min,type:A[e.itemSize]};return e.normalized===!0&&(u.normalized=!0),s.accessors||(s.accessors=[]),s.accessors.push(u)-1}processImage(e,t,i,n="image/png"){if(e!==null){const s=this,A=s.cache,o=s.json,a=s.options,l=s.pending;A.images.has(e)||A.images.set(e,{});const g=A.images.get(e),u=n+":flipY/"+i.toString();if(g[u]!==void 0)return g[u];o.images||(o.images=[]);const f={mimeType:n},h=Qn();h.width=Math.min(e.width,a.maxTextureSize),h.height=Math.min(e.height,a.maxTextureSize);const p=h.getContext("2d");if(i===!0&&(p.translate(0,h.height),p.scale(1,-1)),e.data!==void 0){t!==r.RGBAFormat&&console.error("GLTFExporter: Only RGBAFormat is supported."),(e.width>a.maxTextureSize||e.height>a.maxTextureSize)&&console.warn("GLTFExporter: Image size is bigger than maxTextureSize",e);const E=new Uint8ClampedArray(e.height*e.width*4);for(let B=0;B<E.length;B+=4)E[B+0]=e.data[B+0],E[B+1]=e.data[B+1],E[B+2]=e.data[B+2],E[B+3]=e.data[B+3];p.putImageData(new ImageData(E,e.width,e.height),0,0)}else p.drawImage(e,0,0,h.width,h.height);a.binary===!0?l.push(yn(h,n).then(E=>s.processBufferViewImage(E)).then(E=>{f.bufferView=E})):h.toDataURL!==void 0?f.uri=h.toDataURL(n):l.push(yn(h,n).then(E=>new FileReader().readAsDataURL(E)).then(E=>{f.uri=E}));const I=o.images.push(f)-1;return g[u]=I,I}else throw new Error("THREE.GLTFExporter: No valid image data found. Unable to process texture.")}processSampler(e){const t=this.json;t.samplers||(t.samplers=[]);const i={magFilter:oe[e.magFilter],minFilter:oe[e.minFilter],wrapS:oe[e.wrapS],wrapT:oe[e.wrapT]};return t.samplers.push(i)-1}processTexture(e){const t=this.cache,i=this.json;if(t.textures.has(e))return t.textures.get(e);i.textures||(i.textures=[]);let n=e.userData.mimeType;n==="image/webp"&&(n="image/png");const s={sampler:this.processSampler(e),source:this.processImage(e.image,e.format,e.flipY,n)};e.name&&(s.name=e.name),this._invokeAll(function(o){o.writeTexture&&o.writeTexture(e,s)});const A=i.textures.push(s)-1;return t.textures.set(e,A),A}processMaterial(e){const t=this.cache,i=this.json;if(t.materials.has(e))return t.materials.get(e);if(e.isShaderMaterial)return console.warn("GLTFExporter: THREE.ShaderMaterial not supported."),null;i.materials||(i.materials=[]);const n={pbrMetallicRoughness:{}};e.isMeshStandardMaterial!==!0&&e.isMeshBasicMaterial!==!0&&console.warn("GLTFExporter: Use MeshStandardMaterial or MeshBasicMaterial for best results.");const s=e.color.toArray().concat([e.opacity]);if(_e(s,[1,1,1,1])||(n.pbrMetallicRoughness.baseColorFactor=s),e.isMeshStandardMaterial?(n.pbrMetallicRoughness.metallicFactor=e.metalness,n.pbrMetallicRoughness.roughnessFactor=e.roughness):(n.pbrMetallicRoughness.metallicFactor=.5,n.pbrMetallicRoughness.roughnessFactor=.5),e.metalnessMap||e.roughnessMap){const o=this.buildMetalRoughTexture(e.metalnessMap,e.roughnessMap),a={index:this.processTexture(o),channel:o.channel};this.applyTextureTransform(a,o),n.pbrMetallicRoughness.metallicRoughnessTexture=a}if(e.map){const o={index:this.processTexture(e.map),texCoord:e.map.channel};this.applyTextureTransform(o,e.map),n.pbrMetallicRoughness.baseColorTexture=o}if(e.emissive){const o=e.emissive;if(Math.max(o.r,o.g,o.b)>0&&(n.emissiveFactor=e.emissive.toArray()),e.emissiveMap){const l={index:this.processTexture(e.emissiveMap),texCoord:e.emissiveMap.channel};this.applyTextureTransform(l,e.emissiveMap),n.emissiveTexture=l}}if(e.normalMap){const o={index:this.processTexture(e.normalMap),texCoord:e.normalMap.channel};e.normalScale&&e.normalScale.x!==1&&(o.scale=e.normalScale.x),this.applyTextureTransform(o,e.normalMap),n.normalTexture=o}if(e.aoMap){const o={index:this.processTexture(e.aoMap),texCoord:e.aoMap.channel};e.aoMapIntensity!==1&&(o.strength=e.aoMapIntensity),this.applyTextureTransform(o,e.aoMap),n.occlusionTexture=o}e.transparent?n.alphaMode="BLEND":e.alphaTest>0&&(n.alphaMode="MASK",n.alphaCutoff=e.alphaTest),e.side===r.DoubleSide&&(n.doubleSided=!0),e.name!==""&&(n.name=e.name),this.serializeUserData(e,n),this._invokeAll(function(o){o.writeMaterial&&o.writeMaterial(e,n)});const A=i.materials.push(n)-1;return t.materials.set(e,A),A}processMesh(e){const t=this.cache,i=this.json,n=[e.geometry.uuid];if(Array.isArray(e.material))for(let d=0,C=e.material.length;d<C;d++)n.push(e.material[d].uuid);else n.push(e.material.uuid);const s=n.join(":");if(t.meshes.has(s))return t.meshes.get(s);const A=e.geometry;let o;e.isLineSegments?o=q.LINES:e.isLineLoop?o=q.LINE_LOOP:e.isLine?o=q.LINE_STRIP:e.isPoints?o=q.POINTS:o=e.material.wireframe?q.LINES:q.TRIANGLES;const a={},l={},g=[],u=[],f={uv:"TEXCOORD_0",uv1:"TEXCOORD_1",color:"COLOR_0",skinWeight:"WEIGHTS_0",skinIndex:"JOINTS_0"},h=A.getAttribute("normal");h!==void 0&&!this.isNormalizedNormalAttribute(h)&&(console.warn("THREE.GLTFExporter: Creating normalized normal attribute from the non-normalized one."),A.setAttribute("normal",this.createNormalizedNormalAttribute(h)));let p=null;for(let d in A.attributes){if(d.slice(0,5)==="morph")continue;const C=A.attributes[d];if(d=f[d]||d.toUpperCase(),/^(POSITION|NORMAL|TANGENT|TEXCOORD_\d+|COLOR_\d+|JOINTS_\d+|WEIGHTS_\d+)$/.test(d)||(d="_"+d),t.attributes.has(this.getUID(C))){l[d]=t.attributes.get(this.getUID(C));continue}p=null;const m=C.array;d==="JOINTS_0"&&!(m instanceof Uint16Array)&&!(m instanceof Uint8Array)&&(console.warn('GLTFExporter: Attribute "skinIndex" converted to type UNSIGNED_SHORT.'),p=new r.BufferAttribute(new Uint16Array(m),C.itemSize,C.normalized));const S=this.processAccessor(p||C,A);S!==null&&(d.startsWith("_")||this.detectMeshQuantization(d,C),l[d]=S,t.attributes.set(this.getUID(C),S))}if(h!==void 0&&A.setAttribute("normal",h),Object.keys(l).length===0)return null;if(e.morphTargetInfluences!==void 0&&e.morphTargetInfluences.length>0){const d=[],C=[],x={};if(e.morphTargetDictionary!==void 0)for(const m in e.morphTargetDictionary)x[e.morphTargetDictionary[m]]=m;for(let m=0;m<e.morphTargetInfluences.length;++m){const S={};let w=!1;for(const T in A.morphAttributes){if(T!=="position"&&T!=="normal"){w||(console.warn("GLTFExporter: Only POSITION and NORMAL morph are supported."),w=!0);continue}const M=A.morphAttributes[T][m],R=T.toUpperCase(),U=A.attributes[T];if(t.attributes.has(this.getUID(M,!0))){S[R]=t.attributes.get(this.getUID(M,!0));continue}const V=M.clone();if(!A.morphTargetsRelative)for(let O=0,P=M.count;O<P;O++)for(let y=0;y<M.itemSize;y++)y===0&&V.setX(O,M.getX(O)-U.getX(O)),y===1&&V.setY(O,M.getY(O)-U.getY(O)),y===2&&V.setZ(O,M.getZ(O)-U.getZ(O)),y===3&&V.setW(O,M.getW(O)-U.getW(O));S[R]=this.processAccessor(V,A),t.attributes.set(this.getUID(U,!0),S[R])}u.push(S),d.push(e.morphTargetInfluences[m]),e.morphTargetDictionary!==void 0&&C.push(x[m])}a.weights=d,C.length>0&&(a.extras={},a.extras.targetNames=C)}const I=Array.isArray(e.material);if(I&&A.groups.length===0)return null;const E=I?e.material:[e.material],B=I?A.groups:[{materialIndex:0,start:void 0,count:void 0}];for(let d=0,C=B.length;d<C;d++){const x={mode:o,attributes:l};if(this.serializeUserData(A,x),u.length>0&&(x.targets=u),A.index!==null){let S=this.getUID(A.index);(B[d].start!==void 0||B[d].count!==void 0)&&(S+=":"+B[d].start+":"+B[d].count),t.attributes.has(S)?x.indices=t.attributes.get(S):(x.indices=this.processAccessor(A.index,A,B[d].start,B[d].count),t.attributes.set(S,x.indices)),x.indices===null&&delete x.indices}const m=this.processMaterial(E[B[d].materialIndex]);m!==null&&(x.material=m),g.push(x)}a.primitives=g,i.meshes||(i.meshes=[]),this._invokeAll(function(d){d.writeMesh&&d.writeMesh(e,a)});const Q=i.meshes.push(a)-1;return t.meshes.set(s,Q),Q}detectMeshQuantization(e,t){if(this.extensionsUsed[Qt])return;let i;switch(t.array.constructor){case Int8Array:i="byte";break;case Uint8Array:i="unsigned byte";break;case Int16Array:i="short";break;case Uint16Array:i="unsigned short";break;default:return}t.normalized&&(i+=" normalized");const n=e.split("_",1)[0];In[n]&&In[n].includes(i)&&(this.extensionsUsed[Qt]=!0,this.extensionsRequired[Qt]=!0)}processCamera(e){const t=this.json;t.cameras||(t.cameras=[]);const i=e.isOrthographicCamera,n={type:i?"orthographic":"perspective"};return i?n.orthographic={xmag:e.right*2,ymag:e.top*2,zfar:e.far<=0?.001:e.far,znear:e.near<0?0:e.near}:n.perspective={aspectRatio:e.aspect,yfov:r.MathUtils.degToRad(e.fov),zfar:e.far<=0?.001:e.far,znear:e.near<0?0:e.near},e.name!==""&&(n.name=e.type),t.cameras.push(n)-1}processAnimation(e,t){const i=this.json,n=this.nodeMap;i.animations||(i.animations=[]),e=mt.Utils.mergeMorphTargetTracks(e.clone(),t);const s=e.tracks,A=[],o=[];for(let a=0;a<s.length;++a){const l=s[a],g=r.PropertyBinding.parseTrackName(l.name);let u=r.PropertyBinding.findNode(t,g.nodeName);const f=En[g.propertyName];if(g.objectName==="bones"&&(u.isSkinnedMesh===!0?u=u.skeleton.getBoneByName(g.objectIndex):u=void 0),!u||!f)return console.warn('THREE.GLTFExporter: Could not export animation track "%s".',l.name),null;const h=1;let p=l.values.length/l.times.length;f===En.morphTargetInfluences&&(p/=u.morphTargetInfluences.length);let I;l.createInterpolant.isInterpolantFactoryMethodGLTFCubicSpline===!0?(I="CUBICSPLINE",p/=3):l.getInterpolation()===r.InterpolateDiscrete?I="STEP":I="LINEAR",o.push({input:this.processAccessor(new r.BufferAttribute(l.times,h)),output:this.processAccessor(new r.BufferAttribute(l.values,p)),interpolation:I}),A.push({sampler:o.length-1,target:{node:n.get(u),path:f}})}return i.animations.push({name:e.name||"clip_"+i.animations.length,samplers:o,channels:A}),i.animations.length-1}processSkin(e){const t=this.json,i=this.nodeMap,n=t.nodes[i.get(e)],s=e.skeleton;if(s===void 0)return null;const A=e.skeleton.bones[0];if(A===void 0)return null;const o=[],a=new Float32Array(s.bones.length*16),l=new r.Matrix4;for(let u=0;u<s.bones.length;++u)o.push(i.get(s.bones[u])),l.copy(s.boneInverses[u]),l.multiply(e.bindMatrix).toArray(a,u*16);return t.skins===void 0&&(t.skins=[]),t.skins.push({inverseBindMatrices:this.processAccessor(new r.BufferAttribute(a,16)),joints:o,skeleton:i.get(A)}),n.skin=t.skins.length-1}processNode(e){const t=this.json,i=this.options,n=this.nodeMap;t.nodes||(t.nodes=[]);const s={};if(i.trs){const o=e.quaternion.toArray(),a=e.position.toArray(),l=e.scale.toArray();_e(o,[0,0,0,1])||(s.rotation=o),_e(a,[0,0,0])||(s.translation=a),_e(l,[1,1,1])||(s.scale=l)}else e.matrixAutoUpdate&&e.updateMatrix(),ys(e.matrix)===!1&&(s.matrix=e.matrix.elements);if(e.name!==""&&(s.name=String(e.name)),this.serializeUserData(e,s),e.isMesh||e.isLine||e.isPoints){const o=this.processMesh(e);o!==null&&(s.mesh=o)}else e.isCamera&&(s.camera=this.processCamera(e));if(e.isSkinnedMesh&&this.skins.push(e),e.children.length>0){const o=[];for(let a=0,l=e.children.length;a<l;a++){const g=e.children[a];if(g.visible||i.onlyVisible===!1){const u=this.processNode(g);u!==null&&o.push(u)}}o.length>0&&(s.children=o)}this._invokeAll(function(o){o.writeNode&&o.writeNode(e,s)});const A=t.nodes.push(s)-1;return n.set(e,A),A}processScene(e){const t=this.json,i=this.options;t.scenes||(t.scenes=[],t.scene=0);const n={};e.name!==""&&(n.name=e.name),t.scenes.push(n);const s=[];for(let A=0,o=e.children.length;A<o;A++){const a=e.children[A];if(a.visible||i.onlyVisible===!1){const l=this.processNode(a);l!==null&&s.push(l)}}s.length>0&&(n.nodes=s),this.serializeUserData(e,n)}processObjects(e){const t=new r.Scene;t.name="AuxScene";for(let i=0;i<e.length;i++)t.children.push(e[i]);this.processScene(t)}processInput(e){const t=this.options;e=e instanceof Array?e:[e],this._invokeAll(function(n){n.beforeParse&&n.beforeParse(e)});const i=[];for(let n=0;n<e.length;n++)e[n]instanceof r.Scene?this.processScene(e[n]):i.push(e[n]);i.length>0&&this.processObjects(i);for(let n=0;n<this.skins.length;++n)this.processSkin(this.skins[n]);for(let n=0;n<t.animations.length;++n)this.processAnimation(t.animations[n],e[0]);this._invokeAll(function(n){n.afterParse&&n.afterParse(e)})}_invokeAll(e){for(let t=0,i=this.plugins.length;t<i;t++)e(this.plugins[t])}}class Ts{constructor(e){this.writer=e,this.name="KHR_lights_punctual"}writeNode(e,t){if(!e.isLight)return;if(!e.isDirectionalLight&&!e.isPointLight&&!e.isSpotLight){console.warn("THREE.GLTFExporter: Only directional, point, and spot lights are supported.",e);return}const i=this.writer,n=i.json,s=i.extensionsUsed,A={};e.name&&(A.name=e.name),A.color=e.color.toArray(),A.intensity=e.intensity,e.isDirectionalLight?A.type="directional":e.isPointLight?(A.type="point",e.distance>0&&(A.range=e.distance)):e.isSpotLight&&(A.type="spot",e.distance>0&&(A.range=e.distance),A.spot={},A.spot.innerConeAngle=(e.penumbra-1)*e.angle*-1,A.spot.outerConeAngle=e.angle),e.decay!==void 0&&e.decay!==2&&console.warn("THREE.GLTFExporter: Light decay may be lost. glTF is physically-based, and expects light.decay=2."),e.target&&(e.target.parent!==e||e.target.position.x!==0||e.target.position.y!==0||e.target.position.z!==-1)&&console.warn("THREE.GLTFExporter: Light direction may be lost. For best results, make light.target a child of the light with position 0,0,-1."),s[this.name]||(n.extensions=n.extensions||{},n.extensions[this.name]={lights:[]},s[this.name]=!0);const o=n.extensions[this.name].lights;o.push(A),t.extensions=t.extensions||{},t.extensions[this.name]={light:o.length-1}}}class Ss{constructor(e){this.writer=e,this.name="KHR_materials_unlit"}writeMaterial(e,t){if(!e.isMeshBasicMaterial)return;const n=this.writer.extensionsUsed;t.extensions=t.extensions||{},t.extensions[this.name]={},n[this.name]=!0,t.pbrMetallicRoughness.metallicFactor=0,t.pbrMetallicRoughness.roughnessFactor=.9}}class Ms{constructor(e){this.writer=e,this.name="KHR_materials_clearcoat"}writeMaterial(e,t){if(!e.isMeshPhysicalMaterial||e.clearcoat===0)return;const i=this.writer,n=i.extensionsUsed,s={};if(s.clearcoatFactor=e.clearcoat,e.clearcoatMap){const A={index:i.processTexture(e.clearcoatMap),texCoord:e.clearcoatMap.channel};i.applyTextureTransform(A,e.clearcoatMap),s.clearcoatTexture=A}if(s.clearcoatRoughnessFactor=e.clearcoatRoughness,e.clearcoatRoughnessMap){const A={index:i.processTexture(e.clearcoatRoughnessMap),texCoord:e.clearcoatRoughnessMap.channel};i.applyTextureTransform(A,e.clearcoatRoughnessMap),s.clearcoatRoughnessTexture=A}if(e.clearcoatNormalMap){const A={index:i.processTexture(e.clearcoatNormalMap),texCoord:e.clearcoatNormalMap.channel};i.applyTextureTransform(A,e.clearcoatNormalMap),s.clearcoatNormalTexture=A}t.extensions=t.extensions||{},t.extensions[this.name]=s,n[this.name]=!0}}class vs{constructor(e){this.writer=e,this.name="KHR_materials_iridescence"}writeMaterial(e,t){if(!e.isMeshPhysicalMaterial||e.iridescence===0)return;const i=this.writer,n=i.extensionsUsed,s={};if(s.iridescenceFactor=e.iridescence,e.iridescenceMap){const A={index:i.processTexture(e.iridescenceMap),texCoord:e.iridescenceMap.channel};i.applyTextureTransform(A,e.iridescenceMap),s.iridescenceTexture=A}if(s.iridescenceIor=e.iridescenceIOR,s.iridescenceThicknessMinimum=e.iridescenceThicknessRange[0],s.iridescenceThicknessMaximum=e.iridescenceThicknessRange[1],e.iridescenceThicknessMap){const A={index:i.processTexture(e.iridescenceThicknessMap),texCoord:e.iridescenceThicknessMap.channel};i.applyTextureTransform(A,e.iridescenceThicknessMap),s.iridescenceThicknessTexture=A}t.extensions=t.extensions||{},t.extensions[this.name]=s,n[this.name]=!0}}class Rs{constructor(e){this.writer=e,this.name="KHR_materials_transmission"}writeMaterial(e,t){if(!e.isMeshPhysicalMaterial||e.transmission===0)return;const i=this.writer,n=i.extensionsUsed,s={};if(s.transmissionFactor=e.transmission,e.transmissionMap){const A={index:i.processTexture(e.transmissionMap),texCoord:e.transmissionMap.channel};i.applyTextureTransform(A,e.transmissionMap),s.transmissionTexture=A}t.extensions=t.extensions||{},t.extensions[this.name]=s,n[this.name]=!0}}class Ds{constructor(e){this.writer=e,this.name="KHR_materials_volume"}writeMaterial(e,t){if(!e.isMeshPhysicalMaterial||e.transmission===0)return;const i=this.writer,n=i.extensionsUsed,s={};if(s.thicknessFactor=e.thickness,e.thicknessMap){const A={index:i.processTexture(e.thicknessMap),texCoord:e.thicknessMap.channel};i.applyTextureTransform(A,e.thicknessMap),s.thicknessTexture=A}s.attenuationDistance=e.attenuationDistance,s.attenuationColor=e.attenuationColor.toArray(),t.extensions=t.extensions||{},t.extensions[this.name]=s,n[this.name]=!0}}class Ls{constructor(e){this.writer=e,this.name="KHR_materials_ior"}writeMaterial(e,t){if(!e.isMeshPhysicalMaterial||e.ior===1.5)return;const n=this.writer.extensionsUsed,s={};s.ior=e.ior,t.extensions=t.extensions||{},t.extensions[this.name]=s,n[this.name]=!0}}class Fs{constructor(e){this.writer=e,this.name="KHR_materials_specular"}writeMaterial(e,t){if(!e.isMeshPhysicalMaterial||e.specularIntensity===1&&e.specularColor.equals(Is)&&!e.specularIntensityMap&&!e.specularColorTexture)return;const i=this.writer,n=i.extensionsUsed,s={};if(e.specularIntensityMap){const A={index:i.processTexture(e.specularIntensityMap),texCoord:e.specularIntensityMap.channel};i.applyTextureTransform(A,e.specularIntensityMap),s.specularTexture=A}if(e.specularColorMap){const A={index:i.processTexture(e.specularColorMap),texCoord:e.specularColorMap.channel};i.applyTextureTransform(A,e.specularColorMap),s.specularColorTexture=A}s.specularFactor=e.specularIntensity,s.specularColorFactor=e.specularColor.toArray(),t.extensions=t.extensions||{},t.extensions[this.name]=s,n[this.name]=!0}}class bs{constructor(e){this.writer=e,this.name="KHR_materials_sheen"}writeMaterial(e,t){if(!e.isMeshPhysicalMaterial||e.sheen==0)return;const i=this.writer,n=i.extensionsUsed,s={};if(e.sheenRoughnessMap){const A={index:i.processTexture(e.sheenRoughnessMap),texCoord:e.sheenRoughnessMap.channel};i.applyTextureTransform(A,e.sheenRoughnessMap),s.sheenRoughnessTexture=A}if(e.sheenColorMap){const A={index:i.processTexture(e.sheenColorMap),texCoord:e.sheenColorMap.channel};i.applyTextureTransform(A,e.sheenColorMap),s.sheenColorTexture=A}s.sheenRoughnessFactor=e.sheenRoughness,s.sheenColorFactor=e.sheenColor.toArray(),t.extensions=t.extensions||{},t.extensions[this.name]=s,n[this.name]=!0}}class _s{constructor(e){this.writer=e,this.name="KHR_materials_emissive_strength"}writeMaterial(e,t){if(!e.isMeshStandardMaterial||e.emissiveIntensity===1)return;const n=this.writer.extensionsUsed,s={};s.emissiveStrength=e.emissiveIntensity,t.extensions=t.extensions||{},t.extensions[this.name]=s,n[this.name]=!0}}mt.Utils={insertKeyframe:function(c,e){const i=c.getValueSize(),n=new c.TimeBufferType(c.times.length+1),s=new c.ValueBufferType(c.values.length+i),A=c.createInterpolant(new c.ValueBufferType(i));let o;if(c.times.length===0){n[0]=e;for(let a=0;a<i;a++)s[a]=0;o=0}else if(e<c.times[0]){if(Math.abs(c.times[0]-e)<.001)return 0;n[0]=e,n.set(c.times,1),s.set(A.evaluate(e),0),s.set(c.values,i),o=0}else if(e>c.times[c.times.length-1]){if(Math.abs(c.times[c.times.length-1]-e)<.001)return c.times.length-1;n[n.length-1]=e,n.set(c.times,0),s.set(c.values,0),s.set(A.evaluate(e),c.values.length),o=n.length-1}else for(let a=0;a<c.times.length;a++){if(Math.abs(c.times[a]-e)<.001)return a;if(c.times[a]<e&&c.times[a+1]>e){n.set(c.times.slice(0,a+1),0),n[a+1]=e,n.set(c.times.slice(a+1),a+2),s.set(c.values.slice(0,(a+1)*i),0),s.set(A.evaluate(e),(a+1)*i),s.set(c.values.slice((a+1)*i),(a+2)*i),o=a+1;break}}return c.times=n,c.values=s,o},mergeMorphTargetTracks:function(c,e){const t=[],i={},n=c.tracks;for(let s=0;s<n.length;++s){let A=n[s];const o=r.PropertyBinding.parseTrackName(A.name),a=r.PropertyBinding.findNode(e,o.nodeName);if(o.propertyName!=="morphTargetInfluences"||o.propertyIndex===void 0){t.push(A);continue}if(A.createInterpolant!==A.InterpolantFactoryMethodDiscrete&&A.createInterpolant!==A.InterpolantFactoryMethodLinear){if(A.createInterpolant.isInterpolantFactoryMethodGLTFCubicSpline)throw new Error("THREE.GLTFExporter: Cannot merge tracks with glTF CUBICSPLINE interpolation.");console.warn("THREE.GLTFExporter: Morph target interpolation mode not yet supported. Using LINEAR instead."),A=A.clone(),A.setInterpolation(r.InterpolateLinear)}const l=a.morphTargetInfluences.length,g=a.morphTargetDictionary[o.propertyIndex];if(g===void 0)throw new Error("THREE.GLTFExporter: Morph target name not found: "+o.propertyIndex);let u;if(i[a.uuid]===void 0){u=A.clone();const h=new u.ValueBufferType(l*u.times.length);for(let p=0;p<u.times.length;p++)h[p*l+g]=u.values[p];u.name=(o.nodeName||"")+".morphTargetInfluences",u.values=h,i[a.uuid]=u,t.push(u);continue}const f=A.createInterpolant(new A.ValueBufferType(1));u=i[a.uuid];for(let h=0;h<u.times.length;h++)u.values[h*l+g]=f.evaluate(u.times[h]);for(let h=0;h<A.times.length;h++){const p=this.insertKeyframe(u,A.times[h]);u.values[p*l+g]=A.values[h]}}return c.tracks=t,c}};const we=new r.Raycaster,Ae=new r.Vector3,me=new r.Vector3,W=new r.Quaternion,wn={X:new r.Vector3(1,0,0),Y:new r.Vector3(0,1,0),Z:new r.Vector3(0,0,1)},wt={type:"change"},xn={type:"mouseDown"},Tn={type:"mouseUp",mode:null},Sn={type:"objectChange"};class Gs extends r.Object3D{constructor(e,t,i){super(),t===void 0&&(console.warn('THREE.TransformControls: The second parameter "domElement" is now mandatory.'),t=document),this.isTransformControls=!0,this.visible=!1,this.domElement=t,this.domElement.style.touchAction="none";const n=new Hs(i);this._gizmo=n,this.add(n);const s=new Vs;this._plane=s,this.add(s);const A=this;function o(d,C){let x=C;Object.defineProperty(A,d,{get:function(){return x!==void 0?x:C},set:function(m){x!==m&&(x=m,s[d]=m,n[d]=m,A.dispatchEvent({type:d+"-changed",value:m}),A.dispatchEvent(wt))}}),A[d]=C,s[d]=C,n[d]=C}o("camera",e),o("object",void 0),o("enabled",!0),o("axis",null),o("mode","translate"),o("translationSnap",null),o("rotationSnap",null),o("scaleSnap",null),o("space","world"),o("size",1),o("dragging",!1),o("showX",!0),o("showY",!0),o("showZ",!0);const a=new r.Vector3,l=new r.Vector3,g=new r.Quaternion,u=new r.Quaternion,f=new r.Vector3,h=new r.Quaternion,p=new r.Vector3,I=new r.Vector3,E=new r.Vector3,B=0,Q=new r.Vector3;o("worldPosition",a),o("worldPositionStart",l),o("worldQuaternion",g),o("worldQuaternionStart",u),o("cameraPosition",f),o("cameraQuaternion",h),o("pointStart",p),o("pointEnd",I),o("rotationAxis",E),o("rotationAngle",B),o("eye",Q),this._offset=new r.Vector3,this._startNorm=new r.Vector3,this._endNorm=new r.Vector3,this._cameraScale=new r.Vector3,this._parentPosition=new r.Vector3,this._parentQuaternion=new r.Quaternion,this._parentQuaternionInv=new r.Quaternion,this._parentScale=new r.Vector3,this._worldScaleStart=new r.Vector3,this._worldQuaternionInv=new r.Quaternion,this._worldScale=new r.Vector3,this._positionStart=new r.Vector3,this._quaternionStart=new r.Quaternion,this._scaleStart=new r.Vector3,this._getPointer=Us.bind(this),this._onPointerDown=Ns.bind(this),this._onPointerHover=ks.bind(this),this._onPointerMove=Ps.bind(this),this._onPointerUp=Os.bind(this),this.domElement.addEventListener("pointerdown",this._onPointerDown),this.domElement.addEventListener("pointermove",this._onPointerHover),this.domElement.addEventListener("pointerup",this._onPointerUp)}updateMatrixWorld(){this.object!==void 0&&(this.object.updateMatrixWorld(),this.object.parent===null?console.error("TransformControls: The attached 3D object must be a part of the scene graph."):this.object.parent.matrixWorld.decompose(this._parentPosition,this._parentQuaternion,this._parentScale),this.object.matrixWorld.decompose(this.worldPosition,this.worldQuaternion,this._worldScale),this._parentQuaternionInv.copy(this._parentQuaternion).invert(),this._worldQuaternionInv.copy(this.worldQuaternion).invert()),this.camera.updateMatrixWorld(),this.camera.matrixWorld.decompose(this.cameraPosition,this.cameraQuaternion,this._cameraScale),this.camera.isOrthographicCamera?this.camera.getWorldDirection(this.eye).negate():this.eye.copy(this.cameraPosition).sub(this.worldPosition).normalize(),super.updateMatrixWorld(this)}pointerHover(e){if(this.object===void 0||this.dragging===!0)return;we.setFromCamera(e,this.camera);const t=xt(this._gizmo.picker[this.mode],we);t?this.axis=t.object.name:this.axis=null}pointerDown(e){if(!(this.object===void 0||this.dragging===!0||e.button!==0)&&this.axis!==null){we.setFromCamera(e,this.camera);const t=xt(this._plane,we,!0);t&&(this.object.updateMatrixWorld(),this.object.parent.updateMatrixWorld(),this._positionStart.copy(this.object.position),this._quaternionStart.copy(this.object.quaternion),this._scaleStart.copy(this.object.scale),this.object.matrixWorld.decompose(this.worldPositionStart,this.worldQuaternionStart,this._worldScaleStart),this.pointStart.copy(t.point).sub(this.worldPositionStart)),this.dragging=!0,xn.mode=this.mode,this.dispatchEvent(xn)}}pointerMove(e){const t=this.axis,i=this.mode,n=this.object;let s=this.space;if(i==="scale"?s="local":(t==="E"||t==="XYZE"||t==="XYZ")&&(s="world"),n===void 0||t===null||this.dragging===!1||e.button!==-1)return;we.setFromCamera(e,this.camera);const A=xt(this._plane,we,!0);if(A){if(this.pointEnd.copy(A.point).sub(this.worldPositionStart),i==="translate")this._offset.copy(this.pointEnd).sub(this.pointStart),s==="local"&&t!=="XYZ"&&this._offset.applyQuaternion(this._worldQuaternionInv),t.indexOf("X")===-1&&(this._offset.x=0),t.indexOf("Y")===-1&&(this._offset.y=0),t.indexOf("Z")===-1&&(this._offset.z=0),s==="local"&&t!=="XYZ"?this._offset.applyQuaternion(this._quaternionStart).divide(this._parentScale):this._offset.applyQuaternion(this._parentQuaternionInv).divide(this._parentScale),n.position.copy(this._offset).add(this._positionStart),this.translationSnap&&(s==="local"&&(n.position.applyQuaternion(W.copy(this._quaternionStart).invert()),t.search("X")!==-1&&(n.position.x=Math.round(n.position.x/this.translationSnap)*this.translationSnap),t.search("Y")!==-1&&(n.position.y=Math.round(n.position.y/this.translationSnap)*this.translationSnap),t.search("Z")!==-1&&(n.position.z=Math.round(n.position.z/this.translationSnap)*this.translationSnap),n.position.applyQuaternion(this._quaternionStart)),s==="world"&&(n.parent&&n.position.add(Ae.setFromMatrixPosition(n.parent.matrixWorld)),t.search("X")!==-1&&(n.position.x=Math.round(n.position.x/this.translationSnap)*this.translationSnap),t.search("Y")!==-1&&(n.position.y=Math.round(n.position.y/this.translationSnap)*this.translationSnap),t.search("Z")!==-1&&(n.position.z=Math.round(n.position.z/this.translationSnap)*this.translationSnap),n.parent&&n.position.sub(Ae.setFromMatrixPosition(n.parent.matrixWorld))));else if(i==="scale"){if(t.search("XYZ")!==-1){let o=this.pointEnd.length()/this.pointStart.length();this.pointEnd.dot(this.pointStart)<0&&(o*=-1),me.set(o,o,o)}else Ae.copy(this.pointStart),me.copy(this.pointEnd),Ae.applyQuaternion(this._worldQuaternionInv),me.applyQuaternion(this._worldQuaternionInv),me.divide(Ae),t.search("X")===-1&&(me.x=1),t.search("Y")===-1&&(me.y=1),t.search("Z")===-1&&(me.z=1);n.scale.copy(this._scaleStart).multiply(me),this.scaleSnap&&(t.search("X")!==-1&&(n.scale.x=Math.round(n.scale.x/this.scaleSnap)*this.scaleSnap||this.scaleSnap),t.search("Y")!==-1&&(n.scale.y=Math.round(n.scale.y/this.scaleSnap)*this.scaleSnap||this.scaleSnap),t.search("Z")!==-1&&(n.scale.z=Math.round(n.scale.z/this.scaleSnap)*this.scaleSnap||this.scaleSnap))}else if(i==="rotate"){this._offset.copy(this.pointEnd).sub(this.pointStart);const o=20/this.worldPosition.distanceTo(Ae.setFromMatrixPosition(this.camera.matrixWorld));t==="E"?(this.rotationAxis.copy(this.eye),this.rotationAngle=this.pointEnd.angleTo(this.pointStart),this._startNorm.copy(this.pointStart).normalize(),this._endNorm.copy(this.pointEnd).normalize(),this.rotationAngle*=this._endNorm.cross(this._startNorm).dot(this.eye)<0?1:-1):t==="XYZE"?(this.rotationAxis.copy(this._offset).cross(this.eye).normalize(),this.rotationAngle=this._offset.dot(Ae.copy(this.rotationAxis).cross(this.eye))*o):(t==="X"||t==="Y"||t==="Z")&&(this.rotationAxis.copy(wn[t]),Ae.copy(wn[t]),s==="local"&&Ae.applyQuaternion(this.worldQuaternion),this.rotationAngle=this._offset.dot(Ae.cross(this.eye).normalize())*o),this.rotationSnap&&(this.rotationAngle=Math.round(this.rotationAngle/this.rotationSnap)*this.rotationSnap),s==="local"&&t!=="E"&&t!=="XYZE"?(n.quaternion.copy(this._quaternionStart),n.quaternion.multiply(W.setFromAxisAngle(this.rotationAxis,this.rotationAngle)).normalize()):(this.rotationAxis.applyQuaternion(this._parentQuaternionInv),n.quaternion.copy(W.setFromAxisAngle(this.rotationAxis,this.rotationAngle)),n.quaternion.multiply(this._quaternionStart).normalize())}this.dispatchEvent(wt),this.dispatchEvent(Sn)}}pointerUp(e){e.button===0&&(this.dragging&&this.axis!==null&&(Tn.mode=this.mode,this.dispatchEvent(Tn)),this.dragging=!1,this.axis=null)}dispose(){this.domElement.removeEventListener("pointerdown",this._onPointerDown),this.domElement.removeEventListener("pointermove",this._onPointerHover),this.domElement.removeEventListener("pointermove",this._onPointerMove),this.domElement.removeEventListener("pointerup",this._onPointerUp),this.traverse(function(e){e.geometry&&e.geometry.dispose(),e.material&&e.material.dispose()})}attach(e){return this.object=e,this.visible=!0,this}detach(){return this.object=void 0,this.visible=!1,this.axis=null,this}reset(){this.enabled&&this.dragging&&(this.object.position.copy(this._positionStart),this.object.quaternion.copy(this._quaternionStart),this.object.scale.copy(this._scaleStart),this.dispatchEvent(wt),this.dispatchEvent(Sn),this.pointStart.copy(this.pointEnd))}getRaycaster(){return we}getMode(){return this.mode}setMode(e){this.mode=e}setTranslationSnap(e){this.translationSnap=e}setRotationSnap(e){this.rotationSnap=e}setScaleSnap(e){this.scaleSnap=e}setSize(e){this.size=e}setSpace(e){this.space=e}}function Us(c){if(this.domElement.ownerDocument.pointerLockElement)return{x:0,y:0,button:c.button};{const e=this.domElement.getBoundingClientRect();return{x:(c.clientX-e.left)/e.width*2-1,y:-(c.clientY-e.top)/e.height*2+1,button:c.button}}}function ks(c){if(this.enabled)switch(c.pointerType){case"mouse":case"pen":this.pointerHover(this._getPointer(c));break}}function Ns(c){this.enabled&&(document.pointerLockElement||this.domElement.setPointerCapture(c.pointerId),this.domElement.addEventListener("pointermove",this._onPointerMove),this.pointerHover(this._getPointer(c)),this.pointerDown(this._getPointer(c)))}function Ps(c){this.enabled&&this.pointerMove(this._getPointer(c))}function Os(c){this.enabled&&(this.domElement.releasePointerCapture(c.pointerId),this.domElement.removeEventListener("pointermove",this._onPointerMove),this.pointerUp(this._getPointer(c)))}function xt(c,e,t){const i=e.intersectObject(c,!0);for(let n=0;n<i.length;n++)if(i[n].object.visible||t)return i[n];return!1}const Ye=new r.Euler,z=new r.Vector3(0,1,0),Mn=new r.Vector3(0,0,0),vn=new r.Matrix4,Ke=new r.Quaternion,Je=new r.Quaternion,he=new r.Vector3,Rn=new r.Matrix4,Ge=new r.Vector3(1,0,0),xe=new r.Vector3(0,1,0),Ue=new r.Vector3(0,0,1),ze=new r.Vector3,ke=new r.Vector3,Ne=new r.Vector3;class Hs extends r.Object3D{constructor(e){super(),e==null&&(e=!1),this.isTransformControlsGizmo=!0,this.type="TransformControlsGizmo";const t=new r.MeshBasicMaterial({depthTest:!1,depthWrite:!1,fog:!1,toneMapped:!1,transparent:!0}),i=new r.LineBasicMaterial({depthTest:!1,depthWrite:!1,fog:!1,toneMapped:!1,transparent:!0}),n=t.clone();n.opacity=.15;const s=i.clone();s.opacity=.5;const A=t.clone();A.color.setHex(16711680);const o=t.clone();o.color.setHex(65280);const a=t.clone();a.color.setHex(255);const l=t.clone();l.color.setHex(16711680),l.opacity=.5;const g=t.clone();g.color.setHex(65280),g.opacity=.5;const u=t.clone();u.color.setHex(255),u.opacity=.5;const f=t.clone();f.opacity=.25;const h=t.clone();h.color.setHex(16776960),h.opacity=.25,t.clone().color.setHex(16776960);const I=t.clone();I.color.setHex(7895160);const E=new r.CylinderGeometry(0,.04,.1,12);E.translate(0,.05,0);const B=new r.BoxGeometry(.08,.08,.08);B.translate(0,.04,0);const Q=new r.BufferGeometry;Q.setAttribute("position",new r.Float32BufferAttribute([0,0,0,1,0,0],3));const d=new r.CylinderGeometry(.0075,.0075,.5,3);d.translate(0,.25,0);function C(y,L){const D=new r.TorusGeometry(y,.0075,3,64,L*Math.PI*2);return D.rotateY(Math.PI/2),D.rotateX(Math.PI/2),D}function x(){const y=new r.BufferGeometry;return y.setAttribute("position",new r.Float32BufferAttribute([0,0,0,1,1,1],3)),y}const m={X:[[new r.Mesh(E,A),[.5,0,0],[0,0,-Math.PI/2]],[new r.Mesh(E,A),[-.5,0,0],[0,0,Math.PI/2]],[new r.Mesh(d,A),[0,0,0],[0,0,-Math.PI/2]]],Y:[[new r.Mesh(E,o),[0,.5,0]],[new r.Mesh(E,o),[0,-.5,0],[Math.PI,0,0]],[new r.Mesh(d,o)]],Z:[[new r.Mesh(E,a),[0,0,.5],[Math.PI/2,0,0]],[new r.Mesh(E,a),[0,0,-.5],[-Math.PI/2,0,0]],[new r.Mesh(d,a),null,[Math.PI/2,0,0]]],XYZ:[[new r.Mesh(new r.OctahedronGeometry(.1,0),f.clone()),[0,0,0]]],XY:[[new r.Mesh(new r.BoxGeometry(.15,.15,.01),u.clone()),[.15,.15,0]]],YZ:[[new r.Mesh(new r.BoxGeometry(.15,.15,.01),l.clone()),[0,.15,.15],[0,Math.PI/2,0]]],XZ:[[new r.Mesh(new r.BoxGeometry(.15,.15,.01),g.clone()),[.15,0,.15],[-Math.PI/2,0,0]]]},S={X:[[new r.Mesh(new r.CylinderGeometry(.2,0,.6,4),n),[.3,0,0],[0,0,-Math.PI/2]],[new r.Mesh(new r.CylinderGeometry(.2,0,.6,4),n),[-.3,0,0],[0,0,Math.PI/2]]],Y:[[new r.Mesh(new r.CylinderGeometry(.2,0,.6,4),n),[0,.3,0]],[new r.Mesh(new r.CylinderGeometry(.2,0,.6,4),n),[0,-.3,0],[0,0,Math.PI]]],Z:[[new r.Mesh(new r.CylinderGeometry(.2,0,.6,4),n),[0,0,.3],[Math.PI/2,0,0]],[new r.Mesh(new r.CylinderGeometry(.2,0,.6,4),n),[0,0,-.3],[-Math.PI/2,0,0]]],XYZ:[[new r.Mesh(new r.OctahedronGeometry(.2,0),n)]],XY:[[new r.Mesh(new r.BoxGeometry(.2,.2,.01),n),[.15,.15,0]]],YZ:[[new r.Mesh(new r.BoxGeometry(.2,.2,.01),n),[0,.15,.15],[0,Math.PI/2,0]]],XZ:[[new r.Mesh(new r.BoxGeometry(.2,.2,.01),n),[.15,0,.15],[-Math.PI/2,0,0]]]},w={START:[[new r.Mesh(new r.OctahedronGeometry(.01,2),s),null,null,null,"helper"]],END:[[new r.Mesh(new r.OctahedronGeometry(.01,2),s),null,null,null,"helper"]],DELTA:[[new r.Line(x(),s),null,null,null,"helper"]],X:[[new r.Line(Q,s.clone()),[-1e3,0,0],null,[1e6,1,1],"helper"]],Y:[[new r.Line(Q,s.clone()),[0,-1e3,0],[0,0,Math.PI/2],[1e6,1,1],"helper"]],Z:[[new r.Line(Q,s.clone()),[0,0,-1e3],[0,-Math.PI/2,0],[1e6,1,1],"helper"]]},T={XYZE:[[new r.Mesh(C(.5,1),I),null,[0,Math.PI/2,0]]],X:[[new r.Mesh(C(.5,.5),A)]],Y:[[new r.Mesh(C(.5,.5),o),null,[0,0,-Math.PI/2]]],Z:[[new r.Mesh(C(.5,.5),a),null,[0,Math.PI/2,0]]],E:[[new r.Mesh(C(.75,1),h),null,[0,Math.PI/2,0]]]},M={AXIS:[[new r.Line(Q,s.clone()),[-1e3,0,0],null,[1e6,1,1],"helper"]]},R={XYZE:[[new r.Mesh(new r.SphereGeometry(.25,10,8),n)]],X:[[new r.Mesh(new r.TorusGeometry(.5,.1,4,24),n),[0,0,0],[0,-Math.PI/2,-Math.PI/2]]],Y:[[new r.Mesh(new r.TorusGeometry(.5,.1,4,24),n),[0,0,0],[Math.PI/2,0,0]]],Z:[[new r.Mesh(new r.TorusGeometry(.5,.1,4,24),n),[0,0,0],[0,0,-Math.PI/2]]],E:[[new r.Mesh(new r.TorusGeometry(.75,.1,2,24),n)]]},U={X:[[new r.Mesh(B,A),[.5,0,0],[0,0,-Math.PI/2]],[new r.Mesh(d,A),[0,0,0],[0,0,-Math.PI/2]],[new r.Mesh(B,A),[-.5,0,0],[0,0,Math.PI/2]]],Y:[[new r.Mesh(B,o),[0,.5,0]],[new r.Mesh(d,o)],[new r.Mesh(B,o),[0,-.5,0],[0,0,Math.PI]]],Z:[[new r.Mesh(B,a),[0,0,.5],[Math.PI/2,0,0]],[new r.Mesh(d,a),[0,0,0],[Math.PI/2,0,0]],[new r.Mesh(B,a),[0,0,-.5],[-Math.PI/2,0,0]]],XY:[[new r.Mesh(new r.BoxGeometry(.15,.15,.01),u),[.15,.15,0]]],YZ:[[new r.Mesh(new r.BoxGeometry(.15,.15,.01),l),[0,.15,.15],[0,Math.PI/2,0]]],XZ:[[new r.Mesh(new r.BoxGeometry(.15,.15,.01),g),[.15,0,.15],[-Math.PI/2,0,0]]],XYZ:[[new r.Mesh(new r.BoxGeometry(.1,.1,.1),f.clone())]]},V={X:[[new r.Mesh(new r.CylinderGeometry(.2,0,.6,4),n),[.3,0,0],[0,0,-Math.PI/2]],[new r.Mesh(new r.CylinderGeometry(.2,0,.6,4),n),[-.3,0,0],[0,0,Math.PI/2]]],Y:[[new r.Mesh(new r.CylinderGeometry(.2,0,.6,4),n),[0,.3,0]],[new r.Mesh(new r.CylinderGeometry(.2,0,.6,4),n),[0,-.3,0],[0,0,Math.PI]]],Z:[[new r.Mesh(new r.CylinderGeometry(.2,0,.6,4),n),[0,0,.3],[Math.PI/2,0,0]],[new r.Mesh(new r.CylinderGeometry(.2,0,.6,4),n),[0,0,-.3],[-Math.PI/2,0,0]]],XY:[[new r.Mesh(new r.BoxGeometry(.2,.2,.01),n),[.15,.15,0]]],YZ:[[new r.Mesh(new r.BoxGeometry(.2,.2,.01),n),[0,.15,.15],[0,Math.PI/2,0]]],XZ:[[new r.Mesh(new r.BoxGeometry(.2,.2,.01),n),[.15,0,.15],[-Math.PI/2,0,0]]],XYZ:[[new r.Mesh(new r.BoxGeometry(.2,.2,.2),n),[0,0,0]]]},O={X:[[new r.Line(Q,s.clone()),[-1e3,0,0],null,[1e6,1,1],"helper"]],Y:[[new r.Line(Q,s.clone()),[0,-1e3,0],[0,0,Math.PI/2],[1e6,1,1],"helper"]],Z:[[new r.Line(Q,s.clone()),[0,0,-1e3],[0,-Math.PI/2,0],[1e6,1,1],"helper"]]};e&&(delete T.XYZE,delete T.E,delete U.XY,delete U.XZ,delete U.YZ,delete U.XYZX,delete U.XYZY,delete U.XYZZ);function P(y){const L=new r.Object3D;for(const D in y)for(let k=y[D].length;k--;){const N=y[D][k][0].clone(),Y=y[D][k][1],K=y[D][k][2],G=y[D][k][3],b=y[D][k][4];N.name=D,N.tag=b,Y&&N.position.set(Y[0],Y[1],Y[2]),K&&N.rotation.set(K[0],K[1],K[2]),G&&N.scale.set(G[0],G[1],G[2]),N.updateMatrix();const F=N.geometry.clone();F.applyMatrix4(N.matrix),N.geometry=F,N.renderOrder=1/0,N.position.set(0,0,0),N.rotation.set(0,0,0),N.scale.set(1,1,1),L.add(N)}return L}this.gizmo={},this.picker={},this.helper={},this.add(this.gizmo.translate=P(m)),this.add(this.gizmo.rotate=P(T)),this.add(this.gizmo.scale=P(U)),this.add(this.picker.translate=P(S)),this.add(this.picker.rotate=P(R)),this.add(this.picker.scale=P(V)),this.add(this.helper.translate=P(w)),this.add(this.helper.rotate=P(M)),this.add(this.helper.scale=P(O)),this.picker.translate.visible=!1,this.picker.rotate.visible=!1,this.picker.scale.visible=!1}updateMatrixWorld(e){const i=(this.mode==="scale"?"local":this.space)==="local"?this.worldQuaternion:Je;this.gizmo.translate.visible=this.mode==="translate",this.gizmo.rotate.visible=this.mode==="rotate",this.gizmo.scale.visible=this.mode==="scale",this.helper.translate.visible=this.mode==="translate",this.helper.rotate.visible=this.mode==="rotate",this.helper.scale.visible=this.mode==="scale";let n=[];n=n.concat(this.picker[this.mode].children),n=n.concat(this.gizmo[this.mode].children),n=n.concat(this.helper[this.mode].children);for(let s=0;s<n.length;s++){const A=n[s];A.visible=!0,A.rotation.set(0,0,0),A.position.copy(this.worldPosition);let o;if(this.camera.isOrthographicCamera?o=(this.camera.top-this.camera.bottom)/this.camera.zoom:o=this.worldPosition.distanceTo(this.cameraPosition)*Math.min(1.9*Math.tan(Math.PI*this.camera.fov/360)/this.camera.zoom,7),A.scale.set(1,1,1).multiplyScalar(o*this.size/4),A.tag==="helper"){A.visible=!1,A.name==="AXIS"?(A.visible=!!this.axis,this.axis==="X"&&(W.setFromEuler(Ye.set(0,0,0)),A.quaternion.copy(i).multiply(W),Math.abs(z.copy(Ge).applyQuaternion(i).dot(this.eye))>.9&&(A.visible=!1)),this.axis==="Y"&&(W.setFromEuler(Ye.set(0,0,Math.PI/2)),A.quaternion.copy(i).multiply(W),Math.abs(z.copy(xe).applyQuaternion(i).dot(this.eye))>.9&&(A.visible=!1)),this.axis==="Z"&&(W.setFromEuler(Ye.set(0,Math.PI/2,0)),A.quaternion.copy(i).multiply(W),Math.abs(z.copy(Ue).applyQuaternion(i).dot(this.eye))>.9&&(A.visible=!1)),this.axis==="XYZE"&&(W.setFromEuler(Ye.set(0,Math.PI/2,0)),z.copy(this.rotationAxis),A.quaternion.setFromRotationMatrix(vn.lookAt(Mn,z,xe)),A.quaternion.multiply(W),A.visible=this.dragging),this.axis==="E"&&(A.visible=!1)):A.name==="START"?(A.position.copy(this.worldPositionStart),A.visible=this.dragging):A.name==="END"?(A.position.copy(this.worldPosition),A.visible=this.dragging):A.name==="DELTA"?(A.position.copy(this.worldPositionStart),A.quaternion.copy(this.worldQuaternionStart),Ae.set(1e-10,1e-10,1e-10).add(this.worldPositionStart).sub(this.worldPosition).multiplyScalar(-1),Ae.applyQuaternion(this.worldQuaternionStart.clone().invert()),A.scale.copy(Ae),A.visible=this.dragging):(A.quaternion.copy(i),this.dragging?A.position.copy(this.worldPositionStart):A.position.copy(this.worldPosition),this.axis&&(A.visible=this.axis.search(A.name)!==-1));continue}A.quaternion.copy(i),this.mode==="translate"||this.mode==="scale"?(A.name==="X"&&Math.abs(z.copy(Ge).applyQuaternion(i).dot(this.eye))>.99&&(A.scale.set(1e-10,1e-10,1e-10),A.visible=!1),A.name==="Y"&&Math.abs(z.copy(xe).applyQuaternion(i).dot(this.eye))>.99&&(A.scale.set(1e-10,1e-10,1e-10),A.visible=!1),A.name==="Z"&&Math.abs(z.copy(Ue).applyQuaternion(i).dot(this.eye))>.99&&(A.scale.set(1e-10,1e-10,1e-10),A.visible=!1),A.name==="XY"&&Math.abs(z.copy(Ue).applyQuaternion(i).dot(this.eye))<.2&&(A.scale.set(1e-10,1e-10,1e-10),A.visible=!1),A.name==="YZ"&&Math.abs(z.copy(Ge).applyQuaternion(i).dot(this.eye))<.2&&(A.scale.set(1e-10,1e-10,1e-10),A.visible=!1),A.name==="XZ"&&Math.abs(z.copy(xe).applyQuaternion(i).dot(this.eye))<.2&&(A.scale.set(1e-10,1e-10,1e-10),A.visible=!1)):this.mode==="rotate"&&(Ke.copy(i),z.copy(this.eye).applyQuaternion(W.copy(i).invert()),A.name.search("E")!==-1&&A.quaternion.setFromRotationMatrix(vn.lookAt(this.eye,Mn,xe)),A.name==="X"&&(W.setFromAxisAngle(Ge,Math.atan2(-z.y,z.z)),W.multiplyQuaternions(Ke,W),A.quaternion.copy(W)),A.name==="Y"&&(W.setFromAxisAngle(xe,Math.atan2(z.x,z.z)),W.multiplyQuaternions(Ke,W),A.quaternion.copy(W)),A.name==="Z"&&(W.setFromAxisAngle(Ue,Math.atan2(z.y,z.x)),W.multiplyQuaternions(Ke,W),A.quaternion.copy(W))),A.visible=A.visible&&(A.name.indexOf("X")===-1||this.showX),A.visible=A.visible&&(A.name.indexOf("Y")===-1||this.showY),A.visible=A.visible&&(A.name.indexOf("Z")===-1||this.showZ),A.visible=A.visible&&(A.name.indexOf("E")===-1||this.showX&&this.showY&&this.showZ),A.material._color=A.material._color||A.material.color.clone(),A.material._opacity=A.material._opacity||A.material.opacity,A.material.color.copy(A.material._color),A.material.opacity=A.material._opacity,this.enabled&&this.axis&&(A.name===this.axis||this.axis.split("").some(function(a){return A.name===a}))&&(A.material.color.setHex(16776960),A.material.opacity=1)}super.updateMatrixWorld(e)}}class Vs extends r.Mesh{constructor(){super(new r.PlaneGeometry(1e5,1e5,2,2),new r.MeshBasicMaterial({visible:!1,wireframe:!0,side:r.DoubleSide,transparent:!0,opacity:.1,toneMapped:!1})),this.isTransformControlsPlane=!0,this.type="TransformControlsPlane"}updateMatrixWorld(e){let t=this.space;switch(this.position.copy(this.worldPosition),this.mode==="scale"&&(t="local"),ze.copy(Ge).applyQuaternion(t==="local"?this.worldQuaternion:Je),ke.copy(xe).applyQuaternion(t==="local"?this.worldQuaternion:Je),Ne.copy(Ue).applyQuaternion(t==="local"?this.worldQuaternion:Je),z.copy(ke),this.mode){case"translate":case"scale":switch(this.axis){case"X":z.copy(this.eye).cross(ze),he.copy(ze).cross(z);break;case"Y":z.copy(this.eye).cross(ke),he.copy(ke).cross(z);break;case"Z":z.copy(this.eye).cross(Ne),he.copy(Ne).cross(z);break;case"XY":he.copy(Ne);break;case"YZ":he.copy(ze);break;case"XZ":z.copy(Ne),he.copy(ke);break;case"XYZ":case"E":he.set(0,0,0);break}break;case"rotate":default:he.set(0,0,0)}he.length()===0?this.quaternion.copy(this.cameraQuaternion):(Rn.lookAt(Ae.set(0,0,0),he,z),this.quaternion.setFromRotationMatrix(Rn)),super.updateMatrixWorld(e)}}const Dn={uniforms:{tDiffuse:{value:null},opacity:{value:1}},vertexShader:`

		varying vec2 vUv;

		void main() {

			vUv = uv;
			gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );

		}`,fragmentShader:`

		uniform float opacity;

		uniform sampler2D tDiffuse;

		varying vec2 vUv;

		void main() {

			gl_FragColor = texture2D( tDiffuse, vUv );
			gl_FragColor.a *= opacity;


		}`};class Ln extends ve{constructor(e,t){super(),this.textureID=t!==void 0?t:"tDiffuse",e instanceof r.ShaderMaterial?(this.uniforms=e.uniforms,this.material=e):e&&(this.uniforms=r.UniformsUtils.clone(e.uniforms),this.material=new r.ShaderMaterial({defines:Object.assign({},e.defines),uniforms:this.uniforms,vertexShader:e.vertexShader,fragmentShader:e.fragmentShader})),this.fsQuad=new Mt(this.material)}render(e,t,i){this.uniforms[this.textureID]&&(this.uniforms[this.textureID].value=i.texture),this.fsQuad.material=this.material,this.renderToScreen?(e.setRenderTarget(null),this.fsQuad.render(e)):(e.setRenderTarget(t),this.clear&&e.clear(e.autoClearColor,e.autoClearDepth,e.autoClearStencil),this.fsQuad.render(e))}dispose(){this.material.dispose(),this.fsQuad.dispose()}}class Fn extends ve{constructor(e,t){super(),this.scene=e,this.camera=t,this.clear=!0,this.needsSwap=!1,this.inverse=!1}render(e,t,i){const n=e.getContext(),s=e.state;s.buffers.color.setMask(!1),s.buffers.depth.setMask(!1),s.buffers.color.setLocked(!0),s.buffers.depth.setLocked(!0);let A,o;this.inverse?(A=0,o=1):(A=1,o=0),s.buffers.stencil.setTest(!0),s.buffers.stencil.setOp(n.REPLACE,n.REPLACE,n.REPLACE),s.buffers.stencil.setFunc(n.ALWAYS,A,4294967295),s.buffers.stencil.setClear(o),s.buffers.stencil.setLocked(!0),e.setRenderTarget(i),this.clear&&e.clear(),e.render(this.scene,this.camera),e.setRenderTarget(t),this.clear&&e.clear(),e.render(this.scene,this.camera),s.buffers.color.setLocked(!1),s.buffers.depth.setLocked(!1),s.buffers.stencil.setLocked(!1),s.buffers.stencil.setFunc(n.EQUAL,1,4294967295),s.buffers.stencil.setOp(n.KEEP,n.KEEP,n.KEEP),s.buffers.stencil.setLocked(!0)}}class qs extends ve{constructor(){super(),this.needsSwap=!1}render(e){e.state.buffers.stencil.setLocked(!1),e.state.buffers.stencil.setTest(!1)}}class Ys{constructor(e,t){if(this.renderer=e,this._pixelRatio=e.getPixelRatio(),t===void 0){const i=e.getSize(new r.Vector2);this._width=i.width,this._height=i.height,t=new r.WebGLRenderTarget(this._width*this._pixelRatio,this._height*this._pixelRatio),t.texture.name="EffectComposer.rt1"}else this._width=t.width,this._height=t.height;this.renderTarget1=t,this.renderTarget2=t.clone(),this.renderTarget2.texture.name="EffectComposer.rt2",this.writeBuffer=this.renderTarget1,this.readBuffer=this.renderTarget2,this.renderToScreen=!0,this.passes=[],this.copyPass=new Ln(Dn),this.clock=new r.Clock}swapBuffers(){const e=this.readBuffer;this.readBuffer=this.writeBuffer,this.writeBuffer=e}addPass(e){this.passes.push(e),e.setSize(this._width*this._pixelRatio,this._height*this._pixelRatio)}insertPass(e,t){this.passes.splice(t,0,e),e.setSize(this._width*this._pixelRatio,this._height*this._pixelRatio)}removePass(e){const t=this.passes.indexOf(e);t!==-1&&this.passes.splice(t,1)}isLastEnabledPass(e){for(let t=e+1;t<this.passes.length;t++)if(this.passes[t].enabled)return!1;return!0}render(e){e===void 0&&(e=this.clock.getDelta());const t=this.renderer.getRenderTarget();let i=!1;for(let n=0,s=this.passes.length;n<s;n++){const A=this.passes[n];if(A.enabled!==!1){if(A.renderToScreen=this.renderToScreen&&this.isLastEnabledPass(n),A.render(this.renderer,this.writeBuffer,this.readBuffer,e,i),A.needsSwap){if(i){const o=this.renderer.getContext(),a=this.renderer.state.buffers.stencil;a.setFunc(o.NOTEQUAL,1,4294967295),this.copyPass.render(this.renderer,this.writeBuffer,this.readBuffer,e),a.setFunc(o.EQUAL,1,4294967295)}this.swapBuffers()}Fn!==void 0&&(A instanceof Fn?i=!0:A instanceof qs&&(i=!1))}}this.renderer.setRenderTarget(t)}reset(e){if(e===void 0){const t=this.renderer.getSize(new r.Vector2);this._pixelRatio=this.renderer.getPixelRatio(),this._width=t.width,this._height=t.height,e=this.renderTarget1.clone(),e.setSize(this._width*this._pixelRatio,this._height*this._pixelRatio)}this.renderTarget1.dispose(),this.renderTarget2.dispose(),this.renderTarget1=e,this.renderTarget2=e.clone(),this.writeBuffer=this.renderTarget1,this.readBuffer=this.renderTarget2}setSize(e,t){this._width=e,this._height=t;const i=this._width*this._pixelRatio,n=this._height*this._pixelRatio;this.renderTarget1.setSize(i,n),this.renderTarget2.setSize(i,n);for(let s=0;s<this.passes.length;s++)this.passes[s].setSize(i,n)}setPixelRatio(e){this._pixelRatio=e,this.setSize(this._width,this._height)}dispose(){this.renderTarget1.dispose(),this.renderTarget2.dispose(),this.copyPass.dispose()}}class Ks extends ve{constructor(e,t,i,n,s){super(),this.scene=e,this.camera=t,this.overrideMaterial=i,this.clearColor=n,this.clearAlpha=s!==void 0?s:0,this.clear=!0,this.clearDepth=!1,this.needsSwap=!1,this._oldClearColor=new r.Color}render(e,t,i){const n=e.autoClear;e.autoClear=!1;let s,A;this.overrideMaterial!==void 0&&(A=this.scene.overrideMaterial,this.scene.overrideMaterial=this.overrideMaterial),this.clearColor&&(e.getClearColor(this._oldClearColor),s=e.getClearAlpha(),e.setClearColor(this.clearColor,this.clearAlpha)),this.clearDepth&&e.clearDepth(),e.setRenderTarget(this.renderToScreen?null:i),this.clear&&e.clear(e.autoClearColor,e.autoClearDepth,e.autoClearStencil),e.render(this.scene,this.camera),this.clearColor&&e.setClearColor(this._oldClearColor,s),this.overrideMaterial!==void 0&&(this.scene.overrideMaterial=A),e.autoClear=n}}class Qe extends ve{constructor(e,t,i,n){super(),this.renderScene=t,this.renderCamera=i,this.selectedObjects=n!==void 0?n:[],this.visibleEdgeColor=new r.Color(1,1,1),this.hiddenEdgeColor=new r.Color(.1,.04,.02),this.edgeGlow=0,this.usePatternTexture=!1,this.edgeThickness=1,this.edgeStrength=3,this.downSampleRatio=2,this.pulsePeriod=0,this._visibilityCache=new Map,this.resolution=e!==void 0?new r.Vector2(e.x,e.y):new r.Vector2(256,256);const s=Math.round(this.resolution.x/this.downSampleRatio),A=Math.round(this.resolution.y/this.downSampleRatio);this.renderTargetMaskBuffer=new r.WebGLRenderTarget(this.resolution.x,this.resolution.y),this.renderTargetMaskBuffer.texture.name="OutlinePass.mask",this.renderTargetMaskBuffer.texture.generateMipmaps=!1,this.depthMaterial=new r.MeshDepthMaterial,this.depthMaterial.side=r.DoubleSide,this.depthMaterial.depthPacking=r.RGBADepthPacking,this.depthMaterial.blending=r.NoBlending,this.prepareMaskMaterial=this.getPrepareMaskMaterial(),this.prepareMaskMaterial.side=r.DoubleSide,this.prepareMaskMaterial.fragmentShader=g(this.prepareMaskMaterial.fragmentShader,this.renderCamera),this.renderTargetDepthBuffer=new r.WebGLRenderTarget(this.resolution.x,this.resolution.y),this.renderTargetDepthBuffer.texture.name="OutlinePass.depth",this.renderTargetDepthBuffer.texture.generateMipmaps=!1,this.renderTargetMaskDownSampleBuffer=new r.WebGLRenderTarget(s,A),this.renderTargetMaskDownSampleBuffer.texture.name="OutlinePass.depthDownSample",this.renderTargetMaskDownSampleBuffer.texture.generateMipmaps=!1,this.renderTargetBlurBuffer1=new r.WebGLRenderTarget(s,A),this.renderTargetBlurBuffer1.texture.name="OutlinePass.blur1",this.renderTargetBlurBuffer1.texture.generateMipmaps=!1,this.renderTargetBlurBuffer2=new r.WebGLRenderTarget(Math.round(s/2),Math.round(A/2)),this.renderTargetBlurBuffer2.texture.name="OutlinePass.blur2",this.renderTargetBlurBuffer2.texture.generateMipmaps=!1,this.edgeDetectionMaterial=this.getEdgeDetectionMaterial(),this.renderTargetEdgeBuffer1=new r.WebGLRenderTarget(s,A),this.renderTargetEdgeBuffer1.texture.name="OutlinePass.edge1",this.renderTargetEdgeBuffer1.texture.generateMipmaps=!1,this.renderTargetEdgeBuffer2=new r.WebGLRenderTarget(Math.round(s/2),Math.round(A/2)),this.renderTargetEdgeBuffer2.texture.name="OutlinePass.edge2",this.renderTargetEdgeBuffer2.texture.generateMipmaps=!1;const o=4,a=4;this.separableBlurMaterial1=this.getSeperableBlurMaterial(o),this.separableBlurMaterial1.uniforms.texSize.value.set(s,A),this.separableBlurMaterial1.uniforms.kernelRadius.value=1,this.separableBlurMaterial2=this.getSeperableBlurMaterial(a),this.separableBlurMaterial2.uniforms.texSize.value.set(Math.round(s/2),Math.round(A/2)),this.separableBlurMaterial2.uniforms.kernelRadius.value=a,this.overlayMaterial=this.getOverlayMaterial();const l=Dn;this.copyUniforms=r.UniformsUtils.clone(l.uniforms),this.copyUniforms.opacity.value=1,this.materialCopy=new r.ShaderMaterial({uniforms:this.copyUniforms,vertexShader:l.vertexShader,fragmentShader:l.fragmentShader,blending:r.NoBlending,depthTest:!1,depthWrite:!1,transparent:!0}),this.enabled=!0,this.needsSwap=!1,this._oldClearColor=new r.Color,this.oldClearAlpha=1,this.fsQuad=new Mt(null),this.tempPulseColor1=new r.Color,this.tempPulseColor2=new r.Color,this.textureMatrix=new r.Matrix4;function g(u,f){const h=f.isPerspectiveCamera?"perspective":"orthographic";return u.replace(/DEPTH_TO_VIEW_Z/g,h+"DepthToViewZ")}}dispose(){this.renderTargetMaskBuffer.dispose(),this.renderTargetDepthBuffer.dispose(),this.renderTargetMaskDownSampleBuffer.dispose(),this.renderTargetBlurBuffer1.dispose(),this.renderTargetBlurBuffer2.dispose(),this.renderTargetEdgeBuffer1.dispose(),this.renderTargetEdgeBuffer2.dispose(),this.depthMaterial.dispose(),this.prepareMaskMaterial.dispose(),this.edgeDetectionMaterial.dispose(),this.separableBlurMaterial1.dispose(),this.separableBlurMaterial2.dispose(),this.overlayMaterial.dispose(),this.materialCopy.dispose(),this.fsQuad.dispose()}setSize(e,t){this.renderTargetMaskBuffer.setSize(e,t),this.renderTargetDepthBuffer.setSize(e,t);let i=Math.round(e/this.downSampleRatio),n=Math.round(t/this.downSampleRatio);this.renderTargetMaskDownSampleBuffer.setSize(i,n),this.renderTargetBlurBuffer1.setSize(i,n),this.renderTargetEdgeBuffer1.setSize(i,n),this.separableBlurMaterial1.uniforms.texSize.value.set(i,n),i=Math.round(i/2),n=Math.round(n/2),this.renderTargetBlurBuffer2.setSize(i,n),this.renderTargetEdgeBuffer2.setSize(i,n),this.separableBlurMaterial2.uniforms.texSize.value.set(i,n)}changeVisibilityOfSelectedObjects(e){const t=this._visibilityCache;function i(n){n.isMesh&&(e===!0?n.visible=t.get(n):(t.set(n,n.visible),n.visible=e))}for(let n=0;n<this.selectedObjects.length;n++)this.selectedObjects[n].traverse(i)}changeVisibilityOfNonSelectedObjects(e){const t=this._visibilityCache,i=[];function n(A){A.isMesh&&i.push(A)}for(let A=0;A<this.selectedObjects.length;A++)this.selectedObjects[A].traverse(n);function s(A){if(A.isMesh||A.isSprite){let o=!1;for(let a=0;a<i.length;a++)if(i[a].id===A.id){o=!0;break}if(o===!1){const a=A.visible;(e===!1||t.get(A)===!0)&&(A.visible=e),t.set(A,a)}}else(A.isPoints||A.isLine)&&(e===!0?A.visible=t.get(A):(t.set(A,A.visible),A.visible=e))}this.renderScene.traverse(s)}updateTextureMatrix(){this.textureMatrix.set(.5,0,0,.5,0,.5,0,.5,0,0,.5,.5,0,0,0,1),this.textureMatrix.multiply(this.renderCamera.projectionMatrix),this.textureMatrix.multiply(this.renderCamera.matrixWorldInverse)}render(e,t,i,n,s){if(this.selectedObjects.length>0){e.getClearColor(this._oldClearColor),this.oldClearAlpha=e.getClearAlpha();const A=e.autoClear;e.autoClear=!1,s&&e.state.buffers.stencil.setTest(!1),e.setClearColor(16777215,1),this.changeVisibilityOfSelectedObjects(!1);const o=this.renderScene.background;if(this.renderScene.background=null,this.renderScene.overrideMaterial=this.depthMaterial,e.setRenderTarget(this.renderTargetDepthBuffer),e.clear(),e.render(this.renderScene,this.renderCamera),this.changeVisibilityOfSelectedObjects(!0),this._visibilityCache.clear(),this.updateTextureMatrix(),this.changeVisibilityOfNonSelectedObjects(!1),this.renderScene.overrideMaterial=this.prepareMaskMaterial,this.prepareMaskMaterial.uniforms.cameraNearFar.value.set(this.renderCamera.near,this.renderCamera.far),this.prepareMaskMaterial.uniforms.depthTexture.value=this.renderTargetDepthBuffer.texture,this.prepareMaskMaterial.uniforms.textureMatrix.value=this.textureMatrix,e.setRenderTarget(this.renderTargetMaskBuffer),e.clear(),e.render(this.renderScene,this.renderCamera),this.renderScene.overrideMaterial=null,this.changeVisibilityOfNonSelectedObjects(!0),this._visibilityCache.clear(),this.renderScene.background=o,this.fsQuad.material=this.materialCopy,this.copyUniforms.tDiffuse.value=this.renderTargetMaskBuffer.texture,e.setRenderTarget(this.renderTargetMaskDownSampleBuffer),e.clear(),this.fsQuad.render(e),this.tempPulseColor1.copy(this.visibleEdgeColor),this.tempPulseColor2.copy(this.hiddenEdgeColor),this.pulsePeriod>0){const a=.625+Math.cos(performance.now()*.01/this.pulsePeriod)*.75/2;this.tempPulseColor1.multiplyScalar(a),this.tempPulseColor2.multiplyScalar(a)}this.fsQuad.material=this.edgeDetectionMaterial,this.edgeDetectionMaterial.uniforms.maskTexture.value=this.renderTargetMaskDownSampleBuffer.texture,this.edgeDetectionMaterial.uniforms.texSize.value.set(this.renderTargetMaskDownSampleBuffer.width,this.renderTargetMaskDownSampleBuffer.height),this.edgeDetectionMaterial.uniforms.visibleEdgeColor.value=this.tempPulseColor1,this.edgeDetectionMaterial.uniforms.hiddenEdgeColor.value=this.tempPulseColor2,e.setRenderTarget(this.renderTargetEdgeBuffer1),e.clear(),this.fsQuad.render(e),this.fsQuad.material=this.separableBlurMaterial1,this.separableBlurMaterial1.uniforms.colorTexture.value=this.renderTargetEdgeBuffer1.texture,this.separableBlurMaterial1.uniforms.direction.value=Qe.BlurDirectionX,this.separableBlurMaterial1.uniforms.kernelRadius.value=this.edgeThickness,e.setRenderTarget(this.renderTargetBlurBuffer1),e.clear(),this.fsQuad.render(e),this.separableBlurMaterial1.uniforms.colorTexture.value=this.renderTargetBlurBuffer1.texture,this.separableBlurMaterial1.uniforms.direction.value=Qe.BlurDirectionY,e.setRenderTarget(this.renderTargetEdgeBuffer1),e.clear(),this.fsQuad.render(e),this.fsQuad.material=this.separableBlurMaterial2,this.separableBlurMaterial2.uniforms.colorTexture.value=this.renderTargetEdgeBuffer1.texture,this.separableBlurMaterial2.uniforms.direction.value=Qe.BlurDirectionX,e.setRenderTarget(this.renderTargetBlurBuffer2),e.clear(),this.fsQuad.render(e),this.separableBlurMaterial2.uniforms.colorTexture.value=this.renderTargetBlurBuffer2.texture,this.separableBlurMaterial2.uniforms.direction.value=Qe.BlurDirectionY,e.setRenderTarget(this.renderTargetEdgeBuffer2),e.clear(),this.fsQuad.render(e),this.fsQuad.material=this.overlayMaterial,this.overlayMaterial.uniforms.maskTexture.value=this.renderTargetMaskBuffer.texture,this.overlayMaterial.uniforms.edgeTexture1.value=this.renderTargetEdgeBuffer1.texture,this.overlayMaterial.uniforms.edgeTexture2.value=this.renderTargetEdgeBuffer2.texture,this.overlayMaterial.uniforms.patternTexture.value=this.patternTexture,this.overlayMaterial.uniforms.edgeStrength.value=this.edgeStrength,this.overlayMaterial.uniforms.edgeGlow.value=this.edgeGlow,this.overlayMaterial.uniforms.usePatternTexture.value=this.usePatternTexture,s&&e.state.buffers.stencil.setTest(!0),e.setRenderTarget(i),this.fsQuad.render(e),e.setClearColor(this._oldClearColor,this.oldClearAlpha),e.autoClear=A}this.renderToScreen&&(this.fsQuad.material=this.materialCopy,this.copyUniforms.tDiffuse.value=i.texture,e.setRenderTarget(null),this.fsQuad.render(e))}getPrepareMaskMaterial(){return new r.ShaderMaterial({uniforms:{depthTexture:{value:null},cameraNearFar:{value:new r.Vector2(.5,.5)},textureMatrix:{value:null}},vertexShader:`#include <morphtarget_pars_vertex>
				#include <skinning_pars_vertex>

				varying vec4 projTexCoord;
				varying vec4 vPosition;
				uniform mat4 textureMatrix;

				void main() {

					#include <skinbase_vertex>
					#include <begin_vertex>
					#include <morphtarget_vertex>
					#include <skinning_vertex>
					#include <project_vertex>

					vPosition = mvPosition;

					vec4 worldPosition = vec4( transformed, 1.0 );

					#ifdef USE_INSTANCING

						worldPosition = instanceMatrix * worldPosition;

					#endif
					
					worldPosition = modelMatrix * worldPosition;

					projTexCoord = textureMatrix * worldPosition;

				}`,fragmentShader:`#include <packing>
				varying vec4 vPosition;
				varying vec4 projTexCoord;
				uniform sampler2D depthTexture;
				uniform vec2 cameraNearFar;

				void main() {

					float depth = unpackRGBAToDepth(texture2DProj( depthTexture, projTexCoord ));
					float viewZ = - DEPTH_TO_VIEW_Z( depth, cameraNearFar.x, cameraNearFar.y );
					float depthTest = (-vPosition.z > viewZ) ? 1.0 : 0.0;
					gl_FragColor = vec4(0.0, depthTest, 1.0, 1.0);

				}`})}getEdgeDetectionMaterial(){return new r.ShaderMaterial({uniforms:{maskTexture:{value:null},texSize:{value:new r.Vector2(.5,.5)},visibleEdgeColor:{value:new r.Vector3(1,1,1)},hiddenEdgeColor:{value:new r.Vector3(1,1,1)}},vertexShader:`varying vec2 vUv;

				void main() {
					vUv = uv;
					gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );
				}`,fragmentShader:`varying vec2 vUv;

				uniform sampler2D maskTexture;
				uniform vec2 texSize;
				uniform vec3 visibleEdgeColor;
				uniform vec3 hiddenEdgeColor;

				void main() {
					vec2 invSize = 1.0 / texSize;
					vec4 uvOffset = vec4(1.0, 0.0, 0.0, 1.0) * vec4(invSize, invSize);
					vec4 c1 = texture2D( maskTexture, vUv + uvOffset.xy);
					vec4 c2 = texture2D( maskTexture, vUv - uvOffset.xy);
					vec4 c3 = texture2D( maskTexture, vUv + uvOffset.yw);
					vec4 c4 = texture2D( maskTexture, vUv - uvOffset.yw);
					float diff1 = (c1.r - c2.r)*0.5;
					float diff2 = (c3.r - c4.r)*0.5;
					float d = length( vec2(diff1, diff2) );
					float a1 = min(c1.g, c2.g);
					float a2 = min(c3.g, c4.g);
					float visibilityFactor = min(a1, a2);
					vec3 edgeColor = 1.0 - visibilityFactor > 0.001 ? visibleEdgeColor : hiddenEdgeColor;
					gl_FragColor = vec4(edgeColor, 1.0) * vec4(d);
				}`})}getSeperableBlurMaterial(e){return new r.ShaderMaterial({defines:{MAX_RADIUS:e},uniforms:{colorTexture:{value:null},texSize:{value:new r.Vector2(.5,.5)},direction:{value:new r.Vector2(.5,.5)},kernelRadius:{value:1}},vertexShader:`varying vec2 vUv;

				void main() {
					vUv = uv;
					gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );
				}`,fragmentShader:`#include <common>
				varying vec2 vUv;
				uniform sampler2D colorTexture;
				uniform vec2 texSize;
				uniform vec2 direction;
				uniform float kernelRadius;

				float gaussianPdf(in float x, in float sigma) {
					return 0.39894 * exp( -0.5 * x * x/( sigma * sigma))/sigma;
				}

				void main() {
					vec2 invSize = 1.0 / texSize;
					float sigma = kernelRadius/2.0;
					float weightSum = gaussianPdf(0.0, sigma);
					vec4 diffuseSum = texture2D( colorTexture, vUv) * weightSum;
					vec2 delta = direction * invSize * kernelRadius/float(MAX_RADIUS);
					vec2 uvOffset = delta;
					for( int i = 1; i <= MAX_RADIUS; i ++ ) {
						float x = kernelRadius * float(i) / float(MAX_RADIUS);
						float w = gaussianPdf(x, sigma);
						vec4 sample1 = texture2D( colorTexture, vUv + uvOffset);
						vec4 sample2 = texture2D( colorTexture, vUv - uvOffset);
						diffuseSum += ((sample1 + sample2) * w);
						weightSum += (2.0 * w);
						uvOffset += delta;
					}
					gl_FragColor = diffuseSum/weightSum;
				}`})}getOverlayMaterial(){return new r.ShaderMaterial({uniforms:{maskTexture:{value:null},edgeTexture1:{value:null},edgeTexture2:{value:null},patternTexture:{value:null},edgeStrength:{value:1},edgeGlow:{value:1},usePatternTexture:{value:0}},vertexShader:`varying vec2 vUv;

				void main() {
					vUv = uv;
					gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );
				}`,fragmentShader:`varying vec2 vUv;

				uniform sampler2D maskTexture;
				uniform sampler2D edgeTexture1;
				uniform sampler2D edgeTexture2;
				uniform sampler2D patternTexture;
				uniform float edgeStrength;
				uniform float edgeGlow;
				uniform bool usePatternTexture;

				void main() {
					vec4 edgeValue1 = texture2D(edgeTexture1, vUv);
					vec4 edgeValue2 = texture2D(edgeTexture2, vUv);
					vec4 maskColor = texture2D(maskTexture, vUv);
					vec4 patternColor = texture2D(patternTexture, 6.0 * vUv);
					float visibilityFactor = 1.0 - maskColor.g > 0.0 ? 1.0 : 0.5;
					vec4 edgeValue = edgeValue1 + edgeValue2 * edgeGlow;
					vec4 finalColor = edgeStrength * maskColor.r * edgeValue;
					if(usePatternTexture)
						finalColor += + visibilityFactor * (1.0 - maskColor.r) * (1.0 - patternColor.r);
					gl_FragColor = finalColor;
				}`,blending:r.AdditiveBlending,depthTest:!1,depthWrite:!1,transparent:!0})}}Qe.BlurDirectionX=new r.Vector2(1,0),Qe.BlurDirectionY=new r.Vector2(0,1);const Js={uniforms:{tDiffuse:{value:null},resolution:{value:new r.Vector2(1/1024,1/512)}},vertexShader:`

		varying vec2 vUv;

		void main() {

			vUv = uv;
			gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );

		}`,fragmentShader:`
	precision highp float;

	uniform sampler2D tDiffuse;

	uniform vec2 resolution;

	varying vec2 vUv;

	// FXAA 3.11 implementation by NVIDIA, ported to WebGL by Agost Biro (<EMAIL>)

	//----------------------------------------------------------------------------------
	// File:        es3-keplerFXAAassetsshaders/FXAA_DefaultES.frag
	// SDK Version: v3.00
	// Email:       <EMAIL>
	// Site:        http://developer.nvidia.com/
	//
	// Copyright (c) 2014-2015, NVIDIA CORPORATION. All rights reserved.
	//
	// Redistribution and use in source and binary forms, with or without
	// modification, are permitted provided that the following conditions
	// are met:
	//  * Redistributions of source code must retain the above copyright
	//    notice, this list of conditions and the following disclaimer.
	//  * Redistributions in binary form must reproduce the above copyright
	//    notice, this list of conditions and the following disclaimer in the
	//    documentation and/or other materials provided with the distribution.
	//  * Neither the name of NVIDIA CORPORATION nor the names of its
	//    contributors may be used to endorse or promote products derived
	//    from this software without specific prior written permission.
	//
	// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS ''AS IS'' AND ANY
	// EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
	// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
	// PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR
	// CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
	// EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
	// PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
	// PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
	// OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
	// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
	// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
	//
	//----------------------------------------------------------------------------------

	#ifndef FXAA_DISCARD
			//
			// Only valid for PC OpenGL currently.
			// Probably will not work when FXAA_GREEN_AS_LUMA = 1.
			//
			// 1 = Use discard on pixels which don't need AA.
			//     For APIs which enable concurrent TEX+ROP from same surface.
			// 0 = Return unchanged color on pixels which don't need AA.
			//
			#define FXAA_DISCARD 0
	#endif

	/*--------------------------------------------------------------------------*/
	#define FxaaTexTop(t, p) texture2D(t, p, -100.0)
	#define FxaaTexOff(t, p, o, r) texture2D(t, p + (o * r), -100.0)
	/*--------------------------------------------------------------------------*/

	#define NUM_SAMPLES 5

	// assumes colors have premultipliedAlpha, so that the calculated color contrast is scaled by alpha
	float contrast( vec4 a, vec4 b ) {
			vec4 diff = abs( a - b );
			return max( max( max( diff.r, diff.g ), diff.b ), diff.a );
	}

	/*============================================================================

									FXAA3 QUALITY - PC

	============================================================================*/

	/*--------------------------------------------------------------------------*/
	vec4 FxaaPixelShader(
			vec2 posM,
			sampler2D tex,
			vec2 fxaaQualityRcpFrame,
			float fxaaQualityEdgeThreshold,
			float fxaaQualityinvEdgeThreshold
	) {
			vec4 rgbaM = FxaaTexTop(tex, posM);
			vec4 rgbaS = FxaaTexOff(tex, posM, vec2( 0.0, 1.0), fxaaQualityRcpFrame.xy);
			vec4 rgbaE = FxaaTexOff(tex, posM, vec2( 1.0, 0.0), fxaaQualityRcpFrame.xy);
			vec4 rgbaN = FxaaTexOff(tex, posM, vec2( 0.0,-1.0), fxaaQualityRcpFrame.xy);
			vec4 rgbaW = FxaaTexOff(tex, posM, vec2(-1.0, 0.0), fxaaQualityRcpFrame.xy);
			// . S .
			// W M E
			// . N .

			bool earlyExit = max( max( max(
					contrast( rgbaM, rgbaN ),
					contrast( rgbaM, rgbaS ) ),
					contrast( rgbaM, rgbaE ) ),
					contrast( rgbaM, rgbaW ) )
					< fxaaQualityEdgeThreshold;
			// . 0 .
			// 0 0 0
			// . 0 .

			#if (FXAA_DISCARD == 1)
					if(earlyExit) FxaaDiscard;
			#else
					if(earlyExit) return rgbaM;
			#endif

			float contrastN = contrast( rgbaM, rgbaN );
			float contrastS = contrast( rgbaM, rgbaS );
			float contrastE = contrast( rgbaM, rgbaE );
			float contrastW = contrast( rgbaM, rgbaW );

			float relativeVContrast = ( contrastN + contrastS ) - ( contrastE + contrastW );
			relativeVContrast *= fxaaQualityinvEdgeThreshold;

			bool horzSpan = relativeVContrast > 0.;
			// . 1 .
			// 0 0 0
			// . 1 .

			// 45 deg edge detection and corners of objects, aka V/H contrast is too similar
			if( abs( relativeVContrast ) < .3 ) {
					// locate the edge
					vec2 dirToEdge;
					dirToEdge.x = contrastE > contrastW ? 1. : -1.;
					dirToEdge.y = contrastS > contrastN ? 1. : -1.;
					// . 2 .      . 1 .
					// 1 0 2  ~=  0 0 1
					// . 1 .      . 0 .

					// tap 2 pixels and see which ones are "outside" the edge, to
					// determine if the edge is vertical or horizontal

					vec4 rgbaAlongH = FxaaTexOff(tex, posM, vec2( dirToEdge.x, -dirToEdge.y ), fxaaQualityRcpFrame.xy);
					float matchAlongH = contrast( rgbaM, rgbaAlongH );
					// . 1 .
					// 0 0 1
					// . 0 H

					vec4 rgbaAlongV = FxaaTexOff(tex, posM, vec2( -dirToEdge.x, dirToEdge.y ), fxaaQualityRcpFrame.xy);
					float matchAlongV = contrast( rgbaM, rgbaAlongV );
					// V 1 .
					// 0 0 1
					// . 0 .

					relativeVContrast = matchAlongV - matchAlongH;
					relativeVContrast *= fxaaQualityinvEdgeThreshold;

					if( abs( relativeVContrast ) < .3 ) { // 45 deg edge
							// 1 1 .
							// 0 0 1
							// . 0 1

							// do a simple blur
							return mix(
									rgbaM,
									(rgbaN + rgbaS + rgbaE + rgbaW) * .25,
									.4
							);
					}

					horzSpan = relativeVContrast > 0.;
			}

			if(!horzSpan) rgbaN = rgbaW;
			if(!horzSpan) rgbaS = rgbaE;
			// . 0 .      1
			// 1 0 1  ->  0
			// . 0 .      1

			bool pairN = contrast( rgbaM, rgbaN ) > contrast( rgbaM, rgbaS );
			if(!pairN) rgbaN = rgbaS;

			vec2 offNP;
			offNP.x = (!horzSpan) ? 0.0 : fxaaQualityRcpFrame.x;
			offNP.y = ( horzSpan) ? 0.0 : fxaaQualityRcpFrame.y;

			bool doneN = false;
			bool doneP = false;

			float nDist = 0.;
			float pDist = 0.;

			vec2 posN = posM;
			vec2 posP = posM;

			int iterationsUsed = 0;
			int iterationsUsedN = 0;
			int iterationsUsedP = 0;
			for( int i = 0; i < NUM_SAMPLES; i++ ) {
					iterationsUsed = i;

					float increment = float(i + 1);

					if(!doneN) {
							nDist += increment;
							posN = posM + offNP * nDist;
							vec4 rgbaEndN = FxaaTexTop(tex, posN.xy);
							doneN = contrast( rgbaEndN, rgbaM ) > contrast( rgbaEndN, rgbaN );
							iterationsUsedN = i;
					}

					if(!doneP) {
							pDist += increment;
							posP = posM - offNP * pDist;
							vec4 rgbaEndP = FxaaTexTop(tex, posP.xy);
							doneP = contrast( rgbaEndP, rgbaM ) > contrast( rgbaEndP, rgbaN );
							iterationsUsedP = i;
					}

					if(doneN || doneP) break;
			}


			if ( !doneP && !doneN ) return rgbaM; // failed to find end of edge

			float dist = min(
					doneN ? float( iterationsUsedN ) / float( NUM_SAMPLES - 1 ) : 1.,
					doneP ? float( iterationsUsedP ) / float( NUM_SAMPLES - 1 ) : 1.
			);

			// hacky way of reduces blurriness of mostly diagonal edges
			// but reduces AA quality
			dist = pow(dist, .5);

			dist = 1. - dist;

			return mix(
					rgbaM,
					rgbaN,
					dist * .5
			);
	}

	void main() {
			const float edgeDetectionQuality = .2;
			const float invEdgeDetectionQuality = 1. / edgeDetectionQuality;

			gl_FragColor = FxaaPixelShader(
					vUv,
					tDiffuse,
					resolution,
					edgeDetectionQuality, // [0,1] contrast needed, otherwise early discard
					invEdgeDetectionQuality
			);

	}
	`};Z.DRACOLoader=Bi,Z.EffectComposer=Ys,Z.FBXLoader=es,Z.FXAAShader=Js,Z.GLTFExporter=mt,Z.GLTFLoader=On,Z.KTX2Loader=le,Z.Line2=ps,Z.LineGeometry=pn,Z.LineMaterial=Et,Z.OutlinePass=Qe,Z.PMREMCubeUVPacker=Nn,Z.PMREMGenerator=kn,Z.RGBELoader=Gn,Z.RenderPass=Ks,Z.RoomEnvironment=Pn,Z.ShaderPass=Ln,Z.Sky=Pe,Z.TGALoader=gs,Z.TransformControls=Gs,Z.mergeGeometries=bn});
