mvDefine(["exports","./three","./mapbox"],function(G,m,Mi){"use strict";var Fe={exports:{}},Ut=typeof Reflect=="object"?Reflect:null,xi=Ut&&typeof Ut.apply=="function"?Ut.apply:function(e,i,s){return Function.prototype.apply.call(e,i,s)},ge;Ut&&typeof Ut.ownKeys=="function"?ge=Ut.ownKeys:Object.getOwnPropertySymbols?ge=function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:ge=function(e){return Object.getOwnPropertyNames(e)};function pr(t){console&&console.warn&&console.warn(t)}var _i=Number.isNaN||function(e){return e!==e};function k(){k.init.call(this)}Fe.exports=k,Fe.exports.once=_r,k.EventEmitter=k,k.prototype._events=void 0,k.prototype._eventsCount=0,k.prototype._maxListeners=void 0;var Ei=10;function pe(t){if(typeof t!="function")throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof t)}Object.defineProperty(k,"defaultMaxListeners",{enumerable:!0,get:function(){return Ei},set:function(t){if(typeof t!="number"||t<0||_i(t))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+t+".");Ei=t}}),k.init=function(){(this._events===void 0||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},k.prototype.setMaxListeners=function(e){if(typeof e!="number"||e<0||_i(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this};function Si(t){return t._maxListeners===void 0?k.defaultMaxListeners:t._maxListeners}k.prototype.getMaxListeners=function(){return Si(this)},k.prototype.emit=function(e){for(var i=[],s=1;s<arguments.length;s++)i.push(arguments[s]);var r=e==="error",n=this._events;if(n!==void 0)r=r&&n.error===void 0;else if(!r)return!1;if(r){var a;if(i.length>0&&(a=i[0]),a instanceof Error)throw a;var h=new Error("Unhandled error."+(a?" ("+a.message+")":""));throw h.context=a,h}var o=n[e];if(o===void 0)return!1;if(typeof o=="function")xi(o,this,i);else for(var l=o.length,f=Ri(o,l),s=0;s<l;++s)xi(f[s],this,i);return!0};function bi(t,e,i,s){var r,n,a;if(pe(i),n=t._events,n===void 0?(n=t._events=Object.create(null),t._eventsCount=0):(n.newListener!==void 0&&(t.emit("newListener",e,i.listener?i.listener:i),n=t._events),a=n[e]),a===void 0)a=n[e]=i,++t._eventsCount;else if(typeof a=="function"?a=n[e]=s?[i,a]:[a,i]:s?a.unshift(i):a.push(i),r=Si(t),r>0&&a.length>r&&!a.warned){a.warned=!0;var h=new Error("Possible EventEmitter memory leak detected. "+a.length+" "+String(e)+" listeners added. Use emitter.setMaxListeners() to increase limit");h.name="MaxListenersExceededWarning",h.emitter=t,h.type=e,h.count=a.length,pr(h)}return t}k.prototype.addListener=function(e,i){return bi(this,e,i,!1)},k.prototype.on=k.prototype.addListener,k.prototype.prependListener=function(e,i){return bi(this,e,i,!0)};function yr(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function wi(t,e,i){var s={fired:!1,wrapFn:void 0,target:t,type:e,listener:i},r=yr.bind(s);return r.listener=i,s.wrapFn=r,r}k.prototype.once=function(e,i){return pe(i),this.on(e,wi(this,e,i)),this},k.prototype.prependOnceListener=function(e,i){return pe(i),this.prependListener(e,wi(this,e,i)),this},k.prototype.removeListener=function(e,i){var s,r,n,a,h;if(pe(i),r=this._events,r===void 0)return this;if(s=r[e],s===void 0)return this;if(s===i||s.listener===i)--this._eventsCount===0?this._events=Object.create(null):(delete r[e],r.removeListener&&this.emit("removeListener",e,s.listener||i));else if(typeof s!="function"){for(n=-1,a=s.length-1;a>=0;a--)if(s[a]===i||s[a].listener===i){h=s[a].listener,n=a;break}if(n<0)return this;n===0?s.shift():Mr(s,n),s.length===1&&(r[e]=s[0]),r.removeListener!==void 0&&this.emit("removeListener",e,h||i)}return this},k.prototype.off=k.prototype.removeListener,k.prototype.removeAllListeners=function(e){var i,s,r;if(s=this._events,s===void 0)return this;if(s.removeListener===void 0)return arguments.length===0?(this._events=Object.create(null),this._eventsCount=0):s[e]!==void 0&&(--this._eventsCount===0?this._events=Object.create(null):delete s[e]),this;if(arguments.length===0){var n=Object.keys(s),a;for(r=0;r<n.length;++r)a=n[r],a!=="removeListener"&&this.removeAllListeners(a);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(i=s[e],typeof i=="function")this.removeListener(e,i);else if(i!==void 0)for(r=i.length-1;r>=0;r--)this.removeListener(e,i[r]);return this};function Ti(t,e,i){var s=t._events;if(s===void 0)return[];var r=s[e];return r===void 0?[]:typeof r=="function"?i?[r.listener||r]:[r]:i?xr(r):Ri(r,r.length)}k.prototype.listeners=function(e){return Ti(this,e,!0)},k.prototype.rawListeners=function(e){return Ti(this,e,!1)},k.listenerCount=function(t,e){return typeof t.listenerCount=="function"?t.listenerCount(e):Ai.call(t,e)},k.prototype.listenerCount=Ai;function Ai(t){var e=this._events;if(e!==void 0){var i=e[t];if(typeof i=="function")return 1;if(i!==void 0)return i.length}return 0}k.prototype.eventNames=function(){return this._eventsCount>0?ge(this._events):[]};function Ri(t,e){for(var i=new Array(e),s=0;s<e;++s)i[s]=t[s];return i}function Mr(t,e){for(;e+1<t.length;e++)t[e]=t[e+1];t.pop()}function xr(t){for(var e=new Array(t.length),i=0;i<e.length;++i)e[i]=t[i].listener||t[i];return e}function _r(t,e){return new Promise(function(i,s){function r(a){t.removeListener(e,n),s(a)}function n(){typeof t.removeListener=="function"&&t.removeListener("error",r),i([].slice.call(arguments))}Li(t,e,n,{once:!0}),e!=="error"&&Er(t,r,{once:!0})})}function Er(t,e,i){typeof t.on=="function"&&Li(t,"error",e,i)}function Li(t,e,i,s){if(typeof t.on=="function")s.once?t.once(e,i):t.on(e,i);else if(typeof t.addEventListener=="function")t.addEventListener(e,function r(n){s.once&&t.removeEventListener(e,r),i(n)});else throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof t)}var Sr=Fe.exports;class Mt extends m.BufferGeometry{constructor(){super(),this.isMeshLine=!0,this.type="MeshLine",this.positions=[],this.previous=[],this.next=[],this.side=[],this.width=[],this.indices_array=[],this.uvs=[],this.counters=[],this._points=[],this._geom=null,this.widthCallback=null,this.matrixWorld=new m.Matrix4,Object.defineProperties(this,{geometry:{enumerable:!0,get:function(){return this}},geom:{enumerable:!0,get:function(){return this._geom},set:function(e){this.setGeometry(e,this.widthCallback)}},points:{enumerable:!0,get:function(){return this._points},set:function(e){this.setPoints(e,this.widthCallback)}}})}}Mt.prototype.setMatrixWorld=function(t){this.matrixWorld=t},Mt.prototype.setGeometry=function(t,e){this._geometry=t,this.setPoints(t.getAttribute("position").array,e)},Mt.prototype.setPoints=function(t,e){if(!(t instanceof Float32Array)&&!(t instanceof Array)){console.error("ERROR: The BufferArray of points is not instancied correctly.");return}if(this._points=t,this.widthCallback=e,this.positions=[],this.counters=[],t.length&&t[0]instanceof m.Vector3)for(var i=0;i<t.length;i++){var s=t[i],r=i/t.length;this.positions.push(s.x,s.y,s.z),this.positions.push(s.x,s.y,s.z),this.counters.push(r),this.counters.push(r)}else for(var i=0;i<t.length;i+=3){var r=i/t.length;this.positions.push(t[i],t[i+1],t[i+2]),this.positions.push(t[i],t[i+1],t[i+2]),this.counters.push(r),this.counters.push(r)}this.process()};function br(t,e){var i=new m.Matrix4,s=new m.Ray,r=new m.Sphere,n=new m.Vector3,a=this.geometry;if(a.boundingSphere||a.computeBoundingSphere(),r.copy(a.boundingSphere),r.applyMatrix4(this.matrixWorld),t.ray.intersectSphere(r,n)!==!1){i.copy(this.matrixWorld).invert(),s.copy(t.ray).applyMatrix4(i);var h=new m.Vector3,o=new m.Vector3,l=new m.Vector3,f=this instanceof m.LineSegments?2:1,u=a.index,c=a.attributes;if(u!==null)for(var v=u.array,d=c.position.array,g=c.width.array,p=0,y=v.length-1;p<y;p+=f){var x=v[p],M=v[p+1];h.fromArray(d,x*3),o.fromArray(d,M*3);var _=g[Math.floor(p/3)]!==void 0?g[Math.floor(p/3)]:1,S=t.params.Line.threshold+this.material.lineWidth*_/2,b=S*S,L=s.distanceSqToSegment(h,o,n,l);if(!(L>b)){n.applyMatrix4(this.matrixWorld);var A=t.ray.origin.distanceTo(n);A<t.near||A>t.far||(e.push({distance:A,point:l.clone().applyMatrix4(this.matrixWorld),index:p,face:null,faceIndex:null,object:this}),p=y)}}}}Mt.prototype.raycast=br,Mt.prototype.compareV3=function(t,e){var i=t*6,s=e*6;return this.positions[i]===this.positions[s]&&this.positions[i+1]===this.positions[s+1]&&this.positions[i+2]===this.positions[s+2]},Mt.prototype.copyV3=function(t){var e=t*6;return[this.positions[e],this.positions[e+1],this.positions[e+2]]},Mt.prototype.process=function(){var t=this.positions.length/6;this.previous=[],this.next=[],this.side=[],this.width=[],this.indices_array=[],this.uvs=[];var e,i;this.compareV3(0,t-1)?i=this.copyV3(t-2):i=this.copyV3(0),this.previous.push(i[0],i[1],i[2]),this.previous.push(i[0],i[1],i[2]);for(var s=0;s<t;s++){if(this.side.push(1),this.side.push(-1),this.widthCallback?e=this.widthCallback(s/(t-1)):e=1,this.width.push(e),this.width.push(e),this.uvs.push(s/(t-1),0),this.uvs.push(s/(t-1),1),s<t-1){i=this.copyV3(s),this.previous.push(i[0],i[1],i[2]),this.previous.push(i[0],i[1],i[2]);var r=s*2;this.indices_array.push(r,r+1,r+2),this.indices_array.push(r+2,r+1,r+3)}s>0&&(i=this.copyV3(s),this.next.push(i[0],i[1],i[2]),this.next.push(i[0],i[1],i[2]))}this.compareV3(t-1,0)?i=this.copyV3(1):i=this.copyV3(t-1),this.next.push(i[0],i[1],i[2]),this.next.push(i[0],i[1],i[2]),!this._attributes||this._attributes.position.count!==this.positions.length?this._attributes={position:new m.BufferAttribute(new Float32Array(this.positions),3),previous:new m.BufferAttribute(new Float32Array(this.previous),3),next:new m.BufferAttribute(new Float32Array(this.next),3),side:new m.BufferAttribute(new Float32Array(this.side),1),width:new m.BufferAttribute(new Float32Array(this.width),1),uv:new m.BufferAttribute(new Float32Array(this.uvs),2),index:new m.BufferAttribute(new Uint16Array(this.indices_array),1),counters:new m.BufferAttribute(new Float32Array(this.counters),1)}:(this._attributes.position.copyArray(new Float32Array(this.positions)),this._attributes.position.needsUpdate=!0,this._attributes.previous.copyArray(new Float32Array(this.previous)),this._attributes.previous.needsUpdate=!0,this._attributes.next.copyArray(new Float32Array(this.next)),this._attributes.next.needsUpdate=!0,this._attributes.side.copyArray(new Float32Array(this.side)),this._attributes.side.needsUpdate=!0,this._attributes.width.copyArray(new Float32Array(this.width)),this._attributes.width.needsUpdate=!0,this._attributes.uv.copyArray(new Float32Array(this.uvs)),this._attributes.uv.needsUpdate=!0,this._attributes.index.copyArray(new Uint16Array(this.indices_array)),this._attributes.index.needsUpdate=!0),this.setAttribute("position",this._attributes.position),this.setAttribute("previous",this._attributes.previous),this.setAttribute("next",this._attributes.next),this.setAttribute("side",this._attributes.side),this.setAttribute("width",this._attributes.width),this.setAttribute("uv",this._attributes.uv),this.setAttribute("counters",this._attributes.counters),this.setIndex(this._attributes.index),this.computeBoundingSphere(),this.computeBoundingBox()};function Ge(t,e,i,s,r){var n;if(t=t.subarray||t.slice?t:t.buffer,i=i.subarray||i.slice?i:i.buffer,t=e?t.subarray?t.subarray(e,r&&e+r):t.slice(e,r&&e+r):t,i.set)i.set(t,s);else for(n=0;n<t.length;n++)i[n+s]=t[n];return i}Mt.prototype.advance=function(t){var e=this._attributes.position.array,i=this._attributes.previous.array,s=this._attributes.next.array,r=e.length;Ge(e,0,i,0,r),Ge(e,6,e,0,r-6),e[r-6]=t.x,e[r-5]=t.y,e[r-4]=t.z,e[r-3]=t.x,e[r-2]=t.y,e[r-1]=t.z,Ge(e,6,s,0,r-6),s[r-6]=t.x,s[r-5]=t.y,s[r-4]=t.z,s[r-3]=t.x,s[r-2]=t.y,s[r-1]=t.z,this._attributes.position.needsUpdate=!0,this._attributes.previous.needsUpdate=!0,this._attributes.next.needsUpdate=!0},m.ShaderChunk.meshline_vert=["",m.ShaderChunk.logdepthbuf_pars_vertex,m.ShaderChunk.fog_pars_vertex,"","attribute vec3 previous;","attribute vec3 next;","attribute float side;","attribute float width;","attribute float counters;","","uniform vec2 resolution;","uniform float lineWidth;","uniform vec3 color;","uniform float opacity;","uniform float sizeAttenuation;","","varying vec2 vUV;","varying vec4 vColor;","varying float vCounters;","","vec2 fix( vec4 i, float aspect ) {","","    vec2 res = i.xy / i.w;","    res.x *= aspect;","	 vCounters = counters;","    return res;","","}","","void main() {","","    float aspect = resolution.x / resolution.y;","","    vColor = vec4( color, opacity );","    vUV = uv;","","    mat4 m = projectionMatrix * modelViewMatrix;","    vec4 finalPosition = m * vec4( position, 1.0 );","    vec4 prevPos = m * vec4( previous, 1.0 );","    vec4 nextPos = m * vec4( next, 1.0 );","","    vec2 currentP = fix( finalPosition, aspect );","    vec2 prevP = fix( prevPos, aspect );","    vec2 nextP = fix( nextPos, aspect );","","    float w = lineWidth * width;","","    vec2 dir;","    if( nextP == currentP ) dir = normalize( currentP - prevP );","    else if( prevP == currentP ) dir = normalize( nextP - currentP );","    else {","        vec2 dir1 = normalize( currentP - prevP );","        vec2 dir2 = normalize( nextP - currentP );","        dir = normalize( dir1 + dir2 );","","        vec2 perp = vec2( -dir1.y, dir1.x );","        vec2 miter = vec2( -dir.y, dir.x );","        //w = clamp( w / dot( miter, perp ), 0., 4. * lineWidth * width );","","    }","","    //vec2 normal = ( cross( vec3( dir, 0. ), vec3( 0., 0., 1. ) ) ).xy;","    vec4 normal = vec4( -dir.y, dir.x, 0., 1. );","    normal.xy *= .5 * w;","    normal *= projectionMatrix;","    if( sizeAttenuation == 0. ) {","        normal.xy *= finalPosition.w;","        normal.xy /= ( vec4( resolution, 0., 1. ) * projectionMatrix ).xy;","    }","","    finalPosition.xy += normal.xy * side;","","    gl_Position = finalPosition;","",m.ShaderChunk.logdepthbuf_vertex,m.ShaderChunk.fog_vertex&&"    vec4 mvPosition = modelViewMatrix * vec4( position, 1.0 );",m.ShaderChunk.fog_vertex,"}"].join(`
`),m.ShaderChunk.meshline_frag=["",m.ShaderChunk.fog_pars_fragment,m.ShaderChunk.logdepthbuf_pars_fragment,"","uniform sampler2D map;","uniform sampler2D alphaMap;","uniform float useMap;","uniform float useAlphaMap;","uniform float useDash;","uniform float dashArray;","uniform float dashOffset;","uniform float dashRatio;","uniform float visibility;","uniform float alphaTest;","uniform vec2 repeat;","","varying vec2 vUV;","varying vec4 vColor;","varying float vCounters;","","void main() {","",m.ShaderChunk.logdepthbuf_fragment,"","    vec4 c = vColor;","    if( useMap == 1. ) c *= texture2D( map, vUV * repeat );","    if( useAlphaMap == 1. ) c.a *= texture2D( alphaMap, vUV * repeat ).a;","    if( c.a < alphaTest ) discard;","    if( useDash == 1. ){","        c.a *= ceil(mod(vCounters + dashOffset, dashArray) - (dashArray * dashRatio));","    }","    gl_FragColor = c;","    gl_FragColor.a *= step(vCounters, visibility);","",m.ShaderChunk.fog_fragment,"}"].join(`
`);class Ii extends m.ShaderMaterial{constructor(e){super({uniforms:Object.assign({},m.UniformsLib.fog,{lineWidth:{value:1},map:{value:null},useMap:{value:0},alphaMap:{value:null},useAlphaMap:{value:0},color:{value:new m.Color(16777215)},opacity:{value:1},resolution:{value:new m.Vector2(1,1)},sizeAttenuation:{value:1},dashArray:{value:0},dashOffset:{value:0},dashRatio:{value:.5},useDash:{value:0},visibility:{value:1},alphaTest:{value:0},repeat:{value:new m.Vector2(1,1)}}),vertexShader:m.ShaderChunk.meshline_vert,fragmentShader:m.ShaderChunk.meshline_frag}),this.isMeshLineMaterial=!0,this.type="MeshLineMaterial",Object.defineProperties(this,{lineWidth:{enumerable:!0,get:function(){return this.uniforms.lineWidth.value},set:function(i){this.uniforms.lineWidth.value=i}},map:{enumerable:!0,get:function(){return this.uniforms.map.value},set:function(i){this.uniforms.map.value=i}},useMap:{enumerable:!0,get:function(){return this.uniforms.useMap.value},set:function(i){this.uniforms.useMap.value=i}},alphaMap:{enumerable:!0,get:function(){return this.uniforms.alphaMap.value},set:function(i){this.uniforms.alphaMap.value=i}},useAlphaMap:{enumerable:!0,get:function(){return this.uniforms.useAlphaMap.value},set:function(i){this.uniforms.useAlphaMap.value=i}},color:{enumerable:!0,get:function(){return this.uniforms.color.value},set:function(i){this.uniforms.color.value=i}},opacity:{enumerable:!0,get:function(){return this.uniforms.opacity.value},set:function(i){this.uniforms.opacity.value=i}},resolution:{enumerable:!0,get:function(){return this.uniforms.resolution.value},set:function(i){this.uniforms.resolution.value.copy(i)}},sizeAttenuation:{enumerable:!0,get:function(){return this.uniforms.sizeAttenuation.value},set:function(i){this.uniforms.sizeAttenuation.value=i}},dashArray:{enumerable:!0,get:function(){return this.uniforms.dashArray.value},set:function(i){this.uniforms.dashArray.value=i,this.useDash=i!==0?1:0}},dashOffset:{enumerable:!0,get:function(){return this.uniforms.dashOffset.value},set:function(i){this.uniforms.dashOffset.value=i}},dashRatio:{enumerable:!0,get:function(){return this.uniforms.dashRatio.value},set:function(i){this.uniforms.dashRatio.value=i}},useDash:{enumerable:!0,get:function(){return this.uniforms.useDash.value},set:function(i){this.uniforms.useDash.value=i}},visibility:{enumerable:!0,get:function(){return this.uniforms.visibility.value},set:function(i){this.uniforms.visibility.value=i}},alphaTest:{enumerable:!0,get:function(){return this.uniforms.alphaTest.value},set:function(i){this.uniforms.alphaTest.value=i}},repeat:{enumerable:!0,get:function(){return this.uniforms.repeat.value},set:function(i){this.uniforms.repeat.value.copy(i)}}}),this.setValues(e)}}Ii.prototype.copy=function(t){return m.ShaderMaterial.prototype.copy.call(this,t),this.lineWidth=t.lineWidth,this.map=t.map,this.useMap=t.useMap,this.alphaMap=t.alphaMap,this.useAlphaMap=t.useAlphaMap,this.color.copy(t.color),this.opacity=t.opacity,this.resolution.copy(t.resolution),this.sizeAttenuation=t.sizeAttenuation,this.dashArray.copy(t.dashArray),this.dashOffset.copy(t.dashOffset),this.dashRatio.copy(t.dashRatio),this.useDash=t.useDash,this.visibility=t.visibility,this.alphaTest=t.alphaTest,this.repeat.copy(t.repeat),this};/**
 * postprocessing v6.33.2 build Mon Oct 02 2023
 * https://github.com/pmndrs/postprocessing
 * Copyright 2015-2023 Raoul van Rüschen
 * @license Zlib
 */var Ni="varying vec2 vUv;void main(){vUv=position.xy*0.5+0.5;gl_Position=vec4(position.xy,1.0,1.0);}",N={SKIP:9,SET:30,ADD:0,ALPHA:1,AVERAGE:2,COLOR:3,COLOR_BURN:4,COLOR_DODGE:5,DARKEN:6,DIFFERENCE:7,DIVIDE:8,DST:9,EXCLUSION:10,HARD_LIGHT:11,HARD_MIX:12,HUE:13,INVERT:14,INVERT_RGB:15,LIGHTEN:16,LINEAR_BURN:17,LINEAR_DODGE:18,LINEAR_LIGHT:19,LUMINOSITY:20,MULTIPLY:21,NEGATION:22,NORMAL:23,OVERLAY:24,PIN_LIGHT:25,REFLECT:26,SATURATION:27,SCREEN:28,SOFT_LIGHT:29,SRC:30,SUBTRACT:31,VIVID_LIGHT:32},Pi="",W="srgb",Bt="srgb-linear",Nt={NONE:0,DEPTH:1,CONVOLUTION:2},U={FRAGMENT_HEAD:"FRAGMENT_HEAD",FRAGMENT_MAIN_UV:"FRAGMENT_MAIN_UV",FRAGMENT_MAIN_IMAGE:"FRAGMENT_MAIN_IMAGE",VERTEX_HEAD:"VERTEX_HEAD",VERTEX_MAIN_SUPPORT:"VERTEX_MAIN_SUPPORT"},ye={VERY_SMALL:0,SMALL:1,MEDIUM:2,LARGE:3,VERY_LARGE:4,HUGE:5},wr={SCALE_UP:"lut.scaleup"},Me={DEFAULT:0,ESKIL:1},Ci=Number(m.REVISION.replace(/\D+/g,"")),He=Ci>=152,Oi=new Map([[m.LinearEncoding,Bt],[m.sRGBEncoding,W]]),Tr=new Map([[Bt,m.LinearEncoding],[W,m.sRGBEncoding]]);function Dt(t){return t===null?null:He?t.outputColorSpace:Oi.get(t.outputEncoding)}function ht(t,e){t!==null&&(He?t.colorSpace=e:t.encoding=Tr.get(e))}function qe(t,e){t===null||e===null||(He?e.colorSpace=t.colorSpace:e.encoding=t.encoding)}function Kt(t){return Ci<154?t.replace("colorspace_fragment","encodings_fragment"):t}var Ar=`#ifdef FRAMEBUFFER_PRECISION_HIGH
uniform mediump sampler2D inputBuffer;
#else
uniform lowp sampler2D inputBuffer;
#endif
varying vec2 vUv0;varying vec2 vUv1;varying vec2 vUv2;varying vec2 vUv3;void main(){vec4 sum=texture2D(inputBuffer,vUv0);sum+=texture2D(inputBuffer,vUv1);sum+=texture2D(inputBuffer,vUv2);sum+=texture2D(inputBuffer,vUv3);gl_FragColor=sum*0.25;
#include <colorspace_fragment>
}`,Rr="uniform vec4 texelSize;uniform float kernel;uniform float scale;varying vec2 vUv0;varying vec2 vUv1;varying vec2 vUv2;varying vec2 vUv3;void main(){vec2 uv=position.xy*0.5+0.5;vec2 dUv=(texelSize.xy*vec2(kernel)+texelSize.zw)*scale;vUv0=vec2(uv.x-dUv.x,uv.y+dUv.y);vUv1=vec2(uv.x+dUv.x,uv.y+dUv.y);vUv2=vec2(uv.x+dUv.x,uv.y-dUv.y);vUv3=vec2(uv.x-dUv.x,uv.y-dUv.y);gl_Position=vec4(position.xy,1.0,1.0);}",Lr=[new Float32Array([0,0]),new Float32Array([0,1,1]),new Float32Array([0,1,1,2]),new Float32Array([0,1,2,2,3]),new Float32Array([0,1,2,3,4,4,5]),new Float32Array([0,1,2,3,4,5,7,8,9,10])],Ir=class extends m.ShaderMaterial{constructor(t=new m.Vector4){super({name:"KawaseBlurMaterial",uniforms:{inputBuffer:new m.Uniform(null),texelSize:new m.Uniform(new m.Vector4),scale:new m.Uniform(1),kernel:new m.Uniform(0)},blending:m.NoBlending,toneMapped:!1,depthWrite:!1,depthTest:!1,fragmentShader:Ar,vertexShader:Rr}),this.fragmentShader=Kt(this.fragmentShader),this.setTexelSize(t.x,t.y),this.kernelSize=ye.MEDIUM}set inputBuffer(t){this.uniforms.inputBuffer.value=t}setInputBuffer(t){this.inputBuffer=t}get kernelSequence(){return Lr[this.kernelSize]}get scale(){return this.uniforms.scale.value}set scale(t){this.uniforms.scale.value=t}getScale(){return this.uniforms.scale.value}setScale(t){this.uniforms.scale.value=t}getKernel(){return null}get kernel(){return this.uniforms.kernel.value}set kernel(t){this.uniforms.kernel.value=t}setKernel(t){this.kernel=t}setTexelSize(t,e){this.uniforms.texelSize.value.set(t,e,t*.5,e*.5)}setSize(t,e){const i=1/t,s=1/e;this.uniforms.texelSize.value.set(i,s,i*.5,s*.5)}},Nr=`#include <common>
#include <dithering_pars_fragment>
#ifdef FRAMEBUFFER_PRECISION_HIGH
uniform mediump sampler2D inputBuffer;
#else
uniform lowp sampler2D inputBuffer;
#endif
uniform float opacity;varying vec2 vUv;void main(){vec4 texel=texture2D(inputBuffer,vUv);gl_FragColor=opacity*texel;
#include <colorspace_fragment>
#include <dithering_fragment>
}`,Ui=class extends m.ShaderMaterial{constructor(){super({name:"CopyMaterial",uniforms:{inputBuffer:new m.Uniform(null),opacity:new m.Uniform(1)},blending:m.NoBlending,toneMapped:!1,depthWrite:!1,depthTest:!1,fragmentShader:Nr,vertexShader:Ni}),this.fragmentShader=Kt(this.fragmentShader)}set inputBuffer(t){this.uniforms.inputBuffer.value=t}setInputBuffer(t){this.uniforms.inputBuffer.value=t}getOpacity(t){return this.uniforms.opacity.value}setOpacity(t){this.uniforms.opacity.value=t}},Pr=`#ifdef FRAMEBUFFER_PRECISION_HIGH
uniform mediump sampler2D inputBuffer;
#else
uniform lowp sampler2D inputBuffer;
#endif
#define WEIGHT_INNER 0.125
#define WEIGHT_OUTER 0.0555555
varying vec2 vUv;varying vec2 vUv00;varying vec2 vUv01;varying vec2 vUv02;varying vec2 vUv03;varying vec2 vUv04;varying vec2 vUv05;varying vec2 vUv06;varying vec2 vUv07;varying vec2 vUv08;varying vec2 vUv09;varying vec2 vUv10;varying vec2 vUv11;float clampToBorder(const in vec2 uv){return float(uv.s>=0.0&&uv.s<=1.0&&uv.t>=0.0&&uv.t<=1.0);}void main(){vec4 c=vec4(0.0);vec4 w=WEIGHT_INNER*vec4(clampToBorder(vUv00),clampToBorder(vUv01),clampToBorder(vUv02),clampToBorder(vUv03));c+=w.x*texture2D(inputBuffer,vUv00);c+=w.y*texture2D(inputBuffer,vUv01);c+=w.z*texture2D(inputBuffer,vUv02);c+=w.w*texture2D(inputBuffer,vUv03);w=WEIGHT_OUTER*vec4(clampToBorder(vUv04),clampToBorder(vUv05),clampToBorder(vUv06),clampToBorder(vUv07));c+=w.x*texture2D(inputBuffer,vUv04);c+=w.y*texture2D(inputBuffer,vUv05);c+=w.z*texture2D(inputBuffer,vUv06);c+=w.w*texture2D(inputBuffer,vUv07);w=WEIGHT_OUTER*vec4(clampToBorder(vUv08),clampToBorder(vUv09),clampToBorder(vUv10),clampToBorder(vUv11));c+=w.x*texture2D(inputBuffer,vUv08);c+=w.y*texture2D(inputBuffer,vUv09);c+=w.z*texture2D(inputBuffer,vUv10);c+=w.w*texture2D(inputBuffer,vUv11);c+=WEIGHT_OUTER*texture2D(inputBuffer,vUv);gl_FragColor=c;
#include <colorspace_fragment>
}`,Cr="uniform vec2 texelSize;varying vec2 vUv;varying vec2 vUv00;varying vec2 vUv01;varying vec2 vUv02;varying vec2 vUv03;varying vec2 vUv04;varying vec2 vUv05;varying vec2 vUv06;varying vec2 vUv07;varying vec2 vUv08;varying vec2 vUv09;varying vec2 vUv10;varying vec2 vUv11;void main(){vUv=position.xy*0.5+0.5;vUv00=vUv+texelSize*vec2(-1.0,1.0);vUv01=vUv+texelSize*vec2(1.0,1.0);vUv02=vUv+texelSize*vec2(-1.0,-1.0);vUv03=vUv+texelSize*vec2(1.0,-1.0);vUv04=vUv+texelSize*vec2(-2.0,2.0);vUv05=vUv+texelSize*vec2(0.0,2.0);vUv06=vUv+texelSize*vec2(2.0,2.0);vUv07=vUv+texelSize*vec2(-2.0,0.0);vUv08=vUv+texelSize*vec2(2.0,0.0);vUv09=vUv+texelSize*vec2(-2.0,-2.0);vUv10=vUv+texelSize*vec2(0.0,-2.0);vUv11=vUv+texelSize*vec2(2.0,-2.0);gl_Position=vec4(position.xy,1.0,1.0);}",Or=class extends m.ShaderMaterial{constructor(){super({name:"DownsamplingMaterial",uniforms:{inputBuffer:new m.Uniform(null),texelSize:new m.Uniform(new m.Vector2)},blending:m.NoBlending,toneMapped:!1,depthWrite:!1,depthTest:!1,fragmentShader:Pr,vertexShader:Cr}),this.fragmentShader=Kt(this.fragmentShader)}set inputBuffer(t){this.uniforms.inputBuffer.value=t}setSize(t,e){this.uniforms.texelSize.value.set(1/t,1/e)}},Ur=`#include <common>
#include <packing>
#include <dithering_pars_fragment>
#define packFloatToRGBA(v) packDepthToRGBA(v)
#define unpackRGBAToFloat(v) unpackRGBAToDepth(v)
#ifdef FRAMEBUFFER_PRECISION_HIGH
uniform mediump sampler2D inputBuffer;
#else
uniform lowp sampler2D inputBuffer;
#endif
#if DEPTH_PACKING == 3201
uniform lowp sampler2D depthBuffer;
#elif defined(GL_FRAGMENT_PRECISION_HIGH)
uniform highp sampler2D depthBuffer;
#else
uniform mediump sampler2D depthBuffer;
#endif
uniform vec2 resolution;uniform vec2 texelSize;uniform float cameraNear;uniform float cameraFar;uniform float aspect;uniform float time;varying vec2 vUv;
#if THREE_REVISION < 143
#define luminance(v) linearToRelativeLuminance(v)
#endif
#if THREE_REVISION >= 137
vec4 sRGBToLinear(const in vec4 value){return vec4(mix(pow(value.rgb*0.9478672986+vec3(0.0521327014),vec3(2.4)),value.rgb*0.0773993808,vec3(lessThanEqual(value.rgb,vec3(0.04045)))),value.a);}
#endif
float readDepth(const in vec2 uv){
#if DEPTH_PACKING == 3201
return unpackRGBAToDepth(texture2D(depthBuffer,uv));
#else
return texture2D(depthBuffer,uv).r;
#endif
}float getViewZ(const in float depth){
#ifdef PERSPECTIVE_CAMERA
return perspectiveDepthToViewZ(depth,cameraNear,cameraFar);
#else
return orthographicDepthToViewZ(depth,cameraNear,cameraFar);
#endif
}vec3 RGBToHCV(const in vec3 RGB){vec4 P=mix(vec4(RGB.bg,-1.0,2.0/3.0),vec4(RGB.gb,0.0,-1.0/3.0),step(RGB.b,RGB.g));vec4 Q=mix(vec4(P.xyw,RGB.r),vec4(RGB.r,P.yzx),step(P.x,RGB.r));float C=Q.x-min(Q.w,Q.y);float H=abs((Q.w-Q.y)/(6.0*C+EPSILON)+Q.z);return vec3(H,C,Q.x);}vec3 RGBToHSL(const in vec3 RGB){vec3 HCV=RGBToHCV(RGB);float L=HCV.z-HCV.y*0.5;float S=HCV.y/(1.0-abs(L*2.0-1.0)+EPSILON);return vec3(HCV.x,S,L);}vec3 HueToRGB(const in float H){float R=abs(H*6.0-3.0)-1.0;float G=2.0-abs(H*6.0-2.0);float B=2.0-abs(H*6.0-4.0);return clamp(vec3(R,G,B),0.0,1.0);}vec3 HSLToRGB(const in vec3 HSL){vec3 RGB=HueToRGB(HSL.x);float C=(1.0-abs(2.0*HSL.z-1.0))*HSL.y;return(RGB-0.5)*C+HSL.z;}FRAGMENT_HEAD void main(){FRAGMENT_MAIN_UV vec4 color0=texture2D(inputBuffer,UV);vec4 color1=vec4(0.0);FRAGMENT_MAIN_IMAGE color0.a=clamp(color0.a,0.0,1.0);gl_FragColor=color0;
#ifdef ENCODE_OUTPUT
#include <colorspace_fragment>
#endif
#include <dithering_fragment>
}`,Br="uniform vec2 resolution;uniform vec2 texelSize;uniform float cameraNear;uniform float cameraFar;uniform float aspect;uniform float time;varying vec2 vUv;VERTEX_HEAD void main(){vUv=position.xy*0.5+0.5;VERTEX_MAIN_SUPPORT gl_Position=vec4(position.xy,1.0,1.0);}",Dr=class extends m.ShaderMaterial{constructor(t,e,i,s,r=!1){super({name:"EffectMaterial",defines:{THREE_REVISION:m.REVISION.replace(/\D+/g,""),DEPTH_PACKING:"0",ENCODE_OUTPUT:"1"},uniforms:{inputBuffer:new m.Uniform(null),depthBuffer:new m.Uniform(null),resolution:new m.Uniform(new m.Vector2),texelSize:new m.Uniform(new m.Vector2),cameraNear:new m.Uniform(.3),cameraFar:new m.Uniform(1e3),aspect:new m.Uniform(1),time:new m.Uniform(0)},blending:m.NoBlending,toneMapped:!1,depthWrite:!1,depthTest:!1,dithering:r}),t&&this.setShaderParts(t),e&&this.setDefines(e),i&&this.setUniforms(i),this.copyCameraSettings(s)}set inputBuffer(t){this.uniforms.inputBuffer.value=t}setInputBuffer(t){this.uniforms.inputBuffer.value=t}get depthBuffer(){return this.uniforms.depthBuffer.value}set depthBuffer(t){this.uniforms.depthBuffer.value=t}get depthPacking(){return Number(this.defines.DEPTH_PACKING)}set depthPacking(t){this.defines.DEPTH_PACKING=t.toFixed(0),this.needsUpdate=!0}setDepthBuffer(t,e=m.BasicDepthPacking){this.depthBuffer=t,this.depthPacking=e}setShaderData(t){this.setShaderParts(t.shaderParts),this.setDefines(t.defines),this.setUniforms(t.uniforms),this.setExtensions(t.extensions)}setShaderParts(t){return this.fragmentShader=Ur.replace(U.FRAGMENT_HEAD,t.get(U.FRAGMENT_HEAD)||"").replace(U.FRAGMENT_MAIN_UV,t.get(U.FRAGMENT_MAIN_UV)||"").replace(U.FRAGMENT_MAIN_IMAGE,t.get(U.FRAGMENT_MAIN_IMAGE)||""),this.vertexShader=Br.replace(U.VERTEX_HEAD,t.get(U.VERTEX_HEAD)||"").replace(U.VERTEX_MAIN_SUPPORT,t.get(U.VERTEX_MAIN_SUPPORT)||""),this.fragmentShader=Kt(this.fragmentShader),this.needsUpdate=!0,this}setDefines(t){for(const e of t.entries())this.defines[e[0]]=e[1];return this.needsUpdate=!0,this}setUniforms(t){for(const e of t.entries())this.uniforms[e[0]]=e[1];return this}setExtensions(t){this.extensions={};for(const e of t)this.extensions[e]=!0;return this}get encodeOutput(){return this.defines.ENCODE_OUTPUT!==void 0}set encodeOutput(t){this.encodeOutput!==t&&(t?this.defines.ENCODE_OUTPUT="1":delete this.defines.ENCODE_OUTPUT,this.needsUpdate=!0)}isOutputEncodingEnabled(t){return this.encodeOutput}setOutputEncodingEnabled(t){this.encodeOutput=t}get time(){return this.uniforms.time.value}set time(t){this.uniforms.time.value=t}setDeltaTime(t){this.uniforms.time.value+=t}adoptCameraSettings(t){this.copyCameraSettings(t)}copyCameraSettings(t){t&&(this.uniforms.cameraNear.value=t.near,this.uniforms.cameraFar.value=t.far,t instanceof m.PerspectiveCamera?this.defines.PERSPECTIVE_CAMERA="1":delete this.defines.PERSPECTIVE_CAMERA,this.needsUpdate=!0)}setSize(t,e){const i=this.uniforms;i.resolution.value.set(t,e),i.texelSize.value.set(1/t,1/e),i.aspect.value=t/e}static get Section(){return U}},zr=`#include <common>
#if THREE_REVISION < 143
#define luminance(v) linearToRelativeLuminance(v)
#endif
#ifdef FRAMEBUFFER_PRECISION_HIGH
uniform mediump sampler2D inputBuffer;
#else
uniform lowp sampler2D inputBuffer;
#endif
#ifdef RANGE
uniform vec2 range;
#elif defined(THRESHOLD)
uniform float threshold;uniform float smoothing;
#endif
varying vec2 vUv;void main(){vec4 texel=texture2D(inputBuffer,vUv);float l=luminance(texel.rgb);
#ifdef RANGE
float low=step(range.x,l);float high=step(l,range.y);l*=low*high;
#elif defined(THRESHOLD)
l=smoothstep(threshold,threshold+smoothing,l);
#endif
#ifdef COLOR
gl_FragColor=vec4(texel.rgb*l,l);
#else
gl_FragColor=vec4(l);
#endif
}`,kr=class extends m.ShaderMaterial{constructor(t=!1,e=null){super({name:"LuminanceMaterial",defines:{THREE_REVISION:m.REVISION.replace(/\D+/g,"")},uniforms:{inputBuffer:new m.Uniform(null),threshold:new m.Uniform(0),smoothing:new m.Uniform(1),range:new m.Uniform(null)},blending:m.NoBlending,toneMapped:!1,depthWrite:!1,depthTest:!1,fragmentShader:zr,vertexShader:Ni}),this.colorOutput=t,this.luminanceRange=e}set inputBuffer(t){this.uniforms.inputBuffer.value=t}setInputBuffer(t){this.uniforms.inputBuffer.value=t}get threshold(){return this.uniforms.threshold.value}set threshold(t){this.smoothing>0||t>0?this.defines.THRESHOLD="1":delete this.defines.THRESHOLD,this.uniforms.threshold.value=t}getThreshold(){return this.threshold}setThreshold(t){this.threshold=t}get smoothing(){return this.uniforms.smoothing.value}set smoothing(t){this.threshold>0||t>0?this.defines.THRESHOLD="1":delete this.defines.THRESHOLD,this.uniforms.smoothing.value=t}getSmoothingFactor(){return this.smoothing}setSmoothingFactor(t){this.smoothing=t}get useThreshold(){return this.threshold>0||this.smoothing>0}set useThreshold(t){}get colorOutput(){return this.defines.COLOR!==void 0}set colorOutput(t){t?this.defines.COLOR="1":delete this.defines.COLOR,this.needsUpdate=!0}isColorOutputEnabled(t){return this.colorOutput}setColorOutputEnabled(t){this.colorOutput=t}get useRange(){return this.luminanceRange!==null}set useRange(t){this.luminanceRange=null}get luminanceRange(){return this.uniforms.range.value}set luminanceRange(t){t!==null?this.defines.RANGE="1":delete this.defines.RANGE,this.uniforms.range.value=t,this.needsUpdate=!0}getLuminanceRange(){return this.luminanceRange}setLuminanceRange(t){this.luminanceRange=t}},Fr=`#ifdef FRAMEBUFFER_PRECISION_HIGH
uniform mediump sampler2D inputBuffer;uniform mediump sampler2D supportBuffer;
#else
uniform lowp sampler2D inputBuffer;uniform lowp sampler2D supportBuffer;
#endif
uniform float radius;varying vec2 vUv;varying vec2 vUv0;varying vec2 vUv1;varying vec2 vUv2;varying vec2 vUv3;varying vec2 vUv4;varying vec2 vUv5;varying vec2 vUv6;varying vec2 vUv7;void main(){vec4 c=vec4(0.0);c+=texture2D(inputBuffer,vUv0)*0.0625;c+=texture2D(inputBuffer,vUv1)*0.125;c+=texture2D(inputBuffer,vUv2)*0.0625;c+=texture2D(inputBuffer,vUv3)*0.125;c+=texture2D(inputBuffer,vUv)*0.25;c+=texture2D(inputBuffer,vUv4)*0.125;c+=texture2D(inputBuffer,vUv5)*0.0625;c+=texture2D(inputBuffer,vUv6)*0.125;c+=texture2D(inputBuffer,vUv7)*0.0625;vec4 baseColor=texture2D(supportBuffer,vUv);gl_FragColor=mix(baseColor,c,radius);
#include <colorspace_fragment>
}`,Gr="uniform vec2 texelSize;varying vec2 vUv;varying vec2 vUv0;varying vec2 vUv1;varying vec2 vUv2;varying vec2 vUv3;varying vec2 vUv4;varying vec2 vUv5;varying vec2 vUv6;varying vec2 vUv7;void main(){vUv=position.xy*0.5+0.5;vUv0=vUv+texelSize*vec2(-1.0,1.0);vUv1=vUv+texelSize*vec2(0.0,1.0);vUv2=vUv+texelSize*vec2(1.0,1.0);vUv3=vUv+texelSize*vec2(-1.0,0.0);vUv4=vUv+texelSize*vec2(1.0,0.0);vUv5=vUv+texelSize*vec2(-1.0,-1.0);vUv6=vUv+texelSize*vec2(0.0,-1.0);vUv7=vUv+texelSize*vec2(1.0,-1.0);gl_Position=vec4(position.xy,1.0,1.0);}",Hr=class extends m.ShaderMaterial{constructor(){super({name:"UpsamplingMaterial",uniforms:{inputBuffer:new m.Uniform(null),supportBuffer:new m.Uniform(null),texelSize:new m.Uniform(new m.Vector2),radius:new m.Uniform(.85)},blending:m.NoBlending,toneMapped:!1,depthWrite:!1,depthTest:!1,fragmentShader:Fr,vertexShader:Gr}),this.fragmentShader=Kt(this.fragmentShader)}set inputBuffer(t){this.uniforms.inputBuffer.value=t}set supportBuffer(t){this.uniforms.supportBuffer.value=t}get radius(){return this.uniforms.radius.value}set radius(t){this.uniforms.radius.value=t}setSize(t,e){this.uniforms.texelSize.value.set(1/t,1/e)}},qr=new m.Camera,Et=null;function Vr(){if(Et===null){const t=new Float32Array([-1,-1,0,3,-1,0,-1,3,0]),e=new Float32Array([0,0,2,0,0,2]);Et=new m.BufferGeometry,Et.setAttribute!==void 0?(Et.setAttribute("position",new m.BufferAttribute(t,3)),Et.setAttribute("uv",new m.BufferAttribute(e,2))):(Et.addAttribute("position",new m.BufferAttribute(t,3)),Et.addAttribute("uv",new m.BufferAttribute(e,2)))}return Et}var ot=class mr{constructor(e="Pass",i=new m.Scene,s=qr){this.name=e,this.renderer=null,this.scene=i,this.camera=s,this.screen=null,this.rtt=!0,this.needsSwap=!0,this.needsDepthTexture=!1,this.enabled=!0}get renderToScreen(){return!this.rtt}set renderToScreen(e){if(this.rtt===e){const i=this.fullscreenMaterial;i!==null&&(i.needsUpdate=!0),this.rtt=!e}}set mainScene(e){}set mainCamera(e){}setRenderer(e){this.renderer=e}isEnabled(){return this.enabled}setEnabled(e){this.enabled=e}get fullscreenMaterial(){return this.screen!==null?this.screen.material:null}set fullscreenMaterial(e){let i=this.screen;i!==null?i.material=e:(i=new m.Mesh(Vr(),e),i.frustumCulled=!1,this.scene===null&&(this.scene=new m.Scene),this.scene.add(i),this.screen=i)}getFullscreenMaterial(){return this.fullscreenMaterial}setFullscreenMaterial(e){this.fullscreenMaterial=e}getDepthTexture(){return null}setDepthTexture(e,i=m.BasicDepthPacking){}render(e,i,s,r,n){throw new Error("Render method not implemented!")}setSize(e,i){}initialize(e,i,s){}dispose(){for(const e of Object.keys(this)){const i=this[e];(i instanceof m.WebGLRenderTarget||i instanceof m.Material||i instanceof m.Texture||i instanceof mr)&&this[e].dispose()}}},$r=class extends ot{constructor(t,e=!0){super("CopyPass"),this.fullscreenMaterial=new Ui,this.needsSwap=!1,this.renderTarget=t,t===void 0&&(this.renderTarget=new m.WebGLRenderTarget(1,1,{minFilter:m.LinearFilter,magFilter:m.LinearFilter,stencilBuffer:!1,depthBuffer:!1}),this.renderTarget.texture.name="CopyPass.Target"),this.autoResize=e}get resize(){return this.autoResize}set resize(t){this.autoResize=t}get texture(){return this.renderTarget.texture}getTexture(){return this.renderTarget.texture}setAutoResizeEnabled(t){this.autoResize=t}render(t,e,i,s,r){this.fullscreenMaterial.inputBuffer=e.texture,t.setRenderTarget(this.renderToScreen?null:this.renderTarget),t.render(this.scene,this.camera)}setSize(t,e){this.autoResize&&this.renderTarget.setSize(t,e)}initialize(t,e,i){i!==void 0&&(this.renderTarget.texture.type=i,i!==m.UnsignedByteType?this.fullscreenMaterial.defines.FRAMEBUFFER_PRECISION_HIGH="1":Dt(t)===W&&ht(this.renderTarget.texture,W))}},Wr=class extends ot{constructor(){super("ClearMaskPass",null,null),this.needsSwap=!1}render(t,e,i,s,r){const n=t.state.buffers.stencil;n.setLocked(!1),n.setTest(!1)}},Bi=new m.Color,Di=class extends ot{constructor(t=!0,e=!0,i=!1){super("ClearPass",null,null),this.needsSwap=!1,this.color=t,this.depth=e,this.stencil=i,this.overrideClearColor=null,this.overrideClearAlpha=-1}setClearFlags(t,e,i){this.color=t,this.depth=e,this.stencil=i}getOverrideClearColor(){return this.overrideClearColor}setOverrideClearColor(t){this.overrideClearColor=t}getOverrideClearAlpha(){return this.overrideClearAlpha}setOverrideClearAlpha(t){this.overrideClearAlpha=t}render(t,e,i,s,r){const n=this.overrideClearColor,a=this.overrideClearAlpha,h=t.getClearAlpha(),o=n!==null,l=a>=0;o?(t.getClearColor(Bi),t.setClearColor(n,l?a:h)):l&&t.setClearAlpha(a),t.setRenderTarget(this.renderToScreen?null:e),t.clear(this.color,this.depth,this.stencil),o?t.setClearColor(Bi,h):l&&t.setClearAlpha(h)}},Xr=class extends ot{constructor(t,e,i=null){super("RenderPass",t,e),this.needsSwap=!1,this.clearPass=new Di,this.overrideMaterialManager=i===null?null:new ki(i),this.ignoreBackground=!1,this.skipShadowMapUpdate=!1,this.selection=null}set mainScene(t){this.scene=t}set mainCamera(t){this.camera=t}get renderToScreen(){return super.renderToScreen}set renderToScreen(t){super.renderToScreen=t,this.clearPass.renderToScreen=t}get overrideMaterial(){const t=this.overrideMaterialManager;return t!==null?t.material:null}set overrideMaterial(t){const e=this.overrideMaterialManager;t!==null?e!==null?e.setMaterial(t):this.overrideMaterialManager=new ki(t):e!==null&&(e.dispose(),this.overrideMaterialManager=null)}getOverrideMaterial(){return this.overrideMaterial}setOverrideMaterial(t){this.overrideMaterial=t}get clear(){return this.clearPass.enabled}set clear(t){this.clearPass.enabled=t}getSelection(){return this.selection}setSelection(t){this.selection=t}isBackgroundDisabled(){return this.ignoreBackground}setBackgroundDisabled(t){this.ignoreBackground=t}isShadowMapDisabled(){return this.skipShadowMapUpdate}setShadowMapDisabled(t){this.skipShadowMapUpdate=t}getClearPass(){return this.clearPass}render(t,e,i,s,r){const n=this.scene,a=this.camera,h=this.selection,o=a.layers.mask,l=n.background,f=t.shadowMap.autoUpdate,u=this.renderToScreen?null:e;h!==null&&a.layers.set(h.getLayer()),this.skipShadowMapUpdate&&(t.shadowMap.autoUpdate=!1),(this.ignoreBackground||this.clearPass.overrideClearColor!==null)&&(n.background=null),this.clearPass.enabled&&this.clearPass.render(t,e),t.setRenderTarget(u),this.overrideMaterialManager!==null?this.overrideMaterialManager.render(t,n,a):t.render(n,a),a.layers.mask=o,n.background=l,t.shadowMap.autoUpdate=f}};function zi(t,e,i){for(const s of e){const r="$1"+t+s.charAt(0).toUpperCase()+s.slice(1),n=new RegExp("([^\\.])(\\b"+s+"\\b)","g");for(const a of i.entries())a[1]!==null&&i.set(a[0],a[1].replace(n,r))}}function Qr(t,e,i){let s=e.getFragmentShader(),r=e.getVertexShader();const n=s!==void 0&&/mainImage/.test(s),a=s!==void 0&&/mainUv/.test(s);if(i.attributes|=e.getAttributes(),s===void 0)throw new Error(`Missing fragment shader (${e.name})`);if(a&&i.attributes&Nt.CONVOLUTION)throw new Error(`Effects that transform UVs are incompatible with convolution effects (${e.name})`);if(!n&&!a)throw new Error(`Could not find mainImage or mainUv function (${e.name})`);{const h=/\w+\s+(\w+)\([\w\s,]*\)\s*{/g,o=i.shaderParts;let l=o.get(U.FRAGMENT_HEAD)||"",f=o.get(U.FRAGMENT_MAIN_UV)||"",u=o.get(U.FRAGMENT_MAIN_IMAGE)||"",c=o.get(U.VERTEX_HEAD)||"",v=o.get(U.VERTEX_MAIN_SUPPORT)||"";const d=new Set,g=new Set;if(a&&(f+=`	${t}MainUv(UV);
`,i.uvTransformation=!0),r!==null&&/mainSupport/.test(r)){const x=/mainSupport *\([\w\s]*?uv\s*?\)/.test(r);v+=`	${t}MainSupport(`,v+=x?`vUv);
`:`);
`;for(const M of r.matchAll(/(?:varying\s+\w+\s+([\S\s]*?);)/g))for(const _ of M[1].split(/\s*,\s*/))i.varyings.add(_),d.add(_),g.add(_);for(const M of r.matchAll(h))g.add(M[1])}for(const x of s.matchAll(h))g.add(x[1]);for(const x of e.defines.keys())g.add(x.replace(/\([\w\s,]*\)/g,""));for(const x of e.uniforms.keys())g.add(x);g.delete("while"),g.delete("for"),g.delete("if"),e.uniforms.forEach((x,M)=>i.uniforms.set(t+M.charAt(0).toUpperCase()+M.slice(1),x)),e.defines.forEach((x,M)=>i.defines.set(t+M.charAt(0).toUpperCase()+M.slice(1),x));const p=new Map([["fragment",s],["vertex",r]]);zi(t,g,i.defines),zi(t,g,p),s=p.get("fragment"),r=p.get("vertex");const y=e.blendMode;if(i.blendModes.set(y.blendFunction,y),n){e.inputColorSpace!==null&&e.inputColorSpace!==i.colorSpace&&(u+=e.inputColorSpace===W?`color0 = LinearTosRGB(color0);
	`:`color0 = sRGBToLinear(color0);
	`),e.outputColorSpace!==Pi?i.colorSpace=e.outputColorSpace:e.inputColorSpace!==null&&(i.colorSpace=e.inputColorSpace);const x=/MainImage *\([\w\s,]*?depth[\w\s,]*?\)/;u+=`${t}MainImage(color0, UV, `,i.attributes&Nt.DEPTH&&x.test(s)&&(u+="depth, ",i.readDepth=!0),u+=`color1);
	`;const M=t+"BlendOpacity";i.uniforms.set(M,y.opacity),u+=`color0 = blend${y.blendFunction}(color0, color1, ${M});

	`,l+=`uniform float ${M};

`}if(l+=s+`
`,r!==null&&(c+=r+`
`),o.set(U.FRAGMENT_HEAD,l),o.set(U.FRAGMENT_MAIN_UV,f),o.set(U.FRAGMENT_MAIN_IMAGE,u),o.set(U.VERTEX_HEAD,c),o.set(U.VERTEX_MAIN_SUPPORT,v),e.extensions!==null)for(const x of e.extensions)i.extensions.add(x)}}var jr=class extends ot{constructor(t,...e){super("EffectPass"),this.fullscreenMaterial=new Dr(null,null,null,t),this.listener=i=>this.handleEvent(i),this.effects=[],this.setEffects(e),this.skipRendering=!1,this.minTime=1,this.maxTime=Number.POSITIVE_INFINITY,this.timeScale=1}set mainScene(t){for(const e of this.effects)e.mainScene=t}set mainCamera(t){this.fullscreenMaterial.copyCameraSettings(t);for(const e of this.effects)e.mainCamera=t}get encodeOutput(){return this.fullscreenMaterial.encodeOutput}set encodeOutput(t){this.fullscreenMaterial.encodeOutput=t}get dithering(){return this.fullscreenMaterial.dithering}set dithering(t){const e=this.fullscreenMaterial;e.dithering=t,e.needsUpdate=!0}setEffects(t){for(const e of this.effects)e.removeEventListener("change",this.listener);this.effects=t.sort((e,i)=>i.attributes-e.attributes);for(const e of this.effects)e.addEventListener("change",this.listener)}updateMaterial(){const t=new rn;let e=0;for(const a of this.effects)if(a.blendMode.blendFunction===N.DST)t.attributes|=a.getAttributes()&Nt.DEPTH;else{if(t.attributes&a.getAttributes()&Nt.CONVOLUTION)throw new Error(`Convolution effects cannot be merged (${a.name})`);Qr("e"+e++,a,t)}let i=t.shaderParts.get(U.FRAGMENT_HEAD),s=t.shaderParts.get(U.FRAGMENT_MAIN_IMAGE),r=t.shaderParts.get(U.FRAGMENT_MAIN_UV);const n=/\bblend\b/g;for(const a of t.blendModes.values())i+=a.getShaderCode().replace(n,`blend${a.blendFunction}`)+`
`;t.attributes&Nt.DEPTH?(t.readDepth&&(s=`float depth = readDepth(UV);

	`+s),this.needsDepthTexture=this.getDepthTexture()===null):this.needsDepthTexture=!1,t.colorSpace===W&&(s+=`color0 = sRGBToLinear(color0);
	`),t.uvTransformation?(r=`vec2 transformedUv = vUv;
`+r,t.defines.set("UV","transformedUv")):t.defines.set("UV","vUv"),t.shaderParts.set(U.FRAGMENT_HEAD,i),t.shaderParts.set(U.FRAGMENT_MAIN_IMAGE,s),t.shaderParts.set(U.FRAGMENT_MAIN_UV,r);for(const[a,h]of t.shaderParts)h!==null&&t.shaderParts.set(a,h.trim().replace(/^#/,`
#`));this.skipRendering=e===0,this.needsSwap=!this.skipRendering,this.fullscreenMaterial.setShaderData(t)}recompile(){this.updateMaterial()}getDepthTexture(){return this.fullscreenMaterial.depthBuffer}setDepthTexture(t,e=m.BasicDepthPacking){this.fullscreenMaterial.depthBuffer=t,this.fullscreenMaterial.depthPacking=e;for(const i of this.effects)i.setDepthTexture(t,e)}render(t,e,i,s,r){for(const n of this.effects)n.update(t,e,s);if(!this.skipRendering||this.renderToScreen){const n=this.fullscreenMaterial;n.inputBuffer=e.texture,n.time+=s*this.timeScale,t.setRenderTarget(this.renderToScreen?null:i),t.render(this.scene,this.camera)}}setSize(t,e){this.fullscreenMaterial.setSize(t,e);for(const i of this.effects)i.setSize(t,e)}initialize(t,e,i){this.renderer=t;for(const s of this.effects)s.initialize(t,e,i);this.updateMaterial(),i!==void 0&&i!==m.UnsignedByteType&&(this.fullscreenMaterial.defines.FRAMEBUFFER_PRECISION_HIGH="1")}dispose(){super.dispose();for(const t of this.effects)t.removeEventListener("change",this.listener),t.dispose()}handleEvent(t){switch(t.type){case"change":this.recompile();break}}},Kr=class extends ot{constructor({kernelSize:t=ye.MEDIUM,resolutionScale:e=.5,width:i=dt.AUTO_SIZE,height:s=dt.AUTO_SIZE,resolutionX:r=i,resolutionY:n=s}={}){super("KawaseBlurPass"),this.renderTargetA=new m.WebGLRenderTarget(1,1,{depthBuffer:!1}),this.renderTargetA.texture.name="Blur.Target.A",this.renderTargetB=this.renderTargetA.clone(),this.renderTargetB.texture.name="Blur.Target.B";const a=this.resolution=new dt(this,r,n,e);a.addEventListener("change",h=>this.setSize(a.baseWidth,a.baseHeight)),this._blurMaterial=new Ir,this._blurMaterial.kernelSize=t,this.copyMaterial=new Ui}getResolution(){return this.resolution}get blurMaterial(){return this._blurMaterial}set blurMaterial(t){this._blurMaterial=t}get dithering(){return this.copyMaterial.dithering}set dithering(t){this.copyMaterial.dithering=t}get kernelSize(){return this.blurMaterial.kernelSize}set kernelSize(t){this.blurMaterial.kernelSize=t}get width(){return this.resolution.width}set width(t){this.resolution.preferredWidth=t}get height(){return this.resolution.height}set height(t){this.resolution.preferredHeight=t}get scale(){return this.blurMaterial.scale}set scale(t){this.blurMaterial.scale=t}getScale(){return this.blurMaterial.scale}setScale(t){this.blurMaterial.scale=t}getKernelSize(){return this.kernelSize}setKernelSize(t){this.kernelSize=t}getResolutionScale(){return this.resolution.scale}setResolutionScale(t){this.resolution.scale=t}render(t,e,i,s,r){const n=this.scene,a=this.camera,h=this.renderTargetA,o=this.renderTargetB,l=this.blurMaterial,f=l.kernelSequence;let u=e;this.fullscreenMaterial=l;for(let c=0,v=f.length;c<v;++c){const d=c&1?o:h;l.kernel=f[c],l.inputBuffer=u.texture,t.setRenderTarget(d),t.render(n,a),u=d}this.fullscreenMaterial=this.copyMaterial,this.copyMaterial.inputBuffer=u.texture,t.setRenderTarget(this.renderToScreen?null:i),t.render(n,a)}setSize(t,e){const i=this.resolution;i.setBaseSize(t,e);const s=i.width,r=i.height;this.renderTargetA.setSize(s,r),this.renderTargetB.setSize(s,r),this.blurMaterial.setSize(t,e)}initialize(t,e,i){i!==void 0&&(this.renderTargetA.texture.type=i,this.renderTargetB.texture.type=i,i!==m.UnsignedByteType?(this.blurMaterial.defines.FRAMEBUFFER_PRECISION_HIGH="1",this.copyMaterial.defines.FRAMEBUFFER_PRECISION_HIGH="1"):Dt(t)===W&&(ht(this.renderTargetA.texture,W),ht(this.renderTargetB.texture,W)))}static get AUTO_SIZE(){return dt.AUTO_SIZE}},Yr=class extends ot{constructor({renderTarget:t,luminanceRange:e,colorOutput:i,resolutionScale:s=1,width:r=dt.AUTO_SIZE,height:n=dt.AUTO_SIZE,resolutionX:a=r,resolutionY:h=n}={}){super("LuminancePass"),this.fullscreenMaterial=new kr(i,e),this.needsSwap=!1,this.renderTarget=t,this.renderTarget===void 0&&(this.renderTarget=new m.WebGLRenderTarget(1,1,{depthBuffer:!1}),this.renderTarget.texture.name="LuminancePass.Target");const o=this.resolution=new dt(this,a,h,s);o.addEventListener("change",l=>this.setSize(o.baseWidth,o.baseHeight))}get texture(){return this.renderTarget.texture}getTexture(){return this.renderTarget.texture}getResolution(){return this.resolution}render(t,e,i,s,r){const n=this.fullscreenMaterial;n.inputBuffer=e.texture,t.setRenderTarget(this.renderToScreen?null:this.renderTarget),t.render(this.scene,this.camera)}setSize(t,e){const i=this.resolution;i.setBaseSize(t,e),this.renderTarget.setSize(i.width,i.height)}initialize(t,e,i){i!==void 0&&i!==m.UnsignedByteType&&(this.renderTarget.texture.type=i,this.fullscreenMaterial.defines.FRAMEBUFFER_PRECISION_HIGH="1")}},Zr=class extends ot{constructor(t,e){super("MaskPass",t,e),this.needsSwap=!1,this.clearPass=new Di(!1,!1,!0),this.inverse=!1}set mainScene(t){this.scene=t}set mainCamera(t){this.camera=t}get inverted(){return this.inverse}set inverted(t){this.inverse=t}get clear(){return this.clearPass.enabled}set clear(t){this.clearPass.enabled=t}getClearPass(){return this.clearPass}isInverted(){return this.inverted}setInverted(t){this.inverted=t}render(t,e,i,s,r){const n=t.getContext(),a=t.state.buffers,h=this.scene,o=this.camera,l=this.clearPass,f=this.inverted?0:1,u=1-f;a.color.setMask(!1),a.depth.setMask(!1),a.color.setLocked(!0),a.depth.setLocked(!0),a.stencil.setTest(!0),a.stencil.setOp(n.REPLACE,n.REPLACE,n.REPLACE),a.stencil.setFunc(n.ALWAYS,f,4294967295),a.stencil.setClear(u),a.stencil.setLocked(!0),this.clearPass.enabled&&(this.renderToScreen?l.render(t,null):(l.render(t,e),l.render(t,i))),this.renderToScreen?(t.setRenderTarget(null),t.render(h,o)):(t.setRenderTarget(e),t.render(h,o),t.setRenderTarget(i),t.render(h,o)),a.color.setLocked(!1),a.depth.setLocked(!1),a.stencil.setLocked(!1),a.stencil.setFunc(n.EQUAL,1,4294967295),a.stencil.setOp(n.KEEP,n.KEEP,n.KEEP),a.stencil.setLocked(!0)}},Jr=class extends ot{constructor(){super("MipmapBlurPass"),this.needsSwap=!1,this.renderTarget=new m.WebGLRenderTarget(1,1,{depthBuffer:!1}),this.renderTarget.texture.name="Upsampling.Mipmap0",this.downsamplingMipmaps=[],this.upsamplingMipmaps=[],this.downsamplingMaterial=new Or,this.upsamplingMaterial=new Hr,this.resolution=new m.Vector2}get texture(){return this.renderTarget.texture}get levels(){return this.downsamplingMipmaps.length}set levels(t){if(this.levels!==t){const e=this.renderTarget;this.dispose(),this.downsamplingMipmaps=[],this.upsamplingMipmaps=[];for(let i=0;i<t;++i){const s=e.clone();s.texture.name="Downsampling.Mipmap"+i,this.downsamplingMipmaps.push(s)}this.upsamplingMipmaps.push(e);for(let i=1,s=t-1;i<s;++i){const r=e.clone();r.texture.name="Upsampling.Mipmap"+i,this.upsamplingMipmaps.push(r)}this.setSize(this.resolution.x,this.resolution.y)}}get radius(){return this.upsamplingMaterial.radius}set radius(t){this.upsamplingMaterial.radius=t}render(t,e,i,s,r){const{scene:n,camera:a}=this,{downsamplingMaterial:h,upsamplingMaterial:o}=this,{downsamplingMipmaps:l,upsamplingMipmaps:f}=this;let u=e;this.fullscreenMaterial=h;for(let c=0,v=l.length;c<v;++c){const d=l[c];h.setSize(u.width,u.height),h.inputBuffer=u.texture,t.setRenderTarget(d),t.render(n,a),u=d}this.fullscreenMaterial=o;for(let c=f.length-1;c>=0;--c){const v=f[c];o.setSize(u.width,u.height),o.inputBuffer=u.texture,o.supportBuffer=l[c].texture,t.setRenderTarget(v),t.render(n,a),u=v}}setSize(t,e){const i=this.resolution;i.set(t,e);let s=i.width,r=i.height;for(let n=0,a=this.downsamplingMipmaps.length;n<a;++n)s=Math.round(s*.5),r=Math.round(r*.5),this.downsamplingMipmaps[n].setSize(s,r),n<this.upsamplingMipmaps.length&&this.upsamplingMipmaps[n].setSize(s,r)}initialize(t,e,i){if(i!==void 0){const s=this.downsamplingMipmaps.concat(this.upsamplingMipmaps);for(const r of s)r.texture.type=i;if(i!==m.UnsignedByteType)this.downsamplingMaterial.defines.FRAMEBUFFER_PRECISION_HIGH="1",this.upsamplingMaterial.defines.FRAMEBUFFER_PRECISION_HIGH="1";else if(Dt(t)===W)for(const r of s)ht(r.texture,W)}}dispose(){super.dispose();for(const t of this.downsamplingMipmaps.concat(this.upsamplingMipmaps))t.dispose()}},Ve=1/1e3,tn=1e3,en=class{constructor(){this.startTime=performance.now(),this.previousTime=0,this.currentTime=0,this._delta=0,this._elapsed=0,this._fixedDelta=1e3/60,this.timescale=1,this.useFixedDelta=!1,this._autoReset=!1}get autoReset(){return this._autoReset}set autoReset(t){typeof document<"u"&&document.hidden!==void 0&&(t?document.addEventListener("visibilitychange",this):document.removeEventListener("visibilitychange",this),this._autoReset=t)}get delta(){return this._delta*Ve}get fixedDelta(){return this._fixedDelta*Ve}set fixedDelta(t){this._fixedDelta=t*tn}get elapsed(){return this._elapsed*Ve}update(t){this.useFixedDelta?this._delta=this.fixedDelta:(this.previousTime=this.currentTime,this.currentTime=(t!==void 0?t:performance.now())-this.startTime,this._delta=this.currentTime-this.previousTime),this._delta*=this.timescale,this._elapsed+=this._delta}reset(){this._delta=0,this._elapsed=0,this.currentTime=performance.now()-this.startTime}handleEvent(t){document.hidden||(this.currentTime=performance.now()-this.startTime)}dispose(){this.autoReset=!1}},sn=class{constructor(t=null,{depthBuffer:e=!0,stencilBuffer:i=!1,multisampling:s=0,frameBufferType:r}={}){this.renderer=null,this.inputBuffer=this.createBuffer(e,i,r,s),this.outputBuffer=this.inputBuffer.clone(),this.copyPass=new $r,this.depthTexture=null,this.passes=[],this.timer=new en,this.autoRenderToScreen=!0,this.setRenderer(t)}get multisampling(){return this.inputBuffer.samples||0}set multisampling(t){const e=this.inputBuffer,i=this.multisampling;i>0&&t>0?(this.inputBuffer.samples=t,this.outputBuffer.samples=t,this.inputBuffer.dispose(),this.outputBuffer.dispose()):i!==t&&(this.inputBuffer.dispose(),this.outputBuffer.dispose(),this.inputBuffer=this.createBuffer(e.depthBuffer,e.stencilBuffer,e.texture.type,t),this.inputBuffer.depthTexture=this.depthTexture,this.outputBuffer=this.inputBuffer.clone())}getTimer(){return this.timer}getRenderer(){return this.renderer}setRenderer(t){if(this.renderer=t,t!==null){const e=t.getSize(new m.Vector2),i=t.getContext().getContextAttributes().alpha,s=this.inputBuffer.texture.type;s===m.UnsignedByteType&&Dt(t)===W&&(ht(this.inputBuffer.texture,W),ht(this.outputBuffer.texture,W),this.inputBuffer.dispose(),this.outputBuffer.dispose()),t.autoClear=!1,this.setSize(e.width,e.height);for(const r of this.passes)r.initialize(t,i,s)}}replaceRenderer(t,e=!0){const i=this.renderer,s=i.domElement.parentNode;return this.setRenderer(t),e&&s!==null&&(s.removeChild(i.domElement),s.appendChild(t.domElement)),i}createDepthTexture(){const t=this.depthTexture=new m.DepthTexture;return this.inputBuffer.depthTexture=t,this.inputBuffer.dispose(),this.inputBuffer.stencilBuffer?(t.format=m.DepthStencilFormat,t.type=m.UnsignedInt248Type):t.type=m.UnsignedIntType,t}deleteDepthTexture(){if(this.depthTexture!==null){this.depthTexture.dispose(),this.depthTexture=null,this.inputBuffer.depthTexture=null,this.inputBuffer.dispose();for(const t of this.passes)t.setDepthTexture(null)}}createBuffer(t,e,i,s){const r=this.renderer,n=r===null?new m.Vector2:r.getDrawingBufferSize(new m.Vector2),a={minFilter:m.LinearFilter,magFilter:m.LinearFilter,stencilBuffer:e,depthBuffer:t,type:i},h=new m.WebGLRenderTarget(n.width,n.height,a);return s>0&&(h.ignoreDepthForMultisampleCopy=!1,h.samples=s),i===m.UnsignedByteType&&Dt(r)===W&&ht(h.texture,W),h.texture.name="EffectComposer.Buffer",h.texture.generateMipmaps=!1,h}setMainScene(t){for(const e of this.passes)e.mainScene=t}setMainCamera(t){for(const e of this.passes)e.mainCamera=t}addPass(t,e){const i=this.passes,s=this.renderer,r=s.getDrawingBufferSize(new m.Vector2),n=s.getContext().getContextAttributes().alpha,a=this.inputBuffer.texture.type;if(t.setRenderer(s),t.setSize(r.width,r.height),t.initialize(s,n,a),this.autoRenderToScreen&&(i.length>0&&(i[i.length-1].renderToScreen=!1),t.renderToScreen&&(this.autoRenderToScreen=!1)),e!==void 0?i.splice(e,0,t):i.push(t),this.autoRenderToScreen&&(i[i.length-1].renderToScreen=!0),t.needsDepthTexture||this.depthTexture!==null)if(this.depthTexture===null){const h=this.createDepthTexture();for(t of i)t.setDepthTexture(h)}else t.setDepthTexture(this.depthTexture)}removePass(t){const e=this.passes,i=e.indexOf(t);if(i!==-1&&e.splice(i,1).length>0){if(this.depthTexture!==null){const n=(h,o)=>h||o.needsDepthTexture;e.reduce(n,!1)||(t.getDepthTexture()===this.depthTexture&&t.setDepthTexture(null),this.deleteDepthTexture())}this.autoRenderToScreen&&i===e.length&&(t.renderToScreen=!1,e.length>0&&(e[e.length-1].renderToScreen=!0))}}removeAllPasses(){const t=this.passes;this.deleteDepthTexture(),t.length>0&&(this.autoRenderToScreen&&(t[t.length-1].renderToScreen=!1),this.passes=[])}render(t){const e=this.renderer,i=this.copyPass;let s=this.inputBuffer,r=this.outputBuffer,n=!1,a,h,o;t===void 0&&(this.timer.update(),t=this.timer.delta);for(const l of this.passes)l.enabled&&(l.render(e,s,r,t,n),l.needsSwap&&(n&&(i.renderToScreen=l.renderToScreen,a=e.getContext(),h=e.state.buffers.stencil,h.setFunc(a.NOTEQUAL,1,4294967295),i.render(e,s,r,t,n),h.setFunc(a.EQUAL,1,4294967295)),o=s,s=r,r=o),l instanceof Zr?n=!0:l instanceof Wr&&(n=!1))}setSize(t,e,i){const s=this.renderer,r=s.getSize(new m.Vector2);(t===void 0||e===void 0)&&(t=r.width,e=r.height),(r.width!==t||r.height!==e)&&s.setSize(t,e,i);const n=s.getDrawingBufferSize(new m.Vector2);this.inputBuffer.setSize(n.width,n.height),this.outputBuffer.setSize(n.width,n.height);for(const a of this.passes)a.setSize(n.width,n.height)}reset(){const t=this.timer.autoReset;this.dispose(),this.autoRenderToScreen=!0,this.timer.autoReset=t}dispose(){for(const t of this.passes)t.dispose();this.passes=[],this.inputBuffer!==null&&this.inputBuffer.dispose(),this.outputBuffer!==null&&this.outputBuffer.dispose(),this.deleteDepthTexture(),this.copyPass.dispose(),this.timer.dispose()}},rn=class{constructor(){this.shaderParts=new Map([[U.FRAGMENT_HEAD,null],[U.FRAGMENT_MAIN_UV,null],[U.FRAGMENT_MAIN_IMAGE,null],[U.VERTEX_HEAD,null],[U.VERTEX_MAIN_SUPPORT,null]]),this.defines=new Map,this.uniforms=new Map,this.blendModes=new Map,this.extensions=new Set,this.attributes=Nt.NONE,this.varyings=new Set,this.uvTransformation=!1,this.readDepth=!1,this.colorSpace=Bt}},$e=!1,ki=class{constructor(t=null){this.originalMaterials=new Map,this.material=null,this.materials=null,this.materialsBackSide=null,this.materialsDoubleSide=null,this.materialsFlatShaded=null,this.materialsFlatShadedBackSide=null,this.materialsFlatShadedDoubleSide=null,this.setMaterial(t),this.meshCount=0,this.replaceMaterial=e=>{if(e.isMesh){let i;if(e.material.flatShading)switch(e.material.side){case m.DoubleSide:i=this.materialsFlatShadedDoubleSide;break;case m.BackSide:i=this.materialsFlatShadedBackSide;break;default:i=this.materialsFlatShaded;break}else switch(e.material.side){case m.DoubleSide:i=this.materialsDoubleSide;break;case m.BackSide:i=this.materialsBackSide;break;default:i=this.materials;break}this.originalMaterials.set(e,e.material),e.isSkinnedMesh?e.material=i[2]:e.isInstancedMesh?e.material=i[1]:e.material=i[0],++this.meshCount}}}cloneMaterial(t){if(!(t instanceof m.ShaderMaterial))return t.clone();const e=t.uniforms,i=new Map;for(const r in e){const n=e[r].value;n.isRenderTargetTexture&&(e[r].value=null,i.set(r,n))}const s=t.clone();for(const r of i)e[r[0]].value=r[1],s.uniforms[r[0]].value=r[1];return s}setMaterial(t){if(this.disposeMaterials(),this.material=t,t!==null){const e=this.materials=[this.cloneMaterial(t),this.cloneMaterial(t),this.cloneMaterial(t)];for(const i of e)i.uniforms=Object.assign({},t.uniforms),i.side=m.FrontSide;e[2].skinning=!0,this.materialsBackSide=e.map(i=>{const s=this.cloneMaterial(i);return s.uniforms=Object.assign({},t.uniforms),s.side=m.BackSide,s}),this.materialsDoubleSide=e.map(i=>{const s=this.cloneMaterial(i);return s.uniforms=Object.assign({},t.uniforms),s.side=m.DoubleSide,s}),this.materialsFlatShaded=e.map(i=>{const s=this.cloneMaterial(i);return s.uniforms=Object.assign({},t.uniforms),s.flatShading=!0,s}),this.materialsFlatShadedBackSide=e.map(i=>{const s=this.cloneMaterial(i);return s.uniforms=Object.assign({},t.uniforms),s.flatShading=!0,s.side=m.BackSide,s}),this.materialsFlatShadedDoubleSide=e.map(i=>{const s=this.cloneMaterial(i);return s.uniforms=Object.assign({},t.uniforms),s.flatShading=!0,s.side=m.DoubleSide,s})}}render(t,e,i){const s=t.shadowMap.enabled;if(t.shadowMap.enabled=!1,$e){const r=this.originalMaterials;this.meshCount=0,e.traverse(this.replaceMaterial),t.render(e,i);for(const n of r)n[0].material=n[1];this.meshCount!==r.size&&r.clear()}else{const r=e.overrideMaterial;e.overrideMaterial=this.material,t.render(e,i),e.overrideMaterial=r}t.shadowMap.enabled=s}disposeMaterials(){if(this.material!==null){const t=this.materials.concat(this.materialsBackSide).concat(this.materialsDoubleSide).concat(this.materialsFlatShaded).concat(this.materialsFlatShadedBackSide).concat(this.materialsFlatShadedDoubleSide);for(const e of t)e.dispose()}}dispose(){this.originalMaterials.clear(),this.disposeMaterials()}static get workaroundEnabled(){return $e}static set workaroundEnabled(t){$e=t}},St=-1,dt=class extends m.EventDispatcher{constructor(t,e=St,i=St,s=1){super(),this.resizable=t,this.baseSize=new m.Vector2(1,1),this.preferredSize=new m.Vector2(e,i),this.target=this.preferredSize,this.s=s,this.effectiveSize=new m.Vector2,this.addEventListener("change",()=>this.updateEffectiveSize()),this.updateEffectiveSize()}updateEffectiveSize(){const t=this.baseSize,e=this.preferredSize,i=this.effectiveSize,s=this.scale;e.width!==St?i.width=e.width:e.height!==St?i.width=Math.round(e.height*(t.width/Math.max(t.height,1))):i.width=Math.round(t.width*s),e.height!==St?i.height=e.height:e.width!==St?i.height=Math.round(e.width/Math.max(t.width/Math.max(t.height,1),1)):i.height=Math.round(t.height*s)}get width(){return this.effectiveSize.width}set width(t){this.preferredWidth=t}get height(){return this.effectiveSize.height}set height(t){this.preferredHeight=t}getWidth(){return this.width}getHeight(){return this.height}get scale(){return this.s}set scale(t){this.s!==t&&(this.s=t,this.preferredSize.setScalar(St),this.dispatchEvent({type:"change"}),this.resizable.setSize(this.baseSize.width,this.baseSize.height))}getScale(){return this.scale}setScale(t){this.scale=t}get baseWidth(){return this.baseSize.width}set baseWidth(t){this.baseSize.width!==t&&(this.baseSize.width=t,this.dispatchEvent({type:"change"}),this.resizable.setSize(this.baseSize.width,this.baseSize.height))}getBaseWidth(){return this.baseWidth}setBaseWidth(t){this.baseWidth=t}get baseHeight(){return this.baseSize.height}set baseHeight(t){this.baseSize.height!==t&&(this.baseSize.height=t,this.dispatchEvent({type:"change"}),this.resizable.setSize(this.baseSize.width,this.baseSize.height))}getBaseHeight(){return this.baseHeight}setBaseHeight(t){this.baseHeight=t}setBaseSize(t,e){(this.baseSize.width!==t||this.baseSize.height!==e)&&(this.baseSize.set(t,e),this.dispatchEvent({type:"change"}),this.resizable.setSize(this.baseSize.width,this.baseSize.height))}get preferredWidth(){return this.preferredSize.width}set preferredWidth(t){this.preferredSize.width!==t&&(this.preferredSize.width=t,this.dispatchEvent({type:"change"}),this.resizable.setSize(this.baseSize.width,this.baseSize.height))}getPreferredWidth(){return this.preferredWidth}setPreferredWidth(t){this.preferredWidth=t}get preferredHeight(){return this.preferredSize.height}set preferredHeight(t){this.preferredSize.height!==t&&(this.preferredSize.height=t,this.dispatchEvent({type:"change"}),this.resizable.setSize(this.baseSize.width,this.baseSize.height))}getPreferredHeight(){return this.preferredHeight}setPreferredHeight(t){this.preferredHeight=t}setPreferredSize(t,e){(this.preferredSize.width!==t||this.preferredSize.height!==e)&&(this.preferredSize.set(t,e),this.dispatchEvent({type:"change"}),this.resizable.setSize(this.baseSize.width,this.baseSize.height))}copy(t){this.s=t.scale,this.baseSize.set(t.baseWidth,t.baseHeight),this.preferredSize.set(t.preferredWidth,t.preferredHeight),this.dispatchEvent({type:"change"}),this.resizable.setSize(this.baseSize.width,this.baseSize.height)}static get AUTO_SIZE(){return St}},nn=class extends Set{constructor(t,e=10){super(),this.l=e,this.exclusive=!1,t!==void 0&&this.set(t)}get layer(){return this.l}set layer(t){const e=this.l;for(const i of this)i.layers.disable(e),i.layers.enable(t);this.l=t}getLayer(){return this.layer}setLayer(t){this.layer=t}isExclusive(){return this.exclusive}setExclusive(t){this.exclusive=t}clear(){const t=this.layer;for(const e of this)e.layers.disable(t);return super.clear()}set(t){this.clear();for(const e of t)this.add(e);return this}indexOf(t){return this.has(t)?0:-1}add(t){return this.exclusive?t.layers.set(this.layer):t.layers.enable(this.layer),super.add(t)}delete(t){return this.has(t)&&t.layers.disable(this.layer),super.delete(t)}toggle(t){let e;return this.has(t)?(this.delete(t),e=!1):(this.add(t),e=!0),e}setVisible(t){for(const e of this)t?e.layers.enable(0):e.layers.disable(0);return this}},an="vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){return mix(x,x+y,opacity);}",hn="vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){return mix(x,y,min(y.a,opacity));}",on="vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){return mix(x,(x+y)*0.5,opacity);}",ln="vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){vec3 xHSL=RGBToHSL(x.rgb);vec3 yHSL=RGBToHSL(y.rgb);vec3 z=HSLToRGB(vec3(yHSL.rg,xHSL.b));return vec4(mix(x.rgb,z,opacity),y.a);}",un="vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){vec4 z=mix(step(0.0,y)*(1.0-min(vec4(1.0),(1.0-x)/y)),vec4(1.0),step(1.0,x));return mix(x,z,opacity);}",fn="vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){vec4 z=step(0.0,x)*mix(min(vec4(1.0),x/max(1.0-y,1e-9)),vec4(1.0),step(1.0,y));return mix(x,z,opacity);}",cn="vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){return mix(x,min(x,y),opacity);}",vn="vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){return mix(x,abs(x-y),opacity);}",dn="vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){return mix(x,x/max(y,1e-12),opacity);}",mn="vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){return mix(x,(x+y-2.0*x*y),opacity);}",gn="vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){vec4 a=min(x,1.0),b=min(y,1.0);vec4 z=mix(2.0*a*b,1.0-2.0*(1.0-a)*(1.0-b),step(0.5,y));return mix(x,z,opacity);}",pn="vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){return mix(x,step(1.0,x+y),opacity);}",yn="vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){vec3 xHSL=RGBToHSL(x.rgb);vec3 yHSL=RGBToHSL(y.rgb);vec3 z=HSLToRGB(vec3(yHSL.r,xHSL.gb));return vec4(mix(x.rgb,z,opacity),y.a);}",Mn="vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){return mix(x,1.0-y,opacity);}",xn="vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){return mix(x,y*(1.0-x),opacity);}",_n="vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){return mix(x,max(x,y),opacity);}",En="vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){return mix(x,clamp(y+x-1.0,0.0,1.0),opacity);}",Sn="vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){return mix(x,min(x+y,1.0),opacity);}",bn="vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){return mix(x,clamp(2.0*y+x-1.0,0.0,1.0),opacity);}",wn="vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){vec3 xHSL=RGBToHSL(x.rgb);vec3 yHSL=RGBToHSL(y.rgb);vec3 z=HSLToRGB(vec3(xHSL.rg,yHSL.b));return vec4(mix(x.rgb,z,opacity),y.a);}",Tn="vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){return mix(x,x*y,opacity);}",An="vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){return mix(x,1.0-abs(1.0-x-y),opacity);}",Rn="vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){return mix(x,y,opacity);}",Ln="vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){vec4 z=mix(2.0*y*x,1.0-2.0*(1.0-y)*(1.0-x),step(0.5,x));return mix(x,z,opacity);}",In="vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){vec4 y2=2.0*y;vec4 z=mix(mix(y2,x,step(0.5*x,y)),max(vec4(0.0),y2-1.0),step(x,(y2-1.0)));return mix(x,z,opacity);}",Nn="vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){vec4 z=mix(min(x*x/max(1.0-y,1e-12),1.0),y,step(1.0,y));return mix(x,z,opacity);}",Pn="vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){vec3 xHSL=RGBToHSL(x.rgb);vec3 yHSL=RGBToHSL(y.rgb);vec3 z=HSLToRGB(vec3(xHSL.r,yHSL.g,xHSL.b));return vec4(mix(x.rgb,z,opacity),y.a);}",Cn="vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){return mix(x,x+y-min(x*y,1.0),opacity);}",On="vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){vec4 y2=2.0*y;vec4 w=step(0.5,y);vec4 z=mix(x-(1.0-y2)*x*(1.0-x),mix(x+(y2-1.0)*(sqrt(x)-x),x+(y2-1.0)*x*((16.0*x-12.0)*x+3.0),w*(1.0-step(0.25,x))),w);return mix(x,z,opacity);}",Un="vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){return y;}",Bn="vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){return mix(x,max(x+y-1.0,0.0),opacity);}",Dn="vec4 blend(const in vec4 x,const in vec4 y,const in float opacity){vec4 z=mix(max(1.0-min((1.0-x)/(2.0*y),1.0),0.0),min(x/(2.0*(1.0-y)),1.0),step(0.5,y));return mix(x,z,opacity);}",zn=new Map([[N.ADD,an],[N.ALPHA,hn],[N.AVERAGE,on],[N.COLOR,ln],[N.COLOR_BURN,un],[N.COLOR_DODGE,fn],[N.DARKEN,cn],[N.DIFFERENCE,vn],[N.DIVIDE,dn],[N.DST,null],[N.EXCLUSION,mn],[N.HARD_LIGHT,gn],[N.HARD_MIX,pn],[N.HUE,yn],[N.INVERT,Mn],[N.INVERT_RGB,xn],[N.LIGHTEN,_n],[N.LINEAR_BURN,En],[N.LINEAR_DODGE,Sn],[N.LINEAR_LIGHT,bn],[N.LUMINOSITY,wn],[N.MULTIPLY,Tn],[N.NEGATION,An],[N.NORMAL,Rn],[N.OVERLAY,Ln],[N.PIN_LIGHT,In],[N.REFLECT,Nn],[N.SATURATION,Pn],[N.SCREEN,Cn],[N.SOFT_LIGHT,On],[N.SRC,Un],[N.SUBTRACT,Bn],[N.VIVID_LIGHT,Dn]]),kn=class extends m.EventDispatcher{constructor(t,e=1){super(),this._blendFunction=t,this.opacity=new m.Uniform(e)}getOpacity(){return this.opacity.value}setOpacity(t){this.opacity.value=t}get blendFunction(){return this._blendFunction}set blendFunction(t){this._blendFunction=t,this.dispatchEvent({type:"change"})}getBlendFunction(){return this.blendFunction}setBlendFunction(t){this.blendFunction=t}getShaderCode(){return zn.get(this.blendFunction)}},Yt=class extends m.EventDispatcher{constructor(t,e,{attributes:i=Nt.NONE,blendFunction:s=N.NORMAL,defines:r=new Map,uniforms:n=new Map,extensions:a=null,vertexShader:h=null}={}){super(),this.name=t,this.renderer=null,this.attributes=i,this.fragmentShader=e,this.vertexShader=h,this.defines=r,this.uniforms=n,this.extensions=a,this.blendMode=new kn(s),this.blendMode.addEventListener("change",o=>this.setChanged()),this._inputColorSpace=Bt,this._outputColorSpace=Pi}get inputColorSpace(){return this._inputColorSpace}set inputColorSpace(t){this._inputColorSpace=t,this.setChanged()}get outputColorSpace(){return this._outputColorSpace}set outputColorSpace(t){this._outputColorSpace=t,this.setChanged()}set mainScene(t){}set mainCamera(t){}getName(){return this.name}setRenderer(t){this.renderer=t}getDefines(){return this.defines}getUniforms(){return this.uniforms}getExtensions(){return this.extensions}getBlendMode(){return this.blendMode}getAttributes(){return this.attributes}setAttributes(t){this.attributes=t,this.setChanged()}getFragmentShader(){return this.fragmentShader}setFragmentShader(t){this.fragmentShader=t,this.setChanged()}getVertexShader(){return this.vertexShader}setVertexShader(t){this.vertexShader=t,this.setChanged()}setChanged(){this.dispatchEvent({type:"change"})}setDepthTexture(t,e=m.BasicDepthPacking){}update(t,e,i){}setSize(t,e){}initialize(t,e,i){}dispose(){for(const t of Object.keys(this)){const e=this[t];(e instanceof m.WebGLRenderTarget||e instanceof m.Material||e instanceof m.Texture||e instanceof ot)&&this[t].dispose()}}},Fn=`#ifdef FRAMEBUFFER_PRECISION_HIGH
uniform mediump sampler2D map;
#else
uniform lowp sampler2D map;
#endif
uniform float intensity;void mainImage(const in vec4 inputColor,const in vec2 uv,out vec4 outputColor){vec4 texel=texture2D(map,uv);outputColor=vec4(texel.rgb*intensity,texel.a);}`,Gn=class extends Yt{constructor({blendFunction:t=N.SCREEN,luminanceThreshold:e=.9,luminanceSmoothing:i=.025,mipmapBlur:s=!1,intensity:r=1,radius:n=.85,levels:a=8,kernelSize:h=ye.LARGE,resolutionScale:o=.5,width:l=dt.AUTO_SIZE,height:f=dt.AUTO_SIZE,resolutionX:u=l,resolutionY:c=f}={}){super("BloomEffect",Fn,{blendFunction:t,uniforms:new Map([["map",new m.Uniform(null)],["intensity",new m.Uniform(r)]])}),this.renderTarget=new m.WebGLRenderTarget(1,1,{depthBuffer:!1}),this.renderTarget.texture.name="Bloom.Target",this.blurPass=new Kr({kernelSize:h}),this.luminancePass=new Yr({colorOutput:!0}),this.luminanceMaterial.threshold=e,this.luminanceMaterial.smoothing=i,this.mipmapBlurPass=new Jr,this.mipmapBlurPass.enabled=s,this.mipmapBlurPass.radius=n,this.mipmapBlurPass.levels=a,this.uniforms.get("map").value=s?this.mipmapBlurPass.texture:this.renderTarget.texture;const v=this.resolution=new dt(this,u,c,o);v.addEventListener("change",d=>this.setSize(v.baseWidth,v.baseHeight))}get texture(){return this.mipmapBlurPass.enabled?this.mipmapBlurPass.texture:this.renderTarget.texture}getTexture(){return this.texture}getResolution(){return this.resolution}getBlurPass(){return this.blurPass}getLuminancePass(){return this.luminancePass}get luminanceMaterial(){return this.luminancePass.fullscreenMaterial}getLuminanceMaterial(){return this.luminancePass.fullscreenMaterial}get width(){return this.resolution.width}set width(t){this.resolution.preferredWidth=t}get height(){return this.resolution.height}set height(t){this.resolution.preferredHeight=t}get dithering(){return this.blurPass.dithering}set dithering(t){this.blurPass.dithering=t}get kernelSize(){return this.blurPass.kernelSize}set kernelSize(t){this.blurPass.kernelSize=t}get distinction(){return console.warn(this.name,"distinction was removed"),1}set distinction(t){console.warn(this.name,"distinction was removed")}get intensity(){return this.uniforms.get("intensity").value}set intensity(t){this.uniforms.get("intensity").value=t}getIntensity(){return this.intensity}setIntensity(t){this.intensity=t}getResolutionScale(){return this.resolution.scale}setResolutionScale(t){this.resolution.scale=t}update(t,e,i){const s=this.renderTarget,r=this.luminancePass;r.enabled?(r.render(t,e),this.mipmapBlurPass.enabled?this.mipmapBlurPass.render(t,r.renderTarget):this.blurPass.render(t,r.renderTarget,s)):this.mipmapBlurPass.enabled?this.mipmapBlurPass.render(t,e):this.blurPass.render(t,e,s)}setSize(t,e){const i=this.resolution;i.setBaseSize(t,e),this.renderTarget.setSize(i.width,i.height),this.blurPass.resolution.copy(i),this.luminancePass.setSize(t,e),this.mipmapBlurPass.setSize(t,e)}initialize(t,e,i){this.blurPass.initialize(t,e,i),this.luminancePass.initialize(t,e,i),this.mipmapBlurPass.initialize(t,e,i),i!==void 0&&(this.renderTarget.texture.type=i,Dt(t)===W&&ht(this.renderTarget.texture,W))}},Hn=`#if THREE_REVISION < 143
#define luminance(v) linearToRelativeLuminance(v)
#endif
#define QUALITY(q) ((q) < 5 ? 1.0 : ((q) > 5 ? ((q) < 10 ? 2.0 : ((q) < 11 ? 4.0 : 8.0)) : 1.5))
#define ONE_OVER_TWELVE 0.08333333333333333
varying vec2 vUvDown;varying vec2 vUvUp;varying vec2 vUvLeft;varying vec2 vUvRight;varying vec2 vUvDownLeft;varying vec2 vUvUpRight;varying vec2 vUvUpLeft;varying vec2 vUvDownRight;vec4 fxaa(const in vec4 inputColor,const in vec2 uv){float lumaCenter=luminance(inputColor.rgb);float lumaDown=luminance(texture2D(inputBuffer,vUvDown).rgb);float lumaUp=luminance(texture2D(inputBuffer,vUvUp).rgb);float lumaLeft=luminance(texture2D(inputBuffer,vUvLeft).rgb);float lumaRight=luminance(texture2D(inputBuffer,vUvRight).rgb);float lumaMin=min(lumaCenter,min(min(lumaDown,lumaUp),min(lumaLeft,lumaRight)));float lumaMax=max(lumaCenter,max(max(lumaDown,lumaUp),max(lumaLeft,lumaRight)));float lumaRange=lumaMax-lumaMin;if(lumaRange<max(EDGE_THRESHOLD_MIN,lumaMax*EDGE_THRESHOLD_MAX)){return inputColor;}float lumaDownLeft=luminance(texture2D(inputBuffer,vUvDownLeft).rgb);float lumaUpRight=luminance(texture2D(inputBuffer,vUvUpRight).rgb);float lumaUpLeft=luminance(texture2D(inputBuffer,vUvUpLeft).rgb);float lumaDownRight=luminance(texture2D(inputBuffer,vUvDownRight).rgb);float lumaDownUp=lumaDown+lumaUp;float lumaLeftRight=lumaLeft+lumaRight;float lumaLeftCorners=lumaDownLeft+lumaUpLeft;float lumaDownCorners=lumaDownLeft+lumaDownRight;float lumaRightCorners=lumaDownRight+lumaUpRight;float lumaUpCorners=lumaUpRight+lumaUpLeft;float edgeHorizontal=(abs(-2.0*lumaLeft+lumaLeftCorners)+abs(-2.0*lumaCenter+lumaDownUp)*2.0+abs(-2.0*lumaRight+lumaRightCorners));float edgeVertical=(abs(-2.0*lumaUp+lumaUpCorners)+abs(-2.0*lumaCenter+lumaLeftRight)*2.0+abs(-2.0*lumaDown+lumaDownCorners));bool isHorizontal=(edgeHorizontal>=edgeVertical);float stepLength=isHorizontal?texelSize.y:texelSize.x;float luma1=isHorizontal?lumaDown:lumaLeft;float luma2=isHorizontal?lumaUp:lumaRight;float gradient1=abs(luma1-lumaCenter);float gradient2=abs(luma2-lumaCenter);bool is1Steepest=gradient1>=gradient2;float gradientScaled=0.25*max(gradient1,gradient2);float lumaLocalAverage=0.0;if(is1Steepest){stepLength=-stepLength;lumaLocalAverage=0.5*(luma1+lumaCenter);}else{lumaLocalAverage=0.5*(luma2+lumaCenter);}vec2 currentUv=uv;if(isHorizontal){currentUv.y+=stepLength*0.5;}else{currentUv.x+=stepLength*0.5;}vec2 offset=isHorizontal?vec2(texelSize.x,0.0):vec2(0.0,texelSize.y);vec2 uv1=currentUv-offset*QUALITY(0);vec2 uv2=currentUv+offset*QUALITY(0);float lumaEnd1=luminance(texture2D(inputBuffer,uv1).rgb);float lumaEnd2=luminance(texture2D(inputBuffer,uv2).rgb);lumaEnd1-=lumaLocalAverage;lumaEnd2-=lumaLocalAverage;bool reached1=abs(lumaEnd1)>=gradientScaled;bool reached2=abs(lumaEnd2)>=gradientScaled;bool reachedBoth=reached1&&reached2;if(!reached1){uv1-=offset*QUALITY(1);}if(!reached2){uv2+=offset*QUALITY(1);}if(!reachedBoth){for(int i=2;i<SAMPLES;++i){if(!reached1){lumaEnd1=luminance(texture2D(inputBuffer,uv1).rgb);lumaEnd1=lumaEnd1-lumaLocalAverage;}if(!reached2){lumaEnd2=luminance(texture2D(inputBuffer,uv2).rgb);lumaEnd2=lumaEnd2-lumaLocalAverage;}reached1=abs(lumaEnd1)>=gradientScaled;reached2=abs(lumaEnd2)>=gradientScaled;reachedBoth=reached1&&reached2;if(!reached1){uv1-=offset*QUALITY(i);}if(!reached2){uv2+=offset*QUALITY(i);}if(reachedBoth){break;}}}float distance1=isHorizontal?(uv.x-uv1.x):(uv.y-uv1.y);float distance2=isHorizontal?(uv2.x-uv.x):(uv2.y-uv.y);bool isDirection1=distance1<distance2;float distanceFinal=min(distance1,distance2);float edgeThickness=(distance1+distance2);bool isLumaCenterSmaller=lumaCenter<lumaLocalAverage;bool correctVariation1=(lumaEnd1<0.0)!=isLumaCenterSmaller;bool correctVariation2=(lumaEnd2<0.0)!=isLumaCenterSmaller;bool correctVariation=isDirection1?correctVariation1:correctVariation2;float pixelOffset=-distanceFinal/edgeThickness+0.5;float finalOffset=correctVariation?pixelOffset:0.0;float lumaAverage=ONE_OVER_TWELVE*(2.0*(lumaDownUp+lumaLeftRight)+lumaLeftCorners+lumaRightCorners);float subPixelOffset1=clamp(abs(lumaAverage-lumaCenter)/lumaRange,0.0,1.0);float subPixelOffset2=(-2.0*subPixelOffset1+3.0)*subPixelOffset1*subPixelOffset1;float subPixelOffsetFinal=subPixelOffset2*subPixelOffset2*SUBPIXEL_QUALITY;finalOffset=max(finalOffset,subPixelOffsetFinal);vec2 finalUv=uv;if(isHorizontal){finalUv.y+=finalOffset*stepLength;}else{finalUv.x+=finalOffset*stepLength;}return texture2D(inputBuffer,finalUv);}void mainImage(const in vec4 inputColor,const in vec2 uv,out vec4 outputColor){outputColor=fxaa(inputColor,uv);}`,qn="varying vec2 vUvDown;varying vec2 vUvUp;varying vec2 vUvLeft;varying vec2 vUvRight;varying vec2 vUvDownLeft;varying vec2 vUvUpRight;varying vec2 vUvUpLeft;varying vec2 vUvDownRight;void mainSupport(const in vec2 uv){vUvDown=uv+vec2(0.0,-1.0)*texelSize;vUvUp=uv+vec2(0.0,1.0)*texelSize;vUvRight=uv+vec2(1.0,0.0)*texelSize;vUvLeft=uv+vec2(-1.0,0.0)*texelSize;vUvDownLeft=uv+vec2(-1.0,-1.0)*texelSize;vUvUpRight=uv+vec2(1.0,1.0)*texelSize;vUvUpLeft=uv+vec2(-1.0,1.0)*texelSize;vUvDownRight=uv+vec2(1.0,-1.0)*texelSize;}",Vn=class extends Yt{constructor({blendFunction:t=N.SRC}={}){super("FXAAEffect",Hn,{vertexShader:qn,blendFunction:t,defines:new Map([["EDGE_THRESHOLD_MIN","0.0312"],["EDGE_THRESHOLD_MAX","0.125"],["SUBPIXEL_QUALITY","0.75"],["SAMPLES","12"]])})}get minEdgeThreshold(){return Number(this.defines.get("EDGE_THRESHOLD_MIN"))}set minEdgeThreshold(t){this.defines.set("EDGE_THRESHOLD_MIN",t.toFixed(12)),this.setChanged()}get maxEdgeThreshold(){return Number(this.defines.get("EDGE_THRESHOLD_MAX"))}set maxEdgeThreshold(t){this.defines.set("EDGE_THRESHOLD_MAX",t.toFixed(12)),this.setChanged()}get subpixelQuality(){return Number(this.defines.get("SUBPIXEL_QUALITY"))}set subpixelQuality(t){this.defines.set("SUBPIXEL_QUALITY",t.toFixed(12)),this.setChanged()}get samples(){return Number(this.defines.get("SAMPLES"))}set samples(t){this.defines.set("SAMPLES",t.toFixed(0)),this.setChanged()}};function Fi(t,e,i){const s=document.createElement("canvas"),r=s.getContext("2d");if(s.width=t,s.height=e,i instanceof Image)r.drawImage(i,0,0);else{const n=r.createImageData(t,e);n.data.set(i),r.putImageData(n,0,0)}return s}var $n=class gr{constructor(e=0,i=0,s=null){this.width=e,this.height=i,this.data=s}toCanvas(){return typeof document>"u"?null:Fi(this.width,this.height,this.data)}static from(e){const{width:i,height:s}=e;let r;if(e instanceof Image){const n=Fi(i,s,e);n!==null&&(r=n.getContext("2d").getImageData(0,0,i,s).data)}else r=e.data;return new gr(i,s,r)}},Wn=`"use strict";(()=>{var O={SCALE_UP:"lut.scaleup"};var _=[new Float32Array(3),new Float32Array(3)],n=[new Float32Array(3),new Float32Array(3),new Float32Array(3),new Float32Array(3)],Z=[[new Float32Array([0,0,0]),new Float32Array([1,0,0]),new Float32Array([1,1,0]),new Float32Array([1,1,1])],[new Float32Array([0,0,0]),new Float32Array([1,0,0]),new Float32Array([1,0,1]),new Float32Array([1,1,1])],[new Float32Array([0,0,0]),new Float32Array([0,0,1]),new Float32Array([1,0,1]),new Float32Array([1,1,1])],[new Float32Array([0,0,0]),new Float32Array([0,1,0]),new Float32Array([1,1,0]),new Float32Array([1,1,1])],[new Float32Array([0,0,0]),new Float32Array([0,1,0]),new Float32Array([0,1,1]),new Float32Array([1,1,1])],[new Float32Array([0,0,0]),new Float32Array([0,0,1]),new Float32Array([0,1,1]),new Float32Array([1,1,1])]];function d(a,t,r,m){let i=r[0]-t[0],e=r[1]-t[1],y=r[2]-t[2],h=a[0]-t[0],A=a[1]-t[1],w=a[2]-t[2],c=e*w-y*A,l=y*h-i*w,x=i*A-e*h,u=Math.sqrt(c*c+l*l+x*x),b=u*.5,s=c/u,F=l/u,f=x/u,p=-(a[0]*s+a[1]*F+a[2]*f),M=m[0]*s+m[1]*F+m[2]*f;return Math.abs(M+p)*b/3}function V(a,t,r,m,i,e){let y=(r+m*t+i*t*t)*4;e[0]=a[y+0],e[1]=a[y+1],e[2]=a[y+2]}function k(a,t,r,m,i,e){let y=r*(t-1),h=m*(t-1),A=i*(t-1),w=Math.floor(y),c=Math.floor(h),l=Math.floor(A),x=Math.ceil(y),u=Math.ceil(h),b=Math.ceil(A),s=y-w,F=h-c,f=A-l;if(w===y&&c===h&&l===A)V(a,t,y,h,A,e);else{let p;s>=F&&F>=f?p=Z[0]:s>=f&&f>=F?p=Z[1]:f>=s&&s>=F?p=Z[2]:F>=s&&s>=f?p=Z[3]:F>=f&&f>=s?p=Z[4]:f>=F&&F>=s&&(p=Z[5]);let[M,g,X,Y]=p,P=_[0];P[0]=s,P[1]=F,P[2]=f;let o=_[1],L=x-w,S=u-c,U=b-l;o[0]=L*M[0]+w,o[1]=S*M[1]+c,o[2]=U*M[2]+l,V(a,t,o[0],o[1],o[2],n[0]),o[0]=L*g[0]+w,o[1]=S*g[1]+c,o[2]=U*g[2]+l,V(a,t,o[0],o[1],o[2],n[1]),o[0]=L*X[0]+w,o[1]=S*X[1]+c,o[2]=U*X[2]+l,V(a,t,o[0],o[1],o[2],n[2]),o[0]=L*Y[0]+w,o[1]=S*Y[1]+c,o[2]=U*Y[2]+l,V(a,t,o[0],o[1],o[2],n[3]);let T=d(g,X,Y,P)*6,q=d(M,X,Y,P)*6,C=d(M,g,Y,P)*6,E=d(M,g,X,P)*6;n[0][0]*=T,n[0][1]*=T,n[0][2]*=T,n[1][0]*=q,n[1][1]*=q,n[1][2]*=q,n[2][0]*=C,n[2][1]*=C,n[2][2]*=C,n[3][0]*=E,n[3][1]*=E,n[3][2]*=E,e[0]=n[0][0]+n[1][0]+n[2][0]+n[3][0],e[1]=n[0][1]+n[1][1]+n[2][1]+n[3][1],e[2]=n[0][2]+n[1][2]+n[2][2]+n[3][2]}}var v=class{static expand(t,r){let m=Math.cbrt(t.length/4),i=new Float32Array(3),e=new t.constructor(r**3*4),y=t instanceof Uint8Array?255:1,h=r**2,A=1/(r-1);for(let w=0;w<r;++w)for(let c=0;c<r;++c)for(let l=0;l<r;++l){let x=l*A,u=c*A,b=w*A,s=Math.round(l+c*r+w*h)*4;k(t,m,x,u,b,i),e[s+0]=i[0],e[s+1]=i[1],e[s+2]=i[2],e[s+3]=y}return e}};self.addEventListener("message",a=>{let t=a.data,r=t.data;switch(t.operation){case O.SCALE_UP:r=v.expand(r,t.size);break}postMessage(r,[r.buffer]),close()});})();
`,Gi=new m.Color,Hi=class ke extends m.Data3DTexture{constructor(e,i){super(e,i,i,i),this.type=m.FloatType,this.format=m.RGBAFormat,this.minFilter=m.LinearFilter,this.magFilter=m.LinearFilter,this.wrapS=m.ClampToEdgeWrapping,this.wrapT=m.ClampToEdgeWrapping,this.wrapR=m.ClampToEdgeWrapping,this.unpackAlignment=1,this.needsUpdate=!0,ht(this,Bt),this.domainMin=new m.Vector3(0,0,0),this.domainMax=new m.Vector3(1,1,1)}get isLookupTexture3D(){return!0}scaleUp(e,i=!0){const s=this.image;let r;return e<=s.width?r=Promise.reject(new Error("The target size must be greater than the current size")):r=new Promise((n,a)=>{const h=URL.createObjectURL(new Blob([Wn],{type:"text/javascript"})),o=new Worker(h);o.addEventListener("error",f=>a(f.error)),o.addEventListener("message",f=>{const u=new ke(f.data,e);qe(this,u),u.type=this.type,u.name=this.name,URL.revokeObjectURL(h),n(u)});const l=i?[s.data.buffer]:[];o.postMessage({operation:wr.SCALE_UP,data:s.data,size:e},l)}),r}applyLUT(e){const i=this.image,s=e.image,r=Math.min(i.width,i.height,i.depth),n=Math.min(s.width,s.height,s.depth);if(r!==n)console.error("Size mismatch");else if(e.type!==m.FloatType||this.type!==m.FloatType)console.error("Both LUTs must be FloatType textures");else if(e.format!==m.RGBAFormat||this.format!==m.RGBAFormat)console.error("Both LUTs must be RGBA textures");else{const a=i.data,h=s.data,o=r,l=o**2,f=o-1;for(let u=0,c=o**3;u<c;++u){const v=u*4,d=a[v+0]*f,g=a[v+1]*f,p=a[v+2]*f,y=Math.round(d+g*o+p*l)*4;a[v+0]=h[y+0],a[v+1]=h[y+1],a[v+2]=h[y+2]}this.needsUpdate=!0}return this}convertToUint8(){if(this.type===m.FloatType){const e=this.image.data,i=new Uint8Array(e.length);for(let s=0,r=e.length;s<r;++s)i[s]=e[s]*255+.5;this.image.data=i,this.type=m.UnsignedByteType,this.needsUpdate=!0}return this}convertToFloat(){if(this.type===m.UnsignedByteType){const e=this.image.data,i=new Float32Array(e.length);for(let s=0,r=e.length;s<r;++s)i[s]=e[s]/255;this.image.data=i,this.type=m.FloatType,this.needsUpdate=!0}return this}convertToRGBA(){return console.warn("LookupTexture","convertToRGBA() is deprecated, LUTs are now RGBA by default"),this}convertLinearToSRGB(){const e=this.image.data;if(this.type===m.FloatType){for(let i=0,s=e.length;i<s;i+=4)Gi.fromArray(e,i).convertLinearToSRGB().toArray(e,i);ht(this,W),this.needsUpdate=!0}else console.error("Color space conversion requires FloatType data");return this}convertSRGBToLinear(){const e=this.image.data;if(this.type===m.FloatType){for(let i=0,s=e.length;i<s;i+=4)Gi.fromArray(e,i).convertSRGBToLinear().toArray(e,i);ht(this,Bt),this.needsUpdate=!0}else console.error("Color space conversion requires FloatType data");return this}toDataTexture(){const e=this.image.width,i=this.image.height*this.image.depth,s=new m.DataTexture(this.image.data,e,i);return s.name=this.name,s.type=this.type,s.format=this.format,s.minFilter=m.LinearFilter,s.magFilter=m.LinearFilter,s.wrapS=this.wrapS,s.wrapT=this.wrapT,s.generateMipmaps=!1,s.needsUpdate=!0,qe(this,s),s}static from(e){const i=e.image,{width:s,height:r}=i,n=Math.min(s,r);let a;if(i instanceof Image){const l=$n.from(i).data;if(s>r){a=new Uint8Array(l.length);for(let f=0;f<n;++f)for(let u=0;u<n;++u)for(let c=0;c<n;++c){const v=(c+f*n+u*n*n)*4,d=(c+u*n+f*n*n)*4;a[d+0]=l[v+0],a[d+1]=l[v+1],a[d+2]=l[v+2],a[d+3]=l[v+3]}}else a=new Uint8Array(l.buffer)}else a=i.data.slice();const h=new ke(a,n);return h.type=e.type,h.name=e.name,qe(e,h),h}static createNeutral(e){const i=new Float32Array(e**3*4),s=e**2,r=1/(e-1);for(let a=0;a<e;++a)for(let h=0;h<e;++h)for(let o=0;o<e;++o){const l=(a+h*e+o*s)*4;i[l+0]=a*r,i[l+1]=h*r,i[l+2]=o*r,i[l+3]=1}const n=new ke(i,e);return n.name="neutral",n}},Xn=`uniform vec3 scale;uniform vec3 offset;
#ifdef CUSTOM_INPUT_DOMAIN
uniform vec3 domainMin;uniform vec3 domainMax;
#endif
#ifdef LUT_3D
#ifdef LUT_PRECISION_HIGH
#ifdef GL_FRAGMENT_PRECISION_HIGH
uniform highp sampler3D lut;
#else
uniform mediump sampler3D lut;
#endif
#else
uniform lowp sampler3D lut;
#endif
vec4 applyLUT(const in vec3 rgb){
#ifdef TETRAHEDRAL_INTERPOLATION
vec3 p=floor(rgb);vec3 f=rgb-p;vec3 v1=(p+0.5)*LUT_TEXEL_WIDTH;vec3 v4=(p+1.5)*LUT_TEXEL_WIDTH;vec3 v2,v3;vec3 frac;if(f.r>=f.g){if(f.g>f.b){frac=f.rgb;v2=vec3(v4.x,v1.y,v1.z);v3=vec3(v4.x,v4.y,v1.z);}else if(f.r>=f.b){frac=f.rbg;v2=vec3(v4.x,v1.y,v1.z);v3=vec3(v4.x,v1.y,v4.z);}else{frac=f.brg;v2=vec3(v1.x,v1.y,v4.z);v3=vec3(v4.x,v1.y,v4.z);}}else{if(f.b>f.g){frac=f.bgr;v2=vec3(v1.x,v1.y,v4.z);v3=vec3(v1.x,v4.y,v4.z);}else if(f.r>=f.b){frac=f.grb;v2=vec3(v1.x,v4.y,v1.z);v3=vec3(v4.x,v4.y,v1.z);}else{frac=f.gbr;v2=vec3(v1.x,v4.y,v1.z);v3=vec3(v1.x,v4.y,v4.z);}}vec4 n1=texture(lut,v1);vec4 n2=texture(lut,v2);vec4 n3=texture(lut,v3);vec4 n4=texture(lut,v4);vec4 weights=vec4(1.0-frac.x,frac.x-frac.y,frac.y-frac.z,frac.z);vec4 result=weights*mat4(vec4(n1.r,n2.r,n3.r,n4.r),vec4(n1.g,n2.g,n3.g,n4.g),vec4(n1.b,n2.b,n3.b,n4.b),vec4(1.0));return vec4(result.rgb,1.0);
#else
return texture(lut,rgb);
#endif
}
#else
#ifdef LUT_PRECISION_HIGH
#ifdef GL_FRAGMENT_PRECISION_HIGH
uniform highp sampler2D lut;
#else
uniform mediump sampler2D lut;
#endif
#else
uniform lowp sampler2D lut;
#endif
vec4 applyLUT(const in vec3 rgb){float slice=rgb.b*LUT_SIZE;float slice0=floor(slice);float interp=slice-slice0;float centeredInterp=interp-0.5;float slice1=slice0+sign(centeredInterp);
#ifdef LUT_STRIP_HORIZONTAL
float xOffset=clamp(rgb.r*LUT_TEXEL_HEIGHT,LUT_TEXEL_WIDTH*0.5,LUT_TEXEL_HEIGHT-LUT_TEXEL_WIDTH*0.5);vec2 uv0=vec2(slice0*LUT_TEXEL_HEIGHT+xOffset,rgb.g);vec2 uv1=vec2(slice1*LUT_TEXEL_HEIGHT+xOffset,rgb.g);
#else
float yOffset=clamp(rgb.g*LUT_TEXEL_WIDTH,LUT_TEXEL_HEIGHT*0.5,LUT_TEXEL_WIDTH-LUT_TEXEL_HEIGHT*0.5);vec2 uv0=vec2(rgb.r,slice0*LUT_TEXEL_WIDTH+yOffset);vec2 uv1=vec2(rgb.r,slice1*LUT_TEXEL_WIDTH+yOffset);
#endif
vec4 sample0=texture2D(lut,uv0);vec4 sample1=texture2D(lut,uv1);return mix(sample0,sample1,abs(centeredInterp));}
#endif
void mainImage(const in vec4 inputColor,const in vec2 uv,out vec4 outputColor){vec3 c=inputColor.rgb;
#ifdef CUSTOM_INPUT_DOMAIN
if(c.r>=domainMin.r&&c.g>=domainMin.g&&c.b>=domainMin.b&&c.r<=domainMax.r&&c.g<=domainMax.g&&c.b<=domainMax.b){c=applyLUT(scale*c+offset).rgb;}else{c=inputColor.rgb;}
#else
#if !defined(LUT_3D) || defined(TETRAHEDRAL_INTERPOLATION)
c=clamp(c,0.0,1.0);
#endif
c=applyLUT(scale*c+offset).rgb;
#endif
outputColor=vec4(c,inputColor.a);}`,Qn=class extends Yt{constructor(t,{blendFunction:e=N.SRC,tetrahedralInterpolation:i=!1,inputEncoding:s=m.sRGBEncoding,inputColorSpace:r}={}){super("LUT3DEffect",Xn,{blendFunction:e,uniforms:new Map([["lut",new m.Uniform(null)],["scale",new m.Uniform(new m.Vector3)],["offset",new m.Uniform(new m.Vector3)],["domainMin",new m.Uniform(null)],["domainMax",new m.Uniform(null)]])}),this.tetrahedralInterpolation=i,this.inputColorSpace=r||Oi.get(s),this.lut=t}get inputEncoding(){return this.inputColorSpace}set inputEncoding(t){this.inputColorSpace=t}getInputEncoding(){return this.inputColorSpace}setInputEncoding(t){this.inputColorSpace=t}getOutputEncoding(){return this.outputColorSpace}get lut(){return this.uniforms.get("lut").value}set lut(t){const e=this.defines,i=this.uniforms;if(this.lut!==t&&(i.get("lut").value=t,t!==null)){const s=t.image,r=this.tetrahedralInterpolation;if(e.clear(),e.set("LUT_SIZE",Math.min(s.width,s.height).toFixed(16)),e.set("LUT_TEXEL_WIDTH",(1/s.width).toFixed(16)),e.set("LUT_TEXEL_HEIGHT",(1/s.height).toFixed(16)),i.get("domainMin").value=null,i.get("domainMax").value=null,(t.type===m.FloatType||t.type===m.HalfFloatType)&&e.set("LUT_PRECISION_HIGH","1"),s.width>s.height?e.set("LUT_STRIP_HORIZONTAL","1"):t instanceof m.Data3DTexture&&e.set("LUT_3D","1"),t instanceof Hi){const n=t.domainMin,a=t.domainMax;(n.x!==0||n.y!==0||n.z!==0||a.x!==1||a.y!==1||a.z!==1)&&(e.set("CUSTOM_INPUT_DOMAIN","1"),i.get("domainMin").value=n.clone(),i.get("domainMax").value=a.clone())}this.tetrahedralInterpolation=r}}getLUT(){return this.lut}setLUT(t){this.lut=t}updateScaleOffset(){const t=this.lut;if(t!==null){const e=Math.min(t.image.width,t.image.height),i=this.uniforms.get("scale").value,s=this.uniforms.get("offset").value;if(this.tetrahedralInterpolation&&t instanceof m.Data3DTexture)if(this.defines.has("CUSTOM_INPUT_DOMAIN")){const r=t.domainMax.clone().sub(t.domainMin);i.setScalar(e-1).divide(r),s.copy(t.domainMin).negate().multiply(i)}else i.setScalar(e-1),s.setScalar(0);else if(this.defines.has("CUSTOM_INPUT_DOMAIN")){const r=t.domainMax.clone().sub(t.domainMin).multiplyScalar(e);i.setScalar(e-1).divide(r),s.copy(t.domainMin).negate().multiply(i).addScalar(1/(2*e))}else i.setScalar((e-1)/e),s.setScalar(1/(2*e))}}configureTetrahedralInterpolation(){const t=this.lut;t!==null&&(t.minFilter=m.LinearFilter,t.magFilter=m.LinearFilter,this.tetrahedralInterpolation&&(t instanceof m.Data3DTexture?(t.minFilter=m.NearestFilter,t.magFilter=m.NearestFilter):console.warn("Tetrahedral interpolation requires a 3D texture")),t.source===void 0&&(t.needsUpdate=!0))}get tetrahedralInterpolation(){return this.defines.has("TETRAHEDRAL_INTERPOLATION")}set tetrahedralInterpolation(t){t?this.defines.set("TETRAHEDRAL_INTERPOLATION","1"):this.defines.delete("TETRAHEDRAL_INTERPOLATION"),this.configureTetrahedralInterpolation(),this.updateScaleOffset(),this.setChanged()}setTetrahedralInterpolationEnabled(t){this.tetrahedralInterpolation=t}},jn=`uniform float offset;uniform float darkness;void mainImage(const in vec4 inputColor,const in vec2 uv,out vec4 outputColor){const vec2 center=vec2(0.5);vec3 color=inputColor.rgb;
#if VIGNETTE_TECHNIQUE == 0
float d=distance(uv,center);color*=smoothstep(0.8,offset*0.799,d*(darkness+offset));
#else
vec2 coord=(uv-center)*vec2(offset);color=mix(color,vec3(1.0-darkness),dot(coord,coord));
#endif
outputColor=vec4(color,inputColor.a);}`,Kn=class extends Yt{constructor({blendFunction:t,technique:e=Me.DEFAULT,eskil:i=!1,offset:s=.5,darkness:r=.5}={}){super("VignetteEffect",jn,{blendFunction:t,defines:new Map([["VIGNETTE_TECHNIQUE",e.toFixed(0)]]),uniforms:new Map([["offset",new m.Uniform(s)],["darkness",new m.Uniform(r)]])})}get technique(){return Number(this.defines.get("VIGNETTE_TECHNIQUE"))}set technique(t){this.technique!==t&&(this.defines.set("VIGNETTE_TECHNIQUE",t.toFixed(0)),this.setChanged())}get eskil(){return this.technique===Me.ESKIL}set eskil(t){this.technique=t?Me.ESKIL:Me.DEFAULT}getTechnique(){return this.technique}setTechnique(t){this.technique=t}get offset(){return this.uniforms.get("offset").value}set offset(t){this.uniforms.get("offset").value=t}getOffset(){return this.offset}setOffset(t){this.offset=t}get darkness(){return this.uniforms.get("darkness").value}set darkness(t){this.uniforms.get("darkness").value=t}getDarkness(){return this.darkness}setDarkness(t){this.darkness=t}},Yn=class extends m.Loader{load(t,e=()=>{},i=()=>{},s=null){const r=this.manager,n=new m.LoadingManager,a=new m.FileLoader(n);return a.setPath(this.path),a.setResponseType("text"),new Promise((h,o)=>{n.onError=l=>{r.itemError(l),s!==null?(s(`Failed to load ${l}`),h()):o(`Failed to load ${l}`)},r.itemStart(t),a.load(t,l=>{try{const f=this.parse(l);r.itemEnd(t),e(f),h(f)}catch(f){console.error(f),n.onError(t)}},i)})}parse(t){const e=/^[\d ]+$/m,i=/^([\d.e+-]+) +([\d.e+-]+) +([\d.e+-]+) *$/gm;let s=e.exec(t);if(s===null)throw new Error("Missing grid information");const r=s[0].trim().split(/\s+/g).map(v=>Number(v)),n=r[1]-r[0],a=r.length,h=a**2;for(let v=1,d=r.length;v<d;++v)if(n!==r[v]-r[v-1])throw new Error("Inconsistent grid size");const o=new Float32Array(a**3*4);let l=0,f=0;for(;(s=i.exec(t))!==null;){const v=Number(s[1]),d=Number(s[2]),g=Number(s[3]);l=Math.max(l,v,d,g);const p=f%a,y=Math.floor(f/a)%a,x=Math.floor(f/h)%a,M=(p*h+y*a+x)*4;o[M+0]=v,o[M+1]=d,o[M+2]=g,o[M+3]=1,++f}const u=Math.ceil(Math.log2(l)),c=Math.pow(2,u);for(let v=0,d=o.length;v<d;v+=4)o[v+0]/=c,o[v+1]/=c,o[v+2]/=c;return new Hi(o,a)}};function st(t,e){if(!t)throw new Error("math.gl assertion ".concat(e))}const Zn=1/Math.PI*180,Jn=1/180*Math.PI,H={};H.EPSILON=1e-12,H.debug=!1,H.precision=4,H.printTypes=!1,H.printDegrees=!1,H.printRowMajor=!0;function ta(t){return Math.round(t/H.EPSILON)*H.EPSILON}function ea(t,{precision:e=H.precision||4}={}){return t=ta(t),"".concat(parseFloat(t.toPrecision(e)))}function bt(t){return Array.isArray(t)||ArrayBuffer.isView(t)&&!(t instanceof DataView)}function ia(t){return t.clone?t.clone():new Array(t.length)}function qi(t,e,i){if(bt(t)){i=i||ia(t);for(let s=0;s<i.length&&s<t.length;++s)i[s]=e(t[s],s,i);return i}return e(t)}function sa(t){return na(t)}function ra(t){return aa(t)}function na(t,e){return qi(t,i=>i*Jn,e)}function aa(t,e){return qi(t,i=>i*Zn,e)}function Zt(t,e,i){const s=H.EPSILON;i&&(H.EPSILON=i);try{if(t===e)return!0;if(bt(t)&&bt(e)){if(t.length!==e.length)return!1;for(let r=0;r<t.length;++r)if(!Zt(t[r],e[r]))return!1;return!0}return t&&t.equals?t.equals(e):e&&e.equals?e.equals(t):Number.isFinite(t)&&Number.isFinite(e)?Math.abs(t-e)<=H.EPSILON*Math.max(1,Math.abs(t),Math.abs(e)):!1}finally{H.EPSILON=s}}function ha(t){function e(){var i=Reflect.construct(t,Array.from(arguments));return Object.setPrototypeOf(i,Object.getPrototypeOf(this)),i}return e.prototype=Object.create(t.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t,e}class Vi extends ha(Array){get ELEMENTS(){return st(!1),0}clone(){return new this.constructor().copy(this)}from(e){return Array.isArray(e)?this.copy(e):this.fromObject(e)}fromArray(e,i=0){for(let s=0;s<this.ELEMENTS;++s)this[s]=e[s+i];return this.check()}to(e){return e===this?this:bt(e)?this.toArray(e):this.toObject(e)}toTarget(e){return e?this.to(e):this}toArray(e=[],i=0){for(let s=0;s<this.ELEMENTS;++s)e[i+s]=this[s];return e}toFloat32Array(){return new Float32Array(this)}toString(){return this.formatString(H)}formatString(e){let i="";for(let s=0;s<this.ELEMENTS;++s)i+=(s>0?", ":"")+ea(this[s],e);return"".concat(e.printTypes?this.constructor.name:"","[").concat(i,"]")}equals(e){if(!e||this.length!==e.length)return!1;for(let i=0;i<this.ELEMENTS;++i)if(!Zt(this[i],e[i]))return!1;return!0}exactEquals(e){if(!e||this.length!==e.length)return!1;for(let i=0;i<this.ELEMENTS;++i)if(this[i]!==e[i])return!1;return!0}negate(){for(let e=0;e<this.ELEMENTS;++e)this[e]=-this[e];return this.check()}lerp(e,i,s){s===void 0&&(s=i,i=e,e=this);for(let r=0;r<this.ELEMENTS;++r){const n=e[r];this[r]=n+s*(i[r]-n)}return this.check()}min(e){for(let i=0;i<this.ELEMENTS;++i)this[i]=Math.min(e[i],this[i]);return this.check()}max(e){for(let i=0;i<this.ELEMENTS;++i)this[i]=Math.max(e[i],this[i]);return this.check()}clamp(e,i){for(let s=0;s<this.ELEMENTS;++s)this[s]=Math.min(Math.max(this[s],e[s]),i[s]);return this.check()}add(...e){for(const i of e)for(let s=0;s<this.ELEMENTS;++s)this[s]+=i[s];return this.check()}subtract(...e){for(const i of e)for(let s=0;s<this.ELEMENTS;++s)this[s]-=i[s];return this.check()}scale(e){if(Array.isArray(e))return this.multiply(e);for(let i=0;i<this.ELEMENTS;++i)this[i]*=e;return this.check()}sub(e){return this.subtract(e)}setScalar(e){for(let i=0;i<this.ELEMENTS;++i)this[i]=e;return this.check()}addScalar(e){for(let i=0;i<this.ELEMENTS;++i)this[i]+=e;return this.check()}subScalar(e){return this.addScalar(-e)}multiplyScalar(e){for(let i=0;i<this.ELEMENTS;++i)this[i]*=e;return this.check()}divideScalar(e){return this.scale(1/e)}clampScalar(e,i){for(let s=0;s<this.ELEMENTS;++s)this[s]=Math.min(Math.max(this[s],e),i);return this.check()}multiplyByScalar(e){return this.scale(e)}get elements(){return this}check(){if(H.debug&&!this.validate())throw new Error("math.gl: ".concat(this.constructor.name," some fields set to invalid numbers'"));return this}validate(){let e=this.length===this.ELEMENTS;for(let i=0;i<this.ELEMENTS;++i)e=e&&Number.isFinite(this[i]);return e}}function oa(t,e){if(t.length!==e)return!1;for(let i=0;i<t.length;++i)if(!Number.isFinite(t[i]))return!1;return!0}function Q(t){if(!Number.isFinite(t))throw new Error("Invalid number ".concat(t));return t}function We(t,e,i=""){if(H.debug&&!oa(t,e))throw new Error("math.gl: ".concat(i," some fields set to invalid numbers'"));return t}const $i={};function Xe(t,e){$i[t]||($i[t]=!0,console.warn("".concat(t," has been removed in version ").concat(e,", see upgrade guide for more information")))}class Wi extends Vi{get ELEMENTS(){return st(!1),0}copy(e){return st(!1),this}get x(){return this[0]}set x(e){this[0]=Q(e)}get y(){return this[1]}set y(e){this[1]=Q(e)}len(){return Math.sqrt(this.lengthSquared())}magnitude(){return this.len()}lengthSquared(){let e=0;for(let i=0;i<this.ELEMENTS;++i)e+=this[i]*this[i];return e}magnitudeSquared(){return this.lengthSquared()}distance(e){return Math.sqrt(this.distanceSquared(e))}distanceSquared(e){let i=0;for(let s=0;s<this.ELEMENTS;++s){const r=this[s]-e[s];i+=r*r}return Q(i)}dot(e){let i=0;for(let s=0;s<this.ELEMENTS;++s)i+=this[s]*e[s];return Q(i)}normalize(){const e=this.magnitude();if(e!==0)for(let i=0;i<this.ELEMENTS;++i)this[i]/=e;return this.check()}multiply(...e){for(const i of e)for(let s=0;s<this.ELEMENTS;++s)this[s]*=i[s];return this.check()}divide(...e){for(const i of e)for(let s=0;s<this.ELEMENTS;++s)this[s]/=i[s];return this.check()}lengthSq(){return this.lengthSquared()}distanceTo(e){return this.distance(e)}distanceToSquared(e){return this.distanceSquared(e)}getComponent(e){return st(e>=0&&e<this.ELEMENTS,"index is out of range"),Q(this[e])}setComponent(e,i){return st(e>=0&&e<this.ELEMENTS,"index is out of range"),this[e]=i,this.check()}addVectors(e,i){return this.copy(e).add(i)}subVectors(e,i){return this.copy(e).subtract(i)}multiplyVectors(e,i){return this.copy(e).multiply(i)}addScaledVector(e,i){return this.add(new this.constructor(e).multiplyScalar(i))}}var xe=1e-6,zt=typeof Float32Array<"u"?Float32Array:Array;Math.hypot||(Math.hypot=function(){for(var t=0,e=arguments.length;e--;)t+=arguments[e]*arguments[e];return Math.sqrt(t)});function la(){var t=new zt(2);return zt!=Float32Array&&(t[0]=0,t[1]=0),t}function ua(t,e,i){var s=e[0],r=e[1];return t[0]=i[0]*s+i[2]*r,t[1]=i[1]*s+i[3]*r,t}function fa(t,e,i){var s=e[0],r=e[1];return t[0]=i[0]*s+i[2]*r+i[4],t[1]=i[1]*s+i[3]*r+i[5],t}function ca(t,e,i){var s=e[0],r=e[1];return t[0]=i[0]*s+i[3]*r+i[6],t[1]=i[1]*s+i[4]*r+i[7],t}function Xi(t,e,i){var s=e[0],r=e[1];return t[0]=i[0]*s+i[4]*r+i[12],t[1]=i[1]*s+i[5]*r+i[13],t}(function(){var t=la();return function(e,i,s,r,n,a){var h,o;for(i||(i=2),s||(s=0),r?o=Math.min(r*i+s,e.length):o=e.length,h=s;h<o;h+=i)t[0]=e[h],t[1]=e[h+1],n(t,t,a),e[h]=t[0],e[h+1]=t[1];return e}})();function Qi(t,e,i){const s=e[0],r=e[1],n=i[3]*s+i[7]*r||1;return t[0]=(i[0]*s+i[4]*r)/n,t[1]=(i[1]*s+i[5]*r)/n,t}function ji(t,e,i){const s=e[0],r=e[1],n=e[2],a=i[3]*s+i[7]*r+i[11]*n||1;return t[0]=(i[0]*s+i[4]*r+i[8]*n)/a,t[1]=(i[1]*s+i[5]*r+i[9]*n)/a,t[2]=(i[2]*s+i[6]*r+i[10]*n)/a,t}function va(t,e,i){const s=e[0],r=e[1];return t[0]=i[0]*s+i[2]*r,t[1]=i[1]*s+i[3]*r,t[2]=e[2],t}class da extends Wi{constructor(e=0,i=0){super(2),bt(e)&&arguments.length===1?this.copy(e):(H.debug&&(Q(e),Q(i)),this[0]=e,this[1]=i)}set(e,i){return this[0]=e,this[1]=i,this.check()}copy(e){return this[0]=e[0],this[1]=e[1],this.check()}fromObject(e){return H.debug&&(Q(e.x),Q(e.y)),this[0]=e.x,this[1]=e.y,this.check()}toObject(e){return e.x=this[0],e.y=this[1],e}get ELEMENTS(){return 2}horizontalAngle(){return Math.atan2(this.y,this.x)}verticalAngle(){return Math.atan2(this.x,this.y)}transform(e){return this.transformAsPoint(e)}transformAsPoint(e){return Xi(this,this,e),this.check()}transformAsVector(e){return Qi(this,this,e),this.check()}transformByMatrix3(e){return ca(this,this,e),this.check()}transformByMatrix2x3(e){return fa(this,this,e),this.check()}transformByMatrix2(e){return ua(this,this,e),this.check()}}function ma(){var t=new zt(3);return zt!=Float32Array&&(t[0]=0,t[1]=0,t[2]=0),t}function ga(t){var e=t[0],i=t[1],s=t[2];return Math.hypot(e,i,s)}function Ki(t,e){return t[0]*e[0]+t[1]*e[1]+t[2]*e[2]}function pa(t,e,i){var s=e[0],r=e[1],n=e[2],a=i[0],h=i[1],o=i[2];return t[0]=r*o-n*h,t[1]=n*a-s*o,t[2]=s*h-r*a,t}function Yi(t,e,i){var s=e[0],r=e[1],n=e[2],a=i[3]*s+i[7]*r+i[11]*n+i[15];return a=a||1,t[0]=(i[0]*s+i[4]*r+i[8]*n+i[12])/a,t[1]=(i[1]*s+i[5]*r+i[9]*n+i[13])/a,t[2]=(i[2]*s+i[6]*r+i[10]*n+i[14])/a,t}function ya(t,e,i){var s=e[0],r=e[1],n=e[2];return t[0]=s*i[0]+r*i[3]+n*i[6],t[1]=s*i[1]+r*i[4]+n*i[7],t[2]=s*i[2]+r*i[5]+n*i[8],t}function Ma(t,e,i){var s=i[0],r=i[1],n=i[2],a=i[3],h=e[0],o=e[1],l=e[2],f=r*l-n*o,u=n*h-s*l,c=s*o-r*h,v=r*c-n*u,d=n*f-s*c,g=s*u-r*f,p=a*2;return f*=p,u*=p,c*=p,v*=2,d*=2,g*=2,t[0]=h+f+v,t[1]=o+u+d,t[2]=l+c+g,t}function xa(t,e,i,s){var r=[],n=[];return r[0]=e[0]-i[0],r[1]=e[1]-i[1],r[2]=e[2]-i[2],n[0]=r[0],n[1]=r[1]*Math.cos(s)-r[2]*Math.sin(s),n[2]=r[1]*Math.sin(s)+r[2]*Math.cos(s),t[0]=n[0]+i[0],t[1]=n[1]+i[1],t[2]=n[2]+i[2],t}function _a(t,e,i,s){var r=[],n=[];return r[0]=e[0]-i[0],r[1]=e[1]-i[1],r[2]=e[2]-i[2],n[0]=r[2]*Math.sin(s)+r[0]*Math.cos(s),n[1]=r[1],n[2]=r[2]*Math.cos(s)-r[0]*Math.sin(s),t[0]=n[0]+i[0],t[1]=n[1]+i[1],t[2]=n[2]+i[2],t}function Ea(t,e,i,s){var r=[],n=[];return r[0]=e[0]-i[0],r[1]=e[1]-i[1],r[2]=e[2]-i[2],n[0]=r[0]*Math.cos(s)-r[1]*Math.sin(s),n[1]=r[0]*Math.sin(s)+r[1]*Math.cos(s),n[2]=r[2],t[0]=n[0]+i[0],t[1]=n[1]+i[1],t[2]=n[2]+i[2],t}function Sa(t,e){var i=t[0],s=t[1],r=t[2],n=e[0],a=e[1],h=e[2],o=Math.sqrt(i*i+s*s+r*r),l=Math.sqrt(n*n+a*a+h*h),f=o*l,u=f&&Ki(t,e)/f;return Math.acos(Math.min(Math.max(u,-1),1))}(function(){var t=ma();return function(e,i,s,r,n,a){var h,o;for(i||(i=3),s||(s=0),r?o=Math.min(r*i+s,e.length):o=e.length,h=s;h<o;h+=i)t[0]=e[h],t[1]=e[h+1],t[2]=e[h+2],n(t,t,a),e[h]=t[0],e[h+1]=t[1],e[h+2]=t[2];return e}})();const Qe=[0,0,0],Zi={};class z extends Wi{static get ZERO(){return Zi.ZERO=Zi.ZERO||Object.freeze(new z(0,0,0,0))}constructor(e=0,i=0,s=0){super(-0,-0,-0),arguments.length===1&&bt(e)?this.copy(e):(H.debug&&(Q(e),Q(i),Q(s)),this[0]=e,this[1]=i,this[2]=s)}set(e,i,s){return this[0]=e,this[1]=i,this[2]=s,this.check()}copy(e){return this[0]=e[0],this[1]=e[1],this[2]=e[2],this.check()}fromObject(e){return H.debug&&(Q(e.x),Q(e.y),Q(e.z)),this[0]=e.x,this[1]=e.y,this[2]=e.z,this.check()}toObject(e){return e.x=this[0],e.y=this[1],e.z=this[2],e}get ELEMENTS(){return 3}get z(){return this[2]}set z(e){this[2]=Q(e)}angle(e){return Sa(this,e)}cross(e){return pa(this,this,e),this.check()}rotateX({radians:e,origin:i=Qe}){return xa(this,this,i,e),this.check()}rotateY({radians:e,origin:i=Qe}){return _a(this,this,i,e),this.check()}rotateZ({radians:e,origin:i=Qe}){return Ea(this,this,i,e),this.check()}transform(e){return this.transformAsPoint(e)}transformAsPoint(e){return Yi(this,this,e),this.check()}transformAsVector(e){return ji(this,this,e),this.check()}transformByMatrix3(e){return ya(this,this,e),this.check()}transformByMatrix2(e){return va(this,this,e),this.check()}transformByQuaternion(e){return Ma(this,this,e),this.check()}}class ba extends Vi{get ELEMENTS(){return st(!1),0}get RANK(){return st(!1),0}toString(){let e="[";if(H.printRowMajor){e+="row-major:";for(let i=0;i<this.RANK;++i)for(let s=0;s<this.RANK;++s)e+=" ".concat(this[s*this.RANK+i])}else{e+="column-major:";for(let i=0;i<this.ELEMENTS;++i)e+=" ".concat(this[i])}return e+="]",e}getElementIndex(e,i){return i*this.RANK+e}getElement(e,i){return this[i*this.RANK+e]}setElement(e,i,s){return this[i*this.RANK+e]=Q(s),this}getColumn(e,i=new Array(this.RANK).fill(-0)){const s=e*this.RANK;for(let r=0;r<this.RANK;++r)i[r]=this[s+r];return i}setColumn(e,i){const s=e*this.RANK;for(let r=0;r<this.RANK;++r)this[s+r]=i[r];return this}}function wa(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=1,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=1,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t}function Ta(t,e){if(t===e){var i=e[1],s=e[2],r=e[3],n=e[6],a=e[7],h=e[11];t[1]=e[4],t[2]=e[8],t[3]=e[12],t[4]=i,t[6]=e[9],t[7]=e[13],t[8]=s,t[9]=n,t[11]=e[14],t[12]=r,t[13]=a,t[14]=h}else t[0]=e[0],t[1]=e[4],t[2]=e[8],t[3]=e[12],t[4]=e[1],t[5]=e[5],t[6]=e[9],t[7]=e[13],t[8]=e[2],t[9]=e[6],t[10]=e[10],t[11]=e[14],t[12]=e[3],t[13]=e[7],t[14]=e[11],t[15]=e[15];return t}function Aa(t,e){var i=e[0],s=e[1],r=e[2],n=e[3],a=e[4],h=e[5],o=e[6],l=e[7],f=e[8],u=e[9],c=e[10],v=e[11],d=e[12],g=e[13],p=e[14],y=e[15],x=i*h-s*a,M=i*o-r*a,_=i*l-n*a,S=s*o-r*h,b=s*l-n*h,L=r*l-n*o,A=f*g-u*d,I=f*p-c*d,C=f*y-v*d,O=u*p-c*g,F=u*y-v*g,V=c*y-v*p,P=x*V-M*F+_*O+S*C-b*I+L*A;return P?(P=1/P,t[0]=(h*V-o*F+l*O)*P,t[1]=(r*F-s*V-n*O)*P,t[2]=(g*L-p*b+y*S)*P,t[3]=(c*b-u*L-v*S)*P,t[4]=(o*C-a*V-l*I)*P,t[5]=(i*V-r*C+n*I)*P,t[6]=(p*_-d*L-y*M)*P,t[7]=(f*L-c*_+v*M)*P,t[8]=(a*F-h*C+l*A)*P,t[9]=(s*C-i*F-n*A)*P,t[10]=(d*b-g*_+y*x)*P,t[11]=(u*_-f*b-v*x)*P,t[12]=(h*I-a*O-o*A)*P,t[13]=(i*O-s*I+r*A)*P,t[14]=(g*M-d*S-p*x)*P,t[15]=(f*S-u*M+c*x)*P,t):null}function Ra(t){var e=t[0],i=t[1],s=t[2],r=t[3],n=t[4],a=t[5],h=t[6],o=t[7],l=t[8],f=t[9],u=t[10],c=t[11],v=t[12],d=t[13],g=t[14],p=t[15],y=e*a-i*n,x=e*h-s*n,M=e*o-r*n,_=i*h-s*a,S=i*o-r*a,b=s*o-r*h,L=l*d-f*v,A=l*g-u*v,I=l*p-c*v,C=f*g-u*d,O=f*p-c*d,F=u*p-c*g;return y*F-x*O+M*C+_*I-S*A+b*L}function Ji(t,e,i){var s=e[0],r=e[1],n=e[2],a=e[3],h=e[4],o=e[5],l=e[6],f=e[7],u=e[8],c=e[9],v=e[10],d=e[11],g=e[12],p=e[13],y=e[14],x=e[15],M=i[0],_=i[1],S=i[2],b=i[3];return t[0]=M*s+_*h+S*u+b*g,t[1]=M*r+_*o+S*c+b*p,t[2]=M*n+_*l+S*v+b*y,t[3]=M*a+_*f+S*d+b*x,M=i[4],_=i[5],S=i[6],b=i[7],t[4]=M*s+_*h+S*u+b*g,t[5]=M*r+_*o+S*c+b*p,t[6]=M*n+_*l+S*v+b*y,t[7]=M*a+_*f+S*d+b*x,M=i[8],_=i[9],S=i[10],b=i[11],t[8]=M*s+_*h+S*u+b*g,t[9]=M*r+_*o+S*c+b*p,t[10]=M*n+_*l+S*v+b*y,t[11]=M*a+_*f+S*d+b*x,M=i[12],_=i[13],S=i[14],b=i[15],t[12]=M*s+_*h+S*u+b*g,t[13]=M*r+_*o+S*c+b*p,t[14]=M*n+_*l+S*v+b*y,t[15]=M*a+_*f+S*d+b*x,t}function La(t,e,i){var s=i[0],r=i[1],n=i[2],a,h,o,l,f,u,c,v,d,g,p,y;return e===t?(t[12]=e[0]*s+e[4]*r+e[8]*n+e[12],t[13]=e[1]*s+e[5]*r+e[9]*n+e[13],t[14]=e[2]*s+e[6]*r+e[10]*n+e[14],t[15]=e[3]*s+e[7]*r+e[11]*n+e[15]):(a=e[0],h=e[1],o=e[2],l=e[3],f=e[4],u=e[5],c=e[6],v=e[7],d=e[8],g=e[9],p=e[10],y=e[11],t[0]=a,t[1]=h,t[2]=o,t[3]=l,t[4]=f,t[5]=u,t[6]=c,t[7]=v,t[8]=d,t[9]=g,t[10]=p,t[11]=y,t[12]=a*s+f*r+d*n+e[12],t[13]=h*s+u*r+g*n+e[13],t[14]=o*s+c*r+p*n+e[14],t[15]=l*s+v*r+y*n+e[15]),t}function ts(t,e,i){var s=i[0],r=i[1],n=i[2];return t[0]=e[0]*s,t[1]=e[1]*s,t[2]=e[2]*s,t[3]=e[3]*s,t[4]=e[4]*r,t[5]=e[5]*r,t[6]=e[6]*r,t[7]=e[7]*r,t[8]=e[8]*n,t[9]=e[9]*n,t[10]=e[10]*n,t[11]=e[11]*n,t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15],t}function Ia(t,e,i,s){var r=s[0],n=s[1],a=s[2],h=Math.hypot(r,n,a),o,l,f,u,c,v,d,g,p,y,x,M,_,S,b,L,A,I,C,O,F,V,P,vt;return h<xe?null:(h=1/h,r*=h,n*=h,a*=h,o=Math.sin(i),l=Math.cos(i),f=1-l,u=e[0],c=e[1],v=e[2],d=e[3],g=e[4],p=e[5],y=e[6],x=e[7],M=e[8],_=e[9],S=e[10],b=e[11],L=r*r*f+l,A=n*r*f+a*o,I=a*r*f-n*o,C=r*n*f-a*o,O=n*n*f+l,F=a*n*f+r*o,V=r*a*f+n*o,P=n*a*f-r*o,vt=a*a*f+l,t[0]=u*L+g*A+M*I,t[1]=c*L+p*A+_*I,t[2]=v*L+y*A+S*I,t[3]=d*L+x*A+b*I,t[4]=u*C+g*O+M*F,t[5]=c*C+p*O+_*F,t[6]=v*C+y*O+S*F,t[7]=d*C+x*O+b*F,t[8]=u*V+g*P+M*vt,t[9]=c*V+p*P+_*vt,t[10]=v*V+y*P+S*vt,t[11]=d*V+x*P+b*vt,e!==t&&(t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15]),t)}function Na(t,e,i){var s=Math.sin(i),r=Math.cos(i),n=e[4],a=e[5],h=e[6],o=e[7],l=e[8],f=e[9],u=e[10],c=e[11];return e!==t&&(t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15]),t[4]=n*r+l*s,t[5]=a*r+f*s,t[6]=h*r+u*s,t[7]=o*r+c*s,t[8]=l*r-n*s,t[9]=f*r-a*s,t[10]=u*r-h*s,t[11]=c*r-o*s,t}function Pa(t,e,i){var s=Math.sin(i),r=Math.cos(i),n=e[0],a=e[1],h=e[2],o=e[3],l=e[8],f=e[9],u=e[10],c=e[11];return e!==t&&(t[4]=e[4],t[5]=e[5],t[6]=e[6],t[7]=e[7],t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15]),t[0]=n*r-l*s,t[1]=a*r-f*s,t[2]=h*r-u*s,t[3]=o*r-c*s,t[8]=n*s+l*r,t[9]=a*s+f*r,t[10]=h*s+u*r,t[11]=o*s+c*r,t}function Ca(t,e,i){var s=Math.sin(i),r=Math.cos(i),n=e[0],a=e[1],h=e[2],o=e[3],l=e[4],f=e[5],u=e[6],c=e[7];return e!==t&&(t[8]=e[8],t[9]=e[9],t[10]=e[10],t[11]=e[11],t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15]),t[0]=n*r+l*s,t[1]=a*r+f*s,t[2]=h*r+u*s,t[3]=o*r+c*s,t[4]=l*r-n*s,t[5]=f*r-a*s,t[6]=u*r-h*s,t[7]=c*r-o*s,t}function Oa(t,e){var i=e[0],s=e[1],r=e[2],n=e[3],a=i+i,h=s+s,o=r+r,l=i*a,f=s*a,u=s*h,c=r*a,v=r*h,d=r*o,g=n*a,p=n*h,y=n*o;return t[0]=1-u-d,t[1]=f+y,t[2]=c-p,t[3]=0,t[4]=f-y,t[5]=1-l-d,t[6]=v+g,t[7]=0,t[8]=c+p,t[9]=v-g,t[10]=1-l-u,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t}function Ua(t,e,i,s,r,n,a){var h=1/(i-e),o=1/(r-s),l=1/(n-a);return t[0]=n*2*h,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=n*2*o,t[6]=0,t[7]=0,t[8]=(i+e)*h,t[9]=(r+s)*o,t[10]=(a+n)*l,t[11]=-1,t[12]=0,t[13]=0,t[14]=a*n*2*l,t[15]=0,t}function Ba(t,e,i,s,r){var n=1/Math.tan(e/2),a;return t[0]=n/i,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=n,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[11]=-1,t[12]=0,t[13]=0,t[15]=0,r!=null&&r!==1/0?(a=1/(s-r),t[10]=(r+s)*a,t[14]=2*r*s*a):(t[10]=-1,t[14]=-2*s),t}function Da(t,e,i,s,r,n,a){var h=1/(e-i),o=1/(s-r),l=1/(n-a);return t[0]=-2*h,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=-2*o,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=2*l,t[11]=0,t[12]=(e+i)*h,t[13]=(r+s)*o,t[14]=(a+n)*l,t[15]=1,t}function za(t,e,i,s){var r,n,a,h,o,l,f,u,c,v,d=e[0],g=e[1],p=e[2],y=s[0],x=s[1],M=s[2],_=i[0],S=i[1],b=i[2];return Math.abs(d-_)<xe&&Math.abs(g-S)<xe&&Math.abs(p-b)<xe?wa(t):(f=d-_,u=g-S,c=p-b,v=1/Math.hypot(f,u,c),f*=v,u*=v,c*=v,r=x*c-M*u,n=M*f-y*c,a=y*u-x*f,v=Math.hypot(r,n,a),v?(v=1/v,r*=v,n*=v,a*=v):(r=0,n=0,a=0),h=u*a-c*n,o=c*r-f*a,l=f*n-u*r,v=Math.hypot(h,o,l),v?(v=1/v,h*=v,o*=v,l*=v):(h=0,o=0,l=0),t[0]=r,t[1]=h,t[2]=f,t[3]=0,t[4]=n,t[5]=o,t[6]=u,t[7]=0,t[8]=a,t[9]=l,t[10]=c,t[11]=0,t[12]=-(r*d+n*g+a*p),t[13]=-(h*d+o*g+l*p),t[14]=-(f*d+u*g+c*p),t[15]=1,t)}function ka(){var t=new zt(4);return zt!=Float32Array&&(t[0]=0,t[1]=0,t[2]=0,t[3]=0),t}function Fa(t,e,i){var s=e[0],r=e[1],n=e[2],a=e[3];return t[0]=i[0]*s+i[4]*r+i[8]*n+i[12]*a,t[1]=i[1]*s+i[5]*r+i[9]*n+i[13]*a,t[2]=i[2]*s+i[6]*r+i[10]*n+i[14]*a,t[3]=i[3]*s+i[7]*r+i[11]*n+i[15]*a,t}(function(){var t=ka();return function(e,i,s,r,n,a){var h,o;for(i||(i=4),s||(s=0),r?o=Math.min(r*i+s,e.length):o=e.length,h=s;h<o;h+=i)t[0]=e[h],t[1]=e[h+1],t[2]=e[h+2],t[3]=e[h+3],n(t,t,a),e[h]=t[0],e[h+1]=t[1],e[h+2]=t[2],e[h+3]=t[3];return e}})();const es=Object.freeze([1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1]),Ga=Object.freeze([0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]),Ha=Object.freeze({COL0ROW0:0,COL0ROW1:1,COL0ROW2:2,COL0ROW3:3,COL1ROW0:4,COL1ROW1:5,COL1ROW2:6,COL1ROW3:7,COL2ROW0:8,COL2ROW1:9,COL2ROW2:10,COL2ROW3:11,COL3ROW0:12,COL3ROW1:13,COL3ROW2:14,COL3ROW3:15}),kt={};class Pt extends ba{static get IDENTITY(){return kt.IDENTITY=kt.IDENTITY||Object.freeze(new Pt(es)),kt.IDENTITY}static get ZERO(){return kt.ZERO=kt.ZERO||Object.freeze(new Pt(Ga)),kt.ZERO}get INDICES(){return Ha}get ELEMENTS(){return 16}get RANK(){return 4}constructor(e){super(-0,-0,-0,-0,-0,-0,-0,-0,-0,-0,-0,-0,-0,-0,-0,-0),arguments.length===1&&Array.isArray(e)?this.copy(e):this.identity()}copy(e){return this[0]=e[0],this[1]=e[1],this[2]=e[2],this[3]=e[3],this[4]=e[4],this[5]=e[5],this[6]=e[6],this[7]=e[7],this[8]=e[8],this[9]=e[9],this[10]=e[10],this[11]=e[11],this[12]=e[12],this[13]=e[13],this[14]=e[14],this[15]=e[15],this.check()}set(e,i,s,r,n,a,h,o,l,f,u,c,v,d,g,p){return this[0]=e,this[1]=i,this[2]=s,this[3]=r,this[4]=n,this[5]=a,this[6]=h,this[7]=o,this[8]=l,this[9]=f,this[10]=u,this[11]=c,this[12]=v,this[13]=d,this[14]=g,this[15]=p,this.check()}setRowMajor(e,i,s,r,n,a,h,o,l,f,u,c,v,d,g,p){return this[0]=e,this[1]=n,this[2]=l,this[3]=v,this[4]=i,this[5]=a,this[6]=f,this[7]=d,this[8]=s,this[9]=h,this[10]=u,this[11]=g,this[12]=r,this[13]=o,this[14]=c,this[15]=p,this.check()}toRowMajor(e){return e[0]=this[0],e[1]=this[4],e[2]=this[8],e[3]=this[12],e[4]=this[1],e[5]=this[5],e[6]=this[9],e[7]=this[13],e[8]=this[2],e[9]=this[6],e[10]=this[10],e[11]=this[14],e[12]=this[3],e[13]=this[7],e[14]=this[11],e[15]=this[15],e}identity(){return this.copy(es)}fromQuaternion(e){return Oa(this,e),this.check()}frustum({left:e,right:i,bottom:s,top:r,near:n,far:a}){return a===1/0?Pt._computeInfinitePerspectiveOffCenter(this,e,i,s,r,n):Ua(this,e,i,s,r,n,a),this.check()}static _computeInfinitePerspectiveOffCenter(e,i,s,r,n,a){const h=2*a/(s-i),o=2*a/(n-r),l=(s+i)/(s-i),f=(n+r)/(n-r),u=-1,c=-1,v=-2*a;return e[0]=h,e[1]=0,e[2]=0,e[3]=0,e[4]=0,e[5]=o,e[6]=0,e[7]=0,e[8]=l,e[9]=f,e[10]=u,e[11]=c,e[12]=0,e[13]=0,e[14]=v,e[15]=0,e}lookAt(e,i,s){return arguments.length===1&&({eye:e,center:i,up:s}=e),i=i||[0,0,0],s=s||[0,1,0],za(this,e,i,s),this.check()}ortho({left:e,right:i,bottom:s,top:r,near:n=.1,far:a=500}){return Da(this,e,i,s,r,n,a),this.check()}orthographic({fovy:e=45*Math.PI/180,aspect:i=1,focalDistance:s=1,near:r=.1,far:n=500}){if(e>Math.PI*2)throw Error("radians");const a=e/2,h=s*Math.tan(a),o=h*i;return new Pt().ortho({left:-o,right:o,bottom:-h,top:h,near:r,far:n})}perspective({fovy:e=void 0,fov:i=45*Math.PI/180,aspect:s=1,near:r=.1,far:n=500}={}){if(e=e||i,e>Math.PI*2)throw Error("radians");return Ba(this,e,s,r,n),this.check()}determinant(){return Ra(this)}getScale(e=[-0,-0,-0]){return e[0]=Math.sqrt(this[0]*this[0]+this[1]*this[1]+this[2]*this[2]),e[1]=Math.sqrt(this[4]*this[4]+this[5]*this[5]+this[6]*this[6]),e[2]=Math.sqrt(this[8]*this[8]+this[9]*this[9]+this[10]*this[10]),e}getTranslation(e=[-0,-0,-0]){return e[0]=this[12],e[1]=this[13],e[2]=this[14],e}getRotation(e=[-0,-0,-0,-0,-0,-0,-0,-0,-0,-0,-0,-0,-0,-0,-0,-0],i=null){const s=this.getScale(i||[-0,-0,-0]),r=1/s[0],n=1/s[1],a=1/s[2];return e[0]=this[0]*r,e[1]=this[1]*n,e[2]=this[2]*a,e[3]=0,e[4]=this[4]*r,e[5]=this[5]*n,e[6]=this[6]*a,e[7]=0,e[8]=this[8]*r,e[9]=this[9]*n,e[10]=this[10]*a,e[11]=0,e[12]=0,e[13]=0,e[14]=0,e[15]=1,e}getRotationMatrix3(e=[-0,-0,-0,-0,-0,-0,-0,-0,-0],i=null){const s=this.getScale(i||[-0,-0,-0]),r=1/s[0],n=1/s[1],a=1/s[2];return e[0]=this[0]*r,e[1]=this[1]*n,e[2]=this[2]*a,e[3]=this[4]*r,e[4]=this[5]*n,e[5]=this[6]*a,e[6]=this[8]*r,e[7]=this[9]*n,e[8]=this[10]*a,e}transpose(){return Ta(this,this),this.check()}invert(){return Aa(this,this),this.check()}multiplyLeft(e){return Ji(this,e,this),this.check()}multiplyRight(e){return Ji(this,this,e),this.check()}rotateX(e){return Na(this,this,e),this.check()}rotateY(e){return Pa(this,this,e),this.check()}rotateZ(e){return Ca(this,this,e),this.check()}rotateXYZ([e,i,s]){return this.rotateX(e).rotateY(i).rotateZ(s)}rotateAxis(e,i){return Ia(this,this,e,i),this.check()}scale(e){return Array.isArray(e)?ts(this,this,e):ts(this,this,[e,e,e]),this.check()}translate(e){return La(this,this,e),this.check()}transform(e,i){return e.length===4?(i=Fa(i||[-0,-0,-0,-0],e,this),We(i,4),i):this.transformAsPoint(e,i)}transformAsPoint(e,i){const{length:s}=e;switch(s){case 2:i=Xi(i||[-0,-0],e,this);break;case 3:i=Yi(i||[-0,-0,-0],e,this);break;default:throw new Error("Illegal vector")}return We(i,e.length),i}transformAsVector(e,i){switch(e.length){case 2:i=Qi(i||[-0,-0],e,this);break;case 3:i=ji(i||[-0,-0,-0],e,this);break;default:throw new Error("Illegal vector")}return We(i,e.length),i}makeRotationX(e){return this.identity().rotateX(e)}makeTranslation(e,i,s){return this.identity().translate([e,i,s])}transformPoint(e,i){return Xe("Matrix4.transformPoint","3.0"),this.transformAsPoint(e,i)}transformVector(e,i){return Xe("Matrix4.transformVector","3.0"),this.transformAsPoint(e,i)}transformDirection(e,i){return Xe("Matrix4.transformDirection","3.0"),this.transformAsVector(e,i)}}const je={EPSILON1:.1,EPSILON2:.01,EPSILON3:.001,EPSILON4:1e-4,EPSILON5:1e-5,EPSILON6:1e-6,EPSILON7:1e-7,EPSILON8:1e-8,EPSILON9:1e-9,EPSILON10:1e-10,EPSILON11:1e-11,EPSILON12:1e-12,EPSILON13:1e-13,EPSILON14:1e-14,EPSILON15:1e-15,EPSILON16:1e-16,EPSILON17:1e-17,EPSILON18:1e-18,EPSILON19:1e-19,EPSILON20:1e-20,PI_OVER_TWO:Math.PI/2,PI_OVER_FOUR:Math.PI/4,PI_OVER_SIX:Math.PI/6,TWO_PI:Math.PI*2};function qa(t){t("EPSG:4326","+title=WGS 84 (long/lat) +proj=longlat +ellps=WGS84 +datum=WGS84 +units=degrees"),t("EPSG:4269","+title=NAD83 (long/lat) +proj=longlat +a=6378137.0 +b=6356752.31414036 +ellps=GRS80 +datum=NAD83 +units=degrees"),t("EPSG:3857","+title=WGS 84 / Pseudo-Mercator +proj=merc +a=6378137 +b=6378137 +lat_ts=0.0 +lon_0=0.0 +x_0=0.0 +y_0=0 +k=1.0 +units=m +nadgrids=@null +no_defs"),t.WGS84=t["EPSG:4326"],t["EPSG:3785"]=t["EPSG:3857"],t.GOOGLE=t["EPSG:3857"],t["EPSG:900913"]=t["EPSG:3857"],t["EPSG:102113"]=t["EPSG:3857"]}var Ct=1,Ot=2,Ft=3,Va=4,Ke=5,is=6378137,$a=6356752314e-3,ss=.0066943799901413165,Jt=484813681109536e-20,E=Math.PI/2,Wa=.16666666666666666,Xa=.04722222222222222,Qa=.022156084656084655,w=1e-10,j=.017453292519943295,mt=57.29577951308232,B=Math.PI/4,te=Math.PI*2,$=3.14159265359,tt={};tt.greenwich=0,tt.lisbon=-9.131906111111,tt.paris=2.337229166667,tt.bogota=-74.080916666667,tt.madrid=-3.687938888889,tt.rome=12.452333333333,tt.bern=7.439583333333,tt.jakarta=106.807719444444,tt.ferro=-17.666666666667,tt.brussels=4.367975,tt.stockholm=18.058277777778,tt.athens=23.7163375,tt.oslo=10.722916666667;const ja={ft:{to_meter:.3048},"us-ft":{to_meter:1200/3937}};var rs=/[\s_\-\/\(\)]/g;function wt(t,e){if(t[e])return t[e];for(var i=Object.keys(t),s=e.toLowerCase().replace(rs,""),r=-1,n,a;++r<i.length;)if(n=i[r],a=n.toLowerCase().replace(rs,""),a===s)return t[n]}function Ye(t){var e={},i=t.split("+").map(function(h){return h.trim()}).filter(function(h){return h}).reduce(function(h,o){var l=o.split("=");return l.push(!0),h[l[0].toLowerCase()]=l[1],h},{}),s,r,n,a={proj:"projName",datum:"datumCode",rf:function(h){e.rf=parseFloat(h)},lat_0:function(h){e.lat0=h*j},lat_1:function(h){e.lat1=h*j},lat_2:function(h){e.lat2=h*j},lat_ts:function(h){e.lat_ts=h*j},lon_0:function(h){e.long0=h*j},lon_1:function(h){e.long1=h*j},lon_2:function(h){e.long2=h*j},alpha:function(h){e.alpha=parseFloat(h)*j},gamma:function(h){e.rectified_grid_angle=parseFloat(h)},lonc:function(h){e.longc=h*j},x_0:function(h){e.x0=parseFloat(h)},y_0:function(h){e.y0=parseFloat(h)},k_0:function(h){e.k0=parseFloat(h)},k:function(h){e.k0=parseFloat(h)},a:function(h){e.a=parseFloat(h)},b:function(h){e.b=parseFloat(h)},r_a:function(){e.R_A=!0},zone:function(h){e.zone=parseInt(h,10)},south:function(){e.utmSouth=!0},towgs84:function(h){e.datum_params=h.split(",").map(function(o){return parseFloat(o)})},to_meter:function(h){e.to_meter=parseFloat(h)},units:function(h){e.units=h;var o=wt(ja,h);o&&(e.to_meter=o.to_meter)},from_greenwich:function(h){e.from_greenwich=h*j},pm:function(h){var o=wt(tt,h);e.from_greenwich=(o||parseFloat(h))*j},nadgrids:function(h){h==="@null"?e.datumCode="none":e.nadgrids=h},axis:function(h){var o="ewnsud";h.length===3&&o.indexOf(h.substr(0,1))!==-1&&o.indexOf(h.substr(1,1))!==-1&&o.indexOf(h.substr(2,1))!==-1&&(e.axis=h)},approx:function(){e.approx=!0}};for(s in i)r=i[s],s in a?(n=a[s],typeof n=="function"?n(r):e[n]=r):e[s]=r;return typeof e.datumCode=="string"&&e.datumCode!=="WGS84"&&(e.datumCode=e.datumCode.toLowerCase()),e}var ee=1,ns=2,as=3,_e=4,hs=5,Ze=-1,Ka=/\s/,Ya=/[A-Za-z]/,Za=/[A-Za-z84_]/,Ee=/[,\]]/,os=/[\d\.E\-\+]/;function xt(t){if(typeof t!="string")throw new Error("not a string");this.text=t.trim(),this.level=0,this.place=0,this.root=null,this.stack=[],this.currentObject=null,this.state=ee}xt.prototype.readCharicter=function(){var t=this.text[this.place++];if(this.state!==_e)for(;Ka.test(t);){if(this.place>=this.text.length)return;t=this.text[this.place++]}switch(this.state){case ee:return this.neutral(t);case ns:return this.keyword(t);case _e:return this.quoted(t);case hs:return this.afterquote(t);case as:return this.number(t);case Ze:return}},xt.prototype.afterquote=function(t){if(t==='"'){this.word+='"',this.state=_e;return}if(Ee.test(t)){this.word=this.word.trim(),this.afterItem(t);return}throw new Error(`havn't handled "`+t+'" in afterquote yet, index '+this.place)},xt.prototype.afterItem=function(t){if(t===","){this.word!==null&&this.currentObject.push(this.word),this.word=null,this.state=ee;return}if(t==="]"){this.level--,this.word!==null&&(this.currentObject.push(this.word),this.word=null),this.state=ee,this.currentObject=this.stack.pop(),this.currentObject||(this.state=Ze);return}},xt.prototype.number=function(t){if(os.test(t)){this.word+=t;return}if(Ee.test(t)){this.word=parseFloat(this.word),this.afterItem(t);return}throw new Error(`havn't handled "`+t+'" in number yet, index '+this.place)},xt.prototype.quoted=function(t){if(t==='"'){this.state=hs;return}this.word+=t},xt.prototype.keyword=function(t){if(Za.test(t)){this.word+=t;return}if(t==="["){var e=[];e.push(this.word),this.level++,this.root===null?this.root=e:this.currentObject.push(e),this.stack.push(this.currentObject),this.currentObject=e,this.state=ee;return}if(Ee.test(t)){this.afterItem(t);return}throw new Error(`havn't handled "`+t+'" in keyword yet, index '+this.place)},xt.prototype.neutral=function(t){if(Ya.test(t)){this.word=t,this.state=ns;return}if(t==='"'){this.word="",this.state=_e;return}if(os.test(t)){this.word=t,this.state=as;return}if(Ee.test(t)){this.afterItem(t);return}throw new Error(`havn't handled "`+t+'" in neutral yet, index '+this.place)},xt.prototype.output=function(){for(;this.place<this.text.length;)this.readCharicter();if(this.state===Ze)return this.root;throw new Error('unable to parse string "'+this.text+'". State is '+this.state)};function Ja(t){var e=new xt(t);return e.output()}function ls(t,e,i){Array.isArray(e)&&(i.unshift(e),e=null);var s=e?{}:t,r=i.reduce(function(n,a){return Gt(a,n),n},s);e&&(t[e]=r)}function Gt(t,e){if(!Array.isArray(t)){e[t]=!0;return}var i=t.shift();if(i==="PARAMETER"&&(i=t.shift()),t.length===1){if(Array.isArray(t[0])){e[i]={},Gt(t[0],e[i]);return}e[i]=t[0];return}if(!t.length){e[i]=!0;return}if(i==="TOWGS84"){e[i]=t;return}if(i==="AXIS"){i in e||(e[i]=[]),e[i].push(t);return}Array.isArray(i)||(e[i]={});var s;switch(i){case"UNIT":case"PRIMEM":case"VERT_DATUM":e[i]={name:t[0].toLowerCase(),convert:t[1]},t.length===3&&Gt(t[2],e[i]);return;case"SPHEROID":case"ELLIPSOID":e[i]={name:t[0],a:t[1],rf:t[2]},t.length===4&&Gt(t[3],e[i]);return;case"PROJECTEDCRS":case"PROJCRS":case"GEOGCS":case"GEOCCS":case"PROJCS":case"LOCAL_CS":case"GEODCRS":case"GEODETICCRS":case"GEODETICDATUM":case"EDATUM":case"ENGINEERINGDATUM":case"VERT_CS":case"VERTCRS":case"VERTICALCRS":case"COMPD_CS":case"COMPOUNDCRS":case"ENGINEERINGCRS":case"ENGCRS":case"FITTED_CS":case"LOCAL_DATUM":case"DATUM":t[0]=["name",t[0]],ls(e,i,t);return;default:for(s=-1;++s<t.length;)if(!Array.isArray(t[s]))return Gt(t,e[i]);return ls(e,i,t)}}var th=.017453292519943295;function eh(t,e){var i=e[0],s=e[1];!(i in t)&&s in t&&(t[i]=t[s],e.length===3&&(t[i]=e[2](t[i])))}function _t(t){return t*th}function ih(t){if(t.type==="GEOGCS"?t.projName="longlat":t.type==="LOCAL_CS"?(t.projName="identity",t.local=!0):typeof t.PROJECTION=="object"?t.projName=Object.keys(t.PROJECTION)[0]:t.projName=t.PROJECTION,t.AXIS){for(var e="",i=0,s=t.AXIS.length;i<s;++i){var r=[t.AXIS[i][0].toLowerCase(),t.AXIS[i][1].toLowerCase()];r[0].indexOf("north")!==-1||(r[0]==="y"||r[0]==="lat")&&r[1]==="north"?e+="n":r[0].indexOf("south")!==-1||(r[0]==="y"||r[0]==="lat")&&r[1]==="south"?e+="s":r[0].indexOf("east")!==-1||(r[0]==="x"||r[0]==="lon")&&r[1]==="east"?e+="e":(r[0].indexOf("west")!==-1||(r[0]==="x"||r[0]==="lon")&&r[1]==="west")&&(e+="w")}e.length===2&&(e+="u"),e.length===3&&(t.axis=e)}t.UNIT&&(t.units=t.UNIT.name.toLowerCase(),t.units==="metre"&&(t.units="meter"),t.UNIT.convert&&(t.type==="GEOGCS"?t.DATUM&&t.DATUM.SPHEROID&&(t.to_meter=t.UNIT.convert*t.DATUM.SPHEROID.a):t.to_meter=t.UNIT.convert));var n=t.GEOGCS;t.type==="GEOGCS"&&(n=t),n&&(n.DATUM?t.datumCode=n.DATUM.name.toLowerCase():t.datumCode=n.name.toLowerCase(),t.datumCode.slice(0,2)==="d_"&&(t.datumCode=t.datumCode.slice(2)),(t.datumCode==="new_zealand_geodetic_datum_1949"||t.datumCode==="new_zealand_1949")&&(t.datumCode="nzgd49"),(t.datumCode==="wgs_1984"||t.datumCode==="world_geodetic_system_1984")&&(t.PROJECTION==="Mercator_Auxiliary_Sphere"&&(t.sphere=!0),t.datumCode="wgs84"),t.datumCode.slice(-6)==="_ferro"&&(t.datumCode=t.datumCode.slice(0,-6)),t.datumCode.slice(-8)==="_jakarta"&&(t.datumCode=t.datumCode.slice(0,-8)),~t.datumCode.indexOf("belge")&&(t.datumCode="rnb72"),n.DATUM&&n.DATUM.SPHEROID&&(t.ellps=n.DATUM.SPHEROID.name.replace("_19","").replace(/[Cc]larke\_18/,"clrk"),t.ellps.toLowerCase().slice(0,13)==="international"&&(t.ellps="intl"),t.a=n.DATUM.SPHEROID.a,t.rf=parseFloat(n.DATUM.SPHEROID.rf,10)),n.DATUM&&n.DATUM.TOWGS84&&(t.datum_params=n.DATUM.TOWGS84),~t.datumCode.indexOf("osgb_1936")&&(t.datumCode="osgb36"),~t.datumCode.indexOf("osni_1952")&&(t.datumCode="osni52"),(~t.datumCode.indexOf("tm65")||~t.datumCode.indexOf("geodetic_datum_of_1965"))&&(t.datumCode="ire65"),t.datumCode==="ch1903+"&&(t.datumCode="ch1903"),~t.datumCode.indexOf("israel")&&(t.datumCode="isr93")),t.b&&!isFinite(t.b)&&(t.b=t.a);function a(l){var f=t.to_meter||1;return l*f}var h=function(l){return eh(t,l)},o=[["standard_parallel_1","Standard_Parallel_1"],["standard_parallel_1","Latitude of 1st standard parallel"],["standard_parallel_2","Standard_Parallel_2"],["standard_parallel_2","Latitude of 2nd standard parallel"],["false_easting","False_Easting"],["false_easting","False easting"],["false-easting","Easting at false origin"],["false_northing","False_Northing"],["false_northing","False northing"],["false_northing","Northing at false origin"],["central_meridian","Central_Meridian"],["central_meridian","Longitude of natural origin"],["central_meridian","Longitude of false origin"],["latitude_of_origin","Latitude_Of_Origin"],["latitude_of_origin","Central_Parallel"],["latitude_of_origin","Latitude of natural origin"],["latitude_of_origin","Latitude of false origin"],["scale_factor","Scale_Factor"],["k0","scale_factor"],["latitude_of_center","Latitude_Of_Center"],["latitude_of_center","Latitude_of_center"],["lat0","latitude_of_center",_t],["longitude_of_center","Longitude_Of_Center"],["longitude_of_center","Longitude_of_center"],["longc","longitude_of_center",_t],["x0","false_easting",a],["y0","false_northing",a],["long0","central_meridian",_t],["lat0","latitude_of_origin",_t],["lat0","standard_parallel_1",_t],["lat1","standard_parallel_1",_t],["lat2","standard_parallel_2",_t],["azimuth","Azimuth"],["alpha","azimuth",_t],["srsCode","name"]];o.forEach(h),!t.long0&&t.longc&&(t.projName==="Albers_Conic_Equal_Area"||t.projName==="Lambert_Azimuthal_Equal_Area")&&(t.long0=t.longc),!t.lat_ts&&t.lat1&&(t.projName==="Stereographic_South_Pole"||t.projName==="Polar Stereographic (variant B)")&&(t.lat0=_t(t.lat1>0?90:-90),t.lat_ts=t.lat1)}function us(t){var e=Ja(t),i=e.shift(),s=e.shift();e.unshift(["name",s]),e.unshift(["type",i]);var r={};return Gt(e,r),ih(r),r}function Y(t){var e=this;if(arguments.length===2){var i=arguments[1];typeof i=="string"?i.charAt(0)==="+"?Y[t]=Ye(arguments[1]):Y[t]=us(arguments[1]):Y[t]=i}else if(arguments.length===1){if(Array.isArray(t))return t.map(function(s){Array.isArray(s)?Y.apply(e,s):Y(s)});if(typeof t=="string"){if(t in Y)return Y[t]}else"EPSG"in t?Y["EPSG:"+t.EPSG]=t:"ESRI"in t?Y["ESRI:"+t.ESRI]=t:"IAU2000"in t?Y["IAU2000:"+t.IAU2000]=t:console.log(t);return}}qa(Y);function sh(t){return typeof t=="string"}function rh(t){return t in Y}var nh=["PROJECTEDCRS","PROJCRS","GEOGCS","GEOCCS","PROJCS","LOCAL_CS","GEODCRS","GEODETICCRS","GEODETICDATUM","ENGCRS","ENGINEERINGCRS"];function ah(t){return nh.some(function(e){return t.indexOf(e)>-1})}var hh=["3857","900913","3785","102113"];function oh(t){var e=wt(t,"authority");if(e){var i=wt(e,"epsg");return i&&hh.indexOf(i)>-1}}function lh(t){var e=wt(t,"extension");if(e)return wt(e,"proj4")}function uh(t){return t[0]==="+"}function fh(t){if(sh(t)){if(rh(t))return Y[t];if(ah(t)){var e=us(t);if(oh(e))return Y["EPSG:3857"];var i=lh(e);return i?Ye(i):e}if(uh(t))return Ye(t)}else return t}function fs(t,e){t=t||{};var i,s;if(!e)return t;for(s in e)i=e[s],i!==void 0&&(t[s]=i);return t}function gt(t,e,i){var s=t*e;return i/Math.sqrt(1-s*s)}function ie(t){return t<0?-1:1}function T(t){return Math.abs(t)<=$?t:t-ie(t)*te}function lt(t,e,i){var s=t*i,r=.5*t;return s=Math.pow((1-s)/(1+s),r),Math.tan(.5*(E-e))/s}function se(t,e){for(var i=.5*t,s,r,n=E-2*Math.atan(e),a=0;a<=15;a++)if(s=t*Math.sin(n),r=E-2*Math.atan(e*Math.pow((1-s)/(1+s),i))-n,n+=r,Math.abs(r)<=1e-10)return n;return-9999}function ch(){var t=this.b/this.a;this.es=1-t*t,"x0"in this||(this.x0=0),"y0"in this||(this.y0=0),this.e=Math.sqrt(this.es),this.lat_ts?this.sphere?this.k0=Math.cos(this.lat_ts):this.k0=gt(this.e,Math.sin(this.lat_ts),Math.cos(this.lat_ts)):this.k0||(this.k?this.k0=this.k:this.k0=1)}function vh(t){var e=t.x,i=t.y;if(i*mt>90&&i*mt<-90&&e*mt>180&&e*mt<-180)return null;var s,r;if(Math.abs(Math.abs(i)-E)<=w)return null;if(this.sphere)s=this.x0+this.a*this.k0*T(e-this.long0),r=this.y0+this.a*this.k0*Math.log(Math.tan(B+.5*i));else{var n=Math.sin(i),a=lt(this.e,i,n);s=this.x0+this.a*this.k0*T(e-this.long0),r=this.y0-this.a*this.k0*Math.log(a)}return t.x=s,t.y=r,t}function dh(t){var e=t.x-this.x0,i=t.y-this.y0,s,r;if(this.sphere)r=E-2*Math.atan(Math.exp(-i/(this.a*this.k0)));else{var n=Math.exp(-i/(this.a*this.k0));if(r=se(this.e,n),r===-9999)return null}return s=T(this.long0+e/(this.a*this.k0)),t.x=s,t.y=r,t}var mh=["Mercator","Popular Visualisation Pseudo Mercator","Mercator_1SP","Mercator_Auxiliary_Sphere","merc"];const gh={init:ch,forward:vh,inverse:dh,names:mh};function ph(){}function cs(t){return t}var yh=["longlat","identity"],Mh=[gh,{init:ph,forward:cs,inverse:cs,names:yh}],Se={},be=[];function vs(t,e){var i=be.length;return t.names?(be[i]=t,t.names.forEach(function(s){Se[s.toLowerCase()]=i}),this):(console.log(e),!0)}function xh(t){if(!t)return!1;var e=t.toLowerCase();if(typeof Se[e]<"u"&&be[Se[e]])return be[Se[e]]}function _h(){Mh.forEach(vs)}const Eh={start:_h,add:vs,get:xh};var R={};R.MERIT={a:6378137,rf:298.257,ellipseName:"MERIT 1983"},R.SGS85={a:6378136,rf:298.257,ellipseName:"Soviet Geodetic System 85"},R.GRS80={a:6378137,rf:298.*********,ellipseName:"GRS 1980(IUGG, 1980)"},R.IAU76={a:6378140,rf:298.257,ellipseName:"IAU 1976"},R.airy={a:6377563396e-3,b:635625691e-2,ellipseName:"Airy 1830"},R.APL4={a:6378137,rf:298.25,ellipseName:"Appl. Physics. 1965"},R.NWL9D={a:6378145,rf:298.25,ellipseName:"Naval Weapons Lab., 1965"},R.mod_airy={a:6377340189e-3,b:6356034446e-3,ellipseName:"Modified Airy"},R.andrae={a:637710443e-2,rf:300,ellipseName:"Andrae 1876 (Den., Iclnd.)"},R.aust_SA={a:6378160,rf:298.25,ellipseName:"Australian Natl & S. Amer. 1969"},R.GRS67={a:6378160,rf:298.*********,ellipseName:"GRS 67(IUGG 1967)"},R.bessel={a:6377397155e-3,rf:299.1528128,ellipseName:"Bessel 1841"},R.bess_nam={a:6377483865e-3,rf:299.1528128,ellipseName:"Bessel 1841 (Namibia)"},R.clrk66={a:63782064e-1,b:63565838e-1,ellipseName:"Clarke 1866"},R.clrk80={a:6378249145e-3,rf:293.4663,ellipseName:"Clarke 1880 mod."},R.clrk58={a:6378293645208759e-9,rf:294.2606763692654,ellipseName:"Clarke 1858"},R.CPM={a:63757387e-1,rf:334.29,ellipseName:"Comm. des Poids et Mesures 1799"},R.delmbr={a:6376428,rf:311.5,ellipseName:"Delambre 1810 (Belgium)"},R.engelis={a:637813605e-2,rf:298.2566,ellipseName:"Engelis 1985"},R.evrst30={a:6377276345e-3,rf:300.8017,ellipseName:"Everest 1830"},R.evrst48={a:6377304063e-3,rf:300.8017,ellipseName:"Everest 1948"},R.evrst56={a:6377301243e-3,rf:300.8017,ellipseName:"Everest 1956"},R.evrst69={a:6377295664e-3,rf:300.8017,ellipseName:"Everest 1969"},R.evrstSS={a:6377298556e-3,rf:300.8017,ellipseName:"Everest (Sabah & Sarawak)"},R.fschr60={a:6378166,rf:298.3,ellipseName:"Fischer (Mercury Datum) 1960"},R.fschr60m={a:6378155,rf:298.3,ellipseName:"Fischer 1960"},R.fschr68={a:6378150,rf:298.3,ellipseName:"Fischer 1968"},R.helmert={a:6378200,rf:298.3,ellipseName:"Helmert 1906"},R.hough={a:6378270,rf:297,ellipseName:"Hough"},R.intl={a:6378388,rf:297,ellipseName:"International 1909 (Hayford)"},R.kaula={a:6378163,rf:298.24,ellipseName:"Kaula 1961"},R.lerch={a:6378139,rf:298.257,ellipseName:"Lerch 1979"},R.mprts={a:6397300,rf:191,ellipseName:"Maupertius 1738"},R.new_intl={a:63781575e-1,b:63567722e-1,ellipseName:"New International 1967"},R.plessis={a:6376523,rf:6355863,ellipseName:"Plessis 1817 (France)"},R.krass={a:6378245,rf:298.3,ellipseName:"Krassovsky, 1942"},R.SEasia={a:6378155,b:63567733205e-4,ellipseName:"Southeast Asia"},R.walbeck={a:6376896,b:63558348467e-4,ellipseName:"Walbeck"},R.WGS60={a:6378165,rf:298.3,ellipseName:"WGS 60"},R.WGS66={a:6378145,rf:298.25,ellipseName:"WGS 66"},R.WGS7={a:6378135,rf:298.26,ellipseName:"WGS 72"};var Sh=R.WGS84={a:6378137,rf:298.257223563,ellipseName:"WGS 84"};R.sphere={a:6370997,b:6370997,ellipseName:"Normal Sphere (r=6370997)"};function bh(t,e,i,s){var r=t*t,n=e*e,a=(r-n)/r,h=0;s?(t*=1-a*(Wa+a*(Xa+a*Qa)),r=t*t,a=0):h=Math.sqrt(a);var o=(r-n)/n;return{es:a,e:h,ep2:o}}function wh(t,e,i,s,r){if(!t){var n=wt(R,s);n||(n=Sh),t=n.a,e=n.b,i=n.rf}return i&&!e&&(e=(1-1/i)*t),(i===0||Math.abs(t-e)<w)&&(r=!0,e=t),{a:t,b:e,rf:i,sphere:r}}var X={};X.wgs84={towgs84:"0,0,0",ellipse:"WGS84",datumName:"WGS84"},X.ch1903={towgs84:"674.374,15.056,405.346",ellipse:"bessel",datumName:"swiss"},X.ggrs87={towgs84:"-199.87,74.79,246.62",ellipse:"GRS80",datumName:"Greek_Geodetic_Reference_System_1987"},X.nad83={towgs84:"0,0,0",ellipse:"GRS80",datumName:"North_American_Datum_1983"},X.nad27={nadgrids:"@conus,@alaska,@ntv2_0.gsb,@ntv1_can.dat",ellipse:"clrk66",datumName:"North_American_Datum_1927"},X.potsdam={towgs84:"598.1,73.7,418.2,0.202,0.045,-2.455,6.7",ellipse:"bessel",datumName:"Potsdam Rauenberg 1950 DHDN"},X.carthage={towgs84:"-263.0,6.0,431.0",ellipse:"clark80",datumName:"Carthage 1934 Tunisia"},X.hermannskogel={towgs84:"577.326,90.129,463.919,5.137,1.474,5.297,2.4232",ellipse:"bessel",datumName:"Hermannskogel"},X.osni52={towgs84:"482.530,-130.596,564.557,-1.042,-0.214,-0.631,8.15",ellipse:"airy",datumName:"Irish National"},X.ire65={towgs84:"482.530,-130.596,564.557,-1.042,-0.214,-0.631,8.15",ellipse:"mod_airy",datumName:"Ireland 1965"},X.rassadiran={towgs84:"-133.63,-157.5,-158.62",ellipse:"intl",datumName:"Rassadiran"},X.nzgd49={towgs84:"59.47,-5.04,187.44,0.47,-0.1,1.024,-4.5993",ellipse:"intl",datumName:"New Zealand Geodetic Datum 1949"},X.osgb36={towgs84:"446.448,-125.157,542.060,0.1502,0.2470,0.8421,-20.4894",ellipse:"airy",datumName:"Airy 1830"},X.s_jtsk={towgs84:"589,76,480",ellipse:"bessel",datumName:"S-JTSK (Ferro)"},X.beduaram={towgs84:"-106,-87,188",ellipse:"clrk80",datumName:"Beduaram"},X.gunung_segara={towgs84:"-403,684,41",ellipse:"bessel",datumName:"Gunung Segara Jakarta"},X.rnb72={towgs84:"106.869,-52.2978,103.724,-0.33657,0.456955,-1.84218,1",ellipse:"intl",datumName:"Reseau National Belge 1972"};function Th(t,e,i,s,r,n,a){var h={};return t===void 0||t==="none"?h.datum_type=Ke:h.datum_type=Va,e&&(h.datum_params=e.map(parseFloat),(h.datum_params[0]!==0||h.datum_params[1]!==0||h.datum_params[2]!==0)&&(h.datum_type=Ct),h.datum_params.length>3&&(h.datum_params[3]!==0||h.datum_params[4]!==0||h.datum_params[5]!==0||h.datum_params[6]!==0)&&(h.datum_type=Ot,h.datum_params[3]*=Jt,h.datum_params[4]*=Jt,h.datum_params[5]*=Jt,h.datum_params[6]=h.datum_params[6]/1e6+1)),a&&(h.datum_type=Ft,h.grids=a),h.a=i,h.b=s,h.es=r,h.ep2=n,h}var ds={};function Ah(t,e){var i=new DataView(e),s=Ih(i),r=Nh(i,s);r.nSubgrids>1&&console.log("Only single NTv2 subgrids are currently supported, subsequent sub grids are ignored");var n=Ph(i,r,s),a={header:r,subgrids:n};return ds[t]=a,a}function Rh(t){if(t===void 0)return null;var e=t.split(",");return e.map(Lh)}function Lh(t){if(t.length===0)return null;var e=t[0]==="@";return e&&(t=t.slice(1)),t==="null"?{name:"null",mandatory:!e,grid:null,isNull:!0}:{name:t,mandatory:!e,grid:ds[t]||null,isNull:!1}}function Ht(t){return t/3600*Math.PI/180}function Ih(t){var e=t.getInt32(8,!1);return e===11?!1:(e=t.getInt32(8,!0),e!==11&&console.warn("Failed to detect nadgrid endian-ness, defaulting to little-endian"),!0)}function Nh(t,e){return{nFields:t.getInt32(8,e),nSubgridFields:t.getInt32(24,e),nSubgrids:t.getInt32(40,e),shiftType:Je(t,56,56+8).trim(),fromSemiMajorAxis:t.getFloat64(120,e),fromSemiMinorAxis:t.getFloat64(136,e),toSemiMajorAxis:t.getFloat64(152,e),toSemiMinorAxis:t.getFloat64(168,e)}}function Je(t,e,i){return String.fromCharCode.apply(null,new Uint8Array(t.buffer.slice(e,i)))}function Ph(t,e,i){for(var s=176,r=[],n=0;n<e.nSubgrids;n++){var a=Oh(t,s,i),h=Uh(t,s,a,i),o=Math.round(1+(a.upperLongitude-a.lowerLongitude)/a.longitudeInterval),l=Math.round(1+(a.upperLatitude-a.lowerLatitude)/a.latitudeInterval);r.push({ll:[Ht(a.lowerLongitude),Ht(a.lowerLatitude)],del:[Ht(a.longitudeInterval),Ht(a.latitudeInterval)],lim:[o,l],count:a.gridNodeCount,cvs:Ch(h)})}return r}function Ch(t){return t.map(function(e){return[Ht(e.longitudeShift),Ht(e.latitudeShift)]})}function Oh(t,e,i){return{name:Je(t,e+8,e+16).trim(),parent:Je(t,e+24,e+24+8).trim(),lowerLatitude:t.getFloat64(e+72,i),upperLatitude:t.getFloat64(e+88,i),lowerLongitude:t.getFloat64(e+104,i),upperLongitude:t.getFloat64(e+120,i),latitudeInterval:t.getFloat64(e+136,i),longitudeInterval:t.getFloat64(e+152,i),gridNodeCount:t.getInt32(e+168,i)}}function Uh(t,e,i,s){for(var r=e+176,n=16,a=[],h=0;h<i.gridNodeCount;h++){var o={latitudeShift:t.getFloat32(r+h*n,s),longitudeShift:t.getFloat32(r+h*n+4,s),latitudeAccuracy:t.getFloat32(r+h*n+8,s),longitudeAccuracy:t.getFloat32(r+h*n+12,s)};a.push(o)}return a}function pt(t,e){if(!(this instanceof pt))return new pt(t);e=e||function(l){if(l)throw l};var i=fh(t);if(typeof i!="object"){e(t);return}var s=pt.projections.get(i.projName);if(!s){e(t);return}if(i.datumCode&&i.datumCode!=="none"){var r=wt(X,i.datumCode);r&&(i.datum_params=i.datum_params||(r.towgs84?r.towgs84.split(","):null),i.ellps=r.ellipse,i.datumName=r.datumName?r.datumName:i.datumCode)}i.k0=i.k0||1,i.axis=i.axis||"enu",i.ellps=i.ellps||"wgs84",i.lat1=i.lat1||i.lat0;var n=wh(i.a,i.b,i.rf,i.ellps,i.sphere),a=bh(n.a,n.b,n.rf,i.R_A),h=Rh(i.nadgrids),o=i.datum||Th(i.datumCode,i.datum_params,n.a,n.b,a.es,a.ep2,h);fs(this,i),fs(this,s),this.a=n.a,this.b=n.b,this.rf=n.rf,this.sphere=n.sphere,this.es=a.es,this.e=a.e,this.ep2=a.ep2,this.datum=o,this.init(),e(null,this)}pt.projections=Eh,pt.projections.start();function Bh(t,e){return t.datum_type!==e.datum_type||t.a!==e.a||Math.abs(t.es-e.es)>5e-11?!1:t.datum_type===Ct?t.datum_params[0]===e.datum_params[0]&&t.datum_params[1]===e.datum_params[1]&&t.datum_params[2]===e.datum_params[2]:t.datum_type===Ot?t.datum_params[0]===e.datum_params[0]&&t.datum_params[1]===e.datum_params[1]&&t.datum_params[2]===e.datum_params[2]&&t.datum_params[3]===e.datum_params[3]&&t.datum_params[4]===e.datum_params[4]&&t.datum_params[5]===e.datum_params[5]&&t.datum_params[6]===e.datum_params[6]:!0}function ms(t,e,i){var s=t.x,r=t.y,n=t.z?t.z:0,a,h,o,l;if(r<-E&&r>-1.001*E)r=-E;else if(r>E&&r<1.001*E)r=E;else{if(r<-E)return{x:-1/0,y:-1/0,z:t.z};if(r>E)return{x:1/0,y:1/0,z:t.z}}return s>Math.PI&&(s-=2*Math.PI),h=Math.sin(r),l=Math.cos(r),o=h*h,a=i/Math.sqrt(1-e*o),{x:(a+n)*l*Math.cos(s),y:(a+n)*l*Math.sin(s),z:(a*(1-e)+n)*h}}function gs(t,e,i,s){var r=1e-12,n=r*r,a=30,h,o,l,f,u,c,v,d,g,p,y,x,M,_=t.x,S=t.y,b=t.z?t.z:0,L,A,I;if(h=Math.sqrt(_*_+S*S),o=Math.sqrt(_*_+S*S+b*b),h/i<r){if(L=0,o/i<r)return A=E,I=-s,{x:t.x,y:t.y,z:t.z}}else L=Math.atan2(S,_);l=b/o,f=h/o,u=1/Math.sqrt(1-e*(2-e)*f*f),d=f*(1-e)*u,g=l*u,M=0;do M++,v=i/Math.sqrt(1-e*g*g),I=h*d+b*g-v*(1-e*g*g),c=e*v/(v+I),u=1/Math.sqrt(1-c*(2-c)*f*f),p=f*(1-c)*u,y=l*u,x=y*d-p*g,d=p,g=y;while(x*x>n&&M<a);return A=Math.atan(y/Math.abs(p)),{x:L,y:A,z:I}}function Dh(t,e,i){if(e===Ct)return{x:t.x+i[0],y:t.y+i[1],z:t.z+i[2]};if(e===Ot){var s=i[0],r=i[1],n=i[2],a=i[3],h=i[4],o=i[5],l=i[6];return{x:l*(t.x-o*t.y+h*t.z)+s,y:l*(o*t.x+t.y-a*t.z)+r,z:l*(-h*t.x+a*t.y+t.z)+n}}}function zh(t,e,i){if(e===Ct)return{x:t.x-i[0],y:t.y-i[1],z:t.z-i[2]};if(e===Ot){var s=i[0],r=i[1],n=i[2],a=i[3],h=i[4],o=i[5],l=i[6],f=(t.x-s)/l,u=(t.y-r)/l,c=(t.z-n)/l;return{x:f+o*u-h*c,y:-o*f+u+a*c,z:h*f-a*u+c}}}function we(t){return t===Ct||t===Ot}function kh(t,e,i){if(Bh(t,e)||t.datum_type===Ke||e.datum_type===Ke)return i;var s=t.a,r=t.es;if(t.datum_type===Ft){var n=ps(t,!1,i);if(n!==0)return;s=is,r=ss}var a=e.a,h=e.b,o=e.es;if(e.datum_type===Ft&&(a=is,h=$a,o=ss),r===o&&s===a&&!we(t.datum_type)&&!we(e.datum_type))return i;if(i=ms(i,r,s),we(t.datum_type)&&(i=Dh(i,t.datum_type,t.datum_params)),we(e.datum_type)&&(i=zh(i,e.datum_type,e.datum_params)),i=gs(i,o,a,h),e.datum_type===Ft){var l=ps(e,!0,i);if(l!==0)return}return i}function ps(t,e,i){if(t.grids===null||t.grids.length===0)return console.log("Grid shift grids not found"),-1;for(var s={x:-i.x,y:i.y},r={x:Number.NaN,y:Number.NaN},n=[],a=0;a<t.grids.length;a++){var h=t.grids[a];if(n.push(h.name),h.isNull){r=s;break}if(h.mandatory,h.grid===null){if(h.mandatory)return console.log("Unable to find mandatory grid '"+h.name+"'"),-1;continue}var o=h.grid.subgrids[0],l=(Math.abs(o.del[1])+Math.abs(o.del[0]))/1e4,f=o.ll[0]-l,u=o.ll[1]-l,c=o.ll[0]+(o.lim[0]-1)*o.del[0]+l,v=o.ll[1]+(o.lim[1]-1)*o.del[1]+l;if(!(u>s.y||f>s.x||v<s.y||c<s.x)&&(r=Fh(s,e,o),!isNaN(r.x)))break}return isNaN(r.x)?(console.log("Failed to find a grid shift table for location '"+-s.x*mt+" "+s.y*mt+" tried: '"+n+"'"),-1):(i.x=-r.x,i.y=r.y,0)}function Fh(t,e,i){var s={x:Number.NaN,y:Number.NaN};if(isNaN(t.x))return s;var r={x:t.x,y:t.y};r.x-=i.ll[0],r.y-=i.ll[1],r.x=T(r.x-Math.PI)+Math.PI;var n=ys(r,i);if(e){if(isNaN(n.x))return s;n.x=r.x-n.x,n.y=r.y-n.y;var a=9,h=1e-12,o,l;do{if(l=ys(n,i),isNaN(l.x)){console.log("Inverse grid shift iteration failed, presumably at grid edge.  Using first approximation.");break}o={x:r.x-(l.x+n.x),y:r.y-(l.y+n.y)},n.x+=o.x,n.y+=o.y}while(a--&&Math.abs(o.x)>h&&Math.abs(o.y)>h);if(a<0)return console.log("Inverse grid shift iterator failed to converge."),s;s.x=T(n.x+i.ll[0]),s.y=n.y+i.ll[1]}else isNaN(n.x)||(s.x=t.x+n.x,s.y=t.y+n.y);return s}function ys(t,e){var i={x:t.x/e.del[0],y:t.y/e.del[1]},s={x:Math.floor(i.x),y:Math.floor(i.y)},r={x:i.x-1*s.x,y:i.y-1*s.y},n={x:Number.NaN,y:Number.NaN},a;if(s.x<0||s.x>=e.lim[0]||s.y<0||s.y>=e.lim[1])return n;a=s.y*e.lim[0]+s.x;var h={x:e.cvs[a][0],y:e.cvs[a][1]};a++;var o={x:e.cvs[a][0],y:e.cvs[a][1]};a+=e.lim[0];var l={x:e.cvs[a][0],y:e.cvs[a][1]};a--;var f={x:e.cvs[a][0],y:e.cvs[a][1]},u=r.x*r.y,c=r.x*(1-r.y),v=(1-r.x)*(1-r.y),d=(1-r.x)*r.y;return n.x=v*h.x+c*o.x+d*f.x+u*l.x,n.y=v*h.y+c*o.y+d*f.y+u*l.y,n}function Ms(t,e,i){var s=i.x,r=i.y,n=i.z||0,a,h,o,l={};for(o=0;o<3;o++)if(!(e&&o===2&&i.z===void 0))switch(o===0?(a=s,"ew".indexOf(t.axis[o])!==-1?h="x":h="y"):o===1?(a=r,"ns".indexOf(t.axis[o])!==-1?h="y":h="x"):(a=n,h="z"),t.axis[o]){case"e":l[h]=a;break;case"w":l[h]=-a;break;case"n":l[h]=a;break;case"s":l[h]=-a;break;case"u":i[h]!==void 0&&(l.z=a);break;case"d":i[h]!==void 0&&(l.z=-a);break;default:return null}return l}function xs(t){var e={x:t[0],y:t[1]};return t.length>2&&(e.z=t[2]),t.length>3&&(e.m=t[3]),e}function Gh(t){_s(t.x),_s(t.y)}function _s(t){if(typeof Number.isFinite=="function"){if(Number.isFinite(t))return;throw new TypeError("coordinates must be finite numbers")}if(typeof t!="number"||t!==t||!isFinite(t))throw new TypeError("coordinates must be finite numbers")}function Hh(t,e){return(t.datum.datum_type===Ct||t.datum.datum_type===Ot||t.datum.datum_type===Ft)&&e.datumCode!=="WGS84"||(e.datum.datum_type===Ct||e.datum.datum_type===Ot||e.datum.datum_type===Ft)&&t.datumCode!=="WGS84"}function Te(t,e,i,s){var r;Array.isArray(i)?i=xs(i):i={x:i.x,y:i.y,z:i.z,m:i.m};var n=i.z!==void 0;if(Gh(i),t.datum&&e.datum&&Hh(t,e)&&(r=new pt("WGS84"),i=Te(t,r,i,s),t=r),s&&t.axis!=="enu"&&(i=Ms(t,!1,i)),t.projName==="longlat")i={x:i.x*j,y:i.y*j,z:i.z||0};else if(t.to_meter&&(i={x:i.x*t.to_meter,y:i.y*t.to_meter,z:i.z||0}),i=t.inverse(i),!i)return;if(t.from_greenwich&&(i.x+=t.from_greenwich),i=kh(t.datum,e.datum,i),!!i)return e.from_greenwich&&(i={x:i.x-e.from_greenwich,y:i.y,z:i.z||0}),e.projName==="longlat"?i={x:i.x*mt,y:i.y*mt,z:i.z||0}:(i=e.forward(i),e.to_meter&&(i={x:i.x/e.to_meter,y:i.y/e.to_meter,z:i.z||0})),s&&e.axis!=="enu"?Ms(e,!0,i):(n||delete i.z,i)}var Es=pt("WGS84");function ti(t,e,i,s){var r,n,a;return Array.isArray(i)?(r=Te(t,e,i,s)||{x:NaN,y:NaN},i.length>2?typeof t.name<"u"&&t.name==="geocent"||typeof e.name<"u"&&e.name==="geocent"?typeof r.z=="number"?[r.x,r.y,r.z].concat(i.splice(3)):[r.x,r.y,i[2]].concat(i.splice(3)):[r.x,r.y].concat(i.splice(2)):[r.x,r.y]):(n=Te(t,e,i,s),a=Object.keys(i),a.length===2||a.forEach(function(h){if(typeof t.name<"u"&&t.name==="geocent"||typeof e.name<"u"&&e.name==="geocent"){if(h==="x"||h==="y"||h==="z")return}else if(h==="x"||h==="y")return;n[h]=i[h]}),n)}function Ss(t){return t instanceof pt?t:t.oProj?t.oProj:pt(t)}function rt(t,e,i){t=Ss(t);var s=!1,r;return typeof e>"u"?(e=t,t=Es,s=!0):(typeof e.x<"u"||Array.isArray(e))&&(i=e,e=t,t=Es,s=!0),e=Ss(e),i?ti(t,e,i):(r={forward:function(n,a){return ti(t,e,n,a)},inverse:function(n,a){return ti(e,t,n,a)}},s&&(r.oProj=e),r)}var bs=6,ws="AJSAJS",Ts="AFAFAF",qt=65,et=73,nt=79,re=86,ne=90;const qh={forward:As,inverse:Vh,toPoint:Rs};function As(t,e){return e=e||5,Xh($h({lat:t[1],lon:t[0]}),e)}function Vh(t){var e=ii(Ns(t.toUpperCase()));return e.lat&&e.lon?[e.lon,e.lat,e.lon,e.lat]:[e.left,e.bottom,e.right,e.top]}function Rs(t){var e=ii(Ns(t.toUpperCase()));return e.lat&&e.lon?[e.lon,e.lat]:[(e.left+e.right)/2,(e.top+e.bottom)/2]}function ei(t){return t*(Math.PI/180)}function Ls(t){return 180*(t/Math.PI)}function $h(t){var e=t.lat,i=t.lon,s=6378137,r=.00669438,n=.9996,a,h,o,l,f,u,c,v=ei(e),d=ei(i),g,p;p=Math.floor((i+180)/6)+1,i===180&&(p=60),e>=56&&e<64&&i>=3&&i<12&&(p=32),e>=72&&e<84&&(i>=0&&i<9?p=31:i>=9&&i<21?p=33:i>=21&&i<33?p=35:i>=33&&i<42&&(p=37)),a=(p-1)*6-180+3,g=ei(a),h=r/(1-r),o=s/Math.sqrt(1-r*Math.sin(v)*Math.sin(v)),l=Math.tan(v)*Math.tan(v),f=h*Math.cos(v)*Math.cos(v),u=Math.cos(v)*(d-g),c=s*((1-r/4-3*r*r/64-5*r*r*r/256)*v-(3*r/8+3*r*r/32+45*r*r*r/1024)*Math.sin(2*v)+(15*r*r/256+45*r*r*r/1024)*Math.sin(4*v)-35*r*r*r/3072*Math.sin(6*v));var y=n*o*(u+(1-l+f)*u*u*u/6+(5-18*l+l*l+72*f-58*h)*u*u*u*u*u/120)+5e5,x=n*(c+o*Math.tan(v)*(u*u/2+(5-l+9*f+4*f*f)*u*u*u*u/24+(61-58*l+l*l+600*f-330*h)*u*u*u*u*u*u/720));return e<0&&(x+=1e7),{northing:Math.round(x),easting:Math.round(y),zoneNumber:p,zoneLetter:Wh(e)}}function ii(t){var e=t.northing,i=t.easting,s=t.zoneLetter,r=t.zoneNumber;if(r<0||r>60)return null;var n=.9996,a=6378137,h=.00669438,o,l=(1-Math.sqrt(1-h))/(1+Math.sqrt(1-h)),f,u,c,v,d,g,p,y,x,M=i-5e5,_=e;s<"N"&&(_-=1e7),p=(r-1)*6-180+3,o=h/(1-h),g=_/n,y=g/(a*(1-h/4-3*h*h/64-5*h*h*h/256)),x=y+(3*l/2-27*l*l*l/32)*Math.sin(2*y)+(21*l*l/16-55*l*l*l*l/32)*Math.sin(4*y)+151*l*l*l/96*Math.sin(6*y),f=a/Math.sqrt(1-h*Math.sin(x)*Math.sin(x)),u=Math.tan(x)*Math.tan(x),c=o*Math.cos(x)*Math.cos(x),v=a*(1-h)/Math.pow(1-h*Math.sin(x)*Math.sin(x),1.5),d=M/(f*n);var S=x-f*Math.tan(x)/v*(d*d/2-(5+3*u+10*c-4*c*c-9*o)*d*d*d*d/24+(61+90*u+298*c+45*u*u-252*o-3*c*c)*d*d*d*d*d*d/720);S=Ls(S);var b=(d-(1+2*u+c)*d*d*d/6+(5-2*c+28*u-3*c*c+8*o+24*u*u)*d*d*d*d*d/120)/Math.cos(x);b=p+Ls(b);var L;if(t.accuracy){var A=ii({northing:t.northing+t.accuracy,easting:t.easting+t.accuracy,zoneLetter:t.zoneLetter,zoneNumber:t.zoneNumber});L={top:A.lat,right:A.lon,bottom:S,left:b}}else L={lat:S,lon:b};return L}function Wh(t){var e="Z";return 84>=t&&t>=72?e="X":72>t&&t>=64?e="W":64>t&&t>=56?e="V":56>t&&t>=48?e="U":48>t&&t>=40?e="T":40>t&&t>=32?e="S":32>t&&t>=24?e="R":24>t&&t>=16?e="Q":16>t&&t>=8?e="P":8>t&&t>=0?e="N":0>t&&t>=-8?e="M":-8>t&&t>=-16?e="L":-16>t&&t>=-24?e="K":-24>t&&t>=-32?e="J":-32>t&&t>=-40?e="H":-40>t&&t>=-48?e="G":-48>t&&t>=-56?e="F":-56>t&&t>=-64?e="E":-64>t&&t>=-72?e="D":-72>t&&t>=-80&&(e="C"),e}function Xh(t,e){var i="00000"+t.easting,s="00000"+t.northing;return t.zoneNumber+t.zoneLetter+Qh(t.easting,t.northing,t.zoneNumber)+i.substr(i.length-5,e)+s.substr(s.length-5,e)}function Qh(t,e,i){var s=Is(i),r=Math.floor(t/1e5),n=Math.floor(e/1e5)%20;return jh(r,n,s)}function Is(t){var e=t%bs;return e===0&&(e=bs),e}function jh(t,e,i){var s=i-1,r=ws.charCodeAt(s),n=Ts.charCodeAt(s),a=r+t-1,h=n+e,o=!1;a>ne&&(a=a-ne+qt-1,o=!0),(a===et||r<et&&a>et||(a>et||r<et)&&o)&&a++,(a===nt||r<nt&&a>nt||(a>nt||r<nt)&&o)&&(a++,a===et&&a++),a>ne&&(a=a-ne+qt-1),h>re?(h=h-re+qt-1,o=!0):o=!1,(h===et||n<et&&h>et||(h>et||n<et)&&o)&&h++,(h===nt||n<nt&&h>nt||(h>nt||n<nt)&&o)&&(h++,h===et&&h++),h>re&&(h=h-re+qt-1);var l=String.fromCharCode(a)+String.fromCharCode(h);return l}function Ns(t){if(t&&t.length===0)throw"MGRSPoint coverting from nothing";for(var e=t.length,i=null,s="",r,n=0;!/[A-Z]/.test(r=t.charAt(n));){if(n>=2)throw"MGRSPoint bad conversion from: "+t;s+=r,n++}var a=parseInt(s,10);if(n===0||n+3>e)throw"MGRSPoint bad conversion from: "+t;var h=t.charAt(n++);if(h<="A"||h==="B"||h==="Y"||h>="Z"||h==="I"||h==="O")throw"MGRSPoint zone letter "+h+" not handled: "+t;i=t.substring(n,n+=2);for(var o=Is(a),l=Kh(i.charAt(0),o),f=Yh(i.charAt(1),o);f<Zh(h);)f+=2e6;var u=e-n;if(u%2!==0)throw`MGRSPoint has to have an even number 
of digits after the zone letter and two 100km letters - front 
half for easting meters, second half for 
northing meters`+t;var c=u/2,v=0,d=0,g,p,y,x,M;return c>0&&(g=1e5/Math.pow(10,c),p=t.substring(n,n+c),v=parseFloat(p)*g,y=t.substring(n+c),d=parseFloat(y)*g),x=v+l,M=d+f,{easting:x,northing:M,zoneLetter:h,zoneNumber:a,accuracy:g}}function Kh(t,e){for(var i=ws.charCodeAt(e-1),s=1e5,r=!1;i!==t.charCodeAt(0);){if(i++,i===et&&i++,i===nt&&i++,i>ne){if(r)throw"Bad character: "+t;i=qt,r=!0}s+=1e5}return s}function Yh(t,e){if(t>"V")throw"MGRSPoint given invalid Northing "+t;for(var i=Ts.charCodeAt(e-1),s=0,r=!1;i!==t.charCodeAt(0);){if(i++,i===et&&i++,i===nt&&i++,i>re){if(r)throw"Bad character: "+t;i=qt,r=!0}s+=1e5}return s}function Zh(t){var e;switch(t){case"C":e=11e5;break;case"D":e=2e6;break;case"E":e=28e5;break;case"F":e=37e5;break;case"G":e=46e5;break;case"H":e=55e5;break;case"J":e=64e5;break;case"K":e=73e5;break;case"L":e=82e5;break;case"M":e=91e5;break;case"N":e=0;break;case"P":e=8e5;break;case"Q":e=17e5;break;case"R":e=26e5;break;case"S":e=35e5;break;case"T":e=44e5;break;case"U":e=53e5;break;case"V":e=62e5;break;case"W":e=7e6;break;case"X":e=79e5;break;default:e=-1}if(e>=0)return e;throw"Invalid zone letter: "+t}function Vt(t,e,i){if(!(this instanceof Vt))return new Vt(t,e,i);if(Array.isArray(t))this.x=t[0],this.y=t[1],this.z=t[2]||0;else if(typeof t=="object")this.x=t.x,this.y=t.y,this.z=t.z||0;else if(typeof t=="string"&&typeof e>"u"){var s=t.split(",");this.x=parseFloat(s[0],10),this.y=parseFloat(s[1],10),this.z=parseFloat(s[2],10)||0}else this.x=t,this.y=e,this.z=i||0;console.warn("proj4.Point will be removed in version 3, use proj4.toPoint")}Vt.fromMGRS=function(t){return new Vt(Rs(t))},Vt.prototype.toMGRS=function(t){return As([this.x,this.y],t)};var Jh=1,to=.25,Ps=.046875,Cs=.01953125,Os=.01068115234375,eo=.75,io=.46875,so=.013020833333333334,ro=.007120768229166667,no=.3645833333333333,ao=.005696614583333333,ho=.3076171875;function Us(t){var e=[];e[0]=Jh-t*(to+t*(Ps+t*(Cs+t*Os))),e[1]=t*(eo-t*(Ps+t*(Cs+t*Os)));var i=t*t;return e[2]=i*(io-t*(so+t*ro)),i*=t,e[3]=i*(no-t*ao),e[4]=i*t*ho,e}function Ae(t,e,i,s){return i*=e,e*=e,s[0]*t-i*(s[1]+e*(s[2]+e*(s[3]+e*s[4])))}var oo=20;function Bs(t,e,i){for(var s=1/(1-e),r=t,n=oo;n;--n){var a=Math.sin(r),h=1-e*a*a;if(h=(Ae(r,a,Math.cos(r),i)-t)*(h*Math.sqrt(h))*s,r-=h,Math.abs(h)<w)return r}return r}function lo(){this.x0=this.x0!==void 0?this.x0:0,this.y0=this.y0!==void 0?this.y0:0,this.long0=this.long0!==void 0?this.long0:0,this.lat0=this.lat0!==void 0?this.lat0:0,this.es&&(this.en=Us(this.es),this.ml0=Ae(this.lat0,Math.sin(this.lat0),Math.cos(this.lat0),this.en))}function uo(t){var e=t.x,i=t.y,s=T(e-this.long0),r,n,a,h=Math.sin(i),o=Math.cos(i);if(this.es){var f=o*s,u=Math.pow(f,2),c=this.ep2*Math.pow(o,2),v=Math.pow(c,2),d=Math.abs(o)>w?Math.tan(i):0,g=Math.pow(d,2),p=Math.pow(g,2);r=1-this.es*Math.pow(h,2),f=f/Math.sqrt(r);var y=Ae(i,h,o,this.en);n=this.a*(this.k0*f*(1+u/6*(1-g+c+u/20*(5-18*g+p+14*c-58*g*c+u/42*(61+179*p-p*g-479*g)))))+this.x0,a=this.a*(this.k0*(y-this.ml0+h*s*f/2*(1+u/12*(5-g+9*c+4*v+u/30*(61+p-58*g+270*c-330*g*c+u/56*(1385+543*p-p*g-3111*g))))))+this.y0}else{var l=o*Math.sin(s);if(Math.abs(Math.abs(l)-1)<w)return 93;if(n=.5*this.a*this.k0*Math.log((1+l)/(1-l))+this.x0,a=o*Math.cos(s)/Math.sqrt(1-Math.pow(l,2)),l=Math.abs(a),l>=1){if(l-1>w)return 93;a=0}else a=Math.acos(a);i<0&&(a=-a),a=this.a*this.k0*(a-this.lat0)+this.y0}return t.x=n,t.y=a,t}function fo(t){var e,i,s,r,n=(t.x-this.x0)*(1/this.a),a=(t.y-this.y0)*(1/this.a);if(this.es)if(e=this.ml0+a/this.k0,i=Bs(e,this.es,this.en),Math.abs(i)<E){var u=Math.sin(i),c=Math.cos(i),v=Math.abs(c)>w?Math.tan(i):0,d=this.ep2*Math.pow(c,2),g=Math.pow(d,2),p=Math.pow(v,2),y=Math.pow(p,2);e=1-this.es*Math.pow(u,2);var x=n*Math.sqrt(e)/this.k0,M=Math.pow(x,2);e=e*v,s=i-e*M/(1-this.es)*.5*(1-M/12*(5+3*p-9*d*p+d-4*g-M/30*(61+90*p-252*d*p+45*y+46*d-M/56*(1385+3633*p+4095*y+1574*y*p)))),r=T(this.long0+x*(1-M/6*(1+2*p+d-M/20*(5+28*p+24*y+8*d*p+6*d-M/42*(61+662*p+1320*y+720*y*p))))/c)}else s=E*ie(a),r=0;else{var h=Math.exp(n/this.k0),o=.5*(h-1/h),l=this.lat0+a/this.k0,f=Math.cos(l);e=Math.sqrt((1-Math.pow(f,2))/(1+Math.pow(o,2))),s=Math.asin(e),a<0&&(s=-s),o===0&&f===0?r=0:r=T(Math.atan2(o,f)+this.long0)}return t.x=r,t.y=s,t}var co=["Fast_Transverse_Mercator","Fast Transverse Mercator"];const Re={init:lo,forward:uo,inverse:fo,names:co};function Ds(t){var e=Math.exp(t);return e=(e-1/e)/2,e}function ut(t,e){t=Math.abs(t),e=Math.abs(e);var i=Math.max(t,e),s=Math.min(t,e)/(i||1);return i*Math.sqrt(1+Math.pow(s,2))}function vo(t){var e=1+t,i=e-1;return i===0?t:t*Math.log(e)/i}function mo(t){var e=Math.abs(t);return e=vo(e*(1+e/(ut(1,e)+1))),t<0?-e:e}function si(t,e){for(var i=2*Math.cos(2*e),s=t.length-1,r=t[s],n=0,a;--s>=0;)a=-n+i*r+t[s],n=r,r=a;return e+a*Math.sin(2*e)}function go(t,e){for(var i=2*Math.cos(e),s=t.length-1,r=t[s],n=0,a;--s>=0;)a=-n+i*r+t[s],n=r,r=a;return Math.sin(e)*a}function po(t){var e=Math.exp(t);return e=(e+1/e)/2,e}function zs(t,e,i){for(var s=Math.sin(e),r=Math.cos(e),n=Ds(i),a=po(i),h=2*r*a,o=-2*s*n,l=t.length-1,f=t[l],u=0,c=0,v=0,d,g;--l>=0;)d=c,g=u,c=f,u=v,f=-d+h*c-o*u+t[l],v=-g+o*c+h*u;return h=s*a,o=r*n,[h*f-o*v,h*v+o*f]}function yo(){if(!this.approx&&(isNaN(this.es)||this.es<=0))throw new Error('Incorrect elliptical usage. Try using the +approx option in the proj string, or PROJECTION["Fast_Transverse_Mercator"] in the WKT.');this.approx&&(Re.init.apply(this),this.forward=Re.forward,this.inverse=Re.inverse),this.x0=this.x0!==void 0?this.x0:0,this.y0=this.y0!==void 0?this.y0:0,this.long0=this.long0!==void 0?this.long0:0,this.lat0=this.lat0!==void 0?this.lat0:0,this.cgb=[],this.cbg=[],this.utg=[],this.gtu=[];var t=this.es/(1+Math.sqrt(1-this.es)),e=t/(2-t),i=e;this.cgb[0]=e*(2+e*(-2/3+e*(-2+e*(116/45+e*(26/45+e*(-2854/675)))))),this.cbg[0]=e*(-2+e*(2/3+e*(4/3+e*(-82/45+e*(32/45+e*(4642/4725)))))),i=i*e,this.cgb[1]=i*(7/3+e*(-8/5+e*(-227/45+e*(2704/315+e*(2323/945))))),this.cbg[1]=i*(5/3+e*(-16/15+e*(-13/9+e*(904/315+e*(-1522/945))))),i=i*e,this.cgb[2]=i*(56/15+e*(-136/35+e*(-1262/105+e*(73814/2835)))),this.cbg[2]=i*(-26/15+e*(34/21+e*(8/5+e*(-12686/2835)))),i=i*e,this.cgb[3]=i*(4279/630+e*(-332/35+e*(-399572/14175))),this.cbg[3]=i*(1237/630+e*(-12/5+e*(-24832/14175))),i=i*e,this.cgb[4]=i*(4174/315+e*(-144838/6237)),this.cbg[4]=i*(-734/315+e*(109598/31185)),i=i*e,this.cgb[5]=i*(601676/22275),this.cbg[5]=i*(444337/155925),i=Math.pow(e,2),this.Qn=this.k0/(1+e)*(1+i*(1/4+i*(1/64+i/256))),this.utg[0]=e*(-.5+e*(2/3+e*(-37/96+e*(1/360+e*(81/512+e*(-96199/604800)))))),this.gtu[0]=e*(.5+e*(-2/3+e*(5/16+e*(41/180+e*(-127/288+e*(7891/37800)))))),this.utg[1]=i*(-1/48+e*(-1/15+e*(437/1440+e*(-46/105+e*(1118711/3870720))))),this.gtu[1]=i*(13/48+e*(-3/5+e*(557/1440+e*(281/630+e*(-1983433/1935360))))),i=i*e,this.utg[2]=i*(-17/480+e*(37/840+e*(209/4480+e*(-5569/90720)))),this.gtu[2]=i*(61/240+e*(-103/140+e*(15061/26880+e*(167603/181440)))),i=i*e,this.utg[3]=i*(-4397/161280+e*(11/504+e*(830251/7257600))),this.gtu[3]=i*(49561/161280+e*(-179/168+e*(6601661/7257600))),i=i*e,this.utg[4]=i*(-4583/161280+e*(108847/3991680)),this.gtu[4]=i*(34729/80640+e*(-3418889/1995840)),i=i*e,this.utg[5]=i*(-20648693/638668800),this.gtu[5]=i*(212378941/319334400);var s=si(this.cbg,this.lat0);this.Zb=-this.Qn*(s+go(this.gtu,2*s))}function Mo(t){var e=T(t.x-this.long0),i=t.y;i=si(this.cbg,i);var s=Math.sin(i),r=Math.cos(i),n=Math.sin(e),a=Math.cos(e);i=Math.atan2(s,a*r),e=Math.atan2(n*r,ut(s,r*a)),e=mo(Math.tan(e));var h=zs(this.gtu,2*i,2*e);i=i+h[0],e=e+h[1];var o,l;return Math.abs(e)<=2.623395162778?(o=this.a*(this.Qn*e)+this.x0,l=this.a*(this.Qn*i+this.Zb)+this.y0):(o=1/0,l=1/0),t.x=o,t.y=l,t}function xo(t){var e=(t.x-this.x0)*(1/this.a),i=(t.y-this.y0)*(1/this.a);i=(i-this.Zb)/this.Qn,e=e/this.Qn;var s,r;if(Math.abs(e)<=2.623395162778){var n=zs(this.utg,2*i,2*e);i=i+n[0],e=e+n[1],e=Math.atan(Ds(e));var a=Math.sin(i),h=Math.cos(i),o=Math.sin(e),l=Math.cos(e);i=Math.atan2(a*l,ut(o,l*h)),e=Math.atan2(o,l*h),s=T(e+this.long0),r=si(this.cgb,i)}else s=1/0,r=1/0;return t.x=s,t.y=r,t}var _o=["Extended_Transverse_Mercator","Extended Transverse Mercator","etmerc","Transverse_Mercator","Transverse Mercator","tmerc"];const Le={init:yo,forward:Mo,inverse:xo,names:_o};function Eo(t,e){if(t===void 0){if(t=Math.floor((T(e)+Math.PI)*30/Math.PI)+1,t<0)return 0;if(t>60)return 60}return t}var So="etmerc";function bo(){var t=Eo(this.zone,this.long0);if(t===void 0)throw new Error("unknown utm zone");this.lat0=0,this.long0=(6*Math.abs(t)-183)*j,this.x0=5e5,this.y0=this.utmSouth?1e7:0,this.k0=.9996,Le.init.apply(this),this.forward=Le.forward,this.inverse=Le.inverse}var wo=["Universal Transverse Mercator System","utm"];const To={init:bo,names:wo,dependsOn:So};function ri(t,e){return Math.pow((1-t)/(1+t),e)}var Ao=20;function Ro(){var t=Math.sin(this.lat0),e=Math.cos(this.lat0);e*=e,this.rc=Math.sqrt(1-this.es)/(1-this.es*t*t),this.C=Math.sqrt(1+this.es*e*e/(1-this.es)),this.phic0=Math.asin(t/this.C),this.ratexp=.5*this.C*this.e,this.K=Math.tan(.5*this.phic0+B)/(Math.pow(Math.tan(.5*this.lat0+B),this.C)*ri(this.e*t,this.ratexp))}function Lo(t){var e=t.x,i=t.y;return t.y=2*Math.atan(this.K*Math.pow(Math.tan(.5*i+B),this.C)*ri(this.e*Math.sin(i),this.ratexp))-E,t.x=this.C*e,t}function Io(t){for(var e=1e-14,i=t.x/this.C,s=t.y,r=Math.pow(Math.tan(.5*s+B)/this.K,1/this.C),n=Ao;n>0&&(s=2*Math.atan(r*ri(this.e*Math.sin(t.y),-.5*this.e))-E,!(Math.abs(s-t.y)<e));--n)t.y=s;return n?(t.x=i,t.y=s,t):null}var No=["gauss"];const ni={init:Ro,forward:Lo,inverse:Io,names:No};function Po(){ni.init.apply(this),this.rc&&(this.sinc0=Math.sin(this.phic0),this.cosc0=Math.cos(this.phic0),this.R2=2*this.rc,this.title||(this.title="Oblique Stereographic Alternative"))}function Co(t){var e,i,s,r;return t.x=T(t.x-this.long0),ni.forward.apply(this,[t]),e=Math.sin(t.y),i=Math.cos(t.y),s=Math.cos(t.x),r=this.k0*this.R2/(1+this.sinc0*e+this.cosc0*i*s),t.x=r*i*Math.sin(t.x),t.y=r*(this.cosc0*e-this.sinc0*i*s),t.x=this.a*t.x+this.x0,t.y=this.a*t.y+this.y0,t}function Oo(t){var e,i,s,r,n;if(t.x=(t.x-this.x0)/this.a,t.y=(t.y-this.y0)/this.a,t.x/=this.k0,t.y/=this.k0,n=Math.sqrt(t.x*t.x+t.y*t.y)){var a=2*Math.atan2(n,this.R2);e=Math.sin(a),i=Math.cos(a),r=Math.asin(i*this.sinc0+t.y*e*this.cosc0/n),s=Math.atan2(t.x*e,n*this.cosc0*i-t.y*this.sinc0*e)}else r=this.phic0,s=0;return t.x=s,t.y=r,ni.inverse.apply(this,[t]),t.x=T(t.x+this.long0),t}var Uo=["Stereographic_North_Pole","Oblique_Stereographic","Polar_Stereographic","sterea","Oblique Stereographic Alternative","Double_Stereographic"];const Bo={init:Po,forward:Co,inverse:Oo,names:Uo};function Do(t,e,i){return e*=i,Math.tan(.5*(E+t))*Math.pow((1-e)/(1+e),.5*i)}function zo(){this.coslat0=Math.cos(this.lat0),this.sinlat0=Math.sin(this.lat0),this.sphere?this.k0===1&&!isNaN(this.lat_ts)&&Math.abs(this.coslat0)<=w&&(this.k0=.5*(1+ie(this.lat0)*Math.sin(this.lat_ts))):(Math.abs(this.coslat0)<=w&&(this.lat0>0?this.con=1:this.con=-1),this.cons=Math.sqrt(Math.pow(1+this.e,1+this.e)*Math.pow(1-this.e,1-this.e)),this.k0===1&&!isNaN(this.lat_ts)&&Math.abs(this.coslat0)<=w&&(this.k0=.5*this.cons*gt(this.e,Math.sin(this.lat_ts),Math.cos(this.lat_ts))/lt(this.e,this.con*this.lat_ts,this.con*Math.sin(this.lat_ts))),this.ms1=gt(this.e,this.sinlat0,this.coslat0),this.X0=2*Math.atan(this.ssfn_(this.lat0,this.sinlat0,this.e))-E,this.cosX0=Math.cos(this.X0),this.sinX0=Math.sin(this.X0))}function ko(t){var e=t.x,i=t.y,s=Math.sin(i),r=Math.cos(i),n,a,h,o,l,f,u=T(e-this.long0);return Math.abs(Math.abs(e-this.long0)-Math.PI)<=w&&Math.abs(i+this.lat0)<=w?(t.x=NaN,t.y=NaN,t):this.sphere?(n=2*this.k0/(1+this.sinlat0*s+this.coslat0*r*Math.cos(u)),t.x=this.a*n*r*Math.sin(u)+this.x0,t.y=this.a*n*(this.coslat0*s-this.sinlat0*r*Math.cos(u))+this.y0,t):(a=2*Math.atan(this.ssfn_(i,s,this.e))-E,o=Math.cos(a),h=Math.sin(a),Math.abs(this.coslat0)<=w?(l=lt(this.e,i*this.con,this.con*s),f=2*this.a*this.k0*l/this.cons,t.x=this.x0+f*Math.sin(e-this.long0),t.y=this.y0-this.con*f*Math.cos(e-this.long0),t):(Math.abs(this.sinlat0)<w?(n=2*this.a*this.k0/(1+o*Math.cos(u)),t.y=n*h):(n=2*this.a*this.k0*this.ms1/(this.cosX0*(1+this.sinX0*h+this.cosX0*o*Math.cos(u))),t.y=n*(this.cosX0*h-this.sinX0*o*Math.cos(u))+this.y0),t.x=n*o*Math.sin(u)+this.x0,t))}function Fo(t){t.x-=this.x0,t.y-=this.y0;var e,i,s,r,n,a=Math.sqrt(t.x*t.x+t.y*t.y);if(this.sphere){var h=2*Math.atan(a/(2*this.a*this.k0));return e=this.long0,i=this.lat0,a<=w?(t.x=e,t.y=i,t):(i=Math.asin(Math.cos(h)*this.sinlat0+t.y*Math.sin(h)*this.coslat0/a),Math.abs(this.coslat0)<w?this.lat0>0?e=T(this.long0+Math.atan2(t.x,-1*t.y)):e=T(this.long0+Math.atan2(t.x,t.y)):e=T(this.long0+Math.atan2(t.x*Math.sin(h),a*this.coslat0*Math.cos(h)-t.y*this.sinlat0*Math.sin(h))),t.x=e,t.y=i,t)}else if(Math.abs(this.coslat0)<=w){if(a<=w)return i=this.lat0,e=this.long0,t.x=e,t.y=i,t;t.x*=this.con,t.y*=this.con,s=a*this.cons/(2*this.a*this.k0),i=this.con*se(this.e,s),e=this.con*T(this.con*this.long0+Math.atan2(t.x,-1*t.y))}else r=2*Math.atan(a*this.cosX0/(2*this.a*this.k0*this.ms1)),e=this.long0,a<=w?n=this.X0:(n=Math.asin(Math.cos(r)*this.sinX0+t.y*Math.sin(r)*this.cosX0/a),e=T(this.long0+Math.atan2(t.x*Math.sin(r),a*this.cosX0*Math.cos(r)-t.y*this.sinX0*Math.sin(r)))),i=-1*se(this.e,Math.tan(.5*(E+n)));return t.x=e,t.y=i,t}var Go=["stere","Stereographic_South_Pole","Polar Stereographic (variant B)"];const Ho={init:zo,forward:ko,inverse:Fo,names:Go,ssfn_:Do};function qo(){var t=this.lat0;this.lambda0=this.long0;var e=Math.sin(t),i=this.a,s=this.rf,r=1/s,n=2*r-Math.pow(r,2),a=this.e=Math.sqrt(n);this.R=this.k0*i*Math.sqrt(1-n)/(1-n*Math.pow(e,2)),this.alpha=Math.sqrt(1+n/(1-n)*Math.pow(Math.cos(t),4)),this.b0=Math.asin(e/this.alpha);var h=Math.log(Math.tan(Math.PI/4+this.b0/2)),o=Math.log(Math.tan(Math.PI/4+t/2)),l=Math.log((1+a*e)/(1-a*e));this.K=h-this.alpha*o+this.alpha*a/2*l}function Vo(t){var e=Math.log(Math.tan(Math.PI/4-t.y/2)),i=this.e/2*Math.log((1+this.e*Math.sin(t.y))/(1-this.e*Math.sin(t.y))),s=-this.alpha*(e+i)+this.K,r=2*(Math.atan(Math.exp(s))-Math.PI/4),n=this.alpha*(t.x-this.lambda0),a=Math.atan(Math.sin(n)/(Math.sin(this.b0)*Math.tan(r)+Math.cos(this.b0)*Math.cos(n))),h=Math.asin(Math.cos(this.b0)*Math.sin(r)-Math.sin(this.b0)*Math.cos(r)*Math.cos(n));return t.y=this.R/2*Math.log((1+Math.sin(h))/(1-Math.sin(h)))+this.y0,t.x=this.R*a+this.x0,t}function $o(t){for(var e=t.x-this.x0,i=t.y-this.y0,s=e/this.R,r=2*(Math.atan(Math.exp(i/this.R))-Math.PI/4),n=Math.asin(Math.cos(this.b0)*Math.sin(r)+Math.sin(this.b0)*Math.cos(r)*Math.cos(s)),a=Math.atan(Math.sin(s)/(Math.cos(this.b0)*Math.cos(s)-Math.sin(this.b0)*Math.tan(r))),h=this.lambda0+a/this.alpha,o=0,l=n,f=-1e3,u=0;Math.abs(l-f)>1e-7;){if(++u>20)return;o=1/this.alpha*(Math.log(Math.tan(Math.PI/4+n/2))-this.K)+this.e*Math.log(Math.tan(Math.PI/4+Math.asin(this.e*Math.sin(l))/2)),f=l,l=2*Math.atan(Math.exp(o))-Math.PI/2}return t.x=h,t.y=l,t}var Wo=["somerc"];const Xo={init:qo,forward:Vo,inverse:$o,names:Wo};var $t=1e-7;function Qo(t){var e=["Hotine_Oblique_Mercator","Hotine_Oblique_Mercator_Azimuth_Natural_Origin"],i=typeof t.PROJECTION=="object"?Object.keys(t.PROJECTION)[0]:t.PROJECTION;return"no_uoff"in t||"no_off"in t||e.indexOf(i)!==-1}function jo(){var t,e,i,s,r,n,a,h,o,l,f=0,u,c=0,v=0,d=0,g=0,p=0,y=0;this.no_off=Qo(this),this.no_rot="no_rot"in this;var x=!1;"alpha"in this&&(x=!0);var M=!1;if("rectified_grid_angle"in this&&(M=!0),x&&(y=this.alpha),M&&(f=this.rectified_grid_angle*j),x||M)c=this.longc;else if(v=this.long1,g=this.lat1,d=this.long2,p=this.lat2,Math.abs(g-p)<=$t||(t=Math.abs(g))<=$t||Math.abs(t-E)<=$t||Math.abs(Math.abs(this.lat0)-E)<=$t||Math.abs(Math.abs(p)-E)<=$t)throw new Error;var _=1-this.es;e=Math.sqrt(_),Math.abs(this.lat0)>w?(h=Math.sin(this.lat0),i=Math.cos(this.lat0),t=1-this.es*h*h,this.B=i*i,this.B=Math.sqrt(1+this.es*this.B*this.B/_),this.A=this.B*this.k0*e/t,s=this.B*e/(i*Math.sqrt(t)),r=s*s-1,r<=0?r=0:(r=Math.sqrt(r),this.lat0<0&&(r=-r)),this.E=r+=s,this.E*=Math.pow(lt(this.e,this.lat0,h),this.B)):(this.B=1/e,this.A=this.k0,this.E=s=r=1),x||M?(x?(u=Math.asin(Math.sin(y)/s),M||(f=y)):(u=f,y=Math.asin(s*Math.sin(u))),this.lam0=c-Math.asin(.5*(r-1/r)*Math.tan(u))/this.B):(n=Math.pow(lt(this.e,g,Math.sin(g)),this.B),a=Math.pow(lt(this.e,p,Math.sin(p)),this.B),r=this.E/n,o=(a-n)/(a+n),l=this.E*this.E,l=(l-a*n)/(l+a*n),t=v-d,t<-Math.pi?d-=te:t>Math.pi&&(d+=te),this.lam0=T(.5*(v+d)-Math.atan(l*Math.tan(.5*this.B*(v-d))/o)/this.B),u=Math.atan(2*Math.sin(this.B*T(v-this.lam0))/(r-1/r)),f=y=Math.asin(s*Math.sin(u))),this.singam=Math.sin(u),this.cosgam=Math.cos(u),this.sinrot=Math.sin(f),this.cosrot=Math.cos(f),this.rB=1/this.B,this.ArB=this.A*this.rB,this.BrA=1/this.ArB,this.A*this.B,this.no_off?this.u_0=0:(this.u_0=Math.abs(this.ArB*Math.atan(Math.sqrt(s*s-1)/Math.cos(y))),this.lat0<0&&(this.u_0=-this.u_0)),r=.5*u,this.v_pole_n=this.ArB*Math.log(Math.tan(B-r)),this.v_pole_s=this.ArB*Math.log(Math.tan(B+r))}function Ko(t){var e={},i,s,r,n,a,h,o,l;if(t.x=t.x-this.lam0,Math.abs(Math.abs(t.y)-E)>w){if(a=this.E/Math.pow(lt(this.e,t.y,Math.sin(t.y)),this.B),h=1/a,i=.5*(a-h),s=.5*(a+h),n=Math.sin(this.B*t.x),r=(i*this.singam-n*this.cosgam)/s,Math.abs(Math.abs(r)-1)<w)throw new Error;l=.5*this.ArB*Math.log((1-r)/(1+r)),h=Math.cos(this.B*t.x),Math.abs(h)<$t?o=this.A*t.x:o=this.ArB*Math.atan2(i*this.cosgam+n*this.singam,h)}else l=t.y>0?this.v_pole_n:this.v_pole_s,o=this.ArB*t.y;return this.no_rot?(e.x=o,e.y=l):(o-=this.u_0,e.x=l*this.cosrot+o*this.sinrot,e.y=o*this.cosrot-l*this.sinrot),e.x=this.a*e.x+this.x0,e.y=this.a*e.y+this.y0,e}function Yo(t){var e,i,s,r,n,a,h,o={};if(t.x=(t.x-this.x0)*(1/this.a),t.y=(t.y-this.y0)*(1/this.a),this.no_rot?(i=t.y,e=t.x):(i=t.x*this.cosrot-t.y*this.sinrot,e=t.y*this.cosrot+t.x*this.sinrot+this.u_0),s=Math.exp(-this.BrA*i),r=.5*(s-1/s),n=.5*(s+1/s),a=Math.sin(this.BrA*e),h=(a*this.cosgam+r*this.singam)/n,Math.abs(Math.abs(h)-1)<w)o.x=0,o.y=h<0?-E:E;else{if(o.y=this.E/Math.sqrt((1+h)/(1-h)),o.y=se(this.e,Math.pow(o.y,1/this.B)),o.y===1/0)throw new Error;o.x=-this.rB*Math.atan2(r*this.cosgam-a*this.singam,Math.cos(this.BrA*e))}return o.x+=this.lam0,o}var Zo=["Hotine_Oblique_Mercator","Hotine Oblique Mercator","Hotine_Oblique_Mercator_Azimuth_Natural_Origin","Hotine_Oblique_Mercator_Two_Point_Natural_Origin","Hotine_Oblique_Mercator_Azimuth_Center","Oblique_Mercator","omerc"];const Jo={init:jo,forward:Ko,inverse:Yo,names:Zo};function tl(){if(this.lat2||(this.lat2=this.lat1),this.k0||(this.k0=1),this.x0=this.x0||0,this.y0=this.y0||0,!(Math.abs(this.lat1+this.lat2)<w)){var t=this.b/this.a;this.e=Math.sqrt(1-t*t);var e=Math.sin(this.lat1),i=Math.cos(this.lat1),s=gt(this.e,e,i),r=lt(this.e,this.lat1,e),n=Math.sin(this.lat2),a=Math.cos(this.lat2),h=gt(this.e,n,a),o=lt(this.e,this.lat2,n),l=lt(this.e,this.lat0,Math.sin(this.lat0));Math.abs(this.lat1-this.lat2)>w?this.ns=Math.log(s/h)/Math.log(r/o):this.ns=e,isNaN(this.ns)&&(this.ns=e),this.f0=s/(this.ns*Math.pow(r,this.ns)),this.rh=this.a*this.f0*Math.pow(l,this.ns),this.title||(this.title="Lambert Conformal Conic")}}function el(t){var e=t.x,i=t.y;Math.abs(2*Math.abs(i)-Math.PI)<=w&&(i=ie(i)*(E-2*w));var s=Math.abs(Math.abs(i)-E),r,n;if(s>w)r=lt(this.e,i,Math.sin(i)),n=this.a*this.f0*Math.pow(r,this.ns);else{if(s=i*this.ns,s<=0)return null;n=0}var a=this.ns*T(e-this.long0);return t.x=this.k0*(n*Math.sin(a))+this.x0,t.y=this.k0*(this.rh-n*Math.cos(a))+this.y0,t}function il(t){var e,i,s,r,n,a=(t.x-this.x0)/this.k0,h=this.rh-(t.y-this.y0)/this.k0;this.ns>0?(e=Math.sqrt(a*a+h*h),i=1):(e=-Math.sqrt(a*a+h*h),i=-1);var o=0;if(e!==0&&(o=Math.atan2(i*a,i*h)),e!==0||this.ns>0){if(i=1/this.ns,s=Math.pow(e/(this.a*this.f0),i),r=se(this.e,s),r===-9999)return null}else r=-E;return n=T(o/this.ns+this.long0),t.x=n,t.y=r,t}var sl=["Lambert Tangential Conformal Conic Projection","Lambert_Conformal_Conic","Lambert_Conformal_Conic_1SP","Lambert_Conformal_Conic_2SP","lcc","Lambert Conic Conformal (1SP)","Lambert Conic Conformal (2SP)"];const rl={init:tl,forward:el,inverse:il,names:sl};function nl(){this.a=6377397155e-3,this.es=.006674372230614,this.e=Math.sqrt(this.es),this.lat0||(this.lat0=.863937979737193),this.long0||(this.long0=.7417649320975901-.308341501185665),this.k0||(this.k0=.9999),this.s45=.785398163397448,this.s90=2*this.s45,this.fi0=this.lat0,this.e2=this.es,this.e=Math.sqrt(this.e2),this.alfa=Math.sqrt(1+this.e2*Math.pow(Math.cos(this.fi0),4)/(1-this.e2)),this.uq=1.04216856380474,this.u0=Math.asin(Math.sin(this.fi0)/this.alfa),this.g=Math.pow((1+this.e*Math.sin(this.fi0))/(1-this.e*Math.sin(this.fi0)),this.alfa*this.e/2),this.k=Math.tan(this.u0/2+this.s45)/Math.pow(Math.tan(this.fi0/2+this.s45),this.alfa)*this.g,this.k1=this.k0,this.n0=this.a*Math.sqrt(1-this.e2)/(1-this.e2*Math.pow(Math.sin(this.fi0),2)),this.s0=1.37008346281555,this.n=Math.sin(this.s0),this.ro0=this.k1*this.n0/Math.tan(this.s0),this.ad=this.s90-this.uq}function al(t){var e,i,s,r,n,a,h,o=t.x,l=t.y,f=T(o-this.long0);return e=Math.pow((1+this.e*Math.sin(l))/(1-this.e*Math.sin(l)),this.alfa*this.e/2),i=2*(Math.atan(this.k*Math.pow(Math.tan(l/2+this.s45),this.alfa)/e)-this.s45),s=-f*this.alfa,r=Math.asin(Math.cos(this.ad)*Math.sin(i)+Math.sin(this.ad)*Math.cos(i)*Math.cos(s)),n=Math.asin(Math.cos(i)*Math.sin(s)/Math.cos(r)),a=this.n*n,h=this.ro0*Math.pow(Math.tan(this.s0/2+this.s45),this.n)/Math.pow(Math.tan(r/2+this.s45),this.n),t.y=h*Math.cos(a)/1,t.x=h*Math.sin(a)/1,this.czech||(t.y*=-1,t.x*=-1),t}function hl(t){var e,i,s,r,n,a,h,o,l=t.x;t.x=t.y,t.y=l,this.czech||(t.y*=-1,t.x*=-1),a=Math.sqrt(t.x*t.x+t.y*t.y),n=Math.atan2(t.y,t.x),r=n/Math.sin(this.s0),s=2*(Math.atan(Math.pow(this.ro0/a,1/this.n)*Math.tan(this.s0/2+this.s45))-this.s45),e=Math.asin(Math.cos(this.ad)*Math.sin(s)-Math.sin(this.ad)*Math.cos(s)*Math.cos(r)),i=Math.asin(Math.cos(s)*Math.sin(r)/Math.cos(e)),t.x=this.long0-i/this.alfa,h=e,o=0;var f=0;do t.y=2*(Math.atan(Math.pow(this.k,-1/this.alfa)*Math.pow(Math.tan(e/2+this.s45),1/this.alfa)*Math.pow((1+this.e*Math.sin(h))/(1-this.e*Math.sin(h)),this.e/2))-this.s45),Math.abs(h-t.y)<1e-10&&(o=1),h=t.y,f+=1;while(o===0&&f<15);return f>=15?null:t}var ol=["Krovak","krovak"];const ll={init:nl,forward:al,inverse:hl,names:ol};function Z(t,e,i,s,r){return t*r-e*Math.sin(2*r)+i*Math.sin(4*r)-s*Math.sin(6*r)}function ae(t){return 1-.25*t*(1+t/16*(3+1.25*t))}function he(t){return .375*t*(1+.25*t*(1+.46875*t))}function oe(t){return .05859375*t*t*(1+.75*t)}function le(t){return t*t*t*(35/3072)}function Wt(t,e,i){var s=e*i;return t/Math.sqrt(1-s*s)}function Xt(t){return Math.abs(t)<E?t:t-ie(t)*Math.PI}function Ie(t,e,i,s,r){var n,a;n=t/e;for(var h=0;h<15;h++)if(a=(t-(e*n-i*Math.sin(2*n)+s*Math.sin(4*n)-r*Math.sin(6*n)))/(e-2*i*Math.cos(2*n)+4*s*Math.cos(4*n)-6*r*Math.cos(6*n)),n+=a,Math.abs(a)<=1e-10)return n;return NaN}function ul(){this.sphere||(this.e0=ae(this.es),this.e1=he(this.es),this.e2=oe(this.es),this.e3=le(this.es),this.ml0=this.a*Z(this.e0,this.e1,this.e2,this.e3,this.lat0))}function fl(t){var e,i,s=t.x,r=t.y;if(s=T(s-this.long0),this.sphere)e=this.a*Math.asin(Math.cos(r)*Math.sin(s)),i=this.a*(Math.atan2(Math.tan(r),Math.cos(s))-this.lat0);else{var n=Math.sin(r),a=Math.cos(r),h=Wt(this.a,this.e,n),o=Math.tan(r)*Math.tan(r),l=s*Math.cos(r),f=l*l,u=this.es*a*a/(1-this.es),c=this.a*Z(this.e0,this.e1,this.e2,this.e3,r);e=h*l*(1-f*o*(1/6-(8-o+8*u)*f/120)),i=c-this.ml0+h*n/a*f*(.5+(5-o+6*u)*f/24)}return t.x=e+this.x0,t.y=i+this.y0,t}function cl(t){t.x-=this.x0,t.y-=this.y0;var e=t.x/this.a,i=t.y/this.a,s,r;if(this.sphere){var n=i+this.lat0;s=Math.asin(Math.sin(n)*Math.cos(e)),r=Math.atan2(Math.tan(e),Math.cos(n))}else{var a=this.ml0/this.a+i,h=Ie(a,this.e0,this.e1,this.e2,this.e3);if(Math.abs(Math.abs(h)-E)<=w)return t.x=this.long0,t.y=E,i<0&&(t.y*=-1),t;var o=Wt(this.a,this.e,Math.sin(h)),l=o*o*o/this.a/this.a*(1-this.es),f=Math.pow(Math.tan(h),2),u=e*this.a/o,c=u*u;s=h-o*Math.tan(h)/l*u*u*(.5-(1+3*f)*u*u/24),r=u*(1-c*(f/3+(1+3*f)*f*c/15))/Math.cos(h)}return t.x=T(r+this.long0),t.y=Xt(s),t}var vl=["Cassini","Cassini_Soldner","cass"];const dl={init:ul,forward:fl,inverse:cl,names:vl};function Tt(t,e){var i;return t>1e-7?(i=t*e,(1-t*t)*(e/(1-i*i)-.5/t*Math.log((1-i)/(1+i)))):2*e}var ml=1,gl=2,pl=3,yl=4;function Ml(){var t=Math.abs(this.lat0);if(Math.abs(t-E)<w?this.mode=this.lat0<0?this.S_POLE:this.N_POLE:Math.abs(t)<w?this.mode=this.EQUIT:this.mode=this.OBLIQ,this.es>0){var e;switch(this.qp=Tt(this.e,1),this.mmf=.5/(1-this.es),this.apa=Rl(this.es),this.mode){case this.N_POLE:this.dd=1;break;case this.S_POLE:this.dd=1;break;case this.EQUIT:this.rq=Math.sqrt(.5*this.qp),this.dd=1/this.rq,this.xmf=1,this.ymf=.5*this.qp;break;case this.OBLIQ:this.rq=Math.sqrt(.5*this.qp),e=Math.sin(this.lat0),this.sinb1=Tt(this.e,e)/this.qp,this.cosb1=Math.sqrt(1-this.sinb1*this.sinb1),this.dd=Math.cos(this.lat0)/(Math.sqrt(1-this.es*e*e)*this.rq*this.cosb1),this.ymf=(this.xmf=this.rq)/this.dd,this.xmf*=this.dd;break}}else this.mode===this.OBLIQ&&(this.sinph0=Math.sin(this.lat0),this.cosph0=Math.cos(this.lat0))}function xl(t){var e,i,s,r,n,a,h,o,l,f,u=t.x,c=t.y;if(u=T(u-this.long0),this.sphere){if(n=Math.sin(c),f=Math.cos(c),s=Math.cos(u),this.mode===this.OBLIQ||this.mode===this.EQUIT){if(i=this.mode===this.EQUIT?1+f*s:1+this.sinph0*n+this.cosph0*f*s,i<=w)return null;i=Math.sqrt(2/i),e=i*f*Math.sin(u),i*=this.mode===this.EQUIT?n:this.cosph0*n-this.sinph0*f*s}else if(this.mode===this.N_POLE||this.mode===this.S_POLE){if(this.mode===this.N_POLE&&(s=-s),Math.abs(c+this.lat0)<w)return null;i=B-c*.5,i=2*(this.mode===this.S_POLE?Math.cos(i):Math.sin(i)),e=i*Math.sin(u),i*=s}}else{switch(h=0,o=0,l=0,s=Math.cos(u),r=Math.sin(u),n=Math.sin(c),a=Tt(this.e,n),(this.mode===this.OBLIQ||this.mode===this.EQUIT)&&(h=a/this.qp,o=Math.sqrt(1-h*h)),this.mode){case this.OBLIQ:l=1+this.sinb1*h+this.cosb1*o*s;break;case this.EQUIT:l=1+o*s;break;case this.N_POLE:l=E+c,a=this.qp-a;break;case this.S_POLE:l=c-E,a=this.qp+a;break}if(Math.abs(l)<w)return null;switch(this.mode){case this.OBLIQ:case this.EQUIT:l=Math.sqrt(2/l),this.mode===this.OBLIQ?i=this.ymf*l*(this.cosb1*h-this.sinb1*o*s):i=(l=Math.sqrt(2/(1+o*s)))*h*this.ymf,e=this.xmf*l*o*r;break;case this.N_POLE:case this.S_POLE:a>=0?(e=(l=Math.sqrt(a))*r,i=s*(this.mode===this.S_POLE?l:-l)):e=i=0;break}}return t.x=this.a*e+this.x0,t.y=this.a*i+this.y0,t}function _l(t){t.x-=this.x0,t.y-=this.y0;var e=t.x/this.a,i=t.y/this.a,s,r,n,a,h,o,l;if(this.sphere){var f=0,u,c=0;if(u=Math.sqrt(e*e+i*i),r=u*.5,r>1)return null;switch(r=2*Math.asin(r),(this.mode===this.OBLIQ||this.mode===this.EQUIT)&&(c=Math.sin(r),f=Math.cos(r)),this.mode){case this.EQUIT:r=Math.abs(u)<=w?0:Math.asin(i*c/u),e*=c,i=f*u;break;case this.OBLIQ:r=Math.abs(u)<=w?this.lat0:Math.asin(f*this.sinph0+i*c*this.cosph0/u),e*=c*this.cosph0,i=(f-Math.sin(r)*this.sinph0)*u;break;case this.N_POLE:i=-i,r=E-r;break;case this.S_POLE:r-=E;break}s=i===0&&(this.mode===this.EQUIT||this.mode===this.OBLIQ)?0:Math.atan2(e,i)}else{if(l=0,this.mode===this.OBLIQ||this.mode===this.EQUIT){if(e/=this.dd,i*=this.dd,o=Math.sqrt(e*e+i*i),o<w)return t.x=this.long0,t.y=this.lat0,t;a=2*Math.asin(.5*o/this.rq),n=Math.cos(a),e*=a=Math.sin(a),this.mode===this.OBLIQ?(l=n*this.sinb1+i*a*this.cosb1/o,h=this.qp*l,i=o*this.cosb1*n-i*this.sinb1*a):(l=i*a/o,h=this.qp*l,i=o*n)}else if(this.mode===this.N_POLE||this.mode===this.S_POLE){if(this.mode===this.N_POLE&&(i=-i),h=e*e+i*i,!h)return t.x=this.long0,t.y=this.lat0,t;l=1-h/this.qp,this.mode===this.S_POLE&&(l=-l)}s=Math.atan2(e,i),r=Ll(Math.asin(l),this.apa)}return t.x=T(this.long0+s),t.y=r,t}var El=.3333333333333333,Sl=.17222222222222222,bl=.10257936507936508,wl=.06388888888888888,Tl=.0664021164021164,Al=.016415012942191543;function Rl(t){var e,i=[];return i[0]=t*El,e=t*t,i[0]+=e*Sl,i[1]=e*wl,e*=t,i[0]+=e*bl,i[1]+=e*Tl,i[2]=e*Al,i}function Ll(t,e){var i=t+t;return t+e[0]*Math.sin(i)+e[1]*Math.sin(i+i)+e[2]*Math.sin(i+i+i)}var Il=["Lambert Azimuthal Equal Area","Lambert_Azimuthal_Equal_Area","laea"];const Nl={init:Ml,forward:xl,inverse:_l,names:Il,S_POLE:ml,N_POLE:gl,EQUIT:pl,OBLIQ:yl};function At(t){return Math.abs(t)>1&&(t=t>1?1:-1),Math.asin(t)}function Pl(){Math.abs(this.lat1+this.lat2)<w||(this.temp=this.b/this.a,this.es=1-Math.pow(this.temp,2),this.e3=Math.sqrt(this.es),this.sin_po=Math.sin(this.lat1),this.cos_po=Math.cos(this.lat1),this.t1=this.sin_po,this.con=this.sin_po,this.ms1=gt(this.e3,this.sin_po,this.cos_po),this.qs1=Tt(this.e3,this.sin_po),this.sin_po=Math.sin(this.lat2),this.cos_po=Math.cos(this.lat2),this.t2=this.sin_po,this.ms2=gt(this.e3,this.sin_po,this.cos_po),this.qs2=Tt(this.e3,this.sin_po),this.sin_po=Math.sin(this.lat0),this.cos_po=Math.cos(this.lat0),this.t3=this.sin_po,this.qs0=Tt(this.e3,this.sin_po),Math.abs(this.lat1-this.lat2)>w?this.ns0=(this.ms1*this.ms1-this.ms2*this.ms2)/(this.qs2-this.qs1):this.ns0=this.con,this.c=this.ms1*this.ms1+this.ns0*this.qs1,this.rh=this.a*Math.sqrt(this.c-this.ns0*this.qs0)/this.ns0)}function Cl(t){var e=t.x,i=t.y;this.sin_phi=Math.sin(i),this.cos_phi=Math.cos(i);var s=Tt(this.e3,this.sin_phi),r=this.a*Math.sqrt(this.c-this.ns0*s)/this.ns0,n=this.ns0*T(e-this.long0),a=r*Math.sin(n)+this.x0,h=this.rh-r*Math.cos(n)+this.y0;return t.x=a,t.y=h,t}function Ol(t){var e,i,s,r,n,a;return t.x-=this.x0,t.y=this.rh-t.y+this.y0,this.ns0>=0?(e=Math.sqrt(t.x*t.x+t.y*t.y),s=1):(e=-Math.sqrt(t.x*t.x+t.y*t.y),s=-1),r=0,e!==0&&(r=Math.atan2(s*t.x,s*t.y)),s=e*this.ns0/this.a,this.sphere?a=Math.asin((this.c-s*s)/(2*this.ns0)):(i=(this.c-s*s)/this.ns0,a=this.phi1z(this.e3,i)),n=T(r/this.ns0+this.long0),t.x=n,t.y=a,t}function Ul(t,e){var i,s,r,n,a,h=At(.5*e);if(t<w)return h;for(var o=t*t,l=1;l<=25;l++)if(i=Math.sin(h),s=Math.cos(h),r=t*i,n=1-r*r,a=.5*n*n/s*(e/(1-o)-i/n+.5/t*Math.log((1-r)/(1+r))),h=h+a,Math.abs(a)<=1e-7)return h;return null}var Bl=["Albers_Conic_Equal_Area","Albers","aea"];const Dl={init:Pl,forward:Cl,inverse:Ol,names:Bl,phi1z:Ul};function zl(){this.sin_p14=Math.sin(this.lat0),this.cos_p14=Math.cos(this.lat0),this.infinity_dist=1e3*this.a,this.rc=1}function kl(t){var e,i,s,r,n,a,h,o,l=t.x,f=t.y;return s=T(l-this.long0),e=Math.sin(f),i=Math.cos(f),r=Math.cos(s),a=this.sin_p14*e+this.cos_p14*i*r,n=1,a>0||Math.abs(a)<=w?(h=this.x0+this.a*n*i*Math.sin(s)/a,o=this.y0+this.a*n*(this.cos_p14*e-this.sin_p14*i*r)/a):(h=this.x0+this.infinity_dist*i*Math.sin(s),o=this.y0+this.infinity_dist*(this.cos_p14*e-this.sin_p14*i*r)),t.x=h,t.y=o,t}function Fl(t){var e,i,s,r,n,a;return t.x=(t.x-this.x0)/this.a,t.y=(t.y-this.y0)/this.a,t.x/=this.k0,t.y/=this.k0,(e=Math.sqrt(t.x*t.x+t.y*t.y))?(r=Math.atan2(e,this.rc),i=Math.sin(r),s=Math.cos(r),a=At(s*this.sin_p14+t.y*i*this.cos_p14/e),n=Math.atan2(t.x*i,e*this.cos_p14*s-t.y*this.sin_p14*i),n=T(this.long0+n)):(a=this.phic0,n=0),t.x=n,t.y=a,t}var Gl=["gnom"];const Hl={init:zl,forward:kl,inverse:Fl,names:Gl};function ql(t,e){var i=1-(1-t*t)/(2*t)*Math.log((1-t)/(1+t));if(Math.abs(Math.abs(e)-i)<1e-6)return e<0?-1*E:E;for(var s=Math.asin(.5*e),r,n,a,h,o=0;o<30;o++)if(n=Math.sin(s),a=Math.cos(s),h=t*n,r=Math.pow(1-h*h,2)/(2*a)*(e/(1-t*t)-n/(1-h*h)+.5/t*Math.log((1-h)/(1+h))),s+=r,Math.abs(r)<=1e-10)return s;return NaN}function Vl(){this.sphere||(this.k0=gt(this.e,Math.sin(this.lat_ts),Math.cos(this.lat_ts)))}function $l(t){var e=t.x,i=t.y,s,r,n=T(e-this.long0);if(this.sphere)s=this.x0+this.a*n*Math.cos(this.lat_ts),r=this.y0+this.a*Math.sin(i)/Math.cos(this.lat_ts);else{var a=Tt(this.e,Math.sin(i));s=this.x0+this.a*this.k0*n,r=this.y0+this.a*a*.5/this.k0}return t.x=s,t.y=r,t}function Wl(t){t.x-=this.x0,t.y-=this.y0;var e,i;return this.sphere?(e=T(this.long0+t.x/this.a/Math.cos(this.lat_ts)),i=Math.asin(t.y/this.a*Math.cos(this.lat_ts))):(i=ql(this.e,2*t.y*this.k0/this.a),e=T(this.long0+t.x/(this.a*this.k0))),t.x=e,t.y=i,t}var Xl=["cea"];const Ql={init:Vl,forward:$l,inverse:Wl,names:Xl};function jl(){this.x0=this.x0||0,this.y0=this.y0||0,this.lat0=this.lat0||0,this.long0=this.long0||0,this.lat_ts=this.lat_ts||0,this.title=this.title||"Equidistant Cylindrical (Plate Carre)",this.rc=Math.cos(this.lat_ts)}function Kl(t){var e=t.x,i=t.y,s=T(e-this.long0),r=Xt(i-this.lat0);return t.x=this.x0+this.a*s*this.rc,t.y=this.y0+this.a*r,t}function Yl(t){var e=t.x,i=t.y;return t.x=T(this.long0+(e-this.x0)/(this.a*this.rc)),t.y=Xt(this.lat0+(i-this.y0)/this.a),t}var Zl=["Equirectangular","Equidistant_Cylindrical","eqc"];const Jl={init:jl,forward:Kl,inverse:Yl,names:Zl};var ks=20;function tu(){this.temp=this.b/this.a,this.es=1-Math.pow(this.temp,2),this.e=Math.sqrt(this.es),this.e0=ae(this.es),this.e1=he(this.es),this.e2=oe(this.es),this.e3=le(this.es),this.ml0=this.a*Z(this.e0,this.e1,this.e2,this.e3,this.lat0)}function eu(t){var e=t.x,i=t.y,s,r,n,a=T(e-this.long0);if(n=a*Math.sin(i),this.sphere)Math.abs(i)<=w?(s=this.a*a,r=-1*this.a*this.lat0):(s=this.a*Math.sin(n)/Math.tan(i),r=this.a*(Xt(i-this.lat0)+(1-Math.cos(n))/Math.tan(i)));else if(Math.abs(i)<=w)s=this.a*a,r=-1*this.ml0;else{var h=Wt(this.a,this.e,Math.sin(i))/Math.tan(i);s=h*Math.sin(n),r=this.a*Z(this.e0,this.e1,this.e2,this.e3,i)-this.ml0+h*(1-Math.cos(n))}return t.x=s+this.x0,t.y=r+this.y0,t}function iu(t){var e,i,s,r,n,a,h,o,l;if(s=t.x-this.x0,r=t.y-this.y0,this.sphere)if(Math.abs(r+this.a*this.lat0)<=w)e=T(s/this.a+this.long0),i=0;else{a=this.lat0+r/this.a,h=s*s/this.a/this.a+a*a,o=a;var f;for(n=ks;n;--n)if(f=Math.tan(o),l=-1*(a*(o*f+1)-o-.5*(o*o+h)*f)/((o-a)/f-1),o+=l,Math.abs(l)<=w){i=o;break}e=T(this.long0+Math.asin(s*Math.tan(o)/this.a)/Math.sin(i))}else if(Math.abs(r+this.ml0)<=w)i=0,e=T(this.long0+s/this.a);else{a=(this.ml0+r)/this.a,h=s*s/this.a/this.a+a*a,o=a;var u,c,v,d,g;for(n=ks;n;--n)if(g=this.e*Math.sin(o),u=Math.sqrt(1-g*g)*Math.tan(o),c=this.a*Z(this.e0,this.e1,this.e2,this.e3,o),v=this.e0-2*this.e1*Math.cos(2*o)+4*this.e2*Math.cos(4*o)-6*this.e3*Math.cos(6*o),d=c/this.a,l=(a*(u*d+1)-d-.5*u*(d*d+h))/(this.es*Math.sin(2*o)*(d*d+h-2*a*d)/(4*u)+(a-d)*(u*v-2/Math.sin(2*o))-v),o-=l,Math.abs(l)<=w){i=o;break}u=Math.sqrt(1-this.es*Math.pow(Math.sin(i),2))*Math.tan(i),e=T(this.long0+Math.asin(s*u/this.a)/Math.sin(i))}return t.x=e,t.y=i,t}var su=["Polyconic","poly"];const ru={init:tu,forward:eu,inverse:iu,names:su};function nu(){this.A=[],this.A[1]=.6399175073,this.A[2]=-.1358797613,this.A[3]=.063294409,this.A[4]=-.02526853,this.A[5]=.0117879,this.A[6]=-.0055161,this.A[7]=.0026906,this.A[8]=-.001333,this.A[9]=67e-5,this.A[10]=-34e-5,this.B_re=[],this.B_im=[],this.B_re[1]=.7557853228,this.B_im[1]=0,this.B_re[2]=.249204646,this.B_im[2]=.003371507,this.B_re[3]=-.001541739,this.B_im[3]=.04105856,this.B_re[4]=-.10162907,this.B_im[4]=.01727609,this.B_re[5]=-.26623489,this.B_im[5]=-.36249218,this.B_re[6]=-.6870983,this.B_im[6]=-1.1651967,this.C_re=[],this.C_im=[],this.C_re[1]=1.3231270439,this.C_im[1]=0,this.C_re[2]=-.577245789,this.C_im[2]=-.007809598,this.C_re[3]=.508307513,this.C_im[3]=-.112208952,this.C_re[4]=-.15094762,this.C_im[4]=.18200602,this.C_re[5]=1.01418179,this.C_im[5]=1.64497696,this.C_re[6]=1.9660549,this.C_im[6]=2.5127645,this.D=[],this.D[1]=1.5627014243,this.D[2]=.5185406398,this.D[3]=-.03333098,this.D[4]=-.1052906,this.D[5]=-.0368594,this.D[6]=.007317,this.D[7]=.0122,this.D[8]=.00394,this.D[9]=-.0013}function au(t){var e,i=t.x,s=t.y,r=s-this.lat0,n=i-this.long0,a=r/Jt*1e-5,h=n,o=1,l=0;for(e=1;e<=10;e++)o=o*a,l=l+this.A[e]*o;var f=l,u=h,c=1,v=0,d,g,p=0,y=0;for(e=1;e<=6;e++)d=c*f-v*u,g=v*f+c*u,c=d,v=g,p=p+this.B_re[e]*c-this.B_im[e]*v,y=y+this.B_im[e]*c+this.B_re[e]*v;return t.x=y*this.a+this.x0,t.y=p*this.a+this.y0,t}function hu(t){var e,i=t.x,s=t.y,r=i-this.x0,n=s-this.y0,a=n/this.a,h=r/this.a,o=1,l=0,f,u,c=0,v=0;for(e=1;e<=6;e++)f=o*a-l*h,u=l*a+o*h,o=f,l=u,c=c+this.C_re[e]*o-this.C_im[e]*l,v=v+this.C_im[e]*o+this.C_re[e]*l;for(var d=0;d<this.iterations;d++){var g=c,p=v,y,x,M=a,_=h;for(e=2;e<=6;e++)y=g*c-p*v,x=p*c+g*v,g=y,p=x,M=M+(e-1)*(this.B_re[e]*g-this.B_im[e]*p),_=_+(e-1)*(this.B_im[e]*g+this.B_re[e]*p);g=1,p=0;var S=this.B_re[1],b=this.B_im[1];for(e=2;e<=6;e++)y=g*c-p*v,x=p*c+g*v,g=y,p=x,S=S+e*(this.B_re[e]*g-this.B_im[e]*p),b=b+e*(this.B_im[e]*g+this.B_re[e]*p);var L=S*S+b*b;c=(M*S+_*b)/L,v=(_*S-M*b)/L}var A=c,I=v,C=1,O=0;for(e=1;e<=9;e++)C=C*A,O=O+this.D[e]*C;var F=this.lat0+O*Jt*1e5,V=this.long0+I;return t.x=V,t.y=F,t}var ou=["New_Zealand_Map_Grid","nzmg"];const lu={init:nu,forward:au,inverse:hu,names:ou};function uu(){}function fu(t){var e=t.x,i=t.y,s=T(e-this.long0),r=this.x0+this.a*s,n=this.y0+this.a*Math.log(Math.tan(Math.PI/4+i/2.5))*1.25;return t.x=r,t.y=n,t}function cu(t){t.x-=this.x0,t.y-=this.y0;var e=T(this.long0+t.x/this.a),i=2.5*(Math.atan(Math.exp(.8*t.y/this.a))-Math.PI/4);return t.x=e,t.y=i,t}var vu=["Miller_Cylindrical","mill"];const du={init:uu,forward:fu,inverse:cu,names:vu};var mu=20;function gu(){this.sphere?(this.n=1,this.m=0,this.es=0,this.C_y=Math.sqrt((this.m+1)/this.n),this.C_x=this.C_y/(this.m+1)):this.en=Us(this.es)}function pu(t){var e,i,s=t.x,r=t.y;if(s=T(s-this.long0),this.sphere){if(!this.m)r=this.n!==1?Math.asin(this.n*Math.sin(r)):r;else for(var n=this.n*Math.sin(r),a=mu;a;--a){var h=(this.m*r+Math.sin(r)-n)/(this.m+Math.cos(r));if(r-=h,Math.abs(h)<w)break}e=this.a*this.C_x*s*(this.m+Math.cos(r)),i=this.a*this.C_y*r}else{var o=Math.sin(r),l=Math.cos(r);i=this.a*Ae(r,o,l,this.en),e=this.a*s*l/Math.sqrt(1-this.es*o*o)}return t.x=e,t.y=i,t}function yu(t){var e,i,s,r;return t.x-=this.x0,s=t.x/this.a,t.y-=this.y0,e=t.y/this.a,this.sphere?(e/=this.C_y,s=s/(this.C_x*(this.m+Math.cos(e))),this.m?e=At((this.m*e+Math.sin(e))/this.n):this.n!==1&&(e=At(Math.sin(e)/this.n)),s=T(s+this.long0),e=Xt(e)):(e=Bs(t.y/this.a,this.es,this.en),r=Math.abs(e),r<E?(r=Math.sin(e),i=this.long0+t.x*Math.sqrt(1-this.es*r*r)/(this.a*Math.cos(e)),s=T(i)):r-w<E&&(s=this.long0)),t.x=s,t.y=e,t}var Mu=["Sinusoidal","sinu"];const xu={init:gu,forward:pu,inverse:yu,names:Mu};function _u(){}function Eu(t){for(var e=t.x,i=t.y,s=T(e-this.long0),r=i,n=Math.PI*Math.sin(i);;){var a=-(r+Math.sin(r)-n)/(1+Math.cos(r));if(r+=a,Math.abs(a)<w)break}r/=2,Math.PI/2-Math.abs(i)<w&&(s=0);var h=.900316316158*this.a*s*Math.cos(r)+this.x0,o=1.4142135623731*this.a*Math.sin(r)+this.y0;return t.x=h,t.y=o,t}function Su(t){var e,i;t.x-=this.x0,t.y-=this.y0,i=t.y/(1.4142135623731*this.a),Math.abs(i)>.999999999999&&(i=.999999999999),e=Math.asin(i);var s=T(this.long0+t.x/(.900316316158*this.a*Math.cos(e)));s<-Math.PI&&(s=-Math.PI),s>Math.PI&&(s=Math.PI),i=(2*e+Math.sin(2*e))/Math.PI,Math.abs(i)>1&&(i=1);var r=Math.asin(i);return t.x=s,t.y=r,t}var bu=["Mollweide","moll"];const wu={init:_u,forward:Eu,inverse:Su,names:bu};function Tu(){Math.abs(this.lat1+this.lat2)<w||(this.lat2=this.lat2||this.lat1,this.temp=this.b/this.a,this.es=1-Math.pow(this.temp,2),this.e=Math.sqrt(this.es),this.e0=ae(this.es),this.e1=he(this.es),this.e2=oe(this.es),this.e3=le(this.es),this.sinphi=Math.sin(this.lat1),this.cosphi=Math.cos(this.lat1),this.ms1=gt(this.e,this.sinphi,this.cosphi),this.ml1=Z(this.e0,this.e1,this.e2,this.e3,this.lat1),Math.abs(this.lat1-this.lat2)<w?this.ns=this.sinphi:(this.sinphi=Math.sin(this.lat2),this.cosphi=Math.cos(this.lat2),this.ms2=gt(this.e,this.sinphi,this.cosphi),this.ml2=Z(this.e0,this.e1,this.e2,this.e3,this.lat2),this.ns=(this.ms1-this.ms2)/(this.ml2-this.ml1)),this.g=this.ml1+this.ms1/this.ns,this.ml0=Z(this.e0,this.e1,this.e2,this.e3,this.lat0),this.rh=this.a*(this.g-this.ml0))}function Au(t){var e=t.x,i=t.y,s;if(this.sphere)s=this.a*(this.g-i);else{var r=Z(this.e0,this.e1,this.e2,this.e3,i);s=this.a*(this.g-r)}var n=this.ns*T(e-this.long0),a=this.x0+s*Math.sin(n),h=this.y0+this.rh-s*Math.cos(n);return t.x=a,t.y=h,t}function Ru(t){t.x-=this.x0,t.y=this.rh-t.y+this.y0;var e,i,s,r;this.ns>=0?(i=Math.sqrt(t.x*t.x+t.y*t.y),e=1):(i=-Math.sqrt(t.x*t.x+t.y*t.y),e=-1);var n=0;if(i!==0&&(n=Math.atan2(e*t.x,e*t.y)),this.sphere)return r=T(this.long0+n/this.ns),s=Xt(this.g-i/this.a),t.x=r,t.y=s,t;var a=this.g-i/this.a;return s=Ie(a,this.e0,this.e1,this.e2,this.e3),r=T(this.long0+n/this.ns),t.x=r,t.y=s,t}var Lu=["Equidistant_Conic","eqdc"];const Iu={init:Tu,forward:Au,inverse:Ru,names:Lu};function Nu(){this.R=this.a}function Pu(t){var e=t.x,i=t.y,s=T(e-this.long0),r,n;Math.abs(i)<=w&&(r=this.x0+this.R*s,n=this.y0);var a=At(2*Math.abs(i/Math.PI));(Math.abs(s)<=w||Math.abs(Math.abs(i)-E)<=w)&&(r=this.x0,i>=0?n=this.y0+Math.PI*this.R*Math.tan(.5*a):n=this.y0+Math.PI*this.R*-Math.tan(.5*a));var h=.5*Math.abs(Math.PI/s-s/Math.PI),o=h*h,l=Math.sin(a),f=Math.cos(a),u=f/(l+f-1),c=u*u,v=u*(2/l-1),d=v*v,g=Math.PI*this.R*(h*(u-d)+Math.sqrt(o*(u-d)*(u-d)-(d+o)*(c-d)))/(d+o);s<0&&(g=-g),r=this.x0+g;var p=o+u;return g=Math.PI*this.R*(v*p-h*Math.sqrt((d+o)*(o+1)-p*p))/(d+o),i>=0?n=this.y0+g:n=this.y0-g,t.x=r,t.y=n,t}function Cu(t){var e,i,s,r,n,a,h,o,l,f,u,c,v;return t.x-=this.x0,t.y-=this.y0,u=Math.PI*this.R,s=t.x/u,r=t.y/u,n=s*s+r*r,a=-Math.abs(r)*(1+n),h=a-2*r*r+s*s,o=-2*a+1+2*r*r+n*n,v=r*r/o+(2*h*h*h/o/o/o-9*a*h/o/o)/27,l=(a-h*h/3/o)/o,f=2*Math.sqrt(-l/3),u=3*v/l/f,Math.abs(u)>1&&(u>=0?u=1:u=-1),c=Math.acos(u)/3,t.y>=0?i=(-f*Math.cos(c+Math.PI/3)-h/3/o)*Math.PI:i=-(-f*Math.cos(c+Math.PI/3)-h/3/o)*Math.PI,Math.abs(s)<w?e=this.long0:e=T(this.long0+Math.PI*(n-1+Math.sqrt(1+2*(s*s-r*r)+n*n))/2/s),t.x=e,t.y=i,t}var Ou=["Van_der_Grinten_I","VanDerGrinten","vandg"];const Uu={init:Nu,forward:Pu,inverse:Cu,names:Ou};function Bu(){this.sin_p12=Math.sin(this.lat0),this.cos_p12=Math.cos(this.lat0)}function Du(t){var e=t.x,i=t.y,s=Math.sin(t.y),r=Math.cos(t.y),n=T(e-this.long0),a,h,o,l,f,u,c,v,d,g,p,y,x,M,_,S,b,L,A,I,C,O,F;return this.sphere?Math.abs(this.sin_p12-1)<=w?(t.x=this.x0+this.a*(E-i)*Math.sin(n),t.y=this.y0-this.a*(E-i)*Math.cos(n),t):Math.abs(this.sin_p12+1)<=w?(t.x=this.x0+this.a*(E+i)*Math.sin(n),t.y=this.y0+this.a*(E+i)*Math.cos(n),t):(L=this.sin_p12*s+this.cos_p12*r*Math.cos(n),S=Math.acos(L),b=S?S/Math.sin(S):1,t.x=this.x0+this.a*b*r*Math.sin(n),t.y=this.y0+this.a*b*(this.cos_p12*s-this.sin_p12*r*Math.cos(n)),t):(a=ae(this.es),h=he(this.es),o=oe(this.es),l=le(this.es),Math.abs(this.sin_p12-1)<=w?(f=this.a*Z(a,h,o,l,E),u=this.a*Z(a,h,o,l,i),t.x=this.x0+(f-u)*Math.sin(n),t.y=this.y0-(f-u)*Math.cos(n),t):Math.abs(this.sin_p12+1)<=w?(f=this.a*Z(a,h,o,l,E),u=this.a*Z(a,h,o,l,i),t.x=this.x0+(f+u)*Math.sin(n),t.y=this.y0+(f+u)*Math.cos(n),t):(c=s/r,v=Wt(this.a,this.e,this.sin_p12),d=Wt(this.a,this.e,s),g=Math.atan((1-this.es)*c+this.es*v*this.sin_p12/(d*r)),p=Math.atan2(Math.sin(n),this.cos_p12*Math.tan(g)-this.sin_p12*Math.cos(n)),p===0?A=Math.asin(this.cos_p12*Math.sin(g)-this.sin_p12*Math.cos(g)):Math.abs(Math.abs(p)-Math.PI)<=w?A=-Math.asin(this.cos_p12*Math.sin(g)-this.sin_p12*Math.cos(g)):A=Math.asin(Math.sin(n)*Math.cos(g)/Math.sin(p)),y=this.e*this.sin_p12/Math.sqrt(1-this.es),x=this.e*this.cos_p12*Math.cos(p)/Math.sqrt(1-this.es),M=y*x,_=x*x,I=A*A,C=I*A,O=C*A,F=O*A,S=v*A*(1-I*_*(1-_)/6+C/8*M*(1-2*_)+O/120*(_*(4-7*_)-3*y*y*(1-7*_))-F/48*M),t.x=this.x0+S*Math.sin(p),t.y=this.y0+S*Math.cos(p),t))}function zu(t){t.x-=this.x0,t.y-=this.y0;var e,i,s,r,n,a,h,o,l,f,u,c,v,d,g,p,y,x,M,_,S,b,L,A;return this.sphere?(e=Math.sqrt(t.x*t.x+t.y*t.y),e>2*E*this.a?void 0:(i=e/this.a,s=Math.sin(i),r=Math.cos(i),n=this.long0,Math.abs(e)<=w?a=this.lat0:(a=At(r*this.sin_p12+t.y*s*this.cos_p12/e),h=Math.abs(this.lat0)-E,Math.abs(h)<=w?this.lat0>=0?n=T(this.long0+Math.atan2(t.x,-t.y)):n=T(this.long0-Math.atan2(-t.x,t.y)):n=T(this.long0+Math.atan2(t.x*s,e*this.cos_p12*r-t.y*this.sin_p12*s))),t.x=n,t.y=a,t)):(o=ae(this.es),l=he(this.es),f=oe(this.es),u=le(this.es),Math.abs(this.sin_p12-1)<=w?(c=this.a*Z(o,l,f,u,E),e=Math.sqrt(t.x*t.x+t.y*t.y),v=c-e,a=Ie(v/this.a,o,l,f,u),n=T(this.long0+Math.atan2(t.x,-1*t.y)),t.x=n,t.y=a,t):Math.abs(this.sin_p12+1)<=w?(c=this.a*Z(o,l,f,u,E),e=Math.sqrt(t.x*t.x+t.y*t.y),v=e-c,a=Ie(v/this.a,o,l,f,u),n=T(this.long0+Math.atan2(t.x,t.y)),t.x=n,t.y=a,t):(e=Math.sqrt(t.x*t.x+t.y*t.y),p=Math.atan2(t.x,t.y),d=Wt(this.a,this.e,this.sin_p12),y=Math.cos(p),x=this.e*this.cos_p12*y,M=-x*x/(1-this.es),_=3*this.es*(1-M)*this.sin_p12*this.cos_p12*y/(1-this.es),S=e/d,b=S-M*(1+M)*Math.pow(S,3)/6-_*(1+3*M)*Math.pow(S,4)/24,L=1-M*b*b/2-S*b*b*b/6,g=Math.asin(this.sin_p12*Math.cos(b)+this.cos_p12*Math.sin(b)*y),n=T(this.long0+Math.asin(Math.sin(p)*Math.sin(b)/Math.cos(g))),A=Math.sin(g),a=Math.atan2((A-this.es*L*this.sin_p12)*Math.tan(g),A*(1-this.es)),t.x=n,t.y=a,t))}var ku=["Azimuthal_Equidistant","aeqd"];const Fu={init:Bu,forward:Du,inverse:zu,names:ku};function Gu(){this.sin_p14=Math.sin(this.lat0),this.cos_p14=Math.cos(this.lat0)}function Hu(t){var e,i,s,r,n,a,h,o,l=t.x,f=t.y;return s=T(l-this.long0),e=Math.sin(f),i=Math.cos(f),r=Math.cos(s),a=this.sin_p14*e+this.cos_p14*i*r,n=1,(a>0||Math.abs(a)<=w)&&(h=this.a*n*i*Math.sin(s),o=this.y0+this.a*n*(this.cos_p14*e-this.sin_p14*i*r)),t.x=h,t.y=o,t}function qu(t){var e,i,s,r,n,a,h;return t.x-=this.x0,t.y-=this.y0,e=Math.sqrt(t.x*t.x+t.y*t.y),i=At(e/this.a),s=Math.sin(i),r=Math.cos(i),a=this.long0,Math.abs(e)<=w?(h=this.lat0,t.x=a,t.y=h,t):(h=At(r*this.sin_p14+t.y*s*this.cos_p14/e),n=Math.abs(this.lat0)-E,Math.abs(n)<=w?(this.lat0>=0?a=T(this.long0+Math.atan2(t.x,-t.y)):a=T(this.long0-Math.atan2(-t.x,t.y)),t.x=a,t.y=h,t):(a=T(this.long0+Math.atan2(t.x*s,e*this.cos_p14*r-t.y*this.sin_p14*s)),t.x=a,t.y=h,t))}var Vu=["ortho"];const $u={init:Gu,forward:Hu,inverse:qu,names:Vu};var q={FRONT:1,RIGHT:2,BACK:3,LEFT:4,TOP:5,BOTTOM:6},D={AREA_0:1,AREA_1:2,AREA_2:3,AREA_3:4};function Wu(){this.x0=this.x0||0,this.y0=this.y0||0,this.lat0=this.lat0||0,this.long0=this.long0||0,this.lat_ts=this.lat_ts||0,this.title=this.title||"Quadrilateralized Spherical Cube",this.lat0>=E-B/2?this.face=q.TOP:this.lat0<=-(E-B/2)?this.face=q.BOTTOM:Math.abs(this.long0)<=B?this.face=q.FRONT:Math.abs(this.long0)<=E+B?this.face=this.long0>0?q.RIGHT:q.LEFT:this.face=q.BACK,this.es!==0&&(this.one_minus_f=1-(this.a-this.b)/this.a,this.one_minus_f_squared=this.one_minus_f*this.one_minus_f)}function Xu(t){var e={x:0,y:0},i,s,r,n,a,h,o={value:0};if(t.x-=this.long0,this.es!==0?i=Math.atan(this.one_minus_f_squared*Math.tan(t.y)):i=t.y,s=t.x,this.face===q.TOP)n=E-i,s>=B&&s<=E+B?(o.value=D.AREA_0,r=s-E):s>E+B||s<=-(E+B)?(o.value=D.AREA_1,r=s>0?s-$:s+$):s>-(E+B)&&s<=-B?(o.value=D.AREA_2,r=s+E):(o.value=D.AREA_3,r=s);else if(this.face===q.BOTTOM)n=E+i,s>=B&&s<=E+B?(o.value=D.AREA_0,r=-s+E):s<B&&s>=-B?(o.value=D.AREA_1,r=-s):s<-B&&s>=-(E+B)?(o.value=D.AREA_2,r=-s-E):(o.value=D.AREA_3,r=s>0?-s+$:-s-$);else{var l,f,u,c,v,d,g;this.face===q.RIGHT?s=Qt(s,+E):this.face===q.BACK?s=Qt(s,+$):this.face===q.LEFT&&(s=Qt(s,-E)),c=Math.sin(i),v=Math.cos(i),d=Math.sin(s),g=Math.cos(s),l=v*g,f=v*d,u=c,this.face===q.FRONT?(n=Math.acos(l),r=Ne(n,u,f,o)):this.face===q.RIGHT?(n=Math.acos(f),r=Ne(n,u,-l,o)):this.face===q.BACK?(n=Math.acos(-l),r=Ne(n,u,-f,o)):this.face===q.LEFT?(n=Math.acos(-f),r=Ne(n,u,l,o)):(n=r=0,o.value=D.AREA_0)}return h=Math.atan(12/$*(r+Math.acos(Math.sin(r)*Math.cos(B))-E)),a=Math.sqrt((1-Math.cos(n))/(Math.cos(h)*Math.cos(h))/(1-Math.cos(Math.atan(1/Math.cos(r))))),o.value===D.AREA_1?h+=E:o.value===D.AREA_2?h+=$:o.value===D.AREA_3&&(h+=1.5*$),e.x=a*Math.cos(h),e.y=a*Math.sin(h),e.x=e.x*this.a+this.x0,e.y=e.y*this.a+this.y0,t.x=e.x,t.y=e.y,t}function Qu(t){var e={lam:0,phi:0},i,s,r,n,a,h,o,l,f,u={value:0};if(t.x=(t.x-this.x0)/this.a,t.y=(t.y-this.y0)/this.a,s=Math.atan(Math.sqrt(t.x*t.x+t.y*t.y)),i=Math.atan2(t.y,t.x),t.x>=0&&t.x>=Math.abs(t.y)?u.value=D.AREA_0:t.y>=0&&t.y>=Math.abs(t.x)?(u.value=D.AREA_1,i-=E):t.x<0&&-t.x>=Math.abs(t.y)?(u.value=D.AREA_2,i=i<0?i+$:i-$):(u.value=D.AREA_3,i+=E),f=$/12*Math.tan(i),a=Math.sin(f)/(Math.cos(f)-1/Math.sqrt(2)),h=Math.atan(a),r=Math.cos(i),n=Math.tan(s),o=1-r*r*n*n*(1-Math.cos(Math.atan(1/Math.cos(h)))),o<-1?o=-1:o>1&&(o=1),this.face===q.TOP)l=Math.acos(o),e.phi=E-l,u.value===D.AREA_0?e.lam=h+E:u.value===D.AREA_1?e.lam=h<0?h+$:h-$:u.value===D.AREA_2?e.lam=h-E:e.lam=h;else if(this.face===q.BOTTOM)l=Math.acos(o),e.phi=l-E,u.value===D.AREA_0?e.lam=-h+E:u.value===D.AREA_1?e.lam=-h:u.value===D.AREA_2?e.lam=-h-E:e.lam=h<0?-h-$:-h+$;else{var c,v,d;c=o,f=c*c,f>=1?d=0:d=Math.sqrt(1-f)*Math.sin(h),f+=d*d,f>=1?v=0:v=Math.sqrt(1-f),u.value===D.AREA_1?(f=v,v=-d,d=f):u.value===D.AREA_2?(v=-v,d=-d):u.value===D.AREA_3&&(f=v,v=d,d=-f),this.face===q.RIGHT?(f=c,c=-v,v=f):this.face===q.BACK?(c=-c,v=-v):this.face===q.LEFT&&(f=c,c=v,v=-f),e.phi=Math.acos(-d)-E,e.lam=Math.atan2(v,c),this.face===q.RIGHT?e.lam=Qt(e.lam,-E):this.face===q.BACK?e.lam=Qt(e.lam,-$):this.face===q.LEFT&&(e.lam=Qt(e.lam,+E))}if(this.es!==0){var g,p,y;g=e.phi<0?1:0,p=Math.tan(e.phi),y=this.b/Math.sqrt(p*p+this.one_minus_f_squared),e.phi=Math.atan(Math.sqrt(this.a*this.a-y*y)/(this.one_minus_f*y)),g&&(e.phi=-e.phi)}return e.lam+=this.long0,t.x=e.lam,t.y=e.phi,t}function Ne(t,e,i,s){var r;return t<w?(s.value=D.AREA_0,r=0):(r=Math.atan2(e,i),Math.abs(r)<=B?s.value=D.AREA_0:r>B&&r<=E+B?(s.value=D.AREA_1,r-=E):r>E+B||r<=-(E+B)?(s.value=D.AREA_2,r=r>=0?r-$:r+$):(s.value=D.AREA_3,r+=E)),r}function Qt(t,e){var i=t+e;return i<-$?i+=te:i>+$&&(i-=te),i}var ju=["Quadrilateralized Spherical Cube","Quadrilateralized_Spherical_Cube","qsc"];const Ku={init:Wu,forward:Xu,inverse:Qu,names:ju};var ai=[[1,22199e-21,-715515e-10,31103e-10],[.9986,-482243e-9,-24897e-9,-13309e-10],[.9954,-83103e-8,-448605e-10,-986701e-12],[.99,-.00135364,-59661e-9,36777e-10],[.9822,-.00167442,-449547e-11,-572411e-11],[.973,-.00214868,-903571e-10,18736e-12],[.96,-.00305085,-900761e-10,164917e-11],[.9427,-.00382792,-653386e-10,-26154e-10],[.9216,-.00467746,-10457e-8,481243e-11],[.8962,-.00536223,-323831e-10,-543432e-11],[.8679,-.00609363,-113898e-9,332484e-11],[.835,-.00698325,-640253e-10,934959e-12],[.7986,-.00755338,-500009e-10,935324e-12],[.7597,-.00798324,-35971e-9,-227626e-11],[.7186,-.00851367,-701149e-10,-86303e-10],[.6732,-.00986209,-199569e-9,191974e-10],[.6213,-.010418,883923e-10,624051e-11],[.5722,-.00906601,182e-6,624051e-11],[.5322,-.00677797,275608e-9,624051e-11]],ue=[[-520417e-23,.0124,121431e-23,-845284e-16],[.062,.0124,-126793e-14,422642e-15],[.124,.0124,507171e-14,-160604e-14],[.186,.0123999,-190189e-13,600152e-14],[.248,.0124002,710039e-13,-224e-10],[.31,.0123992,-264997e-12,835986e-13],[.372,.0124029,988983e-12,-311994e-12],[.434,.0123893,-369093e-11,-435621e-12],[.4958,.0123198,-102252e-10,-345523e-12],[.5571,.0121916,-154081e-10,-582288e-12],[.6176,.0119938,-241424e-10,-525327e-12],[.6769,.011713,-320223e-10,-516405e-12],[.7346,.0113541,-397684e-10,-609052e-12],[.7903,.0109107,-489042e-10,-104739e-11],[.8435,.0103431,-64615e-9,-140374e-14],[.8936,.00969686,-64636e-9,-8547e-9],[.9394,.00840947,-192841e-9,-42106e-10],[.9761,.00616527,-256e-6,-42106e-10],[1,.00328947,-319159e-9,-42106e-10]],Fs=.8487,Gs=1.3523,Hs=mt/5,Yu=1/Hs,jt=18,Pe=function(t,e){return t[0]+e*(t[1]+e*(t[2]+e*t[3]))},Zu=function(t,e){return t[1]+e*(2*t[2]+e*3*t[3])};function Ju(t,e,i,s){for(var r=e;s;--s){var n=t(r);if(r-=n,Math.abs(n)<i)break}return r}function tf(){this.x0=this.x0||0,this.y0=this.y0||0,this.long0=this.long0||0,this.es=0,this.title=this.title||"Robinson"}function ef(t){var e=T(t.x-this.long0),i=Math.abs(t.y),s=Math.floor(i*Hs);s<0?s=0:s>=jt&&(s=jt-1),i=mt*(i-Yu*s);var r={x:Pe(ai[s],i)*e,y:Pe(ue[s],i)};return t.y<0&&(r.y=-r.y),r.x=r.x*this.a*Fs+this.x0,r.y=r.y*this.a*Gs+this.y0,r}function sf(t){var e={x:(t.x-this.x0)/(this.a*Fs),y:Math.abs(t.y-this.y0)/(this.a*Gs)};if(e.y>=1)e.x/=ai[jt][0],e.y=t.y<0?-E:E;else{var i=Math.floor(e.y*jt);for(i<0?i=0:i>=jt&&(i=jt-1);;)if(ue[i][0]>e.y)--i;else if(ue[i+1][0]<=e.y)++i;else break;var s=ue[i],r=5*(e.y-s[0])/(ue[i+1][0]-s[0]);r=Ju(function(n){return(Pe(s,n)-e.y)/Zu(s,n)},r,w,100),e.x/=Pe(ai[i],r),e.y=(5*i+r)*j,t.y<0&&(e.y=-e.y)}return e.x=T(e.x+this.long0),e}var rf=["Robinson","robin"];const nf={init:tf,forward:ef,inverse:sf,names:rf};function af(){this.name="geocent"}function hf(t){var e=ms(t,this.es,this.a);return e}function of(t){var e=gs(t,this.es,this.a,this.b);return e}var lf=["Geocentric","geocentric","geocent","Geocent"];const uf={init:af,forward:hf,inverse:of,names:lf};var K={N_POLE:0,S_POLE:1,EQUIT:2,OBLIQ:3},fe={h:{def:1e5,num:!0},azi:{def:0,num:!0,degrees:!0},tilt:{def:0,num:!0,degrees:!0},long0:{def:0,num:!0},lat0:{def:0,num:!0}};function ff(){if(Object.keys(fe).forEach((function(i){if(typeof this[i]>"u")this[i]=fe[i].def;else{if(fe[i].num&&isNaN(this[i]))throw new Error("Invalid parameter value, must be numeric "+i+" = "+this[i]);fe[i].num&&(this[i]=parseFloat(this[i]))}fe[i].degrees&&(this[i]=this[i]*j)}).bind(this)),Math.abs(Math.abs(this.lat0)-E)<w?this.mode=this.lat0<0?K.S_POLE:K.N_POLE:Math.abs(this.lat0)<w?this.mode=K.EQUIT:(this.mode=K.OBLIQ,this.sinph0=Math.sin(this.lat0),this.cosph0=Math.cos(this.lat0)),this.pn1=this.h/this.a,this.pn1<=0||this.pn1>1e10)throw new Error("Invalid height");this.p=1+this.pn1,this.rp=1/this.p,this.h1=1/this.pn1,this.pfact=(this.p+1)*this.h1,this.es=0;var t=this.tilt,e=this.azi;this.cg=Math.cos(e),this.sg=Math.sin(e),this.cw=Math.cos(t),this.sw=Math.sin(t)}function cf(t){t.x-=this.long0;var e=Math.sin(t.y),i=Math.cos(t.y),s=Math.cos(t.x),r,n;switch(this.mode){case K.OBLIQ:n=this.sinph0*e+this.cosph0*i*s;break;case K.EQUIT:n=i*s;break;case K.S_POLE:n=-e;break;case K.N_POLE:n=e;break}switch(n=this.pn1/(this.p-n),r=n*i*Math.sin(t.x),this.mode){case K.OBLIQ:n*=this.cosph0*e-this.sinph0*i*s;break;case K.EQUIT:n*=e;break;case K.N_POLE:n*=-(i*s);break;case K.S_POLE:n*=i*s;break}var a,h;return a=n*this.cg+r*this.sg,h=1/(a*this.sw*this.h1+this.cw),r=(r*this.cg-n*this.sg)*this.cw*h,n=a*h,t.x=r*this.a,t.y=n*this.a,t}function vf(t){t.x/=this.a,t.y/=this.a;var e={x:t.x,y:t.y},i,s,r;r=1/(this.pn1-t.y*this.sw),i=this.pn1*t.x*r,s=this.pn1*t.y*this.cw*r,t.x=i*this.cg+s*this.sg,t.y=s*this.cg-i*this.sg;var n=ut(t.x,t.y);if(Math.abs(n)<w)e.x=0,e.y=t.y;else{var a,h;switch(h=1-n*n*this.pfact,h=(this.p-Math.sqrt(h))/(this.pn1/n+n/this.pn1),a=Math.sqrt(1-h*h),this.mode){case K.OBLIQ:e.y=Math.asin(a*this.sinph0+t.y*h*this.cosph0/n),t.y=(a-this.sinph0*Math.sin(e.y))*n,t.x*=h*this.cosph0;break;case K.EQUIT:e.y=Math.asin(t.y*h/n),t.y=a*n,t.x*=h;break;case K.N_POLE:e.y=Math.asin(a),t.y=-t.y;break;case K.S_POLE:e.y=-Math.asin(a);break}e.x=Math.atan2(t.x,t.y)}return t.x=e.x+this.long0,t.y=e.y,t}var df=["Tilted_Perspective","tpers"];const mf={init:ff,forward:cf,inverse:vf,names:df};function gf(){if(this.flip_axis=this.sweep==="x"?1:0,this.h=Number(this.h),this.radius_g_1=this.h/this.a,this.radius_g_1<=0||this.radius_g_1>1e10)throw new Error;if(this.radius_g=1+this.radius_g_1,this.C=this.radius_g*this.radius_g-1,this.es!==0){var t=1-this.es,e=1/t;this.radius_p=Math.sqrt(t),this.radius_p2=t,this.radius_p_inv2=e,this.shape="ellipse"}else this.radius_p=1,this.radius_p2=1,this.radius_p_inv2=1,this.shape="sphere";this.title||(this.title="Geostationary Satellite View")}function pf(t){var e=t.x,i=t.y,s,r,n,a;if(e=e-this.long0,this.shape==="ellipse"){i=Math.atan(this.radius_p2*Math.tan(i));var h=this.radius_p/ut(this.radius_p*Math.cos(i),Math.sin(i));if(r=h*Math.cos(e)*Math.cos(i),n=h*Math.sin(e)*Math.cos(i),a=h*Math.sin(i),(this.radius_g-r)*r-n*n-a*a*this.radius_p_inv2<0)return t.x=Number.NaN,t.y=Number.NaN,t;s=this.radius_g-r,this.flip_axis?(t.x=this.radius_g_1*Math.atan(n/ut(a,s)),t.y=this.radius_g_1*Math.atan(a/s)):(t.x=this.radius_g_1*Math.atan(n/s),t.y=this.radius_g_1*Math.atan(a/ut(n,s)))}else this.shape==="sphere"&&(s=Math.cos(i),r=Math.cos(e)*s,n=Math.sin(e)*s,a=Math.sin(i),s=this.radius_g-r,this.flip_axis?(t.x=this.radius_g_1*Math.atan(n/ut(a,s)),t.y=this.radius_g_1*Math.atan(a/s)):(t.x=this.radius_g_1*Math.atan(n/s),t.y=this.radius_g_1*Math.atan(a/ut(n,s))));return t.x=t.x*this.a,t.y=t.y*this.a,t}function yf(t){var e=-1,i=0,s=0,r,n,a,h;if(t.x=t.x/this.a,t.y=t.y/this.a,this.shape==="ellipse"){this.flip_axis?(s=Math.tan(t.y/this.radius_g_1),i=Math.tan(t.x/this.radius_g_1)*ut(1,s)):(i=Math.tan(t.x/this.radius_g_1),s=Math.tan(t.y/this.radius_g_1)*ut(1,i));var o=s/this.radius_p;if(r=i*i+o*o+e*e,n=2*this.radius_g*e,a=n*n-4*r*this.C,a<0)return t.x=Number.NaN,t.y=Number.NaN,t;h=(-n-Math.sqrt(a))/(2*r),e=this.radius_g+h*e,i*=h,s*=h,t.x=Math.atan2(i,e),t.y=Math.atan(s*Math.cos(t.x)/e),t.y=Math.atan(this.radius_p_inv2*Math.tan(t.y))}else if(this.shape==="sphere"){if(this.flip_axis?(s=Math.tan(t.y/this.radius_g_1),i=Math.tan(t.x/this.radius_g_1)*Math.sqrt(1+s*s)):(i=Math.tan(t.x/this.radius_g_1),s=Math.tan(t.y/this.radius_g_1)*Math.sqrt(1+i*i)),r=i*i+s*s+e*e,n=2*this.radius_g*e,a=n*n-4*r*this.C,a<0)return t.x=Number.NaN,t.y=Number.NaN,t;h=(-n-Math.sqrt(a))/(2*r),e=this.radius_g+h*e,i*=h,s*=h,t.x=Math.atan2(i,e),t.y=Math.atan(s*Math.cos(t.x)/e)}return t.x=t.x+this.long0,t}var Mf=["Geostationary Satellite View","Geostationary_Satellite","geos"];const xf={init:gf,forward:pf,inverse:yf,names:Mf};function _f(t){t.Proj.projections.add(Re),t.Proj.projections.add(Le),t.Proj.projections.add(To),t.Proj.projections.add(Bo),t.Proj.projections.add(Ho),t.Proj.projections.add(Xo),t.Proj.projections.add(Jo),t.Proj.projections.add(rl),t.Proj.projections.add(ll),t.Proj.projections.add(dl),t.Proj.projections.add(Nl),t.Proj.projections.add(Dl),t.Proj.projections.add(Hl),t.Proj.projections.add(Ql),t.Proj.projections.add(Jl),t.Proj.projections.add(ru),t.Proj.projections.add(lu),t.Proj.projections.add(du),t.Proj.projections.add(xu),t.Proj.projections.add(wu),t.Proj.projections.add(Iu),t.Proj.projections.add(Uu),t.Proj.projections.add(Fu),t.Proj.projections.add($u),t.Proj.projections.add(Ku),t.Proj.projections.add(nf),t.Proj.projections.add(uf),t.Proj.projections.add(mf),t.Proj.projections.add(xf)}rt.defaultDatum="WGS84",rt.Proj=pt,rt.WGS84=new rt.Proj("WGS84"),rt.Point=Vt,rt.toPoint=xs,rt.defs=Y,rt.nadgrid=Ah,rt.transform=Te,rt.mgrs=qh,rt.version="__VERSION__",_f(rt);const Ef=6378137,Sf=6378137,bf=6356752314245179e-9,Ce=t=>t,wf=new z;function Tf(t,e,i=Ce){return bt(t)?(e[0]=i(t[0]),e[1]=i(t[1]),e[2]=t[2]):"longitude"in t?(e[0]=i(t.longitude),e[1]=i(t.latitude),e[2]=t.height):(e[0]=i(t.x),e[1]=i(t.y),e[2]=t.z),e}function Af(t,e=wf){return Tf(t,e,H._cartographicRadians?Ce:sa)}function Rf(t,e,i=Ce){return bt(e)?(e[0]=i(t[0]),e[1]=i(t[1]),e[2]=t[2]):"longitude"in e?(e.longitude=i(t[0]),e.latitude=i(t[1]),e.height=t[2]):(e.x=i(t[0]),e.y=i(t[1]),e.z=t[2]),e}function Lf(t,e){return Rf(t,e,H._cartographicRadians?Ce:ra)}const qs=new z,If=new z,Nf=new z;function Pf(t,e,i=new z){const{oneOverRadii:s,oneOverRadiiSquared:r,centerToleranceSquared:n}=e;qs.from(t);const a=t.x,h=t.y,o=t.z,l=s.x,f=s.y,u=s.z,c=a*a*l*l,v=h*h*f*f,d=o*o*u*u,g=c+v+d,p=Math.sqrt(1/g);if(!Number.isFinite(p))return;const y=If;if(y.copy(t).scale(p),g<n)return y.to(i);const x=r.x,M=r.y,_=r.z,S=Nf;S.set(y.x*x*2,y.y*M*2,y.z*_*2);let b=(1-p)*t.len()/(.5*S.len()),L=0,A,I,C,O;do{b-=L,A=1/(1+b*x),I=1/(1+b*M),C=1/(1+b*_);const F=A*A,V=I*I,P=C*C,vt=F*A,Tc=V*I,Ac=P*C;O=c*F+v*V+d*P-1;const Rc=-2*(c*vt*x+v*Tc*M+d*Ac*_);L=O/Rc}while(Math.abs(O)>je.EPSILON12);return qs.scale([A,I,C]).to(i)}const Vs=1e-14,Cf=new z,$s={up:{south:"east",north:"west",west:"south",east:"north"},down:{south:"west",north:"east",west:"north",east:"south"},south:{up:"west",down:"east",west:"down",east:"up"},north:{up:"east",down:"west",west:"up",east:"down"},west:{up:"north",down:"south",north:"down",south:"up"},east:{up:"south",down:"north",north:"up",south:"down"}},hi={north:[-1,0,0],east:[0,1,0],up:[0,0,1],south:[1,0,0],west:[0,-1,0],down:[0,0,-1]},ce={east:new z,north:new z,up:new z,west:new z,south:new z,down:new z},Of=new z,Uf=new z,Bf=new z;function Ws(t,e,i,s,r,n){const a=$s[e]&&$s[e][i];st(a&&(!s||s===a));let h,o,l;const f=Cf.copy(r);if(Zt(f.x,0,Vs)&&Zt(f.y,0,Vs)){const c=Math.sign(f.z);h=Of.fromArray(hi[e]),e!=="east"&&e!=="west"&&h.scale(c),o=Uf.fromArray(hi[i]),i!=="east"&&i!=="west"&&o.scale(c),l=Bf.fromArray(hi[s]),s!=="east"&&s!=="west"&&l.scale(c)}else{const{up:c,east:v,north:d}=ce;v.set(-f.y,f.x,0).normalize(),t.geodeticSurfaceNormal(f,c),d.copy(c).cross(v);const{down:g,west:p,south:y}=ce;g.copy(c).scale(-1),p.copy(v).scale(-1),y.copy(d).scale(-1),h=ce[e],o=ce[i],l=ce[s]}return n[0]=h.x,n[1]=h.y,n[2]=h.z,n[3]=0,n[4]=o.x,n[5]=o.y,n[6]=o.z,n[7]=0,n[8]=l.x,n[9]=l.y,n[10]=l.z,n[11]=0,n[12]=f.x,n[13]=f.y,n[14]=f.z,n[15]=1,n}const Oe=new z,Xs=new z,Df=new z,ft=new z,zf=new z,Ue=new z;let oi;class li{static get WGS84(){return oi=oi||new li(Ef,Sf,bf),oi}constructor(e=0,i=0,s=0){st(e>=0),st(i>=0),st(s>=0),this.radii=new z(e,i,s),this.radiiSquared=new z(e*e,i*i,s*s),this.radiiToTheFourth=new z(e*e*e*e,i*i*i*i,s*s*s*s),this.oneOverRadii=new z(e===0?0:1/e,i===0?0:1/i,s===0?0:1/s),this.oneOverRadiiSquared=new z(e===0?0:1/(e*e),i===0?0:1/(i*i),s===0?0:1/(s*s)),this.minimumRadius=Math.min(e,i,s),this.maximumRadius=Math.max(e,i,s),this.centerToleranceSquared=je.EPSILON1,this.radiiSquared.z!==0&&(this.squaredXOverSquaredZ=this.radiiSquared.x/this.radiiSquared.z),Object.freeze(this)}equals(e){return this===e||!!(e&&this.radii.equals(e.radii))}toString(){return this.radii.toString()}cartographicToCartesian(e,i=[0,0,0]){const s=Xs,r=Df,[,,n]=e;this.geodeticSurfaceNormalCartographic(e,s),r.copy(this.radiiSquared).scale(s);const a=Math.sqrt(s.dot(r));return r.scale(1/a),s.scale(n),r.add(s),r.to(i)}cartesianToCartographic(e,i=[0,0,0]){Ue.from(e);const s=this.scaleToGeodeticSurface(Ue,ft);if(!s)return;const r=this.geodeticSurfaceNormal(s,Xs),n=zf;n.copy(Ue).subtract(s);const a=Math.atan2(r.y,r.x),h=Math.asin(r.z),o=Math.sign(Ki(n,Ue))*ga(n);return Lf([a,h,o],i)}eastNorthUpToFixedFrame(e,i=new Pt){return Ws(this,"east","north","up",e,i)}localFrameToFixedFrame(e,i,s,r,n=new Pt){return Ws(this,e,i,s,r,n)}geocentricSurfaceNormal(e,i=[0,0,0]){return Oe.from(e).normalize().to(i)}geodeticSurfaceNormalCartographic(e,i=[0,0,0]){const s=Af(e),r=s[0],n=s[1],a=Math.cos(n);return Oe.set(a*Math.cos(r),a*Math.sin(r),Math.sin(n)).normalize(),Oe.to(i)}geodeticSurfaceNormal(e,i=[0,0,0]){return Oe.from(e).scale(this.oneOverRadiiSquared).normalize().to(i)}scaleToGeodeticSurface(e,i){return Pf(e,this,i)}scaleToGeocentricSurface(e,i=[0,0,0]){ft.from(e);const s=ft.x,r=ft.y,n=ft.z,a=this.oneOverRadiiSquared,h=1/Math.sqrt(s*s*a.x+r*r*a.y+n*n*a.z);return ft.multiplyScalar(h).to(i)}transformPositionToScaledSpace(e,i=[0,0,0]){return ft.from(e).scale(this.oneOverRadii).to(i)}transformPositionFromScaledSpace(e,i=[0,0,0]){return ft.from(e).scale(this.radii).to(i)}getSurfaceNormalIntersectionWithZAxis(e,i=0,s=[0,0,0]){st(Zt(this.radii.x,this.radii.y,je.EPSILON15)),st(this.radii.z>0),ft.from(e);const r=ft.z*(1-this.squaredXOverSquaredZ);if(!(Math.abs(r)>=this.radii.z-i))return ft.set(0,0,r).to(s)}}var it=63710088e-1,kf={centimeters:it*100,centimetres:it*100,degrees:it/111325,feet:it*3.28084,inches:it*39.37,kilometers:it/1e3,kilometres:it/1e3,meters:it,metres:it,miles:it/1609.344,millimeters:it*1e3,millimetres:it*1e3,nauticalmiles:it/1852,radians:1,yards:it*1.0936};function Qs(t,e,i){i===void 0&&(i={});var s={type:"Feature"};return(i.id===0||i.id)&&(s.id=i.id),i.bbox&&(s.bbox=i.bbox),s.properties=e||{},s.geometry=t,s}function Ff(t,e,i){i===void 0&&(i={});for(var s=0,r=t;s<r.length;s++){var n=r[s];if(n.length<4)throw new Error("Each LinearRing of a Polygon must have 4 or more Positions.");for(var a=0;a<n[n.length-1].length;a++)if(n[n.length-1][a]!==n[0][a])throw new Error("First and last Position are not equivalent.")}var h={type:"Polygon",coordinates:t};return Qs(h,e,i)}function Gf(t,e,i){i===void 0&&(i={});var s={type:"MultiPolygon",coordinates:t};return Qs(s,e,i)}function Hf(t,e){e===void 0&&(e="kilometers");var i=kf[e];if(!i)throw new Error(e+" units is invalid");return t*i}function Be(t){var e=t%360;return e*Math.PI/180}function qf(t){return!isNaN(t)&&t!==null&&!Array.isArray(t)}function js(t){if(!t)throw new Error("coord is required");if(!Array.isArray(t)){if(t.type==="Feature"&&t.geometry!==null&&t.geometry.type==="Point")return t.geometry.coordinates;if(t.type==="Point")return t.coordinates}if(Array.isArray(t)&&t.length>=2&&!Array.isArray(t[0])&&!Array.isArray(t[1]))return t;throw new Error("coord must be GeoJSON Point or an Array of numbers")}function Ks(t){return t.type==="Feature"?t.geometry:t}function Vf(t,e,i){i===void 0&&(i={});var s=js(t),r=js(e),n=Be(r[1]-s[1]),a=Be(r[0]-s[0]),h=Be(s[1]),o=Be(r[1]),l=Math.pow(Math.sin(n/2),2)+Math.pow(Math.sin(a/2),2)*Math.cos(h)*Math.cos(o);return Hf(2*Math.atan2(Math.sqrt(l),Math.sqrt(1-l)),i.units)}/**
 * splaytree v3.1.1
 * Fast Splay tree for Node and browser
 *
 * <AUTHOR> Milevski <<EMAIL>>
 * @license MIT
 * @preserve
 *//*! *****************************************************************************
  Copyright (c) Microsoft Corporation. All rights reserved.
  Licensed under the Apache License, Version 2.0 (the "License"); you may not use
  this file except in compliance with the License. You may obtain a copy of the
  License at http://www.apache.org/licenses/LICENSE-2.0

  THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
  WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
  MERCHANTABLITY OR NON-INFRINGEMENT.

  See the Apache Version 2.0 License for specific language governing permissions
  and limitations under the License.
  ***************************************************************************** */function $f(t,e){var i={label:0,sent:function(){if(n[0]&1)throw n[1];return n[1]},trys:[],ops:[]},s,r,n,a;return a={next:h(0),throw:h(1),return:h(2)},typeof Symbol=="function"&&(a[Symbol.iterator]=function(){return this}),a;function h(l){return function(f){return o([l,f])}}function o(l){if(s)throw new TypeError("Generator is already executing.");for(;i;)try{if(s=1,r&&(n=l[0]&2?r.return:l[0]?r.throw||((n=r.return)&&n.call(r),0):r.next)&&!(n=n.call(r,l[1])).done)return n;switch(r=0,n&&(l=[l[0]&2,n.value]),l[0]){case 0:case 1:n=l;break;case 4:return i.label++,{value:l[1],done:!1};case 5:i.label++,r=l[1],l=[0];continue;case 7:l=i.ops.pop(),i.trys.pop();continue;default:if(n=i.trys,!(n=n.length>0&&n[n.length-1])&&(l[0]===6||l[0]===2)){i=0;continue}if(l[0]===3&&(!n||l[1]>n[0]&&l[1]<n[3])){i.label=l[1];break}if(l[0]===6&&i.label<n[1]){i.label=n[1],n=l;break}if(n&&i.label<n[2]){i.label=n[2],i.ops.push(l);break}n[2]&&i.ops.pop(),i.trys.pop();continue}l=e.call(t,i)}catch(f){l=[6,f],r=0}finally{s=n=0}if(l[0]&5)throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}}var Rt=function(){function t(e,i){this.next=null,this.key=e,this.data=i,this.left=null,this.right=null}return t}();function Wf(t,e){return t>e?1:t<e?-1:0}function Lt(t,e,i){for(var s=new Rt(null,null),r=s,n=s;;){var a=i(t,e.key);if(a<0){if(e.left===null)break;if(i(t,e.left.key)<0){var h=e.left;if(e.left=h.right,h.right=e,e=h,e.left===null)break}n.left=e,n=e,e=e.left}else if(a>0){if(e.right===null)break;if(i(t,e.right.key)>0){var h=e.right;if(e.right=h.left,h.left=e,e=h,e.right===null)break}r.right=e,r=e,e=e.right}else break}return r.right=e.left,n.left=e.right,e.left=s.right,e.right=s.left,e}function ui(t,e,i,s){var r=new Rt(t,e);if(i===null)return r.left=r.right=null,r;i=Lt(t,i,s);var n=s(t,i.key);return n<0?(r.left=i.left,r.right=i,i.left=null):n>=0&&(r.right=i.right,r.left=i,i.right=null),r}function Ys(t,e,i){var s=null,r=null;if(e){e=Lt(t,e,i);var n=i(e.key,t);n===0?(s=e.left,r=e.right):n<0?(r=e.right,e.right=null,s=e):(s=e.left,e.left=null,r=e)}return{left:s,right:r}}function Xf(t,e,i){return e===null?t:(t===null||(e=Lt(t.key,e,i),e.left=t),e)}function fi(t,e,i,s,r){if(t){s(""+e+(i?"└── ":"├── ")+r(t)+`
`);var n=e+(i?"    ":"│   ");t.left&&fi(t.left,n,!1,s,r),t.right&&fi(t.right,n,!0,s,r)}}var ci=function(){function t(e){e===void 0&&(e=Wf),this._root=null,this._size=0,this._comparator=e}return t.prototype.insert=function(e,i){return this._size++,this._root=ui(e,i,this._root,this._comparator)},t.prototype.add=function(e,i){var s=new Rt(e,i);this._root===null&&(s.left=s.right=null,this._size++,this._root=s);var r=this._comparator,n=Lt(e,this._root,r),a=r(e,n.key);return a===0?this._root=n:(a<0?(s.left=n.left,s.right=n,n.left=null):a>0&&(s.right=n.right,s.left=n,n.right=null),this._size++,this._root=s),this._root},t.prototype.remove=function(e){this._root=this._remove(e,this._root,this._comparator)},t.prototype._remove=function(e,i,s){var r;if(i===null)return null;i=Lt(e,i,s);var n=s(e,i.key);return n===0?(i.left===null?r=i.right:(r=Lt(e,i.left,s),r.right=i.right),this._size--,r):i},t.prototype.pop=function(){var e=this._root;if(e){for(;e.left;)e=e.left;return this._root=Lt(e.key,this._root,this._comparator),this._root=this._remove(e.key,this._root,this._comparator),{key:e.key,data:e.data}}return null},t.prototype.findStatic=function(e){for(var i=this._root,s=this._comparator;i;){var r=s(e,i.key);if(r===0)return i;r<0?i=i.left:i=i.right}return null},t.prototype.find=function(e){return this._root&&(this._root=Lt(e,this._root,this._comparator),this._comparator(e,this._root.key)!==0)?null:this._root},t.prototype.contains=function(e){for(var i=this._root,s=this._comparator;i;){var r=s(e,i.key);if(r===0)return!0;r<0?i=i.left:i=i.right}return!1},t.prototype.forEach=function(e,i){for(var s=this._root,r=[],n=!1;!n;)s!==null?(r.push(s),s=s.left):r.length!==0?(s=r.pop(),e.call(i,s),s=s.right):n=!0;return this},t.prototype.range=function(e,i,s,r){for(var n=[],a=this._comparator,h=this._root,o;n.length!==0||h;)if(h)n.push(h),h=h.left;else{if(h=n.pop(),o=a(h.key,i),o>0)break;if(a(h.key,e)>=0&&s.call(r,h))return this;h=h.right}return this},t.prototype.keys=function(){var e=[];return this.forEach(function(i){var s=i.key;return e.push(s)}),e},t.prototype.values=function(){var e=[];return this.forEach(function(i){var s=i.data;return e.push(s)}),e},t.prototype.min=function(){return this._root?this.minNode(this._root).key:null},t.prototype.max=function(){return this._root?this.maxNode(this._root).key:null},t.prototype.minNode=function(e){if(e===void 0&&(e=this._root),e)for(;e.left;)e=e.left;return e},t.prototype.maxNode=function(e){if(e===void 0&&(e=this._root),e)for(;e.right;)e=e.right;return e},t.prototype.at=function(e){for(var i=this._root,s=!1,r=0,n=[];!s;)if(i)n.push(i),i=i.left;else if(n.length>0){if(i=n.pop(),r===e)return i;r++,i=i.right}else s=!0;return null},t.prototype.next=function(e){var i=this._root,s=null;if(e.right){for(s=e.right;s.left;)s=s.left;return s}for(var r=this._comparator;i;){var n=r(e.key,i.key);if(n===0)break;n<0?(s=i,i=i.left):i=i.right}return s},t.prototype.prev=function(e){var i=this._root,s=null;if(e.left!==null){for(s=e.left;s.right;)s=s.right;return s}for(var r=this._comparator;i;){var n=r(e.key,i.key);if(n===0)break;n<0?i=i.left:(s=i,i=i.right)}return s},t.prototype.clear=function(){return this._root=null,this._size=0,this},t.prototype.toList=function(){return jf(this._root)},t.prototype.load=function(e,i,s){i===void 0&&(i=[]),s===void 0&&(s=!1);var r=e.length,n=this._comparator;if(s&&mi(e,i,0,r-1,n),this._root===null)this._root=vi(e,i,0,r),this._size=r;else{var a=Kf(this.toList(),Qf(e,i),n);r=this._size+r,this._root=di({head:a},0,r)}return this},t.prototype.isEmpty=function(){return this._root===null},Object.defineProperty(t.prototype,"size",{get:function(){return this._size},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"root",{get:function(){return this._root},enumerable:!0,configurable:!0}),t.prototype.toString=function(e){e===void 0&&(e=function(s){return String(s.key)});var i=[];return fi(this._root,"",!0,function(s){return i.push(s)},e),i.join("")},t.prototype.update=function(e,i,s){var r=this._comparator,n=Ys(e,this._root,r),a=n.left,h=n.right;r(e,i)<0?h=ui(i,s,h,r):a=ui(i,s,a,r),this._root=Xf(a,h,r)},t.prototype.split=function(e){return Ys(e,this._root,this._comparator)},t.prototype[Symbol.iterator]=function(){var e;return $f(this,function(i){switch(i.label){case 0:e=this.minNode(),i.label=1;case 1:return e?[4,e]:[3,3];case 2:return i.sent(),e=this.next(e),[3,1];case 3:return[2]}})},t}();function vi(t,e,i,s){var r=s-i;if(r>0){var n=i+Math.floor(r/2),a=t[n],h=e[n],o=new Rt(a,h);return o.left=vi(t,e,i,n),o.right=vi(t,e,n+1,s),o}return null}function Qf(t,e){for(var i=new Rt(null,null),s=i,r=0;r<t.length;r++)s=s.next=new Rt(t[r],e[r]);return s.next=null,i.next}function jf(t){for(var e=t,i=[],s=!1,r=new Rt(null,null),n=r;!s;)e?(i.push(e),e=e.left):i.length>0?(e=n=n.next=i.pop(),e=e.right):s=!0;return n.next=null,r.next}function di(t,e,i){var s=i-e;if(s>0){var r=e+Math.floor(s/2),n=di(t,e,r),a=t.head;return a.left=n,t.head=t.head.next,a.right=di(t,r+1,i),a}return null}function Kf(t,e,i){for(var s=new Rt(null,null),r=s,n=t,a=e;n!==null&&a!==null;)i(n.key,a.key)<0?(r.next=n,n=n.next):(r.next=a,a=a.next),r=r.next;return n!==null?r.next=n:a!==null&&(r.next=a),s.next}function mi(t,e,i,s,r){if(!(i>=s)){for(var n=t[i+s>>1],a=i-1,h=s+1;;){do a++;while(r(t[a],n)<0);do h--;while(r(t[h],n)>0);if(a>=h)break;var o=t[a];t[a]=t[h],t[h]=o,o=e[a],e[a]=e[h],e[h]=o}mi(t,e,i,h,r),mi(t,e,h+1,s,r)}}function at(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Zs(t,e){for(var i=0;i<e.length;i++){var s=e[i];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(t,s.key,s)}}function J(t,e,i){return e&&Zs(t.prototype,e),i&&Zs(t,i),t}var ve=function(e,i){return e.ll.x<=i.x&&i.x<=e.ur.x&&e.ll.y<=i.y&&i.y<=e.ur.y},gi=function(e,i){if(i.ur.x<e.ll.x||e.ur.x<i.ll.x||i.ur.y<e.ll.y||e.ur.y<i.ll.y)return null;var s=e.ll.x<i.ll.x?i.ll.x:e.ll.x,r=e.ur.x<i.ur.x?e.ur.x:i.ur.x,n=e.ll.y<i.ll.y?i.ll.y:e.ll.y,a=e.ur.y<i.ur.y?e.ur.y:i.ur.y;return{ll:{x:s,y:n},ur:{x:r,y:a}}},It=Number.EPSILON;It===void 0&&(It=Math.pow(2,-52));var Yf=It*It,pi=function(e,i){if(-It<e&&e<It&&-It<i&&i<It)return 0;var s=e-i;return s*s<Yf*e*i?0:e<i?-1:1},Zf=function(){function t(){at(this,t),this.reset()}return J(t,[{key:"reset",value:function(){this.xRounder=new Js,this.yRounder=new Js}},{key:"round",value:function(i,s){return{x:this.xRounder.round(i),y:this.yRounder.round(s)}}}]),t}(),Js=function(){function t(){at(this,t),this.tree=new ci,this.round(0)}return J(t,[{key:"round",value:function(i){var s=this.tree.add(i),r=this.tree.prev(s);if(r!==null&&pi(s.key,r.key)===0)return this.tree.remove(i),r.key;var n=this.tree.next(s);return n!==null&&pi(s.key,n.key)===0?(this.tree.remove(i),n.key):i}}]),t}(),de=new Zf,me=function(e,i){return e.x*i.y-e.y*i.x},tr=function(e,i){return e.x*i.x+e.y*i.y},er=function(e,i,s){var r={x:i.x-e.x,y:i.y-e.y},n={x:s.x-e.x,y:s.y-e.y},a=me(r,n);return pi(a,0)},De=function(e){return Math.sqrt(tr(e,e))},Jf=function(e,i,s){var r={x:i.x-e.x,y:i.y-e.y},n={x:s.x-e.x,y:s.y-e.y};return me(n,r)/De(n)/De(r)},tc=function(e,i,s){var r={x:i.x-e.x,y:i.y-e.y},n={x:s.x-e.x,y:s.y-e.y};return tr(n,r)/De(n)/De(r)},ir=function(e,i,s){return i.y===0?null:{x:e.x+i.x/i.y*(s-e.y),y:s}},sr=function(e,i,s){return i.x===0?null:{x:s,y:e.y+i.y/i.x*(s-e.x)}},ec=function(e,i,s,r){if(i.x===0)return sr(s,r,e.x);if(r.x===0)return sr(e,i,s.x);if(i.y===0)return ir(s,r,e.y);if(r.y===0)return ir(e,i,s.y);var n=me(i,r);if(n==0)return null;var a={x:s.x-e.x,y:s.y-e.y},h=me(a,i)/n,o=me(a,r)/n,l=e.x+o*i.x,f=s.x+h*r.x,u=e.y+o*i.y,c=s.y+h*r.y,v=(l+f)/2,d=(u+c)/2;return{x:v,y:d}},yt=function(){J(t,null,[{key:"compare",value:function(i,s){var r=t.comparePoints(i.point,s.point);return r!==0?r:(i.point!==s.point&&i.link(s),i.isLeft!==s.isLeft?i.isLeft?1:-1:ze.compare(i.segment,s.segment))}},{key:"comparePoints",value:function(i,s){return i.x<s.x?-1:i.x>s.x?1:i.y<s.y?-1:i.y>s.y?1:0}}]);function t(e,i){at(this,t),e.events===void 0?e.events=[this]:e.events.push(this),this.point=e,this.isLeft=i}return J(t,[{key:"link",value:function(i){if(i.point===this.point)throw new Error("Tried to link already linked events");for(var s=i.point.events,r=0,n=s.length;r<n;r++){var a=s[r];this.point.events.push(a),a.point=this.point}this.checkForConsuming()}},{key:"checkForConsuming",value:function(){for(var i=this.point.events.length,s=0;s<i;s++){var r=this.point.events[s];if(r.segment.consumedBy===void 0)for(var n=s+1;n<i;n++){var a=this.point.events[n];a.consumedBy===void 0&&r.otherSE.point.events===a.otherSE.point.events&&r.segment.consume(a.segment)}}}},{key:"getAvailableLinkedEvents",value:function(){for(var i=[],s=0,r=this.point.events.length;s<r;s++){var n=this.point.events[s];n!==this&&!n.segment.ringOut&&n.segment.isInResult()&&i.push(n)}return i}},{key:"getLeftmostComparator",value:function(i){var s=this,r=new Map,n=function(h){var o=h.otherSE;r.set(h,{sine:Jf(s.point,i.point,o.point),cosine:tc(s.point,i.point,o.point)})};return function(a,h){r.has(a)||n(a),r.has(h)||n(h);var o=r.get(a),l=o.sine,f=o.cosine,u=r.get(h),c=u.sine,v=u.cosine;return l>=0&&c>=0?f<v?1:f>v?-1:0:l<0&&c<0?f<v?-1:f>v?1:0:c<l?-1:c>l?1:0}}}]),t}(),ic=0,ze=function(){J(t,null,[{key:"compare",value:function(i,s){var r=i.leftSE.point.x,n=s.leftSE.point.x,a=i.rightSE.point.x,h=s.rightSE.point.x;if(h<r)return 1;if(a<n)return-1;var o=i.leftSE.point.y,l=s.leftSE.point.y,f=i.rightSE.point.y,u=s.rightSE.point.y;if(r<n){if(l<o&&l<f)return 1;if(l>o&&l>f)return-1;var c=i.comparePoint(s.leftSE.point);if(c<0)return 1;if(c>0)return-1;var v=s.comparePoint(i.rightSE.point);return v!==0?v:-1}if(r>n){if(o<l&&o<u)return-1;if(o>l&&o>u)return 1;var d=s.comparePoint(i.leftSE.point);if(d!==0)return d;var g=i.comparePoint(s.rightSE.point);return g<0?1:g>0?-1:1}if(o<l)return-1;if(o>l)return 1;if(a<h){var p=s.comparePoint(i.rightSE.point);if(p!==0)return p}if(a>h){var y=i.comparePoint(s.rightSE.point);if(y<0)return 1;if(y>0)return-1}if(a!==h){var x=f-o,M=a-r,_=u-l,S=h-n;if(x>M&&_<S)return 1;if(x<M&&_>S)return-1}return a>h?1:a<h||f<u?-1:f>u?1:i.id<s.id?-1:i.id>s.id?1:0}}]);function t(e,i,s,r){at(this,t),this.id=++ic,this.leftSE=e,e.segment=this,e.otherSE=i,this.rightSE=i,i.segment=this,i.otherSE=e,this.rings=s,this.windings=r}return J(t,[{key:"replaceRightSE",value:function(i){this.rightSE=i,this.rightSE.segment=this,this.rightSE.otherSE=this.leftSE,this.leftSE.otherSE=this.rightSE}},{key:"bbox",value:function(){var i=this.leftSE.point.y,s=this.rightSE.point.y;return{ll:{x:this.leftSE.point.x,y:i<s?i:s},ur:{x:this.rightSE.point.x,y:i>s?i:s}}}},{key:"vector",value:function(){return{x:this.rightSE.point.x-this.leftSE.point.x,y:this.rightSE.point.y-this.leftSE.point.y}}},{key:"isAnEndpoint",value:function(i){return i.x===this.leftSE.point.x&&i.y===this.leftSE.point.y||i.x===this.rightSE.point.x&&i.y===this.rightSE.point.y}},{key:"comparePoint",value:function(i){if(this.isAnEndpoint(i))return 0;var s=this.leftSE.point,r=this.rightSE.point,n=this.vector();if(s.x===r.x)return i.x===s.x?0:i.x<s.x?1:-1;var a=(i.y-s.y)/n.y,h=s.x+a*n.x;if(i.x===h)return 0;var o=(i.x-s.x)/n.x,l=s.y+o*n.y;return i.y===l?0:i.y<l?-1:1}},{key:"getIntersection",value:function(i){var s=this.bbox(),r=i.bbox(),n=gi(s,r);if(n===null)return null;var a=this.leftSE.point,h=this.rightSE.point,o=i.leftSE.point,l=i.rightSE.point,f=ve(s,o)&&this.comparePoint(o)===0,u=ve(r,a)&&i.comparePoint(a)===0,c=ve(s,l)&&this.comparePoint(l)===0,v=ve(r,h)&&i.comparePoint(h)===0;if(u&&f)return v&&!c?h:!v&&c?l:null;if(u)return c&&a.x===l.x&&a.y===l.y?null:a;if(f)return v&&h.x===o.x&&h.y===o.y?null:o;if(v&&c)return null;if(v)return h;if(c)return l;var d=ec(a,this.vector(),o,i.vector());return d===null||!ve(n,d)?null:de.round(d.x,d.y)}},{key:"split",value:function(i){var s=[],r=i.events!==void 0,n=new yt(i,!0),a=new yt(i,!1),h=this.rightSE;this.replaceRightSE(a),s.push(a),s.push(n);var o=new t(n,h,this.rings.slice(),this.windings.slice());return yt.comparePoints(o.leftSE.point,o.rightSE.point)>0&&o.swapEvents(),yt.comparePoints(this.leftSE.point,this.rightSE.point)>0&&this.swapEvents(),r&&(n.checkForConsuming(),a.checkForConsuming()),s}},{key:"swapEvents",value:function(){var i=this.rightSE;this.rightSE=this.leftSE,this.leftSE=i,this.leftSE.isLeft=!0,this.rightSE.isLeft=!1;for(var s=0,r=this.windings.length;s<r;s++)this.windings[s]*=-1}},{key:"consume",value:function(i){for(var s=this,r=i;s.consumedBy;)s=s.consumedBy;for(;r.consumedBy;)r=r.consumedBy;var n=t.compare(s,r);if(n!==0){if(n>0){var a=s;s=r,r=a}if(s.prev===r){var h=s;s=r,r=h}for(var o=0,l=r.rings.length;o<l;o++){var f=r.rings[o],u=r.windings[o],c=s.rings.indexOf(f);c===-1?(s.rings.push(f),s.windings.push(u)):s.windings[c]+=u}r.rings=null,r.windings=null,r.consumedBy=s,r.leftSE.consumedBy=s.leftSE,r.rightSE.consumedBy=s.rightSE}}},{key:"prevInResult",value:function(){return this._prevInResult!==void 0?this._prevInResult:(this.prev?this.prev.isInResult()?this._prevInResult=this.prev:this._prevInResult=this.prev.prevInResult():this._prevInResult=null,this._prevInResult)}},{key:"beforeState",value:function(){if(this._beforeState!==void 0)return this._beforeState;if(!this.prev)this._beforeState={rings:[],windings:[],multiPolys:[]};else{var i=this.prev.consumedBy||this.prev;this._beforeState=i.afterState()}return this._beforeState}},{key:"afterState",value:function(){if(this._afterState!==void 0)return this._afterState;var i=this.beforeState();this._afterState={rings:i.rings.slice(0),windings:i.windings.slice(0),multiPolys:[]};for(var s=this._afterState.rings,r=this._afterState.windings,n=this._afterState.multiPolys,a=0,h=this.rings.length;a<h;a++){var o=this.rings[a],l=this.windings[a],f=s.indexOf(o);f===-1?(s.push(o),r.push(l)):r[f]+=l}for(var u=[],c=[],v=0,d=s.length;v<d;v++)if(r[v]!==0){var g=s[v],p=g.poly;if(c.indexOf(p)===-1)if(g.isExterior)u.push(p);else{c.indexOf(p)===-1&&c.push(p);var y=u.indexOf(g.poly);y!==-1&&u.splice(y,1)}}for(var x=0,M=u.length;x<M;x++){var _=u[x].multiPoly;n.indexOf(_)===-1&&n.push(_)}return this._afterState}},{key:"isInResult",value:function(){if(this.consumedBy)return!1;if(this._isInResult!==void 0)return this._isInResult;var i=this.beforeState().multiPolys,s=this.afterState().multiPolys;switch(ct.type){case"union":{var r=i.length===0,n=s.length===0;this._isInResult=r!==n;break}case"intersection":{var a,h;i.length<s.length?(a=i.length,h=s.length):(a=s.length,h=i.length),this._isInResult=h===ct.numMultiPolys&&a<h;break}case"xor":{var o=Math.abs(i.length-s.length);this._isInResult=o%2===1;break}case"difference":{var l=function(u){return u.length===1&&u[0].isSubject};this._isInResult=l(i)!==l(s);break}default:throw new Error("Unrecognized operation type found ".concat(ct.type))}return this._isInResult}}],[{key:"fromRing",value:function(i,s,r){var n,a,h,o=yt.comparePoints(i,s);if(o<0)n=i,a=s,h=1;else if(o>0)n=s,a=i,h=-1;else throw new Error("Tried to create degenerate segment at [".concat(i.x,", ").concat(i.y,"]"));var l=new yt(n,!0),f=new yt(a,!1);return new t(l,f,[r],[h])}}]),t}(),rr=function(){function t(e,i,s){if(at(this,t),!Array.isArray(e)||e.length===0)throw new Error("Input geometry is not a valid Polygon or MultiPolygon");if(this.poly=i,this.isExterior=s,this.segments=[],typeof e[0][0]!="number"||typeof e[0][1]!="number")throw new Error("Input geometry is not a valid Polygon or MultiPolygon");var r=de.round(e[0][0],e[0][1]);this.bbox={ll:{x:r.x,y:r.y},ur:{x:r.x,y:r.y}};for(var n=r,a=1,h=e.length;a<h;a++){if(typeof e[a][0]!="number"||typeof e[a][1]!="number")throw new Error("Input geometry is not a valid Polygon or MultiPolygon");var o=de.round(e[a][0],e[a][1]);o.x===n.x&&o.y===n.y||(this.segments.push(ze.fromRing(n,o,this)),o.x<this.bbox.ll.x&&(this.bbox.ll.x=o.x),o.y<this.bbox.ll.y&&(this.bbox.ll.y=o.y),o.x>this.bbox.ur.x&&(this.bbox.ur.x=o.x),o.y>this.bbox.ur.y&&(this.bbox.ur.y=o.y),n=o)}(r.x!==n.x||r.y!==n.y)&&this.segments.push(ze.fromRing(n,r,this))}return J(t,[{key:"getSweepEvents",value:function(){for(var i=[],s=0,r=this.segments.length;s<r;s++){var n=this.segments[s];i.push(n.leftSE),i.push(n.rightSE)}return i}}]),t}(),sc=function(){function t(e,i){if(at(this,t),!Array.isArray(e))throw new Error("Input geometry is not a valid Polygon or MultiPolygon");this.exteriorRing=new rr(e[0],this,!0),this.bbox={ll:{x:this.exteriorRing.bbox.ll.x,y:this.exteriorRing.bbox.ll.y},ur:{x:this.exteriorRing.bbox.ur.x,y:this.exteriorRing.bbox.ur.y}},this.interiorRings=[];for(var s=1,r=e.length;s<r;s++){var n=new rr(e[s],this,!1);n.bbox.ll.x<this.bbox.ll.x&&(this.bbox.ll.x=n.bbox.ll.x),n.bbox.ll.y<this.bbox.ll.y&&(this.bbox.ll.y=n.bbox.ll.y),n.bbox.ur.x>this.bbox.ur.x&&(this.bbox.ur.x=n.bbox.ur.x),n.bbox.ur.y>this.bbox.ur.y&&(this.bbox.ur.y=n.bbox.ur.y),this.interiorRings.push(n)}this.multiPoly=i}return J(t,[{key:"getSweepEvents",value:function(){for(var i=this.exteriorRing.getSweepEvents(),s=0,r=this.interiorRings.length;s<r;s++)for(var n=this.interiorRings[s].getSweepEvents(),a=0,h=n.length;a<h;a++)i.push(n[a]);return i}}]),t}(),nr=function(){function t(e,i){if(at(this,t),!Array.isArray(e))throw new Error("Input geometry is not a valid Polygon or MultiPolygon");try{typeof e[0][0][0]=="number"&&(e=[e])}catch{}this.polys=[],this.bbox={ll:{x:Number.POSITIVE_INFINITY,y:Number.POSITIVE_INFINITY},ur:{x:Number.NEGATIVE_INFINITY,y:Number.NEGATIVE_INFINITY}};for(var s=0,r=e.length;s<r;s++){var n=new sc(e[s],this);n.bbox.ll.x<this.bbox.ll.x&&(this.bbox.ll.x=n.bbox.ll.x),n.bbox.ll.y<this.bbox.ll.y&&(this.bbox.ll.y=n.bbox.ll.y),n.bbox.ur.x>this.bbox.ur.x&&(this.bbox.ur.x=n.bbox.ur.x),n.bbox.ur.y>this.bbox.ur.y&&(this.bbox.ur.y=n.bbox.ur.y),this.polys.push(n)}this.isSubject=i}return J(t,[{key:"getSweepEvents",value:function(){for(var i=[],s=0,r=this.polys.length;s<r;s++)for(var n=this.polys[s].getSweepEvents(),a=0,h=n.length;a<h;a++)i.push(n[a]);return i}}]),t}(),rc=function(){J(t,null,[{key:"factory",value:function(i){for(var s=[],r=0,n=i.length;r<n;r++){var a=i[r];if(!(!a.isInResult()||a.ringOut)){for(var h=null,o=a.leftSE,l=a.rightSE,f=[o],u=o.point,c=[];h=o,o=l,f.push(o),o.point!==u;)for(;;){var v=o.getAvailableLinkedEvents();if(v.length===0){var d=f[0].point,g=f[f.length-1].point;throw new Error("Unable to complete output ring starting at [".concat(d.x,",")+" ".concat(d.y,"]. Last matching segment found ends at")+" [".concat(g.x,", ").concat(g.y,"]."))}if(v.length===1){l=v[0].otherSE;break}for(var p=null,y=0,x=c.length;y<x;y++)if(c[y].point===o.point){p=y;break}if(p!==null){var M=c.splice(p)[0],_=f.splice(M.index);_.unshift(_[0].otherSE),s.push(new t(_.reverse()));continue}c.push({index:f.length,point:o.point});var S=o.getLeftmostComparator(h);l=v.sort(S)[0].otherSE;break}s.push(new t(f))}}return s}}]);function t(e){at(this,t),this.events=e;for(var i=0,s=e.length;i<s;i++)e[i].segment.ringOut=this;this.poly=null}return J(t,[{key:"getGeom",value:function(){for(var i=this.events[0].point,s=[i],r=1,n=this.events.length-1;r<n;r++){var a=this.events[r].point,h=this.events[r+1].point;er(a,i,h)!==0&&(s.push(a),i=a)}if(s.length===1)return null;var o=s[0],l=s[1];er(o,i,l)===0&&s.shift(),s.push(s[0]);for(var f=this.isExteriorRing()?1:-1,u=this.isExteriorRing()?0:s.length-1,c=this.isExteriorRing()?s.length:-1,v=[],d=u;d!=c;d+=f)v.push([s[d].x,s[d].y]);return v}},{key:"isExteriorRing",value:function(){if(this._isExteriorRing===void 0){var i=this.enclosingRing();this._isExteriorRing=i?!i.isExteriorRing():!0}return this._isExteriorRing}},{key:"enclosingRing",value:function(){return this._enclosingRing===void 0&&(this._enclosingRing=this._calcEnclosingRing()),this._enclosingRing}},{key:"_calcEnclosingRing",value:function(){for(var i=this.events[0],s=1,r=this.events.length;s<r;s++){var n=this.events[s];yt.compare(i,n)>0&&(i=n)}for(var a=i.segment.prevInResult(),h=a?a.prevInResult():null;;){if(!a)return null;if(!h)return a.ringOut;if(h.ringOut!==a.ringOut)return h.ringOut.enclosingRing()!==a.ringOut?a.ringOut:a.ringOut.enclosingRing();a=h.prevInResult(),h=a?a.prevInResult():null}}}]),t}(),ar=function(){function t(e){at(this,t),this.exteriorRing=e,e.poly=this,this.interiorRings=[]}return J(t,[{key:"addInterior",value:function(i){this.interiorRings.push(i),i.poly=this}},{key:"getGeom",value:function(){var i=[this.exteriorRing.getGeom()];if(i[0]===null)return null;for(var s=0,r=this.interiorRings.length;s<r;s++){var n=this.interiorRings[s].getGeom();n!==null&&i.push(n)}return i}}]),t}(),nc=function(){function t(e){at(this,t),this.rings=e,this.polys=this._composePolys(e)}return J(t,[{key:"getGeom",value:function(){for(var i=[],s=0,r=this.polys.length;s<r;s++){var n=this.polys[s].getGeom();n!==null&&i.push(n)}return i}},{key:"_composePolys",value:function(i){for(var s=[],r=0,n=i.length;r<n;r++){var a=i[r];if(!a.poly)if(a.isExteriorRing())s.push(new ar(a));else{var h=a.enclosingRing();h.poly||s.push(new ar(h)),h.poly.addInterior(a)}}return s}}]),t}(),ac=function(){function t(e){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:ze.compare;at(this,t),this.queue=e,this.tree=new ci(i),this.segments=[]}return J(t,[{key:"process",value:function(i){var s=i.segment,r=[];if(i.consumedBy)return i.isLeft?this.queue.remove(i.otherSE):this.tree.remove(s),r;var n=i.isLeft?this.tree.insert(s):this.tree.find(s);if(!n)throw new Error("Unable to find segment #".concat(s.id," ")+"[".concat(s.leftSE.point.x,", ").concat(s.leftSE.point.y,"] -> ")+"[".concat(s.rightSE.point.x,", ").concat(s.rightSE.point.y,"] ")+"in SweepLine tree. Please submit a bug report.");for(var a=n,h=n,o=void 0,l=void 0;o===void 0;)a=this.tree.prev(a),a===null?o=null:a.key.consumedBy===void 0&&(o=a.key);for(;l===void 0;)h=this.tree.next(h),h===null?l=null:h.key.consumedBy===void 0&&(l=h.key);if(i.isLeft){var f=null;if(o){var u=o.getIntersection(s);if(u!==null&&(s.isAnEndpoint(u)||(f=u),!o.isAnEndpoint(u)))for(var c=this._splitSafely(o,u),v=0,d=c.length;v<d;v++)r.push(c[v])}var g=null;if(l){var p=l.getIntersection(s);if(p!==null&&(s.isAnEndpoint(p)||(g=p),!l.isAnEndpoint(p)))for(var y=this._splitSafely(l,p),x=0,M=y.length;x<M;x++)r.push(y[x])}if(f!==null||g!==null){var _=null;if(f===null)_=g;else if(g===null)_=f;else{var S=yt.comparePoints(f,g);_=S<=0?f:g}this.queue.remove(s.rightSE),r.push(s.rightSE);for(var b=s.split(_),L=0,A=b.length;L<A;L++)r.push(b[L])}r.length>0?(this.tree.remove(s),r.push(i)):(this.segments.push(s),s.prev=o)}else{if(o&&l){var I=o.getIntersection(l);if(I!==null){if(!o.isAnEndpoint(I))for(var C=this._splitSafely(o,I),O=0,F=C.length;O<F;O++)r.push(C[O]);if(!l.isAnEndpoint(I))for(var V=this._splitSafely(l,I),P=0,vt=V.length;P<vt;P++)r.push(V[P])}}this.tree.remove(s)}return r}},{key:"_splitSafely",value:function(i,s){this.tree.remove(i);var r=i.rightSE;this.queue.remove(r);var n=i.split(s);return n.push(r),i.consumedBy===void 0&&this.tree.insert(i),n}}]),t}(),hr=typeof process<"u"&&{}.POLYGON_CLIPPING_MAX_QUEUE_SIZE||1e6,hc=typeof process<"u"&&{}.POLYGON_CLIPPING_MAX_SWEEPLINE_SEGMENTS||1e6,oc=function(){function t(){at(this,t)}return J(t,[{key:"run",value:function(i,s,r){ct.type=i,de.reset();for(var n=[new nr(s,!0)],a=0,h=r.length;a<h;a++)n.push(new nr(r[a],!1));if(ct.numMultiPolys=n.length,ct.type==="difference")for(var o=n[0],l=1;l<n.length;)gi(n[l].bbox,o.bbox)!==null?l++:n.splice(l,1);if(ct.type==="intersection"){for(var f=0,u=n.length;f<u;f++)for(var c=n[f],v=f+1,d=n.length;v<d;v++)if(gi(c.bbox,n[v].bbox)===null)return[]}for(var g=new ci(yt.compare),p=0,y=n.length;p<y;p++)for(var x=n[p].getSweepEvents(),M=0,_=x.length;M<_;M++)if(g.insert(x[M]),g.size>hr)throw new Error("Infinite loop when putting segment endpoints in a priority queue (queue size too big). Please file a bug report.");for(var S=new ac(g),b=g.size,L=g.pop();L;){var A=L.key;if(g.size===b){var I=A.segment;throw new Error("Unable to pop() ".concat(A.isLeft?"left":"right"," SweepEvent ")+"[".concat(A.point.x,", ").concat(A.point.y,"] from segment #").concat(I.id," ")+"[".concat(I.leftSE.point.x,", ").concat(I.leftSE.point.y,"] -> ")+"[".concat(I.rightSE.point.x,", ").concat(I.rightSE.point.y,"] from queue. ")+"Please file a bug report.")}if(g.size>hr)throw new Error("Infinite loop when passing sweep line over endpoints (queue size too big). Please file a bug report.");if(S.segments.length>hc)throw new Error("Infinite loop when passing sweep line over endpoints (too many sweep line segments). Please file a bug report.");for(var C=S.process(A),O=0,F=C.length;O<F;O++){var V=C[O];V.consumedBy===void 0&&g.insert(V)}b=g.size,L=g.pop()}de.reset();var P=rc.factory(S.segments),vt=new nc(P);return vt.getGeom()}}]),t}(),ct=new oc,lc=function(e){for(var i=arguments.length,s=new Array(i>1?i-1:0),r=1;r<i;r++)s[r-1]=arguments[r];return ct.run("union",e,s)},uc=function(e){for(var i=arguments.length,s=new Array(i>1?i-1:0),r=1;r<i;r++)s[r-1]=arguments[r];return ct.run("intersection",e,s)},fc=function(e){for(var i=arguments.length,s=new Array(i>1?i-1:0),r=1;r<i;r++)s[r-1]=arguments[r];return ct.run("xor",e,s)},cc=function(e){for(var i=arguments.length,s=new Array(i>1?i-1:0),r=1;r<i;r++)s[r-1]=arguments[r];return ct.run("difference",e,s)},vc={union:lc,intersection:uc,xor:fc,difference:cc};const dc=vc;function mc(t,e,i){i===void 0&&(i={});var s=Ks(t),r=Ks(e),n=dc.intersection(s.coordinates,r.coordinates);return n.length===0?null:n.length===1?Ff(n[0],i.properties):Gf(n,i.properties)}var or={exports:{}};(or.exports=function(){this.events={}}).prototype={emit:function(e){var i=[].slice.call(arguments,1);[].slice.call(this.events[e]||[]).filter(function(s){s.apply(null,i)})},on:function(e,i){if({}.NODE_ENV!=="production"&&typeof i!="function")throw new Error("Listener must be a function");return(this.events[e]=this.events[e]||[]).push(i),(function(){this.events[e]=this.events[e].filter(function(s){return s!==i})}).bind(this)}};var gc=or.exports;const pc=Mi.getDefaultExportFromCjs(gc);function yc(t){t.events={}}var Mc=yc;const xc=Mi.getDefaultExportFromCjs(Mc);function lr(t,e,i){if(t!==null)for(var s,r,n,a,h,o,l,f=0,u=0,c,v=t.type,d=v==="FeatureCollection",g=v==="Feature",p=d?t.features.length:1,y=0;y<p;y++){l=d?t.features[y].geometry:g?t.geometry:t,c=l?l.type==="GeometryCollection":!1,h=c?l.geometries.length:1;for(var x=0;x<h;x++){var M=0,_=0;if(a=c?l.geometries[x]:l,a!==null){o=a.coordinates;var S=a.type;switch(f=i&&(S==="Polygon"||S==="MultiPolygon")?1:0,S){case null:break;case"Point":if(e(o,u,y,M,_)===!1)return!1;u++,M++;break;case"LineString":case"MultiPoint":for(s=0;s<o.length;s++){if(e(o[s],u,y,M,_)===!1)return!1;u++,S==="MultiPoint"&&M++}S==="LineString"&&M++;break;case"Polygon":case"MultiLineString":for(s=0;s<o.length;s++){for(r=0;r<o[s].length-f;r++){if(e(o[s][r],u,y,M,_)===!1)return!1;u++}S==="MultiLineString"&&M++,S==="Polygon"&&_++}S==="Polygon"&&M++;break;case"MultiPolygon":for(s=0;s<o.length;s++){for(_=0,r=0;r<o[s].length;r++){for(n=0;n<o[s][r].length-f;n++){if(e(o[s][r][n],u,y,M,_)===!1)return!1;u++}_++}M++}break;case"GeometryCollection":for(s=0;s<a.geometries.length;s++)if(lr(a.geometries[s],e,i)===!1)return!1;break;default:throw new Error("Unknown Geometry Type")}}}}}function _c(t){if(!t)throw new Error("geojson is required");switch(t.type){case"Feature":return ur(t);case"FeatureCollection":return Ec(t);case"Point":case"LineString":case"Polygon":case"MultiPoint":case"MultiLineString":case"MultiPolygon":case"GeometryCollection":return yi(t);default:throw new Error("unknown GeoJSON type")}}function ur(t){var e={type:"Feature"};return Object.keys(t).forEach(function(i){switch(i){case"type":case"properties":case"geometry":return;default:e[i]=t[i]}}),e.properties=fr(t.properties),e.geometry=yi(t.geometry),e}function fr(t){var e={};return t&&Object.keys(t).forEach(function(i){var s=t[i];typeof s=="object"?s===null?e[i]=null:Array.isArray(s)?e[i]=s.map(function(r){return r}):e[i]=fr(s):e[i]=s}),e}function Ec(t){var e={type:"FeatureCollection"};return Object.keys(t).forEach(function(i){switch(i){case"type":case"features":return;default:e[i]=t[i]}}),e.features=t.features.map(function(i){return ur(i)}),e}function yi(t){var e={type:t.type};return t.bbox&&(e.bbox=t.bbox),t.type==="GeometryCollection"?(e.geometries=t.geometries.map(function(i){return yi(i)}),e):(e.coordinates=cr(t.coordinates),e)}function cr(t){var e=t;return typeof e[0]!="object"?e.slice():e.map(function(i){return cr(i)})}function Sc(t,e){return e===void 0&&(e={}),bc(t,"wgs84",e)}function bc(t,e,i){i===void 0&&(i={}),i=i||{};var s=i.mutate;if(!t)throw new Error("geojson is required");return Array.isArray(t)&&qf(t[0])?t=e==="mercator"?vr(t):dr(t):(s!==!0&&(t=_c(t)),lr(t,function(r){var n=e==="mercator"?vr(r):dr(r);r[0]=n[0],r[1]=n[1]})),t}function vr(t){var e=Math.PI/180,i=6378137,s=20037508342789244e-9,r=Math.abs(t[0])<=180?t[0]:t[0]-wc(t[0])*360,n=[i*r*e,i*Math.log(Math.tan(Math.PI*.25+.5*t[1]*e))];return n[0]>s&&(n[0]=s),n[0]<-s&&(n[0]=-s),n[1]>s&&(n[1]=s),n[1]<-s&&(n[1]=-s),n}function dr(t){var e=180/Math.PI,i=6378137;return[t[0]*e/i,(Math.PI*.5-2*Math.atan(Math.exp(-t[1]/i)))*e]}function wc(t){return t<0?-1:t>0?1:0}G.BloomEffect=Gn,G.Effect=Yt,G.EffectComposer=sn,G.EffectPass=jr,G.Ellipsoid=li,G.FXAAEffect=Vn,G.KernelSize=ye,G.LUT3DEffect=Qn,G.LUT3dlLoader=Yn,G.MeshLine=Mt,G.MeshLineMaterial=Ii,G.NanoEvents=pc,G.Pass=ot,G.RenderPass=Xr,G.Selection=nn,G.Vector2=da,G.Vector3=z,G.VignetteEffect=Kn,G.distance=Vf,G.eventsExports=Sr,G.intersect=mc,G.proj4=rt,G.toWgs84=Sc,G.unbindAllEvents=xc});
