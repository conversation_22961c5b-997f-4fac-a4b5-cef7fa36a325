(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6a85a773"],{"0538":function(e,t,n){"use strict";var o=n("e330"),i=n("59ed"),s=n("861d"),a=n("1a2d"),c=n("f36a"),l=n("40d5"),r=Function,d=o([].concat),u=o([].join),m={},h=function(e,t,n){if(!a(m,t)){for(var o=[],i=0;i<t;i++)o[i]="a["+i+"]";m[t]=r("C,a","return new C("+u(o,",")+")")}return m[t](e,n)};e.exports=l?r.bind:function(e){var t=i(this),n=t.prototype,o=c(arguments,1),a=function(){var n=d(o,c(arguments));return this instanceof a?h(t,n.length,n):t.apply(e,n)};return s(n)&&(a.prototype=n),a}},4478:function(e,t,n){"use strict";n.d(t,"a",(function(){return s}));n("4ae1"),n("d3b7"),n("f8c9");function o(e,t){return o=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},o(e,t)}function i(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}function s(e,t,n){return s=i()?Reflect.construct.bind():function(e,t,n){var i=[null];i.push.apply(i,t);var s=Function.bind.apply(e,i),a=new s;return n&&o(a,n.prototype),a},s.apply(null,arguments)}},"4ae1":function(e,t,n){var o=n("23e7"),i=n("d066"),s=n("2ba4"),a=n("0538"),c=n("5087"),l=n("825a"),r=n("861d"),d=n("7c73"),u=n("d039"),m=i("Reflect","construct"),h=Object.prototype,p=[].push,f=u((function(){function e(){}return!(m((function(){}),[],e)instanceof e)})),w=!u((function(){m((function(){}))})),v=f||w;o({target:"Reflect",stat:!0,forced:v,sham:v},{construct:function(e,t){c(e),l(t);var n=arguments.length<3?e:c(arguments[2]);if(w&&!f)return m(e,t,n);if(e==n){switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3])}var o=[null];return s(p,o,t),new(s(a,e,o))}var i=n.prototype,u=d(r(i)?i:h),v=s(e,u,t);return r(v)?v:u}})},a9d4:function(e,t,n){"use strict";n("ddac")},bd1e:function(e,t,n){"use strict";n("fd818")},ddac:function(e,t,n){},e709:function(e,t,n){"use strict";n.r(t);var o=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"bottom-menu-container",class:{"first-story":"model"===this.elementMenu}},[e.sectionTypeObj.show?[e._l(e.sectionTypeMenu,(function(t){return n("div",{staticClass:"menu-item cursor-btn",class:{active:e.sectionTypeObj.type===t.label},on:{click:function(n){return e.toggleSection(t)}}},[n("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:t.name,placement:"top"}},[n("span",[n("CommonSVG",{attrs:{color:"#FFFFFF","icon-class":t.icon,size:t.size}})],1)])],1)})),n("div",{staticClass:"vertical-split-line"})]:e._e(),e._l(e.menuList,(function(t,o){return[t.show?n("div",{key:t.label,staticClass:"menu-item cursor-btn",class:{active:0===o&&e.boxShow||1===o&&e.boxTransForm,disabled:t.disabled},on:{click:function(t){return e.toggleMenu(o)}}},[n("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:t.name,disabled:t.disabled,placement:"top"}},[n("span",[n("CommonSVG",{attrs:{color:"#FFFFFF","icon-class":t.icon,size:16}})],1)])],1):e._e(),"rotate"===t.label?[n("transition",{attrs:{name:"sliderFade"}},[n("div",{directives:[{name:"show",rawName:"v-show",value:e.boxTransForm,expression:"boxTransForm"}],staticClass:"item-slider"},[n("div",{staticClass:"transform-content"},[n("el-row",{staticClass:"items-center",attrs:{gutter:10}},[n("el-col",{attrs:{span:12}},[n("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:e.rotate[0],expression:"rotate[0]"}],attrs:{type:"number",title:"",size:"mini",placeholder:"X"},on:{input:function(t){return e.inputChangeRotation(t,"x")}},model:{value:e.rotate[0],callback:function(t){e.$set(e.rotate,0,t)},expression:"rotate[0]"}},[n("template",{slot:"append"},[e._v("X")])],2)],1),n("el-col",{attrs:{span:12}},[n("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:e.rotate[1],expression:"rotate[1]"}],attrs:{type:"number",title:"",size:"mini",placeholder:"Y"},on:{input:function(t){return e.inputChangeRotation(t,"y")}},model:{value:e.rotate[1],callback:function(t){e.$set(e.rotate,1,t)},expression:"rotate[1]"}},[n("template",{slot:"append"},[e._v("Y")])],2)],1),n("el-col",{attrs:{span:12}},[n("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:e.rotate[2],expression:"rotate[2]"}],attrs:{type:"number",title:"",size:"mini",placeholder:"Z"},on:{input:function(t){return e.inputChangeRotation(t,"z")}},model:{value:e.rotate[2],callback:function(t){e.$set(e.rotate,2,t)},expression:"rotate[2]"}},[n("template",{slot:"append"},[e._v("Z")])],2)],1)],1)],1)])])]:e._e()]})),e.sectionDialog?n("dialogComp",{attrs:{hideHeader:!1,needClose:!1,needSelectAll:!0,backgroundColor:"var(--primary-bg-color)",zIndex:"5",draw:!0,left:348,drag:!0,title:e.$t("bottomMenu.section.label"),icon:"icon-details",width:273,height:280,type:"detailInfo",position:"fixed",bottom:60},on:{close:e.closeDialog,selectAll:e.selectAll},scopedSlots:e._u([{key:"center",fn:function(){return[n("div",{staticClass:"content"},[e.dialogList.length?n("div",{staticClass:"list-content"},e._l(e.dialogList,(function(t){return n("div",{key:t.id,staticClass:"item",class:{active:e.selectedList.includes(t.id)},on:{click:function(n){return e.selectItem(t)}}},[n("CommonSVG",{attrs:{"icon-class":"model_element",size:16}}),e._v(" "+e._s(t.name)+" ")],1)})),0):e._e(),0===e.dialogList.length?n("div",{staticClass:"no-data"},[e._v(" "+e._s(e.$t("others.emptyData"))+" ")]):e._e(),n("div",{staticClass:"btn-box"},[n("span",{staticClass:"btn submit",on:{click:function(t){return e.submit()}}},[e._v(" "+e._s(e.$t("menuIconName.confirm"))+" ")])])])]},proxy:!0}],null,!1,2298667582)}):e._e()],2)},i=[],s=n("4478"),a=n("2909"),c=n("ade3"),l=(n("d3b7"),n("3ca3"),n("ddb0"),n("159b"),n("99af"),n("b0c0"),n("a434"),{name:"SectionMenu",components:{CommonSVG:function(){return n.e("chunk-51f90655").then(n.bind(null,"17d0"))}},props:["compKey"],data:function(){return{menuList:[{name:this.$t("menuIconName.hide"),label:"hide",icon:"hide_box_feature",show:!0,disabled:!1},{name:this.$t("menuIconName.rotate"),label:"rotate",icon:"rotate",show:!0,disabled:!1},{name:this.$t("menuIconName.reset"),label:"remove-active",icon:"clear_feature",show:!0,disabled:!1},{name:this.$t("menuIconName.exit"),label:"quit",icon:"quit",show:!0,disabled:!1}],activeMenu:"",positionVisible:!1,dialogList:[],selectedList:[],planeData:[],boxShow:!1,boxTransForm:!1,rotate:[0,0,0],sectionTypeMenu:[{name:this.$t("bottomMenu.section.label1"),label:"box",icon:"tb_sectioning_active",show:!0,active:!0,size:16},{name:this.$t("bottomMenu.section.label2"),label:"plan",icon:"view_icon_section",show:!0,active:!1,size:20}],sectionTypeObj:{type:"box",show:!1}}},computed:{sectionValue:function(){return this.$store.state.menuList.sectionValue},sectionSelectData:function(){return this.$store.state.menuList.sectionSelectData},sectionDialog:function(){return this.$store.state.menuList.sectionDialog},sectionRandom:function(){return this.$store.state.menuList.sectionRandom},selectedSectionData:function(){return this.$store.state.menuList.selectedSectionData},elementMenu:function(){return this.$store.state.menuList.elementMenu},planeSelectionId:function(){return this.$store.state.menuList.planeSelectionId}},created:function(){this.getDialogData()},watch:{sectionRandom:{deep:!0,handler:function(){this.getDialogData()}},compKey:{deep:!0,handler:function(){"model"===this.compKey.type&&this.getDialogData()}}},methods:{toggleSection:function(e){this.sectionTypeObj.type!==e.label&&(this.sectionTypeObj.type=e.label,this.handleReset(),"box"==this.sectionTypeObj.type?(this.menuList[1].show=!0,this.handleActiveBox()):(this.menuList[1].show=!1,this.handleActivePlan()))},handleActiveBox:function(){var e=window.scene.mv._THREE,t=this.sectionSelectData,n=null,o=[];t.forEach((function(t){var i=window.scene.features.get(t),s=i.AABBWorld.realMin,a=i.AABBWorld.realMax,c=[],l=[];if(c[0]=Math.min(s.x,a.x),c[1]=Math.min(s.y,a.y),c[2]=Math.min(s.z,a.z),l[0]=Math.max(s.x,a.x),l[1]=Math.max(s.y,a.y),l[2]=Math.max(s.z,a.z),s.set(c[0],c[1],c[2]),a.set(l[0],l[1],l[2]),n){var r=new e.Box3(s,a);n.union(r)}else n=new e.Box3(s,a);o=o.concat(i)})),window.scene.mv.tools.clip.features=o,window.scene.mv.tools.clip.activeBox(n)},handleActivePlan:function(){var e=window.scene.mv._THREE,t=this.sectionSelectData,n=null,o=[];t.forEach((function(t){var i=window.scene.features.get(t),s=i.AABBWorld.realMin,a=i.AABBWorld.realMax,c=[],l=[];if(c[0]=Math.min(s.x,a.x),c[1]=Math.min(s.y,a.y),c[2]=Math.min(s.z,a.z),l[0]=Math.max(s.x,a.x),l[1]=Math.max(s.y,a.y),l[2]=Math.max(s.z,a.z),s.set(c[0],c[1],c[2]),a.set(l[0],l[1],l[2]),n){var r=new e.Box3(s,a);n.union(r)}else n=new e.Box3(s,a);o=o.concat(i)}));var i=new e.Vector3(n.min.x,n.max.y,n.max.z),s=new e.Vector3(n.max.x,n.max.y,n.max.z),a=new e.Vector3(n.max.x,n.min.y,n.max.z),c=new e.Vector3(n.min.x,n.min.y,n.max.z);window.scene.mv.tools.clip.features=o;var l=[i,s,a,c];window.scene.mv.tools.clip.activePlane(l)},getDialogData:function(){var e=this;this.dialogList=[],this.$store.commit("toogleSectionDialog",!0);var t=window.scene.features;if(t.forEach((function(t){if("model"===t.type&&t.visible){var n=t.activedViews;n.forEach((function(e){"2D"===e.type&&e.visible&&e.id})),e.dialogList.push({id:t.id,viewsId:e.planeSelectionId,name:t.name,parentID:t.id})}})),"plane"===this.sectionValue){this.menuList[1].show=!1;var n=window.scene.features;n.forEach((function(t){if("model"===t.type&&t.visible){var n=t.activedViews;n.forEach((function(n){"2D"===n.type&&n.visible&&e.selectedSectionData[t.id]&&-1!=e.selectedSectionData[t.id].indexOf(n.id)&&(e.selectedList.push(t.id),"box"!==e.sectionValue&&e.planeData.push({parentID:t.id,id:n.id,viewsId:e.planeSelectionId,name:t.name}))}))}}))}else this.selectedSectionData.length&&this.selectedSectionData.forEach((function(t){e.selectedList.push(t)}))},onClipTransformChange:function(e,t){var n=window.scene.mv.tools.clip.boxRotation,o=t.direction,i=t.angle;switch(o){case"x":n[0]=i;break;case"y":n[1]=i;break;case"z":n[2]=i;break}window.scene.mv.tools.clip.boxRotation=n,this.rotate=n,this.$forceUpdate()},toggleMenu:function(e){var t=this;if(3==e||window.scene.mv.tools.clip._boxData||window.scene.mv.tools.clip._planeData){switch(e){case 0:var n=window.scene.mv.tools.clip.show;this.boxShow=!!n,window.scene.mv.tools.clip.show=!n,this.menuList[1].disabled=!!n,this.$nextTick((function(){t.rotate=[0,0,0],t.boxTransForm=!1,window.scene.mv.events.transformChange.off("default",t.onClipTransformChange),window.scene.mv.tools.clip.boxDragEnable=!n;var e=setTimeout((function(){clearTimeout(e),window.scene.mv.tools.transform.deactive(),window.scene.render()}),500)}));break;case 1:if(this.menuList[1].disabled)return;if(this.boxTransForm=!this.boxTransForm,this.boxTransForm){this.rotate=window.scene.mv.tools.clip.boxRotation,window.scene.mv.tools.transform.currentMode="rotate";var o=window.scene.mv.tools.clip.box.boxCenter,i=scene.mv.tools.coordinate.realPosition(o),s=window.scene.mv.tools.clip._boxData.max.z+15;window.scene.mv.tools.transform.active([i.x,i.y,s],10),window.scene.mv.events.transformChange.on("default",this.onClipTransformChange),window.scene.mv.tools.clip.boxDragEnable=!1,window.scene.render()}else window.scene.mv.tools.clip.boxDragEnable=!0,window.scene.mv.tools.transform.deactive(),window.scene.mv.events.transformChange.off("default",this.onClipTransformChange),window.scene.render();break;case 2:this.submit();break;case 3:this.$store.commit("toogleSectionLoaded",!1),window.scene.mv.tools.clip.dispose(),this.$store.commit("toggleBottomMenuActive",""),this.$store.commit("setSectionSelectData",[]),this.$store.commit("setSelectedSectionData",{}),this.$store.commit("setPlaneSelectionId","");break}e<this.menuList.length&&(this.activeMenu=e)}else this.$message(this.$t("bottomMenu.section.message"))},handleReset:function(){window.scene.mv.tools.clip.dispose(),this.boxShow=!1,this.menuList[1].disabled=!1,window.scene.mv.tools.clip.show=!0,this.boxTransForm&&(this.rotate=[0,0,0],this.boxTransForm=!1,window.scene.mv.events.transformChange.off("default",this.onClipTransformChange),window.scene.mv.tools.clip.boxDragEnable=!0,window.scene.mv.tools.transform.deactive(),window.scene.render())},selectItem:function(e){var t=e.id;if(-1===this.selectedList.indexOf(t))this.selectedList.push(t),"box"!==this.sectionValue&&this.planeData.push(e);else{var n=this.selectedList.indexOf(t);if(this.selectedList.splice(n,1),"box"!==this.sectionValue){this.planeData.splice(n,1);var o=Object.assign(this.selectedSectionData,Object(c["a"])({},e.parentID,null));this.$store.commit("setSelectedSectionData",o)}}},selectAll:function(){var e=this;this.selectedList.length!==this.dialogList.length?this.dialogList.forEach((function(t){-1===e.selectedList.indexOf(t.id)&&(e.selectedList.push(t.id),"box"!==e.sectionValue&&e.planeData.push(t))})):(this.selectedList=[],this.planeData=[])},submit:function(){if(this.dialogList.length&&0===this.selectedList.length)this.$message(this.$t("bottomMenu.section.message1"));else if(0!==this.dialogList.length){this.handleReset(),this.$store.commit("setSectionSelectData",this.selectedList);var e=window.scene.mv._THREE;if("box"===this.sectionValue)this.sectionTypeObj.show=!0,"box"==this.sectionTypeObj.type?this.handleActiveBox():this.handleActivePlan();else{var t=[],n=null,o=null,i=null,c=null,l=(new e.Matrix4).makeRotationAxis(new e.Vector3(1,0,0),-Math.PI/2);this.planeData.forEach((function(r){var d=window.scene.features.get(r.parentID).views.get(r.viewsId)||{},u=scene.features.get(r.parentID);if(t=t.concat(u),d&&d.override){n=Object(s["a"])(e.Vector3,Object(a["a"])(d.override.BoundingBox3dLeftDown)),o=Object(s["a"])(e.Vector3,Object(a["a"])(d.override.BoundingBox3dLeftUp)),i=Object(s["a"])(e.Vector3,Object(a["a"])(d.override.BoundingBox3dRightDown)),c=Object(s["a"])(e.Vector3,Object(a["a"])(d.override.BoundingBox3dRightUp));var m=d.model._bpt;n.applyMatrix4(l).applyMatrix4(m),o.applyMatrix4(l).applyMatrix4(m),i.applyMatrix4(l).applyMatrix4(m),c.applyMatrix4(l).applyMatrix4(m),n=window.scene.mv.tools.coordinate.realPosition(n),o=window.scene.mv.tools.coordinate.realPosition(o),i=window.scene.mv.tools.coordinate.realPosition(i),c=window.scene.mv.tools.coordinate.realPosition(c)}})),window.scene.mv.tools.clip.features=t;var r=[n,o,i,c];window.scene.mv.tools.clip.activePlane(r)}}else this.closeDialog()},inputChangeRotation:function(e,t){var n=window.scene.mv.tools.clip.boxRotation;switch(t){case"x":n[0]=e;break;case"y":n[1]=e;break;case"z":n[2]=e;break}window.scene.mv.tools.clip.boxRotation=n},closeDialog:function(){this.$store.commit("toogleSectionDialog",!1)}},beforeDestroy:function(){this.$store.commit("toogleSectionLoaded",!1),window.scene.mv.tools.clip.dispose(),this.$store.commit("setSectionSelectData",[]),this.boxTransForm&&(this.rotate=[0,0,0],this.boxTransForm=!1,window.scene.mv.tools.clip.boxDragEnable=!0,window.scene.mv.events.transformChange.off("default",this.onClipTransformChange),window.scene.mv.tools.transform.deactive(),window.scene.render())}}),r=l,d=(n("a9d4"),n("bd1e"),n("2877")),u=Object(d["a"])(r,o,i,!1,null,"e810b71c",null);t["default"]=u.exports},fd818:function(e,t,n){}}]);