(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-00e7bce4"],{"029c":function(e,t,i){"use strict";i("0a32")},"0a32":function(e,t,i){},"688b":function(e,t,i){},b733:function(e,t,i){"use strict";i("688b")},c7fe:function(e,t,i){"use strict";i.r(t);var s=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("dialogComp",{staticClass:"sourceDom",attrs:{hideHeader:!1,needClose:"true",zIndex:"4",position:"fixed",draw:!1,top:0,right:0,bottom:0,left:0,drag:!1,title:e.$t("dialog.choiceSet.label3"),width:400,height:150,type:"detailInfo"},on:{close:e.closeAddDialog},scopedSlots:e._u([{key:"center",fn:function(){return[i("div",{staticClass:"add-container"},[i("el-input",{class:{"is-error":e.addFormDialog.inputError},attrs:{size:"small",placeholder:e.$t("dialog.choiceSet.placeholder")},model:{value:e.addFormDialog.name,callback:function(t){e.$set(e.addFormDialog,"name",t)},expression:"addFormDialog.name"}}),i("div",{staticClass:"bottom-btn"},[i("span",{staticClass:"cursor-btn cancel margin-right-8",on:{click:e.closeAddDialog}},[e._v(" "+e._s(e.$t("messageTips.cancel"))+" ")]),i("span",{staticClass:"cursor-btn confirm",on:{click:e.saveData}},[e._v(" "+e._s(e.$t("menuIconName.confirm"))+" ")])])],1)]},proxy:!0}])})},o=[],a=(i("b0c0"),i("d3b7"),i("159b"),i("e9c4"),{name:"",props:["currentModifyItem"],data:function(){return{addFormDialog:{menuState:!1,dialogState:!1,name:"",inputError:!1}}},created:function(){this.currentModifyItem&&(this.addFormDialog.name=this.currentModifyItem.name)},methods:{closeAddDialog:function(){this.addFormDialog.name="",this.$store.commit("setActivedType",""),this.$emit("closeAddDialog")},saveData:function(){var e=this;if(""==this.addFormDialog.name)return this.$message.error(this.$t("messageTips.nameNotEmpty")),void(this.addFormDialog.inputError=!0);var t=0;if(window.scene.selectionSets.forEach((function(i){i.name==e.addFormDialog.name&&t++})),t>0)return this.$message.error(this.addFormDialog.name+" "+this.$t("messageTips.nameAlreadyExists")),!1;if(this.currentModifyItem){window.scene.removeSelectionSet(this.currentModifyItem.name);var i=this.currentModifyItem,s=i.color,o=i.opacity,a=i.ids;window.scene.addSelectionSet(this.addFormDialog.name,a,s,o),this.$message.success(this.$t("messageTips.renameSuccess"))}else{var n={};window.scene.getSelection().forEach((function(e){if(n[e.model.id])n[e.model.id].push(e.id);else{var t=[];t.push(e.id),n[e.model.id]=t}})),window.scene.addSelectionSet(this.addFormDialog.name,n,"",""),this.$message.success(this.$t("messageTips.createdSuccess")),window.scene.clearSelection(),window.scene.render()}var c=[];window.scene.selectionSets.forEach((function(e){c.push(JSON.parse(JSON.parse(JSON.stringify(e))))})),this.$store.commit("setSelectionSets",c),this.closeAddDialog()}}}),n=a,c=(i("029c"),i("b733"),i("2877")),r=Object(c["a"])(n,s,o,!1,null,"21597eda",null);t["default"]=r.exports}}]);