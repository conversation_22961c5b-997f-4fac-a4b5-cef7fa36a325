(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-768652ee"],{"03d9":function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"c",(function(){return r})),n.d(t,"b",(function(){return o})),n.d(t,"d",(function(){return s})),n.d(t,"e",(function(){return a})),n.d(t,"g",(function(){return u})),n.d(t,"h",(function(){return l})),n.d(t,"f",(function(){return c}));var i,r,o,s,a,u,l,c,h=n("dff7");(function(e){e.noSelection=h["a"]("noSelection","No selection"),e.singleSelectionRange=h["a"]("singleSelectionRange","Line {0}, Column {1} ({2} selected)"),e.singleSelection=h["a"]("singleSelection","Line {0}, Column {1}"),e.multiSelectionRange=h["a"]("multiSelectionRange","{0} selections ({1} characters selected)"),e.multiSelection=h["a"]("multiSelection","{0} selections"),e.emergencyConfOn=h["a"]("emergencyConfOn","Now changing the setting `accessibilitySupport` to 'on'."),e.openingDocs=h["a"]("openingDocs","Now opening the Editor Accessibility documentation page."),e.readonlyDiffEditor=h["a"]("readonlyDiffEditor"," in a read-only pane of a diff editor."),e.editableDiffEditor=h["a"]("editableDiffEditor"," in a pane of a diff editor."),e.readonlyEditor=h["a"]("readonlyEditor"," in a read-only code editor"),e.editableEditor=h["a"]("editableEditor"," in a code editor"),e.changeConfigToOnMac=h["a"]("changeConfigToOnMac","To configure the editor to be optimized for usage with a Screen Reader press Command+E now."),e.changeConfigToOnWinLinux=h["a"]("changeConfigToOnWinLinux","To configure the editor to be optimized for usage with a Screen Reader press Control+E now."),e.auto_on=h["a"]("auto_on","The editor is configured to be optimized for usage with a Screen Reader."),e.auto_off=h["a"]("auto_off","The editor is configured to never be optimized for usage with a Screen Reader, which is not the case at this time."),e.tabFocusModeOnMsg=h["a"]("tabFocusModeOnMsg","Pressing Tab in the current editor will move focus to the next focusable element. Toggle this behavior by pressing {0}."),e.tabFocusModeOnMsgNoKb=h["a"]("tabFocusModeOnMsgNoKb","Pressing Tab in the current editor will move focus to the next focusable element. The command {0} is currently not triggerable by a keybinding."),e.tabFocusModeOffMsg=h["a"]("tabFocusModeOffMsg","Pressing Tab in the current editor will insert the tab character. Toggle this behavior by pressing {0}."),e.tabFocusModeOffMsgNoKb=h["a"]("tabFocusModeOffMsgNoKb","Pressing Tab in the current editor will insert the tab character. The command {0} is currently not triggerable by a keybinding."),e.openDocMac=h["a"]("openDocMac","Press Command+H now to open a browser window with more information related to editor accessibility."),e.openDocWinLinux=h["a"]("openDocWinLinux","Press Control+H now to open a browser window with more information related to editor accessibility."),e.outroMsg=h["a"]("outroMsg","You can dismiss this tooltip and return to the editor by pressing Escape or Shift+Escape."),e.showAccessibilityHelpAction=h["a"]("showAccessibilityHelpAction","Show Accessibility Help")})(i||(i={})),function(e){e.inspectTokensAction=h["a"]("inspectTokens","Developer: Inspect Tokens")}(r||(r={})),function(e){e.gotoLineLabelValidLineAndColumn=h["a"]("gotoLineLabelValidLineAndColumn","Go to line {0} and character {1}"),e.gotoLineLabelValidLine=h["a"]("gotoLineLabelValidLine","Go to line {0}"),e.gotoLineLabelEmptyWithLineLimit=h["a"]("gotoLineLabelEmptyWithLineLimit","Type a line number between 1 and {0} to navigate to"),e.gotoLineLabelEmptyWithLineAndColumnLimit=h["a"]("gotoLineLabelEmptyWithLineAndColumnLimit","Type a character between 1 and {0} to navigate to"),e.gotoLineAriaLabel=h["a"]("gotoLineAriaLabel","Current Line: {0}. Go to line {1}."),e.gotoLineActionInput=h["a"]("gotoLineActionInput","Type a line number, followed by an optional colon and a character number to navigate to"),e.gotoLineActionLabel=h["a"]("gotoLineActionLabel","Go to Line...")}(o||(o={})),function(e){e.ariaLabelEntryWithKey=h["a"]("ariaLabelEntryWithKey","{0}, {1}, commands"),e.ariaLabelEntry=h["a"]("ariaLabelEntry","{0}, commands"),e.quickCommandActionInput=h["a"]("quickCommandActionInput","Type the name of an action you want to execute"),e.quickCommandActionLabel=h["a"]("quickCommandActionLabel","Command Palette")}(s||(s={})),function(e){e.entryAriaLabel=h["a"]("entryAriaLabel","{0}, symbols"),e.quickOutlineActionInput=h["a"]("quickOutlineActionInput","Type the name of an identifier you wish to navigate to"),e.quickOutlineActionLabel=h["a"]("quickOutlineActionLabel","Go to Symbol..."),e._symbols_=h["a"]("symbols","symbols ({0})"),e._modules_=h["a"]("modules","modules ({0})"),e._class_=h["a"]("class","classes ({0})"),e._interface_=h["a"]("interface","interfaces ({0})"),e._method_=h["a"]("method","methods ({0})"),e._function_=h["a"]("function","functions ({0})"),e._property_=h["a"]("property","properties ({0})"),e._variable_=h["a"]("variable","variables ({0})"),e._variable2_=h["a"]("variable2","variables ({0})"),e._constructor_=h["a"]("_constructor","constructors ({0})"),e._call_=h["a"]("call","calls ({0})")}(a||(a={})),function(e){e.editorViewAccessibleLabel=h["a"]("editorViewAccessibleLabel","Editor content"),e.accessibilityHelpMessageIE=h["a"]("accessibilityHelpMessageIE","Press Ctrl+F1 for Accessibility Options."),e.accessibilityHelpMessage=h["a"]("accessibilityHelpMessage","Press Alt+F1 for Accessibility Options.")}(u||(u={})),function(e){e.toggleHighContrast=h["a"]("toggleHighContrast","Toggle High Contrast Theme")}(l||(l={})),function(e){e.bulkEditServiceSummary=h["a"]("bulkEditServiceSummary","Made {0} edits in {1} files")}(c||(c={}))},"045b":function(e,t,n){"use strict";function i(e,t){var n=e.getCount(),i=e.findTokenIndexAtOffset(t),o=e.getLanguageId(i),s=i;while(s+1<n&&e.getLanguageId(s+1)===o)s++;var a=i;while(a>0&&e.getLanguageId(a-1)===o)a--;return new r(e,o,a,s+1,e.getStartOffset(a),e.getEndOffset(s))}n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return o}));var r=function(){function e(e,t,n,i,r,o){this._actual=e,this.languageId=t,this._firstTokenIndex=n,this._lastTokenIndex=i,this.firstCharOffset=r,this._lastCharOffset=o}return e.prototype.getLineContent=function(){var e=this._actual.getLineContent();return e.substring(this.firstCharOffset,this._lastCharOffset)},e.prototype.getActualLineContentBefore=function(e){var t=this._actual.getLineContent();return t.substring(0,this.firstCharOffset+e)},e.prototype.getTokenCount=function(){return this._lastTokenIndex-this._firstTokenIndex},e.prototype.findTokenIndexAtOffset=function(e){return this._actual.findTokenIndexAtOffset(e+this.firstCharOffset)-this._firstTokenIndex},e.prototype.getStandardTokenType=function(e){return this._actual.getStandardTokenType(e+this._firstTokenIndex)},e}();function o(e){return 0!==(7&e)}},"04d3":function(e,t,n){"use strict";n.d(t,"b",(function(){return h})),n.d(t,"c",(function(){return g})),n.d(t,"a",(function(){return w}));var i=n("e8e3"),r=n("7061"),o=n("6a89"),s=n("b57f"),a=n("0d83"),u=n("2de5"),l=n("a8d0"),c=function(){function e(e,t){this.outputLineIndex=e,this.outputOffset=t}return e}(),h=function(){function e(e,t,n){this.breakOffsets=e,this.breakOffsetsVisibleColumn=t,this.wrappedTextIndentLength=n}return e.getInputOffsetOfOutputPosition=function(e,t,n){return 0===t?n:e[t-1]+n},e.getOutputPositionOfInputOffset=function(e,t){var n=0,i=e.length-1,r=0,o=0;while(n<=i){r=n+(i-n)/2|0;var s=e[r];if(o=r>0?e[r-1]:0,t<o)i=r-1;else{if(!(t>=s))break;n=r+1}}return new c(r,t-o)},e}(),d=function(){function e(e){this._lines=e}return e.prototype.convertViewPositionToModelPosition=function(e){return this._lines.convertViewPositionToModelPosition(e.lineNumber,e.column)},e.prototype.convertViewRangeToModelRange=function(e){return this._lines.convertViewRangeToModelRange(e)},e.prototype.validateViewPosition=function(e,t){return this._lines.validateViewPosition(e.lineNumber,e.column,t)},e.prototype.validateViewRange=function(e,t){return this._lines.validateViewRange(e,t)},e.prototype.convertModelPositionToViewPosition=function(e){return this._lines.convertModelPositionToViewPosition(e.lineNumber,e.column)},e.prototype.convertModelRangeToViewRange=function(e){return this._lines.convertModelRangeToViewRange(e)},e.prototype.modelPositionIsVisible=function(e){return this._lines.modelPositionIsVisible(e.lineNumber,e.column)},e}(),f=function(){function e(e){this._counts=e,this._isValid=!1,this._validEndIndex=-1,this._modelToView=[],this._viewToModel=[]}return e.prototype._invalidate=function(e){this._isValid=!1,this._validEndIndex=Math.min(this._validEndIndex,e-1)},e.prototype._ensureValid=function(){if(!this._isValid){for(var e=this._validEndIndex+1,t=this._counts.length;e<t;e++){var n=this._counts[e],i=e>0?this._modelToView[e-1]:0;this._modelToView[e]=i+n;for(var r=0;r<n;r++)this._viewToModel[i+r]=e}this._modelToView.length=this._counts.length,this._viewToModel.length=this._modelToView[this._modelToView.length-1],this._isValid=!0,this._validEndIndex=this._counts.length-1}},e.prototype.changeValue=function(e,t){this._counts[e]!==t&&(this._counts[e]=t,this._invalidate(e))},e.prototype.removeValues=function(e,t){this._counts.splice(e,t),this._invalidate(e)},e.prototype.insertValues=function(e,t){this._counts=i["a"](this._counts,e,t),this._invalidate(e)},e.prototype.getTotalValue=function(){return this._ensureValid(),this._viewToModel.length},e.prototype.getAccumulatedValue=function(e){return this._ensureValid(),this._modelToView[e]},e.prototype.getIndexOf=function(e){this._ensureValid();var t=this._viewToModel[e],n=t>0?this._modelToView[t-1]:0;return new u["b"](t,e-n)},e}(),g=function(){function e(e,t,n,i,r,o,s,a){this.model=e,this._validModelVersionId=-1,this._domLineBreaksComputerFactory=t,this._monospaceLineBreaksComputerFactory=n,this.fontInfo=i,this.tabSize=r,this.wrappingStrategy=o,this.wrappingColumn=s,this.wrappingIndent=a,this._constructLines(!0,null)}return e.prototype.dispose=function(){this.hiddenAreasIds=this.model.deltaDecorations(this.hiddenAreasIds,[])},e.prototype.createCoordinatesConverter=function(){return new d(this)},e.prototype._constructLines=function(e,t){var n=this;this.lines=[],e&&(this.hiddenAreasIds=[]);for(var i=this.model.getLinesContent(),r=i.length,s=this.createLineBreaksComputer(),a=0;a<r;a++)s.addRequest(i[a],t?t[a]:null);var u=s.finalize(),l=[],c=this.hiddenAreasIds.map((function(e){return n.model.getDecorationRange(e)})).sort(o["a"].compareRangesUsingStarts),h=1,d=0,g=-1,p=g+1<c.length?d+1:r+2;for(a=0;a<r;a++){var m=a+1;m===p&&(g++,h=c[g].startLineNumber,d=c[g].endLineNumber,p=g+1<c.length?d+1:r+2);var _=m>=h&&m<=d,v=C(u[a],!_);l[a]=v.getViewLineCount(),this.lines[a]=v}this._validModelVersionId=this.model.getVersionId(),this.prefixSumComputer=new f(l)},e.prototype.getHiddenAreas=function(){var e=this;return this.hiddenAreasIds.map((function(t){return e.model.getDecorationRange(t)}))},e.prototype._reduceRanges=function(e){var t=this;if(0===e.length)return[];for(var n=e.map((function(e){return t.model.validateRange(e)})).sort(o["a"].compareRangesUsingStarts),i=[],r=n[0].startLineNumber,s=n[0].endLineNumber,a=1,u=n.length;a<u;a++){var l=n[a];l.startLineNumber>s+1?(i.push(new o["a"](r,1,s,1)),r=l.startLineNumber,s=l.endLineNumber):l.endLineNumber>s&&(s=l.endLineNumber)}return i.push(new o["a"](r,1,s,1)),i},e.prototype.setHiddenAreas=function(e){var t=this,n=this._reduceRanges(e),i=this.hiddenAreasIds.map((function(e){return t.model.getDecorationRange(e)})).sort(o["a"].compareRangesUsingStarts);if(n.length===i.length){for(var r=!1,a=0;a<n.length;a++)if(!n[a].equalsRange(i[a])){r=!0;break}if(!r)return!1}for(var u=[],l=0,c=n;l<c.length;l++){var h=c[l];u.push({range:h,options:s["a"].EMPTY})}this.hiddenAreasIds=this.model.deltaDecorations(this.hiddenAreasIds,u);var d=n,f=1,g=0,p=-1,m=p+1<d.length?g+1:this.lines.length+2,_=!1;for(a=0;a<this.lines.length;a++){var v=a+1;v===m&&(p++,f=d[p].startLineNumber,g=d[p].endLineNumber,m=p+1<d.length?g+1:this.lines.length+2);var y=!1;if(v>=f&&v<=g?this.lines[a].isVisible()&&(this.lines[a]=this.lines[a].setVisible(!1),y=!0):(_=!0,this.lines[a].isVisible()||(this.lines[a]=this.lines[a].setVisible(!0),y=!0)),y){var b=this.lines[a].getViewLineCount();this.prefixSumComputer.changeValue(a,b)}}return _||this.setHiddenAreas([]),!0},e.prototype.modelPositionIsVisible=function(e,t){return!(e<1||e>this.lines.length)&&this.lines[e-1].isVisible()},e.prototype.setTabSize=function(e){return this.tabSize!==e&&(this.tabSize=e,this._constructLines(!1,null),!0)},e.prototype.setWrappingSettings=function(e,t,n,i){var r=this.fontInfo.equals(e),o=this.wrappingStrategy===t,s=this.wrappingColumn===n,a=this.wrappingIndent===i;if(r&&o&&s&&a)return!1;var u=r&&o&&!s&&a;this.fontInfo=e,this.wrappingStrategy=t,this.wrappingColumn=n,this.wrappingIndent=i;var l=null;if(u){l=[];for(var c=0,h=this.lines.length;c<h;c++)l[c]=this.lines[c].getLineBreakData()}return this._constructLines(!1,l),!0},e.prototype.createLineBreaksComputer=function(){var e="advanced"===this.wrappingStrategy?this._domLineBreaksComputerFactory:this._monospaceLineBreaksComputerFactory;return e.createLineBreaksComputer(this.fontInfo,this.tabSize,this.wrappingColumn,this.wrappingIndent)},e.prototype.onModelFlushed=function(){this._constructLines(!0,null)},e.prototype.onModelLinesDeleted=function(e,t,n){if(e<=this._validModelVersionId)return null;var i=1===t?1:this.prefixSumComputer.getAccumulatedValue(t-2)+1,r=this.prefixSumComputer.getAccumulatedValue(n-1);return this.lines.splice(t-1,n-t+1),this.prefixSumComputer.removeValues(t-1,n-t+1),new a["k"](i,r)},e.prototype.onModelLinesInserted=function(e,t,n,i){if(e<=this._validModelVersionId)return null;for(var o=this.getHiddenAreas(),s=!1,u=new r["a"](t,1),l=0,c=o;l<c.length;l++){var h=c[l];if(h.containsPosition(u)){s=!0;break}}for(var d=1===t?1:this.prefixSumComputer.getAccumulatedValue(t-2)+1,f=0,g=[],p=[],m=0,_=i.length;m<_;m++){var v=C(i[m],!s);g.push(v);var y=v.getViewLineCount();f+=y,p[m]=y}return this.lines=this.lines.slice(0,t-1).concat(g).concat(this.lines.slice(t-1)),this.prefixSumComputer.insertValues(t-1,p),new a["l"](d,d+f-1)},e.prototype.onModelLineChanged=function(e,t,n){if(e<=this._validModelVersionId)return[!1,null,null,null];var i=t-1,r=this.lines[i].getViewLineCount(),o=this.lines[i].isVisible(),s=C(n,o);this.lines[i]=s;var u=this.lines[i].getViewLineCount(),l=!1,c=0,h=-1,d=0,f=-1,g=0,p=-1;r>u?(c=1===t?1:this.prefixSumComputer.getAccumulatedValue(t-2)+1,h=c+u-1,g=h+1,p=g+(r-u)-1,l=!0):r<u?(c=1===t?1:this.prefixSumComputer.getAccumulatedValue(t-2)+1,h=c+r-1,d=h+1,f=d+(u-r)-1,l=!0):(c=1===t?1:this.prefixSumComputer.getAccumulatedValue(t-2)+1,h=c+u-1),this.prefixSumComputer.changeValue(i,u);var m=c<=h?new a["j"](c,h):null,_=d<=f?new a["l"](d,f):null,v=g<=p?new a["k"](g,p):null;return[l,m,_,v]},e.prototype.acceptVersionId=function(e){this._validModelVersionId=e,1!==this.lines.length||this.lines[0].isVisible()||this.setHiddenAreas([])},e.prototype.getViewLineCount=function(){return this.prefixSumComputer.getTotalValue()},e.prototype._toValidViewLineNumber=function(e){if(e<1)return 1;var t=this.getViewLineCount();return e>t?t:0|e},e.prototype.getActiveIndentGuide=function(e,t,n){e=this._toValidViewLineNumber(e),t=this._toValidViewLineNumber(t),n=this._toValidViewLineNumber(n);var i=this.convertViewPositionToModelPosition(e,this.getViewLineMinColumn(e)),r=this.convertViewPositionToModelPosition(t,this.getViewLineMinColumn(t)),o=this.convertViewPositionToModelPosition(n,this.getViewLineMinColumn(n)),s=this.model.getActiveIndentGuide(i.lineNumber,r.lineNumber,o.lineNumber),a=this.convertModelPositionToViewPosition(s.startLineNumber,1),u=this.convertModelPositionToViewPosition(s.endLineNumber,this.model.getLineMaxColumn(s.endLineNumber));return{startLineNumber:a.lineNumber,endLineNumber:u.lineNumber,indent:s.indent}},e.prototype.getViewLinesIndentGuides=function(e,t){e=this._toValidViewLineNumber(e),t=this._toValidViewLineNumber(t);for(var n=this.convertViewPositionToModelPosition(e,this.getViewLineMinColumn(e)),i=this.convertViewPositionToModelPosition(t,this.getViewLineMaxColumn(t)),o=[],s=[],a=[],u=n.lineNumber-1,l=i.lineNumber-1,c=null,h=u;h<=l;h++){var d=this.lines[h];if(d.isVisible()){var f=d.getViewLineNumberOfModelPosition(0,h===u?n.column:1),g=d.getViewLineNumberOfModelPosition(0,this.model.getLineMaxColumn(h+1)),p=g-f+1,m=0;p>1&&1===d.getViewLineMinColumn(this.model,h+1,g)&&(m=0===f?1:2),s.push(p),a.push(m),null===c&&(c=new r["a"](h+1,0))}else null!==c&&(o=o.concat(this.model.getLinesIndentGuides(c.lineNumber,h)),c=null)}null!==c&&(o=o.concat(this.model.getLinesIndentGuides(c.lineNumber,i.lineNumber)),c=null);for(var _=t-e+1,v=new Array(_),y=0,b=0,C=o.length;b<C;b++){var L=o[b],w=(p=Math.min(_-y,s[b]),m=a[b],void 0);w=2===m?0:1===m?1:p;for(var S=0;S<p;S++)S===w&&(L=0),v[y++]=L}return v},e.prototype.getViewLineContent=function(e){e=this._toValidViewLineNumber(e);var t=this.prefixSumComputer.getIndexOf(e-1),n=t.index,i=t.remainder;return this.lines[n].getViewLineContent(this.model,n+1,i)},e.prototype.getViewLineLength=function(e){e=this._toValidViewLineNumber(e);var t=this.prefixSumComputer.getIndexOf(e-1),n=t.index,i=t.remainder;return this.lines[n].getViewLineLength(this.model,n+1,i)},e.prototype.getViewLineMinColumn=function(e){e=this._toValidViewLineNumber(e);var t=this.prefixSumComputer.getIndexOf(e-1),n=t.index,i=t.remainder;return this.lines[n].getViewLineMinColumn(this.model,n+1,i)},e.prototype.getViewLineMaxColumn=function(e){e=this._toValidViewLineNumber(e);var t=this.prefixSumComputer.getIndexOf(e-1),n=t.index,i=t.remainder;return this.lines[n].getViewLineMaxColumn(this.model,n+1,i)},e.prototype.getViewLineData=function(e){e=this._toValidViewLineNumber(e);var t=this.prefixSumComputer.getIndexOf(e-1),n=t.index,i=t.remainder;return this.lines[n].getViewLineData(this.model,n+1,i)},e.prototype.getViewLinesData=function(e,t,n){e=this._toValidViewLineNumber(e),t=this._toValidViewLineNumber(t);for(var i=this.prefixSumComputer.getIndexOf(e-1),r=e,o=i.index,s=i.remainder,a=[],u=o,l=this.model.getLineCount();u<l;u++){var c=this.lines[u];if(c.isVisible()){var h=u===o?s:0,d=c.getViewLineCount()-h,f=!1;r+d>t&&(f=!0,d=t-r+1);var g=h+d;if(c.getViewLinesData(this.model,u+1,h,g,r-e,n,a),r+=d,f)break}}return a},e.prototype.validateViewPosition=function(e,t,n){e=this._toValidViewLineNumber(e);var i=this.prefixSumComputer.getIndexOf(e-1),o=i.index,s=i.remainder,a=this.lines[o],u=a.getViewLineMinColumn(this.model,o+1,s),l=a.getViewLineMaxColumn(this.model,o+1,s);t<u&&(t=u),t>l&&(t=l);var c=a.getModelColumnOfViewPosition(s,t),h=this.model.validatePosition(new r["a"](o+1,c));return h.equals(n)?new r["a"](e,t):this.convertModelPositionToViewPosition(n.lineNumber,n.column)},e.prototype.validateViewRange=function(e,t){var n=this.validateViewPosition(e.startLineNumber,e.startColumn,t.getStartPosition()),i=this.validateViewPosition(e.endLineNumber,e.endColumn,t.getEndPosition());return new o["a"](n.lineNumber,n.column,i.lineNumber,i.column)},e.prototype.convertViewPositionToModelPosition=function(e,t){e=this._toValidViewLineNumber(e);var n=this.prefixSumComputer.getIndexOf(e-1),i=n.index,o=n.remainder,s=this.lines[i].getModelColumnOfViewPosition(o,t);return this.model.validatePosition(new r["a"](i+1,s))},e.prototype.convertViewRangeToModelRange=function(e){var t=this.convertViewPositionToModelPosition(e.startLineNumber,e.startColumn),n=this.convertViewPositionToModelPosition(e.endLineNumber,e.endColumn);return new o["a"](t.lineNumber,t.column,n.lineNumber,n.column)},e.prototype.convertModelPositionToViewPosition=function(e,t){var n=this.model.validatePosition(new r["a"](e,t)),i=n.lineNumber,o=n.column,s=i-1,a=!1;while(s>0&&!this.lines[s].isVisible())s--,a=!0;if(0===s&&!this.lines[s].isVisible())return new r["a"](1,1);var u,l=1+(0===s?0:this.prefixSumComputer.getAccumulatedValue(s-1));return u=a?this.lines[s].getViewPositionOfModelPosition(l,this.model.getLineMaxColumn(s+1)):this.lines[i-1].getViewPositionOfModelPosition(l,o),u},e.prototype.convertModelRangeToViewRange=function(e){var t=this.convertModelPositionToViewPosition(e.startLineNumber,e.startColumn),n=this.convertModelPositionToViewPosition(e.endLineNumber,e.endColumn);return e.startLineNumber===e.endLineNumber&&t.lineNumber!==n.lineNumber&&n.column===this.getViewLineMinColumn(n.lineNumber)?new o["a"](t.lineNumber,t.column,n.lineNumber-1,this.getViewLineMaxColumn(n.lineNumber-1)):new o["a"](t.lineNumber,t.column,n.lineNumber,n.column)},e.prototype._getViewLineNumberForModelPosition=function(e,t){var n=e-1;if(this.lines[n].isVisible()){var i=1+(0===n?0:this.prefixSumComputer.getAccumulatedValue(n-1));return this.lines[n].getViewLineNumberOfModelPosition(i,t)}while(n>0&&!this.lines[n].isVisible())n--;if(0===n&&!this.lines[n].isVisible())return 1;var r=1+(0===n?0:this.prefixSumComputer.getAccumulatedValue(n-1));return this.lines[n].getViewLineNumberOfModelPosition(r,this.model.getLineMaxColumn(n+1))},e.prototype.getAllOverviewRulerDecorations=function(e,t,n){for(var i=this.model.getOverviewRulerDecorations(e,t),r=new S,o=0,s=i;o<s.length;o++){var a=s[o],u=a.options.overviewRuler,l=u?u.position:0;if(0!==l){var c=u.getColor(n),h=this._getViewLineNumberForModelPosition(a.range.startLineNumber,a.range.startColumn),d=this._getViewLineNumberForModelPosition(a.range.endLineNumber,a.range.endColumn);r.accept(c,h,d,l)}}return r.result},e.prototype.getDecorationsInRange=function(e,t,n){var i=this.convertViewPositionToModelPosition(e.startLineNumber,e.startColumn),s=this.convertViewPositionToModelPosition(e.endLineNumber,e.endColumn);if(s.lineNumber-i.lineNumber<=e.endLineNumber-e.startLineNumber)return this.model.getDecorationsInRange(new o["a"](i.lineNumber,1,s.lineNumber,s.column),t,n);for(var a=[],u=i.lineNumber-1,l=s.lineNumber-1,c=null,h=u;h<=l;h++){var d=this.lines[h];if(d.isVisible())null===c&&(c=new r["a"](h+1,h===u?i.column:1));else if(null!==c){var f=this.model.getLineMaxColumn(h);a=a.concat(this.model.getDecorationsInRange(new o["a"](c.lineNumber,c.column,h,f),t,n)),c=null}}null!==c&&(a=a.concat(this.model.getDecorationsInRange(new o["a"](c.lineNumber,c.column,s.lineNumber,s.column),t,n)),c=null),a.sort((function(e,t){var n=o["a"].compareRangesUsingStarts(e.range,t.range);return 0===n?e.id<t.id?-1:e.id>t.id?1:0:n}));for(var g=[],p=0,m=null,_=0,v=a;_<v.length;_++){var y=v[_],b=y.id;m!==b&&(m=b,g[p++]=y)}return g},e}(),p=function(){function e(){}return e.prototype.isVisible=function(){return!0},e.prototype.setVisible=function(e){return e?this:m.INSTANCE},e.prototype.getLineBreakData=function(){return null},e.prototype.getViewLineCount=function(){return 1},e.prototype.getViewLineContent=function(e,t,n){return e.getLineContent(t)},e.prototype.getViewLineLength=function(e,t,n){return e.getLineLength(t)},e.prototype.getViewLineMinColumn=function(e,t,n){return e.getLineMinColumn(t)},e.prototype.getViewLineMaxColumn=function(e,t,n){return e.getLineMaxColumn(t)},e.prototype.getViewLineData=function(e,t,n){var i=e.getLineTokens(t),r=i.getLineContent();return new l["c"](r,!1,1,r.length+1,0,i.inflate())},e.prototype.getViewLinesData=function(e,t,n,i,r,o,s){o[r]?s[r]=this.getViewLineData(e,t,0):s[r]=null},e.prototype.getModelColumnOfViewPosition=function(e,t){return t},e.prototype.getViewPositionOfModelPosition=function(e,t){return new r["a"](e,t)},e.prototype.getViewLineNumberOfModelPosition=function(e,t){return e},e.INSTANCE=new e,e}(),m=function(){function e(){}return e.prototype.isVisible=function(){return!1},e.prototype.setVisible=function(e){return e?p.INSTANCE:this},e.prototype.getLineBreakData=function(){return null},e.prototype.getViewLineCount=function(){return 0},e.prototype.getViewLineContent=function(e,t,n){throw new Error("Not supported")},e.prototype.getViewLineLength=function(e,t,n){throw new Error("Not supported")},e.prototype.getViewLineMinColumn=function(e,t,n){throw new Error("Not supported")},e.prototype.getViewLineMaxColumn=function(e,t,n){throw new Error("Not supported")},e.prototype.getViewLineData=function(e,t,n){throw new Error("Not supported")},e.prototype.getViewLinesData=function(e,t,n,i,r,o,s){throw new Error("Not supported")},e.prototype.getModelColumnOfViewPosition=function(e,t){throw new Error("Not supported")},e.prototype.getViewPositionOfModelPosition=function(e,t){throw new Error("Not supported")},e.prototype.getViewLineNumberOfModelPosition=function(e,t){throw new Error("Not supported")},e.INSTANCE=new e,e}(),_=function(){function e(e,t){this._lineBreakData=e,this._isVisible=t}return e.prototype.isVisible=function(){return this._isVisible},e.prototype.setVisible=function(e){return this._isVisible=e,this},e.prototype.getLineBreakData=function(){return this._lineBreakData},e.prototype.getViewLineCount=function(){return this._isVisible?this._lineBreakData.breakOffsets.length:0},e.prototype.getInputStartOffsetOfOutputLineIndex=function(e){return h.getInputOffsetOfOutputPosition(this._lineBreakData.breakOffsets,e,0)},e.prototype.getInputEndOffsetOfOutputLineIndex=function(e,t,n){return n+1===this._lineBreakData.breakOffsets.length?e.getLineMaxColumn(t)-1:h.getInputOffsetOfOutputPosition(this._lineBreakData.breakOffsets,n+1,0)},e.prototype.getViewLineContent=function(e,t,n){if(!this._isVisible)throw new Error("Not supported");var i=this.getInputStartOffsetOfOutputLineIndex(n),r=this.getInputEndOffsetOfOutputLineIndex(e,t,n),o=e.getValueInRange({startLineNumber:t,startColumn:i+1,endLineNumber:t,endColumn:r+1});return n>0&&(o=y(this._lineBreakData.wrappedTextIndentLength)+o),o},e.prototype.getViewLineLength=function(e,t,n){if(!this._isVisible)throw new Error("Not supported");var i=this.getInputStartOffsetOfOutputLineIndex(n),r=this.getInputEndOffsetOfOutputLineIndex(e,t,n),o=r-i;return n>0&&(o=this._lineBreakData.wrappedTextIndentLength+o),o},e.prototype.getViewLineMinColumn=function(e,t,n){if(!this._isVisible)throw new Error("Not supported");return n>0?this._lineBreakData.wrappedTextIndentLength+1:1},e.prototype.getViewLineMaxColumn=function(e,t,n){if(!this._isVisible)throw new Error("Not supported");return this.getViewLineContent(e,t,n).length+1},e.prototype.getViewLineData=function(e,t,n){if(!this._isVisible)throw new Error("Not supported");var i=this.getInputStartOffsetOfOutputLineIndex(n),r=this.getInputEndOffsetOfOutputLineIndex(e,t,n),o=e.getValueInRange({startLineNumber:t,startColumn:i+1,endLineNumber:t,endColumn:r+1});n>0&&(o=y(this._lineBreakData.wrappedTextIndentLength)+o);var s=n>0?this._lineBreakData.wrappedTextIndentLength+1:1,a=o.length+1,u=n+1<this.getViewLineCount(),c=0;n>0&&(c=this._lineBreakData.wrappedTextIndentLength);var h=e.getLineTokens(t),d=0===n?0:this._lineBreakData.breakOffsetsVisibleColumn[n-1];return new l["c"](o,u,s,a,d,h.sliceAndInflate(i,r,c))},e.prototype.getViewLinesData=function(e,t,n,i,r,o,s){if(!this._isVisible)throw new Error("Not supported");for(var a=n;a<i;a++){var u=r+a-n;o[u]?s[u]=this.getViewLineData(e,t,a):s[u]=null}},e.prototype.getModelColumnOfViewPosition=function(e,t){if(!this._isVisible)throw new Error("Not supported");var n=t-1;return e>0&&(n<this._lineBreakData.wrappedTextIndentLength?n=0:n-=this._lineBreakData.wrappedTextIndentLength),h.getInputOffsetOfOutputPosition(this._lineBreakData.breakOffsets,e,n)+1},e.prototype.getViewPositionOfModelPosition=function(e,t){if(!this._isVisible)throw new Error("Not supported");var n=h.getOutputPositionOfInputOffset(this._lineBreakData.breakOffsets,t-1),i=n.outputLineIndex,o=n.outputOffset+1;return i>0&&(o+=this._lineBreakData.wrappedTextIndentLength),new r["a"](e+i,o)},e.prototype.getViewLineNumberOfModelPosition=function(e,t){if(!this._isVisible)throw new Error("Not supported");var n=h.getOutputPositionOfInputOffset(this._lineBreakData.breakOffsets,t-1);return e+n.outputLineIndex},e}(),v=[""];function y(e){if(e>=v.length)for(var t=1;t<=e;t++)v[t]=b(t);return v[e]}function b(e){return new Array(e+1).join(" ")}function C(e,t){return null===e?t?p.INSTANCE:m.INSTANCE:new _(e,t)}var L=function(){function e(e){this._lines=e}return e.prototype._validPosition=function(e){return this._lines.model.validatePosition(e)},e.prototype._validRange=function(e){return this._lines.model.validateRange(e)},e.prototype.convertViewPositionToModelPosition=function(e){return this._validPosition(e)},e.prototype.convertViewRangeToModelRange=function(e){return this._validRange(e)},e.prototype.validateViewPosition=function(e,t){return this._validPosition(t)},e.prototype.validateViewRange=function(e,t){return this._validRange(t)},e.prototype.convertModelPositionToViewPosition=function(e){return this._validPosition(e)},e.prototype.convertModelRangeToViewRange=function(e){return this._validRange(e)},e.prototype.modelPositionIsVisible=function(e){var t=this._lines.model.getLineCount();return!(e.lineNumber<1||e.lineNumber>t)},e}(),w=function(){function e(e){this.model=e}return e.prototype.dispose=function(){},e.prototype.createCoordinatesConverter=function(){return new L(this)},e.prototype.getHiddenAreas=function(){return[]},e.prototype.setHiddenAreas=function(e){return!1},e.prototype.setTabSize=function(e){return!1},e.prototype.setWrappingSettings=function(e,t,n,i){return!1},e.prototype.createLineBreaksComputer=function(){var e=[];return{addRequest:function(t,n){e.push(null)},finalize:function(){return e}}},e.prototype.onModelFlushed=function(){},e.prototype.onModelLinesDeleted=function(e,t,n){return new a["k"](t,n)},e.prototype.onModelLinesInserted=function(e,t,n,i){return new a["l"](t,n)},e.prototype.onModelLineChanged=function(e,t,n){return[!1,new a["j"](t,t),null,null]},e.prototype.acceptVersionId=function(e){},e.prototype.getViewLineCount=function(){return this.model.getLineCount()},e.prototype.getActiveIndentGuide=function(e,t,n){return{startLineNumber:e,endLineNumber:e,indent:0}},e.prototype.getViewLinesIndentGuides=function(e,t){for(var n=t-e+1,i=new Array(n),r=0;r<n;r++)i[r]=0;return i},e.prototype.getViewLineContent=function(e){return this.model.getLineContent(e)},e.prototype.getViewLineLength=function(e){return this.model.getLineLength(e)},e.prototype.getViewLineMinColumn=function(e){return this.model.getLineMinColumn(e)},e.prototype.getViewLineMaxColumn=function(e){return this.model.getLineMaxColumn(e)},e.prototype.getViewLineData=function(e){var t=this.model.getLineTokens(e),n=t.getLineContent();return new l["c"](n,!1,1,n.length+1,0,t.inflate())},e.prototype.getViewLinesData=function(e,t,n){var i=this.model.getLineCount();e=Math.min(Math.max(1,e),i),t=Math.min(Math.max(1,t),i);for(var r=[],o=e;o<=t;o++){var s=o-e;n[s]||(r[s]=null),r[s]=this.getViewLineData(o)}return r},e.prototype.getAllOverviewRulerDecorations=function(e,t,n){for(var i=this.model.getOverviewRulerDecorations(e,t),r=new S,o=0,s=i;o<s.length;o++){var a=s[o],u=a.options.overviewRuler,l=u?u.position:0;if(0!==l){var c=u.getColor(n),h=a.range.startLineNumber,d=a.range.endLineNumber;r.accept(c,h,d,l)}}return r.result},e.prototype.getDecorationsInRange=function(e,t,n){return this.model.getDecorationsInRange(e,t,n)},e}(),S=function(){function e(){this.result=Object.create(null)}return e.prototype.accept=function(e,t,n,i){var r=this.result[e];if(r){var o=r[r.length-3],s=r[r.length-1];if(o===i&&s+1>=t)return void(n>s&&(r[r.length-1]=n));r.push(i,t,n)}else this.result[e]=[i,t,n]},e}()},"0d83":function(e,t,n){"use strict";n.d(t,"a",(function(){return s})),n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return u})),n.d(t,"d",(function(){return l})),n.d(t,"f",(function(){return c})),n.d(t,"g",(function(){return h})),n.d(t,"h",(function(){return d})),n.d(t,"i",(function(){return f})),n.d(t,"j",(function(){return g})),n.d(t,"k",(function(){return p})),n.d(t,"l",(function(){return m})),n.d(t,"m",(function(){return _})),n.d(t,"n",(function(){return v})),n.d(t,"o",(function(){return y})),n.d(t,"p",(function(){return b})),n.d(t,"q",(function(){return C})),n.d(t,"r",(function(){return L})),n.d(t,"e",(function(){return w}));var i=n("fdcc"),r=n("a666"),o=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),s=function(){function e(e){this.type=1,this._source=e}return e.prototype.hasChanged=function(e){return this._source.hasChanged(e)},e}(),a=function(){function e(e){this.type=2,this.contentWidth=e.contentWidth,this.contentHeight=e.contentHeight,this.contentWidthChanged=e.contentWidthChanged,this.contentHeightChanged=e.contentHeightChanged}return e}(),u=function(){function e(e,t){this.type=3,this.selections=e,this.modelSelections=t}return e}(),l=function(){function e(){this.type=4}return e}(),c=function(){function e(){this.type=5}return e}(),h=function(){function e(e){this.type=6,this.isFocused=e}return e}(),d=function(){function e(){this.type=7}return e}(),f=function(){function e(){this.type=8}return e}(),g=function(){function e(e,t){this.type=9,this.fromLineNumber=e,this.toLineNumber=t}return e}(),p=function(){function e(e,t){this.type=10,this.fromLineNumber=e,this.toLineNumber=t}return e}(),m=function(){function e(e,t){this.type=11,this.fromLineNumber=e,this.toLineNumber=t}return e}(),_=function(){function e(e,t,n,i,r){this.type=12,this.source=e,this.range=t,this.verticalType=n,this.revealHorizontal=i,this.scrollType=r}return e}(),v=function(){function e(e){this.type=13,this.scrollWidth=e.scrollWidth,this.scrollLeft=e.scrollLeft,this.scrollHeight=e.scrollHeight,this.scrollTop=e.scrollTop,this.scrollWidthChanged=e.scrollWidthChanged,this.scrollLeftChanged=e.scrollLeftChanged,this.scrollHeightChanged=e.scrollHeightChanged,this.scrollTopChanged=e.scrollTopChanged}return e}(),y=function(){function e(){this.type=14}return e}(),b=function(){function e(e){this.type=15,this.ranges=e}return e}(),C=function(){function e(){this.type=16}return e}(),L=function(){function e(){this.type=17}return e}(),w=function(e){function t(){var t=e.call(this)||this;return t._listeners=[],t._collector=null,t._collectorCnt=0,t}return o(t,e),t.prototype.dispose=function(){this._listeners=[],e.prototype.dispose.call(this)},t.prototype._beginEmit=function(){return this._collectorCnt++,1===this._collectorCnt&&(this._collector=new S),this._collector},t.prototype._endEmit=function(){if(this._collectorCnt--,0===this._collectorCnt){var e=this._collector.finalize();this._collector=null,e.length>0&&this._emit(e)}},t.prototype._emit=function(e){for(var t=this._listeners.slice(0),n=0,i=t.length;n<i;n++)O(t[n],e)},t.prototype.addEventListener=function(e){var t=this;return this._listeners.push(e),Object(r["h"])((function(){for(var n=t._listeners,i=0,r=n.length;i<r;i++)if(n[i]===e){n.splice(i,1);break}}))},t}(r["a"]),S=function(){function e(){this._eventsLen=0,this._events=[],this._eventsLen=0}return e.prototype.emit=function(e){this._events[this._eventsLen++]=e},e.prototype.finalize=function(){var e=this._events;return this._events=[],e},e}();function O(e,t){try{e(t)}catch(n){i["e"](n)}}},1080:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return v}));var i=n("3742"),r=n("6a89"),o=function(){function e(t,n,i,r,o,s){this.languageIdentifier=t,this.index=n,this.open=i,this.close=r,this.forwardRegex=o,this.reversedRegex=s,this._openSet=e._toSet(this.open),this._closeSet=e._toSet(this.close)}return e.prototype.isOpen=function(e){return this._openSet.has(e)},e.prototype.isClose=function(e){return this._closeSet.has(e)},e._toSet=function(e){for(var t=new Set,n=0,i=e;n<i.length;n++){var r=i[n];t.add(r)}return t},e}();function s(e){var t=e.length;e=e.map((function(e){return[e[0].toLowerCase(),e[1].toLowerCase()]}));for(var n=[],i=0;i<t;i++)n[i]=i;var r=function(e,t){var n=e[0],i=e[1],r=t[0],o=t[1];return n===r||n===o||i===r||i===o},o=function(e,i){for(var r=Math.min(e,i),o=Math.max(e,i),s=0;s<t;s++)n[s]===o&&(n[s]=r)};for(i=0;i<t;i++)for(var s=e[i],a=i+1;a<t;a++){var u=e[a];r(s,u)&&o(n[i],n[a])}for(var l=[],c=0;c<t;c++){var h=[],d=[];for(i=0;i<t;i++)if(n[i]===c){var f=e[i],g=f[0],p=f[1];h.push(g),d.push(p)}h.length>0&&l.push({open:h,close:d})}return l}var a=function(){function e(e,t){var n=s(t);this.brackets=n.map((function(t,i){return new o(e,i,t.open,t.close,h(t.open,t.close,n,i),d(t.open,t.close,n,i))})),this.forwardRegex=f(this.brackets),this.reversedRegex=g(this.brackets),this.textIsBracket={},this.textIsOpenBracket={},this.maxBracketLength=0;for(var i=0,r=this.brackets;i<r.length;i++){for(var a=r[i],u=0,l=a.open;u<l.length;u++){var c=l[u];this.textIsBracket[c]=a,this.textIsOpenBracket[c]=!0,this.maxBracketLength=Math.max(this.maxBracketLength,c.length)}for(var p=0,m=a.close;p<m.length;p++){var _=m[p];this.textIsBracket[_]=a,this.textIsOpenBracket[_]=!1,this.maxBracketLength=Math.max(this.maxBracketLength,_.length)}}}return e}();function u(e,t,n,i){for(var r=0,o=t.length;r<o;r++)if(r!==n){for(var s=t[r],a=0,u=s.open;a<u.length;a++){var l=u[a];l.indexOf(e)>=0&&i.push(l)}for(var c=0,h=s.close;c<h.length;c++){var d=h[c];d.indexOf(e)>=0&&i.push(d)}}}function l(e,t){return e.length-t.length}function c(e){if(e.length<=1)return e;for(var t=[],n=new Set,i=0,r=e;i<r.length;i++){var o=r[i];n.has(o)||(t.push(o),n.add(o))}return t}function h(e,t,n,i){var r=[];r=r.concat(e),r=r.concat(t);for(var o=0,s=r.length;o<s;o++)u(r[o],n,i,r);return r=c(r),r.sort(l),r.reverse(),m(r)}function d(e,t,n,i){var r=[];r=r.concat(e),r=r.concat(t);for(var o=0,s=r.length;o<s;o++)u(r[o],n,i,r);return r=c(r),r.sort(l),r.reverse(),m(r.map(_))}function f(e){for(var t=[],n=0,i=e;n<i.length;n++){for(var r=i[n],o=0,s=r.open;o<s.length;o++){var a=s[o];t.push(a)}for(var u=0,l=r.close;u<l.length;u++){var h=l[u];t.push(h)}}return t=c(t),m(t)}function g(e){for(var t=[],n=0,i=e;n<i.length;n++){for(var r=i[n],o=0,s=r.open;o<s.length;o++){var a=s[o];t.push(a)}for(var u=0,l=r.close;u<l.length;u++){var h=l[u];t.push(h)}}return t=c(t),m(t.map(_))}function p(e){var t=/^[\w ]+$/.test(e);return e=i["p"](e),t?"\\b"+e+"\\b":e}function m(e){var t="("+e.map(p).join(")|(")+")";return i["l"](t,!0)}var _=function(){function e(e){for(var t="",n=e.length-1;n>=0;n--)t+=e.charAt(n);return t}var t=null,n=null;return function(i){return t!==i&&(t=i,n=e(t)),n}}(),v=function(){function e(){}return e._findPrevBracketInText=function(e,t,n,i){var o=n.match(e);if(!o)return null;var s=n.length-(o.index||0),a=o[0].length,u=i+s;return new r["a"](t,u-a+1,t,u+1)},e.findPrevBracketInRange=function(e,t,n,i,r){var o=_(n),s=o.substring(n.length-r,n.length-i);return this._findPrevBracketInText(e,t,s,i)},e.findNextBracketInText=function(e,t,n,i){var o=n.match(e);if(!o)return null;var s=o.index||0,a=o[0].length;if(0===a)return null;var u=i+s;return new r["a"](t,u+1,t,u+1+a)},e.findNextBracketInRange=function(e,t,n,i,r){var o=n.substring(i,r);return this.findNextBracketInText(e,t,o,i)},e}()},"1b69":function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return o}));var i=n("0a0f"),r=Object(i["c"])("modelService");function o(e){return!e.isTooLargeForSyncing()&&!e.isForSimpleWidget}},2837:function(e,t,n){"use strict";var i;n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return r})),function(e){e[e["None"]=0]="None",e[e["Indent"]=1]="Indent",e[e["IndentOutdent"]=2]="IndentOutdent",e[e["Outdent"]=3]="Outdent"}(i||(i={}));var r=function(){function e(e){if(this.open=e.open,this.close=e.close,this._standardTokenMask=0,Array.isArray(e.notIn))for(var t=0,n=e.notIn.length;t<n;t++){var i=e.notIn[t];switch(i){case"string":this._standardTokenMask|=2;break;case"comment":this._standardTokenMask|=1;break;case"regex":this._standardTokenMask|=4;break}}}return e.prototype.isOK=function(e){return 0===(this._standardTokenMask&e)},e}()},"2de5":function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return o}));var i=n("099d"),r=function(){function e(e,t){this.index=e,this.remainder=t}return e}(),o=function(){function e(e){this.values=e,this.prefixSum=new Uint32Array(e.length),this.prefixSumValidIndex=new Int32Array(1),this.prefixSumValidIndex[0]=-1}return e.prototype.insertValues=function(e,t){e=Object(i["a"])(e);var n=this.values,r=this.prefixSum,o=t.length;return 0!==o&&(this.values=new Uint32Array(n.length+o),this.values.set(n.subarray(0,e),0),this.values.set(n.subarray(e),e+o),this.values.set(t,e),e-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=e-1),this.prefixSum=new Uint32Array(this.values.length),this.prefixSumValidIndex[0]>=0&&this.prefixSum.set(r.subarray(0,this.prefixSumValidIndex[0]+1)),!0)},e.prototype.changeValue=function(e,t){return e=Object(i["a"])(e),t=Object(i["a"])(t),this.values[e]!==t&&(this.values[e]=t,e-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=e-1),!0)},e.prototype.removeValues=function(e,t){e=Object(i["a"])(e),t=Object(i["a"])(t);var n=this.values,r=this.prefixSum;if(e>=n.length)return!1;var o=n.length-e;return t>=o&&(t=o),0!==t&&(this.values=new Uint32Array(n.length-t),this.values.set(n.subarray(0,e),0),this.values.set(n.subarray(e+t),e),this.prefixSum=new Uint32Array(this.values.length),e-1<this.prefixSumValidIndex[0]&&(this.prefixSumValidIndex[0]=e-1),this.prefixSumValidIndex[0]>=0&&this.prefixSum.set(r.subarray(0,this.prefixSumValidIndex[0]+1)),!0)},e.prototype.getTotalValue=function(){return 0===this.values.length?0:this._getAccumulatedValue(this.values.length-1)},e.prototype.getAccumulatedValue=function(e){return e<0?0:(e=Object(i["a"])(e),this._getAccumulatedValue(e))},e.prototype._getAccumulatedValue=function(e){if(e<=this.prefixSumValidIndex[0])return this.prefixSum[e];var t=this.prefixSumValidIndex[0]+1;0===t&&(this.prefixSum[0]=this.values[0],t++),e>=this.values.length&&(e=this.values.length-1);for(var n=t;n<=e;n++)this.prefixSum[n]=this.prefixSum[n-1]+this.values[n];return this.prefixSumValidIndex[0]=Math.max(this.prefixSumValidIndex[0],e),this.prefixSum[e]},e.prototype.getIndexOf=function(e){e=Math.floor(e),this.getTotalValue();var t=0,n=this.values.length-1,i=0,o=0,s=0;while(t<=n)if(i=t+(n-t)/2|0,o=this.prefixSum[i],s=o-this.values[i],e<s)n=i-1;else{if(!(e>=o))break;t=i+1}return new r(i,e-s)},e}()},"32a4":function(e,t,n){"use strict";n.d(t,"a",(function(){return c})),n.d(t,"c",(function(){return h})),n.d(t,"b",(function(){return d}));var i=n("dff7"),r=n("308f"),o=n("b707"),s=n("70cb"),a=n("89cd"),u={ModesRegistry:"editor.modesRegistry"},l=function(){function e(){this._onDidChangeLanguages=new r["a"],this.onDidChangeLanguages=this._onDidChangeLanguages.event,this._languages=[],this._dynamicLanguages=[]}return e.prototype.registerLanguage=function(e){this._languages.push(e),this._onDidChangeLanguages.fire(void 0)},e.prototype.getLanguages=function(){return[].concat(this._languages).concat(this._dynamicLanguages)},e}(),c=new l;a["a"].add(u.ModesRegistry,c);var h="plaintext",d=new o["r"](h,1);c.registerLanguage({id:h,extensions:[".txt",".gitignore"],aliases:[i["a"]("plainText.alias","Plain Text"),"text"],mimetypes:["text/plain"]}),s["a"].register(d,{brackets:[["(",")"],["[","]"],["{","}"]],surroundingPairs:[{open:"{",close:"}"},{open:"[",close:"]"},{open:"(",close:")"},{open:"<",close:">"},{open:'"',close:'"'},{open:"'",close:"'"},{open:"`",close:"`"}],folding:{offSide:!0}})},"32f2":function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return o}));var i=function(){function e(e,t,n){this.from=0|e,this.to=0|t,this.colorId=0|n}return e.compare=function(e,t){return e.colorId===t.colorId?e.from===t.from?e.to-t.to:e.from-t.from:e.colorId-t.colorId},e}(),r=function(){function e(e,t,n){this.startLineNumber=e,this.endLineNumber=t,this.color=n,this._colorZone=null}return e.compare=function(e,t){return e.color===t.color?e.startLineNumber===t.startLineNumber?e.endLineNumber-t.endLineNumber:e.startLineNumber-t.startLineNumber:e.color<t.color?-1:1},e.prototype.setColorZone=function(e){this._colorZone=e},e.prototype.getColorZones=function(){return this._colorZone},e}(),o=function(){function e(e){this._getVerticalOffsetForLine=e,this._zones=[],this._colorZonesInvalid=!1,this._lineHeight=0,this._domWidth=0,this._domHeight=0,this._outerHeight=0,this._pixelRatio=1,this._lastAssignedId=0,this._color2Id=Object.create(null),this._id2Color=[]}return e.prototype.getId2Color=function(){return this._id2Color},e.prototype.setZones=function(e){this._zones=e,this._zones.sort(r.compare)},e.prototype.setLineHeight=function(e){return this._lineHeight!==e&&(this._lineHeight=e,this._colorZonesInvalid=!0,!0)},e.prototype.setPixelRatio=function(e){this._pixelRatio=e,this._colorZonesInvalid=!0},e.prototype.getDOMWidth=function(){return this._domWidth},e.prototype.getCanvasWidth=function(){return this._domWidth*this._pixelRatio},e.prototype.setDOMWidth=function(e){return this._domWidth!==e&&(this._domWidth=e,this._colorZonesInvalid=!0,!0)},e.prototype.getDOMHeight=function(){return this._domHeight},e.prototype.getCanvasHeight=function(){return this._domHeight*this._pixelRatio},e.prototype.setDOMHeight=function(e){return this._domHeight!==e&&(this._domHeight=e,this._colorZonesInvalid=!0,!0)},e.prototype.getOuterHeight=function(){return this._outerHeight},e.prototype.setOuterHeight=function(e){return this._outerHeight!==e&&(this._outerHeight=e,this._colorZonesInvalid=!0,!0)},e.prototype.resolveColorZones=function(){for(var e=this._colorZonesInvalid,t=Math.floor(this._lineHeight),n=Math.floor(this.getCanvasHeight()),r=Math.floor(this._outerHeight),o=n/r,s=Math.floor(4*this._pixelRatio/2),a=[],u=0,l=this._zones.length;u<l;u++){var c=this._zones[u];if(!e){var h=c.getColorZones();if(h){a.push(h);continue}}var d=Math.floor(o*this._getVerticalOffsetForLine(c.startLineNumber)),f=Math.floor(o*(this._getVerticalOffsetForLine(c.endLineNumber)+t)),g=Math.floor((d+f)/2),p=f-g;p<s&&(p=s),g-p<0&&(g=p),g+p>n&&(g=n-p);var m=c.color,_=this._color2Id[m];_||(_=++this._lastAssignedId,this._color2Id[m]=_,this._id2Color[_]=m);var v=new i(g-p,g+p,_);c.setColorZone(v),a.push(v)}return this._colorZonesInvalid=!1,a.sort(i.compare),a},e}()},"4d05":function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return u}));var i=n("3742"),r=n("e1b5"),o=n("8bf1"),s={getInitialState:function(){return o["c"]},tokenize2:function(e,t,n){return Object(o["e"])(0,e,t,n)}};function a(e,t){return void 0===t&&(t=s),l(e,t||s)}function u(e,t,n,i,r,o,s){for(var a="<div>",u=i,l=0,c=0,h=t.getCount();c<h;c++){var d=t.getEndOffset(c);if(!(d<=i)){for(var f="";u<d&&u<r;u++){var g=e.charCodeAt(u);switch(g){case 9:var p=o-(u+l)%o;l+=p-1;while(p>0)f+=s?"&#160;":" ",p--;break;case 60:f+="&lt;";break;case 62:f+="&gt;";break;case 38:f+="&amp;";break;case 0:f+="&#00;";break;case 65279:case 8232:f+="�";break;case 13:f+="&#8203";break;case 32:f+=s?"&#160;":" ";break;default:f+=String.fromCharCode(g)}}if(a+='<span style="'+t.getInlineStyle(c,n)+'">'+f+"</span>",d>r||u>=r)break}}return a+="</div>",a}function l(e,t){for(var n='<div class="monaco-tokenized-source">',o=e.split(/\r\n|\r|\n/),s=t.getInitialState(),a=0,u=o.length;a<u;a++){var l=o[a];a>0&&(n+="<br/>");var c=t.tokenize2(l,s,0);r["a"].convertToEndOffset(c.tokens,l.length);for(var h=new r["a"](c.tokens,l),d=h.inflate(),f=0,g=0,p=d.getCount();g<p;g++){var m=d.getClassName(g),_=d.getEndOffset(g);n+='<span class="'+m+'">'+i["o"](l.substring(f,_))+"</span>",f=_}s=c.endState}return n+="</div>",n}},5110:function(e,t,n){(function(e){
/*!
Copyright (c) 2014 Taylor Hakes
Copyright (c) 2014 Forbes Lindesay
 */
(function(e,t){t()})(0,(function(){"use strict";function t(e){var t=this.constructor;return this.then((function(n){return t.resolve(e()).then((function(){return n}))}),(function(n){return t.resolve(e()).then((function(){return t.reject(n)}))}))}var n=setTimeout;function i(){}function r(e,t){return function(){e.apply(t,arguments)}}function o(e){if(!(this instanceof o))throw new TypeError("Promises must be constructed via new");if("function"!==typeof e)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],h(e,this)}function s(e,t){while(3===e._state)e=e._value;0!==e._state?(e._handled=!0,o._immediateFn((function(){var n=1===e._state?t.onFulfilled:t.onRejected;if(null!==n){var i;try{i=n(e._value)}catch(r){return void u(t.promise,r)}a(t.promise,i)}else(1===e._state?a:u)(t.promise,e._value)}))):e._deferreds.push(t)}function a(e,t){try{if(t===e)throw new TypeError("A promise cannot be resolved with itself.");if(t&&("object"===typeof t||"function"===typeof t)){var n=t.then;if(t instanceof o)return e._state=3,e._value=t,void l(e);if("function"===typeof n)return void h(r(n,t),e)}e._state=1,e._value=t,l(e)}catch(i){u(e,i)}}function u(e,t){e._state=2,e._value=t,l(e)}function l(e){2===e._state&&0===e._deferreds.length&&o._immediateFn((function(){e._handled||o._unhandledRejectionFn(e._value)}));for(var t=0,n=e._deferreds.length;t<n;t++)s(e,e._deferreds[t]);e._deferreds=null}function c(e,t,n){this.onFulfilled="function"===typeof e?e:null,this.onRejected="function"===typeof t?t:null,this.promise=n}function h(e,t){var n=!1;try{e((function(e){n||(n=!0,a(t,e))}),(function(e){n||(n=!0,u(t,e))}))}catch(i){if(n)return;n=!0,u(t,i)}}o.prototype["catch"]=function(e){return this.then(null,e)},o.prototype.then=function(e,t){var n=new this.constructor(i);return s(this,new c(e,t,n)),n},o.prototype["finally"]=t,o.all=function(e){return new o((function(t,n){if(!e||"undefined"===typeof e.length)throw new TypeError("Promise.all accepts an array");var i=Array.prototype.slice.call(e);if(0===i.length)return t([]);var r=i.length;function o(e,s){try{if(s&&("object"===typeof s||"function"===typeof s)){var a=s.then;if("function"===typeof a)return void a.call(s,(function(t){o(e,t)}),n)}i[e]=s,0===--r&&t(i)}catch(u){n(u)}}for(var s=0;s<i.length;s++)o(s,i[s])}))},o.resolve=function(e){return e&&"object"===typeof e&&e.constructor===o?e:new o((function(t){t(e)}))},o.reject=function(e){return new o((function(t,n){n(e)}))},o.race=function(e){return new o((function(t,n){for(var i=0,r=e.length;i<r;i++)e[i].then(t,n)}))},o._immediateFn="function"===typeof setImmediate&&function(e){setImmediate(e)}||function(e){n(e,0)},o._unhandledRejectionFn=function(e){"undefined"!==typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",e)};var d=function(){if("undefined"!==typeof self)return self;if("undefined"!==typeof window)return window;if("undefined"!==typeof e)return e;throw new Error("unable to locate global object")}();"Promise"in d?d.Promise.prototype["finally"]||(d.Promise.prototype["finally"]=t):d["Promise"]=o}))}).call(this,n("c8ba"))},5380:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var i=function(){function e(e,t,n,i){this.configuration=e,this.theme=t,this.model=n,this.viewLayout=n.viewLayout,this.privateViewEventBus=i}return e.prototype.addEventHandler=function(e){this.privateViewEventBus.addEventHandler(e)},e.prototype.removeEventHandler=function(e){this.privateViewEventBus.removeEventHandler(e)},e}()},"567d":function(e,t,n){"use strict";n.d(t,"b",(function(){return Z})),n.d(t,"a",(function(){return te}));var i=n("5fe7"),r=n("a666"),o=n("1b1f"),s=n("c531"),a=n("6a89"),u=n("b707"),l=n("70cb"),c=n("e8e3"),h=n("1b0e"),d=n("258a"),f=n("30db"),g=n("6d8e"),p=n("7061"),m=n("22e9"),_=n("a411"),v=n("d093"),y=n("3170"),b=function(){function e(e,t,n){for(var i=new Uint8Array(e*t),r=0,o=e*t;r<o;r++)i[r]=n;this._data=i,this.rows=e,this.cols=t}return e.prototype.get=function(e,t){return this._data[e*this.cols+t]},e.prototype.set=function(e,t,n){this._data[e*this.cols+t]=n},e}(),C=function(){function e(e){for(var t=0,n=0,i=0,r=e.length;i<r;i++){var o=e[i],s=o[0],a=o[1],u=o[2];a>t&&(t=a),s>n&&(n=s),u>n&&(n=u)}t++,n++;var l=new b(n,t,0);for(i=0,r=e.length;i<r;i++){var c=e[i];s=c[0],a=c[1],u=c[2];l.set(s,a,u)}this._states=l,this._maxCharCode=t}return e.prototype.nextState=function(e,t){return t<0||t>=this._maxCharCode?0:this._states.get(e,t)},e}(),L=null;function w(){return null===L&&(L=new C([[1,104,2],[1,72,2],[1,102,6],[1,70,6],[2,116,3],[2,84,3],[3,116,4],[3,84,4],[4,112,5],[4,80,5],[5,115,9],[5,83,9],[5,58,10],[6,105,7],[6,73,7],[7,108,8],[7,76,8],[8,101,9],[8,69,9],[9,58,10],[10,47,11],[11,47,12]])),L}var S=null;function O(){if(null===S){S=new y["a"](0);for(var e=" \t<>'\"、。｡､，．：；？！＠＃＄％＆＊‘“〈《「『【〔（［｛｢｣｝］）〕】』」》〉”’｀～…",t=0;t<e.length;t++)S.set(e.charCodeAt(t),1);var n=".,;";for(t=0;t<n.length;t++)S.set(n.charCodeAt(t),2)}return S}var I=function(){function e(){}return e._createLink=function(e,t,n,i,r){var o=r-1;do{var s=t.charCodeAt(o),a=e.get(s);if(2!==a)break;o--}while(o>i);if(i>0){var u=t.charCodeAt(i-1),l=t.charCodeAt(o);(40===u&&41===l||91===u&&93===l||123===u&&125===l)&&o--}return{range:{startLineNumber:n,startColumn:i+1,endLineNumber:n,endColumn:o+2},url:t.substring(i,o+1)}},e.computeLinks=function(t,n){void 0===n&&(n=w());for(var i=O(),r=[],o=1,s=t.getLineCount();o<=s;o++){var a=t.getLineContent(o),u=a.length,l=0,c=0,h=0,d=1,f=!1,g=!1,p=!1;while(l<u){var m=!1,_=a.charCodeAt(l);if(13===d){var v=void 0;switch(_){case 40:f=!0,v=0;break;case 41:v=f?0:1;break;case 91:g=!0,v=0;break;case 93:v=g?0:1;break;case 123:p=!0,v=0;break;case 125:v=p?0:1;break;case 39:v=34===h||96===h?0:1;break;case 34:v=39===h||96===h?0:1;break;case 96:v=39===h||34===h?0:1;break;case 42:v=42===h?1:0;break;case 124:v=124===h?1:0;break;default:v=i.get(_)}1===v&&(r.push(e._createLink(i,a,o,c,l)),m=!0)}else if(12===d){v=void 0;91===_?(g=!0,v=0):v=i.get(_),1===v?m=!0:d=13}else d=n.nextState(d,_),0===d&&(m=!0);m&&(d=1,f=!1,g=!1,p=!1,c=l+1,h=_),l++}13===d&&r.push(e._createLink(i,a,o,c,u))}return r},e}();function M(e){return e&&"function"===typeof e.getLineCount&&"function"===typeof e.getLineContent?I.computeLinks(e):[]}var k=function(){function e(){this._defaultValueSet=[["true","false"],["True","False"],["Private","Public","Friend","ReadOnly","Partial","Protected","WriteOnly"],["public","protected","private"]]}return e.prototype.navigateValueSet=function(e,t,n,i,r){if(e&&t){var o=this.doNavigateValueSet(t,r);if(o)return{range:e,value:o}}if(n&&i){o=this.doNavigateValueSet(i,r);if(o)return{range:n,value:o}}return null},e.prototype.doNavigateValueSet=function(e,t){var n=this.numberReplace(e,t);return null!==n?n:this.textReplace(e,t)},e.prototype.numberReplace=function(e,t){var n=Math.pow(10,e.length-(e.lastIndexOf(".")+1)),i=Number(e),r=parseFloat(e);return isNaN(i)||isNaN(r)||i!==r?null:0!==i||t?(i=Math.floor(i*n),i+=t?n:-n,String(i/n)):null},e.prototype.textReplace=function(e,t){return this.valueSetsReplace(this._defaultValueSet,e,t)},e.prototype.valueSetsReplace=function(e,t,n){for(var i=null,r=0,o=e.length;null===i&&r<o;r++)i=this.valueSetReplace(e[r],t,n);return i},e.prototype.valueSetReplace=function(e,t,n){var i=e.indexOf(t);return i>=0?(i+=n?1:-1,i<0?i=e.length-1:i%=e.length,e[i]):null},e.INSTANCE=new e,e}(),N=n("6eab"),T=n("ef8e"),E=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),A=function(e,t,n,i){function r(e){return e instanceof n?e:new n((function(t){t(e)}))}return new(n||(n=Promise))((function(n,o){function s(e){try{u(i.next(e))}catch(t){o(t)}}function a(e){try{u(i["throw"](e))}catch(t){o(t)}}function u(e){e.done?n(e.value):r(e.value).then(s,a)}u((i=i.apply(e,t||[])).next())}))},x=function(e,t){var n,i,r,o,s={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(e){return function(t){return u([e,t])}}function u(o){if(n)throw new TypeError("Generator is already executing.");while(s)try{if(n=1,i&&(r=2&o[0]?i["return"]:o[0]?i["throw"]||((r=i["return"])&&r.call(i),0):i.next)&&!(r=r.call(i,o[1])).done)return r;switch(i=0,r&&(o=[2&o[0],r.value]),o[0]){case 0:case 1:r=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,i=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(r=s.trys,!(r=r.length>0&&r[r.length-1])&&(6===o[0]||2===o[0])){s=0;continue}if(3===o[0]&&(!r||o[1]>r[0]&&o[1]<r[3])){s.label=o[1];break}if(6===o[0]&&s.label<r[1]){s.label=r[1],r=o;break}if(r&&s.label<r[2]){s.label=r[2],s.ops.push(o);break}r[2]&&s.ops.pop(),s.trys.pop();continue}o=t.call(e,s)}catch(a){o=[6,a],i=0}finally{n=r=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}},R=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return E(t,e),Object.defineProperty(t.prototype,"uri",{get:function(){return this._uri},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"version",{get:function(){return this._versionId},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"eol",{get:function(){return this._eol},enumerable:!0,configurable:!0}),t.prototype.getValue=function(){return this.getText()},t.prototype.getLinesContent=function(){return this._lines.slice(0)},t.prototype.getLineCount=function(){return this._lines.length},t.prototype.getLineContent=function(e){return this._lines[e-1]},t.prototype.getWordAtPosition=function(e,t){var n=Object(v["d"])(e.column,Object(v["c"])(t),this._lines[e.lineNumber-1],0);return n?new a["a"](e.lineNumber,n.startColumn,e.lineNumber,n.endColumn):null},t.prototype.createWordIterator=function(e){var t,n,i=this,r=0,o=0,s=[],a=function(){if(o<s.length){var u=n.substring(s[o].start,s[o].end);return o+=1,t?t.value=u:t={done:!1,value:u},t}return r>=i._lines.length?d["c"]:(n=i._lines[r],s=i._wordenize(n,e),o=0,r+=1,a())};return{next:a}},t.prototype.getLineWords=function(e,t){for(var n=this._lines[e-1],i=this._wordenize(n,t),r=[],o=0,s=i;o<s.length;o++){var a=s[o];r.push({word:n.substring(a.start,a.end),startColumn:a.start+1,endColumn:a.end+1})}return r},t.prototype._wordenize=function(e,t){var n,i=[];t.lastIndex=0;while(n=t.exec(e)){if(0===n[0].length)break;i.push({start:n.index,end:n.index+n[0].length})}return i},t.prototype.getValueInRange=function(e){if(e=this._validateRange(e),e.startLineNumber===e.endLineNumber)return this._lines[e.startLineNumber-1].substring(e.startColumn-1,e.endColumn-1);var t=this._eol,n=e.startLineNumber-1,i=e.endLineNumber-1,r=[];r.push(this._lines[n].substring(e.startColumn-1));for(var o=n+1;o<i;o++)r.push(this._lines[o]);return r.push(this._lines[i].substring(0,e.endColumn-1)),r.join(t)},t.prototype.offsetAt=function(e){return e=this._validatePosition(e),this._ensureLineStarts(),this._lineStarts.getAccumulatedValue(e.lineNumber-2)+(e.column-1)},t.prototype.positionAt=function(e){e=Math.floor(e),e=Math.max(0,e),this._ensureLineStarts();var t=this._lineStarts.getIndexOf(e),n=this._lines[t.index].length;return{lineNumber:1+t.index,column:1+Math.min(t.remainder,n)}},t.prototype._validateRange=function(e){var t=this._validatePosition({lineNumber:e.startLineNumber,column:e.startColumn}),n=this._validatePosition({lineNumber:e.endLineNumber,column:e.endColumn});return t.lineNumber!==e.startLineNumber||t.column!==e.startColumn||n.lineNumber!==e.endLineNumber||n.column!==e.endColumn?{startLineNumber:t.lineNumber,startColumn:t.column,endLineNumber:n.lineNumber,endColumn:n.column}:e},t.prototype._validatePosition=function(e){if(!p["a"].isIPosition(e))throw new Error("bad position");var t=e.lineNumber,n=e.column,i=!1;if(t<1)t=1,n=1,i=!0;else if(t>this._lines.length)t=this._lines.length,n=this._lines[t-1].length+1,i=!0;else{var r=this._lines[t-1].length+1;n<1?(n=1,i=!0):n>r&&(n=r,i=!0)}return i?{lineNumber:t,column:n}:e},t}(_["a"]),V=function(){function e(e,t){this._host=e,this._models=Object.create(null),this._foreignModuleFactory=t,this._foreignModule=null}return e.prototype.dispose=function(){this._models=Object.create(null)},e.prototype._getModel=function(e){return this._models[e]},e.prototype._getModels=function(){var e=this,t=[];return Object.keys(this._models).forEach((function(n){return t.push(e._models[n])})),t},e.prototype.acceptNewModel=function(e){this._models[e.url]=new R(g["a"].parse(e.url),e.lines,e.EOL,e.versionId)},e.prototype.acceptModelChanged=function(e,t){if(this._models[e]){var n=this._models[e];n.onEvents(t)}},e.prototype.acceptRemovedModel=function(e){this._models[e]&&delete this._models[e]},e.prototype.computeDiff=function(e,t,n,i){return A(this,void 0,void 0,(function(){var r,o,s,a,u,l,c;return x(this,(function(h){return r=this._getModel(e),o=this._getModel(t),r&&o?(s=r.getLinesContent(),a=o.getLinesContent(),u=new m["a"](s,a,{shouldComputeCharChanges:!0,shouldPostProcessCharChanges:!0,shouldIgnoreTrimWhitespace:n,shouldMakePrettyDiff:!0,maxComputationTime:i}),l=u.computeDiff(),c=!(l.changes.length>0)&&this._modelsAreIdentical(r,o),[2,{quitEarly:l.quitEarly,identical:c,changes:l.changes}]):[2,null]}))}))},e.prototype._modelsAreIdentical=function(e,t){var n=e.getLineCount(),i=t.getLineCount();if(n!==i)return!1;for(var r=1;r<=n;r++){var o=e.getLineContent(r),s=t.getLineContent(r);if(o!==s)return!1}return!0},e.prototype.computeMoreMinimalEdits=function(t,n){return A(this,void 0,void 0,(function(){var i,r,o,s,u,l,d,f,g,p,m,_,v,y,b,C,L,w;return x(this,(function(S){if(i=this._getModel(t),!i)return[2,n];for(r=[],o=void 0,n=Object(c["r"])(n,(function(e,t){if(e.range&&t.range)return a["a"].compareRangesUsingStarts(e.range,t.range);var n=e.range?0:1,i=t.range?0:1;return n-i})),s=0,u=n;s<u.length;s++)if(l=u[s],d=l.range,f=l.text,g=l.eol,"number"===typeof g&&(o=g),(!a["a"].isEmpty(d)||f)&&(p=i.getValueInRange(d),f=f.replace(/\r\n|\n|\r/g,i.eol),p!==f))if(Math.max(f.length,p.length)>e._diffLimit)r.push({range:d,text:f});else for(m=Object(h["b"])(p,f,!1),_=i.offsetAt(a["a"].lift(d).getStartPosition()),v=0,y=m;v<y.length;v++)b=y[v],C=i.positionAt(_+b.originalStart),L=i.positionAt(_+b.originalStart+b.originalLength),w={text:f.substr(b.modifiedStart,b.modifiedLength),range:{startLineNumber:C.lineNumber,startColumn:C.column,endLineNumber:L.lineNumber,endColumn:L.column}},i.getValueInRange(w.range)!==w.text&&r.push(w);return"number"===typeof o&&r.push({eol:o,text:"",range:{startLineNumber:0,startColumn:0,endLineNumber:0,endColumn:0}}),[2,r]}))}))},e.prototype.computeLinks=function(e){return A(this,void 0,void 0,(function(){var t;return x(this,(function(n){return t=this._getModel(e),t?[2,M(t)]:[2,null]}))}))},e.prototype.textualSuggest=function(t,n,i,r){return A(this,void 0,void 0,(function(){var o,s,a,u,l,c,h,d;return x(this,(function(f){if(o=this._getModel(t),!o)return[2,null];for(s=[],a=new Set,u=new RegExp(i,r),l=o.getWordAtPosition(n,u),l&&a.add(o.getValueInRange(l)),c=o.createWordIterator(u),h=c.next();!h.done&&a.size<=e._suggestionsLimit;h=c.next())d=h.value,a.has(d)||(a.add(d),isNaN(Number(d))&&s.push(d));return[2,s]}))}))},e.prototype.computeWordRanges=function(e,t,n,i){return A(this,void 0,void 0,(function(){var r,o,s,a,u,l,c,h,d;return x(this,(function(f){if(r=this._getModel(e),!r)return[2,Object.create(null)];for(o=new RegExp(n,i),s=Object.create(null),a=t.startLineNumber;a<t.endLineNumber;a++)for(u=r.getLineWords(a,o),l=0,c=u;l<c.length;l++)h=c[l],isNaN(Number(h.word))&&(d=s[h.word],d||(d=[],s[h.word]=d),d.push({startLineNumber:a,startColumn:h.startColumn,endLineNumber:a,endColumn:h.endColumn}));return[2,s]}))}))},e.prototype.navigateValueSet=function(e,t,n,i,r){return A(this,void 0,void 0,(function(){var o,s,a,u,l,c;return x(this,(function(h){return o=this._getModel(e),o?(s=new RegExp(i,r),t.startColumn===t.endColumn&&(t={startLineNumber:t.startLineNumber,startColumn:t.startColumn,endLineNumber:t.endLineNumber,endColumn:t.endColumn+1}),a=o.getValueInRange(t),u=o.getWordAtPosition({lineNumber:t.startLineNumber,column:t.startColumn},s),u?(l=o.getValueInRange(u),c=k.INSTANCE.navigateValueSet(t,a,u,l,n),[2,c]):[2,null]):[2,null]}))}))},e.prototype.loadForeignModule=function(e,t,n){var i=this,r=function(e,t){return i._host.fhr(e,t)},o=T["b"](n,r),s={host:o,getMirrorModels:function(){return i._getModels()}};return this._foreignModuleFactory?(this._foreignModule=this._foreignModuleFactory(s,t),Promise.resolve(T["c"](this._foreignModule))):Promise.reject(new Error("Unexpected usage"))},e.prototype.fmr=function(e,t){if(!this._foreignModule||"function"!==typeof this._foreignModule[e])return Promise.reject(new Error("Missing requestHandler or method: "+e));try{return Promise.resolve(this._foreignModule[e].apply(this._foreignModule,t))}catch(n){return Promise.reject(n)}},e._diffLimit=1e5,e._suggestionsLimit=1e4,e}();"function"===typeof importScripts&&(f["b"].monaco=Object(N["a"])());var P=n("1b69"),D=n("7b4a"),W=n("3742"),F=n("d3d7"),H=n("e58e"),B=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),j=function(e,t,n,i){var r,o=arguments.length,s=o<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,n):i;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)s=Reflect.decorate(e,t,n,i);else for(var a=e.length-1;a>=0;a--)(r=e[a])&&(s=(o<3?r(s):o>3?r(t,n,s):r(t,n))||s);return o>3&&s&&Object.defineProperty(t,n,s),s},U=function(e,t){return function(n,i){t(n,i,e)}},z=function(e,t,n,i){function r(e){return e instanceof n?e:new n((function(t){t(e)}))}return new(n||(n=Promise))((function(n,o){function s(e){try{u(i.next(e))}catch(t){o(t)}}function a(e){try{u(i["throw"](e))}catch(t){o(t)}}function u(e){e.done?n(e.value):r(e.value).then(s,a)}u((i=i.apply(e,t||[])).next())}))},K=function(e,t){var n,i,r,o,s={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(e){return function(t){return u([e,t])}}function u(o){if(n)throw new TypeError("Generator is already executing.");while(s)try{if(n=1,i&&(r=2&o[0]?i["return"]:o[0]?i["throw"]||((r=i["return"])&&r.call(i),0):i.next)&&!(r=r.call(i,o[1])).done)return r;switch(i=0,r&&(o=[2&o[0],r.value]),o[0]){case 0:case 1:r=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,i=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(r=s.trys,!(r=r.length>0&&r[r.length-1])&&(6===o[0]||2===o[0])){s=0;continue}if(3===o[0]&&(!r||o[1]>r[0]&&o[1]<r[3])){s.label=o[1];break}if(6===o[0]&&s.label<r[1]){s.label=r[1],r=o;break}if(r&&s.label<r[2]){s.label=r[2],s.ops.push(o);break}r[2]&&s.ops.pop(),s.trys.pop();continue}o=t.call(e,s)}catch(a){o=[6,a],i=0}finally{n=r=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}},Y=6e4,G=3e5;function q(e,t){var n=e.getModel(t);return!!n&&!n.isTooLargeForSyncing()}var Z=function(e){function t(t,n,i){var r=e.call(this)||this;return r._modelService=t,r._workerManager=r._register(new X(r._modelService)),r._logService=i,r._register(u["s"].register("*",{provideLinks:function(e,t){return q(r._modelService,e.uri)?r._workerManager.withWorker().then((function(t){return t.computeLinks(e.uri)})).then((function(e){return e&&{links:e}})):Promise.resolve({links:[]})}})),r._register(u["d"].register("*",new Q(r._workerManager,n,r._modelService))),r}return B(t,e),t.prototype.dispose=function(){e.prototype.dispose.call(this)},t.prototype.canComputeDiff=function(e,t){return q(this._modelService,e)&&q(this._modelService,t)},t.prototype.computeDiff=function(e,t,n,i){return this._workerManager.withWorker().then((function(r){return r.computeDiff(e,t,n,i)}))},t.prototype.computeMoreMinimalEdits=function(e,t){var n=this;if(Object(c["q"])(t)){if(!q(this._modelService,e))return Promise.resolve(t);var i=H["a"].create(!0),r=this._workerManager.withWorker().then((function(n){return n.computeMoreMinimalEdits(e,t)}));return r.finally((function(){return n._logService.trace("FORMAT#computeMoreMinimalEdits",e.toString(!0),i.elapsed())})),r}return Promise.resolve(void 0)},t.prototype.canNavigateValueSet=function(e){return q(this._modelService,e)},t.prototype.navigateValueSet=function(e,t,n){return this._workerManager.withWorker().then((function(i){return i.navigateValueSet(e,t,n)}))},t.prototype.canComputeWordRanges=function(e){return q(this._modelService,e)},t.prototype.computeWordRanges=function(e,t){return this._workerManager.withWorker().then((function(n){return n.computeWordRanges(e,t)}))},t=j([U(0,P["a"]),U(1,D["a"]),U(2,F["a"])],t),t}(r["a"]),Q=function(){function e(e,t,n){this._debugDisplayName="wordbasedCompletions",this._workerManager=e,this._configurationService=t,this._modelService=n}return e.prototype.provideCompletionItems=function(e,t){return z(this,void 0,void 0,(function(){var n,i,r,o,s,u;return K(this,(function(l){switch(l.label){case 0:return n=this._configurationService.getValue(e.uri,t,"editor").wordBasedSuggestions,n?q(this._modelService,e.uri)?(i=e.getWordAtPosition(t),r=i?new a["a"](t.lineNumber,i.startColumn,t.lineNumber,i.endColumn):a["a"].fromPositions(t),o=r.setEndPosition(t.lineNumber,t.column),[4,this._workerManager.withWorker()]):[2,void 0]:[2,void 0];case 1:return s=l.sent(),[4,s.textualSuggest(e.uri,t)];case 2:return u=l.sent(),u?[2,{suggestions:u.map((function(e){return{kind:18,label:e,insertText:e,range:{insert:o,replace:r}}}))}]:[2,void 0]}}))}))},e}(),X=function(e){function t(t){var n=e.call(this)||this;n._modelService=t,n._editorWorkerClient=null,n._lastWorkerUsedTime=(new Date).getTime();var r=n._register(new i["c"]);return r.cancelAndSet((function(){return n._checkStopIdleWorker()}),Math.round(G/2)),n._register(n._modelService.onModelRemoved((function(e){return n._checkStopEmptyWorker()}))),n}return B(t,e),t.prototype.dispose=function(){this._editorWorkerClient&&(this._editorWorkerClient.dispose(),this._editorWorkerClient=null),e.prototype.dispose.call(this)},t.prototype._checkStopEmptyWorker=function(){if(this._editorWorkerClient){var e=this._modelService.getModels();0===e.length&&(this._editorWorkerClient.dispose(),this._editorWorkerClient=null)}},t.prototype._checkStopIdleWorker=function(){if(this._editorWorkerClient){var e=(new Date).getTime()-this._lastWorkerUsedTime;e>G&&(this._editorWorkerClient.dispose(),this._editorWorkerClient=null)}},t.prototype.withWorker=function(){return this._lastWorkerUsedTime=(new Date).getTime(),this._editorWorkerClient||(this._editorWorkerClient=new te(this._modelService,!1,"editorWorkerService")),Promise.resolve(this._editorWorkerClient)},t}(r["a"]),$=function(e){function t(t,n,r){var o=e.call(this)||this;if(o._syncedModels=Object.create(null),o._syncedModelsLastUsedTime=Object.create(null),o._proxy=t,o._modelService=n,!r){var s=new i["c"];s.cancelAndSet((function(){return o._checkStopModelSync()}),Math.round(Y/2)),o._register(s)}return o}return B(t,e),t.prototype.dispose=function(){for(var t in this._syncedModels)Object(r["f"])(this._syncedModels[t]);this._syncedModels=Object.create(null),this._syncedModelsLastUsedTime=Object.create(null),e.prototype.dispose.call(this)},t.prototype.ensureSyncedResources=function(e){for(var t=0,n=e;t<n.length;t++){var i=n[t],r=i.toString();this._syncedModels[r]||this._beginModelSync(i),this._syncedModels[r]&&(this._syncedModelsLastUsedTime[r]=(new Date).getTime())}},t.prototype._checkStopModelSync=function(){var e=(new Date).getTime(),t=[];for(var n in this._syncedModelsLastUsedTime){var i=e-this._syncedModelsLastUsedTime[n];i>Y&&t.push(n)}for(var r=0,o=t;r<o.length;r++){var s=o[r];this._stopModelSync(s)}},t.prototype._beginModelSync=function(e){var t=this,n=this._modelService.getModel(e);if(n&&!n.isTooLargeForSyncing()){var i=e.toString();this._proxy.acceptNewModel({url:n.uri.toString(),lines:n.getLinesContent(),EOL:n.getEOL(),versionId:n.getVersionId()});var o=new r["b"];o.add(n.onDidChangeContent((function(e){t._proxy.acceptModelChanged(i.toString(),e)}))),o.add(n.onWillDispose((function(){t._stopModelSync(i)}))),o.add(Object(r["h"])((function(){t._proxy.acceptRemovedModel(i)}))),this._syncedModels[i]=o}},t.prototype._stopModelSync=function(e){var t=this._syncedModels[e];delete this._syncedModels[e],delete this._syncedModelsLastUsedTime[e],Object(r["f"])(t)},t}(r["a"]),J=function(){function e(e){this._instance=e,this._proxyObj=Promise.resolve(this._instance)}return e.prototype.dispose=function(){this._instance.dispose()},e.prototype.getProxyObject=function(){return this._proxyObj},e}(),ee=function(){function e(e){this._workerClient=e}return e.prototype.fhr=function(e,t){return this._workerClient.fhr(e,t)},e}(),te=function(e){function t(t,n,i){var r=e.call(this)||this;return r._modelService=t,r._keepIdleModels=n,r._workerFactory=new s["a"](i),r._worker=null,r._modelManager=null,r}return B(t,e),t.prototype.fhr=function(e,t){throw new Error("Not implemented!")},t.prototype._getOrCreateWorker=function(){if(!this._worker)try{this._worker=this._register(new o["a"](this._workerFactory,"vs/editor/common/services/editorSimpleWorker",new ee(this)))}catch(e){Object(o["b"])(e),this._worker=new J(new V(new ee(this),null))}return this._worker},t.prototype._getProxy=function(){var e=this;return this._getOrCreateWorker().getProxyObject().then(void 0,(function(t){return Object(o["b"])(t),e._worker=new J(new V(new ee(e),null)),e._getOrCreateWorker().getProxyObject()}))},t.prototype._getOrCreateModelManager=function(e){return this._modelManager||(this._modelManager=this._register(new $(e,this._modelService,this._keepIdleModels))),this._modelManager},t.prototype._withSyncedResources=function(e){var t=this;return this._getProxy().then((function(n){return t._getOrCreateModelManager(n).ensureSyncedResources(e),n}))},t.prototype.computeDiff=function(e,t,n,i){return this._withSyncedResources([e,t]).then((function(r){return r.computeDiff(e.toString(),t.toString(),n,i)}))},t.prototype.computeMoreMinimalEdits=function(e,t){return this._withSyncedResources([e]).then((function(n){return n.computeMoreMinimalEdits(e.toString(),t)}))},t.prototype.computeLinks=function(e){return this._withSyncedResources([e]).then((function(t){return t.computeLinks(e.toString())}))},t.prototype.textualSuggest=function(e,t){var n=this;return this._withSyncedResources([e]).then((function(i){var r=n._modelService.getModel(e);if(!r)return null;var o=l["a"].getWordDefinition(r.getLanguageIdentifier().id),s=o.source,a=Object(W["H"])(o);return i.textualSuggest(e.toString(),t,s,a)}))},t.prototype.computeWordRanges=function(e,t){var n=this;return this._withSyncedResources([e]).then((function(i){var r=n._modelService.getModel(e);if(!r)return Promise.resolve(null);var o=l["a"].getWordDefinition(r.getLanguageIdentifier().id),s=o.source,a=Object(W["H"])(o);return i.computeWordRanges(e.toString(),t,s,a)}))},t.prototype.navigateValueSet=function(e,t,n){var i=this;return this._withSyncedResources([e]).then((function(r){var o=i._modelService.getModel(e);if(!o)return null;var s=l["a"].getWordDefinition(o.getLanguageIdentifier().id),a=s.source,u=Object(W["H"])(s);return r.navigateValueSet(e.toString(),t,n,a,u)}))},t}(r["a"])},5818:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var i=n("0a0f"),r=Object(i["c"])("modeService")},"5adc":function(e,t,n){"use strict";n.d(t,"a",(function(){return l})),n.d(t,"b",(function(){return p}));var i=n("ceb8"),r=function(){function e(e,t,n,i,r){this.token=e,this.index=t,this.fontStyle=n,this.foreground=i,this.background=r}return e}();function o(e){if(!e||!Array.isArray(e))return[];for(var t=[],n=0,i=0,o=e.length;i<o;i++){var s=e[i],a=-1;if("string"===typeof s.fontStyle){a=0;for(var u=s.fontStyle.split(" "),l=0,c=u.length;l<c;l++){var h=u[l];switch(h){case"italic":a|=1;break;case"bold":a|=2;break;case"underline":a|=4;break}}}var d=null;"string"===typeof s.foreground&&(d=s.foreground);var f=null;"string"===typeof s.background&&(f=s.background),t[n++]=new r(s.token||"",i,a,d,f)}return t}function s(e,t){e.sort((function(e,t){var n=d(e.token,t.token);return 0!==n?n:e.index-t.index}));var n=0,i="000000",r="ffffff";while(e.length>=1&&""===e[0].token){var o=e.shift();-1!==o.fontStyle&&(n=o.fontStyle),null!==o.foreground&&(i=o.foreground),null!==o.background&&(r=o.background)}for(var s=new u,a=0,c=t;a<c.length;a++){var h=c[a];s.getId(h)}for(var p=s.getId(i),m=s.getId(r),_=new f(n,p,m),v=new g(_),y=0,b=e.length;y<b;y++){var C=e[y];v.insert(C.token,C.fontStyle,s.getId(C.foreground),s.getId(C.background))}return new l(s,v)}var a=/^#?([0-9A-Fa-f]{6})([0-9A-Fa-f]{2})?$/,u=function(){function e(){this._lastColorId=0,this._id2color=[],this._color2id=new Map}return e.prototype.getId=function(e){if(null===e)return 0;var t=e.match(a);if(!t)throw new Error("Illegal value for token color: "+e);e=t[1].toUpperCase();var n=this._color2id.get(e);return n||(n=++this._lastColorId,this._color2id.set(e,n),this._id2color[n]=i["a"].fromHex("#"+e),n)},e.prototype.getColorMap=function(){return this._id2color.slice(0)},e}(),l=function(){function e(e,t){this._colorMap=e,this._root=t,this._cache=new Map}return e.createFromRawTokenTheme=function(e,t){return this.createFromParsedTokenTheme(o(e),t)},e.createFromParsedTokenTheme=function(e,t){return s(e,t)},e.prototype.getColorMap=function(){return this._colorMap.getColorMap()},e.prototype._match=function(e){return this._root.match(e)},e.prototype.match=function(e,t){var n=this._cache.get(t);if("undefined"===typeof n){var i=this._match(t),r=h(t);n=(i.metadata|r<<8)>>>0,this._cache.set(t,n)}return(n|e<<0)>>>0},e}(),c=/\b(comment|string|regex|regexp)\b/;function h(e){var t=e.match(c);if(!t)return 0;switch(t[1]){case"comment":return 1;case"string":return 2;case"regex":return 4;case"regexp":return 4}throw new Error("Unexpected match for standard token type!")}function d(e,t){return e<t?-1:e>t?1:0}var f=function(){function e(e,t,n){this._fontStyle=e,this._foreground=t,this._background=n,this.metadata=(this._fontStyle<<11|this._foreground<<14|this._background<<23)>>>0}return e.prototype.clone=function(){return new e(this._fontStyle,this._foreground,this._background)},e.prototype.acceptOverwrite=function(e,t,n){-1!==e&&(this._fontStyle=e),0!==t&&(this._foreground=t),0!==n&&(this._background=n),this.metadata=(this._fontStyle<<11|this._foreground<<14|this._background<<23)>>>0},e}(),g=function(){function e(e){this._mainRule=e,this._children=new Map}return e.prototype.match=function(e){if(""===e)return this._mainRule;var t,n,i=e.indexOf(".");-1===i?(t=e,n=""):(t=e.substring(0,i),n=e.substring(i+1));var r=this._children.get(t);return"undefined"!==typeof r?r.match(n):this._mainRule},e.prototype.insert=function(t,n,i,r){if(""!==t){var o,s,a=t.indexOf(".");-1===a?(o=t,s=""):(o=t.substring(0,a),s=t.substring(a+1));var u=this._children.get(o);"undefined"===typeof u&&(u=new e(this._mainRule.clone()),this._children.set(o,u)),u.insert(s,n,i,r)}else this._mainRule.acceptOverwrite(n,i,r)},e}();function p(e){for(var t=[],n=1,i=e.length;n<i;n++){var r=e[n];t[n]=".mtk"+n+" { color: "+r+"; }"}return t.push(".mtki { font-style: italic; }"),t.push(".mtkb { font-weight: bold; }"),t.push(".mtku { text-decoration: underline; text-underline-position: under; }"),t.join("\n")}},6881:function(e,t,n){"use strict";var i,r,o,s,a,u,l,c,h,d,f,g,p,m,_,v,y,b,C,L,w,S,O,I,M,k,N,T,E,A,x,R,V;n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return o})),n.d(t,"d",(function(){return s})),n.d(t,"e",(function(){return a})),n.d(t,"f",(function(){return u})),n.d(t,"g",(function(){return l})),n.d(t,"h",(function(){return c})),n.d(t,"i",(function(){return h})),n.d(t,"j",(function(){return d})),n.d(t,"k",(function(){return f})),n.d(t,"l",(function(){return g})),n.d(t,"m",(function(){return p})),n.d(t,"n",(function(){return m})),n.d(t,"o",(function(){return _})),n.d(t,"p",(function(){return v})),n.d(t,"q",(function(){return y})),n.d(t,"r",(function(){return b})),n.d(t,"s",(function(){return C})),n.d(t,"t",(function(){return L})),n.d(t,"u",(function(){return w})),n.d(t,"v",(function(){return S})),n.d(t,"w",(function(){return O})),n.d(t,"x",(function(){return I})),n.d(t,"y",(function(){return M})),n.d(t,"z",(function(){return k})),n.d(t,"A",(function(){return N})),n.d(t,"B",(function(){return T})),n.d(t,"C",(function(){return E})),n.d(t,"D",(function(){return A})),n.d(t,"E",(function(){return x})),n.d(t,"F",(function(){return R})),n.d(t,"G",(function(){return V})),function(e){e[e["Unknown"]=0]="Unknown",e[e["Disabled"]=1]="Disabled",e[e["Enabled"]=2]="Enabled"}(i||(i={})),function(e){e[e["KeepWhitespace"]=1]="KeepWhitespace",e[e["InsertAsSnippet"]=4]="InsertAsSnippet"}(r||(r={})),function(e){e[e["Method"]=0]="Method",e[e["Function"]=1]="Function",e[e["Constructor"]=2]="Constructor",e[e["Field"]=3]="Field",e[e["Variable"]=4]="Variable",e[e["Class"]=5]="Class",e[e["Struct"]=6]="Struct",e[e["Interface"]=7]="Interface",e[e["Module"]=8]="Module",e[e["Property"]=9]="Property",e[e["Event"]=10]="Event",e[e["Operator"]=11]="Operator",e[e["Unit"]=12]="Unit",e[e["Value"]=13]="Value",e[e["Constant"]=14]="Constant",e[e["Enum"]=15]="Enum",e[e["EnumMember"]=16]="EnumMember",e[e["Keyword"]=17]="Keyword",e[e["Text"]=18]="Text",e[e["Color"]=19]="Color",e[e["File"]=20]="File",e[e["Reference"]=21]="Reference",e[e["Customcolor"]=22]="Customcolor",e[e["Folder"]=23]="Folder",e[e["TypeParameter"]=24]="TypeParameter",e[e["Snippet"]=25]="Snippet"}(o||(o={})),function(e){e[e["Deprecated"]=1]="Deprecated"}(s||(s={})),function(e){e[e["Invoke"]=0]="Invoke",e[e["TriggerCharacter"]=1]="TriggerCharacter",e[e["TriggerForIncompleteCompletions"]=2]="TriggerForIncompleteCompletions"}(a||(a={})),function(e){e[e["EXACT"]=0]="EXACT",e[e["ABOVE"]=1]="ABOVE",e[e["BELOW"]=2]="BELOW"}(u||(u={})),function(e){e[e["NotSet"]=0]="NotSet",e[e["ContentFlush"]=1]="ContentFlush",e[e["RecoverFromMarkers"]=2]="RecoverFromMarkers",e[e["Explicit"]=3]="Explicit",e[e["Paste"]=4]="Paste",e[e["Undo"]=5]="Undo",e[e["Redo"]=6]="Redo"}(l||(l={})),function(e){e[e["LF"]=1]="LF",e[e["CRLF"]=2]="CRLF"}(c||(c={})),function(e){e[e["Text"]=0]="Text",e[e["Read"]=1]="Read",e[e["Write"]=2]="Write"}(h||(h={})),function(e){e[e["None"]=0]="None",e[e["Keep"]=1]="Keep",e[e["Brackets"]=2]="Brackets",e[e["Advanced"]=3]="Advanced",e[e["Full"]=4]="Full"}(d||(d={})),function(e){e[e["acceptSuggestionOnCommitCharacter"]=0]="acceptSuggestionOnCommitCharacter",e[e["acceptSuggestionOnEnter"]=1]="acceptSuggestionOnEnter",e[e["accessibilitySupport"]=2]="accessibilitySupport",e[e["accessibilityPageSize"]=3]="accessibilityPageSize",e[e["ariaLabel"]=4]="ariaLabel",e[e["autoClosingBrackets"]=5]="autoClosingBrackets",e[e["autoClosingOvertype"]=6]="autoClosingOvertype",e[e["autoClosingQuotes"]=7]="autoClosingQuotes",e[e["autoIndent"]=8]="autoIndent",e[e["automaticLayout"]=9]="automaticLayout",e[e["autoSurround"]=10]="autoSurround",e[e["codeLens"]=11]="codeLens",e[e["colorDecorators"]=12]="colorDecorators",e[e["comments"]=13]="comments",e[e["contextmenu"]=14]="contextmenu",e[e["copyWithSyntaxHighlighting"]=15]="copyWithSyntaxHighlighting",e[e["cursorBlinking"]=16]="cursorBlinking",e[e["cursorSmoothCaretAnimation"]=17]="cursorSmoothCaretAnimation",e[e["cursorStyle"]=18]="cursorStyle",e[e["cursorSurroundingLines"]=19]="cursorSurroundingLines",e[e["cursorSurroundingLinesStyle"]=20]="cursorSurroundingLinesStyle",e[e["cursorWidth"]=21]="cursorWidth",e[e["disableLayerHinting"]=22]="disableLayerHinting",e[e["disableMonospaceOptimizations"]=23]="disableMonospaceOptimizations",e[e["dragAndDrop"]=24]="dragAndDrop",e[e["emptySelectionClipboard"]=25]="emptySelectionClipboard",e[e["extraEditorClassName"]=26]="extraEditorClassName",e[e["fastScrollSensitivity"]=27]="fastScrollSensitivity",e[e["find"]=28]="find",e[e["fixedOverflowWidgets"]=29]="fixedOverflowWidgets",e[e["folding"]=30]="folding",e[e["foldingStrategy"]=31]="foldingStrategy",e[e["foldingHighlight"]=32]="foldingHighlight",e[e["fontFamily"]=33]="fontFamily",e[e["fontInfo"]=34]="fontInfo",e[e["fontLigatures"]=35]="fontLigatures",e[e["fontSize"]=36]="fontSize",e[e["fontWeight"]=37]="fontWeight",e[e["formatOnPaste"]=38]="formatOnPaste",e[e["formatOnType"]=39]="formatOnType",e[e["glyphMargin"]=40]="glyphMargin",e[e["gotoLocation"]=41]="gotoLocation",e[e["hideCursorInOverviewRuler"]=42]="hideCursorInOverviewRuler",e[e["highlightActiveIndentGuide"]=43]="highlightActiveIndentGuide",e[e["hover"]=44]="hover",e[e["inDiffEditor"]=45]="inDiffEditor",e[e["letterSpacing"]=46]="letterSpacing",e[e["lightbulb"]=47]="lightbulb",e[e["lineDecorationsWidth"]=48]="lineDecorationsWidth",e[e["lineHeight"]=49]="lineHeight",e[e["lineNumbers"]=50]="lineNumbers",e[e["lineNumbersMinChars"]=51]="lineNumbersMinChars",e[e["links"]=52]="links",e[e["matchBrackets"]=53]="matchBrackets",e[e["minimap"]=54]="minimap",e[e["mouseStyle"]=55]="mouseStyle",e[e["mouseWheelScrollSensitivity"]=56]="mouseWheelScrollSensitivity",e[e["mouseWheelZoom"]=57]="mouseWheelZoom",e[e["multiCursorMergeOverlapping"]=58]="multiCursorMergeOverlapping",e[e["multiCursorModifier"]=59]="multiCursorModifier",e[e["multiCursorPaste"]=60]="multiCursorPaste",e[e["occurrencesHighlight"]=61]="occurrencesHighlight",e[e["overviewRulerBorder"]=62]="overviewRulerBorder",e[e["overviewRulerLanes"]=63]="overviewRulerLanes",e[e["parameterHints"]=64]="parameterHints",e[e["peekWidgetDefaultFocus"]=65]="peekWidgetDefaultFocus",e[e["quickSuggestions"]=66]="quickSuggestions",e[e["quickSuggestionsDelay"]=67]="quickSuggestionsDelay",e[e["readOnly"]=68]="readOnly",e[e["renderControlCharacters"]=69]="renderControlCharacters",e[e["renderIndentGuides"]=70]="renderIndentGuides",e[e["renderFinalNewline"]=71]="renderFinalNewline",e[e["renderLineHighlight"]=72]="renderLineHighlight",e[e["renderValidationDecorations"]=73]="renderValidationDecorations",e[e["renderWhitespace"]=74]="renderWhitespace",e[e["revealHorizontalRightPadding"]=75]="revealHorizontalRightPadding",e[e["roundedSelection"]=76]="roundedSelection",e[e["rulers"]=77]="rulers",e[e["scrollbar"]=78]="scrollbar",e[e["scrollBeyondLastColumn"]=79]="scrollBeyondLastColumn",e[e["scrollBeyondLastLine"]=80]="scrollBeyondLastLine",e[e["selectionClipboard"]=81]="selectionClipboard",e[e["selectionHighlight"]=82]="selectionHighlight",e[e["selectOnLineNumbers"]=83]="selectOnLineNumbers",e[e["showFoldingControls"]=84]="showFoldingControls",e[e["showUnused"]=85]="showUnused",e[e["snippetSuggestions"]=86]="snippetSuggestions",e[e["smoothScrolling"]=87]="smoothScrolling",e[e["stopRenderingLineAfter"]=88]="stopRenderingLineAfter",e[e["suggest"]=89]="suggest",e[e["suggestFontSize"]=90]="suggestFontSize",e[e["suggestLineHeight"]=91]="suggestLineHeight",e[e["suggestOnTriggerCharacters"]=92]="suggestOnTriggerCharacters",e[e["suggestSelection"]=93]="suggestSelection",e[e["tabCompletion"]=94]="tabCompletion",e[e["useTabStops"]=95]="useTabStops",e[e["wordSeparators"]=96]="wordSeparators",e[e["wordWrap"]=97]="wordWrap",e[e["wordWrapBreakAfterCharacters"]=98]="wordWrapBreakAfterCharacters",e[e["wordWrapBreakBeforeCharacters"]=99]="wordWrapBreakBeforeCharacters",e[e["wordWrapColumn"]=100]="wordWrapColumn",e[e["wordWrapMinified"]=101]="wordWrapMinified",e[e["wrappingIndent"]=102]="wrappingIndent",e[e["wrappingStrategy"]=103]="wrappingStrategy",e[e["editorClassName"]=104]="editorClassName",e[e["pixelRatio"]=105]="pixelRatio",e[e["tabFocusMode"]=106]="tabFocusMode",e[e["layoutInfo"]=107]="layoutInfo",e[e["wrappingInfo"]=108]="wrappingInfo"}(f||(f={})),function(e){e[e["TextDefined"]=0]="TextDefined",e[e["LF"]=1]="LF",e[e["CRLF"]=2]="CRLF"}(g||(g={})),function(e){e[e["LF"]=0]="LF",e[e["CRLF"]=1]="CRLF"}(p||(p={})),function(e){e[e["None"]=0]="None",e[e["Indent"]=1]="Indent",e[e["IndentOutdent"]=2]="IndentOutdent",e[e["Outdent"]=3]="Outdent"}(m||(m={})),function(e){e[e["Unknown"]=0]="Unknown",e[e["Backspace"]=1]="Backspace",e[e["Tab"]=2]="Tab",e[e["Enter"]=3]="Enter",e[e["Shift"]=4]="Shift",e[e["Ctrl"]=5]="Ctrl",e[e["Alt"]=6]="Alt",e[e["PauseBreak"]=7]="PauseBreak",e[e["CapsLock"]=8]="CapsLock",e[e["Escape"]=9]="Escape",e[e["Space"]=10]="Space",e[e["PageUp"]=11]="PageUp",e[e["PageDown"]=12]="PageDown",e[e["End"]=13]="End",e[e["Home"]=14]="Home",e[e["LeftArrow"]=15]="LeftArrow",e[e["UpArrow"]=16]="UpArrow",e[e["RightArrow"]=17]="RightArrow",e[e["DownArrow"]=18]="DownArrow",e[e["Insert"]=19]="Insert",e[e["Delete"]=20]="Delete",e[e["KEY_0"]=21]="KEY_0",e[e["KEY_1"]=22]="KEY_1",e[e["KEY_2"]=23]="KEY_2",e[e["KEY_3"]=24]="KEY_3",e[e["KEY_4"]=25]="KEY_4",e[e["KEY_5"]=26]="KEY_5",e[e["KEY_6"]=27]="KEY_6",e[e["KEY_7"]=28]="KEY_7",e[e["KEY_8"]=29]="KEY_8",e[e["KEY_9"]=30]="KEY_9",e[e["KEY_A"]=31]="KEY_A",e[e["KEY_B"]=32]="KEY_B",e[e["KEY_C"]=33]="KEY_C",e[e["KEY_D"]=34]="KEY_D",e[e["KEY_E"]=35]="KEY_E",e[e["KEY_F"]=36]="KEY_F",e[e["KEY_G"]=37]="KEY_G",e[e["KEY_H"]=38]="KEY_H",e[e["KEY_I"]=39]="KEY_I",e[e["KEY_J"]=40]="KEY_J",e[e["KEY_K"]=41]="KEY_K",e[e["KEY_L"]=42]="KEY_L",e[e["KEY_M"]=43]="KEY_M",e[e["KEY_N"]=44]="KEY_N",e[e["KEY_O"]=45]="KEY_O",e[e["KEY_P"]=46]="KEY_P",e[e["KEY_Q"]=47]="KEY_Q",e[e["KEY_R"]=48]="KEY_R",e[e["KEY_S"]=49]="KEY_S",e[e["KEY_T"]=50]="KEY_T",e[e["KEY_U"]=51]="KEY_U",e[e["KEY_V"]=52]="KEY_V",e[e["KEY_W"]=53]="KEY_W",e[e["KEY_X"]=54]="KEY_X",e[e["KEY_Y"]=55]="KEY_Y",e[e["KEY_Z"]=56]="KEY_Z",e[e["Meta"]=57]="Meta",e[e["ContextMenu"]=58]="ContextMenu",e[e["F1"]=59]="F1",e[e["F2"]=60]="F2",e[e["F3"]=61]="F3",e[e["F4"]=62]="F4",e[e["F5"]=63]="F5",e[e["F6"]=64]="F6",e[e["F7"]=65]="F7",e[e["F8"]=66]="F8",e[e["F9"]=67]="F9",e[e["F10"]=68]="F10",e[e["F11"]=69]="F11",e[e["F12"]=70]="F12",e[e["F13"]=71]="F13",e[e["F14"]=72]="F14",e[e["F15"]=73]="F15",e[e["F16"]=74]="F16",e[e["F17"]=75]="F17",e[e["F18"]=76]="F18",e[e["F19"]=77]="F19",e[e["NumLock"]=78]="NumLock",e[e["ScrollLock"]=79]="ScrollLock",e[e["US_SEMICOLON"]=80]="US_SEMICOLON",e[e["US_EQUAL"]=81]="US_EQUAL",e[e["US_COMMA"]=82]="US_COMMA",e[e["US_MINUS"]=83]="US_MINUS",e[e["US_DOT"]=84]="US_DOT",e[e["US_SLASH"]=85]="US_SLASH",e[e["US_BACKTICK"]=86]="US_BACKTICK",e[e["US_OPEN_SQUARE_BRACKET"]=87]="US_OPEN_SQUARE_BRACKET",e[e["US_BACKSLASH"]=88]="US_BACKSLASH",e[e["US_CLOSE_SQUARE_BRACKET"]=89]="US_CLOSE_SQUARE_BRACKET",e[e["US_QUOTE"]=90]="US_QUOTE",e[e["OEM_8"]=91]="OEM_8",e[e["OEM_102"]=92]="OEM_102",e[e["NUMPAD_0"]=93]="NUMPAD_0",e[e["NUMPAD_1"]=94]="NUMPAD_1",e[e["NUMPAD_2"]=95]="NUMPAD_2",e[e["NUMPAD_3"]=96]="NUMPAD_3",e[e["NUMPAD_4"]=97]="NUMPAD_4",e[e["NUMPAD_5"]=98]="NUMPAD_5",e[e["NUMPAD_6"]=99]="NUMPAD_6",e[e["NUMPAD_7"]=100]="NUMPAD_7",e[e["NUMPAD_8"]=101]="NUMPAD_8",e[e["NUMPAD_9"]=102]="NUMPAD_9",e[e["NUMPAD_MULTIPLY"]=103]="NUMPAD_MULTIPLY",e[e["NUMPAD_ADD"]=104]="NUMPAD_ADD",e[e["NUMPAD_SEPARATOR"]=105]="NUMPAD_SEPARATOR",e[e["NUMPAD_SUBTRACT"]=106]="NUMPAD_SUBTRACT",e[e["NUMPAD_DECIMAL"]=107]="NUMPAD_DECIMAL",e[e["NUMPAD_DIVIDE"]=108]="NUMPAD_DIVIDE",e[e["KEY_IN_COMPOSITION"]=109]="KEY_IN_COMPOSITION",e[e["ABNT_C1"]=110]="ABNT_C1",e[e["ABNT_C2"]=111]="ABNT_C2",e[e["MAX_VALUE"]=112]="MAX_VALUE"}(_||(_={})),function(e){e[e["Hint"]=1]="Hint",e[e["Info"]=2]="Info",e[e["Warning"]=4]="Warning",e[e["Error"]=8]="Error"}(v||(v={})),function(e){e[e["Unnecessary"]=1]="Unnecessary",e[e["Deprecated"]=2]="Deprecated"}(y||(y={})),function(e){e[e["Inline"]=1]="Inline",e[e["Gutter"]=2]="Gutter"}(b||(b={})),function(e){e[e["UNKNOWN"]=0]="UNKNOWN",e[e["TEXTAREA"]=1]="TEXTAREA",e[e["GUTTER_GLYPH_MARGIN"]=2]="GUTTER_GLYPH_MARGIN",e[e["GUTTER_LINE_NUMBERS"]=3]="GUTTER_LINE_NUMBERS",e[e["GUTTER_LINE_DECORATIONS"]=4]="GUTTER_LINE_DECORATIONS",e[e["GUTTER_VIEW_ZONE"]=5]="GUTTER_VIEW_ZONE",e[e["CONTENT_TEXT"]=6]="CONTENT_TEXT",e[e["CONTENT_EMPTY"]=7]="CONTENT_EMPTY",e[e["CONTENT_VIEW_ZONE"]=8]="CONTENT_VIEW_ZONE",e[e["CONTENT_WIDGET"]=9]="CONTENT_WIDGET",e[e["OVERVIEW_RULER"]=10]="OVERVIEW_RULER",e[e["SCROLLBAR"]=11]="SCROLLBAR",e[e["OVERLAY_WIDGET"]=12]="OVERLAY_WIDGET",e[e["OUTSIDE_EDITOR"]=13]="OUTSIDE_EDITOR"}(C||(C={})),function(e){e[e["TOP_RIGHT_CORNER"]=0]="TOP_RIGHT_CORNER",e[e["BOTTOM_RIGHT_CORNER"]=1]="BOTTOM_RIGHT_CORNER",e[e["TOP_CENTER"]=2]="TOP_CENTER"}(L||(L={})),function(e){e[e["Left"]=1]="Left",e[e["Center"]=2]="Center",e[e["Right"]=4]="Right",e[e["Full"]=7]="Full"}(w||(w={})),function(e){e[e["Off"]=0]="Off",e[e["On"]=1]="On",e[e["Relative"]=2]="Relative",e[e["Interval"]=3]="Interval",e[e["Custom"]=4]="Custom"}(S||(S={})),function(e){e[e["None"]=0]="None",e[e["Text"]=1]="Text",e[e["Blocks"]=2]="Blocks"}(O||(O={})),function(e){e[e["Smooth"]=0]="Smooth",e[e["Immediate"]=1]="Immediate"}(I||(I={})),function(e){e[e["Auto"]=1]="Auto",e[e["Hidden"]=2]="Hidden",e[e["Visible"]=3]="Visible"}(M||(M={})),function(e){e[e["LTR"]=0]="LTR",e[e["RTL"]=1]="RTL"}(k||(k={})),function(e){e[e["Invoke"]=1]="Invoke",e[e["TriggerCharacter"]=2]="TriggerCharacter",e[e["ContentChange"]=3]="ContentChange"}(N||(N={})),function(e){e[e["File"]=0]="File",e[e["Module"]=1]="Module",e[e["Namespace"]=2]="Namespace",e[e["Package"]=3]="Package",e[e["Class"]=4]="Class",e[e["Method"]=5]="Method",e[e["Property"]=6]="Property",e[e["Field"]=7]="Field",e[e["Constructor"]=8]="Constructor",e[e["Enum"]=9]="Enum",e[e["Interface"]=10]="Interface",e[e["Function"]=11]="Function",e[e["Variable"]=12]="Variable",e[e["Constant"]=13]="Constant",e[e["String"]=14]="String",e[e["Number"]=15]="Number",e[e["Boolean"]=16]="Boolean",e[e["Array"]=17]="Array",e[e["Object"]=18]="Object",e[e["Key"]=19]="Key",e[e["Null"]=20]="Null",e[e["EnumMember"]=21]="EnumMember",e[e["Struct"]=22]="Struct",e[e["Event"]=23]="Event",e[e["Operator"]=24]="Operator",e[e["TypeParameter"]=25]="TypeParameter"}(T||(T={})),function(e){e[e["Deprecated"]=1]="Deprecated"}(E||(E={})),function(e){e[e["Hidden"]=0]="Hidden",e[e["Blink"]=1]="Blink",e[e["Smooth"]=2]="Smooth",e[e["Phase"]=3]="Phase",e[e["Expand"]=4]="Expand",e[e["Solid"]=5]="Solid"}(A||(A={})),function(e){e[e["Line"]=1]="Line",e[e["Block"]=2]="Block",e[e["Underline"]=3]="Underline",e[e["LineThin"]=4]="LineThin",e[e["BlockOutline"]=5]="BlockOutline",e[e["UnderlineThin"]=6]="UnderlineThin"}(x||(x={})),function(e){e[e["AlwaysGrowsWhenTypingAtEdges"]=0]="AlwaysGrowsWhenTypingAtEdges",e[e["NeverGrowsWhenTypingAtEdges"]=1]="NeverGrowsWhenTypingAtEdges",e[e["GrowsOnlyWhenTypingBefore"]=2]="GrowsOnlyWhenTypingBefore",e[e["GrowsOnlyWhenTypingAfter"]=3]="GrowsOnlyWhenTypingAfter"}(R||(R={})),function(e){e[e["None"]=0]="None",e[e["Same"]=1]="Same",e[e["Indent"]=2]="Indent",e[e["DeepIndent"]=3]="DeepIndent"}(V||(V={}))},"6a5d":function(e,t,n){"use strict";n.d(t,"a",(function(){return l}));var i=n("308f"),r=n("a666"),o=n("9768");function s(e,t,n,i){if(Array.isArray(e)){for(var r=0,a=0,u=e;a<u.length;a++){var l=u[a],c=s(l,t,n,i);if(10===c)return c;c>r&&(r=c)}return r}if("string"===typeof e)return i?"*"===e?5:e===n?10:0:0;if(e){var h=e.language,d=e.pattern,f=e.scheme,g=e.hasAccessToAllModels;if(!i&&!g)return 0;r=0;if(f)if(f===t.scheme)r=10;else{if("*"!==f)return 0;r=5}if(h)if(h===n)r=10;else{if("*"!==h)return 0;r=Math.max(r,5)}if(d){if(d!==t.fsPath&&!Object(o["a"])(d,t.fsPath))return 0;r=10}return r}return 0}var a=n("1b69");function u(e){return"string"!==typeof e&&(Array.isArray(e)?e.every(u):!!e.exclusive)}var l=function(){function e(){this._clock=0,this._entries=[],this._onDidChange=new i["a"]}return Object.defineProperty(e.prototype,"onDidChange",{get:function(){return this._onDidChange.event},enumerable:!0,configurable:!0}),e.prototype.register=function(e,t){var n=this,i={selector:e,provider:t,_score:-1,_time:this._clock++};return this._entries.push(i),this._lastCandidate=void 0,this._onDidChange.fire(this._entries.length),Object(r["h"])((function(){if(i){var e=n._entries.indexOf(i);e>=0&&(n._entries.splice(e,1),n._lastCandidate=void 0,n._onDidChange.fire(n._entries.length),i=void 0)}}))},e.prototype.has=function(e){return this.all(e).length>0},e.prototype.all=function(e){if(!e)return[];this._updateScores(e);for(var t=[],n=0,i=this._entries;n<i.length;n++){var r=i[n];r._score>0&&t.push(r.provider)}return t},e.prototype.ordered=function(e){var t=[];return this._orderedForEach(e,(function(e){return t.push(e.provider)})),t},e.prototype.orderedGroups=function(e){var t,n,i=[];return this._orderedForEach(e,(function(e){t&&n===e._score?t.push(e.provider):(n=e._score,t=[e.provider],i.push(t))})),i},e.prototype._orderedForEach=function(e,t){if(e){this._updateScores(e);for(var n=0,i=this._entries;n<i.length;n++){var r=i[n];r._score>0&&t(r)}}},e.prototype._updateScores=function(t){var n={uri:t.uri.toString(),language:t.getLanguageIdentifier().language};if(!this._lastCandidate||this._lastCandidate.language!==n.language||this._lastCandidate.uri!==n.uri){this._lastCandidate=n;for(var i=0,r=this._entries;i<r.length;i++){var o=r[i];if(o._score=s(o.selector,t.uri,t.getLanguageIdentifier().language,Object(a["b"])(t)),u(o.selector)&&o._score>0){for(var l=0,c=this._entries;l<c.length;l++){var h=c[l];h._score=0}o._score=1e3;break}}this._entries.sort(e._compareByScoreAndTime)}},e._compareByScoreAndTime=function(e,t){return e._score<t._score?1:e._score>t._score?-1:e._time<t._time?1:e._time>t._time?-1:0},e}()},"6da2":function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return u})),n.d(t,"a",(function(){return l})),n.d(t,"d",(function(){return h})),n.d(t,"e",(function(){return f}));var i=n("3742"),r=n("7ab3"),o=n("7416"),s=function(){function e(e,t){this.endIndex=e,this.type=t}return e}(),a=function(){function e(e,t){this.startOffset=e,this.endOffset=t}return e.prototype.equals=function(e){return this.startOffset===e.startOffset&&this.endOffset===e.endOffset},e}(),u=function(){function e(e,t,n,i,r,o,s,a,u,l,c,h,d,f,g,p,m,_){this.useMonospaceOptimizations=e,this.canUseHalfwidthRightwardsArrow=t,this.lineContent=n,this.continuesWithWrappedLine=i,this.isBasicASCII=r,this.containsRTL=o,this.fauxIndentLength=s,this.lineTokens=a,this.lineDecorations=u,this.tabSize=l,this.startVisibleColumn=c,this.spaceWidth=h,this.middotWidth=d,this.stopRenderingLineAfter=f,this.renderWhitespace="all"===g?3:"boundary"===g?1:"selection"===g?2:0,this.renderControlCharacters=p,this.fontLigatures=m,this.selectionsOnLine=_&&_.sort((function(e,t){return e.startOffset<t.startOffset?-1:1}))}return e.prototype.sameSelection=function(e){if(null===this.selectionsOnLine)return null===e;if(null===e)return!1;if(e.length!==this.selectionsOnLine.length)return!1;for(var t=0;t<this.selectionsOnLine.length;t++)if(!this.selectionsOnLine[t].equals(e[t]))return!1;return!0},e.prototype.equals=function(e){return this.useMonospaceOptimizations===e.useMonospaceOptimizations&&this.canUseHalfwidthRightwardsArrow===e.canUseHalfwidthRightwardsArrow&&this.lineContent===e.lineContent&&this.continuesWithWrappedLine===e.continuesWithWrappedLine&&this.isBasicASCII===e.isBasicASCII&&this.containsRTL===e.containsRTL&&this.fauxIndentLength===e.fauxIndentLength&&this.tabSize===e.tabSize&&this.startVisibleColumn===e.startVisibleColumn&&this.spaceWidth===e.spaceWidth&&this.stopRenderingLineAfter===e.stopRenderingLineAfter&&this.renderWhitespace===e.renderWhitespace&&this.renderControlCharacters===e.renderControlCharacters&&this.fontLigatures===e.fontLigatures&&o["a"].equalsArr(this.lineDecorations,e.lineDecorations)&&this.lineTokens.equals(e.lineTokens)&&this.sameSelection(e.selectionsOnLine)},e}(),l=function(){function e(e,t){this.length=e,this._data=new Uint32Array(this.length),this._absoluteOffsets=new Uint32Array(this.length)}return e.getPartIndex=function(e){return(4294901760&e)>>>16},e.getCharIndex=function(e){return(65535&e)>>>0},e.prototype.setPartData=function(e,t,n,i){var r=(t<<16|n<<0)>>>0;this._data[e]=r,this._absoluteOffsets[e]=i+n},e.prototype.getAbsoluteOffsets=function(){return this._absoluteOffsets},e.prototype.charOffsetToPartData=function(e){return 0===this.length?0:e<0?this._data[0]:e>=this.length?this._data[this.length-1]:this._data[e]},e.prototype.partDataToCharOffset=function(t,n,i){if(0===this.length)return 0;var r=(t<<16|i<<0)>>>0,o=0,s=this.length-1;while(o+1<s){var a=o+s>>>1,u=this._data[a];if(u===r)return a;u>r?s=a:o=a}if(o===s)return o;var l=this._data[o],c=this._data[s];if(l===r)return o;if(c===r)return s;var h,d=e.getPartIndex(l),f=e.getCharIndex(l),g=e.getPartIndex(c);h=d!==g?n:e.getCharIndex(c);var p=i-f,m=h-i;return p<=m?o:s},e}(),c=function(){function e(e,t,n){this.characterMapping=e,this.containsRTL=t,this.containsForeignElements=n}return e}();function h(e,t){if(0===e.lineContent.length){var n=0,i="<span><span> </span></span>";if(e.lineDecorations.length>0){for(var r=[],o=[],s=0,a=e.lineDecorations.length;s<a;s++){var u=e.lineDecorations[s];1===u.type&&(r.push(e.lineDecorations[s].className),n|=1),2===u.type&&(o.push(e.lineDecorations[s].className),n|=2)}if(0!==n){var h=r.length>0?'<span class="'+r.join(" ")+'"></span>':"",d=o.length>0?'<span class="'+o.join(" ")+'"></span>':"";i="<span>"+h+d+"</span>"}}return t.appendASCIIString(i),new c(new l(0,0),!1,n)}return b(p(e),t)}var d=function(){function e(e,t,n,i){this.characterMapping=e,this.html=t,this.containsRTL=n,this.containsForeignElements=i}return e}();function f(e){var t=Object(r["a"])(1e4),n=h(e,t);return new d(n.characterMapping,t.build(),n.containsRTL,n.containsForeignElements)}var g=function(){function e(e,t,n,i,r,o,s,a,u,l,c,h,d,f,g){this.fontIsMonospace=e,this.canUseHalfwidthRightwardsArrow=t,this.lineContent=n,this.len=i,this.isOverflowing=r,this.parts=o,this.containsForeignElements=s,this.fauxIndentLength=a,this.tabSize=u,this.startVisibleColumn=l,this.containsRTL=c,this.spaceWidth=h,this.middotWidth=d,this.renderWhitespace=f,this.renderControlCharacters=g}return e}();function p(e){var t,n,i=e.useMonospaceOptimizations,r=e.lineContent;-1!==e.stopRenderingLineAfter&&e.stopRenderingLineAfter<r.length?(t=!0,n=e.stopRenderingLineAfter):(t=!1,n=r.length);var o=m(e.lineTokens,e.fauxIndentLength,n);(3===e.renderWhitespace||1===e.renderWhitespace||2===e.renderWhitespace&&e.selectionsOnLine)&&(o=v(r,n,e.continuesWithWrappedLine,o,e.fauxIndentLength,e.tabSize,e.startVisibleColumn,i,e.selectionsOnLine,1===e.renderWhitespace));var s=0;if(e.lineDecorations.length>0){for(var a=0,u=e.lineDecorations.length;a<u;a++){var l=e.lineDecorations[a];3===l.type||1===l.type?s|=1:2===l.type&&(s|=2)}o=y(r,n,o,e.lineDecorations)}return e.containsRTL||(o=_(r,o,!e.isBasicASCII||e.fontLigatures)),new g(i,e.canUseHalfwidthRightwardsArrow,r,n,t,o,s,e.fauxIndentLength,e.tabSize,e.startVisibleColumn,e.containsRTL,e.spaceWidth,e.middotWidth,e.renderWhitespace,e.renderControlCharacters)}function m(e,t,n){var i=[],r=0;t>0&&(i[r++]=new s(t,""));for(var o=0,a=e.getCount();o<a;o++){var u=e.getEndOffset(o);if(!(u<=t)){var l=e.getClassName(o);if(u>=n){i[r++]=new s(n,l);break}i[r++]=new s(u,l)}}return i}function _(e,t,n){var i=0,r=[],o=0;if(n)for(var a=0,u=t.length;a<u;a++){var l=t[a],c=l.endIndex;if(i+50<c){for(var h=l.type,d=-1,f=i,g=i;g<c;g++)32===e.charCodeAt(g)&&(d=g),-1!==d&&g-f>=50&&(r[o++]=new s(d+1,h),f=d+1,d=-1);f!==c&&(r[o++]=new s(c,h))}else r[o++]=l;i=c}else for(a=0,u=t.length;a<u;a++){l=t[a],c=l.endIndex;var p=c-i;if(p>50){h=l.type;var m=Math.ceil(p/50);for(g=1;g<m;g++){var _=i+50*g;r[o++]=new s(_,h)}r[o++]=new s(c,h)}else r[o++]=l;i=c}return r}function v(e,t,n,r,o,a,u,l,c,h){var d,f=[],g=0,p=0,m=r[p].type,_=r[p].endIndex,v=r.length,y=i["q"](e);-1===y?(y=t,d=t):d=i["D"](e);for(var b=!1,C=0,L=c&&c[C],w=u%a,S=o;S<t;S++){var O=e.charCodeAt(S);L&&S>=L.endOffset&&(C++,L=c&&c[C]);var I=void 0;if(S<y||S>d)I=!0;else if(9===O)I=!0;else if(32===O)if(h)if(b)I=!0;else{var M=S+1<t?e.charCodeAt(S+1):0;I=32===M||9===M}else I=!0;else I=!1;I&&c&&(I=!!L&&L.startOffset<=S&&L.endOffset>S),b?(!I||!l&&w>=a)&&(f[g++]=new s(S,"vs-whitespace"),w%=a):(S===_||I&&S>o)&&(f[g++]=new s(S,m),w%=a),9===O?w=a:i["y"](O)?w+=2:w++,b=I,S===_&&(p++,p<v&&(m=r[p].type,_=r[p].endIndex))}var k=!1;if(b)if(n&&h){var N=t>0?e.charCodeAt(t-1):0,T=t>1?e.charCodeAt(t-2):0,E=32===N&&32!==T&&9!==T;E||(k=!0)}else k=!0;return f[g++]=new s(t,k?"vs-whitespace":m),f}function y(e,t,n,i){i.sort(o["a"].compare);for(var r=o["b"].normalize(e,i),a=r.length,u=0,l=[],c=0,h=0,d=0,f=n.length;d<f;d++){var g=n[d],p=g.endIndex,m=g.type;while(u<a&&r[u].startOffset<p){var _=r[u];if(_.startOffset>h&&(h=_.startOffset,l[c++]=new s(h,m)),!(_.endOffset+1<=p)){h=p,l[c++]=new s(h,m+" "+_.className);break}h=_.endOffset+1,l[c++]=new s(h,m+" "+_.className),u++}p>h&&(h=p,l[c++]=new s(h,m))}var v=n[n.length-1].endIndex;if(u<a&&r[u].startOffset===v){var y=[];while(u<a&&r[u].startOffset===v)y.push(r[u].className),u++;l[c++]=new s(h,y.join(" "))}return l}function b(e,t){var n=e.fontIsMonospace,r=e.canUseHalfwidthRightwardsArrow,o=e.containsForeignElements,s=e.lineContent,a=e.len,u=e.isOverflowing,h=e.parts,d=e.fauxIndentLength,f=e.tabSize,g=e.startVisibleColumn,p=e.containsRTL,m=e.spaceWidth,_=e.middotWidth,v=e.renderWhitespace,y=e.renderControlCharacters,b=_>m?11825:183,C=new l(a+1,h.length),L=0,w=g,S=0,O=0,I=0;t.appendASCIIString("<span>");for(var M=0,k=h.length;M<k;M++){I+=O;var N=h[M],T=N.endIndex,E=N.type,A=0!==v&&E.indexOf("vs-whitespace")>=0;if(S=0,t.appendASCIIString('<span class="'),t.appendASCIIString(E),t.appendASCII(34),A){for(var x=0,R=L,V=w;R<T;R++){var P=s.charCodeAt(R),D=0|(9===P?f-V%f:1);x+=D,R>=d&&(V+=D)}if(!n){var W="vs-whitespace"===E;!W&&o||(t.appendASCIIString(' style="display:inline-block;width:'),t.appendASCIIString(String(m*x)),t.appendASCIIString('px"'))}for(t.appendASCII(62);L<T;L++){C.setPartData(L,M,S,I);P=s.charCodeAt(L),D=void 0;if(9===P){D=f-w%f|0,!r||D>1?t.write1(8594):t.write1(65515);for(var F=2;F<=D;F++)t.write1(160)}else D=1,t.write1(b);S+=D,L>=d&&(w+=D)}O=x}else{x=0;for(p&&t.appendASCIIString(' dir="ltr"'),t.appendASCII(62);L<T;L++){C.setPartData(L,M,S,I);P=s.charCodeAt(L);var H=1;D=1;switch(P){case 9:H=f-w%f,D=H;for(F=1;F<=H;F++)t.write1(160);break;case 32:t.write1(160);break;case 60:t.appendASCIIString("&lt;");break;case 62:t.appendASCIIString("&gt;");break;case 38:t.appendASCIIString("&amp;");break;case 0:t.appendASCIIString("&#00;");break;case 65279:case 8232:t.write1(65533);break;default:i["y"](P)&&D++,y&&P<32?t.write1(9216+P):t.write1(P)}S+=H,x+=H,L>=d&&(w+=D)}O=x}t.appendASCIIString("</span>")}return C.setPartData(a,h.length-1,S,I),u&&t.appendASCIIString("<span>&hellip;</span>"),t.appendASCIIString("</span>"),new c(C,p,o)}},"6eab":function(e,t,n){"use strict";n.d(t,"a",(function(){return f}));n("5110");var i=n("2504"),r=n("308f"),o=n("fe45"),s=n("6d8e"),a=n("7061"),u=n("6a89"),l=n("8025"),c=n("4dc7"),h=n("6881"),d=function(){function e(){}return e.chord=function(e,t){return Object(o["a"])(e,t)},e.CtrlCmd=2048,e.Shift=1024,e.Alt=512,e.WinCtrl=256,e}();function f(){return{editor:void 0,languages:void 0,CancellationTokenSource:i["b"],Emitter:r["a"],KeyCode:h["o"],KeyMod:d,Position:a["a"],Range:u["a"],Selection:l["a"],SelectionDirection:h["z"],MarkerSeverity:h["p"],MarkerTag:h["q"],Uri:s["a"],Token:c["a"]}}},"70cb":function(e,t,n){"use strict";n.d(t,"a",(function(){return v}));var i=n("308f"),r=n("a666"),o=n("3742"),s=n("d093"),a=n("2837"),u=n("045b"),l=function(){function e(t){if(t.autoClosingPairs?this._autoClosingPairs=t.autoClosingPairs.map((function(e){return new a["b"](e)})):t.brackets?this._autoClosingPairs=t.brackets.map((function(e){return new a["b"]({open:e[0],close:e[1]})})):this._autoClosingPairs=[],t.__electricCharacterSupport&&t.__electricCharacterSupport.docComment){var n=t.__electricCharacterSupport.docComment;this._autoClosingPairs.push(new a["b"]({open:n.open,close:n.close||""}))}this._autoCloseBefore="string"===typeof t.autoCloseBefore?t.autoCloseBefore:e.DEFAULT_AUTOCLOSE_BEFORE_LANGUAGE_DEFINED,this._surroundingPairs=t.surroundingPairs||this._autoClosingPairs}return e.prototype.getAutoClosingPairs=function(){return this._autoClosingPairs},e.prototype.getAutoCloseBeforeSet=function(){return this._autoCloseBefore},e.shouldAutoClosePair=function(e,t,n){if(0===t.getTokenCount())return!0;var i=t.findTokenIndexAtOffset(n-2),r=t.getStandardTokenType(i);return e.isOK(r)},e.prototype.getSurroundingPairs=function(){return this._surroundingPairs},e.DEFAULT_AUTOCLOSE_BEFORE_LANGUAGE_DEFINED=";:.,=}])> \n\t",e}(),c=n("1080"),h=function(){function e(e){this._richEditBrackets=e}return e.prototype.getElectricCharacters=function(){var e=[];if(this._richEditBrackets)for(var t=0,n=this._richEditBrackets.brackets;t<n.length;t++)for(var i=n[t],r=0,o=i.close;r<o.length;r++){var s=o[r],a=s.charAt(s.length-1);e.push(a)}return e=e.filter((function(e,t,n){return n.indexOf(e)===t})),e},e.prototype.onElectricCharacter=function(e,t,n){if(!this._richEditBrackets||0===this._richEditBrackets.brackets.length)return null;var i=t.findTokenIndexAtOffset(n-1);if(Object(u["b"])(t.getStandardTokenType(i)))return null;var r=this._richEditBrackets.reversedRegex,o=t.getLineContent().substring(0,n-1)+e,s=c["a"].findPrevBracketInRange(r,1,o,0,o.length);if(!s)return null;var a=o.substring(s.startColumn-1,s.endColumn-1).toLowerCase(),l=this._richEditBrackets.textIsOpenBracket[a];if(l)return null;var h=t.getActualLineContentBefore(s.startColumn-1);return/^\s*$/.test(h)?{matchOpenBracket:a}:null},e}(),d=function(){function e(e){this._indentationRules=e}return e.prototype.shouldIncrease=function(e){return!!(this._indentationRules&&this._indentationRules.increaseIndentPattern&&this._indentationRules.increaseIndentPattern.test(e))},e.prototype.shouldDecrease=function(e){return!!(this._indentationRules&&this._indentationRules.decreaseIndentPattern&&this._indentationRules.decreaseIndentPattern.test(e))},e.prototype.shouldIndentNextLine=function(e){return!!(this._indentationRules&&this._indentationRules.indentNextLinePattern&&this._indentationRules.indentNextLinePattern.test(e))},e.prototype.shouldIgnore=function(e){return!!(this._indentationRules&&this._indentationRules.unIndentedLinePattern&&this._indentationRules.unIndentedLinePattern.test(e))},e.prototype.getIndentMetadata=function(e){var t=0;return this.shouldIncrease(e)&&(t+=1),this.shouldDecrease(e)&&(t+=2),this.shouldIndentNextLine(e)&&(t+=4),this.shouldIgnore(e)&&(t+=8),t},e}(),f=n("fdcc"),g=function(){function e(t){var n=this;t=t||{},t.brackets=t.brackets||[["(",")"],["{","}"],["[","]"]],this._brackets=[],t.brackets.forEach((function(t){var i=e._createOpenBracketRegExp(t[0]),r=e._createCloseBracketRegExp(t[1]);i&&r&&n._brackets.push({open:t[0],openRegExp:i,close:t[1],closeRegExp:r})})),this._regExpRules=t.onEnterRules||[]}return e.prototype.onEnter=function(e,t,n,i){if(e>=3)for(var r=0,o=this._regExpRules.length;r<o;r++){var s=this._regExpRules[r],u=[{reg:s.beforeText,text:n},{reg:s.afterText,text:i},{reg:s.oneLineAboveText,text:t}].every((function(e){return!e.reg||e.reg.test(e.text)}));if(u)return s.action}if(e>=2&&n.length>0&&i.length>0)for(r=0,o=this._brackets.length;r<o;r++){var l=this._brackets[r];if(l.openRegExp.test(n)&&l.closeRegExp.test(i))return{indentAction:a["a"].IndentOutdent}}if(e>=2&&n.length>0)for(r=0,o=this._brackets.length;r<o;r++){l=this._brackets[r];if(l.openRegExp.test(n))return{indentAction:a["a"].Indent}}return null},e._createOpenBracketRegExp=function(t){var n=o["p"](t);return/\B/.test(n.charAt(0))||(n="\\b"+n),n+="\\s*$",e._safeRegExp(n)},e._createCloseBracketRegExp=function(t){var n=o["p"](t);return/\B/.test(n.charAt(n.length-1))||(n+="\\b"),n="^\\s*"+n,e._safeRegExp(n)},e._safeRegExp=function(e){try{return new RegExp(e)}catch(t){return Object(f["e"])(t),null}},e}(),p=function(){function e(t,n,i){this._languageIdentifier=t,this._brackets=null,this._electricCharacter=null;var r=null;n&&(r=n._conf),this._conf=e._mergeConf(r,i),this._onEnterSupport=this._conf.brackets||this._conf.indentationRules||this._conf.onEnterRules?new g(this._conf):null,this.comments=e._handleComments(this._conf),this.characterPair=new l(this._conf),this.wordDefinition=this._conf.wordPattern||s["a"],this.indentationRules=this._conf.indentationRules,this._conf.indentationRules?this.indentRulesSupport=new d(this._conf.indentationRules):this.indentRulesSupport=null,this.foldingRules=this._conf.folding||{}}return Object.defineProperty(e.prototype,"brackets",{get:function(){return!this._brackets&&this._conf.brackets&&(this._brackets=new c["b"](this._languageIdentifier,this._conf.brackets)),this._brackets},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"electricCharacter",{get:function(){return this._electricCharacter||(this._electricCharacter=new h(this.brackets)),this._electricCharacter},enumerable:!0,configurable:!0}),e.prototype.onEnter=function(e,t,n,i){return this._onEnterSupport?this._onEnterSupport.onEnter(e,t,n,i):null},e._mergeConf=function(e,t){return{comments:e?t.comments||e.comments:t.comments,brackets:e?t.brackets||e.brackets:t.brackets,wordPattern:e?t.wordPattern||e.wordPattern:t.wordPattern,indentationRules:e?t.indentationRules||e.indentationRules:t.indentationRules,onEnterRules:e?t.onEnterRules||e.onEnterRules:t.onEnterRules,autoClosingPairs:e?t.autoClosingPairs||e.autoClosingPairs:t.autoClosingPairs,surroundingPairs:e?t.surroundingPairs||e.surroundingPairs:t.surroundingPairs,autoCloseBefore:e?t.autoCloseBefore||e.autoCloseBefore:t.autoCloseBefore,folding:e?t.folding||e.folding:t.folding,__electricCharacterSupport:e?t.__electricCharacterSupport||e.__electricCharacterSupport:t.__electricCharacterSupport}},e._handleComments=function(e){var t=e.comments;if(!t)return null;var n={};if(t.lineComment&&(n.lineCommentToken=t.lineComment),t.blockComment){var i=t.blockComment,r=i[0],o=i[1];n.blockCommentStartToken=r,n.blockCommentEndToken=o}return n},e}(),m=function(){function e(e){this.languageIdentifier=e}return e}(),_=function(){function e(){this._entries=new Map,this._onDidChange=new i["a"],this.onDidChange=this._onDidChange.event}return e.prototype.register=function(e,t){var n=this,i=this._getRichEditSupport(e.id),o=new p(e,i,t);return this._entries.set(e.id,o),this._onDidChange.fire(new m(e)),Object(r["h"])((function(){n._entries.get(e.id)===o&&(n._entries.set(e.id,i),n._onDidChange.fire(new m(e)))}))},e.prototype._getRichEditSupport=function(e){return this._entries.get(e)},e.prototype._getElectricCharacterSupport=function(e){var t=this._getRichEditSupport(e);return t&&t.electricCharacter||null},e.prototype.getElectricCharacters=function(e){var t=this._getElectricCharacterSupport(e);return t?t.getElectricCharacters():[]},e.prototype.onElectricCharacter=function(e,t,n){var i=Object(u["a"])(t,n-1),r=this._getElectricCharacterSupport(i.languageId);return r?r.onElectricCharacter(e,i,n-i.firstCharOffset):null},e.prototype.getComments=function(e){var t=this._getRichEditSupport(e);return t&&t.comments||null},e.prototype._getCharacterPairSupport=function(e){var t=this._getRichEditSupport(e);return t&&t.characterPair||null},e.prototype.getAutoClosingPairs=function(e){var t=this._getCharacterPairSupport(e);return t?t.getAutoClosingPairs():[]},e.prototype.getAutoCloseBeforeSet=function(e){var t=this._getCharacterPairSupport(e);return t?t.getAutoCloseBeforeSet():l.DEFAULT_AUTOCLOSE_BEFORE_LANGUAGE_DEFINED},e.prototype.getSurroundingPairs=function(e){var t=this._getCharacterPairSupport(e);return t?t.getSurroundingPairs():[]},e.prototype.shouldAutoClosePair=function(e,t,n){var i=Object(u["a"])(t,n-1);return l.shouldAutoClosePair(e,i,n-i.firstCharOffset)},e.prototype.getWordDefinition=function(e){var t=this._getRichEditSupport(e);return t?Object(s["c"])(t.wordDefinition||null):Object(s["c"])(null)},e.prototype.getFoldingRules=function(e){var t=this._getRichEditSupport(e);return t?t.foldingRules:{}},e.prototype.getIndentRulesSupport=function(e){var t=this._getRichEditSupport(e);return t&&t.indentRulesSupport||null},e.prototype.getPrecedingValidLine=function(e,t,n){var i=e.getLanguageIdAtPosition(t,0);if(t>1){var r=void 0,o=-1;for(r=t-1;r>=1;r--){if(e.getLanguageIdAtPosition(r,0)!==i)return o;var s=e.getLineContent(r);if(!n.shouldIgnore(s)&&!/^\s+$/.test(s)&&""!==s)return r;o=r}}return-1},e.prototype.getInheritIndentForLine=function(e,t,n,i){if(void 0===i&&(i=!0),e<4)return null;var r=this.getIndentRulesSupport(t.getLanguageIdentifier().id);if(!r)return null;if(n<=1)return{indentation:"",action:null};var s=this.getPrecedingValidLine(t,n,r);if(s<0)return null;if(s<1)return{indentation:"",action:null};var u=t.getLineContent(s);if(r.shouldIncrease(u)||r.shouldIndentNextLine(u))return{indentation:o["t"](u),action:a["a"].Indent,line:s};if(r.shouldDecrease(u))return{indentation:o["t"](u),action:null,line:s};if(1===s)return{indentation:o["t"](t.getLineContent(s)),action:null,line:s};var l=s-1,c=r.getIndentMetadata(t.getLineContent(l));if(!(3&c)&&4&c){for(var h=0,d=l-1;d>0;d--)if(!r.shouldIndentNextLine(t.getLineContent(d))){h=d;break}return{indentation:o["t"](t.getLineContent(h+1)),action:null,line:h+1}}if(i)return{indentation:o["t"](t.getLineContent(s)),action:null,line:s};for(d=s;d>0;d--){var f=t.getLineContent(d);if(r.shouldIncrease(f))return{indentation:o["t"](f),action:a["a"].Indent,line:d};if(r.shouldIndentNextLine(f)){h=0;for(var g=d-1;g>0;g--)if(!r.shouldIndentNextLine(t.getLineContent(d))){h=g;break}return{indentation:o["t"](t.getLineContent(h+1)),action:null,line:h+1}}if(r.shouldDecrease(f))return{indentation:o["t"](f),action:null,line:d}}return{indentation:o["t"](t.getLineContent(1)),action:null,line:1}},e.prototype.getGoodIndentForLine=function(e,t,n,i,r){if(e<4)return null;var s=this._getRichEditSupport(n);if(!s)return null;var u=this.getIndentRulesSupport(n);if(!u)return null;var l=this.getInheritIndentForLine(e,t,i),c=t.getLineContent(i);if(l){var h=l.line;if(void 0!==h){var d=s.onEnter(e,"",t.getLineContent(h),"");if(d){var f=o["t"](t.getLineContent(h));return d.removeText&&(f=f.substring(0,f.length-d.removeText)),d.indentAction===a["a"].Indent||d.indentAction===a["a"].IndentOutdent?f=r.shiftIndent(f):d.indentAction===a["a"].Outdent&&(f=r.unshiftIndent(f)),u.shouldDecrease(c)&&(f=r.unshiftIndent(f)),d.appendText&&(f+=d.appendText),o["t"](f)}}return u.shouldDecrease(c)?l.action===a["a"].Indent?l.indentation:r.unshiftIndent(l.indentation):l.action===a["a"].Indent?r.shiftIndent(l.indentation):l.indentation}return null},e.prototype.getIndentForEnter=function(e,t,n,i){if(e<4)return null;t.forceTokenization(n.startLineNumber);var r,s,l=t.getLineTokens(n.startLineNumber),c=Object(u["a"])(l,n.startColumn-1),h=c.getLineContent(),d=!1;if(c.firstCharOffset>0&&l.getLanguageId(0)!==c.languageId?(d=!0,r=h.substr(0,n.startColumn-1-c.firstCharOffset)):r=l.getLineContent().substring(0,n.startColumn-1),n.isEmpty())s=h.substr(n.startColumn-1-c.firstCharOffset);else{var f=this.getScopedLineTokens(t,n.endLineNumber,n.endColumn);s=f.getLineContent().substr(n.endColumn-1-c.firstCharOffset)}var g=this.getIndentRulesSupport(c.languageId);if(!g)return null;var p=r,m=o["t"](r),_={getLineTokens:function(e){return t.getLineTokens(e)},getLanguageIdentifier:function(){return t.getLanguageIdentifier()},getLanguageIdAtPosition:function(e,n){return t.getLanguageIdAtPosition(e,n)},getLineContent:function(e){return e===n.startLineNumber?p:t.getLineContent(e)}},v=o["t"](l.getLineContent()),y=this.getInheritIndentForLine(e,_,n.startLineNumber+1);if(!y){var b=d?v:m;return{beforeEnter:b,afterEnter:b}}var C=d?v:y.indentation;return y.action===a["a"].Indent&&(C=i.shiftIndent(C)),g.shouldDecrease(s)&&(C=i.unshiftIndent(C)),{beforeEnter:d?v:m,afterEnter:C}},e.prototype.getIndentActionForType=function(e,t,n,i,r){if(e<4)return null;var o=this.getScopedLineTokens(t,n.startLineNumber,n.startColumn),s=this.getIndentRulesSupport(o.languageId);if(!s)return null;var u,l=o.getLineContent(),c=l.substr(0,n.startColumn-1-o.firstCharOffset);if(n.isEmpty())u=l.substr(n.startColumn-1-o.firstCharOffset);else{var h=this.getScopedLineTokens(t,n.endLineNumber,n.endColumn);u=h.getLineContent().substr(n.endColumn-1-o.firstCharOffset)}if(!s.shouldDecrease(c+u)&&s.shouldDecrease(c+i+u)){var d=this.getInheritIndentForLine(e,t,n.startLineNumber,!1);if(!d)return null;var f=d.indentation;return d.action!==a["a"].Indent&&(f=r.unshiftIndent(f)),f}return null},e.prototype.getIndentMetadata=function(e,t){var n=this.getIndentRulesSupport(e.getLanguageIdentifier().id);return n?t<1||t>e.getLineCount()?null:n.getIndentMetadata(e.getLineContent(t)):null},e.prototype.getEnterAction=function(e,t,n){var i=this.getScopedLineTokens(t,n.startLineNumber,n.startColumn),r=this._getRichEditSupport(i.languageId);if(!r)return null;var o,s=i.getLineContent(),u=s.substr(0,n.startColumn-1-i.firstCharOffset);if(n.isEmpty())o=s.substr(n.startColumn-1-i.firstCharOffset);else{var l=this.getScopedLineTokens(t,n.endLineNumber,n.endColumn);o=l.getLineContent().substr(n.endColumn-1-i.firstCharOffset)}var c="";if(n.startLineNumber>1&&0===i.firstCharOffset){var h=this.getScopedLineTokens(t,n.startLineNumber-1);h.languageId===i.languageId&&(c=h.getLineContent())}var d=r.onEnter(e,c,u,o);if(!d)return null;var f=d.indentAction,g=d.appendText,p=d.removeText||0;g||(g=f===a["a"].Indent||f===a["a"].IndentOutdent?"\t":"");var m=this.getIndentationAtPosition(t,n.startLineNumber,n.startColumn);return p&&(m=m.substring(0,m.length-p)),{indentAction:f,appendText:g,removeText:p,indentation:m}},e.prototype.getIndentationAtPosition=function(e,t,n){var i=e.getLineContent(t),r=o["t"](i);return r.length>n-1&&(r=r.substring(0,n-1)),r},e.prototype.getScopedLineTokens=function(e,t,n){e.forceTokenization(t);var i=e.getLineTokens(t),r="undefined"===typeof n?e.getLineMaxColumn(t)-1:n-1;return Object(u["a"])(i,r)},e.prototype.getBracketsSupport=function(e){var t=this._getRichEditSupport(e);return t&&t.brackets||null},e}(),v=new _},"72b4":function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var i=n("308f"),r=n("a666"),o=n("ef8e"),s=n("4035"),a=function(){function e(){this._map=new Map,this._promises=new Map,this._onDidChange=new i["a"],this.onDidChange=this._onDidChange.event,this._colorMap=null}return e.prototype.fire=function(e){this._onDidChange.fire({changedLanguages:e,changedColorMap:!1})},e.prototype.register=function(e,t){var n=this;return this._map.set(e,t),this.fire([e]),Object(r["h"])((function(){n._map.get(e)===t&&(n._map.delete(e),n.fire([e]))}))},e.prototype.registerPromise=function(e,t){var n=this,i=null,o=!1;return this._promises.set(e,t.then((function(t){n._promises.delete(e),!o&&t&&(i=n.register(e,t))}))),Object(r["h"])((function(){o=!0,i&&i.dispose()}))},e.prototype.getPromise=function(e){var t=this,n=this.get(e);if(n)return Promise.resolve(n);var i=this._promises.get(e);return i?i.then((function(n){return t.get(e)})):null},e.prototype.get=function(e){return Object(o["o"])(this._map.get(e))},e.prototype.setColorMap=function(e){this._colorMap=e,this._onDidChange.fire({changedLanguages:Object(s["d"])(this._map),changedColorMap:!0})},e.prototype.getColorMap=function(){return this._colorMap},e.prototype.getDefaultBackground=function(){return this._colorMap&&this._colorMap.length>2?this._colorMap[2]:null},e}()},7416:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return a}));var i=n("3742"),r=function(){function e(e,t,n,i){this.startColumn=e,this.endColumn=t,this.className=n,this.type=i}return e._equals=function(e,t){return e.startColumn===t.startColumn&&e.endColumn===t.endColumn&&e.className===t.className&&e.type===t.type},e.equalsArr=function(t,n){var i=t.length,r=n.length;if(i!==r)return!1;for(var o=0;o<i;o++)if(!e._equals(t[o],n[o]))return!1;return!0},e.filter=function(t,n,i,r){if(0===t.length)return[];for(var o=[],s=0,a=0,u=t.length;a<u;a++){var l=t[a],c=l.range;if(!(c.endLineNumber<n||c.startLineNumber>n)&&(!c.isEmpty()||0!==l.type&&3!==l.type)){var h=c.startLineNumber===n?c.startColumn:i,d=c.endLineNumber===n?c.endColumn:r;o[s++]=new e(h,d,l.inlineClassName,l.type)}}return o},e.compare=function(e,t){return e.startColumn===t.startColumn?e.endColumn===t.endColumn?e.className<t.className?-1:e.className>t.className?1:0:e.endColumn-t.endColumn:e.startColumn-t.startColumn},e}(),o=function(){function e(e,t,n){this.startOffset=e,this.endOffset=t,this.className=n}return e}(),s=function(){function e(){this.stopOffsets=[],this.classNames=[],this.count=0}return e.prototype.consumeLowerThan=function(e,t,n){while(this.count>0&&this.stopOffsets[0]<e){var i=0;while(i+1<this.count&&this.stopOffsets[i]===this.stopOffsets[i+1])i++;n.push(new o(t,this.stopOffsets[i],this.classNames.join(" "))),t=this.stopOffsets[i]+1,this.stopOffsets.splice(0,i+1),this.classNames.splice(0,i+1),this.count-=i+1}return this.count>0&&t<e&&(n.push(new o(t,e-1,this.classNames.join(" "))),t=e),t},e.prototype.insert=function(e,t){if(0===this.count||this.stopOffsets[this.count-1]<=e)this.stopOffsets.push(e),this.classNames.push(t);else for(var n=0;n<this.count;n++)if(this.stopOffsets[n]>=e){this.stopOffsets.splice(n,0,e),this.classNames.splice(n,0,t);break}this.count++},e}(),a=function(){function e(){}return e.normalize=function(e,t){if(0===t.length)return[];for(var n=[],r=new s,o=0,a=0,u=t.length;a<u;a++){var l=t[a],c=l.startColumn,h=l.endColumn,d=l.className;if(c>1){var f=e.charCodeAt(c-2);i["z"](f)&&c--}if(h>1){f=e.charCodeAt(h-2);i["z"](f)&&h--}var g=c-1,p=h-2;o=r.consumeLowerThan(g,o,n),0===r.count&&(o=g),r.insert(p,d)}return r.consumeLowerThan(1073741824,o,n),n},e}()},"7b4a":function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return o}));var i=n("0a0f"),r=Object(i["c"])("textResourceConfigurationService"),o=Object(i["c"])("textResourcePropertiesService")},"7e0b":function(e,t,n){"use strict";n.d(t,"a",(function(){return c}));var i=n("3742"),r=n("3170"),o=n("04d3"),s=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),a=function(e){function t(t,n){for(var i=e.call(this,0)||this,r=0;r<t.length;r++)i.set(t.charCodeAt(r),1);for(r=0;r<n.length;r++)i.set(n.charCodeAt(r),2);return i}return s(t,e),t.prototype.get=function(e){return e>=0&&e<256?this._asciiMap[e]:e>=12352&&e<=12543||e>=13312&&e<=19903||e>=19968&&e<=40959?3:this._map.get(e)||this._defaultValue},t}(r["a"]),u=[],l=[],c=function(){function e(e,t){this.classifier=new a(e,t)}return e.create=function(t){return new e(t.get(99),t.get(98))},e.prototype.createLineBreaksComputer=function(e,t,n,i){var r=this;t|=0,n=+n;var o=[],s=[];return{addRequest:function(e,t){o.push(e),s.push(t)},finalize:function(){for(var a=e.typicalFullwidthCharacterWidth/e.typicalHalfwidthCharacterWidth,c=[],f=0,g=o.length;f<g;f++){var p=s[f];c[f]=p?h(r.classifier,p,o[f],t,n,a,i):d(r.classifier,o[f],t,n,a,i)}return u.length=0,l.length=0,c}}},e}();function h(e,t,n,r,o,s,a){if(-1===o)return null;var c=n.length;if(c<=1)return null;var h=t.breakOffsets,d=t.breakOffsetsVisibleColumn,g=m(n,r,o,s,a),_=o-g,v=u,y=l,b=0,C=o,L=h.length,w=0;if(w>=0){var S=Math.abs(d[w]-C);while(w+1<L){var O=Math.abs(d[w+1]-C);if(O>=S)break;S=O,w++}}while(w<L){var I=w<0?0:h[w],M=w<0?0:d[w],k=0,N=0,T=0,E=0;if(M<=C){for(var A=M,x=n.charCodeAt(I-1),R=e.get(x),V=!0,P=I;P<c;P++){var D=P,W=n.charCodeAt(P),F=void 0,H=void 0;if(i["z"](W)?(P++,F=0,H=2):(F=e.get(W),H=f(W,A,r,s)),p(x,R,W,F)&&(k=D,N=A),A+=H,A>C){T=D,E=A-H,A-N>_&&(k=0),V=!1;break}x=W,R=F}if(V){b>0&&(v[b]=h[h.length-1],y[b]=d[h.length-1],b++);break}}if(0===k){A=M,W=n.charCodeAt(I),F=e.get(W);var B=!1;for(P=I-1;P>=0;P--){D=P+1,x=n.charCodeAt(P);if(9===x){B=!0;break}R=void 0;var j=void 0;if(i["A"](x)?(P--,R=0,j=2):(R=e.get(x),j=i["y"](x)?s:1),A<=C){if(0===T&&(T=D,E=A),A<=C-_)break;if(p(x,R,W,F)){k=D,N=A;break}}A-=j,W=x,F=R}if(0!==k){var U=_-(E-N);if(U<=r){var z=n.charCodeAt(T);H=void 0;H=i["z"](z)?2:f(z,E,r,s),U-H<0&&(k=0)}}if(B){w--;continue}}0===k&&(k=T,N=E),v[b]=k,y[b]=N,b++,C=N+_;while(w<0||w<L&&d[w]<N)w++;S=Math.abs(d[w]-C);while(w+1<L){O=Math.abs(d[w+1]-C);if(O>=S)break;S=O,w++}}return 0===b?null:(v.length=b,y.length=b,u=t.breakOffsets,l=t.breakOffsetsVisibleColumn,t.breakOffsets=v,t.breakOffsetsVisibleColumn=y,t.wrappedTextIndentLength=g,t)}function d(e,t,n,r,s,a){if(-1===r)return null;var u=t.length;if(u<=1)return null;var l=m(t,n,r,s,a),c=r-l,h=[],d=[],g=0,_=0,v=0,y=r,b=t.charCodeAt(0),C=e.get(b),L=f(b,0,n,s),w=1;i["z"](b)&&(L+=1,b=t.charCodeAt(1),C=e.get(b),w++);for(var S=w;S<u;S++){var O=S,I=t.charCodeAt(S),M=void 0,k=void 0;i["z"](I)?(S++,M=0,k=2):(M=e.get(I),k=f(I,L,n,s)),p(b,C,I,M)&&(_=O,v=L),L+=k,L>y&&((0===_||L-v>c)&&(_=O,v=L-k),h[g]=_,d[g]=v,g++,y=v+c,_=0),b=I,C=M}return 0===g?null:(h[g]=u,d[g]=L,new o["b"](h,d,l))}function f(e,t,n,r){return 9===e?n-t%n:i["y"](e)?r:1}function g(e,t){return t-e%t}function p(e,t,n,i){return 32!==n&&(2===t||3===t&&2!==i||1===i||3===i&&1!==t)}function m(e,t,n,r,o){var s=0;if(0!==o){var a=i["q"](e);if(-1!==a){for(var u=0;u<a;u++){var l=9===e.charCodeAt(u)?g(s,t):1;s+=l}var c=3===o?2:2===o?1:0;for(u=0;u<c;u++){l=g(s,t);s+=l}s+r>n&&(s=0)}}return s}},"8aeb":function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var i=n("b589"),r=n("82c9"),o=n("32a4"),s=n("459c");function a(e,t,n,o){var a=o===s["a"].ROOT_FOLDER?["rootfolder-icon"]:o===s["a"].FOLDER?["folder-icon"]:["file-icon"];if(n){var c;if(n.scheme===i["b"].data){var h=r["a"].parseMetaData(n);c=h.get(r["a"].META_DATA_LABEL)}else c=l(Object(r["c"])(n).toLowerCase());if(o===s["a"].FOLDER)a.push(c+"-name-folder-icon");else{if(c){a.push(c+"-name-file-icon");for(var d=c.split("."),f=1;f<d.length;f++)a.push(d.slice(f).join(".")+"-ext-file-icon");a.push("ext-file-icon")}var g=u(e,t,n);g&&a.push(l(g)+"-lang-file-icon")}}return a}function u(e,t,n){if(!n)return null;var s=null;if(n.scheme===i["b"].data){var a=r["a"].parseMetaData(n),u=a.get(r["a"].META_DATA_MIME);u&&(s=t.getModeId(u))}else{var l=e.getModel(n);l&&(s=l.getModeId())}return s&&s!==o["c"]?s:t.getModeIdByFilepathOrFirstLine(n)}function l(e){return e.replace(/\s/g,"\\$&")}},"8bf1":function(e,t,n){"use strict";n.d(t,"c",(function(){return s})),n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return u})),n.d(t,"d",(function(){return l})),n.d(t,"e",(function(){return c}));var i=n("4dc7"),r=n("b707"),o=function(){function e(){}return e.prototype.clone=function(){return this},e.prototype.equals=function(e){return this===e},e}(),s=new o,a="vs.editor.nullMode",u=new r["r"](a,0);function l(e,t,n,r){return new i["b"]([new i["a"](r,"",e)],n)}function c(e,t,n,r){var o=new Uint32Array(2);return o[0]=r,o[1]=(16384|e<<0|2<<23)>>>0,new i["c"](o,null===n?s:n)}},"918c":function(e,t,n){"use strict";n.d(t,"i",(function(){return a})),n.d(t,"j",(function(){return u})),n.d(t,"g",(function(){return f})),n.d(t,"f",(function(){return g})),n.d(t,"h",(function(){return m})),n.d(t,"a",(function(){return _})),n.d(t,"k",(function(){return v})),n.d(t,"b",(function(){return b})),n.d(t,"m",(function(){return C})),n.d(t,"e",(function(){return L})),n.d(t,"c",(function(){return w})),n.d(t,"d",(function(){return S})),n.d(t,"l",(function(){return O})),n.d(t,"n",(function(){return M})),n.d(t,"o",(function(){return k})),n.d(t,"p",(function(){return N})),n.d(t,"r",(function(){return T})),n.d(t,"q",(function(){return E}));var i=n("dff7"),r=n("ceb8"),o=n("303e"),s=n("b7d0"),a=Object(o["Tb"])("editor.lineHighlightBackground",{dark:null,light:null,hc:null},i["a"]("lineHighlight","Background color for the highlight of line at the cursor position.")),u=Object(o["Tb"])("editor.lineHighlightBorder",{dark:"#282828",light:"#eeeeee",hc:"#f38518"},i["a"]("lineHighlightBorderBox","Background color for the border around the line at the cursor position.")),l=Object(o["Tb"])("editor.rangeHighlightBackground",{dark:"#ffffff0b",light:"#fdff0033",hc:null},i["a"]("rangeHighlight","Background color of highlighted ranges, like by quick open and find features. The color must not be opaque so as not to hide underlying decorations."),!0),c=Object(o["Tb"])("editor.rangeHighlightBorder",{dark:null,light:null,hc:o["b"]},i["a"]("rangeHighlightBorder","Background color of the border around highlighted ranges."),!0),h=Object(o["Tb"])("editor.symbolHighlightBackground",{dark:o["t"],light:o["t"],hc:null},i["a"]("symbolHighlight","Background color of highlighted symbol, like for go to definition or go next/previous symbol. The color must not be opaque so as not to hide underlying decorations."),!0),d=Object(o["Tb"])("editor.symbolHighlightBorder",{dark:null,light:null,hc:o["b"]},i["a"]("symbolHighlightBorder","Background color of the border around highlighted symbols."),!0),f=Object(o["Tb"])("editorCursor.foreground",{dark:"#AEAFAD",light:r["a"].black,hc:r["a"].white},i["a"]("caret","Color of the editor cursor.")),g=Object(o["Tb"])("editorCursor.background",null,i["a"]("editorCursorBackground","The background color of the editor cursor. Allows customizing the color of a character overlapped by a block cursor.")),p=Object(o["Tb"])("editorWhitespace.foreground",{dark:"#e3e4e229",light:"#33333333",hc:"#e3e4e229"},i["a"]("editorWhitespaces","Color of whitespace characters in the editor.")),m=Object(o["Tb"])("editorIndentGuide.background",{dark:p,light:p,hc:p},i["a"]("editorIndentGuides","Color of the editor indentation guides.")),_=Object(o["Tb"])("editorIndentGuide.activeBackground",{dark:p,light:p,hc:p},i["a"]("editorActiveIndentGuide","Color of the active editor indentation guides.")),v=Object(o["Tb"])("editorLineNumber.foreground",{dark:"#858585",light:"#237893",hc:r["a"].white},i["a"]("editorLineNumbers","Color of editor line numbers.")),y=Object(o["Tb"])("editorActiveLineNumber.foreground",{dark:"#c6c6c6",light:"#0B216F",hc:o["b"]},i["a"]("editorActiveLineNumber","Color of editor active line number"),!1,i["a"]("deprecatedEditorActiveLineNumber","Id is deprecated. Use 'editorLineNumber.activeForeground' instead.")),b=Object(o["Tb"])("editorLineNumber.activeForeground",{dark:y,light:y,hc:y},i["a"]("editorActiveLineNumber","Color of editor active line number")),C=Object(o["Tb"])("editorRuler.foreground",{dark:"#5A5A5A",light:r["a"].lightgrey,hc:r["a"].white},i["a"]("editorRuler","Color of the editor rulers.")),L=Object(o["Tb"])("editorCodeLens.foreground",{dark:"#999999",light:"#999999",hc:"#999999"},i["a"]("editorCodeLensForeground","Foreground color of editor code lenses")),w=Object(o["Tb"])("editorBracketMatch.background",{dark:"#0064001a",light:"#0064001a",hc:"#0064001a"},i["a"]("editorBracketMatchBackground","Background color behind matching brackets")),S=Object(o["Tb"])("editorBracketMatch.border",{dark:"#888",light:"#B9B9B9",hc:o["e"]},i["a"]("editorBracketMatchBorder","Color for matching brackets boxes")),O=Object(o["Tb"])("editorOverviewRuler.border",{dark:"#7f7f7f4d",light:"#7f7f7f4d",hc:"#7f7f7f4d"},i["a"]("editorOverviewRulerBorder","Color of the overview ruler border.")),I=Object(o["Tb"])("editorGutter.background",{dark:o["o"],light:o["o"],hc:o["o"]},i["a"]("editorGutter","Background color of the editor gutter. The gutter contains the glyph margins and the line numbers.")),M=Object(o["Tb"])("editorUnnecessaryCode.border",{dark:null,light:null,hc:r["a"].fromHex("#fff").transparent(.8)},i["a"]("unnecessaryCodeBorder","Border color of unnecessary (unused) source code in the editor.")),k=Object(o["Tb"])("editorUnnecessaryCode.opacity",{dark:r["a"].fromHex("#000a"),light:r["a"].fromHex("#0007"),hc:null},i["a"]("unnecessaryCodeOpacity","Opacity of unnecessary (unused) source code in the editor. For example, \"#000000c0\" will render the code with 75% opacity. For high contrast themes, use the  'editorUnnecessaryCode.border' theme color to underline unnecessary code instead of fading it out.")),N=Object(o["Tb"])("editorOverviewRuler.errorForeground",{dark:new r["a"](new r["c"](255,18,18,.7)),light:new r["a"](new r["c"](255,18,18,.7)),hc:new r["a"](new r["c"](255,50,50,1))},i["a"]("overviewRuleError","Overview ruler marker color for errors.")),T=Object(o["Tb"])("editorOverviewRuler.warningForeground",{dark:o["P"],light:o["P"],hc:o["O"]},i["a"]("overviewRuleWarning","Overview ruler marker color for warnings.")),E=Object(o["Tb"])("editorOverviewRuler.infoForeground",{dark:o["H"],light:o["H"],hc:o["G"]},i["a"]("overviewRuleInfo","Overview ruler marker color for infos."));Object(s["e"])((function(e,t){var n=e.getColor(o["o"]);n&&t.addRule(".monaco-editor, .monaco-editor-background, .monaco-editor .inputarea.ime-input { background-color: "+n+"; }");var i=e.getColor(o["x"]);i&&t.addRule(".monaco-editor, .monaco-editor .inputarea.ime-input { color: "+i+"; }");var r=e.getColor(I);r&&t.addRule(".monaco-editor .margin { background-color: "+r+"; }");var s=e.getColor(l);s&&t.addRule(".monaco-editor .rangeHighlight { background-color: "+s+"; }");var a=e.getColor(c);a&&t.addRule(".monaco-editor .rangeHighlight { border: 1px "+("hc"===e.type?"dotted":"solid")+" "+a+"; }");var u=e.getColor(h);u&&t.addRule(".monaco-editor .symbolHighlight { background-color: "+u+"; }");var f=e.getColor(d);f&&t.addRule(".monaco-editor .symbolHighlight { border: 1px "+("hc"===e.type?"dotted":"solid")+" "+f+"; }");var g=e.getColor(p);g&&t.addRule(".vs-whitespace { color: "+g+" !important; }")}))},"91df":function(e,t,n){"use strict";n.d(t,"a",(function(){return T}));var i=n("ceb8"),r=n("3742"),o=n("fd49"),s=n("7061"),a=n("6a89"),u=n("b707"),l=n("4d05"),c=n("ff6c"),h=n("0d83"),d=n("308f"),f=n("a666"),g=n("42e3"),p=function(){function e(){this._hasPending=!1,this._inserts=[],this._changes=[],this._removes=[]}return e.prototype.insert=function(e){this._hasPending=!0,this._inserts.push(e)},e.prototype.change=function(e){this._hasPending=!0,this._changes.push(e)},e.prototype.remove=function(e){this._hasPending=!0,this._removes.push(e)},e.prototype.mustCommit=function(){return this._hasPending},e.prototype.commit=function(e){if(this._hasPending){var t=this._inserts,n=this._changes,i=this._removes;this._hasPending=!1,this._inserts=[],this._changes=[],this._removes=[],e._commitPendingChanges(t,n,i)}},e}(),m=function(){function e(e,t,n,i,r){this.id=e,this.afterLineNumber=t,this.ordinal=n,this.height=i,this.minWidth=r,this.prefixSum=0}return e}(),_=function(){function e(t,n){this._instanceId=r["M"](++e.INSTANCE_COUNT),this._pendingChanges=new p,this._lastWhitespaceId=0,this._arr=[],this._prefixSumValidIndex=-1,this._minWidth=-1,this._lineCount=t,this._lineHeight=n}return e.findInsertionIndex=function(e,t,n){var i=0,r=e.length;while(i<r){var o=i+r>>>1;t===e[o].afterLineNumber?n<e[o].ordinal?r=o:i=o+1:t<e[o].afterLineNumber?r=o:i=o+1}return i},e.prototype.setLineHeight=function(e){this._checkPendingChanges(),this._lineHeight=e},e.prototype.onFlushed=function(e){this._checkPendingChanges(),this._lineCount=e},e.prototype.changeWhitespace=function(e){var t=this;try{var n={insertWhitespace:function(e,n,i,r){e|=0,n|=0,i|=0,r|=0;var o=t._instanceId+ ++t._lastWhitespaceId;return t._pendingChanges.insert(new m(o,e,n,i,r)),o},changeOneWhitespace:function(e,n,i){n|=0,i|=0,t._pendingChanges.change({id:e,newAfterLineNumber:n,newHeight:i})},removeWhitespace:function(e){t._pendingChanges.remove({id:e})}};return e(n)}finally{this._pendingChanges.commit(this)}},e.prototype._commitPendingChanges=function(e,t,n){if((e.length>0||n.length>0)&&(this._minWidth=-1),e.length+t.length+n.length<=1){for(var i=0,r=e;i<r.length;i++){var o=r[i];this._insertWhitespace(o)}for(var s=0,a=t;s<a.length;s++){var u=a[s];this._changeOneWhitespace(u.id,u.newAfterLineNumber,u.newHeight)}for(var l=0,c=n;l<c.length;l++){var h=c[l],d=this._findWhitespaceIndex(h.id);-1!==d&&this._removeWhitespace(d)}}else{for(var f=new Set,g=0,p=n;g<p.length;g++){h=p[g];f.add(h.id)}for(var m=new Map,_=0,v=t;_<v.length;_++){u=v[_];m.set(u.id,u)}var y=function(e){for(var t=[],n=0,i=e;n<i.length;n++){var r=i[n];if(!f.has(r.id)){if(m.has(r.id)){var o=m.get(r.id);r.afterLineNumber=o.newAfterLineNumber,r.height=o.newHeight}t.push(r)}}return t},b=y(this._arr).concat(y(e));b.sort((function(e,t){return e.afterLineNumber===t.afterLineNumber?e.ordinal-t.ordinal:e.afterLineNumber-t.afterLineNumber})),this._arr=b,this._prefixSumValidIndex=-1}},e.prototype._checkPendingChanges=function(){this._pendingChanges.mustCommit()&&this._pendingChanges.commit(this)},e.prototype._insertWhitespace=function(t){var n=e.findInsertionIndex(this._arr,t.afterLineNumber,t.ordinal);this._arr.splice(n,0,t),this._prefixSumValidIndex=Math.min(this._prefixSumValidIndex,n-1)},e.prototype._findWhitespaceIndex=function(e){for(var t=this._arr,n=0,i=t.length;n<i;n++)if(t[n].id===e)return n;return-1},e.prototype._changeOneWhitespace=function(e,t,n){var i=this._findWhitespaceIndex(e);if(-1!==i&&(this._arr[i].height!==n&&(this._arr[i].height=n,this._prefixSumValidIndex=Math.min(this._prefixSumValidIndex,i-1)),this._arr[i].afterLineNumber!==t)){var r=this._arr[i];this._removeWhitespace(i),r.afterLineNumber=t,this._insertWhitespace(r)}},e.prototype._removeWhitespace=function(e){this._arr.splice(e,1),this._prefixSumValidIndex=Math.min(this._prefixSumValidIndex,e-1)},e.prototype.onLinesDeleted=function(e,t){this._checkPendingChanges(),e|=0,t|=0,this._lineCount-=t-e+1;for(var n=0,i=this._arr.length;n<i;n++){var r=this._arr[n].afterLineNumber;e<=r&&r<=t?this._arr[n].afterLineNumber=e-1:r>t&&(this._arr[n].afterLineNumber-=t-e+1)}},e.prototype.onLinesInserted=function(e,t){this._checkPendingChanges(),e|=0,t|=0,this._lineCount+=t-e+1;for(var n=0,i=this._arr.length;n<i;n++){var r=this._arr[n].afterLineNumber;e<=r&&(this._arr[n].afterLineNumber+=t-e+1)}},e.prototype.getWhitespacesTotalHeight=function(){return this._checkPendingChanges(),0===this._arr.length?0:this.getWhitespacesAccumulatedHeight(this._arr.length-1)},e.prototype.getWhitespacesAccumulatedHeight=function(e){this._checkPendingChanges(),e|=0;var t=Math.max(0,this._prefixSumValidIndex+1);0===t&&(this._arr[0].prefixSum=this._arr[0].height,t++);for(var n=t;n<=e;n++)this._arr[n].prefixSum=this._arr[n-1].prefixSum+this._arr[n].height;return this._prefixSumValidIndex=Math.max(this._prefixSumValidIndex,e),this._arr[e].prefixSum},e.prototype.getLinesTotalHeight=function(){this._checkPendingChanges();var e=this._lineHeight*this._lineCount,t=this.getWhitespacesTotalHeight();return e+t},e.prototype.getWhitespaceAccumulatedHeightBeforeLineNumber=function(e){this._checkPendingChanges(),e|=0;var t=this._findLastWhitespaceBeforeLineNumber(e);return-1===t?0:this.getWhitespacesAccumulatedHeight(t)},e.prototype._findLastWhitespaceBeforeLineNumber=function(e){e|=0;var t=this._arr,n=0,i=t.length-1;while(n<=i){var r=i-n|0,o=r/2|0,s=n+o|0;if(t[s].afterLineNumber<e){if(s+1>=t.length||t[s+1].afterLineNumber>=e)return s;n=s+1|0}else i=s-1|0}return-1},e.prototype._findFirstWhitespaceAfterLineNumber=function(e){e|=0;var t=this._findLastWhitespaceBeforeLineNumber(e),n=t+1;return n<this._arr.length?n:-1},e.prototype.getFirstWhitespaceIndexAfterLineNumber=function(e){return this._checkPendingChanges(),e|=0,this._findFirstWhitespaceAfterLineNumber(e)},e.prototype.getVerticalOffsetForLineNumber=function(e){var t;this._checkPendingChanges(),e|=0,t=e>1?this._lineHeight*(e-1):0;var n=this.getWhitespaceAccumulatedHeightBeforeLineNumber(e);return t+n},e.prototype.getWhitespaceMinWidth=function(){if(this._checkPendingChanges(),-1===this._minWidth){for(var e=0,t=0,n=this._arr.length;t<n;t++)e=Math.max(e,this._arr[t].minWidth);this._minWidth=e}return this._minWidth},e.prototype.isAfterLines=function(e){this._checkPendingChanges();var t=this.getLinesTotalHeight();return e>t},e.prototype.getLineNumberAtOrAfterVerticalOffset=function(e){if(this._checkPendingChanges(),e|=0,e<0)return 1;var t=0|this._lineCount,n=this._lineHeight,i=1,r=t;while(i<r){var o=(i+r)/2|0,s=0|this.getVerticalOffsetForLineNumber(o);if(e>=s+n)i=o+1;else{if(e>=s)return o;r=o}}return i>t?t:i},e.prototype.getLinesViewportData=function(e,t){this._checkPendingChanges(),e|=0,t|=0;var n,i,r=this._lineHeight,o=0|this.getLineNumberAtOrAfterVerticalOffset(e),s=0|this.getVerticalOffsetForLineNumber(o),a=0|this._lineCount,u=0|this.getFirstWhitespaceIndexAfterLineNumber(o),l=0|this.getWhitespacesCount();-1===u?(u=l,i=a+1,n=0):(i=0|this.getAfterLineNumberForWhitespaceIndex(u),n=0|this.getHeightForWhitespaceIndex(u));var c=s,h=c,d=5e5,f=0;s>=d&&(f=Math.floor(s/d)*d,f=Math.floor(f/r)*r,h-=f);for(var g=[],p=e+(t-e)/2,m=-1,_=o;_<=a;_++){if(-1===m){var v=c,y=c+r;(v<=p&&p<y||v>p)&&(m=_)}c+=r,g[_-o]=h,h+=r;while(i===_)h+=n,c+=n,u++,u>=l?i=a+1:(i=0|this.getAfterLineNumberForWhitespaceIndex(u),n=0|this.getHeightForWhitespaceIndex(u));if(c>=t){a=_;break}}-1===m&&(m=a);var b=0|this.getVerticalOffsetForLineNumber(a),C=o,L=a;return C<L&&s<e&&C++,C<L&&b+r>t&&L--,{bigNumbersDelta:f,startLineNumber:o,endLineNumber:a,relativeVerticalOffset:g,centeredLineNumber:m,completelyVisibleStartLineNumber:C,completelyVisibleEndLineNumber:L}},e.prototype.getVerticalOffsetForWhitespaceIndex=function(e){this._checkPendingChanges(),e|=0;var t,n,i=this.getAfterLineNumberForWhitespaceIndex(e);return t=i>=1?this._lineHeight*i:0,n=e>0?this.getWhitespacesAccumulatedHeight(e-1):0,t+n},e.prototype.getWhitespaceIndexAtOrAfterVerticallOffset=function(e){this._checkPendingChanges(),e|=0;var t=0,n=this.getWhitespacesCount()-1;if(n<0)return-1;var i=this.getVerticalOffsetForWhitespaceIndex(n),r=this.getHeightForWhitespaceIndex(n);if(e>=i+r)return-1;while(t<n){var o=Math.floor((t+n)/2),s=this.getVerticalOffsetForWhitespaceIndex(o),a=this.getHeightForWhitespaceIndex(o);if(e>=s+a)t=o+1;else{if(e>=s)return o;n=o}}return t},e.prototype.getWhitespaceAtVerticalOffset=function(e){this._checkPendingChanges(),e|=0;var t=this.getWhitespaceIndexAtOrAfterVerticallOffset(e);if(t<0)return null;if(t>=this.getWhitespacesCount())return null;var n=this.getVerticalOffsetForWhitespaceIndex(t);if(n>e)return null;var i=this.getHeightForWhitespaceIndex(t),r=this.getIdForWhitespaceIndex(t),o=this.getAfterLineNumberForWhitespaceIndex(t);return{id:r,afterLineNumber:o,verticalOffset:n,height:i}},e.prototype.getWhitespaceViewportData=function(e,t){this._checkPendingChanges(),e|=0,t|=0;var n=this.getWhitespaceIndexAtOrAfterVerticallOffset(e),i=this.getWhitespacesCount()-1;if(n<0)return[];for(var r=[],o=n;o<=i;o++){var s=this.getVerticalOffsetForWhitespaceIndex(o),a=this.getHeightForWhitespaceIndex(o);if(s>=t)break;r.push({id:this.getIdForWhitespaceIndex(o),afterLineNumber:this.getAfterLineNumberForWhitespaceIndex(o),verticalOffset:s,height:a})}return r},e.prototype.getWhitespaces=function(){return this._checkPendingChanges(),this._arr.slice(0)},e.prototype.getWhitespacesCount=function(){return this._checkPendingChanges(),this._arr.length},e.prototype.getIdForWhitespaceIndex=function(e){return this._checkPendingChanges(),e|=0,this._arr[e].id},e.prototype.getAfterLineNumberForWhitespaceIndex=function(e){return this._checkPendingChanges(),e|=0,this._arr[e].afterLineNumber},e.prototype.getHeightForWhitespaceIndex=function(e){return this._checkPendingChanges(),e|=0,this._arr[e].height},e.INSTANCE_COUNT=0,e}(),v=n("a8d0"),y=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),b=125,C=function(){function e(e,t,n,i){e|=0,t|=0,n|=0,i|=0,e<0&&(e=0),t<0&&(t=0),n<0&&(n=0),i<0&&(i=0),this.width=e,this.contentWidth=t,this.scrollWidth=Math.max(e,t),this.height=n,this.contentHeight=i,this.scrollHeight=Math.max(n,i)}return e.prototype.equals=function(e){return this.width===e.width&&this.contentWidth===e.contentWidth&&this.height===e.height&&this.contentHeight===e.contentHeight},e}(),L=function(e){function t(t,n){var i=e.call(this)||this;return i._onDidContentSizeChange=i._register(new d["a"]),i.onDidContentSizeChange=i._onDidContentSizeChange.event,i._dimensions=new C(0,0,0,0),i._scrollable=i._register(new g["a"](t,n)),i.onDidScroll=i._scrollable.onScroll,i}return y(t,e),t.prototype.getScrollable=function(){return this._scrollable},t.prototype.setSmoothScrollDuration=function(e){this._scrollable.setSmoothScrollDuration(e)},t.prototype.validateScrollPosition=function(e){return this._scrollable.validateScrollPosition(e)},t.prototype.getScrollDimensions=function(){return this._dimensions},t.prototype.setScrollDimensions=function(e){if(!this._dimensions.equals(e)){var t=this._dimensions;this._dimensions=e,this._scrollable.setScrollDimensions({width:e.width,scrollWidth:e.scrollWidth,height:e.height,scrollHeight:e.scrollHeight});var n=t.contentWidth!==e.contentWidth,i=t.contentHeight!==e.contentHeight;(n||i)&&this._onDidContentSizeChange.fire({contentWidth:e.contentWidth,contentHeight:e.contentHeight,contentWidthChanged:n,contentHeightChanged:i})}},t.prototype.getFutureScrollPosition=function(){return this._scrollable.getFutureScrollPosition()},t.prototype.getCurrentScrollPosition=function(){return this._scrollable.getCurrentScrollPosition()},t.prototype.setScrollPositionNow=function(e){this._scrollable.setScrollPositionNow(e)},t.prototype.setScrollPositionSmooth=function(e){this._scrollable.setScrollPositionSmooth(e)},t}(f["a"]),w=function(e){function t(t,n,i){var r=e.call(this)||this;r._configuration=t;var o=r._configuration.options,s=o.get(107);return r._linesLayout=new _(n,o.get(49)),r._scrollable=r._register(new L(0,i)),r._configureSmoothScrollDuration(),r._scrollable.setScrollDimensions(new C(s.contentWidth,0,s.height,0)),r.onDidScroll=r._scrollable.onDidScroll,r.onDidContentSizeChange=r._scrollable.onDidContentSizeChange,r._updateHeight(),r}return y(t,e),t.prototype.dispose=function(){e.prototype.dispose.call(this)},t.prototype.getScrollable=function(){return this._scrollable.getScrollable()},t.prototype.onHeightMaybeChanged=function(){this._updateHeight()},t.prototype._configureSmoothScrollDuration=function(){this._scrollable.setSmoothScrollDuration(this._configuration.options.get(87)?b:0)},t.prototype.onConfigurationChanged=function(e){var t=this._configuration.options;if(e.hasChanged(49)&&this._linesLayout.setLineHeight(t.get(49)),e.hasChanged(107)){var n=t.get(107),i=n.contentWidth,r=n.height,o=this._scrollable.getScrollDimensions(),s=o.scrollWidth;this._scrollable.setScrollDimensions(new C(i,o.contentWidth,r,this._getContentHeight(i,r,s)))}else this._updateHeight();e.hasChanged(87)&&this._configureSmoothScrollDuration()},t.prototype.onFlushed=function(e){this._linesLayout.onFlushed(e)},t.prototype.onLinesDeleted=function(e,t){this._linesLayout.onLinesDeleted(e,t)},t.prototype.onLinesInserted=function(e,t){this._linesLayout.onLinesInserted(e,t)},t.prototype._getHorizontalScrollbarHeight=function(e,t){var n=this._configuration.options,i=n.get(78);return 2===i.horizontal||e>=t?0:i.horizontalScrollbarSize},t.prototype._getContentHeight=function(e,t,n){var i=this._configuration.options,r=this._linesLayout.getLinesTotalHeight();return i.get(80)?r+=t-i.get(49):r+=this._getHorizontalScrollbarHeight(e,n),r},t.prototype._updateHeight=function(){var e=this._scrollable.getScrollDimensions(),t=e.width,n=e.height,i=e.scrollWidth;this._scrollable.setScrollDimensions(new C(t,e.contentWidth,n,this._getContentHeight(t,n,i)))},t.prototype.getCurrentViewport=function(){var e=this._scrollable.getScrollDimensions(),t=this._scrollable.getCurrentScrollPosition();return new v["f"](t.scrollTop,t.scrollLeft,e.width,e.height)},t.prototype.getFutureViewport=function(){var e=this._scrollable.getScrollDimensions(),t=this._scrollable.getFutureScrollPosition();return new v["f"](t.scrollTop,t.scrollLeft,e.width,e.height)},t.prototype._computeContentWidth=function(e){var t=this._configuration.options,n=t.get(108),i=t.get(34);if(n.isViewportWrapping){var r=t.get(107),o=t.get(54);return e>r.contentWidth+i.typicalHalfwidthCharacterWidth&&o.enabled&&"right"===o.side?e+r.verticalScrollbarWidth:e}var s=t.get(79)*i.typicalHalfwidthCharacterWidth,a=this._linesLayout.getWhitespaceMinWidth();return Math.max(e+s,a)},t.prototype.onMaxLineWidthChanged=function(e){var t=this._scrollable.getScrollDimensions();this._scrollable.setScrollDimensions(new C(t.width,this._computeContentWidth(e),t.height,t.contentHeight)),this._updateHeight()},t.prototype.saveState=function(){var e=this._scrollable.getFutureScrollPosition(),t=e.scrollTop,n=this._linesLayout.getLineNumberAtOrAfterVerticalOffset(t),i=this._linesLayout.getWhitespaceAccumulatedHeightBeforeLineNumber(n);return{scrollTop:t,scrollTopWithoutViewZones:t-i,scrollLeft:e.scrollLeft}},t.prototype.changeWhitespace=function(e){return this._linesLayout.changeWhitespace(e)},t.prototype.getVerticalOffsetForLineNumber=function(e){return this._linesLayout.getVerticalOffsetForLineNumber(e)},t.prototype.isAfterLines=function(e){return this._linesLayout.isAfterLines(e)},t.prototype.getLineNumberAtVerticalOffset=function(e){return this._linesLayout.getLineNumberAtOrAfterVerticalOffset(e)},t.prototype.getWhitespaceAtVerticalOffset=function(e){return this._linesLayout.getWhitespaceAtVerticalOffset(e)},t.prototype.getLinesViewportData=function(){var e=this.getCurrentViewport();return this._linesLayout.getLinesViewportData(e.top,e.top+e.height)},t.prototype.getLinesViewportDataAtScrollTop=function(e){var t=this._scrollable.getScrollDimensions();return e+t.height>t.scrollHeight&&(e=t.scrollHeight-t.height),e<0&&(e=0),this._linesLayout.getLinesViewportData(e,e+t.height)},t.prototype.getWhitespaceViewportData=function(){var e=this.getCurrentViewport();return this._linesLayout.getWhitespaceViewportData(e.top,e.top+e.height)},t.prototype.getWhitespaces=function(){return this._linesLayout.getWhitespaces()},t.prototype.getContentWidth=function(){var e=this._scrollable.getScrollDimensions();return e.contentWidth},t.prototype.getScrollWidth=function(){var e=this._scrollable.getScrollDimensions();return e.scrollWidth},t.prototype.getContentHeight=function(){var e=this._scrollable.getScrollDimensions();return e.contentHeight},t.prototype.getScrollHeight=function(){var e=this._scrollable.getScrollDimensions();return e.scrollHeight},t.prototype.getCurrentScrollLeft=function(){var e=this._scrollable.getCurrentScrollPosition();return e.scrollLeft},t.prototype.getCurrentScrollTop=function(){var e=this._scrollable.getCurrentScrollPosition();return e.scrollTop},t.prototype.validateScrollPosition=function(e){return this._scrollable.validateScrollPosition(e)},t.prototype.setScrollPositionNow=function(e){this._scrollable.setScrollPositionNow(e)},t.prototype.setScrollPositionSmooth=function(e){this._scrollable.setScrollPositionSmooth(e)},t.prototype.deltaScrollNow=function(e,t){var n=this._scrollable.getCurrentScrollPosition();this._scrollable.setScrollPositionNow({scrollLeft:n.scrollLeft+e,scrollTop:n.scrollTop+t})},t}(f["a"]),S=n("04d3"),O=function(){function e(e,t,n,i,r){this.editorId=e,this.model=t,this.configuration=n,this._linesCollection=i,this._coordinatesConverter=r,this._decorationsCache=Object.create(null),this._cachedModelDecorationsResolver=null,this._cachedModelDecorationsResolverViewRange=null}return e.prototype._clearCachedModelDecorationsResolver=function(){this._cachedModelDecorationsResolver=null,this._cachedModelDecorationsResolverViewRange=null},e.prototype.dispose=function(){this._decorationsCache=Object.create(null),this._clearCachedModelDecorationsResolver()},e.prototype.reset=function(){this._decorationsCache=Object.create(null),this._clearCachedModelDecorationsResolver()},e.prototype.onModelDecorationsChanged=function(){this._decorationsCache=Object.create(null),this._clearCachedModelDecorationsResolver()},e.prototype.onLineMappingChanged=function(){this._decorationsCache=Object.create(null),this._clearCachedModelDecorationsResolver()},e.prototype._getOrCreateViewModelDecoration=function(e){var t=e.id,n=this._decorationsCache[t];if(!n){var i=e.range,r=e.options,o=void 0;if(r.isWholeLine){var u=this._coordinatesConverter.convertModelPositionToViewPosition(new s["a"](i.startLineNumber,1)),l=this._coordinatesConverter.convertModelPositionToViewPosition(new s["a"](i.endLineNumber,this.model.getLineMaxColumn(i.endLineNumber)));o=new a["a"](u.lineNumber,u.column,l.lineNumber,l.column)}else o=this._coordinatesConverter.convertModelRangeToViewRange(i);n=new v["e"](o,r),this._decorationsCache[t]=n}return n},e.prototype.getDecorationsViewportData=function(e){var t=null!==this._cachedModelDecorationsResolver;return t=t&&e.equalsRange(this._cachedModelDecorationsResolverViewRange),t||(this._cachedModelDecorationsResolver=this._getDecorationsViewportData(e),this._cachedModelDecorationsResolverViewRange=e),this._cachedModelDecorationsResolver},e.prototype._getDecorationsViewportData=function(e){for(var t=this._linesCollection.getDecorationsInRange(e,this.editorId,Object(o["j"])(this.configuration.options)),n=e.startLineNumber,i=e.endLineNumber,r=[],s=0,u=[],l=n;l<=i;l++)u[l-n]=[];for(var c=0,h=t.length;c<h;c++){var d=t[c],f=d.options,g=this._getOrCreateViewModelDecoration(d),p=g.range;if(r[s++]=g,f.inlineClassName){var m=new v["a"](p,f.inlineClassName,f.inlineClassNameAffectsLetterSpacing?3:0),_=Math.max(n,p.startLineNumber),y=Math.min(i,p.endLineNumber);for(l=_;l<=y;l++)u[l-n].push(m)}if(f.beforeContentClassName&&n<=p.startLineNumber&&p.startLineNumber<=i){m=new v["a"](new a["a"](p.startLineNumber,p.startColumn,p.startLineNumber,p.startColumn),f.beforeContentClassName,1);u[p.startLineNumber-n].push(m)}if(f.afterContentClassName&&n<=p.endLineNumber&&p.endLineNumber<=i){m=new v["a"](new a["a"](p.endLineNumber,p.endColumn,p.endLineNumber,p.endColumn),f.afterContentClassName,2);u[p.endLineNumber-n].push(m)}}return{decorations:r,inlineDecorations:u}},e}(),I=n("5fe7"),M=n("30db"),k=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),N=!0,T=function(e){function t(t,n,i,r,o,s){var a=e.call(this)||this;if(a.editorId=t,a.configuration=n,a.model=i,a._tokenizeViewportSoon=a._register(new I["d"]((function(){return a.tokenizeViewport()}),50)),a.hasFocus=!1,a.viewportStartLine=-1,a.viewportStartLineTrackedRange=null,a.viewportStartLineDelta=0,N&&a.model.isTooLargeForTokenization())a.lines=new S["a"](a.model);else{var u=a.configuration.options,l=u.get(34),d=u.get(103),f=u.get(108),g=u.get(102);a.lines=new S["c"](a.model,r,o,l,a.model.getOptions().tabSize,d,f.wrappingColumn,g)}return a.coordinatesConverter=a.lines.createCoordinatesConverter(),a.viewLayout=a._register(new w(a.configuration,a.getLineCount(),s)),a._register(a.viewLayout.onDidScroll((function(e){e.scrollTopChanged&&a._tokenizeViewportSoon.schedule();try{var t=a._beginEmit();t.emit(new h["n"](e))}finally{a._endEmit()}}))),a._register(a.viewLayout.onDidContentSizeChange((function(e){try{var t=a._beginEmit();t.emit(new h["b"](e))}finally{a._endEmit()}}))),a.decorations=new O(a.editorId,a.model,a.configuration,a.lines,a.coordinatesConverter),a._registerModelEvents(),a._register(a.configuration.onDidChange((function(e){try{var t=a._beginEmit();a._onConfigurationChanged(t,e)}finally{a._endEmit()}}))),a._register(c["a"].getInstance().onDidChange((function(){try{var e=a._beginEmit();e.emit(new h["q"])}finally{a._endEmit()}}))),a}return k(t,e),t.prototype.dispose=function(){e.prototype.dispose.call(this),this.decorations.dispose(),this.lines.dispose(),this.invalidateMinimapColorCache(),this.viewportStartLineTrackedRange=this.model._setTrackedRange(this.viewportStartLineTrackedRange,null,1)},t.prototype.tokenizeViewport=function(){var e=this.viewLayout.getLinesViewportData(),t=this.coordinatesConverter.convertViewPositionToModelPosition(new s["a"](e.startLineNumber,1)),n=this.coordinatesConverter.convertViewPositionToModelPosition(new s["a"](e.endLineNumber,1));this.model.tokenizeViewport(t.lineNumber,n.lineNumber)},t.prototype.setHasFocus=function(e){this.hasFocus=e},t.prototype._onConfigurationChanged=function(e,t){var n=null;if(-1!==this.viewportStartLine){var i=new s["a"](this.viewportStartLine,this.getLineMinColumn(this.viewportStartLine));n=this.coordinatesConverter.convertViewPositionToModelPosition(i)}var r=!1,o=this.configuration.options,a=o.get(34),u=o.get(103),l=o.get(108),c=o.get(102);if(this.lines.setWrappingSettings(a,u,l.wrappingColumn,c)&&(e.emit(new h["f"]),e.emit(new h["i"]),e.emit(new h["d"]),this.decorations.onLineMappingChanged(),this.viewLayout.onFlushed(this.getLineCount()),0!==this.viewLayout.getCurrentScrollTop()&&(r=!0)),t.hasChanged(68)&&(this.decorations.reset(),e.emit(new h["d"])),e.emit(new h["a"](t)),this.viewLayout.onConfigurationChanged(t),r&&n){var d=this.coordinatesConverter.convertModelPositionToViewPosition(n),f=this.viewLayout.getVerticalOffsetForLineNumber(d.lineNumber);this.viewLayout.setScrollPositionNow({scrollTop:f+this.viewportStartLineDelta})}},t.prototype._registerModelEvents=function(){var e=this;this._register(this.model.onDidChangeRawContentFast((function(t){try{for(var n=e._beginEmit(),i=!1,r=!1,o=t.changes,s=t.versionId,a=e.lines.createLineBreaksComputer(),u=0,l=o;u<l.length;u++){var c=l[u];switch(c.changeType){case 4:for(var d=0,f=c.detail;d<f.length;d++){var g=f[d];a.addRequest(g,null)}break;case 2:a.addRequest(c.detail,null);break}}for(var p=a.finalize(),m=0,_=0,v=o;_<v.length;_++){c=v[_];switch(c.changeType){case 1:e.lines.onModelFlushed(),n.emit(new h["f"]),e.decorations.reset(),e.viewLayout.onFlushed(e.getLineCount()),i=!0;break;case 3:var y=e.lines.onModelLinesDeleted(s,c.fromLineNumber,c.toLineNumber);null!==y&&(n.emit(y),e.viewLayout.onLinesDeleted(y.fromLineNumber,y.toLineNumber)),i=!0;break;case 4:var b=p.slice(m,m+c.detail.length);m+=c.detail.length;var C=e.lines.onModelLinesInserted(s,c.fromLineNumber,c.toLineNumber,b);null!==C&&(n.emit(C),e.viewLayout.onLinesInserted(C.fromLineNumber,C.toLineNumber)),i=!0;break;case 2:var L=p[m];m++;var w=e.lines.onModelLineChanged(s,c.lineNumber,L),S=w[0],O=w[1];C=w[2],y=w[3];r=S,O&&n.emit(O),C&&(n.emit(C),e.viewLayout.onLinesInserted(C.fromLineNumber,C.toLineNumber)),y&&(n.emit(y),e.viewLayout.onLinesDeleted(y.fromLineNumber,y.toLineNumber));break;case 5:break}}e.lines.acceptVersionId(s),e.viewLayout.onHeightMaybeChanged(),!i&&r&&(n.emit(new h["i"]),n.emit(new h["d"]),e.decorations.onLineMappingChanged())}finally{e._endEmit()}if(e.viewportStartLine=-1,e.configuration.setMaxLineNumber(e.model.getLineCount()),!e.hasFocus&&e.model.getAttachedEditorCount()>=2&&e.viewportStartLineTrackedRange){var I=e.model._getTrackedRange(e.viewportStartLineTrackedRange);if(I){var M=e.coordinatesConverter.convertModelPositionToViewPosition(I.getStartPosition()),k=e.viewLayout.getVerticalOffsetForLineNumber(M.lineNumber);e.viewLayout.setScrollPositionNow({scrollTop:k+e.viewportStartLineDelta})}}}))),this._register(this.model.onDidChangeTokens((function(t){for(var n=[],i=0,r=t.ranges.length;i<r;i++){var o=t.ranges[i],a=e.coordinatesConverter.convertModelPositionToViewPosition(new s["a"](o.fromLineNumber,1)).lineNumber,u=e.coordinatesConverter.convertModelPositionToViewPosition(new s["a"](o.toLineNumber,e.model.getLineMaxColumn(o.toLineNumber))).lineNumber;n[i]={fromLineNumber:a,toLineNumber:u}}try{var l=e._beginEmit();l.emit(new h["p"](n))}finally{e._endEmit()}t.tokenizationSupportChanged&&e._tokenizeViewportSoon.schedule()}))),this._register(this.model.onDidChangeLanguageConfiguration((function(t){try{var n=e._beginEmit();n.emit(new h["h"])}finally{e._endEmit()}}))),this._register(this.model.onDidChangeOptions((function(t){if(e.lines.setTabSize(e.model.getOptions().tabSize)){e.decorations.onLineMappingChanged(),e.viewLayout.onFlushed(e.getLineCount());try{var n=e._beginEmit();n.emit(new h["f"]),n.emit(new h["i"]),n.emit(new h["d"])}finally{e._endEmit()}}}))),this._register(this.model.onDidChangeDecorations((function(t){e.decorations.onModelDecorationsChanged();try{var n=e._beginEmit();n.emit(new h["d"])}finally{e._endEmit()}})))},t.prototype.setHiddenAreas=function(e){try{var t=this._beginEmit(),n=this.lines.setHiddenAreas(e);n&&(t.emit(new h["f"]),t.emit(new h["i"]),t.emit(new h["d"]),this.decorations.onLineMappingChanged(),this.viewLayout.onFlushed(this.getLineCount()),this.viewLayout.onHeightMaybeChanged())}finally{this._endEmit()}},t.prototype.getVisibleRanges=function(){var e=this.getCompletelyVisibleViewRange(),t=this.coordinatesConverter.convertViewRangeToModelRange(e),n=this.lines.getHiddenAreas();if(0===n.length)return[t];for(var i=[],r=0,o=t.startLineNumber,s=t.startColumn,u=t.endLineNumber,l=t.endColumn,c=0,h=n.length;c<h;c++){var d=n[c].startLineNumber,f=n[c].endLineNumber;f<o||(d>u||(o<d&&(i[r++]=new a["a"](o,s,d-1,this.model.getLineMaxColumn(d-1))),o=f+1,s=1))}return(o<u||o===u&&s<l)&&(i[r++]=new a["a"](o,s,u,l)),i},t.prototype.getCompletelyVisibleViewRange=function(){var e=this.viewLayout.getLinesViewportData(),t=e.completelyVisibleStartLineNumber,n=e.completelyVisibleEndLineNumber;return new a["a"](t,this.getLineMinColumn(t),n,this.getLineMaxColumn(n))},t.prototype.getCompletelyVisibleViewRangeAtScrollTop=function(e){var t=this.viewLayout.getLinesViewportDataAtScrollTop(e),n=t.completelyVisibleStartLineNumber,i=t.completelyVisibleEndLineNumber;return new a["a"](n,this.getLineMinColumn(n),i,this.getLineMaxColumn(i))},t.prototype.saveState=function(){var e=this.viewLayout.saveState(),t=e.scrollTop,n=this.viewLayout.getLineNumberAtVerticalOffset(t),i=this.coordinatesConverter.convertViewPositionToModelPosition(new s["a"](n,this.getLineMinColumn(n))),r=this.viewLayout.getVerticalOffsetForLineNumber(n)-t;return{scrollLeft:e.scrollLeft,firstPosition:i,firstPositionDeltaTop:r}},t.prototype.reduceRestoreState=function(e){if("undefined"===typeof e.firstPosition)return this._reduceRestoreStateCompatibility(e);var t=this.model.validatePosition(e.firstPosition),n=this.coordinatesConverter.convertModelPositionToViewPosition(t),i=this.viewLayout.getVerticalOffsetForLineNumber(n.lineNumber)-e.firstPositionDeltaTop;return{scrollLeft:e.scrollLeft,scrollTop:i}},t.prototype._reduceRestoreStateCompatibility=function(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTopWithoutViewZones}},t.prototype.getTabSize=function(){return this.model.getOptions().tabSize},t.prototype.getOptions=function(){return this.model.getOptions()},t.prototype.getLineCount=function(){return this.lines.getViewLineCount()},t.prototype.setViewport=function(e,t,n){this.viewportStartLine=e;var i=this.coordinatesConverter.convertViewPositionToModelPosition(new s["a"](e,this.getLineMinColumn(e)));this.viewportStartLineTrackedRange=this.model._setTrackedRange(this.viewportStartLineTrackedRange,new a["a"](i.lineNumber,i.column,i.lineNumber,i.column),1);var r=this.viewLayout.getVerticalOffsetForLineNumber(e),o=this.viewLayout.getCurrentScrollTop();this.viewportStartLineDelta=o-r},t.prototype.getActiveIndentGuide=function(e,t,n){return this.lines.getActiveIndentGuide(e,t,n)},t.prototype.getLinesIndentGuides=function(e,t){return this.lines.getViewLinesIndentGuides(e,t)},t.prototype.getLineContent=function(e){return this.lines.getViewLineContent(e)},t.prototype.getLineLength=function(e){return this.lines.getViewLineLength(e)},t.prototype.getLineMinColumn=function(e){return this.lines.getViewLineMinColumn(e)},t.prototype.getLineMaxColumn=function(e){return this.lines.getViewLineMaxColumn(e)},t.prototype.getLineFirstNonWhitespaceColumn=function(e){var t=r["q"](this.getLineContent(e));return-1===t?0:t+1},t.prototype.getLineLastNonWhitespaceColumn=function(e){var t=r["D"](this.getLineContent(e));return-1===t?0:t+2},t.prototype.getDecorationsInViewport=function(e){return this.decorations.getDecorationsViewportData(e).decorations},t.prototype.getViewLineRenderingData=function(e,t){var n=this.model.mightContainRTL(),i=this.model.mightContainNonBasicASCII(),r=this.getTabSize(),o=this.lines.getViewLineData(t),s=this.decorations.getDecorationsViewportData(e).inlineDecorations,a=s[t-e.startLineNumber];return new v["d"](o.minColumn,o.maxColumn,o.content,o.continuesWithWrappedLine,n,i,o.tokens,a,r,o.startVisibleColumn)},t.prototype.getViewLineData=function(e){return this.lines.getViewLineData(e)},t.prototype.getMinimapLinesRenderingData=function(e,t,n){var i=this.lines.getViewLinesData(e,t,n);return new v["b"](this.getTabSize(),i)},t.prototype.getAllOverviewRulerDecorations=function(e){return this.lines.getAllOverviewRulerDecorations(this.editorId,Object(o["j"])(this.configuration.options),e)},t.prototype.invalidateOverviewRulerColorCache=function(){for(var e=this.model.getOverviewRulerDecorations(),t=0,n=e;t<n.length;t++){var i=n[t],r=i.options.overviewRuler;r&&r.invalidateCachedColor()}},t.prototype.invalidateMinimapColorCache=function(){for(var e=this.model.getAllDecorations(),t=0,n=e;t<n.length;t++){var i=n[t],r=i.options.minimap;r&&r.invalidateCachedColor()}},t.prototype.getValueInRange=function(e,t){var n=this.coordinatesConverter.convertViewRangeToModelRange(e);return this.model.getValueInRange(n,t)},t.prototype.getModelLineMaxColumn=function(e){return this.model.getLineMaxColumn(e)},t.prototype.validateModelPosition=function(e){return this.model.validatePosition(e)},t.prototype.validateModelRange=function(e){return this.model.validateRange(e)},t.prototype.deduceModelPositionRelativeToViewPosition=function(e,t,n){var i=this.coordinatesConverter.convertViewPositionToModelPosition(e);2===this.model.getEOL().length&&(t<0?t-=n:t+=n);var r=this.model.getOffsetAt(i),o=r+t;return this.model.getPositionAt(o)},t.prototype.getEOL=function(){return this.model.getEOL()},t.prototype.getPlainTextToCopy=function(e,t,n){var i=n?"\r\n":this.model.getEOL();e=e.slice(0),e.sort(a["a"].compareRangesUsingStarts);for(var r=!1,o=!1,s=0,u=e;s<u.length;s++){var l=u[s];l.isEmpty()?r=!0:o=!0}if(!o){if(!t)return"";for(var c=e.map((function(e){return e.startLineNumber})),h="",d=0;d<c.length;d++)d>0&&c[d-1]===c[d]||(h+=this.model.getLineContent(c[d])+i);return h}if(r&&t){for(var f=[],g=0,p=0,m=e;p<m.length;p++){var _=m[p],v=_.startLineNumber;_.isEmpty()?v!==g&&f.push(this.model.getLineContent(v)):f.push(this.model.getValueInRange(_,n?2:0)),g=v}return 1===f.length?f[0]:f}for(var y=[],b=0,C=e;b<C.length;b++){_=C[b];_.isEmpty()||y.push(this.model.getValueInRange(_,n?2:0))}return 1===y.length?y[0]:y},t.prototype.getRichTextToCopy=function(e,t){var n=this.model.getLanguageIdentifier();if(1===n.id)return null;if(1!==e.length)return null;var i=e[0];if(i.isEmpty()){if(!t)return null;var r=i.startLineNumber;i=new a["a"](r,this.model.getLineMinColumn(r),r,this.model.getLineMaxColumn(r))}var s=this.configuration.options.get(34),u=this._getColorMap(),l=s.fontFamily===o["b"].fontFamily?s.fontFamily:"'"+s.fontFamily+"', "+o["b"].fontFamily;return{mode:n.language,html:'<div style="color: '+u[1]+";background-color: "+u[2]+";font-family: "+l+";font-weight: "+s.fontWeight+";font-size: "+s.fontSize+"px;line-height: "+s.lineHeight+'px;white-space: pre;">'+this._getHTMLToCopy(i,u)+"</div>"}},t.prototype._getHTMLToCopy=function(e,t){for(var n=e.startLineNumber,i=e.startColumn,r=e.endLineNumber,o=e.endColumn,s=this.getTabSize(),a="",u=n;u<=r;u++){var c=this.model.getLineTokens(u),h=c.getLineContent(),d=u===n?i-1:0,f=u===r?o-1:h.length;a+=""===h?"<br>":Object(l["a"])(h,c.inflate(),t,d,f,s,M["h"])}return a},t.prototype._getColorMap=function(){var e=u["B"].getColorMap(),t=["#000000"];if(e)for(var n=1,r=e.length;n<r;n++)t[n]=i["a"].Format.CSS.formatHex(e[n]);return t},t}(h["e"])},a1f8:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var i=function(){function e(e){this._eventHandlerGateKeeper=e,this._eventHandlers=[],this._eventQueue=null,this._isConsumingQueue=!1}return e.prototype.addEventHandler=function(e){for(var t=0,n=this._eventHandlers.length;t<n;t++)this._eventHandlers[t]===e&&console.warn("Detected duplicate listener in ViewEventDispatcher",e);this._eventHandlers.push(e)},e.prototype.removeEventHandler=function(e){for(var t=0;t<this._eventHandlers.length;t++)if(this._eventHandlers[t]===e){this._eventHandlers.splice(t,1);break}},e.prototype.emit=function(e){this._eventQueue?this._eventQueue.push(e):this._eventQueue=[e],this._isConsumingQueue||this.consumeQueue()},e.prototype.emitMany=function(e){this._eventQueue?this._eventQueue=this._eventQueue.concat(e):this._eventQueue=e,this._isConsumingQueue||this.consumeQueue()},e.prototype.consumeQueue=function(){var e=this;this._eventHandlerGateKeeper((function(){try{e._isConsumingQueue=!0,e._doConsumeQueue()}finally{e._isConsumingQueue=!1}}))},e.prototype._doConsumeQueue=function(){while(this._eventQueue){var e=this._eventQueue;this._eventQueue=null;for(var t=this._eventHandlers.slice(0),n=0,i=t.length;n<i;n++)t[n].handleEvents(e)}},e}()},a21f:function(e,t,n){"use strict";n.d(t,"d",(function(){return o})),n.d(t,"c",(function(){return s})),n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return u})),n.d(t,"e",(function(){return l}));var i=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),r=function(){function e(e,t){this._viewLayout=e,this.viewportData=t,this.scrollWidth=this._viewLayout.getScrollWidth(),this.scrollHeight=this._viewLayout.getScrollHeight(),this.visibleRange=this.viewportData.visibleRange,this.bigNumbersDelta=this.viewportData.bigNumbersDelta;var n=this._viewLayout.getCurrentViewport();this.scrollTop=n.top,this.scrollLeft=n.left,this.viewportWidth=n.width,this.viewportHeight=n.height}return e.prototype.getScrolledTopFromAbsoluteTop=function(e){return e-this.scrollTop},e.prototype.getVerticalOffsetForLineNumber=function(e){return this._viewLayout.getVerticalOffsetForLineNumber(e)},e.prototype.getDecorationsInViewport=function(){return this.viewportData.getDecorationsInViewport()},e}(),o=function(e){function t(t,n,i){var r=e.call(this,t,n)||this;return r._viewLines=i,r}return i(t,e),t.prototype.linesVisibleRangesForRange=function(e,t){return this._viewLines.linesVisibleRangesForRange(e,t)},t.prototype.visibleRangeForPosition=function(e){return this._viewLines.visibleRangeForPosition(e)},t}(r),s=function(){function e(e,t,n){this.outsideRenderedLine=e,this.lineNumber=t,this.ranges=n}return e}(),a=function(){function e(e,t){this.left=Math.round(e),this.width=Math.round(t)}return e.prototype.toString=function(){return"["+this.left+","+this.width+"]"},e}(),u=function(){function e(e,t){this.outsideRenderedLine=e,this.left=Math.round(t)}return e}(),l=function(){function e(e,t){this.outsideRenderedLine=e,this.ranges=t}return e}()},a40b:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var i=n("0a0f"),r="editorWorkerService",o=Object(i["c"])(r)},a8d0:function(e,t,n){"use strict";n.d(t,"f",(function(){return r})),n.d(t,"b",(function(){return o})),n.d(t,"c",(function(){return s})),n.d(t,"d",(function(){return a})),n.d(t,"a",(function(){return u})),n.d(t,"e",(function(){return l}));var i=n("3742"),r=function(){function e(e,t,n,i){this.top=0|e,this.left=0|t,this.width=0|n,this.height=0|i}return e}(),o=function(){function e(e,t){this.tabSize=e,this.data=t}return e}(),s=function(){function e(e,t,n,i,r,o){this.content=e,this.continuesWithWrappedLine=t,this.minColumn=n,this.maxColumn=i,this.startVisibleColumn=r,this.tokens=o}return e}(),a=function(){function e(t,n,i,r,o,s,a,u,l,c){this.minColumn=t,this.maxColumn=n,this.content=i,this.continuesWithWrappedLine=r,this.isBasicASCII=e.isBasicASCII(i,s),this.containsRTL=e.containsRTL(i,this.isBasicASCII,o),this.tokens=a,this.inlineDecorations=u,this.tabSize=l,this.startVisibleColumn=c}return e.isBasicASCII=function(e,t){return!t||i["v"](e)},e.containsRTL=function(e,t,n){return!(t||!n)&&i["i"](e)},e}(),u=function(){function e(e,t,n){this.range=e,this.inlineClassName=t,this.type=n}return e}(),l=function(){function e(e,t){this.range=e,this.options=t}return e}()},af50:function(e,t,n){"use strict";n.d(t,"a",(function(){return s}));var i=n("567d"),r=n("ef8e"),o=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}();function s(e,t){return new a(e,t)}var a=function(e){function t(t,n){var i=e.call(this,t,n.keepIdleModels||!1,n.label)||this;return i._foreignModuleId=n.moduleId,i._foreignModuleCreateData=n.createData||null,i._foreignModuleHost=n.host||null,i._foreignProxy=null,i}return o(t,e),t.prototype.fhr=function(e,t){if(!this._foreignModuleHost||"function"!==typeof this._foreignModuleHost[e])return Promise.reject(new Error("Missing method "+e+" or missing main thread foreign host."));try{return Promise.resolve(this._foreignModuleHost[e].apply(this._foreignModuleHost,t))}catch(n){return Promise.reject(n)}},t.prototype._getForeignProxy=function(){var e=this;return this._foreignProxy||(this._foreignProxy=this._getProxy().then((function(t){var n=e._foreignModuleHost?r["c"](e._foreignModuleHost):[];return t.loadForeignModule(e._foreignModuleId,e._foreignModuleCreateData,n).then((function(n){e._foreignModuleCreateData=null;for(var i=function(e,n){return t.fmr(e,n)},r=function(e,t){return function(){var n=Array.prototype.slice.call(arguments,0);return t(e,n)}},o={},s=0,a=n;s<a.length;s++){var u=a[s];o[u]=r(u,i)}return o}))}))),this._foreignProxy},t.prototype.getProxy=function(){return this._getForeignProxy()},t.prototype.withSyncedResources=function(e){var t=this;return this._withSyncedResources(e).then((function(e){return t.getProxy()}))},t}(i["a"])},b78f:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var i=n("0a0f"),r=Object(i["c"])("textModelService")},d19c:function(e,t,n){"use strict";n.d(t,"a",(function(){return b}));var i=n("308f"),r=n("a666"),o=function(){function e(e){this._languageIdentifier=e}return e.prototype.getId=function(){return this._languageIdentifier.language},e}(),s=n("8bf1"),a=n("fdcc"),u=n("b9b4"),l=n("3742"),c=n("b707"),h=n("32a4"),d=n("0910"),f=n("89cd"),g=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),p=Object.prototype.hasOwnProperty,m=function(e){function t(t,n){void 0===t&&(t=!0),void 0===n&&(n=!1);var r=e.call(this)||this;return r._onDidChange=r._register(new i["a"]),r.onDidChange=r._onDidChange.event,r._warnOnOverwrite=n,r._nextLanguageId2=1,r._languageIdToLanguage=[],r._languageToLanguageId=Object.create(null),r._languages={},r._mimeTypesMap={},r._nameMap={},r._lowercaseNameMap={},t&&(r._initializeFromRegistry(),r._register(h["a"].onDidChangeLanguages((function(e){return r._initializeFromRegistry()})))),r}return g(t,e),t.prototype._initializeFromRegistry=function(){this._languages={},this._mimeTypesMap={},this._nameMap={},this._lowercaseNameMap={};var e=h["a"].getLanguages();this._registerLanguages(e)},t.prototype._registerLanguages=function(e){for(var t=this,n=0,i=e;n<i.length;n++){var r=i[n];this._registerLanguage(r)}this._mimeTypesMap={},this._nameMap={},this._lowercaseNameMap={},Object.keys(this._languages).forEach((function(e){var n=t._languages[e];n.name&&(t._nameMap[n.name]=n.identifier),n.aliases.forEach((function(e){t._lowercaseNameMap[e.toLowerCase()]=n.identifier})),n.mimetypes.forEach((function(e){t._mimeTypesMap[e]=n.identifier}))})),f["a"].as(d["a"].Configuration).registerOverrideIdentifiers(h["a"].getLanguages().map((function(e){return e.id}))),this._onDidChange.fire()},t.prototype._getLanguageId=function(e){if(this._languageToLanguageId[e])return this._languageToLanguageId[e];var t=this._nextLanguageId2++;return this._languageIdToLanguage[t]=e,this._languageToLanguageId[e]=t,t},t.prototype._registerLanguage=function(e){var t,n=e.id;if(p.call(this._languages,n))t=this._languages[n];else{var i=this._getLanguageId(n);t={identifier:new c["r"](n,i),name:null,mimetypes:[],aliases:[],extensions:[],filenames:[],configurationFiles:[]},this._languages[n]=t}this._mergeLanguage(t,e)},t.prototype._mergeLanguage=function(e,t){var n,i=t.id,r=null;if(Array.isArray(t.mimetypes)&&t.mimetypes.length>0&&((n=e.mimetypes).push.apply(n,t.mimetypes),r=t.mimetypes[0]),r||(r="text/x-"+i,e.mimetypes.push(r)),Array.isArray(t.extensions))for(var o=0,s=t.extensions;o<s.length;o++){var c=s[o];u["b"]({id:i,mime:r,extension:c},this._warnOnOverwrite),e.extensions.push(c)}if(Array.isArray(t.filenames))for(var h=0,d=t.filenames;h<d.length;h++){var f=d[h];u["b"]({id:i,mime:r,filename:f},this._warnOnOverwrite),e.filenames.push(f)}if(Array.isArray(t.filenamePatterns))for(var g=0,p=t.filenamePatterns;g<p.length;g++){var m=p[g];u["b"]({id:i,mime:r,filepattern:m},this._warnOnOverwrite)}if("string"===typeof t.firstLine&&t.firstLine.length>0){var _=t.firstLine;"^"!==_.charAt(0)&&(_="^"+_);try{var v=new RegExp(_);l["I"](v)||u["b"]({id:i,mime:r,firstline:v},this._warnOnOverwrite)}catch(O){Object(a["e"])(O)}}e.aliases.push(i);var y=null;if("undefined"!==typeof t.aliases&&Array.isArray(t.aliases)&&(y=0===t.aliases.length?[null]:t.aliases),null!==y)for(var b=0,C=y;b<C.length;b++){var L=C[b];L&&0!==L.length&&e.aliases.push(L)}var w=null!==y&&y.length>0;if(w&&null===y[0]);else{var S=(w?y[0]:null)||i;!w&&e.name||(e.name=S)}t.configuration&&e.configurationFiles.push(t.configuration)},t.prototype.isRegisteredMode=function(e){return!!p.call(this._mimeTypesMap,e)||p.call(this._languages,e)},t.prototype.getModeIdForLanguageNameLowercase=function(e){return p.call(this._lowercaseNameMap,e)?this._lowercaseNameMap[e].language:null},t.prototype.extractModeIds=function(e){var t=this;return e?e.split(",").map((function(e){return e.trim()})).map((function(e){return p.call(t._mimeTypesMap,e)?t._mimeTypesMap[e].language:e})).filter((function(e){return p.call(t._languages,e)})):[]},t.prototype.getLanguageIdentifier=function(e){if(e===s["b"]||0===e)return s["a"];var t;if("string"===typeof e)t=e;else if(t=this._languageIdToLanguage[e],!t)return null;return p.call(this._languages,t)?this._languages[t].identifier:null},t.prototype.getModeIdsFromFilepathOrFirstLine=function(e,t){if(!e&&!t)return[];var n=u["a"](e,t);return this.extractModeIds(n.join(","))},t}(r["a"]),_=n("e8e3"),v=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),y=function(e){function t(t,n){var r=e.call(this)||this;return r._onDidChange=r._register(new i["a"]),r.onDidChange=r._onDidChange.event,r._selector=n,r.languageIdentifier=r._selector(),r._register(t((function(){return r._evaluate()}))),r}return v(t,e),t.prototype._evaluate=function(){var e=this._selector();e.id!==this.languageIdentifier.id&&(this.languageIdentifier=e,this._onDidChange.fire(this.languageIdentifier))},t}(r["a"]),b=function(){function e(e){var t=this;void 0===e&&(e=!1),this._onDidCreateMode=new i["a"],this.onDidCreateMode=this._onDidCreateMode.event,this._onLanguagesMaybeChanged=new i["a"],this.onLanguagesMaybeChanged=this._onLanguagesMaybeChanged.event,this._instantiatedModes={},this._registry=new m(!0,e),this._registry.onDidChange((function(){return t._onLanguagesMaybeChanged.fire()}))}return e.prototype.isRegisteredMode=function(e){return this._registry.isRegisteredMode(e)},e.prototype.getModeIdForLanguageName=function(e){return this._registry.getModeIdForLanguageNameLowercase(e)},e.prototype.getModeIdByFilepathOrFirstLine=function(e,t){var n=this._registry.getModeIdsFromFilepathOrFirstLine(e,t);return Object(_["l"])(n,null)},e.prototype.getModeId=function(e){var t=this._registry.extractModeIds(e);return Object(_["l"])(t,null)},e.prototype.getLanguageIdentifier=function(e){return this._registry.getLanguageIdentifier(e)},e.prototype.create=function(e){var t=this;return new y(this.onLanguagesMaybeChanged,(function(){var n=t.getModeId(e);return t._createModeAndGetLanguageIdentifier(n)}))},e.prototype.createByFilepathOrFirstLine=function(e,t){var n=this;return new y(this.onLanguagesMaybeChanged,(function(){var i=n.getModeIdByFilepathOrFirstLine(e,t);return n._createModeAndGetLanguageIdentifier(i)}))},e.prototype._createModeAndGetLanguageIdentifier=function(e){var t=this.getLanguageIdentifier(e||"plaintext")||s["a"];return this._getOrCreateMode(t.language),t},e.prototype.triggerMode=function(e){var t=this.getModeId(e);this._getOrCreateMode(t||"plaintext")},e.prototype._getOrCreateMode=function(e){if(!this._instantiatedModes.hasOwnProperty(e)){var t=this.getLanguageIdentifier(e)||s["a"];this._instantiatedModes[e]=new o(t),this._onDidCreateMode.fire(this._instantiatedModes[e])}return this._instantiatedModes[e]},e}()},d1a7:function(e,t,n){"use strict";n.d(t,"a",(function(){return S}));var i=n("308f"),r=n("a666"),o=n("30db"),s=n("fdcc"),a=n("fd49"),u=n("b57f"),l=n("b707"),c=n("32a4"),h=n("7b4a"),d=n("fbba"),f=n("5fe7"),g=n("2504"),p=n("4111"),m=n("b7d0"),_=n("d3d7"),v=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),y=function(e,t,n,i){var r,o=arguments.length,s=o<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,n):i;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)s=Reflect.decorate(e,t,n,i);else for(var a=e.length-1;a>=0;a--)(r=e[a])&&(s=(o<3?r(s):o>3?r(t,n,s):r(t,n))||s);return o>3&&s&&Object.defineProperty(t,n,s),s},b=function(e,t){return function(n,i){t(n,i,e)}};function C(e){return e.toString()}var L=function(){function e(e,t,n){this._modelEventListeners=new r["b"],this.model=e,this._languageSelection=null,this._languageSelectionListener=null,this._modelEventListeners.add(e.onWillDispose((function(){return t(e)}))),this._modelEventListeners.add(e.onDidChangeLanguage((function(t){return n(e,t)})))}return e.prototype._disposeLanguageSelection=function(){this._languageSelectionListener&&(this._languageSelectionListener.dispose(),this._languageSelectionListener=null),this._languageSelection&&(this._languageSelection.dispose(),this._languageSelection=null)},e.prototype.dispose=function(){this._modelEventListeners.dispose(),this._disposeLanguageSelection()},e.prototype.setLanguage=function(e){var t=this;this._disposeLanguageSelection(),this._languageSelection=e,this._languageSelectionListener=this._languageSelection.onDidChange((function(){return t.model.setMode(e.languageIdentifier)})),this.model.setMode(e.languageIdentifier)},e}(),w=o["d"]||o["e"]?1:2,S=function(e){function t(t,n,r,o){var s=e.call(this)||this;return s._onModelAdded=s._register(new i["a"]),s.onModelAdded=s._onModelAdded.event,s._onModelRemoved=s._register(new i["a"]),s.onModelRemoved=s._onModelRemoved.event,s._onModelModeChanged=s._register(new i["a"]),s.onModelModeChanged=s._onModelModeChanged.event,s._configurationService=t,s._resourcePropertiesService=n,s._models={},s._modelCreationOptionsByLanguageAndResource=Object.create(null),s._configurationServiceSubscription=s._configurationService.onDidChangeConfiguration((function(e){return s._updateModelOptions()})),s._updateModelOptions(),s._register(new O(s,r,t,o)),s}return v(t,e),t._readModelOptions=function(e,t){var n=a["c"].tabSize;if(e.editor&&"undefined"!==typeof e.editor.tabSize){var i=parseInt(e.editor.tabSize,10);isNaN(i)||(n=i),n<1&&(n=1)}var r=n;if(e.editor&&"undefined"!==typeof e.editor.indentSize&&"tabSize"!==e.editor.indentSize){var o=parseInt(e.editor.indentSize,10);isNaN(o)||(r=o),r<1&&(r=1)}var s=a["c"].insertSpaces;e.editor&&"undefined"!==typeof e.editor.insertSpaces&&(s="false"!==e.editor.insertSpaces&&Boolean(e.editor.insertSpaces));var u=w,l=e.eol;"\r\n"===l?u=2:"\n"===l&&(u=1);var c=a["c"].trimAutoWhitespace;e.editor&&"undefined"!==typeof e.editor.trimAutoWhitespace&&(c="false"!==e.editor.trimAutoWhitespace&&Boolean(e.editor.trimAutoWhitespace));var h=a["c"].detectIndentation;e.editor&&"undefined"!==typeof e.editor.detectIndentation&&(h="false"!==e.editor.detectIndentation&&Boolean(e.editor.detectIndentation));var d=a["c"].largeFileOptimizations;return e.editor&&"undefined"!==typeof e.editor.largeFileOptimizations&&(d="false"!==e.editor.largeFileOptimizations&&Boolean(e.editor.largeFileOptimizations)),{isForSimpleWidget:t,tabSize:n,indentSize:r,insertSpaces:s,detectIndentation:h,defaultEOL:u,trimAutoWhitespace:c,largeFileOptimizations:d}},t.prototype.getCreationOptions=function(e,n,i){var r=this._modelCreationOptionsByLanguageAndResource[e+n];if(!r){var o=this._configurationService.getValue("editor",{overrideIdentifier:e,resource:n}),s=this._resourcePropertiesService.getEOL(n,e);r=t._readModelOptions({editor:o,eol:s},i),this._modelCreationOptionsByLanguageAndResource[e+n]=r}return r},t.prototype._updateModelOptions=function(){var e=this._modelCreationOptionsByLanguageAndResource;this._modelCreationOptionsByLanguageAndResource=Object.create(null);for(var n=Object.keys(this._models),i=0,r=n.length;i<r;i++){var o=n[i],s=this._models[o],a=s.model.getLanguageIdentifier().language,u=s.model.uri,l=e[a+u],c=this.getCreationOptions(a,u,s.model.isForSimpleWidget);t._setModelOptionsForModel(s.model,c,l)}},t._setModelOptionsForModel=function(e,t,n){n&&n.defaultEOL!==t.defaultEOL&&1===e.getLineCount()&&e.setEOL(1===t.defaultEOL?0:1),n&&n.detectIndentation===t.detectIndentation&&n.insertSpaces===t.insertSpaces&&n.tabSize===t.tabSize&&n.indentSize===t.indentSize&&n.trimAutoWhitespace===t.trimAutoWhitespace||(t.detectIndentation?(e.detectIndentation(t.insertSpaces,t.tabSize),e.updateOptions({trimAutoWhitespace:t.trimAutoWhitespace})):e.updateOptions({insertSpaces:t.insertSpaces,tabSize:t.tabSize,indentSize:t.indentSize,trimAutoWhitespace:t.trimAutoWhitespace}))},t.prototype.dispose=function(){this._configurationServiceSubscription.dispose(),e.prototype.dispose.call(this)},t.prototype._createModelData=function(e,t,n,i){var r=this,o=this.getCreationOptions(t.language,n,i),s=new u["b"](e,o,t,n),a=C(s.uri);if(this._models[a])throw new Error("ModelService: Cannot add model because it already exists!");var l=new L(s,(function(e){return r._onWillDispose(e)}),(function(e,t){return r._onDidChangeLanguage(e,t)}));return this._models[a]=l,l},t.prototype.createModel=function(e,t,n,i){var r;return void 0===i&&(i=!1),t?(r=this._createModelData(e,t.languageIdentifier,n,i),this.setMode(r.model,t)):r=this._createModelData(e,c["b"],n,i),this._onModelAdded.fire(r.model),r.model},t.prototype.setMode=function(e,t){if(t){var n=this._models[C(e.uri)];n&&n.setLanguage(t)}},t.prototype.getModels=function(){for(var e=[],t=Object.keys(this._models),n=0,i=t.length;n<i;n++){var r=t[n];e.push(this._models[r].model)}return e},t.prototype.getModel=function(e){var t=C(e),n=this._models[t];return n?n.model:null},t.prototype._onWillDispose=function(e){var t=C(e.uri),n=this._models[t];delete this._models[t],n.dispose(),delete this._modelCreationOptionsByLanguageAndResource[e.getLanguageIdentifier().language+e.uri],this._onModelRemoved.fire(e)},t.prototype._onDidChangeLanguage=function(e,n){var i=n.oldLanguage,r=e.getLanguageIdentifier().language,o=this.getCreationOptions(i,e.uri,e.isForSimpleWidget),s=this.getCreationOptions(r,e.uri,e.isForSimpleWidget);t._setModelOptionsForModel(e,s,o),this._onModelModeChanged.fire({model:e,oldModeId:i})},t=y([b(0,d["a"]),b(1,h["b"]),b(2,m["c"]),b(3,_["a"])],t),t}(r["a"]),O=function(e){function t(n,i,r,o){var s=e.call(this)||this;s._configurationService=r,s._watchers=Object.create(null),s._semanticStyling=s._register(new I(i,o));var a=function(e){var n=r.getValue(t.SETTING_ID,{overrideIdentifier:e.getLanguageIdentifier().language,resource:e.uri});return n&&n.enabled},u=function(e){s._watchers[e.uri.toString()]=new E(e,i,s._semanticStyling)},l=function(e,t){t.dispose(),delete s._watchers[e.uri.toString()]};return s._register(n.onModelAdded((function(e){a(e)&&u(e)}))),s._register(n.onModelRemoved((function(e){var t=s._watchers[e.uri.toString()];t&&l(e,t)}))),s._configurationService.onDidChangeConfiguration((function(e){if(e.affectsConfiguration(t.SETTING_ID))for(var i=0,r=n.getModels();i<r.length;i++){var o=r[i],c=s._watchers[o.uri.toString()];a(o)?c||u(o):c&&l(o,c)}})),s}return v(t,e),t.SETTING_ID="editor.semanticHighlighting",t}(r["a"]),I=function(e){function t(t,n){var i=e.call(this)||this;return i._themeService=t,i._logService=n,i._caches=new WeakMap,i._themeService&&i._register(i._themeService.onThemeChange((function(){i._caches=new WeakMap}))),i}return v(t,e),t.prototype.get=function(e){return this._caches.has(e)||this._caches.set(e,new N(e.getLegend(),this._themeService,this._logService)),this._caches.get(e)},t}(r["a"]),M=function(){function e(e,t,n){this.tokenTypeIndex=e,this.tokenModifierSet=t,this.metadata=n,this.next=null}return e}(),k=function(){function e(){this._elementsCount=0,this._currentLengthIndex=0,this._currentLength=e._SIZES[this._currentLengthIndex],this._growCount=Math.round(this._currentLengthIndex+1<e._SIZES.length?2/3*this._currentLength:0),this._elements=[],e._nullOutEntries(this._elements,this._currentLength)}return e._nullOutEntries=function(e,t){for(var n=0;n<t;n++)e[n]=null},e.prototype._hashFunc=function(e,t){return((e<<5)-e+t|0)%this._currentLength},e.prototype.get=function(e,t){var n=this._hashFunc(e,t),i=this._elements[n];while(i){if(i.tokenTypeIndex===e&&i.tokenModifierSet===t)return i;i=i.next}return null},e.prototype.add=function(t,n,i){if(this._elementsCount++,0!==this._growCount&&this._elementsCount>=this._growCount){var r=this._elements;this._currentLengthIndex++,this._currentLength=e._SIZES[this._currentLengthIndex],this._growCount=Math.round(this._currentLengthIndex+1<e._SIZES.length?2/3*this._currentLength:0),this._elements=[],e._nullOutEntries(this._elements,this._currentLength);for(var o=0,s=r;o<s.length;o++){var a=s[o],u=a;while(u){var l=u.next;u.next=null,this._add(u),u=l}}}this._add(new M(t,n,i))},e.prototype._add=function(e){var t=this._hashFunc(e.tokenTypeIndex,e.tokenModifierSet);e.next=this._elements[t],this._elements[t]=e},e._SIZES=[3,7,13,31,61,127,251,509,1021,2039,4093,8191,16381,32749,65521,131071,262139,524287,1048573,2097143],e}(),N=function(){function e(e,t,n){this._legend=e,this._themeService=t,this._logService=n,this._hashTable=new k}return e.prototype.getMetadata=function(e,t){var n,i=this._hashTable.get(e,t);if(i)n=i.metadata;else{for(var r=this._legend.tokenTypes[e],o=[],s=t,a=0;s>0&&a<this._legend.tokenModifiers.length;a++)1&s&&o.push(this._legend.tokenModifiers[a]),s>>=1;var u=this._themeService.getTheme().getTokenStyleMetadata(r,o);if("undefined"===typeof u)n=**********;else{if(n=0,"undefined"!==typeof u.italic){var c=(u.italic?1:0)<<11;n|=1|c}if("undefined"!==typeof u.bold){var h=(u.bold?2:0)<<11;n|=2|h}if("undefined"!==typeof u.underline){var d=(u.underline?4:0)<<11;n|=4|d}if(u.foreground){var f=u.foreground<<14;n|=8|f}0===n&&(n=**********)}this._hashTable.add(e,t,n)}if(this._logService.getLevel()===_["b"].Trace){var g=this._legend.tokenTypes[e],p=t?" "+this._legend.tokenModifiers.filter((function(e,n){return t&1<<n})).join(" "):"";this._logService.trace("tokenStyleMetadata "+(i?"[CACHED] ":"")+g+p+": foreground "+l["A"].getForeground(n)+", fontStyle "+l["A"].getFontStyle(n).toString(2))}return n},e}(),T=function(){function e(e,t,n){this._provider=e,this.resultId=t,this.data=n}return e.prototype.dispose=function(){this._provider.releaseDocumentSemanticTokens(this.resultId)},e}(),E=function(e){function t(t,n,i){var r=e.call(this)||this;return r._isDisposed=!1,r._model=t,r._semanticStyling=i,r._fetchSemanticTokens=r._register(new f["d"]((function(){return r._fetchSemanticTokensNow()}),300)),r._currentResponse=null,r._currentRequestCancellationTokenSource=null,r._register(r._model.onDidChangeContent((function(e){r._fetchSemanticTokens.isScheduled()||r._fetchSemanticTokens.schedule()}))),r._register(l["l"].onDidChange((function(e){return r._fetchSemanticTokens.schedule()}))),n&&r._register(n.onThemeChange((function(e){r._setSemanticTokens(null,null,null,[]),r._fetchSemanticTokens.schedule()}))),r._fetchSemanticTokens.schedule(0),r}return v(t,e),t.prototype.dispose=function(){this._currentResponse&&(this._currentResponse.dispose(),this._currentResponse=null),this._currentRequestCancellationTokenSource&&(this._currentRequestCancellationTokenSource.cancel(),this._currentRequestCancellationTokenSource=null),this._setSemanticTokens(null,null,null,[]),this._isDisposed=!0,e.prototype.dispose.call(this)},t.prototype._fetchSemanticTokensNow=function(){var e=this;if(!this._currentRequestCancellationTokenSource){var t=this._getSemanticColoringProvider();if(t){this._currentRequestCancellationTokenSource=new g["b"];var n=[],i=this._model.onDidChangeContent((function(e){n.push(e)})),r=this._semanticStyling.get(t),o=this._currentResponse&&this._currentResponse.resultId||null,a=Promise.resolve(t.provideDocumentSemanticTokens(this._model,o,this._currentRequestCancellationTokenSource.token));a.then((function(o){e._currentRequestCancellationTokenSource=null,i.dispose(),e._setSemanticTokens(t,o||null,r,n)}),(function(t){t&&"string"===typeof t.message&&-1!==t.message.indexOf("busy")||s["e"](t),e._currentRequestCancellationTokenSource=null,i.dispose(),n.length>0&&(e._fetchSemanticTokens.isScheduled()||e._fetchSemanticTokens.schedule())}))}}},t._isSemanticTokens=function(e){return e&&!!e.data},t._isSemanticTokensEdits=function(e){return e&&Array.isArray(e.edits)},t._copy=function(e,t,n,i,r){for(var o=0;o<r;o++)n[i+o]=e[t+o]},t.prototype._setSemanticTokens=function(e,n,i,r){var o=this._currentResponse;if(this._currentResponse&&(this._currentResponse.dispose(),this._currentResponse=null),this._isDisposed)e&&n&&e.releaseDocumentSemanticTokens(n.resultId);else if(e&&n&&i){if(t._isSemanticTokensEdits(n)){if(!o)return void this._model.setSemanticTokens(null);if(0===n.edits.length)n={resultId:n.resultId,data:o.data};else{for(var s=0,a=0,u=n.edits;a<u.length;a++){var l=u[a];s+=(l.data?l.data.length:0)-l.deleteCount}for(var c=o.data,h=new Uint32Array(c.length+s),d=c.length,f=h.length,g=n.edits.length-1;g>=0;g--){l=n.edits[g];var m=d-(l.start+l.deleteCount);m>0&&(t._copy(c,d-m,h,f-m,m),f-=m),l.data&&(t._copy(l.data,0,h,f-l.data.length,l.data.length),f-=l.data.length),d=l.start}d>0&&t._copy(c,0,h,0,d),n={resultId:n.resultId,data:h}}}if(t._isSemanticTokens(n)){this._currentResponse=new T(e,n.resultId,n.data);c=n.data;var _=n.data.length/5|0,v=Math.max(Math.ceil(_/1024),400),y=[],b=0,C=1,L=0;while(b<_){var w=b,S=Math.min(w+v,_);if(S<_){var O=S;while(O-1>w&&0===c[5*O])O--;if(O-1===w){var I=S;while(I+1<_&&0===c[5*I])I++;S=I}else S=O}h=new Uint32Array(4*(S-w));var M=0,k=0;while(b<S){var N=5*b,E=c[N],A=c[N+1],x=C+E,R=0===E?L+A:A,V=c[N+2],P=c[N+3],D=c[N+4],W=i.getMetadata(P,D);**********!==W&&(0===k&&(k=x),h[M]=x-k,h[M+1]=R,h[M+2]=R+V,h[M+3]=W,M+=4),C=x,L=R,b++}M!==h.length&&(h=h.subarray(0,M));var F=new p["a"](k,new p["c"](h));y.push(F)}if(r.length>0){for(var H=0,B=r;H<B.length;H++)for(var j=B[H],U=0,z=y;U<z.length;U++)for(var K=z[U],Y=0,G=j.changes;Y<G.length;Y++){var q=G[Y];K.applyEdit(q.range,q.text)}this._fetchSemanticTokens.isScheduled()||this._fetchSemanticTokens.schedule()}this._model.setSemanticTokens(y)}else this._model.setSemanticTokens(null)}else this._model.setSemanticTokens(null)},t.prototype._getSemanticColoringProvider=function(){var e=l["l"].ordered(this._model);return e.length>0?e[0]:null},t}(r["a"])},e06b:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var i=n("6a89"),r=function(){function e(e,t,n,r){this.selections=e,this.startLineNumber=0|t.startLineNumber,this.endLineNumber=0|t.endLineNumber,this.relativeVerticalOffset=t.relativeVerticalOffset,this.bigNumbersDelta=0|t.bigNumbersDelta,this.whitespaceViewportData=n,this._model=r,this.visibleRange=new i["a"](t.startLineNumber,this._model.getLineMinColumn(t.startLineNumber),t.endLineNumber,this._model.getLineMaxColumn(t.endLineNumber))}return e.prototype.getViewLineRenderingData=function(e){return this._model.getViewLineRenderingData(this.visibleRange,e)},e.prototype.getDecorationsInViewport=function(){return this._model.getDecorationsInViewport(this.visibleRange)},e}()},e2dc:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var i=n("a666"),r=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),o=function(e){function t(){var t=e.call(this)||this;return t._shouldRender=!0,t}return r(t,e),t.prototype.shouldRender=function(){return this._shouldRender},t.prototype.forceShouldRender=function(){this._shouldRender=!0},t.prototype.setShouldRender=function(){this._shouldRender=!0},t.prototype.onDidRender=function(){this._shouldRender=!1},t.prototype.onConfigurationChanged=function(e){return!1},t.prototype.onContentSizeChanged=function(e){return!1},t.prototype.onCursorStateChanged=function(e){return!1},t.prototype.onDecorationsChanged=function(e){return!1},t.prototype.onFlushed=function(e){return!1},t.prototype.onFocusChanged=function(e){return!1},t.prototype.onLanguageConfigurationChanged=function(e){return!1},t.prototype.onLineMappingChanged=function(e){return!1},t.prototype.onLinesChanged=function(e){return!1},t.prototype.onLinesDeleted=function(e){return!1},t.prototype.onLinesInserted=function(e){return!1},t.prototype.onRevealRangeRequest=function(e){return!1},t.prototype.onScrollChanged=function(e){return!1},t.prototype.onThemeChanged=function(e){return!1},t.prototype.onTokensChanged=function(e){return!1},t.prototype.onTokensColorsChanged=function(e){return!1},t.prototype.onZonesChanged=function(e){return!1},t.prototype.handleEvents=function(e){for(var t=!1,n=0,i=e.length;n<i;n++){var r=e[n];switch(r.type){case 1:this.onConfigurationChanged(r)&&(t=!0);break;case 2:this.onContentSizeChanged(r)&&(t=!0);break;case 3:this.onCursorStateChanged(r)&&(t=!0);break;case 4:this.onDecorationsChanged(r)&&(t=!0);break;case 5:this.onFlushed(r)&&(t=!0);break;case 6:this.onFocusChanged(r)&&(t=!0);break;case 7:this.onLanguageConfigurationChanged(r)&&(t=!0);break;case 8:this.onLineMappingChanged(r)&&(t=!0);break;case 9:this.onLinesChanged(r)&&(t=!0);break;case 10:this.onLinesDeleted(r)&&(t=!0);break;case 11:this.onLinesInserted(r)&&(t=!0);break;case 12:this.onRevealRangeRequest(r)&&(t=!0);break;case 13:this.onScrollChanged(r)&&(t=!0);break;case 15:this.onTokensChanged(r)&&(t=!0);break;case 14:this.onThemeChanged(r)&&(t=!0);break;case 16:this.onTokensColorsChanged(r)&&(t=!0);break;case 17:this.onZonesChanged(r)&&(t=!0);break;default:console.info("View received unknown event: "),console.info(r)}}t&&(this._shouldRender=!0)},t}(i["a"])},e6e5:function(e,t,n){"use strict";n.d(t,"a",(function(){return b}));var i=n("b400"),r=n("a666"),o=n("3352"),s=n("b7d0"),a=n("918c"),u=n("1b69"),l=n("6a89"),c=n("4035"),h=n("b589"),d=n("308f"),f=n("ef8e"),g=n("303e"),p=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),m=function(e,t,n,i){var r,o=arguments.length,s=o<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,n):i;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)s=Reflect.decorate(e,t,n,i);else for(var a=e.length-1;a>=0;a--)(r=e[a])&&(s=(o<3?r(s):o>3?r(t,n,s):r(t,n))||s);return o>3&&s&&Object.defineProperty(t,n,s),s},_=function(e,t){return function(n,i){t(n,i,e)}};function v(e){return e.toString()}var y=function(e){function t(t){var n=e.call(this)||this;return n.model=t,n._markersData=new Map,n._register(Object(r["h"])((function(){n.model.deltaDecorations(Object(c["d"])(n._markersData),[]),n._markersData.clear()}))),n}return p(t,e),t.prototype.update=function(e,t){var n=Object(c["d"])(this._markersData);this._markersData.clear();for(var i=this.model.deltaDecorations(n,t),r=0;r<i.length;r++)this._markersData.set(i[r],e[r])},t.prototype.getMarker=function(e){return this._markersData.get(e.id)},t}(r["a"]),b=function(e){function t(t,n){var i=e.call(this)||this;return i._markerService=n,i._onDidChangeMarker=i._register(new d["a"]),i._markerDecorations=new Map,t.getModels().forEach((function(e){return i._onModelAdded(e)})),i._register(t.onModelAdded(i._onModelAdded,i)),i._register(t.onModelRemoved(i._onModelRemoved,i)),i._register(i._markerService.onMarkerChanged(i._handleMarkerChange,i)),i}return p(t,e),t.prototype.dispose=function(){e.prototype.dispose.call(this),this._markerDecorations.forEach((function(e){return e.dispose()})),this._markerDecorations.clear()},t.prototype.getMarker=function(e,t){var n=this._markerDecorations.get(v(e.uri));return n?Object(f["o"])(n.getMarker(t)):null},t.prototype._handleMarkerChange=function(e){var t=this;e.forEach((function(e){var n=t._markerDecorations.get(v(e));n&&t._updateDecorations(n)}))},t.prototype._onModelAdded=function(e){var t=new y(e);this._markerDecorations.set(v(e.uri),t),this._updateDecorations(t)},t.prototype._onModelRemoved=function(e){var t=this,n=this._markerDecorations.get(v(e.uri));n&&(n.dispose(),this._markerDecorations.delete(v(e.uri))),e.uri.scheme!==h["b"].inMemory&&e.uri.scheme!==h["b"].internal&&e.uri.scheme!==h["b"].vscode||this._markerService&&this._markerService.read({resource:e.uri}).map((function(e){return e.owner})).forEach((function(n){return t._markerService.remove(n,[e.uri])}))},t.prototype._updateDecorations=function(e){var t=this,n=this._markerService.read({resource:e.model.uri,take:500}),i=n.map((function(n){return{range:t._createDecorationRange(e.model,n),options:t._createDecorationOption(n)}}));e.update(n,i),this._onDidChangeMarker.fire(e.model)},t.prototype._createDecorationRange=function(e,t){var n=l["a"].lift(t);if(t.severity!==i["c"].Hint||this._hasMarkerTag(t,1)||this._hasMarkerTag(t,2)||(n=n.setEndPosition(n.startLineNumber,n.startColumn+2)),n=e.validateRange(n),n.isEmpty()){var r=e.getWordAtPosition(n.getStartPosition());if(r)n=new l["a"](n.startLineNumber,r.startColumn,n.endLineNumber,r.endColumn);else{var o=e.getLineLastNonWhitespaceColumn(n.startLineNumber)||e.getLineMaxColumn(n.startLineNumber);1===o||(n=n.endColumn>=o?new l["a"](n.startLineNumber,o-1,n.endLineNumber,o):new l["a"](n.startLineNumber,n.startColumn,n.endLineNumber,n.endColumn+1))}}else if(t.endColumn===Number.MAX_VALUE&&1===t.startColumn&&n.startLineNumber===n.endLineNumber){var s=e.getLineFirstNonWhitespaceColumn(t.startLineNumber);s<n.endColumn&&(n=new l["a"](n.startLineNumber,s,n.endLineNumber,n.endColumn),t.startColumn=s)}return n},t.prototype._createDecorationOption=function(e){var t,n,r,u=void 0,l=void 0;switch(e.severity){case i["c"].Hint:t=this._hasMarkerTag(e,2)?void 0:this._hasMarkerTag(e,1)?"squiggly-unnecessary":"squiggly-hint",n=0;break;case i["c"].Warning:t="squiggly-warning",u=Object(s["f"])(a["r"]),n=20,r={color:Object(s["f"])(g["Jb"]),position:o["c"].Inline};break;case i["c"].Info:t="squiggly-info",u=Object(s["f"])(a["q"]),n=10;break;case i["c"].Error:default:t="squiggly-error",u=Object(s["f"])(a["p"]),n=30,r={color:Object(s["f"])(g["Gb"]),position:o["c"].Inline};break}return e.tags&&(-1!==e.tags.indexOf(1)&&(l="squiggly-inline-unnecessary"),-1!==e.tags.indexOf(2)&&(l="squiggly-inline-deprecated")),{stickiness:1,className:t,showIfCollapsed:!0,overviewRuler:{color:u,position:o["d"].Right},minimap:r,zIndex:n,inlineClassName:l}},t.prototype._hasMarkerTag=function(e,t){return!!e.tags&&e.tags.indexOf(t)>=0},t=m([_(0,u["a"]),_(1,i["b"])],t),t}(r["a"])},efdb:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var i=n("0a0f"),r=Object(i["c"])("markerDecorationsService")},ff6c:function(e,t,n){"use strict";n.d(t,"a",(function(){return s}));var i=n("308f"),r=n("4503"),o=n("b707"),s=function(){function e(){var e=this;this._onDidChange=new i["a"],this.onDidChange=this._onDidChange.event,this._updateColorMap(),o["B"].onDidChange((function(t){t.changedColorMap&&e._updateColorMap()}))}return e.getInstance=function(){return this._INSTANCE||(this._INSTANCE=new e),this._INSTANCE},e.prototype._updateColorMap=function(){var e=o["B"].getColorMap();if(!e)return this._colors=[r["a"].Empty],void(this._backgroundIsLight=!0);this._colors=[r["a"].Empty];for(var t=1;t<e.length;t++){var n=e[t].rgba;this._colors[t]=new r["a"](n.r,n.g,n.b,Math.round(255*n.a))}var i=e[2].getRelativeLuminance();this._backgroundIsLight=i>=.5,this._onDidChange.fire(void 0)},e.prototype.getColor=function(e){return(e<1||e>=this._colors.length)&&(e=2),this._colors[e]},e.prototype.backgroundIsLight=function(){return this._backgroundIsLight},e._INSTANCE=null,e}()}}]);