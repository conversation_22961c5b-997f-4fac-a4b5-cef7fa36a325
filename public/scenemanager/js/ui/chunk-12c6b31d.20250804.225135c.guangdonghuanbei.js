(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-12c6b31d"],{"009f":function(e,t,a){"use strict";var n=a("a47e"),i={underlay:{name:n["a"].t("featureDatas.underlay.name"),label:"underlay",icon:"underlay_element",type:7,listState:!0,datas:[]},model:{name:n["a"].t("featureDatas.model.name"),label:"model",icon:"model_element",type:0,listState:!0,datas:[]},dem:{name:n["a"].t("featureDatas.dem.name"),label:"dem",icon:"dem_element",type:8,listState:!0,datas:[]},wmts:{name:n["a"].t("featureDatas.wmts.name"),label:"wmts",icon:"wmts_element",type:1,listState:!0,datas:[]},wms:{name:n["a"].t("featureDatas.wms.name"),label:"wms",icon:"wms_element",type:120,listState:!0,datas:[]},tms:{name:n["a"].t("featureDatas.tms.name"),label:"tms",icon:"tms_element",type:125,listState:!0,datas:[]},gltf:{name:n["a"].t("featureDatas.gltf.name"),label:"gltf",icon:"gltf_element",type:4,listState:!0,datas:[]},fbx:{name:n["a"].t("featureDatas.fbx.name"),label:"fbx",icon:"fbx",type:1,listState:!0,datas:[]},_3dBuilding:{name:n["a"].t("featureDatas._3dBuilding.name"),label:"_3dBuilding",icon:"_3dBuilding",type:null,listState:!0,datas:[]},_3dTiles:{name:n["a"].t("featureDatas._3dTiles.name"),label:"_3dTiles",icon:"_3dTiles",type:3,listState:!0,datas:[]},skybox:{name:n["a"].t("featureDatas.skybox.name"),label:"skybox",icon:"skybox_element",type:17,listState:!0,datas:[]},video:{name:n["a"].t("featureDatas.video.name"),label:"video",icon:"video_element",type:10,listState:!0,datas:[]},panorama:{name:n["a"].t("featureDatas.panorama.name"),label:"panorama",icon:"panorama_element",type:9,listState:!0,datas:[]},shield:{name:n["a"].t("featureDatas.shield.name"),label:"shield",icon:"shield_element",type:12,listState:!0,datas:[]},heatmap:{name:n["a"].t("featureDatas.heatmap.name"),label:"heatmap",icon:"heatmap_element",type:13,listState:!0,datas:[]},ripplewall:{name:n["a"].t("featureDatas.ripplewall.name"),label:"ripplewall",icon:"ripplewall_element",type:14,listState:!0,datas:[]},ring:{name:n["a"].t("featureDatas.ring.name"),label:"ring",icon:"ring_element",type:15,listState:!0,datas:[]},billboard:{name:n["a"].t("featureDatas.billboard.name"),label:"billboard",icon:"billboard",type:16,listState:!0,datas:[]},geoJSON:{name:n["a"].t("featureDatas.geoJSON.name"),label:"geoJSON",icon:"geojson_element",type:2,listState:!0,datas:[]},annotation:{name:n["a"].t("featureDatas.annotation.name"),label:"annotation",icon:"annotation_element",type:5,listState:!0,datas:[]},shp:{name:n["a"].t("featureDatas.shp.name"),label:"shp",icon:"shp_element",type:6,listState:!0,datas:[]},radar:{name:n["a"].t("featureDatas.radar.name"),label:"radar",icon:"radar_element",type:95,listState:!0,datas:[]},polyline:{name:n["a"].t("featureDatas.polyline.name"),label:"polyline",icon:"polygon_line_element",type:105,listState:!0,datas:[]},polygon:{name:n["a"].t("featureDatas.polygon.name"),label:"polygon",icon:"polygon_surface_element",type:100,listState:!0,datas:[]},flame:{name:n["a"].t("featureDatas.flame.name"),label:"flame",icon:"fire_element",type:110,listState:!0,datas:[]},smoke:{name:n["a"].t("featureDatas.smoke.name"),label:"smoke",icon:"smoke_element",type:115,listState:!0,datas:[]},"batch-extrude":{name:n["a"].t("featureDatas['batch-extrude'].name"),label:"batch_extrude",icon:"batch_extrude_element",type:120,listState:!0,datas:[]},kml:{name:n["a"].t("featureDatas.kml.name"),label:"kml",icon:"kml_element",type:125,listState:!0,datas:[]},vectorextrude:{name:n["a"].t("featureDatas.vectorextrude.name"),label:"vectorextrude",icon:"vectorextrude_element",type:130,listState:!0,datas:[]},waterfall:{name:n["a"].t("featureDatas.waterfall.name"),label:"waterfall",icon:"waterfall_element",type:116,listState:!0,datas:[]}};t["a"]=i},"0541":function(e,t,a){var n=a("23e7"),i=a("1a2d");n({target:"Object",stat:!0},{hasOwn:i})},"07ac":function(e,t,a){var n=a("23e7"),i=a("6f53").values;n({target:"Object",stat:!0},{values:function(e){return i(e)}})},2036:function(e,t,a){},"44aa":function(e,t,a){"use strict";a("2036")},"5de3":function(e,t,a){"use strict";a("b0c0"),a("d3b7"),a("159b");t["a"]={methods:{handelSceneStyleReset:function(e){var t=e?e.type:this.dragData.type,a=this.$store.state.scene.featureRawData;if(a=JSON.parse(a),null==a&&"advanced_material"!==t)return!1;switch(t){case"model":this.resetModelFeature(a);break;case"dem":this.resetDemFeature(a);break;case"_3dBuilding":this.resetBuildingFeature(a);break;case"fbx":case"gltf":this.resetGltfOrFbxFeature(a);break;case"annotation":case"billboard":this.resetAnchorFeature(a);break;case"_3dTiles":this.reset3DTilesFeature(a);break;case"panorama":this.resetPanoramaFeature(a);break;case"polygon":this.resetPolygonFeature(a);break;case"heatmap":this.resetHeatmapFeature(a);break;case"ripplewall":this.resetRipplewallFeature(a);break;case"radar":this.resetRadarFeature(a);break;case"shield":this.resetShieldFeature(a);break;case"ring":this.resetRingFeature(a);break;case"flame":case"smoke":this.resetSmoleFlameFeature(a);break;case"polyline":this.resetPolylineFeature(a);break;case"batch-extrude":this.resetBatchExtrudeFeature(a);break;case"vectorextrude":this.resetVectorExtrudeFeature(a);break;case"wmts":break;case"wms":break;case"tms":break;case"shp":this.resetSHPFeature(a);break;case"kml":break;case"geoJSON":this.resetGeoJSONFeature(a);break;case"advanced_envmap":this.resetAdvancedEnvmapDatas(a);break;case"advanced_setting":this.resetAdvancedSettingDatas(a);break;case"pathAnimation":this.resetPathAnimationDatas(a);break;case"waterfall":this.resetWaterfallDatas(a);break;case"trigger":this.resetTriggerSettingDatas(a);break;case"advanced_material":var n=this.$store.state.scene.advancedMaterialRawData;n.params.length>0&&(n.params=JSON.parse(n.params[0]),this.resetAdvancedMaterialDatas(n));break;case"projector":this.resetProjectorDatas(a);break;case"video":this.resetVideoDatas(a);break;case"snow":this.resetSnowFeature(a);break}setTimeout((function(){window.scene.render()}),300)},resetModelFeature:function(e){var t=window.scene.features.get(e.id);t.version!=e.version?(t.dispose(),window.scene.render(),this.handleModelVersionReset(e)):(t.origin=e.origin,t.altitude=e.altitude,t.rotation=e.rotation,t.name=e.name,t.offset=e.offset,t.area=e.area,t.part=e.part,t.space=e.space,t.texture=e.texture,e.correction&&(t.correction=e.correction)),setTimeout((function(){window.scene.render()}),300)},handleModelVersionReset:function(e){var t=window.location.origin+"/MODEL_URL";t=window.IP_CONFIG.MODEL_URL;var a=window.scene.addFeature("model",e.id);a.origin=e.origin,a.altitude=e.altitude,a.rotation=e.rotation,a.name=e.name,a.offset=e.offset,a.area=e.area,a.part=e.part,a.space=e.space,a.texture=e.texture,e.correction&&(a.correction=e.correction),a.server=t,a.modelID=e.modelID,a.vaultID=e.vaultID,a.version=e.version,a.load().then((function(){a.activeView().then((function(){window.scene.fit2Feature(a)}))}))},resetDemFeature:function(e){var t=window.scene.features.get(e.id);t.name=e.name,t.url=e.url,window.scene.postData({opacity:e.data.opacity},t.dataKey)},resetBuildingFeature:function(e){var t=window.scene.features.get(e.id);t.name=e.name,window.scene.postData({opacity:e.data.opacity,color:e.data.color,minzoom:e.data.minzoom},t.dataKey)},resetGltfOrFbxFeature:function(e){var t=window.scene.features.get(e.id);t.origin=e.origin,t.altitude=e.altitude,t.rotation=e.rotation,t.offset=e.offset,t.name=e.name,t.scale=e.scale,t.url=e.url,t.follow=e.follow,t.followType=e.followType,window.scene.postData({animation:e.data.animation,scale:e.data.scale},t.dataKey)},resetAnchorFeature:function(e){var t=window.scene.features.get(e.id);t.origin=e.origin,t.altitude=e.altitude,t.offset=e.offset,t.name=e.name,t.follow=e.follow,t.followType=e.followType;var a=e.data;window.scene.postData(a,t.dataKey)},reset3DTilesFeature:function(e){var t=window.scene.features.get(e.id);t.origin=e.origin,t.altitude=e.altitude,t.rotation=e.rotation,t.offset=e.offset,t.name=e.name,t.url=e.url,e.correction?t.correction=e.correction:t.correction=void 0,window.scene.postData(e.data,t.dataKey),t.id=e.id},resetPanoramaFeature:function(e){var t=window.scene.features.get(e.id);t.origin=e.origin,t.altitude=e.altitude,t.offset=e.offset,t.name=e.name,t.url=e.url,t.id=e.id,window.scene.postData({radius:e.data.radius,depthPath:e.data.depthPath},t.dataKey)},resetPolygonFeature:function(e){var t=window.scene.features.get(e.id);t.name=e.name,window.scene.postData(e.data,t.dataKey)},resetHeatmapFeature:function(e){var t=window.scene.features.get(e.id);t.name=e.name,t.altitude=e.altitude,window.scene.postData(e.data,t.dataKey)},resetRipplewallFeature:function(e){var t=window.scene.features.get(e.id);t.name=e.name,t.altitude=e.altitude,window.scene.postData(e.data,t.dataKey)},resetRadarFeature:function(e){var t=window.scene.features.get(e.id);t.origin=e.origin,t.altitude=e.altitude,t.offset=e.offset,t.name=e.name,t.follow=e.follow,t.followType=e.followType,window.scene.postData(e.data,t.dataKey)},resetShieldFeature:function(e){var t=window.scene.features.get(e.id);t.origin=e.origin,t.altitude=e.altitude,t.offset=e.offset,t.name=e.name,t.follow=e.follow,t.followType=e.followType,window.scene.postData(e.data,t.dataKey)},resetRingFeature:function(e){var t=window.scene.features.get(e.id);t.origin=e.origin,t.altitude=e.altitude,t.offset=e.offset,t.name=e.name,t.follow=e.follow,t.followType=e.followType,window.scene.postData(e.data,t.dataKey)},resetSmoleFlameFeature:function(e){var t=window.scene.features.get(e.id);t.origin=e.origin,t.altitude=e.altitude,t.offset=e.offset,t.name=e.name,t.follow=e.follow,t.followType=e.followType,window.scene.postData(e.data,t.dataKey)},resetWaterfallDatas:function(e){var t=window.scene.features.get(e.id);t.origin=e.origin,t.altitude=e.altitude,t.offset=e.offset,t.name=e.name,window.scene.postData(e.data,t.dataKey)},resetPolylineFeature:function(e){var t=window.scene.features.get(e.id);t.name=e.name,window.scene.postData(e.data,t.dataKey)},resetBatchExtrudeFeature:function(e){var t=window.scene.features.get(e.id);t.name=e.name,t.origin=e.origin,window.scene.postData(e.data,t.dataKey)},resetVectorExtrudeFeature:function(e){var t=window.scene.features.get(e.id);t.name=e.name,t.url=e.url,window.scene.postData(e.data,t.dataKey)},resetGeoJSONFeature:function(e){var t=window.scene.features.get(e.id);t.name=e.name,t.url=e.url,window.scene.postData(e.data,t.dataKey)},resetSHPFeature:function(e){var t=window.scene.features.get(e.id);t.name=e.name,t.url=e.url,window.scene.postData(e.data,t.dataKey)},resetAdvancedEnvmapDatas:function(e){window.scene.config.envmapIndex=e.img,window.scene.config.envAngle=e.angle,window.scene.config.envmapIntensity=e.intensity,window.scene.config.fogRange=e.fogRange,window.scene.config.fogIntensity=e.fogIntensity,setTimeout((function(){window.scene.render()}),300)},resetAdvancedSettingDatas:function(e){window.scene.config.cullingRatio=e.cullingRatio,window.scene.config.maxMemory=e.maxMemory,window.scene.config.dynamicReleasing=e.dynamicReleasing,window.scene.config.highQuality=e.highQuality,window.scene.config.highlightColor=e.highlightColor,window.scene.config.mapboxToken=e.mapboxToken,window.scene.config.highlight=e.highlight,window.scene.config.ssao=e.ssao,window.scene.config.shadow=e.shadow,window.scene.mv.renderMode=e.modeRadio},resetPathAnimationDatas:function(e){var t=this.$store.state.dialog.activeDialog;-1!==t.indexOf("pathAnimation")&&this.$store.commit("toggleActiveDialog","pathAnimation");var a=window.scene.findPathRoaming(e.id);window.scene.mv.tools.pathRoam.init(a),this.$store.commit("tooglePathanimationActive",!0)},resetTriggerSettingDatas:function(e){var t=window.scene.triggers.get(e.id);t.name=e.name,"range"==e.subType&&(t.radius=e.radius,t.center=e.center),"sensor"==e.subType&&(t.points=e.points,"element"==e.object.type?t.object=window.scene.findObject(e.object.id):t.object=window.scene.features.get(e.object.id)),t.triggerCommands=e.triggerCommands,t.exitCommands=e.exitCommands},resetAdvancedMaterialDatas:function(e){for(var t in!e.originalSign&&window.MaterialParams.original&&delete window.MaterialParams.original,window.MaterialParams)"maps"==t?(window.MaterialParams.maps=[],e.params.maps.length&&e.params.maps.forEach((function(e,t){for(var a in window.MaterialParams.maps[t]={},e)window.MaterialParams.maps[t][a]=e[a]}))):t in e.params?window.MaterialParams[t]=e.params[t]:delete window.MaterialParams[t];window.scene.mv.materialService.updateMaterial(window.MaterialParams),this.$store.commit("saveMaterialRawData","")},resetProjectorDatas:function(e){var t=window.scene.features.get(e.id);t.name=e.name,t.url=e.url,window.scene.postData(e.data,t.dataKey)},resetVideoDatas:function(e){var t=window.scene.features.get(e.id);t.url=e.url,t.priority=e.priority,t.always=e.always,t.follow=e.follow,t.followType=e.followType,window.scene.postData(e.data,t.dataKey)},resetSnowFeature:function(e){var t=window.scene.features.get(e.id);t.origin=e.origin,t.altitude=e.altitude,t.offset=e.offset,t.name=e.name,window.scene.postData(e.data,t.dataKey)}}}},6230:function(e,t,a){},a6ca:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"bottom-menu-container",class:{"setting-open":""!==e.settingShow}},["model"===e.elementMenu&&e.hideModelMenu?a("div",{staticClass:"component-wrap"},[a("div",{staticClass:"name-btn",staticStyle:{"font-size":"12px"},on:{click:function(t){return e.changeVisible()}}},[a("span",[e._v(e._s(e.$t("formRelational.element.label")))])]),a("transition",{attrs:{name:"sliderFade",mode:"out-in"}},[a("div",{directives:[{name:"show",rawName:"v-show",value:e.visible,expression:"visible"}],staticClass:"wrap-menu-list",class:{"overflow-hidden":!e.visible}},[e._l(e.menuList,(function(t,n){return[t.isShow?a("div",{key:t.btnName,staticClass:"menu-item mini cursor-btn",class:{"no-hover":t.children||"twinkle"==t.type||"opacity"==t.type},on:{click:function(a){return e.fnHandler(t)}}},["opacity"===t.type?a("div",{staticClass:"opacity-box"},[t.show?a("div",{staticClass:"setting-box",on:{click:function(e){e.stopPropagation()}}},[a("span",[e._v(e._s(e.$t("formRelational.opacity.label"))+"：")]),a("el-slider",{staticStyle:{flex:"1"},attrs:{min:0,max:1,step:.01},on:{change:e.opacityChange},model:{value:e.opacityValue,callback:function(t){e.opacityValue=t},expression:"opacityValue"}})],1):e._e()]):e._e(),a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:t.btnName,"hide-after":"3000",placement:"top"}},[t.colorPicker?a("el-color-picker",{attrs:{"show-alpha":"","popper-class":"bottom-menu-color-picker",size:"mini"},on:{change:e.setMarkupColor},model:{value:e.selectedColor,callback:function(t){e.selectedColor=t},expression:"selectedColor"}}):a("span",["twinkle"===t.type?a("div",{staticClass:"twinkle-box"},[t.show?a("div",{staticClass:"setting-box",on:{click:function(e){e.stopPropagation()}}},[a("div",{staticClass:"setting-content"},[a("div",{staticClass:"item"},[e._v(" "+e._s(e.$t("bottomMenu.element.label"))+"： "),a("el-color-picker",{attrs:{"show-alpha":"","popper-class":"bottom-menu-color-picker",size:"mini"},on:{change:e.setTwinkleColor},model:{value:e.twinkleColor,callback:function(t){e.twinkleColor=t},expression:"twinkleColor"}})],1),a("div",{staticClass:"item"},[e._v(" "+e._s(e.$t("bottomMenu.element.label1"))+"： "),a("el-input",{staticClass:"pos-item",attrs:{size:"mini",placeholder:"S"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.setTwinkle.apply(null,arguments)}},model:{value:e.twinkleInterval,callback:function(t){e.twinkleInterval=t},expression:"twinkleInterval"}},[a("template",{slot:"append"},[e._v("S")])],2)],1),a("div",{staticClass:"confirm-btn",on:{click:function(t){return t.stopPropagation(),e.setTwinkle.apply(null,arguments)}}},[e._v(" "+e._s(e.$t("menuIconName.confirm"))+" ")])])]):e._e(),a("CommonSVG",{attrs:{color:"#dddddd","icon-class":t.icon,size:t.iconSize?t.iconSize:16}}),a("span",{staticClass:"arrow"})],1):a("div",[a("CommonSVG",{attrs:{color:"#dddddd","icon-class":t.icon,size:t.iconSize?t.iconSize:16}})],1)])],1),t.children?[a("transition",[a("div",{directives:[{name:"show",rawName:"v-show",value:t.show,expression:"item.show"}],staticClass:"item-slider"},e._l(t.children,(function(t){return a("span",{key:t.btnName,staticClass:"cursor-btn child-menu",on:{click:function(a){return a.stopPropagation(),e.fnHandler(t)}}},[e._v(" "+e._s(t.btnName)+" ")])})),0)])]:e._e()],2):e._e()]})),e.canHandle&&e.isEditMode&&!e.multipleBottomMenu?[a("div",{staticClass:"menu-item cursor-btn mini",on:{click:function(t){return e.handelComponentTransform("rotate")}}},[a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:e.$t("menuIconName.rotate"),placement:"top"}},[a("span",[a("CommonSVG",{attrs:{color:"rotate"===e.elementTransformObj?"var(--theme)":"#dddddd","icon-class":"rotating_feature",size:16}})],1)])],1),a("div",{staticClass:"menu-item cursor-btn mini",on:{click:function(t){return e.handelComponentTransform("translate")}}},[a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:e.$t("formRelational.translate.tooltip"),placement:"top"}},[a("span",[a("CommonSVG",{attrs:{color:"translate"===e.elementTransformObj?"var(--theme)":"#dddddd","icon-class":"crosshair_move_feature",size:16}})],1)])],1),a("div",{staticClass:"menu-item cursor-btn mini",on:{click:function(t){return e.handelComponentTransform("scale")}}},[a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:e.$t("formRelational.scale.label"),placement:"top"}},[a("span",[a("CommonSVG",{attrs:{color:"scale"===e.elementTransformObj?"var(--theme)":"#dddddd","icon-class":"xyz",size:16}})],1)])],1)]:e._e()],2)])],1):e._e(),e.canHandle&&e.isEditMode&&!e.multipleBottomMenu?[a("div",{staticClass:"component-wrap model-wrap"},[e.layoutElementMenu.includes(e.elementMenu)?a("div",{staticClass:"name-btn radar-name",domProps:{innerHTML:e._s(e.layoutMenuName)}}):a("div",{staticClass:"name-btn",staticStyle:{"font-size":"12px"},on:{click:function(t){return e.changeVisible()}}},[a("span",[e._v(e._s(e.sceneManageTreeDatas[e.elementMenu].name))])]),a("transition",{attrs:{name:"sliderFadeFeature",mode:"out-in"}},[a("div",{directives:[{name:"show",rawName:"v-show",value:!e.visible,expression:"!visible"}],staticClass:"wrap-menu-list"},[e._l(e.bottomElementMenuList,(function(t,n){return[a("div",{key:t.btnName,staticClass:"menu-item cursor-btn mini",class:{"no-hover":"topBottomPosition"==t.type},on:{click:function(a){return e.fnHandler(t)}}},[a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:t.btnName,placement:"top"}},[a("span",[t.icon?a("CommonSVG",{attrs:{color:e.elementTransformObj===t.type?"var(--theme)":"#dddddd","icon-class":t.icon,size:16}}):e._e()],1),t.colorPicker?a("el-color-picker",{attrs:{"color-format":"rgb","popper-class":"bottom-menu-color-picker",size:"mini"},on:{change:e.setMarkupColor},model:{value:e.batchExtrudeDatas.selectedColor,callback:function(t){e.$set(e.batchExtrudeDatas,"selectedColor",t)},expression:"batchExtrudeDatas.selectedColor"}}):e._e()],1),"topBottomPosition"==t.type?a("div",{directives:[{name:"show",rawName:"v-show",value:t.show,expression:"item.show"}],staticClass:"dialog-setting-box styleSet-list-div",staticStyle:{width:"350px"},on:{click:function(e){e.stopPropagation()}}},[a("div",{staticClass:"items-center margin-bottom-8"},[a("span",{staticClass:"item-title"},[e._v(e._s(e.$t("featureSetting.style.batchExtrude.label6")))]),a("el-row",{staticClass:"items-center",attrs:{gutter:10}},[a("el-col",{attrs:{span:7}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:e.batchExtrudeDatas.bottomPosition[0],expression:"batchExtrudeDatas.bottomPosition[0]"}],attrs:{size:"mini",type:"number",title:"",placeholder:"X"},on:{input:function(t){return e.handleBatchExtrudePostData()}},scopedSlots:e._u([{key:"prepend",fn:function(){return[a("span",[e._v("X")])]},proxy:!0}],null,!0),model:{value:e.batchExtrudeDatas.bottomPosition[0],callback:function(t){e.$set(e.batchExtrudeDatas.bottomPosition,0,t)},expression:"batchExtrudeDatas.bottomPosition[0]"}})],1),a("el-col",{attrs:{span:7}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:e.batchExtrudeDatas.bottomPosition[1],expression:"batchExtrudeDatas.bottomPosition[1]"}],attrs:{size:"mini",type:"number",title:"",placeholder:"Y"},on:{input:function(t){return e.handleBatchExtrudePostData()}},scopedSlots:e._u([{key:"prepend",fn:function(){return[a("span",[e._v("Y")])]},proxy:!0}],null,!0),model:{value:e.batchExtrudeDatas.bottomPosition[1],callback:function(t){e.$set(e.batchExtrudeDatas.bottomPosition,1,t)},expression:"batchExtrudeDatas.bottomPosition[1]"}})],1),a("el-col",{attrs:{span:7}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:e.batchExtrudeDatas.bottomPosition[2],expression:"batchExtrudeDatas.bottomPosition[2]"}],attrs:{size:"mini",type:"number",title:"",placeholder:"Z"},on:{input:function(t){return e.handleBatchExtrudePostData()}},scopedSlots:e._u([{key:"prepend",fn:function(){return[a("span",[e._v("Z")])]},proxy:!0}],null,!0),model:{value:e.batchExtrudeDatas.bottomPosition[2],callback:function(t){e.$set(e.batchExtrudeDatas.bottomPosition,2,t)},expression:"batchExtrudeDatas.bottomPosition[2]"}})],1),a("el-col",{staticClass:"items-center",staticStyle:{padding:"0"},attrs:{span:3}},[a("el-tooltip",{attrs:{enterable:!1,effect:"dark",placement:"right",content:e.$t("featureSetting.style.batchExtrude.tooltip")}},[a("i",{staticClass:"el-icon-sort cursor-btn",staticStyle:{"font-size":"14px"},on:{click:function(t){return e.copyCoordinates("toBottom")}}})]),a("el-tooltip",{attrs:{enterable:!1,effect:"dark",content:e.$t("formRelational.basePoint.placeholder"),placement:"right"}},[a("span",{staticClass:"cursor-btn margin-left-3",on:{click:function(t){return e.clickCheckedOffsetCoordinate("bottomPosition")}}},[a("CommonSVG",{attrs:{size:"16","icon-class":"select_coord"}})],1)])],1)],1)],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[e._v(e._s(e.$t("featureSetting.style.batchExtrude.label7")))]),a("el-row",{staticClass:"items-center",attrs:{gutter:10}},[a("el-col",{attrs:{span:7}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:e.batchExtrudeDatas.topPosition[0],expression:"batchExtrudeDatas.topPosition[0]"}],attrs:{size:"mini",type:"number",title:"",placeholder:"X"},on:{input:function(t){return e.handleBatchExtrudePostData()}},scopedSlots:e._u([{key:"prepend",fn:function(){return[a("span",[e._v("X")])]},proxy:!0}],null,!0),model:{value:e.batchExtrudeDatas.topPosition[0],callback:function(t){e.$set(e.batchExtrudeDatas.topPosition,0,t)},expression:"batchExtrudeDatas.topPosition[0]"}})],1),a("el-col",{attrs:{span:7}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:e.batchExtrudeDatas.topPosition[1],expression:"batchExtrudeDatas.topPosition[1]"}],attrs:{size:"mini",type:"number",title:"",placeholder:"Y"},on:{input:function(t){return e.handleBatchExtrudePostData()}},scopedSlots:e._u([{key:"prepend",fn:function(){return[a("span",[e._v("Y")])]},proxy:!0}],null,!0),model:{value:e.batchExtrudeDatas.topPosition[1],callback:function(t){e.$set(e.batchExtrudeDatas.topPosition,1,t)},expression:"batchExtrudeDatas.topPosition[1]"}})],1),a("el-col",{attrs:{span:7}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:e.batchExtrudeDatas.topPosition[2],expression:"batchExtrudeDatas.topPosition[2]"}],attrs:{size:"mini",type:"number",title:"",placeholder:"Z"},on:{input:function(t){return e.handleBatchExtrudePostData()}},scopedSlots:e._u([{key:"prepend",fn:function(){return[a("span",[e._v("Z")])]},proxy:!0}],null,!0),model:{value:e.batchExtrudeDatas.topPosition[2],callback:function(t){e.$set(e.batchExtrudeDatas.topPosition,2,t)},expression:"batchExtrudeDatas.topPosition[2]"}})],1),a("el-col",{staticClass:"items-center",staticStyle:{padding:"0"},attrs:{span:3}},[a("el-tooltip",{attrs:{enterable:!1,effect:"dark",placement:"right",content:e.$t("featureSetting.style.batchExtrude.tooltip")}},[a("i",{staticClass:"el-icon-sort cursor-btn",staticStyle:{"font-size":"14px"},on:{click:function(t){return e.copyCoordinates("toTop")}}})]),a("el-tooltip",{attrs:{enterable:!1,effect:"dark",content:e.$t("formRelational.basePoint.placeholder"),placement:"right"}},[a("span",{staticClass:"cursor-btn margin-left-3",on:{click:function(t){return e.clickCheckedOffsetCoordinate("topPosition")}}},[a("CommonSVG",{attrs:{size:"16","icon-class":"select_coord"}})],1)])],1)],1)],1)]):e._e()],1)]})),a("div",{staticClass:"menu-item cursor-btn mini",on:{click:function(t){return e.setUp()}}},[a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:e.$t("menuIconName.setUp"),placement:"top"}},[a("span",[a("CommonSVG",{attrs:{"icon-class":"set_up_feature",size:20,color:"#dddddd"}})],1)])],1),a("div",{staticClass:"menu-item cursor-btn mini",on:{click:function(t){return e.deleteElement()}}},[a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:e.$t("menuIconName.remove"),placement:"top"}},[a("span",[a("CommonSVG",{attrs:{"icon-class":"delete",size:16,color:"#dddddd"}})],1)])],1)],2)])],1),e.elementTransformObj?[a("div",{staticClass:"vertical-split-line"}),a("div",{staticClass:"menu-item cursor-btn",staticStyle:{"font-size":"18px"},on:{click:function(t){return e.toggleMenu({label:"quit"})}}},[a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:e.$t("menuIconName.exit"),placement:"top"}},[a("span",[a("CommonSVG",{attrs:{color:"#dddddd","icon-class":"quit",size:16}})],1)])],1),a("div",{staticClass:"menu-item cursor-btn",staticStyle:{"font-size":"18px"},on:{click:function(t){return e.toggleMenu({label:"save"})}}},[a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:e.$t("menuIconName.save"),placement:"top"}},[a("span",[a("CommonSVG",{attrs:{color:"#dddddd","icon-class":"save_feature",size:16}})],1)])],1)]:e._e()]:e._e(),e.bottomModelMenuList.elementExtend.length>0?a("div",{staticClass:"component-wrap"},["model"==e.elementMenu||e.isEditMode?a("div",{staticClass:"vertical-split-line"}):e._e(),e._l(e.bottomModelMenuList.elementExtend,(function(t){return a("div",{key:t.btnName,staticClass:"menu-item cursor-btn",on:{click:function(a){return e.fnHandler(t)}}},[a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:t.btnName,placement:"top"}},[a("span",[t.iconType&&"custom"==t.iconType?a("img",{staticStyle:{"vertical-align":"middle"},attrs:{src:t.icon,width:"100%",height:"100%",alt:t.btnName}}):a("CommonSVG",{attrs:{color:"#dddddd","icon-class":t.icon,size:16}})],1)])],1)}))],2):e._e(),e.positionVisible?a("dialogComp",{attrs:{hideHeader:!1,needClose:!0,backgroundColor:"var(--primary-bg-color)",zIndex:"5",draw:!0,left:600,drag:!0,title:e.$t("bottomMenu.element.label2"),icon:"icon-details",width:273,height:280,type:"detailInfo",position:"fixed",bottom:70},on:{close:e.closeDialog},scopedSlots:e._u([{key:"center",fn:function(){return[a("div",{staticClass:"content"},[a("div",{staticClass:"content-item"},[a("div",{staticClass:"label"},[e._v(" "+e._s(e.$t("bottomMenu.element.label3"))+"： ")]),a("div",{staticClass:"btn-wrap"},[a("div",{staticClass:"coord-icon",class:e.isQueryPositionEnabled?"active":"",on:{click:function(t){return t.stopPropagation(),e.toggleQueryPosition.apply(null,arguments)}}},[a("CommonSVG",{style:e.styleForChoosePoint,attrs:{"icon-class":"select_coord",size:14}}),a("span",[e._v(e._s(e.$t("bottomMenu.element.label4")))])],1)])]),a("div",{staticClass:"content-item basepoint"},[a("div",{staticClass:"label"},[e._v(" "+e._s(e.$t("formRelational.basePoint.label1"))+"： ")]),a("div",{staticClass:"xyz"},[a("el-input",{staticClass:"pos-item",attrs:{size:"mini",placeholder:"X"},model:{value:e.basePoint[0],callback:function(t){e.$set(e.basePoint,0,t)},expression:"basePoint[0]"}},[a("template",{slot:"append"},[e._v("X")])],2),a("el-input",{staticClass:"pos-item",attrs:{size:"mini",placeholder:"Y"},model:{value:e.basePoint[1],callback:function(t){e.$set(e.basePoint,1,t)},expression:"basePoint[1]"}},[a("template",{slot:"append"},[e._v("Y")])],2),a("el-input",{staticClass:"pos-item",attrs:{size:"mini",placeholder:"Z"},model:{value:e.basePoint[2],callback:function(t){e.$set(e.basePoint,2,t)},expression:"basePoint[2]"}},[a("template",{slot:"append"},[e._v("Z")])],2)],1)]),a("div",{staticClass:"footer"},[a("div",{staticClass:"footer-item",on:{click:function(t){return t.stopPropagation(),e.onAnimationBasePointRestore.apply(null,arguments)}}},[e._v(" "+e._s(e.$t("menuIconName.reset1"))+" ")]),a("div",{staticClass:"footer-item",on:{click:function(t){return t.stopPropagation(),e.onAnimationBasePointOk.apply(null,arguments)}}},[e._v(" "+e._s(e.$t("menuIconName.confirm"))+" ")])])])]},proxy:!0}],null,!1,3440140031)}):e._e()],2)},i=[],o=a("b85c"),s=a("3835"),r=(a("d3b7"),a("3ca3"),a("ddb0"),a("4de4"),a("e9c4"),a("caad"),a("ac1f"),a("5319"),a("1276"),a("99af"),a("c740"),a("4d63"),a("c607"),a("2c3e"),a("25f0"),a("466d"),a("a434"),a("cb29"),a("2532"),a("b0c0"),a("159b"),a("0541"),a("a630"),a("07ac"),a("7db0"),a("009f")),l=a("5de3"),c={name:"ElementMenu",components:{CommonSVG:function(){return a.e("chunk-51f90655").then(a.bind(null,"17d0"))}},mixins:[l["a"]],data:function(){return{menuList:[],modelList:[],opacityValue:1,selectedColor:"rgb(255, 50, 0)",elementTransformObj:"",modelId:"",sceneManageTreeDatas:r["a"],visible:!1,rawData:{},isModel:!1,_componentData:{},canHandle:!0,isQueryPositionEnabled:!1,positionVisible:!1,basePoint:[0,0,0],rawBpt:[0,0,0],targetId:"",elementTransformType:"",twinkleColor:"rgb(30, 255, 255)",twinkleInterval:.5,curElementData:{},multipleBottomMenu:!1,hideModelMenu:!0,layoutElementMenu:["radar","polygon","vectorextrude","batch-extrude"],layoutMenuName:"",batchExtrudeDatas:{selectedColor:"rgb(255, 50, 0)",topPosition:[0,0,0],bottomPosition:[0,0,0],which:"bottomPosition",other:"topPosition"}}},computed:{styleForChoosePoint:function(){return this.isQueryPositionEnabled?{color:"#2680FE",cursor:"pointer"}:{color:"unset",cursor:"pointer"}},isEditMode:function(){return this.$store.state.scene.sceneEditMode},settingShow:function(){return this.$store.state.widget.settingActive},elementMenu:function(){return this.$store.state.menuList.elementMenu},bottomModelMenuList:function(){var e=this.$store.state.menuList.bottomModelMenuList;return e.elementExtend=e.elementExtend,e},bottomElementMenuList:function(){var e=[];return e="gltf"==this.elementMenu||"fbx"==this.elementMenu?this.bottomModelMenuList.gltf:this.bottomModelMenuList[this.elementMenu],e||(e=this.bottomModelMenuList["smoke"]),e}},watch:{elementMenu:function(e){""!=this.elementTransformObj&&this.offModelTransformChange(),this.visible="model"===e},"curElementData.id":function(e){this.modelId=e},"$store.state.menuList.bottomMenuActive":{handler:function(e){this.setMultipleMenuState(e)},immediate:!0},bottomMenuChildActive:function(){return this.$store.state.menuList.bottomMenuChildActive}},created:function(){this.modelId=this.curElementData.id,this.setMenu()},methods:{layoutElementMenuName:function(){switch(this.layoutMenuName="",this.elementMenu){case"radar":this.layoutMenuName=this.$t("featureDatas.radar.menuName");break;case"polygon":var e=window.scene.features.get(this.curElementData.id);"extrude"==e.data.subType&&(this.layoutMenuName=this.$t("featureDatas.polygon.extend.extrude.menuName"));break;case"vectorextrude":this.layoutMenuName=this.$t("featureDatas.vectorextrude.menuName");break;case"batch-extrude":this.layoutMenuName=this.$t("featureDatas['batch-extrude'].menuName");break}},setFeatureObj:function(){var e=window.scene.getSelection().filter((function(e){return"element"==e.type}));if("model"===this.elementMenu){var t=this.$store.state.menuList.elementData,a=e.length>0?JSON.parse(JSON.parse(JSON.stringify(e[0].model))):JSON.parse(JSON.stringify(t));this.curElementData=a,this.setElementMenu()}else this.curElementData=JSON.parse(JSON.parse(JSON.stringify(window.scene.selectedFeature)));var n=window.scene.features.get(this.curElementData.id);switch(n&&n.data&&(this.curElementData.data=n.data),this.elementMenu){case"gltf":case"fbx":this.curElementData.scale=n.scale;break}this.$store.commit("saveFeatureRawData",JSON.stringify(this.curElementData))},setElementMenu:function(){var e=window.scene.selectedObjects.size;this.menuList=[{btnName:this.$t("bottomMenu.element.attribute"),icon:"attribute_feature",type:"attribute",fnHandler:"openAttribute",id:5,isShow:!1},{id:4,fnHandler:"zoomCurrentElement",btnName:this.$t("menuIconName.zoom"),icon:"focusing_feature",type:"zoom",isShow:!0},{fnHandler:"",btnName:this.$t("bottomMenu.element.select.name"),show:!1,icon:"select_feature",type:"select",id:0,isShow:!1,children:1==e?[{fnHandler:"selectedSameLevel",btnName:this.$t("bottomMenu.element.select.extend.label")},{fnHandler:"selectedSameType",btnName:this.$t("bottomMenu.element.select.extend.label1")},{fnHandler:"selectedSameFamily",btnName:this.$t("bottomMenu.element.select.extend.label2")},{fnHandler:"selectedSameCategory",btnName:this.$t("bottomMenu.element.select.extend.label3")},{fnHandler:"selectedReverseElection",btnName:this.$t("bottomMenu.element.select.extend.label4")}]:[{fnHandler:"selectedReverseElection",btnName:this.$t("bottomMenu.element.select.extend.label4")}]},{id:1,fnHandler:"hideCurrent",btnName:this.$t("menuIconName.hide"),icon:"hidden_cut",type:"hidden",isShow:!0},{id:2,fnHandler:"isolateCurrent",btnName:this.$t("menuIconName.isolate"),icon:"isolate_feature",type:"isolate",isShow:!0},{id:3,fnHandler:"setElementColor",btnName:this.$t("menuIconName.color"),colorPicker:!0,type:"color",isShow:!0},{id:9,fnHandler:"setElementOpacity",btnName:this.$t("formRelational.opacity.label"),type:"opacity",icon:"transparent_feature",show:!1,isShow:!0},{id:7,fnHandler:"twinkleElement",btnName:this.$t("menuIconName.twinkle"),icon:"twinkle_feature",type:"twinkle",show:!1,isShow:!0},{id:"",fnHandler:"allParents",btnName:this.$t("dialog.hierarchies.name"),icon:"tree_structure_feature",type:"parent",show:!1,isShow:!0},{id:8,fnHandler:"resetElement",btnName:this.$t("menuIconName.reset"),icon:"tb_remove_active",type:"reset",iconSize:17,isShow:!0}]},setMultipleMenuState:function(e){var t=["sectioning","sectionPlaneing"];this.multipleBottomMenu="analysis"===e?"roller"===this.bottomMenuChildActive:t.includes(e),this.multipleBottomMenu&&(this.visible=!0)},hex2Rgba:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;if(!e)return"rgba(255, 50, 0, ".concat(t,")");var a=e.replace(/[(|)|rgba]/g,""),n=a.split(","),i=Object(s["a"])(n,3),o=i[0],r=i[1],l=i[2];return"rgba(".concat(o,",").concat(r,",").concat(l,", ").concat(t,")")},onAnimationBasePointRestore:function(){this.basePoint=this.rawBpt},onAnimationBasePointOk:function(){if(""!=this.targetId){var e=window.scene.findObject(this.targetId);e.bpt=this.basePoint}this.closeDialog(),this.handleModelTransform(this.elementTransformType)},openCoord:function(){if(this.positionVisible=!0,""!=this.targetId){var e=window.scene.findObject(this.targetId);this.basePoint=[0,0,0],this.rawBpt=[0,0,0],e.bpt&&(this.basePoint=JSON.parse(JSON.stringify(e.bpt)),this.rawBpt=JSON.parse(JSON.stringify(e.bpt))),window.scene.mv.status.selectable=!1}},closeDialog:function(){this.positionVisible=!1,window.scene.mv.status.selectable=!0,this.enableOrDisableQueryPosition(!1)},toggleQueryPosition:function(){this.enableOrDisableQueryPosition(!this.isQueryPositionEnabled)},enableOrDisableQueryPosition:function(e){var t=window.scene.mv,a=t.events;e?(a.mouseup.on("default",this.setCurrentAnimationBasePoint),this.isQueryPositionEnabled=!0,this.$message.warning(this.$t("messageTips.checkCoordinate"))):(a.mouseup.off("default",this.setCurrentAnimationBasePoint),this.isQueryPositionEnabled=!1)},setCurrentAnimationBasePoint:function(e){var t=window.scene,a=t.queryPosition(new t.mv._THREE.Vector2(e.clientX,e.clientY));if(a){if(this.basePoint=[a.x,a.y,a.z],""==this.targetId)return;var n=window.scene.findObject(this.targetId);n.bpt=this.basePoint,this.enableOrDisableQueryPosition(!1)}},setMenu:function(e){var t=window.scene.getSelection(),a=0;if(this.setFeatureObj(),this.layoutElementMenuName(),"model"===this.elementMenu){var n=["element","space","voxel"],i=t.filter((function(e){return n.includes(e.type)}));if(i.length<=0?(this.visible=!1,this.hideModelMenu=!1):(this.hideModelMenu=!0,this.visible="model"===this.curElementData.type),t.length&&(this.selectedColor=this.hex2Rgba(t[0].color,t[0].opacity)),a=e||i.length,a>1?(this.menuList[0].isShow=!1,this.menuList[2].isShow=!1,this.menuList[8].isShow=!1,this.canHandle=!1):1==a&&("element"!=t[0].type&&"voxel"!=t[0].type||(this.menuList[0].isShow=!0,this.menuList[2].isShow=!0,this.canHandle=!0,this.menuList[8].isShow=void 0!=t[0].parentObject),"space"==t[0].type&&(this.menuList[0].isShow=!0,this.canHandle=!0)),"timeSeries"==this.settingShow){var o=this.$store.state.scene.dragOverData,s=t.findIndex((function(e){return e.model&&e.model.id==o.id}));s>=0&&(this.menuList[5].isShow=!1,this.menuList[6].isShow=!1,this.menuList[7].isShow=!1)}var r=this.GetQueryString("isBime");r&&(this.menuList[0].isShow=!1)}else if("batch-extrude"===this.elementMenu){var l=t.filter((function(e){return"extrude"===e.type}));this.bottomElementMenuList[1].isShow=a<=1,this.bottomElementMenuList[2].isShow=a<=1,this.$set(this.bottomElementMenuList[2],"show",!1),this.batchExtrudeDatas.selectedColor=l[0].originColor,this.initBatchExtrudePosition()}else this.visible="model"===this.curElementData.type},GetQueryString:function(e){var t=new RegExp("(^|&)"+e+"=([^&]*)(&|$)"),a=window.location.hash.substr(1).match(t);return null!=a?unescape(a[2]):null},handelComponentTransform:function(e){if(""==this.targetId){var t=window.scene.getSelection();this.targetId=t[0].id}if(this.elementTransformObj!==e){var a=window.scene.findObject(this.targetId)||null,n=a||window.scene.getSelection()[0];this._componentData={id:n.id,offset:n.offset,scale:n.scale,rotation:n.rotation,position:n.rotation},this.isModel=!0,"rotate"===e||"scale"===e?(this.openCoord(),this.elementTransformType=e):(this.closeDialog(),this.handleModelTransform(e))}else this.offModelTransformChange()},setUp:function(){var e=this;""!=this.elementTransformObj&&this.offModelTransformChange();var t=this.curElementData,a=window.scene.features.get(t.id);if(""!=this.$store.state.widget.settingActive){var n=this.$store.state.scene.dragOverData;if(n.staticType){try{window.scene.removeFeature(n.id,!0)}catch(d){console.warn(d)}var i=this.computedTreeDatas[n.type].datas,o=i.findIndex((function(e){return e.id==n.id}));i.splice(o,1)}else this.handelSceneStyleReset(n)}"polygon"==t.type&&(a.data.tileExcavation?(t.title=this.$t("featureDatas.polygon.extend.tileExcavation.name"),t.subType="tileExcavation"):a.data.extrude?(t.title=this.$t("featureDatas.polygon.extend.extrude.name"),t.subType="extrude"):a.data.terrainFlatten?(t.title=this.$t("featureDatas.polygon.extend.flatten.name"),t.subType="flatten"):a.data.terrainExcavation?(t.title=this.$t("featureDatas.polygon.extend.excavation.name"),t.subType="excavation"):a.data.tileFlatten?(t.title=this.$t("featureDatas.polygon.extend.tileFlatten.name"),t.subType="tileFlatten"):a.data.demOpacity?(t.title=this.$t("featureDatas.polygon.extend.demOpacity.name"),t.subType="demOpacity"):a.data.water?(t.title=this.$t("featureDatas.polygon.extend.water.name"),t.subType="water"):a.data.fill&&a.data.environment?(t.title=this.$t("featureDatas.polygon.extend.environment.name"),t.subType="environment"):(t.title=this.$t("featureDatas.polygon.extend.surface.name"),t.subType="surface"));var s=t,r=JSON.parse(JSON.parse(JSON.stringify(a)));switch(Object.assign(s,r),void 0!=a.data&&(s.data=a.data),t.type){case"gltf":case"fbx":s.scale=a.scale;break}if(this.$store.commit("saveFeatureRawData",JSON.stringify(s)),"model"==t.type){t.currentVersion=a.currentVersion;var l=JSON.parse(JSON.parse(JSON.stringify(a)));Object.assign(t,l);var c=this.$store.state.dialog.activeDialog;c.includes("modelTree")&&this.$store.commit("toggleActiveDialog","modelTree"),c.includes("viewTree")&&this.$store.commit("toggleActiveDialog","viewTree")}this.$store.commit("saveDragOverData",t),this.$nextTick((function(){"underlay"==t.type||"skybox"==t.type?(e.$store.commit("toggleActiveDialog","Source"),e.$store.commit("toggleSettingActive","closeSetting")):e.$store.commit("toggleSettingActive",t.type)})),this.$store.commit("toogleElementMenu","")},deleteElement:function(){var e=this;""!=this.elementTransformObj&&this.offModelTransformChange();var t=this.curElementData,a=window.scene.features.get(t.id);this.$confirm(this.$t("messageTips.removeFeature",{name:a.name}),this.$t("messageTips.tipsTitle"),{confirmButtonText:this.$t("menuIconName.confirm"),cancelButtonText:this.$t("messageTips.cancel"),type:"warning"}).then((function(){try{a.dispose()}catch(t){console.warn(t)}window.scene.render(),e.$deepUpdateScene(a.type),e.$store.commit("toogleElementMenu","")}))},changeVisible:function(){this.isModel=!1,"model"===this.curElementData.type&&(this.visible=!this.visible,""!=this.elementTransformObj&&this.offModelTransformChange())},setMarkupColor:function(e){"model"==this.curElementData.type&&(this.selectedColor=e,this.handleElementColor()),"batch-extrude"==this.curElementData.type&&this.handleBatchExtrudePostData(),window.scene.clearSelection(),this.$store.commit("toogleElementMenu","")},fnHandler:function(e){if("extend"==e.type)return this.emitElementMenuExtendDatas(JSON.parse(JSON.stringify(e))),!1;if("model"==this.curElementData.type){if(this.menuList.forEach((function(t){e.type!==t.type&&t.show&&(t.show=!1)})),"twinkle"===e.type){if(this.menuList[7].show=!this.menuList[7].show,this.menuList[7].show){var t=window.scene.getSelection()[0];t.twinkleColor&&(this.twinkleColor=t.twinkleColor||"rgb(30, 255, 255)",this.twinkleInterval=t.interval?parseFloat(t.interval/1e3):.5)}return!1}if("opacity"===e.type){if(this.menuList[6].show=!this.menuList[6].show,this.menuList[6].show){var a=window.scene.getSelection()[0];this.opacityValue=void 0===a.opacity?1:1*a.opacity}return!1}this.menuList[8].show=!1}if(Object.hasOwn(e,"show")&&(e.show=!e.show),e.fnHandler){var n=e.fnHandler;this.$options.methods[n].call(this,e.type?e.type:null)}},zoomCurrentElement:function(){var e=window.scene.selectedObjects;if(e.size>0){var t={objectIDs:Array.from(e.keys())};window.scene.execute("fit",t),this.$store.commit("updateMenuExecuteState",{random:(new Date).getTime()})}},setTwinkleColor:function(){},opacityChange:function(e){var t=window.scene.selectedObjects,a=window.scene.getSelection();if(t.size>0){var n={objectIDs:Array.from(t.keys()),opacity:e,color:a[0].color||""};this.opacityValue=e,this.selectedColor=this.hex2Rgba(a[0].color,e),window.scene.execute("color",n)}this.$store.commit("updateMenuExecuteState",{random:(new Date).getTime()}),window.scene.clearSelection(),window.scene.render(),this.$store.commit("toogleElementMenu","")},setTwinkle:function(){var e=this,t=window.scene.selectedObjects;if(t.size>0){var a=[];if(t.forEach((function(e){e.twinkled&&a.push(e.id)})),a.length>0){var n={objectIDs:a,interval:-1,color:this.twinkleColor};window.scene.execute("twinkle",n)}setTimeout((function(){var a=Array.from(t.keys()),n=n=e.twinkleInterval?parseFloat(1e3*e.twinkleInterval):500,i={objectIDs:a,interval:n,color:e.twinkleColor};window.scene.execute("twinkle",i),e.$store.commit("updateMenuExecuteState",{random:(new Date).getTime()}),window.scene.clearSelection(),window.scene.render(),e.$store.commit("toogleElementMenu","")}))}},resetElement:function(){var e=window.scene.selectedObjects;if(e.size>0){var t={objectIDs:Array.from(e.keys())};window.scene.execute("reset",t),this.$store.commit("updateMenuExecuteState",{random:(new Date).getTime()}),window.scene.clearSelection(),window.scene.render(),this.$store.state.dialog.activeDialog.includes("statefulElements")&&this.$bus.emit("onSetStatefulElementsDatas"),this.$store.commit("toogleElementMenu","")}},setElementColor:function(){},handleElementColor:function(){var e=window.scene.selectedObjects;if(e.size>0){if(null==this.selectedColor||""==this.selectedColor)e.forEach((function(e){e.resetColor()}));else{var t=this.selectedColor.match(/\d+(.\d+)?/g),a="rgb(".concat(t[0],",").concat(t[1],",").concat(t[2],")"),n={objectIDs:Array.from(e.keys()),color:a,opacity:t[3]};this.opacityValue=t[3],window.scene.execute("color",n)}this.$store.commit("updateMenuExecuteState",{random:(new Date).getTime()}),this.$store.state.dialog.activeDialog.includes("statefulElements")&&this.$bus.emit("onSetStatefulElementsDatas"),window.scene.render()}},isolateCurrent:function(){var e=window.scene.selectedObjects,t={objectIDs:Array.from(e.keys())};window.scene.execute("isolate",t),this.$store.commit("updateMenuExecuteState",{random:(new Date).getTime()}),window.scene.render()},hideCurrent:function(){var e=window.scene.selectedObjects;if(e.size>0){var t={objectIDs:Array.from(e.keys())};window.scene.execute("hide",t),this.$store.commit("updateMenuExecuteState",{random:(new Date).getTime()}),window.scene.clearSelection(),window.scene.render(),this.$store.state.dialog.activeDialog.includes("statefulElements")&&this.$bus.emit("onSetStatefulElementsDatas"),this.$store.commit("toogleElementMenu",""),this.$store.commit("selectElementData",{})}},selectedCurrent:function(e){this.menuList[2].show=!this.menuList[2].show},selectedSameLevel:function(){var e=window.scene.getSelection()[0];e&&(window.scene.allObjects.forEach((function(t){-1!==t.levelID.indexOf(e.levelID)&&(t.selected=!0)})),window.scene.render(),this.setMenu())},selectedSameType:function(){var e=window.scene.getSelection()[0];e&&(window.scene.allObjects.forEach((function(t){t.typeID==e.typeID&&(t.selected=!0)})),window.scene.render(),this.setMenu())},selectedSameFamily:function(){var e=window.scene.getSelection()[0];e&&(window.scene.allObjects.forEach((function(t){t.familyID==e.familyID&&(t.selected=!0)})),window.scene.render(),this.setMenu())},selectedSameCategory:function(){var e=window.scene.getSelection()[0];e&&(window.scene.allObjects.forEach((function(t){t.categoryID==e.categoryID&&(t.selected=!0)})),window.scene.render(),this.setMenu())},selectedReverseElection:function(){var e=window.scene.selectedObjects,t=window.scene.getInverseSelection();e.forEach((function(e){e.selected=!1})),t.forEach((function(e){e.selected=!0})),window.scene.render(),this.setMenu()},toggleMenu:function(e,t){var a=e.id;switch(e.hasOwnProperty("show")&&this.$set(this.menuList[a],"show",!this.menuList[a].show),this.targetId="",e.label){case"save":var n=window.scene.features.get(this.curElementData.id);""!=this.elementTransformObj&&this.offModelTransformChange(),t||window.scene.clearSelection(),n&&n.clearAABB(),this.$store.commit("toogleElementMenu",""),this.$store.commit("selectElementData",{}),window.scene.render();break;case"quit":var i=this.$store.state.menuList.elementData,o=window.scene.features.get(this.curElementData.id);if(this.visible&&(this._componentData||{}).id){var s=this._componentData,r=window.scene.findObject(s.id);r.offset=s.offset,r.rotation=s.rotation,r.scale=s.scale}""!=this.elementTransformObj&&this.offModelTransformChange(),o&&o.clearAABB(),this.handelSceneStyleReset(i),this.$store.commit("toogleElementMenu",""),this.$store.commit("selectElementData",{}),this._componentData={};break}},clickCheckedCoordinate:function(e){""!=this.elementTransformObj&&"coordinate"!=this.elementTransformObj&&this.offModelTransformChange(),this.elementTransformObj!=e?(window.scene.mv.status.selectable=!1,this.$message.success(this.$t("messageTips.checkCoordinate")),this.elementTransformObj="coordinate",window.scene.mv.events.singleSelection.on("default",this.onCheckedCoordinate)):this.offModelTransformChange("coordinate")},offModelTransformChange:function(e){"coordinate"===e?window.scene.mv.events.singleSelection.off("default",this.onCheckedCoordinate):(window.scene.mv.tools.transform.deactive(),window.scene.mv.events.transformChange.off("default",this.onModelTransformChange),window.scene.forceRepaint=!1),this.elementTransformObj="",this.enableOrDisableQueryPosition(!1),window.scene.mv.status.selectable=!0,this.closeDialog(),setTimeout((function(){window.scene.render()}),500)},onCheckedCoordinate:function(e){var t=[0,0,0],a=window.scene.mv._THREE,n=window.scene.queryPosition(new a.Vector2(e.clientX,e.clientY)),i=window.scene.features.get(this.modelId);""!=n&&void 0!=n?(t=window.scene.mv.tools.coordinate.vector2mercator(n),i.origin=[t[0],t[1]],i.altitude=t[2]):this.$message.error(this.$t("messageTips.errorCheckCoordinate")),window.scene.mv.status.selectable=!1,this.offCheckedCoordinate()},offCheckedCoordinate:function(){window.scene.mv.status.selectable=!0,this.elementTransformObj="",window.scene.mv.events.singleSelection.off("default",this.onCheckedCoordinate)},handleModelTransform:function(e){if("coordinate"==this.elementTransformObj&&this.offCheckedCoordinate(),window.scene.mv.tools.transform.currentMode=e,""==this.elementTransformObj){var t=null;if(this.isModel){var a=null;this.targetId?a=window.scene.findObject(this.targetId):(a=window.scene.getSelection()[0],this.targetId=a.id),t=a}else t=window.scene.features.get(this.modelId);window.scene.mv.status.selectable=!1,window.scene.mv.tools.transform.active(t),window.scene.mv.events.transformChange.on("default",this.onModelTransformChange)}this.elementTransformObj!=e?(this.elementTransformObj=e,window.scene.render()):this.offModelTransformChange()},onModelTransformChange:function(e,t,a){window.scene.features.get(this.modelId)},openAttribute:function(){this.$store.commit("toggleActiveDialog","attribute")},emitElementMenuExtendDatas:function(e){window.dispatchEvent(new CustomEvent("onExtendElementMenuClick",{detail:e}))},allParents:function(){this.$store.commit("toggleActiveDialog","hierarchies")},handleBatchExtrudePostData:function(){var e=window.scene.getSelection()[0],t=window.scene.features.get(e.parent.id),a=JSON.parse(JSON.stringify(t.data)),n=Object.values(a.datas[0].bottomPosition),i=window.scene.mv.tools.coordinate.vector2mercator(n);i.length-=1,t.origin=i;var s,r=Object(o["a"])(a.datas);try{for(r.s();!(s=r.n()).done;){var l=s.value;if(l.id==e.id){l.color=this.batchExtrudeDatas.selectedColor,l.bottomPosition.x=parseFloat(this.batchExtrudeDatas.bottomPosition[0])||0,l.bottomPosition.y=parseFloat(this.batchExtrudeDatas.bottomPosition[1])||0,l.bottomPosition.z=parseFloat(this.batchExtrudeDatas.bottomPosition[2])||0,l.topPosition.x=parseFloat(this.batchExtrudeDatas.topPosition[0])||0,l.topPosition.y=parseFloat(this.batchExtrudeDatas.topPosition[1])||0,l.topPosition.z=parseFloat(this.batchExtrudeDatas.topPosition[2])||0;break}}}catch(c){r.e(c)}finally{r.f()}setTimeout((function(){window.scene.postData(a,t.dataKey),window.scene.render()}),500)},initBatchExtrudePosition:function(){var e=window.scene.getSelection()[0],t=window.scene.features.get(e.parent.id),a=JSON.parse(JSON.stringify(t.data)),n=a.datas.find((function(t){return t.id==e.id}));this.batchExtrudeDatas.bottomPosition[0]=n.bottomPosition.x,this.batchExtrudeDatas.bottomPosition[1]=n.bottomPosition.y,this.batchExtrudeDatas.bottomPosition[2]=n.bottomPosition.z,this.batchExtrudeDatas.topPosition[0]=n.topPosition.x,this.batchExtrudeDatas.topPosition[1]=n.topPosition.y,this.batchExtrudeDatas.topPosition[2]=n.topPosition.z},copyCoordinates:function(e){"toTop"==e?(this.batchExtrudeDatas.topPosition.splice(0,1,this.batchExtrudeDatas.bottomPosition[0]),this.batchExtrudeDatas.topPosition.splice(1,1,this.batchExtrudeDatas.bottomPosition[1])):(this.batchExtrudeDatas.bottomPosition.splice(0,1,this.batchExtrudeDatas.topPosition[0]),this.batchExtrudeDatas.bottomPosition.splice(1,1,this.batchExtrudeDatas.topPosition[1])),this.handleBatchExtrudePostData()},clickCheckedOffsetCoordinate:function(e){this.batchExtrudeDatas.which=e,this.batchExtrudeDatas.other="bottomPosition"===e?"topPosition":"bottomPosition",window.scene.mv.status.selectable=!1,this.$message.success({message:this.$t("messageTips.checkCoordinate"),duration:2e3}),window.scene.mv.events.singleSelection.on("default",this.onCheckedOffsetCoordinate)},onCheckedOffsetCoordinate:function(e){var t=window.scene.mv._THREE,a=window.scene.queryPosition(new t.Vector2(e.clientX,e.clientY));if(""!=a&&void 0!=a){var n=this.batchExtrudeDatas.which,i=this.batchExtrudeDatas.other;if(this.$set(this.batchExtrudeDatas[n],0,a.x),this.$set(this.batchExtrudeDatas[n],1,a.y),this.$set(this.batchExtrudeDatas[n],2,a.z),"bottomPosition"==n){var o=parseFloat(this.batchExtrudeDatas["topPosition"][2]);(0==o||o<a.z)&&this.$set(this.batchExtrudeDatas["topPosition"],2,a.z+10)}0==this.batchExtrudeDatas[i][0]&&0==this.batchExtrudeDatas[i][1]&&(this.$set(this.batchExtrudeDatas[i],0,a.x),this.$set(this.batchExtrudeDatas[i],1,a.y)),this.handleBatchExtrudePostData()}else this.$message.error(this.$t("messageTips.errorCheckCoordinate"));window.scene.mv.status.selectable=!0,window.scene.mv.events.singleSelection.off("default",this.onCheckedOffsetCoordinate)}},beforeDestroy:function(){""!=this.elementTransformObj&&this.offModelTransformChange(this.elementTransformObj),window.scene.mv.status.selectable=!0}},d=c,m=(a("44aa"),a("ea82"),a("2877")),u=Object(m["a"])(d,n,i,!1,null,"51077cc4",null);t["default"]=u.exports},cb29:function(e,t,a){var n=a("23e7"),i=a("81d5"),o=a("44d2");n({target:"Array",proto:!0},{fill:i}),o("fill")},ea82:function(e,t,a){"use strict";a("6230")}}]);