(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-51f90655"],{"17d0":function(e,t,n){"use strict";n.r(t);var s=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("svg",{class:e.svgClass,style:{fontSize:e.svgSize,color:e.color}},[n("use",{attrs:{"xlink:href":e.iconName}})])},c=[],o=(n("a9e3"),{name:"CommonSVG",props:{iconClass:{type:String,required:!0},className:{type:String,default:""},color:{type:String,default:"#98A2B3"},size:{type:[String,Number],default:"23"}},computed:{svgSize:function(){var e=document.documentElement.style.fontSize,t="".concat(parseFloat(this.size)/parseFloat(e),"rem");return t},iconName:function(){return"#icon-".concat(this.iconClass)},svgClass:function(){return this.className?"svg-icon ".concat(this.className):"svg-icon"}}}),a=o,i=(n("8fd1"),n("2877")),r=Object(i["a"])(a,s,c,!1,null,"f76b8e7e",null);t["default"]=r.exports},"3af1":function(e,t,n){},"8fd1":function(e,t,n){"use strict";n("3af1")}}]);