(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-183df35e"],{3382:function(e,t,n){},8105:function(e,t,n){"use strict";n.r(t);var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("dialogComp",{ref:"ObjectSettingsRef",attrs:{drag:!0,draw:!1,hideHeader:!1,needClose:"true",zIndex:"100",title:e.$t("sceneMainMenu.renderSetting.name"),icon:"icon-details",left:e.dialogLeft,top:"165",dragTopOffset:e.dragTopOffset,width:e.dialogWidth,height:"auto",titleTip:e.$t("dialog.renderSetting.tooltip"),type:"detailInfo"},on:{close:e.closeDialog},scopedSlots:e._u([{key:"center",fn:function(){return[n("div",{staticClass:"renderSetting-container"},[n("div",{staticClass:"margin-bottom-10",staticStyle:{color:"var(--text-white)"}},[e._v(" "+e._s(e.$t("dialog.renderSetting.label"))+" ")]),n("div",{staticClass:"space-between-center margin-bottom-15"},e._l(e.modeList,(function(t){return n("el-radio",{key:t.icon,staticStyle:{color:"var(--primary-text-color)"},attrs:{disabled:t.disabled,label:t.value},on:{input:function(t){return e.changeRenderSet("modeRadio")}},model:{value:e.modeRadio,callback:function(t){e.modeRadio=t},expression:"modeRadio"}},[n("CommonSVG",{attrs:{"icon-class":t.icon,size:"17"}}),e._v(" "+e._s(t.title)+" ")],1)})),1),n("div",{staticClass:"line-x margin-bottom-15"}),1==e.modeRadio?n("div",{staticClass:"space-between-center margin-bottom-15"},[n("span",[e._v(e._s(e.$t("topToolBarMenu.advanced.children.advanced_setting.settings.label7")))]),n("el-switch",{on:{change:function(t){return e.changeRenderSet("ssao")}},model:{value:e.ssao,callback:function(t){e.ssao=t},expression:"ssao"}})],1):e._e(),n("div",{staticClass:"space-between-center margin-bottom-15"},[n("span",[e._v(e._s(e.$t("topToolBarMenu.advanced.children.advanced_setting.settings.label8")))]),n("el-switch",{on:{change:function(t){return e.changeRenderSet("shadow")}},model:{value:e.shadow,callback:function(t){e.shadow=t},expression:"shadow"}})],1)])]},proxy:!0}])})},o=[],a=(n("d3b7"),n("3ca3"),n("ddb0"),n("e9c4"),n("159b"),n("f7fe")),s=n.n(a),d={name:"RenderSettings",components:{CommonSVG:function(){return n.e("chunk-51f90655").then(n.bind(null,"17d0"))}},data:function(){return{dialogLeft:350,dialogWidth:400,modeList:[{title:this.$t("dialog.renderSetting.label1"),icon:"engineering_light",value:1,disabled:!1},{title:this.$t("dialog.renderSetting.label2"),icon:"global_illumination_feature",value:2,disabled:!1},{title:this.$t("dialog.renderSetting.label3"),icon:"tb_beautify",value:3,disabled:!1}],modeRadio:1,ssao:!0,shadow:!1}},computed:{dragTopOffset:function(){return this.$store.state.scene.topToolBarHeight},bottomMenuActive:function(){return this.$store.state.menuList.bottomMenuActive}},watch:{bottomMenuActive:{handler:function(e){this.modeList[2].disabled="sectioning"==e},immediate:!0}},created:function(){this.changeRenderSet=s()(this.changeRenderSet,500),this.dialogLeft=(document.body.clientWidth-this.dialogWidth)/2;var e={ssao:window.scene.config.ssao,shadow:window.scene.config.shadow,modeRadio:window.scene.mv.renderMode};this.ssao=window.scene.config.ssao,this.shadow=window.scene.config.shadow,this.modeRadio=window.scene.mv.renderMode;var t=this.$store.state.sceneSetting.renderSettingObj;""==t.rawData&&this.$store.commit("setRenderSettingStorage",{rawData:JSON.stringify(e)})},methods:{changeRenderSet:function(e){var t=null;switch(e){case"modeRadio":document.querySelectorAll(".el-radio__original").forEach((function(e){return e.blur()})),window.scene.mv.renderMode=this.modeRadio,t=this.modeRadio;break;case"ssao":window.scene.config.ssao=this.ssao,t=this.ssao;break;case"shadow":window.scene.config.shadow=this.shadow,t=this.shadow;break}var n={};n[e]=t,this.$store.commit("setRenderSettingStorage",n),this.$nextTick((function(){window.scene.render()}))},closeDialog:function(){this.$store.commit("toggleActiveDialog","renderSetting"),this.$store.commit("toggleMenuActive","renderSetting")}}},r=d,c=(n("ed9a"),n("2877")),l=Object(c["a"])(r,i,o,!1,null,"46f3a642",null);t["default"]=l.exports},ed9a:function(e,t,n){"use strict";n("3382")}}]);