(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-d133c542"],{"3fd8":function(e,t,a){},"45fa":function(e,t,a){"use strict";a("3fd8")},"498a":function(e,t,a){"use strict";var s=a("23e7"),i=a("58a8").trim,n=a("c8d2");s({target:"String",proto:!0,forced:n("trim")},{trim:function(){return i(this)}})},"54bd":function(e,t,a){"use strict";a("d5cb")},7816:function(e,t,a){"use strict";a("f42b")},a866:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("dialogComp",{ref:"linkView",staticClass:"sourceDom",attrs:{hideHeader:!1,needClose:"true",zIndex:"4",height:"150",position:"fixed",left:0,top:0,right:0,bottom:0,title:e.styleFormDatas.typeName,icon:"icon-details",width:390,isSource:!0,type:"detailInfo"},on:{close:e.closeDialog},scopedSlots:e._u([{key:"center",fn:function(){return[a("div",{staticClass:"add-container element-add-link-view"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("el-input",{class:{"is-error":e.inputError&&""==e.styleFormDatas.datas[e.dragData.type].list[0].value},attrs:{size:"small",placeholder:e.dragData.placeholder||e.$t("formRelational.address.placeholder")},model:{value:e.styleFormDatas.datas[e.dragData.type].list[0].value,callback:function(t){e.$set(e.styleFormDatas.datas[e.dragData.type].list[0],"value","string"===typeof t?t.trim():t)},expression:"styleFormDatas.datas[dragData.type].list[0].value"}},[e.styleFormDatas.datas[e.dragData.type].list[0].validate?a("template",{slot:"suffix"},[0==e.styleFormDatas.datas[e.dragData.type].list[0].validateState?a("i",{staticClass:"el-input__icon el-icon-loading"}):e._e(),1==e.styleFormDatas.datas[e.dragData.type].list[0].validateState?a("i",{staticClass:"el-input__icon el-icon-success"}):e._e(),2==e.styleFormDatas.datas[e.dragData.type].list[0].validateState?a("el-tooltip",{staticClass:"box-item",attrs:{enterable:!1,effect:"dark",content:e.$t("others.invalidLink"),placement:"top"}},[2==e.styleFormDatas.datas[e.dragData.type].list[0].validateState?a("i",{staticClass:"el-input__icon el-icon-warning"}):e._e()]):e._e()],1):e._e()],2)],1)],1),e.styleFormDatas.datas[e.dragData.type].list[0].addModeList?a("div",{staticClass:"optional-content"},[a("el-radio-group",{model:{value:e.styleFormDatas.datas[e.dragData.type].list[0].addType,callback:function(t){e.$set(e.styleFormDatas.datas[e.dragData.type].list[0],"addType",t)},expression:"styleFormDatas.datas[dragData.type].list[0].addType"}},e._l(e.styleFormDatas.datas[e.dragData.type].list[0].addModeList,(function(t,s){return a("el-radio",{key:s,attrs:{label:t.value}},[e._v(e._s(t.label))])})),1)],1):e._e(),a("div",{staticClass:"bottom-btn"},[a("span",{staticClass:"cursor-btn cancel margin-right-8",on:{click:e.cancelLinkInput}},[e._v(" "+e._s(e.$t("messageTips.cancel"))+" ")]),a("span",{staticClass:"cursor-btn confirm",on:{click:e.saveLinkInput}},[e._v(" "+e._s(e.$t("menuIconName.confirm"))+" ")])])],1)]},proxy:!0}])})},i=[],n=a("c7eb"),r=a("1da1"),o=(a("d3b7"),a("159b"),a("b0c0"),a("caad"),a("2532"),a("498a"),a("a630"),a("3ca3"),a("ddb0"),a("c740"),a("0e1b")),l=a("aade"),d=a("b893"),c={name:"ElementAddLinkView",mixins:[o["a"],l["a"]],data:function(){return{inputError:!1,linkViewLoading:null,currentFreeSketch:""}},computed:{dragData:function(){return this.$store.state.scene.dragOverData}},created:function(){this.dragData.isDragData?this.styleFormDatas.typeName=this.dragData.title:this.initEditDatas(this.dragData.type)},watch:{"dragData.title":function(){this.styleFormDatas.typeName=this.dragData.title}},methods:{setRandomWmtsLink:function(){var e="",t=Math.floor(8*Math.random()),a=window.location.protocol,s="";s="http:"==a?"http://t".concat(t,".tianditu.com"):"https://t".concat(t,".tianditu.gov.cn"),e=s+this.styleFormDatas.datas[this.dragData.type].list[0].optionalList[0].pathname,this.styleFormDatas.datas[this.dragData.type].list[0].optionalList[0].url=e},initEditDatas:function(e){},initGeoJSONSetting:function(e){var t=this;if(""==e[0].value)return!1;this.linkViewLoading=this.$loading({lock:!0,text:this.$t("messageTips.loadConfiguration"),spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.5)"}),fetch(e[0].value).then((function(e){return e.json()})).then((function(a){if(a.features&&a.features.length>0){var s={useUniquePaint:!0,color:"rgb(255, 50, 0)",opacity:1};a.features.forEach((function(e){e.geometry&&"Polygon"==e.geometry.type&&(s.showFill=!0,s.showOutline=!0,s.outlineColor="rgb(255, 50, 0)",s.outlineWidth=2),e.geometry&&"Point"==e.geometry.type&&(s.showPoint=!0,e.properties&&e.properties.name&&(s.showText=!0,s.size=4,s.textSize=12)),e.geometry&&"LineString"==e.geometry.type&&(s.showLine=!0,s.lineWidth=2)})),t.addGeoJSONFeature(e,s,{priority:0}),setTimeout((function(){t.$deepUpdateScene("geoJSON"),t.linkViewLoading.close(),t.closeDialog(),window.scene.render()}),1e3)}else t.$message.error(t.$t("featureSetting.style.geoJSON.message")),t.linkViewLoading.close()})).catch((function(e){t.$message.error(t.$t("featureSetting.style.geoJSON.message1")),t.linkViewLoading.close()}))},closeDialog:function(){var e=this.$store.state.dialog.activeDialog;e.includes("ElementLink")&&this.$store.commit("toggleActiveDialog","ElementLink")},cancelLinkInput:function(){this.closeDialog(),this.$store.commit("setActivedType","")},addLinkViewLoading:function(){this.linkViewLoading=this.$loading({lock:!0,text:this.$t("others.featurePrepare"),spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.5)"})},saveLinkInput:function(){var e=this;return Object(r["a"])(Object(n["a"])().mark((function t(){var a,s,i,r,o,l,c,u,h,g,m,f,p,y;return Object(n["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(a=e.dragData.type,s=e.styleFormDatas.datas[a].list,""!=s[0].value){t.next=6;break}return e.inputError=!0,e.$message.error(e.$t("messageTips.errorAddress")),t.abrupt("return");case 6:if(i=["_3dTiles","gltf","fbx","panorama","shp","kml","geoJSON"],!i.includes(a)){t.next=17;break}return e.addLinkViewLoading(),r=s[0].value,t.next=12,Object(d["l"])(r);case 12:if(o=t.sent,e.linkViewLoading.close(),o){t.next=17;break}return e.$message({message:e.$t("messageTips.requestFailure")+"，"+e.$t("messageTips.mixedContentErr"),type:"error",duration:6e3}),t.abrupt("return",!1);case 17:t.t0=a,t.next="dem"===t.t0?20:"_3dTiles"===t.t0?29:"fbx"===t.t0||"gltf"===t.t0?31:"panorama"===t.t0?34:"video"===t.t0?37:"polygon"===t.t0?40:"vectorextrude"===t.t0?43:"wmts"===t.t0||"wms"===t.t0||"tms"===t.t0?47:"shp"===t.t0?50:"kml"===t.t0?59:"geoJSON"===t.t0?67:76;break;case 20:if(l=window.scene.findFeature("dem"),!l){t.next=26;break}if(l.url.trim()!=e.styleFormDatas.datas[a].list[0].value.trim()){t.next=26;break}return e.$notify({title:"地形",message:"该地形已存在",type:"info"}),e.cancelLinkInput(),t.abrupt("return");case 26:return e.addLinkViewLoading(),e.addDemFeature(s,{priority:0}),t.abrupt("break",76);case 29:return e.add3DTilesFeature(s,{},{priority:0}),t.abrupt("break",76);case 31:return e.addGltfFbxFeature(),t.abrupt("return");case 34:return e.checkedCoordinateAddFeature(),t.abrupt("return");case 37:return e.freeSketchPolygon("rectangle"),t.abrupt("return");case 40:return e.freeSketchPolygon("polygon"),t.abrupt("return");case 43:return c={top:10,base:0,opacity:1,color:"rgb(255, 50, 0)",useUniquePaint:!0},e.addLinkViewLoading(),e.addVectorExtrudeFeature(s,c,{priority:0}),t.abrupt("break",76);case 47:return e.addLinkViewLoading(),e.addwmtsFeature(s,{priority:0}),t.abrupt("break",76);case 50:if(u=e.styleFormDatas.datas[e.dragData.type].list[0].value,h=Object(d["k"])(u,"shp"),h){t.next=55;break}return e.$message.error(e.$t("messageTips.ensureFileExtension",{type:"shp"})),t.abrupt("return",!1);case 55:return g={color:"rgb(255, 50, 0)",opacity:1,height:1,lineWidth:2,useUniquePaint:!0},e.addLinkViewLoading(),e.addSHPFeature(s,g,{priority:0}),t.abrupt("break",76);case 59:if(m=e.styleFormDatas.datas[e.dragData.type].list[0].value,f=Object(d["k"])(m,"kml"),f){t.next=64;break}return e.$message.error(e.$t("messageTips.ensureFileExtension",{type:"kml"})),t.abrupt("return",!1);case 64:return e.addLinkViewLoading(),e.addKMLFeature(s,{priority:0}),t.abrupt("break",76);case 67:if(p=e.styleFormDatas.datas[e.dragData.type].list[0].value,y=Object(d["k"])(p,["geoJSON","json"]),y){t.next=72;break}return e.$message.error(e.$t("messageTips.ensureFileExtension",{type:"geoJSON 或 json"})),t.abrupt("return",!1);case 72:return e.addLinkViewLoading(),e.initGeoJSONSetting(s),t.abrupt("return");case 76:setTimeout((function(){e.$deepUpdateScene(a),e.linkViewLoading.close(),e.closeDialog(),window.scene.render()}),1e3),e.$store.commit("setActivedType","");case 78:case"end":return t.stop()}}),t)})))()},onCheckedCoordinate:function(e){var t=[0,0,0],a=window.scene.mv._THREE,s=this.dragData.type,i=this.styleFormDatas.datas[s].list[0].addType;try{var n=window.scene.queryPosition(new a.Vector2(e.clientX,e.clientY));""!=n&&void 0!=n&&(t=window.scene.mv.tools.coordinate.vector2mercator(n))}catch(r){}this.styleFormDatas.longitude=t[0],this.styleFormDatas.latitude=t[1],this.styleFormDatas.altitude=t[2],i||(this.currentFreeSketch="",window.scene.mv.status.selectable=!0,window.scene.mv.events.singleSelection.off("default",this.onCheckedCoordinate)),this.submitSetting()},checkedCoordinateAddFeature:function(){this.currentFreeSketch="checkedCoordinate",this.$refs.linkView.$el.style.visibility="hidden",window.scene.mv.status.selectable=!1,this.$message(this.$t("messageTips.publishSomething",{name:this.dragData.title})),window.scene.mv.events.singleSelection.on("default",this.onCheckedCoordinate)},addGltfFbxFeature:function(){this.$refs.linkView.$el.style.visibility="hidden",window.scene.mv.status.selectable=!1;var e=this.dragData.type,t=this.styleFormDatas.datas[e].list[0].addType;t?(this.$message(this.$t("messageTips.continuousAddition")),window.scene.mv.events.contextmenu.on("default",this.offCheckedCoordinate),window["offCheckedCoordinate"]=this.offCheckedCoordinate):this.$message(this.$t("messageTips.publishSomething",{name:this.dragData.title})),window.scene.mv.events.singleSelection.on("default",this.onCheckedCoordinate)},offCheckedCoordinate:function(){window.scene.mv.events.singleSelection.off("default",this.onCheckedCoordinate),window.scene.mv.events.contextmenu.off("default",this.offCheckedCoordinate),this.$message(this.$t("messageTips.exitContinuousAddition")),this.$store.commit("setActivedType",""),window.scene.mv.status.selectable=!0,window.offCheckedCoordinate&&(window.offCheckedCoordinate=null,delete window.offCheckedCoordinate)},submitSetting:function(){var e=this;this.linkViewLoading=this.$loading({lock:!0,text:this.$t("others.featurePrepare"),spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.5)"});var t=this.dragData.type,a=this.styleFormDatas.datas[t].list,s=this.styleFormDatas.datas[t].list[0].addType;switch(t){case"fbx":case"gltf":this.addGltfOrFbxFeature(a,{priority:0},s);break;case"panorama":this.addPanoramaFeature(a,{priority:0});break;case"video":this.addVideoFeature(a,{priority:0});break;case"polygon":var i={color:"rgb(255, 50, 0)",opacity:1,top:0,base:0,interval:100,direction:"x",showline:!1};"environment"===this.dragData.subType&&(i.base=a[4].value),this.addPolygonFeature(a,i,{priority:0});break}setTimeout((function(){e.$deepUpdateScene(t),e.linkViewLoading.close(),e.closeDialog(),window.scene.render()}),1e3)},freeSketchPolygon:function(e){var t=Array.from(window.scene.features.values()),a=t.findIndex((function(e){return"model"==e.type||"underlay"==e.type}));if(-1==a)return this.$message({message:this.$t("messageTips.freeSketch.errorMsg"),type:"warning",showClose:!0,duration:8e3}),this.$store.commit("setActivedType",""),!1;window.scene.mv.tools.draw.active(),this.$refs.linkView.$el.style.visibility="hidden",this.$store.commit("toogleDrawState",!0),"polygon"==e?window.scene.mv.tools.draw.startDrawPolygon():"polyline"==e?window.scene.mv.tools.draw.startDrawLine():"rectangle"==e&&window.scene.mv.tools.draw.startDrawPolygon(),this.$message({showClose:!0,message:this.$t("messageTips.freeSketch.defaultMsg"),duration:7e3}),this.currentFreeSketch=e,"rectangle"==e?window.scene.mv.tools.draw.finishDrawEvent.on("default",this.handleFreeSketchRectangle):window.scene.mv.tools.draw.finishDrawEvent.on("default",this.handleFreeSketchPolygon),document.addEventListener("keyup",this.onEscKeyUp)},checkUrl:function(e){this.styleFormDatas.datas[this.dragData.type].list[0].value=e.url},handleFreeSketchRectangle:function(){var e=window.scene.mv.tools.draw.getAllData();if(e.polygon.length>0){var t=e.polygon[0].points.length;if(t-1<=3)this.$message.error(this.$t("messageTips.freeSketch.errorMsg3"));else{for(var a=0;a<t-1;a++){var s=window.scene.mv.tools.coordinate.vector2mercator(e.polygon[0].points[a]);this.styleFormDatas.datas[this.dragData.type].list[1].cor[a]&&(this.styleFormDatas.datas[this.dragData.type].list[1].cor[a].position.x=s[0],this.styleFormDatas.datas[this.dragData.type].list[1].cor[a].position.y=s[1])}this.submitSetting()}}this.eventLogoffBeforeSetting()},handleFreeSketchPolygon:function(){var e=window.scene.mv.tools.draw.getAllData(),t=this.dragData.type,a=this.styleFormDatas.datas[t].list;if(e.polygon.length>0){var s=e.polygon[0].points.length;if(s-1<=2)this.$message.error(this.$t("messageTips.freeSketch.errorMsg1"));else{for(var i=[],n=0;n<s-1;n++)i[n]=[e.polygon[0].points[n].x,e.polygon[0].points[n].y];a[1].position=i;var r=["environment"];if("polygon"==this.dragData.type&&r.includes(this.dragData.subType)){var o=window.scene.mv.tools.coordinate.vector2mercator(e.polygon[0].points[0]);this.styleFormDatas.datas["polygon"].list[4].value=o[2]}this.submitSetting()}}else{var l=e.polyline[0].points.length;if(l<=1)this.$message.error(this.$t("messageTips.freeSketch.errorMsg2"));else{for(var d=[],c=0;c<l;c++)d[c]=[e.polyline[0].points[c].x,e.polyline[0].points[c].y,e.polyline[0].points[c].z];a[1].position=d,this.submitSetting()}}this.eventLogoffBeforeSetting()},onEscKeyUp:function(e){27==e.keyCode&&"Escape"===e.key&&this.eventLogoffBeforeSetting()},asynchronousListenerTiles:function(){return new Promise((function(e){window.scene.mv.events.featureInited.on("default",(function(){e("featureInited")}))}))},eventLogoffBeforeSetting:function(){switch(this.currentFreeSketch){case"rectangle":window.scene.mv.tools.draw.deactive(),window.scene.mv.tools.draw.finishDrawEvent.off("default",this.handleFreeSketchRectangle);break;case"polyline":case"polygon":window.scene.mv.tools.draw.deactive(),window.scene.mv.tools.draw.finishDrawEvent.off("default",this.handleFreeSketchPolygon);break}this.$store.commit("toogleDrawState",!1),this.$store.commit("setActivedType",""),document.removeEventListener("keyup",this.onEscKeyUp),this.currentFreeSketch="",this.$store.commit("toggleActiveDialog","ElementLink")}}},u=c,h=(a("54bd"),a("45fa"),a("7816"),a("2877")),g=Object(h["a"])(u,s,i,!1,null,"5651f434",null);t["default"]=g.exports},aade:function(e,t,a){"use strict";a("b0c0"),a("7db0"),a("d3b7"),a("d81d"),a("b893");t["a"]={methods:{beforeValidate:function(){var e=this.dragData.type;if("objectSetting"==this.dragData.interlock)return!0;if(window.scene.features.has(this.$refs[e].elementDatas.id)&&this.dragData.isDragData)return this.inputError=!0,this.$message.error(this.$t("formRelational.featureID.message")),!1;if("annotation"===e&&""===this.$refs[e].elementDatas.list[3].htmlCode)return this.inputError=!0,this.$message.error(this.$t("featureSetting.data.anchorPoint.message1")),!1;if(this.styleFormDatas.name!==this.$t("featureDatas._3dBuilding.name")&&""==this.styleFormDatas.name)return this.inputError=!0,this.$message.error(this.$t("featureSetting.style.placeholder")),!1;if(this.$refs[e].elementDatas.longitude<=-180||this.$refs[e].elementDatas.longitude>=180)return this.inputError=!0,this.$refs[e].elementDatas.validate.longitude=!1,this.$message.error(this.$t("formRelational.longitude.message")),!1;if(this.$refs[e].elementDatas.latitude<=-90||this.$refs[e].elementDatas.latitude>=90)return this.inputError=!0,this.$refs[e].elementDatas.validate.latitude=!1,this.$message.error(this.$t("formRelational.latitude.message")),!1;var t=this.$refs[e].elementDatas.list.find((function(e){return e.validate}));if(void 0!=t&&""==t.value&&t.optionState)return this.inputError=!0,this.$message.error(this.$t("others.inputName",{name:t.title})),!1;for(var a in this.$refs[e].elementDatas.longitude=this.$refs[e].elementDatas.longitude||0,this.$refs[e].elementDatas.latitude=this.$refs[e].elementDatas.latitude||0,this.$refs[e].elementDatas.altitude=this.$refs[e].elementDatas.altitude||0,this.$refs[e].elementDatas.rotation=this.$refs[e].elementDatas.rotation||0,void 0!=this.$refs[e].offset&&(this.$refs[e].offset=this.$refs[e].offset.map((function(e){return e||0}))),this.inputError=!1,this.$refs[e].validate)!0;return!0},checkStyleLinkValidate:function(e,t){var a=this,s=this.dragData.type;if(""!=t){this.styleFormDatas.datas[s].list[e].validateState=0;var i=new XMLHttpRequest;i.open("GET",t,!0),i.send();i.onload=function(){var t=null;200==i.status?("success",t=1):("error",t=2),a.styleFormDatas.datas[s].list[e].validateState=t},i.onerror=function(){a.styleFormDatas.datas[s].list[e].validateState=2}}else this.styleFormDatas.datas[s].list[e].validateState=null}}}},c8d2:function(e,t,a){var s=a("5e77").PROPER,i=a("d039"),n=a("5899"),r="​᠎";e.exports=function(e){return i((function(){return!!n[e]()||r[e]()!==r||s&&n[e].name!==e}))}},d5cb:function(e,t,a){},f42b:function(e,t,a){}}]);