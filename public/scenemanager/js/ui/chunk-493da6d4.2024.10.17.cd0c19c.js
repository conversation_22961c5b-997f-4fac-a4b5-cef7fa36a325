(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-493da6d4"],{"2fa4":function(e,t,i){},"878b":function(e,t,i){"use strict";i("2fa4")},cdd6:function(e,t,i){"use strict";i.r(t);var s=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("dialogComp",{attrs:{hideHeader:!1,needClose:"true",zIndex:"5",draw:!0,right:200,drag:!0,title:e.titleContent(),icon:"icon-details",width:360,height:500,type:"detailInfo",dragTopOffset:e.dragTopOffset},on:{close:e.closeDialog},scopedSlots:e._u([{key:"center",fn:function(){return[i("div",{staticClass:"model-tree-container",staticStyle:{height:"100%"}},[e.listData.length?i("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoad,expression:"isLoad"}],staticClass:"tree-middle-container",staticStyle:{height:"100%"},attrs:{"element-loading-text":e.$t("others.dataLoading"),"element-loading-spinner":"el-icon-loading","element-loading-background":"rgba(0, 0, 0, 0.8)"}},[i("div",{staticClass:"tree-content"},e._l(e.listData,(function(t){return i("div",{key:t.id,staticClass:"item",class:{active:e.active===t.id},on:{click:function(i){return e.handleNodeClick(t)}}},[e._v(" "+e._s(t.name)+" ")])})),0)]):i("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoad,expression:"isLoad"}],staticClass:"tree-middle-container",staticStyle:{height:"100%"},attrs:{"element-loading-text":e.$t("others.dataLoading"),"element-loading-spinner":"el-icon-loading","element-loading-background":"rgba(0, 0, 0, 0.8)"}},[i("div",{staticClass:"tree-content"},[i("div",{staticClass:"no-data"},[e._v(" "+e._s(e.$t("others.emptyData"))+" ")])])])])]},proxy:!0}])})},n=[],a=i("5530"),d=(i("d81d"),i("d3b7"),i("159b"),i("99af"),{name:"StructureTree",props:["viewData"],data:function(){return{modelId:"",active:"",listData:[],isLoad:!0,timer:null,defaultTreeProps:{children:"Children",label:"name",isLeaf:"iselem",id:"id"},selectedSystemChildren:[]}},computed:{dragTopOffset:function(){return this.$store.state.scene.topToolBarHeight}},mounted:function(){this.getList()},watch:{"viewData.type":function(){this.getList()}},methods:{titleContent:function(){var e="";switch(this.viewData.type){case"space":e=this.$t("dialog.spaceTree.name");break;case"area":e=this.$t("dialog.areaTree.name");break;case"system":e=this.$t("dialog.systemTree.name");break}return e},getList:function(){var e=this;this.isLoad=!0,this.modelId=this.viewData.modelId;var t=window.scene.features.get(this.modelId);t&&("system"===this.viewData.type?this.listData=t.systems&&t.systems.map((function(e){return Object(a["a"])({},e.topo)})):this.listData=t[this.viewData.type+"s"]||[]),this.timer=setTimeout((function(){e.isLoad=!1}),500)},handleNodeClick:function(e){var t=this.viewData.type;this.modelId=this.viewData.modelId,"space"===t?this.handleSpaceNodeClick(e):"area"===t?this.handleAreaNodeClick(e):"system"===t&&this.handleSystemNodeClick(e)},setSystemChildrenSelected:function(e){var t=this;e.forEach((function(e){var i="".concat(t.modelId,"^").concat(e.id);t.selectedSystemChildren.push(i),e.Attach&&e.Attach.length&&e.Attach.forEach((function(e){t.selectedSystemChildren.push("".concat(t.modelId,"^").concat(e.id))})),e.Children&&t.setSystemChildrenSelected(e.Children)}))},handleSystemNodeClick:function(e){window.scene.clearSelection(),this.selectedSystemChildren=[],e.Children.length?this.setSystemChildrenSelected(e.Children):this.selectedSystemChildren=["".concat(this.modelId,"^").concat(e.id)];var t=window.scene.createArrayObject(this.selectedSystemChildren);if(t.select(),window.scene.fit(this.selectedSystemChildren),window.scene.render(),window.scene.selectedObjects.size>0){var i=window.scene.findObject(this.selectedSystemChildren[this.selectedSystemChildren.length-1]);this.$emit("selectElement",i)}else this.$store.commit("toogleElementMenu",""),this.$store.commit("selectElementData",{});this.active=e.id},handleSpaceNodeClick:function(e){if(window.scene.clearSelection(),e.parent.space){var t=e.id,i=window.scene.findObject(t);i&&(i.selected=!0,window.scene.fit([t]),window.scene.render(),this.active=e.id)}else this.$message.error(this.$t("dialog.systemTree.message"))},handleAreaNodeClick:function(e){if(window.scene.clearSelection(),e.parent.area){var t=e.id,i=window.scene.findObject(t);i&&(i.selected=!0,window.scene.fit([t]),window.scene.render(),this.active=e.id)}else this.$message.error(this.$t("dialog.systemTree.message1"))},handleCheckChange:function(e,t){this.selectedDataLength=t.checkedNodes.length},closeDialog:function(){this.$emit("close")}},beforeDestroy:function(){clearTimeout(this.timer)}}),c=d,o=(i("878b"),i("2877")),l=Object(o["a"])(c,s,n,!1,null,"16d050ef",null);t["default"]=l.exports}}]);