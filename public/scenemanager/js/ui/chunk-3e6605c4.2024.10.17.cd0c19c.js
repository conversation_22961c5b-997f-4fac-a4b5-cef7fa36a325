(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3e6605c4"],{"19ae":function(e,t,n){},"3fac":function(e,t,n){"use strict";n.r(t);var s=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"bottom-menu-container"},[e._l(e.menuList,(function(t,s){return[t.isShow?n("div",{key:t.label,staticClass:"menu-item cursor-btn",class:{active:e.activeMenu==s},on:{click:function(t){return e.toggleMenu(s)}}},[n("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:t.name,placement:"top"}},[n("span",[n("CommonSVG",{attrs:{color:"#FFFFFF","icon-class":t.icon,size:16}})],1)])],1):e._e()]})),n("div",{staticClass:"vertical-split-line"}),n("div",{staticClass:"menu-item cursor-btn",staticStyle:{"font-size":"18px"},on:{click:function(t){return e.toggleMenu(10)}}},[n("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:e.$t("bottomMenu.measure.tooltip"),placement:"top"}},[n("span",[n("CommonSVG",{attrs:{color:"#FFFFFF","icon-class":"clear_feature",size:16}})],1)])],1),n("div",{staticClass:"menu-item cursor-btn",staticStyle:{"font-size":"18px"},on:{click:function(t){return e.toggleMenu(11)}}},[n("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:e.$t("bottomMenu.measure.tooltip1"),placement:"top"}},[n("span",[n("CommonSVG",{attrs:{color:"#FFFFFF","icon-class":"quit",size:16}})],1)])],1)],2)},a=[],i=(n("d3b7"),n("3ca3"),n("ddb0"),{name:"MeasureMenu",components:{CommonSVG:function(){return n.e("chunk-51f90655").then(n.bind(null,"17d0"))}},data:function(){return{activeMenu:-1,menuList:[{name:this.$t("bottomMenu.measure.label"),label:"length-measure",icon:"shortest_distance_feature",isShow:!0},{name:this.$t("bottomMenu.measure.label1"),label:"outline-measure",icon:"measuring_outline_feature",isShow:!0},{name:this.$t("bottomMenu.measure.label2"),label:"shortestDistance-measure",icon:"measure_two_meshs_feature",isShow:!1},{name:this.$t("bottomMenu.measure.label3"),label:"pointToPlane-measure",icon:"measure_space_feature",isShow:!0},{name:this.$t("bottomMenu.measure.label4"),label:"area-measure",icon:"measurement_area_feature",isShow:!0},{name:this.$t("bottomMenu.measure.label5"),label:"angle-measure",icon:"measuring_angle_feature",isShow:!0},{name:this.$t("bottomMenu.measure.label6"),label:"level-measure",icon:"measuring_level_feature",isShow:!0}],activeMenuArr:[]}},methods:{toggleMenu:function(e){switch(e){case 0:if(0==this.activeMenu)return window.scene.mv.tools.measure.length.deactive(),void(this.activeMenu=-1);window.scene.mv.tools.measure.length.active();break;case 1:if(1==this.activeMenu)return window.scene.mv.tools.measure.outline.deactive(),void(this.activeMenu=-1);window.scene.mv.tools.measure.outline.active();break;case 2:if(2==this.activeMenu)return window.scene.mv.tools.measure.minMeshLength.deactive(),void(this.activeMenu=-1);window.scene.mv.tools.measure.minMeshLength.active();break;case 3:if(3==this.activeMenu)return window.scene.mv.tools.measure.pointToPlane.deactive(),void(this.activeMenu=-1);window.scene.mv.tools.measure.pointToPlane.active();break;case 4:if(4==this.activeMenu)return window.scene.mv.tools.measure.area.deactive(),void(this.activeMenu=-1);this.$notify({title:this.$t("bottomMenu.measure.label4"),message:this.$t("bottomMenu.measure.message")}),window.scene.mv.tools.measure.area.active();break;case 5:if(5==this.activeMenu)return window.scene.mv.tools.measure.angle.deactive(),void(this.activeMenu=-1);window.scene.mv.tools.measure.angle.active();break;case 6:if(6==this.activeMenu)return window.scene.mv.tools.measure.level.deactive(),void(this.activeMenu=-1);window.scene.mv.tools.measure.level.active();break;case 10:window.scene.mv.tools.measure.clear();break;case 11:this.$store.commit("toggleBottomMenuActive","measure");break}e<this.menuList.length&&(this.activeMenu=e)}},beforeDestroy:function(){window.scene.mv.tools.measure.clear(),window.scene.mv.tools.measure.deactive(),window.scene.render()}}),o=i,c=(n("f31f"),n("2877")),u=Object(c["a"])(o,s,a,!1,null,"291ccd8e",null);t["default"]=u.exports},f31f:function(e,t,n){"use strict";n("19ae")}}]);