(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0af0ba"],{"0d3c":function(t,e,i){"use strict";var n=i("7c8a"),r=i("8937"),o=function(){function t(t){this._cfgs=Object(r["deepMix"])(this.getDefaultCfgs(),t),this._events={},this.destroyed=!1}return t.prototype.getDefaultCfgs=function(){return{}},t.prototype.initPlugin=function(t){var e=this;e.set("graph",t);var i=e.getEvents(),n={};Object(r["each"])(i,(function(i,o){var a=Object(r["wrapBehavior"])(e,i);n[o]=a,t.on(o,a)})),this._events=n,this.init()},t.prototype.getEvents=function(){return{}},t.prototype.get=function(t){var e;return null===(e=this._cfgs)||void 0===e?void 0:e[t]},t.prototype.set=function(t,e){this._cfgs[t]=e},t.prototype.destroy=function(){},t.prototype.destroyPlugin=function(){this.destroy();var t=this.get("graph"),e=this._events;Object(r["each"])(e,(function(e,i){t.off(i,e)})),this._events=null,this._cfgs=null,this.destroyed=!0},t}(),a=o,s=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!==typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),c="url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHBhdHRlcm4gaWQ9ImdyaWQiIHdpZHRoPSI0MCIgaGVpZ2h0PSI0MCIgcGF0dGVyblVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+PHBhdGggZD0iTSAwIDEwIEwgNDAgMTAgTSAxMCAwIEwgMTAgNDAgTSAwIDIwIEwgNDAgMjAgTSAyMCAwIEwgMjAgNDAgTSAwIDMwIEwgNDAgMzAgTSAzMCAwIEwgMzAgNDAiIGZpbGw9Im5vbmUiIHN0cm9rZT0iI2UwZTBlMCIgb3BhY2l0eT0iMC4yIiBzdHJva2Utd2lkdGg9IjEiLz48cGF0aCBkPSJNIDQwIDAgTCAwIDAgMCA0MCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZTBlMGUwIiBzdHJva2Utd2lkdGg9IjEiLz48L3BhdHRlcm4+PC9kZWZzPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjZ3JpZCkiLz48L3N2Zz4=)",h=function(t){function e(e){return t.call(this,e)||this}return s(e,t),e.prototype.getDefaultCfgs=function(){return{img:c,follow:!0}},e.prototype.init=function(){var t=this.get("graph"),e=t.get("container"),i=t.get("canvas").get("el"),r=this.get("img")||c;r.includes("url(")||(r='url("'.concat(r,'")'));var o=Object(n["b"])("<div class='g6-grid-container' style=\"position:absolute;overflow:hidden;z-index: -1;\"></div>"),a=Object(n["b"])("<div\n        class='g6-grid'\n        style='position:absolute;\n        background-image: ".concat(r,";\n        user-select: none\n        '></div>"));this.set("container",o),this.set("gridContainer",a),this.positionInit(),o.appendChild(a),e.insertBefore(o,i)},e.prototype.positionInit=function(){var t=this.get("graph"),e=t.get("minZoom"),i=t.get("width"),r=t.get("height");Object(n["c"])(this.get("container"),{width:"".concat(i,"px"),height:"".concat(r,"px")});var o=80*i/e,a=80*r/e;Object(n["c"])(this.get("gridContainer"),{width:"".concat(o,"px"),height:"".concat(a,"px"),left:"-".concat(o/2,"px"),top:"-".concat(a/2,"px")})},e.prototype.getEvents=function(){return{viewportchange:"updateGrid"}},e.prototype.updateGrid=function(t){var e=this.get("gridContainer"),i=t.matrix;i||(i=[1,0,0,0,1,0,0,0,1]);var r=this.get("follow"),o="matrix(".concat(i[0],", ").concat(i[1],", ").concat(i[3],", ").concat(i[4],", ").concat(r?i[6]:"0",", ").concat(r?i[7]:"0",")");Object(n["c"])(e,{transform:o})},e.prototype.getContainer=function(){return this.get("container")},e.prototype.destroy=function(){var t=this.get("graph"),e=t.get("container"),i=this.get("container");e.removeChild(i)},e}(a),l=h,d=i("1aef"),p=i.n(d),g=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!==typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}();"undefined"!==typeof document&&p()("\n  .g6-component-contextmenu {\n    border: 1px solid #e2e2e2;\n    border-radius: 4px;\n    font-size: 12px;\n    color: #545454;\n    background-color: rgba(255, 255, 255, 0.9);\n    padding: 10px 8px;\n    box-shadow: rgb(174, 174, 174) 0px 0px 10px;\n  }\n  .g6-contextmenu-ul {\n    padding: 0;\n    margin: 0;\n    list-style: none;\n  }\n\n");var u=function(t){function e(e){return t.call(this,e)||this}return g(e,t),e.prototype.getDefaultCfgs=function(){return{offsetX:6,offsetY:6,handleMenuClick:void 0,getContent:function(t){return"\n          <ul class='g6-contextmenu-ul'>\n            <li>菜单项1</li>\n            <li>菜单项2</li>\n          </ul>\n        "},shouldBegin:function(t){return!0},onHide:function(){return!0},itemTypes:["node","edge","combo"],trigger:"contextmenu"}},e.prototype.getEvents=function(){return"click"===this.get("trigger")?{click:"onMenuShow",touchend:"onMenuShow"}:{contextmenu:"onMenuShow"}},e.prototype.init=function(){var t=this.get("className"),e=Object(n["b"])("<div class=".concat(t||"g6-component-contextmenu","></div>"));Object(n["c"])(e,{top:"0px",position:"absolute",visibility:"hidden"});var i=this.get("container");i||(i=this.get("graph").get("container")),Object(r["isString"])(i)&&(i=document.getElementById(i)),i.appendChild(e),this.set("menu",e)},e.prototype.onMenuShow=function(t){var e=this;t.preventDefault();var i=this.get("itemTypes");if(t.item){if(t.item&&t.item.getType&&-1===i.indexOf(t.item.getType()))return void e.onMenuHide()}else if(-1===i.indexOf("canvas"))return void e.onMenuHide();var o=this.get("shouldBegin");if(o(t)){var a=this.get("menu"),s=this.get("getContent"),c=this.get("graph"),h=s(t,c);Object(r["isString"])(h)?a.innerHTML=h:a.innerHTML=h.outerHTML,this.removeMenuEventListener();var l=this.get("handleMenuClick");if(l){var d=function(e){l(e.target,t.item,c)};this.set("handleMenuClickWrapper",d),a.addEventListener("click",d)}var p=c.get("width"),g=c.get("height"),u=a.getBoundingClientRect(),f=this.get("offsetX")||0,y=this.get("offsetY")||0,v=c.getContainer().offsetTop,m=c.getContainer().offsetLeft,x=t.canvasX+m+f,b=t.canvasY+v+y;x+u.width>p&&(x=t.canvasX-u.width-f+m),b+u.height>g&&(b=t.canvasY-u.height-y+v),Object(n["c"])(a,{top:"".concat(b,"px"),left:"".concat(x,"px"),visibility:"visible"});var w="click"===this.get("trigger"),S=function(t){w?w=!1:e.onMenuHide()};document.body.addEventListener("click",S),this.set("handler",S)}},e.prototype.removeMenuEventListener=function(){var t=this.get("handleMenuClickWrapper"),e=this.get("handler");if(t){var i=this.get("menu");i.removeEventListener("click",t),this.set("handleMenuClickWrapper",null)}e&&document.body.removeEventListener("click",e)},e.prototype.onMenuHide=function(){var t=this.get("menu");t&&Object(n["c"])(t,{visibility:"hidden"}),this.removeMenuEventListener()},e.prototype.destroy=function(){var t=this.get("menu");if(this.removeMenuEventListener(),t){var e=this.get("container");e||(e=this.get("graph").get("container")),Object(r["isString"])(e)&&(e=document.getElementById(e)),e.removeChild(t)}},e}(a),f=u,y=i("53c8"),v=i("bfb1"),m=i("e897"),x=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!==typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),b=function(){return b=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++)for(var r in e=arguments[i],e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t},b.apply(this,arguments)},w=Math.max,S=m["a"].transform,k="default",O="keyShape",C="delegate",M="svg",T=function(t){function e(e){var i=t.call(this,e)||this;return i.handleUpdateCanvas=Object(r["debounce"])((function(t){var e=i;e.destroyed||e.updateCanvas()}),100,!1),i}return x(e,t),e.prototype.getDefaultCfgs=function(){return{container:null,className:"g6-minimap",viewportClassName:"g6-minimap-viewport",type:"default",padding:50,size:[200,120],delegateStyle:{fill:"#40a9ff",stroke:"#096dd9"},refresh:!0}},e.prototype.getEvents=function(){return{beforepaint:"updateViewport",beforeanimate:"disableRefresh",afteranimate:"enableRefresh",viewportchange:"disableOneRefresh"}},e.prototype.disableRefresh=function(){this.set("refresh",!1)},e.prototype.enableRefresh=function(){this.set("refresh",!0),this.updateCanvas()},e.prototype.disableOneRefresh=function(){this.set("viewportChange",!0)},e.prototype.initViewport=function(){var t=this,e=this._cfgs,i=e.size,o=e.graph;if(!this.destroyed){var a=this.get("canvas"),s=a.get("container"),c=Object(n["b"])("\n      <div\n        class=".concat(e.viewportClassName,"\n        style='position:absolute;\n          left:0;\n          top:0;\n          box-sizing:border-box;\n          outline: 2px solid #1980ff;\n          cursor:move'\n        draggable=true\n      </div>")),h=navigator.userAgent.toLowerCase().indexOf("firefox")>-1,l=0,d=0,p=!1,g=0,u=0,f=0,y=0,v=0,m=0;c.addEventListener("dragstart",(function(n){var r,a;if(n.dataTransfer){var s=new Image;s.src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' %3E%3Cpath /%3E%3C/svg%3E",null===(a=(r=n.dataTransfer).setDragImage)||void 0===a||a.call(r,s,0,0);try{n.dataTransfer.setData("text/html","view-port-minimap")}catch(x){n.dataTransfer.setData("text","view-port-minimap")}}if(e.refresh=!1,n.target===c){var h=c.style;g=parseInt(h.left,10),u=parseInt(h.top,10),f=parseInt(h.width,10),y=parseInt(h.height,10),f>i[0]||y>i[1]||(m=o.getZoom(),v=t.get("ratio"),p=!0,l=n.clientX,d=n.clientY)}}),!1),c.addEventListener(h?"dragover":"drag",(function(t){if(p&&!Object(r["isNil"])(t.clientX)&&!Object(r["isNil"])(t.clientY)){var e=l-t.clientX,a=d-t.clientY;(g-e<0||g-e+f>=i[0])&&(e=0),(u-a<0||u-a+y>=i[1])&&(a=0),g-=e,u-=a,Object(n["c"])(c,{left:"".concat(g,"px"),top:"".concat(u,"px")}),o.translate(e*m/v,a*m/v),l=t.clientX,d=t.clientY}}),!1),c.addEventListener("dragend",(function(){p=!1,e.refresh=!0}),!1),this.set("viewport",c),s.appendChild(c)}},e.prototype.updateViewport=function(){if(!this.destroyed){var t=this.get("ratio"),e=(this.get("dx"),this.get("dy"),this.get("totaldx")),i=this.get("totaldy"),r=this.get("graph"),o=this.get("size"),a=r.get("width"),s=r.get("height"),c=r.getPointByCanvas(0,0),h=r.getPointByCanvas(a,s),l=this.get("viewport");l||this.initViewport();var d=(h.x-c.x)*t,p=(h.y-c.y)*t,g=c.x*t+e,u=c.y*t+i,f=g+d,y=u+p;g<0&&(d+=g,g=0),f>o[0]&&(d-=f-o[0]),u<0&&(p+=u,u=0),y>o[1]&&(p-=y-o[1]),this.set("ratio",t);var v="".concat(g,"px"),m="".concat(u,"px");Object(n["c"])(l,{left:v,top:m,width:"".concat(d,"px"),height:"".concat(p,"px")})}},e.prototype.updateGraphShapes=function(){var t=this._cfgs.graph,e=this.get("canvas"),i=t.get("group");if(!i.destroyed){var n=i.clone();n.resetMatrix(),e.clear(),e.add(n);var r=t.get("renderer");r===M&&this.updateVisible(n)}},e.prototype.updateVisible=function(t){var e=this;if(t.isGroup()||t.get("visible")){var i=t.get("children");if(!i||!i.length)return;i.forEach((function(t){t.get("visible")||t.hide(),e.updateVisible(t)}))}else t.hide()},e.prototype.updateKeyShapes=function(){var t=this,e=this._cfgs.graph,i=this.get("canvas"),n=i.get("children")[0]||i.addGroup();Object(r["each"])(e.getEdges(),(function(e){t.updateOneEdgeKeyShape(e,n)})),Object(r["each"])(e.getNodes(),(function(e){t.updateOneNodeKeyShape(e,n)}));var o=e.getCombos();if(o&&o.length){var a=n.find((function(t){return"comboGroup"===t.get("name")}))||n.addGroup({name:"comboGroup"});setTimeout((function(){t.destroyed||(Object(r["each"])(o,(function(e){t.updateOneComboKeyShape(e,a)})),null===a||void 0===a||a.sort(),null===a||void 0===a||a.toBack(),t.updateCanvas())}),250)}this.clearDestroyedShapes()},e.prototype.updateOneComboKeyShape=function(t,e){if(!this.destroyed){var i=this.get("itemMap")||{},n=i[t.get("id")],r=t.getBBox(),o=t.get("keyShape").clone(),a=o.attr(),s={x:r.centerX,y:r.centerY};n?s=Object.assign(a,s):(n=o,e.add(n));var c=n.get("type");"rect"!==c&&"image"!==c||(s.x=r.minX,s.y=r.minY),n.attr(s),t.isVisible()?n.show():n.hide(),n.exist=!0;var h=t.getModel().depth;isNaN(h)||n.set("zIndex",h),i[t.get("id")]=n,this.set("itemMap",i)}},e.prototype.updateOneNodeKeyShape=function(t,e){var i=this.get("itemMap")||{},n=i[t.get("id")],r=t.getBBox(),o=t.get("keyShape").clone(),a=o.attr(),s={x:r.centerX,y:r.centerY};n?s=Object.assign(a,s):(n=o,e.add(n));var c=n.get("type");"rect"!==c&&"image"!==c||(s.x=r.minX,s.y=r.minY),n.attr(s),t.isVisible()?n.show():n.hide(),n.exist=!0;var h=t.getModel().depth;isNaN(h)||n.set("zIndex",h),i[t.get("id")]=n,this.set("itemMap",i)},e.prototype.updateDelegateShapes=function(){var t=this,e=this._cfgs.graph,i=this.get("canvas"),n=i.get("children")[0]||i.addGroup();Object(r["each"])(e.getEdges(),(function(e){t.updateOneEdgeKeyShape(e,n)})),Object(r["each"])(e.getNodes(),(function(e){t.updateOneNodeDelegateShape(e,n)}));var o=e.getCombos();if(o&&o.length){var a=n.find((function(t){return"comboGroup"===t.get("name")}))||n.addGroup({name:"comboGroup"});setTimeout((function(){t.destroyed||(Object(r["each"])(o,(function(e){t.updateOneComboKeyShape(e,a)})),null===a||void 0===a||a.sort(),null===a||void 0===a||a.toBack(),t.updateCanvas())}),250)}this.clearDestroyedShapes()},e.prototype.clearDestroyedShapes=function(){var t=this.get("itemMap")||{},e=Object.keys(t);if(e&&0!==e.length)for(var i=e.length-1;i>=0;i--){var n=t[e[i]],r=n.exist;n.exist=!1,r||(n.remove(),delete t[e[i]])}},e.prototype.updateOneEdgeKeyShape=function(t,e){var i=this.get("itemMap")||{},n=i[t.get("id")];if(n){var r=t.get("keyShape").attr("path");n.attr("path",r)}else n=t.get("keyShape").clone(),e.add(n);t.isVisible()?n.show():n.hide(),n.exist=!0,i[t.get("id")]=n,this.set("itemMap",i)},e.prototype.updateOneNodeDelegateShape=function(t,e){var i=this.get("delegateStyle"),n=this.get("itemMap")||{},r=n[t.get("id")],o=t.getBBox();if(r){var a={x:o.minX,y:o.minY,width:o.width,height:o.height};r.attr(a)}else r=e.addShape("rect",{attrs:b({x:o.minX,y:o.minY,width:o.width,height:o.height},i),name:"minimap-node-shape"});t.isVisible()?r.show():r.hide(),r.exist=!0,n[t.get("id")]=r,this.set("itemMap",n)},e.prototype.init=function(){this.initContainer(),this.get("graph").on("afterupdateitem",this.handleUpdateCanvas),this.get("graph").on("afteritemstatechange",this.handleUpdateCanvas),this.get("graph").on("afteradditem",this.handleUpdateCanvas),this.get("graph").on("afterremoveitem",this.handleUpdateCanvas),this.get("graph").on("afterrender",this.handleUpdateCanvas),this.get("graph").on("afterlayout",this.handleUpdateCanvas)},e.prototype.initContainer=function(){var t=this,e=t.get("graph"),i=t.get("size"),o=t.get("className"),a=t.get("container"),s=Object(n["b"])("<div class='".concat(o,"' style='width: ").concat(i[0],"px; height: ").concat(i[1],"px; overflow: hidden'></div>"));Object(r["isString"])(a)&&(a=document.getElementById(a)),a?a.appendChild(s):e.get("container").appendChild(s),t.set("container",s);var c,h=Object(n["b"])('<div class="g6-minimap-container" style="position: relative;"></div>');s.appendChild(h),h.addEventListener("dragenter",(function(t){t.preventDefault()})),h.addEventListener("dragover",(function(t){t.preventDefault()}));var l=e.get("renderer");c=l===M?new v["a"]({container:h,width:i[0],height:i[1]}):new y["a"]({container:h,width:i[0],height:i[1]}),t.set("canvas",c),t.updateCanvas()},e.prototype.updateCanvas=function(){if(!this.destroyed){var t=this.get("refresh");if(t){var e=this.get("graph");if(!e.get("destroyed")){this.get("viewportChange")&&(this.set("viewportChange",!1),this.updateViewport());var i=this.get("size"),n=this.get("canvas"),r=this.get("type"),o=this.get("padding");if(!n.destroyed){switch(r){case k:this.updateGraphShapes();break;case O:this.updateKeyShapes();break;case C:this.updateDelegateShapes();break;default:break}var a=n.get("children")[0];if(a){a.resetMatrix();var s=a.getCanvasBBox(),c=e.get("canvas").getCanvasBBox(),h=e.getZoom()||1,l=c.width/h,d=c.height/h;Number.isFinite(s.width)&&(l=w(s.width,l),d=w(s.height,d)),l+=2*o,d+=2*o;var p=Math.min(i[0]/l,i[1]/d),g=[1,0,0,0,1,0,0,0,1],u=0,f=0;Number.isFinite(s.minX)&&(u=-s.minX),Number.isFinite(s.minY)&&(f=-s.minY);var y=(i[0]-(l-2*o)*p)/2,v=(i[1]-(d-2*o)*p)/2;g=S(g,[["t",u,f],["s",p,p],["t",y,v]]),a.setMatrix(g),this.set("ratio",p),this.set("totaldx",y+u*p),this.set("totaldy",v+f*p),this.set("dx",y),this.set("dy",v),this.updateViewport()}}}}}},e.prototype.getCanvas=function(){return this.get("canvas")},e.prototype.getViewport=function(){return this.get("viewport")},e.prototype.getContainer=function(){return this.get("container")},e.prototype.destroy=function(){var t;null===(t=this.get("canvas"))||void 0===t||t.destroy();var e=this.get("container");(null===e||void 0===e?void 0:e.parentNode)&&e.parentNode.removeChild(e)},e}(a),E=T,j=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!==typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}();function B(t,e,i){var n=t.x-e.x,r=t.y-e.y;return!i||Math.abs(n)>i||Math.abs(r)>i?Math.sqrt(n*n+r*r):i}function L(t,e){return t.x*e.x+t.y*e.y}function P(t,e){var i=(e.source.y-e.target.y)/(e.source.x-e.target.x),n=(i*i*e.source.x+i*(t.y-e.source.y)+t.x)/(i*i+1),r=i*(n-e.source.x)+e.source.y;return{x:n,y:r}}var I=function(t){function e(e){return t.call(this,e)||this}return j(e,t),e.prototype.getDefaultCfgs=function(){return{edgeBundles:[],edgePoints:[],K:.1,lambda:.1,divisions:1,divRate:2,cycles:6,iterations:90,iterRate:.6666667,bundleThreshold:.6,eps:1e-6,onLayoutEnd:function(){},onTick:function(){}}},e.prototype.init=function(){var t=this.get("graph"),e=this.get("onTick"),i=function(){e&&e(),t.refreshPositions()};this.set("tick",i)},e.prototype.bundling=function(t){var e=this;if(e.set("data",t),!e.isTicking()){var i=t.edges||[],n=t.nodes||[],r={},o=!1;if(n.forEach((function(t){null!==t.x&&null!==!t.y&&void 0!==t.x&&void 0!==!t.y||(o=!0),r[t.id]=t})),o)throw new Error("please layout the graph or assign x and y for nodes first");e.set("nodeIdMap",r);var a=e.get("divisions"),s=e.get("divRate"),c=e.divideEdges(a);e.set("edgePoints",c);var h=e.getEdgeBundles();e.set("edgeBundles",h);for(var l=e.get("cycles"),d=e.get("iterations"),p=e.get("iterRate"),g=e.get("lambda"),u=0;u<l;u++){for(var f=function(t){var n=[];i.forEach((function(t,i){if(t.source!==t.target){var o=r[t.source],s=r[t.target];n[i]=e.getEdgeForces({source:o,target:s},i,a,g);for(var h=0;h<a+1;h++)c[i][h].x+=n[i][h].x,c[i][h].y+=n[i][h].y}}))},y=0;y<d;y++)f(y);g/=2,a*=s,d*=p,c=e.divideEdges(a),e.set("edgePoints",c)}i.forEach((function(t,e){t.source!==t.target&&(t.type="polyline",t.controlPoints=c[e].slice(1,c[e].length-1))}));var v=e.get("graph");v.refresh()}},e.prototype.updateBundling=function(t){var e=this,i=t.data;if(i&&e.set("data",i),e.get("ticking")&&e.set("ticking",!1),Object.keys(t).forEach((function(i){e.set(i,t[i])})),t.onTick){var n=this.get("graph");e.set("tick",(function(){t.onTick(),n.refresh()}))}e.bundling(i)},e.prototype.divideEdges=function(t){var e=this,i=e.get("data").edges,n=e.get("nodeIdMap"),r=e.get("edgePoints");return r&&void 0!==r||(r=[]),i.forEach((function(i,o){r[o]&&void 0!==r[o]||(r[o]=[]);var a=n[i.source],s=n[i.target];if(1===t)r[o].push({x:a.x,y:a.y}),r[o].push({x:.5*(a.x+s.x),y:.5*(a.y+s.y)}),r[o].push({x:s.x,y:s.y});else{var c=0;c=r[o]&&r[o]!==[]?e.getEdgeLength(r[o]):B({x:a.x,y:a.y},{x:s.x,y:s.y});var h=c/(t+1),l=h,d=[{x:a.x,y:a.y}];r[o].forEach((function(t,e){if(0!==e){var i=B(t,r[o][e-1]);while(i>l){var n=l/i,a={x:r[o][e-1].x,y:r[o][e-1].y};a.x+=n*(t.x-r[o][e-1].x),a.y+=n*(t.y-r[o][e-1].y),d.push(a),i-=l,l=h}l-=i}})),d.push({x:s.x,y:s.y}),r[o]=d}})),r},e.prototype.getEdgeLength=function(t){var e=0;return t.forEach((function(i,n){0!==n&&(e+=B(i,t[n-1]))})),e},e.prototype.getEdgeBundles=function(){var t=this,e=t.get("data"),i=e.edges||[],n=t.get("bundleThreshold"),r=t.get("nodeIdMap"),o=t.get("edgeBundles");return o||(o=[]),i.forEach((function(t,e){o[e]&&void 0!==o[e]||(o[e]=[])})),i.forEach((function(e,a){var s=r[e.source],c=r[e.target];i.forEach((function(e,i){if(!(i<=a)){var h=r[e.source],l=r[e.target],d=t.getBundleScore({source:s,target:c},{source:h,target:l});d>=n&&(o[a].push(i),o[i].push(a))}}))})),o},e.prototype.getBundleScore=function(t,e){var i=this;t.vx=t.target.x-t.source.x,t.vy=t.target.y-t.source.y,e.vx=e.target.x-e.source.x,e.vy=e.target.y-e.source.y,t.length=B({x:t.source.x,y:t.source.y},{x:t.target.x,y:t.target.y}),e.length=B({x:e.source.x,y:e.source.y},{x:e.target.x,y:e.target.y});var n=i.getAngleScore(t,e),r=i.getScaleScore(t,e),o=i.getPositionScore(t,e),a=i.getVisibilityScore(t,e);return n*r*o*a},e.prototype.getAngleScore=function(t,e){var i=L({x:t.vx,y:t.vy},{x:e.vx,y:e.vy});return i/(t.length*e.length)},e.prototype.getScaleScore=function(t,e){var i=(t.length+e.length)/2,n=2/(i/Math.min(t.length,e.length)+Math.max(t.length,e.length)/i);return n},e.prototype.getPositionScore=function(t,e){var i=(t.length+e.length)/2,n={x:(t.source.x+t.target.x)/2,y:(t.source.y+t.target.y)/2},r={x:(e.source.x+e.target.x)/2,y:(e.source.y+e.target.y)/2},o=B(n,r);return i/(i+o)},e.prototype.getVisibilityScore=function(t,e){var i=this.getEdgeVisibility(t,e),n=this.getEdgeVisibility(e,t);return i<n?i:n},e.prototype.getEdgeVisibility=function(t,e){var i=P(e.source,t),n=P(e.target,t),r={x:(i.x+n.x)/2,y:(i.y+n.y)/2},o={x:(t.source.x+t.target.x)/2,y:(t.source.y+t.target.y)/2};return Math.max(0,1-2*B(r,o)/B(i,n))},e.prototype.getEdgeForces=function(t,e,i,n){for(var r=this,o=r.get("edgePoints"),a=r.get("K"),s=a/(B(t.source,t.target)*(i+1)),c=[{x:0,y:0}],h=1;h<i;h++){var l={x:0,y:0},d=r.getSpringForce({pre:o[e][h-1],cur:o[e][h],next:o[e][h+1]},s),p=r.getElectrostaticForce(h,e);l.x=n*(d.x+p.x),l.y=n*(d.y+p.y),c.push(l)}return c.push({x:0,y:0}),c},e.prototype.getSpringForce=function(t,e){var i=t.pre.x+t.next.x-2*t.cur.x,n=t.pre.y+t.next.y-2*t.cur.y;return i*=e,n*=e,{x:i,y:n}},e.prototype.getElectrostaticForce=function(t,e){var i=this,n=i.get("eps"),r=i.get("edgeBundles"),o=i.get("edgePoints"),a=r[e],s={x:0,y:0};return a.forEach((function(i){var r={x:o[i][t].x-o[e][t].x,y:o[i][t].y-o[e][t].y};if(Math.abs(r.x)>n||Math.abs(r.y)>n){var a=B(o[i][t],o[e][t]),c=1/a;s.x+=r.x*c,s.y+=r.y*c}})),s},e.prototype.isTicking=function(){return this.get("ticking")},e.prototype.getSimulation=function(){return this.get("forceSimulation")},e.prototype.destroy=function(){this.get("ticking")&&this.getSimulation().stop(),t.prototype.destroy.call(this)},e}(a),A=I,D=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!==typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),_=function(){return _=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++)for(var r in e=arguments[i],e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t},_.apply(this,arguments)},G=.05,Y={stroke:"#000",strokeOpacity:.8,lineWidth:2,fillOpacity:.1,fill:"#ccc"},R=function(t){function e(e){return t.call(this,e)||this}return D(e,t),e.prototype.getDefaultCfgs=function(){return{trigger:"mousemove",d:1.5,r:300,delegateStyle:Object(r["clone"])(Y),showLabel:!1,maxD:5,minD:0,scaleRBy:"unset",scaleDBy:"unset",showDPercent:!0}},e.prototype.getEvents=function(){var t;switch(this.get("trigger")){case"click":t={click:"magnify"};break;case"drag":t={click:"createDelegate"};break;default:t={mousemove:"magnify"};break}return t},e.prototype.init=function(){var t=this,e=t.get("r");t.set("cachedMagnifiedModels",[]),t.set("cachedOriginPositions",{}),t.set("r2",e*e);var i=t.get("d");t.set("molecularParam",(i+1)*e)},e.prototype.createDelegate=function(t){var e=this,i=this,n=i.get("delegate");n&&!n.destroyed||(i.magnify(t),n=i.get("delegate"),n.on("dragstart",(function(t){i.set("delegateCenterDiff",{x:n.attr("x")-t.x,y:n.attr("y")-t.y})})),n.on("drag",(function(t){i.magnify(t)})),"wheel"===this.get("scaleDBy")&&n.on("mousewheel",(function(t){e.scaleDByWheel(t)})),"wheel"===this.get("scaleRBy")&&n.on("mousewheel",(function(t){i.scaleRByWheel(t)})))},e.prototype.scaleRByWheel=function(t){var e=this;if(t&&t.originalEvent){t.preventDefault&&t.preventDefault();var i,n=e.get("graph"),r=e.get("delegate"),o=r?{x:r.attr("x"),y:r.attr("y")}:void 0,a=o||n.getPointByClient(t.clientX,t.clientY);i=t.originalEvent.wheelDelta<0?1-G:1/(1-G);var s=e.get("maxR"),c=e.get("minR"),h=e.get("r");(h>(s||n.get("height"))&&i>1||h<(c||.05*n.get("height"))&&i<1)&&(i=1),h*=i,e.set("r",h),e.set("r2",h*h);var l=e.get("d");e.set("molecularParam",(l+1)*h),e.set("delegateCenterDiff",void 0),e.magnify(t,a)}},e.prototype.scaleRByDrag=function(t){var e=this;if(t){var i,n=e.get("dragPrePos"),r=e.get("graph"),o=r.getPointByClient(t.clientX,t.clientY);i=t.x-n.x<0?1-G:1/(1-G);var a=e.get("maxR"),s=e.get("minR"),c=e.get("r");(c>(a||r.get("height"))&&i>1||c<(s||.05*r.get("height"))&&i<1)&&(i=1),c*=i,e.set("r",c),e.set("r2",c*c);var h=e.get("d");e.set("molecularParam",(h+1)*c),e.magnify(t,o),e.set("dragPrePos",{x:t.x,y:t.y})}},e.prototype.scaleDByWheel=function(t){var e=this;if(t||t.originalEvent){t.preventDefault&&t.preventDefault();var i=0;i=t.originalEvent.wheelDelta<0?-.1:.1;var n=e.get("d"),r=n+i,o=e.get("maxD"),a=e.get("minD");if(r<o&&r>a){e.set("d",r);var s=e.get("r");e.set("molecularParam",(r+1)*s);var c=e.get("delegate"),h=c?{x:c.attr("x"),y:c.attr("y")}:void 0;e.set("delegateCenterDiff",void 0),e.magnify(t,h)}}},e.prototype.scaleDByDrag=function(t){var e=this,i=e.get("dragPrePos"),n=t.x-i.x>0?.1:-.1,r=e.get("d"),o=r+n,a=e.get("maxD"),s=e.get("minD");if(o<a&&o>s){e.set("d",o);var c=e.get("r");e.set("molecularParam",(o+1)*c),e.magnify(t)}e.set("dragPrePos",{x:t.x,y:t.y})},e.prototype.magnify=function(t,e){var i=this;i.restoreCache();var n=i.get("graph"),r=i.get("cachedMagnifiedModels"),o=i.get("cachedOriginPositions"),a=i.get("showLabel"),s=i.get("r"),c=i.get("r2"),h=i.get("d"),l=i.get("molecularParam"),d=n.getNodes(),p=d.length,g=e?{x:e.x,y:e.y}:{x:t.x,y:t.y};!i.get("dragging")||"mousemove"!==i.get("trigger")&&"click"!==i.get("trigger")||(g=i.get("cacheCenter"));var u=i.get("delegateCenterDiff");u&&(g.x+=u.x,g.y+=u.y),i.updateDelegate(g,s);for(var f=0;f<p;f++){var y=d[f].getModel(),v=y.x,m=y.y;if(!isNaN(v)&&!isNaN(m)){var x=(v-g.x)*(v-g.x)+(m-g.y)*(m-g.y);if(!isNaN(x)&&x<c&&0!==x){var b=Math.sqrt(x),w=l*b/(h*b+s),S=(v-g.x)/b,k=(m-g.y)/b;if(y.x=S*w+g.x,y.y=k*w+g.y,o[y.id]||(o[y.id]={x:v,y:m,texts:[]}),r.push(y),a&&2*b<s)for(var O=d[f],C=O.getContainer(),M=C.getChildren(),T=M.length,E=0;E<T;E++){var j=M[E];"text"===j.get("type")&&(o[y.id].texts.push({visible:j.get("visible"),shape:j}),j.set("visible",!0))}}}}n.refreshPositions()},e.prototype.restoreCache=function(){for(var t=this,e=t.get("cachedMagnifiedModels"),i=t.get("cachedOriginPositions"),n=e.length,r=0;r<n;r++){var o=e[r],a=o.id,s=i[a];o.x=s.x,o.y=s.y;for(var c=s.texts.length,h=0;h<c;h++){var l=s.texts[h];l.shape.set("visible",l.visible)}}t.set("cachedMagnifiedModels",[]),t.set("cachedOriginPositions",{})},e.prototype.updateParams=function(t){var e=this,i=t.r,n=t.d,r=t.trigger,o=t.minD,a=t.maxD,s=t.minR,c=t.maxR,h=t.scaleDBy,l=t.scaleRBy;isNaN(t.r)||(e.set("r",i),e.set("r2",i*i)),isNaN(n)||e.set("d",n),isNaN(a)||e.set("maxD",a),isNaN(o)||e.set("minD",o),isNaN(c)||e.set("maxR",c),isNaN(s)||e.set("minR",s);var d=e.get("d"),p=e.get("r");if(e.set("molecularParam",(d+1)*p),"mousemove"!==r&&"click"!==r&&"drag"!==r||e.set("trigger",r),"drag"===h||"wheel"===h||"unset"===h){e.set("scaleDBy",h),e.get("delegate").remove(),e.get("delegate").destroy();var g=e.get("dPercentText");g&&(g.remove(),g.destroy())}if("drag"===l||"wheel"===l||"unset"===l){e.set("scaleRBy",l),e.get("delegate").remove(),e.get("delegate").destroy();g=e.get("dPercentText");g&&(g.remove(),g.destroy())}},e.prototype.updateDelegate=function(t,e){var i=this,n=this,r=n.get("graph"),o=n.get("delegate");if(!o||o.destroyed){var a=r.get("group"),s=n.get("delegateStyle")||Y;o=a.addShape("circle",{attrs:_({r:e/1.5,x:t.x,y:t.y},s),name:"lens-shape",draggable:!0}),"drag"!==this.get("trigger")&&("wheel"===this.get("scaleRBy")?o.on("mousewheel",(function(t){n.scaleRByWheel(t)})):"drag"===this.get("scaleRBy")&&(o.on("dragstart",(function(t){n.set("dragging",!0),n.set("cacheCenter",{x:t.x,y:t.y}),n.set("dragPrePos",{x:t.x,y:t.y})})),o.on("drag",(function(t){n.scaleRByDrag(t)})),o.on("dragend",(function(t){n.set("dragging",!1)}))),"wheel"===this.get("scaleDBy")?o.on("mousewheel",(function(t){i.scaleDByWheel(t)})):"drag"===this.get("scaleDBy")&&(o.on("dragstart",(function(t){n.set("dragging",!0),n.set("cacheCenter",{x:t.x,y:t.y}),n.set("dragPrePos",{x:t.x,y:t.y})})),o.on("drag",(function(t){i.scaleDByDrag(t)})),o.on("dragend",(function(t){n.set("dragging",!1)}))))}else o.attr({x:t.x,y:t.y,r:e/1.5});if(n.get("showDPercent")){var c=Math.round((n.get("d")-n.get("minD"))/(n.get("maxD")-n.get("minD"))*100),h=n.get("dPercentText"),l=t.y+e/1.5+16;if(!h||h.destroyed){var d=r.get("group");h=d.addShape("text",{attrs:{text:"".concat(c,"%"),x:t.x,y:l,fill:"#aaa",stroke:"#fff",lineWidth:1,fontSize:12}}),n.set("dPercentText",h)}else h.attr({text:"".concat(c,"%"),x:t.x,y:l})}n.set("delegate",o)},e.prototype.clear=function(){var t=this.get("graph");this.restoreCache(),t.refreshPositions();var e=this.get("delegate");e&&!e.destroyed&&(e.remove(),e.destroy());var i=this.get("dPercentText");i&&!i.destroyed&&(i.remove(),i.destroy())},e.prototype.destroy=function(){this.clear()},e}(a),X=R,z=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!==typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),H=.05;"undefined"!==typeof document&&p()("\n  .g6-component-toolbar {\n    position: absolute;\n    list-style-type: none;\n    padding: 6px;\n    left: 0px;\n    top: 0px;\n    background-color: rgba(255, 255, 255, 0.9);\n    border: 1px solid #e2e2e2;\n    border-radius: 4px;\n    font-size: 12px;\n    color: #545454;\n    margin: 0;\n  }\n  .g6-component-toolbar li {\n    float: left;\n    text-align: center;\n    width: 35px;\n    height: 24px;\n    cursor: pointer;\n\t\tlist-style-type:none;\n    list-style: none;\n    margin-left: 0px;\n  }\n  .g6-component-toolbar li .icon {\n    opacity: 0.7;\n  }\n  .g6-component-toolbar li .icon:hover {\n    opacity: 1;\n  }\n");var N=function(t){if(!t)return[];if(t.composedPath)return t.composedPath();var e=[],i=t.target;while(i){if(e.push(i),"HTML"===i.tagName)return e.push(document,window),e;i=i.parentElement}return e},F=function(t){function e(e){return t.call(this,e)||this}return z(e,t),e.prototype.getDefaultCfgs=function(){return{handleClick:void 0,getContent:function(t){return'\n          <ul class=\'g6-component-toolbar\'>\n            <li code=\'redo\'>\n              <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="24" height="24">\n                <path d="M256 682.666667c0-102.741333 66.730667-213.333333 213.333333-213.333334 107.008 0 190.762667 56.576 230.570667 125.354667L611.968 682.666667H853.333333v-241.365334l-91.562666 91.562667C704.768 448.469333 601.130667 384 469.333333 384c-196.096 0-298.666667 150.229333-298.666666 298.666667h85.333333z" fill="" p-id="2041"></path>\n              </svg>\n            </li>\n            <li code=\'undo\'>\n              <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="24" height="24">\n                <path d="M170.666667 682.666667h241.365333l-87.936-87.978667C363.904 525.909333 447.658667 469.333333 554.666667 469.333333c146.602667 0 213.333333 110.592 213.333333 213.333334h85.333333c0-148.437333-102.570667-298.666667-298.666666-298.666667-131.797333 0-235.392 64.469333-292.48 148.821333L170.666667 441.301333V682.666667z" fill="" p-id="2764"></path>\n              </svg>\n            </li>\n            <li  code=\'zoomOut\'>\n              <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="24" height="24">\n                <path d="M658.432 428.736a33.216 33.216 0 0 1-33.152 33.152H525.824v99.456a33.216 33.216 0 0 1-66.304 0V461.888H360.064a33.152 33.152 0 0 1 0-66.304H459.52V296.128a33.152 33.152 0 0 1 66.304 0V395.52H625.28c18.24 0 33.152 14.848 33.152 33.152z m299.776 521.792a43.328 43.328 0 0 1-60.864-6.912l-189.248-220.992a362.368 362.368 0 0 1-215.36 70.848 364.8 364.8 0 1 1 364.8-364.736 363.072 363.072 0 0 1-86.912 235.968l192.384 224.64a43.392 43.392 0 0 1-4.8 61.184z m-465.536-223.36a298.816 298.816 0 0 0 298.432-298.432 298.816 298.816 0 0 0-298.432-298.432A298.816 298.816 0 0 0 194.24 428.8a298.816 298.816 0 0 0 298.432 298.432z"></path>\n              </svg>\n            </li>\n            <li code=\'zoomIn\'>\n              <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="24" height="24">\n                <path d="M639.936 416a32 32 0 0 1-32 32h-256a32 32 0 0 1 0-64h256a32 32 0 0 1 32 32z m289.28 503.552a41.792 41.792 0 0 1-58.752-6.656l-182.656-213.248A349.76 349.76 0 0 1 480 768 352 352 0 1 1 832 416a350.4 350.4 0 0 1-83.84 227.712l185.664 216.768a41.856 41.856 0 0 1-4.608 59.072zM479.936 704c158.784 0 288-129.216 288-288S638.72 128 479.936 128a288.32 288.32 0 0 0-288 288c0 158.784 129.216 288 288 288z" p-id="3853"></path>\n              </svg>\n            </li>\n            <li code=\'realZoom\'>\n              <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="20" height="24">\n                <path d="M384 320v384H320V320h64z m256 0v384H576V320h64zM512 576v64H448V576h64z m0-192v64H448V384h64z m355.968 576H92.032A28.16 28.16 0 0 1 64 931.968V28.032C64 12.608 76.608 0 95.168 0h610.368L896 192v739.968a28.16 28.16 0 0 1-28.032 28.032zM704 64v128h128l-128-128z m128 192h-190.464V64H128v832h704V256z"></path>\n              </svg>\n            </li>\n            <li code=\'autoZoom\'>\n              <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="20" height="24">\n                <path d="M684.288 305.28l0.128-0.64-0.128-0.64V99.712c0-19.84 15.552-35.904 34.496-35.712a35.072 35.072 0 0 1 34.56 35.776v171.008h170.944c19.648 0 35.84 15.488 35.712 34.432a35.072 35.072 0 0 1-35.84 34.496h-204.16l-0.64-0.128a32.768 32.768 0 0 1-20.864-7.552c-1.344-1.024-2.816-1.664-3.968-2.816-0.384-0.32-0.512-0.768-0.832-1.088a33.472 33.472 0 0 1-9.408-22.848zM305.28 64a35.072 35.072 0 0 0-34.56 35.776v171.008H99.776A35.072 35.072 0 0 0 64 305.216c0 18.944 15.872 34.496 35.84 34.496h204.16l0.64-0.128a32.896 32.896 0 0 0 20.864-7.552c1.344-1.024 2.816-1.664 3.904-2.816 0.384-0.32 0.512-0.768 0.768-1.088a33.024 33.024 0 0 0 9.536-22.848l-0.128-0.64 0.128-0.704V99.712A35.008 35.008 0 0 0 305.216 64z m618.944 620.288h-204.16l-0.64 0.128-0.512-0.128c-7.808 0-14.72 3.2-20.48 7.68-1.28 1.024-2.752 1.664-3.84 2.752-0.384 0.32-0.512 0.768-0.832 1.088a33.664 33.664 0 0 0-9.408 22.912l0.128 0.64-0.128 0.704v204.288c0 19.712 15.552 35.904 34.496 35.712a35.072 35.072 0 0 0 34.56-35.776V753.28h170.944c19.648 0 35.84-15.488 35.712-34.432a35.072 35.072 0 0 0-35.84-34.496z m-593.92 11.52c-0.256-0.32-0.384-0.768-0.768-1.088-1.088-1.088-2.56-1.728-3.84-2.688a33.088 33.088 0 0 0-20.48-7.68l-0.512 0.064-0.64-0.128H99.84a35.072 35.072 0 0 0-35.84 34.496 35.072 35.072 0 0 0 35.712 34.432H270.72v171.008c0 19.84 15.552 35.84 34.56 35.776a35.008 35.008 0 0 0 34.432-35.712V720l-0.128-0.64 0.128-0.704a33.344 33.344 0 0 0-9.472-22.848zM512 374.144a137.92 137.92 0 1 0 0.128 275.84A137.92 137.92 0 0 0 512 374.08z"></path>\n              </svg>\n            </li>\n          </ul>\n        '},zoomSensitivity:2}},e.prototype.init=function(){var t=this,e=this.get("graph"),i=this.get("getContent"),o=i(e),a=o;Object(r["isString"])(o)&&(a=Object(n["b"])(o));var s=this.get("className");a.setAttribute("class",s||"g6-component-toolbar");var c=this.get("container");c||(c=this.get("graph").get("container")),Object(r["isString"])(c)&&(c=document.getElementById(c)),c.appendChild(a),this.set("toolBar",a);var h=this.get("handleClick");a.addEventListener("click",(function(i){var n=N(i).filter((function(t){return"LI"===t.nodeName}));if(0!==n.length){var r=n[0].getAttribute("code");r&&(h?h(r,e):t.handleDefaultOperator(r))}}));var l=this.get("position");l&&Object(n["c"])(a,{top:"".concat(l.y,"px"),left:"".concat(l.x,"px")}),this.bindUndoRedo()},e.prototype.bindUndoRedo=function(){var t=this.get("graph"),e=document.querySelector('.g6-component-toolbar li[code="undo"]'),i=document.querySelector('.g6-component-toolbar li[code="undo"] svg'),n=document.querySelector('.g6-component-toolbar li[code="redo"]'),r=document.querySelector('.g6-component-toolbar li[code="redo"] svg');e&&i&&n&&r&&t.on("stackchange",(function(t){var o=t.undoStack,a=t.redoStack,s=o.length,c=a.length;1===s?(e.setAttribute("style","cursor: not-allowed"),i.setAttribute("style","opacity: 0.4")):(e.removeAttribute("style"),i.removeAttribute("style")),0===c?(n.setAttribute("style","cursor: not-allowed"),r.setAttribute("style","opacity: 0.4")):(n.removeAttribute("style"),r.removeAttribute("style"))}))},e.prototype.undo=function(){var t=this.get("graph"),e=t.getUndoStack();if(e&&1!==e.length){var i=e.pop();if(i){var n=i.action;t.pushStack(n,Object(r["clone"])(i.data),"redo");var o=i.data.before;if("add"===n&&(o=i.data.after),!o)return;switch(n){case"visible":Object.keys(o).forEach((function(e){var i=o[e];i&&i.forEach((function(e){var i=t.findById(e.id);e.visible?t.showItem(i,!1):t.hideItem(i,!1)}))}));break;case"render":case"update":Object.keys(o).forEach((function(e){var i=o[e];i&&i.forEach((function(e){t.updateItem(e.id,e,!1)}))}));break;case"changedata":t.changeData(o,!1);break;case"delete":Object.keys(o).forEach((function(e){var i=o[e];i&&i.forEach((function(e){var i=e.itemType;delete e.itemType,t.addItem(i,e,!1)}))}));break;case"add":Object.keys(o).forEach((function(e){var i=o[e];i&&i.forEach((function(e){t.removeItem(e.id,!1)}))}));break;case"updateComboTree":Object.keys(o).forEach((function(e){var i=o[e];i&&i.forEach((function(e){t.updateComboTree(e.id,e.parentId,!1)}))}));break;default:}}}},e.prototype.redo=function(){var t=this.get("graph"),e=t.getRedoStack();if(e&&0!==e.length){var i=e.pop();if(i){var n=i.action,o=i.data.after;if(t.pushStack(n,Object(r["clone"])(i.data)),"delete"===n&&(o=i.data.before),!o)return;switch(n){case"visible":Object.keys(o).forEach((function(e){var i=o[e];i&&i.forEach((function(e){var i=t.findById(e.id);e.visible?t.showItem(i,!1):t.hideItem(i,!1)}))}));break;case"render":case"update":Object.keys(o).forEach((function(e){var i=o[e];i&&i.forEach((function(e){t.updateItem(e.id,e,!1)}))}));break;case"changedata":t.changeData(o,!1);break;case"delete":o.edges&&o.edges.forEach((function(e){t.removeItem(e.id,!1)})),o.nodes&&o.nodes.forEach((function(e){t.removeItem(e.id,!1)})),o.combos&&o.combos.forEach((function(e){t.removeItem(e.id,!1)}));break;case"add":Object.keys(o).forEach((function(e){var i=o[e];i&&i.forEach((function(e){var i=e.itemType;delete e.itemType,t.addItem(i,e,!1)}))}));break;case"updateComboTree":Object.keys(o).forEach((function(e){var i=o[e];i&&i.forEach((function(e){t.updateComboTree(e.id,e.parentId,!1)}))}));break;default:}}}},e.prototype.zoomOut=function(){var t=this.get("graph"),e=t.getZoom(),i=1/(1-H*this.get("zoomSensitivity")),n=this.get("maxZoom")||t.get("maxZoom");i*e>n||t.zoomTo(e*i)},e.prototype.zoomIn=function(){var t=this.get("graph"),e=t.getZoom(),i=1-H*this.get("zoomSensitivity"),n=this.get("minZoom")||t.get("minZoom");i*e<n||t.zoomTo(e*i)},e.prototype.realZoom=function(){var t=this.get("graph");t.zoomTo(1)},e.prototype.autoZoom=function(){var t=this.get("graph");t.fitView([20,20])},e.prototype.handleDefaultOperator=function(t){switch(t){case"redo":this.redo();break;case"undo":this.undo();break;case"zoomOut":this.zoomOut();break;case"zoomIn":this.zoomIn();break;case"realZoom":this.realZoom();break;case"autoZoom":this.autoZoom();break;default:}},e.prototype.destroy=function(){var t=this.get("toolBar");if(t){var e=this.get("container");e||(e=this.get("graph").get("container")),Object(r["isString"])(e)&&(e=document.getElementById(e)),e.removeChild(t)}var i=this.get("handleClick");i&&t.removeEventListener("click",i)},e}(a),W=F,V=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!==typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}();"undefined"!==typeof document&&p()("\n  .g6-component-tooltip {\n    border: 1px solid #e2e2e2;\n    border-radius: 4px;\n    font-size: 12px;\n    color: #545454;\n    background-color: rgba(255, 255, 255, 0.9);\n    padding: 10px 8px;\n    box-shadow: rgb(174, 174, 174) 0px 0px 10px;\n  }\n  .tooltip-type {\n    padding: 0;\n    margin: 0;\n  }\n  .tooltip-id {\n    color: #531dab;\n  }\n");var Z=function(t){function e(e){return t.call(this,e)||this}return V(e,t),e.prototype.getDefaultCfgs=function(){return{offsetX:6,offsetY:6,getContent:function(t){return"\n          <h4 class='tooltip-type'>类型：".concat(t.item.getType(),"</h4>\n          <span class='tooltip-id'>ID：").concat(t.item.getID(),"</span>\n        ")},shouldBegin:function(t){return!0},itemTypes:["node","edge","combo"],trigger:"mouseenter",fixToNode:void 0}},e.prototype.getEvents=function(){return"click"===this.get("trigger")?{"node:click":"onClick","edge:click":"onClick","combo:click":"onClick","canvas:click":"onMouseLeave",afterremoveitem:"onMouseLeave",contextmenu:"onMouseLeave",drag:"onMouseLeave"}:{"node:mouseenter":"onMouseEnter","node:mouseleave":"onMouseLeave","node:mousemove":"onMouseMove","edge:mouseenter":"onMouseEnter","edge:mouseleave":"onMouseLeave","edge:mousemove":"onMouseMove","combo:mouseenter":"onMouseEnter","combo:mouseleave":"onMouseLeave","combo:mousemove":"onMouseMove",afterremoveitem:"onMouseLeave",contextmenu:"onMouseLeave","node:drag":"onMouseLeave"}},e.prototype.init=function(){var t=this,e=t.get("className")||"g6-component-tooltip",i=Object(n["b"])("<div class='".concat(e,"'></div>")),o=t.get("container");o||(o=t.get("graph").get("container")),Object(r["isString"])(o)&&(o=document.getElementById(o)),Object(n["c"])(i,{position:"absolute",visibility:"hidden",display:"none"}),o.appendChild(i),"click"!==t.get("trigger")&&(i.addEventListener("mouseenter",(function(t){Object(n["c"])(i,{visibility:"visible",display:"unset"})})),i.addEventListener("mouseleave",(function(e){t.hideTooltip()}))),t.set("tooltip",i)},e.prototype.onClick=function(t){var e=this.get("itemTypes");if(!t.item||!t.item.getType||-1!==e.indexOf(t.item.getType())){var i=t.item,n=this.get("graph");this.currentTarget===i?(this.currentTarget=null,this.hideTooltip(),n.emit("tooltipchange",{item:t.item,action:"hide"})):(this.currentTarget=i,this.showTooltip(t),n.emit("tooltipchange",{item:t.item,action:"show"}))}},e.prototype.onMouseEnter=function(t){var e=this.get("itemTypes");if(!t.item||!t.item.getType||-1!==e.indexOf(t.item.getType())){var i=t.item,n=this.get("graph");this.currentTarget=i,this.showTooltip(t),n.emit("tooltipchange",{item:t.item,action:"show"})}},e.prototype.onMouseMove=function(t){var e=this.get("itemTypes");t.item&&t.item.getType&&-1===e.indexOf(t.item.getType())||this.currentTarget&&t.item===this.currentTarget&&this.showTooltip(t)},e.prototype.onMouseLeave=function(){this.hideTooltip();var t=this.get("graph");t.emit("tooltipchange",{item:this.currentTarget,action:"hide"}),this.currentTarget=null},e.prototype.clearContainer=function(){var t=this.get("tooltip");t&&(t.innerHTML="")},e.prototype.showTooltip=function(t){if(t.item){var e=this.get("itemTypes");if(!t.item.getType||-1!==e.indexOf(t.item.getType())){var i=this.get("tooltip"),n=this.get("getContent"),o=n(t);Object(r["isString"])(o)?i.innerHTML=o:(this.clearContainer(),i.appendChild(o)),this.updatePosition(t)}}},e.prototype.hideTooltip=function(){var t=this.get("tooltip");t&&Object(n["c"])(t,{visibility:"hidden",display:"none"})},e.prototype.updatePosition=function(t){var e=this.get("shouldBegin"),i=this.get("tooltip");if(e(t)){var o=this.get("graph"),a=o.get("width"),s=o.get("height"),c=this.get("offsetX")||0,h=this.get("offsetY")||0,l=o.getPointByClient(t.clientX,t.clientY),d=this.get("fixToNode"),p=t.item;if(p.getType&&"node"===p.getType()&&d&&Object(r["isArray"])(d)&&d.length>=2){var g=p.getBBox();l={x:g.minX+g.width*d[0],y:g.minY+g.height*d[1]}}var u=o.getCanvasByPoint(l.x,l.y),f=u.x,y=u.y,v=o.getContainer(),m={x:f+v.offsetLeft+c,y:y+v.offsetTop+h};Object(n["c"])(i,{visibility:"visible",display:"unset"});var x=i.getBoundingClientRect();f+x.width+c>a&&(m.x-=x.width+c),y+x.height+h>s&&(m.y-=x.height+h),Object(n["c"])(i,{left:"".concat(m.x,"px"),top:"".concat(m.y,"px")})}else Object(n["c"])(i,{visibility:"hidden",display:"none"})},e.prototype.hide=function(){this.onMouseLeave()},e.prototype.destroy=function(){var t=this.get("tooltip");if(t){var e=this.get("container");e||(e=this.get("graph").get("container")),Object(r["isString"])(e)&&(e=document.getElementById(e)),e.removeChild(t)}},e}(a),U=Z,q=i("2ef1"),K=i("f2fb"),J=function(t,e,i){if(i||2===arguments.length)for(var n,r=0,o=e.length;r<o;r++)!n&&r in e||(n||(n=Array.prototype.slice.call(e,0,r)),n[r]=e[r]);return t.concat(n||Array.prototype.slice.call(e))};function Q(t){return Object(r["map"])(t,(function(t,e){var i=0===e?"M":"L",n=t[0],r=t[1];return[i,n,r]}))}function $(t){return Q(t)}function tt(t){if(t.length<=2)return $(t);var e=[];Object(r["each"])(t,(function(t){Object(r["isEqual"])(t,e.slice(e.length-2))||e.push(t[0],t[1])}));var i=q["a"](e,!1),n=Object(r["head"])(t),o=n[0],a=n[1];return i.unshift(["M",o,a]),i}function et(t,e,i,n){void 0===n&&(n=!0);var o=new K["b"]({values:t}),a=new K["a"]({values:Object(r["map"])(t,(function(t,e){return e}))}),s=Object(r["map"])(t,(function(t,n){return[a.scale(n)*e,i-o.scale(t)*i]}));return n?tt(s):$(s)}function it(t,e,i,n){void 0===n&&(n=5);for(var o=new K["b"]({values:t}),a=new K["a"]({values:Object(r["map"])(t,(function(t,e){return e}))}),s=Object(r["map"])(t,(function(t,n){return[a.scale(n)*e,i-o.scale(t)*i]})),c=[],h=0;h<s.length;h++){var l=s[h],d={x:l[0],y:l[1],y0:i,size:n},p=ot(d);c.push.apply(c,p)}return at(c)}function nt(t,e){var i=new K["b"]({values:t}),n=Math.max(0,i.min);return e-i.scale(n)*e}function rt(t,e,i,n){var r=J([],t,!0),o=nt(n,i);return r.push(["L",e,o]),r.push(["L",0,o]),r.push(["Z"]),r}function ot(t){var e,i,n,o,a=t.x,s=t.y,c=t.y0,h=t.size;Object(r["isArray"])(s)?(e=s[0],i=s[1]):(e=c,i=s),Object(r["isArray"])(a)?(n=a[0],o=a[1]):(n=a-h/2,o=a+h/2);var l=[{x:n,y:e},{x:n,y:i}];return l.push({x:o,y:i},{x:o,y:e}),l}function at(t,e){void 0===e&&(e=!0);var i=[],n=t[0];i.push(["M",n.x,n.y]);for(var r=1,o=t.length;r<o;r++)i.push(["L",t[r].x,t[r].y]);return e&&(i.push(["L",n.x,n.y]),i.push(["z"])),i}var st=function(){return st=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++)for(var r in e=arguments[i],e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t},st.apply(this,arguments)},ct={stroke:"#C5C5C5",strokeOpacity:.85},ht={fill:"#CACED4",opacity:.85},lt=function(){function t(t){var e=t.x,i=void 0===e?0:e,n=t.y,r=void 0===n?0:n,o=t.width,a=void 0===o?200:o,s=t.height,c=void 0===s?26:s,h=t.smooth,l=void 0===h||h,d=t.isArea,p=void 0!==d&&d,g=t.data,u=void 0===g?[]:g,f=t.lineStyle,y=t.areaStyle,v=t.group,m=t.interval,x=void 0===m?null:m;this.group=v,this.x=i,this.y=r,this.width=a,this.height=c,this.data=u,this.smooth=l,this.isArea=p,this.lineStyle=Object.assign({},ct,f),this.areaStyle=Object.assign({},ht,y),this.intervalConfig=x,this.renderLine()}return t.prototype.renderLine=function(){var t=this,e=t.x,i=t.y,n=t.width,r=t.height,o=(t.barWidth,t.data),a=t.smooth,s=t.isArea,c=t.lineStyle,h=t.areaStyle,l=this.group.addGroup({name:"trend-group"});if(o){var d=et(o,n,r,a);if(l.addShape("path",{attrs:st({path:d},c),name:"trend-line"}),s){var p=rt(d,n,r,o);l.addShape("path",{attrs:st({path:p},h),name:"trend-area"})}}this.intervalConfig&&l.addShape("path",{attrs:st({path:it(this.intervalConfig.data,n,r,this.intervalConfig.style.barWidth)},this.intervalConfig.style),name:"trend-interval"}),l.move(e,i)},t.prototype.destory=function(){this.group.destroy()},t}(),dt=lt,pt=function(){return pt=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++)for(var r in e=arguments[i],e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t},pt.apply(this,arguments)},gt={fill:"#1890ff",stroke:"#1890ff",type:"trend",radius:2,opacity:1,cursor:"ew-resize",highLightFill:"#0050b3"},ut={fill:"#fff",stroke:"#1890ff",radius:2,opacity:1,cursor:"ew-resize"},ft=function(){function t(t){var e=t.group,i=t.name,n=t.type,r=t.x,o=void 0===r?0:r,a=t.y,s=void 0===a?0:a,c=t.width,h=void 0===c?2:c,l=t.height,d=void 0===l?24:l,p=t.style,g=void 0===p?{}:p;this.group=e,this.name=i,this.handleType=n,this.x=o,this.y=s,this.width=h,this.height=d,"trend"===n?this.style=pt(pt({},gt),g):"simple"===n&&(this.style=pt(pt({},ut),g)),this.renderHandle()}return t.prototype.setX=function(t){this.setXY(t,void 0)},t.prototype.setY=function(t){this.setXY(void 0,t)},t.prototype.setXY=function(t,e){Object(r["isNumber"])(t)&&(this.x=t),Object(r["isNumber"])(e)&&(this.y=e),this.updateXY()},t.prototype.renderHandle=function(){var t=this,e=t.width,i=t.height,n=t.style,r=t.name,o=n.fill,a=n.stroke,s=n.radius,c=n.opacity,h=n.cursor;this.handleGroup=this.group.addGroup(),"trend"===this.handleType?(this.verticalLine=this.handleGroup.addShape("rect",{attrs:{x:0,y:0,width:e,height:i,fill:o,stroke:a,radius:s,opacity:c,cursor:h},name:"".concat(r,"-handler")}),this.topCircle=this.handleGroup.addShape("circle",{attrs:{x:e/2,y:0,r:2*e,fill:o,stroke:a,radius:s,opacity:c,cursor:h,lineAppendWidth:12},name:"".concat(r,"-handler")}),this.bottomCircle=this.handleGroup.addShape("circle",{attrs:{x:e/2,y:i,r:2*e,fill:o,stroke:a,radius:s,opacity:c,cursor:h},name:"".concat(r,"-handler")})):"simple"===this.handleType&&(this.topCircle=this.handleGroup.addShape("circle",{attrs:{x:e/2,y:i/2,r:2*e,fill:o,stroke:a,radius:s,opacity:c,cursor:h,lineWidth:2},name:"".concat(r,"-handler")})),this.updateXY(),"trend"===this.handleType?this.bindTrendEvents():"simple"===this.handleType&&this.bindSimpleEvents()},t.prototype.bindSimpleEvents=function(){var t=this,e=this.name;this.handleGroup.on("".concat(e,"-handler:mouseenter"),(function(){var e=t.style.highLightFill;t.topCircle.attr("fill",e)})),this.handleGroup.on("".concat(e,"-handler:mouseleave"),(function(){var e=t.style.fill;t.topCircle.attr("fill",e)}))},t.prototype.bindTrendEvents=function(){var t=this,e=this.name;this.handleGroup.on("".concat(e,"-handler:mouseenter"),(function(){var e=t.style.highLightFill;t.verticalLine.attr("fill",e),t.topCircle.attr("fill",e),t.bottomCircle.attr("fill",e)})),this.handleGroup.on("".concat(e,"-handler:mouseleave"),(function(){var e=t.style.fill;t.verticalLine.attr("fill",e),t.topCircle.attr("fill",e),t.bottomCircle.attr("fill",e)}))},t.prototype.show=function(){this.handleGroup.show()},t.prototype.hide=function(){this.handleGroup.hide()},t.prototype.updateXY=function(){this.handleGroup.setMatrix([1,0,0,0,1,0,this.x,this.y,1])},t}(),yt=ft,vt="timebarstartplay",mt="timebarendplay",xt="valuechange",bt="timebarConfigChanged",wt="playPauseBtn",St="nextStepBtn",kt="preStepBtn",Ot=function(){return Ot=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++)for(var r in e=arguments[i],e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t},Ot.apply(this,arguments)},Ct=function(){function t(t){this.config=Object(r["deepMix"])({},t),this.init()}return t.prototype.update=function(t){this.config=Object(r["deepMix"])({},this.config,t),this.updateElement(),this.renderMarker()},t.prototype.init=function(){this.initElement(),this.renderMarker()},t.prototype.initElement=function(){var t=this.config,e=t.group,i=t.style,n=i.scale,r=void 0===n?1:n,o=i.offsetX,a=void 0===o?0:o,s=i.offsetY,c=void 0===s?0:s,h=this.config.x+a,l=this.config.y+c,d=e.addGroup({name:wt});this.startMarkerGroup=d.addGroup({name:wt}),this.circle=e.addShape("circle",{attrs:Ot({x:h,y:l,r:this.config.r*r},i),name:wt}),this.startMarker=this.startMarkerGroup.addShape("path",{attrs:{path:this.getStartMarkerPath(h,l,r),fill:i.stroke||"#aaa"},name:"start-marker"}),this.pauseMarkerGroup=d.addGroup({name:wt});var p=.25*this.config.r*r,g=.5*this.config.r*Math.sqrt(3)*r;this.pauseLeftMarker=this.pauseMarkerGroup.addShape("rect",{attrs:{x:h-.375*this.config.r*r,y:l-g/2,width:p,height:g,fill:i.stroke||"#aaa",lineWidth:0}}),this.pauseRightMarker=this.pauseMarkerGroup.addShape("rect",{attrs:{x:h+1/8*this.config.r*r,y:l-g/2,width:p,height:g,fill:i.stroke||"#aaa",lineWidth:0}})},t.prototype.updateElement=function(){var t=this.config.style,e=t.scale,i=void 0===e?1:e,n=t.offsetX,r=void 0===n?0:n,o=t.offsetY,a=void 0===o?0:o,s=this.config.x+r,c=this.config.y+a;this.circle.attr("x",s),this.circle.attr("y",c),this.circle.attr("r",this.config.r*i),this.startMarker.attr("path",this.getStartMarkerPath(s,c,i));var h=.25*this.config.r*i,l=.5*this.config.r*Math.sqrt(3)*i;this.pauseLeftMarker.attr("x",s-.375*this.config.r*i),this.pauseLeftMarker.attr("y",c-l/2),this.pauseLeftMarker.attr("width",h),this.pauseLeftMarker.attr("height",l),this.pauseRightMarker.attr("x",s+1/8*this.config.r*i),this.pauseRightMarker.attr("y",c-l/2),this.pauseRightMarker.attr("width",h),this.pauseRightMarker.attr("height",l)},t.prototype.renderMarker=function(){this.config.isPlay?(this.startMarkerGroup.hide(),this.pauseMarkerGroup.show()):(this.startMarkerGroup.show(),this.pauseMarkerGroup.hide())},t.prototype.getStartMarkerPath=function(t,e,i){var n=.5*this.config.r*Math.sqrt(3)*i;return[["M",t-n/Math.sqrt(3)/2,e-n/2],["L",t+n/Math.sqrt(3),e],["L",t-n/Math.sqrt(3)/2,e+n/2]]},t}(),Mt=Ct,Tt=function(){return Tt=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++)for(var r in e=arguments[i],e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t},Tt.apply(this,arguments)},Et=m["a"].transform,jt="#aaa",Bt="green",Lt={fill:"#aaa",fillOpacity:.35,stroke:"#aaa"},Pt={fill:"#fff"},It={fill:"green"},At={pointer:{fill:"#aaa",lineWidth:0},scroller:{stroke:"#aaa",fill:"#aaa",lineWidth:1,lineAppendWidth:5,cursor:"pointer"},text:{fill:"#aaa",textBaseline:"top"}},Dt={check:{stroke:"green",lineWidth:3},box:{fill:"#fff",stroke:"#aaa",lineWidth:2,radius:3,width:12,height:12},text:{fill:"#aaa",fontSize:12,textBaseline:"top"}},_t={speed:1,loop:!1,fill:"#fff",stroke:"#fff",hideTimeTypeController:!1,preBtnStyle:{fill:"#aaa",stroke:"#aaa"},nextBtnStyle:{fill:"#aaa",stroke:"#aaa"},playBtnStyle:{fill:"#aaa",stroke:"#aaa",fillOpacity:.05},speedControllerStyle:At,timeTypeControllerStyle:Dt},Gt=110,Yt=50,Rt=function(){function t(t){this.controllerCfg=Object(r["deepMix"])({},_t,t),this.group=t.group,this.controllerGroup=this.group.addGroup({name:"controller-group"}),this.speedAxisY=[],this.currentSpeed=this.controllerCfg.speed,this.currentType="range",this.fontFamily=t.fontFamily||"Arial, sans-serif",this.init()}return t.prototype.init=function(){this.renderPlayButton()},t.prototype.getNextMarkerPath=function(t,e,i){return[["M",t,e-i],["L",t+i,e],["L",t,e+i],["Z",t,e-i],["M",t,e],["L",t-i,e-i],["L",t-i,e+i],["Z"]]},t.prototype.getPreMarkerPath=function(t,e,i){return[["M",t,e-i],["L",t-i,e],["L",t,e+i],["L",t,e-i],["M",t,e],["L",t+i,e-i],["L",t+i,e+i],["Z"]]},t.prototype.renderPlayButton=function(){var t=this.controllerCfg,e=t.width,i=t.height,n=t.x,r=t.y,o=t.hideTimeTypeController,a=t.fill,s=void 0===a?jt:a,c=t.stroke,h=void 0===c?Bt:c,l=t.containerStyle,d=void 0===l?{}:l,p=Tt(Tt({},Lt),t.playBtnStyle||{}),g=Tt(Tt({},Pt),t.preBtnStyle||{}),u=Tt(Tt({},It),t.nextBtnStyle||{}),f=i/2-5,y=r+10,v=this.controllerGroup.addShape("rect",{attrs:Tt({x:n,y:y,width:e,height:i,stroke:h,fill:s},d),name:"container-rect"});this.playButton?this.playButton.update({x:e/2,y:y,r:f}):this.playButton=new Mt({group:this.controllerGroup,x:e/2,y:y+f+5,r:f,isPlay:this.isPlay,style:p});var m=g.offsetX||0,x=g.offsetY||0,b=(g.scale||1)*f;this.controllerGroup.addShape("path",{attrs:Tt({path:this.getPreMarkerPath(e/2-5*f+m,y+f+5+x,.5*b)},g),name:kt});var w=u.offsetX||0,S=u.offsetY||0,k=(u.scale||1)*f;this.controllerGroup.addShape("path",{attrs:Tt({path:this.getNextMarkerPath(e/2+5*f+w,y+f+5+S,.5*k)},u),name:St}),v.toBack(),this.renderSpeedBtn(),o||this.renderToggleTime(),this.bindEvent();var O=this.controllerCfg.scale,C=void 0===O?1:O,M=this.controllerGroup.getCanvasBBox(),T=(M.maxX+M.minX)/2,E=(M.maxY+M.minY)/2,j=Et([1,0,0,0,1,0,0,0,1],[["t",-T,-E],["s",C,C],["t",T,E]]);this.controllerGroup.setMatrix(j)},t.prototype.renderSpeedBtn=function(){var t=this.controllerCfg,e=t.y,i=t.width,n=t.hideTimeTypeController,r=Tt(Tt({},At),this.controllerCfg.speedControllerStyle||{}),o=r.scroller,a=void 0===o?{}:o,s=r.text,c=void 0===s?{}:s,h=r.pointer,l=void 0===h?{}:h,d=r.scale,p=void 0===d?1:d,g=r.offsetX,u=void 0===g?0:g,f=r.offsetY,y=void 0===f?0:f,v=this.controllerGroup.addGroup({name:"speed-group"});this.speedGroup=v;var m=[],x=5;this.speedAxisY=[19,22,26,32,39];for(var b=0;b<5;b++){var w=e+this.speedAxisY[b],S=i-(n?Yt:Gt);v.addShape("line",{attrs:Tt({x1:S,x2:S+15,y1:w,y2:w},a),speed:x,name:"speed-rect"}),this.speedAxisY[b]=w,m.push(x),x-=1}this.speedText=v.addShape("text",{attrs:Tt({x:i-(n?Yt:Gt)+20,y:this.speedAxisY[0]+4,text:"1.0X",fontFamily:this.fontFamily||"Arial, sans-serif"},c),name:"speed-text"}),this.speedPoint=v.addShape("path",{attrs:Tt({path:this.getPointerPath(i-(n?Yt:Gt),0),matrix:[1,0,0,0,1,0,0,this.speedAxisY[4],1]},l),name:"speed-pointer"});var k=this.speedGroup.getCanvasBBox(),O=(k.maxX+k.minX)/2,C=(k.maxY+k.minY)/2,M=this.speedGroup.getMatrix()||[1,0,0,0,1,0,0,0,1];M=Et(M,[["t",-O,-C],["s",p,p],["t",O+u*p,C+y*p]]),this.speedGroup.setMatrix(M)},t.prototype.getPointerPath=function(t,e){return[["M",t,e],["L",t-10,e-4],["L",t-10,e+4],["Z"]]},t.prototype.renderToggleTime=function(){var t,e=this.controllerCfg.width,i=Tt(Tt({},Dt),this.controllerCfg.timeTypeControllerStyle||{}),n=i.scale,r=void 0===n?1:n,o=i.offsetX,a=void 0===o?0:o,s=i.offsetY,c=void 0===s?0:s,h=i.box,l=void 0===h?{}:h,d=i.check,p=void 0===d?{}:d,g=i.text,u=void 0===g?{}:g;this.toggleGroup=this.controllerGroup.addGroup({name:"toggle-group"}),this.toggleGroup.addShape("rect",{attrs:Tt({x:e-Yt,y:this.speedAxisY[0]+3.5},l),isChecked:!1,name:"toggle-model"}),this.checkedIcon=this.toggleGroup.addShape("path",{attrs:Tt({path:[["M",e-Yt+3,this.speedAxisY[1]+6],["L",e-Yt+7,this.speedAxisY[1]+10],["L",e-Yt+12,this.speedAxisY[1]+4]]},p),capture:!1,name:"check-icon"}),this.checkedIcon.hide(),this.checkedText=this.toggleGroup.addShape("text",{attrs:Tt({text:(null===(t=this.controllerCfg)||void 0===t?void 0:t.timePointControllerText)||"单一时间",x:e-Yt+15,y:this.speedAxisY[0]+4,fontFamily:"undefined"!==typeof window&&window.getComputedStyle(document.body,null).getPropertyValue("font-family")||"Arial, sans-serif"},u),name:"checked-text"});var f=this.toggleGroup.getCanvasBBox(),y=(f.maxX+f.minX)/2,v=(f.maxY+f.minY)/2,m=this.toggleGroup.getMatrix()||[1,0,0,0,1,0,0,0,1];m=Et(m,[["t",-y,-v],["s",r,r],["t",y+a*r,v+c*r]]),this.toggleGroup.setMatrix(m)},t.prototype.bindEvent=function(){var t=this;this.speedGroup.on("speed-rect:click",(function(e){var i=e.target.attr("y1"),n=t.speedPoint.attr("matrix"),r=t.speedAxisY.indexOf(n[7]||0),o=t.speedAxisY.indexOf(i),a=t.speedAxisY[o]-t.speedAxisY[r];n=Et(n,[["t",0,a]]),t.speedPoint.setMatrix(n),t.currentSpeed=t.speedAxisY.length-o,t.speedText.attr("text","".concat(t.currentSpeed,".0X")),t.group.emit(bt,{speed:t.currentSpeed,type:t.currentType})})),this.speedGroup.on("mousewheel",(function(e){e.preventDefault();var i=t.speedPoint.attr("matrix")||[1,0,0,0,1,0,0,0,1],n=i[7],r=t.speedAxisY.indexOf(n);if(-1===r){var o=1/0;t.speedAxisY.forEach((function(t,e){var i=Math.abs(t-n);o>i&&(o=i,r=e)}))}r=e.originalEvent.deltaY>0?Math.max(0,r-1):Math.min(t.speedAxisY.length-1,r+1);var a=t.speedAxisY[r]-n;i=Et(i,[["t",0,a]]),t.speedPoint.setMatrix(i),t.currentSpeed=t.speedAxisY.length-r,t.speedText.attr("text","".concat(t.currentSpeed,".0X")),t.group.emit(bt,{speed:t.currentSpeed,type:t.currentType})})),this.toggleGroup&&this.toggleGroup.on("toggle-model:click",(function(e){var i,n,r=e.target.get("isChecked");r?(t.checkedIcon.hide(),t.checkedText.attr("text",(null===(n=t.controllerCfg)||void 0===n?void 0:n.timePointControllerText)||"单一时间"),t.currentType="range"):(t.checkedIcon.show(),t.checkedText.attr("text",(null===(i=t.controllerCfg)||void 0===i?void 0:i.timeRangeControllerText)||"时间范围"),t.currentType="single"),e.target.set("isChecked",!r),t.group.emit(bt,{type:t.currentType,speed:t.currentSpeed})}))},t.prototype.destroy=function(){this.speedGroup.off("speed-rect:click"),this.toggleGroup&&(this.toggleGroup.off("toggle-model:click"),this.toggleGroup.destroy()),this.speedGroup.destroy()},t}(),Xt=Rt,zt=function(){return zt=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++)for(var r in e=arguments[i],e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t},zt.apply(this,arguments)},Ht=m["a"].transform,Nt={fill:"#416180",opacity:.05},Ft={fill:"#416180",opacity:.15,radius:5},Wt={fill:"#5B8FF9",opacity:.3,cursor:"grab"},Vt=2,Zt={width:Vt,height:24},Ut={textBaseline:"middle",fill:"#000",opacity:.45},qt={textAlign:"center",textBaseline:"top",fill:"#607889",opacity:.35},Kt={lineWidth:1,stroke:"#ccc"},Jt=function(){function t(t){var e=this;this.prevX=0,this.onMouseDown=function(t){return function(i){e.currentHandler=t;var n=i.originalEvent;n.stopPropagation(),n.preventDefault(),e.prevX=Object(r["get"])(n,"touches.0.pageX",n.pageX);var o=e.canvas.get("container");o.addEventListener("mousemove",e.onMouseMove),o.addEventListener("mouseup",e.onMouseUp),o.addEventListener("mouseleave",e.onMouseUp),o.addEventListener("touchmove",e.onMouseMove),o.addEventListener("touchend",e.onMouseUp),o.addEventListener("touchcancel",e.onMouseUp)}},this.onMouseMove=function(t){t.stopPropagation(),t.preventDefault();var i=Object(r["get"])(t,"touches.0.pageX",t.pageX),n=i-e.prevX,o=e.adjustOffsetRange(n/e.width);e.updateStartEnd(o),e.updateUI(),e.prevX=i},this.onMouseUp=function(){e.currentHandler&&(e.currentHandler=void 0);var t=e.canvas.get("container");t&&(t.removeEventListener("mousemove",e.onMouseMove),t.removeEventListener("mouseup",e.onMouseUp),t.removeEventListener("mouseleave",e.onMouseUp),t.removeEventListener("touchmove",e.onMouseMove),t.removeEventListener("touchend",e.onMouseUp),t.removeEventListener("touchcancel",e.onMouseUp))};var i=t.x,n=void 0===i?0:i,o=t.y,a=void 0===o?0:o,s=t.width,c=void 0===s?100:s,h=t.height,l=t.padding,d=void 0===l?10:l,p=t.trendCfg,g=t.controllerCfg,u=void 0===g?{speed:1}:g,f=t.backgroundStyle,y=void 0===f?{}:f,v=t.foregroundStyle,m=void 0===v?{}:v,x=t.handlerStyle,b=void 0===x?{}:x,w=t.textStyle,S=void 0===w?{}:w,k=t.start,O=void 0===k?0:k,C=t.end,M=void 0===C?1:C,T=t.minText,E=void 0===T?"":T,j=t.maxText,B=void 0===j?"":j,L=t.group,P=t.graph,I=t.canvas,A=t.tick,D=void 0===A?{tickLabelStyle:{},tickLineStyle:{},tickLabelFormatter:function(t){return t},ticks:[]}:A,_=t.type;this.graph=P,this.canvas=I,this.group=L,this.timeBarType=_,this.x=n,this.y=a,this.width=c,this.height=h,this.padding=d,this.ticks=D.ticks,this.trendCfg=p,this.controllerCfg=u,this.currentSpeed=u.speed||1,this.tickLabelFormatter=D.tickLabelFormatter,"trend"===_?this.backgroundStyle=zt(zt({},Nt),y):"simple"===_&&(this.backgroundStyle=zt(zt({},Ft),y)),this.foregroundStyle=zt(zt({},Wt),m),this.handlerStyle=zt(zt({},Zt),b),this.textStyle=zt(zt({},Ut),S),this.tickLabelStyle=zt(zt({},qt),D.tickLabelStyle),this.tickLineStyle=zt(zt({},Kt),D.tickLineStyle),this.currentMode="range",this.start=O,this.end=M,this.minText=E,this.maxText=B,this.fontFamily="undefined"!==typeof window&&window.getComputedStyle(document.body,null).getPropertyValue("font-family")||"Arial, sans-serif",this.renderSlider()}return t.prototype.update=function(t){var e=t.x,i=t.y,n=t.width,o=t.height,a=t.minText,s=t.maxText,c=t.start,h=t.end;this.start=Math.min(1,Math.max(c,0)),this.end=Math.min(1,Math.max(h,0)),Object(r["assign"])(this,{x:e,y:i,width:n,height:o,minText:a,maxText:s}),this.updateUI()},t.prototype.setText=function(t,e){this.minTextShape.attr("text",t),this.maxTextShape.attr("text",e)},t.prototype.renderSlider=function(){var t=this,e=this,i=e.width,n=e.height,o=e.timeBarType;if("trend"===o&&Object(r["size"])(Object(r["get"])(this.trendCfg,"data"))){var a=new dt(zt(zt({x:this.x,y:this.y,width:i,height:n},this.trendCfg),{group:this.group}));this.trendComponent=a}var s=this.group.addGroup({name:"slider-group"});s.addShape("rect",{attrs:zt({x:0,y:0,width:i,height:n},this.backgroundStyle),name:"background"});var c=this.group.addGroup();"trend"===o?(this.minTextShape=c.addShape("text",{attrs:zt({x:0,y:n/2+this.y,textAlign:"right",text:this.minText,silent:!1,fontFamily:this.fontFamily||"Arial, sans-serif",stroke:"#fff",lineWidth:5},this.textStyle),capture:!1,name:"min-text-shape"}),this.maxTextShape=c.addShape("text",{attrs:zt({y:n/2+this.y,textAlign:"left",text:this.maxText,silent:!1,fontFamily:this.fontFamily||"Arial, sans-serif",stroke:"#fff",lineWidth:5},this.textStyle),capture:!1,name:"max-text-shape"})):(this.minTextShape=c.addShape("text",{attrs:zt({x:0,y:this.y-10,textAlign:"center",text:this.minText,silent:!1,fontFamily:this.fontFamily||"Arial, sans-serif",stroke:"#fff",lineWidth:5},this.textStyle),capture:!1,name:"min-text-shape"}),this.maxTextShape=c.addShape("text",{attrs:zt({y:this.y-10,textAlign:"center",text:this.maxText,silent:!1,fontFamily:this.fontFamily||"Arial, sans-serif",stroke:"#fff",lineWidth:5},this.textStyle),capture:!1,name:"max-text-shape"})),this.foregroundShape=this.group.addGroup().addShape("rect",{attrs:zt({x:0,y:this.y,height:n},this.foregroundStyle),name:"foreground-shape"}),this.foregroundShape.on("mousedown",(function(t){t.target.attr("cursor","grabbing")})),this.foregroundShape.on("mouseup",(function(e){e.target.attr("cursor",t.foregroundStyle.cursor||"grab")}));var h=Object(r["get"])(this.handlerStyle,"width",2),l=Object(r["get"])(this.handlerStyle,"height",24),d=this.group.addGroup({name:"minHandlerShape"});this.minHandlerShape=new yt({name:"minHandlerShape",group:d,type:o,x:this.x,y:this.y,width:h,height:l,style:this.handlerStyle});var p=this.group.addGroup({name:"maxHandlerShape"});this.maxHandlerShape=new yt({name:"maxHandlerShape",group:p,type:o,x:this.x,y:this.y,width:h,height:l,style:this.handlerStyle});var g=this.ticks,u=i/(g.length-1);this.tickPosList=[],this.textList&&this.textList.length&&this.textList.forEach((function(t){t.destroy()}));var f=-1/0,y=this.tickLabelStyle.rotate;delete this.tickLabelStyle.rotate,this.textList=g.map((function(e,i){var o;t.tickPosList.push(t.x+i*u),t.tickLabelFormatter?(o=t.tickLabelFormatter(e),!Object(r["isString"])(o)&&o&&(o=e.date)):o=e.date;var a=t.x+i*u,s=t.y+n+5,c=t.group.addShape("text",{attrs:zt({x:a,y:s,text:o,fontFamily:t.fontFamily||"Arial, sans-serif"},t.tickLabelStyle),name:"tick-label"});if(Object(r["isNumber"])(y)&&i!==g.length-1){var h=Ht([1,0,0,0,1,0,0,0,1],[["t",-a,-s],["r",y],["t",a-5,s+2]]);c.attr({textAlign:"left",matrix:h})}0===i?c.attr({textAlign:"left"}):i!==g.length-1&&c.attr({textAlign:"right"});var l=t.group.addShape("line",{attrs:zt({x1:t.x+i*u,y1:t.y+n+2,x2:t.x+i*u,y2:t.y+n+6},t.tickLineStyle),name:"tick-line"});l.toBack();var d=c.getBBox();return d.minX>f?(c.show(),l.show(),f=d.minX+d.width+10):(c.hide(),l.hide()),c})),this.controllerBtnGroup=new Xt(zt({group:this.group,x:this.x,y:this.y+n+25,width:i,height:35},this.controllerCfg)),this.updateStartEnd(0),this.updateUI(),s.move(this.x,this.y),this.bindEvents()},t.prototype.bindEvents=function(){var t=this,e=this.group.find((function(t){return"minHandlerShape"===t.get("name")}));e&&(e.on("minHandlerShape-handler:mousedown",this.onMouseDown(this.minHandlerShape)),e.on("minHandlerShape-handler:touchstart",this.onMouseDown(this.minHandlerShape)));var i=this.group.find((function(t){return"maxHandlerShape"===t.get("name")}));i&&(i.on("maxHandlerShape-handler:mousedown",this.onMouseDown(this.maxHandlerShape)),i.on("maxHandlerShape-handler:touchstart",this.onMouseDown(this.maxHandlerShape))),this.foregroundShape.on("mousedown",this.onMouseDown(this.foregroundShape)),this.foregroundShape.on("touchstart",this.onMouseDown(this.foregroundShape)),this.group.on("".concat(wt,":click"),(function(){t.isPlay=!t.isPlay,t.currentHandler=t.maxHandlerShape,t.changePlayStatus()})),this.group.on("".concat(St,":click"),(function(){t.currentHandler=t.maxHandlerShape,t.updateStartEnd(.01),t.updateUI()})),this.group.on("".concat(kt,":click"),(function(){t.currentHandler=t.maxHandlerShape,t.updateStartEnd(-.01),t.updateUI()})),this.group.on(bt,(function(e){var i=e.type,n=e.speed;t.currentSpeed=n,t.currentMode=i,"single"===i?(t.minHandlerShape.hide(),t.foregroundShape.hide(),t.minTextShape.hide()):"range"===i&&(t.minHandlerShape.show(),t.foregroundShape.show(),t.minTextShape.show())}))},t.prototype.adjustTickIndex=function(t){for(var e=0;e<this.tickPosList.length-1;e++)if(this.tickPosList[e]<=t&&t<=this.tickPosList[e+1])return Math.abs(this.tickPosList[e]-t)<Math.abs(t-this.tickPosList[e+1])?e:e+1;return 0},t.prototype.adjustOffsetRange=function(t){switch(this.currentHandler){case this.minHandlerShape:var e=0-this.start,i=1-this.start;return Math.min(i,Math.max(e,t));case this.maxHandlerShape:e=0-this.end,i=1-this.end;return Math.min(i,Math.max(e,t));case this.foregroundShape:e=0-this.start,i=1-this.end;return Math.min(i,Math.max(e,t));default:return 0}},t.prototype.updateStartEnd=function(t){var e=this.ticks[this.adjustTickIndex(this.start*this.width)],i=this.ticks[this.adjustTickIndex(this.end*this.width)];if(!this.currentHandler)return this.minText=this.tickLabelFormatter?this.tickLabelFormatter(e):null===e||void 0===e?void 0:e.date,void(this.maxText=this.tickLabelFormatter?this.tickLabelFormatter(i):null===i||void 0===i?void 0:i.date);switch(this.currentHandler){case this.minHandlerShape:this.maxText=this.maxTextShape.attr("text"),this.start+=t,this.minText=this.tickLabelFormatter?this.tickLabelFormatter(e):e.date;break;case this.maxHandlerShape:this.minText=this.minTextShape.attr("text"),this.end+=t,this.maxText=this.tickLabelFormatter?this.tickLabelFormatter(i):i.date;break;case this.foregroundShape:this.start+=t,this.end+=t,this.minText=this.tickLabelFormatter?this.tickLabelFormatter(e):e.date,this.maxText=this.tickLabelFormatter?this.tickLabelFormatter(i):i.date;break;default:break}},t.prototype.updateUI=function(){var t=this;this.start<0&&(this.start=0),this.end>1&&(this.end=1);var e=this.x+this.start*this.width,i=this.x+this.end*this.width;this.foregroundShape.attr("x",e),this.foregroundShape.attr("width",i-e);var n=Object(r["get"])(this.handlerStyle,"width",Vt);this.setText(this.minText,this.maxText);var o=this.dodgeText([e,i]),a=o[0],s=o[1];this.minHandlerShape.setX(e-n/2),Object(r["each"])(a,(function(e,i){return t.minTextShape.attr(i,e)})),this.maxHandlerShape.setX(i-n/2),Object(r["each"])(s,(function(e,i){return t.maxTextShape.attr(i,e)})),"range"===this.currentMode?this.graph.emit(xt,{value:[this.start,this.end].sort()}):"single"===this.currentMode&&this.graph.emit(xt,{value:[this.end,this.end]})},t.prototype.dodgeText=function(t){var e,i,n=2,o=Object(r["get"])(this.handlerStyle,"width",Vt),a=this.minTextShape,s=this.maxTextShape,c=t[0],h=t[1],l=!1;c>h&&(e=[h,c],c=e[0],h=e[1],i=[s,a],a=i[0],s=i[1],l=!0);var d=a.getBBox(),p=s.getBBox(),g=null,u=null;return"trend"===this.timeBarType?(g=c-d.width<this.x+n?{x:c+o/2+n,textAlign:"left"}:{x:c-o/2-n,textAlign:"right"},u=h+p.width>this.x+this.width?{x:h-o/2-n,textAlign:"right"}:{x:h+o/2+n,textAlign:"left"}):"simple"===this.timeBarType&&(g=a.attr("x")>d.width?{x:c,textAlign:"center"}:{x:c,textAlign:"left"},u=s.attr("x")>this.width-p.width?{x:h,textAlign:"right"}:{x:h,textAlign:"center"}),l?[u,g]:[g,u]},t.prototype.startPlay=function(){var t=this;return"undefined"!==typeof window?window.requestAnimationFrame((function(){var e=t,i=e.ticks,n=e.width,r=t.currentSpeed,o=n/i.length,a=o/(1e3*(10-r)/60),s=t.adjustOffsetRange(a/t.width);t.updateStartEnd(s),t.updateUI(),t.isPlay&&(t.playHandler=t.startPlay())})):void 0},t.prototype.changePlayStatus=function(t){void 0===t&&(t=!0),this.controllerBtnGroup.playButton.update({isPlay:this.isPlay}),this.isPlay?(this.playHandler=this.startPlay(),this.graph.emit(vt,null)):this.playHandler&&("undefined"!==typeof window&&window.cancelAnimationFrame(this.playHandler),t&&this.graph.emit(mt,null))},t.prototype.destory=function(){this.graph.off(xt,(function(){}));var t=this.group,e=t.find((function(t){return"minHandlerShape"===t.get("name")}));e&&(e.off("minHandlerShape-handler:mousedown"),e.off("minHandlerShape-handler:touchstart"),e.destroy());var i=t.find((function(t){return"maxHandlerShape"===t.get("name")}));i&&(i.off("maxHandlerShape-handler:mousedown"),i.off("maxHandlerShape-handler:touchstart"),i.destroy()),this.foregroundShape.off("mousedown"),this.foregroundShape.off("touchstart"),this.foregroundShape.destroy(),t.off("".concat(wt,":click")),t.off("".concat(St,":click")),t.off("".concat(kt,":click")),t.off(bt),t.destroy(),this.trendComponent&&this.trendComponent.destory()},t}(),Qt=Jt,$t=function(){function t(t){var e=t.x,i=void 0===e?0:e,n=t.y,r=void 0===n?0:n,o=t.container,a=t.text,s=t.padding,c=void 0===s?[4,4,4,4]:s,h=t.className,l=void 0===h?"g6-component-timebar-tooltip":h,d=t.backgroundColor,p=void 0===d?"#000":d,g=t.textColor,u=void 0===g?"#fff":g,f=t.opacity,y=void 0===f?.8:f,v=t.fontSize,m=void 0===v?12:v;this.container=o,this.className=l,this.backgroundColor=p,this.textColor=u,this.x=i,this.y=r,this.text=a,this.padding=c,this.opacity=y,this.fontSize=m,this.render()}return t.prototype.render=function(){var t=this,e=t.className,i=(t.x,t.y,t.backgroundColor),o=t.textColor,a=t.text,s=t.padding,c=t.opacity,h=t.fontSize,l=t.container,d=Object(n["b"])("<div class='".concat(e,"' style=\"position: absolute; width: fit-content; height: fit-content; opacity: ").concat(c,'"></div>'));Object(r["isString"])(l)&&(l=document.getElementById(l)),l.appendChild(d),t.parentHeight=l.offsetHeight,t.parentWidth=l.offsetWidth,Object(n["c"])(d,{visibility:"hidden",top:0,left:0});var p=Object(n["b"])("\n      <div style='position: absolute; white-space:nowrap; background-color: ".concat(i,"; font-size: ").concat(h,"px; border-radius: 4px; width: fit-content; height: fit-content; color: ").concat(o,"; padding: ").concat(s[0],"px ").concat(s[1],"px ").concat(s[2],"px ").concat(s[3],"px'></div>"));p.innerHTML=a,d.appendChild(p),t.backgroundDOM=p;var g=Object(n["b"])("<div style='position: absolute; width: 0px; height: 0px; border-left: 5px solid transparent; border-right: 5px solid transparent; border-top: 10px solid ".concat(i,"'></div>"));d.appendChild(g),t.arrowDOM=g,t.container=d},t.prototype.show=function(t){var e=this,i=t.text,r=t.x;t.y,t.clientX,t.clientY;e.backgroundDOM.innerHTML=i;var o=e.backgroundDOM.offsetWidth,a=e.backgroundDOM.offsetHeight,s=e.arrowDOM.offsetWidth,c=e.arrowDOM.offsetHeight;Object(n["c"])(e.container,{top:"".concat(-a-c,"px"),left:"".concat(r,"px"),visibility:"visible"}),Object(n["c"])(e.backgroundDOM,{marginLeft:"".concat(-o/2,"px")}),Object(n["c"])(e.arrowDOM,{marginLeft:"".concat(-s/2,"px"),top:"".concat(a,"px")});var h=r-o/2,l=r+o/2;h<0?Object(n["c"])(e.backgroundDOM,{marginLeft:"".concat(-o/2-h,"px")}):l>e.parentWidth&&Object(n["c"])(e.backgroundDOM,{marginLeft:"".concat(-o/2-l+e.parentWidth+12,"px")})},t.prototype.hide=function(){Object(n["c"])(this.container,{top:0,left:0,visibility:"hidden"})},t}(),te=$t,ee=function(){return ee=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++)for(var r in e=arguments[i],e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t},ee.apply(this,arguments)},ie=m["a"].transform,ne={fill:"#5B8FF9"},re={fill:"#e6e8e9"},oe=function(){function t(t){this.frameCount=0,this.fontFamily="Arial, sans-serif";var e=t.graph,i=t.canvas,n=t.group,r=t.width,o=t.height,a=t.padding,s=t.data,c=t.start,h=t.end,l=t.x,d=void 0===l?0:l,p=t.y,g=void 0===p?0:p,u=t.tickLabelFormatter,f=t.selectedTickStyle,y=void 0===f?ne:f,v=t.unselectedTickStyle,m=void 0===v?re:v,x=t.tooltipBackgroundColor,b=t.tooltipFomatter,w=t.tickLabelStyle;this.graph=e,this.group=n,this.sliceGroup=n.addGroup({name:"slice-group"}),this.canvas=i,this.width=r,this.height=o,this.padding=a,this.data=s,this.start=c,this.end=h,this.tickLabelFormatter=u,this.tickLabelStyle=w||{},this.selectedTickStyle=y,this.unselectedTickStyle=m,this.x=d,this.y=g,this.tooltipBackgroundColor=x,this.tooltipFomatter=b,this.fontFamily="undefined"!==typeof window&&window.getComputedStyle(document.body,null).getPropertyValue("font-family")||"Arial, sans-serif",this.renderSlices(),this.initEvent()}return t.prototype.renderSlices=function(){var t=this,e=this,i=e.width,n=e.height,o=e.padding,a=e.data,s=e.start,c=e.end,h=e.tickLabelFormatter,l=e.selectedTickStyle,d=e.unselectedTickStyle,p=e.tickLabelStyle,g=i-2*o,u=10,f=4,y=3*o+f+u,v=n-y-2*o,m=2,x=a.length,b=(g-m*(x-1))/x;this.tickWidth=b;var w=this.sliceGroup,S=[],k=[],O=Math.round(x*s),C=Math.round(x*c);this.startTickRectId=O,this.endTickRectId=C;var M=p.rotate;delete p.rotate,a.forEach((function(e,n){var a=n>=O&&n<=C,s=a?l:d,c=w.addShape("rect",{attrs:ee({x:o+n*(b+m),y:o,width:b,height:v},s),draggable:!0,name:"tick-rect-".concat(n)}),g=w.addShape("rect",{attrs:{x:o+n*b+m*(2*n-1)/2,y:o,width:0===n||n===x-1?b+m/2:b+m,height:v,fill:"#fff",opacity:0},draggable:!0,name:"pick-rect-".concat(n)});g.toFront();var u,y=c.getBBox(),T=(y.minX+y.maxX)/2;if(S.push({rect:c,pickRect:g,value:e.date,x:T,y:y.minY}),h?(u=h(e),!Object(r["isString"])(u)&&u&&(u=e.date)):n%Math.round(x/10)===0&&(u=e.date),u){k.push(u);var E=y.maxY+2*o;w.addShape("line",{attrs:{stroke:"#BFBFBF",x1:T,y1:E,x2:T,y2:E+f},name:"tick-line"});var j=E+f+o,B=w.addShape("text",{attrs:ee({fill:"#8c8c8c",stroke:"#fff",lineWidth:1,x:T,y:j,textAlign:"center",text:u,textBaseline:"top",fontSize:10,fontFamily:t.fontFamily||"Arial, sans-serif"},p),capture:!1,name:"tick-label"}),L=B.getBBox();if(L.maxX>i?B.attr("textAlign","right"):L.minX<0&&B.attr("textAlign","left"),Object(r["isNumber"])(M)&&10!==k.length){var P=ie([1,0,0,0,1,0,0,0,1],[["t",-T,-j],["r",M],["t",T-5,j+2]]);B.attr({textAlign:"left",matrix:P})}1===k.length?B.attr({textAlign:"left"}):10===k.length&&B.attr({textAlign:"right"})}})),this.tickRects=S;var T=this.group;this.currentSpeed=1,this.controllerBtnGroup=new Xt({group:T,x:this.x,y:this.y+n+5,width:i,height:40,hideTimeTypeController:!0,speed:this.currentSpeed,fontFamily:this.fontFamily||"Arial, sans-serif"})},t.prototype.initEvent=function(){var t=this,e=this.sliceGroup;e.on("click",(function(e){var i=e.target;if("rect"===i.get("type")&&i.get("name")){var n=parseInt(i.get("name").split("-")[2],10);if(!isNaN(n)){var r=t.tickRects,o=t.unselectedTickStyle;r.forEach((function(t){t.rect.attr(o)}));var a=t.selectedTickStyle;r[n].rect.attr(a),t.startTickRectId=n,t.endTickRectId=n;var s=r.length,c=n/s;t.graph.emit(xt,{value:[c,c]})}}})),e.on("dragstart",(function(e){var i=t.tickRects,n=t.unselectedTickStyle;i.forEach((function(t){t.rect.attr(n)}));var r=e.target,o=parseInt(r.get("name").split("-")[2],10),a=t.selectedTickStyle;i[o].rect.attr(a),t.startTickRectId=o;var s=i.length,c=o/s;t.graph.emit(xt,{value:[c,c]}),t.dragging=!0})),e.on("dragover",(function(e){if(t.dragging&&"rect"===e.target.get("type")){for(var i=parseInt(e.target.get("name").split("-")[2],10),n=t.startTickRectId,r=t.tickRects,o=t.selectedTickStyle,a=t.unselectedTickStyle,s=0;s<r.length;s++){var c=s>=n&&s<=i?o:a;r[s].rect.attr(c)}var h=r.length;t.endTickRectId=i;var l=n/h,d=i/h;t.graph.emit(xt,{value:[l,d]})}})),e.on("drop",(function(e){if(t.dragging&&(t.dragging=!1,"rect"===e.target.get("type"))){var i=t.startTickRectId,n=parseInt(e.target.get("name").split("-")[2],10);if(!(n<i)){var r=t.selectedTickStyle,o=t.tickRects;o[n].rect.attr(r),t.endTickRectId=n;var a=o.length,s=i/a,c=n/a;t.graph.emit(xt,{value:[s,c]})}}}));var i=this,n=i.tooltipBackgroundColor,r=i.tooltipFomatter,o=i.canvas,a=new te({container:o.get("container"),backgroundColor:n}),s=this.tickRects;s.forEach((function(t){var e=t.pickRect;e.on("mouseenter",(function(t){var e=t.target;if("rect"===e.get("type")){var i=parseInt(e.get("name").split("-")[2],10),n=o.getClientByPoint(s[i].x,s[i].y);a.show({x:s[i].x,y:s[i].y,clientX:n.x,clientY:n.y,text:r?r(s[i].value):s[i].value})}})),e.on("mouseleave",(function(t){a.hide()}))}));var c=this.group;c.on("".concat(wt,":click"),(function(){t.isPlay=!t.isPlay,t.changePlayStatus()})),c.on("".concat(St,":click"),(function(){t.updateStartEnd(1)})),c.on("".concat(kt,":click"),(function(){t.updateStartEnd(-1)})),c.on(bt,(function(e){e.type;var i=e.speed;t.currentSpeed=i}))},t.prototype.changePlayStatus=function(t){void 0===t&&(t=!0),this.controllerBtnGroup.playButton.update({isPlay:this.isPlay}),this.isPlay?(this.playHandler=this.startPlay(),this.graph.emit(vt,null)):this.playHandler&&("undefined"!==typeof window&&window.cancelAnimationFrame(this.playHandler),t&&this.graph.emit(mt,null))},t.prototype.startPlay=function(){var t=this;return"undefined"!==typeof window?window.requestAnimationFrame((function(){var e=t.currentSpeed;t.frameCount%(60/e)===0&&(t.frameCount=0,t.updateStartEnd(1)),t.frameCount++,t.isPlay&&(t.playHandler=t.startPlay())})):void 0},t.prototype.updateStartEnd=function(t){var e=this,i=this.tickRects,n=i.length,r=this.unselectedTickStyle,o=this.selectedTickStyle,a=e.endTickRectId;if(t>0?e.endTickRectId++:(i[e.endTickRectId].rect.attr(r),e.endTickRectId--),a!==e.startTickRectId)e.endTickRectId<e.startTickRectId&&(e.startTickRectId=e.endTickRectId);else{for(var s=e.startTickRectId;s<=e.endTickRectId-1;s++)i[s].rect.attr(r);e.startTickRectId=e.endTickRectId}if(i[e.endTickRectId]){i[e.endTickRectId].rect.attr(o);var c=e.startTickRectId/n,h=e.endTickRectId/n;this.graph.emit(xt,{value:[c,h]})}},t.prototype.destory=function(){this.graph.off(xt,(function(){}));var t=this.sliceGroup;t.off("click"),t.off("dragstart"),t.off("dragover"),t.off("drop"),this.tickRects.forEach((function(t){var e=t.pickRect;e.off("mouseenter"),e.off("mouseleave")})),this.tickRects.length=0,t.off("".concat(wt,":click")),t.off("".concat(St,":click")),t.off("".concat(kt,":click")),t.off(bt),this.sliceGroup.destroy()},t}(),ae=oe,se=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!==typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),ce=function(){return ce=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++)for(var r in e=arguments[i],e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t},ce.apply(this,arguments)},he=function(t,e){var i={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(i[n]=t[n]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var r=0;for(n=Object.getOwnPropertySymbols(t);r<n.length;r++)e.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(t,n[r])&&(i[n[r]]=t[n[r]])}return i},le=4,de=26,pe=function(t){function e(e){return t.call(this,e)||this}return se(e,t),e.prototype.getDefaultCfgs=function(){return{container:null,className:"g6-component-timebar",padding:10,type:"trend",trend:{data:[],isArea:!1,smooth:!0},controllerCfg:{speed:1,loop:!1},slider:{start:.1,end:.9,minText:"min",maxText:"max"},tick:{start:.1,end:.9,data:[]},textStyle:{},filterEdge:!1,filterItemTypes:["node"],containerCSS:{}}},e.prototype.initContainer=function(){var t,e,i=this.get("graph"),o=this._cfgs,a=o.width,s=o.height,c=this.get("className")||"g6-component-timebar",h=this.get("container"),l=this.get("graph").get("container");h?(Object(r["isString"])(h)&&(h=document.getElementById(h)),t=h):(t=Object(n["b"])("<div class='".concat(c,"'></div>")),Object(n["c"])(t,{position:"relative"})),l.appendChild(t),this.set("timeBarContainer",t);var d=i.get("renderer");e="SVG"===d?new v["a"]({container:t,width:a,height:s}):new y["a"]({container:t,width:a,height:s}),this.get("containerCSS")&&Object(n["c"])(t,this.get("containerCSS")),this.set("canvas",e)},e.prototype.init=function(){this.initContainer();var t=this.get("canvas"),e=t.addGroup({name:"timebar-group"});this.set("timeBarGroup",e),this.renderTrend(),this.initEvent();var i="undefined"!==typeof window&&window.getComputedStyle(document.body,null).getPropertyValue("font-family")||"Arial, sans-serif";this.set("fontFamily",i)},e.prototype.renderTrend=function(){var t=this,e=this._cfgs,i=e.width,n=e.x,r=e.y,o=e.padding,a=e.type,s=e.trend,c=e.slider,h=e.controllerCfg,l=e.textStyle,d=e.tick,p=e.backgroundStyle,g=e.foregroundStyle,u=s.data,f=he(s,["data"]),y=i-2*o,v="trend"===a?de:le,m=this.get("graph"),x=this.get("timeBarGroup"),b=this.get("canvas"),w=null;if("trend"===a||"simple"===a){var S=this.get("getValue");w=new Qt(ce(ce({graph:m,canvas:b,group:x,type:a,x:n+o,y:"trend"===a?r+o:r+o+15,width:y,height:v,padding:o,backgroundStyle:p,foregroundStyle:g,trendCfg:ce(ce({},f),{data:u.map((function(t){return(null===S||void 0===S?void 0:S(t))||t.value}))})},c),{tick:{ticks:u,tickLabelFormatter:d.tickLabelFormatter,tickLabelStyle:d.tickLabelStyle,tickLineStyle:d.tickLineStyle},handlerStyle:ce(ce({},c.handlerStyle),{height:c.height||v}),controllerCfg:h,textStyle:l}))}else"tick"===a&&(w=new ae(ce({graph:m,canvas:b,group:x,x:n+o,y:r+o,width:i,height:42,padding:2},d)));var k=function e(){var i=t.get("timebar");i.draggingHandler=!1,i.isPlay&&(i.isPlay=!1,i.currentHandler=i.maxHandlerShape,i.changePlayStatus()),document.removeEventListener("mouseup",e)};b.on("mousedown",(function(t){"maxHandlerShape-handler"!==t.target.get("name")&&"minHandlerShape-handler"!==t.target.get("name")&&t.target!==w.foregroundShape||document.addEventListener("mouseup",k)})),this.set("timebar",w)},e.prototype.filterData=function(t){var e,i=t.value,n=null,r=this._cfgs.type;if("trend"===r||"simple"===r?n=this._cfgs.trend.data:"tick"===r&&(n=this._cfgs.tick.data),n&&0!==n.length){var o=this.get("rangeChange"),a=this.get("graph"),s=Math.round(n.length*i[0]),c=Math.round(n.length*i[1]);c=c>=n.length?n.length-1:c,s=s>=n.length?n.length-1:s;var h=null===(e=this._cfgs.tick)||void 0===e?void 0:e.tickLabelFormatter,l=h?h(n[s]):n[s].date,d=h?h(n[c]):n[c].date;if("tick"!==r){var p=this.get("timebar");p.setText(l,d)}if(o)o(a,l,d);else{(!this.cacheGraphData||this.cacheGraphData.nodes&&0===this.cacheGraphData.nodes.length)&&(this.cacheGraphData=a.get("data"));var g=this.get("filterItemTypes"),u=this.get("changeData"),f=this.get("getDate"),y=this.get("shouldIgnore"),v=n[s].date,m=n[c].date;if(u||void 0===u){var x=this.cacheGraphData.nodes,b=this.cacheGraphData.edges;if(g.includes("node")){x=x.filter((function(t){var e=+((null===f||void 0===f?void 0:f(t))||t.date);return e>=v&&e<=m||(null===y||void 0===y?void 0:y("node",t,{min:v,max:m}))}));var w=x.map((function(t){return t.id}));b&&(b=b.filter((function(t){return w.includes(t.source)&&w.includes(t.target)||(null===y||void 0===y?void 0:y("edge",t,{min:v,max:m}))})))}(this.get("filterEdge")||g.includes("edge"))&&(b=b.filter((function(t){var e=+((null===f||void 0===f?void 0:f(t))||t.date);return e>=v&&e<=m||(null===y||void 0===y?void 0:y("edge",t,{min:v,max:m}))}))),a.changeData({nodes:x,edges:b})}else g.includes("node")&&a.getNodes().forEach((function(t){var e=t.getModel();if(!(null===y||void 0===y?void 0:y("node",e,{min:v,max:m}))){var i=+((null===f||void 0===f?void 0:f(e))||e.date);i<v||i>m?a.hideItem(t):a.showItem(t)}})),(this.get("filterEdge")||g.includes("edge"))&&a.getEdges().forEach((function(t){var e=t.getModel();if(!(null===y||void 0===y?void 0:y("edge",e,{min:n[s].date,max:n[c].date}))){var i=+((null===f||void 0===f?void 0:f(e))||e.date);i<n[s].date||i>n[c].date?a.hideItem(t):a.showItem(t)}}))}}else console.warn("请配置 TimeBar 组件的数据")},e.prototype.initEvent=function(){var t=this,e=0,i=0,n=this._cfgs.type;n&&"trend"!==n&&"simple"!==n?"tick"===n&&(e=this._cfgs.tick.start,i=this._cfgs.tick.end):(e=this._cfgs.slider.start,i=this._cfgs.slider.end);var o=this.get("graph");o.on("afterrender",(function(n){t.filterData({value:[e,i]})})),o.on(xt,Object(r["throttle"])((function(e){t.filterData(e)}),200,{trailing:!0,leading:!0}))},e.prototype.destroy=function(){var e=this.get("timebar");e&&e.destory&&e.destory(),t.prototype.destroy.call(this);var i=this.get("timeBarContainer");if(i){var n=this.get("container");n||(n=this.get("graph").get("container")),Object(r["isString"])(n)&&(n=document.getElementById(n)),n.removeChild(i)}},e}(a),ge=pe,ue=i("3822"),fe=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!==typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),ye=ue["h"].applyMatrix;function ve(t,e){var i,n;if(t.naturalWidth)i=t.naturalWidth,n=t.naturalHeight;else{var r=new Image;r.src=t.src,r.onload=function(){e&&e(r.width,r.height)}}return[i,n]}var me=function(t){function e(e){return t.call(this,e)||this}return fe(e,t),e.prototype.getDefaultCfgs=function(){return{container:null,className:"g6-minimap",viewportClassName:"g6-minimap-viewport",width:200,delegateStyle:{fill:"#40a9ff",stroke:"#096dd9"},refresh:!0}},e.prototype.getEvents=function(){return{beforepaint:"updateViewport",beforeanimate:"disableRefresh",afteranimate:"enableRefresh",viewportchange:"disableOneRefresh"}},e.prototype.disableRefresh=function(){this.set("refresh",!1)},e.prototype.enableRefresh=function(){this.set("refresh",!0),this.updateCanvas()},e.prototype.disableOneRefresh=function(){this.set("viewportChange",!0)},e.prototype.initViewport=function(){var t=this,e=this._cfgs,i=e.graph;if(!this.destroyed){var o=this.get("container");Object(r["isString"])(o)&&(o=document.getElementById(o));var a=Object(n["b"])("<div class=".concat(e.viewportClassName,"\n      style='position:absolute;\n        left:0;\n        top:0;\n        box-sizing:border-box;\n        border: 2px solid #1980ff;\n        cursor:move'\n      </div>")),s=0,c=0,h=!1,l=0,d=0,p=0,g=0,u=0,f=0;o.addEventListener("mousedown",(function(n){if(e.refresh=!1,n.target===a){var r=a.style;p=parseInt(r.width,10),g=parseInt(r.height,10);var o=t.get("width"),l=t.get("height");p>o||g>l||(f=i.getZoom(),u=t.get("ratio"),h=!0,s=n.clientX,c=n.clientY)}}),!1),o.addEventListener("mousemove",(function(e){if(h&&!Object(r["isNil"])(e.clientX)&&!Object(r["isNil"])(e.clientY)){var o=t.get("width"),y=t.get("height"),v=a.style;l=parseInt(v.left,10),d=parseInt(v.top,10),p=parseInt(v.width,10),g=parseInt(v.height,10);var m=s-e.clientX,x=c-e.clientY;l-m<0?m=l:l-m+p>=o&&(m=0),d-x<0?x=d:d-x+g>=y&&(x=0),l-=m,d-=x,Object(n["c"])(a,{left:"".concat(l,"px"),top:"".concat(d,"px")}),i.translate(m*f/u,x*f/u),s=e.clientX,c=e.clientY}}),!1),o.addEventListener("mouseleave",(function(){h=!1,e.refresh=!0}),!1),o.addEventListener("mouseup",(function(){h=!1,e.refresh=!0}),!1),this.set("viewport",a),o.appendChild(a)}},e.prototype.updateViewport=function(){if(!this.destroyed){var t=this.get("ratio"),e=this.get("width"),i=this.get("height"),r=this.get("graph"),o=r.get("width"),a=r.get("height"),s=o/a,c=r.getGroup(),h=c.getCanvasBBox(),l=[(h.minX+h.maxX)/2,(h.minY+h.maxY)/2],d=[h.maxX-h.minX,h.maxY-h.minY],p={centerX:l[0],centerY:l[1],width:0,height:0,minX:0,minY:0};h[0]/h[1]>s?(p.width=d[0],p.height=p.width/s):(p.height=d[1],p.width=p.height*s),p.minX=l[0]-p.width/2,p.minY=l[1]-p.height/2;var g=c.getMatrix();g||(g=[1,0,0,0,1,0,0,0,1]);var u=m["b"].invert([1,0,0,0,1,0,0,0,1],g),f=ye({x:p.minX,y:p.minY},u),y=r.getCanvasByPoint(f.x,f.y),v=this.get("viewport");v||this.initViewport();var x=o/p.width,b=x*e,w=x*i,S=e*-y.x/p.width,k=i*-y.y/p.height,O=S+b,C=k+w;S<0&&(b+=S,S=0),O>e&&(b-=O-e),k<0&&(w+=k,k=0),C>i&&(w-=C-i),this.set("ratio",t);var M="".concat(S,"px"),T="".concat(k,"px");Object(n["c"])(v,{left:M,top:T,width:"".concat(b,"px"),height:"".concat(w,"px")})}},e.prototype.init=function(){this.initContainer()},e.prototype.initContainer=function(){var t=this,e=t.get("graph"),i=e.get("width"),o=e.get("height"),a=o/i,s=t.get("className"),c=t.get("container"),h=t.get("width"),l=t.get("height");h||l||(h=200),h?(l=a*h,t.set("height",l)):(h=1/a*l,t.set("width",h));var d=Object(n["b"])("<div class='".concat(s,"' style='width: ").concat(h,"px; height: ").concat(l,"px; overflow: hidden; position: relative;'></div>"));Object(r["isString"])(c)&&(c=document.getElementById(c)),c?c.appendChild(d):e.get("container").appendChild(d),t.set("container",d);var p=Object(n["b"])('<div class="g6-minimap-container" style="position: relative; width: 100%; height: 100%; text-align: center; display: table;"></div>');d.appendChild(p);var g=Object(n["b"])('<span style="display: table-cell; vertical-align: middle; "></span>');p.appendChild(g),t.set("containerDOM",p),t.set("containerSpan",g);var u=Object(n["b"])('<img alt="" src="'.concat(this.get("graphImg"),'" style="display: inline-block; user-select: none;" draggable="false" />'));t.set("imgDOM",u),t.updateImgSize(),g.appendChild(u),t.updateCanvas()},e.prototype.updateImgSize=function(){var t=this,e=t.get("imgDOM"),i=t.get("width"),n=t.get("height");e.onload=function(){var t=ve(e);t[0]>t[1]?e.width=i:e.height=n}},e.prototype.updateCanvas=function(){var t=this.get("refresh");if(t){var e=this.get("graph");if(!e.get("destroyed")){this.get("viewportChange")&&(this.set("viewportChange",!1),this.updateViewport());var i=this.get("width"),n=e.get("canvas").getCanvasBBox(),r=n.width,o=i/r;this.set("ratio",o),this.updateViewport()}}},e.prototype.getViewport=function(){return this.get("viewport")},e.prototype.getContainer=function(){return this.get("container")},e.prototype.updateGraphImg=function(t){var e=this,i=e.get("imgDOM");i.remove(),e.set("graphImg",t);var r=Object(n["b"])('<img alt="" src="'.concat(t,'" style="display: inline-block;" ondragstart="return false;" onselectstart="return false;"/>'));e.set("imgDOM",r),r.src=t,e.updateImgSize();var o=e.get("containerSpan");o.appendChild(r),e.updateCanvas()},e.prototype.destroy=function(){var t=this.get("container");t.parentNode.removeChild(t)},e}(a),xe=me,be=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!==typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),we=function(){return we=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++)for(var r in e=arguments[i],e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t},we.apply(this,arguments)},Se=ue["h"].distance,ke=.05,Oe={stroke:"#000",strokeOpacity:.8,lineWidth:2,fillOpacity:1,fill:"#fff"},Ce=function(t){function e(e){return t.call(this,e)||this}return be(e,t),e.prototype.getDefaultCfgs=function(){return{type:"both",trigger:"mousemove",r:60,delegateStyle:Object(r["clone"])(Oe),showLabel:"edge",scaleRBy:"wheel"}},e.prototype.getEvents=function(){var t;switch(this.get("trigger")){case"click":t={click:"filter"};break;case"drag":t={click:"createDelegate"};break;default:t={mousemove:"filter"};break}return t},e.prototype.init=function(){var t=this,e=t.get("showLabel"),i="node"===e||"both"===e,n="edge"===e||"both"===e;t.set("showNodeLabel",i),t.set("showEdgeLabel",n);var r=t.get("shouldShow");r||t.set("shouldShow",(function(){return!0}))},e.prototype.createDelegate=function(t){var e=this,i=e.get("delegate");i&&!i.destroyed||(e.filter(t),i=e.get("delegate"),i.on("dragstart",(function(t){})),i.on("drag",(function(t){e.filter(t)})),"wheel"===this.get("scaleRBy")&&i.on("mousewheel",(function(t){e.scaleRByWheel(t)})))},e.prototype.scaleRByWheel=function(t){var e=this;if(t&&t.originalEvent){t.preventDefault&&t.preventDefault();var i,n=e.get("graph"),r=e.get("delegate"),o=r?{x:r.attr("x"),y:r.attr("y")}:void 0;o||n.getPointByClient(t.clientX,t.clientY);i=t.originalEvent.wheelDelta<0?1-ke:1/(1-ke);var a=e.get("maxR"),s=e.get("minR"),c=e.get("r");(c>(a||n.get("height"))&&i>1||c<(s||.05*n.get("height"))&&i<1)&&(i=1),c*=i,e.set("r",c),e.filter(t)}},e.prototype.filter=function(t){var e=this,i=e.get("graph"),n=i.getNodes(),r={},o=e.get("r"),a=e.get("type"),s={x:t.x,y:t.y};e.updateDelegate(s,o);var c=e.get("shouldShow"),h=e.get("vShapes");h&&h.forEach((function(t){t.remove(),t.destroy()})),h=[],n.forEach((function(t){var e=t.getModel(),i=e.x,n=e.y;Se({x:i,y:n},s)<o&&(r[e.id]=t)}));var l=i.getEdges(),d=[];l.forEach((function(t){var e=t.getModel(),i=e.source,n=e.target;c(e)&&("only-source"===a||"one"===a?r[i]&&!r[n]&&d.push(t):"only-target"===a||"one"===a?r[n]&&!r[i]&&d.push(t):"both"===a&&r[i]&&r[n]&&d.push(t))}));var p=e.get("showNodeLabel"),g=e.get("showEdgelabel"),u=i.get("group");d.forEach((function(t){var e=t.get("group").get("children");e.forEach((function(t){var e=t.get("type"),i=u.addShape(e,{attrs:t.attr()});h.push(i),p&&"text"===e&&i.set("visible",!0)}))})),Object.keys(r).forEach((function(t){var e=r[t],i=e.get("group").clone();if(u.add(i),h.push(i),g)for(var n=i.get("children"),o=0;o<n.length;o++){var a=n[o];"text"===a.get("type")&&a.set("visible",!0)}})),e.set("vShapes",h)},e.prototype.updateParams=function(t){var e=this,i=t.r,n=t.trigger,r=t.minR,o=t.maxR,a=t.scaleRBy,s=t.showLabel,c=t.shouldShow;if(isNaN(t.r)||e.set("r",i),isNaN(o)||e.set("maxR",o),isNaN(r)||e.set("minR",r),"mousemove"!==n&&"click"!==n||e.set("trigger",n),"wheel"===a||"unset"===a){e.set("scaleRBy",a),e.get("delegate").remove(),e.get("delegate").destroy();var h=e.get("dPercentText");h&&(h.remove(),h.destroy())}"node"!==s&&"both"!==s||e.set("showNodeLabel",!0),"edge"!==s&&"both"!==s||e.set("showEdgeLabel",!0),c&&e.set("shouldShow",c)},e.prototype.updateDelegate=function(t,e){var i=this,n=i.get("graph"),r=i.get("delegate");if(!r||r.destroyed){var o=n.get("group"),a=i.get("delegateStyle")||Oe;r=o.addShape("circle",{attrs:we({r:e,x:t.x,y:t.y},a),name:"lens-shape",draggable:!0}),"drag"!==this.get("trigger")&&"wheel"===this.get("scaleRBy")&&r.on("mousewheel",(function(t){i.scaleRByWheel(t)}))}else r.attr({x:t.x,y:t.y,r:e});i.set("delegate",r)},e.prototype.clear=function(){var t=this,e=t.get("vShapes");e&&e.forEach((function(t){t.remove(),t.destroy()})),e=[],t.set("vShapes",e);var i=t.get("delegate");i&&!i.destroyed&&(i.remove(),i.destroy())},e.prototype.destroy=function(){this.clear()},e}(a),Me=Ce,Te=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!==typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Ee=ue["h"].pointLineDistance,je={stroke:"#FA8C16",lineWidth:1},Be=function(t){function e(e){return t.call(this,e)||this}return Te(e,t),e.prototype.getDefaultCfgs=function(){return{line:je,itemAlignType:"center",tolerance:5,horizontalLines:{},verticalLines:{},alignLines:[]}},e.prototype.init=function(){},e.prototype.getEvents=function(){return{"node:dragstart":"onDragStart","node:drag":"onDrag","node:dragend":"onDragEnd"}},e.prototype.onDragStart=function(){this.initBoxLine()},e.prototype.onDrag=function(t){var e=t.item,i=e.get("delegateShape")||e,n=i.getBBox(),r=e.getModel(),o=r.x-n.x,a=r.y-n.y;this.show({x:n.minX+o,y:n.minY+a},{width:n.width,height:n.height})},e.prototype.onDragEnd=function(){this.destory()},e.prototype.initBoxLine=function(){var t=this._cfgs,e=t.horizontalLines,i=t.verticalLines,n=t.itemAlignType,r=this.get("graph"),o=r.getNodes();o.forEach((function(t){var r=t.getBBox(),o=t.get("id");!0===n||"horizontal"===n?(e["".concat(o,"tltr")]=[r.minX,r.minY,r.maxX,r.minY,t],e["".concat(o,"lcrc")]=[r.minX,r.centerY,r.maxX,r.centerY,t],e["".concat(o,"blbr")]=[r.minX,r.maxY,r.maxX,r.maxY,t]):"center"===n&&(e["".concat(o,"lcrc")]=[r.minX,r.centerY,r.maxX,r.centerY,t]),!0===n||"vertical"===n?(i["".concat(o,"tlbl")]=[r.minX,r.minY,r.minX,r.maxY,t],i["".concat(o,"tcbc")]=[r.centerX,r.minY,r.centerX,r.maxY,t],i["".concat(o,"trbr")]=[r.maxX,r.minY,r.maxX,r.maxY,t]):"center"===n&&(i["".concat(o,"tcbc")]=[r.centerX,r.minY,r.centerX,r.maxY,t])}))},e.prototype.show=function(t,e){var i=Object(r["mix"])({},t);return this.itemAlign(t,e,i),t},e.prototype.itemAlign=function(t,e,i){var n=this,o=this._cfgs,a=o.horizontalLines,s=o.verticalLines,c=o.tolerance,h={x:i.x+e.width/2,y:i.y},l={x:i.x+e.width/2,y:i.y+e.height/2},d={x:i.x+e.width/2,y:i.y+e.height},p={x:i.x,y:i.y+e.height/2},g={x:i.x+e.width,y:i.y+e.height/2},u=[],f=[],y=null;if(this.clearAlignLine(),Object(r["each"])(a,(function(t){t[4].isVisible&&(u.push(n.getLineDisObject(t,h)),u.push(n.getLineDisObject(t,l)),u.push(n.getLineDisObject(t,d)))})),Object(r["each"])(s,(function(t){t[4].isVisible&&(f.push(n.getLineDisObject(t,p)),f.push(n.getLineDisObject(t,l)),f.push(n.getLineDisObject(t,g)))})),u.sort((function(t,e){return t.dis-e.dis})),f.sort((function(t,e){return t.dis-e.dis})),0!==u.length&&u[0].dis<c){t.y=u[0].line[1]-u[0].point.y+i.y,y={type:"item",horizontals:[u[0]]};for(var v=1;v<3;v++)u[0].dis===u[v].dis&&y.horizontals.push(u[v])}if(0!==f.length&&f[0].dis<c){t.x=f[0].line[0]-f[0].point.x+i.x,y?y.verticals=[f[0]]:y={type:"item",verticals:[f[0]]};for(v=1;v<3;v++)f[0].dis===f[v].dis&&y.verticals.push(f[v])}y&&(y.bbox=e,this.addAlignLine(y))},e.prototype.addAlignLine=function(t){var e=t.bbox,i=t.type,n=t.horizontals,o=t.verticals,a=this._cfgs,s=a.line,c=a.alignLines,h=this.get("graph"),l=h.get("group");"item"===i&&(n&&Object(r["each"])(n,(function(t){var i,n,o=t.line,a=t.point,h=(o[0]+o[2])/2;a.x<h?(i=a.x-e.width/2,n=Math.max(o[0],o[2])):(i=a.x+e.width/2,n=Math.min(o[0],o[2]));var d=Object(r["mix"])({x1:i,y1:o[1],x2:n,y2:o[1]},s),p=l.addShape("line",{attrs:d,capture:!1});c.push(p)})),o&&Object(r["each"])(o,(function(t){var i,n,o=t.line,a=t.point,h=(o[1]+o[3])/2;a.y<h?(i=a.y-e.height/2,n=Math.max(o[1],o[3])):(i=a.y+e.height/2,n=Math.min(o[1],o[3]));var d=Object(r["mix"])({x1:o[0],y1:i,x2:o[0],y2:n},s),p=l.addShape("line",{attrs:d,capture:!1});c.push(p)})))},e.prototype.getLineDisObject=function(t,e){return{line:t,point:e,dis:Ee(t,e)}},e.prototype.getContainer=function(){return this.get("container")},e.prototype.clearAlignLine=function(){var t=this._cfgs.alignLines;Object(r["each"])(t,(function(t){t.remove()})),t.length=0},e.prototype.destory=function(){var t=this._cfgs,e=t.horizontalLines,i=t.verticalLines,n=this.get("graph"),r=n.getNodes();r.forEach((function(t){var n=t.get("id");delete e["".concat(n,"tltr")],delete e["".concat(n,"lcrc")],delete e["".concat(n,"blbr")],delete i["".concat(n,"tlbl")],delete i["".concat(n,"tcbc")],delete i["".concat(n,"trbr")]})),this.clearAlignLine()},e}(a),Le=Be,Pe=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!==typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Ie=function(){return Ie=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++)for(var r in e=arguments[i],e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t},Ie.apply(this,arguments)},Ae=["click","mouseenter"],De=function(t){function e(e){return t.call(this,e)||this}return Pe(e,t),e.prototype.getDefaultCfgs=function(){return{data:{},position:"top",padding:8,margin:8,offsetX:0,offsetY:0,layout:"horizontal",flipPage:!1,containerStyle:{},align:void 0,horiSep:8,vertiSep:8,filter:{enable:!1,trigger:"click"}}},e.prototype.init=function(){this.formatArray("padding"),this.formatArray("margin");var t=this.get("filter")||{},e=t.multiple;e&&"mouseenter"===t.trigger&&this.set("multiple",!1);var i=this.get("align");if(!i){var r=this.get("position").split("-");r.includes("left")&&(i="left"),i=r.includes("right")?"right":"center",this.set("align",i)}var o=this.get("graph"),a=o.get("container"),s=Object(n["b"])("<div class='g6-legend-container' style=\"position: absolute;\"></div>");a.appendChild(s),this.set("container",s);var c=this.render();Object(n["c"])(s,this.getContainerPos(c)),this.bindEvents()},e.prototype.getContainerPos=function(t){void 0===t&&(t=[0,0]);var e=this,i=e.get("graph"),n=this.get("offsetX"),r=this.get("offsetY"),o=this.get("margin"),a=this.get("position").split("-"),s={top:0,right:1,bottom:2,left:3},c=0,h=0,l={left:(i.getWidth()-t[0])/2+c,top:(i.getHeight()-t[1])/2+h};return a.forEach((function(e){var n=o[s[e]],r=e;switch(e){case"top":n+=h;break;case"left":n+=c;break;case"bottom":n=i.getHeight()-t[1]-n+h,r="top";break;default:n=i.getWidth()-t[0]-n+c,r="left";break}l[r]=n})),l.top+=r+i.getContainer().offsetTop,l.left+=n+i.getContainer().offsetLeft,Object.keys(l).forEach((function(t){l[t]="".concat(l[t],"px")})),l},e.prototype.bindEvents=function(){var t=this,e=t.get("filter");if(e&&e.enable){var i=e.trigger||"click";Ae.includes(i)||(console.warn("Trigger for legend filterling must be 'click' or 'mouseenter', 'click' will take effect by default."),i="click");var n=t.get("legendCanvas");"mouseenter"===i?(n.on("node-container:mouseenter",(function(e){return t.filterData(e)})),n.on("node-container:mouseleave",(function(e){t.clearFilter(),t.clearActiveLegend()}))):(n.on("node-container:click",(function(e){return t.filterData(e)})),n.on("click",(function(e){e.target&&e.target.isCanvas&&e.target.isCanvas()&&(t.clearFilter(),t.clearActiveLegend())})))}},e.prototype.changeData=function(t){this.set("data",t);var e=this.render();Object(n["c"])(this.get("container"),this.getContainerPos(e))},e.prototype.activateLegend=function(t){var e=this.get("filter"),i=null===e||void 0===e?void 0:e.multiple;i||this.clearActiveLegend();var n=t.get("parent");n.get("active")?(n.set("active",!1),this.findLegendItemsByState("active").length&&n.set("inactive",!0)):(n.set("inactive",!1),n.set("active",!0)),this.findLegendItemsByState("active").length?this.findLegendItemsByState("active","all",!1).forEach((function(t){t.set("inactive",!0)})):this.clearActiveLegend();var r=(null===e||void 0===e?void 0:e.legendStateStyles)||{},o=(null===r||void 0===r?void 0:r.inactive)||{opacity:.5,"text-shape":{opacity:.5}},a=o["text-shape"]||{};this.findLegendItemsByState("inactive").forEach((function(t){var e=t.get("children"),i=e[0],n=e[1];i.attr(Ie(Ie({},i.get("oriAttrs")),o)),n.attr(Ie(Ie({},n.get("oriAttrs")),a))}));var s=(null===r||void 0===r?void 0:r.active)||{stroke:"#000",lineWidth:2,"text-shape":{fontWeight:"bold"}},c=s["text-shape"]||{};this.findLegendItemsByState("active").forEach((function(t){var e=t.get("children"),i=e[0],n=e[1];i.attr(Ie(Ie({},i.get("oriAttrs")),s)),n.attr(Ie(Ie({},n.get("oriAttrs")),c))}))},e.prototype.findLegendItemsByState=function(t,e,i){void 0===e&&(e="all"),void 0===i&&(i=!0);var n=this.get("legendCanvas").find((function(t){return"root"===t.get("name")})),r=n.find((function(t){return"node-group"===t.get("name")})),o=n.find((function(t){return"edge-group"===t.get("name")}));return"node"===e?r.get("children").filter((function(e){return!!e.get(t)===i})):"edge"===e?o.get("children").filter((function(e){return!!e.get(t)===i})):r.get("children").filter((function(e){return!!e.get(t)===i})).concat(o.get("children").filter((function(e){return!!e.get(t)===i})))},e.prototype.clearActiveLegend=function(){var t=this.get("legendCanvas"),e=t.find((function(t){return"root"===t.get("name")})),i=[e.find((function(t){return"node-group"===t.get("name")})),e.find((function(t){return"edge-group"===t.get("name")}))];i.forEach((function(t){t.get("children").forEach((function(t){t.set("active",!1),t.set("inactive",!1);var e=t.get("children"),i=e[0],n=e[1];i.attr(i.get("oriAttrs")),n.attr(n.get("oriAttrs"))}))}))},e.prototype.filterData=function(t){var e=this.get("filter"),i=null===e||void 0===e?void 0:e.filterFunctions;if(e&&i){var n=this.get("legendCanvas"),r=this.get("graph"),o=e.graphActiveState||"active",a=e.graphInactiveState||"inactive",s=e.multiple;this.clearFilter(),s||this.clearActiveLegend(),this.activateLegend(t.target);var c=n.find((function(t){return"root"===t.get("name")})),h=c.find((function(t){return"node-group"===t.get("name")})),l=c.find((function(t){return"edge-group"===t.get("name")})),d=h.get("children").filter((function(t){return t.get("active")})),p=l.get("children").filter((function(t){return t.get("active")})),g=0,u=["getNodes","getEdges"];u.forEach((function(t){r[t]().forEach((function(e){var n=!1,s="getNodes"===t?d:p;s.forEach((function(t){var r=i[t.get("id")];n=n||r(e.getModel())})),n?(r.setItemState(e,a,!1),r.setItemState(e,o,!0),g++):(r.setItemState(e,o,!1),r.setItemState(e,a,!0))}))})),g||u.forEach((function(t){r[t]().forEach((function(t){r.clearItemStates(t,[a])}))}))}},e.prototype.clearFilter=function(){var t=this.get("graph"),e=this.get("filter");if(e){var i=e.graphActiveState||"active",n=e.graphInactiveState||"inactive";t.getNodes().forEach((function(e){t.clearItemStates(e,[i,n])})),t.getEdges().forEach((function(e){t.clearItemStates(e,[i,n])}))}},e.prototype.render=function(){var t=this;this.processData();var e=this.get("legendCanvas");if(!e){e=new y["a"]({container:this.get("container"),width:200,height:200});var i=e.addGroup({name:"root"});i.addGroup({name:"node-group"}),i.addGroup({name:"edge-group"}),this.set("legendCanvas",e)}var n=e.find((function(t){return"root"===t.get("name")})),r=n.find((function(t){return"node-group"===t.get("name")})),o=n.find((function(t){return"edge-group"===t.get("name")})),a=this.get("itemsData"),s=["nodes","edges"],c=[r,o];s.forEach((function(e,i){a[e].forEach((function(n){var r,o,a=c[i].addGroup({id:n.id,name:"node-container"}),s=n.type,h=t.getShapeSize(n),l=h.width,d=h.height,p=h.r,g=t.getStyle(e.substr(0,4),n);switch(n.type){case"circle":o={r:p,x:0,y:0};break;case"rect":o={width:l,height:d,x:-l/2,y:-d/2};break;case"ellipse":o={r1:l,r2:d,x:0,y:0};break;case"line":o={x1:-l/2,y1:0,x2:l/2,y2:0},s="line";break;case"quadratic":o={path:[["M",-l/2,0],["Q",0,l/2,l/2,0]]},s="path";break;case"cubic":o={path:[["M",-l/2,0],["C",-l/6,l/2,l/6,-l/2,l/2,0]]},s="path";break;default:o={r:p,x:0,y:0};break}var u=a.addShape(s,{attrs:Ie(Ie({},o),g),name:"".concat(n.type,"-node-keyShape"),oriAttrs:Ie({opacity:1},g)});if(n.label){var f=u.getBBox(),y=(null===(r=n.labelCfg)||void 0===r?void 0:r.style)||{},v=Ie({textAlign:"begin",fontSize:12,textBaseline:"middle",fill:"#000",opacity:1,fontWeight:"normal"},y);a.addShape("text",{attrs:Ie({x:f.maxX+4,y:0,text:n.label},v),className:"legend-label",name:"".concat(n.type,"-node-text"),oriAttrs:v})}}))}));var h,l=this.get("padding"),d=n.find((function(t){return"title-container"===t.get("name")})),p={height:0,maxY:0,width:0};if(this.get("title")){d||(d=n.addGroup({name:"title-container"}));var g={fontSize:20,fontFamily:"Arial",fontWeight:300,textBaseline:"top",textAlign:"center",fill:"#000",x:0,y:l[0]},u=this.get("titleConfig")||{},f=Object.assign(g,u.style||{});h=d.addShape("text",{attrs:Ie({text:this.get("title")},f)}),p=d.getCanvasBBox(),d.setMatrix([1,0,0,0,1,0,u.offsetX,u.offsetY,1])}this.layoutItems();var v=n.getCanvasBBox(),m=r.getCanvasBBox(),x=m.minX<0?Math.abs(m.minX)+l[3]:l[3],b=p.maxY<m.minY?Math.abs(p.maxY-m.minY)+l[0]:p.maxY+l[0],w=[1,0,0,0,1,0,x,b,1];r.setMatrix(w),v=n.getCanvasBBox();var S=[v.minX+v.width+l[1],v.minY+v.height+l[2]];if(h){u=Ie({position:"center",offsetX:0,offsetY:0},this.get("titleConfig"));p=d.getCanvasBBox();var k=d.getMatrix()||[1,0,0,0,1,0,0,0,1];"center"===u.position?k[6]=S[0]/2+u.offsetX:"right"===u.position?(k[6]=S[0]-l[3]+u.offsetX,h.attr({textAlign:"right"})):(k[6]=l[3]+u.offsetX,h.attr({textAlign:"left"})),d.setMatrix(k),p=d.getCanvasBBox(),x=m.minX<0?Math.abs(m.minX)+l[3]:l[3],b=m.minY<p.maxY?Math.abs(p.maxY-m.minY)+l[0]:p.maxY+l[0],w=[1,0,0,0,1,0,x,b,1],r.setMatrix(w);var O=[1,0,0,0,1,0,x,b,1];"vertical"===this.get("layout")?O[6]+=m.maxX+this.get("horiSep"):O[7]+=m.maxY+this.get("vertiSep"),o.setMatrix(O)}else{m=r.getCanvasBBox();var C=[1,0,0,0,1,0,0,0,1];"vertical"===this.get("layout")?C[6]+=w[6]+m.maxX+this.get("horiSep"):C[7]+=w[7]+m.maxY+this.get("vertiSep"),o.setMatrix(C)}v=n.getCanvasBBox(),m=r.getCanvasBBox(),w=r.getMatrix()||[1,0,0,0,1,0,0,0,1];var M=o.getMatrix()||[1,0,0,0,1,0,0,0,1],T=o.getCanvasBBox();S=[Math.max(m.width+w[6],T.width+M[6])+l[1],Math.max(m.height+w[7],T.height+M[7])+l[2]],e.changeSize(S[0],S[1]);var E=this.get("containerStyle"),j=n.getMatrix()||[1,0,0,0,1,0,0,0,1],B=ue["h"].invertMatrix({x:0,y:0},j),L=n.addShape("rect",{attrs:Ie({x:B.x+(E.lineWidth||1),y:B.y+(E.lineWidth||1),width:S[0]-2*(E.lineWidth||1),height:S[1]-2*(E.lineWidth||1),fill:"#f00",stroke:"#000",lineWidth:1,opacity:.5},E),name:"legend-back-rect",capture:!1});return L.toBack(),S},e.prototype.layoutItems=function(){var t=this.get("legendCanvas"),e=this.get("horiSep"),i=this.get("vertiSep"),n=this.get("layout"),r=this.get("align"),o=[0,0],a=t.find((function(t){return"root"===t.get("name")})),s=a.find((function(t){return"node-group"===t.get("name")})),c=a.find((function(t){return"edge-group"===t.get("name")})),h={min:0,max:-1/0},l=-1/0;s.get("children").forEach((function(t,r){0===r&&(h.min=o[0]);var a=t.get("children")[0],s=t.getCanvasBBox(),c=a.getBBox(),d=c.width,p=c.height,g=0,u=0,f=0;"vertical"===n?(u=o[1],f=o[0]+d/2,o[0]=f+s.height+i,g=s.maxX+u+d/2):(u=o[0]+d/2,f=o[1],o[0]=u+s.width+e,g=s.maxY+f+p/2),o[0]>h.max&&(h.max=o[0]),g>l&&(l=g),t.setMatrix([1,0,0,0,1,0,u,f,1])}));var d=h.max-h.min,p={min:0,max:-1/0},g=s.getCanvasBBox();o[0]=0,o[1]="vertical"===n?g.maxX+e:g.maxY+i,c.get("children").forEach((function(t,r){0===r&&(p.min=o[0]);var a=t.get("children")[0],s=t.getCanvasBBox(),c=a.getBBox(),h=c.width,l=c.height,d=0,g=0;"vertical"===n?(d=o[1],g=o[0],o[0]=g+s.height+i,t.setMatrix([1,0,0,0,1,0,0,g+l/2,1])):(d=o[0],g=o[1],o[0]=d+s.width+e,t.setMatrix([1,0,0,0,1,0,d+h/2,0,1])),o[0]>p.max&&(p.max=o[0])}));var u=p.max-p.min;if(r&&""!==r&&"left"!==r){var f=d-u,y="center"===r?Math.abs(f)/2:Math.abs(f),v=f<0?s:c;v.get("children").forEach((function(t){var e=t.getMatrix()||[1,0,0,0,1,0,0,0,1];"vertical"===n?e[7]+=y:e[6]+=y,t.setMatrix(e)}))}},e.prototype.processData=function(){var t=this.get("data"),e={nodes:[],edges:[]};t.nodes&&(t.nodes.sort((function(t,e){return t.order-e.order})),t.nodes.forEach((function(t){var i,n,o,a,s,c=t.size||[(null===(i=t.style)||void 0===i?void 0:i.width)||(null===(n=t.style)||void 0===n?void 0:n.r)||8,(null===(o=t.style)||void 0===o?void 0:o.height)||(null===(a=t.style)||void 0===a?void 0:a.r)||8],h=(null===(s=t.labelCfg)||void 0===s?void 0:s.style)||{};e.nodes.push({id:t.id||Object(r["uniqueId"])(),type:t.type||"circle",style:Ie({},t.style),order:t.order,label:t.label,itemType:"node",size:c,labelCfg:{position:"right",style:Ie({fontFamily:"Arial"},h)}})}))),t.edges&&(t.edges.sort((function(t,e){return t.order-e.order})),t.edges.forEach((function(t){var i,n,o=t.type||"line";"cubic-horizontal"===t.type&&(o="cubic");var a=(null===(i=t.labelCfg)||void 0===i?void 0:i.style)||{},s=t.size||[(null===(n=t.style)||void 0===n?void 0:n.width)||8,1];e.edges.push({id:t.id||Object(r["uniqueId"])(),type:o,size:s,style:Ie({lineWidth:Object(r["isArray"])(s)?s[1]:1},t.style),order:t.order,label:t.label,itemType:"edge",labelCfg:{position:"right",style:Ie({fontFamily:"Arial"},a)}})}))),this.set("itemsData",e)},e.prototype.getContainer=function(){return this.get("container")},e.prototype.formatArray=function(t){var e=this.get(t);if(Object(r["isNumber"])(e))this.set(t,[e,e,e,e]);else if(Object(r["isArray"])(e))switch(e.length){case 0:this.set(t,[0,0,0,0]);break;case 1:this.set(t,[e[0],e[0],e[0],e[0]]);break;case 2:this.set(t,[e[0],e[1],e[0],e[1]]);break;case 3:this.set(t,[e[0],e[1],e[2],e[1]]);break;default:break}return this.get(t)},e.prototype.getShapeSize=function(t){var e,i,n;return t.size&&(Object(r["isArray"])(t.size)?(e=t.size[0],i=t.size[1]||t.size[0],n=t.size[0]/2):Object(r["isNumber"])(t.size)&&(e=t.size,i=t.size,n=t.size/2)),t.style&&(t.style.width&&(e=t.style.width),t.style.height&&(i=t.style.height),t.style.r&&(n=t.style.r)),n||(n=5),e||(e=n),i||(i=n),{width:e,height:i,r:n}},e.prototype.getStyle=function(t,e){var i="node"===t?{fill:"#ccc",lineWidth:0}:{stroke:"#000",lineWidth:1};return Ie(Ie({},i),e.style||{})},e.prototype.destroy=function(){var t=this.get("graph"),e=t.get("container"),i=this.get("container");e.removeChild(i)},e}(a),_e=De,Ge={PluginBase:a,Menu:f,Grid:l,Minimap:E,Bundling:A,ToolBar:W,Tooltip:U,Fisheye:X,TimeBar:ge,ImageMinimap:xe,EdgeFilterLens:Me,SnapLine:Le,Legend:_e};e["a"]=Ge}}]);