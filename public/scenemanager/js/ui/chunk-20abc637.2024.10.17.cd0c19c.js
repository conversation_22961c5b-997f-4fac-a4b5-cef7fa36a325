(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-20abc637"],{1874:function(t,e,a){"use strict";a("8d57")},"5a93":function(t,e,a){},"8d57":function(t,e,a){},d535:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("dialogComp",{attrs:{hideHeader:!1,needClose:"true",zIndex:"4",draw:!0,right:10,drag:!0,title:t.$t("dialog.choiceSet.label"),icon:"icon-details",width:360,dragTopOffset:t.dragTopOffset,height:t.attrHeight,position:"fixed",type:"detailInfo",top:t.attrTop},on:{close:t.closeDialog},scopedSlots:t._u([{key:"center",fn:function(){return[a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],staticClass:"elementList-container",attrs:{"element-loading-text":t.$t("others.dataLoading"),"element-loading-spinner":"el-icon-loading","element-loading-background":"rgba(0, 0, 0, 0.8)"}},[t.detailsData.length>0?[a("div",{staticClass:"list-content virtually-list-content",on:{scroll:t.updateVisibleList}},[a("div",{staticClass:"item t-header"},[a("span",{staticClass:"item-text"},[t._v(" "+t._s(t.$t("dialog.attribute.table.label3"))+" ")]),a("span",{staticClass:"item-text"},[t._v(" "+t._s(t.$t("dialog.attribute.table.label2"))+" ")])]),a("div",{staticClass:"virtually-item-content"},t._l(t.visibleList,(function(e){return a("div",{key:e.id,staticClass:"item"},[a("span",{staticClass:"item-text"},[t._v(t._s(e.elementID))]),a("span",{staticClass:"item-text",attrs:{title:e.name}},[t._v(t._s(e.name))])])})),0)]),a("div",{staticClass:"bottom-menu"},[a("div",[t._v(t._s(t.$t("formRelational.element.message",{num:t.detailsData.length})))]),a("div",[a("span",{staticClass:"cursor-btn",on:{click:function(e){return t.filterHandle()}}},[t._v(" "+t._s(t.$t("dialog.filterElement.label"))+" ")]),t.detailsData.length&&!t.isLoading?a("span",{staticClass:"cursor-btn margin-left-15",on:{click:function(e){return t.create()}}},[t._v(" "+t._s(t.$t("dialog.choiceSet.label3"))+" ")]):t._e()])])]:t._e(),a("div",{directives:[{name:"show",rawName:"v-show",value:0===t.detailsData.length,expression:"detailsData.length === 0"}],staticClass:"list-wrap text-center no-select"},[t._v(" "+t._s(t.$t("dialog.choiceSet.label2"))+" ")])],2)]},proxy:!0}])}),t.addFormDialog.dialogState?a("dialogComp",{staticClass:"sourceDom",attrs:{hideHeader:!1,needClose:"true",zIndex:"4",position:"fixed",draw:!1,top:0,right:0,bottom:0,left:0,drag:!1,title:t.$t("dialog.choiceSet.label3"),width:400,height:150,type:"detailInfo"},on:{close:t.closeAddDialog},scopedSlots:t._u([{key:"center",fn:function(){return[a("div",{staticClass:"add-container"},[a("el-input",{class:{"is-error":t.addFormDialog.inputError},attrs:{size:"small",placeholder:t.$t("dialog.choiceSet.placeholder")},model:{value:t.addFormDialog.name,callback:function(e){t.$set(t.addFormDialog,"name",e)},expression:"addFormDialog.name"}}),a("div",{staticClass:"bottom-btn"},[a("span",{staticClass:"cursor-btn cancel margin-right-8",on:{click:t.closeAddDialog}},[t._v(" "+t._s(t.$t("messageTips.cancel"))+" ")]),a("span",{staticClass:"cursor-btn confirm",on:{click:t.saveData}},[t._v(" "+t._s(t.$t("menuIconName.confirm"))+" ")])])],1)]},proxy:!0}],null,!1,157957973)}):t._e()],1)},n=[],o=a("c7eb"),s=a("1da1"),l=(a("d3b7"),a("3ca3"),a("ddb0"),a("d81d"),a("99af"),a("a15b"),a("b0c0"),a("159b"),a("e9c4"),a("fb6a"),{name:"elementList",components:{CommonSVG:function(){return a.e("chunk-51f90655").then(a.bind(null,"17d0"))}},data:function(){return{detailsData:[],attrHeight:500,attrTop:170,isLoading:!0,filterData:[],addFormDialog:{dialogState:!1,name:"",inputError:!1},visibleList:[],elementIDs:[]}},computed:{dragTopOffset:function(){return this.$store.state.scene.topToolBarHeight},currentFilterId:function(){return this.$store.state.menuList.currentFilterId},filterConditionData:function(){return this.$store.state.menuList.filterConditionData}},created:function(){this.setDialogSize(),window.addEventListener("resize",this.setDialogSize),this.SearchElementByProperty()},mounted:function(){},watch:{filterConditionData:function(t){}},beforeDestroy:function(){window.removeEventListener("resize",this.setDialogSize)},methods:{setDialogSize:function(){var t=document.body.clientHeight,e=document.querySelector(".desktop-view").clientHeight;0==e?(this.attrHeight=t-40-30,this.attrTop=50):(this.attrHeight=t-e-30,this.attrTop=e+10)},filterHandle:function(){this.$store.commit("toggleActiveDialog","filterCondition")},formatFilterData:function(){var t=this;this.filterData.map((function(e){return e.entityStr=t.createEntity(e),e}))},createEntity:function(t){var e="",a=isNaN(t.conditionsValue)?"'".concat(t.conditionsValue,"'"):t.conditionsValue;switch(t.conditions){case"1":e="Value=".concat(a);break;case"2":e="Value<>".concat(a);break;case"3":e="CAST(Value as DOUBLE) > ".concat(a);break;case"4":e="CAST(Value as DOUBLE) < ".concat(a);break;case"5":e="Value like '%".concat(t.conditionsValue,"%'");break;case"6":e="Value not like '%".concat(t.conditionsValue,"%'");break}return"(Name='".concat(t.peculiarity,"' AND ").concat(e,")")},SearchElementByProperty:function(){var t=this;return Object(s["a"])(Object(o["a"])().mark((function e(){var a,i,n,s;return Object(o["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.filterData=t.filterConditionData,t.formatFilterData(),t.detailsData=[],t.isLoading=!0,t.visibleList=[],window.scene.clearSelection(),a=window.scene.features.get(t.currentFilterId),i=t.filterData.map((function(t){return t.entityStr})).join(" |AND| "),n=new FormData,n.append("FeatureID",a.modelID),n.append("VaultID",a.vaultID),n.append("Entity",i),e.next=14,t.$api.SearchElementByProperty(n);case 14:s=e.sent,0===s.data.length&&t.$message.warning(t.$t("dialog.choiceSet.message")),t.detailsData=s.data,t.isLoading=!1,t.$nextTick((function(){if(s.data.length){t.updateVisibleList(),t.elementIDs=s.data.map((function(t){return"".concat(a.id,"^").concat(t.elementID)}));var e=window.scene.createArrayObject(t.elementIDs);e.select(),window.scene.render()}}));case 19:case"end":return e.stop()}}),e)})))()},create:function(){0!==this.detailsData.length&&(this.addFormDialog.dialogState=!0)},showAddDialog:function(){this.addFormDialog.dialogState=!0},saveData:function(){var t=this;if(""==this.addFormDialog.name)return this.$message.error(this.$t("messageTips.nameNotEmpty")),void(this.addFormDialog.inputError=!0);var e=0;if(window.scene.selectionSets.forEach((function(a){a.name==t.addFormDialog.name&&e++})),e>0)return this.$message.error(this.addFormDialog.name+" "+this.$t("messageTips.nameAlreadyExists")),!1;var a={};a[this.currentFilterId]=this.elementIDs,window.scene.addSelectionSet(this.addFormDialog.name,a,"","");var i=[];window.scene.selectionSets.forEach((function(t){i.push(JSON.parse(JSON.parse(JSON.stringify(t))))})),this.$message.success(this.$t("messageTips.createdSuccess")),this.$store.commit("setSelectionSets",i),this.closeDialog(),this.closeAddDialog()},closeDialog:function(){this.$store.commit("setFilterConditionData",[]),this.$store.commit("toggleActiveDialog","filterConditionList")},closeAddDialog:function(){this.addFormDialog.dialogState=!1,this.addFormDialog.name=""},updateVisibleList:function(){var t=document.querySelector(".virtually-list-content"),e=document.querySelector(".virtually-item-content"),a=document.querySelector(".virtually-list-content").querySelector(".t-header"),i=Math.floor(t.scrollTop/40),n=i+Math.ceil(t.clientHeight/40)+4;this.visibleList=this.detailsData.slice(i,n),e.style.top="".concat(40*i+40,"px"),a.style.top="".concat(t.scrollTop,"px")}}}),r=l,c=(a("1874"),a("fbec"),a("2877")),d=Object(c["a"])(r,i,n,!1,null,"ad4bb824",null);e["default"]=d.exports},fbec:function(t,e,a){"use strict";a("5a93")}}]);