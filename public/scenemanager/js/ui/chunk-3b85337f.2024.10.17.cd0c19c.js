(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3b85337f"],{"01b0":function(e,t,i){},"0a33":function(e,t,i){},"3a19":function(e,t,i){},"70f5":function(e,t,i){"use strict";i.d(t,"a",(function(){return h})),i.d(t,"b",(function(){return p}));var o=i("e8e3"),n=i("2504"),r=i("be5f"),s=i("fdcc"),a=i("4035"),d=i("b707"),l=function(){var e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])},e(t,i)};return function(t,i){function o(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(o.prototype=i.prototype,new o)}}(),c=function(){function e(){}return e.prototype.remove=function(){this.parent&&delete this.parent.children[this.id]},e.findId=function(e,t){var i;"string"===typeof e?i=t.id+"/"+e:(i=t.id+"/"+e.name,void 0!==t.children[i]&&(i=t.id+"/"+e.name+"_"+e.range.startLineNumber+"_"+e.range.startColumn));for(var o=i,n=0;void 0!==t.children[o];n++)o=i+"_"+n;return o},e.empty=function(e){for(var t in e.children)return!1;return!0},e}(),h=function(e){function t(t,i,o){var n=e.call(this)||this;return n.id=t,n.parent=i,n.symbol=o,n.children=Object.create(null),n}return l(t,e),t}(c),u=function(e){function t(t,i,o,n){var r=e.call(this)||this;return r.id=t,r.parent=i,r.provider=o,r.providerIndex=n,r.children=Object.create(null),r}return l(t,e),t}(c),g=function(){function e(){this._n=1,this._val=0}return e.prototype.update=function(e){return this._val=this._val+(e-this._val)/this._n,this._n+=1,this},e}(),p=function(e){function t(t){var i=e.call(this)||this;return i.textModel=t,i.id="root",i.parent=void 0,i._groups=Object.create(null),i.children=Object.create(null),i.id="root",i.parent=void 0,i}return l(t,e),t.create=function(e,i){var o=this,r=this._keys.for(e,!0),s=t._requests.get(r);if(!s){var a=new n["b"];s={promiseCnt:0,source:a,promise:t._create(e,a.token),model:void 0},t._requests.set(r,s);var d=Date.now();s.promise.then((function(){var t=o._keys.for(e,!1),i=o._requestDurations.get(t);i||(i=new g,o._requestDurations.set(t,i)),i.update(Date.now()-d)}))}return s.model?Promise.resolve(s.model):(s.promiseCnt+=1,i.onCancellationRequested((function(){0===--s.promiseCnt&&(s.source.cancel(),t._requests.delete(r))})),new Promise((function(e,i){s.promise.then((function(t){s.model=t,e(t)}),(function(e){t._requests.delete(r),i(e)}))})))},t._create=function(e,i){var r=new n["b"](i),a=new t(e),l=d["m"].ordered(e),h=l.map((function(e,i){var o=c.findId("provider_"+i,a),n=new u(o,a,e,i);return Promise.resolve(e.provideDocumentSymbols(a.textModel,r.token)).then((function(e){for(var i=0,o=e||[];i<o.length;i++){var r=o[i];t._makeOutlineElement(r,n)}return n}),(function(e){return Object(s["f"])(e),n})).then((function(e){c.empty(e)?e.remove():a._groups[o]=e}))})),g=d["m"].onDidChange((function(){var t=d["m"].ordered(e);Object(o["g"])(t,l)||r.cancel()}));return Promise.all(h).then((function(){return r.token.isCancellationRequested&&!i.isCancellationRequested?t._create(e,i):a._compact()})).finally((function(){g.dispose()}))},t._makeOutlineElement=function(e,i){var o=c.findId(e,i),n=new h(o,i,e);if(e.children)for(var r=0,s=e.children;r<s.length;r++){var a=s[r];t._makeOutlineElement(a,n)}i.children[n.id]=n},t.prototype._compact=function(){var e=0;for(var t in this._groups){var i=this._groups[t];void 0===Object(r["b"])(i.children)?delete this._groups[t]:e+=1}if(1!==e)this.children=this._groups;else{i=Object(r["b"])(this._groups);for(var t in i.children){var o=i.children[t];o.parent=this,this.children[o.id]=o}}return this},t._requestDurations=new a["a"](50,.7),t._requests=new a["a"](9,.75),t._keys=new(function(){function e(){this._counter=1,this._data=new WeakMap}return e.prototype.for=function(e,t){return e.id+"/"+(t?e.getVersionId():"")+"/"+this._hash(d["m"].all(e))},e.prototype._hash=function(e){for(var t="",i=0,o=e;i<o.length;i++){var n=o[i],r=this._data.get(n);"undefined"===typeof r&&(r=this._counter++,this._data.set(n,r)),t+=r}return t},e}()),t}(c)},7605:function(e,t,i){"use strict";i.r(t),i.d(t,"FoldingController",(function(){return ie})),i.d(t,"foldBackgroundBackground",(function(){return me}));i("0a33");var o=i("dff7"),n=i("ef8e"),r=i("3742"),s=i("5fe7"),a=i("fe45"),d=i("a666"),l=i("b2cc"),c=i("308f"),h=65535,u=16777215,g=4278190080,p=function(){function e(e,t,i){if(e.length!==t.length||e.length>h)throw new Error("invalid startIndexes or endIndexes size");this._startIndexes=e,this._endIndexes=t,this._collapseStates=new Uint32Array(Math.ceil(e.length/32)),this._types=i,this._parentsComputed=!1}return e.prototype.ensureParentIndices=function(){var e=this;if(!this._parentsComputed){this._parentsComputed=!0;for(var t=[],i=function(i,o){var n=t[t.length-1];return e.getStartLineNumber(n)<=i&&e.getEndLineNumber(n)>=o},o=0,n=this._startIndexes.length;o<n;o++){var r=this._startIndexes[o],s=this._endIndexes[o];if(r>u||s>u)throw new Error("startLineNumber or endLineNumber must not exceed "+u);while(t.length>0&&!i(r,s))t.pop();var a=t.length>0?t[t.length-1]:-1;t.push(o),this._startIndexes[o]=r+((255&a)<<24),this._endIndexes[o]=s+((65280&a)<<16)}}},Object.defineProperty(e.prototype,"length",{get:function(){return this._startIndexes.length},enumerable:!0,configurable:!0}),e.prototype.getStartLineNumber=function(e){return this._startIndexes[e]&u},e.prototype.getEndLineNumber=function(e){return this._endIndexes[e]&u},e.prototype.getType=function(e){return this._types?this._types[e]:void 0},e.prototype.hasTypes=function(){return!!this._types},e.prototype.isCollapsed=function(e){var t=e/32|0,i=e%32;return 0!==(this._collapseStates[t]&1<<i)},e.prototype.setCollapsed=function(e,t){var i=e/32|0,o=e%32,n=this._collapseStates[i];this._collapseStates[i]=t?n|1<<o:n&~(1<<o)},e.prototype.toRegion=function(e){return new f(this,e)},e.prototype.getParentIndex=function(e){this.ensureParentIndices();var t=((this._startIndexes[e]&g)>>>24)+((this._endIndexes[e]&g)>>>16);return t===h?-1:t},e.prototype.contains=function(e,t){return this.getStartLineNumber(e)<=t&&this.getEndLineNumber(e)>=t},e.prototype.findIndex=function(e){var t=0,i=this._startIndexes.length;if(0===i)return-1;while(t<i){var o=Math.floor((t+i)/2);e<this.getStartLineNumber(o)?i=o:t=o+1}return t-1},e.prototype.findRange=function(e){var t=this.findIndex(e);if(t>=0){var i=this.getEndLineNumber(t);if(i>=e)return t;t=this.getParentIndex(t);while(-1!==t){if(this.contains(t,e))return t;t=this.getParentIndex(t)}}return-1},e.prototype.toString=function(){for(var e=[],t=0;t<this.length;t++)e[t]="["+(this.isCollapsed(t)?"+":"-")+"] "+this.getStartLineNumber(t)+"/"+this.getEndLineNumber(t);return e.join(", ")},e}(),f=function(){function e(e,t){this.ranges=e,this.index=t}return Object.defineProperty(e.prototype,"startLineNumber",{get:function(){return this.ranges.getStartLineNumber(this.index)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"endLineNumber",{get:function(){return this.ranges.getEndLineNumber(this.index)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"regionIndex",{get:function(){return this.index},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"parentIndex",{get:function(){return this.ranges.getParentIndex(this.index)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"isCollapsed",{get:function(){return this.ranges.isCollapsed(this.index)},enumerable:!0,configurable:!0}),e.prototype.containedBy=function(e){return e.startLineNumber<=this.startLineNumber&&e.endLineNumber>=this.endLineNumber},e.prototype.containsLine=function(e){return this.startLineNumber<=e&&e<=this.endLineNumber},e}(),_=function(){function e(e,t){this._updateEventEmitter=new c["a"],this.onDidChange=this._updateEventEmitter.event,this._textModel=e,this._decorationProvider=t,this._regions=new p(new Uint32Array(0),new Uint32Array(0)),this._editorDecorationIds=[],this._isInitialized=!1}return Object.defineProperty(e.prototype,"regions",{get:function(){return this._regions},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"textModel",{get:function(){return this._textModel},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"isInitialized",{get:function(){return this._isInitialized},enumerable:!0,configurable:!0}),e.prototype.toggleCollapseState=function(e){var t=this;if(e.length){var i={};this._decorationProvider.changeDecorations((function(o){for(var n=0,r=e;n<r.length;n++){var s=r[n],a=s.regionIndex,d=t._editorDecorationIds[a];if(d&&!i[d]){i[d]=!0;var l=!t._regions.isCollapsed(a);t._regions.setCollapsed(a,l),o.changeDecorationOptions(d,t._decorationProvider.getDecorationOption(l))}}})),this._updateEventEmitter.fire({model:this,collapseStateChanged:e})}},e.prototype.update=function(e,t){var i=this;void 0===t&&(t=[]);var o=[],n=function(e,i){for(var o=0,n=t;o<n.length;o++){var r=n[o];if(e<r&&r<=i)return!0}return!1},r=function(t,r){var s=e.getStartLineNumber(t);r&&n(s,e.getEndLineNumber(t))&&(r=!1),e.setCollapsed(t,r);var a=i._textModel.getLineMaxColumn(s),d={startLineNumber:s,startColumn:a,endLineNumber:s,endColumn:a};o.push({range:d,options:i._decorationProvider.getDecorationOption(r)})},s=0,a=function(){while(s<i._regions.length){var e=i._regions.isCollapsed(s);if(s++,e)return s-1}return-1},d=0,l=a();while(-1!==l&&d<e.length){var c=this._textModel.getDecorationRange(this._editorDecorationIds[l]);if(c){var h=c.startLineNumber;if(this._textModel.getLineMaxColumn(h)===c.startColumn)while(d<e.length){var u=e.getStartLineNumber(d);if(!(h>=u))break;r(d,h===u),d++}}l=a()}while(d<e.length)r(d,!1),d++;this._editorDecorationIds=this._decorationProvider.deltaDecorations(this._editorDecorationIds,o),this._regions=e,this._isInitialized=!0,this._updateEventEmitter.fire({model:this})},e.prototype.getMemento=function(){for(var e=[],t=0;t<this._regions.length;t++)if(this._regions.isCollapsed(t)){var i=this._textModel.getDecorationRange(this._editorDecorationIds[t]);if(i){var o=i.startLineNumber,n=i.endLineNumber+this._regions.getEndLineNumber(t)-this._regions.getStartLineNumber(t);e.push({startLineNumber:o,endLineNumber:n})}}if(e.length>0)return e},e.prototype.applyMemento=function(e){if(Array.isArray(e)){for(var t=[],i=0,o=e;i<o.length;i++){var n=o[i],r=this.getRegionAtLine(n.startLineNumber);r&&!r.isCollapsed&&t.push(r)}this.toggleCollapseState(t)}},e.prototype.dispose=function(){this._decorationProvider.deltaDecorations(this._editorDecorationIds,[])},e.prototype.getAllRegionsAtLine=function(e,t){var i=[];if(this._regions){var o=this._regions.findRange(e),n=1;while(o>=0){var r=this._regions.toRegion(o);t&&!t(r,n)||i.push(r),n++,o=r.parentIndex}}return i},e.prototype.getRegionAtLine=function(e){if(this._regions){var t=this._regions.findRange(e);if(t>=0)return this._regions.toRegion(t)}return null},e.prototype.getRegionsInside=function(e,t){var i=[],o=e?e.regionIndex+1:0,n=e?e.endLineNumber:Number.MAX_VALUE;if(t&&2===t.length)for(var r=[],s=o,a=this._regions.length;s<a;s++){var d=this._regions.toRegion(s);if(!(this._regions.getStartLineNumber(s)<n))break;while(r.length>0&&!d.containedBy(r[r.length-1]))r.pop();r.push(d),t(d,r.length)&&i.push(d)}else for(s=o,a=this._regions.length;s<a;s++){d=this._regions.toRegion(s);if(!(this._regions.getStartLineNumber(s)<n))break;t&&!t(d)||i.push(d)}return i},e}();function m(e,t,i){for(var o=[],n=function(i){var n=e.getRegionAtLine(i);if(n){var r=!n.isCollapsed;if(o.push(n),t>1){var s=e.getRegionsInside(n,(function(e,i){return e.isCollapsed!==r&&i<t}));o.push.apply(o,s)}}},r=0,s=i;r<s.length;r++){var a=s[r];n(a)}e.toggleCollapseState(o)}function b(e,t,i,o){void 0===i&&(i=Number.MAX_VALUE);var n=[];if(o&&o.length>0)for(var r=0,s=o;r<s.length;r++){var a=s[r],d=e.getRegionAtLine(a);if(d&&(d.isCollapsed!==t&&n.push(d),i>1)){var l=e.getRegionsInside(d,(function(e,o){return e.isCollapsed!==t&&o<i}));n.push.apply(n,l)}}else{l=e.getRegionsInside(null,(function(e,o){return e.isCollapsed!==t&&o<i}));n.push.apply(n,l)}e.toggleCollapseState(n)}function v(e,t,i,o){for(var n=[],r=0,s=o;r<s.length;r++){var a=s[r],d=e.getAllRegionsAtLine(a,(function(e,o){return e.isCollapsed!==t&&o<=i}));n.push.apply(n,d)}e.toggleCollapseState(n)}function y(e,t,i){for(var o=[],n=0,r=i;n<r.length;n++){var s=r[n],a=e.getAllRegionsAtLine(s,(function(e){return e.isCollapsed!==t}));a.length>0&&o.push(a[0])}e.toggleCollapseState(o)}function C(e,t,i,o){var n=function(e,n){return n===t&&e.isCollapsed!==i&&!o.some((function(t){return e.containsLine(t)}))},r=e.getRegionsInside(null,n);e.toggleCollapseState(r)}function S(e,t,i){for(var o=e.textModel,n=e.regions,r=[],s=n.length-1;s>=0;s--)if(i!==n.isCollapsed(s)){var a=n.getStartLineNumber(s);t.test(o.getLineContent(a))&&r.push(n.toRegion(s))}e.toggleCollapseState(r)}function R(e,t,i){for(var o=e.regions,n=[],r=o.length-1;r>=0;r--)i!==o.isCollapsed(r)&&t===o.getType(r)&&n.push(o.toRegion(r));e.toggleCollapseState(n)}var w=i("b57f"),I=function(){function e(e){this.editor=e,this.autoHideFoldingControls=!0,this.showFoldingHighlights=!0}return e.prototype.getDecorationOption=function(t){return t?this.showFoldingHighlights?e.COLLAPSED_HIGHLIGHTED_VISUAL_DECORATION:e.COLLAPSED_VISUAL_DECORATION:this.autoHideFoldingControls?e.EXPANDED_AUTO_HIDE_VISUAL_DECORATION:e.EXPANDED_VISUAL_DECORATION},e.prototype.deltaDecorations=function(e,t){return this.editor.deltaDecorations(e,t)},e.prototype.changeDecorations=function(e){return this.editor.changeDecorations(e)},e.COLLAPSED_VISUAL_DECORATION=w["a"].register({stickiness:1,afterContentClassName:"inline-folded",linesDecorationsClassName:"codicon codicon-chevron-right"}),e.COLLAPSED_HIGHLIGHTED_VISUAL_DECORATION=w["a"].register({stickiness:1,afterContentClassName:"inline-folded",className:"folded-background",isWholeLine:!0,linesDecorationsClassName:"codicon codicon-chevron-right"}),e.EXPANDED_AUTO_HIDE_VISUAL_DECORATION=w["a"].register({stickiness:1,linesDecorationsClassName:"codicon codicon-chevron-down"}),e.EXPANDED_VISUAL_DECORATION=w["a"].register({stickiness:1,linesDecorationsClassName:"codicon codicon-chevron-down alwaysShowFoldIcons"}),e}(),O=i("c101"),N=i("6a89"),x=i("e8e3"),F=function(){function e(e){var t=this;this._updateEventEmitter=new c["a"],this._foldingModel=e,this._foldingModelListener=e.onDidChange((function(e){return t.updateHiddenRanges()})),this._hiddenRanges=[],e.regions.length&&this.updateHiddenRanges()}return Object.defineProperty(e.prototype,"onDidChange",{get:function(){return this._updateEventEmitter.event},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"hiddenRanges",{get:function(){return this._hiddenRanges},enumerable:!0,configurable:!0}),e.prototype.updateHiddenRanges=function(){for(var e=!1,t=[],i=0,o=0,n=Number.MAX_VALUE,r=-1,s=this._foldingModel.regions;i<s.length;i++)if(s.isCollapsed(i)){var a=s.getStartLineNumber(i)+1,d=s.getEndLineNumber(i);n<=a&&d<=r||(!e&&o<this._hiddenRanges.length&&this._hiddenRanges[o].startLineNumber===a&&this._hiddenRanges[o].endLineNumber===d?(t.push(this._hiddenRanges[o]),o++):(e=!0,t.push(new N["a"](a,1,d,1))),n=a,r=d)}(e||o<this._hiddenRanges.length)&&this.applyHiddenRanges(t)},e.prototype.applyMemento=function(e){if(!Array.isArray(e)||0===e.length)return!1;for(var t=[],i=0,o=e;i<o.length;i++){var n=o[i];if(!n.startLineNumber||!n.endLineNumber)return!1;t.push(new N["a"](n.startLineNumber+1,1,n.endLineNumber,1))}return this.applyHiddenRanges(t),!0},e.prototype.getMemento=function(){return this._hiddenRanges.map((function(e){return{startLineNumber:e.startLineNumber-1,endLineNumber:e.endLineNumber}}))},e.prototype.applyHiddenRanges=function(e){this._hiddenRanges=e,this._updateEventEmitter.fire(e)},e.prototype.hasRanges=function(){return this._hiddenRanges.length>0},e.prototype.isHidden=function(e){return null!==D(this._hiddenRanges,e)},e.prototype.adjustSelections=function(e){for(var t=this,i=!1,o=this._foldingModel.textModel,n=null,r=function(e){return n&&M(e,n)||(n=D(t._hiddenRanges,e)),n?n.startLineNumber-1:null},s=0,a=e.length;s<a;s++){var d=e[s],l=r(d.startLineNumber);l&&(d=d.setStartPosition(l,o.getLineMaxColumn(l)),i=!0);var c=r(d.endLineNumber);c&&(d=d.setEndPosition(c,o.getLineMaxColumn(c)),i=!0),e[s]=d}return i},e.prototype.dispose=function(){this.hiddenRanges.length>0&&(this._hiddenRanges=[],this._updateEventEmitter.fire(this._hiddenRanges)),this._foldingModelListener&&(this._foldingModelListener.dispose(),this._foldingModelListener=null)},e}();function M(e,t){return e>=t.startLineNumber&&e<=t.endLineNumber}function D(e,t){var i=Object(x["i"])(e,(function(e){return t<e.startLineNumber}))-1;return i>=0&&e[i].endLineNumber>=t?e[i]:null}var E=i("70cb"),L=5e3,P="indent",T=function(){function e(e){this.editorModel=e,this.id=P}return e.prototype.dispose=function(){},e.prototype.compute=function(e){var t=E["a"].getFoldingRules(this.editorModel.getLanguageIdentifier().id),i=t&&!!t.offSide,o=t&&t.markers;return Promise.resolve(A(this.editorModel,i,o))},e}(),k=function(){function e(e){this._startIndexes=[],this._endIndexes=[],this._indentOccurrences=[],this._length=0,this._foldingRangesLimit=e}return e.prototype.insertFirst=function(e,t,i){if(!(e>u||t>u)){var o=this._length;this._startIndexes[o]=e,this._endIndexes[o]=t,this._length++,i<1e3&&(this._indentOccurrences[i]=(this._indentOccurrences[i]||0)+1)}},e.prototype.toIndentRanges=function(e){if(this._length<=this._foldingRangesLimit){for(var t=new Uint32Array(this._length),i=new Uint32Array(this._length),o=this._length-1,n=0;o>=0;o--,n++)t[n]=this._startIndexes[o],i[n]=this._endIndexes[o];return new p(t,i)}var r=0,s=this._indentOccurrences.length;for(o=0;o<this._indentOccurrences.length;o++){var a=this._indentOccurrences[o];if(a){if(a+r>this._foldingRangesLimit){s=o;break}r+=a}}var d=e.getOptions().tabSize;for(t=new Uint32Array(this._foldingRangesLimit),i=new Uint32Array(this._foldingRangesLimit),o=this._length-1,n=0;o>=0;o--){var l=this._startIndexes[o],c=e.getLineContent(l),h=w["b"].computeIndentLevel(c,d);(h<s||h===s&&r++<this._foldingRangesLimit)&&(t[n]=l,i[n]=this._endIndexes[o],n++)}return new p(t,i)},e}();function A(e,t,i,o){void 0===o&&(o=L);var n=e.getOptions().tabSize,r=new k(o),s=void 0;i&&(s=new RegExp("("+i.start.source+")|(?:"+i.end.source+")"));var a=[],d=e.getLineCount()+1;a.push({indent:-1,endAbove:d,line:d});for(var l=e.getLineCount();l>0;l--){var c=e.getLineContent(l),h=w["b"].computeIndentLevel(c,n),u=a[a.length-1];if(-1!==h){var g=void 0;if(s&&(g=c.match(s))){if(!g[1]){a.push({indent:-2,endAbove:l,line:l});continue}var p=a.length-1;while(p>0&&-2!==a[p].indent)p--;if(p>0){a.length=p+1,u=a[p],r.insertFirst(l,u.line,h),u.line=l,u.indent=h,u.endAbove=l;continue}}if(u.indent>h){do{a.pop(),u=a[a.length-1]}while(u.indent>h);var f=u.endAbove-1;f-l>=1&&r.insertFirst(l,f,h)}u.indent===h?u.endAbove=l:a.push({indent:h,endAbove:l,line:l})}else t&&(u.endAbove=l)}return r.toIndentRanges(e)}var W=i("b707"),j=i("fdcc"),B=5e3,V={},H="syntax",U=function(){function e(e,t,i){void 0===i&&(i=B),this.editorModel=e,this.providers=t,this.limit=i,this.id=H}return e.prototype.compute=function(e){var t=this;return q(this.providers,this.editorModel,e).then((function(e){if(e){var i=K(e,t.limit);return i}return null}))},e.prototype.dispose=function(){},e}();function q(e,t,i){var o=null,n=e.map((function(e,n){return Promise.resolve(e.provideFoldingRanges(t,V,i)).then((function(e){if(!i.isCancellationRequested&&Array.isArray(e)){Array.isArray(o)||(o=[]);for(var r=t.getLineCount(),s=0,a=e;s<a.length;s++){var d=a[s];d.start>0&&d.end>d.start&&d.end<=r&&o.push({start:d.start,end:d.end,rank:n,kind:d.kind})}}}),j["f"])}));return Promise.all(n).then((function(e){return o}))}var Z=function(){function e(e){this._startIndexes=[],this._endIndexes=[],this._nestingLevels=[],this._nestingLevelCounts=[],this._types=[],this._length=0,this._foldingRangesLimit=e}return e.prototype.add=function(e,t,i,o){if(!(e>u||t>u)){var n=this._length;this._startIndexes[n]=e,this._endIndexes[n]=t,this._nestingLevels[n]=o,this._types[n]=i,this._length++,o<30&&(this._nestingLevelCounts[o]=(this._nestingLevelCounts[o]||0)+1)}},e.prototype.toIndentRanges=function(){if(this._length<=this._foldingRangesLimit){for(var e=new Uint32Array(this._length),t=new Uint32Array(this._length),i=0;i<this._length;i++)e[i]=this._startIndexes[i],t[i]=this._endIndexes[i];return new p(e,t,this._types)}var o=0,n=this._nestingLevelCounts.length;for(i=0;i<this._nestingLevelCounts.length;i++){var r=this._nestingLevelCounts[i];if(r){if(r+o>this._foldingRangesLimit){n=i;break}o+=r}}e=new Uint32Array(this._foldingRangesLimit),t=new Uint32Array(this._foldingRangesLimit);for(var s=[],a=(i=0,0);i<this._length;i++){var d=this._nestingLevels[i];(d<n||d===n&&o++<this._foldingRangesLimit)&&(e[a]=this._startIndexes[i],t[a]=this._endIndexes[i],s[a]=this._types[i],a++)}return new p(e,t,s)},e}();function K(e,t){for(var i=e.sort((function(e,t){var i=e.start-t.start;return 0===i&&(i=e.rank-t.rank),i})),o=new Z(t),n=void 0,r=[],s=0,a=i;s<a.length;s++){var d=a[s];if(n){if(d.start>n.start)if(d.end<=n.end)r.push(n),n=d,o.add(d.start,d.end,d.kind&&d.kind.value,r.length);else{if(d.start>n.end){do{n=r.pop()}while(n&&d.start>n.end);n&&r.push(n),n=d}o.add(d.start,d.end,d.kind&&d.kind.value,r.length)}}else n=d,o.add(d.start,d.end,d.kind&&d.kind.value,r.length)}return o.toIndentRanges()}var z="init",G=function(){function e(e,t,i,o){if(this.editorModel=e,this.id=z,t.length){var n=function(t){return{range:{startLineNumber:t.startLineNumber,startColumn:0,endLineNumber:t.endLineNumber,endColumn:e.getLineLength(t.endLineNumber)},options:{stickiness:1}}};this.decorationIds=e.deltaDecorations([],t.map(n)),this.timeout=setTimeout(i,o)}}return e.prototype.dispose=function(){this.decorationIds&&(this.editorModel.deltaDecorations(this.decorationIds,[]),this.decorationIds=void 0),"number"===typeof this.timeout&&(clearTimeout(this.timeout),this.timeout=void 0)},e.prototype.compute=function(e){var t=[];if(this.decorationIds)for(var i=0,o=this.decorationIds;i<o.length;i++){var n=o[i],r=this.editorModel.getDecorationRange(n);r&&t.push({start:r.startLineNumber,end:r.endLineNumber,rank:1})}return Promise.resolve(K(t,Number.MAX_VALUE))},e}(),X=i("4fc3"),Y=i("b7d0"),Q=i("303e"),$=function(){var e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])},e(t,i)};return function(t,i){function o(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(o.prototype=i.prototype,new o)}}(),J=function(e,t,i,o){var n,r=arguments.length,s=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,i):o;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)s=Reflect.decorate(e,t,i,o);else for(var a=e.length-1;a>=0;a--)(n=e[a])&&(s=(r<3?n(s):r>3?n(t,i,s):n(t,i))||s);return r>3&&s&&Object.defineProperty(t,i,s),s},ee=function(e,t){return function(i,o){t(i,o,e)}},te=new X["d"]("foldingEnabled",!1),ie=function(e){function t(t,i){var o=e.call(this)||this;o.contextKeyService=i,o.localToDispose=o._register(new d["b"]),o.editor=t;var n=o.editor.getOptions();return o._isEnabled=n.get(30),o._useFoldingProviders="indentation"!==n.get(31),o.foldingModel=null,o.hiddenRangeModel=null,o.rangeProvider=null,o.foldingRegionPromise=null,o.foldingStateMemento=null,o.foldingModelPromise=null,o.updateScheduler=null,o.cursorChangedScheduler=null,o.mouseDownInfo=null,o.foldingDecorationProvider=new I(t),o.foldingDecorationProvider.autoHideFoldingControls="mouseover"===n.get(84),o.foldingDecorationProvider.showFoldingHighlights=n.get(32),o.foldingEnabled=te.bindTo(o.contextKeyService),o.foldingEnabled.set(o._isEnabled),o._register(o.editor.onDidChangeModel((function(){return o.onModelChanged()}))),o._register(o.editor.onDidChangeConfiguration((function(e){if(e.hasChanged(30)){var t=o.editor.getOptions();o._isEnabled=t.get(30),o.foldingEnabled.set(o._isEnabled),o.onModelChanged()}if(e.hasChanged(84)||e.hasChanged(32)){var i=o.editor.getOptions();o.foldingDecorationProvider.autoHideFoldingControls="mouseover"===i.get(84),o.foldingDecorationProvider.showFoldingHighlights=i.get(32),o.onModelContentChanged()}if(e.hasChanged(31)){var n=o.editor.getOptions();o._useFoldingProviders="indentation"!==n.get(31),o.onFoldingStrategyChanged()}}))),o.onModelChanged(),o}return $(t,e),t.get=function(e){return e.getContribution(t.ID)},t.prototype.saveViewState=function(){var e=this.editor.getModel();if(!e||!this._isEnabled||e.isTooLargeForTokenization())return{};if(this.foldingModel){var t=this.foldingModel.isInitialized?this.foldingModel.getMemento():this.hiddenRangeModel.getMemento(),i=this.rangeProvider?this.rangeProvider.id:void 0;return{collapsedRegions:t,lineCount:e.getLineCount(),provider:i}}},t.prototype.restoreViewState=function(e){var t=this.editor.getModel();if(t&&this._isEnabled&&!t.isTooLargeForTokenization()&&this.hiddenRangeModel&&e&&e.collapsedRegions&&e.lineCount===t.getLineCount()){e.provider!==H&&e.provider!==z||(this.foldingStateMemento=e);var i=e.collapsedRegions;if(this.hiddenRangeModel.applyMemento(i)){var o=this.getFoldingModel();o&&o.then((function(e){e&&e.applyMemento(i)})).then(void 0,j["e"])}}},t.prototype.onModelChanged=function(){var e=this;this.localToDispose.clear();var t=this.editor.getModel();this._isEnabled&&t&&!t.isTooLargeForTokenization()&&(this.foldingModel=new _(t,this.foldingDecorationProvider),this.localToDispose.add(this.foldingModel),this.hiddenRangeModel=new F(this.foldingModel),this.localToDispose.add(this.hiddenRangeModel),this.localToDispose.add(this.hiddenRangeModel.onDidChange((function(t){return e.onHiddenRangesChanges(t)}))),this.updateScheduler=new s["a"](200),this.cursorChangedScheduler=new s["d"]((function(){return e.revealCursor()}),200),this.localToDispose.add(this.cursorChangedScheduler),this.localToDispose.add(W["o"].onDidChange((function(){return e.onFoldingStrategyChanged()}))),this.localToDispose.add(this.editor.onDidChangeModelLanguageConfiguration((function(){return e.onFoldingStrategyChanged()}))),this.localToDispose.add(this.editor.onDidChangeModelContent((function(){return e.onModelContentChanged()}))),this.localToDispose.add(this.editor.onDidChangeCursorPosition((function(){return e.onCursorPositionChanged()}))),this.localToDispose.add(this.editor.onMouseDown((function(t){return e.onEditorMouseDown(t)}))),this.localToDispose.add(this.editor.onMouseUp((function(t){return e.onEditorMouseUp(t)}))),this.localToDispose.add({dispose:function(){e.foldingRegionPromise&&(e.foldingRegionPromise.cancel(),e.foldingRegionPromise=null),e.updateScheduler&&e.updateScheduler.cancel(),e.updateScheduler=null,e.foldingModel=null,e.foldingModelPromise=null,e.hiddenRangeModel=null,e.cursorChangedScheduler=null,e.foldingStateMemento=null,e.rangeProvider&&e.rangeProvider.dispose(),e.rangeProvider=null}}),this.onModelContentChanged())},t.prototype.onFoldingStrategyChanged=function(){this.rangeProvider&&this.rangeProvider.dispose(),this.rangeProvider=null,this.onModelContentChanged()},t.prototype.getRangeProvider=function(e){var t=this;if(this.rangeProvider)return this.rangeProvider;if(this.rangeProvider=new T(e),this._useFoldingProviders&&this.foldingModel){var i=W["o"].ordered(this.foldingModel.textModel);if(0===i.length&&this.foldingStateMemento&&this.foldingStateMemento.collapsedRegions){var o=this.rangeProvider=new G(e,this.foldingStateMemento.collapsedRegions,(function(){t.foldingStateMemento=null,t.onFoldingStrategyChanged()}),3e4);return o}i.length>0&&(this.rangeProvider=new U(e,i))}return this.foldingStateMemento=null,this.rangeProvider},t.prototype.getFoldingModel=function(){return this.foldingModelPromise},t.prototype.onModelContentChanged=function(){var e=this;this.updateScheduler&&(this.foldingRegionPromise&&(this.foldingRegionPromise.cancel(),this.foldingRegionPromise=null),this.foldingModelPromise=this.updateScheduler.trigger((function(){var t=e.foldingModel;if(!t)return null;var i=e.foldingRegionPromise=Object(s["f"])((function(i){return e.getRangeProvider(t.textModel).compute(i)}));return i.then((function(o){if(o&&i===e.foldingRegionPromise){var n=e.editor.getSelections(),r=n?n.map((function(e){return e.startLineNumber})):[];t.update(o,r)}return t}))})).then(void 0,(function(e){return Object(j["e"])(e),null})))},t.prototype.onHiddenRangesChanges=function(e){if(this.hiddenRangeModel&&e.length){var t=this.editor.getSelections();t&&this.hiddenRangeModel.adjustSelections(t)&&this.editor.setSelections(t)}this.editor.setHiddenAreas(e)},t.prototype.onCursorPositionChanged=function(){this.hiddenRangeModel&&this.hiddenRangeModel.hasRanges()&&this.cursorChangedScheduler.schedule()},t.prototype.revealCursor=function(){var e=this,t=this.getFoldingModel();t&&t.then((function(t){if(t){var i=e.editor.getSelections();if(i&&i.length>0){for(var o=[],n=function(i){var n=i.selectionStartLineNumber;e.hiddenRangeModel&&e.hiddenRangeModel.isHidden(n)&&o.push.apply(o,t.getAllRegionsAtLine(n,(function(e){return e.isCollapsed&&n>e.startLineNumber})))},r=0,s=i;r<s.length;r++){var a=s[r];n(a)}o.length&&(t.toggleCollapseState(o),e.reveal(i[0].getPosition()))}}})).then(void 0,j["e"])},t.prototype.onEditorMouseDown=function(e){if(this.mouseDownInfo=null,this.hiddenRangeModel&&e.target&&e.target.range&&(e.event.leftButton||e.event.middleButton)){var t=e.target.range,i=!1;switch(e.target.type){case 4:var o=e.target.detail,n=e.target.element.offsetLeft,r=o.offsetX-n;if(r<5)return;i=!0;break;case 6:if(this.hiddenRangeModel.hasRanges()){var s=this.editor.getModel();if(s&&t.startColumn===s.getLineMaxColumn(t.startLineNumber))break}return;default:return}this.mouseDownInfo={lineNumber:t.startLineNumber,iconClicked:i}}},t.prototype.onEditorMouseUp=function(e){var t=this,i=this.getFoldingModel();if(i&&this.mouseDownInfo&&e.target){var o=this.mouseDownInfo.lineNumber,n=this.mouseDownInfo.iconClicked,r=e.target.range;if(r&&r.startLineNumber===o){if(n){if(4!==e.target.type)return}else{var s=this.editor.getModel();if(!s||r.startColumn!==s.getLineMaxColumn(o))return}i.then((function(i){if(i){var r=i.getRegionAtLine(o);if(r&&r.startLineNumber===o){var s=r.isCollapsed;if(n||s){var a=[],d=e.event.middleButton||e.event.shiftKey;if(d)for(var l=0,c=i.getRegionsInside(r);l<c.length;l++){var h=c[l];h.isCollapsed===s&&a.push(h)}!s&&d&&0!==a.length||a.push(r),i.toggleCollapseState(a),t.reveal({lineNumber:o,column:1})}}}})).then(void 0,j["e"])}}},t.prototype.reveal=function(e){this.editor.revealPositionInCenterIfOutsideViewport(e,0)},t.ID="editor.contrib.folding",t=J([ee(1,X["c"])],t),t}(d["a"]),oe=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return $(t,e),t.prototype.runEditorCommand=function(e,t,i){var o=this,n=ie.get(t);if(n){var r=n.getFoldingModel();return r?(this.reportTelemetry(e,t),r.then((function(e){if(e){o.invoke(n,e,t,i);var r=t.getSelection();r&&n.reveal(r.getStartPosition())}}))):void 0}},t.prototype.getSelectedLines=function(e){var t=e.getSelections();return t?t.map((function(e){return e.startLineNumber})):[]},t.prototype.getLineNumbers=function(e,t){return e&&e.selectionLines?e.selectionLines.map((function(e){return e+1})):this.getSelectedLines(t)},t.prototype.run=function(e,t){},t}(l["b"]);function ne(e){if(!n["k"](e)){if(!n["i"](e))return!1;var t=e;if(!n["k"](t.levels)&&!n["h"](t.levels))return!1;if(!n["k"](t.direction)&&!n["j"](t.direction))return!1;if(!n["k"](t.selectionLines)&&(!n["d"](t.selectionLines)||!t.selectionLines.every(n["h"])))return!1}return!0}var re=function(e){function t(){return e.call(this,{id:"editor.unfold",label:o["a"]("unfoldAction.label","Unfold"),alias:"Unfold",precondition:te,kbOpts:{kbExpr:O["a"].editorTextFocus,primary:3161,mac:{primary:2649},weight:100},description:{description:"Unfold the content in the editor",args:[{name:"Unfold editor argument",description:"Property-value pairs that can be passed through this argument:\n\t\t\t\t\t\t* 'levels': Number of levels to unfold. If not set, defaults to 1.\n\t\t\t\t\t\t* 'direction': If 'up', unfold given number of levels up otherwise unfolds down.\n\t\t\t\t\t\t* 'selectionLines': The start lines (0-based) of the editor selections to apply the unfold action to. If not set, the active selection(s) will be used.\n\t\t\t\t\t\t",constraint:ne,schema:{type:"object",properties:{levels:{type:"number",default:1},direction:{type:"string",enum:["up","down"],default:"down"},selectionLines:{type:"array",items:{type:"number"}}}}}]}})||this}return $(t,e),t.prototype.invoke=function(e,t,i,o){var n=o&&o.levels||1,r=this.getLineNumbers(o,i);o&&"up"===o.direction?v(t,!1,n,r):b(t,!1,n,r)},t}(oe),se=function(e){function t(){return e.call(this,{id:"editor.unfoldRecursively",label:o["a"]("unFoldRecursivelyAction.label","Unfold Recursively"),alias:"Unfold Recursively",precondition:te,kbOpts:{kbExpr:O["a"].editorTextFocus,primary:Object(a["a"])(2089,2137),weight:100}})||this}return $(t,e),t.prototype.invoke=function(e,t,i,o){b(t,!1,Number.MAX_VALUE,this.getSelectedLines(i))},t}(oe),ae=function(e){function t(){return e.call(this,{id:"editor.fold",label:o["a"]("foldAction.label","Fold"),alias:"Fold",precondition:te,kbOpts:{kbExpr:O["a"].editorTextFocus,primary:3159,mac:{primary:2647},weight:100},description:{description:"Fold the content in the editor",args:[{name:"Fold editor argument",description:"Property-value pairs that can be passed through this argument:\n\t\t\t\t\t\t\t* 'levels': Number of levels to fold.\n\t\t\t\t\t\t\t* 'direction': If 'up', folds given number of levels up otherwise folds down.\n\t\t\t\t\t\t\t* 'selectionLines': The start lines (0-based) of the editor selections to apply the fold action to. If not set, the active selection(s) will be used.\n\t\t\t\t\t\t\tIf no levels or direction is set, folds the region at the locations or if already collapsed, the first uncollapsed parent instead.\n\t\t\t\t\t\t",constraint:ne,schema:{type:"object",properties:{levels:{type:"number"},direction:{type:"string",enum:["up","down"]},selectionLines:{type:"array",items:{type:"number"}}}}}]}})||this}return $(t,e),t.prototype.invoke=function(e,t,i,o){var n=this.getLineNumbers(o,i),r=o&&o.levels,s=o&&o.direction;"number"!==typeof r&&"string"!==typeof s?y(t,!0,n):"up"===s?v(t,!0,r||1,n):b(t,!0,r||1,n)},t}(oe),de=function(e){function t(){return e.call(this,{id:"editor.toggleFold",label:o["a"]("toggleFoldAction.label","Toggle Fold"),alias:"Toggle Fold",precondition:te,kbOpts:{kbExpr:O["a"].editorTextFocus,primary:Object(a["a"])(2089,2090),weight:100}})||this}return $(t,e),t.prototype.invoke=function(e,t,i){var o=this.getSelectedLines(i);m(t,1,o)},t}(oe),le=function(e){function t(){return e.call(this,{id:"editor.foldRecursively",label:o["a"]("foldRecursivelyAction.label","Fold Recursively"),alias:"Fold Recursively",precondition:te,kbOpts:{kbExpr:O["a"].editorTextFocus,primary:Object(a["a"])(2089,2135),weight:100}})||this}return $(t,e),t.prototype.invoke=function(e,t,i){var o=this.getSelectedLines(i);b(t,!0,Number.MAX_VALUE,o)},t}(oe),ce=function(e){function t(){return e.call(this,{id:"editor.foldAllBlockComments",label:o["a"]("foldAllBlockComments.label","Fold All Block Comments"),alias:"Fold All Block Comments",precondition:te,kbOpts:{kbExpr:O["a"].editorTextFocus,primary:Object(a["a"])(2089,2133),weight:100}})||this}return $(t,e),t.prototype.invoke=function(e,t,i){if(t.regions.hasTypes())R(t,W["n"].Comment.value,!0);else{var o=i.getModel();if(!o)return;var n=E["a"].getComments(o.getLanguageIdentifier().id);if(n&&n.blockCommentStartToken){var s=new RegExp("^\\s*"+Object(r["p"])(n.blockCommentStartToken));S(t,s,!0)}}},t}(oe),he=function(e){function t(){return e.call(this,{id:"editor.foldAllMarkerRegions",label:o["a"]("foldAllMarkerRegions.label","Fold All Regions"),alias:"Fold All Regions",precondition:te,kbOpts:{kbExpr:O["a"].editorTextFocus,primary:Object(a["a"])(2089,2077),weight:100}})||this}return $(t,e),t.prototype.invoke=function(e,t,i){if(t.regions.hasTypes())R(t,W["n"].Region.value,!0);else{var o=i.getModel();if(!o)return;var n=E["a"].getFoldingRules(o.getLanguageIdentifier().id);if(n&&n.markers&&n.markers.start){var r=new RegExp(n.markers.start);S(t,r,!0)}}},t}(oe),ue=function(e){function t(){return e.call(this,{id:"editor.unfoldAllMarkerRegions",label:o["a"]("unfoldAllMarkerRegions.label","Unfold All Regions"),alias:"Unfold All Regions",precondition:te,kbOpts:{kbExpr:O["a"].editorTextFocus,primary:Object(a["a"])(2089,2078),weight:100}})||this}return $(t,e),t.prototype.invoke=function(e,t,i){if(t.regions.hasTypes())R(t,W["n"].Region.value,!1);else{var o=i.getModel();if(!o)return;var n=E["a"].getFoldingRules(o.getLanguageIdentifier().id);if(n&&n.markers&&n.markers.start){var r=new RegExp(n.markers.start);S(t,r,!1)}}},t}(oe),ge=function(e){function t(){return e.call(this,{id:"editor.foldAll",label:o["a"]("foldAllAction.label","Fold All"),alias:"Fold All",precondition:te,kbOpts:{kbExpr:O["a"].editorTextFocus,primary:Object(a["a"])(2089,2069),weight:100}})||this}return $(t,e),t.prototype.invoke=function(e,t,i){b(t,!0)},t}(oe),pe=function(e){function t(){return e.call(this,{id:"editor.unfoldAll",label:o["a"]("unfoldAllAction.label","Unfold All"),alias:"Unfold All",precondition:te,kbOpts:{kbExpr:O["a"].editorTextFocus,primary:Object(a["a"])(2089,2088),weight:100}})||this}return $(t,e),t.prototype.invoke=function(e,t,i){b(t,!1)},t}(oe),fe=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return $(t,e),t.prototype.getFoldingLevel=function(){return parseInt(this.id.substr(t.ID_PREFIX.length))},t.prototype.invoke=function(e,t,i){C(t,this.getFoldingLevel(),!0,this.getSelectedLines(i))},t.ID_PREFIX="editor.foldLevel",t.ID=function(e){return t.ID_PREFIX+e},t}(oe);Object(l["h"])(ie.ID,ie),Object(l["f"])(re),Object(l["f"])(se),Object(l["f"])(ae),Object(l["f"])(le),Object(l["f"])(ge),Object(l["f"])(pe),Object(l["f"])(ce),Object(l["f"])(he),Object(l["f"])(ue),Object(l["f"])(de);for(var _e=1;_e<=7;_e++)Object(l["i"])(new fe({id:fe.ID(_e),label:o["a"]("foldLevelAction.label","Fold Level {0}",_e),alias:"Fold Level "+_e,precondition:te,kbOpts:{kbExpr:O["a"].editorTextFocus,primary:Object(a["a"])(2089,2048|21+_e),weight:100}}));var me=Object(Q["Tb"])("editor.foldBackground",{light:Object(Q["fc"])(Q["K"],.3),dark:Object(Q["fc"])(Q["K"],.3),hc:null},o["a"]("editorSelectionBackground","Color of the editor selection."));Object(Y["e"])((function(e,t){var i=e.getColor(me);i&&t.addRule(".monaco-editor .folded-background { background-color: "+i+"; }")}))},"8ea8":function(e,t,i){"use strict";i("c88e"),i("ba3c");var o=i("dff7"),n=i("b7d0"),r=i("303e"),s=Object(r["Tb"])("symbolIcon.arrayForeground",{dark:r["W"],light:r["W"],hc:r["W"]},Object(o["a"])("symbolIcon.arrayForeground","The foreground color for array symbols. These symbols appear in the outline, breadcrumb, and suggest widget.")),a=Object(r["Tb"])("symbolIcon.booleanForeground",{dark:r["W"],light:r["W"],hc:r["W"]},Object(o["a"])("symbolIcon.booleanForeground","The foreground color for boolean symbols. These symbols appear in the outline, breadcrumb, and suggest widget.")),d=Object(r["Tb"])("symbolIcon.classForeground",{dark:"#EE9D28",light:"#D67E00",hc:"#EE9D28"},Object(o["a"])("symbolIcon.classForeground","The foreground color for class symbols. These symbols appear in the outline, breadcrumb, and suggest widget.")),l=Object(r["Tb"])("symbolIcon.colorForeground",{dark:r["W"],light:r["W"],hc:r["W"]},Object(o["a"])("symbolIcon.colorForeground","The foreground color for color symbols. These symbols appear in the outline, breadcrumb, and suggest widget.")),c=Object(r["Tb"])("symbolIcon.constantForeground",{dark:r["W"],light:r["W"],hc:r["W"]},Object(o["a"])("symbolIcon.constantForeground","The foreground color for constant symbols. These symbols appear in the outline, breadcrumb, and suggest widget.")),h=Object(r["Tb"])("symbolIcon.constructorForeground",{dark:"#B180D7",light:"#652D90",hc:"#B180D7"},Object(o["a"])("symbolIcon.constructorForeground","The foreground color for constructor symbols. These symbols appear in the outline, breadcrumb, and suggest widget.")),u=Object(r["Tb"])("symbolIcon.enumeratorForeground",{dark:"#EE9D28",light:"#D67E00",hc:"#EE9D28"},Object(o["a"])("symbolIcon.enumeratorForeground","The foreground color for enumerator symbols. These symbols appear in the outline, breadcrumb, and suggest widget.")),g=Object(r["Tb"])("symbolIcon.enumeratorMemberForeground",{dark:"#75BEFF",light:"#007ACC",hc:"#75BEFF"},Object(o["a"])("symbolIcon.enumeratorMemberForeground","The foreground color for enumerator member symbols. These symbols appear in the outline, breadcrumb, and suggest widget.")),p=Object(r["Tb"])("symbolIcon.eventForeground",{dark:"#EE9D28",light:"#D67E00",hc:"#EE9D28"},Object(o["a"])("symbolIcon.eventForeground","The foreground color for event symbols. These symbols appear in the outline, breadcrumb, and suggest widget.")),f=Object(r["Tb"])("symbolIcon.fieldForeground",{dark:"#75BEFF",light:"#007ACC",hc:"#75BEFF"},Object(o["a"])("symbolIcon.fieldForeground","The foreground color for field symbols. These symbols appear in the outline, breadcrumb, and suggest widget.")),_=Object(r["Tb"])("symbolIcon.fileForeground",{dark:r["W"],light:r["W"],hc:r["W"]},Object(o["a"])("symbolIcon.fileForeground","The foreground color for file symbols. These symbols appear in the outline, breadcrumb, and suggest widget.")),m=Object(r["Tb"])("symbolIcon.folderForeground",{dark:r["W"],light:r["W"],hc:r["W"]},Object(o["a"])("symbolIcon.folderForeground","The foreground color for folder symbols. These symbols appear in the outline, breadcrumb, and suggest widget.")),b=Object(r["Tb"])("symbolIcon.functionForeground",{dark:"#B180D7",light:"#652D90",hc:"#B180D7"},Object(o["a"])("symbolIcon.functionForeground","The foreground color for function symbols. These symbols appear in the outline, breadcrumb, and suggest widget.")),v=Object(r["Tb"])("symbolIcon.interfaceForeground",{dark:"#75BEFF",light:"#007ACC",hc:"#75BEFF"},Object(o["a"])("symbolIcon.interfaceForeground","The foreground color for interface symbols. These symbols appear in the outline, breadcrumb, and suggest widget.")),y=Object(r["Tb"])("symbolIcon.keyForeground",{dark:r["W"],light:r["W"],hc:r["W"]},Object(o["a"])("symbolIcon.keyForeground","The foreground color for key symbols. These symbols appear in the outline, breadcrumb, and suggest widget.")),C=Object(r["Tb"])("symbolIcon.keywordForeground",{dark:r["W"],light:r["W"],hc:r["W"]},Object(o["a"])("symbolIcon.keywordForeground","The foreground color for keyword symbols. These symbols appear in the outline, breadcrumb, and suggest widget.")),S=Object(r["Tb"])("symbolIcon.methodForeground",{dark:"#B180D7",light:"#652D90",hc:"#B180D7"},Object(o["a"])("symbolIcon.methodForeground","The foreground color for method symbols. These symbols appear in the outline, breadcrumb, and suggest widget.")),R=Object(r["Tb"])("symbolIcon.moduleForeground",{dark:r["W"],light:r["W"],hc:r["W"]},Object(o["a"])("symbolIcon.moduleForeground","The foreground color for module symbols. These symbols appear in the outline, breadcrumb, and suggest widget.")),w=Object(r["Tb"])("symbolIcon.namespaceForeground",{dark:r["W"],light:r["W"],hc:r["W"]},Object(o["a"])("symbolIcon.namespaceForeground","The foreground color for namespace symbols. These symbols appear in the outline, breadcrumb, and suggest widget.")),I=Object(r["Tb"])("symbolIcon.nullForeground",{dark:r["W"],light:r["W"],hc:r["W"]},Object(o["a"])("symbolIcon.nullForeground","The foreground color for null symbols. These symbols appear in the outline, breadcrumb, and suggest widget.")),O=Object(r["Tb"])("symbolIcon.numberForeground",{dark:r["W"],light:r["W"],hc:r["W"]},Object(o["a"])("symbolIcon.numberForeground","The foreground color for number symbols. These symbols appear in the outline, breadcrumb, and suggest widget.")),N=Object(r["Tb"])("symbolIcon.objectForeground",{dark:r["W"],light:r["W"],hc:r["W"]},Object(o["a"])("symbolIcon.objectForeground","The foreground color for object symbols. These symbols appear in the outline, breadcrumb, and suggest widget.")),x=Object(r["Tb"])("symbolIcon.operatorForeground",{dark:r["W"],light:r["W"],hc:r["W"]},Object(o["a"])("symbolIcon.operatorForeground","The foreground color for operator symbols. These symbols appear in the outline, breadcrumb, and suggest widget.")),F=Object(r["Tb"])("symbolIcon.packageForeground",{dark:r["W"],light:r["W"],hc:r["W"]},Object(o["a"])("symbolIcon.packageForeground","The foreground color for package symbols. These symbols appear in the outline, breadcrumb, and suggest widget.")),M=Object(r["Tb"])("symbolIcon.propertyForeground",{dark:r["W"],light:r["W"],hc:r["W"]},Object(o["a"])("symbolIcon.propertyForeground","The foreground color for property symbols. These symbols appear in the outline, breadcrumb, and suggest widget.")),D=Object(r["Tb"])("symbolIcon.referenceForeground",{dark:r["W"],light:r["W"],hc:r["W"]},Object(o["a"])("symbolIcon.referenceForeground","The foreground color for reference symbols. These symbols appear in the outline, breadcrumb, and suggest widget.")),E=Object(r["Tb"])("symbolIcon.snippetForeground",{dark:r["W"],light:r["W"],hc:r["W"]},Object(o["a"])("symbolIcon.snippetForeground","The foreground color for snippet symbols. These symbols appear in the outline, breadcrumb, and suggest widget.")),L=Object(r["Tb"])("symbolIcon.stringForeground",{dark:r["W"],light:r["W"],hc:r["W"]},Object(o["a"])("symbolIcon.stringForeground","The foreground color for string symbols. These symbols appear in the outline, breadcrumb, and suggest widget.")),P=Object(r["Tb"])("symbolIcon.structForeground",{dark:r["W"],light:r["W"],hc:r["W"]},Object(o["a"])("symbolIcon.structForeground","The foreground color for struct symbols. These symbols appear in the outline, breadcrumb, and suggest widget.")),T=Object(r["Tb"])("symbolIcon.textForeground",{dark:r["W"],light:r["W"],hc:r["W"]},Object(o["a"])("symbolIcon.textForeground","The foreground color for text symbols. These symbols appear in the outline, breadcrumb, and suggest widget.")),k=Object(r["Tb"])("symbolIcon.typeParameterForeground",{dark:r["W"],light:r["W"],hc:r["W"]},Object(o["a"])("symbolIcon.typeParameterForeground","The foreground color for type parameter symbols. These symbols appear in the outline, breadcrumb, and suggest widget.")),A=Object(r["Tb"])("symbolIcon.unitForeground",{dark:r["W"],light:r["W"],hc:r["W"]},Object(o["a"])("symbolIcon.unitForeground","The foreground color for unit symbols. These symbols appear in the outline, breadcrumb, and suggest widget.")),W=Object(r["Tb"])("symbolIcon.variableForeground",{dark:"#75BEFF",light:"#007ACC",hc:"#75BEFF"},Object(o["a"])("symbolIcon.variableForeground","The foreground color for variable symbols. These symbols appear in the outline, breadcrumb, and suggest widget."));Object(n["e"])((function(e,t){var i=e.getColor(s);i&&t.addRule(".codicon-symbol-array { color: "+i+" !important; }");var o=e.getColor(a);o&&t.addRule(".codicon-symbol-boolean { color: "+o+" !important; }");var n=e.getColor(d);n&&t.addRule(".codicon-symbol-class { color: "+n+" !important; }");var r=e.getColor(S);r&&t.addRule(".codicon-symbol-method { color: "+r+" !important; }");var j=e.getColor(l);j&&t.addRule(".codicon-symbol-color { color: "+j+" !important; }");var B=e.getColor(c);B&&t.addRule(".codicon-symbol-constant { color: "+B+" !important; }");var V=e.getColor(h);V&&t.addRule(".codicon-symbol-constructor { color: "+V+" !important; }");var H=e.getColor(u);H&&t.addRule("\n\t\t\t.codicon-symbol-value,.codicon-symbol-enum { color: "+H+" !important; }");var U=e.getColor(g);U&&t.addRule(".codicon-symbol-enum-member { color: "+U+" !important; }");var q=e.getColor(p);q&&t.addRule(".codicon-symbol-event { color: "+q+" !important; }");var Z=e.getColor(f);Z&&t.addRule(".codicon-symbol-field { color: "+Z+" !important; }");var K=e.getColor(_);K&&t.addRule(".codicon-symbol-file { color: "+K+" !important; }");var z=e.getColor(m);z&&t.addRule(".codicon-symbol-folder { color: "+z+" !important; }");var G=e.getColor(b);G&&t.addRule(".codicon-symbol-function { color: "+G+" !important; }");var X=e.getColor(v);X&&t.addRule(".codicon-symbol-interface { color: "+X+" !important; }");var Y=e.getColor(y);Y&&t.addRule(".codicon-symbol-key { color: "+Y+" !important; }");var Q=e.getColor(C);Q&&t.addRule(".codicon-symbol-keyword { color: "+Q+" !important; }");var $=e.getColor(R);$&&t.addRule(".codicon-symbol-module { color: "+$+" !important; }");var J=e.getColor(w);J&&t.addRule(".codicon-symbol-namespace { color: "+J+" !important; }");var ee=e.getColor(I);ee&&t.addRule(".codicon-symbol-null { color: "+ee+" !important; }");var te=e.getColor(O);te&&t.addRule(".codicon-symbol-number { color: "+te+" !important; }");var ie=e.getColor(N);ie&&t.addRule(".codicon-symbol-object { color: "+ie+" !important; }");var oe=e.getColor(x);oe&&t.addRule(".codicon-symbol-operator { color: "+oe+" !important; }");var ne=e.getColor(F);ne&&t.addRule(".codicon-symbol-package { color: "+ne+" !important; }");var re=e.getColor(M);re&&t.addRule(".codicon-symbol-property { color: "+re+" !important; }");var se=e.getColor(D);se&&t.addRule(".codicon-symbol-reference { color: "+se+" !important; }");var ae=e.getColor(E);ae&&t.addRule(".codicon-symbol-snippet { color: "+ae+" !important; }");var de=e.getColor(L);de&&t.addRule(".codicon-symbol-string { color: "+de+" !important; }");var le=e.getColor(P);le&&t.addRule(".codicon-symbol-struct { color: "+le+" !important; }");var ce=e.getColor(T);ce&&t.addRule(".codicon-symbol-text { color: "+ce+" !important; }");var he=e.getColor(k);he&&t.addRule(".codicon-symbol-type-parameter { color: "+he+" !important; }");var ue=e.getColor(A);ue&&t.addRule(".codicon-symbol-unit { color: "+ue+" !important; }");var ge=e.getColor(W);ge&&t.addRule(".codicon-symbol-variable { color: "+ge+" !important; }")}))},a106:function(e,t,i){"use strict";i.r(t),i.d(t,"getSelectionSearchString",(function(){return Ae})),i.d(t,"CommonFindController",(function(){return We})),i.d(t,"FindController",(function(){return je})),i.d(t,"StartFindAction",(function(){return Be})),i.d(t,"StartFindWithSelectionAction",(function(){return Ve})),i.d(t,"MatchFindAction",(function(){return He})),i.d(t,"NextMatchFindAction",(function(){return Ue})),i.d(t,"NextMatchFindAction2",(function(){return qe})),i.d(t,"PreviousMatchFindAction",(function(){return Ze})),i.d(t,"PreviousMatchFindAction2",(function(){return Ke})),i.d(t,"SelectionMatchFindAction",(function(){return ze})),i.d(t,"NextSelectionMatchFindAction",(function(){return Ge})),i.d(t,"PreviousSelectionMatchFindAction",(function(){return Xe})),i.d(t,"StartFindReplaceAction",(function(){return Ye}));var o=i("dff7"),n=i("5fe7"),r=i("a666"),s=i("3742"),a=i("b2cc"),d=i("c101"),l=i("2c29"),c=i("7061"),h=i("6a89"),u=i("8025"),g=i("8c027"),p=i("3352"),f=i("b57f"),_=i("303e"),m=i("b7d0"),b=function(){function e(e){this._editor=e,this._decorations=[],this._overviewRulerApproximateDecorations=[],this._findScopeDecorationId=null,this._rangeHighlightDecorationId=null,this._highlightedDecorationId=null,this._startPosition=this._editor.getPosition()}return e.prototype.dispose=function(){this._editor.deltaDecorations(this._allDecorations(),[]),this._decorations=[],this._overviewRulerApproximateDecorations=[],this._findScopeDecorationId=null,this._rangeHighlightDecorationId=null,this._highlightedDecorationId=null},e.prototype.reset=function(){this._decorations=[],this._overviewRulerApproximateDecorations=[],this._findScopeDecorationId=null,this._rangeHighlightDecorationId=null,this._highlightedDecorationId=null},e.prototype.getCount=function(){return this._decorations.length},e.prototype.getFindScope=function(){return this._findScopeDecorationId?this._editor.getModel().getDecorationRange(this._findScopeDecorationId):null},e.prototype.getStartPosition=function(){return this._startPosition},e.prototype.setStartPosition=function(e){this._startPosition=e,this.setCurrentFindMatch(null)},e.prototype._getDecorationIndex=function(e){var t=this._decorations.indexOf(e);return t>=0?t+1:1},e.prototype.getCurrentMatchesPosition=function(t){for(var i=this._editor.getModel().getDecorationsInRange(t),o=0,n=i;o<n.length;o++){var r=n[o],s=r.options;if(s===e._FIND_MATCH_DECORATION||s===e._CURRENT_FIND_MATCH_DECORATION)return this._getDecorationIndex(r.id)}return 1},e.prototype.setCurrentFindMatch=function(t){var i=this,o=null,n=0;if(t)for(var r=0,s=this._decorations.length;r<s;r++){var a=this._editor.getModel().getDecorationRange(this._decorations[r]);if(t.equalsRange(a)){o=this._decorations[r],n=r+1;break}}return null===this._highlightedDecorationId&&null===o||this._editor.changeDecorations((function(t){if(null!==i._highlightedDecorationId&&(t.changeDecorationOptions(i._highlightedDecorationId,e._FIND_MATCH_DECORATION),i._highlightedDecorationId=null),null!==o&&(i._highlightedDecorationId=o,t.changeDecorationOptions(i._highlightedDecorationId,e._CURRENT_FIND_MATCH_DECORATION)),null!==i._rangeHighlightDecorationId&&(t.removeDecoration(i._rangeHighlightDecorationId),i._rangeHighlightDecorationId=null),null!==o){var n=i._editor.getModel().getDecorationRange(o);if(n.startLineNumber!==n.endLineNumber&&1===n.endColumn){var r=n.endLineNumber-1,s=i._editor.getModel().getLineMaxColumn(r);n=new h["a"](n.startLineNumber,n.startColumn,r,s)}i._rangeHighlightDecorationId=t.addDecoration(n,e._RANGE_HIGHLIGHT_DECORATION)}})),n},e.prototype.set=function(t,i){var o=this;this._editor.changeDecorations((function(n){var r=e._FIND_MATCH_DECORATION,s=[];if(t.length>1e3){r=e._FIND_MATCH_NO_OVERVIEW_DECORATION;for(var a=o._editor.getModel().getLineCount(),d=o._editor.getLayoutInfo().height,l=d/a,c=Math.max(2,Math.ceil(3/l)),u=t[0].range.startLineNumber,g=t[0].range.endLineNumber,p=1,f=t.length;p<f;p++){var _=t[p].range;g+c>=_.startLineNumber?_.endLineNumber>g&&(g=_.endLineNumber):(s.push({range:new h["a"](u,1,g,1),options:e._FIND_MATCH_ONLY_OVERVIEW_DECORATION}),u=_.startLineNumber,g=_.endLineNumber)}s.push({range:new h["a"](u,1,g,1),options:e._FIND_MATCH_ONLY_OVERVIEW_DECORATION})}var m=new Array(t.length);for(p=0,f=t.length;p<f;p++)m[p]={range:t[p].range,options:r};o._decorations=n.deltaDecorations(o._decorations,m),o._overviewRulerApproximateDecorations=n.deltaDecorations(o._overviewRulerApproximateDecorations,s),o._rangeHighlightDecorationId&&(n.removeDecoration(o._rangeHighlightDecorationId),o._rangeHighlightDecorationId=null),o._findScopeDecorationId&&(n.removeDecoration(o._findScopeDecorationId),o._findScopeDecorationId=null),i&&(o._findScopeDecorationId=n.addDecoration(i,e._FIND_SCOPE_DECORATION))}))},e.prototype.matchBeforePosition=function(e){if(0===this._decorations.length)return null;for(var t=this._decorations.length-1;t>=0;t--){var i=this._decorations[t],o=this._editor.getModel().getDecorationRange(i);if(o&&!(o.endLineNumber>e.lineNumber)){if(o.endLineNumber<e.lineNumber)return o;if(!(o.endColumn>e.column))return o}}return this._editor.getModel().getDecorationRange(this._decorations[this._decorations.length-1])},e.prototype.matchAfterPosition=function(e){if(0===this._decorations.length)return null;for(var t=0,i=this._decorations.length;t<i;t++){var o=this._decorations[t],n=this._editor.getModel().getDecorationRange(o);if(n&&!(n.startLineNumber<e.lineNumber)){if(n.startLineNumber>e.lineNumber)return n;if(!(n.startColumn<e.column))return n}}return this._editor.getModel().getDecorationRange(this._decorations[0])},e.prototype._allDecorations=function(){var e=[];return e=e.concat(this._decorations),e=e.concat(this._overviewRulerApproximateDecorations),this._findScopeDecorationId&&e.push(this._findScopeDecorationId),this._rangeHighlightDecorationId&&e.push(this._rangeHighlightDecorationId),e},e._CURRENT_FIND_MATCH_DECORATION=f["a"].register({stickiness:1,zIndex:13,className:"currentFindMatch",showIfCollapsed:!0,overviewRuler:{color:Object(m["f"])(_["Lb"]),position:p["d"].Center},minimap:{color:Object(m["f"])(_["Hb"]),position:p["c"].Inline}}),e._FIND_MATCH_DECORATION=f["a"].register({stickiness:1,className:"findMatch",showIfCollapsed:!0,overviewRuler:{color:Object(m["f"])(_["Lb"]),position:p["d"].Center},minimap:{color:Object(m["f"])(_["Hb"]),position:p["c"].Inline}}),e._FIND_MATCH_NO_OVERVIEW_DECORATION=f["a"].register({stickiness:1,className:"findMatch",showIfCollapsed:!0}),e._FIND_MATCH_ONLY_OVERVIEW_DECORATION=f["a"].register({stickiness:1,overviewRuler:{color:Object(m["f"])(_["Lb"]),position:p["d"].Center}}),e._RANGE_HIGHLIGHT_DECORATION=f["a"].register({stickiness:1,className:"rangeHighlight",isWholeLine:!0}),e._FIND_SCOPE_DECORATION=f["a"].register({className:"findScope",isWholeLine:!0}),e}(),v=function(){function e(e,t,i){this._editorSelection=e,this._ranges=t,this._replaceStrings=i,this._trackedEditorSelectionId=null}return e.prototype.getEditOperations=function(e,t){if(this._ranges.length>0){for(var i=[],o=0;o<this._ranges.length;o++)i.push({range:this._ranges[o],text:this._replaceStrings[o]});i.sort((function(e,t){return h["a"].compareRangesUsingStarts(e.range,t.range)}));var n=[],r=i[0];for(o=1;o<i.length;o++)r.range.endLineNumber===i[o].range.startLineNumber&&r.range.endColumn===i[o].range.startColumn?(r.range=r.range.plusRange(i[o].range),r.text=r.text+i[o].text):(n.push(r),r=i[o]);n.push(r);for(var s=0,a=n;s<a.length;s++){var d=a[s];t.addEditOperation(d.range,d.text)}}this._trackedEditorSelectionId=t.trackSelection(this._editorSelection)},e.prototype.computeCursorState=function(e,t){return t.getTrackedSelection(this._trackedEditorSelectionId)},e}(),y=i("10b9"),C=function(){function e(e){this.staticValue=e,this.kind=0}return e}(),S=function(){function e(e){this.pieces=e,this.kind=1}return e}(),R=function(){function e(e){e&&0!==e.length?1===e.length&&null!==e[0].staticValue?this._state=new C(e[0].staticValue):this._state=new S(e):this._state=new C("")}return e.fromStaticValue=function(t){return new e([w.staticValue(t)])},Object.defineProperty(e.prototype,"hasReplacementPatterns",{get:function(){return 1===this._state.kind},enumerable:!0,configurable:!0}),e.prototype.buildReplaceString=function(t,i){if(0===this._state.kind)return i?Object(y["a"])(t,this._state.staticValue):this._state.staticValue;for(var o="",n=0,r=this._state.pieces.length;n<r;n++){var s=this._state.pieces[n];null===s.staticValue?o+=e._substitute(s.matchIndex,t):o+=s.staticValue}return o},e._substitute=function(e,t){if(null===t)return"";if(0===e)return t[0];var i="";while(e>0){if(e<t.length){var o=t[e]||"";return o+i}i=String(e%10)+i,e=Math.floor(e/10)}return"$"+i},e}(),w=function(){function e(e,t){this.staticValue=e,this.matchIndex=t}return e.staticValue=function(t){return new e(t,-1)},e.matchIndex=function(t){return new e(null,t)},e}(),I=function(){function e(e){this._source=e,this._lastCharIndex=0,this._result=[],this._resultLen=0,this._currentStaticPiece=""}return e.prototype.emitUnchanged=function(e){this._emitStatic(this._source.substring(this._lastCharIndex,e)),this._lastCharIndex=e},e.prototype.emitStatic=function(e,t){this._emitStatic(e),this._lastCharIndex=t},e.prototype._emitStatic=function(e){0!==e.length&&(this._currentStaticPiece+=e)},e.prototype.emitMatchIndex=function(e,t){0!==this._currentStaticPiece.length&&(this._result[this._resultLen++]=w.staticValue(this._currentStaticPiece),this._currentStaticPiece=""),this._result[this._resultLen++]=w.matchIndex(e),this._lastCharIndex=t},e.prototype.finalize=function(){return this.emitUnchanged(this._source.length),0!==this._currentStaticPiece.length&&(this._result[this._resultLen++]=w.staticValue(this._currentStaticPiece),this._currentStaticPiece=""),new R(this._result)},e}();function O(e){if(!e||0===e.length)return new R(null);for(var t=new I(e),i=0,o=e.length;i<o;i++){var n=e.charCodeAt(i);if(92!==n){if(36===n){if(i++,i>=o)break;a=e.charCodeAt(i);if(36===a){t.emitUnchanged(i-1),t.emitStatic("$",i+1);continue}if(48===a||38===a){t.emitUnchanged(i-1),t.emitMatchIndex(0,i+1);continue}if(49<=a&&a<=57){var r=a-48;if(i+1<o){var s=e.charCodeAt(i+1);if(48<=s&&s<=57){i++,r=10*r+(s-48),t.emitUnchanged(i-2),t.emitMatchIndex(r,i+1);continue}}t.emitUnchanged(i-1),t.emitMatchIndex(r,i+1);continue}}}else{if(i++,i>=o)break;var a=e.charCodeAt(i);switch(a){case 92:t.emitUnchanged(i-1),t.emitStatic("\\",i+1);break;case 110:t.emitUnchanged(i-1),t.emitStatic("\n",i+1);break;case 116:t.emitUnchanged(i-1),t.emitStatic("\t",i+1);break}}}return t.finalize()}var N=i("4fc3"),x=new N["d"]("findWidgetVisible",!1),F=new N["d"]("findInputFocussed",!1),M=new N["d"]("replaceInputFocussed",!1),D={primary:545,mac:{primary:2593}},E={primary:565,mac:{primary:2613}},L={primary:560,mac:{primary:2608}},P={primary:554,mac:{primary:2602}},T={StartFindAction:"actions.find",StartFindWithSelection:"actions.findWithSelection",NextMatchFindAction:"editor.action.nextMatchFindAction",PreviousMatchFindAction:"editor.action.previousMatchFindAction",NextSelectionMatchFindAction:"editor.action.nextSelectionMatchFindAction",PreviousSelectionMatchFindAction:"editor.action.previousSelectionMatchFindAction",StartFindReplaceAction:"editor.action.startFindReplaceAction",CloseFindWidgetCommand:"closeFindWidget",ToggleCaseSensitiveCommand:"toggleFindCaseSensitive",ToggleWholeWordCommand:"toggleFindWholeWord",ToggleRegexCommand:"toggleFindRegex",ToggleSearchScopeCommand:"toggleFindInSelection",TogglePreserveCaseCommand:"togglePreserveCase",ReplaceOneAction:"editor.action.replaceOne",ReplaceAllAction:"editor.action.replaceAll",SelectAllMatchesAction:"editor.action.selectAllMatches"},k=19999,A=240,W=function(){function e(e,t){var i=this;this._toDispose=new r["b"],this._editor=e,this._state=t,this._isDisposed=!1,this._startSearchingTimer=new n["e"],this._decorations=new b(e),this._toDispose.add(this._decorations),this._updateDecorationsScheduler=new n["d"]((function(){return i.research(!1)}),100),this._toDispose.add(this._updateDecorationsScheduler),this._toDispose.add(this._editor.onDidChangeCursorPosition((function(e){3!==e.reason&&5!==e.reason&&6!==e.reason||i._decorations.setStartPosition(i._editor.getPosition())}))),this._ignoreModelContentChanged=!1,this._toDispose.add(this._editor.onDidChangeModelContent((function(e){i._ignoreModelContentChanged||(e.isFlush&&i._decorations.reset(),i._decorations.setStartPosition(i._editor.getPosition()),i._updateDecorationsScheduler.schedule())}))),this._toDispose.add(this._state.onFindReplaceStateChange((function(e){return i._onStateChanged(e)}))),this.research(!1,this._state.searchScope)}return e.prototype.dispose=function(){this._isDisposed=!0,Object(r["f"])(this._startSearchingTimer),this._toDispose.dispose()},e.prototype._onStateChanged=function(e){var t=this;if(!this._isDisposed&&this._editor.hasModel()&&(e.searchString||e.isReplaceRevealed||e.isRegex||e.wholeWord||e.matchCase||e.searchScope)){var i=this._editor.getModel();i.isTooLargeForSyncing()?(this._startSearchingTimer.cancel(),this._startSearchingTimer.setIfNotSet((function(){e.searchScope?t.research(e.moveCursor,t._state.searchScope):t.research(e.moveCursor)}),A)):e.searchScope?this.research(e.moveCursor,this._state.searchScope):this.research(e.moveCursor)}},e._getSearchRange=function(e,t){return t||e.getFullModelRange()},e.prototype.research=function(e,t){var i=null;i="undefined"!==typeof t?t:this._decorations.getFindScope(),null!==i&&i.startLineNumber!==i.endLineNumber&&(i=1===i.endColumn?new h["a"](i.startLineNumber,1,i.endLineNumber-1,this._editor.getModel().getLineMaxColumn(i.endLineNumber-1)):new h["a"](i.startLineNumber,1,i.endLineNumber,this._editor.getModel().getLineMaxColumn(i.endLineNumber)));var o=this._findMatches(i,!1,k);this._decorations.set(o,i),this._state.changeMatchInfo(this._decorations.getCurrentMatchesPosition(this._editor.getSelection()),this._decorations.getCount(),void 0),e&&this._moveToNextMatch(this._decorations.getStartPosition())},e.prototype._hasMatches=function(){return this._state.matchesCount>0},e.prototype._cannotFind=function(){if(!this._hasMatches()){var e=this._decorations.getFindScope();return e&&this._editor.revealRangeInCenterIfOutsideViewport(e,0),!0}return!1},e.prototype._setCurrentFindMatch=function(e){var t=this._decorations.setCurrentFindMatch(e);this._state.changeMatchInfo(t,this._decorations.getCount(),e),this._editor.setSelection(e),this._editor.revealRangeInCenterIfOutsideViewport(e,0)},e.prototype._prevSearchPosition=function(e){var t=this._state.isRegex&&(this._state.searchString.indexOf("^")>=0||this._state.searchString.indexOf("$")>=0),i=e.lineNumber,o=e.column,n=this._editor.getModel();return t||1===o?(1===i?i=n.getLineCount():i--,o=n.getLineMaxColumn(i)):o--,new c["a"](i,o)},e.prototype._moveToPrevMatch=function(t,i){if(void 0===i&&(i=!1),this._decorations.getCount()<k){var o=this._decorations.matchBeforePosition(t);return o&&o.isEmpty()&&o.getStartPosition().equals(t)&&(t=this._prevSearchPosition(t),o=this._decorations.matchBeforePosition(t)),void(o&&this._setCurrentFindMatch(o))}if(!this._cannotFind()){var n=this._decorations.getFindScope(),r=e._getSearchRange(this._editor.getModel(),n);r.getEndPosition().isBefore(t)&&(t=r.getEndPosition()),t.isBefore(r.getStartPosition())&&(t=r.getEndPosition());var s=t.lineNumber,a=t.column,d=this._editor.getModel(),l=new c["a"](s,a),h=d.findPreviousMatch(this._state.searchString,l,this._state.isRegex,this._state.matchCase,this._state.wholeWord?this._editor.getOption(96):null,!1);if(h&&h.range.isEmpty()&&h.range.getStartPosition().equals(l)&&(l=this._prevSearchPosition(l),h=d.findPreviousMatch(this._state.searchString,l,this._state.isRegex,this._state.matchCase,this._state.wholeWord?this._editor.getOption(96):null,!1)),h)return i||r.containsRange(h.range)?void this._setCurrentFindMatch(h.range):this._moveToPrevMatch(h.range.getStartPosition(),!0)}},e.prototype.moveToPrevMatch=function(){this._moveToPrevMatch(this._editor.getSelection().getStartPosition())},e.prototype._nextSearchPosition=function(e){var t=this._state.isRegex&&(this._state.searchString.indexOf("^")>=0||this._state.searchString.indexOf("$")>=0),i=e.lineNumber,o=e.column,n=this._editor.getModel();return t||o===n.getLineMaxColumn(i)?(i===n.getLineCount()?i=1:i++,o=1):o++,new c["a"](i,o)},e.prototype._moveToNextMatch=function(e){if(this._decorations.getCount()<k){var t=this._decorations.matchAfterPosition(e);return t&&t.isEmpty()&&t.getStartPosition().equals(e)&&(e=this._nextSearchPosition(e),t=this._decorations.matchAfterPosition(e)),void(t&&this._setCurrentFindMatch(t))}var i=this._getNextMatch(e,!1,!0);i&&this._setCurrentFindMatch(i.range)},e.prototype._getNextMatch=function(t,i,o,n){if(void 0===n&&(n=!1),this._cannotFind())return null;var r=this._decorations.getFindScope(),s=e._getSearchRange(this._editor.getModel(),r);s.getEndPosition().isBefore(t)&&(t=s.getStartPosition()),t.isBefore(s.getStartPosition())&&(t=s.getStartPosition());var a=t.lineNumber,d=t.column,l=this._editor.getModel(),h=new c["a"](a,d),u=l.findNextMatch(this._state.searchString,h,this._state.isRegex,this._state.matchCase,this._state.wholeWord?this._editor.getOption(96):null,i);return o&&u&&u.range.isEmpty()&&u.range.getStartPosition().equals(h)&&(h=this._nextSearchPosition(h),u=l.findNextMatch(this._state.searchString,h,this._state.isRegex,this._state.matchCase,this._state.wholeWord?this._editor.getOption(96):null,i)),u?n||s.containsRange(u.range)?u:this._getNextMatch(u.range.getEndPosition(),i,o,!0):null},e.prototype.moveToNextMatch=function(){this._moveToNextMatch(this._editor.getSelection().getEndPosition())},e.prototype._getReplacePattern=function(){return this._state.isRegex?O(this._state.replaceString):R.fromStaticValue(this._state.replaceString)},e.prototype.replace=function(){if(this._hasMatches()){var e=this._getReplacePattern(),t=this._editor.getSelection(),i=this._getNextMatch(t.getStartPosition(),!0,!1);if(i)if(t.equalsRange(i.range)){var o=e.buildReplaceString(i.matches,this._state.preserveCase),n=new l["a"](t,o);this._executeEditorCommand("replace",n),this._decorations.setStartPosition(new c["a"](t.startLineNumber,t.startColumn+o.length)),this.research(!0)}else this._decorations.setStartPosition(this._editor.getPosition()),this._setCurrentFindMatch(i.range)}},e.prototype._findMatches=function(t,i,o){var n=e._getSearchRange(this._editor.getModel(),t);return this._editor.getModel().findMatches(this._state.searchString,n,this._state.isRegex,this._state.matchCase,this._state.wholeWord?this._editor.getOption(96):null,i,o)},e.prototype.replaceAll=function(){if(this._hasMatches()){var e=this._decorations.getFindScope();null===e&&this._state.matchesCount>=k?this._largeReplaceAll():this._regularReplaceAll(e),this.research(!1)}},e.prototype._largeReplaceAll=function(){var e=new g["a"](this._state.searchString,this._state.isRegex,this._state.matchCase,this._state.wholeWord?this._editor.getOption(96):null),t=e.parseSearchRequest();if(t){var i=t.regex;if(!i.multiline){var o="mu";i.ignoreCase&&(o+="i"),i.global&&(o+="g"),i=new RegExp(i.source,o)}var n,r=this._editor.getModel(),s=r.getValue(1),a=r.getFullModelRange(),d=this._getReplacePattern(),c=this._state.preserveCase;n=d.hasReplacementPatterns||c?s.replace(i,(function(){return d.buildReplaceString(arguments,c)})):s.replace(i,d.buildReplaceString(null,c));var h=new l["b"](a,n,this._editor.getSelection());this._executeEditorCommand("replaceAll",h)}},e.prototype._regularReplaceAll=function(e){for(var t=this._getReplacePattern(),i=this._findMatches(e,t.hasReplacementPatterns||this._state.preserveCase,1073741824),o=[],n=0,r=i.length;n<r;n++)o[n]=t.buildReplaceString(i[n].matches,this._state.preserveCase);var s=new v(this._editor.getSelection(),i.map((function(e){return e.range})),o);this._executeEditorCommand("replaceAll",s)},e.prototype.selectAllMatches=function(){if(this._hasMatches()){for(var e=this._decorations.getFindScope(),t=this._findMatches(e,!1,1073741824),i=t.map((function(e){return new u["a"](e.range.startLineNumber,e.range.startColumn,e.range.endLineNumber,e.range.endColumn)})),o=this._editor.getSelection(),n=0,r=i.length;n<r;n++){var s=i[n];if(s.equalsRange(o)){i=[o].concat(i.slice(0,n)).concat(i.slice(n+1));break}}this._editor.setSelections(i)}},e.prototype._executeEditorCommand=function(e,t){try{this._ignoreModelContentChanged=!0,this._editor.pushUndoStop(),this._editor.executeCommand(e,t),this._editor.pushUndoStop()}finally{this._ignoreModelContentChanged=!1}},e}(),j=i("11f7"),B=i("e473"),V=i("1b7d"),H=function(){var e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])},e(t,i)};return function(t,i){function o(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(o.prototype=i.prototype,new o)}}(),U=function(e){function t(t,i,o,r){var s=e.call(this)||this;s._hideSoon=s._register(new n["d"]((function(){return s._hide()}),2e3)),s._isVisible=!1,s._editor=t,s._state=i,s._keybindingService=o,s._domNode=document.createElement("div"),s._domNode.className="findOptionsWidget",s._domNode.style.display="none",s._domNode.style.top="10px",s._domNode.setAttribute("role","presentation"),s._domNode.setAttribute("aria-hidden","true");var a=r.getTheme().getColor(_["Y"]),d=r.getTheme().getColor(_["X"]);return s.caseSensitive=s._register(new B["a"]({appendTitle:s._keybindingLabelFor(T.ToggleCaseSensitiveCommand),isChecked:s._state.matchCase,inputActiveOptionBorder:a,inputActiveOptionBackground:d})),s._domNode.appendChild(s.caseSensitive.domNode),s._register(s.caseSensitive.onChange((function(){s._state.change({matchCase:s.caseSensitive.checked},!1)}))),s.wholeWords=s._register(new B["c"]({appendTitle:s._keybindingLabelFor(T.ToggleWholeWordCommand),isChecked:s._state.wholeWord,inputActiveOptionBorder:a,inputActiveOptionBackground:d})),s._domNode.appendChild(s.wholeWords.domNode),s._register(s.wholeWords.onChange((function(){s._state.change({wholeWord:s.wholeWords.checked},!1)}))),s.regex=s._register(new B["b"]({appendTitle:s._keybindingLabelFor(T.ToggleRegexCommand),isChecked:s._state.isRegex,inputActiveOptionBorder:a,inputActiveOptionBackground:d})),s._domNode.appendChild(s.regex.domNode),s._register(s.regex.onChange((function(){s._state.change({isRegex:s.regex.checked},!1)}))),s._editor.addOverlayWidget(s),s._register(s._state.onFindReplaceStateChange((function(e){var t=!1;e.isRegex&&(s.regex.checked=s._state.isRegex,t=!0),e.wholeWord&&(s.wholeWords.checked=s._state.wholeWord,t=!0),e.matchCase&&(s.caseSensitive.checked=s._state.matchCase,t=!0),!s._state.isRevealed&&t&&s._revealTemporarily()}))),s._register(j["k"](s._domNode,(function(e){return s._onMouseOut()}))),s._register(j["j"](s._domNode,"mouseover",(function(e){return s._onMouseOver()}))),s._applyTheme(r.getTheme()),s._register(r.onThemeChange(s._applyTheme.bind(s))),s}return H(t,e),t.prototype._keybindingLabelFor=function(e){var t=this._keybindingService.lookupKeybinding(e);return t?" ("+t.getLabel()+")":""},t.prototype.dispose=function(){this._editor.removeOverlayWidget(this),e.prototype.dispose.call(this)},t.prototype.getId=function(){return t.ID},t.prototype.getDomNode=function(){return this._domNode},t.prototype.getPosition=function(){return{preference:0}},t.prototype.highlightFindOptions=function(){this._revealTemporarily()},t.prototype._revealTemporarily=function(){this._show(),this._hideSoon.schedule()},t.prototype._onMouseOut=function(){this._hideSoon.schedule()},t.prototype._onMouseOver=function(){this._hideSoon.cancel()},t.prototype._show=function(){this._isVisible||(this._isVisible=!0,this._domNode.style.display="block")},t.prototype._hide=function(){this._isVisible&&(this._isVisible=!1,this._domNode.style.display="none")},t.prototype._applyTheme=function(e){var t={inputActiveOptionBorder:e.getColor(_["Y"]),inputActiveOptionBackground:e.getColor(_["X"])};this.caseSensitive.style(t),this.wholeWords.style(t),this.regex.style(t)},t.ID="editor.contrib.findOptionsWidget",t}(V["a"]);Object(m["e"])((function(e,t){var i=e.getColor(_["Q"]);i&&t.addRule(".monaco-editor .findOptionsWidget { background-color: "+i+"; }");var o=e.getColor(_["S"]);o&&t.addRule(".monaco-editor .findOptionsWidget { color: "+o+"; }");var n=e.getColor(_["hc"]);n&&t.addRule(".monaco-editor .findOptionsWidget { box-shadow: 0 2px 8px "+n+"; }");var r=e.getColor(_["e"]);r&&t.addRule(".monaco-editor .findOptionsWidget { border: 2px solid "+r+"; }")}));var q=i("308f"),Z=function(){var e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])},e(t,i)};return function(t,i){function o(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(o.prototype=i.prototype,new o)}}();function K(e,t){return 1===e||2!==e&&t}var z=function(e){function t(){var t=e.call(this)||this;return t._onFindReplaceStateChange=t._register(new q["a"]),t.onFindReplaceStateChange=t._onFindReplaceStateChange.event,t._searchString="",t._replaceString="",t._isRevealed=!1,t._isReplaceRevealed=!1,t._isRegex=!1,t._isRegexOverride=0,t._wholeWord=!1,t._wholeWordOverride=0,t._matchCase=!1,t._matchCaseOverride=0,t._preserveCase=!1,t._preserveCaseOverride=0,t._searchScope=null,t._matchesPosition=0,t._matchesCount=0,t._currentMatch=null,t}return Z(t,e),Object.defineProperty(t.prototype,"searchString",{get:function(){return this._searchString},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"replaceString",{get:function(){return this._replaceString},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"isRevealed",{get:function(){return this._isRevealed},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"isReplaceRevealed",{get:function(){return this._isReplaceRevealed},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"isRegex",{get:function(){return K(this._isRegexOverride,this._isRegex)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"wholeWord",{get:function(){return K(this._wholeWordOverride,this._wholeWord)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"matchCase",{get:function(){return K(this._matchCaseOverride,this._matchCase)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"preserveCase",{get:function(){return K(this._preserveCaseOverride,this._preserveCase)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"actualIsRegex",{get:function(){return this._isRegex},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"actualWholeWord",{get:function(){return this._wholeWord},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"actualMatchCase",{get:function(){return this._matchCase},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"actualPreserveCase",{get:function(){return this._preserveCase},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"searchScope",{get:function(){return this._searchScope},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"matchesPosition",{get:function(){return this._matchesPosition},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"matchesCount",{get:function(){return this._matchesCount},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"currentMatch",{get:function(){return this._currentMatch},enumerable:!0,configurable:!0}),t.prototype.changeMatchInfo=function(e,t,i){var o={moveCursor:!1,updateHistory:!1,searchString:!1,replaceString:!1,isRevealed:!1,isReplaceRevealed:!1,isRegex:!1,wholeWord:!1,matchCase:!1,preserveCase:!1,searchScope:!1,matchesPosition:!1,matchesCount:!1,currentMatch:!1},n=!1;0===t&&(e=0),e>t&&(e=t),this._matchesPosition!==e&&(this._matchesPosition=e,o.matchesPosition=!0,n=!0),this._matchesCount!==t&&(this._matchesCount=t,o.matchesCount=!0,n=!0),"undefined"!==typeof i&&(h["a"].equalsRange(this._currentMatch,i)||(this._currentMatch=i,o.currentMatch=!0,n=!0)),n&&this._onFindReplaceStateChange.fire(o)},t.prototype.change=function(e,t,i){void 0===i&&(i=!0);var o={moveCursor:t,updateHistory:i,searchString:!1,replaceString:!1,isRevealed:!1,isReplaceRevealed:!1,isRegex:!1,wholeWord:!1,matchCase:!1,preserveCase:!1,searchScope:!1,matchesPosition:!1,matchesCount:!1,currentMatch:!1},n=!1,r=this.isRegex,s=this.wholeWord,a=this.matchCase,d=this.preserveCase;"undefined"!==typeof e.searchString&&this._searchString!==e.searchString&&(this._searchString=e.searchString,o.searchString=!0,n=!0),"undefined"!==typeof e.replaceString&&this._replaceString!==e.replaceString&&(this._replaceString=e.replaceString,o.replaceString=!0,n=!0),"undefined"!==typeof e.isRevealed&&this._isRevealed!==e.isRevealed&&(this._isRevealed=e.isRevealed,o.isRevealed=!0,n=!0),"undefined"!==typeof e.isReplaceRevealed&&this._isReplaceRevealed!==e.isReplaceRevealed&&(this._isReplaceRevealed=e.isReplaceRevealed,o.isReplaceRevealed=!0,n=!0),"undefined"!==typeof e.isRegex&&(this._isRegex=e.isRegex),"undefined"!==typeof e.wholeWord&&(this._wholeWord=e.wholeWord),"undefined"!==typeof e.matchCase&&(this._matchCase=e.matchCase),"undefined"!==typeof e.preserveCase&&(this._preserveCase=e.preserveCase),"undefined"!==typeof e.searchScope&&(h["a"].equalsRange(this._searchScope,e.searchScope)||(this._searchScope=e.searchScope,o.searchScope=!0,n=!0)),this._isRegexOverride="undefined"!==typeof e.isRegexOverride?e.isRegexOverride:0,this._wholeWordOverride="undefined"!==typeof e.wholeWordOverride?e.wholeWordOverride:0,this._matchCaseOverride="undefined"!==typeof e.matchCaseOverride?e.matchCaseOverride:0,this._preserveCaseOverride="undefined"!==typeof e.preserveCaseOverride?e.preserveCaseOverride:0,r!==this.isRegex&&(n=!0,o.isRegex=!0),s!==this.wholeWord&&(n=!0,o.wholeWord=!0),a!==this.matchCase&&(n=!0,o.matchCase=!0),d!==this.preserveCase&&(n=!0,o.preserveCase=!0),n&&this._onFindReplaceStateChange.fire(o)},t}(r["a"]),G=(i("01b0"),i("3813")),X=i("fbcf"),Y=i("70c3"),Q=i("fdcc"),$=i("30db"),J=i("6f66"),ee=function(){var e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])},e(t,i)};return function(t,i){function o(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(o.prototype=i.prototype,new o)}}(),te=o["a"]("label.find","Find"),ie=o["a"]("placeholder.find","Find"),oe=o["a"]("label.previousMatchButton","Previous match"),ne=o["a"]("label.nextMatchButton","Next match"),re=o["a"]("label.toggleSelectionFind","Find in selection"),se=o["a"]("label.closeButton","Close"),ae=o["a"]("label.replace","Replace"),de=o["a"]("placeholder.replace","Replace"),le=o["a"]("label.replaceButton","Replace"),ce=o["a"]("label.replaceAllButton","Replace All"),he=o["a"]("label.toggleReplaceButton","Toggle Replace mode"),ue=o["a"]("title.matchesCountLimit","Only the first {0} results are highlighted, but all find operations work on the entire text.",k),ge=o["a"]("label.matchesLocation","{0} of {1}"),pe=o["a"]("label.noResults","No Results"),fe=419,_e=275,me=_e-54,be=69,ve=33,ye="ctrlEnterReplaceAll.windows.donotask",Ce=$["e"]?256:2048,Se=function(){function e(e){this.afterLineNumber=e,this.heightInPx=ve,this.suppressMouseDown=!1,this.domNode=document.createElement("div"),this.domNode.className="dock-find-viewzone"}return e}();function Re(e,t,i){var o=!!t.match(/\n/);i&&o&&i.selectionStart>0&&e.stopPropagation()}function we(e,t,i){var o=!!t.match(/\n/);i&&o&&i.selectionEnd<i.value.length&&e.stopPropagation()}var Ie=function(e){function t(t,i,o,s,a,d,l,c,h){var u=e.call(this)||this;return u._cachedHeight=null,u._codeEditor=t,u._controller=i,u._state=o,u._contextViewProvider=s,u._keybindingService=a,u._contextKeyService=d,u._storageService=c,u._notificationService=h,u._ctrlEnterReplaceAllWarningPrompted=!!c.getBoolean(ye,0),u._isVisible=!1,u._isReplaceVisible=!1,u._ignoreChangeEvent=!1,u._updateHistoryDelayer=new n["a"](500),u._register(Object(r["h"])((function(){return u._updateHistoryDelayer.cancel()}))),u._register(u._state.onFindReplaceStateChange((function(e){return u._onStateChanged(e)}))),u._buildDomNode(),u._updateButtons(),u._tryUpdateWidgetWidth(),u._findInput.inputBox.layout(),u._register(u._codeEditor.onDidChangeConfiguration((function(e){if(e.hasChanged(68)&&(u._codeEditor.getOption(68)&&u._state.change({isReplaceRevealed:!1},!1),u._updateButtons()),e.hasChanged(107)&&u._tryUpdateWidgetWidth(),e.hasChanged(2)&&u.updateAccessibilitySupport(),e.hasChanged(28)){var t=u._codeEditor.getOption(28).addExtraSpaceOnTop;t&&!u._viewZone&&(u._viewZone=new Se(0),u._showViewZone()),!t&&u._viewZone&&u._removeViewZone()}}))),u.updateAccessibilitySupport(),u._register(u._codeEditor.onDidChangeCursorSelection((function(){u._isVisible&&u._updateToggleSelectionFindButton()}))),u._register(u._codeEditor.onDidFocusEditorWidget((function(){if(u._isVisible){var e=u._controller.getGlobalBufferTerm();e&&e!==u._state.searchString&&(u._state.change({searchString:e},!0),u._findInput.select())}}))),u._findInputFocused=F.bindTo(d),u._findFocusTracker=u._register(j["Z"](u._findInput.inputBox.inputElement)),u._register(u._findFocusTracker.onDidFocus((function(){u._findInputFocused.set(!0),u._updateSearchScope()}))),u._register(u._findFocusTracker.onDidBlur((function(){u._findInputFocused.set(!1)}))),u._replaceInputFocused=M.bindTo(d),u._replaceFocusTracker=u._register(j["Z"](u._replaceInput.inputBox.inputElement)),u._register(u._replaceFocusTracker.onDidFocus((function(){u._replaceInputFocused.set(!0),u._updateSearchScope()}))),u._register(u._replaceFocusTracker.onDidBlur((function(){u._replaceInputFocused.set(!1)}))),u._codeEditor.addOverlayWidget(u),u._codeEditor.getOption(28).addExtraSpaceOnTop&&(u._viewZone=new Se(0)),u._applyTheme(l.getTheme()),u._register(l.onThemeChange(u._applyTheme.bind(u))),u._register(u._codeEditor.onDidChangeModel((function(){u._isVisible&&(u._viewZoneId=void 0)}))),u._register(u._codeEditor.onDidScrollChange((function(e){e.scrollTopChanged?u._layoutViewZone():setTimeout((function(){u._layoutViewZone()}),0)}))),u}return ee(t,e),t.prototype.getId=function(){return t.ID},t.prototype.getDomNode=function(){return this._domNode},t.prototype.getPosition=function(){return this._isVisible?{preference:0}:null},t.prototype._onStateChanged=function(e){if(e.searchString){try{this._ignoreChangeEvent=!0,this._findInput.setValue(this._state.searchString)}finally{this._ignoreChangeEvent=!1}this._updateButtons()}if(e.replaceString&&(this._replaceInput.inputBox.value=this._state.replaceString),e.isRevealed&&(this._state.isRevealed?this._reveal():this._hide(!0)),e.isReplaceRevealed&&(this._state.isReplaceRevealed?this._codeEditor.getOption(68)||this._isReplaceVisible||(this._isReplaceVisible=!0,this._replaceInput.width=j["H"](this._findInput.domNode),this._updateButtons(),this._replaceInput.inputBox.layout()):this._isReplaceVisible&&(this._isReplaceVisible=!1,this._updateButtons())),(e.isRevealed||e.isReplaceRevealed)&&(this._state.isRevealed||this._state.isReplaceRevealed)&&this._tryUpdateHeight()&&this._showViewZone(),e.isRegex&&this._findInput.setRegex(this._state.isRegex),e.wholeWord&&this._findInput.setWholeWords(this._state.wholeWord),e.matchCase&&this._findInput.setCaseSensitive(this._state.matchCase),e.searchScope&&(this._state.searchScope?this._toggleSelectionFind.checked=!0:this._toggleSelectionFind.checked=!1,this._updateToggleSelectionFindButton()),e.searchString||e.matchesCount||e.matchesPosition){var t=this._state.searchString.length>0&&0===this._state.matchesCount;j["Y"](this._domNode,"no-results",t),this._updateMatchesCount(),this._updateButtons()}(e.searchString||e.currentMatch)&&this._layoutViewZone(),e.updateHistory&&this._delayedUpdateHistory()},t.prototype._delayedUpdateHistory=function(){this._updateHistoryDelayer.trigger(this._updateHistory.bind(this))},t.prototype._updateHistory=function(){this._state.searchString&&this._findInput.inputBox.addToHistory(),this._state.replaceString&&this._replaceInput.inputBox.addToHistory()},t.prototype._updateMatchesCount=function(){var e;if(this._matchesCount.style.minWidth=be+"px",this._state.matchesCount>=k?this._matchesCount.title=ue:this._matchesCount.title="",this._matchesCount.firstChild&&this._matchesCount.removeChild(this._matchesCount.firstChild),this._state.matchesCount>0){var t=String(this._state.matchesCount);this._state.matchesCount>=k&&(t+="+");var i=String(this._state.matchesPosition);"0"===i&&(i="?"),e=s["r"](ge,i,t)}else e=pe;this._matchesCount.appendChild(document.createTextNode(e)),Object(G["a"])(this._getAriaLabel(e,this._state.currentMatch,this._state.searchString),!0),be=Math.max(be,this._matchesCount.clientWidth)},t.prototype._getAriaLabel=function(e,t,i){return e===pe?""===i?o["a"]("ariaSearchNoResultEmpty","{0} found",e):o["a"]("ariaSearchNoResult","{0} found for {1}",e,i):t?o["a"]("ariaSearchNoResultWithLineNum","{0} found for {1} at {2}",e,i,t.startLineNumber+":"+t.startColumn):o["a"]("ariaSearchNoResultWithLineNumNoCurrentMatch","{0} found for {1}",e,i)},t.prototype._updateToggleSelectionFindButton=function(){var e=this._codeEditor.getSelection(),t=!!e&&(e.startLineNumber!==e.endLineNumber||e.startColumn!==e.endColumn),i=this._toggleSelectionFind.checked;this._isVisible&&(i||t)?this._toggleSelectionFind.enable():this._toggleSelectionFind.disable()},t.prototype._updateButtons=function(){this._findInput.setEnabled(this._isVisible),this._replaceInput.setEnabled(this._isVisible&&this._isReplaceVisible),this._updateToggleSelectionFindButton(),this._closeBtn.setEnabled(this._isVisible);var e=this._state.searchString.length>0,t=!!this._state.matchesCount;this._prevBtn.setEnabled(this._isVisible&&e&&t),this._nextBtn.setEnabled(this._isVisible&&e&&t),this._replaceBtn.setEnabled(this._isVisible&&this._isReplaceVisible&&e),this._replaceAllBtn.setEnabled(this._isVisible&&this._isReplaceVisible&&e),j["Y"](this._domNode,"replaceToggled",this._isReplaceVisible),this._toggleReplaceBtn.toggleClass("codicon-chevron-right",!this._isReplaceVisible),this._toggleReplaceBtn.toggleClass("codicon-chevron-down",this._isReplaceVisible),this._toggleReplaceBtn.setExpanded(this._isReplaceVisible);var i=!this._codeEditor.getOption(68);this._toggleReplaceBtn.setEnabled(this._isVisible&&i)},t.prototype._reveal=function(){var e=this;if(!this._isVisible){this._isVisible=!0;var t=this._codeEditor.getSelection();switch(this._codeEditor.getOption(28).autoFindInSelection){case"always":this._toggleSelectionFind.checked=!0;break;case"never":this._toggleSelectionFind.checked=!1;break;case"multiline":var i=!!t&&t.startLineNumber!==t.endLineNumber;this._toggleSelectionFind.checked=i;break;default:break}this._tryUpdateWidgetWidth(),this._updateButtons(),setTimeout((function(){j["f"](e._domNode,"visible"),e._domNode.setAttribute("aria-hidden","false")}),0),setTimeout((function(){e._findInput.validate()}),200),this._codeEditor.layoutOverlayWidget(this);var o=!0;if(this._codeEditor.getOption(28).seedSearchStringFromSelection&&t){var n=this._codeEditor.getDomNode();if(n){var r=j["C"](n),s=this._codeEditor.getScrolledVisiblePosition(t.getStartPosition()),a=r.left+(s?s.left:0),d=s?s.top:0;if(this._viewZone&&d<this._viewZone.heightInPx){t.endLineNumber>t.startLineNumber&&(o=!1);var l=j["F"](this._domNode).left;a>l&&(o=!1);var c=this._codeEditor.getScrolledVisiblePosition(t.getEndPosition()),h=r.left+(c?c.left:0);h>l&&(o=!1)}}}this._showViewZone(o)}},t.prototype._hide=function(e){this._isVisible&&(this._isVisible=!1,this._updateButtons(),j["P"](this._domNode,"visible"),this._domNode.setAttribute("aria-hidden","true"),this._findInput.clearMessage(),e&&this._codeEditor.focus(),this._codeEditor.layoutOverlayWidget(this),this._removeViewZone())},t.prototype._layoutViewZone=function(){var e=this,t=this._codeEditor.getOption(28).addExtraSpaceOnTop;if(t){if(this._isVisible){var i=this._viewZone;void 0===this._viewZoneId&&i&&this._codeEditor.changeViewZones((function(t){i.heightInPx=e._getHeight(),e._viewZoneId=t.addZone(i),e._codeEditor.setScrollTop(e._codeEditor.getScrollTop()+i.heightInPx)}))}}else this._removeViewZone()},t.prototype._showViewZone=function(e){var t=this;if(void 0===e&&(e=!0),this._isVisible){var i=this._codeEditor.getOption(28).addExtraSpaceOnTop;if(i){void 0===this._viewZone&&(this._viewZone=new Se(0));var o=this._viewZone;this._codeEditor.changeViewZones((function(i){if(void 0!==t._viewZoneId){var n=t._getHeight();if(n===o.heightInPx)return;var r=n-o.heightInPx;return o.heightInPx=n,i.layoutZone(t._viewZoneId),void(e&&t._codeEditor.setScrollTop(t._codeEditor.getScrollTop()+r))}r=t._getHeight();o.heightInPx=r,t._viewZoneId=i.addZone(o),e&&t._codeEditor.setScrollTop(t._codeEditor.getScrollTop()+r)}))}}},t.prototype._removeViewZone=function(){var e=this;this._codeEditor.changeViewZones((function(t){void 0!==e._viewZoneId&&(t.removeZone(e._viewZoneId),e._viewZoneId=void 0,e._viewZone&&(e._codeEditor.setScrollTop(e._codeEditor.getScrollTop()-e._viewZone.heightInPx),e._viewZone=void 0))}))},t.prototype._applyTheme=function(e){var t={inputActiveOptionBorder:e.getColor(_["Y"]),inputActiveOptionBackground:e.getColor(_["X"]),inputBackground:e.getColor(_["Z"]),inputForeground:e.getColor(_["bb"]),inputBorder:e.getColor(_["ab"]),inputValidationInfoBackground:e.getColor(_["fb"]),inputValidationInfoForeground:e.getColor(_["hb"]),inputValidationInfoBorder:e.getColor(_["gb"]),inputValidationWarningBackground:e.getColor(_["ib"]),inputValidationWarningForeground:e.getColor(_["kb"]),inputValidationWarningBorder:e.getColor(_["jb"]),inputValidationErrorBackground:e.getColor(_["cb"]),inputValidationErrorForeground:e.getColor(_["eb"]),inputValidationErrorBorder:e.getColor(_["db"])};this._findInput.style(t),this._replaceInput.style(t),this._toggleSelectionFind.style(t)},t.prototype._tryUpdateWidgetWidth=function(){if(this._isVisible&&j["M"](this._domNode)){var e=this._codeEditor.getLayoutInfo(),t=e.contentWidth;if(t<=0)j["f"](this._domNode,"hiddenEditor");else{j["I"](this._domNode,"hiddenEditor")&&j["P"](this._domNode,"hiddenEditor");var i=e.width,o=e.minimapWidth,n=!1,r=!1,s=!1;if(this._resized){var a=j["H"](this._domNode);if(a>fe)return this._domNode.style.maxWidth=i-28-o-15+"px",void(this._replaceInput.width=j["H"](this._findInput.domNode))}if(fe+28+o>=i&&(r=!0),fe+28+o-be>=i&&(s=!0),fe+28+o-be>=i+50&&(n=!0),j["Y"](this._domNode,"collapsed-find-widget",n),j["Y"](this._domNode,"narrow-find-widget",s),j["Y"](this._domNode,"reduced-find-widget",r),s||n||(this._domNode.style.maxWidth=i-28-o-15+"px"),this._resized){this._findInput.inputBox.layout();var d=this._findInput.inputBox.element.clientWidth;d>0&&(this._replaceInput.width=d)}else this._isReplaceVisible&&(this._replaceInput.width=j["H"](this._findInput.domNode))}}},t.prototype._getHeight=function(){var e=0;return e+=4,e+=this._findInput.inputBox.height+2,this._isReplaceVisible&&(e+=4,e+=this._replaceInput.inputBox.height+2),e+=4,e},t.prototype._tryUpdateHeight=function(){var e=this._getHeight();return(null===this._cachedHeight||this._cachedHeight!==e)&&(this._cachedHeight=e,this._domNode.style.height=e+"px",!0)},t.prototype.focusFindInput=function(){this._findInput.select(),this._findInput.focus()},t.prototype.focusReplaceInput=function(){this._replaceInput.select(),this._replaceInput.focus()},t.prototype.highlightFindOptions=function(){this._findInput.highlightFindOptions()},t.prototype._updateSearchScope=function(){if(this._codeEditor.hasModel()&&this._toggleSelectionFind.checked){var e=this._codeEditor.getSelection();1===e.endColumn&&e.endLineNumber>e.startLineNumber&&(e=e.setEndPosition(e.endLineNumber-1,this._codeEditor.getModel().getLineMaxColumn(e.endLineNumber-1)));var t=this._state.currentMatch;e.startLineNumber!==e.endLineNumber&&(h["a"].equalsRange(e,t)||this._state.change({searchScope:e},!0))}},t.prototype._onFindInputMouseDown=function(e){e.middleButton&&e.stopPropagation()},t.prototype._onFindInputKeyDown=function(e){return e.equals(3|Ce)?(this._findInput.inputBox.insertAtCursor("\n"),void e.preventDefault()):e.equals(2)?(this._isReplaceVisible?this._replaceInput.focus():this._findInput.focusOnCaseSensitive(),void e.preventDefault()):e.equals(2066)?(this._codeEditor.focus(),void e.preventDefault()):e.equals(16)?Re(e,this._findInput.getValue(),this._findInput.domNode.querySelector("textarea")):e.equals(18)?we(e,this._findInput.getValue(),this._findInput.domNode.querySelector("textarea")):void 0},t.prototype._onReplaceInputKeyDown=function(e){return e.equals(3|Ce)?($["h"]&&$["f"]&&!this._ctrlEnterReplaceAllWarningPrompted&&(this._notificationService.info(o["a"]("ctrlEnter.keybindingChanged","Ctrl+Enter now inserts line break instead of replacing all. You can modify the keybinding for editor.action.replaceAll to override this behavior.")),this._ctrlEnterReplaceAllWarningPrompted=!0,this._storageService.store(ye,!0,0)),this._replaceInput.inputBox.insertAtCursor("\n"),void e.preventDefault()):e.equals(2)?(this._findInput.focusOnCaseSensitive(),void e.preventDefault()):e.equals(1026)?(this._findInput.focus(),void e.preventDefault()):e.equals(2066)?(this._codeEditor.focus(),void e.preventDefault()):e.equals(16)?Re(e,this._replaceInput.inputBox.value,this._replaceInput.inputBox.element.querySelector("textarea")):e.equals(18)?we(e,this._replaceInput.inputBox.value,this._replaceInput.inputBox.element.querySelector("textarea")):void 0},t.prototype.getHorizontalSashTop=function(e){return 0},t.prototype.getHorizontalSashLeft=function(e){return 0},t.prototype.getHorizontalSashWidth=function(e){return 500},t.prototype._keybindingLabelFor=function(e){var t=this._keybindingService.lookupKeybinding(e);return t?" ("+t.getLabel()+")":""},t.prototype._buildDomNode=function(){var e=this,t=!0,i=!0;this._findInput=this._register(new J["a"](null,this._contextViewProvider,{width:me,label:te,placeholder:ie,appendCaseSensitiveLabel:this._keybindingLabelFor(T.ToggleCaseSensitiveCommand),appendWholeWordsLabel:this._keybindingLabelFor(T.ToggleWholeWordCommand),appendRegexLabel:this._keybindingLabelFor(T.ToggleRegexCommand),validation:function(t){if(0===t.length||!e._findInput.getRegex())return null;try{return new RegExp(t),null}catch(i){return{content:i.message}}},flexibleHeight:t,flexibleWidth:i,flexibleMaxHeight:118},this._contextKeyService,!0)),this._findInput.setRegex(!!this._state.isRegex),this._findInput.setCaseSensitive(!!this._state.matchCase),this._findInput.setWholeWords(!!this._state.wholeWord),this._register(this._findInput.onKeyDown((function(t){return e._onFindInputKeyDown(t)}))),this._register(this._findInput.inputBox.onDidChange((function(){e._ignoreChangeEvent||e._state.change({searchString:e._findInput.getValue()},!0)}))),this._register(this._findInput.onDidOptionChange((function(){e._state.change({isRegex:e._findInput.getRegex(),wholeWord:e._findInput.getWholeWords(),matchCase:e._findInput.getCaseSensitive()},!0)}))),this._register(this._findInput.onCaseSensitiveKeyDown((function(t){t.equals(1026)&&e._isReplaceVisible&&(e._replaceInput.focus(),t.preventDefault())}))),this._register(this._findInput.onRegexKeyDown((function(t){t.equals(2)&&e._isReplaceVisible&&(e._replaceInput.focusOnPreserve(),t.preventDefault())}))),this._register(this._findInput.inputBox.onDidHeightChange((function(t){e._tryUpdateHeight()&&e._showViewZone()}))),$["d"]&&this._register(this._findInput.onMouseDown((function(t){return e._onFindInputMouseDown(t)}))),this._matchesCount=document.createElement("div"),this._matchesCount.className="matchesCount",this._updateMatchesCount(),this._prevBtn=this._register(new Oe({label:oe+this._keybindingLabelFor(T.PreviousMatchFindAction),className:"codicon codicon-arrow-up",onTrigger:function(){e._codeEditor.getAction(T.PreviousMatchFindAction).run().then(void 0,Q["e"])}})),this._nextBtn=this._register(new Oe({label:ne+this._keybindingLabelFor(T.NextMatchFindAction),className:"codicon codicon-arrow-down",onTrigger:function(){e._codeEditor.getAction(T.NextMatchFindAction).run().then(void 0,Q["e"])}}));var o=document.createElement("div");o.className="find-part",o.appendChild(this._findInput.domNode);var n=document.createElement("div");n.className="find-actions",o.appendChild(n),n.appendChild(this._matchesCount),n.appendChild(this._prevBtn.domNode),n.appendChild(this._nextBtn.domNode),this._toggleSelectionFind=this._register(new X["a"]({actionClassName:"codicon codicon-selection",title:re+this._keybindingLabelFor(T.ToggleSearchScopeCommand),isChecked:!1})),this._register(this._toggleSelectionFind.onChange((function(){if(e._toggleSelectionFind.checked){if(e._codeEditor.hasModel()){var t=e._codeEditor.getSelection();1===t.endColumn&&t.endLineNumber>t.startLineNumber&&(t=t.setEndPosition(t.endLineNumber-1,e._codeEditor.getModel().getLineMaxColumn(t.endLineNumber-1))),t.isEmpty()||e._state.change({searchScope:t},!0)}}else e._state.change({searchScope:null},!0)}))),n.appendChild(this._toggleSelectionFind.domNode),this._closeBtn=this._register(new Oe({label:se+this._keybindingLabelFor(T.CloseFindWidgetCommand),className:"codicon codicon-close",onTrigger:function(){e._state.change({isRevealed:!1,searchScope:null},!1)},onKeyDown:function(t){t.equals(2)&&e._isReplaceVisible&&(e._replaceBtn.isEnabled()?e._replaceBtn.focus():e._codeEditor.focus(),t.preventDefault())}})),n.appendChild(this._closeBtn.domNode),this._replaceInput=this._register(new J["b"](null,void 0,{label:ae,placeholder:de,history:[],flexibleHeight:t,flexibleWidth:i,flexibleMaxHeight:118},this._contextKeyService,!0)),this._replaceInput.setPreserveCase(!!this._state.preserveCase),this._register(this._replaceInput.onKeyDown((function(t){return e._onReplaceInputKeyDown(t)}))),this._register(this._replaceInput.inputBox.onDidChange((function(){e._state.change({replaceString:e._replaceInput.inputBox.value},!1)}))),this._register(this._replaceInput.inputBox.onDidHeightChange((function(t){e._isReplaceVisible&&e._tryUpdateHeight()&&e._showViewZone()}))),this._register(this._replaceInput.onDidOptionChange((function(){e._state.change({preserveCase:e._replaceInput.getPreserveCase()},!0)}))),this._register(this._replaceInput.onPreserveCaseKeyDown((function(t){t.equals(2)&&(e._prevBtn.isEnabled()?e._prevBtn.focus():e._nextBtn.isEnabled()?e._nextBtn.focus():e._toggleSelectionFind.enabled?e._toggleSelectionFind.focus():e._closeBtn.isEnabled()&&e._closeBtn.focus(),t.preventDefault())}))),this._replaceBtn=this._register(new Oe({label:le+this._keybindingLabelFor(T.ReplaceOneAction),className:"codicon codicon-replace",onTrigger:function(){e._controller.replace()},onKeyDown:function(t){t.equals(1026)&&(e._closeBtn.focus(),t.preventDefault())}})),this._replaceAllBtn=this._register(new Oe({label:ce+this._keybindingLabelFor(T.ReplaceAllAction),className:"codicon codicon-replace-all",onTrigger:function(){e._controller.replaceAll()}}));var r=document.createElement("div");r.className="replace-part",r.appendChild(this._replaceInput.domNode);var s=document.createElement("div");s.className="replace-actions",r.appendChild(s),s.appendChild(this._replaceBtn.domNode),s.appendChild(this._replaceAllBtn.domNode),this._toggleReplaceBtn=this._register(new Oe({label:he,className:"codicon toggle left",onTrigger:function(){e._state.change({isReplaceRevealed:!e._isReplaceVisible},!1),e._isReplaceVisible&&(e._replaceInput.width=j["H"](e._findInput.domNode),e._replaceInput.inputBox.layout()),e._showViewZone()}})),this._toggleReplaceBtn.toggleClass("codicon-chevron-down",this._isReplaceVisible),this._toggleReplaceBtn.toggleClass("codicon-chevron-right",!this._isReplaceVisible),this._toggleReplaceBtn.setExpanded(this._isReplaceVisible),this._domNode=document.createElement("div"),this._domNode.className="editor-widget find-widget",this._domNode.setAttribute("aria-hidden","true"),this._domNode.style.width=fe+"px",this._domNode.appendChild(this._toggleReplaceBtn.domNode),this._domNode.appendChild(o),this._domNode.appendChild(r),this._resizeSash=new Y["a"](this._domNode,this,{orientation:0}),this._resized=!1;var a=fe;this._register(this._resizeSash.onDidStart((function(){a=j["H"](e._domNode)}))),this._register(this._resizeSash.onDidChange((function(t){e._resized=!0;var i=a+t.startX-t.currentX;if(!(i<fe)){var o=parseFloat(j["z"](e._domNode).maxWidth)||0;i>o||(e._domNode.style.width=i+"px",e._isReplaceVisible&&(e._replaceInput.width=j["H"](e._findInput.domNode)),e._findInput.inputBox.layout(),e._tryUpdateHeight())}}))),this._register(this._resizeSash.onDidReset((function(){var t=j["H"](e._domNode);if(!(t<fe)){var i=fe;if(!e._resized||t===fe){var o=e._codeEditor.getLayoutInfo();i=o.width-28-o.minimapWidth-15,e._resized=!0}e._domNode.style.width=i+"px",e._isReplaceVisible&&(e._replaceInput.width=j["H"](e._findInput.domNode)),e._findInput.inputBox.layout()}})))},t.prototype.updateAccessibilitySupport=function(){var e=this._codeEditor.getOption(2);this._findInput.setFocusInputOnOptionClick(2!==e)},t.ID="editor.contrib.findWidget",t}(V["a"]),Oe=function(e){function t(t){var i=e.call(this)||this;return i._opts=t,i._domNode=document.createElement("div"),i._domNode.title=i._opts.label,i._domNode.tabIndex=0,i._domNode.className="button "+i._opts.className,i._domNode.setAttribute("role","button"),i._domNode.setAttribute("aria-label",i._opts.label),i.onclick(i._domNode,(function(e){i._opts.onTrigger(),e.preventDefault()})),i.onkeydown(i._domNode,(function(e){if(e.equals(10)||e.equals(3))return i._opts.onTrigger(),void e.preventDefault();i._opts.onKeyDown&&i._opts.onKeyDown(e)})),i}return ee(t,e),Object.defineProperty(t.prototype,"domNode",{get:function(){return this._domNode},enumerable:!0,configurable:!0}),t.prototype.isEnabled=function(){return this._domNode.tabIndex>=0},t.prototype.focus=function(){this._domNode.focus()},t.prototype.setEnabled=function(e){j["Y"](this._domNode,"disabled",!e),this._domNode.setAttribute("aria-disabled",String(!e)),this._domNode.tabIndex=e?0:-1},t.prototype.setExpanded=function(e){this._domNode.setAttribute("aria-expanded",String(!!e))},t.prototype.toggleClass=function(e,t){j["Y"](this._domNode,e,t)},t}(V["a"]);Object(m["e"])((function(e,t){var i=function(e,i){i&&t.addRule(".monaco-editor "+e+" { background-color: "+i+"; }")};i(".findMatch",e.getColor(_["t"])),i(".currentFindMatch",e.getColor(_["r"])),i(".findScope",e.getColor(_["v"]));var o=e.getColor(_["Q"]);i(".find-widget",o);var n=e.getColor(_["hc"]);n&&t.addRule(".monaco-editor .find-widget { box-shadow: 0 2px 8px "+n+"; }");var r=e.getColor(_["u"]);r&&t.addRule(".monaco-editor .findMatch { border: 1px "+("hc"===e.type?"dotted":"solid")+" "+r+"; box-sizing: border-box; }");var s=e.getColor(_["s"]);s&&t.addRule(".monaco-editor .currentFindMatch { border: 2px solid "+s+"; padding: 1px; box-sizing: border-box; }");var a=e.getColor(_["w"]);a&&t.addRule(".monaco-editor .findScope { border: 1px "+("hc"===e.type?"dashed":"solid")+" "+a+"; }");var d=e.getColor(_["e"]);d&&t.addRule(".monaco-editor .find-widget { border: 1px solid "+d+"; }");var l=e.getColor(_["S"]);l&&t.addRule(".monaco-editor .find-widget { color: "+l+"; }");var c=e.getColor(_["U"]);c&&t.addRule(".monaco-editor .find-widget.no-results .matchesCount { color: "+c+"; }");var h=e.getColor(_["T"]);if(h)t.addRule(".monaco-editor .find-widget .monaco-sash { background-color: "+h+"; width: 3px !important; margin-left: -4px;}");else{var u=e.getColor(_["R"]);u&&t.addRule(".monaco-editor .find-widget .monaco-sash { background-color: "+u+"; width: 3px !important; margin-left: -4px;}")}var g=e.getColor(_["V"]);g&&t.addRule(".monaco-editor .find-widget .monaco-inputbox.synthetic-focus { outline-color: "+g+"; }")}));var Ne=i("f577"),xe=i("533b"),Fe=i("0a0f"),Me=i("6dec"),De=i("03e8"),Ee=i("b0cd"),Le=function(){var e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])},e(t,i)};return function(t,i){function o(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(o.prototype=i.prototype,new o)}}(),Pe=function(e,t,i,o){var n,r=arguments.length,s=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,i):o;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)s=Reflect.decorate(e,t,i,o);else for(var a=e.length-1;a>=0;a--)(n=e[a])&&(s=(r<3?n(s):r>3?n(t,i,s):n(t,i))||s);return r>3&&s&&Object.defineProperty(t,i,s),s},Te=function(e,t){return function(i,o){t(i,o,e)}},ke=524288;function Ae(e){if(!e.hasModel())return null;var t=e.getSelection();if(t.startLineNumber===t.endLineNumber)if(t.isEmpty()){var i=e.getModel().getWordAtPosition(t.getStartPosition());if(i)return i.word}else if(e.getModel().getValueLengthInRange(t)<ke)return e.getModel().getValueInRange(t);return null}var We=function(e){function t(t,i,o,r){var s=e.call(this)||this;return s._editor=t,s._findWidgetVisible=x.bindTo(i),s._contextKeyService=i,s._storageService=o,s._clipboardService=r,s._updateHistoryDelayer=new n["a"](500),s._state=s._register(new z),s.loadQueryState(),s._register(s._state.onFindReplaceStateChange((function(e){return s._onStateChanged(e)}))),s._model=null,s._register(s._editor.onDidChangeModel((function(){var e=s._editor.getModel()&&s._state.isRevealed;s.disposeModel(),s._state.change({searchScope:null,matchCase:s._storageService.getBoolean("editor.matchCase",1,!1),wholeWord:s._storageService.getBoolean("editor.wholeWord",1,!1),isRegex:s._storageService.getBoolean("editor.isRegex",1,!1),preserveCase:s._storageService.getBoolean("editor.preserveCase",1,!1)},!1),e&&s._start({forceRevealReplace:!1,seedSearchStringFromSelection:!1,seedSearchStringFromGlobalClipboard:!1,shouldFocus:0,shouldAnimate:!1,updateSearchScope:!1})}))),s}return Le(t,e),t.get=function(e){return e.getContribution(t.ID)},t.prototype.dispose=function(){this.disposeModel(),e.prototype.dispose.call(this)},t.prototype.disposeModel=function(){this._model&&(this._model.dispose(),this._model=null)},t.prototype._onStateChanged=function(e){this.saveQueryState(e),e.isRevealed&&(this._state.isRevealed?this._findWidgetVisible.set(!0):(this._findWidgetVisible.reset(),this.disposeModel())),e.searchString&&this.setGlobalBufferTerm(this._state.searchString)},t.prototype.saveQueryState=function(e){e.isRegex&&this._storageService.store("editor.isRegex",this._state.actualIsRegex,1),e.wholeWord&&this._storageService.store("editor.wholeWord",this._state.actualWholeWord,1),e.matchCase&&this._storageService.store("editor.matchCase",this._state.actualMatchCase,1),e.preserveCase&&this._storageService.store("editor.preserveCase",this._state.actualPreserveCase,1)},t.prototype.loadQueryState=function(){this._state.change({matchCase:this._storageService.getBoolean("editor.matchCase",1,this._state.matchCase),wholeWord:this._storageService.getBoolean("editor.wholeWord",1,this._state.wholeWord),isRegex:this._storageService.getBoolean("editor.isRegex",1,this._state.isRegex),preserveCase:this._storageService.getBoolean("editor.preserveCase",1,this._state.preserveCase)},!1)},t.prototype.isFindInputFocused=function(){return!!F.getValue(this._contextKeyService)},t.prototype.getState=function(){return this._state},t.prototype.closeFindWidget=function(){this._state.change({isRevealed:!1,searchScope:null},!1),this._editor.focus()},t.prototype.toggleCaseSensitive=function(){this._state.change({matchCase:!this._state.matchCase},!1),this._state.isRevealed||this.highlightFindOptions()},t.prototype.toggleWholeWords=function(){this._state.change({wholeWord:!this._state.wholeWord},!1),this._state.isRevealed||this.highlightFindOptions()},t.prototype.toggleRegex=function(){this._state.change({isRegex:!this._state.isRegex},!1),this._state.isRevealed||this.highlightFindOptions()},t.prototype.toggleSearchScope=function(){if(this._state.searchScope)this._state.change({searchScope:null},!0);else if(this._editor.hasModel()){var e=this._editor.getSelection();1===e.endColumn&&e.endLineNumber>e.startLineNumber&&(e=e.setEndPosition(e.endLineNumber-1,this._editor.getModel().getLineMaxColumn(e.endLineNumber-1))),e.isEmpty()||this._state.change({searchScope:e},!0)}},t.prototype.setSearchString=function(e){this._state.isRegex&&(e=s["p"](e)),this._state.change({searchString:e},!1)},t.prototype.highlightFindOptions=function(){},t.prototype._start=function(e){if(this.disposeModel(),this._editor.hasModel()){var t={isRevealed:!0};if(e.seedSearchStringFromSelection){var i=Ae(this._editor);i&&(this._state.isRegex?t.searchString=s["p"](i):t.searchString=i)}if(!t.searchString&&e.seedSearchStringFromGlobalClipboard){i=this.getGlobalBufferTerm();i&&(t.searchString=i)}if(e.forceRevealReplace?t.isReplaceRevealed=!0:this._findWidgetVisible.get()||(t.isReplaceRevealed=!1),e.updateSearchScope){var o=this._editor.getSelection();o.isEmpty()||(t.searchScope=o)}this._state.change(t,!1),this._model||(this._model=new W(this._editor,this._state))}},t.prototype.start=function(e){this._start(e)},t.prototype.moveToNextMatch=function(){return!!this._model&&(this._model.moveToNextMatch(),!0)},t.prototype.moveToPrevMatch=function(){return!!this._model&&(this._model.moveToPrevMatch(),!0)},t.prototype.replace=function(){return!!this._model&&(this._model.replace(),!0)},t.prototype.replaceAll=function(){return!!this._model&&(this._model.replaceAll(),!0)},t.prototype.selectAllMatches=function(){return!!this._model&&(this._model.selectAllMatches(),this._editor.focus(),!0)},t.prototype.getGlobalBufferTerm=function(){return this._editor.getOption(28).globalFindClipboard&&this._clipboardService&&this._editor.hasModel()&&!this._editor.getModel().isTooLargeForSyncing()?this._clipboardService.readFindText():""},t.prototype.setGlobalBufferTerm=function(e){this._editor.getOption(28).globalFindClipboard&&this._clipboardService&&this._editor.hasModel()&&!this._editor.getModel().isTooLargeForSyncing()&&this._clipboardService.writeFindText(e)},t.ID="editor.contrib.findController",t=Pe([Te(1,N["c"]),Te(2,De["a"]),Te(3,Ne["a"])],t),t}(r["a"]),je=function(e){function t(t,i,o,n,r,s,a,d){var l=e.call(this,t,o,a,d)||this;return l._contextViewService=i,l._keybindingService=n,l._themeService=r,l._notificationService=s,l._widget=null,l._findOptionsWidget=null,l}return Le(t,e),t.prototype._start=function(t){this._widget||this._createFindWidget();var i=this._editor.getSelection(),o=!1;switch(this._editor.getOption(28).autoFindInSelection){case"always":o=!0;break;case"never":o=!1;break;case"multiline":var n=!!i&&i.startLineNumber!==i.endLineNumber;o=n;break;default:break}t.updateSearchScope=o,e.prototype._start.call(this,t),2===t.shouldFocus?this._widget.focusReplaceInput():1===t.shouldFocus&&this._widget.focusFindInput()},t.prototype.highlightFindOptions=function(){this._widget||this._createFindWidget(),this._state.isRevealed?this._widget.highlightFindOptions():this._findOptionsWidget.highlightFindOptions()},t.prototype._createFindWidget=function(){this._widget=this._register(new Ie(this._editor,this,this._state,this._contextViewService,this._keybindingService,this._contextKeyService,this._themeService,this._storageService,this._notificationService)),this._findOptionsWidget=this._register(new U(this._editor,this._state,this._keybindingService,this._themeService))},t=Pe([Te(1,xe["b"]),Te(2,N["c"]),Te(3,Me["a"]),Te(4,m["c"]),Te(5,Ee["a"]),Te(6,De["a"]),Te(7,Object(Fe["d"])(Ne["a"]))],t),t}(We),Be=function(e){function t(){return e.call(this,{id:T.StartFindAction,label:o["a"]("startFindAction","Find"),alias:"Find",precondition:void 0,kbOpts:{kbExpr:null,primary:2084,weight:100},menuOpts:{menuId:17,group:"3_find",title:o["a"]({key:"miFind",comment:["&& denotes a mnemonic"]},"&&Find"),order:1}})||this}return Le(t,e),t.prototype.run=function(e,t){var i=We.get(t);i&&i.start({forceRevealReplace:!1,seedSearchStringFromSelection:t.getOption(28).seedSearchStringFromSelection,seedSearchStringFromGlobalClipboard:t.getOption(28).globalFindClipboard,shouldFocus:1,shouldAnimate:!0,updateSearchScope:!1})},t}(a["b"]),Ve=function(e){function t(){return e.call(this,{id:T.StartFindWithSelection,label:o["a"]("startFindWithSelectionAction","Find With Selection"),alias:"Find With Selection",precondition:void 0,kbOpts:{kbExpr:null,primary:0,mac:{primary:2083},weight:100}})||this}return Le(t,e),t.prototype.run=function(e,t){var i=We.get(t);i&&(i.start({forceRevealReplace:!1,seedSearchStringFromSelection:!0,seedSearchStringFromGlobalClipboard:!1,shouldFocus:0,shouldAnimate:!0,updateSearchScope:!1}),i.setGlobalBufferTerm(i.getState().searchString))},t}(a["b"]),He=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return Le(t,e),t.prototype.run=function(e,t){var i=We.get(t);i&&!this._run(i)&&(i.start({forceRevealReplace:!1,seedSearchStringFromSelection:0===i.getState().searchString.length&&t.getOption(28).seedSearchStringFromSelection,seedSearchStringFromGlobalClipboard:!0,shouldFocus:0,shouldAnimate:!0,updateSearchScope:!1}),this._run(i))},t}(a["b"]),Ue=function(e){function t(){return e.call(this,{id:T.NextMatchFindAction,label:o["a"]("findNextMatchAction","Find Next"),alias:"Find Next",precondition:void 0,kbOpts:{kbExpr:d["a"].focus,primary:61,mac:{primary:2085,secondary:[61]},weight:100}})||this}return Le(t,e),t.prototype._run=function(e){return e.moveToNextMatch()},t}(He),qe=function(e){function t(){return e.call(this,{id:T.NextMatchFindAction,label:o["a"]("findNextMatchAction","Find Next"),alias:"Find Next",precondition:void 0,kbOpts:{kbExpr:N["a"].and(d["a"].focus,F),primary:3,weight:100}})||this}return Le(t,e),t.prototype._run=function(e){return e.moveToNextMatch()},t}(He),Ze=function(e){function t(){return e.call(this,{id:T.PreviousMatchFindAction,label:o["a"]("findPreviousMatchAction","Find Previous"),alias:"Find Previous",precondition:void 0,kbOpts:{kbExpr:d["a"].focus,primary:1085,mac:{primary:3109,secondary:[1085]},weight:100}})||this}return Le(t,e),t.prototype._run=function(e){return e.moveToPrevMatch()},t}(He),Ke=function(e){function t(){return e.call(this,{id:T.PreviousMatchFindAction,label:o["a"]("findPreviousMatchAction","Find Previous"),alias:"Find Previous",precondition:void 0,kbOpts:{kbExpr:N["a"].and(d["a"].focus,F),primary:1027,weight:100}})||this}return Le(t,e),t.prototype._run=function(e){return e.moveToPrevMatch()},t}(He),ze=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return Le(t,e),t.prototype.run=function(e,t){var i=We.get(t);if(i){var o=Ae(t);o&&i.setSearchString(o),this._run(i)||(i.start({forceRevealReplace:!1,seedSearchStringFromSelection:t.getOption(28).seedSearchStringFromSelection,seedSearchStringFromGlobalClipboard:!1,shouldFocus:0,shouldAnimate:!0,updateSearchScope:!1}),this._run(i))}},t}(a["b"]),Ge=function(e){function t(){return e.call(this,{id:T.NextSelectionMatchFindAction,label:o["a"]("nextSelectionMatchFindAction","Find Next Selection"),alias:"Find Next Selection",precondition:void 0,kbOpts:{kbExpr:d["a"].focus,primary:2109,weight:100}})||this}return Le(t,e),t.prototype._run=function(e){return e.moveToNextMatch()},t}(ze),Xe=function(e){function t(){return e.call(this,{id:T.PreviousSelectionMatchFindAction,label:o["a"]("previousSelectionMatchFindAction","Find Previous Selection"),alias:"Find Previous Selection",precondition:void 0,kbOpts:{kbExpr:d["a"].focus,primary:3133,weight:100}})||this}return Le(t,e),t.prototype._run=function(e){return e.moveToPrevMatch()},t}(ze),Ye=function(e){function t(){return e.call(this,{id:T.StartFindReplaceAction,label:o["a"]("startReplace","Replace"),alias:"Replace",precondition:void 0,kbOpts:{kbExpr:null,primary:2086,mac:{primary:2596},weight:100},menuOpts:{menuId:17,group:"3_find",title:o["a"]({key:"miReplace",comment:["&& denotes a mnemonic"]},"&&Replace"),order:2}})||this}return Le(t,e),t.prototype.run=function(e,t){if(t.hasModel()&&!t.getOption(68)){var i=We.get(t),o=t.getSelection(),n=i.isFindInputFocused(),r=!o.isEmpty()&&o.startLineNumber===o.endLineNumber&&t.getOption(28).seedSearchStringFromSelection&&!n,s=n||r?2:1;i&&i.start({forceRevealReplace:!0,seedSearchStringFromSelection:r,seedSearchStringFromGlobalClipboard:t.getOption(28).seedSearchStringFromSelection,shouldFocus:s,shouldAnimate:!0,updateSearchScope:!1})}},t}(a["b"]);Object(a["h"])(We.ID,je),Object(a["f"])(Be),Object(a["f"])(Ve),Object(a["f"])(Ue),Object(a["f"])(qe),Object(a["f"])(Ze),Object(a["f"])(Ke),Object(a["f"])(Ge),Object(a["f"])(Xe),Object(a["f"])(Ye);var Qe=a["c"].bindToContribution(We.get);Object(a["g"])(new Qe({id:T.CloseFindWidgetCommand,precondition:x,handler:function(e){return e.closeFindWidget()},kbOpts:{weight:105,kbExpr:d["a"].focus,primary:9,secondary:[1033]}})),Object(a["g"])(new Qe({id:T.ToggleCaseSensitiveCommand,precondition:void 0,handler:function(e){return e.toggleCaseSensitive()},kbOpts:{weight:105,kbExpr:d["a"].focus,primary:D.primary,mac:D.mac,win:D.win,linux:D.linux}})),Object(a["g"])(new Qe({id:T.ToggleWholeWordCommand,precondition:void 0,handler:function(e){return e.toggleWholeWords()},kbOpts:{weight:105,kbExpr:d["a"].focus,primary:E.primary,mac:E.mac,win:E.win,linux:E.linux}})),Object(a["g"])(new Qe({id:T.ToggleRegexCommand,precondition:void 0,handler:function(e){return e.toggleRegex()},kbOpts:{weight:105,kbExpr:d["a"].focus,primary:L.primary,mac:L.mac,win:L.win,linux:L.linux}})),Object(a["g"])(new Qe({id:T.ToggleSearchScopeCommand,precondition:void 0,handler:function(e){return e.toggleSearchScope()},kbOpts:{weight:105,kbExpr:d["a"].focus,primary:P.primary,mac:P.mac,win:P.win,linux:P.linux}})),Object(a["g"])(new Qe({id:T.ReplaceOneAction,precondition:x,handler:function(e){return e.replace()},kbOpts:{weight:105,kbExpr:d["a"].focus,primary:3094}})),Object(a["g"])(new Qe({id:T.ReplaceOneAction,precondition:x,handler:function(e){return e.replace()},kbOpts:{weight:105,kbExpr:N["a"].and(d["a"].focus,M),primary:3}})),Object(a["g"])(new Qe({id:T.ReplaceAllAction,precondition:x,handler:function(e){return e.replaceAll()},kbOpts:{weight:105,kbExpr:d["a"].focus,primary:2563}})),Object(a["g"])(new Qe({id:T.ReplaceAllAction,precondition:x,handler:function(e){return e.replaceAll()},kbOpts:{weight:105,kbExpr:N["a"].and(d["a"].focus,M),primary:void 0,mac:{primary:2051}}})),Object(a["g"])(new Qe({id:T.SelectAllMatchesAction,precondition:x,handler:function(e){return e.selectAllMatches()},kbOpts:{weight:105,kbExpr:d["a"].focus,primary:515}}))},ba3c:function(e,t,i){},c88e:function(e,t,i){},fd11:function(e,t,i){"use strict";i.r(t),i.d(t,"DragAndDropController",(function(){return g}));i("3a19");var o=i("a666"),n=i("30db"),r=i("b2cc"),s=i("7061"),a=i("6a89"),d=i("8025"),l=function(){function e(e,t,i){this.selection=e,this.targetPosition=t,this.copy=i,this.targetSelection=null}return e.prototype.getEditOperations=function(e,t){var i=e.getValueInRange(this.selection);this.copy||t.addEditOperation(this.selection,null),t.addEditOperation(new a["a"](this.targetPosition.lineNumber,this.targetPosition.column,this.targetPosition.lineNumber,this.targetPosition.column),i),!this.selection.containsPosition(this.targetPosition)||this.copy&&(this.selection.getEndPosition().equals(this.targetPosition)||this.selection.getStartPosition().equals(this.targetPosition))?this.copy?this.targetSelection=new d["a"](this.targetPosition.lineNumber,this.targetPosition.column,this.selection.endLineNumber-this.selection.startLineNumber+this.targetPosition.lineNumber,this.selection.startLineNumber===this.selection.endLineNumber?this.targetPosition.column+this.selection.endColumn-this.selection.startColumn:this.selection.endColumn):this.targetPosition.lineNumber>this.selection.endLineNumber?this.targetSelection=new d["a"](this.targetPosition.lineNumber-this.selection.endLineNumber+this.selection.startLineNumber,this.targetPosition.column,this.targetPosition.lineNumber,this.selection.startLineNumber===this.selection.endLineNumber?this.targetPosition.column+this.selection.endColumn-this.selection.startColumn:this.selection.endColumn):this.targetPosition.lineNumber<this.selection.endLineNumber?this.targetSelection=new d["a"](this.targetPosition.lineNumber,this.targetPosition.column,this.targetPosition.lineNumber+this.selection.endLineNumber-this.selection.startLineNumber,this.selection.startLineNumber===this.selection.endLineNumber?this.targetPosition.column+this.selection.endColumn-this.selection.startColumn:this.selection.endColumn):this.selection.endColumn<=this.targetPosition.column?this.targetSelection=new d["a"](this.targetPosition.lineNumber-this.selection.endLineNumber+this.selection.startLineNumber,(this.selection.startLineNumber,this.selection.endLineNumber,this.targetPosition.column-this.selection.endColumn+this.selection.startColumn),this.targetPosition.lineNumber,this.selection.startLineNumber===this.selection.endLineNumber?this.targetPosition.column:this.selection.endColumn):this.targetSelection=new d["a"](this.targetPosition.lineNumber-this.selection.endLineNumber+this.selection.startLineNumber,this.targetPosition.column,this.targetPosition.lineNumber,this.targetPosition.column+this.selection.endColumn-this.selection.startColumn):this.targetSelection=this.selection},e.prototype.computeCursorState=function(e,t){return this.targetSelection},e}(),c=i("b57f"),h=function(){var e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])},e(t,i)};return function(t,i){function o(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(o.prototype=i.prototype,new o)}}();function u(e){return n["e"]?e.altKey:e.ctrlKey}var g=function(e){function t(t){var i=e.call(this)||this;return i._editor=t,i._register(i._editor.onMouseDown((function(e){return i._onEditorMouseDown(e)}))),i._register(i._editor.onMouseUp((function(e){return i._onEditorMouseUp(e)}))),i._register(i._editor.onMouseDrag((function(e){return i._onEditorMouseDrag(e)}))),i._register(i._editor.onMouseDrop((function(e){return i._onEditorMouseDrop(e)}))),i._register(i._editor.onKeyDown((function(e){return i.onEditorKeyDown(e)}))),i._register(i._editor.onKeyUp((function(e){return i.onEditorKeyUp(e)}))),i._register(i._editor.onDidBlurEditorWidget((function(){return i.onEditorBlur()}))),i._dndDecorationIds=[],i._mouseDown=!1,i._modifierPressed=!1,i._dragSelection=null,i}return h(t,e),t.prototype.onEditorBlur=function(){this._removeDecoration(),this._dragSelection=null,this._mouseDown=!1,this._modifierPressed=!1},t.prototype.onEditorKeyDown=function(e){this._editor.getOption(24)&&(u(e)&&(this._modifierPressed=!0),this._mouseDown&&u(e)&&this._editor.updateOptions({mouseStyle:"copy"}))},t.prototype.onEditorKeyUp=function(e){this._editor.getOption(24)&&(u(e)&&(this._modifierPressed=!1),this._mouseDown&&e.keyCode===t.TRIGGER_KEY_VALUE&&this._editor.updateOptions({mouseStyle:"default"}))},t.prototype._onEditorMouseDown=function(e){this._mouseDown=!0},t.prototype._onEditorMouseUp=function(e){this._mouseDown=!1,this._editor.updateOptions({mouseStyle:"text"})},t.prototype._onEditorMouseDrag=function(e){var t=e.target;if(null===this._dragSelection){var i=this._editor.getSelections()||[],o=i.filter((function(e){return t.position&&e.containsPosition(t.position)}));if(1!==o.length)return;this._dragSelection=o[0]}u(e.event)?this._editor.updateOptions({mouseStyle:"copy"}):this._editor.updateOptions({mouseStyle:"default"}),t.position&&(this._dragSelection.containsPosition(t.position)?this._removeDecoration():this.showAt(t.position))},t.prototype._onEditorMouseDrop=function(e){if(e.target&&(this._hitContent(e.target)||this._hitMargin(e.target))&&e.target.position){var i=new s["a"](e.target.position.lineNumber,e.target.position.column);if(null===this._dragSelection){var o=null;if(e.event.shiftKey){var n=this._editor.getSelection();if(n){var r=n.selectionStartLineNumber,a=n.selectionStartColumn;o=[new d["a"](r,a,i.lineNumber,i.column)]}}else o=(this._editor.getSelections()||[]).map((function(e){return e.containsPosition(i)?new d["a"](i.lineNumber,i.column,i.lineNumber,i.column):e}));this._editor.setSelections(o||[],"mouse")}else(!this._dragSelection.containsPosition(i)||(u(e.event)||this._modifierPressed)&&(this._dragSelection.getEndPosition().equals(i)||this._dragSelection.getStartPosition().equals(i)))&&(this._editor.pushUndoStop(),this._editor.executeCommand(t.ID,new l(this._dragSelection,i,u(e.event)||this._modifierPressed)),this._editor.pushUndoStop())}this._editor.updateOptions({mouseStyle:"text"}),this._removeDecoration(),this._dragSelection=null,this._mouseDown=!1},t.prototype.showAt=function(e){var i=[{range:new a["a"](e.lineNumber,e.column,e.lineNumber,e.column),options:t._DECORATION_OPTIONS}];this._dndDecorationIds=this._editor.deltaDecorations(this._dndDecorationIds,i),this._editor.revealPosition(e,1)},t.prototype._removeDecoration=function(){this._dndDecorationIds=this._editor.deltaDecorations(this._dndDecorationIds,[])},t.prototype._hitContent=function(e){return 6===e.type||7===e.type},t.prototype._hitMargin=function(e){return 2===e.type||3===e.type||4===e.type},t.prototype.dispose=function(){this._removeDecoration(),this._dragSelection=null,this._mouseDown=!1,this._modifierPressed=!1,e.prototype.dispose.call(this)},t.ID="editor.contrib.dragAndDrop",t.TRIGGER_KEY_VALUE=n["e"]?6:5,t._DECORATION_OPTIONS=c["a"].register({className:"dnd-target"}),t}(o["a"]);Object(r["h"])(g.ID,g)}}]);