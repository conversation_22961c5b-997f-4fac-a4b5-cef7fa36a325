(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-bd080c00"],{"0089":function(t,e,a){t.exports=a.p+"static/img/鹅卵石-07.942692d4.png"},"0119":function(t,e,a){},"0188":function(t,e,a){t.exports=a.p+"static/img/地砖-03.a179e8d7.png"},"01de":function(t,e,a){t.exports=a.p+"static/img/沙地-01.e96b7274.png"},"04de":function(t,e,a){"use strict";a("0d69")},"05c2":function(t,e,a){t.exports=a.p+"static/img/meta-video.6f2f6a7d.png"},"0659":function(t,e,a){t.exports=a.p+"static/img/地砖-04.574bed93.png"},"066e":function(t,e,a){"use strict";a("12cd")},"06ff":function(t,e,a){},"0d5b":function(t,e,a){var i={"./压路机.png":"ee5f","./灌木.png":"d675","./白色轿车.png":"6ac8","./空调面板.png":"855e"};function n(t){var e=s(t);return a(e)}function s(t){if(!a.o(i,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return i[t]}n.keys=function(){return Object.keys(i)},n.resolve=s,t.exports=n,n.id="0d5b"},"0d69":function(t,e,a){},"0ee7":function(t,e,a){t.exports=a.p+"static/img/沙地-04.b25d9580.png"},"0fa7":function(t,e,a){"use strict";a("edf8")},"108b":function(t,e,a){},"129f":function(t,e){t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!=t&&e!=e}},"12cd":function(t,e,a){},1367:function(t,e,a){t.exports=a.p+"static/img/柏油路-03.2c5ab5d6.png"},1492:function(t,e,a){var i={"./公交车.png":"ef87","./工人(动态).png":"372f","./空调.png":"d7e9","./草石头.png":"5e5c","./野草_1.png":"da4e","./野草_2.png":"dffd"};function n(t){var e=s(t);return a(e)}function s(t){if(!a.o(i,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return i[t]}n.keys=function(){return Object.keys(i)},n.resolve=s,t.exports=n,n.id="1492"},"230a":function(t,e,a){t.exports=a.p+"static/img/地砖-01.d1e0f02d.png"},2797:function(t,e,a){t.exports=a.p+"static/img/地砖-08.b9c401d1.png"},"2f13":function(t,e,a){t.exports=a.p+"static/img/meta-3DTiles.19631555.png"},"34e5":function(t,e,a){"use strict";a("108b")},"372f":function(t,e,a){t.exports=a.p+"static/img/工人(动态).c69be70a.png"},3914:function(t,e,a){t.exports=a.p+"static/img/thumb_gis_annotation.ddff8325.png"},3992:function(t,e,a){t.exports=a.p+"static/img/鹅卵石-04.74e52098.png"},"39f2":function(t,e,a){t.exports=a.p+"static/img/塑胶-03.f0ea4431.png"},"42a9":function(t,e,a){t.exports=a.p+"static/img/鹅卵石-03.53200844.png"},4552:function(t,e,a){"use strict";a("06ff")},"459b":function(t,e,a){t.exports=a.p+"static/img/地砖-05.80b32b35.png"},"45cf":function(t,e,a){t.exports=a.p+"static/img/鹅卵石-02.9c2c1284.png"},"45da":function(t,e,a){t.exports=a.p+"static/img/地砖-02.bbeb8f0a.png"},"46c0":function(t,e,a){t.exports=a.p+"static/img/柏油路-02.c35f9945.png"},"48a0":function(t,e,a){t.exports=a.p+"static/img/草坪-04.a04aad5b.png"},"4c1b":function(t,e,a){},"4f98":function(t,e,a){t.exports=a.p+"static/img/柏油路-01.6e93823a.png"},"4fa5":function(t,e,a){t.exports=a.p+"static/img/鹅卵石-06.50a3de57.png"},5084:function(t,e,a){t.exports=a.p+"static/img/thumb_noon.313f5036.png"},"554c":function(t,e,a){t.exports=a.p+"static/img/地砖-10.b179c79f.png"},"58d9":function(t,e,a){t.exports=a.p+"static/img/草坪-02.d41e6018.png"},"5a44":function(t,e){t.exports="data:image/png;base64,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"},"5cf9":function(t,e,a){t.exports=a.p+"static/img/草坪-01.729e425c.png"},"5e5c":function(t,e,a){t.exports=a.p+"static/img/草石头.04f147e9.png"},"631b":function(t,e,a){t.exports=a.p+"static/img/thumb_gis_satellite_v9.8f4013c4.png"},6546:function(t,e,a){t.exports=a.p+"static/img/thumb_night.ca503ad7.png"},6674:function(t,e,a){t.exports=a.p+"static/img/thumb_gis_arcGIS.a994309f.png"},"690d":function(t,e,a){t.exports=a.p+"static/img/地砖-07.d322540b.png"},"6ac8":function(t,e){t.exports="data:image/png;base64,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"},"70da":function(t,e,a){},"71d2":function(t,e,a){t.exports=a.p+"static/img/thumb_gis_tianditu.127ca95f.png"},7477:function(t,e,a){t.exports=a.p+"static/img/thumb_evening.3b62bacf.png"},"7bd2":function(t,e,a){t.exports=a.p+"static/img/thumb_gis_light_blue.631fec54.png"},"7e32f":function(t,e,a){t.exports=a.p+"static/img/草坪-06.f798633d.png"},"7ecc":function(t,e,a){t.exports=a.p+"static/img/meta-dem.417f07c2.png"},"7f83":function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJAAAACQCAMAAADQmBKKAAAA4VBMVEWweWnbomyveGjan2zMhWq8e2rcpm3Ph2rCfmrZm2vYmWu0emm3emnKg2rIgWouNkC/fWrEf2rbpGzGgGrXl2suNT/UkGovPUoyT2jRi2rVkmrQiWowRVe5e2rdqm7anWsvOkYwQlPcqG0vP00xSV8uOEMvQFDTjmrXlWowNj8xTWUxSFzcqW3TjWowRlrWk2sxTGMwN0GxeWn/+mz/9mtmUVG2fGsxS2H/82v/72v/62v/3GsxOUP/52v/4mtmUE/5t2v+ymv7v2v902vxs2venGvrnWv2pmrkl2rejmreq26X5erXAAAGP0lEQVR42szQS2+yQBiG4ZdoVEw0EQmJknwkbmBR4qIsOATDyVP//w/6RsCO8sIwowW5xrpom3nuDHwh//rxVY8G9dXVOLjLwa7w1aKbBlJB0SDsr8oYCdgkB5PSrgVvW/s6jqDKF5ogu85MmBTyQooyUXYTDuLbr4CdslMmA6CUQGnSXabCAgpbxykYSAqPjkskCqRnSk+kJiC16mSdETQwQwoaSaORBOQr/0ifMirOHYzuDJLVa9cIKYJYOp1vDDJun6EwwDDymk82GY/AGJFD0F93nmawAM0w6v+10xAM5CdGb+QG8FsiF6dGJ9vNQUMxL8Cckns2R8oguTxIHw04iE/7jnAFphKgqnPyuR2Et/MUxWkaR0eDdxp3UJDHlE3EXNQxC3zXcRzX9Q+JLAuGYEB+LBXhLvs5uM53yXEPmawKdGCWBZZlqfmPytTwPJFHcijHi+cqGmeyKsBSy0OpvI7J/XnoI2UqwkzBQXU4b6TvQ4v8TCAH28Pe2pLTpvbKn5D0oKLgZLGWccIzIDXlef6T1S71vjHHTeb79nlsW4AttueyzXznu4YTSjw34FkcJNampV59kJex3oFFI0DLbQWUN18C1yHz+LjpcYun2SEUaBXcl5xDhwQhjuMGR00sgxHEH2dGoVPPPTQHaWymaYJ5owkqgtzmIEEmBSYllGY3BwWyKViBghowg7KDW9/jNQeZLWzbBvvO5FUGnQPPretx/dgy6TabjYDNYjJcU9+t44XRvm0d29zBpsrmpCeh59bwg6PdboPhIDZ05SnwvboHSvab9t33gzA9Ofge4qcn+8X7cqA/2gg5paGPeoJM0/kDMNDfcIkrb+SHQWbrb5hOYXqnv+AUH0LfJyHlVxhEV13MtIIGYRy3XZKAJBXCMIjPGvc4hoPE6ddzkgaHmyCIo9NmKgoHvcm+nqIkjuMkOl9sffqO8Q2MH0xfotu5l2rGCIwZpn9tjOGggRlO0KIAiyfjPi0q2zSIrYN1BlgMDCwLi09aPoAl0nXhkgmWA7ImYE0tP2FdAWuk6741E6zWYkSWRaxKsKpY92aF4SCs2wIMZrPZ6h1o8CWzXzCrt+rSjAFmvDqYrw8amP/M0EGqwzAMRdGHwBCwBt6ANuHB3//SvtsqdqDJo7SJlSO3pO5Al6CUZSmbmc7Xr1C6pZ3x40qF8KDBc8OgJJ84qZ0+SEfKVRKF3ZZ0Jb4C6V4MpmlMsjSH+Xc7OsbUWpDpc/rlvt/WH0i+9lXweoK2p0eF+r3/e7226Xl85wramH/83vrD+fSAjaDPndTAQW8kt4P8RifJe96CNH9Eufw91DwmyAhoB7ULq6pbcJXJ9Yy3wGAQAVVPBE4eIBsIIaubBMnNgoQERRQJCYooEhIUUSQkKKJISFBEkZCgiCQhQRFFQoICikhQTJEcgYQUCQ2a3SQMZHqRUMirv0B5wD+zZJDiMAxDUd1Aq5BtoLigpRfdFeb+t5r/HwZDQwYyE5i8KJIsW/Z32teXeMn+E52OCBHVu8yucI6DMvYdY5owywwJrKfZbpY+N+HRnB3QwwgiK3M5T/GK5NEeREKRDW/HA4QficqqpVfKctzI+3iU6FZm87skrhQsJ8VO5syqyIDRzGfYaQwfcHT3smN6kOmqZKpYwD0EDmrskJ86uASzRZkcUkaHXKQ29EElE5nFF3BipiYkkCpymrwXUtNLpQOHUGElzH1UpJxkqEaSqZAe8+6VSELL+DX4pbqdICAJ0VyrJ4NR45Vu9nvbBNsTmc4hCHWJ7qLu4Gr4T06pp7AsdxMYKI4pTyK2gENY1dMlRQbU2IPwnmgw1RVruTUfmusqRAq+3yF0/IXlzLJoCS0bKNmhouwgv7o/2s2ItkHbmowI5Mc1IL+6P7abEdu6busGK/kuit08kF/dH+vvoBsu7o/1ZsTjZnwKej6esscJLu7/bnaMUSiIQSA6x0iRRggpEhC73P9m3y9hLWRhCwsfE3QmDIhZDNCcNEnl8+I+4v/ZfVAxQL1TJ5XNwM1fs57dRy9GvYO4GNjMm/eDmj+Wcfy73nfO7mMXA6MYGJEzjir4kBvZfRyRI8cQ28MMSOgk9iHfWbJUIc7tYxUD+tpqhi4Xy1Svu2fZfbRilDvoB8sHg3hQVSKGAAAAAElFTkSuQmCC"},"841c":function(t,e,a){"use strict";var i=a("c65b"),n=a("d784"),s=a("825a"),o=a("1d80"),r=a("129f"),c=a("577e"),l=a("dc4a"),d=a("14c3");n("search",(function(t,e,a){return[function(e){var a=o(this),n=void 0==e?void 0:l(e,t);return n?i(n,e,a):new RegExp(e)[t](c(a))},function(t){var i=s(this),n=c(t),o=a(e,i,n);if(o.done)return o.value;var l=i.lastIndex;r(l,0)||(i.lastIndex=0);var u=d(i,n);return r(i.lastIndex,l)||(i.lastIndex=l),null===u?-1:u.index}]}))},"855c":function(t,e,a){t.exports=a.p+"static/img/thumb_gis_blackish_green.7ef892ac.png"},"855e":function(t,e){t.exports="data:image/png;base64,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"},"92df":function(t,e,a){"use strict";a("0119")},9507:function(t,e,a){t.exports=a.p+"static/img/草坪-03.291686d4.png"},9629:function(t,e,a){t.exports=a.p+"static/img/thumb_gis_space_grey.90044b88.png"},"9a49":function(t,e,a){t.exports=a.p+"static/img/thumb_gis_blank.346545a3.png"},a414:function(t,e,a){"use strict";a("4c1b")},a7c9:function(t,e,a){t.exports=a.p+"static/img/地砖-06.01abadea.png"},ab0f:function(t,e,a){t.exports=a.p+"static/img/沙地-02.be56ac58.png"},abfc:function(t,e,a){t.exports=a.p+"static/img/thumb_gis_satellite_streets.afd76146.png"},b39c:function(t,e,a){t.exports=a.p+"static/img/thumb_gis_tianditu_streets.70a12dcb.png"},b634:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("dialogComp",{ref:"linkView",staticClass:"sourceDom",attrs:{hideHeader:!1,needClose:"true",zIndex:"4",draw:!0,left:t.dialogLeft,drag:!0,title:t.setSourceTitle,icon:"icon-details",width:t.dialogWidth,height:t.dialogHeight,isSource:!0,type:"detailInfo",top:t.TopToolbarHeight},on:{close:t.closeDialog},scopedSlots:t._u([{key:"center",fn:function(){return[a("div",{staticClass:"source-container"},[a("div",{staticClass:"right-content",class:{modelContent:"model"==t.dragData.type}},["model"==t.dragData.type?a("ModelObj",{ref:"modelObjRef"}):t._e(),"underlay"==t.dragData.type?a("SourceUnderlay",{attrs:{currentData:t.dragData},on:{close:t.closeDialog}}):t._e(),"skybox"==t.dragData.type?a("SourceSkyBox",{attrs:{currentData:t.dragData},on:{close:t.closeDialog}}):t._e(),"gltf"==t.dragData.type||"fbx"==t.dragData.type||"panorama"==t.dragData.type||"_3dTiles"==t.dragData.type?a("SceneMaterialLibrary",{key:t.dataKey,attrs:{currentData:t.dragData,vothingElementArr:t.vothingElementArr},on:{checkedCoordinateAddFeature:t.checkedCoordinateAddFeature}}):t._e(),"polygon"==t.dragData.type?a("SourceElementImage",{attrs:{currentData:t.dragData},on:{freeSketchPolygon:t.freeSketchPolygon}}):t._e()],1)])]},proxy:!0}])})},n=[],s=(a("caad"),a("2532"),a("99af"),a("d81d"),function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"resources-label-container"},[a("div",{directives:[{name:"show",rawName:"v-show",value:t.labelDatas.length>0,expression:"labelDatas.length > 0"}],staticClass:"top-filter"},[a("div",{staticClass:"search"},[a("el-input",{attrs:{placeholder:t.$t("dialog.materialLibrary.model.placeholder"),size:"mini"},model:{value:t.searchValue,callback:function(e){t.searchValue=e},expression:"searchValue"}},[a("i",{staticClass:"el-input__icon el-icon-search",attrs:{slot:"prefix"},slot:"prefix"})])],1),a("div",{staticClass:"filter"},[t.toggleMode?a("el-tooltip",{attrs:{effect:"dark",enterable:!1,placement:"top",content:t.$t("dialog.materialLibrary.model.label")}},[a("span",{staticClass:"outline-none cursor-btn",on:{click:function(e){return t.handleToggleMode(!1)}}},[a("CommonSVG",{attrs:{size:"17","icon-class":"list_mode"}})],1)]):a("el-tooltip",{attrs:{effect:"dark",enterable:!1,placement:"top",content:t.$t("dialog.materialLibrary.model.label1")}},[a("span",{staticClass:"outline-none cursor-btn",on:{click:function(e){return t.handleToggleMode(!0)}}},[a("CommonSVG",{attrs:{size:"17","icon-class":"view_mode"}})],1)])],1)]),t.toggleMode?a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"label-content",class:{h100:t.loading},attrs:{"element-loading-spinner":"el-icon-loading","element-loading-background":"rgba(0, 0, 0, 0)"}},t._l(t.filterCurrentDatas,(function(e,i){return a("div",{key:e.featureID,staticClass:"label-item"},[a("div",{staticClass:"thumb"},[a("div",{staticClass:"order-number"},[t._v(t._s(i+1))]),e.timeseries?a("div",{staticClass:"time-series-icon"},[a("CommonSVG",{attrs:{size:"14","icon-class":"time_series",color:"#FFFFFF"}})],1):t._e(),""==e.thumbnail||"Project"==e.thumbnail?a("img",{attrs:{src:t.defaultImg,alt:""}}):a("img",{attrs:{src:"data:image/png;base64,"+e.thumbnail,alt:""}}),a("div",{staticClass:"comSvg"},[a("a",{on:{click:function(a){return a.preventDefault(),t.handleAddFeature(e,"default")}}},[a("el-tooltip",{staticClass:"box-item margin-top-5",attrs:{enterable:!1,effect:"dark",content:t.$t("dialog.materialLibrary.model.tooltip"),placement:"top"}},[a("CommonSVG",{attrs:{color:"#DCDCDC",size:"20","icon-class":"mode_icon"}})],1)],1),a("el-tooltip",{staticClass:"box-item",attrs:{enterable:!1,effect:"dark",content:t.$t("dialog.materialLibrary.model.tooltip1"),placement:"top"},nativeOn:{click:function(a){return t.handleAddFeature(e,"custom")}}},[a("CommonSVG",{attrs:{color:"#DCDCDC",size:"20","icon-class":"model_click_feature"}})],1)],1)]),a("el-tooltip",{staticClass:"box-item",attrs:{"open-delay":"1000",enterable:!1,effect:"dark",content:e.featureName,placement:"bottom"}},[a("p",{staticClass:"title"},[e.loaded&&e.loaded.length>0?a("CommonSVG",{attrs:{color:"rgb(103, 194, 58)",size:"17","icon-class":"point"}}):t._e(),t._v(" "+t._s(e.featureName)+" ")],1)])],1)})),0):t._e(),t.toggleMode?t._e():a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"label-content label-list-content",class:{h100:t.loading},attrs:{"element-loading-spinner":"el-icon-loading","element-loading-background":"rgba(0, 0, 0, 0)"}},t._l(t.filterCurrentDatas,(function(e,i){return a("div",{key:e.featureID,staticClass:"label-item overflow-hidden"},[a("div",{staticClass:"thumb"},[a("div",{staticClass:"order-number"},[t._v(t._s(i+1))]),e.timeseries?a("div",{staticClass:"time-series-icon"},[a("CommonSVG",{attrs:{size:"14","icon-class":"time_series",color:"#FFFFFF"}})],1):t._e(),""==e.thumbnail||"Project"==e.thumbnail?a("img",{attrs:{src:t.defaultImg,alt:""}}):a("img",{attrs:{src:"data:image/png;base64,"+e.thumbnail,alt:""}})]),a("div",{staticClass:"desc"},[a("el-tooltip",{staticClass:"box-item",attrs:{"open-delay":"1000",enterable:!1,effect:"dark",content:e.featureName,placement:"right"}},[a("p",{staticClass:"title"},[e.loaded&&e.loaded.length>0?a("CommonSVG",{attrs:{color:"rgb(103, 194, 58)",size:"17","icon-class":"point"}}):t._e(),t._v(" "+t._s(e.featureName)+" ")],1)]),a("div",{staticClass:"color-98A3B3"},[t._v(t._s(e.createTime.replace(/(.+)T(.+)\.\d+\+?.+/g,"$1 $2")))])],1),a("div",{staticClass:"comSvg-right"},[a("a",{on:{click:function(a){return a.preventDefault(),t.handleAddFeature(e,"default")}}},[a("el-tooltip",{staticClass:"box-item margin-top-5",attrs:{enterable:!1,effect:"dark",content:t.$t("dialog.materialLibrary.model.tooltip"),placement:"top"},nativeOn:{click:function(a){return t.handleAddFeature(e,"default")}}},[a("CommonSVG",{attrs:{color:"#DCDCDC",size:"20","icon-class":"mode_icon"}})],1)],1),a("el-tooltip",{staticClass:"box-item margin-left-10 margin-right-10",attrs:{enterable:!1,effect:"dark",content:t.$t("dialog.materialLibrary.model.tooltip1"),placement:"top"},nativeOn:{click:function(a){return t.handleAddFeature(e,"custom")}}},[a("CommonSVG",{attrs:{color:"#DCDCDC",size:"20","icon-class":"model_click_feature"}})],1)],1)])})),0),this.labelDatas.length>0?a("div",{staticClass:"total"},[t._v(" "+t._s(t.$t("dialog.materialLibrary.model.totalText",{num:t.labelDatas.length}))+" ")]):t._e()])}),o=[],r=a("c7eb"),c=a("1da1"),l=(a("d3b7"),a("3ca3"),a("ddb0"),a("4ec9"),a("159b"),a("ac1f"),a("841c"),a("c740"),a("a434"),a("cb29"),!0),d={name:"ModelObj",components:{CommonSVG:function(){return a.e("chunk-51f90655").then(a.bind(null,"17d0"))}},data:function(){return{loading:!1,labelDatas:[],defaultImg:a("5a44"),projectID:"",searchValue:"",toggleMode:!0,modelFeatureID:new Map,locked:!1}},computed:{filterCurrentDatas:function(){var t=[],e=this.searchValue.toLowerCase();return this.labelDatas.forEach((function(a){var i=a.featureName.toLowerCase();-1!=i.search(e)&&t.push(a)})),t},settingActive:function(){return this.$store.state.widget.settingActive}},watch:{settingActive:function(t){""!=t&&(this.locked=!1)}},mounted:function(){this.projectID=this.getProjectId(),this.getAllFeatures(),this.toggleMode=l,this.featuresEventListener("on")},methods:{featuresEventListener:function(t){window.scene.mv.events.featureListChanged[t]("remove",this.handleRemoveModel)},handleAddModel:function(t){var e=window.scene.features.get(t);if("model"==e.type){this.modelFeatureID.set(e.id,e.modelID);var a=this.labelDatas.findIndex((function(t){return t.featureID==e.modelID}));void 0==a.loaded&&(a.loaded=[]),a.loaded.push(!0)}},handleRemoveModel:function(t){if(this.modelFeatureID.has(t)){var e=this.modelFeatureID.get(t),a=this.labelDatas.findIndex((function(t){return t.featureID==e}));this.labelDatas[a].loaded.splice(0,1)}},handleToggleMode:function(t){this.toggleMode=t,l=t},resetData:function(){""==this.settingActive&&(this.locked=!1)},handleAddFeature:function(t,e){this.locked||(""==this.settingActive?(this.locked=!0,"custom"==e?this.$parent.$parent.checkedCoordinateAddFeature(t):this.$parent.$parent.modelDefaultCoordinateAddFeature(t)):this.$message(this.$t("messageTips.exitEditingStatus")))},getProjectId:function(){return this.$store.state.scene.currentSceneProjectID},getAllFeatures:function(){var t=this;return Object(c["a"])(Object(r["a"])().mark((function e(){var a,i,n;return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a=t.getLoadedModels(),i=t.projectID,t.loading=!0,e.next=5,t.$api.getAllFeatures({VaultID:i});case 5:n=e.sent,n.data.forEach((function(t){if(a.has(t.featureID)){var e=a.get(t.featureID);t.loaded=Array(e).fill(!0)}t.type="model",t.isDragData=!0,t.maxVersion=t.currentVersion})),t.loading=!1,t.labelDatas=n.data;case 9:case"end":return e.stop()}}),e)})))()},getLoadedModels:function(){var t=this,e=new Map;return window.scene.features.forEach((function(a){if("model"==a.type){if(e.has(a.modelID)){var i=e.get(a.modelID);i+=1,e.set(a.modelID,i)}else e.set(a.modelID,1);t.modelFeatureID.set(a.id,a.modelID)}})),e}},beforeDestroy:function(){this.featuresEventListener("off")}},u=d,g=(a("34e5"),a("2877")),m=Object(g["a"])(u,s,o,!1,null,"2760a9f4",null),p=m.exports,h=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"resources-label-container"},[a("div",{staticClass:"label-container"},[a("div",{staticClass:"label-content"},t._l(t.underlayWmtsDatas,(function(e){return a("div",{key:e.id,staticClass:"label-item cursor-btn",on:{click:function(a){return t.handleAddwmtsFeature(e,e.id)}}},[a("div",{class:[{active:e.id==t.underlayRadio},"thumb"]},[a("img",{attrs:{src:e.thumb,alt:""}}),a("i",{staticClass:"icon el-icon-plus"})]),a("p",{class:["title",{arcGIS:"arcGIS"==e.label}]},[t._v(t._s(e.title))])])})),0),a("div",{staticClass:"padding-left-20 padding-right-20 margin-bottom-10 fieldset-x color-dcdcdc"},[t._v("Mapbox")]),a("div",{staticClass:"label-content"},t._l(t.underlayLabelDatas,(function(e,i){return a("div",{key:i,staticClass:"label-item cursor-btn",on:{click:function(a){return t.handleAddFeature(e,i)}}},[a("div",{class:[{active:i==t.underlayRadio},"thumb"]},[a("img",{attrs:{src:e.thumb,alt:""}}),a("i",{staticClass:"icon el-icon-plus"})]),a("p",{staticClass:"title"},[t._v(t._s(e.title))])])})),0)]),a("div",{staticClass:"projection"},[a("span",[t._v(t._s(t.$t("dialog.materialLibrary.underlay.label")))]),a("el-radio",{staticClass:"margin-right-10",staticStyle:{color:"var(--primary-text-color)"},attrs:{label:"globe"},on:{input:t.setProjection},model:{value:t.projectionValue,callback:function(e){t.projectionValue=e},expression:"projectionValue"}},[t._v(" "+t._s(t.$t("dialog.materialLibrary.underlay.label1"))+" ")]),a("el-radio",{staticStyle:{color:"var(--primary-text-color)"},attrs:{label:"mercator"},on:{input:t.setProjection},model:{value:t.projectionValue,callback:function(e){t.projectionValue=e},expression:"projectionValue"}},[t._v(" "+t._s(t.$t("dialog.materialLibrary.underlay.label2"))+" ")])],1)])},f=[],b=(a("b0c0"),{name:"SourceUnderlay",props:["currentData"],data:function(){return{underlayWmtsDatas:[{title:"天地图",thumb:a("71d2"),url:"",type:"wmts",label:"tianditu",pathname:"/img_w/wmts?tk=1e1e425c920f09544d68abc6d3817d04&SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&FORMAT=tiles"},{title:"天地图(街道)",thumb:a("b39c"),url:"",url1:"",type:"wmts",label:"tianditu_streets",pathname:"/vec_w/wmts?tk=1e1e425c920f09544d68abc6d3817d04&SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&FORMAT=tiles",pathname1:"/cva_w/wmts?tk=1e1e425c920f09544d68abc6d3817d04&SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cva&STYLE=default&TILEMATRIXSET=w&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&FORMAT=tiles"},{title:"ArcGIS Online",thumb:a("6674"),url:"https://services.arcgisonline.com/arcgis/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}",type:"wmts",label:"arcGIS"}],underlayLabelDatas:[{title:"影像注记",thumb:a("abfc"),url:"/ref/style/satellite.json",type:"underlay",label:"satelliteStreets"},{title:"影像",thumb:a("631b"),url:"/ref/style/lightsatellite.json",type:"underlay",label:"satelliteV9"},{title:"黑色",thumb:a("bde1"),url:"/ref/style/dark.json",type:"underlay",label:"black"},{title:"注记",thumb:a("3914"),url:"/ref/style/annotation.json",type:"underlay",label:"annotation"},{title:"白色",thumb:a("dec6"),url:"/ref/style/light.json",type:"underlay",label:"white"},{title:"太空灰",thumb:a("9629"),url:"/ref/style/grey.json",type:"underlay",label:"spaceGrey"},{title:"街道",thumb:a("bef7"),url:"/ref/style/streets.json",type:"underlay",label:"vector"},{title:"墨绿",thumb:a("855c"),url:"/ref/style/darkgreen.json",type:"underlay",label:"blackishGreen"},{title:"荧光",thumb:a("cf80"),url:"/ref/style/globe.json",type:"underlay",label:"fluorescence"},{title:"深蓝科技",thumb:a("bbc9"),url:"/ref/style/darkblue.json",type:"underlay",label:"darkBlue"},{title:"蓝色极简",thumb:a("7bd2"),url:"/ref/style/lightblue.json",type:"underlay",label:"lightBlue"},{title:"空白球体",thumb:a("9a49"),url:"/ref/style/blank.json",type:"underlay",noToken:!0,label:"blank"}],underlayRadio:999,projectionValue:"globe"}},created:function(){this.setUnderlay();var t=window.scene.findFeature("underlay");t&&(this.underlayRadio=this.underlayLabelDatas.findIndex((function(e){return e.title==t.name})),this.projectionValue=t.data.projectionValue);var e=window.scene.findFeature("underlay_wmts");if(e){var a=this.underlayWmtsDatas.findIndex((function(t){return t.title==e.name}));this.underlayRadio=-1*(a+1)}this.setRandomWmtsLink()},methods:{handleAddwmtsFeature:function(t,e){var a=this;if(this.underlayRadio==e)return!1;window.scene.features.has("underlay")||this.addUnderlayFeature(11),window.scene.removeFeature("underlay_wmts_streets",!0),window.scene.removeFeature("underlay_wmts",!0),setTimeout((function(){var i=window.scene.findFeature("underlay_wmts");if(i||(i=window.scene.addFeature("wmts","underlay_wmts")),i.name=t.title,i.url=t.url,i.priority=0,i.always=!1,i.loaded||i.load(),"tianditu_streets"==t.label){var n=window.scene.addFeature("wmts","underlay_wmts_streets");n.name=t.title,n.url=t.url1,n.priority=0,n.always=!1,n.loaded||n.load()}a.$deepUpdateScene("wmts"),a.underlayRadio=e,a.$emit("close"),window.scene.render(),a.$store.commit("setActivedType","")}),500)},setRandomWmtsLink:function(){var t=Math.floor(8*Math.random()),e=window.location.protocol,a="";a="http:"==e?"http://t".concat(t,".tianditu.com"):"https://t".concat(t,".tianditu.gov.cn"),this.underlayWmtsDatas[0].url=a+this.underlayWmtsDatas[0].pathname,this.underlayWmtsDatas[1].url=a+this.underlayWmtsDatas[1].pathname,this.underlayWmtsDatas[1].url1=a+this.underlayWmtsDatas[1].pathname1},setUnderlay:function(){var t=this.$i18n.locale,e=this.$i18n.getLocaleMessage(t).dialog.materialLibrary.underlay.labels;this.underlayLabelDatas.forEach((function(t){t.title=e[t.label]})),this.underlayWmtsDatas.forEach((function(t,a){t.title=e[t.label],t.id=-1*(a+1)}))},setProjection:function(t){var e=this;if(window.scene.features.has("underlay")){var a=window.scene.findFeature("underlay");a.setProjection(t),window.scene.postData({projectionValue:t},a.dataKey),this.$nextTick((function(){e.$deepUpdateScene("underlay")}))}},handleAddFeature:function(t,e){if(!t.noToken){var a=this.beforeValidate();if(!a)return!1}if(this.underlayRadio==e)return!1;"blank"!=t.label&&(window.scene.removeFeature("underlay_wmts",!0),window.scene.removeFeature("underlay_wmts_streets",!0),this.$deepUpdateScene("wmts")),this.underlayRadio=e,this.addUnderlayFeature(e)},beforeValidate:function(){this.$notify.closeAll();var t=window.scene.config.mapboxToken;return""!=t&&void 0!=t&&"YOUR_MAPBOX_ACCESS_TOKEN"!=t||(this.$message.error(this.$t("dialog.materialLibrary.underlay.message")),this.$notify.info({title:this.$t("dialog.materialLibrary.underlay.message2"),dangerouslyUseHTMLString:!0,message:this.$t("dialog.materialLibrary.underlay.message1"),duration:15e3}),!1)},addUnderlayFeature:function(t){var e=this;return Object(c["a"])(Object(r["a"])().mark((function a(){var i,n,s;return Object(r["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return i=window.scene.features.get("underlay"),n=!0,i?i.dispose():n=!1,a.next=5,e.asyncRequestMapBoxUrl(t).catch((function(t){return console.error(t),e.$message.error(e.$t("others.requestError")),"error"}));case 5:if(s=a.sent,i=null,"error"!=s){a.next=9;break}return a.abrupt("return",!1);case 9:i=window.scene.addFeature("underlay"),i.url=s,i.name=e.underlayLabelDatas[t].title,i.load(),n?e.$notify({title:e.$t("featureDatas.underlay.name"),message:e.$t("dialog.materialLibrary.updated",{name:i.name}),type:"success"}):(e.$store.commit("setSceneUnderlayStatus",{underlayState:!0}),e.$notify({title:e.$t("featureDatas.underlay.name"),message:e.$t("messageTips.AddedToScene"),type:"success"})),window.scene.postData({projectionValue:e.projectionValue},i.dataKey),i.setProjection(e.projectionValue),setTimeout((function(){e.$deepUpdateScene("underlay"),e.$emit("close"),window.scene.render()}),500),e.$store.commit("setActivedType","");case 18:case"end":return a.stop()}}),a)})))()},asyncRequestMapBoxUrl:function(t){var e=this;return new Promise((function(a,i){var n=window.IP_CONFIG.SOURCES_URL+e.underlayLabelDatas[t].url;fetch(n).then((function(t){return t.json()})).then((function(t){t.sprite=window.IP_CONFIG.SOURCES_URL+"/ref/style/sprite",t.glyphs=window.IP_CONFIG.SOURCES_URL+"/fonts/{fontstack}/{range}.pbf",a(t)})).catch((function(t){i(t)}))}))}}}),y=b,v=(a("4552"),Object(g["a"])(y,h,f,!1,null,"26107e5c",null)),A=v.exports,w=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"resources-label-container"},[a("div",{staticClass:"label-content"},t._l(t.skyBoxOptions,(function(e,i){return a("div",{key:i,staticClass:"label-item cursor-btn",on:{click:function(e){return t.handleAddFeature(i)}}},[a("div",{class:[{active:i==t.skyBoxValueRadio},"thumb"]},[a("img",{attrs:{src:e.thumb,alt:""}}),a("i",{staticClass:"icon el-icon-plus"})]),a("p",{staticClass:"title"},[t._v(t._s(e.title))])])})),0)])},D=[],C={name:"SourceSkyBox",props:["currentData"],data:function(){return{skyBoxOptions:[{title:"早晨",thumb:a("7f83"),value:"morning",type:"underlay"},{title:"中午",thumb:a("5084"),value:"noon",type:"underlay"},{title:"傍晚",thumb:a("7477"),value:"evening",type:"underlay"},{title:"夜间",thumb:a("6546"),value:"night",type:"underlay"}],skyBoxValueRadio:-1}},created:function(){this.setSkybox();var t=window.scene.findFeature("skybox");t&&(this.skyBoxValueRadio=this.skyBoxOptions.findIndex((function(e){return e.title==t.name})))},methods:{setSkybox:function(){var t=this.$i18n.locale,e=this.$i18n.getLocaleMessage(t).dialog.materialLibrary.skybox.labels;this.skyBoxOptions.forEach((function(t,a){t.title=e[a]}))},handleAddFeature:function(t){this.skyBoxValueRadio=t,this.addSkyboxFeature()},addSkyboxFeature:function(){var t=this,e=window.scene.findFeature("skybox"),a=!0;e?e=window.scene.features.get("skybox"):(e=window.scene.addFeature("skybox"),e.load(),a=!1),e.name=this.skyBoxOptions[this.skyBoxValueRadio].title,e.setTime(this.skyBoxOptions[this.skyBoxValueRadio].value),a?this.$notify({title:this.$t("featureDatas.skybox.name"),message:this.$t("dialog.materialLibrary.updated",{name:e.name}),type:"success"}):this.$notify({title:"".concat(this.$t("featureDatas.skybox.name"),"-").concat(e.name),message:this.$t("messageTips.AddedToScene"),type:"success"}),setTimeout((function(){t.$deepUpdateScene("skybox"),t.$emit("close"),window.scene.render()}),1e3),this.$store.commit("setActivedType","")}}},S=C,k=(a("04de"),Object(g["a"])(S,w,D,!1,null,"61ecf956",null)),E=k.exports,x=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"resources-label-container"},[i("div",{staticClass:"top-filter"},[i("div",{staticClass:"search"},[i("el-input",{attrs:{placeholder:t.$t("dialog.materialLibrary.placeholder"),size:"mini"},model:{value:t.searchValue,callback:function(e){t.searchValue=e},expression:"searchValue"}},[i("i",{staticClass:"el-input__icon el-icon-search",attrs:{slot:"prefix"},slot:"prefix"})])],1),!t.isVothing&&t.isFilterVisible?i("div",{staticClass:"filter"},[i("el-popover",{attrs:{placement:"bottom",trigger:"click",width:"200"},model:{value:t.popoverVisible,callback:function(e){t.popoverVisible=e},expression:"popoverVisible"}},[i("ul",{staticClass:"material-filter-item"},t._l(t.filterKey,(function(e){return i("li",{key:e,class:{active:e==t.currentKey},on:{click:function(a){return t.materialFilter(e)}}},[t._v(" "+t._s(e)+" ")])})),0),i("div",{staticClass:"cursor-btn",attrs:{slot:"reference"},slot:"reference"},[i("CommonSVG",{attrs:{size:"18","icon-class":"screening"}}),i("span",{staticClass:"margin-left-5"},[t._v(t._s(t.currentKey))])],1)])],1):t._e()]),""!=t.searchValue||t.loading?t._e():i("div",{staticClass:"custom-btn cursor-btn",on:{click:function(e){return t.addCustom()}}},[i("img",{attrs:{src:a("e11d")}})]),t.isVothing&&t.isTab?i("div",{staticClass:"vo-custom-tab",style:""===t.searchValue||t.loading?"":"margin-top: 35px;"},[i("span",{class:"sys"===t.isType?"tabs active":"tabs",on:{click:function(e){return t.getSourceType("sys")}}},[t._v(" "+t._s(t.$t("dialog.materialLibrary.label"))+" ")]),i("span",{class:"my"===t.isType?"tabs active":"tabs",on:{click:function(e){return t.getSourceType("my")}}},[t._v(" "+t._s(t.$t("dialog.materialLibrary.label1"))+" ")])]):t._e(),i("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"label-content",class:{h100:t.loading,"pd-t40":t.isVothing&&""!==t.searchValue&&"panorama"===t.currentData.type||!t.isVothing&&""!==t.searchValue},attrs:{"element-loading-spinner":"el-icon-loading","element-loading-background":"rgba(0, 0, 0, 0)"}},t._l(t.filterCurrentDatas,(function(e,a){return i("div",{key:e.title,staticClass:"label-item",on:{click:function(e){return t.setCurIndex(a)}}},[i("div",{staticClass:"thumb"},[i("img",{attrs:{src:t.getItemImg(e.thumb),alt:e.title}}),i("div",{staticClass:"comSvg"},[i("el-tooltip",{staticClass:"box-item",attrs:{enterable:!1,effect:"dark",content:t.$t("dialog.materialLibrary.tooltip"),placement:"top"},nativeOn:{click:function(a){return t.addFgltfFbxFeature(e,0)}}},[i("CommonSVG",{attrs:{color:"#DCDCDC",size:"20","icon-class":"once"}})],1),!t.isVothing||t.isVothing&&t.isTab?i("el-tooltip",{staticClass:"box-item",attrs:{enterable:!1,effect:"dark",content:t.$t("dialog.materialLibrary.tooltip1"),placement:"top"},nativeOn:{click:function(a){return t.addFgltfFbxFeature(e,1)}}},[i("CommonSVG",{attrs:{color:"#DCDCDC",size:"20","icon-class":"continuous_feature"}})],1):t._e()],1)]),i("el-tooltip",{staticClass:"box-item",attrs:{"open-delay":"1000",enterable:!1,effect:"dark",content:e.title,placement:"bottom"}},[i("p",{staticClass:"title"},[t._v(t._s(e.title))])])],1)})),0)])},I=[],M=a("2909"),T=(a("a630"),a("5319"),a("1276"),{name:"SceneMaterialLibrary",props:["currentData","vothingElementArr"],components:{CommonSVG:function(){return a.e("chunk-51f90655").then(a.bind(null,"17d0"))}},data:function(){return{materialList:[],loading:!0,allDatas:new Map,currentDatas:[],filterKey:[this.$t("dialog.materialLibrary.all")],currentKey:this.$t("dialog.materialLibrary.all"),popoverVisible:!1,searchValue:"",category:"gltf",curIndex:"",isType:"sys",isTab:!1,betaData:[],isFilterVisible:!0}},created:function(){this.isVothing?this.getVothingData():(this.loading=!1,this.isFilterVisible=!1)},computed:{filterCurrentDatas:function(){var t=this,e=[];return this.currentDatas.forEach((function(a){-1!=a.title.search(t.searchValue)&&e.push(a)})),e},activeDialog:function(){return this.$store.state.dialog.activeDialog},projectId:function(){return window.localStorage.getItem("cj-projectId")},isVothing:function(){return this.$store.state.menuList.isVothing}},methods:{getItemImg:function(t){if(!this.isVothing)return t;if(t)return"panorama"===this.currentData.type?"".concat(window.IP_CONFIG.PANO_URL,"/Panorama").concat(t,"/cover.png"):"http"===t.substring(0,4)?t:window.IP_CONFIG.BASE_URL+t;switch(this.currentData.type){case"dem":return a("7ecc");case"gltf":case"fbx":return a("dc7c");case"_3dTiles":return a("2f13");case"video":return a("05c2");case"wmts":case"wms":case"tms":return a("dec5");case"geoJSON":case"shp":case"kml":return a("c14d")}},setCurIndex:function(t){this.curIndex=t===this.curIndex?"":t},addCustom:function(){this.activeDialog.includes("ElementLink")||(this.curIndex="",this.$store.commit("toggleActiveDialog","ElementLink"),this.$store.commit("toggleActiveDialog","Source"))},addFgltfFbxFeature:function(t,e){this.$emit("checkedCoordinateAddFeature",{item:t,addType:e})},handleAddFeature:function(t){this.$emit("checkedCoordinateAddFeature",t)},materialFilter:function(t){var e=this;if(this.currentKey=t,this.searchValue="",this.currentDatas=[],t==this.$t("dialog.materialLibrary.all")){var a=Array.from(this.allDatas.values());a.forEach((function(t){var a;(a=e.currentDatas).push.apply(a,Object(M["a"])(t))}))}else this.isVothing&&t===this.$t("dialog.materialLibrary.vothing.label")?this.currentDatas=this.betaData:this.currentDatas=this.allDatas.get(t);this.popoverVisible=!1},getAllList:function(){var t=this;return Object(c["a"])(Object(r["a"])().mark((function e(){var a,i;return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a=t.category,e.next=3,t.$api.getAllMaterialList();case 3:i=e.sent,i.data.length>0&&(i.data.forEach((function(e){var i;0!=e.Materials.length&&(e.Materials.forEach((function(t){t.thumb=window.IP_CONFIG.BASE_URL+t.thumb,t.type=a,t.isDragData=!0,t.defaultSource=!0})),t.allDatas.set(e.CategoryName,e.Materials),(i=t.currentDatas).push.apply(i,Object(M["a"])(e.Materials)))})),t.filterKey=t.filterKey.concat(Array.from(t.allDatas.keys()))),t.loading=!1;case 6:case"end":return e.stop()}}),e)})))()},getVothingData:function(){var t=this;return Object(c["a"])(Object(r["a"])().mark((function e(){return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.loading=!1,"gltf"!==t.currentData.type&&"fbx"!==t.currentData.type||(t.isTab=!0,t.betaData=[],t.vothingElementArr.forEach((function(e,a){t.betaData.push({title:e.ElementName,thumb:e.ElementLogo,fileSrc:e.ElementValue,isDragData:!0,type:t.currentData.type,defaultSource:!0,IsSystem:e.IsSystem})})),t.betaData.length>0?t.getSourceType("sys"):t.addCustom()),"panorama"===t.currentData.type&&(t.isTab=!1,t.betaData=[],t.vothingElementArr.forEach((function(e,a){t.betaData.push({title:e.PbName,thumb:e.PbUrl,fileSrc:e.PbUrl,isDragData:!0,type:t.currentData.type,defaultSource:!0})})),t.currentDatas=t.betaData);case 3:case"end":return e.stop()}}),e)})))()},getSourceType:function(t){this.isType=t;var e=[];this.betaData.forEach((function(a){"sys"===t&&a.IsSystem&&e.push(a),"my"!==t||a.IsSystem||e.push(a)})),this.currentDatas=e},getPublicImg:function(){var t=this,e=this.category,i=null;switch(e){case"gltf":i=a("1492");break;case"fbx":i=a("0d5b");break;case"environment":i=a("ee57");break}i.keys().forEach((function(a,i){var n=a.replace(/(.*\/)*([\s\S]*?)/gi,"$2"),s=n.split(".")[0],o="".concat("","/image/").concat(e,"/").concat(n);if("environment"==e)t.materialList.push({id:i,title:s,thumb:o,isDragData:!0,type:e,defaultSource:!0});else{var r="/".concat(e,"/").concat(s,".").concat(e);t.materialList.push({id:i,title:s,thumb:o,fileSrc:r,isDragData:!0,type:e,defaultSource:!0})}}))}}}),F=T,L=(a("a414"),Object(g["a"])(F,x,I,!1,null,"ee296a2a",null)),V=L.exports,B=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"resources-label-container"},[i("div",{staticClass:"custom-btn",on:{click:function(e){return t.addCustom()}}},[i("img",{attrs:{src:a("e11d")}})]),t.isVothing?i("div",{staticClass:"vo-custom-tab"},[i("span",{class:"sys"===t.isType?"tabs active":"tabs",on:{click:function(e){return t.getSourceType("sys")}}},[t._v(" "+t._s(t.$t("dialog.materialLibrary.label"))+" ")]),i("span",{class:"my"===t.isType?"tabs active":"tabs",on:{click:function(e){return t.getSourceType("my")}}},[t._v(" "+t._s(t.$t("dialog.materialLibrary.label1"))+" ")])]):t._e(),i("div",{staticClass:"label-content"},t._l(t.imgList,(function(e){return i("div",{key:e.id,staticClass:"label-item cursor-btn",on:{click:function(a){return t.handleAddFeature(e)}}},[i("div",{staticClass:"thumb"},[i("img",{attrs:{src:e.thumb,alt:""}}),i("i",{staticClass:"icon el-icon-plus"})]),i("p",{staticClass:"title"},[t._v(t._s(e.fileName))])])})),0)])},R=[],O=a("b893"),Q={name:"SourceElementImage",props:["currentData"],data:function(){return{imgList:[],isType:"sys",allList:[]}},computed:{activeDialog:function(){return this.$store.state.dialog.activeDialog},projectId:function(){return window.localStorage.getItem("cj-projectId")},isVothing:function(){return this.$store.state.menuList.isVothing}},created:function(){this.isVothing&&this.getVothingImgs()},methods:{getVothingImgs:function(){var t=this;return Object(c["a"])(Object(r["a"])().mark((function e(){var a,i;return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a={ProjectId:t.projectId,ElementTypeId:"Texture",PageIndex:1,PageSize:9999,Key:""},e.next=3,t.$api.GetElementList(a);case 3:i=e.sent,200===i.data.ErrorCode&&(t.allList=i.data.Result.List,t.getSourceType("sys"));case 5:case"end":return e.stop()}}),e)})))()},getItemImg:function(t){if(!this.isVothing)return t;if(t)return"http"===t.substring(0,4)?t:window.IP_CONFIG.BASE_URL+t;switch(this.currentData.type){case"dem":return a("7ecc");case"gltf":case"fbx":return a("dc7c");case"_3dTiles":return a("2f13");case"video":return a("05c2");case"wmts":case"wms":case"tms":return a("dec5");case"geoJSON":case"shp":case"kml":return a("c14d")}},getSourceType:function(t){var e=this;this.isType=t;var a=[];this.allList.forEach((function(i){"sys"===t&&i.IsSystem&&a.push({id:i.ElementId,title:e.$t("featureDatas.polygon.extend.environment.name"),fileName:i.ElementName,thumb:e.getItemImg(i.ElementValue),isDragData:!0,type:e.currentData.type,subType:e.currentData.subType,defaultSource:!0}),"my"!==t||i.IsSystem||a.push({id:i.ElementId,title:e.$t("featureDatas.polygon.extend.environment.name"),fileName:i.ElementName,thumb:e.getItemImg(i.ElementValue),isDragData:!0,type:e.currentData.type,subType:e.currentData.subType,defaultSource:!0})})),this.imgList=a},getPublicImg:function(){var t=this,e=this.currentData.subType,i=null;switch(e){case"environment":i=a("ee57");break}i.keys().forEach((function(a,i){var n=a.replace(/(.*\/)*([\s\S]*?)/gi,"$2"),s=n.split(".")[0],o="".concat("","/image/").concat(e,"/").concat(n);if("environment"==e)t.imgList.push({id:i,title:t.$t("featureDatas.polygon.extend.environment.name"),fileName:s,thumb:o,isDragData:!0,type:t.currentData.type,subType:t.currentData.subType,defaultSource:!0});else{var r="/".concat(e,"/").concat(s,".").concat(e);t.imgList.push({id:i,title:s,thumb:o,fileSrc:r,isDragData:!0})}}))},addCustom:function(){this.activeDialog.includes("ElementLink")||(this.$store.commit("toggleActiveDialog","ElementLink"),this.$store.commit("toggleActiveDialog","Source"))},closeDialog:function(){this.$emit("close")},checkedElementSource:function(t){var e=this.dragData.type,a=window.location.origin+Object(O["g"])()+t.thumb;this.styleFormDatas.datas[e].list[0].value=a,this.freeSketchPolygon("polygon")},handleAddFeature:function(t){this.$emit("freeSketchPolygon","polygon",t),this.closeDialog()}}},U=Q,G=(a("92df"),Object(g["a"])(U,B,R,!1,null,"7ffa8b9d",null)),H=G.exports,j=a("0e1b"),P={name:"Source",props:["vothingElementArr"],mixins:[j["a"]],components:{ModelObj:p,SourceUnderlay:A,SourceSkyBox:E,SceneMaterialLibrary:V,SourceElementImage:H},data:function(){return{currentData:{title:"",type:"",name:""},tablabel:"one",currentMenu:{},dialogLeft:0,sceneLabelDatas:[],dialogHeight:200,dialogWidth:300,TopToolbarHeight:122,currentSelectSource:null,currentListenerEvent:"",addType:0,dataKey:Date.now()}},computed:{sourceCurrentTypeEq:function(){return this.$store.state.dialog.sourceCurrentTypeEq},dragData:function(){var t=["gltf","fbx","panorama","_3dTiles"];return t.includes(this.$store.state.scene.dragOverData.type)&&(this.dataKey=Date.now()),this.$store.state.scene.dragOverData},setSourceTitle:function(){var t={model:this.$t("featureDatas.model.name"),underlay:this.$t("featureDatas.underlay.name"),skybox:this.$t("featureDatas.skybox.name"),panorama:this.$t("featureDatas.panorama.name"),_3dTiles:this.$t("featureDatas._3dTiles.name"),gltf:this.$t("dialog.materialLibrary.gltf"),fbx:this.$t("dialog.materialLibrary.fbx"),polygon:this.$t("dialog.materialLibrary.polygon")};return t[this.dragData.type]},isVothing:function(){return this.$store.state.menuList.isVothing}},created:function(){var t=document.querySelector(".sceneManageDom").style.left;parseInt(t)<0?this.dialogLeft=30:this.dialogLeft=335;var e=["skybox","underlay"];e.includes(this.dragData.type)||this.changeManageSize(),"skybox"==this.dragData.type?(this.dialogWidth=385,this.dialogHeight=185):"underlay"==this.dragData.type?this.dialogHeight=430:(this.dialogWidth=292,this.dialogHeight=100,this.changeManageSize()),window.addEventListener("resize",this.changeManageSize)},watch:{"dragData.type":function(t){"skybox"==t?(this.dialogWidth=385,this.dialogHeight=185):"underlay"==t?(this.dialogWidth=300,this.dialogHeight=430):(this.dialogWidth=300,this.dialogHeight=100,this.changeManageSize()),this.offEvents()}},mounted:function(){},methods:{initData:function(){this.currentData.type=this.dragData.type,this.currentData.title=this.dragData.title},onCheckedCoordinate:function(t){var e=[0,0,0],a=window.scene.mv._THREE;try{var i=window.scene.queryPosition(new a.Vector2(t.clientX,t.clientY));""!=i&&void 0!=i&&(e=window.scene.mv.tools.coordinate.vector2mercator(i))}catch(n){console.error(n)}this.styleFormDatas.longitude=e[0],this.styleFormDatas.latitude=e[1],this.styleFormDatas.altitude=e[2],this.addType&&window.scene.mv.status.selectable&&(window.scene.mv.status.selectable=!1),this.addType||this.offEvents(),this.submitSetting()},checkedCoordinateAddFeature:function(t){this.currentSelectSource=t.item?t.item:t,this.addType=t.item?t.addType:0,this.$refs.linkView.$el.style.visibility="hidden",window.scene.mv.status.selectable=!1;var e="",a=this.currentSelectSource.type;switch(a){case"model":e=this.currentSelectSource.featureName;break;case"fbx":e=this.currentSelectSource.title;break;case"gltf":e=this.currentSelectSource.title;break;case"panorama":e=this.currentSelectSource.title;break;case"_3dTiles":e=this.currentSelectSource.title;break}this.currentListenerEvent="onCheckedCoordinate",window.scene.mv.events.singleSelection.on("default",this.onCheckedCoordinate),document.addEventListener("keyup",this.onEscKeyUp),this.addType?(this.$message(this.$t("messageTips.continuousAddition")),window.scene.mv.events.contextmenu.on("default",this.offCheckedCoordinate),window["offCheckedCoordinate"]=this.offCheckedCoordinate):this.$message(this.$t("messageTips.publishSomething",{name:e}))},offCheckedCoordinate:function(){window.scene.mv.events.singleSelection.off("default",this.onCheckedCoordinate),window.scene.mv.events.contextmenu.off("default",this.offCheckedCoordinate),this.$message(this.$t("messageTips.exitContinuousAddition")),this.$store.commit("setActivedType",""),this.closeDialog(),window.scene.mv.status.selectable=!0,this.addType=0,window.offCheckedCoordinate&&(window.offCheckedCoordinate=null,delete window.offCheckedCoordinate)},freeSketchPolygon:function(t,e){this.currentSelectSource=e,window.scene.mv.tools.draw.active(),this.$refs.linkView.$el.style.visibility="hidden",this.$store.commit("toogleDrawState",!0),"polygon"==t?(window.scene.mv.tools.draw.allowConCavePolygon=!0,window.scene.mv.tools.draw.startDrawPolygon()):"polyline"==t?window.scene.mv.tools.draw.startDrawLine():"rectangle"==t&&window.scene.mv.tools.draw.startDrawPolygon(),this.$message({showClose:!0,message:this.$t("messageTips.freeSketch.defaultMsg"),duration:7e3}),"rectangle"==t?(this.currentListenerEvent="handleFreeSketchRectangle",window.scene.mv.tools.draw.finishDrawEvent.on("default",this.handleFreeSketchRectangle)):(this.currentListenerEvent="handleFreeSketchPolygon",window.scene.mv.tools.draw.finishDrawEvent.on("default",this.handleFreeSketchPolygon)),document.addEventListener("keyup",this.onEscKeyUp)},handleFreeSketchPolygon:function(){var t=window.scene.mv.tools.draw.getAllData(),e=this.currentSelectSource.type,a=this.styleFormDatas.datas[e].list;if(this.$store.commit("toogleDrawState",!1),t.polygon.length>0){var i=t.polygon[0].points.length;if(i-1<=2)this.$message.error(this.$t("messageTips.freeSketch.errorMsg1"));else{for(var n=[],s=0;s<i-1;s++)n[s]=[t.polygon[0].points[s].x,t.polygon[0].points[s].y,t.polygon[0].points[s].z];a[1].position=n;var o=["environment"];if("polygon"==this.currentSelectSource.type&&o.includes(this.currentSelectSource.subType)){var r=window.scene.mv.tools.coordinate.vector2mercator(t.polygon[0].points[0]);this.styleFormDatas.datas["polygon"].list[4].value=r[2]}this.submitSetting()}}else{var c=t.polyline[0].points.length;if(c<=1)this.$message.error(this.$t("messageTips.freeSketch.errorMsg2"));else{for(var l=[],d=0;d<c;d++)l[d]=[t.polyline[0].points[d].x,t.polyline[0].points[d].y,t.polyline[0].points[d].z];a[1].position=l,this.submitSetting()}}window.scene.mv.tools.draw.deactive(),this.offEvents()},onEscKeyUp:function(t){27==t.keyCode&&"Escape"===t.key&&this.offEvents()},offEvents:function(){var t=this;switch(window.scene.mv.tools.draw.deactive(),this.$nextTick((function(){t.$refs.linkView&&(t.$refs.linkView.$el.style.visibility="visible")})),window.scene.mv.status.selectable=!0,""!=this.currentListenerEvent&&document.removeEventListener("keyup",this.onEscKeyUp),this.currentListenerEvent){case"handleFreeSketchRectangle":window.scene.mv.tools.draw.finishDrawEvent.off("default",this.handleFreeSketchRectangle);break;case"handleFreeSketchPolygon":window.scene.mv.tools.draw.finishDrawEvent.off("default",this.handleFreeSketchPolygon);break;case"onCheckedCoordinate":this.addType||(window.scene.mv.events.singleSelection.off("default",this.onCheckedCoordinate),"model"==this.dragData.type&&this.$refs.modelObjRef.resetData());break}this.currentListenerEvent=""},changeManageSize:function(){if("skybox"!=this.dragData.type){var t=document.body.clientHeight;this.dialogHeight=100-(2*this.TopToolbarHeight+100)/(t/100)+"%"}},closeDialog:function(){var t=this.$store.state.dialog.activeDialog;t.includes("Source")&&this.$store.commit("toggleActiveDialog","Source"),this.$store.commit("toggleMenuActive","widgetSource")},handleWidgetClick:function(t,e){},submitSetting:function(){var t=this,e=this.currentSelectSource.type,a=this.styleFormDatas.datas[e].list;switch(this.$store.commit("saveDragOverData",this.currentSelectSource),e){case"model":this.addModelFeature(a,{priority:0});break;case"underlay":this.addUnderlayFeature(a);break;case"fbx":this.isVothing&&(a[0].value=this.currentSelectSource.fileSrc,this.addGltfOrFbxFeature(a,{priority:0},this.addType));break;case"gltf":this.addGltfOrFbxFeature(a,{priority:0},this.addType);break;case"panorama":this.isVothing&&(a[0].value="".concat(window.IP_CONFIG.PANO_URL,"/Panorama").concat(this.currentSelectSource.thumb,"/cover.png"),this.addPanoramaFeature(a,{priority:0}));break;case"polygon":var i={color:"rgb(255, 50, 0)",opacity:1,top:0,base:0,interval:100,direction:"x",showline:!1};"environment"===this.dragData.subType&&(i.base=a[4].value),this.addPolygonFeature(a,i,{priority:0});break;case"_3dTiles":this.isVothing&&(this.addLinkViewLoading(),this.add3DTilesFeature(a,{},{priority:0}));break}this.addType||"model"==e||(this.currentSelectSource=null,this.closeDialog()),"model"!=e&&setTimeout((function(){t.$deepUpdateScene(e),window.scene.render()}),1e3)},modelDefaultCoordinateAddFeature:function(t){if(this.currentSelectSource=t.item?t.item:t,this.styleFormDatas.longitude=0,this.styleFormDatas.latitude=0,this.styleFormDatas.altitude=0,this.styleFormDatas.rotation=[0,0,0],this.styleFormDatas.offset=[0,0,0],!t.extension||""==t.extension)return this.submitSetting(),!1;var e=JSON.parse(t.extension);if(e.origin&&(this.styleFormDatas.longitude=e.origin[1],this.styleFormDatas.latitude=e.origin[0]),e.altitude&&(this.styleFormDatas.altitude=e.altitude),e.rotation&&(this.styleFormDatas.rotation=e.rotation),e.offset){var a=1;this.styleFormDatas.longitude>0&&this.styleFormDatas.latitude>0&&(a=window.scene.mv.tools.coordinate.getBasePointScale(this.styleFormDatas.longitude,this.styleFormDatas.latitude)),this.styleFormDatas.offset=e.offset.map((function(t){return t*a}))}this.submitSetting()}},beforeDestroy:function(){this.offEvents(),window.removeEventListener("resize",this.changeManageSize)}},z=P,N=(a("cf9a"),a("0fa7"),a("066e"),Object(g["a"])(z,i,n,!1,null,"4ebef91c",null));e["default"]=N.exports},b8fd:function(t,e,a){t.exports=a.p+"static/img/塑胶-05.7c8555dd.png"},bbc9:function(t,e,a){t.exports=a.p+"static/img/thumb_gis_dark_blue.f5b58b32.png"},bde1:function(t,e,a){t.exports=a.p+"static/img/thumb_gis_black.41edc3d4.png"},bef7:function(t,e,a){t.exports=a.p+"static/img/thumb_gis_vector.234492a5.png"},c14d:function(t,e,a){t.exports=a.p+"static/img/meta-shp.0e65e0a0.png"},c74d:function(t,e,a){t.exports=a.p+"static/img/沙地-03.79e37c85.png"},cf80:function(t,e,a){t.exports=a.p+"static/img/thumb_gis_fluorescence.336aac7a.png"},cf9a:function(t,e,a){"use strict";a("70da")},d44b:function(t,e,a){t.exports=a.p+"static/img/鹅卵石-05.e42e95e3.png"},d675:function(t,e,a){t.exports=a.p+"static/img/灌木.96bb3e4b.png"},d7e9:function(t,e){t.exports="data:image/png;base64,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"},d82a:function(t,e,a){t.exports=a.p+"static/img/鹅卵石-01.c5215fcf.png"},da4e:function(t,e,a){t.exports=a.p+"static/img/野草_1.c35f12ca.png"},dc7c:function(t,e,a){t.exports=a.p+"static/img/meta-gltf.c8b3e119.png"},dec5:function(t,e,a){t.exports=a.p+"static/img/meta-wms.7a364751.png"},dec6:function(t,e,a){t.exports=a.p+"static/img/thumb_gis_white.4d2a11f2.png"},dffd:function(t,e,a){t.exports=a.p+"static/img/野草_2.21e2f50d.png"},e11d:function(t,e){t.exports="data:image/png;base64,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"},e948:function(t,e,a){t.exports=a.p+"static/img/塑胶-01.aba00ddd.png"},eba3:function(t,e,a){t.exports=a.p+"static/img/塑胶-04.dfb349a7.png"},ed11:function(t,e,a){t.exports=a.p+"static/img/地砖-09.4531fcff.png"},edf8:function(t,e,a){},ee57:function(t,e,a){var i={"./地砖-01.png":"230a","./地砖-02.png":"45da","./地砖-03.png":"0188","./地砖-04.png":"0659","./地砖-05.png":"459b","./地砖-06.png":"a7c9","./地砖-07.png":"690d","./地砖-08.png":"2797","./地砖-09.png":"ed11","./地砖-10.png":"554c","./塑胶-01.png":"e948","./塑胶-02.png":"f50d","./塑胶-03.png":"39f2","./塑胶-04.png":"eba3","./塑胶-05.png":"b8fd","./柏油路-01.png":"4f98","./柏油路-02.png":"46c0","./柏油路-03.png":"1367","./沙地-01.png":"01de","./沙地-02.png":"ab0f","./沙地-03.png":"c74d","./沙地-04.png":"0ee7","./草坪-01.png":"5cf9","./草坪-02.png":"58d9","./草坪-03.png":"9507","./草坪-04.png":"48a0","./草坪-05.png":"ff0e","./草坪-06.png":"7e32f","./鹅卵石-01.png":"d82a","./鹅卵石-02.png":"45cf","./鹅卵石-03.png":"42a9","./鹅卵石-04.png":"3992","./鹅卵石-05.png":"d44b","./鹅卵石-06.png":"4fa5","./鹅卵石-07.png":"0089"};function n(t){var e=s(t);return a(e)}function s(t){if(!a.o(i,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return i[t]}n.keys=function(){return Object.keys(i)},n.resolve=s,t.exports=n,n.id="ee57"},ee5f:function(t,e,a){t.exports=a.p+"static/img/压路机.823d2da2.png"},ef87:function(t,e,a){t.exports=a.p+"static/img/公交车.b6f5860f.png"},f50d:function(t,e,a){t.exports=a.p+"static/img/塑胶-02.f6f6da07.png"},ff0e:function(t,e,a){t.exports=a.p+"static/img/草坪-05.3b3f362b.png"}}]);