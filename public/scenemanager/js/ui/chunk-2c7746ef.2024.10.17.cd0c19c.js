(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2c7746ef"],{"0b11":function(e,t,n){"use strict";n.r(t);var o=n("b2cc"),i=n("0b54");Object(o["h"])(i["f"].ID,i["f"]),Object(o["f"])(i["e"]),Object(o["f"])(i["g"]),Object(o["f"])(i["h"]),Object(o["f"])(i["d"]),Object(o["f"])(i["a"]),Object(o["f"])(i["c"]),Object(o["g"])(new i["b"])},"0b54":function(e,t,n){"use strict";n.d(t,"f",(function(){return be})),n.d(t,"e",(function(){return we})),n.d(t,"b",(function(){return Se})),n.d(t,"g",(function(){return ke})),n.d(t,"h",(function(){return Oe})),n.d(t,"d",(function(){return Le})),n.d(t,"c",(function(){return xe})),n.d(t,"a",(function(){return Pe}));var o,i=n("41d2"),r=n("a666"),s=n("3742"),a=n("b2cc"),c=n("c7f5"),u=n("c101"),l=n("8495"),d=n("e8e3"),h=n("fdcc"),f=n("351f"),p=n("0a0f"),g=n("11f7"),m=n("5aa5"),_=n("f070"),b=n("7061"),v=n("b707"),y=n("9e56"),C=n("533b"),w=n("6dec"),S=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),k=function(){return k=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var i in t=arguments[n],t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},k.apply(this,arguments)},O=function(e,t,n,o){var i,r=arguments.length,s=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)s=Reflect.decorate(e,t,n,o);else for(var a=e.length-1;a>=0;a--)(i=e[a])&&(s=(r<3?i(s):r>3?i(t,n,s):i(t,n))||s);return r>3&&s&&Object.defineProperty(t,n,s),s},L=function(e,t){return function(n,o){t(n,o,e)}},x=function(e,t,n,o){function i(e){return e instanceof n?e:new n((function(t){t(e)}))}return new(n||(n=Promise))((function(n,r){function s(e){try{c(o.next(e))}catch(t){r(t)}}function a(e){try{c(o["throw"](e))}catch(t){r(t)}}function c(e){e.done?n(e.value):i(e.value).then(s,a)}c((o=o.apply(e,t||[])).next())}))},P=function(e,t){var n,o,i,r,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function a(e){return function(t){return c([e,t])}}function c(r){if(n)throw new TypeError("Generator is already executing.");while(s)try{if(n=1,o&&(i=2&r[0]?o["return"]:r[0]?o["throw"]||((i=o["return"])&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return s.label++,{value:r[1],done:!1};case 5:s.label++,o=r[1],r=[0];continue;case 7:r=s.ops.pop(),s.trys.pop();continue;default:if(i=s.trys,!(i=i.length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){s=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){s.label=r[1];break}if(6===r[0]&&s.label<i[1]){s.label=i[1],i=r;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(r);break}i[2]&&s.ops.pop(),s.trys.pop();continue}r=t.call(e,s)}catch(a){r=[6,a],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}},D=function(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;var o=Array(e),i=0;for(t=0;t<n;t++)for(var r=arguments[t],s=0,a=r.length;s<a;s++,i++)o[i]=r[s];return o},A=function(e){function t(t,n){var o=e.call(this,t.command?t.command.id:t.title,t.title,void 0,!t.disabled,n)||this;return o.action=t,o}return S(t,e),t}(_["a"]),N=function(e){function t(t,n,o,i){var s=e.call(this)||this;return s._editor=t,s._delegate=n,s._contextMenuService=o,s._visible=!1,s._showingActions=s._register(new r["d"]),s._keybindingResolver=new M({getKeybindings:function(){return i.getKeybindings()}}),s}return S(t,e),Object.defineProperty(t.prototype,"isVisible",{get:function(){return this._visible},enumerable:!0,configurable:!0}),t.prototype.show=function(e,t,n,o){return x(this,void 0,void 0,(function(){var i,r,s,a,c=this;return P(this,(function(u){if(i=o.includeDisabledActions?t.allActions:t.validActions,!i.length)return this._visible=!1,[2];if(!this._editor.getDomNode())throw this._visible=!1,Object(h["a"])();return this._visible=!0,this._showingActions.value=t,r=this.getMenuActions(e,i),s=b["a"].isIPosition(n)?this._toCoords(n):n||{x:0,y:0},a=this._keybindingResolver.getResolver(),this._contextMenuService.showContextMenu({getAnchor:function(){return s},getActions:function(){return r},onHide:function(){c._visible=!1,c._editor.focus()},autoSelectFirstItem:!0,getKeyBinding:function(e){return e instanceof A?a(e.action):void 0}}),[2]}))}))},t.prototype.getMenuActions=function(e,t){var n,o,i=this,r=function(e){return new A(e,(function(){return i._delegate.onSelectCodeAction(e)}))},s=t.map(r),a=this._editor.getModel();if(a&&s.length)for(var c=0,u=v["a"].all(a);c<u.length;c++){var l=u[c];if(l._getAdditionalMenuItems){var d=l._getAdditionalMenuItems({trigger:e.type,only:null===(o=null===(n=e.filter)||void 0===n?void 0:n.include)||void 0===o?void 0:o.value},t);d.length&&s.push.apply(s,D([new m["d"]],d.map((function(e){return r({title:e.title,command:e})}))))}}return s},t.prototype._toCoords=function(e){if(!this._editor.hasModel())return{x:0,y:0};this._editor.revealPosition(e,1),this._editor.render();var t=this._editor.getScrolledVisiblePosition(e),n=Object(g["C"])(this._editor.getDomNode()),o=n.left+t.left,i=n.top+t.top+t.height;return{x:o,y:i}},t=O([L(2,C["a"]),L(3,w["a"])],t),t}(r["a"]),M=function(){function e(e){this._keybindingProvider=e}return e.prototype.getResolver=function(){var t=this,n=new i["a"]((function(){return t._keybindingProvider.getKeybindings().filter((function(t){return e.codeActionCommands.indexOf(t.command)>=0})).filter((function(e){return e.resolvedKeybinding})).map((function(e){var t=e.commandArgs;return e.command===l["d"]?t={kind:y["b"].SourceOrganizeImports.value}:e.command===l["b"]&&(t={kind:y["b"].SourceFixAll.value}),k({resolvedKeybinding:e.resolvedKeybinding},y["a"].fromUser(t,{kind:y["b"].None,apply:"never"}))}))}));return function(e){if(e.kind){var o=t.bestKeybindingForCodeAction(e,n.getValue());return null===o||void 0===o?void 0:o.resolvedKeybinding}}},e.prototype.bestKeybindingForCodeAction=function(e,t){if(e.kind){var n=new y["b"](e.kind);return t.filter((function(e){return e.kind.contains(n)})).filter((function(t){return!t.preferred||e.isPreferred})).reduceRight((function(e,t){return e?e.kind.contains(t.kind)?t:e:t}),void 0)}},e.codeActionCommands=[l["e"],l["a"],l["f"],l["d"],l["b"]],e}(),I=n("00a3"),j=n("308f"),E=(n("30d5"),n("b57f")),T=n("dff7"),R=n("b7d0"),F=n("303e"),B=n("a60f"),V=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),W=function(e,t,n,o){var i,r=arguments.length,s=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)s=Reflect.decorate(e,t,n,o);else for(var a=e.length-1;a>=0;a--)(i=e[a])&&(s=(r<3?i(s):r>3?i(t,n,s):i(t,n))||s);return r>3&&s&&Object.defineProperty(t,n,s),s},U=function(e,t){return function(n,o){t(n,o,e)}};(function(e){e.Hidden={type:0};var t=function(){function e(e,t,n,o){this.actions=e,this.trigger=t,this.editorPosition=n,this.widgetPosition=o,this.type=1}return e}();e.Showing=t})(o||(o={}));var q=function(e){function t(t,n,i,r){var s=e.call(this)||this;return s._editor=t,s._quickFixActionId=n,s._preferredFixActionId=i,s._keybindingService=r,s._onClick=s._register(new j["a"]),s.onClick=s._onClick.event,s._state=o.Hidden,s._domNode=document.createElement("div"),s._domNode.className="codicon codicon-lightbulb",s._editor.addContentWidget(s),s._register(s._editor.onDidChangeModelContent((function(e){var t=s._editor.getModel();(1!==s.state.type||!t||s.state.editorPosition.lineNumber>=t.getLineCount())&&s.hide()}))),B["b"].ignoreTarget(s._domNode),s._register(g["n"](s._domNode,(function(e){if(1===s.state.type){s._editor.focus(),e.preventDefault();var t=g["C"](s._domNode),n=t.top,o=t.height,i=s._editor.getOption(49),r=Math.floor(i/3);null!==s.state.widgetPosition.position&&s.state.widgetPosition.position.lineNumber<s.state.editorPosition.lineNumber&&(r+=i),s._onClick.fire({x:e.posx,y:n+o+r,actions:s.state.actions,trigger:s.state.trigger})}}))),s._register(g["j"](s._domNode,"mouseenter",(function(e){if(1===(1&e.buttons)){s.hide();var t=new I["a"];t.startMonitoring(e.target,e.buttons,I["b"],(function(){}),(function(){t.dispose()}))}}))),s._register(s._editor.onDidChangeConfiguration((function(e){e.hasChanged(47)&&!s._editor.getOption(47).enabled&&s.hide()}))),s._updateLightBulbTitle(),s._register(s._keybindingService.onDidUpdateKeybindings(s._updateLightBulbTitle,s)),s}return V(t,e),t.prototype.dispose=function(){e.prototype.dispose.call(this),this._editor.removeContentWidget(this)},t.prototype.getId=function(){return"LightBulbWidget"},t.prototype.getDomNode=function(){return this._domNode},t.prototype.getPosition=function(){return 1===this._state.type?this._state.widgetPosition:null},t.prototype.update=function(e,n,i){var r=this;if(e.validActions.length<=0)return this.hide();var s=this._editor.getOptions();if(!s.get(47).enabled)return this.hide();var a=i.lineNumber,c=i.column,u=this._editor.getModel();if(!u)return this.hide();var l=u.getOptions().tabSize,d=s.get(34),h=u.getLineContent(a),f=E["b"].computeIndentLevel(h,l),p=d.spaceWidth*f>22,m=function(e){return e>2&&r._editor.getTopForLineNumber(e)===r._editor.getTopForLineNumber(e-1)},_=a;if(!p)if(a>1&&!m(a-1))_-=1;else if(m(a+1)){if(c*d.spaceWidth<22)return this.hide()}else _+=1;this.state=new o.Showing(e,n,i,{position:{lineNumber:_,column:1},preference:t._posPref}),g["Y"](this._domNode,"codicon-lightbulb-autofix",e.hasAutoFix),this._editor.layoutContentWidget(this)},t.prototype.hide=function(){this.state=o.Hidden,this._editor.layoutContentWidget(this)},Object.defineProperty(t.prototype,"state",{get:function(){return this._state},set:function(e){this._state=e,this._updateLightBulbTitle()},enumerable:!0,configurable:!0}),t.prototype._updateLightBulbTitle=function(){if(1===this.state.type&&this.state.actions.hasAutoFix){var e=this._keybindingService.lookupKeybinding(this._preferredFixActionId);if(e)return void(this.title=T["a"]("prefferedQuickFixWithKb","Show Fixes. Preferred Fix Available ({0})",e.getLabel()))}var t=this._keybindingService.lookupKeybinding(this._quickFixActionId);this.title=t?T["a"]("quickFixWithKb","Show Fixes ({0})",t.getLabel()):T["a"]("quickFix","Show Fixes")},Object.defineProperty(t.prototype,"title",{set:function(e){this._domNode.title=e},enumerable:!0,configurable:!0}),t._posPref=[0],t=W([U(3,w["a"])],t),t}(r["a"]);Object(R["e"])((function(e,t){var n=e.getColor(F["J"]);n&&t.addRule("\n\t\t.monaco-editor .contentWidgets .codicon-lightbulb {\n\t\t\tcolor: "+n+";\n\t\t}");var o=e.getColor(F["I"]);o&&t.addRule("\n\t\t.monaco-editor .contentWidgets .codicon-lightbulb-autofix {\n\t\t\tcolor: "+o+";\n\t\t}")}));var H,z=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),K=function(e,t,n,o){var i,r=arguments.length,s=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)s=Reflect.decorate(e,t,n,o);else for(var a=e.length-1;a>=0;a--)(i=e[a])&&(s=(r<3?i(s):r>3?i(t,n,s):i(t,n))||s);return r>3&&s&&Object.defineProperty(t,n,s),s},Z=function(e,t){return function(n,o){t(n,o,e)}},G=function(e,t,n,o){function i(e){return e instanceof n?e:new n((function(t){t(e)}))}return new(n||(n=Promise))((function(n,r){function s(e){try{c(o.next(e))}catch(t){r(t)}}function a(e){try{c(o["throw"](e))}catch(t){r(t)}}function c(e){e.done?n(e.value):i(e.value).then(s,a)}c((o=o.apply(e,t||[])).next())}))},Y=function(e,t){var n,o,i,r,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function a(e){return function(t){return c([e,t])}}function c(r){if(n)throw new TypeError("Generator is already executing.");while(s)try{if(n=1,o&&(i=2&r[0]?o["return"]:r[0]?o["throw"]||((i=o["return"])&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return s.label++,{value:r[1],done:!1};case 5:s.label++,o=r[1],r=[0];continue;case 7:r=s.ops.pop(),s.trys.pop();continue;default:if(i=s.trys,!(i=i.length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){s=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){s.label=r[1];break}if(6===r[0]&&s.label<i[1]){s.label=i[1],i=r;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(r);break}i[2]&&s.ops.pop(),s.trys.pop();continue}r=t.call(e,s)}catch(a){r=[6,a],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}},Q=function(e){function t(t,n,o,s,a){var c=e.call(this)||this;return c._editor=t,c.delegate=s,c._activeCodeActions=c._register(new r["d"]),c._codeActionWidget=new i["a"]((function(){return c._register(a.createInstance(N,c._editor,{onSelectCodeAction:function(e){return G(c,void 0,void 0,(function(){return Y(this,(function(t){return this.delegate.applyCodeAction(e,!0),[2]}))}))}}))})),c._lightBulbWidget=new i["a"]((function(){var e=c._register(a.createInstance(q,c._editor,n,o));return c._register(e.onClick((function(e){return c.showCodeActionList(e.trigger,e.actions,e,{includeDisabledActions:!1})}))),e})),c}return z(t,e),t.prototype.update=function(e){var t,n,o;return G(this,void 0,void 0,(function(){var i,r,s,a,c;return Y(this,(function(u){switch(u.label){case 0:if(1!==e.type)return null===(t=this._lightBulbWidget.rawValue)||void 0===t||t.hide(),[2];u.label=1;case 1:return u.trys.push([1,3,,4]),[4,e.actions];case 2:return i=u.sent(),[3,4];case 3:return r=u.sent(),Object(h["e"])(r),[2];case 4:if(this._lightBulbWidget.getValue().update(i,e.trigger,e.position),2!==e.trigger.type)return[3,11];if(!(null===(n=e.trigger.filter)||void 0===n?void 0:n.include))return[3,10];if(s=this.tryGetValidActionToApply(e.trigger,i),!s)return[3,9];u.label=5;case 5:return u.trys.push([5,,7,8]),[4,this.delegate.applyCodeAction(s,!1)];case 6:return u.sent(),[3,8];case 7:return i.dispose(),[7];case 8:return[2];case 9:if(e.trigger.context&&(a=this.getInvalidActionThatWouldHaveBeenApplied(e.trigger,i),a&&a.disabled))return f["a"].get(this._editor).showMessage(a.disabled,e.trigger.context.position),i.dispose(),[2];u.label=10;case 10:return c=!!(null===(o=e.trigger.filter)||void 0===o?void 0:o.include),!e.trigger.context||i.allActions.length&&(c||i.validActions.length)?(this._activeCodeActions.value=i,this._codeActionWidget.getValue().show(e.trigger,i,e.position,{includeDisabledActions:c}),[3,12]):(f["a"].get(this._editor).showMessage(e.trigger.context.notAvailableMessage,e.trigger.context.position),this._activeCodeActions.value=i,i.dispose(),[2]);case 11:this._codeActionWidget.getValue().isVisible?i.dispose():this._activeCodeActions.value=i,u.label=12;case 12:return[2]}}))}))},t.prototype.getInvalidActionThatWouldHaveBeenApplied=function(e,t){if(t.allActions.length)return"first"===e.autoApply&&0===t.validActions.length||"ifSingle"===e.autoApply&&1===t.allActions.length?Object(d["h"])(t.allActions,(function(e){return e.disabled})):void 0},t.prototype.tryGetValidActionToApply=function(e,t){if(t.validActions.length)return"first"===e.autoApply&&t.validActions.length>0||"ifSingle"===e.autoApply&&1===t.validActions.length?t.validActions[0]:void 0},t.prototype.showCodeActionList=function(e,t,n,o){return G(this,void 0,void 0,(function(){return Y(this,(function(i){return this._codeActionWidget.getValue().show(e,t,n,o),[2]}))}))},t=K([Z(4,p["a"])],t),t}(r["a"]),J=n("9e74"),X=n("4fc3"),$=n("b400"),ee=n("b0cd"),te=n("b539"),ne=n("5d75"),oe=n("5fe7"),ie=n("6a89"),re=n("82c9"),se=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),ae=new X["d"]("supportedCodeAction",""),ce=function(e){function t(t,n,o,i){void 0===i&&(i=250);var r=e.call(this)||this;return r._editor=t,r._markerService=n,r._signalChange=o,r._delay=i,r._autoTriggerTimer=r._register(new oe["e"]),r._register(r._markerService.onMarkerChanged((function(e){return r._onMarkerChanges(e)}))),r._register(r._editor.onDidChangeCursorPosition((function(){return r._onCursorChange()}))),r}return se(t,e),t.prototype.trigger=function(e){var t=this._getRangeOfSelectionUnlessWhitespaceEnclosed(e);return this._createEventAndSignalChange(e,t)},t.prototype._onMarkerChanges=function(e){var t=this,n=this._editor.getModel();n&&e.some((function(e){return Object(re["e"])(e,n.uri)}))&&this._autoTriggerTimer.cancelAndSet((function(){t.trigger({type:1})}),this._delay)},t.prototype._onCursorChange=function(){var e=this;this._autoTriggerTimer.cancelAndSet((function(){e.trigger({type:1})}),this._delay)},t.prototype._getRangeOfMarker=function(e){var t=this._editor.getModel();if(t)for(var n=0,o=this._markerService.read({resource:t.uri});n<o.length;n++){var i=o[n],r=t.validateRange(i);if(ie["a"].intersectRanges(r,e))return ie["a"].lift(r)}},t.prototype._getRangeOfSelectionUnlessWhitespaceEnclosed=function(e){if(this._editor.hasModel()){var t=this._editor.getModel(),n=this._editor.getSelection();if(n.isEmpty()&&1===e.type){var o=n.getPosition(),i=o.lineNumber,r=o.column,s=t.getLineContent(i);if(0===s.length)return;if(1===r){if(/\s/.test(s[0]))return}else if(r===t.getLineMaxColumn(i)){if(/\s/.test(s[s.length-1]))return}else if(/\s/.test(s[r-2])&&/\s/.test(s[r-1]))return}return n}},t.prototype._createEventAndSignalChange=function(e,t){var n=this._editor.getModel();if(t&&n){var o=this._getRangeOfMarker(t),i=o?o.getStartPosition():t.getStartPosition(),r={trigger:e,selection:t,position:i};return this._signalChange(r),r}this._signalChange(void 0)},t}(r["a"]);(function(e){e.Empty={type:0};var t=function(){function e(e,t,n,o){this.trigger=e,this.rangeOrSelection=t,this.position=n,this.actions=o,this.type=1}return e}();e.Triggered=t})(H||(H={}));var ue=function(e){function t(t,n,o,i){var s=e.call(this)||this;return s._editor=t,s._markerService=n,s._progressService=i,s._codeActionOracle=s._register(new r["d"]),s._state=H.Empty,s._onDidChangeState=s._register(new j["a"]),s.onDidChangeState=s._onDidChangeState.event,s._supportedCodeActions=ae.bindTo(o),s._register(s._editor.onDidChangeModel((function(){return s._update()}))),s._register(s._editor.onDidChangeModelLanguage((function(){return s._update()}))),s._register(v["a"].onDidChange((function(){return s._update()}))),s._update(),s}return se(t,e),t.prototype.dispose=function(){e.prototype.dispose.call(this),this.setState(H.Empty,!0)},t.prototype._update=function(){var e=this;this._codeActionOracle.value=void 0,this.setState(H.Empty);var t=this._editor.getModel();if(t&&v["a"].has(t)&&!this._editor.getOption(68)){for(var n=[],o=0,i=v["a"].all(t);o<i.length;o++){var r=i[o];Array.isArray(r.providedCodeActionKinds)&&n.push.apply(n,r.providedCodeActionKinds)}this._supportedCodeActions.set(n.join(" ")),this._codeActionOracle.value=new ce(this._editor,this._markerService,(function(n){if(n){var o=Object(oe["f"])((function(e){return Object(l["c"])(t,n.selection,n.trigger,e)}));e._progressService&&2===n.trigger.type&&e._progressService.showWhile(o,250),e.setState(new H.Triggered(n.trigger,n.selection,n.position,o))}else e.setState(H.Empty)}),void 0),this._codeActionOracle.value.trigger({type:1})}else this._supportedCodeActions.reset()},t.prototype.trigger=function(e){this._codeActionOracle.value&&this._codeActionOracle.value.trigger(e)},t.prototype.setState=function(e,t){e!==this._state&&(1===this._state.type&&this._state.actions.cancel(),this._state=e,t||this._onDidChangeState.fire(e))},t}(r["a"]),le=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),de=function(e,t,n,o){var i,r=arguments.length,s=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)s=Reflect.decorate(e,t,n,o);else for(var a=e.length-1;a>=0;a--)(i=e[a])&&(s=(r<3?i(s):r>3?i(t,n,s):i(t,n))||s);return r>3&&s&&Object.defineProperty(t,n,s),s},he=function(e,t){return function(n,o){t(n,o,e)}},fe=function(e,t,n,o){function i(e){return e instanceof n?e:new n((function(t){t(e)}))}return new(n||(n=Promise))((function(n,r){function s(e){try{c(o.next(e))}catch(t){r(t)}}function a(e){try{c(o["throw"](e))}catch(t){r(t)}}function c(e){e.done?n(e.value):i(e.value).then(s,a)}c((o=o.apply(e,t||[])).next())}))},pe=function(e,t){var n,o,i,r,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function a(e){return function(t){return c([e,t])}}function c(r){if(n)throw new TypeError("Generator is already executing.");while(s)try{if(n=1,o&&(i=2&r[0]?o["return"]:r[0]?o["throw"]||((i=o["return"])&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return s.label++,{value:r[1],done:!1};case 5:s.label++,o=r[1],r=[0];continue;case 7:r=s.ops.pop(),s.trys.pop();continue;default:if(i=s.trys,!(i=i.length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){s=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){s.label=r[1];break}if(6===r[0]&&s.label<i[1]){s.label=i[1],i=r;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(r);break}i[2]&&s.ops.pop(),s.trys.pop();continue}r=t.call(e,s)}catch(a){r=[6,a],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}},ge=function(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;var o=Array(e),i=0;for(t=0;t<n;t++)for(var r=arguments[t],s=0,a=r.length;s<a;s++,i++)o[i]=r[s];return o};function me(e){return X["a"].regex(ae.keys()[0],new RegExp("(\\s|^)"+Object(s["p"])(e.value)+"\\b"))}var _e={type:"object",required:["kind"],defaultSnippets:[{body:{kind:""}}],properties:{kind:{type:"string",description:T["a"]("args.schema.kind","Kind of the code action to run.")},apply:{type:"string",description:T["a"]("args.schema.apply","Controls when the returned actions are applied."),default:"ifSingle",enum:["first","ifSingle","never"],enumDescriptions:[T["a"]("args.schema.apply.first","Always apply the first returned code action."),T["a"]("args.schema.apply.ifSingle","Apply the first returned code action if it is the only one."),T["a"]("args.schema.apply.never","Do not apply the returned code actions.")]},preferred:{type:"boolean",default:!1,description:T["a"]("args.schema.preferred","Controls if only preferred code actions should be returned.")}}},be=function(e){function t(t,n,o,r,s){var a=e.call(this)||this;return a._instantiationService=s,a._editor=t,a._model=a._register(new ue(a._editor,n,o,r)),a._register(a._model.onDidChangeState((function(e){return a.update(e)}))),a._ui=new i["a"]((function(){return a._register(new Q(t,we.Id,Pe.Id,{applyCodeAction:function(e,t){return fe(a,void 0,void 0,(function(){return pe(this,(function(n){switch(n.label){case 0:return n.trys.push([0,,2,3]),[4,this._applyCodeAction(e)];case 1:return n.sent(),[3,3];case 2:return t&&this._trigger({type:1,filter:{}}),[7];case 3:return[2]}}))}))}},a._instantiationService))})),a}return le(t,e),t.get=function(e){return e.getContribution(t.ID)},t.prototype.update=function(e){this._ui.getValue().update(e)},t.prototype.showCodeActions=function(e,t,n){return this._ui.getValue().showCodeActionList(e,t,n,{includeDisabledActions:!1})},t.prototype.manualTriggerAtCurrentPosition=function(e,t,n){if(this._editor.hasModel()){f["a"].get(this._editor).closeMessage();var o=this._editor.getPosition();this._trigger({type:2,filter:t,autoApply:n,context:{notAvailableMessage:e,position:o}})}},t.prototype._trigger=function(e){return this._model.trigger(e)},t.prototype._applyCodeAction=function(e){return this._instantiationService.invokeFunction(ve,e,this._editor)},t.ID="editor.contrib.quickFixController",t=de([he(1,$["b"]),he(2,X["c"]),he(3,te["a"]),he(4,p["a"])],t),t}(r["a"]);function ve(e,t,n){return fe(this,void 0,void 0,(function(){var o,i,r,s,a,u;return pe(this,(function(l){switch(l.label){case 0:return o=e.get(c["a"]),i=e.get(J["b"]),r=e.get(ne["a"]),s=e.get(ee["a"]),r.publicLog2("codeAction.applyCodeAction",{codeActionTitle:t.title,codeActionKind:t.kind,codeActionIsPreferred:!!t.isPreferred}),t.edit?[4,o.apply(t.edit,{editor:n})]:[3,2];case 1:l.sent(),l.label=2;case 2:if(!t.command)return[3,6];l.label=3;case 3:return l.trys.push([3,5,,6]),[4,i.executeCommand.apply(i,ge([t.command.id],t.command.arguments||[]))];case 4:return l.sent(),[3,6];case 5:return a=l.sent(),u=ye(a),s.error("string"===typeof u?u:T["a"]("applyCodeActionFailed","An unknown error occurred while applying the code action")),[3,6];case 6:return[2]}}))}))}function ye(e){return"string"===typeof e?e:e instanceof Error&&"string"===typeof e.message?e.message:void 0}function Ce(e,t,n,o){if(e.hasModel()){var i=be.get(e);i&&i.manualTriggerAtCurrentPosition(t,n,o)}}var we=function(e){function t(){return e.call(this,{id:t.Id,label:T["a"]("quickfix.trigger.label","Quick Fix..."),alias:"Quick Fix...",precondition:X["a"].and(u["a"].writable,u["a"].hasCodeActionsProvider),kbOpts:{kbExpr:u["a"].editorTextFocus,primary:2132,weight:100}})||this}return le(t,e),t.prototype.run=function(e,t){return Ce(t,T["a"]("editor.action.quickFix.noneMessage","No code actions available"),void 0,void 0)},t.Id="editor.action.quickFix",t}(a["b"]),Se=function(e){function t(){return e.call(this,{id:l["a"],precondition:X["a"].and(u["a"].writable,u["a"].hasCodeActionsProvider),description:{description:"Trigger a code action",args:[{name:"args",schema:_e}]}})||this}return le(t,e),t.prototype.runEditorCommand=function(e,t,n){var o=y["a"].fromUser(n,{kind:y["b"].Empty,apply:"ifSingle"});return Ce(t,"string"===typeof(null===n||void 0===n?void 0:n.kind)?o.preferred?T["a"]("editor.action.codeAction.noneMessage.preferred.kind","No preferred code actions for '{0}' available",n.kind):T["a"]("editor.action.codeAction.noneMessage.kind","No code actions for '{0}' available",n.kind):o.preferred?T["a"]("editor.action.codeAction.noneMessage.preferred","No preferred code actions available"):T["a"]("editor.action.codeAction.noneMessage","No code actions available"),{include:o.kind,includeSourceActions:!0,onlyIncludePreferredActions:o.preferred},o.apply)},t}(a["c"]),ke=function(e){function t(){return e.call(this,{id:l["e"],label:T["a"]("refactor.label","Refactor..."),alias:"Refactor...",precondition:X["a"].and(u["a"].writable,u["a"].hasCodeActionsProvider),kbOpts:{kbExpr:u["a"].editorTextFocus,primary:3120,mac:{primary:1328},weight:100},contextMenuOpts:{group:"1_modification",order:2,when:X["a"].and(u["a"].writable,me(y["b"].Refactor))},description:{description:"Refactor...",args:[{name:"args",schema:_e}]}})||this}return le(t,e),t.prototype.run=function(e,t,n){var o=y["a"].fromUser(n,{kind:y["b"].Refactor,apply:"never"});return Ce(t,"string"===typeof(null===n||void 0===n?void 0:n.kind)?o.preferred?T["a"]("editor.action.refactor.noneMessage.preferred.kind","No preferred refactorings for '{0}' available",n.kind):T["a"]("editor.action.refactor.noneMessage.kind","No refactorings for '{0}' available",n.kind):o.preferred?T["a"]("editor.action.refactor.noneMessage.preferred","No preferred refactorings available"):T["a"]("editor.action.refactor.noneMessage","No refactorings available"),{include:y["b"].Refactor.contains(o.kind)?o.kind:y["b"].None,onlyIncludePreferredActions:o.preferred},o.apply)},t}(a["b"]),Oe=function(e){function t(){return e.call(this,{id:l["f"],label:T["a"]("source.label","Source Action..."),alias:"Source Action...",precondition:X["a"].and(u["a"].writable,u["a"].hasCodeActionsProvider),contextMenuOpts:{group:"1_modification",order:2.1,when:X["a"].and(u["a"].writable,me(y["b"].Source))},description:{description:"Source Action...",args:[{name:"args",schema:_e}]}})||this}return le(t,e),t.prototype.run=function(e,t,n){var o=y["a"].fromUser(n,{kind:y["b"].Source,apply:"never"});return Ce(t,"string"===typeof(null===n||void 0===n?void 0:n.kind)?o.preferred?T["a"]("editor.action.source.noneMessage.preferred.kind","No preferred source actions for '{0}' available",n.kind):T["a"]("editor.action.source.noneMessage.kind","No source actions for '{0}' available",n.kind):o.preferred?T["a"]("editor.action.source.noneMessage.preferred","No preferred source actions available"):T["a"]("editor.action.source.noneMessage","No source actions available"),{include:y["b"].Source.contains(o.kind)?o.kind:y["b"].None,includeSourceActions:!0,onlyIncludePreferredActions:o.preferred},o.apply)},t}(a["b"]),Le=function(e){function t(){return e.call(this,{id:l["d"],label:T["a"]("organizeImports.label","Organize Imports"),alias:"Organize Imports",precondition:X["a"].and(u["a"].writable,me(y["b"].SourceOrganizeImports)),kbOpts:{kbExpr:u["a"].editorTextFocus,primary:1581,weight:100}})||this}return le(t,e),t.prototype.run=function(e,t){return Ce(t,T["a"]("editor.action.organize.noneMessage","No organize imports action available"),{include:y["b"].SourceOrganizeImports,includeSourceActions:!0},"ifSingle")},t}(a["b"]),xe=function(e){function t(){return e.call(this,{id:l["b"],label:T["a"]("fixAll.label","Fix All"),alias:"Fix All",precondition:X["a"].and(u["a"].writable,me(y["b"].SourceFixAll))})||this}return le(t,e),t.prototype.run=function(e,t){return Ce(t,T["a"]("fixAll.noneMessage","No fix all action available"),{include:y["b"].SourceFixAll,includeSourceActions:!0},"ifSingle")},t}(a["b"]),Pe=function(e){function t(){return e.call(this,{id:t.Id,label:T["a"]("autoFix.label","Auto Fix..."),alias:"Auto Fix...",precondition:X["a"].and(u["a"].writable,me(y["b"].QuickFix)),kbOpts:{kbExpr:u["a"].editorTextFocus,primary:1620,mac:{primary:2644},weight:100}})||this}return le(t,e),t.prototype.run=function(e,t){return Ce(t,T["a"]("editor.action.autoFix.noneMessage","No auto fixes available"),{include:y["b"].QuickFix,onlyIncludePreferredActions:!0},"ifSingle")},t.Id="editor.action.autoFix",t}(a["b"])},"10f4":function(e,t,n){},2935:function(e,t,n){"use strict";n.r(t);var o=n("dff7"),i=n("b2cc"),r=n("c101"),s=n("6a89"),a=function(){function e(e,t){this._selection=e,this._isMovingLeft=t,this._cutStartIndex=-1,this._cutEndIndex=-1,this._moved=!1,this._selectionId=null}return e.prototype.getEditOperations=function(e,t){var n=this._selection;if(this._selectionId=t.trackSelection(n),n.startLineNumber===n.endLineNumber&&(!this._isMovingLeft||0!==n.startColumn)&&(this._isMovingLeft||n.endColumn!==e.getLineMaxColumn(n.startLineNumber))){var o,i,r,a=n.selectionStartLineNumber,c=e.getLineContent(a);this._isMovingLeft?(o=c.substring(0,n.startColumn-2),i=c.substring(n.startColumn-1,n.endColumn-1),r=c.substring(n.startColumn-2,n.startColumn-1)+c.substring(n.endColumn-1)):(o=c.substring(0,n.startColumn-1)+c.substring(n.endColumn-1,n.endColumn),i=c.substring(n.startColumn-1,n.endColumn-1),r=c.substring(n.endColumn));var u=o+i+r;t.addEditOperation(new s["a"](a,1,a,e.getLineMaxColumn(a)),null),t.addEditOperation(new s["a"](a,1,a,1),u),this._cutStartIndex=n.startColumn+(this._isMovingLeft?-1:1),this._cutEndIndex=this._cutStartIndex+n.endColumn-n.startColumn,this._moved=!0}},e.prototype.computeCursorState=function(e,t){var n=t.getTrackedSelection(this._selectionId);return this._moved&&(n=n.setStartPosition(n.startLineNumber,this._cutStartIndex),n=n.setEndPosition(n.startLineNumber,this._cutEndIndex)),n},e}(),c=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),u=function(e){function t(t,n){var o=e.call(this,n)||this;return o.left=t,o}return c(t,e),t.prototype.run=function(e,t){if(t.hasModel()){for(var n=[],o=t.getSelections(),i=0,r=o;i<r.length;i++){var s=r[i];n.push(new a(s,this.left))}t.pushUndoStop(),t.executeCommands(this.id,n),t.pushUndoStop()}},t}(i["b"]),l=function(e){function t(){return e.call(this,!0,{id:"editor.action.moveCarretLeftAction",label:o["a"]("caret.moveLeft","Move Caret Left"),alias:"Move Caret Left",precondition:r["a"].writable})||this}return c(t,e),t}(u),d=function(e){function t(){return e.call(this,!1,{id:"editor.action.moveCarretRightAction",label:o["a"]("caret.moveRight","Move Caret Right"),alias:"Move Caret Right",precondition:r["a"].writable})||this}return c(t,e),t}(u);Object(i["f"])(l),Object(i["f"])(d)},"30d5":function(e,t,n){},"44c7":function(e,t,n){},"58db":function(e,t,n){"use strict";n.d(t,"a",(function(){return y}));n("10f4");var o=n("0f70"),i=n("11f7"),r=n("00a3"),s=n("1b7d"),a=n("ceb8"),c=n("308f"),u=n("a666"),l=n("303e"),d=n("b7d0"),h=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),f=i["a"],p=function(e){function t(t,n,o){var r=e.call(this)||this;r.model=n,r.domNode=f(".colorpicker-header"),i["q"](t,r.domNode),r.pickedColorNode=i["q"](r.domNode,f(".picked-color"));var s=i["q"](r.domNode,f(".original-color"));return s.style.backgroundColor=a["a"].Format.CSS.format(r.model.originalColor)||"",r.backgroundColor=o.getTheme().getColor(l["A"])||a["a"].white,r._register(Object(d["e"])((function(e,t){r.backgroundColor=e.getColor(l["A"])||a["a"].white}))),r._register(i["j"](r.pickedColorNode,i["d"].CLICK,(function(){return r.model.selectNextColorPresentation()}))),r._register(i["j"](s,i["d"].CLICK,(function(){r.model.color=r.model.originalColor,r.model.flushColor()}))),r._register(n.onDidChangeColor(r.onDidChangeColor,r)),r._register(n.onDidChangePresentation(r.onDidChangePresentation,r)),r.pickedColorNode.style.backgroundColor=a["a"].Format.CSS.format(n.color)||"",i["Y"](r.pickedColorNode,"light",n.color.rgba.a<.5?r.backgroundColor.isLighter():n.color.isLighter()),r}return h(t,e),t.prototype.onDidChangeColor=function(e){this.pickedColorNode.style.backgroundColor=a["a"].Format.CSS.format(e)||"",i["Y"](this.pickedColorNode,"light",e.rgba.a<.5?this.backgroundColor.isLighter():e.isLighter()),this.onDidChangePresentation()},t.prototype.onDidChangePresentation=function(){this.pickedColorNode.textContent=this.model.presentation?this.model.presentation.label:""},t}(u["a"]),g=function(e){function t(t,n,o){var r=e.call(this)||this;return r.model=n,r.pixelRatio=o,r.domNode=f(".colorpicker-body"),i["q"](t,r.domNode),r.saturationBox=new m(r.domNode,r.model,r.pixelRatio),r._register(r.saturationBox),r._register(r.saturationBox.onDidChange(r.onDidSaturationValueChange,r)),r._register(r.saturationBox.onColorFlushed(r.flushColor,r)),r.opacityStrip=new b(r.domNode,r.model),r._register(r.opacityStrip),r._register(r.opacityStrip.onDidChange(r.onDidOpacityChange,r)),r._register(r.opacityStrip.onColorFlushed(r.flushColor,r)),r.hueStrip=new v(r.domNode,r.model),r._register(r.hueStrip),r._register(r.hueStrip.onDidChange(r.onDidHueChange,r)),r._register(r.hueStrip.onColorFlushed(r.flushColor,r)),r}return h(t,e),t.prototype.flushColor=function(){this.model.flushColor()},t.prototype.onDidSaturationValueChange=function(e){var t=e.s,n=e.v,o=this.model.color.hsva;this.model.color=new a["a"](new a["b"](o.h,t,n,o.a))},t.prototype.onDidOpacityChange=function(e){var t=this.model.color.hsva;this.model.color=new a["a"](new a["b"](t.h,t.s,t.v,e))},t.prototype.onDidHueChange=function(e){var t=this.model.color.hsva,n=360*(1-e);this.model.color=new a["a"](new a["b"](360===n?0:n,t.s,t.v,t.a))},t.prototype.layout=function(){this.saturationBox.layout(),this.opacityStrip.layout(),this.hueStrip.layout()},t}(u["a"]),m=function(e){function t(t,n,o){var r=e.call(this)||this;return r.model=n,r.pixelRatio=o,r._onDidChange=new c["a"],r.onDidChange=r._onDidChange.event,r._onColorFlushed=new c["a"],r.onColorFlushed=r._onColorFlushed.event,r.domNode=f(".saturation-wrap"),i["q"](t,r.domNode),r.canvas=document.createElement("canvas"),r.canvas.className="saturation-box",i["q"](r.domNode,r.canvas),r.selection=f(".saturation-selection"),i["q"](r.domNode,r.selection),r.layout(),r._register(i["h"](r.domNode,(function(e){return r.onMouseDown(e)}))),r._register(r.model.onDidChangeColor(r.onDidChangeColor,r)),r.monitor=null,r}return h(t,e),t.prototype.onMouseDown=function(e){var t=this;this.monitor=this._register(new r["a"]);var n=i["C"](this.domNode);e.target!==this.selection&&this.onDidChangePosition(e.offsetX,e.offsetY),this.monitor.startMonitoring(e.target,e.buttons,r["b"],(function(e){return t.onDidChangePosition(e.posx-n.left,e.posy-n.top)}),(function(){return null}));var o=i["i"](document,(function(){t._onColorFlushed.fire(),o.dispose(),t.monitor&&(t.monitor.stopMonitoring(!0),t.monitor=null)}),!0)},t.prototype.onDidChangePosition=function(e,t){var n=Math.max(0,Math.min(1,e/this.width)),o=Math.max(0,Math.min(1,1-t/this.height));this.paintSelection(n,o),this._onDidChange.fire({s:n,v:o})},t.prototype.layout=function(){this.width=this.domNode.offsetWidth,this.height=this.domNode.offsetHeight,this.canvas.width=this.width*this.pixelRatio,this.canvas.height=this.height*this.pixelRatio,this.paint();var e=this.model.color.hsva;this.paintSelection(e.s,e.v)},t.prototype.paint=function(){var e=this.model.color.hsva,t=new a["a"](new a["b"](e.h,1,1,1)),n=this.canvas.getContext("2d"),o=n.createLinearGradient(0,0,this.canvas.width,0);o.addColorStop(0,"rgba(255, 255, 255, 1)"),o.addColorStop(.5,"rgba(255, 255, 255, 0.5)"),o.addColorStop(1,"rgba(255, 255, 255, 0)");var i=n.createLinearGradient(0,0,0,this.canvas.height);i.addColorStop(0,"rgba(0, 0, 0, 0)"),i.addColorStop(1,"rgba(0, 0, 0, 1)"),n.rect(0,0,this.canvas.width,this.canvas.height),n.fillStyle=a["a"].Format.CSS.format(t),n.fill(),n.fillStyle=o,n.fill(),n.fillStyle=i,n.fill()},t.prototype.paintSelection=function(e,t){this.selection.style.left=e*this.width+"px",this.selection.style.top=this.height-t*this.height+"px"},t.prototype.onDidChangeColor=function(){this.monitor&&this.monitor.isMonitoring()||this.paint()},t}(u["a"]),_=function(e){function t(t,n){var o=e.call(this)||this;return o.model=n,o._onDidChange=new c["a"],o.onDidChange=o._onDidChange.event,o._onColorFlushed=new c["a"],o.onColorFlushed=o._onColorFlushed.event,o.domNode=i["q"](t,f(".strip")),o.overlay=i["q"](o.domNode,f(".overlay")),o.slider=i["q"](o.domNode,f(".slider")),o.slider.style.top="0px",o._register(i["h"](o.domNode,(function(e){return o.onMouseDown(e)}))),o.layout(),o}return h(t,e),t.prototype.layout=function(){this.height=this.domNode.offsetHeight-this.slider.offsetHeight;var e=this.getValue(this.model.color);this.updateSliderPosition(e)},t.prototype.onMouseDown=function(e){var t=this,n=this._register(new r["a"]),o=i["C"](this.domNode);i["f"](this.domNode,"grabbing"),e.target!==this.slider&&this.onDidChangeTop(e.offsetY),n.startMonitoring(e.target,e.buttons,r["b"],(function(e){return t.onDidChangeTop(e.posy-o.top)}),(function(){return null}));var s=i["i"](document,(function(){t._onColorFlushed.fire(),s.dispose(),n.stopMonitoring(!0),i["P"](t.domNode,"grabbing")}),!0)},t.prototype.onDidChangeTop=function(e){var t=Math.max(0,Math.min(1,1-e/this.height));this.updateSliderPosition(t),this._onDidChange.fire(t)},t.prototype.updateSliderPosition=function(e){this.slider.style.top=(1-e)*this.height+"px"},t}(u["a"]),b=function(e){function t(t,n){var o=e.call(this,t,n)||this;return i["f"](o.domNode,"opacity-strip"),o._register(n.onDidChangeColor(o.onDidChangeColor,o)),o.onDidChangeColor(o.model.color),o}return h(t,e),t.prototype.onDidChangeColor=function(e){var t=e.rgba,n=t.r,o=t.g,i=t.b,r=new a["a"](new a["c"](n,o,i,1)),s=new a["a"](new a["c"](n,o,i,0));this.overlay.style.background="linear-gradient(to bottom, "+r+" 0%, "+s+" 100%)"},t.prototype.getValue=function(e){return e.hsva.a},t}(_),v=function(e){function t(t,n){var o=e.call(this,t,n)||this;return i["f"](o.domNode,"hue-strip"),o}return h(t,e),t.prototype.getValue=function(e){return 1-e.hsva.h/360},t}(_),y=function(e){function t(t,n,i,r){var s=e.call(this)||this;s.model=n,s.pixelRatio=i,s._register(Object(o["o"])((function(){return s.layout()})));var a=f(".colorpicker-widget");t.appendChild(a);var c=new p(a,s.model,r);return s.body=new g(a,s.model,s.pixelRatio),s._register(c),s._register(s.body),s}return h(t,e),t.prototype.layout=function(){this.body.layout()},t}(s["a"])},6483:function(e,t,n){"use strict";n.d(t,"b",(function(){return l})),n.d(t,"a",(function(){return d}));var o=n("2504"),i=n("fdcc"),r=n("6d8e"),s=n("b2cc"),a=n("6a89"),c=n("b707"),u=n("1b69");function l(e,t){var n=[],o=c["c"].ordered(e).reverse(),i=o.map((function(o){return Promise.resolve(o.provideDocumentColors(e,t)).then((function(e){if(Array.isArray(e))for(var t=0,i=e;t<i.length;t++){var r=i[t];n.push({colorInfo:r,provider:o})}}))}));return Promise.all(i).then((function(){return n}))}function d(e,t,n,o){return Promise.resolve(n.provideColorPresentations(e,t,o))}Object(s["j"])("_executeDocumentColorProvider",(function(e,t){var n=t.resource;if(!(n instanceof r["a"]))throw Object(i["b"])();var s=e.get(u["a"]).getModel(n);if(!s)throw Object(i["b"])();var a=[],l=c["c"].ordered(s).reverse(),d=l.map((function(e){return Promise.resolve(e.provideDocumentColors(s,o["a"].None)).then((function(e){if(Array.isArray(e))for(var t=0,n=e;t<n.length;t++){var o=n[t];a.push({range:o.range,color:[o.color.red,o.color.green,o.color.blue,o.color.alpha]})}}))}));return Promise.all(d).then((function(){return a}))})),Object(s["j"])("_executeColorPresentationProvider",(function(e,t){var n=t.resource,s=t.color,l=t.range;if(!(n instanceof r["a"])||!Array.isArray(s)||4!==s.length||!a["a"].isIRange(l))throw Object(i["b"])();var d=s[0],h=s[1],f=s[2],p=s[3],g=e.get(u["a"]).getModel(n);if(!g)throw Object(i["b"])();var m={range:l,color:{red:d,green:h,blue:f,alpha:p}},_=[],b=c["c"].ordered(g).reverse(),v=b.map((function(e){return Promise.resolve(e.provideColorPresentations(g,m,o["a"].None)).then((function(e){Array.isArray(e)&&_.push.apply(_,e)}))}));return Promise.all(v).then((function(){return _}))}))},"6daf":function(e,t,n){"use strict";n.r(t);var o=n("dff7"),i=n("b2cc"),r=n("2c29"),s=n("6a89"),a=n("c101"),c=n("f85a"),u=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),l=function(e){function t(){return e.call(this,{id:"editor.action.transposeLetters",label:o["a"]("transposeLetters.label","Transpose Letters"),alias:"Transpose Letters",precondition:a["a"].writable,kbOpts:{kbExpr:a["a"].textInputFocus,primary:0,mac:{primary:306},weight:100}})||this}return u(t,e),t.prototype.run=function(e,t){if(t.hasModel()){for(var n=t.getModel(),o=[],i=t.getSelections(),a=0,u=i;a<u.length;a++){var l=u[a];if(l.isEmpty()){var d=l.startLineNumber,h=l.startColumn,f=n.getLineMaxColumn(d);if(1!==d||1!==h&&(2!==h||2!==f)){var p=h===f?l.getPosition():c["a"].rightPosition(n,l.getPosition().lineNumber,l.getPosition().column),g=c["a"].leftPosition(n,p.lineNumber,p.column),m=c["a"].leftPosition(n,g.lineNumber,g.column),_=n.getValueInRange(s["a"].fromPositions(m,g)),b=n.getValueInRange(s["a"].fromPositions(g,p)),v=s["a"].fromPositions(m,p);o.push(new r["a"](v,b+_))}}}o.length>0&&(t.pushUndoStop(),t.executeCommands(this.id,o),t.pushUndoStop())}},t}(i["b"]);Object(i["f"])(l)},"6e4e":function(e,t,n){"use strict";n.r(t),n.d(t,"BracketMatchingController",(function(){return w}));n("f004");var o=n("dff7"),i=n("5fe7"),r=n("a666"),s=n("b2cc"),a=n("7061"),c=n("6a89"),u=n("8025"),l=n("c101"),d=n("3352"),h=n("b57f"),f=n("918c"),p=n("303e"),g=n("b7d0"),m=n("7e32"),_=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),b=Object(p["Tb"])("editorOverviewRuler.bracketMatchForeground",{dark:"#A0A0A0",light:"#A0A0A0",hc:"#A0A0A0"},o["a"]("overviewRulerBracketMatchForeground","Overview ruler marker color for matching brackets.")),v=function(e){function t(){return e.call(this,{id:"editor.action.jumpToBracket",label:o["a"]("smartSelect.jumpBracket","Go to Bracket"),alias:"Go to Bracket",precondition:void 0,kbOpts:{kbExpr:l["a"].editorTextFocus,primary:3160,weight:100}})||this}return _(t,e),t.prototype.run=function(e,t){var n=w.get(t);n&&n.jumpToBracket()},t}(s["b"]),y=function(e){function t(){return e.call(this,{id:"editor.action.selectToBracket",label:o["a"]("smartSelect.selectToBracket","Select to Bracket"),alias:"Select to Bracket",precondition:void 0,description:{description:"Select to Bracket",args:[{name:"args",schema:{type:"object",properties:{selectBrackets:{type:"boolean",default:!0}}}}]}})||this}return _(t,e),t.prototype.run=function(e,t,n){var o=w.get(t);if(o){var i=!0;n&&!1===n.selectBrackets&&(i=!1),o.selectToBracket(i)}},t}(s["b"]),C=function(){function e(e,t,n){this.position=e,this.brackets=t,this.options=n}return e}(),w=function(e){function t(t){var n=e.call(this)||this;return n._editor=t,n._lastBracketsData=[],n._lastVersionId=0,n._decorations=[],n._updateBracketsSoon=n._register(new i["d"]((function(){return n._updateBrackets()}),50)),n._matchBrackets=n._editor.getOption(53),n._updateBracketsSoon.schedule(),n._register(t.onDidChangeCursorPosition((function(e){"never"!==n._matchBrackets&&n._updateBracketsSoon.schedule()}))),n._register(t.onDidChangeModelContent((function(e){n._updateBracketsSoon.schedule()}))),n._register(t.onDidChangeModel((function(e){n._lastBracketsData=[],n._decorations=[],n._updateBracketsSoon.schedule()}))),n._register(t.onDidChangeModelLanguageConfiguration((function(e){n._lastBracketsData=[],n._updateBracketsSoon.schedule()}))),n._register(t.onDidChangeConfiguration((function(e){e.hasChanged(53)&&(n._matchBrackets=n._editor.getOption(53),n._decorations=n._editor.deltaDecorations(n._decorations,[]),n._lastBracketsData=[],n._lastVersionId=0,n._updateBracketsSoon.schedule())}))),n}return _(t,e),t.get=function(e){return e.getContribution(t.ID)},t.prototype.jumpToBracket=function(){if(this._editor.hasModel()){var e=this._editor.getModel(),t=this._editor.getSelections().map((function(t){var n=t.getStartPosition(),o=e.matchBracket(n),i=null;if(o)o[0].containsPosition(n)?i=o[1].getStartPosition():o[1].containsPosition(n)&&(i=o[0].getStartPosition());else{var r=e.findEnclosingBrackets(n);if(r)i=r[0].getStartPosition();else{var s=e.findNextBracket(n);s&&s.range&&(i=s.range.getStartPosition())}}return i?new u["a"](i.lineNumber,i.column,i.lineNumber,i.column):new u["a"](n.lineNumber,n.column,n.lineNumber,n.column)}));this._editor.setSelections(t),this._editor.revealRange(t[0])}},t.prototype.selectToBracket=function(e){if(this._editor.hasModel()){var t=this._editor.getModel(),n=[];this._editor.getSelections().forEach((function(o){var i=o.getStartPosition(),r=t.matchBracket(i);if(!r&&(r=t.findEnclosingBrackets(i),!r)){var s=t.findNextBracket(i);s&&s.range&&(r=t.matchBracket(s.range.getStartPosition()))}var a=null,l=null;if(r){r.sort(c["a"].compareRangesUsingStarts);var d=r[0],h=r[1];a=e?d.getStartPosition():d.getEndPosition(),l=e?h.getEndPosition():h.getStartPosition()}a&&l&&n.push(new u["a"](a.lineNumber,a.column,l.lineNumber,l.column))})),n.length>0&&(this._editor.setSelections(n),this._editor.revealRange(n[0]))}},t.prototype._updateBrackets=function(){if("never"!==this._matchBrackets){this._recomputeBrackets();for(var e=[],t=0,n=0,o=this._lastBracketsData;n<o.length;n++){var i=o[n],r=i.brackets;r&&(e[t++]={range:r[0],options:i.options},e[t++]={range:r[1],options:i.options})}this._decorations=this._editor.deltaDecorations(this._decorations,e)}},t.prototype._recomputeBrackets=function(){if(!this._editor.hasModel())return this._lastBracketsData=[],void(this._lastVersionId=0);var e=this._editor.getSelections();if(e.length>100)return this._lastBracketsData=[],void(this._lastVersionId=0);var n=this._editor.getModel(),o=n.getVersionId(),i=[];this._lastVersionId===o&&(i=this._lastBracketsData);for(var r=[],s=0,c=0,u=e.length;c<u;c++){var l=e[c];l.isEmpty()&&(r[s++]=l.getStartPosition())}r.length>1&&r.sort(a["a"].compare);var d=[],h=0,f=0,p=i.length;for(c=0,u=r.length;c<u;c++){var g=r[c];while(f<p&&i[f].position.isBefore(g))f++;if(f<p&&i[f].position.equals(g))d[h++]=i[f];else{var m=n.matchBracket(g),_=t._DECORATION_OPTIONS_WITH_OVERVIEW_RULER;m||"always"!==this._matchBrackets||(m=n.findEnclosingBrackets(g,20),_=t._DECORATION_OPTIONS_WITHOUT_OVERVIEW_RULER),d[h++]=new C(g,m,_)}}this._lastBracketsData=d,this._lastVersionId=o},t.ID="editor.contrib.bracketMatchingController",t._DECORATION_OPTIONS_WITH_OVERVIEW_RULER=h["a"].register({stickiness:1,className:"bracket-match",overviewRuler:{color:Object(g["f"])(b),position:d["d"].Center}}),t._DECORATION_OPTIONS_WITHOUT_OVERVIEW_RULER=h["a"].register({stickiness:1,className:"bracket-match"}),t}(r["a"]);Object(s["h"])(w.ID,w),Object(s["f"])(y),Object(s["f"])(v),Object(g["e"])((function(e,t){var n=e.getColor(f["c"]);n&&t.addRule(".monaco-editor .bracket-match { background-color: "+n+"; }");var o=e.getColor(f["d"]);o&&t.addRule(".monaco-editor .bracket-match { border: 1px solid "+o+"; }")})),m["c"].appendMenuItem(19,{group:"5_infile_nav",command:{id:"editor.action.jumpToBracket",title:o["a"]({key:"miGoToBracket",comment:["&& denotes a mnemonic"]},"Go to &&Bracket")},order:2})},"77a4":function(e,t,n){"use strict";n.r(t),n.d(t,"CodeLensContribution",(function(){return q}));var o=n("5fe7"),i=n("fdcc"),r=n("a666"),s=n("bc04"),a=n("b2cc"),c=n("b707"),u=n("e8e3"),l=n("2504"),d=n("6d8e"),h=n("1b69"),f=function(){function e(){this.lenses=[],this._disposables=new r["b"]}return e.prototype.dispose=function(){this._disposables.dispose()},e.prototype.add=function(e,t){this._disposables.add(e);for(var n=0,o=e.lenses;n<o.length;n++){var i=o[n];this.lenses.push({symbol:i,provider:t})}},e}();function p(e,t){var n=c["b"].ordered(e),o=new Map,r=new f,s=n.map((function(n,s){return o.set(n,s),Promise.resolve(n.provideCodeLenses(e,t)).then((function(e){return e&&r.add(e,n)})).catch(i["f"])}));return Promise.all(s).then((function(){return r.lenses=Object(u["r"])(r.lenses,(function(e,t){return e.symbol.range.startLineNumber<t.symbol.range.startLineNumber?-1:e.symbol.range.startLineNumber>t.symbol.range.startLineNumber?1:o.get(e.provider)<o.get(t.provider)?-1:o.get(e.provider)>o.get(t.provider)?1:e.symbol.range.startColumn<t.symbol.range.startColumn?-1:e.symbol.range.startColumn>t.symbol.range.startColumn?1:0})),r}))}Object(a["j"])("_executeCodeLensProvider",(function(e,t){var n=t.resource,o=t.itemResolveCount;if(!(n instanceof d["a"]))throw Object(i["b"])();var s=e.get(h["a"]).getModel(n);if(!s)throw Object(i["b"])();var a=[],c=new r["b"];return p(s,l["a"].None).then((function(e){c.add(e);for(var t=[],n=function(e){"undefined"===typeof o||Boolean(e.symbol.command)?a.push(e.symbol):o-- >0&&e.provider.resolveCodeLens&&t.push(Promise.resolve(e.provider.resolveCodeLens(s,e.symbol,l["a"].None)).then((function(t){return a.push(t||e.symbol)})))},i=0,r=e.lenses;i<r.length;i++){var u=r[i];n(u)}return Promise.all(t)})).then((function(){return a})).finally((function(){setTimeout((function(){return c.dispose()}),100)}))}));n("44c7");var g=n("11f7"),m=n("561a"),_=n("3742"),b=n("6a89"),v=n("b57f"),y=n("918c"),C=n("303e"),w=n("b7d0"),S=function(){function e(e,t){this.afterLineNumber=e,this._onHeight=t,this.heightInLines=1,this.suppressMouseDown=!0,this.domNode=document.createElement("div")}return e.prototype.onComputedHeight=function(e){void 0===this._lastHeight?this._lastHeight=e:this._lastHeight!==e&&(this._lastHeight=e,this._onHeight())},e}(),k=function(){function e(t,n,o){this.allowEditorOverflow=!1,this.suppressMouseDown=!0,this._commands=new Map,this._isEmpty=!0,this._editor=t,this._id="codelens.widget-"+e._idPool++,this.updatePosition(o),this._domNode=document.createElement("span"),this._domNode.className="codelens-decoration "+n}return e.prototype.withCommands=function(e,t){this._commands.clear();for(var n="",o=!1,i=0;i<e.length;i++){var r=e[i];if(r&&(o=!0,r.command)){var s=Object(m["c"])(Object(_["o"])(r.command.title));r.command.id?(n+="<a id="+i+">"+s+"</a>",this._commands.set(String(i),r.command)):n+="<span>"+s+"</span>",i+1<e.length&&(n+="<span>&#160;|&#160;</span>")}}o?(n||(n="&#160;"),this._domNode.innerHTML=n,this._isEmpty&&t&&g["f"](this._domNode,"fadein"),this._isEmpty=!1):this._domNode.innerHTML="<span>no commands</span>"},e.prototype.getCommand=function(e){return e.parentElement===this._domNode?this._commands.get(e.id):void 0},e.prototype.getId=function(){return this._id},e.prototype.getDomNode=function(){return this._domNode},e.prototype.updatePosition=function(e){var t=this._editor.getModel().getLineFirstNonWhitespaceColumn(e);this._widgetPosition={position:{lineNumber:e,column:t},preference:[1]}},e.prototype.getPosition=function(){return this._widgetPosition||null},e._idPool=0,e}(),O=function(){function e(){this._removeDecorations=[],this._addDecorations=[],this._addDecorationsCallbacks=[]}return e.prototype.addDecoration=function(e,t){this._addDecorations.push(e),this._addDecorationsCallbacks.push(t)},e.prototype.removeDecoration=function(e){this._removeDecorations.push(e)},e.prototype.commit=function(e){for(var t=e.deltaDecorations(this._removeDecorations,this._addDecorations),n=0,o=t.length;n<o;n++)this._addDecorationsCallbacks[n](t[n])},e}(),L=function(){function e(e,t,n,o,i,r){var s,a=this;this._isDisposed=!1,this._editor=t,this._className=n,this._data=e,this._decorationIds=[];var c=[];this._data.forEach((function(e,t){e.symbol.command&&c.push(e.symbol),o.addDecoration({range:e.symbol.range,options:v["a"].EMPTY},(function(e){return a._decorationIds[t]=e})),s=s?b["a"].plusRange(s,e.symbol.range):b["a"].lift(e.symbol.range)})),this._viewZone=new S(s.startLineNumber-1,r),this._viewZoneId=i.addZone(this._viewZone),c.length>0&&(this._createContentWidgetIfNecessary(),this._contentWidget.withCommands(c,!1))}return e.prototype._createContentWidgetIfNecessary=function(){this._contentWidget||(this._contentWidget=new k(this._editor,this._className,this._viewZone.afterLineNumber+1),this._editor.addContentWidget(this._contentWidget))},e.prototype.dispose=function(e,t){this._decorationIds.forEach(e.removeDecoration,e),this._decorationIds=[],t&&t.removeZone(this._viewZoneId),this._contentWidget&&(this._editor.removeContentWidget(this._contentWidget),this._contentWidget=void 0),this._isDisposed=!0},e.prototype.isDisposed=function(){return this._isDisposed},e.prototype.isValid=function(){var e=this;return this._decorationIds.some((function(t,n){var o=e._editor.getModel().getDecorationRange(t),i=e._data[n].symbol;return!(!o||b["a"].isEmpty(i.range)!==o.isEmpty())}))},e.prototype.updateCodeLensSymbols=function(e,t){var n=this;this._decorationIds.forEach(t.removeDecoration,t),this._decorationIds=[],this._data=e,this._data.forEach((function(e,o){t.addDecoration({range:e.symbol.range,options:v["a"].EMPTY},(function(e){return n._decorationIds[o]=e}))}))},e.prototype.computeIfNecessary=function(e){if(!this._viewZone.domNode.hasAttribute("monaco-visible-view-zone"))return null;for(var t=0;t<this._decorationIds.length;t++){var n=e.getDecorationRange(this._decorationIds[t]);n&&(this._data[t].symbol.range=n)}return this._data},e.prototype.updateCommands=function(e){this._createContentWidgetIfNecessary(),this._contentWidget.withCommands(e,!0);for(var t=0;t<this._data.length;t++){var n=e[t];if(n){var o=this._data[t].symbol;o.command=n.command||o.command}}},e.prototype.getCommand=function(e){var t;return null===(t=this._contentWidget)||void 0===t?void 0:t.getCommand(e)},e.prototype.getLineNumber=function(){var e=this._editor.getModel().getDecorationRange(this._decorationIds[0]);return e?e.startLineNumber:-1},e.prototype.update=function(e){if(this.isValid()){var t=this._editor.getModel().getDecorationRange(this._decorationIds[0]);t&&(this._viewZone.afterLineNumber=t.startLineNumber-1,e.layoutZone(this._viewZoneId),this._contentWidget&&(this._contentWidget.updatePosition(t.startLineNumber),this._editor.layoutContentWidget(this._contentWidget)))}},e}();Object(w["e"])((function(e,t){var n=e.getColor(y["e"]);n&&(t.addRule(".monaco-editor .codelens-decoration { color: "+n+"; }"),t.addRule(".monaco-editor .codelens-decoration .codicon { color: "+n+"; }"));var o=e.getColor(C["n"]);o&&(t.addRule(".monaco-editor .codelens-decoration > a:hover { color: "+o+" !important; }"),t.addRule(".monaco-editor .codelens-decoration > a:hover .codicon { color: "+o+" !important; }"))}));var x=n("9e74"),P=n("b0cd"),D=n("0a0f"),A=n("f5f3"),N=n("4035"),M=n("03e8"),I=n("0bfb"),j=function(e,t,n,o){var i,r=arguments.length,s=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)s=Reflect.decorate(e,t,n,o);else for(var a=e.length-1;a>=0;a--)(i=e[a])&&(s=(r<3?i(s):r>3?i(t,n,s):i(t,n))||s);return r>3&&s&&Object.defineProperty(t,n,s),s},E=function(e,t){return function(n,o){t(n,o,e)}},T=Object(D["c"])("ICodeLensCache"),R=function(){function e(e,t){this.lineCount=e,this.data=t}return e}(),F=function(){function e(e){var t=this;this._fakeProvider=new(function(){function e(){}return e.prototype.provideCodeLenses=function(){throw new Error("not supported")},e}()),this._cache=new N["a"](20,.75);var n="codelens/cache";Object(o["k"])((function(){return e.remove(n,1)}));var i="codelens/cache2",r=e.get(i,1,"{}");this._deserialize(r),Object(I["a"])(e.onWillSaveState)((function(n){n.reason===M["c"].SHUTDOWN&&e.store(i,t._serialize(),1)}))}return e.prototype.put=function(e,t){var n=t.lenses.map((function(e){var t;return{range:e.symbol.range,command:e.symbol.command&&{id:"",title:null===(t=e.symbol.command)||void 0===t?void 0:t.title}}})),o=new f;o.add({lenses:n,dispose:function(){}},this._fakeProvider);var i=new R(e.getLineCount(),o);this._cache.set(e.uri.toString(),i)},e.prototype.get=function(e){var t=this._cache.get(e.uri.toString());return t&&t.lineCount===e.getLineCount()?t.data:void 0},e.prototype.delete=function(e){this._cache.delete(e.uri.toString())},e.prototype._serialize=function(){var e=Object.create(null);return this._cache.forEach((function(t,n){for(var o=new Set,i=0,r=t.data.lenses;i<r.length;i++){var s=r[i];o.add(s.symbol.range.startLineNumber)}e[n]={lineCount:t.lineCount,lines:Object(N["e"])(o)}})),JSON.stringify(e)},e.prototype._deserialize=function(e){try{var t=JSON.parse(e);for(var n in t){for(var o=t[n],i=[],r=0,s=o.lines;r<s.length;r++){var a=s[r];i.push({range:new b["a"](a,1,a,11)})}var c=new f;c.add({lenses:i,dispose:function(){}},this._fakeProvider),this._cache.set(n,new R(o.lineCount,c))}}catch(u){}},e=j([E(0,M["a"])],e),e}();Object(A["b"])(T,F);var B=n("eda7"),V=function(e,t,n,o){var i,r=arguments.length,s=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)s=Reflect.decorate(e,t,n,o);else for(var a=e.length-1;a>=0;a--)(i=e[a])&&(s=(r<3?i(s):r>3?i(t,n,s):i(t,n))||s);return r>3&&s&&Object.defineProperty(t,n,s),s},W=function(e,t){return function(n,o){t(n,o,e)}},U=function(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;var o=Array(e),i=0;for(t=0;t<n;t++)for(var r=arguments[t],s=0,a=r.length;s<a;s++,i++)o[i]=r[s];return o},q=function(){function e(e,t,n,o){var i=this;this._editor=e,this._commandService=t,this._notificationService=n,this._codeLensCache=o,this._globalToDispose=new r["b"],this._localToDispose=new r["b"],this._lenses=[],this._oldCodeLensModels=new r["b"],this._modelChangeCounter=0,this._isEnabled=this._editor.getOption(11),this._globalToDispose.add(this._editor.onDidChangeModel((function(){return i._onModelChange()}))),this._globalToDispose.add(this._editor.onDidChangeModelLanguage((function(){return i._onModelChange()}))),this._globalToDispose.add(this._editor.onDidChangeConfiguration((function(){var e=i._isEnabled;i._isEnabled=i._editor.getOption(11),e!==i._isEnabled&&i._onModelChange()}))),this._globalToDispose.add(c["b"].onDidChange(this._onModelChange,this)),this._globalToDispose.add(this._editor.onDidChangeConfiguration((function(e){e.hasChanged(34)&&i._updateLensStyle()}))),this._onModelChange(),this._styleClassName=Object(B["a"])(this._editor.getId()).toString(16),this._styleElement=g["w"](g["N"](this._editor.getContainerDomNode())?this._editor.getContainerDomNode():void 0),this._updateLensStyle()}return e.prototype.dispose=function(){this._localDispose(),this._globalToDispose.dispose(),this._oldCodeLensModels.dispose(),Object(r["f"])(this._currentCodeLensModel)},e.prototype._updateLensStyle=function(){var e=this._editor.getOptions(),t=e.get(34),n=e.get(49),o=Math.round(1.1*n),i=Math.round(.9*t.fontSize),r="\n\t\t.monaco-editor .codelens-decoration."+this._styleClassName+" { height: "+o+"px; line-height: "+n+"px; font-size: "+i+"px; padding-right: "+Math.round(.45*t.fontSize)+"px;}\n\t\t.monaco-editor .codelens-decoration."+this._styleClassName+" > a > .codicon { line-height: "+n+"px; font-size: "+i+"px; }\n\t\t";this._styleElement.innerHTML=r},e.prototype._localDispose=function(){this._currentFindCodeLensSymbolsPromise&&(this._currentFindCodeLensSymbolsPromise.cancel(),this._currentFindCodeLensSymbolsPromise=void 0,this._modelChangeCounter++),this._currentResolveCodeLensSymbolsPromise&&(this._currentResolveCodeLensSymbolsPromise.cancel(),this._currentResolveCodeLensSymbolsPromise=void 0),this._localToDispose.clear(),this._oldCodeLensModels.clear(),Object(r["f"])(this._currentCodeLensModel)},e.prototype._onModelChange=function(){var e=this;this._localDispose();var t=this._editor.getModel();if(t&&this._isEnabled){var n=this._codeLensCache.get(t);if(n&&this._renderCodeLensSymbols(n),c["b"].has(t)){for(var a=0,u=c["b"].all(t);a<u.length;a++){var l=u[a];if("function"===typeof l.onDidChange){var d=l.onDidChange((function(){return f.schedule()}));this._localToDispose.add(d)}}var h=this._detectVisibleLenses=new o["d"]((function(){return e._onViewportChanged()}),250),f=new o["d"]((function(){var n=++e._modelChangeCounter;e._currentFindCodeLensSymbolsPromise&&e._currentFindCodeLensSymbolsPromise.cancel(),e._currentFindCodeLensSymbolsPromise=Object(o["f"])((function(e){return p(t,e)})),e._currentFindCodeLensSymbolsPromise.then((function(o){n===e._modelChangeCounter&&(e._currentCodeLensModel&&e._oldCodeLensModels.add(e._currentCodeLensModel),e._currentCodeLensModel=o,e._codeLensCache.put(t,o),e._renderCodeLensSymbols(o),h.schedule())}),i["e"])}),250);this._localToDispose.add(f),this._localToDispose.add(h),this._localToDispose.add(this._editor.onDidChangeModelContent((function(){e._editor.changeDecorations((function(t){e._editor.changeViewZones((function(n){var o=[],i=-1;e._lenses.forEach((function(e){e.isValid()&&i!==e.getLineNumber()?(e.update(n),i=e.getLineNumber()):o.push(e)}));var r=new O;o.forEach((function(t){t.dispose(r,n),e._lenses.splice(e._lenses.indexOf(t),1)})),r.commit(t)}))})),h.schedule(),f.schedule()}))),this._localToDispose.add(this._editor.onDidScrollChange((function(t){t.scrollTopChanged&&e._lenses.length>0&&h.schedule()}))),this._localToDispose.add(this._editor.onDidLayoutChange((function(){h.schedule()}))),this._localToDispose.add(Object(r["h"])((function(){if(e._editor.getModel()){var t=s["c"].capture(e._editor);e._editor.changeDecorations((function(t){e._editor.changeViewZones((function(n){e._disposeAllLenses(t,n)}))})),t.restore(e._editor)}else e._disposeAllLenses(void 0,void 0)}))),this._localToDispose.add(this._editor.onMouseUp((function(t){var n;if(9===t.target.type){var o=t.target.element;if("SPAN"===(null===o||void 0===o?void 0:o.tagName)&&(o=o.parentElement),"A"===(null===o||void 0===o?void 0:o.tagName))for(var i=0,r=e._lenses;i<r.length;i++){var s=r[i],a=s.getCommand(o);if(a){(n=e._commandService).executeCommand.apply(n,U([a.id],a.arguments||[])).catch((function(t){return e._notificationService.error(t)}));break}}}}))),f.schedule()}else n&&this._localToDispose.add(Object(o["g"])((function(){var o=e._codeLensCache.get(t);n===o&&(e._codeLensCache.delete(t),e._onModelChange())}),3e4))}},e.prototype._disposeAllLenses=function(e,t){for(var n=new O,o=0,i=this._lenses;o<i.length;o++){var r=i[o];r.dispose(n,t)}e&&n.commit(e),this._lenses=[]},e.prototype._renderCodeLensSymbols=function(e){var t=this;if(this._editor.hasModel()){for(var n,o=this._editor.getModel().getLineCount(),i=[],r=0,a=e.lenses;r<a.length;r++){var c=a[r],u=c.symbol.range.startLineNumber;u<1||u>o||(n&&n[n.length-1].symbol.range.startLineNumber===u?n.push(c):(n=[c],i.push(n)))}var l=s["c"].capture(this._editor);this._editor.changeDecorations((function(e){t._editor.changeViewZones((function(n){var o=new O,r=0,s=0;while(s<i.length&&r<t._lenses.length){var a=i[s][0].symbol.range.startLineNumber,c=t._lenses[r].getLineNumber();c<a?(t._lenses[r].dispose(o,n),t._lenses.splice(r,1)):c===a?(t._lenses[r].updateCodeLensSymbols(i[s],o),s++,r++):(t._lenses.splice(r,0,new L(i[s],t._editor,t._styleClassName,o,n,(function(){return t._detectVisibleLenses&&t._detectVisibleLenses.schedule()}))),r++,s++)}while(r<t._lenses.length)t._lenses[r].dispose(o,n),t._lenses.splice(r,1);while(s<i.length)t._lenses.push(new L(i[s],t._editor,t._styleClassName,o,n,(function(){return t._detectVisibleLenses&&t._detectVisibleLenses.schedule()}))),s++;o.commit(e)}))})),l.restore(this._editor)}},e.prototype._onViewportChanged=function(){var e=this;this._currentResolveCodeLensSymbolsPromise&&(this._currentResolveCodeLensSymbolsPromise.cancel(),this._currentResolveCodeLensSymbolsPromise=void 0);var t=this._editor.getModel();if(t){var n=[],r=[];if(this._lenses.forEach((function(e){var o=e.computeIfNecessary(t);o&&(n.push(o),r.push(e))})),0!==n.length){var s=Object(o["f"])((function(e){var o=n.map((function(n,o){var s=new Array(n.length),a=n.map((function(n,o){return n.symbol.command||"function"!==typeof n.provider.resolveCodeLens?(s[o]=n.symbol,Promise.resolve(void 0)):Promise.resolve(n.provider.resolveCodeLens(t,n.symbol,e)).then((function(e){s[o]=e}),i["f"])}));return Promise.all(a).then((function(){e.isCancellationRequested||r[o].isDisposed()||r[o].updateCommands(s)}))}));return Promise.all(o)}));this._currentResolveCodeLensSymbolsPromise=s,this._currentResolveCodeLensSymbolsPromise.then((function(){e._currentCodeLensModel&&e._codeLensCache.put(t,e._currentCodeLensModel),e._oldCodeLensModels.clear(),s===e._currentResolveCodeLensSymbolsPromise&&(e._currentResolveCodeLensSymbolsPromise=void 0)}),(function(t){Object(i["e"])(t),s===e._currentResolveCodeLensSymbolsPromise&&(e._currentResolveCodeLensSymbolsPromise=void 0)}))}}},e.ID="css.editor.codeLens",e=V([W(1,x["b"]),W(2,P["a"]),W(3,T)],e),e}();Object(a["h"])(q.ID,q)},"7c3e":function(e,t,n){"use strict";n.r(t),n.d(t,"ContextMenuController",(function(){return _}));var o=n("dff7"),i=n("11f7"),r=n("5aa5"),s=n("a666"),a=n("b2cc"),c=n("c101"),u=n("7e32"),l=n("4fc3"),d=n("533b"),h=n("6dec"),f=n("9cfc"),p=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),g=function(e,t,n,o){var i,r=arguments.length,s=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)s=Reflect.decorate(e,t,n,o);else for(var a=e.length-1;a>=0;a--)(i=e[a])&&(s=(r<3?i(s):r>3?i(t,n,s):i(t,n))||s);return r>3&&s&&Object.defineProperty(t,n,s),s},m=function(e,t){return function(n,o){t(n,o,e)}},_=function(){function e(e,t,n,o,i,r){var a=this;this._contextMenuService=t,this._contextViewService=n,this._contextKeyService=o,this._keybindingService=i,this._menuService=r,this._toDispose=new s["b"],this._contextMenuIsBeingShownCount=0,this._editor=e,this._toDispose.add(this._editor.onContextMenu((function(e){return a._onContextMenu(e)}))),this._toDispose.add(this._editor.onMouseWheel((function(e){a._contextMenuIsBeingShownCount>0&&a._contextViewService.hideContextView()}))),this._toDispose.add(this._editor.onKeyDown((function(e){58===e.keyCode&&(e.preventDefault(),e.stopPropagation(),a.showContextMenu())})))}return e.get=function(t){return t.getContribution(e.ID)},e.prototype._onContextMenu=function(e){if(this._editor.hasModel()){if(!this._editor.getOption(14))return this._editor.focus(),void(e.target.position&&!this._editor.getSelection().containsPosition(e.target.position)&&this._editor.setPosition(e.target.position));if(12!==e.target.type&&(e.event.preventDefault(),6===e.target.type||7===e.target.type||1===e.target.type)){if(this._editor.focus(),e.target.position){for(var t=!1,n=0,o=this._editor.getSelections();n<o.length;n++){var i=o[n];if(i.containsPosition(e.target.position)){t=!0;break}}t||this._editor.setPosition(e.target.position)}var r=null;1!==e.target.type&&(r={x:e.event.posx-1,width:2,y:e.event.posy-1,height:2}),this.showContextMenu(r)}}},e.prototype.showContextMenu=function(e){if(this._editor.getOption(14)&&this._editor.hasModel())if(this._contextMenuService){var t=this._getMenuActions(this._editor.getModel(),7);t.length>0&&this._doShowContextMenu(t,e)}else this._editor.focus()},e.prototype._getMenuActions=function(e,t){var n=[],o=this._menuService.createMenu(t,this._contextKeyService),i=o.getActions({arg:e.uri});o.dispose();for(var s=0,a=i;s<a.length;s++){for(var c=a[s],l=c[1],d=0,h=0,p=l;h<p.length;h++){var g=p[h];if(g instanceof u["d"]){var m=this._getMenuActions(e,g.item.submenu);m.length>0&&(n.push(new f["a"](g.label,m)),d++)}else n.push(g),d++}d&&n.push(new r["d"])}return n.length&&n.pop(),n},e.prototype._doShowContextMenu=function(e,t){var n=this;if(void 0===t&&(t=null),this._editor.hasModel()){var o=this._editor.getOption(44);if(this._editor.updateOptions({hover:{enabled:!1}}),!t){this._editor.revealPosition(this._editor.getPosition(),1),this._editor.render();var s=this._editor.getScrolledVisiblePosition(this._editor.getPosition()),a=i["C"](this._editor.getDomNode()),c=a.left+s.left,u=a.top+s.top+s.height;t={x:c,y:u}}this._contextMenuIsBeingShownCount++,this._contextMenuService.showContextMenu({getAnchor:function(){return t},getActions:function(){return e},getActionViewItem:function(e){var t=n._keybindingFor(e);if(t)return new r["b"](e,e,{label:!0,keybinding:t.getLabel(),isMenu:!0});var o=e;return"function"===typeof o.getActionViewItem?o.getActionViewItem():new r["b"](e,e,{icon:!0,label:!0,isMenu:!0})},getKeyBinding:function(e){return n._keybindingFor(e)},onHide:function(e){n._contextMenuIsBeingShownCount--,n._editor.focus(),n._editor.updateOptions({hover:o})}})}},e.prototype._keybindingFor=function(e){return this._keybindingService.lookupKeybinding(e.id)},e.prototype.dispose=function(){this._contextMenuIsBeingShownCount>0&&this._contextViewService.hideContextView(),this._toDispose.dispose()},e.ID="editor.contrib.contextmenu",e=g([m(1,d["a"]),m(2,d["b"]),m(3,l["c"]),m(4,h["a"]),m(5,u["a"])],e),e}(),b=function(e){function t(){return e.call(this,{id:"editor.action.showContextMenu",label:o["a"]("action.showContextMenu.label","Show Editor Context Menu"),alias:"Show Editor Context Menu",precondition:void 0,kbOpts:{kbExpr:c["a"].textInputFocus,primary:1092,weight:100}})||this}return p(t,e),t.prototype.run=function(e,t){var n=_.get(t);n.showContextMenu()},t}(a["b"]);Object(a["h"])(_.ID,_),Object(a["f"])(b)},8495:function(e,t,n){"use strict";n.d(t,"a",(function(){return v})),n.d(t,"e",(function(){return y})),n.d(t,"f",(function(){return C})),n.d(t,"d",(function(){return w})),n.d(t,"b",(function(){return S})),n.d(t,"c",(function(){return O}));var o=n("e8e3"),i=n("2504"),r=n("fdcc"),s=n("a666"),a=n("6d8e"),c=n("bc04"),u=n("b2cc"),l=n("6a89"),d=n("8025"),h=n("b707"),f=n("1b69"),p=n("9e56"),g=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),m=function(e,t,n,o){function i(e){return e instanceof n?e:new n((function(t){t(e)}))}return new(n||(n=Promise))((function(n,r){function s(e){try{c(o.next(e))}catch(t){r(t)}}function a(e){try{c(o["throw"](e))}catch(t){r(t)}}function c(e){e.done?n(e.value):i(e.value).then(s,a)}c((o=o.apply(e,t||[])).next())}))},_=function(e,t){var n,o,i,r,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function a(e){return function(t){return c([e,t])}}function c(r){if(n)throw new TypeError("Generator is already executing.");while(s)try{if(n=1,o&&(i=2&r[0]?o["return"]:r[0]?o["throw"]||((i=o["return"])&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return s.label++,{value:r[1],done:!1};case 5:s.label++,o=r[1],r=[0];continue;case 7:r=s.ops.pop(),s.trys.pop();continue;default:if(i=s.trys,!(i=i.length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){s=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){s.label=r[1];break}if(6===r[0]&&s.label<i[1]){s.label=i[1],i=r;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(r);break}i[2]&&s.ops.pop(),s.trys.pop();continue}r=t.call(e,s)}catch(a){r=[6,a],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}},b=function(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;var o=Array(e),i=0;for(t=0;t<n;t++)for(var r=arguments[t],s=0,a=r.length;s<a;s++,i++)o[i]=r[s];return o},v="editor.action.codeAction",y="editor.action.refactor",C="editor.action.sourceAction",w="editor.action.organizeImports",S="editor.action.fixAll",k=function(e){function t(n,i){var r=e.call(this)||this;return r._register(i),r.allActions=Object(o["r"])(b(n),t.codeActionsComparator),r.validActions=r.allActions.filter((function(e){return!e.disabled})),r}return g(t,e),t.codeActionsComparator=function(e,t){return Object(o["q"])(e.diagnostics)?Object(o["q"])(t.diagnostics)?e.diagnostics[0].message.localeCompare(t.diagnostics[0].message):-1:Object(o["q"])(t.diagnostics)?1:0},Object.defineProperty(t.prototype,"hasAutoFix",{get:function(){return this.validActions.some((function(e){return!!e.kind&&p["b"].QuickFix.contains(new p["b"](e.kind))&&!!e.isPreferred}))},enumerable:!0,configurable:!0}),t}(s["a"]);function O(e,t,n,i){var a,u=this,l=n.filter||{},d={only:null===(a=l.include)||void 0===a?void 0:a.value,trigger:n.type},f=new c["d"](e,i),g=L(e,l),b=new s["b"],v=g.map((function(n){return m(u,void 0,void 0,(function(){var o,i;return _(this,(function(s){switch(s.label){case 0:return s.trys.push([0,2,,3]),[4,n.provideCodeActions(e,t,d,f.token)];case 1:return o=s.sent(),f.token.isCancellationRequested||!o?[2,[]]:(b.add(o),[2,o.actions.filter((function(e){return e&&Object(p["c"])(l,e)}))]);case 2:if(i=s.sent(),Object(r["d"])(i))throw i;return Object(r["f"])(i),[2,[]];case 3:return[2]}}))}))})),y=h["a"].onDidChange((function(){var t=h["a"].all(e);Object(o["g"])(t,g)||f.cancel()}));return Promise.all(v).then(o["m"]).then((function(e){return new k(e,b)})).finally((function(){y.dispose(),f.dispose()}))}function L(e,t){return h["a"].all(e).filter((function(e){return!e.providedCodeActionKinds||e.providedCodeActionKinds.some((function(e){return Object(p["d"])(t,new p["b"](e))}))}))}Object(u["j"])("_executeCodeActionProvider",(function(e,t){return m(this,void 0,void 0,(function(){var n,o,s,c,u,h;return _(this,(function(g){switch(g.label){case 0:if(n=t.resource,o=t.rangeOrSelection,s=t.kind,!(n instanceof a["a"]))throw Object(r["b"])();if(c=e.get(f["a"]).getModel(n),!c)throw Object(r["b"])();if(u=d["a"].isISelection(o)?d["a"].liftSelection(o):l["a"].isIRange(o)?c.validateRange(o):void 0,!u)throw Object(r["b"])();return[4,O(c,u,{type:2,filter:{includeSourceActions:!0,include:s&&s.value?new p["b"](s.value):void 0}},i["a"].None)];case 1:return h=g.sent(),setTimeout((function(){return h.dispose()}),100),[2,h.validActions]}}))}))}))},"92a6":function(e,t,n){"use strict";n.r(t),n.d(t,"ColorDetector",(function(){return v}));var o=n("5fe7"),i=n("ceb8"),r=n("fdcc"),s=n("eda7"),a=n("a666"),c=n("b2cc"),u=n("5717"),l=n("6a89"),d=n("b57f"),h=n("b707"),f=n("6483"),p=n("fbba"),g=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),m=function(e,t,n,o){var i,r=arguments.length,s=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)s=Reflect.decorate(e,t,n,o);else for(var a=e.length-1;a>=0;a--)(i=e[a])&&(s=(r<3?i(s):r>3?i(t,n,s):i(t,n))||s);return r>3&&s&&Object.defineProperty(t,n,s),s},_=function(e,t){return function(n,o){t(n,o,e)}},b=500,v=function(e){function t(t,n,o){var i=e.call(this)||this;return i._editor=t,i._codeEditorService=n,i._configurationService=o,i._localToDispose=i._register(new a["b"]),i._decorationsIds=[],i._colorDatas=new Map,i._colorDecoratorIds=[],i._decorationsTypes=new Set,i._register(t.onDidChangeModel((function(e){i._isEnabled=i.isEnabled(),i.onModelChanged()}))),i._register(t.onDidChangeModelLanguage((function(e){return i.onModelChanged()}))),i._register(h["c"].onDidChange((function(e){return i.onModelChanged()}))),i._register(t.onDidChangeConfiguration((function(e){var t=i._isEnabled;i._isEnabled=i.isEnabled(),t!==i._isEnabled&&(i._isEnabled?i.onModelChanged():i.removeAllDecorations())}))),i._timeoutTimer=null,i._computePromise=null,i._isEnabled=i.isEnabled(),i.onModelChanged(),i}return g(t,e),t.prototype.isEnabled=function(){var e=this._editor.getModel();if(!e)return!1;var t=e.getLanguageIdentifier(),n=this._configurationService.getValue(t.language);if(n){var o=n["colorDecorators"];if(o&&void 0!==o["enable"]&&!o["enable"])return o["enable"]}return this._editor.getOption(12)},t.get=function(e){return e.getContribution(this.ID)},t.prototype.dispose=function(){this.stop(),this.removeAllDecorations(),e.prototype.dispose.call(this)},t.prototype.onModelChanged=function(){var e=this;if(this.stop(),this._isEnabled){var n=this._editor.getModel();n&&h["c"].has(n)&&(this._localToDispose.add(this._editor.onDidChangeModelContent((function(n){e._timeoutTimer||(e._timeoutTimer=new o["e"],e._timeoutTimer.cancelAndSet((function(){e._timeoutTimer=null,e.beginCompute()}),t.RECOMPUTE_TIME))}))),this.beginCompute())}},t.prototype.beginCompute=function(){var e=this;this._computePromise=Object(o["f"])((function(t){var n=e._editor.getModel();return n?Object(f["b"])(n,t):Promise.resolve([])})),this._computePromise.then((function(t){e.updateDecorations(t),e.updateColorDecorators(t),e._computePromise=null}),r["e"])},t.prototype.stop=function(){this._timeoutTimer&&(this._timeoutTimer.cancel(),this._timeoutTimer=null),this._computePromise&&(this._computePromise.cancel(),this._computePromise=null),this._localToDispose.clear()},t.prototype.updateDecorations=function(e){var t=this,n=e.map((function(e){return{range:{startLineNumber:e.colorInfo.range.startLineNumber,startColumn:e.colorInfo.range.startColumn,endLineNumber:e.colorInfo.range.endLineNumber,endColumn:e.colorInfo.range.endColumn},options:d["a"].EMPTY}}));this._decorationsIds=this._editor.deltaDecorations(this._decorationsIds,n),this._colorDatas=new Map,this._decorationsIds.forEach((function(n,o){return t._colorDatas.set(n,e[o])}))},t.prototype.updateColorDecorators=function(e){for(var t=this,n=[],o={},r=0;r<e.length&&n.length<b;r++){var a=e[r].colorInfo.color,c=a.red,u=a.green,l=a.blue,d=a.alpha,h=new i["c"](Math.round(255*c),Math.round(255*u),Math.round(255*l),d),f=Object(s["a"])(h).toString(16),p="rgba("+h.r+", "+h.g+", "+h.b+", "+h.a+")",g="colorBox-"+f;this._decorationsTypes.has(g)||o[g]||this._codeEditorService.registerDecorationType(g,{before:{contentText:" ",border:"solid 0.1em #000",margin:"0.1em 0.2em 0 0.2em",width:"0.8em",height:"0.8em",backgroundColor:p},dark:{before:{border:"solid 0.1em #eee"}}},void 0,this._editor),o[g]=!0,n.push({range:{startLineNumber:e[r].colorInfo.range.startLineNumber,startColumn:e[r].colorInfo.range.startColumn,endLineNumber:e[r].colorInfo.range.endLineNumber,endColumn:e[r].colorInfo.range.endColumn},options:this._codeEditorService.resolveDecorationOptions(g,!0)})}this._decorationsTypes.forEach((function(e){o[e]||t._codeEditorService.removeDecorationType(e)})),this._colorDecoratorIds=this._editor.deltaDecorations(this._colorDecoratorIds,n)},t.prototype.removeAllDecorations=function(){var e=this;this._decorationsIds=this._editor.deltaDecorations(this._decorationsIds,[]),this._colorDecoratorIds=this._editor.deltaDecorations(this._colorDecoratorIds,[]),this._decorationsTypes.forEach((function(t){e._codeEditorService.removeDecorationType(t)}))},t.prototype.getColorData=function(e){var t=this,n=this._editor.getModel();if(!n)return null;var o=n.getDecorationsInRange(l["a"].fromPositions(e,e)).filter((function(e){return t._colorDatas.has(e.id)}));return 0===o.length?null:this._colorDatas.get(o[0].id)},t.ID="editor.contrib.colorDetector",t.RECOMPUTE_TIME=1e3,t=m([_(1,u["a"]),_(2,p["a"])],t),t}(a["a"]);Object(c["h"])(v.ID,v)},"9e56":function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"d",(function(){return r})),n.d(t,"c",(function(){return s})),n.d(t,"a",(function(){return c}));var o=n("3742"),i=function(){function e(e){this.value=e}return e.prototype.equals=function(e){return this.value===e.value},e.prototype.contains=function(t){return this.equals(t)||""===this.value||Object(o["N"])(t.value,this.value+e.sep)},e.prototype.intersects=function(e){return this.contains(e)||e.contains(this)},e.prototype.append=function(t){return new e(this.value+e.sep+t)},e.sep=".",e.None=new e("@@none@@"),e.Empty=new e(""),e.QuickFix=new e("quickfix"),e.Refactor=new e("refactor"),e.Source=new e("source"),e.SourceOrganizeImports=e.Source.append("organizeImports"),e.SourceFixAll=e.Source.append("fixAll"),e}();function r(e,t){return!(e.include&&!e.include.intersects(t))&&((!e.excludes||!e.excludes.some((function(n){return a(t,n,e.include)})))&&!(!e.includeSourceActions&&i.Source.contains(t)))}function s(e,t){var n=t.kind?new i(t.kind):void 0;return!!(!e.include||n&&e.include.contains(n))&&(!(e.excludes&&n&&e.excludes.some((function(t){return a(n,t,e.include)})))&&(!(!e.includeSourceActions&&n&&i.Source.contains(n))&&!(e.onlyIncludePreferredActions&&!t.isPreferred)))}function a(e,t,n){return!!t.contains(e)&&(!n||!t.contains(n))}var c=function(){function e(e,t,n){this.kind=e,this.apply=t,this.preferred=n}return e.fromUser=function(t,n){return t&&"object"===typeof t?new e(e.getKindFromUser(t,n.kind),e.getApplyFromUser(t,n.apply),e.getPreferredUser(t)):new e(n.kind,n.apply,!1)},e.getApplyFromUser=function(e,t){switch("string"===typeof e.apply?e.apply.toLowerCase():""){case"first":return"first";case"never":return"never";case"ifsingle":return"ifSingle";default:return t}},e.getKindFromUser=function(e,t){return"string"===typeof e.kind?new i(e.kind):t},e.getPreferredUser=function(e){return"boolean"===typeof e.preferred&&e.preferred},e}()},"9f4d":function(e,t,n){"use strict";n.r(t);var o=n("dff7"),i=n("fe45"),r=n("b2cc"),s=n("c101"),a=n("d3f4"),c=n("7061"),u=n("6a89"),l=n("8025"),d=n("70cb"),h=function(){function e(e,t){this._selection=e,this._insertSpace=t,this._usedEndToken=null}return e._haystackHasNeedleAtOffset=function(e,t,n){if(n<0)return!1;var o=t.length,i=e.length;if(n+o>i)return!1;for(var r=0;r<o;r++){var s=e.charCodeAt(n+r),a=t.charCodeAt(r);if(s!==a&&(!(s>=65&&s<=90&&s+32===a)&&!(a>=65&&a<=90&&a+32===s)))return!1}return!0},e.prototype._createOperationsForBlockComment=function(t,n,o,i,r,s){var a,c=t.startLineNumber,l=t.startColumn,d=t.endLineNumber,h=t.endColumn,f=r.getLineContent(c),p=r.getLineContent(d),g=f.lastIndexOf(n,l-1+n.length),m=p.indexOf(o,h-1-o.length);if(-1!==g&&-1!==m)if(c===d){var _=f.substring(g+n.length,m);_.indexOf(o)>=0&&(g=-1,m=-1)}else{var b=f.substring(g+n.length),v=p.substring(0,m);(b.indexOf(o)>=0||v.indexOf(o)>=0)&&(g=-1,m=-1)}-1!==g&&-1!==m?(i&&g+n.length<f.length&&32===f.charCodeAt(g+n.length)&&(n+=" "),i&&m>0&&32===p.charCodeAt(m-1)&&(o=" "+o,m-=1),a=e._createRemoveBlockCommentOperations(new u["a"](c,g+n.length+1,d,m+1),n,o)):(a=e._createAddBlockCommentOperations(t,n,o,this._insertSpace),this._usedEndToken=1===a.length?o:null);for(var y=0,C=a;y<C.length;y++){var w=C[y];s.addTrackedEditOperation(w.range,w.text)}},e._createRemoveBlockCommentOperations=function(e,t,n){var o=[];return u["a"].isEmpty(e)?o.push(a["a"].delete(new u["a"](e.startLineNumber,e.startColumn-t.length,e.endLineNumber,e.endColumn+n.length))):(o.push(a["a"].delete(new u["a"](e.startLineNumber,e.startColumn-t.length,e.startLineNumber,e.startColumn))),o.push(a["a"].delete(new u["a"](e.endLineNumber,e.endColumn,e.endLineNumber,e.endColumn+n.length)))),o},e._createAddBlockCommentOperations=function(e,t,n,o){var i=[];return u["a"].isEmpty(e)?i.push(a["a"].replace(new u["a"](e.startLineNumber,e.startColumn,e.endLineNumber,e.endColumn),t+"  "+n)):(i.push(a["a"].insert(new c["a"](e.startLineNumber,e.startColumn),t+(o?" ":""))),i.push(a["a"].insert(new c["a"](e.endLineNumber,e.endColumn),(o?" ":"")+n))),i},e.prototype.getEditOperations=function(e,t){var n=this._selection.startLineNumber,o=this._selection.startColumn;e.tokenizeIfCheap(n);var i=e.getLanguageIdAtPosition(n,o),r=d["a"].getComments(i);r&&r.blockCommentStartToken&&r.blockCommentEndToken&&this._createOperationsForBlockComment(this._selection,r.blockCommentStartToken,r.blockCommentEndToken,this._insertSpace,e,t)},e.prototype.computeCursorState=function(e,t){var n=t.getInverseEditOperations();if(2===n.length){var o=n[0],i=n[1];return new l["a"](o.range.endLineNumber,o.range.endColumn,i.range.startLineNumber,i.range.startColumn)}var r=n[0].range,s=this._usedEndToken?-this._usedEndToken.length-1:0;return new l["a"](r.endLineNumber,r.endColumn+s,r.endLineNumber,r.endColumn+s)},e}(),f=n("3742"),p=function(){function e(e,t,n,o){this._selection=e,this._tabSize=t,this._type=n,this._insertSpace=o,this._selectionId=null,this._deltaColumn=0,this._moveEndPositionDown=!1}return e._gatherPreflightCommentStrings=function(e,t,n){e.tokenizeIfCheap(t);var o=e.getLanguageIdAtPosition(t,1),i=d["a"].getComments(o),r=i?i.lineCommentToken:null;if(!r)return null;for(var s=[],a=0,c=n-t+1;a<c;a++)s[a]={ignore:!1,commentStr:r,commentStrOffset:0,commentStrLength:r.length};return s},e._analyzeLines=function(e,t,n,o,i){var r,s=!0;r=0===e||1!==e;for(var a=0,c=o.length;a<c;a++){var u=o[a],l=i+a,d=n.getLineContent(l),p=f["q"](d);if(-1!==p){if(s=!1,u.ignore=!1,u.commentStrOffset=p,r&&!h._haystackHasNeedleAtOffset(d,u.commentStr,p)&&(0===e?r=!1:1===e||(u.ignore=!0)),r&&t){var g=p+u.commentStrLength;g<d.length&&32===d.charCodeAt(g)&&(u.commentStrLength+=1)}}else u.ignore=!0,u.commentStrOffset=d.length}if(0===e&&s){r=!1;for(a=0,c=o.length;a<c;a++)o[a].ignore=!1}return{supported:!0,shouldRemoveComments:r,lines:o}},e._gatherPreflightData=function(t,n,o,i,r){var s=e._gatherPreflightCommentStrings(o,i,r);return null===s?{supported:!1}:e._analyzeLines(t,n,o,s,i)},e.prototype._executeLineComments=function(t,n,o,i){var r;o.shouldRemoveComments?r=e._createRemoveLineCommentsOperations(o.lines,i.startLineNumber):(e._normalizeInsertionPoint(t,o.lines,i.startLineNumber,this._tabSize),r=this._createAddLineCommentsOperations(o.lines,i.startLineNumber));for(var s=new c["a"](i.positionLineNumber,i.positionColumn),a=0,u=r.length;a<u;a++)if(n.addEditOperation(r[a].range,r[a].text),r[a].range.isEmpty()&&r[a].range.getStartPosition().equals(s)){var l=t.getLineContent(s.lineNumber);l.length+1===s.column&&(this._deltaColumn=(r[a].text||"").length)}this._selectionId=n.trackSelection(i)},e.prototype._attemptRemoveBlockComment=function(e,t,n,o){var i=t.startLineNumber,r=t.endLineNumber,s=o.length+Math.max(e.getLineFirstNonWhitespaceColumn(t.startLineNumber),t.startColumn),a=e.getLineContent(i).lastIndexOf(n,s-1),c=e.getLineContent(r).indexOf(o,t.endColumn-1-n.length);return-1!==a&&-1===c&&(c=e.getLineContent(i).indexOf(o,a+n.length),r=i),-1===a&&-1!==c&&(a=e.getLineContent(r).lastIndexOf(n,c),i=r),!t.isEmpty()||-1!==a&&-1!==c||(a=e.getLineContent(i).indexOf(n),-1!==a&&(c=e.getLineContent(i).indexOf(o,a+n.length))),-1!==a&&32===e.getLineContent(i).charCodeAt(a+n.length)&&(n+=" "),-1!==c&&32===e.getLineContent(r).charCodeAt(c-1)&&(o=" "+o,c-=1),-1!==a&&-1!==c?h._createRemoveBlockCommentOperations(new u["a"](i,a+n.length+1,r,c+1),n,o):null},e.prototype._executeBlockComment=function(e,t,n){e.tokenizeIfCheap(n.startLineNumber);var o=e.getLanguageIdAtPosition(n.startLineNumber,1),i=d["a"].getComments(o);if(i&&i.blockCommentStartToken&&i.blockCommentEndToken){var r=i.blockCommentStartToken,s=i.blockCommentEndToken,a=this._attemptRemoveBlockComment(e,n,r,s);if(!a){if(n.isEmpty()){var c=e.getLineContent(n.startLineNumber),l=f["q"](c);-1===l&&(l=c.length),a=h._createAddBlockCommentOperations(new u["a"](n.startLineNumber,l+1,n.startLineNumber,c.length+1),r,s,this._insertSpace)}else a=h._createAddBlockCommentOperations(new u["a"](n.startLineNumber,e.getLineFirstNonWhitespaceColumn(n.startLineNumber),n.endLineNumber,e.getLineMaxColumn(n.endLineNumber)),r,s,this._insertSpace);1===a.length&&(this._deltaColumn=r.length+1)}this._selectionId=t.trackSelection(n);for(var p=0,g=a;p<g.length;p++){var m=g[p];t.addEditOperation(m.range,m.text)}}},e.prototype.getEditOperations=function(t,n){var o=this._selection;this._moveEndPositionDown=!1,o.startLineNumber<o.endLineNumber&&1===o.endColumn&&(this._moveEndPositionDown=!0,o=o.setEndPosition(o.endLineNumber-1,t.getLineMaxColumn(o.endLineNumber-1)));var i=e._gatherPreflightData(this._type,this._insertSpace,t,o.startLineNumber,o.endLineNumber);return i.supported?this._executeLineComments(t,n,i,o):this._executeBlockComment(t,n,o)},e.prototype.computeCursorState=function(e,t){var n=t.getTrackedSelection(this._selectionId);return this._moveEndPositionDown&&(n=n.setEndPosition(n.endLineNumber+1,1)),new l["a"](n.selectionStartLineNumber,n.selectionStartColumn+this._deltaColumn,n.positionLineNumber,n.positionColumn+this._deltaColumn)},e._createRemoveLineCommentsOperations=function(e,t){for(var n=[],o=0,i=e.length;o<i;o++){var r=e[o];r.ignore||n.push(a["a"].delete(new u["a"](t+o,r.commentStrOffset+1,t+o,r.commentStrOffset+r.commentStrLength+1)))}return n},e.prototype._createAddLineCommentsOperations=function(e,t){for(var n=[],o=this._insertSpace?" ":"",i=0,r=e.length;i<r;i++){var s=e[i];s.ignore||n.push(a["a"].insert(new c["a"](t+i,s.commentStrOffset+1),s.commentStr+o))}return n},e.nextVisibleColumn=function(e,t,n,o){return n?e+(t-e%t):e+o},e._normalizeInsertionPoint=function(t,n,o,i){for(var r,s,a=1073741824,c=0,u=n.length;c<u;c++)if(!n[c].ignore){for(var l=t.getLineContent(o+c),d=0,h=0,f=n[c].commentStrOffset;d<a&&h<f;h++)d=e.nextVisibleColumn(d,i,9===l.charCodeAt(h),1);d<a&&(a=d)}a=Math.floor(a/i)*i;for(c=0,u=n.length;c<u;c++)if(!n[c].ignore){l=t.getLineContent(o+c),d=0;for(r=0,s=n[c].commentStrOffset;d<a&&r<s;r++)d=e.nextVisibleColumn(d,i,9===l.charCodeAt(r),1);n[c].commentStrOffset=d>a?r-1:r}},e}(),g=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),m=function(e){function t(t,n){var o=e.call(this,n)||this;return o._type=t,o}return g(t,e),t.prototype.run=function(e,t){if(t.hasModel()){for(var n=t.getModel(),o=[],i=t.getSelections(),r=n.getOptions(),s=t.getOption(13),a=0,c=i;a<c.length;a++){var u=c[a];o.push(new p(u,r.tabSize,this._type,s.insertSpace))}t.pushUndoStop(),t.executeCommands(this.id,o),t.pushUndoStop()}},t}(r["b"]),_=function(e){function t(){return e.call(this,0,{id:"editor.action.commentLine",label:o["a"]("comment.line","Toggle Line Comment"),alias:"Toggle Line Comment",precondition:s["a"].writable,kbOpts:{kbExpr:s["a"].editorTextFocus,primary:2133,weight:100},menuOpts:{menuId:17,group:"5_insert",title:o["a"]({key:"miToggleLineComment",comment:["&& denotes a mnemonic"]},"&&Toggle Line Comment"),order:1}})||this}return g(t,e),t}(m),b=function(e){function t(){return e.call(this,1,{id:"editor.action.addCommentLine",label:o["a"]("comment.line.add","Add Line Comment"),alias:"Add Line Comment",precondition:s["a"].writable,kbOpts:{kbExpr:s["a"].editorTextFocus,primary:Object(i["a"])(2089,2081),weight:100}})||this}return g(t,e),t}(m),v=function(e){function t(){return e.call(this,2,{id:"editor.action.removeCommentLine",label:o["a"]("comment.line.remove","Remove Line Comment"),alias:"Remove Line Comment",precondition:s["a"].writable,kbOpts:{kbExpr:s["a"].editorTextFocus,primary:Object(i["a"])(2089,2099),weight:100}})||this}return g(t,e),t}(m),y=function(e){function t(){return e.call(this,{id:"editor.action.blockComment",label:o["a"]("comment.block","Toggle Block Comment"),alias:"Toggle Block Comment",precondition:s["a"].writable,kbOpts:{kbExpr:s["a"].editorTextFocus,primary:1567,linux:{primary:3103},weight:100},menuOpts:{menuId:17,group:"5_insert",title:o["a"]({key:"miToggleBlockComment",comment:["&& denotes a mnemonic"]},"Toggle &&Block Comment"),order:2}})||this}return g(t,e),t.prototype.run=function(e,t){if(t.hasModel()){for(var n=t.getOption(13),o=[],i=t.getSelections(),r=0,s=i;r<s.length;r++){var a=s[r];o.push(new h(a,n.insertSpace))}t.pushUndoStop(),t.executeCommands(this.id,o),t.pushUndoStop()}},t}(r["b"]);Object(r["f"])(_),Object(r["f"])(b),Object(r["f"])(v),Object(r["f"])(y)},aed7:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var o=n("308f"),i=function(){function e(e,t,n){this.presentationIndex=n,this._onColorFlushed=new o["a"],this.onColorFlushed=this._onColorFlushed.event,this._onDidChangeColor=new o["a"],this.onDidChangeColor=this._onDidChangeColor.event,this._onDidChangePresentation=new o["a"],this.onDidChangePresentation=this._onDidChangePresentation.event,this.originalColor=e,this._color=e,this._colorPresentations=t}return Object.defineProperty(e.prototype,"color",{get:function(){return this._color},set:function(e){this._color.equals(e)||(this._color=e,this._onDidChangeColor.fire(e))},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"presentation",{get:function(){return this.colorPresentations[this.presentationIndex]},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"colorPresentations",{get:function(){return this._colorPresentations},set:function(e){this._colorPresentations=e,this.presentationIndex>e.length-1&&(this.presentationIndex=0),this._onDidChangePresentation.fire(this.presentation)},enumerable:!0,configurable:!0}),e.prototype.selectNextColorPresentation=function(){this.presentationIndex=(this.presentationIndex+1)%this.colorPresentations.length,this.flushColor(),this._onDidChangePresentation.fire(this.presentation)},e.prototype.guessColorPresentation=function(e,t){for(var n=0;n<this.colorPresentations.length;n++)if(t===this.colorPresentations[n].label){this.presentationIndex=n,this._onDidChangePresentation.fire(this.presentation);break}},e.prototype.flushColor=function(){this._onColorFlushed.fire(this._color)},e}()},c36f:function(e,t,n){"use strict";n.r(t);n("e431");var o=n("dff7"),i=n("0f70"),r=n("30db"),s=n("e53c"),a=n("b2cc"),c=n("5717"),u=n("c101"),l=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),d="9_cutcopypaste",h=r["f"]||document.queryCommandSupported("cut"),f=r["f"]||document.queryCommandSupported("copy"),p=f&&!i["f"],g=r["f"]||!i["d"]&&document.queryCommandSupported("paste"),m=function(e){function t(t,n){var o=e.call(this,n)||this;return o.browserCommand=t,o}return l(t,e),t.prototype.runCommand=function(e,t){var n=e.get(c["a"]).getFocusedCodeEditor();n&&n.hasTextFocus()?n.trigger("keyboard",this.id,t):document.execCommand(this.browserCommand)},t.prototype.run=function(e,t){t.focus(),document.execCommand(this.browserCommand)},t}(a["b"]),_=function(e){function t(){var t=this,n={kbExpr:u["a"].textInputFocus,primary:2102,win:{primary:2102,secondary:[1044]},weight:100};return r["f"]||(n=void 0),t=e.call(this,"cut",{id:"editor.action.clipboardCutAction",label:o["a"]("actions.clipboard.cutLabel","Cut"),alias:"Cut",precondition:u["a"].writable,kbOpts:n,contextMenuOpts:{group:d,order:1},menuOpts:{menuId:17,group:"2_ccp",title:o["a"]({key:"miCut",comment:["&& denotes a mnemonic"]},"Cu&&t"),order:1}})||this,t}return l(t,e),t.prototype.run=function(t,n){if(n.hasModel()){var o=n.getOption(25);!o&&n.getSelection().isEmpty()||e.prototype.run.call(this,t,n)}},t}(m),b=function(e){function t(){var t=this,n={kbExpr:u["a"].textInputFocus,primary:2081,win:{primary:2081,secondary:[2067]},weight:100};return r["f"]||(n=void 0),t=e.call(this,"copy",{id:"editor.action.clipboardCopyAction",label:o["a"]("actions.clipboard.copyLabel","Copy"),alias:"Copy",precondition:void 0,kbOpts:n,contextMenuOpts:{group:d,order:2},menuOpts:{menuId:17,group:"2_ccp",title:o["a"]({key:"miCopy",comment:["&& denotes a mnemonic"]},"&&Copy"),order:2}})||this,t}return l(t,e),t.prototype.run=function(t,n){if(n.hasModel()){var o=n.getOption(25);!o&&n.getSelection().isEmpty()||e.prototype.run.call(this,t,n)}},t}(m),v=function(e){function t(){var t=this,n={kbExpr:u["a"].textInputFocus,primary:2100,win:{primary:2100,secondary:[1043]},weight:100};return r["f"]||(n=void 0),t=e.call(this,"paste",{id:"editor.action.clipboardPasteAction",label:o["a"]("actions.clipboard.pasteLabel","Paste"),alias:"Paste",precondition:u["a"].writable,kbOpts:n,contextMenuOpts:{group:d,order:3},menuOpts:{menuId:17,group:"2_ccp",title:o["a"]({key:"miPaste",comment:["&& denotes a mnemonic"]},"&&Paste"),order:3}})||this,t}return l(t,e),t}(m),y=function(e){function t(){return e.call(this,"copy",{id:"editor.action.clipboardCopyWithSyntaxHighlightingAction",label:o["a"]("actions.clipboard.copyWithSyntaxHighlightingLabel","Copy With Syntax Highlighting"),alias:"Copy With Syntax Highlighting",precondition:void 0,kbOpts:{kbExpr:u["a"].textInputFocus,primary:0,weight:100}})||this}return l(t,e),t.prototype.run=function(t,n){if(n.hasModel()){var o=n.getOption(25);!o&&n.getSelection().isEmpty()||(s["a"].forceCopyWithSyntaxHighlighting=!0,e.prototype.run.call(this,t,n),s["a"].forceCopyWithSyntaxHighlighting=!1)}},t}(m);h&&Object(a["f"])(_),f&&Object(a["f"])(b),g&&Object(a["f"])(v),p&&Object(a["f"])(y)},e431:function(e,t,n){},e516:function(e,t,n){"use strict";n.r(t),n.d(t,"CursorUndoRedoController",(function(){return l})),n.d(t,"CursorUndo",(function(){return d})),n.d(t,"CursorRedo",(function(){return h}));var o=n("dff7"),i=n("a666"),r=n("b2cc"),s=n("c101"),a=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),c=function(){function e(e){this.selections=e}return e.prototype.equals=function(e){var t=this.selections.length,n=e.selections.length;if(t!==n)return!1;for(var o=0;o<t;o++)if(!this.selections[o].equalsSelection(e.selections[o]))return!1;return!0},e}(),u=function(){function e(e,t,n){this.cursorState=e,this.scrollTop=t,this.scrollLeft=n}return e}(),l=function(e){function t(t){var n=e.call(this)||this;return n._editor=t,n._isCursorUndoRedo=!1,n._undoStack=[],n._redoStack=[],n._register(t.onDidChangeModel((function(e){n._undoStack=[],n._redoStack=[]}))),n._register(t.onDidChangeModelContent((function(e){n._undoStack=[],n._redoStack=[]}))),n._register(t.onDidChangeCursorSelection((function(e){if(!n._isCursorUndoRedo&&e.oldSelections&&e.oldModelVersionId===e.modelVersionId){var o=new c(e.oldSelections),i=n._undoStack.length>0&&n._undoStack[n._undoStack.length-1].cursorState.equals(o);i||(n._undoStack.push(new u(o,t.getScrollTop(),t.getScrollLeft())),n._redoStack=[],n._undoStack.length>50&&n._undoStack.shift())}}))),n}return a(t,e),t.get=function(e){return e.getContribution(t.ID)},t.prototype.cursorUndo=function(){this._editor.hasModel()&&0!==this._undoStack.length&&(this._redoStack.push(new u(new c(this._editor.getSelections()),this._editor.getScrollTop(),this._editor.getScrollLeft())),this._applyState(this._undoStack.pop()))},t.prototype.cursorRedo=function(){this._editor.hasModel()&&0!==this._redoStack.length&&(this._undoStack.push(new u(new c(this._editor.getSelections()),this._editor.getScrollTop(),this._editor.getScrollLeft())),this._applyState(this._redoStack.pop()))},t.prototype._applyState=function(e){this._isCursorUndoRedo=!0,this._editor.setSelections(e.cursorState.selections),this._editor.setScrollPosition({scrollTop:e.scrollTop,scrollLeft:e.scrollLeft}),this._isCursorUndoRedo=!1},t.ID="editor.contrib.cursorUndoRedoController",t}(i["a"]),d=function(e){function t(){return e.call(this,{id:"cursorUndo",label:o["a"]("cursor.undo","Cursor Undo"),alias:"Cursor Undo",precondition:void 0,kbOpts:{kbExpr:s["a"].textInputFocus,primary:2099,weight:100}})||this}return a(t,e),t.prototype.run=function(e,t,n){l.get(t).cursorUndo()},t}(r["b"]),h=function(e){function t(){return e.call(this,{id:"cursorRedo",label:o["a"]("cursor.redo","Cursor Redo"),alias:"Cursor Redo",precondition:void 0})||this}return a(t,e),t.prototype.run=function(e,t,n){l.get(t).cursorRedo()},t}(r["b"]);Object(r["h"])(l.ID,l),Object(r["f"])(d),Object(r["f"])(h)},f004:function(e,t,n){}}]);