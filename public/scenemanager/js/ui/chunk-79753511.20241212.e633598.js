(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-79753511"],{"4d7c":function(t,i,e){},ba90:function(t,i,e){"use strict";e.r(i);var n=function(){var t=this,i=t.$createElement,e=t._self._c||i;return e("div",{staticClass:"mobile-right-sidebar",style:{right:t.sidebarRight}},[e("div",{staticClass:"sidebar-title"},[t._v(" 视点列表 "),e("span",{on:{click:function(i){return t.setSidebarRight("-46vw")}}},[e("i",{staticClass:"el-icon-arrow-right"})])]),e("div",{staticClass:"sidebar-content"},[0==t.viewpointDatas.length?e("p",{staticClass:"text-center"},[t._v("暂无视点数据")]):t._e(),t._l(t.viewpointDatas,(function(i,n){return e("div",{key:i.id,staticClass:"view-list",on:{click:function(e){return t.restoreData(i)}}},[i.default?e("div",{staticClass:"list-mark"},[t._v(" "+t._s(t.$t("others.default"))+" ")]):t._e(),e("img",{attrs:{src:i.thumbnail,alt:""}}),e("p",[t._v(t._s(i.name||t.$t("topToolBarMenu.advanced.children.viewpoint.name")+n))])])}))],2)])},s=[],a=(e("d3b7"),e("159b"),{name:"MarkupListView",data:function(){return{sidebarRight:"-46vw",viewpointDatas:[]}},mounted:function(){this.getSceneViewpoints()},methods:{restoreData:function(t){window.scene.restoreViewpoint(t.id),this.setSidebarRight("-46vw")},setSidebarRight:function(t){this.sidebarRight=t},getSceneViewpoints:function(){var t=this;window.scene.viewpoints.length>0&&window.scene.viewpoints.forEach((function(i){"viewpoint"==i.type&&t.viewpointDatas.unshift(i)}))}}}),c=a,o=(e("e7e1"),e("2877")),r=Object(o["a"])(c,n,s,!1,null,"cb1f96c4",null);i["default"]=r.exports},e7e1:function(t,i,e){"use strict";e("4d7c")}}]);