(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-ef5a74ba","chunk-51f90655"],{"07ac":function(e,t,n){var i=n("23e7"),o=n("6f53").values;i({target:"Object",stat:!0},{values:function(e){return o(e)}})},"17d0":function(e,t,n){"use strict";n.r(t);var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("svg",{class:e.svgClass,style:{fontSize:e.svgSize,color:e.color}},[n("use",{attrs:{"xlink:href":e.iconName}})])},o=[],r=(n("a9e3"),{name:"CommonSVG",props:{iconClass:{type:String,required:!0},className:{type:String,default:""},color:{type:String,default:"#98A2B3"},size:{type:[String,Number],default:"23"}},computed:{svgSize:function(){var e=document.documentElement.style.fontSize,t="".concat(parseFloat(this.size)/parseFloat(e),"rem");return t},iconName:function(){return"#icon-".concat(this.iconClass)},svgClass:function(){return this.className?"svg-icon ".concat(this.className):"svg-icon"}}}),a=r,l=(n("8fd1"),n("2877")),s=Object(l["a"])(a,i,o,!1,null,"f76b8e7e",null);t["default"]=s.exports},"1c2f":function(e,t,n){},"3af1":function(e,t,n){},"5cb3":function(e,t,n){"use strict";n.r(t);var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{directives:[{name:"dragresize",rawName:"v-dragresize",value:e.dragConfig,expression:"dragConfig"}],ref:"posWrapper",staticClass:"animationtimeline-pos-wrapper",style:e.posStyleObj},[n("animation-time-line",{ref:"animationTimeline"})],1)},o=[],r=(n("a9e3"),function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:"timelineWrapper",staticClass:"timeline-wrapper"},[n("header",{staticClass:"timeline-header"},[n("div",{staticClass:"col-1"},[e.isElementAnimationGroup?n("el-tooltip",{attrs:{enterable:!1,content:e.$t("animate.label4"),effect:"dark",placement:"top"}},[n("div",{staticClass:"item autoPlay"},[n("el-checkbox",{model:{value:e.autoPlay,callback:function(t){e.autoPlay=t},expression:"autoPlay"}},[e._v(" "+e._s(e.$t("animate.label4"))+" ")])],1)]):e._e(),e.isElementAnimationGroup?n("div",{staticClass:"rest"},[e.hasAnimationTargets?n("div",{staticClass:"wrapperForToggalAllCurrent",on:{click:function(t){return t.stopPropagation(),e.doToggleCurrentAnimationGroupBusiness.apply(null,arguments)}}},[n("div",{staticClass:"labelForToggleAll"},[e._v(e._s(e.labelForToggleAllAnimations))]),n("CommonSVG",{attrs:{"icon-class":e.isCurrentGroupSomeAnimating?"ani_stop_feature":"ani_play_feature",size:18,color:"#2680fe"}})],1):e._e()]):e._e()],1),n("div",{staticClass:"col-2"},[e.isElementAnimationGroup?n("el-tooltip",{attrs:{enterable:!1,content:"当前模式:"+e.labelForIsSelectedJoin,effect:"dark",placement:"top"}},[n("el-checkbox",{staticClass:"item check-join",attrs:{disabled:e.isModalBasePointVisible},model:{value:e.isSelectedJoin,callback:function(t){e.isSelectedJoin=t},expression:"isSelectedJoin"}},[e._v(" "+e._s(e.labelForIsSelectedJoin)+" ")])],1):e._e(),e.isElementAnimationGroup?[n("div",{directives:[{name:"show",rawName:"v-show",value:!e.isSelectedJoin&&e.infoWaitingForConfirm,expression:"!isSelectedJoin && infoWaitingForConfirm"}],staticClass:"item confirm-join"},[n("div",{staticClass:"confirm-content"},[e._v(" 将【"+e._s(e.infoWaitingForConfirm&&e.infoWaitingForConfirm.name)+"】加入动画列表吗? ")]),n("div",{staticClass:"actions"},[n("el-button",{attrs:{size:"mini"},on:{click:function(t){return t.stopPropagation(),e.confirmJoinCancel.apply(null,arguments)}}},[e._v("取消")]),n("el-button",{attrs:{size:"mini"},on:{click:function(t){return t.stopPropagation(),e.confirmJoinOk.apply(null,arguments)}}},[e._v("确认")])],1)])]:e._e(),n("div",{staticClass:"rest"},[n("el-tooltip",{attrs:{enterable:!1,effect:"dark",content:e.$t("animate.tooltip3"),placement:"top"}},[n("div",{staticClass:"item time-fromAxis"},[e._v(" "+e._s(e.timeFromTimeline)+" ")])]),n("el-tooltip",{attrs:{enterable:!1,effect:"dark",content:"输入数字后回车或上下键跳转到指定秒",placement:"top"}},[n("div",{staticClass:"item timeToJump"},[n("div",{staticClass:"head"},[e._v("跳转至")]),n("el-input-number",{staticClass:"inputNumCus",attrs:{controls:!1,precision:1,step:.1,min:0,size:"mini",placeholder:"数字秒"},on:{change:e.onTimeNumToJumpChange},model:{value:e.timeNumToJump,callback:function(t){e.timeNumToJump=t},expression:"timeNumToJump"}}),n("div",{staticClass:"tail"},[e._v("S")])],1)]),n("el-tooltip",{attrs:{enterable:!1,effect:"dark",content:e.$t("animate.tooltip5"),placement:"top"}},[n("div",{staticClass:"item action-quit",on:{click:function(t){return t.stopPropagation(),e.confirmDoCloseBusiness.apply(null,arguments)}}},[n("CommonSVG",{attrs:{"icon-class":"ani_exit_feature",size:14}})],1)]),n("el-tooltip",{attrs:{enterable:!1,effect:"dark",content:e.$t("animate.save"),placement:"top"}},[n("div",{staticClass:"item action-save",on:{click:function(t){return t.stopPropagation(),e.doSaveAnimationBusiness.apply(null,arguments)}}},[n("CommonSVG",{attrs:{"icon-class":"ani_save_feature",size:20}})],1)])],1)],2)]),n("main",{staticClass:"main"},[n("div",{staticClass:"outline"},[n("div",{staticClass:"outline-header",style:e.getOutlineHeaderStyleObj()}),n("div",{ref:"outlineScrollContainer",staticClass:"outline-scroll-container",on:{mousewheel:function(t){return t.stopPropagation(),e.onOutlineMouseWheel(t)}}},[n("div",{ref:"outlineContainer",staticClass:"outline-items"},[e.isElementAnimationGroup?e._l(e.finalTargets,(function(t){return n("div",{key:t.id,staticClass:"ani-target",class:{animating:t.isAnimating}},[n("div",{staticClass:"track-composed",style:e.composedTrackStyle,on:{mouseenter:function(n){return n.stopPropagation(),e.onEnterOutlineItem({isComposedTrack:!0,target:t},n)},mouseleave:function(n){return n.stopPropagation(),e.onLeaveOutlineItem({isComposedTrack:!0,target:t},n)}}},[n("el-tooltip",{attrs:{effect:"dark",placement:"top-end",content:t.type+"-"+t.name,enterable:!1,disabled:!t.subTracks.length||t.isAnimating,"open-delay":e.delayForTooltip}},[n("div",{staticClass:"targetNameWrapper",class:{animating:t.isAnimating},on:{click:function(n){return n.stopPropagation(),e.toggleAniTarget(t)}}},[n("i",{staticClass:"targetNameIcon",class:t.iconClass,staticStyle:{color:"#fff"}}),n("span",{staticClass:"targetNameValue"},[e._v(e._s(t.name))])])]),t.isSetBasePointVisable?n("el-tooltip",{attrs:{effect:"dark",placement:"top",content:"点击设置基点",enterable:!1,disabled:t.isAnimating,"open-delay":e.delayForTooltip}},[n("div",{staticClass:"basePointWrapper",class:{animating:t.isAnimating},on:{click:function(n){return n.stopPropagation(),e.showSetBasePointModal(t)}}},[n("div",{staticClass:"basePointLabel"},[e._v("基点")]),n("CommonSVG",{staticStyle:{color:"#2680fe"},attrs:{"icon-class":"choose_coordinates",size:18}})],1)]):e._e(),n("div",{staticClass:"actions"},[n("el-tooltip",{attrs:{effect:"dark",placement:"top",content:"点击选择播放方式",enterable:!1,disabled:t.isAnimating,"open-delay":e.delayForTooltip}},[n("div",{staticClass:"action loopType"},[n("el-select",{staticClass:"el-select-cus",attrs:{placeholder:"播放方式",size:"mini",disabled:t.isAnimating},model:{value:t.loopTypeId,callback:function(n){e.$set(t,"loopTypeId",n)},expression:"target.loopTypeId"}},e._l(e.loopTypes,(function(e){return n("el-option",{key:e.id,attrs:{label:e.label,value:e.id}})})),1)],1)]),n("el-tooltip",{attrs:{effect:"dark",placement:"top",content:t.isAnimating?"点击停止动画":"点击播放动画",enterable:!1,"open-delay":e.delayForTooltip}},[n("div",{staticClass:"action togglePlay",on:{click:function(n){return n.stopPropagation(),e.doTogglePlayAniBusiness(t)}}},[n("CommonSVG",{attrs:{"icon-class":t.isAnimating?"ani_stop_feature":"ani_play_feature",color:"#fff",size:16}})],1)]),n("el-tooltip",{attrs:{effect:"dark",placement:"top",content:"点击删除动画",enterable:!1,disabled:t.isAnimating,"open-delay":e.delayForTooltip}},[n("div",{staticClass:"action delete",on:{click:function(n){return n.stopPropagation(),e.doDeleteAniTarget(t)}}},[n("CommonSVG",{attrs:{"icon-class":"delete",color:"#fff",size:16}})],1)]),n("el-tooltip",{attrs:{effect:"dark",placement:"top",content:"点击添加轨道",enterable:!1,disabled:t.isAnimating,"open-delay":e.delayForTooltip}},[n("div",{staticClass:"action addTrack",on:{click:function(n){return n.stopPropagation(),e.showAddTrackTypeModal(t)}}},[n("CommonSVG",{attrs:{"icon-class":"add_element",color:"#fff",size:16}})],1)])],1)],1),n("el-collapse-transition",[n("div",{directives:[{name:"show",rawName:"v-show",value:!t.isCollapsed,expression:"!target.isCollapsed"}],staticClass:"sub-tracks"},e._l(t.subTracks,(function(i){return n("div",{key:i.extendInfo.trackTypeId,staticClass:"track-sub",style:e.subTrackStyle,on:{mouseenter:function(n){return n.stopPropagation(),e.onEnterOutlineItem({isComposedTrack:!1,target:t,subTrack:i},n)},mouseleave:function(n){return n.stopPropagation(),e.onLeaveOutlineItem({isComposedTrack:!1,target:t,subTrack:i},n)}}},[n("el-tooltip",{attrs:{effect:"dark",placement:"top",content:i.extendInfo.trackTypeTitle+"轨道",enterable:!1,disabled:t.isAnimating,"open-delay":e.delayForTooltip}},[n("div",{staticClass:"label"},[n("CommonSVG",{attrs:{"icon-class":"layer",color:"#fff",size:16}}),n("div",{staticClass:"trackTypeTitle"},[e._v(" "+e._s(i.extendInfo.trackTypeTitle)+" ")])],1)]),n("div",{staticClass:"actions"},[n("div",{staticClass:"action keyframe-nav"},[n("el-tooltip",{attrs:{effect:"dark",placement:"top",content:e.$t("animate.tooltip6"),enterable:!1,disabled:t.isAnimating,"open-delay":e.delayForTooltip}},[n("div",{staticClass:"toolbar-btn frame-first",on:{click:function(n){return n.stopPropagation(),e.toSpecificFrame(t,i,"first")}}},[n("CommonSVG",{attrs:{"icon-class":"start_feature",size:14,color:"#fff"}})],1)]),n("el-tooltip",{attrs:{effect:"dark",placement:"top",content:e.$t("animate.tooltip7"),enterable:!1,disabled:t.isAnimating,"open-delay":e.delayForTooltip}},[n("div",{staticClass:"toolbar-btn frame-prev",on:{click:function(n){return n.stopPropagation(),e.toSpecificFrame(t,i,"prev")}}},[n("CommonSVG",{attrs:{"icon-class":"previous_frame_feature",size:16,color:"#fff"}})],1)]),n("el-tooltip",{attrs:{effect:"dark",placement:"top",content:e.$t("animate.tooltip8"),enterable:!1,disabled:t.isAnimating,"open-delay":e.delayForTooltip}},[n("div",{staticClass:"toolbar-btn frame-next",on:{click:function(n){return n.stopPropagation(),e.toSpecificFrame(t,i,"next")}}},[n("CommonSVG",{attrs:{"icon-class":"next_frame_feature",size:16,color:"#fff"}})],1)]),n("el-tooltip",{attrs:{effect:"dark",placement:"top",content:e.$t("animate.tooltip9"),enterable:!1,disabled:t.isAnimating,"open-delay":e.delayForTooltip}},[n("div",{staticClass:"toolbar-btn frame-last",on:{click:function(n){return n.stopPropagation(),e.toSpecificFrame(t,i,"last")}}},[n("CommonSVG",{attrs:{"icon-class":"end_feature",size:14,color:"#fff"}})],1)])],1),n("el-tooltip",{attrs:{effect:"dark",placement:"top",content:"点击删除轨道",enterable:!1,disabled:t.isAnimating,"open-delay":e.delayForTooltip}},[n("div",{staticClass:"action deleteTrack",on:{click:function(n){return n.stopPropagation(),e.doDeleteSubTrackBusiness(t,i)}}},[n("CommonSVG",{attrs:{"icon-class":"delete",color:"#fff",size:16}})],1)]),n("el-tooltip",{attrs:{effect:"dark",placement:"top",content:"点击插入关键帧",enterable:!1,disabled:t.isAnimating,"open-delay":e.delayForTooltip}},[n("div",{staticClass:"action addKeyframe",on:{click:function(n){return n.stopPropagation(),e.doAddKeyframeBusiness(t,i)}}},[n("CommonSVG",{attrs:{"icon-class":"add_frame",color:"#fff",size:16}})],1)])],1)],1)})),0)])],1)})):e._l(e.finalTargets,(function(t){return n("div",{key:t.id,staticClass:"ani-target",class:{animating:t.isAnimating}},[n("div",{staticClass:"track-composed isCameraAnimation",style:e.composedTrackStyle,on:{mouseenter:function(n){return n.stopPropagation(),e.onEnterOutlineItem({isComposedTrack:!0,target:t,isCameraAnimation:!0},n)},mouseleave:function(n){return n.stopPropagation(),e.onLeaveOutlineItem({isComposedTrack:!0,target:t,isCameraAnimation:!0},n)}}},[n("div",{staticClass:"targetNameWrapper",class:{animating:t.isAnimating}},[n("CommonSVG",{attrs:{"icon-class":"layer",color:"#fff",size:16}}),n("span",{staticClass:"targetNameValue"},[e._v(e._s(e.labelForCameraAnimation))])],1),n("div",{staticClass:"actions"},[n("div",{staticClass:"action keyframe-nav"},[n("el-tooltip",{attrs:{effect:"dark",placement:"top",content:e.$t("animate.tooltip6"),enterable:!1,disabled:t.isAnimating,"open-delay":e.delayForTooltip}},[n("div",{staticClass:"toolbar-btn frame-first",on:{click:function(n){return n.stopPropagation(),e.toSpecificFrame(t,t.subTracks[0],"first")}}},[n("CommonSVG",{attrs:{"icon-class":"start_feature",size:14,color:"#fff"}})],1)]),n("el-tooltip",{attrs:{effect:"dark",placement:"top",content:e.$t("animate.tooltip7"),enterable:!1,disabled:t.isAnimating,"open-delay":e.delayForTooltip}},[n("div",{staticClass:"toolbar-btn frame-prev",on:{click:function(n){return n.stopPropagation(),e.toSpecificFrame(t,t.subTracks[0],"prev")}}},[n("CommonSVG",{attrs:{"icon-class":"previous_frame_feature",size:16,color:"#fff"}})],1)]),n("el-tooltip",{attrs:{effect:"dark",placement:"top",content:e.$t("animate.tooltip8"),enterable:!1,disabled:t.isAnimating,"open-delay":e.delayForTooltip}},[n("div",{staticClass:"toolbar-btn frame-next",on:{click:function(n){return n.stopPropagation(),e.toSpecificFrame(t,t.subTracks[0],"next")}}},[n("CommonSVG",{attrs:{"icon-class":"next_frame_feature",size:16,color:"#fff"}})],1)]),n("el-tooltip",{attrs:{effect:"dark",placement:"top",content:e.$t("animate.tooltip9"),enterable:!1,disabled:t.isAnimating,"open-delay":e.delayForTooltip}},[n("div",{staticClass:"toolbar-btn frame-last",on:{click:function(n){return n.stopPropagation(),e.toSpecificFrame(t,t.subTracks[0],"last")}}},[n("CommonSVG",{attrs:{"icon-class":"end_feature",size:14,color:"#fff"}})],1)])],1),n("el-tooltip",{attrs:{effect:"dark",placement:"top",content:"点击选择播放方式",enterable:!1,disabled:t.isAnimating,"open-delay":e.delayForTooltip}},[n("div",{staticClass:"action loopType"},[n("el-select",{staticClass:"el-select-cus",attrs:{placeholder:"播放方式",size:"mini",disabled:t.isAnimating},model:{value:t.loopTypeId,callback:function(n){e.$set(t,"loopTypeId",n)},expression:"target.loopTypeId"}},e._l(e.loopTypes,(function(e){return n("el-option",{key:e.id,attrs:{label:e.label,value:e.id}})})),1)],1)]),n("el-tooltip",{attrs:{effect:"dark",placement:"top",content:t.isAnimating?"点击停止动画":"点击播放动画",enterable:!1,"open-delay":e.delayForTooltip}},[n("div",{staticClass:"action togglePlay",on:{click:function(n){return n.stopPropagation(),e.doTogglePlayAniBusiness(t)}}},[n("CommonSVG",{attrs:{"icon-class":t.isAnimating?"ani_stop_feature":"ani_play_feature",color:"#fff",size:16}})],1)]),n("el-tooltip",{attrs:{effect:"dark",placement:"top",content:"点击删除动画",enterable:!1,disabled:t.isAnimating,"open-delay":e.delayForTooltip}},[n("div",{staticClass:"action delete",on:{click:function(n){return n.stopPropagation(),e.doDeleteAniTarget(t)}}},[n("CommonSVG",{attrs:{"icon-class":"delete",color:"#fff",size:16}})],1)]),n("el-tooltip",{attrs:{effect:"dark",placement:"top",content:"点击插入关键帧",enterable:!1,disabled:t.isAnimating,"open-delay":e.delayForTooltip}},[n("div",{staticClass:"action addKeyframe",on:{click:function(n){return n.stopPropagation(),e.doAddKeyframeBusiness(t,t.subTracks[0])}}},[n("CommonSVG",{attrs:{"icon-class":"add_frame",color:"#fff",size:16}})],1)])],1)])])}))],2)])]),n("div",{staticClass:"timeline",attrs:{id:"timeline"}})]),n("transition",{attrs:{name:"slide"}},[n("div",{directives:[{name:"show",rawName:"v-show",value:e.isModalTrackTypesVisible,expression:"isModalTrackTypesVisible"}],staticClass:"modal-trackTypes"},[n("div",{staticClass:"header"},[n("div",{staticClass:"title"},[e._v("轨道")]),n("div",{staticClass:"close"},[n("CommonSVG",{attrs:{"icon-class":"shut_down",size:14,color:"#fff"},nativeOn:{click:function(t){t.stopPropagation(),e.isModalTrackTypesVisible=!1}}})],1)]),e._l(e.trackTypes,(function(t){return[e.currentAniTargetSubTrackTypeIds.includes(t.id)?n("div",{key:"disabled"+t.id,staticClass:"track-type disabled"},[e._v(" "+e._s(t.label)+" ")]):n("div",{key:t.id,staticClass:"track-type",on:{click:function(n){return n.stopPropagation(),e.doAddTrackBusiness(t)}}},[e._v(" "+e._s(t.label)+" ")])]}))],2)]),e.isElementAnimationGroup?[n("transition",{attrs:{name:"slide"}},[n("div",{directives:[{name:"show",rawName:"v-show",value:e.keyframeInEdit,expression:"keyframeInEdit"}],staticClass:"modal-keyframeProps"},[n("div",{staticClass:"header"},[n("div",{staticClass:"title"},[e._v(e._s(e.trackTitleInEdit))]),n("div",{staticClass:"close",on:{click:function(t){return t.stopPropagation(),e.onKeyframePropClose.apply(null,arguments)}}},[n("CommonSVG",{attrs:{"icon-class":"shut_down",color:"#fff",size:14}})],1)]),n("div",{staticClass:"content"},["position"===e.trackTypeIdInEdit?[n("div",{staticClass:"content-item"},[n("div",{staticClass:"label"},[e._v("对象：")]),n("div",[e._v(e._s(e.aniTargetInEdit&&e.aniTargetInEdit.name))])]),n("div",{staticClass:"content-item"},[n("div",{staticClass:"label"},[e._v(e._s(e.$t("animate.time"))+"：")]),n("el-input",{attrs:{size:"mini",placeholder:e.$t("animate.time"),value:e.getFormatTime(e.keyframeModel.position.time),disabled:""}})],1),n("div",{staticClass:"content-item transform"},[n("div",{staticClass:"label"},[e._v(e._s(e.$t("animate.position"))+"：")]),n("div",{staticClass:"xyz"},[n("el-input",{staticClass:"pos-item",attrs:{size:"mini",placeholder:"X",type:"number",title:""},on:{input:function(t){return e.doKeyframePropChangeBusiness(t,"position",0)}},model:{value:e.keyframeModel.position.valueForView[0],callback:function(t){e.$set(e.keyframeModel.position.valueForView,0,t)},expression:"keyframeModel.position.valueForView[0]"}},[n("template",{slot:"append"},[e._v("X")])],2),n("el-input",{staticClass:"pos-item",attrs:{size:"mini",placeholder:"Y",type:"number",title:""},on:{input:function(t){return e.doKeyframePropChangeBusiness(t,"position",1)}},model:{value:e.keyframeModel.position.valueForView[1],callback:function(t){e.$set(e.keyframeModel.position.valueForView,1,t)},expression:"keyframeModel.position.valueForView[1]"}},[n("template",{slot:"append"},[e._v("Y")])],2),n("el-input",{staticClass:"pos-item",attrs:{size:"mini",placeholder:"Z",type:"number",title:""},on:{input:function(t){return e.doKeyframePropChangeBusiness(t,"position",2)}},model:{value:e.keyframeModel.position.valueForView[2],callback:function(t){e.$set(e.keyframeModel.position.valueForView,2,t)},expression:"keyframeModel.position.valueForView[2]"}},[n("template",{slot:"append"},[e._v("Z")])],2)],1)])]:"rotation"===e.trackTypeIdInEdit?[n("div",{staticClass:"content-item"},[n("div",{staticClass:"label"},[e._v("对象：")]),n("div",[e._v(e._s(e.aniTargetInEdit&&e.aniTargetInEdit.name))])]),n("div",{staticClass:"content-item"},[n("div",{staticClass:"label"},[e._v(e._s(e.$t("animate.time"))+"：")]),n("el-input",{attrs:{size:"mini",placeholder:e.$t("animate.time"),value:e.getFormatTime(e.keyframeModel.rotation.time),disabled:""}})],1),n("div",{staticClass:"content-item transform"},[n("div",{staticClass:"label"},[e._v(e._s(e.$t("menuIconName.rotate"))+"：")]),n("div",{staticClass:"xyz"},[n("el-input",{staticClass:"pos-item",attrs:{size:"mini",placeholder:"X",type:"number",title:""},on:{input:function(t){return e.doKeyframePropChangeBusiness(t,"rotation",0)}},model:{value:e.keyframeModel.rotation.valueForView[0],callback:function(t){e.$set(e.keyframeModel.rotation.valueForView,0,t)},expression:"keyframeModel.rotation.valueForView[0]"}},[n("template",{slot:"append"},[e._v("X")])],2),n("el-input",{staticClass:"pos-item",attrs:{size:"mini",placeholder:"Y",type:"number",title:""},on:{input:function(t){return e.doKeyframePropChangeBusiness(t,"rotation",1)}},model:{value:e.keyframeModel.rotation.valueForView[1],callback:function(t){e.$set(e.keyframeModel.rotation.valueForView,1,t)},expression:"keyframeModel.rotation.valueForView[1]"}},[n("template",{slot:"append"},[e._v("Y")])],2),n("el-input",{staticClass:"pos-item",attrs:{size:"mini",placeholder:"Z",type:"number",title:""},on:{input:function(t){return e.doKeyframePropChangeBusiness(t,"rotation",2)}},model:{value:e.keyframeModel.rotation.valueForView[2],callback:function(t){e.$set(e.keyframeModel.rotation.valueForView,2,t)},expression:"keyframeModel.rotation.valueForView[2]"}},[n("template",{slot:"append"},[e._v("Z")])],2)],1)])]:"scale"===e.trackTypeIdInEdit?[n("div",{staticClass:"content-item"},[n("div",{staticClass:"label"},[e._v("对象：")]),n("div",[e._v(e._s(e.aniTargetInEdit&&e.aniTargetInEdit.name))])]),n("div",{staticClass:"content-item"},[n("div",{staticClass:"label"},[e._v(e._s(e.$t("animate.time"))+"：")]),n("el-input",{attrs:{size:"mini",placeholder:e.$t("animate.time"),value:e.getFormatTime(e.keyframeModel.scale.time),disabled:""}})],1),n("div",{staticClass:"content-item transform"},[n("div",{staticClass:"label"},[e._v(e._s(e.$t("formRelational.scale.label"))+"：")]),n("div",{staticClass:"xyz"},[n("el-input",{staticClass:"pos-item",attrs:{size:"mini",placeholder:"X",type:"number",title:""},on:{input:function(t){return e.doKeyframePropChangeBusiness(t,"scale",0)}},model:{value:e.keyframeModel.scale.valueForView[0],callback:function(t){e.$set(e.keyframeModel.scale.valueForView,0,t)},expression:"keyframeModel.scale.valueForView[0]"}},[n("template",{slot:"append"},[e._v("X")])],2),n("el-input",{staticClass:"pos-item",attrs:{size:"mini",placeholder:"Y",type:"number",title:""},on:{input:function(t){return e.doKeyframePropChangeBusiness(t,"scale",1)}},model:{value:e.keyframeModel.scale.valueForView[1],callback:function(t){e.$set(e.keyframeModel.scale.valueForView,1,t)},expression:"keyframeModel.scale.valueForView[1]"}},[n("template",{slot:"append"},[e._v("Y")])],2),n("el-input",{staticClass:"pos-item",attrs:{size:"mini",placeholder:"Z",type:"number",title:""},on:{input:function(t){return e.doKeyframePropChangeBusiness(t,"scale",2)}},model:{value:e.keyframeModel.scale.valueForView[2],callback:function(t){e.$set(e.keyframeModel.scale.valueForView,2,t)},expression:"keyframeModel.scale.valueForView[2]"}},[n("template",{slot:"append"},[e._v("Z")])],2)],1)])]:"visible"===e.trackTypeIdInEdit?[n("div",{staticClass:"content-item"},[n("div",{staticClass:"label"},[e._v("对象：")]),n("div",[e._v(e._s(e.aniTargetInEdit&&e.aniTargetInEdit.name))])]),n("div",{staticClass:"content-item"},[n("div",{staticClass:"label"},[e._v(e._s(e.$t("animate.time"))+"：")]),n("el-input",{attrs:{size:"mini",placeholder:e.$t("animate.time"),value:e.getFormatTime(e.keyframeModel.visible.time),disabled:""}})],1),n("div",{staticClass:"content-item"},[n("div",{staticClass:"label"},[e._v(e._s(e.$t("menuIconName.show"))+"：")]),n("el-checkbox",{attrs:{checked:e.keyframeModel.visible.value,"true-label":!0,"false-label":!1},on:{change:function(t){return e.doKeyframePropChangeBusiness(t,"visible")}},model:{value:e.keyframeModel.visible.value,callback:function(t){e.$set(e.keyframeModel.visible,"value",t)},expression:"keyframeModel.visible.value"}})],1)]:"color"===e.trackTypeIdInEdit?[n("div",{staticClass:"content-item"},[n("div",{staticClass:"label"},[e._v("对象：")]),n("div",[e._v(e._s(e.aniTargetInEdit&&e.aniTargetInEdit.name))])]),n("div",{staticClass:"content-item"},[n("div",{staticClass:"label"},[e._v(e._s(e.$t("animate.time"))+"：")]),n("el-input",{attrs:{size:"mini",placeholder:e.$t("animate.time"),value:e.getFormatTime(e.keyframeModel.color.time),disabled:""}})],1),n("div",{staticClass:"content-item"},[n("div",{staticClass:"label"},[e._v(e._s(e.$t("formRelational.color.label"))+"：")]),n("el-color-picker",{attrs:{size:"mini"},on:{change:function(t){return e.doKeyframePropChangeBusiness(t,"color")}},model:{value:e.keyframeModel.color.value,callback:function(t){e.$set(e.keyframeModel.color,"value",t)},expression:"keyframeModel.color.value"}})],1)]:e._e()],2),n("div",{staticClass:"footer"},[n("div",{staticClass:"footer-item",on:{click:function(t){return t.stopPropagation(),e.onKeyframePropRestore.apply(null,arguments)}}},[e._v(" "+e._s(e.$t("menuIconName.reset1"))+" ")])])])])]:e._e(),n("transition",{attrs:{name:"slide"}},[e.isModalBasePointVisible&&e.aniTargetInEdit?n("div",{staticClass:"modal-animationBasePoint"},[n("div",{staticClass:"header"},[n("div",{staticClass:"title"},[e._v(e._s(e.$t("animate.label6")))]),n("div",{staticClass:"close",on:{click:function(t){return t.stopPropagation(),e.hideSetBasepointModal.apply(null,arguments)}}},[n("CommonSVG",{attrs:{"icon-class":"shut_down",color:"#fff",size:14}})],1)]),n("div",{staticClass:"content"},[n("div",{staticClass:"content-item"},[n("div",{staticClass:"label"},[e._v("对象：")]),n("div",[e._v(e._s(e.aniTargetInEdit&&e.aniTargetInEdit.name))])]),n("div",{staticClass:"content-item"},[n("div",{staticClass:"label"},[e._v(e._s(e.$t("animate.label7"))+"：")]),n("CommonSVG",{style:e.styleForChoosePoint,attrs:{"icon-class":"select_coord",size:16},nativeOn:{click:function(t){return t.stopPropagation(),e.toggleQueryPosition.apply(null,arguments)}}})],1),n("div",{staticClass:"content-item basepoint"},[n("div",{staticClass:"label"},[e._v(e._s(e.$t("formRelational.basePoint.label1"))+"：")]),n("div",{staticClass:"xyz"},[n("el-input",{staticClass:"pos-item",attrs:{size:"mini",placeholder:"X",type:"number"},model:{value:e.finalTargetsBasePointMap[e.aniTargetInEdit.id].x,callback:function(t){e.$set(e.finalTargetsBasePointMap[e.aniTargetInEdit.id],"x",e._n(t))},expression:"finalTargetsBasePointMap[aniTargetInEdit.id].x"}},[n("template",{slot:"append"},[e._v("X")])],2),n("el-input",{staticClass:"pos-item",attrs:{size:"mini",placeholder:"Y",type:"number"},model:{value:e.finalTargetsBasePointMap[e.aniTargetInEdit.id].y,callback:function(t){e.$set(e.finalTargetsBasePointMap[e.aniTargetInEdit.id],"y",e._n(t))},expression:"finalTargetsBasePointMap[aniTargetInEdit.id].y"}},[n("template",{slot:"append"},[e._v("Y")])],2),n("el-input",{staticClass:"pos-item",attrs:{size:"mini",placeholder:"Z",type:"number"},model:{value:e.finalTargetsBasePointMap[e.aniTargetInEdit.id].z,callback:function(t){e.$set(e.finalTargetsBasePointMap[e.aniTargetInEdit.id],"z",e._n(t))},expression:"finalTargetsBasePointMap[aniTargetInEdit.id].z"}},[n("template",{slot:"append"},[e._v("Z")])],2)],1)])]),n("div",{staticClass:"footer"},[n("div",{staticClass:"footer-item",on:{click:function(t){return t.stopPropagation(),e.onAnimationBasePointRestore.apply(null,arguments)}}},[e._v(" "+e._s(e.$t("menuIconName.reset1"))+" ")]),n("div",{staticClass:"footer-item",on:{click:function(t){return t.stopPropagation(),e.closeBptModalAndUpdate.apply(null,arguments)}}},[e._v(" 确定 ")])])]):e._e()]),e.isModalAddAnimationVisible?n("dialogComp",{attrs:{hideHeader:!1,needClose:"true",zIndex:"100",position:"fixed",draw:!1,top:0,right:0,bottom:0,left:0,drag:!1,title:"添加动画组",width:300,height:150,type:"detailInfo"},on:{close:e.onAddAnimationCancel},scopedSlots:e._u([{key:"center",fn:function(){return[n("div",{staticClass:"add-container"},[n("el-input",{ref:"addInput",attrs:{size:"small",placeholder:"请输入动画组名称"},scopedSlots:e._u([{key:"prepend",fn:function(){return[e._v(" 动画组名称 ")]},proxy:!0}],null,!1,1838266575),model:{value:e.animationGroupToAdd,callback:function(t){e.animationGroupToAdd=t},expression:"animationGroupToAdd"}}),n("div",{staticClass:"bottom-btn"},[n("span",{staticClass:"cursor-btn cancel margin-right-8",on:{click:e.onAddAnimationCancel}},[e._v(" "+e._s(e.$t("messageTips.cancel"))+" ")]),n("span",{staticClass:"cursor-btn confirm",on:{click:function(t){return t.stopPropagation(),e.doAddOrUpdateAnimationGroupBusiness.apply(null,arguments)}}},[e._v(" "+e._s(e.$t("menuIconName.confirm"))+" ")])])],1)]},proxy:!0}],null,!1,2266396451)}):e._e()],2)}),a=[],l=n("3835"),s=n("2909"),c=n("b85c"),u=(n("e9c4"),n("d81d"),n("d3b7"),n("b0c0"),n("99af"),n("4de4"),n("159b"),n("7db0"),n("c740"),n("a434"),n("b64b"),n("caad"),n("2532"),n("07ac"),n("a630"),n("3ca3"),n("6062"),n("ddb0"),n("b680"),n("ac1f"),n("00b4"),n("fb6a"),n("4ec9"),n("0eb6"),n("b7ef"),n("8bd4"),n("ad1f"),n("f7fe")),d=n.n(u),f=n("9be3"),m=n("17d0"),p=/^\s*$/i,v={position:"translate",rotation:"rotate",scale:"scale"},h=27962e4,y={trackHeight:36,trackMarginBottom:2,composedTrackFillColor:"rgba(255,255,255,0.1)",composedTrackHoverFillColor:"rgba(255,255,255,0.2)",composedTrackKeyframeColor:"#2680FE",subTrackFillColor:"rgba(255,255,255,0.05)",subTrackHoverFillColor:"#24416e",timelineOptions:{id:"timeline",headerHeight:35,stepPx:120,stepSmallPx:12,stepVal:1e3,snapStep:100,snapEnabled:!0,leftMargin:24,headerFillColor:"#242630",fillColor:"#242630",selectionColor:"#0f0",font:"10px 'Microsoft Yahei'",zoomMin:1,zoomMax:8,timelineStyle:{width:1,marginTop:14,marginBottom:1,capStyle:{width:12,height:30,strokeColor:"hsla(33,100%,50%,1)",fillColor:"hsla(33,100%,50%,1)",capType:"triangle"},strokeColor:"hsla(33,100%,50%,1)",fillColor:"hsla(33,100%,50%,1)",cursor:"ew-resize"},keyframesStyle:{fillColor:"#FEA426",shape:"Rhomb",selectedFillColor:"#f00"},rowsStyle:{groupsStyle:{fillColor:"#475059"}}}},g=function(e){return null===e||void 0===e?e:JSON.parse(JSON.stringify(e))},_={name:"AnimationTimeLine",components:{CommonSVG:m["default"]},data:function(){return{animationGroupFromVuex:null,finalTargetsCopy:[],delayForTooltip:300,isSelectedJoin:!1,infoWaitingForConfirm:null,timeNumToJump:null,finalTargets:[],finalTargetsBaksMap:{},aniTargetInEdit:null,keyframeInEdit:null,trackInEdit:null,sizeObserver:null,timeline:null,keyframeModel:{position:{time:0,value:[],valueForView:[]},rotation:{time:0,value:[],valueForView:[]},scale:{time:0,value:[],valueForView:[]},visible:{time:0,value:null},color:{time:0,value:null,originColor:null},camera:{time:0,value:{},valueForView:{}}},trackTypes:[{id:"position",label:"位移"},{id:"rotation",label:"旋转"},{id:"scale",label:"缩放"},{id:"visible",label:"显隐"},{id:"color",label:"颜色"}],isModalTrackTypesVisible:!1,loopTypes:[{id:0,label:this.$t("animate.label10")},{id:1,label:this.$t("animate.label11")},{id:2,label:this.$t("animate.label12")}],loopTypeId:1,idFromFeaturePicked:null,idFromSingleSelection:null,finalTargetsBasePointMap:{},isCreateNew:!0,animationGroupNameToUpdate:"",targetsNameMap:{},isQueryPositionEnabled:!1,autoPlay:!0,timeFromTimeline:null,isModalBasePointVisible:!1,isModalAddAnimationVisible:!1,animationGroupToAdd:"",targetTimeOffsetRecord:{},isTransformtoolsVisible:!1}},computed:{trackInEditExtendInfo:function(){var e,t;return null!==(e=null===(t=this.trackInEdit)||void 0===t?void 0:t.extendInfo)&&void 0!==e?e:null},trackTypeIdInEdit:function(){var e,t;return null!==(e=null===(t=this.trackInEditExtendInfo)||void 0===t?void 0:t.trackTypeId)&&void 0!==e?e:null},trackTitleInEdit:function(){var e,t;return null!==(e=null===(t=this.trackInEditExtendInfo)||void 0===t?void 0:t.trackTypeTitle)&&void 0!==e?e:null},labelForIsSelectedJoin:function(){return this.isSelectedJoin?"选中即加入动画列表":"选中确认后加入动画列表"},styleForChoosePoint:function(){return this.isQueryPositionEnabled?{color:"#2680FE",cursor:"pointer"}:{color:"unset",cursor:"pointer"}},currentAniTargetSubTracks:function(){var e,t;return null!==(e=null===(t=this.aniTargetInEdit)||void 0===t?void 0:t.subTracks)&&void 0!==e?e:[]},currentAniTargetSubTrackTypeIds:function(){return this.currentAniTargetSubTracks.map((function(e){return e.extendInfo.trackTypeId}))},isElementAnimationGroup:function(){return"animation_element"===this.$store.state.animation.animationSubtype},subTrackStyle:function(){return{marginBottom:y.trackMarginBottom+"px",height:y.trackHeight+"px",backgroundColor:y.subTrackFillColor}},composedTrackStyle:function(){return{marginBottom:y.trackMarginBottom+"px",height:y.trackHeight+"px",backgroundColor:y.composedTrackFillColor}},hasAnimationTargets:function(){return!!this.finalTargets.length},isCurrentGroupSomeAnimating:function(){return this.finalTargets.some((function(e){return!0===e.isAnimating}))},labelForToggleAllAnimations:function(){return this.isCurrentGroupSomeAnimating?"全部停止":"全部播放"},labelForCameraAnimation:function(){return this.isCreateNew?"相机动画":this.$store.state.animation.animationGroupNameInEdit}},watch:{isSelectedJoin:function(e){e||(this.infoWaitingForConfirm=null)},isModalBasePointVisible:function(e){this.enableOrDisableQueryPosition(e)},keyframeInEdit:{handler:function(e){e?(this.aniTargetInEdit=e.extendInfo.target,this.trackInEdit=e.extendInfo.subTrack):(this.trackInEdit=null,this.deactiveTransformTools(),this.resetKeyframeModel())},immediate:!0}},created:function(){this.activeAnimationTab(),this.throttledRePaint=d()(this.repaintTimeline,18,{leading:!1,trailing:!0}),this.$bus.on("RestoreAnimation",this.doRestoreAnimationBusiness),this.$bus.on("PlayOrStopAnimationGroup",this.playOrStopAnimationGroup),window.what=this},mounted:function(){var e=this;this.initTimeline(),this.sizeObserver=new ResizeObserver(this.throttledRePaint),this.sizeObserver.observe(this.$refs.timelineWrapper),window.addEventListener("keydown",this.onWinKeyDown,!0),this.$nextTick((function(){e.isElementAnimationGroup?e.doElementAnimationBusiness():e.doCameraAnimationBusiness()}))},beforeDestroy:function(){this.sizeObserver.unobserve(this.$refs.timelineWrapper),window.removeEventListener("keydown",this.onWinKeyDown,!0),this.enableOrDisableQueryPosition(!1),this.unRegisterEnginePickEvents(),this.unRegisterControlEnd(),this.deactiveTransformTools(),this.$store.commit("setActivedType",""),this.$store.commit("setAnimationSubtype",""),this.$store.commit("setIsTimeLineVisible",!1),this.$store.commit("setAnimationGroupNameInEdit",null),this.$bus.off("RestoreAnimation",this.doRestoreAnimationBusiness),this.$bus.off("PlayOrStopAnimationGroup",this.playOrStopAnimationGroup),this.timeline.dispose()},destroyed:function(){this.$bus.emit("AnimationTimeLineDestroyed")},methods:{getOutlineHeaderStyleObj:function(){var e,t,n=null!==(e=null===(t=this.timeline)||void 0===t?void 0:t.getOptions())&&void 0!==e?e:null;if(n)return{height:"".concat(n.headerHeight,"px"),minHeight:"".concat(n.headerHeight,"px"),maxHeight:"".concat(n.headerHeight,"px"),backgroundColor:n.headerFillColor}},doElementAnimationBusiness:function(){this.registerEnginePickEvents(),this.$notify({title:"点选动画目标",message:this.labelForIsSelectedJoin,duration:6e3,showClose:!0,type:"success"})},doCameraAnimationBusiness:function(){var e;(this.registerControlEnd(),this.appendCameraInfoToBaksMap(),this.isCreateNew)&&(window.scene.features.has("camera")?e=window.scene.features.get("camera"):(e=window.scene.addFeature("camera"),e.name=this.$t("animate.label"),e.load()),this.appendCameraToFinalTargets(e),this.setTimelineModel())},registerControlEnd:function(){window.scene.mv.controller.addEventListener("controlend",this.onControlEnd)},unRegisterControlEnd:function(){window.scene.mv.controller.removeEventListener("controlend",this.onControlEnd)},onControlEnd:function(){this.keyframeInEdit&&!this.isElementAnimationGroup&&this.recordKeyframeCameraState(this.finalTargets[0].subTracks[0],this.keyframeInEdit.val)},hideSetBasepointModal:function(){this.isModalBasePointVisible=!1},closeBptModalAndUpdate:function(){var e=this;this.isModalBasePointVisible=!1,this.isTransformtoolsVisible&&setTimeout((function(){var t,n,i;e.deactiveTransformTools();var o=null===(t=e.trackInEditExtendInfo)||void 0===t?void 0:t.aniTargetId,r=null===(n=e.trackInEditExtendInfo)||void 0===n?void 0:n.aniTargetType,a=v[null===(i=e.trackInEditExtendInfo)||void 0===i?void 0:i.trackTypeId];if(o&&r){var l=e.finalTargetsBasePointMap[o],s=l?[l.x,l.y,l.z]:null;e.setAnimationTargetBpt({id:o,type:r,basePoint:s}),a&&e.activeTransformToolsByTarget(o,r,a)}}),20)},getUniqueNameByTarget:function(e,t){if(e&&void 0!==t&&null!==t){if(!0===t)return this.targetsNameMap[e.id];var n=e.id,i="".concat(n,"-").concat(Date.now());return this.targetsNameMap[n]=i,i}},onTimeNumToJumpChange:function(e,t){var n=this;if(null===e||void 0===e)this.$nextTick((function(){n.timeNumToJump=t}));else{var i=1e3*Number(e);i>h&&(i=h,this.$nextTick((function(){n.timeNumToJump=h/1e3}))),this.timeline.setTime(i),this.moveTimelineIntoTheBounds()}},moveTimelineIntoTheBounds:function(){var e=this.timeline;if(e){if(e._startPos||e._scrollAreaClickOrDragStarted)return;var t=e.scrollLeft,n=e.scrollLeft+e.getClientWidth(),i=e.valToPx(e.getTime())+e._leftMargin();(i<=t||i>=n)&&(this.timeline.scrollLeft=i-y.timelineOptions.leftMargin)}},confirmJoinCancel:function(){this.infoWaitingForConfirm=null},confirmJoinOk:function(){var e=this.infoWaitingForConfirm,t=e.name,n=e.type,i=e.id;this.appendFinalTargets([{name:t,type:n,id:i}]),this.appendFinalTargetsBaksMap([{name:t,type:n,id:i}]),this.infoWaitingForConfirm=null},registerEnginePickEvents:function(){var e=window.scene.mv.events;e.singleSelection.on("default",this.onSingleSelection),e.featurePicked.on("default",this.onFeaturePicked)},unRegisterEnginePickEvents:function(){var e=window.scene.mv.events;e.singleSelection.off("default",this.onSingleSelection),e.featurePicked.off("default",this.onFeaturePicked)},onWinKeyDown:function(e){var t,n,i,o=this;if(46===(null===e||void 0===e?void 0:e.keyCode)){var r=null!==(t=null===(n=this.timeline)||void 0===n||null===(i=n.getSelectedElements())||void 0===i?void 0:i.filter((function(e){return"keyframe"===e.type})))&&void 0!==t?t:[],a=r.length;a>0&&this.$confirm("确认删除选中的(红色)".concat(a,"个关键帧吗？"),"关键帧删除确认",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then((function(){r.forEach((function(e){e.row.keyframes=e.row.keyframes.filter((function(e){return!e.selected})),o.doDeleteKeyframeBusiness(e.keyframe)})),o.setTimelineModel()})).catch((function(){}))}},doDeleteKeyframeBusiness:function(e){if(e){var t=e.extendInfo,n=t.subTrack,i=t.target,o=n.extendInfo.trackTypeId,r=this.targetTimeOffsetRecord;delete n.extendInfo.timeValuesMap[e.val],"position"===o&&delete r[i.id][e.val],e===this.keyframeInEdit&&(this.keyframeInEdit=null)}},activeAnimationTab:function(){"animation"!==this.$store.state.animation.validActivedType&&this.$store.commit("setValidActivedType","animation")},initTimeline:function(){var e=this.timeline=new f["Timeline"];e.initialize(y.timelineOptions),e.setInteractionMode("selection"),e.onSelected(this.onTimelineKeyframeSelected),e.onTimeChanged(this.onTimelineChanged),e.onDrag(this.onTimelineDrag),e.onDragFinished(this.onTimelineDragFinished),e.onScroll(this.onTimelineScroll),this.timeFromTimeline="".concat(Number(e.getTime())/1e3," S"),this.$bus.emit("AnimationTimeLineSettled")},onTimelineDrag:function(e){var t=e.elements.filter((function(e){return"keyframe"===e.type}));if(1===t.length){var n=t[0].keyframe,i=n.extendInfo.subTrack.extendInfo.trackTypeId;this.keyframeModel[i].time=n.val}this.setTimelineModel()},onTimelineDragFinished:function(e){var t=this,n=e.elements.filter((function(e){return"keyframe"===e.type}));if(null!==n&&void 0!==n&&n.length){var i=!1,o={};n.forEach((function(e){if(e.val!==e.startedVal){var n=e.keyframe.extendInfo,r=n.subTrack,a=n.target,l="".concat(a.id,"-").concat(r.extendInfo.trackTypeId);o[l]||(o[l]={target:a,subTrack:r,changeList:[]});var s=g(r.extendInfo.timeValuesMap),c=[e.val,s[[e.startedVal]]];"position"===r.extendInfo.trackTypeId&&c.push(t.targetTimeOffsetRecord[a.id][e.startedVal]),o[l].changeList.push(c);var u=r.keyframes.filter((function(t){return t.val===e.val}));if(u.length>1){var d=u.find((function(e){return!1===e.selected})),f=r.keyframes.findIndex((function(e){return e===d}));-1!==f&&(i||(i=!0),r.keyframes.splice(f,1))}}})),Object.keys(o).forEach((function(e){var n=o[e],i=n.target,r=n.subTrack,a=n.changeList,l=g(r.extendInfo.timeValuesMap);if(a.forEach((function(e){l[e[0]]=e[1]})),r.extendInfo.timeValuesMap=r.keyframes.map((function(e){return e.val})).reduce((function(e,t){return e[t]=l[t],e}),{}),"position"===r.extendInfo.trackTypeId){var s=r.extendInfo.aniTargetId,c=g(t.targetTimeOffsetRecord[s]);a.forEach((function(e){c[e[0]]=e[2]})),t.targetTimeOffsetRecord[s]=r.keyframes.map((function(e){return e.val})).reduce((function(e,t){return e[t]=c[t],e}),{})}t.detchZeroTimeKeyframe(i,r)})),i&&this.setTimelineModel()}},doAddKeyframeBusiness:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(!e.isAnimating){null===n?n=this.timeline.getTime():(n=Number(n),(isNaN(n)||n<0)&&(n=0));var i=t.keyframes.find((function(e){return e.val===n}));if(i)0===i.val&&!0===i.hidden?(i.hidden=!1,this.recordKeyframeTargetState(t,e.type,e.id,0),this.timeline.select([i]),this.setTimelineModel()):(this.$message.warning("【".concat(e.name,"】- 【").concat(t.extendInfo.trackTypeTitle,"】").concat(n/1e3,"S处已存在关键帧")),this.timeline.select([i]));else{var o={val:n,selected:!1,selectable:!0,draggable:!0,hidden:!1,extendInfo:{target:e,subTrack:t},style:{cursor:"pointer"}};t.keyframes.push(o),this.recordKeyframeTargetState(t,e.type,e.id,n),this.timeline.select([o]),this.detchZeroTimeKeyframe(e,t),this.setTimelineModel()}}},recordKeyframeCameraState:function(e,t){var n=this.keyframeModel.camera;n.time=t;var i=window.scene.getCameraInfo();n.value=g(i),n.valueForView=g(i),e.extendInfo.timeValuesMap[t]=g(i)},recordKeyframeTargetState:function(e,t,n,i){if("camera"!==t&&"camera"!==n&&this.isElementAnimationGroup){var o,r,a=e.extendInfo.trackTypeId;if(o="element"===t?window.scene.findObject(n):window.scene.findFeature(n),!o||!a)return;var l=this.keyframeModel[a];if(!l)return;switch(a){case"position":case"rotation":case"scale":r=o[a],e.extendInfo.timeValuesMap[i]=g(r),l.time=i,l.value=g(r),l.valueForView=g(r),"position"===a&&(e.extendInfo.timeValuesMap[i]=g("element"===t?o.offset:o.position),this.targetTimeOffsetRecord[n]||(this.targetTimeOffsetRecord[n]={}),this.targetTimeOffsetRecord[n][i]=g(o.offset));break;case"visible":case"color":r=o[a],e.extendInfo.timeValuesMap[i]=r,l.time=i,l.value=r;break;default:break}}else this.recordKeyframeCameraState(e,i)},detchZeroTimeKeyframe:function(e,t){if(e&&t){var n=t.keyframes,i=n.find((function(e){return 0===e.val})),o=t.extendInfo.trackTypeId;if(!i&&["position","rotation","scale"].includes(o)){var r=e.id,a=JSON.parse(this.finalTargetsBaksMap[r]),l={val:0,selected:!1,selectable:!0,draggable:!0,hidden:!0,extendInfo:{target:e,subTrack:t},style:{cursor:"pointer"}};t.keyframes.push(l),"position"===o?("element"===e.type?t.extendInfo.timeValuesMap[0]=a.offset:t.extendInfo.timeValuesMap[0]=a.position,this.targetTimeOffsetRecord[r]||(this.targetTimeOffsetRecord[r]={}),this.targetTimeOffsetRecord[r][0]=a.offset):t.extendInfo.timeValuesMap[0]=a[o]}}},activeTransformToolsByTarget:function(e,t,n){if(t&&e&&n){var i,o=window.scene.mv.tools.transform;o.currentMode=n,window.scene.mv.events.transformChange.on("default",this.onTransformChange),i="element"===t?window.scene.findObject(e):window.scene.findFeature(e),i&&(o.active(i),this.isTransformtoolsVisible=!0)}},deactiveTransformTools:function(){window.scene.mv.events.transformChange.off("default",this.onTransformChange),window.scene.mv.tools.transform.deactive(),this.isTransformtoolsVisible=!1},onSingleSelection:function(e){var t,n;if(this.idFromFeaturePicked){var i=window.scene.getSelection();this.idFromSingleSelection=null!==(t=null===(n=i[0])||void 0===n?void 0:n.id)&&void 0!==t?t:null;var o=null,r=!0;this.idFromSingleSelection?o=this.idFromSingleSelection:(o=this.idFromFeaturePicked,r=!1);var a=this.finalTargets.some((function(e){return e.id===o}));if(o&&!a){var l=null;if(l=r?window.scene.findObject(o):window.scene.findFeature(o),!l)return void(this.infoWaitingForConfirm=null);if(this.isSelectedJoin){if(this.isModalBasePointVisible)return;var s=[{id:o,name:l.name,type:l.type}];this.appendFinalTargets(s),this.appendFinalTargetsBaksMap(s)}else{if(this.isModalBasePointVisible)return;this.infoWaitingForConfirm={name:l.name,id:l.id,type:l.type}}}else this.infoWaitingForConfirm=null}else this.infoWaitingForConfirm=null},onFeaturePicked:function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;this.idFromFeaturePicked=null!==(e=null===t||void 0===t?void 0:t.id)&&void 0!==e?e:null},appendFinalTargets:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];if(e.length){var t=this.finalTargets,n=t.map((function(e){return e.id})),i=!1;e.forEach((function(e){if(!n.includes(e.id)){var o;if(o="element"===e.type?window.scene.findObject(e.id):window.scene.findFeature(e.id),!o)return;t.push({id:e.id,name:o.name,type:o.type,loopTypeId:1,isCollapsed:!1,isAnimating:!1,iconClass:"el-icon-caret-top",composedTrack:{style:{fillColor:y.composedTrackFillColor,height:y.trackHeight,marginBottom:y.trackMarginBottom}},isSetBasePointVisable:"element"===e.type,subTracks:[]}),i=!0}})),i&&this.setTimelineModel()}},appendCameraToFinalTargets:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;!this.finalTargets.some((function(e){return"camera"===e.id}))&&e&&this.finalTargets.push({id:e.id,name:e.name||this.$t("animate.label"),type:e.type,loopTypeId:1,isAnimating:!1,composedTrack:{style:{fillColor:y.composedTrackFillColor,height:y.trackHeight,marginBottom:y.trackMarginBottom}},subTracks:[{min:null,max:null,style:{height:y.trackHeight,fillColor:y.composedTrackFillColor,marginBottom:y.trackMarginBottom,keyframesStyle:{}},keyframes:[],hidden:!1,keyframesDraggable:!0,groupsDraggable:!1,extendInfo:{aniTargetId:e.id,aniTargetType:e.type,trackTypeId:"camera",trackTypeTitle:"相机轨道",timeValuesMap:{}}}]})},appendFinalTargetsBaksMap:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];if(t.length){var n=g(this.finalTargetsBaksMap),i=Object.keys(n),o=!1;t.forEach((function(t){if(!i.includes(t.id)){var r=e.getTargetFromScene(t.id,t.type);if(!r)return;var a=JSON.parse(r.snap());a.position=r.position,n[r.id]=JSON.stringify(a),o=!0}})),o&&(this.finalTargetsBaksMap=n)}},appendCameraInfoToBaksMap:function(){this.finalTargetsBaksMap.camera=JSON.stringify(window.scene.getCameraInfo())},toggleAniTarget:function(e){e.isAnimating||(e.isCollapsed=!e.isCollapsed,e.isCollapsed?(e.iconClass="el-icon-caret-bottom",e.subTracks.forEach((function(e){e.hidden=!0})),this.setTimelineModel()):(e.iconClass="el-icon-caret-top",e.subTracks.forEach((function(e){e.hidden=!1})),this.setTimelineModel()))},doTogglePlayAniBusiness:function(e){e.isAnimating?this.doStopAniBusiness(e):this.getCheckFinalTargetsResults()&&(this.regenerateAnimation(e,!0),requestAnimationFrame((function(){window.scene.render()})))},setAnimationZeroTimeUserData:function(e,t){if(e&&t){var n=["position","rotation","scale","camera"];e.userData.zeroTimeInfo={},t.subTracks.forEach((function(t){var i=t.extendInfo.trackTypeId;if(n.includes(i)){var o=t.keyframes.find((function(e){return 0===e.val}));o&&(e.userData.zeroTimeInfo[i]={hidden:o.hidden})}}))}},regenerateAnimation:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=window.scene;if(i){var o=this.getUniqueNameByTarget(e,!0);o?(i.stop(o),i.removeAnimation(o),i.removeClip(o),this.isCreateNew&&(o=this.getUniqueNameByTarget(e,!1))):o=this.getUniqueNameByTarget(e,!1);var r=i.mv.animatonService,a=e.subTracks.reduce((function(e,t){return e[t.extendInfo.trackTypeId]=t.extendInfo,e}),{}),l=Object.keys(a),u=[];if(this.isElementAnimationGroup){var d,f=Object(c["a"])(l);try{var m=function(){var t=d.value,n=a[t],i=n.timeValuesMap;if("position"===t)if("element"===e.type){var o=Object.keys(i).reduce((function(e,t){var n;return e[0].push(Number(t)/1e3),(n=e[1]).push.apply(n,Object(s["a"])(i[t])),e}),[[],[]]),l=r.createOffset(o[0],o[1]);u.push(l)}else{var c=Object.keys(i).reduce((function(e,t){var n;return e[0].push(Number(t)/1e3),(n=e[1]).push.apply(n,Object(s["a"])(i[t])),e}),[[],[]]),f=r.createPosition(c[0],c[1]);u.push(f)}if("rotation"===t){var m=Object.keys(i).reduce((function(e,t){return e[0].push(Number(t)/1e3),e[1].push(i[t][0]),e[2].push(i[t][1]),e[3].push(i[t][2]),e}),[[],[],[],[]]),p=r.createRotation("x",m[0],m[1]);u.push(p);var v=r.createRotation("y",m[0],m[2]);u.push(v);var h=r.createRotation("z",m[0],m[3]);u.push(h)}if("scale"===t){var y=Object.keys(i).reduce((function(e,t){return e[0].push(Number(t)/1e3),e[1].push(i[t][0]),e[2].push(i[t][1]),e[3].push(i[t][2]),e}),[[],[],[],[]]),g=r.createScale("x",y[0],y[1]),_=r.createScale("y",y[0],y[2]),b=r.createScale("z",y[0],y[3]);u.push(g,_,b)}if("visible"===t){var k=Object.keys(i).reduce((function(e,t){return e[0].push(Number(t)/1e3),e[1].push(i[t]),e}),[[],[]]),T=r.createVisibility(k[0],k[1]);u.push(T)}if("color"===t){var C=Object.keys(i).reduce((function(e,t){return e[0].push(Number(t)/1e3),e[1].push(i[t]),e}),[[],[]]),w=r.createColor(C[0],C[1]);u.push(w)}};for(f.s();!(d=f.n()).done;)m()}catch(M){f.e(M)}finally{f.f()}}else{var p=a.camera.timeValuesMap,v=window.scene.mv.tools.coordinate.realPosition,h=Object.keys(p).reduce((function(e,t){return e[t]=Object.values(v(p[t].target)),e}),{}),y=Object.keys(p).reduce((function(e,t){return e[0].push(Number(t)/1e3),e[1].push(h[t][0]),e[2].push(h[t][1]),e[3].push(h[t][2]),e}),[[],[],[],[]]),g=r.createRotation("x",y[0],y[1]);u.push(g);var _=r.createRotation("y",y[0],y[2]);u.push(_);var b=r.createRotation("z",y[0],y[3]);u.push(b);var k=Object.keys(p).reduce((function(e,t){var n;return e[0].push(Number(t)/1e3),(n=e[1]).push.apply(n,Object(s["a"])(Object.values(v(p[t].focalOffset)))),e}),[[],[]]),T=r.createOffset(k[0],k[1]);u.push(T);var C=Object.keys(p).reduce((function(e,t){var n;return e[0].push(Number(t)/1e3),(n=e[1]).push.apply(n,Object(s["a"])(Object.values(v(p[t].position)))),e}),[[],[]]),w=r.createPosition(C[0],C[1]);u.push(w)}if(u.length){var S=i.addAnimation(o),x=r.newClip(o,u);return i.addClip(x),S.userData||(S.userData={}),this.isElementAnimationGroup&&l.includes("position")&&(S.userData.offsetData=this.targetTimeOffsetRecord[e.id]),this.setAnimationZeroTimeUserData(S,e),S.rootObjectID=e.id,S.rootType="element"===e.type?"object":"feature",S.clip=x.name,S.loop=e.loopTypeId,S.timeScale=1,"element"===e.type&&this.setAnimationBasePoint(S,e.id,e.type),S.auto=!!this.isElementAnimationGroup&&this.autoPlay,S.active(!1),n&&(this.recoverTargetById(e.id,e.type),requestAnimationFrame((function(){t.setAnimationTargetBpt({id:e.id,type:e.type,basePoint:S.basePoint}),S.active(!0),t.setTargetInAnimating(e),0===e.loopTypeId&&setTimeout((function(){t.recoverTargetById(e.id,e.type),t.setTargetNotInAnimating(e)}),1e3*S.duration+50)}))),S}}},setAnimationTargetBpt:function(e){var t=e.id,n=e.type,i=e.basePoint;if("element"===n&&null!==i&&void 0!==i&&i.length){var o=this.getTargetFromScene(t,n);o&&(o.bpt=i)}},setAnimationBasePoint:function(e,t,n){if("element"===n){var i=this.finalTargetsBasePointMap[t];if(i){var o=i.x,r=i.y,a=i.z;e.basePoint=[o,r,a]}else{var l=this.getTargetFromScene(t,n);l&&(e.basePoint=Object(s["a"])(l.bpt))}}},getTargetIfValid:function(e){if(!e)return!1;var t=e.subTracks,n=t.length;if(!n)return this.$message({message:"动画【".concat(e.name,"】需要创建至少1个轨道,才能播放"),type:"error",duration:5e3,showClose:!0}),!1;for(var i=!0,o=0;o<n;o++)if(i=this.getTrackIfValid(e,t[o]),!i)break;return i},getTrackIfValid:function(e,t){if(!t)return!1;var n=t.keyframes,i=n.length;return!!i||(this.$message({message:"轨道【".concat(e.name,"-").concat(t.extendInfo.trackTypeTitle,"】数据无效：需要至少1个关键帧,才能播放"),type:"error",duration:5e3,showClose:!0}),!1)},doStopAniBusiness:function(e){if(e){var t=this.getUniqueNameByTarget(e,!0);t&&(window.scene.stop(t),this.recoverTargetById(e.id,e.type),this.setTargetNotInAnimating(e))}},recoverTargetById:function(e,t){var n,i=this.finalTargetsBaksMap[e];"camera"===e?i&&window.scene.resetCamera(JSON.parse(i)):i&&(n="element"===t?window.scene.findObject(e):window.scene.findFeature(e),n&&n.recover(i))},setTargetInAnimating:function(e){var t=this;if(e){e.isAnimating=!0;var n=this.keyframeInEdit;e.subTracks.forEach((function(e){e.keyframes.forEach((function(e){e.selected=!1,e.selectable=!1,e.draggable=!1,e===n&&(t.deactiveTransformTools(),t.keyframeInEdit=null)}))})),this.setTimelineModel(),this.isCreateNew||this.setAniInVuexIfAnimating(e,!0)}},setTargetNotInAnimating:function(e){e&&(e.isAnimating=!1,e.subTracks.forEach((function(e){e.keyframes.forEach((function(e){e.selected=!1,e.selectable=!0,e.draggable=!0}))})),this.setTimelineModel(),this.isCreateNew||this.setAniInVuexIfAnimating(e,!1))},playOrStopAnimationGroup:function(e){var t=this;e?this.getCheckFinalTargetsResults()&&this.finalTargets.forEach((function(e){t.regenerateAnimation(e,!0)})):this.finalTargets.forEach((function(e){t.doStopAniBusiness(e)})),setTimeout((function(){var e;null===(e=window.scene)||void 0===e||e.render()}))},doToggleCurrentAnimationGroupBusiness:function(){this.isCurrentGroupSomeAnimating?this.playOrStopAnimationGroup(!1):this.playOrStopAnimationGroup(!0)},setAniInVuexIfAnimating:function(e,t){if(!this.isCreateNew){var n=this.animationGroupNameToUpdate;if(!n)return;var i=this.$store.state.animation.animationGroups.find((function(e){return e.groupName===n}));if(i){var o=i.animationList,r=o.find((function(t){return t.id===e.id}));r&&(r.isAnimating=t,t?i.isSomeAnimating=!0:o.every((function(e){return!1===e.isAnimating}))&&(i.isSomeAnimating=!1))}}},showAddTrackTypeModal:function(e){e&&!e.isAnimating&&(this.aniTargetInEdit=e,e.isCollapsed=!1,this.isModalTrackTypesVisible=!0)},onEnterOutlineItem:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0,n=e.isComposedTrack,i=e.target,o=e.subTrack,r=e.isCameraAnimation;i&&!i.isAnimating&&(r||n?(null!==t&&void 0!==t&&t.target&&(t.target.style.backgroundColor=y.composedTrackHoverFillColor),i.composedTrack.style.fillColor=y.composedTrackHoverFillColor):o&&(null!==t&&void 0!==t&&t.target&&(t.target.style.backgroundColor=y.subTrackHoverFillColor),o.style.fillColor=y.subTrackHoverFillColor),this.setTimelineModel())},onLeaveOutlineItem:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0,n=e.isComposedTrack,i=e.target,o=e.subTrack,r=e.isCameraAnimation;i&&!i.isAnimating&&(r||n?(null!==t&&void 0!==t&&t.target&&(t.target.style.backgroundColor=y.composedTrackFillColor),i.composedTrack.style.fillColor=y.composedTrackFillColor):o&&(null!==t&&void 0!==t&&t.target&&(t.target.style.backgroundColor=y.subTrackFillColor),o.style.fillColor=y.subTrackFillColor),this.setTimelineModel())},doDeleteAniTarget:function(e){var t=this;e&&!e.isAnimating&&this.$confirm("确定删除动画目标【".concat(e.name,"】的所有轨道和相关动画吗？"),"动画目标删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var n=t.finalTargets.findIndex((function(t){return t===e}));if(-1!==n){var i=t.finalTargets.splice(n,1);i&&(t.setTimelineModel(),t.$message({message:"删除动画目标成功",type:"success",duration:2e3}),i[0]===t.aniTargetInEdit&&(t.keyframeInEdit=null))}})).catch((function(){}))},showSetBasePointModal:function(e){if(e&&!e.isAnimating&&"element"===e.type){var t=this.getTargetFromScene(e.id,e.type);if(t){if(t.selected=!0,window.scene.render(),this.aniTargetInEdit=e,!this.finalTargetsBasePointMap[e.id]){var n=Object(l["a"])(t.bpt,3),i=n[0],o=n[1],r=n[2];this.$set(this.finalTargetsBasePointMap,e.id,{x:i,y:o,z:r})}this.isModalBasePointVisible=!0}}},doDeleteSubTrackBusiness:function(e,t){var n=this;t&&!e.isAnimating&&this.$confirm("确定删除【".concat(e.name,"】的【").concat(t.extendInfo.trackTypeTitle,"】轨道吗？"),"轨道删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var i=e.subTracks.findIndex((function(e){return e===t}));if(-1!==i){var o=e.subTracks.splice(i,1);o&&(n.setTimelineModel(),n.$message({message:"删除轨道成功",type:"success",duration:2e3}),o[0]===n.trackInEdit&&(n.keyframeInEdit=null))}})).catch((function(){}))},doAddTrackBusiness:function(e){var t=this.aniTargetInEdit;if(t){var n=this.aniTargetInEdit.subTracks.some((function(t){return t.id===e.id}));if(!n){this.isModalTrackTypesVisible=!1;var i={min:null,max:null,style:{height:y.trackHeight,fillColor:y.subTrackFillColor,marginBottom:y.trackMarginBottom,keyframesStyle:{}},keyframes:[],hidden:!1,keyframesDraggable:!0,groupsDraggable:!1,extendInfo:{aniTargetId:t.id,aniTargetType:t.type,trackTypeId:e.id,trackTypeTitle:e.label,timeValuesMap:{}}};t.subTracks.push(i),this.setTimelineModel()}}},setTimelineModel:function(){var e=this,t=[],n=!this.isElementAnimationGroup;this.finalTargets.forEach((function(i){if(n)i.subTracks.forEach((function(e){Object.assign(e.style,i.composedTrack.style)})),t=t.concat(i.subTracks);else{var o,r=i.composedTrack.style,a=e.generateComposedTrackByAniTarget(i);Object.assign(a.style,r),i.composedTrack=a,(o=t).push.apply(o,[a].concat(Object(s["a"])(i.subTracks)))}})),this.timeline.setModel({rows:t})},generateComposedTrackByAniTarget:function(e){var t=e.subTracks.reduce((function(e,t){var n;return(n=e.keyframes).push.apply(n,Object(s["a"])(t.keyframes)),e}),{min:null,max:null,style:{height:y.trackHeight,marginBottom:y.trackMarginBottom,fillColor:y.composedTrackFillColor,keyframesStyle:{fillColor:y.composedTrackKeyframeColor}},keyframes:[],hidden:!1,keyframesDraggable:!1,groupsDraggable:!1,extendInfo:{aniTargetId:e.id,aniTargetType:e.type,trackTypeId:"composed-".concat(e.id,"-").concat(e.type),trackTypeTitle:"合成轨道",timeValuesMap:{}}}),n=[],i=[];t.keyframes.forEach((function(e){0===e.val?n.push(e):i.push(e)}));var o=n.some((function(e){return!e.hidden})),r=Array.from(new Set(i.map((function(e){return e.val}))));return o&&r.unshift(0),t.keyframes=r.map((function(e){return{min:null,max:null,selected:!1,selectable:!1,val:e,group:"",style:{},hidden:!1,draggable:!1}})),t},getTargetFromScene:function(e,t){if(e&&t)return"element"===t?window.scene.findObject(e):window.scene.findFeature(e)},onTimelineKeyframeSelected:function(e){var t,n=null===e||void 0===e||null===(t=e.selected)||void 0===t?void 0:t.length;1===n?this.isElementAnimationGroup?this.doElementKeyframeSelectedBusiness(e):this.doCameraKeyframeSelectedBusiness(e):this.keyframeInEdit=null},doElementKeyframeSelectedBusiness:function(e){var t=this,n=e.selected[0];if(!n||!this.keyframeInEdit||this.keyframeInEdit!==n){var i=n.extendInfo,o=i.target,r=i.subTrack,a=o.id,s=o.type,c=r.extendInfo.trackTypeId,u=v[c],d=window.scene.mv.tools.transform,f=r.extendInfo.timeValuesMap[n.val],m=this.getTargetFromScene(a,s),p=this.finalTargetsBasePointMap[a];p&&(m.bpt=[p.x,p.y,p.z]);var h=!1;switch(c){case"position":case"rotation":case"scale":h=!0,"position"===c?m.offset=g("element"===s?f:this.targetTimeOffsetRecord[a][n.val]):m[c]=g(f);break;case"visible":m.visible=f;break;case"color":null!==f&&void 0!==f&&(m.selected=!1,window.scene.execute("color",{objectIDs:[o.id],color:f,opacity:1}));break;default:break}if(h)if(d.transformObj){var y=this.keyframeInEdit.extendInfo,_=y.target,b=y.subTrack;a!==_.id?(this.deactiveTransformTools(),this.$nextTick((function(){t.activeTransformToolsByTarget(a,s,u)}))):c!==b.extendInfo.trackTypeId&&this.$nextTick((function(){window.scene.mv.tools.transform.currentMode=u})),window.scene.mv.tools.transform.syncTargetChange()}else this.$nextTick((function(){t.activeTransformToolsByTarget(a,s,u)}));this.keyframeInEdit=n;var k=this.keyframeModel[c];if(k.time=n.val,"position"===c)if("element"===o.type){var T=Object(l["a"])(r.extendInfo.timeValuesMap[n.val],3),C=T[0],w=T[1],S=T[2],x=Object(l["a"])(m.bpt,3),M=x[0],P=x[1],E=x[2];k.value=[C+M,w+P,S+E],k.valueForView=[C+M,w+P,S+E]}else k.value=g(r.extendInfo.timeValuesMap[n.val]),k.valueForView=g(r.extendInfo.timeValuesMap[n.val]);else k.value=g(r.extendInfo.timeValuesMap[n.val]),k.valueForView=g(r.extendInfo.timeValuesMap[n.val]);window.scene.render()}},doCameraKeyframeSelectedBusiness:function(e){var t=e.selected[0];if(!t||!this.keyframeInEdit||this.keyframeInEdit!==t){var n=t.extendInfo.subTrack;window.scene.resetCamera(n.extendInfo.timeValuesMap[t.val]),this.keyframeInEdit=t}},onTimelineChanged:function(e){this.timeFromTimeline="".concat(e.val/1e3," S")},onTimelineScroll:function(e){var t=this.timeline.getOptions();if(t){var n=this.$refs.outlineContainer;n&&(n.style.minHeight="".concat(e.scrollHeight,"px"),this.$refs.outlineScrollContainer.scrollTop=e.scrollTop)}},onTimelineSettled:function(){this.timeFromTimeline="".concat(Number(this.timeline.getTime())/1e3," S")},onTransformChange:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];if(this.keyframeInEdit){var i=this.keyframeModel[this.trackTypeIdInEdit];switch(i.time=this.keyframeInEdit.val,this.trackTypeIdInEdit){case"position":var o=this.keyframeInEdit.extendInfo,r=(o.subTrack,o.target),a=this.getTargetFromScene(r.id,r.type);i.value=g(a.position),i.valueForView=g(a.position),"element"===r.type?this.trackInEdit.extendInfo.timeValuesMap[i.time]=g(a.offset):this.trackInEdit.extendInfo.timeValuesMap[i.time]=g(i.value);var l=a.id;this.targetTimeOffsetRecord[l]||(this.targetTimeOffsetRecord[l]={}),this.targetTimeOffsetRecord[l][this.keyframeInEdit.val]=g(a.offset);break;case"rotation":if(!t[1])return;var s=t[1],c=s.direction,u=s.angle,d=s.reverse;switch(c){case"x":i.value[0]=Number((!0===d?u-360:u).toFixed(3));break;case"y":i.value[1]=Number((!0===d?u-360:u).toFixed(3));break;case"z":i.value[2]=Number((!0===d?u-360:u).toFixed(3));break}i.value=g(i.value),i.valueForView=g(i.value),this.trackInEdit.extendInfo.timeValuesMap[i.time]=g(i.value);break;case"scale":if(!t[2])return;var f=t[2],m=f.direction,p=f.scale;switch(m){case"x":i.value[0]=p;break;case"y":i.value[1]=p;break;case"z":i.value[2]=p;break}i.value=g(i.value),i.valueForView=g(i.value),this.trackInEdit.extendInfo.timeValuesMap[i.time]=g(i.value);break}}},toSpecificFrame:function(e,t,n){var i,o;if(!e.isAnimating){var r,a=t.keyframes.filter((function(e){return!0!==e.hidden})).map((function(e){return e.val})),l=null!==(i=null===(o=this.keyframeInEdit)||void 0===o?void 0:o.val)&&void 0!==i?i:null,s=!1;switch(n){case"first":r=Math.min.apply(null,a);break;case"last":r=Math.max.apply(null,a);break;case"prev":case"next":var c=this.keyframeInEdit;if(c&&t.keyframes.some((function(e){return e===c})))if(s=!0,"prev"===n){var u=a.filter((function(e){return e<l}));r=Math.max.apply(null,u)}else{var d=a.filter((function(e){return e>l}));r=Math.min.apply(null,d)}else r="prev"===n?Math.min.apply(null,a):Math.max.apply(null,a);break}if(!(r===Number.POSITIVE_INFINITY||r===Number.NEGATIVE_INFINITY||s&&l===r)){var f=t.keyframes.find((function(e){return e.val===r}));f&&f.hidden||this.timeline.select([f])}}},resetKeyframeModel:function(){this.keyframeModel={position:{time:0,value:[],valueForView:[]},rotation:{time:0,value:[],valueForView:[]},scale:{time:0,value:[],valueForView:[]},visible:{time:0,value:!0},color:{time:0,value:null,originColor:null},camera:{time:0,value:{},valueForView:{}}}},onKeyframePropRestore:function(){var e,t=this.keyframeInEdit,n=null===t||void 0===t?void 0:t.extendInfo,i=n.subTrack,o=n.target,r=null===i||void 0===i||null===(e=i.extendInfo)||void 0===e?void 0:e.trackTypeId;if(r){var a=this.getTargetFromScene(o.id,o.type);if(a){var l=JSON.parse(this.finalTargetsBaksMap[o.id]);if(l){var s,c=window.scene;switch(r){case"position":case"rotation":case"scale":"position"===r?(a.offset=g(l.offset),this.targetTimeOffsetRecord[o.id][t.val]=g(l.offset),this.keyframeModel.position.value=g(l.position),this.keyframeModel.position.valueForView=g(l.position),"element"===o.type?i.extendInfo.timeValuesMap[t.val]=g(l.offset):i.extendInfo.timeValuesMap[t.val]=g(l.position)):(s=l[r],a[r]=g(s),this.keyframeModel[r].value=g(s),this.keyframeModel[r].valueForView=g(s),i.extendInfo.timeValuesMap[t.val]=g(s)),c.mv.tools.transform.syncTargetChange();break;case"visible":case"color":s=l[r],"color"===r?null!==s&&void 0!==s&&(this.keyframeModel[r].value=s,i.extendInfo.timeValuesMap[t.val]=s,c.execute("color",{objectIDs:[o.id],color:s,opacity:1})):(this.keyframeModel[r].value=s,i.extendInfo.timeValuesMap[t.val]=s,a[r]=s);break;default:break}c.render()}}}},onKeyframePropClose:function(){this.keyframeInEdit&&(this.keyframeInEdit.selected=!1,this.keyframeInEdit=null,this.$nextTick(this.setTimelineModel))},repaintTimeline:function(){var e=this.timeline;e&&(e.rescale(),e.redraw())},doSaveAnimationBusiness:function(){var e=this;this.isCreateNew?this.getCheckFinalTargetsResults()&&(this.isModalAddAnimationVisible=!0,this.$nextTick((function(){var t;null===(t=e.$refs.addInput)||void 0===t||t.focus()}))):this.doAddOrUpdateAnimationGroupBusiness()},doRestoreAnimationBusiness:function(e){var t=this;if(e){this.isCreateNew=!1;var n=e.animationList,i=e.groupName,o=e.targetsBakMap,r=e.isElementAnimationGroup,a=e.isSomeAnimating;this.autoPlay=!!r&&a,this.$store.commit("setAnimationGroupNameInEdit",i),this.animationGroupFromVuex=e,this.animationGroupNameToUpdate=i,this.finalTargetsBaksMap=o;var c={id:"",name:"",type:"",loopTypeId:1,isCollapsed:!1,isAnimating:!1,iconClass:"el-icon-caret-top",composedTrack:{style:{fillColor:y.composedTrackFillColor,height:y.trackHeight,marginBottom:y.trackMarginBottom}},isSetBasePointVisable:!1,subTracks:[]},u={min:null,max:null,style:{height:y.trackHeight,fillColor:y.subTrackFillColor,marginBottom:y.trackMarginBottom,keyframesStyle:{}},keyframes:[],hidden:!1,keyframesDraggable:!0,groupsDraggable:!1,extendInfo:{aniTargetId:"",aniTargetType:"",trackTypeId:"",trackTypeTitle:"",timeValuesMap:{}}},d=function(e,n,i){if(r){var o="element"===e.type?/\.animationOffset/i:/\.animationPosition/i,a=i.filter((function(e){return o.test(e.name)}));if(a.length){var l,c,d=g(u);d.extendInfo.aniTargetId=e.id,d.extendInfo.aniTargetType=e.type,d.extendInfo.trackTypeId="position",d.extendInfo.trackTypeTitle="位移";var f=a[0],m=f.times.map((function(e){return 1e3*e})),p={};null!==n&&void 0!==n&&null!==(l=n.zeroTimeInfo)&&void 0!==l&&null!==(c=l.position)&&void 0!==c&&c.hidden?m.forEach((function(e,t){0!==e&&(p[e]=Array.from(f.values.slice(3*t,3+3*t)))})):m.forEach((function(e,t){p[e]=Array.from(f.values.slice(3*t,3+3*t))})),d.extendInfo.timeValuesMap=p,d.keyframes=Object.keys(p).map((function(t){return{val:Number(t),selected:!1,selectable:!0,draggable:!0,extendInfo:{target:e,subTrack:d},style:{cursor:"pointer"}}})),e.subTracks.push(d)}var v=/\.animationRotation\[(?:x|y|z)\]/i,h=i.filter((function(e){return v.test(e.name)}));if(3===h.length){var y,_,b=g(u);b.extendInfo.aniTargetId=e.id,b.extendInfo.aniTargetType=e.type,b.extendInfo.trackTypeId="rotation",b.extendInfo.trackTypeTitle="旋转";var k=h.find((function(e){return/\.animationRotation\[x\]/i.test(e.name)})),T=h.find((function(e){return/\.animationRotation\[y\]/i.test(e.name)})),C=h.find((function(e){return/\.animationRotation\[z\]/i.test(e.name)})),w=k.times.map((function(e){return 1e3*e})),S={};null!==n&&void 0!==n&&null!==(y=n.zeroTimeInfo)&&void 0!==y&&null!==(_=y.rotation)&&void 0!==_&&_.hidden?w.reduce((function(e,t,n){return 0===t||(e[t]=[k.values[n],T.values[n],C.values[n]]),e}),S):w.reduce((function(e,t,n){return e[t]=[k.values[n],T.values[n],C.values[n]],e}),S),b.extendInfo.timeValuesMap=S,b.keyframes=Object.keys(S).map((function(t){return{val:Number(t),selected:!1,selectable:!0,draggable:!0,extendInfo:{target:e,subTrack:b},style:{cursor:"pointer"}}})),e.subTracks.push(b)}var x=/\.animationScale\[(?:x|y|z)\]/i,M=i.filter((function(e){return x.test(e.name)}));if(3===M.length){var P,E,I=g(u);I.extendInfo.aniTargetId=e.id,I.extendInfo.aniTargetType=e.type,I.extendInfo.trackTypeId="scale",I.extendInfo.trackTypeTitle="缩放";var A=M.find((function(e){return/\.animationScale\[x\]/i.test(e.name)})),O=M.find((function(e){return/\.animationScale\[y\]/i.test(e.name)})),F=M.find((function(e){return/\.animationScale\[z\]/i.test(e.name)})),V=A.times.map((function(e){return 1e3*e})),B={};null!==n&&void 0!==n&&null!==(P=n.zeroTimeInfo)&&void 0!==P&&null!==(E=P.scale)&&void 0!==E&&E.hidden?V.reduce((function(e,t,n){return 0===t||(e[t]=[A.values[n],O.values[n],F.values[n]]),e}),B):V.reduce((function(e,t,n){return e[t]=[A.values[n],O.values[n],F.values[n]],e}),B),I.extendInfo.timeValuesMap=B,I.keyframes=Object.keys(B).map((function(t){return{val:Number(t),selected:!1,selectable:!0,draggable:!0,extendInfo:{target:e,subTrack:I},style:{cursor:"pointer"}}})),e.subTracks.push(I)}var D=/\.visible/i,R=i.filter((function(e){return D.test(e.name)}));if(R.length){var N=g(u);N.extendInfo.aniTargetId=e.id,N.extendInfo.aniTargetType=e.type,N.extendInfo.trackTypeId="visible",N.extendInfo.trackTypeTitle=t.$t("menuIconName.visible");var z=R[0].times.map((function(e){return 1e3*e})),j={};z.forEach((function(e,t){j[e]=R[0].values[t]})),N.extendInfo.timeValuesMap=j,N.keyframes=Object.keys(j).map((function(t){return{val:Number(t),selected:!1,selectable:!0,draggable:!0,extendInfo:{target:e,subTrack:N},style:{cursor:"pointer"}}})),e.subTracks.push(N)}var G=/\.animationColor/i,K=i.filter((function(e){return G.test(e.name)}));if(K.length){var $=g(u);$.extendInfo.aniTargetId=e.id,$.extendInfo.aniTargetType=e.type,$.extendInfo.trackTypeId="color",$.extendInfo.trackTypeTitle=t.$t("formRelational.color.label");var W=K[0].times.map((function(e){return 1e3*e})),L={};W.forEach((function(e,t){L[e]=K[0].values[t]})),$.extendInfo.timeValuesMap=L,$.keyframes=Object.keys(L).map((function(t){return{val:Number(t),selected:!1,selectable:!0,draggable:!0,extendInfo:{target:e,subTrack:$},style:{cursor:"pointer"}}})),e.subTracks.push($)}}else{var H,U,Z=g(u);Z.extendInfo.aniTargetId=e.id,Z.extendInfo.aniTargetType=e.type,Z.extendInfo.trackTypeId="camera",Z.extendInfo.trackTypeTitle="相机轨道";var J=window.scene.mv.tools.coordinate.scenePosition,Y=i[0].times.map((function(e){return 1e3*e})),q=null===n||void 0===n||null===(H=n.zeroTimeInfo)||void 0===H||null===(U=H.camera)||void 0===U?void 0:U.hidden;q&&Y.splice(0,1);var X={},Q=/\.animationPosition/i,ee=/\.animationOffset/i,te=/\.animationRotation\[x\]/i,ne=/\.animationRotation\[y\]/i,ie=/\.animationRotation\[z\]/i,oe=[],re=[],ae=[];i.forEach((function(e){te.test(e.name)?oe.push.apply(oe,Object(s["a"])(Array.from(e.values))):ne.test(e.name)?re.push.apply(re,Object(s["a"])(Array.from(e.values))):ie.test(e.name)&&ae.push.apply(ae,Object(s["a"])(Array.from(e.values)))})),Y.forEach((function(e,t){X[e]={position:[],target:[],focalOffset:[]},t=q?t+1:t;var n=i.filter((function(e){return Q.test(e.name)}))[0];X[e].position=Object.values(J(Array.from(n.values.slice(3*t,3+3*t))));var o=i.filter((function(e){return ee.test(e.name)}))[0];X[e].focalOffset=Object.values(J(Array.from(o.values.slice(3*t,3+3*t)))),X[e].target=Object.values(J([oe[t],re[t],ae[t]]))})),Z.extendInfo.timeValuesMap=X,Z.keyframes=Object.keys(X).map((function(t){return{val:Number(t),selected:!1,selectable:!0,draggable:!0,extendInfo:{target:e,subTrack:Z},style:{cursor:"pointer"}}})),e.subTracks.push(Z)}},f=window.scene,m=function(e){var i=t.getTargetFromScene(e.id,e.type),o=f.animations.get(e.animationName),a=g(c);a.id=i.id,a.name=i.name,a.type=i.type,a.isAnimating=i.animating||i._animating,a.isSetBasePointVisable="element"===i.type,a.loopTypeId=o.loop,r||(delete a.isCollapsed,delete a.iconClass,a.isAnimating=n.some((function(e){return e.isAnimating}))),d(a,o.userData,f.clips.get(e.animationName).tracks),t.finalTargets.push(a)},p=function(e){var n,i,o;t.targetTimeOffsetRecord[e.id]=g(null!==(n=null===(i=f.animations.get(e.animationName))||void 0===i||null===(o=i.userData)||void 0===o?void 0:o.offsetData)&&void 0!==n?n:{})},v=function(e){var n=f.animations.get(e.animationName);if(n&&"object"===n.rootType){var i=Object(l["a"])(n.basePoint,3),o=i[0],r=i[1],a=i[2];t.$set(t.finalTargetsBasePointMap,e.id,{x:o,y:r,z:a})}},h=function(e){t.targetsNameMap[e.id]=e.animationName};n.forEach((function(e){m(e),r&&(p(e),v(e)),h(e)})),this.setTimelineModel(),this.$nextTick((function(){t.finalTargetsCopy=window.structuredClone(t.finalTargets)}))}},getUnderlayStatus:function(){var e={loaded:!1};if(window.scene){var t=window.scene.features.get("underlay");t&&(e.loaded=!0,e.name=t.name,e.visible=t.visible)}return e},getCheckFinalTargetsResults:function(){var e=this,t=this.finalTargets.map((function(t){return e.getTargetIfValid(t)}));return t.length&&t.every((function(e){return!0===e}))},doAddOrUpdateAnimationGroupBusiness:function(){var e,t=this,n=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],i=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(this.isCreateNew){if(!i){if(e=this.animationGroupToAdd,!e||p.test(e))return void this.$message.warning(this.$t("animate.placeholder"));var o=this.$store.state.animation.animationGroups;if(o.some((function(t){return t.groupName===e})))return void this.$message.warning("该动画组已存在,请重新命名")}}else e=this.animationGroupNameToUpdate;if(this.getCheckFinalTargetsResults()){var r=[];this.finalTargets.forEach((function(n){var i=t.regenerateAnimation(n,!1);i&&(i.userData.belongToGroup=e,i.userData.isElementAnimationGroup=t.isElementAnimationGroup,i.userData.underlayStatus=t.getUnderlayStatus(),r.push({id:n.id,type:n.type,animationName:i.name,isAnimating:i.actived&&i.auto,loopType:i.loop}))})),this.$store.commit("addOrUpdateAnimationGroups",[{groupName:e,animationList:r,isSomeAnimating:r.length&&r.some((function(e){return!0===e.isAnimating})),targetsBakMap:this.finalTargetsBaksMap,isElementAnimationGroup:this.isElementAnimationGroup}]),this.isCreateNew?n&&this.$notify({title:"动画组:".concat(e),message:"添加成功",type:"success"}):n&&this.$notify({title:"动画组:".concat(e),message:"更新成功",type:"success"}),this.keyframeInEdit=null,this.onAddAnimationCancel(),this.hideAnimationTimeline()}i&&this.hideAnimationTimeline()},onAddAnimationCancel:function(){this.isModalAddAnimationVisible=!1,this.animationGroupToAdd=""},hideAnimationTimeline:function(){this.$store.commit("setIsTimeLineVisible",!1),this.$store.commit("setAnimationGroupNameInEdit",null)},confirmDoCloseBusiness:function(){var e=this;this.$confirm("关闭后,编辑的数据不被保存,是否确认关闭?","提示",{confirmButtonText:"确认关闭",cancelButtonText:"取消关闭",type:"warning"}).then((function(){e.doCloseTimelineBusiness()})).catch((function(){}))},doCloseTimelineBusiness:function(){var e,t,n=this,i=window.scene;this.finalTargets.forEach((function(e){n.recoverTargetById(e.id,e.type)})),t=null!==(e=this.finalTargetsCopy)&&void 0!==e&&e.length?this.finalTargets.filter((function(e){return n.finalTargetsCopy.some((function(t){return t.id!==e.id}))})):this.finalTargets,t.forEach((function(e){var t=n.getUniqueNameByTarget(e,!0);t&&(i.stop(t),i.removeAnimation(t),i.removeClip(t),n.recoverTargetById(e.id,e.type))})),this.isCreateNew||this.$store.commit("removeAnimationGroupRecover",[this.animationGroupFromVuex]),this.finalTargets=this.finalTargetsCopy,this.doAddOrUpdateAnimationGroupBusiness(!1,!0)},onOutlineMouseWheel:function(e){this.timeline&&(this.timeline.scrollTop=this.timeline._scrollContainer.scrollTop+e.deltaY)},doKeyframePropChangeBusiness:function(e,t,n){if(this.keyframeInEdit&&t){var i=this.keyframeInEdit.val,o=this.keyframeInEdit.extendInfo,r=o.subTrack,a=o.target,l=this.getTargetFromScene(a.id,a.type);if(l){var s=window.scene;switch(t){case"position":case"rotation":case"scale":var c=this.keyframeModel[t];if(p.test(e))return;var u=Number(e);if("rotation"===t?u%=360:"scale"===t&&(u<=0?(c.valueForView[n]=0,u=.1):c.valueForView[n]=u),c.value[n]=u,"position"===t){var d=l.position,f=l.offset,m=[d[0]-f[0],d[1]-f[1],d[2]-f[2]],v=c.value,h=[v[0]-m[0],v[1]-m[1],v[2]-m[2]];this.targetTimeOffsetRecord[a.id][i]=g(h),l.offset=g(h),"element"===a.type?r.extendInfo.timeValuesMap[i]=g(h):r.extendInfo.timeValuesMap[i]=g(c.value)}else r.extendInfo.timeValuesMap[i]=g(c.value),l[t]=g(c.value);window.scene.mv.tools.transform.syncTargetChange();break;case"visible":case"color":r.extendInfo.timeValuesMap[i]=e,"visible"===t?l[t]=e:s.execute("color",{objectIDs:[a.id],color:e,opacity:1});break}s.render()}}},getFormatTime:function(e){var t=Number(e);return isNaN(t)?"":"".concat(t/1e3," S")},toggleQueryPosition:function(){this.enableOrDisableQueryPosition(!this.isQueryPositionEnabled)},onAnimationBasePointRestore:function(){if(this.aniTargetInEdit){var e=this.aniTargetInEdit,t=e.id,n=(e.type,JSON.parse(this.finalTargetsBaksMap[t]));if(n){var i=Object(l["a"])(n.bpt,3),o=i[0],r=i[1],a=i[2];this.$set(this.finalTargetsBasePointMap,t,{x:o,y:r,z:a})}}},enableOrDisableQueryPosition:function(e){var t=window.scene.mv.events;e?(t.mousedown.on("default",this.updateAniTargetBasePointMap),this.isQueryPositionEnabled=!0,this.$message.warning(this.$t("messageTips.checkCoordinate"))):(t.mousedown.off("default",this.updateAniTargetBasePointMap),this.isQueryPositionEnabled=!1)},setObjectBpt:function(e,t){var n=window.scene.findObject(e);n&&null!==t&&void 0!==t&&t.length&&(n.bpt=t)},updateAniTargetBasePointMap:function(e){var t=window.scene,n=t.queryPosition(new t.mv._THREE.Vector2(e.clientX,e.clientY));n&&this.aniTargetInEdit.id&&this.$set(this.finalTargetsBasePointMap,this.aniTargetInEdit.id,{x:n.x,y:n.y,z:n.z})}}},b=_,k=(n("b2f1"),n("2877")),T=Object(k["a"])(b,r,a,!1,null,"6c7203c1",null),C=T.exports,w={name:"AnimationTimeLineWrapped",props:{position:{type:String,default:"fixed"},zIndex:{type:Number,default:4},width:{type:String,default:"100%"},height:{type:String,default:"200px"},top:{type:String,default:"auto"},right:{type:String,default:"0"},bottom:{type:String,default:"0"},left:{type:String,default:"0"}},data:function(){return{defaultStyleObj:{position:"fixed",zIndex:4,width:"100%",height:"200px",top:"auto",right:"0",bottom:"0",left:"0"}}},components:{AnimationTimeLine:C},computed:{posStyleObj:function(){return Object.assign({},this.defaultStyleObj,this.$props)},dragConfig:function(){return[{dragBorder:"top",setCssProperty:"height",min:200,max:550}]}},mounted:function(){this.setAnimationTimeLinePosWrapperSize()},methods:{setAnimationTimeLinePosWrapperSize:function(){var e=document.querySelector(".sceneManageDom");if(e){var t=e.style.left,n=document.querySelector(".animationtimeline-pos-wrapper");parseInt(t)<0?(n.style.left=0,n.style.width="100%"):(n.style.left="300px",n.style.width="calc(100% - 300px)")}}}},S=w,x=(n("5fa7"),Object(k["a"])(S,i,o,!1,null,"59ba3e58",null));t["default"]=x.exports},"5fa7":function(e,t,n){"use strict";n("b1e3")},"8fd1":function(e,t,n){"use strict";n("3af1")},"9be3":function(e,t,n){!function(t,n){e.exports=n()}(self,()=>(()=>{"use strict";var e={d:(t,n)=>{for(var i in n)e.o(n,i)&&!e.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:n[i]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,o(i.key),i)}}function o(e){var t=function(e,t){if("object"!==n(e)||null===e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var o=i.call(e,t);if("object"!==n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"===n(t)?t:String(t)}e.r(t),e.d(t,{Timeline:()=>He,TimelineCapShape:()=>Se,TimelineClickEvent:()=>me,TimelineCursorType:()=>we,TimelineDragEvent:()=>Ee,TimelineDraggableData:()=>M,TimelineElementType:()=>_,TimelineEventSource:()=>j,TimelineEvents:()=>Me,TimelineEventsEmitter:()=>a,TimelineInteractionMode:()=>Ce,TimelineKeyframeChangedEvent:()=>G,TimelineKeyframeShape:()=>r,TimelineScrollSource:()=>Pe,TimelineSelectedEvent:()=>ie,TimelineSelectionMode:()=>xe,TimelineStyleUtils:()=>k,TimelineTimeChangedEvent:()=>q,TimelineUtils:()=>f,defaultGroupStyle:()=>p,defaultRowHeight:()=>m,defaultTimelineConsts:()=>Ie,defaultTimelineKeyframeStyle:()=>v,defaultTimelineOptions:()=>Oe,defaultTimelineRowStyle:()=>h,defaultTimelineStyle:()=>Ae});var r,a=function(){function e(){var t,n,i;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),t=this,i=[],(n=o(n="_subscriptions"))in t?Object.defineProperty(t,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[n]=i}var t,n;return t=e,(n=[{key:"on",value:function(e,t){return!!t&&(this._subscriptions.push({topic:e,callback:t}),!0)}},{key:"off",value:function(e,t){var n=this._subscriptions.length;return this._subscriptions=this._subscriptions.filter((function(n){return n&&n.callback!=t&&n.topic!=e})),n!==this._subscriptions.length}},{key:"offAll",value:function(){this._subscriptions.length=0}},{key:"emit",value:function(e,t){this._subscriptions.forEach((function(n){(null==n?void 0:n.topic)===e&&null!=n&&n.callback&&n.callback(t)}))}}])&&i(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),e}();function l(e){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},l(e)}function s(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,u(i.key),i)}}function c(e,t,n){return(t=u(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function u(e){var t=function(e,t){if("object"!==l(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t);if("object"!==l(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"===l(t)?t:String(t)}!function(e){e.None="none",e.Rhomb="rhomb",e.Circle="circle",e.Rect="rect"}(r||(r={}));var d=[1,2,5,10],f=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}var t,n;return t=e,n=[{key:"drawLine",value:function(e,t,n,i,o){e.moveTo(t,n),e.lineTo(i,o)}},{key:"isNumber",value:function(e){return!("number"!=typeof e||!e&&0!==e||isNaN(e)||!Number.isFinite(e))}},{key:"deleteElement",value:function(e,t){var n=e.indexOf(t);return-1!==n?e.splice(n,1):e}},{key:"isOverlap",value:function(e,t,n){return!!n&&n.x<=e&&n.x+n.width>=e&&n.y<=t&&n.y+n.height>=t}},{key:"findGoodStep",value:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:d;if(i||(i=d),t<=0||isNaN(t)||!Number.isFinite(t))return t;for(var o=t,r=null,a=e.getPowArgument(t),l=0;l<i.length;l++){var s=i[l],c=s*Math.pow(10,a);if(!n||n%c==0){var u=e.getDistance(t,c);if(0==u||u<=.1&&a>0){r=u,o=c;break}(!r||r>u)&&(r=u,o=c)}}return o}},{key:"keepInBounds",value:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;return e.isNumber(t)&&(e.isNumber(n)&&(n||0===n)&&(t=Math.max(t,n)),e.isNumber(i)&&(i||0===i)&&(t=Math.min(t,i))),t}},{key:"setMinMax",value:function(t,n){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(!n||!t)return t;var o=n?n.min:Number.NaN,r=t.min,a=(o||0===o)&&e.isNumber(o),l=(r||0===r)&&e.isNumber(r);a&&l?t.min=i?Math.min(o,r):Math.max(o,r):a&&(t.min=o);var s=n?n.max:Number.NaN,c=t.max,u=(s||0===s)&&e.isNumber(s),d=(c||0===c)&&e.isNumber(c);return u&&d?t.max=i?Math.max(s,c):Math.min(s,c):u&&(t.max=s),t}},{key:"shrinkSelf",value:function(e,t){return e?(e.x-=t,e.y-=t,e.width+=t,e.height+=t,e):e}},{key:"isRectIntersects",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(!e||!t)return console.log("Rectangles cannot be empty"),!1;var i=t.x+t.width,o=t.y+t.height;return n?e.x<=i&&t.x<=e.x+e.width&&e.y<=o&&t.y<=e.y+e.height:e.x<i&&t.x<e.x+e.width&&e.y<o&&t.y<e.y+e.height}},{key:"getDistance",value:function(e,t,n,i){return null!=n&&null!=i?Math.sqrt(Math.pow(e-n,2)+Math.pow(t-i,2)):Math.abs(e-t)}},{key:"sign",value:function(e){return e>=0?1:-1}},{key:"clearBrowserSelection",value:function(){if(window)if(window.getSelection){var e=window.getSelection();e&&e.removeAllRanges()}else{var t=window.document;t.selection&&t.selection.empty()}}},{key:"getPowArgument",value:function(e){if(!e||0===e||!isFinite(e))return 1;if(e>=10&&e<100)return 1;if(e>=100&&e<1e3)return 2;if(e>=1e3&&e<1e4)return 3;e=Math.abs(e);var t=0,n=this.sign(e);if(e>1){for(;e>=1;)e=Math.floor(e/10),t++;return n*t-1}return e>0?Math.floor(Math.log(e)/Math.log(10)+1)-1:1}},{key:"mergeOptions",value:function(t,n){n=n||{};var i=e.cloneOptions(t);return function e(t,n){if(t&&n)for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&void 0!==n[i]&&("object"===l(n[i])?"id"===i?n[i]&&n[i]!==t[i]&&(t[i]=n[i]):t[i]?e(t[i],n[i]):t[i]=n[i]:t[i]=n[i])}(i,n),i}},{key:"timePadZero",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2,n=String(e),i=Math.pow(10,t-1);if(e<i)for(;String(i).length>n.length;)n="0".concat(e);return n}}],n&&s(t,n),Object.defineProperty(t,"prototype",{writable:!1}),e}();c(f,"deepClone",(function(e){return JSON.parse(JSON.stringify(e))})),c(f,"cloneOptions",(function(e){var t=JSON.parse(JSON.stringify(e,(function(e,t){return"id"===e?void 0:t})));return t.id=e.id,t}));var m=24,p={height:"auto",marginTop:4,fillColor:"#094771"},v={fillColor:"DarkOrange",shape:r.Rhomb,selectedFillColor:"red",strokeColor:"black",selectedStrokeColor:"black",strokeThickness:.2,height:"auto",width:"auto"},h={height:m,marginBottom:2,fillColor:"#252526",keyframesStyle:v,groupsStyle:p};function y(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,(o=function(e,t){if("object"!==g(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t);if("object"!==g(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(i.key,"string"),"symbol"===g(o)?o:String(o)),i)}var o}function g(e){return g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},g(e)}var _,b="undefined",k=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}var t,n;return t=e,n=[{key:"getGroup",value:function(e){return e&&"string"==typeof e?null:e||null}},{key:"getGroupStyle",value:function(t){var n;return(null===(n=e.getGroup(t))||void 0===n?void 0:n.style)||null}},{key:"getFirstSet",value:function(t){for(var n=arguments.length,i=new Array(n>1?n-1:0),o=1;o<n;o++)i[o-1]=arguments[o];return e.getValue.apply(e,[t,!1].concat(i))}},{key:"getValue",value:function(t){for(var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=[],o=!1,r=arguments.length,a=new Array(r>2?r-2:0),l=2;l<r;l++)a[l-2]=arguments[l];a.forEach((function(e){if(!o&&g(e)!==b){if("number"==typeof e){if(!f.isNumber(e))return}else{if("boolean"==typeof e)return i.push(e),void(n&&!1===e&&(o=!0));if(!e)return}i.push(e)}}));var s=i&&i.length>0?i[0]:t;return!o&&e.getValueOrDefault(s,t)}},{key:"getValueOrDefault",value:function(e,t){return g(e)===b?t:"boolean"==typeof e?e:"number"==typeof e?e||0===e?e:t:e||t}},{key:"keyframeWidth",value:function(t,n,i,o){var r,a,l,s,c,u,d,f,m,p,h;return e.getFirstSet(v.width||"",null==t||null===(r=t.style)||void 0===r?void 0:r.width,null===(a=e.getGroupStyle(n))||void 0===a||null===(l=a.keyframesStyle)||void 0===l?void 0:l.width,null==i||null===(s=i.keyframesStyle)||void 0===s?void 0:s.width,null==i||null===(c=i.groupsStyle)||void 0===c||null===(u=c.keyframesStyle)||void 0===u?void 0:u.width,null==o||null===(d=o.rowsStyle)||void 0===d||null===(f=d.groupsStyle)||void 0===f||null===(m=f.keyframesStyle)||void 0===m?void 0:m.width,null==o||null===(p=o.rowsStyle)||void 0===p||null===(h=p.keyframesStyle)||void 0===h?void 0:h.width)}},{key:"keyframeHeight",value:function(t,n,i,o){var r,a,l,s,c,u,d,f,m,p,h;return e.getFirstSet(v.height||"",null==t||null===(r=t.style)||void 0===r?void 0:r.height,null===(a=e.getGroupStyle(n))||void 0===a||null===(l=a.keyframesStyle)||void 0===l?void 0:l.height,null==i||null===(s=i.keyframesStyle)||void 0===s?void 0:s.height,null==i||null===(c=i.groupsStyle)||void 0===c||null===(u=c.keyframesStyle)||void 0===u?void 0:u.height,null==o||null===(d=o.rowsStyle)||void 0===d||null===(f=d.groupsStyle)||void 0===f||null===(m=f.keyframesStyle)||void 0===m?void 0:m.height,null==o||null===(p=o.rowsStyle)||void 0===p||null===(h=p.keyframesStyle)||void 0===h?void 0:h.height)}},{key:"keyframeShape",value:function(t,n,i,o){var a,l,s,c,u,d,f,m,p,h,y,g=v.shape||r.Rhomb;return e.getFirstSet(g,null==t||null===(a=t.style)||void 0===a?void 0:a.shape,null===(l=e.getGroupStyle(n))||void 0===l||null===(s=l.keyframesStyle)||void 0===s?void 0:s.shape,null==i||null===(c=i.groupsStyle)||void 0===c||null===(u=c.keyframesStyle)||void 0===u?void 0:u.shape,null==i||null===(d=i.keyframesStyle)||void 0===d?void 0:d.shape,n?null==o||null===(f=o.rowsStyle)||void 0===f||null===(m=f.groupsStyle)||void 0===m||null===(p=m.keyframesStyle)||void 0===p?void 0:p.shape:void 0,null==o||null===(h=o.rowsStyle)||void 0===h||null===(y=h.keyframesStyle)||void 0===y?void 0:y.shape)}},{key:"keyframeFillColor",value:function(t,n,i,o){var r,a,l,s,c,u,d,f,m,p,h,y=v.fillColor||"";return e.getFirstSet(y,null==t||null===(r=t.style)||void 0===r?void 0:r.fillColor,null===(a=e.getGroupStyle(n))||void 0===a||null===(l=a.keyframesStyle)||void 0===l?void 0:l.fillColor,null==i||null===(s=i.groupsStyle)||void 0===s||null===(c=s.keyframesStyle)||void 0===c?void 0:c.fillColor,null==i||null===(u=i.keyframesStyle)||void 0===u?void 0:u.fillColor,n?null==o||null===(d=o.rowsStyle)||void 0===d||null===(f=d.groupsStyle)||void 0===f||null===(m=f.keyframesStyle)||void 0===m?void 0:m.fillColor:void 0,null==o||null===(p=o.rowsStyle)||void 0===p||null===(h=p.keyframesStyle)||void 0===h?void 0:h.fillColor)}},{key:"keyframeSelectedFillColor",value:function(t,n,i,o){var r,a,l,s,c,u,d,f,m,p,h,y=v.selectedFillColor||"";return e.getFirstSet(y,null==t||null===(r=t.style)||void 0===r?void 0:r.selectedFillColor,null===(a=e.getGroupStyle(n))||void 0===a||null===(l=a.keyframesStyle)||void 0===l?void 0:l.selectedFillColor,null==i||null===(s=i.groupsStyle)||void 0===s||null===(c=s.keyframesStyle)||void 0===c?void 0:c.selectedFillColor,null==i||null===(u=i.keyframesStyle)||void 0===u?void 0:u.selectedFillColor,n?null==o||null===(d=o.rowsStyle)||void 0===d||null===(f=d.groupsStyle)||void 0===f||null===(m=f.keyframesStyle)||void 0===m?void 0:m.selectedFillColor:void 0,null==o||null===(p=o.rowsStyle)||void 0===p||null===(h=p.keyframesStyle)||void 0===h?void 0:h.selectedFillColor)}},{key:"keyframeStrokeThickness",value:function(t,n,i,o){var r,a,l,s,c,u,d,f,m,p,h,y=v.strokeThickness||0;return e.getFirstSet(y,null==t||null===(r=t.style)||void 0===r?void 0:r.strokeThickness,null===(a=e.getGroupStyle(n))||void 0===a||null===(l=a.keyframesStyle)||void 0===l?void 0:l.strokeThickness,null==i||null===(s=i.groupsStyle)||void 0===s||null===(c=s.keyframesStyle)||void 0===c?void 0:c.strokeThickness,null==i||null===(u=i.keyframesStyle)||void 0===u?void 0:u.strokeThickness,n?null==o||null===(d=o.rowsStyle)||void 0===d||null===(f=d.groupsStyle)||void 0===f||null===(m=f.keyframesStyle)||void 0===m?void 0:m.strokeThickness:void 0,null==o||null===(p=o.rowsStyle)||void 0===p||null===(h=p.keyframesStyle)||void 0===h?void 0:h.strokeThickness)}},{key:"keyframeStrokeColor",value:function(t,n,i,o){var r,a,l,s,c,u,d,f,m,p,h,y=v.strokeColor||"";return e.getFirstSet(y,null==t||null===(r=t.style)||void 0===r?void 0:r.strokeColor,null===(a=e.getGroupStyle(n))||void 0===a||null===(l=a.keyframesStyle)||void 0===l?void 0:l.strokeColor,null==i||null===(s=i.groupsStyle)||void 0===s||null===(c=s.keyframesStyle)||void 0===c?void 0:c.strokeColor,null==i||null===(u=i.keyframesStyle)||void 0===u?void 0:u.strokeColor,n?null==o||null===(d=o.rowsStyle)||void 0===d||null===(f=d.groupsStyle)||void 0===f||null===(m=f.keyframesStyle)||void 0===m?void 0:m.strokeColor:void 0,null==o||null===(p=o.rowsStyle)||void 0===p||null===(h=p.keyframesStyle)||void 0===h?void 0:h.strokeColor)}},{key:"keyframeSelectedStrokeColor",value:function(t,n,i,o){var r,a,l,s,c,u,d,f,m,p,h,y=v.selectedStrokeColor||"";return e.getFirstSet(y,null==t||null===(r=t.style)||void 0===r?void 0:r.selectedStrokeColor,null===(a=e.getGroupStyle(n))||void 0===a||null===(l=a.keyframesStyle)||void 0===l?void 0:l.selectedStrokeColor,null==i||null===(s=i.groupsStyle)||void 0===s||null===(c=s.keyframesStyle)||void 0===c?void 0:c.selectedStrokeColor,null==i||null===(u=i.keyframesStyle)||void 0===u?void 0:u.selectedStrokeColor,n?null==o||null===(d=o.rowsStyle)||void 0===d||null===(f=d.groupsStyle)||void 0===f||null===(m=f.keyframesStyle)||void 0===m?void 0:m.selectedStrokeColor:void 0,null==o||null===(p=o.rowsStyle)||void 0===p||null===(h=p.keyframesStyle)||void 0===h?void 0:h.selectedStrokeColor)}},{key:"groupHeight",value:function(t,n,i){var o,r,a,l;return e.getFirstSet(p.height||"auto",null===(o=e.getGroupStyle(n))||void 0===o?void 0:o.height,null==i||null===(r=i.groupsStyle)||void 0===r?void 0:r.height,null==t||null===(a=t.rowsStyle)||void 0===a||null===(l=a.groupsStyle)||void 0===l?void 0:l.height)}},{key:"groupMarginTop",value:function(t,n,i){var o,r,a,l;return e.getFirstSet(p.marginTop||"auto",null===(o=e.getGroupStyle(n))||void 0===o?void 0:o.marginTop,null==i||null===(r=i.groupsStyle)||void 0===r?void 0:r.marginTop,null==t||null===(a=t.rowsStyle)||void 0===a||null===(l=a.groupsStyle)||void 0===l?void 0:l.marginTop)}},{key:"groupFillColor",value:function(t,n,i){var o,r,a,l;return e.getFirstSet(p.fillColor||"",null===(o=e.getGroupStyle(n))||void 0===o?void 0:o.fillColor,null==i||null===(r=i.groupsStyle)||void 0===r?void 0:r.fillColor,null==t||null===(a=t.rowsStyle)||void 0===a||null===(l=a.groupsStyle)||void 0===l?void 0:l.fillColor)}},{key:"groupStrokeColor",value:function(t,n,i){var o,r,a,l;return e.getFirstSet(p.strokeColor||"",null===(o=e.getGroupStyle(n))||void 0===o?void 0:o.strokeColor,null==i||null===(r=i.groupsStyle)||void 0===r?void 0:r.strokeColor,null==t||null===(a=t.rowsStyle)||void 0===a||null===(l=a.groupsStyle)||void 0===l?void 0:l.strokeColor)}},{key:"groupStrokeThickness",value:function(t,n,i){var o,r,a,l;return e.getFirstSet(p.strokeThickness||"",null===(o=e.getGroupStyle(n))||void 0===o?void 0:o.strokeThickness,null==i||null===(r=i.groupsStyle)||void 0===r?void 0:r.strokeThickness,null==t||null===(a=t.rowsStyle)||void 0===a||null===(l=a.groupsStyle)||void 0===l?void 0:l.strokeThickness)||0}},{key:"groupsRadii",value:function(t,n,i){var o,r,a,l;return e.getFirstSet(p.radii||"",null===(o=e.getGroupStyle(n))||void 0===o?void 0:o.radii,null==i||null===(r=i.groupsStyle)||void 0===r?void 0:r.radii,null==t||null===(a=t.rowsStyle)||void 0===a||null===(l=a.groupsStyle)||void 0===l?void 0:l.radii)||0}},{key:"getRowHeight",value:function(t,n){var i,o=h.height||0;return e.getFirstSet(o,null==t?void 0:t.height,null==n||null===(i=n.rowsStyle)||void 0===i?void 0:i.height)}},{key:"getRowMarginBottom",value:function(t,n){var i,o=h.marginBottom||0;return e.getFirstSet(o,null==t?void 0:t.marginBottom,null==n||null===(i=n.rowsStyle)||void 0===i?void 0:i.marginBottom)}},{key:"getRowFillColor",value:function(t,n){var i,o=h.fillColor||"";return e.getFirstSet(o,null==t?void 0:t.fillColor,null==n||null===(i=n.rowsStyle)||void 0===i?void 0:i.fillColor)}},{key:"headerHeight",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:30;return(null==e?void 0:e.headerHeight)||t}},{key:"keyframeDraggable",value:function(t,n,i,o){var r,a=!(arguments.length>4&&void 0!==arguments[4])||arguments[4],l=!0,s=e.getValue(a,l,null==t?void 0:t.draggable,null===(r=e.getGroup(n))||void 0===r?void 0:r.keyframesDraggable,null==i?void 0:i.keyframesDraggable,null==o?void 0:o.keyframesDraggable);return s}},{key:"groupDraggable",value:function(t,n,i){var o;return e.getValue(!0,!0,null===(o=e.getGroup(t))||void 0===o?void 0:o.draggable,null==n?void 0:n.groupsDraggable,null==i?void 0:i.groupsDraggable)}}],n&&y(t,n),Object.defineProperty(t,"prototype",{writable:!1}),e}();function T(e){return T="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},T(e)}function C(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,x(i.key),i)}}function w(e,t,n){return t&&C(e.prototype,t),n&&C(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function S(e,t,n){return(t=x(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function x(e){var t=function(e,t){if("object"!==T(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t);if("object"!==T(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"===T(t)?t:String(t)}!function(e){e.Timeline="timeline",e.Keyframe="keyframe",e.Group="group",e.Row="row",e.None="none"}(_||(_={}));var M=w((function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),S(this,"changed",!1),S(this,"target",void 0),S(this,"elements",void 0),S(this,"type",_.None),S(this,"val",void 0),S(this,"prevVal",void 0)}));function P(e){return P="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},P(e)}function E(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,I(i.key),i)}}function I(e){var t=function(e,t){if("object"!==P(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t);if("object"!==P(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"===P(t)?t:String(t)}var A=function(){function e(){var t,n;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),t=this,(n=I(n="_prevented"))in t?Object.defineProperty(t,n,{value:!1,enumerable:!0,configurable:!0,writable:!0}):t[n]=!1}var t,n;return t=e,(n=[{key:"preventDefault",value:function(){this._prevented=!0}},{key:"isPrevented",value:function(){return this._prevented}}])&&E(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),e}();function O(e){return O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},O(e)}function F(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function V(e,t){return V=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},V(e,t)}function B(e,t){if(t&&("object"===O(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return D(e)}function D(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function R(e){return R=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},R(e)}function N(e,t,n){return(t=z(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function z(e){var t=function(e,t){if("object"!==O(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t);if("object"!==O(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"===O(t)?t:String(t)}var j,G=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&V(e,t)}(r,e);var t,n,i,o=(n=r,i=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=R(n);if(i){var o=R(this).constructor;e=Reflect.construct(t,arguments,o)}else e=t.apply(this,arguments);return B(this,e)});function r(){var e;F(this,r);for(var t=arguments.length,n=new Array(t),i=0;i<t;i++)n[i]=arguments[i];return N(D(e=o.call.apply(o,[this].concat(n))),"val",void 0),N(D(e),"prevVal",void 0),N(D(e),"target",void 0),N(D(e),"source",void 0),e}return t=r,Object.defineProperty(t,"prototype",{writable:!1}),t}(A);function K(e){return K="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},K(e)}function $(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function W(e,t){return W=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},W(e,t)}function L(e,t){if(t&&("object"===K(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return H(e)}function H(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function U(e){return U=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},U(e)}function Z(e,t,n){return(t=J(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function J(e){var t=function(e,t){if("object"!==K(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t);if("object"!==K(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"===K(t)?t:String(t)}!function(e){e.User="user",e.Programmatically="programmatically",e.SetTimeMethod="setTimeMethod"}(j||(j={}));var Y,q=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&W(e,t)}(r,e);var t,n,i,o=(n=r,i=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=U(n);if(i){var o=U(this).constructor;e=Reflect.construct(t,arguments,o)}else e=t.apply(this,arguments);return L(this,e)});function r(){var e;$(this,r);for(var t=arguments.length,n=new Array(t),i=0;i<t;i++)n[i]=arguments[i];return Z(H(e=o.call.apply(o,[this].concat(n))),"val",0),Z(H(e),"prevVal",0),Z(H(e),"source",j.User),e}return t=r,Object.defineProperty(t,"prototype",{writable:!1}),t}(A);function X(e){return X="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},X(e)}function Q(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,ne(i.key),i)}}function ee(e,t,n){return t&&Q(e.prototype,t),n&&Q(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function te(e,t,n){return(t=ne(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ne(e){var t=function(e,t){if("object"!==X(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t);if("object"!==X(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"===X(t)?t:String(t)}!function(e){e.Keyframes="keyframes"}(Y||(Y={}));var ie=ee((function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),te(this,"selected",[]),te(this,"changed",[]),te(this,"mode",Y.Keyframes)}));function oe(e){return oe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},oe(e)}function re(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function ae(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,fe(i.key),i)}}function le(e,t){return le=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},le(e,t)}function se(e,t){if(t&&("object"===oe(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return ce(e)}function ce(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ue(e){return ue=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},ue(e)}function de(e,t,n){return(t=fe(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function fe(e){var t=function(e,t){if("object"!==oe(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t);if("object"!==oe(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"===oe(t)?t:String(t)}var me=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&le(e,t)}(a,e);var t,n,i,o,r=(i=a,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=ue(i);if(o){var n=ue(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return se(this,e)});function a(){var e;re(this,a);for(var t=arguments.length,n=new Array(t),i=0;i<t;i++)n[i]=arguments[i];return de(ce(e=r.call.apply(r,[this].concat(n))),"args",null),de(ce(e),"elements",[]),de(ce(e),"target",null),de(ce(e),"point",null),e}return t=a,(n=[{key:"pos",get:function(){var e;return(null===(e=this.point)||void 0===e?void 0:e.pos)||null}},{key:"val",get:function(){return this.point?this.point.val:NaN}}])&&ae(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),a}(A);function pe(e){return pe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},pe(e)}function ve(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function he(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,Te(i.key),i)}}function ye(e,t){return ye=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},ye(e,t)}function ge(e,t){if(t&&("object"===pe(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return _e(e)}function _e(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function be(e){return be=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},be(e)}function ke(e,t,n){return(t=Te(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Te(e){var t=function(e,t){if("object"!==pe(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t);if("object"!==pe(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"===pe(t)?t:String(t)}var Ce,we,Se,xe,Me,Pe,Ee=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ye(e,t)}(a,e);var t,n,i,o,r=(i=a,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=be(i);if(o){var n=be(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return ge(this,e)});function a(){var e;ve(this,a);for(var t=arguments.length,n=new Array(t),i=0;i<t;i++)n[i]=arguments[i];return ke(_e(e=r.call.apply(r,[this].concat(n))),"args",null),ke(_e(e),"elements",null),ke(_e(e),"target",null),ke(_e(e),"point",null),ke(_e(e),"prevPoint",null),e}return t=a,(n=[{key:"pos",get:function(){var e;return(null===(e=this.point)||void 0===e?void 0:e.pos)||null}}])&&he(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),a}(A);!function(e){e.Selection="selection",e.Pan="pan",e.NonInteractivePan="nonInteractivePan",e.Zoom="zoom",e.None="none"}(Ce||(Ce={})),function(e){e.Alias="alias",e.AllScroll="all-scroll",e.Auto="auto",e.Cell="cell",e.ContextMenu="context-menu",e.ColResize="col-resize",e.Copy="copy",e.Crosshair="crosshair",e.Default="default",e.EResize="e-resize",e.EWResize="ew-resize",e.Grab="grab",e.Grabbing="grabbing",e.Help="help",e.Move="move",e.NResize="n-resize",e.NEResize="ne-resize",e.NESWResize="nesw-resize",e.NSResize="ns-resize",e.NWResize="nw-resize",e.NWSEResize="nwse-resize",e.NoDrop="no-drop",e.None="none",e.NotAllowed="not-allowed",e.Pointer="pointer",e.Progress="progress",e.RowResize="row-resize",e.SResize="s-resize",e.SEResize="se-resize",e.SWResize="sw-resize",e.Text="text",e.WResize="w-resize",e.Wait="wait",e.ZoomIn="zoom-in",e.ZoomOut="zoom-out"}(we||(we={})),function(e){e.None="none",e.Triangle="triangle",e.Rect="rect"}(Se||(Se={})),function(e){e.Normal="normal",e.Append="append",e.Revert="revert"}(xe||(xe={})),function(e){e.Selected="selected",e.TimeChanged="timechanged",e.KeyframeChanged="keyframeChanged",e.DragStarted="dragStarted",e.Drag="drag",e.DragFinished="dragFinished",e.Scroll="scroll",e.ScrollFinished="scrollFinished",e.ContextMenu="onContextMenu",e.DoubleClick="doubleClick",e.MouseDown="mouseDown"}(Me||(Me={})),function(e){e.DefaultMode="none",e.ZoomMode="zoom",e.ScrollBySelection="scrollBySelection"}(Pe||(Pe={}));var Ie={autoPanSpeed:50,scrollByDragSpeed:.12,clickDetectionMs:120,doubleClickTimeoutMs:400,scrollFinishedTimeoutMs:500,autoPanByScrollPadding:10,clickThreshold:3,clickDetectionMinRadius:2,autoPanSpeedLimit:10,defaultGroupHeight:.7},Ae={width:2,marginTop:15,marginBottom:0,strokeColor:"DarkOrange",fillColor:"DarkOrange",capStyle:{width:4,height:10,capType:Se.Rect,strokeColor:"DarkOrange",fillColor:"DarkOrange"},cursor:we.EWResize},Oe={snapAllKeyframesOnMove:!1,snapEnabled:!0,timelineStyle:Ae,stepPx:120,stepVal:1e3,stepSmallPx:30,snapStep:200,leftMargin:25,headerFillColor:"#101011",fillColor:"#101011",labelsColor:"#D5D5D5",tickColor:"#D5D5D5",selectionColor:"White",rowsStyle:h,keyframesStyle:v,groupsStyle:p,headerHeight:30,font:"11px sans-serif",zoom:1,zoomSpeed:.1,zoomMin:.1,zoomMax:8,controlKeyIsMetaKey:!1,scrollContainerClass:"scroll-container",groupsDraggable:!0,keyframesDraggable:!0,timelineDraggable:!0,min:0,max:Number.MAX_VALUE};function Fe(e){return Fe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Fe(e)}function Ve(e){return function(e){if(Array.isArray(e))return Be(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return Be(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Be(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Be(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=new Array(t);n<t;n++)i[n]=e[n];return i}function De(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Re(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,Le(i.key),i)}}function Ne(e,t){return Ne=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Ne(e,t)}function ze(e,t){if(t&&("object"===Fe(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return je(e)}function je(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Ge(){return Ge="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var i=Ke(e,t);if(i){var o=Object.getOwnPropertyDescriptor(i,t);return o.get?o.get.call(arguments.length<3?e:n):o.value}},Ge.apply(this,arguments)}function Ke(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=$e(e)););return e}function $e(e){return $e=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},$e(e)}function We(e,t,n){return(t=Le(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Le(e){var t=function(e,t){if("object"!==Fe(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t);if("object"!==Fe(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"===Fe(t)?t:String(t)}var He=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ne(e,t)}(l,e);var t,n,i,o,a=(i=l,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=$e(i);if(o){var n=$e(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return ze(this,e)});function l(){var e,t,n,i,o,s=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,c=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return De(this,l),We(je(o=a.call(this)),"_container",null),We(je(o),"_canvas",null),We(je(o),"_scrollContainer",null),We(je(o),"_scrollContent",null),We(je(o),"_ctx",null),We(je(o),"_options",void 0),We(je(o),"_startPosMouseArgs",null),We(je(o),"_scrollStartPos",null),We(je(o),"_currentPos",null),We(je(o),"_selectionRect",null),We(je(o),"_selectionRectEnabled",!1),We(je(o),"_drag",null),We(je(o),"_startedDragWithCtrl",!1),We(je(o),"_startedDragWithShiftKey",!1),We(je(o),"_scrollProgrammatically",!1),We(je(o),"_clickTimeout",null),We(je(o),"_lastClickTime",0),We(je(o),"_lastClickPoint",null),We(je(o),"_consts",Ie),We(je(o),"_clickAllowed",!1),We(je(o),"_scrollFinishedTimerRef",null),We(je(o),"_val",0),We(je(o),"_pixelRatio",1),We(je(o),"_currentZoom",0),We(je(o),"_intervalRef",null),We(je(o),"_autoPanLastActionDate",0),We(je(o),"_isPanStarted",!1),We(je(o),"_interactionMode",Ce.Selection),We(je(o),"_lastUsedArgs",null),We(je(o),"_model",null),We(je(o),"_scrollAreaClickOrDragStarted",!1),We(je(o),"initialize",(function(e,t){if(o._model=t,!e||!e.id)throw new Error("Element cannot be empty. Should be string or DOM element.");o._generateContainers(e.id),o._options=f.cloneOptions(Oe),e&&(o._options=o._setOptions(e)),o._subscribeComponentEvents(),o.rescale(),o.redraw()})),We(je(o),"_generateContainers",(function(e){if(e instanceof HTMLElement?o._container=e:o._container=document.getElementById(e),!o._container)throw new Error("Element cannot be empty. Should be string or DOM element.");if(o._scrollContainer=document.createElement("div"),o._scrollContent=document.createElement("div"),o._canvas=document.createElement("canvas"),o._canvas&&o._canvas.getContext){o._container.innerHTML="",o._container.style.position="relative",o._canvas.style.cssText="image-rendering: -moz-crisp-edges;image-rendering: -webkit-crisp-edges;image-rendering: pixelated;image-rendering: crisp-edges;user-select: none;-webkit-user-select: none;-khtml-user-select: none;-moz-user-select: none;-o-user-select: none;user-select: none;touch-action: none;position: relative;-webkit-user-drag: none;-khtml-user-drag: none;-moz-user-drag: none;-o-user-drag: none;user-drag: none;padding: inherit",o._scrollContainer.style.cssText="overflow: scroll;position: absolute;width:  100%;height:  100%;",o._scrollContent.style.width=o._scrollContent.style.height="100%",o._scrollContainer.appendChild(o._scrollContent),o._container.appendChild(o._scrollContainer);var t=o._scrollContainer.offsetWidth-o._scrollContent.clientWidth;o._canvas.style.width=o._canvas.style.height="calc(100% -"+(t||17)+"px)",o._container.appendChild(o._canvas),o._ctx=o._getCtx()}else console.log("Cannot initialize canvas context.")})),We(je(o),"_subscribeComponentEvents",(function(){if(o._unsubscribeComponentEvents(),!o._container||!o._scrollContainer||!o._canvas)throw Error("Cannot subscribe on scroll events while one of the containers is null or empty. Please call initialize method first");o._container&&o._container.addEventListener("wheel",o._handleWheelEvent),o._scrollContainer&&(o._scrollContainer.addEventListener("scroll",o._handleScrollEvent),o._scrollContainer.addEventListener("touchstart",o._handleScrollMouseDownEvent),o._scrollContainer.addEventListener("mousedown",o._handleScrollMouseDownEvent)),document.addEventListener("keyup",o._handleKeyUp,!1),document.addEventListener("keydown",o._handleKeyDown,!1),window.addEventListener("blur",o._handleBlurEvent,!1),window.addEventListener("resize",o._handleWindowResizeEvent,!1),o._canvas&&(o._canvas.addEventListener("touchstart",o._handleMouseDownEvent,!1),o._canvas.addEventListener("mousedown",o._handleMouseDownEvent,!1),o._canvas.addEventListener("contextmenu",o._handleContextMenu,!1)),window.addEventListener("mousemove",o._handleMouseMoveEvent,!1),window.addEventListener("touchmove",o._handleMouseMoveEvent,!1),window.addEventListener("mouseup",o._handleMouseUpEvent,!1),window.addEventListener("touchend",o._handleMouseUpEvent,!1)})),We(je(o),"_unsubscribeComponentEvents",(function(){var e;null===(e=o._container)||void 0===e||e.removeEventListener("wheel",o._handleWheelEvent),o._scrollContainer?(o._scrollContainer.removeEventListener("scroll",o._handleScrollEvent),o._scrollContainer.removeEventListener("touchstart",o._handleScrollMouseDownEvent),o._scrollContainer.removeEventListener("mousedown",o._handleScrollMouseDownEvent)):console.warn("Cannot unsubscribe scroll while it's already empty"),window.removeEventListener("blur",o._handleBlurEvent),window.removeEventListener("resize",o._handleWindowResizeEvent),document.removeEventListener("keydown",o._handleKeyDown),document.removeEventListener("keyup",o._handleKeyUp),o._canvas?(o._canvas.removeEventListener("touchstart",o._handleMouseDownEvent),o._canvas.removeEventListener("mousedown",o._handleMouseDownEvent),o._canvas.removeEventListener("contextmenu",o._handleContextMenu)):console.warn("Cannot unsubscribe canvas while it's already empty"),window.removeEventListener("mousemove",o._handleMouseMoveEvent),window.removeEventListener("touchmove",o._handleMouseMoveEvent),window.removeEventListener("mouseup",o._handleMouseUpEvent),window.removeEventListener("touchend",o._handleMouseUpEvent)})),We(je(o),"dispose",(function(){o.offAll(),o._stopAutoPan(),o._clearScrollFinishedTimer(),o._unsubscribeComponentEvents(),o._container&&(o._container.innerHTML=""),o._container=null,o._canvas=null,o._scrollContainer=null,o._scrollContent=null,o._ctx=null,o._cleanUpSelection()})),We(je(o),"_handleKeyUp",(function(e){o._interactionMode===Ce.Zoom&&o._setZoomCursor(e)})),We(je(o),"_handleKeyDown",(function(e){o._interactionMode===Ce.Zoom&&o._setZoomCursor(e)})),We(je(o),"_setZoomCursor",(function(e){o._controlKeyPressed(e)?o._setCursor(we.ZoomOut):o._setCursor(we.ZoomIn)})),We(je(o),"_handleBlurEvent",(function(){o._cleanUpSelection(!0)})),We(je(o),"_handleWindowResizeEvent",(function(){o.rescale(),o.redraw()})),We(je(o),"_clearScrollFinishedTimer",(function(){o._scrollFinishedTimerRef&&(clearTimeout(o._scrollFinishedTimerRef),o._scrollFinishedTimerRef=null)})),We(je(o),"_handleScrollMouseDownEvent",(function(){o._scrollAreaClickOrDragStarted=!0})),We(je(o),"_handleScrollEvent",(function(e){var t=o._scrollProgrammatically;o._scrollProgrammatically&&(o._scrollProgrammatically=!1),o._clearScrollFinishedTimer(),o._scrollFinishedTimerRef=window.setTimeout((function(){o._isPanStarted||(o._clearScrollFinishedTimer(),o.rescale(),o.redraw()),o._emitScrollEvent(e,t,Me.ScrollFinished)}),o._consts.scrollFinishedTimeoutMs),o.redraw(),o._emitScrollEvent(e,t)})),We(je(o),"_controlKeyPressed",(function(e){return o._options&&void 0!==o._options.controlKeyIsMetaKey?o._options.controlKeyIsMetaKey||o._options.controlKeyIsMetaKey?e.metaKey:e.ctrlKey:e.metaKey||e.ctrlKey})),We(je(o),"_handleWheelEvent",(function(e){if(o._scrollContainer&&o._canvas)if(o._controlKeyPressed(e)){e.preventDefault();var t=o._getMousePos(o._canvas,e),n=Math.max(0,t.pos.x||0);o._zoom(f.sign(e.deltaY),o._options.zoomSpeed||0,n)}else o.scrollTop=o._scrollContainer.scrollTop+e.deltaY,e.preventDefault()})),We(je(o),"_zoom",(function(e,t,n){if(t&&t>0&&t<=1){n+=.2*f.getDistance(o._canvasClientWidth()/2,n);var i=o._canvasClientWidth()/n,r=o._fromScreen(n),a=e*o._currentZoom*t;o._currentZoom=o._setZoom(o._currentZoom+a);var l=o.valToPx(r),s=Math.round(l-o._canvasClientWidth()/i);s<=0&&(s=0),o._rescaleInternal(s+o._canvasClientWidth(),null,Pe.ZoomMode),o.scrollLeft=s,o.redraw()}})),We(je(o),"zoomIn",(function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:o._options.zoomSpeed,n=(null===(e=o._scrollContainer)||void 0===e?void 0:e.clientWidth)||0;t&&n&&o._zoom(1,t,n/2)})),We(je(o),"zoomOut",(function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:o._options.zoomSpeed,n=(null===(e=o._scrollContainer)||void 0===e?void 0:e.clientWidth)||0;t&&n&&o._zoom(-1,t,n/2)})),We(je(o),"_setZoom",(function(e){var t,n,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;return i=f.isNumber(i)?i:null===(t=o._options)||void 0===t?void 0:t.zoomMin,r=f.isNumber(r)?r:null===(n=o._options)||void 0===n?void 0:n.zoomMax,f.isNumber(e)?(e=(e=f.keepInBounds(e,i,r))||1,o._currentZoom=e,e):e})),We(je(o),"setZoom",(function(e){var t=o.getZoom();if(t!==e){var n=o._setZoom(e);if(t!=n)return o.rescale(),o.redraw(),n}return t})),We(je(o),"getZoom",(function(){return f.isNumber(o._currentZoom)&&o._currentZoom||1})),We(je(o),"_getClickDetectionRadius",(function(e){var t=o._consts.clickDetectionMinRadius||1;return Math.max(t,(null==e?void 0:e.radius)||t)})),We(je(o),"_handleContextMenu",(function(t){if(f.clearBrowserSelection(),o._canvas&&o._scrollContainer){var n=o._trackMousePos(o._canvas,t),i=o._getClickDetectionRadius(n),r=o.elementFromPoint(n.pos,i,[]),a=o._findDraggableElement(r,n.val),s=new me;s.point=n,s.args=t,s.elements=r,s.target=a,Ge((e=je(o),$e(l.prototype)),"emit",e).call(e,Me.ContextMenu,s)}else o._cleanUpSelection()})),We(je(o),"_handleMouseDownEvent",(function(e){if(f.clearBrowserSelection(),o._canvas&&o._scrollContainer){if(o._startPosMouseArgs=o._trackMousePos(o._canvas,e),o._startPosMouseArgs){var i=Date.now()-o._lastClickTime<o._consts.doubleClickTimeoutMs;o._lastClickPoint&&o._startPosMouseArgs&&f.getDistance(o._lastClickPoint.x,o._lastClickPoint.y,o._startPosMouseArgs.pos.x,o._startPosMouseArgs.pos.y)>o._consts.clickThreshold&&(i=!1),o._lastClickPoint=o._startPosMouseArgs.pos,o._scrollStartPos={x:o._scrollContainer.scrollLeft,y:o._scrollContainer.scrollTop},o._clickAllowed=!0;var r=null;o._interactionMode!==Ce.NonInteractivePan&&o._interactionMode!==Ce.None||(r=[_.Timeline]);var a=o._getClickDetectionRadius(o._startPosMouseArgs),s=o.elementFromPoint(o._startPosMouseArgs.pos,a,r),c=o._findDraggableElement(s,o._startPosMouseArgs.val),u=new me;if(u.point=o._startPosMouseArgs,u.args=e,u.elements=s,u.target=c,i)Ge((t=je(o),$e(l.prototype)),"emit",t).call(t,Me.DoubleClick,u);else if(Ge((n=je(o),$e(l.prototype)),"emit",n).call(n,Me.MouseDown,u),o._clickTimeout=Date.now(),o._lastClickTime=Date.now(),u.isPrevented())o._cleanUpSelection();else{var d;if(o._currentPos=o._startPosMouseArgs,c&&o._interactionMode!==Ce.Zoom)if(o._drag=new M,o._drag.val=c.val,o._drag.type=c.type,o._drag.target=o._setElementDragState(c,c.val),c.type===_.Keyframe)o._startedDragWithCtrl=o._controlKeyPressed(e),o._startedDragWithShiftKey=e.shiftKey,null==c||!c.keyframe||null!=c&&null!==(d=c.keyframe)&&void 0!==d&&d.selected||o._controlKeyPressed(e)||o._selectInternal(c.keyframe),o._drag.elements=o.getSelectedElements().map((function(e){return o._setElementDragState(e,e.val)}));else if(c.type===_.Group){var m=o._drag.target.keyframes;m&&Array.isArray(m)&&(o._drag.elements=m.map((function(e){var t;return o._setElementDragState(o._convertToTimelineElement((null===(t=o._drag)||void 0===t?void 0:t.target.row)||null,e),e.val)})))}else o._drag.elements=[o._drag.target];o.redraw()}}}else o._cleanUpSelection()})),We(je(o),"_setElementDragState",(function(e,t){var n=e;return n.prevVal=n.val,void 0!==n.startedVal&&null!==n.startedVal||(n.startedVal=t),void 0!==n.prevVal&&null!==n.prevVal||(n.prevVal=t),n.val=t,n})),We(je(o),"isLeftButtonClicked",(function(e){return!!e&&1==e.buttons})),We(je(o),"_handleMouseMoveEvent",(function(e){if(e?o._lastUsedArgs=e:e=o._lastUsedArgs,e&&o._canvas){var t=e,n=t.changedTouches&&t.changedTouches.length>0;o._currentPos=o._trackMousePos(o._canvas,e),!o._isPanStarted&&o._selectionRect&&o._clickTimeoutIsOver()?o._interactionMode===Ce.None||o._interactionMode===Ce.Zoom||o._interactionMode===Ce.NonInteractivePan?o._selectionRectEnabled=!1:o._selectionRectEnabled=!0:o._selectionRectEnabled=!1;var i=o.isLeftButtonClicked(e);if(i||(o._scrollAreaClickOrDragStarted=!1),o._startPosMouseArgs)if(i||n){if(o._drag&&!o._startedDragWithCtrl){var r=o._currentPos.val;if(o._drag.type===_.Timeline)o._setTimeInternal(r,j.User);else if((o._drag.type==_.Keyframe||o._drag.type==_.Group)&&o._drag.elements){var a=Math.floor(r-o._drag.val);if(0!==o._moveElements(a,o._drag.elements,j.User)){if(!o._drag.changed){o._drag.prevVal=o._drag.val;var l=o._emitDragStartedEvent(o._drag);if(!l||l.isPrevented())return o._cleanUpSelection(!0),void(o._drag=null)}o._drag.changed=!0,o._drag.val+=a,o._emitDragEvent(o._drag)}}}o._interactionMode!==Ce.Pan&&o._interactionMode!==Ce.NonInteractivePan||o._drag?o._interactionMode!==Ce.None&&o._scrollBySelectionOutOfBounds(o._currentPos.pos):(o._isPanStarted=!0,o._setCursor(we.Grabbing),o._scrollByPan(o._startPosMouseArgs.pos,o._currentPos.pos,o._scrollStartPos)),o.redraw()}else o._cleanUpSelection(!0),o.redraw();else if(!n){var s=null;o._interactionMode!==Ce.NonInteractivePan&&o._interactionMode!==Ce.None||(s=[_.Timeline]);var c=o._getClickDetectionRadius(o._currentPos),u=o.elementFromPoint(o._currentPos.pos,c,s),d=o._findDraggableElement(u,o._currentPos.val);if(o._isPanStarted||o._interactionMode===Ce.Pan||o._interactionMode===Ce.NonInteractivePan)i?o._setCursor(we.Grabbing):o._setCursor(we.Grab);else{if(o._interactionMode===Ce.Zoom)return void o._setZoomCursor(e);o._setCursor(we.Default)}if(d){var f=null;if(d.type===_.Group)f=f||we.EWResize;else if(d.type==_.Keyframe){var m,p,v;f=f||(null!==(m=null===(p=d.keyframe)||void 0===p||null===(v=p.style)||void 0===v?void 0:v.cursor)&&void 0!==m?m:we.Pointer)}else if(d.type==_.Timeline){var h,y;f=f||(null===(h=o._options)||void 0===h||null===(y=h.timelineStyle)||void 0===y?void 0:y.cursor)||null}f&&o._setCursor(f)}}n&&e.preventDefault()}})),We(je(o),"_handleMouseUpEvent",(function(e){if(o._scrollAreaClickOrDragStarted=!1,o._canvas&&o._startPosMouseArgs){var t=o._trackMousePos(o._canvas,e);if(o._clickAllowed||!o._clickTimeoutIsOver()||o._drag&&(o._startedDragWithCtrl||o._startedDragWithShiftKey))if(o._options&&o._interactionMode===Ce.Zoom){var n=o._controlKeyPressed(e)?1:-1,i=o._getMousePos(o._canvas,e),r=Math.max(0,i.pos.x||0);o._zoom(n,o._options.zoomSpeed||0,r)}else o._performClick(t,o._drag);else if(!o._drag&&o._selectionRect&&o._selectionRectEnabled)if(o._interactionMode===Ce.Zoom);else if(o._interactionMode!==Ce.None){var a=o._getKeyframesByRectangle(o._selectionRect),l=e.shiftKey?xe.Append:xe.Normal;o.select(a,l)}o._cleanUpSelection(),o.redraw()}})),We(je(o),"_canvasClientHeight",(function(){return o._canvas?o._canvas.clientHeight:0})),We(je(o),"_canvasClientWidth",(function(){return o._canvas?o._canvas.clientWidth:0})),We(je(o),"_getKeyframesByRectangle",(function(e){var t=[];return o._forEachKeyframe((function(n){!1!==n.model.selectable&&(n.shape===r.Rect?f.isRectIntersects(n.size,e):f.isOverlap(n.size.x,n.size.y,e))&&t.push(n.model)})),t})),We(je(o),"_performClick",(function(e,t){var n=!1;if(t&&t.type===_.Keyframe){var i,r,a=xe.Normal;o._startedDragWithCtrl&&o._controlKeyPressed(e.args)?o._controlKeyPressed(e.args)&&(a=xe.Revert):o._startedDragWithShiftKey&&e.args.shiftKey&&(a=xe.Append),n=o._selectInternal((null==t||null===(i=t.target)||void 0===i?void 0:i.keyframe)||null,a).selectionChanged||n,e.args.shiftKey&&!1!==(null===(r=o._options)||void 0===r?void 0:r.timelineDraggable)&&(n=o._setTimeInternal(e.val,j.User)||n)}else{var l;n=o._selectInternal(null).selectionChanged||n,!1!==(null===(l=o._options)||void 0===l?void 0:l.timelineDraggable)&&(n=o._setTimeInternal(e.val,j.User)||n)}return n})),We(je(o),"_setKeyframePos",(function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:j.Programmatically;if(!e||!e.keyframe)return t;if(t=Math.floor(t),e.keyframe&&e.keyframe.val!=t){e.prevVal=e.val,e.val=t,e.keyframe.val=t;var i=o._emitKeyframeChanged(e,n);return i.isPrevented()&&(e.val=i.prevVal,e.keyframe.val=i.prevVal),t}return t})),We(je(o),"_setCursor",(function(e){o._canvas&&o._canvas.style.cursor!=e&&(o._canvas.style.cursor=e)})),We(je(o),"setInteractionMode",(function(e){o._interactionMode!=e&&(o._interactionMode=e,o._cleanUpSelection(!0),o.redraw())})),We(je(o),"getInteractionMode",(function(){return o._interactionMode})),We(je(o),"_convertToTimelineElement",(function(e,t){return{type:_.Keyframe,val:t.val,keyframe:t,row:e}})),We(je(o),"getSelectedKeyframes",(function(){return o.getSelectedElements().map((function(e){return e.keyframe}))})),We(je(o),"getSelectedElements",(function(){var e=[];return o._forEachKeyframe((function(t){t&&t.model.selected&&e.push(o._convertToTimelineElement(t.rowViewModel.model,t.model))})),e})),We(je(o),"getAllKeyframes",(function(){var e=[];return o._forEachKeyframe((function(t){e.push(t.model)})),e})),We(je(o),"selectAllKeyframes",(function(){return o.select(o.getAllKeyframes(),xe.Normal)})),We(je(o),"deselectAll",(function(){return o.select(null)})),We(je(o),"_changeNodeState",(function(e,t,n){if(t.selected!==n){var i="boolean"!=typeof t.selectable||t.selectable;if(!n||n&&i)return t.selected=n,e.changed.push(t),!0}return!1})),We(je(o),"select",(function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:xe.Normal,n=o._selectInternal(e,t);return n.selectionChanged&&o.redraw(),n})),We(je(o),"_selectInternal",(function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:xe.Normal;e||(e=[]),Array.isArray(e)||(e=[e]);var n={selectionChanged:!1,selected:o.getSelectedKeyframes(),changed:[]},i=e;if(i&&t===xe.Append)e.forEach((function(e){o._changeNodeState(n,e,!0)&&e.selected&&n.selected.push(e)}));else if(i&&t===xe.Revert)e.forEach((function(e){n.selected.indexOf(e)>=0?(o._changeNodeState(n,e,!1),f.deleteElement(n.selected,e)):(o._changeNodeState(n,e,!0),e.selected&&n.selected.push(e))}));else if(t===xe.Normal){var r=[];e&&e.forEach((function(e){o._changeNodeState(n,e,!0),e.selected&&r.push(e)})),n.selected.forEach((function(e){i.indexOf(e)>=0||o._changeNodeState(n,e,!1)})),n.changed.length>0&&(r?n.selected=r:n.selected.length=0)}return n.changed.length>0&&(n.selectionChanged=!0,o._emitKeyframesSelected(n)),n})),We(je(o),"_startAutoPan",(function(){o._consts.autoPanSpeed&&(o._intervalRef||(o._intervalRef=window.setInterval((function(){o._handleMouseMoveEvent(null)}),o._consts.autoPanSpeed)))})),We(je(o),"_stopAutoPan",(function(){o._intervalRef&&(clearInterval(o._intervalRef),o._intervalRef=null),o._autoPanLastActionDate=0})),We(je(o),"_toScreenPx",(function(e){return o.valToPx(e)-o.scrollLeft+o._leftMargin()})),We(je(o),"_fromScreen",(function(e){return o.pxToVal(o.scrollLeft+e-o._leftMargin())})),We(je(o),"valToPx",(function(e){if(!o._options||!o._options.stepPx)return e;var t=o._options.min||0;f.isNumber(t)||(t=0),t*=o._currentZoom||1;var n=(o._options.stepVal||0)*o._currentZoom||1;return(-t+e)*(o._options.stepPx/n)})),We(je(o),"_mousePosToVal",(function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=Math.min(e,o._canvasClientWidth()),i=o._fromScreen(n);return i=Math.round(i),t&&(i=o.snapVal(i)),i})),We(je(o),"_formatUnitsText",(function(e){var t=f.sign(e)<0?"-":"",n=(e=Math.abs(e))/1e3,i=Math.floor(n/31536e3);n%=31536e3;var o=Math.floor(n/86400);n%=86400;var r=Math.floor(n/3600);n%=3600;var a=Math.floor(n/60);n%=60;var l="";return i&&(l+=i+":"),o&&(l+=o+":"),r&&(l+=r+":"),a&&(l+=r?f.timePadZero(a):a+":"),isNaN(n)||(l+=a?f.timePadZero(n):n),t+l})),We(je(o),"_leftMargin",(function(){var e;return(null===(e=o._options)||void 0===e?void 0:e.leftMargin)||0})),We(je(o),"_renderTicks",(function(){var e;if(!(!o._ctx||!o._ctx.canvas||o._ctx.canvas.clientWidth<=0||o._ctx.canvas.clientHeight<=0)&&o._options&&o._options.stepPx){var t=o._canvasClientWidth()-o._leftMargin(),n=o.pxToVal(o.scrollLeft),i=o.pxToVal(o.scrollLeft+t);if(!isNaN(n)&&!isNaN(i)&&n!==i){if(i<n){var r=i;i=n,n=r}var a=f.getDistance(n,i);if(!(a<=0)){var l=f.findGoodStep(a/(t/o._options.stepPx),0,null===(e=o._options)||void 0===e?void 0:e.denominators),s=Math.floor(n/l)*l,c=Math.ceil(i/l)*l+l;if(f.isNumber(l)&&!(l<=0)&&0!==Math.abs(c-s)){var u,d=0;o._options.stepSmallPx&&(d=f.findGoodStep(a/(t/o._options.stepSmallPx),0,null===(u=o._options)||void 0===u?void 0:u.denominators));var m=0;o._ctx.save();for(var p=k.headerHeight(o._options),v=p/2,h=p/1.3,y=s;y<=c;y+=l){var g=o._getSharp(o._toScreenPx(y));o._ctx.save(),o._ctx.beginPath(),o._ctx.setLineDash([4]),o._ctx.lineWidth=1,o._options.tickColor&&(o._ctx.strokeStyle=o._options.tickColor),f.drawLine(o._ctx,g,v,g,p),o._ctx.stroke(),o._options.labelsColor&&(o._ctx.fillStyle=o._options.labelsColor),o._options.font&&(o._ctx.font=o._options.font);var _=o._formatUnitsText(y),b=o._ctx.measureText(_),T=g-b.width/2;if((isNaN(m)||m<=T)&&(m=T+b.width,o._ctx.fillText(_,T,10)),o._ctx.restore(),f.isNumber(d)&&!(d<=0))for(var C=y+d;C<y+l;C+=d){var w=o._getSharp(o._toScreenPx(C));o._ctx.beginPath(),o._ctx.lineWidth=o._pixelRatio,o._options.tickColor&&(o._ctx.strokeStyle=o._options.tickColor),f.drawLine(o._ctx,w,h,w,p),o._ctx.stroke()}}o._ctx.restore()}}}}})),We(je(o),"_generateViewModel",(function(){var e={size:{x:0,y:0,width:0,height:0},min:null,max:null,rowsViewModels:[],keyframesViewModels:[]};if(!o._model)return e;var t=o._model.rows;if(!t||!Array.isArray(t)||t.length<=0)return e;var n=k.headerHeight(o._options);return t.forEach((function(t,i){if(t&&!t.hidden){var r=k.getRowHeight(t.style||null,o._options),a=k.getRowMarginBottom(t.style||null,o._options),l=n-(o._scrollContainer?o._scrollContainer.scrollTop:0);n+=r+a,0==i&&(e.size.y=l),e.size.height=Math.max(n+r,e.size.height);var s={size:{x:0,y:l,width:o._canvasClientWidth(),height:r},marginBottom:a,model:t,index:i,min:null,max:null,groupsViewModels:[],keyframesViewModels:[]};e.rowsViewModels.push(s),!t.keyframes||!t.keyframes.forEach||t.keyframes.length<=0||(t&&t.keyframes&&t.keyframes.forEach((function(n){var i;if(n&&f.isNumber(n.val)){if(!n.hidden){var r=(null===(i=s.groupsViewModels)||void 0===i?void 0:i.find((function(e){return n.group===e.groupModel})))||null;r||(r={min:n.val,max:n.val,size:null,groupModel:n.group,keyframesViewModels:[]},s.groupsViewModels.push(r));var a=k.keyframeShape(n,n.group,t.style||null,o._options),l=o._getKeyframePosition(n,r,s,a),c={model:n,rowViewModel:s,groupViewModel:r,size:l,shape:a},u=null===r.min?n.val:Math.min(n.val,r.min),d=null===r.max?n.val:Math.max(n.val,r.max);f.isNumber(u)&&(r.min=u),f.isNumber(d)&&(r.max=d),s.keyframesViewModels.push(c),r.keyframesViewModels.push(c),e.keyframesViewModels.push(c)}}else console.log("Unexpected null keyframe or having invalid value")})),s.groupsViewModels.forEach((function(e){f.setMinMax(s,e,!0);var t=o._getKeyframesGroupSize(e,s);e.size=t})),f.setMinMax(e,s,!0))}})),f.isNumber(e.max)&&(e.max||0===e.max)&&(e.size.width=o.valToPx(e.max)),e})),We(je(o),"_renderRows",(function(){if(o._ctx){var e=o._generateViewModel();if(null!=e&&e.rowsViewModels)try{o._ctx.save(),e.rowsViewModels.forEach((function(e){if(e&&o._ctx){o._ctx.fillStyle=k.getRowFillColor(e.model.style||null,o._options);var t=o._cutBounds(e.size);if(null!=t&&t.rect){var n=null==t?void 0:t.rect;o._ctx.fillRect(n.x,n.y,n.width,n.height)}o._renderGroupBounds(e)}}))}finally{o._ctx.restore()}}})),We(je(o),"_renderGroupBounds",(function(e){var t;e&&o._ctx&&(null==e||null===(t=e.groupsViewModels)||void 0===t||t.forEach((function(t){var n,i,r,a,l;if(o._ctx&&!(((null==t||null===(n=t.keyframesViewModels)||void 0===n?void 0:n.length)||0)<=1)){var s=k.groupFillColor(o._options,t.groupModel,null==e||null===(i=e.model)||void 0===i?void 0:i.style),c=k.groupStrokeColor(o._options,t.groupModel,null==e||null===(r=e.model)||void 0===r?void 0:r.style),u=k.groupStrokeThickness(o._options,t.groupModel,null==e||null===(a=e.model)||void 0===a?void 0:a.style),d=k.groupsRadii(o._options,t.groupModel,null==e||null===(l=e.model)||void 0===l?void 0:l.style);if(t.size)try{o._ctx.save();var f=o._cutBounds(t.size);if(null!=f&&f.rect){var m=f.rect;c||(u=0),o._ctx.strokeStyle=u>0?c:"",o._ctx.fillStyle=s,o._ctx.lineWidth=u,o._ctx.beginPath(),o._ctx.roundRect(m.x+u,m.y+u,m.width-u,m.height-u,d),o._ctx.fill(),u>0&&o._ctx.stroke()}}finally{o._ctx.restore()}else console.log("Size of the group cannot be calculated")}})))})),We(je(o),"_cutBounds",(function(e){if(!e)return null;var t=o._canvasClientWidth()-0,n=k.headerHeight(o._options)+0,i=o._canvasClientHeight()-0;return o._cutBoundsWhenOverlap(e,0,t,n,i)})),We(je(o),"_cutBoundsWhenOverlap",(function(e,t,n,i,o){if(!e)return null;if(f.isRectIntersects(e,{x:t,y:i,width:f.getDistance(t,n),height:f.getDistance(i,o)})){var r=Math.max(e.y,i),a=Math.max(e.x,t),l=e.x-a,s=e.y-r;return{rect:{height:e.height+s,width:e.width+l,x:a,y:r},overlapY:Math.abs(s)>0,overlapX:Math.abs(l)>0}}return null})),We(je(o),"_getKeyframesGroupSize",(function(e,t){var n,i,r=t.size.y,a=t.size.height,l=e.groupModel||null,s=k.groupHeight(o._options,l,null==t||null===(n=t.model)||void 0===n?void 0:n.style),c=k.groupMarginTop(o._options,l,null==t||null===(i=t.model)||void 0===i?void 0:i.style),u="auto"===s;s&&!u||(s=Math.floor(a)),(s="string"==typeof s?parseInt(s):s)>a&&(s=a);var d="auto"===c;"string"==typeof c&&(c=d?(a-s)/2:parseInt(c)||0),d||u&&(s-=2*c);var m=o._toScreenPx(e.min),p=o._toScreenPx(e.max);return{x:m,y:r+c,height:s,width:f.getDistance(m,p)}})),We(je(o),"_getKeyframePosition",(function(e,t,n,i){var a,l;if(!e)return console.log("keyframe should be defined."),null;var s=e.val;if(!f.isNumber(s))return null;var c=n.size,u=c.y+c.height/2,d=(null==t?void 0:t.groupModel)||null,m=k.keyframeHeight(e,d,null==n||null===(a=n.model)||void 0===a?void 0:a.style,o._options),p=k.keyframeWidth(e,d,null==n||null===(l=n.model)||void 0===l?void 0:l.style,o._options);if("auto"===m&&(m=c.height/3),"auto"===p&&(p=m),m=Number(m),!Number.isNaN(m)&&m&&m>0){var v={x:Math.floor(o._toScreenPx(s)),y:u=Math.floor(u),height:m,width:p};return i===r.Rect&&(v.y=v.y-v.height/2,v.x=v.x-v.width/2),v}return null})),We(je(o),"_renderKeyframes",(function(){o._forEachKeyframe((function(e){if(o._ctx){var t=e.size;if(t){var n=o._getSharp(t.x),i=t.y,r=o._cutBounds({x:n-t.width/2,y:i-t.height/2,width:t.width,height:t.height});if(!r)return;o._ctx.save();try{r&&r.overlapY&&(o._ctx.beginPath(),o._ctx.rect(0,k.headerHeight(o._options),o._canvasClientWidth(),o._canvasClientWidth()),o._ctx.clip()),o._renderKeyframe(o._ctx,e)}finally{o._ctx.restore()}}}}))})),We(je(o),"_renderKeyframe",(function(e,t){var n,i=t.shape;if(i!==r.None){var a=t.size,l=o._getSharp(a.x),s=a.y,c=t.model,u=t.rowViewModel.model.style||null,d=(null==t||null===(n=t.groupViewModel)||void 0===n?void 0:n.groupModel)||null,f=c.selected?k.keyframeSelectedFillColor(c,d,u,o._options):k.keyframeFillColor(c,d,u,o._options),m=k.keyframeStrokeThickness(c,d,u,o._options),p="";m>0&&(p=c.selected?k.keyframeSelectedStrokeColor(c,d,u,o._options):k.keyframeStrokeColor(c,d,u,o._options)),i==r.Rhomb?(e.beginPath(),e.translate(l,s),e.rotate(45*Math.PI/180),m>0&&p&&(e.fillStyle=p,e.rect(-a.width/2,-a.height/2,a.width,a.height),e.fill()),e.fillStyle=f,e.translate(m,m),e.rect(-a.width/2,-a.height/2,a.width-2*m,a.height-2*m),e.fill()):i==r.Circle?(e.beginPath(),m>0&&p&&(e.fillStyle=p,e.arc(l,s,a.height,0,2*Math.PI)),e.fillStyle=f,e.arc(l,s,a.height-m,0,2*Math.PI),e.fill()):i==r.Rect&&(e.beginPath(),m>0&&p&&(e.fillStyle=p,e.rect(l,s,a.width,a.height),e.fill()),e.fillStyle=f,e.rect(l+m,s+m,a.width-m,a.height-m),e.fill())}})),We(je(o),"_renderSelectionRect",(function(){if(!o._drag&&o._ctx&&o._canvas){if(o._ctx.save(),o._selectionRect&&o._selectionRectEnabled){o._ctx.setLineDash([4]),o._ctx.lineWidth=o._pixelRatio;var e=o._options.selectionColor;e&&(o._ctx.strokeStyle=e),o._ctx.strokeRect(o._getSharp(o._selectionRect.x,1),o._getSharp(o._selectionRect.y,1),Math.floor(o._selectionRect.width),Math.floor(o._selectionRect.height))}o._ctx.restore()}})),We(je(o),"_renderBackground",(function(){o._ctx&&o._canvas&&(o._options.fillColor?(o._ctx.save(),o._ctx.beginPath(),o._ctx.rect(0,0,o._canvasClientWidth(),o._canvasClientHeight()),o._ctx.fillStyle=o._options.fillColor,o._ctx.fill(),o._ctx.restore()):o._ctx.clearRect(0,0,o._canvas.width,o._canvas.height))})),We(je(o),"_renderTimeline",(function(){if(o._ctx&&o._options&&o._options.timelineStyle){var e=o._options.timelineStyle;o._ctx.save();try{var t=e.width||1;o._ctx.lineWidth=t*o._pixelRatio;var n=o._getSharp(o._toScreenPx(o._val),t);e.strokeColor&&(o._ctx.strokeStyle=e.strokeColor),e.fillColor&&(o._ctx.fillStyle=e.fillColor);var i=e.marginTop||0,r=e.marginBottom||0;o._ctx.beginPath();var a=o._canvasClientHeight()-r;f.drawLine(o._ctx,n,i,n,a),o._ctx.stroke(),o._renderTimelineCap(n,i)}finally{o._ctx.restore()}}})),We(je(o),"_renderTimelineCap",(function(e,t){var n,i,r=null===(n=o._options)||void 0===n||null===(i=n.timelineStyle)||void 0===i?void 0:i.capStyle;if(o._ctx&&r&&r.capType!==Se.None){o._ctx.save();try{var a=r.width||0,l=r.height||0;a&&l&&(o._ctx.strokeStyle=r.strokeColor||"",o._ctx.fillStyle=r.fillColor||"white",r.capType===Se.Triangle?(o._ctx.beginPath(),o._ctx.moveTo(e-a/2,t),o._ctx.lineTo(e+a/2,t),o._ctx.lineTo(e,l),o._ctx.closePath(),o._ctx.stroke(),o._ctx.fill()):r.capType===Se.Rect&&(o._ctx.fillRect(e-a/2,t,a,l),o._ctx.fill()))}finally{o._ctx.restore()}}})),We(je(o),"_renderHeaderBackground",(function(){o._ctx&&o._options&&k.headerHeight(o._options)&&(o._ctx.save(),o._ctx.lineWidth=o._pixelRatio,o._options.headerFillColor?(o._ctx.lineWidth=o._pixelRatio,o._ctx.fillStyle=o._options.headerFillColor,o._ctx.fillRect(0,0,o._canvasClientWidth(),k.headerHeight(o._options))):o._ctx.clearRect(0,0,o._canvasClientWidth(),k.headerHeight(o._options)),o._ctx.restore())})),We(je(o),"redraw",(function(){var e;null!==(e=window)&&void 0!==e&&e.requestAnimationFrame?window.requestAnimationFrame(o._redrawInternal):o._redrawInternal()})),We(je(o),"scrollToRightBounds",(function(){o._scrollContainer&&o._scrollContainer.scrollLeft!==o._scrollContainer.scrollWidth&&(o.scrollLeft=o._scrollContainer.scrollWidth)})),We(je(o),"_redrawInternal",(function(){o._ctx&&o._scrollContainer?(o.valToPx(o._val)>o._scrollContainer.scrollWidth&&(o.rescale(),!o._isPanStarted&&o._drag&&o._drag.type!==_.Timeline&&o.scrollToRightBounds()),o._renderBackground(),o._renderRows(),o._renderHeaderBackground(),o._renderTicks(),o._renderKeyframes(),o._renderSelectionRect(),o._renderTimeline()):console.log("Context is not initialized")})),We(je(o),"_getSharp",(function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;return e=Math.round(e),t%2==0?e:e+o._pixelRatio/2})),We(je(o),"getTime",(function(){return o._val})),We(je(o),"_setTimeInternal",(function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:j.Programmatically;if(!o._options)return!1;if(e=Math.round(e),e=f.keepInBounds(e,o._options.min),o._val!=e){var n=o._val,i=new q;return i.val=e,i.prevVal=n,i.source=t,o._val=e,o.emit(Me.TimeChanged,i),!i.isPrevented()||(o._val=n,!1)}return!1})),We(je(o),"setTime",(function(e){if(o._drag&&o._drag.type===_.Timeline)return!1;var t=o._setTimeInternal(e,j.SetTimeMethod);return t&&(o.rescale(),o.redraw()),t})),We(je(o),"getOptions",(function(){return o._options})),We(je(o),"setOptions",(function(e){return o._options=o._setOptions(e),o.rescale(),o.redraw(),o._options})),We(je(o),"_applyContainersStyles",(function(){if(o._scrollContainer&&o._options){var e=o._scrollContainer.classList;o._options.scrollContainerClass&&!e.contains(o._options.scrollContainerClass)&&e.add(o._options.scrollContainerClass),o._options.fillColor&&(o._scrollContainer.style.background=o._options.fillColor)}})),We(je(o),"_setOptions",(function(e){return e?(o._options=f.mergeOptions(o._options,e),o._options.snapStep=f.keepInBounds(o._options.snapStep||0,0,o._options.stepVal||0),o._currentZoom=o._setZoom(o._options.zoom||0,o._options.zoomMin,o._options.zoomMax),o._options.min=f.isNumber(o._options.min)?o._options.min:0,o._options.max=f.isNumber(o._options.max)?o._options.max:Number.MAX_VALUE,o._applyContainersStyles(),!1===e.timelineDraggable&&o._drag&&o._drag.type===_.Timeline&&o._cleanUpSelection(),o._options):o._options||{}})),We(je(o),"getModel",(function(){return o._model})),We(je(o),"setModel",(function(e){o._model=e,o.rescale(),o.redraw()})),We(je(o),"_getMousePos",(function(e,t){var n=1,i=0,r=0;if(t.changedTouches&&t.changedTouches.length>0){var a=t.changedTouches[0];i=a.clientX,r=a.clientY,n=Math.max(n,a.radiusX,a.radiusY)}else i=t.clientX,r=t.clientY;var l=e.getBoundingClientRect(),s=e.width/o._pixelRatio/l.width,c=e.height/o._pixelRatio/l.height;return{pos:{x:(i-l.left)*s,y:(r-l.top)*c},radius:n,args:t}})),We(je(o),"_updateCanvasScale",(function(){if(!o._scrollContainer||!o._container||!o._ctx)return console.log("Component should be initialized first."),!1;var e=!1,t=o._scrollContainer.clientWidth*o._pixelRatio,n=o._scrollContainer.clientHeight*o._pixelRatio;return Math.floor(t)!=Math.floor(o._ctx.canvas.width)&&(o._ctx.canvas.width=t,e=!0),Math.floor(n)!=Math.floor(o._ctx.canvas.height)&&(o._ctx.canvas.height=n,e=!0),e&&o._ctx.setTransform(o._pixelRatio,0,0,o._pixelRatio,0,0),e})),We(je(o),"rescale",(function(){return o._rescaleInternal()})),We(je(o),"_rescaleInternal",(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Pe.DefaultMode,i=o._updateCanvasScale();if(!o._scrollContent)return i;var r=o._generateViewModel();if(r&&r.size){var a,l=o._options.stepPx||0;e=e||0;var s=o.valToPx(o._val)+o._leftMargin(),c=0,u=o.scrollLeft+o.getClientWidth();s>=u&&(c=n==Pe.ScrollBySelection?Math.floor(s+o._leftMargin()):Math.floor(s+o.getClientWidth()+o._leftMargin()));var d=r.size.width+o._leftMargin()+l;e=Math.max(e,d,u,c);var f=Math.floor(e)+"px";f!=o._scrollContent.style.minWidth&&(o._scrollContent.style.minWidth=f,i=!0),t=Math.max(Math.floor(r.size.height+.2*o._canvasClientHeight()),((null===(a=o._scrollContainer)||void 0===a?void 0:a.scrollTop)||0)+o._canvasClientHeight()-1,Math.round(t||0));var m=Math.floor(t)+"px";if(o._scrollContent.style.minHeight!=m)return o._scrollContent.style.minHeight=m,i}return i})),We(je(o),"_filterDraggableElements",(function(e){return e.filter((function(e){if(!e)return!1;var t;if(e.type===_.Keyframe){if(!k.keyframeDraggable(e.keyframe||null,(null===(t=e.keyframe)||void 0===t?void 0:t.group)||null,(null==e?void 0:e.row)||null,o._options))return!1}else if(e.type===_.Group){if(!k.groupDraggable(e.group||null,e.row||null,o._options))return!1}else if(e.type===_.Timeline){var n;if(!1===(null===(n=o._options)||void 0===n?void 0:n.timelineDraggable))return!1}else if(e.type===_.Row)return!1;return!0}))})),We(je(o),"_findDraggableElement",(function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=function(e){return e===_.Timeline?3:e===_.Keyframe?1:e===_.Group?2:-1},i=function(e,i){var o=n(e.type),r=n(i.type);return o!==r||null!==t&&(o=f.getDistance(e.val,t))!==(r=f.getDistance(i.val,t))?o<r?1:-1:0},r=o._filterDraggableElements(e).sort(i);return r.length>0?r[r.length-1]:null})),We(je(o),"elementFromPoint",(function(e,t,n){var i;t=Math.max(t,1);var a=[];if(!e)return a;var l,s=k.headerHeight(o._options),c=o._toScreenPx(o._val),u=0,d=null===(i=o._options)||void 0===i?void 0:i.timelineStyle;d&&(u=Math.max((d.width||1)*o._pixelRatio,((null==d||null===(l=d.capStyle)||void 0===l?void 0:l.width)||0)*o._pixelRatio||1)+t),(e.y<=.5*s||e.x>=c-u/2&&e.x<=c+u/2)&&a.push({val:o._val,type:_.Timeline});var m=o._options.snapEnabled;return e.y>=s&&o._options.keyframesDraggable&&o._forEachKeyframe((function(n,i,l){var s=n.rowViewModel;l&&s.groupsViewModels&&s.groupsViewModels.forEach((function(t){if(null!=t&&t.size&&f.isOverlap(e.x,e.y,t.size)){var n=(null==t?void 0:t.keyframesViewModels.map((function(e){return e.model})))||[],i={val:o._mousePosToVal(e.x,m),type:_.Group,group:t.groupModel,row:s.model,keyframes:n},r=o.snapVal(t.min);i.val+=t.min-r,a.push(i)}}));var c=n.size;if(c){var u=!1;if(n.shape===r.Rect){var d=f.shrinkSelf({x:e.x,y:e.y,height:t,width:t},t);u=f.isRectIntersects(d,c,!0)}else u=f.getDistance(c.x,c.y,e.x,e.y)<=c.height+t;u&&a.push({keyframe:n.model,keyframes:[n.model],val:n.model.val,row:n.rowViewModel.model,type:_.Keyframe})}}),(function(t){if(f.isOverlap(e.x,e.y,t.size)){var n={val:o._mousePosToVal(e.x,m),keyframes:t.model.keyframes,type:_.Row,row:t.model};a.push(n)}})),n&&0!==n.length?a.filter((function(e){return n&&n.includes(e.type)})):a})),We(je(o),"onTimeChanged",(function(e){o.on(Me.TimeChanged,e)})),We(je(o),"onDragStarted",(function(e){o.on(Me.DragStarted,e)})),We(je(o),"onDrag",(function(e){o.on(Me.Drag,e)})),We(je(o),"onDragFinished",(function(e){o.on(Me.DragFinished,e)})),We(je(o),"onDoubleClick",(function(e){o.on(Me.DoubleClick,e)})),We(je(o),"onKeyframeChanged",(function(e){o.on(Me.KeyframeChanged,e)})),We(je(o),"onMouseDown",(function(e){o.on(Me.MouseDown,e)})),We(je(o),"onSelected",(function(e){o.on(Me.Selected,e)})),We(je(o),"onScroll",(function(e){o.on(Me.Scroll,e)})),We(je(o),"onScrollFinished",(function(e){o.on(Me.ScrollFinished,e)})),We(je(o),"onContextMenu",(function(e){o.on(Me.ContextMenu,e)})),We(je(o),"_emitScrollEvent",(function(e,t){var n,r,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Me.Scroll,s={args:e,scrollProgrammatically:t,scrollLeft:o.scrollLeft,scrollTop:o.scrollTop,scrollHeight:(null===(n=o._scrollContainer)||void 0===n?void 0:n.scrollHeight)||0,scrollWidth:(null===(r=o._scrollContainer)||void 0===r?void 0:r.scrollWidth)||0};return Ge((i=je(o),$e(l.prototype)),"emit",i).call(i,a,s),s})),We(je(o),"_emitKeyframeChanged",(function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:j.Programmatically,n=new G;return n.val=e.val,n.prevVal=e.prevVal,n.target=e,n.source=t,o.emit(Me.KeyframeChanged,n),n})),We(je(o),"_emitDragStartedEvent",(function(e){if(!e)return null;var t=o._getDragEventArgs(e,o._currentPos);return o.emit(Me.DragStarted,t),t.isPrevented()&&o._preventDrag(t,e,!0),t})),We(je(o),"_emitDragFinishedEvent",(function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e||!e.changed)return null;var n=o._getDragEventArgs(e,o._currentPos);return t&&n.preventDefault(),o.emit(Me.DragFinished,n),n.isPrevented()&&o._preventDrag(n,e,!0),n})),We(je(o),"_preventDrag",(function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];e.elements&&e.elements.forEach((function(e){var t=n?e.startedVal:e.prevVal;o._setKeyframePos(e,t)})),t.val=t.prevVal,e.point=e.prevPoint})),We(je(o),"_emitDragEvent",(function(e){if(!e)return null;var t=o._getDragEventArgs(e,o._currentPos);return o.emit(Me.Drag,t),t.isPrevented()&&o._preventDrag(t,e,!1),t})),We(je(o),"_emitKeyframesSelected",(function(e){var t=new ie;return t.selected=e.selected,t.changed=e.changed,o.emit(Me.Selected,t),t})),We(je(o),"_getDragEventArgs",(function(e,t){var n=new Ee;return n.point=t,n.elements=Ve(e.elements||[]),n.target=(null==e?void 0:e.target)||null,n})),o._options=f.cloneOptions(Oe),(s||c)&&o.initialize(s,c),o}return t=l,n=[{key:"_getCtx",value:function(){return this._canvas?(this._ctx||(this._ctx=this._canvas.getContext("2d")),this._ctx):null}},{key:"_moveElements",value:function(e,t){var n=this,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:j.Programmatically;if(!t)return 0;var o=!1;if(Math.abs(e)>0){var r={min:Number.MIN_SAFE_INTEGER,max:Number.MAX_SAFE_INTEGER};if(r=f.setMinMax(r,this._options),t.forEach((function(t){var i;if(t&&t.keyframe){var o=f.setMinMax(f.setMinMax({min:r.min,max:r.max},t.keyframe),t.row||null),a=(null!==(i=n._options)&&void 0!==i&&i.snapAllKeyframesOnMove?n.snapVal(t.keyframe.val):t.keyframe.val)+e;(o.min||0===o.min)&&f.isNumber(o.min)&&a<o.min&&(e+=f.getDistance(o.min,a)),(o.max||0===o.max)&&f.isNumber(o.max)&&a>o.max&&(e-=f.getDistance(o.max,a))}})),Math.abs(e)>0&&t.forEach((function(t){if(null!=t&&t.keyframe){var r=t.keyframe.val,a=r+e,l=n._setKeyframePos(t,a,i);o=l!==r}})),o)return e}return 0}},{key:"_forEachKeyframe",value:function(e,t){var n;if((e||t)&&this._model){var i=this._generateViewModel();i&&(null==i||null===(n=i.rowsViewModels)||void 0===n||n.forEach((function(n){if(n){t&&t(n);var i=!0;e&&n.keyframesViewModels.forEach((function(t,n){t&&e(t,n,i),i=!1}))}})))}}},{key:"_trackMousePos",value:function(e,t){var n=this._getMousePos(e,t),i=n.pos;if(n.originalVal=this._mousePosToVal(i.x,!1),n.snapVal=this._mousePosToVal(i.x,!0),n.val=n.originalVal,this._options&&this._options.snapEnabled&&(n.val=n.snapVal),this._startPosMouseArgs){var o,r;this._selectionRect||(this._selectionRect={});var a=this._startPosMouseArgs.pos,l=Math.floor(a.x+(((null===(o=this._scrollStartPos)||void 0===o?void 0:o.x)||0)-this.scrollLeft)),s=Math.floor(a.y+(((null===(r=this._scrollStartPos)||void 0===r?void 0:r.y)||0)-this.scrollTop));this._selectionRect.x=Math.min(l,i.x),this._selectionRect.y=Math.min(s,i.y),this._selectionRect.width=Math.max(l,i.x)-this._selectionRect.x,this._selectionRect.height=Math.max(s,i.y)-this._selectionRect.y,this._clickAllowed&&(this._clickAllowed=this._selectionRect.height<=this._consts.clickThreshold&&this._selectionRect.width<=this._consts.clickThreshold)}return n}},{key:"getClientWidth",value:function(){var e;return(null===(e=this._scrollContainer)||void 0===e?void 0:e.clientWidth)||0}},{key:"getClientHeight",value:function(){var e;return(null===(e=this._scrollContainer)||void 0===e?void 0:e.clientHeight)||0}},{key:"_cleanUpSelection",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this._drag&&this._emitDragFinishedEvent(this._drag,e),this._startPosMouseArgs=null,this._drag=null,this._scrollAreaClickOrDragStarted=!1,this._startedDragWithCtrl=!1,this._startedDragWithShiftKey=!1,this._selectionRect=null,this._clickTimeout=null,this._scrollStartPos=null,this._isPanStarted=!1,this._clickAllowed=!1,this._stopAutoPan()}},{key:"_clickTimeoutIsOver",value:function(){return!!(this._clickTimeout&&Date.now()-this._clickTimeout>this._consts.clickDetectionMs)}},{key:"_checkUpdateSpeedTooFast",value:function(){return!!(this._autoPanLastActionDate&&Date.now()-this._autoPanLastActionDate<=this._consts.autoPanSpeedLimit)||(this._autoPanLastActionDate=Date.now(),!1)}},{key:"_scrollByPan",value:function(e,t,n){if(e&&t&&this._scrollContainer){var i=0,o=0;n&&(i=n.x,o=n.y);var r=Math.round(e.x-t.x),a=i+r;r>0&&this._rescaleInternal(a+this._canvasClientWidth()),r>0&&a+this._canvasClientWidth()>=this._scrollContainer.scrollWidth-5?this.scrollLeft=this._scrollContainer.scrollWidth:this.scrollLeft=a,this.scrollTop=Math.round(o+e.y-t.y)}}},{key:"_scrollBySelectionOutOfBounds",value:function(e){if(!this._scrollContainer)return!1;var t=e.x,n=e.y,i=!1,o=0,r=0,a=this._consts.autoPanByScrollPadding,l=t<=a,s=t>=this._canvasClientWidth()-a,c=n<=a,u=n>=this._canvasClientHeight()-a,d=null,m=null;if(l||s||c||u){if(this._startAutoPan(),this._checkUpdateSpeedTooFast())return!1;var p=isNaN(this._consts.scrollByDragSpeed)?1:this._consts.scrollByDragSpeed;l?o=-f.getDistance(t,a)*p:s&&(o=f.getDistance(t,this._canvasClientWidth()-a)*p,d=this.scrollLeft+this._canvasClientWidth()+o),c?r=-f.getDistance(t,a)*p/4:u&&(r=f.getDistance(t,this._canvasClientHeight()-a)*p/4,m=this._scrollContainer.scrollTop+this._canvasClientHeight())}else this._stopAutoPan();return(d||m)&&this._rescaleInternal(d,m,Pe.ScrollBySelection),Math.abs(o)>0&&(this.scrollLeft=this._scrollContainer.scrollLeft+o,i=!0),Math.abs(r)>0&&(this.scrollTop=this._scrollContainer.scrollTop+r,i=!0),i}},{key:"pxToVal",value:function(e){if(!this._options)return e;var t=this._options.min||0;f.isNumber(t)||(t=0);var n=this._options.stepPx||0;return 0===n?e:(t*=this._currentZoom||1)+e/n*((this._options.stepVal||0)*this._currentZoom||1)}},{key:"snapVal",value:function(e){if(this._options&&this._options.snapEnabled&&this._options.snapStep){var t=this._options.snapStep,n=e/t,i=Math.round(n),o=Math.abs(this._options.min||0)/this._options.snapStep,r=f.sign(this._options.min||1)*(o-Math.floor(o))*this._options.snapStep;e=Math.round(r)+Math.round(i*t)}return f.keepInBounds(e,this._options.min,this._options.max)}},{key:"scrollLeft",get:function(){var e;return(null===(e=this._scrollContainer)||void 0===e?void 0:e.scrollLeft)||0},set:function(e){this._scrollContainer&&this._scrollContainer.scrollLeft!==e&&(this._scrollProgrammatically=!0,this._scrollContainer.scrollLeft=e)}},{key:"scrollTop",get:function(){var e;return(null===(e=this._scrollContainer)||void 0===e?void 0:e.scrollTop)||0},set:function(e){this._scrollContainer&&this._scrollContainer.scrollTop!==e&&(this._scrollProgrammatically=!0,this._scrollContainer.scrollTop=e)}}],n&&Re(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),l}(a);return t})())},ad1f:function(e,t,n){var i=n("c430"),o=n("23e7"),r=n("da84"),a=n("d066"),l=n("e330"),s=n("d039"),c=n("90e3"),u=n("1626"),d=n("68ee"),f=n("861d"),m=n("d9b5"),p=n("2266"),v=n("825a"),h=n("f5df"),y=n("1a2d"),g=n("8418"),_=n("9112"),b=n("07fa"),k=n("d6d6"),T=n("90d8"),C=n("b980"),w=r.Object,S=r.Date,x=r.Error,M=r.EvalError,P=r.RangeError,E=r.ReferenceError,I=r.SyntaxError,A=r.TypeError,O=r.URIError,F=r.PerformanceMark,V=r.WebAssembly,B=V&&V.CompileError||x,D=V&&V.LinkError||x,R=V&&V.RuntimeError||x,N=a("DOMException"),z=a("Set"),j=a("Map"),G=j.prototype,K=l(G.has),$=l(G.get),W=l(G.set),L=l(z.prototype.add),H=a("Object","keys"),U=l([].push),Z=l((!0).valueOf),J=l(1..valueOf),Y=l("".valueOf),q=l(S.prototype.getTime),X=c("structuredClone"),Q="DataCloneError",ee="Transferring",te=function(e){return!s((function(){var t=new r.Set([7]),n=e(t),i=e(w(7));return n==t||!n.has(7)||"object"!=typeof i||7!=i}))&&e},ne=function(e){return!s((function(){var t=new x,n=e({a:t,b:t});return!(n&&n.a===n.b&&n.a instanceof x)}))},ie=function(e){return!s((function(){var t=e(new r.AggregateError([1],X,{cause:3}));return"AggregateError"!=t.name||1!=t.errors[0]||t.message!=X||3!=t.cause}))},oe=r.structuredClone,re=i||!ne(oe)||!ie(oe),ae=!oe&&te((function(e){return new F(X,{detail:e}).detail})),le=te(oe)||ae,se=function(e){throw new N("Uncloneable type: "+e,Q)},ce=function(e,t){throw new N((t||"Cloning")+" of "+e+" cannot be properly polyfilled in this engine",Q)},ue=function(e,t){if(m(e)&&se("Symbol"),!f(e))return e;if(t){if(K(t,e))return $(t,e)}else t=new j;var n,i,o,l,s,c,p,v,k,F,V=h(e),G=!1;switch(V){case"Array":o=[],G=!0;break;case"Object":o={},G=!0;break;case"Map":o=new j,G=!0;break;case"Set":o=new z,G=!0;break;case"RegExp":o=new RegExp(e.source,T(e));break;case"Error":switch(i=e.name,i){case"AggregateError":o=a("AggregateError")([]);break;case"EvalError":o=M();break;case"RangeError":o=P();break;case"ReferenceError":o=E();break;case"SyntaxError":o=I();break;case"TypeError":o=A();break;case"URIError":o=O();break;case"CompileError":o=B();break;case"LinkError":o=D();break;case"RuntimeError":o=R();break;default:o=x()}G=!0;break;case"DOMException":o=new N(e.message,e.name),G=!0;break;case"DataView":case"Int8Array":case"Uint8Array":case"Uint8ClampedArray":case"Int16Array":case"Uint16Array":case"Int32Array":case"Uint32Array":case"Float32Array":case"Float64Array":case"BigInt64Array":case"BigUint64Array":n=r[V],f(n)||ce(V),o=new n(ue(e.buffer,t),e.byteOffset,"DataView"===V?e.byteLength:e.length);break;case"DOMQuad":try{o=new DOMQuad(ue(e.p1,t),ue(e.p2,t),ue(e.p3,t),ue(e.p4,t))}catch(U){le?o=le(e):ce(V)}break;case"FileList":if(n=r.DataTransfer,d(n)){for(l=new n,s=0,c=b(e);s<c;s++)l.items.add(ue(e[s],t));o=l.files}else le?o=le(e):ce(V);break;case"ImageData":try{o=new ImageData(ue(e.data,t),e.width,e.height,{colorSpace:e.colorSpace})}catch(U){le?o=le(e):ce(V)}break;default:if(le)o=le(e);else switch(V){case"BigInt":o=w(e.valueOf());break;case"Boolean":o=w(Z(e));break;case"Number":o=w(J(e));break;case"String":o=w(Y(e));break;case"Date":o=new S(q(e));break;case"ArrayBuffer":n=r.DataView,n||"function"==typeof e.slice||ce(V);try{if("function"==typeof e.slice)o=e.slice(0);else for(c=e.byteLength,o=new ArrayBuffer(c),k=new n(e),F=new n(o),s=0;s<c;s++)F.setUint8(s,k.getUint8(s))}catch(U){throw new N("ArrayBuffer is detached",Q)}break;case"SharedArrayBuffer":o=e;break;case"Blob":try{o=e.slice(0,e.size,e.type)}catch(U){ce(V)}break;case"DOMPoint":case"DOMPointReadOnly":n=r[V];try{o=n.fromPoint?n.fromPoint(e):new n(e.x,e.y,e.z,e.w)}catch(U){ce(V)}break;case"DOMRect":case"DOMRectReadOnly":n=r[V];try{o=n.fromRect?n.fromRect(e):new n(e.x,e.y,e.width,e.height)}catch(U){ce(V)}break;case"DOMMatrix":case"DOMMatrixReadOnly":n=r[V];try{o=n.fromMatrix?n.fromMatrix(e):new n(e)}catch(U){ce(V)}break;case"AudioData":case"VideoFrame":u(e.clone)||ce(V);try{o=e.clone()}catch(U){se(V)}break;case"File":try{o=new File([e],e.name,e)}catch(U){ce(V)}break;case"CryptoKey":case"GPUCompilationMessage":case"GPUCompilationInfo":case"ImageBitmap":case"RTCCertificate":case"WebAssembly.Module":ce(V);default:se(V)}}if(W(t,e,o),G)switch(V){case"Array":case"Object":for(p=H(e),s=0,c=b(p);s<c;s++)v=p[s],g(o,v,ue(e[v],t));break;case"Map":e.forEach((function(e,n){W(o,ue(n,t),ue(e,t))}));break;case"Set":e.forEach((function(e){L(o,ue(e,t))}));break;case"Error":_(o,"message",ue(e.message,t)),y(e,"cause")&&_(o,"cause",ue(e.cause,t)),"AggregateError"==i&&(o.errors=ue(e.errors,t));case"DOMException":C&&_(o,"stack",ue(e.stack,t))}return o},de=oe&&!s((function(){var e=new ArrayBuffer(8),t=oe(e,{transfer:[e]});return 0!=e.byteLength||8!=t.byteLength})),fe=function(e,t){if(!f(e))throw A("Transfer option cannot be converted to a sequence");var n=[];p(e,(function(e){U(n,v(e))}));var i,o,a,l,s,c,m,y=0,g=b(n);if(de){l=oe(n,{transfer:n});while(y<g)W(t,n[y],l[y++])}else while(y<g){if(i=n[y++],K(t,i))throw new N("Duplicate transferable",Q);switch(o=h(i),o){case"ImageBitmap":a=r.OffscreenCanvas,d(a)||ce(o,ee);try{c=new a(i.width,i.height),m=c.getContext("bitmaprenderer"),m.transferFromImageBitmap(i),s=c.transferToImageBitmap()}catch(_){}break;case"AudioData":case"VideoFrame":u(i.clone)&&u(i.close)||ce(o,ee);try{s=i.clone(),i.close()}catch(_){}break;case"ArrayBuffer":case"MessagePort":case"OffscreenCanvas":case"ReadableStream":case"TransformStream":case"WritableStream":ce(o,ee)}if(void 0===s)throw new N("This object cannot be transferred: "+o,Q);W(t,i,s)}};o({global:!0,enumerable:!0,sham:!de,forced:re},{structuredClone:function(e){var t,n=k(arguments.length,1)>1&&null!=arguments[1]?v(arguments[1]):void 0,i=n?n.transfer:void 0;return void 0!==i&&(t=new j,fe(i,t)),ue(e,t)}})},b1e3:function(e,t,n){},b2f1:function(e,t,n){"use strict";n("1c2f")}}]);