(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-9e908b00"],{"0e1b":function(t,e,a){"use strict";var i=a("c7eb"),n=a("1da1"),s=(a("b0c0"),a("d81d"),a("d3b7"),a("159b"),a("99af"),a("e9c4"),a("b64b"),a("ac1f"),a("00b4"),a("caad"),a("cb29"),a("25f0"),a("a434"),a("1276"),a("a15b"),a("fb6a"),a("5319"),{data:function(){return{anchorExampleCss:"\n.anchor-point-panel {\n    position: relative;\n}\n.anchor-point-panel .container {\n  min-width: 200px;\n  border: 1px solid var(--theme);\n  padding: 4px 10px;\n  background-color: rgba(0, 0, 0,0.7);\n  overflow: hidden;\n  color: #FFFFFF;\n  position: absolute;\n  pointer-events: none;\n}\n.container .corner-marker {\n    position: absolute;\n    width: 15px;\n    height: 15px;\n    border: 1px solid #19B2FF;\n}\n.corner-marker.one {\n    top: 0;\n    left: 0;\n    border-right: none;\n    border-bottom: none;\n}\n.corner-marker.two {\n    top: 0;\n    right: 0;\n    border-left: none;\n    border-bottom: none;\n}\n.corner-marker.three {\n    bottom: 0;\n    left: 0;\n    border-right: none;\n    border-top: none;\n}\n.corner-marker.four {\n    bottom: 0;\n    right: 0;\n    border-left: none;\n    border-top: none;\n}\n.anchor-point-panel .panel-title {\n    position: relative;\n    height: 30px;\n    margin-bottom: 10px;\n    display: flex;\n    align-items: center;\n    overflow: hidden;\n    white-space: nowrap;\n    text-align: center;\n    background: linear-gradient(90deg, rgba(0,147,255,0) 0%, rgba(0,147,255,0.2) 49%, rgba(0,147,255,0) 100%);\n}\n.anchor-point-panel .panel-title:before {\n    content: '';\n    width: 50px;\n    height: 2px;\n    background: rgba(25,178,255,0.9);\n    border-radius: 0 0 2px 2px;\n    top: 0;\n    left: 50%;\n    position: absolute;\n    margin-left: -25px;\n}\n.anchor-point-panel .panel-title>span{\n    width: 100%;\n    font-size: 14px;\n}\n.anchor-point-panel .panel-close {\n  font-style: normal;\n  font-size: 24px;\n  cursor: pointer;\n  margin-top: -5px;\n  margin-left: 15px;\n  pointer-events: auto;\n}\n.anchor-point-panel .panel-close:before {\n  content: \"\\00D7\";\n}\n.anchor-point-panel .panel-close:hover {\nopacity: 0.8\n}\n.anchor-point-panel .container .item-list {\n  height: 24px;\n  display: flex;\n  justify-content: space-between;\n  flex-wrap: nowrap;\n  align-items: center;\n  white-space: nowrap;\n  margin-bottom: 2px;\n}\n.anchor-point-panel .container .item-list .desc {\n  margin-left: 15px;\n  color: #99FFFF;\n}\n.anchor-point-panel .panel-point-img img {\n    width: 100%;\n    height: 100%;\n}\n.anchor-point-panel.anchor-leadWire .panel-point-img:before,\n.anchor-point-panel.anchor-leadWire .panel-point-img:after {\n    content: '';\n}\n\n.anchor-point-panel.anchor-leadWire-default .container{\n    transform: translateX(-50%);\n    margin-left: 16px;\n    bottom:100%;\n}\n\n.anchor-point-panel.anchor-leadWire-left .container{\n    bottom:calc(100% + 8px);\n    right: 100%;\n    margin-right: 20px;\n}\n.anchor-point-panel.anchor-leadWire-left .panel-point-img:before {\n    width: 1px;\n    height: 35px;\n    background-color: var(--theme);\n    position: absolute;\n    top: -34px;\n    left: 50%;\n    border-top-right-radius: 10px;\n    transform: rotateZ(-20deg);\n    margin-left: -6px;\n}\n\n.anchor-point-panel.anchor-leadWire-left .panel-point-img:after {\n    width: 24px;\n    height: 1px;\n    background-color: var(--theme);\n    position: absolute;\n    top: -34px;\n    right: 50%;\n    margin-right: 12px;\n}\n\n.anchor-point-panel.anchor-leadWire-middle .container{\n    bottom:calc(100% + 20px);\n    transform: translateX(-50%);\n    margin-left: 16px;\n}\n.anchor-point-panel.anchor-leadWire-middle .panel-point-img:before {\n    width: 1px;\n    height: 20px;\n    background-color: var(--theme);\n    position: absolute;\n    top: -20px;\n    left: 50%;\n}\n\n.anchor-point-panel.anchor-leadWire-right .container{\n    bottom:calc(100% + 8px);\n    left: 100%;\n    margin-left: 20px;\n}\n.anchor-point-panel.anchor-leadWire-right .panel-point-img:before {\n    width: 1px;\n    height: 35px;\n    background-color: var(--theme);\n    position: absolute;\n    top: -34px;\n    left: 50%;\n    border-top-left-radius: 10px;\n    transform: rotateZ(20deg);\n    margin-left: 6px;\n}\n.anchor-point-panel.anchor-leadWire-right .panel-point-img:after {\n    width: 24px;\n    height: 1px;\n    background-color: var(--theme);\n    position: absolute;\n    top: -34px;\n    left: 50%;\n    margin-left: 12px;\n}",billboardExampleCss:"\n                .anchor-point-panel .container {\n                  min-width: 200px;\n                  border: 1px solid var(--theme);\n                  padding: 4px 10px;\n                  background-color: rgba(0, 0, 0,0.7);\n                  overflow: hidden;\n                  color: #FFFFFF;\n                  position: relative;\n                }\n                .container .corner-marker {\n                    position: absolute;\n                    width: 15px;\n                    height: 15px;\n                    border: 1px solid #19B2FF;\n                }\n                .corner-marker.one {\n                    top: 0;\n                    left: 0;\n                    border-right: none;\n                    border-bottom: none;\n                }\n                .corner-marker.two {\n                    top: 0;\n                    right: 0;\n                    border-left: none;\n                    border-bottom: none;\n                }\n                .corner-marker.three {\n                    bottom: 0;\n                    left: 0;\n                    border-right: none;\n                    border-top: none;\n                }\n                .corner-marker.four {\n                    bottom: 0;\n                    right: 0;\n                    border-left: none;\n                    border-top: none;\n                }\n                .anchor-point-panel .panel-title {\n                    position: relative;\n                    height: 30px;\n                    margin-bottom: 10px;\n                    display: flex;\n                    align-items: center;\n                    overflow: hidden;\n                    white-space: nowrap;\n                    text-align: center;\n                    background: linear-gradient(90deg, rgba(0,147,255,0) 0%, rgba(0,147,255,0.2) 49%, rgba(0,147,255,0) 100%);\n                }\n                .anchor-point-panel .panel-title:before {\n                    content: '';\n                    width: 50px;\n                    height: 2px;\n                    background: rgba(25,178,255,0.9);\n                    border-radius: 0 0 2px 2px;\n                    top: 0;\n                    left: 50%;\n                    position: absolute;\n                    margin-left: -25px;\n                }\n                .anchor-point-panel .panel-title>span{\n                    width: 100%;\n                    font-size: 14px;\n                }\n                .anchor-point-panel .container .item-list {\n                  height: 24px;\n                  display: flex;\n                  justify-content: space-between;\n                  flex-wrap: nowrap;\n                  align-items: center;\n                  white-space: nowrap;\n                }\n                .anchor-point-panel .container .item-list .title {\n                  color: #DDDDDD;\n                }\n                .anchor-point-panel .container .item-list .desc {\n                  margin-left: 15px;\n                  color: #99FFFF;\n                }\n                .anchor-point-panel.anchor-leadWire .panel-point-img:before,\n                .anchor-point-panel.anchor-leadWire .panel-point-img:after {\n                    content: '';\n                }\n                \n                .anchor-point-panel.anchor-leadWire-default .panel-point-img{\n                    width: 100%;\n                    text-align: center;\n                }\n                .anchor-point-panel.anchor-leadWire-left .panel-point-img{\n                    width: 100%;\n                    text-align: right;\n                    position: relative;\n                }\n                .anchor-point-panel.anchor-leadWire-default .container {\n                    transform: unset;\n                    margin-left: unset;\n                    bottom: unset;\n                }\n                .anchor-point-panel.anchor-leadWire-left .container{\n                    bottom: unset;\n                    right: unset;\n                    margin-right: 50px;\n                }\n                .anchor-point-panel.anchor-leadWire-left .panel-point-img:before {\n                    width: 1px;\n                    height: 35px;\n                    background-color: var(--theme);\n                    position: absolute;\n                    bottom: 100%;\n                    right: 20px;\n                    top: unset;\n                    left: unset;\n                    border-top-right-radius: 10px;\n                    transform: rotateZ(-20deg);\n                }\n                \n                .anchor-point-panel.anchor-leadWire-left .panel-point-img:after {\n                    width: 24px;\n                    height: 1px;\n                    background-color: var(--theme);\n                    position: absolute;\n                    top: -34px;\n                    right: 27px;\n                    margin-right: unset;\n                }\n                \n                .anchor-point-panel.anchor-leadWire-middle .container{\n                    margin-bottom: 20px;\n                    bottom: unset;\n                    transform: unset;\n                    margin-left: unset;\n                }\n                .anchor-point-panel.anchor-leadWire-middle .panel-point-img{\n                    width: 100%;\n                    text-align: center;\n                    position: relative;\n                }\n                .anchor-point-panel.anchor-leadWire-middle .panel-point-img:before {\n                    width: 1px;\n                    height: 20px;\n                    background-color: var(--theme);\n                    position: absolute;\n                    bottom: 100%;\n                    left: 50%;\n                }\n                \n                .anchor-point-panel.anchor-leadWire-right .container{\n                    bottom: unset;\n                    left: unset;\n                    margin-left: 50px;\n                }\n                .anchor-point-panel.anchor-leadWire-right .panel-point-img{\n                    width: 100%;\n                    text-align: left;\n                    position: relative;\n                }\n                .anchor-point-panel.anchor-leadWire-right .panel-point-img:before {\n                    width: 1px;\n                    height: 35px;\n                    background-color: var(--theme);\n                    position: absolute;\n                    bottom: 100%;\n                    left: 20px;\n                    border-top-left-radius: 10px;\n                    transform: rotateZ(20deg);\n                    top: unset;\n                    margin-left: unset;\n                }\n                .anchor-point-panel.anchor-leadWire-right .panel-point-img:after {\n                    width: 24px;\n                    height: 1px;\n                    background-color: var(--theme);\n                    position: absolute;\n                    top: -34px;\n                    left: 26px;\n                    margin-left: unset;\n                }"}},created:function(){this.setPanelTemplateScene()},methods:{setBasicAnchorImage:function(t){var e="",a=0==t[6].annotationRadio?"annotation":"billboard";return this.styleFormDatas.datas[a].setType.checkedImg=t[5].checkedType,0==parseInt(t[5].checkedType)&&(this.styleFormDatas.datas[a].setType.customSrc=t[5].fileSrc),-1!=parseInt(t[5].checkedType)&&(e=t[5].fileSrc),e},setCustomAnchorType:function(t){var e=this.dragData.type;0==t?this.$set(this.styleFormDatas.datas[e].list[4],"optionState",!1):this.$set(this.styleFormDatas.datas[e].list[4],"optionState",!0)},setAnchorPanelLeadWireType:function(t,e){this.$set(this.styleFormDatas.datas[e].list[5],"optionState",t)},setDefaultVideoAnchorHTML:function(t,e,a){var i=this.setLeadWireDataAnchorCls(t),n=this.setBasicAnchorImage(t),s="width:".concat(t[1].width+"px",";height:").concat(t[1].height+"px",";"),o="",r="",l="";""!=n?(o='<img class="point-img" src="'.concat(n,'">'),r='<i class="panel-close"></i>'):s="width:".concat(t[1].width+"px",";height:0};"),""!=n&&"alwaysShow"!=e.annotation.panelToggleMode&&"mouseover"!=e.annotation.panelToggleMode||(r='<i class="panel-close" style="display: none"></i>'),window.myVideo&&window.myVideo[this.dragData.id]&&window.myVideoId===a&&(window.myVideo[this.dragData.id]&&window.myVideo[this.dragData.id].pause(),window.myVideo[this.dragData.id]&&window.myVideo[this.dragData.id].dispose());var d=/\.(m3u8|hls|mp4)$/;l=d.test(e.annotation.videoLink)?e.annotation.videoLink.indexOf(".m3u8")>-1?'\n                    <video id="myVideo-'.concat(a,'" class="video-js vjs-default-skin vjs-big-play-centered" loop="loop" controls preload="auto" muted="\'muted" width="240" style="width: 100%" data-setup=\'{"autoplay": true}\'> \n                        <source id="source" src="').concat(e.annotation.videoLink,"\" type='application/x-mpegURL'></source>\n                    </video>\n                    "):'\n                    <video id="myVideo-'.concat(a,'" class="video-js vjs-default-skin vjs-big-play-centered" loop="loop" controls preload="auto" muted="\'muted" width="240" style="width: 100%" data-setup=\'{"autoplay": true}\'> \n                        <source id="source" src="').concat(e.annotation.videoLink,'"></source>\n                    </video>\n                    '):'<iframe width="240" class="video-iframe" style="border:none;" src="'.concat(e.annotation.videoLink,'">\n                </iframe>');var c="margin-left:".concat(t[1].width/2,"px;");if("alwaysShow"!==e.annotation.panelToggleMode){c+="display:none";var p=i;i=p.split(" ").splice(1).toString()}c+="width: 260px;";var h='<div class="anchor-point-panel '.concat(i,'" style="').concat(s,'">\n                                    <div class="container" style="pointer-events:auto;').concat(c,'">\n                                        <div class="panel-title" style="justify-content:center;margin-bottom:0;">\n                                            <span style="margin-top:5px;">').concat(e.annotation.title,"</span>\n                                            ").concat(r,"\n                                        </div>\n                                        <div>\n                                            ").concat(l,'\n                                        </div>\n                                        <div>\n                                            <span class="corner-marker one"></span>\n                                            <span class="corner-marker two"></span>\n                                            <span class="corner-marker three"></span>\n                                            <span class="corner-marker four"></span>\n                                        </div>\n                                    </div>\n                                    <div class="panel-point-img" style="').concat(s,'">\n                                        ').concat(o,"\n                                    </div>\n                                </div>");return h},setDefaultDataAnchorHTML:function(t,e){var a=this.setLeadWireDataAnchorCls(t),i=this.setBasicAnchorImage(t),n=null,s="",o="";""!=i&&(s='<img class="point-img" src="'.concat(i,'">'),o='<i class="panel-close"></i>'),""!=i&&"alwaysShow"!=e.annotation.panelToggleMode&&"mouseover"!=e.annotation.panelToggleMode||(o='<i class="panel-close" style="display: none"></i>');var r="margin-left:".concat(t[1].width/2,"px;");if("alwaysShow"!==e.annotation.panelToggleMode){r+="display:none;";var l=a;a=l.split(" ").splice(1).toString()}return r+="width: 260px;",n='<div class="anchor-point-panel '.concat(a,'" style="${anchorStyle}">\n                                    <div class="container" style="').concat(r,'">\n                                        <div class="panel-title">\n                                            <span>${panelTitle}</span>\n                                            ').concat(o,'\n                                        </div>\n                                        <loop></loop>\n                                        <div>\n                                            <span class="corner-marker one"></span>\n                                            <span class="corner-marker two"></span>\n                                            <span class="corner-marker three"></span>\n                                            <span class="corner-marker four"></span>\n                                        </div>\n                                    </div>\n                                    <div class="panel-point-img" style="${anchorStyle}">\n                                        ').concat(s,"\n                                    </div>\n                                </div>"),n},setDefaultDataBillboardHTML:function(t){var e=this.setLeadWireDataAnchorCls(t),a=this.setBasicAnchorImage(t),i="";""!=a&&(i='<img class="point-img" src="'.concat(a,'" style="${anchorStyle}">'));var n='<div class="anchor-point-panel '.concat(e,'">\n                                <div class="container">\n                                    <div class="panel-title">\n                                        <span>${panelTitle}</span>\n                                    </div>\n                                    <loop></loop>\n                                    <div>\n                                        <span class="corner-marker one"></span>\n                                        <span class="corner-marker two"></span>\n                                        <span class="corner-marker three"></span>\n                                        <span class="corner-marker four"></span>\n                                    </div>\n                                </div>\n                                <div class="panel-point-img">\n                                    ').concat(i,"\n                                </div>\n                            </div>");return n},setLeadWireDataAnchorCls:function(t){var e="anchor-leadWire ";switch(parseInt(t[4].checkedType)){case 0:e+="anchor-leadWire-default";break;case 1:e+="anchor-leadWire-left";break;case 2:e+="anchor-leadWire-middle";break;case 3:e+="anchor-leadWire-right";break}return e},setDefaultDataAnchorCss:function(t,e,a){var i=document.querySelector("."+e[0]);if(null!=i)return!1;if(""!=a){var n=document.createTextNode(a),s=document.createElement("style"),o=document.querySelector("head");s.id="annotation-css-"+t,s.classList=e.join(" "),s.type="text/css",s.appendChild(n),o.appendChild(s)}},setCustomDataAnchorHTML:function(t,e,a){var i=a,n=i.indexOf("<loop>"),s=i.indexOf("</loop>"),o=i.slice(n,s+7),r="";e.dataList.forEach((function(t){var e="",a="";"img"===t.valType?(e='<img width="100%" height="100%" src="'.concat(t.value,'" alt="">'),a="height:auto;"):e="link"===t.valType?'<a href="'.concat(t.value,'" target="_blank">点击查看</a>'):t.value,r+='<div class="item-list" style="'.concat(a,'">\n                                <span class="title">').concat(t.key,'</span>\n                                <span class="desc ').concat(t.valType,'">\n                                    ').concat(e,"\n                                </span>\n                            </div>")}));var l="width:30px;height:30px;",d=i.replace(o,r),c=new Function("panelTitle","anchorStyle","return `".concat(d,"`;"))(e.title,l);return c},setPanelTemplateScene:function(){var t=document.querySelector("#default-panel-template-scene");if(null!=t)return!1;var e=document.createElement("template");e.id="default-panel-template-scene",e.innerHTML='<div class="anchor-point-panel" style="&dollar;{anchorStyle}">\n            <div class="container">\n                <div class="panel-title">\n                    <span>&dollar;{panelTitle}</span>\n                    <i class="panel-close"></i>\n                </div>\n                <loop>\n                    <div class="item-list">\n                        <span class="title">&dollar;{loop.key}</span>\n                        <span class="desc">&dollar;{loop.value}</span>\n                    </div>\n                </loop>\n                    <div>\n                        <span class="corner-marker one"></span>\n                        <span class="corner-marker two"></span>\n                        <span class="corner-marker three"></span>\n                        <span class="corner-marker four"></span>\n                    </div>\n            </div>\n            <div style="&dollar;{anchorStyle}">\n                <img class="point-img" src="http://www.probim.cn:8088/bimexample/img/point.png">\n            </div>\n        </div>',document.body.appendChild(e)},setDataAnnotationDefaultEvent:function(t){var e="",a="";return t.annotation.panelToggleMode&&"alwaysShow"!=t.annotation.panelToggleMode&&("mouseover"===t.annotation.panelToggleMode?e='annotation.addEventListener("mouseover",e=>{if(e.target.className=="point-img"){annotation.querySelector(".container").style.display="block";annotation.firstChild.classList.add("anchor-leadWire");}});annotation.addEventListener("mouseleave",e=>{annotation.querySelector(".container").style.display="none";annotation.firstChild.classList.remove("anchor-leadWire");});':(e='annotation.addEventListener("'+t.annotation.panelToggleMode+'",e=>{if(e.target.className=="point-img"){annotation.querySelector(".container").style.display="block";annotation.firstChild.classList.add("anchor-leadWire");}});',a='annotation.querySelector(".panel-close").addEventListener("click",e=>{e.stopPropagation();annotation.querySelector(".container").style.display="none";annotation.firstChild.classList.remove("anchor-leadWire");});')),e+='annotation.addEventListener("mouseover",()=>{annotation.style.zIndex=99});annotation.addEventListener("mouseleave",()=>{annotation.style.zIndex=9});',e+a},setHTML:function(t,e){var a=t;if(null!=e&&void 0!=e&&""!=e){var i=t,n=i.indexOf("<loop>"),s=i.indexOf("</loop>"),o=i.slice(n,s+7),r=o.substring(6,o.length-7),l=new Function("data","return data.map((loop,index)=>{return `".concat(r,'`}).join("")'))(e.dataList),d="width:".concat(e.width+"px",";height:").concat(e.height+"px",";"),c=i.replace(o,l);a=new Function("panelTitle","anchorStyle","return `".concat(c,"`;"))(e.title,d)}var p=(new DOMParser).parseFromString(a,"text/html"),h=p.body.firstChild;return h}}}),o=a("ed08"),r=a("b893"),l=a("5a94");e["a"]={mixins:[s],data:function(){return{styleFormDatas:{id:"",thumn:"",dataType:"",typeName:"",name:"",longitude:0,latitude:0,altitude:0,rotation:[0,0,0],offset:[0,0,0],validate:{longitude:!0,latitude:!0,altitude:!0,rotation:!0},datas:{model:{type:"model",name:this.$t("featureDatas.model.name"),list:[{optionState:!0,phaseValue:-1,phaseOption:[{options:[]},{options:[]}]},{title:this.$t("featureSetting.advanced.model.label"),setOptions:[{title:this.$t("featureSetting.advanced.model.label1"),state:!1,attr:"space"},{title:this.$t("featureSetting.advanced.model.label2"),state:!1,attr:"area"},{title:this.$t("featureSetting.advanced.model.label3"),state:!1,attr:"part"},{title:this.$t("featureSetting.advanced.model.label4"),state:!0,attr:"texture"}],optionState:!0}]},dem:{type:"dem",name:this.$t("featureDatas.dem.name"),list:[{title:this.$t("formRelational.address.label"),name:"",value:"mapbox://mapbox.mapbox-terrain-dem-v1",defaultAddress:"mapbox://mapbox.mapbox-terrain-dem-v1",optionState:!0},{title:this.$t("formRelational.opacity.label"),value:1,optionState:!1}]},_3dTiles:{type:"_3dTiles",name:this.$t("featureDatas._3dTiles.subName"),list:[{title:this.$t("formRelational.address.label"),value:"",optionState:!0,validate:!0,validateState:null}]},wmts:{type:"wmts",name:this.$t("featureDatas.wmts.name"),list:[{title:this.$t("formRelational.address.label"),value:"",optionState:!0,validate:!0,optionalList:[{itemImg:"./image/wmts/wmts1.png",label:this.$t("featureDatas.wmts.extend.mapWorld"),pathname:"/img_w/wmts?tk=1e1e425c920f09544d68abc6d3817d04&SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&FORMAT=tiles",url:""},{itemImg:"./image/wmts/wmts2.png",label:"ArcGIS Online",url:"https://services.arcgisonline.com/arcgis/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"}]}]},wms:{type:"wms",name:this.$t("featureDatas.wms.name"),list:[{title:this.$t("formRelational.address.label"),value:"https://img.nj.gov/imagerywms/Natural2015?bbox={bbox-epsg-3857}&format=image/png&service=WMS&version=1.1.1&request=GetMap&srs=EPSG:3857&transparent=true&width=256&height=256&layers=Natural2015",optionState:!0,validate:!0}]},tms:{type:"tms",name:this.$t("featureDatas.tms.name"),list:[{title:this.$t("formRelational.address.label"),value:"",optionState:!0,validate:!0}]},_3dBuilding:{type:"_3dBuilding",name:this.$t("featureDatas._3dBuilding.name"),list:[{title:this.$t("formRelational.color.label"),value:"rgb(215, 245, 255)"},{title:this.$t("formRelational.opacity.label"),value:1},{title:this.$t("featureSetting.style._3dBuilding.label"),value:12}]},annotation:{type:"annotation",name:this.$t("featureDatas.annotation.name"),list:[{clickCoordinate:!0,title:this.$t("formRelational.basePoint.label1"),tooltip:this.$t("formRelational.basePoint.placeholder"),optionState:!0},{title:this.$t("formRelational.translate.label"),optionState:!0},{title:this.$t("featureSetting.advanced.model.label"),width:30,height:30,optionState:!0,distanceType:!0,distance:500,tipContent:this.$t("featureSetting.style.anchorPoint.tooltip1")},{title:this.$t("others.type"),radio:"0",needPanel:!1,optionState:!0},{title:this.$t("featureSetting.style.anchorPoint.label1"),radio:"0",defaultImg:["".concat("","image/point_1.png"),"".concat("","image/point_2.png"),"".concat("","image/point_3.png"),"".concat("","image/point_4.png")],checkedImg:0,imgLink:"",htmlCode:"",cssCode:"",optionState:!1},{title:this.$t("featureSetting.style.anchorPoint.label10"),defaultImg:[a("f6a2"),a("113c"),a("fc5e"),a("2007")],checkedType:0,optionState:!1}],setType:{}},billboard:{type:"billboard",name:this.$t("featureDatas.billboard.name"),list:[{clickCoordinate:!0,title:this.$t("formRelational.basePoint.label1"),tooltip:this.$t("formRelational.basePoint.placeholder"),optionState:!0},{title:this.$t("formRelational.translate.label"),optionState:!0},{title:this.$t("featureSetting.style.anchorPoint.label4"),width:4,height:2,optionState:!0,lockScale:!0,locked:!1,tips:!0,tipContent:this.$t("featureSetting.style.anchorPoint.tooltip2")},{title:this.$t("others.type"),radio:"0",needPanel:!1,optionState:!0},{title:this.$t("featureSetting.style.anchorPoint.label1"),radio:"0",defaultImg:["".concat("","image/point_1.png"),"".concat("","image/point_2.png"),"".concat("","image/point_3.png"),"".concat("","image/point_4.png")],checkedImg:0,imgLink:"",htmlCode:"",cssCode:"",optionState:!1},{title:this.$t("featureSetting.style.anchorPoint.label10"),defaultImg:[a("f6a2"),a("113c"),a("fc5e"),a("2007")],checkedType:0,optionState:!1}],setType:{}},gltf:{type:"glTF",name:this.$t("featureDatas.gltf.subName"),list:[{title:this.$t("formRelational.address.label"),value:"",optionState:!0,needImageSource:!1,validate:!0,validateState:null,addType:0,addModeList:[{label:this.$t("dialog.materialLibrary.tooltip"),value:0},{label:this.$t("dialog.materialLibrary.tooltip1"),value:1}]}]},fbx:{type:"fbx",name:this.$t("featureDatas.fbx.subName"),list:[{title:this.$t("formRelational.address.label"),value:"",validate:!0,validateState:null,addType:0,addModeList:[{label:this.$t("dialog.materialLibrary.tooltip"),value:0},{label:this.$t("dialog.materialLibrary.tooltip1"),value:1}]}]},panorama:{type:"panorama",name:this.$t("featureDatas.panorama.name"),list:[{title:this.$t("formRelational.address.label"),value:"",optionState:!0,validate:!0,validateState:null},{title:this.$t("formRelational.radius.label"),value:20,optionState:!0},{title:this.$t("featureSetting.style.panorama.deepPath"),value:"",optionState:!0}]},shield:{type:"shield",name:this.$t("featureDatas.shield.name"),list:[{title:this.$t("formRelational.address.label"),value:"",optionState:!1},{clickCoordinate:!0,title:this.$t("formRelational.basePoint.label1"),tooltip:this.$t("formRelational.basePoint.placeholder"),optionState:!0}]},heatmap:{type:"heatmap",name:this.$t("featureDatas.heatmap.name"),list:[{title:this.$t("menuIconName.setUp"),optionState:!0}]},video:{type:"video",name:this.$t("featureDatas.video.name"),list:[{title:this.$t("formRelational.address.label"),value:"",optionState:!0,validate:!0,validateState:null},{title:this.$t("featureSetting.style.video.coordinate"),cor:[{id:1,position:{x:0,y:0}},{id:2,position:{x:0,y:0}},{id:3,position:{x:0,y:0}},{id:4,position:{x:0,y:0}}]}]},ring:{type:"ring",name:this.$t("featureDatas.ring.name"),list:[{title:this.$t("formRelational.address.label"),value:"",optionState:!1},{clickCoordinate:!0,title:this.$t("formRelational.basePoint.label1"),tooltip:this.$t("formRelational.basePoint.placeholder"),optionState:!0}]},ripplewall:{type:"ripplewall",name:this.$t("featureDatas.ripplewall.name"),list:[{title:this.$t("menuIconName.setUp"),optionState:!0},{title:this.$t("dialog.coordinate.name"),position:[],units:[]}]},radar:{type:"radar",name:this.$t("featureDatas.radar.name"),list:[{title:this.$t("formRelational.address.label"),value:"",optionState:!1},{clickCoordinate:!0,title:this.$t("formRelational.basePoint.label1"),tooltip:this.$t("formRelational.basePoint.placeholder"),optionState:!0}]},shp:{type:"shp",name:this.$t("featureDatas.shp.name"),list:[{title:this.$t("formRelational.address.label"),value:"",optionState:!0,validate:!0,validateState:null}]},geoJSON:{type:"geoJSON",name:this.$t("featureDatas.geoJSON.name"),list:[{title:this.$t("formRelational.address.label"),value:"",optionState:!0,validate:!0,validateState:null}]},vectorextrude:{type:"vectorextrude",name:this.$t("featureDatas.vectorextrude.name"),list:[{title:this.$t("formRelational.address.label"),value:"",optionState:!0,validate:!0,validateState:null}]},polyline:{type:"polyline",name:this.$t("featureDatas.polyline.name"),list:[{title:this.$t("formRelational.image.label1"),value:"",optionState:!0,validateImg:!0,validateState:null},{title:this.$t("dialog.coordinate.name"),position:[[0,0,0],[0,0,0]],optionState:!0,addOption:!0,freeSketchGeometry:!0,tooltip:this.$t("formRelational.basePoint.tooltip"),optionItem:{name:"position",value:[0,0,0]}}]},polygon:{type:"polygon",name:"",list:[{title:this.$t("formRelational.image.label1"),value:"",optionState:!1,needImageSource:!0,validate:!0,validateState:null},{title:this.$t("dialog.coordinate.name"),position:[[0,0],[0,0],[0,0]],optionState:!0,freeSketchGeometry:!0,addOption:!0,tooltip:this.$t("formRelational.basePoint.tooltip"),optionItem:{name:"position",value:[0,0]}},{title:this.$t("formRelational.color.label"),value:"rgb(255, 50, 0)"},{title:this.$t("formRelational.opacity.label"),value:1},{title:this.$t("formRelational.baseTop.label"),value:10},{title:this.$t("formRelational.top.label"),value:10},{title:this.$t("formRelational.speed.label"),value:1},{title:this.$t("formRelational.showLine.label"),value:!1}]},flame:{type:"flame",name:this.$t("featureDatas.flame.name"),list:[{clickCoordinate:!0,title:this.$t("formRelational.basePoint.label"),tooltip:this.$t("formRelational.basePoint.placeholder"),optionState:!0}]},smoke:{type:"smoke",name:this.$t("featureDatas.smoke.name"),list:[{clickCoordinate:!0,title:this.$t("formRelational.basePoint.label"),tooltip:this.$t("formRelational.basePoint.placeholder"),optionState:!0}]},"batch-extrude":{type:"batch_extrude",name:this.$t("featureDatas['batch-extrude'].name"),list:[{title:this.$t("featureSetting.style.batchExtrude.label8"),position:[[-1,-1],[1,-1],[1,1],[-1,1]],radius:2,optionState:!0,addOption:!0,tooltip:this.$t("formRelational.basePoint.tooltip"),type:"polygon",optionItem:{name:"position",value:[0,0]}}]},kml:{type:"kml",name:this.$t("featureDatas.kml.name"),list:[{title:this.$t("formRelational.address.label"),value:"",optionState:!0,validate:!0,validateState:null}]},advanced_envmap:{list:[{title:this.$t("formRelational.image.label"),value:3},{title:this.$t("topToolBarMenu.advanced.children.advanced_envmap.settings.label1"),value:30},{title:this.$t("topToolBarMenu.advanced.children.advanced_envmap.settings.label2"),value:.7}]},advanced_setting:{list:[{title:this.$t("topToolBarMenu.advanced.children.advanced_setting.settings.label"),value:0},{title:this.$t("topToolBarMenu.advanced.children.advanced_setting.settings.label1"),value:8192},{title:this.$t("topToolBarMenu.advanced.children.advanced_setting.settings.label2"),value:!1},{title:this.$t("topToolBarMenu.advanced.children.advanced_setting.settings.label3"),value:!1},{title:this.$t("topToolBarMenu.advanced.children.advanced_setting.settings.label4"),value:"rgb(0, 0.6274509803921569, 0.9764705882352941)"}]},pathAnimation:{type:"pathAnimation",name:this.$t("topToolBarMenu.advanced.children.pathAnimation.name"),list:[{},{title:this.$t("topToolBarMenu.advanced.children.pathAnimation.settings.label6"),position:[[0,0,0],[0,0,0]],optionState:!0}]},waterfall:{type:"waterfall",name:this.$t("featureDatas.waterfall.name"),list:[{clickCoordinate:!0,title:this.$t("formRelational.basePoint.label"),tooltip:this.$t("formRelational.basePoint.placeholder"),optionState:!0}]},trigger:{list:[{title:this.$t("topToolBarMenu.advanced.children.trigger.name")},{title:this.$t("featureSetting.triggerConditions.label6"),position:[]}]},projector:{elementIDs:[],position:[0,0,0],rotation:[0,0,0],fov:30,aspect:16/9,scale:1,helper:!0,list:[{title:"资源地址",value:"",validate:!0}]},snow:{width:200,height:200,speed:5,density:5},tailline:{type:"tailline",name:this.$t("featureDatas.tailline.name"),list:[{title:this.$t("formRelational.image.label1"),value:"",optionState:!0,validateImg:!0,validateState:null},{title:this.$t("dialog.coordinate.name"),position:[[0,0,0],[0,0,0]],optionState:!0,addOption:!0,freeSketchGeometry:!0,tooltip:this.$t("formRelational.basePoint.tooltip"),optionItem:{name:"position",value:[0,0,0]}}]}}}}},computed:{isVothing:function(){return this.$store.state.menuList.isVothing}},methods:{addModelFeature:function(t,e){var a=this;if(this.dragData.isDragData){var i={id:this.dragData.featureID,name:this.dragData.featureName};this.hanleAddModelFeature(t,i,"add",e)}else{var n=window.scene.features.get(this.dragData.id),s=""===n.version?-1:n.version;if(s!==t[0].phaseValue)this.$confirm(this.$t("messageTips.reloadModel"),this.$t("messageTips.tipsTitle"),{confirmButtonText:this.$t("menuIconName.confirm"),cancelButtonText:this.$t("messageTips.cancel"),type:"warning"}).then((function(){n.dispose(),window.scene.render();var i={id:a.dragData.modelID,name:a.dragData.name};a.hanleAddModelFeature(t,i,"edit",e)})).catch((function(){a.$parent.loading&&a.$parent.loading.close()}));else if(this.dragData.setFeatureID&&this.dragData.id!=this.styleFormDatas.id){n.dispose(),window.scene.render();var o={id:this.dragData.modelID,name:this.dragData.name};this.hanleAddModelFeature(t,o,"edit",e)}else{n.origin=[parseFloat(this.styleFormDatas.longitude+""),parseFloat(this.styleFormDatas.latitude+"")],n.altitude=parseFloat(this.styleFormDatas.altitude+""),n.rotation=this.styleFormDatas.rotation.map((function(t){return parseFloat(t+"")})),n.offset=this.styleFormDatas.offset.map((function(t){return parseFloat(t+"")})),n.id=this.styleFormDatas.id,n.priority=e.priority,n.always=e.always||!1,n.predefineCameraInfo=e.predefineCameraInfo,t[1].setOptions.forEach((function(t){n[t.attr]=t.state})),this.$emit("closeSet"),this.$nextTick((function(){a.$deepUpdateScene("model")}));var r="【".concat(this.$t("featureDatas.model.name"),"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:r,type:"success"})}}},hanleAddModelFeature:function(t,e,a,i){var n=this,s=window.location.origin+"/MODEL_URL";s=window.IP_CONFIG.MODEL_URL;var o=window.scene.addFeature("model",this.styleFormDatas.id);o.server=s,o.modelID=e.id,o.vaultID=this.$store.state.scene.currentSceneProjectID,o.version=t[0].phaseValue>=0?t[0].phaseValue:"",o.origin=[parseFloat(this.styleFormDatas.longitude+""),parseFloat(this.styleFormDatas.latitude+"")],o.altitude=parseFloat(this.styleFormDatas.altitude+""),o.rotation=this.styleFormDatas.rotation.map((function(t){return parseFloat(t+"")})),o.name=e.name,o.priority=i.priority,o.always=i.always||!1,o.predefineCameraInfo=i.predefineCameraInfo;for(var r=[],l=0;l<this.styleFormDatas.offset.length;l++)r[l]=parseFloat(this.styleFormDatas.offset[l]+"");o.offset=r,t[1].setOptions.forEach((function(t){o[t.attr]=t.state})),o.load().then((function(){o.activeView().then((function(){window.scene.fit2Feature(o),n.$nextTick((function(){n.$deepUpdateScene("model")}));var t="".concat("add"==a?n.$t("others.added"):n.$t("others.updated"));n.$message({showClose:!0,message:"【".concat(n.$t("featureDatas.model.name"),"】").concat(t),type:"success"}),n.$emit("closeSet");var e=JSON.parse(JSON.parse(JSON.stringify(o)));"add"==a?(e.maxVersion=o.maxVersion,e.staticType=!0,e.setFeatureID=!0,n.$store.commit("saveDragOverData",e),n.$store.commit("toggleSettingActive","model")):(e.setFeatureID&&delete e.setFeatureID,e.staticType&&delete e.staticType,n.$store.commit("saveDragOverData",e))})).catch((function(t){console.error(t)})).finally((function(){n.$emit("closeSet")}))})).catch((function(t){console.error(t)})).finally((function(){n.$emit("closeSet")}))},addDemFeature:function(t,e){var a=window.scene.features.get("dem");if(a){""!=t[0].value?a.url=t[0].value:a.url=t[0].defaultAddress,a.name=""==this.styleFormDatas.name?a.name:this.styleFormDatas.name,a.priority=e.priority,a.always=e.always||!1,a.predefineCameraInfo=e.predefineCameraInfo,a.dataKey=window.scene.postData({opacity:t[1].value},a.id);var i="【".concat(this.$t("featureDatas.dem.name"),"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:i,type:"success"})}else{var n=window.scene.addFeature("dem","d1");""!=t[0].value?n.url=t[0].value:n.url=t[0].defaultAddress,n.dataKey=n.id,n.name=this.$t("featureDatas.dem.name"),n.priority=e.priority,n.always=e.always||!1,n.predefineCameraInfo=e.predefineCameraInfo,n.load();var s="【".concat(this.$t("featureDatas.dem.name"),"】").concat(this.$t("others.added"));this.$message({showClose:!0,message:s,type:"success"});var o=JSON.parse(JSON.parse(JSON.stringify(n)));o.staticType=!0,this.$store.commit("saveDragOverData",o),this.$store.commit("toggleSettingActive","dem")}},add3DTilesFeature:function(t,e,a){var s=this;return Object(n["a"])(Object(i["a"])().mark((function n(){var o,r,d,c,p;return Object(i["a"])().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(!s.dragData.isDragData){i.next=23;break}return o=window.scene.addFeature("tile",s.styleFormDatas.id),o.url=t[0].value,o.dataKey="tile-"+o.id,o.offset=s.styleFormDatas.offset.map((function(t){return parseFloat(t+"")})),o.rotation=s.styleFormDatas.rotation.map((function(t){return parseFloat(t+"")})),o.priority=a.priority,o.always=a.always||!1,o.predefineCameraInfo=a.predefineCameraInfo,window.scene.postData(e,o.dataKey),o.name=l["a"]["_3dTiles"].getName()||s.styleFormDatas.name,o.load(),r="【".concat(s.$t("featureDatas._3dTiles.name"),"】").concat(s.$t("others.added")),s.$message({showClose:!0,message:r,type:"success"}),i.next=16,s.asynchronousListenerTiles();case 16:window.scene.fit2Feature(o.id),d=JSON.parse(JSON.parse(JSON.stringify(o))),d.staticType=!0,s.$store.commit("saveDragOverData",d),s.$store.commit("toggleSettingActive","_3dTiles"),i.next=37;break;case 23:c=window.scene.features.get(s.dragData.id),c.url=t[0].value,c.id=s.styleFormDatas.id,c.name=s.styleFormDatas.name||c.name,c.offset=s.styleFormDatas.offset.map((function(t){return parseFloat(t+"")})),c.origin=[parseFloat(s.styleFormDatas.longitude+""),parseFloat(s.styleFormDatas.latitude+"")],c.altitude=parseFloat(s.styleFormDatas.altitude+""),c.rotation=s.styleFormDatas.rotation.map((function(t){return parseFloat(t+"")})),c.priority=a.priority,c.always=a.always||!1,c.predefineCameraInfo=a.predefineCameraInfo,window.scene.postData(e,c.dataKey),p="【".concat(s.$t("featureDatas._3dTiles.name"),"】").concat(s.$t("others.updated")),s.$message({showClose:!0,message:p,type:"success"});case 37:case"end":return i.stop()}}),n)})))()},addwmtsFeature:function(t,e){var a=this.dragData.type;if(this.dragData.isDragData){var i=window.scene.addFeature(a,this.styleFormDatas.id);i.name=l["a"][a].getName()||this.styleFormDatas.name,i.url=t[0].value,i.priority=e.priority,i.always=e.always||!1,i.predefineCameraInfo=e.predefineCameraInfo,i.load();var n="【".concat(a,"】").concat(this.$t("others.added"));this.$message({showClose:!0,message:n,type:"success"});var s=JSON.parse(JSON.parse(JSON.stringify(i)));s.staticType=!0,this.$store.commit("saveDragOverData",s),this.$store.commit("toggleSettingActive",a)}else{var o=window.scene.features.get(this.dragData.id);o.name=this.styleFormDatas.name||o.name,o.url=t[0].value,o.id=this.styleFormDatas.id,o.priority=e.priority,o.always=e.always||!1,o.predefineCameraInfo=e.predefineCameraInfo;var r="【".concat(a,"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:r,type:"success"})}},add3DbuildingFeature:function(t){var e=window.scene.features.get("3DBuilding"),a=!0;if(e||(a=!1,e=window.scene.addFeature("building"),e.dataKey="building"+e.id,window.scene.postData({color:t[0].value,opacity:t[1].value,minzoom:t[2].value},e.dataKey)),e.name=this.styleFormDatas.name||this.$t("featureDatas._3dBuilding.name"),a){var i=JSON.parse(JSON.parse(JSON.stringify(e)));i.staticType=!0,this.$store.commit("saveDragOverData",i),this.$store.commit("toggleSettingActive","_3dBuilding");var n="【".concat(this.$t("featureDatas._3dBuilding.name"),"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:n,type:"success"})}else{e.load();var s="【".concat(this.$t("featureDatas._3dBuilding.name"),"】").concat(this.$t("others.added"));this.$message({showClose:!0,message:s,type:"success"});var o=JSON.parse(JSON.parse(JSON.stringify(e)));o.staticType=!0,this.$store.commit("saveDragOverData",o),this.$store.commit("toggleSettingActive","_3dBuilding")}},addAnnotationFeature:function(t,e,a,i,n){var s=this,d="",c="",p='var clickTimeSign = null;annotation.addEventListener("click",()=>{window.scene.fit2Feature(annotation.id)});';this.styleFormDatas.datas.annotation.setType.type=t[2].radio,this.styleFormDatas.datas.annotation.setType.annotationRadio=t[6].annotationRadio,this.styleFormDatas.datas.annotation.setType.visibleDistance=t[0].value,a&&a.length>0&&(a.forEach((function(t){t.jsCodes&&""!=t.jsCodes&&(p+=t.jsCodes+" ")})),this.styleFormDatas.datas.annotation.setType.eventsList=a),e.annotation.width=t[1].width,e.annotation.height=parseInt(t[5].checkedType)>=0?t[1].height:0,""==e.annotation.title&&""==e.annotation.videoLink&&(e.annotation.title=this.$t("featureSetting.data.anchorPoint.label7"));var h=null;if(this.dragData.isDragData?(h=window.scene.addFeature("annotation"),h.name=l["a"]["annotation"].getName()||this.styleFormDatas.name):(h=window.scene.features.get(this.dragData.id),h.name=this.styleFormDatas.name||h.name,"billboard"==h.type&&(h=window.scene.addFeature("annotation"),h.dataKey="annotion-"+h.id,window.scene.removeFeature(this.dragData.id,!0),this.$deepUpdateScene("billboard"))),h.origin=[parseFloat(this.styleFormDatas.longitude+""),parseFloat(this.styleFormDatas.latitude+"")],h.altitude=parseFloat(this.styleFormDatas.altitude+""),h.priority=n.priority,h.always=n.always||!1,h.predefineCameraInfo=n.predefineCameraInfo,void 0!=i&&e.annotation.dataList&&0==e.annotation.dataList.length&&(Object.keys(i.data.widgets.entityProperty).forEach((function(t){i.data.widgets.entityProperty[t]&&t.length>0&&i.data.widgets.entityProperty[t].forEach((function(t){e.annotation.dataList.push({key:t.name,value:0})}))})),e.annotation.dataList=Object(o["a"])(e.annotation.dataList,"key")),"custom"==t[2].radio)this.styleFormDatas.datas.annotation.setType.anchorType="custom",d=t[3].htmlCode,c=t[3].cssCode;else if(this.styleFormDatas.datas.annotation.setType.anchorType="default",t[4].checkedType>=0&&e.annotation.dataList&&e.annotation.dataList.length>0||"videoAnchor"==t[2].radio){if(this.styleFormDatas.datas.annotation.setType.dataPanel=!0,this.styleFormDatas.datas.annotation.setType.panelToggleMode=e.annotation.panelToggleMode,"videoAnchor"==t[2].radio){""==e.annotation.title&&(e.annotation.title=this.$t("featureSetting.data.anchorPoint.label8")),this.styleFormDatas.datas.annotation.setType.video={},this.styleFormDatas.datas.annotation.setType.video.videoLink=e.annotation.videoLink,this.styleFormDatas.datas.annotation.setType.video.title=e.annotation.title,d=this.setDefaultVideoAnchorHTML(t,e,this.dragData.id);var m=/\.(m3u8|hls|mp4)$/;m.test(e.annotation.videoLink)?p+="\n                            let scriptElement = document.getElementById('script1');\n                            if (!scriptElement) {\n                                let script1 = document.createElement('script');\n                                script1.id = 'script1';\n                                script1.src = '".concat(window.location.origin+Object(r["g"])(),"sources/videoJs/video.min.js';\n                                let script2 = document.createElement('script');\n                                script2.id = 'script2';\n                                script2.src = '").concat(window.location.origin+Object(r["g"])(),'sources/videoJs/videojs-contrib-hls.min.js\';\n                                document.body.appendChild(script1);\n                                document.body.appendChild(script2);\n                                var link=document.createElement("link");\n                                link.setAttribute("rel", "stylesheet");\n                                link.setAttribute("type", "text/css");\n                                link.setAttribute("href", \'').concat(window.location.origin+Object(r["g"])(),"sources/videoJs/video-js-cdn.min.css');\n                                var heads = document.getElementsByTagName(\"head\");\n                                if(heads.length) {\n                                    heads[0].appendChild(link);\n                                } else {\n                                    document.documentElement.appendChild(link);\n                                }\n                            };\n                            setTimeout(() => {\n                                window.myVideoId = '").concat(this.dragData.id,"';\n                                if (!window.myVideo) {\n                                    window.myVideo = {}\n                                }\n                                window.myVideo['").concat(this.dragData.id,"'] = videojs('myVideo-").concat(this.dragData.id,"',{\n\n                                    bigPlayButton : true,\n\n                                    textTrackDisplay : false,\n\n                                    posterImage: false,\n                                    autoplay: true,\n\n                                    errorDisplay : false\n\n                                    })\n\n                                    window.myVideo['").concat(this.dragData.id,"'].play() // 视频播放\n\n                            }, 1000);\n                            "):this.$nextTick((function(){s.$store.commit("listenAnnotation")}))}else d=this.setDefaultDataAnchorHTML(t,e);p+=this.setDataAnnotationDefaultEvent(e),this.styleFormDatas.datas.annotation.setType.lineType=t[4].checkedType}else{var g=this.setBasicAnchorImage(t);""==g&&(g="".concat("","image/anchor/1_point.png"),this.styleFormDatas.datas.annotation.setType.checkedImg=1),d='<img style="width:'.concat(t[1].width,"px;\n                                                height:").concat(t[1].height,"px;\n                                                margin-top:").concat(-1*t[1].height/2+10,'px"\n                                                src="').concat(g,'">')}this.styleFormDatas.datas.annotation.setType.checkedImgSize={width:t[1].width,height:t[1].height},h.offset=this.styleFormDatas.offset.map((function(t){return parseFloat(t+"")})),h.name=this.styleFormDatas.name||h.name;var u={content:e.annotation.dataList&&e.annotation.dataList.length>0?e.annotation:"",htmlCode:d,cssCode:c,jsCode:p,minDistance:""!=t[0].value[0]?parseFloat(t[0].value[0]):0,maxDistance:""!=t[0].value[1]?parseFloat(t[0].value[1]):1/0,setType:this.styleFormDatas.datas.annotation.setType};if(h.dataKey="annotation-"+h.id,window.scene.postData(u,"annotation-"+h.id),this.isVothing&&this.setIotElementData(h.dataKey,e,i),this.dragData.isDragData||"billboard"==this.dragData.type){h.load();var f="【".concat(this.$t("featureSetting.style.anchorPoint.label20"),"】").concat(this.$t("others.added"));this.$message({showClose:!0,message:f,type:"success"}),this.$deepUpdateScene("annotation")}else{var y=JSON.parse(JSON.stringify(this.dragData));y.staticType&&(delete y.staticType,this.$store.commit("saveDragOverData",y));var v="【".concat(this.$t("featureSetting.style.anchorPoint.label20"),"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:v,type:"success"})}},addPanoramaFeature:function(t,e){var a=null;if(this.dragData.isDragData?(a=window.scene.addFeature("panorama"),a.name=l["a"]["panorama"].getName()||this.styleFormDatas.name):(a=window.scene.features.get(this.dragData.id),a.name=this.styleFormDatas.name||a.name),a.offset=this.styleFormDatas.offset.map((function(t){return parseFloat(t+"")})),a.radius=parseFloat(t[1].value),a.url=t[0].value,a.origin=[parseFloat(this.styleFormDatas.longitude+""),parseFloat(this.styleFormDatas.latitude+"")],a.altitude=parseFloat(this.styleFormDatas.altitude+""),a.priority=e.priority,a.always=e.always||!1,a.predefineCameraInfo=e.predefineCameraInfo,a.dataKey=window.scene.postData({radius:a.radius,depthPath:t[2].value}),this.dragData.isDragData){a.load(),window.scene.fit2Feature(a);var i="【".concat(this.$t("featureDatas.panorama.name"),"】").concat(this.$t("others.added"));this.$message({showClose:!0,message:i,type:"success"});var n=JSON.parse(JSON.parse(JSON.stringify(a)));n.staticType=!0,this.$store.commit("saveDragOverData",n),this.$store.commit("toggleSettingActive","panorama")}else{var s="【".concat(this.$t("featureDatas.panorama.name"),"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:s,type:"success"})}},addShieldFeature:function(t,e,a,i){var n=null;this.dragData.isDragData?(n=window.scene.addFeature("shield"),n.name=l["a"]["shield"].getName()||this.styleFormDatas.name):(n=window.scene.features.get(this.dragData.id),n.name=this.styleFormDatas.name||n.name),n.radius=parseFloat(e.radius),n.origin=[parseFloat(this.styleFormDatas.longitude+""),parseFloat(this.styleFormDatas.latitude+"")],n.altitude=parseFloat(this.styleFormDatas.altitude+""),n.offset=this.styleFormDatas.offset.map((function(t){return parseFloat(t+"")})),n.priority=i.priority,n.always=i.always||!1,n.predefineCameraInfo=i.predefineCameraInfo;var s={color:e.color,opacity:e.opacity,radius:parseFloat(e.radius+""),speed:Math.floor(e.speed/33.33*100)/100};if(n.dataKey="shield-"+n.id,window.scene.postData(s,n.dataKey),this.isVothing&&this.setIotElementData(n.dataKey,e,a),this.dragData.isDragData){n.load(),window.scene.fit2Feature(n);var o="【".concat(this.$t("featureDatas.shield.name"),"】").concat(this.$t("others.added"));this.$message({showClose:!0,message:o,type:"success"});var r=JSON.parse(JSON.parse(JSON.stringify(n)));r.staticType=!0,this.$store.commit("saveDragOverData",r),this.$store.commit("toggleSettingActive","shield")}else{var d="【".concat(this.$t("featureDatas.shield.name"),"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:d,type:"success"})}},addHeatmapFeature:function(t,e,a,i){var n=null;this.dragData.isDragData?(n=window.scene.addFeature("heatmap"),n.name=l["a"]["heatmap"].getName()||this.styleFormDatas.name):(n=window.scene.features.get(this.dragData.id),n.name=this.styleFormDatas.name||n.name),n.origin=[parseFloat(this.styleFormDatas.longitude+""),parseFloat(this.styleFormDatas.latitude+"")],n.altitude=parseFloat(this.styleFormDatas.altitude+""),n.priority=i.priority,n.always=i.always||!1,n.predefineCameraInfo=i.predefineCameraInfo;var s=e.pointsArray||[],o=[];if(s&&s.length>0?s.map((function(t){var e={};e.position=[parseFloat(t.x+""),parseFloat(t.y+"")],e.value=parseFloat(t.value+""),o.push(e)})):o=[],n.dataKey="heatmap-"+n.id,window.scene.postData({points:o,gradient:e.gradient,color:e.color,size:parseFloat(e.size+""),scaleZ:parseFloat(e.scaleZ+""),opacity:parseFloat(e.opacity+""),wireframe:e.wireframe},n.dataKey),this.isVothing&&this.setIotElementData(n.dataKey,e,a),this.dragData.isDragData){n.load(),this.$message({showClose:!0,message:this.$t("featureSetting.style.heatMap.message"),type:"success"});var r=JSON.parse(JSON.parse(JSON.stringify(n)));r.staticType=!0,this.$store.commit("saveDragOverData",r),this.$store.commit("toggleSettingActive","heatmap")}else{var d="【".concat(this.$t("featureDatas.heatmap.name"),"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:d,type:"success"})}},addRingFeature:function(t,e,a,i){var n=null;if(this.dragData.isDragData?(n=window.scene.addFeature("ring"),n.name=l["a"]["ring"].getName()||this.styleFormDatas.name):(n=window.scene.features.get(this.dragData.id),n.name=this.styleFormDatas.name||n.name),n.origin=[parseFloat(this.styleFormDatas.longitude+""),parseFloat(this.styleFormDatas.latitude+"")],n.altitude=parseFloat(this.styleFormDatas.altitude+""),n.offset=this.styleFormDatas.offset.map((function(t){return parseFloat(t+"")})),n.dataKey="ring-"+n.id,n.priority=i.priority,n.always=i.always||!1,n.predefineCameraInfo=i.predefineCameraInfo,window.scene.postData({radius:parseFloat(e.radius+""),color:e.color,speed:e.speed},n.dataKey),this.isVothing&&this.setIotElementData(n.dataKey,e,a),this.dragData.isDragData){n.load(),window.scene.fit2Feature(n);var s="【".concat(this.$t("featureDatas.ring.name"),"】").concat(this.$t("others.added"));this.$message({showClose:!0,message:s,type:"success"});var o=JSON.parse(JSON.parse(JSON.stringify(n)));o.staticType=!0,this.$store.commit("saveDragOverData",o),this.$store.commit("toggleSettingActive","ring")}else{var r="【".concat(this.$t("featureDatas.ring.name"),"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:r,type:"success"})}},addRipplewallFeature:function(t,e,a,i){var n=null;this.dragData.isDragData?(n=window.scene.addFeature("ripplewall"),n.dataKey="ripplewall-"+n.id,n.name=l["a"]["ripplewall"].getName()||this.styleFormDatas.name):(n=window.scene.features.get(this.dragData.id),n.name=this.styleFormDatas.name||n.name),n.origin=[parseFloat(this.styleFormDatas.longitude+""),parseFloat(this.styleFormDatas.latitude+"")],n.altitude=parseFloat(this.styleFormDatas.altitude+""),n.priority=i.priority,n.always=i.always||!1,n.predefineCameraInfo=i.predefineCameraInfo;var s=t[1].units,o=s[0].position,r=s[s.length-1].position;if(o.x==r.x&&o.y==r.y||(s[s.length]={id:s.length+1,position:s[0].position}),window.scene.postData({units:JSON.parse(JSON.stringify(s)),color:e.color,speed:Math.floor(e.speed/33.33*100)/100,opacity:e.opacity,height:parseFloat(e.height+"")},n.dataKey),this.isVothing&&this.setIotElementData(n.dataKey,e,a),this.dragData.isDragData){n.load(),window.scene.fit2Feature(n);var d="【".concat(this.$t("featureDatas.ripplewall.name"),"】").concat(this.$t("others.added"));this.$message({showClose:!0,message:d,type:"success"});var c=JSON.parse(JSON.parse(JSON.stringify(n)));c.staticType=!0,this.$store.commit("saveDragOverData",c),this.$store.commit("toggleSettingActive","ripplewall")}else{var p="【".concat(this.$t("featureDatas.ripplewall.name"),"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:p,type:"success"})}},addRadarFeature:function(t,e,a,i){var n=null;this.dragData.isDragData?(n=window.scene.addFeature("radar"),n.dataKey="radar-"+n.id,n.name=l["a"]["radar"].getName()||this.styleFormDatas.name):(n=window.scene.features.get(this.dragData.id),n.name=this.styleFormDatas.name||n.name),n.origin=[parseFloat(this.styleFormDatas.longitude+""),parseFloat(this.styleFormDatas.latitude+"")],n.altitude=parseFloat(this.styleFormDatas.altitude+""),n.offset=this.styleFormDatas.offset.map((function(t){return parseFloat(t+"")})),n.priority=i.priority,n.always=i.always||!1,n.predefineCameraInfo=i.predefineCameraInfo;var s=parseFloat(e.width+"")/180*Math.PI,o={color:e.color,speed:e.speed,radius:parseFloat(e.radius+""),opacity:parseFloat(e.opacity+""),width:s};if(this.isVothing&&this.setIotElementData(n.dataKey,e,a),window.scene.postData(o,n.dataKey),this.dragData.isDragData){n.load(),window.scene.fit2Feature(n);var r="【".concat(this.$t("featureDatas.radar.name"),"】").concat(this.$t("others.added"));this.$message({showClose:!0,message:r,type:"success"});var d=JSON.parse(JSON.parse(JSON.stringify(n)));d.staticType=!0,this.$store.commit("saveDragOverData",d),this.$store.commit("toggleSettingActive","radar")}else{var c="【".concat(this.$t("featureDatas.radar.name"),"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:c,type:"success"})}},addVideoFeature:function(t,e){var a=null;this.dragData.isDragData?(a=window.scene.addFeature("video"),a.dataKey="video-"+a.id,a.name=l["a"]["video"].getName()||this.styleFormDatas.name):(a=window.scene.features.get(this.dragData.id),a.name=this.styleFormDatas.name||a.name),a.url=t[0].value,a.priority=e.priority,a.always=e.always||!1,a.predefineCameraInfo=e.predefineCameraInfo;var i=[];if(t[1].cor.forEach((function(t){i.push([parseFloat(t.position.x+""),parseFloat(t.position.y+"")])})),window.scene.postData(i,a.dataKey),this.dragData.isDragData){a.load(),window.scene.fit2Feature(a);var n="【".concat(this.$t("featureDatas.video.name"),"】").concat(this.$t("others.added"));this.$message({showClose:!0,message:n,type:"success"});var s=JSON.parse(JSON.parse(JSON.stringify(a)));s.staticType=!0,this.$store.commit("saveDragOverData",s),this.$store.commit("toggleSettingActive","video")}else{var o="【".concat(this.$t("featureDatas.video.name"),"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:o,type:"success"})}},addSHPFeature:function(t,e,a){var i=null;if(this.dragData.isDragData?(i=window.scene.addFeature("shp"),i.name=l["a"]["shp"].getName()||this.styleFormDatas.name):(i=window.scene.features.get(this.dragData.id),i.name=this.styleFormDatas.name||i.name),i.url=t[0].value,i.priority=a.priority,i.always=a.always||!1,i.predefineCameraInfo=a.predefineCameraInfo,i.dataKey=window.scene.postData({color:e.color,opacity:parseFloat(e.opacity+""),lineWidth:parseFloat(e.lineWidth+""),height:parseFloat(e.height+""),useUniquePaint:e.useUniquePaint}),this.dragData.isDragData){i.load();var n="【".concat(this.$t("featureDatas.shp.name"),"】").concat(this.$t("others.added"));this.$message({showClose:!0,message:n,type:"success"});var s=JSON.parse(JSON.parse(JSON.stringify(i)));s.staticType=!0,this.$store.commit("saveDragOverData",s),this.$store.commit("toggleSettingActive","shp")}else{var o="【".concat(this.$t("featureDatas.shp.name"),"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:o,type:"success"})}},addGeoJSONFeature:function(t,e,a){var i=null;this.dragData.isDragData?(i=window.scene.addFeature("geoJSON"),i.name=l["a"]["geoJSON"].getName()||this.styleFormDatas.name):(i=window.scene.features.get(this.dragData.id),i.name=this.styleFormDatas.name||i.name);var n=["lineWidth","opacity","size","textSize","outlineWidth"];for(var s in e)n.includes(s)&&(e[s]=parseFloat(e[s]+""));var o=JSON.stringify(e);if(e.originalData=o,i.url=t[0].value,i.dataKey=window.scene.postData(e),i.priority=a.priority,i.always=a.always||!1,i.predefineCameraInfo=a.predefineCameraInfo,this.dragData.isDragData){i.load();var r="【".concat(this.$t("featureDatas.geoJSON.name"),"】").concat(this.$t("others.added"));this.$message({showClose:!0,message:r,type:"success"}),window.scene.render();var d=JSON.parse(JSON.parse(JSON.stringify(i)));d.staticType=!0,this.$store.commit("saveDragOverData",d),this.$store.commit("toggleSettingActive","geoJSON")}else{var c="【".concat(this.$t("featureDatas.geoJSON.name"),"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:c,type:"success"})}},addGltfOrFbxFeature:function(t,e,a){var i=this,n=null,s=null,o=1,r=!0;if(this.dragData.isDragData)if(n=window.scene.addFeature(this.dragData.type),s=this.dragData.defaultSource,s){var d=this.dragData.fileSrc.substring(0,4);this.isVothing?n.url="http"===d?this.dragData.fileSrc:window.IP_CONFIG.BASE_URL+this.dragData.fileSrc:n.url=window.IP_CONFIG.BASE_URL+this.dragData.fileSrc,n.name=l["a"][this.dragData.type].getName()||this.dragData.title}else n.url=t[0].value,n.name=l["a"][this.dragData.type].getName()||("fbx"===this.dragData.type?this.$t("featureDatas.fbx.subName"):this.$t("featureDatas.gltf.subName"));else n=window.scene.features.get(this.dragData.id),s=n.data.defaultSource,n.url=t[2].value,o=t[0].value,r=t[1].value,n.name=this.styleFormDatas.name||n.name;n.origin=[parseFloat(this.styleFormDatas.longitude+""),parseFloat(this.styleFormDatas.latitude+"")],n.altitude=parseFloat(this.styleFormDatas.altitude+""),n.offset=this.styleFormDatas.offset.map((function(t){return parseFloat(t+"")})),n.rotation=this.styleFormDatas.rotation.map((function(t){return parseFloat(t+"")})),n.priority=e.priority,n.always=e.always||!1,n.predefineCameraInfo=e.predefineCameraInfo,n.dataKey=window.scene.postData({defaultSource:s,scale:o,animation:r});var c="".concat("fbx"==this.dragData.type?this.$t("featureDatas.fbx.subName"):this.$t("featureDatas.gltf.subName"));this.dragData.isDragData?(n.load(),setTimeout((function(){window.scene.render(),i.$message({showClose:!0,message:"【".concat(c,"】").concat(i.$t("others.added")),type:"success"});var t=JSON.parse(JSON.parse(JSON.stringify(n)));t.staticType=!0,a||(window.scene.fit2Feature(n),i.$store.commit("saveDragOverData",t),i.$store.commit("toggleSettingActive","gltf"))}),500)):this.$message({showClose:!0,message:"【".concat(c,"】").concat(this.$t("others.updated")),type:"success"})},addPolylineFeature:function(t,e,a){var i=null;this.dragData.isDragData?(i=window.scene.addFeature("polyline"),i.name=l["a"]["polyline"].getName()||this.styleFormDatas.name):(i=window.scene.features.get(this.dragData.id),i.name=this.styleFormDatas.name||i.name),i.priority=a.priority,i.always=a.always||!1,i.predefineCameraInfo=a.predefineCameraInfo;var n=[];t[1].position.forEach((function(t,e){n[e]=t.map((function(t){return parseFloat(t+"")}))}));var s="";""!=t[0].value&&(s=t[0].value);var o={positions:n,flow:e.flow,width:e.width,opacity:e.opacity,image:s,color:e.color,interval:10-(e.interval-1)};if(i.dataKey=window.scene.postData(o,i.dataKey),this.dragData.isDragData){i.load();var r="【".concat(this.$t("featureDatas.polyline.name"),"】").concat(this.$t("others.added"));this.$message({showClose:!0,message:r,type:"success"});var d=JSON.parse(JSON.parse(JSON.stringify(i)));d.staticType=!0,this.$store.commit("saveDragOverData",d),this.$store.commit("toggleSettingActive","polyline")}else{var c="【".concat(this.$t("featureDatas.polyline.name"),"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:c,type:"success"})}},addPolygonFeature:function(t,e,a){var i=null,n=!1;if(this.dragData.isDragData){if(i=window.scene.addFeature("polygon"),n=this.dragData.defaultSource,n){var s=this.dragData.thumb.substring(0,4);this.isVothing?t[0].value="http"===s?this.dragData.thumb:window.location.origin+Object(r["g"])()+this.dragData.thumb:t[0].value=window.location.origin+Object(r["g"])()+this.dragData.thumb}}else i=window.scene.features.get(this.dragData.id),i.name=this.styleFormDatas.name||i.name;var o={positions:[],top:0,base:0,flatten:!1,excavation:!1,extrude:!1,water:!1,fill:!1,showline:!1,color:"rgb(255, 50, 0)",opacity:1,image:"",flow:!1,interval:1,direction:"x",environment:!1,subType:this.dragData.subType,title:"",imageRepeat:{x:1,y:1},terrainOpacity:.1};switch(t[1].position.forEach((function(t,e){o.positions[e]=t.map((function(t){return parseFloat(t+"")}))})),"{}"!=JSON.stringify(e)&&void 0!=e&&(o.color=e.color,o.opacity=e.opacity,o.top=parseFloat(e.top+""),o.base=parseFloat(e.base+""),o.interval=e.interval,o.showline=e.showline,o.imageRepeat=e.imageRepeat,o.terrainOpacity=e.terrainOpacity),this.dragData.subType){case"surface":o.fill=!0,this.handleSubtypeFeatureChanged("surface",i.id,o);break;case"extrude":o.extrude=!0,o.top=Math.abs(o.top)+o.base,this.handleSubtypeFeatureChanged("extrude",i.id,o);break;case"flatten":o.flatten=!0,o.terrainFlatten=!0,o.top=-1*Math.abs(o.top)+o.base,this.handleSubtypeFeatureChanged("flatten",i.id,o);break;case"tileFlatten":o.flatten=!0,o.tileFlatten=!0,o.top=-1*Math.abs(o.top)+o.base,this.handleSubtypeFeatureChanged("tileFlatten",i.id,o);break;case"environment":o.fill=!0,o.environment=!0,o.image=t[0].value,this.handleSubtypeFeatureChanged("environment",i.id,o);break;case"tileExcavation":o.excavation=!0,o.tileExcavation=!0,o.top=-1*Math.abs(o.top)+o.base,this.handleSubtypeFeatureChanged("tileExcavation",i.id,o);break;case"excavation":o.excavation=!0,o.terrainExcavation=!0,o.top=-1*Math.abs(o.top)+o.base,this.handleSubtypeFeatureChanged("excavation",i.id,o);break;case"demOpacity":this.handleSubtypeFeatureChanged("demOpacity",i.id,o);break;case"water":o.water=!0,o.flow=!0,this.handleSubtypeFeatureChanged("water",i.id,o);break}if(i.priority=a.priority,i.always=a.always||!1,i.predefineCameraInfo=a.predefineCameraInfo,this.dragData.isDragData){i.name=o.title,i.dataKey=window.scene.postData(o),i.load();var d="【".concat(this.$t("featureDatas.polygon.name"),"-").concat(o.title,"】").concat(this.$t("others.added"));this.$message({showClose:!0,message:d,type:"success"});var c=JSON.parse(JSON.parse(JSON.stringify(i)));c.subType=this.dragData.subType,c.title=l["a"][this.dragData.subType].getBaseName(),c.staticType=!0,this.$store.commit("saveDragOverData",c),this.$store.commit("toggleSettingActive","polygon")}else{window.scene.postData(o,i.dataKey);var p="【".concat(this.$t("featureDatas.polygon.name"),"-").concat(i.name,"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:p,type:"success"}),window.scene.fit2Feature(i.id)}},addBillboardFeature:function(t,e,a){var i=this,n="",s="";this.styleFormDatas.datas.billboard.setType.type=t[2].radio,this.styleFormDatas.datas.billboard.setType.annotationRadio=t[6].annotationRadio,this.styleFormDatas.datas.billboard.setType.visibleDistance=t[0].value,""==e.annotation.title&&(e.annotation.title=this.$t("featureSetting.data.anchorPoint.label7")),e.annotation.width=t[1].width,e.annotation.height=t[1].height;var o=null;if(this.dragData.staticType?(o=window.scene.addFeature("billboard"),o.dataKey="billboard-"+o.id,window.scene.removeFeature(this.dragData.id,!0)):(o=window.scene.features.get(this.dragData.id),"annotation"==o.type&&(o=window.scene.addFeature("billboard"),o.dataKey="billboard-"+o.id,window.scene.removeFeature(this.dragData.id,!0),this.$deepUpdateScene("annotation"))),o.origin=[parseFloat(this.styleFormDatas.longitude+""),parseFloat(this.styleFormDatas.latitude+"")],o.altitude=parseFloat(this.styleFormDatas.altitude+""),o.name=l["a"]["billboard"].getName()||this.styleFormDatas.name,o.priority=a.priority,o.always=a.always||!1,o.predefineCameraInfo=a.predefineCameraInfo,"custom"==t[2].radio)this.styleFormDatas.datas.billboard.setType.anchorType="custom",n=t[3].htmlCode,s=t[3].cssCode;else if(this.styleFormDatas.datas.billboard.setType.anchorType="default",t[4].checkedType>=0&&e.annotation.dataList&&e.annotation.dataList.length>0)if(this.styleFormDatas.datas.billboard.setType.dataPanel=!0,"custom"==t[2].radio)n=t[3].htmlCode,s=t[3].cssCode;else{var r=this.setDefaultDataBillboardHTML(t);n=this.setCustomDataAnchorHTML(t,e.annotation,r),s=this.billboardExampleCss,this.styleFormDatas.datas.billboard.setType.lineType=t[4].checkedType}else{var d=this.setBasicAnchorImage(t);""==d&&(d="".concat("","image/anchor/1_point.png"),this.styleFormDatas.datas.annotation.setType.checkedImg=1),n='<img src="'.concat(d,'" style="vertical-align:bottom">')}this.styleFormDatas.datas.billboard.setType.checkedImgSize={width:t[1].width,height:t[1].height},o.offset=this.styleFormDatas.offset.map((function(t){return parseFloat(t+"")}));var c=document.createElement("div");c.id="billboard-cyb",c.style.position="fixed",document.body.prepend(c);var p=document.createTextNode(s),h=document.createElement("style");h.type="text/css",h.appendChild(p),c.appendChild(h);var m=(new DOMParser).parseFromString(n,"text/html");c.appendChild(m.body.firstChild),setTimeout((function(){window.domtoimage.toPng(document.querySelector("#billboard-cyb")).then((function(a){var n={image:a,rect:{width:0,height:0},line:!1,lineWidth:2,lineColor:"green",setType:i.styleFormDatas.datas.billboard.setType,content:e.annotation.dataList&&e.annotation.dataList.length>0?e.annotation:""};if(t[1].locked){var s=new Image;s.onload=function(){n.rect.width=t[1].lockWidth,n.rect.height=s.clientHeight/s.clientWidth*t[1].lockWidth,n.setType.locked=t[1].locked,i.handleBillboardImage(o,n,c,s)},s.src=a,s.id="cyb-img",s.style.position="fixed",s.style.visibility="hidden",document.body.appendChild(s)}else n.rect.width=t[1].width,n.rect.height=t[1].height,i.handleBillboardImage(o,n,c)})).catch((function(t){console.error(t),document.body.removeChild(c)}))}),500)},handleBillboardImage:function(t,e,a,i){window.scene.postData(e,t.dataKey);var n="";"annotation"==this.dragData.type?(t.load(),n=this.$t("others.added")):n=this.$t("others.updated"),this.$message({showClose:!0,message:"【".concat(this.$t("featureDatas.billboard.name"),"-").concat(this.styleFormDatas.name,"】").concat(n),type:"success"}),window.scene.render(),document.body.removeChild(a),i&&document.body.removeChild(i),this.$deepUpdateScene("billboard"),this.$emit("closeSet"),setTimeout((function(){window.scene.render()}),1e3)},addTemporaryAnnotationFeature:function(t){var e={position:{x:0,y:0,z:0},content:"",htmlCode:'<img style="width:30px;height:30px;" src="'.concat("",'image/point.gif">'),cssCode:"",jsCode:"",minDistance:0,maxDistance:1/0},a=window.scene.addFeature("annotation");a.origin=[parseFloat(this.styleFormDatas.longitude+""),parseFloat(this.styleFormDatas.latitude+"")],a.altitude=parseFloat(this.styleFormDatas.altitude+""),a.name=l["a"]["annotation"].getName()||t.title||this.$t("featureSetting.style.anchorPoint.label19"),a.offset=[0,0,0],a.dataKey="annotation-"+a.id,window.scene.postData(e,a.dataKey),a.load(),window.scene.render();var i=JSON.parse(JSON.parse(JSON.stringify(a)));i.staticType=!0,i.checkedType=t.subType,this.$store.commit("saveDragOverData",i),this.$store.commit("toggleSettingActive","annotation")},addFlameFeature:function(t,e){var a=null;this.dragData.isDragData?(a=window.scene.addFeature(this.dragData.type),a.dataKey="".concat(this.dragData.type,"-").concat(a.id),a.name=l["a"][this.dragData.type].getName()||this.styleFormDatas.name):(a=window.scene.features.get(this.dragData.id),a.name=this.styleFormDatas.name||a.name),a.origin=[parseFloat(this.styleFormDatas.longitude+""),parseFloat(this.styleFormDatas.latitude+"")],a.altitude=parseFloat(this.styleFormDatas.altitude+""),a.offset=this.styleFormDatas.offset.map((function(t){return parseFloat(t+"")})),a.priority=e.priority,a.always=e.always||!1,a.predefineCameraInfo=e.predefineCameraInfo,window.scene.postData({width:parseFloat(t.width+""),height:parseFloat(t.height+"")},a.dataKey);var i="flame"===this.dragData.type?this.$t("featureDatas.flame.name"):this.$t("featureDatas.smoke.name");if(this.dragData.isDragData){a.load(),this.$message({showClose:!0,message:"【".concat(i,"】").concat(this.$t("others.added")),type:"success"}),window.scene.fit2Feature(a);var n=JSON.parse(JSON.parse(JSON.stringify(a)));n.staticType=!0,this.$store.commit("saveDragOverData",n),this.$store.commit("toggleSettingActive",this.dragData.type)}else this.$message({showClose:!0,message:"【".concat(i,"】").concat(this.$t("others.updated")),type:"success"})},addBatchExtrudeFeature:function(t,e,a){var i=null;this.dragData.isDragData?(i=window.scene.addFeature("batch-extrude"),i.dataKey="".concat(this.dragData.type,"-").concat(i.id)):(i=window.scene.features.get(this.dragData.id),i.name=this.styleFormDatas.name||i.name);var n=[],s=2;"polygon"==t[0].type?n=t[0].position.map((function(t){return{x:parseFloat(t[0]+"")||0,y:parseFloat(t[1]+"")||0}})):s=parseFloat(t[0].radius+"")||2;var o=[];e.datas.forEach((function(t){o.push({color:t.color,bottomPosition:{x:parseFloat(t.bottomPosition[0]+"")||0,y:parseFloat(t.bottomPosition[1]+"")||0,z:parseFloat(t.bottomPosition[2]+"")||0},topPosition:{x:parseFloat(t.topPosition[0]+"")||0,y:parseFloat(t.topPosition[1]+"")||0,z:parseFloat(t.topPosition[2]+"")||0}})}));var r={type:t[0].type,coords:n,radius:s,datas:o,circleSet:{segments:32}};i.origin=[parseFloat(this.styleFormDatas.longitude+""),parseFloat(this.styleFormDatas.latitude+"")],i.altitude=parseFloat(this.styleFormDatas.altitude+""),i.name=this.styleFormDatas.name,i.priority=a.priority,i.always=a.always||!1,i.predefineCameraInfo=a.predefineCameraInfo,window.scene.postData(r,i.dataKey);var l=this.$t("featureDatas['batch-extrude'].name")+"-"+this.styleFormDatas.name;this.dragData.isDragData?(i.load(),this.$message({showClose:!0,message:"【".concat(l,"】").concat(this.$t("others.added")),type:"success"}),window.scene.fit2Feature(i)):this.$message({showClose:!0,message:"【".concat(l,"】").concat(this.$t("others.updated")),type:"success"})},addBatchExtrudeFeature2:function(){var t=window.scene.addFeature("batch-extrude");t.dataKey="batch-extrude-".concat(t.id),t.name=l["a"]["batch-extrude"].getName()||this.styleFormDatas.name,t.priority=0;var e={type:"polygon",coords:[{x:-1,y:-1},{x:1,y:-1},{x:1,y:1},{x:-1,y:1}],radius:2,datas:[],circleSet:{segments:24}};window.scene.postData(e,t.dataKey),t.load();var a=JSON.parse(JSON.parse(JSON.stringify(t)));a.staticType=!0,this.$store.commit("saveDragOverData",a),this.$store.commit("toggleSettingActive","batch-extrude")},addKMLFeature:function(t,e){var a=null;if(this.dragData.isDragData?(a=window.scene.addFeature("kml"),a.name=l["a"]["kml"].getName()||this.styleFormDatas.name):(a=window.scene.features.get(this.dragData.id),a.name=this.styleFormDatas.name||a.name),a.url=t[0].value,a.priority=e.priority,a.always=e.always||!1,a.predefineCameraInfo=e.predefineCameraInfo,this.dragData.isDragData){a.load();var i="【".concat(this.$t("featureDatas.kml.name"),"】").concat(this.$t("others.added"));this.$message({showClose:!0,message:i,type:"success"});var n=JSON.parse(JSON.parse(JSON.stringify(a)));n.staticType=!0,this.$store.commit("saveDragOverData",n),this.$store.commit("toggleSettingActive","kml")}else{var s="【".concat(this.$t("featureDatas.kml.name"),"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:s,type:"success"})}},addVectorExtrudeFeature:function(t,e,a){var i=this,n=null;this.dragData.isDragData?(n=window.scene.addFeature("vectorextrude"),n.name=l["a"]["vectorextrude"].getName()||this.styleFormDatas.name):(n=window.scene.features.get(this.dragData.id),n.name=this.styleFormDatas.name||n.name);var s=JSON.stringify(e);if(e.originalData=s,n.url=t[0].value,n.dataKey=window.scene.postData(e),n.priority=a.priority,n.always=a.always||!1,n.predefineCameraInfo=a.predefineCameraInfo,this.dragData.isDragData)n.load().then((function(){window.scene.fit2Feature(n);var t="【".concat(i.$t("featureDatas.vectorextrude.name"),"】").concat(i.$t("others.added"));i.$message({showClose:!0,message:t,type:"success"}),window.scene.render();var e=JSON.parse(JSON.parse(JSON.stringify(n)));e.staticType=!0,i.$store.commit("saveDragOverData",e),i.$store.commit("toggleSettingActive","vectorextrude")}));else{var o="【".concat(this.$t("featureDatas.vectorextrude.name"),"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:o,type:"success"})}},handleTopAdvancedEnvironmentSet:function(t){this.$store.commit("toggleSettingActive",t)},addPathAnimation:function(t,e){e[1].position;var a=t.type;t.staticType=!0,this.$store.commit("saveDragOverData",t),this.$store.commit("toggleSettingActive",a);var i=this.$store.state.dialog.activeDialog;-1!==i.indexOf("pathAnimation")&&this.$store.commit("toggleActiveDialog","pathAnimation")},addWaterfallFeature:function(t,e){var a=null;this.dragData.isDragData?(a=window.scene.addFeature(this.dragData.type),a.dataKey="".concat(this.dragData.type,"-").concat(a.id),a.name=l["a"][this.dragData.type].getName()||this.styleFormDatas.name):(a=window.scene.features.get(this.dragData.id),a.name=this.styleFormDatas.name||a.name),a.origin=[parseFloat(this.styleFormDatas.longitude+""),parseFloat(this.styleFormDatas.latitude+"")],a.altitude=parseFloat(this.styleFormDatas.altitude+""),a.offset=this.styleFormDatas.offset.map((function(t){return parseFloat(t+"")})),window.scene.postData({width:parseFloat(t.width+""),height:parseFloat(t.height+""),depth:parseFloat(t.depth+"")},a.dataKey);var i="";if(this.dragData.isDragData){a.load(),i="【".concat(this.$t("featureDatas.waterfall.subName"),"】").concat(this.$t("others.added")),window.scene.fit2Feature(a);var n=JSON.parse(JSON.parse(JSON.stringify(a)));n.staticType=!0,this.$store.commit("saveDragOverData",n),this.$store.commit("toggleSettingActive",this.dragData.type)}else i="【".concat(this.$t("featureDatas.waterfall.subName"),"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:i,type:"success"})},setIotElementData:function(t,e,a){void 0!=a&&null!=a&&(a.elementid=t,a.data.title=e.title||"",a.data.elementType=this.dragData.type,this.$store.commit("setCurrentWidgetInfo",a))},handleSubtypeFeatureChanged:function(t,e,a){this.dragData.isDragData&&(this.$bus.emit("subtypeFeatureChanged",{eventType:"add",featureType:t,featureId:e}),a.title=l["a"][t].getName())},addProjectorFeature:function(t,e){var a=null;this.dragData.isDragData?(a=window.scene.addFeature("projector"),a.dataKey="".concat(this.dragData.type,"-").concat(a.id),a.name=l["a"][this.dragData.type].getName()||this.styleFormDatas.name,a.url=e,window.scene.postData(t,a.dataKey)):(a=window.scene.features.get(this.dragData.id),a.name=this.styleFormDatas.name||a.name);var i="";if(this.dragData.isDragData){a.load(),i="【".concat(this.$t("featureDatas.projector.name"),"】").concat(this.$t("others.added"));var n=JSON.parse(JSON.parse(JSON.stringify(a)));n.staticType=!0,this.$store.commit("saveDragOverData",n),this.$store.commit("toggleSettingActive",this.dragData.type)}else i="【".concat(this.$t("featureDatas.projector.name"),"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:i,type:"success"})},addSnowFeature:function(t){var e=null;this.dragData.isDragData?(e=window.scene.addFeature(this.dragData.type),e.dataKey="".concat(this.dragData.type,"-").concat(e.id),e.name=l["a"][this.dragData.type].getName()||this.styleFormDatas.name,window.scene.postData({width:50,height:50,speed:1,density:1},e.dataKey)):(e=window.scene.features.get(this.dragData.id),e.name=this.styleFormDatas.name||e.name),e.origin=[parseFloat(this.styleFormDatas.longitude+""),parseFloat(this.styleFormDatas.latitude+"")],e.altitude=parseFloat(this.styleFormDatas.altitude+""),e.offset=this.styleFormDatas.offset.map((function(t){return parseFloat(t+"")})),e.priority=t.priority,e.always=t.always||!1,e.predefineCameraInfo=t.predefineCameraInfo;var a="";if(this.dragData.isDragData){e.load(),a="【".concat(this.$t("featureDatas.snow.name"),"】").concat(this.$t("others.added")),setTimeout((function(){window.scene.fit2Feature(e)}),800);var i=JSON.parse(JSON.parse(JSON.stringify(e)));i.staticType=!0,this.$store.commit("saveDragOverData",i),this.$store.commit("toggleSettingActive",this.dragData.type)}else a="【".concat(this.$t("featureDatas.snow.name"),"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:a,type:"success"})},addTailLineFeature:function(t,e,a){var i=null;this.dragData.isDragData?(i=window.scene.addFeature("tailline"),i.name=l["a"]["tailline"].getName()||this.styleFormDatas.name):(i=window.scene.features.get(this.dragData.id),i.name=this.styleFormDatas.name||i.name),i.priority=a.priority,i.always=a.always||!1,i.predefineCameraInfo=a.predefineCameraInfo;var n=[];t[1].position.forEach((function(t,e){n[e]=t.map((function(t){return parseFloat(t+"")}))}));var s="";""!=t[0].value&&(s=t[0].value);var o={positions:n,color:e.color,tailMap:s,speed:e.speed,reverse:e.reverse,lineWidth:e.lineWidth};i.dataKey=window.scene.postData(o,i.dataKey);var r="";if(this.dragData.isDragData){i.load(),r="【".concat(this.$t("featureDatas.tailline.name"),"】").concat(this.$t("others.added"));var d=JSON.parse(JSON.parse(JSON.stringify(i)));d.staticType=!0,this.$store.commit("saveDragOverData",d),this.$store.commit("toggleSettingActive","tailline")}else r="【".concat(this.$t("featureDatas.tailline.name"),"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:r,type:"success"})}}}},"113c":function(t,e){t.exports="data:image/png;base64,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"},2007:function(t,e){t.exports="data:image/png;base64,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"},cb29:function(t,e,a){var i=a("23e7"),n=a("81d5"),s=a("44d2");i({target:"Array",proto:!0},{fill:n}),s("fill")},f6a2:function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJAAAACQCAMAAADQmBKKAAAC/VBMVEUIGjMAAAAURIUngP82RVmrsrrFyc+lrLV6hJJmcoG8wci4vsRLWWsLHjfj5ejJzdOXn6qSm6YYKUD39/jL0NXBx8ycpK5RXnCorrdWY3QkMiwNHzLLwApYZXY0Pyn24wF7fRpYXyI6RigZKS8UJz4QIzG6wMeknhJibX06SV0yQVYeLi3x8vPp7O7czAbS1drAxcyTnKZ1f45hZSBFU2Y+TmE/SSc3QikRJDsOITkJHDLh5ObT1trIzNK1usKus7yJkp1ve4lqdoUmNkzo2ATl1QTf0QbTxQiGhBh8h5Nrbh5caHhRWCNCUGMVJi8ngf/z4QLu2wPi0wXb3eG5sA6pohGPlqKPjBaLlJ+Gj5t/fxpNWm1MVCVETSYJHDX8/Pz96QD65gHs7vDWyAfFuQzBuAy1rA6emhSSkBaTkRWCgRldaXkvPSogMUcaK0Px3gLl5+rW2t7Q09jPwgnN0taxt76jqrOeprCKk56EjZluch1sd4Zmah9cYSFHVmcuPlIqNywoOE4lNEodL0UmfficlxSVnadxfYtIUiU0Q1cwiv8sZt3z9PWzucCYlBSRmqWAipZ0dh1fa3teYyE8S185R1spOU8iM0o8lPXe4eTd4OPY298ngP/P0tcgatPIvAu+tA0mgf8YUqNnc4NVXCImgP8OMmEMKVEkNUsJIT8mgP8mgv8qgv/JvQuyuL+tphCgqLGWkhUXdupeanoWcOctgvlUvftjxfz5+fokePDq2gQic+QeY8QZV6yblhQXTJQWSZEZfO4TP30SPHUNLVYLJUcngP8ngf8og/8fauEmgv8ka+UiceIQoPtAn/XV2NwHfPcOh/YsYtU3lvN4y/qFzPkuZtwVkfVUs/guauNwvvdjtvcsX88Reu4sZNYVgfB2vPU3nfUtaOBIpvYbjPBtsfRWqfItZ9cZeOsmgP//////6wAHlf8tZ+ErY9aT5f8Inf993f8Ejf+G4v9z2P+e7P9k0f8Ko/8Ch/9vzPsbnvk/q/dKnfA3feg1dORtf45jAAAA6XRSTlPMAN6l1Ovx6uHd7+7Yzfjy5+bP/fLw6Nnq2tHN8trT/eHa1M/Oze7q3NXT0Pv59vTv5uDb19bW1M3NzPf08e3s49/e0fn59/Tj4d7b2dbOk/z6+Pbu6+Xl5OPi2NjWzP7+/fr18fDt6Obm4tvT0M/7+fXz8/Ls6ejk49/e3dvX0tLR0dD76Obf19MG/vzs5+Xi39vb1dTS0A329vXz8/Hx7+rl3dnV1dLQzr9uK/Hs6+nm3dvaQP79/fr59+3n5+Hg3dva09DNe1JPNSca+Pj08eXj4uDW1NPPwb+2rqeWiIKCfXVoW005M5oIJp8AAAmwSURBVHja7NfpUxJxHMfxTzMLC4vcyimVgigIWiiJEQpoGRVpHhhpalpaqaVWXpVm1jg12n1Md9Nd0zk9aXrQv9Df0fpP9FsqazpcGsNs4j3D7wH75DX7/bH7A4sWWElQEpQEfVcSxFcSxFcSxFcSxNd/A3paef/6h7/Y9fuVT78Bnaw8dufBY/zFHj+4c6zy5BfQySc3H+Kv9/Dmk5OfQZU3sSC6WfkJ9PTYArg/XA+PPY2BKu9ggXSnMga6/wALpAf3Y6Drj7FAenw9BvqABdOHn4GKG5rwq2wUZrKAKpsX0FHWAsUB0gDwXCaTDfhkpAhIFyd1DQ0NhYB+YJlDghrXvIDGWBVcm0kiwHea6p+yjew/PXIJwHpHj9frnfQCOXRzwHKJFZaUlFxKNEjJsuwEPudz1rROKZvValYPm7U54gHKWkUgqSno8gcHlWx3okGqIEsdlMS6C1/pQeuUUuFKuaaHlqEE3pELXh0ACVHr1CcCd9fLEj8yAUsXRqN9+dFoCoYEAoFLqWBEBMQVbVYPgtTtYsV7bBD028UJBx11svmb72pp3YQeVCrXgEJ4kAMdDTLNk/7Ozs5xYBkrowIRQb8g8aADCtYWrBHTushz7IlOtIbOKRTOkWt6aS+rfrOKq1mL12nqzcyyIUG/35pwUKmdtVDNKlrndjTBd1DGbJZGekqjTUihcmTgcmhxxa2m7G8uCvpH1ZZEg4rJc+iABLQOjkK0pvWcfs1Mplx0hgB8BemhppT7yR7C1WCiQShjVamFlyU0GJfeWcou7xzSu91BPQdSC7nIyEBAoyMWyUscdM8DyALppFMPD1yDEE/1XelxtrZKOJBDxKX+BAIc6tfz8aRu6sZMUnybVAUuFfetpQkoluJPg/6Jt/1vlATxlQTxlQTxNU+gcSn5gKsbM3kAPMLvtNL8Z0CFAZoaQB+45AilcQHw2e32VSfs3eApa2Ztz24EjHMHjUcPTJxTORUKk3TsxiOairlwsSRHo3FqNDmYtbVLtlUcQW561aaCjS8OnT2/ZtuSbOMcQYFl6jSNvjScOjZW2qqgfUwv8x6QiHBa0aMYxKxtXZKZdbsOtTsxXI6twxuqDhuL8k7NEURJBPQgXQIrADl0FFkAsUyE/RRFpWLWjjQCdR3mlqKMrSDVvgJQtG6OINGeyLiKEdhpAL2DViHjYBhbNCCCjBz4a8BTRl6eOXNJRe3ebAB5BX9iU0/QsgF/cU7aetjkPaNyKeQgiUW4olQqe8HTxsb0nSumNyBz2gC0HfoToHFrOEeBYkcxiqVyCP1Cn5WJgcimGuMZ2XAHcLbN3EL2dUsXUPcCwNK2uZ6pS+g+D0LCMwDk7lJlGEgjoLhGtmLb7kNkE7ffzqitANC1Kft8/d7zcwRpJVMvJS4lngekpv0arZL8f08loJDf6/f7vSLM2r3aHWuNyNq1vSMXJMPx7e2NmCPonB3wjABSbdNQuBCmUY/HBVxI+VQYP/QfvsviLwniKwnia15AHk+KpRsmkMg6U7gY6ARPCQFdUNIpOuSDS47+VHI+awWQrw+FZKFQA2ava2PuClIXuIoOGwAszuBW8xEAmWTJIpcXG8lFM3ctlx/UoEnV+Mscwj7XnokrOXRDWVnZDUCvoUymGpOJwmzl7mrJOFRdXV3VDlJ99Y6qRtSX3646jMzVFWthrNhIDNMVFZvKM7GmAMCzpfwgt87LrBcrwEAVvLqeLqGd9EtIa0TwMVcZBrOWfeptBkj7hgF0VZmxdLdhuggF1aivz8zD7i3gQADaX3wC3YoDFBigA8sZa5MkNjJBpy3fZrMohSKs0mq1NzBrRqzmQCv2ZgHYsmM4ex1532ahaNqwpaMx3VxhnAG9I6CVK1fGc4fCUHgQ0WgVwGWZbplmec/y5aNasQi9wWAwHzzFQHUdIL3bW7e7fG1Wed2G2pZMQ3rb4V3r1m2vJ6D09NX7zFizjTQdB2hof41EgkJvGGFrb5P8kXuV2/7t8YMfZF6yGKT6vNhhqKh9x3BLLgDDPuOmrjVdi6cbG4erCuIfGYQqVwAWhwqAXCpU6iIReQy0ymQy9cUDWpsOosL51QSVbjxuQEEeSHX3DJuwPSM2suzs+EHSM1e9KuiGSjmQSfH1gNYrFovjGln1IW6nbF2571ZdVQZut72KzXHxDqCto3xjDLR0X/y/so/s2b9vEmEcx/HPgBj0CJbAtd61PVJ6/EzhTkHKpS21oEW4ECEYm1ZME8XeUQuJ7WLaWoOBwFKh1jI52cHV1T9J/wkfXA4dDvWSDm1fw5Nv8izv4Um+w7MSvpVdoLN4QMdp85hpxObxOEhQR5gRBGEmAX0fGOQyLCDdZsFnKjGAzVSmQJyS2b3/llwDCGYkMgLy5PCgOgg7gCr8s3YEyBgFeifQc6F22b+4DBrmMujM6AdlD4DXyTr+MDGKv1FKQ5NmjAdtxkUKcLTNVmhMzWbz1QI5TNAlq6qXU1W1hMNarbaHUMx40KKlH/QJmwfQVCORozlhNxKpQpdSXu3b7oLleZ5R8l6lZTSoJ4gTZjP5GbNgUH1n7WS+jSEU71RfsYsg8TKd3xivGA3CTZFC73sAg/w3wnQinrx+9XhI0L3xX/ZR4B5+KyLFHBoPenqNwlJ4BYPuZq3wRYHoZ+hqaC96uVSCxLGNVaNBds8chSsmAb/xfTn62MHWLvQVMz84Yl0Gk3KngndC2HAZDfKMilTAhhELBiSdx2NwvIvsbEFXqMWBKMtIP5KffX3shbdrNGga4tr8G0yH29AsLvkTqNqdWRp6nhRyXItYllFIvXBt52Mo7xkMIsTOewBtGhpq1uq02Rw+2KFHcfHrz4m8DAZAo3F6P5QzHmTF/5JYqQRi0o0+XpJy52KX/WTf3k0ABqEoDN8kA7qUQ9jaam0hKopvnCoghJAs4C38JvgHOIcXQKLwGSQSIJHEDJIWkLByBjEeAYXI2Qw6us6AQNb92VNL6mE5T+W7OFfVuHAuFJyp6rPJH41cC5E2kJ8E8NhBO2gH/eygu936yFEYCKIw3O/69AV8DuRh1izRMKNJ5JxZdNvG5CjMAVAvXgshUd8JftWi9FyeNWjS/G9NQfAZFPQrcRwvF6v3+igHFh80KMTrddazWG02ha83kPig4HN7OO5vBzqdbQMsBdp4mySJ+bvYzAdYCrxa1mOK1mo7BE2BVzaZota6mgNNwYOeMWk+ivQMPAUfftJbUBcEv0HzdJePfgMQ/AahswujEgi+g/AdtkHwH1QKyyD4DwLzEl9hfkjQXRLkIkEuEuQiQS4S5CJBLhLkIkEPdwWle2optfhn9gAAAABJRU5ErkJggg=="},fc5e:function(t,e){t.exports="data:image/png;base64,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"}}]);