(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-93d7c22e"],{"494b":function(e,t,n){},"8c07":function(e,t,n){"use strict";n("494b")},cd11:function(e,t,n){"use strict";n.r(t);var l=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"bottom-element-menu"},[n("div",{staticClass:"menu-list"},e._l(e.menuList,(function(t){return n("div",{key:t.label,staticClass:"list-item",class:{active:e.activeMenu==t.label&&t.needActive},on:{click:function(n){return n.stopPropagation(),e.handleMenuClick(t)}}},["color"==t.label?n("label",{staticClass:"color-div",style:{background:e.colorWellValue}},[n("input",{staticClass:"colorWellInput",attrs:{type:"color",value:"#CC0000",id:"colorWell"},on:{change:function(t){return e.getColorWell(t)}}})]):n("CommonSVG",{attrs:{"icon-class":t.icon}}),n("span",[e._v(e._s(t.title))]),"select"==t.label?n("div",{directives:[{name:"show",rawName:"v-show",value:e.selectState,expression:"selectState"}],staticClass:"select-list"},[n("ul",e._l(t.children,(function(t,l){return n("li",{key:"select"+l,on:{click:function(n){return e.handleSelectChild(t)}}},[e._v(" "+e._s(t.btnName)+" ")])})),0)]):e._e()],1)})),0),e.twinkleState?n("div",{staticClass:"twink-option"},[n("div",{staticClass:"option-item"},[e._v(" "+e._s(e.$t("bottomMenu.element.label"))+"： "),n("label",{staticClass:"color-div",style:{background:e.twinkData.color},attrs:{for:"twinkColorWell"}},[n("input",{staticClass:"colorWellInput",attrs:{type:"color",value:"#1effff",id:"twinkColorWell"},on:{change:function(t){return e.getTwinkColorWell(t)}}})])]),n("label",{staticClass:"option-item",attrs:{for:"twinkInterval"}},[e._v(" "+e._s(e.$t("bottomMenu.element.label1"))+"： "),n("input",{directives:[{name:"model",rawName:"v-model",value:e.twinkData.time,expression:"twinkData.time"}],attrs:{type:"number",id:"twinkInterval"},domProps:{value:e.twinkData.time},on:{change:function(t){return e.getColorWell(t)},input:function(t){t.target.composing||e.$set(e.twinkData,"time",t.target.value)}}}),e._v("  S ")]),n("div",{staticClass:"twink-btn",on:{click:function(t){return e.setElementTwink()}}},[e._v(" "+e._s(e.$t("menuIconName.confirm"))+" ")])]):e._e()])},i=[],c=(n("d3b7"),n("3ca3"),n("ddb0"),n("a630"),n("159b"),{name:"BottomElementMenu",components:{CommonSVG:function(){return n.e("chunk-51f90655").then(n.bind(null,"17d0"))}},data:function(){return{menuList:[{title:this.$t("bottomMenu.element.attribute"),icon:"attribute_feature",label:"attribute",needActive:!0},{title:this.$t("bottomMenu.element.select.name"),icon:"select_feature",label:"select",children:[],needActive:!0},{title:this.$t("menuIconName.hide"),icon:"hidden_cut",label:"hide"},{title:this.$t("menuIconName.isolate"),icon:"isolate_feature",label:"isolate"},{title:this.$t("menuIconName.color"),icon:"isolate_feature",label:"color",needActive:!0},{title:this.$t("menuIconName.zoom"),icon:"focusing_feature",label:"zoom"},{title:this.$t("menuIconName.twinkle"),icon:"twinkle_feature",label:"twinkle",needActive:!0},{title:this.$t("messageTips.cancel"),icon:"quit",label:"cancel"}],activeMenu:"",selectState:!1,colorWellValue:"#CC0000",twinkleState:!1,twinkData:{color:"#1effff",time:.5}}},created:function(){this.setMenuList(),this.$bus.on("closeAttributeDialog",this.handleMenuClick)},methods:{setElementTwink:function(){var e=window.scene.selectedObjects;if(e.size>0){var t=Array.from(e.keys()),n={objectIDs:t,interval:1e3*this.twinkData.time,color:this.twinkData.color};window.scene.execute("twinkle",n),window.scene.clearSelection(),window.scene.render(),this.$emit("cancel")}},getTwinkColorWell:function(e){this.twinkData.color=e.target.value},getColorWell:function(e){this.colorWellValue=e.target.value;var t=window.scene.selectedObjects;if(t.size>0){var n={objectIDs:Array.from(t.keys()),color:this.colorWellValue,opacity:1};window.scene.execute("color",n),window.scene.clearSelection(),window.scene.render(),this.$emit("cancel")}},selectedSameLevel:function(){var e=window.scene.selectedObjects;if(e.size>0){var t=Array.from(e.values()),n=t[0],l=n.model;l.objects.forEach((function(e){-1!==e.levelID.indexOf(n.levelID)&&(e.selected=!0)})),window.scene.render()}},selectedSameType:function(){var e=window.scene.selectedObjects;if(e.size>0){var t=Array.from(e.values()),n=t[0],l=n.model;l.objects.forEach((function(e){e.typeID==n.typeID&&(e.selected=!0)})),window.scene.render()}},selectedSameFamily:function(){var e=window.scene.selectedObjects;if(e.size>0){var t=Array.from(e.values()),n=t[0],l=n.model;l.objects.forEach((function(e){e.familyID==n.familyID&&(e.selected=!0)})),window.scene.render()}},selectedSameCategory:function(){var e=window.scene.selectedObjects;if(e.size>0){var t=Array.from(e.values()),n=t[0],l=n.model;l.objects.forEach((function(e){e.categoryID==n.categoryID&&(e.selected=!0)})),window.scene.render()}},selectedReverseElection:function(){var e=window.scene.selectedObjects,t=window.scene.getInverseSelection();e.forEach((function(e){e.selected=!1})),t.forEach((function(e){e.selected=!0})),window.scene.render()},handleSelectChild:function(e){var t=e.fnHandler;this.$options.methods[t].call(this)},setMenuList:function(){var e=window.scene.selectedObjects;e.size>1?this.menulists[1].children=[{fnHandler:"selectedReverseElection",btnName:this.$t("bottomMenu.element.select.extend.label4")}]:this.menuList[1].children=[{fnHandler:"selectedSameLevel",btnName:this.$t("bottomMenu.element.select.extend.label")},{fnHandler:"selectedSameType",btnName:this.$t("bottomMenu.element.select.extend.label1")},{fnHandler:"selectedSameFamily",btnName:this.$t("bottomMenu.element.select.extend.label2")},{fnHandler:"selectedSameCategory",btnName:this.$t("bottomMenu.element.select.extend.label3")},{fnHandler:"selectedReverseElection",btnName:this.$t("bottomMenu.element.select.extend.label4")}]},handleElementIsolate:function(){var e=window.scene.selectedObjects,t={objectIDs:Array.from(e.keys())};window.scene.execute("isolate",t),window.scene.render()},handleElementZoom:function(){var e=window.scene.selectedObjects;if(e.size>0){var t={objectIDs:Array.from(e.keys())};window.scene.execute("fit",t)}},handleElementHide:function(){var e=window.scene.selectedObjects;if(e.size>0){var t={objectIDs:Array.from(e.keys())};window.scene.execute("hide",t),window.scene.clearSelection(),window.scene.render(),this.$emit("cancel")}},setBottomMenuDialogState:function(e){"select"!==e.label&&this.selectState&&(this.selectState=!1),"attribute"!=e.label&&"attribute"==this.activeMenu&&this.$emit("handleAttrState"),"twinkle"!=e.label&&this.twinkleState&&(this.twinkleState=!1)},handleMenuClick:function(e){switch(this.setBottomMenuDialogState(e),e.needActive?e.label===this.activeMenu?this.activeMenu="":this.activeMenu=e.label:this.activeMenu="",e.label){case"attribute":this.$emit("handleAttrState");break;case"select":this.selectState=!this.selectState;break;case"hide":this.handleElementHide();break;case"isolate":this.handleElementIsolate();break;case"color":document.querySelector("#colorWell").click();break;case"zoom":this.handleElementZoom();break;case"twinkle":this.twinkleState=!this.twinkleState;break;case"cancel":this.$emit("cancel"),window.scene.clearSelection(),window.scene.render();break}}},beforeDestroy:function(){this.$bus.off("closeAttributeDialog",this.handleMenuClick)}}),s=c,a=(n("8c07"),n("2877")),o=Object(a["a"])(s,l,i,!1,null,"cdc0cb64",null);t["default"]=o.exports}}]);