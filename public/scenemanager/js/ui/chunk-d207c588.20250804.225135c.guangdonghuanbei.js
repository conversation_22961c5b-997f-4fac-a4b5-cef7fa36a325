(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-d207c588"],{"009f":function(e,t,a){"use strict";var i=a("a47e"),s={underlay:{name:i["a"].t("featureDatas.underlay.name"),label:"underlay",icon:"underlay_element",type:7,listState:!0,datas:[]},model:{name:i["a"].t("featureDatas.model.name"),label:"model",icon:"model_element",type:0,listState:!0,datas:[]},dem:{name:i["a"].t("featureDatas.dem.name"),label:"dem",icon:"dem_element",type:8,listState:!0,datas:[]},wmts:{name:i["a"].t("featureDatas.wmts.name"),label:"wmts",icon:"wmts_element",type:1,listState:!0,datas:[]},wms:{name:i["a"].t("featureDatas.wms.name"),label:"wms",icon:"wms_element",type:120,listState:!0,datas:[]},tms:{name:i["a"].t("featureDatas.tms.name"),label:"tms",icon:"tms_element",type:125,listState:!0,datas:[]},gltf:{name:i["a"].t("featureDatas.gltf.name"),label:"gltf",icon:"gltf_element",type:4,listState:!0,datas:[]},fbx:{name:i["a"].t("featureDatas.fbx.name"),label:"fbx",icon:"fbx",type:1,listState:!0,datas:[]},_3dBuilding:{name:i["a"].t("featureDatas._3dBuilding.name"),label:"_3dBuilding",icon:"_3dBuilding",type:null,listState:!0,datas:[]},_3dTiles:{name:i["a"].t("featureDatas._3dTiles.name"),label:"_3dTiles",icon:"_3dTiles",type:3,listState:!0,datas:[]},skybox:{name:i["a"].t("featureDatas.skybox.name"),label:"skybox",icon:"skybox_element",type:17,listState:!0,datas:[]},video:{name:i["a"].t("featureDatas.video.name"),label:"video",icon:"video_element",type:10,listState:!0,datas:[]},panorama:{name:i["a"].t("featureDatas.panorama.name"),label:"panorama",icon:"panorama_element",type:9,listState:!0,datas:[]},shield:{name:i["a"].t("featureDatas.shield.name"),label:"shield",icon:"shield_element",type:12,listState:!0,datas:[]},heatmap:{name:i["a"].t("featureDatas.heatmap.name"),label:"heatmap",icon:"heatmap_element",type:13,listState:!0,datas:[]},ripplewall:{name:i["a"].t("featureDatas.ripplewall.name"),label:"ripplewall",icon:"ripplewall_element",type:14,listState:!0,datas:[]},ring:{name:i["a"].t("featureDatas.ring.name"),label:"ring",icon:"ring_element",type:15,listState:!0,datas:[]},billboard:{name:i["a"].t("featureDatas.billboard.name"),label:"billboard",icon:"billboard",type:16,listState:!0,datas:[]},geoJSON:{name:i["a"].t("featureDatas.geoJSON.name"),label:"geoJSON",icon:"geojson_element",type:2,listState:!0,datas:[]},annotation:{name:i["a"].t("featureDatas.annotation.name"),label:"annotation",icon:"annotation_element",type:5,listState:!0,datas:[]},shp:{name:i["a"].t("featureDatas.shp.name"),label:"shp",icon:"shp_element",type:6,listState:!0,datas:[]},radar:{name:i["a"].t("featureDatas.radar.name"),label:"radar",icon:"radar_element",type:95,listState:!0,datas:[]},polyline:{name:i["a"].t("featureDatas.polyline.name"),label:"polyline",icon:"polygon_line_element",type:105,listState:!0,datas:[]},polygon:{name:i["a"].t("featureDatas.polygon.name"),label:"polygon",icon:"polygon_surface_element",type:100,listState:!0,datas:[]},flame:{name:i["a"].t("featureDatas.flame.name"),label:"flame",icon:"fire_element",type:110,listState:!0,datas:[]},smoke:{name:i["a"].t("featureDatas.smoke.name"),label:"smoke",icon:"smoke_element",type:115,listState:!0,datas:[]},"batch-extrude":{name:i["a"].t("featureDatas['batch-extrude'].name"),label:"batch_extrude",icon:"batch_extrude_element",type:120,listState:!0,datas:[]},kml:{name:i["a"].t("featureDatas.kml.name"),label:"kml",icon:"kml_element",type:125,listState:!0,datas:[]},vectorextrude:{name:i["a"].t("featureDatas.vectorextrude.name"),label:"vectorextrude",icon:"vectorextrude_element",type:130,listState:!0,datas:[]},waterfall:{name:i["a"].t("featureDatas.waterfall.name"),label:"waterfall",icon:"waterfall_element",type:116,listState:!0,datas:[]}};t["a"]=s},"03c2":function(e,t,a){},"1e3a":function(e,t,a){var i={"./TriggerFlame.vue":"332d","./TriggerRadar.vue":"b4b8","./TriggerRing.vue":"9312","./TriggerRipplewall.vue":"881b","./TriggerShield.vue":"a38c","./TriggerSmoke.vue":"8c00","./TriggerWaterfall.vue":"f371"};function s(e){var t=n(e);return a(t)}function n(e){if(!a.o(i,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return i[e]}s.keys=function(){return Object.keys(i)},s.resolve=n,e.exports=s,s.id="1e3a"},"2a61":function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return o}));a("d81d"),a("b64b"),a("b0c0"),a("d3b7"),a("159b"),a("e9c4"),a("99af");var i=a("8bbf"),s=a.n(i),n=a("a47e");function r(e,t,a){var i=null;i=a.isDragData?window.scene.addTrigger("range"):window.scene.triggers.get(a.id),i.center=Object.keys(e.range.position).map((function(t){return parseFloat(e.range.position[t]+"")})),i.radius=parseFloat(e.range.radius+""),i.name=e.name||n["a"].t("topToolBarMenu.advanced.children.trigger.name")+"-"+(new Date).getTime();var r=l(t);r.length>0?(r[0].length>0&&(i.triggerCommands=r[0]),r[1].length>0&&(i.exitCommands=r[1])):window.scene.removeTrigger(i.id),a.isDragData?s.a.prototype.$notify({title:n["a"].t("topToolBarMenu.advanced.children.trigger.name"),message:n["a"].t("others.added"),type:"success"}):s.a.prototype.$notify({title:n["a"].t("topToolBarMenu.advanced.children.trigger.name"),message:n["a"].t("others.updated"),type:"success"})}function o(e,t,a){var i=null;i=a.isDragData?window.scene.addTrigger("sensor"):window.scene.triggers.get(a.id),i.points=[],e.sensor.position.forEach((function(t,a){i.points[a]=[parseFloat(t.x+""),parseFloat(t.y+""),parseFloat(e.sensor.height+"")]})),i.objectID=e.sensor.target.id,"element"==e.sensor.target.type?i.objectType="object":i.objectType="feature",i.name=e.name||n["a"].t("topToolBarMenu.advanced.children.trigger.name")+"-"+(new Date).getTime();var r=l(t);r.length>0?(r[0].length>0&&(i.triggerCommands=r[0]),r[1].length>0&&(i.exitCommands=r[1])):window.scene.removeTrigger(i.id),a.isDragData?s.a.prototype.$notify({title:n["a"].t("topToolBarMenu.advanced.children.trigger.name"),message:n["a"].t("others.added"),type:"success"}):s.a.prototype.$notify({title:n["a"].t("topToolBarMenu.advanced.children.trigger.name"),message:n["a"].t("others.updated"),type:"success"})}function l(e){var t=[[],[]];return e.forEach((function(e,a){e.behaviorList.length>0&&e.behaviorList.forEach((function(i,s){var n=e.objectsList[s],r=null;if(""!=i){switch(i){case"element_zoom":case"feature_zoom":r=d(n,e,s,a);break;case"element_isolate":r=u(n,e,s,a);break;case"element_hide":case"feature_hide":r=m(n,e,s,a);break;case"element_show":case"feature_show":r=p(n,e,s,a);break;case"animate_play":case"animate_pause":case"animate_stop":r=h(n,e,s,a);break;case"feature_postData":r=c(n,e,s,a)}"{}"!==JSON.stringify(r)&&("trigger"==e.eventsValue?t[0].push(r):t[1].push(r))}}))})),t}function c(e,t,a,i){var s={command:"post"};return s.args={dataKey:t.selectedFeatures[a][0].dataKey,data:t.selectedFeatures[a][0].postdata,additional:{objType:e,index:a,selectedObj:t.selectedFeatures[a],li:i}},s}function d(e,t,a,i){var s={};if(0==e&&t.selectedElements[a].length>0){var n=[];t.selectedElements[a].forEach((function(e){n.push(e.id)})),s.command="fit",s.args={objectIDs:n,additional:{selectedObj:t.selectedElements[a],index:a,objType:e,li:i}}}if(1==e&&t.selectedFeatures[a].length>0){var r=[];t.selectedFeatures[a].forEach((function(e){r.push(e.id)})),s.command="fit",s.args={featureIDs:r,additional:{selectedObj:t.selectedFeatures[a],index:a,objType:e,li:i}}}return s}function u(e,t,a,i){var s={};if(0==e&&t.selectedElements[a].length>0){var n=[];t.selectedElements[a].forEach((function(e){n.push(e.id)})),s.command="isolate",s.args={objectIDs:n,additional:{selectedObj:t.selectedElements[a],index:a,objType:e,li:i}}}return s}function m(e,t,a,i){var s={};if(0==e&&t.selectedElements[a].length>0){var n=[];t.selectedElements[a].forEach((function(e){n.push(e.id)})),s.command="hide",s.args={objectIDs:n,additional:{selectedObj:t.selectedElements[a],index:a,objType:e,li:i}}}if(1==e&&t.selectedFeatures[a].length>0){var r=[];t.selectedFeatures[a].forEach((function(e){r.push(e.id)})),s.command="hide",s.args={featureIDs:r,additional:{selectedObj:t.selectedFeatures[a],index:a,objType:e,li:i}}}return s}function p(e,t,a,i){var s={};if(0==e&&t.selectedElements[a].length>0){var n=[];t.selectedElements[a].forEach((function(e){n.push(e.id)})),s.command="show",s.args={objectIDs:n,additional:{selectedObj:t.selectedElements[a],index:a,objType:e,li:i}}}if(1==e&&t.selectedFeatures[a].length>0){var r=[];t.selectedFeatures[a].forEach((function(e){r.push(e.id)})),s.command="show",s.args={featureIDs:r,additional:{selectedObj:t.selectedFeatures[a],index:a,objType:e,li:i}}}return s}function h(e,t,a,i){var s={};if(t.selectedAnimates[a].length>0){var n=[],r="";switch(t.behaviorList[a]){case"animate_play":r="play";break;case"animate_pause":r="pause";break;case"animate_stop":r="stop";break}t.selectedAnimates[a].forEach((function(e){n=n.concat(e.resultIds)})),s.command=r,s.args={animationIDs:n,additional:{selectedObj:t.selectedAnimates[a],index:a,objType:e,li:i}}}return s}},3285:function(e,t,a){"use strict";a("d3b7"),a("3ca3"),a("ddb0");var i=a("f7fe"),s=a.n(i);t["a"]={created:function(){this.inputChangeOrigin=s()(this.inputChangeOrigin,800),this.inputChangeScale=s()(this.inputChangeScale,800),this.inputChangeRotation=s()(this.inputChangeRotation,500),this.inputChangeOffset=s()(this.inputChangeOffset,500)},components:{CommonSVG:function(){return a.e("chunk-51f90655").then(a.bind(null,"17d0"))}},methods:{clickCheckedCoordinate:function(){this.$emit("clickCheckedCoordinate")},handleModelTransform:function(e){this.$emit("handleModelTransform",e)},inputChangeOrigin:function(e,t){var a=""==e?0:parseFloat(e);"longitude"==t&&(a<=-180&&(a=-179,this.elementDatas.longitude=a),a>=180&&(a=179,this.elementDatas.longitude=a)),"latitude"==t&&(a<=-90&&(a=-89,this.elementDatas.latitude=a),a>=90&&(a=89,this.elementDatas.latitude=a)),this.$emit("inputChangeOrigin",{type:t,value:a})},inputChangeRotation:function(e,t){this.$emit("inputChangeRotation",{type:t,value:e})},inputChangeOffset:function(e,t){this.$emit("inputChangeOffset",{type:t,value:e})},inputChangeScale:function(e,t){var a=""==e||0==e?1:parseFloat(e+"");switch(t){case"x":this.elementDatas.scale[0]=a;break;case"y":this.elementDatas.scale[1]=a;break;case"z":this.elementDatas.scale[2]=a;break}this.$emit("inputChangeScale",{type:t,value:a})}}}},"332d":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e._t("default"),a("div",{staticClass:"styleSet-list-div"},[a("div",{staticClass:"items-center margin-bottom-8 margin-top-8"},[a("span",{staticClass:"item-title"},[e._v(e._s(e.$t("formRelational.width.label")))]),a("el-input",{attrs:{size:"mini",type:"number",placeholder:e.$t("formRelational.width.placeholder")},on:{input:function(t){return e.inputChangePostData("width")}},scopedSlots:e._u([{key:"append",fn:function(){return[a("span",[e._v("m")])]},proxy:!0}]),model:{value:e.params.width,callback:function(t){e.$set(e.params,"width",t)},expression:"params.width"}})],1),a("div",{staticClass:"items-center margin-bottom-8"},[a("span",{staticClass:"item-title"},[e._v(e._s(e.$t("formRelational.top.label")))]),a("el-input",{attrs:{size:"mini",type:"number",placeholder:e.$t("formRelational.top.placeholder")},on:{input:function(t){return e.inputChangePostData("height")}},scopedSlots:e._u([{key:"append",fn:function(){return[a("span",[e._v("m")])]},proxy:!0}]),model:{value:e.params.height,callback:function(t){e.$set(e.params,"height",t)},expression:"params.height"}})],1),a("div",{staticClass:"items-center margin-bottom-8"},[a("span",{staticClass:"item-title"},[e._v(e._s(e.$t("formRelational.speed.label")))]),a("el-slider",{staticStyle:{width:"100%"},attrs:{min:1,max:10,step:1},on:{change:function(t){return e.inputChangePostData("speed")}},model:{value:e.params.speed,callback:function(t){e.$set(e.params,"speed",t)},expression:"params.speed"}}),a("span",{staticClass:"slider-title"},[e._v(e._s(e.params.speed))])],1),a("div",{staticClass:"items-center margin-bottom-8"},[a("span",{staticClass:"item-title"},[e._v(e._s(e.$t("formRelational.gain.label")))]),a("el-slider",{staticStyle:{width:"100%"},attrs:{min:.1,max:5,step:.1},on:{change:function(t){return e.inputChangePostData("gain")}},model:{value:e.params.gain,callback:function(t){e.$set(e.params,"gain",t)},expression:"params.gain"}}),a("span",{staticClass:"slider-title"},[e._v(e._s(e.params.gain))])],1),a("div",{staticClass:"items-center margin-bottom-8"},[a("span",{staticClass:"item-title"},[e._v(e._s(e.$t("formRelational.lacunarity.label")))]),a("el-slider",{staticStyle:{width:"100%"},attrs:{min:1,max:10,step:1},on:{change:function(t){return e.inputChangePostData("lacunarity")}},model:{value:e.params.lacunarity,callback:function(t){e.$set(e.params,"lacunarity",t)},expression:"params.lacunarity"}}),a("span",{staticClass:"slider-title"},[e._v(e._s(e.params.lacunarity))])],1),a("div",{staticClass:"items-center margin-bottom-8"},[a("span",{staticClass:"item-title"},[e._v(e._s(e.$t("formRelational.magnitude.label")))]),a("el-slider",{staticStyle:{width:"100%"},attrs:{min:1,max:10,step:.1},on:{change:function(t){return e.inputChangePostData("magnitude")}},model:{value:e.params.magnitude,callback:function(t){e.$set(e.params,"magnitude",t)},expression:"params.magnitude"}}),a("span",{staticClass:"slider-title"},[e._v(e._s(e.params.magnitude))])],1)])],2)},s=[],n={name:"TriggerFlame",props:["dragData"],data:function(){return{params:{width:10,height:30,speed:1,gain:.1,lacunarity:1,magnitude:1}}},created:function(){this.dragData.isDragData&&this.$emit("saveTriggerExtendParams",this.params)},methods:{initParams:function(e){this.params=function(e){var t=e.width,a=e.height,i=e.speed,s=e.gain,n=e.lacunarity,r=e.magnitude;return{width:t,height:a,speed:i,gain:s,lacunarity:n,magnitude:r}}(e)},inputChangePostData:function(e){var t=parseFloat(this.params[e]+"");this.params[e]=t,this.$emit("saveTriggerExtendParams",this.params)}}},r=n,o=(a("fb80"),a("2877")),l=Object(o["a"])(r,i,s,!1,null,"79100a48",null);t["default"]=l.exports},"354f":function(e,t,a){},"43fe":function(e){e.exports=JSON.parse('{"envMapsName":[{"name":"影棚光1","src":"./image/environmentSet/StudioSpotsDots.jpg"},{"name":"影棚光2","src":"./image/environmentSet/StudioSpots.jpg"},{"name":"阳光1","src":"./image/environmentSet/Sun10Clock.jpg"},{"name":"阳光2","src":"./image/environmentSet/Sun10ClockNorth.jpg"},{"name":"阳光3","src":"./image/environmentSet/Sun12Clock.jpg"},{"name":"阳光4","src":"./image/environmentSet/Sun12ClockNorth.jpg"},{"name":"大厅","src":"./image/environmentSet/MuseumLobby.jpg"},{"name":"正厅","src":"./image/environmentSet/ModernAtrium.jpg"},{"name":"日落1","src":"./image/environmentSet/SunsetOverRocks1.jpg"},{"name":"日落2","src":"./image/environmentSet/SunsetOverRocks2.jpg"},{"name":"水库","src":"./image/environmentSet/Reservoir.jpg"},{"name":"夜景","src":"./image/environmentSet/SidewalkAtNight.jpg"},{"name":"植物园","src":"./image/environmentSet/BotanicalGardens.jpg"},{"name":"海边","src":"./image/environmentSet/Boat.jpg"},{"name":"路上","src":"./image/environmentSet/Road.jpg"},{"name":"广场","src":"./image/environmentSet/Sunny.jpg"},{"name":"沙漠","src":"./image/environmentSet/Loess.jpg"},{"name":"日落","src":"./image/environmentSet/Sunset.jpg"}]}')},"49a3":function(e,t,a){"use strict";a("a9c5")},"4b6c":function(e,t,a){},"550c":function(e,t,a){"use strict";a("4b6c")},"5d3f":function(e,t,a){"use strict";a("9285")},"5de3":function(e,t,a){"use strict";a("b0c0"),a("d3b7"),a("159b");t["a"]={methods:{handelSceneStyleReset:function(e){var t=e?e.type:this.dragData.type,a=this.$store.state.scene.featureRawData;if(a=JSON.parse(a),null==a&&"advanced_material"!==t)return!1;switch(t){case"model":this.resetModelFeature(a);break;case"dem":this.resetDemFeature(a);break;case"_3dBuilding":this.resetBuildingFeature(a);break;case"fbx":case"gltf":this.resetGltfOrFbxFeature(a);break;case"annotation":case"billboard":this.resetAnchorFeature(a);break;case"_3dTiles":this.reset3DTilesFeature(a);break;case"panorama":this.resetPanoramaFeature(a);break;case"polygon":this.resetPolygonFeature(a);break;case"heatmap":this.resetHeatmapFeature(a);break;case"ripplewall":this.resetRipplewallFeature(a);break;case"radar":this.resetRadarFeature(a);break;case"shield":this.resetShieldFeature(a);break;case"ring":this.resetRingFeature(a);break;case"flame":case"smoke":this.resetSmoleFlameFeature(a);break;case"polyline":this.resetPolylineFeature(a);break;case"batch-extrude":this.resetBatchExtrudeFeature(a);break;case"vectorextrude":this.resetVectorExtrudeFeature(a);break;case"wmts":break;case"wms":break;case"tms":break;case"shp":this.resetSHPFeature(a);break;case"kml":break;case"geoJSON":this.resetGeoJSONFeature(a);break;case"advanced_envmap":this.resetAdvancedEnvmapDatas(a);break;case"advanced_setting":this.resetAdvancedSettingDatas(a);break;case"pathAnimation":this.resetPathAnimationDatas(a);break;case"waterfall":this.resetWaterfallDatas(a);break;case"trigger":this.resetTriggerSettingDatas(a);break;case"advanced_material":var i=this.$store.state.scene.advancedMaterialRawData;i.params.length>0&&(i.params=JSON.parse(i.params[0]),this.resetAdvancedMaterialDatas(i));break;case"projector":this.resetProjectorDatas(a);break;case"video":this.resetVideoDatas(a);break;case"snow":this.resetSnowFeature(a);break}setTimeout((function(){window.scene.render()}),300)},resetModelFeature:function(e){var t=window.scene.features.get(e.id);t.version!=e.version?(t.dispose(),window.scene.render(),this.handleModelVersionReset(e)):(t.origin=e.origin,t.altitude=e.altitude,t.rotation=e.rotation,t.name=e.name,t.offset=e.offset,t.area=e.area,t.part=e.part,t.space=e.space,t.texture=e.texture,e.correction&&(t.correction=e.correction)),setTimeout((function(){window.scene.render()}),300)},handleModelVersionReset:function(e){var t=window.location.origin+"/MODEL_URL";t=window.IP_CONFIG.MODEL_URL;var a=window.scene.addFeature("model",e.id);a.origin=e.origin,a.altitude=e.altitude,a.rotation=e.rotation,a.name=e.name,a.offset=e.offset,a.area=e.area,a.part=e.part,a.space=e.space,a.texture=e.texture,e.correction&&(a.correction=e.correction),a.server=t,a.modelID=e.modelID,a.vaultID=e.vaultID,a.version=e.version,a.load().then((function(){a.activeView().then((function(){window.scene.fit2Feature(a)}))}))},resetDemFeature:function(e){var t=window.scene.features.get(e.id);t.name=e.name,t.url=e.url,window.scene.postData({opacity:e.data.opacity},t.dataKey)},resetBuildingFeature:function(e){var t=window.scene.features.get(e.id);t.name=e.name,window.scene.postData({opacity:e.data.opacity,color:e.data.color,minzoom:e.data.minzoom},t.dataKey)},resetGltfOrFbxFeature:function(e){var t=window.scene.features.get(e.id);t.origin=e.origin,t.altitude=e.altitude,t.rotation=e.rotation,t.offset=e.offset,t.name=e.name,t.scale=e.scale,t.url=e.url,t.follow=e.follow,t.followType=e.followType,window.scene.postData({animation:e.data.animation,scale:e.data.scale},t.dataKey)},resetAnchorFeature:function(e){var t=window.scene.features.get(e.id);t.origin=e.origin,t.altitude=e.altitude,t.offset=e.offset,t.name=e.name,t.follow=e.follow,t.followType=e.followType;var a=e.data;window.scene.postData(a,t.dataKey)},reset3DTilesFeature:function(e){var t=window.scene.features.get(e.id);t.origin=e.origin,t.altitude=e.altitude,t.rotation=e.rotation,t.offset=e.offset,t.name=e.name,t.url=e.url,e.correction?t.correction=e.correction:t.correction=void 0,window.scene.postData(e.data,t.dataKey),t.id=e.id},resetPanoramaFeature:function(e){var t=window.scene.features.get(e.id);t.origin=e.origin,t.altitude=e.altitude,t.offset=e.offset,t.name=e.name,t.url=e.url,t.id=e.id,window.scene.postData({radius:e.data.radius,depthPath:e.data.depthPath},t.dataKey)},resetPolygonFeature:function(e){var t=window.scene.features.get(e.id);t.name=e.name,window.scene.postData(e.data,t.dataKey)},resetHeatmapFeature:function(e){var t=window.scene.features.get(e.id);t.name=e.name,t.altitude=e.altitude,window.scene.postData(e.data,t.dataKey)},resetRipplewallFeature:function(e){var t=window.scene.features.get(e.id);t.name=e.name,t.altitude=e.altitude,window.scene.postData(e.data,t.dataKey)},resetRadarFeature:function(e){var t=window.scene.features.get(e.id);t.origin=e.origin,t.altitude=e.altitude,t.offset=e.offset,t.name=e.name,t.follow=e.follow,t.followType=e.followType,window.scene.postData(e.data,t.dataKey)},resetShieldFeature:function(e){var t=window.scene.features.get(e.id);t.origin=e.origin,t.altitude=e.altitude,t.offset=e.offset,t.name=e.name,t.follow=e.follow,t.followType=e.followType,window.scene.postData(e.data,t.dataKey)},resetRingFeature:function(e){var t=window.scene.features.get(e.id);t.origin=e.origin,t.altitude=e.altitude,t.offset=e.offset,t.name=e.name,t.follow=e.follow,t.followType=e.followType,window.scene.postData(e.data,t.dataKey)},resetSmoleFlameFeature:function(e){var t=window.scene.features.get(e.id);t.origin=e.origin,t.altitude=e.altitude,t.offset=e.offset,t.name=e.name,t.follow=e.follow,t.followType=e.followType,window.scene.postData(e.data,t.dataKey)},resetWaterfallDatas:function(e){var t=window.scene.features.get(e.id);t.origin=e.origin,t.altitude=e.altitude,t.offset=e.offset,t.name=e.name,window.scene.postData(e.data,t.dataKey)},resetPolylineFeature:function(e){var t=window.scene.features.get(e.id);t.name=e.name,window.scene.postData(e.data,t.dataKey)},resetBatchExtrudeFeature:function(e){var t=window.scene.features.get(e.id);t.name=e.name,t.origin=e.origin,window.scene.postData(e.data,t.dataKey)},resetVectorExtrudeFeature:function(e){var t=window.scene.features.get(e.id);t.name=e.name,t.url=e.url,window.scene.postData(e.data,t.dataKey)},resetGeoJSONFeature:function(e){var t=window.scene.features.get(e.id);t.name=e.name,t.url=e.url,window.scene.postData(e.data,t.dataKey)},resetSHPFeature:function(e){var t=window.scene.features.get(e.id);t.name=e.name,t.url=e.url,window.scene.postData(e.data,t.dataKey)},resetAdvancedEnvmapDatas:function(e){window.scene.config.envmapIndex=e.img,window.scene.config.envAngle=e.angle,window.scene.config.envmapIntensity=e.intensity,window.scene.config.fogRange=e.fogRange,window.scene.config.fogIntensity=e.fogIntensity,setTimeout((function(){window.scene.render()}),300)},resetAdvancedSettingDatas:function(e){window.scene.config.cullingRatio=e.cullingRatio,window.scene.config.maxMemory=e.maxMemory,window.scene.config.dynamicReleasing=e.dynamicReleasing,window.scene.config.highQuality=e.highQuality,window.scene.config.highlightColor=e.highlightColor,window.scene.config.mapboxToken=e.mapboxToken,window.scene.config.highlight=e.highlight,window.scene.config.ssao=e.ssao,window.scene.config.shadow=e.shadow,window.scene.mv.renderMode=e.modeRadio},resetPathAnimationDatas:function(e){var t=this.$store.state.dialog.activeDialog;-1!==t.indexOf("pathAnimation")&&this.$store.commit("toggleActiveDialog","pathAnimation");var a=window.scene.findPathRoaming(e.id);window.scene.mv.tools.pathRoam.init(a),this.$store.commit("tooglePathanimationActive",!0)},resetTriggerSettingDatas:function(e){var t=window.scene.triggers.get(e.id);t.name=e.name,"range"==e.subType&&(t.radius=e.radius,t.center=e.center),"sensor"==e.subType&&(t.points=e.points,"element"==e.object.type?t.object=window.scene.findObject(e.object.id):t.object=window.scene.features.get(e.object.id)),t.triggerCommands=e.triggerCommands,t.exitCommands=e.exitCommands},resetAdvancedMaterialDatas:function(e){for(var t in!e.originalSign&&window.MaterialParams.original&&delete window.MaterialParams.original,window.MaterialParams)"maps"==t?(window.MaterialParams.maps=[],e.params.maps.length&&e.params.maps.forEach((function(e,t){for(var a in window.MaterialParams.maps[t]={},e)window.MaterialParams.maps[t][a]=e[a]}))):t in e.params?window.MaterialParams[t]=e.params[t]:delete window.MaterialParams[t];window.scene.mv.materialService.updateMaterial(window.MaterialParams),this.$store.commit("saveMaterialRawData","")},resetProjectorDatas:function(e){var t=window.scene.features.get(e.id);t.name=e.name,t.url=e.url,window.scene.postData(e.data,t.dataKey)},resetVideoDatas:function(e){var t=window.scene.features.get(e.id);t.url=e.url,t.priority=e.priority,t.always=e.always,t.follow=e.follow,t.followType=e.followType,window.scene.postData(e.data,t.dataKey)},resetSnowFeature:function(e){var t=window.scene.features.get(e.id);t.origin=e.origin,t.altitude=e.altitude,t.offset=e.offset,t.name=e.name,window.scene.postData(e.data,t.dataKey)}}}},"60d7":function(e,t,a){},6919:function(e,t,a){},"749c":function(e,t,a){"use strict";a("84e9")},8064:function(e,t,a){"use strict";a("354f")},"84e9":function(e,t,a){},"881b":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e._t("default"),a("div",{staticClass:"styleSet-list-div"},[a("div",{staticClass:"items-center margin-bottom-8 margin-top-10"},[a("span",{staticClass:"item-title"},[e._v(e._s(e.$t("formRelational.color.label")))]),a("el-color-picker",{staticStyle:{"margin-right":"12px"},attrs:{"popper-class":"colorPicker","show-alpha":!1,"color-format":"rgb",size:"mini"},on:{change:function(t){return e.inputChangePostData("color")}},model:{value:e.params.color,callback:function(t){e.$set(e.params,"color",t)},expression:"params.color"}}),a("span",{staticClass:"item-title"},[e._v(e._s(e.params.color))])],1),a("div",{staticClass:"items-center margin-bottom-8"},[a("span",{staticClass:"item-title"},[e._v(e._s(e.$t("formRelational.opacity.label")))]),a("el-slider",{staticStyle:{width:"100%"},attrs:{min:.1,max:1,step:.1},on:{change:function(t){return e.inputChangePostData("opacity")}},model:{value:e.params.opacity,callback:function(t){e.$set(e.params,"opacity",t)},expression:"params.opacity"}}),a("span",{staticClass:"slider-title"},[e._v(e._s(e.params.opacity))])],1),a("div",{staticClass:"items-center margin-bottom-8"},[a("span",{staticClass:"item-title"},[e._v(e._s(e.$t("formRelational.top.label")))]),a("el-input",{attrs:{size:"mini",type:"number",placeholder:e.$t("formRelational.top.placeholder")},on:{input:function(t){return e.inputChangePostData("height")}},scopedSlots:e._u([{key:"append",fn:function(){return[a("span",[e._v("m")])]},proxy:!0}]),model:{value:e.params.height,callback:function(t){e.$set(e.params,"height",t)},expression:"params.height"}})],1),a("div",{staticClass:"items-center margin-bottom-8"},[a("span",{staticClass:"item-title"},[e._v(e._s(e.$t("formRelational.speed.label")))]),a("el-slider",{staticStyle:{width:"100%"},attrs:{min:1,max:10,step:1},on:{change:function(t){return e.inputChangePostData("speed")}},model:{value:e.params.speed,callback:function(t){e.$set(e.params,"speed",t)},expression:"params.speed"}}),a("span",{staticClass:"slider-title"},[e._v(e._s(e.params.speed))])],1)])],2)},s=[],n=(a("e9c4"),{name:"TriggerRipplewall",props:["dragData"],data:function(){return{params:{color:"rgb(255, 50, 0)",opacity:1,height:0,speed:1}}},created:function(){this.dragData.isDragData&&this.$emit("saveTriggerExtendParams",this.params)},methods:{initParams:function(e,t){this.params=function(e){var t=e.color,a=e.opacity,i=e.height,s=e.speed;return{color:t,opacity:a,height:i,speed:s}}(e),t&&(this.params.speed=Math.ceil(33.33*e.speed))},inputChangePostData:function(e){var t=JSON.parse(JSON.stringify(this.params));if("color"!=e){var a=parseFloat(t[e]+"");t[e]=a}t.speed=Math.floor(t.speed/33.33*100)/100,this.$emit("saveTriggerExtendParams",t)}}}),r=n,o=(a("e8db"),a("2877")),l=Object(o["a"])(r,i,s,!1,null,"15a8fbfe",null);t["default"]=l.exports},"88e8":function(e,t,a){"use strict";a("6919")},"8a0c":function(e,t,a){"use strict";a("03c2")},"8c00":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e._t("default"),a("div",{staticClass:"styleSet-list-div"},[a("div",{staticClass:"items-center margin-bottom-8 margin-top-10"},[a("span",{staticClass:"item-title"},[e._v(e._s(e.$t("formRelational.color.label")))]),a("el-color-picker",{staticStyle:{"margin-right":"12px"},attrs:{"popper-class":"colorPicker","show-alpha":!1,"color-format":"rgb",size:"mini"},on:{change:function(t){return e.inputChangePostData("color")}},model:{value:e.params.color,callback:function(t){e.$set(e.params,"color",t)},expression:"params.color"}}),a("span",{staticClass:"item-title"},[e._v(e._s(e.params.color))])],1),a("div",{staticClass:"items-center margin-bottom-8"},[a("span",{staticClass:"item-title"},[e._v(e._s(e.$t("formRelational.width.label")))]),a("el-input",{attrs:{size:"mini",type:"number",placeholder:e.$t("formRelational.width.placeholder")},on:{input:function(t){return e.inputChangePostData("width")}},scopedSlots:e._u([{key:"append",fn:function(){return[a("span",[e._v("m")])]},proxy:!0}]),model:{value:e.params.width,callback:function(t){e.$set(e.params,"width",t)},expression:"params.width"}})],1),a("div",{staticClass:"items-center margin-bottom-8"},[a("span",{staticClass:"item-title"},[e._v(e._s(e.$t("formRelational.top.label")))]),a("el-input",{attrs:{size:"mini",type:"number",placeholder:e.$t("formRelational.top.placeholder")},on:{input:function(t){return e.inputChangePostData("height")}},scopedSlots:e._u([{key:"append",fn:function(){return[a("span",[e._v("m")])]},proxy:!0}]),model:{value:e.params.height,callback:function(t){e.$set(e.params,"height",t)},expression:"params.height"}})],1)])],2)},s=[],n={name:"TriggerSmoke",props:["dragData"],data:function(){return{params:{width:10,height:30,color:""}}},created:function(){this.dragData.isDragData&&this.$emit("saveTriggerExtendParams",this.params)},methods:{initParams:function(e){this.params=function(e){var t=e.width,a=e.height,i=e.color;return{width:t,height:a,color:i}}(e)},inputChangePostData:function(e){if("color"!=e){var t=parseFloat(this.params[e]+"");this.params[e]=t}this.$emit("saveTriggerExtendParams",this.params)}}},r=n,o=a("2877"),l=Object(o["a"])(r,i,s,!1,null,"745f612e",null);t["default"]=l.exports},"8c81":function(e,t,a){"use strict";var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"time-series"},[a("div",{staticClass:"operation-container"},[a("div",{staticClass:"margin-left-10"},[a("div",{staticClass:"el-input el-input--mini el-input-group el-input-group--prepend"},[a("div",{staticClass:"el-input-group__prepend"},[a("span",[e._v(e._s(e.$t("dialog.attribute.name")))])]),a("el-select",{attrs:{disabled:"play"==e.playObj.status,size:"mini"},on:{change:e.handleTimeSeriesPropertiesSelected},model:{value:e.checkedProperty,callback:function(t){e.checkedProperty=t},expression:"checkedProperty"}},e._l(e.timeseriesProperties,(function(e,t){return a("el-option",{key:e.name,attrs:{label:e.name,value:t}})})),1)],1)]),"pause"==e.playObj.status?a("div",{staticClass:"popup-item-center items-center margin-left-10",on:{click:function(t){return e.handleTimeSeriesPlay("play")}}},[a("CommonSVG",{attrs:{"icon-class":"ani_play_feature",size:12}}),a("span",{staticClass:"margin-left-10"},[e._v(" "+e._s(e.$t("featureSetting.timeSeries.label"))+" ")])],1):e._e(),"play"==e.playObj.status?a("div",{staticClass:"popup-item-center items-center margin-left-10",on:{click:function(t){return e.handleTimeSeriesPlay("pause")}}},[a("CommonSVG",{attrs:{"icon-class":"ani_pause_feature",size:12}}),a("span",{staticClass:"margin-left-10 color-FF6640"},[e._v(" "+e._s(e.$t("featureSetting.timeSeries.label1"))+" ")])],1):e._e(),a("div",{staticClass:"popup-item-center items-center margin-left-10",class:{"disable-btn":"play"==e.playObj.status},on:{click:function(t){return e.switchDialog()}}},[a("CommonSVG",{attrs:{"icon-class":"set_up_feature",size:12}}),a("span",{staticClass:"margin-left-10"},[e._v(" "+e._s(e.$t("menuIconName.setUp"))+" ")])],1),a("div",{staticClass:"operation-form-button margin-left-10"},[a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:e.$t("menuIconName.exit"),placement:"top"}},[a("span",{staticClass:"btn btn-default cursor-btn",on:{click:function(t){return e.exitTimeSeries()}}},[a("i",{staticClass:"el-icon-close"})])])],1)]),a("div",{staticClass:"time-line-container",class:{prohibit:"play"==e.playObj.status}},[a("div",{staticClass:"line-content timeSeriesLineContent"},[e._l(e.timeseriesNodeList,(function(t,i){return a("div",{key:t.label,staticClass:"node-item",class:[{"node-item-end":t.end},{active:e.selectedNode==i},{"time-show":e.playObj.currentNodeEq==i}],style:{left:t.left+"px"},attrs:{"data-time":t.label},on:{click:function(a){return e.handleTimeLineNodeClick(t,i)}}},[a("span",{style:{marginLeft:t.leftOffset?t.leftOffset+"px":0}},[e._v(e._s(t.label))])])})),a("div",{ref:"progressBarRef",staticClass:"progress-bar",style:{left:e.progressBarData.left+"px"}})],2)]),e.setupObj.visible?a("dialogComp",{attrs:{drag:!0,hideHeader:!1,needClose:"true",zIndex:"4",height:"240",position:"fixed",left:0,top:0,right:0,bottom:0,title:e.$t("menuIconName.setUp"),icon:"icon-details",width:390,isSource:!0,type:"detailInfo"},on:{close:function(t){return e.switchDialog()}},scopedSlots:e._u([{key:"center",fn:function(){return[a("div",{staticClass:"add-container time-series-setup"},[a("el-input",{attrs:{placeholder:e.$t("others.min"),size:"small"},model:{value:e.setupObj.min,callback:function(t){e.$set(e.setupObj,"min",t)},expression:"setupObj.min"}},[a("template",{slot:"prepend"},[a("div",{staticClass:"setup-label"},[e._v(e._s(e.$t("others.min")))])])],2),a("el-input",{attrs:{placeholder:e.$t("others.max"),size:"small"},model:{value:e.setupObj.max,callback:function(t){e.$set(e.setupObj,"max",t)},expression:"setupObj.max"}},[a("template",{slot:"prepend"},[a("div",{staticClass:"setup-label"},[e._v(e._s(e.$t("others.max")))])])],2),a("div",{staticClass:"el-input el-input--small el-input-group el-input-group--prepend"},[a("div",{staticClass:"el-input-group__prepend"},[a("span",[e._v(e._s(e.$t("featureSetting.timeSeries.label2")))])]),a("el-select",{attrs:{size:"small"},on:{change:e.handleTimeSeriesColorMapChange},model:{value:e.setupObj.selectedColor,callback:function(t){e.$set(e.setupObj,"selectedColor",t)},expression:"setupObj.selectedColor"}},e._l(e.setupObj.colorMaps,(function(e){return a("el-option",{key:e,attrs:{label:e,value:e}})})),1)],1),a("div",{staticClass:"bottom-btn margin-top-15"},[a("span",{staticClass:"cursor-btn cancel margin-right-8",on:{click:function(t){return e.cancelSetup()}}},[e._v(" "+e._s(e.$t("messageTips.cancel"))+" ")]),a("span",{staticClass:"cursor-btn confirm",on:{click:function(t){return e.saveSetup()}}},[e._v(" "+e._s(e.$t("menuIconName.confirm"))+" ")])])],1)]},proxy:!0}],null,!1,800320700)}):e._e()],1)},s=[],n=(a("d3b7"),a("3ca3"),a("ddb0"),a("4ec9"),a("159b"),a("caad"),a("2532"),a("ac1f"),a("1276"),a("a630"),a("d81d"),{name:"TimeSeries",components:{CommonSVG:function(){return a.e("chunk-51f90655").then(a.bind(null,"17d0"))}},props:["dragData"],data:function(){return{timeseries:["1998-01-01","1998-02-01","1998-03-01","1998-04-01","1998-07-01","1998-10-01","1999-01-01","1999-04-01","1999-07-01","1999-10-01","2000-01-01","2000-04-01","2000-07-01","2000-10-01","2001-01-01","2001-04-01","2001-07-01","2001-10-01","2002-01-01","2002-04-01","2002-07-01","2002-10-01","2003-01-01","2003-04-01","2003-07-01","2003-10-01","2004-01-01","2004-04-01","2004-07-01","2004-10-01","2005-01-01","2005-04-01","2005-07-01","2005-10-01","2006-01-01","2006-04-01","2006-07-01","2006-10-01","2007-01-01","2007-04-01","2007-07-01","2007-10-01","2007-12-31"],selectedNode:-1,timeseriesNodeList:[],timeseriesProperties:[],checkedProperty:0,setupObj:{visible:!1,min:0,max:1,colorMaps:["rainbow","cooltowarm","blackbody","grayscale"],selectedColor:"rainbow"},progressBarData:{width:0,left:0,time:1,startTime:0,startWidth:0,duration:1e3},playObj:{status:"pause",currentNodeEq:-1,startTime:0},timer:null,wRed:null,colored:new Map,twinkled:new Map}},created:function(){this.handleElementStatusSave()},mounted:function(){this.init()},methods:{handleElementStatusSave:function(){var e=this,t=this.dragData.id;if(window.scene.coloredObjects.size>0){var a=[];if(window.scene.coloredObjects.forEach((function(i){if(i.id.includes(t)){var s=i.color+"^"+i.opacity,n=[];e.colored.has(s)&&(n=e.colored.get(s)),n.push(i.id),a.push(i.id),e.colored.set(s,n)}})),a.length){var i=window.scene.createArrayObject(a);i.resetColor()}}if(window.scene.twinkleObjects.size>0){var s=[];if(window.scene.twinkleObjects.forEach((function(a){if(a.id.includes(t)){var i=a.twinkleColor+"^"+a.interval,n=[];e.twinkled.has(i)&&(n=e.twinkled.get(i)),n.push(a.id),s.push(a.id),e.twinkled.set(i,n)}})),s.length){var n=window.scene.createArrayObject(s);n.twinkle("",-1)}}},handleElementStatusReset:function(){if(this.colored.size){var e=[];this.colored.forEach((function(t,a){var i=a.split("^");if(parseFloat(i[1])>=0){var s=i[0],n=parseFloat(i[1]),r={objectIDs:t,opacity:n,color:s};e.push(r)}})),e.forEach((function(e){window.scene.execute("color",e)}))}if(this.twinkled.size){var t=[];this.colored.forEach((function(e,a){var i=a.split("^");if(parseFloat(i[1])>=0){var s=i[0],n=parseFloat(i[1]),r={objectIDs:e,interval:n,color:s};t.push(r)}})),t.forEach((function(e){window.scene.execute("twinkle",e)}))}},init:function(){var e=this,t=window.scene.findFeature(this.dragData.id);t&&(t.timeseries&&t.timeseriesProperties?this.setTimeseriesPropertiesData():t.load("timeseries").then((function(){e.setTimeseriesPropertiesData()})))},setTimeseriesPropertiesData:function(){var e=this,t=window.scene.findFeature(this.dragData.id);t.timeseries.size&&(this.timeseries=Array.from(t.timeseries.values()),this.setNodeStyle());var a=null;a=setInterval((function(){if(t.timeseriesProperties.length){clearInterval(a),e.timeseriesProperties=t.timeseriesProperties;var i=t.timeseriesProperties[0];e.setupObj.min=(parseFloat(i.min)-parseFloat(i.base))/parseFloat(i.divisor),e.setupObj.max=(parseFloat(i.max)-parseFloat(i.base))/parseFloat(i.divisor),window.scene.mv.lut.min=e.setupObj.min,window.scene.mv.lut.max=e.setupObj.max,window.scene.mv.lut.visible=!0}}),500)},handleTimeSeriesPropertiesSelected:function(e){var t=this.timeseriesProperties[e];this.setupObj.min=(parseFloat(t.min)-parseFloat(t.base))/parseFloat(t.divisor),this.setupObj.max=(parseFloat(t.max)-parseFloat(t.base))/parseFloat(t.divisor),window.scene.mv.lut.min=this.setupObj.min,window.scene.mv.lut.max=this.setupObj.max,window.scene.mv.lut.visible=!0},handleTimeSeriesPlay:function(e){if("play"==e){if(this.timeseries.length<=0||this.timeseriesProperties.length<=0)return this.$message.error(this.$t("featureSetting.timeSeries.message1")),!1;this.setupObj.visible&&this.cancelSetup(),window.scene.mv.lut.min=this.setupObj.min,window.scene.mv.lut.max=this.setupObj.max,window.scene.mv.lut.colorMap=this.setupObj.selectedColor,this.selectedNode<0&&(this.selectedNode=0);var t=this.selectedNode;this.playObj.currentNodeEq>=this.timeseries.length-1&&(this.progressBarData.width=0,this.playObj.currentNodeEq=t),this.playObj.currentNodeEq>=0&&this.playObj.currentNodeEq<this.timeseries.length-1&&(t=this.playObj.currentNodeEq),this.applyTimeSeriesStatus(t)}"pause"==e&&this.timer&&(clearTimeout(this.timer),this.timer=null),this.playObj.status=e},applyTimeSeriesStatus:function(e){var t=this,a=window.scene.findFeature(this.dragData.id);a.applyTimeSeriesStatus(this.timeseries[e],this.timeseriesProperties[this.checkedProperty]);var i=document.querySelectorAll(".node-item")[e].getBoundingClientRect().right;this.progressBarData.startWidth=this.progressBarData.width,this.progressBarData.width=i-this.progressBarData.left,this.progressBarData.startTime=performance.now(),requestAnimationFrame(this.progressBarWidthAnimate),this.playObj.currentNodeEq>=this.timeseries.length-1?this.handleTimeSeriesPlay("pause"):this.timer=setTimeout((function(){t.playObj.currentNodeEq=e+1,t.applyTimeSeriesStatus(t.playObj.currentNodeEq)}),this.progressBarData.duration)},progressBarWidthAnimate:function(e){var t=e-this.progressBarData.startTime,a=this.$refs.progressBarRef,i=this.progressBarData.duration/1.5;if(a)if(t<i){var s=t/i,n=this.progressBarData.startWidth+(this.progressBarData.width-this.progressBarData.startWidth)*s;a.style.width=n+"px",this.wRed=requestAnimationFrame(this.progressBarWidthAnimate)}else a.style.width=this.progressBarData.width+"px"},handleTimeSeriesColorMapChange:function(e){},handleTimeLineNodeClick:function(e,t){if("play"!=this.playObj.status){var a=e.left;if(this.playObj.currentNodeEq=-1,this.progressBarData.width=0,this.progressBarData.left=a,this.$refs.progressBarRef.style.width="0px",t==this.selectedNode)this.selectedNode=-1,this.clearTimeSeriesStatus();else{this.selectedNode=t;var i=window.scene.findFeature(this.dragData.id);i.applyTimeSeriesStatus(this.timeseries[t],this.timeseriesProperties[this.checkedProperty])}}},setNodeStyle:function(){var e=this,t=new Date(this.timeseries[0]),a=new Date(this.timeseries[this.timeseries.length-1]),i=document.querySelector(".timeSeriesLineContent").offsetWidth,s=t.getTime(),n=a.getTime(),r=n-s;this.timeseriesNodeList=this.timeseries.map((function(t,a){var n={label:t,left:0},o=new Date(t),l=o.getTime(),c=(l-s)/r*i;a==e.timeseries.length-1&&(c-=10,n.end=!0),n.left=c;var d=c+5-33;d<=0&&(n.leftOffset=33-(c+5));var u=c+5+33;return u>=i&&(n.leftOffset=i-u),n}))},clearTimeSeriesStatus:function(){var e=window.scene.findFeature(this.dragData.id);e.clearTimeSeriesStatus()},saveSetup:function(){window.scene.mv.lut.min=this.setupObj.min,window.scene.mv.lut.max=this.setupObj.max,window.scene.mv.lut.colorMap=this.setupObj.selectedColor,this.setupObj.visible=!1},cancelSetup:function(){this.setupObj.min=window.scene.mv.lut.min,this.setupObj.max=window.scene.mv.lut.max,this.setupObj.selectedColor=window.scene.mv.lut.colorMap,this.setupObj.visible=!1},switchDialog:function(){if(this.timeseries.length<=0||this.timeseriesProperties.length<=0)return this.$message.error(this.$t("featureSetting.timeSeries.message1")),!1;"play"!=this.playObj.status&&(this.setupObj.visible?this.cancelSetup():this.setupObj.visible=!0)},exitTimeSeries:function(){var e=this;"play"==this.playObj.status?this.$confirm(this.$t("featureSetting.timeSeries.message"),this.$t("messageTips.tipsTitle"),{confirmButtonText:this.$t("menuIconName.confirm"),cancelButtonText:this.$t("messageTips.cancel"),type:"warning"}).then((function(){e.$store.commit("toggleSettingActive","")})).catch((function(){})):this.$store.commit("toggleSettingActive","")}},beforeDestroy:function(){this.wRed&&cancelAnimationFrame(this.wRed),this.handleTimeSeriesPlay("pause"),this.clearTimeSeriesStatus(),window.scene.mv.lut.visible=!1,this.$store.state.dialog.activeDialog.includes("attribute")&&this.$store.commit("toggleActiveDialog","attribute"),this.handleElementStatusReset(),this.$store.commit("toogleElementMenu",""),this.$store.commit("selectElementData",{})}}),r=n,o=(a("8a0c"),a("749c"),a("2877")),l=Object(o["a"])(r,i,s,!1,null,"2c0e2520",null);t["a"]=l.exports},9285:function(e,t,a){},9312:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e._t("default"),a("div",{staticClass:"styleSet-list-div"},[a("div",{staticClass:"items-center margin-bottom-8 margin-top-10"},[a("span",{staticClass:"item-title"},[e._v(e._s(e.$t("formRelational.color.label")))]),a("el-color-picker",{staticStyle:{"margin-right":"12px"},attrs:{"popper-class":"colorPicker","show-alpha":!1,"color-format":"rgb",size:"mini"},on:{change:function(t){return e.inputChangePostData("color")}},model:{value:e.params.color,callback:function(t){e.$set(e.params,"color",t)},expression:"params.color"}}),a("span",{staticClass:"item-title"},[e._v(e._s(e.params.color))])],1),a("div",{staticClass:"items-center margin-bottom-8"},[a("span",{staticClass:"item-title"},[e._v(e._s(e.$t("formRelational.radius.label")))]),a("el-input",{attrs:{size:"mini",type:"number",placeholder:e.$t("formRelational.radius.placeholder")},on:{input:function(t){return e.inputChangePostData("radius")}},scopedSlots:e._u([{key:"append",fn:function(){return[a("span",[e._v("m")])]},proxy:!0}]),model:{value:e.params.radius,callback:function(t){e.$set(e.params,"radius",t)},expression:"params.radius"}})],1),a("div",{staticClass:"items-center margin-bottom-8"},[a("span",{staticClass:"item-title"},[e._v(e._s(e.$t("formRelational.speed.label")))]),a("el-slider",{staticStyle:{width:"100%"},attrs:{min:1,max:10,step:1},on:{change:function(t){return e.inputChangePostData("speed")}},model:{value:e.params.speed,callback:function(t){e.$set(e.params,"speed",t)},expression:"params.speed"}}),a("span",{staticClass:"slider-title"},[e._v(e._s(e.params.speed))])],1)])],2)},s=[],n={name:"TriggerRing",props:["dragData"],data:function(){return{params:{color:"rgb(255, 50, 0)",radius:50,speed:.2}}},created:function(){this.dragData.isDragData&&this.$emit("saveTriggerExtendParams",this.params)},methods:{initParams:function(e){this.params=function(e){var t=e.color,a=e.radius,i=e.speed;return{color:t,radius:a,speed:i}}(e)},inputChangePostData:function(e){if("color"!=e){var t=parseFloat(this.params[e]+"");this.params[e]=t}this.$emit("saveTriggerExtendParams",this.params)}}},r=n,o=(a("49a3"),a("2877")),l=Object(o["a"])(r,i,s,!1,null,"80cf8f5e",null);t["default"]=l.exports},"95e9":function(e,t,a){},"964f":function(e,t,a){"use strict";var i=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"flex"},[i("div",{staticClass:"popup-box h100 relative ml16"},[i("span",{staticClass:"data-title"},[e._v(" "+e._s(e.$t("featureSetting.vothing.label"))+" ")]),i("el-cascader",{staticStyle:{width:"100%"},attrs:{options:e.iotDataSelect.dataOptions,props:{value:"id",label:"name",children:"children",checkStrictly:!0},clearable:"","popper-class":"data-area-cascader","change-on-select":""},on:{change:e.iotDataChange},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.node,s=t.data;return[i("span",[e._v(e._s(s.name))]),a.isLeaf?e._e():i("span",[e._v(" ("+e._s(s.children.length)+") ")])]}}]),model:{value:e.iotDataSelect.selectedData,callback:function(t){e.$set(e.iotDataSelect,"selectedData",t)},expression:"iotDataSelect.selectedData"}})],1),i("div",{staticClass:"popup-box h100 relative ml16 pr"},[i("span",{staticClass:"item-title"},[e._v(" "+e._s(e.$t("featureSetting.vothing.label1"))+" ")]),i("div",{staticClass:"popup-item-center items-center",on:{click:e.togglePanel}},[i("span",{staticClass:"margin-left-5"},[e._v(" "+e._s(e.$t("featureSetting.style.heatMap.label5"))+" ")]),i("CommonSVG",{attrs:{"icon-class":"add_feature",size:12}})],1),e.visibleData?i("div",{staticClass:"reation-container"},[i("div",{staticClass:"title"},[i("span",[e._v(" "+e._s(e.$t("featureSetting.style.heatMap.label5"))+" ")]),i("div",[i("i",{staticClass:"el-icon-close cursor-btn",on:{click:e.togglePanel}})])]),i("div",{staticClass:"content"},[i("div",{staticClass:"datas-input events-input"},[e.colorSet.includes(e.currentElement.type)?i("el-row",{attrs:{gutter:20}},[i("el-col",{attrs:{span:24}},[i("div",{staticClass:"style-color-picker"},[i("span",{staticClass:"label",staticStyle:{"margin-right":"0px","min-width":"72px"}},[e._v(e._s(e.$t("formRelational.color.label")))]),i("el-color-picker",{staticStyle:{"margin-left":"12px"},attrs:{"popper-class":"colorPicker","show-alpha":!1,"color-format":"rgb",size:"small"},model:{value:e.dataObj[e.currentElement.type].value,callback:function(t){e.$set(e.dataObj[e.currentElement.type],"value",t)},expression:"dataObj[currentElement.type].value"}}),i("span",{staticClass:"value"},[e._v(e._s(e.dataObj[e.currentElement.type].value))])],1)]),"heatmap"!==e.currentElement.type?i("el-col",{staticStyle:{padding:"0"},attrs:{span:2}},[i("el-tooltip",{attrs:{placement:"top",disabled:"color"===e.notShowRelation||"color"!==e.iotDataContent.type}},[i("div",{attrs:{slot:"content"},slot:"content"},[e._v(" "+e._s(e.iotDataContent.entity+": "+e.iotDataContent.property)+" ")]),i("div",{staticClass:"relationBK"},[i("img",{staticClass:"relationImg",class:e.iotDataSelect.selectedData===[]||"0"===e.iotDataSelect.selectedData[0]?"disabled":"",attrs:{src:a("c862"),alt:e.$t("featureSetting.vothing.label3"),title:e.iotDataSelect.selectedData===[]||"0"===e.iotDataSelect.selectedData[0]?e.$t("featureSetting.vothing.label2"):""},on:{mouseover:function(t){return e.mouseover("color")},mouseout:function(t){return e.mouseout("color")},click:function(t){return e.getRelations("color")}}})])])],1):e._e()],1):e._e(),e.opacitySet.includes(e.currentElement.type)?i("el-row",{attrs:{gutter:20}},[i("el-col",{attrs:{span:24}},[i("div",{staticClass:"style-color-picker"},[i("span",{staticClass:"label",staticStyle:{"margin-right":"0px","min-width":"72px"}},[e._v(e._s(e.$t("formRelational.opacity.label")))]),i("el-slider",{staticClass:"slider opacitySlider",attrs:{step:.1,"input-size":"small",min:.1,max:1,"show-tooltip":!0},model:{value:e.dataObj[e.currentElement.type].opacity,callback:function(t){e.$set(e.dataObj[e.currentElement.type],"opacity",t)},expression:"dataObj[currentElement.type].opacity"}})],1)]),"heatmap"!==e.currentElement.type?i("el-col",{staticStyle:{padding:"0"},attrs:{span:2}},[i("el-tooltip",{attrs:{placement:"top",disabled:"opacity"===e.notShowRelation||"opacity"!==e.iotDataContent.type}},[i("div",{attrs:{slot:"content"},slot:"content"},[e._v(" "+e._s(e.iotDataContent.entity+": "+e.iotDataContent.property)+" ")]),i("div",{staticClass:"relationBK"},[i("img",{staticClass:"relationImg",class:e.iotDataSelect.selectedData===[]||"0"===e.iotDataSelect.selectedData[0]?"disabled":"",attrs:{src:a("c862"),alt:e.$t("featureSetting.vothing.label3"),title:e.iotDataSelect.selectedData===[]||"0"===e.iotDataSelect.selectedData[0]?e.$t("featureSetting.vothing.label2"):""},on:{mouseover:function(t){return e.mouseover("opacity")},mouseout:function(t){return e.mouseout("opacity")},click:function(t){return e.getRelations("opacity")}}})])])],1):e._e()],1):e._e(),e.radiusSet.includes(e.currentElement.type)?i("el-row",{attrs:{gutter:20}},[i("el-col",{attrs:{span:24}},[i("div",{staticClass:"style-color-picker"},[i("span",{staticClass:"label",staticStyle:{"margin-right":"0px","min-width":"72px"}},[e._v(e._s(e.$t("formRelational.radius.label")))]),i("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:e.dataObj[e.currentElement.type].radius,expression:"dataObj[currentElement.type].radius"}],attrs:{size:"small",type:"number",title:"",placeholder:e.$t("formRelational.radius.placeholder")},scopedSlots:e._u([{key:"append",fn:function(){return[i("span",[e._v("m")])]},proxy:!0}],null,!1,1549734309),model:{value:e.dataObj[e.currentElement.type].radius,callback:function(t){e.$set(e.dataObj[e.currentElement.type],"radius",t)},expression:"dataObj[currentElement.type].radius"}})],1)]),"heatmap"!==e.currentElement.type?i("el-col",{staticStyle:{padding:"0"},attrs:{span:2}},[i("el-tooltip",{attrs:{placement:"top",disabled:"radius"===e.notShowRelation||"radius"!==e.iotDataContent.type}},[i("div",{attrs:{slot:"content"},slot:"content"},[e._v(" "+e._s(e.iotDataContent.entity+": "+e.iotDataContent.property)+" ")]),i("div",{staticClass:"relationBK"},[i("img",{staticClass:"relationImg",class:e.iotDataSelect.selectedData===[]||"0"===e.iotDataSelect.selectedData[0]?"disabled":"",attrs:{src:a("c862"),alt:e.$t("featureSetting.vothing.label3"),title:e.iotDataSelect.selectedData===[]||"0"===e.iotDataSelect.selectedData[0]?e.$t("featureSetting.vothing.label2"):""},on:{mouseover:function(t){return e.mouseover("radius")},mouseout:function(t){return e.mouseout("radius")},click:function(t){return e.getRelations("radius")}}})])])],1):e._e()],1):e._e(),e.speedSet.includes(e.currentElement.type)?i("el-row",{attrs:{gutter:20}},[i("el-col",{attrs:{span:24}},[i("div",{staticClass:"style-color-picker"},[i("span",{staticClass:"label",staticStyle:{"margin-right":"0px","min-width":"72px"}},[e._v(e._s(e.$t("formRelational.speed.label")))]),i("el-slider",{staticClass:"slider speedslider",attrs:{step:1,"input-size":"small",min:1,max:10,"show-tooltip":!0},on:{change:e.sliderChange},model:{value:e.dataObj[e.currentElement.type].speed,callback:function(t){e.$set(e.dataObj[e.currentElement.type],"speed",t)},expression:"dataObj[currentElement.type].speed"}})],1)]),"heatmap"!==e.currentElement.type?i("el-col",{staticStyle:{padding:"0"},attrs:{span:2}},[i("el-tooltip",{attrs:{placement:"top",disabled:"speed"===e.notShowRelation||"speed"!==e.iotDataContent.type}},[i("div",{attrs:{slot:"content"},slot:"content"},[e._v(" "+e._s(e.iotDataContent.entity+": "+e.iotDataContent.property)+" ")]),i("div",{staticClass:"relationBK"},[i("img",{staticClass:"relationImg",class:e.iotDataSelect.selectedData===[]||"0"===e.iotDataSelect.selectedData[0]?"disabled":"",attrs:{src:a("c862"),alt:e.$t("featureSetting.vothing.label3"),title:e.iotDataSelect.selectedData===[]||"0"===e.iotDataSelect.selectedData[0]?e.$t("featureSetting.vothing.label2"):""},on:{mouseover:function(t){return e.mouseover("speed")},mouseout:function(t){return e.mouseout("speed")},click:function(t){return e.getRelations("speed")}}})])])],1):e._e()],1):e._e(),e.pointsSet.includes(e.currentElement.type)?i("div",[e._l(e.dataObj.pointsArray,(function(t,s){return[i("el-row",{key:s,attrs:{gutter:20}},[i("el-col",{attrs:{span:6}},[i("el-input",{attrs:{size:"small",type:"number",title:"",placeholder:"X"},scopedSlots:e._u([{key:"prepend",fn:function(){return[i("span",[e._v("X")])]},proxy:!0}],null,!0),model:{value:t.x,callback:function(a){e.$set(t,"x",a)},expression:"data.x"}})],1),i("el-col",{attrs:{span:6}},[i("el-input",{attrs:{size:"small",type:"number",title:"",placeholder:"Y"},scopedSlots:e._u([{key:"prepend",fn:function(){return[i("span",[e._v("Y")])]},proxy:!0}],null,!0),model:{value:t.y,callback:function(a){e.$set(t,"y",a)},expression:"data.y"}})],1),i("el-col",{attrs:{span:8}},[i("el-input",{attrs:{size:"small",type:"number",title:"",placeholder:"value"},scopedSlots:e._u([{key:"prepend",fn:function(){return[i("span",[e._v("Value")])]},proxy:!0}],null,!0),model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"data.value"}})],1),"heatmap"===e.currentElement.type?i("el-col",{staticStyle:{padding:"0","margin-right":"4px"},attrs:{span:4}},[i("el-tooltip",{attrs:{placement:"top",disabled:e.notShowRelation===s||"heatmap"!==e.iotDataContent.type}},[i("div",{attrs:{slot:"content"},slot:"content"},[e._v(" "+e._s(e.iotDataContent.entity+": "+e.iotDataContent.property)+" ")]),i("div",{staticClass:"relationBK"},[i("img",{staticClass:"relationImg",class:e.iotDataSelect.selectedData===[]||"0"===e.iotDataSelect.selectedData[0]?"disabled":"",attrs:{src:a("c862"),title:e.iotDataSelect.selectedData===[]||"0"===e.iotDataSelect.selectedData[0]?e.$t("featureSetting.vothing.label2"):"",alt:e.$t("featureSetting.vothing.label3")},on:{mouseover:function(t){return e.mouseover("heatmap",s)},mouseout:function(t){return e.mouseout("heatmap",s)},click:function(t){return e.getRelations("heatmap",s)}}})])])],1):e._e()],1)]}))],2):e._e()],1),i("div",{staticClass:"btn-box"},[i("span",{staticClass:"btn",on:{click:e.togglePanel}},[e._v(" "+e._s(e.$t("menuIconName.confirm"))+" ")])])])]):e._e()])])},s=[],n=(a("d3b7"),a("159b"),a("b0c0"),a("d81d"),a("e9c4"),a("009f")),r=a("ed08"),o={name:"vothingData",props:["currentElement"],data:function(){return{setType:"",iotDataContent:{},relationArr:[],relationSet:["shield","ring","radar","ripplewall","heatmap","annotation"],colorSet:["shield","ring","radar","ripplewall"],opacitySet:["shield","radar","ripplewall"],radiusSet:["shield","radar","ring"],speedSet:["shield","ring","radar","ripplewall"],pointsSet:["heatmap"],sceneManageTreeDatas:n["a"],notShowRelation:"",iotDataSelect:{dataOptions:[],selectedData:["0"],selectedContent:{}},dataObj:{title:"",dataList:[],sceneDataSign:"",annotation:{title:"",dataList:[],dataTypeSelect:""},shield:{value:"rgb(255, 50, 0)",opacity:1,radius:"50",speed:Math.ceil(6.666)},heatmap:{value:"rgb(255, 50, 0)",opacity:1,scaleZ:5,wireframe:!1,radius:13,gradient:{a:"rgb(0,0,255)",b:"rgb(0,255,0)",c:"rgb(255,255,0)",d:"rgb(255,0,0)"},checkedIndex:-1},ring:{value:"rgb(255, 50, 0)",radius:"50",speed:Math.ceil(6.666)},ripplewall:{height:1,value:"rgb(255, 50, 0)",radius:"50",speed:Math.ceil(.015*33.33),opacity:1},radar:{value:"rgb(255, 50, 0)",radius:"50",speed:1,opacity:1,width:90},unitsArray:[{id:1,position:{x:0,y:0}},{id:2,position:{x:0,y:0}},{id:3,position:{x:0,y:0}}],pointsArray:[],polyline:{value:"rgb(255, 50, 0)",opacity:1,lineWidth:2,flow:!0,speed:.03}},visibleData:!1}},components:{},computed:{isVothing:function(){return this.$store.state.menuList.isVothing}},mounted:function(){this.getiotData(),this.initEditDatas(),this.currentElement.isDragData?this.setType="add":this.setType="edit"},methods:{togglePanel:function(){this.visibleData=!this.visibleData},mouseover:function(e,t){var a=null;this.relationArr.length>0?(this.relationArr.forEach((function(i){"heatmap"===e?t===i.relationData.relationKey.index&&(a=i):e===i.relationData.type&&(a=i)})),a&&a.chosedEntity&&a.chosedProperty?(this.iotDataContent={entity:a.chosedEntity.name,property:a.chosedProperty.name,type:e},this.notShowRelation=""):this.notShowRelation="heatmap"===e?t:e):this.notShowRelation="heatmap"===e?t:e},mouseout:function(e){this.notShowRelation=""},initEditDatas:function(){var e=this;if(this.isVothing){var t,a=this.$store.state.scene.sceneList||[],i=this.currentElement.dataKey;for(var s in a)"element"===a[s].type&&a[s].elementid===i&&(t=a[s].data);t&&t.widgets&&(this.iotDataSelect.selectedData=t.widgets.entityData?[t.widgets.entityData.id]:["0"],this.iotDataSelect.selectedContent=t.widgets.entityData?t.widgets.entityData:{},this.relationArr=t.widgets.relationArr,this.$emit("setRelationData",this.relationArr))}var n=this.currentElement.type;switch(n){case"annotation":var r=window.scene.features.get(this.currentElement.id);this.dataObj.annotation.dataTypeSelect=r.data.content.dataTypeSelect,this.dataObj.annotation.title=r.data.content.title,this.dataObj.annotation.dataList=r.data.content.dataList;break;case"ring":var o=window.scene.features.get(this.currentElement.id);this.dataObj[n].value=o.data.color,this.dataObj[n].radius=o.data.radius,this.dataObj[n].speed=Math.ceil(33.33*o.data.speed);break;case"shield":var l=window.scene.features.get(this.currentElement.id);this.dataObj[n].value=l.data.color,this.dataObj[n].opacity=l.data.opacity,this.dataObj[n].radius=l.data.radius,this.dataObj[n].speed=Math.ceil(33.33*l.data.speed);break;case"heatmap":var c=window.scene.features.get(this.currentElement.id);this.dataObj[n].value=c.data.color,this.dataObj[n].opacity=c.data.opacity,this.dataObj[n].gradient.a=c.data.gradient["0.25"],this.dataObj[n].gradient.b=c.data.gradient["0.55"],this.dataObj[n].gradient.c=c.data.gradient["0.85"],this.dataObj[n].gradient.d=c.data.gradient["1"],this.dataObj[n].radius=c.data.size,this.dataObj[n].scaleZ=c.data.scaleZ,this.dataObj[n].wireframe=c.data.wireframe,this.dataObj.pointsArray=[],c.data.points&&c.data.points.length>0?c.data.points.map((function(t){e.dataObj.pointsArray.push({value:t.value,x:t.position[0],y:t.position[1]})})):this.dataObj.pointsArray=[];break;case"radar":var d=window.scene.features.get(this.currentElement.id);this.dataObj[n].value=d.data.color,this.dataObj[n].opacity=d.data.opacity,this.dataObj[n].radius=d.data.radius,this.dataObj[n].speed=d.data.speed,this.dataObj[n].width=180*parseFloat(d.data.width+"")/Math.PI;break;case"ripplewall":var u=window.scene.features.get(this.currentElement.id);this.dataObj[n].value=u.data.color,this.dataObj[n].opacity=u.data.opacity,this.dataObj[n].speed=Math.ceil(33.33*u.data.speed),this.dataObj[n].height=parseFloat(u.data.height+""),this.dataObj.unitsArray=JSON.parse(JSON.stringify(u.data.units));break}},getiotData:function(){var e=this.$store.state.scene.iotentityList||[];e.length>0?(this.iotDataSelect.dataOptions=Object(r["b"])(e),this.iotDataSelect.dataOptions.length>0&&"0"!==this.iotDataSelect.dataOptions[0].id&&this.iotDataSelect.dataOptions.unshift({name:this.$t("others.nothing"),id:"0"})):this.iotDataSelect.dataOptions=[{name:this.$t("others.nothing"),id:"0"}]},iotDataChange:function(e){if("0"!==e){var t=Object(r["c"])(this.iotDataSelect.dataOptions,e[e.length-1]);if(this.iotDataSelect.selectedContent=t,"heatmap"===this.currentElement.type){this.dataObj.pointsArray=[];for(var a=0;a<t.data.entity.length;a++)this.dataObj.pointsArray.push({value:0,x:0,y:0})}}else this.iotDataSelect.selectedContent={};this.relationArr=[],this.$store.commit("getRelationData",{isShow:!1})},getRelations:function(e,t){if(""!==this.iotDataSelect.selectedData&&"0"!==this.iotDataSelect.selectedData[0]){this.$store.commit("getRelationData",{isShow:!1});var a={};a="heatmap"===e?{type:e,value:this.dataObj.pointsArray[t],index:t}:{type:e,value:"color"===e?this.dataObj[this.currentElement.type]["value"]:this.dataObj[this.currentElement.type][e]};var i=null;this.relationArr.length>0&&this.relationArr.forEach((function(a){"heatmap"===e?t===a.relationData.relationKey.index&&(i=a):e===a.relationData.type&&(i=a)})),this.$store.commit("getRelationData",{isShow:!0,setType:this.setType,data:{selectedData:this.iotDataSelect.selectedData,selectedContent:this.iotDataSelect.selectedContent,dragid:this.currentElement.id,type:e,relationKey:a,formerData:i}})}}}},l=o,c=(a("88e8"),a("5d3f"),a("2877")),d=Object(c["a"])(l,i,s,!1,null,"7fa66fea",null);t["a"]=d.exports},a38c:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e._t("default"),a("div",{staticClass:"styleSet-list-div"},[a("div",{staticClass:"items-center margin-bottom-8 margin-top-10"},[a("span",{staticClass:"item-title"},[e._v(e._s(e.$t("formRelational.color.label")))]),a("el-color-picker",{staticStyle:{"margin-right":"12px"},attrs:{"popper-class":"colorPicker","show-alpha":!1,"color-format":"rgb",size:"mini"},on:{change:function(t){return e.inputChangePostData("color")}},model:{value:e.params.color,callback:function(t){e.$set(e.params,"color",t)},expression:"params.color"}}),a("span",{staticClass:"item-title"},[e._v(e._s(e.params.color))])],1),a("div",{staticClass:"items-center margin-bottom-8"},[a("span",{staticClass:"item-title"},[e._v(e._s(e.$t("formRelational.opacity.label")))]),a("el-slider",{staticStyle:{width:"100%"},attrs:{min:.1,max:1,step:.1},on:{change:function(t){return e.inputChangePostData("opacity")}},model:{value:e.params.opacity,callback:function(t){e.$set(e.params,"opacity",t)},expression:"params.opacity"}}),a("span",{staticClass:"slider-title"},[e._v(e._s(e.params.opacity))])],1),a("div",{staticClass:"items-center margin-bottom-8"},[a("span",{staticClass:"item-title"},[e._v(e._s(e.$t("formRelational.radius.label")))]),a("el-input",{attrs:{size:"mini",type:"number",placeholder:e.$t("formRelational.radius.placeholder")},on:{input:function(t){return e.inputChangePostData("radius")}},scopedSlots:e._u([{key:"append",fn:function(){return[a("span",[e._v("m")])]},proxy:!0}]),model:{value:e.params.radius,callback:function(t){e.$set(e.params,"radius",t)},expression:"params.radius"}})],1),a("div",{staticClass:"items-center margin-bottom-8"},[a("span",{staticClass:"item-title"},[e._v(e._s(e.$t("formRelational.speed.label")))]),a("el-slider",{staticStyle:{width:"100%"},attrs:{min:1,max:10,step:1},on:{change:function(t){return e.inputChangePostData("speed")}},model:{value:e.params.speed,callback:function(t){e.$set(e.params,"speed",t)},expression:"params.speed"}}),a("span",{staticClass:"slider-title"},[e._v(e._s(e.params.speed))])],1)])],2)},s=[],n=(a("e9c4"),{name:"TriggerShield",props:["dragData"],data:function(){return{params:{color:"rgb(255, 50, 0)",opacity:1,height:0,radius:5,speed:1}}},created:function(){this.dragData.isDragData&&this.$emit("saveTriggerExtendParams",this.params)},methods:{initParams:function(e,t){this.params=function(e){var t=e.color,a=e.opacity,i=e.height,s=e.radius,n=e.speed;return{color:t,opacity:a,height:i,radius:s,speed:n}}(e),t&&(this.params.speed=Math.ceil(33.33*e.speed))},inputChangePostData:function(e){var t=JSON.parse(JSON.stringify(this.params));if("color"!=e){var a=parseFloat(t[e]+"");t[e]=a}t.speed=Math.floor(t.speed/33.33*100)/100,this.$emit("saveTriggerExtendParams",t)}}}),r=n,o=(a("8064"),a("2877")),l=Object(o["a"])(r,i,s,!1,null,"cf40c7b2",null);t["default"]=l.exports},a78c:function(e,t,a){"use strict";a("60d7")},a9c5:function(e,t,a){},aade:function(e,t,a){"use strict";a("b0c0"),a("7db0"),a("d3b7"),a("d81d"),a("b893");t["a"]={methods:{beforeValidate:function(){var e=this.dragData.type;if("objectSetting"==this.dragData.interlock)return!0;if(window.scene.features.has(this.$refs[e].elementDatas.id)&&this.dragData.isDragData)return this.inputError=!0,this.$message.error(this.$t("formRelational.featureID.message")),!1;if("annotation"===e&&""===this.$refs[e].elementDatas.list[3].htmlCode)return this.inputError=!0,this.$message.error(this.$t("featureSetting.data.anchorPoint.message1")),!1;if(this.styleFormDatas.name!==this.$t("featureDatas._3dBuilding.name")&&""==this.styleFormDatas.name)return this.inputError=!0,this.$message.error(this.$t("featureSetting.style.placeholder")),!1;if(this.$refs[e].elementDatas.longitude<=-180||this.$refs[e].elementDatas.longitude>=180)return this.inputError=!0,this.$refs[e].elementDatas.validate.longitude=!1,this.$message.error(this.$t("formRelational.longitude.message")),!1;if(this.$refs[e].elementDatas.latitude<=-90||this.$refs[e].elementDatas.latitude>=90)return this.inputError=!0,this.$refs[e].elementDatas.validate.latitude=!1,this.$message.error(this.$t("formRelational.latitude.message")),!1;var t=this.$refs[e].elementDatas.list.find((function(e){return e.validate}));if(void 0!=t&&""==t.value&&t.optionState)return this.inputError=!0,this.$message.error(this.$t("others.inputName",{name:t.title})),!1;for(var a in this.$refs[e].elementDatas.longitude=this.$refs[e].elementDatas.longitude||0,this.$refs[e].elementDatas.latitude=this.$refs[e].elementDatas.latitude||0,this.$refs[e].elementDatas.altitude=this.$refs[e].elementDatas.altitude||0,this.$refs[e].elementDatas.rotation=this.$refs[e].elementDatas.rotation||0,void 0!=this.$refs[e].offset&&(this.$refs[e].offset=this.$refs[e].offset.map((function(e){return e||0}))),this.inputError=!1,this.$refs[e].validate)!0;return!0},checkStyleLinkValidate:function(e,t){var a=this,i=this.dragData.type;if(""!=t){this.styleFormDatas.datas[i].list[e].validateState=0;var s=new XMLHttpRequest;s.open("GET",t,!0),s.send();s.onload=function(){var t=null;200==s.status?("success",t=1):("error",t=2),a.styleFormDatas.datas[i].list[e].validateState=t},s.onerror=function(){a.styleFormDatas.datas[i].list[e].validateState=2}}else this.styleFormDatas.datas[i].list[e].validateState=null}}}},af0b:function(e,t,a){"use strict";var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"bottom-content flex relative"},[a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("div",{staticClass:"popup-item-center items-center",class:{disable:e.currentBehaviorTab==e.eventsOptions.length-1},on:{click:function(t){return e.addObjectEvent()}}},[a("span",{staticClass:"margin-left-5"},[e._v(" "+e._s(e.$t("formRelational.trigger.label5"))+" ")]),a("CommonSVG",{attrs:{"icon-class":"add_feature",size:12}})],1)])]),a("div",{staticClass:"after-lineX"}),e.behaviorForm.length>0?a("div",{staticClass:"anchor-list-div h100"},e._l(e.behaviorForm,(function(t,i){return a("div",{key:"events-"+i},[a("div",{staticClass:"items-center"},[a("div",{ref:"behaviorForm"+i,refInFor:!0,staticClass:"popup-item-center items-center",on:{click:function(t){return e.togglePanel(i)}}},[a("span",{staticClass:"margin-left-5 margin-right-5"},[e._v(e._s(t.name))]),a("CommonSVG",{attrs:{"icon-class":"notation_feature",size:12}})],1),a("el-tooltip",{attrs:{effect:"dark",content:e.$t("menuIconName.delete"),placement:"top"}},[a("i",{staticClass:"el-icon el-icon-delete cursor-btn",on:{click:function(t){return e.deleteObjectEvent(i)}}})])],1)])})),0):e._e(),e.behaviorForm.length>0&&e.popupState?a("div",{staticClass:"regional-coordinates-container",style:{left:e.popupLeft+"px",maxHeight:e.popupMaxHeight+"px"}},[a("div",{staticClass:"title"},[a("span",[e._v(e._s(e.$t("featureSetting.triggerBehavior.label")))]),a("i",{staticClass:"el-icon-close cursor-btn",on:{click:function(t){return e.togglePanel(e.currentBehaviorTab)}}})]),"custom"!=e.behaviorForm[e.currentBehaviorTab].behaviorList[0]?a("div",{staticClass:"annotation-data-type-select margin-bottom-8"},[a("div",{staticClass:"el-input el-input--small el-input-group el-input-group--prepend"},[a("div",{staticClass:"el-input-group__prepend"},[a("span",[e._v(e._s(e.$t("featureSetting.triggerConditions.name")))])]),a("el-select",{attrs:{size:"mini",placeholder:e.$t("featureSetting.triggerConditions.placeholder")},on:{change:e.selectObjectEvent},model:{value:e.behaviorForm[e.currentBehaviorTab].eventsValue,callback:function(t){e.$set(e.behaviorForm[e.currentBehaviorTab],"eventsValue",t)},expression:"behaviorForm[currentBehaviorTab].eventsValue"}},e._l(e.eventsOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)]):e._e(),e._l(e.behaviorForm[e.currentBehaviorTab].behaviorListState,(function(t,i){return a("div",{key:"behavior-"+i,staticClass:"tab-div tab-events"},[a("div",{staticClass:"tab-top-bar datas-top-bar"},[a("div",{staticClass:"left-title",on:{click:function(t){return e.switchListState(i)}}},[a("span",[e._v(e._s(e.$t("featureSetting.triggerBehavior.label1")+(i+1)))]),a("i",t?{staticClass:"el-icon-caret-bottom"}:{staticClass:"el-icon-caret-top"})]),a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:e.$t("featureSetting.triggerBehavior.tooltip"),placement:"top"}},[a("div",{staticClass:"right-menu cursor-btn",on:{click:function(t){return e.removeCurrentBehaviorType(i)}}},[a("i",{staticClass:"el-icon el-icon-delete"})])])],1),a("el-collapse-transition",[a("div",{directives:[{name:"show",rawName:"v-show",value:t,expression:"listState"}],staticClass:"events-input"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{staticClass:"margin-bottom-5",attrs:{span:24}},[a("div",{staticClass:"el-input el-input--mini el-input-group el-input-group--prepend"},[a("div",{staticClass:"el-input-group__prepend"},[a("span",[e._v(" "+e._s(e.$t("featureSetting.triggerBehavior.label1"))+" ")])]),a("el-select",{attrs:{size:"mini",placeholder:e.$t("featureSetting.triggerBehavior.placeholder")},model:{value:e.behaviorForm[e.currentBehaviorTab].behaviorList[i],callback:function(t){e.$set(e.behaviorForm[e.currentBehaviorTab].behaviorList,i,t)},expression:"behaviorForm[currentBehaviorTab].behaviorList[stateEq]"}},e._l(e.behaviorOption,(function(t,s){return a("el-option-group",{key:"behavior"+s,attrs:{label:t.label}},e._l(t.options,(function(t){return a("el-option",{key:t.type,attrs:{disabled:e.behaviorForm[e.currentBehaviorTab].behaviorList.includes(t.type)&&"feature_postData"!=t.type,label:t.title,value:t.type},nativeOn:{click:function(a){return e.getBehaviorValue(t.type,s,i)}}})})),1)})),1)],1)])],1),void 0!=e.behaviorForm[e.currentBehaviorTab].objectsList[i]&&e.behaviorForm[e.currentBehaviorTab].objectsList[i]>=0?a("el-row",{staticClass:"margin-bottom-5",attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("div",{staticClass:"el-input el-input--mini el-input-group el-input-group--prepend"},[a("div",{staticClass:"el-input-group__prepend"},[a("span",[e._v(e._s(e.$t("formRelational.trigger.label")))])]),"0"==e.behaviorForm[e.currentBehaviorTab].objectsList[i]?[a("div",{staticClass:"space-between el-input__inner cursor-btn",on:{click:function(t){return e.getElementsTags(i)}}},[e._v(" "+e._s(e.behaviorForm[e.currentBehaviorTab].selectedElementIndex==i?e.$t("formRelational.trigger.label1"):e.$t("formRelational.trigger.label2"))+" "),a("CommonSVG",{attrs:{"icon-class":"mouse_left",size:14}})],1)]:e._e(),"1"==e.behaviorForm[e.currentBehaviorTab].objectsList[i]?[a("div",{staticClass:"space-between el-input__inner cursor-btn",on:{click:function(t){return e.getFeaturesTags(i)}}},[e._v(" "+e._s(e.behaviorForm[e.currentBehaviorTab].selectedFeatureIndex==i?e.$t("formRelational.trigger.label3"):e.$t("formRelational.trigger.label4"))+" "),a("CommonSVG",{attrs:{"icon-class":"mouse_left",size:14}})],1)]:e._e(),"2"==e.behaviorForm[e.currentBehaviorTab].objectsList[i]?[a("el-select",{attrs:{size:"mini",placeholder:e.$t("formRelational.trigger.placeholder")},on:{change:function(t){return e.selectAnimateOption(t,i)}}},e._l(e.animationOption,(function(t){return a("el-option",{key:t.value,attrs:{disabled:e.behaviorForm[e.currentBehaviorTab].selectedAnimateDisable.includes(t.value),label:t.label,value:t.value}})})),1)]:e._e()],2)])],1):e._e(),"0"==e.behaviorForm[e.currentBehaviorTab].objectsList[i]&&e.behaviorForm[e.currentBehaviorTab].selectedElements[i].length>0?a("el-row",{staticClass:"margin-bottom-5",staticStyle:{"flex-wrap":"wrap"},attrs:{gutter:20}},[e.behaviorForm[e.currentBehaviorTab].selectedElements[i].length<6?a("el-col",{attrs:{span:24}},e._l(e.behaviorForm[e.currentBehaviorTab].selectedElements[i],(function(t){return a("el-tag",{key:t.id,staticClass:"margin-right-5 margin-bottom-5 cursor-btn",attrs:{color:"rgba(255,255,255,0.1)",size:"small",effect:"dark",type:"info","disable-transitions":!0,closable:""},on:{click:function(a){return e.handleTagClick(t,i,"element")},close:function(a){return e.handleTagClose(t,i,"element")}}},[e._v(" "+e._s(t.name)+" ")])})),1):a("el-col",{attrs:{span:24}},[a("div",{staticClass:"el-input el-input--mini el-input-group"},[a("el-popover",{attrs:{placement:"top",width:"400",trigger:"manual"},model:{value:e.behaviorForm[e.currentBehaviorTab].selectedElementsState,callback:function(t){e.$set(e.behaviorForm[e.currentBehaviorTab],"selectedElementsState",t)},expression:"behaviorForm[currentBehaviorTab].selectedElementsState"}},[a("div",[a("div",{staticClass:"space-between el-icon"},[a("span",[e._v(" ")]),a("i",{staticClass:"el-icon-close cursor-btn",on:{click:function(t){t.stopPropagation(),e.behaviorForm[e.currentBehaviorTab].selectedElementsState=!e.behaviorForm[e.currentBehaviorTab].selectedElementsState}}})])]),e._l(e.behaviorForm[e.currentBehaviorTab].selectedElements[i],(function(t){return a("el-tag",{key:t.id,staticClass:"margin-right-5 margin-bottom-5 cursor-btn",attrs:{color:"rgba(255,255,255,0.1)",size:"small",effect:"dark",type:"info","disable-transitions":!0,closable:""},on:{click:function(a){return e.handleTagClick(t,i,"element")},close:function(a){return e.handleTagClose(t,i,"element")}}},[e._v(" "+e._s(t.name)+" ")])})),a("div",{staticClass:"el-input__inner cursor-btn text-center",attrs:{slot:"reference"},on:{click:function(t){e.behaviorForm[e.currentBehaviorTab].selectedElementsState=!e.behaviorForm[e.currentBehaviorTab].selectedElementsState}},slot:"reference"},[e._v(" "+e._s(e.$t("formRelational.trigger.message",{num:e.behaviorForm[e.currentBehaviorTab].selectedElements[i].length}))+" ")])],2)],1)])],1):e._e(),"1"==e.behaviorForm[e.currentBehaviorTab].objectsList[i]&&e.behaviorForm[e.currentBehaviorTab].selectedFeatures[i].length>0?[a("el-row",{staticClass:"margin-bottom-5",staticStyle:{"flex-wrap":"wrap"},attrs:{gutter:20}},[e.behaviorForm[e.currentBehaviorTab].selectedFeatures[i].length<6?a("el-col",{attrs:{span:24}},e._l(e.behaviorForm[e.currentBehaviorTab].selectedFeatures[i],(function(t){return a("el-tag",{key:t.id,staticClass:"margin-right-5 margin-bottom-5 cursor-btn",attrs:{color:"rgba(255,255,255,0.1)",size:"small",effect:"dark",type:"info","disable-transitions":!0,closable:""},on:{click:function(a){return e.handleTagClick(t,i,"feature")},close:function(a){return e.handleTagClose(t,i,"feature")}}},[e._v(" "+e._s(t.name)+" ")])})),1):a("el-col",{attrs:{span:24}},[a("div",{staticClass:"el-input el-input--mini el-input-group"},[a("el-popover",{attrs:{placement:"top",width:"400",trigger:"manual"},model:{value:e.behaviorForm[e.currentBehaviorTab].selectedFeaturesState,callback:function(t){e.$set(e.behaviorForm[e.currentBehaviorTab],"selectedFeaturesState",t)},expression:"behaviorForm[currentBehaviorTab].selectedFeaturesState"}},[a("div",[a("div",{staticClass:"space-between el-icon"},[a("span",[e._v(" ")]),a("i",{staticClass:"el-icon-close cursor-btn",on:{click:function(t){t.stopPropagation(),e.behaviorForm[e.currentBehaviorTab].selectedFeaturesState=!e.behaviorForm[e.currentBehaviorTab].selectedFeaturesState}}})])]),e._l(e.behaviorForm[e.currentBehaviorTab].selectedFeatures[i],(function(t){return a("el-tag",{key:t.id,staticClass:"margin-right-5 margin-bottom-5 cursor-btn",attrs:{color:"rgba(255,255,255,0.1)",size:"small",effect:"dark",type:"info","disable-transitions":!0,closable:""},on:{click:function(a){return e.handleTagClick(t,i,"feature")},close:function(a){return e.handleTagClose(t,i,"feature")}}},[e._v(" "+e._s(t.name)+" ")])})),a("div",{staticClass:"el-input__inner cursor-btn text-center",attrs:{slot:"reference"},on:{click:function(t){e.behaviorForm[e.currentBehaviorTab].selectedFeaturesState=!e.behaviorForm[e.currentBehaviorTab].selectedFeaturesState}},slot:"reference"},[e._v(" "+e._s(e.$t("formRelational.trigger.message1",{num:e.behaviorForm[e.currentBehaviorTab].selectedFeatures[i].length}))+" ")])],2)],1)])],1),"feature_postData"===e.behaviorForm[e.currentBehaviorTab].behaviorList[i]?a(e.behaviorForm[e.currentBehaviorTab].selectedFeatures[i][0].componentType,{ref:"featureSetDiv"+i,refInFor:!0,tag:"div",staticClass:"feature-set-div",class:"featureSetDiv"+i,attrs:{dragData:e.dragData},on:{saveTriggerExtendParams:function(t){return e.saveTriggerExtendParams(t,i)}}}):e._e()]:e._e(),"2"==e.behaviorForm[e.currentBehaviorTab].objectsList[i]&&e.behaviorForm[e.currentBehaviorTab].selectedAnimates[i].length>0?a("el-row",{staticClass:"margin-bottom-5",staticStyle:{"flex-wrap":"wrap"},attrs:{gutter:20}},[e.behaviorForm[e.currentBehaviorTab].selectedAnimates[i].length<6?a("el-col",{attrs:{span:24}},e._l(e.behaviorForm[e.currentBehaviorTab].selectedAnimates[i],(function(t){return a("el-tag",{key:t.id,staticClass:"margin-right-5 margin-bottom-5 cursor-btn",attrs:{color:"rgba(255,255,255,0.1)",size:"small",effect:"dark",type:"info","disable-transitions":!0,closable:""},on:{click:function(a){return e.handleTagClick(t,i,"animate")},close:function(a){return e.handleTagClose(t,i,"animate")}}},[e._v(" "+e._s(t.name)+" ")])})),1):a("el-col",{attrs:{span:24}},[a("div",{staticClass:"el-input el-input--mini el-input-group"},[a("el-popover",{attrs:{placement:"top",width:"400",trigger:"manual"},model:{value:e.behaviorForm[e.currentBehaviorTab].selectedAnimatesState,callback:function(t){e.$set(e.behaviorForm[e.currentBehaviorTab],"selectedAnimatesState",t)},expression:"behaviorForm[currentBehaviorTab].selectedAnimatesState"}},[a("div",[a("div",{staticClass:"space-between el-icon"},[a("span",[e._v(" ")]),a("i",{staticClass:"el-icon-close cursor-btn",on:{click:function(t){t.stopPropagation(),e.behaviorForm[e.currentBehaviorTab].selectedAnimatesState=!e.behaviorForm[e.currentBehaviorTab].selectedAnimatesState}}})])]),e._l(e.behaviorForm[e.currentBehaviorTab].selectedAnimates[i],(function(t){return a("el-tag",{key:t.id,staticClass:"margin-right-5 margin-bottom-5 cursor-btn",attrs:{color:"rgba(255,255,255,0.1)",size:"small",effect:"dark",type:"info","disable-transitions":!0,closable:""},on:{click:function(a){return e.handleTagClick(t,i,"animate")},close:function(a){return e.handleTagClose(t,i,"animate")}}},[e._v(" "+e._s(t.name)+" ")])})),a("div",{staticClass:"el-input__inner cursor-btn text-center",attrs:{slot:"reference"},on:{click:function(t){e.behaviorForm[e.currentBehaviorTab].selectedAnimatesState=!e.behaviorForm[e.currentBehaviorTab].selectedAnimatesState}},slot:"reference"},[e._v(" "+e._s(e.$t("formRelational.trigger.message2",{num:e.behaviorForm[e.currentBehaviorTab].selectedAnimates[i].length}))+" ")])],2)],1)])],1):e._e()],2)])],1)})),a("div",{staticClass:"add-behavior-btn cursor-btn",on:{click:e.addCurrentBehaviorType}},[a("i",{staticClass:"el-icon-plus"}),e._v(" "+e._s(e.$t("formRelational.trigger.label5"))+" ")])],2):e._e()])},s=[],n=a("b85c"),r=a("5530"),o=(a("d3b7"),a("159b"),a("ddb0"),a("b0c0"),a("3ca3"),a("6062"),a("e9c4"),a("7db0"),a("c740"),a("a434"),a("caad"),a("d81d"),a("2532"),a("1e3a")),l={};o.keys().forEach((function(e){var t=o(e).default;l[t.name]=t}));var c={name:"SensorTriggerBehavior",components:Object(r["a"])(Object(r["a"])({},l),{},{CommonSVG:function(){return a.e("chunk-51f90655").then(a.bind(null,"17d0"))}}),props:["dragData","tabActive"],data:function(){return{contentType:"",currentBehaviorTab:0,currentSelectedEvent:{index:new Set,value:""},eventsOptions:[{value:"trigger",label:this.$t("featureSetting.triggerBehavior.label2"),disabled:!1},{value:"exit",label:this.$t("featureSetting.triggerBehavior.label3"),disabled:!1}],behaviorOption:[],behaviorOptionLen:0,animationOption:[],behaviorForm:[],popupLeft:0,popupMaxHeight:100,popupState:!1}},created:function(){var e=this,t=this.$store.state.anchorEvents.eventSetUniversalApi;this.behaviorOption=JSON.parse(JSON.stringify(t)),this.behaviorOption[1].options.push({title:this.$t("formRelational.feature.event.postData"),type:"feature_postData"});var a=this.$store.getters.allAnimations;a.length>0&&a.forEach((function(t){var a=[],i="";for(var s in t)a.push(t[s].animationName),i=t[s].groupName;e.animationOption.push({value:a[0],label:i,resultIds:a,disabled:!1})})),void 0==this.dragData.isDragData&&this.initEdit()},mounted:function(){this.setPopupMaxHeight(),window.addEventListener("resize",this.setPopupMaxHeight)},watch:{currentBehaviorTab:function(e,t){this.behaviorForm[t].getElementsTagsState&&this.elementPickFinishedOff(t),this.behaviorForm[t].getFeaturesTagsState&&this.featurePickFinishedOff(t)},tabActive:function(e){"sceneEventSet"!=e&&this.behaviorForm.length>0&&(this.behaviorForm[this.currentBehaviorTab].getElementsTagsState&&this.elementPickFinishedOff(this.currentBehaviorTab),this.behaviorForm[this.currentBehaviorTab].getFeaturesTagsState&&this.featurePickFinishedOff(this.currentBehaviorTab))}},methods:{saveTriggerExtendParams:function(e,t){this.behaviorForm[this.currentBehaviorTab].selectedFeatures[t][0].postdata=e},getBehaviorForm:function(){return this.behaviorForm},selectAnimateOption:function(e,t){var a=this.animationOption.find((function(t){return t.value==e}));this.behaviorForm[this.currentBehaviorTab].selectedAnimates[t].push({id:a.value,name:a.label,resultIds:a.resultIds}),this.behaviorForm[this.currentBehaviorTab].selectedAnimateDisable.push(e)},handleTagClick:function(e,t,a){switch(a){case"element":var i=window.scene.findObject(e.id);i.selected=!0;break;case"feature":window.scene.fit2Feature(e.id);break}window.scene.render()},handleTagClose:function(e,t,a){var i="";switch(a){case"element":i="selectedElements";var s=window.scene.findObject(e.id);s.selected=!1;break;case"feature":i="selectedFeatures",window.scene.features.get(e.id).clearAABB();break;case"animate":i="selectedAnimates";var n=this.behaviorForm[this.currentBehaviorTab].selectedAnimateDisable.findIndex((function(t){return t==e.id}));this.behaviorForm[this.currentBehaviorTab].selectedAnimateDisable.splice(n,1);break}window.scene.render();var r=this.behaviorForm[this.currentBehaviorTab][i][t].findIndex((function(t){return t.id===e.id}));this.behaviorForm[this.currentBehaviorTab][i][t].splice(r,1)},getFeaturesTags:function(e){this.behaviorForm[this.currentBehaviorTab].getFeaturesTagsState&&(window.scene.mv.events.featurePicked.off("default",this.getSelectedFeatures),this.behaviorForm[this.currentBehaviorTab].getFeaturesTagsState=!1,window.scene.config.highlight=!0),e!=this.behaviorForm[this.currentBehaviorTab].selectedFeatureIndex?(this.behaviorForm[this.currentBehaviorTab].selectedFeatureIndex=e,window.scene.mv.events.featurePicked.on("default",this.getSelectedFeatures),this.behaviorForm[this.currentBehaviorTab].getFeaturesTagsState=!0,window.scene.config.highlight=!1,this.behaviorForm[this.currentBehaviorTab].getElementsTagsState&&this.elementPickFinishedOff(this.currentBehaviorTab)):this.behaviorForm[this.currentBehaviorTab].selectedFeatureIndex=-1},getSelectedFeatures:function(e){var t=this;if("polygon"!=e.type||"trigger-sensor-polygon"!=e.id){var a=this.behaviorForm[this.currentBehaviorTab].selectedFeatureIndex;if("feature_postData"==this.behaviorForm[this.currentBehaviorTab].behaviorList[a]){var i=["flame","radar","ring","ripplewall","shield","smoke","waterfall"];if(!i.includes(e.type))return window.scene.clearSelection(),window.scene.render(),!1;this.$set(this.behaviorForm[this.currentBehaviorTab].selectedFeatures,a,[]),this.$nextTick((function(){t.behaviorForm[t.currentBehaviorTab].selectedFeatures[a].push({name:e.name,id:e.id,type:e.type,componentType:"trigger-"+e.type,dataKey:e.dataKey}),t.$nextTick((function(){var i=window.scene.features.get(e.id).data;t.$refs["featureSetDiv"+a][0].initParams(i,"convert"),t.behaviorForm[t.currentBehaviorTab].selectedFeatures[a][0].postdata=i}))}))}else{var s=this.behaviorForm[this.currentBehaviorTab].selectedFeatures[a].findIndex((function(t){return t.id===e.id}));s<0&&this.behaviorForm[this.currentBehaviorTab].selectedFeatures[a].push({name:e.name,id:e.id,dataKey:e.dataKey})}}else window.scene.clearSelection()},getElementsTags:function(e){var t,a=!1,i=Object(n["a"])(window.scene.features);try{for(i.s();!(t=i.n()).done;){var s=t.value;if("model"==s[1].type){a=!0;break}}}catch(r){i.e(r)}finally{i.f()}if(!a)return this.$message.error(this.$t("messageTips.addOneModel")),!1;this.behaviorForm[this.currentBehaviorTab].getElementsTagsState&&(window.scene.mv.events.pickFinished.off("default",this.getSelectedObjects),this.behaviorForm[this.currentBehaviorTab].getElementsTagsState=!1),e!=this.behaviorForm[this.currentBehaviorTab].selectedElementIndex?(this.behaviorForm[this.currentBehaviorTab].selectedElementIndex=e,window.scene.mv.events.pickFinished.on("default",this.getSelectedObjects),this.behaviorForm[this.currentBehaviorTab].getElementsTagsState=!0,this.behaviorForm[this.currentBehaviorTab].getFeaturesTagsState&&this.featurePickFinishedOff(this.currentBehaviorTab)):this.behaviorForm[this.currentBehaviorTab].selectedElementIndex=-1},getSelectedObjects:function(e){var t=window.scene.findObject(e),a=this.behaviorForm[this.currentBehaviorTab].selectedElementIndex,i=this.behaviorForm[this.currentBehaviorTab].selectedElements[a].findIndex((function(t){return t.id===e}));i<0&&this.behaviorForm[this.currentBehaviorTab].selectedElements[a].push({name:t.name,id:t.id})},initEdit:function(){this.dragData.triggerCommands.length>0&&this.initEditTriggerCommands("triggerCommands"),this.dragData.exitCommands.length>0&&this.initEditTriggerCommands("exitCommands"),this.currentBehaviorTab=this.behaviorForm.length-1},initEditTriggerCommands:function(e){var t=[{fit:["element_zoom",this.$t("formRelational.element.event.zoom")],isolate:["element_isolate",this.$t("formRelational.element.event.isolate")],hide:["element_hide",this.$t("formRelational.element.event.hide")],show:["element_show",this.$t("formRelational.element.event.show")]},{fit:["feature_zoom",this.$t("formRelational.feature.event.zoom")],hide:["feature_hide",this.$t("formRelational.feature.event.hide")],show:["feature_show",this.$t("formRelational.feature.event.show")],post:["feature_postData",this.$t("formRelational.feature.event.postData")]},{play:["animate_play",this.$t("formRelational.animate.play")],stop:["animate_stop",this.$t("formRelational.animate.stop")]}],a={name:"",eventsValue:"",behaviorList:[""],objectsList:[],animateList:[],behaviorListState:[!0],selectedAnimates:[[]],selectedAnimateDisable:[],selectedAnimatesState:!1,getElementsTagsState:!1,selectedElementIndex:-1,selectedElements:[[]],selectedElementsState:!1,getFeaturesTagsState:!1,selectedFeatureIndex:-1,selectedFeatures:[[]],selectedFeaturesState:!1};"triggerCommands"==e?(a.name=this.$t("featureSetting.triggerBehavior.label2"),a.eventsValue="trigger"):(a.name=this.$t("featureSetting.triggerBehavior.label3"),a.eventsValue="exit"),this.dragData[e].forEach((function(e){var i=e.args.additional;a.objectsList[i.index]=i.objType,a.behaviorList[i.index]=t[i.objType][e.command][0],0==i.objType&&(a.selectedElements[i.index]=i.selectedObj),1==i.objType&&(a.selectedFeatures[i.index]=i.selectedObj),2==i.objType&&(a.selectedAnimates[i.index]=i.selectedObj,a.selectedAnimateDisable=i.selectedObj.map((function(e){return e.id}))),a.behaviorListState[i.index]=!0}));var i=this.dragData[e][0].args.additional.li;this.$set(this.behaviorForm,i,a)},switchListState:function(e){this.$set(this.behaviorForm[this.currentBehaviorTab].behaviorListState,e,!this.behaviorForm[this.currentBehaviorTab].behaviorListState[e])},addObjectEvent:function(){var e=this;window.scene.clearSelection(),window.scene.render(),this.currentBehaviorTab>=this.eventsOptions.length-1||(this.popupState=!0,this.behaviorForm.length>0?this.currentBehaviorTab=this.behaviorForm.length:this.currentBehaviorTab=0,this.behaviorForm.push({name:"",eventsValue:"",behaviorList:[""],objectsList:[],animateList:[],selectedAnimates:[[]],selectedAnimateDisable:[],selectedAnimatesState:!1,behaviorListState:[!0],getElementsTagsState:!1,selectedElementIndex:-1,selectedElements:[[]],selectedElementsState:!1,getFeaturesTagsState:!1,selectedFeatureIndex:-1,selectedFeatures:[[]],selectedFeaturesState:!1}),this.$nextTick((function(){e.popupLeft=e.$refs["behaviorForm"+e.currentBehaviorTab][0].offsetLeft})))},deleteObjectEvent:function(e){this.behaviorForm.splice(e,1),this.currentSelectedEvent.index.delete(e),this.$set(this.eventsOptions[e],"disabled",!1)},selectObjectEvent:function(e,t){var a=this.eventsOptions.findIndex((function(t){return t.value==e}));this.currentSelectedEvent.index.add(a),this.behaviorForm[this.currentBehaviorTab].name=this.eventsOptions[a].label},getEventsValue:function(e){},getBehaviorValue:function(e,t,a){var i=this;if(this.$set(this.behaviorForm[this.currentBehaviorTab].behaviorList,a,e),void 0!=this.behaviorForm[this.currentBehaviorTab].objectsList[a]&&this.behaviorForm[this.currentBehaviorTab].objectsList[a]!=t)switch(parseInt(this.behaviorForm[this.currentBehaviorTab].objectsList[a])){case 0:this.behaviorForm[this.currentBehaviorTab].selectedElements[a]=[],this.behaviorForm[this.currentBehaviorTab].getElementsTagsState&&this.elementPickFinishedOff(this.currentBehaviorTab);break;case 1:this.behaviorForm[this.currentBehaviorTab].selectedFeatures[a]=[],this.behaviorForm[this.currentBehaviorTab].getFeaturesTagsState&&this.featurePickFinishedOff(this.currentBehaviorTab);break;case 2:this.behaviorForm[this.currentBehaviorTab].selectedAnimates[a].length>0&&this.behaviorForm[this.currentBehaviorTab].selectedAnimates[a].forEach((function(e){var t=i.behaviorForm[i.currentBehaviorTab].selectedAnimateDisable.findIndex((function(t){return t==e.id}));i.behaviorForm[i.currentBehaviorTab].selectedAnimateDisable.splice(t,1)})),this.behaviorForm[this.currentBehaviorTab].selectedAnimates[a]=[];break}this.$set(this.behaviorForm[this.currentBehaviorTab].objectsList,a,t)},removeCurrentBehaviorType:function(e){var t=this;this.behaviorForm[this.currentBehaviorTab].behaviorList.splice(e,1),this.behaviorForm[this.currentBehaviorTab].objectsList.splice(e,1),this.behaviorForm[this.currentBehaviorTab].behaviorListState.splice(e,1),this.behaviorForm[this.currentBehaviorTab].selectedElements.splice(e,1),this.behaviorForm[this.currentBehaviorTab].selectedFeatures.splice(e,1),this.behaviorForm[this.currentBehaviorTab].animateList.splice(e,1),this.behaviorForm[this.currentBehaviorTab].selectedAnimates[e].forEach((function(e){var a=t.behaviorForm[t.currentBehaviorTab].selectedAnimateDisable.findIndex((function(t){return t==e.id}));-1!=a&&t.behaviorForm[t.currentBehaviorTab].selectedAnimateDisable.splice(a,1)})),this.behaviorForm[this.currentBehaviorTab].selectedAnimates.splice(e,1)},addCurrentBehaviorType:function(){this.behaviorForm[this.currentBehaviorTab].behaviorList.push(""),this.behaviorForm[this.currentBehaviorTab].behaviorListState.push(!0),this.behaviorForm[this.currentBehaviorTab].selectedElements.push([]),this.behaviorForm[this.currentBehaviorTab].selectedFeatures.push([]),this.behaviorForm[this.currentBehaviorTab].selectedAnimates.push([])},elementPickFinishedOff:function(e){window.scene.mv.events.pickFinished.off("default",this.getSelectedObjects),this.behaviorForm[e].selectedElementIndex=-1,this.behaviorForm[e].getElementsTagsState=!1},featurePickFinishedOff:function(e){window.scene.mv.events.featurePicked.off("default",this.getSelectedFeatures),this.behaviorForm[e].selectedFeatureIndex=-1,this.behaviorForm[e].getFeaturesTagsState=!1,window.scene.config.highlight=!0},togglePanel:function(e){var t=this;if(this.currentBehaviorTab==e&&this.popupState?(this.popupState=!1,window.scene.clearSelection(),window.scene.render()):this.popupState=!0,this.behaviorForm[this.currentBehaviorTab].getElementsTagsState&&this.elementPickFinishedOff(this.currentBehaviorTab),this.behaviorForm[this.currentBehaviorTab].getFeaturesTagsState&&this.featurePickFinishedOff(this.currentBehaviorTab),this.behaviorForm[e].selectedElementsState&&(this.behaviorForm[e].selectedElementsState=!1),"custom"==this.contentType?this.popupLeft=this.$refs["behaviorForm"+e].offsetLeft:this.popupLeft=this.$refs["behaviorForm"+e][0].offsetLeft,this.currentBehaviorTab=e,this.popupState&&this.behaviorForm[e].behaviorList.includes("feature_postData")){var a=[];this.behaviorForm[e].behaviorList.forEach((function(e,t){"feature_postData"==e&&a.push(t)})),this.$nextTick((function(){a.forEach((function(a){var i=t.behaviorForm[e].selectedFeatures[a][0].postdata;t.$refs["featureSetDiv"+a][0].initParams(i,"convert")}))}))}},beforeValidate:function(){if(0==this.behaviorForm.length)return this.$message.error(this.$t("featureSetting.triggerBehavior.message")),!1;var e,t=!0,a=Object(n["a"])(this.behaviorForm);try{for(a.s();!(e=a.n()).done;){var i=e.value;if(""==i.eventsValue){t=!1;break}if(0==i.objectsList.length){t=!1;break}for(var s=!0,r=0;r<i.objectsList.length;r++){if(0==i.objectsList[r]&&0==i.selectedElements[r].length){s=!1;break}if(1==i.objectsList[r]&&0==i.selectedFeatures[r].length){s=!1;break}if(2==i.objectsList[r]&&0==i.selectedAnimates[r].length){s=!1;break}if(3==i.objectsList[r]&&0==i.selectedFeatures[r].length){s=!1;break}}if(!s){t=!1;break}}}catch(o){a.e(o)}finally{a.f()}return!!t||(this.$message.error(this.$t("featureSetting.triggerBehavior.message1")),!1)},setPopupMaxHeight:function(){var e=document.body.clientHeight,t=document.querySelector(".topToolBarContainer").clientHeight,a=document.querySelector(".settings-container").clientHeight;this.popupMaxHeight=e-t-a-20}},beforeDestroy:function(){window.removeEventListener("resize",this.setPopupMaxHeight),void 0!=this.behaviorForm[this.currentBehaviorTab]&&this.behaviorForm[this.currentBehaviorTab].getElementsTagsState&&this.elementPickFinishedOff(this.currentBehaviorTab),void 0!=this.behaviorForm[this.currentBehaviorTab]&&this.behaviorForm[this.currentBehaviorTab].getFeaturesTagsState&&this.featurePickFinishedOff(this.currentBehaviorTab)}},d=c,u=(a("550c"),a("2877")),m=Object(u["a"])(d,i,s,!1,null,"197a5027",null);t["a"]=m.exports},b4b8:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e._t("default"),a("div",{staticClass:"styleSet-list-div"},[a("div",{staticClass:"items-center margin-bottom-8 margin-top-10"},[a("span",{staticClass:"item-title"},[e._v(e._s(e.$t("formRelational.color.label")))]),a("el-color-picker",{staticStyle:{"margin-right":"12px"},attrs:{"popper-class":"colorPicker","show-alpha":!1,"color-format":"rgb",size:"mini"},on:{change:function(t){return e.inputChangePostData("color")}},model:{value:e.params.color,callback:function(t){e.$set(e.params,"color",t)},expression:"params.color"}}),a("span",{staticClass:"item-title"},[e._v(e._s(e.params.color))])],1),a("div",{staticClass:"items-center margin-bottom-8"},[a("span",{staticClass:"item-title"},[e._v(e._s(e.$t("formRelational.opacity.label")))]),a("el-slider",{staticStyle:{width:"100%"},attrs:{min:.1,max:1,step:.1},on:{change:function(t){return e.inputChangePostData("opacity")}},model:{value:e.params.opacity,callback:function(t){e.$set(e.params,"opacity",t)},expression:"params.opacity"}}),a("span",{staticClass:"slider-title"},[e._v(e._s(e.params.opacity))])],1),a("div",{staticClass:"items-center margin-bottom-8"},[a("span",{staticClass:"item-title"},[e._v(e._s(e.$t("formRelational.radius.label")))]),a("el-input",{attrs:{size:"mini",type:"number",placeholder:e.$t("formRelational.radius.placeholder")},on:{input:function(t){return e.inputChangePostData("radius")}},scopedSlots:e._u([{key:"append",fn:function(){return[a("span",[e._v("m")])]},proxy:!0}]),model:{value:e.params.radius,callback:function(t){e.$set(e.params,"radius",t)},expression:"params.radius"}})],1),a("div",{staticClass:"items-center margin-bottom-8"},[a("span",{staticClass:"item-title"},[e._v(e._s(e.$t("formRelational.speed.label")))]),a("el-slider",{staticStyle:{width:"100%"},attrs:{min:1,max:10,step:1},on:{change:function(t){return e.inputChangePostData("speed")}},model:{value:e.params.speed,callback:function(t){e.$set(e.params,"speed",t)},expression:"params.speed"}}),a("span",{staticClass:"slider-title"},[e._v(e._s(e.params.speed))])],1),a("div",{staticClass:"items-center margin-bottom-8"},[a("span",{staticClass:"item-title"},[e._v(" "+e._s(e.$t("featureSetting.style.radar.label"))+" ")]),a("el-input",{attrs:{size:"mini",type:"number",placeholder:e.$t("featureSetting.style.radar.placeholder")},on:{input:function(t){return e.inputChangePostData("width")}},model:{value:e.params.width,callback:function(t){e.$set(e.params,"width",t)},expression:"params.width"}})],1)])],2)},s=[],n=(a("e9c4"),{name:"TriggerRadar",props:["dragData"],data:function(){return{params:{color:"rgb(255, 50, 0)",opacity:1,radius:5,speed:1,width:90}}},created:function(){this.dragData.isDragData&&this.$emit("saveTriggerExtendParams",this.params)},methods:{initParams:function(e,t){this.params=function(e){var t=e.color,a=e.opacity,i=e.radius,s=e.speed,n=e.width;return{color:t,opacity:a,radius:i,speed:s,width:n}}(e),t&&(this.params.width=Math.round(e.width/(Math.PI/180)))},inputChangePostData:function(e){var t=JSON.parse(JSON.stringify(this.params));if("color"!=e){var a=parseFloat(t[e]+"");t[e]=a}t.width=t.width/180*Math.PI,this.$emit("saveTriggerExtendParams",t)}}}),r=n,o=(a("cccc"),a("2877")),l=Object(o["a"])(r,i,s,!1,null,"366a0ebc",null);t["default"]=l.exports},b8b9:function(e,t,a){"use strict";a("d647")},ca4e:function(e,t,a){},cccc:function(e,t,a){"use strict";a("95e9")},d647:function(e,t,a){},e780:function(e,t,a){"use strict";var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"bottom-content flex"},[a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[e._v(" "+e._s(e.$t("featureSetting.triggerConditions.label"))+" ")]),a("div",{staticClass:"items-center"},[a("CommonSVG",{attrs:{"icon-class":e.dragData.icon,size:18}}),a("span",{staticClass:"margin-left-5"},[e._v(e._s(e.typeText))])],1)]),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[e._v(" "+e._s(e.$t("featureSetting.triggerConditions.label1"))+" ")]),a("el-input",{class:{"is-error":e.inputError},attrs:{size:"mini",placeholder:e.$t("others.name")},model:{value:e.params.name,callback:function(t){e.$set(e.params,"name",t)},expression:"params.name"}})],1)]),a("div",{staticClass:"after-lineX"}),"range"==e.params.triggerType?[a("div",{staticClass:"styleSet-list-div h100"},[a("el-row",{staticClass:"items-center",attrs:{gutter:e.itemGutter}},[a("el-col",{staticClass:"space-between-center",attrs:{span:12}},[a("span",{staticClass:"item-title"},[e._v(" "+e._s(e.$t("featureSetting.triggerConditions.label2"))+" ")]),a("el-tooltip",{attrs:{effect:"dark",content:e.$t("formRelational.basePoint.placeholder"),placement:"top"}},[a("span",{staticClass:"outline-none cursor-btn",on:{click:function(t){return e.clickCheckedCoordinate()}}},[a("CommonSVG",{attrs:{color:"var(--theme)",size:"18","icon-class":"select_coord"}})],1)])],1),a("el-col",{attrs:{span:12}},[a("el-input",{attrs:{type:"number",size:"mini",placeholder:"X"},on:{input:function(t){return e.inputChangeRangeOffset(t,"x")}},scopedSlots:e._u([{key:"prepend",fn:function(){return[a("span",[e._v("X")])]},proxy:!0}],null,!1,829979430),model:{value:e.params.range.position.x,callback:function(t){e.$set(e.params.range.position,"x",t)},expression:"params.range.position.x"}})],1)],1),a("el-row",{staticClass:"items-center",attrs:{gutter:e.itemGutter}},[a("el-col",{attrs:{span:12}},[a("el-input",{attrs:{type:"number",size:"mini",placeholder:"Y"},on:{input:function(t){return e.inputChangeRangeOffset(t,"y")}},scopedSlots:e._u([{key:"prepend",fn:function(){return[e._v("Y")]},proxy:!0}],null,!1,3752717980),model:{value:e.params.range.position.y,callback:function(t){e.$set(e.params.range.position,"y",t)},expression:"params.range.position.y"}})],1),a("el-col",{attrs:{span:12}},[a("el-input",{attrs:{type:"number",size:"mini",placeholder:"Z"},on:{input:function(t){return e.inputChangeRangeOffset(t,"z")}},scopedSlots:e._u([{key:"prepend",fn:function(){return[a("span",[e._v("Z")])]},proxy:!0}],null,!1,4187818916),model:{value:e.params.range.position.z,callback:function(t){e.$set(e.params.range.position,"z",t)},expression:"params.range.position.z"}})],1)],1)],1),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-160"},[a("div",{staticClass:"items-center"},[a("div",{staticClass:"item-title",staticStyle:{"flex-shrink":"0"}},[e._v(" "+e._s(e.$t("featureSetting.triggerConditions.label3"))+" "),a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",placement:"top",enterable:!1}},[a("div",{attrs:{slot:"content"},slot:"content"},[e._v(" "+e._s(e.$t("featureSetting.triggerConditions.tooltip"))+" ")]),a("i",{staticClass:"outline-none el-input__icon el-icon-question"})])],1),a("el-input",{attrs:{type:"number",size:"mini",placeholder:e.$t("formRelational.radius.placeholder")},scopedSlots:e._u([{key:"append",fn:function(){return[a("span",[e._v("m")])]},proxy:!0}],null,!1,1549734309),model:{value:e.params.range.radius,callback:function(t){e.$set(e.params.range,"radius",t)},expression:"params.range.radius"}})],1)])]:e._e(),"sensor"==e.params.triggerType?[a("div",{staticClass:"styleSet-list-div h100 list-div-160"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[e._v(" "+e._s(e.$t("featureSetting.triggerConditions.label4"))+" ")]),a("div",{staticClass:"popup-item-center items-center",on:{click:function(t){return e.toggleRegionPanel()}}},[a("CommonSVG",{attrs:{"icon-class":"xyz",size:16}}),a("span",{staticClass:"margin-left-5"},[e._v(" "+e._s(e.$t("featureSetting.triggerConditions.label5"))+" ")])],1)]),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[e._v(e._s(e.$t("formRelational.baseTop.label")))]),a("el-input",{attrs:{type:"number",size:"mini",placeholder:e.$t("formRelational.baseTop.placeholder")},on:{input:function(t){return e.inputChangePolygonPostData(t,"base")}},model:{value:e.params.sensor.height,callback:function(t){e.$set(e.params.sensor,"height",t)},expression:"params.sensor.height"}})],1),e.regionPanelState?a("div",{staticClass:"regional-coordinates-container reginoal-dialog"},[a("div",{staticClass:"title"},[a("span",[e._v(e._s(e.$t("featureSetting.triggerConditions.label6")))]),a("div",[a("i",{staticClass:"el-icon-plus cursor-btn margin-right-8",on:{click:function(t){return e.addPoint()}}}),a("i",{staticClass:"el-icon-close cursor-btn",on:{click:function(t){return e.toggleRegionPanel()}}})])]),e._l(e.params.sensor.position,(function(t,i){return a("div",{key:"sensor"+i,staticClass:"position-list"},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("el-input",{attrs:{type:"number",size:"mini",placeholder:"X"},on:{input:function(t){return e.inputChangePolygonPostData(t,"positions")}},scopedSlots:e._u([{key:"prepend",fn:function(){return[a("span",[e._v("X")])]},proxy:!0}],null,!0),model:{value:t.x,callback:function(a){e.$set(t,"x",a)},expression:"pos.x"}})],1),a("el-col",{attrs:{span:12}},[a("el-input",{attrs:{type:"number",size:"mini",placeholder:"Y"},on:{input:function(t){return e.inputChangePolygonPostData(t,"positions")}},scopedSlots:e._u([{key:"prepend",fn:function(){return[e._v("Y")]},proxy:!0}],null,!0),model:{value:t.y,callback:function(a){e.$set(t,"y",a)},expression:"pos.y"}})],1)],1),a("div",{staticClass:"delete-btn"},[a("span",{staticClass:"coord-box",on:{click:function(t){return e.clickCheckedSensorCoordinate(i)}}},[a("CommonSVG",{attrs:{color:e.curIndex===i?"var(--theme)":"#FFFFFF",size:"16","icon-class":"select_coord"}})],1),e.params.sensor.position.length>3?a("i",{staticClass:"el-icon el-icon-remove-outline icons",on:{click:function(t){return e.delPoints(i)}}}):e._e()])],1)}))],2):e._e()]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("div",{staticClass:"item-title",staticStyle:{"flex-shrink":"0"}},[e._v(" "+e._s(e.$t("featureSetting.triggerConditions.label7"))+" "),a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",placement:"top",enterable:!1}},[a("div",{attrs:{slot:"content"},domProps:{innerHTML:e._s(e.$t("featureSetting.triggerConditions.tooltip1"))},slot:"content"}),a("i",{staticClass:"outline-none el-input__icon el-icon-question"})])],1),a("div",{staticClass:"popup-item-center items-center",on:{click:function(t){return e.toggleClickSelectTarget()}}},[a("CommonSVG",{attrs:{"icon-class":"mouse_left",size:16}}),a("span",{staticClass:"margin-left-5"},[e._v(e._s(e.params.sensor.target.name))])],1)])]),a("div",{staticClass:"after-lineX"})]:e._e()],2)},s=[],n=(a("d3b7"),a("3ca3"),a("ddb0"),a("b0c0"),a("a630"),a("159b"),a("a434"),a("caad"),a("d81d"),a("07ac"),{name:"TriggerConditions",props:["dragData"],components:{CommonSVG:function(){return a.e("chunk-51f90655").then(a.bind(null,"17d0"))}},data:function(){return{itemGutter:10,inputError:!1,typeText:"",clickCoordinateState:!1,regionPanelState:!1,curIndex:-1,params:{annotationID:"",name:"",triggerType:"range",range:{position:{x:0,y:0,z:0},radius:1},sensor:{position:[],height:0,target:{id:"",name:this.$t("featureSetting.triggerConditions.label8"),type:""}}},selectTargetState:!1}},created:function(){this.dragData.isDragData?this.init():this.initEdit()},methods:{init:function(){var e=this;this.params.triggerType=this.dragData.subType,this.typeText=this.setTypeText(this.dragData.subType);var t=0;this.params.name=this.dragData.name||this.typeText+this.$t("topToolBarMenu.advanced.children.trigger.name");var a=Array.from(window.scene.triggers.values());a.forEach((function(a){a.name===e.params.name+t&&(t+=1)})),this.params.name=this.params.name+t,"sensor"==this.dragData.subType?(this.addTemporaryPolygonFeature(this.dragData.sensorPosition),this.dragData.sensorPosition.forEach((function(t){e.params.sensor.position.push({x:t[0],y:t[1]})})),this.params.sensor.height=this.dragData.sensorPosition[0][2]):(this.params.range.position=this.dragData.rangePosition,this.addTemporaryAnnotationFeature(this.dragData.rangePosition))},initEdit:function(){var e=this;if(this.params.triggerType=this.dragData.subType,this.typeText=this.setTypeText(this.dragData.subType),this.params.name=this.dragData.name,"range"==this.dragData.subType&&(this.params.range.position.x=this.dragData.center[0],this.params.range.position.y=this.dragData.center[1],this.params.range.position.z=this.dragData.center[2],this.addTemporaryAnnotationFeature(this.dragData.center),this.params.range.radius=this.dragData.radius),"sensor"==this.dragData.subType){var t=window.scene.triggers.get(this.dragData.id),a=null;a="object"==t.objectType?window.scene.findObject(t.objectID):window.scene.findFeature(t.objectID),a?(this.params.sensor.target.id=t.objectID,this.params.sensor.target.type=t.objectType,this.params.sensor.target.name=a.name):this.$message.error(this.$t("messageTips.noTriggerObject")),this.addTemporaryPolygonFeature(this.dragData.points),this.dragData.points.forEach((function(t){e.params.sensor.position.push({x:t[0],y:t[1]})})),this.params.sensor.height=this.dragData.points[0][2]}},toggleRegionPanel:function(){this.regionPanelState=!this.regionPanelState},setTypeText:function(e){var t="";return"range"==e&&(t=this.$t("topToolBarMenu.advanced.children.trigger.extend.range.name")),"sensor"==e&&(t=this.$t("topToolBarMenu.advanced.children.trigger.extend.sensor.name")),t},clickCheckedCoordinate:function(){this.clickCoordinateState?this.offCheckedCoordinate():(window.scene.mv.status.selectable=!1,this.$message.success(this.$t("messageTips.checkCoordinate")),this.clickCoordinateState=!0,window.scene.mv.events.singleSelection.on("default",this.onCheckedCoordinate))},onCheckedCoordinate:function(e){if(window.scene.features.has("trigger-center-annotation")){var t=window.scene.features.get("trigger-center-annotation");t.dispose(),window.scene.render()}var a=window.scene.mv._THREE,i=window.scene.queryPosition(new a.Vector2(e.clientX,e.clientY));""!=i&&void 0!=i?(this.params.range.position=i,this.offCheckedCoordinate(),this.addTemporaryAnnotationFeature(i)):this.$message.error(this.$t("messageTips.errorCheckCoordinate"))},offCheckedCoordinate:function(){window.scene.mv.status.selectable=!0,this.clickCoordinateState=!1,window.scene.mv.events.singleSelection.off("default",this.onCheckedCoordinate)},addTemporaryAnnotationFeature:function(e){var t=window.scene.mv.tools.coordinate.vector2mercator(e),a={position:{x:0,y:0,z:0},content:"",htmlCode:'<div class="triggerScatterPlot"></div>',cssCode:"",jsCode:"",minDistance:0,maxDistance:1/0},i="trigger-center-annotation",s=window.scene.addFeature("annotation",i);this.params.annotationID=s.id,s.origin=[t[0],t[1]],s.altitude=t[2],s.name=this.$t("featureSetting.triggerConditions.label9"),s.offset=[0,0,0],s.dataKey="annotation-"+s.id,window.scene.postData(a,s.dataKey),s.load(),window.scene.render()},inputChangeRangeOffset:function(e,t){var a=[parseFloat(this.params.range.position.x+""),parseFloat(this.params.range.position.y+""),parseFloat(this.params.range.position.z+"")],i=window.scene.mv.tools.coordinate.vector2mercator(a),s=window.scene.features.get("trigger-center-annotation");s.origin=[i[0],i[1]],s.altitude=i[2]},delPoints:function(e){this.params.sensor.position.splice(e,1),-1!=this.curIndex&&this.offEvent(),this.inputChangePolygonPostData("","positions")},addPoint:function(){this.params.sensor.position.push({x:0,y:0,z:0}),-1!=this.curIndex&&this.offEvent()},clickCheckedSensorCoordinate:function(e){-1==this.curIndex?(this.curIndex=e,window.scene.mv.status.selectable=!1,this.$message.success(this.$t("messageTips.checkCoordinate")),window.scene.mv.events.singleSelection.on("default",this.onCheckedSensorCoordinate)):this.curIndex===e&&this.offEvent()},onCheckedSensorCoordinate:function(e){var t=window.scene.mv._THREE,a=window.scene.queryPosition(new t.Vector2(e.clientX,e.clientY));""!=a&&void 0!=a?(this.params.sensor.position[this.curIndex].x=a.x,this.params.sensor.position[this.curIndex].y=a.y,this.offEvent(),this.inputChangePolygonPostData("","positions")):this.$message.error(this.$t("messageTips.errorCheckCoordinate"))},offEvent:function(){this.curIndex=-1,window.scene.mv.status.selectable=!0,window.scene.mv.events.singleSelection.off("default",this.onCheckedSensorCoordinate)},addTemporaryPolygonFeature:function(e){var t=window.scene.addFeature("polygon","trigger-sensor-polygon"),a={positions:e,top:0,base:e[0][2],flatten:!1,excavation:!1,extrude:!1,water:!1,fill:!0,showline:!1,color:"rgb(50, 255, 0)",opacity:.3,direction:"x"};t.name=this.$t("featureSetting.triggerConditions.label4"),t.load(),t.dataKey=window.scene.postData(a),window.scene.render()},toggleClickSelectTarget:function(){this.selectTargetState?this.offClickSelectTarget():(this.selectTargetState=!0,this.$message.info(this.$t("featureSetting.triggerConditions.message")),window.scene.mv.events.featurePicked.on("default",this.onClickSelectTarget))},onClickSelectTarget:function(e){var t=this,a=["model","gltf","fbx"];e.type&&a.includes(e.type)&&("model"===e.type?setTimeout((function(){t.params.sensor.target.id=window.scene.getSelection()[0].id,t.params.sensor.target.name=window.scene.getSelection()[0].name,t.params.sensor.target.type="element"})):(this.params.sensor.target.id=e.id,this.params.sensor.target.name=e.name,this.params.sensor.target.type=e.type),this.offClickSelectTarget())},offClickSelectTarget:function(){this.selectTargetState=!1,window.scene.mv.events.featurePicked.off("default",this.onClickSelectTarget)},beforeValidate:function(){if("sensor"==this.params.triggerType){if(""==this.params.sensor.target.id)return this.$message.error(this.$t("featureSetting.triggerConditions.message1")),!1}else if(this.params.range.radius<=0)return this.$message.error(this.$t("featureSetting.triggerConditions.message2")),!1;return!0},inputChangePolygonPostData:function(e,t){var a=window.scene.features.get("trigger-sensor-polygon"),i={},s=null;switch(i.fill=!0,t){case"base":s=parseFloat(e+"");break;case"positions":s=[],this.params.sensor.position.forEach((function(e,t){s[t]=Object.values(e).map((function(e){return parseFloat(e+"")}))}));break}i[t]=s,window.scene.postData(i,a.dataKey),setTimeout((function(){window.scene.render()}),200)}},beforeDestroy:function(){this.clickCoordinateState&&this.offCheckedCoordinate(),"range"==this.dragData.subType&&window.scene.removeFeature(this.params.annotationID,!0),"sensor"==this.dragData.subType&&window.scene.removeFeature("trigger-sensor-polygon",!0)}}),r=n,o=(a("b8b9"),a("2877")),l=Object(o["a"])(r,i,s,!1,null,"3bc04974",null);t["a"]=l.exports},e8db:function(e,t,a){"use strict";a("fc31")},f371:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e._t("default"),a("div",{staticClass:"styleSet-list-div"},[a("div",{staticClass:"items-center margin-bottom-8 margin-top-8"},[a("span",{staticClass:"item-title"},[e._v(e._s(e.$t("formRelational.width.label")))]),a("el-input",{attrs:{size:"mini",type:"number",placeholder:e.$t("formRelational.width.placeholder")},on:{input:function(t){return e.inputChangePostData("width")}},scopedSlots:e._u([{key:"append",fn:function(){return[a("span",[e._v("m")])]},proxy:!0}]),model:{value:e.params.width,callback:function(t){e.$set(e.params,"width",t)},expression:"params.width"}})],1),a("div",{staticClass:"items-center margin-bottom-8"},[a("span",{staticClass:"item-title"},[e._v(e._s(e.$t("formRelational.top.label")))]),a("el-input",{attrs:{size:"mini",type:"number",placeholder:e.$t("formRelational.top.placeholder")},on:{input:function(t){return e.inputChangePostData("height")}},scopedSlots:e._u([{key:"append",fn:function(){return[a("span",[e._v("m")])]},proxy:!0}]),model:{value:e.params.height,callback:function(t){e.$set(e.params,"height",t)},expression:"params.height"}})],1),a("div",{staticClass:"items-center margin-bottom-8"},[a("span",{staticClass:"item-title"},[e._v(" "+e._s(e.$t("featureSetting.style.waterFall.label"))+" ")]),a("el-input",{attrs:{size:"mini",type:"number",placeholder:e.$t("featureSetting.style.waterFall.placeholder")},on:{input:function(t){return e.inputChangePostData("depth")}},scopedSlots:e._u([{key:"append",fn:function(){return[a("span",[e._v("m")])]},proxy:!0}]),model:{value:e.params.depth,callback:function(t){e.$set(e.params,"depth",t)},expression:"params.depth"}})],1)])],2)},s=[],n=(a("e9c4"),{name:"TriggerWaterfall",props:["dragData"],data:function(){return{params:{width:1,height:1,depth:1}}},created:function(){this.dragData.isDragData&&this.$emit("saveTriggerExtendParams",this.params)},methods:{initParams:function(e){this.params=function(e){var t=e.width,a=e.height,i=e.depth;return{width:t,height:a,depth:i}}(e)},inputChangePostData:function(e){var t=JSON.parse(JSON.stringify(this.params)),a=parseFloat(t[e]+"");t[e]=a,this.$emit("saveTriggerExtendParams",t)}}}),r=n,o=(a("a78c"),a("2877")),l=Object(o["a"])(r,i,s,!1,null,"15dd52c0",null);t["default"]=l.exports},fb80:function(e,t,a){"use strict";a("ca4e")},fc31:function(e,t,a){}}]);