(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-31c3a4ce"],{"07eb":function(t,e,i){},"14b5":function(t,e,i){"use strict";i("07eb")},"3eed":function(t,e,i){},d535:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("dialogComp",{attrs:{hideHeader:!1,needClose:"true",zIndex:"4",draw:!0,right:10,drag:!0,title:t.$t("dialog.choiceSet.label"),icon:"icon-details",width:360,minWidth:345,dragTopOffset:t.dragTopOffset,height:t.attrHeight,position:"fixed",type:"detailInfo",top:t.attrTop},on:{close:t.closeDialog},scopedSlots:t._u([{key:"center",fn:function(){return[i("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],staticClass:"elementList-container",attrs:{"element-loading-text":t.$t("others.dataLoading"),"element-loading-spinner":"el-icon-loading","element-loading-background":"rgba(0, 0, 0, 0.8)"}},[t.detailsData.length>0?[i("div",{staticClass:"list-content virtually-list-content",on:{scroll:t.updateVisibleList}},[i("div",{staticClass:"item t-header"},[i("span",{staticClass:"item-text"},[t._v(" "+t._s(t.$t("dialog.attribute.table.label3"))+" ")]),i("span",{staticClass:"item-text"},[t._v(" "+t._s(t.$t("dialog.attribute.table.label2"))+" ")])]),i("div",{staticClass:"virtually-item-content"},t._l(t.visibleList,(function(e){return i("div",{key:e.id,staticClass:"item"},[i("span",{staticClass:"item-text",attrs:{title:e.elementID}},[t._v(t._s(e.elementID))]),i("span",{staticClass:"item-text",attrs:{title:e.name}},[t._v(t._s(e.name))])])})),0)]),i("div",{staticClass:"bottom-menu"},[i("div",[t._v(t._s(t.$t("formRelational.element.message",{num:t.detailsData.length})))]),!t.isEdit||t.isShare||t.isVothingScenemanager?t._e():i("div",[i("span",{staticClass:"cursor-btn",on:{click:function(e){return t.filterHandle()}}},[t._v(" "+t._s(t.$t("dialog.filterElement.label"))+" ")]),t.detailsData.length&&!t.isLoading?i("span",{staticClass:"cursor-btn margin-left-15",on:{click:function(e){return t.create()}}},[t._v(" "+t._s(t.$t("dialog.choiceSet.label3"))+" ")]):t._e()])])]:t._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:0===t.detailsData.length,expression:"detailsData.length === 0"}],staticClass:"list-wrap text-center no-select"},[t._v(" "+t._s(t.$t("dialog.choiceSet.label2"))+" ")])],2)]},proxy:!0}])}),t.addFormDialog.dialogState?i("dialogComp",{staticClass:"sourceDom",attrs:{hideHeader:!1,needClose:"true",zIndex:"4",position:"fixed",draw:!1,top:0,right:0,bottom:0,left:0,drag:!1,title:t.$t("dialog.choiceSet.label3"),width:400,height:150,type:"detailInfo"},on:{close:t.closeAddDialog},scopedSlots:t._u([{key:"center",fn:function(){return[i("div",{staticClass:"add-container"},[i("el-input",{class:{"is-error":t.addFormDialog.inputError},attrs:{size:"small",placeholder:t.$t("dialog.choiceSet.placeholder")},model:{value:t.addFormDialog.name,callback:function(e){t.$set(t.addFormDialog,"name",e)},expression:"addFormDialog.name"}}),i("div",{staticClass:"bottom-btn"},[i("span",{staticClass:"cursor-btn cancel margin-right-8",on:{click:t.closeAddDialog}},[t._v(" "+t._s(t.$t("messageTips.cancel"))+" ")]),i("span",{staticClass:"cursor-btn confirm",on:{click:t.saveData}},[t._v(" "+t._s(t.$t("menuIconName.confirm"))+" ")])])],1)]},proxy:!0}],null,!1,157957973)}):t._e()],1)},n=[],s=i("c7eb"),o=i("1da1"),r=(i("d3b7"),i("3ca3"),i("ddb0"),i("d81d"),i("99af"),i("a15b"),i("b0c0"),i("159b"),i("e9c4"),i("fb6a"),{name:"elementList",props:["isShare","isVothingScenemanager"],components:{CommonSVG:function(){return i.e("chunk-51f90655").then(i.bind(null,"17d0"))}},data:function(){return{detailsData:[],attrHeight:500,attrTop:170,isLoading:!0,filterData:[],addFormDialog:{dialogState:!1,name:"",inputError:!1},visibleList:[],elementIDs:[]}},computed:{dragTopOffset:function(){return this.$store.state.scene.topToolBarHeight},currentFilterId:function(){return this.$store.state.menuList.currentFilterId},filterConditionData:function(){return this.$store.state.menuList.filterConditionData},isEdit:function(){return this.$store.state.scene.sceneEditMode}},created:function(){this.setDialogSize(),window.addEventListener("resize",this.setDialogSize),this.SearchElementByProperty()},mounted:function(){},watch:{filterConditionData:function(t){}},beforeDestroy:function(){window.removeEventListener("resize",this.setDialogSize)},methods:{setDialogSize:function(){var t=document.body.clientHeight,e=document.querySelector(".desktop-view").clientHeight;0==e?(this.attrHeight=t-40-30,this.attrTop=50):(this.attrHeight=t-e-30,this.attrTop=e+10)},filterHandle:function(){this.$store.commit("toggleActiveDialog","filterCondition")},formatFilterData:function(){var t=this;this.filterData.map((function(e){return e.entityStr=t.createEntity(e),e}))},createEntity:function(t){var e="",i=isNaN(t.conditionsValue)?"'".concat(t.conditionsValue,"'"):t.conditionsValue;switch(t.conditions){case"1":e="Value=".concat(i);break;case"2":e="Value<>".concat(i);break;case"3":e="CAST(Value as DOUBLE) > ".concat(i);break;case"4":e="CAST(Value as DOUBLE) < ".concat(i);break;case"5":e="Value like '%".concat(t.conditionsValue,"%'");break;case"6":e="Value not like '%".concat(t.conditionsValue,"%'");break}return"(Name='".concat(t.peculiarity,"' AND ").concat(e,")")},SearchElementByProperty:function(){var t=this;return Object(o["a"])(Object(s["a"])().mark((function e(){var i,a,n,o;return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.filterData=t.filterConditionData,t.formatFilterData(),t.detailsData=[],t.isLoading=!0,t.visibleList=[],window.scene.clearSelection(),i=window.scene.features.get(t.currentFilterId),a=t.filterData.map((function(t){return t.entityStr})).join(" |AND| "),n=new FormData,n.append("FeatureID",i.modelID),n.append("VaultID",i.vaultID),n.append("Entity",a),e.next=14,t.$api.SearchElementByProperty(n);case 14:o=e.sent,0===o.data.length&&t.$message.warning(t.$t("dialog.choiceSet.message")),t.detailsData=o.data,t.isLoading=!1,t.$nextTick((function(){if(o.data.length){t.updateVisibleList(),t.elementIDs=o.data.map((function(t){return"".concat(i.id,"^").concat(t.elementID)}));var e=window.scene.createArrayObject(t.elementIDs);e.select(),window.scene.render()}}));case 19:case"end":return e.stop()}}),e)})))()},create:function(){0!==this.detailsData.length&&(this.addFormDialog.dialogState=!0)},showAddDialog:function(){this.addFormDialog.dialogState=!0},saveData:function(){var t=this;if(""==this.addFormDialog.name)return this.$message.error(this.$t("messageTips.nameNotEmpty")),void(this.addFormDialog.inputError=!0);var e=0;if(window.scene.selectionSets.forEach((function(i){i.name==t.addFormDialog.name&&e++})),e>0)return this.$message.error(this.addFormDialog.name+" "+this.$t("messageTips.nameAlreadyExists")),!1;var i={};i[this.currentFilterId]=this.elementIDs,window.scene.addSelectionSet(this.addFormDialog.name,i,"","");var a=[];window.scene.selectionSets.forEach((function(t){a.push(JSON.parse(JSON.parse(JSON.stringify(t))))})),this.$message.success(this.$t("messageTips.createdSuccess")),this.$store.commit("setSelectionSets",a),this.closeDialog(),this.closeAddDialog()},closeDialog:function(){this.$store.commit("setFilterConditionData",[]),this.$store.commit("toggleActiveDialog","filterConditionList")},closeAddDialog:function(){this.addFormDialog.dialogState=!1,this.addFormDialog.name=""},updateVisibleList:function(){var t=document.querySelector(".virtually-list-content"),e=document.querySelector(".virtually-item-content"),i=document.querySelector(".virtually-list-content").querySelector(".t-header"),a=Math.floor(t.scrollTop/40),n=a+Math.ceil(t.clientHeight/40)+4;this.visibleList=this.detailsData.slice(a,n),e.style.top="".concat(40*a+40,"px"),i.style.top="".concat(t.scrollTop,"px")}}}),l=r,c=(i("14b5"),i("f037"),i("2877")),d=Object(c["a"])(l,a,n,!1,null,"128f1feb",null);e["default"]=d.exports},f037:function(t,e,i){"use strict";i("3eed")}}]);