(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-32c959d2"],{"0a89":function(t,e,n){"use strict";n.d(e,"b",(function(){return s})),n.d(e,"a",(function(){return a}));var o=n("3742"),i=n("7061"),r=n("6a89"),s=function(){function t(t,e,n,o,i){this.value=t,this.selectionStart=e,this.selectionEnd=n,this.selectionStartPosition=o,this.selectionEndPosition=i}return t.prototype.toString=function(){return"[ <"+this.value+">, selectionStart: "+this.selectionStart+", selectionEnd: "+this.selectionEnd+"]"},t.readFromTextArea=function(e){return new t(e.getValue(),e.getSelectionStart(),e.getSelectionEnd(),null,null)},t.prototype.collapseSelection=function(){return new t(this.value,this.value.length,this.value.length,null,null)},t.prototype.writeToTextArea=function(t,e,n){e.setValue(t,this.value),n&&e.setSelectionRange(t,this.selectionStart,this.selectionEnd)},t.prototype.deduceEditorPosition=function(t){if(t<=this.selectionStart){var e=this.value.substring(t,this.selectionStart);return this._finishDeduceEditorPosition(this.selectionStartPosition,e,-1)}if(t>=this.selectionEnd){e=this.value.substring(this.selectionEnd,t);return this._finishDeduceEditorPosition(this.selectionEndPosition,e,1)}var n=this.value.substring(this.selectionStart,t);if(-1===n.indexOf(String.fromCharCode(8230)))return this._finishDeduceEditorPosition(this.selectionStartPosition,n,1);var o=this.value.substring(t,this.selectionEnd);return this._finishDeduceEditorPosition(this.selectionEndPosition,o,-1)},t.prototype._finishDeduceEditorPosition=function(t,e,n){var o=0,i=-1;while(-1!==(i=e.indexOf("\n",i+1)))o++;return[t,n*e.length,o]},t.selectedText=function(e){return new t(e,0,e.length,null,null)},t.deduceInput=function(t,e,n){if(!t)return{text:"",replaceCharCnt:0};var i=t.value,r=t.selectionStart,s=t.selectionEnd,a=e.value,u=e.selectionStart,l=e.selectionEnd,c=i.substring(s),p=a.substring(l),d=o["d"](c,p);a=a.substring(0,a.length-d),i=i.substring(0,i.length-d);var h=i.substring(0,r),f=a.substring(0,u),g=o["c"](h,f);if(a=a.substring(g),i=i.substring(g),u-=g,r-=g,l-=g,s-=g,n&&u===l&&i.length>0){var m=null;if(u===a.length?o["N"](a,i)&&(m=a.substring(i.length)):o["m"](a,i)&&(m=a.substring(0,a.length-i.length)),null!==m&&m.length>0&&(/\uFE0F/.test(m)||o["g"](m)))return{text:m,replaceCharCnt:0}}if(u===l){if(i===a&&0===r&&s===i.length&&u===a.length&&-1===a.indexOf("\n")&&o["h"](a))return{text:"",replaceCharCnt:0};var _=h.length-g;return{text:a,replaceCharCnt:_}}var v=s-r;return{text:a,replaceCharCnt:v}},t.EMPTY=new t("",0,0,null,null),t}(),a=function(){function t(){}return t._getPageOfLine=function(t,e){return Math.floor((t-1)/e)},t._getRangeForPage=function(t,e){var n=t*e,o=n+1,i=n+e;return new r["a"](o,1,i+1,1)},t.fromEditorSelection=function(e,n,o,a,u){var l,c=t._getPageOfLine(o.startLineNumber,a),p=t._getRangeForPage(c,a),d=t._getPageOfLine(o.endLineNumber,a),h=t._getRangeForPage(d,a),f=p.intersectRanges(new r["a"](1,1,o.startLineNumber,o.startColumn)),g=n.getValueInRange(f,1),m=n.getLineCount(),_=n.getLineMaxColumn(m),v=h.intersectRanges(new r["a"](o.endLineNumber,o.endColumn,m,_)),y=n.getValueInRange(v,1);if(c===d||c+1===d)l=n.getValueInRange(o,1);else{var C=p.intersectRanges(o),w=h.intersectRanges(o);l=n.getValueInRange(C,1)+String.fromCharCode(8230)+n.getValueInRange(w,1)}if(u){var b=500;g.length>b&&(g=g.substring(g.length-b,g.length)),y.length>b&&(y=y.substring(0,b)),l.length>2*b&&(l=l.substring(0,b)+String.fromCharCode(8230)+l.substring(l.length-b,l.length))}return new s(g+l+y,g.length,g.length+l.length,new i["a"](o.startLineNumber,o.startColumn),new i["a"](o.endLineNumber,o.endColumn))},t}()},"1ddc":function(t,e,n){"use strict";n.d(e,"b",(function(){return m})),n.d(e,"a",(function(){return v}));var o=n("0f70"),i=n("308f"),r=n("a666"),s=n("30db"),a=function(){function t(t,e){this.chr=t,this.type=e,this.width=0}return t.prototype.fulfill=function(t){this.width=t},t}(),u=function(){function t(t,e){this._bareFontInfo=t,this._requests=e,this._container=null,this._testElements=null}return t.prototype.read=function(){this._createDomElements(),document.body.appendChild(this._container),this._readFromDomElements(),document.body.removeChild(this._container),this._container=null,this._testElements=null},t.prototype._createDomElements=function(){var e=document.createElement("div");e.style.position="absolute",e.style.top="-50000px",e.style.width="50000px";var n=document.createElement("div");n.style.fontFamily=this._bareFontInfo.getMassagedFontFamily(),n.style.fontWeight=this._bareFontInfo.fontWeight,n.style.fontSize=this._bareFontInfo.fontSize+"px",n.style.fontFeatureSettings=this._bareFontInfo.fontFeatureSettings,n.style.lineHeight=this._bareFontInfo.lineHeight+"px",n.style.letterSpacing=this._bareFontInfo.letterSpacing+"px",e.appendChild(n);var o=document.createElement("div");o.style.fontFamily=this._bareFontInfo.getMassagedFontFamily(),o.style.fontWeight="bold",o.style.fontSize=this._bareFontInfo.fontSize+"px",o.style.fontFeatureSettings=this._bareFontInfo.fontFeatureSettings,o.style.lineHeight=this._bareFontInfo.lineHeight+"px",o.style.letterSpacing=this._bareFontInfo.letterSpacing+"px",e.appendChild(o);var i=document.createElement("div");i.style.fontFamily=this._bareFontInfo.getMassagedFontFamily(),i.style.fontWeight=this._bareFontInfo.fontWeight,i.style.fontSize=this._bareFontInfo.fontSize+"px",i.style.fontFeatureSettings=this._bareFontInfo.fontFeatureSettings,i.style.lineHeight=this._bareFontInfo.lineHeight+"px",i.style.letterSpacing=this._bareFontInfo.letterSpacing+"px",i.style.fontStyle="italic",e.appendChild(i);for(var r=[],s=0,a=this._requests;s<a.length;s++){var u=a[s],l=void 0;0===u.type&&(l=n),2===u.type&&(l=o),1===u.type&&(l=i),l.appendChild(document.createElement("br"));var c=document.createElement("span");t._render(c,u),l.appendChild(c),r.push(c)}this._container=e,this._testElements=r},t._render=function(t,e){if(" "===e.chr){for(var n="&#160;",o=0;o<8;o++)n+=n;t.innerHTML=n}else{var i=e.chr;for(o=0;o<8;o++)i+=i;t.textContent=i}},t.prototype._readFromDomElements=function(){for(var t=0,e=this._requests.length;t<e;t++){var n=this._requests[t],o=this._testElements[t];n.fulfill(o.offsetWidth/256)}},t}();function l(t,e){var n=new u(t,e);n.read()}var c=n("a37f"),p=n("8830"),d=n("fd49"),h=n("fb71"),f=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},t(e,n)};return function(e,n){function o(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),g=function(){function t(){this._keys=Object.create(null),this._values=Object.create(null)}return t.prototype.has=function(t){var e=t.getId();return!!this._values[e]},t.prototype.get=function(t){var e=t.getId();return this._values[e]},t.prototype.put=function(t,e){var n=t.getId();this._keys[n]=t,this._values[n]=e},t.prototype.remove=function(t){var e=t.getId();delete this._keys[e],delete this._values[e]},t.prototype.getValues=function(){var t=this;return Object.keys(this._keys).map((function(e){return t._values[e]}))},t}();function m(){_.INSTANCE.clearCache()}var _=function(t){function e(){var e=t.call(this)||this;return e._onDidChange=e._register(new i["a"]),e.onDidChange=e._onDidChange.event,e._cache=new g,e._evictUntrustedReadingsTimeout=-1,e}return f(e,t),e.prototype.dispose=function(){-1!==this._evictUntrustedReadingsTimeout&&(clearTimeout(this._evictUntrustedReadingsTimeout),this._evictUntrustedReadingsTimeout=-1),t.prototype.dispose.call(this)},e.prototype.clearCache=function(){this._cache=new g,this._onDidChange.fire()},e.prototype._writeToCache=function(t,e){var n=this;this._cache.put(t,e),e.isTrusted||-1!==this._evictUntrustedReadingsTimeout||(this._evictUntrustedReadingsTimeout=setTimeout((function(){n._evictUntrustedReadingsTimeout=-1,n._evictUntrustedReadings()}),5e3))},e.prototype._evictUntrustedReadings=function(){for(var t=this._cache.getValues(),e=!1,n=0,o=t.length;n<o;n++){var i=t[n];i.isTrusted||(e=!0,this._cache.remove(i))}e&&this._onDidChange.fire()},e.prototype.readConfiguration=function(t){if(!this._cache.has(t)){var n=e._actualReadConfiguration(t);(n.typicalHalfwidthCharacterWidth<=2||n.typicalFullwidthCharacterWidth<=2||n.spaceWidth<=2||n.maxDigitWidth<=2)&&(n=new h["b"]({zoomLevel:o["c"](),fontFamily:n.fontFamily,fontWeight:n.fontWeight,fontSize:n.fontSize,fontFeatureSettings:n.fontFeatureSettings,lineHeight:n.lineHeight,letterSpacing:n.letterSpacing,isMonospace:n.isMonospace,typicalHalfwidthCharacterWidth:Math.max(n.typicalHalfwidthCharacterWidth,5),typicalFullwidthCharacterWidth:Math.max(n.typicalFullwidthCharacterWidth,5),canUseHalfwidthRightwardsArrow:n.canUseHalfwidthRightwardsArrow,spaceWidth:Math.max(n.spaceWidth,5),middotWidth:Math.max(n.middotWidth,5),maxDigitWidth:Math.max(n.maxDigitWidth,5)},!1)),this._writeToCache(t,n)}return this._cache.get(t)},e.createRequest=function(t,e,n,o){var i=new a(t,e);return n.push(i),o&&o.push(i),i},e._actualReadConfiguration=function(t){var e=[],n=[],i=this.createRequest("n",0,e,n),r=this.createRequest("ｍ",0,e,null),s=this.createRequest(" ",0,e,n),a=this.createRequest("0",0,e,n),u=this.createRequest("1",0,e,n),c=this.createRequest("2",0,e,n),p=this.createRequest("3",0,e,n),f=this.createRequest("4",0,e,n),g=this.createRequest("5",0,e,n),m=this.createRequest("6",0,e,n),_=this.createRequest("7",0,e,n),v=this.createRequest("8",0,e,n),y=this.createRequest("9",0,e,n),C=this.createRequest("→",0,e,n),w=this.createRequest("￫",0,e,null),b=this.createRequest("·",0,e,n);this.createRequest("|",0,e,n),this.createRequest("/",0,e,n),this.createRequest("-",0,e,n),this.createRequest("_",0,e,n),this.createRequest("i",0,e,n),this.createRequest("l",0,e,n),this.createRequest("m",0,e,n),this.createRequest("|",1,e,n),this.createRequest("_",1,e,n),this.createRequest("i",1,e,n),this.createRequest("l",1,e,n),this.createRequest("m",1,e,n),this.createRequest("n",1,e,n),this.createRequest("|",2,e,n),this.createRequest("_",2,e,n),this.createRequest("i",2,e,n),this.createRequest("l",2,e,n),this.createRequest("m",2,e,n),this.createRequest("n",2,e,n),l(t,e);for(var S=Math.max(a.width,u.width,c.width,p.width,f.width,g.width,m.width,_.width,v.width,y.width),x=t.fontFeatureSettings===d["d"].OFF,T=n[0].width,E=1,O=n.length;x&&E<O;E++){var M=T-n[E].width;if(M<-.001||M>.001){x=!1;break}}var A=!0;x&&w.width!==T&&(A=!1),w.width>C.width&&(A=!1);var D=o["b"]()>2e3;return new h["b"]({zoomLevel:o["c"](),fontFamily:t.fontFamily,fontWeight:t.fontWeight,fontSize:t.fontSize,fontFeatureSettings:t.fontFeatureSettings,lineHeight:t.lineHeight,letterSpacing:t.letterSpacing,isMonospace:x,typicalHalfwidthCharacterWidth:i.width,typicalFullwidthCharacterWidth:r.width,canUseHalfwidthRightwardsArrow:A,spaceWidth:s.width,middotWidth:b.width,maxDigitWidth:S},D)},e.INSTANCE=new e,e}(r["a"]),v=function(t){function e(e,n,i,r){void 0===i&&(i=null);var s=t.call(this,e,n)||this;return s.accessibilityService=r,s._elementSizeObserver=s._register(new c["a"](i,n.dimension,(function(){return s._onReferenceDomElementSizeChanged()}))),s._register(_.INSTANCE.onDidChange((function(){return s._onCSSBasedConfigurationChanged()}))),s._validatedOptions.get(9)&&s._elementSizeObserver.startObserving(),s._register(o["o"]((function(t){return s._recomputeOptions()}))),s._register(s.accessibilityService.onDidChangeScreenReaderOptimized((function(){return s._recomputeOptions()}))),s._recomputeOptions(),s}return f(e,t),e.applyFontInfoSlow=function(t,e){t.style.fontFamily=e.getMassagedFontFamily(),t.style.fontWeight=e.fontWeight,t.style.fontSize=e.fontSize+"px",t.style.fontFeatureSettings=e.fontFeatureSettings,t.style.lineHeight=e.lineHeight+"px",t.style.letterSpacing=e.letterSpacing+"px"},e.applyFontInfo=function(t,e){t.setFontFamily(e.getMassagedFontFamily()),t.setFontWeight(e.fontWeight),t.setFontSize(e.fontSize),t.setFontFeatureSettings(e.fontFeatureSettings),t.setLineHeight(e.lineHeight),t.setLetterSpacing(e.letterSpacing)},e.prototype._onReferenceDomElementSizeChanged=function(){this._recomputeOptions()},e.prototype._onCSSBasedConfigurationChanged=function(){this._recomputeOptions()},e.prototype.observeReferenceElement=function(t){this._elementSizeObserver.observe(t)},e.prototype.dispose=function(){t.prototype.dispose.call(this)},e.prototype._getExtraEditorClassName=function(){var t="";return o["k"]||o["n"]||(t+="no-user-select "),s["e"]&&(t+="mac "),t},e.prototype._getEnvConfiguration=function(){return{extraEditorClassName:this._getExtraEditorClassName(),outerWidth:this._elementSizeObserver.getWidth(),outerHeight:this._elementSizeObserver.getHeight(),emptySelectionClipboard:o["m"]||o["h"],pixelRatio:o["a"](),zoomLevel:o["c"](),accessibilitySupport:this.accessibilityService.isScreenReaderOptimized()?2:this.accessibilityService.getAccessibilitySupport()}},e.prototype.readConfiguration=function(t){return _.INSTANCE.readConfiguration(t)},e}(p["a"])},"56f3":function(t,e,n){},9904:function(t,e,n){"use strict";n.d(e,"a",(function(){return M}));var o=n("11f7"),i=n("30db"),r=n("a60f"),s=n("a666"),a=n("0f70"),u=n("5d28"),l=n("5fe7"),c=n("bae1"),p=n("411b"),d=n("62bd"),h=n("7061"),f=n("8025"),g=n("e2dc"),m=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},t(e,n)};return function(e,n){function o(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}();function _(t){return function(e,n){var o=!1;return t&&(o=t.mouseTargetIsWidget(n)),o||n.preventDefault(),n}}var v=function(t){function e(n,i,r){var s=t.call(this)||this;s._isFocused=!1,s._context=n,s.viewController=i,s.viewHelper=r,s.mouseTargetFactory=new c["c"](s._context,r),s._mouseDownOperation=s._register(new y(s._context,s.viewController,s.viewHelper,(function(t,e){return s._createMouseTarget(t,e)}),(function(t){return s._getMouseColumn(t)}))),s._asyncFocus=s._register(new l["d"]((function(){return s.viewHelper.focusTextArea()}),0)),s.lastMouseLeaveTime=-1;var h=new p["c"](s.viewHelper.viewDomNode);s._register(h.onContextMenu(s.viewHelper.viewDomNode,(function(t){return s._onContextMenu(t,!0)}))),s._register(h.onMouseMoveThrottled(s.viewHelper.viewDomNode,(function(t){return s._onMouseMove(t)}),_(s.mouseTargetFactory),e.MOUSE_MOVE_MINIMUM_TIME)),s._register(h.onMouseUp(s.viewHelper.viewDomNode,(function(t){return s._onMouseUp(t)}))),s._register(h.onMouseLeave(s.viewHelper.viewDomNode,(function(t){return s._onMouseLeave(t)}))),s._register(h.onMouseDown(s.viewHelper.viewDomNode,(function(t){return s._onMouseDown(t)})));var f=function(t){if(s.viewController.emitMouseWheel(t),s._context.configuration.options.get(57)){var e=new u["c"](t);if(e.browserEvent.ctrlKey||e.browserEvent.metaKey){var n=d["a"].getZoomLevel(),o=e.deltaY>0?1:-1;d["a"].setZoomLevel(n+o),e.preventDefault(),e.stopPropagation()}}};return s._register(o["j"](s.viewHelper.viewDomNode,a["f"]?"mousewheel":"wheel",f,{capture:!0,passive:!1})),s._context.addEventHandler(s),s}return m(e,t),e.prototype.dispose=function(){this._context.removeEventHandler(this),t.prototype.dispose.call(this)},e.prototype.onCursorStateChanged=function(t){return this._mouseDownOperation.onCursorStateChanged(t),!1},e.prototype.onFocusChanged=function(t){return this._isFocused=t.isFocused,!1},e.prototype.onScrollChanged=function(t){return this._mouseDownOperation.onScrollChanged(),!1},e.prototype.getTargetAtClientPoint=function(t,e){var n=new p["a"](t,e),o=n.toPageCoordinates(),i=Object(p["g"])(this.viewHelper.viewDomNode);return o.y<i.y||o.y>i.y+i.height||o.x<i.x||o.x>i.x+i.width?null:this.mouseTargetFactory.createMouseTarget(this.viewHelper.getLastRenderData(),i,o,null)},e.prototype._createMouseTarget=function(t,e){return this.mouseTargetFactory.createMouseTarget(this.viewHelper.getLastRenderData(),t.editorPos,t.pos,e?t.target:null)},e.prototype._getMouseColumn=function(t){return this.mouseTargetFactory.getMouseColumn(t.editorPos,t.pos)},e.prototype._onContextMenu=function(t,e){this.viewController.emitContextMenu({event:t,target:this._createMouseTarget(t,e)})},e.prototype._onMouseMove=function(t){if(!this._mouseDownOperation.isActive()){var e=t.timestamp;e<this.lastMouseLeaveTime||this.viewController.emitMouseMove({event:t,target:this._createMouseTarget(t,!0)})}},e.prototype._onMouseLeave=function(t){this.lastMouseLeaveTime=(new Date).getTime(),this.viewController.emitMouseLeave({event:t,target:null})},e.prototype._onMouseUp=function(t){this.viewController.emitMouseUp({event:t,target:this._createMouseTarget(t,!0)})},e.prototype._onMouseDown=function(t){var e=this,n=this._createMouseTarget(t,!0),o=6===n.type||7===n.type,r=2===n.type||3===n.type||4===n.type,s=3===n.type,u=this._context.configuration.options.get(83),l=8===n.type||5===n.type,c=9===n.type,p=t.leftButton||t.middleButton;i["e"]&&t.leftButton&&t.ctrlKey&&(p=!1);var d=function(){a["i"]&&!e._isFocused?e._asyncFocus.schedule():(t.preventDefault(),e.viewHelper.focusTextArea())};if(p&&(o||s&&u))d(),this._mouseDownOperation.start(n.type,t);else if(r)t.preventDefault();else if(l){var h=n.detail;this.viewHelper.shouldSuppressMouseDownOnViewZone(h.viewZoneId)&&(d(),this._mouseDownOperation.start(n.type,t),t.preventDefault())}else c&&this.viewHelper.shouldSuppressMouseDownOnWidget(n.detail)&&(d(),t.preventDefault());this.viewController.emitMouseDown({event:t,target:n})},e.MOUSE_MOVE_MINIMUM_TIME=100,e}(g["a"]),y=function(t){function e(e,n,o,i,r){var s=t.call(this)||this;return s._context=e,s._viewController=n,s._viewHelper=o,s._createMouseTarget=i,s._getMouseColumn=r,s._mouseMoveMonitor=s._register(new p["e"](s._viewHelper.viewDomNode)),s._onScrollTimeout=s._register(new l["e"]),s._mouseState=new C,s._currentSelection=new f["a"](1,1,1,1),s._isActive=!1,s._lastMouseEvent=null,s}return m(e,t),e.prototype.dispose=function(){t.prototype.dispose.call(this)},e.prototype.isActive=function(){return this._isActive},e.prototype._onMouseDownThenMove=function(t){this._lastMouseEvent=t,this._mouseState.setModifiers(t);var e=this._findMousePosition(t,!0);e&&(this._mouseState.isDragAndDrop?this._viewController.emitMouseDrag({event:t,target:e}):this._dispatchMouse(e,!0))},e.prototype.start=function(t,e){var n=this;this._lastMouseEvent=e,this._mouseState.setStartedOnLineNumbers(3===t),this._mouseState.setStartButtons(e),this._mouseState.setModifiers(e);var o=this._findMousePosition(e,!0);if(o&&o.position){this._mouseState.trySetCount(e.detail,o.position),e.detail=this._mouseState.count;var i=this._context.configuration.options;if(!i.get(68)&&i.get(24)&&!this._mouseState.altKey&&e.detail<2&&!this._isActive&&!this._currentSelection.isEmpty()&&6===o.type&&o.position&&this._currentSelection.containsPosition(o.position))return this._mouseState.isDragAndDrop=!0,this._isActive=!0,void this._mouseMoveMonitor.startMonitoring(e.target,e.buttons,_(null),(function(t){return n._onMouseDownThenMove(t)}),(function(){var t=n._findMousePosition(n._lastMouseEvent,!0);n._viewController.emitMouseDrop({event:n._lastMouseEvent,target:t?n._createMouseTarget(n._lastMouseEvent,!0):null}),n._stop()}));this._mouseState.isDragAndDrop=!1,this._dispatchMouse(o,e.shiftKey),this._isActive||(this._isActive=!0,this._mouseMoveMonitor.startMonitoring(e.target,e.buttons,_(null),(function(t){return n._onMouseDownThenMove(t)}),(function(){return n._stop()})))}},e.prototype._stop=function(){this._isActive=!1,this._onScrollTimeout.cancel()},e.prototype.onScrollChanged=function(){var t=this;this._isActive&&this._onScrollTimeout.setIfNotSet((function(){if(t._lastMouseEvent){var e=t._findMousePosition(t._lastMouseEvent,!1);e&&(t._mouseState.isDragAndDrop||t._dispatchMouse(e,!0))}}),10)},e.prototype.onCursorStateChanged=function(t){this._currentSelection=t.selections[0]},e.prototype._getPositionOutsideEditor=function(t){var e=t.editorPos,n=this._context.model,o=this._context.viewLayout,i=this._getMouseColumn(t);if(t.posy<e.y){var r=Math.max(o.getCurrentScrollTop()-(e.y-t.posy),0),s=c["a"].getZoneAtCoord(this._context,r);if(s){var a=this._helpPositionJumpOverViewZone(s);if(a)return new c["b"](null,13,i,a)}var u=o.getLineNumberAtVerticalOffset(r);return new c["b"](null,13,i,new h["a"](u,1))}if(t.posy>e.y+e.height){r=o.getCurrentScrollTop()+(t.posy-e.y),s=c["a"].getZoneAtCoord(this._context,r);if(s){a=this._helpPositionJumpOverViewZone(s);if(a)return new c["b"](null,13,i,a)}var l=o.getLineNumberAtVerticalOffset(r);return new c["b"](null,13,i,new h["a"](l,n.getLineMaxColumn(l)))}var p=o.getLineNumberAtVerticalOffset(o.getCurrentScrollTop()+(t.posy-e.y));return t.posx<e.x?new c["b"](null,13,i,new h["a"](p,1)):t.posx>e.x+e.width?new c["b"](null,13,i,new h["a"](p,n.getLineMaxColumn(p))):null},e.prototype._findMousePosition=function(t,e){var n=this._getPositionOutsideEditor(t);if(n)return n;var o=this._createMouseTarget(t,e),i=o.position;if(!i)return null;if(8===o.type||5===o.type){var r=this._helpPositionJumpOverViewZone(o.detail);if(r)return new c["b"](o.element,o.type,o.mouseColumn,r,null,o.detail)}return o},e.prototype._helpPositionJumpOverViewZone=function(t){var e=new h["a"](this._currentSelection.selectionStartLineNumber,this._currentSelection.selectionStartColumn),n=t.positionBefore,o=t.positionAfter;return n&&o?n.isBefore(e)?n:o:null},e.prototype._dispatchMouse=function(t,e){t.position&&this._viewController.dispatchMouse({position:t.position,mouseColumn:t.mouseColumn,startedOnLineNumbers:this._mouseState.startedOnLineNumbers,inSelectionMode:e,mouseDownCount:this._mouseState.count,altKey:this._mouseState.altKey,ctrlKey:this._mouseState.ctrlKey,metaKey:this._mouseState.metaKey,shiftKey:this._mouseState.shiftKey,leftButton:this._mouseState.leftButton,middleButton:this._mouseState.middleButton})},e}(s["a"]),C=function(){function t(){this._altKey=!1,this._ctrlKey=!1,this._metaKey=!1,this._shiftKey=!1,this._leftButton=!1,this._middleButton=!1,this._startedOnLineNumbers=!1,this._lastMouseDownPosition=null,this._lastMouseDownPositionEqualCount=0,this._lastMouseDownCount=0,this._lastSetMouseDownCountTime=0,this.isDragAndDrop=!1}return Object.defineProperty(t.prototype,"altKey",{get:function(){return this._altKey},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"ctrlKey",{get:function(){return this._ctrlKey},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"metaKey",{get:function(){return this._metaKey},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"shiftKey",{get:function(){return this._shiftKey},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"leftButton",{get:function(){return this._leftButton},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"middleButton",{get:function(){return this._middleButton},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"startedOnLineNumbers",{get:function(){return this._startedOnLineNumbers},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"count",{get:function(){return this._lastMouseDownCount},enumerable:!0,configurable:!0}),t.prototype.setModifiers=function(t){this._altKey=t.altKey,this._ctrlKey=t.ctrlKey,this._metaKey=t.metaKey,this._shiftKey=t.shiftKey},t.prototype.setStartButtons=function(t){this._leftButton=t.leftButton,this._middleButton=t.middleButton},t.prototype.setStartedOnLineNumbers=function(t){this._startedOnLineNumbers=t},t.prototype.trySetCount=function(e,n){var o=(new Date).getTime();o-this._lastSetMouseDownCountTime>t.CLEAR_MOUSE_DOWN_COUNT_TIME&&(e=1),this._lastSetMouseDownCountTime=o,e>this._lastMouseDownCount+1&&(e=this._lastMouseDownCount+1),this._lastMouseDownPosition&&this._lastMouseDownPosition.equals(n)?this._lastMouseDownPositionEqualCount++:this._lastMouseDownPositionEqualCount=1,this._lastMouseDownPosition=n,this._lastMouseDownCount=Math.min(e,this._lastMouseDownPositionEqualCount)},t.CLEAR_MOUSE_DOWN_COUNT_TIME=400,t}(),w=n("0a31"),b=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},t(e,n)};return function(e,n){function o(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}();function S(t,e){var n={translationY:e.translationY,translationX:e.translationX};return t&&(n.translationY+=t.translationY,n.translationX+=t.translationX),n}var x=function(t){function e(e,n,i){var r=t.call(this,e,n,i)||this;return r.viewHelper.linesContentDomNode.style.msTouchAction="none",r.viewHelper.linesContentDomNode.style.msContentZooming="none",r._installGestureHandlerTimeout=window.setTimeout((function(){if(r._installGestureHandlerTimeout=-1,window.MSGesture){var t=new MSGesture,e=new MSGesture;t.target=r.viewHelper.linesContentDomNode,e.target=r.viewHelper.linesContentDomNode,r.viewHelper.linesContentDomNode.addEventListener("MSPointerDown",(function(n){var o=n.pointerType;o!==(n.MSPOINTER_TYPE_MOUSE||"mouse")?o===(n.MSPOINTER_TYPE_TOUCH||"touch")?(r._lastPointerType="touch",t.addPointer(n.pointerId)):(r._lastPointerType="pen",e.addPointer(n.pointerId)):r._lastPointerType="mouse"})),r._register(o["m"](r.viewHelper.linesContentDomNode,"MSGestureChange",(function(t){return r._onGestureChange(t)}),S)),r._register(o["j"](r.viewHelper.linesContentDomNode,"MSGestureTap",(function(t){return r._onCaptureGestureTap(t)}),!0))}}),100),r._lastPointerType="mouse",r}return b(e,t),e.prototype._onMouseDown=function(e){"mouse"===this._lastPointerType&&t.prototype._onMouseDown.call(this,e)},e.prototype._onCaptureGestureTap=function(t){var e=this,n=new p["b"](t,this.viewHelper.viewDomNode),o=this._createMouseTarget(n,!1);o.position&&this.viewController.moveTo(o.position),n.browserEvent.fromElement?(n.preventDefault(),this.viewHelper.focusTextArea()):setTimeout((function(){e.viewHelper.focusTextArea()}))},e.prototype._onGestureChange=function(t){this._context.viewLayout.deltaScrollNow(-t.translationX,-t.translationY)},e.prototype.dispose=function(){window.clearTimeout(this._installGestureHandlerTimeout),t.prototype.dispose.call(this)},e}(v),T=function(t){function e(e,n,i){var r=t.call(this,e,n,i)||this;return r.viewHelper.linesContentDomNode.style.touchAction="none",r._installGestureHandlerTimeout=window.setTimeout((function(){if(r._installGestureHandlerTimeout=-1,window.MSGesture){var t=new MSGesture,e=new MSGesture;t.target=r.viewHelper.linesContentDomNode,e.target=r.viewHelper.linesContentDomNode,r.viewHelper.linesContentDomNode.addEventListener("pointerdown",(function(n){var o=n.pointerType;"mouse"!==o?"touch"===o?(r._lastPointerType="touch",t.addPointer(n.pointerId)):(r._lastPointerType="pen",e.addPointer(n.pointerId)):r._lastPointerType="mouse"})),r._register(o["m"](r.viewHelper.linesContentDomNode,"MSGestureChange",(function(t){return r._onGestureChange(t)}),S)),r._register(o["j"](r.viewHelper.linesContentDomNode,"MSGestureTap",(function(t){return r._onCaptureGestureTap(t)}),!0))}}),100),r._lastPointerType="mouse",r}return b(e,t),e.prototype._onMouseDown=function(e){"mouse"===this._lastPointerType&&t.prototype._onMouseDown.call(this,e)},e.prototype._onCaptureGestureTap=function(t){var e=this,n=new p["b"](t,this.viewHelper.viewDomNode),o=this._createMouseTarget(n,!1);o.position&&this.viewController.moveTo(o.position),n.browserEvent.fromElement?(n.preventDefault(),this.viewHelper.focusTextArea()):setTimeout((function(){e.viewHelper.focusTextArea()}))},e.prototype._onGestureChange=function(t){this._context.viewLayout.deltaScrollNow(-t.translationX,-t.translationY)},e.prototype.dispose=function(){window.clearTimeout(this._installGestureHandlerTimeout),t.prototype.dispose.call(this)},e}(v),E=function(t){function e(e,n,i){var s=t.call(this,e,n,i)||this;s._register(r["b"].addTarget(s.viewHelper.linesContentDomNode)),s._register(o["j"](s.viewHelper.linesContentDomNode,r["a"].Tap,(function(t){return s.onTap(t)}))),s._register(o["j"](s.viewHelper.linesContentDomNode,r["a"].Change,(function(t){return s.onChange(t)}))),s._register(o["j"](s.viewHelper.linesContentDomNode,r["a"].Contextmenu,(function(t){return s._onContextMenu(new p["b"](t,s.viewHelper.viewDomNode),!1)}))),s._lastPointerType="mouse",s._register(o["j"](s.viewHelper.linesContentDomNode,"pointerdown",(function(t){var e=t.pointerType;s._lastPointerType="mouse"!==e?"touch"===e?"touch":"pen":"mouse"})));var a=new p["d"](s.viewHelper.viewDomNode);return s._register(a.onPointerMoveThrottled(s.viewHelper.viewDomNode,(function(t){return s._onMouseMove(t)}),_(s.mouseTargetFactory),v.MOUSE_MOVE_MINIMUM_TIME)),s._register(a.onPointerUp(s.viewHelper.viewDomNode,(function(t){return s._onMouseUp(t)}))),s._register(a.onPointerLeave(s.viewHelper.viewDomNode,(function(t){return s._onMouseLeave(t)}))),s._register(a.onPointerDown(s.viewHelper.viewDomNode,(function(t){return s._onMouseDown(t)}))),s}return b(e,t),e.prototype.onTap=function(t){if(t.initialTarget&&this.viewHelper.linesContentDomNode.contains(t.initialTarget)){t.preventDefault(),this.viewHelper.focusTextArea();var e=this._createMouseTarget(new p["b"](t,this.viewHelper.viewDomNode),!1);e.position&&this.viewController.dispatchMouse({position:e.position,mouseColumn:e.position.column,startedOnLineNumbers:!1,mouseDownCount:t.tapCount,inSelectionMode:!1,altKey:!1,ctrlKey:!1,metaKey:!1,shiftKey:!1,leftButton:!1,middleButton:!1})}},e.prototype.onChange=function(t){"touch"===this._lastPointerType&&this._context.viewLayout.deltaScrollNow(-t.translationX,-t.translationY)},e.prototype._onMouseDown=function(e){e.target&&this.viewHelper.linesContentDomNode.contains(e.target)&&"touch"===this._lastPointerType||t.prototype._onMouseDown.call(this,e)},e}(v),O=function(t){function e(e,n,i){var s=t.call(this,e,n,i)||this;return s._register(r["b"].addTarget(s.viewHelper.linesContentDomNode)),s._register(o["j"](s.viewHelper.linesContentDomNode,r["a"].Tap,(function(t){return s.onTap(t)}))),s._register(o["j"](s.viewHelper.linesContentDomNode,r["a"].Change,(function(t){return s.onChange(t)}))),s._register(o["j"](s.viewHelper.linesContentDomNode,r["a"].Contextmenu,(function(t){return s._onContextMenu(new p["b"](t,s.viewHelper.viewDomNode),!1)}))),s}return b(e,t),e.prototype.onTap=function(t){t.preventDefault(),this.viewHelper.focusTextArea();var e=this._createMouseTarget(new p["b"](t,this.viewHelper.viewDomNode),!1);e.position&&this.viewController.moveTo(e.position)},e.prototype.onChange=function(t){this._context.viewLayout.deltaScrollNow(-t.translationX,-t.translationY)},e}(v),M=function(t){function e(e,n,o){var r=t.call(this)||this;return window.navigator.msPointerEnabled?r.handler=r._register(new x(e,n,o)):i["c"]&&w["a"].pointerEvents?r.handler=r._register(new E(e,n,o)):window.TouchEvent?r.handler=r._register(new O(e,n,o)):window.navigator.pointerEnabled||window.PointerEvent?r.handler=r._register(new T(e,n,o)):r.handler=r._register(new v(e,n,o)),r}return b(e,t),e.prototype.getTargetAtClientPoint=function(t,e){return this.handler.getTargetAtClientPoint(t,e)},e}(s["a"])},a37f:function(t,e,n){"use strict";n.d(e,"a",(function(){return s}));var o=n("a666"),i=n("11f7"),r=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},t(e,n)};return function(e,n){function o(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),s=function(t){function e(e,n,o){var i=t.call(this)||this;return i.referenceDomElement=e,i.changeCallback=o,i.width=-1,i.height=-1,i.mutationObserver=null,i.windowSizeListener=null,i.measureReferenceDomElement(!1,n),i}return r(e,t),e.prototype.dispose=function(){this.stopObserving(),t.prototype.dispose.call(this)},e.prototype.getWidth=function(){return this.width},e.prototype.getHeight=function(){return this.height},e.prototype.startObserving=function(){var t=this;!this.mutationObserver&&this.referenceDomElement&&(this.mutationObserver=new MutationObserver((function(){return t._onDidMutate()})),this.mutationObserver.observe(this.referenceDomElement,{attributes:!0})),this.windowSizeListener||(this.windowSizeListener=i["j"](window,"resize",(function(){return t._onDidResizeWindow()})))},e.prototype.stopObserving=function(){this.mutationObserver&&(this.mutationObserver.disconnect(),this.mutationObserver=null),this.windowSizeListener&&(this.windowSizeListener.dispose(),this.windowSizeListener=null)},e.prototype.observe=function(t){this.measureReferenceDomElement(!0,t)},e.prototype._onDidMutate=function(){this.measureReferenceDomElement(!0)},e.prototype._onDidResizeWindow=function(){this.measureReferenceDomElement(!0)},e.prototype.measureReferenceDomElement=function(t,e){var n=0,o=0;e?(n=e.width,o=e.height):this.referenceDomElement&&(n=this.referenceDomElement.clientWidth,o=this.referenceDomElement.clientHeight),n=Math.max(5,n),o=Math.max(5,o),this.width===n&&this.height===o||(this.width=n,this.height=o,t&&this.changeCallback())},e}(o["a"])},bae1:function(t,e,n){"use strict";n.d(e,"d",(function(){return d})),n.d(e,"b",(function(){return h})),n.d(e,"a",(function(){return g})),n.d(e,"c",(function(){return C}));var o=n("0f70"),i=n("411b"),r=n("4247"),s=n("6ec9"),a=n("7061"),u=n("6a89"),l=n("2e5d"),c=n("11f7"),p=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},t(e,n)};return function(e,n){function o(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),d=function(){function t(t,e){this.lastViewCursorsRenderData=t,this.lastTextareaPosition=e}return t}(),h=function(){function t(t,e,n,o,i,r){void 0===n&&(n=0),void 0===o&&(o=null),void 0===i&&(i=null),void 0===r&&(r=null),this.element=t,this.type=e,this.mouseColumn=n,this.position=o,!i&&o&&(i=new u["a"](o.lineNumber,o.column,o.lineNumber,o.column)),this.range=i,this.detail=r}return t._typeToString=function(t){return 1===t?"TEXTAREA":2===t?"GUTTER_GLYPH_MARGIN":3===t?"GUTTER_LINE_NUMBERS":4===t?"GUTTER_LINE_DECORATIONS":5===t?"GUTTER_VIEW_ZONE":6===t?"CONTENT_TEXT":7===t?"CONTENT_EMPTY":8===t?"CONTENT_VIEW_ZONE":9===t?"CONTENT_WIDGET":10===t?"OVERVIEW_RULER":11===t?"SCROLLBAR":12===t?"OVERLAY_WIDGET":"UNKNOWN"},t.toString=function(t){return this._typeToString(t.type)+": "+t.position+" - "+t.range+" - "+t.detail},t.prototype.toString=function(){return t.toString(this)},t}(),f=function(){function t(){}return t.isTextArea=function(t){return 2===t.length&&3===t[0]&&6===t[1]},t.isChildOfViewLines=function(t){return t.length>=4&&3===t[0]&&7===t[3]},t.isStrictChildOfViewLines=function(t){return t.length>4&&3===t[0]&&7===t[3]},t.isChildOfScrollableElement=function(t){return t.length>=2&&3===t[0]&&5===t[1]},t.isChildOfMinimap=function(t){return t.length>=2&&3===t[0]&&8===t[1]},t.isChildOfContentWidgets=function(t){return t.length>=4&&3===t[0]&&1===t[3]},t.isChildOfOverflowingContentWidgets=function(t){return t.length>=1&&2===t[0]},t.isChildOfOverlayWidgets=function(t){return t.length>=2&&3===t[0]&&4===t[1]},t}(),g=function(){function t(t,e,n){this.model=t.model;var o=t.configuration.options;this.layoutInfo=o.get(107),this.viewDomNode=e.viewDomNode,this.lineHeight=o.get(49),this.typicalHalfwidthCharacterWidth=o.get(34).typicalHalfwidthCharacterWidth,this.lastRenderData=n,this._context=t,this._viewHelper=e}return t.prototype.getZoneAtCoord=function(e){return t.getZoneAtCoord(this._context,e)},t.getZoneAtCoord=function(t,e){var n=t.viewLayout.getWhitespaceAtVerticalOffset(e);if(n){var o=n.verticalOffset+n.height/2,i=t.model.getLineCount(),r=null,s=void 0,u=null;return n.afterLineNumber!==i&&(u=new a["a"](n.afterLineNumber+1,1)),n.afterLineNumber>0&&(r=new a["a"](n.afterLineNumber,t.model.getLineMaxColumn(n.afterLineNumber))),s=null===u?r:null===r?u:e<o?r:u,{viewZoneId:n.id,afterLineNumber:n.afterLineNumber,positionBefore:r,positionAfter:u,position:s}}return null},t.prototype.getFullLineRangeAtCoord=function(t){if(this._context.viewLayout.isAfterLines(t)){var e=this._context.model.getLineCount(),n=this._context.model.getLineMaxColumn(e);return{range:new u["a"](e,n,e,n),isAfterLines:!0}}var o=this._context.viewLayout.getLineNumberAtVerticalOffset(t),i=this._context.model.getLineMaxColumn(o);return{range:new u["a"](o,1,o,i),isAfterLines:!1}},t.prototype.getLineNumberAtVerticalOffset=function(t){return this._context.viewLayout.getLineNumberAtVerticalOffset(t)},t.prototype.isAfterLines=function(t){return this._context.viewLayout.isAfterLines(t)},t.prototype.getVerticalOffsetForLineNumber=function(t){return this._context.viewLayout.getVerticalOffsetForLineNumber(t)},t.prototype.findAttribute=function(e,n){return t._findAttribute(e,n,this._viewHelper.viewDomNode)},t._findAttribute=function(t,e,n){while(t&&t!==document.body){if(t.hasAttribute&&t.hasAttribute(e))return t.getAttribute(e);if(t===n)return null;t=t.parentNode}return null},t.prototype.getLineWidth=function(t){return this._viewHelper.getLineWidth(t)},t.prototype.visibleRangeForPosition=function(t,e){return this._viewHelper.visibleRangeForPosition(t,e)},t.prototype.getPositionFromDOMInfo=function(t,e){return this._viewHelper.getPositionFromDOMInfo(t,e)},t.prototype.getCurrentScrollTop=function(){return this._context.viewLayout.getCurrentScrollTop()},t.prototype.getCurrentScrollLeft=function(){return this._context.viewLayout.getCurrentScrollLeft()},t}(),m=function(){function t(t,e,n){this.editorPos=e,this.pos=n,this.mouseVerticalOffset=Math.max(0,t.getCurrentScrollTop()+n.y-e.y),this.mouseContentHorizontalOffset=t.getCurrentScrollLeft()+n.x-e.x-t.layoutInfo.contentLeft,this.isInMarginArea=n.x-e.x<t.layoutInfo.contentLeft&&n.x-e.x>=t.layoutInfo.glyphMarginLeft,this.isInContentArea=!this.isInMarginArea,this.mouseColumn=Math.max(0,C._getMouseColumn(this.mouseContentHorizontalOffset,t.typicalHalfwidthCharacterWidth))}return t}(),_=function(t){function e(e,n,o,i){var s=t.call(this,e,n,o)||this;return s._ctx=e,i?(s.target=i,s.targetPath=r["a"].collect(i,e.viewDomNode)):(s.target=null,s.targetPath=new Uint8Array(0)),s}return p(e,t),e.prototype.toString=function(){return"pos("+this.pos.x+","+this.pos.y+"), editorPos("+this.editorPos.x+","+this.editorPos.y+"), mouseVerticalOffset: "+this.mouseVerticalOffset+", mouseContentHorizontalOffset: "+this.mouseContentHorizontalOffset+"\n\ttarget: "+(this.target?this.target.outerHTML:null)},e.prototype.fulfill=function(t,e,n,o){void 0===e&&(e=null),void 0===n&&(n=null),void 0===o&&(o=null);var i=this.mouseColumn;return e&&e.column<this._ctx.model.getLineMaxColumn(e.lineNumber)&&(i=l["a"].visibleColumnFromColumn(this._ctx.model.getLineContent(e.lineNumber),e.column,this._ctx.model.getOptions().tabSize)+1),new h(this.target,t,i,e,n,o)},e.prototype.withTarget=function(t){return new e(this._ctx,this.editorPos,this.pos,t)},e}(m),v={isAfterLines:!0};function y(t){return{isAfterLines:!1,horizontalDistanceToText:t}}var C=function(){function t(t,e){this._context=t,this._viewHelper=e}return t.prototype.mouseTargetIsWidget=function(t){var e=t.target,n=r["a"].collect(e,this._viewHelper.viewDomNode);return!(!f.isChildOfContentWidgets(n)&&!f.isChildOfOverflowingContentWidgets(n))||!!f.isChildOfOverlayWidgets(n)},t.prototype.createMouseTarget=function(e,n,o,i){var r=new g(this._context,this._viewHelper,e),s=new _(r,n,o,i);try{var a=t._createMouseTarget(r,s,!1);return a}catch(u){return s.fulfill(0)}},t._createMouseTarget=function(e,n,o){if(null===n.target){if(o)return n.fulfill(0);var i=t._doHitTest(e,n);return i.position?t.createMouseTargetFromHitTestPosition(e,n,i.position.lineNumber,i.position.column):this._createMouseTarget(e,n.withTarget(i.hitTarget),!0)}var r=n,s=null;return s=s||t._hitTestContentWidget(e,r),s=s||t._hitTestOverlayWidget(e,r),s=s||t._hitTestMinimap(e,r),s=s||t._hitTestScrollbarSlider(e,r),s=s||t._hitTestViewZone(e,r),s=s||t._hitTestMargin(e,r),s=s||t._hitTestViewCursor(e,r),s=s||t._hitTestTextArea(e,r),s=s||t._hitTestViewLines(e,r,o),s=s||t._hitTestScrollbar(e,r),s||n.fulfill(0)},t._hitTestContentWidget=function(t,e){if(f.isChildOfContentWidgets(e.targetPath)||f.isChildOfOverflowingContentWidgets(e.targetPath)){var n=t.findAttribute(e.target,"widgetId");return n?e.fulfill(9,null,null,n):e.fulfill(0)}return null},t._hitTestOverlayWidget=function(t,e){if(f.isChildOfOverlayWidgets(e.targetPath)){var n=t.findAttribute(e.target,"widgetId");return n?e.fulfill(12,null,null,n):e.fulfill(0)}return null},t._hitTestViewCursor=function(t,e){if(e.target)for(var n=t.lastRenderData.lastViewCursorsRenderData,o=0,i=n;o<i.length;o++){var r=i[o];if(e.target===r.domNode)return e.fulfill(6,r.position)}if(e.isInContentArea){n=t.lastRenderData.lastViewCursorsRenderData;for(var s=e.mouseContentHorizontalOffset,a=e.mouseVerticalOffset,u=0,l=n;u<l.length;u++){r=l[u];if(!(s<r.contentLeft)&&!(s>r.contentLeft+r.width)){var c=t.getVerticalOffsetForLineNumber(r.position.lineNumber);if(c<=a&&a<=c+r.height)return e.fulfill(6,r.position)}}}return null},t._hitTestViewZone=function(t,e){var n=t.getZoneAtCoord(e.mouseVerticalOffset);if(n){var o=e.isInContentArea?8:5;return e.fulfill(o,n.position,null,n)}return null},t._hitTestTextArea=function(t,e){return f.isTextArea(e.targetPath)?t.lastRenderData.lastTextareaPosition?e.fulfill(6,t.lastRenderData.lastTextareaPosition):e.fulfill(1,t.lastRenderData.lastTextareaPosition):null},t._hitTestMargin=function(t,e){if(e.isInMarginArea){var n=t.getFullLineRangeAtCoord(e.mouseVerticalOffset),o=n.range.getStartPosition(),i=Math.abs(e.pos.x-e.editorPos.x),r={isAfterLines:n.isAfterLines,glyphMarginLeft:t.layoutInfo.glyphMarginLeft,glyphMarginWidth:t.layoutInfo.glyphMarginWidth,lineNumbersWidth:t.layoutInfo.lineNumbersWidth,offsetX:i};return i-=t.layoutInfo.glyphMarginLeft,i<=t.layoutInfo.glyphMarginWidth?e.fulfill(2,o,n.range,r):(i-=t.layoutInfo.glyphMarginWidth,i<=t.layoutInfo.lineNumbersWidth?e.fulfill(3,o,n.range,r):(i-=t.layoutInfo.lineNumbersWidth,e.fulfill(4,o,n.range,r)))}return null},t._hitTestViewLines=function(e,n,o){if(!f.isChildOfViewLines(n.targetPath))return null;if(e.isAfterLines(n.mouseVerticalOffset)){var i=e.model.getLineCount(),r=e.model.getLineMaxColumn(i);return n.fulfill(7,new a["a"](i,r),void 0,v)}if(o){if(f.isStrictChildOfViewLines(n.targetPath)){var s=e.getLineNumberAtVerticalOffset(n.mouseVerticalOffset);if(0===e.model.getLineLength(s)){var u=e.getLineWidth(s),l=y(n.mouseContentHorizontalOffset-u);return n.fulfill(7,new a["a"](s,1),void 0,l)}var c=e.getLineWidth(s);if(n.mouseContentHorizontalOffset>=c){l=y(n.mouseContentHorizontalOffset-c);var p=new a["a"](s,e.model.getLineMaxColumn(s));return n.fulfill(7,p,void 0,l)}}return n.fulfill(0)}var d=t._doHitTest(e,n);return d.position?t.createMouseTargetFromHitTestPosition(e,n,d.position.lineNumber,d.position.column):this._createMouseTarget(e,n.withTarget(d.hitTarget),!0)},t._hitTestMinimap=function(t,e){if(f.isChildOfMinimap(e.targetPath)){var n=t.getLineNumberAtVerticalOffset(e.mouseVerticalOffset),o=t.model.getLineMaxColumn(n);return e.fulfill(11,new a["a"](n,o))}return null},t._hitTestScrollbarSlider=function(t,e){if(f.isChildOfScrollableElement(e.targetPath)&&e.target&&1===e.target.nodeType){var n=e.target.className;if(n&&/\b(slider|scrollbar)\b/.test(n)){var o=t.getLineNumberAtVerticalOffset(e.mouseVerticalOffset),i=t.model.getLineMaxColumn(o);return e.fulfill(11,new a["a"](o,i))}}return null},t._hitTestScrollbar=function(t,e){if(f.isChildOfScrollableElement(e.targetPath)){var n=t.getLineNumberAtVerticalOffset(e.mouseVerticalOffset),o=t.model.getLineMaxColumn(n);return e.fulfill(11,new a["a"](n,o))}return null},t.prototype.getMouseColumn=function(e,n){var o=this._context.configuration.options,i=o.get(107),r=this._context.viewLayout.getCurrentScrollLeft()+n.x-e.x-i.contentLeft;return t._getMouseColumn(r,o.get(34).typicalHalfwidthCharacterWidth)},t._getMouseColumn=function(t,e){if(t<0)return 1;var n=Math.round(t/e);return n+1},t.createMouseTargetFromHitTestPosition=function(t,e,n,i){var r=new a["a"](n,i),s=t.getLineWidth(n);if(e.mouseContentHorizontalOffset>s){if(o["e"]&&1===r.column){var l=y(e.mouseContentHorizontalOffset-s);return e.fulfill(7,new a["a"](n,t.model.getLineMaxColumn(n)),void 0,l)}var c=y(e.mouseContentHorizontalOffset-s);return e.fulfill(7,r,void 0,c)}var p=t.visibleRangeForPosition(n,i);if(!p)return e.fulfill(0,r);var d=p.left;if(e.mouseContentHorizontalOffset===d)return e.fulfill(6,r);var h=[];if(h.push({offset:p.left,column:i}),i>1){var f=t.visibleRangeForPosition(n,i-1);f&&h.push({offset:f.left,column:i-1})}var g=t.model.getLineMaxColumn(n);if(i<g){var m=t.visibleRangeForPosition(n,i+1);m&&h.push({offset:m.left,column:i+1})}h.sort((function(t,e){return t.offset-e.offset}));for(var _=1;_<h.length;_++){var v=h[_-1],C=h[_];if(v.offset<=e.mouseContentHorizontalOffset&&e.mouseContentHorizontalOffset<=C.offset){var w=new u["a"](n,v.column,n,C.column);return e.fulfill(6,r,w)}}return e.fulfill(6,r)},t._doHitTestWithCaretRangeFromPoint=function(t,e){var n=t.getLineNumberAtVerticalOffset(e.mouseVerticalOffset),o=t.getVerticalOffsetForLineNumber(n),r=o+Math.floor(t.lineHeight/2),s=e.pos.y+(r-e.mouseVerticalOffset);s<=e.editorPos.y&&(s=e.editorPos.y+1),s>=e.editorPos.y+t.layoutInfo.height&&(s=e.editorPos.y+t.layoutInfo.height-1);var a=new i["f"](e.pos.x,s),u=this._actualDoHitTestWithCaretRangeFromPoint(t,a.toClientCoordinates());return u.position?u:this._actualDoHitTestWithCaretRangeFromPoint(t,e.pos.toClientCoordinates())},t._actualDoHitTestWithCaretRangeFromPoint=function(t,e){var n,o=c["E"](t.viewDomNode);if(n=o?"undefined"===typeof o.caretRangeFromPoint?w(o,e.clientX,e.clientY):o.caretRangeFromPoint(e.clientX,e.clientY):document.caretRangeFromPoint(e.clientX,e.clientY),!n||!n.startContainer)return{position:null,hitTarget:null};var i=n.startContainer,r=null;if(i.nodeType===i.TEXT_NODE){var a=i.parentNode,u=a?a.parentNode:null,l=u?u.parentNode:null,p=l&&l.nodeType===l.ELEMENT_NODE?l.className:null;if(p===s["b"].CLASS_NAME){var d=t.getPositionFromDOMInfo(a,n.startOffset);return{position:d,hitTarget:null}}r=i.parentNode}else if(i.nodeType===i.ELEMENT_NODE){a=i.parentNode,u=a?a.parentNode:null;var h=u&&u.nodeType===u.ELEMENT_NODE?u.className:null;if(h===s["b"].CLASS_NAME){d=t.getPositionFromDOMInfo(i,i.textContent.length);return{position:d,hitTarget:null}}r=i}return{position:null,hitTarget:r}},t._doHitTestWithCaretPositionFromPoint=function(t,e){var n=document.caretPositionFromPoint(e.clientX,e.clientY);if(n.offsetNode.nodeType===n.offsetNode.TEXT_NODE){var o=n.offsetNode.parentNode,i=o?o.parentNode:null,r=i?i.parentNode:null,a=r&&r.nodeType===r.ELEMENT_NODE?r.className:null;if(a===s["b"].CLASS_NAME){var u=t.getPositionFromDOMInfo(n.offsetNode.parentNode,n.offset);return{position:u,hitTarget:null}}return{position:null,hitTarget:n.offsetNode.parentNode}}return{position:null,hitTarget:n.offsetNode}},t._doHitTestWithMoveToPoint=function(t,e){var n=null,o=null,i=document.body.createTextRange();try{i.moveToPoint(e.clientX,e.clientY)}catch(p){return{position:null,hitTarget:null}}i.collapse(!0);var r=i?i.parentElement():null,a=r?r.parentNode:null,u=a?a.parentNode:null,l=u&&u.nodeType===u.ELEMENT_NODE?u.className:"";if(l===s["b"].CLASS_NAME){var c=i.duplicate();c.moveToElementText(r),c.setEndPoint("EndToStart",i),n=t.getPositionFromDOMInfo(r,c.text.length),c.moveToElementText(t.viewDomNode)}else o=r;return i.moveToElementText(t.viewDomNode),{position:n,hitTarget:o}},t._doHitTest=function(t,e){return"function"===typeof document.caretRangeFromPoint?this._doHitTestWithCaretRangeFromPoint(t,e):document.caretPositionFromPoint?this._doHitTestWithCaretPositionFromPoint(t,e.pos.toClientCoordinates()):document.body.createTextRange?this._doHitTestWithMoveToPoint(t,e.pos.toClientCoordinates()):{position:null,hitTarget:null}},t}();function w(t,e,n){var o=document.createRange(),i=t.elementFromPoint(e,n);if(null!==i){while(i&&i.firstChild&&i.firstChild.nodeType!==i.firstChild.TEXT_NODE)i=i.lastChild;var r=i.getBoundingClientRect(),s=window.getComputedStyle(i,null).getPropertyValue("font"),a=i.innerText,u=r.left,l=0,c=void 0;if(e>r.left+r.width)l=a.length;else for(var p=b.getInstance(),d=0;d<a.length+1;d++){if(c=p.getCharWidth(a.charAt(d),s)/2,u+=c,e<u){l=d;break}u+=c}o.setStart(i.firstChild,l),o.setEnd(i.firstChild,l)}return o}var b=function(){function t(){this._cache={},this._canvas=document.createElement("canvas")}return t.getInstance=function(){return t._INSTANCE||(t._INSTANCE=new t),t._INSTANCE},t.prototype.getCharWidth=function(t,e){var n=t+e;if(this._cache[n])return this._cache[n];var o=this._canvas.getContext("2d");o.font=e;var i=o.measureText(t),r=i.width;return this._cache[n]=r,r},t._INSTANCE=null,t}()},bc04:function(t,e,n){"use strict";n.d(e,"a",(function(){return m})),n.d(e,"b",(function(){return _})),n.d(e,"d",(function(){return v})),n.d(e,"c",(function(){return y}));var o=n("3742"),i=n("2504"),r=n("a666"),s=n("b2cc"),a=n("4fc3"),u=n("db88"),l=n("0a0f"),c=n("f5f3"),p=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},t(e,n)};return function(e,n){function o(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),d=Object(l["c"])("IEditorCancelService"),h=new a["d"]("cancellableOperation",!1);Object(c["b"])(d,function(){function t(){this._tokens=new WeakMap}return t.prototype.add=function(t,e){var n,o=this._tokens.get(t);return o||(o=t.invokeWithinContext((function(t){var e=h.bindTo(t.get(a["c"])),n=new u["a"];return{key:e,tokens:n}})),this._tokens.set(t,o)),o.key.set(!0),n=o.tokens.push(e),function(){n&&(n(),o.key.set(!o.tokens.isEmpty()),n=void 0)}},t.prototype.cancel=function(t){var e=this._tokens.get(t);if(e){var n=e.tokens.pop();n&&(n.cancel(),e.key.set(!e.tokens.isEmpty()))}},t}(),!0);var f=function(t){function e(e,n){var o=t.call(this,n)||this;return o.editor=e,o._unregister=e.invokeWithinContext((function(t){return t.get(d).add(e,o)})),o}return p(e,t),e.prototype.dispose=function(){this._unregister(),t.prototype.dispose.call(this)},e}(i["b"]);Object(s["g"])(new(function(t){function e(){return t.call(this,{id:"editor.cancelOperation",kbOpts:{weight:100,primary:9},precondition:h})||this}return p(e,t),e.prototype.runEditorCommand=function(t,e){t.get(d).cancel(e)},e}(s["c"])));var g=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},t(e,n)};return function(e,n){function o(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),m=function(){function t(t,e){if(this.flags=e,0!==(1&this.flags)){var n=t.getModel();this.modelVersionId=n?o["r"]("{0}#{1}",n.uri.toString(),n.getVersionId()):null}else this.modelVersionId=null;0!==(4&this.flags)?this.position=t.getPosition():this.position=null,0!==(2&this.flags)?this.selection=t.getSelection():this.selection=null,0!==(8&this.flags)?(this.scrollLeft=t.getScrollLeft(),this.scrollTop=t.getScrollTop()):(this.scrollLeft=-1,this.scrollTop=-1)}return t.prototype._equals=function(e){if(!(e instanceof t))return!1;var n=e;return this.modelVersionId===n.modelVersionId&&(this.scrollLeft===n.scrollLeft&&this.scrollTop===n.scrollTop&&(!(!this.position&&n.position||this.position&&!n.position||this.position&&n.position&&!this.position.equals(n.position))&&!(!this.selection&&n.selection||this.selection&&!n.selection||this.selection&&n.selection&&!this.selection.equalsRange(n.selection))))},t.prototype.validate=function(e){return this._equals(new t(e,this.flags))},t}(),_=function(t){function e(e,n,o){var i=t.call(this,e,o)||this;return i.editor=e,i._listener=new r["b"],4&n&&i._listener.add(e.onDidChangeCursorPosition((function(t){return i.cancel()}))),2&n&&i._listener.add(e.onDidChangeCursorSelection((function(t){return i.cancel()}))),8&n&&i._listener.add(e.onDidScrollChange((function(t){return i.cancel()}))),1&n&&(i._listener.add(e.onDidChangeModel((function(t){return i.cancel()}))),i._listener.add(e.onDidChangeModelContent((function(t){return i.cancel()})))),i}return g(e,t),e.prototype.dispose=function(){this._listener.dispose(),t.prototype.dispose.call(this)},e}(f),v=function(t){function e(e,n){var o=t.call(this,n)||this;return o._listener=e.onDidChangeContent((function(){return o.cancel()})),o}return g(e,t),e.prototype.dispose=function(){this._listener.dispose(),t.prototype.dispose.call(this)},e}(i["b"]),y=function(){function t(t,e){this._visiblePosition=t,this._visiblePositionScrollDelta=e}return t.capture=function(e){var n=null,o=0;if(0!==e.getScrollTop()){var i=e.getVisibleRanges();if(i.length>0){n=i[0].getStartPosition();var r=e.getTopForPosition(n.lineNumber,n.column);o=e.getScrollTop()-r}}return new t(n,o)},t.prototype.restore=function(t){if(this._visiblePosition){var e=t.getTopForPosition(this._visiblePosition.lineNumber,this._visiblePosition.column);t.setScrollTop(e+this._visiblePositionScrollDelta)}},t}()},d585:function(t,e,n){"use strict";n.r(e),n.d(e,"CoreEditorCommand",(function(){return S})),n.d(e,"EditorScroll_",(function(){return o})),n.d(e,"RevealLine_",(function(){return i})),n.d(e,"CoreNavigationCommands",(function(){return r})),n.d(e,"CoreEditingCommands",(function(){return s}));var o,i,r,s,a=n("dff7"),u=n("ef8e"),l=n("b2cc"),c=n("5717"),p=n("fd91"),d=n("2e5d"),h=n("b272"),f=n("a007"),g=n("191f"),m=n("7061"),_=n("6a89"),v=n("8ae8"),y=n("c101"),C=n("4fc3"),w=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},t(e,n)};return function(e,n){function o(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),b=0,S=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return w(e,t),e.prototype.runEditorCommand=function(t,e,n){var o=e._getCursors();o&&this.runCoreEditorCommand(o,n||{})},e}(l["c"]);function x(t){t.register()}(function(t){var e=function(t){if(!u["i"](t))return!1;var e=t;return!!u["j"](e.to)&&(!(!u["k"](e.by)&&!u["j"](e.by))&&(!(!u["k"](e.value)&&!u["h"](e.value))&&!(!u["k"](e.revealCursor)&&!u["e"](e.revealCursor))))};function n(e){var n,o;switch(e.to){case t.RawDirection.Up:n=1;break;case t.RawDirection.Down:n=2;break;default:return null}switch(e.by){case t.RawUnit.Line:o=1;break;case t.RawUnit.WrappedLine:o=2;break;case t.RawUnit.Page:o=3;break;case t.RawUnit.HalfPage:o=4;break;default:o=2}var i=Math.floor(e.value||1),r=!!e.revealCursor;return{direction:n,unit:o,value:i,revealCursor:r,select:!!e.select}}t.description={description:"Scroll editor in the given direction",args:[{name:"Editor scroll argument object",description:"Property-value pairs that can be passed through this argument:\n\t\t\t\t\t* 'to': A mandatory direction value.\n\t\t\t\t\t\t```\n\t\t\t\t\t\t'up', 'down'\n\t\t\t\t\t\t```\n\t\t\t\t\t* 'by': Unit to move. Default is computed based on 'to' value.\n\t\t\t\t\t\t```\n\t\t\t\t\t\t'line', 'wrappedLine', 'page', 'halfPage'\n\t\t\t\t\t\t```\n\t\t\t\t\t* 'value': Number of units to move. Default is '1'.\n\t\t\t\t\t* 'revealCursor': If 'true' reveals the cursor if it is outside view port.\n\t\t\t\t",constraint:e,schema:{type:"object",required:["to"],properties:{to:{type:"string",enum:["up","down"]},by:{type:"string",enum:["line","wrappedLine","page","halfPage"]},value:{type:"number",default:1},revealCursor:{type:"boolean"}}}}]},t.RawDirection={Up:"up",Down:"down"},t.RawUnit={Line:"line",WrappedLine:"wrappedLine",Page:"page",HalfPage:"halfPage"},t.parse=n})(o||(o={})),function(t){var e=function(t){if(!u["i"](t))return!1;var e=t;return!!u["h"](e.lineNumber)&&!(!u["k"](e.at)&&!u["j"](e.at))};t.description={description:"Reveal the given line at the given logical position",args:[{name:"Reveal line argument object",description:"Property-value pairs that can be passed through this argument:\n\t\t\t\t\t* 'lineNumber': A mandatory line number value.\n\t\t\t\t\t* 'at': Logical position at which line has to be revealed .\n\t\t\t\t\t\t```\n\t\t\t\t\t\t'top', 'center', 'bottom'\n\t\t\t\t\t\t```\n\t\t\t\t",constraint:e,schema:{type:"object",required:["lineNumber"],properties:{lineNumber:{type:"number"},at:{type:"string",enum:["top","center","bottom"]}}}}]},t.RawAtArgument={Top:"top",Center:"center",Bottom:"bottom"}}(i||(i={})),function(t){var e=function(t){function e(e){var n=t.call(this,e)||this;return n._inSelectionMode=e.inSelectionMode,n}return w(e,t),e.prototype.runCoreEditorCommand=function(t,e){t.context.model.pushStackElement(),t.setStates(e.source,3,[f["b"].moveTo(t.context,t.getPrimaryCursor(),this._inSelectionMode,e.position,e.viewPosition)]),t.reveal(e.source,!0,0,0)},e}(S);t.MoveTo=Object(l["g"])(new e({id:"_moveTo",inSelectionMode:!1,precondition:void 0})),t.MoveToSelect=Object(l["g"])(new e({id:"_moveToSelect",inSelectionMode:!0,precondition:void 0}));var n=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return w(e,t),e.prototype.runCoreEditorCommand=function(t,e){t.context.model.pushStackElement();var n=this._getColumnSelectResult(t.context,t.getPrimaryCursor(),t.getColumnSelectData(),e);t.setStates(e.source,3,n.viewStates.map((function(t){return d["d"].fromViewState(t)}))),t.setColumnSelectData({isReal:!0,fromViewLineNumber:n.fromLineNumber,fromViewVisualColumn:n.fromVisualColumn,toViewLineNumber:n.toLineNumber,toViewVisualColumn:n.toVisualColumn}),t.reveal(e.source,!0,n.reversed?1:2,0)},e}(S);t.ColumnSelect=Object(l["g"])(new(function(t){function e(){return t.call(this,{id:"columnSelect",precondition:void 0})||this}return w(e,t),e.prototype._getColumnSelectResult=function(t,e,n,o){var i=t.model.validatePosition(o.position),r=t.validateViewPosition(new m["a"](o.viewPosition.lineNumber,o.viewPosition.column),i),s=o.doColumnSelect?n.fromViewLineNumber:r.lineNumber,a=o.doColumnSelect?n.fromViewVisualColumn:o.mouseColumn-1;return p["a"].columnSelect(t.config,t.viewModel,s,a,r.lineNumber,o.mouseColumn-1)},e}(n))),t.CursorColumnSelectLeft=Object(l["g"])(new(function(t){function e(){return t.call(this,{id:"cursorColumnSelectLeft",precondition:void 0,kbOpts:{weight:b,kbExpr:y["a"].textInputFocus,primary:3599,linux:{primary:0}}})||this}return w(e,t),e.prototype._getColumnSelectResult=function(t,e,n,o){return p["a"].columnSelectLeft(t.config,t.viewModel,n)},e}(n))),t.CursorColumnSelectRight=Object(l["g"])(new(function(t){function e(){return t.call(this,{id:"cursorColumnSelectRight",precondition:void 0,kbOpts:{weight:b,kbExpr:y["a"].textInputFocus,primary:3601,linux:{primary:0}}})||this}return w(e,t),e.prototype._getColumnSelectResult=function(t,e,n,o){return p["a"].columnSelectRight(t.config,t.viewModel,n)},e}(n)));var r=function(t){function e(e){var n=t.call(this,e)||this;return n._isPaged=e.isPaged,n}return w(e,t),e.prototype._getColumnSelectResult=function(t,e,n,o){return p["a"].columnSelectUp(t.config,t.viewModel,n,this._isPaged)},e}(n);t.CursorColumnSelectUp=Object(l["g"])(new r({isPaged:!1,id:"cursorColumnSelectUp",precondition:void 0,kbOpts:{weight:b,kbExpr:y["a"].textInputFocus,primary:3600,linux:{primary:0}}})),t.CursorColumnSelectPageUp=Object(l["g"])(new r({isPaged:!0,id:"cursorColumnSelectPageUp",precondition:void 0,kbOpts:{weight:b,kbExpr:y["a"].textInputFocus,primary:3595,linux:{primary:0}}}));var s=function(t){function e(e){var n=t.call(this,e)||this;return n._isPaged=e.isPaged,n}return w(e,t),e.prototype._getColumnSelectResult=function(t,e,n,o){return p["a"].columnSelectDown(t.config,t.viewModel,n,this._isPaged)},e}(n);t.CursorColumnSelectDown=Object(l["g"])(new s({isPaged:!1,id:"cursorColumnSelectDown",precondition:void 0,kbOpts:{weight:b,kbExpr:y["a"].textInputFocus,primary:3602,linux:{primary:0}}})),t.CursorColumnSelectPageDown=Object(l["g"])(new s({isPaged:!0,id:"cursorColumnSelectPageDown",precondition:void 0,kbOpts:{weight:b,kbExpr:y["a"].textInputFocus,primary:3596,linux:{primary:0}}}));var a=function(t){function e(){return t.call(this,{id:"cursorMove",precondition:void 0,description:f["a"].description})||this}return w(e,t),e.prototype.runCoreEditorCommand=function(t,e){var n=f["a"].parse(e);n&&this._runCursorMove(t,e.source,n)},e.prototype._runCursorMove=function(t,e,n){t.context.model.pushStackElement(),t.setStates(e,3,f["b"].move(t.context,t.getAll(),n)),t.reveal(e,!0,0,0)},e}(S);t.CursorMoveImpl=a,t.CursorMove=Object(l["g"])(new a);var u=function(e){function n(t){var n=e.call(this,t)||this;return n._staticArgs=t.args,n}return w(n,e),n.prototype.runCoreEditorCommand=function(e,n){var o=this._staticArgs;-1===this._staticArgs.value&&(o={direction:this._staticArgs.direction,unit:this._staticArgs.unit,select:this._staticArgs.select,value:e.context.config.pageSize}),t.CursorMove._runCursorMove(e,n.source,o)},n}(S);t.CursorLeft=Object(l["g"])(new u({args:{direction:0,unit:0,select:!1,value:1},id:"cursorLeft",precondition:void 0,kbOpts:{weight:b,kbExpr:y["a"].textInputFocus,primary:15,mac:{primary:15,secondary:[288]}}})),t.CursorLeftSelect=Object(l["g"])(new u({args:{direction:0,unit:0,select:!0,value:1},id:"cursorLeftSelect",precondition:void 0,kbOpts:{weight:b,kbExpr:y["a"].textInputFocus,primary:1039}})),t.CursorRight=Object(l["g"])(new u({args:{direction:1,unit:0,select:!1,value:1},id:"cursorRight",precondition:void 0,kbOpts:{weight:b,kbExpr:y["a"].textInputFocus,primary:17,mac:{primary:17,secondary:[292]}}})),t.CursorRightSelect=Object(l["g"])(new u({args:{direction:1,unit:0,select:!0,value:1},id:"cursorRightSelect",precondition:void 0,kbOpts:{weight:b,kbExpr:y["a"].textInputFocus,primary:1041}})),t.CursorUp=Object(l["g"])(new u({args:{direction:2,unit:2,select:!1,value:1},id:"cursorUp",precondition:void 0,kbOpts:{weight:b,kbExpr:y["a"].textInputFocus,primary:16,mac:{primary:16,secondary:[302]}}})),t.CursorUpSelect=Object(l["g"])(new u({args:{direction:2,unit:2,select:!0,value:1},id:"cursorUpSelect",precondition:void 0,kbOpts:{weight:b,kbExpr:y["a"].textInputFocus,primary:1040,secondary:[3088],mac:{primary:1040},linux:{primary:1040}}})),t.CursorPageUp=Object(l["g"])(new u({args:{direction:2,unit:2,select:!1,value:-1},id:"cursorPageUp",precondition:void 0,kbOpts:{weight:b,kbExpr:y["a"].textInputFocus,primary:11}})),t.CursorPageUpSelect=Object(l["g"])(new u({args:{direction:2,unit:2,select:!0,value:-1},id:"cursorPageUpSelect",precondition:void 0,kbOpts:{weight:b,kbExpr:y["a"].textInputFocus,primary:1035}})),t.CursorDown=Object(l["g"])(new u({args:{direction:3,unit:2,select:!1,value:1},id:"cursorDown",precondition:void 0,kbOpts:{weight:b,kbExpr:y["a"].textInputFocus,primary:18,mac:{primary:18,secondary:[300]}}})),t.CursorDownSelect=Object(l["g"])(new u({args:{direction:3,unit:2,select:!0,value:1},id:"cursorDownSelect",precondition:void 0,kbOpts:{weight:b,kbExpr:y["a"].textInputFocus,primary:1042,secondary:[3090],mac:{primary:1042},linux:{primary:1042}}})),t.CursorPageDown=Object(l["g"])(new u({args:{direction:3,unit:2,select:!1,value:-1},id:"cursorPageDown",precondition:void 0,kbOpts:{weight:b,kbExpr:y["a"].textInputFocus,primary:12}})),t.CursorPageDownSelect=Object(l["g"])(new u({args:{direction:3,unit:2,select:!0,value:-1},id:"cursorPageDownSelect",precondition:void 0,kbOpts:{weight:b,kbExpr:y["a"].textInputFocus,primary:1036}})),t.CreateCursor=Object(l["g"])(new(function(t){function e(){return t.call(this,{id:"createCursor",precondition:void 0})||this}return w(e,t),e.prototype.runCoreEditorCommand=function(t,e){var n,o=t.context;n=e.wholeLine?f["b"].line(o,t.getPrimaryCursor(),!1,e.position,e.viewPosition):f["b"].moveTo(o,t.getPrimaryCursor(),!1,e.position,e.viewPosition);var i=t.getAll();if(i.length>1)for(var r=n.modelState?n.modelState.position:null,s=n.viewState?n.viewState.position:null,a=0,u=i.length;a<u;a++){var l=i[a];if((!r||l.modelState.selection.containsPosition(r))&&(!s||l.viewState.selection.containsPosition(s)))return i.splice(a,1),t.context.model.pushStackElement(),void t.setStates(e.source,3,i)}i.push(n),t.context.model.pushStackElement(),t.setStates(e.source,3,i)},e}(S))),t.LastCursorMoveToSelect=Object(l["g"])(new(function(t){function e(){return t.call(this,{id:"_lastCursorMoveToSelect",precondition:void 0})||this}return w(e,t),e.prototype.runCoreEditorCommand=function(t,e){var n=t.context,o=t.getLastAddedCursorIndex(),i=t.getAll(),r=i.slice(0);r[o]=f["b"].moveTo(n,i[o],!0,e.position,e.viewPosition),t.context.model.pushStackElement(),t.setStates(e.source,3,r)},e}(S)));var c=function(t){function e(e){var n=t.call(this,e)||this;return n._inSelectionMode=e.inSelectionMode,n}return w(e,t),e.prototype.runCoreEditorCommand=function(t,e){t.context.model.pushStackElement(),t.setStates(e.source,3,f["b"].moveToBeginningOfLine(t.context,t.getAll(),this._inSelectionMode)),t.reveal(e.source,!0,0,0)},e}(S);t.CursorHome=Object(l["g"])(new c({inSelectionMode:!1,id:"cursorHome",precondition:void 0,kbOpts:{weight:b,kbExpr:y["a"].textInputFocus,primary:14,mac:{primary:14,secondary:[2063]}}})),t.CursorHomeSelect=Object(l["g"])(new c({inSelectionMode:!0,id:"cursorHomeSelect",precondition:void 0,kbOpts:{weight:b,kbExpr:y["a"].textInputFocus,primary:1038,mac:{primary:1038,secondary:[3087]}}})),t.CursorLineStart=Object(l["g"])(new(function(t){function e(){return t.call(this,{id:"cursorLineStart",precondition:void 0,kbOpts:{weight:b,kbExpr:y["a"].textInputFocus,primary:0,mac:{primary:287}}})||this}return w(e,t),e.prototype.runCoreEditorCommand=function(t,e){t.context.model.pushStackElement(),t.setStates(e.source,3,this._exec(t.context,t.getAll())),t.reveal(e.source,!0,0,0)},e.prototype._exec=function(t,e){for(var n=[],o=0,i=e.length;o<i;o++){var r=e[o],s=r.modelState.position.lineNumber;n[o]=d["d"].fromModelState(r.modelState.move(!1,s,1,0))}return n},e}(S)));var h=function(t){function e(e){var n=t.call(this,e)||this;return n._inSelectionMode=e.inSelectionMode,n}return w(e,t),e.prototype.runCoreEditorCommand=function(t,e){t.context.model.pushStackElement(),t.setStates(e.source,3,f["b"].moveToEndOfLine(t.context,t.getAll(),this._inSelectionMode)),t.reveal(e.source,!0,0,0)},e}(S);t.CursorEnd=Object(l["g"])(new h({inSelectionMode:!1,id:"cursorEnd",precondition:void 0,kbOpts:{weight:b,kbExpr:y["a"].textInputFocus,primary:13,mac:{primary:13,secondary:[2065]}}})),t.CursorEndSelect=Object(l["g"])(new h({inSelectionMode:!0,id:"cursorEndSelect",precondition:void 0,kbOpts:{weight:b,kbExpr:y["a"].textInputFocus,primary:1037,mac:{primary:1037,secondary:[3089]}}})),t.CursorLineEnd=Object(l["g"])(new(function(t){function e(){return t.call(this,{id:"cursorLineEnd",precondition:void 0,kbOpts:{weight:b,kbExpr:y["a"].textInputFocus,primary:0,mac:{primary:291}}})||this}return w(e,t),e.prototype.runCoreEditorCommand=function(t,e){t.context.model.pushStackElement(),t.setStates(e.source,3,this._exec(t.context,t.getAll())),t.reveal(e.source,!0,0,0)},e.prototype._exec=function(t,e){for(var n=[],o=0,i=e.length;o<i;o++){var r=e[o],s=r.modelState.position.lineNumber,a=t.model.getLineMaxColumn(s);n[o]=d["d"].fromModelState(r.modelState.move(!1,s,a,0))}return n},e}(S)));var g=function(t){function e(e){var n=t.call(this,e)||this;return n._inSelectionMode=e.inSelectionMode,n}return w(e,t),e.prototype.runCoreEditorCommand=function(t,e){t.context.model.pushStackElement(),t.setStates(e.source,3,f["b"].moveToBeginningOfBuffer(t.context,t.getAll(),this._inSelectionMode)),t.reveal(e.source,!0,0,0)},e}(S);t.CursorTop=Object(l["g"])(new g({inSelectionMode:!1,id:"cursorTop",precondition:void 0,kbOpts:{weight:b,kbExpr:y["a"].textInputFocus,primary:2062,mac:{primary:2064}}})),t.CursorTopSelect=Object(l["g"])(new g({inSelectionMode:!0,id:"cursorTopSelect",precondition:void 0,kbOpts:{weight:b,kbExpr:y["a"].textInputFocus,primary:3086,mac:{primary:3088}}}));var v=function(t){function e(e){var n=t.call(this,e)||this;return n._inSelectionMode=e.inSelectionMode,n}return w(e,t),e.prototype.runCoreEditorCommand=function(t,e){t.context.model.pushStackElement(),t.setStates(e.source,3,f["b"].moveToEndOfBuffer(t.context,t.getAll(),this._inSelectionMode)),t.reveal(e.source,!0,0,0)},e}(S);t.CursorBottom=Object(l["g"])(new v({inSelectionMode:!1,id:"cursorBottom",precondition:void 0,kbOpts:{weight:b,kbExpr:y["a"].textInputFocus,primary:2061,mac:{primary:2066}}})),t.CursorBottomSelect=Object(l["g"])(new v({inSelectionMode:!0,id:"cursorBottomSelect",precondition:void 0,kbOpts:{weight:b,kbExpr:y["a"].textInputFocus,primary:3085,mac:{primary:3090}}}));var C=function(t){function e(){return t.call(this,{id:"editorScroll",precondition:void 0,description:o.description})||this}return w(e,t),e.prototype.runCoreEditorCommand=function(t,e){var n=o.parse(e);n&&this._runEditorScroll(t,e.source,n)},e.prototype._runEditorScroll=function(t,e,n){var o=this._computeDesiredScrollTop(t.context,n);if(n.revealCursor){var i=t.context.getCompletelyVisibleViewRangeAtScrollTop(o);t.setStates(e,3,[f["b"].findPositionInViewportIfOutside(t.context,t.getPrimaryCursor(),i,n.select)])}t.scrollTo(o)},e.prototype._computeDesiredScrollTop=function(t,e){if(1===e.unit){var n=t.getCompletelyVisibleModelRange(),o=void 0;o=1===e.direction?Math.max(1,n.startLineNumber-e.value):Math.min(t.model.getLineCount(),n.startLineNumber+e.value);var i=t.convertModelPositionToViewPosition(new m["a"](o,1));return t.getVerticalOffsetForViewLine(i.lineNumber)}var r;r=3===e.unit?t.config.pageSize*e.value:4===e.unit?Math.round(t.config.pageSize/2)*e.value:e.value;var s=(1===e.direction?-1:1)*r;return t.getCurrentScrollTop()+s*t.config.lineHeight},e}(S);t.EditorScrollImpl=C,t.EditorScroll=Object(l["g"])(new C),t.ScrollLineUp=Object(l["g"])(new(function(e){function n(){return e.call(this,{id:"scrollLineUp",precondition:void 0,kbOpts:{weight:b,kbExpr:y["a"].textInputFocus,primary:2064,mac:{primary:267}}})||this}return w(n,e),n.prototype.runCoreEditorCommand=function(e,n){t.EditorScroll._runEditorScroll(e,n.source,{direction:1,unit:2,value:1,revealCursor:!1,select:!1})},n}(S))),t.ScrollPageUp=Object(l["g"])(new(function(e){function n(){return e.call(this,{id:"scrollPageUp",precondition:void 0,kbOpts:{weight:b,kbExpr:y["a"].textInputFocus,primary:2059,win:{primary:523},linux:{primary:523}}})||this}return w(n,e),n.prototype.runCoreEditorCommand=function(e,n){t.EditorScroll._runEditorScroll(e,n.source,{direction:1,unit:3,value:1,revealCursor:!1,select:!1})},n}(S))),t.ScrollLineDown=Object(l["g"])(new(function(e){function n(){return e.call(this,{id:"scrollLineDown",precondition:void 0,kbOpts:{weight:b,kbExpr:y["a"].textInputFocus,primary:2066,mac:{primary:268}}})||this}return w(n,e),n.prototype.runCoreEditorCommand=function(e,n){t.EditorScroll._runEditorScroll(e,n.source,{direction:2,unit:2,value:1,revealCursor:!1,select:!1})},n}(S))),t.ScrollPageDown=Object(l["g"])(new(function(e){function n(){return e.call(this,{id:"scrollPageDown",precondition:void 0,kbOpts:{weight:b,kbExpr:y["a"].textInputFocus,primary:2060,win:{primary:524},linux:{primary:524}}})||this}return w(n,e),n.prototype.runCoreEditorCommand=function(e,n){t.EditorScroll._runEditorScroll(e,n.source,{direction:2,unit:3,value:1,revealCursor:!1,select:!1})},n}(S)));var x=function(t){function e(e){var n=t.call(this,e)||this;return n._inSelectionMode=e.inSelectionMode,n}return w(e,t),e.prototype.runCoreEditorCommand=function(t,e){t.context.model.pushStackElement(),t.setStates(e.source,3,[f["b"].word(t.context,t.getPrimaryCursor(),this._inSelectionMode,e.position)]),t.reveal(e.source,!0,0,0)},e}(S);t.WordSelect=Object(l["g"])(new x({inSelectionMode:!1,id:"_wordSelect",precondition:void 0})),t.WordSelectDrag=Object(l["g"])(new x({inSelectionMode:!0,id:"_wordSelectDrag",precondition:void 0})),t.LastCursorWordSelect=Object(l["g"])(new(function(t){function e(){return t.call(this,{id:"lastCursorWordSelect",precondition:void 0})||this}return w(e,t),e.prototype.runCoreEditorCommand=function(t,e){var n=t.context,o=t.getLastAddedCursorIndex(),i=t.getAll(),r=i.slice(0),s=i[o];r[o]=f["b"].word(n,s,s.modelState.hasSelection(),e.position),n.model.pushStackElement(),t.setStates(e.source,3,r)},e}(S)));var T=function(t){function e(e){var n=t.call(this,e)||this;return n._inSelectionMode=e.inSelectionMode,n}return w(e,t),e.prototype.runCoreEditorCommand=function(t,e){t.context.model.pushStackElement(),t.setStates(e.source,3,[f["b"].line(t.context,t.getPrimaryCursor(),this._inSelectionMode,e.position,e.viewPosition)]),t.reveal(e.source,!1,0,0)},e}(S);t.LineSelect=Object(l["g"])(new T({inSelectionMode:!1,id:"_lineSelect",precondition:void 0})),t.LineSelectDrag=Object(l["g"])(new T({inSelectionMode:!0,id:"_lineSelectDrag",precondition:void 0}));var E=function(t){function e(e){var n=t.call(this,e)||this;return n._inSelectionMode=e.inSelectionMode,n}return w(e,t),e.prototype.runCoreEditorCommand=function(t,e){var n=t.getLastAddedCursorIndex(),o=t.getAll(),i=o.slice(0);i[n]=f["b"].line(t.context,o[n],this._inSelectionMode,e.position,e.viewPosition),t.context.model.pushStackElement(),t.setStates(e.source,3,i)},e}(S);t.LastCursorLineSelect=Object(l["g"])(new E({inSelectionMode:!1,id:"lastCursorLineSelect",precondition:void 0})),t.LastCursorLineSelectDrag=Object(l["g"])(new E({inSelectionMode:!0,id:"lastCursorLineSelectDrag",precondition:void 0})),t.ExpandLineSelection=Object(l["g"])(new(function(t){function e(){return t.call(this,{id:"expandLineSelection",precondition:void 0,kbOpts:{weight:b,kbExpr:y["a"].textInputFocus,primary:2090}})||this}return w(e,t),e.prototype.runCoreEditorCommand=function(t,e){t.context.model.pushStackElement(),t.setStates(e.source,3,f["b"].expandLineSelection(t.context,t.getAll())),t.reveal(e.source,!0,0,0)},e}(S))),t.CancelSelection=Object(l["g"])(new(function(t){function e(){return t.call(this,{id:"cancelSelection",precondition:y["a"].hasNonEmptySelection,kbOpts:{weight:b,kbExpr:y["a"].textInputFocus,primary:9,secondary:[1033]}})||this}return w(e,t),e.prototype.runCoreEditorCommand=function(t,e){t.context.model.pushStackElement(),t.setStates(e.source,3,[f["b"].cancelSelection(t.context,t.getPrimaryCursor())]),t.reveal(e.source,!0,0,0)},e}(S))),t.RemoveSecondaryCursors=Object(l["g"])(new(function(t){function e(){return t.call(this,{id:"removeSecondaryCursors",precondition:y["a"].hasMultipleSelections,kbOpts:{weight:b+1,kbExpr:y["a"].textInputFocus,primary:9,secondary:[1033]}})||this}return w(e,t),e.prototype.runCoreEditorCommand=function(t,e){t.context.model.pushStackElement(),t.setStates(e.source,3,[t.getPrimaryCursor()]),t.reveal(e.source,!0,0,0)},e}(S))),t.RevealLine=Object(l["g"])(new(function(t){function e(){return t.call(this,{id:"revealLine",precondition:void 0,description:i.description})||this}return w(e,t),e.prototype.runCoreEditorCommand=function(t,e){var n=e,o=(n.lineNumber||0)+1;o<1&&(o=1);var r=t.context.model.getLineCount();o>r&&(o=r);var s=new _["a"](o,1,o,t.context.model.getLineMaxColumn(o)),a=0;if(n.at)switch(n.at){case i.RawAtArgument.Top:a=3;break;case i.RawAtArgument.Center:a=1;break;case i.RawAtArgument.Bottom:a=4;break;default:break}var u=t.context.convertModelRangeToViewRange(s);t.revealRange(e.source,!1,u,a,0)},e}(S))),t.SelectAll=Object(l["g"])(new(function(t){function e(){return t.call(this,{id:"selectAll",precondition:void 0})||this}return w(e,t),e.prototype.runCoreEditorCommand=function(t,e){t.context.model.pushStackElement(),t.setStates(e.source,3,[f["b"].selectAll(t.context,t.getPrimaryCursor())])},e}(S))),t.SetSelection=Object(l["g"])(new(function(t){function e(){return t.call(this,{id:"setSelection",precondition:void 0})||this}return w(e,t),e.prototype.runCoreEditorCommand=function(t,e){t.context.model.pushStackElement(),t.setStates(e.source,3,[d["d"].fromModelSelection(e.selection)])},e}(S)))}(r||(r={})),function(t){var e=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return w(e,t),e.prototype.runEditorCommand=function(t,e,n){var o=e._getCursors();o&&this.runCoreEditingCommand(e,o,n||{})},e}(l["c"]);t.CoreEditingCommand=e,t.LineBreakInsert=Object(l["g"])(new(function(t){function e(){return t.call(this,{id:"lineBreakInsert",precondition:y["a"].writable,kbOpts:{weight:b,kbExpr:y["a"].textInputFocus,primary:0,mac:{primary:301}}})||this}return w(e,t),e.prototype.runCoreEditingCommand=function(t,e,n){t.pushUndoStop(),t.executeCommands(this.id,g["a"].lineBreakInsert(e.context.config,e.context.model,e.getAll().map((function(t){return t.modelState.selection}))))},e}(e))),t.Outdent=Object(l["g"])(new(function(t){function e(){return t.call(this,{id:"outdent",precondition:y["a"].writable,kbOpts:{weight:b,kbExpr:C["a"].and(y["a"].editorTextFocus,y["a"].tabDoesNotMoveFocus),primary:1026}})||this}return w(e,t),e.prototype.runCoreEditingCommand=function(t,e,n){t.pushUndoStop(),t.executeCommands(this.id,g["a"].outdent(e.context.config,e.context.model,e.getAll().map((function(t){return t.modelState.selection})))),t.pushUndoStop()},e}(e))),t.Tab=Object(l["g"])(new(function(t){function e(){return t.call(this,{id:"tab",precondition:y["a"].writable,kbOpts:{weight:b,kbExpr:C["a"].and(y["a"].editorTextFocus,y["a"].tabDoesNotMoveFocus),primary:2}})||this}return w(e,t),e.prototype.runCoreEditingCommand=function(t,e,n){t.pushUndoStop(),t.executeCommands(this.id,g["a"].tab(e.context.config,e.context.model,e.getAll().map((function(t){return t.modelState.selection})))),t.pushUndoStop()},e}(e))),t.DeleteLeft=Object(l["g"])(new(function(t){function e(){return t.call(this,{id:"deleteLeft",precondition:y["a"].writable,kbOpts:{weight:b,kbExpr:y["a"].textInputFocus,primary:1,secondary:[1025],mac:{primary:1,secondary:[1025,294,257]}}})||this}return w(e,t),e.prototype.runCoreEditingCommand=function(t,e,n){var o=h["a"].deleteLeft(e.getPrevEditOperationType(),e.context.config,e.context.model,e.getAll().map((function(t){return t.modelState.selection}))),i=o[0],r=o[1];i&&t.pushUndoStop(),t.executeCommands(this.id,r),e.setPrevEditOperationType(2)},e}(e))),t.DeleteRight=Object(l["g"])(new(function(t){function e(){return t.call(this,{id:"deleteRight",precondition:y["a"].writable,kbOpts:{weight:b,kbExpr:y["a"].textInputFocus,primary:20,mac:{primary:20,secondary:[290,276]}}})||this}return w(e,t),e.prototype.runCoreEditingCommand=function(t,e,n){var o=h["a"].deleteRight(e.getPrevEditOperationType(),e.context.config,e.context.model,e.getAll().map((function(t){return t.modelState.selection}))),i=o[0],r=o[1];i&&t.pushUndoStop(),t.executeCommands(this.id,r),e.setPrevEditOperationType(3)},e}(e)))}(s||(s={}));var T=function(t){function e(e){var n=t.call(this,e)||this;return n._editorHandler=e.editorHandler,n._inputHandler=e.inputHandler,n}return w(e,t),e.prototype.runCommand=function(t,e){var n=t.get(c["a"]).getFocusedCodeEditor();if(n&&n.hasTextFocus())return this._runEditorHandler(t,n,e);var o=document.activeElement;if(!(o&&["input","textarea"].indexOf(o.tagName.toLowerCase())>=0)){var i=t.get(c["a"]).getActiveCodeEditor();return i?(i.focus(),this._runEditorHandler(t,i,e)):void 0}document.execCommand(this._inputHandler)},e.prototype._runEditorHandler=function(t,e,n){var o=this._editorHandler;"string"===typeof o?e.trigger("keyboard",o,n):(n=n||{},n.source="keyboard",o.runEditorCommand(t,e,n))},e}(l["a"]),E=function(t){function e(e,n,o){var i=t.call(this,{id:e,precondition:void 0,description:o})||this;return i._handlerId=n,i}return w(e,t),e.prototype.runCommand=function(t,e){var n=t.get(c["a"]).getFocusedCodeEditor();n&&n.trigger("keyboard",this._handlerId,e)},e}(l["a"]);function O(t,e){x(new E("default:"+t,t)),x(new E(t,t,e))}x(new T({editorHandler:r.SelectAll,inputHandler:"selectAll",id:"editor.action.selectAll",precondition:y["a"].textInputFocus,kbOpts:{weight:b,kbExpr:null,primary:2079},menuOpts:{menuId:25,group:"1_basic",title:a["a"]({key:"miSelectAll",comment:["&& denotes a mnemonic"]},"&&Select All"),order:1}})),x(new T({editorHandler:v["b"].Undo,inputHandler:"undo",id:v["b"].Undo,precondition:y["a"].writable,kbOpts:{weight:b,kbExpr:y["a"].textInputFocus,primary:2104},menuOpts:{menuId:17,group:"1_do",title:a["a"]({key:"miUndo",comment:["&& denotes a mnemonic"]},"&&Undo"),order:1}})),x(new E("default:"+v["b"].Undo,v["b"].Undo)),x(new T({editorHandler:v["b"].Redo,inputHandler:"redo",id:v["b"].Redo,precondition:y["a"].writable,kbOpts:{weight:b,kbExpr:y["a"].textInputFocus,primary:2103,secondary:[3128],mac:{primary:3128}},menuOpts:{menuId:17,group:"1_do",title:a["a"]({key:"miRedo",comment:["&& denotes a mnemonic"]},"&&Redo"),order:2}})),x(new E("default:"+v["b"].Redo,v["b"].Redo)),O(v["b"].Type,{description:"Type",args:[{name:"args",schema:{type:"object",required:["text"],properties:{text:{type:"string"}}}}]}),O(v["b"].ReplacePreviousChar),O(v["b"].CompositionStart),O(v["b"].CompositionEnd),O(v["b"].Paste),O(v["b"].Cut)},e393:function(t,e,n){"use strict";n.d(e,"a",(function(){return S}));n("56f3");var o=n("dff7"),i=n("0f70"),r=n("6653"),s=n("30db"),a=n("3742"),u=n("1ddc"),l=n("e53c"),c=n("0a89"),p=n("4247"),d=n("b160"),h=n("3d43"),f=n("fd49"),g=n("e6ff"),m=n("7061"),_=n("6a89"),v=n("8025"),y=n("0d83"),C=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},t(e,n)};return function(e,n){function o(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),w=function(){function t(t,e,n){this.top=t,this.left=e,this.width=n}return t.prototype.setWidth=function(e){return new t(this.top,this.left,e)},t}(),b=i["f"]||i["h"],S=function(t){function e(e,n,o){var a=t.call(this,e)||this;a._primaryCursorPosition=new m["a"](1,1),a._primaryCursorVisibleRange=null,a._viewController=n,a._viewHelper=o,a._scrollLeft=0,a._scrollTop=0;var u=a._context.configuration.options,d=u.get(107);a._setAccessibilityOptions(u),a._contentLeft=d.contentLeft,a._contentWidth=d.contentWidth,a._contentHeight=d.height,a._fontInfo=u.get(34),a._lineHeight=u.get(49),a._emptySelectionClipboard=u.get(25),a._copyWithSyntaxHighlighting=u.get(15),a._visibleTextArea=null,a._selections=[new v["a"](1,1,1,1)],a._modelSelections=[new v["a"](1,1,1,1)],a._lastRenderPosition=null,a.textArea=Object(r["b"])(document.createElement("textarea")),p["a"].write(a.textArea,6),a.textArea.setClassName("inputarea"),a.textArea.setAttribute("wrap","off"),a.textArea.setAttribute("autocorrect","off"),a.textArea.setAttribute("autocapitalize","off"),a.textArea.setAttribute("autocomplete","off"),a.textArea.setAttribute("spellcheck","false"),a.textArea.setAttribute("aria-label",a._getAriaLabel(u)),a.textArea.setAttribute("role","textbox"),a.textArea.setAttribute("aria-multiline","true"),a.textArea.setAttribute("aria-haspopup","false"),a.textArea.setAttribute("aria-autocomplete","both"),s["g"]&&u.get(68)&&a.textArea.setAttribute("readonly","true"),a.textAreaCover=Object(r["b"])(document.createElement("div")),a.textAreaCover.setPosition("absolute");var h={getLineCount:function(){return a._context.model.getLineCount()},getLineMaxColumn:function(t){return a._context.model.getLineMaxColumn(t)},getValueInRange:function(t,e){return a._context.model.getValueInRange(t,e)}},f={getDataToCopy:function(t){var e=a._context.model.getPlainTextToCopy(a._modelSelections,a._emptySelectionClipboard,s["h"]),n=a._context.model.getEOL(),o=a._emptySelectionClipboard&&1===a._modelSelections.length&&a._modelSelections[0].isEmpty(),i=Array.isArray(e)?e:null,r=Array.isArray(e)?e.join(n):e,u=void 0,c=null;if(t&&(l["a"].forceCopyWithSyntaxHighlighting||a._copyWithSyntaxHighlighting&&r.length<65536)){var p=a._context.model.getRichTextToCopy(a._modelSelections,a._emptySelectionClipboard);p&&(u=p.html,c=p.mode)}return{isFromEmptySelection:o,multicursorText:i,text:r,html:u,mode:c}},getScreenReaderContent:function(t){if(i["j"])return c["b"].EMPTY;if(1===a._accessibilitySupport){if(s["e"]){var e=a._selections[0];if(e.isEmpty()){var n=e.getStartPosition(),o=a._getWordBeforePosition(n);if(0===o.length&&(o=a._getCharacterBeforePosition(n)),o.length>0)return new c["b"](o,o.length,o.length,n,n)}}return c["b"].EMPTY}return c["a"].fromEditorSelection(t,h,a._selections[0],a._accessibilityPageSize,0===a._accessibilitySupport)},deduceModelPosition:function(t,e,n){return a._context.model.deduceModelPositionRelativeToViewPosition(t,e,n)}};return a._textAreaInput=a._register(new l["b"](f,a.textArea)),a._register(a._textAreaInput.onKeyDown((function(t){a._viewController.emitKeyDown(t)}))),a._register(a._textAreaInput.onKeyUp((function(t){a._viewController.emitKeyUp(t)}))),a._register(a._textAreaInput.onPaste((function(t){var e=!1,n=null,o=null;t.metadata&&(e=a._emptySelectionClipboard&&!!t.metadata.isFromEmptySelection,n="undefined"!==typeof t.metadata.multicursorText?t.metadata.multicursorText:null,o=t.metadata.mode),a._viewController.paste("keyboard",t.text,e,n,o)}))),a._register(a._textAreaInput.onCut((function(){a._viewController.cut("keyboard")}))),a._register(a._textAreaInput.onType((function(t){t.replaceCharCnt?a._viewController.replacePreviousChar("keyboard",t.text,t.replaceCharCnt):a._viewController.type("keyboard",t.text)}))),a._register(a._textAreaInput.onSelectionChangeRequest((function(t){a._viewController.setSelection("keyboard",t)}))),a._register(a._textAreaInput.onCompositionStart((function(){var t=a._selections[0].startLineNumber,e=a._selections[0].startColumn;a._context.privateViewEventBus.emit(new y["m"]("keyboard",new _["a"](t,e,t,e),0,!0,1));var n=a._viewHelper.visibleRangeForPositionRelativeToEditor(t,e);n&&(a._visibleTextArea=new w(a._context.viewLayout.getVerticalOffsetForLineNumber(t),n.left,b?0:1),a._render()),a.textArea.setClassName("inputarea ime-input"),a._viewController.compositionStart("keyboard")}))),a._register(a._textAreaInput.onCompositionUpdate((function(t){i["f"]?a._visibleTextArea=a._visibleTextArea.setWidth(0):a._visibleTextArea=a._visibleTextArea.setWidth(x(t.data,a._fontInfo)),a._render()}))),a._register(a._textAreaInput.onCompositionEnd((function(){a._visibleTextArea=null,a._render(),a.textArea.setClassName("inputarea"),a._viewController.compositionEnd("keyboard")}))),a._register(a._textAreaInput.onFocus((function(){a._context.privateViewEventBus.emit(new y["g"](!0))}))),a._register(a._textAreaInput.onBlur((function(){a._context.privateViewEventBus.emit(new y["g"](!1))}))),a}return C(e,t),e.prototype.dispose=function(){t.prototype.dispose.call(this)},e.prototype._getWordBeforePosition=function(t){var e=this._context.model.getLineContent(t.lineNumber),n=Object(g["a"])(this._context.configuration.options.get(96)),o=t.column,i=0;while(o>1){var r=e.charCodeAt(o-2),s=n.get(r);if(0!==s||i>50)return e.substring(o-1,t.column-1);i++,o--}return e.substring(0,t.column-1)},e.prototype._getCharacterBeforePosition=function(t){if(t.column>1){var e=this._context.model.getLineContent(t.lineNumber),n=e.charAt(t.column-2);if(!a["z"](n.charCodeAt(0)))return n}return""},e.prototype._getAriaLabel=function(t){var e=t.get(2);return 1===e?o["a"]("accessibilityOffAriaLabel","The editor is not accessible at this time. Press Alt+F1 for options."):t.get(4)},e.prototype._setAccessibilityOptions=function(t){this._accessibilitySupport=t.get(2);var e=t.get(3);2===this._accessibilitySupport&&e===f["e"].accessibilityPageSize.defaultValue?this._accessibilityPageSize=160:this._accessibilityPageSize=e},e.prototype.onConfigurationChanged=function(t){var e=this._context.configuration.options,n=e.get(107);return this._setAccessibilityOptions(e),this._contentLeft=n.contentLeft,this._contentWidth=n.contentWidth,this._contentHeight=n.height,this._fontInfo=e.get(34),this._lineHeight=e.get(49),this._emptySelectionClipboard=e.get(25),this._copyWithSyntaxHighlighting=e.get(15),this.textArea.setAttribute("aria-label",this._getAriaLabel(e)),s["g"]&&t.hasChanged(68)&&(e.get(68)?this.textArea.setAttribute("readonly","true"):this.textArea.removeAttribute("readonly")),t.hasChanged(2)&&this._textAreaInput.writeScreenReaderContent("strategy changed"),!0},e.prototype.onCursorStateChanged=function(t){return this._selections=t.selections.slice(0),this._modelSelections=t.modelSelections.slice(0),this._textAreaInput.writeScreenReaderContent("selection changed"),!0},e.prototype.onDecorationsChanged=function(t){return!0},e.prototype.onFlushed=function(t){return!0},e.prototype.onLinesChanged=function(t){return!0},e.prototype.onLinesDeleted=function(t){return!0},e.prototype.onLinesInserted=function(t){return!0},e.prototype.onScrollChanged=function(t){return this._scrollLeft=t.scrollLeft,this._scrollTop=t.scrollTop,!0},e.prototype.onZonesChanged=function(t){return!0},e.prototype.isFocused=function(){return this._textAreaInput.isFocused()},e.prototype.focusTextArea=function(){this._textAreaInput.focusTextArea()},e.prototype.getLastRenderData=function(){return this._lastRenderPosition},e.prototype.setAriaOptions=function(t){t.activeDescendant?(this.textArea.setAttribute("aria-haspopup","true"),this.textArea.setAttribute("aria-autocomplete","list"),this.textArea.setAttribute("aria-activedescendant",t.activeDescendant)):(this.textArea.setAttribute("aria-haspopup","false"),this.textArea.setAttribute("aria-autocomplete","both"),this.textArea.removeAttribute("aria-activedescendant"))},e.prototype.prepareRender=function(t){this._primaryCursorPosition=new m["a"](this._selections[0].positionLineNumber,this._selections[0].positionColumn),this._primaryCursorVisibleRange=t.visibleRangeForPosition(this._primaryCursorPosition)},e.prototype.render=function(t){this._textAreaInput.writeScreenReaderContent("render"),this._render()},e.prototype._render=function(){if(this._visibleTextArea)this._renderInsideEditor(null,this._visibleTextArea.top-this._scrollTop,this._contentLeft+this._visibleTextArea.left-this._scrollLeft,this._visibleTextArea.width,this._lineHeight);else if(this._primaryCursorVisibleRange){var t=this._contentLeft+this._primaryCursorVisibleRange.left-this._scrollLeft;if(t<this._contentLeft||t>this._contentLeft+this._contentWidth)this._renderAtTopLeft();else{var e=this._context.viewLayout.getVerticalOffsetForLineNumber(this._selections[0].positionLineNumber)-this._scrollTop;e<0||e>this._contentHeight?this._renderAtTopLeft():s["e"]?this._renderInsideEditor(this._primaryCursorPosition,e,t,b?0:1,this._lineHeight):this._renderInsideEditor(this._primaryCursorPosition,e,t,b?0:1,b?0:1)}}else this._renderAtTopLeft()},e.prototype._renderInsideEditor=function(t,e,n,o,i){this._lastRenderPosition=t;var r=this.textArea,s=this.textAreaCover;u["a"].applyFontInfo(r,this._fontInfo),r.setTop(e),r.setLeft(n),r.setWidth(o),r.setHeight(i),s.setTop(0),s.setLeft(0),s.setWidth(0),s.setHeight(0)},e.prototype._renderAtTopLeft=function(){this._lastRenderPosition=null;var t=this.textArea,e=this.textAreaCover;if(u["a"].applyFontInfo(t,this._fontInfo),t.setTop(0),t.setLeft(0),e.setTop(0),e.setLeft(0),b)return t.setWidth(0),t.setHeight(0),e.setWidth(0),void e.setHeight(0);t.setWidth(1),t.setHeight(1),e.setWidth(1),e.setHeight(1);var n=this._context.configuration.options;n.get(40)?e.setClassName("monaco-editor-background textAreaCover "+h["a"].OUTER_CLASS_NAME):0!==n.get(50).renderType?e.setClassName("monaco-editor-background textAreaCover "+d["a"].CLASS_NAME):e.setClassName("monaco-editor-background textAreaCover")},e}(p["b"]);function x(t,e){var n=document.createElement("canvas"),o=n.getContext("2d");o.font=T(e);var r=o.measureText(t);return i["h"]?r.width+2:r.width}function T(t){return E("normal",t.fontWeight,t.fontSize,t.lineHeight,t.fontFamily)}function E(t,e,n,o,i){return t+" normal "+e+" "+n+"px / "+o+"px "+i}},e53c:function(t,e,n){"use strict";n.d(e,"a",(function(){return f})),n.d(e,"b",(function(){return m}));var o=n("0f70"),i=n("11f7"),r=n("5fe7"),s=n("308f"),a=n("a666"),u=n("30db"),l=n("3742"),c=n("0a89"),p=n("8025"),d=n("0a31"),h=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},t(e,n)};return function(e,n){function o(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),f={forceCopyWithSyntaxHighlighting:!1},g=function(){function t(){this._lastState=null}return t.prototype.set=function(t,e){this._lastState={lastCopiedValue:t,data:e}},t.prototype.get=function(t){return this._lastState&&this._lastState.lastCopiedValue===t?this._lastState.data:(this._lastState=null,null)},t.INSTANCE=new t,t}(),m=function(t){function e(e,n){var a=t.call(this)||this;a.textArea=n,a._onFocus=a._register(new s["a"]),a.onFocus=a._onFocus.event,a._onBlur=a._register(new s["a"]),a.onBlur=a._onBlur.event,a._onKeyDown=a._register(new s["a"]),a.onKeyDown=a._onKeyDown.event,a._onKeyUp=a._register(new s["a"]),a.onKeyUp=a._onKeyUp.event,a._onCut=a._register(new s["a"]),a.onCut=a._onCut.event,a._onPaste=a._register(new s["a"]),a.onPaste=a._onPaste.event,a._onType=a._register(new s["a"]),a.onType=a._onType.event,a._onCompositionStart=a._register(new s["a"]),a.onCompositionStart=a._onCompositionStart.event,a._onCompositionUpdate=a._register(new s["a"]),a.onCompositionUpdate=a._onCompositionUpdate.event,a._onCompositionEnd=a._register(new s["a"]),a.onCompositionEnd=a._onCompositionEnd.event,a._onSelectionChangeRequest=a._register(new s["a"]),a.onSelectionChangeRequest=a._onSelectionChangeRequest.event,a._host=e,a._textArea=a._register(new v(n)),a._asyncTriggerCut=a._register(new r["d"]((function(){return a._onCut.fire()}),0)),a._textAreaState=c["b"].EMPTY,a._selectionChangeListener=null,a.writeScreenReaderContent("ctor"),a._hasFocus=!1,a._isDoingComposition=!1,a._nextCommand=0,a._register(i["o"](n.domNode,"keydown",(function(t){!a._isDoingComposition||109!==t.keyCode&&1!==t.keyCode||t.stopPropagation(),t.equals(9)&&t.preventDefault(),a._onKeyDown.fire(t)}))),a._register(i["o"](n.domNode,"keyup",(function(t){a._onKeyUp.fire(t)}))),a._register(i["j"](n.domNode,"compositionstart",(function(t){a._isDoingComposition||(a._isDoingComposition=!0,o["f"]||a._setAndWriteTextAreaState("compositionstart",c["b"].EMPTY),a._onCompositionStart.fire())})));var p=function(t){var e=a._textAreaState,n=c["b"].readFromTextArea(a._textArea);return[n,c["b"].deduceInput(e,n,t)]},d=function(t){var e=a._textAreaState,n=c["b"].selectedText(t),o={text:n.value,replaceCharCnt:e.selectionEnd-e.selectionStart};return[n,o]},h=function(t){return!(!o["f"]||"ja"!==t)||!(!o["i"]||0!==t.indexOf("zh-Han"))};return a._register(i["j"](n.domNode,"compositionupdate",(function(t){if(h(t.locale)){var e=p(!1),n=e[0],o=e[1];return a._textAreaState=n,a._onType.fire(o),void a._onCompositionUpdate.fire(t)}var i=d(t.data),r=i[0],s=i[1];a._textAreaState=r,a._onType.fire(s),a._onCompositionUpdate.fire(t)}))),a._register(i["j"](n.domNode,"compositionend",(function(t){if(a._isDoingComposition){if(h(t.locale)){var e=p(!1),n=e[0],i=e[1];a._textAreaState=n,a._onType.fire(i)}else{var r=d(t.data);n=r[0],i=r[1];a._textAreaState=n,a._onType.fire(i)}(o["f"]||o["d"])&&(a._textAreaState=c["b"].readFromTextArea(a._textArea)),a._isDoingComposition&&(a._isDoingComposition=!1,a._onCompositionEnd.fire())}}))),a._register(i["j"](n.domNode,"input",(function(){if(a._textArea.setIgnoreSelectionChangeTime("received input event"),!a._isDoingComposition){var t=p(u["e"]),e=t[0],n=t[1];0===n.replaceCharCnt&&1===n.text.length&&l["z"](n.text.charCodeAt(0))||(a._textAreaState=e,0===a._nextCommand?""!==n.text&&a._onType.fire(n):(""===n.text&&0===n.replaceCharCnt||a._firePaste(n.text,null),a._nextCommand=0))}}))),a._register(i["j"](n.domNode,"cut",(function(t){a._textArea.setIgnoreSelectionChangeTime("received cut event"),a._ensureClipboardGetsEditorSelection(t),a._asyncTriggerCut.schedule()}))),a._register(i["j"](n.domNode,"copy",(function(t){a._ensureClipboardGetsEditorSelection(t)}))),a._register(i["j"](n.domNode,"paste",(function(t){if(a._textArea.setIgnoreSelectionChangeTime("received paste event"),_.canUseTextData(t)){var e=_.getTextData(t),n=e[0],o=e[1];""!==n&&a._firePaste(n,o)}else a._textArea.getSelectionStart()!==a._textArea.getSelectionEnd()&&a._setAndWriteTextAreaState("paste",c["b"].EMPTY),a._nextCommand=1}))),a._register(i["j"](n.domNode,"focus",(function(){a._setHasFocus(!0)}))),a._register(i["j"](n.domNode,"blur",(function(){a._setHasFocus(!1)}))),a}return h(e,t),e.prototype._installSelectionChangeListener=function(){var t=this,e=0;return i["j"](document,"selectionchange",(function(n){if(t._hasFocus&&!t._isDoingComposition&&o["d"]&&u["h"]){var i=Date.now(),r=i-e;if(e=i,!(r<5)){var s=i-t._textArea.getIgnoreSelectionChangeTime();if(t._textArea.resetSelectionChangeTime(),!(s<100)&&t._textAreaState.selectionStartPosition&&t._textAreaState.selectionEndPosition){var a=t._textArea.getValue();if(t._textAreaState.value===a){var l=t._textArea.getSelectionStart(),c=t._textArea.getSelectionEnd();if(t._textAreaState.selectionStart!==l||t._textAreaState.selectionEnd!==c){var d=t._textAreaState.deduceEditorPosition(l),h=t._host.deduceModelPosition(d[0],d[1],d[2]),f=t._textAreaState.deduceEditorPosition(c),g=t._host.deduceModelPosition(f[0],f[1],f[2]),m=new p["a"](h.lineNumber,h.column,g.lineNumber,g.column);t._onSelectionChangeRequest.fire(m)}}}}}}))},e.prototype.dispose=function(){t.prototype.dispose.call(this),this._selectionChangeListener&&(this._selectionChangeListener.dispose(),this._selectionChangeListener=null)},e.prototype.focusTextArea=function(){this._setHasFocus(!0),this.refreshFocusState()},e.prototype.isFocused=function(){return this._hasFocus},e.prototype.refreshFocusState=function(){var t=i["E"](this.textArea.domNode);t?this._setHasFocus(t.activeElement===this.textArea.domNode):i["M"](this.textArea.domNode)?this._setHasFocus(document.activeElement===this.textArea.domNode):this._setHasFocus(!1)},e.prototype._setHasFocus=function(t){this._hasFocus!==t&&(this._hasFocus=t,this._selectionChangeListener&&(this._selectionChangeListener.dispose(),this._selectionChangeListener=null),this._hasFocus&&(this._selectionChangeListener=this._installSelectionChangeListener()),this._hasFocus&&(o["e"]?this._setAndWriteTextAreaState("focusgain",c["b"].EMPTY):this.writeScreenReaderContent("focusgain")),this._hasFocus?this._onFocus.fire():this._onBlur.fire())},e.prototype._setAndWriteTextAreaState=function(t,e){this._hasFocus||(e=e.collapseSelection()),e.writeToTextArea(t,this._textArea,this._hasFocus),this._textAreaState=e},e.prototype.writeScreenReaderContent=function(t){this._isDoingComposition||this._setAndWriteTextAreaState(t,this._host.getScreenReaderContent(this._textAreaState))},e.prototype._ensureClipboardGetsEditorSelection=function(t){var e=this._host.getDataToCopy(_.canUseTextData(t)&&d["a"].clipboard.richText),n={version:1,isFromEmptySelection:e.isFromEmptySelection,multicursorText:e.multicursorText,mode:e.mode};g.INSTANCE.set(o["h"]?e.text.replace(/\r\n/g,"\n"):e.text,n),_.canUseTextData(t)?_.setTextData(t,e.text,e.html,n):this._setAndWriteTextAreaState("copy or cut",c["b"].selectedText(e.text))},e.prototype._firePaste=function(t,e){e||(e=g.INSTANCE.get(t)),this._onPaste.fire({text:t,metadata:e})},e}(a["a"]),_=function(){function t(){}return t.canUseTextData=function(t){return!!t.clipboardData||!!window.clipboardData},t.getTextData=function(t){if(t.clipboardData){t.preventDefault();var e=t.clipboardData.getData("text/plain"),n=null,o=t.clipboardData.getData("vscode-editor-data");if("string"===typeof o)try{n=JSON.parse(o),1!==n.version&&(n=null)}catch(i){}return[e,n]}if(window.clipboardData){t.preventDefault();e=window.clipboardData.getData("Text");return[e,null]}throw new Error("ClipboardEventUtils.getTextData: Cannot use text data!")},t.setTextData=function(t,e,n,o){if(t.clipboardData)return t.clipboardData.setData("text/plain",e),"string"===typeof n&&t.clipboardData.setData("text/html",n),t.clipboardData.setData("vscode-editor-data",JSON.stringify(o)),void t.preventDefault();if(window.clipboardData)return window.clipboardData.setData("Text",e),void t.preventDefault();throw new Error("ClipboardEventUtils.setTextData: Cannot use text data!")},t}(),v=function(t){function e(e){var n=t.call(this)||this;return n._actual=e,n._ignoreSelectionChangeTime=0,n}return h(e,t),e.prototype.setIgnoreSelectionChangeTime=function(t){this._ignoreSelectionChangeTime=Date.now()},e.prototype.getIgnoreSelectionChangeTime=function(){return this._ignoreSelectionChangeTime},e.prototype.resetSelectionChangeTime=function(){this._ignoreSelectionChangeTime=0},e.prototype.getValue=function(){return this._actual.domNode.value},e.prototype.setValue=function(t,e){var n=this._actual.domNode;n.value!==e&&(this.setIgnoreSelectionChangeTime("setValue"),n.value=e)},e.prototype.getSelectionStart=function(){return this._actual.domNode.selectionStart},e.prototype.getSelectionEnd=function(){return this._actual.domNode.selectionEnd},e.prototype.setSelectionRange=function(t,e,n){var r=this._actual.domNode,s=null,a=i["E"](r);s=a?a.activeElement:document.activeElement;var u=s===r,l=r.selectionStart,c=r.selectionEnd;if(u&&l===e&&c===n)o["h"]&&window.parent!==window&&r.focus();else{if(u)return this.setIgnoreSelectionChangeTime("setSelectionRange"),r.setSelectionRange(e,n),void(o["h"]&&window.parent!==window&&r.focus());try{var p=i["V"](r);this.setIgnoreSelectionChangeTime("setSelectionRange"),r.focus(),r.setSelectionRange(e,n),i["T"](r,p)}catch(d){}}},e}(a["a"])}}]);