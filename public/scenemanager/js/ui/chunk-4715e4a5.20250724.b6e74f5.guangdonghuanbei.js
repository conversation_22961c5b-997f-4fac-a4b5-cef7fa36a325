(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4715e4a5"],{"035e":function(t,e){},"045a":function(t,e,n){"use strict";n.d(e,"a",(function(){return N}));var r,i,a,o,s,u,c,l,h,f,p=n("a34a"),d=n.n(p),E=n("c973"),g=n.n(E),m=n("c86f"),v=n.n(m),y=n("970b"),T=n.n(y),b=n("5bc3"),A=n.n(b),_=n("53ec"),R=n.n(_),S=(n("d400"),n("20e7")),C=n("e1c6"),O=n("fae1"),N=(r=Object(C["injectable"])(),i=Object(C["inject"])(O["a"].HierarchyComponentManager),a=Object(C["inject"])(O["a"].TransformComponentManager),o=Object(C["inject"])(O["a"].MeshComponentManager),r((f=function(){function t(){T()(this,t),v()(this,"hierarchy",c,this),v()(this,"transform",l,this),v()(this,"mesh",h,this)}return A()(t,[{key:"execute",value:function(){var t=g()(d.a.mark((function t(){return d.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:this.runTransformUpdateSystem(),this.runHierarchyUpdateSystem();case 2:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}()},{key:"tearDown",value:function(){this.hierarchy.clear(),this.transform.clear()}},{key:"getHierarchyComponentManager",value:function(){return this.hierarchy}},{key:"getTransformComponentManager",value:function(){return this.transform}},{key:"runTransformUpdateSystem",value:function(){var t=this;this.transform.forEach((function(e,n){(n.isDirty()||n.isLocalDirty())&&(t.setMeshAABBDirty(t.mesh.getComponentByEntity(e)),n.updateTransform())}))}},{key:"runHierarchyUpdateSystem",value:function(){var t=this;this.hierarchy.forEach((function(e,n){var r=t.transform.getComponentByEntity(e),i=t.transform.getComponentByEntity(n.parentID);null!==r&&null!==i&&r.updateTransformWithParent(i)}))}},{key:"attach",value:function(t,e,n){this.hierarchy.contains(t)&&this.detach(t),this.hierarchy.create(t,{parentID:e});var r=this.mesh.getComponentByEntity(e);if(this.setMeshAABBDirty(r),r&&-1===r.children.indexOf(t)&&r.children.push(t),this.hierarchy.getCount()>1)for(var i=this.hierarchy.getCount()-1;i>0;--i)for(var a=this.hierarchy.getEntity(i),o=0;o<i;++o){var s=this.hierarchy.getComponent(o);if(s.parentID===a){this.hierarchy.moveItem(i,o),++i;break}}this.hierarchy.getComponentByEntity(t);var u=this.transform.getComponentByEntity(e);null===u&&(u=this.transform.create(e));var c=this.transform.getComponentByEntity(t);null===c&&(c=this.transform.create(t),u=this.transform.getComponentByEntity(e)),c.parent=u,!n&&u&&(c.matrixTransform(S["b"].invert(S["b"].create(),u.worldTransform)),c.updateTransform()),u&&c.updateTransformWithParent(u)}},{key:"detach",value:function(t){var e=this.hierarchy.getComponentByEntity(t);if(null!==e){var n=this.transform.getComponentByEntity(t);null!==n&&(n.parent=null,n.applyTransform()),this.hierarchy.removeKeepSorted(t);var r=this.mesh.getComponentByEntity(e.parentID);if(r){var i=r.children.indexOf(t);r.children.splice(i,1)}this.setMeshAABBDirty(r)}}},{key:"detachChildren",value:function(t){var e=this.mesh.getComponentByEntity(t);e&&(e.children=[]);for(var n=0;n<this.hierarchy.getCount();){var r;if((null===(r=this.hierarchy.getComponent(n))||void 0===r?void 0:r.parentID)===t){var i=this.hierarchy.getEntity(n);this.detach(i)}else++n}}},{key:"setMeshAABBDirty",value:function(t){t&&(t.aabbDirty=!0)}}]),t}(),u=f,c=R()(u.prototype,"hierarchy",[i],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),l=R()(u.prototype,"transform",[a],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),h=R()(u.prototype,"mesh",[o],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),s=u))||s)},"10d1":function(t,e,n){"use strict";n.d(e,"a",(function(){return u}));var r=n("970b"),i=n.n(r),a=n("5bc3"),o=n.n(a),s=n("20e7"),u=function(){function t(e,n){i()(this,t),this.center=void 0,this.halfExtents=void 0,this.min=s["e"].create(),this.max=s["e"].create(),this.update(e,n)}return o()(t,[{key:"update",value:function(t,e){this.center=t||s["e"].create(),this.halfExtents=e||s["e"].fromValues(.5,.5,.5),this.min=s["e"].sub(this.min,this.center,this.halfExtents),this.max=s["e"].add(this.max,this.center,this.halfExtents)}},{key:"setMinMax",value:function(t,e){s["e"].add(this.center,e,t),s["e"].scale(this.center,this.center,.5),s["e"].sub(this.halfExtents,e,t),s["e"].scale(this.halfExtents,this.halfExtents,.5),s["e"].copy(this.min,t),s["e"].copy(this.max,e)}},{key:"getMin",value:function(){return this.min}},{key:"getMax",value:function(){return this.max}},{key:"add",value:function(t){var e=this.center,n=e[0],r=e[1],i=e[2],a=this.halfExtents,o=a[0],s=a[1],u=a[2],c=n-o,l=n+o,h=r-s,f=r+s,p=i-u,d=i+u,E=t.center,g=E[0],m=E[1],v=E[2],y=t.halfExtents,T=y[0],b=y[1],A=y[2],_=g-T,R=g+T,S=m-b,C=m+b,O=v-A,N=v+A;_<c&&(c=_),R>l&&(l=R),S<h&&(h=S),C>f&&(f=C),O<p&&(p=O),N>d&&(d=N),e[0]=.5*(c+l),e[1]=.5*(h+f),e[2]=.5*(p+d),a[0]=.5*(l-c),a[1]=.5*(f-h),a[2]=.5*(d-p),this.min[0]=c,this.min[1]=h,this.min[2]=p,this.max[0]=l,this.max[1]=f,this.max[2]=d}},{key:"intersects",value:function(t){var e=this.getMax(),n=this.getMin(),r=t.getMax(),i=t.getMin();return n[0]<=r[0]&&e[0]>=i[0]&&n[1]<=r[1]&&e[1]>=i[1]&&n[2]<=r[2]&&e[2]>=i[2]}},{key:"containsPoint",value:function(t){var e=this.getMin(),n=this.getMax();return!(t[0]<e[0]||t[0]>n[0]||t[1]<e[1]||t[1]>n[1]||t[2]<e[2]||t[2]>n[2])}},{key:"getNegativeFarPoint",value:function(t){return 273===t.pnVertexFlag?s["e"].copy(s["e"].create(),this.min):272===t.pnVertexFlag?s["e"].fromValues(this.min[0],this.min[1],this.max[2]):257===t.pnVertexFlag?s["e"].fromValues(this.min[0],this.max[1],this.min[2]):256===t.pnVertexFlag?s["e"].fromValues(this.min[0],this.max[1],this.max[2]):17===t.pnVertexFlag?s["e"].fromValues(this.max[0],this.min[1],this.min[2]):16===t.pnVertexFlag?s["e"].fromValues(this.max[0],this.min[1],this.max[2]):1===t.pnVertexFlag?s["e"].fromValues(this.max[0],this.max[1],this.min[2]):s["e"].fromValues(this.max[0],this.max[1],this.max[2])}},{key:"getPositiveFarPoint",value:function(t){return 273===t.pnVertexFlag?s["e"].copy(s["e"].create(),this.max):272===t.pnVertexFlag?s["e"].fromValues(this.max[0],this.max[1],this.min[2]):257===t.pnVertexFlag?s["e"].fromValues(this.max[0],this.min[1],this.max[2]):256===t.pnVertexFlag?s["e"].fromValues(this.max[0],this.min[1],this.min[2]):17===t.pnVertexFlag?s["e"].fromValues(this.min[0],this.max[1],this.max[2]):16===t.pnVertexFlag?s["e"].fromValues(this.min[0],this.max[1],this.min[2]):1===t.pnVertexFlag?s["e"].fromValues(this.min[0],this.min[1],this.max[2]):s["e"].fromValues(this.min[0],this.min[1],this.min[2])}}]),t}()},1574:function(t,e){},1651:function(t,e,n){"use strict";n.d(e,"a",(function(){return k}));var r,i,a,o,s,u,c,l,h,f,p,d,E,g=n("a34a"),m=n.n(g),v=n("c973"),y=n.n(v),T=n("c86f"),b=n.n(T),A=n("970b"),_=n.n(A),R=n("5bc3"),S=n.n(R),C=n("53ec"),O=n.n(C),N=(n("d400"),n("e1c6")),P=n("fae1"),x=n("fb10"),I=n("f781");function L(t,e){var n;if("undefined"===typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(n=w(t))||e&&t&&"number"===typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,s=!1;return{s:function(){n=t[Symbol.iterator]()},n:function(){var t=n.next();return o=t.done,t},e:function(t){s=!0,a=t},f:function(){try{o||null==n.return||n.return()}finally{if(s)throw a}}}}function w(t,e){if(t){if("string"===typeof t)return D(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?D(t,e):void 0}}function D(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}var M={NONE:0,ENCODE:1,HIGHLIGHT:2},k=(r=Object(N["injectable"])(),i=Object(N["inject"])(P["a"].RenderEngine),a=Object(N["inject"])(P["a"].ResourcePool),o=Object(N["inject"])(P["a"].RenderPassFactory),s=Object(N["inject"])(P["a"].MeshComponentManager),r((E=d=function(){function t(){var e=this;_()(this,t),b()(this,"engine",l,this),b()(this,"resourcePool",h,this),b()(this,"renderPassFactory",f,this),b()(this,"mesh",p,this),this.pickingFBO=void 0,this.views=void 0,this.highlightEnabled=!0,this.highlightColor=[255,0,0,255],this.alreadyInRendering=!1,this.setup=function(t,e,n){var r=t.createRenderTarget(e,"picking fbo",{width:1,height:1});n.data={output:e.write(t,r)},e.hasSideEffect=!0},this.execute=function(){var t=y()(m.a.mark((function t(n,r,i){var a,o,s;return m.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.views=i,!e.alreadyInRendering){t.next=3;break}return t.abrupt("return");case 3:a=L(i);try{for(s=function(){var t=o.value,i=t.getViewport(),a=i.width,s=i.height;e.alreadyInRendering=!0;var u=n.getResourceNode(r.data.output);e.pickingFBO=e.resourcePool.getOrCreateResource(u.resource),e.pickingFBO.resize({width:a,height:s}),e.engine.useFramebuffer(e.pickingFBO,(function(){e.engine.clear({framebuffer:e.pickingFBO,color:[0,0,0,0],stencil:0,depth:1});var n,r=e.renderPassFactory(I["a"].IDENTIFIER),i=[],a=t.getScene(),o=L(a.getEntities());try{for(o.s();!(n=o.n()).done;){var s=n.value,u=e.mesh.getComponentByEntity(s),c=u.material;c.setUniform("u_PickingStage",M.ENCODE),i.push(u)}}catch(l){o.e(l)}finally{o.f()}r.renderView(t),i.forEach((function(t){var e=t.material;e.setUniform("u_PickingStage",M.HIGHLIGHT)})),e.alreadyInRendering=!1}))},a.s();!(o=a.n()).done;)s()}catch(u){a.e(u)}finally{a.f()}case 5:case"end":return t.stop()}}),t)})));return function(e,n,r){return t.apply(this,arguments)}}(),this.pick=function(t,n){var r,i,a=t.x,o=t.y,s=e.engine,u=s.readPixels,c=s.useFramebuffer,l=n.getViewport(),h=l.width,f=l.height,p=a*window.devicePixelRatio,d=o*window.devicePixelRatio;if(!(p>h||p<0||d>f||d<0))return c(e.pickingFBO,(function(){r=u({x:Math.round(p),y:Math.round(f-(o+1)*window.devicePixelRatio),width:1,height:1,data:new Uint8Array(4),framebuffer:e.pickingFBO}),0===r[0]&&0===r[1]&&0===r[2]||(i=Object(x["a"])(r),e.highlightEnabled&&e.highlightPickedFeature(r,n))})),i}}return S()(t,[{key:"enableHighlight",value:function(t){this.highlightEnabled=t}},{key:"setHighlightColor",value:function(t){this.highlightColor=t}},{key:"highlightPickedFeature",value:function(t,e){if(t){var n,r=L(e.getScene().getEntities());try{for(r.s();!(n=r.n()).done;){var i=n.value,a=this.mesh.getComponentByEntity(i),o=a.material;o.setUniform("u_PickingStage",M.HIGHLIGHT),o.setUniform("u_PickingColor",[t[0],t[1],t[2]]),o.setUniform("u_HighlightColor",this.highlightColor)}}catch(s){r.e(s)}finally{r.f()}}}}]),t}(),d.IDENTIFIER="PixelPicking Pass",c=E,l=O()(c.prototype,"engine",[i],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),h=O()(c.prototype,"resourcePool",[a],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),f=O()(c.prototype,"renderPassFactory",[o],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),p=O()(c.prototype,"mesh",[s],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),u=c))||u)},"19cf":function(t,e,n){"use strict";n.d(e,"a",(function(){return y}));var r=n("970b"),i=n.n(r),a=n("5bc3"),o=n.n(a),s=n("3c96"),u=n.n(s),c=n("ed6d"),l=n.n(c),h=n("6b58"),f=n.n(h),p=n("36c6"),d=n.n(p),E=n("20e7"),g=n("a03b");function m(t){var e=v();return function(){var n,r=d()(t);if(e){var i=d()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return f()(this,n)}}function v(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}var y=function(t){l()(n,t);var e=m(n);function n(t){var r;return i()(this,n),r=e.call(this,t),r.dirtyFlag=void 0,r.localDirtyFlag=void 0,r.parent=null,r.localPosition=E["e"].fromValues(0,0,0),r.localRotation=E["c"].fromValues(0,0,0,1),r.localScale=E["e"].fromValues(1,1,1),r.localTransform=E["b"].create(),r.position=E["e"].fromValues(0,0,0),r.rotation=E["c"].fromValues(0,0,0,1),r.scaling=E["e"].fromValues(1,1,1),r.worldTransform=E["b"].create(),r.matrixTransform=function(){var t=E["b"].create();return function(e){E["b"].multiply(t,r.getLocalTransform(),e),E["b"].getScaling(r.localScale,t),E["b"].getTranslation(r.localPosition,t),E["b"].getRotation(r.localRotation,t)}}(),r.rotateRollPitchYaw=function(){var t=E["c"].create(),e=E["c"].create(),n=E["c"].create();return function(i,a,o){r.setDirty(),E["c"].fromEuler(t,i,0,0),E["c"].fromEuler(e,0,a,0),E["c"].fromEuler(n,0,0,o),E["c"].multiply(r.localRotation,t,r.localRotation),E["c"].multiply(r.localRotation,r.localRotation,e),E["c"].multiply(r.localRotation,n,r.localRotation),E["c"].normalize(r.localRotation,r.localRotation)}}(),r.lerp=function(){var t=E["e"].create(),e=E["c"].create(),n=E["e"].create(),i=E["e"].create(),a=E["c"].create(),o=E["e"].create();return function(s,u,c){r.setDirty(),E["b"].getScaling(t,s.worldTransform),E["b"].getTranslation(n,s.worldTransform),E["b"].getRotation(e,s.worldTransform),E["b"].getScaling(i,u.worldTransform),E["b"].getTranslation(o,u.worldTransform),E["b"].getRotation(a,u.worldTransform),E["e"].lerp(r.localScale,t,i,c),E["c"].slerp(r.localRotation,e,a,c),E["e"].lerp(r.localPosition,n,o,c)}}(),r.translate=function(){var t=E["e"].create();return function(e){return E["e"].add(t,r.getPosition(),e),r.setPosition(t),r.setDirty(!0),u()(r)}}(),r.translateLocal=function(){return function(t){return E["e"].transformQuat(t,t,r.localRotation),E["e"].add(r.localPosition,r.localPosition,t),r.setLocalDirty(!0),u()(r)}}(),r.setPosition=function(){var t=E["b"].create();return function(e){return r.position=e,r.setLocalDirty(!0),null===r.parent?E["e"].copy(r.localPosition,e):(E["b"].copy(t,r.parent.worldTransform),E["b"].invert(t,t),E["e"].transformMat4(r.localPosition,e,t)),u()(r)}}(),r.rotate=function(){var t=E["c"].create();return function(e){if(null===r.parent)E["c"].multiply(r.localRotation,r.localRotation,e),E["c"].normalize(r.localRotation,r.localRotation);else{var n=r.getRotation(),i=r.parent.getRotation();E["c"].copy(t,i),E["c"].invert(t,t),E["c"].multiply(t,t,e),E["c"].multiply(r.localRotation,e,n),E["c"].normalize(r.localRotation,r.localRotation)}return r.setLocalDirty(),u()(r)}}(),r.rotateLocal=function(){return function(t){return E["c"].multiply(r.localRotation,r.localRotation,t),E["c"].normalize(r.localRotation,r.localRotation),r.setLocalDirty(!0),u()(r)}}(),r.setRotation=function(){var t=E["c"].create();return function(e){return null===r.parent?E["c"].copy(r.localRotation,e):(E["c"].copy(t,r.parent.getRotation()),E["c"].invert(t,t),E["c"].copy(r.localRotation,t),E["c"].mul(r.localRotation,r.localRotation,e)),r.setLocalDirty(!0),u()(r)}}(),r}return o()(n,[{key:"setLocalPosition",value:function(t){E["e"].copy(this.localPosition,t),this.setLocalDirty(!0)}},{key:"setLocalScale",value:function(t){E["e"].copy(this.localScale,t),this.setLocalDirty(!0)}},{key:"setLocalRotation",value:function(t){return E["c"].copy(this.localRotation,t),this.setLocalDirty(!0),this}},{key:"isDirty",value:function(){return this.dirtyFlag}},{key:"setDirty",value:function(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];t?this.dirtyFlag|=n.DIRTY:this.dirtyFlag&=~n.DIRTY}},{key:"isLocalDirty",value:function(){return this.localDirtyFlag}},{key:"setLocalDirty",value:function(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];t?(this.localDirtyFlag|=n.DIRTY,this.setDirty(!0)):this.localDirtyFlag&=~n.DIRTY}},{key:"updateTransform",value:function(){this.isLocalDirty()&&this.getLocalTransform(),this.isDirty()&&null===this.parent&&(E["b"].copy(this.worldTransform,this.getLocalTransform()),this.setDirty(!1))}},{key:"updateTransformWithParent",value:function(t){E["b"].multiply(this.worldTransform,t.worldTransform,this.getLocalTransform())}},{key:"applyTransform",value:function(){this.setDirty(),E["b"].getScaling(this.localScale,this.worldTransform),E["b"].getTranslation(this.localPosition,this.worldTransform),E["b"].getRotation(this.localRotation,this.worldTransform)}},{key:"clearTransform",value:function(){this.setDirty(),this.localPosition=E["e"].fromValues(0,0,0),this.localRotation=E["c"].fromValues(0,0,0,1),this.localScale=E["e"].fromValues(1,1,1)}},{key:"scaleLocal",value:function(t){return this.setLocalDirty(),E["e"].multiply(this.localScale,this.localScale,t),this}},{key:"getLocalPosition",value:function(){return this.localPosition}},{key:"getLocalRotation",value:function(){return this.localRotation}},{key:"getLocalScale",value:function(){return this.localScale}},{key:"getLocalTransform",value:function(){return this.localDirtyFlag&&(E["b"].fromRotationTranslationScale(this.localTransform,this.localRotation,this.localPosition,this.localScale),this.setLocalDirty(!1)),this.localTransform}},{key:"getWorldTransform",value:function(){return this.isLocalDirty()||this.isDirty()?(this.parent&&this.parent.getWorldTransform(),this.updateTransform(),this.worldTransform):this.worldTransform}},{key:"getPosition",value:function(){return E["b"].getTranslation(this.position,this.worldTransform),this.position}},{key:"getRotation",value:function(){return E["b"].getRotation(this.rotation,this.worldTransform),this.rotation}},{key:"getScale",value:function(){return E["b"].getScaling(this.scaling,this.worldTransform),this.scaling}}]),n}(g["a"]);y.DIRTY=1},"1a3d":function(t,e,n){"use strict";n.d(e,"AST_TOKEN_TYPES",(function(){return h})),n.d(e,"STORAGE_CLASS",(function(){return p})),n.d(e,"Target",(function(){return d})),n.d(e,"DefineValuePlaceholder",(function(){return g}));n("98db"),n("a03b"),n("cf93"),n("d1aa"),n("e1d3"),n("a61d"),n("465c"),n("6da5"),n("e81f"),n("91f9"),n("1651"),n("b72b"),n("210d"),n("045a"),n("19cf");var r=n("f72d");n.d(e,"createEntity",(function(){return r["b"]}));var i=n("fae1");n.d(e,"IDENTIFIER",(function(){return i["a"]}));var a=n("3aa4");n.d(e,"createWorldContainer",(function(){return a["a"]}));var o=n("4ac6");n.d(e,"generateAABBFromVertices",(function(){return o["a"]}));var s=n("9633");n.d(e,"isSafari",(function(){return s["a"]}));n("f238");var u=n("e3c8");n.d(e,"AABB",(function(){return u["a"]})),n.d(e,"Frustum",(function(){return u["b"]}));var c=n("e175");n.o(c,"gl")&&n.d(e,"gl",(function(){return c["gl"]}));var l=n("1574");n.o(l,"gl")&&n.d(e,"gl",(function(){return l["gl"]}));var h,f,p,d,E=n("6327");n.o(E,"gl")&&n.d(e,"gl",(function(){return E["gl"]})),n.o(c,"gl")&&n.d(e,"gl",(function(){return c["gl"]})),function(t){t["Void"]="Void",t["Boolean"]="Boolean",t["Float"]="Float",t["Uint32"]="Uint32",t["Int32"]="Int32",t["Vector"]="Vector",t["Vector2Float"]="vec2<f32>",t["Vector3Float"]="vec3<f32>",t["Vector4Float"]="vec4<f32>",t["Vector2Boolean"]="vec2<bool>",t["Vector3Boolean"]="vec3<bool>",t["Vector4Boolean"]="vec4<bool>",t["Vector2Uint"]="vec2<u32>",t["Vector3Uint"]="vec3<u32>",t["Vector4Uint"]="vec4<u32>",t["Vector2Int"]="vec2<i32>",t["Vector3Int"]="vec3<i32>",t["Vector4Int"]="vec4<i32>",t["Matrix"]="Matrix",t["Matrix3x3Float"]="mat3x3<f32>",t["Matrix4x4Float"]="mat4x4<i32>",t["Struct"]="Struct",t["FloatArray"]="Float[]",t["Vector4FloatArray"]="vec4<f32>[]"}(h||(h={})),function(t){t["Program"]="Program",t["Identifier"]="Identifier",t["VariableDeclaration"]="VariableDeclaration",t["BlockStatement"]="BlockStatement",t["ReturnStatement"]="ReturnStatement",t["FunctionDeclaration"]="FunctionDeclaration",t["VariableDeclarator"]="VariableDeclarator",t["AssignmentExpression"]="AssignmentExpression",t["LogicalExpression"]="LogicalExpression",t["BinaryExpression"]="BinaryExpression",t["ArrayExpression"]="ArrayExpression",t["UnaryExpression"]="UnaryExpression",t["UpdateExpression"]="UpdateExpression",t["FunctionExpression"]="FunctionExpression",t["MemberExpression"]="MemberExpression",t["ConditionalExpression"]="ConditionalExpression",t["ExpressionStatement"]="ExpressionStatement",t["CallExpression"]="CallExpression",t["NumThreadStatement"]="NumThreadStatement",t["StorageStatement"]="StorageStatement",t["DoWhileStatement"]="DoWhileStatement",t["WhileStatement"]="WhileStatement",t["ForStatement"]="ForStatement",t["BreakStatement"]="BreakStatement",t["ContinueStatement"]="ContinueStatement",t["IfStatement"]="IfStatement",t["ImportedFunctionStatement"]="ImportedFunctionStatement"}(f||(f={})),function(t){t["Input"]="Input",t["Output"]="Output",t["Uniform"]="Uniform",t["Workgroup"]="Workgroup",t["UniformConstant"]="UniformConstant",t["Image"]="Image",t["StorageBuffer"]="StorageBuffer",t["Private"]="Private",t["Function"]="Function"}(p||(p={})),function(t){t["GLSL100"]="GLSL100",t["GLSL450"]="GLSL450",t["WGSL"]="WGSL"}(d||(d={}));var g="__DefineValuePlaceholder__"},"210d":function(t,e,n){"use strict";n.d(e,"a",(function(){return g}));var r=n("970b"),i=n.n(r),a=n("3c96"),o=n.n(a),s=n("ed6d"),u=n.n(s),c=n("6b58"),l=n.n(c),h=n("36c6"),f=n.n(h),p=n("a03b");function d(t){var e=E();return function(){var n,r=f()(t);if(e){var i=f()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return l()(this,n)}}function E(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}var g=function(t){u()(n,t);var e=d(n);function n(t){var r;return i()(this,n),r=e.call(this,t),r.parentID=void 0,Object.assign(o()(r),t),r}return n}(p["a"])},"257d":function(t,e){},"2c64":function(t,e,n){"use strict";n.d(e,"a",(function(){return h}));var r,i,a,o=n("970b"),s=n.n(o),u=n("5bc3"),c=n.n(u),l=n("e1c6");(function(t){t["PANSTART"]="PANSTART",t["PANEND"]="PANEND",t["PANMOVE"]="PANMOVE",t["PINCH"]="PINCH",t["KEYDOWN"]="KEYDOWN",t["KEYUP"]="KEYUP",t["HOVER"]="HOVER"})(a||(a={}));var h=(r=Object(l["injectable"])(),r(i=function(){function t(){s()(this,t)}return c()(t,[{key:"listen",value:function(t){}},{key:"on",value:function(t,e){}},{key:"connect",value:function(){}},{key:"disconnect",value:function(){}},{key:"destroy",value:function(){}}]),t}())||i)},"327f":function(t,e,n){"use strict";n.d(e,"a",(function(){return h}));var r,i,a,o=n("970b"),s=n.n(o),u=n("5bc3"),c=n.n(u),l=n("e1c6"),h=(r=Object(l["injectable"])(),r((a=function(){function t(){s()(this,t),this.config=void 0}return c()(t,[{key:"get",value:function(){return this.config}},{key:"set",value:function(t){this.config=t}}]),t}(),i=a))||i)},"362a":function(t,e){},"39ba":function(t,e){},"3aa4":function(t,e,n){"use strict";n.d(e,"a",(function(){return J}));n("98db");var r,i,a,o,s,u,c=n("e1c6"),l=n("00fc"),h=n.n(l),f=n("a03b"),p=n("c86f"),d=n.n(p),E=n("970b"),g=n.n(E),m=n("5bc3"),v=n.n(m),y=n("53ec"),T=n.n(y),b=(n("d400"),n("fae1")),A=n("93ac"),_=(r=Object(c["injectable"])(),i=Object(c["inject"])(b["a"].RenderEngine),r((u=function(){function t(){g()(this,t),d()(this,"engine",s,this),this.resourcePool={}}return v()(t,[{key:"getOrCreateResource",value:function(t){if(!this.resourcePool[t.name]){var e=t.descriptor,n=e.width,r=e.height,i=e.usage;this.resourcePool[t.name]=this.engine.createFramebuffer({color:this.engine.createTexture2D({width:n,height:r,wrapS:A["a"].CLAMP_TO_EDGE,wrapT:A["a"].CLAMP_TO_EDGE,usage:i})})}return this.resourcePool[t.name]}},{key:"clean",value:function(){this.resourcePool={}}}]),t}(),o=u,s=T()(o.prototype,"engine",[i],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),a=o))||a),R=n("cf93"),S=n("d1aa"),C=n("e1d3"),O=n("a61d"),N=n("465c"),P=n("6da5"),x=n("e81f"),I=n("91f9"),L=n("8350"),w=n("1651"),D=n("f781"),M=n("b72b"),k=n("210d"),B=n("ed6d"),U=n.n(B),F=n("6b58"),j=n.n(F),G=n("36c6"),V=n.n(G);function H(t){var e=X();return function(){var n,r=V()(t);if(e){var i=V()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return j()(this,n)}}function X(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}var W=function(t){U()(n,t);var e=H(n);function n(t){var r;return g()(this,n),r=e.call(this,t),r.name=void 0,r.name=t.name||"",r}return n}(f["a"]),z=n("045a"),Y=n("19cf"),K=n("327f"),Z=n("2c64"),q=n("c99e"),Q=new c["Container"];h()(Q,!1);function J(){var t=new c["Container"];return t.parent=Q,t.bind(b["a"].Systems).to(z["a"]).inSingletonScope().whenTargetNamed(b["a"].SceneGraphSystem),t.bind(b["a"].Systems).to(R["a"]).inSingletonScope().whenTargetNamed(b["a"].FrameGraphSystem),t.bind(b["a"].Systems).to(I["a"]).inSingletonScope().whenTargetNamed(b["a"].MeshSystem),t.bind(b["a"].Systems).to(C["a"]).inSingletonScope().whenTargetNamed(b["a"].GeometrySystem),t.bind(b["a"].Systems).to(N["a"]).inSingletonScope().whenTargetNamed(b["a"].MaterialSystem),t.bind(b["a"].Systems).to(M["a"]).inSingletonScope().whenTargetNamed(b["a"].RendererSystem),t.bind(b["a"].ResourcePool).to(_).inSingletonScope(),t.bind(b["a"].ConfigService).to(K["a"]).inSingletonScope(),t.bind(b["a"].InteractorService).to(Z["a"]).inSingletonScope(),t.bind(b["a"].RenderPass).to(D["a"]).inSingletonScope().whenTargetNamed(D["a"].IDENTIFIER),t.bind(b["a"].RenderPass).to(L["a"]).inSingletonScope().whenTargetNamed(L["a"].IDENTIFIER),t.bind(b["a"].RenderPass).to(w["a"]).inSingletonScope().whenTargetNamed(w["a"].IDENTIFIER),t.bind(b["a"].RenderPassFactory).toFactory((function(t){return function(e){return t.container.getNamed(b["a"].RenderPass,e)}})),t}Q.bind(b["a"].ShaderModuleService).to(q["a"]).inSingletonScope(),Q.bind(b["a"].NameComponentManager).toConstantValue(new f["b"](W)),Q.bind(b["a"].HierarchyComponentManager).toConstantValue(new f["b"](k["a"])),Q.bind(b["a"].TransformComponentManager).toConstantValue(new f["b"](Y["a"])),Q.bind(b["a"].MeshComponentManager).toConstantValue(new f["b"](x["a"])),Q.bind(b["a"].CullableComponentManager).toConstantValue(new f["b"](P["a"])),Q.bind(b["a"].GeometryComponentManager).toConstantValue(new f["b"](S["a"])),Q.bind(b["a"].MaterialComponentManager).toConstantValue(new f["b"](O["a"]))},"465c":function(t,e,n){"use strict";n.d(e,"a",(function(){return L}));var r,i,a,o,s,u,c,l,h,f,p=n("9523"),d=n.n(p),E=n("a34a"),g=n.n(E),m=n("c973"),v=n.n(m),y=n("c86f"),T=n.n(y),b=n("970b"),A=n.n(b),_=n("5bc3"),R=n.n(_),S=n("53ec"),C=n.n(S),O=(n("d400"),n("e1c6")),N=n("1a3d"),P=n("fae1");function x(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function I(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?x(Object(n),!0).forEach((function(e){d()(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):x(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var L=(r=Object(O["injectable"])(),i=Object(O["inject"])(P["a"].MaterialComponentManager),a=Object(O["inject"])(P["a"].RenderEngine),o=Object(O["inject"])(P["a"].ShaderModuleService),r((f=function(){function t(){A()(this,t),T()(this,"material",c,this),T()(this,"engine",l,this),T()(this,"shaderModule",h,this)}return R()(t,[{key:"execute",value:function(){var t=v()(g.a.mark((function t(){return g.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:case"end":return t.stop()}}),t)})));function e(){return t.apply(this,arguments)}return e}()},{key:"tearDown",value:function(){this.material.clear()}},{key:"createShaderMaterial",value:function(t){var e=Object(N["createEntity"])(),n=t.vertexShader,r=t.fragmentShader,i=[];if(!this.engine.supportWebGPU){var a="material-".concat(e);this.shaderModule.registerModule(a,{vs:t.vertexShader,fs:t.fragmentShader});var o=this.shaderModule.getModule(a);n=o.vs,r=o.fs,o.uniforms&&(i=Object.keys(o.uniforms).map((function(t){return{dirty:!0,name:t,data:o.uniforms[t]}})))}return this.material.create(e,I(I({vertexShaderGLSL:n,fragmentShaderGLSL:r},t),{},{uniforms:i}))}}]),t}(),u=f,c=C()(u.prototype,"material",[i],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),l=C()(u.prototype,"engine",[a],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),h=C()(u.prototype,"shaderModule",[o],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),s=u))||s)},"4ac6":function(t,e,n){"use strict";n.d(e,"a",(function(){return a}));var r=n("20e7"),i=n("10d1");function a(t){for(var e=new i["a"],n=r["e"].fromValues(t[0],t[1],t[2]),a=r["e"].fromValues(t[0],t[1],t[2]),o=3;o<t.length;){var s=t[o++],u=t[o++],c=t[o++];s<n[0]&&(n[0]=s),u<n[1]&&(n[1]=u),c<n[2]&&(n[2]=c),s>a[0]&&(a[0]=s),u>a[1]&&(a[1]=u),c>a[2]&&(a[2]=c)}return e.setMinMax(n,a),e}},"57fc":function(t,e,n){"use strict";var r;(function(t){t["Normal"]="normal",t["PostProcessing"]="post-processing"})(r||(r={}))},6327:function(t,e){},6635:function(t,e){},"6da5":function(t,e,n){"use strict";n.d(e,"a",(function(){return m}));var r,i=n("970b"),a=n.n(i),o=n("3c96"),s=n.n(o),u=n("ed6d"),c=n.n(u),l=n("6b58"),h=n.n(l),f=n("36c6"),p=n.n(f),d=n("a03b");function E(t){var e=g();return function(){var n,r=p()(t);if(e){var i=p()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return h()(this,n)}}function g(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}(function(t){t[t["Standard"]=0]="Standard"})(r||(r={}));var m=function(t){c()(n,t);var e=E(n);function n(t){var i;return a()(this,n),i=e.call(this,t),i.strategy=r.Standard,i.visibilityPlaneMask=0,i.visible=!1,Object.assign(s()(i),t),i}return n}(d["a"])},7175:function(t,e,n){"use strict";n.d(e,"a",(function(){return u}));var r=n("970b"),i=n.n(r),a=n("5bc3"),o=n.n(a),s=n("20e7"),u=function(){function t(e,n){i()(this,t),this.distance=void 0,this.normal=void 0,this.pnVertexFlag=void 0,this.distance=e||0,this.normal=n||s["e"].fromValues(0,1,0),this.updatePNVertexFlag()}return o()(t,[{key:"updatePNVertexFlag",value:function(){this.pnVertexFlag=(Number(this.normal[0]>=0)<<8)+(Number(this.normal[1]>=0)<<4)+Number(this.normal[2]>=0)}},{key:"distanceToPoint",value:function(t){return s["e"].dot(t,this.normal)-this.distance}},{key:"normalize",value:function(){var t=1/s["e"].len(this.normal);s["e"].scale(this.normal,this.normal,t),this.distance*=t}},{key:"intersectsLine",value:function(t,e,n){var r=this.distanceToPoint(t),i=this.distanceToPoint(e),a=r/(r-i),o=a>=0&&a<=1;return o&&n&&s["e"].lerp(n,t,e,a),o}}]),t}()},8350:function(t,e,n){"use strict";n.d(e,"a",(function(){return x}));var r,i,a,o,s,u,c,l,h,f=n("a34a"),p=n.n(f),d=n("c973"),E=n.n(d),g=n("c86f"),m=n.n(g),v=n("970b"),y=n.n(v),T=n("53ec"),b=n.n(T),A=(n("d400"),n("e1c6")),_=n("fae1"),R=n("93ac"),S=n("f781"),C="varying vec2 v_UV;\n\nuniform sampler2D u_Texture;\n\nvoid main() {\n  gl_FragColor = vec4(texture2D(u_Texture, v_UV));\n}",O="attribute vec2 a_Position;\n\nvarying vec2 v_UV;\n\nvoid main() {\n  v_UV = 0.5 * (a_Position + 1.0);\n  gl_Position = vec4(a_Position, 0., 1.);\n}",N="layout(set = 0, binding = 0) uniform sampler u_TextureSampler;\nlayout(set = 0, binding = 1) uniform texture2D u_Texture;\n\nlayout(location = 0) in vec2 v_UV;\nlayout(location = 0) out vec4 outColor;\n\nvoid main() {\n  outColor = texture(sampler2D(u_Texture, u_TextureSampler), v_UV);\n}",P="layout(location = 0) in vec2 a_Position;\nlayout(location = 0) out vec2 v_UV;\n\nvoid main() {\n  v_UV = 0.5 * (a_Position + 1.0);\n  gl_Position = vec4(a_Position, 0., 1.);\n}",x=(r=Object(A["injectable"])(),i=Object(A["inject"])(_["a"].RenderEngine),a=Object(A["inject"])(_["a"].ResourcePool),r((h=l=function t(){var e=this;y()(this,t),m()(this,"engine",u,this),m()(this,"resourcePool",c,this),this.model=void 0,this.setup=function(t,e,n){var r=t.getPass(S["a"].IDENTIFIER);if(r){var i=t.createRenderTarget(e,"render to screen",{width:1,height:1});n.data={input:e.read(r.data.output),output:e.write(t,i)}}},this.execute=function(){var t=E()(p.a.mark((function t(n,r){var i,a,o,s,u,c,l;return p.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(i=e.engine,a=i.createModel,o=i.createAttribute,s=i.createBuffer,e.model){t.next=6;break}return t.next=4,a({vs:e.engine.supportWebGPU?P:O,fs:e.engine.supportWebGPU?N:C,attributes:{a_Position:o({buffer:s({data:[-4,-4,4,-4,0,4],type:R["a"].FLOAT}),size:2,arrayStride:8,stepMode:"vertex",attributes:[{shaderLocation:0,offset:0,format:"float2"}]})},uniforms:{u_Texture:null},depth:{enable:!1},count:3,blend:{enable:!0}});case 4:u=t.sent,e.model=u;case 6:c=n.getResourceNode(r.data.input),l=e.resourcePool.getOrCreateResource(c.resource),e.engine.useFramebuffer(null,(function(){e.engine.clear({framebuffer:null,color:[0,0,0,0],depth:1,stencil:0}),e.model.draw({uniforms:{u_Texture:l}})}));case 9:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}(),this.tearDown=function(){e.model=void 0}},l.IDENTIFIER="Copy Pass",s=h,u=b()(s.prototype,"engine",[i],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),c=b()(s.prototype,"resourcePool",[a],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),o=s))||o)},"91f9":function(t,e,n){"use strict";n.d(e,"a",(function(){return B}));var r,i,a,o,s,u,c,l,h,f,p,d,E,g,m=n("a34a"),v=n.n(m),y=n("c973"),T=n.n(y),b=n("c86f"),A=n.n(b),_=n("970b"),R=n.n(_),S=n("5bc3"),C=n.n(S),O=n("53ec"),N=n.n(O),P=(n("d400"),n("20e7")),x=n("e1c6"),I=n("fae1"),L=n("a9fb"),w=n("fb10");function D(t,e){var n;if("undefined"===typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(n=M(t))||e&&t&&"number"===typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,s=!1;return{s:function(){n=t[Symbol.iterator]()},n:function(){var t=n.next();return o=t.done,t},e:function(t){s=!0,a=t},f:function(){try{o||null==n.return||n.return()}finally{if(s)throw a}}}}function M(t,e){if(t){if("string"===typeof t)return k(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?k(t,e):void 0}}function k(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}var B=(r=Object(x["injectable"])(),i=Object(x["inject"])(I["a"].MeshComponentManager),a=Object(x["inject"])(I["a"].CullableComponentManager),o=Object(x["inject"])(I["a"].GeometryComponentManager),s=Object(x["inject"])(I["a"].HierarchyComponentManager),u=Object(x["inject"])(I["a"].TransformComponentManager),r((g=function(){function t(){R()(this,t),A()(this,"mesh",h,this),A()(this,"cullable",f,this),A()(this,"geometry",p,this),A()(this,"hierarchy",d,this),A()(this,"transform",E,this),this.planes=void 0}return C()(t,[{key:"setFrustumPlanes",value:function(t){this.planes=t}},{key:"execute",value:function(){var t=T()(v.a.mark((function t(e){var n,r,i,a,o,s,u,c,l,h,f,p,d,E,g,m,y,T,b,A,_;return v.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:n=D(e);try{for(n.s();!(r=n.n()).done;){i=r.value,a=i.getScene(),o=i.getCamera(),s=D(a.getEntities());try{for(s.s();!(u=s.n()).done;)c=u.value,l=this.mesh.getComponentByEntity(c),l&&(h=this.hierarchy.getComponentByEntity(c),f=this.cullable.getComponentByEntity(c),p=l.geometry,d=this.transform.getComponentByEntity(c),p&&p.aabb&&d&&l.aabbDirty&&(E=d.worldTransform,g=p.aabb,m=g.center,y=g.halfExtents,T=P["e"].transformMat4(P["e"].create(),m,E),b=Object(w["b"])(E,P["a"].create()),A=P["e"].transformMat3(P["e"].create(),y,b),l.aabb.update(T,A),l.aabbDirty=!1),f&&p&&(_=this.cullable.getComponentByEntity((null===h||void 0===h?void 0:h.parentID)||-1),f.visibilityPlaneMask=this.computeVisibilityWithPlaneMask(l.aabb,(null===_||void 0===_?void 0:_.visibilityPlaneMask)||L["b"].INDETERMINATE,this.planes||o.getFrustum().planes),f.visible=f.visibilityPlaneMask!==L["b"].OUTSIDE))}catch(v){s.e(v)}finally{s.f()}}}catch(v){n.e(v)}finally{n.f()}case 2:case"end":return t.stop()}}),t,this)})));function e(e){return t.apply(this,arguments)}return e}()},{key:"tearDown",value:function(){this.cullable.clear(),this.mesh.clear()}},{key:"computeVisibilityWithPlaneMask",value:function(t,e,n){if(e===L["b"].OUTSIDE||e===L["b"].INSIDE)return e;for(var r=L["b"].INSIDE,i=0,a=n.length;i<a;++i){var o=i<31?1<<i:0;if(!(i<31&&0===(e&o))){var s=n[i],u=s.normal,c=s.distance;if(P["e"].dot(u,t.getNegativeFarPoint(n[i]))+c>0)return L["b"].OUTSIDE;P["e"].dot(u,t.getPositiveFarPoint(n[i]))+c>0&&(r|=o)}}return r}}]),t}(),l=g,h=N()(l.prototype,"mesh",[i],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),f=N()(l.prototype,"cullable",[a],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),p=N()(l.prototype,"geometry",[o],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),d=N()(l.prototype,"hierarchy",[s],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),E=N()(l.prototype,"transform",[u],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),c=l))||c)},"93ac":function(t,e,n){"use strict";var r;n.d(e,"a",(function(){return r})),function(t){t[t["DEPTH_BUFFER_BIT"]=256]="DEPTH_BUFFER_BIT",t[t["STENCIL_BUFFER_BIT"]=1024]="STENCIL_BUFFER_BIT",t[t["COLOR_BUFFER_BIT"]=16384]="COLOR_BUFFER_BIT",t[t["POINTS"]=0]="POINTS",t[t["LINES"]=1]="LINES",t[t["LINE_LOOP"]=2]="LINE_LOOP",t[t["LINE_STRIP"]=3]="LINE_STRIP",t[t["TRIANGLES"]=4]="TRIANGLES",t[t["TRIANGLE_STRIP"]=5]="TRIANGLE_STRIP",t[t["TRIANGLE_FAN"]=6]="TRIANGLE_FAN",t[t["ZERO"]=0]="ZERO",t[t["ONE"]=1]="ONE",t[t["SRC_COLOR"]=768]="SRC_COLOR",t[t["ONE_MINUS_SRC_COLOR"]=769]="ONE_MINUS_SRC_COLOR",t[t["SRC_ALPHA"]=770]="SRC_ALPHA",t[t["ONE_MINUS_SRC_ALPHA"]=771]="ONE_MINUS_SRC_ALPHA",t[t["DST_ALPHA"]=772]="DST_ALPHA",t[t["ONE_MINUS_DST_ALPHA"]=773]="ONE_MINUS_DST_ALPHA",t[t["DST_COLOR"]=774]="DST_COLOR",t[t["ONE_MINUS_DST_COLOR"]=775]="ONE_MINUS_DST_COLOR",t[t["SRC_ALPHA_SATURATE"]=776]="SRC_ALPHA_SATURATE",t[t["FUNC_ADD"]=32774]="FUNC_ADD",t[t["BLEND_EQUATION"]=32777]="BLEND_EQUATION",t[t["BLEND_EQUATION_RGB"]=32777]="BLEND_EQUATION_RGB",t[t["BLEND_EQUATION_ALPHA"]=34877]="BLEND_EQUATION_ALPHA",t[t["FUNC_SUBTRACT"]=32778]="FUNC_SUBTRACT",t[t["FUNC_REVERSE_SUBTRACT"]=32779]="FUNC_REVERSE_SUBTRACT",t[t["MAX_EXT"]=32776]="MAX_EXT",t[t["MIN_EXT"]=32775]="MIN_EXT",t[t["BLEND_DST_RGB"]=32968]="BLEND_DST_RGB",t[t["BLEND_SRC_RGB"]=32969]="BLEND_SRC_RGB",t[t["BLEND_DST_ALPHA"]=32970]="BLEND_DST_ALPHA",t[t["BLEND_SRC_ALPHA"]=32971]="BLEND_SRC_ALPHA",t[t["CONSTANT_COLOR"]=32769]="CONSTANT_COLOR",t[t["ONE_MINUS_CONSTANT_COLOR"]=32770]="ONE_MINUS_CONSTANT_COLOR",t[t["CONSTANT_ALPHA"]=32771]="CONSTANT_ALPHA",t[t["ONE_MINUS_CONSTANT_ALPHA"]=32772]="ONE_MINUS_CONSTANT_ALPHA",t[t["BLEND_COLOR"]=32773]="BLEND_COLOR",t[t["ARRAY_BUFFER"]=34962]="ARRAY_BUFFER",t[t["ELEMENT_ARRAY_BUFFER"]=34963]="ELEMENT_ARRAY_BUFFER",t[t["ARRAY_BUFFER_BINDING"]=34964]="ARRAY_BUFFER_BINDING",t[t["ELEMENT_ARRAY_BUFFER_BINDING"]=34965]="ELEMENT_ARRAY_BUFFER_BINDING",t[t["STREAM_DRAW"]=35040]="STREAM_DRAW",t[t["STATIC_DRAW"]=35044]="STATIC_DRAW",t[t["DYNAMIC_DRAW"]=35048]="DYNAMIC_DRAW",t[t["BUFFER_SIZE"]=34660]="BUFFER_SIZE",t[t["BUFFER_USAGE"]=34661]="BUFFER_USAGE",t[t["CURRENT_VERTEX_ATTRIB"]=34342]="CURRENT_VERTEX_ATTRIB",t[t["FRONT"]=1028]="FRONT",t[t["BACK"]=1029]="BACK",t[t["FRONT_AND_BACK"]=1032]="FRONT_AND_BACK",t[t["CULL_FACE"]=2884]="CULL_FACE",t[t["BLEND"]=3042]="BLEND",t[t["DITHER"]=3024]="DITHER",t[t["STENCIL_TEST"]=2960]="STENCIL_TEST",t[t["DEPTH_TEST"]=2929]="DEPTH_TEST",t[t["SCISSOR_TEST"]=3089]="SCISSOR_TEST",t[t["POLYGON_OFFSET_FILL"]=32823]="POLYGON_OFFSET_FILL",t[t["SAMPLE_ALPHA_TO_COVERAGE"]=32926]="SAMPLE_ALPHA_TO_COVERAGE",t[t["SAMPLE_COVERAGE"]=32928]="SAMPLE_COVERAGE",t[t["NO_ERROR"]=0]="NO_ERROR",t[t["INVALID_ENUM"]=1280]="INVALID_ENUM",t[t["INVALID_VALUE"]=1281]="INVALID_VALUE",t[t["INVALID_OPERATION"]=1282]="INVALID_OPERATION",t[t["OUT_OF_MEMORY"]=1285]="OUT_OF_MEMORY",t[t["CW"]=2304]="CW",t[t["CCW"]=2305]="CCW",t[t["LINE_WIDTH"]=2849]="LINE_WIDTH",t[t["ALIASED_POINT_SIZE_RANGE"]=33901]="ALIASED_POINT_SIZE_RANGE",t[t["ALIASED_LINE_WIDTH_RANGE"]=33902]="ALIASED_LINE_WIDTH_RANGE",t[t["CULL_FACE_MODE"]=2885]="CULL_FACE_MODE",t[t["FRONT_FACE"]=2886]="FRONT_FACE",t[t["DEPTH_RANGE"]=2928]="DEPTH_RANGE",t[t["DEPTH_WRITEMASK"]=2930]="DEPTH_WRITEMASK",t[t["DEPTH_CLEAR_VALUE"]=2931]="DEPTH_CLEAR_VALUE",t[t["DEPTH_FUNC"]=2932]="DEPTH_FUNC",t[t["STENCIL_CLEAR_VALUE"]=2961]="STENCIL_CLEAR_VALUE",t[t["STENCIL_FUNC"]=2962]="STENCIL_FUNC",t[t["STENCIL_FAIL"]=2964]="STENCIL_FAIL",t[t["STENCIL_PASS_DEPTH_FAIL"]=2965]="STENCIL_PASS_DEPTH_FAIL",t[t["STENCIL_PASS_DEPTH_PASS"]=2966]="STENCIL_PASS_DEPTH_PASS",t[t["STENCIL_REF"]=2967]="STENCIL_REF",t[t["STENCIL_VALUE_MASK"]=2963]="STENCIL_VALUE_MASK",t[t["STENCIL_WRITEMASK"]=2968]="STENCIL_WRITEMASK",t[t["STENCIL_BACK_FUNC"]=34816]="STENCIL_BACK_FUNC",t[t["STENCIL_BACK_FAIL"]=34817]="STENCIL_BACK_FAIL",t[t["STENCIL_BACK_PASS_DEPTH_FAIL"]=34818]="STENCIL_BACK_PASS_DEPTH_FAIL",t[t["STENCIL_BACK_PASS_DEPTH_PASS"]=34819]="STENCIL_BACK_PASS_DEPTH_PASS",t[t["STENCIL_BACK_REF"]=36003]="STENCIL_BACK_REF",t[t["STENCIL_BACK_VALUE_MASK"]=36004]="STENCIL_BACK_VALUE_MASK",t[t["STENCIL_BACK_WRITEMASK"]=36005]="STENCIL_BACK_WRITEMASK",t[t["VIEWPORT"]=2978]="VIEWPORT",t[t["SCISSOR_BOX"]=3088]="SCISSOR_BOX",t[t["COLOR_CLEAR_VALUE"]=3106]="COLOR_CLEAR_VALUE",t[t["COLOR_WRITEMASK"]=3107]="COLOR_WRITEMASK",t[t["UNPACK_ALIGNMENT"]=3317]="UNPACK_ALIGNMENT",t[t["PACK_ALIGNMENT"]=3333]="PACK_ALIGNMENT",t[t["MAX_TEXTURE_SIZE"]=3379]="MAX_TEXTURE_SIZE",t[t["MAX_VIEWPORT_DIMS"]=3386]="MAX_VIEWPORT_DIMS",t[t["SUBPIXEL_BITS"]=3408]="SUBPIXEL_BITS",t[t["RED_BITS"]=3410]="RED_BITS",t[t["GREEN_BITS"]=3411]="GREEN_BITS",t[t["BLUE_BITS"]=3412]="BLUE_BITS",t[t["ALPHA_BITS"]=3413]="ALPHA_BITS",t[t["DEPTH_BITS"]=3414]="DEPTH_BITS",t[t["STENCIL_BITS"]=3415]="STENCIL_BITS",t[t["POLYGON_OFFSET_UNITS"]=10752]="POLYGON_OFFSET_UNITS",t[t["POLYGON_OFFSET_FACTOR"]=32824]="POLYGON_OFFSET_FACTOR",t[t["TEXTURE_BINDING_2D"]=32873]="TEXTURE_BINDING_2D",t[t["SAMPLE_BUFFERS"]=32936]="SAMPLE_BUFFERS",t[t["SAMPLES"]=32937]="SAMPLES",t[t["SAMPLE_COVERAGE_VALUE"]=32938]="SAMPLE_COVERAGE_VALUE",t[t["SAMPLE_COVERAGE_INVERT"]=32939]="SAMPLE_COVERAGE_INVERT",t[t["COMPRESSED_TEXTURE_FORMATS"]=34467]="COMPRESSED_TEXTURE_FORMATS",t[t["DONT_CARE"]=4352]="DONT_CARE",t[t["FASTEST"]=4353]="FASTEST",t[t["NICEST"]=4354]="NICEST",t[t["GENERATE_MIPMAP_HINT"]=33170]="GENERATE_MIPMAP_HINT",t[t["BYTE"]=5120]="BYTE",t[t["UNSIGNED_BYTE"]=5121]="UNSIGNED_BYTE",t[t["SHORT"]=5122]="SHORT",t[t["UNSIGNED_SHORT"]=5123]="UNSIGNED_SHORT",t[t["INT"]=5124]="INT",t[t["UNSIGNED_INT"]=5125]="UNSIGNED_INT",t[t["FLOAT"]=5126]="FLOAT",t[t["DEPTH_COMPONENT"]=6402]="DEPTH_COMPONENT",t[t["ALPHA"]=6406]="ALPHA",t[t["RGB"]=6407]="RGB",t[t["RGBA"]=6408]="RGBA",t[t["LUMINANCE"]=6409]="LUMINANCE",t[t["LUMINANCE_ALPHA"]=6410]="LUMINANCE_ALPHA",t[t["UNSIGNED_SHORT_4_4_4_4"]=32819]="UNSIGNED_SHORT_4_4_4_4",t[t["UNSIGNED_SHORT_5_5_5_1"]=32820]="UNSIGNED_SHORT_5_5_5_1",t[t["UNSIGNED_SHORT_5_6_5"]=33635]="UNSIGNED_SHORT_5_6_5",t[t["FRAGMENT_SHADER"]=35632]="FRAGMENT_SHADER",t[t["VERTEX_SHADER"]=35633]="VERTEX_SHADER",t[t["MAX_VERTEX_ATTRIBS"]=34921]="MAX_VERTEX_ATTRIBS",t[t["MAX_VERTEX_UNIFORM_VECTORS"]=36347]="MAX_VERTEX_UNIFORM_VECTORS",t[t["MAX_VARYING_VECTORS"]=36348]="MAX_VARYING_VECTORS",t[t["MAX_COMBINED_TEXTURE_IMAGE_UNITS"]=35661]="MAX_COMBINED_TEXTURE_IMAGE_UNITS",t[t["MAX_VERTEX_TEXTURE_IMAGE_UNITS"]=35660]="MAX_VERTEX_TEXTURE_IMAGE_UNITS",t[t["MAX_TEXTURE_IMAGE_UNITS"]=34930]="MAX_TEXTURE_IMAGE_UNITS",t[t["MAX_FRAGMENT_UNIFORM_VECTORS"]=36349]="MAX_FRAGMENT_UNIFORM_VECTORS",t[t["SHADER_TYPE"]=35663]="SHADER_TYPE",t[t["DELETE_STATUS"]=35712]="DELETE_STATUS",t[t["LINK_STATUS"]=35714]="LINK_STATUS",t[t["VALIDATE_STATUS"]=35715]="VALIDATE_STATUS",t[t["ATTACHED_SHADERS"]=35717]="ATTACHED_SHADERS",t[t["ACTIVE_UNIFORMS"]=35718]="ACTIVE_UNIFORMS",t[t["ACTIVE_ATTRIBUTES"]=35721]="ACTIVE_ATTRIBUTES",t[t["SHADING_LANGUAGE_VERSION"]=35724]="SHADING_LANGUAGE_VERSION",t[t["CURRENT_PROGRAM"]=35725]="CURRENT_PROGRAM",t[t["NEVER"]=512]="NEVER",t[t["LESS"]=513]="LESS",t[t["EQUAL"]=514]="EQUAL",t[t["LEQUAL"]=515]="LEQUAL",t[t["GREATER"]=516]="GREATER",t[t["NOTEQUAL"]=517]="NOTEQUAL",t[t["GEQUAL"]=518]="GEQUAL",t[t["ALWAYS"]=519]="ALWAYS",t[t["KEEP"]=7680]="KEEP",t[t["REPLACE"]=7681]="REPLACE",t[t["INCR"]=7682]="INCR",t[t["DECR"]=7683]="DECR",t[t["INVERT"]=5386]="INVERT",t[t["INCR_WRAP"]=34055]="INCR_WRAP",t[t["DECR_WRAP"]=34056]="DECR_WRAP",t[t["VENDOR"]=7936]="VENDOR",t[t["RENDERER"]=7937]="RENDERER",t[t["VERSION"]=7938]="VERSION",t[t["NEAREST"]=9728]="NEAREST",t[t["LINEAR"]=9729]="LINEAR",t[t["NEAREST_MIPMAP_NEAREST"]=9984]="NEAREST_MIPMAP_NEAREST",t[t["LINEAR_MIPMAP_NEAREST"]=9985]="LINEAR_MIPMAP_NEAREST",t[t["NEAREST_MIPMAP_LINEAR"]=9986]="NEAREST_MIPMAP_LINEAR",t[t["LINEAR_MIPMAP_LINEAR"]=9987]="LINEAR_MIPMAP_LINEAR",t[t["TEXTURE_MAG_FILTER"]=10240]="TEXTURE_MAG_FILTER",t[t["TEXTURE_MIN_FILTER"]=10241]="TEXTURE_MIN_FILTER",t[t["TEXTURE_WRAP_S"]=10242]="TEXTURE_WRAP_S",t[t["TEXTURE_WRAP_T"]=10243]="TEXTURE_WRAP_T",t[t["TEXTURE_2D"]=3553]="TEXTURE_2D",t[t["TEXTURE"]=5890]="TEXTURE",t[t["TEXTURE_CUBE_MAP"]=34067]="TEXTURE_CUBE_MAP",t[t["TEXTURE_BINDING_CUBE_MAP"]=34068]="TEXTURE_BINDING_CUBE_MAP",t[t["TEXTURE_CUBE_MAP_POSITIVE_X"]=34069]="TEXTURE_CUBE_MAP_POSITIVE_X",t[t["TEXTURE_CUBE_MAP_NEGATIVE_X"]=34070]="TEXTURE_CUBE_MAP_NEGATIVE_X",t[t["TEXTURE_CUBE_MAP_POSITIVE_Y"]=34071]="TEXTURE_CUBE_MAP_POSITIVE_Y",t[t["TEXTURE_CUBE_MAP_NEGATIVE_Y"]=34072]="TEXTURE_CUBE_MAP_NEGATIVE_Y",t[t["TEXTURE_CUBE_MAP_POSITIVE_Z"]=34073]="TEXTURE_CUBE_MAP_POSITIVE_Z",t[t["TEXTURE_CUBE_MAP_NEGATIVE_Z"]=34074]="TEXTURE_CUBE_MAP_NEGATIVE_Z",t[t["MAX_CUBE_MAP_TEXTURE_SIZE"]=34076]="MAX_CUBE_MAP_TEXTURE_SIZE",t[t["TEXTURE0"]=33984]="TEXTURE0",t[t["TEXTURE1"]=33985]="TEXTURE1",t[t["TEXTURE2"]=33986]="TEXTURE2",t[t["TEXTURE3"]=33987]="TEXTURE3",t[t["TEXTURE4"]=33988]="TEXTURE4",t[t["TEXTURE5"]=33989]="TEXTURE5",t[t["TEXTURE6"]=33990]="TEXTURE6",t[t["TEXTURE7"]=33991]="TEXTURE7",t[t["TEXTURE8"]=33992]="TEXTURE8",t[t["TEXTURE9"]=33993]="TEXTURE9",t[t["TEXTURE10"]=33994]="TEXTURE10",t[t["TEXTURE11"]=33995]="TEXTURE11",t[t["TEXTURE12"]=33996]="TEXTURE12",t[t["TEXTURE13"]=33997]="TEXTURE13",t[t["TEXTURE14"]=33998]="TEXTURE14",t[t["TEXTURE15"]=33999]="TEXTURE15",t[t["TEXTURE16"]=34e3]="TEXTURE16",t[t["TEXTURE17"]=34001]="TEXTURE17",t[t["TEXTURE18"]=34002]="TEXTURE18",t[t["TEXTURE19"]=34003]="TEXTURE19",t[t["TEXTURE20"]=34004]="TEXTURE20",t[t["TEXTURE21"]=34005]="TEXTURE21",t[t["TEXTURE22"]=34006]="TEXTURE22",t[t["TEXTURE23"]=34007]="TEXTURE23",t[t["TEXTURE24"]=34008]="TEXTURE24",t[t["TEXTURE25"]=34009]="TEXTURE25",t[t["TEXTURE26"]=34010]="TEXTURE26",t[t["TEXTURE27"]=34011]="TEXTURE27",t[t["TEXTURE28"]=34012]="TEXTURE28",t[t["TEXTURE29"]=34013]="TEXTURE29",t[t["TEXTURE30"]=34014]="TEXTURE30",t[t["TEXTURE31"]=34015]="TEXTURE31",t[t["ACTIVE_TEXTURE"]=34016]="ACTIVE_TEXTURE",t[t["REPEAT"]=10497]="REPEAT",t[t["CLAMP_TO_EDGE"]=33071]="CLAMP_TO_EDGE",t[t["MIRRORED_REPEAT"]=33648]="MIRRORED_REPEAT",t[t["FLOAT_VEC2"]=35664]="FLOAT_VEC2",t[t["FLOAT_VEC3"]=35665]="FLOAT_VEC3",t[t["FLOAT_VEC4"]=35666]="FLOAT_VEC4",t[t["INT_VEC2"]=35667]="INT_VEC2",t[t["INT_VEC3"]=35668]="INT_VEC3",t[t["INT_VEC4"]=35669]="INT_VEC4",t[t["BOOL"]=35670]="BOOL",t[t["BOOL_VEC2"]=35671]="BOOL_VEC2",t[t["BOOL_VEC3"]=35672]="BOOL_VEC3",t[t["BOOL_VEC4"]=35673]="BOOL_VEC4",t[t["FLOAT_MAT2"]=35674]="FLOAT_MAT2",t[t["FLOAT_MAT3"]=35675]="FLOAT_MAT3",t[t["FLOAT_MAT4"]=35676]="FLOAT_MAT4",t[t["SAMPLER_2D"]=35678]="SAMPLER_2D",t[t["SAMPLER_CUBE"]=35680]="SAMPLER_CUBE",t[t["VERTEX_ATTRIB_ARRAY_ENABLED"]=34338]="VERTEX_ATTRIB_ARRAY_ENABLED",t[t["VERTEX_ATTRIB_ARRAY_SIZE"]=34339]="VERTEX_ATTRIB_ARRAY_SIZE",t[t["VERTEX_ATTRIB_ARRAY_STRIDE"]=34340]="VERTEX_ATTRIB_ARRAY_STRIDE",t[t["VERTEX_ATTRIB_ARRAY_TYPE"]=34341]="VERTEX_ATTRIB_ARRAY_TYPE",t[t["VERTEX_ATTRIB_ARRAY_NORMALIZED"]=34922]="VERTEX_ATTRIB_ARRAY_NORMALIZED",t[t["VERTEX_ATTRIB_ARRAY_POINTER"]=34373]="VERTEX_ATTRIB_ARRAY_POINTER",t[t["VERTEX_ATTRIB_ARRAY_BUFFER_BINDING"]=34975]="VERTEX_ATTRIB_ARRAY_BUFFER_BINDING",t[t["COMPILE_STATUS"]=35713]="COMPILE_STATUS",t[t["LOW_FLOAT"]=36336]="LOW_FLOAT",t[t["MEDIUM_FLOAT"]=36337]="MEDIUM_FLOAT",t[t["HIGH_FLOAT"]=36338]="HIGH_FLOAT",t[t["LOW_INT"]=36339]="LOW_INT",t[t["MEDIUM_INT"]=36340]="MEDIUM_INT",t[t["HIGH_INT"]=36341]="HIGH_INT",t[t["FRAMEBUFFER"]=36160]="FRAMEBUFFER",t[t["RENDERBUFFER"]=36161]="RENDERBUFFER",t[t["RGBA4"]=32854]="RGBA4",t[t["RGB5_A1"]=32855]="RGB5_A1",t[t["RGB565"]=36194]="RGB565",t[t["DEPTH_COMPONENT16"]=33189]="DEPTH_COMPONENT16",t[t["STENCIL_INDEX"]=6401]="STENCIL_INDEX",t[t["STENCIL_INDEX8"]=36168]="STENCIL_INDEX8",t[t["DEPTH_STENCIL"]=34041]="DEPTH_STENCIL",t[t["RENDERBUFFER_WIDTH"]=36162]="RENDERBUFFER_WIDTH",t[t["RENDERBUFFER_HEIGHT"]=36163]="RENDERBUFFER_HEIGHT",t[t["RENDERBUFFER_INTERNAL_FORMAT"]=36164]="RENDERBUFFER_INTERNAL_FORMAT",t[t["RENDERBUFFER_RED_SIZE"]=36176]="RENDERBUFFER_RED_SIZE",t[t["RENDERBUFFER_GREEN_SIZE"]=36177]="RENDERBUFFER_GREEN_SIZE",t[t["RENDERBUFFER_BLUE_SIZE"]=36178]="RENDERBUFFER_BLUE_SIZE",t[t["RENDERBUFFER_ALPHA_SIZE"]=36179]="RENDERBUFFER_ALPHA_SIZE",t[t["RENDERBUFFER_DEPTH_SIZE"]=36180]="RENDERBUFFER_DEPTH_SIZE",t[t["RENDERBUFFER_STENCIL_SIZE"]=36181]="RENDERBUFFER_STENCIL_SIZE",t[t["FRAMEBUFFER_ATTACHMENT_OBJECT_TYPE"]=36048]="FRAMEBUFFER_ATTACHMENT_OBJECT_TYPE",t[t["FRAMEBUFFER_ATTACHMENT_OBJECT_NAME"]=36049]="FRAMEBUFFER_ATTACHMENT_OBJECT_NAME",t[t["FRAMEBUFFER_ATTACHMENT_TEXTURE_LEVEL"]=36050]="FRAMEBUFFER_ATTACHMENT_TEXTURE_LEVEL",t[t["FRAMEBUFFER_ATTACHMENT_TEXTURE_CUBE_MAP_FACE"]=36051]="FRAMEBUFFER_ATTACHMENT_TEXTURE_CUBE_MAP_FACE",t[t["COLOR_ATTACHMENT0"]=36064]="COLOR_ATTACHMENT0",t[t["DEPTH_ATTACHMENT"]=36096]="DEPTH_ATTACHMENT",t[t["STENCIL_ATTACHMENT"]=36128]="STENCIL_ATTACHMENT",t[t["DEPTH_STENCIL_ATTACHMENT"]=33306]="DEPTH_STENCIL_ATTACHMENT",t[t["NONE"]=0]="NONE",t[t["FRAMEBUFFER_COMPLETE"]=36053]="FRAMEBUFFER_COMPLETE",t[t["FRAMEBUFFER_INCOMPLETE_ATTACHMENT"]=36054]="FRAMEBUFFER_INCOMPLETE_ATTACHMENT",t[t["FRAMEBUFFER_INCOMPLETE_MISSING_ATTACHMENT"]=36055]="FRAMEBUFFER_INCOMPLETE_MISSING_ATTACHMENT",t[t["FRAMEBUFFER_INCOMPLETE_DIMENSIONS"]=36057]="FRAMEBUFFER_INCOMPLETE_DIMENSIONS",t[t["FRAMEBUFFER_UNSUPPORTED"]=36061]="FRAMEBUFFER_UNSUPPORTED",t[t["FRAMEBUFFER_BINDING"]=36006]="FRAMEBUFFER_BINDING",t[t["RENDERBUFFER_BINDING"]=36007]="RENDERBUFFER_BINDING",t[t["MAX_RENDERBUFFER_SIZE"]=34024]="MAX_RENDERBUFFER_SIZE",t[t["INVALID_FRAMEBUFFER_OPERATION"]=1286]="INVALID_FRAMEBUFFER_OPERATION",t[t["UNPACK_FLIP_Y_WEBGL"]=37440]="UNPACK_FLIP_Y_WEBGL",t[t["UNPACK_PREMULTIPLY_ALPHA_WEBGL"]=37441]="UNPACK_PREMULTIPLY_ALPHA_WEBGL",t[t["CONTEXT_LOST_WEBGL"]=37442]="CONTEXT_LOST_WEBGL",t[t["UNPACK_COLORSPACE_CONVERSION_WEBGL"]=37443]="UNPACK_COLORSPACE_CONVERSION_WEBGL",t[t["BROWSER_DEFAULT_WEBGL"]=37444]="BROWSER_DEFAULT_WEBGL",t[t["COPY_SRC"]=1]="COPY_SRC",t[t["COPY_DST"]=2]="COPY_DST",t[t["SAMPLED"]=4]="SAMPLED",t[t["STORAGE"]=8]="STORAGE",t[t["RENDER_ATTACHMENT"]=16]="RENDER_ATTACHMENT"}(r||(r={}))},9633:function(t,e,n){"use strict";n.d(e,"a",(function(){return r}));var r="undefined"!==typeof navigator&&/Version\/[\d\.]+.*Safari/.test(navigator.userAgent)},"9cee":function(t,e){},a03b:function(t,e,n){"use strict";n.d(e,"a",(function(){return f})),n.d(e,"b",(function(){return p}));var r=n("a34a"),i=n.n(r),a=n("c973"),o=n.n(a),s=n("5bc3"),u=n.n(s),c=n("970b"),l=n.n(c),h=n("f72d"),f=function t(e){l()(this,t)},p=function(){function t(e){l()(this,t),this.clazz=void 0,this.components=[],this.entities=[],this.lookup={},this.clazz=e}return u()(t,[{key:"clear",value:function(){this.components=[],this.entities=[],this.lookup={}}},{key:"contains",value:function(t){return this.lookup[t]>-1}},{key:"create",value:function(t,e){this.lookup[t]=this.components.length;var n=new this.clazz(e||{});return this.components.push(n),this.entities.push(t),n}},{key:"remove",value:function(t){var e=this.lookup[t];e>-1&&e<this.components.length-1&&(this.components[e]=this.components[this.components.length-1],this.entities[e]=this.entities[this.entities.length-1],this.lookup[this.entities[e]]=e),this.components.pop(),this.entities.pop(),delete this.lookup[t]}},{key:"removeKeepSorted",value:function(t){var e=this.lookup[t];if(e>-1){var n=this.entities[e];if(e<this.components.length-1){for(var r=e+1;r<this.components.length;++r)this.components[r-1]=this.components[r];for(var i=e+1;i<this.entities.length;++i)this.entities[i-1]=this.entities[i],this.lookup[this.entities[i-1]]=i-1}this.components.pop(),this.entities.pop(),delete this.lookup[n]}}},{key:"moveItem",value:function(t,e){if(t!==e){for(var n=this.components[t],r=this.entities[t],i=t<e?1:-1,a=t;a!==e;a+=i){var o=a+i;this.components[a]=this.components[o],this.entities[a]=this.entities[o],this.lookup[this.entities[a]]=a}this.components[e]=n,this.entities[e]=r,this.lookup[r]=e}}},{key:"getEntity",value:function(t){return this.entities[t]}},{key:"getComponent",value:function(t){return this.components[t]}},{key:"getComponentByEntity",value:function(t){var e=this.lookup[t];return e>-1?this.components[e]:null}},{key:"getCount",value:function(){return this.components.length}},{key:"getEntityByComponentIndex",value:function(t){for(var e=0,n=Object.keys(this.lookup);e<n.length;e++){var r=n[e],i=Number(r);if(this.lookup[i]===t)return i}return h["a"]}},{key:"find",value:function(t){for(var e=0;e<this.getCount();e++){var n=this.getComponent(e);if(t(n,e))return n}return null}},{key:"findIndex",value:function(t){for(var e=0;e<this.getCount();e++){var n=this.getComponent(e);if(t(n,e))return e}return-1}},{key:"forEach",value:function(t){for(var e=0,n=Object.keys(this.lookup);e<n.length;e++){var r=n[e],i=Number(r),a=this.lookup[i];t(i,this.getComponent(a))}}},{key:"forEachAsync",value:function(){var t=o()(i.a.mark((function t(e){var n,r,a,o,s;return i.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:n=0,r=Object.keys(this.lookup);case 1:if(!(n<r.length)){t.next=10;break}return a=r[n],o=Number(a),s=this.lookup[o],t.next=7,e(o,this.getComponent(s));case 7:n++,t.next=1;break;case 10:case"end":return t.stop()}}),t,this)})));function e(e){return t.apply(this,arguments)}return e}()},{key:"map",value:function(t){for(var e=[],n=0,r=Object.keys(this.lookup);n<r.length;n++){var i=r[n],a=Number(i),o=this.lookup[a];e.push(t(a,this.getComponent(o)))}return e}}]),t}()},a256:function(t,e){},a61d:function(t,e,n){"use strict";n.d(e,"a",(function(){return _}));var r=n("9523"),i=n.n(r),a=n("970b"),o=n.n(a),s=n("5bc3"),u=n.n(s),c=n("3c96"),l=n.n(c),h=n("ed6d"),f=n.n(h),p=n("6b58"),d=n.n(p),E=n("36c6"),g=n.n(E),m=n("1a3d"),v=n("a03b");function y(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function T(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?y(Object(n),!0).forEach((function(e){i()(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):y(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function b(t){var e=A();return function(){var n,r=g()(t);if(e){var i=g()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return d()(this,n)}}function A(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}var _=function(t){f()(n,t);var e=b(n);function n(t){var r;return o()(this,n),r=e.call(this,t),r.vertexShaderGLSL=void 0,r.fragmentShaderGLSL=void 0,r.defines={},r.dirty=!0,r.uniforms=[],r.cull={enable:!0,face:m["gl"].BACK},r.depth={enable:!0},r.blend=void 0,r.entity=void 0,r.type=void 0,Object.assign(l()(r),t),r}return u()(n,[{key:"setDefines",value:function(t){return this.defines=T(T({},this.defines),t),this}},{key:"setCull",value:function(t){return this.cull=t,this}},{key:"setDepth",value:function(t){return this.depth=t,this}},{key:"setBlend",value:function(t){return this.blend=t,this}},{key:"setUniform",value:function(t,e){var n=this;if("string"!==typeof t)return Object.keys(t).forEach((function(e){return n.setUniform(e,t[e])})),this;var r=this.uniforms.find((function(e){return e.name===t}));return r?(r.dirty=!0,r.data=e):this.uniforms.push({name:t,dirty:!0,data:e}),this.dirty=!0,this}}]),n}(v["a"])},a9fb:function(t,e,n){"use strict";n.d(e,"b",(function(){return r})),n.d(e,"a",(function(){return f}));var r,i=n("278c"),a=n.n(i),o=n("970b"),s=n.n(o),u=n("5bc3"),c=n.n(u),l=n("20e7"),h=n("7175");(function(t){t[t["OUTSIDE"]=4294967295]="OUTSIDE",t[t["INSIDE"]=0]="INSIDE",t[t["INDETERMINATE"]=2147483647]="INDETERMINATE"})(r||(r={}));var f=function(){function t(e){if(s()(this,t),this.planes=[],e)this.planes=e;else for(var n=0;n<6;n++)this.planes.push(new h["a"])}return c()(t,[{key:"extractFromVPMatrix",value:function(t){var e=a()(t,16),n=e[0],r=e[1],i=e[2],o=e[3],s=e[4],u=e[5],c=e[6],h=e[7],f=e[8],p=e[9],d=e[10],E=e[11],g=e[12],m=e[13],v=e[14],y=e[15];l["e"].set(this.planes[0].normal,o-n,h-s,E-f),this.planes[0].distance=-(y-g),l["e"].set(this.planes[1].normal,o+n,h+s,E+f),this.planes[1].distance=-(y+g),l["e"].set(this.planes[2].normal,o+r,h+u,E+p),this.planes[2].distance=-(y+m),l["e"].set(this.planes[3].normal,o-r,h-u,E-p),this.planes[3].distance=-(y-m),l["e"].set(this.planes[4].normal,o-i,h-c,E-d),this.planes[4].distance=-(y-v),l["e"].set(this.planes[5].normal,o+i,h+c,E+d),this.planes[5].distance=-(y+v),this.planes.forEach((function(t){t.normalize(),t.updatePNVertexFlag()}))}}]),t}()},b72b:function(t,e,n){"use strict";n.d(e,"a",(function(){return w}));var r,i,a,o,s,u,c,l,h,f,p,d,E,g=n("a34a"),m=n.n(g),v=n("c973"),y=n.n(v),T=n("c86f"),b=n.n(T),A=n("970b"),_=n.n(A),R=n("5bc3"),S=n.n(R),C=n("53ec"),O=n.n(C),N=(n("d400"),n("e1c6")),P=n("fae1"),x=n("8350"),I=n("1651"),L=n("f781"),w=(r=Object(N["injectable"])(),i=Object(N["inject"])(P["a"].Systems),a=Object(N["named"])(P["a"].FrameGraphSystem),o=Object(N["inject"])(P["a"].RenderPassFactory),s=Object(N["inject"])(P["a"].ConfigService),u=Object(N["inject"])(P["a"].ResourcePool),r((E=function(){function t(){_()(this,t),b()(this,"frameGraphSystem",h,this),b()(this,"renderPassFactory",f,this),b()(this,"configService",p,this),b()(this,"resourcePool",d,this)}return S()(t,[{key:"execute",value:function(){var t=y()(m.a.mark((function t(e){var n,r,i,a,o,s,u,c;return m.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:n=this.renderPassFactory(L["a"].IDENTIFIER),r=n.setup,i=n.execute,this.frameGraphSystem.addPass(L["a"].IDENTIFIER,r,i),a=this.renderPassFactory(x["a"].IDENTIFIER),o=a.setup,s=a.execute,u=a.tearDown,c=this.frameGraphSystem.addPass(x["a"].IDENTIFIER,o,s,u),this.frameGraphSystem.present(c.data.output);case 5:case"end":return t.stop()}}),t,this)})));function e(e){return t.apply(this,arguments)}return e}()},{key:"tearDown",value:function(){this.resourcePool.clean()}},{key:"pick",value:function(t,e){var n=this.renderPassFactory(I["a"].IDENTIFIER);return n.pick(t,e)}}]),t}(),l=E,h=O()(l.prototype,"frameGraphSystem",[i,a],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),f=O()(l.prototype,"renderPassFactory",[o],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),p=O()(l.prototype,"configService",[s],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),d=O()(l.prototype,"resourcePool",[u],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),c=l))||c)},bdd3:function(t,e){},bfb1:function(t,e,n){"use strict";n.d(e,"a",(function(){return _t}));var r={};n.r(r),n.d(r,"Base",(function(){return _})),n.d(r,"Circle",(function(){return S})),n.d(r,"Dom",(function(){return O})),n.d(r,"Ellipse",(function(){return P})),n.d(r,"Image",(function(){return I})),n.d(r,"Line",(function(){return D})),n.d(r,"Marker",(function(){return U})),n.d(r,"Path",(function(){return j})),n.d(r,"Polygon",(function(){return V})),n.d(r,"Polyline",(function(){return X})),n.d(r,"Rect",(function(){return Y})),n.d(r,"Text",(function(){return tt}));var i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},i(t,e)};function a(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}var o=function(){return o=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var i in e=arguments[n],e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t},o.apply(this,arguments)};Object.create;Object.create;var s=n("6855"),u=n("8937"),c={rect:"path",circle:"circle",line:"line",path:"path",marker:"path",text:"text",polyline:"polyline",polygon:"polygon",image:"image",ellipse:"ellipse",dom:"foreignObject"},l={opacity:"opacity",fillStyle:"fill",fill:"fill",fillOpacity:"fill-opacity",strokeStyle:"stroke",strokeOpacity:"stroke-opacity",stroke:"stroke",x:"x",y:"y",r:"r",rx:"rx",ry:"ry",width:"width",height:"height",x1:"x1",x2:"x2",y1:"y1",y2:"y2",lineCap:"stroke-linecap",lineJoin:"stroke-linejoin",lineWidth:"stroke-width",lineDash:"stroke-dasharray",lineDashOffset:"stroke-dashoffset",miterLimit:"stroke-miterlimit",font:"font",fontSize:"font-size",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",fontFamily:"font-family",startArrow:"marker-start",endArrow:"marker-end",path:"d",class:"class",id:"id",style:"style",preserveAspectRatio:"preserveAspectRatio"};function h(t){return document.createElementNS("http://www.w3.org/2000/svg",t)}function f(t){var e=c[t.type],n=t.getParent();if(!e)throw new Error("the type "+t.type+" is not supported by svg");var r=h(e);if(t.get("id")&&(r.id=t.get("id")),t.set("el",r),t.set("attrs",{}),n){var i=n.get("el");i||(i=n.createDom(),n.set("el",i)),i.appendChild(r)}return r}function p(t,e){var n=t.get("el"),r=Object(u["toArray"])(n.children).sort(e),i=document.createDocumentFragment();r.forEach((function(t){i.appendChild(t)})),n.appendChild(i)}function d(t,e){var n=t.parentNode,r=Array.from(n.childNodes).filter((function(t){return 1===t.nodeType&&"defs"!==t.nodeName.toLowerCase()})),i=r[e],a=r.indexOf(t);if(i){if(a>e)n.insertBefore(t,i);else if(a<e){var o=r[e+1];o?n.insertBefore(t,o):n.appendChild(t)}}else n.appendChild(t)}function E(t,e){var n=t.cfg.el,r=t.attr(),i={dx:r.shadowOffsetX,dy:r.shadowOffsetY,blur:r.shadowBlur,color:r.shadowColor};if(i.dx||i.dy||i.blur||i.color){var a=e.find("filter",i);a||(a=e.addShadow(i)),n.setAttribute("filter","url(#"+a+")")}else n.removeAttribute("filter")}function g(t){var e=t.attr().matrix;if(e){for(var n=t.cfg.el,r=[],i=0;i<9;i+=3)r.push(e[i]+","+e[i+1]);r=r.join(","),-1===r.indexOf("NaN")?n.setAttribute("transform","matrix("+r+")"):console.warn("invalid matrix:",e)}}function m(t,e){var n=t.getClip(),r=t.get("el");if(n){if(n&&!r.hasAttribute("clip-path")){f(n),n.createPath(e);var i=e.addClip(n);r.setAttribute("clip-path","url(#"+i+")")}}else r.removeAttribute("clip-path")}function v(t,e){e.forEach((function(e){e.draw(t)}))}function y(t,e){var n=t.get("canvas");if(n&&n.get("autoDraw")){var r=n.get("context"),i=t.getParent(),a=i?i.getChildren():[n],o=t.get("el");if("remove"===e){var s=t.get("isClipShape");if(s){var u=o&&o.parentNode,c=u&&u.parentNode;u&&c&&c.removeChild(u)}else o&&o.parentNode&&o.parentNode.removeChild(o)}else if("show"===e)o.setAttribute("visibility","visible");else if("hide"===e)o.setAttribute("visibility","hidden");else if("zIndex"===e)d(o,a.indexOf(t));else if("sort"===e){var l=t.get("children");l&&l.length&&p(t,(function(t,e){return l.indexOf(t)-l.indexOf(e)?1:0}))}else"clear"===e?o&&(o.innerHTML=""):"matrix"===e?g(t):"clip"===e?m(t,r):"attr"===e||"add"===e&&t.draw(r)}}var T=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.isEntityGroup=function(){return!0},e.prototype.createDom=function(){var t=h("g");this.set("el",t);var e=this.getParent();if(e){var n=e.get("el");n||(n=e.createDom(),e.set("el",n)),n.appendChild(t)}return t},e.prototype.afterAttrsChange=function(e){t.prototype.afterAttrsChange.call(this,e);var n=this.get("canvas");if(n&&n.get("autoDraw")){var r=n.get("context");this.createPath(r,e)}},e.prototype.onCanvasChange=function(t){y(this,t)},e.prototype.getShapeBase=function(){return r},e.prototype.getGroupBase=function(){return e},e.prototype.draw=function(t){var e=this.getChildren(),n=this.get("el");this.get("destroyed")?n&&n.parentNode.removeChild(n):(n||this.createDom(),m(this,t),this.createPath(t),e.length&&v(t,e))},e.prototype.createPath=function(t,e){var n=this.attr(),r=this.get("el");Object(u["each"])(e||n,(function(t,e){l[e]&&r.setAttribute(l[e],t)})),g(this)},e}(s["b"]),b=T,A=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="svg",e.canFill=!1,e.canStroke=!1,e}return a(e,t),e.prototype.getDefaultAttrs=function(){var e=t.prototype.getDefaultAttrs.call(this);return o(o({},e),{lineWidth:1,lineAppendWidth:0,strokeOpacity:1,fillOpacity:1})},e.prototype.afterAttrsChange=function(e){t.prototype.afterAttrsChange.call(this,e);var n=this.get("canvas");if(n&&n.get("autoDraw")){var r=n.get("context");this.draw(r,e)}},e.prototype.getShapeBase=function(){return r},e.prototype.getGroupBase=function(){return b},e.prototype.onCanvasChange=function(t){y(this,t)},e.prototype.calculateBBox=function(){var t=this.get("el"),e=null;if(t)e=t.getBBox();else{var n=Object(s["g"])(this.get("type"));n&&(e=n(this))}if(e){var r=e.x,i=e.y,a=e.width,o=e.height,u=this.getHitLineWidth(),c=u/2,l=r-c,h=i-c,f=r+a+c,p=i+o+c;return{x:l,y:h,minX:l,minY:h,maxX:f,maxY:p,width:a+u,height:o+u}}return{x:0,y:0,minX:0,minY:0,maxX:0,maxY:0,width:0,height:0}},e.prototype.isFill=function(){var t=this.attr(),e=t.fill,n=t.fillStyle;return(e||n||this.isClipShape())&&this.canFill},e.prototype.isStroke=function(){var t=this.attr(),e=t.stroke,n=t.strokeStyle;return(e||n)&&this.canStroke},e.prototype.draw=function(t,e){var n=this.get("el");this.get("destroyed")?n&&n.parentNode.removeChild(n):(n||f(this),m(this,t),this.createPath(t,e),this.shadow(t,e),this.strokeAndFill(t,e),this.transform(e))},e.prototype.createPath=function(t,e){},e.prototype.strokeAndFill=function(t,e){var n=e||this.attr(),r=n.fill,i=n.fillStyle,a=n.stroke,o=n.strokeStyle,s=n.fillOpacity,u=n.strokeOpacity,c=n.lineWidth,h=this.get("el");this.canFill&&(e?"fill"in n?this._setColor(t,"fill",r):"fillStyle"in n&&this._setColor(t,"fill",i):this._setColor(t,"fill",r||i),s&&h.setAttribute(l["fillOpacity"],s)),this.canStroke&&c>0&&(e?"stroke"in n?this._setColor(t,"stroke",a):"strokeStyle"in n&&this._setColor(t,"stroke",o):this._setColor(t,"stroke",a||o),u&&h.setAttribute(l["strokeOpacity"],u),c&&h.setAttribute(l["lineWidth"],c))},e.prototype._setColor=function(t,e,n){var r=this.get("el");if(n)if(n=n.trim(),/^[r,R,L,l]{1}[\s]*\(/.test(n)){var i=t.find("gradient",n);i||(i=t.addGradient(n)),r.setAttribute(l[e],"url(#"+i+")")}else if(/^[p,P]{1}[\s]*\(/.test(n)){i=t.find("pattern",n);i||(i=t.addPattern(n)),r.setAttribute(l[e],"url(#"+i+")")}else r.setAttribute(l[e],n);else r.setAttribute(l[e],"none")},e.prototype.shadow=function(t,e){var n=this.attr(),r=e||n,i=r.shadowOffsetX,a=r.shadowOffsetY,o=r.shadowBlur,s=r.shadowColor;(i||a||o||s)&&E(this,t)},e.prototype.transform=function(t){var e=this.attr(),n=(t||e).matrix;n&&g(this)},e.prototype.isInShape=function(t,e){return this.isPointInPath(t,e)},e.prototype.isPointInPath=function(t,e){var n=this.get("el"),r=this.get("canvas"),i=r.get("el").getBoundingClientRect(),a=t+i.left,o=e+i.top,s=document.elementFromPoint(a,o);return!(!s||!s.isEqualNode(n))},e.prototype.getHitLineWidth=function(){var t=this.attrs,e=t.lineWidth,n=t.lineAppendWidth;return this.isStroke()?e+n:0},e}(s["c"]),_=A,R=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="circle",e.canFill=!0,e.canStroke=!0,e}return a(e,t),e.prototype.getDefaultAttrs=function(){var e=t.prototype.getDefaultAttrs.call(this);return o(o({},e),{x:0,y:0,r:0})},e.prototype.createPath=function(t,e){var n=this.attr(),r=this.get("el");Object(u["each"])(e||n,(function(t,e){"x"===e||"y"===e?r.setAttribute("c"+e,t):l[e]&&r.setAttribute(l[e],t)}))},e}(_),S=R,C=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="dom",e.canFill=!1,e.canStroke=!1,e}return a(e,t),e.prototype.createPath=function(t,e){var n=this.attr(),r=this.get("el");if(Object(u["each"])(e||n,(function(t,e){l[e]&&r.setAttribute(l[e],t)})),"function"===typeof n["html"]){var i=n["html"].call(this,n);if(i instanceof Element||i instanceof HTMLDocument){for(var a=r.childNodes,o=a.length-1;o>=0;o--)r.removeChild(a[o]);r.appendChild(i)}else r.innerHTML=i}else r.innerHTML=n["html"]},e}(_),O=C,N=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="ellipse",e.canFill=!0,e.canStroke=!0,e}return a(e,t),e.prototype.getDefaultAttrs=function(){var e=t.prototype.getDefaultAttrs.call(this);return o(o({},e),{x:0,y:0,rx:0,ry:0})},e.prototype.createPath=function(t,e){var n=this.attr(),r=this.get("el");Object(u["each"])(e||n,(function(t,e){"x"===e||"y"===e?r.setAttribute("c"+e,t):l[e]&&r.setAttribute(l[e],t)}))},e}(_),P=N,x=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="image",e.canFill=!1,e.canStroke=!1,e}return a(e,t),e.prototype.getDefaultAttrs=function(){var e=t.prototype.getDefaultAttrs.call(this);return o(o({},e),{x:0,y:0,width:0,height:0})},e.prototype.createPath=function(t,e){var n=this,r=this.attr(),i=this.get("el");Object(u["each"])(e||r,(function(t,e){"img"===e?n._setImage(r.img):l[e]&&i.setAttribute(l[e],t)}))},e.prototype.setAttr=function(t,e){this.attrs[t]=e,"img"===t&&this._setImage(e)},e.prototype._setImage=function(t){var e=this.attr(),n=this.get("el");if(Object(u["isString"])(t))n.setAttribute("href",t);else if(t instanceof window.Image)e.width||(n.setAttribute("width",t.width),this.attr("width",t.width)),e.height||(n.setAttribute("height",t.height),this.attr("height",t.height)),n.setAttribute("href",t.src);else if(t instanceof HTMLElement&&Object(u["isString"])(t.nodeName)&&"CANVAS"===t.nodeName.toUpperCase())n.setAttribute("href",t.toDataURL());else if(t instanceof ImageData){var r=document.createElement("canvas");r.setAttribute("width",""+t.width),r.setAttribute("height",""+t.height),r.getContext("2d").putImageData(t,0,0),e.width||(n.setAttribute("width",""+t.width),this.attr("width",t.width)),e.height||(n.setAttribute("height",""+t.height),this.attr("height",t.height)),n.setAttribute("href",r.toDataURL())}},e}(_),I=x,L=n("1183"),w=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="line",e.canFill=!1,e.canStroke=!0,e}return a(e,t),e.prototype.getDefaultAttrs=function(){var e=t.prototype.getDefaultAttrs.call(this);return o(o({},e),{x1:0,y1:0,x2:0,y2:0,startArrow:!1,endArrow:!1})},e.prototype.createPath=function(t,e){var n=this.attr(),r=this.get("el");Object(u["each"])(e||n,(function(e,i){if("startArrow"===i||"endArrow"===i)if(e){var a=Object(u["isObject"])(e)?t.addArrow(n,l[i]):t.getDefaultArrow(n,l[i]);r.setAttribute(l[i],"url(#"+a+")")}else r.removeAttribute(l[i]);else l[i]&&r.setAttribute(l[i],e)}))},e.prototype.getTotalLength=function(){var t=this.attr(),e=t.x1,n=t.y1,r=t.x2,i=t.y2;return L["c"].length(e,n,r,i)},e.prototype.getPoint=function(t){var e=this.attr(),n=e.x1,r=e.y1,i=e.x2,a=e.y2;return L["c"].pointAt(n,r,i,a,t)},e}(_),D=w,M={circle:function(t,e,n){return[["M",t,e],["m",-n,0],["a",n,n,0,1,0,2*n,0],["a",n,n,0,1,0,2*-n,0]]},square:function(t,e,n){return[["M",t-n,e-n],["L",t+n,e-n],["L",t+n,e+n],["L",t-n,e+n],["Z"]]},diamond:function(t,e,n){return[["M",t-n,e],["L",t,e-n],["L",t+n,e],["L",t,e+n],["Z"]]},triangle:function(t,e,n){var r=n*Math.sin(1/3*Math.PI);return[["M",t-n,e+r],["L",t,e-r],["L",t+n,e+r],["z"]]},triangleDown:function(t,e,n){var r=n*Math.sin(1/3*Math.PI);return[["M",t-n,e-r],["L",t+n,e-r],["L",t,e+r],["Z"]]}},k={get:function(t){return M[t]},register:function(t,e){M[t]=e},remove:function(t){delete M[t]},getAll:function(){return M}},B=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="marker",e.canFill=!0,e.canStroke=!0,e}return a(e,t),e.prototype.createPath=function(t){var e=this.get("el");e.setAttribute("d",this._assembleMarker())},e.prototype._assembleMarker=function(){var t=this._getPath();return Object(u["isArray"])(t)?t.map((function(t){return t.join(" ")})).join(""):t},e.prototype._getPath=function(){var t,e=this.attr(),n=e.x,r=e.y,i=e.r||e.radius,a=e.symbol||"circle";return t=Object(u["isFunction"])(a)?a:k.get(a),t?t(n,r,i):(console.warn(t+" symbol is not exist."),null)},e.symbolsFactory=k,e}(_),U=B,F=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="path",e.canFill=!0,e.canStroke=!0,e}return a(e,t),e.prototype.getDefaultAttrs=function(){var e=t.prototype.getDefaultAttrs.call(this);return o(o({},e),{startArrow:!1,endArrow:!1})},e.prototype.createPath=function(t,e){var n=this,r=this.attr(),i=this.get("el");Object(u["each"])(e||r,(function(e,a){if("path"===a&&Object(u["isArray"])(e))i.setAttribute("d",n._formatPath(e));else if("startArrow"===a||"endArrow"===a)if(e){var o=Object(u["isObject"])(e)?t.addArrow(r,l[a]):t.getDefaultArrow(r,l[a]);i.setAttribute(l[a],"url(#"+o+")")}else i.removeAttribute(l[a]);else l[a]&&i.setAttribute(l[a],e)}))},e.prototype._formatPath=function(t){var e=t.map((function(t){return t.join(" ")})).join("");return~e.indexOf("NaN")?"":e},e.prototype.getTotalLength=function(){var t=this.get("el");return t?t.getTotalLength():null},e.prototype.getPoint=function(t){var e=this.get("el"),n=this.getTotalLength();if(0===n)return null;var r=e?e.getPointAtLength(t*n):null;return r?{x:r.x,y:r.y}:null},e}(_),j=F,G=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="polygon",e.canFill=!0,e.canStroke=!0,e}return a(e,t),e.prototype.createPath=function(t,e){var n=this.attr(),r=this.get("el");Object(u["each"])(e||n,(function(t,e){"points"===e&&Object(u["isArray"])(t)&&t.length>=2?r.setAttribute("points",t.map((function(t){return t[0]+","+t[1]})).join(" ")):l[e]&&r.setAttribute(l[e],t)}))},e}(_),V=G,H=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="polyline",e.canFill=!0,e.canStroke=!0,e}return a(e,t),e.prototype.getDefaultAttrs=function(){var e=t.prototype.getDefaultAttrs.call(this);return o(o({},e),{startArrow:!1,endArrow:!1})},e.prototype.onAttrChange=function(e,n,r){t.prototype.onAttrChange.call(this,e,n,r),-1!==["points"].indexOf(e)&&this._resetCache()},e.prototype._resetCache=function(){this.set("totalLength",null),this.set("tCache",null)},e.prototype.createPath=function(t,e){var n=this.attr(),r=this.get("el");Object(u["each"])(e||n,(function(t,e){"points"===e&&Object(u["isArray"])(t)&&t.length>=2?r.setAttribute("points",t.map((function(t){return t[0]+","+t[1]})).join(" ")):l[e]&&r.setAttribute(l[e],t)}))},e.prototype.getTotalLength=function(){var t=this.attr().points,e=this.get("totalLength");return Object(u["isNil"])(e)?(this.set("totalLength",L["d"].length(t)),this.get("totalLength")):e},e.prototype.getPoint=function(t){var e,n,r=this.attr().points,i=this.get("tCache");return i||(this._setTcache(),i=this.get("tCache")),Object(u["each"])(i,(function(r,i){t>=r[0]&&t<=r[1]&&(e=(t-r[0])/(r[1]-r[0]),n=i)})),L["c"].pointAt(r[n][0],r[n][1],r[n+1][0],r[n+1][1],e)},e.prototype._setTcache=function(){var t=this.attr().points;if(t&&0!==t.length){var e=this.getTotalLength();if(!(e<=0)){var n,r,i=0,a=[];Object(u["each"])(t,(function(o,s){t[s+1]&&(n=[],n[0]=i/e,r=L["c"].length(o[0],o[1],t[s+1][0],t[s+1][1]),i+=r,n[1]=i/e,a.push(n))})),this.set("tCache",a)}}},e.prototype.getStartTangent=function(){var t=this.attr().points,e=[];return e.push([t[1][0],t[1][1]]),e.push([t[0][0],t[0][1]]),e},e.prototype.getEndTangent=function(){var t=this.attr().points,e=t.length-1,n=[];return n.push([t[e-1][0],t[e-1][1]]),n.push([t[e][0],t[e][1]]),n},e}(_),X=H;function W(t){var e=0,n=0,r=0,i=0;return Object(u["isArray"])(t)?1===t.length?e=n=r=i=t[0]:2===t.length?(e=r=t[0],n=i=t[1]):3===t.length?(e=t[0],n=i=t[1],r=t[2]):(e=t[0],n=t[1],r=t[2],i=t[3]):e=n=r=i=t,{r1:e,r2:n,r3:r,r4:i}}var z=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="rect",e.canFill=!0,e.canStroke=!0,e}return a(e,t),e.prototype.getDefaultAttrs=function(){var e=t.prototype.getDefaultAttrs.call(this);return o(o({},e),{x:0,y:0,width:0,height:0,radius:0})},e.prototype.createPath=function(t,e){var n=this,r=this.attr(),i=this.get("el"),a=!1,o=["x","y","width","height","radius"];Object(u["each"])(e||r,(function(t,e){-1===o.indexOf(e)||a?-1===o.indexOf(e)&&l[e]&&i.setAttribute(l[e],t):(i.setAttribute("d",n._assembleRect(r)),a=!0)}))},e.prototype._assembleRect=function(t){var e=t.x,n=t.y,r=t.width,i=t.height,a=t.radius;if(!a)return"M "+e+","+n+" l "+r+",0 l 0,"+i+" l"+-r+" 0 z";var o=W(a);Object(u["isArray"])(a)?1===a.length?o.r1=o.r2=o.r3=o.r4=a[0]:2===a.length?(o.r1=o.r3=a[0],o.r2=o.r4=a[1]):3===a.length?(o.r1=a[0],o.r2=o.r4=a[1],o.r3=a[2]):(o.r1=a[0],o.r2=a[1],o.r3=a[2],o.r4=a[3]):o.r1=o.r2=o.r3=o.r4=a;var s=[["M "+(e+o.r1)+","+n],["l "+(r-o.r1-o.r2)+",0"],["a "+o.r2+","+o.r2+",0,0,1,"+o.r2+","+o.r2],["l 0,"+(i-o.r2-o.r3)],["a "+o.r3+","+o.r3+",0,0,1,"+-o.r3+","+o.r3],["l "+(o.r3+o.r4-r)+",0"],["a "+o.r4+","+o.r4+",0,0,1,"+-o.r4+","+-o.r4],["l 0,"+(o.r4+o.r1-i)],["a "+o.r1+","+o.r1+",0,0,1,"+o.r1+","+-o.r1],["z"]];return s.join(" ")},e}(_),Y=z,K=n("7b46"),Z=.3,q={top:"before-edge",middle:"central",bottom:"after-edge",alphabetic:"baseline",hanging:"hanging"},Q={top:"text-before-edge",middle:"central",bottom:"text-after-edge",alphabetic:"alphabetic",hanging:"hanging"},J={left:"left",start:"left",center:"middle",right:"end",end:"end"},$=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="text",e.canFill=!0,e.canStroke=!0,e}return a(e,t),e.prototype.getDefaultAttrs=function(){var e=t.prototype.getDefaultAttrs.call(this);return o(o({},e),{x:0,y:0,text:null,fontSize:12,fontFamily:"sans-serif",fontStyle:"normal",fontWeight:"normal",fontVariant:"normal",textAlign:"start",textBaseline:"bottom"})},e.prototype.createPath=function(t,e){var n=this,r=this.attr(),i=this.get("el");this._setFont(),Object(u["each"])(e||r,(function(t,e){"text"===e?n._setText(""+t):"matrix"===e&&t?g(n):l[e]&&i.setAttribute(l[e],t)})),i.setAttribute("paint-order","stroke"),i.setAttribute("style","stroke-linecap:butt; stroke-linejoin:miter;")},e.prototype._setFont=function(){var t=this.get("el"),e=this.attr(),n=e.textBaseline,r=e.textAlign,i=Object(K["a"])();i&&"firefox"===i.name?t.setAttribute("dominant-baseline",Q[n]||"alphabetic"):t.setAttribute("alignment-baseline",q[n]||"baseline"),t.setAttribute("text-anchor",J[r]||"left")},e.prototype._setText=function(t){var e=this.get("el"),n=this.attr(),r=n.x,i=n.textBaseline,a=void 0===i?"bottom":i;if(t)if(~t.indexOf("\n")){var o=t.split("\n"),s=o.length-1,c="";Object(u["each"])(o,(function(t,e){0===e?"alphabetic"===a?c+='<tspan x="'+r+'" dy="'+-s+'em">'+t+"</tspan>":"top"===a?c+='<tspan x="'+r+'" dy="0.9em">'+t+"</tspan>":"middle"===a?c+='<tspan x="'+r+'" dy="'+-(s-1)/2+'em">'+t+"</tspan>":"bottom"===a?c+='<tspan x="'+r+'" dy="-'+(s+Z)+'em">'+t+"</tspan>":"hanging"===a&&(c+='<tspan x="'+r+'" dy="'+(-(s-1)-Z)+'em">'+t+"</tspan>"):c+='<tspan x="'+r+'" dy="1em">'+t+"</tspan>"})),e.innerHTML=c}else e.innerHTML=t;else e.innerHTML=""},e}(_),tt=$,et=/^l\s*\(\s*([\d.]+)\s*\)\s*(.*)/i,nt=/^r\s*\(\s*([\d.]+)\s*,\s*([\d.]+)\s*,\s*([\d.]+)\s*\)\s*(.*)/i,rt=/[\d.]+:(#[^\s]+|[^)]+\))/gi;function it(t){var e=t.match(rt);if(!e)return"";var n="";return e.sort((function(t,e){return t=t.split(":"),e=e.split(":"),Number(t[0])-Number(e[0])})),Object(u["each"])(e,(function(t){t=t.split(":"),n+='<stop offset="'+t[0]+'" stop-color="'+t[1]+'"></stop>'})),n}function at(t,e){var n,r,i=et.exec(t),a=Object(u["mod"])(Object(u["toRadian"])(parseFloat(i[1])),2*Math.PI),o=i[2];a>=0&&a<.5*Math.PI?(n={x:0,y:0},r={x:1,y:1}):.5*Math.PI<=a&&a<Math.PI?(n={x:1,y:0},r={x:0,y:1}):Math.PI<=a&&a<1.5*Math.PI?(n={x:1,y:1},r={x:0,y:0}):(n={x:0,y:1},r={x:1,y:0});var s=Math.tan(a),c=s*s,l=(r.x-n.x+s*(r.y-n.y))/(c+1)+n.x,h=s*(r.x-n.x+s*(r.y-n.y))/(c+1)+n.y;e.setAttribute("x1",n.x),e.setAttribute("y1",n.y),e.setAttribute("x2",l),e.setAttribute("y2",h),e.innerHTML=it(o)}function ot(t,e){var n=nt.exec(t),r=parseFloat(n[1]),i=parseFloat(n[2]),a=parseFloat(n[3]),o=n[4];e.setAttribute("cx",r),e.setAttribute("cy",i),e.setAttribute("r",a),e.innerHTML=it(o)}var st=function(){function t(t){this.cfg={};var e=null,n=Object(u["uniqueId"])("gradient_");return"l"===t.toLowerCase()[0]?(e=h("linearGradient"),at(t,e)):(e=h("radialGradient"),ot(t,e)),e.setAttribute("id",n),this.el=e,this.id=n,this.cfg=t,this}return t.prototype.match=function(t,e){return this.cfg===e},t}(),ut=st,ct={shadowColor:"color",shadowOpacity:"opacity",shadowBlur:"blur",shadowOffsetX:"dx",shadowOffsetY:"dy"},lt={x:"-40%",y:"-40%",width:"200%",height:"200%"},ht=function(){function t(t){this.type="filter",this.cfg={},this.type="filter";var e=h("filter");return Object(u["each"])(lt,(function(t,n){e.setAttribute(n,t)})),this.el=e,this.id=Object(u["uniqueId"])("filter_"),this.el.id=this.id,this.cfg=t,this._parseShadow(t,e),this}return t.prototype.match=function(t,e){if(this.type!==t)return!1;var n=!0,r=this.cfg;return Object(u["each"])(Object.keys(r),(function(t){if(r[t]!==e[t])return n=!1,!1})),n},t.prototype.update=function(t,e){var n=this.cfg;return n[ct[t]]=e,this._parseShadow(n,this.el),this},t.prototype._parseShadow=function(t,e){var n='<feDropShadow\n      dx="'+(t.dx||0)+'"\n      dy="'+(t.dy||0)+'"\n      stdDeviation="'+(t.blur?t.blur/10:0)+'"\n      flood-color="'+(t.color?t.color:"#000")+'"\n      flood-opacity="'+(t.opacity?t.opacity:1)+'"\n      />';e.innerHTML=n},t}(),ft=ht,pt=function(){function t(t,e){this.cfg={};var n=h("marker"),r=Object(u["uniqueId"])("marker_");n.setAttribute("id",r);var i=h("path");i.setAttribute("stroke",t.stroke||"none"),i.setAttribute("fill",t.fill||"none"),n.appendChild(i),n.setAttribute("overflow","visible"),n.setAttribute("orient","auto-start-reverse"),this.el=n,this.child=i,this.id=r;var a=t["marker-start"===e?"startArrow":"endArrow"];return this.stroke=t.stroke||"#000",!0===a?this._setDefaultPath(e,i):(this.cfg=a,this._setMarker(t.lineWidth,i)),this}return t.prototype.match=function(){return!1},t.prototype._setDefaultPath=function(t,e){var n=this.el;e.setAttribute("d","M0,0 L"+10*Math.cos(Math.PI/6)+",5 L0,10"),n.setAttribute("refX",""+10*Math.cos(Math.PI/6)),n.setAttribute("refY","5")},t.prototype._setMarker=function(t,e){var n=this.el,r=this.cfg.path,i=this.cfg.d;Object(u["isArray"])(r)&&(r=r.map((function(t){return t.join(" ")})).join("")),e.setAttribute("d",r),n.appendChild(e),i&&n.setAttribute("refX",""+i/t)},t.prototype.update=function(t){var e=this.child;e.attr?e.attr("fill",t):e.setAttribute("fill",t)},t}(),dt=pt,Et=function(){function t(t){this.type="clip",this.cfg={};var e=h("clipPath");this.el=e,this.id=Object(u["uniqueId"])("clip_"),e.id=this.id;var n=t.cfg.el;return e.appendChild(n),this.cfg=t,this}return t.prototype.match=function(){return!1},t.prototype.remove=function(){var t=this.el;t.parentNode.removeChild(t)},t}(),gt=Et,mt=/^p\s*\(\s*([axyn])\s*\)\s*(.*)/i,vt=function(){function t(t){this.cfg={};var e=h("pattern");e.setAttribute("patternUnits","userSpaceOnUse");var n=h("image");e.appendChild(n);var r=Object(u["uniqueId"])("pattern_");e.id=r,this.el=e,this.id=r,this.cfg=t;var i=mt.exec(t),a=i[2];n.setAttribute("href",a);var o=new Image;function s(){e.setAttribute("width",""+o.width),e.setAttribute("height",""+o.height)}return a.match(/^data:/i)||(o.crossOrigin="Anonymous"),o.src=a,o.complete?s():(o.onload=s,o.src=o.src),this}return t.prototype.match=function(t,e){return this.cfg===e},t}(),yt=vt,Tt=function(){function t(t){var e=h("defs"),n=Object(u["uniqueId"])("defs_");e.id=n,t.appendChild(e),this.children=[],this.defaultArrow={},this.el=e,this.canvas=t}return t.prototype.find=function(t,e){for(var n=this.children,r=null,i=0;i<n.length;i++)if(n[i].match(t,e)){r=n[i].id;break}return r},t.prototype.findById=function(t){for(var e=this.children,n=null,r=0;r<e.length;r++)if(e[r].id===t){n=e[r];break}return n},t.prototype.add=function(t){this.children.push(t),t.canvas=this.canvas,t.parent=this},t.prototype.getDefaultArrow=function(t,e){var n=t.stroke||t.strokeStyle;if(this.defaultArrow[n])return this.defaultArrow[n].id;var r=new dt(t,e);return this.defaultArrow[n]=r,this.el.appendChild(r.el),this.add(r),r.id},t.prototype.addGradient=function(t){var e=new ut(t);return this.el.appendChild(e.el),this.add(e),e.id},t.prototype.addArrow=function(t,e){var n=new dt(t,e);return this.el.appendChild(n.el),this.add(n),n.id},t.prototype.addShadow=function(t){var e=new ft(t);return this.el.appendChild(e.el),this.add(e),e.id},t.prototype.addPattern=function(t){var e=new yt(t);return this.el.appendChild(e.el),this.add(e),e.id},t.prototype.addClip=function(t){var e=new gt(t);return this.el.appendChild(e.el),this.add(e),e.id},t}(),bt=Tt,At=function(t){function e(e){return t.call(this,o(o({},e),{autoDraw:!0,renderer:"svg"}))||this}return a(e,t),e.prototype.getShapeBase=function(){return r},e.prototype.getGroupBase=function(){return b},e.prototype.getShape=function(t,e,n){var r=n.target||n.srcElement;if(!c[r.tagName]){var i=r.parentNode;while(i&&!c[i.tagName])i=i.parentNode;r=i}return this.find((function(t){return t.get("el")===r}))},e.prototype.createDom=function(){var t=h("svg"),e=new bt(t);return t.setAttribute("width",""+this.get("width")),t.setAttribute("height",""+this.get("height")),this.set("context",e),t},e.prototype.onCanvasChange=function(t){var e=this.get("context"),n=this.get("el");if("sort"===t){var r=this.get("children");r&&r.length&&p(this,(function(t,e){return r.indexOf(t)-r.indexOf(e)?1:0}))}else if("clear"===t){if(n){n.innerHTML="";var i=e.el;i.innerHTML="",n.appendChild(i)}}else"matrix"===t?g(this):"clip"===t?m(this,e):"changeSize"===t&&(n.setAttribute("width",""+this.get("width")),n.setAttribute("height",""+this.get("height")))},e.prototype.draw=function(){var t=this.get("context"),e=this.getChildren();m(this,t),e.length&&v(t,e)},e}(s["a"]),_t=At},c99e:function(t,e,n){"use strict";n.d(e,"a",(function(){return S}));var r=n("9523"),i=n.n(r),a=n("970b"),o=n.n(a),s=n("5bc3"),u=n.n(s),c=n("e1c6");function l(t){var e=0;switch(t){case"vec2":case"ivec2":e=2;break;case"vec3":case"ivec3":e=3;break;case"vec4":case"ivec4":case"mat2":e=4;break;case"mat3":e=9;break;case"mat4":e=16;break;default:}return e}var h,f,p,d=/uniform\s+(bool|float|int|vec2|vec3|vec4|ivec2|ivec3|ivec4|mat2|mat3|mat4|sampler2D|samplerCube)\s+([\s\S]*?);/g;function E(t){var e={};return t=t.replace(d,(function(t,n,r){var i=r.split(":"),a=i[0].trim(),o="";switch(i.length>1&&(o=i[1].trim()),n){case"bool":o="true"===o;break;case"float":case"int":o=Number(o);break;case"vec2":case"vec3":case"vec4":case"ivec2":case"ivec3":case"ivec4":case"mat2":case"mat3":case"mat4":o=o?o.replace("[","").replace("]","").split(",").reduce((function(t,e){return t.push(Number(e.trim())),t}),[]):new Array(l(n)).fill(0);break;default:}return e[a]=o,"uniform ".concat(n," ").concat(a,";\n")})),{content:t,uniforms:e}}function g(t){return t.filter((function(t,e,n){return n.indexOf(t)===e}))}function m(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function v(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?m(Object(n),!0).forEach((function(e){i()(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):m(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var y="varying vec4 v_PickingResult;\nuniform vec4 u_HighlightColor : [0, 0, 0, 0];\nuniform float u_PickingStage : 0.0;\n\n#define PICKING_ENCODE 1.0\n#define PICKING_HIGHLIGHT 2.0\n#define COLOR_SCALE 1. / 255.\n\n/*\n * Returns highlight color if this item is selected.\n */\nvec4 filterHighlightColor(vec4 color) {\n  bool selected = bool(v_PickingResult.a);\n\n  if (selected) {\n    vec4 highLightColor = u_HighlightColor * COLOR_SCALE;\n\n    float highLightAlpha = highLightColor.a;\n    float highLightRatio = highLightAlpha / (highLightAlpha + color.a * (1.0 - highLightAlpha));\n\n    vec3 resultRGB = mix(color.rgb, highLightColor.rgb, highLightRatio);\n    return vec4(resultRGB, color.a);\n  } else {\n    return color;\n  }\n}\n\n/*\n * Returns picking color if picking enabled else unmodified argument.\n */\nvec4 filterPickingColor(vec4 color) {\n  vec3 pickingColor = v_PickingResult.rgb;\n  if (u_PickingStage == PICKING_ENCODE && length(pickingColor) < 0.001) {\n    discard;\n  }\n  return u_PickingStage == PICKING_ENCODE ? vec4(pickingColor, step(0.001,color.a)): color;\n}\n\n/*\n * Returns picking color if picking is enabled if not\n * highlight color if this item is selected, otherwise unmodified argument.\n */\nvec4 filterColor(vec4 color) {\n  return filterPickingColor(filterHighlightColor(color));\n}\n",T="attribute vec3 a_PickingColor;\nvarying vec4 v_PickingResult;\n\nuniform vec3 u_PickingColor : [0, 0, 0];\nuniform vec4 u_HighlightColor : [0, 0, 0, 0];\nuniform float u_PickingStage : 0.0;\nuniform float u_PickingThreshold : 1.0;\nuniform float u_PickingBuffer: 0.0;\n\n#define PICKING_ENCODE 1.0\n#define PICKING_HIGHLIGHT 2.0\n#define COLOR_SCALE 1. / 255.\n\nbool isVertexPicked(vec3 vertexColor) {\n  return\n    abs(vertexColor.r - u_PickingColor.r) < u_PickingThreshold &&\n    abs(vertexColor.g - u_PickingColor.g) < u_PickingThreshold &&\n    abs(vertexColor.b - u_PickingColor.b) < u_PickingThreshold;\n}\n\nvoid setPickingColor(vec3 pickingColor) {\n  // compares only in highlight stage\n  v_PickingResult.a = float((u_PickingStage == PICKING_HIGHLIGHT) && isVertexPicked(pickingColor));\n\n  // Stores the picking color so that the fragment shader can render it during picking\n  v_PickingResult.rgb = pickingColor * COLOR_SCALE;\n}\n\nfloat setPickingSize(float x) {\n   return u_PickingStage == PICKING_ENCODE ? x + u_PickingBuffer : x;\n}",b="/**\n * 2D signed distance field functions\n * @see http://www.iquilezles.org/www/articles/distfunctions2d/distfunctions2d.htm\n */\n\nfloat ndot(vec2 a, vec2 b ) { return a.x*b.x - a.y*b.y; }\n\nfloat sdCircle(vec2 p, float r) {\n  return length(p) - r;\n}\n\nfloat sdEquilateralTriangle(vec2 p) {\n  float k = sqrt(3.0);\n  p.x = abs(p.x) - 1.0;\n  p.y = p.y + 1.0/k;\n  if( p.x + k*p.y > 0.0 ) p = vec2(p.x-k*p.y,-k*p.x-p.y)/2.0;\n  p.x -= clamp( p.x, -2.0, 0.0 );\n  return -length(p)*sign(p.y);\n}\n\nfloat sdBox(vec2 p, vec2 b) {\n  vec2 d = abs(p)-b;\n  return length(max(d,vec2(0))) + min(max(d.x,d.y),0.0);\n}\n\nfloat sdPentagon(vec2 p, float r) {\n  vec3 k = vec3(0.809016994,0.587785252,0.726542528);\n  p.x = abs(p.x);\n  p -= 2.0*min(dot(vec2(-k.x,k.y),p),0.0)*vec2(-k.x,k.y);\n  p -= 2.0*min(dot(vec2( k.x,k.y),p),0.0)*vec2( k.x,k.y);\n  p -= vec2(clamp(p.x,-r*k.z,r*k.z),r);\n  return length(p)*sign(p.y);\n}\n\nfloat sdHexagon(vec2 p, float r) {\n  vec3 k = vec3(-0.866025404,0.5,0.577350269);\n  p = abs(p);\n  p -= 2.0*min(dot(k.xy,p),0.0)*k.xy;\n  p -= vec2(clamp(p.x, -k.z*r, k.z*r), r);\n  return length(p)*sign(p.y);\n}\n\nfloat sdOctogon(vec2 p, float r) {\n  vec3 k = vec3(-0.9238795325, 0.3826834323, 0.4142135623 );\n  p = abs(p);\n  p -= 2.0*min(dot(vec2( k.x,k.y),p),0.0)*vec2( k.x,k.y);\n  p -= 2.0*min(dot(vec2(-k.x,k.y),p),0.0)*vec2(-k.x,k.y);\n  p -= vec2(clamp(p.x, -k.z*r, k.z*r), r);\n  return length(p)*sign(p.y);\n}\n\nfloat sdHexagram(vec2 p, float r) {\n  vec4 k=vec4(-0.5,0.8660254038,0.5773502692,1.7320508076);\n  p = abs(p);\n  p -= 2.0*min(dot(k.xy,p),0.0)*k.xy;\n  p -= 2.0*min(dot(k.yx,p),0.0)*k.yx;\n  p -= vec2(clamp(p.x,r*k.z,r*k.w),r);\n  return length(p)*sign(p.y);\n}\n\nfloat sdRhombus(vec2 p, vec2 b) {\n  vec2 q = abs(p);\n  float h = clamp((-2.0*ndot(q,b)+ndot(b,b))/dot(b,b),-1.0,1.0);\n  float d = length( q - 0.5*b*vec2(1.0-h,1.0+h) );\n  return d * sign( q.x*b.y + q.y*b.x - b.x*b.y );\n}\n\nfloat sdVesica(vec2 p, float r, float d) {\n  p = abs(p);\n  float b = sqrt(r*r-d*d); // can delay this sqrt\n  return ((p.y-b)*d>p.x*b)\n          ? length(p-vec2(0.0,b))\n          : length(p-vec2(-d,0.0))-r;\n}",A=/precision\s+(high|low|medium)p\s+float/,_="#ifdef GL_FRAGMENT_PRECISION_HIGH\n precision highp float;\n #else\n precision mediump float;\n#endif\n",R=/#pragma include (["^+"]?["\ "[a-zA-Z_0-9](.*)"]*?)/g,S=(h=Object(c["injectable"])(),h((p=function(){function t(){o()(this,t),this.moduleCache={},this.rawContentCache={}}return u()(t,[{key:"registerBuiltinModules",value:function(){this.destroy(),this.registerModule("picking",{vs:T,fs:y}),this.registerModule("sdf2d",{vs:"",fs:b})}},{key:"registerModule",value:function(t,e){if(!this.rawContentCache[t]){var n=e.vs,r=void 0===n?"":n,i=e.fs,a=void 0===i?"":i,o=e.uniforms,s=E(r),u=s.content,c=s.uniforms,l=E(a),h=l.content,f=l.uniforms;this.rawContentCache[t]={fs:h,uniforms:v(v(v({},c),f),o),vs:u}}}},{key:"destroy",value:function(){this.moduleCache={},this.rawContentCache={}}},{key:"getModule",value:function(t){var e=this;if(this.moduleCache[t])return this.moduleCache[t];var n=this.rawContentCache[t].vs||"",r=this.rawContentCache[t].fs||"",i=this.processModule(n,[],"vs"),a=i.content,o=i.includeList,s=this.processModule(r,[],"fs"),u=s.content,c=s.includeList,l=u,h=g(o.concat(c).concat(t)).reduce((function(t,n){return v(v({},t),e.rawContentCache[n].uniforms)}),{});return A.test(u)||(l=_+u),this.moduleCache[t]={fs:l.trim(),uniforms:h,vs:a.trim()},this.moduleCache[t]}},{key:"processModule",value:function(t,e,n){var r=this,i=t.replace(R,(function(t,i){var a=i.split(" "),o=a[0].replace(/"/g,"");if(e.indexOf(o)>-1)return"";var s=r.rawContentCache[o][n];e.push(o);var u=r.processModule(s||"",e,n),c=u.content;return c}));return{content:i,includeList:e}}}]),t}(),f=p))||f)},c9a5:function(t,e){},cf93:function(t,e,n){"use strict";n.d(e,"a",(function(){return V}));var r=n("278c"),i=n.n(r),a=n("a34a"),o=n.n(a),s=n("c973"),u=n.n(s),c=n("c86f"),l=n.n(c),h=n("970b"),f=n.n(h),p=n("5bc3"),d=n.n(p),E=n("53ec"),g=n.n(E),m=(n("d400"),n("e1c6")),v=n("fae1"),y=function t(){f()(this,t),this.index=void 0},T=function t(){f()(this,t),this.name=void 0,this.data=void 0,this.execute=void 0,this.tearDown=void 0},b=function(){function t(){f()(this,t),this.name=void 0,this.refCount=0,this.hasSideEffect=!1,this.devirtualize=[],this.destroy=[],this.reads=[],this.writes=[]}return d()(t,[{key:"read",value:function(t){return this.reads.find((function(e){return e.index===t.index}))||this.reads.push(t),t}},{key:"sample",value:function(t){return this.read(t),t}},{key:"write",value:function(t,e){var n=this.writes.find((function(t){return t.index===e.index}));if(n)return e;var r=t.getResourceNode(e);r.resource.version++,r.resource.imported&&(this.hasSideEffect=!0);var i=t.createResourceNode(r.resource),a=t.getResourceNode(i);return a.writer=this,this.writes.push(i),i}}]),t}(),A=n("ed6d"),_=n.n(A),R=n("6b58"),S=n.n(R),C=n("36c6"),O=n.n(C),N=function t(){f()(this,t),this.first=void 0,this.last=void 0};function P(t){var e=x();return function(){var n,r=O()(t);if(e){var i=O()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return S()(this,n)}}function x(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}var I,L,w,D,M,k,B=function(t){_()(n,t);var e=P(n);function n(){var t;f()(this,n);for(var r=arguments.length,i=new Array(r),a=0;a<r;a++)i[a]=arguments[a];return t=e.call.apply(e,[this].concat(i)),t.version=0,t.refs=0,t.name=void 0,t.imported=void 0,t.priority=void 0,t.discardStart=!0,t.discardEnd=!1,t.descriptor=void 0,t.resource=void 0,t}return d()(n,[{key:"preExecuteDestroy",value:function(){this.discardEnd=!0}},{key:"postExecuteDestroy",value:function(){this.imported}},{key:"postExecuteDevirtualize",value:function(){this.discardStart=!1}},{key:"preExecuteDevirtualize",value:function(){this.imported}}]),n}(N),U=function t(){f()(this,t),this.resource=void 0,this.writer=void 0,this.readerCount=0,this.version=void 0};function F(t,e){var n;if("undefined"===typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(n=j(t))||e&&t&&"number"===typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,s=!1;return{s:function(){n=t[Symbol.iterator]()},n:function(){var t=n.next();return o=t.done,t},e:function(t){s=!0,a=t},f:function(){try{o||null==n.return||n.return()}finally{if(s)throw a}}}}function j(t,e){if(t){if("string"===typeof t)return G(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?G(t,e):void 0}}function G(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}var V=(I=Object(m["injectable"])(),L=Object(m["inject"])(v["a"].RenderEngine),I((k=function(){function t(){f()(this,t),this.passNodes=[],this.resourceNodes=[],this.frameGraphPasses=[],l()(this,"engine",M,this)}return d()(t,[{key:"execute",value:function(){var t=u()(o.a.mark((function t(e){return o.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return this.compile(),t.next=3,this.executePassNodes(e);case 3:case"end":return t.stop()}}),t,this)})));function e(e){return t.apply(this,arguments)}return e}()},{key:"tearDown",value:function(){this.frameGraphPasses.forEach((function(t){t.tearDown&&t.tearDown()})),this.reset()}},{key:"addPass",value:function(t,e,n,r){var i=new T;i.execute=n,r&&(i.tearDown=r),i.name=t;var a=new b;return a.name=t,this.passNodes.push(a),this.frameGraphPasses.push(i),e(this,a,i),i}},{key:"getPass",value:function(t){return this.frameGraphPasses.find((function(e){return e.name===t}))}},{key:"compile",value:function(){var t,e=this,n=F(this.passNodes);try{for(n.s();!(t=n.n()).done;){var r=t.value;r.refCount=r.writes.length+(r.hasSideEffect?1:0),r.reads.forEach((function(t){e.resourceNodes[t.index].readerCount++}))}}catch(L){n.e(L)}finally{n.f()}var i,a=[],o=F(this.resourceNodes);try{for(o.s();!(i=o.n()).done;){var s=i.value;0===s.readerCount&&a.push(s)}}catch(L){o.e(L)}finally{o.f()}while(a.length){var u=a.pop(),c=u&&u.writer;if(c&&0===--c.refCount){var l,h=F(c.reads);try{for(h.s();!(l=h.n()).done;){var f=l.value,p=this.resourceNodes[f.index];0===--p.readerCount&&a.push(p)}}catch(L){h.e(L)}finally{h.f()}}}this.resourceNodes.forEach((function(t){t.resource.refs+=t.readerCount}));var d,E=F(this.passNodes);try{for(E.s();!(d=E.n()).done;){var g=d.value;if(g.refCount){var m,v=F(g.reads);try{for(v.s();!(m=v.n()).done;){var y=m.value,T=this.resourceNodes[y.index].resource;T.first=T.first?T.first:g,T.last=g}}catch(L){v.e(L)}finally{v.f()}var b,A=F(g.writes);try{for(A.s();!(b=A.n()).done;){var _=b.value,R=this.resourceNodes[_.index].resource;R.first=R.first?R.first:g,R.last=g}}catch(L){A.e(L)}finally{A.f()}}}}catch(L){E.e(L)}finally{E.f()}for(var S=0;S<2;S++){var C,O=F(this.resourceNodes);try{for(O.s();!(C=O.n()).done;){var N=C.value,P=N.resource;if(P.priority===S&&P.refs){var x=P.first,I=P.last;x&&I&&(x.devirtualize.push(P),I.destroy.push(P))}}}catch(L){O.e(L)}finally{O.f()}}}},{key:"executePassNodes",value:function(){var t=u()(o.a.mark((function t(e){var n,r,a,s,u,c,l,h,f,p,d,E,g,m,v,y,T;return o.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:n=F(this.passNodes.entries()),t.prev=1,n.s();case 3:if((r=n.n()).done){t.next=18;break}if(a=i()(r.value,2),s=a[0],u=a[1],!u.refCount){t.next=16;break}c=F(u.devirtualize);try{for(c.s();!(l=c.n()).done;)h=l.value,h.preExecuteDevirtualize(this.engine)}catch(o){c.e(o)}finally{c.f()}f=F(u.destroy);try{for(f.s();!(p=f.n()).done;)d=p.value,d.preExecuteDestroy(this.engine)}catch(o){f.e(o)}finally{f.f()}return t.next=12,this.frameGraphPasses[s].execute(this,this.frameGraphPasses[s],e);case 12:E=F(u.devirtualize);try{for(E.s();!(g=E.n()).done;)m=g.value,m.postExecuteDevirtualize(this.engine)}catch(o){E.e(o)}finally{E.f()}v=F(u.destroy);try{for(v.s();!(y=v.n()).done;)T=y.value,T.postExecuteDestroy(this.engine)}catch(o){v.e(o)}finally{v.f()}case 16:t.next=3;break;case 18:t.next=23;break;case 20:t.prev=20,t.t0=t["catch"](1),n.e(t.t0);case 23:return t.prev=23,n.f(),t.finish(23);case 26:this.reset();case 27:case"end":return t.stop()}}),t,this,[[1,20,23,26]])})));function e(e){return t.apply(this,arguments)}return e}()},{key:"reset",value:function(){this.passNodes=[],this.resourceNodes=[],this.frameGraphPasses=[]}},{key:"getResourceNode",value:function(t){return this.resourceNodes[t.index]}},{key:"createResourceNode",value:function(t){var e=new U;e.resource=t,e.version=t.version,this.resourceNodes.push(e);var n=new y;return n.index=this.resourceNodes.length-1,n}},{key:"createTexture",value:function(t,e,n){var r=new B;return r.name=e,r.descriptor=n,this.createResourceNode(r)}},{key:"createRenderTarget",value:function(t,e,n){var r=new B;return r.name=e,r.descriptor=n,this.createResourceNode(r)}},{key:"present",value:function(t){this.addPass("Present",(function(e,n){n.read(t),n.hasSideEffect=!0}),u()(o.a.mark((function t(){return o.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:case"end":return t.stop()}}),t)}))))}}]),t}(),D=k,M=g()(D.prototype,"engine",[L],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),w=D))||w)},d1aa:function(t,e,n){"use strict";n.d(e,"a",(function(){return rt}));var r=n("9523"),i=n.n(r),a=n("970b"),o=n.n(a),s=n("5bc3"),u=n.n(s),c=n("3c96"),l=n.n(c),h=n("ed6d"),f=n.n(h),p=n("6b58"),d=n.n(p),E=n("36c6"),g=n.n(E),m=n("20e7"),v=n("a03b"),y=n("7037"),T=n.n(y),b=9007199254740991,A="[object Arguments]",_="[object Array]",R="[object Boolean]",S="[object Date]",C="[object Error]",O="[object Function]",N="[object Map]",P="[object Number]",x="[object Object]",I="[object RegExp]",L="[object Set]",w="[object String]",D="[object WeakMap]",M="[object ArrayBuffer]",k="[object DataView]",B="[object Float32Array]",U="[object Float64Array]",F="[object Int8Array]",j="[object Int16Array]",G="[object Int32Array]",V="[object Uint8Array]",H="[object Uint8ClampedArray]",X="[object Uint16Array]",W="[object Uint32Array]",z={};z[B]=z[U]=z[F]=z[j]=z[G]=z[V]=z[H]=z[X]=z[W]=!0,z[A]=z[_]=z[M]=z[R]=z[k]=z[S]=z[C]=z[O]=z[N]=z[P]=z[x]=z[I]=z[L]=z[w]=z[D]=!1;var Y=Object.prototype,K=Y.toString;function Z(t){return Q(t)&&q(t.length)&&!!z[K.call(t)]}function q(t){return"number"===typeof t&&t>-1&&t%1===0&&t<=b}function Q(t){return!!t&&"object"===T()(t)}var J=Z;function $(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function tt(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?$(Object(n),!0).forEach((function(e){i()(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):$(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function et(t){var e=nt();return function(){var n,r=g()(t);if(e){var i=g()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return d()(this,n)}}function nt(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}var rt=function(t){f()(n,t);var e=et(n);function n(t){var r;return o()(this,n),r=e.call(this,t),r.dirty=!0,r.attributes=[],r.indices=void 0,r.indicesBuffer=void 0,r.vertexCount=0,r.maxInstancedCount=void 0,r.aabb=void 0,r.entity=void 0,Object.assign(l()(r),t),r}return u()(n,[{key:"setAttribute",value:function(t,e,n,r){var i=this.attributes.find((function(e){return e.name===t}));return i?(i.data=e,i.dirty=!0):this.attributes.push(tt(tt({dirty:!0,name:t,data:e},n),{},{bufferGetter:r})),this.dirty=!0,this}},{key:"setIndex",value:function(t){return this.indices=new Uint32Array(t.buffer?t.buffer:t),this.dirty=!0,this}},{key:"applyMatrix",value:function(t){var e=this.attributes.find((function(t){var e=t.name;return"position"===e})),n=this.attributes.find((function(t){var e=t.name;return"normal"===e}));if(e&&(e.dirty=!0,e.data&&e.data.length))for(var r=0;r<e.data.length;r+=3){var i=m["f"].fromValues(e.data[r],e.data[r+1],e.data[r+2],1);m["f"].transformMat4(i,i,t),J(e.data)?e.data.set([i[0],i[1],i[2]],r):(e.data[r]=i[0],e.data[r+1]=i[1],e.data[r+2]=i[2])}if(n){var a=m["a"].normalFromMat4(m["a"].create(),t);if(n.data&&n.data.length)for(var o=0;o<n.data.length;o+=3){var s=m["e"].fromValues(n.data[o],n.data[o+1],n.data[o+2]);m["e"].transformMat3(s,s,a),m["e"].normalize(s,s),J(n.data)?n.data.set([s[0],s[1],s[2]],o):(n.data[o]=s[0],n.data[o+1]=s[1],n.data[o+2]=s[2])}}}}]),n}(v["a"])},d26b:function(t,e,n){"use strict";n.d(e,"a",(function(){return Kt})),n.d(e,"b",(function(){return Le}));var r,i,a,o,s,u,c,l,h,f,p,d,E,g,m=n("a34a"),v=n.n(m),y=n("c973"),T=n.n(y),b=n("970b"),A=n.n(b),_=n("5bc3"),R=n.n(_),S=n("1a3d"),C=n("e1c6"),O=n("7dbb"),N=n.n(O),P=function(){function t(e,n){A()(this,t),this.attribute=void 0,this.buffer=void 0;var r=n.buffer,i=n.offset,a=n.stride,o=n.normalized,s=n.size,u=n.divisor;this.buffer=r,this.attribute={buffer:r.get(),offset:i||0,stride:a||0,normalized:o||!1,divisor:u||0},s&&(this.attribute.size=s)}return R()(t,[{key:"get",value:function(){return this.attribute}},{key:"updateBuffer",value:function(t){this.buffer.subData(t)}},{key:"destroy",value:function(){this.buffer.destroy()}}]),t}(),x=n("9523"),I=n.n(x),L=(r={},I()(r,S["gl"].POINTS,"points"),I()(r,S["gl"].LINES,"lines"),I()(r,S["gl"].LINE_LOOP,"line loop"),I()(r,S["gl"].LINE_STRIP,"line strip"),I()(r,S["gl"].TRIANGLES,"triangles"),I()(r,S["gl"].TRIANGLE_FAN,"triangle fan"),I()(r,S["gl"].TRIANGLE_STRIP,"triangle strip"),r),w=(i={},I()(i,S["gl"].STATIC_DRAW,"static"),I()(i,S["gl"].DYNAMIC_DRAW,"dynamic"),I()(i,S["gl"].STREAM_DRAW,"stream"),i),D=(a={},I()(a,S["gl"].BYTE,"int8"),I()(a,S["gl"].UNSIGNED_INT,"int16"),I()(a,S["gl"].INT,"int32"),I()(a,S["gl"].UNSIGNED_BYTE,"uint8"),I()(a,S["gl"].UNSIGNED_SHORT,"uint16"),I()(a,S["gl"].UNSIGNED_INT,"uint32"),I()(a,S["gl"].FLOAT,"float"),a),M=(o={},I()(o,S["gl"].ALPHA,"alpha"),I()(o,S["gl"].LUMINANCE,"luminance"),I()(o,S["gl"].LUMINANCE_ALPHA,"luminance alpha"),I()(o,S["gl"].RGB,"rgb"),I()(o,S["gl"].RGBA,"rgba"),I()(o,S["gl"].RGBA4,"rgba4"),I()(o,S["gl"].RGB5_A1,"rgb5 a1"),I()(o,S["gl"].RGB565,"rgb565"),I()(o,S["gl"].DEPTH_COMPONENT,"depth"),I()(o,S["gl"].DEPTH_STENCIL,"depth stencil"),o),k=(s={},I()(s,S["gl"].DONT_CARE,"dont care"),I()(s,S["gl"].NICEST,"nice"),I()(s,S["gl"].FASTEST,"fast"),s),B=(u={},I()(u,S["gl"].NEAREST,"nearest"),I()(u,S["gl"].LINEAR,"linear"),I()(u,S["gl"].LINEAR_MIPMAP_LINEAR,"mipmap"),I()(u,S["gl"].NEAREST_MIPMAP_LINEAR,"nearest mipmap linear"),I()(u,S["gl"].LINEAR_MIPMAP_NEAREST,"linear mipmap nearest"),I()(u,S["gl"].NEAREST_MIPMAP_NEAREST,"nearest mipmap nearest"),u),U=(c={},I()(c,S["gl"].REPEAT,"repeat"),I()(c,S["gl"].CLAMP_TO_EDGE,"clamp"),I()(c,S["gl"].MIRRORED_REPEAT,"mirror"),c),F=(l={},I()(l,S["gl"].NONE,"none"),I()(l,S["gl"].BROWSER_DEFAULT_WEBGL,"browser"),l),j=(h={},I()(h,S["gl"].NEVER,"never"),I()(h,S["gl"].ALWAYS,"always"),I()(h,S["gl"].LESS,"less"),I()(h,S["gl"].LEQUAL,"lequal"),I()(h,S["gl"].GREATER,"greater"),I()(h,S["gl"].GEQUAL,"gequal"),I()(h,S["gl"].EQUAL,"equal"),I()(h,S["gl"].NOTEQUAL,"notequal"),h),G=(f={},I()(f,S["gl"].FUNC_ADD,"add"),I()(f,S["gl"].MIN_EXT,"min"),I()(f,S["gl"].MAX_EXT,"max"),I()(f,S["gl"].FUNC_SUBTRACT,"subtract"),I()(f,S["gl"].FUNC_REVERSE_SUBTRACT,"reverse subtract"),f),V=(p={},I()(p,S["gl"].ZERO,"zero"),I()(p,S["gl"].ONE,"one"),I()(p,S["gl"].SRC_COLOR,"src color"),I()(p,S["gl"].ONE_MINUS_SRC_COLOR,"one minus src color"),I()(p,S["gl"].SRC_ALPHA,"src alpha"),I()(p,S["gl"].ONE_MINUS_SRC_ALPHA,"one minus src alpha"),I()(p,S["gl"].DST_COLOR,"dst color"),I()(p,S["gl"].ONE_MINUS_DST_COLOR,"one minus dst color"),I()(p,S["gl"].DST_ALPHA,"dst alpha"),I()(p,S["gl"].ONE_MINUS_DST_ALPHA,"one minus dst alpha"),I()(p,S["gl"].CONSTANT_COLOR,"constant color"),I()(p,S["gl"].ONE_MINUS_CONSTANT_COLOR,"one minus constant color"),I()(p,S["gl"].CONSTANT_ALPHA,"constant alpha"),I()(p,S["gl"].ONE_MINUS_CONSTANT_ALPHA,"one minus constant alpha"),I()(p,S["gl"].SRC_ALPHA_SATURATE,"src alpha saturate"),p),H=(d={},I()(d,S["gl"].NEVER,"never"),I()(d,S["gl"].ALWAYS,"always"),I()(d,S["gl"].LESS,"less"),I()(d,S["gl"].LEQUAL,"lequal"),I()(d,S["gl"].GREATER,"greater"),I()(d,S["gl"].GEQUAL,"gequal"),I()(d,S["gl"].EQUAL,"equal"),I()(d,S["gl"].NOTEQUAL,"notequal"),d),X=(E={},I()(E,S["gl"].ZERO,"zero"),I()(E,S["gl"].KEEP,"keep"),I()(E,S["gl"].REPLACE,"replace"),I()(E,S["gl"].INVERT,"invert"),I()(E,S["gl"].INCR,"increment"),I()(E,S["gl"].DECR,"decrement"),I()(E,S["gl"].INCR_WRAP,"increment wrap"),I()(E,S["gl"].DECR_WRAP,"decrement wrap"),E),W=(g={},I()(g,S["gl"].FRONT,"front"),I()(g,S["gl"].BACK,"back"),g),z=function(){function t(e,n){A()(this,t),this.buffer=void 0;var r=n.data,i=n.usage,a=n.type;this.buffer=e.buffer({data:r,usage:w[i||S["gl"].STATIC_DRAW],type:D[a||S["gl"].UNSIGNED_BYTE]})}return R()(t,[{key:"get",value:function(){return this.buffer}},{key:"destroy",value:function(){}},{key:"subData",value:function(t){var e=t.data,n=t.offset;this.buffer.subdata(e,n)}}]),t}(),Y=n("448a"),K=n.n(Y),Z=n("7037"),q=n.n(Z),Q=9007199254740991,J="[object Arguments]",$="[object Array]",tt="[object Boolean]",et="[object Date]",nt="[object Error]",rt="[object Function]",it="[object Map]",at="[object Number]",ot="[object Object]",st="[object RegExp]",ut="[object Set]",ct="[object String]",lt="[object WeakMap]",ht="[object ArrayBuffer]",ft="[object DataView]",pt="[object Float32Array]",dt="[object Float64Array]",Et="[object Int8Array]",gt="[object Int16Array]",mt="[object Int32Array]",vt="[object Uint8Array]",yt="[object Uint8ClampedArray]",Tt="[object Uint16Array]",bt="[object Uint32Array]",At={};At[pt]=At[dt]=At[Et]=At[gt]=At[mt]=At[vt]=At[yt]=At[Tt]=At[bt]=!0,At[J]=At[$]=At[ht]=At[tt]=At[ft]=At[et]=At[nt]=At[rt]=At[it]=At[at]=At[ot]=At[st]=At[ut]=At[ct]=At[lt]=!1;var _t=Object.prototype,Rt=_t.toString;function St(t){return Ot(t)&&Ct(t.length)&&!!At[Rt.call(t)]}function Ct(t){return"number"===typeof t&&t>-1&&t%1===0&&t<=Q}function Ot(t){return!!t&&"object"===q()(t)}var Nt=St;function Pt(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function xt(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Pt(Object(n),!0).forEach((function(e){I()(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Pt(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var It="attribute vec3 a_Position;\nattribute vec2 a_TexCoord;\n\nvarying vec2 v_TexCoord;\n\nvoid main() {\n  gl_Position = vec4(a_Position, 1.0);\n  v_TexCoord = a_TexCoord;\n}",Lt=0,wt=!1,Dt=function(){function t(e,n){var r=this;A()(this,t),this.reGl=e,this.context=n,this.entity=Object(S["createEntity"])(),this.texFBO=void 0,this.computeCommand=void 0,this.textureCache={},this.outputTextureName=void 0,this.swapOutputTextureName=void 0,this.compiledPingpong=void 0,this.dynamicPingpong=void 0;var i={};this.context.uniforms.forEach((function(t){var e=t.name,n=t.type,a=t.data,o=t.isReferer,s=t.storageClass;if(s===S["STORAGE_CLASS"].StorageBuffer){if(o)r.textureCache[e]={data:void 0},i["".concat(e,"Size")]=function(){return a.compiledBundle.context.output.textureSize};else{r.textureCache[e]=r.calcDataTexture(e,n,a);var u=r.textureCache[e],c=u.textureWidth,l=u.isOutput;i["".concat(e,"Size")]=[c,c],l&&(r.outputTextureName=e,r.context.needPingpong&&(r.outputTextureName="".concat(e,"Output"),r.textureCache[r.outputTextureName]=r.calcDataTexture(e,n,a)))}i[e]=function(){return wt&&console.log("[".concat(r.entity,"]: ").concat(e," ").concat(r.textureCache[e].id)),r.textureCache[e].texture}}else if(s===S["STORAGE_CLASS"].Uniform){if(a&&(Array.isArray(a)||Nt(a))&&a.length>16)throw new Error("invalid data type ".concat(n));i[e]=function(){return t.data}}}));var a=this.getOuputDataTexture(),o=a.textureWidth,s=a.texelCount;i.u_OutputTextureSize=[o,o],i.u_OutputTexelCount=s,this.context.output.textureSize=[o,o];var u={attributes:{a_Position:[[-1,1,0],[-1,-1,0],[1,1,0],[1,-1,0]],a_TexCoord:[[0,1],[0,0],[1,1],[1,0]]},frag:"#ifdef GL_FRAGMENT_PRECISION_HIGH\n  precision highp float;\n#else\n  precision mediump float;\n#endif\n".concat(this.context.shader),uniforms:i,vert:It,primitive:"triangle strip",count:4};this.computeCommand=this.reGl(u)}return R()(t,[{key:"run",value:function(){var t=this;this.context.maxIteration>1&&this.context.needPingpong&&(this.compiledPingpong=!0),(this.compiledPingpong||this.dynamicPingpong)&&this.swap(),this.texFBO=this.reGl.framebuffer({color:this.getOuputDataTexture().texture}),this.texFBO.use((function(){t.computeCommand()})),wt&&console.log("[".concat(this.entity,"]: output ").concat(this.getOuputDataTexture().id))}},{key:"readData",value:function(){var t=T()(v.a.mark((function t(){var e,n,r,i,a,o,s,u,c=this;return v.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.reGl({framebuffer:this.texFBO})((function(){e=c.reGl.read()})),!e){t.next=6;break}if(n=this.getOuputDataTexture(),r=n.originalDataLength,i=n.elementsPerTexel,a=n.typedArrayConstructor,o=void 0===a?Float32Array:a,s=[],4!==i)for(u=0;u<e.length;u+=4)1===i?s.push(e[u]):2===i?s.push(e[u],e[u+1]):s.push(e[u],e[u+1],e[u+2]);else s=e;return t.abrupt("return",new o(s.slice(0,r)));case 6:return t.abrupt("return",new Float32Array);case 7:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}()},{key:"confirmInput",value:function(t,e){var n;this.entity===t.entity?(this.dynamicPingpong=!0,n=this):n=t,this.textureCache[e].id=n.getOuputDataTexture().id,this.textureCache[e].texture=n.getOuputDataTexture().texture,wt&&console.log("[".concat(this.entity,"]: confirm input ").concat(e," from model ").concat(n.entity,", ").concat(n.getOuputDataTexture().id))}},{key:"updateUniform",value:function(){}},{key:"updateBuffer",value:function(t,e){var n=this.context.uniforms.find((function(e){var n=e.name;return n===t}));if(n){var r=this.calcDataTexture(t,n.type,e),i=r.texture,a=r.data;this.textureCache[t].data=a,this.textureCache[t].texture=i}}},{key:"destroy",value:function(){}},{key:"swap",value:function(){if(this.swapOutputTextureName||this.createSwapOutputDataTexture(),this.compiledPingpong){var t=this.context.output.name;this.textureCache[t].id=this.getOuputDataTexture().id,this.textureCache[t].texture=this.getOuputDataTexture().texture}var e=this.outputTextureName;this.outputTextureName=this.swapOutputTextureName,this.swapOutputTextureName=e,wt&&console.log("[".concat(this.entity,"]: after swap, output ").concat(this.getOuputDataTexture().id))}},{key:"getOuputDataTexture",value:function(){return this.textureCache[this.outputTextureName]}},{key:"createSwapOutputDataTexture",value:function(){var t=this.cloneDataTexture(this.getOuputDataTexture());this.swapOutputTextureName="".concat(this.entity,"-swap"),this.textureCache[this.swapOutputTextureName]=t}},{key:"cloneDataTexture",value:function(t){var e=t.data,n=t.textureWidth;return xt(xt({},t),{},{id:Lt++,texture:this.reGl.texture({width:n,height:n,data:e,type:"float"})})}},{key:"calcDataTexture",value:function(t,e,n){var r=1;e===S["AST_TOKEN_TYPES"].Vector4FloatArray&&(r=4);for(var i=[],a=0;a<n.length;a+=r)1===r?i.push(n[a],0,0,0):2===r?i.push(n[a],n[a+1],0,0):3===r?i.push(n[a],n[a+1],n[a+2],0):4===r&&i.push(n[a],n[a+1],n[a+2],n[a+3]);var o=n.length,s=Math.ceil(o/r),u=Math.ceil(Math.sqrt(s)),c=u*u;s<c&&i.push.apply(i,K()(new Array(4*(c-s)).fill(0)));var l=this.reGl.texture({width:u,height:u,data:i,type:"float"});return{id:Lt++,data:i,originalDataLength:o,typedArrayConstructor:Nt(n)?n.constructor:void 0,textureWidth:u,texture:l,texelCount:s,elementsPerTexel:r,isOutput:t===this.context.output.name}}}]),t}(),Mt=function(){function t(e,n){A()(this,t),this.elements=void 0;var r=n.data,i=n.usage,a=n.type,o=n.count;this.elements=e.elements({data:r,usage:w[i||S["gl"].STATIC_DRAW],type:D[a||S["gl"].UNSIGNED_BYTE],count:o})}return R()(t,[{key:"get",value:function(){return this.elements}},{key:"subData",value:function(t){var e=t.data;this.elements.subdata(e)}},{key:"destroy",value:function(){this.elements.destroy()}}]),t}(),kt=function(){function t(e,n){A()(this,t),this.framebuffer=void 0;var r=n.width,i=n.height,a=n.color,o=n.colors,s=(n.depth,n.stencil,{width:r,height:i});Array.isArray(o)&&(s.colors=o.map((function(t){return t.get()}))),a&&"boolean"!==typeof a&&(s.color=a.get()),this.framebuffer=e.framebuffer(s)}return R()(t,[{key:"get",value:function(){return this.framebuffer}},{key:"destroy",value:function(){this.framebuffer.destroy()}},{key:"resize",value:function(t){var e=t.width,n=t.height;this.framebuffer.resize(e,n)}}]),t}();function Bt(t){var e=q()(t);return null!=t&&("object"===e||"function"===e)}function Ut(t){var e={};return Object.keys(t).forEach((function(n){Ft(n,t[n],e,"")})),e}function Ft(t,e,n,r){null===e||"number"===typeof e||"boolean"===typeof e||Array.isArray(e)&&"number"===typeof e[0]||Nt(e)||""===e||void 0!==e.resize?n["".concat(r&&r+".").concat(t)]=e:(Bt(e)&&Object.keys(e).forEach((function(i){Ft(i,e[i],n,"".concat(r&&r+".").concat(t))})),Array.isArray(e)&&e.forEach((function(e,i){Object.keys(e).forEach((function(a){Ft(a,e[a],n,"".concat(r&&r+".").concat(t,"[").concat(i,"]"))}))})))}function jt(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function Gt(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?jt(Object(n),!0).forEach((function(e){I()(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):jt(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var Vt,Ht,Xt,Wt,zt=function(){function t(e,n){A()(this,t),this.reGl=void 0,this.drawCommand=void 0,this.uniforms={},this.reGl=e;var r=n.vs,i=n.fs,a=n.defines,o=n.attributes,s=n.uniforms,u=n.primitive,c=n.count,l=n.elements,h=n.depth,f=n.blend,p=n.stencil,d=n.cull,E=n.instances,g=n.scissor,m=n.viewport,v={};s&&(this.uniforms=Ut(s),Object.keys(s).forEach((function(t){v[t]=e.prop(t)})));var y={};Object.keys(o).forEach((function(t){y[t]=o[t].get()}));var T=a&&this.generateDefines(a)||"",b={attributes:y,frag:"#ifdef GL_FRAGMENT_PRECISION_HIGH\n  precision highp float;\n#else\n  precision mediump float;\n#endif\n".concat(T,"\n").concat(i),uniforms:v,vert:"\n".concat(T,"\n").concat(r),primitive:L[void 0===u?S["gl"].TRIANGLES:u]};E&&(b.instances=E),c&&(b.count=c),l&&(b.elements=l.get()),g&&(b.scissor=g),m&&(b.viewport=m),this.initDepthDrawParams({depth:h},b),this.initBlendDrawParams({blend:f},b),this.initStencilDrawParams({stencil:p},b),this.initCullDrawParams({cull:d},b),this.drawCommand=e(b)}return R()(t,[{key:"addUniforms",value:function(t){this.uniforms=Gt(Gt({},this.uniforms),Ut(t))}},{key:"draw",value:function(t){var e=Gt(Gt({},this.uniforms),Ut(t.uniforms||{})),n={};Object.keys(e).forEach((function(t){var r=q()(e[t]);"boolean"===r||"number"===r||Array.isArray(e[t])||e[t].BYTES_PER_ELEMENT?n[t]=e[t]:"string"===r||(n[t]=e[t].get())})),this.drawCommand(n)}},{key:"destroy",value:function(){}},{key:"initDepthDrawParams",value:function(t,e){var n=t.depth;n&&(e.depth={enable:void 0===n.enable||!!n.enable,mask:void 0===n.mask||!!n.mask,func:j[n.func||S["gl"].LESS],range:n.range||[0,1]})}},{key:"initBlendDrawParams",value:function(t,e){var n=t.blend;if(n){var r=n.enable,i=n.func,a=n.equation,o=n.color,s=void 0===o?[0,0,0,0]:o;e.blend={enable:!!r,func:{srcRGB:V[i&&i.srcRGB||S["gl"].SRC_ALPHA],srcAlpha:V[i&&i.srcAlpha||S["gl"].SRC_ALPHA],dstRGB:V[i&&i.dstRGB||S["gl"].ONE_MINUS_SRC_ALPHA],dstAlpha:V[i&&i.dstAlpha||S["gl"].ONE_MINUS_SRC_ALPHA]},equation:{rgb:G[a&&a.rgb||S["gl"].FUNC_ADD],alpha:G[a&&a.alpha||S["gl"].FUNC_ADD]},color:s}}}},{key:"initStencilDrawParams",value:function(t,e){var n=t.stencil;if(n){var r=n.enable,i=n.mask,a=void 0===i?-1:i,o=n.func,s=void 0===o?{cmp:S["gl"].ALWAYS,ref:0,mask:-1}:o,u=n.opFront,c=void 0===u?{fail:S["gl"].KEEP,zfail:S["gl"].KEEP,zpass:S["gl"].KEEP}:u,l=n.opBack,h=void 0===l?{fail:S["gl"].KEEP,zfail:S["gl"].KEEP,zpass:S["gl"].KEEP}:l;e.stencil={enable:!!r,mask:a,func:Gt(Gt({},s),{},{cmp:H[s.cmp]}),opFront:{fail:X[c.fail],zfail:X[c.zfail],zpass:X[c.zpass]},opBack:{fail:X[h.fail],zfail:X[h.zfail],zpass:X[h.zpass]}}}}},{key:"initCullDrawParams",value:function(t,e){var n=t.cull;if(n){var r=n.enable,i=n.face,a=void 0===i?S["gl"].BACK:i;e.cull={enable:!!r,face:W[a]}}}},{key:"generateDefines",value:function(t){return Object.keys(t).map((function(e){return"#define ".concat(e," ").concat(Number(t[e]))})).join("\n")}}]),t}(),Yt=function(){function t(e,n){A()(this,t),this.texture=void 0,this.width=void 0,this.height=void 0;var r=n.data,i=n.type,a=void 0===i?S["gl"].UNSIGNED_BYTE:i,o=n.width,s=n.height,u=n.flipY,c=void 0!==u&&u,l=n.format,h=void 0===l?S["gl"].RGBA:l,f=n.mipmap,p=void 0!==f&&f,d=n.wrapS,E=void 0===d?S["gl"].CLAMP_TO_EDGE:d,g=n.wrapT,m=void 0===g?S["gl"].CLAMP_TO_EDGE:g,v=n.aniso,y=void 0===v?0:v,T=n.alignment,b=void 0===T?1:T,_=n.premultiplyAlpha,R=void 0!==_&&_,C=n.mag,O=void 0===C?S["gl"].NEAREST:C,N=n.min,P=void 0===N?S["gl"].NEAREST:N,x=n.colorSpace,I=void 0===x?S["gl"].BROWSER_DEFAULT_WEBGL:x;this.width=o,this.height=s;var L={width:o,height:s,type:D[a],format:M[h],wrapS:U[E],wrapT:U[m],mag:B[O],min:B[P],alignment:b,flipY:c,colorSpace:F[I],premultiplyAlpha:R,aniso:y};r&&(L.data=r),"number"===typeof p?L.mipmap=k[p]:"boolean"===typeof p&&(L.mipmap=p),this.texture=e.texture(L)}return R()(t,[{key:"get",value:function(){return this.texture}},{key:"update",value:function(){this.texture._texture.bind()}},{key:"resize",value:function(t){var e=t.width,n=t.height;this.texture.resize(e,n),this.width=e,this.height=n}},{key:"destroy",value:function(){this.texture.destroy()}}]),t}(),Kt=(Vt=Object(C["injectable"])(),Vt((Xt=function(){function t(){var e=this;A()(this,t),this.supportWebGPU=!1,this.useWGSL=!1,this.$canvas=void 0,this.gl=void 0,this.inited=void 0,this.createModel=function(){var t=T()(v.a.mark((function t(n){return v.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!n.uniforms){t.next=3;break}return t.next=3,Promise.all(Object.keys(n.uniforms).map(function(){var t=T()(v.a.mark((function t(e){var r;return v.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!n.uniforms[e]||void 0===n.uniforms[e].load){t.next=5;break}return t.next=3,n.uniforms[e].load();case 3:r=t.sent,n.uniforms[e]=r;case 5:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()));case 3:return t.abrupt("return",new zt(e.gl,n));case 4:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),this.createAttribute=function(t){return new P(e.gl,t)},this.createBuffer=function(t){return new z(e.gl,t)},this.createElements=function(t){return new Mt(e.gl,t)},this.createTexture2D=function(t){return new Yt(e.gl,t)},this.createFramebuffer=function(t){return new kt(e.gl,t)},this.useFramebuffer=function(t,n){e.gl({framebuffer:t?t.get():null})(n)},this.createComputeModel=function(){var t=T()(v.a.mark((function t(n){return v.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.abrupt("return",new Dt(e.gl,n));case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),this.clear=function(t){var n=t.color,r=t.depth,i=t.stencil,a=t.framebuffer,o=void 0===a?null:a,s={color:n,depth:r,stencil:i};s.framebuffer=null===o?o:o.get(),e.gl.clear(s)},this.setScissor=function(t){e.gl&&e.gl._gl&&(t.enable&&t.box?(e.gl._gl.enable(S["gl"].SCISSOR_TEST),e.gl._gl.scissor(t.box.x,t.box.y,t.box.width,t.box.height)):e.gl._gl.disable(S["gl"].SCISSOR_TEST),e.gl._refresh())},this.viewport=function(t){var n=t.x,r=t.y,i=t.width,a=t.height;e.gl&&e.gl._gl&&(e.gl._gl.viewport(n,r,i,a),e.gl._refresh())},this.readPixels=function(t){var n=t.framebuffer,r=t.x,i=t.y,a=t.width,o=t.height,s={x:r,y:i,width:a,height:o};return n&&(s.framebuffer=n.get()),e.gl.read(s)},this.getCanvas=function(){return e.$canvas},this.getGLContext=function(){return e.gl._gl},this.destroy=function(){e.gl&&(e.gl.destroy(),e.inited=!1)}}return R()(t,[{key:"init",value:function(){var t=T()(v.a.mark((function t(e){return v.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!this.inited){t.next=2;break}return t.abrupt("return");case 2:return this.$canvas=e.canvas,t.next=5,new Promise((function(t,n){N()({canvas:e.canvas,attributes:{alpha:!0,antialias:e.antialias,premultipliedAlpha:!0},pixelRatio:1,extensions:["OES_element_index_uint","OES_texture_float","OES_standard_derivatives","angle_instanced_arrays"],optionalExtensions:["EXT_texture_filter_anisotropic","EXT_blend_minmax","WEBGL_depth_texture"],profile:!0,onDone:function(e,r){!e&&r||n(e),t(r)}})}));case 5:this.gl=t.sent,this.inited=!0;case 7:case"end":return t.stop()}}),t,this)})));function e(e){return t.apply(this,arguments)}return e}()},{key:"isFloatSupported",value:function(){return this.gl.limits.readFloat}},{key:"beginFrame",value:function(){}},{key:"endFrame",value:function(){}}]),t}(),Ht=Xt))||Ht),Zt=n("5e45");function qt(){return"undefined"!==typeof window}function Qt(t,e,n,r){if(qt()){var i=document.getElementsByTagName("head")[0],a=document.createElement("script");a.setAttribute("type","text/javascript"),a.setAttribute("src",t),r&&(a.id=r),a.onload=function(){e&&e()},a.onerror=function(e){n&&n("Unable to load script '".concat(t,"'"),e)},i.appendChild(a)}}function Jt(t,e){return new Promise((function(e,n){Qt(t,(function(){e()}),(function(t,e){n(e)}))}))}var $t=function(){return te.apply(this,arguments)};function te(){return te=T()(v.a.mark((function t(){return v.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!Wt){t.next=2;break}return t.abrupt("return",Wt);case 2:return t.next=4,Jt("https://preview.babylonjs.com/glslang/glslang.js");case 4:return Wt=window.glslang("https://preview.babylonjs.com/glslang/glslang.wasm"),t.abrupt("return",Wt);case 6:case"end":return t.stop()}}),t)}))),te.apply(this,arguments)}var ee=function(){function t(e,n){A()(this,t),this.engine=e,this.options=n,this.attribute=void 0,this.buffer=void 0;var r=n,i=r.buffer,a=r.offset,o=r.stride,s=r.normalized,u=r.size,c=r.divisor,l=r.arrayStride,h=r.attributes,f=r.stepMode;this.buffer=i,this.attribute={buffer:i.get(),offset:a||0,stride:o||0,normalized:s||!1,divisor:c||0,arrayStride:l||0,attributes:h,stepMode:f||"vertex"},u&&(this.attribute.size=u)}return R()(t,[{key:"get",value:function(){return this.attribute}},{key:"updateBuffer",value:function(t){this.buffer.subData(t)}},{key:"destroy",value:function(){this.buffer.destroy()}}]),t}(),ne=function(){function t(e,n){A()(this,t),this.engine=e,this.options=n,this.buffer=void 0;var r=n,i=r.data,a=r.usage;r.type;this.buffer=this.createBuffer(i instanceof Array?new Float32Array(i):i,a||Zt["BufferUsage"].Vertex|Zt["BufferUsage"].CopyDst)}return R()(t,[{key:"get",value:function(){return this.buffer}},{key:"destroy",value:function(){this.buffer.destroy()}},{key:"subData",value:function(t){var e=t.data,n=t.offset;this.setSubData(this.buffer,n,e instanceof Array?new Float32Array(e):e)}},{key:"createBuffer",value:function(t,e){var n=t.byteLength%4,r={size:t.byteLength+n,usage:e},i=this.engine.device.createBuffer(r);return this.setSubData(i,0,t),i}},{key:"setSubData",value:function(t,e,n){var r=S["isSafari"]?this.engine.device.getQueue():this.engine.device.defaultQueue;r.writeBuffer(t,e,n)}}]),t}();function re(t){return"number"===typeof t}var ie,ae,oe,se,ue,ce,le,he=function(){function t(e,n){A()(this,t),this.engine=e,this.context=n,this.entity=Object(S["createEntity"])(),this.uniformGPUBufferLayout=[],this.uniformBuffer=void 0,this.vertexBuffers={},this.outputBuffer=void 0,this.bindGroupEntries=void 0,this.bindGroup=void 0,this.computePipeline=void 0}return R()(t,[{key:"init",value:function(){var t=T()(v.a.mark((function t(){var e,n,r,i,a,o,s,u=this;return v.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,this.compileComputePipelineStageDescriptor(this.context.shader);case 2:e=t.sent,n=e.computeStage,r=this.context.uniforms.filter((function(t){return t.storageClass===S["STORAGE_CLASS"].StorageBuffer})),i=this.context.uniforms.filter((function(t){return t.storageClass===S["STORAGE_CLASS"].Uniform})),a=i.length?1:0,this.bindGroupEntries=[],a&&(o=0,s=[],i.forEach((function(t){if(re(t.data))u.uniformGPUBufferLayout.push({name:t.name,offset:o}),o+=4,s.push(t.data);else{var e,n=(null===(e=t.data)||void 0===e?void 0:e.length)||1;3===n&&(n=4,t.data.push(0));var r=o/4%4;if(r>0){var i=4-r;if(n>1&&n<=i)2===n&&(3===i&&(o+=4,s.push(0)),s.push.apply(s,K()(t.data)),u.uniformGPUBufferLayout.push({name:t.name,offset:o}));else{for(var a=0;a<i;a++)o+=4,s.push(0);s.push.apply(s,K()(t.data)),u.uniformGPUBufferLayout.push({name:t.name,offset:o})}}o+=4*n}})),this.uniformBuffer=new ne(this.engine,{data:s instanceof Array?new Float32Array(s):s,usage:Zt["BufferUsage"].Uniform|Zt["BufferUsage"].CopyDst}),this.bindGroupEntries.push({binding:0,resource:{buffer:this.uniformBuffer.get()}})),r.forEach((function(t){var e;null!==t.data&&(t.type!==S["AST_TOKEN_TYPES"].Vector4FloatArray&&t.type!==S["AST_TOKEN_TYPES"].FloatArray||(t.name===u.context.output.name?(e=new ne(u.engine,{data:isFinite(Number(t.data))?[t.data]:t.data,usage:Zt["BufferUsage"].Storage|Zt["BufferUsage"].CopyDst|Zt["BufferUsage"].CopySrc}),u.outputBuffer=e,u.context.output={name:t.name,length:isFinite(Number(t.data))?1:t.data.length,typedArrayConstructor:Float32Array,gpuBuffer:e.get()}):t.isReferer?t.data.model&&t.data.model.outputBuffer&&(e=t.data.model.outputBuffer):e=new ne(u.engine,{data:isFinite(Number(t.data))?[t.data]:t.data,usage:Zt["BufferUsage"].Storage|Zt["BufferUsage"].CopyDst|Zt["BufferUsage"].CopySrc}),u.vertexBuffers[t.name]=e,u.bindGroupEntries.push({binding:a,resource:{name:t.name,refer:e?void 0:t.data,buffer:e?e.get():void 0}}),a++))})),this.computePipeline=this.engine.device.createComputePipeline({computeStage:n}),console.log(this.bindGroupEntries),this.bindGroup=this.engine.device.createBindGroup({layout:this.computePipeline.getBindGroupLayout(0),entries:this.bindGroupEntries});case 13:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}()},{key:"destroy",value:function(){var t=this;this.uniformBuffer&&this.uniformBuffer.destroy(),Object.keys(this.vertexBuffers).forEach((function(e){return t.vertexBuffers[e].destroy()}))}},{key:"readData",value:function(){var t=T()(v.a.mark((function t(){var e,n,r,i,a,o,s,u,c,l;return v.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e=this.context.output,!e){t.next=16;break}if(n=e.length,r=e.typedArrayConstructor,i=e.gpuBuffer,!i){t.next=16;break}return a=n*r.BYTES_PER_ELEMENT,o=this.engine.device.createBuffer({size:a,usage:GPUBufferUsage.COPY_DST|GPUBufferUsage.MAP_READ}),s=this.engine.device.createCommandEncoder(),s.copyBufferToBuffer(i,0,o,0,a),u=S["isSafari"]?this.engine.device.getQueue():this.engine.device.defaultQueue,u.submit([s.finish()]),t.next=12,o.mapAsync(Zt["MapMode"].Read);case 12:return c=o.getMappedRange(),l=new r(c.slice(0)),o.unmap(),t.abrupt("return",l);case 16:return t.abrupt("return",new Float32Array);case 17:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}()},{key:"run",value:function(){var t;this.engine.currentComputePass&&(this.engine.currentComputePass.setPipeline(this.computePipeline),this.engine.currentComputePass.setBindGroup(0,this.bindGroup),(t=this.engine.currentComputePass).dispatch.apply(t,K()(this.context.dispatch)))}},{key:"updateBuffer",value:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=this.vertexBuffers[t];r&&r.subData({data:e,offset:n})}},{key:"updateUniform",value:function(t,e){var n=this.uniformGPUBufferLayout.find((function(e){return e.name===t}));n&&this.uniformBuffer.subData({data:Number.isFinite(e)?new Float32Array([e]):new Float32Array(e),offset:n.offset})}},{key:"confirmInput",value:function(t,e){var n=this.vertexBuffers[e],r=t.outputBuffer;if(n&&r&&n!==r){var i=this.engine.device.createCommandEncoder(),a=t.context.output,o=a.length,s=a.typedArrayConstructor,u=o*s.BYTES_PER_ELEMENT;i.copyBufferToBuffer(r.get(),0,n.get(),0,u);var c=S["isSafari"]?this.engine.device.getQueue():this.engine.device.defaultQueue;c.submit([i.finish()])}}},{key:"compileShaderToSpirV",value:function(t,e,n){return this.compileRawShaderToSpirV(n+t,e)}},{key:"compileRawShaderToSpirV",value:function(t,e){return this.engine.glslang.compileGLSL(t,e)}},{key:"compileComputePipelineStageDescriptor",value:function(){var t=T()(v.a.mark((function t(e){var n,r;return v.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(n=e,r="#version 450\n",this.engine.options.useWGSL){t.next=6;break}return t.next=5,this.compileShaderToSpirV(e,"compute",r);case 5:n=t.sent;case 6:return t.abrupt("return",{computeStage:{module:this.engine.device.createShaderModule({code:n,isWHLSL:S["isSafari"]}),entryPoint:"main"}});case 7:case"end":return t.stop()}}),t,this)})));function e(e){return t.apply(this,arguments)}return e}()}]),t}(),fe=function(){function t(e,n){A()(this,t),this.engine=e,this.options=n,this.indexCount=void 0,this.buffer=void 0;var r=n,i=r.data,a=(r.usage,r.type,r.count);this.indexCount=a||0,this.buffer=new ne(e,{data:i instanceof Array?new Uint16Array(i):i,usage:Zt["BufferUsage"].Index|Zt["BufferUsage"].CopyDst})}return R()(t,[{key:"get",value:function(){return this.buffer}},{key:"subData",value:function(t){this.buffer.subData(t)}},{key:"destroy",value:function(){this.buffer.destroy()}}]),t}(),pe=function(){function t(e,n){A()(this,t),this.engine=e,this.options=n,this.colorTexture=void 0,this.depthTexture=void 0,this.width=0,this.height=0;var r=n,i=(r.width,r.height,r.color),a=(r.colors,r.depth);r.stencil;i&&(this.colorTexture=i),a&&(this.depthTexture=a)}return R()(t,[{key:"get",value:function(){var t,e;return{color:null===(t=this.colorTexture)||void 0===t?void 0:t.get(),depth:null===(e=this.depthTexture)||void 0===e?void 0:e.get()}}},{key:"destroy",value:function(){var t,e;null===(t=this.colorTexture)||void 0===t||t.destroy(),null===(e=this.depthTexture)||void 0===e||e.destroy()}},{key:"resize",value:function(t){var e,n,r=t.width,i=t.height;r===this.width&&i===this.height||(null===(e=this.colorTexture)||void 0===e||e.resize({width:r,height:i}),null===(n=this.depthTexture)||void 0===n||n.resize({width:r,height:i}));this.width=r,this.height=i}}]),t}(),de=(ie={},I()(ie,S["gl"].POINTS,Zt["PrimitiveTopology"].PointList),I()(ie,S["gl"].LINES,Zt["PrimitiveTopology"].LineList),I()(ie,S["gl"].LINE_LOOP,Zt["PrimitiveTopology"].LineList),I()(ie,S["gl"].LINE_STRIP,Zt["PrimitiveTopology"].LineStrip),I()(ie,S["gl"].TRIANGLES,Zt["PrimitiveTopology"].TriangleList),I()(ie,S["gl"].TRIANGLE_FAN,Zt["PrimitiveTopology"].TriangleList),I()(ie,S["gl"].TRIANGLE_STRIP,Zt["PrimitiveTopology"].TriangleStrip),ie),Ee=(ae={},I()(ae,S["gl"].NEVER,Zt["CompareFunction"].Never),I()(ae,S["gl"].ALWAYS,Zt["CompareFunction"].Always),I()(ae,S["gl"].LESS,Zt["CompareFunction"].Less),I()(ae,S["gl"].LEQUAL,Zt["CompareFunction"].LessEqual),I()(ae,S["gl"].GREATER,Zt["CompareFunction"].Greater),I()(ae,S["gl"].GEQUAL,Zt["CompareFunction"].GreaterEqual),I()(ae,S["gl"].EQUAL,Zt["CompareFunction"].Equal),I()(ae,S["gl"].NOTEQUAL,Zt["CompareFunction"].NotEqual),ae),ge=(oe={},I()(oe,S["gl"].FUNC_ADD,Zt["BlendOperation"].Add),I()(oe,S["gl"].MIN_EXT,Zt["BlendOperation"].Min),I()(oe,S["gl"].MAX_EXT,Zt["BlendOperation"].Max),I()(oe,S["gl"].FUNC_SUBTRACT,Zt["BlendOperation"].Subtract),I()(oe,S["gl"].FUNC_REVERSE_SUBTRACT,Zt["BlendOperation"].ReverseSubtract),oe),me=(se={},I()(se,S["gl"].ZERO,Zt["BlendFactor"].Zero),I()(se,S["gl"].ONE,Zt["BlendFactor"].One),I()(se,S["gl"].SRC_COLOR,Zt["BlendFactor"].SrcColor),I()(se,S["gl"].ONE_MINUS_SRC_COLOR,Zt["BlendFactor"].OneMinusSrcColor),I()(se,S["gl"].SRC_ALPHA,Zt["BlendFactor"].SrcAlpha),I()(se,S["gl"].ONE_MINUS_SRC_ALPHA,Zt["BlendFactor"].OneMinusSrcAlpha),I()(se,S["gl"].DST_COLOR,Zt["BlendFactor"].DstColor),I()(se,S["gl"].ONE_MINUS_DST_COLOR,Zt["BlendFactor"].OneMinusDstColor),I()(se,S["gl"].DST_ALPHA,Zt["BlendFactor"].DstAlpha),I()(se,S["gl"].ONE_MINUS_DST_ALPHA,Zt["BlendFactor"].OneMinusDstAlpha),I()(se,S["gl"].CONSTANT_COLOR,Zt["BlendFactor"].BlendColor),I()(se,S["gl"].ONE_MINUS_CONSTANT_COLOR,Zt["BlendFactor"].OneMinusBlendColor),I()(se,S["gl"].SRC_ALPHA_SATURATE,Zt["BlendFactor"].SrcAlphaSaturated),se),ve=(ue={},I()(ue,S["gl"].ALPHA,"r8unorm"),I()(ue,S["gl"].RGBA,"rgba8unorm"),I()(ue,S["gl"].DEPTH_COMPONENT,"depth32float"),I()(ue,S["gl"].DEPTH_STENCIL,"depth24plus-stencil8"),ue),ye=(ce={},I()(ce,S["gl"].NEAREST,"nearest"),I()(ce,S["gl"].LINEAR,"linear"),ce),Te=(le={},I()(le,S["gl"].REPEAT,"repeat"),I()(le,S["gl"].CLAMP_TO_EDGE,"clamp-to-edge"),I()(le,S["gl"].MIRRORED_REPEAT,"mirror-repeat"),le);function be(t){var e=t.cull;return e&&e.enable?e.face?e.face===S["gl"].FRONT?Zt["CullMode"].Front:Zt["CullMode"].Back:void 0:Zt["CullMode"].None}function Ae(t){var e=t.depth,n=(t.stencil,{compare:Zt["CompareFunction"].Always,depthFailOp:Zt["StencilOperation"].Keep,failOp:Zt["StencilOperation"].Keep,passOp:Zt["StencilOperation"].Keep});return{depthWriteEnabled:e&&e.enable,depthCompare:Ee[(null===e||void 0===e?void 0:e.func)||S["gl"].ALWAYS],format:Zt["TextureFormat"].Depth24PlusStencil8,stencilFront:n,stencilBack:n,stencilReadMask:4294967295,stencilWriteMask:4294967295}}function _e(t,e){var n=t.blend;return[{format:e,alphaBlend:{srcFactor:me[n&&n.func&&n.func.srcAlpha||S["gl"].ONE],dstFactor:me[n&&n.func&&n.func.dstAlpha||S["gl"].ZERO],operation:ge[n&&n.equation&&n.equation.alpha||S["gl"].FUNC_ADD]},colorBlend:{srcFactor:me[n&&n.func&&n.func.srcRGB||S["gl"].ONE],dstFactor:me[n&&n.func&&n.func.dstRGB||S["gl"].ZERO],operation:ge[n&&n.equation&&n.equation.rgb||S["gl"].FUNC_ADD]},writeMask:Zt["ColorWrite"].All}]}function Re(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function Se(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Re(Object(n),!0).forEach((function(e){I()(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Re(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function Ce(t){for(var e=0,n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];for(var a=0,o=r;a<o.length;a++){var s=o[a];e+=s.length}for(var u=new t(e),c=0,l=0,h=r;l<h.length;l++){var f=h[l];u.set(f,c),c+=f.length}return u}var Oe,Ne,Pe,xe=function(){function t(e,n){A()(this,t),this.engine=e,this.options=n,this.pipelineLayout=void 0,this.renderPipeline=void 0,this.uniformsBindGroupLayout=void 0,this.uniformBindGroup=void 0,this.uniformBuffer=void 0,this.uniforms={},this.uniformGPUBufferLayout=[],this.attributeCache={},this.indexBuffer=void 0,this.indexCount=void 0}return R()(t,[{key:"init",value:function(){var t=T()(v.a.mark((function t(){var e,n,r,i,a,o,s,u,c,l,h,f,p,d,E,g,m=this;return v.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e=this.options,n=e.vs,r=e.fs,i=e.attributes,a=e.uniforms,o=e.primitive,e.count,s=e.elements,u=e.depth,c=e.blend,l=e.stencil,h=e.cull,e.instances,t.next=3,this.compilePipelineStageDescriptor(n,r,null);case 3:f=t.sent,p=f.vertexStage,d=f.fragmentStage,a&&this.buildUniformBindGroup(a),s&&(this.indexBuffer=s.get(),this.indexCount=s.indexCount),E={vertexBuffers:Object.keys(i).map((function(t,e){var n=i[t],r=n.get(),a=r.arrayStride,o=r.stepMode,s=r.attributes;return m.attributeCache[t]=n,{arrayStride:a,stepMode:o,attributes:s}}))},g={sampleCount:this.engine.mainPassSampleCount,primitiveTopology:de[o||S["gl"].TRIANGLES],rasterizationState:Se(Se({},this.getDefaultRasterizationStateDescriptor()),{},{cullMode:be({cull:h})}),depthStencilState:Ae({depth:u,stencil:l}),colorStates:_e({blend:c},this.engine.options.swapChainFormat),layout:this.pipelineLayout,vertexStage:p,fragmentStage:d,vertexState:E},this.renderPipeline=this.engine.device.createRenderPipeline(g);case 11:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}()},{key:"addUniforms",value:function(t){this.uniforms=Se(Se({},this.uniforms),Ut(t))}},{key:"draw",value:function(t){var e=this,n=this.engine.getCurrentRenderPass(),r=Se(Se({},this.uniforms),Ut(t.uniforms||{})),i=[];Object.keys(r).forEach((function(t){var n=q()(r[t]);if("boolean"===n||"number"===n||Array.isArray(r[t])||r[t].BYTES_PER_ELEMENT){var a,o=null===(a=e.uniformGPUBufferLayout.find((function(e){var n=e.name;return n===t})))||void 0===a?void 0:a.offset;null!==o&&e.uniformBuffer.subData({data:r[t],offset:o})}else{var s,u=null===(s=e.uniformGPUBufferLayout.find((function(e){var n=e.name;return n===t})))||void 0===s?void 0:s.offset;if(null!==u){var c=r[t].get(),l=c.color||c,h=l.texture,f=l.sampler;f&&(i.push({binding:u,resource:f}),u++),i.push({binding:u,resource:h.createView()})}}})),this.uniformBuffer&&(i[0]={binding:0,resource:{buffer:this.uniformBuffer.get()}}),this.uniformBindGroup=this.engine.device.createBindGroup({layout:this.uniformsBindGroupLayout,entries:i}),this.renderPipeline&&n.setPipeline(this.renderPipeline),n.setBindGroup(0,this.uniformBindGroup),this.indexBuffer&&n.setIndexBuffer(this.indexBuffer.get(),Zt["IndexFormat"].Uint32,0),Object.keys(this.attributeCache).forEach((function(t,r){n.setVertexBuffer(0+r,e.attributeCache[t].get().buffer,0)})),this.indexBuffer?n.drawIndexed(this.indexCount,this.options.instances||1,0,0,0):n.draw(this.options.count||0,this.options.instances||0,0,0)}},{key:"destroy",value:function(){throw new Error("Method not implemented.")}},{key:"compilePipelineStageDescriptor",value:function(){var t=T()(v.a.mark((function t(e,n,r){var i,a,o;return v.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(i="#version 450\n",a=e,o=n,this.engine.options.useWGSL){t.next=10;break}return t.next=6,this.compileShaderToSpirV(e,"vertex",i);case 6:return a=t.sent,t.next=9,this.compileShaderToSpirV(n,"fragment",i);case 9:o=t.sent;case 10:return t.abrupt("return",this.createPipelineStageDescriptor(a,o));case 11:case"end":return t.stop()}}),t,this)})));function e(e,n,r){return t.apply(this,arguments)}return e}()},{key:"compileShaderToSpirV",value:function(t,e,n){return this.compileRawShaderToSpirV(n+t,e)}},{key:"compileRawShaderToSpirV",value:function(t,e){return this.engine.glslang.compileGLSL(t,e)}},{key:"createPipelineStageDescriptor",value:function(t,e){return{vertexStage:{module:this.engine.device.createShaderModule({code:t,isWHLSL:S["isSafari"]}),entryPoint:"main"},fragmentStage:{module:this.engine.device.createShaderModule({code:e,isWHLSL:S["isSafari"]}),entryPoint:"main"}}}},{key:"getDefaultRasterizationStateDescriptor",value:function(){return{frontFace:Zt["FrontFace"].CCW,cullMode:Zt["CullMode"].None,depthBias:0,depthBiasSlopeScale:0,depthBiasClamp:0}}},{key:"buildUniformBindGroup",value:function(t){var e=this,n=0,r=Ce.apply(void 0,[Float32Array].concat(K()(Object.keys(t).map((function(r){return t[r]?(e.uniformGPUBufferLayout.push({name:r,offset:n}),n+=4*(t[r].length||1),t[r]):[]}))))),i=[],a=!1;r.length&&(a=!0,i.push({binding:0,visibility:Zt["ShaderStage"].Fragment|Zt["ShaderStage"].Vertex,type:Zt["BindingType"].UniformBuffer})),Object.keys(t).filter((function(e){return null===t[e]})).forEach((function(t,n){e.uniformGPUBufferLayout.push({name:t,offset:2*n+(a?1:0)}),i.push({binding:2*n+(a?1:0),visibility:Zt["ShaderStage"].Fragment,type:Zt["BindingType"].Sampler},{binding:2*n+(a?1:0)+1,visibility:Zt["ShaderStage"].Fragment,type:Zt["BindingType"].SampledTexture})})),this.uniformsBindGroupLayout=this.engine.device.createBindGroupLayout({entries:i}),this.pipelineLayout=this.engine.device.createPipelineLayout({bindGroupLayouts:[this.uniformsBindGroupLayout]}),a&&(this.uniformBuffer=new ne(this.engine,{data:r instanceof Array?new Float32Array(r):r,usage:Zt["BufferUsage"].Uniform|Zt["BufferUsage"].CopyDst}))}}]),t}(),Ie=function(){function t(e,n){A()(this,t),this.engine=e,this.options=n,this.texture=void 0,this.sampler=void 0,this.width=void 0,this.height=void 0,this.createTexture()}return R()(t,[{key:"get",value:function(){return{texture:this.texture,sampler:this.sampler}}},{key:"update",value:function(){}},{key:"resize",value:function(t){var e=t.width,n=t.height;e===this.width&&n===this.height||(this.destroy(),this.createTexture()),this.width=e,this.height=n}},{key:"destroy",value:function(){this.texture&&this.texture.destroy()}},{key:"createTexture",value:function(){var t=this.options,e=(t.data,t.type),n=(void 0===e&&S["gl"].UNSIGNED_BYTE,t.width),r=t.height,i=(t.flipY,t.format),a=void 0===i?S["gl"].RGBA:i,o=(t.mipmap,t.wrapS),s=void 0===o?S["gl"].CLAMP_TO_EDGE:o,u=t.wrapT,c=void 0===u?S["gl"].CLAMP_TO_EDGE:u,l=t.aniso,h=void 0===l?0:l,f=(t.alignment,t.premultiplyAlpha,t.mag),p=void 0===f?S["gl"].NEAREST:f,d=t.min,E=void 0===d?S["gl"].NEAREST:d,g=t.colorSpace,m=(void 0===g&&S["gl"].BROWSER_DEFAULT_WEBGL,t.usage);this.width=n,this.height=r,this.texture=this.engine.device.createTexture({size:[n,r,1],mipLevelCount:1,sampleCount:1,dimension:Zt["TextureDimension"].E2d,format:ve[a],usage:m||Zt["TextureUsage"].Sampled|Zt["TextureUsage"].CopyDst}),(!m||m&Zt["TextureUsage"].Sampled)&&(this.sampler=this.engine.device.createSampler({addressModeU:Te[s],addressModeV:Te[c],addressModeW:Te[s],magFilter:ye[p],minFilter:ye[E],maxAnisotropy:h}))}}]),t}(),Le=(Oe=Object(C["injectable"])(),Oe((Pe=function(){function t(){var e=this;A()(this,t),this.supportWebGPU=!0,this.useWGSL=!1,this.options=void 0,this.canvas=void 0,this.context=void 0,this.glslang=void 0,this.adapter=void 0,this.device=void 0,this.swapChain=void 0,this.mainPassSampleCount=void 0,this.mainTexture=void 0,this.depthTexture=void 0,this.mainColorAttachments=void 0,this.mainTextureExtends=void 0,this.mainDepthAttachment=void 0,this.uploadEncoder=void 0,this.renderEncoder=void 0,this.computeEncoder=void 0,this.renderTargetEncoder=void 0,this.commandBuffers=new Array(4).fill(void 0),this.currentRenderPass=null,this.mainRenderPass=null,this.currentRenderTargetViewDescriptor=void 0,this.currentComputePass=null,this.bundleEncoder=void 0,this.tempBuffers=[],this.currentRenderTarget=null,this.uploadEncoderDescriptor={label:"upload"},this.renderEncoderDescriptor={label:"render"},this.renderTargetEncoderDescriptor={label:"renderTarget"},this.computeEncoderDescriptor={label:"compute"},this.pipelines={},this.computePipelines={},this.defaultSampleCount=4,this.clearDepthValue=1,this.clearStencilValue=0,this.transientViewport={x:1/0,y:0,width:0,height:0},this.cachedViewport={x:0,y:0,width:0,height:0},this.clear=function(t){t.framebuffer;var n=t.color,r=t.depth,i=t.stencil;e.options.supportCompute&&e.startComputePass(),e.currentRenderTarget?(e.currentRenderPass&&e.endRenderTargetRenderPass(),e.startRenderTargetRenderPass(e.currentRenderTarget,n||null,!!r,!!i)):(e.mainColorAttachments[0].loadValue=n||Zt["LoadOp"].Load,e.mainDepthAttachment.depthLoadValue=r||Zt["LoadOp"].Load,e.mainDepthAttachment.stencilLoadValue=i?e.clearStencilValue:Zt["LoadOp"].Load,e.mainRenderPass&&e.endMainRenderPass(),e.startMainRenderPass())},this.createModel=function(){var t=T()(v.a.mark((function t(n){var r;return v.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return r=new xe(e,n),t.next=3,r.init();case 3:return t.abrupt("return",r);case 4:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),this.createAttribute=function(t){return new ee(e,t)},this.createBuffer=function(t){return new ne(e,t)},this.createElements=function(t){return new fe(e,t)},this.createTexture2D=function(t){return new Ie(e,t)},this.createFramebuffer=function(t){return new pe(e,t)},this.useFramebuffer=function(t,n){e.currentRenderTarget&&e.unbindFramebuffer(e.currentRenderTarget),e.currentRenderTarget=t,e.currentRenderTargetViewDescriptor={dimension:Zt["TextureViewDimension"].E2d,arrayLayerCount:1,aspect:Zt["TextureAspect"].All},e.currentRenderPass=null,n()},this.createComputeModel=function(){var t=T()(v.a.mark((function t(n){var r;return v.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return r=new he(e,n),t.next=3,r.init();case 3:return t.abrupt("return",r);case 4:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),this.getCanvas=function(){return e.canvas},this.getGLContext=function(){throw new Error("Method not implemented.")},this.viewport=function(t){var n=t.x,r=t.y,i=t.width,a=t.height;if(e.currentRenderPass){if(e.transientViewport.x!==1/0){var o=e.getCurrentRenderPass();o.setViewport(e.transientViewport.x,e.transientViewport.y,e.transientViewport.width,e.transientViewport.height,0,1)}else if(n!==e.cachedViewport.x||r!==e.cachedViewport.y||i!==e.cachedViewport.width||a!==e.cachedViewport.height){e.cachedViewport={x:n,y:r,width:i,height:a};var s=e.getCurrentRenderPass();s.setViewport(n,r,i,a,0,1)}}else e.transientViewport={x:n,y:r,width:i,height:a}},this.readPixels=function(t){throw new Error("Method not implemented.")}}return R()(t,[{key:"isFloatSupported",value:function(){return!0}},{key:"init",value:function(){var t=T()(v.a.mark((function t(e){return v.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return this.canvas=e.canvas,this.options=e,this.useWGSL=!!e.useWGSL,this.mainPassSampleCount=e.antialiasing?this.defaultSampleCount:1,t.next=6,this.initGlslang();case 6:this.initContextAndSwapChain(),this.initMainAttachments();case 8:case"end":return t.stop()}}),t,this)})));function e(e){return t.apply(this,arguments)}return e}()},{key:"setScissor",value:function(t){throw new Error("Method not implemented.")}},{key:"destroy",value:function(){this.mainTexture&&this.mainTexture.destroy(),this.depthTexture&&this.depthTexture.destroy(),this.tempBuffers.forEach((function(t){return t.destroy()})),this.tempBuffers=[]}},{key:"beginFrame",value:function(){this.uploadEncoder=this.device.createCommandEncoder(this.uploadEncoderDescriptor),this.renderEncoder=this.device.createCommandEncoder(this.renderEncoderDescriptor),this.renderTargetEncoder=this.device.createCommandEncoder(this.renderTargetEncoderDescriptor),this.options.supportCompute&&(this.computeEncoder=this.device.createCommandEncoder(this.computeEncoderDescriptor))}},{key:"endFrame",value:function(){this.options.supportCompute&&this.endComputePass(),this.endMainRenderPass(),this.commandBuffers[0]=this.uploadEncoder.finish(),this.commandBuffers[1]=this.renderEncoder.finish(),this.options.supportCompute&&(this.commandBuffers[2]=this.computeEncoder.finish()),this.commandBuffers[3]=this.renderTargetEncoder.finish(),S["isSafari"]?this.device.getQueue().submit(this.commandBuffers.filter((function(t){return t}))):this.device.defaultQueue.submit(this.commandBuffers.filter((function(t){return t})))}},{key:"getCurrentRenderPass",value:function(){return this.currentRenderTarget&&!this.currentRenderPass?this.startRenderTargetRenderPass(this.currentRenderTarget,null,!1,!1):this.currentRenderPass||this.startMainRenderPass(),this.currentRenderPass}},{key:"initGlslang",value:function(){var t=T()(v.a.mark((function t(){var e,n;return v.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,$t();case 2:return this.glslang=t.sent,t.next=5,null===(e=navigator)||void 0===e||null===(n=e.gpu)||void 0===n?void 0:n.requestAdapter();case 5:return this.adapter=t.sent,t.next=8,this.adapter.requestDevice();case 8:this.device=t.sent;case 9:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}()},{key:"initContextAndSwapChain",value:function(){this.context=this.canvas.getContext(S["isSafari"]?"gpu":"gpupresent"),this.swapChain=this.context.configureSwapChain({device:this.device,format:this.options.swapChainFormat,usage:Zt["TextureUsage"].OutputAttachment|Zt["TextureUsage"].CopySrc})}},{key:"initMainAttachments",value:function(){if(this.mainTextureExtends={width:this.canvas.width,height:this.canvas.height,depth:1},this.options.antialiasing){var t={size:this.mainTextureExtends,mipLevelCount:1,sampleCount:this.mainPassSampleCount,dimension:Zt["TextureDimension"].E2d,format:Zt["TextureFormat"].BGRA8Unorm,usage:Zt["TextureUsage"].OutputAttachment};this.mainTexture&&this.mainTexture.destroy(),this.mainTexture=this.device.createTexture(t),this.mainColorAttachments=[{attachment:S["isSafari"]?this.mainTexture.createDefaultView():this.mainTexture.createView(),loadValue:[0,0,0,1],storeOp:Zt["StoreOp"].Store}]}else this.mainColorAttachments=[{attachment:S["isSafari"]?this.swapChain.getCurrentTexture().createDefaultView():this.swapChain.getCurrentTexture().createView(),loadValue:[0,0,0,1],storeOp:Zt["StoreOp"].Store}];var e={size:this.mainTextureExtends,mipLevelCount:1,sampleCount:this.mainPassSampleCount,dimension:Zt["TextureDimension"].E2d,format:S["isSafari"]?"depth32float-stencil8":Zt["TextureFormat"].Depth24PlusStencil8,usage:Zt["TextureUsage"].OutputAttachment};this.depthTexture&&this.depthTexture.destroy(),this.depthTexture=this.device.createTexture(e),this.mainDepthAttachment={attachment:S["isSafari"]?this.depthTexture.createDefaultView():this.depthTexture.createView(),depthLoadValue:this.clearDepthValue,depthStoreOp:Zt["StoreOp"].Store,stencilLoadValue:this.clearStencilValue,stencilStoreOp:Zt["StoreOp"].Store}}},{key:"startComputePass",value:function(){this.currentComputePass&&this.endComputePass(),this.currentComputePass=this.computeEncoder.beginComputePass()}},{key:"startMainRenderPass",value:function(){this.currentRenderPass&&!this.currentRenderTarget&&this.endMainRenderPass(),this.options.antialiasing?this.mainColorAttachments[0].resolveTarget=S["isSafari"]?this.swapChain.getCurrentTexture().createDefaultView():this.swapChain.getCurrentTexture().createView():this.mainColorAttachments[0].attachment=S["isSafari"]?this.swapChain.getCurrentTexture().createDefaultView():this.swapChain.getCurrentTexture().createView(),this.currentRenderPass=this.renderEncoder.beginRenderPass({colorAttachments:this.mainColorAttachments,depthStencilAttachment:this.mainDepthAttachment}),this.mainRenderPass=this.currentRenderPass,this.cachedViewport&&this.viewport(this.cachedViewport)}},{key:"startRenderTargetRenderPass",value:function(t,e,n){var r,i,a,o=arguments.length>3&&void 0!==arguments[3]&&arguments[3],s=null===(r=t.get().color)||void 0===r?void 0:r.texture;s&&(a=s.createView(this.currentRenderTargetViewDescriptor));var u,c=null===(i=t.get().depth)||void 0===i?void 0:i.texture;c&&(u=c.createView());var l=this.renderTargetEncoder.beginRenderPass({colorAttachments:[{attachment:a,loadValue:null!==e?e:Zt["LoadOp"].Load,storeOp:Zt["StoreOp"].Store}],depthStencilAttachment:c&&u?{attachment:u,depthLoadValue:n?this.clearDepthValue:Zt["LoadOp"].Load,depthStoreOp:Zt["StoreOp"].Store,stencilLoadValue:o?this.clearStencilValue:Zt["LoadOp"].Load,stencilStoreOp:Zt["StoreOp"].Store}:void 0});this.currentRenderPass=l,this.cachedViewport&&this.viewport(this.cachedViewport)}},{key:"endMainRenderPass",value:function(){this.currentRenderPass===this.mainRenderPass&&null!==this.currentRenderPass&&(this.currentRenderPass.endPass(),this.resetCachedViewport(),this.currentRenderPass=null,this.mainRenderPass=null)}},{key:"endComputePass",value:function(){this.currentComputePass&&(this.currentComputePass.endPass(),this.currentComputePass=null)}},{key:"endRenderTargetRenderPass",value:function(){this.currentRenderPass&&(this.currentRenderPass.endPass(),this.resetCachedViewport())}},{key:"resetCachedViewport",value:function(){this.cachedViewport={x:0,y:0,width:0,height:0}}},{key:"unbindFramebuffer",value:function(t){this.currentRenderPass&&this.currentRenderPass!==this.mainRenderPass&&this.endRenderTargetRenderPass(),this.transientViewport.x=1/0,this.currentRenderTarget=null,this.currentRenderPass=this.mainRenderPass}}]),t}(),Ne=Pe))||Ne)},e175:function(t,e,n){"use strict";var r=n("93ac");n.d(e,"gl",(function(){return r["a"]}));n("a256"),n("035e"),n("39ba"),n("f911"),n("9cee"),n("c9a5"),n("57fc"),n("bdd3"),n("362a"),n("257d"),n("6635")},e1d3:function(t,e,n){"use strict";n.d(e,"a",(function(){return O}));var r,i,a,o,s,u,c,l,h=n("a34a"),f=n.n(h),p=n("c973"),d=n.n(p),E=n("c86f"),g=n.n(E),m=n("970b"),v=n.n(m),y=n("5bc3"),T=n.n(y),b=n("53ec"),A=n.n(b),_=(n("d400"),n("e1c6")),R=n("1a3d"),S=n("fae1"),C=n("93ac"),O=(r=Object(_["injectable"])(),i=Object(_["inject"])(S["a"].GeometryComponentManager),a=Object(_["inject"])(S["a"].RenderEngine),r((l=function(){function t(){v()(this,t),g()(this,"geometry",u,this),g()(this,"engine",c,this)}return T()(t,[{key:"execute",value:function(){var t=d()(f.a.mark((function t(){var e=this;return f.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:this.geometry.forEach((function(t,n){n.dirty&&(n.attributes.forEach((function(t){if(t.dirty&&t.data){var n;if(t.buffer)null===(n=t.buffer)||void 0===n||n.subData({data:t.data,offset:0});else t.buffer=e.engine.createBuffer({data:t.data,type:C["a"].FLOAT});t.dirty=!1}})),n.indices&&(n.indicesBuffer?n.indicesBuffer.subData({data:n.indices,offset:0}):n.indicesBuffer=e.engine.createElements({data:n.indices,count:n.indices.length,type:C["a"].UNSIGNED_INT,usage:C["a"].STATIC_DRAW})),n.dirty=!1)}));case 1:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}()},{key:"tearDown",value:function(){this.geometry.forEach((function(t,e){e.indicesBuffer&&e.indicesBuffer.destroy(),e.attributes.forEach((function(t){t.buffer&&t.buffer.destroy()}))})),this.geometry.clear()}},{key:"createBufferGeometry",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{vertexCount:3},e=t.vertexCount,n=Object(R["createEntity"])();return this.geometry.create(n,{vertexCount:e})}},{key:"createInstancedBufferGeometry",value:function(t){var e=t.maxInstancedCount,n=t.vertexCount,r=Object(R["createEntity"])();return this.geometry.create(r,{maxInstancedCount:e,vertexCount:n})}}]),t}(),s=l,u=A()(s.prototype,"geometry",[i],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),c=A()(s.prototype,"engine",[a],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),o=s))||o)},e3c8:function(t,e,n){"use strict";n.d(e,"a",(function(){return r["a"]})),n.d(e,"b",(function(){return a["a"]}));var r=n("10d1"),i=(n("970b"),n("5bc3"),n("20e7")),a=(i["e"].create(),n("a9fb"));n("7175"),i["e"].create(),i["e"].create(),i["e"].create(),i["e"].create(),i["e"].create()},e81f:function(t,e,n){"use strict";n.d(e,"a",(function(){return m}));var r=n("970b"),i=n.n(r),a=n("3c96"),o=n.n(a),s=n("ed6d"),u=n.n(s),c=n("6b58"),l=n.n(c),h=n("36c6"),f=n.n(h),p=n("a03b"),d=n("10d1");function E(t){var e=g();return function(){var n,r=f()(t);if(e){var i=f()(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return l()(this,n)}}function g(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}var m=function(t){u()(n,t);var e=E(n);function n(t){var r;return i()(this,n),r=e.call(this,t),r.material=void 0,r.geometry=void 0,r.aabb=new d["a"],r.aabbDirty=!0,r.model=void 0,r.visible=!0,r.children=[],Object.assign(o()(r),t),r}return n}(p["a"])},f238:function(t,e,n){"use strict";n("327f"),n("2c64"),n("c99e")},f72d:function(t,e,n){"use strict";n.d(e,"a",(function(){return r})),n.d(e,"b",(function(){return a}));var r=-1,i=1;function a(){return i++}},f781:function(t,e,n){"use strict";n.d(e,"a",(function(){return z}));var r,i,a,o,s,u,c,l,h,f,p,d,E,g,m,v,y,T,b,A,_,R,S,C,O=n("a34a"),N=n.n(O),P=n("c973"),x=n.n(P),I=n("c86f"),L=n.n(I),w=n("970b"),D=n.n(w),M=n("5bc3"),k=n.n(M),B=n("53ec"),U=n.n(B),F=(n("d400"),n("20e7")),j=n("e1c6"),G=n("fae1"),V=n("93ac");function H(t,e){var n;if("undefined"===typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(n=X(t))||e&&t&&"number"===typeof t.length){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,s=!1;return{s:function(){n=t[Symbol.iterator]()},n:function(){var t=n.next();return o=t.done,t},e:function(t){s=!0,a=t},f:function(){try{o||null==n.return||n.return()}finally{if(s)throw a}}}}function X(t,e){if(t){if("string"===typeof t)return W(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?W(t,e):void 0}}function W(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}var z=(r=Object(j["injectable"])(),i=Object(j["inject"])(G["a"].MeshComponentManager),a=Object(j["inject"])(G["a"].GeometryComponentManager),o=Object(j["inject"])(G["a"].MaterialComponentManager),s=Object(j["inject"])(G["a"].CullableComponentManager),u=Object(j["inject"])(G["a"].TransformComponentManager),c=Object(j["inject"])(G["a"].HierarchyComponentManager),l=Object(j["inject"])(G["a"].Systems),h=Object(j["named"])(G["a"].FrameGraphSystem),f=Object(j["inject"])(G["a"].RenderEngine),p=Object(j["inject"])(G["a"].ResourcePool),r((C=S=function(){function t(){var e=this;D()(this,t),L()(this,"mesh",g,this),L()(this,"geometry",m,this),L()(this,"material",v,this),L()(this,"cullable",y,this),L()(this,"transform",T,this),L()(this,"hierarchy",b,this),L()(this,"frameGraphSystem",A,this),L()(this,"engine",_,this),L()(this,"resourcePool",R,this),this.modelCache={},this.setup=function(t,e,n){var r=t.createRenderTarget(e,"color buffer",{width:1,height:1,usage:V["a"].RENDER_ATTACHMENT|V["a"].SAMPLED|V["a"].COPY_SRC});n.data={output:e.write(t,r)}},this.execute=function(){var t=x()(N.a.mark((function t(n,r,i){var a,o,s,u,c,l;return N.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:a=n.getResourceNode(r.data.output),o=e.resourcePool.getOrCreateResource(a.resource),s=H(i),t.prev=3,s.s();case 5:if((u=s.n()).done){t.next=11;break}return c=u.value,t.next=9,e.initView(c);case 9:t.next=5;break;case 11:t.next=16;break;case 13:t.prev=13,t.t0=t["catch"](3),s.e(t.t0);case 16:return t.prev=16,s.f(),t.finish(16);case 19:l=e.engine.getCanvas(),o.resize({width:l.width,height:l.height}),e.engine.setScissor({enable:!1}),e.engine.clear({framebuffer:o,color:i[0].getClearColor(),depth:1}),e.engine.useFramebuffer(o,(function(){var t,n=H(i);try{for(n.s();!(t=n.n()).done;){var r=t.value;e.renderView(r)}}catch(a){n.e(a)}finally{n.f()}}));case 24:case"end":return t.stop()}}),t,null,[[3,13,16,19]])})));return function(e,n,r){return t.apply(this,arguments)}}()}return k()(t,[{key:"renderView",value:function(t){var e=t.getScene(),n=t.getCamera(),r=n.getViewTransform(),i=F["b"].multiply(F["b"].create(),n.getPerspective(),r);n.getFrustum().extractFromVPMatrix(i);var a=t.getViewport(),o=a.x,s=a.y,u=a.width,c=a.height;this.engine.viewport({x:o,y:s,width:u,height:c});var l,h=H(e.getEntities());try{for(h.s();!(l=h.n()).done;){var f=l.value;this.renderMesh(f,{camera:n,view:t,viewMatrix:r})}}catch(p){h.e(p)}finally{h.f()}}},{key:"renderMesh",value:function(t,e){var n=e.camera,r=e.view,i=e.viewMatrix,a=this.mesh.getComponentByEntity(t);if(a&&a.visible){var o=a.material,s=a.geometry;if(s&&!s.dirty&&o){var u=this.transform.getComponentByEntity(t),c=F["b"].multiply(F["b"].create(),i,u.worldTransform),l=r.getViewport(),h=l.width,f=l.height;o.setUniform({projectionMatrix:n.getPerspective(),modelViewMatrix:c,modelMatrix:u.worldTransform,viewMatrix:i,cameraPosition:n.getPosition(),u_viewport:[h,f]}),a.model&&(a.model.draw({uniforms:o.uniforms.reduce((function(t,e){return t[e.name]=e.data,t}),{})}),o.uniforms.forEach((function(t){t.dirty=!1})),o.dirty=!1)}}}},{key:"initMesh",value:function(){var t=x()(N.a.mark((function t(e,n){var r,i,a,o,s,u,c,l;return N.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(r=this.mesh.getComponentByEntity(e),r){t.next=3;break}return t.abrupt("return");case 3:if(i=r.material,a=r.geometry,a&&!a.dirty&&i){t.next=7;break}return t.abrupt("return");case 7:if(r.model){t.next=24;break}if(o="m-".concat(i.entity,"-g-").concat(a.entity),!this.modelCache[o]){t.next=12;break}return r.model=this.modelCache[o],t.abrupt("return");case 12:return i.setUniform({projectionMatrix:1,modelViewMatrix:1,modelMatrix:1,viewMatrix:1,cameraPosition:1,u_viewport:1}),s=this.engine,u=s.createModel,c=s.createAttribute,l={vs:i.vertexShaderGLSL,fs:i.fragmentShaderGLSL,defines:i.defines,attributes:a.attributes.reduce((function(t,e){return e.data&&e.buffer&&(t[e.name]=c({buffer:e.buffer,attributes:e.attributes,arrayStride:e.arrayStride,stepMode:e.stepMode,divisor:"vertex"===e.stepMode?0:1})),t}),{}),uniforms:i.uniforms.reduce((function(t,e){return t[e.name]=e.data,t}),{}),scissor:{enable:!0,box:function(){return n.getViewport()}}},i.cull&&(l.cull=i.cull),i.depth&&(l.depth=i.depth),i.blend&&(l.blend=i.blend),a.indicesBuffer&&(l.elements=a.indicesBuffer),a.maxInstancedCount&&(l.instances=a.maxInstancedCount,l.count=a.vertexCount||3),t.next=22,u(l);case 22:r.model=t.sent,this.modelCache[o]=r.model;case 24:case"end":return t.stop()}}),t,this)})));function e(e,n){return t.apply(this,arguments)}return e}()},{key:"initView",value:function(){var t=x()(N.a.mark((function t(e){var n,r,i,a;return N.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:n=e.getScene(),r=H(n.getEntities()),t.prev=2,r.s();case 4:if((i=r.n()).done){t.next=10;break}return a=i.value,t.next=8,this.initMesh(a,e);case 8:t.next=4;break;case 10:t.next=15;break;case 12:t.prev=12,t.t0=t["catch"](2),r.e(t.t0);case 15:return t.prev=15,r.f(),t.finish(15);case 18:case"end":return t.stop()}}),t,this,[[2,12,15,18]])})));function e(e){return t.apply(this,arguments)}return e}()}]),t}(),S.IDENTIFIER="Render Pass",E=C,g=U()(E.prototype,"mesh",[i],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),m=U()(E.prototype,"geometry",[a],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),v=U()(E.prototype,"material",[o],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),y=U()(E.prototype,"cullable",[s],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),T=U()(E.prototype,"transform",[u],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),b=U()(E.prototype,"hierarchy",[c],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),A=U()(E.prototype,"frameGraphSystem",[l,h],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),_=U()(E.prototype,"engine",[f],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),R=U()(E.prototype,"resourcePool",[p],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),d=E))||d)},f911:function(t,e){},fae1:function(t,e,n){"use strict";n.d(e,"a",(function(){return r}));var r={HierarchyComponentManager:Symbol("HierarchyComponentManager"),TransformComponentManager:Symbol("TransformComponentManager"),NameComponentManager:Symbol("NameComponentManager"),SceneGraphSystem:Symbol("SceneGraphSystem"),FrameGraphSystem:Symbol("FrameGraphSystem"),ResourcePool:Symbol("ResourcePool"),ResourceHandleComponentManager:Symbol("ResourceHandleComponentManager"),PassNodeComponentManager:Symbol("PassNodeComponentManager"),RendererSystem:Symbol("RendererSystem"),RenderPass:Symbol("RenderPass"),RenderPassFactory:Symbol("Factory<IRenderPass>"),Renderable:Symbol("Factory<IRenderPass>"),MeshSystem:Symbol("MeshSystem"),MeshComponentManager:Symbol("MeshComponentManager"),CullableComponentManager:Symbol("CullableComponentManager"),Geometry:Symbol("Geometry"),GeometrySystem:Symbol("GeometrySystem"),GeometryComponentManager:Symbol("GeometryComponentManager"),Material:Symbol("Material"),MaterialSystem:Symbol("MaterialSystem"),MaterialComponentManager:Symbol("MaterialComponentManager"),ForwardRenderPath:Symbol("ForwardRenderPath"),ComputeSystem:Symbol("ComputeSystem"),ComputeComponentManager:Symbol("ComputeComponentManager"),ComputeStrategy:Symbol("ComputeStrategy"),Systems:Symbol("Systems"),World:Symbol("World"),RenderEngine:Symbol("RenderEngine"),WebGPUEngine:Symbol("WebGPUEngine"),WebGLEngine:Symbol("WebGLEngine"),ShaderModuleService:Symbol("ShaderModuleService"),ConfigService:Symbol("ConfigService"),InteractorService:Symbol("InteractorService"),IEventEmitter:Symbol("IEventEmitter"),Light:Symbol("Light")}},fb10:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"a",(function(){return o}));var r=n("278c"),i=n.n(r);n("20e7");function a(t,e){return e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[4],e[4]=t[5],e[5]=t[6],e[6]=t[8],e[7]=t[9],e[8]=t[10],e}function o(t){var e=i()(t,3),n=e[0],r=e[1],a=e[2],o=n+256*r+65536*a-1;return o}}}]);