(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-16845329"],{"0792":function(e,t,s){"use strict";s("43e2")},"43e2":function(e,t,s){},b471:function(e,t,s){"use strict";s.r(t);var i=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"bottom-menu-container"},[e._l(e.menuList,(function(t,i){return[s("div",{key:t.label,staticClass:"menu-item",class:[{active:e.activeMenu.includes(i)},t.disable?"disable-btn":"cursor-btn"],on:{click:function(s){return e.toggleMenu(t)}}},[s("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:t.name,placement:"top"}},[s("span",[s("CommonSVG",{attrs:{color:"#FFFFFF","icon-class":t.icon,size:16}})],1)])],1),t.speed?[s("transition",{attrs:{name:"sliderFade"}},[s("div",{directives:[{name:"show",rawName:"v-show",value:t.show,expression:"item.show"}],staticClass:"item-slider"},[s("el-slider",{staticStyle:{width:"100%"},attrs:{min:t.min,max:t.max,step:t.step},on:{change:function(s){e.setRoamSpeed(s,t)}},model:{value:t.speed,callback:function(s){e.$set(t,"speed",s)},expression:"item.speed"}})],1)])]:e._e()]}))],2)},o=[],n=(s("d3b7"),s("3ca3"),s("ddb0"),s("159b"),s("caad"),s("2532"),{name:"Roam",components:{CommonSVG:function(){return s.e("chunk-51f90655").then(s.bind(null,"17d0"))}},data:function(){return{menuList:[{id:0,name:this.$t("bottomMenu.roam.label"),label:"gravity",icon:"gravity_feature",disable:!0},{id:1,name:this.$t("bottomMenu.roam.label1"),label:"collide",icon:"collision_feature",disable:!0},{id:2,name:this.$t("bottomMenu.roam.label2"),label:"moving",icon:"move_speed_feature",speed:1,width:"0px",show:!1,max:10,min:1,step:1,disable:!0},{id:3,name:this.$t("bottomMenu.roam.label3"),label:"rotate",icon:"rotate_speed_feature",speed:10,width:"0px",show:!1,max:10,min:1,step:1,disable:!0},{id:4,name:this.$t("bottomMenu.roam.label4"),label:"angleView",icon:"distance_feature",speed:3,width:"0px",show:!1,max:10,min:3,step:1,disable:!0},{id:5,name:this.$t("menuIconName.exit"),label:"quit",icon:"quit"}],menuList2:[],activeMenu:new Array(4),roamActived:!1,roamTipsObj:null}},created:function(){document.addEventListener("keyup",this.onEscKeyUp)},mounted:function(){window.scene.mv.status.selectable=!1,this.roamTipsObj=this.$message({type:"success",message:this.$t("bottomMenu.roam.message"),duration:0}),window.scene.mv.events.singleSelection.on("default",this.singleSelectionFn),this.$store.commit("toogleRoamActive",!0)},methods:{onEscKeyUp:function(e){27==e.keyCode&&"Escape"===e.key&&this.toggleMenu({id:5})},setRoamSpeed:function(e,t){switch(this.$set(this.menuList[t.id],"speed",e),t.label){case"rotate":window.scene.mv.tools.roam.rotationSpeedMultiplier=.05*e;break;case"moving":window.scene.mv.tools.roam.movementSpeedMultiplier=5*e;break;case"angleView":window.scene.mv.tools.roam.cameraToPersonDistance=e;break}},singleSelectionFn:function(e){var t=window.scene.queryPosition(new window.scene.mv._THREE.Vector2(e.clientX,e.clientY));""!=t&&void 0!=t?(window.scene.mv.tools.roam.movementSpeedMultiplier=5,window.scene.mv.tools.roam.rotationSpeedMultiplier=.5,window.scene.mv.tools.roam.isGravity=!1,window.scene.mv.tools.roam.isCollision=!1,window.scene.mv.tools.roam.active(t),window.scene.mv.events.singleSelection.off("default",this.singleSelectionFn),document.removeEventListener("keyup",this.onEscKeyUp),window.scene.mv.status.selectable=!0,this.roamActived=!0,this.menuList.forEach((function(e){e.disable&&(e.disable=!1)})),this.roamTipsObj.close(),this.roamTipsObj=null):this.$message.error(this.$t("messageTips.errorCheckCoordinate"))},toggleMenu:function(e){var t=this;if(e.disable)return!1;var s=e.id,i=null;switch(this.activeMenu.includes(s)?(i=!1,this.$set(this.activeMenu,s,-1)):(i=!0,this.$set(this.activeMenu,s,s)),s){case 0:window.scene.mv.tools.roam.isGravity=i;break;case 1:window.scene.mv.tools.roam.isCollision=i;break;case 2:e.show?this.$set(this.menuList[s],"show",!1):this.$set(this.menuList[s],"show",!0);break;case 3:e.show?this.$set(this.menuList[s],"show",!1):this.$set(this.menuList[s],"show",!0);break;case 4:e.show?this.$set(this.menuList[s],"show",!1):this.$set(this.menuList[s],"show",!0);break;case 5:this.$nextTick((function(){t.$store.commit("toggleMenuActive","empty"),t.$store.commit("toggleBottomMenuActive",""),t.$store.commit("toggleActiveDialog","empty")}));break}}},beforeDestroy:function(){this.$store.commit("toogleRoamActive",!1),this.roamActived?window.scene.mv.tools.roam.deactive():(window.scene.mv.status.selectable=!0,window.scene.mv.events.singleSelection.off("default",this.singleSelectionFn),document.removeEventListener("keyup",this.onEscKeyUp),this.roamTipsObj&&(this.roamTipsObj.close(),this.roamTipsObj=null)),window.scene.render()}}),a=n,c=(s("0792"),s("2877")),l=Object(c["a"])(a,i,o,!1,null,"0b06ff43",null);t["default"]=l.exports}}]);