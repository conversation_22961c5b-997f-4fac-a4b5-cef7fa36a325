(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-65c23028"],{"04a5":function(e,t,n){"use strict";n.d(t,"a",(function(){return S}));var i=n("fdcc"),o=n("308f"),r=n("3742"),s=n("2e5d"),a=n("7061"),u=n("6a89"),l=n("8025"),d=function(){function e(e){this._selTrackedRange=null,this._trackSelection=!0,this._setState(e,new s["f"](new u["a"](1,1,1,1),0,new a["a"](1,1),0),new s["f"](new u["a"](1,1,1,1),0,new a["a"](1,1),0))}return e.prototype.dispose=function(e){this._removeTrackedRange(e)},e.prototype.startTrackingSelection=function(e){this._trackSelection=!0,this._updateTrackedRange(e)},e.prototype.stopTrackingSelection=function(e){this._trackSelection=!1,this._removeTrackedRange(e)},e.prototype._updateTrackedRange=function(e){this._trackSelection&&(this._selTrackedRange=e.model._setTrackedRange(this._selTrackedRange,this.modelState.selection,0))},e.prototype._removeTrackedRange=function(e){this._selTrackedRange=e.model._setTrackedRange(this._selTrackedRange,null,0)},e.prototype.asCursorState=function(){return new s["d"](this.modelState,this.viewState)},e.prototype.readSelectionFromMarkers=function(e){var t=e.model._getTrackedRange(this._selTrackedRange);return 0===this.modelState.selection.getDirection()?new l["a"](t.startLineNumber,t.startColumn,t.endLineNumber,t.endColumn):new l["a"](t.endLineNumber,t.endColumn,t.startLineNumber,t.startColumn)},e.prototype.ensureValidState=function(e){this._setState(e,this.modelState,this.viewState)},e.prototype.setState=function(e,t,n){this._setState(e,t,n)},e.prototype._setState=function(e,t,n){if(t){r=e.model.validateRange(t.selectionStart);var i=t.selectionStart.equalsRange(r)?t.selectionStartLeftoverVisibleColumns:0,o=(l=e.model.validatePosition(t.position),t.position.equals(l)?t.leftoverVisibleColumns:0);t=new s["f"](r,i,l,o)}else{if(!n)return;var r=e.model.validateRange(e.convertViewRangeToModelRange(n.selectionStart)),l=e.model.validatePosition(e.convertViewPositionToModelPosition(n.position.lineNumber,n.position.column));t=new s["f"](r,n.selectionStartLeftoverVisibleColumns,l,n.leftoverVisibleColumns)}if(n){h=e.validateViewRange(n.selectionStart,t.selectionStart),m=e.validateViewPosition(n.position,t.position);n=new s["f"](h,t.selectionStartLeftoverVisibleColumns,m,t.leftoverVisibleColumns)}else{var d=e.convertModelPositionToViewPosition(new a["a"](t.selectionStart.startLineNumber,t.selectionStart.startColumn)),c=e.convertModelPositionToViewPosition(new a["a"](t.selectionStart.endLineNumber,t.selectionStart.endColumn)),h=new u["a"](d.lineNumber,d.column,c.lineNumber,c.column),m=e.convertModelPositionToViewPosition(t.position);n=new s["f"](h,t.selectionStartLeftoverVisibleColumns,m,t.leftoverVisibleColumns)}this.modelState=t,this.viewState=n,this._updateTrackedRange(e)},e}(),c=function(){function e(e){this.context=e,this.primaryCursor=new d(e),this.secondaryCursors=[],this.lastAddedCursorIndex=0}return e.prototype.dispose=function(){this.primaryCursor.dispose(this.context),this.killSecondaryCursors()},e.prototype.startTrackingSelections=function(){this.primaryCursor.startTrackingSelection(this.context);for(var e=0,t=this.secondaryCursors.length;e<t;e++)this.secondaryCursors[e].startTrackingSelection(this.context)},e.prototype.stopTrackingSelections=function(){this.primaryCursor.stopTrackingSelection(this.context);for(var e=0,t=this.secondaryCursors.length;e<t;e++)this.secondaryCursors[e].stopTrackingSelection(this.context)},e.prototype.updateContext=function(e){this.context=e},e.prototype.ensureValidState=function(){this.primaryCursor.ensureValidState(this.context);for(var e=0,t=this.secondaryCursors.length;e<t;e++)this.secondaryCursors[e].ensureValidState(this.context)},e.prototype.readSelectionFromMarkers=function(){var e=[];e[0]=this.primaryCursor.readSelectionFromMarkers(this.context);for(var t=0,n=this.secondaryCursors.length;t<n;t++)e[t+1]=this.secondaryCursors[t].readSelectionFromMarkers(this.context);return e},e.prototype.getAll=function(){var e=[];e[0]=this.primaryCursor.asCursorState();for(var t=0,n=this.secondaryCursors.length;t<n;t++)e[t+1]=this.secondaryCursors[t].asCursorState();return e},e.prototype.getViewPositions=function(){var e=[];e[0]=this.primaryCursor.viewState.position;for(var t=0,n=this.secondaryCursors.length;t<n;t++)e[t+1]=this.secondaryCursors[t].viewState.position;return e},e.prototype.getSelections=function(){var e=[];e[0]=this.primaryCursor.modelState.selection;for(var t=0,n=this.secondaryCursors.length;t<n;t++)e[t+1]=this.secondaryCursors[t].modelState.selection;return e},e.prototype.getViewSelections=function(){var e=[];e[0]=this.primaryCursor.viewState.selection;for(var t=0,n=this.secondaryCursors.length;t<n;t++)e[t+1]=this.secondaryCursors[t].viewState.selection;return e},e.prototype.setSelections=function(e){this.setStates(s["d"].fromModelSelections(e))},e.prototype.getPrimaryCursor=function(){return this.primaryCursor.asCursorState()},e.prototype.setStates=function(e){null!==e&&(this.primaryCursor.setState(this.context,e[0].modelState,e[0].viewState),this._setSecondaryStates(e.slice(1)))},e.prototype._setSecondaryStates=function(e){var t=this.secondaryCursors.length,n=e.length;if(t<n)for(var i=n-t,o=0;o<i;o++)this._addSecondaryCursor();else if(t>n){var r=t-n;for(o=0;o<r;o++)this._removeSecondaryCursor(this.secondaryCursors.length-1)}for(o=0;o<n;o++)this.secondaryCursors[o].setState(this.context,e[o].modelState,e[o].viewState)},e.prototype.killSecondaryCursors=function(){this._setSecondaryStates([])},e.prototype._addSecondaryCursor=function(){this.secondaryCursors.push(new d(this.context)),this.lastAddedCursorIndex=this.secondaryCursors.length},e.prototype.getLastAddedCursorIndex=function(){return 0===this.secondaryCursors.length||0===this.lastAddedCursorIndex?0:this.lastAddedCursorIndex},e.prototype._removeSecondaryCursor=function(e){this.lastAddedCursorIndex>=e+1&&this.lastAddedCursorIndex--,this.secondaryCursors[e].dispose(this.context),this.secondaryCursors.splice(e,1)},e.prototype._getAll=function(){var e=[];e[0]=this.primaryCursor;for(var t=0,n=this.secondaryCursors.length;t<n;t++)e[t+1]=this.secondaryCursors[t];return e},e.prototype.normalize=function(){if(0!==this.secondaryCursors.length){for(var e=this._getAll(),t=[],n=0,i=e.length;n<i;n++)t.push({index:n,selection:e[n].modelState.selection});t.sort((function(e,t){return e.selection.startLineNumber===t.selection.startLineNumber?e.selection.startColumn-t.selection.startColumn:e.selection.startLineNumber-t.selection.startLineNumber}));for(var o=0;o<t.length-1;o++){var r=t[o],a=t[o+1],u=r.selection,d=a.selection;if(this.context.config.multiCursorMergeOverlapping){var c=void 0;if(c=d.isEmpty()||u.isEmpty()?d.getStartPosition().isBeforeOrEqual(u.getEndPosition()):d.getStartPosition().isBefore(u.getEndPosition()),c){var h=r.index<a.index?o:o+1,m=r.index<a.index?o+1:o,f=t[m].index,p=t[h].index,g=t[m].selection,w=t[h].selection;if(!g.equalsSelection(w)){var v=g.plusRange(w),b=g.selectionStartLineNumber===g.startLineNumber&&g.selectionStartColumn===g.startColumn,C=w.selectionStartLineNumber===w.startLineNumber&&w.selectionStartColumn===w.startColumn,y=void 0;f===this.lastAddedCursorIndex?(y=b,this.lastAddedCursorIndex=p):y=C;var S=void 0;S=y?new l["a"](v.startLineNumber,v.startColumn,v.endLineNumber,v.endColumn):new l["a"](v.endLineNumber,v.endColumn,v.startLineNumber,v.startColumn),t[h].selection=S;var _=s["d"].fromModelSelection(S);e[p].setState(this.context,_.modelState,_.viewState)}for(var L=0,N=t;L<N.length;L++){var k=N[L];k.index>f&&k.index--}e.splice(f,1),t.splice(m,1),this._removeSecondaryCursor(f-1),o--}}}}},e}(),h=n("b272"),m=n("191f"),f=n("8ae8"),p=n("0d83"),g=n("a666"),w=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}();function v(e){for(var t=0,n=e.length;t<n;t++)if(8===e[t].type)return!0;return!1}var b=function(){function e(e,t,n,i,o,r){this.selections=e,this.modelVersionId=t,this.oldSelections=n,this.oldModelVersionId=i,this.source=o,this.reason=r}return e}(),C=function(){function e(e,t){this.modelVersionId=e.getVersionId(),this.cursorState=t.getAll()}return e.prototype.equals=function(e){if(!e)return!1;if(this.modelVersionId!==e.modelVersionId)return!1;if(this.cursorState.length!==e.cursorState.length)return!1;for(var t=0,n=this.cursorState.length;t<n;t++)if(!this.cursorState[t].equals(e.cursorState[t]))return!1;return!0},e}(),y=function(){function e(e,t,n){this._model=e,this._autoClosedCharactersDecorations=t,this._autoClosedEnclosingDecorations=n}return e.getAllAutoClosedCharacters=function(e){for(var t=[],n=0,i=e;n<i.length;n++){var o=i[n];t=t.concat(o.getAutoClosedCharactersRanges())}return t},e.prototype.dispose=function(){this._autoClosedCharactersDecorations=this._model.deltaDecorations(this._autoClosedCharactersDecorations,[]),this._autoClosedEnclosingDecorations=this._model.deltaDecorations(this._autoClosedEnclosingDecorations,[])},e.prototype.getAutoClosedCharactersRanges=function(){for(var e=[],t=0;t<this._autoClosedCharactersDecorations.length;t++){var n=this._model.getDecorationRange(this._autoClosedCharactersDecorations[t]);n&&e.push(n)}return e},e.prototype.isValid=function(e){for(var t=[],n=0;n<this._autoClosedEnclosingDecorations.length;n++){var i=this._model.getDecorationRange(this._autoClosedEnclosingDecorations[n]);if(i&&(t.push(i),i.startLineNumber!==i.endLineNumber))return!1}t.sort(u["a"].compareRangesUsingStarts),e.sort(u["a"].compareRangesUsingStarts);for(n=0;n<e.length;n++){if(n>=t.length)return!1;if(!t[n].strictContainsRange(e[n]))return!1}return!0},e}(),S=function(e){function t(t,n,i){var r=e.call(this)||this;r._onDidReachMaxCursorCount=r._register(new o["a"]),r.onDidReachMaxCursorCount=r._onDidReachMaxCursorCount.event,r._onDidAttemptReadOnlyEdit=r._register(new o["a"]),r.onDidAttemptReadOnlyEdit=r._onDidAttemptReadOnlyEdit.event,r._onDidChange=r._register(new o["a"]),r.onDidChange=r._onDidChange.event,r._configuration=t,r._model=n,r._knownModelVersionId=r._model.getVersionId(),r._viewModel=i,r.context=new s["c"](r._configuration,r._model,r._viewModel),r._cursors=new c(r.context),r._isHandling=!1,r._isDoingComposition=!1,r._selectionsWhenCompositionStarted=null,r._columnSelectData=null,r._autoClosedActions=[],r._prevEditOperationType=0,r._register(r._model.onDidChangeRawContent((function(e){if(r._knownModelVersionId=e.versionId,!r._isHandling){var t=e.containsEvent(1);r._onModelContentChanged(t)}}))),r._register(i.addEventListener((function(e){v(e)&&r._knownModelVersionId===r._model.getVersionId()&&r.setStates("viewModel",0,r.getAll())})));var a=function(){r.context=new s["c"](r._configuration,r._model,r._viewModel),r._cursors.updateContext(r.context)};return r._register(r._model.onDidChangeLanguage((function(e){a()}))),r._register(r._model.onDidChangeLanguageConfiguration((function(){a()}))),r._register(r._model.onDidChangeOptions((function(){a()}))),r._register(r._configuration.onDidChange((function(e){s["b"].shouldRecreate(e)&&a()}))),r}return w(t,e),t.prototype.dispose=function(){this._cursors.dispose(),this._autoClosedActions=Object(g["f"])(this._autoClosedActions),e.prototype.dispose.call(this)},t.prototype._validateAutoClosedActions=function(){if(this._autoClosedActions.length>0)for(var e=this._cursors.getSelections(),t=0;t<this._autoClosedActions.length;t++){var n=this._autoClosedActions[t];n.isValid(e)||(n.dispose(),this._autoClosedActions.splice(t,1),t--)}},t.prototype.getPrimaryCursor=function(){return this._cursors.getPrimaryCursor()},t.prototype.getLastAddedCursorIndex=function(){return this._cursors.getLastAddedCursorIndex()},t.prototype.getAll=function(){return this._cursors.getAll()},t.prototype.setStates=function(e,n,i){null!==i&&i.length>t.MAX_CURSOR_COUNT&&(i=i.slice(0,t.MAX_CURSOR_COUNT),this._onDidReachMaxCursorCount.fire(void 0));var o=new C(this._model,this);this._cursors.setStates(i),this._cursors.normalize(),this._columnSelectData=null,this._validateAutoClosedActions(),this._emitStateChangedIfNecessary(e,n,o)},t.prototype.setColumnSelectData=function(e){this._columnSelectData=e},t.prototype.reveal=function(e,t,n,i){this._revealRange(e,n,0,t,i)},t.prototype.revealRange=function(e,t,n,i,o){this.emitCursorRevealRange(e,n,i,t,o)},t.prototype.scrollTo=function(e){this._viewModel.viewLayout.setScrollPositionSmooth({scrollTop:e})},t.prototype.saveState=function(){for(var e=[],t=this._cursors.getSelections(),n=0,i=t.length;n<i;n++){var o=t[n];e.push({inSelectionMode:!o.isEmpty(),selectionStart:{lineNumber:o.selectionStartLineNumber,column:o.selectionStartColumn},position:{lineNumber:o.positionLineNumber,column:o.positionColumn}})}return e},t.prototype.restoreState=function(e){for(var t=[],n=0,i=e.length;n<i;n++){var o=e[n],r=1,a=1;o.position&&o.position.lineNumber&&(r=o.position.lineNumber),o.position&&o.position.column&&(a=o.position.column);var u=r,l=a;o.selectionStart&&o.selectionStart.lineNumber&&(u=o.selectionStart.lineNumber),o.selectionStart&&o.selectionStart.column&&(l=o.selectionStart.column),t.push({selectionStartLineNumber:u,selectionStartColumn:l,positionLineNumber:r,positionColumn:a})}this.setStates("restoreState",0,s["d"].fromModelSelections(t)),this.reveal("restoreState",!0,0,1)},t.prototype._onModelContentChanged=function(e){if(this._prevEditOperationType=0,e)this._cursors.dispose(),this._cursors=new c(this.context),this._validateAutoClosedActions(),this._emitStateChangedIfNecessary("model",1,null);else{var t=this._cursors.readSelectionFromMarkers();this.setStates("modelChange",2,s["d"].fromModelSelections(t))}},t.prototype.getSelection=function(){return this._cursors.getPrimaryCursor().modelState.selection},t.prototype.getColumnSelectData=function(){if(this._columnSelectData)return this._columnSelectData;var e=this._cursors.getPrimaryCursor(),t=e.viewState.selectionStart.getStartPosition(),n=t.lineNumber,i=s["a"].visibleColumnFromColumn2(this.context.config,this.context.viewModel,t);return{isReal:!1,fromViewLineNumber:n,fromViewVisualColumn:i,toViewLineNumber:n,toViewVisualColumn:i}},t.prototype.getSelections=function(){return this._cursors.getSelections()},t.prototype.getViewSelections=function(){return this._cursors.getViewSelections()},t.prototype.getPosition=function(){return this._cursors.getPrimaryCursor().modelState.position},t.prototype.setSelections=function(e,t){this.setStates(e,0,s["d"].fromModelSelections(t))},t.prototype.getPrevEditOperationType=function(){return this._prevEditOperationType},t.prototype.setPrevEditOperationType=function(e){this._prevEditOperationType=e},t.prototype._pushAutoClosedAction=function(e,t){for(var n=[],i=[],o=0,r=e.length;o<r;o++)n.push({range:e[o],options:{inlineClassName:"auto-closed-character",stickiness:1}}),i.push({range:t[o],options:{stickiness:1}});var s=this._model.deltaDecorations([],n),a=this._model.deltaDecorations([],i);this._autoClosedActions.push(new y(this._model,s,a))},t.prototype._executeEditOperation=function(e){if(e){e.shouldPushStackElementBefore&&this._model.pushStackElement();var t=_.executeCommands(this._model,this._cursors.getSelections(),e.commands);if(t){this._interpretCommandResult(t);for(var n=[],i=[],o=0;o<e.commands.length;o++){var r=e.commands[o];r instanceof m["b"]&&r.enclosingRange&&r.closeCharacterRange&&(n.push(r.closeCharacterRange),i.push(r.enclosingRange))}n.length>0&&this._pushAutoClosedAction(n,i),this._prevEditOperationType=e.type}e.shouldPushStackElementAfter&&this._model.pushStackElement()}},t.prototype._interpretCommandResult=function(e){e&&0!==e.length||(e=this._cursors.readSelectionFromMarkers()),this._columnSelectData=null,this._cursors.setSelections(e),this._cursors.normalize()},t.prototype._emitStateChangedIfNecessary=function(e,t,n){var i=new C(this._model,this);if(i.equals(n))return!1;var o=this._cursors.getSelections(),r=this._cursors.getViewSelections();try{var s=this._beginEmit();s.emit(new p["c"](r,o))}finally{this._endEmit()}if(!n||n.cursorState.length!==i.cursorState.length||i.cursorState.some((function(e,t){return!e.modelState.equals(n.cursorState[t].modelState)}))){var a=n?n.cursorState.map((function(e){return e.modelState.selection})):null,u=n?n.modelVersionId:0;this._onDidChange.fire(new b(o,i.modelVersionId,a,u,e||"keyboard",t))}return!0},t.prototype._revealRange=function(e,t,n,i,o){var r=this._cursors.getViewPositions(),s=r[0];if(1===t)for(var a=1;a<r.length;a++)r[a].isBefore(s)&&(s=r[a]);else if(2===t)for(a=1;a<r.length;a++)s.isBeforeOrEqual(r[a])&&(s=r[a]);else if(r.length>1)return;var l=new u["a"](s.lineNumber,s.column,s.lineNumber,s.column);this.emitCursorRevealRange(e,l,n,i,o)},t.prototype.emitCursorRevealRange=function(e,t,n,i,o){try{var r=this._beginEmit();r.emit(new p["m"](e,t,n,i,o))}finally{this._endEmit()}},t.prototype._findAutoClosingPairs=function(e){if(!e.length)return null;for(var t=[],n=0,i=e.length;n<i;n++){var o=e[n];if(!o.text||o.text.indexOf("\n")>=0)return null;var r=o.text.match(/([)\]}>'"`])([^)\]}>'"`]*)$/);if(!r)return null;var s=r[1],a=this.context.config.autoClosingPairsClose2.get(s);if(!a||1!==a.length)return null;var u=a[0].open,l=o.text.length-r[2].length-1,d=o.text.lastIndexOf(u,l-1);if(-1===d)return null;t.push([d,l])}return t},t.prototype.executeEdits=function(e,t,n){var i=this,o=null;"snippet"===e&&(o=this._findAutoClosingPairs(t)),o&&(t[0]._isTracked=!0);var r=[],s=[],a=this._model.pushEditOperations(this.getSelections(),t,(function(e){if(o)for(var t=0,a=o.length;t<a;t++){var l=o[t],d=l[0],c=l[1],h=e[t],m=h.range.startLineNumber,f=h.range.startColumn-1+d,p=h.range.startColumn-1+c;r.push(new u["a"](m,p+1,m,p+2)),s.push(new u["a"](m,f+1,m,p+2))}var g=n(e);return g&&(i._isHandling=!0),g}));a&&(this._isHandling=!1,this.setSelections(e,a)),r.length>0&&this._pushAutoClosedAction(r,s)},t.prototype.trigger=function(e,t,n){var o=f["b"];if(t===o.CompositionStart)return this._isDoingComposition=!0,void(this._selectionsWhenCompositionStarted=this.getSelections().slice(0));if(t===o.CompositionEnd&&(this._isDoingComposition=!1),this._configuration.options.get(68))this._onDidAttemptReadOnlyEdit.fire(void 0);else{var r=new C(this._model,this),s=0;t!==o.Undo&&t!==o.Redo&&this._cursors.stopTrackingSelections(),this._cursors.ensureValidState(),this._isHandling=!0;try{switch(t){case o.Type:this._type(e,n.text);break;case o.ReplacePreviousChar:this._replacePreviousChar(n.text,n.replaceCharCnt);break;case o.Paste:s=4,this._paste(n.text,n.pasteOnNewLine,n.multicursorText||[]);break;case o.Cut:this._cut();break;case o.Undo:s=5,this._interpretCommandResult(this._model.undo());break;case o.Redo:s=6,this._interpretCommandResult(this._model.redo());break;case o.ExecuteCommand:this._externalExecuteCommand(n);break;case o.ExecuteCommands:this._externalExecuteCommands(n);break;case o.CompositionEnd:this._interpretCompositionEnd(e);break}}catch(a){Object(i["e"])(a)}this._isHandling=!1,t!==o.Undo&&t!==o.Redo&&this._cursors.startTrackingSelections(),this._validateAutoClosedActions(),this._emitStateChangedIfNecessary(e,s,r)&&this._revealRange(e,0,0,!0,0)}},t.prototype._interpretCompositionEnd=function(e){if(!this._isDoingComposition&&"keyboard"===e){var t=y.getAllAutoClosedCharacters(this._autoClosedActions);this._executeEditOperation(m["a"].compositionEndWithInterceptors(this._prevEditOperationType,this.context.config,this.context.model,this._selectionsWhenCompositionStarted,this.getSelections(),t)),this._selectionsWhenCompositionStarted=null}},t.prototype._type=function(e,t){if(this._isDoingComposition||"keyboard"!==e)this._executeEditOperation(m["a"].typeWithoutInterceptors(this._prevEditOperationType,this.context.config,this.context.model,this.getSelections(),t));else{var n=t.length,i=0;while(i<n){var o=r["E"](t,i),s=t.substr(i,o),a=y.getAllAutoClosedCharacters(this._autoClosedActions);this._executeEditOperation(m["a"].typeWithInterceptors(this._prevEditOperationType,this.context.config,this.context.model,this.getSelections(),a,s)),i+=o}}},t.prototype._replacePreviousChar=function(e,t){this._executeEditOperation(m["a"].replacePreviousChar(this._prevEditOperationType,this.context.config,this.context.model,this.getSelections(),e,t))},t.prototype._paste=function(e,t,n){this._executeEditOperation(m["a"].paste(this.context.config,this.context.model,this.getSelections(),e,t,n))},t.prototype._cut=function(){this._executeEditOperation(h["a"].cut(this.context.config,this.context.model,this.getSelections()))},t.prototype._externalExecuteCommand=function(e){this._cursors.killSecondaryCursors(),this._executeEditOperation(new s["e"](0,[e],{shouldPushStackElementBefore:!1,shouldPushStackElementAfter:!1}))},t.prototype._externalExecuteCommands=function(e){this._executeEditOperation(new s["e"](0,e,{shouldPushStackElementBefore:!1,shouldPushStackElementAfter:!1}))},t.MAX_CURSOR_COUNT=1e4,t}(p["e"]),_=function(){function e(){}return e.executeCommands=function(e,t,n){for(var i={model:e,selectionsBefore:t,trackedRanges:[],trackedRangesDirection:[]},o=this._innerExecuteCommands(i,n),r=0,s=i.trackedRanges.length;r<s;r++)i.model._setTrackedRange(i.trackedRanges[r],null,0);return o},e._innerExecuteCommands=function(e,t){if(this._arrayIsEmpty(t))return null;var n=this._getEditOperations(e,t);if(0===n.operations.length)return null;var i=n.operations,o=this._getLoserCursorMap(i);if(o.hasOwnProperty("0"))return console.warn("Ignoring commands"),null;for(var r=[],s=0,a=i.length;s<a;s++)o.hasOwnProperty(i[s].identifier.major.toString())||r.push(i[s]);n.hadTrackedEditOperation&&r.length>0&&(r[0]._isTracked=!0);var u=e.model.pushEditOperations(e.selectionsBefore,r,(function(n){for(var i=[],o=0;o<e.selectionsBefore.length;o++)i[o]=[];for(var r=0,s=n;r<s.length;r++){var a=s[r];a.identifier&&i[a.identifier.major].push(a)}var u=function(e,t){return e.identifier.minor-t.identifier.minor},d=[],c=function(n){i[n].length>0?(i[n].sort(u),d[n]=t[n].computeCursorState(e.model,{getInverseEditOperations:function(){return i[n]},getTrackedSelection:function(t){var n=parseInt(t,10),i=e.model._getTrackedRange(e.trackedRanges[n]);return 0===e.trackedRangesDirection[n]?new l["a"](i.startLineNumber,i.startColumn,i.endLineNumber,i.endColumn):new l["a"](i.endLineNumber,i.endColumn,i.startLineNumber,i.startColumn)}})):d[n]=e.selectionsBefore[n]};for(o=0;o<e.selectionsBefore.length;o++)c(o);return d}));u||(u=e.selectionsBefore);var d=[];for(var c in o)o.hasOwnProperty(c)&&d.push(parseInt(c,10));d.sort((function(e,t){return t-e}));for(var h=0,m=d;h<m.length;h++){var f=m[h];u.splice(f,1)}return u},e._arrayIsEmpty=function(e){for(var t=0,n=e.length;t<n;t++)if(e[t])return!1;return!0},e._getEditOperations=function(e,t){for(var n=[],i=!1,o=0,r=t.length;o<r;o++){var s=t[o];if(s){var a=this._getEditOperationsFromCommand(e,o,s);n=n.concat(a.operations),i=i||a.hadTrackedEditOperation}}return{operations:n,hadTrackedEditOperation:i}},e._getEditOperationsFromCommand=function(e,t,n){var o=[],r=0,s=function(e,i,s){void 0===s&&(s=!1),e.isEmpty()&&""===i||o.push({identifier:{major:t,minor:r++},range:e,text:i,forceMoveMarkers:s,isAutoWhitespaceEdit:n.insertsAutoWhitespace})},a=!1,u=function(e,t,n){a=!0,s(e,t,n)},l=function(t,n){var i;if(t.isEmpty())if("boolean"===typeof n)i=n?2:3;else{var o=e.model.getLineMaxColumn(t.startLineNumber);i=t.startColumn===o?2:3}else i=1;var r=e.trackedRanges.length,s=e.model._setTrackedRange(null,t,i);return e.trackedRanges[r]=s,e.trackedRangesDirection[r]=t.getDirection(),r.toString()},d={addEditOperation:s,addTrackedEditOperation:u,trackSelection:l};try{n.getEditOperations(e.model,d)}catch(c){return Object(i["e"])(c),{operations:[],hadTrackedEditOperation:!1}}return{operations:o,hadTrackedEditOperation:a}},e._getLoserCursorMap=function(e){e=e.slice(0),e.sort((function(e,t){return-u["a"].compareRangesUsingEnds(e.range,t.range)}));for(var t={},n=1;n<e.length;n++){var i=e[n-1],o=e[n];if(i.range.getStartPosition().isBefore(o.range.getEndPosition())){var r=void 0;r=i.identifier.major>o.identifier.major?i.identifier.major:o.identifier.major,t[r.toString()]=!0;for(var s=0;s<e.length;s++)e[s].identifier.major===r&&(e.splice(s,1),s<n&&n--,s--);n>0&&n--}}return t},e}()},"191f":function(e,t,n){"use strict";n.d(t,"a",(function(){return p})),n.d(t,"b",(function(){return g}));var i=n("fdcc"),o=n("3742"),r=n("2c29"),s=n("ccde"),a=n("6a89"),u=n("8025"),l=function(){function e(e,t,n){this._range=e,this._charBeforeSelection=t,this._charAfterSelection=n}return e.prototype.getEditOperations=function(e,t){t.addTrackedEditOperation(new a["a"](this._range.startLineNumber,this._range.startColumn,this._range.startLineNumber,this._range.startColumn),this._charBeforeSelection),t.addTrackedEditOperation(new a["a"](this._range.endLineNumber,this._range.endColumn,this._range.endLineNumber,this._range.endColumn),this._charAfterSelection)},e.prototype.computeCursorState=function(e,t){var n=t.getInverseEditOperations(),i=n[0].range,o=n[1].range;return new u["a"](i.endLineNumber,i.endColumn,o.endLineNumber,o.endColumn-this._charAfterSelection.length)},e}(),d=n("2e5d"),c=n("e6ff"),h=n("2837"),m=n("70cb"),f=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),p=function(){function e(){}return e.indent=function(e,t,n){if(null===t||null===n)return[];for(var i=[],o=0,r=n.length;o<r;o++)i[o]=new s["a"](n[o],{isUnshift:!1,tabSize:e.tabSize,indentSize:e.indentSize,insertSpaces:e.insertSpaces,useTabStops:e.useTabStops,autoIndent:e.autoIndent});return i},e.outdent=function(e,t,n){for(var i=[],o=0,r=n.length;o<r;o++)i[o]=new s["a"](n[o],{isUnshift:!0,tabSize:e.tabSize,indentSize:e.indentSize,insertSpaces:e.insertSpaces,useTabStops:e.useTabStops,autoIndent:e.autoIndent});return i},e.shiftIndent=function(e,t,n){return n=n||1,s["a"].shiftIndent(t,t.length+n,e.tabSize,e.indentSize,e.insertSpaces)},e.unshiftIndent=function(e,t,n){return n=n||1,s["a"].unshiftIndent(t,t.length+n,e.tabSize,e.indentSize,e.insertSpaces)},e._distributedPaste=function(e,t,n,i){for(var o=[],s=0,a=n.length;s<a;s++)o[s]=new r["a"](n[s],i[s]);return new d["e"](0,o,{shouldPushStackElementBefore:!0,shouldPushStackElementAfter:!0})},e._simplePaste=function(e,t,n,i,o){for(var s=[],u=0,l=n.length;u<l;u++){var c=n[u],h=c.getPosition();if(o&&!c.isEmpty()&&(o=!1),o&&i.indexOf("\n")!==i.length-1&&(o=!1),o){var m=new a["a"](h.lineNumber,1,h.lineNumber,1);s[u]=new r["b"](m,i,c,!0)}else s[u]=new r["a"](c,i)}return new d["e"](0,s,{shouldPushStackElementBefore:!0,shouldPushStackElementAfter:!0})},e._distributePasteToCursors=function(e,t,n,i,o){if(i)return null;if(1===t.length)return null;if(o&&o.length===t.length)return o;if("spread"===e.multiCursorPaste){10===n.charCodeAt(n.length-1)&&(n=n.substr(0,n.length-1)),13===n.charCodeAt(n.length-1)&&(n=n.substr(0,n.length-1));var r=n.split(/\r\n|\r|\n/);if(r.length===t.length)return r}return null},e.paste=function(e,t,n,i,o,r){var s=this._distributePasteToCursors(e,n,i,o,r);return s?(n=n.sort(a["a"].compareRangesUsingStarts),this._distributedPaste(e,t,n,s)):this._simplePaste(e,t,n,i,o)},e._goodIndentForLine=function(t,n,i){var r=null,s="",u=m["a"].getInheritIndentForLine(t.autoIndent,n,i,!1);if(u)r=u.action,s=u.indentation;else if(i>1){var l=void 0;for(l=i-1;l>=1;l--){var d=n.getLineContent(l),c=o["D"](d);if(c>=0)break}if(l<1)return null;var f=n.getLineMaxColumn(l),p=m["a"].getEnterAction(t.autoIndent,n,new a["a"](l,f,l,f));p&&(s=p.indentation+p.appendText)}return r&&(r===h["a"].Indent&&(s=e.shiftIndent(t,s)),r===h["a"].Outdent&&(s=e.unshiftIndent(t,s)),s=t.normalizeIndentation(s)),s||null},e._replaceJumpToNextIndent=function(e,t,n,i){var o="",s=n.getStartPosition();if(e.insertSpaces)for(var a=d["a"].visibleColumnFromColumn2(e,t,s),u=e.indentSize,l=u-a%u,c=0;c<l;c++)o+=" ";else o="\t";return new r["a"](n,o,i)},e.tab=function(e,t,n){for(var i=[],u=0,l=n.length;u<l;u++){var d=n[u];if(d.isEmpty()){var c=t.getLineContent(d.startLineNumber);if(/^\s*$/.test(c)&&t.isCheapToTokenize(d.startLineNumber)){var h=this._goodIndentForLine(e,t,d.startLineNumber);h=h||"\t";var m=e.normalizeIndentation(h);if(!o["N"](c,m)){i[u]=new r["a"](new a["a"](d.startLineNumber,1,d.startLineNumber,c.length+1),m,!0);continue}}i[u]=this._replaceJumpToNextIndent(e,t,d,!0)}else{if(d.startLineNumber===d.endLineNumber){var f=t.getLineMaxColumn(d.startLineNumber);if(1!==d.startColumn||d.endColumn!==f){i[u]=this._replaceJumpToNextIndent(e,t,d,!1);continue}}i[u]=new s["a"](d,{isUnshift:!1,tabSize:e.tabSize,indentSize:e.indentSize,insertSpaces:e.insertSpaces,useTabStops:e.useTabStops,autoIndent:e.autoIndent})}}return i},e.replacePreviousChar=function(e,t,n,i,o,s){for(var u=[],l=0,c=i.length;l<c;l++){var h=i[l];if(h.isEmpty()){var m=h.getPosition(),f=Math.max(1,m.column-s),p=new a["a"](m.lineNumber,f,m.lineNumber,m.column);u[l]=new r["a"](p,o)}else u[l]=null}return new d["e"](1,u,{shouldPushStackElementBefore:1!==e,shouldPushStackElementAfter:!1})},e._typeCommand=function(e,t,n){return n?new r["e"](e,t,!0):new r["a"](e,t,!0)},e._enter=function(t,n,i,s){if(0===t.autoIndent)return e._typeCommand(s,"\n",i);if(!n.isCheapToTokenize(s.getStartPosition().lineNumber)||1===t.autoIndent){var u=n.getLineContent(s.startLineNumber),l=o["t"](u).substring(0,s.startColumn-1);return e._typeCommand(s,"\n"+t.normalizeIndentation(l),i)}var c=m["a"].getEnterAction(t.autoIndent,n,s);if(c){if(c.indentAction===h["a"].None)return e._typeCommand(s,"\n"+t.normalizeIndentation(c.indentation+c.appendText),i);if(c.indentAction===h["a"].Indent)return e._typeCommand(s,"\n"+t.normalizeIndentation(c.indentation+c.appendText),i);if(c.indentAction===h["a"].IndentOutdent){var f=t.normalizeIndentation(c.indentation),p=t.normalizeIndentation(c.indentation+c.appendText),g="\n"+p+"\n"+f;return i?new r["e"](s,g,!0):new r["d"](s,g,-1,p.length-f.length,!0)}if(c.indentAction===h["a"].Outdent){var w=e.unshiftIndent(t,c.indentation);return e._typeCommand(s,"\n"+t.normalizeIndentation(w+c.appendText),i)}}var v=n.getLineContent(s.startLineNumber),b=o["t"](v).substring(0,s.startColumn-1);if(t.autoIndent>=4){var C=m["a"].getIndentForEnter(t.autoIndent,n,s,{unshiftIndent:function(n){return e.unshiftIndent(t,n)},shiftIndent:function(n){return e.shiftIndent(t,n)},normalizeIndentation:function(e){return t.normalizeIndentation(e)}});if(C){var y=d["a"].visibleColumnFromColumn2(t,n,s.getEndPosition()),S=s.endColumn,_="\n";b!==t.normalizeIndentation(C.beforeEnter)&&(_=t.normalizeIndentation(C.beforeEnter)+v.substring(b.length,s.startColumn-1)+"\n",s=new a["a"](s.startLineNumber,1,s.endLineNumber,s.endColumn));var L=n.getLineContent(s.endLineNumber),N=o["q"](L);if(s=N>=0?s.setEndPosition(s.endLineNumber,Math.max(s.endColumn,N+1)):s.setEndPosition(s.endLineNumber,n.getLineMaxColumn(s.endLineNumber)),i)return new r["e"](s,_+t.normalizeIndentation(C.afterEnter),!0);var k=0;return S<=N+1&&(t.insertSpaces||(y=Math.ceil(y/t.indentSize)),k=Math.min(y+1-t.normalizeIndentation(C.afterEnter).length-1,0)),new r["d"](s,_+t.normalizeIndentation(C.afterEnter),0,k,!0)}}return e._typeCommand(s,"\n"+t.normalizeIndentation(b),i)},e._isAutoIndentType=function(e,t,n){if(e.autoIndent<4)return!1;for(var i=0,o=n.length;i<o;i++)if(!t.isCheapToTokenize(n[i].getEndPosition().lineNumber))return!1;return!0},e._runAutoIndentType=function(t,n,i,o){var r=m["a"].getIndentationAtPosition(n,i.startLineNumber,i.startColumn),s=m["a"].getIndentActionForType(t.autoIndent,n,i,o,{shiftIndent:function(n){return e.shiftIndent(t,n)},unshiftIndent:function(n){return e.unshiftIndent(t,n)}});if(null===s)return null;if(s!==t.normalizeIndentation(r)){var u=n.getLineFirstNonWhitespaceColumn(i.startLineNumber);return 0===u?e._typeCommand(new a["a"](i.startLineNumber,0,i.endLineNumber,i.endColumn),t.normalizeIndentation(s)+o,!1):e._typeCommand(new a["a"](i.startLineNumber,0,i.endLineNumber,i.endColumn),t.normalizeIndentation(s)+n.getLineContent(i.startLineNumber).substring(u-1,i.startColumn-1)+o,!1)}return null},e._isAutoClosingOvertype=function(e,t,n,i,o){if("never"===e.autoClosingOvertype)return!1;if(!e.autoClosingPairsClose2.has(o))return!1;for(var r=0,s=n.length;r<s;r++){var a=n[r];if(!a.isEmpty())return!1;var u=a.getPosition(),l=t.getLineContent(u.lineNumber),c=l.charAt(u.column-1);if(c!==o)return!1;var h=Object(d["g"])(o),m=u.column>2?l.charCodeAt(u.column-2):0;if(92===m&&h)return!1;if("auto"===e.autoClosingOvertype){for(var f=!1,p=0,g=i.length;p<g;p++){var w=i[p];if(u.lineNumber===w.startLineNumber&&u.column===w.startColumn){f=!0;break}}if(!f)return!1}}return!0},e._runAutoClosingOvertype=function(e,t,n,i,o){for(var s=[],u=0,l=i.length;u<l;u++){var c=i[u],h=c.getPosition(),m=new a["a"](h.lineNumber,h.column,h.lineNumber,h.column+1);s[u]=new r["a"](m,o)}return new d["e"](1,s,{shouldPushStackElementBefore:1!==e,shouldPushStackElementAfter:!1})},e._autoClosingPairIsSymmetric=function(e){var t=e.open,n=e.close;return t.indexOf(n)>=0||n.indexOf(t)>=0},e._isBeforeClosingBrace=function(t,n,i){var o=t.autoClosingPairsClose2.get(i);if(!o)return!1;for(var r=e._autoClosingPairIsSymmetric(n),s=0,a=o;s<a.length;s++){var u=a[s],l=e._autoClosingPairIsSymmetric(u);if(r||!l)return!0}return!1},e._findAutoClosingPairOpen=function(e,t,n,i){var o=e.autoClosingPairsOpen2.get(i);if(!o)return null;for(var r=null,s=0,u=o;s<u.length;s++){var l=u[s];if(null===r||l.open.length>r.open.length){for(var d=!0,c=0,h=n;c<h.length;c++){var m=h[c],f=t.getValueInRange(new a["a"](m.lineNumber,m.column-l.open.length+1,m.lineNumber,m.column));if(f+i!==l.open){d=!1;break}}d&&(r=l)}}return r},e._isAutoClosingOpenCharType=function(t,n,o,r,s){var a=Object(d["g"])(r),u=a?t.autoClosingQuotes:t.autoClosingBrackets;if("never"===u)return null;var l=this._findAutoClosingPairOpen(t,n,o.map((function(e){return e.getPosition()})),r);if(!l)return null;for(var h=a?t.shouldAutoCloseBefore.quote:t.shouldAutoCloseBefore.bracket,f=0,p=o.length;f<p;f++){var g=o[f];if(!g.isEmpty())return null;var w=g.getPosition(),v=n.getLineContent(w.lineNumber);if(v.length>w.column-1){var b=v.charAt(w.column-1),C=e._isBeforeClosingBrace(t,l,b);if(!C&&!h(b))return null}if(!n.isCheapToTokenize(w.lineNumber))return null;if(1===l.open.length&&a&&"always"!==u){var y=Object(c["a"])(t.wordSeparators);if(s&&w.column>1&&0===y.get(v.charCodeAt(w.column-2)))return null;if(!s&&w.column>2&&0===y.get(v.charCodeAt(w.column-3)))return null}n.forceTokenization(w.lineNumber);var S=n.getLineTokens(w.lineNumber),_=!1;try{_=m["a"].shouldAutoClosePair(l,S,s?w.column:w.column-1)}catch(L){Object(i["e"])(L)}if(!_)return null}return l},e._runAutoClosingOpenCharType=function(e,t,n,i,o,r,s){for(var a=[],u=0,l=i.length;u<l;u++){var c=i[u];a[u]=new g(c,o,r,s.close)}return new d["e"](1,a,{shouldPushStackElementBefore:!0,shouldPushStackElementAfter:!1})},e._shouldSurroundChar=function(e,t){return Object(d["g"])(t)?"quotes"===e.autoSurround||"languageDefined"===e.autoSurround:"brackets"===e.autoSurround||"languageDefined"===e.autoSurround},e._isSurroundSelectionType=function(t,n,i,o){if(!e._shouldSurroundChar(t,o)||!t.surroundingPairs.hasOwnProperty(o))return!1;for(var r=Object(d["g"])(o),s=0,a=i.length;s<a;s++){var u=i[s];if(u.isEmpty())return!1;for(var l=!0,c=u.startLineNumber;c<=u.endLineNumber;c++){var h=n.getLineContent(c),m=c===u.startLineNumber?u.startColumn-1:0,f=c===u.endLineNumber?u.endColumn-1:h.length,p=h.substring(m,f);if(/[^ \t]/.test(p)){l=!1;break}}if(l)return!1;if(r&&u.startLineNumber===u.endLineNumber&&u.startColumn+1===u.endColumn){var g=n.getValueInRange(u);if(Object(d["g"])(g))return!1}}return!0},e._runSurroundSelectionType=function(e,t,n,i,o){for(var r=[],s=0,a=i.length;s<a;s++){var u=i[s],c=t.surroundingPairs[o];r[s]=new l(u,o,c)}return new d["e"](0,r,{shouldPushStackElementBefore:!0,shouldPushStackElementAfter:!0})},e._isTypeInterceptorElectricChar=function(e,t,n){return!(1!==n.length||!t.isCheapToTokenize(n[0].getEndPosition().lineNumber))},e._typeInterceptorElectricChar=function(e,t,n,s,u){if(!t.electricChars.hasOwnProperty(u)||!s.isEmpty())return null;var l=s.getPosition();n.forceTokenization(l.lineNumber);var c,h=n.getLineTokens(l.lineNumber);try{c=m["a"].onElectricCharacter(u,h,l.column)}catch(N){return Object(i["e"])(N),null}if(!c)return null;if(c.matchOpenBracket){var f=(h.getLineContent()+u).lastIndexOf(c.matchOpenBracket)+1,p=n.findMatchingBracketUp(c.matchOpenBracket,{lineNumber:l.lineNumber,column:f});if(p){if(p.startLineNumber===l.lineNumber)return null;var g=n.getLineContent(p.startLineNumber),w=o["t"](g),v=t.normalizeIndentation(w),b=n.getLineContent(l.lineNumber),C=n.getLineFirstNonWhitespaceColumn(l.lineNumber)||l.column,y=b.substring(C-1,l.column-1),S=v+y+u,_=new a["a"](l.lineNumber,1,l.lineNumber,l.column),L=new r["a"](_,S);return new d["e"](1,[L],{shouldPushStackElementBefore:!1,shouldPushStackElementAfter:!0})}}return null},e.compositionEndWithInterceptors=function(e,t,n,i,o,s){if(!i||u["a"].selectionsArrEqual(i,o))return null;for(var l=null,c=0,h=o;c<h.length;c++){var m=h[c];if(!m.isEmpty())return null;var f=m.getPosition(),p=n.getValueInRange(new a["a"](f.lineNumber,f.column-1,f.lineNumber,f.column));if(null===l)l=p;else if(l!==p)return null}if(!l)return null;if(this._isAutoClosingOvertype(t,n,o,s,l)){var g=o.map((function(e){return new r["a"](new a["a"](e.positionLineNumber,e.positionColumn,e.positionLineNumber,e.positionColumn+1),"",!1)}));return new d["e"](1,g,{shouldPushStackElementBefore:!0,shouldPushStackElementAfter:!1})}var w=this._isAutoClosingOpenCharType(t,n,o,l,!1);return w?this._runAutoClosingOpenCharType(e,t,n,o,l,!1,w):null},e.typeWithInterceptors=function(t,n,i,o,s,a){if("\n"===a){for(var u=[],l=0,c=o.length;l<c;l++)u[l]=e._enter(n,i,!1,o[l]);return new d["e"](1,u,{shouldPushStackElementBefore:!0,shouldPushStackElementAfter:!1})}if(this._isAutoIndentType(n,i,o)){var h=[],m=!1;for(l=0,c=o.length;l<c;l++)if(h[l]=this._runAutoIndentType(n,i,o[l],a),!h[l]){m=!0;break}if(!m)return new d["e"](1,h,{shouldPushStackElementBefore:!0,shouldPushStackElementAfter:!1})}if(this._isAutoClosingOvertype(n,i,o,s,a))return this._runAutoClosingOvertype(t,n,i,o,a);var f=this._isAutoClosingOpenCharType(n,i,o,a,!0);if(f)return this._runAutoClosingOpenCharType(t,n,i,o,a,!0,f);if(this._isSurroundSelectionType(n,i,o,a))return this._runSurroundSelectionType(t,n,i,o,a);if(this._isTypeInterceptorElectricChar(n,i,o)){var p=this._typeInterceptorElectricChar(t,n,i,o[0],a);if(p)return p}var g=[];for(l=0,c=o.length;l<c;l++)g[l]=new r["a"](o[l],a);var w=1!==t;return" "===a&&(w=!0),new d["e"](1,g,{shouldPushStackElementBefore:w,shouldPushStackElementAfter:!1})},e.typeWithoutInterceptors=function(e,t,n,i,o){for(var s=[],a=0,u=i.length;a<u;a++)s[a]=new r["a"](i[a],o);return new d["e"](1,s,{shouldPushStackElementBefore:1!==e,shouldPushStackElementAfter:!1})},e.lineInsertBefore=function(e,t,n){if(null===t||null===n)return[];for(var i=[],o=0,s=n.length;o<s;o++){var u=n[o].positionLineNumber;if(1===u)i[o]=new r["e"](new a["a"](1,1,1,1),"\n");else{u--;var l=t.getLineMaxColumn(u);i[o]=this._enter(e,t,!1,new a["a"](u,l,u,l))}}return i},e.lineInsertAfter=function(e,t,n){if(null===t||null===n)return[];for(var i=[],o=0,r=n.length;o<r;o++){var s=n[o].positionLineNumber,u=t.getLineMaxColumn(s);i[o]=this._enter(e,t,!1,new a["a"](s,u,s,u))}return i},e.lineBreakInsert=function(e,t,n){for(var i=[],o=0,r=n.length;o<r;o++)i[o]=this._enter(e,t,!0,n[o]);return i},e}(),g=function(e){function t(t,n,i,o){var r=e.call(this,t,(i?n:"")+o,0,-o.length)||this;return r._openCharacter=n,r._closeCharacter=o,r.closeCharacterRange=null,r.enclosingRange=null,r}return f(t,e),t.prototype.computeCursorState=function(t,n){var i=n.getInverseEditOperations(),o=i[0].range;return this.closeCharacterRange=new a["a"](o.startLineNumber,o.endColumn-this._closeCharacter.length,o.endLineNumber,o.endColumn),this.enclosingRange=new a["a"](o.startLineNumber,o.endColumn-this._openCharacter.length-this._closeCharacter.length,o.endLineNumber,o.endColumn),e.prototype.computeCursorState.call(this,t,n)},t}(r["d"])},"22e9":function(e,t,n){"use strict";n.d(t,"a",(function(){return h}));var i=n("1b0e"),o=n("3742"),r=3;function s(e,t,n,o){var r=new i["a"](e,t,n);return r.ComputeDiff(o)}var a=function(){function e(e){for(var t=[],n=[],i=0,o=e.length;i<o;i++)t[i]=m(e[i],1),n[i]=f(e[i],1);this.lines=e,this._startColumns=t,this._endColumns=n}return e.prototype.getElements=function(){for(var e=[],t=0,n=this.lines.length;t<n;t++)e[t]=this.lines[t].substring(this._startColumns[t]-1,this._endColumns[t]-1);return e},e.prototype.getStartLineNumber=function(e){return e+1},e.prototype.getEndLineNumber=function(e){return e+1},e.prototype.createCharSequence=function(e,t,n){for(var i=[],o=[],r=[],s=0,a=t;a<=n;a++)for(var l=this.lines[a],d=e?this._startColumns[a]:1,c=e?this._endColumns[a]:l.length+1,h=d;h<c;h++)i[s]=l.charCodeAt(h-1),o[s]=a+1,r[s]=h,s++;return new u(i,o,r)},e}(),u=function(){function e(e,t,n){this._charCodes=e,this._lineNumbers=t,this._columns=n}return e.prototype.getElements=function(){return this._charCodes},e.prototype.getStartLineNumber=function(e){return this._lineNumbers[e]},e.prototype.getStartColumn=function(e){return this._columns[e]},e.prototype.getEndLineNumber=function(e){return this._lineNumbers[e]},e.prototype.getEndColumn=function(e){return this._columns[e]+1},e}(),l=function(){function e(e,t,n,i,o,r,s,a){this.originalStartLineNumber=e,this.originalStartColumn=t,this.originalEndLineNumber=n,this.originalEndColumn=i,this.modifiedStartLineNumber=o,this.modifiedStartColumn=r,this.modifiedEndLineNumber=s,this.modifiedEndColumn=a}return e.createFromDiffChange=function(t,n,i){var o,r,s,a,u,l,d,c;return 0===t.originalLength?(o=0,r=0,s=0,a=0):(o=n.getStartLineNumber(t.originalStart),r=n.getStartColumn(t.originalStart),s=n.getEndLineNumber(t.originalStart+t.originalLength-1),a=n.getEndColumn(t.originalStart+t.originalLength-1)),0===t.modifiedLength?(u=0,l=0,d=0,c=0):(u=i.getStartLineNumber(t.modifiedStart),l=i.getStartColumn(t.modifiedStart),d=i.getEndLineNumber(t.modifiedStart+t.modifiedLength-1),c=i.getEndColumn(t.modifiedStart+t.modifiedLength-1)),new e(o,r,s,a,u,l,d,c)},e}();function d(e){if(e.length<=1)return e;for(var t=[e[0]],n=t[0],i=1,o=e.length;i<o;i++){var s=e[i],a=s.originalStart-(n.originalStart+n.originalLength),u=s.modifiedStart-(n.modifiedStart+n.modifiedLength),l=Math.min(a,u);l<r?(n.originalLength=s.originalStart+s.originalLength-n.originalStart,n.modifiedLength=s.modifiedStart+s.modifiedLength-n.modifiedStart):(t.push(s),n=s)}return t}var c=function(){function e(e,t,n,i,o){this.originalStartLineNumber=e,this.originalEndLineNumber=t,this.modifiedStartLineNumber=n,this.modifiedEndLineNumber=i,this.charChanges=o}return e.createFromDiffResult=function(t,n,i,o,r,a,u){var c,h,m,f,p=void 0;if(0===n.originalLength?(c=i.getStartLineNumber(n.originalStart)-1,h=0):(c=i.getStartLineNumber(n.originalStart),h=i.getEndLineNumber(n.originalStart+n.originalLength-1)),0===n.modifiedLength?(m=o.getStartLineNumber(n.modifiedStart)-1,f=0):(m=o.getStartLineNumber(n.modifiedStart),f=o.getEndLineNumber(n.modifiedStart+n.modifiedLength-1)),a&&n.originalLength>0&&n.originalLength<20&&n.modifiedLength>0&&n.modifiedLength<20&&r()){var g=i.createCharSequence(t,n.originalStart,n.originalStart+n.originalLength-1),w=o.createCharSequence(t,n.modifiedStart,n.modifiedStart+n.modifiedLength-1),v=s(g,w,r,!0).changes;u&&(v=d(v)),p=[];for(var b=0,C=v.length;b<C;b++)p.push(l.createFromDiffChange(v[b],g,w))}return new e(c,h,m,f,p)},e}(),h=function(){function e(e,t,n){this.shouldComputeCharChanges=n.shouldComputeCharChanges,this.shouldPostProcessCharChanges=n.shouldPostProcessCharChanges,this.shouldIgnoreTrimWhitespace=n.shouldIgnoreTrimWhitespace,this.shouldMakePrettyDiff=n.shouldMakePrettyDiff,this.originalLines=e,this.modifiedLines=t,this.original=new a(e),this.modified=new a(t),this.continueLineDiff=p(n.maxComputationTime),this.continueCharDiff=p(0===n.maxComputationTime?0:Math.min(n.maxComputationTime,5e3))}return e.prototype.computeDiff=function(){if(1===this.original.lines.length&&0===this.original.lines[0].length)return{quitEarly:!1,changes:[{originalStartLineNumber:1,originalEndLineNumber:1,modifiedStartLineNumber:1,modifiedEndLineNumber:this.modified.lines.length,charChanges:[{modifiedEndColumn:0,modifiedEndLineNumber:0,modifiedStartColumn:0,modifiedStartLineNumber:0,originalEndColumn:0,originalEndLineNumber:0,originalStartColumn:0,originalStartLineNumber:0}]}]};if(1===this.modified.lines.length&&0===this.modified.lines[0].length)return{quitEarly:!1,changes:[{originalStartLineNumber:1,originalEndLineNumber:this.original.lines.length,modifiedStartLineNumber:1,modifiedEndLineNumber:1,charChanges:[{modifiedEndColumn:0,modifiedEndLineNumber:0,modifiedStartColumn:0,modifiedStartLineNumber:0,originalEndColumn:0,originalEndLineNumber:0,originalStartColumn:0,originalStartLineNumber:0}]}]};var e=s(this.original,this.modified,this.continueLineDiff,this.shouldMakePrettyDiff),t=e.changes,n=e.quitEarly;if(this.shouldIgnoreTrimWhitespace){for(var i=[],o=0,r=t.length;o<r;o++)i.push(c.createFromDiffResult(this.shouldIgnoreTrimWhitespace,t[o],this.original,this.modified,this.continueCharDiff,this.shouldComputeCharChanges,this.shouldPostProcessCharChanges));return{quitEarly:n,changes:i}}for(var a=[],u=0,l=0,d=(o=-1,t.length);o<d;o++){var h=o+1<d?t[o+1]:null,p=h?h.originalStart:this.originalLines.length,g=h?h.modifiedStart:this.modifiedLines.length;while(u<p&&l<g){var w=this.originalLines[u],v=this.modifiedLines[l];if(w!==v){var b=m(w,1),C=m(v,1);while(b>1&&C>1){var y=w.charCodeAt(b-2),S=v.charCodeAt(C-2);if(y!==S)break;b--,C--}(b>1||C>1)&&this._pushTrimWhitespaceCharChange(a,u+1,1,b,l+1,1,C);var _=f(w,1),L=f(v,1),N=w.length+1,k=v.length+1;while(_<N&&L<k){y=w.charCodeAt(_-1),S=w.charCodeAt(L-1);if(y!==S)break;_++,L++}(_<N||L<k)&&this._pushTrimWhitespaceCharChange(a,u+1,_,N,l+1,L,k)}u++,l++}h&&(a.push(c.createFromDiffResult(this.shouldIgnoreTrimWhitespace,h,this.original,this.modified,this.continueCharDiff,this.shouldComputeCharChanges,this.shouldPostProcessCharChanges)),u+=h.originalLength,l+=h.modifiedLength)}return{quitEarly:n,changes:a}},e.prototype._pushTrimWhitespaceCharChange=function(e,t,n,i,o,r,s){if(!this._mergeTrimWhitespaceCharChange(e,t,n,i,o,r,s)){var a=void 0;this.shouldComputeCharChanges&&(a=[new l(t,n,t,i,o,r,o,s)]),e.push(new c(t,t,o,o,a))}},e.prototype._mergeTrimWhitespaceCharChange=function(e,t,n,i,o,r,s){var a=e.length;if(0===a)return!1;var u=e[a-1];return 0!==u.originalEndLineNumber&&0!==u.modifiedEndLineNumber&&(u.originalEndLineNumber+1===t&&u.modifiedEndLineNumber+1===o&&(u.originalEndLineNumber=t,u.modifiedEndLineNumber=o,this.shouldComputeCharChanges&&u.charChanges&&u.charChanges.push(new l(t,n,t,i,o,r,o,s)),!0))},e}();function m(e,t){var n=o["q"](e);return-1===n?t:n+1}function f(e,t){var n=o["D"](e);return-1===n?t:n+2}function p(e){if(0===e)return function(){return!0};var t=Date.now();return function(){return Date.now()-t<e}}},"2c29":function(e,t,n){"use strict";n.d(t,"a",(function(){return o})),n.d(t,"c",(function(){return r})),n.d(t,"e",(function(){return s})),n.d(t,"d",(function(){return a})),n.d(t,"b",(function(){return u}));var i=n("8025"),o=function(){function e(e,t,n){void 0===n&&(n=!1),this._range=e,this._text=t,this.insertsAutoWhitespace=n}return e.prototype.getEditOperations=function(e,t){t.addTrackedEditOperation(this._range,this._text)},e.prototype.computeCursorState=function(e,t){var n=t.getInverseEditOperations(),o=n[0].range;return new i["a"](o.endLineNumber,o.endColumn,o.endLineNumber,o.endColumn)},e}(),r=function(){function e(e,t){this._range=e,this._text=t}return e.prototype.getEditOperations=function(e,t){t.addTrackedEditOperation(this._range,this._text)},e.prototype.computeCursorState=function(e,t){var n=t.getInverseEditOperations(),o=n[0].range;return new i["a"](o.startLineNumber,o.startColumn,o.endLineNumber,o.endColumn)},e}(),s=function(){function e(e,t,n){void 0===n&&(n=!1),this._range=e,this._text=t,this.insertsAutoWhitespace=n}return e.prototype.getEditOperations=function(e,t){t.addTrackedEditOperation(this._range,this._text)},e.prototype.computeCursorState=function(e,t){var n=t.getInverseEditOperations(),o=n[0].range;return new i["a"](o.startLineNumber,o.startColumn,o.startLineNumber,o.startColumn)},e}(),a=function(){function e(e,t,n,i,o){void 0===o&&(o=!1),this._range=e,this._text=t,this._columnDeltaOffset=i,this._lineNumberDeltaOffset=n,this.insertsAutoWhitespace=o}return e.prototype.getEditOperations=function(e,t){t.addTrackedEditOperation(this._range,this._text)},e.prototype.computeCursorState=function(e,t){var n=t.getInverseEditOperations(),o=n[0].range;return new i["a"](o.endLineNumber+this._lineNumberDeltaOffset,o.endColumn+this._columnDeltaOffset,o.endLineNumber+this._lineNumberDeltaOffset,o.endColumn+this._columnDeltaOffset)},e}(),u=function(){function e(e,t,n,i){void 0===i&&(i=!1),this._range=e,this._text=t,this._initialSelection=n,this._forceMoveMarkers=i,this._selectionId=null}return e.prototype.getEditOperations=function(e,t){t.addTrackedEditOperation(this._range,this._text,this._forceMoveMarkers),this._selectionId=t.trackSelection(this._initialSelection)},e.prototype.computeCursorState=function(e,t){return t.getTrackedSelection(this._selectionId)},e}()},"2e5d":function(e,t,n){"use strict";n.d(t,"b",(function(){return f})),n.d(t,"f",(function(){return p})),n.d(t,"c",(function(){return g})),n.d(t,"d",(function(){return b})),n.d(t,"e",(function(){return C})),n.d(t,"a",(function(){return y})),n.d(t,"g",(function(){return S}));var i=n("fdcc"),o=n("3742"),r=n("7061"),s=n("6a89"),a=n("8025"),u=n("b57f"),l=n("70cb"),d=function(){return!0},c=function(){return!1},h=function(e){return" "===e||"\t"===e};function m(e,t,n){e.has(t)?e.get(t).push(n):e.set(t,[n])}var f=function(){function e(t,n,i){this._languageIdentifier=t;var o=i.options,r=o.get(107);this.readOnly=o.get(68),this.tabSize=n.tabSize,this.indentSize=n.indentSize,this.insertSpaces=n.insertSpaces,this.lineHeight=o.get(49),this.pageSize=Math.max(1,Math.floor(r.height/this.lineHeight)-2),this.useTabStops=o.get(95),this.wordSeparators=o.get(96),this.emptySelectionClipboard=o.get(25),this.copyWithSyntaxHighlighting=o.get(15),this.multiCursorMergeOverlapping=o.get(58),this.multiCursorPaste=o.get(60),this.autoClosingBrackets=o.get(5),this.autoClosingQuotes=o.get(7),this.autoClosingOvertype=o.get(6),this.autoSurround=o.get(10),this.autoIndent=o.get(8),this.autoClosingPairsOpen2=new Map,this.autoClosingPairsClose2=new Map,this.surroundingPairs={},this._electricChars=null,this.shouldAutoCloseBefore={quote:e._getShouldAutoClose(t,this.autoClosingQuotes),bracket:e._getShouldAutoClose(t,this.autoClosingBrackets)};var s=e._getAutoClosingPairs(t);if(s)for(var a=0,u=s;a<u.length;a++){var l=u[a];m(this.autoClosingPairsOpen2,l.open.charAt(l.open.length-1),l),1===l.close.length&&m(this.autoClosingPairsClose2,l.close,l)}var d=e._getSurroundingPairs(t);if(d)for(var c=0,h=d;c<h.length;c++){l=h[c];this.surroundingPairs[l.open]=l.close}}return e.shouldRecreate=function(e){return e.hasChanged(107)||e.hasChanged(96)||e.hasChanged(25)||e.hasChanged(58)||e.hasChanged(60)||e.hasChanged(5)||e.hasChanged(7)||e.hasChanged(6)||e.hasChanged(10)||e.hasChanged(95)||e.hasChanged(49)||e.hasChanged(68)},Object.defineProperty(e.prototype,"electricChars",{get:function(){if(!this._electricChars){this._electricChars={};var t=e._getElectricCharacters(this._languageIdentifier);if(t)for(var n=0,i=t;n<i.length;n++){var o=i[n];this._electricChars[o]=!0}}return this._electricChars},enumerable:!0,configurable:!0}),e.prototype.normalizeIndentation=function(e){return u["b"].normalizeIndentation(e,this.indentSize,this.insertSpaces)},e._getElectricCharacters=function(e){try{return l["a"].getElectricCharacters(e.id)}catch(t){return Object(i["e"])(t),null}},e._getAutoClosingPairs=function(e){try{return l["a"].getAutoClosingPairs(e.id)}catch(t){return Object(i["e"])(t),null}},e._getShouldAutoClose=function(t,n){switch(n){case"beforeWhitespace":return h;case"languageDefined":return e._getLanguageDefinedShouldAutoClose(t);case"always":return d;case"never":return c}},e._getLanguageDefinedShouldAutoClose=function(e){try{var t=l["a"].getAutoCloseBeforeSet(e.id);return function(e){return-1!==t.indexOf(e)}}catch(n){return Object(i["e"])(n),c}},e._getSurroundingPairs=function(e){try{return l["a"].getSurroundingPairs(e.id)}catch(t){return Object(i["e"])(t),null}},e}(),p=function(){function e(t,n,i,o){this.selectionStart=t,this.selectionStartLeftoverVisibleColumns=n,this.position=i,this.leftoverVisibleColumns=o,this.selection=e._computeSelection(this.selectionStart,this.position)}return e.prototype.equals=function(e){return this.selectionStartLeftoverVisibleColumns===e.selectionStartLeftoverVisibleColumns&&this.leftoverVisibleColumns===e.leftoverVisibleColumns&&this.position.equals(e.position)&&this.selectionStart.equalsRange(e.selectionStart)},e.prototype.hasSelection=function(){return!this.selection.isEmpty()||!this.selectionStart.isEmpty()},e.prototype.move=function(t,n,i,o){return t?new e(this.selectionStart,this.selectionStartLeftoverVisibleColumns,new r["a"](n,i),o):new e(new s["a"](n,i,n,i),o,new r["a"](n,i),o)},e._computeSelection=function(e,t){var n,i,o,r;return e.isEmpty()?(n=e.startLineNumber,i=e.startColumn,o=t.lineNumber,r=t.column):t.isBeforeOrEqual(e.getStartPosition())?(n=e.endLineNumber,i=e.endColumn,o=t.lineNumber,r=t.column):(n=e.startLineNumber,i=e.startColumn,o=t.lineNumber,r=t.column),new a["a"](n,i,o,r)},e}(),g=function(){function e(e,t,n){this.model=t,this.viewModel=n,this.config=new f(this.model.getLanguageIdentifier(),this.model.getOptions(),e)}return e.prototype.validateViewPosition=function(e,t){return this.viewModel.coordinatesConverter.validateViewPosition(e,t)},e.prototype.validateViewRange=function(e,t){return this.viewModel.coordinatesConverter.validateViewRange(e,t)},e.prototype.convertViewRangeToModelRange=function(e){return this.viewModel.coordinatesConverter.convertViewRangeToModelRange(e)},e.prototype.convertViewPositionToModelPosition=function(e,t){return this.viewModel.coordinatesConverter.convertViewPositionToModelPosition(new r["a"](e,t))},e.prototype.convertModelPositionToViewPosition=function(e){return this.viewModel.coordinatesConverter.convertModelPositionToViewPosition(e)},e.prototype.convertModelRangeToViewRange=function(e){return this.viewModel.coordinatesConverter.convertModelRangeToViewRange(e)},e.prototype.getCurrentScrollTop=function(){return this.viewModel.viewLayout.getCurrentScrollTop()},e.prototype.getCompletelyVisibleViewRange=function(){return this.viewModel.getCompletelyVisibleViewRange()},e.prototype.getCompletelyVisibleModelRange=function(){var e=this.viewModel.getCompletelyVisibleViewRange();return this.viewModel.coordinatesConverter.convertViewRangeToModelRange(e)},e.prototype.getCompletelyVisibleViewRangeAtScrollTop=function(e){return this.viewModel.getCompletelyVisibleViewRangeAtScrollTop(e)},e.prototype.getVerticalOffsetForViewLine=function(e){return this.viewModel.viewLayout.getVerticalOffsetForLineNumber(e)},e}(),w=function(){function e(e){this.modelState=e,this.viewState=null}return e}(),v=function(){function e(e){this.modelState=null,this.viewState=e}return e}(),b=function(){function e(e,t){this.modelState=e,this.viewState=t}return e.fromModelState=function(e){return new w(e)},e.fromViewState=function(e){return new v(e)},e.fromModelSelection=function(t){var n=t.selectionStartLineNumber,i=t.selectionStartColumn,o=t.positionLineNumber,a=t.positionColumn,u=new p(new s["a"](n,i,n,i),0,new r["a"](o,a),0);return e.fromModelState(u)},e.fromModelSelections=function(e){for(var t=[],n=0,i=e.length;n<i;n++)t[n]=this.fromModelSelection(e[n]);return t},e.prototype.equals=function(e){return this.viewState.equals(e.viewState)&&this.modelState.equals(e.modelState)},e}(),C=function(){function e(e,t,n){this.type=e,this.commands=t,this.shouldPushStackElementBefore=n.shouldPushStackElementBefore,this.shouldPushStackElementAfter=n.shouldPushStackElementAfter}return e}(),y=function(){function e(){}return e.visibleColumnFromColumn=function(t,n,i){var r=t.length,s=n-1<r?n-1:r,a=0,u=0;while(u<s){var l=o["u"](t,s,u);if(u+=l>=65536?2:1,9===l)a=e.nextRenderTabStop(a,i);else{var d=o["s"](l);while(u<s){var c=o["u"](t,s,u),h=o["s"](c);if(o["b"](d,h))break;u+=c>=65536?2:1,d=h}o["y"](l)||o["w"](l)?a+=2:a+=1}}return a},e.visibleColumnFromColumn2=function(e,t,n){return this.visibleColumnFromColumn(t.getLineContent(n.lineNumber),n.column,e.tabSize)},e.columnFromVisibleColumn=function(t,n,i){if(n<=0)return 1;var r=t.length,s=0,a=1,u=0;while(u<r){var l=o["u"](t,r,u);u+=l>=65536?2:1;var d=void 0;if(9===l)d=e.nextRenderTabStop(s,i);else{var c=o["s"](l);while(u<r){var h=o["u"](t,r,u),m=o["s"](h);if(o["b"](c,m))break;u+=h>=65536?2:1,c=m}d=o["y"](l)||o["w"](l)?s+2:s+1}var f=u+1;if(d>=n){var p=n-s,g=d-n;return g<p?f:a}s=d,a=f}return r+1},e.columnFromVisibleColumn2=function(e,t,n,i){var o=this.columnFromVisibleColumn(t.getLineContent(n),i,e.tabSize),r=t.getLineMinColumn(n);if(o<r)return r;var s=t.getLineMaxColumn(n);return o>s?s:o},e.nextRenderTabStop=function(e,t){return e+t-e%t},e.nextIndentTabStop=function(e,t){return e+t-e%t},e.prevRenderTabStop=function(e,t){return e-1-(e-1)%t},e.prevIndentTabStop=function(e,t){return e-1-(e-1)%t},e}();function S(e){return"'"===e||'"'===e||"`"===e}},3170:function(e,t,n){"use strict";n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return r}));var i=n("099d"),o=function(){function e(t){var n=Object(i["b"])(t);this._defaultValue=n,this._asciiMap=e._createAsciiMap(n),this._map=new Map}return e._createAsciiMap=function(e){for(var t=new Uint8Array(256),n=0;n<256;n++)t[n]=e;return t},e.prototype.set=function(e,t){var n=Object(i["b"])(t);e>=0&&e<256?this._asciiMap[e]=n:this._map.set(e,n)},e.prototype.get=function(e){return e>=0&&e<256?this._asciiMap[e]:this._map.get(e)||this._defaultValue},e}(),r=function(){function e(){this._actual=new o(0)}return e.prototype.add=function(e){this._actual.set(e,1)},e.prototype.has=function(e){return 1===this._actual.get(e)},e}()},4503:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var i=function(){function e(t,n,i,o){this.r=e._clamp(t),this.g=e._clamp(n),this.b=e._clamp(i),this.a=e._clamp(o)}return e._clamp=function(e){return e<0?0:e>255?255:0|e},e.Empty=new e(0,0,0,0),e}()},"4dc7":function(e,t,n){"use strict";n.d(t,"a",(function(){return i})),n.d(t,"b",(function(){return o})),n.d(t,"c",(function(){return r}));var i=function(){function e(e,t,n){this.offset=0|e,this.type=t,this.language=n}return e.prototype.toString=function(){return"("+this.offset+", "+this.type+")"},e}(),o=function(){function e(e,t){this.tokens=e,this.endState=t}return e}(),r=function(){function e(e,t){this.tokens=e,this.endState=t}return e}()},"62bd":function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var i=n("308f"),o=new(function(){function e(){this._zoomLevel=0,this._onDidChangeZoomLevel=new i["a"],this.onDidChangeZoomLevel=this._onDidChangeZoomLevel.event}return e.prototype.getZoomLevel=function(){return this._zoomLevel},e.prototype.setZoomLevel=function(e){e=Math.min(Math.max(-5,e),20),this._zoomLevel!==e&&(this._zoomLevel=e,this._onDidChangeZoomLevel.fire(this._zoomLevel))},e}())},"6a89":function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var i=n("7061"),o=function(){function e(e,t,n,i){e>n||e===n&&t>i?(this.startLineNumber=n,this.startColumn=i,this.endLineNumber=e,this.endColumn=t):(this.startLineNumber=e,this.startColumn=t,this.endLineNumber=n,this.endColumn=i)}return e.prototype.isEmpty=function(){return e.isEmpty(this)},e.isEmpty=function(e){return e.startLineNumber===e.endLineNumber&&e.startColumn===e.endColumn},e.prototype.containsPosition=function(t){return e.containsPosition(this,t)},e.containsPosition=function(e,t){return!(t.lineNumber<e.startLineNumber||t.lineNumber>e.endLineNumber)&&(!(t.lineNumber===e.startLineNumber&&t.column<e.startColumn)&&!(t.lineNumber===e.endLineNumber&&t.column>e.endColumn))},e.prototype.containsRange=function(t){return e.containsRange(this,t)},e.containsRange=function(e,t){return!(t.startLineNumber<e.startLineNumber||t.endLineNumber<e.startLineNumber)&&(!(t.startLineNumber>e.endLineNumber||t.endLineNumber>e.endLineNumber)&&(!(t.startLineNumber===e.startLineNumber&&t.startColumn<e.startColumn)&&!(t.endLineNumber===e.endLineNumber&&t.endColumn>e.endColumn)))},e.prototype.strictContainsRange=function(t){return e.strictContainsRange(this,t)},e.strictContainsRange=function(e,t){return!(t.startLineNumber<e.startLineNumber||t.endLineNumber<e.startLineNumber)&&(!(t.startLineNumber>e.endLineNumber||t.endLineNumber>e.endLineNumber)&&(!(t.startLineNumber===e.startLineNumber&&t.startColumn<=e.startColumn)&&!(t.endLineNumber===e.endLineNumber&&t.endColumn>=e.endColumn)))},e.prototype.plusRange=function(t){return e.plusRange(this,t)},e.plusRange=function(t,n){var i,o,r,s;return n.startLineNumber<t.startLineNumber?(i=n.startLineNumber,o=n.startColumn):n.startLineNumber===t.startLineNumber?(i=n.startLineNumber,o=Math.min(n.startColumn,t.startColumn)):(i=t.startLineNumber,o=t.startColumn),n.endLineNumber>t.endLineNumber?(r=n.endLineNumber,s=n.endColumn):n.endLineNumber===t.endLineNumber?(r=n.endLineNumber,s=Math.max(n.endColumn,t.endColumn)):(r=t.endLineNumber,s=t.endColumn),new e(i,o,r,s)},e.prototype.intersectRanges=function(t){return e.intersectRanges(this,t)},e.intersectRanges=function(t,n){var i=t.startLineNumber,o=t.startColumn,r=t.endLineNumber,s=t.endColumn,a=n.startLineNumber,u=n.startColumn,l=n.endLineNumber,d=n.endColumn;return i<a?(i=a,o=u):i===a&&(o=Math.max(o,u)),r>l?(r=l,s=d):r===l&&(s=Math.min(s,d)),i>r||i===r&&o>s?null:new e(i,o,r,s)},e.prototype.equalsRange=function(t){return e.equalsRange(this,t)},e.equalsRange=function(e,t){return!!e&&!!t&&e.startLineNumber===t.startLineNumber&&e.startColumn===t.startColumn&&e.endLineNumber===t.endLineNumber&&e.endColumn===t.endColumn},e.prototype.getEndPosition=function(){return new i["a"](this.endLineNumber,this.endColumn)},e.prototype.getStartPosition=function(){return new i["a"](this.startLineNumber,this.startColumn)},e.prototype.toString=function(){return"["+this.startLineNumber+","+this.startColumn+" -> "+this.endLineNumber+","+this.endColumn+"]"},e.prototype.setEndPosition=function(t,n){return new e(this.startLineNumber,this.startColumn,t,n)},e.prototype.setStartPosition=function(t,n){return new e(t,n,this.endLineNumber,this.endColumn)},e.prototype.collapseToStart=function(){return e.collapseToStart(this)},e.collapseToStart=function(t){return new e(t.startLineNumber,t.startColumn,t.startLineNumber,t.startColumn)},e.fromPositions=function(t,n){return void 0===n&&(n=t),new e(t.lineNumber,t.column,n.lineNumber,n.column)},e.lift=function(t){return t?new e(t.startLineNumber,t.startColumn,t.endLineNumber,t.endColumn):null},e.isIRange=function(e){return e&&"number"===typeof e.startLineNumber&&"number"===typeof e.startColumn&&"number"===typeof e.endLineNumber&&"number"===typeof e.endColumn},e.areIntersectingOrTouching=function(e,t){return!(e.endLineNumber<t.startLineNumber||e.endLineNumber===t.startLineNumber&&e.endColumn<t.startColumn)&&!(t.endLineNumber<e.startLineNumber||t.endLineNumber===e.startLineNumber&&t.endColumn<e.startColumn)},e.areIntersecting=function(e,t){return!(e.endLineNumber<t.startLineNumber||e.endLineNumber===t.startLineNumber&&e.endColumn<=t.startColumn)&&!(t.endLineNumber<e.startLineNumber||t.endLineNumber===e.startLineNumber&&t.endColumn<=e.startColumn)},e.compareRangesUsingStarts=function(e,t){if(e&&t){var n=0|e.startLineNumber,i=0|t.startLineNumber;if(n===i){var o=0|e.startColumn,r=0|t.startColumn;if(o===r){var s=0|e.endLineNumber,a=0|t.endLineNumber;if(s===a){var u=0|e.endColumn,l=0|t.endColumn;return u-l}return s-a}return o-r}return n-i}var d=e?1:0,c=t?1:0;return d-c},e.compareRangesUsingEnds=function(e,t){return e.endLineNumber===t.endLineNumber?e.endColumn===t.endColumn?e.startLineNumber===t.startLineNumber?e.startColumn-t.startColumn:e.startLineNumber-t.startLineNumber:e.endColumn-t.endColumn:e.endLineNumber-t.endLineNumber},e.spansMultipleLines=function(e){return e.endLineNumber>e.startLineNumber},e}()},7061:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var i=function(){function e(e,t){this.lineNumber=e,this.column=t}return e.prototype.with=function(t,n){return void 0===t&&(t=this.lineNumber),void 0===n&&(n=this.column),t===this.lineNumber&&n===this.column?this:new e(t,n)},e.prototype.delta=function(e,t){return void 0===e&&(e=0),void 0===t&&(t=0),this.with(this.lineNumber+e,this.column+t)},e.prototype.equals=function(t){return e.equals(this,t)},e.equals=function(e,t){return!e&&!t||!!e&&!!t&&e.lineNumber===t.lineNumber&&e.column===t.column},e.prototype.isBefore=function(t){return e.isBefore(this,t)},e.isBefore=function(e,t){return e.lineNumber<t.lineNumber||!(t.lineNumber<e.lineNumber)&&e.column<t.column},e.prototype.isBeforeOrEqual=function(t){return e.isBeforeOrEqual(this,t)},e.isBeforeOrEqual=function(e,t){return e.lineNumber<t.lineNumber||!(t.lineNumber<e.lineNumber)&&e.column<=t.column},e.compare=function(e,t){var n=0|e.lineNumber,i=0|t.lineNumber;if(n===i){var o=0|e.column,r=0|t.column;return o-r}return n-i},e.prototype.clone=function(){return new e(this.lineNumber,this.column)},e.prototype.toString=function(){return"("+this.lineNumber+","+this.column+")"},e.lift=function(t){return new e(t.lineNumber,t.column)},e.isIPosition=function(e){return e&&"number"===typeof e.lineNumber&&"number"===typeof e.column},e}()},"7ab3":function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var i,o=n("3742");i="undefined"!==typeof TextDecoder?function(e){return new r(e)}:function(e){return new s};var r=function(){function e(e){this._decoder=new TextDecoder("UTF-16LE"),this._capacity=0|e,this._buffer=new Uint16Array(this._capacity),this._completedStrings=null,this._bufferLength=0}return e.prototype.reset=function(){this._completedStrings=null,this._bufferLength=0},e.prototype.build=function(){return null!==this._completedStrings?(this._flushBuffer(),this._completedStrings.join("")):this._buildBuffer()},e.prototype._buildBuffer=function(){if(0===this._bufferLength)return"";var e=new Uint16Array(this._buffer.buffer,0,this._bufferLength);return this._decoder.decode(e)},e.prototype._flushBuffer=function(){var e=this._buildBuffer();this._bufferLength=0,null===this._completedStrings?this._completedStrings=[e]:this._completedStrings[this._completedStrings.length]=e},e.prototype.write1=function(e){var t=this._capacity-this._bufferLength;t<=1&&(0===t||o["z"](e))&&this._flushBuffer(),this._buffer[this._bufferLength++]=e},e.prototype.appendASCII=function(e){this._bufferLength===this._capacity&&this._flushBuffer(),this._buffer[this._bufferLength++]=e},e.prototype.appendASCIIString=function(e){var t=e.length;if(this._bufferLength+t>=this._capacity)return this._flushBuffer(),void(this._completedStrings[this._completedStrings.length]=e);for(var n=0;n<t;n++)this._buffer[this._bufferLength++]=e.charCodeAt(n)},e}(),s=function(){function e(){this._pieces=[],this._piecesLen=0}return e.prototype.reset=function(){this._pieces=[],this._piecesLen=0},e.prototype.build=function(){return this._pieces.join("")},e.prototype.write1=function(e){this._pieces[this._piecesLen++]=String.fromCharCode(e)},e.prototype.appendASCII=function(e){this._pieces[this._piecesLen++]=String.fromCharCode(e)},e.prototype.appendASCIIString=function(e){this._pieces[this._piecesLen++]=e},e}()},"7faa":function(e,t,n){"use strict";n.d(t,"a",(function(){return s}));var i=n("3742"),o=n("d3f4"),r=n("6a89"),s=function(){function e(e,t){this._selection=e,this._cursors=t,this._selectionId=null}return e.prototype.getEditOperations=function(e,t){for(var n=a(e,this._cursors),i=0,o=n.length;i<o;i++){var r=n[i];t.addEditOperation(r.range,r.text)}this._selectionId=t.trackSelection(this._selection)},e.prototype.computeCursorState=function(e,t){return t.getTrackedSelection(this._selectionId)},e}();function a(e,t){t.sort((function(e,t){return e.lineNumber===t.lineNumber?e.column-t.column:e.lineNumber-t.lineNumber}));for(var n=t.length-2;n>=0;n--)t[n].lineNumber===t[n+1].lineNumber&&t.splice(n,1);for(var s=[],a=0,u=0,l=t.length,d=1,c=e.getLineCount();d<=c;d++){var h=e.getLineContent(d),m=h.length+1,f=0;if(!(u<l&&t[u].lineNumber===d&&(f=t[u].column,u++,f===m))&&0!==h.length){var p=i["D"](h),g=0;if(-1===p)g=1;else{if(p===h.length-1)continue;g=p+2}g=Math.max(f,g),s[a++]=o["a"].delete(new r["a"](d,g,d,m))}}return s}},8025:function(e,t,n){"use strict";n.d(t,"a",(function(){return s}));var i=n("7061"),o=n("6a89"),r=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),s=function(e){function t(t,n,i,o){var r=e.call(this,t,n,i,o)||this;return r.selectionStartLineNumber=t,r.selectionStartColumn=n,r.positionLineNumber=i,r.positionColumn=o,r}return r(t,e),t.prototype.toString=function(){return"["+this.selectionStartLineNumber+","+this.selectionStartColumn+" -> "+this.positionLineNumber+","+this.positionColumn+"]"},t.prototype.equalsSelection=function(e){return t.selectionsEqual(this,e)},t.selectionsEqual=function(e,t){return e.selectionStartLineNumber===t.selectionStartLineNumber&&e.selectionStartColumn===t.selectionStartColumn&&e.positionLineNumber===t.positionLineNumber&&e.positionColumn===t.positionColumn},t.prototype.getDirection=function(){return this.selectionStartLineNumber===this.startLineNumber&&this.selectionStartColumn===this.startColumn?0:1},t.prototype.setEndPosition=function(e,n){return 0===this.getDirection()?new t(this.startLineNumber,this.startColumn,e,n):new t(e,n,this.startLineNumber,this.startColumn)},t.prototype.getPosition=function(){return new i["a"](this.positionLineNumber,this.positionColumn)},t.prototype.setStartPosition=function(e,n){return 0===this.getDirection()?new t(e,n,this.endLineNumber,this.endColumn):new t(this.endLineNumber,this.endColumn,e,n)},t.fromPositions=function(e,n){return void 0===n&&(n=e),new t(e.lineNumber,e.column,n.lineNumber,n.column)},t.liftSelection=function(e){return new t(e.selectionStartLineNumber,e.selectionStartColumn,e.positionLineNumber,e.positionColumn)},t.selectionsArrEqual=function(e,t){if(e&&!t||!e&&t)return!1;if(!e&&!t)return!0;if(e.length!==t.length)return!1;for(var n=0,i=e.length;n<i;n++)if(!this.selectionsEqual(e[n],t[n]))return!1;return!0},t.isISelection=function(e){return e&&"number"===typeof e.selectionStartLineNumber&&"number"===typeof e.selectionStartColumn&&"number"===typeof e.positionLineNumber&&"number"===typeof e.positionColumn},t.createWithDirection=function(e,n,i,o,r){return 0===r?new t(e,n,i,o):new t(i,o,e,n)},t}(o["a"])},8830:function(e,t,n){"use strict";n.d(t,"b",(function(){return g})),n.d(t,"a",(function(){return _})),n.d(t,"d",(function(){return V})),n.d(t,"c",(function(){return D}));var i=n("dff7"),o=n("308f"),r=n("a666"),s=n("aa3d"),a=n("e8e3"),u=n("fd49"),l=n("62bd"),d=n("fb71"),c=n("0910"),h=n("89cd"),m=n("be5f"),f=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),p=function(){return p=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var o in t=arguments[n],t)Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},p.apply(this,arguments)},g=new(function(){function e(){this._tabFocus=!1,this._onDidChangeTabFocus=new o["a"],this.onDidChangeTabFocus=this._onDidChangeTabFocus.event}return e.prototype.getTabFocusMode=function(){return this._tabFocus},e.prototype.setTabFocusMode=function(e){this._tabFocus!==e&&(this._tabFocus=e,this._onDidChangeTabFocus.fire(this._tabFocus))},e}()),w=Object.hasOwnProperty,v=function(){function e(){this._values=[]}return e.prototype._read=function(e){return this._values[e]},e.prototype.get=function(e){return this._values[e]},e.prototype._write=function(e,t){this._values[e]=t},e}(),b=function(){function e(){this._values=[]}return e.prototype._read=function(e){return this._values[e]},e.prototype._write=function(e,t){this._values[e]=t},e}(),C=function(){function e(){}return e.readOptions=function(e){for(var t=e,n=new b,i=0,o=u["i"];i<o.length;i++){var r=o[i],s="_never_"===r.name?void 0:t[r.name];n._write(r.id,s)}return n},e.validateOptions=function(e){for(var t=new u["h"],n=0,i=u["i"];n<i.length;n++){var o=i[n];t._write(o.id,o.validate(e._read(o.id)))}return t},e.computeOptions=function(e,t){for(var n=new v,i=0,o=u["i"];i<o.length;i++){var r=o[i];n._write(r.id,r.compute(t,n,e._read(r.id)))}return n},e._deepEquals=function(t,n){if("object"!==typeof t||"object"!==typeof n)return t===n;if(Array.isArray(t)||Array.isArray(n))return!(!Array.isArray(t)||!Array.isArray(n))&&a["g"](t,n);for(var i in t)if(!e._deepEquals(t[i],n[i]))return!1;return!0},e.checkEquals=function(t,n){for(var i=[],o=!1,r=0,s=u["i"];r<s.length;r++){var a=s[r],l=!e._deepEquals(t._read(a.id),n._read(a.id));i[a.id]=l,l&&(o=!0)}return o?new u["a"](i):null},e}();function y(e){var t=e.wordWrap;!0===t?e.wordWrap="on":!1===t&&(e.wordWrap="off");var n=e.lineNumbers;!0===n?e.lineNumbers="on":!1===n&&(e.lineNumbers="off");var i=e.autoClosingBrackets;!1===i&&(e.autoClosingBrackets="never",e.autoClosingQuotes="never",e.autoSurround="never");var o=e.cursorBlinking;"visible"===o&&(e.cursorBlinking="solid");var r=e.renderWhitespace;!0===r?e.renderWhitespace="boundary":!1===r&&(e.renderWhitespace="none");var s=e.renderLineHighlight;!0===s?e.renderLineHighlight="line":!1===s&&(e.renderLineHighlight="none");var a=e.acceptSuggestionOnEnter;!0===a?e.acceptSuggestionOnEnter="on":!1===a&&(e.acceptSuggestionOnEnter="off");var u=e.tabCompletion;!1===u?e.tabCompletion="off":!0===u&&(e.tabCompletion="onlySnippets");var l=e.suggest;if(l&&"object"===typeof l.filteredTypes&&l.filteredTypes){var d={method:"showMethods",function:"showFunctions",constructor:"showConstructors",field:"showFields",variable:"showVariables",class:"showClasses",struct:"showStructs",interface:"showInterfaces",module:"showModules",property:"showProperties",event:"showEvents",operator:"showOperators",unit:"showUnits",value:"showValues",constant:"showConstants",enum:"showEnums",enumMember:"showEnumMembers",keyword:"showKeywords",text:"showWords",color:"showColors",file:"showFiles",reference:"showReferences",folder:"showFolders",typeParameter:"showTypeParameters",snippet:"showSnippets"};Object(m["c"])(d,(function(e){var t=l.filteredTypes[e.key];!1===t&&(l[e.value]=t)}))}var c=e.hover;!0===c?e.hover={enabled:!0}:!1===c&&(e.hover={enabled:!1});var h=e.parameterHints;!0===h?e.parameterHints={enabled:!0}:!1===h&&(e.parameterHints={enabled:!1});var f=e.autoIndent;!0===f?e.autoIndent="full":!1===f&&(e.autoIndent="advanced");var p=e.matchBrackets;!0===p?e.matchBrackets="always":!1===p&&(e.matchBrackets="never")}function S(e){var t=s["c"](e);return y(t),t}var _=function(e){function t(t,n){var i=e.call(this)||this;return i._onDidChange=i._register(new o["a"]),i.onDidChange=i._onDidChange.event,i.isSimpleWidget=t,i._isDominatedByLongLines=!1,i._lineNumbersDigitCount=1,i._rawOptions=S(n),i._readOptions=C.readOptions(i._rawOptions),i._validatedOptions=C.validateOptions(i._readOptions),i._register(l["a"].onDidChangeZoomLevel((function(e){return i._recomputeOptions()}))),i._register(g.onDidChangeTabFocus((function(e){return i._recomputeOptions()}))),i}return f(t,e),t.prototype.observeReferenceElement=function(e){},t.prototype.dispose=function(){e.prototype.dispose.call(this)},t.prototype._recomputeOptions=function(){var e=this.options,t=this._computeInternalOptions();if(e){var n=C.checkEquals(e,t);if(null===n)return;this.options=t,this._onDidChange.fire(n)}else this.options=t},t.prototype.getRawOptions=function(){return this._rawOptions},t.prototype._computeInternalOptions=function(){var e=this._getEnvConfiguration(),t=d["a"].createFromValidatedSettings(this._validatedOptions,e.zoomLevel,this.isSimpleWidget),n={outerWidth:e.outerWidth,outerHeight:e.outerHeight,fontInfo:this.readConfiguration(t),extraEditorClassName:e.extraEditorClassName,isDominatedByLongLines:this._isDominatedByLongLines,lineNumbersDigitCount:this._lineNumbersDigitCount,emptySelectionClipboard:e.emptySelectionClipboard,pixelRatio:e.pixelRatio,tabFocusMode:g.getTabFocusMode(),accessibilitySupport:e.accessibilitySupport};return C.computeOptions(this._validatedOptions,n)},t._subsetEquals=function(e,t){for(var n in t)if(w.call(t,n)){var i=t[n],o=e[n];if(o===i)continue;if(Array.isArray(o)&&Array.isArray(i)){if(!a["g"](o,i))return!1;continue}if("object"===typeof o&&"object"===typeof i){if(!this._subsetEquals(o,i))return!1;continue}return!1}return!0},t.prototype.updateOptions=function(e){if("undefined"!==typeof e){var n=S(e);t._subsetEquals(this._rawOptions,n)||(this._rawOptions=s["g"](this._rawOptions,n||{}),this._readOptions=C.readOptions(this._rawOptions),this._validatedOptions=C.validateOptions(this._readOptions),this._recomputeOptions())}},t.prototype.setIsDominatedByLongLines=function(e){this._isDominatedByLongLines=e,this._recomputeOptions()},t.prototype.setMaxLineNumber=function(e){var n=t._digitCount(e);this._lineNumbersDigitCount!==n&&(this._lineNumbersDigitCount=n,this._recomputeOptions())},t._digitCount=function(e){var t=0;while(e)e=Math.floor(e/10),t++;return t||1},t}(r["a"]),L=Object.freeze({id:"editor",order:5,type:"object",title:i["a"]("editorConfigurationTitle","Editor"),scope:5}),N=h["a"].as(c["a"].Configuration),k=p(p({},L),{properties:{"editor.tabSize":{type:"number",default:u["c"].tabSize,minimum:1,markdownDescription:i["a"]("tabSize","The number of spaces a tab is equal to. This setting is overridden based on the file contents when `#editor.detectIndentation#` is on.")},"editor.insertSpaces":{type:"boolean",default:u["c"].insertSpaces,markdownDescription:i["a"]("insertSpaces","Insert spaces when pressing `Tab`. This setting is overridden based on the file contents when `#editor.detectIndentation#` is on.")},"editor.detectIndentation":{type:"boolean",default:u["c"].detectIndentation,markdownDescription:i["a"]("detectIndentation","Controls whether `#editor.tabSize#` and `#editor.insertSpaces#` will be automatically detected when a file is opened based on the file contents.")},"editor.trimAutoWhitespace":{type:"boolean",default:u["c"].trimAutoWhitespace,description:i["a"]("trimAutoWhitespace","Remove trailing auto inserted whitespace.")},"editor.largeFileOptimizations":{type:"boolean",default:u["c"].largeFileOptimizations,description:i["a"]("largeFileOptimizations","Special handling for large files to disable certain memory intensive features.")},"editor.wordBasedSuggestions":{type:"boolean",default:!0,description:i["a"]("wordBasedSuggestions","Controls whether completions should be computed based on words in the document.")},"editor.semanticHighlighting.enabled":{type:"boolean",default:!1,description:i["a"]("semanticHighlighting.enabled","Controls whether the semanticHighlighting is shown for the languages that support it.")},"editor.stablePeek":{type:"boolean",default:!1,markdownDescription:i["a"]("stablePeek","Keep peek editors open even when double clicking their content or when hitting `Escape`.")},"editor.maxTokenizationLineLength":{type:"integer",default:2e4,description:i["a"]("maxTokenizationLineLength","Lines above this length will not be tokenized for performance reasons")},"diffEditor.maxComputationTime":{type:"number",default:5e3,description:i["a"]("maxComputationTime","Timeout in milliseconds after which diff computation is cancelled. Use 0 for no timeout.")},"diffEditor.renderSideBySide":{type:"boolean",default:!0,description:i["a"]("sideBySide","Controls whether the diff editor shows the diff side by side or inline.")},"diffEditor.ignoreTrimWhitespace":{type:"boolean",default:!0,description:i["a"]("ignoreTrimWhitespace","Controls whether the diff editor shows changes in leading or trailing whitespace as diffs.")},"diffEditor.renderIndicators":{type:"boolean",default:!0,description:i["a"]("renderIndicators","Controls whether the diff editor shows +/- indicators for added/removed changes.")}}});function O(e){return"undefined"!==typeof e.type||"undefined"!==typeof e.anyOf}for(var E=0,P=u["i"];E<P.length;E++){var I=P[E],M=I.schema;if("undefined"!==typeof M)if(O(M))k.properties["editor."+I.name]=M;else for(var T in M)w.call(M,T)&&(k.properties[T]=M[T])}var x=null;function W(){return null===x&&(x=Object.create(null),Object.keys(k.properties).forEach((function(e){x[e]=!0}))),x}function V(e){var t=W();return t["editor."+e]||!1}function D(e){var t=W();return t["diffEditor."+e]||!1}N.registerConfiguration(k)},"8ae8":function(e,t,n){"use strict";function i(e){return e&&"string"===typeof e.id}n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return r}));var o={ICodeEditor:"vs.editor.ICodeEditor",IDiffEditor:"vs.editor.IDiffEditor"},r={ExecuteCommand:"executeCommand",ExecuteCommands:"executeCommands",Type:"type",ReplacePreviousChar:"replacePreviousChar",CompositionStart:"compositionStart",CompositionEnd:"compositionEnd",Paste:"paste",Cut:"cut",Undo:"undo",Redo:"redo"}},a007:function(e,t,n){"use strict";n.d(t,"b",(function(){return d})),n.d(t,"a",(function(){return i}));var i,o=n("ef8e"),r=n("2e5d"),s=n("f85a"),a=n("d48d"),u=n("7061"),l=n("6a89"),d=function(){function e(){}return e.addCursorDown=function(e,t,n){for(var i=[],o=0,a=0,u=t.length;a<u;a++){var l=t[a];i[o++]=new r["d"](l.modelState,l.viewState),i[o++]=n?r["d"].fromModelState(s["a"].translateDown(e.config,e.model,l.modelState)):r["d"].fromViewState(s["a"].translateDown(e.config,e.viewModel,l.viewState))}return i},e.addCursorUp=function(e,t,n){for(var i=[],o=0,a=0,u=t.length;a<u;a++){var l=t[a];i[o++]=new r["d"](l.modelState,l.viewState),i[o++]=n?r["d"].fromModelState(s["a"].translateUp(e.config,e.model,l.modelState)):r["d"].fromViewState(s["a"].translateUp(e.config,e.viewModel,l.viewState))}return i},e.moveToBeginningOfLine=function(e,t,n){for(var i=[],o=0,r=t.length;o<r;o++){var s=t[o];i[o]=this._moveToLineStart(e,s,n)}return i},e._moveToLineStart=function(e,t,n){var i=t.viewState.position.column,o=t.modelState.position.column,r=i===o,s=t.viewState.position.lineNumber,a=e.viewModel.getLineFirstNonWhitespaceColumn(s),u=i===a;return r||u?this._moveToLineStartByModel(e,t,n):this._moveToLineStartByView(e,t,n)},e._moveToLineStartByView=function(e,t,n){return r["d"].fromViewState(s["a"].moveToBeginningOfLine(e.config,e.viewModel,t.viewState,n))},e._moveToLineStartByModel=function(e,t,n){return r["d"].fromModelState(s["a"].moveToBeginningOfLine(e.config,e.model,t.modelState,n))},e.moveToEndOfLine=function(e,t,n){for(var i=[],o=0,r=t.length;o<r;o++){var s=t[o];i[o]=this._moveToLineEnd(e,s,n)}return i},e._moveToLineEnd=function(e,t,n){var i=t.viewState.position,o=e.viewModel.getLineMaxColumn(i.lineNumber),r=i.column===o,s=t.modelState.position,a=e.model.getLineMaxColumn(s.lineNumber),u=o-i.column===a-s.column;return r||u?this._moveToLineEndByModel(e,t,n):this._moveToLineEndByView(e,t,n)},e._moveToLineEndByView=function(e,t,n){return r["d"].fromViewState(s["a"].moveToEndOfLine(e.config,e.viewModel,t.viewState,n))},e._moveToLineEndByModel=function(e,t,n){return r["d"].fromModelState(s["a"].moveToEndOfLine(e.config,e.model,t.modelState,n))},e.expandLineSelection=function(e,t){for(var n=[],i=0,o=t.length;i<o;i++){var s=t[i],a=s.modelState.selection.startLineNumber,d=e.model.getLineCount(),c=s.modelState.selection.endLineNumber,h=void 0;c===d?h=e.model.getLineMaxColumn(d):(c++,h=1),n[i]=r["d"].fromModelState(new r["f"](new l["a"](a,1,a,1),0,new u["a"](c,h),0))}return n},e.moveToBeginningOfBuffer=function(e,t,n){for(var i=[],o=0,a=t.length;o<a;o++){var u=t[o];i[o]=r["d"].fromModelState(s["a"].moveToBeginningOfBuffer(e.config,e.model,u.modelState,n))}return i},e.moveToEndOfBuffer=function(e,t,n){for(var i=[],o=0,a=t.length;o<a;o++){var u=t[o];i[o]=r["d"].fromModelState(s["a"].moveToEndOfBuffer(e.config,e.model,u.modelState,n))}return i},e.selectAll=function(e,t){var n=e.model.getLineCount(),i=e.model.getLineMaxColumn(n);return r["d"].fromModelState(new r["f"](new l["a"](1,1,1,1),0,new u["a"](n,i),0))},e.line=function(e,t,n,i,o){var s=e.model.validatePosition(i),a=o?e.validateViewPosition(new u["a"](o.lineNumber,o.column),s):e.convertModelPositionToViewPosition(s);if(!n||!t.modelState.hasSelection()){var d=e.model.getLineCount(),c=s.lineNumber+1,h=1;return c>d&&(c=d,h=e.model.getLineMaxColumn(c)),r["d"].fromModelState(new r["f"](new l["a"](s.lineNumber,1,c,h),0,new u["a"](c,h),0))}var m=t.modelState.selectionStart.getStartPosition().lineNumber;if(s.lineNumber<m)return r["d"].fromViewState(t.viewState.move(t.modelState.hasSelection(),a.lineNumber,1,0));if(s.lineNumber>m){d=e.viewModel.getLineCount();var f=a.lineNumber+1,p=1;return f>d&&(f=d,p=e.viewModel.getLineMaxColumn(f)),r["d"].fromViewState(t.viewState.move(t.modelState.hasSelection(),f,p,0))}var g=t.modelState.selectionStart.getEndPosition();return r["d"].fromModelState(t.modelState.move(t.modelState.hasSelection(),g.lineNumber,g.column,0))},e.word=function(e,t,n,i){var o=e.model.validatePosition(i);return r["d"].fromModelState(a["a"].word(e.config,e.model,t.modelState,n,o))},e.cancelSelection=function(e,t){if(!t.modelState.hasSelection())return new r["d"](t.modelState,t.viewState);var n=t.viewState.position.lineNumber,i=t.viewState.position.column;return r["d"].fromViewState(new r["f"](new l["a"](n,i,n,i),0,new u["a"](n,i),0))},e.moveTo=function(e,t,n,i,o){var s=e.model.validatePosition(i),a=o?e.validateViewPosition(new u["a"](o.lineNumber,o.column),s):e.convertModelPositionToViewPosition(s);return r["d"].fromViewState(t.viewState.move(n,a.lineNumber,a.column,0))},e.move=function(e,t,n){var i=n.select,o=n.value;switch(n.direction){case 0:return 4===n.unit?this._moveHalfLineLeft(e,t,i):this._moveLeft(e,t,i,o);case 1:return 4===n.unit?this._moveHalfLineRight(e,t,i):this._moveRight(e,t,i,o);case 2:return 2===n.unit?this._moveUpByViewLines(e,t,i,o):this._moveUpByModelLines(e,t,i,o);case 3:return 2===n.unit?this._moveDownByViewLines(e,t,i,o):this._moveDownByModelLines(e,t,i,o);case 4:return this._moveToViewMinColumn(e,t,i);case 5:return this._moveToViewFirstNonWhitespaceColumn(e,t,i);case 6:return this._moveToViewCenterColumn(e,t,i);case 7:return this._moveToViewMaxColumn(e,t,i);case 8:return this._moveToViewLastNonWhitespaceColumn(e,t,i);case 9:var r=t[0],s=e.getCompletelyVisibleModelRange(),a=this._firstLineNumberInRange(e.model,s,o),u=e.model.getLineFirstNonWhitespaceColumn(a);return[this._moveToModelPosition(e,r,i,a,u)];case 11:r=t[0],s=e.getCompletelyVisibleModelRange(),a=this._lastLineNumberInRange(e.model,s,o),u=e.model.getLineFirstNonWhitespaceColumn(a);return[this._moveToModelPosition(e,r,i,a,u)];case 10:r=t[0],s=e.getCompletelyVisibleModelRange(),a=Math.round((s.startLineNumber+s.endLineNumber)/2),u=e.model.getLineFirstNonWhitespaceColumn(a);return[this._moveToModelPosition(e,r,i,a,u)];case 12:for(var l=e.getCompletelyVisibleViewRange(),d=[],c=0,h=t.length;c<h;c++){r=t[c];d[c]=this.findPositionInViewportIfOutside(e,r,l,i)}return d}return null},e.findPositionInViewportIfOutside=function(e,t,n,i){var o=t.viewState.position.lineNumber;if(n.startLineNumber<=o&&o<=n.endLineNumber-1)return new r["d"](t.modelState,t.viewState);o>n.endLineNumber-1&&(o=n.endLineNumber-1),o<n.startLineNumber&&(o=n.startLineNumber);var s=e.viewModel.getLineFirstNonWhitespaceColumn(o);return this._moveToViewPosition(e,t,i,o,s)},e._firstLineNumberInRange=function(e,t,n){var i=t.startLineNumber;return t.startColumn!==e.getLineMinColumn(i)&&i++,Math.min(t.endLineNumber,i+n-1)},e._lastLineNumberInRange=function(e,t,n){var i=t.startLineNumber;return t.startColumn!==e.getLineMinColumn(i)&&i++,Math.max(i,t.endLineNumber-n+1)},e._moveLeft=function(e,t,n,i){for(var o=[],a=0,u=t.length;a<u;a++){var l=t[a],d=s["a"].moveLeft(e.config,e.viewModel,l.viewState,n,i);if(1===i&&d.position.lineNumber!==l.viewState.position.lineNumber){var c=e.viewModel.coordinatesConverter.convertViewPositionToModelPosition(d.position);c.lineNumber===l.modelState.position.lineNumber&&(d=s["a"].moveLeft(e.config,e.viewModel,d,n,1))}o[a]=r["d"].fromViewState(d)}return o},e._moveHalfLineLeft=function(e,t,n){for(var i=[],o=0,a=t.length;o<a;o++){var u=t[o],l=u.viewState.position.lineNumber,d=Math.round(e.viewModel.getLineContent(l).length/2);i[o]=r["d"].fromViewState(s["a"].moveLeft(e.config,e.viewModel,u.viewState,n,d))}return i},e._moveRight=function(e,t,n,i){for(var o=[],a=0,u=t.length;a<u;a++){var l=t[a],d=s["a"].moveRight(e.config,e.viewModel,l.viewState,n,i);if(1===i&&d.position.lineNumber!==l.viewState.position.lineNumber){var c=e.viewModel.coordinatesConverter.convertViewPositionToModelPosition(d.position);c.lineNumber===l.modelState.position.lineNumber&&(d=s["a"].moveRight(e.config,e.viewModel,d,n,1))}o[a]=r["d"].fromViewState(d)}return o},e._moveHalfLineRight=function(e,t,n){for(var i=[],o=0,a=t.length;o<a;o++){var u=t[o],l=u.viewState.position.lineNumber,d=Math.round(e.viewModel.getLineContent(l).length/2);i[o]=r["d"].fromViewState(s["a"].moveRight(e.config,e.viewModel,u.viewState,n,d))}return i},e._moveDownByViewLines=function(e,t,n,i){for(var o=[],a=0,u=t.length;a<u;a++){var l=t[a];o[a]=r["d"].fromViewState(s["a"].moveDown(e.config,e.viewModel,l.viewState,n,i))}return o},e._moveDownByModelLines=function(e,t,n,i){for(var o=[],a=0,u=t.length;a<u;a++){var l=t[a];o[a]=r["d"].fromModelState(s["a"].moveDown(e.config,e.model,l.modelState,n,i))}return o},e._moveUpByViewLines=function(e,t,n,i){for(var o=[],a=0,u=t.length;a<u;a++){var l=t[a];o[a]=r["d"].fromViewState(s["a"].moveUp(e.config,e.viewModel,l.viewState,n,i))}return o},e._moveUpByModelLines=function(e,t,n,i){for(var o=[],a=0,u=t.length;a<u;a++){var l=t[a];o[a]=r["d"].fromModelState(s["a"].moveUp(e.config,e.model,l.modelState,n,i))}return o},e._moveToViewPosition=function(e,t,n,i,o){return r["d"].fromViewState(t.viewState.move(n,i,o,0))},e._moveToModelPosition=function(e,t,n,i,o){return r["d"].fromModelState(t.modelState.move(n,i,o,0))},e._moveToViewMinColumn=function(e,t,n){for(var i=[],o=0,r=t.length;o<r;o++){var s=t[o],a=s.viewState.position.lineNumber,u=e.viewModel.getLineMinColumn(a);i[o]=this._moveToViewPosition(e,s,n,a,u)}return i},e._moveToViewFirstNonWhitespaceColumn=function(e,t,n){for(var i=[],o=0,r=t.length;o<r;o++){var s=t[o],a=s.viewState.position.lineNumber,u=e.viewModel.getLineFirstNonWhitespaceColumn(a);i[o]=this._moveToViewPosition(e,s,n,a,u)}return i},e._moveToViewCenterColumn=function(e,t,n){for(var i=[],o=0,r=t.length;o<r;o++){var s=t[o],a=s.viewState.position.lineNumber,u=Math.round((e.viewModel.getLineMaxColumn(a)+e.viewModel.getLineMinColumn(a))/2);i[o]=this._moveToViewPosition(e,s,n,a,u)}return i},e._moveToViewMaxColumn=function(e,t,n){for(var i=[],o=0,r=t.length;o<r;o++){var s=t[o],a=s.viewState.position.lineNumber,u=e.viewModel.getLineMaxColumn(a);i[o]=this._moveToViewPosition(e,s,n,a,u)}return i},e._moveToViewLastNonWhitespaceColumn=function(e,t,n){for(var i=[],o=0,r=t.length;o<r;o++){var s=t[o],a=s.viewState.position.lineNumber,u=e.viewModel.getLineLastNonWhitespaceColumn(a);i[o]=this._moveToViewPosition(e,s,n,a,u)}return i},e}();(function(e){var t=function(e){if(!o["i"](e))return!1;var t=e;return!!o["j"](t.to)&&(!(!o["k"](t.select)&&!o["e"](t.select))&&(!(!o["k"](t.by)&&!o["j"](t.by))&&!(!o["k"](t.value)&&!o["h"](t.value))))};function n(t){if(!t.to)return null;var n;switch(t.to){case e.RawDirection.Left:n=0;break;case e.RawDirection.Right:n=1;break;case e.RawDirection.Up:n=2;break;case e.RawDirection.Down:n=3;break;case e.RawDirection.WrappedLineStart:n=4;break;case e.RawDirection.WrappedLineFirstNonWhitespaceCharacter:n=5;break;case e.RawDirection.WrappedLineColumnCenter:n=6;break;case e.RawDirection.WrappedLineEnd:n=7;break;case e.RawDirection.WrappedLineLastNonWhitespaceCharacter:n=8;break;case e.RawDirection.ViewPortTop:n=9;break;case e.RawDirection.ViewPortBottom:n=11;break;case e.RawDirection.ViewPortCenter:n=10;break;case e.RawDirection.ViewPortIfOutside:n=12;break;default:return null}var i=0;switch(t.by){case e.RawUnit.Line:i=1;break;case e.RawUnit.WrappedLine:i=2;break;case e.RawUnit.Character:i=3;break;case e.RawUnit.HalfLine:i=4;break}return{direction:n,unit:i,select:!!t.select,value:t.value||1}}e.description={description:"Move cursor to a logical position in the view",args:[{name:"Cursor move argument object",description:"Property-value pairs that can be passed through this argument:\n\t\t\t\t\t* 'to': A mandatory logical position value providing where to move the cursor.\n\t\t\t\t\t\t```\n\t\t\t\t\t\t'left', 'right', 'up', 'down'\n\t\t\t\t\t\t'wrappedLineStart', 'wrappedLineEnd', 'wrappedLineColumnCenter'\n\t\t\t\t\t\t'wrappedLineFirstNonWhitespaceCharacter', 'wrappedLineLastNonWhitespaceCharacter'\n\t\t\t\t\t\t'viewPortTop', 'viewPortCenter', 'viewPortBottom', 'viewPortIfOutside'\n\t\t\t\t\t\t```\n\t\t\t\t\t* 'by': Unit to move. Default is computed based on 'to' value.\n\t\t\t\t\t\t```\n\t\t\t\t\t\t'line', 'wrappedLine', 'character', 'halfLine'\n\t\t\t\t\t\t```\n\t\t\t\t\t* 'value': Number of units to move. Default is '1'.\n\t\t\t\t\t* 'select': If 'true' makes the selection. Default is 'false'.\n\t\t\t\t",constraint:t,schema:{type:"object",required:["to"],properties:{to:{type:"string",enum:["left","right","up","down","wrappedLineStart","wrappedLineEnd","wrappedLineColumnCenter","wrappedLineFirstNonWhitespaceCharacter","wrappedLineLastNonWhitespaceCharacter","viewPortTop","viewPortCenter","viewPortBottom","viewPortIfOutside"]},by:{type:"string",enum:["line","wrappedLine","character","halfLine"]},value:{type:"number",default:1},select:{type:"boolean",default:!1}}}}]},e.RawDirection={Left:"left",Right:"right",Up:"up",Down:"down",WrappedLineStart:"wrappedLineStart",WrappedLineFirstNonWhitespaceCharacter:"wrappedLineFirstNonWhitespaceCharacter",WrappedLineColumnCenter:"wrappedLineColumnCenter",WrappedLineEnd:"wrappedLineEnd",WrappedLineLastNonWhitespaceCharacter:"wrappedLineLastNonWhitespaceCharacter",ViewPortTop:"viewPortTop",ViewPortCenter:"viewPortCenter",ViewPortBottom:"viewPortBottom",ViewPortIfOutside:"viewPortIfOutside"},e.RawUnit={Line:"line",WrappedLine:"wrappedLine",Character:"character",HalfLine:"halfLine"},e.parse=n})(i||(i={}))},b272:function(e,t,n){"use strict";n.d(t,"a",(function(){return u}));var i=n("3742"),o=n("2c29"),r=n("2e5d"),s=n("f85a"),a=n("6a89"),u=function(){function e(){}return e.deleteRight=function(e,t,n,i){for(var r=[],u=3!==e,l=0,d=i.length;l<d;l++){var c=i[l],h=c;if(h.isEmpty()){var m=c.getPosition(),f=s["a"].right(t,n,m.lineNumber,m.column);h=new a["a"](f.lineNumber,f.column,m.lineNumber,m.column)}h.isEmpty()?r[l]=null:(h.startLineNumber!==h.endLineNumber&&(u=!0),r[l]=new o["a"](h,""))}return[u,r]},e._isAutoClosingPairDelete=function(e,t,n){if("never"===e.autoClosingBrackets&&"never"===e.autoClosingQuotes)return!1;for(var i=0,o=n.length;i<o;i++){var s=n[i],a=s.getPosition();if(!s.isEmpty())return!1;var u=t.getLineContent(a.lineNumber),l=u[a.column-2],d=e.autoClosingPairsOpen2.get(l);if(!d)return!1;if(Object(r["g"])(l)){if("never"===e.autoClosingQuotes)return!1}else if("never"===e.autoClosingBrackets)return!1;for(var c=u[a.column-1],h=!1,m=0,f=d;m<f.length;m++){var p=f[m];p.open===l&&p.close===c&&(h=!0)}if(!h)return!1}return!0},e._runAutoClosingPairDelete=function(e,t,n){for(var i=[],r=0,s=n.length;r<s;r++){var u=n[r].getPosition(),l=new a["a"](u.lineNumber,u.column-1,u.lineNumber,u.column+1);i[r]=new o["a"](l,"")}return[!0,i]},e.deleteLeft=function(e,t,n,u){if(this._isAutoClosingPairDelete(t,n,u))return this._runAutoClosingPairDelete(t,n,u);for(var l=[],d=2!==e,c=0,h=u.length;c<h;c++){var m=u[c],f=m;if(f.isEmpty()){var p=m.getPosition();if(t.useTabStops&&p.column>1){var g=n.getLineContent(p.lineNumber),w=i["q"](g),v=-1===w?g.length+1:w+1;if(p.column<=v){var b=r["a"].visibleColumnFromColumn2(t,n,p),C=r["a"].prevIndentTabStop(b,t.indentSize),y=r["a"].columnFromVisibleColumn2(t,n,p.lineNumber,C);f=new a["a"](p.lineNumber,y,p.lineNumber,p.column)}else f=new a["a"](p.lineNumber,p.column-1,p.lineNumber,p.column)}else{var S=s["a"].left(t,n,p.lineNumber,p.column);f=new a["a"](S.lineNumber,S.column,p.lineNumber,p.column)}}f.isEmpty()?l[c]=null:(f.startLineNumber!==f.endLineNumber&&(d=!0),l[c]=new o["a"](f,""))}return[d,l]},e.cut=function(e,t,n){for(var i=[],s=0,u=n.length;s<u;s++){var l=n[s];if(l.isEmpty())if(e.emptySelectionClipboard){var d=l.getPosition(),c=void 0,h=void 0,m=void 0,f=void 0;d.lineNumber<t.getLineCount()?(c=d.lineNumber,h=1,m=d.lineNumber+1,f=1):d.lineNumber>1?(c=d.lineNumber-1,h=t.getLineMaxColumn(d.lineNumber-1),m=d.lineNumber,f=t.getLineMaxColumn(d.lineNumber)):(c=d.lineNumber,h=1,m=d.lineNumber,f=t.getLineMaxColumn(d.lineNumber));var p=new a["a"](c,h,m,f);p.isEmpty()?i[s]=null:i[s]=new o["a"](p,"")}else i[s]=null;else i[s]=new o["a"](l,"")}return new r["e"](0,i,{shouldPushStackElementBefore:!0,shouldPushStackElementAfter:!0})},e}()},c101:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var i,o=n("4fc3");(function(e){e.editorSimpleInput=new o["d"]("editorSimpleInput",!1),e.editorTextFocus=new o["d"]("editorTextFocus",!1),e.focus=new o["d"]("editorFocus",!1),e.textInputFocus=new o["d"]("textInputFocus",!1),e.readOnly=new o["d"]("editorReadonly",!1),e.writable=e.readOnly.toNegated(),e.hasNonEmptySelection=new o["d"]("editorHasSelection",!1),e.hasOnlyEmptySelection=e.hasNonEmptySelection.toNegated(),e.hasMultipleSelections=new o["d"]("editorHasMultipleSelections",!1),e.hasSingleSelection=e.hasMultipleSelections.toNegated(),e.tabMovesFocus=new o["d"]("editorTabMovesFocus",!1),e.tabDoesNotMoveFocus=e.tabMovesFocus.toNegated(),e.isInEmbeddedEditor=new o["d"]("isInEmbeddedEditor",!1),e.canUndo=new o["d"]("canUndo",!1),e.canRedo=new o["d"]("canRedo",!1),e.languageId=new o["d"]("editorLangId",""),e.hasCompletionItemProvider=new o["d"]("editorHasCompletionItemProvider",!1),e.hasCodeActionsProvider=new o["d"]("editorHasCodeActionsProvider",!1),e.hasCodeLensProvider=new o["d"]("editorHasCodeLensProvider",!1),e.hasDefinitionProvider=new o["d"]("editorHasDefinitionProvider",!1),e.hasDeclarationProvider=new o["d"]("editorHasDeclarationProvider",!1),e.hasImplementationProvider=new o["d"]("editorHasImplementationProvider",!1),e.hasTypeDefinitionProvider=new o["d"]("editorHasTypeDefinitionProvider",!1),e.hasHoverProvider=new o["d"]("editorHasHoverProvider",!1),e.hasDocumentHighlightProvider=new o["d"]("editorHasDocumentHighlightProvider",!1),e.hasDocumentSymbolProvider=new o["d"]("editorHasDocumentSymbolProvider",!1),e.hasReferenceProvider=new o["d"]("editorHasReferenceProvider",!1),e.hasRenameProvider=new o["d"]("editorHasRenameProvider",!1),e.hasSignatureHelpProvider=new o["d"]("editorHasSignatureHelpProvider",!1),e.hasDocumentFormattingProvider=new o["d"]("editorHasDocumentFormattingProvider",!1),e.hasDocumentSelectionFormattingProvider=new o["d"]("editorHasDocumentSelectionFormattingProvider",!1),e.hasMultipleDocumentFormattingProvider=new o["d"]("editorHasMultipleDocumentFormattingProvider",!1),e.hasMultipleDocumentSelectionFormattingProvider=new o["d"]("editorHasMultipleDocumentSelectionFormattingProvider",!1)})(i||(i={}))},ccde:function(e,t,n){"use strict";n.d(t,"a",(function(){return d}));var i=n("3742"),o=n("2e5d"),r=n("6a89"),s=n("8025"),a=n("70cb"),u=Object.create(null);function l(e,t){u[e]||(u[e]=["",e]);for(var n=u[e],i=n.length;i<=t;i++)n[i]=n[i-1]+e;return n[t]}var d=function(){function e(e,t){this._opts=t,this._selection=e,this._selectionId=null,this._useLastEditRangeForCursorEndPosition=!1,this._selectionStartColumnStaysPut=!1}return e.unshiftIndent=function(e,t,n,i,r){var s=o["a"].visibleColumnFromColumn(e,t,n);if(r){var a=l(" ",i),u=o["a"].prevIndentTabStop(s,i),d=u/i;return l(a,d)}a="\t",u=o["a"].prevRenderTabStop(s,n),d=u/n;return l(a,d)},e.shiftIndent=function(e,t,n,i,r){var s=o["a"].visibleColumnFromColumn(e,t,n);if(r){var a=l(" ",i),u=o["a"].nextIndentTabStop(s,i),d=u/i;return l(a,d)}a="\t",u=o["a"].nextRenderTabStop(s,n),d=u/n;return l(a,d)},e.prototype._addEditOperation=function(e,t,n){this._useLastEditRangeForCursorEndPosition?e.addTrackedEditOperation(t,n):e.addEditOperation(t,n)},e.prototype.getEditOperations=function(t,n){var s=this._selection.startLineNumber,u=this._selection.endLineNumber;1===this._selection.endColumn&&s!==u&&(u-=1);var d=this._opts,c=d.tabSize,h=d.indentSize,m=d.insertSpaces,f=s===u;if(this._selection.isEmpty()&&/^\s*$/.test(t.getLineContent(s))&&(this._useLastEditRangeForCursorEndPosition=!0),this._opts.useTabStops)for(var p=0,g=0,w=s;w<=u;w++,p=g){g=0;var v=t.getLineContent(w),b=i["q"](v);if((!this._opts.isUnshift||0!==v.length&&0!==b)&&(f||this._opts.isUnshift||0!==v.length)){if(-1===b&&(b=v.length),w>1){var C=o["a"].visibleColumnFromColumn(v,b+1,c);if(C%h!==0&&t.isCheapToTokenize(w-1)){var y=a["a"].getEnterAction(this._opts.autoIndent,t,new r["a"](w-1,t.getLineMaxColumn(w-1),w-1,t.getLineMaxColumn(w-1)));if(y){if(g=p,y.appendText)for(var S=0,_=y.appendText.length;S<_&&g<h;S++){if(32!==y.appendText.charCodeAt(S))break;g++}y.removeText&&(g=Math.max(0,g-y.removeText));for(S=0;S<g;S++){if(0===b||32!==v.charCodeAt(b-1))break;b--}}}}if(!this._opts.isUnshift||0!==b){var L=void 0;L=this._opts.isUnshift?e.unshiftIndent(v,b+1,c,h,m):e.shiftIndent(v,b+1,c,h,m),this._addEditOperation(n,new r["a"](w,1,w,b+1),L),w!==s||this._selection.isEmpty()||(this._selectionStartColumnStaysPut=this._selection.startColumn<=b+1)}}}else{var N=m?l(" ",h):"\t";for(w=s;w<=u;w++){v=t.getLineContent(w),b=i["q"](v);if((!this._opts.isUnshift||0!==v.length&&0!==b)&&((f||this._opts.isUnshift||0!==v.length)&&(-1===b&&(b=v.length),!this._opts.isUnshift||0!==b)))if(this._opts.isUnshift){b=Math.min(b,h);for(var k=0;k<b;k++){var O=v.charCodeAt(k);if(9===O){b=k+1;break}}this._addEditOperation(n,new r["a"](w,1,w,b+1),"")}else this._addEditOperation(n,new r["a"](w,1,w,1),N),w!==s||this._selection.isEmpty()||(this._selectionStartColumnStaysPut=1===this._selection.startColumn)}}this._selectionId=n.trackSelection(this._selection)},e.prototype.computeCursorState=function(e,t){if(this._useLastEditRangeForCursorEndPosition){var n=t.getInverseEditOperations()[0];return new s["a"](n.range.endLineNumber,n.range.endColumn,n.range.endLineNumber,n.range.endColumn)}var i=t.getTrackedSelection(this._selectionId);if(this._selectionStartColumnStaysPut){var o=this._selection.startColumn,r=i.startColumn;return r<=o?i:0===i.getDirection()?new s["a"](i.startLineNumber,o,i.endLineNumber,i.endColumn):new s["a"](i.endLineNumber,i.endColumn,i.startLineNumber,o)}return i},e}()},d3f4:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var i=n("6a89"),o=function(){function e(){}return e.insert=function(e,t){return{range:new i["a"](e.lineNumber,e.column,e.lineNumber,e.column),text:t,forceMoveMarkers:!0}},e.delete=function(e){return{range:e,text:null}},e.replace=function(e,t){return{range:e,text:t}},e.replaceMove=function(e,t){return{range:e,text:t,forceMoveMarkers:!0}},e}()},d48d:function(e,t,n){"use strict";n.d(t,"a",(function(){return l})),n.d(t,"b",(function(){return d}));var i=n("3742"),o=n("2e5d"),r=n("e6ff"),s=n("7061"),a=n("6a89"),u=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),l=function(){function e(){}return e._createWord=function(e,t,n,i,o){return{start:i,end:o,wordType:t,nextCharClass:n}},e._findPreviousWordOnLine=function(e,t,n){var i=t.getLineContent(n.lineNumber);return this._doFindPreviousWordOnLine(i,e,n)},e._doFindPreviousWordOnLine=function(e,t,n){for(var i=0,o=n.column-2;o>=0;o--){var r=e.charCodeAt(o),s=t.get(r);if(0===s){if(2===i)return this._createWord(e,i,s,o+1,this._findEndOfWord(e,t,i,o+1));i=1}else if(2===s){if(1===i)return this._createWord(e,i,s,o+1,this._findEndOfWord(e,t,i,o+1));i=2}else if(1===s&&0!==i)return this._createWord(e,i,s,o+1,this._findEndOfWord(e,t,i,o+1))}return 0!==i?this._createWord(e,i,1,0,this._findEndOfWord(e,t,i,0)):null},e._findEndOfWord=function(e,t,n,i){for(var o=e.length,r=i;r<o;r++){var s=e.charCodeAt(r),a=t.get(s);if(1===a)return r;if(1===n&&2===a)return r;if(2===n&&0===a)return r}return o},e._findNextWordOnLine=function(e,t,n){var i=t.getLineContent(n.lineNumber);return this._doFindNextWordOnLine(i,e,n)},e._doFindNextWordOnLine=function(e,t,n){for(var i=0,o=e.length,r=n.column-1;r<o;r++){var s=e.charCodeAt(r),a=t.get(s);if(0===a){if(2===i)return this._createWord(e,i,a,this._findStartOfWord(e,t,i,r-1),r);i=1}else if(2===a){if(1===i)return this._createWord(e,i,a,this._findStartOfWord(e,t,i,r-1),r);i=2}else if(1===a&&0!==i)return this._createWord(e,i,a,this._findStartOfWord(e,t,i,r-1),r)}return 0!==i?this._createWord(e,i,1,this._findStartOfWord(e,t,i,o-1),o):null},e._findStartOfWord=function(e,t,n,i){for(var o=i;o>=0;o--){var r=e.charCodeAt(o),s=t.get(r);if(1===s)return o+1;if(1===n&&2===s)return o+1;if(2===n&&0===s)return o+1}return 0},e.moveWordLeft=function(t,n,i,o){var r=i.lineNumber,a=i.column,u=!1;1===a&&r>1&&(u=!0,r-=1,a=n.getLineMaxColumn(r));var l=e._findPreviousWordOnLine(t,n,new s["a"](r,a));if(0===o){if(l&&!u){var d=n.getLineLastNonWhitespaceColumn(r);if(d<a)return new s["a"](r,l.end+1)}return new s["a"](r,l?l.start+1:1)}if(1===o)return l&&2===l.wordType&&l.end-l.start===1&&0===l.nextCharClass&&(l=e._findPreviousWordOnLine(t,n,new s["a"](r,l.start+1))),new s["a"](r,l?l.start+1:1);if(3===o){while(l&&2===l.wordType)l=e._findPreviousWordOnLine(t,n,new s["a"](r,l.start+1));return new s["a"](r,l?l.start+1:1)}return l&&a<=l.end+1&&(l=e._findPreviousWordOnLine(t,n,new s["a"](r,l.start+1))),new s["a"](r,l?l.end+1:1)},e._moveWordPartLeft=function(e,t){var n=t.lineNumber,o=e.getLineMaxColumn(n);if(1===t.column)return n>1?new s["a"](n-1,e.getLineMaxColumn(n-1)):t;for(var r=e.getLineContent(n),a=t.column-1;a>1;a--){var u=r.charCodeAt(a-2),l=r.charCodeAt(a-1);if(95!==u&&95===l)return new s["a"](n,a);if(i["B"](u)&&i["C"](l))return new s["a"](n,a);if(i["C"](u)&&i["C"](l)&&a+1<o){var d=r.charCodeAt(a);if(i["B"](d))return new s["a"](n,a)}}return new s["a"](n,1)},e.moveWordRight=function(t,n,i,o){var r=i.lineNumber,a=i.column,u=!1;a===n.getLineMaxColumn(r)&&r<n.getLineCount()&&(u=!0,r+=1,a=1);var l=e._findNextWordOnLine(t,n,new s["a"](r,a));if(2===o)l&&2===l.wordType&&l.end-l.start===1&&0===l.nextCharClass&&(l=e._findNextWordOnLine(t,n,new s["a"](r,l.end+1))),a=l?l.end+1:n.getLineMaxColumn(r);else if(3===o){u&&(a=0);while(l&&(2===l.wordType||l.start+1<=a))l=e._findNextWordOnLine(t,n,new s["a"](r,l.end+1));a=l?l.start+1:n.getLineMaxColumn(r)}else l&&!u&&a>=l.start+1&&(l=e._findNextWordOnLine(t,n,new s["a"](r,l.end+1))),a=l?l.start+1:n.getLineMaxColumn(r);return new s["a"](r,a)},e._moveWordPartRight=function(e,t){var n=t.lineNumber,o=e.getLineMaxColumn(n);if(t.column===o)return n<e.getLineCount()?new s["a"](n+1,1):t;for(var r=e.getLineContent(n),a=t.column+1;a<o;a++){var u=r.charCodeAt(a-2),l=r.charCodeAt(a-1);if(95===u&&95!==l)return new s["a"](n,a);if(i["B"](u)&&i["C"](l))return new s["a"](n,a);if(i["C"](u)&&i["C"](l)&&a+1<o){var d=r.charCodeAt(a);if(i["B"](d))return new s["a"](n,a)}}return new s["a"](n,o)},e._deleteWordLeftWhitespace=function(e,t){var n=e.getLineContent(t.lineNumber),o=t.column-2,r=i["D"](n,o);return r+1<o?new a["a"](t.lineNumber,r+2,t.lineNumber,t.column):null},e.deleteWordLeft=function(t,n,i,o,r){if(!i.isEmpty())return i;var u=new s["a"](i.positionLineNumber,i.positionColumn),l=u.lineNumber,d=u.column;if(1===l&&1===d)return null;if(o){var c=this._deleteWordLeftWhitespace(n,u);if(c)return c}var h=e._findPreviousWordOnLine(t,n,u);return 0===r?h?d=h.start+1:d>1?d=1:(l--,d=n.getLineMaxColumn(l)):(h&&d<=h.end+1&&(h=e._findPreviousWordOnLine(t,n,new s["a"](l,h.start+1))),h?d=h.end+1:d>1?d=1:(l--,d=n.getLineMaxColumn(l))),new a["a"](l,d,u.lineNumber,u.column)},e._deleteWordPartLeft=function(t,n){if(!n.isEmpty())return n;var i=n.getPosition(),o=e._moveWordPartLeft(t,i);return new a["a"](i.lineNumber,i.column,o.lineNumber,o.column)},e._findFirstNonWhitespaceChar=function(e,t){for(var n=e.length,i=t;i<n;i++){var o=e.charAt(i);if(" "!==o&&"\t"!==o)return i}return n},e._deleteWordRightWhitespace=function(e,t){var n=e.getLineContent(t.lineNumber),i=t.column-1,o=this._findFirstNonWhitespaceChar(n,i);return i+1<o?new a["a"](t.lineNumber,t.column,t.lineNumber,o+1):null},e.deleteWordRight=function(t,n,i,o,r){if(!i.isEmpty())return i;var u=new s["a"](i.positionLineNumber,i.positionColumn),l=u.lineNumber,d=u.column,c=n.getLineCount(),h=n.getLineMaxColumn(l);if(l===c&&d===h)return null;if(o){var m=this._deleteWordRightWhitespace(n,u);if(m)return m}var f=e._findNextWordOnLine(t,n,u);return 2===r?f?d=f.end+1:d<h||l===c?d=h:(l++,f=e._findNextWordOnLine(t,n,new s["a"](l,1)),d=f?f.start+1:n.getLineMaxColumn(l)):(f&&d>=f.start+1&&(f=e._findNextWordOnLine(t,n,new s["a"](l,f.end+1))),f?d=f.start+1:d<h||l===c?d=h:(l++,f=e._findNextWordOnLine(t,n,new s["a"](l,1)),d=f?f.start+1:n.getLineMaxColumn(l))),new a["a"](l,d,u.lineNumber,u.column)},e._deleteWordPartRight=function(t,n){if(!n.isEmpty())return n;var i=n.getPosition(),o=e._moveWordPartRight(t,i);return new a["a"](i.lineNumber,i.column,o.lineNumber,o.column)},e.word=function(t,n,i,u,l){var d,c,h,m,f=Object(r["a"])(t.wordSeparators),p=e._findPreviousWordOnLine(f,n,l),g=e._findNextWordOnLine(f,n,l);if(!u)return p&&1===p.wordType&&p.start<=l.column-1&&l.column-1<=p.end?(d=p.start+1,c=p.end+1):g&&1===g.wordType&&g.start<=l.column-1&&l.column-1<=g.end?(d=g.start+1,c=g.end+1):(d=p?p.end+1:1,c=g?g.start+1:n.getLineMaxColumn(l.lineNumber)),new o["f"](new a["a"](l.lineNumber,d,l.lineNumber,c),0,new s["a"](l.lineNumber,c),0);p&&1===p.wordType&&p.start<l.column-1&&l.column-1<p.end?(h=p.start+1,m=p.end+1):g&&1===g.wordType&&g.start<l.column-1&&l.column-1<g.end?(h=g.start+1,m=g.end+1):(h=l.column,m=l.column);var w,v=l.lineNumber;if(i.selectionStart.containsPosition(l))w=i.selectionStart.endColumn;else if(l.isBeforeOrEqual(i.selectionStart.getStartPosition())){w=h;var b=new s["a"](v,w);i.selectionStart.containsPosition(b)&&(w=i.selectionStart.endColumn)}else{w=m;b=new s["a"](v,w);i.selectionStart.containsPosition(b)&&(w=i.selectionStart.startColumn)}return i.move(!0,v,w,0)},e}(),d=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return u(t,e),t.deleteWordPartLeft=function(e,t,n,i){var o=c([l.deleteWordLeft(e,t,n,i,0),l.deleteWordLeft(e,t,n,i,2),l._deleteWordPartLeft(t,n)]);return o.sort(a["a"].compareRangesUsingEnds),o[2]},t.deleteWordPartRight=function(e,t,n,i){var o=c([l.deleteWordRight(e,t,n,i,0),l.deleteWordRight(e,t,n,i,2),l._deleteWordPartRight(t,n)]);return o.sort(a["a"].compareRangesUsingStarts),o[0]},t.moveWordPartLeft=function(e,t,n){var i=c([l.moveWordLeft(e,t,n,0),l.moveWordLeft(e,t,n,2),l._moveWordPartLeft(t,n)]);return i.sort(s["a"].compare),i[2]},t.moveWordPartRight=function(e,t,n){var i=c([l.moveWordRight(e,t,n,0),l.moveWordRight(e,t,n,2),l._moveWordPartRight(t,n)]);return i.sort(s["a"].compare),i[0]},t}(l);function c(e){return e.filter((function(e){return Boolean(e)}))}},e1b5:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var i=n("b707"),o=function(){function e(e,t){this._tokens=e,this._tokensCount=this._tokens.length>>>1,this._text=t}return e.prototype.equals=function(t){return t instanceof e&&this.slicedEquals(t,0,this._tokensCount)},e.prototype.slicedEquals=function(e,t,n){if(this._text!==e._text)return!1;if(this._tokensCount!==e._tokensCount)return!1;for(var i=t<<1,o=i+(n<<1),r=i;r<o;r++)if(this._tokens[r]!==e._tokens[r])return!1;return!0},e.prototype.getLineContent=function(){return this._text},e.prototype.getCount=function(){return this._tokensCount},e.prototype.getStartOffset=function(e){return e>0?this._tokens[e-1<<1]:0},e.prototype.getMetadata=function(e){var t=this._tokens[1+(e<<1)];return t},e.prototype.getLanguageId=function(e){var t=this._tokens[1+(e<<1)];return i["A"].getLanguageId(t)},e.prototype.getStandardTokenType=function(e){var t=this._tokens[1+(e<<1)];return i["A"].getTokenType(t)},e.prototype.getForeground=function(e){var t=this._tokens[1+(e<<1)];return i["A"].getForeground(t)},e.prototype.getClassName=function(e){var t=this._tokens[1+(e<<1)];return i["A"].getClassNameFromMetadata(t)},e.prototype.getInlineStyle=function(e,t){var n=this._tokens[1+(e<<1)];return i["A"].getInlineStyleFromMetadata(n,t)},e.prototype.getEndOffset=function(e){return this._tokens[e<<1]},e.prototype.findTokenIndexAtOffset=function(t){return e.findIndexInTokensArray(this._tokens,t)},e.prototype.inflate=function(){return this},e.prototype.sliceAndInflate=function(e,t,n){return new r(this,e,t,n)},e.convertToEndOffset=function(e,t){for(var n=e.length>>>1,i=n-1,o=0;o<i;o++)e[o<<1]=e[o+1<<1];e[i<<1]=t},e.findIndexInTokensArray=function(e,t){if(e.length<=2)return 0;var n=0,i=(e.length>>>1)-1;while(n<i){var o=n+Math.floor((i-n)/2),r=e[o<<1];if(r===t)return o+1;r<t?n=o+1:r>t&&(i=o)}return n},e}(),r=function(){function e(e,t,n,i){this._source=e,this._startOffset=t,this._endOffset=n,this._deltaOffset=i,this._firstTokenIndex=e.findTokenIndexAtOffset(t),this._tokensCount=0;for(var o=this._firstTokenIndex,r=e.getCount();o<r;o++){var s=e.getStartOffset(o);if(s>=n)break;this._tokensCount++}}return e.prototype.equals=function(t){return t instanceof e&&(this._startOffset===t._startOffset&&this._endOffset===t._endOffset&&this._deltaOffset===t._deltaOffset&&this._source.slicedEquals(t._source,this._firstTokenIndex,this._tokensCount))},e.prototype.getCount=function(){return this._tokensCount},e.prototype.getForeground=function(e){return this._source.getForeground(this._firstTokenIndex+e)},e.prototype.getEndOffset=function(e){var t=this._source.getEndOffset(this._firstTokenIndex+e);return Math.min(this._endOffset,t)-this._startOffset+this._deltaOffset},e.prototype.getClassName=function(e){return this._source.getClassName(this._firstTokenIndex+e)},e.prototype.getInlineStyle=function(e,t){return this._source.getInlineStyle(this._firstTokenIndex+e,t)},e.prototype.findTokenIndexAtOffset=function(e){return this._source.findTokenIndexAtOffset(e+this._startOffset-this._deltaOffset)-this._firstTokenIndex},e}()},e6ff:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var i=n("3170"),o=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),r=function(e){function t(t){for(var n=e.call(this,0)||this,i=0,o=t.length;i<o;i++)n.set(t.charCodeAt(i),2);return n.set(32,1),n.set(9,1),n}return o(t,e),t}(i["a"]);function s(e){var t={};return function(n){return t.hasOwnProperty(n)||(t[n]=e(n)),t[n]}}var a=s((function(e){return new r(e)}))},f58f:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var i=function(){function e(e,t,n,i,o,r){this.id=e,this.label=t,this.alias=n,this._precondition=i,this._run=o,this._contextKeyService=r}return e.prototype.isSupported=function(){return this._contextKeyService.contextMatchesRules(this._precondition)},e.prototype.run=function(){if(!this.isSupported())return Promise.resolve(void 0);var e=this._run();return e||Promise.resolve(void 0)},e}()},f85a:function(e,t,n){"use strict";n.d(t,"a",(function(){return u}));var i=n("2e5d"),o=n("7061"),r=n("6a89"),s=n("3742"),a=function(){function e(e,t,n){this.lineNumber=e,this.column=t,this.leftoverVisibleColumns=n}return e}(),u=function(){function e(){}return e.leftPosition=function(e,t,n){return n>e.getLineMinColumn(t)?n-=s["G"](e.getLineContent(t),n-1):t>1&&(t-=1,n=e.getLineMaxColumn(t)),new o["a"](t,n)},e.left=function(t,n,i,o){var r=e.leftPosition(n,i,o);return new a(r.lineNumber,r.column,0)},e.moveLeft=function(t,n,i,o,r){var s,a;if(i.hasSelection()&&!o)s=i.selection.startLineNumber,a=i.selection.startColumn;else{var u=e.left(t,n,i.position.lineNumber,i.position.column-(r-1));s=u.lineNumber,a=u.column}return i.move(o,s,a,0)},e.rightPosition=function(e,t,n){return n<e.getLineMaxColumn(t)?n+=s["E"](e.getLineContent(t),n-1):t<e.getLineCount()&&(t+=1,n=e.getLineMinColumn(t)),new o["a"](t,n)},e.right=function(t,n,i,o){var r=e.rightPosition(n,i,o);return new a(r.lineNumber,r.column,0)},e.moveRight=function(t,n,i,o,r){var s,a;if(i.hasSelection()&&!o)s=i.selection.endLineNumber,a=i.selection.endColumn;else{var u=e.right(t,n,i.position.lineNumber,i.position.column+(r-1));s=u.lineNumber,a=u.column}return i.move(o,s,a,0)},e.down=function(e,t,n,o,r,s,u){var l=i["a"].visibleColumnFromColumn(t.getLineContent(n),o,e.tabSize)+r;n+=s;var d=t.getLineCount();return n>d?(n=d,o=u?t.getLineMaxColumn(n):Math.min(t.getLineMaxColumn(n),o)):o=i["a"].columnFromVisibleColumn2(e,t,n,l),r=l-i["a"].visibleColumnFromColumn(t.getLineContent(n),o,e.tabSize),new a(n,o,r)},e.moveDown=function(t,n,i,o,r){var s,a;i.hasSelection()&&!o?(s=i.selection.endLineNumber,a=i.selection.endColumn):(s=i.position.lineNumber,a=i.position.column);var u=e.down(t,n,s,a,i.leftoverVisibleColumns,r,!0);return i.move(o,u.lineNumber,u.column,u.leftoverVisibleColumns)},e.translateDown=function(t,n,s){var a=s.selection,u=e.down(t,n,a.selectionStartLineNumber,a.selectionStartColumn,s.selectionStartLeftoverVisibleColumns,1,!1),l=e.down(t,n,a.positionLineNumber,a.positionColumn,s.leftoverVisibleColumns,1,!1);return new i["f"](new r["a"](u.lineNumber,u.column,u.lineNumber,u.column),u.leftoverVisibleColumns,new o["a"](l.lineNumber,l.column),l.leftoverVisibleColumns)},e.up=function(e,t,n,o,r,s,u){var l=i["a"].visibleColumnFromColumn(t.getLineContent(n),o,e.tabSize)+r;return n-=s,n<1?(n=1,o=u?t.getLineMinColumn(n):Math.min(t.getLineMaxColumn(n),o)):o=i["a"].columnFromVisibleColumn2(e,t,n,l),r=l-i["a"].visibleColumnFromColumn(t.getLineContent(n),o,e.tabSize),new a(n,o,r)},e.moveUp=function(t,n,i,o,r){var s,a;i.hasSelection()&&!o?(s=i.selection.startLineNumber,a=i.selection.startColumn):(s=i.position.lineNumber,a=i.position.column);var u=e.up(t,n,s,a,i.leftoverVisibleColumns,r,!0);return i.move(o,u.lineNumber,u.column,u.leftoverVisibleColumns)},e.translateUp=function(t,n,s){var a=s.selection,u=e.up(t,n,a.selectionStartLineNumber,a.selectionStartColumn,s.selectionStartLeftoverVisibleColumns,1,!1),l=e.up(t,n,a.positionLineNumber,a.positionColumn,s.leftoverVisibleColumns,1,!1);return new i["f"](new r["a"](u.lineNumber,u.column,u.lineNumber,u.column),u.leftoverVisibleColumns,new o["a"](l.lineNumber,l.column),l.leftoverVisibleColumns)},e.moveToBeginningOfLine=function(e,t,n,i){var o,r=n.position.lineNumber,s=t.getLineMinColumn(r),a=t.getLineFirstNonWhitespaceColumn(r)||s,u=n.position.column;return o=u===a?s:a,n.move(i,r,o,0)},e.moveToEndOfLine=function(e,t,n,i){var o=n.position.lineNumber,r=t.getLineMaxColumn(o);return n.move(i,o,r,0)},e.moveToBeginningOfBuffer=function(e,t,n,i){return n.move(i,1,1,0)},e.moveToEndOfBuffer=function(e,t,n,i){var o=t.getLineCount(),r=t.getLineMaxColumn(o);return n.move(i,o,r,0)},e}()},fb71:function(e,t,n){"use strict";n.d(t,"a",(function(){return u})),n.d(t,"b",(function(){return l}));var i=n("30db"),o=n("62bd"),r=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),s=i["e"]?1.5:1.35,a=8,u=function(){function e(e){this.zoomLevel=e.zoomLevel,this.fontFamily=String(e.fontFamily),this.fontWeight=String(e.fontWeight),this.fontSize=e.fontSize,this.fontFeatureSettings=e.fontFeatureSettings,this.lineHeight=0|e.lineHeight,this.letterSpacing=e.letterSpacing}return e.createFromValidatedSettings=function(t,n,i){var o=t.get(33),r=t.get(37),s=t.get(36),a=t.get(35),u=t.get(49),l=t.get(46);return e._create(o,r,s,a,u,l,n,i)},e._create=function(t,n,i,r,u,l,d,c){0===u?u=Math.round(s*i):u<a&&(u=a);var h=1+(c?0:.1*o["a"].getZoomLevel());return i*=h,u*=h,new e({zoomLevel:d,fontFamily:t,fontWeight:n,fontSize:i,fontFeatureSettings:r,lineHeight:u,letterSpacing:l})},e.prototype.getId=function(){return this.zoomLevel+"-"+this.fontFamily+"-"+this.fontWeight+"-"+this.fontSize+"-"+this.fontFeatureSettings+"-"+this.lineHeight+"-"+this.letterSpacing},e.prototype.getMassagedFontFamily=function(){return/[,"']/.test(this.fontFamily)?this.fontFamily:/[+ ]/.test(this.fontFamily)?'"'+this.fontFamily+'"':this.fontFamily},e}(),l=function(e){function t(t,n){var i=e.call(this,t)||this;return i.isTrusted=n,i.isMonospace=t.isMonospace,i.typicalHalfwidthCharacterWidth=t.typicalHalfwidthCharacterWidth,i.typicalFullwidthCharacterWidth=t.typicalFullwidthCharacterWidth,i.canUseHalfwidthRightwardsArrow=t.canUseHalfwidthRightwardsArrow,i.spaceWidth=t.spaceWidth,i.middotWidth=t.middotWidth,i.maxDigitWidth=t.maxDigitWidth,i}return r(t,e),t.prototype.equals=function(e){return this.fontFamily===e.fontFamily&&this.fontWeight===e.fontWeight&&this.fontSize===e.fontSize&&this.fontFeatureSettings===e.fontFeatureSettings&&this.lineHeight===e.lineHeight&&this.letterSpacing===e.letterSpacing&&this.typicalHalfwidthCharacterWidth===e.typicalHalfwidthCharacterWidth&&this.typicalFullwidthCharacterWidth===e.typicalFullwidthCharacterWidth&&this.canUseHalfwidthRightwardsArrow===e.canUseHalfwidthRightwardsArrow&&this.spaceWidth===e.spaceWidth&&this.middotWidth===e.middotWidth&&this.maxDigitWidth===e.maxDigitWidth},t}(u)},fd49:function(e,t,n){"use strict";n.d(t,"f",(function(){return u})),n.d(t,"a",(function(){return l})),n.d(t,"h",(function(){return d})),n.d(t,"g",(function(){return y})),n.d(t,"d",(function(){return P})),n.d(t,"j",(function(){return q})),n.d(t,"b",(function(){return ee})),n.d(t,"c",(function(){return te})),n.d(t,"i",(function(){return ne})),n.d(t,"e",(function(){return oe}));var i=n("dff7"),o=n("30db"),r=n("d093"),s=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),a=function(){return a=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var o in t=arguments[n],t)Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},a.apply(this,arguments)},u=8,l=function(){function e(e){this._values=e}return e.prototype.hasChanged=function(e){return this._values[e]},e}(),d=function(){function e(){this._values=[]}return e.prototype._read=function(e){return this._values[e]},e.prototype.get=function(e){return this._values[e]},e.prototype._write=function(e,t){this._values[e]=t},e}(),c=function(){function e(e,t,n,i){this.id=e,this.name=t,this.defaultValue=n,this.schema=i}return e.prototype.compute=function(e,t,n){return n},e}(),h=function(){function e(e,t){void 0===t&&(t=null),this.schema=void 0,this.id=e,this.name="_never_",this.defaultValue=void 0,this.deps=t}return e.prototype.validate=function(e){return this.defaultValue},e}(),m=function(){function e(e,t,n,i){this.id=e,this.name=t,this.defaultValue=n,this.schema=i}return e.prototype.validate=function(e){return"undefined"===typeof e?this.defaultValue:e},e.prototype.compute=function(e,t,n){return n},e}(),f=function(e){function t(t,n,i,o){void 0===o&&(o=void 0);var r=this;return"undefined"!==typeof o&&(o.type="boolean",o.default=i),r=e.call(this,t,n,i,o)||this,r}return s(t,e),t.boolean=function(e,t){return"undefined"===typeof e?t:"false"!==e&&Boolean(e)},t.prototype.validate=function(e){return t.boolean(e,this.defaultValue)},t}(m),p=function(e){function t(t,n,i,o,r,s){void 0===s&&(s=void 0);var a=this;return"undefined"!==typeof s&&(s.type="integer",s.default=i,s.minimum=o,s.maximum=r),a=e.call(this,t,n,i,s)||this,a.minimum=o,a.maximum=r,a}return s(t,e),t.clampedInt=function(e,t,n,i){var o;return"undefined"===typeof e?o=t:(o=parseInt(e,10),isNaN(o)&&(o=t)),o=Math.max(n,o),o=Math.min(i,o),0|o},t.prototype.validate=function(e){return t.clampedInt(e,this.defaultValue,this.minimum,this.maximum)},t}(m),g=function(e){function t(t,n,i,o,r){var s=this;return"undefined"!==typeof r&&(r.type="number",r.default=i),s=e.call(this,t,n,i,r)||this,s.validationFn=o,s}return s(t,e),t.clamp=function(e,t,n){return e<t?t:e>n?n:e},t.float=function(e,t){if("number"===typeof e)return e;if("undefined"===typeof e)return t;var n=parseFloat(e);return isNaN(n)?t:n},t.prototype.validate=function(e){return this.validationFn(t.float(e,this.defaultValue))},t}(m),w=function(e){function t(t,n,i,o){void 0===o&&(o=void 0);var r=this;return"undefined"!==typeof o&&(o.type="string",o.default=i),r=e.call(this,t,n,i,o)||this,r}return s(t,e),t.string=function(e,t){return"string"!==typeof e?t:e},t.prototype.validate=function(e){return t.string(e,this.defaultValue)},t}(m),v=function(e){function t(t,n,i,o,r){void 0===r&&(r=void 0);var s=this;return"undefined"!==typeof r&&(r.type="string",r.enum=o,r.default=i),s=e.call(this,t,n,i,r)||this,s._allowedValues=o,s}return s(t,e),t.stringSet=function(e,t,n){return"string"!==typeof e||-1===n.indexOf(e)?t:e},t.prototype.validate=function(e){return t.stringSet(e,this.defaultValue,this._allowedValues)},t}(m),b=function(e){function t(t,n,i,o,r,s,a){void 0===a&&(a=void 0);var u=this;return"undefined"!==typeof a&&(a.type="string",a.enum=r,a.default=o),u=e.call(this,t,n,i,a)||this,u._allowedValues=r,u._convert=s,u}return s(t,e),t.prototype.validate=function(e){return"string"!==typeof e||-1===this._allowedValues.indexOf(e)?this.defaultValue:this._convert(e)},t}(c);function C(e){switch(e){case"none":return 0;case"keep":return 1;case"brackets":return 2;case"advanced":return 3;case"full":return 4}}var y,S=function(e){function t(){return e.call(this,2,"accessibilitySupport",0,{type:"string",enum:["auto","on","off"],enumDescriptions:[i["a"]("accessibilitySupport.auto","The editor will use platform APIs to detect when a Screen Reader is attached."),i["a"]("accessibilitySupport.on","The editor will be permanently optimized for usage with a Screen Reader."),i["a"]("accessibilitySupport.off","The editor will never be optimized for usage with a Screen Reader.")],default:"auto",description:i["a"]("accessibilitySupport","Controls whether the editor should run in a mode where it is optimized for screen readers.")})||this}return s(t,e),t.prototype.validate=function(e){switch(e){case"auto":return 0;case"off":return 1;case"on":return 2}return this.defaultValue},t.prototype.compute=function(e,t,n){return 0===n?e.accessibilitySupport:n},t}(c),_=function(e){function t(){var t=this,n={insertSpace:!0};return t=e.call(this,13,"comments",n,{"editor.comments.insertSpace":{type:"boolean",default:n.insertSpace,description:i["a"]("comments.insertSpace","Controls whether a space character is inserted when commenting.")}})||this,t}return s(t,e),t.prototype.validate=function(e){if("object"!==typeof e)return this.defaultValue;var t=e;return{insertSpace:f.boolean(t.insertSpace,this.defaultValue.insertSpace)}},t}(c);function L(e){switch(e){case"blink":return 1;case"smooth":return 2;case"phase":return 3;case"expand":return 4;case"solid":return 5}}function N(e){switch(e){case"line":return y.Line;case"block":return y.Block;case"underline":return y.Underline;case"line-thin":return y.LineThin;case"block-outline":return y.BlockOutline;case"underline-thin":return y.UnderlineThin}}(function(e){e[e["Line"]=1]="Line",e[e["Block"]=2]="Block",e[e["Underline"]=3]="Underline",e[e["LineThin"]=4]="LineThin",e[e["BlockOutline"]=5]="BlockOutline",e[e["UnderlineThin"]=6]="UnderlineThin"})(y||(y={}));var k=function(e){function t(){return e.call(this,104,[55,26])||this}return s(t,e),t.prototype.compute=function(e,t,n){var i="monaco-editor";return t.get(26)&&(i+=" "+t.get(26)),e.extraEditorClassName&&(i+=" "+e.extraEditorClassName),"default"===t.get(55)?i+=" mouse-default":"copy"===t.get(55)&&(i+=" mouse-copy"),t.get(85)&&(i+=" showUnused"),i},t}(h),O=function(e){function t(){return e.call(this,25,"emptySelectionClipboard",!0,{description:i["a"]("emptySelectionClipboard","Controls whether copying without a selection copies the current line.")})||this}return s(t,e),t.prototype.compute=function(e,t,n){return n&&e.emptySelectionClipboard},t}(f),E=function(e){function t(){var t=this,n={seedSearchStringFromSelection:!0,autoFindInSelection:"never",globalFindClipboard:!1,addExtraSpaceOnTop:!0};return t=e.call(this,28,"find",n,{"editor.find.seedSearchStringFromSelection":{type:"boolean",default:n.seedSearchStringFromSelection,description:i["a"]("find.seedSearchStringFromSelection","Controls whether the search string in the Find Widget is seeded from the editor selection.")},"editor.find.autoFindInSelection":{type:"string",enum:["never","always","multiline"],default:n.autoFindInSelection,enumDescriptions:[i["a"]("editor.find.autoFindInSelection.never","Never turn on Find in selection automatically (default)"),i["a"]("editor.find.autoFindInSelection.always","Always turn on Find in selection automatically"),i["a"]("editor.find.autoFindInSelection.multiline","Turn on Find in selection automatically when multiple lines of content are selected.")],description:i["a"]("find.autoFindInSelection","Controls whether the find operation is carried out on selected text or the entire file in the editor.")},"editor.find.globalFindClipboard":{type:"boolean",default:n.globalFindClipboard,description:i["a"]("find.globalFindClipboard","Controls whether the Find Widget should read or modify the shared find clipboard on macOS."),included:o["e"]},"editor.find.addExtraSpaceOnTop":{type:"boolean",default:n.addExtraSpaceOnTop,description:i["a"]("find.addExtraSpaceOnTop","Controls whether the Find Widget should add extra lines on top of the editor. When true, you can scroll beyond the first line when the Find Widget is visible.")}})||this,t}return s(t,e),t.prototype.validate=function(e){if("object"!==typeof e)return this.defaultValue;var t=e;return{seedSearchStringFromSelection:f.boolean(t.seedSearchStringFromSelection,this.defaultValue.seedSearchStringFromSelection),autoFindInSelection:"boolean"===typeof e.autoFindInSelection?e.autoFindInSelection?"always":"never":v.stringSet(t.autoFindInSelection,this.defaultValue.autoFindInSelection,["never","always","multiline"]),globalFindClipboard:f.boolean(t.globalFindClipboard,this.defaultValue.globalFindClipboard),addExtraSpaceOnTop:f.boolean(t.addExtraSpaceOnTop,this.defaultValue.addExtraSpaceOnTop)}},t}(c),P=function(e){function t(){return e.call(this,35,"fontLigatures",t.OFF,{anyOf:[{type:"boolean",description:i["a"]("fontLigatures","Enables/Disables font ligatures.")},{type:"string",description:i["a"]("fontFeatureSettings","Explicit font-feature-settings.")}],description:i["a"]("fontLigaturesGeneral","Configures font ligatures."),default:!1})||this}return s(t,e),t.prototype.validate=function(e){return"undefined"===typeof e?this.defaultValue:"string"===typeof e?"false"===e?t.OFF:"true"===e?t.ON:e:Boolean(e)?t.ON:t.OFF},t.OFF='"liga" off, "calt" off',t.ON='"liga" on, "calt" on',t}(c),I=function(e){function t(){return e.call(this,34)||this}return s(t,e),t.prototype.compute=function(e,t,n){return e.fontInfo},t}(h),M=function(e){function t(){return e.call(this,36,"fontSize",ee.fontSize,{type:"number",minimum:6,maximum:100,default:ee.fontSize,description:i["a"]("fontSize","Controls the font size in pixels.")})||this}return s(t,e),t.prototype.validate=function(e){var t=g.float(e,this.defaultValue);return 0===t?ee.fontSize:g.clamp(t,6,100)},t.prototype.compute=function(e,t,n){return e.fontInfo.fontSize},t}(m),T=function(e){function t(){var t=this,n={multiple:"peek",multipleDefinitions:"peek",multipleTypeDefinitions:"peek",multipleDeclarations:"peek",multipleImplementations:"peek",multipleReferences:"peek",alternativeDefinitionCommand:"editor.action.goToReferences",alternativeTypeDefinitionCommand:"editor.action.goToReferences",alternativeDeclarationCommand:"editor.action.goToReferences",alternativeImplementationCommand:"",alternativeReferenceCommand:""},o={type:"string",enum:["peek","gotoAndPeek","goto"],default:n.multiple,enumDescriptions:[i["a"]("editor.gotoLocation.multiple.peek","Show peek view of the results (default)"),i["a"]("editor.gotoLocation.multiple.gotoAndPeek","Go to the primary result and show a peek view"),i["a"]("editor.gotoLocation.multiple.goto","Go to the primary result and enable peek-less navigation to others")]};return t=e.call(this,41,"gotoLocation",n,{"editor.gotoLocation.multiple":{deprecationMessage:i["a"]("editor.gotoLocation.multiple.deprecated","This setting is deprecated, please use separate settings like 'editor.editor.gotoLocation.multipleDefinitions' or 'editor.editor.gotoLocation.multipleImplementations' instead.")},"editor.gotoLocation.multipleDefinitions":a({description:i["a"]("editor.editor.gotoLocation.multipleDefinitions","Controls the behavior the 'Go to Definition'-command when multiple target locations exist.")},o),"editor.gotoLocation.multipleTypeDefinitions":a({description:i["a"]("editor.editor.gotoLocation.multipleTypeDefinitions","Controls the behavior the 'Go to Type Definition'-command when multiple target locations exist.")},o),"editor.gotoLocation.multipleDeclarations":a({description:i["a"]("editor.editor.gotoLocation.multipleDeclarations","Controls the behavior the 'Go to Declaration'-command when multiple target locations exist.")},o),"editor.gotoLocation.multipleImplementations":a({description:i["a"]("editor.editor.gotoLocation.multipleImplemenattions","Controls the behavior the 'Go to Implementations'-command when multiple target locations exist.")},o),"editor.gotoLocation.multipleReferences":a({description:i["a"]("editor.editor.gotoLocation.multipleReferences","Controls the behavior the 'Go to References'-command when multiple target locations exist.")},o),"editor.gotoLocation.alternativeDefinitionCommand":{type:"string",default:n.alternativeDefinitionCommand,description:i["a"]("alternativeDefinitionCommand","Alternative command id that is being executed when the result of 'Go to Definition' is the current location.")},"editor.gotoLocation.alternativeTypeDefinitionCommand":{type:"string",default:n.alternativeTypeDefinitionCommand,description:i["a"]("alternativeTypeDefinitionCommand","Alternative command id that is being executed when the result of 'Go to Type Definition' is the current location.")},"editor.gotoLocation.alternativeDeclarationCommand":{type:"string",default:n.alternativeDeclarationCommand,description:i["a"]("alternativeDeclarationCommand","Alternative command id that is being executed when the result of 'Go to Declaration' is the current location.")},"editor.gotoLocation.alternativeImplementationCommand":{type:"string",default:n.alternativeImplementationCommand,description:i["a"]("alternativeImplementationCommand","Alternative command id that is being executed when the result of 'Go to Implementation' is the current location.")},"editor.gotoLocation.alternativeReferenceCommand":{type:"string",default:n.alternativeReferenceCommand,description:i["a"]("alternativeReferenceCommand","Alternative command id that is being executed when the result of 'Go to Reference' is the current location.")}})||this,t}return s(t,e),t.prototype.validate=function(e){var t,n,i,o,r;if("object"!==typeof e)return this.defaultValue;var s=e;return{multiple:v.stringSet(s.multiple,this.defaultValue.multiple,["peek","gotoAndPeek","goto"]),multipleDefinitions:null!==(t=s.multipleDefinitions)&&void 0!==t?t:v.stringSet(s.multipleDefinitions,"peek",["peek","gotoAndPeek","goto"]),multipleTypeDefinitions:null!==(n=s.multipleTypeDefinitions)&&void 0!==n?n:v.stringSet(s.multipleTypeDefinitions,"peek",["peek","gotoAndPeek","goto"]),multipleDeclarations:null!==(i=s.multipleDeclarations)&&void 0!==i?i:v.stringSet(s.multipleDeclarations,"peek",["peek","gotoAndPeek","goto"]),multipleImplementations:null!==(o=s.multipleImplementations)&&void 0!==o?o:v.stringSet(s.multipleImplementations,"peek",["peek","gotoAndPeek","goto"]),multipleReferences:null!==(r=s.multipleReferences)&&void 0!==r?r:v.stringSet(s.multipleReferences,"peek",["peek","gotoAndPeek","goto"]),alternativeDefinitionCommand:w.string(s.alternativeDefinitionCommand,this.defaultValue.alternativeDefinitionCommand),alternativeTypeDefinitionCommand:w.string(s.alternativeTypeDefinitionCommand,this.defaultValue.alternativeTypeDefinitionCommand),alternativeDeclarationCommand:w.string(s.alternativeDeclarationCommand,this.defaultValue.alternativeDeclarationCommand),alternativeImplementationCommand:w.string(s.alternativeImplementationCommand,this.defaultValue.alternativeImplementationCommand),alternativeReferenceCommand:w.string(s.alternativeReferenceCommand,this.defaultValue.alternativeReferenceCommand)}},t}(c),x=function(e){function t(){var t=this,n={enabled:!0,delay:300,sticky:!0};return t=e.call(this,44,"hover",n,{"editor.hover.enabled":{type:"boolean",default:n.enabled,description:i["a"]("hover.enabled","Controls whether the hover is shown.")},"editor.hover.delay":{type:"number",default:n.delay,description:i["a"]("hover.delay","Controls the delay in milliseconds after which the hover is shown.")},"editor.hover.sticky":{type:"boolean",default:n.sticky,description:i["a"]("hover.sticky","Controls whether the hover should remain visible when mouse is moved over it.")}})||this,t}return s(t,e),t.prototype.validate=function(e){if("object"!==typeof e)return this.defaultValue;var t=e;return{enabled:f.boolean(t.enabled,this.defaultValue.enabled),delay:p.clampedInt(t.delay,this.defaultValue.delay,0,1e4),sticky:f.boolean(t.sticky,this.defaultValue.sticky)}},t}(c),W=function(e){function t(){return e.call(this,107,[40,48,30,54,78,50])||this}return s(t,e),t.prototype.compute=function(e,n,i){return t.computeLayout(n,{outerWidth:e.outerWidth,outerHeight:e.outerHeight,lineHeight:e.fontInfo.lineHeight,lineNumbersDigitCount:e.lineNumbersDigitCount,typicalHalfwidthCharacterWidth:e.fontInfo.typicalHalfwidthCharacterWidth,maxDigitWidth:e.fontInfo.maxDigitWidth,pixelRatio:e.pixelRatio})},t.computeLayout=function(e,t){var n,i=0|t.outerWidth,o=0|t.outerHeight,r=0|t.lineHeight,s=0|t.lineNumbersDigitCount,a=t.typicalHalfwidthCharacterWidth,l=t.maxDigitWidth,d=t.pixelRatio,c=e.get(40),h=0!==e.get(50).renderType,m=0|e.get(51),f=e.get(54),g=f.enabled,w=f.side,v=f.renderCharacters,b=d>=2?Math.round(2*f.scale):f.scale,C=0|f.maxColumn,y=e.get(78),S=0|y.verticalScrollbarSize,_=y.verticalHasArrows,L=0|y.arrowSize,N=0|y.horizontalScrollbarSize,k=e.get(48),O=e.get(30);if("string"===typeof k&&/^\d+(\.\d+)?ch$/.test(k)){var E=parseFloat(k.substr(0,k.length-2));n=p.clampedInt(E*a,0,0,1e3)}else n=p.clampedInt(k,0,0,1e3);O&&(n+=16);var P=0;if(h){var I=Math.max(s,m);P=Math.round(I*l)}var M=0;c&&(M=r);var T,x,W,V,D=0,R=D+M,A=R+P,F=A+n,z=i-M-P-n;if(g){var B=b/d;T=v?1:2,W=Math.max(0,Math.floor((z-S-2)*B/(a+B)))+u;var H=W/B;H>C&&(W=Math.floor(C*B)),V=z-W,"left"===w?(x=0,D+=W,R+=W,A+=W,F+=W):x=i-W-S}else x=0,W=0,T=0,V=z;var q=Math.max(1,Math.floor((V-S-2)/a)),U=_?L:0;return{width:i,height:o,glyphMarginLeft:D,glyphMarginWidth:M,lineNumbersLeft:R,lineNumbersWidth:P,decorationsLeft:A,decorationsWidth:n,contentLeft:F,contentWidth:V,renderMinimap:T,minimapLeft:x,minimapWidth:W,viewportColumn:q,verticalScrollbarWidth:S,horizontalScrollbarHeight:N,overviewRuler:{top:U,width:S,height:o-2*U,right:0}}},t}(h),V=function(e){function t(){var t=this,n={enabled:!0};return t=e.call(this,47,"lightbulb",n,{"editor.lightbulb.enabled":{type:"boolean",default:n.enabled,description:i["a"]("codeActions","Enables the code action lightbulb in the editor.")}})||this,t}return s(t,e),t.prototype.validate=function(e){if("object"!==typeof e)return this.defaultValue;var t=e;return{enabled:f.boolean(t.enabled,this.defaultValue.enabled)}},t}(c),D=function(e){function t(){return e.call(this,49,"lineHeight",ee.lineHeight,0,150,{description:i["a"]("lineHeight","Controls the line height. Use 0 to compute the line height from the font size.")})||this}return s(t,e),t.prototype.compute=function(e,t,n){return e.fontInfo.lineHeight},t}(p),R=function(e){function t(){var t=this,n={enabled:!0,side:"right",showSlider:"mouseover",renderCharacters:!0,maxColumn:120,scale:1};return t=e.call(this,54,"minimap",n,{"editor.minimap.enabled":{type:"boolean",default:n.enabled,description:i["a"]("minimap.enabled","Controls whether the minimap is shown.")},"editor.minimap.side":{type:"string",enum:["left","right"],default:n.side,description:i["a"]("minimap.side","Controls the side where to render the minimap.")},"editor.minimap.showSlider":{type:"string",enum:["always","mouseover"],default:n.showSlider,description:i["a"]("minimap.showSlider","Controls when the minimap slider is shown.")},"editor.minimap.scale":{type:"number",default:n.scale,minimum:1,maximum:3,description:i["a"]("minimap.scale","Scale of content drawn in the minimap.")},"editor.minimap.renderCharacters":{type:"boolean",default:n.renderCharacters,description:i["a"]("minimap.renderCharacters","Render the actual characters on a line as opposed to color blocks.")},"editor.minimap.maxColumn":{type:"number",default:n.maxColumn,description:i["a"]("minimap.maxColumn","Limit the width of the minimap to render at most a certain number of columns.")}})||this,t}return s(t,e),t.prototype.validate=function(e){if("object"!==typeof e)return this.defaultValue;var t=e;return{enabled:f.boolean(t.enabled,this.defaultValue.enabled),side:v.stringSet(t.side,this.defaultValue.side,["right","left"]),showSlider:v.stringSet(t.showSlider,this.defaultValue.showSlider,["always","mouseover"]),renderCharacters:f.boolean(t.renderCharacters,this.defaultValue.renderCharacters),scale:p.clampedInt(t.scale,1,1,3),maxColumn:p.clampedInt(t.maxColumn,this.defaultValue.maxColumn,1,1e4)}},t}(c);function A(e){return"ctrlCmd"===e?o["e"]?"metaKey":"ctrlKey":"altKey"}var F=function(e){function t(){var t=this,n={enabled:!0,cycle:!1};return t=e.call(this,64,"parameterHints",n,{"editor.parameterHints.enabled":{type:"boolean",default:n.enabled,description:i["a"]("parameterHints.enabled","Enables a pop-up that shows parameter documentation and type information as you type.")},"editor.parameterHints.cycle":{type:"boolean",default:n.cycle,description:i["a"]("parameterHints.cycle","Controls whether the parameter hints menu cycles or closes when reaching the end of the list.")}})||this,t}return s(t,e),t.prototype.validate=function(e){if("object"!==typeof e)return this.defaultValue;var t=e;return{enabled:f.boolean(t.enabled,this.defaultValue.enabled),cycle:f.boolean(t.cycle,this.defaultValue.cycle)}},t}(c),z=function(e){function t(){return e.call(this,105)||this}return s(t,e),t.prototype.compute=function(e,t,n){return e.pixelRatio},t}(h),B=function(e){function t(){var t=this,n={other:!0,comments:!1,strings:!1};return t=e.call(this,66,"quickSuggestions",n,{anyOf:[{type:"boolean"},{type:"object",properties:{strings:{type:"boolean",default:n.strings,description:i["a"]("quickSuggestions.strings","Enable quick suggestions inside strings.")},comments:{type:"boolean",default:n.comments,description:i["a"]("quickSuggestions.comments","Enable quick suggestions inside comments.")},other:{type:"boolean",default:n.other,description:i["a"]("quickSuggestions.other","Enable quick suggestions outside of strings and comments.")}}}],default:n,description:i["a"]("quickSuggestions","Controls whether suggestions should automatically show up while typing.")})||this,t.defaultValue=n,t}return s(t,e),t.prototype.validate=function(e){if("boolean"===typeof e)return e;if("object"===typeof e){var t=e,n={other:f.boolean(t.other,this.defaultValue.other),comments:f.boolean(t.comments,this.defaultValue.comments),strings:f.boolean(t.strings,this.defaultValue.strings)};return!!(n.other&&n.comments&&n.strings)||!!(n.other||n.comments||n.strings)&&n}return this.defaultValue},t}(c),H=function(e){function t(){return e.call(this,50,"lineNumbers",{renderType:1,renderFn:null},{type:"string",enum:["off","on","relative","interval"],enumDescriptions:[i["a"]("lineNumbers.off","Line numbers are not rendered."),i["a"]("lineNumbers.on","Line numbers are rendered as absolute number."),i["a"]("lineNumbers.relative","Line numbers are rendered as distance in lines to cursor position."),i["a"]("lineNumbers.interval","Line numbers are rendered every 10 lines.")],default:"on",description:i["a"]("lineNumbers","Controls the display of line numbers.")})||this}return s(t,e),t.prototype.validate=function(e){var t=this.defaultValue.renderType,n=this.defaultValue.renderFn;return"undefined"!==typeof e&&("function"===typeof e?(t=4,n=e):t="interval"===e?3:"relative"===e?2:"on"===e?1:0),{renderType:t,renderFn:n}},t}(c);function q(e){var t=e.get(73);return"editable"===t?e.get(68):"on"!==t}var U=function(e){function t(){var t=this,n=[];return t=e.call(this,77,"rulers",n,{type:"array",items:{type:"number"},default:n,description:i["a"]("rulers","Render vertical rulers after a certain number of monospace characters. Use multiple values for multiple rulers. No rulers are drawn if array is empty.")})||this,t}return s(t,e),t.prototype.validate=function(e){if(Array.isArray(e)){for(var t=[],n=0,i=e;n<i.length;n++){var o=i[n];t.push(p.clampedInt(o,0,0,1e4))}return t.sort((function(e,t){return e-t})),t}return this.defaultValue},t}(m);function j(e,t){if("string"!==typeof e)return t;switch(e){case"hidden":return 2;case"visible":return 3;default:return 1}}var G=function(e){function t(){return e.call(this,78,"scrollbar",{vertical:1,horizontal:1,arrowSize:11,useShadows:!0,verticalHasArrows:!1,horizontalHasArrows:!1,horizontalScrollbarSize:10,horizontalSliderSize:10,verticalScrollbarSize:14,verticalSliderSize:14,handleMouseWheel:!0,alwaysConsumeMouseWheel:!0})||this}return s(t,e),t.prototype.validate=function(e){if("object"!==typeof e)return this.defaultValue;var t=e,n=p.clampedInt(t.horizontalScrollbarSize,this.defaultValue.horizontalScrollbarSize,0,1e3),i=p.clampedInt(t.verticalScrollbarSize,this.defaultValue.verticalScrollbarSize,0,1e3);return{arrowSize:p.clampedInt(t.arrowSize,this.defaultValue.arrowSize,0,1e3),vertical:j(t.vertical,this.defaultValue.vertical),horizontal:j(t.horizontal,this.defaultValue.horizontal),useShadows:f.boolean(t.useShadows,this.defaultValue.useShadows),verticalHasArrows:f.boolean(t.verticalHasArrows,this.defaultValue.verticalHasArrows),horizontalHasArrows:f.boolean(t.horizontalHasArrows,this.defaultValue.horizontalHasArrows),handleMouseWheel:f.boolean(t.handleMouseWheel,this.defaultValue.handleMouseWheel),alwaysConsumeMouseWheel:f.boolean(t.alwaysConsumeMouseWheel,this.defaultValue.alwaysConsumeMouseWheel),horizontalScrollbarSize:n,horizontalSliderSize:p.clampedInt(t.horizontalSliderSize,n,0,1e3),verticalScrollbarSize:i,verticalSliderSize:p.clampedInt(t.verticalSliderSize,i,0,1e3)}},t}(c),Q=function(e){function t(){var t=this,n={insertMode:"insert",insertHighlight:!1,filterGraceful:!0,snippetsPreventQuickSuggestions:!0,localityBonus:!1,shareSuggestSelections:!1,showIcons:!0,maxVisibleSuggestions:12,showMethods:!0,showFunctions:!0,showConstructors:!0,showFields:!0,showVariables:!0,showClasses:!0,showStructs:!0,showInterfaces:!0,showModules:!0,showProperties:!0,showEvents:!0,showOperators:!0,showUnits:!0,showValues:!0,showConstants:!0,showEnums:!0,showEnumMembers:!0,showKeywords:!0,showWords:!0,showColors:!0,showFiles:!0,showReferences:!0,showFolders:!0,showTypeParameters:!0,showSnippets:!0,hideStatusBar:!0};return t=e.call(this,89,"suggest",n,{"editor.suggest.insertMode":{type:"string",enum:["insert","replace"],enumDescriptions:[i["a"]("suggest.insertMode.insert","Insert suggestion without overwriting text right of the cursor."),i["a"]("suggest.insertMode.replace","Insert suggestion and overwrite text right of the cursor.")],default:n.insertMode,description:i["a"]("suggest.insertMode","Controls whether words are overwritten when accepting completions. Note that this depends on extensions opting into this feature.")},"editor.suggest.insertHighlight":{type:"boolean",default:n.insertHighlight,description:i["a"]("suggest.insertHighlight","Controls whether unexpected text modifications while accepting completions should be highlighted, e.g `insertMode` is `replace` but the completion only supports `insert`.")},"editor.suggest.filterGraceful":{type:"boolean",default:n.filterGraceful,description:i["a"]("suggest.filterGraceful","Controls whether filtering and sorting suggestions accounts for small typos.")},"editor.suggest.localityBonus":{type:"boolean",default:n.localityBonus,description:i["a"]("suggest.localityBonus","Controls whether sorting favours words that appear close to the cursor.")},"editor.suggest.shareSuggestSelections":{type:"boolean",default:n.shareSuggestSelections,markdownDescription:i["a"]("suggest.shareSuggestSelections","Controls whether remembered suggestion selections are shared between multiple workspaces and windows (needs `#editor.suggestSelection#`).")},"editor.suggest.snippetsPreventQuickSuggestions":{type:"boolean",default:n.snippetsPreventQuickSuggestions,description:i["a"]("suggest.snippetsPreventQuickSuggestions","Controls whether an active snippet prevents quick suggestions.")},"editor.suggest.showIcons":{type:"boolean",default:n.showIcons,description:i["a"]("suggest.showIcons","Controls whether to show or hide icons in suggestions.")},"editor.suggest.maxVisibleSuggestions":{type:"number",default:n.maxVisibleSuggestions,minimum:1,maximum:15,description:i["a"]("suggest.maxVisibleSuggestions","Controls how many suggestions IntelliSense will show before showing a scrollbar (maximum 15).")},"editor.suggest.filteredTypes":{type:"object",deprecationMessage:i["a"]("deprecated","This setting is deprecated, please use separate settings like 'editor.suggest.showKeywords' or 'editor.suggest.showSnippets' instead.")},"editor.suggest.showMethods":{type:"boolean",default:!0,markdownDescription:i["a"]("editor.suggest.showMethods","When enabled IntelliSense shows `method`-suggestions.")},"editor.suggest.showFunctions":{type:"boolean",default:!0,markdownDescription:i["a"]("editor.suggest.showFunctions","When enabled IntelliSense shows `function`-suggestions.")},"editor.suggest.showConstructors":{type:"boolean",default:!0,markdownDescription:i["a"]("editor.suggest.showConstructors","When enabled IntelliSense shows `constructor`-suggestions.")},"editor.suggest.showFields":{type:"boolean",default:!0,markdownDescription:i["a"]("editor.suggest.showFields","When enabled IntelliSense shows `field`-suggestions.")},"editor.suggest.showVariables":{type:"boolean",default:!0,markdownDescription:i["a"]("editor.suggest.showVariables","When enabled IntelliSense shows `variable`-suggestions.")},"editor.suggest.showClasses":{type:"boolean",default:!0,markdownDescription:i["a"]("editor.suggest.showClasss","When enabled IntelliSense shows `class`-suggestions.")},"editor.suggest.showStructs":{type:"boolean",default:!0,markdownDescription:i["a"]("editor.suggest.showStructs","When enabled IntelliSense shows `struct`-suggestions.")},"editor.suggest.showInterfaces":{type:"boolean",default:!0,markdownDescription:i["a"]("editor.suggest.showInterfaces","When enabled IntelliSense shows `interface`-suggestions.")},"editor.suggest.showModules":{type:"boolean",default:!0,markdownDescription:i["a"]("editor.suggest.showModules","When enabled IntelliSense shows `module`-suggestions.")},"editor.suggest.showProperties":{type:"boolean",default:!0,markdownDescription:i["a"]("editor.suggest.showPropertys","When enabled IntelliSense shows `property`-suggestions.")},"editor.suggest.showEvents":{type:"boolean",default:!0,markdownDescription:i["a"]("editor.suggest.showEvents","When enabled IntelliSense shows `event`-suggestions.")},"editor.suggest.showOperators":{type:"boolean",default:!0,markdownDescription:i["a"]("editor.suggest.showOperators","When enabled IntelliSense shows `operator`-suggestions.")},"editor.suggest.showUnits":{type:"boolean",default:!0,markdownDescription:i["a"]("editor.suggest.showUnits","When enabled IntelliSense shows `unit`-suggestions.")},"editor.suggest.showValues":{type:"boolean",default:!0,markdownDescription:i["a"]("editor.suggest.showValues","When enabled IntelliSense shows `value`-suggestions.")},"editor.suggest.showConstants":{type:"boolean",default:!0,markdownDescription:i["a"]("editor.suggest.showConstants","When enabled IntelliSense shows `constant`-suggestions.")},"editor.suggest.showEnums":{type:"boolean",default:!0,markdownDescription:i["a"]("editor.suggest.showEnums","When enabled IntelliSense shows `enum`-suggestions.")},"editor.suggest.showEnumMembers":{type:"boolean",default:!0,markdownDescription:i["a"]("editor.suggest.showEnumMembers","When enabled IntelliSense shows `enumMember`-suggestions.")},"editor.suggest.showKeywords":{type:"boolean",default:!0,markdownDescription:i["a"]("editor.suggest.showKeywords","When enabled IntelliSense shows `keyword`-suggestions.")},"editor.suggest.showWords":{type:"boolean",default:!0,markdownDescription:i["a"]("editor.suggest.showTexts","When enabled IntelliSense shows `text`-suggestions.")},"editor.suggest.showColors":{type:"boolean",default:!0,markdownDescription:i["a"]("editor.suggest.showColors","When enabled IntelliSense shows `color`-suggestions.")},"editor.suggest.showFiles":{type:"boolean",default:!0,markdownDescription:i["a"]("editor.suggest.showFiles","When enabled IntelliSense shows `file`-suggestions.")},"editor.suggest.showReferences":{type:"boolean",default:!0,markdownDescription:i["a"]("editor.suggest.showReferences","When enabled IntelliSense shows `reference`-suggestions.")},"editor.suggest.showCustomcolors":{type:"boolean",default:!0,markdownDescription:i["a"]("editor.suggest.showCustomcolors","When enabled IntelliSense shows `customcolor`-suggestions.")},"editor.suggest.showFolders":{type:"boolean",default:!0,markdownDescription:i["a"]("editor.suggest.showFolders","When enabled IntelliSense shows `folder`-suggestions.")},"editor.suggest.showTypeParameters":{type:"boolean",default:!0,markdownDescription:i["a"]("editor.suggest.showTypeParameters","When enabled IntelliSense shows `typeParameter`-suggestions.")},"editor.suggest.showSnippets":{type:"boolean",default:!0,markdownDescription:i["a"]("editor.suggest.showSnippets","When enabled IntelliSense shows `snippet`-suggestions.")},"editor.suggest.hideStatusBar":{type:"boolean",default:!0,markdownDescription:i["a"]("editor.suggest.hideStatusBar","Controls the visibility of the status bar at the bottom of the suggest widget.")}})||this,t}return s(t,e),t.prototype.validate=function(e){if("object"!==typeof e)return this.defaultValue;var t=e;return{insertMode:v.stringSet(t.insertMode,this.defaultValue.insertMode,["insert","replace"]),insertHighlight:f.boolean(t.insertHighlight,this.defaultValue.insertHighlight),filterGraceful:f.boolean(t.filterGraceful,this.defaultValue.filterGraceful),snippetsPreventQuickSuggestions:f.boolean(t.snippetsPreventQuickSuggestions,this.defaultValue.filterGraceful),localityBonus:f.boolean(t.localityBonus,this.defaultValue.localityBonus),shareSuggestSelections:f.boolean(t.shareSuggestSelections,this.defaultValue.shareSuggestSelections),showIcons:f.boolean(t.showIcons,this.defaultValue.showIcons),maxVisibleSuggestions:p.clampedInt(t.maxVisibleSuggestions,this.defaultValue.maxVisibleSuggestions,1,15),showMethods:f.boolean(t.showMethods,this.defaultValue.showMethods),showFunctions:f.boolean(t.showFunctions,this.defaultValue.showFunctions),showConstructors:f.boolean(t.showConstructors,this.defaultValue.showConstructors),showFields:f.boolean(t.showFields,this.defaultValue.showFields),showVariables:f.boolean(t.showVariables,this.defaultValue.showVariables),showClasses:f.boolean(t.showClasses,this.defaultValue.showClasses),showStructs:f.boolean(t.showStructs,this.defaultValue.showStructs),showInterfaces:f.boolean(t.showInterfaces,this.defaultValue.showInterfaces),showModules:f.boolean(t.showModules,this.defaultValue.showModules),showProperties:f.boolean(t.showProperties,this.defaultValue.showProperties),showEvents:f.boolean(t.showEvents,this.defaultValue.showEvents),showOperators:f.boolean(t.showOperators,this.defaultValue.showOperators),showUnits:f.boolean(t.showUnits,this.defaultValue.showUnits),showValues:f.boolean(t.showValues,this.defaultValue.showValues),showConstants:f.boolean(t.showConstants,this.defaultValue.showConstants),showEnums:f.boolean(t.showEnums,this.defaultValue.showEnums),showEnumMembers:f.boolean(t.showEnumMembers,this.defaultValue.showEnumMembers),showKeywords:f.boolean(t.showKeywords,this.defaultValue.showKeywords),showWords:f.boolean(t.showWords,this.defaultValue.showWords),showColors:f.boolean(t.showColors,this.defaultValue.showColors),showFiles:f.boolean(t.showFiles,this.defaultValue.showFiles),showReferences:f.boolean(t.showReferences,this.defaultValue.showReferences),showFolders:f.boolean(t.showFolders,this.defaultValue.showFolders),showTypeParameters:f.boolean(t.showTypeParameters,this.defaultValue.showTypeParameters),showSnippets:f.boolean(t.showSnippets,this.defaultValue.showSnippets),hideStatusBar:f.boolean(t.hideStatusBar,this.defaultValue.hideStatusBar)}},t}(c),K=function(e){function t(){return e.call(this,106,[68])||this}return s(t,e),t.prototype.compute=function(e,t,n){var i=t.get(68);return!!i||e.tabFocusMode},t}(h);function Z(e){switch(e){case"none":return 0;case"same":return 1;case"indent":return 2;case"deepIndent":return 3}}var J=function(e){function t(){return e.call(this,108,[97,100,101,107,2])||this}return s(t,e),t.prototype.compute=function(e,t,n){var i=t.get(97),o=t.get(100),r=t.get(101),s=t.get(107),a=t.get(2),u=null;return u=2===a?{isWordWrapMinified:!1,isViewportWrapping:!1,wrappingColumn:-1}:r&&e.isDominatedByLongLines?{isWordWrapMinified:!0,isViewportWrapping:!0,wrappingColumn:Math.max(1,s.viewportColumn)}:"on"===i?{isWordWrapMinified:!1,isViewportWrapping:!0,wrappingColumn:Math.max(1,s.viewportColumn)}:"bounded"===i?{isWordWrapMinified:!1,isViewportWrapping:!0,wrappingColumn:Math.min(Math.max(1,s.viewportColumn),o)}:"wordWrapColumn"===i?{isWordWrapMinified:!1,isViewportWrapping:!1,wrappingColumn:o}:{isWordWrapMinified:!1,isViewportWrapping:!1,wrappingColumn:-1},{isDominatedByLongLines:e.isDominatedByLongLines,isWordWrapMinified:u.isWordWrapMinified,isViewportWrapping:u.isViewportWrapping,wrappingColumn:u.wrappingColumn}},t}(h),X="Consolas, 'Courier New', monospace",$="Menlo, Monaco, 'Courier New', monospace",Y="'Droid Sans Mono', 'monospace', monospace, 'Droid Sans Fallback'",ee={fontFamily:o["e"]?$:o["d"]?Y:X,fontWeight:"normal",fontSize:o["e"]?12:14,lineHeight:0,letterSpacing:0},te={tabSize:4,indentSize:4,insertSpaces:!0,detectIndentation:!0,trimAutoWhitespace:!0,largeFileOptimizations:!0},ne=[];function ie(e){return ne[e.id]=e,e}var oe={acceptSuggestionOnCommitCharacter:ie(new f(0,"acceptSuggestionOnCommitCharacter",!0,{markdownDescription:i["a"]("acceptSuggestionOnCommitCharacter","Controls whether suggestions should be accepted on commit characters. For example, in JavaScript, the semi-colon (`;`) can be a commit character that accepts a suggestion and types that character.")})),acceptSuggestionOnEnter:ie(new v(1,"acceptSuggestionOnEnter","on",["on","smart","off"],{markdownEnumDescriptions:["",i["a"]("acceptSuggestionOnEnterSmart","Only accept a suggestion with `Enter` when it makes a textual change."),""],markdownDescription:i["a"]("acceptSuggestionOnEnter","Controls whether suggestions should be accepted on `Enter`, in addition to `Tab`. Helps to avoid ambiguity between inserting new lines or accepting suggestions.")})),accessibilitySupport:ie(new S),accessibilityPageSize:ie(new p(3,"accessibilityPageSize",10,1,1073741824,{description:i["a"]("accessibilityPageSize","Controls the number of lines in the editor that can be read out by a screen reader. Warning: this has a performance implication for numbers larger than the default.")})),ariaLabel:ie(new w(4,"ariaLabel",i["a"]("editorViewAccessibleLabel","Editor content"))),autoClosingBrackets:ie(new v(5,"autoClosingBrackets","languageDefined",["always","languageDefined","beforeWhitespace","never"],{enumDescriptions:["",i["a"]("editor.autoClosingBrackets.languageDefined","Use language configurations to determine when to autoclose brackets."),i["a"]("editor.autoClosingBrackets.beforeWhitespace","Autoclose brackets only when the cursor is to the left of whitespace."),""],description:i["a"]("autoClosingBrackets","Controls whether the editor should automatically close brackets after the user adds an opening bracket.")})),autoClosingOvertype:ie(new v(6,"autoClosingOvertype","auto",["always","auto","never"],{enumDescriptions:["",i["a"]("editor.autoClosingOvertype.auto","Type over closing quotes or brackets only if they were automatically inserted."),""],description:i["a"]("autoClosingOvertype","Controls whether the editor should type over closing quotes or brackets.")})),autoClosingQuotes:ie(new v(7,"autoClosingQuotes","languageDefined",["always","languageDefined","beforeWhitespace","never"],{enumDescriptions:["",i["a"]("editor.autoClosingQuotes.languageDefined","Use language configurations to determine when to autoclose quotes."),i["a"]("editor.autoClosingQuotes.beforeWhitespace","Autoclose quotes only when the cursor is to the left of whitespace."),""],description:i["a"]("autoClosingQuotes","Controls whether the editor should automatically close quotes after the user adds an opening quote.")})),autoIndent:ie(new b(8,"autoIndent",4,"full",["none","keep","brackets","advanced","full"],C,{enumDescriptions:[i["a"]("editor.autoIndent.none","The editor will not insert indentation automatically."),i["a"]("editor.autoIndent.keep","The editor will keep the current line's indentation."),i["a"]("editor.autoIndent.brackets","The editor will keep the current line's indentation and honor language defined brackets."),i["a"]("editor.autoIndent.advanced","The editor will keep the current line's indentation, honor language defined brackets and invoke special onEnterRules defined by languages."),i["a"]("editor.autoIndent.full","The editor will keep the current line's indentation, honor language defined brackets, invoke special onEnterRules defined by languages, and honor indentationRules defined by languages.")],description:i["a"]("autoIndent","Controls whether the editor should automatically adjust the indentation when users type, paste, move or indent lines.")})),automaticLayout:ie(new f(9,"automaticLayout",!1)),autoSurround:ie(new v(10,"autoSurround","languageDefined",["languageDefined","quotes","brackets","never"],{enumDescriptions:[i["a"]("editor.autoSurround.languageDefined","Use language configurations to determine when to automatically surround selections."),i["a"]("editor.autoSurround.quotes","Surround with quotes but not brackets."),i["a"]("editor.autoSurround.brackets","Surround with brackets but not quotes."),""],description:i["a"]("autoSurround","Controls whether the editor should automatically surround selections.")})),codeLens:ie(new f(11,"codeLens",!0,{description:i["a"]("codeLens","Controls whether the editor shows CodeLens.")})),colorDecorators:ie(new f(12,"colorDecorators",!0,{description:i["a"]("colorDecorators","Controls whether the editor should render the inline color decorators and color picker.")})),comments:ie(new _),contextmenu:ie(new f(14,"contextmenu",!0)),copyWithSyntaxHighlighting:ie(new f(15,"copyWithSyntaxHighlighting",!0,{description:i["a"]("copyWithSyntaxHighlighting","Controls whether syntax highlighting should be copied into the clipboard.")})),cursorBlinking:ie(new b(16,"cursorBlinking",1,"blink",["blink","smooth","phase","expand","solid"],L,{description:i["a"]("cursorBlinking","Control the cursor animation style.")})),cursorSmoothCaretAnimation:ie(new f(17,"cursorSmoothCaretAnimation",!1,{description:i["a"]("cursorSmoothCaretAnimation","Controls whether the smooth caret animation should be enabled.")})),cursorStyle:ie(new b(18,"cursorStyle",y.Line,"line",["line","block","underline","line-thin","block-outline","underline-thin"],N,{description:i["a"]("cursorStyle","Controls the cursor style.")})),cursorSurroundingLines:ie(new p(19,"cursorSurroundingLines",0,0,1073741824,{description:i["a"]("cursorSurroundingLines","Controls the minimal number of visible leading and trailing lines surrounding the cursor. Known as 'scrollOff' or `scrollOffset` in some other editors.")})),cursorSurroundingLinesStyle:ie(new v(20,"cursorSurroundingLinesStyle","default",["default","all"],{enumDescriptions:[i["a"]("cursorSurroundingLinesStyle.default","`cursorSurroundingLines` is enforced only when triggered via the keyboard or API."),i["a"]("cursorSurroundingLinesStyle.all","`cursorSurroundingLines` is enforced always.")],description:i["a"]("cursorSurroundingLinesStyle","Controls when `cursorSurroundingLines` should be enforced.")})),cursorWidth:ie(new p(21,"cursorWidth",0,0,1073741824,{markdownDescription:i["a"]("cursorWidth","Controls the width of the cursor when `#editor.cursorStyle#` is set to `line`.")})),disableLayerHinting:ie(new f(22,"disableLayerHinting",!1)),disableMonospaceOptimizations:ie(new f(23,"disableMonospaceOptimizations",!1)),dragAndDrop:ie(new f(24,"dragAndDrop",!0,{description:i["a"]("dragAndDrop","Controls whether the editor should allow moving selections via drag and drop.")})),emptySelectionClipboard:ie(new O),extraEditorClassName:ie(new w(26,"extraEditorClassName","")),fastScrollSensitivity:ie(new g(27,"fastScrollSensitivity",5,(function(e){return e<=0?5:e}),{markdownDescription:i["a"]("fastScrollSensitivity","Scrolling speed multiplier when pressing `Alt`.")})),find:ie(new E),fixedOverflowWidgets:ie(new f(29,"fixedOverflowWidgets",!1)),folding:ie(new f(30,"folding",!0,{description:i["a"]("folding","Controls whether the editor has code folding enabled.")})),foldingStrategy:ie(new v(31,"foldingStrategy","auto",["auto","indentation"],{markdownDescription:i["a"]("foldingStrategy","Controls the strategy for computing folding ranges. `auto` uses a language specific folding strategy, if available. `indentation` uses the indentation based folding strategy.")})),foldingHighlight:ie(new f(32,"foldingHighlight",!0,{description:i["a"]("foldingHighlight","Controls whether the editor should highlight folded ranges.")})),fontFamily:ie(new w(33,"fontFamily",ee.fontFamily,{description:i["a"]("fontFamily","Controls the font family.")})),fontInfo:ie(new I),fontLigatures2:ie(new P),fontSize:ie(new M),fontWeight:ie(new w(37,"fontWeight",ee.fontWeight,{enum:["normal","bold","100","200","300","400","500","600","700","800","900"],description:i["a"]("fontWeight","Controls the font weight.")})),formatOnPaste:ie(new f(38,"formatOnPaste",!1,{description:i["a"]("formatOnPaste","Controls whether the editor should automatically format the pasted content. A formatter must be available and the formatter should be able to format a range in a document.")})),formatOnType:ie(new f(39,"formatOnType",!1,{description:i["a"]("formatOnType","Controls whether the editor should automatically format the line after typing.")})),glyphMargin:ie(new f(40,"glyphMargin",!0,{description:i["a"]("glyphMargin","Controls whether the editor should render the vertical glyph margin. Glyph margin is mostly used for debugging.")})),gotoLocation:ie(new T),hideCursorInOverviewRuler:ie(new f(42,"hideCursorInOverviewRuler",!1,{description:i["a"]("hideCursorInOverviewRuler","Controls whether the cursor should be hidden in the overview ruler.")})),highlightActiveIndentGuide:ie(new f(43,"highlightActiveIndentGuide",!0,{description:i["a"]("highlightActiveIndentGuide","Controls whether the editor should highlight the active indent guide.")})),hover:ie(new x),inDiffEditor:ie(new f(45,"inDiffEditor",!1)),letterSpacing:ie(new g(46,"letterSpacing",ee.letterSpacing,(function(e){return g.clamp(e,-5,20)}),{description:i["a"]("letterSpacing","Controls the letter spacing in pixels.")})),lightbulb:ie(new V),lineDecorationsWidth:ie(new m(48,"lineDecorationsWidth",10)),lineHeight:ie(new D),lineNumbers:ie(new H),lineNumbersMinChars:ie(new p(51,"lineNumbersMinChars",5,1,300)),links:ie(new f(52,"links",!0,{description:i["a"]("links","Controls whether the editor should detect links and make them clickable.")})),matchBrackets:ie(new v(53,"matchBrackets","always",["always","near","never"],{description:i["a"]("matchBrackets","Highlight matching brackets.")})),minimap:ie(new R),mouseStyle:ie(new v(55,"mouseStyle","text",["text","default","copy"])),mouseWheelScrollSensitivity:ie(new g(56,"mouseWheelScrollSensitivity",1,(function(e){return 0===e?1:e}),{markdownDescription:i["a"]("mouseWheelScrollSensitivity","A multiplier to be used on the `deltaX` and `deltaY` of mouse wheel scroll events.")})),mouseWheelZoom:ie(new f(57,"mouseWheelZoom",!1,{markdownDescription:i["a"]("mouseWheelZoom","Zoom the font of the editor when using mouse wheel and holding `Ctrl`.")})),multiCursorMergeOverlapping:ie(new f(58,"multiCursorMergeOverlapping",!0,{description:i["a"]("multiCursorMergeOverlapping","Merge multiple cursors when they are overlapping.")})),multiCursorModifier:ie(new b(59,"multiCursorModifier","altKey","alt",["ctrlCmd","alt"],A,{markdownEnumDescriptions:[i["a"]("multiCursorModifier.ctrlCmd","Maps to `Control` on Windows and Linux and to `Command` on macOS."),i["a"]("multiCursorModifier.alt","Maps to `Alt` on Windows and Linux and to `Option` on macOS.")],markdownDescription:i["a"]({key:"multiCursorModifier",comment:["- `ctrlCmd` refers to a value the setting can take and should not be localized.","- `Control` and `Command` refer to the modifier keys Ctrl or Cmd on the keyboard and can be localized."]},"The modifier to be used to add multiple cursors with the mouse. The Go To Definition and Open Link mouse gestures will adapt such that they do not conflict with the multicursor modifier. [Read more](https://code.visualstudio.com/docs/editor/codebasics#_multicursor-modifier).")})),multiCursorPaste:ie(new v(60,"multiCursorPaste","spread",["spread","full"],{markdownEnumDescriptions:[i["a"]("multiCursorPaste.spread","Each cursor pastes a single line of the text."),i["a"]("multiCursorPaste.full","Each cursor pastes the full text.")],markdownDescription:i["a"]("multiCursorPaste","Controls pasting when the line count of the pasted text matches the cursor count.")})),occurrencesHighlight:ie(new f(61,"occurrencesHighlight",!0,{description:i["a"]("occurrencesHighlight","Controls whether the editor should highlight semantic symbol occurrences.")})),overviewRulerBorder:ie(new f(62,"overviewRulerBorder",!0,{description:i["a"]("overviewRulerBorder","Controls whether a border should be drawn around the overview ruler.")})),overviewRulerLanes:ie(new p(63,"overviewRulerLanes",3,0,3)),parameterHints:ie(new F),peekWidgetDefaultFocus:ie(new v(65,"peekWidgetDefaultFocus","tree",["tree","editor"],{enumDescriptions:[i["a"]("peekWidgetDefaultFocus.tree","Focus the tree when opening peek"),i["a"]("peekWidgetDefaultFocus.editor","Focus the editor when opening peek")],description:i["a"]("peekWidgetDefaultFocus","Controls whether to focus the inline editor or the tree in the peek widget.")})),quickSuggestions:ie(new B),quickSuggestionsDelay:ie(new p(67,"quickSuggestionsDelay",10,0,1073741824,{description:i["a"]("quickSuggestionsDelay","Controls the delay in milliseconds after which quick suggestions will show up.")})),readOnly:ie(new f(68,"readOnly",!1)),renderControlCharacters:ie(new f(69,"renderControlCharacters",!1,{description:i["a"]("renderControlCharacters","Controls whether the editor should render control characters.")})),renderIndentGuides:ie(new f(70,"renderIndentGuides",!0,{description:i["a"]("renderIndentGuides","Controls whether the editor should render indent guides.")})),renderFinalNewline:ie(new f(71,"renderFinalNewline",!0,{description:i["a"]("renderFinalNewline","Render last line number when the file ends with a newline.")})),renderLineHighlight:ie(new v(72,"renderLineHighlight","line",["none","gutter","line","all"],{enumDescriptions:["","","",i["a"]("renderLineHighlight.all","Highlights both the gutter and the current line.")],description:i["a"]("renderLineHighlight","Controls how the editor should render the current line highlight.")})),renderValidationDecorations:ie(new v(73,"renderValidationDecorations","editable",["editable","on","off"])),renderWhitespace:ie(new v(74,"renderWhitespace","none",["none","boundary","selection","all"],{enumDescriptions:["",i["a"]("renderWhitespace.boundary","Render whitespace characters except for single spaces between words."),i["a"]("renderWhitespace.selection","Render whitespace characters only on selected text."),""],description:i["a"]("renderWhitespace","Controls how the editor should render whitespace characters.")})),revealHorizontalRightPadding:ie(new p(75,"revealHorizontalRightPadding",30,0,1e3)),roundedSelection:ie(new f(76,"roundedSelection",!0,{description:i["a"]("roundedSelection","Controls whether selections should have rounded corners.")})),rulers:ie(new U),scrollbar:ie(new G),scrollBeyondLastColumn:ie(new p(79,"scrollBeyondLastColumn",5,0,1073741824,{description:i["a"]("scrollBeyondLastColumn","Controls the number of extra characters beyond which the editor will scroll horizontally.")})),scrollBeyondLastLine:ie(new f(80,"scrollBeyondLastLine",!0,{description:i["a"]("scrollBeyondLastLine","Controls whether the editor will scroll beyond the last line.")})),selectionClipboard:ie(new f(81,"selectionClipboard",!0,{description:i["a"]("selectionClipboard","Controls whether the Linux primary clipboard should be supported."),included:o["d"]})),selectionHighlight:ie(new f(82,"selectionHighlight",!0,{description:i["a"]("selectionHighlight","Controls whether the editor should highlight matches similar to the selection.")})),selectOnLineNumbers:ie(new f(83,"selectOnLineNumbers",!0)),showFoldingControls:ie(new v(84,"showFoldingControls","mouseover",["always","mouseover"],{description:i["a"]("showFoldingControls","Controls whether the fold controls on the gutter are automatically hidden.")})),showUnused:ie(new f(85,"showUnused",!0,{description:i["a"]("showUnused","Controls fading out of unused code.")})),snippetSuggestions:ie(new v(86,"snippetSuggestions","inline",["top","bottom","inline","none"],{enumDescriptions:[i["a"]("snippetSuggestions.top","Show snippet suggestions on top of other suggestions."),i["a"]("snippetSuggestions.bottom","Show snippet suggestions below other suggestions."),i["a"]("snippetSuggestions.inline","Show snippets suggestions with other suggestions."),i["a"]("snippetSuggestions.none","Do not show snippet suggestions.")],description:i["a"]("snippetSuggestions","Controls whether snippets are shown with other suggestions and how they are sorted.")})),smoothScrolling:ie(new f(87,"smoothScrolling",!1,{description:i["a"]("smoothScrolling","Controls whether the editor will scroll using an animation.")})),stopRenderingLineAfter:ie(new p(88,"stopRenderingLineAfter",1e4,-1,1073741824)),suggest:ie(new Q),suggestFontSize:ie(new p(90,"suggestFontSize",0,0,1e3,{markdownDescription:i["a"]("suggestFontSize","Font size for the suggest widget. When set to `0`, the value of `#editor.fontSize#` is used.")})),suggestLineHeight:ie(new p(91,"suggestLineHeight",0,0,1e3,{markdownDescription:i["a"]("suggestLineHeight","Line height for the suggest widget. When set to `0`, the value of `#editor.lineHeight#` is used.")})),suggestOnTriggerCharacters:ie(new f(92,"suggestOnTriggerCharacters",!0,{description:i["a"]("suggestOnTriggerCharacters","Controls whether suggestions should automatically show up when typing trigger characters.")})),suggestSelection:ie(new v(93,"suggestSelection","recentlyUsed",["first","recentlyUsed","recentlyUsedByPrefix"],{markdownEnumDescriptions:[i["a"]("suggestSelection.first","Always select the first suggestion."),i["a"]("suggestSelection.recentlyUsed","Select recent suggestions unless further typing selects one, e.g. `console.| -> console.log` because `log` has been completed recently."),i["a"]("suggestSelection.recentlyUsedByPrefix","Select suggestions based on previous prefixes that have completed those suggestions, e.g. `co -> console` and `con -> const`.")],description:i["a"]("suggestSelection","Controls how suggestions are pre-selected when showing the suggest list.")})),tabCompletion:ie(new v(94,"tabCompletion","off",["on","off","onlySnippets"],{enumDescriptions:[i["a"]("tabCompletion.on","Tab complete will insert the best matching suggestion when pressing tab."),i["a"]("tabCompletion.off","Disable tab completions."),i["a"]("tabCompletion.onlySnippets","Tab complete snippets when their prefix match. Works best when 'quickSuggestions' aren't enabled.")],description:i["a"]("tabCompletion","Enables tab completions.")})),useTabStops:ie(new f(95,"useTabStops",!0,{description:i["a"]("useTabStops","Inserting and deleting whitespace follows tab stops.")})),wordSeparators:ie(new w(96,"wordSeparators",r["b"],{description:i["a"]("wordSeparators","Characters that will be used as word separators when doing word related navigations or operations.")})),wordWrap:ie(new v(97,"wordWrap","off",["off","on","wordWrapColumn","bounded"],{markdownEnumDescriptions:[i["a"]("wordWrap.off","Lines will never wrap."),i["a"]("wordWrap.on","Lines will wrap at the viewport width."),i["a"]({key:"wordWrap.wordWrapColumn",comment:["- `editor.wordWrapColumn` refers to a different setting and should not be localized."]},"Lines will wrap at `#editor.wordWrapColumn#`."),i["a"]({key:"wordWrap.bounded",comment:["- viewport means the edge of the visible window size.","- `editor.wordWrapColumn` refers to a different setting and should not be localized."]},"Lines will wrap at the minimum of viewport and `#editor.wordWrapColumn#`.")],description:i["a"]({key:"wordWrap",comment:["- 'off', 'on', 'wordWrapColumn' and 'bounded' refer to values the setting can take and should not be localized.","- `editor.wordWrapColumn` refers to a different setting and should not be localized."]},"Controls how lines should wrap.")})),wordWrapBreakAfterCharacters:ie(new w(98,"wordWrapBreakAfterCharacters"," \t})]?|/&.,;¢°′″‰℃、。｡､￠，．：；？！％・･ゝゞヽヾーァィゥェォッャュョヮヵヶぁぃぅぇぉっゃゅょゎゕゖㇰㇱㇲㇳㇴㇵㇶㇷㇸㇹㇺㇻㇼㇽㇾㇿ々〻ｧｨｩｪｫｬｭｮｯｰ”〉》」』】〕）］｝｣")),wordWrapBreakBeforeCharacters:ie(new w(99,"wordWrapBreakBeforeCharacters","([{‘“〈《「『【〔（［｛｢£¥＄￡￥+＋")),wordWrapColumn:ie(new p(100,"wordWrapColumn",80,1,1073741824,{markdownDescription:i["a"]({key:"wordWrapColumn",comment:["- `editor.wordWrap` refers to a different setting and should not be localized.","- 'wordWrapColumn' and 'bounded' refer to values the different setting can take and should not be localized."]},"Controls the wrapping column of the editor when `#editor.wordWrap#` is `wordWrapColumn` or `bounded`.")})),wordWrapMinified:ie(new f(101,"wordWrapMinified",!0)),wrappingIndent:ie(new b(102,"wrappingIndent",1,"same",["none","same","indent","deepIndent"],Z,{enumDescriptions:[i["a"]("wrappingIndent.none","No indentation. Wrapped lines begin at column 1."),i["a"]("wrappingIndent.same","Wrapped lines get the same indentation as the parent."),i["a"]("wrappingIndent.indent","Wrapped lines get +1 indentation toward the parent."),i["a"]("wrappingIndent.deepIndent","Wrapped lines get +2 indentation toward the parent.")],description:i["a"]("wrappingIndent","Controls the indentation of wrapped lines.")})),wrappingStrategy:ie(new v(103,"wrappingStrategy","simple",["simple","advanced"],{enumDescriptions:[i["a"]("wrappingStrategy.simple","Assumes that all characters are of the same width. This is a fast algorithm that works correctly for monospace fonts and certain scripts (like Latin characters) where glyphs are of equal width."),i["a"]("wrappingStrategy.advanced","Delegates wrapping points computation to the browser. This is a slow algorithm, that might cause freezes for large files, but it works correctly in all cases.")],description:i["a"]("wrappingStrategy","Controls the algorithm that computes wrapping points.")})),editorClassName:ie(new k),pixelRatio:ie(new z),tabFocusMode:ie(new K),layoutInfo:ie(new W),wrappingInfo:ie(new J)}},fd91:function(e,t,n){"use strict";n.d(t,"a",(function(){return s}));var i=n("2e5d"),o=n("7061"),r=n("6a89"),s=function(){function e(){}return e.columnSelect=function(e,t,n,s,a,u){for(var l=Math.abs(a-n)+1,d=n>a,c=s>u,h=s<u,m=[],f=0;f<l;f++){var p=n+(d?-f:f),g=i["a"].columnFromVisibleColumn2(e,t,p,s),w=i["a"].columnFromVisibleColumn2(e,t,p,u),v=i["a"].visibleColumnFromColumn2(e,t,new o["a"](p,g)),b=i["a"].visibleColumnFromColumn2(e,t,new o["a"](p,w));if(h){if(v>u)continue;if(b<s)continue}if(c){if(b>s)continue;if(v<u)continue}m.push(new i["f"](new r["a"](p,g,p,g),0,new o["a"](p,w),0))}if(0===m.length)for(f=0;f<l;f++){p=n+(d?-f:f);var C=t.getLineMaxColumn(p);m.push(new i["f"](new r["a"](p,C,p,C),0,new o["a"](p,C),0))}return{viewStates:m,reversed:d,fromLineNumber:n,fromVisualColumn:s,toLineNumber:a,toVisualColumn:u}},e.columnSelectLeft=function(t,n,i){var o=i.toViewVisualColumn;return o>1&&o--,e.columnSelect(t,n,i.fromViewLineNumber,i.fromViewVisualColumn,i.toViewLineNumber,o)},e.columnSelectRight=function(e,t,n){for(var r=0,s=Math.min(n.fromViewLineNumber,n.toViewLineNumber),a=Math.max(n.fromViewLineNumber,n.toViewLineNumber),u=s;u<=a;u++){var l=t.getLineMaxColumn(u),d=i["a"].visibleColumnFromColumn2(e,t,new o["a"](u,l));r=Math.max(r,d)}var c=n.toViewVisualColumn;return c<r&&c++,this.columnSelect(e,t,n.fromViewLineNumber,n.fromViewVisualColumn,n.toViewLineNumber,c)},e.columnSelectUp=function(e,t,n,i){var o=i?e.pageSize:1,r=Math.max(1,n.toViewLineNumber-o);return this.columnSelect(e,t,n.fromViewLineNumber,n.fromViewVisualColumn,r,n.toViewVisualColumn)},e.columnSelectDown=function(e,t,n,i){var o=i?e.pageSize:1,r=Math.min(t.getLineCount(),n.toViewLineNumber+o);return this.columnSelect(e,t,n.fromViewLineNumber,n.fromViewVisualColumn,r,n.toViewVisualColumn)},e}()}}]);