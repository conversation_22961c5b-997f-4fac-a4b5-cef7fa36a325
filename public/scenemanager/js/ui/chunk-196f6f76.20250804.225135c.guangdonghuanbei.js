(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-196f6f76"],{"55c2":function(t,e,s){},9006:function(t,e,s){"use strict";s.r(e);var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"locator-wrapper"},[s("div",{staticClass:"search-block"},[s("div",{staticClass:"search-input"},[s("el-input",{ref:"elInput",attrs:{placeholder:t.$t("formRelational.locatorInput.placeholder"),clearable:""},on:{clear:t.resetLocatorStatus},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.doSearch.apply(null,arguments)}},model:{value:t.province,callback:function(e){t.province=e},expression:"province"}})],1),s("el-button",{staticClass:"search-btn",attrs:{loading:t.isLoading,icon:"el-icon-search"},on:{click:t.doSearch}})],1),t.resultCount?s("div",{staticClass:"search-result-wrapper"},[s("el-collapse-transition",[s("div",{directives:[{name:"show",rawName:"v-show",value:!t.isSearchResultCollapsed,expression:"!isSearchResultCollapsed"}],staticClass:"search-result-items"},t._l(t.searchResults,(function(e){return s("div",{key:e.uid,staticClass:"search-result-item",on:{click:function(s){return s.stopPropagation(),t.onSearchResultItemClick(e)}}},[s("div",{staticClass:"mb-4 ellipsis"},[t._v(t._s(e.name))]),s("div",{staticClass:"address"},[t._v(t._s(e.address))])])})),0)]),s("div",{staticClass:"search-result-tip",class:{withTopShadow:!t.isSearchResultCollapsed},on:{click:t.toggleResult}},[s("CommonSVG",{attrs:{"icon-class":t.iconClass,size:12}}),s("div",{staticClass:"ml-4"},[t._v(" "+t._s(t.tipContent)+" ")])],1)],1):t._e()])},n=[],i=s("3835"),r=s("c7eb"),o=s("1da1"),c=(s("d3b7"),s("3ca3"),s("ddb0"),s("ac1f"),s("00b4"),s("caad"),s("2532"),s("b0c0"),s("365c")),l=52.35987755982988,u=3.141592653589793,h=6378245,d=.006693421622965943;function p(t,e){var s=t-.0065,a=e-.006,n=Math.sqrt(s*s+a*a)-2e-5*Math.sin(a*l),i=Math.atan2(a,s)-3e-6*Math.cos(s*l),r=n*Math.cos(i),o=n*Math.sin(i);return[r,o]}function v(t,e){if(m(t,e))return[t,e];var s=f(t-105,e-35),a=C(t-105,e-35),n=e/180*u,i=Math.sin(n);i=1-d*i*i;var r=Math.sqrt(i);s=180*s/(h*(1-d)/(i*r)*u),a=180*a/(h/r*Math.cos(n)*u);var o=e+s,c=t+a;return[2*t-c,2*e-o]}function f(t,e){var s=2*t-100+3*e+.2*e*e+.1*t*e+.2*Math.sqrt(Math.abs(t));return s+=2*(20*Math.sin(6*t*u)+20*Math.sin(2*t*u))/3,s+=2*(20*Math.sin(e*u)+40*Math.sin(e/3*u))/3,s+=2*(160*Math.sin(e/12*u)+320*Math.sin(e*u/30))/3,s}function C(t,e){var s=300+t+2*e+.1*t*t+.1*t*e+.1*Math.sqrt(Math.abs(t));return s+=2*(20*Math.sin(6*t*u)+20*Math.sin(2*t*u))/3,s+=2*(20*Math.sin(t*u)+40*Math.sin(t/3*u))/3,s+=2*(150*Math.sin(t/12*u)+300*Math.sin(t/30*u))/3,s}function m(t,e){return!(73.66<t<135.05&&3.86<e<53.55)}var S={name:"Locator",props:{strLocation:{type:String,default:""}},components:{CommonSVG:function(){return s.e("chunk-51f90655").then(s.bind(null,"17d0"))}},data:function(){return{province:this.strLocation,searchResults:[],isLoading:!1,isSearchResultCollapsed:!1,regForEmpty:/^\s*$/}},computed:{resultCount:function(){var t,e;return null!==(t=null===(e=this.searchResults)||void 0===e?void 0:e.length)&&void 0!==t?t:0},iconClass:function(){return this.isSearchResultCollapsed?"collapse-d":"collapse-u"},tipContent:function(){return this.isSearchResultCollapsed?this.$t("messageTips.locatorInput",{resultCount:this.resultCount}):this.$t("others.collapse1")}},mounted:function(){setTimeout(this.focus,100)},methods:{doSearch:function(){var t=this;return Object(o["a"])(Object(r["a"])().mark((function e(){var s,a,n,i,o;return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(a=t.province,n=t.regForEmpty,a&&!n.test(a)){e.next=4;break}return t.resetLocatorStatus(),e.abrupt("return");case 4:return t.isLoading=!0,e.next=7,c["a"].getLocatorSearchResult(t.province).catch((function(){})).finally((function(){t.isLoading=!1}));case 7:i=e.sent,o=null!==(s=null===i||void 0===i?void 0:i.data)&&void 0!==s?s:"",t.searchResults=0===(null===o||void 0===o?void 0:o.status)?o.results:[],t.searchResults.length?t.isSearchResultCollapsed=!1:t.isSearchResultCollapsed=!0;case 11:case"end":return e.stop()}}),e)})))()},toggleResult:function(){this.isSearchResultCollapsed=!this.isSearchResultCollapsed},onSearchResultItemClick:function(t){var e,s=null!==(e=t.location)&&void 0!==e?e:{},a=s.lat,n=s.lng;if(a&&n){var r,o=p(n,a),c=Object(i["a"])(o,2);n=c[0],a=c[1];var l=v(n,a),u=Object(i["a"])(l,2);n=u[0],a=u[1];var h=window.scene.mv.tools.coordinate.mercator2vector(n,a,0,!1);window.scene.fitter.fit2FeatureCenter(h,100),this.$emit("searchResultItemClicked",t),null!==(r=this.$store.getters.menuActive)&&void 0!==r&&r.includes("locator")&&this.$store.commit("toggleMenuActive","locator")}else this.continuousSearch(t)},resetLocatorStatus:function(){this.searchResults=[],this.isSearchResultCollapsed=!1,this.isLoading=!1,this.$refs.elInput.focus()},focus:function(){this.$refs.elInput.focus()},continuousSearch:function(t){this.province+="-"+t.name,this.doSearch()}}},M=S,b=(s("9d82"),s("2877")),R=Object(b["a"])(M,a,n,!1,null,"774be34a",null);e["default"]=R.exports},"9d82":function(t,e,s){"use strict";s("55c2")}}]);