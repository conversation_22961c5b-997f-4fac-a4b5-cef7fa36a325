(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-11451c12"],{"0baa":function(e,t,n){},"27e6":function(e,t,n){},"2ab7":function(e,t,n){},3695:function(e,t,n){"use strict";n.d(t,"a",(function(){return C}));var o=n("11f7"),i=n("db88"),r=n("438a"),s=n("b589"),a=n("82c9"),h=n("6d8e"),l=n("5717"),u=n("9e74"),d=n("5bd7"),c=n("7c09"),p=function(e,t,n,o){var i,r=arguments.length,s=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)s=Reflect.decorate(e,t,n,o);else for(var a=e.length-1;a>=0;a--)(i=e[a])&&(s=(r<3?i(s):r>3?i(t,n,s):i(t,n))||s);return r>3&&s&&Object.defineProperty(t,n,s),s},f=function(e,t){return function(n,o){t(n,o,e)}},g=function(e,t,n,o){function i(e){return e instanceof n?e:new n((function(t){t(e)}))}return new(n||(n=Promise))((function(n,r){function s(e){try{h(o.next(e))}catch(t){r(t)}}function a(e){try{h(o["throw"](e))}catch(t){r(t)}}function h(e){e.done?n(e.value):i(e.value).then(s,a)}h((o=o.apply(e,t||[])).next())}))},_=function(e,t){var n,o,i,r,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function a(e){return function(t){return h([e,t])}}function h(r){if(n)throw new TypeError("Generator is already executing.");while(s)try{if(n=1,o&&(i=2&r[0]?o["return"]:r[0]?o["throw"]||((i=o["return"])&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return s.label++,{value:r[1],done:!1};case 5:s.label++,o=r[1],r=[0];continue;case 7:r=s.ops.pop(),s.trys.pop();continue;default:if(i=s.trys,!(i=i.length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){s=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){s.label=r[1];break}if(6===r[0]&&s.label<i[1]){s.label=i[1],i=r;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(r);break}i[2]&&s.ops.pop(),s.trys.pop();continue}r=t.call(e,s)}catch(a){r=[6,a],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}},m=function(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;var o=Array(e),i=0;for(t=0;t<n;t++)for(var r=arguments[t],s=0,a=r.length;s<a;s++,i++)o[i]=r[s];return o},v=function(){function e(e){this._commandService=e}return e.prototype.open=function(e){return g(this,void 0,void 0,(function(){var t,n;return _(this,(function(o){switch(o.label){case 0:if(!Object(d["c"])(e,s["b"].command))return[2,!1];if("string"===typeof e&&(e=h["a"].parse(e)),!u["a"].getCommand(e.path))throw new Error("command '"+e.path+"' NOT known");t=[];try{t=Object(r["a"])(decodeURIComponent(e.query))}catch(i){try{t=Object(r["a"])(e.query)}catch(a){}}return Array.isArray(t)||(t=[t]),[4,(n=this._commandService).executeCommand.apply(n,m([e.path],t))];case 1:return o.sent(),[2,!0]}}))}))},e=p([f(0,u["b"])],e),e}(),y=function(){function e(e){this._editorService=e}return e.prototype.open=function(e,t){return g(this,void 0,void 0,(function(){var n,o;return _(this,(function(i){switch(i.label){case 0:return"string"===typeof e&&(e=h["a"].parse(e)),n=void 0,o=/^L?(\d+)(?:,(\d+))?/.exec(e.fragment),o&&(n={startLineNumber:parseInt(o[1]),startColumn:o[2]?parseInt(o[2]):1},e=e.with({fragment:""})),e.scheme===s["b"].file&&(e=Object(a["g"])(e)),[4,this._editorService.openCodeEditor({resource:e,options:{selection:n,context:(null===t||void 0===t?void 0:t.fromUserGesture)?c["a"].USER:c["a"].API}},this._editorService.getFocusedCodeEditor(),null===t||void 0===t?void 0:t.openToSide)];case 1:return i.sent(),[2,!0]}}))}))},e=p([f(0,l["a"])],e),e}(),C=function(){function e(e,t){var n=this;this._openers=new i["a"],this._validators=new i["a"],this._resolvers=new i["a"],this._externalOpener={openExternal:function(e){return o["ab"](e),Promise.resolve(!0)}},this._openers.push({open:function(e,t){return g(n,void 0,void 0,(function(){return _(this,(function(n){switch(n.label){case 0:return(null===t||void 0===t?void 0:t.openExternal)||Object(d["c"])(e,s["b"].mailto)||Object(d["c"])(e,s["b"].http)||Object(d["c"])(e,s["b"].https)?[4,this._doOpenExternal(e,t)]:[3,2];case 1:return n.sent(),[2,!0];case 2:return[2,!1]}}))}))}}),this._openers.push(new v(t)),this._openers.push(new y(e))}return e.prototype.open=function(e,t){return g(this,void 0,void 0,(function(){var n,o,i,r,s,a,h;return _(this,(function(l){switch(l.label){case 0:n=0,o=this._validators.toArray(),l.label=1;case 1:return n<o.length?(i=o[n],[4,i.shouldOpen(e)]):[3,4];case 2:if(!l.sent())return[2,!1];l.label=3;case 3:return n++,[3,1];case 4:r=0,s=this._openers.toArray(),l.label=5;case 5:return r<s.length?(a=s[r],[4,a.open(e,t)]):[3,8];case 6:if(h=l.sent(),h)return[2,!0];l.label=7;case 7:return r++,[3,5];case 8:return[2,!1]}}))}))},e.prototype.resolveExternalUri=function(e,t){return g(this,void 0,void 0,(function(){var n,o,i,r;return _(this,(function(s){switch(s.label){case 0:n=0,o=this._resolvers.toArray(),s.label=1;case 1:return n<o.length?(i=o[n],[4,i.resolveExternalUri(e,t)]):[3,4];case 2:if(r=s.sent(),r)return[2,r];s.label=3;case 3:return n++,[3,1];case 4:return[2,{resolved:e,dispose:function(){}}]}}))}))},e.prototype._doOpenExternal=function(e,t){return g(this,void 0,void 0,(function(){var n,o;return _(this,(function(i){switch(i.label){case 0:return n="string"===typeof e?h["a"].parse(e):e,[4,this.resolveExternalUri(n,t)];case 1:return o=i.sent().resolved,"string"===typeof e&&n.toString()===o.toString()?[2,this._externalOpener.openExternal(e)]:[2,this._externalOpener.openExternal(encodeURI(o.toString(!0)))]}}))}))},e.prototype.dispose=function(){this._validators.clear()},e=p([f(0,l["a"]),f(1,u["b"])],e),e}()},"38a2":function(e,t,n){},"3d43":function(e,t,n){"use strict";n.d(t,"a",(function(){return s}));var o=n("6653"),i=n("4247"),r=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),s=function(e){function t(n){var i=e.call(this,n)||this,r=i._context.configuration.options,s=r.get(107);return i._canUseLayerHinting=!r.get(22),i._contentLeft=s.contentLeft,i._glyphMarginLeft=s.glyphMarginLeft,i._glyphMarginWidth=s.glyphMarginWidth,i._domNode=Object(o["b"])(document.createElement("div")),i._domNode.setClassName(t.OUTER_CLASS_NAME),i._domNode.setPosition("absolute"),i._domNode.setAttribute("role","presentation"),i._domNode.setAttribute("aria-hidden","true"),i._glyphMarginBackgroundDomNode=Object(o["b"])(document.createElement("div")),i._glyphMarginBackgroundDomNode.setClassName(t.CLASS_NAME),i._domNode.appendChild(i._glyphMarginBackgroundDomNode),i}return r(t,e),t.prototype.dispose=function(){e.prototype.dispose.call(this)},t.prototype.getDomNode=function(){return this._domNode},t.prototype.onConfigurationChanged=function(e){var t=this._context.configuration.options,n=t.get(107);return this._canUseLayerHinting=!t.get(22),this._contentLeft=n.contentLeft,this._glyphMarginLeft=n.glyphMarginLeft,this._glyphMarginWidth=n.glyphMarginWidth,!0},t.prototype.onScrollChanged=function(t){return e.prototype.onScrollChanged.call(this,t)||t.scrollTopChanged},t.prototype.prepareRender=function(e){},t.prototype.render=function(e){this._domNode.setLayerHinting(this._canUseLayerHinting),this._domNode.setContain("strict");var t=e.scrollTop-e.bigNumbersDelta;this._domNode.setTop(-t);var n=Math.min(e.scrollHeight,1e6);this._domNode.setHeight(n),this._domNode.setWidth(this._contentLeft),this._glyphMarginBackgroundDomNode.setLeft(this._glyphMarginLeft),this._glyphMarginBackgroundDomNode.setWidth(this._glyphMarginWidth),this._glyphMarginBackgroundDomNode.setHeight(n)},t.CLASS_NAME="glyph-margin",t.OUTER_CLASS_NAME="margin",t}(i["b"])},"411b":function(e,t,n){"use strict";n.d(t,"f",(function(){return h})),n.d(t,"a",(function(){return l})),n.d(t,"g",(function(){return d})),n.d(t,"b",(function(){return c})),n.d(t,"c",(function(){return p})),n.d(t,"d",(function(){return f})),n.d(t,"e",(function(){return g}));var o=n("11f7"),i=n("00a3"),r=n("5d28"),s=n("a666"),a=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),h=function(){function e(e,t){this.x=e,this.y=t}return e.prototype.toClientCoordinates=function(){return new l(this.x-o["e"].scrollX,this.y-o["e"].scrollY)},e}(),l=function(){function e(e,t){this.clientX=e,this.clientY=t}return e.prototype.toPageCoordinates=function(){return new h(this.clientX+o["e"].scrollX,this.clientY+o["e"].scrollY)},e}(),u=function(){function e(e,t,n,o){this.x=e,this.y=t,this.width=n,this.height=o}return e}();function d(e){var t=o["C"](e);return new u(t.left,t.top,t.width,t.height)}var c=function(e){function t(t,n){var o=e.call(this,t)||this;return o.pos=new h(o.posx,o.posy),o.editorPos=d(n),o}return a(t,e),t}(r["b"]),p=function(){function e(e){this._editorViewDomNode=e}return e.prototype._create=function(e){return new c(e,this._editorViewDomNode)},e.prototype.onContextMenu=function(e,t){var n=this;return o["j"](e,"contextmenu",(function(e){t(n._create(e))}))},e.prototype.onMouseUp=function(e,t){var n=this;return o["j"](e,"mouseup",(function(e){t(n._create(e))}))},e.prototype.onMouseDown=function(e,t){var n=this;return o["j"](e,"mousedown",(function(e){t(n._create(e))}))},e.prototype.onMouseLeave=function(e,t){var n=this;return o["k"](e,(function(e){t(n._create(e))}))},e.prototype.onMouseMoveThrottled=function(e,t,n,i){var r=this,s=function(e,t){return n(e,r._create(t))};return o["m"](e,"mousemove",t,s,i)},e}(),f=function(){function e(e){this._editorViewDomNode=e}return e.prototype._create=function(e){return new c(e,this._editorViewDomNode)},e.prototype.onPointerUp=function(e,t){var n=this;return o["j"](e,"pointerup",(function(e){t(n._create(e))}))},e.prototype.onPointerDown=function(e,t){var n=this;return o["j"](e,"pointerdown",(function(e){t(n._create(e))}))},e.prototype.onPointerLeave=function(e,t){var n=this;return o["l"](e,(function(e){t(n._create(e))}))},e.prototype.onPointerMoveThrottled=function(e,t,n,i){var r=this,s=function(e,t){return n(e,r._create(t))};return o["m"](e,"pointermove",t,s,i)},e}(),g=function(e){function t(t){var n=e.call(this)||this;return n._editorViewDomNode=t,n._globalMouseMoveMonitor=n._register(new i["a"]),n._keydownListener=null,n}return a(t,e),t.prototype.startMonitoring=function(e,t,n,i,r){var s=this;this._keydownListener=o["o"](document,"keydown",(function(e){var t=e.toKeybinding();t.isModifierKey()||s._globalMouseMoveMonitor.stopMonitoring(!0)}),!0);var a=function(e,t){return n(e,new c(t,s._editorViewDomNode))};this._globalMouseMoveMonitor.startMonitoring(e,t,a,i,(function(){s._keydownListener.dispose(),r()}))},t}(s["a"])},4247:function(e,t,n){"use strict";n.d(t,"b",(function(){return s})),n.d(t,"a",(function(){return a}));var o=n("6653"),i=n("e2dc"),r=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),s=function(e){function t(t){var n=e.call(this)||this;return n._context=t,n._context.addEventHandler(n),n}return r(t,e),t.prototype.dispose=function(){this._context.removeEventHandler(this),e.prototype.dispose.call(this)},t}(i["a"]),a=function(){function e(){}return e.write=function(e,t){o["a"],e.setAttribute("data-mprt",String(t))},e.read=function(e){var t=e.getAttribute("data-mprt");return null===t?0:parseInt(t,10)},e.collect=function(e,t){var n=[],o=0;while(e&&e!==document.body){if(e===t)break;e.nodeType===e.ELEMENT_NODE&&(n[o++]=this.read(e)),e=e.parentElement}for(var i=new Uint8Array(o),r=0;r<o;r++)i[r]=n[o-r-1];return i},e}()},"56dc":function(e,t,n){},5717:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var o=n("0a0f"),i=Object(o["c"])("codeEditorService")},"5d70":function(e,t,n){},"6ec9":function(e,t,n){"use strict";n.d(t,"a",(function(){return _})),n.d(t,"c",(function(){return m})),n.d(t,"b",(function(){return v}));var o=n("0f70"),i=n("6653"),r=n("30db"),s=n("a21f"),a=function(){function e(e,t){this.left=e,this.width=t}return e.prototype.toString=function(){return"["+this.left+","+this.width+"]"},e.compare=function(e,t){return e.left-t.left},e}(),h=function(){function e(){}return e._createRange=function(){return this._handyReadyRange||(this._handyReadyRange=document.createRange()),this._handyReadyRange},e._detachRange=function(e,t){e.selectNodeContents(t)},e._readClientRects=function(e,t,n,o,i){var r=this._createRange();try{return r.setStart(e,t),r.setEnd(n,o),r.getClientRects()}catch(s){return null}finally{this._detachRange(r,i)}},e._mergeAdjacentRanges=function(e){if(1===e.length)return[new s["b"](e[0].left,e[0].width)];e.sort(a.compare);for(var t=[],n=0,o=e[0].left,i=e[0].width,r=1,h=e.length;r<h;r++){var l=e[r],u=l.left,d=l.width;o+i+.9>=u?i=Math.max(i,u+d-o):(t[n++]=new s["b"](o,i),o=u,i=d)}return t[n++]=new s["b"](o,i),t},e._createHorizontalRangesFromClientRects=function(e,t){if(!e||0===e.length)return null;for(var n=[],o=0,i=e.length;o<i;o++){var r=e[o];n[o]=new a(Math.max(0,r.left-t),r.width)}return this._mergeAdjacentRanges(n)},e.readHorizontalRanges=function(e,t,n,o,i,r,s){var a=0,h=e.children.length-1;if(a>h)return null;t=Math.min(h,Math.max(a,t)),o=Math.min(h,Math.max(a,o)),t!==o&&o>0&&0===i&&(o--,i=1073741824);var l=e.children[t].firstChild,u=e.children[o].firstChild;if(l&&u||(!l&&0===n&&t>0&&(l=e.children[t-1].firstChild,n=1073741824),!u&&0===i&&o>0&&(u=e.children[o-1].firstChild,i=1073741824)),!l||!u)return null;n=Math.min(l.textContent.length,Math.max(0,n)),i=Math.min(u.textContent.length,Math.max(0,i));var d=this._readClientRects(l,n,u,i,s);return this._createHorizontalRangesFromClientRects(d,r)},e}(),l=n("7416"),u=n("6da2"),d=n("b7d0"),c=n("fd49"),p=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),f=function(){return!!r["f"]||!(r["d"]||o["h"]||o["k"])}(),g=o["f"],_=function(){function e(e,t){this._domNode=e,this._clientRectDeltaLeft=0,this._clientRectDeltaLeftRead=!1,this.endNode=t}return Object.defineProperty(e.prototype,"clientRectDeltaLeft",{get:function(){return this._clientRectDeltaLeftRead||(this._clientRectDeltaLeftRead=!0,this._clientRectDeltaLeft=this._domNode.getBoundingClientRect().left),this._clientRectDeltaLeft},enumerable:!0,configurable:!0}),e}(),m=function(){function e(e,t){this.themeType=t;var n=e.options,o=n.get(34);this.renderWhitespace=n.get(74),this.renderControlCharacters=n.get(69),this.spaceWidth=o.spaceWidth,this.middotWidth=o.middotWidth,this.useMonospaceOptimizations=o.isMonospace&&!n.get(23),this.canUseHalfwidthRightwardsArrow=o.canUseHalfwidthRightwardsArrow,this.lineHeight=n.get(49),this.stopRenderingLineAfter=n.get(88),this.fontLigatures=n.get(35)}return e.prototype.equals=function(e){return this.themeType===e.themeType&&this.renderWhitespace===e.renderWhitespace&&this.renderControlCharacters===e.renderControlCharacters&&this.spaceWidth===e.spaceWidth&&this.middotWidth===e.middotWidth&&this.useMonospaceOptimizations===e.useMonospaceOptimizations&&this.canUseHalfwidthRightwardsArrow===e.canUseHalfwidthRightwardsArrow&&this.lineHeight===e.lineHeight&&this.stopRenderingLineAfter===e.stopRenderingLineAfter&&this.fontLigatures===e.fontLigatures},e}(),v=function(){function e(e){this._options=e,this._isMaybeInvalid=!0,this._renderedViewLine=null}return e.prototype.getDomNode=function(){return this._renderedViewLine&&this._renderedViewLine.domNode?this._renderedViewLine.domNode.domNode:null},e.prototype.setDomNode=function(e){if(!this._renderedViewLine)throw new Error("I have no rendered view line to set the dom node to...");this._renderedViewLine.domNode=Object(i["b"])(e)},e.prototype.onContentChanged=function(){this._isMaybeInvalid=!0},e.prototype.onTokensChanged=function(){this._isMaybeInvalid=!0},e.prototype.onDecorationsChanged=function(){this._isMaybeInvalid=!0},e.prototype.onOptionsChanged=function(e){this._isMaybeInvalid=!0,this._options=e},e.prototype.onSelectionChanged=function(){return!(!g&&this._options.themeType!==d["b"]&&"selection"!==this._options.renderWhitespace)&&(this._isMaybeInvalid=!0,!0)},e.prototype.renderLine=function(t,n,o,i){if(!1===this._isMaybeInvalid)return!1;this._isMaybeInvalid=!1;var r=o.getViewLineRenderingData(t),s=this._options,a=l["a"].filter(r.inlineDecorations,t,r.minColumn,r.maxColumn),h=null;if(g||s.themeType===d["b"]||"selection"===this._options.renderWhitespace)for(var p=o.selections,_=0,m=p;_<m.length;_++){var v=m[_];if(!(v.endLineNumber<t||v.startLineNumber>t)){var C=v.startLineNumber===t?v.startColumn:r.minColumn,b=v.endLineNumber===t?v.endColumn:r.maxColumn;C<b&&("selection"!==this._options.renderWhitespace?a.push(new l["a"](C,b,"inline-selected-text",0)):(h||(h=[]),h.push(new u["b"](C-1,b-1))))}}var w=new u["c"](s.useMonospaceOptimizations,s.canUseHalfwidthRightwardsArrow,r.content,r.continuesWithWrappedLine,r.isBasicASCII,r.containsRTL,r.minColumn-1,r.tokens,a,r.tabSize,r.startVisibleColumn,s.spaceWidth,s.middotWidth,s.stopRenderingLineAfter,s.renderWhitespace,s.renderControlCharacters,s.fontLigatures!==c["d"].OFF,h);if(this._renderedViewLine&&this._renderedViewLine.input.equals(w))return!1;i.appendASCIIString('<div style="top:'),i.appendASCIIString(String(n)),i.appendASCIIString("px;height:"),i.appendASCIIString(String(this._options.lineHeight)),i.appendASCIIString('px;" class="'),i.appendASCIIString(e.CLASS_NAME),i.appendASCIIString('">');var L=Object(u["d"])(w,i);i.appendASCIIString("</div>");var S=null;return f&&r.isBasicASCII&&s.useMonospaceOptimizations&&0===L.containsForeignElements&&r.content.length<300&&w.lineTokens.getCount()<100&&(S=new y(this._renderedViewLine?this._renderedViewLine.domNode:null,w,L.characterMapping)),S||(S=N(this._renderedViewLine?this._renderedViewLine.domNode:null,w,L.characterMapping,L.containsRTL,L.containsForeignElements)),this._renderedViewLine=S,!0},e.prototype.layoutLine=function(e,t){this._renderedViewLine&&this._renderedViewLine.domNode&&(this._renderedViewLine.domNode.setTop(t),this._renderedViewLine.domNode.setHeight(this._options.lineHeight))},e.prototype.getWidth=function(){return this._renderedViewLine?this._renderedViewLine.getWidth():0},e.prototype.getWidthIsFast=function(){return!this._renderedViewLine||this._renderedViewLine.getWidthIsFast()},e.prototype.getVisibleRangesForRange=function(e,t,n){if(!this._renderedViewLine)return null;e|=0,t|=0,e=Math.min(this._renderedViewLine.input.lineContent.length+1,Math.max(1,e)),t=Math.min(this._renderedViewLine.input.lineContent.length+1,Math.max(1,t));var o=0|this._renderedViewLine.input.stopRenderingLineAfter,i=!1;-1!==o&&e>o+1&&t>o+1&&(i=!0),-1!==o&&e>o+1&&(e=o+1),-1!==o&&t>o+1&&(t=o+1);var r=this._renderedViewLine.getVisibleRangesForRange(e,t,n);return r&&r.length>0?new s["e"](i,r):null},e.prototype.getColumnOfNodeOffset=function(e,t,n){return this._renderedViewLine?this._renderedViewLine.getColumnOfNodeOffset(e,t,n):1},e.CLASS_NAME="view-line",e}(),y=function(){function e(e,t,n){this.domNode=e,this.input=t,this._characterMapping=n,this._charWidth=t.spaceWidth}return e.prototype.getWidth=function(){return this._getCharPosition(this._characterMapping.length)},e.prototype.getWidthIsFast=function(){return!0},e.prototype.getVisibleRangesForRange=function(e,t,n){var o=this._getCharPosition(e),i=this._getCharPosition(t);return[new s["b"](o,i-o)]},e.prototype._getCharPosition=function(e){var t=this._characterMapping.getAbsoluteOffsets();return 0===t.length?0:Math.round(this._charWidth*t[e-1])},e.prototype.getColumnOfNodeOffset=function(e,t,n){var o=t.textContent.length,i=-1;while(t)t=t.previousSibling,i++;var r=this._characterMapping.partDataToCharOffset(i,o,n);return r+1},e}(),C=function(){function e(e,t,n,o,i){if(this.domNode=e,this.input=t,this._characterMapping=n,this._isWhitespaceOnly=/^\s*$/.test(t.lineContent),this._containsForeignElements=i,this._cachedWidth=-1,this._pixelOffsetCache=null,!o||0===this._characterMapping.length){this._pixelOffsetCache=new Int32Array(Math.max(2,this._characterMapping.length+1));for(var r=0,s=this._characterMapping.length;r<=s;r++)this._pixelOffsetCache[r]=-1}}return e.prototype._getReadingTarget=function(e){return e.domNode.firstChild},e.prototype.getWidth=function(){return this.domNode?(-1===this._cachedWidth&&(this._cachedWidth=this._getReadingTarget(this.domNode).offsetWidth),this._cachedWidth):0},e.prototype.getWidthIsFast=function(){return-1!==this._cachedWidth},e.prototype.getVisibleRangesForRange=function(e,t,n){if(!this.domNode)return null;if(null!==this._pixelOffsetCache){var o=this._readPixelOffset(this.domNode,e,n);if(-1===o)return null;var i=this._readPixelOffset(this.domNode,t,n);return-1===i?null:[new s["b"](o,i-o)]}return this._readVisibleRangesForRange(this.domNode,e,t,n)},e.prototype._readVisibleRangesForRange=function(e,t,n,o){if(t===n){var i=this._readPixelOffset(e,t,o);return-1===i?null:[new s["b"](i,0)]}return this._readRawVisibleRangesForRange(e,t,n,o)},e.prototype._readPixelOffset=function(e,t,n){if(0===this._characterMapping.length){if(0===this._containsForeignElements)return 0;if(2===this._containsForeignElements)return 0;if(1===this._containsForeignElements)return this.getWidth();var o=this._getReadingTarget(e);return o.firstChild?o.firstChild.offsetWidth:0}if(null!==this._pixelOffsetCache){var i=this._pixelOffsetCache[t];if(-1!==i)return i;var r=this._actualReadPixelOffset(e,t,n);return this._pixelOffsetCache[t]=r,r}return this._actualReadPixelOffset(e,t,n)},e.prototype._actualReadPixelOffset=function(e,t,n){if(0===this._characterMapping.length){var o=h.readHorizontalRanges(this._getReadingTarget(e),0,0,0,0,n.clientRectDeltaLeft,n.endNode);return o&&0!==o.length?o[0].left:-1}if(t===this._characterMapping.length&&this._isWhitespaceOnly&&0===this._containsForeignElements)return this.getWidth();var i=this._characterMapping.charOffsetToPartData(t-1),r=u["a"].getPartIndex(i),s=u["a"].getCharIndex(i),a=h.readHorizontalRanges(this._getReadingTarget(e),r,s,r,s,n.clientRectDeltaLeft,n.endNode);return a&&0!==a.length?a[0].left:-1},e.prototype._readRawVisibleRangesForRange=function(e,t,n,o){if(1===t&&n===this._characterMapping.length)return[new s["b"](0,this.getWidth())];var i=this._characterMapping.charOffsetToPartData(t-1),r=u["a"].getPartIndex(i),a=u["a"].getCharIndex(i),l=this._characterMapping.charOffsetToPartData(n-1),d=u["a"].getPartIndex(l),c=u["a"].getCharIndex(l);return h.readHorizontalRanges(this._getReadingTarget(e),r,a,d,c,o.clientRectDeltaLeft,o.endNode)},e.prototype.getColumnOfNodeOffset=function(e,t,n){var o=t.textContent.length,i=-1;while(t)t=t.previousSibling,i++;var r=this._characterMapping.partDataToCharOffset(i,o,n);return r+1},e}(),b=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return p(t,e),t.prototype._readVisibleRangesForRange=function(t,n,o,i){var r=e.prototype._readVisibleRangesForRange.call(this,t,n,o,i);if(!r||0===r.length||n===o||1===n&&o===this._characterMapping.length)return r;if(!this.input.containsRTL){var s=this._readPixelOffset(t,o,i);if(-1!==s){var a=r[r.length-1];a.left<s&&(a.width=s-a.left)}}return r},t}(C),N=function(){return o["m"]?w:L}();function w(e,t,n,o,i){return new b(e,t,n,o,i)}function L(e,t,n,o,i){return new C(e,t,n,o,i)}},"725e":function(e,t,n){},7608:function(e,t,n){"use strict";n.d(t,"a",(function(){return s}));var o=n("a666"),i=n("bae1"),r=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),s=function(e){function t(t){var n=e.call(this)||this;return n.onDidContentSizeChange=null,n.onDidScroll=null,n.onDidGainFocus=null,n.onDidLoseFocus=null,n.onKeyDown=null,n.onKeyUp=null,n.onContextMenu=null,n.onMouseMove=null,n.onMouseLeave=null,n.onMouseUp=null,n.onMouseDown=null,n.onMouseDrag=null,n.onMouseDrop=null,n.onMouseWheel=null,n._viewModel=t,n}return r(t,e),t.prototype.emitContentSizeChange=function(e){this.onDidContentSizeChange&&this.onDidContentSizeChange(e)},t.prototype.emitScrollChanged=function(e){this.onDidScroll&&this.onDidScroll(e)},t.prototype.emitViewFocusGained=function(){this.onDidGainFocus&&this.onDidGainFocus(void 0)},t.prototype.emitViewFocusLost=function(){this.onDidLoseFocus&&this.onDidLoseFocus(void 0)},t.prototype.emitKeyDown=function(e){this.onKeyDown&&this.onKeyDown(e)},t.prototype.emitKeyUp=function(e){this.onKeyUp&&this.onKeyUp(e)},t.prototype.emitContextMenu=function(e){this.onContextMenu&&this.onContextMenu(this._convertViewToModelMouseEvent(e))},t.prototype.emitMouseMove=function(e){this.onMouseMove&&this.onMouseMove(this._convertViewToModelMouseEvent(e))},t.prototype.emitMouseLeave=function(e){this.onMouseLeave&&this.onMouseLeave(this._convertViewToModelMouseEvent(e))},t.prototype.emitMouseUp=function(e){this.onMouseUp&&this.onMouseUp(this._convertViewToModelMouseEvent(e))},t.prototype.emitMouseDown=function(e){this.onMouseDown&&this.onMouseDown(this._convertViewToModelMouseEvent(e))},t.prototype.emitMouseDrag=function(e){this.onMouseDrag&&this.onMouseDrag(this._convertViewToModelMouseEvent(e))},t.prototype.emitMouseDrop=function(e){this.onMouseDrop&&this.onMouseDrop(this._convertViewToModelMouseEvent(e))},t.prototype.emitMouseWheel=function(e){this.onMouseWheel&&this.onMouseWheel(e)},t.prototype._convertViewToModelMouseEvent=function(e){return e.target?{event:e.event,target:this._convertViewToModelMouseTarget(e.target)}:e},t.prototype._convertViewToModelMouseTarget=function(e){return t.convertViewToModelMouseTarget(e,this._viewModel.coordinatesConverter)},t.convertViewToModelMouseTarget=function(e,t){return new a(e.element,e.type,e.mouseColumn,e.position?t.convertViewPositionToModelPosition(e.position):null,e.range?t.convertViewRangeToModelRange(e.range):null,e.detail)},t}(o["a"]),a=function(){function e(e,t,n,o,i,r){this.element=e,this.type=t,this.mouseColumn=n,this.position=o,this.range=i,this.detail=r}return e.prototype.toString=function(){return i["b"].toString(this)},e}()},"782d":function(e,t,n){},"7e97":function(e,t,n){"use strict";n.d(t,"a",(function(){return vt}));var o=n("11f7"),i=n("6653"),r=n("fdcc"),s=n("9904"),a=n("e393"),h=n("d585"),l=n("7061"),u=n("30db"),d=function(){function e(e,t,n,o){this.configuration=e,this.viewModel=t,this.outgoingEvents=n,this.commandDelegate=o}return e.prototype._execMouseCommand=function(e,t){t.source="mouse",this.commandDelegate.executeEditorCommand(e,t)},e.prototype.paste=function(e,t,n,o,i){this.commandDelegate.paste(e,t,n,o,i)},e.prototype.type=function(e,t){this.commandDelegate.type(e,t)},e.prototype.replacePreviousChar=function(e,t,n){this.commandDelegate.replacePreviousChar(e,t,n)},e.prototype.compositionStart=function(e){this.commandDelegate.compositionStart(e)},e.prototype.compositionEnd=function(e){this.commandDelegate.compositionEnd(e)},e.prototype.cut=function(e){this.commandDelegate.cut(e)},e.prototype.setSelection=function(e,t){this.commandDelegate.executeEditorCommand(h["CoreNavigationCommands"].SetSelection,{source:e,selection:t})},e.prototype._validateViewColumn=function(e){var t=this.viewModel.getLineMinColumn(e.lineNumber);return e.column<t?new l["a"](e.lineNumber,t):e},e.prototype._hasMulticursorModifier=function(e){switch(this.configuration.options.get(59)){case"altKey":return e.altKey;case"ctrlKey":return e.ctrlKey;case"metaKey":return e.metaKey}return!1},e.prototype._hasNonMulticursorModifier=function(e){switch(this.configuration.options.get(59)){case"altKey":return e.ctrlKey||e.metaKey;case"ctrlKey":return e.altKey||e.metaKey;case"metaKey":return e.ctrlKey||e.altKey}return!1},e.prototype.dispatchMouse=function(e){var t=u["d"]&&this.configuration.options.get(81);e.middleButton&&!t?this._columnSelect(e.position,e.mouseColumn,e.inSelectionMode):e.startedOnLineNumbers?this._hasMulticursorModifier(e)?e.inSelectionMode?this._lastCursorLineSelect(e.position):this._createCursor(e.position,!0):e.inSelectionMode?this._lineSelectDrag(e.position):this._lineSelect(e.position):e.mouseDownCount>=4?this._selectAll():3===e.mouseDownCount?this._hasMulticursorModifier(e)?e.inSelectionMode?this._lastCursorLineSelectDrag(e.position):this._lastCursorLineSelect(e.position):e.inSelectionMode?this._lineSelectDrag(e.position):this._lineSelect(e.position):2===e.mouseDownCount?this._hasMulticursorModifier(e)?this._lastCursorWordSelect(e.position):e.inSelectionMode?this._wordSelectDrag(e.position):this._wordSelect(e.position):this._hasMulticursorModifier(e)?this._hasNonMulticursorModifier(e)||(e.shiftKey?this._columnSelect(e.position,e.mouseColumn,!0):e.inSelectionMode?this._lastCursorMoveToSelect(e.position):this._createCursor(e.position,!1)):e.inSelectionMode?e.altKey?this._columnSelect(e.position,e.mouseColumn,!0):this._moveToSelect(e.position):this.moveTo(e.position)},e.prototype._usualArgs=function(e){return e=this._validateViewColumn(e),{position:this._convertViewToModelPosition(e),viewPosition:e}},e.prototype.moveTo=function(e){this._execMouseCommand(h["CoreNavigationCommands"].MoveTo,this._usualArgs(e))},e.prototype._moveToSelect=function(e){this._execMouseCommand(h["CoreNavigationCommands"].MoveToSelect,this._usualArgs(e))},e.prototype._columnSelect=function(e,t,n){e=this._validateViewColumn(e),this._execMouseCommand(h["CoreNavigationCommands"].ColumnSelect,{position:this._convertViewToModelPosition(e),viewPosition:e,mouseColumn:t,doColumnSelect:n})},e.prototype._createCursor=function(e,t){e=this._validateViewColumn(e),this._execMouseCommand(h["CoreNavigationCommands"].CreateCursor,{position:this._convertViewToModelPosition(e),viewPosition:e,wholeLine:t})},e.prototype._lastCursorMoveToSelect=function(e){this._execMouseCommand(h["CoreNavigationCommands"].LastCursorMoveToSelect,this._usualArgs(e))},e.prototype._wordSelect=function(e){this._execMouseCommand(h["CoreNavigationCommands"].WordSelect,this._usualArgs(e))},e.prototype._wordSelectDrag=function(e){this._execMouseCommand(h["CoreNavigationCommands"].WordSelectDrag,this._usualArgs(e))},e.prototype._lastCursorWordSelect=function(e){this._execMouseCommand(h["CoreNavigationCommands"].LastCursorWordSelect,this._usualArgs(e))},e.prototype._lineSelect=function(e){this._execMouseCommand(h["CoreNavigationCommands"].LineSelect,this._usualArgs(e))},e.prototype._lineSelectDrag=function(e){this._execMouseCommand(h["CoreNavigationCommands"].LineSelectDrag,this._usualArgs(e))},e.prototype._lastCursorLineSelect=function(e){this._execMouseCommand(h["CoreNavigationCommands"].LastCursorLineSelect,this._usualArgs(e))},e.prototype._lastCursorLineSelectDrag=function(e){this._execMouseCommand(h["CoreNavigationCommands"].LastCursorLineSelectDrag,this._usualArgs(e))},e.prototype._selectAll=function(){this._execMouseCommand(h["CoreNavigationCommands"].SelectAll,{})},e.prototype._convertViewToModelPosition=function(e){return this.viewModel.coordinatesConverter.convertViewPositionToModelPosition(e)},e.prototype.emitKeyDown=function(e){this.outgoingEvents.emitKeyDown(e)},e.prototype.emitKeyUp=function(e){this.outgoingEvents.emitKeyUp(e)},e.prototype.emitContextMenu=function(e){this.outgoingEvents.emitContextMenu(e)},e.prototype.emitMouseMove=function(e){this.outgoingEvents.emitMouseMove(e)},e.prototype.emitMouseLeave=function(e){this.outgoingEvents.emitMouseLeave(e)},e.prototype.emitMouseUp=function(e){this.outgoingEvents.emitMouseUp(e)},e.prototype.emitMouseDown=function(e){this.outgoingEvents.emitMouseDown(e)},e.prototype.emitMouseDrag=function(e){this.outgoingEvents.emitMouseDrag(e)},e.prototype.emitMouseDrop=function(e){this.outgoingEvents.emitMouseDrop(e)},e.prototype.emitMouseWheel=function(e){this.outgoingEvents.emitMouseWheel(e)},e}(),c=n("7608"),p=n("1ddc"),f=n("7ab3"),g=function(){function e(e){this._createLine=e,this._set(1,[])}return e.prototype.flush=function(){this._set(1,[])},e.prototype._set=function(e,t){this._lines=t,this._rendLineNumberStart=e},e.prototype._get=function(){return{rendLineNumberStart:this._rendLineNumberStart,lines:this._lines}},e.prototype.getStartLineNumber=function(){return this._rendLineNumberStart},e.prototype.getEndLineNumber=function(){return this._rendLineNumberStart+this._lines.length-1},e.prototype.getCount=function(){return this._lines.length},e.prototype.getLine=function(e){var t=e-this._rendLineNumberStart;if(t<0||t>=this._lines.length)throw new Error("Illegal value for lineNumber");return this._lines[t]},e.prototype.onLinesDeleted=function(e,t){if(0===this.getCount())return null;var n=this.getStartLineNumber(),o=this.getEndLineNumber();if(t<n){var i=t-e+1;return this._rendLineNumberStart-=i,null}if(e>o)return null;for(var r=0,s=0,a=n;a<=o;a++){var h=a-this._rendLineNumberStart;e<=a&&a<=t&&(0===s?(r=h,s=1):s++)}if(e<n){var l=0;l=t<n?t-e+1:n-e,this._rendLineNumberStart-=l}var u=this._lines.splice(r,s);return u},e.prototype.onLinesChanged=function(e,t){if(0===this.getCount())return!1;for(var n=this.getStartLineNumber(),o=this.getEndLineNumber(),i=!1,r=e;r<=t;r++)r>=n&&r<=o&&(this._lines[r-this._rendLineNumberStart].onContentChanged(),i=!0);return i},e.prototype.onLinesInserted=function(e,t){if(0===this.getCount())return null;var n=t-e+1,o=this.getStartLineNumber(),i=this.getEndLineNumber();if(e<=o)return this._rendLineNumberStart+=n,null;if(e>i)return null;if(n+e>i){var r=this._lines.splice(e-this._rendLineNumberStart,i-e+1);return r}for(var s=[],a=0;a<n;a++)s[a]=this._createLine();var h=e-this._rendLineNumberStart,l=this._lines.slice(0,h),u=this._lines.slice(h,this._lines.length-n),d=this._lines.slice(this._lines.length-n,this._lines.length);return this._lines=l.concat(s).concat(u),d},e.prototype.onTokensChanged=function(e){if(0===this.getCount())return!1;for(var t=this.getStartLineNumber(),n=this.getEndLineNumber(),o=!1,i=0,r=e.length;i<r;i++){var s=e[i];if(!(s.toLineNumber<t||s.fromLineNumber>n))for(var a=Math.max(t,s.fromLineNumber),h=Math.min(n,s.toLineNumber),l=a;l<=h;l++){var u=l-this._rendLineNumberStart;this._lines[u].onTokensChanged(),o=!0}}return o},e}(),_=function(){function e(e){var t=this;this._host=e,this.domNode=this._createDomNode(),this._linesCollection=new g((function(){return t._host.createVisibleLine()}))}return e.prototype._createDomNode=function(){var e=Object(i["b"])(document.createElement("div"));return e.setClassName("view-layer"),e.setPosition("absolute"),e.domNode.setAttribute("role","presentation"),e.domNode.setAttribute("aria-hidden","true"),e},e.prototype.onConfigurationChanged=function(e){return!!e.hasChanged(107)},e.prototype.onFlushed=function(e){return this._linesCollection.flush(),!0},e.prototype.onLinesChanged=function(e){return this._linesCollection.onLinesChanged(e.fromLineNumber,e.toLineNumber)},e.prototype.onLinesDeleted=function(e){var t=this._linesCollection.onLinesDeleted(e.fromLineNumber,e.toLineNumber);if(t)for(var n=0,o=t.length;n<o;n++){var i=t[n].getDomNode();i&&this.domNode.domNode.removeChild(i)}return!0},e.prototype.onLinesInserted=function(e){var t=this._linesCollection.onLinesInserted(e.fromLineNumber,e.toLineNumber);if(t)for(var n=0,o=t.length;n<o;n++){var i=t[n].getDomNode();i&&this.domNode.domNode.removeChild(i)}return!0},e.prototype.onScrollChanged=function(e){return e.scrollTopChanged},e.prototype.onTokensChanged=function(e){return this._linesCollection.onTokensChanged(e.ranges)},e.prototype.onZonesChanged=function(e){return!0},e.prototype.getStartLineNumber=function(){return this._linesCollection.getStartLineNumber()},e.prototype.getEndLineNumber=function(){return this._linesCollection.getEndLineNumber()},e.prototype.getVisibleLine=function(e){return this._linesCollection.getLine(e)},e.prototype.renderLines=function(e){var t=this._linesCollection._get(),n=new m(this.domNode.domNode,this._host,e),o={rendLineNumberStart:t.rendLineNumberStart,lines:t.lines,linesLength:t.lines.length},i=n.render(o,e.startLineNumber,e.endLineNumber,e.relativeVerticalOffset);this._linesCollection._set(i.rendLineNumberStart,i.lines)},e}(),m=function(){function e(e,t,n){this.domNode=e,this.host=t,this.viewportData=n}return e.prototype.render=function(e,t,n,o){var i={rendLineNumberStart:e.rendLineNumberStart,lines:e.lines.slice(0),linesLength:e.linesLength};if(i.rendLineNumberStart+i.linesLength-1<t||n<i.rendLineNumberStart){i.rendLineNumberStart=t,i.linesLength=n-t+1,i.lines=[];for(var r=t;r<=n;r++)i.lines[r-t]=this.host.createVisibleLine();return this._finishRendering(i,!0,o),i}if(this._renderUntouchedLines(i,Math.max(t-i.rendLineNumberStart,0),Math.min(n-i.rendLineNumberStart,i.linesLength-1),o,t),i.rendLineNumberStart>t){var s=t,a=Math.min(n,i.rendLineNumberStart-1);s<=a&&(this._insertLinesBefore(i,s,a,o,t),i.linesLength+=a-s+1)}else if(i.rendLineNumberStart<t){var h=Math.min(i.linesLength,t-i.rendLineNumberStart);h>0&&(this._removeLinesBefore(i,h),i.linesLength-=h)}if(i.rendLineNumberStart=t,i.rendLineNumberStart+i.linesLength-1<n){s=i.rendLineNumberStart+i.linesLength,a=n;s<=a&&(this._insertLinesAfter(i,s,a,o,t),i.linesLength+=a-s+1)}else if(i.rendLineNumberStart+i.linesLength-1>n){s=Math.max(0,n-i.rendLineNumberStart+1),a=i.linesLength-1,h=a-s+1;h>0&&(this._removeLinesAfter(i,h),i.linesLength-=h)}return this._finishRendering(i,!1,o),i},e.prototype._renderUntouchedLines=function(e,t,n,o,i){for(var r=e.rendLineNumberStart,s=e.lines,a=t;a<=n;a++){var h=r+a;s[a].layoutLine(h,o[h-i])}},e.prototype._insertLinesBefore=function(e,t,n,o,i){for(var r=[],s=0,a=t;a<=n;a++)r[s++]=this.host.createVisibleLine();e.lines=r.concat(e.lines)},e.prototype._removeLinesBefore=function(e,t){for(var n=0;n<t;n++){var o=e.lines[n].getDomNode();o&&this.domNode.removeChild(o)}e.lines.splice(0,t)},e.prototype._insertLinesAfter=function(e,t,n,o,i){for(var r=[],s=0,a=t;a<=n;a++)r[s++]=this.host.createVisibleLine();e.lines=e.lines.concat(r)},e.prototype._removeLinesAfter=function(e,t){for(var n=e.linesLength-t,o=0;o<t;o++){var i=e.lines[n+o].getDomNode();i&&this.domNode.removeChild(i)}e.lines.splice(n,t)},e.prototype._finishRenderingNewLines=function(e,t,n,o){var i=this.domNode.lastChild;t||!i?this.domNode.innerHTML=n:i.insertAdjacentHTML("afterend",n);for(var r=this.domNode.lastChild,s=e.linesLength-1;s>=0;s--){var a=e.lines[s];o[s]&&(a.setDomNode(r),r=r.previousSibling)}},e.prototype._finishRenderingInvalidLines=function(e,t,n){var o=document.createElement("div");o.innerHTML=t;for(var i=0;i<e.linesLength;i++){var r=e.lines[i];if(n[i]){var s=o.firstChild,a=r.getDomNode();a.parentNode.replaceChild(s,a),r.setDomNode(s)}}},e.prototype._finishRendering=function(t,n,o){var i=e._sb,r=t.linesLength,s=t.lines,a=t.rendLineNumberStart,h=[];i.reset();for(var l=!1,u=0;u<r;u++){var d=s[u];h[u]=!1;var c=d.getDomNode();if(!c){var p=d.renderLine(u+a,o[u],this.viewportData,i);p&&(h[u]=!0,l=!0)}}l&&this._finishRenderingNewLines(t,n,i.build(),h),i.reset();var f=!1,g=[];for(u=0;u<r;u++){d=s[u];if(g[u]=!1,!h[u]){p=d.renderLine(u+a,o[u],this.viewportData,i);p&&(g[u]=!0,f=!0)}}f&&this._finishRenderingInvalidLines(t,i.build(),g)},e._sb=Object(f["a"])(1e5),e}(),v=n("4247"),y=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),C=function(e){function t(t){var n=e.call(this,t)||this;return n._visibleLines=new _(n),n.domNode=n._visibleLines.domNode,n._dynamicOverlays=[],n._isFocused=!1,n.domNode.setClassName("view-overlays"),n}return y(t,e),t.prototype.shouldRender=function(){if(e.prototype.shouldRender.call(this))return!0;for(var t=0,n=this._dynamicOverlays.length;t<n;t++){var o=this._dynamicOverlays[t];if(o.shouldRender())return!0}return!1},t.prototype.dispose=function(){e.prototype.dispose.call(this);for(var t=0,n=this._dynamicOverlays.length;t<n;t++){var o=this._dynamicOverlays[t];o.dispose()}this._dynamicOverlays=[]},t.prototype.getDomNode=function(){return this.domNode},t.prototype.createVisibleLine=function(){return new b(this._context.configuration,this._dynamicOverlays)},t.prototype.addDynamicOverlay=function(e){this._dynamicOverlays.push(e)},t.prototype.onConfigurationChanged=function(e){this._visibleLines.onConfigurationChanged(e);for(var t=this._visibleLines.getStartLineNumber(),n=this._visibleLines.getEndLineNumber(),o=t;o<=n;o++){var i=this._visibleLines.getVisibleLine(o);i.onConfigurationChanged(e)}return!0},t.prototype.onFlushed=function(e){return this._visibleLines.onFlushed(e)},t.prototype.onFocusChanged=function(e){return this._isFocused=e.isFocused,!0},t.prototype.onLinesChanged=function(e){return this._visibleLines.onLinesChanged(e)},t.prototype.onLinesDeleted=function(e){return this._visibleLines.onLinesDeleted(e)},t.prototype.onLinesInserted=function(e){return this._visibleLines.onLinesInserted(e)},t.prototype.onScrollChanged=function(e){return this._visibleLines.onScrollChanged(e)||!0},t.prototype.onTokensChanged=function(e){return this._visibleLines.onTokensChanged(e)},t.prototype.onZonesChanged=function(e){return this._visibleLines.onZonesChanged(e)},t.prototype.prepareRender=function(e){for(var t=this._dynamicOverlays.filter((function(e){return e.shouldRender()})),n=0,o=t.length;n<o;n++){var i=t[n];i.prepareRender(e),i.onDidRender()}},t.prototype.render=function(e){this._viewOverlaysRender(e),this.domNode.toggleClassName("focused",this._isFocused)},t.prototype._viewOverlaysRender=function(e){this._visibleLines.renderLines(e.viewportData)},t}(v["b"]),b=function(){function e(e,t){this._configuration=e,this._lineHeight=this._configuration.options.get(49),this._dynamicOverlays=t,this._domNode=null,this._renderedContent=null}return e.prototype.getDomNode=function(){return this._domNode?this._domNode.domNode:null},e.prototype.setDomNode=function(e){this._domNode=Object(i["b"])(e)},e.prototype.onContentChanged=function(){},e.prototype.onTokensChanged=function(){},e.prototype.onConfigurationChanged=function(e){this._lineHeight=this._configuration.options.get(49)},e.prototype.renderLine=function(e,t,n,o){for(var i="",r=0,s=this._dynamicOverlays.length;r<s;r++){var a=this._dynamicOverlays[r];i+=a.render(n.startLineNumber,e)}return this._renderedContent!==i&&(this._renderedContent=i,o.appendASCIIString('<div style="position:absolute;top:'),o.appendASCIIString(String(t)),o.appendASCIIString("px;width:100%;height:"),o.appendASCIIString(String(this._lineHeight)),o.appendASCIIString('px;">'),o.appendASCIIString(i),o.appendASCIIString("</div>"),!0)},e.prototype.layoutLine=function(e,t){this._domNode&&(this._domNode.setTop(t),this._domNode.setHeight(this._lineHeight))},e}(),N=function(e){function t(t){var n=e.call(this,t)||this,o=n._context.configuration.options,i=o.get(107);return n._contentWidth=i.contentWidth,n.domNode.setHeight(0),n}return y(t,e),t.prototype.onConfigurationChanged=function(t){var n=this._context.configuration.options,o=n.get(107);return this._contentWidth=o.contentWidth,e.prototype.onConfigurationChanged.call(this,t)||!0},t.prototype.onScrollChanged=function(t){return e.prototype.onScrollChanged.call(this,t)||t.scrollWidthChanged},t.prototype._viewOverlaysRender=function(t){e.prototype._viewOverlaysRender.call(this,t),this.domNode.setWidth(Math.max(t.scrollWidth,this._contentWidth))},t}(C),w=function(e){function t(t){var n=e.call(this,t)||this,o=n._context.configuration.options,i=o.get(107);return n._contentLeft=i.contentLeft,n.domNode.setClassName("margin-view-overlays"),n.domNode.setWidth(1),p["a"].applyFontInfo(n.domNode,o.get(34)),n}return y(t,e),t.prototype.onConfigurationChanged=function(t){var n=this._context.configuration.options;p["a"].applyFontInfo(this.domNode,n.get(34));var o=n.get(107);return this._contentLeft=o.contentLeft,e.prototype.onConfigurationChanged.call(this,t)||!0},t.prototype.onScrollChanged=function(t){return e.prototype.onScrollChanged.call(this,t)||t.scrollHeightChanged},t.prototype._viewOverlaysRender=function(t){e.prototype._viewOverlaysRender.call(this,t);var n=Math.min(t.scrollHeight,1e6);this.domNode.setHeight(n),this.domNode.setWidth(this._contentLeft)},t}(C),L=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),S=function(){function e(e,t){this.top=e,this.left=t}return e}(),D=function(e){function t(t,n){var o=e.call(this,t)||this;return o._viewDomNode=n,o._widgets={},o.domNode=Object(i["b"])(document.createElement("div")),v["a"].write(o.domNode,1),o.domNode.setClassName("contentWidgets"),o.domNode.setPosition("absolute"),o.domNode.setTop(0),o.overflowingContentWidgetsDomNode=Object(i["b"])(document.createElement("div")),v["a"].write(o.overflowingContentWidgetsDomNode,2),o.overflowingContentWidgetsDomNode.setClassName("overflowingContentWidgets"),o}return L(t,e),t.prototype.dispose=function(){e.prototype.dispose.call(this),this._widgets={}},t.prototype.onConfigurationChanged=function(e){for(var t=Object.keys(this._widgets),n=0,o=t;n<o.length;n++){var i=o[n];this._widgets[i].onConfigurationChanged(e)}return!0},t.prototype.onDecorationsChanged=function(e){return!0},t.prototype.onFlushed=function(e){return!0},t.prototype.onLineMappingChanged=function(e){for(var t=Object.keys(this._widgets),n=0,o=t;n<o.length;n++){var i=o[n];this._widgets[i].onLineMappingChanged(e)}return!0},t.prototype.onLinesChanged=function(e){return!0},t.prototype.onLinesDeleted=function(e){return!0},t.prototype.onLinesInserted=function(e){return!0},t.prototype.onScrollChanged=function(e){return!0},t.prototype.onZonesChanged=function(e){return!0},t.prototype.addWidget=function(e){var t=new R(this._context,this._viewDomNode,e);this._widgets[t.id]=t,t.allowEditorOverflow?this.overflowingContentWidgetsDomNode.appendChild(t.domNode):this.domNode.appendChild(t.domNode),this.setShouldRender()},t.prototype.setWidgetPosition=function(e,t,n){var o=this._widgets[e.getId()];o.setPosition(t,n),this.setShouldRender()},t.prototype.removeWidget=function(e){var t=e.getId();if(this._widgets.hasOwnProperty(t)){var n=this._widgets[t];delete this._widgets[t];var o=n.domNode.domNode;o.parentNode.removeChild(o),o.removeAttribute("monaco-visible-content-widget"),this.setShouldRender()}},t.prototype.shouldSuppressMouseDownOnWidget=function(e){return!!this._widgets.hasOwnProperty(e)&&this._widgets[e].suppressMouseDown},t.prototype.onBeforeRender=function(e){for(var t=Object.keys(this._widgets),n=0,o=t;n<o.length;n++){var i=o[n];this._widgets[i].onBeforeRender(e)}},t.prototype.prepareRender=function(e){for(var t=Object.keys(this._widgets),n=0,o=t;n<o.length;n++){var i=o[n];this._widgets[i].prepareRender(e)}},t.prototype.render=function(e){for(var t=Object.keys(this._widgets),n=0,o=t;n<o.length;n++){var i=o[n];this._widgets[i].render(e)}},t}(v["b"]),R=function(){function e(e,t,n){this._context=e,this._viewDomNode=t,this._actual=n,this.domNode=Object(i["b"])(this._actual.getDomNode()),this.id=this._actual.getId(),this.allowEditorOverflow=this._actual.allowEditorOverflow||!1,this.suppressMouseDown=this._actual.suppressMouseDown||!1;var o=this._context.configuration.options,r=o.get(107);this._fixedOverflowWidgets=o.get(29),this._contentWidth=r.contentWidth,this._contentLeft=r.contentLeft,this._lineHeight=o.get(49),this._range=null,this._viewRange=null,this._preference=[],this._cachedDomNodeClientWidth=-1,this._cachedDomNodeClientHeight=-1,this._maxWidth=this._getMaxWidth(),this._isVisible=!1,this._renderData=null,this.domNode.setPosition(this._fixedOverflowWidgets&&this.allowEditorOverflow?"fixed":"absolute"),this.domNode.setVisibility("hidden"),this.domNode.setAttribute("widgetId",this.id),this.domNode.setMaxWidth(this._maxWidth)}return e.prototype.onConfigurationChanged=function(e){var t=this._context.configuration.options;if(this._lineHeight=t.get(49),e.hasChanged(107)){var n=t.get(107);this._contentLeft=n.contentLeft,this._contentWidth=n.contentWidth,this._maxWidth=this._getMaxWidth()}},e.prototype.onLineMappingChanged=function(e){this._setPosition(this._range)},e.prototype._setPosition=function(e){if(this._range=e,this._viewRange=null,this._range){var t=this._context.model.validateModelRange(this._range);(this._context.model.coordinatesConverter.modelPositionIsVisible(t.getStartPosition())||this._context.model.coordinatesConverter.modelPositionIsVisible(t.getEndPosition()))&&(this._viewRange=this._context.model.coordinatesConverter.convertModelRangeToViewRange(t))}},e.prototype._getMaxWidth=function(){return this.allowEditorOverflow?window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth:this._contentWidth},e.prototype.setPosition=function(e,t){this._setPosition(e),this._preference=t,this._cachedDomNodeClientWidth=-1,this._cachedDomNodeClientHeight=-1},e.prototype._layoutBoxInViewport=function(e,t,n,o,i){var r=e.top,s=r,a=t.top+this._lineHeight,h=i.viewportHeight-a,l=r-o,u=s>=o,d=a,c=h>=o,p=e.left,f=t.left;return p+n>i.scrollLeft+i.viewportWidth&&(p=i.scrollLeft+i.viewportWidth-n),f+n>i.scrollLeft+i.viewportWidth&&(f=i.scrollLeft+i.viewportWidth-n),p<i.scrollLeft&&(p=i.scrollLeft),f<i.scrollLeft&&(f=i.scrollLeft),{fitsAbove:u,aboveTop:l,aboveLeft:p,fitsBelow:c,belowTop:d,belowLeft:f}},e.prototype._layoutHorizontalSegmentInPage=function(e,t,n,i){var r=Math.max(0,t.left-i),s=Math.min(t.left+t.width+i,e.width),a=t.left+n-o["e"].scrollX;if(a+i>s){var h=a-(s-i);a-=h,n-=h}if(a<r){h=a-r;a-=h,n-=h}return[n,a]},e.prototype._layoutBoxInPage=function(e,t,n,i,r){var s=e.top-i,a=t.top+this._lineHeight,h=o["C"](this._viewDomNode.domNode),l=h.top+s-o["e"].scrollY,u=h.top+a-o["e"].scrollY,d=o["y"](document.body),c=this._layoutHorizontalSegmentInPage(d,h,e.left-r.scrollLeft+this._contentLeft,n),p=c[0],f=c[1],g=this._layoutHorizontalSegmentInPage(d,h,t.left-r.scrollLeft+this._contentLeft,n),_=g[0],m=g[1],v=22,y=22,C=l>=v,b=u+i<=d.height-y;return this._fixedOverflowWidgets?{fitsAbove:C,aboveTop:Math.max(l,v),aboveLeft:f,fitsBelow:b,belowTop:u,belowLeft:m}:{fitsAbove:C,aboveTop:Math.max(s,v),aboveLeft:p,fitsBelow:b,belowTop:a,belowLeft:_}},e.prototype._prepareRenderWidgetAtExactPositionOverflowing=function(e){return new S(e.top,e.left+this._contentLeft)},e.prototype._getTopAndBottomLeft=function(e){if(!this._viewRange)return[null,null];var t=e.linesVisibleRangesForRange(this._viewRange,!1);if(!t||0===t.length)return[null,null];for(var n=t[0],o=t[0],i=0,r=t;i<r.length;i++){var s=r[i];s.lineNumber<n.lineNumber&&(n=s),s.lineNumber>o.lineNumber&&(o=s)}for(var a=1073741824,h=0,l=n.ranges;h<l.length;h++){var u=l[h];u.left<a&&(a=u.left)}for(var d=1073741824,c=0,p=o.ranges;c<p.length;c++){u=p[c];u.left<d&&(d=u.left)}var f=e.getVerticalOffsetForLineNumber(n.lineNumber)-e.scrollTop,g=new S(f,a),_=e.getVerticalOffsetForLineNumber(o.lineNumber)-e.scrollTop,m=new S(_,d);return[g,m]},e.prototype._prepareRenderWidget=function(e){var t,n=this._getTopAndBottomLeft(e),o=n[0],i=n[1];if(!o||!i)return null;if(-1===this._cachedDomNodeClientWidth||-1===this._cachedDomNodeClientHeight){var r=this.domNode.domNode;this._cachedDomNodeClientWidth=r.clientWidth,this._cachedDomNodeClientHeight=r.clientHeight}if(t=this.allowEditorOverflow?this._layoutBoxInPage(o,i,this._cachedDomNodeClientWidth,this._cachedDomNodeClientHeight,e):this._layoutBoxInViewport(o,i,this._cachedDomNodeClientWidth,this._cachedDomNodeClientHeight,e),this._preference)for(var s=1;s<=2;s++)for(var a=0,h=this._preference;a<h.length;a++){var l=h[a];if(1===l){if(!t)return null;if(2===s||t.fitsAbove)return new S(t.aboveTop,t.aboveLeft)}else{if(2!==l)return this.allowEditorOverflow?this._prepareRenderWidgetAtExactPositionOverflowing(o):o;if(!t)return null;if(2===s||t.fitsBelow)return new S(t.belowTop,t.belowLeft)}}return null},e.prototype.onBeforeRender=function(e){this._viewRange&&this._preference&&(this._viewRange.endLineNumber<e.startLineNumber||this._viewRange.startLineNumber>e.endLineNumber||this.domNode.setMaxWidth(this._maxWidth))},e.prototype.prepareRender=function(e){this._renderData=this._prepareRenderWidget(e)},e.prototype.render=function(e){this._renderData?(this.allowEditorOverflow?(this.domNode.setTop(this._renderData.top),this.domNode.setLeft(this._renderData.left)):(this.domNode.setTop(this._renderData.top+e.scrollTop-e.bigNumbersDelta),this.domNode.setLeft(this._renderData.left)),this._isVisible||(this.domNode.setVisibility("inherit"),this.domNode.setAttribute("monaco-visible-content-widget","true"),this._isVisible=!0)):this._isVisible&&(this.domNode.removeAttribute("monaco-visible-content-widget"),this._isVisible=!1,this.domNode.setVisibility("hidden"))},e}(),x=(n("930f"),n("e096")),M=n("918c"),O=n("e8e3"),A=n("b7d0"),W=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),E=!0,T=function(e){function t(t){var n=e.call(this)||this;n._context=t;var o=n._context.configuration.options,i=o.get(107);return n._lineHeight=o.get(49),n._renderLineHighlight=o.get(72),n._contentLeft=i.contentLeft,n._contentWidth=i.contentWidth,n._selectionIsEmpty=!0,n._cursorLineNumbers=[],n._selections=[],n._renderData=null,n._context.addEventHandler(n),n}return W(t,e),t.prototype.dispose=function(){this._context.removeEventHandler(this),e.prototype.dispose.call(this)},t.prototype._readFromSelections=function(){var e=!1,t=E?this._selections.slice(0,1):this._selections,n=t.map((function(e){return e.positionLineNumber}));n.sort((function(e,t){return e-t})),O["g"](this._cursorLineNumbers,n)||(this._cursorLineNumbers=n,e=!0);var o=t.every((function(e){return e.isEmpty()}));return this._selectionIsEmpty!==o&&(this._selectionIsEmpty=o,e=!0),e},t.prototype.onThemeChanged=function(e){return this._readFromSelections()},t.prototype.onConfigurationChanged=function(e){var t=this._context.configuration.options,n=t.get(107);return this._lineHeight=t.get(49),this._renderLineHighlight=t.get(72),this._contentLeft=n.contentLeft,this._contentWidth=n.contentWidth,!0},t.prototype.onCursorStateChanged=function(e){return this._selections=e.selections,this._readFromSelections()},t.prototype.onFlushed=function(e){return!0},t.prototype.onLinesDeleted=function(e){return!0},t.prototype.onLinesInserted=function(e){return!0},t.prototype.onScrollChanged=function(e){return e.scrollWidthChanged||e.scrollTopChanged},t.prototype.onZonesChanged=function(e){return!0},t.prototype.prepareRender=function(e){if(this._shouldRenderThis()){for(var t=this._renderOne(e),n=e.visibleRange.startLineNumber,o=e.visibleRange.endLineNumber,i=this._cursorLineNumbers.length,r=0,s=[],a=n;a<=o;a++){var h=a-n;while(r<i&&this._cursorLineNumbers[r]<a)r++;r<i&&this._cursorLineNumbers[r]===a?s[h]=t:s[h]=""}this._renderData=s}else this._renderData=null},t.prototype.render=function(e,t){if(!this._renderData)return"";var n=t-e;return n>=this._renderData.length?"":this._renderData[n]},t}(x["a"]),I=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return W(t,e),t.prototype._renderOne=function(e){var t="current-line"+(this._shouldRenderOther()?" current-line-both":"");return'<div class="'+t+'" style="width:'+Math.max(e.scrollWidth,this._contentWidth)+"px; height:"+this._lineHeight+'px;"></div>'},t.prototype._shouldRenderThis=function(){return("line"===this._renderLineHighlight||"all"===this._renderLineHighlight)&&this._selectionIsEmpty},t.prototype._shouldRenderOther=function(){return"gutter"===this._renderLineHighlight||"all"===this._renderLineHighlight},t}(T),H=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return W(t,e),t.prototype._renderOne=function(e){var t="current-line current-line-margin"+(this._shouldRenderOther()?" current-line-margin-both":"");return'<div class="'+t+'" style="width:'+this._contentLeft+"px; height:"+this._lineHeight+'px;"></div>'},t.prototype._shouldRenderThis=function(){return"gutter"===this._renderLineHighlight||"all"===this._renderLineHighlight},t.prototype._shouldRenderOther=function(){return("line"===this._renderLineHighlight||"all"===this._renderLineHighlight)&&this._selectionIsEmpty},t}(T);Object(A["e"])((function(e,t){E=!1;var n=e.getColor(M["i"]);if(n&&(t.addRule(".monaco-editor .view-overlays .current-line { background-color: "+n+"; }"),t.addRule(".monaco-editor .margin-view-overlays .current-line-margin { background-color: "+n+"; border: none; }")),!n||n.isTransparent()||e.defines(M["j"])){var o=e.getColor(M["j"]);o&&(E=!0,t.addRule(".monaco-editor .view-overlays .current-line { border: 2px solid "+o+"; }"),t.addRule(".monaco-editor .margin-view-overlays .current-line-margin { border: 2px solid "+o+"; }"),"hc"===e.type&&(t.addRule(".monaco-editor .view-overlays .current-line { border-width: 1px; }"),t.addRule(".monaco-editor .margin-view-overlays .current-line-margin { border-width: 1px; }")))}}));n("56dc");var P=n("6a89"),F=n("a21f"),B=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),V=function(e){function t(t){var n=e.call(this)||this;n._context=t;var o=n._context.configuration.options;return n._lineHeight=o.get(49),n._typicalHalfwidthCharacterWidth=o.get(34).typicalHalfwidthCharacterWidth,n._renderResult=null,n._context.addEventHandler(n),n}return B(t,e),t.prototype.dispose=function(){this._context.removeEventHandler(this),this._renderResult=null,e.prototype.dispose.call(this)},t.prototype.onConfigurationChanged=function(e){var t=this._context.configuration.options;return this._lineHeight=t.get(49),this._typicalHalfwidthCharacterWidth=t.get(34).typicalHalfwidthCharacterWidth,!0},t.prototype.onDecorationsChanged=function(e){return!0},t.prototype.onFlushed=function(e){return!0},t.prototype.onLinesChanged=function(e){return!0},t.prototype.onLinesDeleted=function(e){return!0},t.prototype.onLinesInserted=function(e){return!0},t.prototype.onScrollChanged=function(e){return e.scrollTopChanged||e.scrollWidthChanged},t.prototype.onZonesChanged=function(e){return!0},t.prototype.prepareRender=function(e){for(var t=e.getDecorationsInViewport(),n=[],o=0,i=0,r=t.length;i<r;i++){var s=t[i];s.options.className&&(n[o++]=s)}n=n.sort((function(e,t){if(e.options.zIndex<t.options.zIndex)return-1;if(e.options.zIndex>t.options.zIndex)return 1;var n=e.options.className,o=t.options.className;return n<o?-1:n>o?1:P["a"].compareRangesUsingStarts(e.range,t.range)}));for(var a=e.visibleRange.startLineNumber,h=e.visibleRange.endLineNumber,l=[],u=a;u<=h;u++){var d=u-a;l[d]=""}this._renderWholeLineDecorations(e,n,l),this._renderNormalDecorations(e,n,l),this._renderResult=l},t.prototype._renderWholeLineDecorations=function(e,t,n){for(var o=String(this._lineHeight),i=e.visibleRange.startLineNumber,r=e.visibleRange.endLineNumber,s=0,a=t.length;s<a;s++){var h=t[s];if(h.options.isWholeLine)for(var l='<div class="cdr '+h.options.className+'" style="left:0;width:100%;height:'+o+'px;"></div>',u=Math.max(h.range.startLineNumber,i),d=Math.min(h.range.endLineNumber,r),c=u;c<=d;c++){var p=c-i;n[p]+=l}}},t.prototype._renderNormalDecorations=function(e,t,n){for(var o=String(this._lineHeight),i=e.visibleRange.startLineNumber,r=null,s=!1,a=null,h=0,l=t.length;h<l;h++){var u=t[h];if(!u.options.isWholeLine){var d=u.options.className,c=Boolean(u.options.showIfCollapsed),p=u.range;c&&1===p.endColumn&&p.endLineNumber!==p.startLineNumber&&(p=new P["a"](p.startLineNumber,p.startColumn,p.endLineNumber-1,this._context.model.getLineMaxColumn(p.endLineNumber-1))),r===d&&s===c&&P["a"].areIntersectingOrTouching(a,p)?a=P["a"].plusRange(a,p):(null!==r&&this._renderNormalDecoration(e,a,r,s,o,i,n),r=d,s=c,a=p)}}null!==r&&this._renderNormalDecoration(e,a,r,s,o,i,n)},t.prototype._renderNormalDecoration=function(e,t,n,o,i,r,s){var a=e.linesVisibleRangesForRange(t,"findMatch"===n);if(a)for(var h=0,l=a.length;h<l;h++){var u=a[h];if(!u.outsideRenderedLine){var d=u.lineNumber-r;if(o&&1===u.ranges.length){var c=u.ranges[0];0===c.width&&(u.ranges[0]=new F["b"](c.left,this._typicalHalfwidthCharacterWidth))}for(var p=0,f=u.ranges.length;p<f;p++){var g=u.ranges[p],_='<div class="cdr '+n+'" style="left:'+String(g.left)+"px;width:"+String(g.width)+"px;height:"+i+'px;"></div>';s[d]+=_}}}},t.prototype.render=function(e,t){if(!this._renderResult)return"";var n=t-e;return n<0||n>=this._renderResult.length?"":this._renderResult[n]},t}(x["a"]),k=n("1898"),j=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),z=function(e){function t(t,n,r,s){var a=e.call(this,t)||this,h=a._context.configuration.options,l=h.get(78),u=h.get(56),d=h.get(27),c={listenOnDomNode:r.domNode,className:"editor-scrollable "+Object(A["d"])(t.theme.type),useShadows:!1,lazyRender:!0,vertical:l.vertical,horizontal:l.horizontal,verticalHasArrows:l.verticalHasArrows,horizontalHasArrows:l.horizontalHasArrows,verticalScrollbarSize:l.verticalScrollbarSize,verticalSliderSize:l.verticalSliderSize,horizontalScrollbarSize:l.horizontalScrollbarSize,horizontalSliderSize:l.horizontalSliderSize,handleMouseWheel:l.handleMouseWheel,alwaysConsumeMouseWheel:l.alwaysConsumeMouseWheel,arrowSize:l.arrowSize,mouseWheelScrollSensitivity:u,fastScrollSensitivity:d};a.scrollbar=a._register(new k["c"](n.domNode,c,a._context.viewLayout.getScrollable())),v["a"].write(a.scrollbar.getDomNode(),5),a.scrollbarDomNode=Object(i["b"])(a.scrollbar.getDomNode()),a.scrollbarDomNode.setPosition("absolute"),a._setLayout();var p=function(e,t,n){var o={};if(t){var i=e.scrollTop;i&&(o.scrollTop=a._context.viewLayout.getCurrentScrollTop()+i,e.scrollTop=0)}if(n){var r=e.scrollLeft;r&&(o.scrollLeft=a._context.viewLayout.getCurrentScrollLeft()+r,e.scrollLeft=0)}a._context.viewLayout.setScrollPositionNow(o)};return a._register(o["j"](r.domNode,"scroll",(function(e){return p(r.domNode,!0,!0)}))),a._register(o["j"](n.domNode,"scroll",(function(e){return p(n.domNode,!0,!1)}))),a._register(o["j"](s.domNode,"scroll",(function(e){return p(s.domNode,!0,!1)}))),a._register(o["j"](a.scrollbarDomNode.domNode,"scroll",(function(e){return p(a.scrollbarDomNode.domNode,!0,!1)}))),a}return j(t,e),t.prototype.dispose=function(){e.prototype.dispose.call(this)},t.prototype._setLayout=function(){var e=this._context.configuration.options,t=e.get(107);this.scrollbarDomNode.setLeft(t.contentLeft);var n=e.get(54),o=n.side;"right"===o?this.scrollbarDomNode.setWidth(t.contentWidth+t.minimapWidth):this.scrollbarDomNode.setWidth(t.contentWidth),this.scrollbarDomNode.setHeight(t.height)},t.prototype.getOverviewRulerLayoutInfo=function(){return this.scrollbar.getOverviewRulerLayoutInfo()},t.prototype.getDomNode=function(){return this.scrollbarDomNode},t.prototype.delegateVerticalScrollbarMouseDown=function(e){this.scrollbar.delegateVerticalScrollbarMouseDown(e)},t.prototype.onConfigurationChanged=function(e){if(e.hasChanged(78)||e.hasChanged(56)||e.hasChanged(27)){var t=this._context.configuration.options,n=t.get(78),o=t.get(56),i=t.get(27),r={handleMouseWheel:n.handleMouseWheel,mouseWheelScrollSensitivity:o,fastScrollSensitivity:i};this.scrollbar.updateOptions(r)}return e.hasChanged(107)&&this._setLayout(),!0},t.prototype.onScrollChanged=function(e){return!0},t.prototype.onThemeChanged=function(e){return this.scrollbar.updateClassName("editor-scrollable "+Object(A["d"])(this._context.theme.type)),!0},t.prototype.prepareRender=function(e){},t.prototype.render=function(e){this.scrollbar.renderNow()},t}(v["b"]),U=(n("8478"),function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}()),Z=function(){function e(e,t,n){this.startLineNumber=+e,this.endLineNumber=+t,this.className=String(n)}return e}(),K=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return U(t,e),t.prototype._render=function(e,t,n){for(var o=[],i=e;i<=t;i++){var r=i-e;o[r]=[]}if(0===n.length)return o;n.sort((function(e,t){return e.className===t.className?e.startLineNumber===t.startLineNumber?e.endLineNumber-t.endLineNumber:e.startLineNumber-t.startLineNumber:e.className<t.className?-1:1}));for(var s=null,a=0,h=0,l=n.length;h<l;h++){var u=n[h],d=u.className,c=Math.max(u.startLineNumber,e)-e,p=Math.min(u.endLineNumber,t)-e;s===d?(c=Math.max(a+1,c),a=Math.max(a,p)):(s=d,a=p);for(var f=c;f<=a;f++)o[f].push(s)}return o},t}(x["a"]),G=function(e){function t(t){var n=e.call(this)||this;n._context=t;var o=n._context.configuration.options,i=o.get(107);return n._lineHeight=o.get(49),n._glyphMargin=o.get(40),n._glyphMarginLeft=i.glyphMarginLeft,n._glyphMarginWidth=i.glyphMarginWidth,n._renderResult=null,n._context.addEventHandler(n),n}return U(t,e),t.prototype.dispose=function(){this._context.removeEventHandler(this),this._renderResult=null,e.prototype.dispose.call(this)},t.prototype.onConfigurationChanged=function(e){var t=this._context.configuration.options,n=t.get(107);return this._lineHeight=t.get(49),this._glyphMargin=t.get(40),this._glyphMarginLeft=n.glyphMarginLeft,this._glyphMarginWidth=n.glyphMarginWidth,!0},t.prototype.onDecorationsChanged=function(e){return!0},t.prototype.onFlushed=function(e){return!0},t.prototype.onLinesChanged=function(e){return!0},t.prototype.onLinesDeleted=function(e){return!0},t.prototype.onLinesInserted=function(e){return!0},t.prototype.onScrollChanged=function(e){return e.scrollTopChanged},t.prototype.onZonesChanged=function(e){return!0},t.prototype._getDecorations=function(e){for(var t=e.getDecorationsInViewport(),n=[],o=0,i=0,r=t.length;i<r;i++){var s=t[i],a=s.options.glyphMarginClassName;a&&(n[o++]=new Z(s.range.startLineNumber,s.range.endLineNumber,a))}return n},t.prototype.prepareRender=function(e){if(this._glyphMargin){for(var t=e.visibleRange.startLineNumber,n=e.visibleRange.endLineNumber,o=this._render(t,n,this._getDecorations(e)),i=this._lineHeight.toString(),r=this._glyphMarginLeft.toString(),s=this._glyphMarginWidth.toString(),a='" style="left:'+r+"px;width:"+s+"px;height:"+i+'px;"></div>',h=[],l=t;l<=n;l++){var u=l-t,d=o[u];0===d.length?h[u]="":h[u]='<div class="cgmr codicon '+d.join(" ")+a}this._renderResult=h}else this._renderResult=null},t.prototype.render=function(e,t){if(!this._renderResult)return"";var n=t-e;return n<0||n>=this._renderResult.length?"":this._renderResult[n]},t}(K),q=(n("0baa"),function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}()),X=function(e){function t(t){var n=e.call(this)||this;n._context=t,n._primaryLineNumber=0;var o=n._context.configuration.options,i=o.get(108),r=o.get(34);return n._lineHeight=o.get(49),n._spaceWidth=r.spaceWidth,n._enabled=o.get(70),n._activeIndentEnabled=o.get(43),n._maxIndentLeft=-1===i.wrappingColumn?-1:i.wrappingColumn*r.typicalHalfwidthCharacterWidth,n._renderResult=null,n._context.addEventHandler(n),n}return q(t,e),t.prototype.dispose=function(){this._context.removeEventHandler(this),this._renderResult=null,e.prototype.dispose.call(this)},t.prototype.onConfigurationChanged=function(e){var t=this._context.configuration.options,n=t.get(108),o=t.get(34);return this._lineHeight=t.get(49),this._spaceWidth=o.spaceWidth,this._enabled=t.get(70),this._activeIndentEnabled=t.get(43),this._maxIndentLeft=-1===n.wrappingColumn?-1:n.wrappingColumn*o.typicalHalfwidthCharacterWidth,!0},t.prototype.onCursorStateChanged=function(e){var t=e.selections[0],n=t.isEmpty()?t.positionLineNumber:0;return this._primaryLineNumber!==n&&(this._primaryLineNumber=n,!0)},t.prototype.onDecorationsChanged=function(e){return!0},t.prototype.onFlushed=function(e){return!0},t.prototype.onLinesChanged=function(e){return!0},t.prototype.onLinesDeleted=function(e){return!0},t.prototype.onLinesInserted=function(e){return!0},t.prototype.onScrollChanged=function(e){return e.scrollTopChanged},t.prototype.onZonesChanged=function(e){return!0},t.prototype.onLanguageConfigurationChanged=function(e){return!0},t.prototype.prepareRender=function(e){if(this._enabled){var t=e.visibleRange.startLineNumber,n=e.visibleRange.endLineNumber,o=this._context.model.getOptions().indentSize,i=o*this._spaceWidth,r=e.scrollWidth,s=this._lineHeight,a=this._context.model.getLinesIndentGuides(t,n),h=0,u=0,d=0;if(this._activeIndentEnabled&&this._primaryLineNumber){var c=this._context.model.getActiveIndentGuide(this._primaryLineNumber,t,n);h=c.startLineNumber,u=c.endLineNumber,d=c.indent}for(var p=[],f=t;f<=n;f++){var g=h<=f&&f<=u,_=f-t,m=a[_],v="";if(m>=1)for(var y=e.visibleRangeForPosition(new l["a"](f,1)),C=y?y.left:0,b=1;b<=m;b++){var N=g&&b===d?"cigra":"cigr";if(v+='<div class="'+N+'" style="left:'+C+"px;height:"+s+"px;width:"+i+'px"></div>',C+=i,C>r||this._maxIndentLeft>0&&C>this._maxIndentLeft)break}p[_]=v}this._renderResult=p}else this._renderResult=null},t.prototype.render=function(e,t){if(!this._renderResult)return"";var n=t-e;return n<0||n>=this._renderResult.length?"":this._renderResult[n]},t}(x["a"]);Object(A["e"])((function(e,t){var n=e.getColor(M["h"]);n&&t.addRule(".monaco-editor .lines-content .cigr { box-shadow: 1px 0 0 0 "+n+" inset; }");var o=e.getColor(M["a"])||n;o&&t.addRule(".monaco-editor .lines-content .cigra { box-shadow: 1px 0 0 0 "+o+" inset; }")}));var Y=n("b160"),$=(n("38a2"),n("5fe7")),J=n("6ec9"),Q=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),ee=function(){function e(){this._currentVisibleRange=new P["a"](1,1,1,1)}return e.prototype.getCurrentVisibleRange=function(){return this._currentVisibleRange},e.prototype.setCurrentVisibleRange=function(e){this._currentVisibleRange=e},e}(),te=function(){function e(e,t,n,o,i,r){this.lineNumber=e,this.startColumn=t,this.endColumn=n,this.startScrollTop=o,this.stopScrollTop=i,this.scrollType=r}return e}(),ne=function(e){function t(t,n){var o=e.call(this,t)||this;o._linesContent=n,o._textRangeRestingSpot=document.createElement("div"),o._visibleLines=new _(o),o.domNode=o._visibleLines.domNode;var i=o._context.configuration,r=o._context.configuration.options,s=r.get(34),a=r.get(108);return o._lineHeight=r.get(49),o._typicalHalfwidthCharacterWidth=s.typicalHalfwidthCharacterWidth,o._isViewportWrapping=a.isViewportWrapping,o._revealHorizontalRightPadding=r.get(75),o._cursorSurroundingLines=r.get(19),o._cursorSurroundingLinesStyle=r.get(20),o._canUseLayerHinting=!r.get(22),o._viewLineOptions=new J["c"](i,o._context.theme.type),v["a"].write(o.domNode,7),o.domNode.setClassName("view-lines"),p["a"].applyFontInfo(o.domNode,s),o._maxLineWidth=0,o._asyncUpdateLineWidths=new $["d"]((function(){o._updateLineWidthsSlow()}),200),o._lastRenderedData=new ee,o._horizontalRevealRequest=null,o}return Q(t,e),t.prototype.dispose=function(){this._asyncUpdateLineWidths.dispose(),e.prototype.dispose.call(this)},t.prototype.getDomNode=function(){return this.domNode},t.prototype.createVisibleLine=function(){return new J["b"](this._viewLineOptions)},t.prototype.onConfigurationChanged=function(e){this._visibleLines.onConfigurationChanged(e),e.hasChanged(108)&&(this._maxLineWidth=0);var t=this._context.configuration.options,n=t.get(34),o=t.get(108);return this._lineHeight=t.get(49),this._typicalHalfwidthCharacterWidth=n.typicalHalfwidthCharacterWidth,this._isViewportWrapping=o.isViewportWrapping,this._revealHorizontalRightPadding=t.get(75),this._cursorSurroundingLines=t.get(19),this._cursorSurroundingLinesStyle=t.get(20),this._canUseLayerHinting=!t.get(22),p["a"].applyFontInfo(this.domNode,n),this._onOptionsMaybeChanged(),e.hasChanged(107)&&(this._maxLineWidth=0),!0},t.prototype._onOptionsMaybeChanged=function(){var e=this._context.configuration,t=new J["c"](e,this._context.theme.type);if(!this._viewLineOptions.equals(t)){this._viewLineOptions=t;for(var n=this._visibleLines.getStartLineNumber(),o=this._visibleLines.getEndLineNumber(),i=n;i<=o;i++){var r=this._visibleLines.getVisibleLine(i);r.onOptionsChanged(this._viewLineOptions)}return!0}return!1},t.prototype.onCursorStateChanged=function(e){for(var t=this._visibleLines.getStartLineNumber(),n=this._visibleLines.getEndLineNumber(),o=!1,i=t;i<=n;i++)o=this._visibleLines.getVisibleLine(i).onSelectionChanged()||o;return o},t.prototype.onDecorationsChanged=function(e){for(var t=this._visibleLines.getStartLineNumber(),n=this._visibleLines.getEndLineNumber(),o=t;o<=n;o++)this._visibleLines.getVisibleLine(o).onDecorationsChanged();return!0},t.prototype.onFlushed=function(e){var t=this._visibleLines.onFlushed(e);return this._maxLineWidth=0,t},t.prototype.onLinesChanged=function(e){return this._visibleLines.onLinesChanged(e)},t.prototype.onLinesDeleted=function(e){return this._visibleLines.onLinesDeleted(e)},t.prototype.onLinesInserted=function(e){return this._visibleLines.onLinesInserted(e)},t.prototype.onRevealRangeRequest=function(e){var t=this._computeScrollTopToRevealRange(this._context.viewLayout.getFutureViewport(),e.source,e.range,e.verticalType),n=this._context.viewLayout.validateScrollPosition({scrollTop:t});e.revealHorizontal?e.range.startLineNumber!==e.range.endLineNumber?n={scrollTop:n.scrollTop,scrollLeft:0}:this._horizontalRevealRequest=new te(e.range.startLineNumber,e.range.startColumn,e.range.endColumn,this._context.viewLayout.getCurrentScrollTop(),n.scrollTop,e.scrollType):this._horizontalRevealRequest=null;var o=Math.abs(this._context.viewLayout.getCurrentScrollTop()-n.scrollTop);return 0===e.scrollType&&o>this._lineHeight?this._context.viewLayout.setScrollPositionSmooth(n):this._context.viewLayout.setScrollPositionNow(n),!0},t.prototype.onScrollChanged=function(e){if(this._horizontalRevealRequest&&e.scrollLeftChanged&&(this._horizontalRevealRequest=null),this._horizontalRevealRequest&&e.scrollTopChanged){var t=Math.min(this._horizontalRevealRequest.startScrollTop,this._horizontalRevealRequest.stopScrollTop),n=Math.max(this._horizontalRevealRequest.startScrollTop,this._horizontalRevealRequest.stopScrollTop);(e.scrollTop<t||e.scrollTop>n)&&(this._horizontalRevealRequest=null)}return this.domNode.setWidth(e.scrollWidth),this._visibleLines.onScrollChanged(e)||!0},t.prototype.onTokensChanged=function(e){return this._visibleLines.onTokensChanged(e)},t.prototype.onZonesChanged=function(e){return this._context.viewLayout.onMaxLineWidthChanged(this._maxLineWidth),this._visibleLines.onZonesChanged(e)},t.prototype.onThemeChanged=function(e){return this._onOptionsMaybeChanged()},t.prototype.getPositionFromDOMInfo=function(e,t){var n=this._getViewLineDomNode(e);if(null===n)return null;var o=this._getLineNumberFor(n);if(-1===o)return null;if(o<1||o>this._context.model.getLineCount())return null;if(1===this._context.model.getLineMaxColumn(o))return new l["a"](o,1);var i=this._visibleLines.getStartLineNumber(),r=this._visibleLines.getEndLineNumber();if(o<i||o>r)return null;var s=this._visibleLines.getVisibleLine(o).getColumnOfNodeOffset(o,e,t),a=this._context.model.getLineMinColumn(o);return s<a&&(s=a),new l["a"](o,s)},t.prototype._getViewLineDomNode=function(e){while(e&&1===e.nodeType){if(e.className===J["b"].CLASS_NAME)return e;e=e.parentElement}return null},t.prototype._getLineNumberFor=function(e){for(var t=this._visibleLines.getStartLineNumber(),n=this._visibleLines.getEndLineNumber(),o=t;o<=n;o++){var i=this._visibleLines.getVisibleLine(o);if(e===i.getDomNode())return o}return-1},t.prototype.getLineWidth=function(e){var t=this._visibleLines.getStartLineNumber(),n=this._visibleLines.getEndLineNumber();return e<t||e>n?-1:this._visibleLines.getVisibleLine(e).getWidth()},t.prototype.linesVisibleRangesForRange=function(e,t){if(this.shouldRender())return null;var n=e.endLineNumber,o=P["a"].intersectRanges(e,this._lastRenderedData.getCurrentVisibleRange());if(!o)return null;var i=[],r=0,s=new J["a"](this.domNode.domNode,this._textRangeRestingSpot),a=0;t&&(a=this._context.model.coordinatesConverter.convertViewPositionToModelPosition(new l["a"](o.startLineNumber,1)).lineNumber);for(var h=this._visibleLines.getStartLineNumber(),u=this._visibleLines.getEndLineNumber(),d=o.startLineNumber;d<=o.endLineNumber;d++)if(!(d<h||d>u)){var c=d===o.startLineNumber?o.startColumn:1,p=d===o.endLineNumber?o.endColumn:this._context.model.getLineMaxColumn(d),f=this._visibleLines.getVisibleLine(d).getVisibleRangesForRange(c,p,s);if(f){if(t&&d<n){var g=a;a=this._context.model.coordinatesConverter.convertViewPositionToModelPosition(new l["a"](d+1,1)).lineNumber,g!==a&&(f.ranges[f.ranges.length-1].width+=this._typicalHalfwidthCharacterWidth)}i[r++]=new F["c"](f.outsideRenderedLine,d,f.ranges)}}return 0===r?null:i},t.prototype._visibleRangesForLineRange=function(e,t,n){return this.shouldRender()||e<this._visibleLines.getStartLineNumber()||e>this._visibleLines.getEndLineNumber()?null:this._visibleLines.getVisibleLine(e).getVisibleRangesForRange(t,n,new J["a"](this.domNode.domNode,this._textRangeRestingSpot))},t.prototype.visibleRangeForPosition=function(e){var t=this._visibleRangesForLineRange(e.lineNumber,e.column,e.column);return t?new F["a"](t.outsideRenderedLine,t.ranges[0].left):null},t.prototype.updateLineWidths=function(){this._updateLineWidths(!1)},t.prototype._updateLineWidthsFast=function(){return this._updateLineWidths(!0)},t.prototype._updateLineWidthsSlow=function(){this._updateLineWidths(!1)},t.prototype._updateLineWidths=function(e){for(var t=this._visibleLines.getStartLineNumber(),n=this._visibleLines.getEndLineNumber(),o=1,i=!0,r=t;r<=n;r++){var s=this._visibleLines.getVisibleLine(r);!e||s.getWidthIsFast()?o=Math.max(o,s.getWidth()):i=!1}return i&&1===t&&n===this._context.model.getLineCount()&&(this._maxLineWidth=0),this._ensureMaxLineWidth(o),i},t.prototype.prepareRender=function(){throw new Error("Not supported")},t.prototype.render=function(){throw new Error("Not supported")},t.prototype.renderText=function(e){if(this._visibleLines.renderLines(e),this._lastRenderedData.setCurrentVisibleRange(e.visibleRange),this.domNode.setWidth(this._context.viewLayout.getScrollWidth()),this.domNode.setHeight(Math.min(this._context.viewLayout.getScrollHeight(),1e6)),this._horizontalRevealRequest){var t=this._horizontalRevealRequest.lineNumber,n=this._horizontalRevealRequest.startColumn,o=this._horizontalRevealRequest.endColumn,i=this._horizontalRevealRequest.scrollType;if(e.startLineNumber<=t&&t<=e.endLineNumber){this._horizontalRevealRequest=null,this.onDidRender();var r=this._computeScrollLeftToRevealRange(t,n,o),s=this._isViewportWrapping;s||this._ensureMaxLineWidth(r.maxHorizontalOffset),0===i?this._context.viewLayout.setScrollPositionSmooth({scrollLeft:r.scrollLeft}):this._context.viewLayout.setScrollPositionNow({scrollLeft:r.scrollLeft})}}this._updateLineWidthsFast()||this._asyncUpdateLineWidths.schedule(),this._linesContent.setLayerHinting(this._canUseLayerHinting),this._linesContent.setContain("strict");var a=this._context.viewLayout.getCurrentScrollTop()-e.bigNumbersDelta;this._linesContent.setTop(-a),this._linesContent.setLeft(-this._context.viewLayout.getCurrentScrollLeft())},t.prototype._ensureMaxLineWidth=function(e){var t=Math.ceil(e);this._maxLineWidth<t&&(this._maxLineWidth=t,this._context.viewLayout.onMaxLineWidthChanged(this._maxLineWidth))},t.prototype._computeScrollTopToRevealRange=function(e,t,n,o){var i,r,s=e.top,a=e.height,h=s+a;i=this._context.viewLayout.getVerticalOffsetForLineNumber(n.startLineNumber),r=this._context.viewLayout.getVerticalOffsetForLineNumber(n.endLineNumber)+this._lineHeight;var l,u="mouse"===t&&"default"===this._cursorSurroundingLinesStyle;if(!u){var d=Math.min(a/this._lineHeight/2,this._cursorSurroundingLines);i-=d*this._lineHeight,r+=Math.max(0,d-1)*this._lineHeight}if(0!==o&&4!==o||(r+=this._lineHeight),r-i>a)l=i;else if(1===o||2===o)if(2===o&&s<=i&&r<=h)l=s;else{var c=(i+r)/2;l=Math.max(0,c-a/2)}else l=this._computeMinimumScrolling(s,h,i,r,3===o,4===o);return l},t.prototype._computeScrollLeftToRevealRange=function(e,n,o){var i=0,r=this._context.viewLayout.getCurrentViewport(),s=r.left,a=s+r.width,h=this._visibleRangesForLineRange(e,n,o),l=1073741824,u=0;if(!h)return{scrollLeft:s,maxHorizontalOffset:i};for(var d=0,c=h.ranges;d<c.length;d++){var p=c[d];p.left<l&&(l=p.left),p.left+p.width>u&&(u=p.left+p.width)}i=u,l=Math.max(0,l-t.HORIZONTAL_EXTRA_PX),u+=this._revealHorizontalRightPadding;var f=this._computeMinimumScrolling(s,a,l,u);return{scrollLeft:f,maxHorizontalOffset:i}},t.prototype._computeMinimumScrolling=function(e,t,n,o,i,r){e|=0,t|=0,n|=0,o|=0,i=!!i,r=!!r;var s=t-e,a=o-n;return a<s?i?n:r?Math.max(0,o-s):n<e?n:o>t?Math.max(0,o-s):e:n},t.HORIZONTAL_EXTRA_PX=30,t}(v["b"]),oe=(n("27e6"),function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}()),ie=function(e){function t(t){var n=e.call(this)||this;n._context=t;var o=n._context.configuration.options,i=o.get(107);return n._decorationsLeft=i.decorationsLeft,n._decorationsWidth=i.decorationsWidth,n._renderResult=null,n._context.addEventHandler(n),n}return oe(t,e),t.prototype.dispose=function(){this._context.removeEventHandler(this),this._renderResult=null,e.prototype.dispose.call(this)},t.prototype.onConfigurationChanged=function(e){var t=this._context.configuration.options,n=t.get(107);return this._decorationsLeft=n.decorationsLeft,this._decorationsWidth=n.decorationsWidth,!0},t.prototype.onDecorationsChanged=function(e){return!0},t.prototype.onFlushed=function(e){return!0},t.prototype.onLinesChanged=function(e){return!0},t.prototype.onLinesDeleted=function(e){return!0},t.prototype.onLinesInserted=function(e){return!0},t.prototype.onScrollChanged=function(e){return e.scrollTopChanged},t.prototype.onZonesChanged=function(e){return!0},t.prototype._getDecorations=function(e){for(var t=e.getDecorationsInViewport(),n=[],o=0,i=0,r=t.length;i<r;i++){var s=t[i],a=s.options.linesDecorationsClassName;a&&(n[o++]=new Z(s.range.startLineNumber,s.range.endLineNumber,a))}return n},t.prototype.prepareRender=function(e){for(var t=e.visibleRange.startLineNumber,n=e.visibleRange.endLineNumber,o=this._render(t,n,this._getDecorations(e)),i=this._decorationsLeft.toString(),r=this._decorationsWidth.toString(),s='" style="left:'+i+"px;width:"+r+'px;"></div>',a=[],h=t;h<=n;h++){for(var l=h-t,u=o[l],d="",c=0,p=u.length;c<p;c++)d+='<div class="cldr '+u[c]+s;a[l]=d}this._renderResult=a},t.prototype.render=function(e,t){return this._renderResult?this._renderResult[t-e]:""},t}(K),re=n("3d43"),se=(n("5d70"),function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}()),ae=function(e){function t(t){var n=e.call(this)||this;return n._context=t,n._renderResult=null,n._context.addEventHandler(n),n}return se(t,e),t.prototype.dispose=function(){this._context.removeEventHandler(this),this._renderResult=null,e.prototype.dispose.call(this)},t.prototype.onConfigurationChanged=function(e){return!0},t.prototype.onDecorationsChanged=function(e){return!0},t.prototype.onFlushed=function(e){return!0},t.prototype.onLinesChanged=function(e){return!0},t.prototype.onLinesDeleted=function(e){return!0},t.prototype.onLinesInserted=function(e){return!0},t.prototype.onScrollChanged=function(e){return e.scrollTopChanged},t.prototype.onZonesChanged=function(e){return!0},t.prototype._getDecorations=function(e){for(var t=e.getDecorationsInViewport(),n=[],o=0,i=0,r=t.length;i<r;i++){var s=t[i],a=s.options.marginClassName;a&&(n[o++]=new Z(s.range.startLineNumber,s.range.endLineNumber,a))}return n},t.prototype.prepareRender=function(e){for(var t=e.visibleRange.startLineNumber,n=e.visibleRange.endLineNumber,o=this._render(t,n,this._getDecorations(e)),i=[],r=t;r<=n;r++){for(var s=r-t,a=o[s],h="",l=0,u=a.length;l<u;l++)h+='<div class="cmdr '+a[l]+'" style=""></div>';i[s]=h}this._renderResult=i},t.prototype.render=function(e,t){return this._renderResult?this._renderResult[t-e]:""},t}(K),he=(n("f20b"),n("00a3")),le=n("3742"),ue=n("fd49"),de=n("ff6c"),ce=n("0d83"),pe=n("303e"),fe=n("a60f"),ge=function(){for(var e=[],t=32;t<=126;t++)e.push(t);return e.push(65533),e}(),_e=function(e,t){return e-=32,e<0||e>96?t<=2?(e+96)%96:95:e},me=function(){function e(t,n){this.scale=n,this.charDataNormal=e.soften(t,.8),this.charDataLight=e.soften(t,50/60)}return e.soften=function(e,t){for(var n=new Uint8ClampedArray(e.length),o=0,i=e.length;o<i;o++)n[o]=e[o]*t;return n},e.prototype.renderChar=function(e,t,n,o,i,r,s,a){var h=1*this.scale,l=2*this.scale;if(t+h>e.width||n+l>e.height)console.warn("bad render request outside image data");else for(var u=a?this.charDataLight:this.charDataNormal,d=_e(o,s),c=4*e.width,p=r.r,f=r.g,g=r.b,_=i.r-p,m=i.g-f,v=i.b-g,y=e.data,C=d*h*l,b=n*c+4*t,N=0;N<l;N++){for(var w=b,L=0;L<h;L++){var S=u[C++]/255;y[w++]=p+_*S,y[w++]=f+m*S,y[w++]=g+v*S,w++}b+=c}},e.prototype.blockRenderChar=function(e,t,n,o,i,r){var s=1*this.scale,a=2*this.scale;if(t+s>e.width||n+a>e.height)console.warn("bad render request outside image data");else for(var h=4*e.width,l=.5,u=i.r,d=i.g,c=i.b,p=o.r-u,f=o.g-d,g=o.b-c,_=u+p*l,m=d+f*l,v=c+g*l,y=e.data,C=n*h+4*t,b=0;b<a;b++){for(var N=C,w=0;w<s;w++)y[N++]=_,y[N++]=m,y[N++]=v,N++;C+=h}},e}(),ve=n("0bfb"),ye={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15},Ce=function(e){for(var t=new Uint8ClampedArray(e.length/2),n=0;n<e.length;n+=2)t[n>>1]=ye[e[n]]<<4|15&ye[e[n+1]];return t},be={1:Object(ve["a"])((function(){return Ce("0000511D6300CF609C709645A78432005642574171487021003C451900274D35D762755E8B629C5BA856AF57BA649530C167D1512A272A3F6038604460398526BCA2A968DB6F8957C768BE5FBE2FB467CF5D8D5B795DC7625B5DFF50DE64C466DB2FC47CD860A65E9A2EB96CB54CE06DA763AB2EA26860524D3763536601005116008177A8705E53AB738E6A982F88BAA35B5F5B626D9C636B449B737E5B7B678598869A662F6B5B8542706C704C80736A607578685B70594A49715A4522E792")})),2:Object(ve["a"])((function(){return Ce("000000000000000055394F383D2800008B8B1F210002000081B1CBCBCC820000847AAF6B9AAF2119BE08B8881AD60000A44FD07DCCF107015338130C00000000385972265F390B406E2437634B4B48031B12B8A0847000001E15B29A402F0000000000004B33460B00007A752C2A0000000000004D3900000084394B82013400ABA5CFC7AD9C0302A45A3E5A98AB000089A43382D97900008BA54AA087A70A0248A6A7AE6DBE0000BF6F94987EA40A01A06DCFA7A7A9030496C32F77891D0000A99FB1A0AFA80603B29AB9CA75930D010C0948354D3900000C0948354F37460D0028BE673D8400000000AF9D7B6E00002B007AA8933400007AA642675C2700007984CFB9C3985B768772A8A6B7B20000CAAECAAFC4B700009F94A6009F840009D09F9BA4CA9C0000CC8FC76DC87F0000C991C472A2000000A894A48CA7B501079BA2C9C69BA20000B19A5D3FA89000005CA6009DA2960901B0A7F0669FB200009D009E00B7890000DAD0F5D092820000D294D4C48BD10000B5A7A4A3B1A50402CAB6CBA6A2000000B5A7A4A3B1A8044FCDADD19D9CB00000B7778F7B8AAE0803C9AB5D3F5D3F00009EA09EA0BAB006039EA0989A8C7900009B9EF4D6B7C00000A9A7816CACA80000ABAC84705D3F000096DA635CDC8C00006F486F266F263D4784006124097B00374F6D2D6D2D6D4A3A95872322000000030000000000008D8939130000000000002E22A5C9CBC70600AB25C0B5C9B400061A2DB04CA67001082AA6BEBEBFC606002321DACBC19E03087AA08B6768380000282FBAC0B8CA7A88AD25BBA5A29900004C396C5894A6000040485A6E356E9442A32CD17EADA70000B4237923628600003E2DE9C1D7B500002F25BBA5A2990000231DB6AFB4A804023025C0B5CAB588062B2CBDBEC0C706882435A75CA20000002326BD6A82A908048B4B9A5A668000002423A09CB4BB060025259C9D8A7900001C1FCAB2C7C700002A2A9387ABA200002626A4A47D6E9D14333163A0C87500004B6F9C2D643A257049364936493647358A34438355497F1A0000A24C1D590000D38DFFBDD4CD3126")}))},Ne=function(){function e(){}return e.create=function(t,n){return this.lastCreated&&t===this.lastCreated.scale&&n===this.lastFontFamily?this.lastCreated:(o=be[t]?new me(be[t](),t):e.createFromSampleData(e.createSampleData(n).data,t),this.lastFontFamily=n,this.lastCreated=o,o);var o},e.createSampleData=function(e){var t=document.createElement("canvas"),n=t.getContext("2d");t.style.height="16px",t.height=16,t.width=960,t.style.width="960px",n.fillStyle="#ffffff",n.font="bold 16px "+e,n.textBaseline="middle";for(var o=0,i=0,r=ge;i<r.length;i++){var s=r[i];n.fillText(String.fromCharCode(s),o,8),o+=10}return n.getImageData(0,0,960,16)},e.createFromSampleData=function(t,n){var o=61440;if(t.length!==o)throw new Error("Unexpected source in MinimapCharRenderer");var i=e._downsample(t,n);return new me(i,n)},e._downsampleChar=function(e,t,n,o,i){for(var r=1*i,s=2*i,a=o,h=0,l=0;l<s;l++)for(var u=l/s*16,d=(l+1)/s*16,c=0;c<r;c++){for(var p=c/r*10,f=(c+1)/r*10,g=0,_=0,m=u;m<d;m++)for(var v=t+3840*Math.floor(m),y=1-(m-Math.floor(m)),C=p;C<f;C++){var b=1-(C-Math.floor(C)),N=v+4*Math.floor(C),w=b*y;_+=w,g+=e[N]*e[N+3]/255*w}var L=g/_;h=Math.max(h,L),n[a++]=L}return h},e._downsample=function(e,t){for(var n=2*t*1*t,o=96*n,i=new Uint8ClampedArray(o),r=0,s=0,a=0,h=0;h<96;h++)a=Math.max(a,this._downsampleChar(e,s,i,r,t)),r+=n,s+=40;if(a>0)for(var l=255/a,u=0;u<o;u++)i[u]*=l;return i},e}(),we=n("3352"),Le=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}();function Se(e,t){return 1===e?2*t:3*t}function De(e,t){return 1*t}var Re=140,xe=2,Me=function(){function e(e){var t=this,n=e.options,o=n.get(105),i=n.get(107),r=n.get(34);this.renderMinimap=0|i.renderMinimap,this.scrollBeyondLastLine=n.get(80);var s=n.get(54);this.showSlider=s.showSlider,this.fontScale=Math.round(s.scale*o),this.charRenderer=Object(ve["a"])((function(){return Ne.create(t.fontScale,r.fontFamily)})),this.pixelRatio=o,this.typicalHalfwidthCharacterWidth=r.typicalHalfwidthCharacterWidth,this.lineHeight=n.get(49),this.minimapLeft=i.minimapLeft,this.minimapWidth=i.minimapWidth,this.minimapHeight=i.height,this.canvasInnerWidth=Math.floor(o*this.minimapWidth),this.canvasInnerHeight=Math.floor(o*this.minimapHeight),this.canvasOuterWidth=this.canvasInnerWidth/o,this.canvasOuterHeight=this.canvasInnerHeight/o}return e.prototype.equals=function(e){return this.renderMinimap===e.renderMinimap&&this.scrollBeyondLastLine===e.scrollBeyondLastLine&&this.showSlider===e.showSlider&&this.pixelRatio===e.pixelRatio&&this.typicalHalfwidthCharacterWidth===e.typicalHalfwidthCharacterWidth&&this.lineHeight===e.lineHeight&&this.fontScale===e.fontScale&&this.minimapLeft===e.minimapLeft&&this.minimapWidth===e.minimapWidth&&this.minimapHeight===e.minimapHeight&&this.canvasInnerWidth===e.canvasInnerWidth&&this.canvasInnerHeight===e.canvasInnerHeight&&this.canvasOuterWidth===e.canvasOuterWidth&&this.canvasOuterHeight===e.canvasOuterHeight},e}(),Oe=function(){function e(e,t,n,o,i,r,s){this.scrollTop=e,this.scrollHeight=t,this._computedSliderRatio=n,this.sliderTop=o,this.sliderHeight=i,this.startLineNumber=r,this.endLineNumber=s}return e.prototype.getDesiredScrollTopFromDelta=function(e){var t=this.sliderTop+e;return Math.round(t/this._computedSliderRatio)},e.prototype.getDesiredScrollTopFromTouchLocation=function(e){return Math.round((e-this.sliderHeight/2)/this._computedSliderRatio)},e.create=function(t,n,o,i,r,s,a,h,l){var u,d,c=t.pixelRatio,p=Se(t.renderMinimap,t.fontScale),f=Math.floor(t.canvasInnerHeight/p),g=t.lineHeight;if(r&&o!==s){var _=o-n+1;u=Math.floor(_*p/c)}else{var m=i/g;u=Math.floor(m*p/c)}d=t.scrollBeyondLastLine?(s-1)*p/c:Math.max(0,s*p/c-u),d=Math.min(t.minimapHeight-u,d);var v=d/(h-i),y=a*v,C=0;if(t.scrollBeyondLastLine){m=i/g;C=m}if(f>=s+C){var b=1,N=s;return new e(a,h,v,y,u,b,N)}b=Math.max(1,Math.floor(n-y*c/p));l&&l.scrollHeight===h&&(l.scrollTop>a&&(b=Math.min(b,l.startLineNumber)),l.scrollTop<a&&(b=Math.max(b,l.startLineNumber)));N=Math.min(s,b+f-1);return new e(a,h,v,y,u,b,N)},e}(),Ae=function(){function e(e){this.dy=e}return e.prototype.onContentChanged=function(){this.dy=-1},e.prototype.onTokensChanged=function(){this.dy=-1},e.INVALID=new e(-1),e}(),We=function(){function e(e,t,n){this.renderedLayout=e,this._imageData=t,this._renderedLines=new g((function(){return Ae.INVALID})),this._renderedLines._set(e.startLineNumber,n)}return e.prototype.linesEquals=function(e){if(!this.scrollEquals(e))return!1;for(var t=this._renderedLines._get(),n=t.lines,o=0,i=n.length;o<i;o++)if(-1===n[o].dy)return!1;return!0},e.prototype.scrollEquals=function(e){return this.renderedLayout.startLineNumber===e.startLineNumber&&this.renderedLayout.endLineNumber===e.endLineNumber},e.prototype._get=function(){var e=this._renderedLines._get();return{imageData:this._imageData,rendLineNumberStart:e.rendLineNumberStart,lines:e.lines}},e.prototype.onLinesChanged=function(e){return this._renderedLines.onLinesChanged(e.fromLineNumber,e.toLineNumber)},e.prototype.onLinesDeleted=function(e){this._renderedLines.onLinesDeleted(e.fromLineNumber,e.toLineNumber)},e.prototype.onLinesInserted=function(e){this._renderedLines.onLinesInserted(e.fromLineNumber,e.toLineNumber)},e.prototype.onTokensChanged=function(e){return this._renderedLines.onTokensChanged(e.ranges)},e}(),Ee=function(){function e(t,n,o,i){this._backgroundFillData=e._createBackgroundFillData(n,o,i),this._buffers=[t.createImageData(n,o),t.createImageData(n,o)],this._lastUsedBuffer=0}return e.prototype.getBuffer=function(){this._lastUsedBuffer=1-this._lastUsedBuffer;var e=this._buffers[this._lastUsedBuffer];return e.data.set(this._backgroundFillData),e},e._createBackgroundFillData=function(e,t,n){for(var o=n.r,i=n.g,r=n.b,s=new Uint8ClampedArray(e*t*4),a=0,h=0;h<t;h++)for(var l=0;l<e;l++)s[a]=o,s[a+1]=i,s[a+2]=r,s[a+3]=255,a+=4;return s},e}(),Te=function(e){function t(t){var n=e.call(this,t)||this;return n._selections=[],n._renderDecorations=!1,n._gestureInProgress=!1,n._options=new Me(n._context.configuration),n._lastRenderData=null,n._buffers=null,n._selectionColor=n._context.theme.getColor(pe["Ib"]),n._domNode=Object(i["b"])(document.createElement("div")),v["a"].write(n._domNode,8),n._domNode.setClassName(n._getMinimapDomNodeClassName()),n._domNode.setPosition("absolute"),n._domNode.setAttribute("role","presentation"),n._domNode.setAttribute("aria-hidden","true"),n._shadow=Object(i["b"])(document.createElement("div")),n._shadow.setClassName("minimap-shadow-hidden"),n._domNode.appendChild(n._shadow),n._canvas=Object(i["b"])(document.createElement("canvas")),n._canvas.setPosition("absolute"),n._canvas.setLeft(0),n._domNode.appendChild(n._canvas),n._decorationsCanvas=Object(i["b"])(document.createElement("canvas")),n._decorationsCanvas.setPosition("absolute"),n._decorationsCanvas.setClassName("minimap-decorations-layer"),n._decorationsCanvas.setLeft(0),n._domNode.appendChild(n._decorationsCanvas),n._slider=Object(i["b"])(document.createElement("div")),n._slider.setPosition("absolute"),n._slider.setClassName("minimap-slider"),n._slider.setLayerHinting(!0),n._slider.setContain("strict"),n._domNode.appendChild(n._slider),n._sliderHorizontal=Object(i["b"])(document.createElement("div")),n._sliderHorizontal.setPosition("absolute"),n._sliderHorizontal.setClassName("minimap-slider-horizontal"),n._slider.appendChild(n._sliderHorizontal),n._tokensColorTracker=de["a"].getInstance(),n._applyLayout(),n._mouseDownListener=o["o"](n._domNode.domNode,"mousedown",(function(e){e.preventDefault();var t=n._options.renderMinimap;if(0!==t&&n._lastRenderData){var o=Se(t,n._options.fontScale),i=n._options.pixelRatio*e.browserEvent.offsetY,r=Math.floor(i/o),s=r+n._lastRenderData.renderedLayout.startLineNumber;s=Math.min(s,n._context.model.getLineCount()),n._context.privateViewEventBus.emit(new ce["m"]("mouse",new P["a"](s,1,s,1),1,!1,0))}})),n._sliderMouseMoveMonitor=new he["a"],n._sliderMouseDownListener=o["o"](n._slider.domNode,"mousedown",(function(e){if(e.preventDefault(),e.stopPropagation(),e.leftButton&&n._lastRenderData){var t=e.posy,o=e.posx,i=n._lastRenderData.renderedLayout;n._slider.toggleClassName("active",!0),n._sliderMouseMoveMonitor.startMonitoring(e.target,e.buttons,he["b"],(function(e){var r=Math.abs(e.posx-o);if(u["h"]&&r>Re)n._context.viewLayout.setScrollPositionNow({scrollTop:i.scrollTop});else{var s=e.posy-t;n._context.viewLayout.setScrollPositionNow({scrollTop:i.getDesiredScrollTopFromDelta(s)})}}),(function(){n._slider.toggleClassName("active",!1)}))}})),n._gestureDisposable=fe["b"].addTarget(n._domNode.domNode),n._sliderTouchStartListener=o["j"](n._domNode.domNode,fe["a"].Start,(function(e){e.preventDefault(),e.stopPropagation(),n._lastRenderData&&(n._slider.toggleClassName("active",!0),n._gestureInProgress=!0,n.scrollDueToTouchEvent(e))})),n._sliderTouchMoveListener=o["o"](n._domNode.domNode,fe["a"].Change,(function(e){e.preventDefault(),e.stopPropagation(),n._lastRenderData&&n._gestureInProgress&&n.scrollDueToTouchEvent(e)})),n._sliderTouchEndListener=o["o"](n._domNode.domNode,fe["a"].End,(function(e){e.preventDefault(),e.stopPropagation(),n._gestureInProgress=!1,n._slider.toggleClassName("active",!1)})),n}return Le(t,e),t.prototype.scrollDueToTouchEvent=function(e){var t=this._domNode.domNode.getBoundingClientRect().top,n=this._lastRenderData.renderedLayout.getDesiredScrollTopFromTouchLocation(e.pageY-t);this._context.viewLayout.setScrollPositionNow({scrollTop:n})},t.prototype.dispose=function(){this._mouseDownListener.dispose(),this._sliderMouseMoveMonitor.dispose(),this._sliderMouseDownListener.dispose(),this._gestureDisposable.dispose(),this._sliderTouchStartListener.dispose(),this._sliderTouchMoveListener.dispose(),this._sliderTouchEndListener.dispose(),e.prototype.dispose.call(this)},t.prototype._getMinimapDomNodeClassName=function(){return"always"===this._options.showSlider?"minimap slider-always":"minimap slider-mouseover"},t.prototype.getDomNode=function(){return this._domNode},t.prototype._applyLayout=function(){this._domNode.setLeft(this._options.minimapLeft),this._domNode.setWidth(this._options.minimapWidth),this._domNode.setHeight(this._options.minimapHeight),this._shadow.setHeight(this._options.minimapHeight),this._canvas.setWidth(this._options.canvasOuterWidth),this._canvas.setHeight(this._options.canvasOuterHeight),this._canvas.domNode.width=this._options.canvasInnerWidth,this._canvas.domNode.height=this._options.canvasInnerHeight,this._decorationsCanvas.setWidth(this._options.canvasOuterWidth),this._decorationsCanvas.setHeight(this._options.canvasOuterHeight),this._decorationsCanvas.domNode.width=this._options.canvasInnerWidth,this._decorationsCanvas.domNode.height=this._options.canvasInnerHeight,this._slider.setWidth(this._options.minimapWidth)},t.prototype._getBuffer=function(){return this._buffers||this._options.canvasInnerWidth>0&&this._options.canvasInnerHeight>0&&(this._buffers=new Ee(this._canvas.domNode.getContext("2d"),this._options.canvasInnerWidth,this._options.canvasInnerHeight,this._tokensColorTracker.getColor(2))),this._buffers?this._buffers.getBuffer():null},t.prototype._onOptionsMaybeChanged=function(){var e=new Me(this._context.configuration);return!this._options.equals(e)&&(this._options=e,this._lastRenderData=null,this._buffers=null,this._applyLayout(),this._domNode.setClassName(this._getMinimapDomNodeClassName()),!0)},t.prototype.onConfigurationChanged=function(e){return this._onOptionsMaybeChanged()},t.prototype.onCursorStateChanged=function(e){return this._selections=e.selections,this._renderDecorations=!0,!0},t.prototype.onFlushed=function(e){return this._lastRenderData=null,!0},t.prototype.onLinesChanged=function(e){return!!this._lastRenderData&&this._lastRenderData.onLinesChanged(e)},t.prototype.onLinesDeleted=function(e){return this._lastRenderData&&this._lastRenderData.onLinesDeleted(e),!0},t.prototype.onLinesInserted=function(e){return this._lastRenderData&&this._lastRenderData.onLinesInserted(e),!0},t.prototype.onScrollChanged=function(e){return this._renderDecorations=!0,!0},t.prototype.onTokensChanged=function(e){return!!this._lastRenderData&&this._lastRenderData.onTokensChanged(e)},t.prototype.onTokensColorsChanged=function(e){return this._lastRenderData=null,this._buffers=null,!0},t.prototype.onZonesChanged=function(e){return this._lastRenderData=null,!0},t.prototype.onDecorationsChanged=function(e){return this._renderDecorations=!0,!0},t.prototype.onThemeChanged=function(e){return this._context.model.invalidateMinimapColorCache(),this._selectionColor=this._context.theme.getColor(pe["Ib"]),this._renderDecorations=!0,!0},t.prototype.prepareRender=function(e){},t.prototype.render=function(e){var t=this._options.renderMinimap;if(0===t)return this._shadow.setClassName("minimap-shadow-hidden"),this._sliderHorizontal.setWidth(0),void this._sliderHorizontal.setHeight(0);e.scrollLeft+e.viewportWidth>=e.scrollWidth?this._shadow.setClassName("minimap-shadow-hidden"):this._shadow.setClassName("minimap-shadow-visible");var n=Oe.create(this._options,e.visibleRange.startLineNumber,e.visibleRange.endLineNumber,e.viewportHeight,e.viewportData.whitespaceViewportData.length>0,this._context.model.getLineCount(),e.scrollTop,e.scrollHeight,this._lastRenderData?this._lastRenderData.renderedLayout:null);this._slider.setTop(n.sliderTop),this._slider.setHeight(n.sliderHeight);var o=e.scrollLeft/this._options.typicalHalfwidthCharacterWidth,i=Math.min(this._options.minimapWidth,Math.round(o*De(this._options.renderMinimap,this._options.fontScale)/this._options.pixelRatio));this._sliderHorizontal.setLeft(i),this._sliderHorizontal.setWidth(this._options.minimapWidth-i),this._sliderHorizontal.setTop(0),this._sliderHorizontal.setHeight(n.sliderHeight),this.renderDecorations(n),this._lastRenderData=this.renderLines(n)},t.prototype.renderDecorations=function(e){if(this._renderDecorations){this._renderDecorations=!1;var t=this._context.model.getDecorationsInViewport(new P["a"](e.startLineNumber,1,e.endLineNumber,this._context.model.getLineMaxColumn(e.endLineNumber))),n=this._options,o=n.renderMinimap,i=n.canvasInnerWidth,r=n.canvasInnerHeight,s=Se(o,this._options.fontScale),a=De(o,this._options.fontScale),h=this._context.model.getOptions().tabSize,l=this._decorationsCanvas.domNode.getContext("2d");l.clearRect(0,0,i,r);for(var u=new Map,d=0;d<this._selections.length;d++)for(var c=this._selections[d],p=c.startLineNumber;p<=c.endLineNumber;p++)this.renderDecorationOnLine(l,u,c,this._selectionColor,e,p,s,s,h,a);for(d=0;d<t.length;d++){var f=t[d];if(f.options.minimap){var g=f.options.minimap.getColor(this._context.theme);for(p=f.range.startLineNumber;p<=f.range.endLineNumber;p++)switch(f.options.minimap.position){case we["c"].Inline:this.renderDecorationOnLine(l,u,f.range,g,e,p,s,s,h,a);continue;case we["c"].Gutter:var _=(p-e.startLineNumber)*s,m=2;this.renderDecoration(l,g,m,_,xe,s);continue}}}}},t.prototype.renderDecorationOnLine=function(e,t,n,o,i,r,s,a,h,l){var u=(r-i.startLineNumber)*a;if(!(u+s<0||u>this._options.canvasInnerHeight)){var d=t.get(r),c=!d;if(!d){var p=this._context.model.getLineContent(r);d=[ue["f"]];for(var f=1;f<p.length+1;f++){var g=p.charCodeAt(f-1),_=9===g?h*l:le["y"](g)?2*l:l;d[f]=d[f-1]+_}t.set(r,d)}var m=n.startColumn,v=n.endColumn,y=n.startLineNumber,C=n.endLineNumber,b=y===r?d[m-1]:ue["f"],N=C>r?d.length-1:v-1;if(N>0){var w=d[N]-b||2;this.renderDecoration(e,o,b,u,w,s)}c&&this.renderLineHighlight(e,o,u,s)}},t.prototype.renderLineHighlight=function(e,t,n,o){e.fillStyle=t&&t.transparent(.5).toString()||"",e.fillRect(ue["f"],n,e.canvas.width,o)},t.prototype.renderDecoration=function(e,t,n,o,i,r){e.fillStyle=t&&t.toString()||"",e.fillRect(n,o,i,r)},t.prototype.renderLines=function(e){var n=this._options.renderMinimap,o=this._options.charRenderer(),i=e.startLineNumber,r=e.endLineNumber,s=Se(n,this._options.fontScale);if(this._lastRenderData&&this._lastRenderData.linesEquals(e)){var a=this._lastRenderData._get();return new We(e,a.imageData,a.lines)}var h=this._getBuffer();if(!h)return null;for(var l=t._renderUntouchedLines(h,i,r,s,this._lastRenderData),u=l[0],d=l[1],c=l[2],p=this._context.model.getMinimapLinesRenderingData(i,r,c),f=p.tabSize,g=this._tokensColorTracker.getColor(2),_=this._tokensColorTracker.backgroundIsLight(),m=0,v=[],y=0,C=r-i+1;y<C;y++)c[y]&&t._renderLine(h,g,_,n,this._tokensColorTracker,o,m,f,p.data[y],this._options.fontScale),v[y]=new Ae(m),m+=s;var b=-1===u?0:u,N=-1===d?h.height:d,w=N-b,L=this._canvas.domNode.getContext("2d");return L.putImageData(h,0,0,0,b,h.width,w),new We(e,h,v)},t._renderUntouchedLines=function(e,t,n,o,i){var r=[];if(!i){for(var s=0,a=n-t+1;s<a;s++)r[s]=!0;return[-1,-1,r]}for(var h=i._get(),l=h.imageData.data,u=h.rendLineNumberStart,d=h.lines,c=d.length,p=e.width,f=e.data,g=(n-t+1)*o*p*4,_=-1,m=-1,v=-1,y=-1,C=-1,b=-1,N=0,w=t;w<=n;w++){var L=w-t,S=w-u,D=S>=0&&S<c?d[S].dy:-1;if(-1!==D){var R=D*p*4,x=(D+o)*p*4,M=N*p*4,O=(N+o)*p*4;y===R&&b===M?(y=x,b=O):(-1!==v&&(f.set(l.subarray(v,y),C),-1===_&&0===v&&v===C&&(_=y),-1===m&&y===g&&v===C&&(m=v)),v=R,y=x,C=M,b=O),r[L]=!1,N+=o}else r[L]=!0,N+=o}-1!==v&&(f.set(l.subarray(v,y),C),-1===_&&0===v&&v===C&&(_=y),-1===m&&y===g&&v===C&&(m=v));var A=-1===_?-1:_/(4*p),W=-1===m?-1:m/(4*p);return[A,W,r]},t._renderLine=function(e,t,n,o,i,r,s,a,h,l){for(var u=h.content,d=h.tokens,c=De(o,l),p=e.width-c,f=ue["f"],g=0,_=0,m=0,v=d.getCount();m<v;m++)for(var y=d.getEndOffset(m),C=d.getForeground(m),b=i.getColor(C);g<y;g++){if(f>p)return;var N=u.charCodeAt(g);if(9===N){var w=a-(g+_)%a;_+=w-1,f+=w*c}else if(32===N)f+=c;else for(var L=le["y"](N)?2:1,S=0;S<L;S++)if(2===o?r.blockRenderChar(e,f,s,b,t,n):r.renderChar(e,f,s,N,b,t,l,n),f+=c,f>p)return}},t}(v["b"]);Object(A["e"])((function(e,t){var n=e.getColor(pe["Xb"]);if(n){var o=n.transparent(.5);t.addRule(".monaco-editor .minimap-slider, .monaco-editor .minimap-slider .minimap-slider-horizontal { background: "+o+"; }")}var i=e.getColor(pe["Yb"]);if(i){var r=i.transparent(.5);t.addRule(".monaco-editor .minimap-slider:hover, .monaco-editor .minimap-slider:hover .minimap-slider-horizontal { background: "+r+"; }")}var s=e.getColor(pe["Wb"]);if(s){var a=s.transparent(.5);t.addRule(".monaco-editor .minimap-slider.active, .monaco-editor .minimap-slider.active .minimap-slider-horizontal { background: "+a+"; }")}var h=e.getColor(pe["Vb"]);h&&t.addRule(".monaco-editor .minimap-shadow-visible { box-shadow: "+h+" -6px 0 6px -6px inset; }")}));n("725e");var Ie=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),He=function(e){function t(t){var n=e.call(this,t)||this,o=n._context.configuration.options,r=o.get(107);return n._widgets={},n._verticalScrollbarWidth=r.verticalScrollbarWidth,n._minimapWidth=r.minimapWidth,n._horizontalScrollbarHeight=r.horizontalScrollbarHeight,n._editorHeight=r.height,n._editorWidth=r.width,n._domNode=Object(i["b"])(document.createElement("div")),v["a"].write(n._domNode,4),n._domNode.setClassName("overlayWidgets"),n}return Ie(t,e),t.prototype.dispose=function(){e.prototype.dispose.call(this),this._widgets={}},t.prototype.getDomNode=function(){return this._domNode},t.prototype.onConfigurationChanged=function(e){var t=this._context.configuration.options,n=t.get(107);return this._verticalScrollbarWidth=n.verticalScrollbarWidth,this._minimapWidth=n.minimapWidth,this._horizontalScrollbarHeight=n.horizontalScrollbarHeight,this._editorHeight=n.height,this._editorWidth=n.width,!0},t.prototype.addWidget=function(e){var t=Object(i["b"])(e.getDomNode());this._widgets[e.getId()]={widget:e,preference:null,domNode:t},t.setPosition("absolute"),t.setAttribute("widgetId",e.getId()),this._domNode.appendChild(t),this.setShouldRender()},t.prototype.setWidgetPosition=function(e,t){var n=this._widgets[e.getId()];return n.preference!==t&&(n.preference=t,this.setShouldRender(),!0)},t.prototype.removeWidget=function(e){var t=e.getId();if(this._widgets.hasOwnProperty(t)){var n=this._widgets[t],o=n.domNode.domNode;delete this._widgets[t],o.parentNode.removeChild(o),this.setShouldRender()}},t.prototype._renderWidget=function(e){var t=e.domNode;if(null!==e.preference)if(0===e.preference)t.setTop(0),t.setRight(2*this._verticalScrollbarWidth+this._minimapWidth);else if(1===e.preference){var n=t.domNode.clientHeight;t.setTop(this._editorHeight-n-2*this._horizontalScrollbarHeight),t.setRight(2*this._verticalScrollbarWidth+this._minimapWidth)}else 2===e.preference&&(t.setTop(0),t.domNode.style.right="50%");else t.unsetTop()},t.prototype.prepareRender=function(e){},t.prototype.render=function(e){this._domNode.setWidth(this._editorWidth);for(var t=Object.keys(this._widgets),n=0,o=t.length;n<o;n++){var i=t[n];this._renderWidget(this._widgets[i])}},t}(v["b"]),Pe=n("ceb8"),Fe=n("b707"),Be=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),Ve=function(){function e(e,t){var n=e.options;this.lineHeight=n.get(49),this.pixelRatio=n.get(105),this.overviewRulerLanes=n.get(63),this.renderBorder=n.get(62);var o=t.getColor(M["l"]);this.borderColor=o?o.toString():null,this.hideCursor=n.get(42);var i=t.getColor(M["g"]);this.cursorColor=i?i.transparent(.7).toString():null,this.themeType=t.type;var r=n.get(54),s=r.enabled,a=r.side,h=s?Fe["B"].getDefaultBackground():null;this.backgroundColor=null===h||"left"===a?null:Pe["a"].Format.CSS.formatHex(h);var l=n.get(107),u=l.overviewRuler;this.top=u.top,this.right=u.right,this.domWidth=u.width,this.domHeight=u.height,0===this.overviewRulerLanes?(this.canvasWidth=0,this.canvasHeight=0):(this.canvasWidth=this.domWidth*this.pixelRatio|0,this.canvasHeight=this.domHeight*this.pixelRatio|0);var d=this._initLanes(1,this.canvasWidth,this.overviewRulerLanes),c=d[0],p=d[1];this.x=c,this.w=p}return e.prototype._initLanes=function(e,t,n){var o=t-e;if(n>=3){var i=Math.floor(o/3),r=Math.floor(o/3),s=o-i-r,a=e,h=a+i,l=a+i+s;return[[0,a,h,a,l,a,h,a],[0,i,s,i+s,r,i+s+r,s+r,i+s+r]]}if(2===n){i=Math.floor(o/2),r=o-i,a=e,l=a+i;return[[0,a,a,a,l,a,a,a],[0,i,i,i,r,i+r,i+r,i+r]]}var u=e,d=o;return[[0,u,u,u,u,u,u,u],[0,d,d,d,d,d,d,d]]},e.prototype.equals=function(e){return this.lineHeight===e.lineHeight&&this.pixelRatio===e.pixelRatio&&this.overviewRulerLanes===e.overviewRulerLanes&&this.renderBorder===e.renderBorder&&this.borderColor===e.borderColor&&this.hideCursor===e.hideCursor&&this.cursorColor===e.cursorColor&&this.themeType===e.themeType&&this.backgroundColor===e.backgroundColor&&this.top===e.top&&this.right===e.right&&this.domWidth===e.domWidth&&this.domHeight===e.domHeight&&this.canvasWidth===e.canvasWidth&&this.canvasHeight===e.canvasHeight},e}(),ke=function(e){function t(t){var n=e.call(this,t)||this;return n._domNode=Object(i["b"])(document.createElement("canvas")),n._domNode.setClassName("decorationsOverviewRuler"),n._domNode.setPosition("absolute"),n._domNode.setLayerHinting(!0),n._domNode.setContain("strict"),n._domNode.setAttribute("aria-hidden","true"),n._updateSettings(!1),n._tokensColorTrackerListener=Fe["B"].onDidChange((function(e){e.changedColorMap&&n._updateSettings(!0)})),n._cursorPositions=[],n}return Be(t,e),t.prototype.dispose=function(){e.prototype.dispose.call(this),this._tokensColorTrackerListener.dispose()},t.prototype._updateSettings=function(e){var t=new Ve(this._context.configuration,this._context.theme);return(!this._settings||!this._settings.equals(t))&&(this._settings=t,this._domNode.setTop(this._settings.top),this._domNode.setRight(this._settings.right),this._domNode.setWidth(this._settings.domWidth),this._domNode.setHeight(this._settings.domHeight),this._domNode.domNode.width=this._settings.canvasWidth,this._domNode.domNode.height=this._settings.canvasHeight,e&&this._render(),!0)},t.prototype.onConfigurationChanged=function(e){return this._updateSettings(!1)},t.prototype.onCursorStateChanged=function(e){this._cursorPositions=[];for(var t=0,n=e.selections.length;t<n;t++)this._cursorPositions[t]=e.selections[t].getPosition();return this._cursorPositions.sort(l["a"].compare),!0},t.prototype.onDecorationsChanged=function(e){return!0},t.prototype.onFlushed=function(e){return!0},t.prototype.onScrollChanged=function(e){return e.scrollHeightChanged},t.prototype.onZonesChanged=function(e){return!0},t.prototype.onThemeChanged=function(e){return this._context.model.invalidateOverviewRulerColorCache(),this._updateSettings(!1)},t.prototype.getDomNode=function(){return this._domNode.domNode},t.prototype.prepareRender=function(e){},t.prototype.render=function(e){this._render()},t.prototype._render=function(){if(0!==this._settings.overviewRulerLanes){var e=this._settings.canvasWidth,t=this._settings.canvasHeight,n=this._settings.lineHeight,o=this._context.viewLayout,i=this._context.viewLayout.getScrollHeight(),r=t/i,s=this._context.model.getAllOverviewRulerDecorations(this._context.theme),a=6*this._settings.pixelRatio|0,h=a/2|0,l=this._domNode.domNode.getContext("2d");null===this._settings.backgroundColor?l.clearRect(0,0,e,t):(l.fillStyle=this._settings.backgroundColor,l.fillRect(0,0,e,t));var u=this._settings.x,d=this._settings.w,c=Object.keys(s);c.sort();for(var p=0,f=c.length;p<f;p++){var g=c[p],_=s[g];l.fillStyle=g;for(var m=0,v=0,y=0,C=0,b=_.length;C<b;C++){var N=_[3*C],w=_[3*C+1],L=_[3*C+2],S=o.getVerticalOffsetForLineNumber(w)*r|0,D=(o.getVerticalOffsetForLineNumber(L)+n)*r|0,R=D-S;if(R<a){var x=(S+D)/2|0;x<h?x=h:x+h>t&&(x=t-h),S=x-h,D=x+h}S>y+1||N!==m?(0!==C&&l.fillRect(u[m],v,d[m],y-v),m=N,v=S,y=D):D>y&&(y=D)}l.fillRect(u[m],v,d[m],y-v)}if(!this._settings.hideCursor&&this._settings.cursorColor){var M=2*this._settings.pixelRatio|0,O=M/2|0,A=this._settings.x[7],W=this._settings.w[7];l.fillStyle=this._settings.cursorColor;for(v=-100,y=-100,C=0,b=this._cursorPositions.length;C<b;C++){var E=this._cursorPositions[C];x=o.getVerticalOffsetForLineNumber(E.lineNumber)*r|0;x<O?x=O:x+O>t&&(x=t-O);S=x-O,D=S+M;S>y+1?(0!==C&&l.fillRect(A,v,W,y-v),v=S,y=D):D>y&&(y=D)}l.fillRect(A,v,W,y-v)}this._settings.renderBorder&&this._settings.borderColor&&this._settings.overviewRulerLanes>0&&(l.beginPath(),l.lineWidth=1,l.strokeStyle=this._settings.borderColor,l.moveTo(0,0),l.lineTo(0,t),l.stroke(),l.moveTo(0,0),l.lineTo(e,0),l.stroke())}else this._domNode.setBackgroundColor(this._settings.backgroundColor?this._settings.backgroundColor:"")},t}(v["b"]),je=n("32f2"),ze=n("e2dc"),Ue=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),Ze=function(e){function t(t,n){var o=e.call(this)||this;o._context=t;var r=o._context.configuration.options;return o._domNode=Object(i["b"])(document.createElement("canvas")),o._domNode.setClassName(n),o._domNode.setPosition("absolute"),o._domNode.setLayerHinting(!0),o._domNode.setContain("strict"),o._zoneManager=new je["b"]((function(e){return o._context.viewLayout.getVerticalOffsetForLineNumber(e)})),o._zoneManager.setDOMWidth(0),o._zoneManager.setDOMHeight(0),o._zoneManager.setOuterHeight(o._context.viewLayout.getScrollHeight()),o._zoneManager.setLineHeight(r.get(49)),o._zoneManager.setPixelRatio(r.get(105)),o._context.addEventHandler(o),o}return Ue(t,e),t.prototype.dispose=function(){this._context.removeEventHandler(this),e.prototype.dispose.call(this)},t.prototype.onConfigurationChanged=function(e){var t=this._context.configuration.options;return e.hasChanged(49)&&(this._zoneManager.setLineHeight(t.get(49)),this._render()),e.hasChanged(105)&&(this._zoneManager.setPixelRatio(t.get(105)),this._domNode.setWidth(this._zoneManager.getDOMWidth()),this._domNode.setHeight(this._zoneManager.getDOMHeight()),this._domNode.domNode.width=this._zoneManager.getCanvasWidth(),this._domNode.domNode.height=this._zoneManager.getCanvasHeight(),this._render()),!0},t.prototype.onFlushed=function(e){return this._render(),!0},t.prototype.onScrollChanged=function(e){return e.scrollHeightChanged&&(this._zoneManager.setOuterHeight(e.scrollHeight),this._render()),!0},t.prototype.onZonesChanged=function(e){return this._render(),!0},t.prototype.getDomNode=function(){return this._domNode.domNode},t.prototype.setLayout=function(e){this._domNode.setTop(e.top),this._domNode.setRight(e.right);var t=!1;t=this._zoneManager.setDOMWidth(e.width)||t,t=this._zoneManager.setDOMHeight(e.height)||t,t&&(this._domNode.setWidth(this._zoneManager.getDOMWidth()),this._domNode.setHeight(this._zoneManager.getDOMHeight()),this._domNode.domNode.width=this._zoneManager.getCanvasWidth(),this._domNode.domNode.height=this._zoneManager.getCanvasHeight(),this._render())},t.prototype.setZones=function(e){this._zoneManager.setZones(e),this._render()},t.prototype._render=function(){if(0===this._zoneManager.getOuterHeight())return!1;var e=this._zoneManager.getCanvasWidth(),t=this._zoneManager.getCanvasHeight(),n=this._zoneManager.resolveColorZones(),o=this._zoneManager.getId2Color(),i=this._domNode.domNode.getContext("2d");return i.clearRect(0,0,e,t),n.length>0&&this._renderOneLane(i,n,o,e),!0},t.prototype._renderOneLane=function(e,t,n,o){for(var i=0,r=0,s=0,a=0,h=t;a<h.length;a++){var l=h[a],u=l.colorId,d=l.from,c=l.to;u!==i?(e.fillRect(0,r,o,s-r),i=u,e.fillStyle=n[i],r=d,s=c):s>=d?s=Math.max(s,c):(e.fillRect(0,r,o,s-r),r=d,s=c)}e.fillRect(0,r,o,s-r)},t}(ze["a"]),Ke=(n("ef37"),function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}()),Ge=function(e){function t(t){var n=e.call(this,t)||this;n.domNode=Object(i["b"])(document.createElement("div")),n.domNode.setAttribute("role","presentation"),n.domNode.setAttribute("aria-hidden","true"),n.domNode.setClassName("view-rulers"),n._renderedRulers=[];var o=n._context.configuration.options;return n._rulers=o.get(77),n._typicalHalfwidthCharacterWidth=o.get(34).typicalHalfwidthCharacterWidth,n}return Ke(t,e),t.prototype.dispose=function(){e.prototype.dispose.call(this)},t.prototype.onConfigurationChanged=function(e){var t=this._context.configuration.options;return this._rulers=t.get(77),this._typicalHalfwidthCharacterWidth=t.get(34).typicalHalfwidthCharacterWidth,!0},t.prototype.onScrollChanged=function(e){return e.scrollHeightChanged},t.prototype.prepareRender=function(e){},t.prototype._ensureRulersCount=function(){var e=this._renderedRulers.length,t=this._rulers.length;if(e!==t)if(e<t){var n=this._context.model.getOptions().tabSize,o=n,r=t-e;while(r>0){var s=Object(i["b"])(document.createElement("div"));s.setClassName("view-ruler"),s.setWidth(o),this.domNode.appendChild(s),this._renderedRulers.push(s),r--}}else{var a=e-t;while(a>0){s=this._renderedRulers.pop();this.domNode.removeChild(s),a--}}},t.prototype.render=function(e){this._ensureRulersCount();for(var t=0,n=this._rulers.length;t<n;t++){var o=this._renderedRulers[t];o.setHeight(Math.min(e.scrollHeight,1e6)),o.setLeft(this._rulers[t]*this._typicalHalfwidthCharacterWidth)}},t}(v["b"]);Object(A["e"])((function(e,t){var n=e.getColor(M["m"]);n&&t.addRule(".monaco-editor .view-ruler { box-shadow: 1px 0 0 0 "+n+" inset; }")}));n("d8c3");var qe=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),Xe=function(e){function t(t){var n=e.call(this,t)||this;n._scrollTop=0,n._width=0,n._updateWidth(),n._shouldShow=!1;var o=n._context.configuration.options,r=o.get(78);return n._useShadows=r.useShadows,n._domNode=Object(i["b"])(document.createElement("div")),n._domNode.setAttribute("role","presentation"),n._domNode.setAttribute("aria-hidden","true"),n}return qe(t,e),t.prototype.dispose=function(){e.prototype.dispose.call(this)},t.prototype._updateShouldShow=function(){var e=this._useShadows&&this._scrollTop>0;return this._shouldShow!==e&&(this._shouldShow=e,!0)},t.prototype.getDomNode=function(){return this._domNode},t.prototype._updateWidth=function(){var e=this._context.configuration.options,t=e.get(107);0===t.renderMinimap||t.minimapWidth>0&&0===t.minimapLeft?this._width=t.width:this._width=t.width-t.minimapWidth-t.verticalScrollbarWidth},t.prototype.onConfigurationChanged=function(e){var t=this._context.configuration.options,n=t.get(78);return this._useShadows=n.useShadows,this._updateWidth(),this._updateShouldShow(),!0},t.prototype.onScrollChanged=function(e){return this._scrollTop=e.scrollTop,this._updateShouldShow()},t.prototype.prepareRender=function(e){},t.prototype.render=function(e){this._domNode.setWidth(this._width),this._domNode.setClassName(this._shouldShow?"scroll-decoration":"")},t}(v["b"]);Object(A["e"])((function(e,t){var n=e.getColor(pe["Vb"]);n&&t.addRule(".monaco-editor .scroll-decoration { box-shadow: "+n+" 0 6px 6px -6px inset; }")}));n("782d");var Ye=n("0f70"),$e=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),Je=function(){function e(e){this.left=e.left,this.width=e.width,this.startStyle=null,this.endStyle=null}return e}(),Qe=function(){function e(e,t){this.lineNumber=e,this.ranges=t}return e}();function et(e){return new Je(e)}function tt(e){return new Qe(e.lineNumber,e.ranges.map(et))}var nt=Ye["f"],ot=function(e){function t(t){var n=e.call(this)||this;n._previousFrameVisibleRangesWithStyle=[],n._context=t;var o=n._context.configuration.options;return n._lineHeight=o.get(49),n._roundedSelection=o.get(76),n._typicalHalfwidthCharacterWidth=o.get(34).typicalHalfwidthCharacterWidth,n._selections=[],n._renderResult=null,n._context.addEventHandler(n),n}return $e(t,e),t.prototype.dispose=function(){this._context.removeEventHandler(this),this._renderResult=null,e.prototype.dispose.call(this)},t.prototype.onConfigurationChanged=function(e){var t=this._context.configuration.options;return this._lineHeight=t.get(49),this._roundedSelection=t.get(76),this._typicalHalfwidthCharacterWidth=t.get(34).typicalHalfwidthCharacterWidth,!0},t.prototype.onCursorStateChanged=function(e){return this._selections=e.selections.slice(0),!0},t.prototype.onDecorationsChanged=function(e){return!0},t.prototype.onFlushed=function(e){return!0},t.prototype.onLinesChanged=function(e){return!0},t.prototype.onLinesDeleted=function(e){return!0},t.prototype.onLinesInserted=function(e){return!0},t.prototype.onScrollChanged=function(e){return e.scrollTopChanged},t.prototype.onZonesChanged=function(e){return!0},t.prototype._visibleRangesHaveGaps=function(e){for(var t=0,n=e.length;t<n;t++){var o=e[t];if(o.ranges.length>1)return!0}return!1},t.prototype._enrichVisibleRangesWithStyle=function(e,t,n){var o=this._typicalHalfwidthCharacterWidth/4,i=null,r=null;if(n&&n.length>0&&t.length>0){var s=t[0].lineNumber;if(s===e.startLineNumber)for(var a=0;!i&&a<n.length;a++)n[a].lineNumber===s&&(i=n[a].ranges[0]);var h=t[t.length-1].lineNumber;if(h===e.endLineNumber)for(a=n.length-1;!r&&a>=0;a--)n[a].lineNumber===h&&(r=n[a].ranges[0]);i&&!i.startStyle&&(i=null),r&&!r.startStyle&&(r=null)}a=0;for(var l=t.length;a<l;a++){var u=t[a].ranges[0],d=u.left,c=u.left+u.width,p={top:0,bottom:0},f={top:0,bottom:0};if(a>0){var g=t[a-1].ranges[0].left,_=t[a-1].ranges[0].left+t[a-1].ranges[0].width;it(d-g)<o?p.top=2:d>g&&(p.top=1),it(c-_)<o?f.top=2:g<c&&c<_&&(f.top=1)}else i&&(p.top=i.startStyle.top,f.top=i.endStyle.top);if(a+1<l){var m=t[a+1].ranges[0].left,v=t[a+1].ranges[0].left+t[a+1].ranges[0].width;it(d-m)<o?p.bottom=2:m<d&&d<v&&(p.bottom=1),it(c-v)<o?f.bottom=2:c<v&&(f.bottom=1)}else r&&(p.bottom=r.startStyle.bottom,f.bottom=r.endStyle.bottom);u.startStyle=p,u.endStyle=f}},t.prototype._getVisibleRangesWithStyle=function(e,t,n){var o=t.linesVisibleRangesForRange(e,!0)||[],i=o.map(tt),r=this._visibleRangesHaveGaps(i);return nt||r||!this._roundedSelection||this._enrichVisibleRangesWithStyle(t.visibleRange,i,n),i},t.prototype._createSelectionPiece=function(e,t,n,o,i){return'<div class="cslr '+n+'" style="top:'+e.toString()+"px;left:"+o.toString()+"px;width:"+i.toString()+"px;height:"+t+'px;"></div>'},t.prototype._actualRenderOneSelection=function(e,n,o,i){if(0!==i.length)for(var r=!!i[0].ranges[0].startStyle,s=this._lineHeight.toString(),a=(this._lineHeight-1).toString(),h=i[0].lineNumber,l=i[i.length-1].lineNumber,u=0,d=i.length;u<d;u++){for(var c=i[u],p=c.lineNumber,f=p-n,g=o&&(p===l||p===h)?a:s,_=o&&p===h?1:0,m="",v="",y=0,C=c.ranges.length;y<C;y++){var b=c.ranges[y];if(r){var N=b.startStyle,w=b.endStyle;if(1===N.top||1===N.bottom){m+=this._createSelectionPiece(_,g,t.SELECTION_CLASS_NAME,b.left-t.ROUNDED_PIECE_WIDTH,t.ROUNDED_PIECE_WIDTH);var L=t.EDITOR_BACKGROUND_CLASS_NAME;1===N.top&&(L+=" "+t.SELECTION_TOP_RIGHT),1===N.bottom&&(L+=" "+t.SELECTION_BOTTOM_RIGHT),m+=this._createSelectionPiece(_,g,L,b.left-t.ROUNDED_PIECE_WIDTH,t.ROUNDED_PIECE_WIDTH)}if(1===w.top||1===w.bottom){m+=this._createSelectionPiece(_,g,t.SELECTION_CLASS_NAME,b.left+b.width,t.ROUNDED_PIECE_WIDTH);var S=t.EDITOR_BACKGROUND_CLASS_NAME;1===w.top&&(S+=" "+t.SELECTION_TOP_LEFT),1===w.bottom&&(S+=" "+t.SELECTION_BOTTOM_LEFT),m+=this._createSelectionPiece(_,g,S,b.left+b.width,t.ROUNDED_PIECE_WIDTH)}}var D=t.SELECTION_CLASS_NAME;if(r){N=b.startStyle,w=b.endStyle;0===N.top&&(D+=" "+t.SELECTION_TOP_LEFT),0===N.bottom&&(D+=" "+t.SELECTION_BOTTOM_LEFT),0===w.top&&(D+=" "+t.SELECTION_TOP_RIGHT),0===w.bottom&&(D+=" "+t.SELECTION_BOTTOM_RIGHT)}v+=this._createSelectionPiece(_,g,D,b.left,b.width)}e[f][0]+=m,e[f][1]+=v}},t.prototype.prepareRender=function(e){for(var t=[],n=e.visibleRange.startLineNumber,o=e.visibleRange.endLineNumber,i=n;i<=o;i++){var r=i-n;t[r]=["",""]}for(var s=[],a=0,h=this._selections.length;a<h;a++){var l=this._selections[a];if(l.isEmpty())s[a]=null;else{var u=this._getVisibleRangesWithStyle(l,e,this._previousFrameVisibleRangesWithStyle[a]);s[a]=u,this._actualRenderOneSelection(t,n,this._selections.length>1,u)}}this._previousFrameVisibleRangesWithStyle=s,this._renderResult=t.map((function(e){var t=e[0],n=e[1];return t+n}))},t.prototype.render=function(e,t){if(!this._renderResult)return"";var n=t-e;return n<0||n>=this._renderResult.length?"":this._renderResult[n]},t.SELECTION_CLASS_NAME="selected-text",t.SELECTION_TOP_LEFT="top-left-radius",t.SELECTION_BOTTOM_LEFT="bottom-left-radius",t.SELECTION_TOP_RIGHT="top-right-radius",t.SELECTION_BOTTOM_RIGHT="bottom-right-radius",t.EDITOR_BACKGROUND_CLASS_NAME="monaco-editor-background",t.ROUNDED_PIECE_WIDTH=10,t}(x["a"]);function it(e){return e<0?-e:e}Object(A["e"])((function(e,t){var n=e.getColor(pe["K"]);n&&t.addRule(".monaco-editor .focused .selected-text { background-color: "+n+"; }");var o=e.getColor(pe["F"]);o&&t.addRule(".monaco-editor .selected-text { background-color: "+o+"; }");var i=e.getColor(pe["L"]);i&&t.addRule(".monaco-editor .view-line span.inline-selected-text { color: "+i+"; }")}));n("d93b");var rt=function(){function e(e,t,n,o,i,r){this.top=e,this.left=t,this.width=n,this.height=o,this.textContent=i,this.textContentClassName=r}return e}(),st=function(){function e(e){this._context=e;var t=this._context.configuration.options,n=t.get(34);this._cursorStyle=t.get(18),this._lineHeight=t.get(49),this._typicalHalfwidthCharacterWidth=n.typicalHalfwidthCharacterWidth,this._lineCursorWidth=Math.min(t.get(21),this._typicalHalfwidthCharacterWidth),this._isVisible=!0,this._domNode=Object(i["b"])(document.createElement("div")),this._domNode.setClassName("cursor"),this._domNode.setHeight(this._lineHeight),this._domNode.setTop(0),this._domNode.setLeft(0),p["a"].applyFontInfo(this._domNode,n),this._domNode.setDisplay("none"),this._position=new l["a"](1,1),this._lastRenderedContent="",this._renderData=null}return e.prototype.getDomNode=function(){return this._domNode},e.prototype.getPosition=function(){return this._position},e.prototype.show=function(){this._isVisible||(this._domNode.setVisibility("inherit"),this._isVisible=!0)},e.prototype.hide=function(){this._isVisible&&(this._domNode.setVisibility("hidden"),this._isVisible=!1)},e.prototype.onConfigurationChanged=function(e){var t=this._context.configuration.options,n=t.get(34);return this._cursorStyle=t.get(18),this._lineHeight=t.get(49),this._typicalHalfwidthCharacterWidth=n.typicalHalfwidthCharacterWidth,this._lineCursorWidth=Math.min(t.get(21),this._typicalHalfwidthCharacterWidth),p["a"].applyFontInfo(this._domNode,n),!0},e.prototype.onCursorPositionChanged=function(e){return this._position=e,!0},e.prototype._prepareRender=function(e){var t="";if(this._cursorStyle===ue["g"].Line||this._cursorStyle===ue["g"].LineThin){var n,i=e.visibleRangeForPosition(this._position);if(!i||i.outsideRenderedLine)return null;if(this._cursorStyle===ue["g"].Line){if(n=o["u"](this._lineCursorWidth>0?this._lineCursorWidth:2),n>2){var r=this._context.model.getLineContent(this._position.lineNumber),s=le["E"](r,this._position.column-1);t=r.substr(this._position.column-1,s)}}else n=o["u"](1);var a=i.left;n>=2&&a>=1&&(a-=1);var h=e.getVerticalOffsetForLineNumber(this._position.lineNumber)-e.bigNumbersDelta;return new rt(h,a,n,this._lineHeight,t,"")}var l=this._context.model.getLineContent(this._position.lineNumber),u=le["E"](l,this._position.column-1),d=e.linesVisibleRangesForRange(new P["a"](this._position.lineNumber,this._position.column,this._position.lineNumber,this._position.column+u),!1);if(!d||0===d.length)return null;var c=d[0];if(c.outsideRenderedLine||0===c.ranges.length)return null;var p=c.ranges[0],f=p.width<1?this._typicalHalfwidthCharacterWidth:p.width,g="";if(this._cursorStyle===ue["g"].Block){var _=this._context.model.getViewLineData(this._position.lineNumber);t=l.substr(this._position.column-1,u);var m=_.tokens.findTokenIndexAtOffset(this._position.column-1);g=_.tokens.getClassName(m)}var v=e.getVerticalOffsetForLineNumber(this._position.lineNumber)-e.bigNumbersDelta,y=this._lineHeight;return this._cursorStyle!==ue["g"].Underline&&this._cursorStyle!==ue["g"].UnderlineThin||(v+=this._lineHeight-2,y=2),new rt(v,p.left,f,y,t,g)},e.prototype.prepareRender=function(e){this._renderData=this._prepareRender(e)},e.prototype.render=function(e){return this._renderData?(this._lastRenderedContent!==this._renderData.textContent&&(this._lastRenderedContent=this._renderData.textContent,this._domNode.domNode.textContent=this._lastRenderedContent),this._domNode.setClassName("cursor "+this._renderData.textContentClassName),this._domNode.setDisplay("block"),this._domNode.setTop(this._renderData.top),this._domNode.setLeft(this._renderData.left),this._domNode.setWidth(this._renderData.width),this._domNode.setLineHeight(this._renderData.height),this._domNode.setHeight(this._renderData.height),{domNode:this._domNode.domNode,position:this._position,contentLeft:this._renderData.left,height:this._renderData.height,width:2}):(this._domNode.setDisplay("none"),null)},e}(),at=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),ht=function(e){function t(t){var n=e.call(this,t)||this,o=n._context.configuration.options;return n._readOnly=o.get(68),n._cursorBlinking=o.get(16),n._cursorStyle=o.get(18),n._cursorSmoothCaretAnimation=o.get(17),n._selectionIsEmpty=!0,n._isVisible=!1,n._primaryCursor=new st(n._context),n._secondaryCursors=[],n._renderData=[],n._domNode=Object(i["b"])(document.createElement("div")),n._domNode.setAttribute("role","presentation"),n._domNode.setAttribute("aria-hidden","true"),n._updateDomClassName(),n._domNode.appendChild(n._primaryCursor.getDomNode()),n._startCursorBlinkAnimation=new $["e"],n._cursorFlatBlinkInterval=new $["c"],n._blinkingEnabled=!1,n._editorHasFocus=!1,n._updateBlinking(),n}return at(t,e),t.prototype.dispose=function(){e.prototype.dispose.call(this),this._startCursorBlinkAnimation.dispose(),this._cursorFlatBlinkInterval.dispose()},t.prototype.getDomNode=function(){return this._domNode},t.prototype.onConfigurationChanged=function(e){var t=this._context.configuration.options;this._readOnly=t.get(68),this._cursorBlinking=t.get(16),this._cursorStyle=t.get(18),this._cursorSmoothCaretAnimation=t.get(17),this._updateBlinking(),this._updateDomClassName(),this._primaryCursor.onConfigurationChanged(e);for(var n=0,o=this._secondaryCursors.length;n<o;n++)this._secondaryCursors[n].onConfigurationChanged(e);return!0},t.prototype._onCursorPositionChanged=function(e,t){if(this._primaryCursor.onCursorPositionChanged(e),this._updateBlinking(),this._secondaryCursors.length<t.length)for(var n=t.length-this._secondaryCursors.length,o=0;o<n;o++){var i=new st(this._context);this._domNode.domNode.insertBefore(i.getDomNode().domNode,this._primaryCursor.getDomNode().domNode.nextSibling),this._secondaryCursors.push(i)}else if(this._secondaryCursors.length>t.length){var r=this._secondaryCursors.length-t.length;for(o=0;o<r;o++)this._domNode.removeChild(this._secondaryCursors[0].getDomNode()),this._secondaryCursors.splice(0,1)}for(o=0;o<t.length;o++)this._secondaryCursors[o].onCursorPositionChanged(t[o])},t.prototype.onCursorStateChanged=function(e){for(var t=[],n=0,o=e.selections.length;n<o;n++)t[n]=e.selections[n].getPosition();this._onCursorPositionChanged(t[0],t.slice(1));var i=e.selections[0].isEmpty();return this._selectionIsEmpty!==i&&(this._selectionIsEmpty=i,this._updateDomClassName()),!0},t.prototype.onDecorationsChanged=function(e){return!0},t.prototype.onFlushed=function(e){return!0},t.prototype.onFocusChanged=function(e){return this._editorHasFocus=e.isFocused,this._updateBlinking(),!1},t.prototype.onLinesChanged=function(e){return!0},t.prototype.onLinesDeleted=function(e){return!0},t.prototype.onLinesInserted=function(e){return!0},t.prototype.onScrollChanged=function(e){return!0},t.prototype.onTokensChanged=function(e){var t=function(t){for(var n=0,o=e.ranges.length;n<o;n++)if(e.ranges[n].fromLineNumber<=t.lineNumber&&t.lineNumber<=e.ranges[n].toLineNumber)return!0;return!1};if(t(this._primaryCursor.getPosition()))return!0;for(var n=0,o=this._secondaryCursors;n<o.length;n++){var i=o[n];if(t(i.getPosition()))return!0}return!1},t.prototype.onZonesChanged=function(e){return!0},t.prototype._getCursorBlinking=function(){return this._editorHasFocus?this._readOnly?5:this._cursorBlinking:0},t.prototype._updateBlinking=function(){var e=this;this._startCursorBlinkAnimation.cancel(),this._cursorFlatBlinkInterval.cancel();var n=this._getCursorBlinking(),o=0===n,i=5===n;o?this._hide():this._show(),this._blinkingEnabled=!1,this._updateDomClassName(),o||i||(1===n?this._cursorFlatBlinkInterval.cancelAndSet((function(){e._isVisible?e._hide():e._show()}),t.BLINK_INTERVAL):this._startCursorBlinkAnimation.setIfNotSet((function(){e._blinkingEnabled=!0,e._updateDomClassName()}),t.BLINK_INTERVAL))},t.prototype._updateDomClassName=function(){this._domNode.setClassName(this._getClassName())},t.prototype._getClassName=function(){var e="cursors-layer";switch(this._selectionIsEmpty||(e+=" has-selection"),this._cursorStyle){case ue["g"].Line:e+=" cursor-line-style";break;case ue["g"].Block:e+=" cursor-block-style";break;case ue["g"].Underline:e+=" cursor-underline-style";break;case ue["g"].LineThin:e+=" cursor-line-thin-style";break;case ue["g"].BlockOutline:e+=" cursor-block-outline-style";break;case ue["g"].UnderlineThin:e+=" cursor-underline-thin-style";break;default:e+=" cursor-line-style"}if(this._blinkingEnabled)switch(this._getCursorBlinking()){case 1:e+=" cursor-blink";break;case 2:e+=" cursor-smooth";break;case 3:e+=" cursor-phase";break;case 4:e+=" cursor-expand";break;case 5:e+=" cursor-solid";break;default:e+=" cursor-solid"}else e+=" cursor-solid";return this._cursorSmoothCaretAnimation&&(e+=" cursor-smooth-caret-animation"),e},t.prototype._show=function(){this._primaryCursor.show();for(var e=0,t=this._secondaryCursors.length;e<t;e++)this._secondaryCursors[e].show();this._isVisible=!0},t.prototype._hide=function(){this._primaryCursor.hide();for(var e=0,t=this._secondaryCursors.length;e<t;e++)this._secondaryCursors[e].hide();this._isVisible=!1},t.prototype.prepareRender=function(e){this._primaryCursor.prepareRender(e);for(var t=0,n=this._secondaryCursors.length;t<n;t++)this._secondaryCursors[t].prepareRender(e)},t.prototype.render=function(e){var t=[],n=0,o=this._primaryCursor.render(e);o&&(t[n++]=o);for(var i=0,r=this._secondaryCursors.length;i<r;i++){var s=this._secondaryCursors[i].render(e);s&&(t[n++]=s)}this._renderData=t},t.prototype.getLastRenderData=function(){return this._renderData},t.BLINK_INTERVAL=500,t}(v["b"]);Object(A["e"])((function(e,t){var n=e.getColor(M["g"]);if(n){var o=e.getColor(M["f"]);o||(o=n.opposite()),t.addRule(".monaco-editor .cursor { background-color: "+n+"; border-color: "+n+"; color: "+o+"; }"),"hc"===e.type&&t.addRule(".monaco-editor .cursors-layer.has-selection .cursor { border-left: 1px solid "+o+"; border-right: 1px solid "+o+"; }")}}));var lt=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),ut=function(){throw new Error("Invalid change accessor")},dt=function(e){function t(t){var n=e.call(this,t)||this,o=n._context.configuration.options,r=o.get(107);return n._lineHeight=o.get(49),n._contentWidth=r.contentWidth,n._contentLeft=r.contentLeft,n.domNode=Object(i["b"])(document.createElement("div")),n.domNode.setClassName("view-zones"),n.domNode.setPosition("absolute"),n.domNode.setAttribute("role","presentation"),n.domNode.setAttribute("aria-hidden","true"),n.marginDomNode=Object(i["b"])(document.createElement("div")),n.marginDomNode.setClassName("margin-view-zones"),n.marginDomNode.setPosition("absolute"),n.marginDomNode.setAttribute("role","presentation"),n.marginDomNode.setAttribute("aria-hidden","true"),n._zones={},n}return lt(t,e),t.prototype.dispose=function(){e.prototype.dispose.call(this),this._zones={}},t.prototype._recomputeWhitespacesProps=function(){for(var e=this,t=this._context.viewLayout.getWhitespaces(),n=new Map,o=0,i=t;o<i.length;o++){var r=i[o];n.set(r.id,r)}return this._context.viewLayout.changeWhitespace((function(t){for(var o=!1,i=Object.keys(e._zones),r=0,s=i.length;r<s;r++){var a=i[r],h=e._zones[a],l=e._computeWhitespaceProps(h.delegate),u=n.get(a);!u||u.afterLineNumber===l.afterViewLineNumber&&u.height===l.heightInPx||(t.changeOneWhitespace(a,l.afterViewLineNumber,l.heightInPx),e._safeCallOnComputedHeight(h.delegate,l.heightInPx),o=!0)}return o}))},t.prototype.onConfigurationChanged=function(e){var t=this._context.configuration.options,n=t.get(107);return this._lineHeight=t.get(49),this._contentWidth=n.contentWidth,this._contentLeft=n.contentLeft,e.hasChanged(49)&&this._recomputeWhitespacesProps(),!0},t.prototype.onLineMappingChanged=function(e){var t=this._recomputeWhitespacesProps();return t&&this._context.viewLayout.onHeightMaybeChanged(),t},t.prototype.onLinesDeleted=function(e){return!0},t.prototype.onScrollChanged=function(e){return e.scrollTopChanged||e.scrollWidthChanged},t.prototype.onZonesChanged=function(e){return!0},t.prototype.onLinesInserted=function(e){return!0},t.prototype._getZoneOrdinal=function(e){return"undefined"!==typeof e.afterColumn?e.afterColumn:1e4},t.prototype._computeWhitespaceProps=function(e){if(0===e.afterLineNumber)return{afterViewLineNumber:0,heightInPx:this._heightInPixels(e),minWidthInPx:this._minWidthInPixels(e)};var t,n;if("undefined"!==typeof e.afterColumn)t=this._context.model.validateModelPosition({lineNumber:e.afterLineNumber,column:e.afterColumn});else{var o=this._context.model.validateModelPosition({lineNumber:e.afterLineNumber,column:1}).lineNumber;t=new l["a"](o,this._context.model.getModelLineMaxColumn(o))}n=t.column===this._context.model.getModelLineMaxColumn(t.lineNumber)?this._context.model.validateModelPosition({lineNumber:t.lineNumber+1,column:1}):this._context.model.validateModelPosition({lineNumber:t.lineNumber,column:t.column+1});var i=this._context.model.coordinatesConverter.convertModelPositionToViewPosition(t),r=this._context.model.coordinatesConverter.modelPositionIsVisible(n);return{afterViewLineNumber:i.lineNumber,heightInPx:r?this._heightInPixels(e):0,minWidthInPx:this._minWidthInPixels(e)}},t.prototype.changeViewZones=function(e){var t=this;return this._context.viewLayout.changeWhitespace((function(n){var o=!1,i={addZone:function(e){return o=!0,t._addZone(n,e)},removeZone:function(e){e&&(o=t._removeZone(n,e)||o)},layoutZone:function(e){e&&(o=t._layoutZone(n,e)||o)}};return ct(e,i),i.addZone=ut,i.removeZone=ut,i.layoutZone=ut,o}))},t.prototype._addZone=function(e,t){var n=this._computeWhitespaceProps(t),o=e.insertWhitespace(n.afterViewLineNumber,this._getZoneOrdinal(t),n.heightInPx,n.minWidthInPx),r={whitespaceId:o,delegate:t,isVisible:!1,domNode:Object(i["b"])(t.domNode),marginDomNode:t.marginDomNode?Object(i["b"])(t.marginDomNode):null};return this._safeCallOnComputedHeight(r.delegate,n.heightInPx),r.domNode.setPosition("absolute"),r.domNode.domNode.style.width="100%",r.domNode.setDisplay("none"),r.domNode.setAttribute("monaco-view-zone",r.whitespaceId),this.domNode.appendChild(r.domNode),r.marginDomNode&&(r.marginDomNode.setPosition("absolute"),r.marginDomNode.domNode.style.width="100%",r.marginDomNode.setDisplay("none"),r.marginDomNode.setAttribute("monaco-view-zone",r.whitespaceId),this.marginDomNode.appendChild(r.marginDomNode)),this._zones[r.whitespaceId]=r,this.setShouldRender(),r.whitespaceId},t.prototype._removeZone=function(e,t){if(this._zones.hasOwnProperty(t)){var n=this._zones[t];return delete this._zones[t],e.removeWhitespace(n.whitespaceId),n.domNode.removeAttribute("monaco-visible-view-zone"),n.domNode.removeAttribute("monaco-view-zone"),n.domNode.domNode.parentNode.removeChild(n.domNode.domNode),n.marginDomNode&&(n.marginDomNode.removeAttribute("monaco-visible-view-zone"),n.marginDomNode.removeAttribute("monaco-view-zone"),n.marginDomNode.domNode.parentNode.removeChild(n.marginDomNode.domNode)),this.setShouldRender(),!0}return!1},t.prototype._layoutZone=function(e,t){if(this._zones.hasOwnProperty(t)){var n=this._zones[t],o=this._computeWhitespaceProps(n.delegate);return e.changeOneWhitespace(n.whitespaceId,o.afterViewLineNumber,o.heightInPx),this._safeCallOnComputedHeight(n.delegate,o.heightInPx),this.setShouldRender(),!0}return!1},t.prototype.shouldSuppressMouseDownOnViewZone=function(e){if(this._zones.hasOwnProperty(e)){var t=this._zones[e];return Boolean(t.delegate.suppressMouseDown)}return!1},t.prototype._heightInPixels=function(e){return"number"===typeof e.heightInPx?e.heightInPx:"number"===typeof e.heightInLines?this._lineHeight*e.heightInLines:this._lineHeight},t.prototype._minWidthInPixels=function(e){return"number"===typeof e.minWidthInPx?e.minWidthInPx:0},t.prototype._safeCallOnComputedHeight=function(e,t){if("function"===typeof e.onComputedHeight)try{e.onComputedHeight(t)}catch(n){Object(r["e"])(n)}},t.prototype._safeCallOnDomNodeTop=function(e,t){if("function"===typeof e.onDomNodeTop)try{e.onDomNodeTop(t)}catch(n){Object(r["e"])(n)}},t.prototype.prepareRender=function(e){},t.prototype.render=function(e){for(var t=e.viewportData.whitespaceViewportData,n={},o=!1,i=0,r=t.length;i<r;i++)n[t[i].id]=t[i],o=!0;var s=Object.keys(this._zones);for(i=0,r=s.length;i<r;i++){var a=s[i],h=this._zones[a],l=0,u=0,d="none";n.hasOwnProperty(a)?(l=n[a].verticalOffset-e.bigNumbersDelta,u=n[a].height,d="block",h.isVisible||(h.domNode.setAttribute("monaco-visible-view-zone","true"),h.isVisible=!0),this._safeCallOnDomNodeTop(h.delegate,e.getScrolledTopFromAbsoluteTop(n[a].verticalOffset))):(h.isVisible&&(h.domNode.removeAttribute("monaco-visible-view-zone"),h.isVisible=!1),this._safeCallOnDomNodeTop(h.delegate,e.getScrolledTopFromAbsoluteTop(-1e6))),h.domNode.setTop(l),h.domNode.setHeight(u),h.domNode.setDisplay(d),h.marginDomNode&&(h.marginDomNode.setTop(l),h.marginDomNode.setHeight(u),h.marginDomNode.setDisplay(d))}o&&(this.domNode.setWidth(Math.max(e.scrollWidth,this._contentWidth)),this.marginDomNode.setWidth(this._contentLeft))},t}(v["b"]);function ct(e,t){try{return e(t)}catch(n){Object(r["e"])(n)}}var pt=n("5380"),ft=n("a1f8"),gt=n("e06b"),_t=n("bae1"),mt=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),vt=function(e){function t(t,n,o,r,h,l){var u=e.call(this)||this;u._cursor=h,u._renderAnimationFrame=null,u.outgoingEvents=l;var c=new d(n,r,u.outgoingEvents,t);u.eventDispatcher=new ft["a"]((function(e){return u._renderOnce(e)})),u.eventDispatcher.addEventHandler(u),u._context=new pt["a"](n,o.getTheme(),r,u.eventDispatcher),u._register(o.onThemeChange((function(e){u._context.theme=e,u.eventDispatcher.emit(new ce["o"]),u.render(!0,!1)}))),u.viewParts=[],u._textAreaHandler=new a["a"](u._context,c,u.createTextAreaHandlerHelper()),u.viewParts.push(u._textAreaHandler),u.linesContent=Object(i["b"])(document.createElement("div")),u.linesContent.setClassName("lines-content monaco-editor-background"),u.linesContent.setPosition("absolute"),u.domNode=Object(i["b"])(document.createElement("div")),u.domNode.setClassName(u.getEditorClassName()),u.overflowGuardContainer=Object(i["b"])(document.createElement("div")),v["a"].write(u.overflowGuardContainer,3),u.overflowGuardContainer.setClassName("overflow-guard"),u._scrollbar=new z(u._context,u.linesContent,u.domNode,u.overflowGuardContainer),u.viewParts.push(u._scrollbar),u.viewLines=new ne(u._context,u.linesContent),u.viewZones=new dt(u._context),u.viewParts.push(u.viewZones);var p=new ke(u._context);u.viewParts.push(p);var f=new Xe(u._context);u.viewParts.push(f);var g=new N(u._context);u.viewParts.push(g),g.addDynamicOverlay(new I(u._context)),g.addDynamicOverlay(new ot(u._context)),g.addDynamicOverlay(new X(u._context)),g.addDynamicOverlay(new V(u._context));var _=new w(u._context);u.viewParts.push(_),_.addDynamicOverlay(new H(u._context)),_.addDynamicOverlay(new G(u._context)),_.addDynamicOverlay(new ae(u._context)),_.addDynamicOverlay(new ie(u._context)),_.addDynamicOverlay(new Y["a"](u._context));var m=new re["a"](u._context);m.getDomNode().appendChild(u.viewZones.marginDomNode),m.getDomNode().appendChild(_.getDomNode()),u.viewParts.push(m),u.contentWidgets=new D(u._context,u.domNode),u.viewParts.push(u.contentWidgets),u.viewCursors=new ht(u._context),u.viewParts.push(u.viewCursors),u.overlayWidgets=new He(u._context),u.viewParts.push(u.overlayWidgets);var y=new Ge(u._context);u.viewParts.push(y);var C=new Te(u._context);if(u.viewParts.push(C),p){var b=u._scrollbar.getOverviewRulerLayoutInfo();b.parent.insertBefore(p.getDomNode(),b.insertBefore)}return u.linesContent.appendChild(g.getDomNode()),u.linesContent.appendChild(y.domNode),u.linesContent.appendChild(u.viewZones.domNode),u.linesContent.appendChild(u.viewLines.getDomNode()),u.linesContent.appendChild(u.contentWidgets.domNode),u.linesContent.appendChild(u.viewCursors.getDomNode()),u.overflowGuardContainer.appendChild(m.getDomNode()),u.overflowGuardContainer.appendChild(u._scrollbar.getDomNode()),u.overflowGuardContainer.appendChild(f.getDomNode()),u.overflowGuardContainer.appendChild(u._textAreaHandler.textArea),u.overflowGuardContainer.appendChild(u._textAreaHandler.textAreaCover),u.overflowGuardContainer.appendChild(u.overlayWidgets.getDomNode()),u.overflowGuardContainer.appendChild(C.getDomNode()),u.domNode.appendChild(u.overflowGuardContainer),u.domNode.appendChild(u.contentWidgets.overflowingContentWidgetsDomNode),u._applyLayout(),u.pointerHandler=u._register(new s["a"](u._context,c,u.createPointerHandlerHelper())),u._register(r.addEventListener((function(e){u.eventDispatcher.emitMany(e)}))),u._register(u._cursor.addEventListener((function(e){u.eventDispatcher.emitMany(e)}))),u}return mt(t,e),t.prototype._flushAccumulatedAndRenderNow=function(){this._renderNow()},t.prototype.createPointerHandlerHelper=function(){var e=this;return{viewDomNode:this.domNode.domNode,linesContentDomNode:this.linesContent.domNode,focusTextArea:function(){e.focus()},getLastRenderData:function(){var t=e.viewCursors.getLastRenderData()||[],n=e._textAreaHandler.getLastRenderData();return new _t["d"](t,n)},shouldSuppressMouseDownOnViewZone:function(t){return e.viewZones.shouldSuppressMouseDownOnViewZone(t)},shouldSuppressMouseDownOnWidget:function(t){return e.contentWidgets.shouldSuppressMouseDownOnWidget(t)},getPositionFromDOMInfo:function(t,n){return e._flushAccumulatedAndRenderNow(),e.viewLines.getPositionFromDOMInfo(t,n)},visibleRangeForPosition:function(t,n){return e._flushAccumulatedAndRenderNow(),e.viewLines.visibleRangeForPosition(new l["a"](t,n))},getLineWidth:function(t){return e._flushAccumulatedAndRenderNow(),e.viewLines.getLineWidth(t)}}},t.prototype.createTextAreaHandlerHelper=function(){var e=this;return{visibleRangeForPositionRelativeToEditor:function(t,n){return e._flushAccumulatedAndRenderNow(),e.viewLines.visibleRangeForPosition(new l["a"](t,n))}}},t.prototype._applyLayout=function(){var e=this._context.configuration.options,t=e.get(107);this.domNode.setWidth(t.width),this.domNode.setHeight(t.height),this.overflowGuardContainer.setWidth(t.width),this.overflowGuardContainer.setHeight(t.height),this.linesContent.setWidth(1e6),this.linesContent.setHeight(1e6)},t.prototype.getEditorClassName=function(){var e=this._textAreaHandler.isFocused()?" focused":"";return this._context.configuration.options.get(104)+" "+Object(A["d"])(this._context.theme.type)+e},t.prototype.onConfigurationChanged=function(e){return this.domNode.setClassName(this.getEditorClassName()),this._applyLayout(),!1},t.prototype.onContentSizeChanged=function(e){return this.outgoingEvents.emitContentSizeChange(e),!1},t.prototype.onFocusChanged=function(e){return this.domNode.setClassName(this.getEditorClassName()),this._context.model.setHasFocus(e.isFocused),e.isFocused?this.outgoingEvents.emitViewFocusGained():this.outgoingEvents.emitViewFocusLost(),!1},t.prototype.onScrollChanged=function(e){return this.outgoingEvents.emitScrollChanged(e),!1},t.prototype.onThemeChanged=function(e){return this.domNode.setClassName(this.getEditorClassName()),!1},t.prototype.dispose=function(){null!==this._renderAnimationFrame&&(this._renderAnimationFrame.dispose(),this._renderAnimationFrame=null),this.eventDispatcher.removeEventHandler(this),this.outgoingEvents.dispose(),this.viewLines.dispose();for(var t=0,n=this.viewParts.length;t<n;t++)this.viewParts[t].dispose();this.viewParts=[],e.prototype.dispose.call(this)},t.prototype._renderOnce=function(e){var t=yt(e);return this._scheduleRender(),t},t.prototype._scheduleRender=function(){null===this._renderAnimationFrame&&(this._renderAnimationFrame=o["U"](this._onRenderScheduled.bind(this),100))},t.prototype._onRenderScheduled=function(){this._renderAnimationFrame=null,this._flushAccumulatedAndRenderNow()},t.prototype._renderNow=function(){var e=this;yt((function(){return e._actualRender()}))},t.prototype._getViewPartsToRender=function(){for(var e=[],t=0,n=0,o=this.viewParts.length;n<o;n++){var i=this.viewParts[n];i.shouldRender()&&(e[t++]=i)}return e},t.prototype._actualRender=function(){if(o["M"](this.domNode.domNode)){var e=this._getViewPartsToRender();if(this.viewLines.shouldRender()||0!==e.length){var t=this._context.viewLayout.getLinesViewportData();this._context.model.setViewport(t.startLineNumber,t.endLineNumber,t.centeredLineNumber);var n=new gt["a"](this._cursor.getViewSelections(),t,this._context.viewLayout.getWhitespaceViewportData(),this._context.model);this.contentWidgets.shouldRender()&&this.contentWidgets.onBeforeRender(n),this.viewLines.shouldRender()&&(this.viewLines.renderText(n),this.viewLines.onDidRender(),e=this._getViewPartsToRender());for(var i=new F["d"](this._context.viewLayout,n,this.viewLines),r=0,s=e.length;r<s;r++){var a=e[r];a.prepareRender(i)}for(r=0,s=e.length;r<s;r++){a=e[r];a.render(i),a.onDidRender()}}}},t.prototype.delegateVerticalScrollbarMouseDown=function(e){this._scrollbar.delegateVerticalScrollbarMouseDown(e)},t.prototype.restoreState=function(e){this._context.viewLayout.setScrollPositionNow({scrollTop:e.scrollTop}),this._context.model.tokenizeViewport(),this._renderNow(),this.viewLines.updateLineWidths(),this._context.viewLayout.setScrollPositionNow({scrollLeft:e.scrollLeft})},t.prototype.getOffsetForColumn=function(e,t){var n=this._context.model.validateModelPosition({lineNumber:e,column:t}),o=this._context.model.coordinatesConverter.convertModelPositionToViewPosition(n);this._flushAccumulatedAndRenderNow();var i=this.viewLines.visibleRangeForPosition(new l["a"](o.lineNumber,o.column));return i?i.left:-1},t.prototype.getTargetAtClientPoint=function(e,t){var n=this.pointerHandler.getTargetAtClientPoint(e,t);return n?c["a"].convertViewToModelMouseTarget(n,this._context.model.coordinatesConverter):null},t.prototype.createOverviewRuler=function(e){return new Ze(this._context,e)},t.prototype.change=function(e){var t=this;return this._renderOnce((function(){var n=t.viewZones.changeViewZones(e);return n&&(t._context.viewLayout.onHeightMaybeChanged(),t._context.privateViewEventBus.emit(new ce["r"])),n}))},t.prototype.render=function(e,t){if(t){this.viewLines.forceShouldRender();for(var n=0,o=this.viewParts.length;n<o;n++){var i=this.viewParts[n];i.forceShouldRender()}}e?this._flushAccumulatedAndRenderNow():this._scheduleRender()},t.prototype.focus=function(){this._textAreaHandler.focusTextArea()},t.prototype.isFocused=function(){return this._textAreaHandler.isFocused()},t.prototype.setAriaOptions=function(e){this._textAreaHandler.setAriaOptions(e)},t.prototype.addContentWidget=function(e){this.contentWidgets.addWidget(e.widget),this.layoutContentWidget(e),this._scheduleRender()},t.prototype.layoutContentWidget=function(e){var t=e.position&&e.position.range||null;if(null===t){var n=e.position?e.position.position:null;null!==n&&(t=new P["a"](n.lineNumber,n.column,n.lineNumber,n.column))}var o=e.position?e.position.preference:null;this.contentWidgets.setWidgetPosition(e.widget,t,o),this._scheduleRender()},t.prototype.removeContentWidget=function(e){this.contentWidgets.removeWidget(e.widget),this._scheduleRender()},t.prototype.addOverlayWidget=function(e){this.overlayWidgets.addWidget(e.widget),this.layoutOverlayWidget(e),this._scheduleRender()},t.prototype.layoutOverlayWidget=function(e){var t=e.position?e.position.preference:null,n=this.overlayWidgets.setWidgetPosition(e.widget,t);n&&this._scheduleRender()},t.prototype.removeOverlayWidget=function(e){this.overlayWidgets.removeWidget(e.widget),this._scheduleRender()},t}(ze["a"]);function yt(e){try{return e()}catch(t){Object(r["e"])(t)}}},8478:function(e,t,n){},"930f":function(e,t,n){},"9e6f":function(e,t,n){"use strict";n.d(t,"a",(function(){return v}));var o=n("11f7"),i=n("a666"),r=n("3742"),s=n("6d8e"),a=n("308f"),h=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),l=function(e){function t(){var t=e.call(this)||this;return t._onCodeEditorAdd=t._register(new a["a"]),t.onCodeEditorAdd=t._onCodeEditorAdd.event,t._onCodeEditorRemove=t._register(new a["a"]),t.onCodeEditorRemove=t._onCodeEditorRemove.event,t._onDiffEditorAdd=t._register(new a["a"]),t._onDiffEditorRemove=t._register(new a["a"]),t._codeEditors=Object.create(null),t._diffEditors=Object.create(null),t}return h(t,e),t.prototype.addCodeEditor=function(e){this._codeEditors[e.getId()]=e,this._onCodeEditorAdd.fire(e)},t.prototype.removeCodeEditor=function(e){delete this._codeEditors[e.getId()]&&this._onCodeEditorRemove.fire(e)},t.prototype.listCodeEditors=function(){var e=this;return Object.keys(this._codeEditors).map((function(t){return e._codeEditors[t]}))},t.prototype.addDiffEditor=function(e){this._diffEditors[e.getId()]=e,this._onDiffEditorAdd.fire(e)},t.prototype.removeDiffEditor=function(e){delete this._diffEditors[e.getId()]&&this._onDiffEditorRemove.fire(e)},t.prototype.listDiffEditors=function(){var e=this;return Object.keys(this._diffEditors).map((function(t){return e._diffEditors[t]}))},t.prototype.getFocusedCodeEditor=function(){for(var e=null,t=this.listCodeEditors(),n=0,o=t;n<o.length;n++){var i=o[n];if(i.hasTextFocus())return i;i.hasWidgetFocus()&&(e=i)}return e},t}(i["a"]),u=n("8ae8"),d=n("3352"),c=n("b7d0"),p=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),f=function(e,t,n,o){var i,r=arguments.length,s=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)s=Reflect.decorate(e,t,n,o);else for(var a=e.length-1;a>=0;a--)(i=e[a])&&(s=(r<3?i(s):r>3?i(t,n,s):i(t,n))||s);return r>3&&s&&Object.defineProperty(t,n,s),s},g=function(e,t){return function(n,o){t(n,o,e)}},_=function(){function e(e,t,n){this._parent=e,this._editorId=t,this.styleSheet=n,this._refCount=0}return e.prototype.ref=function(){this._refCount++},e.prototype.unref=function(){var e;this._refCount--,0===this._refCount&&(null===(e=this.styleSheet.parentNode)||void 0===e||e.removeChild(this.styleSheet),this._parent._removeEditorStyleSheets(this._editorId))},e}(),m=function(){function e(e){this.styleSheet=e}return e.prototype.ref=function(){},e.prototype.unref=function(){},e}(),v=function(e){function t(t,n){void 0===n&&(n=null);var o=e.call(this)||this;return o._decorationOptionProviders=new Map,o._editorStyleSheets=new Map,o._globalStyleSheet=n?new m(n):null,o._themeService=t,o}return p(t,e),t.prototype._getOrCreateGlobalStyleSheet=function(){return this._globalStyleSheet||(this._globalStyleSheet=new m(o["w"]())),this._globalStyleSheet},t.prototype._getOrCreateStyleSheet=function(e){if(!e)return this._getOrCreateGlobalStyleSheet();var t=e.getContainerDomNode();if(!o["N"](t))return this._getOrCreateGlobalStyleSheet();var n=e.getId();if(!this._editorStyleSheets.has(n)){var i=new _(this,n,o["w"](t));this._editorStyleSheets.set(n,i)}return this._editorStyleSheets.get(n)},t.prototype._removeEditorStyleSheets=function(e){this._editorStyleSheets.delete(e)},t.prototype.registerDecorationType=function(e,t,n,o){var i=this._decorationOptionProviders.get(e);if(!i){var r=this._getOrCreateStyleSheet(o),s={styleSheet:r.styleSheet,key:e,parentTypeKey:n,options:t||Object.create(null)};i=n?new y(this._themeService,r,s):new C(this._themeService,r,s),this._decorationOptionProviders.set(e,i)}i.refCount++},t.prototype.removeDecorationType=function(e){var t=this._decorationOptionProviders.get(e);t&&(t.refCount--,t.refCount<=0&&(this._decorationOptionProviders.delete(e),t.dispose(),this.listCodeEditors().forEach((function(t){return t.removeDecorations(e)}))))},t.prototype.resolveDecorationOptions=function(e,t){var n=this._decorationOptionProviders.get(e);if(!n)throw new Error("Unknown decoration type key: "+e);return n.getOptions(this,t)},t=f([g(0,c["c"])],t),t}(l),y=function(){function e(e,t,n){this._styleSheet=t,this._styleSheet.ref(),this._parentTypeKey=n.parentTypeKey,this.refCount=0,this._beforeContentRules=new N(3,n,e),this._afterContentRules=new N(4,n,e)}return e.prototype.getOptions=function(e,t){var n=e.resolveDecorationOptions(this._parentTypeKey,!0);return this._beforeContentRules&&(n.beforeContentClassName=this._beforeContentRules.className),this._afterContentRules&&(n.afterContentClassName=this._afterContentRules.className),n},e.prototype.dispose=function(){this._beforeContentRules&&(this._beforeContentRules.dispose(),this._beforeContentRules=null),this._afterContentRules&&(this._afterContentRules.dispose(),this._afterContentRules=null),this._styleSheet.unref()},e}(),C=function(){function e(e,t,n){var o=this;this._disposables=new i["b"],this._styleSheet=t,this._styleSheet.ref(),this.refCount=0;var r=function(t){var i=new N(t,n,e);if(o._disposables.add(i),i.hasContent)return i.className},s=function(t){var i=new N(t,n,e);return o._disposables.add(i),i.hasContent?{className:i.className,hasLetterSpacing:i.hasLetterSpacing}:null};this.className=r(0);var a=s(1);a&&(this.inlineClassName=a.className,this.inlineClassNameAffectsLetterSpacing=a.hasLetterSpacing),this.beforeContentClassName=r(3),this.afterContentClassName=r(4),this.glyphMarginClassName=r(2);var h=n.options;this.isWholeLine=Boolean(h.isWholeLine),this.stickiness=h.rangeBehavior;var l=h.light&&h.light.overviewRulerColor||h.overviewRulerColor,u=h.dark&&h.dark.overviewRulerColor||h.overviewRulerColor;"undefined"===typeof l&&"undefined"===typeof u||(this.overviewRuler={color:l||u,darkColor:u||l,position:h.overviewRulerLane||d["d"].Center})}return e.prototype.getOptions=function(e,t){return t?{inlineClassName:this.inlineClassName,beforeContentClassName:this.beforeContentClassName,afterContentClassName:this.afterContentClassName,className:this.className,glyphMarginClassName:this.glyphMarginClassName,isWholeLine:this.isWholeLine,overviewRuler:this.overviewRuler,stickiness:this.stickiness}:this},e.prototype.dispose=function(){this._disposables.dispose(),this._styleSheet.unref()},e}(),b={color:"color:{0} !important;",opacity:"opacity:{0};",backgroundColor:"background-color:{0};",outline:"outline:{0};",outlineColor:"outline-color:{0};",outlineStyle:"outline-style:{0};",outlineWidth:"outline-width:{0};",border:"border:{0};",borderColor:"border-color:{0};",borderRadius:"border-radius:{0};",borderSpacing:"border-spacing:{0};",borderStyle:"border-style:{0};",borderWidth:"border-width:{0};",fontStyle:"font-style:{0};",fontWeight:"font-weight:{0};",textDecoration:"text-decoration:{0};",cursor:"cursor:{0};",letterSpacing:"letter-spacing:{0};",gutterIconPath:"background:{0} center center no-repeat;",gutterIconSize:"background-size:{0};",contentText:"content:'{0}';",contentIconPath:"content:{0};",margin:"margin:{0};",width:"width:{0};",height:"height:{0};"},N=function(){function e(e,t,n){var o=this;this._theme=n.getTheme(),this._ruleType=e,this._providerArgs=t,this._usesThemeColors=!1,this._hasContent=!1,this._hasLetterSpacing=!1;var i=w.getClassName(this._providerArgs.key,e);this._providerArgs.parentTypeKey&&(i=i+" "+w.getClassName(this._providerArgs.parentTypeKey,e)),this._className=i,this._unThemedSelector=w.getSelector(this._providerArgs.key,this._providerArgs.parentTypeKey,e),this._buildCSS(),this._usesThemeColors?this._themeListener=n.onThemeChange((function(e){o._theme=n.getTheme(),o._removeCSS(),o._buildCSS()})):this._themeListener=null}return e.prototype.dispose=function(){this._hasContent&&(this._removeCSS(),this._hasContent=!1),this._themeListener&&(this._themeListener.dispose(),this._themeListener=null)},Object.defineProperty(e.prototype,"hasContent",{get:function(){return this._hasContent},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"hasLetterSpacing",{get:function(){return this._hasLetterSpacing},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"className",{get:function(){return this._className},enumerable:!0,configurable:!0}),e.prototype._buildCSS=function(){var e,t,n,o=this._providerArgs.options;switch(this._ruleType){case 0:e=this.getCSSTextForModelDecorationClassName(o),t=this.getCSSTextForModelDecorationClassName(o.light),n=this.getCSSTextForModelDecorationClassName(o.dark);break;case 1:e=this.getCSSTextForModelDecorationInlineClassName(o),t=this.getCSSTextForModelDecorationInlineClassName(o.light),n=this.getCSSTextForModelDecorationInlineClassName(o.dark);break;case 2:e=this.getCSSTextForModelDecorationGlyphMarginClassName(o),t=this.getCSSTextForModelDecorationGlyphMarginClassName(o.light),n=this.getCSSTextForModelDecorationGlyphMarginClassName(o.dark);break;case 3:e=this.getCSSTextForModelDecorationContentClassName(o.before),t=this.getCSSTextForModelDecorationContentClassName(o.light&&o.light.before),n=this.getCSSTextForModelDecorationContentClassName(o.dark&&o.dark.before);break;case 4:e=this.getCSSTextForModelDecorationContentClassName(o.after),t=this.getCSSTextForModelDecorationContentClassName(o.light&&o.light.after),n=this.getCSSTextForModelDecorationContentClassName(o.dark&&o.dark.after);break;default:throw new Error("Unknown rule type: "+this._ruleType)}var i=this._providerArgs.styleSheet.sheet,r=!1;e.length>0&&(i.insertRule(this._unThemedSelector+" {"+e+"}",0),r=!0),t.length>0&&(i.insertRule(".vs"+this._unThemedSelector+" {"+t+"}",0),r=!0),n.length>0&&(i.insertRule(".vs-dark"+this._unThemedSelector+", .hc-black"+this._unThemedSelector+" {"+n+"}",0),r=!0),this._hasContent=r},e.prototype._removeCSS=function(){o["O"](this._unThemedSelector,this._providerArgs.styleSheet)},e.prototype.getCSSTextForModelDecorationClassName=function(e){if(!e)return"";var t=[];return this.collectCSSText(e,["backgroundColor"],t),this.collectCSSText(e,["outline","outlineColor","outlineStyle","outlineWidth"],t),this.collectBorderSettingsCSSText(e,t),t.join("")},e.prototype.getCSSTextForModelDecorationInlineClassName=function(e){if(!e)return"";var t=[];return this.collectCSSText(e,["fontStyle","fontWeight","textDecoration","cursor","color","opacity","letterSpacing"],t),e.letterSpacing&&(this._hasLetterSpacing=!0),t.join("")},e.prototype.getCSSTextForModelDecorationContentClassName=function(e){if(!e)return"";var t=[];if("undefined"!==typeof e){if(this.collectBorderSettingsCSSText(e,t),"undefined"!==typeof e.contentIconPath&&t.push(r["r"](b.contentIconPath,o["r"](s["a"].revive(e.contentIconPath)))),"string"===typeof e.contentText){var n=e.contentText.match(/^.*$/m)[0],i=n.replace(/['\\]/g,"\\$&");t.push(r["r"](b.contentText,i))}this.collectCSSText(e,["fontStyle","fontWeight","textDecoration","color","opacity","backgroundColor","margin"],t),this.collectCSSText(e,["width","height"],t)&&t.push("display:inline-block;")}return t.join("")},e.prototype.getCSSTextForModelDecorationGlyphMarginClassName=function(e){if(!e)return"";var t=[];return"undefined"!==typeof e.gutterIconPath&&(t.push(r["r"](b.gutterIconPath,o["r"](s["a"].revive(e.gutterIconPath)))),"undefined"!==typeof e.gutterIconSize&&t.push(r["r"](b.gutterIconSize,e.gutterIconSize))),t.join("")},e.prototype.collectBorderSettingsCSSText=function(e,t){return!!this.collectCSSText(e,["border","borderColor","borderRadius","borderSpacing","borderStyle","borderWidth"],t)&&(t.push(r["r"]("box-sizing: border-box;")),!0)},e.prototype.collectCSSText=function(e,t,n){for(var o=n.length,i=0,s=t;i<s.length;i++){var a=s[i],h=this.resolveValue(e[a]);"string"===typeof h&&n.push(r["r"](b[a],h))}return n.length!==o},e.prototype.resolveValue=function(e){if(Object(u["c"])(e)){this._usesThemeColors=!0;var t=this._theme.getColor(e.id);return t?t.toString():"transparent"}return e},e}(),w=function(){function e(){}return e.getClassName=function(e,t){return"ced-"+e+"-"+t},e.getSelector=function(e,t,n){var o=".monaco-editor ."+this.getClassName(e,n);return t&&(o=o+"."+this.getClassName(t,n)),3===n?o+="::before":4===n&&(o+="::after"),o},e}()},b055:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var o=n("8ae8");function i(e){return!(!e||"function"!==typeof e.getEditorType)&&e.getEditorType()===o["a"].ICodeEditor}},b160:function(e,t,n){"use strict";n.d(t,"a",(function(){return l}));n("2ab7");var o=n("30db"),i=n("e096"),r=n("7061"),s=n("918c"),a=n("b7d0"),h=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),l=function(e){function t(t){var n=e.call(this)||this;return n._context=t,n._readConfig(),n._lastCursorModelPosition=new r["a"](1,1),n._renderResult=null,n._context.addEventHandler(n),n}return h(t,e),t.prototype._readConfig=function(){var e=this._context.configuration.options;this._lineHeight=e.get(49);var t=e.get(50);this._renderLineNumbers=t.renderType,this._renderCustomLineNumbers=t.renderFn,this._renderFinalNewline=e.get(71);var n=e.get(107);this._lineNumbersLeft=n.lineNumbersLeft,this._lineNumbersWidth=n.lineNumbersWidth},t.prototype.dispose=function(){this._context.removeEventHandler(this),this._renderResult=null,e.prototype.dispose.call(this)},t.prototype.onConfigurationChanged=function(e){return this._readConfig(),!0},t.prototype.onCursorStateChanged=function(e){var t=e.selections[0].getPosition();return this._lastCursorModelPosition=this._context.model.coordinatesConverter.convertViewPositionToModelPosition(t),2===this._renderLineNumbers||3===this._renderLineNumbers},t.prototype.onFlushed=function(e){return!0},t.prototype.onLinesChanged=function(e){return!0},t.prototype.onLinesDeleted=function(e){return!0},t.prototype.onLinesInserted=function(e){return!0},t.prototype.onScrollChanged=function(e){return e.scrollTopChanged},t.prototype.onZonesChanged=function(e){return!0},t.prototype._getLineRenderLineNumber=function(e){var t=this._context.model.coordinatesConverter.convertViewPositionToModelPosition(new r["a"](e,1));if(1!==t.column)return"";var n=t.lineNumber;if(this._renderCustomLineNumbers)return this._renderCustomLineNumbers(n);if(2===this._renderLineNumbers){var o=Math.abs(this._lastCursorModelPosition.lineNumber-n);return 0===o?'<span class="relative-current-line-number">'+n+"</span>":String(o)}return 3===this._renderLineNumbers?this._lastCursorModelPosition.lineNumber===n||n%10===0?String(n):"":String(n)},t.prototype.prepareRender=function(e){if(0!==this._renderLineNumbers){for(var n=o["d"]?this._lineHeight%2===0?" lh-even":" lh-odd":"",i=e.visibleRange.startLineNumber,r=e.visibleRange.endLineNumber,s='<div class="'+t.CLASS_NAME+n+'" style="left:'+this._lineNumbersLeft.toString()+"px;width:"+this._lineNumbersWidth.toString()+'px;">',a=this._context.model.getLineCount(),h=[],l=i;l<=r;l++){var u=l-i;if(this._renderFinalNewline||l!==a||0!==this._context.model.getLineLength(l)){var d=this._getLineRenderLineNumber(l);h[u]=d?s+d+"</div>":""}else h[u]=""}this._renderResult=h}else this._renderResult=null},t.prototype.render=function(e,t){if(!this._renderResult)return"";var n=t-e;return n<0||n>=this._renderResult.length?"":this._renderResult[n]},t.CLASS_NAME="line-numbers",t}(i["a"]);Object(a["e"])((function(e,t){var n=e.getColor(s["k"]);n&&t.addRule(".monaco-editor .line-numbers { color: "+n+"; }");var o=e.getColor(s["b"]);o&&t.addRule(".monaco-editor .current-line ~ .line-numbers { color: "+o+"; }")}))},b2cc:function(e,t,n){"use strict";n.d(t,"a",(function(){return v})),n.d(t,"c",(function(){return y})),n.d(t,"b",(function(){return C})),n.d(t,"j",(function(){return b})),n.d(t,"e",(function(){return N})),n.d(t,"k",(function(){return w})),n.d(t,"l",(function(){return L})),n.d(t,"g",(function(){return S})),n.d(t,"f",(function(){return D})),n.d(t,"i",(function(){return R})),n.d(t,"h",(function(){return x})),n.d(t,"d",(function(){return o}));var o,i=n("fdcc"),r=n("6d8e"),s=n("5717"),a=n("7061"),h=n("1b69"),l=n("b78f"),u=n("7e32"),d=n("9e74"),c=n("4fc3"),p=n("9eb8"),f=n("89cd"),g=n("5d75"),_=n("ef8e"),m=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),v=function(){function e(e){this.id=e.id,this.precondition=e.precondition,this._kbOpts=e.kbOpts,this._menuOpts=e.menuOpts,this._description=e.description}return e.prototype.register=function(){var e=this;if(Array.isArray(this._menuOpts)?this._menuOpts.forEach(this._registerMenuItem,this):this._menuOpts&&this._registerMenuItem(this._menuOpts),this._kbOpts){var t=this._kbOpts.kbExpr;this.precondition&&(t=t?c["a"].and(t,this.precondition):this.precondition),p["a"].registerCommandAndKeybindingRule({id:this.id,handler:function(t,n){return e.runCommand(t,n)},weight:this._kbOpts.weight,when:t,primary:this._kbOpts.primary,secondary:this._kbOpts.secondary,win:this._kbOpts.win,linux:this._kbOpts.linux,mac:this._kbOpts.mac,description:this._description})}else d["a"].registerCommand({id:this.id,handler:function(t,n){return e.runCommand(t,n)},description:this._description})},e.prototype._registerMenuItem=function(e){u["c"].appendMenuItem(e.menuId,{group:e.group,command:{id:this.id,title:e.title},when:e.when,order:e.order})},e}(),y=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return m(t,e),t.bindToContribution=function(e){return function(t){function n(e){var n=t.call(this,e)||this;return n._callback=e.handler,n}return m(n,t),n.prototype.runEditorCommand=function(t,n,o){var i=e(n);i&&this._callback(e(n),o)},n}(t)},t.prototype.runCommand=function(e,t){var n=this,o=e.get(s["a"]),i=o.getFocusedCodeEditor()||o.getActiveCodeEditor();if(i)return i.invokeWithinContext((function(e){var o=e.get(c["c"]);if(o.contextMatchesRules(Object(_["n"])(n.precondition)))return n.runEditorCommand(e,i,t)}))},t}(v),C=function(e){function t(n){var o=e.call(this,t.convertOptions(n))||this;return o.label=n.label,o.alias=n.alias,o}return m(t,e),t.convertOptions=function(e){var t;function n(t){return t.menuId||(t.menuId=7),t.title||(t.title=e.label),t.when=c["a"].and(e.precondition,t.when),t}return t=Array.isArray(e.menuOpts)?e.menuOpts:e.menuOpts?[e.menuOpts]:[],Array.isArray(e.contextMenuOpts)?t.push.apply(t,e.contextMenuOpts.map(n)):e.contextMenuOpts&&t.push(n(e.contextMenuOpts)),e.menuOpts=t,e},t.prototype.runEditorCommand=function(e,t,n){return this.reportTelemetry(e,t),this.run(e,t,n||{})},t.prototype.reportTelemetry=function(e,t){e.get(g["a"]).publicLog2("editorActionInvoked",{name:this.label,id:this.id})},t}(y);function b(e,t){d["a"].registerCommand(e,(function(e,n){return t(e,n||{})}))}function N(e,t){b(e,(function(e,n){var o=n.resource,s=n.position;if(!(o instanceof r["a"]))throw Object(i["b"])("resource");if(!a["a"].isIPosition(s))throw Object(i["b"])("position");var u=e.get(h["a"]).getModel(o);if(u){var d=a["a"].lift(s);return t(u,d,n)}return e.get(l["a"]).createModelReference(o).then((function(e){return new Promise((function(o,i){try{var r=t(e.object.textEditorModel,a["a"].lift(s),n);o(r)}catch(h){i(h)}})).finally((function(){e.dispose()}))}))}))}function w(e,t){d["a"].registerCommand(e,(function(e){for(var n=[],o=1;o<arguments.length;o++)n[o-1]=arguments[o];var i=n[0],s=n[1];Object(_["a"])(r["a"].isUri(i)),Object(_["a"])(a["a"].isIPosition(s));var u=e.get(h["a"]).getModel(i);if(u){var d=a["a"].lift(s);return t(u,d,n.slice(2))}return e.get(l["a"]).createModelReference(i).then((function(e){return new Promise((function(o,i){try{var r=t(e.object.textEditorModel,a["a"].lift(s),n.slice(2));o(r)}catch(h){i(h)}})).finally((function(){e.dispose()}))}))}))}function L(e,t){d["a"].registerCommand(e,(function(e){for(var n=[],o=1;o<arguments.length;o++)n[o-1]=arguments[o];var i=n[0];Object(_["a"])(r["a"].isUri(i));var s=e.get(h["a"]).getModel(i);return s?t(s,n.slice(1)):e.get(l["a"]).createModelReference(i).then((function(e){return new Promise((function(o,i){try{var r=t(e.object.textEditorModel,n.slice(1));o(r)}catch(s){i(s)}})).finally((function(){e.dispose()}))}))}))}function S(e){return O.INSTANCE.registerEditorCommand(e),e}function D(e){O.INSTANCE.registerEditorAction(new e)}function R(e){O.INSTANCE.registerEditorAction(e)}function x(e,t){O.INSTANCE.registerEditorContribution(e,t)}(function(e){function t(e){return O.INSTANCE.getEditorCommand(e)}function n(){return O.INSTANCE.getEditorActions()}function o(){return O.INSTANCE.getEditorContributions()}function i(e){return O.INSTANCE.getEditorContributions().filter((function(t){return e.indexOf(t.id)>=0}))}function r(){return O.INSTANCE.getDiffEditorContributions()}e.getEditorCommand=t,e.getEditorActions=n,e.getEditorContributions=o,e.getSomeEditorContributions=i,e.getDiffEditorContributions=r})(o||(o={}));var M={EditorCommonContributions:"editor.contributions"},O=function(){function e(){this.editorContributions=[],this.diffEditorContributions=[],this.editorActions=[],this.editorCommands=Object.create(null)}return e.prototype.registerEditorContribution=function(e,t){this.editorContributions.push({id:e,ctor:t})},e.prototype.getEditorContributions=function(){return this.editorContributions.slice(0)},e.prototype.getDiffEditorContributions=function(){return this.diffEditorContributions.slice(0)},e.prototype.registerEditorAction=function(e){e.register(),this.editorActions.push(e)},e.prototype.getEditorActions=function(){return this.editorActions.slice(0)},e.prototype.registerEditorCommand=function(e){e.register(),this.editorCommands[e.id]=e},e.prototype.getEditorCommand=function(e){return this.editorCommands[e]||null},e.INSTANCE=new e,e}();f["a"].add(M.EditorCommonContributions,O.INSTANCE)},c7f5:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var o=n("0a0f"),i=Object(o["c"])("IWorkspaceEditService")},d8c3:function(e,t,n){},d93b:function(e,t,n){},e096:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var o=n("e2dc"),i=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),r=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t}(o["a"])},e8f2:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var o=n("04d3"),i=n("7ab3"),r=n("3742"),s=n("1ddc"),a=function(){function e(){}return e.create=function(){return new e},e.prototype.createLineBreaksComputer=function(e,t,n,o){t|=0,n=+n;var i=[];return{addRequest:function(e,t){i.push(e)},finalize:function(){return h(i,e,t,n,o)}}},e}();function h(e,t,n,a,h){if(-1===a){for(var d=[],c=0,p=e.length;c<p;c++)d[c]=null;return d}var f=Math.round(a*t.typicalHalfwidthCharacterWidth);2!==h&&3!==h||(h=1);var g=document.createElement("div");s["a"].applyFontInfoSlow(g,t);var _=Object(i["a"])(1e4),m=[],v=[],y=[],C=[],b=[];for(c=0;c<e.length;c++){var N=e[c],w=0,L=0,S=f;if(0!==h)if(w=r["q"](N),-1===w)w=0;else{for(var D=0;D<w;D++){var R=9===N.charCodeAt(D)?n-L%n:1;L+=R}var x=Math.ceil(t.spaceWidth*L);x+t.typicalFullwidthCharacterWidth>f?(w=0,L=0):S=f-x}var M=N.substr(w),O=l(M,L,n,S,_);m[c]=w,v[c]=L,y[c]=M,C[c]=O[0],b[c]=O[1]}g.innerHTML=_.build(),g.style.position="absolute",g.style.top="10000",g.style.wordWrap="break-word",document.body.appendChild(g);var A=document.createRange(),W=Array.prototype.slice.call(g.children,0),E=[];for(c=0;c<e.length;c++){var T=W[c],I=u(A,T,y[c],C[c]);if(null!==I){w=m[c],L=v[c];var H=b[c],P=[],F=0;for(p=I.length;F<p;F++)P[F]=H[I[F]];if(0!==w)for(F=0,p=I.length;F<p;F++)I[F]+=w;E[c]=new o["b"](I,P,L)}else E[c]=null}return document.body.removeChild(g),E}function l(e,t,n,o,i){i.appendASCIIString('<div style="width:'),i.appendASCIIString(String(o)),i.appendASCIIString('px;">');for(var s=e.length,a=t,h=0,l=[],u=[],d=0<s?e.charCodeAt(0):0,c=0;c<s;c++){l[c]=h,u[c]=a;var p=d;d=c+1<s?e.charCodeAt(c+1):0;var f=1,g=1;switch(p){case 9:f=n-a%n,g=f;for(var _=1;_<=f;_++)_<f?i.write1(160):i.appendASCII(32);break;case 32:32===d?i.write1(160):i.appendASCII(32);break;case 60:i.appendASCIIString("&lt;");break;case 62:i.appendASCIIString("&gt;");break;case 38:i.appendASCIIString("&amp;");break;case 0:i.appendASCIIString("&#00;");break;case 65279:case 8232:i.write1(65533);break;default:r["y"](p)&&g++,i.write1(p)}h+=f,a+=g}return l[e.length]=h,u[e.length]=a,i.appendASCIIString("</div>"),[l,u]}function u(e,t,n,o){if(n.length<=1)return null;var i=t.firstChild,r=[];return d(e,i,o,0,null,n.length-1,null,r),0===r.length?null:(r.push(n.length),r)}function d(e,t,n,o,i,r,s,a){if(o!==r&&(i=i||c(e,t,n[o],n[o+1]),s=s||c(e,t,n[r],n[r+1]),!(Math.abs(i[0].top-s[0].top)<=.1)))if(o+1!==r){var h=o+(r-o)/2|0,l=c(e,t,n[h],n[h+1]);d(e,t,n,o,i,h,l,a),d(e,t,n,h,l,r,s,a)}else a.push(r)}function c(e,t,n,o){return e.setStart(t,n),e.setEnd(t,o),e.getClientRects()}},ef37:function(e,t,n){},f20b:function(e,t,n){}}]);