(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-cc039926"],{"0cf7":function(t,e,n){"use strict";function i(t,e){return t}n.d(e,"a",(function(){return o}));var r=function(t){var e={};function n(i){if(e[i])return e[i].exports;var r=e[i]={i:i,l:!1,exports:{}};return t[i].call(r.exports,r,r.exports,n),r.l=!0,r.exports}return n.m=t,n.c=e,n.d=function(t,e,i){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:i})},n.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"===typeof t&&t&&t.__esModule)return t;var i=Object.create(null);if(n.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var r in t)n.d(i,r,function(e){return t[e]}.bind(null,r));return i},n.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s=15)}([,,function(t,e,n){function i(t){this.__parent=t,this.__character_count=0,this.__indent_count=-1,this.__alignment_count=0,this.__wrap_point_index=0,this.__wrap_point_character_count=0,this.__wrap_point_indent_count=-1,this.__wrap_point_alignment_count=0,this.__items=[]}function r(t,e){this.__cache=[""],this.__indent_size=t.indent_size,this.__indent_string=t.indent_char,t.indent_with_tabs||(this.__indent_string=new Array(t.indent_size+1).join(t.indent_char)),e=e||"",t.indent_level>0&&(e=new Array(t.indent_level+1).join(this.__indent_string)),this.__base_string=e,this.__base_string_length=e.length}function _(t,e){this.__indent_cache=new r(t,e),this.raw=!1,this._end_with_newline=t.end_with_newline,this.indent_size=t.indent_size,this.wrap_line_length=t.wrap_line_length,this.indent_empty_lines=t.indent_empty_lines,this.__lines=[],this.previous_line=null,this.current_line=null,this.next_line=new i(this),this.space_before_token=!1,this.non_breaking_space=!1,this.previous_token_wrapped=!1,this.__add_outputline()}i.prototype.clone_empty=function(){var t=new i(this.__parent);return t.set_indent(this.__indent_count,this.__alignment_count),t},i.prototype.item=function(t){return t<0?this.__items[this.__items.length+t]:this.__items[t]},i.prototype.has_match=function(t){for(var e=this.__items.length-1;e>=0;e--)if(this.__items[e].match(t))return!0;return!1},i.prototype.set_indent=function(t,e){this.is_empty()&&(this.__indent_count=t||0,this.__alignment_count=e||0,this.__character_count=this.__parent.get_indent_size(this.__indent_count,this.__alignment_count))},i.prototype._set_wrap_point=function(){this.__parent.wrap_line_length&&(this.__wrap_point_index=this.__items.length,this.__wrap_point_character_count=this.__character_count,this.__wrap_point_indent_count=this.__parent.next_line.__indent_count,this.__wrap_point_alignment_count=this.__parent.next_line.__alignment_count)},i.prototype._should_wrap=function(){return this.__wrap_point_index&&this.__character_count>this.__parent.wrap_line_length&&this.__wrap_point_character_count>this.__parent.next_line.__character_count},i.prototype._allow_wrap=function(){if(this._should_wrap()){this.__parent.add_new_line();var t=this.__parent.current_line;return t.set_indent(this.__wrap_point_indent_count,this.__wrap_point_alignment_count),t.__items=this.__items.slice(this.__wrap_point_index),this.__items=this.__items.slice(0,this.__wrap_point_index),t.__character_count+=this.__character_count-this.__wrap_point_character_count,this.__character_count=this.__wrap_point_character_count," "===t.__items[0]&&(t.__items.splice(0,1),t.__character_count-=1),!0}return!1},i.prototype.is_empty=function(){return 0===this.__items.length},i.prototype.last=function(){return this.is_empty()?null:this.__items[this.__items.length-1]},i.prototype.push=function(t){this.__items.push(t);var e=t.lastIndexOf("\n");-1!==e?this.__character_count=t.length-e:this.__character_count+=t.length},i.prototype.pop=function(){var t=null;return this.is_empty()||(t=this.__items.pop(),this.__character_count-=t.length),t},i.prototype._remove_indent=function(){this.__indent_count>0&&(this.__indent_count-=1,this.__character_count-=this.__parent.indent_size)},i.prototype._remove_wrap_indent=function(){this.__wrap_point_indent_count>0&&(this.__wrap_point_indent_count-=1)},i.prototype.trim=function(){while(" "===this.last())this.__items.pop(),this.__character_count-=1},i.prototype.toString=function(){var t="";return this.is_empty()?this.__parent.indent_empty_lines&&(t=this.__parent.get_indent_string(this.__indent_count)):(t=this.__parent.get_indent_string(this.__indent_count,this.__alignment_count),t+=this.__items.join("")),t},r.prototype.get_indent_size=function(t,e){var n=this.__base_string_length;return e=e||0,t<0&&(n=0),n+=t*this.__indent_size,n+=e,n},r.prototype.get_indent_string=function(t,e){var n=this.__base_string;return e=e||0,t<0&&(t=0,n=""),e+=t*this.__indent_size,this.__ensure_cache(e),n+=this.__cache[e],n},r.prototype.__ensure_cache=function(t){while(t>=this.__cache.length)this.__add_column()},r.prototype.__add_column=function(){var t=this.__cache.length,e=0,n="";this.__indent_size&&t>=this.__indent_size&&(e=Math.floor(t/this.__indent_size),t-=e*this.__indent_size,n=new Array(e+1).join(this.__indent_string)),t&&(n+=new Array(t+1).join(" ")),this.__cache.push(n)},_.prototype.__add_outputline=function(){this.previous_line=this.current_line,this.current_line=this.next_line.clone_empty(),this.__lines.push(this.current_line)},_.prototype.get_line_number=function(){return this.__lines.length},_.prototype.get_indent_string=function(t,e){return this.__indent_cache.get_indent_string(t,e)},_.prototype.get_indent_size=function(t,e){return this.__indent_cache.get_indent_size(t,e)},_.prototype.is_empty=function(){return!this.previous_line&&this.current_line.is_empty()},_.prototype.add_new_line=function(t){return!(this.is_empty()||!t&&this.just_added_newline())&&(this.raw||this.__add_outputline(),!0)},_.prototype.get_code=function(t){this.trim(!0);var e=this.current_line.pop();e&&("\n"===e[e.length-1]&&(e=e.replace(/\n+$/g,"")),this.current_line.push(e)),this._end_with_newline&&this.__add_outputline();var n=this.__lines.join("\n");return"\n"!==t&&(n=n.replace(/[\n]/g,t)),n},_.prototype.set_wrap_point=function(){this.current_line._set_wrap_point()},_.prototype.set_indent=function(t,e){return t=t||0,e=e||0,this.next_line.set_indent(t,e),this.__lines.length>1?(this.current_line.set_indent(t,e),!0):(this.current_line.set_indent(),!1)},_.prototype.add_raw_token=function(t){for(var e=0;e<t.newlines;e++)this.__add_outputline();this.current_line.set_indent(-1),this.current_line.push(t.whitespace_before),this.current_line.push(t.text),this.space_before_token=!1,this.non_breaking_space=!1,this.previous_token_wrapped=!1},_.prototype.add_token=function(t){this.__add_space_before_token(),this.current_line.push(t),this.space_before_token=!1,this.non_breaking_space=!1,this.previous_token_wrapped=this.current_line._allow_wrap()},_.prototype.__add_space_before_token=function(){this.space_before_token&&!this.just_added_newline()&&(this.non_breaking_space||this.set_wrap_point(),this.current_line.push(" "))},_.prototype.remove_indent=function(t){var e=this.__lines.length;while(t<e)this.__lines[t]._remove_indent(),t++;this.current_line._remove_wrap_indent()},_.prototype.trim=function(t){t=void 0!==t&&t,this.current_line.trim();while(t&&this.__lines.length>1&&this.current_line.is_empty())this.__lines.pop(),this.current_line=this.__lines[this.__lines.length-1],this.current_line.trim();this.previous_line=this.__lines.length>1?this.__lines[this.__lines.length-2]:null},_.prototype.just_added_newline=function(){return this.current_line.is_empty()},_.prototype.just_added_blankline=function(){return this.is_empty()||this.current_line.is_empty()&&this.previous_line.is_empty()},_.prototype.ensure_empty_line_above=function(t,e){var n=this.__lines.length-2;while(n>=0){var r=this.__lines[n];if(r.is_empty())break;if(0!==r.item(0).indexOf(t)&&r.item(-1)!==e){this.__lines.splice(n+1,0,new i(this)),this.previous_line=this.__lines[this.__lines.length-2];break}n--}},t.exports.Output=_},,,,function(t,e,n){function i(t,e){this.raw_options=r(t,e),this.disabled=this._get_boolean("disabled"),this.eol=this._get_characters("eol","auto"),this.end_with_newline=this._get_boolean("end_with_newline"),this.indent_size=this._get_number("indent_size",4),this.indent_char=this._get_characters("indent_char"," "),this.indent_level=this._get_number("indent_level"),this.preserve_newlines=this._get_boolean("preserve_newlines",!0),this.max_preserve_newlines=this._get_number("max_preserve_newlines",32786),this.preserve_newlines||(this.max_preserve_newlines=0),this.indent_with_tabs=this._get_boolean("indent_with_tabs","\t"===this.indent_char),this.indent_with_tabs&&(this.indent_char="\t",1===this.indent_size&&(this.indent_size=4)),this.wrap_line_length=this._get_number("wrap_line_length",this._get_number("max_char")),this.indent_empty_lines=this._get_boolean("indent_empty_lines"),this.templating=this._get_selection_list("templating",["auto","none","django","erb","handlebars","php"],["auto"])}function r(t,e){var n,i={};for(n in t=_(t),t)n!==e&&(i[n]=t[n]);if(e&&t[e])for(n in t[e])i[n]=t[e][n];return i}function _(t){var e,n={};for(e in t){var i=e.replace(/-/g,"_");n[i]=t[e]}return n}i.prototype._get_array=function(t,e){var n=this.raw_options[t],i=e||[];return"object"===typeof n?null!==n&&"function"===typeof n.concat&&(i=n.concat()):"string"===typeof n&&(i=n.split(/[^a-zA-Z0-9_\/\-]+/)),i},i.prototype._get_boolean=function(t,e){var n=this.raw_options[t],i=void 0===n?!!e:!!n;return i},i.prototype._get_characters=function(t,e){var n=this.raw_options[t],i=e||"";return"string"===typeof n&&(i=n.replace(/\\r/,"\r").replace(/\\n/,"\n").replace(/\\t/,"\t")),i},i.prototype._get_number=function(t,e){var n=this.raw_options[t];e=parseInt(e,10),isNaN(e)&&(e=0);var i=parseInt(n,10);return isNaN(i)&&(i=e),i},i.prototype._get_selection=function(t,e,n){var i=this._get_selection_list(t,e,n);if(1!==i.length)throw new Error("Invalid Option Value: The option '"+t+"' can only be one of the following values:\n"+e+"\nYou passed in: '"+this.raw_options[t]+"'");return i[0]},i.prototype._get_selection_list=function(t,e,n){if(!e||0===e.length)throw new Error("Selection list cannot be empty.");if(n=n||[e[0]],!this._is_valid_selection(n,e))throw new Error("Invalid Default Value!");var i=this._get_array(t,n);if(!this._is_valid_selection(i,e))throw new Error("Invalid Option Value: The option '"+t+"' can contain only the following values:\n"+e+"\nYou passed in: '"+this.raw_options[t]+"'");return i},i.prototype._is_valid_selection=function(t,e){return t.length&&e.length&&!t.some((function(t){return-1===e.indexOf(t)}))},t.exports.Options=i,t.exports.normalizeOpts=_,t.exports.mergeOpts=r},,function(t,e,n){var i=RegExp.prototype.hasOwnProperty("sticky");function r(t){this.__input=t||"",this.__input_length=this.__input.length,this.__position=0}r.prototype.restart=function(){this.__position=0},r.prototype.back=function(){this.__position>0&&(this.__position-=1)},r.prototype.hasNext=function(){return this.__position<this.__input_length},r.prototype.next=function(){var t=null;return this.hasNext()&&(t=this.__input.charAt(this.__position),this.__position+=1),t},r.prototype.peek=function(t){var e=null;return t=t||0,t+=this.__position,t>=0&&t<this.__input_length&&(e=this.__input.charAt(t)),e},r.prototype.__match=function(t,e){t.lastIndex=e;var n=t.exec(this.__input);return!n||i&&t.sticky||n.index!==e&&(n=null),n},r.prototype.test=function(t,e){return e=e||0,e+=this.__position,e>=0&&e<this.__input_length&&!!this.__match(t,e)},r.prototype.testChar=function(t,e){var n=this.peek(e);return t.lastIndex=0,null!==n&&t.test(n)},r.prototype.match=function(t){var e=this.__match(t,this.__position);return e?this.__position+=e[0].length:e=null,e},r.prototype.read=function(t,e,n){var i,r="";return t&&(i=this.match(t),i&&(r+=i[0])),!e||!i&&t||(r+=this.readUntil(e,n)),r},r.prototype.readUntil=function(t,e){var n="",i=this.__position;t.lastIndex=this.__position;var r=t.exec(this.__input);return r?(i=r.index,e&&(i+=r[0].length)):i=this.__input_length,n=this.__input.substring(this.__position,i),this.__position=i,n},r.prototype.readUntilAfter=function(t){return this.readUntil(t,!0)},r.prototype.get_regexp=function(t,e){var n=null,r="g";return e&&i&&(r="y"),"string"===typeof t&&""!==t?n=new RegExp(t,r):t&&(n=new RegExp(t.source,r)),n},r.prototype.get_literal_regexp=function(t){return RegExp(t.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&"))},r.prototype.peekUntilAfter=function(t){var e=this.__position,n=this.readUntilAfter(t);return this.__position=e,n},r.prototype.lookBack=function(t){var e=this.__position-1;return e>=t.length&&this.__input.substring(e-t.length,e).toLowerCase()===t},t.exports.InputScanner=r},,,,,function(t,e,n){function i(t,e){t="string"===typeof t?t:t.source,e="string"===typeof e?e:e.source,this.__directives_block_pattern=new RegExp(t+/ beautify( \w+[:]\w+)+ /.source+e,"g"),this.__directive_pattern=/ (\w+)[:](\w+)/g,this.__directives_end_ignore_pattern=new RegExp(t+/\sbeautify\signore:end\s/.source+e,"g")}i.prototype.get_directives=function(t){if(!t.match(this.__directives_block_pattern))return null;var e={};this.__directive_pattern.lastIndex=0;var n=this.__directive_pattern.exec(t);while(n)e[n[1]]=n[2],n=this.__directive_pattern.exec(t);return e},i.prototype.readIgnored=function(t){return t.readUntilAfter(this.__directives_end_ignore_pattern)},t.exports.Directives=i},,function(t,e,n){var i=n(16).Beautifier,r=n(17).Options;function _(t,e){var n=new i(t,e);return n.beautify()}t.exports=_,t.exports.defaultOptions=function(){return new r}},function(t,e,n){var i=n(17).Options,r=n(2).Output,_=n(8).InputScanner,s=n(13).Directives,o=new s(/\/\*/,/\*\//),a=/\r\n|[\r\n]/,h=/\r\n|[\r\n]/g,p=/\s/,u=/(?:\s|\n)+/g,c=/\/\*(?:[\s\S]*?)((?:\*\/)|$)/g,l=/\/\/(?:[^\n\r\u2028\u2029]*)/g;function d(t,e){this._source_text=t||"",this._options=new i(e),this._ch=null,this._input=null,this.NESTED_AT_RULE={"@page":!0,"@font-face":!0,"@keyframes":!0,"@media":!0,"@supports":!0,"@document":!0},this.CONDITIONAL_GROUP_RULE={"@media":!0,"@supports":!0,"@document":!0}}d.prototype.eatString=function(t){var e="";this._ch=this._input.next();while(this._ch){if(e+=this._ch,"\\"===this._ch)e+=this._input.next();else if(-1!==t.indexOf(this._ch)||"\n"===this._ch)break;this._ch=this._input.next()}return e},d.prototype.eatWhitespace=function(t){var e=p.test(this._input.peek()),n=!0;while(p.test(this._input.peek()))this._ch=this._input.next(),t&&"\n"===this._ch&&(this._options.preserve_newlines||n)&&(n=!1,this._output.add_new_line(!0));return e},d.prototype.foundNestedPseudoClass=function(){var t=0,e=1,n=this._input.peek(e);while(n){if("{"===n)return!0;if("("===n)t+=1;else if(")"===n){if(0===t)return!1;t-=1}else if(";"===n||"}"===n)return!1;e++,n=this._input.peek(e)}return!1},d.prototype.print_string=function(t){this._output.set_indent(this._indentLevel),this._output.non_breaking_space=!0,this._output.add_token(t)},d.prototype.preserveSingleSpace=function(t){t&&(this._output.space_before_token=!0)},d.prototype.indent=function(){this._indentLevel++},d.prototype.outdent=function(){this._indentLevel>0&&this._indentLevel--},d.prototype.beautify=function(){if(this._options.disabled)return this._source_text;var t=this._source_text,e=this._options.eol;"auto"===e&&(e="\n",t&&a.test(t||"")&&(e=t.match(a)[0])),t=t.replace(h,"\n");var n=t.match(/^[\t ]*/)[0];this._output=new r(this._options,n),this._input=new _(t),this._indentLevel=0,this._nestedLevel=0,this._ch=null;var i,s,d,f=0,g=!1,m=!1,y=!1,v=!1,w=!1,b=this._ch;while(1){if(i=this._input.read(u),s=""!==i,d=b,this._ch=this._input.next(),"\\"===this._ch&&this._input.hasNext()&&(this._ch+=this._input.next()),b=this._ch,!this._ch)break;if("/"===this._ch&&"*"===this._input.peek()){this._output.add_new_line(),this._input.back();var x=this._input.read(c),k=o.get_directives(x);k&&"start"===k.ignore&&(x+=o.readIgnored(this._input)),this.print_string(x),this.eatWhitespace(!0),this._output.add_new_line()}else if("/"===this._ch&&"/"===this._input.peek())this._output.space_before_token=!0,this._input.back(),this.print_string(this._input.read(l)),this.eatWhitespace(!0);else if("@"===this._ch)if(this.preserveSingleSpace(s),"{"===this._input.peek())this.print_string(this._ch+this.eatString("}"));else{this.print_string(this._ch);var E=this._input.peekUntilAfter(/[: ,;{}()[\]\/='"]/g);E.match(/[ :]$/)&&(E=this.eatString(": ").replace(/\s$/,""),this.print_string(E),this._output.space_before_token=!0),E=E.replace(/\s$/,""),"extend"===E?v=!0:"import"===E&&(w=!0),E in this.NESTED_AT_RULE?(this._nestedLevel+=1,E in this.CONDITIONAL_GROUP_RULE&&(y=!0)):g||0!==f||-1===E.indexOf(":")||(m=!0,this.indent())}else"#"===this._ch&&"{"===this._input.peek()?(this.preserveSingleSpace(s),this.print_string(this._ch+this.eatString("}"))):"{"===this._ch?(m&&(m=!1,this.outdent()),this.indent(),this._output.space_before_token=!0,this.print_string(this._ch),y?(y=!1,g=this._indentLevel>this._nestedLevel):g=this._indentLevel>=this._nestedLevel,this._options.newline_between_rules&&g&&this._output.previous_line&&"{"!==this._output.previous_line.item(-1)&&this._output.ensure_empty_line_above("/",","),this.eatWhitespace(!0),this._output.add_new_line()):"}"===this._ch?(this.outdent(),this._output.add_new_line(),"{"===d&&this._output.trim(!0),w=!1,v=!1,m&&(this.outdent(),m=!1),this.print_string(this._ch),g=!1,this._nestedLevel&&this._nestedLevel--,this.eatWhitespace(!0),this._output.add_new_line(),this._options.newline_between_rules&&!this._output.just_added_blankline()&&"}"!==this._input.peek()&&this._output.add_new_line(!0)):":"===this._ch?!g&&!y||this._input.lookBack("&")||this.foundNestedPseudoClass()||this._input.lookBack("(")||v||0!==f?(this._input.lookBack(" ")&&(this._output.space_before_token=!0),":"===this._input.peek()?(this._ch=this._input.next(),this.print_string("::")):this.print_string(":")):(this.print_string(":"),m||(m=!0,this._output.space_before_token=!0,this.eatWhitespace(!0),this.indent())):'"'===this._ch||"'"===this._ch?(this.preserveSingleSpace(s),this.print_string(this._ch+this.eatString(this._ch)),this.eatWhitespace(!0)):";"===this._ch?0===f?(m&&(this.outdent(),m=!1),v=!1,w=!1,this.print_string(this._ch),this.eatWhitespace(!0),"/"!==this._input.peek()&&this._output.add_new_line()):(this.print_string(this._ch),this.eatWhitespace(!0),this._output.space_before_token=!0):"("===this._ch?this._input.lookBack("url")?(this.print_string(this._ch),this.eatWhitespace(),f++,this.indent(),this._ch=this._input.next(),")"===this._ch||'"'===this._ch||"'"===this._ch?this._input.back():this._ch&&(this.print_string(this._ch+this.eatString(")")),f&&(f--,this.outdent()))):(this.preserveSingleSpace(s),this.print_string(this._ch),this.eatWhitespace(),f++,this.indent()):")"===this._ch?(f&&(f--,this.outdent()),this.print_string(this._ch)):","===this._ch?(this.print_string(this._ch),this.eatWhitespace(!0),!this._options.selector_separator_newline||m||0!==f||w?this._output.space_before_token=!0:this._output.add_new_line()):">"!==this._ch&&"+"!==this._ch&&"~"!==this._ch||m||0!==f?"]"===this._ch?this.print_string(this._ch):"["===this._ch?(this.preserveSingleSpace(s),this.print_string(this._ch)):"="===this._ch?(this.eatWhitespace(),this.print_string("="),p.test(this._ch)&&(this._ch="")):"!"!==this._ch||this._input.lookBack("\\")?(this.preserveSingleSpace(s),this.print_string(this._ch)):(this.print_string(" "),this.print_string(this._ch)):this._options.space_around_combinator?(this._output.space_before_token=!0,this.print_string(this._ch),this._output.space_before_token=!0):(this.print_string(this._ch),this.eatWhitespace(),this._ch&&p.test(this._ch)&&(this._ch=""))}var T=this._output.get_code(e);return T},t.exports.Beautifier=d},function(t,e,n){var i=n(6).Options;function r(t){i.call(this,t,"css"),this.selector_separator_newline=this._get_boolean("selector_separator_newline",!0),this.newline_between_rules=this._get_boolean("newline_between_rules",!0);var e=this._get_boolean("space_around_selector_separator");this.space_around_combinator=this._get_boolean("space_around_combinator")||e}r.prototype=new i,t.exports.Options=r}]),_=r,s=function(t){var e={};function n(i){if(e[i])return e[i].exports;var r=e[i]={i:i,l:!1,exports:{}};return t[i].call(r.exports,r,r.exports,n),r.l=!0,r.exports}return n.m=t,n.c=e,n.d=function(t,e,i){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:i})},n.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"===typeof t&&t&&t.__esModule)return t;var i=Object.create(null);if(n.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var r in t)n.d(i,r,function(e){return t[e]}.bind(null,r));return i},n.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s=18)}([,,function(t,e,n){function i(t){this.__parent=t,this.__character_count=0,this.__indent_count=-1,this.__alignment_count=0,this.__wrap_point_index=0,this.__wrap_point_character_count=0,this.__wrap_point_indent_count=-1,this.__wrap_point_alignment_count=0,this.__items=[]}function r(t,e){this.__cache=[""],this.__indent_size=t.indent_size,this.__indent_string=t.indent_char,t.indent_with_tabs||(this.__indent_string=new Array(t.indent_size+1).join(t.indent_char)),e=e||"",t.indent_level>0&&(e=new Array(t.indent_level+1).join(this.__indent_string)),this.__base_string=e,this.__base_string_length=e.length}function _(t,e){this.__indent_cache=new r(t,e),this.raw=!1,this._end_with_newline=t.end_with_newline,this.indent_size=t.indent_size,this.wrap_line_length=t.wrap_line_length,this.indent_empty_lines=t.indent_empty_lines,this.__lines=[],this.previous_line=null,this.current_line=null,this.next_line=new i(this),this.space_before_token=!1,this.non_breaking_space=!1,this.previous_token_wrapped=!1,this.__add_outputline()}i.prototype.clone_empty=function(){var t=new i(this.__parent);return t.set_indent(this.__indent_count,this.__alignment_count),t},i.prototype.item=function(t){return t<0?this.__items[this.__items.length+t]:this.__items[t]},i.prototype.has_match=function(t){for(var e=this.__items.length-1;e>=0;e--)if(this.__items[e].match(t))return!0;return!1},i.prototype.set_indent=function(t,e){this.is_empty()&&(this.__indent_count=t||0,this.__alignment_count=e||0,this.__character_count=this.__parent.get_indent_size(this.__indent_count,this.__alignment_count))},i.prototype._set_wrap_point=function(){this.__parent.wrap_line_length&&(this.__wrap_point_index=this.__items.length,this.__wrap_point_character_count=this.__character_count,this.__wrap_point_indent_count=this.__parent.next_line.__indent_count,this.__wrap_point_alignment_count=this.__parent.next_line.__alignment_count)},i.prototype._should_wrap=function(){return this.__wrap_point_index&&this.__character_count>this.__parent.wrap_line_length&&this.__wrap_point_character_count>this.__parent.next_line.__character_count},i.prototype._allow_wrap=function(){if(this._should_wrap()){this.__parent.add_new_line();var t=this.__parent.current_line;return t.set_indent(this.__wrap_point_indent_count,this.__wrap_point_alignment_count),t.__items=this.__items.slice(this.__wrap_point_index),this.__items=this.__items.slice(0,this.__wrap_point_index),t.__character_count+=this.__character_count-this.__wrap_point_character_count,this.__character_count=this.__wrap_point_character_count," "===t.__items[0]&&(t.__items.splice(0,1),t.__character_count-=1),!0}return!1},i.prototype.is_empty=function(){return 0===this.__items.length},i.prototype.last=function(){return this.is_empty()?null:this.__items[this.__items.length-1]},i.prototype.push=function(t){this.__items.push(t);var e=t.lastIndexOf("\n");-1!==e?this.__character_count=t.length-e:this.__character_count+=t.length},i.prototype.pop=function(){var t=null;return this.is_empty()||(t=this.__items.pop(),this.__character_count-=t.length),t},i.prototype._remove_indent=function(){this.__indent_count>0&&(this.__indent_count-=1,this.__character_count-=this.__parent.indent_size)},i.prototype._remove_wrap_indent=function(){this.__wrap_point_indent_count>0&&(this.__wrap_point_indent_count-=1)},i.prototype.trim=function(){while(" "===this.last())this.__items.pop(),this.__character_count-=1},i.prototype.toString=function(){var t="";return this.is_empty()?this.__parent.indent_empty_lines&&(t=this.__parent.get_indent_string(this.__indent_count)):(t=this.__parent.get_indent_string(this.__indent_count,this.__alignment_count),t+=this.__items.join("")),t},r.prototype.get_indent_size=function(t,e){var n=this.__base_string_length;return e=e||0,t<0&&(n=0),n+=t*this.__indent_size,n+=e,n},r.prototype.get_indent_string=function(t,e){var n=this.__base_string;return e=e||0,t<0&&(t=0,n=""),e+=t*this.__indent_size,this.__ensure_cache(e),n+=this.__cache[e],n},r.prototype.__ensure_cache=function(t){while(t>=this.__cache.length)this.__add_column()},r.prototype.__add_column=function(){var t=this.__cache.length,e=0,n="";this.__indent_size&&t>=this.__indent_size&&(e=Math.floor(t/this.__indent_size),t-=e*this.__indent_size,n=new Array(e+1).join(this.__indent_string)),t&&(n+=new Array(t+1).join(" ")),this.__cache.push(n)},_.prototype.__add_outputline=function(){this.previous_line=this.current_line,this.current_line=this.next_line.clone_empty(),this.__lines.push(this.current_line)},_.prototype.get_line_number=function(){return this.__lines.length},_.prototype.get_indent_string=function(t,e){return this.__indent_cache.get_indent_string(t,e)},_.prototype.get_indent_size=function(t,e){return this.__indent_cache.get_indent_size(t,e)},_.prototype.is_empty=function(){return!this.previous_line&&this.current_line.is_empty()},_.prototype.add_new_line=function(t){return!(this.is_empty()||!t&&this.just_added_newline())&&(this.raw||this.__add_outputline(),!0)},_.prototype.get_code=function(t){this.trim(!0);var e=this.current_line.pop();e&&("\n"===e[e.length-1]&&(e=e.replace(/\n+$/g,"")),this.current_line.push(e)),this._end_with_newline&&this.__add_outputline();var n=this.__lines.join("\n");return"\n"!==t&&(n=n.replace(/[\n]/g,t)),n},_.prototype.set_wrap_point=function(){this.current_line._set_wrap_point()},_.prototype.set_indent=function(t,e){return t=t||0,e=e||0,this.next_line.set_indent(t,e),this.__lines.length>1?(this.current_line.set_indent(t,e),!0):(this.current_line.set_indent(),!1)},_.prototype.add_raw_token=function(t){for(var e=0;e<t.newlines;e++)this.__add_outputline();this.current_line.set_indent(-1),this.current_line.push(t.whitespace_before),this.current_line.push(t.text),this.space_before_token=!1,this.non_breaking_space=!1,this.previous_token_wrapped=!1},_.prototype.add_token=function(t){this.__add_space_before_token(),this.current_line.push(t),this.space_before_token=!1,this.non_breaking_space=!1,this.previous_token_wrapped=this.current_line._allow_wrap()},_.prototype.__add_space_before_token=function(){this.space_before_token&&!this.just_added_newline()&&(this.non_breaking_space||this.set_wrap_point(),this.current_line.push(" "))},_.prototype.remove_indent=function(t){var e=this.__lines.length;while(t<e)this.__lines[t]._remove_indent(),t++;this.current_line._remove_wrap_indent()},_.prototype.trim=function(t){t=void 0!==t&&t,this.current_line.trim();while(t&&this.__lines.length>1&&this.current_line.is_empty())this.__lines.pop(),this.current_line=this.__lines[this.__lines.length-1],this.current_line.trim();this.previous_line=this.__lines.length>1?this.__lines[this.__lines.length-2]:null},_.prototype.just_added_newline=function(){return this.current_line.is_empty()},_.prototype.just_added_blankline=function(){return this.is_empty()||this.current_line.is_empty()&&this.previous_line.is_empty()},_.prototype.ensure_empty_line_above=function(t,e){var n=this.__lines.length-2;while(n>=0){var r=this.__lines[n];if(r.is_empty())break;if(0!==r.item(0).indexOf(t)&&r.item(-1)!==e){this.__lines.splice(n+1,0,new i(this)),this.previous_line=this.__lines[this.__lines.length-2];break}n--}},t.exports.Output=_},function(t,e,n){function i(t,e,n,i){this.type=t,this.text=e,this.comments_before=null,this.newlines=n||0,this.whitespace_before=i||"",this.parent=null,this.next=null,this.previous=null,this.opened=null,this.closed=null,this.directives=null}t.exports.Token=i},,,function(t,e,n){function i(t,e){this.raw_options=r(t,e),this.disabled=this._get_boolean("disabled"),this.eol=this._get_characters("eol","auto"),this.end_with_newline=this._get_boolean("end_with_newline"),this.indent_size=this._get_number("indent_size",4),this.indent_char=this._get_characters("indent_char"," "),this.indent_level=this._get_number("indent_level"),this.preserve_newlines=this._get_boolean("preserve_newlines",!0),this.max_preserve_newlines=this._get_number("max_preserve_newlines",32786),this.preserve_newlines||(this.max_preserve_newlines=0),this.indent_with_tabs=this._get_boolean("indent_with_tabs","\t"===this.indent_char),this.indent_with_tabs&&(this.indent_char="\t",1===this.indent_size&&(this.indent_size=4)),this.wrap_line_length=this._get_number("wrap_line_length",this._get_number("max_char")),this.indent_empty_lines=this._get_boolean("indent_empty_lines"),this.templating=this._get_selection_list("templating",["auto","none","django","erb","handlebars","php"],["auto"])}function r(t,e){var n,i={};for(n in t=_(t),t)n!==e&&(i[n]=t[n]);if(e&&t[e])for(n in t[e])i[n]=t[e][n];return i}function _(t){var e,n={};for(e in t){var i=e.replace(/-/g,"_");n[i]=t[e]}return n}i.prototype._get_array=function(t,e){var n=this.raw_options[t],i=e||[];return"object"===typeof n?null!==n&&"function"===typeof n.concat&&(i=n.concat()):"string"===typeof n&&(i=n.split(/[^a-zA-Z0-9_\/\-]+/)),i},i.prototype._get_boolean=function(t,e){var n=this.raw_options[t],i=void 0===n?!!e:!!n;return i},i.prototype._get_characters=function(t,e){var n=this.raw_options[t],i=e||"";return"string"===typeof n&&(i=n.replace(/\\r/,"\r").replace(/\\n/,"\n").replace(/\\t/,"\t")),i},i.prototype._get_number=function(t,e){var n=this.raw_options[t];e=parseInt(e,10),isNaN(e)&&(e=0);var i=parseInt(n,10);return isNaN(i)&&(i=e),i},i.prototype._get_selection=function(t,e,n){var i=this._get_selection_list(t,e,n);if(1!==i.length)throw new Error("Invalid Option Value: The option '"+t+"' can only be one of the following values:\n"+e+"\nYou passed in: '"+this.raw_options[t]+"'");return i[0]},i.prototype._get_selection_list=function(t,e,n){if(!e||0===e.length)throw new Error("Selection list cannot be empty.");if(n=n||[e[0]],!this._is_valid_selection(n,e))throw new Error("Invalid Default Value!");var i=this._get_array(t,n);if(!this._is_valid_selection(i,e))throw new Error("Invalid Option Value: The option '"+t+"' can contain only the following values:\n"+e+"\nYou passed in: '"+this.raw_options[t]+"'");return i},i.prototype._is_valid_selection=function(t,e){return t.length&&e.length&&!t.some((function(t){return-1===e.indexOf(t)}))},t.exports.Options=i,t.exports.normalizeOpts=_,t.exports.mergeOpts=r},,function(t,e,n){var i=RegExp.prototype.hasOwnProperty("sticky");function r(t){this.__input=t||"",this.__input_length=this.__input.length,this.__position=0}r.prototype.restart=function(){this.__position=0},r.prototype.back=function(){this.__position>0&&(this.__position-=1)},r.prototype.hasNext=function(){return this.__position<this.__input_length},r.prototype.next=function(){var t=null;return this.hasNext()&&(t=this.__input.charAt(this.__position),this.__position+=1),t},r.prototype.peek=function(t){var e=null;return t=t||0,t+=this.__position,t>=0&&t<this.__input_length&&(e=this.__input.charAt(t)),e},r.prototype.__match=function(t,e){t.lastIndex=e;var n=t.exec(this.__input);return!n||i&&t.sticky||n.index!==e&&(n=null),n},r.prototype.test=function(t,e){return e=e||0,e+=this.__position,e>=0&&e<this.__input_length&&!!this.__match(t,e)},r.prototype.testChar=function(t,e){var n=this.peek(e);return t.lastIndex=0,null!==n&&t.test(n)},r.prototype.match=function(t){var e=this.__match(t,this.__position);return e?this.__position+=e[0].length:e=null,e},r.prototype.read=function(t,e,n){var i,r="";return t&&(i=this.match(t),i&&(r+=i[0])),!e||!i&&t||(r+=this.readUntil(e,n)),r},r.prototype.readUntil=function(t,e){var n="",i=this.__position;t.lastIndex=this.__position;var r=t.exec(this.__input);return r?(i=r.index,e&&(i+=r[0].length)):i=this.__input_length,n=this.__input.substring(this.__position,i),this.__position=i,n},r.prototype.readUntilAfter=function(t){return this.readUntil(t,!0)},r.prototype.get_regexp=function(t,e){var n=null,r="g";return e&&i&&(r="y"),"string"===typeof t&&""!==t?n=new RegExp(t,r):t&&(n=new RegExp(t.source,r)),n},r.prototype.get_literal_regexp=function(t){return RegExp(t.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&"))},r.prototype.peekUntilAfter=function(t){var e=this.__position,n=this.readUntilAfter(t);return this.__position=e,n},r.prototype.lookBack=function(t){var e=this.__position-1;return e>=t.length&&this.__input.substring(e-t.length,e).toLowerCase()===t},t.exports.InputScanner=r},function(t,e,n){var i=n(8).InputScanner,r=n(3).Token,_=n(10).TokenStream,s=n(11).WhitespacePattern,o={START:"TK_START",RAW:"TK_RAW",EOF:"TK_EOF"},a=function(t,e){this._input=new i(t),this._options=e||{},this.__tokens=null,this._patterns={},this._patterns.whitespace=new s(this._input)};a.prototype.tokenize=function(){var t;this._input.restart(),this.__tokens=new _,this._reset();var e=new r(o.START,""),n=null,i=[],s=new _;while(e.type!==o.EOF){t=this._get_next_token(e,n);while(this._is_comment(t))s.add(t),t=this._get_next_token(e,n);s.isEmpty()||(t.comments_before=s,s=new _),t.parent=n,this._is_opening(t)?(i.push(n),n=t):n&&this._is_closing(t,n)&&(t.opened=n,n.closed=t,n=i.pop(),t.parent=n),t.previous=e,e.next=t,this.__tokens.add(t),e=t}return this.__tokens},a.prototype._is_first_token=function(){return this.__tokens.isEmpty()},a.prototype._reset=function(){},a.prototype._get_next_token=function(t,e){this._readWhitespace();var n=this._input.read(/.+/g);return n?this._create_token(o.RAW,n):this._create_token(o.EOF,"")},a.prototype._is_comment=function(t){return!1},a.prototype._is_opening=function(t){return!1},a.prototype._is_closing=function(t,e){return!1},a.prototype._create_token=function(t,e){var n=new r(t,e,this._patterns.whitespace.newline_count,this._patterns.whitespace.whitespace_before_token);return n},a.prototype._readWhitespace=function(){return this._patterns.whitespace.read()},t.exports.Tokenizer=a,t.exports.TOKEN=o},function(t,e,n){function i(t){this.__tokens=[],this.__tokens_length=this.__tokens.length,this.__position=0,this.__parent_token=t}i.prototype.restart=function(){this.__position=0},i.prototype.isEmpty=function(){return 0===this.__tokens_length},i.prototype.hasNext=function(){return this.__position<this.__tokens_length},i.prototype.next=function(){var t=null;return this.hasNext()&&(t=this.__tokens[this.__position],this.__position+=1),t},i.prototype.peek=function(t){var e=null;return t=t||0,t+=this.__position,t>=0&&t<this.__tokens_length&&(e=this.__tokens[t]),e},i.prototype.add=function(t){this.__parent_token&&(t.parent=this.__parent_token),this.__tokens.push(t),this.__tokens_length+=1},t.exports.TokenStream=i},function(t,e,n){var i=n(12).Pattern;function r(t,e){i.call(this,t,e),e?this._line_regexp=this._input.get_regexp(e._line_regexp):this.__set_whitespace_patterns("",""),this.newline_count=0,this.whitespace_before_token=""}r.prototype=new i,r.prototype.__set_whitespace_patterns=function(t,e){t+="\\t ",e+="\\n\\r",this._match_pattern=this._input.get_regexp("["+t+e+"]+",!0),this._newline_regexp=this._input.get_regexp("\\r\\n|["+e+"]")},r.prototype.read=function(){this.newline_count=0,this.whitespace_before_token="";var t=this._input.read(this._match_pattern);if(" "===t)this.whitespace_before_token=" ";else if(t){var e=this.__split(this._newline_regexp,t);this.newline_count=e.length-1,this.whitespace_before_token=e[this.newline_count]}return t},r.prototype.matching=function(t,e){var n=this._create();return n.__set_whitespace_patterns(t,e),n._update(),n},r.prototype._create=function(){return new r(this._input,this)},r.prototype.__split=function(t,e){t.lastIndex=0;var n=0,i=[],r=t.exec(e);while(r)i.push(e.substring(n,r.index)),n=r.index+r[0].length,r=t.exec(e);return n<e.length?i.push(e.substring(n,e.length)):i.push(""),i},t.exports.WhitespacePattern=r},function(t,e,n){function i(t,e){this._input=t,this._starting_pattern=null,this._match_pattern=null,this._until_pattern=null,this._until_after=!1,e&&(this._starting_pattern=this._input.get_regexp(e._starting_pattern,!0),this._match_pattern=this._input.get_regexp(e._match_pattern,!0),this._until_pattern=this._input.get_regexp(e._until_pattern),this._until_after=e._until_after)}i.prototype.read=function(){var t=this._input.read(this._starting_pattern);return this._starting_pattern&&!t||(t+=this._input.read(this._match_pattern,this._until_pattern,this._until_after)),t},i.prototype.read_match=function(){return this._input.match(this._match_pattern)},i.prototype.until_after=function(t){var e=this._create();return e._until_after=!0,e._until_pattern=this._input.get_regexp(t),e._update(),e},i.prototype.until=function(t){var e=this._create();return e._until_after=!1,e._until_pattern=this._input.get_regexp(t),e._update(),e},i.prototype.starting_with=function(t){var e=this._create();return e._starting_pattern=this._input.get_regexp(t,!0),e._update(),e},i.prototype.matching=function(t){var e=this._create();return e._match_pattern=this._input.get_regexp(t,!0),e._update(),e},i.prototype._create=function(){return new i(this._input,this)},i.prototype._update=function(){},t.exports.Pattern=i},function(t,e,n){function i(t,e){t="string"===typeof t?t:t.source,e="string"===typeof e?e:e.source,this.__directives_block_pattern=new RegExp(t+/ beautify( \w+[:]\w+)+ /.source+e,"g"),this.__directive_pattern=/ (\w+)[:](\w+)/g,this.__directives_end_ignore_pattern=new RegExp(t+/\sbeautify\signore:end\s/.source+e,"g")}i.prototype.get_directives=function(t){if(!t.match(this.__directives_block_pattern))return null;var e={};this.__directive_pattern.lastIndex=0;var n=this.__directive_pattern.exec(t);while(n)e[n[1]]=n[2],n=this.__directive_pattern.exec(t);return e},i.prototype.readIgnored=function(t){return t.readUntilAfter(this.__directives_end_ignore_pattern)},t.exports.Directives=i},function(t,e,n){var i=n(12).Pattern,r={django:!1,erb:!1,handlebars:!1,php:!1};function _(t,e){i.call(this,t,e),this.__template_pattern=null,this._disabled=Object.assign({},r),this._excluded=Object.assign({},r),e&&(this.__template_pattern=this._input.get_regexp(e.__template_pattern),this._excluded=Object.assign(this._excluded,e._excluded),this._disabled=Object.assign(this._disabled,e._disabled));var n=new i(t);this.__patterns={handlebars_comment:n.starting_with(/{{!--/).until_after(/--}}/),handlebars_unescaped:n.starting_with(/{{{/).until_after(/}}}/),handlebars:n.starting_with(/{{/).until_after(/}}/),php:n.starting_with(/<\?(?:[=]|php)/).until_after(/\?>/),erb:n.starting_with(/<%[^%]/).until_after(/[^%]%>/),django:n.starting_with(/{%/).until_after(/%}/),django_value:n.starting_with(/{{/).until_after(/}}/),django_comment:n.starting_with(/{#/).until_after(/#}/)}}_.prototype=new i,_.prototype._create=function(){return new _(this._input,this)},_.prototype._update=function(){this.__set_templated_pattern()},_.prototype.disable=function(t){var e=this._create();return e._disabled[t]=!0,e._update(),e},_.prototype.read_options=function(t){var e=this._create();for(var n in r)e._disabled[n]=-1===t.templating.indexOf(n);return e._update(),e},_.prototype.exclude=function(t){var e=this._create();return e._excluded[t]=!0,e._update(),e},_.prototype.read=function(){var t="";t=this._match_pattern?this._input.read(this._starting_pattern):this._input.read(this._starting_pattern,this.__template_pattern);var e=this._read_template();while(e)this._match_pattern?e+=this._input.read(this._match_pattern):e+=this._input.readUntil(this.__template_pattern),t+=e,e=this._read_template();return this._until_after&&(t+=this._input.readUntilAfter(this._until_pattern)),t},_.prototype.__set_templated_pattern=function(){var t=[];this._disabled.php||t.push(this.__patterns.php._starting_pattern.source),this._disabled.handlebars||t.push(this.__patterns.handlebars._starting_pattern.source),this._disabled.erb||t.push(this.__patterns.erb._starting_pattern.source),this._disabled.django||(t.push(this.__patterns.django._starting_pattern.source),t.push(this.__patterns.django_value._starting_pattern.source),t.push(this.__patterns.django_comment._starting_pattern.source)),this._until_pattern&&t.push(this._until_pattern.source),this.__template_pattern=this._input.get_regexp("(?:"+t.join("|")+")")},_.prototype._read_template=function(){var t="",e=this._input.peek();if("<"===e){var n=this._input.peek(1);this._disabled.php||this._excluded.php||"?"!==n||(t=t||this.__patterns.php.read()),this._disabled.erb||this._excluded.erb||"%"!==n||(t=t||this.__patterns.erb.read())}else"{"===e&&(this._disabled.handlebars||this._excluded.handlebars||(t=t||this.__patterns.handlebars_comment.read(),t=t||this.__patterns.handlebars_unescaped.read(),t=t||this.__patterns.handlebars.read()),this._disabled.django||(this._excluded.django||this._excluded.handlebars||(t=t||this.__patterns.django_value.read()),this._excluded.django||(t=t||this.__patterns.django_comment.read(),t=t||this.__patterns.django.read())));return t},t.exports.TemplatablePattern=_},,,,function(t,e,n){var i=n(19).Beautifier,r=n(20).Options;function _(t,e,n,r){var _=new i(t,e,n,r);return _.beautify()}t.exports=_,t.exports.defaultOptions=function(){return new r}},function(t,e,n){var i=n(20).Options,r=n(2).Output,_=n(21).Tokenizer,s=n(21).TOKEN,o=/\r\n|[\r\n]/,a=/\r\n|[\r\n]/g,h=function(t,e){this.indent_level=0,this.alignment_size=0,this.max_preserve_newlines=t.max_preserve_newlines,this.preserve_newlines=t.preserve_newlines,this._output=new r(t,e)};h.prototype.current_line_has_match=function(t){return this._output.current_line.has_match(t)},h.prototype.set_space_before_token=function(t,e){this._output.space_before_token=t,this._output.non_breaking_space=e},h.prototype.set_wrap_point=function(){this._output.set_indent(this.indent_level,this.alignment_size),this._output.set_wrap_point()},h.prototype.add_raw_token=function(t){this._output.add_raw_token(t)},h.prototype.print_preserved_newlines=function(t){var e=0;t.type!==s.TEXT&&t.previous.type!==s.TEXT&&(e=t.newlines?1:0),this.preserve_newlines&&(e=t.newlines<this.max_preserve_newlines+1?t.newlines:this.max_preserve_newlines+1);for(var n=0;n<e;n++)this.print_newline(n>0);return 0!==e},h.prototype.traverse_whitespace=function(t){return!(!t.whitespace_before&&!t.newlines)&&(this.print_preserved_newlines(t)||(this._output.space_before_token=!0),!0)},h.prototype.previous_token_wrapped=function(){return this._output.previous_token_wrapped},h.prototype.print_newline=function(t){this._output.add_new_line(t)},h.prototype.print_token=function(t){t.text&&(this._output.set_indent(this.indent_level,this.alignment_size),this._output.add_token(t.text))},h.prototype.indent=function(){this.indent_level++},h.prototype.get_full_indent=function(t){return t=this.indent_level+(t||0),t<1?"":this._output.get_indent_string(t)};var p=function(t){var e=null,n=t.next;while(n.type!==s.EOF&&t.closed!==n){if(n.type===s.ATTRIBUTE&&"type"===n.text){n.next&&n.next.type===s.EQUALS&&n.next.next&&n.next.next.type===s.VALUE&&(e=n.next.next.text);break}n=n.next}return e},u=function(t,e){var n=null,i=null;return e.closed?("script"===t?n="text/javascript":"style"===t&&(n="text/css"),n=p(e)||n,n.search("text/css")>-1?i="css":n.search(/(text|application|dojo)\/(x-)?(javascript|ecmascript|jscript|livescript|(ld\+)?json|method|aspect)/)>-1?i="javascript":n.search(/(text|application|dojo)\/(x-)?(html)/)>-1?i="html":n.search(/test\/null/)>-1&&(i="null"),i):null};function c(t,e){return-1!==e.indexOf(t)}function l(t,e,n){this.parent=t||null,this.tag=e?e.tag_name:"",this.indent_level=n||0,this.parser_token=e||null}function d(t){this._printer=t,this._current_frame=null}function f(t,e,n,r){this._source_text=t||"",e=e||{},this._js_beautify=n,this._css_beautify=r,this._tag_stack=null;var _=new i(e,"html");this._options=_,this._is_wrap_attributes_force="force"===this._options.wrap_attributes.substr(0,"force".length),this._is_wrap_attributes_force_expand_multiline="force-expand-multiline"===this._options.wrap_attributes,this._is_wrap_attributes_force_aligned="force-aligned"===this._options.wrap_attributes,this._is_wrap_attributes_aligned_multiple="aligned-multiple"===this._options.wrap_attributes,this._is_wrap_attributes_preserve="preserve"===this._options.wrap_attributes.substr(0,"preserve".length),this._is_wrap_attributes_preserve_aligned="preserve-aligned"===this._options.wrap_attributes}d.prototype.get_parser_token=function(){return this._current_frame?this._current_frame.parser_token:null},d.prototype.record_tag=function(t){var e=new l(this._current_frame,t,this._printer.indent_level);this._current_frame=e},d.prototype._try_pop_frame=function(t){var e=null;return t&&(e=t.parser_token,this._printer.indent_level=t.indent_level,this._current_frame=t.parent),e},d.prototype._get_frame=function(t,e){var n=this._current_frame;while(n){if(-1!==t.indexOf(n.tag))break;if(e&&-1!==e.indexOf(n.tag)){n=null;break}n=n.parent}return n},d.prototype.try_pop=function(t,e){var n=this._get_frame([t],e);return this._try_pop_frame(n)},d.prototype.indent_to_tag=function(t){var e=this._get_frame(t);e&&(this._printer.indent_level=e.indent_level)},f.prototype.beautify=function(){if(this._options.disabled)return this._source_text;var t=this._source_text,e=this._options.eol;"auto"===this._options.eol&&(e="\n",t&&o.test(t)&&(e=t.match(o)[0])),t=t.replace(a,"\n");var n=t.match(/^[\t ]*/)[0],i={text:"",type:""},r=new g,p=new h(this._options,n),u=new _(t,this._options).tokenize();this._tag_stack=new d(p);var c=null,l=u.next();while(l.type!==s.EOF)l.type===s.TAG_OPEN||l.type===s.COMMENT?(c=this._handle_tag_open(p,l,r,i),r=c):l.type===s.ATTRIBUTE||l.type===s.EQUALS||l.type===s.VALUE||l.type===s.TEXT&&!r.tag_complete?c=this._handle_inside_tag(p,l,r,u):l.type===s.TAG_CLOSE?c=this._handle_tag_close(p,l,r):l.type===s.TEXT?c=this._handle_text(p,l,r):p.add_raw_token(l),i=c,l=u.next();var f=p._output.get_code(e);return f},f.prototype._handle_tag_close=function(t,e,n){var i={text:e.text,type:e.type};return t.alignment_size=0,n.tag_complete=!0,t.set_space_before_token(e.newlines||""!==e.whitespace_before,!0),n.is_unformatted?t.add_raw_token(e):("<"===n.tag_start_char&&(t.set_space_before_token("/"===e.text[0],!0),this._is_wrap_attributes_force_expand_multiline&&n.has_wrapped_attrs&&t.print_newline(!1)),t.print_token(e)),!n.indent_content||n.is_unformatted||n.is_content_unformatted||(t.indent(),n.indent_content=!1),n.is_inline_element||n.is_unformatted||n.is_content_unformatted||t.set_wrap_point(),i},f.prototype._handle_inside_tag=function(t,e,n,i){var r=n.has_wrapped_attrs,_={text:e.text,type:e.type};if(t.set_space_before_token(e.newlines||""!==e.whitespace_before,!0),n.is_unformatted)t.add_raw_token(e);else if("{"===n.tag_start_char&&e.type===s.TEXT)t.print_preserved_newlines(e)?(e.newlines=0,t.add_raw_token(e)):t.print_token(e);else{if(e.type===s.ATTRIBUTE?(t.set_space_before_token(!0),n.attr_count+=1):(e.type===s.EQUALS||e.type===s.VALUE&&e.previous.type===s.EQUALS)&&t.set_space_before_token(!1),e.type===s.ATTRIBUTE&&"<"===n.tag_start_char&&((this._is_wrap_attributes_preserve||this._is_wrap_attributes_preserve_aligned)&&(t.traverse_whitespace(e),r=r||0!==e.newlines),this._is_wrap_attributes_force)){var o=n.attr_count>1;if(this._is_wrap_attributes_force_expand_multiline&&1===n.attr_count){var a,h=!0,p=0;do{if(a=i.peek(p),a.type===s.ATTRIBUTE){h=!1;break}p+=1}while(p<4&&a.type!==s.EOF&&a.type!==s.TAG_CLOSE);o=!h}o&&(t.print_newline(!1),r=!0)}t.print_token(e),r=r||t.previous_token_wrapped(),n.has_wrapped_attrs=r}return _},f.prototype._handle_text=function(t,e,n){var i={text:e.text,type:"TK_CONTENT"};return n.custom_beautifier_name?this._print_custom_beatifier_text(t,e,n):n.is_unformatted||n.is_content_unformatted?t.add_raw_token(e):(t.traverse_whitespace(e),t.print_token(e)),i},f.prototype._print_custom_beatifier_text=function(t,e,n){var i=this;if(""!==e.text){var r,_=e.text,s=1,o="",a="";"javascript"===n.custom_beautifier_name&&"function"===typeof this._js_beautify?r=this._js_beautify:"css"===n.custom_beautifier_name&&"function"===typeof this._css_beautify?r=this._css_beautify:"html"===n.custom_beautifier_name&&(r=function(t,e){var n=new f(t,e,i._js_beautify,i._css_beautify);return n.beautify()}),"keep"===this._options.indent_scripts?s=0:"separate"===this._options.indent_scripts&&(s=-t.indent_level);var h=t.get_full_indent(s);if(_=_.replace(/\n[ \t]*$/,""),"html"!==n.custom_beautifier_name&&"<"===_[0]&&_.match(/^(<!--|<!\[CDATA\[)/)){var p=/^(<!--[^\n]*|<!\[CDATA\[)(\n?)([ \t\n]*)([\s\S]*)(-->|]]>)$/.exec(_);if(!p)return void t.add_raw_token(e);o=h+p[1]+"\n",_=p[4],p[5]&&(a=h+p[5]),_=_.replace(/\n[ \t]*$/,""),(p[2]||-1!==p[3].indexOf("\n"))&&(p=p[3].match(/[ \t]+$/),p&&(e.whitespace_before=p[0]))}if(_)if(r){var u=function(){this.eol="\n"};u.prototype=this._options.raw_options;var c=new u;_=r(h+_,c)}else{var l=e.whitespace_before;l&&(_=_.replace(new RegExp("\n("+l+")?","g"),"\n")),_=h+_.replace(/\n/g,"\n"+h)}o&&(_=_?o+_+"\n"+a:o+a),t.print_newline(!1),_&&(e.text=_,e.whitespace_before="",e.newlines=0,t.add_raw_token(e),t.print_newline(!0))}},f.prototype._handle_tag_open=function(t,e,n,i){var r=this._get_tag_open_token(e);return(n.is_unformatted||n.is_content_unformatted)&&e.type===s.TAG_OPEN&&0===e.text.indexOf("</")?t.add_raw_token(e):(t.traverse_whitespace(e),this._set_tag_position(t,e,r,n,i),r.is_inline_element||t.set_wrap_point(),t.print_token(e)),(this._is_wrap_attributes_force_aligned||this._is_wrap_attributes_aligned_multiple||this._is_wrap_attributes_preserve_aligned)&&(r.alignment_size=e.text.length+1),r.tag_complete||r.is_unformatted||(t.alignment_size=r.alignment_size),r};var g=function(t,e){var n;(this.parent=t||null,this.text="",this.type="TK_TAG_OPEN",this.tag_name="",this.is_inline_element=!1,this.is_unformatted=!1,this.is_content_unformatted=!1,this.is_empty_element=!1,this.is_start_tag=!1,this.is_end_tag=!1,this.indent_content=!1,this.multiline_content=!1,this.custom_beautifier_name=null,this.start_tag_token=null,this.attr_count=0,this.has_wrapped_attrs=!1,this.alignment_size=0,this.tag_complete=!1,this.tag_start_char="",this.tag_check="",e)?(this.tag_start_char=e.text[0],this.text=e.text,"<"===this.tag_start_char?(n=e.text.match(/^<([^\s>]*)/),this.tag_check=n?n[1]:""):(n=e.text.match(/^{{[#\^]?([^\s}]+)/),this.tag_check=n?n[1]:""),this.tag_check=this.tag_check.toLowerCase(),e.type===s.COMMENT&&(this.tag_complete=!0),this.is_start_tag="/"!==this.tag_check.charAt(0),this.tag_name=this.is_start_tag?this.tag_check:this.tag_check.substr(1),this.is_end_tag=!this.is_start_tag||e.closed&&"/>"===e.closed.text,this.is_end_tag=this.is_end_tag||"{"===this.tag_start_char&&(this.text.length<3||/[^#\^]/.test(this.text.charAt(2)))):this.tag_complete=!0};f.prototype._get_tag_open_token=function(t){var e=new g(this._tag_stack.get_parser_token(),t);return e.alignment_size=this._options.wrap_attributes_indent_size,e.is_end_tag=e.is_end_tag||c(e.tag_check,this._options.void_elements),e.is_empty_element=e.tag_complete||e.is_start_tag&&e.is_end_tag,e.is_unformatted=!e.tag_complete&&c(e.tag_check,this._options.unformatted),e.is_content_unformatted=!e.is_empty_element&&c(e.tag_check,this._options.content_unformatted),e.is_inline_element=c(e.tag_name,this._options.inline)||"{"===e.tag_start_char,e},f.prototype._set_tag_position=function(t,e,n,i,r){if(n.is_empty_element||(n.is_end_tag?n.start_tag_token=this._tag_stack.try_pop(n.tag_name):(this._do_optional_end_element(n)&&(n.is_inline_element||(n.parent&&(n.parent.multiline_content=!0),t.print_newline(!1))),this._tag_stack.record_tag(n),"script"!==n.tag_name&&"style"!==n.tag_name||n.is_unformatted||n.is_content_unformatted||(n.custom_beautifier_name=u(n.tag_check,e)))),c(n.tag_check,this._options.extra_liners)&&(t.print_newline(!1),t._output.just_added_blankline()||t.print_newline(!0)),n.is_empty_element){if("{"===n.tag_start_char&&"else"===n.tag_check){this._tag_stack.indent_to_tag(["if","unless","each"]),n.indent_content=!0;var _=t.current_line_has_match(/{{#if/);_||t.print_newline(!1)}"!--"===n.tag_name&&r.type===s.TAG_CLOSE&&i.is_end_tag&&-1===n.text.indexOf("\n")||n.is_inline_element||n.is_unformatted||t.print_newline(!1)}else n.is_unformatted||n.is_content_unformatted?n.is_inline_element||n.is_unformatted||t.print_newline(!1):n.is_end_tag?(n.start_tag_token&&n.start_tag_token.multiline_content||!(n.is_inline_element||i.is_inline_element||r.type===s.TAG_CLOSE&&n.start_tag_token===i||"TK_CONTENT"===r.type))&&t.print_newline(!1):(n.indent_content=!n.custom_beautifier_name,"<"===n.tag_start_char&&("html"===n.tag_name?n.indent_content=this._options.indent_inner_html:"head"===n.tag_name?n.indent_content=this._options.indent_head_inner_html:"body"===n.tag_name&&(n.indent_content=this._options.indent_body_inner_html)),n.is_inline_element||"TK_CONTENT"===r.type||(n.parent&&(n.parent.multiline_content=!0),t.print_newline(!1)))},f.prototype._do_optional_end_element=function(t){var e=null;if(!t.is_empty_element&&t.is_start_tag&&t.parent)return"body"===t.tag_name?e=e||this._tag_stack.try_pop("head"):"li"===t.tag_name?e=e||this._tag_stack.try_pop("li",["ol","ul"]):"dd"===t.tag_name||"dt"===t.tag_name?(e=e||this._tag_stack.try_pop("dt",["dl"]),e=e||this._tag_stack.try_pop("dd",["dl"])):"rp"===t.tag_name||"rt"===t.tag_name?(e=e||this._tag_stack.try_pop("rt",["ruby","rtc"]),e=e||this._tag_stack.try_pop("rp",["ruby","rtc"])):"optgroup"===t.tag_name?e=e||this._tag_stack.try_pop("optgroup",["select"]):"option"===t.tag_name?e=e||this._tag_stack.try_pop("option",["select","datalist","optgroup"]):"colgroup"===t.tag_name?e=e||this._tag_stack.try_pop("caption",["table"]):"thead"===t.tag_name?(e=e||this._tag_stack.try_pop("caption",["table"]),e=e||this._tag_stack.try_pop("colgroup",["table"])):"tbody"===t.tag_name||"tfoot"===t.tag_name?(e=e||this._tag_stack.try_pop("caption",["table"]),e=e||this._tag_stack.try_pop("colgroup",["table"]),e=e||this._tag_stack.try_pop("thead",["table"]),e=e||this._tag_stack.try_pop("tbody",["table"])):"tr"===t.tag_name?(e=e||this._tag_stack.try_pop("caption",["table"]),e=e||this._tag_stack.try_pop("colgroup",["table"]),e=e||this._tag_stack.try_pop("tr",["table","thead","tbody","tfoot"])):"th"!==t.tag_name&&"td"!==t.tag_name||(e=e||this._tag_stack.try_pop("td",["table","thead","tbody","tfoot","tr"]),e=e||this._tag_stack.try_pop("th",["table","thead","tbody","tfoot","tr"])),t.parent=this._tag_stack.get_parser_token(),e},t.exports.Beautifier=f},function(t,e,n){var i=n(6).Options;function r(t){i.call(this,t,"html"),1===this.templating.length&&"auto"===this.templating[0]&&(this.templating=["django","erb","handlebars","php"]),this.indent_inner_html=this._get_boolean("indent_inner_html"),this.indent_body_inner_html=this._get_boolean("indent_body_inner_html",!0),this.indent_head_inner_html=this._get_boolean("indent_head_inner_html",!0),this.indent_handlebars=this._get_boolean("indent_handlebars",!0),this.wrap_attributes=this._get_selection("wrap_attributes",["auto","force","force-aligned","force-expand-multiline","aligned-multiple","preserve","preserve-aligned"]),this.wrap_attributes_indent_size=this._get_number("wrap_attributes_indent_size",this.indent_size),this.extra_liners=this._get_array("extra_liners",["head","body","/html"]),this.inline=this._get_array("inline",["a","abbr","area","audio","b","bdi","bdo","br","button","canvas","cite","code","data","datalist","del","dfn","em","embed","i","iframe","img","input","ins","kbd","keygen","label","map","mark","math","meter","noscript","object","output","progress","q","ruby","s","samp","select","small","span","strong","sub","sup","svg","template","textarea","time","u","var","video","wbr","text","acronym","big","strike","tt"]),this.void_elements=this._get_array("void_elements",["area","base","br","col","embed","hr","img","input","keygen","link","menuitem","meta","param","source","track","wbr","!doctype","?xml","basefont","isindex"]),this.unformatted=this._get_array("unformatted",[]),this.content_unformatted=this._get_array("content_unformatted",["pre","textarea"]),this.unformatted_content_delimiter=this._get_characters("unformatted_content_delimiter"),this.indent_scripts=this._get_selection("indent_scripts",["normal","keep","separate"])}r.prototype=new i,t.exports.Options=r},function(t,e,n){var i=n(9).Tokenizer,r=n(9).TOKEN,_=n(13).Directives,s=n(14).TemplatablePattern,o=n(12).Pattern,a={TAG_OPEN:"TK_TAG_OPEN",TAG_CLOSE:"TK_TAG_CLOSE",ATTRIBUTE:"TK_ATTRIBUTE",EQUALS:"TK_EQUALS",VALUE:"TK_VALUE",COMMENT:"TK_COMMENT",TEXT:"TK_TEXT",UNKNOWN:"TK_UNKNOWN",START:r.START,RAW:r.RAW,EOF:r.EOF},h=new _(/<\!--/,/-->/),p=function(t,e){i.call(this,t,e),this._current_tag_name="";var n=new s(this._input).read_options(this._options),r=new o(this._input);if(this.__patterns={word:n.until(/[\n\r\t <]/),single_quote:n.until_after(/'/),double_quote:n.until_after(/"/),attribute:n.until(/[\n\r\t =\/>]/),element_name:n.until(/[\n\r\t >\/]/),handlebars_comment:r.starting_with(/{{!--/).until_after(/--}}/),handlebars:r.starting_with(/{{/).until_after(/}}/),handlebars_open:r.until(/[\n\r\t }]/),handlebars_raw_close:r.until(/}}/),comment:r.starting_with(/<!--/).until_after(/-->/),cdata:r.starting_with(/<!\[CDATA\[/).until_after(/]]>/),conditional_comment:r.starting_with(/<!\[/).until_after(/]>/),processing:r.starting_with(/<\?/).until_after(/\?>/)},this._options.indent_handlebars&&(this.__patterns.word=this.__patterns.word.exclude("handlebars")),this._unformatted_content_delimiter=null,this._options.unformatted_content_delimiter){var _=this._input.get_literal_regexp(this._options.unformatted_content_delimiter);this.__patterns.unformatted_content_delimiter=r.matching(_).until_after(_)}};p.prototype=new i,p.prototype._is_comment=function(t){return!1},p.prototype._is_opening=function(t){return t.type===a.TAG_OPEN},p.prototype._is_closing=function(t,e){return t.type===a.TAG_CLOSE&&e&&((">"===t.text||"/>"===t.text)&&"<"===e.text[0]||"}}"===t.text&&"{"===e.text[0]&&"{"===e.text[1])},p.prototype._reset=function(){this._current_tag_name=""},p.prototype._get_next_token=function(t,e){var n=null;this._readWhitespace();var i=this._input.peek();return null===i?this._create_token(a.EOF,""):(n=n||this._read_open_handlebars(i,e),n=n||this._read_attribute(i,t,e),n=n||this._read_raw_content(i,t,e),n=n||this._read_close(i,e),n=n||this._read_content_word(i),n=n||this._read_comment_or_cdata(i),n=n||this._read_processing(i),n=n||this._read_open(i,e),n=n||this._create_token(a.UNKNOWN,this._input.next()),n)},p.prototype._read_comment_or_cdata=function(t){var e=null,n=null,i=null;if("<"===t){var r=this._input.peek(1);"!"===r&&(n=this.__patterns.comment.read(),n?(i=h.get_directives(n),i&&"start"===i.ignore&&(n+=h.readIgnored(this._input))):n=this.__patterns.cdata.read()),n&&(e=this._create_token(a.COMMENT,n),e.directives=i)}return e},p.prototype._read_processing=function(t){var e=null,n=null,i=null;if("<"===t){var r=this._input.peek(1);"!"!==r&&"?"!==r||(n=this.__patterns.conditional_comment.read(),n=n||this.__patterns.processing.read()),n&&(e=this._create_token(a.COMMENT,n),e.directives=i)}return e},p.prototype._read_open=function(t,e){var n=null,i=null;return e||"<"===t&&(n=this._input.next(),"/"===this._input.peek()&&(n+=this._input.next()),n+=this.__patterns.element_name.read(),i=this._create_token(a.TAG_OPEN,n)),i},p.prototype._read_open_handlebars=function(t,e){var n=null,i=null;return e||this._options.indent_handlebars&&"{"===t&&"{"===this._input.peek(1)&&("!"===this._input.peek(2)?(n=this.__patterns.handlebars_comment.read(),n=n||this.__patterns.handlebars.read(),i=this._create_token(a.COMMENT,n)):(n=this.__patterns.handlebars_open.read(),i=this._create_token(a.TAG_OPEN,n))),i},p.prototype._read_close=function(t,e){var n=null,i=null;return e&&("<"===e.text[0]&&(">"===t||"/"===t&&">"===this._input.peek(1))?(n=this._input.next(),"/"===t&&(n+=this._input.next()),i=this._create_token(a.TAG_CLOSE,n)):"{"===e.text[0]&&"}"===t&&"}"===this._input.peek(1)&&(this._input.next(),this._input.next(),i=this._create_token(a.TAG_CLOSE,"}}"))),i},p.prototype._read_attribute=function(t,e,n){var i=null,r="";if(n&&"<"===n.text[0])if("="===t)i=this._create_token(a.EQUALS,this._input.next());else if('"'===t||"'"===t){var _=this._input.next();_+='"'===t?this.__patterns.double_quote.read():this.__patterns.single_quote.read(),i=this._create_token(a.VALUE,_)}else r=this.__patterns.attribute.read(),r&&(i=e.type===a.EQUALS?this._create_token(a.VALUE,r):this._create_token(a.ATTRIBUTE,r));return i},p.prototype._is_content_unformatted=function(t){return-1===this._options.void_elements.indexOf(t)&&(-1!==this._options.content_unformatted.indexOf(t)||-1!==this._options.unformatted.indexOf(t))},p.prototype._read_raw_content=function(t,e,n){var i="";if(n&&"{"===n.text[0])i=this.__patterns.handlebars_raw_close.read();else if(e.type===a.TAG_CLOSE&&"<"===e.opened.text[0]){var r=e.opened.text.substr(1).toLowerCase();if("script"===r||"style"===r){var _=this._read_comment_or_cdata(t);if(_)return _.type=a.TEXT,_;i=this._input.readUntil(new RegExp("</"+r+"[\\n\\r\\t ]*?>","ig"))}else this._is_content_unformatted(r)&&(i=this._input.readUntil(new RegExp("</"+r+"[\\n\\r\\t ]*?>","ig")))}return i?this._create_token(a.TEXT,i):null},p.prototype._read_content_word=function(t){var e="";if(this._options.unformatted_content_delimiter&&t===this._options.unformatted_content_delimiter[0]&&(e=this.__patterns.unformatted_content_delimiter.read()),e||(e=this.__patterns.word.read()),e)return this._create_token(a.TEXT,e)},t.exports.Tokenizer=p,t.exports.TOKEN=a}]);function o(t,e){return s(t,e,i,_)}},2563:function(t,e,n){"use strict";var i,r,_,s,o,a,h,p,u,c,l,d,f,g,m,y,v,w,b,x;n.d(e,"h",(function(){return i})),n.d(e,"i",(function(){return r})),n.d(e,"f",(function(){return _})),n.d(e,"d",(function(){return p})),n.d(e,"b",(function(){return l})),n.d(e,"l",(function(){return m})),n.d(e,"g",(function(){return O})),n.d(e,"a",(function(){return j})),n.d(e,"e",(function(){return S})),n.d(e,"c",(function(){return P})),n.d(e,"k",(function(){return W})),n.d(e,"j",(function(){return q})),function(t){function e(t,e){return{line:t,character:e}}function n(t){var e=t;return Z.objectLiteral(e)&&Z.number(e.line)&&Z.number(e.character)}t.create=e,t.is=n}(i||(i={})),function(t){function e(t,e,n,r){if(Z.number(t)&&Z.number(e)&&Z.number(n)&&Z.number(r))return{start:i.create(t,e),end:i.create(n,r)};if(i.is(t)&&i.is(e))return{start:t,end:e};throw new Error("Range#create called with invalid arguments["+t+", "+e+", "+n+", "+r+"]")}function n(t){var e=t;return Z.objectLiteral(e)&&i.is(e.start)&&i.is(e.end)}t.create=e,t.is=n}(r||(r={})),function(t){function e(t,e){return{uri:t,range:e}}function n(t){var e=t;return Z.defined(e)&&r.is(e.range)&&(Z.string(e.uri)||Z.undefined(e.uri))}t.create=e,t.is=n}(_||(_={})),function(t){function e(t,e,n,i){return{targetUri:t,targetRange:e,targetSelectionRange:n,originSelectionRange:i}}function n(t){var e=t;return Z.defined(e)&&r.is(e.targetRange)&&Z.string(e.targetUri)&&(r.is(e.targetSelectionRange)||Z.undefined(e.targetSelectionRange))&&(r.is(e.originSelectionRange)||Z.undefined(e.originSelectionRange))}t.create=e,t.is=n}(s||(s={})),function(t){function e(t,e,n,i){return{red:t,green:e,blue:n,alpha:i}}function n(t){var e=t;return Z.number(e.red)&&Z.number(e.green)&&Z.number(e.blue)&&Z.number(e.alpha)}t.create=e,t.is=n}(o||(o={})),function(t){function e(t,e){return{range:t,color:e}}function n(t){var e=t;return r.is(e.range)&&o.is(e.color)}t.create=e,t.is=n}(a||(a={})),function(t){function e(t,e,n){return{label:t,textEdit:e,additionalTextEdits:n}}function n(t){var e=t;return Z.string(e.label)&&(Z.undefined(e.textEdit)||m.is(e))&&(Z.undefined(e.additionalTextEdits)||Z.typedArray(e.additionalTextEdits,m.is))}t.create=e,t.is=n}(h||(h={})),function(t){t["Comment"]="comment",t["Imports"]="imports",t["Region"]="region"}(p||(p={})),function(t){function e(t,e,n,i,r){var _={startLine:t,endLine:e};return Z.defined(n)&&(_.startCharacter=n),Z.defined(i)&&(_.endCharacter=i),Z.defined(r)&&(_.kind=r),_}function n(t){var e=t;return Z.number(e.startLine)&&Z.number(e.startLine)&&(Z.undefined(e.startCharacter)||Z.number(e.startCharacter))&&(Z.undefined(e.endCharacter)||Z.number(e.endCharacter))&&(Z.undefined(e.kind)||Z.string(e.kind))}t.create=e,t.is=n}(u||(u={})),function(t){function e(t,e){return{location:t,message:e}}function n(t){var e=t;return Z.defined(e)&&_.is(e.location)&&Z.string(e.message)}t.create=e,t.is=n}(c||(c={})),function(t){t.Error=1,t.Warning=2,t.Information=3,t.Hint=4}(l||(l={})),function(t){t.Unnecessary=1,t.Deprecated=2}(d||(d={})),function(t){function e(t,e,n,i,r,_){var s={range:t,message:e};return Z.defined(n)&&(s.severity=n),Z.defined(i)&&(s.code=i),Z.defined(r)&&(s.source=r),Z.defined(_)&&(s.relatedInformation=_),s}function n(t){var e=t;return Z.defined(e)&&r.is(e.range)&&Z.string(e.message)&&(Z.number(e.severity)||Z.undefined(e.severity))&&(Z.number(e.code)||Z.string(e.code)||Z.undefined(e.code))&&(Z.string(e.source)||Z.undefined(e.source))&&(Z.undefined(e.relatedInformation)||Z.typedArray(e.relatedInformation,c.is))}t.create=e,t.is=n}(f||(f={})),function(t){function e(t,e){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];var r={title:t,command:e};return Z.defined(n)&&n.length>0&&(r.arguments=n),r}function n(t){var e=t;return Z.defined(e)&&Z.string(e.title)&&Z.string(e.command)}t.create=e,t.is=n}(g||(g={})),function(t){function e(t,e){return{range:t,newText:e}}function n(t,e){return{range:{start:t,end:t},newText:e}}function i(t){return{range:t,newText:""}}function _(t){var e=t;return Z.objectLiteral(e)&&Z.string(e.newText)&&r.is(e.range)}t.replace=e,t.insert=n,t.del=i,t.is=_}(m||(m={})),function(t){function e(t,e){return{textDocument:t,edits:e}}function n(t){var e=t;return Z.defined(e)&&E.is(e.textDocument)&&Array.isArray(e.edits)}t.create=e,t.is=n}(y||(y={})),function(t){function e(t,e){var n={kind:"create",uri:t};return void 0===e||void 0===e.overwrite&&void 0===e.ignoreIfExists||(n.options=e),n}function n(t){var e=t;return e&&"create"===e.kind&&Z.string(e.uri)&&(void 0===e.options||(void 0===e.options.overwrite||Z.boolean(e.options.overwrite))&&(void 0===e.options.ignoreIfExists||Z.boolean(e.options.ignoreIfExists)))}t.create=e,t.is=n}(v||(v={})),function(t){function e(t,e,n){var i={kind:"rename",oldUri:t,newUri:e};return void 0===n||void 0===n.overwrite&&void 0===n.ignoreIfExists||(i.options=n),i}function n(t){var e=t;return e&&"rename"===e.kind&&Z.string(e.oldUri)&&Z.string(e.newUri)&&(void 0===e.options||(void 0===e.options.overwrite||Z.boolean(e.options.overwrite))&&(void 0===e.options.ignoreIfExists||Z.boolean(e.options.ignoreIfExists)))}t.create=e,t.is=n}(w||(w={})),function(t){function e(t,e){var n={kind:"delete",uri:t};return void 0===e||void 0===e.recursive&&void 0===e.ignoreIfNotExists||(n.options=e),n}function n(t){var e=t;return e&&"delete"===e.kind&&Z.string(e.uri)&&(void 0===e.options||(void 0===e.options.recursive||Z.boolean(e.options.recursive))&&(void 0===e.options.ignoreIfNotExists||Z.boolean(e.options.ignoreIfNotExists)))}t.create=e,t.is=n}(b||(b={})),function(t){function e(t){var e=t;return e&&(void 0!==e.changes||void 0!==e.documentChanges)&&(void 0===e.documentChanges||e.documentChanges.every((function(t){return Z.string(t.kind)?v.is(t)||w.is(t)||b.is(t):y.is(t)})))}t.is=e}(x||(x={}));var k,E,T,O,A,j,S,C,I,z,N,L,U,R,P,M,W,D,K,F,B,G,V,$,X,Q,q,Y=function(){function t(t){this.edits=t}return t.prototype.insert=function(t,e){this.edits.push(m.insert(t,e))},t.prototype.replace=function(t,e){this.edits.push(m.replace(t,e))},t.prototype.delete=function(t){this.edits.push(m.del(t))},t.prototype.add=function(t){this.edits.push(t)},t.prototype.all=function(){return this.edits},t.prototype.clear=function(){this.edits.splice(0,this.edits.length)},t}();(function(){function t(t){var e=this;this._textEditChanges=Object.create(null),t&&(this._workspaceEdit=t,t.documentChanges?t.documentChanges.forEach((function(t){if(y.is(t)){var n=new Y(t.edits);e._textEditChanges[t.textDocument.uri]=n}})):t.changes&&Object.keys(t.changes).forEach((function(n){var i=new Y(t.changes[n]);e._textEditChanges[n]=i})))}Object.defineProperty(t.prototype,"edit",{get:function(){return this._workspaceEdit},enumerable:!0,configurable:!0}),t.prototype.getTextEditChange=function(t){if(E.is(t)){if(this._workspaceEdit||(this._workspaceEdit={documentChanges:[]}),!this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.");var e=t,n=this._textEditChanges[e.uri];if(!n){var i=[],r={textDocument:e,edits:i};this._workspaceEdit.documentChanges.push(r),n=new Y(i),this._textEditChanges[e.uri]=n}return n}if(this._workspaceEdit||(this._workspaceEdit={changes:Object.create(null)}),!this._workspaceEdit.changes)throw new Error("Workspace edit is not configured for normal text edit changes.");n=this._textEditChanges[t];if(!n){i=[];this._workspaceEdit.changes[t]=i,n=new Y(i),this._textEditChanges[t]=n}return n},t.prototype.createFile=function(t,e){this.checkDocumentChanges(),this._workspaceEdit.documentChanges.push(v.create(t,e))},t.prototype.renameFile=function(t,e,n){this.checkDocumentChanges(),this._workspaceEdit.documentChanges.push(w.create(t,e,n))},t.prototype.deleteFile=function(t,e){this.checkDocumentChanges(),this._workspaceEdit.documentChanges.push(b.create(t,e))},t.prototype.checkDocumentChanges=function(){if(!this._workspaceEdit||!this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.")}})();(function(t){function e(t){return{uri:t}}function n(t){var e=t;return Z.defined(e)&&Z.string(e.uri)}t.create=e,t.is=n})(k||(k={})),function(t){function e(t,e){return{uri:t,version:e}}function n(t){var e=t;return Z.defined(e)&&Z.string(e.uri)&&(null===e.version||Z.number(e.version))}t.create=e,t.is=n}(E||(E={})),function(t){function e(t,e,n,i){return{uri:t,languageId:e,version:n,text:i}}function n(t){var e=t;return Z.defined(e)&&Z.string(e.uri)&&Z.string(e.languageId)&&Z.number(e.version)&&Z.string(e.text)}t.create=e,t.is=n}(T||(T={})),function(t){t.PlainText="plaintext",t.Markdown="markdown"}(O||(O={})),function(t){function e(e){var n=e;return n===t.PlainText||n===t.Markdown}t.is=e}(O||(O={})),function(t){function e(t){var e=t;return Z.objectLiteral(t)&&O.is(e.kind)&&Z.string(e.value)}t.is=e}(A||(A={})),function(t){t.Text=1,t.Method=2,t.Function=3,t.Constructor=4,t.Field=5,t.Variable=6,t.Class=7,t.Interface=8,t.Module=9,t.Property=10,t.Unit=11,t.Value=12,t.Enum=13,t.Keyword=14,t.Snippet=15,t.Color=16,t.File=17,t.Reference=18,t.Folder=19,t.EnumMember=20,t.Constant=21,t.Struct=22,t.Event=23,t.Operator=24,t.TypeParameter=25}(j||(j={})),function(t){t.PlainText=1,t.Snippet=2}(S||(S={})),function(t){t.Deprecated=1}(C||(C={})),function(t){function e(t){return{label:t}}t.create=e}(I||(I={})),function(t){function e(t,e){return{items:t||[],isIncomplete:!!e}}t.create=e}(z||(z={})),function(t){function e(t){return t.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}function n(t){var e=t;return Z.string(e)||Z.objectLiteral(e)&&Z.string(e.language)&&Z.string(e.value)}t.fromPlainText=e,t.is=n}(N||(N={})),function(t){function e(t){var e=t;return!!e&&Z.objectLiteral(e)&&(A.is(e.contents)||N.is(e.contents)||Z.typedArray(e.contents,N.is))&&(void 0===t.range||r.is(t.range))}t.is=e}(L||(L={})),function(t){function e(t,e){return e?{label:t,documentation:e}:{label:t}}t.create=e}(U||(U={})),function(t){function e(t,e){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];var r={label:t};return Z.defined(e)&&(r.documentation=e),Z.defined(n)?r.parameters=n:r.parameters=[],r}t.create=e}(R||(R={})),function(t){t.Text=1,t.Read=2,t.Write=3}(P||(P={})),function(t){function e(t,e){var n={range:t};return Z.number(e)&&(n.kind=e),n}t.create=e}(M||(M={})),function(t){t.File=1,t.Module=2,t.Namespace=3,t.Package=4,t.Class=5,t.Method=6,t.Property=7,t.Field=8,t.Constructor=9,t.Enum=10,t.Interface=11,t.Function=12,t.Variable=13,t.Constant=14,t.String=15,t.Number=16,t.Boolean=17,t.Array=18,t.Object=19,t.Key=20,t.Null=21,t.EnumMember=22,t.Struct=23,t.Event=24,t.Operator=25,t.TypeParameter=26}(W||(W={})),function(t){t.Deprecated=1}(D||(D={})),function(t){function e(t,e,n,i,r){var _={name:t,kind:e,location:{uri:i,range:n}};return r&&(_.containerName=r),_}t.create=e}(K||(K={})),function(t){function e(t,e,n,i,r,_){var s={name:t,detail:e,kind:n,range:i,selectionRange:r};return void 0!==_&&(s.children=_),s}function n(t){var e=t;return e&&Z.string(e.name)&&Z.number(e.kind)&&r.is(e.range)&&r.is(e.selectionRange)&&(void 0===e.detail||Z.string(e.detail))&&(void 0===e.deprecated||Z.boolean(e.deprecated))&&(void 0===e.children||Array.isArray(e.children))}t.create=e,t.is=n}(F||(F={})),function(t){t.Empty="",t.QuickFix="quickfix",t.Refactor="refactor",t.RefactorExtract="refactor.extract",t.RefactorInline="refactor.inline",t.RefactorRewrite="refactor.rewrite",t.Source="source",t.SourceOrganizeImports="source.organizeImports",t.SourceFixAll="source.fixAll"}(B||(B={})),function(t){function e(t,e){var n={diagnostics:t};return void 0!==e&&null!==e&&(n.only=e),n}function n(t){var e=t;return Z.defined(e)&&Z.typedArray(e.diagnostics,f.is)&&(void 0===e.only||Z.typedArray(e.only,Z.string))}t.create=e,t.is=n}(G||(G={})),function(t){function e(t,e,n){var i={title:t};return g.is(e)?i.command=e:i.edit=e,void 0!==n&&(i.kind=n),i}function n(t){var e=t;return e&&Z.string(e.title)&&(void 0===e.diagnostics||Z.typedArray(e.diagnostics,f.is))&&(void 0===e.kind||Z.string(e.kind))&&(void 0!==e.edit||void 0!==e.command)&&(void 0===e.command||g.is(e.command))&&(void 0===e.isPreferred||Z.boolean(e.isPreferred))&&(void 0===e.edit||x.is(e.edit))}t.create=e,t.is=n}(V||(V={})),function(t){function e(t,e){var n={range:t};return Z.defined(e)&&(n.data=e),n}function n(t){var e=t;return Z.defined(e)&&r.is(e.range)&&(Z.undefined(e.command)||g.is(e.command))}t.create=e,t.is=n}($||($={})),function(t){function e(t,e){return{tabSize:t,insertSpaces:e}}function n(t){var e=t;return Z.defined(e)&&Z.number(e.tabSize)&&Z.boolean(e.insertSpaces)}t.create=e,t.is=n}(X||(X={})),function(t){function e(t,e,n){return{range:t,target:e,data:n}}function n(t){var e=t;return Z.defined(e)&&r.is(e.range)&&(Z.undefined(e.target)||Z.string(e.target))}t.create=e,t.is=n}(Q||(Q={})),function(t){function e(t,e){return{range:t,parent:e}}function n(e){var n=e;return void 0!==n&&r.is(n.range)&&(void 0===n.parent||t.is(n.parent))}t.create=e,t.is=n}(q||(q={}));var J;(function(t){function e(t,e,n,i){return new H(t,e,n,i)}function n(t){var e=t;return!!(Z.defined(e)&&Z.string(e.uri)&&(Z.undefined(e.languageId)||Z.string(e.languageId))&&Z.number(e.lineCount)&&Z.func(e.getText)&&Z.func(e.positionAt)&&Z.func(e.offsetAt))}function i(t,e){for(var n=t.getText(),i=r(e,(function(t,e){var n=t.range.start.line-e.range.start.line;return 0===n?t.range.start.character-e.range.start.character:n})),_=n.length,s=i.length-1;s>=0;s--){var o=i[s],a=t.offsetAt(o.range.start),h=t.offsetAt(o.range.end);if(!(h<=_))throw new Error("Overlapping edit");n=n.substring(0,a)+o.newText+n.substring(h,n.length),_=a}return n}function r(t,e){if(t.length<=1)return t;var n=t.length/2|0,i=t.slice(0,n),_=t.slice(n);r(i,e),r(_,e);var s=0,o=0,a=0;while(s<i.length&&o<_.length){var h=e(i[s],_[o]);t[a++]=h<=0?i[s++]:_[o++]}while(s<i.length)t[a++]=i[s++];while(o<_.length)t[a++]=_[o++];return t}t.create=e,t.is=n,t.applyEdits=i})(J||(J={}));var Z,H=function(){function t(t,e,n,i){this._uri=t,this._languageId=e,this._version=n,this._content=i,this._lineOffsets=void 0}return Object.defineProperty(t.prototype,"uri",{get:function(){return this._uri},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"languageId",{get:function(){return this._languageId},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"version",{get:function(){return this._version},enumerable:!0,configurable:!0}),t.prototype.getText=function(t){if(t){var e=this.offsetAt(t.start),n=this.offsetAt(t.end);return this._content.substring(e,n)}return this._content},t.prototype.update=function(t,e){this._content=t.text,this._version=e,this._lineOffsets=void 0},t.prototype.getLineOffsets=function(){if(void 0===this._lineOffsets){for(var t=[],e=this._content,n=!0,i=0;i<e.length;i++){n&&(t.push(i),n=!1);var r=e.charAt(i);n="\r"===r||"\n"===r,"\r"===r&&i+1<e.length&&"\n"===e.charAt(i+1)&&i++}n&&e.length>0&&t.push(e.length),this._lineOffsets=t}return this._lineOffsets},t.prototype.positionAt=function(t){t=Math.max(Math.min(t,this._content.length),0);var e=this.getLineOffsets(),n=0,r=e.length;if(0===r)return i.create(0,t);while(n<r){var _=Math.floor((n+r)/2);e[_]>t?r=_:n=_+1}var s=n-1;return i.create(s,t-e[s])},t.prototype.offsetAt=function(t){var e=this.getLineOffsets();if(t.line>=e.length)return this._content.length;if(t.line<0)return 0;var n=e[t.line],i=t.line+1<e.length?e[t.line+1]:this._content.length;return Math.max(Math.min(n+t.character,i),n)},Object.defineProperty(t.prototype,"lineCount",{get:function(){return this.getLineOffsets().length},enumerable:!0,configurable:!0}),t}();(function(t){var e=Object.prototype.toString;function n(t){return"undefined"!==typeof t}function i(t){return"undefined"===typeof t}function r(t){return!0===t||!1===t}function _(t){return"[object String]"===e.call(t)}function s(t){return"[object Number]"===e.call(t)}function o(t){return"[object Function]"===e.call(t)}function a(t){return null!==t&&"object"===typeof t}function h(t,e){return Array.isArray(t)&&t.every(e)}t.defined=n,t.undefined=i,t.boolean=r,t.string=_,t.number=s,t.func=o,t.objectLiteral=a,t.typedArray=h})(Z||(Z={}))}}]);