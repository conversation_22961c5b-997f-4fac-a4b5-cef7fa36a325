(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-45290234"],{"0122":function(t,e,i){"use strict";i.d(e,"a",(function(){return r}));var o=i("6edb"),n=i("6855"),r=function(t){function e(e,i){var o=t.call(this,e,i)||this;return o.item=i.item,o.canvasX=i.canvasX,o.canvasY=i.canvasY,o.wheelDelta=i.wheelDelta,o.detail=i.detail,o}return Object(o["b"])(e,t),e}(n["d"])},1649:function(t,e,i){"use strict";var o=i("9ab4"),n=i("3822"),r=i("8937");Object(n["l"])("circle",{options:{size:n["e"].defaultNode.size,style:{x:0,y:0,stroke:n["e"].defaultNode.style.stroke,fill:n["e"].defaultNode.style.fill,lineWidth:n["e"].defaultNode.style.lineWidth},labelCfg:{style:{fill:n["e"].nodeLabel.style.fill,fontSize:n["e"].nodeLabel.style.fontSize,fontFamily:n["e"].windowFontFamily}},linkPoints:{top:!1,right:!1,bottom:!1,left:!1,size:n["e"].defaultNode.linkPoints.size,lineWidth:n["e"].defaultNode.linkPoints.lineWidth,fill:n["e"].defaultNode.linkPoints.fill,stroke:n["e"].defaultNode.linkPoints.stroke},icon:{show:!1,img:"https://gw.alipayobjects.com/zos/bmw-prod/5d015065-8505-4e7a-baec-976f81e3c41d.svg",width:20,height:20},stateStyles:Object(o["a"])({},n["e"].nodeStateStyles)},shapeType:"circle",labelPosition:"center",drawShape:function(t,e){var i=(this.mergeStyle||this.getOptions(t)).icon,n=void 0===i?{}:i,a=this.getShapeStyle(t),s=Object(r["deepMix"])({},n,t.icon),c="".concat(this.type,"-keyShape"),h=e.addShape("circle",{attrs:a,className:c,name:c,draggable:!0});e["shapeMap"][c]=h;var d=s.width,l=s.height,p=s.show,u=s.text;if(p){var f="".concat(this.type,"-icon");e["shapeMap"][f]=u?e.addShape("text",{attrs:Object(o["a"])({x:0,y:0,fontSize:12,fill:"#000",stroke:"#000",textBaseline:"middle",textAlign:"center"},s),className:f,name:f,draggable:!0}):e.addShape("image",{attrs:Object(o["a"])({x:-d/2,y:-l/2},s),className:f,name:f,draggable:!0})}return this.drawLinkPoints(t,e),h},drawLinkPoints:function(t,e){var i=(this.mergeStyle||this.getOptions(t)).linkPoints;if(i){var n=i||{},r=n.top,a=n.left,s=n.right,c=n.bottom,h=n.size,d=n.r,l=Object(o["e"])(n,["top","left","right","bottom","size","r"]),p=this.getSize(t),u=p[0]/2;if(a){var f="link-point-left";e["shapeMap"][f]=e.addShape("circle",{attrs:Object(o["a"])(Object(o["a"])({},l),{x:-u,y:0,r:h/2||d||5}),className:f,name:f,isAnchorPoint:!0})}if(s){var g="link-point-right";e["shapeMap"][g]=e.addShape("circle",{attrs:Object(o["a"])(Object(o["a"])({},l),{x:u,y:0,r:h/2||d||5}),className:g,name:g,isAnchorPoint:!0})}if(r){var y="link-point-top";e["shapeMap"][y]=e.addShape("circle",{attrs:Object(o["a"])(Object(o["a"])({},l),{x:0,y:-u,r:h/2||d||5}),className:y,name:y,isAnchorPoint:!0})}if(c){var m="link-point-bottom";e["shapeMap"][m]=e.addShape("circle",{attrs:Object(o["a"])(Object(o["a"])({},l),{x:0,y:u,r:h/2||d||5}),className:m,name:m,isAnchorPoint:!0})}}},getShapeStyle:function(t){var e=(this.mergeStyle||this.getOptions(t)).style,i={stroke:t.color},n=Object(r["deepMix"])({},e,i),a=this.getSize(t),s=a[0]/2,c=Object(o["a"])({x:0,y:0,r:s},n);return c},update:function(t,e,i){var n=e.getContainer(),r=this.getSize(t),a=Object(o["a"])({},t.style);void 0===t.style.stroke&&t.color&&(a.stroke=t.color),void 0!==t.style.r||isNaN(r[0])||(a.r=r[0]/2),this.updateShape(t,e,a,!0,i),this.updateLinkPoints(t,n)}},"single-node"),Object(n["l"])("rect",{options:{size:[100,30],style:{radius:0,stroke:n["e"].defaultNode.style.stroke,fill:n["e"].defaultNode.style.fill,lineWidth:n["e"].defaultNode.style.lineWidth},labelCfg:{style:{fill:n["e"].nodeLabel.style.fill,fontSize:n["e"].nodeLabel.style.fontSize,fontFamily:n["e"].windowFontFamily}},linkPoints:{top:!1,right:!1,bottom:!1,left:!1,size:n["e"].defaultNode.linkPoints.size,lineWidth:n["e"].defaultNode.linkPoints.lineWidth,fill:n["e"].defaultNode.linkPoints.fill,stroke:n["e"].defaultNode.linkPoints.stroke},icon:{show:!1,img:"https://gw.alipayobjects.com/zos/bmw-prod/5d015065-8505-4e7a-baec-976f81e3c41d.svg",width:20,height:20},anchorPoints:[[0,.5],[1,.5]],stateStyles:Object(o["a"])({},n["e"].nodeStateStyles)},shapeType:"rect",labelPosition:"center",drawShape:function(t,e){var i=this.getShapeStyle(t),o=e.addShape("rect",{attrs:i,className:"".concat(this.type,"-keyShape"),name:"".concat(this.type,"-keyShape"),draggable:!0});return e["shapeMap"]["".concat(this.type,"-keyShape")]=o,this.drawLinkPoints(t,e),o},drawLinkPoints:function(t,e){var i=(this.mergeStyle||this.getOptions(t)).linkPoints,n=void 0===i?{}:i,r=n.top,a=n.left,s=n.right,c=n.bottom,h=n.size,d=n.r,l=Object(o["e"])(n,["top","left","right","bottom","size","r"]),p=this.getSize(t),u=p[0],f=p[1];a&&(e["shapeMap"]["link-point-left"]=e.addShape("circle",{attrs:Object(o["a"])(Object(o["a"])({},l),{x:-u/2,y:0,r:h/2||d||5}),className:"link-point-left",name:"link-point-left",isAnchorPoint:!0})),s&&(e["shapeMap"]["link-point-right"]=e.addShape("circle",{attrs:Object(o["a"])(Object(o["a"])({},l),{x:u/2,y:0,r:h/2||d||5}),className:"link-point-right",name:"link-point-right",isAnchorPoint:!0})),r&&(e["shapeMap"]["link-point-top"]=e.addShape("circle",{attrs:Object(o["a"])(Object(o["a"])({},l),{x:0,y:-f/2,r:h/2||d||5}),className:"link-point-top",name:"link-point-top",isAnchorPoint:!0})),c&&(e["shapeMap"]["link-point-bottom"]=e.addShape("circle",{attrs:Object(o["a"])(Object(o["a"])({},l),{x:0,y:f/2,r:h/2||d||5}),className:"link-point-bottom",name:"link-point-bottom",isAnchorPoint:!0}))},getShapeStyle:function(t){var e=(this.mergeStyle||this.getOptions(t)).style,i={stroke:t.color},n=Object(r["mix"])({},e,i),a=this.getSize(t),s=n.width||a[0],c=n.height||a[1],h=Object(o["a"])({x:-s/2,y:-c/2,width:s,height:c},n);return h},update:function(t,e,i){var o=e.getContainer(),n=this.getOptions({}).style,a=this.getSize(t),s=e.get("keyShape");t.size||(a[0]=s.attr("width")||n.width,a[1]=s.attr("height")||n.height);var c={stroke:t.color,x:-a[0]/2,y:-a[1]/2,width:a[0],height:a[1]},h=Object(r["mix"])({},n,s.attr(),c);h=Object(r["mix"])(h,t.style),this.updateShape(t,e,h,!1,i),this.updateLinkPoints(t,o)}},"single-node"),Object(n["l"])("ellipse",{options:{size:[80,40],style:{x:0,y:0,stroke:n["e"].defaultNode.style.stroke,fill:n["e"].defaultNode.style.fill,lineWidth:n["e"].defaultNode.style.lineWidth},labelCfg:{style:{fill:n["e"].nodeLabel.style.fill,fontSize:n["e"].nodeLabel.style.fontSize,fontFamily:n["e"].windowFontFamily}},linkPoints:{top:!1,right:!1,bottom:!1,left:!1,size:n["e"].defaultNode.linkPoints.size,lineWidth:n["e"].defaultNode.linkPoints.lineWidth,fill:n["e"].defaultNode.linkPoints.fill,stroke:n["e"].defaultNode.linkPoints.stroke},icon:{show:!1,img:"https://gw.alipayobjects.com/zos/bmw-prod/5d015065-8505-4e7a-baec-976f81e3c41d.svg",width:20,height:20},stateStyles:Object(o["a"])({},n["e"].nodeStateStyles)},shapeType:"ellipse",labelPosition:"center",drawShape:function(t,e){var i=(this.mergeStyle||this.getOptions(t)).icon,n=void 0===i?{}:i,r=this.getShapeStyle(t),a=e.addShape("ellipse",{attrs:r,className:"ellipse-keyShape",name:"ellipse-keyShape",draggable:!0});e["shapeMap"]["ellipse-keyShape"]=a;var s=n.width,c=n.height,h=n.show,d=n.text;return h&&(e["shapeMap"]["".concat(this.type,"-icon")]=d?e.addShape("text",{attrs:Object(o["a"])({x:0,y:0,fontSize:12,fill:"#000",stroke:"#000",textBaseline:"middle",textAlign:"center"},n),className:"".concat(this.type,"-icon"),name:"".concat(this.type,"-icon"),draggable:!0}):e.addShape("image",{attrs:Object(o["a"])({x:-s/2,y:-c/2},n),className:"".concat(this.type,"-icon"),name:"".concat(this.type,"-icon"),draggable:!0})),this.drawLinkPoints(t,e),a},drawLinkPoints:function(t,e){var i=(this.mergeStyle||this.getOptions(t)).linkPoints,n=void 0===i?{}:i,r=n.top,a=n.left,s=n.right,c=n.bottom,h=n.size,d=n.r,l=Object(o["e"])(n,["top","left","right","bottom","size","r"]),p=this.getSize(t),u=p[0]/2,f=p[1]/2;a&&(e["shapeMap"]["link-point-left"]=e.addShape("circle",{attrs:Object(o["a"])(Object(o["a"])({},l),{x:-u,y:0,r:h/2||d||5}),className:"link-point-left",name:"link-point-left",isAnchorPoint:!0})),s&&(e["shapeMap"]["link-point-right"]=e.addShape("circle",{attrs:Object(o["a"])(Object(o["a"])({},l),{x:u,y:0,r:h/2||d||5}),className:"link-point-right",name:"link-point-right",isAnchorPoint:!0})),r&&(e["shapeMap"]["link-point-top"]=e.addShape("circle",{attrs:Object(o["a"])(Object(o["a"])({},l),{x:0,y:-f,r:h/2||d||5}),className:"link-point-top",name:"link-point-top",isAnchorPoint:!0})),c&&(e["shapeMap"]["link-point-bottom"]=e.addShape("circle",{attrs:Object(o["a"])(Object(o["a"])({},l),{x:0,y:f,r:h/2||d||5}),className:"link-point-bottom",name:"link-point-bottom",isAnchorPoint:!0}))},getShapeStyle:function(t){var e=(this.mergeStyle||this.getOptions(t)).style,i={stroke:t.color},n=Object(r["mix"])({},e,i),a=this.getSize(t),s=a[0]/2,c=a[1]/2,h=Object(o["a"])({x:0,y:0,rx:s,ry:c},n);return h},update:function(t,e,i){var o=e.getContainer(),n=this.getOptions({}).style,a=this.getSize(t),s={stroke:t.color,rx:a[0]/2,ry:a[1]/2},c=e.get("keyShape"),h=Object(r["mix"])({},n,c.attr(),s);h=Object(r["mix"])(h,t.style),this.updateShape(t,e,h,!0,i),this.updateLinkPoints(t,o)}},"single-node"),Object(n["l"])("diamond",{options:{size:[80,80],style:{stroke:n["e"].defaultNode.style.stroke,fill:n["e"].defaultNode.style.fill,lineWidth:n["e"].defaultNode.style.lineWidth},labelCfg:{style:{fill:n["e"].nodeLabel.style.fill,fontSize:n["e"].nodeLabel.style.fontSize,fontFamily:n["e"].windowFontFamily}},linkPoints:{top:!1,right:!1,bottom:!1,left:!1,size:n["e"].defaultNode.linkPoints.size,lineWidth:n["e"].defaultNode.linkPoints.lineWidth,fill:n["e"].defaultNode.linkPoints.fill,stroke:n["e"].defaultNode.linkPoints.stroke},icon:{show:!1,img:"https://gw.alipayobjects.com/zos/bmw-prod/5d015065-8505-4e7a-baec-976f81e3c41d.svg",width:20,height:20},stateStyles:Object(o["a"])({},n["e"].nodeStateStyles)},shapeType:"diamond",labelPosition:"center",drawShape:function(t,e){var i=(this.mergeStyle||this.getOptions(t)).icon,n=void 0===i?{}:i,r=this.getShapeStyle(t),a=e.addShape("path",{attrs:r,className:"".concat(this.type,"-keyShape"),name:"".concat(this.type,"-keyShape"),draggable:!0});e["shapeMap"]["".concat(this.type,"-keyShape")]=a;var s=n.width,c=n.height,h=n.show,d=n.text;return h&&(e["shapeMap"]["".concat(this.type,"-icon")]=d?e.addShape("text",{attrs:Object(o["a"])({x:0,y:0,fontSize:12,fill:"#000",stroke:"#000",textBaseline:"middle",textAlign:"center"},n),className:"".concat(this.type,"-icon"),name:"".concat(this.type,"-icon"),draggable:!0}):e.addShape("image",{attrs:Object(o["a"])({x:-s/2,y:-c/2},n),className:"".concat(this.type,"-icon"),name:"".concat(this.type,"-icon"),draggable:!0})),this.drawLinkPoints(t,e),a},drawLinkPoints:function(t,e){var i=(this.mergeStyle||this.getOptions(t)).linkPoints,n=void 0===i?{}:i,r=n.top,a=n.left,s=n.right,c=n.bottom,h=n.size,d=n.r,l=Object(o["e"])(n,["top","left","right","bottom","size","r"]),p=this.getSize(t),u=p[0],f=p[1];a&&(e["shapeMap"]["link-point-left"]=e.addShape("circle",{attrs:Object(o["a"])(Object(o["a"])({},l),{x:-u/2,y:0,r:h/2||d||5}),className:"link-point-left",name:"link-point-left",isAnchorPoint:!0})),s&&(e["shapeMap"]["link-point-right"]=e.addShape("circle",{attrs:Object(o["a"])(Object(o["a"])({},l),{x:u/2,y:0,r:h/2||d||5}),className:"link-point-right",name:"link-point-right",isAnchorPoint:!0})),r&&(e["shapeMap"]["link-point-top"]=e.addShape("circle",{attrs:Object(o["a"])(Object(o["a"])({},l),{x:0,y:-f/2,r:h/2||d||5}),className:"link-point-top",name:"link-point-top",isAnchorPoint:!0})),c&&(e["shapeMap"]["link-point-bottom"]=e.addShape("circle",{attrs:Object(o["a"])(Object(o["a"])({},l),{x:0,y:f/2,r:h/2||d||5}),className:"link-point-bottom",name:"link-point-bottom",isAnchorPoint:!0}))},getPath:function(t){var e=this.getSize(t),i=e[0],o=e[1],n=[["M",0,-o/2],["L",i/2,0],["L",0,o/2],["L",-i/2,0],["Z"]];return n},getShapeStyle:function(t){var e=(this.mergeStyle||this.getOptions(t)).style,i={stroke:t.color},n=Object(r["mix"])({},e,i),a=this.getPath(t),s=Object(o["a"])({path:a},n);return s},update:function(t,e,i){var o=e.getContainer(),n=this.getOptions({}).style,a=this.getPath(t),s={stroke:t.color,path:a},c=e.get("keyShape"),h=Object(r["mix"])({},n,c.attr(),s);h=Object(r["mix"])(h,t.style),this.updateShape(t,e,h,!0,i),this.updateLinkPoints(t,o)}},"single-node"),Object(n["l"])("triangle",{options:{size:40,direction:"up",style:{stroke:n["e"].defaultNode.style.stroke,fill:n["e"].defaultNode.style.fill,lineWidth:n["e"].defaultNode.style.lineWidth},labelCfg:{style:{fill:n["e"].nodeLabel.style.fill,fontSize:n["e"].nodeLabel.style.fontSize},offset:15},linkPoints:{top:!1,right:!1,bottom:!1,left:!1,size:n["e"].defaultNode.linkPoints.size,lineWidth:n["e"].defaultNode.linkPoints.lineWidth,fill:n["e"].defaultNode.linkPoints.fill,stroke:n["e"].defaultNode.linkPoints.stroke},icon:{show:!1,img:"https://gw.alipayobjects.com/zos/bmw-prod/5d015065-8505-4e7a-baec-976f81e3c41d.svg",width:20,height:20,offset:6},stateStyles:Object(o["a"])({},n["e"].nodeStateStyles)},shapeType:"triangle",labelPosition:"bottom",drawShape:function(t,e){var i=this.mergeStyle||this.getOptions(t),n=i.icon,r=void 0===n?{}:n,a=i.direction,s=this.getShapeStyle(t),c=t.direction||a,h=e.addShape("path",{attrs:s,className:"".concat(this.type,"-keyShape"),name:"".concat(this.type,"-keyShape"),draggable:!0});e["shapeMap"]["".concat(this.type,"-keyShape")]=h;var d=r.width,l=r.height,p=r.show,u=r.offset,f=r.text;if(p)if(f)e["shapeMap"]["".concat(this.type,"-icon")]=e.addShape("text",{attrs:Object(o["a"])({x:0,y:0,fontSize:12,fill:"#000",stroke:"#000",textBaseline:"middle",textAlign:"center"},r),className:"".concat(this.type,"-icon"),name:"".concat(this.type,"-icon"),draggable:!0});else{var g=-d/2,y=-l/2;"up"!==c&&"down"!==c||(y+=u),"left"!==c&&"right"!==c||(g+=u),e["shapeMap"]["".concat(this.type,"-icon")]=e.addShape("image",{attrs:Object(o["a"])({x:g,y:y},r),className:"".concat(this.type,"-icon"),name:"".concat(this.type,"-icon"),draggable:!0})}return this.drawLinkPoints(t,e),h},drawLinkPoints:function(t,e){var i=this.mergeStyle||this.getOptions(t),n=i.linkPoints,r=void 0===n?{}:n,a=i.direction,s=t.direction||a,c=r.top,h=r.left,d=r.right,l=r.bottom,p=r.size,u=r.r,f=Object(o["e"])(r,["top","left","right","bottom","size","r"]),g=this.getSize(t),y=g[0];if(h){var m=null,b=y*Math.sin(1/3*Math.PI),v=y*Math.sin(1/3*Math.PI);"up"===s?m=[-v,b]:"down"===s?m=[-v,-b]:"left"===s&&(m=[-v,v-b]),m&&(e["shapeMap"]["link-point-left"]=e.addShape("circle",{attrs:Object(o["a"])(Object(o["a"])({},f),{x:m[0],y:m[1],r:p/2||u||5}),className:"link-point-left",name:"link-point-left"}))}if(d){var x=null;b=y*Math.sin(1/3*Math.PI),v=y*Math.sin(1/3*Math.PI);"up"===s?x=[v,b]:"down"===s?x=[v,-b]:"right"===s&&(x=[v,v-b]),x&&(e["shapeMap"]["link-point-right"]=e.addShape("circle",{attrs:Object(o["a"])(Object(o["a"])({},f),{x:x[0],y:x[1],r:p/2||u||5}),className:"link-point-right",name:"link-point-right"}))}if(c){var O=null;b=y*Math.sin(1/3*Math.PI),v=y*Math.sin(1/3*Math.PI);"up"===s?O=[v-b,-b]:"left"===s?O=[v,-b]:"right"===s&&(O=[-v,-b]),O&&(e["shapeMap"]["link-point-top"]=e.addShape("circle",{attrs:Object(o["a"])(Object(o["a"])({},f),{x:O[0],y:O[1],r:p/2||u||5}),className:"link-point-top",name:"link-point-top"}))}if(l){var j=null;b=y*Math.sin(1/3*Math.PI),v=y*Math.sin(1/3*Math.PI);"down"===s?j=[-v+b,b]:"left"===s?j=[v,b]:"right"===s&&(j=[-v,b]),j&&(e["shapeMap"]["link-point-bottom"]=e.addShape("circle",{attrs:Object(o["a"])(Object(o["a"])({},f),{x:j[0],y:j[1],r:p/2||u||5}),className:"link-point-bottom",name:"link-point-bottom"}))}},getPath:function(t){var e=(this.mergeStyle||this.getOptions(t)).direction,i=t.direction||e,o=this.getSize(t),n=o[0],r=n*Math.sin(1/3*Math.PI),a=n*Math.sin(1/3*Math.PI),s=[["M",-a,r],["L",0,-r],["L",a,r],["Z"]];return"down"===i?s=[["M",-a,-r],["L",a,-r],["L",0,r],["Z"]]:"left"===i?s=[["M",-a,a-r],["L",a,-a],["L",a,a],["Z"]]:"right"===i&&(s=[["M",a,a-r],["L",-a,a],["L",-a,-a],["Z"]]),s},getShapeStyle:function(t){var e=(this.mergeStyle||this.getOptions(t)).style,i={stroke:t.color},n=Object(r["mix"])({},e,i),a=this.getPath(t),s=Object(o["a"])({path:a},n);return s},update:function(t,e,i){var o=e.getContainer(),n=this.getOptions({}).style,a=this.getPath(t),s={stroke:t.color,path:a},c=e.get("keyShape"),h=Object(r["mix"])({},n,c.attr(),s);h=Object(r["mix"])(h,t.style),this.updateShape(t,e,h,!0,i),this.updateLinkPoints(t,o)},updateLinkPoints:function(t,e){var i=this.getOptions({}),n=i.linkPoints,a=i.direction,s=t.direction||a,c=e["shapeMap"]["link-point-left"]||e.find((function(t){return"link-point-left"===t.get("className")})),h=e["shapeMap"]["link-point-right"]||e.find((function(t){return"link-point-right"===t.get("className")})),d=e["shapeMap"]["link-point-top"]||e.find((function(t){return"link-point-top"===t.get("className")})),l=e["shapeMap"]["link-point-bottom"]||e.find((function(t){return"link-point-bottom"===t.get("className")})),p=n,u=c||h||d||l;u&&(p=u.attr());var f=Object(r["mix"])({},p,t.linkPoints),g=f.fill,y=f.stroke,m=f.lineWidth,b=f.size/2;b||(b=f.r);var v=t.linkPoints?t.linkPoints:{left:void 0,right:void 0,top:void 0,bottom:void 0},x=v.left,O=v.right,j=v.top,S=v.bottom,M=this.getSize(t),k=M[0],w={r:b,fill:g,stroke:y,lineWidth:m},C=null,P=k*Math.sin(1/3*Math.PI),I=k*Math.sin(1/3*Math.PI);"up"===s?C=[-I,P]:"down"===s?C=[-I,-P]:"left"===s&&(C=[-I,I-P]),C&&(c?x||void 0===x?c.attr(Object(o["a"])(Object(o["a"])({},w),{x:C[0],y:C[1]})):(c.remove(),delete e["shapeMap"]["link-point-left"]):x&&(e["shapeMap"]["link-point-left"]=e.addShape("circle",{attrs:Object(o["a"])(Object(o["a"])({},w),{x:C[0],y:C[1]}),className:"link-point-left",name:"link-point-left",isAnchorPoint:!0})));var N=null;"up"===s?N=[I,P]:"down"===s?N=[I,-P]:"right"===s&&(N=[I,I-P]),N&&(h?O||void 0===O?h.attr(Object(o["a"])(Object(o["a"])({},w),{x:N[0],y:N[1]})):(h.remove(),delete e["shapeMap"]["link-point-right"]):O&&(e["shapeMap"]["link-point-right"]=e.addShape("circle",{attrs:Object(o["a"])(Object(o["a"])({},w),{x:N[0],y:N[1]}),className:"link-point-right",name:"link-point-right",isAnchorPoint:!0})));var B=null;"up"===s?B=[I-P,-P]:"left"===s?B=[I,-P]:"right"===s&&(B=[-I,-P]),B&&(d?j||void 0===j?d.attr(Object(o["a"])(Object(o["a"])({},w),{x:B[0],y:B[1]})):(d.remove(),delete e["shapeMap"]["link-point-top"]):j&&(e["shapeMap"]["link-point-top"]=e.addShape("circle",{attrs:Object(o["a"])(Object(o["a"])({},w),{x:B[0],y:B[1]}),className:"link-point-top",name:"link-point-top",isAnchorPoint:!0})));var T=null;"down"===s?T=[-I+P,P]:"left"===s?T=[I,P]:"right"===s&&(T=[-I,P]),T&&(l?S||void 0===S?l.attr(Object(o["a"])(Object(o["a"])({},w),{x:T[0],y:T[1]})):(l.remove(),delete e["shapeMap"]["link-point-bottom"]):S&&(e["shapeMap"]["link-point-bottom"]=e.addShape("circle",{attrs:Object(o["a"])(Object(o["a"])({},w),{x:T[0],y:T[1]}),className:"link-point-bottom",name:"link-point-bottom",isAnchorPoint:!0})))}},"single-node"),Object(n["l"])("modelRect",{options:{size:[185,70],style:{radius:5,stroke:"#69c0ff",fill:"#ffffff",lineWidth:n["e"].defaultNode.style.lineWidth,fillOpacity:1},labelCfg:{style:{fill:"#595959",fontSize:14,fontFamily:n["e"].windowFontFamily},offset:30},descriptionCfg:{style:{fontSize:12,fill:"#bfbfbf",fontFamily:n["e"].windowFontFamily},paddingTop:0},preRect:{show:!0,width:4,fill:"#40a9ff",radius:2},linkPoints:{top:!1,right:!1,bottom:!1,left:!1,size:10,lineWidth:1,fill:"#72CC4A",stroke:"#72CC4A"},logoIcon:{show:!0,x:0,y:0,img:"https://gw.alipayobjects.com/zos/basement_prod/4f81893c-1806-4de4-aff3-9a6b266bc8a2.svg",width:16,height:16,offset:0},stateIcon:{show:!0,x:0,y:0,img:"https://gw.alipayobjects.com/zos/basement_prod/300a2523-67e0-4cbf-9d4a-67c077b40395.svg",width:16,height:16,offset:-5},anchorPoints:[[0,.5],[1,.5]]},shapeType:"modelRect",drawShape:function(t,e){var i=(this.mergeStyle||this.getOptions(t)).preRect,n=void 0===i?{}:i,r=this.getShapeStyle(t),a=this.getSize(t),s=a[0],c=a[1],h=e.addShape("rect",{attrs:r,className:"".concat(this.type,"-keyShape"),name:"".concat(this.type,"-keyShape"),draggable:!0});e["shapeMap"]["".concat(this.type,"-keyShape")]=h;var d=n.show,l=Object(o["e"])(n,["show"]);return d&&(e["shapeMap"]["pre-rect"]=e.addShape("rect",{attrs:Object(o["a"])({x:-s/2,y:-c/2,height:c},l),className:"pre-rect",name:"pre-rect",draggable:!0})),this.drawLogoIcon(t,e),this.drawStateIcon(t,e),this.drawLinkPoints(t,e),h},drawLogoIcon:function(t,e){var i=(this.mergeStyle||this.getOptions(t)).logoIcon,n=void 0===i?{}:i,r=this.getSize(t),a=r[0];if(n.show){var s=n.width,c=n.height,h=n.x,d=n.y,l=n.offset,p=n.text,u=Object(o["e"])(n,["width","height","x","y","offset","text"]);e["shapeMap"]["rect-logo-icon"]=p?e.addShape("text",{attrs:Object(o["a"])({x:0,y:0,fontSize:12,fill:"#000",stroke:"#000",textBaseline:"middle",textAlign:"center"},u),className:"rect-logo-icon",name:"rect-logo-icon",draggable:!0}):e.addShape("image",{attrs:Object(o["a"])(Object(o["a"])({},u),{x:h||-a/2+s+l,y:d||-c/2,width:s,height:c}),className:"rect-logo-icon",name:"rect-logo-icon",draggable:!0})}},drawStateIcon:function(t,e){var i=(this.mergeStyle||this.getOptions(t)).stateIcon,n=void 0===i?{}:i,r=this.getSize(t),a=r[0];if(n.show){var s=n.width,c=n.height,h=n.x,d=n.y,l=n.offset,p=n.text,u=Object(o["e"])(n,["width","height","x","y","offset","text"]);e["shapeMap"]["rect-state-icon"]=p?e.addShape("text",{attrs:Object(o["a"])({x:0,y:0,fontSize:12,fill:"#000",stroke:"#000",textBaseline:"middle",textAlign:"center"},u),className:"rect-state-icon",name:"rect-state-icon",draggable:!0}):e.addShape("image",{attrs:Object(o["a"])(Object(o["a"])({},u),{x:h||a/2-s+l,y:d||-c/2,width:s,height:c}),className:"rect-state-icon",name:"rect-state-icon",draggable:!0})}},drawLinkPoints:function(t,e){var i=(this.mergeStyle||this.getOptions(t)).linkPoints,n=void 0===i?{}:i,r=n.top,a=n.left,s=n.right,c=n.bottom,h=n.size,d=n.r,l=Object(o["e"])(n,["top","left","right","bottom","size","r"]),p=this.getSize(t),u=p[0],f=p[1];a&&(e["shapeMap"]["link-point-left"]=e.addShape("circle",{attrs:Object(o["a"])(Object(o["a"])({},l),{x:-u/2,y:0,r:h/2||d||5}),className:"link-point-left",name:"link-point-left",isAnchorPoint:!0})),s&&(e["shapeMap"]["link-point-right"]=e.addShape("circle",{attrs:Object(o["a"])(Object(o["a"])({},l),{x:u/2,y:0,r:h/2||d||5}),className:"link-point-right",name:"link-point-right",isAnchorPoint:!0})),r&&(e["shapeMap"]["link-point-top"]=e.addShape("circle",{attrs:Object(o["a"])(Object(o["a"])({},l),{x:0,y:-f/2,r:h/2||d||5}),className:"link-point-top",name:"link-point-top",isAnchorPoint:!0})),c&&(e["shapeMap"]["link-point-bottom"]=e.addShape("circle",{attrs:Object(o["a"])(Object(o["a"])({},l),{x:0,y:f/2,r:h/2||d||5}),className:"link-point-bottom",name:"link-point-bottom",isAnchorPoint:!0}))},drawLabel:function(t,e){var i=this.getOptions(t),n=i.labelCfg,a=void 0===n?{}:n,s=i.logoIcon,c=void 0===s?{}:s,h=i.descriptionCfg,d=void 0===h?{}:h,l=this.getSize(t),p=l[0],u=null,f=c.show,g=c.width,y=-p/2+a.offset;f&&(y=-p/2+g+a.offset);var m=a.style,b=d.style,v=d.paddingTop;return Object(r["isString"])(t.description)?(u=e.addShape("text",{attrs:Object(o["a"])(Object(o["a"])({},m),{x:y,y:-5,text:t.label}),className:"text-shape",name:"text-shape",draggable:!0,labelRelated:!0}),e["shapeMap"]["text-shape"]=u,e["shapeMap"]["rect-description"]=e.addShape("text",{attrs:Object(o["a"])(Object(o["a"])({},b),{x:y,y:17+(v||0),text:t.description}),className:"rect-description",name:"rect-description",draggable:!0,labelRelated:!0})):(u=e.addShape("text",{attrs:Object(o["a"])(Object(o["a"])({},m),{x:y,y:7,text:t.label}),className:"text-shape",name:"text-shape",draggable:!0,labelRelated:!0}),e["shapeMap"]["text-shape"]=u),u},getShapeStyle:function(t){var e=(this.mergeStyle||this.getOptions(t)).style,i={stroke:t.color},n=Object(r["mix"])({},e,i),a=this.getSize(t),s=n.width||a[0],c=n.height||a[1],h=Object(o["a"])({x:-s/2,y:-c/2,width:s,height:c},n);return h},update:function(t,e){var i=this.mergeStyle||this.getOptions(t),n=i.style,a=void 0===n?{}:n,s=i.labelCfg,c=void 0===s?{}:s,h=i.descriptionCfg,d=void 0===h?{}:h,l=this.getSize(t),p=l[0],u=l[1],f=e.get("keyShape");f.attr(Object(o["a"])(Object(o["a"])({},a),{x:-p/2,y:-u/2,width:p,height:u}));var g=e.getContainer(),y=g["shapeMap"]["rect-logo-icon"]||g.find((function(t){return"rect-logo-icon"===t.get("className")})),m=y?y.attr():{},b=Object(r["mix"])({},m,t.logoIcon),v=b.width;void 0===v&&(v=this.options.logoIcon.width);var x=t.logoIcon?t.logoIcon.show:void 0,O=c.offset,j=-p/2+v+O;x||void 0===x||(j=-p/2+O);var S=g["shapeMap"]["node-label"]||g.find((function(t){return"node-label"===t.get("className")})),M=g["shapeMap"]["rect-description"]||g.find((function(t){return"rect-description"===t.get("className")}));if(t.label)if(S){var k=t.labelCfg?t.labelCfg.style:{},w=Object(r["mix"])({},S.attr(),k);t.label&&(w.text=t.label),w.x=j,Object(r["isString"])(t.description)&&(w.y=-5),M&&(M.resetMatrix(),M.attr({x:j})),S.resetMatrix(),S.attr(w)}else g["shapeMap"]["node-label"]=g.addShape("text",{attrs:Object(o["a"])(Object(o["a"])({},c.style),{x:j,y:t.description?-5:7,text:t.label}),className:"node-label",name:"node-label",draggable:!0,labelRelated:!0});if(Object(r["isString"])(t.description)){var C=d.paddingTop;if(M){k=t.descriptionCfg?t.descriptionCfg.style:{};var P=Object(r["mix"])({},M.attr(),k);Object(r["isString"])(t.description)&&(P.text=t.description),P.x=j,M.resetMatrix(),M.attr(Object(o["a"])(Object(o["a"])({},P),{y:17+(C||0)}))}else g["shapeMap"]["rect-description"]=g.addShape("text",{attrs:Object(o["a"])(Object(o["a"])({},d.style),{x:j,y:17+(C||0),text:t.description}),className:"rect-description",name:"rect-description",draggable:!0,labelRelated:!0})}var I=g["shapeMap"]["pre-rect"]||g.find((function(t){return"pre-rect"===t.get("className")}));if(I&&!I.destroyed){var N=Object(r["mix"])({},I.attr(),t.preRect);I.attr(Object(o["a"])(Object(o["a"])({},N),{x:-p/2,y:-u/2,height:u}))}if(y&&!y.destroyed)if(x||void 0===x){var B=b.width,T=b.height,E=b.x,Y=b.y,X=b.offset,z=Object(o["e"])(b,["width","height","x","y","offset"]);y.attr(Object(o["a"])(Object(o["a"])({},z),{x:E||-p/2+B+X,y:Y||-T/2,width:B,height:T}))}else y.remove(),delete g["shapeMap"]["pre-rect"];else x&&this.drawLogoIcon(t,g);var L=g["shapeMap"]["rect-state-icon"]||g.find((function(t){return"rect-state-icon"===t.get("className")})),A=L?L.attr():{},F=Object(r["mix"])({},A,t.stateIcon);if(L){F.show||void 0===F.show||(L.remove(),delete g["shapeMap"]["rect-state-icon"]);var V=F.width,D=(T=F.height,E=F.x,Y=F.y,F.offset),W=Object(o["e"])(F,["width","height","x","y","offset"]);L.attr(Object(o["a"])(Object(o["a"])({},W),{x:E||p/2-V+D,y:Y||-T/2,width:V,height:T}))}else F.show&&this.drawStateIcon(t,g);this.updateLinkPoints(t,g)}},"single-node"),Object(n["l"])("star",{options:{size:60,style:{stroke:n["e"].defaultNode.style.stroke,fill:n["e"].defaultNode.style.fill,lineWidth:n["e"].defaultNode.style.lineWidth},labelCfg:{style:{fill:n["e"].nodeLabel.style.fill,fontSize:n["e"].nodeLabel.style.fontSize,fontFamily:n["e"].windowFontFamily}},linkPoints:{top:!1,right:!1,bottom:!1,left:!1,size:n["e"].defaultNode.linkPoints.size,lineWidth:n["e"].defaultNode.linkPoints.lineWidth,fill:n["e"].defaultNode.linkPoints.fill,stroke:n["e"].defaultNode.linkPoints.stroke},icon:{show:!1,img:"https://gw.alipayobjects.com/zos/bmw-prod/5d015065-8505-4e7a-baec-976f81e3c41d.svg",width:20,height:20},stateStyles:Object(o["a"])({},n["e"].nodeStateStyles)},shapeType:"star",labelPosition:"center",drawShape:function(t,e){var i=(this.mergeStyle||this.getOptions(t)).icon,n=void 0===i?{}:i,r=this.getShapeStyle(t),a=e.addShape("path",{attrs:r,className:"".concat(this.type,"-keyShape"),name:"".concat(this.type,"-keyShape"),draggable:!0});e["shapeMap"]["".concat(this.type,"-keyShape")]=a;var s=n.width,c=n.height,h=n.show,d=n.text;return h&&(e["shapeMap"]["".concat(this.type,"-icon")]=d?e.addShape("text",{attrs:Object(o["a"])({x:0,y:0,fontSize:12,fill:"#000",stroke:"#000",textBaseline:"middle",textAlign:"center"},n),className:"".concat(this.type,"-icon"),name:"".concat(this.type,"-icon"),draggable:!0}):e.addShape("image",{attrs:Object(o["a"])({x:-s/2,y:-c/2},n),className:"".concat(this.type,"-icon"),name:"".concat(this.type,"-icon"),draggable:!0})),this.drawLinkPoints(t,e),a},drawLinkPoints:function(t,e){var i=(this.mergeStyle||this.getOptions(t)).linkPoints,n=void 0===i?{}:i,r=n.top,a=n.left,s=n.right,c=n.leftBottom,h=n.rightBottom,d=n.size,l=n.r,p=Object(o["e"])(n,["top","left","right","leftBottom","rightBottom","size","r"]),u=this.getSize(t),f=u[0];if(s){var g=Math.cos(.1*Math.PI)*f,y=Math.sin(.1*Math.PI)*f;e["shapeMap"]["link-point-right"]=e.addShape("circle",{attrs:Object(o["a"])(Object(o["a"])({},p),{x:g,y:-y,r:d/2||l||5}),className:"link-point-right",name:"link-point-right"})}if(r){g=Math.cos(.5*Math.PI)*f,y=Math.sin(.5*Math.PI)*f;e["shapeMap"]["link-point-top"]=e.addShape("circle",{attrs:Object(o["a"])(Object(o["a"])({},p),{x:g,y:-y,r:d/2||l||5}),className:"link-point-top",name:"link-point-top"})}if(a){g=Math.cos(.9*Math.PI)*f,y=Math.sin(.9*Math.PI)*f;e["shapeMap"]["link-point-left"]=e.addShape("circle",{attrs:Object(o["a"])(Object(o["a"])({},p),{x:g,y:-y,r:d/2||l||5}),className:"link-point-left",name:"link-point-left"})}if(c){g=Math.cos(1.3*Math.PI)*f,y=Math.sin(1.3*Math.PI)*f;e["shapeMap"]["link-point-bottom"]=e.addShape("circle",{attrs:Object(o["a"])(Object(o["a"])({},p),{x:g,y:-y,r:d/2||l||5}),className:"link-point-left-bottom",name:"link-point-left-bottom"})}if(h){g=Math.cos(1.7*Math.PI)*f,y=Math.sin(1.7*Math.PI)*f;e["shapeMap"]["link-point-right-bottom"]=e.addShape("circle",{attrs:Object(o["a"])(Object(o["a"])({},p),{x:g,y:-y,r:d/2||l||5}),className:"link-point-right-bottom",name:"link-point-right-bottom"})}},getPath:function(t){for(var e=this.getSize(t),i=e[0],o=3*i/8,n=t.innerR||o,r=[],a=0;a<5;a++){var s=Math.cos((18+72*a)/180*Math.PI)*i,c=Math.sin((18+72*a)/180*Math.PI)*i,h=Math.cos((54+72*a)/180*Math.PI)*n,d=Math.sin((54+72*a)/180*Math.PI)*n;0===a?r.push(["M",s,-c]):r.push(["L",s,-c]),r.push(["L",h,-d])}return r.push(["Z"]),r},getShapeStyle:function(t){var e=(this.mergeStyle||this.getOptions(t)).style,i={stroke:t.color},n=Object(r["mix"])({},e,i),a=this.getPath(t),s=Object(o["a"])({path:a},n);return s},update:function(t,e,i){var o=e.getContainer(),n=this.getOptions({}).style,a=this.getPath(t),s={stroke:t.color,path:a},c=e.get("keyShape"),h=Object(r["mix"])({},n,c.attr(),s);h=Object(r["mix"])(h,t.style),this.updateShape(t,e,h,!0,i),this.updateLinkPoints(t,o)},updateLinkPoints:function(t,e){var i=this.getOptions({}).linkPoints,n=e["shapeMap"]["link-point-left"]||e.find((function(t){return"link-point-left"===t.get("className")})),a=e["shapeMap"]["link-point-right"]||e.find((function(t){return"link-point-right"===t.get("className")})),s=e["shapeMap"]["link-point-top"]||e.find((function(t){return"link-point-top"===t.get("className")})),c=e["shapeMap"]["link-point-left-bottom"]||e.find((function(t){return"link-point-left-bottom"===t.get("className")})),h=e["shapeMap"]["link-point-left-bottom"]||e.find((function(t){return"link-point-right-bottom"===t.get("className")})),d=i,l=n||a||s||c||h;l&&(d=l.attr());var p=Object(r["mix"])({},d,t.linkPoints),u=p.fill,f=p.stroke,g=p.lineWidth,y=p.size/2;y||(y=p.r);var m=t.linkPoints?t.linkPoints:{left:void 0,right:void 0,top:void 0,leftBottom:void 0,rightBottom:void 0},b=m.left,v=m.right,x=m.top,O=m.leftBottom,j=m.rightBottom,S=this.getSize(t),M=S[0],k={r:y,fill:u,stroke:f,lineWidth:g},w=Math.cos(.1*Math.PI)*M,C=Math.sin(.1*Math.PI)*M;a?v||void 0===v?a.attr(Object(o["a"])(Object(o["a"])({},k),{x:w,y:-C})):(a.remove(),delete e["shapeMap"]["link-point-right"]):v&&(e["shapeMap"]["link-point-right"]=e.addShape("circle",{attrs:Object(o["a"])(Object(o["a"])({},k),{x:w,y:-C}),className:"link-point-right",name:"link-point-right",isAnchorPoint:!0})),w=Math.cos(.5*Math.PI)*M,C=Math.sin(.5*Math.PI)*M,s?x||void 0===x?s.attr(Object(o["a"])(Object(o["a"])({},k),{x:w,y:-C})):(s.remove(),delete e["shapeMap"]["link-point-top"]):x&&(e["shapeMap"]["link-point-top"]=e.addShape("circle",{attrs:Object(o["a"])(Object(o["a"])({},k),{x:w,y:-C}),className:"link-point-top",name:"link-point-top",isAnchorPoint:!0})),w=Math.cos(.9*Math.PI)*M,C=Math.sin(.9*Math.PI)*M,n?b||void 0===b?n.attr(Object(o["a"])(Object(o["a"])({},k),{x:w,y:-C})):(n.remove(),delete e["shapeMap"]["link-point-left"]):b&&(e["shapeMap"]["link-point-left"]=e.addShape("circle",{attrs:Object(o["a"])(Object(o["a"])({},k),{x:w,y:-C}),className:"link-point-left",name:"link-point-left",isAnchorPoint:!0})),w=Math.cos(1.3*Math.PI)*M,C=Math.sin(1.3*Math.PI)*M,c?O||void 0===O?c.attr(Object(o["a"])(Object(o["a"])({},k),{x:w,y:-C})):(c.remove(),delete e["shapeMap"]["link-point-left-bottom"]):O&&(e["shapeMap"]["link-point-left-bottom"]=e.addShape("circle",{attrs:Object(o["a"])(Object(o["a"])({},k),{x:w,y:-C}),className:"link-point-left-bottom",name:"link-point-left-bottom",isAnchorPoint:!0})),w=Math.cos(1.7*Math.PI)*M,C=Math.sin(1.7*Math.PI)*M,h?j||void 0===j?h.attr(Object(o["a"])(Object(o["a"])({},k),{x:w,y:-C})):(h.remove(),delete e["shapeMap"]["link-point-right-bottom"]):j&&(e["shapeMap"]["link-point-right-bottom"]=e.addShape("circle",{attrs:Object(o["a"])(Object(o["a"])({},k),{x:w,y:-C}),className:"link-point-right-bottom",name:"link-point-right-bottom",isAnchorPoint:!0}))}},"single-node");var a=n["h"].defaultSubjectColors;Object(n["l"])("donut",{options:{size:n["e"].defaultNode.size,style:{x:0,y:0,stroke:n["e"].defaultNode.style.stroke,fill:n["e"].defaultNode.style.fill,lineWidth:n["e"].defaultNode.style.lineWidth},labelCfg:{style:{fill:n["e"].nodeLabel.style.fill,fontSize:n["e"].nodeLabel.style.fontSize,fontFamily:n["e"].windowFontFamily}},linkPoints:{top:!1,right:!1,bottom:!1,left:!1,size:n["e"].defaultNode.linkPoints.size,lineWidth:n["e"].defaultNode.linkPoints.lineWidth,fill:n["e"].defaultNode.linkPoints.fill,stroke:n["e"].defaultNode.linkPoints.stroke},icon:{show:!1,img:"https://gw.alipayobjects.com/zos/bmw-prod/5d015065-8505-4e7a-baec-976f81e3c41d.svg",width:20,height:20},stateStyles:Object(o["a"])({},n["e"].nodeStateStyles)},shapeType:"circle",labelPosition:"center",drawShape:function(t,e){var i=(this.mergeStyle||this.getOptions(t)).icon,n=void 0===i?{}:i,s=this.getShapeStyle(t),c=Object(r["deepMix"])({},n,t.icon),h=e.addShape("circle",{attrs:s,className:"".concat(this.type,"-keyShape"),draggable:!0,name:"".concat(this.type,"-keyShape")});e["shapeMap"]["".concat(this.type,"-keyShape")]=h;var d=c.width,l=c.height,p=c.show,u=c.text;p&&(e["shapeMap"]["".concat(this.type,"-icon")]=u?e.addShape("text",{attrs:Object(o["a"])({x:0,y:0,fontSize:12,fill:"#000",stroke:"#000",textBaseline:"middle",textAlign:"center"},c),className:"".concat(this.type,"-icon"),name:"".concat(this.type,"-icon"),draggable:!0}):e.addShape("image",{attrs:Object(o["a"])({x:-d/2,y:-l/2},c),className:"".concat(this.type,"-icon"),name:"".concat(this.type,"-icon"),draggable:!0}));var f=h.attr("r"),g=.6*f,y=(f+g)/2,m=t,b=m.donutAttrs,v=void 0===b?{}:b,x=m.donutColorMap,O=void 0===x?{}:x,j=Object.keys(v).length;if(v&&j>1){var S=[],M=0;if(Object.keys(v).forEach((function(t){var e=v[t]||0;Object(r["isNumber"])(e)&&(S.push({key:t,value:e,color:O[t]}),M+=e)})),M){var k=f-g;if(1===j)return void(e["shapeMap"]["fan-shape-0"]=e.addShape("circle",{attrs:{r:y,x:0,y:0,stroke:S[0].color||a[0],lineWidth:k},name:"fan-shape-0",draggable:!0}));var w=[y,0],C=0;S.forEach((function(t,i){var o=t.value/M;if(!(o<.001))if(o>.999&&(o=1),1!==o){t.percent=o,t.angle=o*Math.PI*2,t.beginAgnle=C,C+=t.angle,t.endAngle=C,t.arcBegin=w,t.arcEnd=[y*Math.cos(t.endAngle),-y*Math.sin(t.endAngle)];var n=t.angle>Math.PI?1:0,r=[["M",t.arcBegin[0],t.arcBegin[1]],["A",y,y,0,n,0,t.arcEnd[0],t.arcEnd[1]],["L",t.arcEnd[0],t.arcEnd[1]]];e["shapeMap"]["fan-shape-".concat(i)]=e.addShape("path",{attrs:{path:r,lineWidth:k,stroke:t.color||a[i%a.length]},name:"fan-shape-".concat(i),draggable:!0}),w=t.arcEnd}else e["shapeMap"]["fan-shape-".concat(i)]=e.addShape("circle",{attrs:{r:y,x:0,y:0,stroke:t.color||a[i%a.length],lineWidth:k},name:"fan-shape-".concat(i),draggable:!0})}))}}return this.drawLinkPoints(t,e),h},update:void 0},"circle");var s=function(t){var e=t.x,i=t.y;return{x:e,y:i,centerX:e,centerY:i,minX:e,minY:i,maxX:e,maxY:i,height:0,width:0}},c=function(t){void 0===t&&(t=[]);var e=[],i=[];t.forEach((function(t){e.push(t.x),i.push(t.y)}));var o=Math.min.apply(Math,e),n=Math.max.apply(Math,e),r=Math.min.apply(Math,i),a=Math.max.apply(Math,i);return{centerX:(o+n)/2,centerY:(r+a)/2,maxX:n,maxY:a,minX:o,minY:r,height:a-r,width:n-o}},h=function(t){for(var e=[],i={},o=t.length,n=o-1;n>=0;n--){var r=t[n];r.id="".concat(r.x,"|||").concat(r.y),i[r.id]=r,e.push(r)}return e},d=function(t){return h(t)},l=function(t,e){return t.width||t.height?{centerX:t.centerX,centerY:t.centerY,minX:t.minX-e,minY:t.minY-e,maxX:t.maxX+e,maxY:t.maxY+e,height:t.height+2*e,width:t.width+2*e}:t},p=function(t,e){var i=Math.abs(t.x-e.centerX),o=Math.abs(t.y-e.centerY);return 0===i&&0===o?0:i/e.width>o/e.height},u=function(t,e,i){var o=p(e,t);if(0===o){var n=t.centerX,r=t.centerY;return i.y<e.y?r=t.minY:i.x>e.x?n=t.maxX:i.x<e.x?n=t.minX:i.x===e.x&&(r=t.maxY),{x:n,y:r}}return o?{x:e.x>t.centerX?t.maxX:t.minX,y:e.y}:{x:e.x,y:e.y>t.centerY?t.maxY:t.minY}},f=function(t,e){var i=Math.min(t.minX,e.minX),o=Math.min(t.minY,e.minY),n=Math.max(t.maxX,e.maxX),r=Math.max(t.maxY,e.maxY);return{centerX:(i+n)/2,centerY:(o+r)/2,minX:i,minY:o,maxX:n,maxY:r,height:r-o,width:n-i}},g=function(t){return[{x:t.minX,y:t.minY},{x:t.maxX,y:t.minY},{x:t.maxX,y:t.maxY},{x:t.minX,y:t.maxY}]},y=function(t,e){var i=t.x,o=t.y;return i<e.minX||i>e.maxX||o<e.minY||o>e.maxY},m=function(t,e){return e<t.minX||e>t.maxX?[]:[{x:e,y:t.minY},{x:e,y:t.maxY}]},b=function(t,e){return e<t.minY||e>t.maxY?[]:[{x:t.minX,y:e},{x:t.maxX,y:e}]},v=function(t,e){return m(t,e.x).concat(b(t,e.y))},x=function(t,e){return Math.abs(t.x-e.x)+Math.abs(t.y-e.y)},O=function(t,e){var i=-2,o=0;return e.forEach((function(e){e&&(t.x===e.x&&(o+=i),t.y===e.y&&(o+=i))})),o},j=function(t,e,i,o,n){return x(t,e)+x(t,i)+O(t,[e,i,o,n])},S=function t(e,i,o,n,r){void 0===r&&(r=0),e.unshift(i[n]),o[n]&&o[n]!==n&&r<=100&&t(e,i,o,o[n],r+1)},M=function(t,e){var i=t.indexOf(e);i>-1&&t.splice(i,1)},k=function(t,e,i,o){var n=i.x-t.x,r=i.y-t.y,a=o.x-t.x,s=o.y-t.y,c=i.x-e.x,h=i.y-e.y,d=o.x-e.x,l=o.y-e.y,p=n*s-r*a,u=c*l-h*d,f=n*h-r*c,g=a*l-s*d;return p*u<=0&&f*g<=0},w=function(t,e,i){if(i.width||i.height){var o=g(i),n=o[0],r=o[1],a=o[2],s=o[3];return k(t,e,n,r)||k(t,e,n,s)||k(t,e,r,a)||k(t,e,a,s)}return!1},C=function(t,e,i,o){var n=[];return t.forEach((function(t){if(t!==e&&(t.x===e.x||t.y===e.y)){if(w(t,e,i)||w(t,e,o))return;n.push(t)}})),h(n)},P=function(t,e,i,o,n,r,a){var s=[],c=[e],h={},d={},l={};d[e.id]=0,l[e.id]=j(e,i,e);var p,u,f={};t.forEach((function(t){f[t.id]=t}));while(c.length){if(p=void 0,u=1/0,c.forEach((function(t){l[t.id]<=u&&(u=l[t.id],p=t)})),p===i){var g=[];return S(g,f,h,i.id),g}M(c,p),s.push(p),C(t,p,o,n).forEach((function(t){if(-1===s.indexOf(t)){-1===c.indexOf(t)&&c.push(t);var o=l[p.id]+x(p,t);d[t.id]&&o>=d[t.id]||(h[t.id]=p.id,d[t.id]=o,l[t.id]=d[t.id]+j(t,i,e,r,a))}}))}return[e,i]},I=function(t,e,i){return!(t.x===e.x&&e.x===i.x||t.y===e.y&&e.y===i.y)},N=function(t,e,i,o){var n=x(t,e),r=x(i,e);n<o&&(o=n),r<o&&(o=r);var a={x:e.x-o/n*(e.x-t.x),y:e.y-o/n*(e.y-t.y)},s={x:e.x-o/r*(e.x-i.x),y:e.y-o/r*(e.y-i.y)};return[a,s]},B=function(t,e){var i=[],o=t[0];return i.push("M".concat(o.x," ").concat(o.y)),t.forEach((function(o,n){var r=t[n+1],a=t[n+2];if(r&&a)if(I(o,r,a)){var s=N(o,r,a,e),c=s[0],h=s[1];i.push("L".concat(c.x," ").concat(c.y)),i.push("Q".concat(r.x," ").concat(r.y," ").concat(h.x," ").concat(h.y)),i.push("L".concat(h.x," ").concat(h.y))}else i.push("L".concat(r.x," ").concat(r.y));else r&&i.push("L".concat(r.x," ").concat(r.y))})),i.join("")},T=function(t,e,i,o,n){var r,a;if(i&&i.getType())if("combo"===i.getType()){var p=i.getKeyShape();r=p.getCanvasBBox()||s(t),r.centerX=(r.minX+r.maxX)/2,r.centerY=(r.minY+r.maxY)/2}else r=i.getBBox();else r=s(t);if(o&&o.getType())if("combo"===o.getType()){var m=o.getKeyShape().getBBox();if(m){var b=o.getModel(),x=b.x,O=b.y;a={x:x,y:O,width:m.width,height:m.height,minX:m.minX+x,maxX:m.maxX+x,minY:m.minY+O,maxY:m.maxY+O},a.centerX=(a.minX+a.maxX)/2,a.centerY=(a.minY+a.maxY)/2}else a=s(e)}else a=o&&o.getBBox();else a=s(e);var j=l(r,n),S=l(a,n),M=u(j,t,e),k=u(S,e,t),w=c([M,k]),C=f(j,w),I=f(S,w),N=[];N=N.concat(g(C)).concat(g(I));var B={x:(t.x+e.x)/2,y:(t.y+e.y)/2};[w,C,I].forEach((function(t){N=N.concat(v(t,B).filter((function(t){return y(t,j)&&y(t,S)})))})),[{x:M.x,y:k.y},{x:k.x,y:M.y}].forEach((function(t){y(t,j)&&y(t,S)&&N.push(t)})),N.unshift(M),N.push(k),N=h(N);var T=P(N,M,k,r,a,t,e);return T.unshift(t),T.push(e),d(T)},E=function(t,e){return Math.abs(t.x-e.x)+Math.abs(t.y-e.y)},Y=function(t,e){return Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2))},X=function(t,e){return[t,e]},z=function(t,e,i,o,n){return d(T(t,e,i,o,n.offset))},L={offset:20,maxAllowedDirectionChange:Math.PI/2,maximumLoops:2e3,gridSize:10,directions:[{stepX:1,stepY:0},{stepX:-1,stepY:0},{stepX:0,stepY:1},{stepX:0,stepY:-1}],get penalties(){return{0:0,45:this.gridSize/2,90:this.gridSize/2}},distFunc:E,fallbackRoute:z},A=(Math.PI,function(t,e){var i=Math.round(Math.abs(t/e)),o=t<0?-1:1;return i<0?0:o*i}),F=function(t,e,i){var o={};return t.forEach((function(t){if(t)for(var n=l(t.getBBox(),i),r=A(n.minX,e);r<=A(n.maxX,e);r+=1)for(var a=A(n.minY,e);a<=A(n.maxY,e);a+=1)o["".concat(r,"|||").concat(a)]=!0})),o},V=function(t,e){var i=e.x-t.x,o=e.y-t.y;return i||o?Math.atan2(o,i):0},D=function(t,e){var i=Math.abs(t-e);return i>Math.PI?2*Math.PI-i:i},W=function(t,e,i){for(var o=1/0,n=0,r=e.length;n<r;n++){var a=i(t,e[n]);a<o&&(o=a)}return o},G=function(t,e,i,o,r){var a=[];if(!i)return[t];var s=r.directions,c=r.offset,h=i.getBBox(),d=e.x>h.minX&&e.x<h.maxX&&e.y>h.minY&&e.y<h.maxY,p=l(h,c);for(var f in p)p[f]=A(p[f],r.gridSize);if(d){for(var g=0,y=s;g<y.length;g++){var m=y[g],b=[[{x:p.minX,y:p.minY},{x:p.maxX,y:p.minY}],[{x:p.minX,y:p.minY},{x:p.minX,y:p.maxY}],[{x:p.maxX,y:p.minY},{x:p.maxX,y:p.maxY}],[{x:p.minX,y:p.maxY},{x:p.maxX,y:p.maxY}]];for(f=0;f<4;f++){var v=b[f],x=n["h"].getLineIntersect(t,{x:t.x+m.stepX*p.width,y:t.y+m.stepY*p.height},v[0],v[1]);x&&!w(t,x,h)&&(x.id="".concat(x.x,"|||").concat(x.y),a.push(x))}}return a}var O=u(p,t,o);return O.id="".concat(O.x,"|||").concat(O.y),[O]},R=function(t,e,i,o){var n=V(t,e);if(!i[t.id]){var r=V(o,t);return D(r,n)}var a=V({x:i[t.id].x,y:i[t.id].y},t);return D(a,n)},U=function(t,e,i,o,n,r,a){var s=[o],c=t.id,h=t.x,d=t.y,l={x:h,y:d,id:c};R(l,r,e,i)&&s.unshift({x:r.x===o.x?o.x:l.x*a,y:r.y===o.y?o.y:l.y*a});while(e[c]&&e[c].id!==c){var p={x:h,y:d,id:c},u=e[c].id,f=e[c].x,g=e[c].y,y={x:f,y:g,id:u},m=R(y,p,e,i);m&&s.unshift({x:y.x===p.x?s[0].x:y.x*a,y:y.y===p.y?s[0].y:y.y*a}),c=u,h=f,d=g}var b={x:h,y:d,id:c};return s[0].x=b.x===i.x?n.x:s[0].x,s[0].y=b.y===i.y?n.y:s[0].y,s.unshift(n),s},Z=function(t,e,i,o,n){if(isNaN(t.x)||isNaN(e.x))return[];var a=Object(r["deepMix"])(L,n);a.obstacles=a.obstacles||[];var s=a.gridSize,c=F(a.obstacles.concat([i,o]),s,a.offset),h={x:A(t.x,s),y:A(t.y,s)},d={x:A(e.x,s),y:A(e.y,s)};t.id="".concat(h.x,"|||").concat(h.y),e.id="".concat(d.x,"|||").concat(d.y);var l=G(h,t,i,d,a),p=G(d,e,o,h,a);l.forEach((function(t){delete c[t.id]})),p.forEach((function(t){delete c[t.id]}));for(var u={},f={},g={},y={},m={},b=0;b<l.length;b++){var v=l[b];u[v.id]=v,y[v.id]=0,m[v.id]=W(v,p,a.distFunc)}var x,O,j,S,M,k,w,C=a.maximumLoops,P=a.penalties;while(Object.keys(u).length>0&&C>0){if(x=void 0,O=1/0,Object.keys(u).forEach((function(t){var e=u[t].id;m[e]<=O&&(O=m[e],x=u[e])})),!x)break;if(p.findIndex((function(t){return t.x===x.x&&t.y===x.y}))>-1)return U(x,g,h,e,t,d,s);delete u[x.id],f[x.id]=!0;for(b=0;b<a.directions.length;b++)j=a.directions[b],S={x:x.x+j.stepX,y:x.y+j.stepY,id:"".concat(Math.round(x.x)+j.stepX,"|||").concat(Math.round(x.y)+j.stepY)},f[S.id]||(w=R(x,S,g,h),w>a.maxAllowedDirectionChange||c[S.id]||(u[S.id]||(u[S.id]=S),M=a.distFunc(x,S)+(isNaN(P[w])?s:P[w]),k=y[x.id]+M,y[S.id]&&k>=y[S.id]||(g[S.id]=x,y[S.id]=k,m[S.id]=k+W(S,p,a.distFunc))));C-=1}return a.fallbackRoute(t,e,i,o,a)};Object(n["k"])("polyline",{options:{color:n["e"].defaultEdge.color,size:n["e"].defaultEdge.size,style:{radius:0,offset:15,x:0,y:0,stroke:n["e"].defaultEdge.style.stroke,lineAppendWidth:n["e"].defaultEdge.style.lineAppendWidth},labelCfg:{style:{fill:n["e"].edgeLabel.style.fill,fontSize:n["e"].edgeLabel.style.fontSize,fontFamily:n["e"].windowFontFamily}},routeCfg:{obstacles:[],maxAllowedDirectionChange:Math.PI,maximumLoops:500,gridSize:10},stateStyles:Object(o["a"])({},n["e"].edgeStateStyles)},shapeType:"polyline",labelPosition:"center",drawShape:function(t,e){var i=this.getShapeStyle(t);0===i.radius&&delete i.radius;var o=e.addShape("path",{className:"edge-shape",name:"edge-shape",attrs:i});return e["shapeMap"]["edge-shape"]=o,o},getShapeStyle:function(t){var e=this.options.style,i={stroke:t.color},o=Object(r["mix"])({},e,i,t.style);t=this.getPathPoints(t),this.radius=o.radius,this.offset=o.offset;var a=t.startPoint,s=t.endPoint,c=this.getControlPoints(t),h=[a];c&&(h=h.concat(c)),h.push(s);var d=t.sourceNode,l=t.targetNode,p=o.radius,u=this.options.routeCfg,f=Object(r["mix"])({},u,t.routeCfg);f.offset=o.offset;var g=this.getPath(h,d,l,p,f);(Object(r["isArray"])(g)&&g.length<=1||Object(r["isString"])(g)&&-1===g.indexOf("L"))&&(g="M0 0, L0 0"),(isNaN(a.x)||isNaN(a.y)||isNaN(s.x)||isNaN(s.y))&&(g="M0 0, L0 0");var y=Object(r["mix"])({},n["e"].defaultEdge.style,o,{lineWidth:t.size,path:g});return y},updateShapeStyle:function(t,e){var i=e.getContainer();if(e.isVisible()){var o={stroke:t.color},n=i["shapeMap"]["edge-shape"]||i.find((function(t){return"edge-shape"===t.get("className")}))||e.getKeyShape(),a=t.size;t=this.getPathPoints(t);var s=t.startPoint,c=t.endPoint,h=this.getControlPoints(t),d=[s];h&&(d=d.concat(h)),d.push(c);var l=n.attr(),p=Object(r["mix"])({},o,l,t.style),u=t.sourceNode,f=t.targetNode,g=p.radius,y=this.options.routeCfg,m=Object(r["mix"])({},y,t.routeCfg);m.offset=p.offset;var b=this.getPath(d,u,f,g,m);(Object(r["isArray"])(b)&&b.length<=1||Object(r["isString"])(b)&&-1===b.indexOf("L"))&&(b="M0 0, L0 0"),(isNaN(s.x)||isNaN(s.y)||isNaN(c.x)||isNaN(c.y))&&(b="M0 0, L0 0"),l.endArrow&&!1===p.endArrow&&(t.style.endArrow={path:""}),l.startArrow&&!1===p.startArrow&&(t.style.startArrow={path:""});var v=Object(r["mix"])(o,n.attr(),{lineWidth:a,path:b},t.style);n&&n.attr(v)}},getPath:function(t,e,i,o,a){var s=a.offset,c=a.simple;if(!s||t.length>2){if(o)return B(t,o);var h=[];return Object(r["each"])(t,(function(t,e){0===e?h.push(["M",t.x,t.y]):h.push(["L",t.x,t.y])})),h}var d=c?T(t[t.length-1],t[0],i,e,s):Z(t[0],t[t.length-1],e,i,a);if(!d||!d.length)return"M0 0, L0 0";if(o){var l=B(d,o);return l}var p=n["h"].pointsToPolygon(d);return p}},"single-edge")},2974:function(t,e,i){"use strict";i.r(e),i.d(e,"getBBox",(function(){return f})),i.d(e,"getLoopCfgs",(function(){return g})),i.d(e,"getLabelPosition",(function(){return y})),i.d(e,"traverseTree",(function(){return v})),i.d(e,"traverseTreeUp",(function(){return x})),i.d(e,"getLetterWidth",(function(){return O})),i.d(e,"getTextSize",(function(){return j})),i.d(e,"truncateLabelByLength",(function(){return S})),i.d(e,"plainCombosToTrees",(function(){return M})),i.d(e,"reconstructTree",(function(){return k})),i.d(e,"getComboBBox",(function(){return w})),i.d(e,"shouldRefreshEdge",(function(){return C})),i.d(e,"cloneBesidesImg",(function(){return P}));var o=i("6edb"),n=i("e897"),r=i("2e0c"),a=i("adc7"),s={" ":.3329986572265625,a:.5589996337890625,A:.6569992065429687,b:.58599853515625,B:.6769989013671875,c:.5469985961914062,C:.7279998779296875,d:.58599853515625,D:.705999755859375,e:.554998779296875,E:.63699951171875,f:.37299957275390627,F:.5769989013671875,g:.5909988403320312,G:.7479995727539063,h:.555999755859375,H:.7199996948242188,i:.255999755859375,I:.23699951171875,j:.26699981689453123,J:.5169998168945312,k:.5289993286132812,K:.6899993896484375,l:.23499908447265624,L:.5879989624023437,m:.854998779296875,M:.8819992065429687,n:.5589996337890625,N:.7189987182617188,o:.58599853515625,O:.7669998168945312,p:.58599853515625,P:.6419998168945312,q:.58599853515625,Q:.7669998168945312,r:.3649993896484375,R:.6759994506835938,s:.504998779296875,S:.6319992065429687,t:.354998779296875,T:.6189987182617187,u:.5599990844726562,U:.7139999389648437,v:.48199920654296874,V:.6389999389648438,w:.754998779296875,W:.929998779296875,x:.5089996337890625,X:.63699951171875,y:.4959991455078125,Y:.66199951171875,z:.48699951171875,Z:.6239990234375,0:.6,1:.40099945068359377,2:.6,3:.6,4:.6,5:.6,6:.6,7:.5469985961914062,8:.6,9:.6,"[":.3329986572265625,"]":.3329986572265625,",":.26399993896484375,".":.26399993896484375,";":.26399993896484375,":":.26399993896484375,"{":.3329986572265625,"}":.3329986572265625,"\\":.5,"|":.19499969482421875,"=":.604998779296875,"+":.604998779296875,"-":.604998779296875,_:.5,"`":.3329986572265625," ~":.8329986572265625,"!":.3329986572265625,"@":.8579986572265625,"#":.6,$:.6,"%":.9699996948242188,"^":.517999267578125,"&":.7259994506835937,"*":.505999755859375,"(":.3329986572265625,")":.3329986572265625,"<":.604998779296875,">":.604998779296875,"/":.5,"?":.53699951171875},c=i("8937"),h=Math.PI,d=Math.sin,l=Math.cos,p=d(h/8),u=l(h/8),f=function(t,e){var i=t.getBBox(),o={x:i.minX,y:i.minY},n={x:i.maxX,y:i.maxY};if(e){var r=e.getMatrix();r||(r=[1,0,0,0,1,0,0,0,1]),o=Object(a["applyMatrix"])(o,r),n=Object(a["applyMatrix"])(n,r)}var s=o.x,c=o.y,h=n.x,d=n.y;return{x:s,y:c,minX:s,minY:c,maxX:h,maxY:d,width:h-s,height:d-c}},g=function(t){var e=t.sourceNode||t.targetNode,i=e.get("group"),o=i.getMatrix();o||(o=[1,0,0,0,1,0,0,0,1]);var a=e.getKeyShape(),s=a.getBBox(),c=t.loopCfg||{},h=c.dist||2*Math.max(s.width,s.height),d=c.position||r["a"].defaultLoopPosition,l=[o[6],o[7]],f=[t.startPoint.x,t.startPoint.y],g=[t.endPoint.x,t.endPoint.y],y=s.height/2,m=s.height/2,b=y*p,v=y*u,x=m*p,O=m*u;if(f[0]===g[0]&&f[1]===g[1]){switch(d){case"top":f=[l[0]-b,l[1]-v],g=[l[0]+x,l[1]-O];break;case"top-right":y=s.height/2,m=s.width/2,b=y*p,v=y*u,x=m*p,O=m*u,f=[l[0]+b,l[1]-v],g=[l[0]+O,l[1]-x];break;case"right":y=s.width/2,m=s.width/2,b=y*p,v=y*u,x=m*p,O=m*u,f=[l[0]+v,l[1]-b],g=[l[0]+O,l[1]+x];break;case"bottom-right":y=s.width/2,m=s.height/2,b=y*p,v=y*u,x=m*p,O=m*u,f=[l[0]+v,l[1]+b],g=[l[0]+x,l[1]+O];break;case"bottom":y=s.height/2,m=s.height/2,b=y*p,v=y*u,x=m*p,O=m*u,f=[l[0]+b,l[1]+v],g=[l[0]-x,l[1]+O];break;case"bottom-left":y=s.height/2,m=s.width/2,b=y*p,v=y*u,x=m*p,O=m*u,f=[l[0]-b,l[1]+v],g=[l[0]-O,l[1]+x];break;case"left":y=s.width/2,m=s.width/2,b=y*p,v=y*u,x=m*p,O=m*u,f=[l[0]-v,l[1]+b],g=[l[0]-O,l[1]-x];break;case"top-left":y=s.width/2,m=s.height/2,b=y*p,v=y*u,x=m*p,O=m*u,f=[l[0]-v,l[1]-b],g=[l[0]-x,l[1]-O];break;default:y=s.width/2,m=s.width/2,b=y*p,v=y*u,x=m*p,O=m*u,f=[l[0]-b,l[1]-v],g=[l[0]+x,l[1]-O]}if(!1===c.clockwise){var j=[f[0],f[1]];f=[g[0],g[1]],g=[j[0],j[1]]}}var S=[f[0]-l[0],f[1]-l[1]],M=(y+h)/y,k=(m+h)/m;!1===c.clockwise&&(M=(m+h)/m,k=(y+h)/y);var w=n["c"].scale([0,0],S,M),C=[l[0]+w[0],l[1]+w[1]],P=[g[0]-l[0],g[1]-l[1]],I=n["c"].scale([0,0],P,k),N=[l[0]+I[0],l[1]+I[1]];return t.startPoint={x:f[0],y:f[1]},t.endPoint={x:g[0],y:g[1]},t.controlPoints=[{x:C[0],y:C[1]},{x:N[0],y:N[1]}],t},y=function(t,e,i,n,r){var a=1e-4,s=[],c=null===t||void 0===t?void 0:t.getPoint(e);if(!c)return{x:0,y:0,angle:0};if(e<a)s=t.getStartTangent().reverse();else if(e>1-a)s=t.getEndTangent();else{var p=null===t||void 0===t?void 0:t.getPoint(e+a);s.push([c.x,c.y]),s.push([p.x,p.y])}var u=Math.atan2(s[1][1]-s[0][1],s[1][0]-s[0][0]);if(u<0&&(u+=2*h),i&&(c.x+=l(u)*i,c.y+=d(u)*i),n){var f=u-h/2;u>.5*h&&u<1.5*h&&(f-=h),c.x+=l(f)*n,c.y+=d(f)*n}var g={x:c.x,y:c.y,angle:u};return r?(u>.5*h&&u<1.5*h&&(u-=h),Object(o["a"])({rotate:u},g)):g},m=function t(e,i){if(!1===i(e))return!1;if(e&&e.children)for(var o=e.children.length-1;o>=0;o--)if(!t(e.children[o],i))return!1;return!0},b=function t(e,i){if(e&&e.children)for(var o=e.children.length-1;o>=0;o--)if(!t(e.children[o],i))return;return!1!==i(e)},v=function(t,e){"function"===typeof e&&m(t,e)},x=function(t,e){"function"===typeof e&&b(t,e)},O=function(t,e){return e*(s[t]||1)},j=function(t,e){var i=0,o=new RegExp("[一-龥]+");return t.split("").forEach((function(t){o.test(t)?i+=e:i+=O(t,e)})),[i,e]},S=function(t,e){return"number"!==typeof e||e<=0||e>=t.length?t:t.substring(0,e)+"..."},M=function(t,e){var i=[],o={},n={};t.forEach((function(t){n[t.id]=t})),t.forEach((function(t,e){var r=Object(c["clone"])(t);r.itemType="combo",r.children=void 0,r.parentId===r.id?(console.warn("The parentId for combo ".concat(r.id," can not be the same as the combo's id")),delete r.parentId):r.parentId&&!n[r.parentId]&&(console.warn("The parent combo for combo ".concat(r.id," does not exist!")),delete r.parentId);var a=o[r.id];if(a){if(r.children=a.children,o[r.id]=r,a=r,!a.parentId)return void i.push(a);var s=o[a.parentId];if(s)s.children?s.children.push(r):s.children=[r];else{var h={id:a.parentId,children:[a]};o[a.parentId]=h,o[r.id]=r}}else if(Object(c["isString"])(t.parentId)){var d=o[t.parentId];if(d)d.children?d.children.push(r):d.children=[r],o[r.id]=r;else{var l={id:t.parentId,children:[r]};o[l.id]=l,o[r.id]=r}}else i.push(r),o[r.id]=r}));var r={};(e||[]).forEach((function(t){r[t.id]=t;var e=o[t.comboId];if(e){var i={id:t.id,comboId:t.comboId};e.children?e.children.push(i):e.children=[i],i.itemType="node",o[t.id]=i}}));var a=0;return i.forEach((function(t){t.depth=a+10,m(t,(function(t){var e,i=o[t.id].itemType;e="node"===i?o[t.comboId]:o[t.parentId],t.depth=e&&"node"===i?a+1:a+10,a<t.depth&&(a=t.depth);var n=r[t.id];return n&&(n.depth=t.depth),!0}))})),i},k=function(t,e,i){var o,n,r=t,a={root:{children:t}},s=!1,c="root";(t||[]).forEach((function(t){if(!s)return t.id===e?(n=t,"combo"===t.itemType?n.parentId=i:n.comboId=i,void(s=!0)):void v(t,(function(t){var o;return a[t.id]={children:(null===t||void 0===t?void 0:t.children)||[]},r=null===(o=a[t.parentId||t.comboId||"root"])||void 0===o?void 0:o.children,!t||!t.removed&&e!==t.id||!r||(c=t.parentId||t.comboId||"root",n=t,"combo"===t.itemType?n.parentId=i:n.comboId=i,s=!0,!1)}))})),r=null===(o=a[c])||void 0===o?void 0:o.children;var h=r?r.indexOf(n):-1;if(h>-1&&r.splice(h,1),s||(n={id:e,itemType:"node",comboId:i},a[e]={children:void 0}),e){var d=!1;if(i){var l=0;(t||[]).forEach((function(t){d||v(t,(function(t){return i!==t.id||(d=!0,t.children?t.children.push(n):t.children=[n],l=t.depth,"node"===n.itemType?n.depth=l+2:n.depth=l+1,!1)}))}))}else i&&d||"node"===n.itemType||t.push(n);var p=n.depth;v(n,(function(t){return"node"===t.itemType?p+=2:p+=1,t.depth=p,!0}))}return t},w=function(t,e,i){var o={minX:1/0,minY:1/0,maxX:-1/0,maxY:-1/0,x:void 0,y:void 0,width:void 0,height:void 0,centerX:void 0,centerY:void 0};if(!t||0===t.length){var n=null===i||void 0===i?void 0:i.getModel(),r=n||{},a=r.x,s=r.y;return{minX:a,minY:s,maxX:a,maxY:s,x:a,y:s,width:void 0,height:void 0}}return t.forEach((function(t){var i=e.findById(t.id);if(i&&i.isVisible()){i.set("bboxCanvasCache",void 0);var n=i.getCanvasBBox();n.x&&o.minX>n.minX&&(o.minX=n.minX),n.y&&o.minY>n.minY&&(o.minY=n.minY),n.x&&o.maxX<n.maxX&&(o.maxX=n.maxX),n.y&&o.maxY<n.maxY&&(o.maxY=n.maxY)}})),o.x=(o.minX+o.maxX)/2,o.y=(o.minY+o.maxY)/2,o.width=o.maxX-o.minX,o.height=o.maxY-o.minY,o.centerX=(o.minX+o.maxX)/2,o.centerY=(o.minY+o.maxY)/2,Object.keys(o).forEach((function(t){o[t]!==1/0&&o[t]!==-1/0||(o[t]=void 0)})),o},C=function(t){var e=Object(c["isNumber"])(t.x)||Object(c["isNumber"])(t.y)||t.type||t.anchorPoints||t.size;return t.style&&(e=e||Object(c["isNumber"])(t.style.r)||Object(c["isNumber"])(t.style.width)||Object(c["isNumber"])(t.style.height)||Object(c["isNumber"])(t.style.rx)||Object(c["isNumber"])(t.style.ry)),e},P=function(t){var e={};return Object.keys(t).forEach((function(i){var o=t[i];if(Object(c["isObject"])(o)&&!Object(c["isArray"])(o)){var n={};Object.keys(o).forEach((function(t){var e=o[t];("img"!==t||Object(c["isString"])(e))&&(n[t]=Object(c["clone"])(e))})),e[i]=n}else e[i]=Object(c["clone"])(o)})),e}},3822:function(t,e,i){"use strict";i.d(e,"d",(function(){return dt["a"]})),i.d(e,"g",(function(){return dt["c"]})),i.d(e,"f",(function(){return dt["b"]})),i.d(e,"l",(function(){return vt})),i.d(e,"j",(function(){return Ot})),i.d(e,"b",(function(){return ht})),i.d(e,"h",(function(){return ft})),i.d(e,"k",(function(){return xt})),i.d(e,"i",(function(){return jt})),i.d(e,"c",(function(){return yt})),i.d(e,"a",(function(){return bt})),i.d(e,"e",(function(){return St}));var o={};i.r(o),i.d(o,"defaultSubjectColors",(function(){return lt}));var n=i("3e1e"),r=i("6edb"),a=i("7fa2"),s=i("e897"),c=i("8937"),h=i("97b1"),d=i("adc7"),l=i("2974"),p=function(t){if(!t)return console.error("G6 Error Tips: the data must be defined"),!1;var e=t.nodes,i=t.edges,o=t.combos,n=void 0===o?[]:o;if(!e&&!i){var a=!0;return Object(l["traverseTree"])(t,(function(t){return!!Object(c["isString"])(t.id)||(a=!1,!1)})),a}var s=(e||[]).find((function(t){return!Object(c["isString"])(t.id)}));if(s)return console.warn("G6 Warning Tips: missing 'id' property, or %c".concat(s.id,"%c is not a string."),"font-size: 20px; color: red;",""),!1;var h=(e||[]).map((function(t){return t.id})),d=null===n||void 0===n?void 0:n.map((function(t){return t.id})),p=Object(r["d"])(Object(r["d"])([],h,!0),d,!0),u=(i||[]).find((function(t){return!p.includes(t.source)||!p.includes(t.target)}));return!u||(console.warn("G6 Warning Tips: The source %c".concat(u.source,"%c or the target %c").concat(u.target,"%c of the edge do not exist in the nodes or combos."),"font-size: 20px; color: red;","","font-size: 20px; color: red;",""),!1)},u=function(t,e){if("node"===t||"combo"===t){if(e.id&&!Object(c["isString"])(e.id))return console.warn("G6 Warning Tips: missing 'id' property, or the 'id' %c".concat(e.id,"%c is not a string."),"font-size: 20px; color: red;",""),!1}else if("edge"===t&&(!e.source||!e.target))return console.warn("G6 Warning Tips: missing 'source' or 'target' for the edge."),!1;return!0},f=i("2e0c"),g=i("28d5"),y=function(){function t(t){this.graph=t,this.destroyed=!1,this.modes=t.get("modes")||{default:[]},this.formatModes(),this.mode=t.get("defaultMode")||"default",this.currentBehaves=[],this.setMode(this.mode)}return t.prototype.formatModes=function(){var t=this.modes;Object(c["each"])(t,(function(t){Object(c["each"])(t,(function(e,i){Object(c["isString"])(e)&&(t[i]={type:e})}))}))},t.prototype.setBehaviors=function(t){var e,i=this.graph,o=this.modes[t],n=[];Object(c["each"])(o||[],(function(t){var o=g["a"].getBehavior(t.type||t);o&&(e=new o(t),e&&(e.bind(i),n.push(e)))})),this.currentBehaves=n},t.mergeBehaviors=function(t,e){return Object(c["each"])(e,(function(e){t.indexOf(e)<0&&(Object(c["isString"])(e)&&(e={type:e}),t.push(e))})),t},t.filterBehaviors=function(t,e){var i=[];return t.forEach((function(t){var o="";o=Object(c["isString"])(t)?t:t.type,e.indexOf(o)<0&&i.push(t)})),i},t.prototype.setMode=function(t){var e=this,i=e.modes,o=e.graph,n=t,r=i[n];r&&(o.emit("beforemodechange",{mode:t}),Object(c["each"])(this.currentBehaves,(function(t){t.delegate&&t.delegate.remove(),t.unbind(o)})),this.setBehaviors(n),o.emit("aftermodechange",{mode:t}),this.mode=t)},t.prototype.getMode=function(){return this.mode},t.prototype.manipulateBehaviors=function(e,i,o){var n,r=this;if(n=Object(c["isArray"])(e)?e:[e],Object(c["isArray"])(i))return Object(c["each"])(i,(function(e){r.modes[e]?r.modes[e]=o?t.mergeBehaviors(r.modes[e]||[],n):t.filterBehaviors(r.modes[e]||[],n):o&&(r.modes[e]=n)})),this;var a=i;return i||(a=this.mode),this.modes[a]||o&&(this.modes[a]=n),this.modes[a]=o?t.mergeBehaviors(this.modes[a]||[],n):t.filterBehaviors(this.modes[a]||[],n),this.formatModes(),this.setMode(this.mode),this},t.prototype.updateBehavior=function(t,e,i){Object(c["isString"])(t)&&(t={type:t});var o=[];if(i&&i!==this.mode&&"default"!==i){if(o=this.modes[i],!o||!o.length)return console.warn("Update behavior failed! There is no behaviors in this mode on the graph."),this;var n=o.length;for(a=0;a<n;a++){s=o[a];if(s.type===t.type||s===t.type)return s===t.type&&(s={type:s}),Object.assign(s,e),o[a]=s,this;a===n-1&&console.warn("Update behavior failed! There is no such behavior in the mode")}}else{if(o=this.currentBehaves,!o||!o.length)return console.warn("Update behavior failed! There is no behaviors in this mode on the graph."),this;for(var r=o.length,a=0;a<r;a++){var s=o[a];if(s.type===t.type)return s.updateCfg(e),this;a===r-1&&console.warn("Update behavior failed! There is no such behavior in the mode")}}return this},t.prototype.destroy=function(){this.graph=null,this.modes=null,this.currentBehaves=null,this.destroyed=!0},t}(),m=y,b=i("6618"),v=function(){function t(t){this.destroyed=!1,this.graph=t,this.destroyed=!1}return t.prototype.getViewCenter=function(){var t=this.getFormatPadding(),e=this.graph,i=this.graph.get("width"),o=e.get("height");return{x:(i-t[1]-t[3])/2+t[3],y:(o-t[0]-t[2])/2+t[0]}},t.prototype.fitCenter=function(t,e){var i=this.graph,o=i.get("group");o.resetMatrix();var n=o.getCanvasBBox();if(0!==n.width&&0!==n.height){var r=this.getViewCenter(),a={x:n.x+n.width/2,y:n.y+n.height/2};i.translate(r.x-a.x,r.y-a.y,t,e)}},t.prototype.fitView=function(t,e){var i=this.graph,o=this.getFormatPadding(),n=i.get("width"),a=i.get("height"),s=i.get("group");s.resetMatrix();var c=s.getCanvasBBox();if(0!==c.width&&0!==c.height){var h=this.getViewCenter(),d={x:c.x+c.width/2,y:c.y+c.height/2},l=e;t&&(l=Object(r["a"])(Object(r["a"])({},e||{duration:500,easing:"easeCubic"}),{callback:function(){var t;i.zoom(f,h,!0,e),null===(t=null===e||void 0===e?void 0:e.callback)||void 0===t||t.call(e)}}));var p=(n-o[1]-o[3])/c.width,u=(a-o[0]-o[2])/c.height,f=p;p>u&&(f=u),i.translate(h.x-d.x,h.y-d.y,t,l),t||i.zoom(f,h)||console.warn("zoom failed, ratio out of range, ratio: %f",f)}},t.prototype.fitViewByRules=function(t,e,i){var o=t.onlyOutOfViewPort,n=void 0!==o&&o,r=t.direction,a=void 0===r?"both":r,s=t.ratioRule,c=void 0===s?"min":s,h=this.graph,d=this.getFormatPadding(),l=h.get("width"),p=h.get("height"),u=h.get("group");u.resetMatrix();var f=u.getCanvasBBox();if(0!==f.width&&0!==f.height){var g=this.getViewCenter(),y={x:f.x+f.width/2,y:f.y+f.height/2};h.translate(g.x-y.x,g.y-y.y,e,i);var m,b=(l-d[1]-d[3])/f.width,v=(p-d[0]-d[2])/f.height;m="x"===a?b:"y"===a?v:"max"===c?Math.max(b,v):Math.min(b,v),n&&(m=m<1?m:1);var x=h.getZoom(),O=x*m,j=h.get("minZoom");O<j&&(O=j,console.warn("fitview failed, ratio out of range, ratio: %f",m,"graph minzoom has been used instead")),h.zoomTo(O,g,e,i)}},t.prototype.getFormatPadding=function(){var t=this.graph.get("fitViewPadding");return Object(b["formatPadding"])(t)},t.prototype.focusPoint=function(t,e,i){var o=this,n=this.getViewCenter(),a=this.getPointByCanvas(n.x,n.y),s=this.graph.get("group").getMatrix();if(s||(s=[1,0,0,0,1,0,0,0,1]),e){var c=(a.x-t.x)*s[0],h=(a.y-t.y)*s[4],d=0,l=0,p=0,u=0;this.graph.get("canvas").animate((function(t){p=c*t,u=h*t,o.graph.translate(p-d,u-l),d=p,l=u}),Object(r["a"])({},i))}else this.graph.translate((a.x-t.x)*s[0],(a.y-t.y)*s[4])},t.prototype.getPointByCanvas=function(t,e){var i=this.graph.get("group").getMatrix();i||(i=[1,0,0,0,1,0,0,0,1]);var o=Object(d["invertMatrix"])({x:t,y:e},i);return o},t.prototype.getPointByClient=function(t,e){var i=this.graph.get("canvas"),o=i.getPointByClient(t,e);return this.getPointByCanvas(o.x,o.y)},t.prototype.getClientByPoint=function(t,e){var i=this.graph.get("canvas"),o=this.getCanvasByPoint(t,e),n=i.getClientByPoint(o.x,o.y);return{x:n.x,y:n.y}},t.prototype.getCanvasByPoint=function(t,e){var i=this.graph.get("group").getMatrix();return i||(i=[1,0,0,0,1,0,0,0,1]),Object(d["applyMatrix"])({x:t,y:e},i)},t.prototype.focus=function(t,e,i){if(Object(c["isString"])(t)&&(t=this.graph.findById(t)),t){var o=0,n=0;if(t.getType&&"edge"===t.getType()){var r=t.getSource().get("group").getMatrix(),a=t.getTarget().get("group").getMatrix();r&&a?(o=(r[6]+a[6])/2,n=(r[7]+a[7])/2):(r||a)&&(o=r?r[6]:a[6],n=r?r[7]:a[7])}else{var s=t.get("group"),h=s.getMatrix();h||(h=[1,0,0,0,1,0,0,0,1]),o=h[6],n=h[7]}this.focusPoint({x:o,y:n},e,i)}},t.prototype.changeSize=function(t,e){var i=this.graph;if(!Object(c["isNumber"])(t)||!Object(c["isNumber"])(e))throw Error("invalid canvas width & height, please make sure width & height type is number");i.set({width:t,height:e});var o=i.get("canvas");o.changeSize(t,e);var n=i.get("plugins");n.forEach((function(t){t.get("gridContainer")&&t.positionInit()}))},t.prototype.destroy=function(){this.graph=null,this.destroyed=!1},t}(),x=v,O=i("1924"),j="bboxCache",S="bboxCanvasCache",M=function(){function t(t){this._cfg={},this.destroyed=!1;var e={id:void 0,type:"item",model:{},group:void 0,animate:!1,visible:!0,locked:!1,event:!0,keyShape:void 0,states:[]};this._cfg=Object.assign(e,this.getDefaultCfg(),t);var i=this.get("model"),o=i.id,n=this.get("type");o||(o=Object(b["uniqueId"])(n),this.get("model").id=o),this.set("id",o);var r=t.group;r&&(r.set("item",this),r.set("id",o)),this.init(),this.draw();var a=i.shape||i.type||("edge"===n?"line":"circle"),s=this.get("shapeFactory");if(s&&s[a]){var h=s[a].options;if(h&&h.stateStyles){var d=this.get("styles")||i.stateStyles;d=Object(c["deepMix"])({},h.stateStyles,d),this.set("styles",d)}}}return t.prototype.calculateBBox=function(){var t=this.get("keyShape"),e=this.get("group"),i=Object(l["getBBox"])(t,e);return i.x=i.minX,i.y=i.minY,i.width=i.maxX-i.minX,i.height=i.maxY-i.minY,i.centerX=(i.minX+i.maxX)/2,i.centerY=(i.minY+i.maxY)/2,i},t.prototype.calculateCanvasBBox=function(){var t=this.get("keyShape"),e=this.get("group"),i=Object(l["getBBox"])(t,e);return i.x=i.minX,i.y=i.minY,i.width=i.maxX-i.minX,i.height=i.maxY-i.minY,i.centerX=(i.minX+i.maxX)/2,i.centerY=(i.minY+i.maxY)/2,i},t.prototype.drawInner=function(){var t=this,e=t.get("shapeFactory"),i=t.get("group"),o=t.get("model");i.clear();var n=o.visible;if(void 0===n||n||t.changeVisibility(n),e){t.updatePosition(o);var r=t.getShapeCfg(o),a=r.type,s=e.draw(a,r,i);s&&(t.set("keyShape",s),s.set("isKeyShape",!0),s.set("draggable",!0)),this.setOriginStyle(),this.set("currentShape",a),this.restoreStates(e,a)}},t.prototype.setOriginStyle=function(){var t=this.get("group"),e=t.get("children"),i=this.getKeyShape(),o=this,n=i.get("name");if(this.get("originStyle")){var r=this.get("originStyle");n&&!r[n]&&(r[n]={});var a=this.getCurrentStatesStyle(),s=function(t){var i=e[t],s=i.get("name"),h=i.attr();if(s&&s!==n){var d=a[s];r[s]||(r[s]={}),d?Object.keys(h).forEach((function(t){var e=h[t];e!==d[t]&&(r[s][t]=e)})):r[s]="image"!==i.get("type")?Object(c["clone"])(h):o.getShapeStyleByName(s)}else{var l=i.attr(),p={};Object.keys(a).forEach((function(t){var e=a[t];t!==n&&Object(c["isPlainObject"])(e)||(p[t]=e)})),Object.keys(l).forEach((function(t){var e=l[t];p[t]!==e&&(n?r[n][t]=e:r[t]=e)}))}};for(d=0;d<e.length;d++)s(d);delete r.path,delete r.matrix,delete r.x,delete r.y,r[n]&&(delete r[n].x,delete r[n].y,delete r[n].matrix,delete r[n].path),o.set("originStyle",r)}else{for(var h={},d=0;d<e.length;d++){var l=e[d],p=l.get("type"),u=l.get("name");if(u&&u!==n)h[u]="image"!==p?Object(c["clone"])(l.attr()):o.getShapeStyleByName(u),"text"===p&&h[u]&&(delete h[u].x,delete h[u].y,delete h[u].matrix);else{var f=o.getShapeStyleByName();if(delete f.path,delete f.matrix,n)if(u)h[n]=f;else{var g=Object(b["uniqueId"])("shape");l.set("name",g),t["shapeMap"][g]=l,h[g]="image"!==p?Object(c["clone"])(l.attr()):o.getShapeStyleByName(u)}else Object.assign(h,f)}}o.set("originStyle",h)}},t.prototype.restoreStates=function(t,e){var i=this,o=i.get("states");Object(c["each"])(o,(function(o){t.setState(e,o,!0,i)}))},t.prototype.init=function(){var t=O["a"].getFactory(this.get("type"));this.set("shapeFactory",t)},t.prototype.get=function(t){return this._cfg[t]},t.prototype.set=function(t,e){Object(c["isPlainObject"])(t)?this._cfg=Object(r["a"])(Object(r["a"])({},this._cfg),t):this._cfg[t]=e},t.prototype.getDefaultCfg=function(){return{}},t.prototype.clearCache=function(){this.set(j,null),this.set(S,null)},t.prototype.beforeDraw=function(){},t.prototype.afterDraw=function(){},t.prototype.afterUpdate=function(){},t.prototype.draw=function(){this.beforeDraw(),this.drawInner(),this.afterDraw()},t.prototype.getShapeStyleByName=function(t){var e,i=this.get("group");if(e=t?i["shapeMap"][t]:this.getKeyShape(),e){var o={};return Object(c["each"])(e.attr(),(function(t,e){("img"!==e||Object(c["isString"])(t))&&(o[e]=t)})),o}return{}},t.prototype.getShapeCfg=function(t,e){var i=this.get("styles");if(i){var o=t;return o.style=Object(r["a"])(Object(r["a"])({},i),t.style),o}return t},t.prototype.getStateStyle=function(t){var e=this.get("styles"),i=e&&e[t];return i},t.prototype.getOriginStyle=function(){return this.get("originStyle")},t.prototype.getCurrentStatesStyle=function(){var t=this,e={},i=t.getStates();return i&&i.length?(Object(c["each"])(t.getStates(),(function(i){e=Object.assign(e,t.getStateStyle(i))})),e):this.get("originStyle")},t.prototype.setState=function(t,e){var i=this.get("states"),o=this.get("shapeFactory"),n=t,r=t;Object(c["isString"])(e)&&(n="".concat(t,":").concat(e),r="".concat(t,":"));var a=i;if(Object(c["isBoolean"])(e)){var s=i.indexOf(r);if(e){if(s>-1)return;i.push(n)}else s>-1&&i.splice(s,1)}else if(Object(c["isString"])(e)){var h=i.filter((function(t){return t.includes(r)}));h.length>0&&this.clearStates(h),a=a.filter((function(t){return!t.includes(r)})),a.push(n),this.set("states",a)}if(o){var d=this.get("model"),l=d.type;o.setState(l,t,e,this)}},t.prototype.clearStates=function(t){var e=this,i=e.getStates(),o=e.get("shapeFactory"),n=e.get("model"),r=n.type;t||(t=i),Object(c["isString"])(t)&&(t=[t]);var a=i.filter((function(e){return-1===t.indexOf(e)}));e.set("states",a),t.forEach((function(t){o.setState(r,t,!1,e)}))},t.prototype.getContainer=function(){return this.get("group")},t.prototype.getKeyShape=function(){return this.get("keyShape")},t.prototype.getModel=function(){return this.get("model")},t.prototype.getType=function(){return this.get("type")},t.prototype.getID=function(){return this.get("id")},t.prototype.isItem=function(){return!0},t.prototype.getStates=function(){return this.get("states")},t.prototype.hasState=function(t){var e=this.getStates();return e.indexOf(t)>=0},t.prototype.refresh=function(t){var e=this.get("model");this.updatePosition(e),this.updateShape(t),this.afterUpdate(),this.clearCache()},t.prototype.getUpdateType=function(t){},t.prototype.update=function(t,e){void 0===e&&(e=void 0);var i=this.get("model");if("move"===e)this.updatePosition(t);else{var o=i.visible,n=t.visible;o!==n&&void 0!==n&&this.changeVisibility(n);var r={x:i.x,y:i.y};t.x=isNaN(+t.x)?i.x:+t.x,t.y=isNaN(+t.y)?i.y:+t.y;var a=this.get("styles");if(t.stateStyles){var s=t.stateStyles;Object(c["mix"])(a,s),delete t.stateStyles}Object.assign(i,t),r.x===t.x&&r.y===t.y||this.updatePosition(t),this.updateShape(e)}this.afterUpdate(),this.clearCache()},t.prototype.updateShape=function(t){var e=this.get("shapeFactory"),i=this.get("model"),o=i.type;if(e.shouldUpdate(o)&&o===this.get("currentShape")){var n=this.getShapeCfg(i,t);e.baseUpdate(o,n,this,t),"move"!==t&&this.setOriginStyle()}else this.draw();this.restoreStates(e,o)},t.prototype.updatePosition=function(t){var e=this.get("model"),i=isNaN(+t.x)?+e.x:+t.x,o=isNaN(+t.y)?+e.y:+t.y,n=this.get("group");if(isNaN(i)||isNaN(o))return!1;e.x=i,e.y=o;var r=n.getMatrix();return(!r||r[6]!==i||r[7]!==o)&&(n.resetMatrix(),Object(d["translate"])(n,{x:i,y:o}),this.clearCache(),!0)},t.prototype.getBBox=function(){var t=this.get(j);return t||(t=this.calculateBBox(),this.set(j,t)),t},t.prototype.getCanvasBBox=function(){var t=this.get(S);return t||(t=this.calculateCanvasBBox(),this.set(S,t)),t},t.prototype.toFront=function(){var t=this.get("group");t.toFront()},t.prototype.toBack=function(){var t=this.get("group");t.toBack()},t.prototype.show=function(){this.changeVisibility(!0)},t.prototype.hide=function(){this.changeVisibility(!1)},t.prototype.changeVisibility=function(t){var e=this.get("group");t?e.show():e.hide(),this.set("visible",t)},t.prototype.isVisible=function(){return this.get("visible")},t.prototype.enableCapture=function(t){var e=this.get("group");e&&e.set("capture",t)},t.prototype.destroy=function(){if(!this.destroyed){var t=this.get("animate"),e=this.get("group");t&&e.stopAnimate(),e["shapeMap"]={},this.clearCache(),e.remove(),this._cfg=null,this.destroyed=!0}},t}(),k=M,w={source:"start",target:"end"},C="Node",P="Point",I="Anchor",N=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return Object(r["b"])(e,t),e.prototype.getDefaultCfg=function(){return{type:"edge",sourceNode:null,targetNode:null,startPoint:null,endPoint:null,linkCenter:!1}},e.prototype.setEnd=function(t,e){var i=w[t]+P,o=t+C,n=this.get(o);n&&!n.destroyed&&n.removeEdge(this),Object(c["isPlainObject"])(e)?(this.set(i,e),this.set(o,null)):e&&(e.addEdge(this),this.set(o,e),this.set(i,null))},e.prototype.getLinkPoint=function(t,e,i){var o=w[t]+P,n=t+C,r=this.get(o);if(!r){var a=this.get(n),s=t+I,h=this.getPrePoint(t,i),d=e[s];Object(c["isNil"])(d)||(r=a.getLinkPointByAnchor(d)),r=r||a.getLinkPoint(h),Object(c["isNil"])(r.index)||this.set("".concat(t,"AnchorIndex"),r.index)}return r},e.prototype.getPrePoint=function(t,e){if(e&&e.length){var i="source"===t?0:e.length-1;return e[i]}var o="source"===t?"target":"source";return this.getEndPoint(o)},e.prototype.getEndPoint=function(t){var e=t+C,i=w[t]+P,o=this.get(e);return o?o.get("model"):this.get(i)},e.prototype.getControlPointsByCenter=function(t){var e=this.getEndPoint("source"),i=this.getEndPoint("target"),o=this.get("shapeFactory"),n=t.type;return o.getControlPoints(n,{startPoint:e,endPoint:i})},e.prototype.getEndCenter=function(t){var e=t+C,i=w[t]+P,o=this.get(e);if(o){var n=o.getBBox();return{x:n.centerX,y:n.centerY}}return this.get(i)},e.prototype.init=function(){t.prototype.init.call(this),this.setSource(this.get("source")),this.setTarget(this.get("target"))},e.prototype.getShapeCfg=function(e,i){var o=this,n=o.get("linkCenter"),r=(null===i||void 0===i?void 0:i.includes("move"))?e:t.prototype.getShapeCfg.call(this,e);if(n)r.startPoint=o.getEndCenter("source"),r.endPoint=o.getEndCenter("target");else{var a=r.controlPoints||o.getControlPointsByCenter(r);r.startPoint=o.getLinkPoint("source",e,a),r.endPoint=o.getLinkPoint("target",e,a)}return r.sourceNode=o.get("sourceNode"),r.targetNode=o.get("targetNode"),r},e.prototype.getModel=function(){var t=this.get("model"),e=this.get("source".concat(C)),i=this.get("target".concat(C));return e?delete t["source".concat(C)]:t.source=this.get("start".concat(P)),i?delete t["target".concat(C)]:t.target=this.get("end".concat(P)),Object(c["isString"])(t.source)||Object(c["isPlainObject"])(t.source)||(t.source=t.source.getID()),Object(c["isString"])(t.target)||Object(c["isPlainObject"])(t.target)||(t.target=t.target.getID()),t},e.prototype.setSource=function(t){this.setEnd("source",t),this.set("source",t)},e.prototype.setTarget=function(t){this.setEnd("target",t),this.set("target",t)},e.prototype.getSource=function(){return this.get("source")},e.prototype.getTarget=function(){return this.get("target")},e.prototype.updatePosition=function(){return!1},e.prototype.update=function(t,e){void 0===e&&(e=void 0);var i=this.get("model"),o=i.visible,n=t.visible;o!==n&&void 0!==n&&this.changeVisibility(n);var r=this.get("styles");if(t.stateStyles){var a=t.stateStyles;Object(c["mix"])(r,a),delete t.stateStyles}Object.assign(i,t),this.updateShape(e),this.afterUpdate(),this.clearCache()},e.prototype.destroy=function(){var e=this.get("source".concat(C)),i=this.get("target".concat(C));e&&!e.destroyed&&e.removeEdge(this),i&&!i.destroyed&&i.removeEdge(this),t.prototype.destroy.call(this)},e}(k),B=N,T="anchorPointsCache",E="bboxCache",Y=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return Object(r["b"])(e,t),e.prototype.getNearestPoint=function(t,e){for(var i=0,o=t[0],n=Object(d["distance"])(t[0],e),r=0;r<t.length;r++){var a=t[r],s=Object(d["distance"])(a,e);s<n&&(o=a,n=s,i=r)}return o.anchorIndex=i,o},e.prototype.getDefaultCfg=function(){return{type:"node",edges:[]}},e.prototype.getEdges=function(){return this.get("edges")},e.prototype.getInEdges=function(){var t=this;return this.get("edges").filter((function(e){return e.get("target")===t}))},e.prototype.getOutEdges=function(){var t=this;return this.get("edges").filter((function(e){return e.get("source")===t}))},e.prototype.getNeighbors=function(t){var e=this,i=this.get("edges");if("target"===t){var o=function(t){return t.getSource()===e};return i.filter(o).map((function(t){return t.getTarget()}))}if("source"===t){var n=function(t){return t.getTarget()===e};return i.filter(n).map((function(t){return t.getSource()}))}var r=function(t){return t.getSource()===e?t.getTarget():t.getSource()};return i.map(r)},e.prototype.getLinkPointByAnchor=function(t){var e=this.getAnchorPoints();return e[t]},e.prototype.getLinkPoint=function(t){var e,i,o=this.get("keyShape"),n=o.get("type"),r=this.get("type"),a=this.getBBox();"combo"===r?(e=a.centerX||(a.maxX+a.minX)/2,i=a.centerY||(a.maxY+a.minY)/2):(e=a.centerX,i=a.centerY);var s,c=this.getAnchorPoints();switch(n){case"circle":s=Object(d["getCircleIntersectByPoint"])({x:e,y:i,r:a.width/2},t);break;case"ellipse":s=Object(d["getEllipseIntersectByPoint"])({x:e,y:i,rx:a.width/2,ry:a.height/2},t);break;default:s=Object(d["getRectIntersectByPoint"])(a,t)}var h=s;return c.length&&(h||(h=t),h=this.getNearestPoint(c,h)),h||(h={x:e,y:i}),h},e.prototype.getAnchorPoints=function(){var t=this.get(T);if(!t){t=[];var e=this.get("shapeFactory"),i=this.getBBox(),o=this.get("model"),n=this.getShapeCfg(o),r=o.type,a=e.getAnchorPoints(r,n)||[];Object(c["each"])(a,(function(e,o){var n={x:i.minX+e[0]*i.width,y:i.minY+e[1]*i.height,anchorIndex:o};t.push(n)})),this.set(T,t)}return t},e.prototype.addEdge=function(t){this.get("edges").push(t)},e.prototype.lock=function(){this.set("locked",!0)},e.prototype.unlock=function(){this.set("locked",!1)},e.prototype.hasLocked=function(){return this.get("locked")},e.prototype.removeEdge=function(t){var e=this.getEdges(),i=e.indexOf(t);i>-1&&e.splice(i,1)},e.prototype.clearCache=function(){this.set(E,null),this.set(T,null)},e.prototype.getUpdateType=function(t){var e,i,o,n,r;if(t){var a=!Object(c["isNil"])(t.x),s=!Object(c["isNil"])(t.y),h=Object.keys(t);if(1===h.length&&(a||s)||2===h.length&&a&&s)return"move";if(Object(c["isNumber"])(t.x)||Object(c["isNumber"])(t.y)||t.type||t.anchorPoints||t.size||(null===t||void 0===t?void 0:t.style)&&((null===(e=null===t||void 0===t?void 0:t.style)||void 0===e?void 0:e.r)||(null===(i=null===t||void 0===t?void 0:t.style)||void 0===i?void 0:i.width)||(null===(o=null===t||void 0===t?void 0:t.style)||void 0===o?void 0:o.height)||(null===(n=null===t||void 0===t?void 0:t.style)||void 0===n?void 0:n.rx)||(null===(r=null===t||void 0===t?void 0:t.style)||void 0===r?void 0:r.ry)))return"bbox|label";var d=h.includes("label")||h.includes("labelCfg");return d?"style|label":"style"}},e}(k),X=Y,z="bboxCache",L="bboxCanvasCache",A="sizeCache",F="anchorPointsCache",V=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return Object(r["b"])(e,t),e.prototype.getDefaultCfg=function(){return{type:"combo",nodes:[],edges:[],combos:[]}},e.prototype.getShapeCfg=function(t){var e=this.get("styles"),i=this.get("bbox");if(e&&i){var o=t,n={r:Math.hypot(i.height,i.width)/2||f["a"].defaultCombo.size[0]/2,width:i.width||f["a"].defaultCombo.size[0],height:i.height||f["a"].defaultCombo.size[1]};o.style=Object(r["a"])(Object(r["a"])(Object(r["a"])({},e),t.style),n);var a=t.padding||f["a"].defaultCombo.padding;return Object(c["isNumber"])(a)?(n.r+=a,n.width+=2*a,n.height+=2*a):(n.r+=a[0],n.width+=a[1]+a[3]||2*a[1],n.height+=a[0]+a[2]||2*a[0]),this.set(A,n),o}return t},e.prototype.calculateCanvasBBox=function(){if(!this.destroyed){var t=this.get("keyShape"),e=this.get("group"),i=Object(l["getBBox"])(t,e);i.centerX=(i.minX+i.maxX)/2,i.centerY=(i.minY+i.maxY)/2;var o=this.get(A),n=this.get(z)||{},r=n.x,a=n.x;if(o){o.width=Math.max(o.width,i.width),o.height=Math.max(o.height,i.height);var s=t.get("type");"circle"===s?(i.width=2*o.r,i.height=2*o.r):(i.width=o.width,i.height=o.height),i.minX=i.centerX-i.width/2,i.minY=i.centerY-i.height/2,i.maxX=i.centerX+i.width/2,i.maxY=i.centerY+i.height/2}else i.width=i.maxX-i.minX,i.height=i.maxY-i.minY,i.centerX=(i.minX+i.maxX)/2,i.centerY=(i.minY+i.maxY)/2;return i.x=i.minX,i.y=i.minY,i.x===r&&i.y===a||this.set(F,null),i}},e.prototype.getChildren=function(){var t=this;return{nodes:t.getNodes(),combos:t.getCombos()}},e.prototype.getNodes=function(){var t=this;return t.get("nodes")},e.prototype.getCombos=function(){var t=this;return t.get("combos")},e.prototype.addChild=function(t){var e=this,i=t.getType();switch(i){case"node":e.addNode(t);break;case"combo":e.addCombo(t);break;default:return console.warn("Only node or combo items are allowed to be added into a combo"),!1}return!0},e.prototype.addCombo=function(t){var e=this;return e.get("combos").push(t),!0},e.prototype.addNode=function(t){var e=this;return e.get("nodes").push(t),!0},e.prototype.removeChild=function(t){var e=this,i=t.getType();switch(i){case"node":e.removeNode(t);break;case"combo":e.removeCombo(t);break;default:return console.warn("Only node or combo items are allowed to be added into a combo"),!1}return!0},e.prototype.removeCombo=function(t){if(t){var e=this.getCombos(),i=e.indexOf(t);return i>-1&&(e.splice(i,1),!0)}},e.prototype.removeNode=function(t){if(t){var e=this.getNodes(),i=e.indexOf(t);return i>-1&&(e.splice(i,1),!0)}},e.prototype.getUpdateType=function(t){},e.prototype.getBBox=function(){this.set(L,null);var t=this.calculateCanvasBBox();return t},e.prototype.clearCache=function(){this.set(z,null),this.set(L,null),this.set(F,null)},e.prototype.destroy=function(){if(!this.destroyed){var t=this.get("animate"),e=this.get("group");t&&e.stopAnimate(),e["shapeMap"]={},this.clearCache(),this.set(A,null),this.set("bbox",null),e.remove(),this._cfg=null,this.destroyed=!0}},e}(X),D=V,W="node",G="edge",R="vedge",U="combo",Z="default",_="Mapper",q="stateStyles",H=function(){function t(t){var e=this;this.edgeToBeUpdateMap={},this.throttleRefresh=Object(c["throttle"])((function(t){var i,o=e.graph;if(o&&!o.get("destroyed")){var n=e.edgeToBeUpdateMap;n&&(null===(i=Object.keys(n))||void 0===i?void 0:i.length)&&(Object.keys(n).forEach((function(t){var e=n[t].edge;if(e&&!e.destroyed){var i=e.getSource(),o=e.getTarget();i&&!i.destroyed&&o&&!o.destroyed&&e.refresh(n[t].updateType)}})),e.edgeToBeUpdateMap={})}}),16,{trailing:!0,leading:!0}),this.graph=t,this.destroyed=!1}return t.prototype.addItem=function(t,e){var i=this.graph,o=t===R?G:t,n=i.get("".concat(o,"Group"))||i.get("group"),r=Object(c["upperFirst"])(o),a=null,s=i.get(o+Object(c["upperFirst"])(q))||{},h=i.get(Z+r);e[q]&&(s=e[q]),h&&Object(c["each"])(h,(function(t,i){Object(c["isObject"])(t)&&!Object(c["isArray"])(t)?e[i]=Object(c["deepMix"])({},t,e[i]):Object(c["isArray"])(t)?e[i]=e[i]||Object(c["clone"])(h[i]):e[i]=e[i]||h[i]}));var d=i.get(o+_);if(d){var p=d(e);p[q]&&(s=p[q],delete p[q]),Object(c["each"])(p,(function(t,i){Object(c["isObject"])(t)&&!Object(c["isArray"])(t)?e[i]=Object(c["deepMix"])({},e[i],t):e[i]=p[i]||e[i]}))}if(i.emit("beforeadditem",{type:t,model:e}),t===G||t===R){var u=void 0,f=void 0;if(u=e.source,f=e.target,u&&Object(c["isString"])(u)&&(u=i.findById(u)),f&&Object(c["isString"])(f)&&(f=i.findById(f)),!u||!f)return void console.warn("The source or target node of edge ".concat(e.id," does not exist!"));u.getType&&"combo"===u.getType()&&(e.isComboEdge=!0),f.getType&&"combo"===f.getType()&&(e.isComboEdge=!0),a=new B({model:e,source:u,target:f,styles:s,linkCenter:i.get("linkCenter"),group:n.addGroup()})}else if(t===W)a=new X({model:e,styles:s,group:n.addGroup()});else if(t===U){var g=e.children,y=Object(l["getComboBBox"])(g,i),m=void 0,b=void 0;if(isNaN(y.x)?isNaN(e.x)&&(m=100*Math.random()):m=y.x,isNaN(y.y)?isNaN(e.y)&&(b=100*Math.random()):b=y.y,isNaN(e.x)||isNaN(e.y))e.x=m,e.y=b;else{var v=e.x-m,x=e.y-b;this.updateComboSucceeds(e.id,v,x,g)}var O=n.addGroup();O.setZIndex(e.depth),a=new D({model:e,styles:s,animate:!1,bbox:e.collapsed?Object(l["getComboBBox"])([],i):y,group:O});var j=a.getModel();(g||[]).forEach((function(t){var e=i.findById(t.id);a.addChild(e),t.depth=j.depth+2}))}if(a)return i.get("".concat(t,"s")).push(a),i.get("itemMap")[a.get("id")]=a,i.emit("afteradditem",{item:a,model:e}),a},t.prototype.updateItem=function(t,e){var i,o,n=this,a=this.graph;if(Object(c["isString"])(t)&&(t=a.findById(t)),t&&!t.destroyed){var s="";t.getType&&(s=t.getType());var h=a.get(s+_),d=t.getModel(),l=d.x,p=d.y,u=t.getUpdateType(e);if(h){var f=Object(c["deepMix"])({},d,e),g=h(f),y=Object(c["deepMix"])({},d,g,e);g[q]&&(t.set("styles",y[q]),delete y[q]),Object(c["each"])(y,(function(t,i){e[i]=t}))}else Object(c["each"])(e,(function(t,i){d[i]&&Object(c["isObject"])(t)&&!Object(c["isArray"])(t)&&(e[i]=Object(r["a"])(Object(r["a"])({},d[i]),e[i]))}));if(a.emit("beforeupdateitem",{item:t,cfg:e}),s===G){if(e.source){var m=e.source;Object(c["isString"])(m)&&(m=a.findById(m)),t.setSource(m)}if(e.target){var b=e.target;Object(c["isString"])(b)&&(b=a.findById(b)),t.setTarget(b)}t.update(e)}else if(s===W){t.update(e,u);var v=t.getEdges(),x=(null===u||void 0===u?void 0:u.includes("bbox"))||"move"===u;"move"===u?Object(c["each"])(v,(function(t){n.edgeToBeUpdateMap[t.getID()]={edge:t,updateType:u},n.throttleRefresh()})):x&&Object(c["each"])(v,(function(t){t.refresh(u)}))}else if(s===U){if(t.update(e,u),!isNaN(e.x)||!isNaN(e.y)){var O=e.x-l||0,j=e.y-p||0;this.updateComboSucceeds(d.id,O,j)}var S=t.getEdges();x=(null===u||void 0===u?void 0:u.includes("bbox"))||"move"===u;if(x&&s===U){var M=t.get("shapeFactory"),k=d.type||"circle",w=void 0===d.animate||void 0===e.animate?null===(o=null===(i=M[k])||void 0===i?void 0:i.options)||void 0===o?void 0:o.animate:d.animate||e.animate;w?setTimeout((function(){if(t&&!t.destroyed){var e=t.getKeyShape();e&&!e.destroyed&&Object(c["each"])(S,(function(t){t&&!t.destroyed&&t.refresh()}))}}),201):Object(c["each"])(S,(function(t){t.refresh()}))}}a.emit("afterupdateitem",{item:t,cfg:e})}},t.prototype.updateCombo=function(t,e,i){var o,n,r=this,a=this.graph;if(Object(c["isString"])(t)&&(t=a.findById(t)),t&&!t.destroyed){var s=t.getModel(),h=Object(l["getComboBBox"])(e,a,t),d=h.x,p=h.y;t.set("bbox",h);var u=d,f=p;i?(u=isNaN(s.x)?d:s.x,f=isNaN(s.y)?p:s.y):(u=isNaN(d)?s.x:d,f=isNaN(p)?s.y:p),t.update({x:u,y:f});var g=t.get("shapeFactory"),y=s.type||"circle",m=void 0===s.animate?null===(n=null===(o=g[y])||void 0===o?void 0:o.options)||void 0===n?void 0:n.animate:s.animate;m?setTimeout((function(){if(t&&!t.destroyed){var e=t.getKeyShape();e&&!e.destroyed&&(t.getShapeCfg(s),r.updateComboEdges(t))}}),201):this.updateComboEdges(t)}},t.prototype.updateComboEdges=function(t){for(var e=t.getEdges()||[],i=0;i<e.length;i++){var o=e[i];o&&!o.destroyed&&o.refresh()}},t.prototype.collapseCombo=function(t,e){void 0===e&&(e=!0);var i=this.graph;Object(c["isString"])(t)&&(t=i.findById(t));var o=t.getChildren();o.nodes.forEach((function(t){i.hideItem(t,e)})),o.combos.forEach((function(t){i.hideItem(t,e)}))},t.prototype.updateComboSucceeds=function(t,e,i,o){var n=this;void 0===o&&(o=[]);var r=this.graph;if(e||i){var a=o;if(!(null===a||void 0===a?void 0:a.length)){var s=r.get("comboTrees");null===s||void 0===s||s.forEach((function(e){Object(l["traverseTree"])(e,(function(e){return e.id!==t||(a=e.children,!1)}))}))}null===a||void 0===a||a.forEach((function(t){var o=r.findById(t.id);if(o){var a=o.getModel();n.updateItem(t.id,{x:(a.x||0)+e,y:(a.y||0)+i})}}))}},t.prototype.expandCombo=function(t,e){void 0===e&&(e=!0);var i=this.graph;Object(c["isString"])(t)&&(t=i.findById(t));var o=t.getChildren();o.nodes.forEach((function(t){i.showItem(t,e)})),o.combos.forEach((function(t){t.getModel().collapsed?t.show():i.showItem(t,e)}))},t.prototype.removeItem=function(t){var e=this,i=this.graph;if(Object(c["isString"])(t)&&(t=i.findById(t)),t&&!t.destroyed){var o=Object(c["clone"])(t.getModel());i.emit("beforeremoveitem",{item:o});var n="";t.getType&&(n=t.getType());var r=i.get("".concat(n,"s")),a=r.indexOf(t);if(a>-1&&r.splice(a,1),n===G){var s=i.get("v".concat(n,"s")),h=s.indexOf(t);h>-1&&s.splice(h,1)}var d=t.get("id"),p=i.get("itemMap");delete p[d];var u=i.get("comboTrees"),f=t.get("id");if(n===W){var g=t.getModel().comboId;if(u&&g){var y=u,m=!1;u.forEach((function(t){m||Object(l["traverseTree"])(t,(function(t){if(t.id===f&&y){var e=y.indexOf(t);return y.splice(e,1),m=!0,!1}return y=t.children,!0}))}))}for(var b=t.getEdges(),v=b.length-1;v>=0;v--)i.removeItem(b[v],!1);g&&i.updateCombo(g)}else if(n===U){var x,O=t.getModel().parentId,j=!1;(u||[]).forEach((function(t){j||Object(l["traverseTree"])(t,(function(t){return t.id!==f||(x=t,j=!0,!1)}))})),x.removed=!0,x&&x.children&&x.children.forEach((function(t){e.removeItem(t.id)}));for(b=t.getEdges(),v=b.length;v>=0;v--)i.removeItem(b[v],!1);O&&i.updateCombo(O)}t.destroy(),i.emit("afterremoveitem",{item:o,type:n})}},t.prototype.setItemState=function(t,e,i){var o=this.graph,n=e;Object(c["isString"])(i)&&(n="".concat(e,":").concat(i)),t.hasState(n)===i&&i||Object(c["isString"])(i)&&t.hasState(n)||(o.emit("beforeitemstatechange",{item:t,state:n,enabled:i}),t.setState(e,i),o.autoPaint(),o.emit("afteritemstatechange",{item:t,state:n,enabled:i}))},t.prototype.priorityState=function(t,e){var i=this.graph,o=t;Object(c["isString"])(t)&&(o=i.findById(t)),this.setItemState(o,e,!1),this.setItemState(o,e,!0)},t.prototype.clearItemStates=function(t,e){var i=this.graph;Object(c["isString"])(t)&&(t=i.findById(t)),i.emit("beforeitemstatesclear",{item:t,states:e}),t.clearStates(e),i.emit("afteritemstatesclear",{item:t,states:e})},t.prototype.refreshItem=function(t){var e=this.graph;Object(c["isString"])(t)&&(t=e.findById(t)),e.emit("beforeitemrefresh",{item:t}),t.refresh(),e.emit("afteritemrefresh",{item:t})},t.prototype.addCombos=function(t,e){var i=this,o=this.graph;(t||[]).forEach((function(t){Object(l["traverseTreeUp"])(t,(function(t){var o;return e.forEach((function(e){e.id===t.id&&(e.children=t.children,e.depth=t.depth,o=e)})),o&&i.addItem("combo",o),!0}))}));var n=o.get("comboGroup");n&&n.sort()},t.prototype.changeItemVisibility=function(t,e){var i=this,o=this.graph;if(Object(c["isString"])(t)&&(t=o.findById(t)),t){if(o.emit("beforeitemvisibilitychange",{item:t,visible:e}),t.changeVisibility(e),t.getType&&t.getType()===W){var n=t.getEdges();Object(c["each"])(n,(function(t){(!e||t.get("source").isVisible()&&t.get("target").isVisible())&&i.changeItemVisibility(t,e)}))}else if(t.getType&&t.getType()===U){var r=o.get("comboTrees"),a=t.get("id"),s=[],h=!1;(r||[]).forEach((function(t){h||t.children&&0!==t.children.length&&Object(l["traverseTree"])(t,(function(t){return t.id!==a||(s=t.children,h=!0,!1)}))})),s&&(!e||e&&!t.getModel().collapsed)&&s.forEach((function(t){var n=o.findById(t.id);i.changeItemVisibility(n,e)}));n=t.getEdges();Object(c["each"])(n,(function(t){(!e||t.get("source").isVisible()&&t.get("target").isVisible())&&i.changeItemVisibility(t,e)}))}return o.emit("afteritemvisibilitychange",{item:t,visible:e}),t}console.warn("The item to be shown or hidden does not exist!")},t.prototype.destroy=function(){this.graph=null,this.destroyed=!0},t}(),K=H,J=null,Q=function(){function t(t){this.graph=t,this.cachedStates={enabled:{},disabled:{}},this.destroyed=!1}return t.checkCache=function(t,e,i){if(i[e]){var o=i[e].indexOf(t);o>=0&&i[e].splice(o,1)}},t.cacheState=function(t,e,i){i[e]||(i[e]=[]),i[e].push(t)},t.prototype.updateState=function(e,i,o){var n=this,r=t.checkCache,a=t.cacheState;if(!e.destroyed){var s=this.cachedStates,c=s.enabled,h=s.disabled;o?(r(e,i,h),a(e,i,c)):(r(e,i,c),a(e,i,h)),J&&clearTimeout(J),J=setTimeout((function(){J=null,n.updateGraphStates()}),16)}},t.prototype.updateStates=function(t,e,i){var o=this;Object(c["isString"])(e)?this.updateState(t,e,i):e.forEach((function(e){o.updateState(t,e,i)}))},t.prototype.updateGraphStates=function(){var t=this.graph.get("states"),e=this.cachedStates;Object(c["each"])(e.disabled,(function(e,i){t[i]&&(t[i]=t[i].filter((function(t){return e.indexOf(t)<0&&!e.destroyed})))})),Object(c["each"])(e.enabled,(function(e,i){if(t[i]){var o={};t[i].forEach((function(t){t.destroyed||(o[t.get("id")]=!0)})),e.forEach((function(e){if(!e.destroyed){var n=e.get("id");o[n]||(o[n]=!0,t[i].push(e))}}))}else t[i]=e})),this.graph.emit("graphstatechange",{states:t}),this.cachedStates={enabled:{},disabled:{}}},t.prototype.destroy=function(){this.graph=null,this.cachedStates=null,J&&clearTimeout(J),J=null,this.destroyed=!0},t}(),$=Q,tt=i("2ef1"),et=i("ae60"),it=i("737c"),ot=i("e003"),nt=function(){function t(t,e){this.cfg=Object(c["deepMix"])(this.getDefaultCfg(),e),this.graph=t,this.id=this.cfg.id,this.group=this.cfg.group,this.members=this.cfg.members.map((function(e){return Object(c["isString"])(e)?t.findById(e):e})),this.nonMembers=this.cfg.nonMembers.map((function(e){return Object(c["isString"])(e)?t.findById(e):e})),this.setPadding(),this.setType(),this.path=this.calcPath(this.members,this.nonMembers),this.render()}return t.prototype.getDefaultCfg=function(){return{id:"g6-hull",type:"round-convex",members:[],nonMembers:[],style:{fill:"lightblue",stroke:"blue",opacity:.2},padding:10}},t.prototype.setPadding=function(){var t=this.members.length&&this.members[0].getKeyShape().getCanvasBBox().width/2;this.padding=this.cfg.padding>0?this.cfg.padding+t:10+t,this.cfg.bubbleCfg={nodeR0:this.padding-t,nodeR1:this.padding-t,morphBuffer:this.padding-t}},t.prototype.setType=function(){this.type=this.cfg.type,this.members.length<3&&(this.type="round-convex"),"round-convex"!==this.type&&"smooth-convex"!==this.type&&"bubble"!==this.type&&(console.warn("The hull type should be either round-convex, smooth-convex or bubble, round-convex is used by default."),this.type="round-convex")},t.prototype.calcPath=function(t,e){var i,o,n;switch(this.type){case"round-convex":i=Object(it["a"])(t),n=Object(et["roundedHull"])(i.map((function(t){return[t.x,t.y]})),this.padding),o=Object(tt["b"])(n);break;case"smooth-convex":i=Object(it["a"])(t),2===i.length?(n=Object(et["roundedHull"])(i.map((function(t){return[t.x,t.y]})),this.padding),o=Object(tt["b"])(n)):i.length>2&&(n=Object(et["paddedHull"])(i.map((function(t){return[t.x,t.y]})),this.padding),o=Object(et["getClosedSpline"])(n));break;case"bubble":i=Object(ot["a"])(t,e,this.cfg.bubbleCfg),o=i.length>=2&&Object(et["getClosedSpline"])(i);break;default:}return o},t.prototype.render=function(){this.group.addShape("path",{attrs:Object(r["a"])({path:this.path},this.cfg.style),id:this.id,name:this.cfg.id,capture:!1}),this.group.toBack()},t.prototype.addMember=function(t){if(t){Object(c["isString"])(t)&&(t=this.graph.findById(t)),this.members.push(t);var e=this.nonMembers.indexOf(t);return e>-1&&this.nonMembers.splice(e,1),this.updateData(this.members,this.nonMembers),!0}},t.prototype.addNonMember=function(t){if(t){Object(c["isString"])(t)&&(t=this.graph.findById(t)),this.nonMembers.push(t);var e=this.members.indexOf(t);return e>-1&&this.members.splice(e,1),this.updateData(this.members,this.nonMembers),!0}},t.prototype.removeMember=function(t){if(t){Object(c["isString"])(t)&&(t=this.graph.findById(t));var e=this.members.indexOf(t);return e>-1&&(this.members.splice(e,1),this.updateData(this.members,this.nonMembers),!0)}},t.prototype.removeNonMember=function(t){if(t){Object(c["isString"])(t)&&(t=this.graph.findById(t));var e=this.nonMembers.indexOf(t);return e>-1&&(this.nonMembers.splice(e,1),this.updateData(this.members,this.nonMembers),!0)}},t.prototype.updateData=function(t,e){var i=this;this.group.findById(this.id).remove(),t&&(this.members=t.map((function(t){return Object(c["isString"])(t)?i.graph.findById(t):t}))),e&&(this.nonMembers=e.map((function(t){return Object(c["isString"])(t)?i.graph.findById(t):t}))),this.path=this.calcPath(this.members,this.nonMembers),this.render()},t.prototype.updateStyle=function(t){var e=this.group.findById(this.id);e.attr(Object(r["a"])({},t))},t.prototype.updateCfg=function(t){var e=this;this.cfg=Object(c["deepMix"])(this.cfg,t),this.id=this.cfg.id,this.group=this.cfg.group,t.members&&(this.members=this.cfg.members.map((function(t){return Object(c["isString"])(t)?e.graph.findById(t):t}))),t.nonMembers&&(this.nonMembers=this.cfg.nonMembers.map((function(t){return Object(c["isString"])(t)?e.graph.findById(t):t}))),this.setPadding(),this.setType(),this.path=this.calcPath(this.members,this.nonMembers),this.render()},t.prototype.contain=function(t){var e,i,o=this;e=Object(c["isString"])(t)?this.graph.findById(t):t;var n=e.getKeyShape();if("path"===e.get("type"))i=Object(et["pathToPoints"])(n.attr("path"));else{var r=n.getCanvasBBox();i=[[r.minX,r.minY],[r.maxX,r.minY],[r.maxX,r.maxY],[r.minX,r.maxY]]}return i=i.map((function(t){var e=o.graph.getPointByCanvas(t[0],t[1]);return[e.x,e.y]})),Object(d["isPolygonsIntersect"])(i,Object(et["pathToPoints"])(this.path))},t.prototype.destroy=function(){this.group.remove(),this.cfg=null},t}(),rt=nt,at=s["a"].transform,st="node",ct=function(t){function e(e){var i=t.call(this)||this;return i.sortCombos=Object(c["debounce"])((function(){var t=i.get("comboSorted");if(i&&!i.destroyed&&!t){i.set("comboSorted",!0);var e=[],o={},n=i.get("comboTrees");(n||[]).forEach((function(t){Object(l["traverseTree"])(t,(function(t){return e[t.depth]?e[t.depth].push(t.id):e[t.depth]=[t.id],o[t.id]=t.depth,!0}))}));var r=i.getEdges().concat(i.get("vedges"));(r||[]).forEach((function(t){var i=t.getModel(),n=o[i.source]||0,r=o[i.target]||0,a=Math.max(n,r);e[a]?e[a].push(i.id):e[a]=[i.id]})),e.forEach((function(t){if(t&&t.length)for(var e=t.length-1;e>=0;e--){var o=i.findById(t[e]);o&&o.toFront()}}))}}),500,!1),i.cfg=Object(c["deepMix"])(i.getDefaultCfg(),e),i.init(),i.animating=!1,i.destroyed=!1,i.cfg.enabledStack&&(i.undoStack=new h["Stack"](i.cfg.maxStep),i.redoStack=new h["Stack"](i.cfg.maxStep)),i}return Object(r["b"])(e,t),e.prototype.init=function(){this.initCanvas();var t=new x(this),e=new m(this),i=new K(this),o=new $(this);this.set({viewController:t,modeController:e,itemController:i,stateController:o}),this.initLayoutController(),this.initEventController(),this.initGroups(),this.initPlugins()},e.prototype.initGroups=function(){var t=this.get("canvas");if(t){var e=t.get("el"),i=(e||{}).id,o=void 0===i?"g6":i,n=t.addGroup({id:"".concat(o,"-root"),className:f["a"].rootContainerClassName});if(this.get("groupByTypes")){var r=n.addGroup({id:"".concat(o,"-edge"),className:f["a"].edgeContainerClassName}),a=n.addGroup({id:"".concat(o,"-node"),className:f["a"].nodeContainerClassName}),s=n.addGroup({id:"".concat(o,"-combo"),className:f["a"].comboContainerClassName});s.toBack(),this.set({nodeGroup:a,edgeGroup:r,comboGroup:s})}var c=n.addGroup({id:"".concat(o,"-delegate"),className:f["a"].delegateContainerClassName});this.set({delegateGroup:c}),this.set("group",n)}},e.prototype.getDefaultCfg=function(){return{container:void 0,width:void 0,height:void 0,renderer:"canvas",modes:{},plugins:[],data:{},fitViewPadding:10,minZoom:.2,maxZoom:10,event:!0,groupByTypes:!0,directed:!1,autoPaint:!0,nodes:[],edges:[],combos:[],vedges:[],itemMap:{},linkCenter:!1,defaultNode:{},defaultEdge:{},nodeStateStyles:{},edgeStateStyles:{},states:{},animate:!1,animateCfg:{onFrame:void 0,duration:500,easing:"easeLinear"},callback:void 0,enabledStack:!1,maxStep:10,tooltips:[]}},e.prototype.set=function(t,e){return Object(c["isPlainObject"])(t)?this.cfg=Object(r["a"])(Object(r["a"])({},this.cfg),t):this.cfg[t]=e,"enabledStack"!==t||!e||this.undoStack||this.redoStack||(this.undoStack=new h["Stack"](this.cfg.maxStep),this.redoStack=new h["Stack"](this.cfg.maxStep)),this},e.prototype.get=function(t){var e;return null===(e=this.cfg)||void 0===e?void 0:e[t]},e.prototype.getGroup=function(){return this.get("group")},e.prototype.getContainer=function(){return this.get("container")},e.prototype.getMinZoom=function(){return this.get("minZoom")},e.prototype.setMinZoom=function(t){return this.set("minZoom",t)},e.prototype.getMaxZoom=function(){return this.get("maxZoom")},e.prototype.setMaxZoom=function(t){return this.set("maxZoom",t)},e.prototype.getWidth=function(){return this.get("width")},e.prototype.getHeight=function(){return this.get("height")},e.prototype.clearItemStates=function(t,e){Object(c["isString"])(t)&&(t=this.findById(t));var i=this.get("itemController");e||(e=t.get("states")),i.clearItemStates(t,e);var o=this.get("stateController");o.updateStates(t,e,!1)},e.prototype.node=function(t){"function"===typeof t&&this.set("nodeMapper",t)},e.prototype.edge=function(t){"function"===typeof t&&this.set("edgeMapper",t)},e.prototype.combo=function(t){"function"===typeof t&&this.set("comboMapper",t)},e.prototype.findById=function(t){return this.get("itemMap")[t]},e.prototype.find=function(t,e){var i,o=this.get("".concat(t,"s"));return Object(c["each"])(o,(function(t,o){if(e(t,o))return i=t,i})),i},e.prototype.findAll=function(t,e){var i=[];return Object(c["each"])(this.get("".concat(t,"s")),(function(t,o){e(t,o)&&i.push(t)})),i},e.prototype.findAllByState=function(t,e){return this.findAll(t,(function(t){return t.hasState(e)}))},e.prototype.getAnimateCfgWithCallback=function(t){var e,i=t.animateCfg,o=t.callback;if(i)if(e=Object(c["clone"])(i),i.callback){var n=i.callback;e.callback=function(){o(),n()}}else e.callback=o;else e={duration:500,callback:o};return e},e.prototype.translate=function(t,e,i,o){var n=this,r=this.get("group"),a=Object(c["clone"])(r.getMatrix());if(a||(a=[1,0,0,0,1,0,0,0,1]),i){var s=this.getAnimateCfgWithCallback({animateCfg:o,callback:function(){return n.emit("viewportchange",{action:"translate",matrix:r.getMatrix()})}});Object(d["move"])(r,{x:r.getCanvasBBox().x+t,y:r.getCanvasBBox().y+e},i,s||{duration:500,easing:"easeCubic"})}else a=at(a,[["t",t,e]]),r.setMatrix(a),this.emit("viewportchange",{action:"translate",matrix:a}),this.autoPaint()},e.prototype.moveTo=function(t,e,i,o){var n=this.get("group");Object(d["move"])(n,{x:t,y:e},i,o||{duration:500,easing:"easeCubic"}),this.emit("viewportchange",{action:"move",matrix:n.getMatrix()})},e.prototype.fitView=function(t,e,i,o){t&&this.set("fitViewPadding",t);var n=this.get("viewController");e?n.fitViewByRules(e,i,o):n.fitView(i,o),this.autoPaint()},e.prototype.fitCenter=function(t,e){var i=this.get("viewController");i.fitCenter(t,e),this.autoPaint()},e.prototype.addBehaviors=function(t,e){var i=this.get("modeController");return i.manipulateBehaviors(t,e,!0),this},e.prototype.removeBehaviors=function(t,e){var i=this.get("modeController");return i.manipulateBehaviors(t,e,!1),this},e.prototype.updateBehavior=function(t,e,i){var o=this.get("modeController");return o.updateBehavior(t,e,i),this},e.prototype.zoom=function(t,e,i,o){var n=this,r=this.get("group"),a=Object(c["clone"])(r.getMatrix()),s=this.get("minZoom"),h=this.get("maxZoom");if(a||(a=[1,0,0,0,1,0,0,0,1]),a=at(a,e?[["t",-e.x,-e.y],["s",t,t],["t",e.x,e.y]]:[["s",t,t]]),s&&a[0]<s||h&&a[0]>h)return!1;if(i){var l=Object(c["clone"])(r.getMatrix());l||(l=[1,0,0,0,1,0,0,0,1]);var p=l[0],u=p*t,f=this.getAnimateCfgWithCallback({animateCfg:o,callback:function(){return n.emit("viewportchange",{action:"zoom",matrix:r.getMatrix()})}});r.animate((function(t){if(1===t)l=a;else{var i=Object(d["lerp"])(p,u,t)/l[0];l=at(l,e?[["t",-e.x,-e.y],["s",i,i],["t",e.x,e.y]]:[["s",i,i]])}return{matrix:l}}),f)}else r.setMatrix(a),this.emit("viewportchange",{action:"zoom",matrix:a}),this.autoPaint();return!0},e.prototype.zoomTo=function(t,e,i,o){var n=t/this.getZoom();return this.zoom(n,e,i,o)},e.prototype.focusItem=function(t,e,i){var o=this.get("viewController"),n=!1;e?n=!0:void 0===e&&(n=this.get("animate"));var r={};i?r=i:void 0===i&&(r=this.get("animateCfg")),o.focus(t,n,r),this.autoPaint()},e.prototype.autoPaint=function(){this.get("autoPaint")&&this.paint()},e.prototype.paint=function(){this.emit("beforepaint"),this.get("canvas").draw(),this.emit("afterpaint")},e.prototype.getPointByClient=function(t,e){var i=this.get("viewController");return i.getPointByClient(t,e)},e.prototype.getClientByPoint=function(t,e){var i=this.get("viewController");return i.getClientByPoint(t,e)},e.prototype.getPointByCanvas=function(t,e){var i=this.get("viewController");return i.getPointByCanvas(t,e)},e.prototype.getCanvasByPoint=function(t,e){var i=this.get("viewController");return i.getCanvasByPoint(t,e)},e.prototype.getGraphCenterPoint=function(){var t=this.get("group").getCanvasBBox();return{x:(t.minX+t.maxX)/2,y:(t.minY+t.maxY)/2}},e.prototype.getViewPortCenterPoint=function(){return this.getPointByCanvas(this.get("width")/2,this.get("height")/2)},e.prototype.showItem=function(t,e){void 0===e&&(e=!0);var i=this.get("itemController"),o=i.changeItemVisibility(t,!0);if(e&&this.get("enabledStack")){var n=o.getID(),r=o.getType(),a={},s={};switch(r){case"node":a.nodes=[{id:n,visible:!1}],s.nodes=[{id:n,visible:!0}];break;case"edge":a.nodes=[{id:n,visible:!1}],s.edges=[{id:n,visible:!0}];break;case"combo":a.nodes=[{id:n,visible:!1}],s.combos=[{id:n,visible:!0}];break;default:break}this.pushStack("visible",{before:a,after:s})}},e.prototype.hideItem=function(t,e){void 0===e&&(e=!0);var i=this.get("itemController"),o=i.changeItemVisibility(t,!1);if(e&&this.get("enabledStack")){var n=o.getID(),r=o.getType(),a={},s={};switch(r){case"node":a.nodes=[{id:n,visible:!0}],s.nodes=[{id:n,visible:!1}];break;case"edge":a.nodes=[{id:n,visible:!0}],s.edges=[{id:n,visible:!1}];break;case"combo":a.nodes=[{id:n,visible:!0}],s.combos=[{id:n,visible:!1}];break;default:break}this.pushStack("visible",{before:a,after:s})}},e.prototype.refreshItem=function(t){var e=this.get("itemController");e.refreshItem(t)},e.prototype.setAutoPaint=function(t){var e=this;e.set("autoPaint",t);var i=e.get("canvas");i.set("autoDraw",t)},e.prototype.remove=function(t,e){void 0===e&&(e=!0),this.removeItem(t,e)},e.prototype.removeItem=function(t,e){void 0===e&&(e=!0);var i=t;if(Object(c["isString"])(t)&&(i=this.findById(t)),!i&&Object(c["isString"])(t))console.warn("The item ".concat(t," to be removed does not exist!"));else if(i){var o="";if(i.getType&&(o=i.getType()),e&&this.get("enabledStack")){var n=Object(r["a"])(Object(r["a"])({},i.getModel()),{itemType:o}),a={};switch(o){case"node":a.nodes=[n],a.edges=[];for(var s=i.getEdges(),h=s.length-1;h>=0;h--)a.edges.push(Object(r["a"])(Object(r["a"])({},s[h].getModel()),{itemType:"edge"}));break;case"edge":a.edges=[n];break;case"combo":a.combos=[n];break;default:break}this.pushStack("delete",{before:a,after:{}})}if("node"===o){var d=i.getModel();d.comboId&&this.updateComboTree(i,void 0,!1)}var p=this.get("itemController");if(p.removeItem(i),"combo"===o){var u=Object(l["reconstructTree"])(this.get("comboTrees"));this.set("comboTrees",u)}}},e.prototype.innerAddItem=function(t,e,i){var o=this;if(!u(t,e))return!1;if(!e.id||!this.findById(e.id)){var n,a=this.get("comboTrees")||[];if("combo"===t){var s=this.get("itemMap"),h=!1;if(a.forEach((function(o){h||Object(l["traverseTreeUp"])(o,(function(o){if(e.parentId===o.id){h=!0;var a=Object(r["a"])({id:e.id,depth:o.depth+2},e);o.children?o.children.push(a):o.children=[a],e.depth=a.depth,n=i.addItem(t,e)}var c=s[o.id];return h&&c&&c.getType&&"combo"===c.getType()&&i.updateCombo(c,o.children),!0}))})),!h){var d=Object(r["a"])({id:e.id,depth:0},e);e.depth=d.depth,a.push(d),n=i.addItem(t,e)}this.set("comboTrees",a),e.collapsed&&setTimeout((function(){n&&!n.destroyed&&(o.collapseCombo(n,!1),o.updateCombo(n))}),0)}else if("node"===t&&Object(c["isString"])(e.comboId)&&a){var p=this.findById(e.comboId);p&&p.getType&&"combo"!==p.getType()&&console.warn("'".concat(e.comboId,"' is not a id of a combo in the graph, the node will be added without combo.")),n=i.addItem(t,e);var f=this.get("itemMap"),g=!1,y=!1;a.forEach((function(t){y||g||Object(l["traverseTreeUp"])(t,(function(t){if(t.id===e.id)return y=!0,!1;if(e.comboId===t.id&&!y){g=!0;var o=Object(c["clone"])(e);o.itemType="node",t.children?t.children.push(o):t.children=[o],o.depth=t.depth+1}return g&&f[t.id].getType&&"combo"===f[t.id].getType()&&i.updateCombo(f[t.id],t.children),!0}))}))}else n=i.addItem(t,e);if("node"===t&&e.comboId||"combo"===t&&e.parentId){p=this.findById(e.comboId||e.parentId);p&&p.getType&&"combo"===p.getType()&&p.addChild(n)}return n}console.warn("This item exists already. Be sure the id %c".concat(e.id,"%c is unique."),"font-size: 20px; color: red;","")},e.prototype.addItem=function(t,e,i,o){void 0===i&&(i=!0),void 0===o&&(o=!0);var n=this.get("comboSorted");this.set("comboSorted",n&&!o);var a=this.get("itemController"),s=this.innerAddItem(t,e,a);if(!1===s||!0===s)return s;var c=this.get("combos");if(c&&c.length>0&&this.sortCombos(),this.autoPaint(),i&&this.get("enabledStack")){var h=Object(r["a"])(Object(r["a"])({},s.getModel()),{itemType:t}),d={};switch(t){case"node":d.nodes=[h];break;case"edge":d.edges=[h];break;case"combo":d.combos=[h];break;default:break}this.pushStack("add",{before:{},after:d})}return s},e.prototype.addItems=function(t,e,i){void 0===t&&(t=[]),void 0===e&&(e=!0),void 0===i&&(i=!0);var o=this.get("comboSorted");this.set("comboSorted",o&&!i);for(var n=this.get("itemController"),a=[],s=0;s<t.length;s++){var c=t[s];"edge"!==c.type&&"vedge"!==c.type?a.push(this.innerAddItem(c.type,c.model,n)):a.push(void 0)}for(s=0;s<t.length;s++){c=t[s];"edge"!==c.type&&"vedge"!==c.type||(a[s]=this.innerAddItem(c.type,c.model,n))}if(i){var h=this.get("combos");h&&h.length>0&&this.sortCombos()}if(this.autoPaint(),e&&this.get("enabledStack")){var d={nodes:[],edges:[],combos:[]};for(s=0;s<t.length;s++){var l=t[s].type,p=a[s];if(p&&!0!==p){var u=Object(r["a"])(Object(r["a"])({},p.getModel()),{itemType:l});switch(l){case"node":d.nodes.push(u);break;case"edge":d.edges.push(u);break;case"combo":d.combos.push(u);break;default:break}}}this.pushStack("addItems",{before:{},after:d})}return a},e.prototype.add=function(t,e,i,o){return void 0===i&&(i=!0),void 0===o&&(o=!0),this.addItem(t,e,i,o)},e.prototype.updateItem=function(t,e,i){var o=this;void 0===i&&(i=!0);var n,a=this.get("itemController");n=Object(c["isString"])(t)?this.findById(t):t;var s=Object(c["clone"])(n.getModel()),h="";n.getType&&(h=n.getType());var d=Object(r["d"])([],n.getStates(),!0);if("combo"===h&&Object(c["each"])(d,(function(t){return o.setItemState(n,t,!1)})),a.updateItem(n,e),"combo"===h&&Object(c["each"])(d,(function(t){return o.setItemState(n,t,!0)})),i&&this.get("enabledStack")){var l={nodes:[],edges:[],combos:[]},p={nodes:[],edges:[],combos:[]},u=Object(r["a"])({id:s.id},e);switch(h){case"node":l.nodes.push(s),p.nodes.push(u);break;case"edge":l.edges.push(s),p.edges.push(u);break;case"combo":l.combos.push(s),p.combos.push(u);break;default:break}"node"===h&&l.nodes.push(s),this.pushStack("update",{before:l,after:p})}},e.prototype.update=function(t,e,i){void 0===i&&(i=!0),this.updateItem(t,e,i)},e.prototype.setItemState=function(t,e,i){Object(c["isString"])(t)&&(t=this.findById(t));var o=this.get("itemController");o.setItemState(t,e,i);var n=this.get("stateController");Object(c["isString"])(i)?n.updateState(t,"".concat(e,":").concat(i),!0):n.updateState(t,e,i)},e.prototype.priorityState=function(t,e){var i=this.get("itemController");i.priorityState(t,e)},e.prototype.data=function(t){p(t),this.set("data",t)},e.prototype.render=function(){var t=this;this.set("comboSorted",!1);var e=this.get("data");if(this.get("enabledStack")&&this.clearStack(),!e)throw new Error("data must be defined first");var i=e.nodes,o=void 0===i?[]:i,n=e.edges,r=void 0===n?[]:n,a=e.combos,s=void 0===a?[]:a;if(this.clear(!0),this.emit("beforerender"),t.addItems(o.map((function(t){return{type:"node",model:t}})),!1,!1),0!==(null===s||void 0===s?void 0:s.length)){var c=Object(l["plainCombosToTrees"])(s,o);this.set("comboTrees",c),t.addCombos(s)}t.addItems(r.map((function(t){return{type:"edge",model:t}})),!1,!1);var h=t.get("animate");(t.get("fitView")||t.get("fitCenter"))&&t.set("animate",!1);var d=t.get("layoutController");if(d){if(d.layout(p),this.destroyed)return}else p();function p(){(t.get("comboTrees")||[]).forEach((function(e){Object(l["traverseTreeUp"])(e,(function(e){var i=t.findById(e.id);return"combo"===i.getType()&&e.collapsed&&(t.collapseCombo(e.id,!1),t.updateCombo(i)),!0}))})),t.get("fitView")?t.fitView():t.get("fitCenter")&&t.fitCenter(),t.autoPaint(),t.emit("afterrender"),(t.get("fitView")||t.get("fitCenter"))&&t.set("animate",h),setTimeout((function(){var e;null===(e=t.getCombos())||void 0===e||e.forEach((function(t){t.set("animate",!0)}))}),0)}if(!this.get("groupByTypes"))if(s&&0!==s.length)this.sortCombos();else if(e.nodes&&e.edges&&e.nodes.length<e.edges.length){var u=this.getNodes();u.forEach((function(t){t.toFront()}))}else{var f=this.getEdges();f.forEach((function(t){t.toBack()}))}this.get("enabledStack")&&this.pushStack("render")},e.prototype.read=function(t){this.data(t),this.render()},e.prototype.diffItems=function(t,e,i){var o,n=this,r=this.get("itemMap");Object(c["each"])(i,(function(i){if(o=r[i.id],o){if(n.get("animate")&&t===st){var a=o.getContainer().getMatrix();a||(a=[1,0,0,0,1,0,0,0,1]),o.set("originAttrs",{x:a[6],y:a[7]})}n.updateItem(o,i,!1)}else o=n.addItem(t,i,!1);o&&e["".concat(t,"s")].push(o)}))},e.prototype.changeData=function(t,e){var i,o=this;void 0===e&&(e=!0);var n=this,r=t||n.get("data");if(!p(r))return this;e&&this.get("enabledStack")&&this.pushStack("changedata",{before:n.save(),after:r}),this.set("comboSorted",!1),this.removeHulls(),this.getNodes().map((function(t){return n.clearItemStates(t)})),this.getEdges().map((function(t){return n.clearItemStates(t)}));var a=this.get("canvas"),s=a.get("localRefresh");a.set("localRefresh",!1),n.get("data")||(n.data(r),n.render());var h=this.get("itemMap"),d={nodes:[],edges:[]},u=r.combos;if(u){var f=Object(l["plainCombosToTrees"])(u,r.nodes);this.set("comboTrees",f)}else this.set("comboTrees",[]);this.diffItems("node",d,r.nodes),Object(c["each"])(h,(function(t,e){h[e].getModel().depth=0,t.getType&&"edge"===t.getType()||(t.getType&&"combo"===t.getType()?(delete h[e],t.destroy()):d.nodes.indexOf(t)<0&&(delete h[e],n.remove(t,!1)))}));for(var g=this.getCombos(),y=g.length,m=y-1;m>=0;m--)g[m].destroyed&&g.splice(m,1);u&&(n.addCombos(u),this.get("groupByTypes")||this.sortCombos()),this.diffItems("edge",d,r.edges),Object(c["each"])(h,(function(t,e){(!t.getType||"node"!==t.getType()&&"combo"!==t.getType())&&d.edges.indexOf(t)<0&&(delete h[e],n.remove(t,!1))})),(this.get("comboTrees")||[]).forEach((function(t){Object(l["traverseTreeUp"])(t,(function(t){var e=o.findById(t.id);return"combo"===e.getType()&&t.collapsed&&o.collapseCombo(t.id,!1),!0}))})),this.set({nodes:d.nodes,edges:d.edges});var b=this.get("layoutController");return b&&(b.changeData((function(){setTimeout((function(){var t;null===(t=n.getCombos())||void 0===t||t.forEach((function(t){t.set("animate",!0)}))}),0)})),n.get("animate")&&!b.getLayoutType()?(n.positionsAnimate(),null===(i=n.getCombos())||void 0===i||i.forEach((function(t){return t.set("animate",!0)}))):n.autoPaint()),setTimeout((function(){a.set("localRefresh",s)}),16),this},e.prototype.addCombos=function(t){var e=this,i=e.get("comboTrees"),o=this.get("itemController");o.addCombos(i,t)},e.prototype.createCombo=function(t,e){var i=this;this.set("comboSorted",!1);var o,n="";if(t){if(Object(c["isString"])(t))n=t,o={id:t};else{if(n=t.id,!n)return void console.warn("Create combo failed. Please assign a unique string id for the adding combo.");o=t}var r=e.map((function(t){var e=i.findById(t),o=e.getModel(),r="";e.getType&&(r=e.getType());var a={id:e.getID(),itemType:r};return"combo"===r?(a.parentId=n,o.parentId=n):"node"===r&&(a.comboId=n,o.comboId=n),a}));o.children=r,this.addItem("combo",o,!1),this.set("comboSorted",!1);var a=this.get("comboTrees");(a||[]).forEach((function(t){Object(l["traverseTreeUp"])(t,(function(t){return t.id!==n||(t.itemType="combo",t.children=r,!1)}))})),a&&this.sortCombos()}},e.prototype.uncombo=function(t){var e,i=this,o=this,n=t;if(Object(c["isString"])(t)&&(n=this.findById(t)),!n||n.getType&&"combo"!==n.getType())console.warn("The item is not a combo!");else{var r=n.getModel().parentId,a=o.get("comboTrees");a||(a=[]);var s,h=this.get("itemMap"),d=n.get("id"),p=[],u=this.get("combos"),f=this.findById(r);if(a.forEach((function(t){s||Object(l["traverseTreeUp"])(t,(function(t){var e;if(t.id===d){s=t;var o=n.getEdges().map((function(t){return t.getID()}));o.forEach((function(t){i.removeItem(t,!1)}));var a=u.indexOf(n);u.splice(a,1),delete h[d],n.destroy(),i.emit("afterremoveitem",{item:n,type:"combo"})}if(r&&s&&t.id===r){f.removeCombo(n),p=t.children;a=p.indexOf(s);return-1!==a&&p.splice(a,1),null===(e=s.children)||void 0===e||e.forEach((function(t){var e=i.findById(t.id),o=e.getModel();e.getType&&"combo"===e.getType()?(t.parentId=r,delete t.comboId,o.parentId=r,delete o.comboId):e.getType&&"node"===e.getType()&&(t.comboId=r,o.comboId=r),f.addChild(e),p.push(t)})),!1}return!0}))})),!r&&s){var g=a.indexOf(s);a.splice(g,1),null===(e=s.children)||void 0===e||e.forEach((function(t){t.parentId=void 0;var e=i.findById(t.id).getModel();delete e.parentId,delete e.comboId,"node"!==t.itemType&&a.push(t)}))}}},e.prototype.updateCombos=function(t){var e=this;void 0===t&&(t=!1);var i=this,o=this.get("comboTrees"),n=i.get("itemController"),a=i.get("itemMap");(o||[]).forEach((function(i){Object(l["traverseTreeUp"])(i,(function(i){var o;if(!i)return!0;var s=a[i.id];if("combo"===(null===(o=null===s||void 0===s?void 0:s.getType)||void 0===o?void 0:o.call(s))){var h=Object(r["d"])([],s.getStates(),!0);Object(c["each"])(h,(function(t){return e.setItemState(s,t,!1)})),n.updateCombo(s,i.children,t),Object(c["each"])(h,(function(t){return e.setItemState(s,t,!0)}))}return!0}))})),i.sortCombos()},e.prototype.updateCombo=function(t){var e,i=this,o=this,n=t;if(Object(c["isString"])(t)&&(n=this.findById(t)),!n||n.getType&&"combo"!==n.getType())console.warn("The item to be updated is not a combo!");else{e=n.get("id");var a=this.get("comboTrees"),s=o.get("itemController"),h=o.get("itemMap");(a||[]).forEach((function(t){Object(l["traverseTreeUp"])(t,(function(t){if(!t)return!0;var o=h[t.id];if(e===t.id&&o&&o.getType&&"combo"===o.getType()){var n=Object(r["d"])([],o.getStates(),!0);Object(c["each"])(n,(function(t){o.getStateStyle(t)&&i.setItemState(o,t,!1)})),s.updateCombo(o,t.children),Object(c["each"])(n,(function(t){o.getStateStyle(t)&&i.setItemState(o,t,!0)})),e&&(e=t.parentId)}return!0}))}))}},e.prototype.updateComboTree=function(t,e,i){void 0===i&&(i=!0);var o,n=this;this.set("comboSorted",!1),o=Object(c["isString"])(t)?n.findById(t):t;var r=o.getModel(),a=r.comboId||r.parentId,s="";if(o.getType&&(s=o.getType()),e&&"combo"===s){var h,d=this.get("comboTrees"),p=!0;if((d||[]).forEach((function(t){h||Object(l["traverseTree"])(t,(function(t){if(!h)return t.id===o.getID()&&(h=t),!0}))})),Object(l["traverseTree"])(h,(function(t){return t.id!==e||(p=!1,!1)})),!p)return void console.warn("Failed to update the combo tree! The parentId points to a descendant of the combo!")}if(i&&this.get("enabledStack")){var u={},f={};"combo"===s?(u.combos=[{id:r.id,parentId:r.parentId}],f.combos=[{id:r.id,parentId:e}]):"node"===s&&(u.nodes=[{id:r.id,parentId:r.comboId}],f.nodes=[{id:r.id,parentId:e}]),this.pushStack("updateComboTree",{before:u,after:f})}if(r.parentId||r.comboId){var g=this.findById(r.parentId||r.comboId);g&&g.removeChild(o)}if("combo"===s?r.parentId=e:"node"===s&&(r.comboId=e),e){var y=this.findById(e);y&&y.addChild(o)}if(a){y=this.findById(a);y&&y.removeChild(o)}var m=Object(l["reconstructTree"])(this.get("comboTrees"),r.id,e);this.set("comboTrees",m),this.updateCombos()},e.prototype.save=function(){var t=[],e=[],i=[];return Object(c["each"])(this.get("nodes"),(function(e){t.push(e.getModel())})),Object(c["each"])(this.get("edges"),(function(t){e.push(t.getModel())})),Object(c["each"])(this.get("combos"),(function(t){i.push(t.getModel())})),{nodes:t,edges:e,combos:i}},e.prototype.changeSize=function(t,e){var i=this.get("viewController");return i.changeSize(t,e),this},e.prototype.refresh=function(){var t=this;if(t.emit("beforegraphrefresh"),t.get("animate"))t.positionsAnimate();else{var e=t.get("nodes"),i=t.get("edges"),o=t.get("edges");Object(c["each"])(e,(function(t){t.refresh()})),Object(c["each"])(i,(function(t){t.refresh()})),Object(c["each"])(o,(function(t){t.refresh()}))}t.emit("aftergraphrefresh"),t.autoPaint()},e.prototype.getNodes=function(){return this.get("nodes")},e.prototype.getEdges=function(){return this.get("edges")},e.prototype.getCombos=function(){return this.get("combos")},e.prototype.getComboChildren=function(t){if(Object(c["isString"])(t)&&(t=this.findById(t)),t&&(!t.getType||"combo"===t.getType()))return t.getChildren();console.warn("The combo does not exist!")},e.prototype.positionsAnimate=function(t){var e=this;e.emit("beforeanimate");var i=e.get("animateCfg"),o=i.onFrame,n=t?e.getNodes().concat(e.getCombos()):e.getNodes(),r=n.map((function(t){var e=t.getModel();return{id:e.id,x:e.x,y:e.y}}));e.isAnimating()&&e.stopAnimate();var a=e.get("canvas");a.animate((function(i){Object(c["each"])(r,(function(t){var n=e.findById(t.id);if(n&&!n.destroyed){var r=n.get("originAttrs"),a=n.get("model"),s=n.getContainer().getMatrix();if(void 0!==r&&null!==r||(s&&(r={x:s[6],y:s[7]}),n.set("originAttrs",r||0)),o){var c=o(n,i,t,r||{x:0,y:0});n.set("model",Object.assign(a,c))}else r?(a.x=r.x+(t.x-r.x)*i,a.y=r.y+(t.y-r.y)*i):(a.x=t.x,a.y=t.y)}})),e.refreshPositions(t)}),{duration:i.duration,easing:i.easing,callback:function(){Object(c["each"])(n,(function(t){t.set("originAttrs",null)})),i.callback&&i.callback(),e.emit("afteranimate"),e.animating=!1}})},e.prototype.refreshPositions=function(t){var e=this;e.emit("beforegraphrefreshposition");var i,o=e.get("nodes"),n=e.get("edges"),r=e.get("vedges"),a=e.get("combos"),s={},h=function(t){Object(c["each"])(t,(function(t){i=t.getModel();var e=t.get("originAttrs");if(!e||i.x!==e.x||i.y!==e.y){var o=t.updatePosition({x:i.x,y:i.y});s[i.id]=o,i.comboId&&(s[i.comboId]=s[i.comboId]||o)}}))};h(o),a&&0!==a.length&&(t?(h(a),e.updateCombos()):e.updateCombos()),Object(c["each"])(n,(function(t){var e=t.getSource().getModel(),i=t.getTarget();if(!Object(c["isPlainObject"])(i)){var o=i.getModel();(s[e.id]||s[o.id]||t.getModel().isComboEdge)&&t.refresh()}})),Object(c["each"])(r,(function(t){t.refresh()})),e.emit("aftergraphrefreshposition"),e.autoPaint()},e.prototype.stopAnimate=function(){this.get("canvas").stopAnimate()},e.prototype.isAnimating=function(){return this.animating},e.prototype.getZoom=function(){var t=this.get("group").getMatrix();return t?t[0]:1},e.prototype.getCurrentMode=function(){var t=this.get("modeController");return t.getMode()},e.prototype.setMode=function(t){var e=this.get("modeController");return e.setMode(t),this},e.prototype.clear=function(t){var e;return void 0===t&&(t=!1),null===(e=this.get("canvas"))||void 0===e||e.clear(),this.initGroups(),this.set({itemMap:{},nodes:[],edges:[],groups:[],combos:[],comboTrees:[]}),t||this.emit("afterrender"),this},e.prototype.updateLayout=function(t,e,i){var o=this,n=this.get("layoutController");if(Object(c["isString"])(t)&&(t={type:t}),e){var r=i;r||(r="begin"===e?{x:0,y:0}:{x:this.getWidth()/2,y:this.getHeight()/2}),r=this.getPointByCanvas(r.x,r.y);var a=["force","gForce","fruchterman"];a.includes(t.type)||!t.type&&a.includes(null===n||void 0===n?void 0:n.layoutType)?t.center=[r.x,r.y]:this.once("afterlayout",(function(t){var i=o.getGroup().getMatrix()||[1,0,0,0,1,0,0,0,1];r.x=r.x*i[0]+i[6],r.y=r.y*i[0]+i[7];var n=o.getGroup().getCanvasBBox(),a=n.minX,s=n.maxX,c=n.minY,h=n.maxY,d={x:(a+s)/2,y:(c+h)/2};"begin"===e&&(d.x=a,d.y=c),o.translate(r.x-d.x,r.y-d.y)}))}var s=this.get("layout"),h={};Object.assign(h,s,t),this.set("layout",h),n&&(n.isLayoutTypeSame(h)&&h.gpuEnabled===s.gpuEnabled?n.updateLayoutCfg(h):n.changeLayout(h))},e.prototype.destroyLayout=function(){var t=this.get("layoutController");null===t||void 0===t||t.destroyLayout()},e.prototype.layout=function(){var t,e=this.get("layoutController"),i=this.get("layout");i&&e&&(i.workerEnabled?e.layout():(null===(t=e.layoutMethods)||void 0===t?void 0:t.length)?e.relayout(!0):e.layout())},e.prototype.collapseCombo=function(t,e){var i=this;if(void 0===e&&(e=!0),!this.destroyed)if(Object(c["isString"])(t)&&(t=this.findById(t)),t){this.emit("beforecollapseexpandcombo",{action:"expand",item:t});var o=t.getModel(),n=this.get("itemController");n.collapseCombo(t,e),o.collapsed=!0;var r=this.getEdges().concat(this.get("vedges")),a=[],s=this.get("comboTrees"),h=!1;(s||[]).forEach((function(t){h||Object(l["traverseTree"])(t,(function(t){if(h&&t.depth<=o.depth)return!1;if(o.id===t.id&&(h=!0),h){var e=i.findById(t.id);e&&e.getType&&"combo"===e.getType()&&(a=a.concat(e.getNodes()),a=a.concat(e.getCombos()))}return!0}))}));var d={};r.forEach((function(t){var e=t.getModel(),n=e.isVEdge,r=e.size,s=void 0===r?1:r;if(!t.isVisible()||n){var c,h=t.getSource(),l=t.getTarget(),p=null;if(h.getModel().id===o.id||a.includes(h)&&!a.includes(l)?(p=l,c=!1):(l.getModel().id===o.id||!a.includes(h)&&a.includes(l))&&(p=h,c=!0),p){if(n)return void i.removeItem(t,!1);var u=p.getModel();while(!p.isVisible()){var f=u.parentId,g=u.comboId,y=f||g;if(p=i.findById(y),!p||!y)return;u=p.getModel()}var m=u.id,b=c?{source:m,target:o.id,size:s,isVEdge:!0}:{source:o.id,target:m,size:s,isVEdge:!0},v="".concat(b.source,"-").concat(b.target);if(d[v])return void(d[v].size+=s);d[v]=b}}})),this.addItems(Object.values(d).map((function(t){return{type:"vedge",model:t}})),!1),this.emit("aftercollapseexpandcombo",{action:"collapse",item:t})}else console.warn("The combo to be collapsed does not exist!")},e.prototype.expandCombo=function(t,e){var i=this;if(void 0===e&&(e=!0),Object(c["isString"])(t)&&(t=this.findById(t)),!t||t.getType&&"combo"!==t.getType())console.warn("The combo to be collapsed does not exist!");else{this.emit("beforecollapseexpandcombo",{action:"expand",item:t});var o=t.getModel(),n=this.get("itemController");n.expandCombo(t,e),o.collapsed=!1;var r=this.getEdges().concat(this.get("vedges")),a=[],s=this.get("comboTrees"),h=!1;(s||[]).forEach((function(t){h||Object(l["traverseTree"])(t,(function(t){if(h&&t.depth<=o.depth)return!1;if(o.id===t.id&&(h=!0),h){var e=i.findById(t.id);e&&e.getType&&"combo"===e.getType()&&(a=a.concat(e.getNodes()),a=a.concat(e.getCombos()))}return!0}))}));var d={};r.forEach((function(t){if(!t.isVisible()||t.getModel().isVEdge){var e,n=t.getSource(),r=t.getTarget(),s=n.get("id"),c=r.get("id"),h=null;if(s===o.id||a.includes(n)&&!a.includes(r)?(h=r,e=!1):c===o.id||!a.includes(n)&&a.includes(r)?(h=n,e=!0):a.includes(n)&&a.includes(r)&&n.isVisible()&&r.isVisible()&&t.show(),h){var l=t.getModel(),p=l.isVEdge,u=l.size,f=void 0===u?1:u;if(p)return void i.removeItem(t,!1);var g=h.getModel();while(!h.isVisible()){var y=g.parentId,m=g.comboId,b=y||m;if(h=i.findById(b),!h||!b)return;g=h.getModel()}var v=g.id,x=e?r:n,O=x.getModel();while(!x.isVisible()){var j=g.parentId,S=g.comboId,M=j||S;if(x=i.findById(M),!x||!M)return;if(O.comboId===o.id||O.parentId===o.id)break;O=x.getModel()}var k=O.id;if(v){var w=e?{source:v,target:k,isVEdge:!0,size:f}:{source:k,target:v,isVEdge:!0,size:f},C="".concat(w.source,"-").concat(w.target);if(d[C])return void(d[C].size+=f);d[C]=w}}}})),this.addItems(Object.values(d).map((function(t){return{type:"vedge",model:t}})),!1),this.emit("aftercollapseexpandcombo",{action:"expand",item:t})}},e.prototype.collapseExpandCombo=function(t,e){if(void 0===e&&(e=!0),Object(c["isString"])(t)&&(t=this.findById(t)),t&&(!t.getType||"combo"===t.getType())){var i=t.getModel(),o=this.findById(i.parentId);while(o){var n=o.getModel();if(n.collapsed)return console.warn("Fail to expand the combo since it's ancestor combo is collapsed."),void(o=void 0);o=this.findById(n.parentId)}var r=i.collapsed;r?this.expandCombo(t,e):this.collapseCombo(t,e),this.updateCombo(t)}},e.prototype.getNeighbors=function(t,e){var i=t;return Object(c["isString"])(t)&&(i=this.findById(t)),i.getNeighbors(e)},e.prototype.getNodeDegree=function(t,e,i){void 0===e&&(e=void 0),void 0===i&&(i=!1);var o=t;Object(c["isString"])(t)&&(o=this.findById(t));var n=this.get("degrees");n&&!i||(n=Object(h["getDegree"])(this.save()),this.set("degrees",n));var r=n[o.getID()],a=0;if(!r)return 0;switch(e){case"in":a=r.inDegree;break;case"out":a=r.outDegree;break;case"all":a=r;break;default:a=r.degree;break}return a},e.prototype.getUndoStack=function(){return this.undoStack},e.prototype.getRedoStack=function(){return this.redoStack},e.prototype.getStackData=function(){return this.get("enabledStack")?{undoStack:this.undoStack.toArray(),redoStack:this.redoStack.toArray()}:null},e.prototype.clearStack=function(){this.get("enabledStack")&&(this.undoStack.clear(),this.redoStack.clear())},e.prototype.pushStack=function(t,e,i){if(void 0===t&&(t="update"),void 0===i&&(i="undo"),this.get("enabledStack")){var o=e?Object(c["clone"])(e):{before:{},after:Object(c["clone"])(this.save())};"redo"===i?this.redoStack.push({action:t,data:o}):this.undoStack.push({action:t,data:o}),this.emit("stackchange",{undoStack:this.undoStack,redoStack:this.redoStack})}else console.warn("请先启用 undo & redo 功能，在实例化 Graph 时候配置 enabledStack: true !")},e.prototype.getAdjMatrix=function(t,e){void 0===t&&(t=!0),void 0===e&&(e=this.get("directed"));var i=this.get("adjMatrix");return i&&t||(i=Object(h["getAdjMatrix"])(this.save(),e),this.set("adjMatrix",i)),i},e.prototype.getShortestPathMatrix=function(t,e){void 0===t&&(t=!0),void 0===e&&(e=this.get("directed"));var i=this.get("adjMatrix"),o=this.get("shortestPathMatrix");return i&&t||(i=Object(h["getAdjMatrix"])(this.save(),e),this.set("adjMatrix",i)),o&&t||(o=Object(h["floydWarshall"])(this.save(),e),this.set("shortestPathMatrix",o)),o},e.prototype.on=function(e,i,o){return t.prototype.on.call(this,e,i,o)},e.prototype.destroy=function(){var t,e,i,o,n;this.clear(),this.clearStack(),null===(t=this.get("itemController"))||void 0===t||t.destroy(),null===(e=this.get("modeController"))||void 0===e||e.destroy(),null===(i=this.get("viewController"))||void 0===i||i.destroy(),null===(o=this.get("stateController"))||void 0===o||o.destroy(),null===(n=this.get("canvas"))||void 0===n||n.destroy(),this.cfg=null,this.destroyed=!0,this.redoStack=null,this.undoStack=null},e.prototype.createHull=function(t){if(t.members&&!(t.members.length<1)){var e=this.get("hullGroup"),i=this.get("hullMap");if(i||(i={},this.set("hullMap",i)),e&&!e.get("destroyed")||(e=this.get("group").addGroup({id:"hullGroup"}),e.toBack(),this.set("hullGroup",e)),i[t.id])return console.warn("Existed hull id."),i[t.id];var o=e.addGroup({id:"".concat(t.id,"-container")}),n=new rt(this,Object(r["a"])(Object(r["a"])({},t),{group:o})),a=n.id;return i[a]=n,n}console.warn("Create hull failed! The members is empty.")},e.prototype.getHulls=function(){return this.get("hullMap")},e.prototype.getHullById=function(t){return this.get("hullMap")[t]},e.prototype.removeHull=function(t){var e,i;i=Object(c["isString"])(t)?this.getHullById(t):t,null===(e=this.get("hullMap"))||void 0===e||delete e[i.id],i.destroy()},e.prototype.removeHulls=function(){var t=this.getHulls();t&&Object.keys(t).length&&(Object.keys(t).forEach((function(e){var i=t[e];i.destroy()})),this.set("hullMap",{}))},e}(a["a"]),ht=ct,dt=i("edbf"),lt=["#5F95FF","#61DDAA","#65789B","#F6BD16","#7262FD","#78D3F8","#9661BC","#F6903D","#008685","#F08BB4"],pt=s["a"].transform,ut=Object(r["a"])(Object(r["a"])(Object(r["a"])(Object(r["a"])(Object(r["a"])(Object(r["a"])({},b),l),et),d),o),{transform:pt,mat3:s["b"]}),ft=ut,gt=function(){function t(t){this.graph=t,this.layoutCfg=t.get("layout")||{},this.layoutType=this.getLayoutType(),this.layoutMethods=[],this.initLayout()}return t.prototype.initLayout=function(){},t.prototype.getLayoutType=function(){return this.getLayoutCfgType(this.layoutCfg)},t.prototype.getLayoutCfgType=function(t){var e=t.type;if(e)return e;var i=t.pipes;return Array.isArray(i)?i.map((function(t){return(null===t||void 0===t?void 0:t.type)||""})):null},t.prototype.isLayoutTypeSame=function(t){var e=this.getLayoutCfgType(t);return Array.isArray(this.layoutType)?this.layoutType.every((function(t,i){return t===e[i]})):(null===t||void 0===t?void 0:t.type)===this.layoutType},t.prototype.refreshLayout=function(){var t=this,e=t.graph,i=t.layoutType;e&&(e.get("animate")?e.positionsAnimate("comboCombined"===i):e.refreshPositions("comboCombined"===i))},t.prototype.changeLayout=function(t){this.layoutCfg=t,this.layoutType=t.type||this.layoutType,this.destoryLayoutMethods(),this.layout()},t.prototype.changeData=function(t){this.destoryLayoutMethods(),this.layout(t)},t.prototype.destoryLayoutMethods=function(){var t=this.layoutMethods;null===t||void 0===t||t.forEach((function(t){t.destroy()})),this.layoutMethods=[]},t.prototype.destroyLayout=function(){var t=this.graph;this.destoryLayoutMethods(),t.set("layout",void 0),this.layoutCfg=void 0,this.layoutType=void 0,this.layoutMethods=void 0},t.prototype.setDataFromGraph=function(){for(var t=[],e=[],i=[],o=[],n=[],r=[],a=[],s=this.graph.getNodes(),c=this.graph.getEdges(),h=this.graph.getCombos(),d=s.length,l=0;l<d;l++){var p=s[l];if(p&&!p.destroyed){var u=p.getModel();p.isVisible()?t.push(u):e.push(u)}}var f=c.length;for(l=0;l<f;l++){var g=c[l];if(g&&!g.destroyed){u=g.getModel();g.isVisible()?u.isComboEdge?n.push(u):i.push(u):o.push(u)}}var y=h.length;for(l=0;l<y;l++){var m=h[l];if(!m.destroyed){u=m.getModel();m.isVisible()?r.push(u):a.push(u)}}return{nodes:t,hiddenNodes:e,edges:i,hiddenEdges:o,combos:r,hiddenCombos:a,comboEdges:n}},t.prototype.reLayoutMethod=function(t,e){var i=this;return new Promise((function(o,n){var r=i.graph,a=null===e||void 0===e?void 0:e.type;e.onLayoutEnd=function(){r.emit("aftersublayout",{type:a}),o()},t.init(i.data),"force"===a&&(t.ticking=!1,t.forceSimulation.stop()),r.emit("beforesublayout",{type:a}),t.execute(),t.isCustomLayout&&e.onLayoutEnd&&e.onLayoutEnd()}))},t.prototype.relayout=function(t){var e=this,i=this,o=i.graph,n=i.layoutMethods,r=i.layoutCfg;if(t){this.data=this.setDataFromGraph();var a=this.data.nodes;if(!a)return!1;this.initPositions(r.center,a)}o.emit("beforelayout");var s=Promise.resolve();null===n||void 0===n||n.forEach((function(t,i){var o=r[i]||r;s=s.then((function(){var a,s=e.reLayoutMethod(t,o);return i===n.length-1&&(null===(a=r.onAllLayoutEnd)||void 0===a||a.call(r)),s}))}))},t.prototype.filterLayoutData=function(t,e){var i,o,n=t.nodes,a=t.edges,s=Object(r["c"])(t,["nodes","edges"]);if(!n)return t;i=Object(c["isFunction"])(null===e||void 0===e?void 0:e.nodesFilter)?e.nodesFilter:function(){return!0};var h=n.filter(i);if(Object(c["isFunction"])(null===e||void 0===e?void 0:e.edgesFilter))o=e.edgesFilter;else{var d=h.reduce((function(t,e){return t[e.id]=!0,t}),{});o=function(t){return d[t.source]&&d[t.target]}}return Object(r["a"])({nodes:h,edges:a.filter(o)},s)},t.prototype.getLayoutBBox=function(t){var e=this.graph,i=Object(c["groupBy"])(e.getNodes(),(function(t){return t.getModel().layoutOrder})),o=Object.values(i).map((function(t){var e=Object(b["calculationItemsBBox"])(t);return e.size=[e.width,e.height],e})),n=Object.values(Object(c["groupBy"])(t,"layoutOrder"));return{groupNodes:n,layoutNodes:o}},t.prototype.layoutAnimate=function(){},t.prototype.moveToZero=function(){var t=this.graph,e=t.get("data"),i=e.nodes;if(void 0!==i[0].x&&null!==i[0].x&&!Object(b["isNaN"])(i[0].x)){for(var o=[0,0],n=i.length,r=0;r<n;r++){var a=i[r];o[0]+=a.x,o[1]+=a.y}o[0]/=i.length,o[1]/=i.length;for(r=0;r<n;r++){a=i[r];a.x-=o[0],a.y-=o[1]}}},t.prototype.initPositions=function(t,e){var i=this.graph;if(!e)return!1;var o=e?e.length:0;if(o){var n=.85*i.get("width"),r=.85*i.get("height"),a=Math.ceil(Math.sqrt(o)*(n/r)),s=Math.ceil(o/a),c=n/(a-1),h=r/(s-1);isFinite(c)&&c||(c=0),isFinite(h)&&c||(h=0);for(var d=t[0]-n/2,l=t[1]-r/2,p=!0,u=0;u<o;u++){var f=e[u];Object(b["isNaN"])(+f.x)&&(p=!1,f.x=u%a*c+d),Object(b["isNaN"])(+f.y)&&(p=!1,f.y=Math.floor(u/a)*h+l)}return p}},t.prototype.destroy=function(){this.graph=null,this.destoryLayoutMethods(),this.destroyed=!0},t}(),yt=gt,mt=function(){function t(t){this.graph=t,this.destroyed=!1,this.initEvents()}return t}(),bt=mt,vt=(i("0122"),dt["c"].registerNode),xt=dt["c"].registerEdge,Ot=dt["c"].registerCombo,jt=n["a"].registerBehavior,St=f["a"];f["a"].version,dt["c"],dt["c"].registerNode,dt["c"].registerEdge,dt["c"].registerCombo,n["a"].registerBehavior,dt["a"],dt["b"]},6618:function(t,e,i){"use strict";i.r(e),i.d(e,"uniqueId",(function(){return r})),i.d(e,"formatPadding",(function(){return a})),i.d(e,"cloneEvent",(function(){return s})),i.d(e,"isViewportChanged",(function(){return c})),i.d(e,"isNaN",(function(){return h})),i.d(e,"calculationItemsBBox",(function(){return d})),i.d(e,"processParallelEdges",(function(){return l}));var o=i("8937"),n=i("0122"),r=function(t){return"".concat(t,"-").concat(Math.random()).concat(Date.now())},a=function(t){if(Object(o["isArray"])(t))switch(t.length){case 4:return t;case 3:return t.push(t[1]),t;case 2:return t.concat(t);case 1:return[t[0],t[0],t[0],t[0]];default:return[0,0,0,0]}if(Object(o["isNumber"])(t))return[t,t,t,t];if(Object(o["isString"])(t)){var e=parseInt(t,10);return[e,e,e,e]}return[0,0,0,0]},s=function(t){var e=new n["a"](t.type,t);return e.clientX=t.clientX,e.clientY=t.clientY,e.x=t.x,e.y=t.y,e.target=t.target,e.currentTarget=t.currentTarget,e.bubbles=!0,e.item=t.item,e},c=function(t){if(!t)return!1;for(var e=9,i=[1,0,0,0,1,0,0,0,1],o=0;o<e;o++)if(t[o]!==i[o])return!0;return!1},h=function(t){return Number.isNaN(Number(t))},d=function(t){for(var e=1/0,i=-1/0,o=1/0,n=-1/0,r=0;r<t.length;r++){var a=t[r],s=a.getBBox(),c=s.minX,h=s.minY,d=s.maxX,l=s.maxY;c<e&&(e=c),h<o&&(o=h),d>i&&(i=d),l>n&&(n=l)}var p=Math.floor(e),u=Math.floor(o),f=Math.ceil(i)-Math.floor(e),g=Math.ceil(n)-Math.floor(o);return{x:p,y:u,width:f,height:g,minX:e,minY:o,maxX:i,maxY:n}},l=function(t,e,i,o,n){void 0===e&&(e=15),void 0===i&&(i="quadratic"),void 0===o&&(o=void 0),void 0===n&&(n=void 0);for(var r=t.length,a=2*e,s=["top","top-right","right","bottom-right","bottom","bottom-left","left","top-left"],c={},h=[],d={},l=0;l<r;l++){var p=t[l],u=p.source,f=p.target,g="".concat(u,"-").concat(f);if(!h[l]){c[g]||(c[g]=[]),h[l]=!0,c[g].push(p);for(var y=0;y<r;y++)if(l!==y){var m=t[y],b=m.source,v=m.target;h[y]||(u===v&&f===b?(c[g].push(m),h[y]=!0,d["".concat(b,"|").concat(v,"|").concat(c[g].length-1)]=!0):u===b&&f===v&&(c[g].push(m),h[y]=!0))}}}for(var x in c)for(var O=c[x],j=O.length,S=0;S<j;S++){var M=O[S];if(M.source!==M.target)if(1===j&&o&&M.source!==M.target)M.type=o;else{M.type=i;var k=(S%2===0?1:-1)*(d["".concat(M.source,"|").concat(M.target,"|").concat(S)]?-1:1);M.curveOffset=j%2===1?k*Math.ceil(S/2)*a:k*(Math.floor(S/2)*a+e)}else n&&(M.type=n),M.loopCfg={position:s[S%8],dist:20*Math.floor(S/8)+50}}return t}},"6edb":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"a",(function(){return r})),i.d(e,"c",(function(){return a})),i.d(e,"d",(function(){return s}));var o=function(t,e){return o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},o(t,e)};function n(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}var r=function(){return r=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var n in e=arguments[i],e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t},r.apply(this,arguments)};function a(t,e){var i={};for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&e.indexOf(o)<0&&(i[o]=t[o]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var n=0;for(o=Object.getOwnPropertySymbols(t);n<o.length;n++)e.indexOf(o[n])<0&&Object.prototype.propertyIsEnumerable.call(t,o[n])&&(i[o[n]]=t[o[n]])}return i}Object.create;function s(t,e,i){if(i||2===arguments.length)for(var o,n=0,r=e.length;n<r;n++)!o&&n in e||(o||(o=Array.prototype.slice.call(e,0,n)),o[n]=e[n]);return t.concat(o||Array.prototype.slice.call(e))}Object.create},adc7:function(t,e,i){"use strict";i.r(e),i.d(e,"compare",(function(){return a})),i.d(e,"getLineIntersect",(function(){return c})),i.d(e,"getRectIntersectByPoint",(function(){return h})),i.d(e,"getCircleIntersectByPoint",(function(){return d})),i.d(e,"getEllipseIntersectByPoint",(function(){return l})),i.d(e,"applyMatrix",(function(){return p})),i.d(e,"invertMatrix",(function(){return u})),i.d(e,"getCircleCenterByPoints",(function(){return f})),i.d(e,"distance",(function(){return g})),i.d(e,"scaleMatrix",(function(){return y})),i.d(e,"floydWarshall",(function(){return m})),i.d(e,"getAdjMatrix",(function(){return b})),i.d(e,"translate",(function(){return v})),i.d(e,"move",(function(){return x})),i.d(e,"scale",(function(){return O})),i.d(e,"rotate",(function(){return j})),i.d(e,"getDegree",(function(){return S})),i.d(e,"isPointInPolygon",(function(){return k})),i.d(e,"intersectBBox",(function(){return w})),i.d(e,"isPolygonsIntersect",(function(){return P})),i.d(e,"Line",(function(){return I})),i.d(e,"getBBoxBoundLine",(function(){return N})),i.d(e,"itemIntersectByLine",(function(){return T})),i.d(e,"fractionToLine",(function(){return E})),i.d(e,"getPointsCenter",(function(){return Y})),i.d(e,"squareDist",(function(){return X})),i.d(e,"pointLineSquareDist",(function(){return z})),i.d(e,"isPointsOverlap",(function(){return L})),i.d(e,"pointRectSquareDist",(function(){return A})),i.d(e,"pointLineDistance",(function(){return F})),i.d(e,"lerp",(function(){return V}));var o=i("e897"),n=i("8937"),r=o["a"].transform,a=function(t){return function(e,i){return e[t]-i[t]}},s=function(t,e,i){return t>=e&&t<=i},c=function(t,e,i,o){var n=1e-4,r={x:i.x-t.x,y:i.y-t.y},a={x:e.x-t.x,y:e.y-t.y},c={x:o.x-i.x,y:o.y-i.y},h=a.x*c.y-a.y*c.x,d=h*h,l=1/h,p=a.x*a.x+a.y*a.y,u=c.x*c.x+c.y*c.y;if(d>n*p*u){var f=(r.x*c.y-r.y*c.x)*l,g=(r.x*a.y-r.y*a.x)*l;return s(f,0,1)&&s(g,0,1)?{x:t.x+f*a.x,y:t.y+f*a.y}:null}return null},h=function(t,e){var i=t.x,o=t.y,n=t.width,r=t.height,a=i+n/2,s=o+r/2,h=[],d={x:a,y:s};h.push({x:i,y:o}),h.push({x:i+n,y:o}),h.push({x:i+n,y:o+r}),h.push({x:i,y:o+r}),h.push({x:i,y:o});for(var l=null,p=1;p<h.length;p++)if(l=c(h[p-1],h[p],d,e),l)break;return l},d=function(t,e){var i=t.x,o=t.y,n=t.r,r=e.x,a=e.y,s=r-i,c=a-o;if(s*s+c*c<n*n)return null;var h=Math.atan(c/s);return{x:i+Math.abs(n*Math.cos(h))*Math.sign(s),y:o+Math.abs(n*Math.sin(h))*Math.sign(c)}},l=function(t,e){var i=t.rx,o=t.ry,n=t.x,r=t.y,a=e.x-n,s=e.y-r,c=Math.atan2(s/o,a/i);return c<0&&(c+=2*Math.PI),{x:n+i*Math.cos(c),y:r+o*Math.sin(c)}},p=function(t,e,i){void 0===i&&(i=1);var n=[t.x,t.y,i];return e&&!isNaN(e[0])||(e=[1,0,0,0,1,0,0,0,1]),o["d"].transformMat3(n,n,e),{x:n[0],y:n[1]}},u=function(t,e,i){void 0===i&&(i=1),e&&!isNaN(e[0])||(e=[1,0,0,0,1,0,0,0,1]);var n=o["b"].invert([1,0,0,0,1,0,0,0,1],e);n||(n=[1,0,0,0,1,0,0,0,1]);var r=[t.x,t.y,i];return o["d"].transformMat3(r,r,n),{x:r[0],y:r[1]}},f=function(t,e,i){var o=t.x-e.x,n=t.y-e.y,r=t.x-i.x,a=t.y-i.y,s=(t.x*t.x-e.x*e.x-e.y*e.y+t.y*t.y)/2,c=(t.x*t.x-i.x*i.x-i.y*i.y+t.y*t.y)/2,h=n*r-o*a;return{x:-(a*s-n*c)/h,y:-(o*c-r*s)/h}},g=function(t,e){var i=t.x-e.x,o=t.y-e.y;return Math.sqrt(i*i+o*o)},y=function(t,e){var i=[];return t.forEach((function(t){var o=[];t.forEach((function(t){o.push(t*e)})),i.push(o)})),i},m=function(t){for(var e=[],i=t.length,o=0;o<i;o+=1){e[o]=[];for(var n=0;n<i;n+=1)o===n?e[o][n]=0:0!==t[o][n]&&t[o][n]?e[o][n]=t[o][n]:e[o][n]=1/0}for(var r=0;r<i;r+=1)for(o=0;o<i;o+=1)for(n=0;n<i;n+=1)e[o][n]>e[o][r]+e[r][n]&&(e[o][n]=e[o][r]+e[r][n]);return e},b=function(t,e){var i=t.nodes,o=t.edges,n=[],r={};if(!i)throw new Error("invalid nodes data!");return i&&i.forEach((function(t,e){r[t.id]=e;var i=[];n.push(i)})),o&&o.forEach((function(t){var i=t.source,o=t.target,a=r[i],s=r[o];n[a][s]=1,e||(n[s][a]=1)})),n},v=function(t,e){t.translate(e.x,e.y)},x=function(t,e,i,o){void 0===o&&(o={duration:500});var n=t.getMatrix();n||(n=[1,0,0,0,1,0,0,0,1]);var a=t.getCanvasBBox(),s=e.x-a.minX,c=e.y-a.minY;if(i){var h=s*n[0],d=c*n[4],l=0,p=0,u=0,f=0;t.animate((function(t){return u=h*t,f=d*t,n=r(n,[["t",u-l,f-p]]),l=u,p=f,{matrix:n}}),o)}else{var g=r(n,[["t",s,c]]);t.setMatrix(g)}},O=function(t,e){var i=t.getMatrix();i||(i=[1,0,0,0,1,0,0,0,1]);var o=e;Object(n["isArray"])(e)||(o=[e,e]),Object(n["isArray"])(e)&&1===e.length&&(o=[e[0],e[0]]),i=r(i,[["s",o[0],o[1]]]),t.setMatrix(i)},j=function(t,e){var i=t.getMatrix();i||(i=[1,0,0,0,1,0,0,0,1]),i=r(i,[["r",e]]),t.setMatrix(i)},S=function(t,e,i){for(var o=[],n=0;n<t;n++)o[n]=0;return i.forEach((function(t){t.source&&(o[e[t.source]]+=1),t.target&&(o[e[t.target]]+=1)})),o};function M(t,e,i){return(i[0]-t[0])*(e[1]-t[1])===(e[0]-t[0])*(i[1]-t[1])&&Math.min(t[0],e[0])<=i[0]&&i[0]<=Math.max(t[0],e[0])&&Math.min(t[1],e[1])<=i[1]&&i[1]<=Math.max(t[1],e[1])}var k=function(t,e,i){var o=!1,n=t.length,r=1e-6;function a(t){return Math.abs(t)<r?0:t<0?-1:1}if(n<=2)return!1;for(var s=0;s<n;s++){var c=t[s],h=t[(s+1)%n];if(M(c,h,[e,i]))return!0;a(c[1]-i)>0!==a(h[1]-i)>0&&a(e-(i-c[1])*(c[0]-h[0])/(c[1]-h[1])-c[0])<0&&(o=!o)}return o},w=function(t,e){return!(e.minX>t.maxX||e.maxX<t.minX||e.minY>t.maxY||e.maxY<t.minY)},C=function(t,e){var i=!1;return Object(n["each"])(t,(function(t){if(c(t.from,t.to,e.from,e.to))return i=!0,!1})),i},P=function(t,e){var i=function(t){var e=t.map((function(t){return t[0]})),i=t.map((function(t){return t[1]}));return{minX:Math.min.apply(null,e),maxX:Math.max.apply(null,e),minY:Math.min.apply(null,i),maxY:Math.max.apply(null,i)}},o=function(t){for(var e=[],i=t.length,o=0;o<i-1;o++){var n=t[o],r=t[o+1];e.push({from:{x:n[0],y:n[1]},to:{x:r[0],y:r[1]}})}if(e.length>1){var a=t[0],s=t[i-1];e.push({from:{x:s[0],y:s[1]},to:{x:a[0],y:a[1]}})}return e};if(t.length<2||e.length<2)return!1;var r=i(t),a=i(e);if(!w(r,a))return!1;var s=!1;if(Object(n["each"])(e,(function(e){if(k(t,e[0],e[1]))return s=!0,!1})),s)return!0;if(Object(n["each"])(t,(function(t){if(k(e,t[0],t[1]))return s=!0,!1})),s)return!0;var c=o(t),h=o(e),d=!1;return Object(n["each"])(h,(function(t){if(C(c,t))return d=!0,!1})),d},I=function(){function t(t,e,i,o){this.x1=t,this.y1=e,this.x2=i,this.y2=o}return t.prototype.getBBox=function(){var t=Math.min(this.x1,this.x2),e=Math.min(this.y1,this.y2),i=Math.max(this.x1,this.x2),o=Math.max(this.y1,this.y2),n={x:t,y:e,minX:t,minY:e,maxX:i,maxY:o,width:i-t,height:o-e};return n},t}(),N=function(t,e){var i={top:[t.minX,t.minY,t.maxX,t.minY],left:[t.minX,t.minY,t.minX,t.maxY],bottom:[t.minX,t.maxY,t.maxX,t.maxY],right:[t.maxX,t.minY,t.maxX,t.maxY]};return i[e]},B=function(t,e){var i=(e.x2-e.x1)*(t.y1-e.y1)-(e.y2-e.y1)*(t.x1-e.x1),o=(t.x2-t.x1)*(t.y1-e.y1)-(t.y2-t.y1)*(t.x1-e.x1),n=(e.y2-e.y1)*(t.x2-t.x1)-(e.x2-e.x1)*(t.y2-t.y1);if(n){var r=i/n,a=o/n;if(r>=0&&r<=1&&a>=0&&a<=1)return r}return Number.POSITIVE_INFINITY},T=function(t,e){for(var i=["top","left","bottom","right"],o=t.getBBox(),n=0,r=[],a=0;a<4;a++){var s=N(o,i[a]),h=s[0],d=s[1],l=s[2],p=s[3];r[a]=c({x:e.x1,y:e.y1},{x:e.x2,y:e.y2},{x:h,y:d},{x:l,y:p}),r[a]&&(n+=1)}return[r,n]},E=function(t,e){for(var i=["top","left","bottom","right"],o=t.getBBox(),n=Number.POSITIVE_INFINITY,r=0,a=0;a<4;a++){var s=N(o,i[a]),c=s[0],h=s[1],d=s[2],l=s[3],p=B(e,new I(c,h,d,l));p=Math.abs(p-.5),p>=0&&p<=1&&(r+=1,n=p<n?p:n)}return 0===r?-1:n},Y=function(t){var e=0,i=0;if(t.length>0){for(var o=0,n=t;o<n.length;o++){var r=n[o];e+=r.x,i+=r.y}e/=t.length,i/=t.length}return{x:e,y:i}},X=function(t,e){return Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2)},z=function(t,e){var i,o=e.x1,n=e.y1,r=e.x2-o,a=e.y2-n,s=t.x-o,c=t.y-n,h=s*r+c*a;h<=0?i=0:(s=r-s,c=a-c,h=s*r+c*a,i=h<=0?0:h*h/(r*r+a*a));var d=s*s+c*c-i;return d<0&&(d=0),d},L=function(t,e,i){return void 0===i&&(i=.001),Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2)<Math.pow(i,2)},A=function(t,e){var i=t.x<e.x,o=t.x>e.x+e.width,n=t.y>e.y+e.height,r=t.y<e.y,a=i||o||n||r;if(!a)return 0;if(n&&!i&&!o)return Math.pow(e.y+e.height-t.y,2);if(r&&!i&&!o)return Math.pow(t.y-e.y,2);if(i&&!n&&!r)return Math.pow(e.x-t.x,2);if(o&&!n&&!r)return Math.pow(e.x+e.width-t.x,2);var s=Math.min(Math.abs(e.x-t.x),Math.abs(e.x+e.width-t.x)),c=Math.min(Math.abs(e.y-t.y),Math.abs(e.y+e.height-t.y));return s*s+c*c},F=function(t,e){var i=t[0],n=t[1],r=t[2],a=t[3],s=e.x,c=e.y,h=[r-i,a-n];if(o["c"].exactEquals(h,[0,0]))return NaN;var d=[-h[1],h[0]];o["c"].normalize(d,d);var l=[s-i,c-n];return Math.abs(o["c"].dot(l,d))},V=function(t,e,i){return t+(e-t)*i}},ae60:function(t,e,i){"use strict";i.r(e),i.d(e,"getSpline",(function(){return a})),i.d(e,"getControlPoint",(function(){return s})),i.d(e,"pointsToPolygon",(function(){return c})),i.d(e,"pathToPoints",(function(){return h})),i.d(e,"getClosedSpline",(function(){return d})),i.d(e,"roundedHull",(function(){return f})),i.d(e,"paddedHull",(function(){return g}));var o=i("e897"),n=i("2ef1"),r=function(t,e){return t&&e?t.replace(/\\?\{([^{}]+)\}/g,(function(t,i){if("\\"===t.charAt(0))return t.slice(1);var o=e[i];return 0===o&&(o="0"),o||""})):t},a=function(t){var e=[];if(t.length<2)throw new Error("point length must largn than 2, now it's ".concat(t.length));for(var i=0,o=t;i<o.length;i++){var r=o[i],a=r.x,s=r.y;e.push(a),e.push(s)}var c=Object(n["a"])(e);return c.unshift(["M",t[0].x,t[0].y]),c},s=function(t,e,i,n){void 0===i&&(i=0),void 0===n&&(n=0);var r={x:(1-i)*t.x+i*e.x,y:(1-i)*t.y+i*e.y},a=[0,0];o["c"].normalize(a,[e.x-t.x,e.y-t.y]),a&&(a[0]||a[1])||(a=[0,0]);var s=[-a[1]*n,a[0]*n];return r.x+=s[0],r.y+=s[1],r},c=function(t,e){var i=t.length;if(!i)return"";for(var o="",n="",a=0;a<i;a++){var s=t[a];n=0===a?"M{x} {y}":"L{x} {y}",o+=r(n,s)}return e&&(o+="Z"),o},h=function(t){var e=[];return t.forEach((function(t){var i=t[0];if("A"!==i)for(var o=1;o<t.length;o+=2)e.push([t[o],t[o+1]]);else{var n=t.length;e.push([t[n-2],t[n-1]])}})),e},d=function(t){if(t.length<2)throw new Error("point length must largn than 2, now it's ".concat(t.length));var e=t[0],i=t[1],o=t[t.length-1],n=t[t.length-2];t.unshift(o),t.unshift(n),t.push(e),t.push(i);for(var r=[],a=1;a<t.length-2;a+=1){var s=t[a-1].x,c=t[a-1].y,h=t[a].x,d=t[a].y,l=t[a+1].x,p=t[a+1].y,u=a!==t.length-2?t[a+2].x:l,f=a!==t.length-2?t[a+2].y:p,g=h+(l-s)/6,y=d+(p-c)/6,m=l-(u-h)/6,b=p-(f-d)/6;r.push(["C",g,y,m,b,l,p])}return r.unshift(["M",o.x,o.y]),r},l=function(t,e){return o["c"].scale([0,0],o["c"].normalize([0,0],t),e)},p=function(t,e){var i=[t[1]-e[1],e[0]-t[0]],o=Math.sqrt(i[0]*i[0]+i[1]*i[1]);if(0===o)throw new Error("p0 should not be equal to p1");return[i[0]/o,i[1]/o]},u=function(t,e){return[e[0]-t[0],e[1]-t[1]]};function f(t,e){var i=function(t){var i=[t[0][0],t[0][1]-e],o=[t[0][0],t[0][1]+e];return"M ".concat(i," A ").concat(e,",").concat(e,",0,0,0,").concat(o," A ").concat(e,",").concat(e,",0,0,0,").concat(i)},n=function(t){var i=o["c"].scale([0,0],p(t[0],t[1]),e),n=o["c"].scale([0,0],i,-1),r=o["c"].add([0,0],t[0],i),a=o["c"].add([0,0],t[1],i),s=o["c"].add([0,0],t[1],n),c=o["c"].add([0,0],t[0],n);return"M ".concat(r," L ").concat(a," A ").concat([e,e,"0,0,0",s].join(",")," L ").concat(c," A ").concat([e,e,"0,0,0",r].join(","))};if(!t||t.length<1)return"";if(1===t.length)return i(t);if(2===t.length)return n(t);for(var r=new Array(t.length),a=0;a<r.length;++a){var s=0===a?t[t.length-1]:t[a-1],c=t[a],h=o["c"].scale([0,0],p(s,c),e);r[a]=[o["c"].add([0,0],s,h),o["c"].add([0,0],c,h)]}var d="A ".concat([e,e,"0,0,0,"].join(","));return r=r.map((function(t,e){var i="";return 0===e&&(i="M ".concat(r[r.length-1][1]," ")),i+="".concat(d+t[0]," L ").concat(t[1]),i})),r.join(" ")}function g(t,e){var i=t.length,n=function(t){var i=[t[0][0],t[0][1]-e],o=[t[0][0],t[0][1]+e];return"M ".concat(i," A ").concat([e,e,"0,0,0",o].join(",")," A ").concat([e,e,"0,0,0",i].join(","))},r=function(t){var i=u(t[0],t[1]),n=l(i,e),r=o["c"].add([0,0],t[0],o["c"].scale([0,0],n,-1)),a=o["c"].add([0,0],t[1],n),s=1.2*e,c=l(o["c"].normalize([0,0],i),s),h=o["c"].scale([0,0],c,-1),d=o["c"].add([0,0],r,h),p=o["c"].add([0,0],a,h),f=o["c"].add([0,0],r,c);return"M ".concat(r," C ").concat([d,p,a].join(",")," S ").concat([f,r].join(",")," Z")};if(!t||i<1)return"";if(1===i)return n(t);if(2===i)return r(t);for(var a=t.map((function(e,n){var r=t[(n+1)%i];return{p:e,v:o["c"].normalize([0,0],u(e,r))}})),s=0;s<a.length;++s){var c=s>0?s-1:i-1,h=o["c"].normalize([0,0],o["c"].add([0,0],a[c].v,o["c"].scale([0,0],a[s].v,-1)));a[s].p=o["c"].add([0,0],a[s].p,o["c"].scale([0,0],h,e))}return a.map((function(t){var e=t.p;return{x:e[0],y:e[1]}}))}}}]);