(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2987fe29"],{"0632":function(t,e,i){},"0829":function(t,e,i){},"2d47":function(t,e,i){"use strict";i.r(e),i.d(e,"DeleteWordPartLeft",(function(){return u})),i.d(e,"DeleteWordPartRight",(function(){return d})),i.d(e,"WordPartLeftCommand",(function(){return h})),i.d(e,"CursorWordPartLeft",(function(){return p})),i.d(e,"CursorWordPartLeftSelect",(function(){return g})),i.d(e,"WordPartRightCommand",(function(){return f})),i.d(e,"CursorWordPartRight",(function(){return m})),i.d(e,"CursorWordPartRightSelect",(function(){return b}));var n=i("b2cc"),o=i("d48d"),r=i("6a89"),s=i("c101"),a=i("b3b2"),c=i("9e74"),l=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])},t(e,i)};return function(e,i){function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),u=function(t){function e(){return t.call(this,{whitespaceHeuristics:!0,wordNavigationType:0,id:"deleteWordPartLeft",precondition:s["a"].writable,kbOpts:{kbExpr:s["a"].textInputFocus,primary:0,mac:{primary:769},weight:100}})||this}return l(e,t),e.prototype._delete=function(t,e,i,n,s){var a=o["b"].deleteWordPartLeft(t,e,i,n);return a||new r["a"](1,1,1,1)},e}(a["DeleteWordCommand"]),d=function(t){function e(){return t.call(this,{whitespaceHeuristics:!0,wordNavigationType:2,id:"deleteWordPartRight",precondition:s["a"].writable,kbOpts:{kbExpr:s["a"].textInputFocus,primary:0,mac:{primary:788},weight:100}})||this}return l(e,t),e.prototype._delete=function(t,e,i,n,s){var a=o["b"].deleteWordPartRight(t,e,i,n);if(a)return a;var c=e.getLineCount(),l=e.getLineMaxColumn(c);return new r["a"](c,l,c,l)},e}(a["DeleteWordCommand"]),h=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return l(e,t),e.prototype._move=function(t,e,i,n){return o["b"].moveWordPartLeft(t,e,i)},e}(a["MoveWordCommand"]),p=function(t){function e(){return t.call(this,{inSelectionMode:!1,wordNavigationType:0,id:"cursorWordPartLeft",precondition:void 0,kbOpts:{kbExpr:s["a"].textInputFocus,primary:0,mac:{primary:783},weight:100}})||this}return l(e,t),e}(h);c["a"].registerCommandAlias("cursorWordPartStartLeft","cursorWordPartLeft");var g=function(t){function e(){return t.call(this,{inSelectionMode:!0,wordNavigationType:0,id:"cursorWordPartLeftSelect",precondition:void 0,kbOpts:{kbExpr:s["a"].textInputFocus,primary:0,mac:{primary:1807},weight:100}})||this}return l(e,t),e}(h);c["a"].registerCommandAlias("cursorWordPartStartLeftSelect","cursorWordPartLeftSelect");var f=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return l(e,t),e.prototype._move=function(t,e,i,n){return o["b"].moveWordPartRight(t,e,i)},e}(a["MoveWordCommand"]),m=function(t){function e(){return t.call(this,{inSelectionMode:!1,wordNavigationType:2,id:"cursorWordPartRight",precondition:void 0,kbOpts:{kbExpr:s["a"].textInputFocus,primary:0,mac:{primary:785},weight:100}})||this}return l(e,t),e}(f),b=function(t){function e(){return t.call(this,{inSelectionMode:!0,wordNavigationType:2,id:"cursorWordPartRightSelect",precondition:void 0,kbOpts:{kbExpr:s["a"].textInputFocus,primary:0,mac:{primary:1809},weight:100}})||this}return l(e,t),e}(f);Object(n["g"])(new u),Object(n["g"])(new d),Object(n["g"])(new p),Object(n["g"])(new g),Object(n["g"])(new m),Object(n["g"])(new b)},"33f9":function(t,e,i){self["MonacoEnvironment"]=function(t){function e(t){return t.replace(/\/$/,"")}return{getWorkerUrl:function(n,o){var r=i.p,s=(r?e(r)+"/":"")+t[o];if(/^((http:)|(https:)|(file:)|(\/\/))/.test(s)){var a=String(window.location),c=a.substr(0,a.length-window.location.hash.length-window.location.search.length-window.location.pathname.length);if(s.substring(0,c.length)!==c){var l="/*"+o+'*/importScripts("'+s+'");',u=new Blob([l],{type:"application/javascript"});return URL.createObjectURL(u)}}return s}}}({editorWorkerService:"editor.worker.js",css:"css.worker.js",html:"html.worker.js",json:"json.worker.js",less:"css.worker.js",scss:"css.worker.js",handlebars:"html.worker.js",razor:"html.worker.js"}),i("4816"),i("6e4e"),i("2935"),i("c36f"),i("0b11"),i("77a4"),i("92a6"),i("9f4d"),i("7c3e"),i("d585"),i("e516"),i("fd11"),i("a106"),i("7605"),i("6df4"),i("7082"),i("958f"),i("0210"),i("f187"),i("1f84"),i("aee8"),i("a222"),i("1af3"),i("8090"),i("747f"),i("d844"),i("f17c"),i("5b02"),i("bfe0"),i("5900"),i("e2c2"),i("43ad"),i("d741"),i("b574"),i("7a9e"),i("bd50"),i("93ba"),i("6daf"),i("5ed2"),i("b3b2"),i("2d47"),t.exports=i("f33e"),i("7257"),i("f41d"),i("f570"),i("73d3"),i("8457"),i("7367"),i("a79b")},4153:function(t,e,i){"use strict";i.d(e,"b",(function(){return b})),i.d(e,"a",(function(){return v})),i.d(e,"c",(function(){return y})),i.d(e,"e",(function(){return w})),i.d(e,"d",(function(){return k})),i.d(e,"f",(function(){return P}));var n,o=i("5fe7"),r=i("aa3d"),s=i("fdcc"),a=i("b2cc"),c=i("b707"),l=i("7061"),u=i("4fc3"),d=i("2504"),h=i("6a89"),p=i("7e93"),g=i("a666"),f=function(t,e,i,n){function o(t){return t instanceof i?t:new i((function(e){e(t)}))}return new(i||(i=Promise))((function(i,r){function s(t){try{c(n.next(t))}catch(e){r(e)}}function a(t){try{c(n["throw"](t))}catch(e){r(e)}}function c(t){t.done?i(t.value):o(t.value).then(s,a)}c((n=n.apply(t,e||[])).next())}))},m=function(t,e){var i,n,o,r,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return r={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function a(t){return function(e){return c([t,e])}}function c(r){if(i)throw new TypeError("Generator is already executing.");while(s)try{if(i=1,n&&(o=2&r[0]?n["return"]:r[0]?n["throw"]||((o=n["return"])&&o.call(n),0):n.next)&&!(o=o.call(n,r[1])).done)return o;switch(n=0,o&&(r=[2&r[0],o.value]),r[0]){case 0:case 1:o=r;break;case 4:return s.label++,{value:r[1],done:!1};case 5:s.label++,n=r[1],r=[0];continue;case 7:r=s.ops.pop(),s.trys.pop();continue;default:if(o=s.trys,!(o=o.length>0&&o[o.length-1])&&(6===r[0]||2===r[0])){s=0;continue}if(3===r[0]&&(!o||r[1]>o[0]&&r[1]<o[3])){s.label=r[1];break}if(6===r[0]&&s.label<o[1]){s.label=o[1],o=r;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(r);break}o[2]&&s.ops.pop(),s.trys.pop();continue}r=e.call(t,s)}catch(a){r=[6,a],n=0}finally{i=o=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}},b={Visible:new u["d"]("suggestWidgetVisible",!1),MultipleSuggestions:new u["d"]("suggestWidgetMultipleSuggestions",!1),MakesTextEdit:new u["d"]("suggestionMakesTextEdit",!0),AcceptSuggestionsOnEnter:new u["d"]("acceptSuggestionOnEnter",!0)},_=function(){function t(t,e,i,n,o){var a=this;this.position=t,this.completion=e,this.container=i,this.provider=n,this.isResolved=!1,this.score=p["a"].Default,this.distance=0,this.textLabel="string"===typeof e.label?e.label:e.label.name,this.labelLow=this.textLabel.toLowerCase(),this.sortTextLow=e.sortText&&e.sortText.toLowerCase(),this.filterTextLow=e.filterText&&e.filterText.toLowerCase(),h["a"].isIRange(e.range)?(this.editStart=new l["a"](e.range.startLineNumber,e.range.startColumn),this.editInsertEnd=new l["a"](e.range.endLineNumber,e.range.endColumn),this.editReplaceEnd=new l["a"](e.range.endLineNumber,e.range.endColumn)):(this.editStart=new l["a"](e.range.insert.startLineNumber,e.range.insert.startColumn),this.editInsertEnd=new l["a"](e.range.insert.endLineNumber,e.range.insert.endColumn),this.editReplaceEnd=new l["a"](e.range.replace.endLineNumber,e.range.replace.endColumn));var c,u=n.resolveCompletionItem;"function"!==typeof u?(this.resolve=function(){return Promise.resolve()},this.isResolved=!0):this.resolve=function(i){return c||(c=Promise.resolve(u.call(n,o,t,e,i)).then((function(t){Object(r["a"])(e,t),a.isResolved=!0}),(function(t){Object(s["d"])(t)&&(c=void 0)})),i.onCancellationRequested((function(){a.isResolved||(c=void 0)}))),c}}return t}(),v=function(){function t(t,e,i){void 0===t&&(t=2),void 0===e&&(e=new Set),void 0===i&&(i=new Set),this.snippetSortOrder=t,this.kindFilter=e,this.providerFilter=i}return t.default=new t,t}();function y(){return n}function w(t,e,i,r,a){void 0===i&&(i=v.default),void 0===r&&(r={triggerKind:0}),void 0===a&&(a=d["a"].None);var l=t.getWordAtPosition(e),u=l?new h["a"](e.lineNumber,l.startColumn,e.lineNumber,l.endColumn):h["a"].fromPositions(e),p=u.setEndPosition(e.lineNumber,e.column);e=e.clone();var f=c["d"].orderedGroups(t);!i.kindFilter.has(25)&&n&&f.unshift([n]);var m=[],b=new g["b"],y=!1,w=f.map((function(o){return function(){return Promise.all(o.map((function(o){if(!(i.providerFilter.size>0)||i.providerFilter.has(o))return Promise.resolve(o.provideCompletionItems(t,e,r,a)).then((function(r){var s=m.length;if(r){for(var a=0,c=r.suggestions||[];a<c.length;a++){var l=c[a];i.kindFilter.has(l.kind)||(l.range||(l.range={insert:p,replace:u}),l.sortText||(l.sortText="string"===typeof l.label?l.label:l.label.name),m.push(new _(e,l,r,o,t)))}Object(g["g"])(r)&&b.add(r)}s!==m.length&&o!==n&&(y=!0)}),s["f"])})))}})),S=Object(o["h"])(w,(function(){return y||a.isCancellationRequested})).then((function(){return a.isCancellationRequested?(b.dispose(),Promise.reject(Object(s["a"])())):m.sort(k(i.snippetSortOrder))}));return S}function S(t,e){if(t.sortTextLow&&e.sortTextLow){if(t.sortTextLow<e.sortTextLow)return-1;if(t.sortTextLow>e.sortTextLow)return 1}return t.completion.label<e.completion.label?-1:t.completion.label>e.completion.label?1:t.completion.kind-e.completion.kind}function C(t,e){if(t.completion.kind!==e.completion.kind){if(25===t.completion.kind)return-1;if(25===e.completion.kind)return 1}return S(t,e)}function O(t,e){if(t.completion.kind!==e.completion.kind){if(25===t.completion.kind)return 1;if(25===e.completion.kind)return-1}return S(t,e)}var x=new Map;function k(t){return x.get(t)}x.set(0,C),x.set(2,O),x.set(1,S),Object(a["e"])("_executeCompletionItemProvider",(function(t,e,i){return f(void 0,void 0,void 0,(function(){var n,o,r,s,a,c,l,u;return m(this,(function(h){switch(h.label){case 0:return n={incomplete:!1,suggestions:[]},o=new g["b"],r=[],s=i["maxItemsToResolve"]||0,[4,w(t,e)];case 1:for(a=h.sent(),c=0,l=a;c<l.length;c++)u=l[c],r.length<s&&r.push(u.resolve(d["a"].None)),n.incomplete=n.incomplete||u.container.incomplete,n.suggestions.push(u.completion),Object(g["g"])(u.container)&&o.add(u.container);h.label=2;case 2:return h.trys.push([2,,4,5]),[4,Promise.all(r)];case 3:return h.sent(),[2,n];case 4:return setTimeout((function(){return o.dispose()}),100),[7];case 5:return[2]}}))}))}));var D=new(function(){function t(){this.onlyOnceSuggestions=[]}return t.prototype.provideCompletionItems=function(){var t=this.onlyOnceSuggestions.slice(0),e={suggestions:t};return this.onlyOnceSuggestions.length=0,e},t}());function P(t,e){setTimeout((function(){var i;(i=D.onlyOnceSuggestions).push.apply(i,e),t.getContribution("editor.contrib.suggestController").triggerSuggest((new Set).add(D))}),0)}c["d"].register("*",D)},"43ad":function(t,e,i){"use strict";i.r(e),i.d(e,"rename",(function(){return B})),i.d(e,"RenameAction",(function(){return U}));var n=i("dff7"),o=i("fdcc"),r=i("4fc3"),s=i("b539"),a=i("b2cc"),c=i("c101"),l=(i("0632"),i("a666")),u=i("7061"),d=i("6a89"),h=i("303e"),p=i("b7d0"),g=i("6dec"),f=i("11f7"),m=function(t,e,i,n){var o,r=arguments.length,s=r<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)s=Reflect.decorate(t,e,i,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(r<3?o(s):r>3?o(e,i,s):o(e,i))||s);return r>3&&s&&Object.defineProperty(e,i,s),s},b=function(t,e){return function(i,n){e(i,n,t)}},_=new r["d"]("renameInputVisible",!1),v=function(){function t(t,e,i,n,o){var r=this;this._editor=t,this._acceptKeybindings=e,this._themeService=i,this._keybindingService=n,this._disposables=new l["b"],this.allowEditorOverflow=!0,this._visibleContextKey=_.bindTo(o),this._editor.addContentWidget(this),this._disposables.add(this._editor.onDidChangeConfiguration((function(t){t.hasChanged(34)&&r._updateFont()}))),this._disposables.add(i.onThemeChange(this._updateStyles,this))}return t.prototype.dispose=function(){this._disposables.dispose(),this._editor.removeContentWidget(this)},t.prototype.getId=function(){return"__renameInputWidget"},t.prototype.getDomNode=function(){var t=this;if(!this._domNode){this._domNode=document.createElement("div"),this._domNode.className="monaco-editor rename-box",this._input=document.createElement("input"),this._input.className="rename-input",this._input.type="text",this._input.setAttribute("aria-label",Object(n["a"])("renameAriaLabel","Rename input. Type new name and press Enter to commit.")),this._domNode.appendChild(this._input),this._label=document.createElement("div"),this._label.className="rename-label",this._domNode.appendChild(this._label);var e=function(){var e,i,o=t._acceptKeybindings,r=o[0],s=o[1];t._keybindingService.lookupKeybinding(r),t._label.innerText=Object(n["a"])("label","{0} to Rename, {1} to Preview",null===(e=t._keybindingService.lookupKeybinding(r))||void 0===e?void 0:e.getLabel(),null===(i=t._keybindingService.lookupKeybinding(s))||void 0===i?void 0:i.getLabel())};e(),this._disposables.add(this._keybindingService.onDidUpdateKeybindings(e)),this._updateFont(),this._updateStyles(this._themeService.getTheme())}return this._domNode},t.prototype._updateStyles=function(t){var e,i,n,o;if(this._input&&this._domNode){var r=t.getColor(h["hc"]);this._domNode.style.backgroundColor=String(null!==(e=t.getColor(h["Q"]))&&void 0!==e?e:""),this._domNode.style.boxShadow=r?" 0 2px 8px "+r:"",this._domNode.style.color=String(null!==(i=t.getColor(h["bb"]))&&void 0!==i?i:""),this._input.style.backgroundColor=String(null!==(n=t.getColor(h["Z"]))&&void 0!==n?n:"");var s=t.getColor(h["ab"]);this._input.style.borderWidth=s?"1px":"0px",this._input.style.borderStyle=s?"solid":"none",this._input.style.borderColor=null!==(o=null===s||void 0===s?void 0:s.toString())&&void 0!==o?o:"none"}},t.prototype._updateFont=function(){if(this._input&&this._label){var t=this._editor.getOption(34);this._input.style.fontFamily=t.fontFamily,this._input.style.fontWeight=t.fontWeight,this._input.style.fontSize=t.fontSize+"px",this._label.style.fontSize=.8*t.fontSize+"px"}},t.prototype.getPosition=function(){return this._visible?{position:this._position,preference:[2,1]}:null},t.prototype.acceptInput=function(t){this._currentAcceptInput&&this._currentAcceptInput(t)},t.prototype.cancelInput=function(t){this._currentCancelInput&&this._currentCancelInput(t)},t.prototype.getInput=function(t,e,i,n,o){var r=this;Object(f["Y"])(this._domNode,"preview",o),this._position=new u["a"](t.startLineNumber,t.startColumn),this._input.value=e,this._input.setAttribute("selectionStart",i.toString()),this._input.setAttribute("selectionEnd",n.toString()),this._input.size=Math.max(1.1*(t.endColumn-t.startColumn),20);var s=new l["b"];return new Promise((function(i){r._currentCancelInput=function(t){return r._currentAcceptInput=void 0,r._currentCancelInput=void 0,i(t),!0},r._currentAcceptInput=function(t){0!==r._input.value.trim().length&&r._input.value!==e?(r._currentAcceptInput=void 0,r._currentCancelInput=void 0,i({newName:r._input.value,wantsPreview:o&&t})):r.cancelInput(!0)};var n=function(){var e=r._editor.getPosition();e&&d["a"].containsPosition(t,e)||r.cancelInput(!0)};s.add(r._editor.onDidChangeCursorSelection(n)),s.add(r._editor.onDidBlurEditorWidget((function(){return r.cancelInput(!1)}))),r._show()})).finally((function(){s.dispose(),r._hide()}))},t.prototype._show=function(){var t=this;this._editor.revealLineInCenterIfOutsideViewport(this._position.lineNumber,0),this._visible=!0,this._visibleContextKey.set(!0),this._editor.layoutContentWidget(this),setTimeout((function(){t._input.focus(),t._input.setSelectionRange(parseInt(t._input.getAttribute("selectionStart")),parseInt(t._input.getAttribute("selectionEnd")))}),100)},t.prototype._hide=function(){this._visible=!1,this._visibleContextKey.reset(),this._editor.layoutContentWidget(this)},t=m([b(2,p["c"]),b(3,g["a"]),b(4,r["c"])],t),t}(),y=i("b707"),w=i("3813"),S=i("351f"),C=i("bc04"),O=i("b0cd"),x=i("c7f5"),k=i("6d8e"),D=i("5717"),P=i("2504"),T=i("5fe7"),j=i("ef8e"),N=i("d3d7"),E=i("0a0f"),L=i("89cd"),M=i("0910"),I=i("7b4a"),R=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])},t(e,i)};return function(e,i){function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),W=function(t,e,i,n){var o,r=arguments.length,s=r<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)s=Reflect.decorate(t,e,i,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(r<3?o(s):r>3?o(e,i,s):o(e,i))||s);return r>3&&s&&Object.defineProperty(e,i,s),s},A=function(t,e){return function(i,n){e(i,n,t)}},F=function(t,e,i,n){function o(t){return t instanceof i?t:new i((function(e){e(t)}))}return new(i||(i=Promise))((function(i,r){function s(t){try{c(n.next(t))}catch(e){r(e)}}function a(t){try{c(n["throw"](t))}catch(e){r(e)}}function c(t){t.done?i(t.value):o(t.value).then(s,a)}c((n=n.apply(t,e||[])).next())}))},H=function(t,e){var i,n,o,r,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return r={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function a(t){return function(e){return c([t,e])}}function c(r){if(i)throw new TypeError("Generator is already executing.");while(s)try{if(i=1,n&&(o=2&r[0]?n["return"]:r[0]?n["throw"]||((o=n["return"])&&o.call(n),0):n.next)&&!(o=o.call(n,r[1])).done)return o;switch(n=0,o&&(r=[2&r[0],o.value]),r[0]){case 0:case 1:o=r;break;case 4:return s.label++,{value:r[1],done:!1};case 5:s.label++,n=r[1],r=[0];continue;case 7:r=s.ops.pop(),s.trys.pop();continue;default:if(o=s.trys,!(o=o.length>0&&o[o.length-1])&&(6===r[0]||2===r[0])){s=0;continue}if(3===r[0]&&(!o||r[1]>o[0]&&r[1]<o[3])){s.label=r[1];break}if(6===r[0]&&s.label<o[1]){s.label=o[1],o=r;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(r);break}o[2]&&s.ops.pop(),s.trys.pop();continue}r=e.call(t,s)}catch(a){r=[6,a],n=0}finally{i=o=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}},V=function(){function t(t,e){this.model=t,this.position=e,this._providers=y["v"].ordered(t)}return t.prototype.hasProvider=function(){return this._providers.length>0},t.prototype.resolveRenameLocation=function(t){return F(this,void 0,void 0,(function(){var e,i,n,o;return H(this,(function(r){switch(r.label){case 0:return e=this._providers[0],e?e.resolveRenameLocation?(n=j["n"],[4,e.resolveRenameLocation(this.model,this.position,t)]):[3,2]:[2,void 0];case 1:i=n.apply(void 0,[r.sent()]),r.label=2;case 2:return!i&&(o=this.model.getWordAtPosition(this.position),o)?[2,{range:new d["a"](this.position.lineNumber,o.startColumn,this.position.lineNumber,o.endColumn),text:o.word}]:[2,i]}}))}))},t.prototype.provideRenameEdits=function(t,e,i,o){return F(this,void 0,void 0,(function(){var r,s;return H(this,(function(a){switch(a.label){case 0:return r=this._providers[e],r?[4,r.provideRenameEdits(this.model,this.position,t,o)]:[2,{edits:[],rejectReason:i.join("\n")}];case 1:return s=a.sent(),s?s.rejectReason?[2,this.provideRenameEdits(t,e+1,i.concat(s.rejectReason),o)]:[2,s]:[2,this.provideRenameEdits(t,e+1,i.concat(n["a"]("no result","No result.")),o)]}}))}))},t}();function B(t,e,i){return F(this,void 0,void 0,(function(){return H(this,(function(n){return[2,new V(t,e).provideRenameEdits(i,0,[],P["a"].None)]}))}))}var q=function(){function t(t,e,i,n,o,r,s){var a=this;this.editor=t,this._instaService=e,this._notificationService=i,this._bulkEditService=n,this._progressService=o,this._logService=r,this._configService=s,this._dispoableStore=new l["b"],this._cts=new P["b"],this._renameInputField=this._dispoableStore.add(new T["b"]((function(){return a._dispoableStore.add(a._instaService.createInstance(v,a.editor,["acceptRenameInput","acceptRenameInputWithPreview"]))})))}return t.get=function(e){return e.getContribution(t.ID)},t.prototype.dispose=function(){this._dispoableStore.dispose(),this._cts.dispose(!0)},t.prototype.run=function(){return F(this,void 0,void 0,(function(){var t,e,i,o,r,s,a,c,l,u,h,p=this;return H(this,(function(g){switch(g.label){case 0:if(this._cts.dispose(!0),!this.editor.hasModel())return[2,void 0];if(t=this.editor.getPosition(),e=new V(this.editor.getModel(),t),!e.hasProvider())return[2,void 0];this._cts=new C["b"](this.editor,5),g.label=1;case 1:return g.trys.push([1,3,,4]),o=e.resolveRenameLocation(this._cts.token),this._progressService.showWhile(o,250),[4,o];case 2:return i=g.sent(),[3,4];case 3:return r=g.sent(),S["a"].get(this.editor).showMessage(r||n["a"]("resolveRenameLocationFailed","An unknown error occurred while resolving rename location"),t),[2,void 0];case 4:return i?i.rejectReason?(S["a"].get(this.editor).showMessage(i.rejectReason,t),[2,void 0]):this._cts.token.isCancellationRequested?[2,void 0]:(s=this.editor.getSelection(),a=0,c=i.text.length,d["a"].isEmpty(s)||d["a"].spansMultipleLines(s)||!d["a"].containsRange(i.range,s)||(a=Math.max(0,s.startColumn-i.range.startColumn),c=Math.min(i.range.endColumn,s.endColumn)-i.range.startColumn),l=this._bulkEditService.hasPreviewHandler()&&this._configService.getValue(this.editor.getModel().uri,"editor.rename.enablePreview"),[4,this._renameInputField.getValue().getInput(i.range,i.text,a,c,l)]):[2,void 0];case 5:return u=g.sent(),"boolean"===typeof u?(u&&this.editor.focus(),[2,void 0]):(this.editor.focus(),h=Object(T["j"])(e.provideRenameEdits(u.newName,0,[],this._cts.token),this._cts.token).then((function(t){return F(p,void 0,void 0,(function(){var e=this;return H(this,(function(o){return t&&this.editor.hasModel()?t.rejectReason?(this._notificationService.info(t.rejectReason),[2]):(this._bulkEditService.apply(t,{editor:this.editor,showPreview:u.wantsPreview,label:n["a"]("label","Renaming '{0}'",null===i||void 0===i?void 0:i.text)}).then((function(t){t.ariaSummary&&Object(w["a"])(n["a"]("aria","Successfully renamed '{0}' to '{1}'. Summary: {2}",i.text,u.newName,t.ariaSummary))})).catch((function(t){e._notificationService.error(n["a"]("rename.failedApply","Rename failed to apply edits")),e._logService.error(t)})),[2]):[2]}))}))}),(function(t){p._notificationService.error(n["a"]("rename.failed","Rename failed to compute edits")),p._logService.error(t)})),this._progressService.showWhile(h,250),[2,h])}}))}))},t.prototype.acceptRenameInput=function(t){this._renameInputField.getValue().acceptInput(t)},t.prototype.cancelRenameInput=function(){this._renameInputField.getValue().cancelInput(!0)},t.ID="editor.contrib.renameController",t=W([A(1,E["a"]),A(2,O["a"]),A(3,x["a"]),A(4,s["a"]),A(5,N["a"]),A(6,I["a"])],t),t}(),U=function(t){function e(){return t.call(this,{id:"editor.action.rename",label:n["a"]("rename.label","Rename Symbol"),alias:"Rename Symbol",precondition:r["a"].and(c["a"].writable,c["a"].hasRenameProvider),kbOpts:{kbExpr:c["a"].editorTextFocus,primary:60,weight:100},contextMenuOpts:{group:"1_modification",order:1.1}})||this}return R(e,t),e.prototype.runCommand=function(e,i){var n=this,r=e.get(D["a"]),s=Array.isArray(i)&&i||[void 0,void 0],a=s[0],c=s[1];return k["a"].isUri(a)&&u["a"].isIPosition(c)?r.openCodeEditor({resource:a},r.getActiveCodeEditor()).then((function(t){t&&(t.setPosition(c),t.invokeWithinContext((function(e){return n.reportTelemetry(e,t),n.run(e,t)})))}),o["e"]):t.prototype.runCommand.call(this,e,i)},e.prototype.run=function(t,e){var i=q.get(e);return i?i.run():Promise.resolve()},e}(a["b"]);Object(a["h"])(q.ID,q),Object(a["f"])(U);var K=a["c"].bindToContribution(q.get);Object(a["g"])(new K({id:"acceptRenameInput",precondition:_,handler:function(t){return t.acceptRenameInput(!1)},kbOpts:{weight:199,kbExpr:c["a"].focus,primary:3}})),Object(a["g"])(new K({id:"acceptRenameInputWithPreview",precondition:r["a"].and(_,r["a"].has("config.editor.rename.enablePreview")),handler:function(t){return t.acceptRenameInput(!0)},kbOpts:{weight:199,kbExpr:c["a"].focus,primary:1027}})),Object(a["g"])(new K({id:"cancelRenameInput",precondition:_,handler:function(t){return t.cancelRenameInput()},kbOpts:{weight:199,kbExpr:c["a"].focus,primary:9,secondary:[1033]}})),Object(a["e"])("_executeDocumentRenameProvider",(function(t,e,i){var n=i.newName;if("string"!==typeof n)throw Object(o["b"])("newName");return B(t,e,n)})),L["a"].as(M["a"].Configuration).registerConfiguration({id:"editor",properties:{"editor.rename.enablePreview":{scope:5,description:n["a"]("enablePreview","Enable/disable the ability to preview changes before renaming"),default:!0,type:"boolean"}}})},"5ed2":function(t,e,i){"use strict";i.r(e),i.d(e,"getOccurrencesAtPosition",(function(){return P}));var n=i("dff7"),o=i("e8e3"),r=i("5fe7"),s=i("2504"),a=i("fdcc"),c=i("a666"),l=i("b2cc"),u=i("6a89"),d=i("c101"),h=i("3352"),p=i("b57f"),g=i("b707"),f=i("4fc3"),m=i("303e"),b=i("b7d0"),_=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])},t(e,i)};return function(e,i){function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),v=function(t,e,i,n){var o,r=arguments.length,s=r<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)s=Reflect.decorate(t,e,i,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(r<3?o(s):r>3?o(e,i,s):o(e,i))||s);return r>3&&s&&Object.defineProperty(e,i,s),s},y=function(t,e){return function(i,n){e(i,n,t)}},w=Object(m["Tb"])("editor.wordHighlightBackground",{dark:"#575757B8",light:"#57575740",hc:null},n["a"]("wordHighlight","Background color of a symbol during read-access, like reading a variable. The color must not be opaque so as not to hide underlying decorations."),!0),S=Object(m["Tb"])("editor.wordHighlightStrongBackground",{dark:"#004972B8",light:"#0e639c40",hc:null},n["a"]("wordHighlightStrong","Background color of a symbol during write-access, like writing to a variable. The color must not be opaque so as not to hide underlying decorations."),!0),C=Object(m["Tb"])("editor.wordHighlightBorder",{light:null,dark:null,hc:m["b"]},n["a"]("wordHighlightBorder","Border color of a symbol during read-access, like reading a variable.")),O=Object(m["Tb"])("editor.wordHighlightStrongBorder",{light:null,dark:null,hc:m["b"]},n["a"]("wordHighlightStrongBorder","Border color of a symbol during write-access, like writing to a variable.")),x=Object(m["Tb"])("editorOverviewRuler.wordHighlightForeground",{dark:"#A0A0A0CC",light:"#A0A0A0CC",hc:"#A0A0A0CC"},n["a"]("overviewRulerWordHighlightForeground","Overview ruler marker color for symbol highlights. The color must not be opaque so as not to hide underlying decorations."),!0),k=Object(m["Tb"])("editorOverviewRuler.wordHighlightStrongForeground",{dark:"#C0A0C0CC",light:"#C0A0C0CC",hc:"#C0A0C0CC"},n["a"]("overviewRulerWordHighlightStrongForeground","Overview ruler marker color for write-access symbol highlights. The color must not be opaque so as not to hide underlying decorations."),!0),D=new f["d"]("hasWordHighlights",!1);function P(t,e,i){var n=g["i"].ordered(t);return Object(r["h"])(n.map((function(n){return function(){return Promise.resolve(n.provideDocumentHighlights(t,e,i)).then(void 0,a["f"])}})),o["q"])}var T=function(){function t(t,e,i){var n=this;this._wordRange=this._getCurrentWordRange(t,e),this.result=Object(r["f"])((function(o){return n._compute(t,e,i,o)}))}return t.prototype._getCurrentWordRange=function(t,e){var i=t.getWordAtPosition(e.getPosition());return i?new u["a"](e.startLineNumber,i.startColumn,e.startLineNumber,i.endColumn):null},t.prototype.isValid=function(t,e,i){for(var n=e.startLineNumber,o=e.startColumn,r=e.endColumn,s=this._getCurrentWordRange(t,e),a=Boolean(this._wordRange&&this._wordRange.equalsRange(s)),c=0,l=i.length;!a&&c<l;c++){var u=t.getDecorationRange(i[c]);u&&u.startLineNumber===n&&u.startColumn<=o&&u.endColumn>=r&&(a=!0)}return a},t.prototype.cancel=function(){this.result.cancel()},t}(),j=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return _(e,t),e.prototype._compute=function(t,e,i,n){return P(t,e.getPosition(),n).then((function(t){return t||[]}))},e}(T),N=function(t){function e(e,i,n){var o=t.call(this,e,i,n)||this;return o._selectionIsEmpty=i.isEmpty(),o}return _(e,t),e.prototype._compute=function(t,e,i,n){return Object(r["l"])(250,n).then((function(){if(!e.isEmpty())return[];var n=t.getWordAtPosition(e.getPosition());if(!n)return[];var o=t.findMatches(n.word,!0,!1,!0,i,!1);return o.map((function(t){return{range:t.range,kind:g["h"].Text}}))}))},e.prototype.isValid=function(e,i,n){var o=i.isEmpty();return this._selectionIsEmpty===o&&t.prototype.isValid.call(this,e,i,n)},e}(T);function E(t,e,i){return g["i"].has(t)?new j(t,e,i):new N(t,e,i)}Object(l["k"])("_executeDocumentHighlights",(function(t,e){return P(t,e,s["a"].None)}));var L=function(){function t(t,e){var i=this;this.toUnhook=new c["b"],this.workerRequestTokenId=0,this.workerRequestCompleted=!1,this.workerRequestValue=[],this.lastCursorPositionChangeTime=0,this.renderDecorationsTimer=-1,this.editor=t,this._hasWordHighlights=D.bindTo(e),this._ignorePositionChangeEvent=!1,this.occurrencesHighlight=this.editor.getOption(61),this.model=this.editor.getModel(),this.toUnhook.add(t.onDidChangeCursorPosition((function(t){i._ignorePositionChangeEvent||i.occurrencesHighlight&&i._onPositionChanged(t)}))),this.toUnhook.add(t.onDidChangeModelContent((function(t){i._stopAll()}))),this.toUnhook.add(t.onDidChangeConfiguration((function(t){var e=i.editor.getOption(61);i.occurrencesHighlight!==e&&(i.occurrencesHighlight=e,i._stopAll())}))),this._decorationIds=[],this.workerRequestTokenId=0,this.workerRequest=null,this.workerRequestCompleted=!1,this.lastCursorPositionChangeTime=0,this.renderDecorationsTimer=-1}return t.prototype.hasDecorations=function(){return this._decorationIds.length>0},t.prototype.restore=function(){this.occurrencesHighlight&&this._run()},t.prototype._getSortedHighlights=function(){var t=this;return o["d"](this._decorationIds.map((function(e){return t.model.getDecorationRange(e)})).sort(u["a"].compareRangesUsingStarts))},t.prototype.moveNext=function(){var t=this,e=this._getSortedHighlights(),i=o["k"](e,(function(e){return e.containsPosition(t.editor.getPosition())})),n=(i+1)%e.length,r=e[n];try{this._ignorePositionChangeEvent=!0,this.editor.setPosition(r.getStartPosition()),this.editor.revealRangeInCenterIfOutsideViewport(r)}finally{this._ignorePositionChangeEvent=!1}},t.prototype.moveBack=function(){var t=this,e=this._getSortedHighlights(),i=o["k"](e,(function(e){return e.containsPosition(t.editor.getPosition())})),n=(i-1+e.length)%e.length,r=e[n];try{this._ignorePositionChangeEvent=!0,this.editor.setPosition(r.getStartPosition()),this.editor.revealRangeInCenterIfOutsideViewport(r)}finally{this._ignorePositionChangeEvent=!1}},t.prototype._removeDecorations=function(){this._decorationIds.length>0&&(this._decorationIds=this.editor.deltaDecorations(this._decorationIds,[]),this._hasWordHighlights.set(!1))},t.prototype._stopAll=function(){this._removeDecorations(),-1!==this.renderDecorationsTimer&&(clearTimeout(this.renderDecorationsTimer),this.renderDecorationsTimer=-1),null!==this.workerRequest&&(this.workerRequest.cancel(),this.workerRequest=null),this.workerRequestCompleted||(this.workerRequestTokenId++,this.workerRequestCompleted=!0)},t.prototype._onPositionChanged=function(t){this.occurrencesHighlight&&3===t.reason?this._run():this._stopAll()},t.prototype._run=function(){var t=this,e=this.editor.getSelection();if(e.startLineNumber===e.endLineNumber){var i=e.startLineNumber,n=e.startColumn,o=e.endColumn,r=this.model.getWordAtPosition({lineNumber:i,column:n});if(!r||r.startColumn>n||r.endColumn<o)this._stopAll();else{var s=this.workerRequest&&this.workerRequest.isValid(this.model,e,this._decorationIds);if(this.lastCursorPositionChangeTime=(new Date).getTime(),s)this.workerRequestCompleted&&-1!==this.renderDecorationsTimer&&(clearTimeout(this.renderDecorationsTimer),this.renderDecorationsTimer=-1,this._beginRenderDecorations());else{this._stopAll();var c=++this.workerRequestTokenId;this.workerRequestCompleted=!1,this.workerRequest=E(this.model,this.editor.getSelection(),this.editor.getOption(96)),this.workerRequest.result.then((function(e){c===t.workerRequestTokenId&&(t.workerRequestCompleted=!0,t.workerRequestValue=e||[],t._beginRenderDecorations())}),a["e"])}}}else this._stopAll()},t.prototype._beginRenderDecorations=function(){var t=this,e=(new Date).getTime(),i=this.lastCursorPositionChangeTime+250;e>=i?(this.renderDecorationsTimer=-1,this.renderDecorations()):this.renderDecorationsTimer=setTimeout((function(){t.renderDecorations()}),i-e)},t.prototype.renderDecorations=function(){this.renderDecorationsTimer=-1;for(var e=[],i=0,n=this.workerRequestValue.length;i<n;i++){var o=this.workerRequestValue[i];e.push({range:o.range,options:t._getDecorationOptions(o.kind)})}this._decorationIds=this.editor.deltaDecorations(this._decorationIds,e),this._hasWordHighlights.set(this.hasDecorations())},t._getDecorationOptions=function(t){return t===g["h"].Write?this._WRITE_OPTIONS:t===g["h"].Text?this._TEXT_OPTIONS:this._REGULAR_OPTIONS},t.prototype.dispose=function(){this._stopAll(),this.toUnhook.dispose()},t._WRITE_OPTIONS=p["a"].register({stickiness:1,className:"wordHighlightStrong",overviewRuler:{color:Object(b["f"])(k),position:h["d"].Center}}),t._TEXT_OPTIONS=p["a"].register({stickiness:1,className:"selectionHighlight",overviewRuler:{color:Object(b["f"])(m["Mb"]),position:h["d"].Center}}),t._REGULAR_OPTIONS=p["a"].register({stickiness:1,className:"wordHighlight",overviewRuler:{color:Object(b["f"])(x),position:h["d"].Center}}),t}(),M=function(t){function e(e,i){var n=t.call(this)||this;n.wordHighligher=null;var o=function(){e.hasModel()&&(n.wordHighligher=new L(e,i))};return n._register(e.onDidChangeModel((function(t){n.wordHighligher&&(n.wordHighligher.dispose(),n.wordHighligher=null),o()}))),o(),n}return _(e,t),e.get=function(t){return t.getContribution(e.ID)},e.prototype.saveViewState=function(){return!(!this.wordHighligher||!this.wordHighligher.hasDecorations())},e.prototype.moveNext=function(){this.wordHighligher&&this.wordHighligher.moveNext()},e.prototype.moveBack=function(){this.wordHighligher&&this.wordHighligher.moveBack()},e.prototype.restoreViewState=function(t){this.wordHighligher&&t&&this.wordHighligher.restore()},e.prototype.dispose=function(){this.wordHighligher&&(this.wordHighligher.dispose(),this.wordHighligher=null),t.prototype.dispose.call(this)},e.ID="editor.contrib.wordHighlighter",e=v([y(1,f["c"])],e),e}(c["a"]),I=function(t){function e(e,i){var n=t.call(this,i)||this;return n._isNext=e,n}return _(e,t),e.prototype.run=function(t,e){var i=M.get(e);i&&(this._isNext?i.moveNext():i.moveBack())},e}(l["b"]),R=function(t){function e(){return t.call(this,!0,{id:"editor.action.wordHighlight.next",label:n["a"]("wordHighlight.next.label","Go to Next Symbol Highlight"),alias:"Go to Next Symbol Highlight",precondition:D,kbOpts:{kbExpr:d["a"].editorTextFocus,primary:65,weight:100}})||this}return _(e,t),e}(I),W=function(t){function e(){return t.call(this,!1,{id:"editor.action.wordHighlight.prev",label:n["a"]("wordHighlight.previous.label","Go to Previous Symbol Highlight"),alias:"Go to Previous Symbol Highlight",precondition:D,kbOpts:{kbExpr:d["a"].editorTextFocus,primary:1089,weight:100}})||this}return _(e,t),e}(I),A=function(t){function e(){return t.call(this,{id:"editor.action.wordHighlight.trigger",label:n["a"]("wordHighlight.trigger.label","Trigger Symbol Highlight"),alias:"Trigger Symbol Highlight",precondition:D.toNegated(),kbOpts:{kbExpr:d["a"].editorTextFocus,primary:0,weight:100}})||this}return _(e,t),e.prototype.run=function(t,e,i){var n=M.get(e);n&&n.restoreViewState(!0)},e}(l["b"]);Object(l["h"])(M.ID,M),Object(l["f"])(R),Object(l["f"])(W),Object(l["f"])(A),Object(b["e"])((function(t,e){var i=t.getColor(m["M"]);i&&(e.addRule(".monaco-editor .focused .selectionHighlight { background-color: "+i+"; }"),e.addRule(".monaco-editor .selectionHighlight { background-color: "+i.transparent(.5)+"; }"));var n=t.getColor(w);n&&e.addRule(".monaco-editor .wordHighlight { background-color: "+n+"; }");var o=t.getColor(S);o&&e.addRule(".monaco-editor .wordHighlightStrong { background-color: "+o+"; }");var r=t.getColor(m["N"]);r&&e.addRule(".monaco-editor .selectionHighlight { border: 1px "+("hc"===t.type?"dotted":"solid")+" "+r+"; box-sizing: border-box; }");var s=t.getColor(C);s&&e.addRule(".monaco-editor .wordHighlight { border: 1px "+("hc"===t.type?"dashed":"solid")+" "+s+"; box-sizing: border-box; }");var a=t.getColor(O);a&&e.addRule(".monaco-editor .wordHighlightStrong { border: 1px "+("hc"===t.type?"dashed":"solid")+" "+a+"; box-sizing: border-box; }")}))},"67b4":function(t,e,i){"use strict";i.d(e,"a",(function(){return c}));var n=i("7061"),o=i("6a89"),r=i("db88"),s=function(t,e,i,n){function o(t){return t instanceof i?t:new i((function(e){e(t)}))}return new(i||(i=Promise))((function(i,r){function s(t){try{c(n.next(t))}catch(e){r(e)}}function a(t){try{c(n["throw"](t))}catch(e){r(e)}}function c(t){t.done?i(t.value):o(t.value).then(s,a)}c((n=n.apply(t,e||[])).next())}))},a=function(t,e){var i,n,o,r,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return r={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function a(t){return function(e){return c([t,e])}}function c(r){if(i)throw new TypeError("Generator is already executing.");while(s)try{if(i=1,n&&(o=2&r[0]?n["return"]:r[0]?n["throw"]||((o=n["return"])&&o.call(n),0):n.next)&&!(o=o.call(n,r[1])).done)return o;switch(n=0,o&&(r=[2&r[0],o.value]),r[0]){case 0:case 1:o=r;break;case 4:return s.label++,{value:r[1],done:!1};case 5:s.label++,n=r[1],r=[0];continue;case 7:r=s.ops.pop(),s.trys.pop();continue;default:if(o=s.trys,!(o=o.length>0&&o[o.length-1])&&(6===r[0]||2===r[0])){s=0;continue}if(3===r[0]&&(!o||r[1]>o[0]&&r[1]<o[3])){s.label=r[1];break}if(6===r[0]&&s.label<o[1]){s.label=o[1],o=r;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(r);break}o[2]&&s.ops.pop(),s.trys.pop();continue}r=e.call(t,s)}catch(a){r=[6,a],n=0}finally{i=o=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}},c=function(){function t(){}return t.prototype.provideSelectionRanges=function(e,i){return s(this,void 0,void 0,(function(){var n,o,r,s,c;return a(this,(function(l){switch(l.label){case 0:n=[],o=function(i){var o,r;return a(this,(function(s){switch(s.label){case 0:return o=[],n.push(o),r=new Map,[4,new Promise((function(n){return t._bracketsRightYield(n,0,e,i,r)}))];case 1:return s.sent(),[4,new Promise((function(n){return t._bracketsLeftYield(n,0,e,i,r,o)}))];case 2:return s.sent(),[2]}}))},r=0,s=i,l.label=1;case 1:return r<s.length?(c=s[r],[5,o(c)]):[3,4];case 2:l.sent(),l.label=3;case 3:return r++,[3,1];case 4:return[2,n]}}))}))},t._bracketsRightYield=function(e,i,n,o,s){var a=new Map,c=Date.now();while(1){if(i>=t._maxRounds){e();break}if(!o){e();break}var l=n.findNextBracket(o);if(!l){e();break}var u=Date.now()-c;if(u>t._maxDuration){setTimeout((function(){return t._bracketsRightYield(e,i+1,n,o,s)}));break}var d=l.close[0];if(l.isOpen){var h=a.has(d)?a.get(d):0;a.set(d,h+1)}else{h=a.has(d)?a.get(d):0;if(h-=1,a.set(d,Math.max(0,h)),h<0){var p=s.get(d);p||(p=new r["a"],s.set(d,p)),p.push(l.range)}}o=l.range.getEndPosition()}},t._bracketsLeftYield=function(e,i,n,r,s,a){var c=new Map,l=Date.now();while(1){if(i>=t._maxRounds&&0===s.size){e();break}if(!r){e();break}var u=n.findPrevBracket(r);if(!u){e();break}var d=Date.now()-l;if(d>t._maxDuration){setTimeout((function(){return t._bracketsLeftYield(e,i+1,n,r,s,a)}));break}var h=u.close[0];if(u.isOpen){b=c.has(h)?c.get(h):0;if(b-=1,c.set(h,Math.max(0,b)),b<0){var p=s.get(h);if(p){var g=p.shift();0===p.size&&s.delete(h);var f=o["a"].fromPositions(u.range.getEndPosition(),g.getStartPosition()),m=o["a"].fromPositions(u.range.getStartPosition(),g.getEndPosition());a.push({range:f}),a.push({range:m}),t._addBracketLeading(n,m,a)}}}else{var b=c.has(h)?c.get(h):0;c.set(h,b+1)}r=u.range.getStartPosition()}},t._addBracketLeading=function(t,e,i){if(e.startLineNumber!==e.endLineNumber){var r=e.startLineNumber,s=t.getLineFirstNonWhitespaceColumn(r);0!==s&&s!==e.startColumn&&(i.push({range:o["a"].fromPositions(new n["a"](r,s),e.getEndPosition())}),i.push({range:o["a"].fromPositions(new n["a"](r,1),e.getEndPosition())}));var a=r-1;if(a>0){var c=t.getLineFirstNonWhitespaceColumn(a);c===e.startColumn&&c!==t.getLineLastNonWhitespaceColumn(a)&&(i.push({range:o["a"].fromPositions(new n["a"](a,c),e.getEndPosition())}),i.push({range:o["a"].fromPositions(new n["a"](a,1),e.getEndPosition())}))}}},t._maxDuration=30,t._maxRounds=2,t}()},7275:function(t,e,i){"use strict";i.d(e,"a",(function(){return b}));i("b968");var n=i("11f7"),o=i("70c3"),r=i("ceb8"),s=i("9c3e"),a=i("a666"),c=i("aa3d"),l=i("6a89"),u=i("b57f"),d=new r["a"](new r["c"](0,122,204)),h={showArrow:!0,showFrame:!0,className:"",frameColor:d,arrowColor:d,keepEditorSelection:!1},p="vs.editor.contrib.zoneWidget",g=function(){function t(t,e,i,n,o,r){this.id="",this.domNode=t,this.afterLineNumber=e,this.afterColumn=i,this.heightInLines=n,this._onDomNodeTop=o,this._onComputedHeight=r}return t.prototype.onDomNodeTop=function(t){this._onDomNodeTop(t)},t.prototype.onComputedHeight=function(t){this._onComputedHeight(t)},t}(),f=function(){function t(t,e){this._id=t,this._domNode=e}return t.prototype.getId=function(){return this._id},t.prototype.getDomNode=function(){return this._domNode},t.prototype.getPosition=function(){return null},t}(),m=function(){function t(e){this._editor=e,this._ruleName=t._IdGenerator.nextId(),this._decorations=[],this._color=null,this._height=-1}return t.prototype.dispose=function(){this.hide(),n["O"](this._ruleName)},Object.defineProperty(t.prototype,"color",{set:function(t){this._color!==t&&(this._color=t,this._updateStyle())},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"height",{set:function(t){this._height!==t&&(this._height=t,this._updateStyle())},enumerable:!0,configurable:!0}),t.prototype._updateStyle=function(){n["O"](this._ruleName),n["v"](".monaco-editor "+this._ruleName,"border-style: solid; border-color: transparent; border-bottom-color: "+this._color+"; border-width: "+this._height+"px; bottom: -"+this._height+"px; margin-left: -"+this._height+"px; ")},t.prototype.show=function(t){this._decorations=this._editor.deltaDecorations(this._decorations,[{range:l["a"].fromPositions(t),options:{className:this._ruleName,stickiness:1}}])},t.prototype.hide=function(){this._editor.deltaDecorations(this._decorations,[])},t._IdGenerator=new s["a"](".arrow-decoration-"),t}(),b=function(){function t(t,e){var i=this;void 0===e&&(e={}),this._arrow=null,this._overlayWidget=null,this._resizeSash=null,this._positionMarkerId=[],this._viewZone=null,this._disposables=new a["b"],this.container=null,this._isShowing=!1,this.editor=t,this.options=c["c"](e),c["g"](this.options,h,!1),this.domNode=document.createElement("div"),this.options.isAccessible||(this.domNode.setAttribute("aria-hidden","true"),this.domNode.setAttribute("role","presentation")),this._disposables.add(this.editor.onDidLayoutChange((function(t){var e=i._getWidth(t);i.domNode.style.width=e+"px",i.domNode.style.left=i._getLeft(t)+"px",i._onWidth(e)})))}return t.prototype.dispose=function(){var t=this;this._overlayWidget&&(this.editor.removeOverlayWidget(this._overlayWidget),this._overlayWidget=null),this._viewZone&&this.editor.changeViewZones((function(e){t._viewZone&&e.removeZone(t._viewZone.id),t._viewZone=null})),this.editor.deltaDecorations(this._positionMarkerId,[]),this._positionMarkerId=[],this._disposables.dispose()},t.prototype.create=function(){n["f"](this.domNode,"zone-widget"),this.options.className&&n["f"](this.domNode,this.options.className),this.container=document.createElement("div"),n["f"](this.container,"zone-widget-container"),this.domNode.appendChild(this.container),this.options.showArrow&&(this._arrow=new m(this.editor),this._disposables.add(this._arrow)),this._fillContainer(this.container),this._initSash(),this._applyStyles()},t.prototype.style=function(t){t.frameColor&&(this.options.frameColor=t.frameColor),t.arrowColor&&(this.options.arrowColor=t.arrowColor),this._applyStyles()},t.prototype._applyStyles=function(){if(this.container&&this.options.frameColor){var t=this.options.frameColor.toString();this.container.style.borderTopColor=t,this.container.style.borderBottomColor=t}if(this._arrow&&this.options.arrowColor){var e=this.options.arrowColor.toString();this._arrow.color=e}},t.prototype._getWidth=function(t){return t.width-t.minimapWidth-t.verticalScrollbarWidth},t.prototype._getLeft=function(t){return t.minimapWidth>0&&0===t.minimapLeft?t.minimapWidth:0},t.prototype._onViewZoneTop=function(t){this.domNode.style.top=t+"px"},t.prototype._onViewZoneHeight=function(t){if(this.domNode.style.height=t+"px",this.container){var e=t-this._decoratingElementsHeight();this.container.style.height=e+"px";var i=this.editor.getLayoutInfo();this._doLayout(e,this._getWidth(i))}this._resizeSash&&this._resizeSash.layout()},Object.defineProperty(t.prototype,"position",{get:function(){var t=this._positionMarkerId[0];if(t){var e=this.editor.getModel();if(e){var i=e.getDecorationRange(t);if(i)return i.getStartPosition()}}},enumerable:!0,configurable:!0}),t.prototype.show=function(t,e){var i=l["a"].isIRange(t)?l["a"].lift(t):l["a"].fromPositions(t);this._isShowing=!0,this._showImpl(i,e),this._isShowing=!1,this._positionMarkerId=this.editor.deltaDecorations(this._positionMarkerId,[{range:i,options:u["a"].EMPTY}])},t.prototype.hide=function(){var t=this;this._viewZone&&(this.editor.changeViewZones((function(e){t._viewZone&&e.removeZone(t._viewZone.id)})),this._viewZone=null),this._overlayWidget&&(this.editor.removeOverlayWidget(this._overlayWidget),this._overlayWidget=null),this._arrow&&this._arrow.hide()},t.prototype._decoratingElementsHeight=function(){var t=this.editor.getOption(49),e=0;if(this.options.showArrow){var i=Math.round(t/3);e+=2*i}if(this.options.showFrame){var n=Math.round(t/9);e+=2*n}return e},t.prototype._showImpl=function(t,e){var i=this,n=t.getStartPosition(),o=this.editor.getLayoutInfo(),r=this._getWidth(o);this.domNode.style.width=r+"px",this.domNode.style.left=this._getLeft(o)+"px";var s=document.createElement("div");s.style.overflow="hidden";var a=this.editor.getOption(49),c=this.editor.getLayoutInfo().height/a*.8;e>=c&&(e=c);var l=0,u=0;if(this._arrow&&this.options.showArrow&&(l=Math.round(a/3),this._arrow.height=l,this._arrow.show(n)),this.options.showFrame&&(u=Math.round(a/9)),this.editor.changeViewZones((function(t){i._viewZone&&t.removeZone(i._viewZone.id),i._overlayWidget&&(i.editor.removeOverlayWidget(i._overlayWidget),i._overlayWidget=null),i.domNode.style.top="-1000px",i._viewZone=new g(s,n.lineNumber,n.column,e,(function(t){return i._onViewZoneTop(t)}),(function(t){return i._onViewZoneHeight(t)})),i._viewZone.id=t.addZone(i._viewZone),i._overlayWidget=new f(p+i._viewZone.id,i.domNode),i.editor.addOverlayWidget(i._overlayWidget)})),this.container&&this.options.showFrame){var d=this.options.frameWidth?this.options.frameWidth:u;this.container.style.borderTopWidth=d+"px",this.container.style.borderBottomWidth=d+"px"}var h=e*a-this._decoratingElementsHeight();this.container&&(this.container.style.top=l+"px",this.container.style.height=h+"px",this.container.style.overflow="hidden"),this._doLayout(h,r),this.options.keepEditorSelection||this.editor.setSelection(t);var m=this.editor.getModel();if(m){var b=t.endLineNumber+1;b<=m.getLineCount()?this.revealLine(b,!1):this.revealLine(m.getLineCount(),!0)}},t.prototype.revealLine=function(t,e){e?this.editor.revealLineInCenter(t,0):this.editor.revealLine(t,0)},t.prototype.setCssClass=function(t,e){this.container&&(e&&this.container.classList.remove(e),n["f"](this.container,t))},t.prototype._onWidth=function(t){},t.prototype._doLayout=function(t,e){},t.prototype._relayout=function(t){var e=this;this._viewZone&&this._viewZone.heightInLines!==t&&this.editor.changeViewZones((function(i){e._viewZone&&(e._viewZone.heightInLines=t,i.layoutZone(e._viewZone.id))}))},t.prototype._initSash=function(){var t,e=this;this._resizeSash||(this._resizeSash=this._disposables.add(new o["a"](this.domNode,this,{orientation:1})),this.options.isResizeable||(this._resizeSash.hide(),this._resizeSash.state=0),this._disposables.add(this._resizeSash.onDidStart((function(i){e._viewZone&&(t={startY:i.startY,heightInLines:e._viewZone.heightInLines})}))),this._disposables.add(this._resizeSash.onDidEnd((function(){t=void 0}))),this._disposables.add(this._resizeSash.onDidChange((function(i){if(t){var n=(i.currentY-t.startY)/e.editor.getOption(49),o=n<0?Math.ceil(n):Math.floor(n),r=t.heightInLines+o;r>5&&r<35&&e._relayout(r)}}))))},t.prototype.getHorizontalSashLeft=function(){return 0},t.prototype.getHorizontalSashTop=function(){return(null===this.domNode.style.height?0:parseInt(this.domNode.style.height))-this._decoratingElementsHeight()/2},t.prototype.getHorizontalSashWidth=function(){var t=this.editor.getLayoutInfo();return t.width-t.minimapWidth},t}()},7457:function(t,e,i){},"7a9e":function(t,e,i){"use strict";i.r(e),i.d(e,"SuggestController",(function(){return Qt})),i.d(e,"TriggerSuggestAction",(function(){return te}));var n=i("3813"),o=i("e8e3"),r=i("fdcc"),s=i("fe45"),a=i("a666"),c=i("b2cc"),l=i("d3f4"),u=i("6a89"),d=i("c101"),h=i("b574"),p=i("b800"),g=i("4035"),f=i("03e8"),m=i("b707"),b=i("5fe7"),_=i("0a0f"),v=i("fbba"),y=i("f5f3"),w=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])},t(e,i)};return function(e,i){function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),S=function(t,e,i,n){var o,r=arguments.length,s=r<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)s=Reflect.decorate(t,e,i,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(r<3?o(s):r>3?o(e,i,s):o(e,i))||s);return r>3&&s&&Object.defineProperty(e,i,s),s},C=function(t,e){return function(i,n){e(i,n,t)}},O=function(){function t(){}return t.prototype.select=function(t,e,i){if(0===i.length)return 0;for(var n=i[0].score[0],o=1;o<i.length;o++){var r=i[o],s=r.score,a=r.completion;if(s[0]!==n)break;if(a.preselect)return o}return 0},t}(),x=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return w(e,t),e.prototype.memorize=function(t,e,i){},e.prototype.toJSON=function(){},e.prototype.fromJSON=function(){},e}(O),k=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e._cache=new g["a"](300,.66),e._seq=0,e}return w(e,t),e.prototype.memorize=function(t,e,i){var n=i.completion.label,o=t.getLanguageIdentifier().language+"/"+n;this._cache.set(o,{touch:this._seq++,type:i.completion.kind,insertText:i.completion.insertText})},e.prototype.select=function(e,i,n){if(0===n.length)return 0;var o=e.getLineContent(i.lineNumber).substr(i.column-10,i.column-1);if(/\s$/.test(o))return t.prototype.select.call(this,e,i,n);for(var r=n[0].score[0],s=-1,a=-1,c=-1,l=0;l<n.length;l++){if(n[l].score[0]!==r)break;var u=e.getLanguageIdentifier().language+"/"+n[l].completion.label,d=this._cache.peek(u);if(d&&d.touch>c&&d.type===n[l].completion.kind&&d.insertText===n[l].completion.insertText&&(c=d.touch,a=l),n[l].completion.preselect&&-1===s)return l}return-1!==a?a:-1!==s?s:0},e.prototype.toJSON=function(){var t=[];return this._cache.forEach((function(e,i){t.push([i,e])})),t},e.prototype.fromJSON=function(t){this._cache.clear();for(var e=0,i=0,n=t;i<n.length;i++){var o=n[i],r=o[0],s=o[1];s.touch=e,s.type="number"===typeof s.type?s.type:Object(m["E"])(s.type),this._cache.set(r,s)}this._seq=this._cache.size},e}(O),D=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e._trie=g["c"].forStrings(),e._seq=0,e}return w(e,t),e.prototype.memorize=function(t,e,i){var n=t.getWordUntilPosition(e).word,o=t.getLanguageIdentifier().language+"/"+n;this._trie.set(o,{type:i.completion.kind,insertText:i.completion.insertText,touch:this._seq++})},e.prototype.select=function(e,i,n){var o=e.getWordUntilPosition(i).word;if(!o)return t.prototype.select.call(this,e,i,n);var r=e.getLanguageIdentifier().language+"/"+o,s=this._trie.get(r);if(s||(s=this._trie.findSubstr(r)),s)for(var a=0;a<n.length;a++){var c=n[a].completion,l=c.kind,u=c.insertText;if(l===s.type&&u===s.insertText)return a}return t.prototype.select.call(this,e,i,n)},e.prototype.toJSON=function(){var t=[];return this._trie.forEach((function(e,i){return t.push([i,e])})),t.sort((function(t,e){return-(t[1].touch-e[1].touch)})).forEach((function(t,e){return t[1].touch=e})),t.slice(0,200)},e.prototype.fromJSON=function(t){if(this._trie.clear(),t.length>0){this._seq=t[0][1].touch+1;for(var e=0,i=t;e<i.length;e++){var n=i[e],o=n[0],r=n[1];r.type="number"===typeof r.type?r.type:Object(m["E"])(r.type),this._trie.set(o,r)}}},e}(O),P=function(t){function e(e,i){var n=t.call(this)||this;n._storageService=e,n._configService=i,n._storagePrefix="suggest/memories";var o=function(){var t=n._configService.getValue("editor.suggestSelection"),e=n._configService.getValue("editor.suggest.shareSuggestSelections");n._update(t,e,!1)};return n._persistSoon=n._register(new b["d"]((function(){return n._saveState()}),500)),n._register(e.onWillSaveState((function(t){t.reason===f["c"].SHUTDOWN&&n._saveState()}))),n._register(n._configService.onDidChangeConfiguration((function(t){(t.affectsConfiguration("editor.suggestSelection")||t.affectsConfiguration("editor.suggest.shareSuggestSelections"))&&o()}))),n._register(n._storageService.onDidChangeStorage((function(t){0===t.scope&&0===t.key.indexOf(n._storagePrefix)&&(document.hasFocus()||n._update(n._mode,n._shareMem,!0))}))),o(),n}return w(e,t),e.prototype._update=function(t,e,i){if(i||this._mode!==t||this._shareMem!==e){this._shareMem=e,this._mode=t,this._strategy="recentlyUsedByPrefix"===t?new D:"recentlyUsed"===t?new k:new x;try{var n=e?0:1,o=this._storageService.get(this._storagePrefix+"/"+this._mode,n);o&&this._strategy.fromJSON(JSON.parse(o))}catch(r){}}},e.prototype.memorize=function(t,e,i){this._strategy.memorize(t,e,i),this._persistSoon.schedule()},e.prototype.select=function(t,e,i){return this._strategy.select(t,e,i)},e.prototype._saveState=function(){var t=JSON.stringify(this._strategy),e=this._shareMem?0:1;this._storageService.store(this._storagePrefix+"/"+this._mode,t,e)},e=S([C(0,f["a"]),C(1,v["a"])],e),e}(a["a"]),T=Object(_["c"])("ISuggestMemories");Object(y["b"])(T,P,!0);var j=i("dff7"),N=i("9e74"),E=i("4fc3"),L=i("9eb8"),M=i("4153"),I=function(t,e,i,n){var o,r=arguments.length,s=r<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)s=Reflect.decorate(t,e,i,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(r<3?o(s):r>3?o(e,i,s):o(e,i))||s);return r>3&&s&&Object.defineProperty(e,i,s),s},R=function(t,e){return function(i,n){e(i,n,t)}},W=function(){function t(e,i){this._editor=e,this._index=0,this._ckOtherSuggestions=t.OtherSuggestions.bindTo(i)}return t.prototype.dispose=function(){this.reset()},t.prototype.reset=function(){this._ckOtherSuggestions.reset(),Object(a["f"])(this._listener),this._model=void 0,this._acceptNext=void 0,this._ignore=!1},t.prototype.set=function(e,i){var n=this,o=e.model,r=e.index;if(0!==o.items.length){var s=t._moveIndex(!0,o,r);s!==r?(this._acceptNext=i,this._model=o,this._index=r,this._listener=this._editor.onDidChangeCursorPosition((function(){n._ignore||n.reset()})),this._ckOtherSuggestions.set(!0)):this.reset()}else this.reset()},t._moveIndex=function(t,e,i){var n=i;while(1){if(n=(n+e.items.length+(t?1:-1))%e.items.length,n===i)break;if(!e.items[n].completion.additionalTextEdits)break}return n},t.prototype.next=function(){this._move(!0)},t.prototype.prev=function(){this._move(!1)},t.prototype._move=function(e){if(this._model)try{this._ignore=!0,this._index=t._moveIndex(e,this._model,this._index),this._acceptNext({index:this._index,item:this._model.items[this._index],model:this._model})}finally{this._ignore=!1}},t.OtherSuggestions=new E["d"]("hasOtherSuggestions",!1),t=I([R(1,E["c"])],t),t}(),A=i("308f"),F=i("8025"),H=i("7e93"),V=i("3742"),B=(function(){function t(t,e){this.leadingLineContent=t,this.characterCountDelta=e}}(),function(){function t(e,i,n,o,r,s){this._snippetCompareFn=t._compareCompletionItems,this._items=e,this._column=i,this._wordDistance=o,this._options=r,this._refilterKind=1,this._lineContext=n,"top"===s?this._snippetCompareFn=t._compareCompletionItemsSnippetsUp:"bottom"===s&&(this._snippetCompareFn=t._compareCompletionItemsSnippetsDown)}return Object.defineProperty(t.prototype,"lineContext",{get:function(){return this._lineContext},set:function(t){this._lineContext.leadingLineContent===t.leadingLineContent&&this._lineContext.characterCountDelta===t.characterCountDelta||(this._refilterKind=this._lineContext.characterCountDelta<t.characterCountDelta&&this._filteredItems?2:1,this._lineContext=t)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"items",{get:function(){return this._ensureCachedState(),this._filteredItems},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"incomplete",{get:function(){return this._ensureCachedState(),this._isIncomplete},enumerable:!0,configurable:!0}),t.prototype.adopt=function(t){for(var e=new Array,i=0;i<this._items.length;)t.has(this._items[i].provider)?i++:(e.push(this._items[i]),this._items[i]=this._items[this._items.length-1],this._items.pop());return this._refilterKind=1,e},Object.defineProperty(t.prototype,"stats",{get:function(){return this._ensureCachedState(),this._stats},enumerable:!0,configurable:!0}),t.prototype._ensureCachedState=function(){0!==this._refilterKind&&this._createCachedState()},t.prototype._createCachedState=function(){this._isIncomplete=new Set,this._stats={suggestionCount:0,snippetCount:0,textCount:0};for(var t=this._lineContext,e=t.leadingLineContent,i=t.characterCountDelta,n="",o="",r=1===this._refilterKind?this._items:this._filteredItems,s=[],a=!this._options.filterGraceful||r.length>2e3?H["d"]:H["e"],c=0;c<r.length;c++){var l=r[c];l.container.incomplete&&this._isIncomplete.add(l.provider);var u=l.position.column-l.editStart.column,d=u+i-(l.position.column-this._column);if(n.length!==d&&(n=0===d?"":e.slice(-d),o=n.toLowerCase()),l.word=n,0===d)l.score=H["a"].Default;else{var h=0;while(h<u){var p=n.charCodeAt(h);if(32!==p&&9!==p)break;h+=1}var g="string"===typeof l.completion.label?l.completion.label:l.completion.label.name;if(h>=d)l.score=H["a"].Default;else if("string"===typeof l.completion.filterText){var f=a(n,o,h,l.completion.filterText,l.filterTextLow,0,!1);if(!f)continue;0===Object(V["f"])(l.completion.filterText,g)?l.score=f:(l.score=Object(H["b"])(n,o,h,g,l.labelLow,0),l.score[0]=f[0])}else{f=a(n,o,h,g,l.labelLow,0,!1);if(!f)continue;l.score=f}}switch(l.idx=c,l.distance=this._wordDistance.distance(l.position,l.completion),s.push(l),this._stats.suggestionCount++,l.completion.kind){case 25:this._stats.snippetCount++;break;case 18:this._stats.textCount++;break}}this._filteredItems=s.sort(this._snippetCompareFn),this._refilterKind=0},t._compareCompletionItems=function(t,e){return t.score[0]>e.score[0]?-1:t.score[0]<e.score[0]?1:t.distance<e.distance?-1:t.distance>e.distance?1:t.idx<e.idx?-1:t.idx>e.idx?1:0},t._compareCompletionItemsSnippetsDown=function(e,i){if(e.completion.kind!==i.completion.kind){if(25===e.completion.kind)return 1;if(25===i.completion.kind)return-1}return t._compareCompletionItems(e,i)},t._compareCompletionItemsSnippetsUp=function(e,i){if(e.completion.kind!==i.completion.kind){if(25===e.completion.kind)return-1;if(25===i.completion.kind)return 1}return t._compareCompletionItems(e,i)},t}()),q=i("2504"),U=i("67b4"),K=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])},t(e,i)};return function(e,i){function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),z=function(t,e,i,n){function o(t){return t instanceof i?t:new i((function(e){e(t)}))}return new(i||(i=Promise))((function(i,r){function s(t){try{c(n.next(t))}catch(e){r(e)}}function a(t){try{c(n["throw"](t))}catch(e){r(e)}}function c(t){t.done?i(t.value):o(t.value).then(s,a)}c((n=n.apply(t,e||[])).next())}))},G=function(t,e){var i,n,o,r,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return r={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function a(t){return function(e){return c([t,e])}}function c(r){if(i)throw new TypeError("Generator is already executing.");while(s)try{if(i=1,n&&(o=2&r[0]?n["return"]:r[0]?n["throw"]||((o=n["return"])&&o.call(n),0):n.next)&&!(o=o.call(n,r[1])).done)return o;switch(n=0,o&&(r=[2&r[0],o.value]),r[0]){case 0:case 1:o=r;break;case 4:return s.label++,{value:r[1],done:!1};case 5:s.label++,n=r[1],r=[0];continue;case 7:r=s.ops.pop(),s.trys.pop();continue;default:if(o=s.trys,!(o=o.length>0&&o[o.length-1])&&(6===r[0]||2===r[0])){s=0;continue}if(3===r[0]&&(!o||r[1]>o[0]&&r[1]<o[3])){s.label=r[1];break}if(6===r[0]&&s.label<o[1]){s.label=o[1],o=r;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(r);break}o[2]&&s.ops.pop(),s.trys.pop();continue}r=e.call(t,s)}catch(a){r=[6,a],n=0}finally{i=o=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}},Z=function(){function t(){}return t.create=function(e,i){return z(this,void 0,void 0,(function(){var n,r,s,a;return G(this,(function(c){switch(c.label){case 0:return i.getOption(89).localityBonus&&i.hasModel()?(n=i.getModel(),r=i.getPosition(),e.canComputeWordRanges(n.uri)?[4,(new U["a"]).provideSelectionRanges(n,[r])]:[2,t.None]):[2,t.None];case 1:return s=c.sent(),s&&0!==s.length&&0!==s[0].length?[4,e.computeWordRanges(n.uri,s[0][0].range)]:[2,t.None];case 2:return a=c.sent(),[2,new(function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return K(e,t),e.prototype.distance=function(t,e){if(!a||!r.equals(i.getPosition()))return 0;if(17===e.kind)return 2<<20;var n="string"===typeof e.label?e.label:e.label.name,c=a[n];if(Object(o["p"])(c))return 2<<20;for(var l=Object(o["c"])(c,u["a"].fromPositions(t),u["a"].compareRangesUsingStarts),d=l>=0?c[l]:c[Math.max(0,~l-1)],h=s.length,p=0,g=s[0];p<g.length;p++){var f=g[p];if(!u["a"].containsRange(f.range,d))break;h-=1}return h},e}(t))]}}))}))},t.None=new(function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return K(e,t),e.prototype.distance=function(){return 0},e}(t)),t}(),J=function(){function t(t,e,i,n){this.leadingLineContent=t.getLineContent(e.lineNumber).substr(0,e.column-1),this.leadingWord=t.getWordUntilPosition(e),this.lineNumber=e.lineNumber,this.column=e.column,this.auto=i,this.shy=n}return t.shouldAutoTrigger=function(t){if(!t.hasModel())return!1;var e=t.getModel(),i=t.getPosition();e.tokenizeIfCheap(i.lineNumber);var n=e.getWordAtPosition(i);return!!n&&(n.endColumn===i.column&&!!isNaN(Number(n.word)))},t}(),Y=function(){function t(t,e){var i=this;this._editor=t,this._editorWorker=e,this._toDispose=new a["b"],this._quickSuggestDelay=10,this._triggerCharacterListener=new a["b"],this._triggerQuickSuggest=new b["e"],this._state=0,this._completionDisposables=new a["b"],this._onDidCancel=new A["a"],this._onDidTrigger=new A["a"],this._onDidSuggest=new A["a"],this.onDidCancel=this._onDidCancel.event,this.onDidTrigger=this._onDidTrigger.event,this.onDidSuggest=this._onDidSuggest.event,this._currentSelection=this._editor.getSelection()||new F["a"](1,1,1,1),this._toDispose.add(this._editor.onDidChangeModel((function(){i._updateTriggerCharacters(),i.cancel()}))),this._toDispose.add(this._editor.onDidChangeModelLanguage((function(){i._updateTriggerCharacters(),i.cancel()}))),this._toDispose.add(this._editor.onDidChangeConfiguration((function(){i._updateTriggerCharacters(),i._updateQuickSuggest()}))),this._toDispose.add(m["d"].onDidChange((function(){i._updateTriggerCharacters(),i._updateActiveSuggestSession()}))),this._toDispose.add(this._editor.onDidChangeCursorSelection((function(t){i._onCursorChange(t)})));var n=!1;this._toDispose.add(this._editor.onDidCompositionStart((function(){n=!0}))),this._toDispose.add(this._editor.onDidCompositionEnd((function(){n=!1,i._refilterCompletionItems()}))),this._toDispose.add(this._editor.onDidChangeModelContent((function(){n||i._refilterCompletionItems()}))),this._updateTriggerCharacters(),this._updateQuickSuggest()}return t.prototype.dispose=function(){Object(a["f"])(this._triggerCharacterListener),Object(a["f"])([this._onDidCancel,this._onDidSuggest,this._onDidTrigger,this._triggerQuickSuggest]),this._toDispose.dispose(),this._completionDisposables.dispose(),this.cancel()},t.prototype._updateQuickSuggest=function(){this._quickSuggestDelay=this._editor.getOption(67),(isNaN(this._quickSuggestDelay)||!this._quickSuggestDelay&&0!==this._quickSuggestDelay||this._quickSuggestDelay<0)&&(this._quickSuggestDelay=10)},t.prototype._updateTriggerCharacters=function(){var t=this;if(this._triggerCharacterListener.clear(),!this._editor.getOption(68)&&this._editor.hasModel()&&this._editor.getOption(92)){for(var e=new Map,i=0,n=m["d"].all(this._editor.getModel());i<n.length;i++)for(var o=n[i],r=0,s=o.triggerCharacters||[];r<s.length;r++){var a=s[r],c=e.get(a);c||(c=new Set,c.add(Object(M["c"])()),e.set(a,c)),c.add(o)}var l=function(i){if(!i){var n=t._editor.getPosition(),o=t._editor.getModel();i=o.getLineContent(n.lineNumber).substr(0,n.column-1)}var r="";Object(V["A"])(i.charCodeAt(i.length-1))?Object(V["z"])(i.charCodeAt(i.length-2))&&(r=i.substr(i.length-2)):r=i.charAt(i.length-1);var s=e.get(r);if(s){var a=t._completionModel?t._completionModel.adopt(s):void 0;t.trigger({auto:!0,shy:!1,triggerCharacter:r},Boolean(t._completionModel),s,a)}};this._triggerCharacterListener.add(this._editor.onDidType(l)),this._triggerCharacterListener.add(this._editor.onDidCompositionEnd(l))}},Object.defineProperty(t.prototype,"state",{get:function(){return this._state},enumerable:!0,configurable:!0}),t.prototype.cancel=function(t){void 0===t&&(t=!1),0!==this._state&&(this._triggerQuickSuggest.cancel(),this._requestToken&&(this._requestToken.cancel(),this._requestToken=void 0),this._state=0,this._completionModel=void 0,this._context=void 0,this._onDidCancel.fire({retrigger:t}))},t.prototype.clear=function(){this._completionDisposables.clear()},t.prototype._updateActiveSuggestSession=function(){0!==this._state&&(this._editor.hasModel()&&m["d"].has(this._editor.getModel())?this.trigger({auto:2===this._state,shy:!1},!0):this.cancel())},t.prototype._onCursorChange=function(t){var e=this;if(this._editor.hasModel()){var i=this._editor.getModel(),n=this._currentSelection;if(this._currentSelection=this._editor.getSelection(),!t.selection.isEmpty()||0!==t.reason||"keyboard"!==t.source&&"deleteLeft"!==t.source)this.cancel();else if(m["d"].has(i)&&0===this._state){if(!1===this._editor.getOption(66))return;if(!n.containsRange(this._currentSelection)&&!n.getEndPosition().isBeforeOrEqual(this._currentSelection.getPosition()))return;if(this._editor.getOption(89).snippetsPreventQuickSuggestions&&h["SnippetController2"].get(this._editor).isInSnippet())return;this.cancel(),this._triggerQuickSuggest.cancelAndSet((function(){if(0===e._state&&J.shouldAutoTrigger(e._editor)&&e._editor.hasModel()){var t=e._editor.getModel(),i=e._editor.getPosition(),n=e._editor.getOption(66);if(!1!==n){if(!0===n);else{t.tokenizeIfCheap(i.lineNumber);var o=t.getLineTokens(i.lineNumber),r=o.getStandardTokenType(o.findTokenIndexAtOffset(Math.max(i.column-1-1,0))),s=n.other&&0===r||n.comments&&1===r||n.strings&&2===r;if(!s)return}e.trigger({auto:!0,shy:!1})}}}),this._quickSuggestDelay)}}},t.prototype._refilterCompletionItems=function(){var t=this;Promise.resolve().then((function(){if(0!==t._state&&t._editor.hasModel()){var e=t._editor.getModel(),i=t._editor.getPosition(),n=new J(e,i,2===t._state,!1);t._onNewContext(n)}}))},t.prototype.trigger=function(e,i,n,s){var c=this;if(void 0===i&&(i=!1),this._editor.hasModel()){var l,u=this._editor.getModel(),d=e.auto,h=new J(u,this._editor.getPosition(),d,e.shy);this.cancel(i),this._state=d?2:1,this._onDidTrigger.fire({auto:d,shy:e.shy,position:this._editor.getPosition()}),this._context=h,l=e.triggerCharacter?{triggerKind:1,triggerCharacter:e.triggerCharacter}:n&&n.size>0?{triggerKind:2}:{triggerKind:0},this._requestToken=new q["b"];var p=this._editor.getOption(86),g=1;switch(p){case"top":g=0;break;case"bottom":g=2;break}var f=t._createItemKindFilter(this._editor),m=Z.create(this._editorWorker,this._editor),b=Object(M["e"])(u,this._editor.getPosition(),new M["a"](g,f,n),l,this._requestToken.token);Promise.all([b,m]).then((function(t){var i=t[0],n=t[1];if(Object(a["f"])(c._requestToken),0!==c._state&&c._editor.hasModel()){var r=c._editor.getModel();if(Object(o["q"])(s)){var l=Object(M["d"])(g);i=i.concat(s).sort(l)}var u=new J(r,c._editor.getPosition(),d,e.shy);c._completionModel=new B(i,c._context.column,{leadingLineContent:u.leadingLineContent,characterCountDelta:u.column-c._context.column},n,c._editor.getOption(89),c._editor.getOption(86));for(var h=0,p=i;h<p.length;h++){var f=p[h];Object(a["g"])(f.container)&&c._completionDisposables.add(f.container)}c._onNewContext(u)}})).catch(r["e"])}},t._createItemKindFilter=function(t){var e=new Set,i=t.getOption(86);"none"===i&&e.add(25);var n=t.getOption(89);return n.showMethods||e.add(0),n.showFunctions||e.add(1),n.showConstructors||e.add(2),n.showFields||e.add(3),n.showVariables||e.add(4),n.showClasses||e.add(5),n.showStructs||e.add(6),n.showInterfaces||e.add(7),n.showModules||e.add(8),n.showProperties||e.add(9),n.showEvents||e.add(10),n.showOperators||e.add(11),n.showUnits||e.add(12),n.showValues||e.add(13),n.showConstants||e.add(14),n.showEnums||e.add(15),n.showEnumMembers||e.add(16),n.showKeywords||e.add(17),n.showWords||e.add(18),n.showColors||e.add(19),n.showFiles||e.add(20),n.showReferences||e.add(21),n.showColors||e.add(22),n.showFolders||e.add(23),n.showTypeParameters||e.add(24),n.showSnippets||e.add(25),e},t.prototype._onNewContext=function(t){if(this._context)if(t.lineNumber===this._context.lineNumber){if(t.leadingWord.startColumn<this._context.leadingWord.startColumn)this.cancel();else if(t.column<this._context.column)t.leadingWord.word?this.trigger({auto:this._context.auto,shy:!1},!0):this.cancel();else if(this._completionModel)if(t.column>this._context.column&&this._completionModel.incomplete.size>0&&0!==t.leadingWord.word.length){var e=this._completionModel.incomplete,i=this._completionModel.adopt(e);this.trigger({auto:2===this._state,shy:!1},!0,e,i)}else{var n=this._completionModel.lineContext,o=!1;if(this._completionModel.lineContext={leadingLineContent:t.leadingLineContent,characterCountDelta:t.column-this._context.column},0===this._completionModel.items.length){if(J.shouldAutoTrigger(this._editor)&&this._context.leadingWord.endColumn<t.leadingWord.startColumn)return void this.trigger({auto:this._context.auto,shy:!1},!0);if(this._context.auto)return void this.cancel();if(this._completionModel.lineContext=n,o=this._completionModel.items.length>0,o&&0===t.leadingWord.word.length)return void this.cancel()}this._onDidSuggest.fire({completionModel:this._completionModel,auto:this._context.auto,shy:this._context.shy,isFrozen:o})}}else this.cancel()},t}(),X=(i("0829"),i("9e7e"),i("93be"),i("8ea8"),i("11f7")),$=i("72a7"),Q=i("1898"),tt=i("6dec"),et=i("5d75"),it=i("a6d7"),nt=i("b7d0"),ot=i("303e"),rt=i("dea0"),st=i("5818"),at=i("5bd7"),ct=i("c4e3"),lt=i("8aeb"),ut=i("1b69"),dt=i("6d8e"),ht=i("459c"),pt=i("78bc"),gt=i("7061"),ft=function(){return ft=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++)for(var o in e=arguments[i],e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},ft.apply(this,arguments)},mt=function(t,e,i,n){var o,r=arguments.length,s=r<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)s=Reflect.decorate(t,e,i,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(r<3?o(s):r>3?o(e,i,s):o(e,i))||s);return r>3&&s&&Object.defineProperty(e,i,s),s},bt=function(t,e){return function(i,n){e(i,n,t)}},_t=function(t,e,i,n){function o(t){return t instanceof i?t:new i((function(e){e(t)}))}return new(i||(i=Promise))((function(i,r){function s(t){try{c(n.next(t))}catch(e){r(e)}}function a(t){try{c(n["throw"](t))}catch(e){r(e)}}function c(t){t.done?i(t.value):o(t.value).then(s,a)}c((n=n.apply(t,e||[])).next())}))},vt=function(t,e){var i,n,o,r,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return r={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function a(t){return function(e){return c([t,e])}}function c(r){if(i)throw new TypeError("Generator is already executing.");while(s)try{if(i=1,n&&(o=2&r[0]?n["return"]:r[0]?n["throw"]||((o=n["return"])&&o.call(n),0):n.next)&&!(o=o.call(n,r[1])).done)return o;switch(n=0,o&&(r=[2&r[0],o.value]),r[0]){case 0:case 1:o=r;break;case 4:return s.label++,{value:r[1],done:!1};case 5:s.label++,n=r[1],r=[0];continue;case 7:r=s.ops.pop(),s.trys.pop();continue;default:if(o=s.trys,!(o=o.length>0&&o[o.length-1])&&(6===r[0]||2===r[0])){s=0;continue}if(3===r[0]&&(!o||r[1]>o[0]&&r[1]<o[3])){s.label=r[1];break}if(6===r[0]&&s.label<o[1]){s.label=o[1],o=r;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(r);break}o[2]&&s.ops.pop(),s.trys.pop();continue}r=e.call(t,s)}catch(a){r=[6,a],n=0}finally{i=o=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}},yt=!1,wt=Object(ot["Tb"])("editorSuggestWidget.background",{dark:ot["Q"],light:ot["Q"],hc:ot["Q"]},j["a"]("editorSuggestWidgetBackground","Background color of the suggest widget.")),St=Object(ot["Tb"])("editorSuggestWidget.border",{dark:ot["R"],light:ot["R"],hc:ot["R"]},j["a"]("editorSuggestWidgetBorder","Border color of the suggest widget.")),Ct=Object(ot["Tb"])("editorSuggestWidget.foreground",{dark:ot["x"],light:ot["x"],hc:ot["x"]},j["a"]("editorSuggestWidgetForeground","Foreground color of the suggest widget.")),Ot=Object(ot["Tb"])("editorSuggestWidget.selectedBackground",{dark:ot["rb"],light:ot["rb"],hc:ot["rb"]},j["a"]("editorSuggestWidgetSelectedBackground","Background color of the selected entry in the suggest widget.")),xt=Object(ot["Tb"])("editorSuggestWidget.highlightForeground",{dark:ot["tb"],light:ot["tb"],hc:ot["tb"]},j["a"]("editorSuggestWidgetHighlightForeground","Color of the match highlights in the suggest widget.")),kt=/^(#([\da-f]{3}){1,2}|(rgb|hsl)a\(\s*(\d{1,3}%?\s*,\s*){3}(1|0?\.\d+)\)|(rgb|hsl)\(\s*\d{1,3}%?(\s*,\s*\d{1,3}%?){2}\s*\))$/i;function Dt(t,e){var i="string"===typeof t.completion.label?t.completion.label:t.completion.label.name;return i.match(kt)?(e[0]=i,!0):!("string"!==typeof t.completion.documentation||!t.completion.documentation.match(kt))&&(e[0]=t.completion.documentation,!0)}function Pt(t){if(!t)return!1;var e=t.completion;return!!e.documentation||e.detail&&e.detail!==e.label}function Tt(t){return"suggest-aria-id:"+t}var jt=function(){function t(t,e,i,n,o,r){this.widget=t,this.editor=e,this.triggerKeybindingLabel=i,this._modelService=n,this._modeService=o,this._themeService=r}return Object.defineProperty(t.prototype,"templateId",{get:function(){return"suggestion"},enumerable:!0,configurable:!0}),t.prototype.renderTemplate=function(t){var e=this,i=Object.create(null);i.disposables=new a["b"],i.root=t,Object(X["f"])(i.root,"show-file-icons"),i.icon=Object(X["q"])(t,Object(X["a"])(".icon")),i.colorspan=Object(X["q"])(i.icon,Object(X["a"])("span.colorspan"));var n=Object(X["q"])(t,Object(X["a"])(".contents")),o=Object(X["q"])(n,Object(X["a"])(".main"));i.left=Object(X["q"])(o,Object(X["a"])("span.left")),i.right=Object(X["q"])(o,Object(X["a"])("span.right")),i.iconContainer=Object(X["q"])(i.left,Object(X["a"])(".icon-label.codicon")),i.iconLabel=new ct["a"](i.left,{supportHighlights:!0,supportCodicons:!0}),i.disposables.add(i.iconLabel),i.signatureLabel=Object(X["q"])(i.left,Object(X["a"])("span.signature-label")),i.qualifierLabel=Object(X["q"])(i.left,Object(X["a"])("span.qualifier-label")),i.detailsLabel=Object(X["q"])(i.right,Object(X["a"])("span.details-label")),i.readMore=Object(X["q"])(i.right,Object(X["a"])("span.readMore.codicon.codicon-info")),i.readMore.title=j["a"]("readMore","Read More...{0}",this.triggerKeybindingLabel);var r=function(){var t=e.editor.getOptions(),n=t.get(34),r=n.fontFamily,s=n.fontFeatureSettings,a=t.get(90)||n.fontSize,c=t.get(91)||n.lineHeight,l=n.fontWeight,u=a+"px",d=c+"px";i.root.style.fontSize=u,i.root.style.fontWeight=l,o.style.fontFamily=r,o.style.fontFeatureSettings=s,o.style.lineHeight=d,i.icon.style.height=d,i.icon.style.width=d,i.readMore.style.height=d,i.readMore.style.width=d};return r(),i.disposables.add(A["b"].chain(this.editor.onDidChangeConfiguration.bind(this.editor)).filter((function(t){return t.hasChanged(34)||t.hasChanged(90)||t.hasChanged(91)})).on(r,null)),i},t.prototype.renderElement=function(t,e,i){var n=this,r=i,s=t.completion,a="string"===typeof s.label?s.label:s.label.name;r.root.id=Tt(e),r.icon.className="icon "+Object(m["F"])(s.kind),r.colorspan.style.backgroundColor="";var c={labelEscapeNewLines:!0,matches:Object(H["c"])(t.score)},l=[];if(19===s.kind&&Dt(t,l))r.icon.className="icon customcolor",r.iconContainer.className="icon hide",r.colorspan.style.backgroundColor=l[0];else if(20===s.kind&&this._themeService.getIconTheme().hasFileIcons){r.icon.className="icon hide",r.iconContainer.className="icon hide";var u=Object(lt["a"])(this._modelService,this._modeService,dt["a"].from({scheme:"fake",path:a}),ht["a"].FILE),d=Object(lt["a"])(this._modelService,this._modeService,dt["a"].from({scheme:"fake",path:s.detail}),ht["a"].FILE);c.extraClasses=u.length>d.length?u:d}else 23===s.kind&&this._themeService.getIconTheme().hasFolderIcons?(r.icon.className="icon hide",r.iconContainer.className="icon hide",c.extraClasses=Object(o["m"])([Object(lt["a"])(this._modelService,this._modeService,dt["a"].from({scheme:"fake",path:a}),ht["a"].FOLDER),Object(lt["a"])(this._modelService,this._modeService,dt["a"].from({scheme:"fake",path:s.detail}),ht["a"].FOLDER)])):(r.icon.className="icon hide",r.iconContainer.className="",Object(X["g"])(r.iconContainer,"suggest-icon codicon codicon-symbol-"+Object(m["F"])(s.kind)));s.tags&&s.tags.indexOf(1)>=0&&(c.extraClasses=(c.extraClasses||[]).concat(["deprecated"]),c.matches=[]),r.iconLabel.setLabel(a,void 0,c),"string"===typeof s.label?(r.signatureLabel.textContent="",r.qualifierLabel.textContent="",r.detailsLabel.textContent=(s.detail||"").replace(/\n.*$/m,""),Object(X["P"])(r.right,"always-show-details")):(r.signatureLabel.textContent=(s.label.signature||"").replace(/\n.*$/m,""),r.qualifierLabel.textContent=(s.label.qualifier||"").replace(/\n.*$/m,""),r.detailsLabel.textContent=(s.label.type||"").replace(/\n.*$/m,""),Object(X["f"])(r.right,"always-show-details")),Pt(t)?(Object(X["f"])(r.right,"can-expand-details"),Object(X["X"])(r.readMore),r.readMore.onmousedown=function(t){t.stopPropagation(),t.preventDefault()},r.readMore.onclick=function(t){t.stopPropagation(),t.preventDefault(),n.widget.toggleDetails()}):(Object(X["P"])(r.right,"can-expand-details"),Object(X["J"])(r.readMore),r.readMore.onmousedown=null,r.readMore.onclick=null)},t.prototype.disposeTemplate=function(t){t.disposables.dispose()},t=mt([bt(3,ut["a"]),bt(4,st["a"]),bt(5,nt["c"])],t),t}(),Nt=function(){function t(t,e,i,n,o){var r=this;this.widget=e,this.editor=i,this.markdownRenderer=n,this.kbToggleDetails=o,this.borderWidth=1,this.disposables=new a["b"],this.el=Object(X["q"])(t,Object(X["a"])(".details")),this.disposables.add(Object(a["h"])((function(){return t.removeChild(r.el)}))),this.body=Object(X["a"])(".body"),this.scrollbar=new Q["a"](this.body,{}),Object(X["q"])(this.el,this.scrollbar.getDomNode()),this.disposables.add(this.scrollbar),this.header=Object(X["q"])(this.body,Object(X["a"])(".header")),this.close=Object(X["q"])(this.header,Object(X["a"])("span.codicon.codicon-close")),this.close.title=j["a"]("readLess","Read less...{0}",this.kbToggleDetails),this.type=Object(X["q"])(this.header,Object(X["a"])("p.type")),this.docs=Object(X["q"])(this.body,Object(X["a"])("p.docs")),this.configureFont(),A["b"].chain(this.editor.onDidChangeConfiguration.bind(this.editor)).filter((function(t){return t.hasChanged(34)})).on(this.configureFont,this,this.disposables),n.onDidRenderCodeBlock((function(){return r.scrollbar.scanDomNode()}),this,this.disposables)}return Object.defineProperty(t.prototype,"element",{get:function(){return this.el},enumerable:!0,configurable:!0}),t.prototype.renderLoading=function(){this.type.textContent=j["a"]("loading","Loading..."),this.docs.textContent=""},t.prototype.renderItem=function(t,e){var i=this;this.renderDisposeable=Object(a["f"])(this.renderDisposeable);var n=t.completion,o=n.documentation,r=n.detail;if(e){var s="";s+="score: "+t.score[0]+(t.word?", compared '"+(t.completion.filterText&&t.completion.filterText+" (filterText)"||t.completion.label)+"' with '"+t.word+"'":" (no prefix)")+"\n",s+="distance: "+t.distance+", see localityBonus-setting\n",s+="index: "+t.idx+", based on "+(t.completion.sortText&&'sortText: "'+t.completion.sortText+'"'||"label")+"\n",o=(new pt["a"]).appendCodeblock("empty",s),r="Provider: "+t.provider._debugDisplayName}if(!e&&!Pt(t))return this.type.textContent="",this.docs.textContent="",void Object(X["f"])(this.el,"no-docs");if(Object(X["P"])(this.el,"no-docs"),"string"===typeof o)Object(X["P"])(this.docs,"markdown-docs"),this.docs.textContent=o;else{Object(X["f"])(this.docs,"markdown-docs"),this.docs.innerHTML="";var c=this.markdownRenderer.render(o);this.renderDisposeable=c,this.docs.appendChild(c.element)}r?(this.type.innerText=r,Object(X["X"])(this.type)):(this.type.innerText="",Object(X["J"])(this.type)),this.el.style.height=this.header.offsetHeight+this.docs.offsetHeight+2*this.borderWidth+"px",this.el.style.userSelect="text",this.el.tabIndex=-1,this.close.onmousedown=function(t){t.preventDefault(),t.stopPropagation()},this.close.onclick=function(t){t.preventDefault(),t.stopPropagation(),i.widget.toggleDetails()},this.body.scrollTop=0,this.scrollbar.scanDomNode()},t.prototype.scrollDown=function(t){void 0===t&&(t=8),this.body.scrollTop+=t},t.prototype.scrollUp=function(t){void 0===t&&(t=8),this.body.scrollTop-=t},t.prototype.scrollTop=function(){this.body.scrollTop=0},t.prototype.scrollBottom=function(){this.body.scrollTop=this.body.scrollHeight},t.prototype.pageDown=function(){this.scrollDown(80)},t.prototype.pageUp=function(){this.scrollUp(80)},t.prototype.setBorderWidth=function(t){this.borderWidth=t},t.prototype.configureFont=function(){var t=this.editor.getOptions(),e=t.get(34),i=e.fontFamily,n=t.get(90)||e.fontSize,o=t.get(91)||e.lineHeight,r=e.fontWeight,s=n+"px",a=o+"px";this.el.style.fontSize=s,this.el.style.fontWeight=r,this.el.style.fontFeatureSettings=e.fontFeatureSettings,this.type.style.fontFamily=i,this.close.style.height=a,this.close.style.width=a},t.prototype.dispose=function(){this.disposables.dispose(),this.renderDisposeable=Object(a["f"])(this.renderDisposeable)},t}(),Et=function(){function t(t,e,i,n,o,r,s,c,l){var u,d,h=this;this.editor=t,this.telemetryService=e,this.keybindingService=i,this.allowEditorOverflow=!0,this.suppressMouseDown=!1,this.state=null,this.isAuto=!1,this.loadingTimeout=a["a"].None,this.currentSuggestionDetails=null,this.ignoreFocusEvents=!1,this.completionModel=null,this.showTimeout=new b["e"],this.toDispose=new a["b"],this.onDidSelectEmitter=new A["a"],this.onDidFocusEmitter=new A["a"],this.onDidHideEmitter=new A["a"],this.onDidShowEmitter=new A["a"],this.onDidSelect=this.onDidSelectEmitter.event,this.onDidFocus=this.onDidFocusEmitter.event,this.onDidHide=this.onDidHideEmitter.event,this.onDidShow=this.onDidShowEmitter.event,this.maxWidgetWidth=660,this.listWidth=330,this.firstFocusInCurrentList=!1,this.preferDocPositionTop=!1,this.docsPositionPreviousWidgetY=null,this.explainMode=!1,this._onDetailsKeydown=new A["a"],this.onDetailsKeyDown=this._onDetailsKeydown.event;var p=this.toDispose.add(new rt["a"](t,s,c)),g=null!==(d=null===(u=i.lookupKeybinding("toggleSuggestionDetails"))||void 0===u?void 0:u.getLabel())&&void 0!==d?d:"";this.msgDetailsLess=j["a"]("detail.less","{0} for less...",g),this.msgDetailMore=j["a"]("detail.more","{0} for more...",g),this.isAuto=!1,this.focusedItem=null,this.storageService=r,this.element=Object(X["a"])(".editor-widget.suggest-widget"),this.toDispose.add(Object(X["j"])(this.element,"click",(function(t){t.target===h.element&&h.hideWidget()}))),this.messageElement=Object(X["q"])(this.element,Object(X["a"])(".message")),this.listElement=Object(X["q"])(this.element,Object(X["a"])(".tree"));var f=function(){return Object(X["Y"])(h.element,"with-status-bar",!h.editor.getOption(89).hideStatusBar)};f(),this.statusBarElement=Object(X["q"])(this.element,Object(X["a"])(".suggest-status-bar")),this.statusBarLeftSpan=Object(X["q"])(this.statusBarElement,Object(X["a"])("span")),this.statusBarRightSpan=Object(X["q"])(this.statusBarElement,Object(X["a"])("span")),this.setStatusBarLeftText(""),this.setStatusBarRightText(""),this.details=l.createInstance(Nt,this.element,this,this.editor,p,g);var m=function(){return Object(X["Y"])(h.element,"no-icons",!h.editor.getOption(89).showIcons)};m();var _=l.createInstance(jt,this,this.editor,g);this.list=new $["c"]("SuggestWidget",this.listElement,this,[_],{useShadows:!1,openController:{shouldOpen:function(){return!1}},mouseSupport:!1,accessibilityProvider:{getAriaLabel:function(t){var e="string"===typeof t.completion.label?t.completion.label:t.completion.label.name;if(t.isResolved&&h.expandDocsSettingFromStorage()){var i=t.completion,n=i.documentation,o=i.detail,r=V["r"]("{0}{1}",o||"",n?"string"===typeof n?n:n.value:"");return j["a"]("ariaCurrenttSuggestionReadDetails","Item {0}, docs: {1}",e,r)}return e}}}),this.toDispose.add(Object(it["b"])(this.list,o,{listInactiveFocusBackground:Ot,listInactiveFocusOutline:ot["b"]})),this.toDispose.add(o.onThemeChange((function(t){return h.onThemeChange(t)}))),this.toDispose.add(t.onDidLayoutChange((function(){return h.onEditorLayoutChange()}))),this.toDispose.add(this.list.onMouseDown((function(t){return h.onListMouseDownOrTap(t)}))),this.toDispose.add(this.list.onTap((function(t){return h.onListMouseDownOrTap(t)}))),this.toDispose.add(this.list.onSelectionChange((function(t){return h.onListSelection(t)}))),this.toDispose.add(this.list.onFocusChange((function(t){return h.onListFocus(t)}))),this.toDispose.add(this.editor.onDidChangeCursorSelection((function(){return h.onCursorSelectionChanged()}))),this.toDispose.add(this.editor.onDidChangeConfiguration((function(t){t.hasChanged(89)&&(f(),m())}))),this.suggestWidgetVisible=M["b"].Visible.bindTo(n),this.suggestWidgetMultipleSuggestions=M["b"].MultipleSuggestions.bindTo(n),this.editor.addContentWidget(this),this.setState(0),this.onThemeChange(o.getTheme()),this.toDispose.add(Object(X["o"])(this.details.element,"keydown",(function(t){h._onDetailsKeydown.fire(t)}))),this.toDispose.add(this.editor.onMouseDown((function(t){return h.onEditorMouseDown(t)})))}return t.prototype.onEditorMouseDown=function(t){this.details.element.contains(t.target.element)?this.details.element.focus():this.element.contains(t.target.element)&&this.editor.focus()},t.prototype.onCursorSelectionChanged=function(){0!==this.state&&this.editor.layoutContentWidget(this)},t.prototype.onEditorLayoutChange=function(){3!==this.state&&5!==this.state||!this.expandDocsSettingFromStorage()||this.expandSideOrBelow()},t.prototype.onListMouseDownOrTap=function(t){"undefined"!==typeof t.element&&"undefined"!==typeof t.index&&(t.browserEvent.preventDefault(),t.browserEvent.stopPropagation(),this.select(t.element,t.index))},t.prototype.onListSelection=function(t){t.elements.length&&this.select(t.elements[0],t.indexes[0])},t.prototype.select=function(t,e){var i=this.completionModel;i&&(this.onDidSelectEmitter.fire({item:t,index:e,model:i}),this.editor.focus())},t.prototype.onThemeChange=function(t){var e=t.getColor(wt);e&&(this.listElement.style.backgroundColor=e.toString(),this.statusBarElement.style.backgroundColor=e.toString(),this.details.element.style.backgroundColor=e.toString(),this.messageElement.style.backgroundColor=e.toString());var i=t.getColor(St);i&&(this.listElement.style.borderColor=i.toString(),this.statusBarElement.style.borderColor=i.toString(),this.details.element.style.borderColor=i.toString(),this.messageElement.style.borderColor=i.toString(),this.detailsBorderColor=i.toString());var n=t.getColor(ot["V"]);n&&(this.detailsFocusBorderColor=n.toString()),this.details.setBorderWidth("hc"===t.type?2:1)},t.prototype.onListFocus=function(t){var e,i,n=this;if(!this.ignoreFocusEvents){if(!t.elements.length)return this.currentSuggestionDetails&&(this.currentSuggestionDetails.cancel(),this.currentSuggestionDetails=null,this.focusedItem=null),void this.editor.setAriaOptions({activeDescendant:void 0});if(this.completionModel){var o=t.elements[0],s=t.indexes[0];if(this.firstFocusInCurrentList=!this.focusedItem,o!==this.focusedItem){var a="insert"===this.editor.getOption(89).insertMode,c=null===(e=this.keybindingService.lookupKeybinding("acceptSelectedSuggestion"))||void 0===e?void 0:e.getLabel(),l=null===(i=this.keybindingService.lookupKeybinding("acceptAlternativeSelectedSuggestion"))||void 0===i?void 0:i.getLabel();gt["a"].equals(o.editInsertEnd,o.editReplaceEnd)?this.setStatusBarLeftText(j["a"]("accept","{0} to accept",c)):a?this.setStatusBarLeftText(j["a"]("insert","{0} to insert, {1} to replace",c,l)):this.setStatusBarLeftText(j["a"]("replace","{0} to replace, {1} to insert",c,l)),this.currentSuggestionDetails&&(this.currentSuggestionDetails.cancel(),this.currentSuggestionDetails=null),this.focusedItem=o,this.list.reveal(s),this.currentSuggestionDetails=Object(b["f"])((function(t){return _t(n,void 0,void 0,(function(){var e,i,n=this;return vt(this,(function(r){switch(r.label){case 0:return e=Object(b["g"])((function(){return n.showDetails(!0)}),250),t.onCancellationRequested((function(){return e.dispose()})),[4,o.resolve(t)];case 1:return i=r.sent(),e.dispose(),[2,i]}}))}))})),this.currentSuggestionDetails.then((function(){s>=n.list.length||o!==n.list.element(s)||(n.ignoreFocusEvents=!0,n.list.splice(s,1,[o]),n.list.setFocus([s]),n.ignoreFocusEvents=!1,n.expandDocsSettingFromStorage()?n.showDetails(!1):Object(X["P"])(n.element,"docs-side"),Pt(n.focusedItem)?n.expandDocsSettingFromStorage()?n.setStatusBarRightText(n.msgDetailsLess):n.setStatusBarRightText(n.msgDetailMore):n.statusBarRightSpan.innerText="",n.editor.setAriaOptions({activeDescendant:Tt(s)}))})).catch(r["e"])}this.onDidFocusEmitter.fire({item:o,index:s,model:this.completionModel})}}},t.prototype.setState=function(e){if(this.element){var i=this.state!==e;switch(this.state=e,Object(X["Y"])(this.element,"frozen",4===e),e){case 0:Object(X["J"])(this.messageElement,this.details.element,this.listElement,this.statusBarElement),this.hide(),this.listHeight=0,i&&this.list.splice(0,this.list.length),this.focusedItem=null;break;case 1:this.messageElement.textContent=t.LOADING_MESSAGE,Object(X["J"])(this.listElement,this.details.element,this.statusBarElement),Object(X["X"])(this.messageElement),Object(X["P"])(this.element,"docs-side"),this.show(),this.focusedItem=null;break;case 2:this.messageElement.textContent=t.NO_SUGGESTIONS_MESSAGE,Object(X["J"])(this.listElement,this.details.element,this.statusBarElement),Object(X["X"])(this.messageElement),Object(X["P"])(this.element,"docs-side"),this.show(),this.focusedItem=null;break;case 3:Object(X["J"])(this.messageElement),Object(X["X"])(this.listElement,this.statusBarElement),this.show();break;case 4:Object(X["J"])(this.messageElement),Object(X["X"])(this.listElement),this.show();break;case 5:Object(X["J"])(this.messageElement),Object(X["X"])(this.details.element,this.listElement,this.statusBarElement),this.show();break}}},t.prototype.showTriggered=function(t,e){var i=this;0===this.state&&(this.isAuto=!!t,this.isAuto||(this.loadingTimeout=Object(b["g"])((function(){return i.setState(1)}),e)))},t.prototype.showSuggestions=function(t,e,i,n){if(this.preferDocPositionTop=!1,this.docsPositionPreviousWidgetY=null,this.loadingTimeout.dispose(),this.currentSuggestionDetails&&(this.currentSuggestionDetails.cancel(),this.currentSuggestionDetails=null),this.completionModel!==t&&(this.completionModel=t),i&&2!==this.state&&0!==this.state)this.setState(4);else{var o=this.completionModel.items.length,r=0===o;if(this.suggestWidgetMultipleSuggestions.set(o>1),r)n?this.setState(0):this.setState(2),this.completionModel=null;else{if(3!==this.state){var s=this.completionModel.stats;s["wasAutomaticallyTriggered"]=!!n,this.telemetryService.publicLog("suggestWidget",ft({},s))}this.focusedItem=null,this.list.splice(0,this.list.length,this.completionModel.items),i?this.setState(4):this.setState(3),this.list.reveal(e,0),this.list.setFocus([e]),this.detailsBorderColor&&(this.details.element.style.borderColor=this.detailsBorderColor)}}},t.prototype.selectNextPage=function(){switch(this.state){case 0:return!1;case 5:return this.details.pageDown(),!0;case 1:return!this.isAuto;default:return this.list.focusNextPage(),!0}},t.prototype.selectNext=function(){switch(this.state){case 0:return!1;case 1:return!this.isAuto;default:return this.list.focusNext(1,!0),!0}},t.prototype.selectLast=function(){switch(this.state){case 0:return!1;case 5:return this.details.scrollBottom(),!0;case 1:return!this.isAuto;default:return this.list.focusLast(),!0}},t.prototype.selectPreviousPage=function(){switch(this.state){case 0:return!1;case 5:return this.details.pageUp(),!0;case 1:return!this.isAuto;default:return this.list.focusPreviousPage(),!0}},t.prototype.selectPrevious=function(){switch(this.state){case 0:return!1;case 1:return!this.isAuto;default:return this.list.focusPrevious(1,!0),!1}},t.prototype.selectFirst=function(){switch(this.state){case 0:return!1;case 5:return this.details.scrollTop(),!0;case 1:return!this.isAuto;default:return this.list.focusFirst(),!0}},t.prototype.getFocusedItem=function(){if(0!==this.state&&2!==this.state&&1!==this.state&&this.completionModel)return{item:this.list.getFocusedElements()[0],index:this.list.getFocus()[0],model:this.completionModel}},t.prototype.toggleDetailsFocus=function(){5===this.state?(this.setState(3),this.detailsBorderColor&&(this.details.element.style.borderColor=this.detailsBorderColor)):3===this.state&&this.expandDocsSettingFromStorage()&&(this.setState(5),this.detailsFocusBorderColor&&(this.details.element.style.borderColor=this.detailsFocusBorderColor)),this.telemetryService.publicLog2("suggestWidget:toggleDetailsFocus")},t.prototype.toggleDetails=function(){if(Pt(this.list.getFocusedElements()[0]))if(this.expandDocsSettingFromStorage())this.updateExpandDocsSetting(!1),Object(X["J"])(this.details.element),Object(X["P"])(this.element,"docs-side"),Object(X["P"])(this.element,"docs-below"),this.editor.layoutContentWidget(this),this.setStatusBarRightText(this.msgDetailMore),this.telemetryService.publicLog2("suggestWidget:collapseDetails");else{if(3!==this.state&&5!==this.state&&4!==this.state)return;this.updateExpandDocsSetting(!0),this.showDetails(!1),this.setStatusBarRightText(this.msgDetailsLess),this.telemetryService.publicLog2("suggestWidget:expandDetails")}},t.prototype.showDetails=function(t){t||this.expandSideOrBelow(),Object(X["X"])(this.details.element),this.details.element.style.maxHeight=this.maxWidgetHeight+"px",t?this.details.renderLoading():this.details.renderItem(this.list.getFocusedElements()[0],this.explainMode),this.listElement.style.marginTop="0px",this.editor.layoutContentWidget(this),this.adjustDocsPosition(),this.editor.focus()},t.prototype.toggleExplainMode=function(){this.list.getFocusedElements()[0]&&this.expandDocsSettingFromStorage()&&(this.explainMode=!this.explainMode,this.showDetails(!1))},t.prototype.show=function(){var t=this,e=this.updateListHeight();e!==this.listHeight&&(this.editor.layoutContentWidget(this),this.listHeight=e),this.suggestWidgetVisible.set(!0),this.showTimeout.cancelAndSet((function(){Object(X["f"])(t.element,"visible"),t.onDidShowEmitter.fire(t)}),100)},t.prototype.hide=function(){this.suggestWidgetVisible.reset(),this.suggestWidgetMultipleSuggestions.reset(),Object(X["P"])(this.element,"visible")},t.prototype.hideWidget=function(){this.loadingTimeout.dispose(),this.setState(0),this.onDidHideEmitter.fire(this)},t.prototype.getPosition=function(){if(0===this.state)return null;var t=[2,1];return this.preferDocPositionTop&&(t=[1]),{position:this.editor.getPosition(),preference:t}},t.prototype.getDomNode=function(){return this.element},t.prototype.getId=function(){return t.ID},t.prototype.isFrozen=function(){return 4===this.state},t.prototype.updateListHeight=function(){var t=0;if(2===this.state||1===this.state)t=this.unfocusedHeight;else{var e=this.list.contentHeight/this.unfocusedHeight,i=this.editor.getOption(89).maxVisibleSuggestions;t=Math.min(e,i)*this.unfocusedHeight}return this.element.style.lineHeight=this.unfocusedHeight+"px",this.listElement.style.height=t+"px",this.statusBarElement.style.top=t+"px",this.list.layout(t),t},t.prototype.adjustDocsPosition=function(){if(this.editor.hasModel()){var t=this.editor.getOption(49),e=this.editor.getScrolledVisiblePosition(this.editor.getPosition()),i=Object(X["C"])(this.editor.getDomNode()),n=i.left+e.left,o=i.top+e.top+e.height,r=Object(X["C"])(this.element),s=r.left,a=r.top;if(this.docsPositionPreviousWidgetY&&this.docsPositionPreviousWidgetY<a&&!this.preferDocPositionTop)return this.preferDocPositionTop=!0,void this.adjustDocsPosition();this.docsPositionPreviousWidgetY=a,s<n-this.listWidth?Object(X["f"])(this.element,"list-right"):Object(X["P"])(this.element,"list-right"),Object(X["I"])(this.element,"docs-side")&&o-t>a&&this.details.element.offsetHeight>this.listElement.offsetHeight&&(this.listElement.style.marginTop=this.details.element.offsetHeight-this.listElement.offsetHeight+"px")}},t.prototype.expandSideOrBelow=function(){if(!Pt(this.focusedItem)&&this.firstFocusInCurrentList)return Object(X["P"])(this.element,"docs-side"),void Object(X["P"])(this.element,"docs-below");var t=this.element.style.maxWidth.match(/(\d+)px/);!t||Number(t[1])<this.maxWidgetWidth?(Object(X["f"])(this.element,"docs-below"),Object(X["P"])(this.element,"docs-side")):Pt(this.focusedItem)&&(Object(X["f"])(this.element,"docs-side"),Object(X["P"])(this.element,"docs-below"))},Object.defineProperty(t.prototype,"maxWidgetHeight",{get:function(){return this.unfocusedHeight*this.editor.getOption(89).maxVisibleSuggestions},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"unfocusedHeight",{get:function(){var t=this.editor.getOptions();return t.get(91)||t.get(34).lineHeight},enumerable:!0,configurable:!0}),t.prototype.getHeight=function(t){return this.unfocusedHeight},t.prototype.getTemplateId=function(t){return"suggestion"},t.prototype.expandDocsSettingFromStorage=function(){return this.storageService.getBoolean("expandSuggestionDocs",0,yt)},t.prototype.updateExpandDocsSetting=function(t){this.storageService.store("expandSuggestionDocs",t,0)},t.prototype.setStatusBarLeftText=function(t){this.statusBarLeftSpan.innerText=t},t.prototype.setStatusBarRightText=function(t){this.statusBarRightSpan.innerText=t},t.prototype.dispose=function(){this.details.dispose(),this.list.dispose(),this.toDispose.dispose(),this.loadingTimeout.dispose(),this.showTimeout.dispose()},t.ID="editor.widget.suggestWidget",t.LOADING_MESSAGE=j["a"]("suggestWidget.loading","Loading..."),t.NO_SUGGESTIONS_MESSAGE=j["a"]("suggestWidget.noSuggestions","No suggestions."),t=mt([bt(1,et["a"]),bt(2,tt["a"]),bt(3,E["c"]),bt(4,nt["c"]),bt(5,f["a"]),bt(6,st["a"]),bt(7,at["a"]),bt(8,_["a"])],t),t}();Object(nt["e"])((function(t,e){var i=t.getColor(xt);i&&e.addRule(".monaco-editor .suggest-widget .monaco-list .monaco-list-row .monaco-highlighted-label .highlight { color: "+i+"; }");var n=t.getColor(Ct);n&&e.addRule(".monaco-editor .suggest-widget { color: "+n+"; }");var o=t.getColor(ot["ec"]);o&&e.addRule(".monaco-editor .suggest-widget a { color: "+o+"; }");var r=t.getColor(ot["dc"]);r&&e.addRule(".monaco-editor .suggest-widget code { background-color: "+r+"; }")}));var Lt=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])},t(e,i)};return function(e,i){function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Mt=function(t,e,i,n){var o,r=arguments.length,s=r<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)s=Reflect.decorate(t,e,i,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(r<3?o(s):r>3?o(e,i,s):o(e,i))||s);return r>3&&s&&Object.defineProperty(e,i,s),s},It=function(t,e){return function(i,n){e(i,n,t)}},Rt=function(t){function e(i,n){var o=t.call(this)||this;return o._editor=i,o._enabled=!1,o._ckAtEnd=e.AtEnd.bindTo(n),o._register(o._editor.onDidChangeConfiguration((function(t){return t.hasChanged(94)&&o._update()}))),o._update(),o}return Lt(e,t),e.prototype.dispose=function(){t.prototype.dispose.call(this),Object(a["f"])(this._selectionListener),this._ckAtEnd.reset()},e.prototype._update=function(){var t=this,e="on"===this._editor.getOption(94);if(this._enabled!==e)if(this._enabled=e,this._enabled){var i=function(){if(t._editor.hasModel()){var e=t._editor.getModel(),i=t._editor.getSelection(),n=e.getWordAtPosition(i.getStartPosition());n?t._ckAtEnd.set(n.endColumn===i.getStartPosition().column):t._ckAtEnd.set(!1)}else t._ckAtEnd.set(!1)};this._selectionListener=this._editor.onDidChangeCursorSelection(i),i()}else this._selectionListener&&(this._ckAtEnd.reset(),this._selectionListener.dispose(),this._selectionListener=void 0)},e.AtEnd=new E["d"]("atEndOfWord",!1),e=Mt([It(1,E["c"])],e),e}(a["a"]),Wt=i("a40b"),At=i("ef8e"),Ft=i("3170"),Ht=function(){function t(t,e,i){var n=this;this._disposables=new a["b"],this._disposables.add(e.onDidShow((function(){return n._onItem(e.getFocusedItem())}))),this._disposables.add(e.onDidFocus(this._onItem,this)),this._disposables.add(e.onDidHide(this.reset,this)),this._disposables.add(t.onWillType((function(o){if(n._active&&!e.isFrozen()){var r=o.charCodeAt(o.length-1);n._active.acceptCharacters.has(r)&&t.getOption(0)&&i(n._active.item)}})))}return t.prototype._onItem=function(t){if(t&&Object(o["q"])(t.item.completion.commitCharacters)){if(!this._active||this._active.item.item!==t.item){for(var e=new Ft["b"],i=0,n=t.item.completion.commitCharacters;i<n.length;i++){var r=n[i];r.length>0&&e.add(r.charCodeAt(0))}this._active={acceptCharacters:e,item:t}}}else this.reset()},t.prototype.reset=function(){this._active=void 0},t.prototype.dispose=function(){this._disposables.dispose()},t}(),Vt=i("30db"),Bt=i("e32d"),qt=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])},t(e,i)};return function(e,i){function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Ut=function(){function t(t){var e=this;this._controller=t,this._disposables=new a["b"],this._decorations=[],this._disposables.add(t.model.onDidSuggest((function(t){if(!t.shy){var i=e._controller.widget.getValue(),n=i.getFocusedItem();n&&e._highlight(n.item),e._widgetListener||(e._widgetListener=i.onDidFocus((function(t){return e._highlight(t.item)})))}}))),this._disposables.add(t.model.onDidCancel((function(){e._reset()})))}return t.prototype.dispose=function(){this._reset(),this._disposables.dispose(),Object(a["f"])(this._widgetListener),Object(a["f"])(this._shiftKeyListener)},t.prototype._reset=function(){this._decorations=this._controller.editor.deltaDecorations(this._decorations,[]),this._shiftKeyListener&&(this._shiftKeyListener.dispose(),this._shiftKeyListener=void 0)},t.prototype._highlight=function(t){var e,i=this;this._currentItem=t;var n=this._controller.editor.getOption(89),o=[];if(n.insertHighlight){this._shiftKeyListener||(this._shiftKeyListener=Kt.event((function(){return i._highlight(i._currentItem)})));var r=this._controller.getOverwriteInfo(t,Kt.isPressed),s=this._controller.editor.getPosition();if("insert"===n.insertMode&&r.overwriteAfter>0)o=[{range:new u["a"](s.lineNumber,s.column,s.lineNumber,s.column+r.overwriteAfter),options:{inlineClassName:"suggest-insert-unexpected"}}];else if("replace"===n.insertMode&&0===r.overwriteAfter){var a=null===(e=this._controller.editor.getModel())||void 0===e?void 0:e.getWordAtPosition(s);a&&a.endColumn>s.column&&(o=[{range:new u["a"](s.lineNumber,s.column,s.lineNumber,a.endColumn),options:{inlineClassName:"suggest-insert-unexpected"}}])}}this._decorations=this._controller.editor.deltaDecorations(this._decorations,o)},t}(),Kt=new(function(t){function e(){var e=t.call(this)||this;return e._subscriptions=new a["b"],e._isPressed=!1,e._subscriptions.add(Object(Bt["a"])(document.body,"keydown")((function(t){return e.isPressed=t.shiftKey}))),e._subscriptions.add(Object(Bt["a"])(document.body,"keyup")((function(){return e.isPressed=!1}))),e._subscriptions.add(Object(Bt["a"])(document.body,"mouseleave")((function(){return e.isPressed=!1}))),e._subscriptions.add(Object(Bt["a"])(document.body,"blur")((function(){return e.isPressed=!1}))),e}return qt(e,t),Object.defineProperty(e.prototype,"isPressed",{get:function(){return this._isPressed},set:function(t){this._isPressed!==t&&(this._isPressed=t,this.fire(t))},enumerable:!0,configurable:!0}),e.prototype.dispose=function(){this._subscriptions.dispose(),t.prototype.dispose.call(this)},e}(A["a"])),zt=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])},t(e,i)};return function(e,i){function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Gt=function(){return Gt=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++)for(var o in e=arguments[i],e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},Gt.apply(this,arguments)},Zt=function(t,e,i,n){var o,r=arguments.length,s=r<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)s=Reflect.decorate(t,e,i,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(r<3?o(s):r>3?o(e,i,s):o(e,i))||s);return r>3&&s&&Object.defineProperty(e,i,s),s},Jt=function(t,e){return function(i,n){e(i,n,t)}},Yt=function(){for(var t=0,e=0,i=arguments.length;e<i;e++)t+=arguments[e].length;var n=Array(t),o=0;for(e=0;e<i;e++)for(var r=arguments[e],s=0,a=r.length;s<a;s++,o++)n[o]=r[s];return n},Xt=!1,$t=function(){function t(t,e){this._model=t,this._position=e;var i=t.getLineMaxColumn(e.lineNumber);if(i!==e.column){var n=t.getOffsetAt(e),o=t.getPositionAt(n+1);this._marker=t.deltaDecorations([],[{range:u["a"].fromPositions(e,o),options:{stickiness:1}}])}}return t.prototype.dispose=function(){this._marker&&!this._model.isDisposed()&&this._model.deltaDecorations(this._marker,[])},t.prototype.delta=function(t){if(this._model.isDisposed()||this._position.lineNumber!==t.lineNumber)return 0;if(this._marker){var e=this._model.getDecorationRange(this._marker[0]),i=this._model.getOffsetAt(e.getStartPosition());return i-this._model.getOffsetAt(t)}return this._model.getLineMaxColumn(t.lineNumber)-t.column},t}(),Qt=function(){function t(t,e,i,n,o,r){var c=this;this._memoryService=i,this._commandService=n,this._contextKeyService=o,this._instantiationService=r,this._lineSuffix=new a["d"],this._toDispose=new a["b"],this.editor=t,this.model=new Y(this.editor,e),this.widget=this._toDispose.add(new b["b"]((function(){var t=c._instantiationService.createInstance(Et,c.editor);c._toDispose.add(t),c._toDispose.add(t.onDidSelect((function(t){return c._insertSuggestion(t,0)}),c));var e=new Ht(c.editor,t,(function(t){return c._insertSuggestion(t,2)}));c._toDispose.add(e),c._toDispose.add(c.model.onDidSuggest((function(t){0===t.completionModel.items.length&&e.reset()})));var i=M["b"].MakesTextEdit.bindTo(c._contextKeyService);return c._toDispose.add(t.onDidFocus((function(t){var e=t.item,n=c.editor.getPosition(),o=e.editStart.column,r=n.column,s=!0;if("smart"===c.editor.getOption(1)&&2===c.model.state&&!e.completion.command&&!e.completion.additionalTextEdits&&!(4&e.completion.insertTextRules)&&r-o===e.completion.insertText.length){var a=c.editor.getModel().getValueInRange({startLineNumber:n.lineNumber,startColumn:o,endLineNumber:n.lineNumber,endColumn:r});s=a!==e.completion.insertText}i.set(s)}))),c._toDispose.add(Object(a["h"])((function(){return i.reset()}))),c._toDispose.add(t.onDetailsKeyDown((function(t){t.toKeybinding().equals(new s["e"](!0,!1,!1,!1,33))||Vt["e"]&&t.toKeybinding().equals(new s["e"](!1,!1,!1,!0,33))?t.stopPropagation():t.toKeybinding().isModifierKey()||c.editor.focus()}))),t}))),this._alternatives=this._toDispose.add(new b["b"]((function(){return c._toDispose.add(new W(c.editor,c._contextKeyService))}))),this._toDispose.add(r.createInstance(Rt,t)),this._toDispose.add(this.model.onDidTrigger((function(t){c.widget.getValue().showTriggered(t.auto,t.shy?250:50),c._lineSuffix.value=new $t(c.editor.getModel(),t.position)}))),this._toDispose.add(this.model.onDidSuggest((function(t){if(!t.shy){var e=c._memoryService.select(c.editor.getModel(),c.editor.getPosition(),t.completionModel.items);c.widget.getValue().showSuggestions(t.completionModel,e,t.isFrozen,t.auto)}}))),this._toDispose.add(this.model.onDidCancel((function(t){t.retrigger||c.widget.getValue().hideWidget()}))),this._toDispose.add(this.editor.onDidBlurEditorWidget((function(){Xt||(c.model.cancel(),c.model.clear())})));var l=M["b"].AcceptSuggestionsOnEnter.bindTo(o),u=function(){var t=c.editor.getOption(1);l.set("on"===t||"smart"===t)};this._toDispose.add(this.editor.onDidChangeConfiguration((function(){return u()}))),u(),this._toDispose.add(new Ut(this))}return t.get=function(e){return e.getContribution(t.ID)},t.prototype.dispose=function(){this._alternatives.dispose(),this._toDispose.dispose(),this.widget.dispose(),this.model.dispose(),this._lineSuffix.dispose()},t.prototype._insertSuggestion=function(t,e){var i,n=this;if(!t||!t.item)return this._alternatives.getValue().reset(),this.model.cancel(),void this.model.clear();if(this.editor.hasModel()){var o=this.editor.getModel(),s=o.getAlternativeVersionId(),a=t.item,c=a.completion;1&e||this.editor.pushUndoStop();var d=this.getOverwriteInfo(a,Boolean(8&e));this._memoryService.memorize(o,this.editor.getPosition(),a),Array.isArray(c.additionalTextEdits)&&this.editor.executeEdits("suggestController.additionalTextEdits",c.additionalTextEdits.map((function(t){return l["a"].replace(u["a"].lift(t.range),t.text)})));var g=c.insertText;4&c.insertTextRules||(g=p["c"].escape(g)),h["SnippetController2"].get(this.editor).insert(g,{overwriteBefore:d.overwriteBefore,overwriteAfter:d.overwriteAfter,undoStopBefore:!1,undoStopAfter:!1,adjustWhitespace:!(1&c.insertTextRules)}),2&e||this.editor.pushUndoStop(),c.command?c.command.id===te.id?this.model.trigger({auto:!0,shy:!1},!0):((i=this._commandService).executeCommand.apply(i,Yt([c.command.id],c.command.arguments?Yt(c.command.arguments):[])).catch(r["e"]).finally((function(){return n.model.clear()})),this.model.cancel()):(this.model.cancel(),this.model.clear()),4&e&&this._alternatives.getValue().set(t,(function(t){while(o.canUndo()){s!==o.getAlternativeVersionId()&&o.undo(),n._insertSuggestion(t,3|(8&e?8:0));break}})),this._alertCompletionItem(t.item)}},t.prototype.getOverwriteInfo=function(t,e){Object(At["a"])(this.editor.hasModel());var i="replace"===this.editor.getOption(89).insertMode;e&&(i=!i);var n=t.position.column-t.editStart.column,o=(i?t.editReplaceEnd.column:t.editInsertEnd.column)-t.position.column,r=this.editor.getPosition().column-t.position.column,s=this._lineSuffix.value?this._lineSuffix.value.delta(this.editor.getPosition()):0;return{overwriteBefore:n+r,overwriteAfter:o+s}},t.prototype._alertCompletionItem=function(t){var e=t.completion,i="string"===typeof e.label?e.label:e.label.name;if(Object(o["q"])(e.additionalTextEdits)){var r=j["a"]("arai.alert.snippet","Accepting '{0}' made {1} additional edits",i,e.additionalTextEdits.length);Object(n["a"])(r)}},t.prototype.triggerSuggest=function(t){this.editor.hasModel()&&(this.model.trigger({auto:!1,shy:!1},!1,t),this.editor.revealLine(this.editor.getPosition().lineNumber,0),this.editor.focus())},t.prototype.triggerSuggestAndAcceptBest=function(t){var e=this;if(this.editor.hasModel()){var i=this.editor.getPosition(),n=function(){i.equals(e.editor.getPosition())&&e._commandService.executeCommand(t.fallback)},o=function(t){if(4&t.completion.insertTextRules||t.completion.additionalTextEdits)return!0;var i=e.editor.getPosition(),n=t.editStart.column,o=i.column;if(o-n!==t.completion.insertText.length)return!0;var r=e.editor.getModel().getValueInRange({startLineNumber:i.lineNumber,startColumn:n,endLineNumber:i.lineNumber,endColumn:o});return r!==t.completion.insertText};A["b"].once(this.model.onDidTrigger)((function(t){var i=[];A["b"].any(e.model.onDidTrigger,e.model.onDidCancel)((function(){Object(a["f"])(i),n()}),void 0,i),e.model.onDidSuggest((function(t){var r=t.completionModel;if(Object(a["f"])(i),0!==r.items.length){var s=e._memoryService.select(e.editor.getModel(),e.editor.getPosition(),r.items),c=r.items[s];o(c)?(e.editor.pushUndoStop(),e._insertSuggestion({index:s,item:c,model:r},7)):n()}else n()}),void 0,i)})),this.model.trigger({auto:!1,shy:!0}),this.editor.revealLine(i.lineNumber,0),this.editor.focus()}},t.prototype.acceptSelectedSuggestion=function(t,e){var i=this.widget.getValue().getFocusedItem(),n=0;t&&(n|=4),e&&(n|=8),this._insertSuggestion(i,n)},t.prototype.acceptNextSuggestion=function(){this._alternatives.getValue().next()},t.prototype.acceptPrevSuggestion=function(){this._alternatives.getValue().prev()},t.prototype.cancelSuggestWidget=function(){this.model.cancel(),this.model.clear(),this.widget.getValue().hideWidget()},t.prototype.selectNextSuggestion=function(){this.widget.getValue().selectNext()},t.prototype.selectNextPageSuggestion=function(){this.widget.getValue().selectNextPage()},t.prototype.selectLastSuggestion=function(){this.widget.getValue().selectLast()},t.prototype.selectPrevSuggestion=function(){this.widget.getValue().selectPrevious()},t.prototype.selectPrevPageSuggestion=function(){this.widget.getValue().selectPreviousPage()},t.prototype.selectFirstSuggestion=function(){this.widget.getValue().selectFirst()},t.prototype.toggleSuggestionDetails=function(){this.widget.getValue().toggleDetails()},t.prototype.toggleExplainMode=function(){this.widget.getValue().toggleExplainMode()},t.prototype.toggleSuggestionFocus=function(){this.widget.getValue().toggleDetailsFocus()},t.ID="editor.contrib.suggestController",t=Zt([Jt(1,Wt["a"]),Jt(2,T),Jt(3,N["b"]),Jt(4,E["c"]),Jt(5,_["a"])],t),t}(),te=function(t){function e(){return t.call(this,{id:e.id,label:j["a"]("suggest.trigger.label","Trigger Suggest"),alias:"Trigger Suggest",precondition:E["a"].and(d["a"].writable,d["a"].hasCompletionItemProvider),kbOpts:{kbExpr:d["a"].textInputFocus,primary:2058,mac:{primary:266,secondary:[521]},weight:100}})||this}return zt(e,t),e.prototype.run=function(t,e){var i=Qt.get(e);i&&i.triggerSuggest()},e.id="editor.action.triggerSuggest",e}(c["b"]);Object(c["h"])(Qt.ID,Qt),Object(c["f"])(te);var ee=190,ie=c["c"].bindToContribution(Qt.get);Object(c["g"])(new ie({id:"acceptSelectedSuggestion",precondition:M["b"].Visible,handler:function(t){t.acceptSelectedSuggestion(!0,!1)}})),L["a"].registerKeybindingRule({id:"acceptSelectedSuggestion",when:E["a"].and(M["b"].Visible,d["a"].textInputFocus),primary:2,weight:ee}),L["a"].registerKeybindingRule({id:"acceptSelectedSuggestion",when:E["a"].and(M["b"].Visible,d["a"].textInputFocus,M["b"].AcceptSuggestionsOnEnter,M["b"].MakesTextEdit),primary:3,weight:ee}),Object(c["g"])(new ie({id:"acceptAlternativeSelectedSuggestion",precondition:E["a"].and(M["b"].Visible,d["a"].textInputFocus),kbOpts:{weight:ee,kbExpr:d["a"].textInputFocus,primary:1027,secondary:[1026]},handler:function(t){t.acceptSelectedSuggestion(!1,!0)}})),N["a"].registerCommandAlias("acceptSelectedSuggestionOnEnter","acceptSelectedSuggestion"),Object(c["g"])(new ie({id:"hideSuggestWidget",precondition:M["b"].Visible,handler:function(t){return t.cancelSuggestWidget()},kbOpts:{weight:ee,kbExpr:d["a"].textInputFocus,primary:9,secondary:[1033]}})),Object(c["g"])(new ie({id:"selectNextSuggestion",precondition:E["a"].and(M["b"].Visible,M["b"].MultipleSuggestions),handler:function(t){return t.selectNextSuggestion()},kbOpts:{weight:ee,kbExpr:d["a"].textInputFocus,primary:18,secondary:[2066],mac:{primary:18,secondary:[2066,300]}}})),Object(c["g"])(new ie({id:"selectNextPageSuggestion",precondition:E["a"].and(M["b"].Visible,M["b"].MultipleSuggestions),handler:function(t){return t.selectNextPageSuggestion()},kbOpts:{weight:ee,kbExpr:d["a"].textInputFocus,primary:12,secondary:[2060]}})),Object(c["g"])(new ie({id:"selectLastSuggestion",precondition:E["a"].and(M["b"].Visible,M["b"].MultipleSuggestions),handler:function(t){return t.selectLastSuggestion()}})),Object(c["g"])(new ie({id:"selectPrevSuggestion",precondition:E["a"].and(M["b"].Visible,M["b"].MultipleSuggestions),handler:function(t){return t.selectPrevSuggestion()},kbOpts:{weight:ee,kbExpr:d["a"].textInputFocus,primary:16,secondary:[2064],mac:{primary:16,secondary:[2064,302]}}})),Object(c["g"])(new ie({id:"selectPrevPageSuggestion",precondition:E["a"].and(M["b"].Visible,M["b"].MultipleSuggestions),handler:function(t){return t.selectPrevPageSuggestion()},kbOpts:{weight:ee,kbExpr:d["a"].textInputFocus,primary:11,secondary:[2059]}})),Object(c["g"])(new ie({id:"selectFirstSuggestion",precondition:E["a"].and(M["b"].Visible,M["b"].MultipleSuggestions),handler:function(t){return t.selectFirstSuggestion()}})),Object(c["g"])(new ie({id:"toggleSuggestionDetails",precondition:M["b"].Visible,handler:function(t){return t.toggleSuggestionDetails()},kbOpts:{weight:ee,kbExpr:d["a"].textInputFocus,primary:2058,mac:{primary:266}}})),Object(c["g"])(new ie({id:"toggleExplainMode",precondition:M["b"].Visible,handler:function(t){return t.toggleExplainMode()},kbOpts:{weight:100,primary:2133}})),Object(c["g"])(new ie({id:"toggleSuggestionFocus",precondition:M["b"].Visible,handler:function(t){return t.toggleSuggestionFocus()},kbOpts:{weight:ee,kbExpr:d["a"].textInputFocus,primary:2570,mac:{primary:778}}})),Object(c["g"])(new ie({id:"insertBestCompletion",precondition:E["a"].and(E["a"].equals("config.editor.tabCompletion","on"),Rt.AtEnd,M["b"].Visible.toNegated(),W.OtherSuggestions.toNegated(),h["SnippetController2"].InSnippetMode.toNegated()),handler:function(t,e){t.triggerSuggestAndAcceptBest(Object(At["i"])(e)?Gt({fallback:"tab"},e):{fallback:"tab"})},kbOpts:{weight:ee,primary:2}})),Object(c["g"])(new ie({id:"insertNextSuggestion",precondition:E["a"].and(E["a"].equals("config.editor.tabCompletion","on"),W.OtherSuggestions,M["b"].Visible.toNegated(),h["SnippetController2"].InSnippetMode.toNegated()),handler:function(t){return t.acceptNextSuggestion()},kbOpts:{weight:ee,kbExpr:d["a"].textInputFocus,primary:2}})),Object(c["g"])(new ie({id:"insertPrevSuggestion",precondition:E["a"].and(E["a"].equals("config.editor.tabCompletion","on"),W.OtherSuggestions,M["b"].Visible.toNegated(),h["SnippetController2"].InSnippetMode.toNegated()),handler:function(t){return t.acceptPrevSuggestion()},kbOpts:{weight:ee,kbExpr:d["a"].textInputFocus,primary:1026}}))},"93ba":function(t,e,i){"use strict";i.r(e),i.d(e,"ToggleTabFocusModeAction",(function(){return c}));var n=i("dff7"),o=i("3813"),r=i("b2cc"),s=i("8830"),a=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])},t(e,i)};return function(e,i){function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),c=function(t){function e(){return t.call(this,{id:e.ID,label:n["a"]({key:"toggle.tabMovesFocus",comment:["Turn on/off use of tab key for moving focus around VS Code"]},"Toggle Tab Key Moves Focus"),alias:"Toggle Tab Key Moves Focus",precondition:void 0,kbOpts:{kbExpr:null,primary:2091,mac:{primary:1323},weight:100}})||this}return a(e,t),e.prototype.run=function(t,e){var i=s["b"].getTabFocusMode(),r=!i;s["b"].setTabFocusMode(r),r?Object(o["a"])(n["a"]("toggle.tabMovesFocus.on","Pressing Tab will now move focus to the next focusable element")):Object(o["a"])(n["a"]("toggle.tabMovesFocus.off","Pressing Tab will now insert the tab character"))},e.ID="editor.action.toggleTabFocusMode",e}(r["b"]);Object(r["f"])(c)},"9e7e":function(t,e,i){},b3b2:function(t,e,i){"use strict";i.r(e),i.d(e,"MoveWordCommand",(function(){return m})),i.d(e,"WordLeftCommand",(function(){return b})),i.d(e,"WordRightCommand",(function(){return _})),i.d(e,"CursorWordStartLeft",(function(){return v})),i.d(e,"CursorWordEndLeft",(function(){return y})),i.d(e,"CursorWordLeft",(function(){return w})),i.d(e,"CursorWordStartLeftSelect",(function(){return S})),i.d(e,"CursorWordEndLeftSelect",(function(){return C})),i.d(e,"CursorWordLeftSelect",(function(){return O})),i.d(e,"CursorWordAccessibilityLeft",(function(){return x})),i.d(e,"CursorWordAccessibilityLeftSelect",(function(){return k})),i.d(e,"CursorWordStartRight",(function(){return D})),i.d(e,"CursorWordEndRight",(function(){return P})),i.d(e,"CursorWordRight",(function(){return T})),i.d(e,"CursorWordStartRightSelect",(function(){return j})),i.d(e,"CursorWordEndRightSelect",(function(){return N})),i.d(e,"CursorWordRightSelect",(function(){return E})),i.d(e,"CursorWordAccessibilityRight",(function(){return L})),i.d(e,"CursorWordAccessibilityRightSelect",(function(){return M})),i.d(e,"DeleteWordCommand",(function(){return I})),i.d(e,"DeleteWordLeftCommand",(function(){return R})),i.d(e,"DeleteWordRightCommand",(function(){return W})),i.d(e,"DeleteWordStartLeft",(function(){return A})),i.d(e,"DeleteWordEndLeft",(function(){return F})),i.d(e,"DeleteWordLeft",(function(){return H})),i.d(e,"DeleteWordStartRight",(function(){return V})),i.d(e,"DeleteWordEndRight",(function(){return B})),i.d(e,"DeleteWordRight",(function(){return q}));var n=i("b2cc"),o=i("2c29"),r=i("2e5d"),s=i("d48d"),a=i("e6ff"),c=i("7061"),l=i("6a89"),u=i("8025"),d=i("c101"),h=i("4779"),p=i("4fc3"),g=i("fd49"),f=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])},t(e,i)};return function(e,i){function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),m=function(t){function e(e){var i=t.call(this,e)||this;return i._inSelectionMode=e.inSelectionMode,i._wordNavigationType=e.wordNavigationType,i}return f(e,t),e.prototype.runEditorCommand=function(t,e,i){var n=this;if(e.hasModel()){var o=Object(a["a"])(e.getOption(96)),s=e.getModel(),l=e.getSelections(),u=l.map((function(t){var e=new c["a"](t.positionLineNumber,t.positionColumn),i=n._move(o,s,e,n._wordNavigationType);return n._moveTo(t,i,n._inSelectionMode)}));if(e._getCursors().setStates("moveWordCommand",0,u.map((function(t){return r["d"].fromModelSelection(t)}))),1===u.length){var d=new c["a"](u[0].positionLineNumber,u[0].positionColumn);e.revealPosition(d,0)}}},e.prototype._moveTo=function(t,e,i){return i?new u["a"](t.selectionStartLineNumber,t.selectionStartColumn,e.lineNumber,e.column):new u["a"](e.lineNumber,e.column,e.lineNumber,e.column)},e}(n["c"]),b=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return f(e,t),e.prototype._move=function(t,e,i,n){return s["a"].moveWordLeft(t,e,i,n)},e}(m),_=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return f(e,t),e.prototype._move=function(t,e,i,n){return s["a"].moveWordRight(t,e,i,n)},e}(m),v=function(t){function e(){return t.call(this,{inSelectionMode:!1,wordNavigationType:0,id:"cursorWordStartLeft",precondition:void 0,kbOpts:{kbExpr:d["a"].textInputFocus,primary:2063,mac:{primary:527},weight:100}})||this}return f(e,t),e}(b),y=function(t){function e(){return t.call(this,{inSelectionMode:!1,wordNavigationType:2,id:"cursorWordEndLeft",precondition:void 0})||this}return f(e,t),e}(b),w=function(t){function e(){return t.call(this,{inSelectionMode:!1,wordNavigationType:1,id:"cursorWordLeft",precondition:void 0})||this}return f(e,t),e}(b),S=function(t){function e(){return t.call(this,{inSelectionMode:!0,wordNavigationType:0,id:"cursorWordStartLeftSelect",precondition:void 0,kbOpts:{kbExpr:d["a"].textInputFocus,primary:3087,mac:{primary:1551},weight:100}})||this}return f(e,t),e}(b),C=function(t){function e(){return t.call(this,{inSelectionMode:!0,wordNavigationType:2,id:"cursorWordEndLeftSelect",precondition:void 0})||this}return f(e,t),e}(b),O=function(t){function e(){return t.call(this,{inSelectionMode:!0,wordNavigationType:1,id:"cursorWordLeftSelect",precondition:void 0})||this}return f(e,t),e}(b),x=function(t){function e(){return t.call(this,{inSelectionMode:!1,wordNavigationType:3,id:"cursorWordAccessibilityLeft",precondition:void 0,kbOpts:{kbExpr:p["a"].and(d["a"].textInputFocus,h["a"]),win:{primary:2063},weight:101}})||this}return f(e,t),e.prototype._move=function(e,i,n,o){return t.prototype._move.call(this,Object(a["a"])(g["e"].wordSeparators.defaultValue),i,n,o)},e}(b),k=function(t){function e(){return t.call(this,{inSelectionMode:!0,wordNavigationType:3,id:"cursorWordAccessibilityLeftSelect",precondition:void 0,kbOpts:{kbExpr:p["a"].and(d["a"].textInputFocus,h["a"]),win:{primary:3087},weight:101}})||this}return f(e,t),e.prototype._move=function(e,i,n,o){return t.prototype._move.call(this,Object(a["a"])(g["e"].wordSeparators.defaultValue),i,n,o)},e}(b),D=function(t){function e(){return t.call(this,{inSelectionMode:!1,wordNavigationType:0,id:"cursorWordStartRight",precondition:void 0})||this}return f(e,t),e}(_),P=function(t){function e(){return t.call(this,{inSelectionMode:!1,wordNavigationType:2,id:"cursorWordEndRight",precondition:void 0,kbOpts:{kbExpr:d["a"].textInputFocus,primary:2065,mac:{primary:529},weight:100}})||this}return f(e,t),e}(_),T=function(t){function e(){return t.call(this,{inSelectionMode:!1,wordNavigationType:2,id:"cursorWordRight",precondition:void 0})||this}return f(e,t),e}(_),j=function(t){function e(){return t.call(this,{inSelectionMode:!0,wordNavigationType:0,id:"cursorWordStartRightSelect",precondition:void 0})||this}return f(e,t),e}(_),N=function(t){function e(){return t.call(this,{inSelectionMode:!0,wordNavigationType:2,id:"cursorWordEndRightSelect",precondition:void 0,kbOpts:{kbExpr:d["a"].textInputFocus,primary:3089,mac:{primary:1553},weight:100}})||this}return f(e,t),e}(_),E=function(t){function e(){return t.call(this,{inSelectionMode:!0,wordNavigationType:2,id:"cursorWordRightSelect",precondition:void 0})||this}return f(e,t),e}(_),L=function(t){function e(){return t.call(this,{inSelectionMode:!1,wordNavigationType:3,id:"cursorWordAccessibilityRight",precondition:void 0,kbOpts:{kbExpr:p["a"].and(d["a"].textInputFocus,h["a"]),win:{primary:2065},weight:101}})||this}return f(e,t),e.prototype._move=function(e,i,n,o){return t.prototype._move.call(this,Object(a["a"])(g["e"].wordSeparators.defaultValue),i,n,o)},e}(_),M=function(t){function e(){return t.call(this,{inSelectionMode:!0,wordNavigationType:3,id:"cursorWordAccessibilityRightSelect",precondition:void 0,kbOpts:{kbExpr:p["a"].and(d["a"].textInputFocus,h["a"]),win:{primary:3089},weight:101}})||this}return f(e,t),e.prototype._move=function(e,i,n,o){return t.prototype._move.call(this,Object(a["a"])(g["e"].wordSeparators.defaultValue),i,n,o)},e}(_),I=function(t){function e(e){var i=t.call(this,e)||this;return i._whitespaceHeuristics=e.whitespaceHeuristics,i._wordNavigationType=e.wordNavigationType,i}return f(e,t),e.prototype.runEditorCommand=function(t,e,i){var n=this;if(e.hasModel()){var r=Object(a["a"])(e.getOption(96)),s=e.getModel(),c=e.getSelections(),l=c.map((function(t){var e=n._delete(r,s,t,n._whitespaceHeuristics,n._wordNavigationType);return new o["a"](e,"")}));e.pushUndoStop(),e.executeCommands(this.id,l),e.pushUndoStop()}},e}(n["c"]),R=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return f(e,t),e.prototype._delete=function(t,e,i,n,o){var r=s["a"].deleteWordLeft(t,e,i,n,o);return r||new l["a"](1,1,1,1)},e}(I),W=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return f(e,t),e.prototype._delete=function(t,e,i,n,o){var r=s["a"].deleteWordRight(t,e,i,n,o);if(r)return r;var a=e.getLineCount(),c=e.getLineMaxColumn(a);return new l["a"](a,c,a,c)},e}(I),A=function(t){function e(){return t.call(this,{whitespaceHeuristics:!1,wordNavigationType:0,id:"deleteWordStartLeft",precondition:d["a"].writable})||this}return f(e,t),e}(R),F=function(t){function e(){return t.call(this,{whitespaceHeuristics:!1,wordNavigationType:2,id:"deleteWordEndLeft",precondition:d["a"].writable})||this}return f(e,t),e}(R),H=function(t){function e(){return t.call(this,{whitespaceHeuristics:!0,wordNavigationType:0,id:"deleteWordLeft",precondition:d["a"].writable,kbOpts:{kbExpr:d["a"].textInputFocus,primary:2049,mac:{primary:513},weight:100}})||this}return f(e,t),e}(R),V=function(t){function e(){return t.call(this,{whitespaceHeuristics:!1,wordNavigationType:0,id:"deleteWordStartRight",precondition:d["a"].writable})||this}return f(e,t),e}(W),B=function(t){function e(){return t.call(this,{whitespaceHeuristics:!1,wordNavigationType:2,id:"deleteWordEndRight",precondition:d["a"].writable})||this}return f(e,t),e}(W),q=function(t){function e(){return t.call(this,{whitespaceHeuristics:!0,wordNavigationType:2,id:"deleteWordRight",precondition:d["a"].writable,kbOpts:{kbExpr:d["a"].textInputFocus,primary:2068,mac:{primary:532},weight:100}})||this}return f(e,t),e}(W);Object(n["g"])(new v),Object(n["g"])(new y),Object(n["g"])(new w),Object(n["g"])(new S),Object(n["g"])(new C),Object(n["g"])(new O),Object(n["g"])(new D),Object(n["g"])(new P),Object(n["g"])(new T),Object(n["g"])(new j),Object(n["g"])(new N),Object(n["g"])(new E),Object(n["g"])(new x),Object(n["g"])(new k),Object(n["g"])(new L),Object(n["g"])(new M),Object(n["g"])(new A),Object(n["g"])(new F),Object(n["g"])(new H),Object(n["g"])(new V),Object(n["g"])(new B),Object(n["g"])(new q)},b574:function(t,e,i){"use strict";i.r(e),i.d(e,"SnippetController2",(function(){return q}));var n=i("a666"),o=i("3742"),r=i("b2cc"),s=i("6a89"),a=i("8025"),c=i("c101"),l=i("4153"),u=i("4fc3"),d=i("d3d7"),h=i("e8e3"),p=(i("7457"),i("d3f4")),g=i("b57f"),f=i("f577"),m=i("1165"),b=i("0a0f"),_=i("b800"),v=i("dff7"),y=i("32b8"),w=i("82c9"),S=i("70cb"),C=i("20e3"),O=i("debc"),x=function(){function t(t){this._delegates=t}return t.prototype.resolve=function(t){for(var e=0,i=this._delegates;e<i.length;e++){var n=i[e],o=n.resolve(t);if(void 0!==o)return o}},t}(),k=function(){function t(t,e){this._model=t,this._selection=e}return t.prototype.resolve=function(t){var e=t.name;if("SELECTION"===e||"TM_SELECTED_TEXT"===e){var i=this._model.getValueInRange(this._selection)||void 0;if(i&&this._selection.startLineNumber!==this._selection.endLineNumber&&t.snippet){var n=this._model.getLineContent(this._selection.startLineNumber),r=Object(o["t"])(n,0,this._selection.startColumn-1),s=r;t.snippet.walk((function(e){return e!==t&&(e instanceof _["d"]&&(s=Object(o["t"])(e.value.split(/\r\n|\r|\n/).pop())),!0)}));var a=Object(o["c"])(s,r);i=i.replace(/(\r\n|\r|\n)(.*)/g,(function(t,e,i){return""+e+s.substr(a)+i}))}return i}if("TM_CURRENT_LINE"===e)return this._model.getLineContent(this._selection.positionLineNumber);if("TM_CURRENT_WORD"===e){var c=this._model.getWordAtPosition({lineNumber:this._selection.positionLineNumber,column:this._selection.positionColumn});return c&&c.word||void 0}return"TM_LINE_INDEX"===e?String(this._selection.positionLineNumber-1):"TM_LINE_NUMBER"===e?String(this._selection.positionLineNumber):void 0},t}(),D=function(){function t(t,e){this._labelService=t,this._model=e}return t.prototype.resolve=function(t){var e=t.name;if("TM_FILENAME"===e)return y["basename"](this._model.uri.fsPath);if("TM_FILENAME_BASE"===e){var i=y["basename"](this._model.uri.fsPath),n=i.lastIndexOf(".");return n<=0?i:i.slice(0,n)}return"TM_DIRECTORY"===e&&this._labelService?"."===y["dirname"](this._model.uri.fsPath)?"":this._labelService.getUriLabel(Object(w["d"])(this._model.uri)):"TM_FILEPATH"===e&&this._labelService?this._labelService.getUriLabel(this._model.uri):void 0},t}(),P=function(){function t(t,e,i,n){this._readClipboardText=t,this._selectionIdx=e,this._selectionCount=i,this._spread=n}return t.prototype.resolve=function(t){if("CLIPBOARD"===t.name){var e=this._readClipboardText();if(e){if(this._spread){var i=e.split(/\r\n|\n|\r/).filter((function(t){return!Object(o["x"])(t)}));if(i.length===this._selectionCount)return i[this._selectionIdx]}return e}}},t}(),T=function(){function t(t){this._model=t}return t.prototype.resolve=function(t){var e=t.name,i=this._model.getLanguageIdentifier(),n=S["a"].getComments(i.id);if(n)return"LINE_COMMENT"===e?n.lineCommentToken||void 0:"BLOCK_COMMENT_START"===e?n.blockCommentStartToken||void 0:"BLOCK_COMMENT_END"===e&&n.blockCommentEndToken||void 0},t}(),j=function(){function t(){}return t.prototype.resolve=function(e){var i=e.name;return"CURRENT_YEAR"===i?String((new Date).getFullYear()):"CURRENT_YEAR_SHORT"===i?String((new Date).getFullYear()).slice(-2):"CURRENT_MONTH"===i?Object(o["F"])((new Date).getMonth().valueOf()+1,2):"CURRENT_DATE"===i?Object(o["F"])((new Date).getDate().valueOf(),2):"CURRENT_HOUR"===i?Object(o["F"])((new Date).getHours().valueOf(),2):"CURRENT_MINUTE"===i?Object(o["F"])((new Date).getMinutes().valueOf(),2):"CURRENT_SECOND"===i?Object(o["F"])((new Date).getSeconds().valueOf(),2):"CURRENT_DAY_NAME"===i?t.dayNames[(new Date).getDay()]:"CURRENT_DAY_NAME_SHORT"===i?t.dayNamesShort[(new Date).getDay()]:"CURRENT_MONTH_NAME"===i?t.monthNames[(new Date).getMonth()]:"CURRENT_MONTH_NAME_SHORT"===i?t.monthNamesShort[(new Date).getMonth()]:"CURRENT_SECONDS_UNIX"===i?String(Math.floor(Date.now()/1e3)):void 0},t.dayNames=[v["a"]("Sunday","Sunday"),v["a"]("Monday","Monday"),v["a"]("Tuesday","Tuesday"),v["a"]("Wednesday","Wednesday"),v["a"]("Thursday","Thursday"),v["a"]("Friday","Friday"),v["a"]("Saturday","Saturday")],t.dayNamesShort=[v["a"]("SundayShort","Sun"),v["a"]("MondayShort","Mon"),v["a"]("TuesdayShort","Tue"),v["a"]("WednesdayShort","Wed"),v["a"]("ThursdayShort","Thu"),v["a"]("FridayShort","Fri"),v["a"]("SaturdayShort","Sat")],t.monthNames=[v["a"]("January","January"),v["a"]("February","February"),v["a"]("March","March"),v["a"]("April","April"),v["a"]("May","May"),v["a"]("June","June"),v["a"]("July","July"),v["a"]("August","August"),v["a"]("September","September"),v["a"]("October","October"),v["a"]("November","November"),v["a"]("December","December")],t.monthNamesShort=[v["a"]("JanuaryShort","Jan"),v["a"]("FebruaryShort","Feb"),v["a"]("MarchShort","Mar"),v["a"]("AprilShort","Apr"),v["a"]("MayShort","May"),v["a"]("JuneShort","Jun"),v["a"]("JulyShort","Jul"),v["a"]("AugustShort","Aug"),v["a"]("SeptemberShort","Sep"),v["a"]("OctoberShort","Oct"),v["a"]("NovemberShort","Nov"),v["a"]("DecemberShort","Dec")],t}(),N=function(){function t(t){this._workspaceService=t}return t.prototype.resolve=function(t){if(this._workspaceService){var e=Object(C["c"])(this._workspaceService.getWorkspace());if(e)return"WORKSPACE_NAME"===t.name?this._resolveWorkspaceName(e):"WORKSPACE_FOLDER"===t.name?this._resoveWorkspacePath(e):void 0}},t.prototype._resolveWorkspaceName=function(t){if(Object(C["b"])(t))return y["basename"](t.path);var e=y["basename"](t.configPath.path);return Object(o["m"])(e,C["a"])&&(e=e.substr(0,e.length-C["a"].length-1)),e},t.prototype._resoveWorkspacePath=function(t){if(Object(C["b"])(t))return Object(O["c"])(t.fsPath);var e=y["basename"](t.configPath.path),i=t.configPath.fsPath;return Object(o["m"])(i,e)&&(i=i.substr(0,i.length-e.length-1)),i?Object(O["c"])(i):"/"},t}(),E=function(){function t(){}return t.prototype.resolve=function(t){var e=t.name;return"RANDOM"===e?Math.random().toString().slice(-6):"RANDOM_HEX"===e?Math.random().toString(16).slice(-6):void 0},t}(),L=i("b7d0"),M=i("303e"),I=i("47cb");Object(L["e"])((function(t,e){function i(e){var i=t.getColor(e);return i?i.toString():"transparent"}e.addRule(".monaco-editor .snippet-placeholder { background-color: "+i(M["bc"])+"; outline-color: "+i(M["cc"])+"; }"),e.addRule(".monaco-editor .finish-snippet-placeholder { background-color: "+i(M["Zb"])+"; outline-color: "+i(M["ac"])+"; }")}));var R=function(){function t(t,e,i){this._nestingLevel=1,this._editor=t,this._snippet=e,this._offset=i,this._placeholderGroups=Object(h["o"])(e.placeholders,_["b"].compareByIndex),this._placeholderGroupsIdx=-1}return t.prototype.dispose=function(){if(this._placeholderDecorations){var t=[];this._placeholderDecorations.forEach((function(e){return t.push(e)})),this._editor.deltaDecorations(t,[])}this._placeholderGroups.length=0},t.prototype._initDecorations=function(){var e=this;if(!this._placeholderDecorations){this._placeholderDecorations=new Map;var i=this._editor.getModel();this._editor.changeDecorations((function(n){for(var o=0,r=e._snippet.placeholders;o<r.length;o++){var a=r[o],c=e._snippet.offset(a),l=e._snippet.fullLen(a),u=s["a"].fromPositions(i.getPositionAt(e._offset+c),i.getPositionAt(e._offset+c+l)),d=a.isFinalTabstop?t._decor.inactiveFinal:t._decor.inactive,h=n.addDecoration(u,d);e._placeholderDecorations.set(a,h)}}))}},t.prototype.move=function(e){var i=this;if(!this._editor.hasModel())return[];if(this._initDecorations(),this._placeholderGroupsIdx>=0){for(var n=[],o=0,r=this._placeholderGroups[this._placeholderGroupsIdx];o<r.length;o++){var s=r[o];if(s.transform){var c=this._placeholderDecorations.get(s),l=this._editor.getModel().getDecorationRange(c),u=this._editor.getModel().getValueInRange(l);n.push(p["a"].replaceMove(l,s.transform.resolve(u)))}}n.length>0&&this._editor.executeEdits("snippet.placeholderTransform",n)}var d=!1;!0===e&&this._placeholderGroupsIdx<this._placeholderGroups.length-1?(this._placeholderGroupsIdx+=1,d=!0):!1===e&&this._placeholderGroupsIdx>0&&(this._placeholderGroupsIdx-=1,d=!0);var h=this._editor.getModel().changeDecorations((function(e){for(var n=new Set,o=[],r=0,s=i._placeholderGroups[i._placeholderGroupsIdx];r<s.length;r++){var c=s[r],l=i._placeholderDecorations.get(c),u=i._editor.getModel().getDecorationRange(l);o.push(new a["a"](u.startLineNumber,u.startColumn,u.endLineNumber,u.endColumn)),d=d&&i._hasPlaceholderBeenCollapsed(c),e.changeDecorationOptions(l,c.isFinalTabstop?t._decor.activeFinal:t._decor.active),n.add(c);for(var h=0,p=i._snippet.enclosingPlaceholders(c);h<p.length;h++){var g=p[h],f=i._placeholderDecorations.get(g);e.changeDecorationOptions(f,g.isFinalTabstop?t._decor.activeFinal:t._decor.active),n.add(g)}}return i._placeholderDecorations.forEach((function(i,o){n.has(o)||e.changeDecorationOptions(i,o.isFinalTabstop?t._decor.inactiveFinal:t._decor.inactive)})),o}));return d?this.move(e):h},t.prototype._hasPlaceholderBeenCollapsed=function(t){var e=t;while(e){if(e instanceof _["b"]){var i=this._placeholderDecorations.get(e),n=this._editor.getModel().getDecorationRange(i);if(n.isEmpty()&&e.toString().length>0)return!0}e=e.parent}return!1},Object.defineProperty(t.prototype,"isAtFirstPlaceholder",{get:function(){return this._placeholderGroupsIdx<=0||0===this._placeholderGroups.length},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"isAtLastPlaceholder",{get:function(){return this._placeholderGroupsIdx===this._placeholderGroups.length-1},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"hasPlaceholder",{get:function(){return this._snippet.placeholders.length>0},enumerable:!0,configurable:!0}),t.prototype.computePossibleSelections=function(){for(var t=new Map,e=0,i=this._placeholderGroups;e<i.length;e++)for(var n=i[e],o=void 0,r=0,s=n;r<s.length;r++){var a=s[r];if(a.isFinalTabstop)break;o||(o=[],t.set(a.index,o));var c=this._placeholderDecorations.get(a),l=this._editor.getModel().getDecorationRange(c);if(!l){t.delete(a.index);break}o.push(l)}return t},Object.defineProperty(t.prototype,"choice",{get:function(){return this._placeholderGroups[this._placeholderGroupsIdx][0].choice},enumerable:!0,configurable:!0}),t.prototype.merge=function(e){var i=this,n=this._editor.getModel();this._nestingLevel*=10,this._editor.changeDecorations((function(o){for(var r=0,a=i._placeholderGroups[i._placeholderGroupsIdx];r<a.length;r++){var c=a[r],l=e.shift();console.assert(!l._placeholderDecorations);for(var u=l._snippet.placeholderInfo.last.index,d=0,p=l._snippet.placeholderInfo.all;d<p.length;d++){var g=p[d];g.isFinalTabstop?g.index=c.index+(u+1)/i._nestingLevel:g.index=c.index+g.index/i._nestingLevel}i._snippet.replace(c,l._snippet.children);var f=i._placeholderDecorations.get(c);o.removeDecoration(f),i._placeholderDecorations.delete(c);for(var m=0,b=l._snippet.placeholders;m<b.length;m++){var v=b[m],y=l._snippet.offset(v),w=l._snippet.fullLen(v),S=s["a"].fromPositions(n.getPositionAt(l._offset+y),n.getPositionAt(l._offset+y+w)),C=o.addDecoration(S,t._decor.inactive);i._placeholderDecorations.set(v,C)}}i._placeholderGroups=Object(h["o"])(i._snippet.placeholders,_["b"].compareByIndex)}))},t._decor={active:g["a"].register({stickiness:0,className:"snippet-placeholder"}),inactive:g["a"].register({stickiness:1,className:"snippet-placeholder"}),activeFinal:g["a"].register({stickiness:1,className:"finish-snippet-placeholder"}),inactiveFinal:g["a"].register({stickiness:1,className:"finish-snippet-placeholder"})},t}(),W={overwriteBefore:0,overwriteAfter:0,adjustWhitespace:!0,clipboardText:void 0},A=function(){function t(t,e,i){void 0===i&&(i=W),this._templateMerges=[],this._snippets=[],this._editor=t,this._template=e,this._options=i}return t.adjustWhitespace=function(t,e,i,n,r){var s=t.getLineContent(e.lineNumber),a=Object(o["t"])(s,0,e.column-1);i.walk((function(e){if(e instanceof _["d"]&&!(e.parent instanceof _["a"])){var i=e.value.split(/\r\n|\r|\n/);if(n)for(var s=1;s<i.length;s++){var c=Object(o["t"])(i[s]);i[s]=t.normalizeIndentation(a+c)+i[s].substr(c.length)}if(r){var l=i.join(t.getEOL());l!==e.value&&e.parent.replace(e,[new _["d"](l)])}}return!0}))},t.adjustSelection=function(t,e,i,n){if(0!==i||0!==n){var o=e.positionLineNumber,r=e.positionColumn,s=r-i,c=r+n,l=t.validateRange({startLineNumber:o,startColumn:s,endLineNumber:o,endColumn:c});e=a["a"].createWithDirection(l.startLineNumber,l.startColumn,l.endLineNumber,l.endColumn,e.getDirection())}return e},t.createEditsAndSnippets=function(e,i,n,o,r,a,c){var l=[],u=[];if(!e.hasModel())return{edits:l,snippets:u};for(var d=e.getModel(),h=e.invokeWithinContext((function(t){return t.get(m["a"],b["d"])})),g=e.invokeWithinContext((function(t){return new D(t.get(I["a"],b["d"]),d)})),v=e.invokeWithinContext((function(t){return t.get(f["a"],b["d"])})),y=function(){return c||v&&v.readTextSync()},w=0,S=d.getValueInRange(t.adjustSelection(d,e.getSelection(),n,0)),C=d.getValueInRange(t.adjustSelection(d,e.getSelection(),0,o)),O=d.getLineFirstNonWhitespaceColumn(e.getSelection().positionLineNumber),L=e.getSelections().map((function(t,e){return{selection:t,idx:e}})).sort((function(t,e){return s["a"].compareRangesUsingStarts(t.selection,e.selection)})),M=0,W=L;M<W.length;M++){var A=W[M],F=A.selection,H=A.idx,V=t.adjustSelection(d,F,n,0),B=t.adjustSelection(d,F,0,o);S!==d.getValueInRange(V)&&(V=F),C!==d.getValueInRange(B)&&(B=F);var q=F.setStartPosition(V.startLineNumber,V.startColumn).setEndPosition(B.endLineNumber,B.endColumn),U=(new _["c"]).parse(i,!0,r),K=q.getStartPosition();t.adjustWhitespace(d,K,U,a||H>0&&O!==d.getLineFirstNonWhitespaceColumn(F.positionLineNumber),!0),U.resolveVariables(new x([g,new P(y,H,L.length,"spread"===e.getOption(60)),new k(d,F),new T(d),new j,new N(h),new E]));var z=d.getOffsetAt(K)+w;w+=U.toString().length-d.getValueLengthInRange(q),l[H]=p["a"].replace(q,U.toString()),u[H]=new R(e,U,z)}return{edits:l,snippets:u}},t.prototype.dispose=function(){Object(n["f"])(this._snippets)},t.prototype._logInfo=function(){return'template="'+this._template+'", merged_templates="'+this._templateMerges.join(" -> ")+'"'},t.prototype.insert=function(){var e=this;if(this._editor.hasModel()){var i=t.createEditsAndSnippets(this._editor,this._template,this._options.overwriteBefore,this._options.overwriteAfter,!1,this._options.adjustWhitespace,this._options.clipboardText),n=i.edits,o=i.snippets;this._snippets=o,this._editor.executeEdits("snippet",n,(function(t){return e._snippets[0].hasPlaceholder?e._move(!0):t.map((function(t){return a["a"].fromPositions(t.range.getEndPosition())}))})),this._editor.revealRange(this._editor.getSelections()[0])}},t.prototype.merge=function(e,i){var n=this;if(void 0===i&&(i=W),this._editor.hasModel()){this._templateMerges.push([this._snippets[0]._nestingLevel,this._snippets[0]._placeholderGroupsIdx,e]);var o=t.createEditsAndSnippets(this._editor,e,i.overwriteBefore,i.overwriteAfter,!0,i.adjustWhitespace,i.clipboardText),r=o.edits,s=o.snippets;this._editor.executeEdits("snippet",r,(function(t){for(var e=0,i=n._snippets;e<i.length;e++){var o=i[e];o.merge(s)}return console.assert(0===s.length),n._snippets[0].hasPlaceholder?n._move(void 0):t.map((function(t){return a["a"].fromPositions(t.range.getEndPosition())}))}))}},t.prototype.next=function(){var t=this._move(!0);this._editor.setSelections(t),this._editor.revealPositionInCenterIfOutsideViewport(t[0].getPosition())},t.prototype.prev=function(){var t=this._move(!1);this._editor.setSelections(t),this._editor.revealPositionInCenterIfOutsideViewport(t[0].getPosition())},t.prototype._move=function(t){for(var e=[],i=0,n=this._snippets;i<n.length;i++){var o=n[i],r=o.move(t);e.push.apply(e,r)}return e},Object.defineProperty(t.prototype,"isAtFirstPlaceholder",{get:function(){return this._snippets[0].isAtFirstPlaceholder},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"isAtLastPlaceholder",{get:function(){return this._snippets[0].isAtLastPlaceholder},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"hasPlaceholder",{get:function(){return this._snippets[0].hasPlaceholder},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"choice",{get:function(){return this._snippets[0].choice},enumerable:!0,configurable:!0}),t.prototype.isSelectionWithinPlaceholders=function(){if(!this.hasPlaceholder)return!1;var t=this._editor.getSelections();if(t.length<this._snippets.length)return!1;for(var e=new Map,i=function(i){var n=i.computePossibleSelections();if(0===e.size&&n.forEach((function(i,n){i.sort(s["a"].compareRangesUsingStarts);for(var o=0,r=t;o<r.length;o++){var a=r[o];if(i[0].containsRange(a)){e.set(n,[]);break}}})),0===e.size)return{value:!1};e.forEach((function(t,e){t.push.apply(t,n.get(e))}))},n=0,o=this._snippets;n<o.length;n++){var r=o[n],a=i(r);if("object"===typeof a)return a.value}return t.sort(s["a"].compareRangesUsingStarts),e.forEach((function(i,n){if(i.length===t.length){i.sort(s["a"].compareRangesUsingStarts);for(var o=0;o<i.length;o++)if(!i[o].containsRange(t[o]))return void e.delete(n)}else e.delete(n)})),e.size>0},t}(),F=function(){return F=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++)for(var o in e=arguments[i],e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},F.apply(this,arguments)},H=function(t,e,i,n){var o,r=arguments.length,s=r<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)s=Reflect.decorate(t,e,i,n);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(r<3?o(s):r>3?o(e,i,s):o(e,i))||s);return r>3&&s&&Object.defineProperty(e,i,s),s},V=function(t,e){return function(i,n){e(i,n,t)}},B={overwriteBefore:0,overwriteAfter:0,undoStopBefore:!0,undoStopAfter:!0,adjustWhitespace:!0,clipboardText:void 0},q=function(){function t(e,i,o){this._editor=e,this._logService=i,this._snippetListener=new n["b"],this._modelVersionId=-1,this._inSnippet=t.InSnippetMode.bindTo(o),this._hasNextTabstop=t.HasNextTabstop.bindTo(o),this._hasPrevTabstop=t.HasPrevTabstop.bindTo(o)}return t.get=function(e){return e.getContribution(t.ID)},t.prototype.dispose=function(){this._inSnippet.reset(),this._hasPrevTabstop.reset(),this._hasNextTabstop.reset(),Object(n["f"])(this._session),this._snippetListener.dispose()},t.prototype.insert=function(t,e){try{this._doInsert(t,"undefined"===typeof e?B:F(F({},B),e))}catch(i){this.cancel(),this._logService.error(i),this._logService.error("snippet_error"),this._logService.error("insert_template=",t),this._logService.error("existing_template=",this._session?this._session._logInfo():"<no_session>")}},t.prototype._doInsert=function(t,e){var i=this;this._editor.hasModel()&&(this._snippetListener.clear(),e.undoStopBefore&&this._editor.getModel().pushStackElement(),this._session?this._session.merge(t,e):(this._modelVersionId=this._editor.getModel().getAlternativeVersionId(),this._session=new A(this._editor,t,e),this._session.insert()),e.undoStopAfter&&this._editor.getModel().pushStackElement(),this._updateState(),this._snippetListener.add(this._editor.onDidChangeModelContent((function(t){return t.isFlush&&i.cancel()}))),this._snippetListener.add(this._editor.onDidChangeModel((function(){return i.cancel()}))),this._snippetListener.add(this._editor.onDidChangeCursorSelection((function(){return i._updateState()}))))},t.prototype._updateState=function(){if(this._session&&this._editor.hasModel()){if(this._modelVersionId===this._editor.getModel().getAlternativeVersionId())return this.cancel();if(!this._session.hasPlaceholder)return this.cancel();if(this._session.isAtLastPlaceholder||!this._session.isSelectionWithinPlaceholders())return this.cancel();this._inSnippet.set(!0),this._hasPrevTabstop.set(!this._session.isAtFirstPlaceholder),this._hasNextTabstop.set(!this._session.isAtLastPlaceholder),this._handleChoice()}},t.prototype._handleChoice=function(){var t=this;if(this._session&&this._editor.hasModel()){var e=this._session.choice;if(e){if(this._currentChoice!==e){this._currentChoice=e,this._editor.setSelections(this._editor.getSelections().map((function(t){return a["a"].fromPositions(t.getStartPosition())})));var i=e.options[0];Object(l["f"])(this._editor,e.options.map((function(e,n){return{kind:13,label:e.value,insertText:e.value,sortText:Object(o["J"])("a",n+1),range:s["a"].fromPositions(t._editor.getPosition(),t._editor.getPosition().delta(0,i.value.length))}})))}}else this._currentChoice=void 0}else this._currentChoice=void 0},t.prototype.finish=function(){while(this._inSnippet.get())this.next()},t.prototype.cancel=function(t){void 0===t&&(t=!1),this._inSnippet.reset(),this._hasPrevTabstop.reset(),this._hasNextTabstop.reset(),this._snippetListener.clear(),Object(n["f"])(this._session),this._session=void 0,this._modelVersionId=-1,t&&this._editor.setSelections([this._editor.getSelection()])},t.prototype.prev=function(){this._session&&this._session.prev(),this._updateState()},t.prototype.next=function(){this._session&&this._session.next(),this._updateState()},t.prototype.isInSnippet=function(){return Boolean(this._inSnippet.get())},t.ID="snippetController2",t.InSnippetMode=new u["d"]("inSnippetMode",!1),t.HasNextTabstop=new u["d"]("hasNextTabstop",!1),t.HasPrevTabstop=new u["d"]("hasPrevTabstop",!1),t=H([V(1,d["a"]),V(2,u["c"])],t),t}();Object(r["h"])(q.ID,q);var U=r["c"].bindToContribution(q.get);Object(r["g"])(new U({id:"jumpToNextSnippetPlaceholder",precondition:u["a"].and(q.InSnippetMode,q.HasNextTabstop),handler:function(t){return t.next()},kbOpts:{weight:130,kbExpr:c["a"].editorTextFocus,primary:2}})),Object(r["g"])(new U({id:"jumpToPrevSnippetPlaceholder",precondition:u["a"].and(q.InSnippetMode,q.HasPrevTabstop),handler:function(t){return t.prev()},kbOpts:{weight:130,kbExpr:c["a"].editorTextFocus,primary:1026}})),Object(r["g"])(new U({id:"leaveSnippet",precondition:q.InSnippetMode,handler:function(t){return t.cancel(!0)},kbOpts:{weight:130,kbExpr:c["a"].editorTextFocus,primary:9,secondary:[1033]}})),Object(r["g"])(new U({id:"acceptSnippet",precondition:q.InSnippetMode,handler:function(t){return t.finish()}}))},b800:function(t,e,i){"use strict";i.d(e,"d",(function(){return c})),i.d(e,"b",(function(){return u})),i.d(e,"a",(function(){return d})),i.d(e,"c",(function(){return b}));var n,o=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])},t(e,i)};return function(e,i){function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),r=function(){for(var t=0,e=0,i=arguments.length;e<i;e++)t+=arguments[e].length;var n=Array(t),o=0;for(e=0;e<i;e++)for(var r=arguments[e],s=0,a=r.length;s<a;s++,o++)n[o]=r[s];return n},s=function(){function t(){this.value="",this.pos=0}return t.isDigitCharacter=function(t){return t>=48&&t<=57},t.isVariableCharacter=function(t){return 95===t||t>=97&&t<=122||t>=65&&t<=90},t.prototype.text=function(t){this.value=t,this.pos=0},t.prototype.tokenText=function(t){return this.value.substr(t.pos,t.len)},t.prototype.next=function(){if(this.pos>=this.value.length)return{type:14,pos:this.pos,len:0};var e,i=this.pos,n=0,o=this.value.charCodeAt(i);if(e=t._table[o],"number"===typeof e)return this.pos+=1,{type:e,pos:i,len:1};if(t.isDigitCharacter(o)){e=8;do{n+=1,o=this.value.charCodeAt(i+n)}while(t.isDigitCharacter(o));return this.pos+=n,{type:e,pos:i,len:n}}if(t.isVariableCharacter(o)){e=9;do{o=this.value.charCodeAt(i+ ++n)}while(t.isVariableCharacter(o)||t.isDigitCharacter(o));return this.pos+=n,{type:e,pos:i,len:n}}e=10;do{n+=1,o=this.value.charCodeAt(i+n)}while(!isNaN(o)&&"undefined"===typeof t._table[o]&&!t.isDigitCharacter(o)&&!t.isVariableCharacter(o));return this.pos+=n,{type:e,pos:i,len:n}},t._table=(n={},n[36]=0,n[58]=1,n[44]=2,n[123]=3,n[125]=4,n[92]=5,n[47]=6,n[124]=7,n[43]=11,n[45]=12,n[63]=13,n),t}(),a=function(){function t(){this._children=[]}return t.prototype.appendChild=function(t){return t instanceof c&&this._children[this._children.length-1]instanceof c?this._children[this._children.length-1].value+=t.value:(t.parent=this,this._children.push(t)),this},t.prototype.replace=function(t,e){var i=t.parent,n=i.children.indexOf(t),o=i.children.slice(0);o.splice.apply(o,r([n,1],e)),i._children=o,function t(e,i){for(var n=0,o=e;n<o.length;n++){var r=o[n];r.parent=i,t(r.children,r)}}(e,i)},Object.defineProperty(t.prototype,"children",{get:function(){return this._children},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"snippet",{get:function(){var t=this;while(1){if(!t)return;if(t instanceof m)return t;t=t.parent}},enumerable:!0,configurable:!0}),t.prototype.toString=function(){return this.children.reduce((function(t,e){return t+e.toString()}),"")},t.prototype.len=function(){return 0},t}(),c=function(t){function e(e){var i=t.call(this)||this;return i.value=e,i}return o(e,t),e.prototype.toString=function(){return this.value},e.prototype.len=function(){return this.value.length},e.prototype.clone=function(){return new e(this.value)},e}(a),l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),e}(a),u=function(t){function e(e){var i=t.call(this)||this;return i.index=e,i}return o(e,t),e.compareByIndex=function(t,e){return t.index===e.index?0:t.isFinalTabstop?1:e.isFinalTabstop||t.index<e.index?-1:t.index>e.index?1:0},Object.defineProperty(e.prototype,"isFinalTabstop",{get:function(){return 0===this.index},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"choice",{get:function(){return 1===this._children.length&&this._children[0]instanceof d?this._children[0]:void 0},enumerable:!0,configurable:!0}),e.prototype.clone=function(){var t=new e(this.index);return this.transform&&(t.transform=this.transform.clone()),t._children=this.children.map((function(t){return t.clone()})),t},e}(l),d=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.options=[],e}return o(e,t),e.prototype.appendChild=function(t){return t instanceof c&&(t.parent=this,this.options.push(t)),this},e.prototype.toString=function(){return this.options[0].value},e.prototype.len=function(){return this.options[0].len()},e.prototype.clone=function(){var t=new e;return this.options.forEach(t.appendChild,t),t},e}(a),h=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.regexp=new RegExp(""),e}return o(e,t),e.prototype.resolve=function(t){var e=this,i=!1,n=t.replace(this.regexp,(function(){return i=!0,e._replace(Array.prototype.slice.call(arguments,0,-2))}));return!i&&this._children.some((function(t){return t instanceof p&&Boolean(t.elseValue)}))&&(n=this._replace([])),n},e.prototype._replace=function(t){for(var e="",i=0,n=this._children;i<n.length;i++){var o=n[i];if(o instanceof p){var r=t[o.index]||"";r=o.resolve(r),e+=r}else e+=o.toString()}return e},e.prototype.toString=function(){return""},e.prototype.clone=function(){var t=new e;return t.regexp=new RegExp(this.regexp.source,(this.regexp.ignoreCase?"i":"")+(this.regexp.global?"g":"")),t._children=this.children.map((function(t){return t.clone()})),t},e}(a),p=function(t){function e(e,i,n,o){var r=t.call(this)||this;return r.index=e,r.shorthandName=i,r.ifValue=n,r.elseValue=o,r}return o(e,t),e.prototype.resolve=function(t){return"upcase"===this.shorthandName?t?t.toLocaleUpperCase():"":"downcase"===this.shorthandName?t?t.toLocaleLowerCase():"":"capitalize"===this.shorthandName?t?t[0].toLocaleUpperCase()+t.substr(1):"":"pascalcase"===this.shorthandName?t?this._toPascalCase(t):"":Boolean(t)&&"string"===typeof this.ifValue?this.ifValue:Boolean(t)||"string"!==typeof this.elseValue?t||"":this.elseValue},e.prototype._toPascalCase=function(t){var e=t.match(/[a-z]+/gi);return e?e.map((function(t){return t.charAt(0).toUpperCase()+t.substr(1).toLowerCase()})).join(""):t},e.prototype.clone=function(){var t=new e(this.index,this.shorthandName,this.ifValue,this.elseValue);return t},e}(a),g=function(t){function e(e){var i=t.call(this)||this;return i.name=e,i}return o(e,t),e.prototype.resolve=function(t){var e=t.resolve(this);return this.transform&&(e=this.transform.resolve(e||"")),void 0!==e&&(this._children=[new c(e)],!0)},e.prototype.clone=function(){var t=new e(this.name);return this.transform&&(t.transform=this.transform.clone()),t._children=this.children.map((function(t){return t.clone()})),t},e}(l);function f(t,e){var i=r(t);while(i.length>0){var n=i.shift(),o=e(n);if(!o)break;i.unshift.apply(i,n.children)}}var m=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return o(e,t),Object.defineProperty(e.prototype,"placeholderInfo",{get:function(){if(!this._placeholders){var t,e=[];this.walk((function(i){return i instanceof u&&(e.push(i),t=!t||t.index<i.index?i:t),!0})),this._placeholders={all:e,last:t}}return this._placeholders},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"placeholders",{get:function(){var t=this.placeholderInfo.all;return t},enumerable:!0,configurable:!0}),e.prototype.offset=function(t){var e=0,i=!1;return this.walk((function(n){return n===t?(i=!0,!1):(e+=n.len(),!0)})),i?e:-1},e.prototype.fullLen=function(t){var e=0;return f([t],(function(t){return e+=t.len(),!0})),e},e.prototype.enclosingPlaceholders=function(t){var e=[],i=t.parent;while(i)i instanceof u&&e.push(i),i=i.parent;return e},e.prototype.resolveVariables=function(t){var e=this;return this.walk((function(i){return i instanceof g&&i.resolve(t)&&(e._placeholders=void 0),!0})),this},e.prototype.appendChild=function(e){return this._placeholders=void 0,t.prototype.appendChild.call(this,e)},e.prototype.replace=function(e,i){return this._placeholders=void 0,t.prototype.replace.call(this,e,i)},e.prototype.clone=function(){var t=new e;return this._children=this.children.map((function(t){return t.clone()})),t},e.prototype.walk=function(t){f(this.children,t)},e}(a),b=function(){function t(){this._scanner=new s,this._token={type:14,pos:0,len:0}}return t.escape=function(t){return t.replace(/\$|}|\\/g,"\\$&")},t.prototype.parse=function(t,e,i){this._scanner.text(t),this._token=this._scanner.next();var n=new m;while(this._parse(n));var o=new Map,r=[],s=0;n.walk((function(t){return t instanceof u&&(s+=1,t.isFinalTabstop?o.set(0,void 0):!o.has(t.index)&&t.children.length>0?o.set(t.index,t.children):r.push(t)),!0}));for(var a=0,c=r;a<c.length;a++){var l=c[a],d=o.get(l.index);if(d){var h=new u(l.index);h.transform=l.transform;for(var p=0,g=d;p<g.length;p++){var f=g[p];h.appendChild(f.clone())}n.replace(l,[h])}}return i||(i=s>0&&e),!o.has(0)&&i&&n.appendChild(new u(0)),n},t.prototype._accept=function(t,e){if(void 0===t||this._token.type===t){var i=!e||this._scanner.tokenText(this._token);return this._token=this._scanner.next(),i}return!1},t.prototype._backTo=function(t){return this._scanner.pos=t.pos+t.len,this._token=t,!1},t.prototype._until=function(t){var e=this._token;while(this._token.type!==t){if(14===this._token.type)return!1;if(5===this._token.type){var i=this._scanner.next();if(0!==i.type&&4!==i.type&&5!==i.type)return!1}this._token=this._scanner.next()}var n=this._scanner.value.substring(e.pos,this._token.pos).replace(/\\(\$|}|\\)/g,"$1");return this._token=this._scanner.next(),n},t.prototype._parse=function(t){return this._parseEscaped(t)||this._parseTabstopOrVariableName(t)||this._parseComplexPlaceholder(t)||this._parseComplexVariable(t)||this._parseAnything(t)},t.prototype._parseEscaped=function(t){var e;return!!(e=this._accept(5,!0))&&(e=this._accept(0,!0)||this._accept(4,!0)||this._accept(5,!0)||e,t.appendChild(new c(e)),!0)},t.prototype._parseTabstopOrVariableName=function(t){var e,i=this._token,n=this._accept(0)&&(e=this._accept(9,!0)||this._accept(8,!0));return n?(t.appendChild(/^\d+$/.test(e)?new u(Number(e)):new g(e)),!0):this._backTo(i)},t.prototype._parseComplexPlaceholder=function(t){var e,i=this._token,n=this._accept(0)&&this._accept(3)&&(e=this._accept(8,!0));if(!n)return this._backTo(i);var o=new u(Number(e));if(this._accept(1))while(1){if(this._accept(4))return t.appendChild(o),!0;if(!this._parse(o))return t.appendChild(new c("${"+e+":")),o.children.forEach(t.appendChild,t),!0}else{if(!(o.index>0&&this._accept(7)))return this._accept(6)?this._parseTransform(o)?(t.appendChild(o),!0):(this._backTo(i),!1):this._accept(4)?(t.appendChild(o),!0):this._backTo(i);var r=new d;while(1){if(this._parseChoiceElement(r)){if(this._accept(2))continue;if(this._accept(7)&&(o.appendChild(r),this._accept(4)))return t.appendChild(o),!0}return this._backTo(i),!1}}},t.prototype._parseChoiceElement=function(t){var e=this._token,i=[];while(1){if(2===this._token.type||7===this._token.type)break;var n=void 0;if(n=(n=this._accept(5,!0))?this._accept(2,!0)||this._accept(7,!0)||this._accept(5,!0)||n:this._accept(void 0,!0),!n)return this._backTo(e),!1;i.push(n)}return 0===i.length?(this._backTo(e),!1):(t.appendChild(new c(i.join(""))),!0)},t.prototype._parseComplexVariable=function(t){var e,i=this._token,n=this._accept(0)&&this._accept(3)&&(e=this._accept(9,!0));if(!n)return this._backTo(i);var o=new g(e);if(!this._accept(1))return this._accept(6)?this._parseTransform(o)?(t.appendChild(o),!0):(this._backTo(i),!1):this._accept(4)?(t.appendChild(o),!0):this._backTo(i);while(1){if(this._accept(4))return t.appendChild(o),!0;if(!this._parse(o))return t.appendChild(new c("${"+e+":")),o.children.forEach(t.appendChild,t),!0}},t.prototype._parseTransform=function(t){var e=new h,i="",n="";while(1){if(this._accept(6))break;var o=void 0;if(o=this._accept(5,!0))o=this._accept(6,!0)||o,i+=o;else{if(14===this._token.type)return!1;i+=this._accept(void 0,!0)}}while(1){if(this._accept(6))break;o=void 0;if(o=this._accept(5,!0))o=this._accept(5,!0)||this._accept(6,!0)||o,e.appendChild(new c(o));else if(!this._parseFormatString(e)&&!this._parseAnything(e))return!1}while(1){if(this._accept(4))break;if(14===this._token.type)return!1;n+=this._accept(void 0,!0)}try{e.regexp=new RegExp(i,n)}catch(r){return!1}return t.transform=e,!0},t.prototype._parseFormatString=function(t){var e=this._token;if(!this._accept(0))return!1;var i=!1;this._accept(3)&&(i=!0);var n=this._accept(8,!0);if(!n)return this._backTo(e),!1;if(!i)return t.appendChild(new p(Number(n))),!0;if(this._accept(4))return t.appendChild(new p(Number(n))),!0;if(!this._accept(1))return this._backTo(e),!1;if(this._accept(6)){var o=this._accept(9,!0);return o&&this._accept(4)?(t.appendChild(new p(Number(n),o)),!0):(this._backTo(e),!1)}if(this._accept(11)){var r=this._until(4);if(r)return t.appendChild(new p(Number(n),void 0,r,void 0)),!0}else if(this._accept(12)){var s=this._until(4);if(s)return t.appendChild(new p(Number(n),void 0,void 0,s)),!0}else if(this._accept(13)){r=this._until(1);if(r){s=this._until(4);if(s)return t.appendChild(new p(Number(n),void 0,r,s)),!0}}else{s=this._until(4);if(s)return t.appendChild(new p(Number(n),void 0,void 0,s)),!0}return this._backTo(e),!1},t.prototype._parseAnything=function(t){return 14!==this._token.type&&(t.appendChild(new c(this._scanner.tokenText(this._token))),this._accept(void 0),!0)},t}()},b968:function(t,e,i){},d741:function(t,e,i){"use strict";i.r(e),i.d(e,"provideSelectionRanges",(function(){return k}));var n=i("e8e3"),o=i("2504"),r=i("b2cc"),s=i("7061"),a=i("6a89"),c=i("8025"),l=i("c101"),u=i("b707"),d=i("dff7"),h=i("a666"),p=i("3742"),g=function(){function t(){}return t.prototype.provideSelectionRanges=function(t,e){for(var i=[],n=0,o=e;n<o.length;n++){var r=o[n],s=[];i.push(s),this._addInWordRanges(s,t,r),this._addWordRanges(s,t,r),this._addWhitespaceLine(s,t,r),s.push({range:t.getFullModelRange()})}return i},t.prototype._addInWordRanges=function(t,e,i){var n=e.getWordAtPosition(i);if(n){for(var o=n.word,r=n.startColumn,s=i.column-r,c=s,l=s,u=0;c>=0;c--){var d=o.charCodeAt(c);if(95===d||45===d)break;if(Object(p["B"])(d)&&Object(p["C"])(u))break;u=d}for(c+=1;l<o.length;l++){d=o.charCodeAt(l);if(Object(p["C"])(d)&&Object(p["B"])(u))break;if(95===d||45===d)break;u=d}c<l&&t.push({range:new a["a"](i.lineNumber,r+c,i.lineNumber,r+l)})}},t.prototype._addWordRanges=function(t,e,i){var n=e.getWordAtPosition(i);n&&t.push({range:new a["a"](i.lineNumber,n.startColumn,i.lineNumber,n.endColumn)})},t.prototype._addWhitespaceLine=function(t,e,i){e.getLineLength(i.lineNumber)>0&&0===e.getLineFirstNonWhitespaceColumn(i.lineNumber)&&0===e.getLineLastNonWhitespaceColumn(i.lineNumber)&&t.push({range:new a["a"](i.lineNumber,1,i.lineNumber,e.getLineMaxColumn(i.lineNumber))})},t}(),f=i("67b4"),m=i("9e74"),b=i("fdcc"),_=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])},t(e,i)};return function(e,i){function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),v=function(t,e,i,n){function o(t){return t instanceof i?t:new i((function(e){e(t)}))}return new(i||(i=Promise))((function(i,r){function s(t){try{c(n.next(t))}catch(e){r(e)}}function a(t){try{c(n["throw"](t))}catch(e){r(e)}}function c(t){t.done?i(t.value):o(t.value).then(s,a)}c((n=n.apply(t,e||[])).next())}))},y=function(t,e){var i,n,o,r,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return r={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function a(t){return function(e){return c([t,e])}}function c(r){if(i)throw new TypeError("Generator is already executing.");while(s)try{if(i=1,n&&(o=2&r[0]?n["return"]:r[0]?n["throw"]||((o=n["return"])&&o.call(n),0):n.next)&&!(o=o.call(n,r[1])).done)return o;switch(n=0,o&&(r=[2&r[0],o.value]),r[0]){case 0:case 1:o=r;break;case 4:return s.label++,{value:r[1],done:!1};case 5:s.label++,n=r[1],r=[0];continue;case 7:r=s.ops.pop(),s.trys.pop();continue;default:if(o=s.trys,!(o=o.length>0&&o[o.length-1])&&(6===r[0]||2===r[0])){s=0;continue}if(3===r[0]&&(!o||r[1]>o[0]&&r[1]<o[3])){s.label=r[1];break}if(6===r[0]&&s.label<o[1]){s.label=o[1],o=r;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(r);break}o[2]&&s.ops.pop(),s.trys.pop();continue}r=e.call(t,s)}catch(a){r=[6,a],n=0}finally{i=o=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}},w=function(){function t(t,e){this.index=t,this.ranges=e}return t.prototype.mov=function(e){var i=this.index+(e?1:-1);if(i<0||i>=this.ranges.length)return this;var n=new t(i,this.ranges);return n.ranges[i].equalsRange(this.ranges[this.index])?n.mov(e):n},t}(),S=function(){function t(t){this._ignoreSelection=!1,this._editor=t}return t.get=function(e){return e.getContribution(t.ID)},t.prototype.dispose=function(){Object(h["f"])(this._selectionListener)},t.prototype.run=function(t){var e=this;if(this._editor.hasModel()){var i=this._editor.getSelections(),r=this._editor.getModel();if(u["w"].has(r)){var s=Promise.resolve(void 0);return this._state||(s=k(r,i.map((function(t){return t.getPosition()})),o["a"].None).then((function(t){if(n["q"](t)&&t.length===i.length&&e._editor.hasModel()&&n["g"](e._editor.getSelections(),i,(function(t,e){return t.equalsSelection(e)}))){for(var o=function(e){t[e]=t[e].filter((function(t){return t.containsPosition(i[e].getStartPosition())&&t.containsPosition(i[e].getEndPosition())})),t[e].unshift(i[e])},r=0;r<t.length;r++)o(r);e._state=t.map((function(t){return new w(0,t)})),Object(h["f"])(e._selectionListener),e._selectionListener=e._editor.onDidChangeCursorPosition((function(){e._ignoreSelection||(Object(h["f"])(e._selectionListener),e._state=void 0)}))}}))),s.then((function(){if(e._state){e._state=e._state.map((function(e){return e.mov(t)}));var i=e._state.map((function(t){return c["a"].fromPositions(t.ranges[t.index].getStartPosition(),t.ranges[t.index].getEndPosition())}));e._ignoreSelection=!0;try{e._editor.setSelections(i)}finally{e._ignoreSelection=!1}}}))}}},t.ID="editor.contrib.smartSelectController",t}(),C=function(t){function e(e,i){var n=t.call(this,i)||this;return n._forward=e,n}return _(e,t),e.prototype.run=function(t,e){return v(this,void 0,void 0,(function(){var t;return y(this,(function(i){switch(i.label){case 0:return t=S.get(e),t?[4,t.run(this._forward)]:[3,2];case 1:i.sent(),i.label=2;case 2:return[2]}}))}))},e}(r["b"]),O=function(t){function e(){return t.call(this,!0,{id:"editor.action.smartSelect.expand",label:d["a"]("smartSelect.expand","Expand Selection"),alias:"Expand Selection",precondition:void 0,kbOpts:{kbExpr:l["a"].editorTextFocus,primary:1553,mac:{primary:3345,secondary:[1297]},weight:100},menuOpts:{menuId:25,group:"1_basic",title:d["a"]({key:"miSmartSelectGrow",comment:["&& denotes a mnemonic"]},"&&Expand Selection"),order:2}})||this}return _(e,t),e}(C);m["a"].registerCommandAlias("editor.action.smartSelect.grow","editor.action.smartSelect.expand");var x=function(t){function e(){return t.call(this,!1,{id:"editor.action.smartSelect.shrink",label:d["a"]("smartSelect.shrink","Shrink Selection"),alias:"Shrink Selection",precondition:void 0,kbOpts:{kbExpr:l["a"].editorTextFocus,primary:1551,mac:{primary:3343,secondary:[1295]},weight:100},menuOpts:{menuId:25,group:"1_basic",title:d["a"]({key:"miSmartSelectShrink",comment:["&& denotes a mnemonic"]},"&&Shrink Selection"),order:3}})||this}return _(e,t),e}(C);function k(t,e,i){var o=u["w"].all(t);1===o.length&&o.unshift(new f["a"]);for(var r=[],c=[],l=0,d=o;l<d.length;l++){var h=d[l];r.push(Promise.resolve(h.provideSelectionRanges(t,e,i)).then((function(t){if(n["q"](t)&&t.length===e.length)for(var i=0;i<e.length;i++){c[i]||(c[i]=[]);for(var o=0,r=t[i];o<r.length;o++){var s=r[o];a["a"].isIRange(s.range)&&a["a"].containsPosition(s.range,e[i])&&c[i].push(a["a"].lift(s.range))}}}),b["f"]))}return Promise.all(r).then((function(){return c.map((function(e){if(0===e.length)return[];e.sort((function(t,e){return s["a"].isBefore(t.getStartPosition(),e.getStartPosition())?1:s["a"].isBefore(e.getStartPosition(),t.getStartPosition())||s["a"].isBefore(t.getEndPosition(),e.getEndPosition())?-1:s["a"].isBefore(e.getEndPosition(),t.getEndPosition())?1:0}));for(var i,n=[],o=0,r=e;o<r.length;o++){var c=r[o];(!i||a["a"].containsRange(c,i)&&!a["a"].equalsRange(c,i))&&(n.push(c),i=c)}for(var l=[n[0]],u=1;u<n.length;u++){var d=n[u-1],h=n[u];if(h.startLineNumber!==d.startLineNumber||h.endLineNumber!==d.endLineNumber){var p=new a["a"](d.startLineNumber,t.getLineFirstNonWhitespaceColumn(d.startLineNumber),d.endLineNumber,t.getLineLastNonWhitespaceColumn(d.endLineNumber));p.containsRange(d)&&!p.equalsRange(d)&&h.containsRange(p)&&!h.equalsRange(p)&&l.push(p);var g=new a["a"](d.startLineNumber,1,d.endLineNumber,t.getLineMaxColumn(d.endLineNumber));g.containsRange(d)&&!g.equalsRange(p)&&h.containsRange(g)&&!h.equalsRange(g)&&l.push(g)}l.push(h)}return l}))}))}Object(r["h"])(S.ID,S),Object(r["f"])(O),Object(r["f"])(x),u["w"].register("*",new g),Object(r["l"])("_executeSelectionRangeProvider",(function(t){for(var e=[],i=1;i<arguments.length;i++)e[i-1]=arguments[i];var n=e[0];return k(t,n,o["a"].None)}))},f33e:function(t,e,i){"use strict";i.r(e),i.d(e,"CancellationTokenSource",(function(){return l})),i.d(e,"Emitter",(function(){return u})),i.d(e,"KeyCode",(function(){return d})),i.d(e,"KeyMod",(function(){return h})),i.d(e,"Position",(function(){return p})),i.d(e,"Range",(function(){return g})),i.d(e,"Selection",(function(){return f})),i.d(e,"SelectionDirection",(function(){return m})),i.d(e,"MarkerSeverity",(function(){return b})),i.d(e,"MarkerTag",(function(){return _})),i.d(e,"Uri",(function(){return v})),i.d(e,"Token",(function(){return y})),i.d(e,"editor",(function(){return w})),i.d(e,"languages",(function(){return S}));var n=i("fd49"),o=i("6eab"),r=i("4390"),s=i("5249"),a=self;n["e"].wrappingIndent.defaultValue=0,n["e"].glyphMargin.defaultValue=!1,n["e"].autoIndent.defaultValue=3,n["e"].overviewRulerLanes.defaultValue=2;var c=Object(o["a"])();c.editor=Object(r["a"])(),c.languages=Object(s["a"])();var l=c.CancellationTokenSource,u=c.Emitter,d=c.KeyCode,h=c.KeyMod,p=c.Position,g=c.Range,f=c.Selection,m=c.SelectionDirection,b=c.MarkerSeverity,_=c.MarkerTag,v=c.Uri,y=c.Token,w=c.editor,S=c.languages;a.monaco=c,"undefined"!==typeof a.require&&"function"===typeof a.require.config&&a.require.config({ignoreDuplicateModules:["vscode-languageserver-types","vscode-languageserver-types/main","vscode-nls","vscode-nls/vscode-nls","jsonc-parser","jsonc-parser/main","vscode-uri","vscode-uri/index","vs/basic-languages/typescript/typescript"]})}}]);