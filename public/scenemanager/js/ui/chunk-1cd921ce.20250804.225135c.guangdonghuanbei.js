(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1cd921ce"],{"0043":function(t,n){function e(t,n,e){var r=n[0],o=n[1];return t[0]=e[0]*r+e[4]*o+e[12],t[1]=e[1]*r+e[5]*o+e[13],t}t.exports=e},"00fc":function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=e("51cb");function o(t,n){void 0===n&&(n=!0);var e=r.makePropertyInjectDecorator(t,n),o=r.makePropertyInjectNamedDecorator(t,n),i=r.makePropertyInjectTaggedDecorator(t,n),u=r.makePropertyMultiInjectDecorator(t,n);return{lazyInject:e,lazyInjectNamed:o,lazyInjectTagged:i,lazyMultiInject:u}}n.default=o},"0300":function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.Lookup=void 0;var r=e("30e3"),o=function(){function t(){this._map=new Map}return t.prototype.getMap=function(){return this._map},t.prototype.add=function(t,n){if(null===t||void 0===t)throw new Error(r.NULL_ARGUMENT);if(null===n||void 0===n)throw new Error(r.NULL_ARGUMENT);var e=this._map.get(t);void 0!==e?(e.push(n),this._map.set(t,e)):this._map.set(t,[n])},t.prototype.get=function(t){if(null===t||void 0===t)throw new Error(r.NULL_ARGUMENT);var n=this._map.get(t);if(void 0!==n)return n;throw new Error(r.KEY_NOT_FOUND)},t.prototype.remove=function(t){if(null===t||void 0===t)throw new Error(r.NULL_ARGUMENT);if(!this._map.delete(t))throw new Error(r.KEY_NOT_FOUND)},t.prototype.removeByCondition=function(t){var n=this;this._map.forEach((function(e,r){var o=e.filter((function(n){return!t(n)}));o.length>0?n._map.set(r,o):n._map.delete(r)}))},t.prototype.hasKey=function(t){if(null===t||void 0===t)throw new Error(r.NULL_ARGUMENT);return this._map.has(t)},t.prototype.clone=function(){var n=new t;return this._map.forEach((function(t,e){t.forEach((function(t){return n.add(e,t.clone())}))})),n},t.prototype.traverse=function(t){this._map.forEach((function(n,e){t(e,n)}))},t}();n.Lookup=o},"033c":function(t,n){function e(t,n){return t[0]=-n[0],t[1]=-n[1],t}t.exports=e},"06ed":function(t,n){function e(t,n,e){return t[0]=n[0]/e[0],t[1]=n[1]/e[1],t}t.exports=e},"086a":function(t,n,e){t.exports=e("339b")},"0a4a":function(t,n,e){"use strict";e.r(n),e.d(n,"forceCenter",(function(){return r})),e.d(n,"forceCollide",(function(){return O})),e.d(n,"forceLink",(function(){return R})),e.d(n,"forceManyBody",(function(){return Q})),e.d(n,"forceRadial",(function(){return Z})),e.d(n,"forceSimulation",(function(){return K})),e.d(n,"forceX",(function(){return X})),e.d(n,"forceY",(function(){return tt}));var r=function(t,n){var e,r=1;function o(){var o,i,u=e.length,a=0,s=0;for(o=0;o<u;++o)i=e[o],a+=i.x,s+=i.y;for(a=(a/u-t)*r,s=(s/u-n)*r,o=0;o<u;++o)i=e[o],i.x-=a,i.y-=s}return null==t&&(t=0),null==n&&(n=0),o.initialize=function(t){e=t},o.x=function(n){return arguments.length?(t=+n,o):t},o.y=function(t){return arguments.length?(n=+t,o):n},o.strength=function(t){return arguments.length?(r=+t,o):r},o},o=function(t){const n=+this._x.call(null,t),e=+this._y.call(null,t);return i(this.cover(n,e),n,e,t)};function i(t,n,e,r){if(isNaN(n)||isNaN(e))return t;var o,i,u,a,s,c,f,h,d,l=t._root,g={data:r},p=t._x0,m=t._y0,v=t._x1,y=t._y1;if(!l)return t._root=g,t;while(l.length)if((c=n>=(i=(p+v)/2))?p=i:v=i,(f=e>=(u=(m+y)/2))?m=u:y=u,o=l,!(l=l[h=f<<1|c]))return o[h]=g,t;if(a=+t._x.call(null,l.data),s=+t._y.call(null,l.data),n===a&&e===s)return g.next=l,o?o[h]=g:t._root=g,t;do{o=o?o[h]=new Array(4):t._root=new Array(4),(c=n>=(i=(p+v)/2))?p=i:v=i,(f=e>=(u=(m+y)/2))?m=u:y=u}while((h=f<<1|c)===(d=(s>=u)<<1|a>=i));return o[d]=l,o[h]=g,t}function u(t){var n,e,r,o,u=t.length,a=new Array(u),s=new Array(u),c=1/0,f=1/0,h=-1/0,d=-1/0;for(e=0;e<u;++e)isNaN(r=+this._x.call(null,n=t[e]))||isNaN(o=+this._y.call(null,n))||(a[e]=r,s[e]=o,r<c&&(c=r),r>h&&(h=r),o<f&&(f=o),o>d&&(d=o));if(c>h||f>d)return this;for(this.cover(c,f).cover(h,d),e=0;e<u;++e)i(this,a[e],s[e],t[e]);return this}var a=function(t,n){if(isNaN(t=+t)||isNaN(n=+n))return this;var e=this._x0,r=this._y0,o=this._x1,i=this._y1;if(isNaN(e))o=(e=Math.floor(t))+1,i=(r=Math.floor(n))+1;else{var u,a,s=o-e||1,c=this._root;while(e>t||t>=o||r>n||n>=i)switch(a=(n<r)<<1|t<e,u=new Array(4),u[a]=c,c=u,s*=2,a){case 0:o=e+s,i=r+s;break;case 1:e=o-s,i=r+s;break;case 2:o=e+s,r=i-s;break;case 3:e=o-s,r=i-s;break}this._root&&this._root.length&&(this._root=c)}return this._x0=e,this._y0=r,this._x1=o,this._y1=i,this},s=function(){var t=[];return this.visit((function(n){if(!n.length)do{t.push(n.data)}while(n=n.next)})),t},c=function(t){return arguments.length?this.cover(+t[0][0],+t[0][1]).cover(+t[1][0],+t[1][1]):isNaN(this._x0)?void 0:[[this._x0,this._y0],[this._x1,this._y1]]},f=function(t,n,e,r,o){this.node=t,this.x0=n,this.y0=e,this.x1=r,this.y1=o},h=function(t,n,e){var r,o,i,u,a,s,c,h=this._x0,d=this._y0,l=this._x1,g=this._y1,p=[],m=this._root;m&&p.push(new f(m,h,d,l,g)),null==e?e=1/0:(h=t-e,d=n-e,l=t+e,g=n+e,e*=e);while(s=p.pop())if(!(!(m=s.node)||(o=s.x0)>l||(i=s.y0)>g||(u=s.x1)<h||(a=s.y1)<d))if(m.length){var v=(o+u)/2,y=(i+a)/2;p.push(new f(m[3],v,y,u,a),new f(m[2],o,y,v,a),new f(m[1],v,i,u,y),new f(m[0],o,i,v,y)),(c=(n>=y)<<1|t>=v)&&(s=p[p.length-1],p[p.length-1]=p[p.length-1-c],p[p.length-1-c]=s)}else{var w=t-+this._x.call(null,m.data),b=n-+this._y.call(null,m.data),M=w*w+b*b;if(M<e){var _=Math.sqrt(e=M);h=t-_,d=n-_,l=t+_,g=n+_,r=m.data}}return r},d=function(t){if(isNaN(i=+this._x.call(null,t))||isNaN(u=+this._y.call(null,t)))return this;var n,e,r,o,i,u,a,s,c,f,h,d,l=this._root,g=this._x0,p=this._y0,m=this._x1,v=this._y1;if(!l)return this;if(l.length)while(1){if((c=i>=(a=(g+m)/2))?g=a:m=a,(f=u>=(s=(p+v)/2))?p=s:v=s,n=l,!(l=l[h=f<<1|c]))return this;if(!l.length)break;(n[h+1&3]||n[h+2&3]||n[h+3&3])&&(e=n,d=h)}while(l.data!==t)if(r=l,!(l=l.next))return this;return(o=l.next)&&delete l.next,r?(o?r.next=o:delete r.next,this):n?(o?n[h]=o:delete n[h],(l=n[0]||n[1]||n[2]||n[3])&&l===(n[3]||n[2]||n[1]||n[0])&&!l.length&&(e?e[d]=l:this._root=l),this):(this._root=o,this)};function l(t){for(var n=0,e=t.length;n<e;++n)this.remove(t[n]);return this}var g=function(){return this._root},p=function(){var t=0;return this.visit((function(n){if(!n.length)do{++t}while(n=n.next)})),t},m=function(t){var n,e,r,o,i,u,a=[],s=this._root;s&&a.push(new f(s,this._x0,this._y0,this._x1,this._y1));while(n=a.pop())if(!t(s=n.node,r=n.x0,o=n.y0,i=n.x1,u=n.y1)&&s.length){var c=(r+i)/2,h=(o+u)/2;(e=s[3])&&a.push(new f(e,c,h,i,u)),(e=s[2])&&a.push(new f(e,r,h,c,u)),(e=s[1])&&a.push(new f(e,c,o,i,h)),(e=s[0])&&a.push(new f(e,r,o,c,h))}return this},v=function(t){var n,e=[],r=[];this._root&&e.push(new f(this._root,this._x0,this._y0,this._x1,this._y1));while(n=e.pop()){var o=n.node;if(o.length){var i,u=n.x0,a=n.y0,s=n.x1,c=n.y1,h=(u+s)/2,d=(a+c)/2;(i=o[0])&&e.push(new f(i,u,a,h,d)),(i=o[1])&&e.push(new f(i,h,a,s,d)),(i=o[2])&&e.push(new f(i,u,d,h,c)),(i=o[3])&&e.push(new f(i,h,d,s,c))}r.push(n)}while(n=r.pop())t(n.node,n.x0,n.y0,n.x1,n.y1);return this};function y(t){return t[0]}var w=function(t){return arguments.length?(this._x=t,this):this._x};function b(t){return t[1]}var M=function(t){return arguments.length?(this._y=t,this):this._y};function _(t,n,e){var r=new E(null==n?y:n,null==e?b:e,NaN,NaN,NaN,NaN);return null==t?r:r.addAll(t)}function E(t,n,e,r,o,i){this._x=t,this._y=n,this._x0=e,this._y0=r,this._x1=o,this._y1=i,this._root=void 0}function x(t){var n={data:t.data},e=n;while(t=t.next)e=e.next={data:t.data};return n}var N=_.prototype=E.prototype;N.copy=function(){var t,n,e=new E(this._x,this._y,this._x0,this._y0,this._x1,this._y1),r=this._root;if(!r)return e;if(!r.length)return e._root=x(r),e;t=[{source:r,target:e._root=new Array(4)}];while(r=t.pop())for(var o=0;o<4;++o)(n=r.source[o])&&(n.length?t.push({source:n,target:r.target[o]=new Array(4)}):r.target[o]=x(n));return e},N.add=o,N.addAll=u,N.cover=a,N.data=s,N.extent=c,N.find=h,N.remove=d,N.removeAll=l,N.root=g,N.size=p,N.visit=m,N.visitAfter=v,N.x=w,N.y=M;var S=function(t){return function(){return t}},T=function(t){return 1e-6*(t()-.5)};function A(t){return t.x+t.vx}function I(t){return t.y+t.vy}var O=function(t){var n,e,r,o=1,i=1;function u(){for(var t,u,s,c,f,h,d,l=n.length,g=0;g<i;++g)for(u=_(n,A,I).visitAfter(a),t=0;t<l;++t)s=n[t],h=e[s.index],d=h*h,c=s.x+s.vx,f=s.y+s.vy,u.visit(p);function p(t,n,e,i,u){var a=t.data,l=t.r,g=h+l;if(!a)return n>c+g||i<c-g||e>f+g||u<f-g;if(a.index>s.index){var p=c-a.x-a.vx,m=f-a.y-a.vy,v=p*p+m*m;v<g*g&&(0===p&&(p=T(r),v+=p*p),0===m&&(m=T(r),v+=m*m),v=(g-(v=Math.sqrt(v)))/v*o,s.vx+=(p*=v)*(g=(l*=l)/(d+l)),s.vy+=(m*=v)*g,a.vx-=p*(g=1-g),a.vy-=m*g)}}}function a(t){if(t.data)return t.r=e[t.data.index];for(var n=t.r=0;n<4;++n)t[n]&&t[n].r>t.r&&(t.r=t[n].r)}function s(){if(n){var r,o,i=n.length;for(e=new Array(i),r=0;r<i;++r)o=n[r],e[o.index]=+t(o,r,n)}}return"function"!==typeof t&&(t=S(null==t?1:+t)),u.initialize=function(t,e){n=t,r=e,s()},u.iterations=function(t){return arguments.length?(i=+t,u):i},u.strength=function(t){return arguments.length?(o=+t,u):o},u.radius=function(n){return arguments.length?(t="function"===typeof n?n:S(+n),s(),u):t},u};function C(t){return t.index}function j(t,n){var e=t.get(n);if(!e)throw new Error("node not found: "+n);return e}var R=function(t){var n,e,r,o,i,u,a=C,s=h,c=S(30),f=1;function h(t){return 1/Math.min(o[t.source.index],o[t.target.index])}function d(r){for(var o=0,a=t.length;o<f;++o)for(var s,c,h,d,l,g,p,m=0;m<a;++m)s=t[m],c=s.source,h=s.target,d=h.x+h.vx-c.x-c.vx||T(u),l=h.y+h.vy-c.y-c.vy||T(u),g=Math.sqrt(d*d+l*l),g=(g-e[m])/g*r*n[m],d*=g,l*=g,h.vx-=d*(p=i[m]),h.vy-=l*p,c.vx+=d*(p=1-p),c.vy+=l*p}function l(){if(r){var u,s,c=r.length,f=t.length,h=new Map(r.map((t,n)=>[a(t,n,r),t]));for(u=0,o=new Array(c);u<f;++u)s=t[u],s.index=u,"object"!==typeof s.source&&(s.source=j(h,s.source)),"object"!==typeof s.target&&(s.target=j(h,s.target)),o[s.source.index]=(o[s.source.index]||0)+1,o[s.target.index]=(o[s.target.index]||0)+1;for(u=0,i=new Array(f);u<f;++u)s=t[u],i[u]=o[s.source.index]/(o[s.source.index]+o[s.target.index]);n=new Array(f),g(),e=new Array(f),p()}}function g(){if(r)for(var e=0,o=t.length;e<o;++e)n[e]=+s(t[e],e,t)}function p(){if(r)for(var n=0,o=t.length;n<o;++n)e[n]=+c(t[n],n,t)}return null==t&&(t=[]),d.initialize=function(t,n){r=t,u=n,l()},d.links=function(n){return arguments.length?(t=n,l(),d):t},d.id=function(t){return arguments.length?(a=t,d):a},d.iterations=function(t){return arguments.length?(f=+t,d):f},d.strength=function(t){return arguments.length?(s="function"===typeof t?t:S(+t),g(),d):s},d.distance=function(t){return arguments.length?(c="function"===typeof t?t:S(+t),p(),d):c},d},k={value:()=>{}};function P(){for(var t,n=0,e=arguments.length,r={};n<e;++n){if(!(t=arguments[n]+"")||t in r||/[\s.]/.test(t))throw new Error("illegal type: "+t);r[t]=[]}return new D(r)}function D(t){this._=t}function B(t,n){return t.trim().split(/^|\s+/).map((function(t){var e="",r=t.indexOf(".");if(r>=0&&(e=t.slice(r+1),t=t.slice(0,r)),t&&!n.hasOwnProperty(t))throw new Error("unknown type: "+t);return{type:t,name:e}}))}function L(t,n){for(var e,r=0,o=t.length;r<o;++r)if((e=t[r]).name===n)return e.value}function G(t,n,e){for(var r=0,o=t.length;r<o;++r)if(t[r].name===n){t[r]=k,t=t.slice(0,r).concat(t.slice(r+1));break}return null!=e&&t.push({name:n,value:e}),t}D.prototype=P.prototype={constructor:D,on:function(t,n){var e,r=this._,o=B(t+"",r),i=-1,u=o.length;if(!(arguments.length<2)){if(null!=n&&"function"!==typeof n)throw new Error("invalid callback: "+n);while(++i<u)if(e=(t=o[i]).type)r[e]=G(r[e],t.name,n);else if(null==n)for(e in r)r[e]=G(r[e],t.name,null);return this}while(++i<u)if((e=(t=o[i]).type)&&(e=L(r[e],t.name)))return e},copy:function(){var t={},n=this._;for(var e in n)t[e]=n[e].slice();return new D(t)},call:function(t,n){if((e=arguments.length-2)>0)for(var e,r,o=new Array(e),i=0;i<e;++i)o[i]=arguments[i+2];if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(r=this._[t],i=0,e=r.length;i<e;++i)r[i].value.apply(n,o)},apply:function(t,n,e){if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(var r=this._[t],o=0,i=r.length;o<i;++o)r[o].value.apply(n,e)}};var q=P,F=e("74f4");const U=1664525,V=1013904223,z=4294967296;var W=function(){let t=1;return()=>(t=(U*t+V)%z)/z};function Y(t){return t.x}function H(t){return t.y}var $=10,J=Math.PI*(3-Math.sqrt(5)),K=function(t){var n,e=1,r=.001,o=1-Math.pow(r,1/300),i=0,u=.6,a=new Map,s=Object(F["a"])(h),c=q("tick","end"),f=W();function h(){d(),c.call("tick",n),e<r&&(s.stop(),c.call("end",n))}function d(r){var s,c,f=t.length;void 0===r&&(r=1);for(var h=0;h<r;++h)for(e+=(i-e)*o,a.forEach((function(t){t(e)})),s=0;s<f;++s)c=t[s],null==c.fx?c.x+=c.vx*=u:(c.x=c.fx,c.vx=0),null==c.fy?c.y+=c.vy*=u:(c.y=c.fy,c.vy=0);return n}function l(){for(var n,e=0,r=t.length;e<r;++e){if(n=t[e],n.index=e,null!=n.fx&&(n.x=n.fx),null!=n.fy&&(n.y=n.fy),isNaN(n.x)||isNaN(n.y)){var o=$*Math.sqrt(.5+e),i=e*J;n.x=o*Math.cos(i),n.y=o*Math.sin(i)}(isNaN(n.vx)||isNaN(n.vy))&&(n.vx=n.vy=0)}}function g(n){return n.initialize&&n.initialize(t,f),n}return null==t&&(t=[]),l(),n={tick:d,restart:function(){return s.restart(h),n},stop:function(){return s.stop(),n},nodes:function(e){return arguments.length?(t=e,l(),a.forEach(g),n):t},alpha:function(t){return arguments.length?(e=+t,n):e},alphaMin:function(t){return arguments.length?(r=+t,n):r},alphaDecay:function(t){return arguments.length?(o=+t,n):+o},alphaTarget:function(t){return arguments.length?(i=+t,n):i},velocityDecay:function(t){return arguments.length?(u=1-t,n):1-u},randomSource:function(t){return arguments.length?(f=t,a.forEach(g),n):f},force:function(t,e){return arguments.length>1?(null==e?a.delete(t):a.set(t,g(e)),n):a.get(t)},find:function(n,e,r){var o,i,u,a,s,c=0,f=t.length;for(null==r?r=1/0:r*=r,c=0;c<f;++c)a=t[c],o=n-a.x,i=e-a.y,u=o*o+i*i,u<r&&(s=a,r=u);return s},on:function(t,e){return arguments.length>1?(c.on(t,e),n):c.on(t)}}},Q=function(){var t,n,e,r,o,i=S(-30),u=1,a=1/0,s=.81;function c(e){var o,i=t.length,u=_(t,Y,H).visitAfter(h);for(r=e,o=0;o<i;++o)n=t[o],u.visit(d)}function f(){if(t){var n,e,r=t.length;for(o=new Array(r),n=0;n<r;++n)e=t[n],o[e.index]=+i(e,n,t)}}function h(t){var n,e,r,i,u,a=0,s=0;if(t.length){for(r=i=u=0;u<4;++u)(n=t[u])&&(e=Math.abs(n.value))&&(a+=n.value,s+=e,r+=e*n.x,i+=e*n.y);t.x=r/s,t.y=i/s}else{n=t,n.x=n.data.x,n.y=n.data.y;do{a+=o[n.data.index]}while(n=n.next)}t.value=a}function d(t,i,c,f){if(!t.value)return!0;var h=t.x-n.x,d=t.y-n.y,l=f-i,g=h*h+d*d;if(l*l/s<g)return g<a&&(0===h&&(h=T(e),g+=h*h),0===d&&(d=T(e),g+=d*d),g<u&&(g=Math.sqrt(u*g)),n.vx+=h*t.value*r/g,n.vy+=d*t.value*r/g),!0;if(!(t.length||g>=a)){(t.data!==n||t.next)&&(0===h&&(h=T(e),g+=h*h),0===d&&(d=T(e),g+=d*d),g<u&&(g=Math.sqrt(u*g)));do{t.data!==n&&(l=o[t.data.index]*r/g,n.vx+=h*l,n.vy+=d*l)}while(t=t.next)}}return c.initialize=function(n,r){t=n,e=r,f()},c.strength=function(t){return arguments.length?(i="function"===typeof t?t:S(+t),f(),c):i},c.distanceMin=function(t){return arguments.length?(u=t*t,c):Math.sqrt(u)},c.distanceMax=function(t){return arguments.length?(a=t*t,c):Math.sqrt(a)},c.theta=function(t){return arguments.length?(s=t*t,c):Math.sqrt(s)},c},Z=function(t,n,e){var r,o,i,u=S(.1);function a(t){for(var u=0,a=r.length;u<a;++u){var s=r[u],c=s.x-n||1e-6,f=s.y-e||1e-6,h=Math.sqrt(c*c+f*f),d=(i[u]-h)*o[u]*t/h;s.vx+=c*d,s.vy+=f*d}}function s(){if(r){var n,e=r.length;for(o=new Array(e),i=new Array(e),n=0;n<e;++n)i[n]=+t(r[n],n,r),o[n]=isNaN(i[n])?0:+u(r[n],n,r)}}return"function"!==typeof t&&(t=S(+t)),null==n&&(n=0),null==e&&(e=0),a.initialize=function(t){r=t,s()},a.strength=function(t){return arguments.length?(u="function"===typeof t?t:S(+t),s(),a):u},a.radius=function(n){return arguments.length?(t="function"===typeof n?n:S(+n),s(),a):t},a.x=function(t){return arguments.length?(n=+t,a):n},a.y=function(t){return arguments.length?(e=+t,a):e},a},X=function(t){var n,e,r,o=S(.1);function i(t){for(var o,i=0,u=n.length;i<u;++i)o=n[i],o.vx+=(r[i]-o.x)*e[i]*t}function u(){if(n){var i,u=n.length;for(e=new Array(u),r=new Array(u),i=0;i<u;++i)e[i]=isNaN(r[i]=+t(n[i],i,n))?0:+o(n[i],i,n)}}return"function"!==typeof t&&(t=S(null==t?0:+t)),i.initialize=function(t){n=t,u()},i.strength=function(t){return arguments.length?(o="function"===typeof t?t:S(+t),u(),i):o},i.x=function(n){return arguments.length?(t="function"===typeof n?n:S(+n),u(),i):t},i},tt=function(t){var n,e,r,o=S(.1);function i(t){for(var o,i=0,u=n.length;i<u;++i)o=n[i],o.vy+=(r[i]-o.y)*e[i]*t}function u(){if(n){var i,u=n.length;for(e=new Array(u),r=new Array(u),i=0;i<u;++i)e[i]=isNaN(r[i]=+t(n[i],i,n))?0:+o(n[i],i,n)}}return"function"!==typeof t&&(t=S(null==t?0:+t)),i.initialize=function(t){n=t,u()},i.strength=function(t){return arguments.length?(o="function"===typeof t?t:S(+t),u(),i):o},i.y=function(n){return arguments.length?(t="function"===typeof n?n:S(+n),u(),i):t},i}},"0aae":function(t,n){function e(t,n){return t[0]=Math.floor(n[0]),t[1]=Math.floor(n[1]),t}t.exports=e},"0b70":function(t,n){function e(t,n){var e=n[0]-t[0],r=n[1]-t[1];return e*e+r*r}t.exports=e},"0fd9":function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.BindingInWhenOnSyntax=void 0;var r=e("e801"),o=e("e34e"),i=e("cf81"),u=function(){function t(t){this._binding=t,this._bindingWhenSyntax=new i.BindingWhenSyntax(this._binding),this._bindingOnSyntax=new o.BindingOnSyntax(this._binding),this._bindingInSyntax=new r.BindingInSyntax(t)}return t.prototype.inRequestScope=function(){return this._bindingInSyntax.inRequestScope()},t.prototype.inSingletonScope=function(){return this._bindingInSyntax.inSingletonScope()},t.prototype.inTransientScope=function(){return this._bindingInSyntax.inTransientScope()},t.prototype.when=function(t){return this._bindingWhenSyntax.when(t)},t.prototype.whenTargetNamed=function(t){return this._bindingWhenSyntax.whenTargetNamed(t)},t.prototype.whenTargetIsDefault=function(){return this._bindingWhenSyntax.whenTargetIsDefault()},t.prototype.whenTargetTagged=function(t,n){return this._bindingWhenSyntax.whenTargetTagged(t,n)},t.prototype.whenInjectedInto=function(t){return this._bindingWhenSyntax.whenInjectedInto(t)},t.prototype.whenParentNamed=function(t){return this._bindingWhenSyntax.whenParentNamed(t)},t.prototype.whenParentTagged=function(t,n){return this._bindingWhenSyntax.whenParentTagged(t,n)},t.prototype.whenAnyAncestorIs=function(t){return this._bindingWhenSyntax.whenAnyAncestorIs(t)},t.prototype.whenNoAncestorIs=function(t){return this._bindingWhenSyntax.whenNoAncestorIs(t)},t.prototype.whenAnyAncestorNamed=function(t){return this._bindingWhenSyntax.whenAnyAncestorNamed(t)},t.prototype.whenAnyAncestorTagged=function(t,n){return this._bindingWhenSyntax.whenAnyAncestorTagged(t,n)},t.prototype.whenNoAncestorNamed=function(t){return this._bindingWhenSyntax.whenNoAncestorNamed(t)},t.prototype.whenNoAncestorTagged=function(t,n){return this._bindingWhenSyntax.whenNoAncestorTagged(t,n)},t.prototype.whenAnyAncestorMatches=function(t){return this._bindingWhenSyntax.whenAnyAncestorMatches(t)},t.prototype.whenNoAncestorMatches=function(t){return this._bindingWhenSyntax.whenNoAncestorMatches(t)},t.prototype.onActivation=function(t){return this._bindingOnSyntax.onActivation(t)},t}();n.BindingInWhenOnSyntax=u},"123d":function(t,n){function e(){var t=new Float32Array(2);return t[0]=0,t[1]=0,t}t.exports=e},"14a2":function(t,n,e){t.exports=o;var r=e("123d")();function o(t,n,e,o,i,u){var a,s;for(n||(n=2),e||(e=0),s=o?Math.min(o*n+e,t.length):t.length,a=e;a<s;a+=n)r[0]=t[a],r[1]=t[a+1],i(r,r,u),t[a]=r[0],t[a+1]=r[1];return t}},"155f":function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.TargetTypeEnum=n.BindingTypeEnum=n.BindingScopeEnum=void 0;var r={Request:"Request",Singleton:"Singleton",Transient:"Transient"};n.BindingScopeEnum=r;var o={ConstantValue:"ConstantValue",Constructor:"Constructor",DynamicValue:"DynamicValue",Factory:"Factory",Function:"Function",Instance:"Instance",Invalid:"Invalid",Provider:"Provider"};n.BindingTypeEnum=o;var i={ClassProperty:"ClassProperty",ConstructorArgument:"ConstructorArgument",Variable:"Variable"};n.TargetTypeEnum=i},1788:function(t,n,e){t.exports=o;var r=e("3443");function o(t,n){var e=t[0],o=t[1],i=n[0],u=n[1];return Math.abs(e-i)<=r*Math.max(1,Math.abs(e),Math.abs(i))&&Math.abs(o-u)<=r*Math.max(1,Math.abs(o),Math.abs(u))}},1789:function(t,n,e){"use strict";e.r(n),e.d(n,"assign",(function(){return d})),e.d(n,"format",(function(){return C})),e.d(n,"parse",(function(){return j})),e.d(n,"defaultI18n",(function(){return v})),e.d(n,"setGlobalDateI18n",(function(){return w})),e.d(n,"setGlobalDateMasks",(function(){return O}));var r=/d{1,4}|M{1,4}|YY(?:YY)?|S{1,3}|Do|ZZ|Z|([HhMsDm])\1?|[aA]|"[^"]*"|'[^']*'/g,o="\\d\\d?",i="\\d\\d",u="\\d{3}",a="\\d{4}",s="[^\\s]+",c=/\[([^]*?)\]/gm;function f(t,n){for(var e=[],r=0,o=t.length;r<o;r++)e.push(t[r].substr(0,n));return e}var h=function(t){return function(n,e){var r=e[t].map((function(t){return t.toLowerCase()})),o=r.indexOf(n.toLowerCase());return o>-1?o:null}};function d(t){for(var n=[],e=1;e<arguments.length;e++)n[e-1]=arguments[e];for(var r=0,o=n;r<o.length;r++){var i=o[r];for(var u in i)t[u]=i[u]}return t}var l=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],g=["January","February","March","April","May","June","July","August","September","October","November","December"],p=f(g,3),m=f(l,3),v={dayNamesShort:m,dayNames:l,monthNamesShort:p,monthNames:g,amPm:["am","pm"],DoFn:function(t){return t+["th","st","nd","rd"][t%10>3?0:(t-t%10!==10?1:0)*t%10]}},y=d({},v),w=function(t){return y=d(y,t)},b=function(t){return t.replace(/[|\\{()[^$+*?.-]/g,"\\$&")},M=function(t,n){void 0===n&&(n=2),t=String(t);while(t.length<n)t="0"+t;return t},_={D:function(t){return String(t.getDate())},DD:function(t){return M(t.getDate())},Do:function(t,n){return n.DoFn(t.getDate())},d:function(t){return String(t.getDay())},dd:function(t){return M(t.getDay())},ddd:function(t,n){return n.dayNamesShort[t.getDay()]},dddd:function(t,n){return n.dayNames[t.getDay()]},M:function(t){return String(t.getMonth()+1)},MM:function(t){return M(t.getMonth()+1)},MMM:function(t,n){return n.monthNamesShort[t.getMonth()]},MMMM:function(t,n){return n.monthNames[t.getMonth()]},YY:function(t){return M(String(t.getFullYear()),4).substr(2)},YYYY:function(t){return M(t.getFullYear(),4)},h:function(t){return String(t.getHours()%12||12)},hh:function(t){return M(t.getHours()%12||12)},H:function(t){return String(t.getHours())},HH:function(t){return M(t.getHours())},m:function(t){return String(t.getMinutes())},mm:function(t){return M(t.getMinutes())},s:function(t){return String(t.getSeconds())},ss:function(t){return M(t.getSeconds())},S:function(t){return String(Math.round(t.getMilliseconds()/100))},SS:function(t){return M(Math.round(t.getMilliseconds()/10),2)},SSS:function(t){return M(t.getMilliseconds(),3)},a:function(t,n){return t.getHours()<12?n.amPm[0]:n.amPm[1]},A:function(t,n){return t.getHours()<12?n.amPm[0].toUpperCase():n.amPm[1].toUpperCase()},ZZ:function(t){var n=t.getTimezoneOffset();return(n>0?"-":"+")+M(100*Math.floor(Math.abs(n)/60)+Math.abs(n)%60,4)},Z:function(t){var n=t.getTimezoneOffset();return(n>0?"-":"+")+M(Math.floor(Math.abs(n)/60),2)+":"+M(Math.abs(n)%60,2)}},E=function(t){return+t-1},x=[null,o],N=[null,s],S=["isPm",s,function(t,n){var e=t.toLowerCase();return e===n.amPm[0]?0:e===n.amPm[1]?1:null}],T=["timezoneOffset","[^\\s]*?[\\+\\-]\\d\\d:?\\d\\d|[^\\s]*?Z?",function(t){var n=(t+"").match(/([+-]|\d\d)/gi);if(n){var e=60*+n[1]+parseInt(n[2],10);return"+"===n[0]?e:-e}return 0}],A={D:["day",o],DD:["day",i],Do:["day",o+s,function(t){return parseInt(t,10)}],M:["month",o,E],MM:["month",i,E],YY:["year",i,function(t){var n=new Date,e=+(""+n.getFullYear()).substr(0,2);return+(""+(+t>68?e-1:e)+t)}],h:["hour",o,void 0,"isPm"],hh:["hour",i,void 0,"isPm"],H:["hour",o],HH:["hour",i],m:["minute",o],mm:["minute",i],s:["second",o],ss:["second",i],YYYY:["year",a],S:["millisecond","\\d",function(t){return 100*+t}],SS:["millisecond",i,function(t){return 10*+t}],SSS:["millisecond",u],d:x,dd:x,ddd:N,dddd:N,MMM:["month",s,h("monthNamesShort")],MMMM:["month",s,h("monthNames")],a:S,A:S,ZZ:T,Z:T},I={default:"ddd MMM DD YYYY HH:mm:ss",shortDate:"M/D/YY",mediumDate:"MMM D, YYYY",longDate:"MMMM D, YYYY",fullDate:"dddd, MMMM D, YYYY",isoDate:"YYYY-MM-DD",isoDateTime:"YYYY-MM-DDTHH:mm:ssZ",shortTime:"HH:mm",mediumTime:"HH:mm:ss",longTime:"HH:mm:ss.SSS"},O=function(t){return d(I,t)},C=function(t,n,e){if(void 0===n&&(n=I["default"]),void 0===e&&(e={}),"number"===typeof t&&(t=new Date(t)),"[object Date]"!==Object.prototype.toString.call(t)||isNaN(t.getTime()))throw new Error("Invalid Date pass to format");n=I[n]||n;var o=[];n=n.replace(c,(function(t,n){return o.push(n),"@@@"}));var i=d(d({},y),e);return n=n.replace(r,(function(n){return _[n](t,i)})),n.replace(/@@@/g,(function(){return o.shift()}))};function j(t,n,e){if(void 0===e&&(e={}),"string"!==typeof n)throw new Error("Invalid format in fecha parse");if(n=I[n]||n,t.length>1e3)return null;var o=new Date,i={year:o.getFullYear(),month:0,day:1,hour:0,minute:0,second:0,millisecond:0,isPm:null,timezoneOffset:null},u=[],a=[],s=n.replace(c,(function(t,n){return a.push(b(n)),"@@@"})),f={},h={};s=b(s).replace(r,(function(t){var n=A[t],e=n[0],r=n[1],o=n[3];if(f[e])throw new Error("Invalid format. "+e+" specified twice in format");return f[e]=!0,o&&(h[o]=!0),u.push(n),"("+r+")"})),Object.keys(h).forEach((function(t){if(!f[t])throw new Error("Invalid format. "+t+" is required in specified format")})),s=s.replace(/@@@/g,(function(){return a.shift()}));var l=t.match(new RegExp(s,"i"));if(!l)return null;for(var g,p=d(d({},y),e),m=1;m<l.length;m++){var v=u[m-1],w=v[0],M=v[2],_=M?M(l[m],p):+l[m];if(null==_)return null;i[w]=_}if(1===i.isPm&&null!=i.hour&&12!==+i.hour?i.hour=+i.hour+12:0===i.isPm&&12===+i.hour&&(i.hour=0),null==i.timezoneOffset){g=new Date(i.year,i.month,i.day,i.hour,i.minute,i.second,i.millisecond);for(var E=[["month","getMonth"],["day","getDate"],["hour","getHours"],["minute","getMinutes"],["second","getSeconds"]],x=(m=0,E.length);m<x;m++)if(f[E[m][0]]&&i[E[m][0]]!==g[E[m][1]]())return null}else if(g=new Date(Date.UTC(i.year,i.month,i.day,i.hour,i.minute-i.timezoneOffset,i.second,i.millisecond)),i.month>11||i.month<0||i.day>31||i.day<1||i.hour>23||i.hour<0||i.minute>59||i.minute<0||i.second>59||i.second<0)return null;return g}var R={format:C,parse:j,defaultI18n:v,setGlobalDateI18n:w,setGlobalDateMasks:O};n["default"]=R},1979:function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.Metadata=void 0;var r=e("c5f4"),o=function(){function t(t,n){this.key=t,this.value=n}return t.prototype.toString=function(){return this.key===r.NAMED_TAG?"named: "+this.value.toString()+" ":"tagged: { key:"+this.key.toString()+", value: "+this.value+" }"},t}();n.Metadata=o},"1aef":function(t,n){var e=[],r=[],o="insert-css: You need to provide a CSS string. Usage: insertCss(cssString[, options]).";function i(t,n){if(n=n||{},void 0===t)throw new Error(o);var i,a=!0===n.prepend?"prepend":"append",s=void 0!==n.container?n.container:document.querySelector("head"),c=e.indexOf(s);return-1===c&&(c=e.push(s)-1,r[c]={}),void 0!==r[c]&&void 0!==r[c][a]?i=r[c][a]:(i=r[c][a]=u(),"prepend"===a?s.insertBefore(i,s.childNodes[0]):s.appendChild(i)),65279===t.charCodeAt(0)&&(t=t.substr(1,t.length)),i.styleSheet?i.styleSheet.cssText+=t:i.textContent+=t,i}function u(){var t=document.createElement("style");return t.setAttribute("type","text/css"),t}t.exports=i,t.exports.insertCss=i},"1d89":function(t,n,e){"use strict";e.r(n),function(t){e.d(n,"BRIDGE_GRAPH_NAME",(function(){return a})),e.d(n,"GraphType",(function(){return f})),e.d(n,"HierarchyNodeType",(function(){return h})),e.d(n,"InclusionType",(function(){return c})),e.d(n,"LAYOUT_CONFIG",(function(){return r})),e.d(n,"NodeType",(function(){return s})),e.d(n,"ROOT_NAME",(function(){return u})),e.d(n,"buildGraph",(function(){return us})),e.d(n,"flatGraph",(function(){return as})),e.d(n,"getEdges",(function(){return ss})),e.d(n,"mergeConfig",(function(){return o}));const r={graph:{meta:{rankDir:"TB",nodeSep:50,rankSep:50,edgeSep:5,align:void 0}},subScene:{meta:{paddingTop:20,paddingBottom:20,paddingLeft:20,paddingRight:20,labelHeight:20}},nodeSize:{meta:{width:100,maxLabelWidth:0,height:20},node:{width:80,height:20,labelOffset:10,maxLabelWidth:40},bridge:{width:5,height:5,radius:2,labelOffset:0}}};function o(t={},n=r){var e,o,i,u;const a=JSON.parse(JSON.stringify(n)),s=(null===(e=null==t?void 0:t.graph)||void 0===e?void 0:e.meta)||{},c=(null===(o=null==t?void 0:t.subScene)||void 0===o?void 0:o.meta)||{},f=(null===(i=null==t?void 0:t.nodeSize)||void 0===i?void 0:i.meta)||{},h=(null===(u=null==t?void 0:t.nodeSize)||void 0===u?void 0:u.node)||{},d=a.nodeSize.bridge;return{graph:{meta:Object.assign(a.graph.meta,s)},subScene:{meta:Object.assign(a.subScene.meta,c)},nodeSize:{meta:Object.assign(a.nodeSize.meta,f),node:Object.assign(a.nodeSize.node,h),bridge:d}}}function i(t){return`◬${t}◬`}const u=i("ROOT"),a=i("BRIDGE_GRAPH");var s,c,f,h;!function(t){t[t.META=0]="META",t[t.NODE=1]="NODE",t[t.BRIDGE=2]="BRIDGE"}(s||(s={})),function(t){t[t.INCLUDE=0]="INCLUDE",t[t.EXCLUDE=1]="EXCLUDE",t[t.UNSPECIFIED=2]="UNSPECIFIED"}(c||(c={})),function(t){t[t.META=0]="META",t[t.CORE=1]="CORE",t[t.BRIDGE=2]="BRIDGE"}(f||(f={})),function(t){t[t.META=0]="META",t[t.OP=1]="OP",t[t.SERIES=2]="SERIES"}(h||(h={}));var d="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof t?t:"undefined"!=typeof self?self:{};function l(t,n){return t(n={exports:{}},n.exports),n.exports}var g=function(){this.__data__=[],this.size=0},p=function(t,n){return t===n||t!=t&&n!=n},m=function(t,n){for(var e=t.length;e--;)if(p(t[e][0],n))return e;return-1},v=Array.prototype.splice,y=function(t){var n=this.__data__,e=m(n,t);return!(e<0)&&(e==n.length-1?n.pop():v.call(n,e,1),--this.size,!0)},w=function(t){var n=this.__data__,e=m(n,t);return e<0?void 0:n[e][1]},b=function(t){return m(this.__data__,t)>-1},M=function(t,n){var e=this.__data__,r=m(e,t);return r<0?(++this.size,e.push([t,n])):e[r][1]=n,this};function _(t){var n=-1,e=null==t?0:t.length;for(this.clear();++n<e;){var r=t[n];this.set(r[0],r[1])}}_.prototype.clear=g,_.prototype.delete=y,_.prototype.get=w,_.prototype.has=b,_.prototype.set=M;var E,x=_,N=function(){this.__data__=new x,this.size=0},S=function(t){var n=this.__data__,e=n.delete(t);return this.size=n.size,e},T=function(t){return this.__data__.get(t)},A=function(t){return this.__data__.has(t)},I="object"==typeof d&&d&&d.Object===Object&&d,O="object"==typeof self&&self&&self.Object===Object&&self,C=I||O||Function("return this")(),j=C.Symbol,R=Object.prototype,k=R.hasOwnProperty,P=R.toString,D=j?j.toStringTag:void 0,B=function(t){var n=k.call(t,D),e=t[D];try{t[D]=void 0;var r=!0}catch(t){}var o=P.call(t);return r&&(n?t[D]=e:delete t[D]),o},L=Object.prototype.toString,G=function(t){return L.call(t)},q=j?j.toStringTag:void 0,F=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":q&&q in Object(t)?B(t):G(t)},U=function(t){var n=typeof t;return null!=t&&("object"==n||"function"==n)},V=function(t){if(!U(t))return!1;var n=F(t);return"[object Function]"==n||"[object GeneratorFunction]"==n||"[object AsyncFunction]"==n||"[object Proxy]"==n},z=C["__core-js_shared__"],W=(E=/[^.]+$/.exec(z&&z.keys&&z.keys.IE_PROTO||""))?"Symbol(src)_1."+E:"",Y=function(t){return!!W&&W in t},H=Function.prototype.toString,$=function(t){if(null!=t){try{return H.call(t)}catch(t){}try{return t+""}catch(t){}}return""},J=/^\[object .+?Constructor\]$/,K=Function.prototype,Q=Object.prototype,Z=K.toString,X=Q.hasOwnProperty,tt=RegExp("^"+Z.call(X).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),nt=function(t){return!(!U(t)||Y(t))&&(V(t)?tt:J).test($(t))},et=function(t,n){return null==t?void 0:t[n]},rt=function(t,n){var e=et(t,n);return nt(e)?e:void 0},ot=rt(C,"Map"),it=rt(Object,"create"),ut=function(){this.__data__=it?it(null):{},this.size=0},at=function(t){var n=this.has(t)&&delete this.__data__[t];return this.size-=n?1:0,n},st=Object.prototype.hasOwnProperty,ct=function(t){var n=this.__data__;if(it){var e=n[t];return"__lodash_hash_undefined__"===e?void 0:e}return st.call(n,t)?n[t]:void 0},ft=Object.prototype.hasOwnProperty,ht=function(t){var n=this.__data__;return it?void 0!==n[t]:ft.call(n,t)},dt=function(t,n){var e=this.__data__;return this.size+=this.has(t)?0:1,e[t]=it&&void 0===n?"__lodash_hash_undefined__":n,this};function lt(t){var n=-1,e=null==t?0:t.length;for(this.clear();++n<e;){var r=t[n];this.set(r[0],r[1])}}lt.prototype.clear=ut,lt.prototype.delete=at,lt.prototype.get=ct,lt.prototype.has=ht,lt.prototype.set=dt;var gt=lt,pt=function(){this.size=0,this.__data__={hash:new gt,map:new(ot||x),string:new gt}},mt=function(t){var n=typeof t;return"string"==n||"number"==n||"symbol"==n||"boolean"==n?"__proto__"!==t:null===t},vt=function(t,n){var e=t.__data__;return mt(n)?e["string"==typeof n?"string":"hash"]:e.map},yt=function(t){var n=vt(this,t).delete(t);return this.size-=n?1:0,n},wt=function(t){return vt(this,t).get(t)},bt=function(t){return vt(this,t).has(t)},Mt=function(t,n){var e=vt(this,t),r=e.size;return e.set(t,n),this.size+=e.size==r?0:1,this};function _t(t){var n=-1,e=null==t?0:t.length;for(this.clear();++n<e;){var r=t[n];this.set(r[0],r[1])}}_t.prototype.clear=pt,_t.prototype.delete=yt,_t.prototype.get=wt,_t.prototype.has=bt,_t.prototype.set=Mt;var Et=_t,xt=function(t,n){var e=this.__data__;if(e instanceof x){var r=e.__data__;if(!ot||r.length<199)return r.push([t,n]),this.size=++e.size,this;e=this.__data__=new Et(r)}return e.set(t,n),this.size=e.size,this};function Nt(t){var n=this.__data__=new x(t);this.size=n.size}Nt.prototype.clear=N,Nt.prototype.delete=S,Nt.prototype.get=T,Nt.prototype.has=A,Nt.prototype.set=xt;var St=Nt,Tt=function(t,n){for(var e=-1,r=null==t?0:t.length;++e<r&&!1!==n(t[e],e,t););return t},At=function(){try{var t=rt(Object,"defineProperty");return t({},"",{}),t}catch(t){}}(),It=function(t,n,e){"__proto__"==n&&At?At(t,n,{configurable:!0,enumerable:!0,value:e,writable:!0}):t[n]=e},Ot=Object.prototype.hasOwnProperty,Ct=function(t,n,e){var r=t[n];Ot.call(t,n)&&p(r,e)&&(void 0!==e||n in t)||It(t,n,e)},jt=function(t,n,e,r){var o=!e;e||(e={});for(var i=-1,u=n.length;++i<u;){var a=n[i],s=r?r(e[a],t[a],a,e,t):void 0;void 0===s&&(s=t[a]),o?It(e,a,s):Ct(e,a,s)}return e},Rt=function(t,n){for(var e=-1,r=Array(t);++e<t;)r[e]=n(e);return r},kt=function(t){return null!=t&&"object"==typeof t},Pt=function(t){return kt(t)&&"[object Arguments]"==F(t)},Dt=Object.prototype,Bt=Dt.hasOwnProperty,Lt=Dt.propertyIsEnumerable,Gt=Pt(function(){return arguments}())?Pt:function(t){return kt(t)&&Bt.call(t,"callee")&&!Lt.call(t,"callee")},qt=Array.isArray,Ft=function(){return!1},Ut=l((function(t,n){var e=n&&!n.nodeType&&n,r=e&&t&&!t.nodeType&&t,o=r&&r.exports===e?C.Buffer:void 0,i=(o?o.isBuffer:void 0)||Ft;t.exports=i})),Vt=/^(?:0|[1-9]\d*)$/,zt=function(t,n){var e=typeof t;return!!(n=null==n?9007199254740991:n)&&("number"==e||"symbol"!=e&&Vt.test(t))&&t>-1&&t%1==0&&t<n},Wt=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=9007199254740991},Yt={};Yt["[object Float32Array]"]=Yt["[object Float64Array]"]=Yt["[object Int8Array]"]=Yt["[object Int16Array]"]=Yt["[object Int32Array]"]=Yt["[object Uint8Array]"]=Yt["[object Uint8ClampedArray]"]=Yt["[object Uint16Array]"]=Yt["[object Uint32Array]"]=!0,Yt["[object Arguments]"]=Yt["[object Array]"]=Yt["[object ArrayBuffer]"]=Yt["[object Boolean]"]=Yt["[object DataView]"]=Yt["[object Date]"]=Yt["[object Error]"]=Yt["[object Function]"]=Yt["[object Map]"]=Yt["[object Number]"]=Yt["[object Object]"]=Yt["[object RegExp]"]=Yt["[object Set]"]=Yt["[object String]"]=Yt["[object WeakMap]"]=!1;var Ht=function(t){return kt(t)&&Wt(t.length)&&!!Yt[F(t)]},$t=function(t){return function(n){return t(n)}},Jt=l((function(t,n){var e=n&&!n.nodeType&&n,r=e&&t&&!t.nodeType&&t,o=r&&r.exports===e&&I.process,i=function(){try{var t=r&&r.require&&r.require("util").types;return t||o&&o.binding&&o.binding("util")}catch(t){}}();t.exports=i})),Kt=Jt&&Jt.isTypedArray,Qt=Kt?$t(Kt):Ht,Zt=Object.prototype.hasOwnProperty,Xt=function(t,n){var e=qt(t),r=!e&&Gt(t),o=!e&&!r&&Ut(t),i=!e&&!r&&!o&&Qt(t),u=e||r||o||i,a=u?Rt(t.length,String):[],s=a.length;for(var c in t)!n&&!Zt.call(t,c)||u&&("length"==c||o&&("offset"==c||"parent"==c)||i&&("buffer"==c||"byteLength"==c||"byteOffset"==c)||zt(c,s))||a.push(c);return a},tn=Object.prototype,nn=function(t){var n=t&&t.constructor;return t===("function"==typeof n&&n.prototype||tn)},en=function(t,n){return function(e){return t(n(e))}},rn=en(Object.keys,Object),on=Object.prototype.hasOwnProperty,un=function(t){if(!nn(t))return rn(t);var n=[];for(var e in Object(t))on.call(t,e)&&"constructor"!=e&&n.push(e);return n},an=function(t){return null!=t&&Wt(t.length)&&!V(t)},sn=function(t){return an(t)?Xt(t):un(t)},cn=function(t,n){return t&&jt(n,sn(n),t)},fn=function(t){var n=[];if(null!=t)for(var e in Object(t))n.push(e);return n},hn=Object.prototype.hasOwnProperty,dn=function(t){if(!U(t))return fn(t);var n=nn(t),e=[];for(var r in t)("constructor"!=r||!n&&hn.call(t,r))&&e.push(r);return e},ln=function(t){return an(t)?Xt(t,!0):dn(t)},gn=function(t,n){return t&&jt(n,ln(n),t)},pn=l((function(t,n){var e=n&&!n.nodeType&&n,r=e&&t&&!t.nodeType&&t,o=r&&r.exports===e?C.Buffer:void 0,i=o?o.allocUnsafe:void 0;t.exports=function(t,n){if(n)return t.slice();var e=t.length,r=i?i(e):new t.constructor(e);return t.copy(r),r}})),mn=function(t,n){var e=-1,r=t.length;for(n||(n=Array(r));++e<r;)n[e]=t[e];return n},vn=function(t,n){for(var e=-1,r=null==t?0:t.length,o=0,i=[];++e<r;){var u=t[e];n(u,e,t)&&(i[o++]=u)}return i},yn=function(){return[]},wn=Object.prototype.propertyIsEnumerable,bn=Object.getOwnPropertySymbols,Mn=bn?function(t){return null==t?[]:(t=Object(t),vn(bn(t),(function(n){return wn.call(t,n)})))}:yn,_n=function(t,n){return jt(t,Mn(t),n)},En=function(t,n){for(var e=-1,r=n.length,o=t.length;++e<r;)t[o+e]=n[e];return t},xn=en(Object.getPrototypeOf,Object),Nn=Object.getOwnPropertySymbols?function(t){for(var n=[];t;)En(n,Mn(t)),t=xn(t);return n}:yn,Sn=function(t,n){return jt(t,Nn(t),n)},Tn=function(t,n,e){var r=n(t);return qt(t)?r:En(r,e(t))},An=function(t){return Tn(t,sn,Mn)},In=function(t){return Tn(t,ln,Nn)},On=rt(C,"DataView"),Cn=rt(C,"Promise"),jn=rt(C,"Set"),Rn=rt(C,"WeakMap"),kn=$(On),Pn=$(ot),Dn=$(Cn),Bn=$(jn),Ln=$(Rn),Gn=F;(On&&"[object DataView]"!=Gn(new On(new ArrayBuffer(1)))||ot&&"[object Map]"!=Gn(new ot)||Cn&&"[object Promise]"!=Gn(Cn.resolve())||jn&&"[object Set]"!=Gn(new jn)||Rn&&"[object WeakMap]"!=Gn(new Rn))&&(Gn=function(t){var n=F(t),e="[object Object]"==n?t.constructor:void 0,r=e?$(e):"";if(r)switch(r){case kn:return"[object DataView]";case Pn:return"[object Map]";case Dn:return"[object Promise]";case Bn:return"[object Set]";case Ln:return"[object WeakMap]"}return n});var qn=Gn,Fn=Object.prototype.hasOwnProperty,Un=function(t){var n=t.length,e=new t.constructor(n);return n&&"string"==typeof t[0]&&Fn.call(t,"index")&&(e.index=t.index,e.input=t.input),e},Vn=C.Uint8Array,zn=function(t){var n=new t.constructor(t.byteLength);return new Vn(n).set(new Vn(t)),n},Wn=function(t,n){var e=n?zn(t.buffer):t.buffer;return new t.constructor(e,t.byteOffset,t.byteLength)},Yn=/\w*$/,Hn=function(t){var n=new t.constructor(t.source,Yn.exec(t));return n.lastIndex=t.lastIndex,n},$n=j?j.prototype:void 0,Jn=$n?$n.valueOf:void 0,Kn=function(t){return Jn?Object(Jn.call(t)):{}},Qn=function(t,n){var e=n?zn(t.buffer):t.buffer;return new t.constructor(e,t.byteOffset,t.length)},Zn=function(t,n,e){var r=t.constructor;switch(n){case"[object ArrayBuffer]":return zn(t);case"[object Boolean]":case"[object Date]":return new r(+t);case"[object DataView]":return Wn(t,e);case"[object Float32Array]":case"[object Float64Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return Qn(t,e);case"[object Map]":return new r;case"[object Number]":case"[object String]":return new r(t);case"[object RegExp]":return Hn(t);case"[object Set]":return new r;case"[object Symbol]":return Kn(t)}},Xn=Object.create,te=function(){function t(){}return function(n){if(!U(n))return{};if(Xn)return Xn(n);t.prototype=n;var e=new t;return t.prototype=void 0,e}}(),ne=function(t){return"function"!=typeof t.constructor||nn(t)?{}:te(xn(t))},ee=function(t){return kt(t)&&"[object Map]"==qn(t)},re=Jt&&Jt.isMap,oe=re?$t(re):ee,ie=function(t){return kt(t)&&"[object Set]"==qn(t)},ue=Jt&&Jt.isSet,ae=ue?$t(ue):ie,se={};se["[object Arguments]"]=se["[object Array]"]=se["[object ArrayBuffer]"]=se["[object DataView]"]=se["[object Boolean]"]=se["[object Date]"]=se["[object Float32Array]"]=se["[object Float64Array]"]=se["[object Int8Array]"]=se["[object Int16Array]"]=se["[object Int32Array]"]=se["[object Map]"]=se["[object Number]"]=se["[object Object]"]=se["[object RegExp]"]=se["[object Set]"]=se["[object String]"]=se["[object Symbol]"]=se["[object Uint8Array]"]=se["[object Uint8ClampedArray]"]=se["[object Uint16Array]"]=se["[object Uint32Array]"]=!0,se["[object Error]"]=se["[object Function]"]=se["[object WeakMap]"]=!1;var ce=function t(n,e,r,o,i,u){var a,s=1&e,c=2&e,f=4&e;if(r&&(a=i?r(n,o,i,u):r(n)),void 0!==a)return a;if(!U(n))return n;var h=qt(n);if(h){if(a=Un(n),!s)return mn(n,a)}else{var d=qn(n),l="[object Function]"==d||"[object GeneratorFunction]"==d;if(Ut(n))return pn(n,s);if("[object Object]"==d||"[object Arguments]"==d||l&&!i){if(a=c||l?{}:ne(n),!s)return c?Sn(n,gn(a,n)):_n(n,cn(a,n))}else{if(!se[d])return i?n:{};a=Zn(n,d,s)}}u||(u=new St);var g=u.get(n);if(g)return g;u.set(n,a),ae(n)?n.forEach((function(o){a.add(t(o,e,r,o,n,u))})):oe(n)&&n.forEach((function(o,i){a.set(i,t(o,e,r,i,n,u))}));var p=h?void 0:(f?c?In:An:c?ln:sn)(n);return Tt(p||n,(function(o,i){p&&(o=n[i=o]),Ct(a,i,t(o,e,r,i,n,u))})),a},fe=function(t){return ce(t,4)},he=function(t){return function(){return t}},de=function(t){return function(n,e,r){for(var o=-1,i=Object(n),u=r(n),a=u.length;a--;){var s=u[t?a:++o];if(!1===e(i[s],s,i))break}return n}}(),le=function(t,n){return t&&de(t,n,sn)},ge=function(t,n){return function(e,r){if(null==e)return e;if(!an(e))return t(e,r);for(var o=e.length,i=n?o:-1,u=Object(e);(n?i--:++i<o)&&!1!==r(u[i],i,u););return e}}(le),pe=function(t){return t},me=function(t){return"function"==typeof t?t:pe},ve=function(t,n){return(qt(t)?Tt:ge)(t,me(n))},ye=ve,we=function(t,n){var e=[];return ge(t,(function(t,r,o){n(t,r,o)&&e.push(t)})),e},be=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this},Me=function(t){return this.__data__.has(t)};function _e(t){var n=-1,e=null==t?0:t.length;for(this.__data__=new Et;++n<e;)this.add(t[n])}_e.prototype.add=_e.prototype.push=be,_e.prototype.has=Me;var Ee=_e,xe=function(t,n){for(var e=-1,r=null==t?0:t.length;++e<r;)if(n(t[e],e,t))return!0;return!1},Ne=function(t,n){return t.has(n)},Se=function(t,n,e,r,o,i){var u=1&e,a=t.length,s=n.length;if(a!=s&&!(u&&s>a))return!1;var c=i.get(t),f=i.get(n);if(c&&f)return c==n&&f==t;var h=-1,d=!0,l=2&e?new Ee:void 0;for(i.set(t,n),i.set(n,t);++h<a;){var g=t[h],p=n[h];if(r)var m=u?r(p,g,h,n,t,i):r(g,p,h,t,n,i);if(void 0!==m){if(m)continue;d=!1;break}if(l){if(!xe(n,(function(t,n){if(!Ne(l,n)&&(g===t||o(g,t,e,r,i)))return l.push(n)}))){d=!1;break}}else if(g!==p&&!o(g,p,e,r,i)){d=!1;break}}return i.delete(t),i.delete(n),d},Te=function(t){var n=-1,e=Array(t.size);return t.forEach((function(t,r){e[++n]=[r,t]})),e},Ae=function(t){var n=-1,e=Array(t.size);return t.forEach((function(t){e[++n]=t})),e},Ie=j?j.prototype:void 0,Oe=Ie?Ie.valueOf:void 0,Ce=function(t,n,e,r,o,i,u){switch(e){case"[object DataView]":if(t.byteLength!=n.byteLength||t.byteOffset!=n.byteOffset)return!1;t=t.buffer,n=n.buffer;case"[object ArrayBuffer]":return!(t.byteLength!=n.byteLength||!i(new Vn(t),new Vn(n)));case"[object Boolean]":case"[object Date]":case"[object Number]":return p(+t,+n);case"[object Error]":return t.name==n.name&&t.message==n.message;case"[object RegExp]":case"[object String]":return t==n+"";case"[object Map]":var a=Te;case"[object Set]":var s=1&r;if(a||(a=Ae),t.size!=n.size&&!s)return!1;var c=u.get(t);if(c)return c==n;r|=2,u.set(t,n);var f=Se(a(t),a(n),r,o,i,u);return u.delete(t),f;case"[object Symbol]":if(Oe)return Oe.call(t)==Oe.call(n)}return!1},je=Object.prototype.hasOwnProperty,Re=function(t,n,e,r,o,i){var u=1&e,a=An(t),s=a.length;if(s!=An(n).length&&!u)return!1;for(var c=s;c--;){var f=a[c];if(!(u?f in n:je.call(n,f)))return!1}var h=i.get(t),d=i.get(n);if(h&&d)return h==n&&d==t;var l=!0;i.set(t,n),i.set(n,t);for(var g=u;++c<s;){var p=t[f=a[c]],m=n[f];if(r)var v=u?r(m,p,f,n,t,i):r(p,m,f,t,n,i);if(!(void 0===v?p===m||o(p,m,e,r,i):v)){l=!1;break}g||(g="constructor"==f)}if(l&&!g){var y=t.constructor,w=n.constructor;y==w||!("constructor"in t)||!("constructor"in n)||"function"==typeof y&&y instanceof y&&"function"==typeof w&&w instanceof w||(l=!1)}return i.delete(t),i.delete(n),l},ke=Object.prototype.hasOwnProperty,Pe=function(t,n,e,r,o,i){var u=qt(t),a=qt(n),s=u?"[object Array]":qn(t),c=a?"[object Array]":qn(n),f="[object Object]"==(s="[object Arguments]"==s?"[object Object]":s),h="[object Object]"==(c="[object Arguments]"==c?"[object Object]":c),d=s==c;if(d&&Ut(t)){if(!Ut(n))return!1;u=!0,f=!1}if(d&&!f)return i||(i=new St),u||Qt(t)?Se(t,n,e,r,o,i):Ce(t,n,s,e,r,o,i);if(!(1&e)){var l=f&&ke.call(t,"__wrapped__"),g=h&&ke.call(n,"__wrapped__");if(l||g){var p=l?t.value():t,m=g?n.value():n;return i||(i=new St),o(p,m,e,r,i)}}return!!d&&(i||(i=new St),Re(t,n,e,r,o,i))},De=function t(n,e,r,o,i){return n===e||(null==n||null==e||!kt(n)&&!kt(e)?n!=n&&e!=e:Pe(n,e,r,o,t,i))},Be=function(t,n,e,r){var o=e.length,i=o,u=!r;if(null==t)return!i;for(t=Object(t);o--;){var a=e[o];if(u&&a[2]?a[1]!==t[a[0]]:!(a[0]in t))return!1}for(;++o<i;){var s=(a=e[o])[0],c=t[s],f=a[1];if(u&&a[2]){if(void 0===c&&!(s in t))return!1}else{var h=new St;if(r)var d=r(c,f,s,t,n,h);if(!(void 0===d?De(f,c,3,r,h):d))return!1}}return!0},Le=function(t){return t==t&&!U(t)},Ge=function(t){for(var n=sn(t),e=n.length;e--;){var r=n[e],o=t[r];n[e]=[r,o,Le(o)]}return n},qe=function(t,n){return function(e){return null!=e&&e[t]===n&&(void 0!==n||t in Object(e))}},Fe=function(t){var n=Ge(t);return 1==n.length&&n[0][2]?qe(n[0][0],n[0][1]):function(e){return e===t||Be(e,t,n)}},Ue=function(t){return"symbol"==typeof t||kt(t)&&"[object Symbol]"==F(t)},Ve=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,ze=/^\w*$/,We=function(t,n){if(qt(t))return!1;var e=typeof t;return!("number"!=e&&"symbol"!=e&&"boolean"!=e&&null!=t&&!Ue(t))||ze.test(t)||!Ve.test(t)||null!=n&&t in Object(n)};function Ye(t,n){if("function"!=typeof t||null!=n&&"function"!=typeof n)throw new TypeError("Expected a function");var e=function(){var r=arguments,o=n?n.apply(this,r):r[0],i=e.cache;if(i.has(o))return i.get(o);var u=t.apply(this,r);return e.cache=i.set(o,u)||i,u};return e.cache=new(Ye.Cache||Et),e}Ye.Cache=Et;var He,$e=Ye,Je=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,Ke=/\\(\\)?/g,Qe=function(t){var n=$e(t,(function(t){return 500===e.size&&e.clear(),t})),e=n.cache;return n}((function(t){var n=[];return 46===t.charCodeAt(0)&&n.push(""),t.replace(Je,(function(t,e,r,o){n.push(r?o.replace(Ke,"$1"):e||t)})),n})),Ze=function(t,n){for(var e=-1,r=null==t?0:t.length,o=Array(r);++e<r;)o[e]=n(t[e],e,t);return o},Xe=j?j.prototype:void 0,tr=Xe?Xe.toString:void 0,nr=function t(n){if("string"==typeof n)return n;if(qt(n))return Ze(n,t)+"";if(Ue(n))return tr?tr.call(n):"";var e=n+"";return"0"==e&&1/n==-1/0?"-0":e},er=function(t){return null==t?"":nr(t)},rr=function(t,n){return qt(t)?t:We(t,n)?[t]:Qe(er(t))},or=function(t){if("string"==typeof t||Ue(t))return t;var n=t+"";return"0"==n&&1/t==-1/0?"-0":n},ir=function(t,n){for(var e=0,r=(n=rr(n,t)).length;null!=t&&e<r;)t=t[or(n[e++])];return e&&e==r?t:void 0},ur=function(t,n,e){var r=null==t?void 0:ir(t,n);return void 0===r?e:r},ar=function(t,n){return null!=t&&n in Object(t)},sr=function(t,n,e){for(var r=-1,o=(n=rr(n,t)).length,i=!1;++r<o;){var u=or(n[r]);if(!(i=null!=t&&e(t,u)))break;t=t[u]}return i||++r!=o?i:!!(o=null==t?0:t.length)&&Wt(o)&&zt(u,o)&&(qt(t)||Gt(t))},cr=function(t,n){return null!=t&&sr(t,n,ar)},fr=function(t,n){return We(t)&&Le(n)?qe(or(t),n):function(e){var r=ur(e,t);return void 0===r&&r===n?cr(e,t):De(n,r,3)}},hr=function(t){return function(n){return null==n?void 0:n[t]}},dr=function(t){return function(n){return ir(n,t)}},lr=function(t){return We(t)?hr(or(t)):dr(t)},gr=function(t){return"function"==typeof t?t:null==t?pe:"object"==typeof t?qt(t)?fr(t[0],t[1]):Fe(t):lr(t)},pr=function(t,n){return(qt(t)?vn:we)(t,gr(n))},mr=Object.prototype.hasOwnProperty,vr=function(t,n){return null!=t&&mr.call(t,n)},yr=function(t,n){return null!=t&&sr(t,n,vr)},wr=Object.prototype.hasOwnProperty,br=function(t){if(null==t)return!0;if(an(t)&&(qt(t)||"string"==typeof t||"function"==typeof t.splice||Ut(t)||Qt(t)||Gt(t)))return!t.length;var n=qn(t);if("[object Map]"==n||"[object Set]"==n)return!t.size;if(nn(t))return!un(t).length;for(var e in t)if(wr.call(t,e))return!1;return!0},Mr=function(t){return void 0===t},_r=function(t,n){var e=-1,r=an(t)?Array(t.length):[];return ge(t,(function(t,o,i){r[++e]=n(t,o,i)})),r},Er=function(t,n){return(qt(t)?Ze:_r)(t,gr(n))},xr=function(t,n,e,r){var o=-1,i=null==t?0:t.length;for(r&&i&&(e=t[++o]);++o<i;)e=n(e,t[o],o,t);return e},Nr=function(t,n,e,r,o){return o(t,(function(t,o,i){e=r?(r=!1,t):n(e,t,o,i)})),e},Sr=function(t,n,e){var r=qt(t)?xr:Nr,o=arguments.length<3;return r(t,gr(n),e,o,ge)},Tr=function(t){return"string"==typeof t||!qt(t)&&kt(t)&&"[object String]"==F(t)},Ar=hr("length"),Ir=RegExp("[\\u200d\\ud800-\\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]"),Or=function(t){return Ir.test(t)},Cr="[\\ud800-\\udfff]",jr="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",Rr="\\ud83c[\\udffb-\\udfff]",kr="[^\\ud800-\\udfff]",Pr="(?:\\ud83c[\\udde6-\\uddff]){2}",Dr="[\\ud800-\\udbff][\\udc00-\\udfff]",Br="(?:"+jr+"|"+Rr+")?",Lr="[\\ufe0e\\ufe0f]?"+Br+"(?:\\u200d(?:"+[kr,Pr,Dr].join("|")+")[\\ufe0e\\ufe0f]?"+Br+")*",Gr="(?:"+[kr+jr+"?",jr,Pr,Dr,Cr].join("|")+")",qr=RegExp(Rr+"(?="+Rr+")|"+Gr+Lr,"g"),Fr=function(t){for(var n=qr.lastIndex=0;qr.test(t);)++n;return n},Ur=function(t){return Or(t)?Fr(t):Ar(t)},Vr=function(t){if(null==t)return 0;if(an(t))return Tr(t)?Ur(t):t.length;var n=qn(t);return"[object Map]"==n||"[object Set]"==n?t.size:un(t).length},zr=function(t,n,e){var r=qt(t),o=r||Ut(t)||Qt(t);if(n=gr(n),null==e){var i=t&&t.constructor;e=o?r?new i:[]:U(t)&&V(i)?te(xn(t)):{}}return(o?Tt:le)(t,(function(t,r,o){return n(e,t,r,o)})),e},Wr=j?j.isConcatSpreadable:void 0,Yr=function(t){return qt(t)||Gt(t)||!!(Wr&&t&&t[Wr])},Hr=function t(n,e,r,o,i){var u=-1,a=n.length;for(r||(r=Yr),i||(i=[]);++u<a;){var s=n[u];e>0&&r(s)?e>1?t(s,e-1,r,o,i):En(i,s):o||(i[i.length]=s)}return i},$r=function(t,n,e){switch(e.length){case 0:return t.call(n);case 1:return t.call(n,e[0]);case 2:return t.call(n,e[0],e[1]);case 3:return t.call(n,e[0],e[1],e[2])}return t.apply(n,e)},Jr=Math.max,Kr=function(t,n,e){return n=Jr(void 0===n?t.length-1:n,0),function(){for(var r=arguments,o=-1,i=Jr(r.length-n,0),u=Array(i);++o<i;)u[o]=r[n+o];o=-1;for(var a=Array(n+1);++o<n;)a[o]=r[o];return a[n]=e(u),$r(t,this,a)}},Qr=At?function(t,n){return At(t,"toString",{configurable:!0,enumerable:!1,value:he(n),writable:!0})}:pe,Zr=Date.now,Xr=function(t){var n=0,e=0;return function(){var r=Zr(),o=16-(r-e);if(e=r,o>0){if(++n>=800)return arguments[0]}else n=0;return t.apply(void 0,arguments)}}(Qr),to=function(t,n){return Xr(Kr(t,n,pe),t+"")},no=function(t,n,e,r){for(var o=t.length,i=e+(r?1:-1);r?i--:++i<o;)if(n(t[i],i,t))return i;return-1},eo=function(t){return t!=t},ro=function(t,n,e){for(var r=e-1,o=t.length;++r<o;)if(t[r]===n)return r;return-1},oo=function(t,n,e){return n==n?ro(t,n,e):no(t,eo,e)},io=function(t,n){return!(null==t||!t.length)&&oo(t,n,0)>-1},uo=function(t,n,e){for(var r=-1,o=null==t?0:t.length;++r<o;)if(e(n,t[r]))return!0;return!1},ao=function(){},so=jn&&1/Ae(new jn([,-0]))[1]==1/0?function(t){return new jn(t)}:ao,co=function(t,n,e){var r=-1,o=io,i=t.length,u=!0,a=[],s=a;if(e)u=!1,o=uo;else if(i>=200){var c=n?null:so(t);if(c)return Ae(c);u=!1,o=Ne,s=new Ee}else s=n?[]:a;t:for(;++r<i;){var f=t[r],h=n?n(f):f;if(f=e||0!==f?f:0,u&&h==h){for(var d=s.length;d--;)if(s[d]===h)continue t;n&&s.push(h),a.push(f)}else o(s,h,e)||(s!==a&&s.push(h),a.push(f))}return a},fo=function(t){return kt(t)&&an(t)},ho=to((function(t){return co(Hr(t,1,fo,!0))})),lo=function(t,n){return Ze(n,(function(n){return t[n]}))},go=function(t){return null==t?[]:lo(t,sn(t))};try{He={clone:fe,constant:he,each:ye,filter:pr,has:yr,isArray:qt,isEmpty:br,isFunction:V,isUndefined:Mr,keys:sn,map:Er,reduce:Sr,size:Vr,transform:zr,union:ho,values:go}}catch(r){}He||(He=window._);var po=He,mo=vo;function vo(t){this._isDirected=!po.has(t,"directed")||t.directed,this._isMultigraph=!!po.has(t,"multigraph")&&t.multigraph,this._isCompound=!!po.has(t,"compound")&&t.compound,this._label=void 0,this._defaultNodeLabelFn=po.constant(void 0),this._defaultEdgeLabelFn=po.constant(void 0),this._nodes={},this._isCompound&&(this._parent={},this._children={},this._children["\0"]={}),this._in={},this._preds={},this._out={},this._sucs={},this._edgeObjs={},this._edgeLabels={}}function yo(t,n){t[n]?t[n]++:t[n]=1}function wo(t,n){--t[n]||delete t[n]}function bo(t,n,e,r){var o=""+n,i=""+e;if(!t&&o>i){var u=o;o=i,i=u}return o+""+i+""+(po.isUndefined(r)?"\0":r)}function Mo(t,n,e,r){var o=""+n,i=""+e;if(!t&&o>i){var u=o;o=i,i=u}var a={v:o,w:i};return r&&(a.name=r),a}function _o(t,n){return bo(t,n.v,n.w,n.name)}vo.prototype._nodeCount=0,vo.prototype._edgeCount=0,vo.prototype.isDirected=function(){return this._isDirected},vo.prototype.isMultigraph=function(){return this._isMultigraph},vo.prototype.isCompound=function(){return this._isCompound},vo.prototype.setGraph=function(t){return this._label=t,this},vo.prototype.graph=function(){return this._label},vo.prototype.setDefaultNodeLabel=function(t){return po.isFunction(t)||(t=po.constant(t)),this._defaultNodeLabelFn=t,this},vo.prototype.nodeCount=function(){return this._nodeCount},vo.prototype.nodes=function(){return po.keys(this._nodes)},vo.prototype.sources=function(){var t=this;return po.filter(this.nodes(),(function(n){return po.isEmpty(t._in[n])}))},vo.prototype.sinks=function(){var t=this;return po.filter(this.nodes(),(function(n){return po.isEmpty(t._out[n])}))},vo.prototype.setNodes=function(t,n){var e=arguments,r=this;return po.each(t,(function(t){e.length>1?r.setNode(t,n):r.setNode(t)})),this},vo.prototype.setNode=function(t,n){return po.has(this._nodes,t)?(arguments.length>1&&(this._nodes[t]=n),this):(this._nodes[t]=arguments.length>1?n:this._defaultNodeLabelFn(t),this._isCompound&&(this._parent[t]="\0",this._children[t]={},this._children["\0"][t]=!0),this._in[t]={},this._preds[t]={},this._out[t]={},this._sucs[t]={},++this._nodeCount,this)},vo.prototype.node=function(t){return this._nodes[t]},vo.prototype.hasNode=function(t){return po.has(this._nodes,t)},vo.prototype.removeNode=function(t){var n=this;if(po.has(this._nodes,t)){var e=function(t){n.removeEdge(n._edgeObjs[t])};delete this._nodes[t],this._isCompound&&(this._removeFromParentsChildList(t),delete this._parent[t],po.each(this.children(t),(function(t){n.setParent(t)})),delete this._children[t]),po.each(po.keys(this._in[t]),e),delete this._in[t],delete this._preds[t],po.each(po.keys(this._out[t]),e),delete this._out[t],delete this._sucs[t],--this._nodeCount}return this},vo.prototype.setParent=function(t,n){if(!this._isCompound)throw new Error("Cannot set parent in a non-compound graph");if(po.isUndefined(n))n="\0";else{for(var e=n+="";!po.isUndefined(e);e=this.parent(e))if(e===t)throw new Error("Setting "+n+" as parent of "+t+" would create a cycle");this.setNode(n)}return this.setNode(t),this._removeFromParentsChildList(t),this._parent[t]=n,this._children[n][t]=!0,this},vo.prototype._removeFromParentsChildList=function(t){delete this._children[this._parent[t]][t]},vo.prototype.parent=function(t){if(this._isCompound){var n=this._parent[t];if("\0"!==n)return n}},vo.prototype.children=function(t){if(po.isUndefined(t)&&(t="\0"),this._isCompound){var n=this._children[t];if(n)return po.keys(n)}else{if("\0"===t)return this.nodes();if(this.hasNode(t))return[]}},vo.prototype.predecessors=function(t){var n=this._preds[t];if(n)return po.keys(n)},vo.prototype.successors=function(t){var n=this._sucs[t];if(n)return po.keys(n)},vo.prototype.neighbors=function(t){var n=this.predecessors(t);if(n)return po.union(n,this.successors(t))},vo.prototype.isLeaf=function(t){return 0===(this.isDirected()?this.successors(t):this.neighbors(t)).length},vo.prototype.filterNodes=function(t){var n=new this.constructor({directed:this._isDirected,multigraph:this._isMultigraph,compound:this._isCompound});n.setGraph(this.graph());var e=this;po.each(this._nodes,(function(e,r){t(r)&&n.setNode(r,e)})),po.each(this._edgeObjs,(function(t){n.hasNode(t.v)&&n.hasNode(t.w)&&n.setEdge(t,e.edge(t))}));var r={};function o(t){var i=e.parent(t);return void 0===i||n.hasNode(i)?(r[t]=i,i):i in r?r[i]:o(i)}return this._isCompound&&po.each(n.nodes(),(function(t){n.setParent(t,o(t))})),n},vo.prototype.setDefaultEdgeLabel=function(t){return po.isFunction(t)||(t=po.constant(t)),this._defaultEdgeLabelFn=t,this},vo.prototype.edgeCount=function(){return this._edgeCount},vo.prototype.edges=function(){return po.values(this._edgeObjs)},vo.prototype.setPath=function(t,n){var e=this,r=arguments;return po.reduce(t,(function(t,o){return r.length>1?e.setEdge(t,o,n):e.setEdge(t,o),o})),this},vo.prototype.setEdge=function(){var t,n,e,r,o=!1,i=arguments[0];"object"==typeof i&&null!==i&&"v"in i?(t=i.v,n=i.w,e=i.name,2===arguments.length&&(r=arguments[1],o=!0)):(t=i,n=arguments[1],e=arguments[3],arguments.length>2&&(r=arguments[2],o=!0)),t=""+t,n=""+n,po.isUndefined(e)||(e=""+e);var u=bo(this._isDirected,t,n,e);if(po.has(this._edgeLabels,u))return o&&(this._edgeLabels[u]=r),this;if(!po.isUndefined(e)&&!this._isMultigraph)throw new Error("Cannot set a named edge when isMultigraph = false");this.setNode(t),this.setNode(n),this._edgeLabels[u]=o?r:this._defaultEdgeLabelFn(t,n,e);var a=Mo(this._isDirected,t,n,e);return t=a.v,n=a.w,Object.freeze(a),this._edgeObjs[u]=a,yo(this._preds[n],t),yo(this._sucs[t],n),this._in[n][u]=a,this._out[t][u]=a,this._edgeCount++,this},vo.prototype.edge=function(t,n,e){var r=1===arguments.length?_o(this._isDirected,arguments[0]):bo(this._isDirected,t,n,e);return this._edgeLabels[r]},vo.prototype.hasEdge=function(t,n,e){var r=1===arguments.length?_o(this._isDirected,arguments[0]):bo(this._isDirected,t,n,e);return po.has(this._edgeLabels,r)},vo.prototype.removeEdge=function(t,n,e){var r=1===arguments.length?_o(this._isDirected,arguments[0]):bo(this._isDirected,t,n,e),o=this._edgeObjs[r];return o&&(t=o.v,n=o.w,delete this._edgeLabels[r],delete this._edgeObjs[r],wo(this._preds[n],t),wo(this._sucs[t],n),delete this._in[n][r],delete this._out[t][r],this._edgeCount--),this},vo.prototype.inEdges=function(t,n){var e=this._in[t];if(e){var r=po.values(e);return n?po.filter(r,(function(t){return t.v===n})):r}},vo.prototype.outEdges=function(t,n){var e=this._out[t];if(e){var r=po.values(e);return n?po.filter(r,(function(t){return t.w===n})):r}},vo.prototype.nodeEdges=function(t,n){var e=this.inEdges(t,n);if(e)return e.concat(this.outEdges(t,n))};var Eo={Graph:mo,version:"2.1.8"},xo={write:function(t){var n={options:{directed:t.isDirected(),multigraph:t.isMultigraph(),compound:t.isCompound()},nodes:No(t),edges:So(t)};return po.isUndefined(t.graph())||(n.value=po.clone(t.graph())),n},read:function(t){var n=new mo(t.options).setGraph(t.value);return po.each(t.nodes,(function(t){n.setNode(t.v,t.value),t.parent&&n.setParent(t.v,t.parent)})),po.each(t.edges,(function(t){n.setEdge({v:t.v,w:t.w,name:t.name},t.value)})),n}};function No(t){return po.map(t.nodes(),(function(n){var e=t.node(n),r=t.parent(n),o={v:n};return po.isUndefined(e)||(o.value=e),po.isUndefined(r)||(o.parent=r),o}))}function So(t){return po.map(t.edges(),(function(n){var e=t.edge(n),r={v:n.v,w:n.w};return po.isUndefined(n.name)||(r.name=n.name),po.isUndefined(e)||(r.value=e),r}))}var To=function(t){var n,e={},r=[];function o(r){po.has(e,r)||(e[r]=!0,n.push(r),po.each(t.successors(r),o),po.each(t.predecessors(r),o))}return po.each(t.nodes(),(function(t){n=[],o(t),n.length&&r.push(n)})),r},Ao=Io;function Io(){this._arr=[],this._keyIndices={}}Io.prototype.size=function(){return this._arr.length},Io.prototype.keys=function(){return this._arr.map((function(t){return t.key}))},Io.prototype.has=function(t){return po.has(this._keyIndices,t)},Io.prototype.priority=function(t){var n=this._keyIndices[t];if(void 0!==n)return this._arr[n].priority},Io.prototype.min=function(){if(0===this.size())throw new Error("Queue underflow");return this._arr[0].key},Io.prototype.add=function(t,n){var e=this._keyIndices;if(t=String(t),!po.has(e,t)){var r=this._arr,o=r.length;return e[t]=o,r.push({key:t,priority:n}),this._decrease(o),!0}return!1},Io.prototype.removeMin=function(){this._swap(0,this._arr.length-1);var t=this._arr.pop();return delete this._keyIndices[t.key],this._heapify(0),t.key},Io.prototype.decrease=function(t,n){var e=this._keyIndices[t];if(n>this._arr[e].priority)throw new Error("New priority is greater than current priority. Key: "+t+" Old: "+this._arr[e].priority+" New: "+n);this._arr[e].priority=n,this._decrease(e)},Io.prototype._heapify=function(t){var n=this._arr,e=2*t,r=e+1,o=t;e<n.length&&(o=n[e].priority<n[o].priority?e:o,r<n.length&&(o=n[r].priority<n[o].priority?r:o),o!==t&&(this._swap(t,o),this._heapify(o)))},Io.prototype._decrease=function(t){for(var n,e=this._arr,r=e[t].priority;0!==t&&!(e[n=t>>1].priority<r);)this._swap(t,n),t=n},Io.prototype._swap=function(t,n){var e=this._arr,r=this._keyIndices,o=e[t],i=e[n];e[t]=i,e[n]=o,r[i.key]=t,r[o.key]=n};var Oo=function(t,n,e,r){return function(t,n,e,r){var o,i,u={},a=new Ao,s=function(t){var n=t.v!==o?t.v:t.w,r=u[n],s=e(t),c=i.distance+s;if(s<0)throw new Error("dijkstra does not allow negative edge weights. Bad edge: "+t+" Weight: "+s);c<r.distance&&(r.distance=c,r.predecessor=o,a.decrease(n,c))};for(t.nodes().forEach((function(t){var e=t===n?0:Number.POSITIVE_INFINITY;u[t]={distance:e},a.add(t,e)}));a.size()>0&&(o=a.removeMin(),(i=u[o]).distance!==Number.POSITIVE_INFINITY);)r(o).forEach(s);return u}(t,String(n),e||Co,r||function(n){return t.outEdges(n)})},Co=po.constant(1),jo=function(t,n,e){return po.transform(t.nodes(),(function(r,o){r[o]=Oo(t,o,n,e)}),{})},Ro=function(t){var n=0,e=[],r={},o=[];function i(u){var a=r[u]={onStack:!0,lowlink:n,index:n++};if(e.push(u),t.successors(u).forEach((function(t){po.has(r,t)?r[t].onStack&&(a.lowlink=Math.min(a.lowlink,r[t].index)):(i(t),a.lowlink=Math.min(a.lowlink,r[t].lowlink))})),a.lowlink===a.index){var s,c=[];do{s=e.pop(),r[s].onStack=!1,c.push(s)}while(u!==s);o.push(c)}}return t.nodes().forEach((function(t){po.has(r,t)||i(t)})),o},ko=function(t){return po.filter(Ro(t),(function(n){return n.length>1||1===n.length&&t.hasEdge(n[0],n[0])}))},Po=function(t,n,e){return function(t,n,e){var r={},o=t.nodes();return o.forEach((function(t){r[t]={},r[t][t]={distance:0},o.forEach((function(n){t!==n&&(r[t][n]={distance:Number.POSITIVE_INFINITY})})),e(t).forEach((function(e){var o=e.v===t?e.w:e.v,i=n(e);r[t][o]={distance:i,predecessor:t}}))})),o.forEach((function(t){var n=r[t];o.forEach((function(e){var i=r[e];o.forEach((function(e){var r=i[t],o=n[e],u=i[e],a=r.distance+o.distance;a<u.distance&&(u.distance=a,u.predecessor=o.predecessor)}))}))})),r}(t,n||Do,e||function(n){return t.outEdges(n)})},Do=po.constant(1),Bo=Lo;function Lo(t){var n={},e={},r=[];if(po.each(t.sinks(),(function o(i){if(po.has(e,i))throw new Go;po.has(n,i)||(e[i]=!0,n[i]=!0,po.each(t.predecessors(i),o),delete e[i],r.push(i))})),po.size(n)!==t.nodeCount())throw new Go;return r}function Go(){}Lo.CycleException=Go,Go.prototype=new Error;var qo=function(t,n,e){po.isArray(n)||(n=[n]);var r=(t.isDirected()?t.successors:t.neighbors).bind(t),o=[],i={};return po.each(n,(function(n){if(!t.hasNode(n))throw new Error("Graph does not have node: "+n);Fo(t,n,"post"===e,i,r,o)})),o};function Fo(t,n,e,r,o,i){po.has(r,n)||(r[n]=!0,e||i.push(n),po.each(o(n),(function(n){Fo(t,n,e,r,o,i)})),e&&i.push(n))}var Uo,Vo={Graph:Eo.Graph,json:xo,alg:{components:To,dijkstra:Oo,dijkstraAll:jo,findCycles:ko,floydWarshall:Po,isAcyclic:function(t){try{Bo(t)}catch(t){if(t instanceof Bo.CycleException)return!1;throw t}return!0},postorder:function(t,n){return qo(t,n,"post")},preorder:function(t,n){return qo(t,n,"pre")},prim:function(t,n){var e,r=new mo,o={},i=new Ao;function u(t){var r=t.v===e?t.w:t.v,u=i.priority(r);if(void 0!==u){var a=n(t);a<u&&(o[r]=e,i.decrease(r,a))}}if(0===t.nodeCount())return r;po.each(t.nodes(),(function(t){i.add(t,Number.POSITIVE_INFINITY),r.setNode(t)})),i.decrease(t.nodes()[0],0);for(var a=!1;i.size()>0;){if(e=i.removeMin(),po.has(o,e))r.setEdge(e,o[e]);else{if(a)throw new Error("Input graph is not connected: "+t);a=!0}t.nodeEdges(e).forEach(u)}return r},tarjan:Ro,topsort:Bo},version:Eo.version};try{Uo=Vo}catch(r){}Uo||(Uo=window.graphlib);var zo,Wo=Uo,Yo=function(t){return ce(t,5)},Ho=function(t,n,e){if(!U(e))return!1;var r=typeof n;return!!("number"==r?an(e)&&zt(n,e.length):"string"==r&&n in e)&&p(e[n],t)},$o=Object.prototype,Jo=$o.hasOwnProperty,Ko=to((function(t,n){t=Object(t);var e=-1,r=n.length,o=r>2?n[2]:void 0;for(o&&Ho(n[0],n[1],o)&&(r=1);++e<r;)for(var i=n[e],u=ln(i),a=-1,s=u.length;++a<s;){var c=u[a],f=t[c];(void 0===f||p(f,$o[c])&&!Jo.call(t,c))&&(t[c]=i[c])}return t})),Qo=function(t){return function(n,e,r){var o=Object(n);if(!an(n)){var i=gr(e);n=sn(n),e=function(t){return i(o[t],t,o)}}var u=t(n,e,r);return u>-1?o[i?n[u]:u]:void 0}},Zo=/^\s+|\s+$/g,Xo=/^[-+]0x[0-9a-f]+$/i,ti=/^0b[01]+$/i,ni=/^0o[0-7]+$/i,ei=parseInt,ri=function(t){if("number"==typeof t)return t;if(Ue(t))return NaN;if(U(t)){var n="function"==typeof t.valueOf?t.valueOf():t;t=U(n)?n+"":n}if("string"!=typeof t)return 0===t?t:+t;t=t.replace(Zo,"");var e=ti.test(t);return e||ni.test(t)?ei(t.slice(2),e?2:8):Xo.test(t)?NaN:+t},oi=function(t){return t?1/0===(t=ri(t))||-1/0===t?17976931348623157e292*(t<0?-1:1):t==t?t:0:0===t?t:0},ii=function(t){var n=oi(t),e=n%1;return n==n?e?n-e:n:0},ui=Math.max,ai=Qo((function(t,n,e){var r=null==t?0:t.length;if(!r)return-1;var o=null==e?0:ii(e);return o<0&&(o=ui(r+o,0)),no(t,gr(n),o)})),si=function(t){return null!=t&&t.length?Hr(t,1):[]},ci=function(t,n){return null==t?t:de(t,me(n),ln)},fi=function(t){var n=null==t?0:t.length;return n?t[n-1]:void 0},hi=function(t,n){var e={};return n=gr(n),le(t,(function(t,r,o){It(e,r,n(t,r,o))})),e},di=function(t,n,e){for(var r=-1,o=t.length;++r<o;){var i=t[r],u=n(i);if(null!=u&&(void 0===a?u==u&&!Ue(u):e(u,a)))var a=u,s=i}return s},li=function(t,n){return t>n},gi=function(t){return t&&t.length?di(t,pe,li):void 0},pi=function(t,n,e){(void 0!==e&&!p(t[n],e)||void 0===e&&!(n in t))&&It(t,n,e)},mi=Function.prototype,vi=Object.prototype,yi=mi.toString,wi=vi.hasOwnProperty,bi=yi.call(Object),Mi=function(t){if(!kt(t)||"[object Object]"!=F(t))return!1;var n=xn(t);if(null===n)return!0;var e=wi.call(n,"constructor")&&n.constructor;return"function"==typeof e&&e instanceof e&&yi.call(e)==bi},_i=function(t,n){if(("constructor"!==n||"function"!=typeof t[n])&&"__proto__"!=n)return t[n]},Ei=function(t){return jt(t,ln(t))},xi=function(t,n,e,r,o,i,u){var a=_i(t,e),s=_i(n,e),c=u.get(s);if(c)pi(t,e,c);else{var f=i?i(a,s,e+"",t,n,u):void 0,h=void 0===f;if(h){var d=qt(s),l=!d&&Ut(s),g=!d&&!l&&Qt(s);f=s,d||l||g?qt(a)?f=a:fo(a)?f=mn(a):l?(h=!1,f=pn(s,!0)):g?(h=!1,f=Qn(s,!0)):f=[]:Mi(s)||Gt(s)?(f=a,Gt(a)?f=Ei(a):U(a)&&!V(a)||(f=ne(s))):h=!1}h&&(u.set(s,f),o(f,s,r,i,u),u.delete(s)),pi(t,e,f)}},Ni=function t(n,e,r,o,i){n!==e&&de(e,(function(u,a){if(i||(i=new St),U(u))xi(n,e,a,r,t,o,i);else{var s=o?o(_i(n,a),u,a+"",n,e,i):void 0;void 0===s&&(s=u),pi(n,a,s)}}),ln)},Si=function(t){return to((function(n,e){var r=-1,o=e.length,i=o>1?e[o-1]:void 0,u=o>2?e[2]:void 0;for(i=t.length>3&&"function"==typeof i?(o--,i):void 0,u&&Ho(e[0],e[1],u)&&(i=o<3?void 0:i,o=1),n=Object(n);++r<o;){var a=e[r];a&&t(n,a,r,i)}return n}))}((function(t,n,e){Ni(t,n,e)})),Ti=function(t,n){return t<n},Ai=function(t){return t&&t.length?di(t,pe,Ti):void 0},Ii=function(t,n){return t&&t.length?di(t,gr(n),Ti):void 0},Oi=function(){return C.Date.now()},Ci=function(t,n,e,r){if(!U(t))return t;for(var o=-1,i=(n=rr(n,t)).length,u=i-1,a=t;null!=a&&++o<i;){var s=or(n[o]),c=e;if("__proto__"===s||"constructor"===s||"prototype"===s)return t;if(o!=u){var f=a[s];void 0===(c=r?r(f,s,a):void 0)&&(c=U(f)?f:zt(n[o+1])?[]:{})}Ct(a,s,c),a=a[s]}return t},ji=function(t,n,e){for(var r=-1,o=n.length,i={};++r<o;){var u=n[r],a=ir(t,u);e(a,u)&&Ci(i,rr(u,t),a)}return i},Ri=function(t,n){return ji(t,n,(function(n,e){return cr(t,e)}))},ki=function(t){return Xr(Kr(t,void 0,si),t+"")}((function(t,n){return null==t?{}:Ri(t,n)})),Pi=Math.ceil,Di=Math.max,Bi=function(t,n,e,r){for(var o=-1,i=Di(Pi((n-t)/(e||1)),0),u=Array(i);i--;)u[r?i:++o]=t,t+=e;return u},Li=function(t){return function(n,e,r){return r&&"number"!=typeof r&&Ho(n,e,r)&&(e=r=void 0),n=oi(n),void 0===e?(e=n,n=0):e=oi(e),r=void 0===r?n<e?1:-1:oi(r),Bi(n,e,r,t)}}(),Gi=function(t,n){var e=t.length;for(t.sort(n);e--;)t[e]=t[e].value;return t},qi=function(t,n){if(t!==n){var e=void 0!==t,r=null===t,o=t==t,i=Ue(t),u=void 0!==n,a=null===n,s=n==n,c=Ue(n);if(!a&&!c&&!i&&t>n||i&&u&&s&&!a&&!c||r&&u&&s||!e&&s||!o)return 1;if(!r&&!i&&!c&&t<n||c&&e&&o&&!r&&!i||a&&e&&o||!u&&o||!s)return-1}return 0},Fi=function(t,n,e){for(var r=-1,o=t.criteria,i=n.criteria,u=o.length,a=e.length;++r<u;){var s=qi(o[r],i[r]);if(s)return r>=a?s:s*("desc"==e[r]?-1:1)}return t.index-n.index},Ui=function(t,n,e){n=n.length?Ze(n,(function(t){return qt(t)?function(n){return ir(n,1===t.length?t[0]:t)}:t})):[pe];var r=-1;n=Ze(n,$t(gr));var o=_r(t,(function(t,e,o){return{criteria:Ze(n,(function(n){return n(t)})),index:++r,value:t}}));return Gi(o,(function(t,n){return Fi(t,n,e)}))},Vi=to((function(t,n){if(null==t)return[];var e=n.length;return e>1&&Ho(t,n[0],n[1])?n=[]:e>2&&Ho(n[0],n[1],n[2])&&(n=[n[0]]),Ui(t,Hr(n,1),[])})),zi=0,Wi=function(t){var n=++zi;return er(t)+n},Yi=function(t,n,e){for(var r=-1,o=t.length,i=n.length,u={};++r<o;){var a=r<i?n[r]:void 0;e(u,t[r],a)}return u},Hi=function(t,n){return Yi(t||[],n||[],Ct)};try{zo={cloneDeep:Yo,constant:he,defaults:Ko,each:ye,filter:pr,find:ai,flatten:si,forEach:ve,forIn:ci,has:yr,isUndefined:Mr,last:fi,map:Er,mapValues:hi,max:gi,merge:Si,min:Ai,minBy:Ii,now:Oi,pick:ki,range:Li,reduce:Sr,sortBy:Vi,uniqueId:Wi,values:go,zipObject:Hi}}catch(r){}zo||(zo=window._);var $i=zo,Ji=Ki;function Ki(){var t={};t._next=t._prev=t,this._sentinel=t}function Qi(t){t._prev._next=t._next,t._next._prev=t._prev,delete t._next,delete t._prev}function Zi(t,n){if("_next"!==t&&"_prev"!==t)return n}Ki.prototype.dequeue=function(){var t=this._sentinel,n=t._prev;if(n!==t)return Qi(n),n},Ki.prototype.enqueue=function(t){var n=this._sentinel;t._prev&&t._next&&Qi(t),t._next=n._next,n._next._prev=t,n._next=t,t._prev=n},Ki.prototype.toString=function(){for(var t=[],n=this._sentinel,e=n._prev;e!==n;)t.push(JSON.stringify(e,Zi)),e=e._prev;return"["+t.join(", ")+"]"};var Xi=Wo.Graph,tu=function(t,n){if(t.nodeCount()<=1)return[];var e=function(t,n){var e=new Xi,r=0,o=0;$i.forEach(t.nodes(),(function(t){e.setNode(t,{v:t,in:0,out:0})})),$i.forEach(t.edges(),(function(t){var i=e.edge(t.v,t.w)||0,u=n(t),a=i+u;e.setEdge(t.v,t.w,a),o=Math.max(o,e.node(t.v).out+=u),r=Math.max(r,e.node(t.w).in+=u)}));var i=$i.range(o+r+3).map((function(){return new Ji})),u=r+1;return $i.forEach(e.nodes(),(function(t){ru(i,u,e.node(t))})),{graph:e,buckets:i,zeroIdx:u}}(t,n||nu),r=function(t,n,e){for(var r,o=[],i=n[n.length-1],u=n[0];t.nodeCount();){for(;r=u.dequeue();)eu(t,n,e,r);for(;r=i.dequeue();)eu(t,n,e,r);if(t.nodeCount())for(var a=n.length-2;a>0;--a)if(r=n[a].dequeue()){o=o.concat(eu(t,n,e,r,!0));break}}return o}(e.graph,e.buckets,e.zeroIdx);return $i.flatten($i.map(r,(function(n){return t.outEdges(n.v,n.w)})),!0)},nu=$i.constant(1);function eu(t,n,e,r,o){var i=o?[]:void 0;return $i.forEach(t.inEdges(r.v),(function(r){var u=t.edge(r),a=t.node(r.v);o&&i.push({v:r.v,w:r.w}),a.out-=u,ru(n,e,a)})),$i.forEach(t.outEdges(r.v),(function(r){var o=t.edge(r),i=r.w,u=t.node(i);u.in-=o,ru(n,e,u)})),t.removeNode(r.v),i}function ru(t,n,e){e.out?e.in?t[e.out-e.in+n].enqueue(e):t[t.length-1].enqueue(e):t[0].enqueue(e)}var ou={run:function(t){var n="greedy"===t.graph().acyclicer?tu(t,function(t){return function(n){return t.edge(n).weight}}(t)):function(t){var n=[],e={},r={};function o(i){$i.has(r,i)||(r[i]=!0,e[i]=!0,$i.forEach(t.outEdges(i),(function(t){$i.has(e,t.w)?n.push(t):o(t.w)})),delete e[i])}return $i.forEach(t.nodes(),o),n}(t);$i.forEach(n,(function(n){var e=t.edge(n);t.removeEdge(n),e.forwardName=n.name,e.reversed=!0,t.setEdge(n.w,n.v,e,$i.uniqueId("rev"))}))},undo:function(t){$i.forEach(t.edges(),(function(n){var e=t.edge(n);if(e.reversed){t.removeEdge(n);var r=e.forwardName;delete e.reversed,delete e.forwardName,t.setEdge(n.w,n.v,e,r)}}))}},iu=Wo.Graph,uu={addDummyNode:au,simplify:function(t){var n=(new iu).setGraph(t.graph());return $i.forEach(t.nodes(),(function(e){n.setNode(e,t.node(e))})),$i.forEach(t.edges(),(function(e){var r=n.edge(e.v,e.w)||{weight:0,minlen:1},o=t.edge(e);n.setEdge(e.v,e.w,{weight:r.weight+o.weight,minlen:Math.max(r.minlen,o.minlen)})})),n},asNonCompoundGraph:function(t){var n=new iu({multigraph:t.isMultigraph()}).setGraph(t.graph());return $i.forEach(t.nodes(),(function(e){t.children(e).length||n.setNode(e,t.node(e))})),$i.forEach(t.edges(),(function(e){n.setEdge(e,t.edge(e))})),n},successorWeights:function(t){var n=$i.map(t.nodes(),(function(n){var e={};return $i.forEach(t.outEdges(n),(function(n){e[n.w]=(e[n.w]||0)+t.edge(n).weight})),e}));return $i.zipObject(t.nodes(),n)},predecessorWeights:function(t){var n=$i.map(t.nodes(),(function(n){var e={};return $i.forEach(t.inEdges(n),(function(n){e[n.v]=(e[n.v]||0)+t.edge(n).weight})),e}));return $i.zipObject(t.nodes(),n)},intersectRect:function(t,n){var e,r,o=t.x,i=t.y,u=n.x-o,a=n.y-i,s=t.width/2,c=t.height/2;if(!u&&!a)throw new Error("Not possible to find intersection inside of the rectangle");return Math.abs(a)*s>Math.abs(u)*c?(a<0&&(c=-c),e=c*u/a,r=c):(u<0&&(s=-s),e=s,r=s*a/u),{x:o+e,y:i+r}},buildLayerMatrix:function(t){var n=$i.map($i.range(su(t)+1),(function(){return[]}));return $i.forEach(t.nodes(),(function(e){var r=t.node(e),o=r.rank;$i.isUndefined(o)||(n[o][r.order]=e)})),n},normalizeRanks:function(t){var n=$i.min($i.map(t.nodes(),(function(n){return t.node(n).rank})));$i.forEach(t.nodes(),(function(e){var r=t.node(e);$i.has(r,"rank")&&(r.rank-=n)}))},removeEmptyRanks:function(t){var n=$i.min($i.map(t.nodes(),(function(n){return t.node(n).rank}))),e=[];$i.forEach(t.nodes(),(function(r){var o=t.node(r).rank-n;e[o]||(e[o]=[]),e[o].push(r)}));var r=0,o=t.graph().nodeRankFactor;$i.forEach(e,(function(n,e){$i.isUndefined(n)&&e%o!=0?--r:r&&$i.forEach(n,(function(n){t.node(n).rank+=r}))}))},addBorderNode:function(t,n,e,r){var o={width:0,height:0};return arguments.length>=4&&(o.rank=e,o.order=r),au(t,"border",o,n)},maxRank:su,partition:function(t,n){var e={lhs:[],rhs:[]};return $i.forEach(t,(function(t){n(t)?e.lhs.push(t):e.rhs.push(t)})),e},time:function(t,n){var e=$i.now();try{return n()}finally{console.log(t+" time: "+($i.now()-e)+"ms")}},notime:function(t,n){return n()}};function au(t,n,e,r){var o;do{o=$i.uniqueId(r)}while(t.hasNode(o));return e.dummy=n,t.setNode(o,e),o}function su(t){return $i.max($i.map(t.nodes(),(function(n){var e=t.node(n).rank;if(!$i.isUndefined(e))return e})))}var cu={run:function(t){t.graph().dummyChains=[],$i.forEach(t.edges(),(function(n){!function(t,n){var e,r,o,i=n.v,u=t.node(i).rank,a=n.w,s=t.node(a).rank,c=n.name,f=t.edge(n),h=f.labelRank;if(s!==u+1){for(t.removeEdge(n),o=0,++u;u<s;++o,++u)f.points=[],r={width:0,height:0,edgeLabel:f,edgeObj:n,rank:u},e=uu.addDummyNode(t,"edge",r,"_d"),u===h&&(r.width=f.width,r.height=f.height,r.dummy="edge-label",r.labelpos=f.labelpos),t.setEdge(i,e,{weight:f.weight},c),0===o&&t.graph().dummyChains.push(e),i=e;t.setEdge(i,a,{weight:f.weight},c)}}(t,n)}))},undo:function(t){$i.forEach(t.graph().dummyChains,(function(n){var e,r=t.node(n),o=r.edgeLabel;for(t.setEdge(r.edgeObj,o);r.dummy;)e=t.successors(n)[0],t.removeNode(n),o.points.push({x:r.x,y:r.y}),"edge-label"===r.dummy&&(o.x=r.x,o.y=r.y,o.width=r.width,o.height=r.height),n=e,r=t.node(n)}))}},fu=function(t){var n={};$i.forEach(t.sources(),(function e(r){var o=t.node(r);if($i.has(n,r))return o.rank;n[r]=!0;var i=$i.min($i.map(t.outEdges(r),(function(n){return e(n.w)-t.edge(n).minlen})));return i!==Number.POSITIVE_INFINITY&&null!=i||(i=0),o.rank=i}))},hu=function(t,n){return t.node(n.w).rank-t.node(n.v).rank-t.edge(n).minlen},du=Wo.Graph,lu=hu,gu=function(t){var n,e,r=new du({directed:!1}),o=t.nodes()[0],i=t.nodeCount();for(r.setNode(o,{});pu(r,t)<i;)n=mu(r,t),e=r.hasNode(n.v)?lu(t,n):-lu(t,n),vu(r,t,e);return r};function pu(t,n){return $i.forEach(t.nodes(),(function e(r){$i.forEach(n.nodeEdges(r),(function(o){var i=o.v,u=r===i?o.w:i;t.hasNode(u)||lu(n,o)||(t.setNode(u,{}),t.setEdge(r,u,{}),e(u))}))})),t.nodeCount()}function mu(t,n){return $i.minBy(n.edges(),(function(e){if(t.hasNode(e.v)!==t.hasNode(e.w))return lu(n,e)}))}function vu(t,n,e){$i.forEach(t.nodes(),(function(t){n.node(t).rank+=e}))}var yu=hu,wu=fu,bu=Wo.alg.preorder,Mu=Wo.alg.postorder,_u=uu.simplify,Eu=xu;function xu(t){t=_u(t),wu(t);var n,e=gu(t);for(Tu(e),Nu(e,t);n=Iu(e);)Cu(e,t,n,Ou(e,t,n))}function Nu(t,n){var e=Mu(t,t.nodes());e=e.slice(0,e.length-1),$i.forEach(e,(function(e){!function(t,n,e){var r=t.node(e).parent;t.edge(e,r).cutvalue=Su(t,n,e)}(t,n,e)}))}function Su(t,n,e){var r=t.node(e).parent,o=!0,i=n.edge(e,r),u=0;return i||(o=!1,i=n.edge(r,e)),u=i.weight,$i.forEach(n.nodeEdges(e),(function(i){var a,s,c=i.v===e,f=c?i.w:i.v;if(f!==r){var h=c===o,d=n.edge(i).weight;if(u+=h?d:-d,a=e,s=f,t.hasEdge(a,s)){var l=t.edge(e,f).cutvalue;u+=h?-l:l}}})),u}function Tu(t,n){arguments.length<2&&(n=t.nodes()[0]),Au(t,{},1,n)}function Au(t,n,e,r,o){var i=e,u=t.node(r);return n[r]=!0,$i.forEach(t.neighbors(r),(function(o){$i.has(n,o)||(e=Au(t,n,e,o,r))})),u.low=i,u.lim=e++,o?u.parent=o:delete u.parent,e}function Iu(t){return $i.find(t.edges(),(function(n){return t.edge(n).cutvalue<0}))}function Ou(t,n,e){var r=e.v,o=e.w;n.hasEdge(r,o)||(r=e.w,o=e.v);var i=t.node(r),u=t.node(o),a=i,s=!1;i.lim>u.lim&&(a=u,s=!0);var c=$i.filter(n.edges(),(function(n){return s===ju(t,t.node(n.v),a)&&s!==ju(t,t.node(n.w),a)}));return $i.minBy(c,(function(t){return yu(n,t)}))}function Cu(t,n,e,r){var o=e.v,i=e.w;t.removeEdge(o,i),t.setEdge(r.v,r.w,{}),Tu(t),Nu(t,n),function(t,n){var e=$i.find(t.nodes(),(function(t){return!n.node(t).parent})),r=bu(t,e);r=r.slice(1),$i.forEach(r,(function(e){var r=t.node(e).parent,o=n.edge(e,r),i=!1;o||(o=n.edge(r,e),i=!0),n.node(e).rank=n.node(r).rank+(i?o.minlen:-o.minlen)}))}(t,n)}function ju(t,n,e){return e.low<=n.lim&&n.lim<=e.lim}xu.initLowLimValues=Tu,xu.initCutValues=Nu,xu.calcCutValue=Su,xu.leaveEdge=Iu,xu.enterEdge=Ou,xu.exchangeEdges=Cu;var Ru=fu,ku=function(t){switch(t.graph().ranker){case"network-simplex":Du(t);break;case"tight-tree":!function(t){Ru(t),gu(t)}(t);break;case"longest-path":Pu(t);break;default:Du(t)}},Pu=Ru;function Du(t){Eu(t)}var Bu=function(t){var n=function(t){var n={},e=0;function r(o){var i=e;$i.forEach(t.children(o),r),n[o]={low:i,lim:e++}}return $i.forEach(t.children(),r),n}(t);$i.forEach(t.graph().dummyChains,(function(e){for(var r=t.node(e),o=r.edgeObj,i=function(t,n,e,r){var o,i,u=[],a=[],s=Math.min(n[e].low,n[r].low),c=Math.max(n[e].lim,n[r].lim);o=e;do{o=t.parent(o),u.push(o)}while(o&&(n[o].low>s||c>n[o].lim));for(i=o,o=r;(o=t.parent(o))!==i;)a.push(o);return{path:u.concat(a.reverse()),lca:i}}(t,n,o.v,o.w),u=i.path,a=i.lca,s=0,c=u[s],f=!0;e!==o.w;){if(r=t.node(e),f){for(;(c=u[s])!==a&&t.node(c).maxRank<r.rank;)s++;c===a&&(f=!1)}if(!f){for(;s<u.length-1&&t.node(c=u[s+1]).minRank<=r.rank;)s++;c=u[s]}t.setParent(e,c),e=t.successors(e)[0]}}))},Lu={run:function(t){var n=uu.addDummyNode(t,"root",{},"_root"),e=function(t){var n={};function e(r,o){var i=t.children(r);i&&i.length&&$i.forEach(i,(function(t){e(t,o+1)})),n[r]=o}return $i.forEach(t.children(),(function(t){e(t,1)})),n}(t),r=$i.max($i.values(e))-1,o=2*r+1;t.graph().nestingRoot=n,$i.forEach(t.edges(),(function(n){t.edge(n).minlen*=o}));var i=function(t){return $i.reduce(t.edges(),(function(n,e){return n+t.edge(e).weight}),0)}(t)+1;$i.forEach(t.children(),(function(u){Gu(t,n,o,i,r,e,u)})),t.graph().nodeRankFactor=o},cleanup:function(t){var n=t.graph();t.removeNode(n.nestingRoot),delete n.nestingRoot,$i.forEach(t.edges(),(function(n){t.edge(n).nestingEdge&&t.removeEdge(n)}))}};function Gu(t,n,e,r,o,i,u){var a=t.children(u);if(a.length){var s=uu.addBorderNode(t,"_bt"),c=uu.addBorderNode(t,"_bb"),f=t.node(u);t.setParent(s,u),f.borderTop=s,t.setParent(c,u),f.borderBottom=c,$i.forEach(a,(function(a){Gu(t,n,e,r,o,i,a);var f=t.node(a),h=f.borderTop?f.borderTop:a,d=f.borderBottom?f.borderBottom:a,l=f.borderTop?r:2*r,g=h!==d?1:o-i[u]+1;t.setEdge(s,h,{weight:l,minlen:g,nestingEdge:!0}),t.setEdge(d,c,{weight:l,minlen:g,nestingEdge:!0})})),t.parent(u)||t.setEdge(n,s,{weight:0,minlen:o+i[u]})}else u!==n&&t.setEdge(n,u,{weight:0,minlen:e})}var qu=function(t){$i.forEach(t.children(),(function n(e){var r=t.children(e),o=t.node(e);if(r.length&&$i.forEach(r,n),$i.has(o,"minRank")){o.borderLeft=[],o.borderRight=[];for(var i=o.minRank,u=o.maxRank+1;i<u;++i)Fu(t,"borderLeft","_bl",e,o,i),Fu(t,"borderRight","_br",e,o,i)}}))};function Fu(t,n,e,r,o,i){var u={width:0,height:0,rank:i,borderType:n},a=o[n][i-1],s=uu.addDummyNode(t,"border",u,e);o[n][i]=s,t.setParent(s,r),a&&t.setEdge(a,s,{weight:1})}var Uu={adjust:function(t){var n=t.graph().rankdir.toLowerCase();"lr"!==n&&"rl"!==n||Vu(t)},undo:function(t){var n=t.graph().rankdir.toLowerCase();"bt"!==n&&"rl"!==n||function(t){$i.forEach(t.nodes(),(function(n){Wu(t.node(n))})),$i.forEach(t.edges(),(function(n){var e=t.edge(n);$i.forEach(e.points,Wu),$i.has(e,"y")&&Wu(e)}))}(t),"lr"!==n&&"rl"!==n||(function(t){$i.forEach(t.nodes(),(function(n){Yu(t.node(n))})),$i.forEach(t.edges(),(function(n){var e=t.edge(n);$i.forEach(e.points,Yu),$i.has(e,"x")&&Yu(e)}))}(t),Vu(t))}};function Vu(t){$i.forEach(t.nodes(),(function(n){zu(t.node(n))})),$i.forEach(t.edges(),(function(n){zu(t.edge(n))}))}function zu(t){var n=t.width;t.width=t.height,t.height=n}function Wu(t){t.y=-t.y}function Yu(t){var n=t.x;t.x=t.y,t.y=n}var Hu=function(t){var n={},e=$i.filter(t.nodes(),(function(n){return!t.children(n).length})),r=$i.max($i.map(e,(function(n){return t.node(n).rank}))),o=$i.map($i.range(r+1),(function(){return[]}));function i(e){if(!$i.has(n,e)){n[e]=!0;var r=t.node(e);o[r.rank].push(e),$i.forEach(t.successors(e),i)}}var u=$i.sortBy(e,(function(n){return t.node(n).rank}));return $i.forEach(u,i),o},$u=function(t,n){for(var e=0,r=1;r<n.length;++r)e+=Ju(t,n[r-1],n[r]);return e};function Ju(t,n,e){for(var r=$i.zipObject(e,$i.map(e,(function(t,n){return n}))),o=$i.flatten($i.map(n,(function(n){return $i.sortBy($i.map(t.outEdges(n),(function(n){return{pos:r[n.w],weight:t.edge(n).weight}})),"pos")})),!0),i=1;i<e.length;)i<<=1;var u=2*i-1;i-=1;var a=$i.map(new Array(u),(function(){return 0})),s=0;return $i.forEach(o.forEach((function(t){var n=t.pos+i;a[n]+=t.weight;for(var e=0;n>0;)n%2&&(e+=a[n+1]),a[n=n-1>>1]+=t.weight;s+=t.weight*e}))),s}var Ku=function(t,n){return $i.map(n,(function(n){var e=t.inEdges(n);if(e.length){var r=$i.reduce(e,(function(n,e){var r=t.edge(e),o=t.node(e.v);return{sum:n.sum+r.weight*o.order,weight:n.weight+r.weight}}),{sum:0,weight:0});return{v:n,barycenter:r.sum/r.weight,weight:r.weight}}return{v:n}}))},Qu=function(t,n){var e={};return $i.forEach(t,(function(t,n){var r=e[t.v]={indegree:0,in:[],out:[],vs:[t.v],i:n};$i.isUndefined(t.barycenter)||(r.barycenter=t.barycenter,r.weight=t.weight)})),$i.forEach(n.edges(),(function(t){var n=e[t.v],r=e[t.w];$i.isUndefined(n)||$i.isUndefined(r)||(r.indegree++,n.out.push(e[t.w]))})),function(t){var n=[];function e(t){return function(n){n.merged||($i.isUndefined(n.barycenter)||$i.isUndefined(t.barycenter)||n.barycenter>=t.barycenter)&&function(t,n){var e=0,r=0;t.weight&&(e+=t.barycenter*t.weight,r+=t.weight),n.weight&&(e+=n.barycenter*n.weight,r+=n.weight),t.vs=n.vs.concat(t.vs),t.barycenter=e/r,t.weight=r,t.i=Math.min(n.i,t.i),n.merged=!0}(t,n)}}function r(n){return function(e){e.in.push(n),0==--e.indegree&&t.push(e)}}for(;t.length;){var o=t.pop();n.push(o),$i.forEach(o.in.reverse(),e(o)),$i.forEach(o.out,r(o))}return $i.map($i.filter(n,(function(t){return!t.merged})),(function(t){return $i.pick(t,["vs","i","barycenter","weight"])}))}($i.filter(e,(function(t){return!t.indegree})))},Zu=function(t,n){var e,r=uu.partition(t,(function(t){return $i.has(t,"barycenter")})),o=r.lhs,i=$i.sortBy(r.rhs,(function(t){return-t.i})),u=[],a=0,s=0,c=0;o.sort((e=!!n,function(t,n){return t.barycenter<n.barycenter?-1:t.barycenter>n.barycenter?1:e?n.i-t.i:t.i-n.i})),c=Xu(u,i,c),$i.forEach(o,(function(t){c+=t.vs.length,u.push(t.vs),a+=t.barycenter*t.weight,s+=t.weight,c=Xu(u,i,c)}));var f={vs:$i.flatten(u,!0)};return s&&(f.barycenter=a/s,f.weight=s),f};function Xu(t,n,e){for(var r;n.length&&(r=$i.last(n)).i<=e;)n.pop(),t.push(r.vs),e++;return e}var ta=function t(n,e,r,o){var i=n.children(e),u=n.node(e),a=u?u.borderLeft:void 0,s=u?u.borderRight:void 0,c={};a&&(i=$i.filter(i,(function(t){return t!==a&&t!==s})));var f=Ku(n,i);$i.forEach(f,(function(e){if(n.children(e.v).length){var i=t(n,e.v,r,o);c[e.v]=i,$i.has(i,"barycenter")&&(u=e,a=i,$i.isUndefined(u.barycenter)?(u.barycenter=a.barycenter,u.weight=a.weight):(u.barycenter=(u.barycenter*u.weight+a.barycenter*a.weight)/(u.weight+a.weight),u.weight+=a.weight))}var u,a}));var h=Qu(f,r);!function(t,n){$i.forEach(t,(function(t){t.vs=$i.flatten(t.vs.map((function(t){return n[t]?n[t].vs:t})),!0)}))}(h,c);var d=Zu(h,o);if(a&&(d.vs=$i.flatten([a,d.vs,s],!0),n.predecessors(a).length)){var l=n.node(n.predecessors(a)[0]),g=n.node(n.predecessors(s)[0]);$i.has(d,"barycenter")||(d.barycenter=0,d.weight=0),d.barycenter=(d.barycenter*d.weight+l.order+g.order)/(d.weight+2),d.weight+=2}return d},na=Wo.Graph,ea=function(t,n,e){var r=function(t){for(var n;t.hasNode(n=$i.uniqueId("_root")););return n}(t),o=new na({compound:!0}).setGraph({root:r}).setDefaultNodeLabel((function(n){return t.node(n)}));return $i.forEach(t.nodes(),(function(i){var u=t.node(i),a=t.parent(i);(u.rank===n||u.minRank<=n&&n<=u.maxRank)&&(o.setNode(i),o.setParent(i,a||r),$i.forEach(t[e](i),(function(n){var e=n.v===i?n.w:n.v,r=o.edge(e,i),u=$i.isUndefined(r)?0:r.weight;o.setEdge(e,i,{weight:t.edge(n).weight+u})})),$i.has(u,"minRank")&&o.setNode(i,{borderLeft:u.borderLeft[n],borderRight:u.borderRight[n]}))})),o},ra=function(t,n,e){var r,o={};$i.forEach(e,(function(e){for(var i,u,a=t.parent(e);a;){if((i=t.parent(a))?(u=o[i],o[i]=a):(u=r,r=a),u&&u!==a)return void n.setEdge(u,a);a=i}}))},oa=Wo.Graph,ia=function(t){var n=uu.maxRank(t),e=ua(t,$i.range(1,n+1),"inEdges"),r=ua(t,$i.range(n-1,-1,-1),"outEdges"),o=Hu(t);sa(t,o);for(var i,u=Number.POSITIVE_INFINITY,a=0,s=0;s<4;++a,++s){aa(a%2?e:r,a%4>=2),o=uu.buildLayerMatrix(t);var c=$u(t,o);c<u&&(s=0,i=$i.cloneDeep(o),u=c)}sa(t,i)};function ua(t,n,e){return $i.map(n,(function(n){return ea(t,n,e)}))}function aa(t,n){var e=new oa;$i.forEach(t,(function(t){var r=t.graph().root,o=ta(t,r,e,n);$i.forEach(o.vs,(function(n,e){t.node(n).order=e})),ra(t,e,o.vs)}))}function sa(t,n){$i.forEach(n,(function(n){$i.forEach(n,(function(n,e){t.node(n).order=e}))}))}var ca=Wo.Graph,fa=function(t){var n,e=uu.buildLayerMatrix(t),r=$i.merge(ha(t,e),da(t,e)),o={};$i.forEach(["u","d"],(function(i){n="u"===i?e:$i.values(e).reverse(),$i.forEach(["l","r"],(function(e){"r"===e&&(n=$i.map(n,(function(t){return $i.values(t).reverse()})));var u=("u"===i?t.predecessors:t.successors).bind(t),a=pa(t,n,r,u),s=ma(t,n,a.root,a.align,"r"===e);"r"===e&&(s=$i.mapValues(s,(function(t){return-t}))),o[i+e]=s}))}));var i=va(t,o);return ya(o,i),wa(o,t.graph().align)};function ha(t,n){var e={};return $i.reduce(n,(function(n,r){var o=0,i=0,u=n.length,a=$i.last(r);return $i.forEach(r,(function(n,s){var c=function(t,n){if(t.node(n).dummy)return $i.find(t.predecessors(n),(function(n){return t.node(n).dummy}))}(t,n),f=c?t.node(c).order:u;(c||n===a)&&($i.forEach(r.slice(i,s+1),(function(n){$i.forEach(t.predecessors(n),(function(r){var i=t.node(r),u=i.order;!(u<o||f<u)||i.dummy&&t.node(n).dummy||la(e,r,n)}))})),i=s+1,o=f)})),r})),e}function da(t,n){var e={};function r(n,r,o,i,u){var a;$i.forEach($i.range(r,o),(function(r){a=n[r],t.node(a).dummy&&$i.forEach(t.predecessors(a),(function(n){var r=t.node(n);r.dummy&&(r.order<i||r.order>u)&&la(e,n,a)}))}))}return $i.reduce(n,(function(n,e){var o,i=-1,u=0;return $i.forEach(e,(function(a,s){if("border"===t.node(a).dummy){var c=t.predecessors(a);c.length&&(o=t.node(c[0]).order,r(e,u,s,i,o),u=s,i=o)}r(e,u,e.length,o,n.length)})),e})),e}function la(t,n,e){if(n>e){var r=n;n=e,e=r}var o=t[n];o||(t[n]=o={}),o[e]=!0}function ga(t,n,e){if(n>e){var r=n;n=e,e=r}return $i.has(t[n],e)}function pa(t,n,e,r){var o={},i={},u={};return $i.forEach(n,(function(t){$i.forEach(t,(function(t,n){o[t]=t,i[t]=t,u[t]=n}))})),$i.forEach(n,(function(t){var n=-1;$i.forEach(t,(function(t){var a=r(t);if(a.length)for(var s=((a=$i.sortBy(a,(function(t){return u[t]}))).length-1)/2,c=Math.floor(s),f=Math.ceil(s);c<=f;++c){var h=a[c];i[t]===t&&n<u[h]&&!ga(e,t,h)&&(i[h]=t,i[t]=o[t]=o[h],n=u[h])}}))})),{root:o,align:i}}function ma(t,n,e,r,o){var i={},u=function(t,n,e,r){var o=new ca,i=t.graph(),u=function(t,n,e){return function(r,o,i){var u,a=r.node(o),s=r.node(i),c=0;if(c+=a.width/2,$i.has(a,"labelpos"))switch(a.labelpos.toLowerCase()){case"l":u=-a.width/2;break;case"r":u=a.width/2}if(u&&(c+=e?u:-u),u=0,c+=(a.dummy?n:t)/2,c+=(s.dummy?n:t)/2,c+=s.width/2,$i.has(s,"labelpos"))switch(s.labelpos.toLowerCase()){case"l":u=s.width/2;break;case"r":u=-s.width/2}return u&&(c+=e?u:-u),u=0,c}}(i.nodesep,i.edgesep,r);return $i.forEach(n,(function(n){var r;$i.forEach(n,(function(n){var i=e[n];if(o.setNode(i),r){var a=e[r],s=o.edge(a,i);o.setEdge(a,i,Math.max(u(t,n,r),s||0))}r=n}))})),o}(t,n,e,o),a=o?"borderLeft":"borderRight";function s(t,n){for(var e=u.nodes(),r=e.pop(),o={};r;)o[r]?t(r):(o[r]=!0,e.push(r),e=e.concat(n(r))),r=e.pop()}return s((function(t){i[t]=u.inEdges(t).reduce((function(t,n){return Math.max(t,i[n.v]+u.edge(n))}),0)}),u.predecessors.bind(u)),s((function(n){var e=u.outEdges(n).reduce((function(t,n){return Math.min(t,i[n.w]-u.edge(n))}),Number.POSITIVE_INFINITY),r=t.node(n);e!==Number.POSITIVE_INFINITY&&r.borderType!==a&&(i[n]=Math.max(i[n],e))}),u.successors.bind(u)),$i.forEach(r,(function(t){i[t]=i[e[t]]})),i}function va(t,n){return $i.minBy($i.values(n),(function(n){var e=Number.NEGATIVE_INFINITY,r=Number.POSITIVE_INFINITY;return $i.forIn(n,(function(n,o){var i=function(t,n){return t.node(n).width}(t,o)/2;e=Math.max(n+i,e),r=Math.min(n-i,r)})),e-r}))}function ya(t,n){var e=$i.values(n),r=$i.min(e),o=$i.max(e);$i.forEach(["u","d"],(function(e){$i.forEach(["l","r"],(function(i){var u,a=e+i,s=t[a];if(s!==n){var c=$i.values(s);(u="l"===i?r-$i.min(c):o-$i.max(c))&&(t[a]=$i.mapValues(s,(function(t){return t+u})))}}))}))}function wa(t,n){return $i.mapValues(t.ul,(function(e,r){if(n)return t[n.toLowerCase()][r];var o=$i.sortBy($i.map(t,r));return(o[1]+o[2])/2}))}var ba=fa,Ma=function(t){(function(t){var n=uu.buildLayerMatrix(t),e=t.graph().ranksep,r=0;$i.forEach(n,(function(n){var o=$i.max($i.map(n,(function(n){return t.node(n).height})));$i.forEach(n,(function(n){t.node(n).y=r+o/2})),r+=o+e}))})(t=uu.asNonCompoundGraph(t)),$i.forEach(ba(t),(function(n,e){t.node(e).x=n}))},_a=uu.normalizeRanks,Ea=uu.removeEmptyRanks,xa=uu,Na=Wo.Graph,Sa=function(t,n){var e=n&&n.debugTiming?xa.time:xa.notime;e("layout",(function(){var n=e("  buildLayoutGraph",(function(){return function(t){var n=new Na({multigraph:!0,compound:!0}),e=Da(t.graph());return n.setGraph($i.merge({},Aa,Pa(e,Ta),$i.pick(e,Ia))),$i.forEach(t.nodes(),(function(e){var r=Da(t.node(e));n.setNode(e,$i.defaults(Pa(r,Oa),Ca)),n.setParent(e,t.parent(e))})),$i.forEach(t.edges(),(function(e){var r=Da(t.edge(e));n.setEdge(e,$i.merge({},Ra,Pa(r,ja),$i.pick(r,ka)))})),n}(t)}));e("  runLayout",(function(){!function(t,n){n("    makeSpaceForEdgeLabels",(function(){!function(t){var n=t.graph();n.ranksep/=2,$i.forEach(t.edges(),(function(e){var r=t.edge(e);r.minlen*=2,"c"!==r.labelpos.toLowerCase()&&("TB"===n.rankdir||"BT"===n.rankdir?r.width+=r.labeloffset:r.height+=r.labeloffset)}))}(t)})),n("    removeSelfEdges",(function(){!function(t){$i.forEach(t.edges(),(function(n){if(n.v===n.w){var e=t.node(n.v);e.selfEdges||(e.selfEdges=[]),e.selfEdges.push({e:n,label:t.edge(n)}),t.removeEdge(n)}}))}(t)})),n("    acyclic",(function(){ou.run(t)})),n("    nestingGraph.run",(function(){Lu.run(t)})),n("    rank",(function(){ku(xa.asNonCompoundGraph(t))})),n("    injectEdgeLabelProxies",(function(){!function(t){$i.forEach(t.edges(),(function(n){var e=t.edge(n);if(e.width&&e.height){var r=t.node(n.v),o={rank:(t.node(n.w).rank-r.rank)/2+r.rank,e:n};xa.addDummyNode(t,"edge-proxy",o,"_ep")}}))}(t)})),n("    removeEmptyRanks",(function(){Ea(t)})),n("    nestingGraph.cleanup",(function(){Lu.cleanup(t)})),n("    normalizeRanks",(function(){_a(t)})),n("    assignRankMinMax",(function(){!function(t){var n=0;$i.forEach(t.nodes(),(function(e){var r=t.node(e);r.borderTop&&(r.minRank=t.node(r.borderTop).rank,r.maxRank=t.node(r.borderBottom).rank,n=$i.max(n,r.maxRank))})),t.graph().maxRank=n}(t)})),n("    removeEdgeLabelProxies",(function(){!function(t){$i.forEach(t.nodes(),(function(n){var e=t.node(n);"edge-proxy"===e.dummy&&(t.edge(e.e).labelRank=e.rank,t.removeNode(n))}))}(t)})),n("    normalize.run",(function(){cu.run(t)})),n("    parentDummyChains",(function(){Bu(t)})),n("    addBorderSegments",(function(){qu(t)})),n("    order",(function(){ia(t)})),n("    insertSelfEdges",(function(){!function(t){var n=xa.buildLayerMatrix(t);$i.forEach(n,(function(n){var e=0;$i.forEach(n,(function(n,r){var o=t.node(n);o.order=r+e,$i.forEach(o.selfEdges,(function(n){xa.addDummyNode(t,"selfedge",{width:n.label.width,height:n.label.height,rank:o.rank,order:r+ ++e,e:n.e,label:n.label},"_se")})),delete o.selfEdges}))}))}(t)})),n("    adjustCoordinateSystem",(function(){Uu.adjust(t)})),n("    position",(function(){Ma(t)})),n("    positionSelfEdges",(function(){!function(t){$i.forEach(t.nodes(),(function(n){var e=t.node(n);if("selfedge"===e.dummy){var r=t.node(e.e.v),o=r.x+r.width/2,i=r.y,u=e.x-o,a=r.height/2;t.setEdge(e.e,e.label),t.removeNode(n),e.label.points=[{x:o+2*u/3,y:i-a},{x:o+5*u/6,y:i-a},{x:o+u,y:i},{x:o+5*u/6,y:i+a},{x:o+2*u/3,y:i+a}],e.label.x=e.x,e.label.y=e.y}}))}(t)})),n("    removeBorderNodes",(function(){!function(t){$i.forEach(t.nodes(),(function(n){if(t.children(n).length){var e=t.node(n),r=t.node(e.borderTop),o=t.node(e.borderBottom),i=t.node($i.last(e.borderLeft)),u=t.node($i.last(e.borderRight));e.width=Math.abs(u.x-i.x),e.height=Math.abs(o.y-r.y),e.x=i.x+e.width/2,e.y=r.y+e.height/2}})),$i.forEach(t.nodes(),(function(n){"border"===t.node(n).dummy&&t.removeNode(n)}))}(t)})),n("    normalize.undo",(function(){cu.undo(t)})),n("    fixupEdgeLabelCoords",(function(){!function(t){$i.forEach(t.edges(),(function(n){var e=t.edge(n);if($i.has(e,"x"))switch("l"!==e.labelpos&&"r"!==e.labelpos||(e.width-=e.labeloffset),e.labelpos){case"l":e.x-=e.width/2+e.labeloffset;break;case"r":e.x+=e.width/2+e.labeloffset}}))}(t)})),n("    undoCoordinateSystem",(function(){Uu.undo(t)})),n("    translateGraph",(function(){!function(t){var n=Number.POSITIVE_INFINITY,e=0,r=Number.POSITIVE_INFINITY,o=0,i=t.graph(),u=i.marginx||0,a=i.marginy||0;function s(t){var i=t.x,u=t.y,a=t.width,s=t.height;n=Math.min(n,i-a/2),e=Math.max(e,i+a/2),r=Math.min(r,u-s/2),o=Math.max(o,u+s/2)}$i.forEach(t.nodes(),(function(n){s(t.node(n))})),$i.forEach(t.edges(),(function(n){var e=t.edge(n);$i.has(e,"x")&&s(e)})),n-=u,r-=a,$i.forEach(t.nodes(),(function(e){var o=t.node(e);o.x-=n,o.y-=r})),$i.forEach(t.edges(),(function(e){var o=t.edge(e);$i.forEach(o.points,(function(t){t.x-=n,t.y-=r})),$i.has(o,"x")&&(o.x-=n),$i.has(o,"y")&&(o.y-=r)})),i.width=e-n+u,i.height=o-r+a}(t)})),n("    assignNodeIntersects",(function(){!function(t){$i.forEach(t.edges(),(function(n){var e,r,o=t.edge(n),i=t.node(n.v),u=t.node(n.w);o.points?(e=o.points[0],r=o.points[o.points.length-1]):(o.points=[],e=u,r=i),o.points.unshift(xa.intersectRect(i,e)),o.points.push(xa.intersectRect(u,r))}))}(t)})),n("    reversePoints",(function(){!function(t){$i.forEach(t.edges(),(function(n){var e=t.edge(n);e.reversed&&e.points.reverse()}))}(t)})),n("    acyclic.undo",(function(){ou.undo(t)}))}(n,e)})),e("  updateInputGraph",(function(){!function(t,n){$i.forEach(t.nodes(),(function(e){var r=t.node(e),o=n.node(e);r&&(r.x=o.x,r.y=o.y,n.children(e).length&&(r.width=o.width,r.height=o.height))})),$i.forEach(t.edges(),(function(e){var r=t.edge(e),o=n.edge(e);r.points=o.points,$i.has(o,"x")&&(r.x=o.x,r.y=o.y)})),t.graph().width=n.graph().width,t.graph().height=n.graph().height}(t,n)}))}))},Ta=["nodesep","edgesep","ranksep","marginx","marginy"],Aa={ranksep:50,edgesep:20,nodesep:50,rankdir:"tb"},Ia=["acyclicer","ranker","rankdir","align"],Oa=["width","height"],Ca={width:0,height:0},ja=["minlen","weight","width","height","labeloffset"],Ra={minlen:1,weight:1,width:0,height:0,labeloffset:10,labelpos:"r"},ka=["labelpos"];function Pa(t,n){return $i.mapValues($i.pick(t,n),Number)}function Da(t){var n={};return $i.forEach(t,(function(t,e){n[e.toLowerCase()]=t})),n}var Ba=Wo.Graph,La={graphlib:Wo,layout:Sa,debug:{debugOrdering:function(t){var n=uu.buildLayerMatrix(t),e=new Ba({compound:!0,multigraph:!0}).setGraph({});return $i.forEach(t.nodes(),(function(n){e.setNode(n,{label:n}),e.setParent(n,"layer"+t.node(n).rank)})),$i.forEach(t.edges(),(function(t){e.setEdge(t.v,t.w,{},t.name)})),$i.forEach(n,(function(t,n){var r="layer"+n;e.setNode(r,{rank:"same"}),$i.reduce(t,(function(t,n){return e.setEdge(t,n,{style:"invis"}),n}))})),e}},util:{time:uu.time,notime:uu.notime},version:"0.8.5"},Ga=La.graphlib,qa=La.layout;class Fa{constructor(t){this.cardinality=1,this.include=c.UNSPECIFIED,this.isGroupNode=!1,this.parentNode=null,this.type=s.NODE,this.name=t.name,this.attr=t.attr||{},this.inputs=t.inputs,this.path=t.path||[],this.width=t.width,this.height=t.height}}class Ua{constructor(){this.nodes={},this.edges=[],this.nodes={},this.edges=[]}}function Va(t){const n=new Ua;return t.nodes.map(t=>new Fa(t)).forEach(t=>{n.nodes[t.name]=t,t.inputs.forEach(e=>{!function(t,n,e){e.name!==n.name&&t.edges.push(Object.assign(Object.assign({},e.attr),{v:e.name,w:n.name}))}(n,t,e)})}),n}class za{constructor(t,n={}){this.attr=null,this.bridgeGraph=null,this.cardinality=0,this.depth=1,this.include=c.UNSPECIFIED,this.isGroupNode=!0,this.parentNode=null,this.type=s.META,this.path=[],this.name=t,this.metaGraph=$a(t,f.META,n)}getFirstChild(){return this.metaGraph.node(this.metaGraph.nodes()[0])}getChildren(){return this.metaGraph.nodes().map(t=>this.metaGraph.node(t))}leaves(){let t,n=[],e=[this];for(;e.length;){let r=e.shift();r.isGroupNode?(t=r.metaGraph,t.nodes().forEach(n=>e.push(t.node(n)))):n.push(r.name)}return n}}class Wa{constructor(t,n){this.v=t,this.w=n,this.baseEdgeList=[],this.inbound=null,this.name=null}addBaseEdge(t,n){this.baseEdgeList.push(t)}}function Ya(t,n={}){return new za(t,n)}function Ha(t,n){return new Wa(t,n)}function $a(t,n,e){const r=e||{},o=new Ga.Graph(r);return o.setGraph({name:t,rankdir:r.rankdir,type:n,align:r.align}),o}class Ja{constructor(t={}){this.graphOptions=t,this.index={},this.graphOptions.compound=!0,this.root=Ya(u,this.graphOptions),this.index[u]=this.root}getNodeMap(){return this.index}node(t){return this.index[t]}setNode(t,n){this.index[t]=n}getBridgeGraph(t){const n=this.index[t];if(!n)throw Error("Could not find node in hierarchy: "+t);if(!("metaGraph"in n))return null;const e=n;if(e.bridgeGraph)return e.bridgeGraph;const r=$a(a,f.BRIDGE,this.graphOptions);if(e.bridgeGraph=r,!n.parentNode||!("metaGraph"in n.parentNode))return r;const o=n.parentNode;return[o.metaGraph,this.getBridgeGraph(o.name)].forEach(n=>{n.edges().filter(n=>n.v===t||n.w===t).forEach(e=>{const o=e.w===t;n.edge(e).baseEdgeList.forEach(n=>{const[i,u]=o?[n.w,e.v]:[n.v,e.w],a=this.getChildName(t,i),s={v:o?u:a,w:o?a:u};let c=r.edge(s);c||(c=Ha(s.v,s.w),c.inbound=o,r.setEdge(s.v,s.w,c)),c.addBaseEdge(n,this)})})}),r}getChildName(t,n){let e=this.index[n];for(;e;){if(e.parentNode&&e.parentNode.name===t)return e.name;e=e.parentNode}throw Error("Could not find immediate child for descendant: "+n)}getPredecessors(t){const n=this.index[t];if(!n)throw Error("Could not find node with name: "+t);return this.getOneWayEdges(n,!0)}getSuccessors(t){const n=this.index[t];if(!n)throw Error("Could not find node with name: "+t);return this.getOneWayEdges(n,!1)}getOneWayEdges(t,n){const e=[];if(!t.parentNode||!t.parentNode.isGroupNode)return e;const r=t.parentNode,o=r.metaGraph,i=this.getBridgeGraph(r.name);return Qa(o,t,n,e),Qa(i,t,n,e),e}}function Ka(t,n){const{rankDirection:e,align:r}=n,o=new Ja({rankdir:e,align:r});return function(t,n){Object.keys(n.nodes).forEach(e=>{const r=n.nodes[e],o=r.path;let i=t.root;i.depth=Math.max(o.length,i.depth);for(let n=0;n<o.length&&(i.depth=Math.max(i.depth,o.length-n),i.cardinality+=r.cardinality,n!==o.length-1);n++){const e=o[n];let u=t.node(e);u||(u=Ya(e,t.graphOptions),u.path=r.path.slice(0,n+1),u.parentNode=i,t.setNode(e,u),i.metaGraph.setNode(e,u)),i=u}t.setNode(r.name,r),r.parentNode=i,i.metaGraph.setNode(r.name,r)})}(o,t),function(t,n){const e=t.getNodeMap();let r=[],o=[];const i=(t,n)=>{let e=0;for(;t;)n[e++]=t.name,t=t.parentNode;return e-1};n.edges.forEach(u=>{r=[],o=[];let a=i(n.nodes[u.v],r),s=i(n.nodes[u.w],o);for(;r[a]===o[s];)if(a--,s--,a<0||s<0)throw Error("No difference found between ancestor paths.");const c=e[r[a+1]],f=r[a],h=o[s];let d=c.metaGraph.edge(f,h);d||(d=Ha(f,h),c.metaGraph.setEdge(f,h,d)),d.addBaseEdge(u,t)})}(o,t),o}function Qa(t,n,e,r){(e?t.inEdges(n.name):t.outEdges(n.name)).forEach(n=>{const e=t.edge(n);r.push(e)})}class Za{constructor(t){this.hierarchy=t,this.index={},this.hasSubHierarchy={},this.root=new ns(this.hierarchy.root,this.hierarchy.graphOptions),this.index[t.root.name]=this.root,this.buildSubHierarchy(t.root.name),this.root.expanded=!0}getRenderInfoNodes(){return Object.values(this.index)}getSubHierarchy(){return this.hasSubHierarchy}buildSubHierarchy(t){if(t in this.hasSubHierarchy)return;this.hasSubHierarchy[t]=!0;const n=this.index[t];if(n.node.type!==s.META)return;const e=n,r=e.node.metaGraph,o=e.coreGraph;r.nodes().forEach(t=>{const n=this.getOrCreateRenderNodeByName(t);o.setNode(t,n)}),r.edges().forEach(t=>{const n=r.edge(t),e=new ts(n);o.setEdge(t.v,t.w,e)});const i=e.node.parentNode;if(!i)return;const u=this.getRenderNodeByName(i.name),a=(t,...n)=>n.concat([t?"IN":"OUT"]).join("~~"),f=this.hierarchy.getBridgeGraph(t);f.edges().forEach(n=>{const e=f.edge(n),h=!!r.node(n.w),[d,l]=h?[n.w,n.v]:[n.v,n.w],g=n=>{const e=h?{v:n,w:t}:{v:t,w:n};return u.coreGraph.edge(e)};let p=g(l);p||(p=g(a(h,l,i.name)));const m=a(h,t),v=a(h,l,t);let y=o.node(v);if(!y){let t=o.node(m);if(!t){const n={name:m,type:s.BRIDGE,isGroupNode:!1,cardinality:0,parentNode:null,include:c.UNSPECIFIED,inbound:h,attr:{}};t=new Xa(n),this.index[m]=t,o.setNode(m,t)}const n={name:v,type:s.BRIDGE,isGroupNode:!1,cardinality:1,parentNode:null,include:c.UNSPECIFIED,inbound:h,attr:{}};y=new Xa(n),this.index[v]=y,o.setNode(v,y),o.setParent(v,m),t.node.cardinality++}const w=new ts(e);w.adjoiningMetaEdge=p,h?o.setEdge(v,d,w):o.setEdge(d,v,w)})}getOrCreateRenderNodeByName(t){if(!t)return null;if(t in this.index)return this.index[t];const n=this.getNodeByName(t);return n?(this.index[t]=n.isGroupNode?new ns(n,this.hierarchy.graphOptions):new Xa(n),this.index[t]):null}getRenderNodeByName(t){return this.index[t]}getNodeByName(t){return this.hierarchy.node(t)}}class Xa{constructor(t){this.node=t,this.expanded=!1,this.x=0,this.y=0,this.coreBox={width:0,height:0},this.outboxWidth=0,this.labelOffset=0,this.radius=0,this.labelHeight=0,this.paddingTop=0,this.paddingLeft=0,this.paddingRight=0,this.paddingBottom=0,this.width=t.width||0,this.height=t.height||0,this.displayName=t.name,this.attr=t.attr}}class ts{constructor(t){this.metaEdge=t,this.adjoiningMetaEdge=null,this.weight=1,this.points=[]}}class ns extends Xa{constructor(t,n){super(t);const e=t.metaGraph.graph();n.compound=!0,this.coreGraph=$a(e.name,f.CORE,n)}}function es(t,n){t.node.isGroupNode&&function(t,n){const e=o(n);t.coreGraph.nodes().map(n=>t.coreGraph.node(n)).forEach(t=>{var r,o,i,u,a,c;const{height:f,width:h}=t;switch(t.node.type){case s.NODE:Object.assign(t,e.nodeSize.node),t.height=f||e.nodeSize.node.height,t.width=h||e.nodeSize.node.width;break;case s.BRIDGE:Object.assign(t,e.nodeSize.bridge);break;case s.META:t.expanded?es(t,n):(Object.assign(t,e.nodeSize.meta),t.height=e.nodeSize.meta.height,t.width=e.nodeSize.meta.width);break;default:throw Error("Unrecognized node type: "+t.node.type)}if(!t.expanded){const e=t.attr;!function(t,n=!1){if(t.coreBox.width=t.width,t.coreBox.height=t.height,!n){const n=(""+t.displayName).length,e=3;t.width=Math.max(t.coreBox.width,n*e)}}(t,n&&(0===t.node.type&&!!(null===(o=null===(r=null==n?void 0:n.nodeSize)||void 0===r?void 0:r.meta)||void 0===o?void 0:o.width)||1===t.node.type&&(!!(null===(u=null===(i=null==n?void 0:n.nodeSize)||void 0===i?void 0:i.node)||void 0===u?void 0:u.width)||!!e.width)||2===t.node.type&&!!(null===(c=null===(a=null==n?void 0:n.nodeSize)||void 0===a?void 0:a.bridge)||void 0===c?void 0:c.width)))}})}(t,n),t.node.type===s.META&&function(t,n){const e=o(n),r=e.subScene.meta;Object.assign(t,r);const{nodeSep:i,rankSep:u,edgeSep:a,align:c}=e.graph.meta,f={nodesep:i,ranksep:u,edgesep:a,align:c};Object.assign(t.coreBox,function(t,n){const{ranksep:e,nodesep:r,edgesep:o,align:i}=n;Object.assign(t.graph(),{ranksep:e,nodesep:r,edgesep:o,align:i});const u=[];if(t.nodes().forEach(n=>{t.node(n).node.type!==s.BRIDGE&&u.push(n)}),!u.length)return{width:0,height:0};qa(t);let a=1/0,c=1/0,f=-1/0,h=-1/0;return u.forEach(n=>{const e=t.node(n),r=.5*e.width,o=e.x-r,i=e.x+r;a=o<a?o:a,f=i>f?i:f;const u=.5*e.height,s=e.y-u,d=e.y+u;c=s<c?s:c,h=d>h?d:h}),t.edges().forEach(n=>{const e=t.edge(n),r=t.node(e.metaEdge.v),o=t.node(e.metaEdge.w);if(3===e.points.length&&function(t){let n=rs(t[0],t[1]);for(let e=1;e<t.length-1;e++){const r=rs(t[e],t[e+1]);if(Math.abs(r-n)>1)return!1;n=r}return!0}(e.points)){if(null!=r){const t=r.expanded?r.x:os(r);e.points[0].x=t}if(null!=o){const t=o.expanded?o.x:os(o);e.points[2].x=t}e.points=[e.points[0],e.points[1]]}const i=e.points[e.points.length-2];null!=o&&(e.points[e.points.length-1]=is(i,o));const u=e.points[1];null!=r&&(e.points[0]=is(u,r)),e.points.forEach(t=>{a=t.x<a?t.x:a,f=t.x>f?t.x:f,c=t.y<c?t.y:c,h=t.y>h?t.y:h})}),t.nodes().forEach(n=>{const e=t.node(n);e.x-=a,e.y-=c}),t.edges().forEach(n=>{t.edge(n).points.forEach(t=>{t.x-=a,t.y-=c})}),{width:f-a,height:h-c}}(t.coreGraph,f));let h=0;t.coreGraph.nodeCount()>0&&h++;const d=h<=1?0:h;t.coreBox.width+=d+d,t.coreBox.height=r.labelHeight+t.coreBox.height,t.width=t.coreBox.width+r.paddingLeft+r.paddingRight,t.height=t.paddingTop+t.coreBox.height+t.paddingBottom}(t,n)}function rs(t,n){const e=n.x-t.x,r=n.y-t.y;return 180*Math.atan(r/e)/Math.PI}function os(t){return t.expanded?t.x:t.x-t.width/2+0+t.coreBox.width/2}function is(t,n){const e=n.expanded?n.x:os(n),r=n.y,o=t.x-e,i=t.y-r;let u,a,s=n.expanded?n.width:n.coreBox.width,c=n.expanded?n.height:n.coreBox.height;return Math.abs(i)*s/2>Math.abs(o)*c/2?(i<0&&(c=-c),u=0===i?0:c/2*o/i,a=c/2):(o<0&&(s=-s),u=s/2,a=0===o?0:s/2*i/o),{x:e+u,y:r+a}}function us(t,n,e){var o,i,u,a;const s=t.nodes.filter((t,n,e)=>e.findIndex(n=>n.id===t.id)!==n).map(t=>t.id);if(s.length)throw new Error("Duplicated ids found: "+s.join(", "));const c=function(t){const n={nodes:[]},e=t.compound,r=Object.keys(e||{}),o=new Map,i=(t,n=[])=>{if(o.has(t))return o.get(t);for(let o=0;o<r.length;o++){let u=r[o];if(e&&-1!==e[u].indexOf(t))return n.unshift(u),i(u,n)}return 0===n.length&&o.set(t,n),n},u=n=>t.edges.filter(t=>t.w===n).map(t=>({name:t.v}));return t.nodes.forEach(t=>{const e=t.id,r=[...i(e),e];let o=u(e);n.nodes.push({name:e,path:r,inputs:o,width:t.width,height:t.height,attr:Object.assign({},t)})}),n}(t),f=function(t,n){const e=(t,n)=>{for(let e of n.values())if(e.includes(t))return!0;return!1},r=(t,n=[])=>{if(0===Object.keys(t).length)return[...new Set(n)];const o=new Map(Object.keys(t).map(n=>[n,t[n]])),i={};for(let[r,u]of o)e(r,o)?i[r]=u:n=n.concat(r,u);return r(i,n)};return r(t).filter(t=>n.includes(t))}(t.compound||{},(null==n?void 0:n.expanded)||[]),h=function(t,n){return function(t){return new Za(t)}(Ka(Va(t),n))}(c,{rankDirection:(null===(i=null===(o=null==e?void 0:e.graph)||void 0===o?void 0:o.meta)||void 0===i?void 0:i.rankDir)||(null==n?void 0:n.rankDirection)||r.graph.meta.rankDir,align:(null===(a=null===(u=null==e?void 0:e.graph)||void 0===u?void 0:u.meta)||void 0===a?void 0:a.align)||r.graph.meta.align});return function(t,n){n.forEach(n=>{const e=t.getRenderInfoNodes().find(t=>t.displayName===n);let r=e&&e.node&&e.node.name||"";const o=t.getRenderNodeByName(r);if(!o)throw new Error("No nodes found: "+r);o.expanded=!0,t.buildSubHierarchy(r)})}(h,f),es(h.root,e),cs(h.root)}function as(t,n=!1){const e=JSON.parse(JSON.stringify(t)),r={nodes:[e],edges:[...e.edges]};return e.nodes.forEach((function t(n){(0===n.type||1===n.type)&&r.nodes.push(n),0===n.type&&(r.edges=r.edges.concat(n.edges)),Array.isArray(n.nodes)&&n.nodes.forEach(t)})),n&&r.nodes.forEach(t=>{const n=r.nodes.find(n=>n.id===t.parentNodeName);if(n){const e=n.x-n.width/2+n.paddingLeft,r=n.y-n.height/2+n.labelHeight+n.paddingTop;n.id!==u&&(t.x+=e,t.y+=r),0===t.type&&t.edges.forEach(n=>{n.points.forEach(n=>{n.x+=t.x-t.width/2+t.paddingLeft,n.y+=t.y-t.height/2+t.labelHeight+t.paddingTop})})}}),r}function ss(t,n,e,r){var o,i;let a=[];const s=(null===(o=e.find(n=>n.id===t))||void 0===o?void 0:o.path)||[],c=(null===(i=e.find(t=>t.id===n))||void 0===i?void 0:i.path)||[],f=[u,...s].slice(0,s.length).reverse(),h=[u,...c].slice(0,c.length);return f.forEach(o=>{const i=e.find(t=>t.id===o);a=a.concat(i.edges.filter(e=>e.baseEdgeList.some(e=>e.v===((null==r?void 0:r.v)||t)&&e.w===((null==r?void 0:r.w)||n))))}),h.filter(t=>!f.includes(t)).forEach(o=>{const i=e.find(t=>t.id===o);a=a.concat(i.edges.filter(e=>e.baseEdgeList.some(e=>e.v===((null==r?void 0:r.v)||t)&&e.w===((null==r?void 0:r.w)||n))))}),a}function cs(t){const n=t.coreGraph.nodes().map(n=>t.coreGraph.node(n));return Object.assign(Object.assign({},fs(t)),{expanded:t.expanded,nodes:t.expanded?(e=n,e.map(t=>t.node.type===s.META?cs(t):fs(t))):[],edges:t.expanded?hs(t):[]});var e}function fs(t){return{id:t.node.name,name:t.node.name,type:t.node.type,cardinality:t.node.cardinality,attr:t.attr,parentNodeName:t.node.parentNode?t.node.parentNode.name:null,coreBox:Object.assign({},t.coreBox),x:t.x,y:t.y,width:t.width,height:t.height,radius:t.radius,labelHeight:t.labelHeight,labelOffset:t.labelOffset,outboxWidth:t.outboxWidth,paddingLeft:t.paddingLeft,paddingTop:t.paddingTop,paddingRight:t.paddingRight,paddingBottom:t.paddingBottom,path:t.node.path}}function hs(t){return t.coreGraph.edges().map(n=>({renderInfoEdge:t.coreGraph.edge(n),edge:n})).filter(({renderInfoEdge:t})=>t.metaEdge).map(({edge:n,renderInfoEdge:e})=>{const r=function(t,n){const e=n.points.map(t=>Object.assign({},t));if(n.adjoiningMetaEdge){const r=n.adjoiningMetaEdge.points,o=n.metaEdge.inbound,i=o?r[r.length-1]:r[0],u=e[o?0:e.length-1],a=t.x-t.width/2,s=t.y-t.height/2,c=i.x-a,f=i.y-s,h=-t.paddingLeft,d=-(t.paddingTop+t.labelHeight);u.x=c+h,u.y=f+d}return e}(t,e);return{adjoiningEdge:e.adjoiningMetaEdge?{w:e.adjoiningMetaEdge.metaEdge.w,v:e.adjoiningMetaEdge.metaEdge.v}:null,inbound:e.metaEdge.inbound,w:n.w,v:n.v,points:r,weight:e.weight,baseEdgeList:e.metaEdge.baseEdgeList,parentNodeName:t.node.name}})}}.call(this,e("c8ba"))},"1e94":function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.ContainerSnapshot=void 0;var r=function(){function t(){}return t.of=function(n,e){var r=new t;return r.bindings=n,r.middleware=e,r},t}();n.ContainerSnapshot=r},"20e7":function(t,n,e){"use strict";e.d(n,"a",(function(){return r})),e.d(n,"b",(function(){return o})),e.d(n,"c",(function(){return u})),e.d(n,"d",(function(){return he})),e.d(n,"e",(function(){return Bt})),e.d(n,"f",(function(){return i}));var r={};e.r(r),e.d(r,"create",(function(){return s})),e.d(r,"fromMat4",(function(){return c})),e.d(r,"clone",(function(){return f})),e.d(r,"copy",(function(){return h})),e.d(r,"fromValues",(function(){return d})),e.d(r,"set",(function(){return l})),e.d(r,"identity",(function(){return g})),e.d(r,"transpose",(function(){return p})),e.d(r,"invert",(function(){return m})),e.d(r,"adjoint",(function(){return v})),e.d(r,"determinant",(function(){return y})),e.d(r,"multiply",(function(){return w})),e.d(r,"translate",(function(){return b})),e.d(r,"rotate",(function(){return M})),e.d(r,"scale",(function(){return _})),e.d(r,"fromTranslation",(function(){return E})),e.d(r,"fromRotation",(function(){return x})),e.d(r,"fromScaling",(function(){return N})),e.d(r,"fromMat2d",(function(){return S})),e.d(r,"fromQuat",(function(){return T})),e.d(r,"normalFromMat4",(function(){return A})),e.d(r,"projection",(function(){return I})),e.d(r,"str",(function(){return O})),e.d(r,"frob",(function(){return C})),e.d(r,"add",(function(){return j})),e.d(r,"subtract",(function(){return R})),e.d(r,"multiplyScalar",(function(){return k})),e.d(r,"multiplyScalarAndAdd",(function(){return P})),e.d(r,"exactEquals",(function(){return D})),e.d(r,"equals",(function(){return B})),e.d(r,"mul",(function(){return L})),e.d(r,"sub",(function(){return G}));var o={};e.r(o),e.d(o,"create",(function(){return q})),e.d(o,"clone",(function(){return F})),e.d(o,"copy",(function(){return U})),e.d(o,"fromValues",(function(){return V})),e.d(o,"set",(function(){return z})),e.d(o,"identity",(function(){return W})),e.d(o,"transpose",(function(){return Y})),e.d(o,"invert",(function(){return H})),e.d(o,"adjoint",(function(){return $})),e.d(o,"determinant",(function(){return J})),e.d(o,"multiply",(function(){return K})),e.d(o,"translate",(function(){return Q})),e.d(o,"scale",(function(){return Z})),e.d(o,"rotate",(function(){return X})),e.d(o,"rotateX",(function(){return tt})),e.d(o,"rotateY",(function(){return nt})),e.d(o,"rotateZ",(function(){return et})),e.d(o,"fromTranslation",(function(){return rt})),e.d(o,"fromScaling",(function(){return ot})),e.d(o,"fromRotation",(function(){return it})),e.d(o,"fromXRotation",(function(){return ut})),e.d(o,"fromYRotation",(function(){return at})),e.d(o,"fromZRotation",(function(){return st})),e.d(o,"fromRotationTranslation",(function(){return ct})),e.d(o,"fromQuat2",(function(){return ft})),e.d(o,"getTranslation",(function(){return ht})),e.d(o,"getScaling",(function(){return dt})),e.d(o,"getRotation",(function(){return lt})),e.d(o,"fromRotationTranslationScale",(function(){return gt})),e.d(o,"fromRotationTranslationScaleOrigin",(function(){return pt})),e.d(o,"fromQuat",(function(){return mt})),e.d(o,"frustum",(function(){return vt})),e.d(o,"perspectiveNO",(function(){return yt})),e.d(o,"perspective",(function(){return wt})),e.d(o,"perspectiveZO",(function(){return bt})),e.d(o,"perspectiveFromFieldOfView",(function(){return Mt})),e.d(o,"orthoNO",(function(){return _t})),e.d(o,"ortho",(function(){return Et})),e.d(o,"orthoZO",(function(){return xt})),e.d(o,"lookAt",(function(){return Nt})),e.d(o,"targetTo",(function(){return St})),e.d(o,"str",(function(){return Tt})),e.d(o,"frob",(function(){return At})),e.d(o,"add",(function(){return It})),e.d(o,"subtract",(function(){return Ot})),e.d(o,"multiplyScalar",(function(){return Ct})),e.d(o,"multiplyScalarAndAdd",(function(){return jt})),e.d(o,"exactEquals",(function(){return Rt})),e.d(o,"equals",(function(){return kt})),e.d(o,"mul",(function(){return Pt})),e.d(o,"sub",(function(){return Dt}));var i={};e.r(i),e.d(i,"create",(function(){return Lt})),e.d(i,"clone",(function(){return Gt})),e.d(i,"fromValues",(function(){return qt})),e.d(i,"copy",(function(){return Ft})),e.d(i,"set",(function(){return Ut})),e.d(i,"add",(function(){return Vt})),e.d(i,"subtract",(function(){return zt})),e.d(i,"multiply",(function(){return Wt})),e.d(i,"divide",(function(){return Yt})),e.d(i,"ceil",(function(){return Ht})),e.d(i,"floor",(function(){return $t})),e.d(i,"min",(function(){return Jt})),e.d(i,"max",(function(){return Kt})),e.d(i,"round",(function(){return Qt})),e.d(i,"scale",(function(){return Zt})),e.d(i,"scaleAndAdd",(function(){return Xt})),e.d(i,"distance",(function(){return tn})),e.d(i,"squaredDistance",(function(){return nn})),e.d(i,"length",(function(){return en})),e.d(i,"squaredLength",(function(){return rn})),e.d(i,"negate",(function(){return on})),e.d(i,"inverse",(function(){return un})),e.d(i,"normalize",(function(){return an})),e.d(i,"dot",(function(){return sn})),e.d(i,"cross",(function(){return cn})),e.d(i,"lerp",(function(){return fn})),e.d(i,"random",(function(){return hn})),e.d(i,"transformMat4",(function(){return dn})),e.d(i,"transformQuat",(function(){return ln})),e.d(i,"zero",(function(){return gn})),e.d(i,"str",(function(){return pn})),e.d(i,"exactEquals",(function(){return mn})),e.d(i,"equals",(function(){return vn})),e.d(i,"sub",(function(){return yn})),e.d(i,"mul",(function(){return wn})),e.d(i,"div",(function(){return bn})),e.d(i,"dist",(function(){return Mn})),e.d(i,"sqrDist",(function(){return _n})),e.d(i,"len",(function(){return En})),e.d(i,"sqrLen",(function(){return xn})),e.d(i,"forEach",(function(){return Nn}));var u={};e.r(u),e.d(u,"create",(function(){return Sn})),e.d(u,"identity",(function(){return Tn})),e.d(u,"setAxisAngle",(function(){return An})),e.d(u,"getAxisAngle",(function(){return In})),e.d(u,"getAngle",(function(){return On})),e.d(u,"multiply",(function(){return Cn})),e.d(u,"rotateX",(function(){return jn})),e.d(u,"rotateY",(function(){return Rn})),e.d(u,"rotateZ",(function(){return kn})),e.d(u,"calculateW",(function(){return Pn})),e.d(u,"exp",(function(){return Dn})),e.d(u,"ln",(function(){return Bn})),e.d(u,"pow",(function(){return Ln})),e.d(u,"slerp",(function(){return Gn})),e.d(u,"random",(function(){return qn})),e.d(u,"invert",(function(){return Fn})),e.d(u,"conjugate",(function(){return Un})),e.d(u,"fromMat3",(function(){return Vn})),e.d(u,"fromEuler",(function(){return zn})),e.d(u,"str",(function(){return Wn})),e.d(u,"clone",(function(){return Yn})),e.d(u,"fromValues",(function(){return Hn})),e.d(u,"copy",(function(){return $n})),e.d(u,"set",(function(){return Jn})),e.d(u,"add",(function(){return Kn})),e.d(u,"mul",(function(){return Qn})),e.d(u,"scale",(function(){return Zn})),e.d(u,"dot",(function(){return Xn})),e.d(u,"lerp",(function(){return te})),e.d(u,"length",(function(){return ne})),e.d(u,"len",(function(){return ee})),e.d(u,"squaredLength",(function(){return re})),e.d(u,"sqrLen",(function(){return oe})),e.d(u,"normalize",(function(){return ie})),e.d(u,"exactEquals",(function(){return ue})),e.d(u,"equals",(function(){return ae})),e.d(u,"rotationTo",(function(){return se})),e.d(u,"sqlerp",(function(){return ce})),e.d(u,"setAxes",(function(){return fe}));var a=e("c94d");function s(){var t=new a["a"](9);return a["a"]!=Float32Array&&(t[1]=0,t[2]=0,t[3]=0,t[5]=0,t[6]=0,t[7]=0),t[0]=1,t[4]=1,t[8]=1,t}function c(t,n){return t[0]=n[0],t[1]=n[1],t[2]=n[2],t[3]=n[4],t[4]=n[5],t[5]=n[6],t[6]=n[8],t[7]=n[9],t[8]=n[10],t}function f(t){var n=new a["a"](9);return n[0]=t[0],n[1]=t[1],n[2]=t[2],n[3]=t[3],n[4]=t[4],n[5]=t[5],n[6]=t[6],n[7]=t[7],n[8]=t[8],n}function h(t,n){return t[0]=n[0],t[1]=n[1],t[2]=n[2],t[3]=n[3],t[4]=n[4],t[5]=n[5],t[6]=n[6],t[7]=n[7],t[8]=n[8],t}function d(t,n,e,r,o,i,u,s,c){var f=new a["a"](9);return f[0]=t,f[1]=n,f[2]=e,f[3]=r,f[4]=o,f[5]=i,f[6]=u,f[7]=s,f[8]=c,f}function l(t,n,e,r,o,i,u,a,s,c){return t[0]=n,t[1]=e,t[2]=r,t[3]=o,t[4]=i,t[5]=u,t[6]=a,t[7]=s,t[8]=c,t}function g(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=1,t[5]=0,t[6]=0,t[7]=0,t[8]=1,t}function p(t,n){if(t===n){var e=n[1],r=n[2],o=n[5];t[1]=n[3],t[2]=n[6],t[3]=e,t[5]=n[7],t[6]=r,t[7]=o}else t[0]=n[0],t[1]=n[3],t[2]=n[6],t[3]=n[1],t[4]=n[4],t[5]=n[7],t[6]=n[2],t[7]=n[5],t[8]=n[8];return t}function m(t,n){var e=n[0],r=n[1],o=n[2],i=n[3],u=n[4],a=n[5],s=n[6],c=n[7],f=n[8],h=f*u-a*c,d=-f*i+a*s,l=c*i-u*s,g=e*h+r*d+o*l;return g?(g=1/g,t[0]=h*g,t[1]=(-f*r+o*c)*g,t[2]=(a*r-o*u)*g,t[3]=d*g,t[4]=(f*e-o*s)*g,t[5]=(-a*e+o*i)*g,t[6]=l*g,t[7]=(-c*e+r*s)*g,t[8]=(u*e-r*i)*g,t):null}function v(t,n){var e=n[0],r=n[1],o=n[2],i=n[3],u=n[4],a=n[5],s=n[6],c=n[7],f=n[8];return t[0]=u*f-a*c,t[1]=o*c-r*f,t[2]=r*a-o*u,t[3]=a*s-i*f,t[4]=e*f-o*s,t[5]=o*i-e*a,t[6]=i*c-u*s,t[7]=r*s-e*c,t[8]=e*u-r*i,t}function y(t){var n=t[0],e=t[1],r=t[2],o=t[3],i=t[4],u=t[5],a=t[6],s=t[7],c=t[8];return n*(c*i-u*s)+e*(-c*o+u*a)+r*(s*o-i*a)}function w(t,n,e){var r=n[0],o=n[1],i=n[2],u=n[3],a=n[4],s=n[5],c=n[6],f=n[7],h=n[8],d=e[0],l=e[1],g=e[2],p=e[3],m=e[4],v=e[5],y=e[6],w=e[7],b=e[8];return t[0]=d*r+l*u+g*c,t[1]=d*o+l*a+g*f,t[2]=d*i+l*s+g*h,t[3]=p*r+m*u+v*c,t[4]=p*o+m*a+v*f,t[5]=p*i+m*s+v*h,t[6]=y*r+w*u+b*c,t[7]=y*o+w*a+b*f,t[8]=y*i+w*s+b*h,t}function b(t,n,e){var r=n[0],o=n[1],i=n[2],u=n[3],a=n[4],s=n[5],c=n[6],f=n[7],h=n[8],d=e[0],l=e[1];return t[0]=r,t[1]=o,t[2]=i,t[3]=u,t[4]=a,t[5]=s,t[6]=d*r+l*u+c,t[7]=d*o+l*a+f,t[8]=d*i+l*s+h,t}function M(t,n,e){var r=n[0],o=n[1],i=n[2],u=n[3],a=n[4],s=n[5],c=n[6],f=n[7],h=n[8],d=Math.sin(e),l=Math.cos(e);return t[0]=l*r+d*u,t[1]=l*o+d*a,t[2]=l*i+d*s,t[3]=l*u-d*r,t[4]=l*a-d*o,t[5]=l*s-d*i,t[6]=c,t[7]=f,t[8]=h,t}function _(t,n,e){var r=e[0],o=e[1];return t[0]=r*n[0],t[1]=r*n[1],t[2]=r*n[2],t[3]=o*n[3],t[4]=o*n[4],t[5]=o*n[5],t[6]=n[6],t[7]=n[7],t[8]=n[8],t}function E(t,n){return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=1,t[5]=0,t[6]=n[0],t[7]=n[1],t[8]=1,t}function x(t,n){var e=Math.sin(n),r=Math.cos(n);return t[0]=r,t[1]=e,t[2]=0,t[3]=-e,t[4]=r,t[5]=0,t[6]=0,t[7]=0,t[8]=1,t}function N(t,n){return t[0]=n[0],t[1]=0,t[2]=0,t[3]=0,t[4]=n[1],t[5]=0,t[6]=0,t[7]=0,t[8]=1,t}function S(t,n){return t[0]=n[0],t[1]=n[1],t[2]=0,t[3]=n[2],t[4]=n[3],t[5]=0,t[6]=n[4],t[7]=n[5],t[8]=1,t}function T(t,n){var e=n[0],r=n[1],o=n[2],i=n[3],u=e+e,a=r+r,s=o+o,c=e*u,f=r*u,h=r*a,d=o*u,l=o*a,g=o*s,p=i*u,m=i*a,v=i*s;return t[0]=1-h-g,t[3]=f-v,t[6]=d+m,t[1]=f+v,t[4]=1-c-g,t[7]=l-p,t[2]=d-m,t[5]=l+p,t[8]=1-c-h,t}function A(t,n){var e=n[0],r=n[1],o=n[2],i=n[3],u=n[4],a=n[5],s=n[6],c=n[7],f=n[8],h=n[9],d=n[10],l=n[11],g=n[12],p=n[13],m=n[14],v=n[15],y=e*a-r*u,w=e*s-o*u,b=e*c-i*u,M=r*s-o*a,_=r*c-i*a,E=o*c-i*s,x=f*p-h*g,N=f*m-d*g,S=f*v-l*g,T=h*m-d*p,A=h*v-l*p,I=d*v-l*m,O=y*I-w*A+b*T+M*S-_*N+E*x;return O?(O=1/O,t[0]=(a*I-s*A+c*T)*O,t[1]=(s*S-u*I-c*N)*O,t[2]=(u*A-a*S+c*x)*O,t[3]=(o*A-r*I-i*T)*O,t[4]=(e*I-o*S+i*N)*O,t[5]=(r*S-e*A-i*x)*O,t[6]=(p*E-m*_+v*M)*O,t[7]=(m*b-g*E-v*w)*O,t[8]=(g*_-p*b+v*y)*O,t):null}function I(t,n,e){return t[0]=2/n,t[1]=0,t[2]=0,t[3]=0,t[4]=-2/e,t[5]=0,t[6]=-1,t[7]=1,t[8]=1,t}function O(t){return"mat3("+t[0]+", "+t[1]+", "+t[2]+", "+t[3]+", "+t[4]+", "+t[5]+", "+t[6]+", "+t[7]+", "+t[8]+")"}function C(t){return Math.hypot(t[0],t[1],t[2],t[3],t[4],t[5],t[6],t[7],t[8])}function j(t,n,e){return t[0]=n[0]+e[0],t[1]=n[1]+e[1],t[2]=n[2]+e[2],t[3]=n[3]+e[3],t[4]=n[4]+e[4],t[5]=n[5]+e[5],t[6]=n[6]+e[6],t[7]=n[7]+e[7],t[8]=n[8]+e[8],t}function R(t,n,e){return t[0]=n[0]-e[0],t[1]=n[1]-e[1],t[2]=n[2]-e[2],t[3]=n[3]-e[3],t[4]=n[4]-e[4],t[5]=n[5]-e[5],t[6]=n[6]-e[6],t[7]=n[7]-e[7],t[8]=n[8]-e[8],t}function k(t,n,e){return t[0]=n[0]*e,t[1]=n[1]*e,t[2]=n[2]*e,t[3]=n[3]*e,t[4]=n[4]*e,t[5]=n[5]*e,t[6]=n[6]*e,t[7]=n[7]*e,t[8]=n[8]*e,t}function P(t,n,e,r){return t[0]=n[0]+e[0]*r,t[1]=n[1]+e[1]*r,t[2]=n[2]+e[2]*r,t[3]=n[3]+e[3]*r,t[4]=n[4]+e[4]*r,t[5]=n[5]+e[5]*r,t[6]=n[6]+e[6]*r,t[7]=n[7]+e[7]*r,t[8]=n[8]+e[8]*r,t}function D(t,n){return t[0]===n[0]&&t[1]===n[1]&&t[2]===n[2]&&t[3]===n[3]&&t[4]===n[4]&&t[5]===n[5]&&t[6]===n[6]&&t[7]===n[7]&&t[8]===n[8]}function B(t,n){var e=t[0],r=t[1],o=t[2],i=t[3],u=t[4],s=t[5],c=t[6],f=t[7],h=t[8],d=n[0],l=n[1],g=n[2],p=n[3],m=n[4],v=n[5],y=n[6],w=n[7],b=n[8];return Math.abs(e-d)<=a["b"]*Math.max(1,Math.abs(e),Math.abs(d))&&Math.abs(r-l)<=a["b"]*Math.max(1,Math.abs(r),Math.abs(l))&&Math.abs(o-g)<=a["b"]*Math.max(1,Math.abs(o),Math.abs(g))&&Math.abs(i-p)<=a["b"]*Math.max(1,Math.abs(i),Math.abs(p))&&Math.abs(u-m)<=a["b"]*Math.max(1,Math.abs(u),Math.abs(m))&&Math.abs(s-v)<=a["b"]*Math.max(1,Math.abs(s),Math.abs(v))&&Math.abs(c-y)<=a["b"]*Math.max(1,Math.abs(c),Math.abs(y))&&Math.abs(f-w)<=a["b"]*Math.max(1,Math.abs(f),Math.abs(w))&&Math.abs(h-b)<=a["b"]*Math.max(1,Math.abs(h),Math.abs(b))}var L=w,G=R;function q(){var t=new a["a"](16);return a["a"]!=Float32Array&&(t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[11]=0,t[12]=0,t[13]=0,t[14]=0),t[0]=1,t[5]=1,t[10]=1,t[15]=1,t}function F(t){var n=new a["a"](16);return n[0]=t[0],n[1]=t[1],n[2]=t[2],n[3]=t[3],n[4]=t[4],n[5]=t[5],n[6]=t[6],n[7]=t[7],n[8]=t[8],n[9]=t[9],n[10]=t[10],n[11]=t[11],n[12]=t[12],n[13]=t[13],n[14]=t[14],n[15]=t[15],n}function U(t,n){return t[0]=n[0],t[1]=n[1],t[2]=n[2],t[3]=n[3],t[4]=n[4],t[5]=n[5],t[6]=n[6],t[7]=n[7],t[8]=n[8],t[9]=n[9],t[10]=n[10],t[11]=n[11],t[12]=n[12],t[13]=n[13],t[14]=n[14],t[15]=n[15],t}function V(t,n,e,r,o,i,u,s,c,f,h,d,l,g,p,m){var v=new a["a"](16);return v[0]=t,v[1]=n,v[2]=e,v[3]=r,v[4]=o,v[5]=i,v[6]=u,v[7]=s,v[8]=c,v[9]=f,v[10]=h,v[11]=d,v[12]=l,v[13]=g,v[14]=p,v[15]=m,v}function z(t,n,e,r,o,i,u,a,s,c,f,h,d,l,g,p,m){return t[0]=n,t[1]=e,t[2]=r,t[3]=o,t[4]=i,t[5]=u,t[6]=a,t[7]=s,t[8]=c,t[9]=f,t[10]=h,t[11]=d,t[12]=l,t[13]=g,t[14]=p,t[15]=m,t}function W(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=1,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=1,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t}function Y(t,n){if(t===n){var e=n[1],r=n[2],o=n[3],i=n[6],u=n[7],a=n[11];t[1]=n[4],t[2]=n[8],t[3]=n[12],t[4]=e,t[6]=n[9],t[7]=n[13],t[8]=r,t[9]=i,t[11]=n[14],t[12]=o,t[13]=u,t[14]=a}else t[0]=n[0],t[1]=n[4],t[2]=n[8],t[3]=n[12],t[4]=n[1],t[5]=n[5],t[6]=n[9],t[7]=n[13],t[8]=n[2],t[9]=n[6],t[10]=n[10],t[11]=n[14],t[12]=n[3],t[13]=n[7],t[14]=n[11],t[15]=n[15];return t}function H(t,n){var e=n[0],r=n[1],o=n[2],i=n[3],u=n[4],a=n[5],s=n[6],c=n[7],f=n[8],h=n[9],d=n[10],l=n[11],g=n[12],p=n[13],m=n[14],v=n[15],y=e*a-r*u,w=e*s-o*u,b=e*c-i*u,M=r*s-o*a,_=r*c-i*a,E=o*c-i*s,x=f*p-h*g,N=f*m-d*g,S=f*v-l*g,T=h*m-d*p,A=h*v-l*p,I=d*v-l*m,O=y*I-w*A+b*T+M*S-_*N+E*x;return O?(O=1/O,t[0]=(a*I-s*A+c*T)*O,t[1]=(o*A-r*I-i*T)*O,t[2]=(p*E-m*_+v*M)*O,t[3]=(d*_-h*E-l*M)*O,t[4]=(s*S-u*I-c*N)*O,t[5]=(e*I-o*S+i*N)*O,t[6]=(m*b-g*E-v*w)*O,t[7]=(f*E-d*b+l*w)*O,t[8]=(u*A-a*S+c*x)*O,t[9]=(r*S-e*A-i*x)*O,t[10]=(g*_-p*b+v*y)*O,t[11]=(h*b-f*_-l*y)*O,t[12]=(a*N-u*T-s*x)*O,t[13]=(e*T-r*N+o*x)*O,t[14]=(p*w-g*M-m*y)*O,t[15]=(f*M-h*w+d*y)*O,t):null}function $(t,n){var e=n[0],r=n[1],o=n[2],i=n[3],u=n[4],a=n[5],s=n[6],c=n[7],f=n[8],h=n[9],d=n[10],l=n[11],g=n[12],p=n[13],m=n[14],v=n[15];return t[0]=a*(d*v-l*m)-h*(s*v-c*m)+p*(s*l-c*d),t[1]=-(r*(d*v-l*m)-h*(o*v-i*m)+p*(o*l-i*d)),t[2]=r*(s*v-c*m)-a*(o*v-i*m)+p*(o*c-i*s),t[3]=-(r*(s*l-c*d)-a*(o*l-i*d)+h*(o*c-i*s)),t[4]=-(u*(d*v-l*m)-f*(s*v-c*m)+g*(s*l-c*d)),t[5]=e*(d*v-l*m)-f*(o*v-i*m)+g*(o*l-i*d),t[6]=-(e*(s*v-c*m)-u*(o*v-i*m)+g*(o*c-i*s)),t[7]=e*(s*l-c*d)-u*(o*l-i*d)+f*(o*c-i*s),t[8]=u*(h*v-l*p)-f*(a*v-c*p)+g*(a*l-c*h),t[9]=-(e*(h*v-l*p)-f*(r*v-i*p)+g*(r*l-i*h)),t[10]=e*(a*v-c*p)-u*(r*v-i*p)+g*(r*c-i*a),t[11]=-(e*(a*l-c*h)-u*(r*l-i*h)+f*(r*c-i*a)),t[12]=-(u*(h*m-d*p)-f*(a*m-s*p)+g*(a*d-s*h)),t[13]=e*(h*m-d*p)-f*(r*m-o*p)+g*(r*d-o*h),t[14]=-(e*(a*m-s*p)-u*(r*m-o*p)+g*(r*s-o*a)),t[15]=e*(a*d-s*h)-u*(r*d-o*h)+f*(r*s-o*a),t}function J(t){var n=t[0],e=t[1],r=t[2],o=t[3],i=t[4],u=t[5],a=t[6],s=t[7],c=t[8],f=t[9],h=t[10],d=t[11],l=t[12],g=t[13],p=t[14],m=t[15],v=n*u-e*i,y=n*a-r*i,w=n*s-o*i,b=e*a-r*u,M=e*s-o*u,_=r*s-o*a,E=c*g-f*l,x=c*p-h*l,N=c*m-d*l,S=f*p-h*g,T=f*m-d*g,A=h*m-d*p;return v*A-y*T+w*S+b*N-M*x+_*E}function K(t,n,e){var r=n[0],o=n[1],i=n[2],u=n[3],a=n[4],s=n[5],c=n[6],f=n[7],h=n[8],d=n[9],l=n[10],g=n[11],p=n[12],m=n[13],v=n[14],y=n[15],w=e[0],b=e[1],M=e[2],_=e[3];return t[0]=w*r+b*a+M*h+_*p,t[1]=w*o+b*s+M*d+_*m,t[2]=w*i+b*c+M*l+_*v,t[3]=w*u+b*f+M*g+_*y,w=e[4],b=e[5],M=e[6],_=e[7],t[4]=w*r+b*a+M*h+_*p,t[5]=w*o+b*s+M*d+_*m,t[6]=w*i+b*c+M*l+_*v,t[7]=w*u+b*f+M*g+_*y,w=e[8],b=e[9],M=e[10],_=e[11],t[8]=w*r+b*a+M*h+_*p,t[9]=w*o+b*s+M*d+_*m,t[10]=w*i+b*c+M*l+_*v,t[11]=w*u+b*f+M*g+_*y,w=e[12],b=e[13],M=e[14],_=e[15],t[12]=w*r+b*a+M*h+_*p,t[13]=w*o+b*s+M*d+_*m,t[14]=w*i+b*c+M*l+_*v,t[15]=w*u+b*f+M*g+_*y,t}function Q(t,n,e){var r,o,i,u,a,s,c,f,h,d,l,g,p=e[0],m=e[1],v=e[2];return n===t?(t[12]=n[0]*p+n[4]*m+n[8]*v+n[12],t[13]=n[1]*p+n[5]*m+n[9]*v+n[13],t[14]=n[2]*p+n[6]*m+n[10]*v+n[14],t[15]=n[3]*p+n[7]*m+n[11]*v+n[15]):(r=n[0],o=n[1],i=n[2],u=n[3],a=n[4],s=n[5],c=n[6],f=n[7],h=n[8],d=n[9],l=n[10],g=n[11],t[0]=r,t[1]=o,t[2]=i,t[3]=u,t[4]=a,t[5]=s,t[6]=c,t[7]=f,t[8]=h,t[9]=d,t[10]=l,t[11]=g,t[12]=r*p+a*m+h*v+n[12],t[13]=o*p+s*m+d*v+n[13],t[14]=i*p+c*m+l*v+n[14],t[15]=u*p+f*m+g*v+n[15]),t}function Z(t,n,e){var r=e[0],o=e[1],i=e[2];return t[0]=n[0]*r,t[1]=n[1]*r,t[2]=n[2]*r,t[3]=n[3]*r,t[4]=n[4]*o,t[5]=n[5]*o,t[6]=n[6]*o,t[7]=n[7]*o,t[8]=n[8]*i,t[9]=n[9]*i,t[10]=n[10]*i,t[11]=n[11]*i,t[12]=n[12],t[13]=n[13],t[14]=n[14],t[15]=n[15],t}function X(t,n,e,r){var o,i,u,s,c,f,h,d,l,g,p,m,v,y,w,b,M,_,E,x,N,S,T,A,I=r[0],O=r[1],C=r[2],j=Math.hypot(I,O,C);return j<a["b"]?null:(j=1/j,I*=j,O*=j,C*=j,o=Math.sin(e),i=Math.cos(e),u=1-i,s=n[0],c=n[1],f=n[2],h=n[3],d=n[4],l=n[5],g=n[6],p=n[7],m=n[8],v=n[9],y=n[10],w=n[11],b=I*I*u+i,M=O*I*u+C*o,_=C*I*u-O*o,E=I*O*u-C*o,x=O*O*u+i,N=C*O*u+I*o,S=I*C*u+O*o,T=O*C*u-I*o,A=C*C*u+i,t[0]=s*b+d*M+m*_,t[1]=c*b+l*M+v*_,t[2]=f*b+g*M+y*_,t[3]=h*b+p*M+w*_,t[4]=s*E+d*x+m*N,t[5]=c*E+l*x+v*N,t[6]=f*E+g*x+y*N,t[7]=h*E+p*x+w*N,t[8]=s*S+d*T+m*A,t[9]=c*S+l*T+v*A,t[10]=f*S+g*T+y*A,t[11]=h*S+p*T+w*A,n!==t&&(t[12]=n[12],t[13]=n[13],t[14]=n[14],t[15]=n[15]),t)}function tt(t,n,e){var r=Math.sin(e),o=Math.cos(e),i=n[4],u=n[5],a=n[6],s=n[7],c=n[8],f=n[9],h=n[10],d=n[11];return n!==t&&(t[0]=n[0],t[1]=n[1],t[2]=n[2],t[3]=n[3],t[12]=n[12],t[13]=n[13],t[14]=n[14],t[15]=n[15]),t[4]=i*o+c*r,t[5]=u*o+f*r,t[6]=a*o+h*r,t[7]=s*o+d*r,t[8]=c*o-i*r,t[9]=f*o-u*r,t[10]=h*o-a*r,t[11]=d*o-s*r,t}function nt(t,n,e){var r=Math.sin(e),o=Math.cos(e),i=n[0],u=n[1],a=n[2],s=n[3],c=n[8],f=n[9],h=n[10],d=n[11];return n!==t&&(t[4]=n[4],t[5]=n[5],t[6]=n[6],t[7]=n[7],t[12]=n[12],t[13]=n[13],t[14]=n[14],t[15]=n[15]),t[0]=i*o-c*r,t[1]=u*o-f*r,t[2]=a*o-h*r,t[3]=s*o-d*r,t[8]=i*r+c*o,t[9]=u*r+f*o,t[10]=a*r+h*o,t[11]=s*r+d*o,t}function et(t,n,e){var r=Math.sin(e),o=Math.cos(e),i=n[0],u=n[1],a=n[2],s=n[3],c=n[4],f=n[5],h=n[6],d=n[7];return n!==t&&(t[8]=n[8],t[9]=n[9],t[10]=n[10],t[11]=n[11],t[12]=n[12],t[13]=n[13],t[14]=n[14],t[15]=n[15]),t[0]=i*o+c*r,t[1]=u*o+f*r,t[2]=a*o+h*r,t[3]=s*o+d*r,t[4]=c*o-i*r,t[5]=f*o-u*r,t[6]=h*o-a*r,t[7]=d*o-s*r,t}function rt(t,n){return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=1,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=1,t[11]=0,t[12]=n[0],t[13]=n[1],t[14]=n[2],t[15]=1,t}function ot(t,n){return t[0]=n[0],t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=n[1],t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=n[2],t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t}function it(t,n,e){var r,o,i,u=e[0],s=e[1],c=e[2],f=Math.hypot(u,s,c);return f<a["b"]?null:(f=1/f,u*=f,s*=f,c*=f,r=Math.sin(n),o=Math.cos(n),i=1-o,t[0]=u*u*i+o,t[1]=s*u*i+c*r,t[2]=c*u*i-s*r,t[3]=0,t[4]=u*s*i-c*r,t[5]=s*s*i+o,t[6]=c*s*i+u*r,t[7]=0,t[8]=u*c*i+s*r,t[9]=s*c*i-u*r,t[10]=c*c*i+o,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t)}function ut(t,n){var e=Math.sin(n),r=Math.cos(n);return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=r,t[6]=e,t[7]=0,t[8]=0,t[9]=-e,t[10]=r,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t}function at(t,n){var e=Math.sin(n),r=Math.cos(n);return t[0]=r,t[1]=0,t[2]=-e,t[3]=0,t[4]=0,t[5]=1,t[6]=0,t[7]=0,t[8]=e,t[9]=0,t[10]=r,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t}function st(t,n){var e=Math.sin(n),r=Math.cos(n);return t[0]=r,t[1]=e,t[2]=0,t[3]=0,t[4]=-e,t[5]=r,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=1,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t}function ct(t,n,e){var r=n[0],o=n[1],i=n[2],u=n[3],a=r+r,s=o+o,c=i+i,f=r*a,h=r*s,d=r*c,l=o*s,g=o*c,p=i*c,m=u*a,v=u*s,y=u*c;return t[0]=1-(l+p),t[1]=h+y,t[2]=d-v,t[3]=0,t[4]=h-y,t[5]=1-(f+p),t[6]=g+m,t[7]=0,t[8]=d+v,t[9]=g-m,t[10]=1-(f+l),t[11]=0,t[12]=e[0],t[13]=e[1],t[14]=e[2],t[15]=1,t}function ft(t,n){var e=new a["a"](3),r=-n[0],o=-n[1],i=-n[2],u=n[3],s=n[4],c=n[5],f=n[6],h=n[7],d=r*r+o*o+i*i+u*u;return d>0?(e[0]=2*(s*u+h*r+c*i-f*o)/d,e[1]=2*(c*u+h*o+f*r-s*i)/d,e[2]=2*(f*u+h*i+s*o-c*r)/d):(e[0]=2*(s*u+h*r+c*i-f*o),e[1]=2*(c*u+h*o+f*r-s*i),e[2]=2*(f*u+h*i+s*o-c*r)),ct(t,n,e),t}function ht(t,n){return t[0]=n[12],t[1]=n[13],t[2]=n[14],t}function dt(t,n){var e=n[0],r=n[1],o=n[2],i=n[4],u=n[5],a=n[6],s=n[8],c=n[9],f=n[10];return t[0]=Math.hypot(e,r,o),t[1]=Math.hypot(i,u,a),t[2]=Math.hypot(s,c,f),t}function lt(t,n){var e=new a["a"](3);dt(e,n);var r=1/e[0],o=1/e[1],i=1/e[2],u=n[0]*r,s=n[1]*o,c=n[2]*i,f=n[4]*r,h=n[5]*o,d=n[6]*i,l=n[8]*r,g=n[9]*o,p=n[10]*i,m=u+h+p,v=0;return m>0?(v=2*Math.sqrt(m+1),t[3]=.25*v,t[0]=(d-g)/v,t[1]=(l-c)/v,t[2]=(s-f)/v):u>h&&u>p?(v=2*Math.sqrt(1+u-h-p),t[3]=(d-g)/v,t[0]=.25*v,t[1]=(s+f)/v,t[2]=(l+c)/v):h>p?(v=2*Math.sqrt(1+h-u-p),t[3]=(l-c)/v,t[0]=(s+f)/v,t[1]=.25*v,t[2]=(d+g)/v):(v=2*Math.sqrt(1+p-u-h),t[3]=(s-f)/v,t[0]=(l+c)/v,t[1]=(d+g)/v,t[2]=.25*v),t}function gt(t,n,e,r){var o=n[0],i=n[1],u=n[2],a=n[3],s=o+o,c=i+i,f=u+u,h=o*s,d=o*c,l=o*f,g=i*c,p=i*f,m=u*f,v=a*s,y=a*c,w=a*f,b=r[0],M=r[1],_=r[2];return t[0]=(1-(g+m))*b,t[1]=(d+w)*b,t[2]=(l-y)*b,t[3]=0,t[4]=(d-w)*M,t[5]=(1-(h+m))*M,t[6]=(p+v)*M,t[7]=0,t[8]=(l+y)*_,t[9]=(p-v)*_,t[10]=(1-(h+g))*_,t[11]=0,t[12]=e[0],t[13]=e[1],t[14]=e[2],t[15]=1,t}function pt(t,n,e,r,o){var i=n[0],u=n[1],a=n[2],s=n[3],c=i+i,f=u+u,h=a+a,d=i*c,l=i*f,g=i*h,p=u*f,m=u*h,v=a*h,y=s*c,w=s*f,b=s*h,M=r[0],_=r[1],E=r[2],x=o[0],N=o[1],S=o[2],T=(1-(p+v))*M,A=(l+b)*M,I=(g-w)*M,O=(l-b)*_,C=(1-(d+v))*_,j=(m+y)*_,R=(g+w)*E,k=(m-y)*E,P=(1-(d+p))*E;return t[0]=T,t[1]=A,t[2]=I,t[3]=0,t[4]=O,t[5]=C,t[6]=j,t[7]=0,t[8]=R,t[9]=k,t[10]=P,t[11]=0,t[12]=e[0]+x-(T*x+O*N+R*S),t[13]=e[1]+N-(A*x+C*N+k*S),t[14]=e[2]+S-(I*x+j*N+P*S),t[15]=1,t}function mt(t,n){var e=n[0],r=n[1],o=n[2],i=n[3],u=e+e,a=r+r,s=o+o,c=e*u,f=r*u,h=r*a,d=o*u,l=o*a,g=o*s,p=i*u,m=i*a,v=i*s;return t[0]=1-h-g,t[1]=f+v,t[2]=d-m,t[3]=0,t[4]=f-v,t[5]=1-c-g,t[6]=l+p,t[7]=0,t[8]=d+m,t[9]=l-p,t[10]=1-c-h,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t}function vt(t,n,e,r,o,i,u){var a=1/(e-n),s=1/(o-r),c=1/(i-u);return t[0]=2*i*a,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=2*i*s,t[6]=0,t[7]=0,t[8]=(e+n)*a,t[9]=(o+r)*s,t[10]=(u+i)*c,t[11]=-1,t[12]=0,t[13]=0,t[14]=u*i*2*c,t[15]=0,t}function yt(t,n,e,r,o){var i,u=1/Math.tan(n/2);return t[0]=u/e,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=u,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[11]=-1,t[12]=0,t[13]=0,t[15]=0,null!=o&&o!==1/0?(i=1/(r-o),t[10]=(o+r)*i,t[14]=2*o*r*i):(t[10]=-1,t[14]=-2*r),t}var wt=yt;function bt(t,n,e,r,o){var i,u=1/Math.tan(n/2);return t[0]=u/e,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=u,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[11]=-1,t[12]=0,t[13]=0,t[15]=0,null!=o&&o!==1/0?(i=1/(r-o),t[10]=o*i,t[14]=o*r*i):(t[10]=-1,t[14]=-r),t}function Mt(t,n,e,r){var o=Math.tan(n.upDegrees*Math.PI/180),i=Math.tan(n.downDegrees*Math.PI/180),u=Math.tan(n.leftDegrees*Math.PI/180),a=Math.tan(n.rightDegrees*Math.PI/180),s=2/(u+a),c=2/(o+i);return t[0]=s,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=c,t[6]=0,t[7]=0,t[8]=-(u-a)*s*.5,t[9]=(o-i)*c*.5,t[10]=r/(e-r),t[11]=-1,t[12]=0,t[13]=0,t[14]=r*e/(e-r),t[15]=0,t}function _t(t,n,e,r,o,i,u){var a=1/(n-e),s=1/(r-o),c=1/(i-u);return t[0]=-2*a,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=-2*s,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=2*c,t[11]=0,t[12]=(n+e)*a,t[13]=(o+r)*s,t[14]=(u+i)*c,t[15]=1,t}var Et=_t;function xt(t,n,e,r,o,i,u){var a=1/(n-e),s=1/(r-o),c=1/(i-u);return t[0]=-2*a,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=-2*s,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=c,t[11]=0,t[12]=(n+e)*a,t[13]=(o+r)*s,t[14]=i*c,t[15]=1,t}function Nt(t,n,e,r){var o,i,u,s,c,f,h,d,l,g,p=n[0],m=n[1],v=n[2],y=r[0],w=r[1],b=r[2],M=e[0],_=e[1],E=e[2];return Math.abs(p-M)<a["b"]&&Math.abs(m-_)<a["b"]&&Math.abs(v-E)<a["b"]?W(t):(h=p-M,d=m-_,l=v-E,g=1/Math.hypot(h,d,l),h*=g,d*=g,l*=g,o=w*l-b*d,i=b*h-y*l,u=y*d-w*h,g=Math.hypot(o,i,u),g?(g=1/g,o*=g,i*=g,u*=g):(o=0,i=0,u=0),s=d*u-l*i,c=l*o-h*u,f=h*i-d*o,g=Math.hypot(s,c,f),g?(g=1/g,s*=g,c*=g,f*=g):(s=0,c=0,f=0),t[0]=o,t[1]=s,t[2]=h,t[3]=0,t[4]=i,t[5]=c,t[6]=d,t[7]=0,t[8]=u,t[9]=f,t[10]=l,t[11]=0,t[12]=-(o*p+i*m+u*v),t[13]=-(s*p+c*m+f*v),t[14]=-(h*p+d*m+l*v),t[15]=1,t)}function St(t,n,e,r){var o=n[0],i=n[1],u=n[2],a=r[0],s=r[1],c=r[2],f=o-e[0],h=i-e[1],d=u-e[2],l=f*f+h*h+d*d;l>0&&(l=1/Math.sqrt(l),f*=l,h*=l,d*=l);var g=s*d-c*h,p=c*f-a*d,m=a*h-s*f;return l=g*g+p*p+m*m,l>0&&(l=1/Math.sqrt(l),g*=l,p*=l,m*=l),t[0]=g,t[1]=p,t[2]=m,t[3]=0,t[4]=h*m-d*p,t[5]=d*g-f*m,t[6]=f*p-h*g,t[7]=0,t[8]=f,t[9]=h,t[10]=d,t[11]=0,t[12]=o,t[13]=i,t[14]=u,t[15]=1,t}function Tt(t){return"mat4("+t[0]+", "+t[1]+", "+t[2]+", "+t[3]+", "+t[4]+", "+t[5]+", "+t[6]+", "+t[7]+", "+t[8]+", "+t[9]+", "+t[10]+", "+t[11]+", "+t[12]+", "+t[13]+", "+t[14]+", "+t[15]+")"}function At(t){return Math.hypot(t[0],t[1],t[2],t[3],t[4],t[5],t[6],t[7],t[8],t[9],t[10],t[11],t[12],t[13],t[14],t[15])}function It(t,n,e){return t[0]=n[0]+e[0],t[1]=n[1]+e[1],t[2]=n[2]+e[2],t[3]=n[3]+e[3],t[4]=n[4]+e[4],t[5]=n[5]+e[5],t[6]=n[6]+e[6],t[7]=n[7]+e[7],t[8]=n[8]+e[8],t[9]=n[9]+e[9],t[10]=n[10]+e[10],t[11]=n[11]+e[11],t[12]=n[12]+e[12],t[13]=n[13]+e[13],t[14]=n[14]+e[14],t[15]=n[15]+e[15],t}function Ot(t,n,e){return t[0]=n[0]-e[0],t[1]=n[1]-e[1],t[2]=n[2]-e[2],t[3]=n[3]-e[3],t[4]=n[4]-e[4],t[5]=n[5]-e[5],t[6]=n[6]-e[6],t[7]=n[7]-e[7],t[8]=n[8]-e[8],t[9]=n[9]-e[9],t[10]=n[10]-e[10],t[11]=n[11]-e[11],t[12]=n[12]-e[12],t[13]=n[13]-e[13],t[14]=n[14]-e[14],t[15]=n[15]-e[15],t}function Ct(t,n,e){return t[0]=n[0]*e,t[1]=n[1]*e,t[2]=n[2]*e,t[3]=n[3]*e,t[4]=n[4]*e,t[5]=n[5]*e,t[6]=n[6]*e,t[7]=n[7]*e,t[8]=n[8]*e,t[9]=n[9]*e,t[10]=n[10]*e,t[11]=n[11]*e,t[12]=n[12]*e,t[13]=n[13]*e,t[14]=n[14]*e,t[15]=n[15]*e,t}function jt(t,n,e,r){return t[0]=n[0]+e[0]*r,t[1]=n[1]+e[1]*r,t[2]=n[2]+e[2]*r,t[3]=n[3]+e[3]*r,t[4]=n[4]+e[4]*r,t[5]=n[5]+e[5]*r,t[6]=n[6]+e[6]*r,t[7]=n[7]+e[7]*r,t[8]=n[8]+e[8]*r,t[9]=n[9]+e[9]*r,t[10]=n[10]+e[10]*r,t[11]=n[11]+e[11]*r,t[12]=n[12]+e[12]*r,t[13]=n[13]+e[13]*r,t[14]=n[14]+e[14]*r,t[15]=n[15]+e[15]*r,t}function Rt(t,n){return t[0]===n[0]&&t[1]===n[1]&&t[2]===n[2]&&t[3]===n[3]&&t[4]===n[4]&&t[5]===n[5]&&t[6]===n[6]&&t[7]===n[7]&&t[8]===n[8]&&t[9]===n[9]&&t[10]===n[10]&&t[11]===n[11]&&t[12]===n[12]&&t[13]===n[13]&&t[14]===n[14]&&t[15]===n[15]}function kt(t,n){var e=t[0],r=t[1],o=t[2],i=t[3],u=t[4],s=t[5],c=t[6],f=t[7],h=t[8],d=t[9],l=t[10],g=t[11],p=t[12],m=t[13],v=t[14],y=t[15],w=n[0],b=n[1],M=n[2],_=n[3],E=n[4],x=n[5],N=n[6],S=n[7],T=n[8],A=n[9],I=n[10],O=n[11],C=n[12],j=n[13],R=n[14],k=n[15];return Math.abs(e-w)<=a["b"]*Math.max(1,Math.abs(e),Math.abs(w))&&Math.abs(r-b)<=a["b"]*Math.max(1,Math.abs(r),Math.abs(b))&&Math.abs(o-M)<=a["b"]*Math.max(1,Math.abs(o),Math.abs(M))&&Math.abs(i-_)<=a["b"]*Math.max(1,Math.abs(i),Math.abs(_))&&Math.abs(u-E)<=a["b"]*Math.max(1,Math.abs(u),Math.abs(E))&&Math.abs(s-x)<=a["b"]*Math.max(1,Math.abs(s),Math.abs(x))&&Math.abs(c-N)<=a["b"]*Math.max(1,Math.abs(c),Math.abs(N))&&Math.abs(f-S)<=a["b"]*Math.max(1,Math.abs(f),Math.abs(S))&&Math.abs(h-T)<=a["b"]*Math.max(1,Math.abs(h),Math.abs(T))&&Math.abs(d-A)<=a["b"]*Math.max(1,Math.abs(d),Math.abs(A))&&Math.abs(l-I)<=a["b"]*Math.max(1,Math.abs(l),Math.abs(I))&&Math.abs(g-O)<=a["b"]*Math.max(1,Math.abs(g),Math.abs(O))&&Math.abs(p-C)<=a["b"]*Math.max(1,Math.abs(p),Math.abs(C))&&Math.abs(m-j)<=a["b"]*Math.max(1,Math.abs(m),Math.abs(j))&&Math.abs(v-R)<=a["b"]*Math.max(1,Math.abs(v),Math.abs(R))&&Math.abs(y-k)<=a["b"]*Math.max(1,Math.abs(y),Math.abs(k))}var Pt=K,Dt=Ot,Bt=e("9fe7");function Lt(){var t=new a["a"](4);return a["a"]!=Float32Array&&(t[0]=0,t[1]=0,t[2]=0,t[3]=0),t}function Gt(t){var n=new a["a"](4);return n[0]=t[0],n[1]=t[1],n[2]=t[2],n[3]=t[3],n}function qt(t,n,e,r){var o=new a["a"](4);return o[0]=t,o[1]=n,o[2]=e,o[3]=r,o}function Ft(t,n){return t[0]=n[0],t[1]=n[1],t[2]=n[2],t[3]=n[3],t}function Ut(t,n,e,r,o){return t[0]=n,t[1]=e,t[2]=r,t[3]=o,t}function Vt(t,n,e){return t[0]=n[0]+e[0],t[1]=n[1]+e[1],t[2]=n[2]+e[2],t[3]=n[3]+e[3],t}function zt(t,n,e){return t[0]=n[0]-e[0],t[1]=n[1]-e[1],t[2]=n[2]-e[2],t[3]=n[3]-e[3],t}function Wt(t,n,e){return t[0]=n[0]*e[0],t[1]=n[1]*e[1],t[2]=n[2]*e[2],t[3]=n[3]*e[3],t}function Yt(t,n,e){return t[0]=n[0]/e[0],t[1]=n[1]/e[1],t[2]=n[2]/e[2],t[3]=n[3]/e[3],t}function Ht(t,n){return t[0]=Math.ceil(n[0]),t[1]=Math.ceil(n[1]),t[2]=Math.ceil(n[2]),t[3]=Math.ceil(n[3]),t}function $t(t,n){return t[0]=Math.floor(n[0]),t[1]=Math.floor(n[1]),t[2]=Math.floor(n[2]),t[3]=Math.floor(n[3]),t}function Jt(t,n,e){return t[0]=Math.min(n[0],e[0]),t[1]=Math.min(n[1],e[1]),t[2]=Math.min(n[2],e[2]),t[3]=Math.min(n[3],e[3]),t}function Kt(t,n,e){return t[0]=Math.max(n[0],e[0]),t[1]=Math.max(n[1],e[1]),t[2]=Math.max(n[2],e[2]),t[3]=Math.max(n[3],e[3]),t}function Qt(t,n){return t[0]=Math.round(n[0]),t[1]=Math.round(n[1]),t[2]=Math.round(n[2]),t[3]=Math.round(n[3]),t}function Zt(t,n,e){return t[0]=n[0]*e,t[1]=n[1]*e,t[2]=n[2]*e,t[3]=n[3]*e,t}function Xt(t,n,e,r){return t[0]=n[0]+e[0]*r,t[1]=n[1]+e[1]*r,t[2]=n[2]+e[2]*r,t[3]=n[3]+e[3]*r,t}function tn(t,n){var e=n[0]-t[0],r=n[1]-t[1],o=n[2]-t[2],i=n[3]-t[3];return Math.hypot(e,r,o,i)}function nn(t,n){var e=n[0]-t[0],r=n[1]-t[1],o=n[2]-t[2],i=n[3]-t[3];return e*e+r*r+o*o+i*i}function en(t){var n=t[0],e=t[1],r=t[2],o=t[3];return Math.hypot(n,e,r,o)}function rn(t){var n=t[0],e=t[1],r=t[2],o=t[3];return n*n+e*e+r*r+o*o}function on(t,n){return t[0]=-n[0],t[1]=-n[1],t[2]=-n[2],t[3]=-n[3],t}function un(t,n){return t[0]=1/n[0],t[1]=1/n[1],t[2]=1/n[2],t[3]=1/n[3],t}function an(t,n){var e=n[0],r=n[1],o=n[2],i=n[3],u=e*e+r*r+o*o+i*i;return u>0&&(u=1/Math.sqrt(u)),t[0]=e*u,t[1]=r*u,t[2]=o*u,t[3]=i*u,t}function sn(t,n){return t[0]*n[0]+t[1]*n[1]+t[2]*n[2]+t[3]*n[3]}function cn(t,n,e,r){var o=e[0]*r[1]-e[1]*r[0],i=e[0]*r[2]-e[2]*r[0],u=e[0]*r[3]-e[3]*r[0],a=e[1]*r[2]-e[2]*r[1],s=e[1]*r[3]-e[3]*r[1],c=e[2]*r[3]-e[3]*r[2],f=n[0],h=n[1],d=n[2],l=n[3];return t[0]=h*c-d*s+l*a,t[1]=-f*c+d*u-l*i,t[2]=f*s-h*u+l*o,t[3]=-f*a+h*i-d*o,t}function fn(t,n,e,r){var o=n[0],i=n[1],u=n[2],a=n[3];return t[0]=o+r*(e[0]-o),t[1]=i+r*(e[1]-i),t[2]=u+r*(e[2]-u),t[3]=a+r*(e[3]-a),t}function hn(t,n){var e,r,o,i,u,s;n=n||1;do{e=2*a["c"]()-1,r=2*a["c"]()-1,u=e*e+r*r}while(u>=1);do{o=2*a["c"]()-1,i=2*a["c"]()-1,s=o*o+i*i}while(s>=1);var c=Math.sqrt((1-u)/s);return t[0]=n*e,t[1]=n*r,t[2]=n*o*c,t[3]=n*i*c,t}function dn(t,n,e){var r=n[0],o=n[1],i=n[2],u=n[3];return t[0]=e[0]*r+e[4]*o+e[8]*i+e[12]*u,t[1]=e[1]*r+e[5]*o+e[9]*i+e[13]*u,t[2]=e[2]*r+e[6]*o+e[10]*i+e[14]*u,t[3]=e[3]*r+e[7]*o+e[11]*i+e[15]*u,t}function ln(t,n,e){var r=n[0],o=n[1],i=n[2],u=e[0],a=e[1],s=e[2],c=e[3],f=c*r+a*i-s*o,h=c*o+s*r-u*i,d=c*i+u*o-a*r,l=-u*r-a*o-s*i;return t[0]=f*c+l*-u+h*-s-d*-a,t[1]=h*c+l*-a+d*-u-f*-s,t[2]=d*c+l*-s+f*-a-h*-u,t[3]=n[3],t}function gn(t){return t[0]=0,t[1]=0,t[2]=0,t[3]=0,t}function pn(t){return"vec4("+t[0]+", "+t[1]+", "+t[2]+", "+t[3]+")"}function mn(t,n){return t[0]===n[0]&&t[1]===n[1]&&t[2]===n[2]&&t[3]===n[3]}function vn(t,n){var e=t[0],r=t[1],o=t[2],i=t[3],u=n[0],s=n[1],c=n[2],f=n[3];return Math.abs(e-u)<=a["b"]*Math.max(1,Math.abs(e),Math.abs(u))&&Math.abs(r-s)<=a["b"]*Math.max(1,Math.abs(r),Math.abs(s))&&Math.abs(o-c)<=a["b"]*Math.max(1,Math.abs(o),Math.abs(c))&&Math.abs(i-f)<=a["b"]*Math.max(1,Math.abs(i),Math.abs(f))}var yn=zt,wn=Wt,bn=Yt,Mn=tn,_n=nn,En=en,xn=rn,Nn=function(){var t=Lt();return function(n,e,r,o,i,u){var a,s;for(e||(e=4),r||(r=0),s=o?Math.min(o*e+r,n.length):n.length,a=r;a<s;a+=e)t[0]=n[a],t[1]=n[a+1],t[2]=n[a+2],t[3]=n[a+3],i(t,t,u),n[a]=t[0],n[a+1]=t[1],n[a+2]=t[2],n[a+3]=t[3];return n}}();function Sn(){var t=new a["a"](4);return a["a"]!=Float32Array&&(t[0]=0,t[1]=0,t[2]=0),t[3]=1,t}function Tn(t){return t[0]=0,t[1]=0,t[2]=0,t[3]=1,t}function An(t,n,e){e*=.5;var r=Math.sin(e);return t[0]=r*n[0],t[1]=r*n[1],t[2]=r*n[2],t[3]=Math.cos(e),t}function In(t,n){var e=2*Math.acos(n[3]),r=Math.sin(e/2);return r>a["b"]?(t[0]=n[0]/r,t[1]=n[1]/r,t[2]=n[2]/r):(t[0]=1,t[1]=0,t[2]=0),e}function On(t,n){var e=Xn(t,n);return Math.acos(2*e*e-1)}function Cn(t,n,e){var r=n[0],o=n[1],i=n[2],u=n[3],a=e[0],s=e[1],c=e[2],f=e[3];return t[0]=r*f+u*a+o*c-i*s,t[1]=o*f+u*s+i*a-r*c,t[2]=i*f+u*c+r*s-o*a,t[3]=u*f-r*a-o*s-i*c,t}function jn(t,n,e){e*=.5;var r=n[0],o=n[1],i=n[2],u=n[3],a=Math.sin(e),s=Math.cos(e);return t[0]=r*s+u*a,t[1]=o*s+i*a,t[2]=i*s-o*a,t[3]=u*s-r*a,t}function Rn(t,n,e){e*=.5;var r=n[0],o=n[1],i=n[2],u=n[3],a=Math.sin(e),s=Math.cos(e);return t[0]=r*s-i*a,t[1]=o*s+u*a,t[2]=i*s+r*a,t[3]=u*s-o*a,t}function kn(t,n,e){e*=.5;var r=n[0],o=n[1],i=n[2],u=n[3],a=Math.sin(e),s=Math.cos(e);return t[0]=r*s+o*a,t[1]=o*s-r*a,t[2]=i*s+u*a,t[3]=u*s-i*a,t}function Pn(t,n){var e=n[0],r=n[1],o=n[2];return t[0]=e,t[1]=r,t[2]=o,t[3]=Math.sqrt(Math.abs(1-e*e-r*r-o*o)),t}function Dn(t,n){var e=n[0],r=n[1],o=n[2],i=n[3],u=Math.sqrt(e*e+r*r+o*o),a=Math.exp(i),s=u>0?a*Math.sin(u)/u:0;return t[0]=e*s,t[1]=r*s,t[2]=o*s,t[3]=a*Math.cos(u),t}function Bn(t,n){var e=n[0],r=n[1],o=n[2],i=n[3],u=Math.sqrt(e*e+r*r+o*o),a=u>0?Math.atan2(u,i)/u:0;return t[0]=e*a,t[1]=r*a,t[2]=o*a,t[3]=.5*Math.log(e*e+r*r+o*o+i*i),t}function Ln(t,n,e){return Bn(t,n),Zn(t,t,e),Dn(t,t),t}function Gn(t,n,e,r){var o,i,u,s,c,f=n[0],h=n[1],d=n[2],l=n[3],g=e[0],p=e[1],m=e[2],v=e[3];return i=f*g+h*p+d*m+l*v,i<0&&(i=-i,g=-g,p=-p,m=-m,v=-v),1-i>a["b"]?(o=Math.acos(i),u=Math.sin(o),s=Math.sin((1-r)*o)/u,c=Math.sin(r*o)/u):(s=1-r,c=r),t[0]=s*f+c*g,t[1]=s*h+c*p,t[2]=s*d+c*m,t[3]=s*l+c*v,t}function qn(t){var n=a["c"](),e=a["c"](),r=a["c"](),o=Math.sqrt(1-n),i=Math.sqrt(n);return t[0]=o*Math.sin(2*Math.PI*e),t[1]=o*Math.cos(2*Math.PI*e),t[2]=i*Math.sin(2*Math.PI*r),t[3]=i*Math.cos(2*Math.PI*r),t}function Fn(t,n){var e=n[0],r=n[1],o=n[2],i=n[3],u=e*e+r*r+o*o+i*i,a=u?1/u:0;return t[0]=-e*a,t[1]=-r*a,t[2]=-o*a,t[3]=i*a,t}function Un(t,n){return t[0]=-n[0],t[1]=-n[1],t[2]=-n[2],t[3]=n[3],t}function Vn(t,n){var e,r=n[0]+n[4]+n[8];if(r>0)e=Math.sqrt(r+1),t[3]=.5*e,e=.5/e,t[0]=(n[5]-n[7])*e,t[1]=(n[6]-n[2])*e,t[2]=(n[1]-n[3])*e;else{var o=0;n[4]>n[0]&&(o=1),n[8]>n[3*o+o]&&(o=2);var i=(o+1)%3,u=(o+2)%3;e=Math.sqrt(n[3*o+o]-n[3*i+i]-n[3*u+u]+1),t[o]=.5*e,e=.5/e,t[3]=(n[3*i+u]-n[3*u+i])*e,t[i]=(n[3*i+o]+n[3*o+i])*e,t[u]=(n[3*u+o]+n[3*o+u])*e}return t}function zn(t,n,e,r){var o=.5*Math.PI/180;n*=o,e*=o,r*=o;var i=Math.sin(n),u=Math.cos(n),a=Math.sin(e),s=Math.cos(e),c=Math.sin(r),f=Math.cos(r);return t[0]=i*s*f-u*a*c,t[1]=u*a*f+i*s*c,t[2]=u*s*c-i*a*f,t[3]=u*s*f+i*a*c,t}function Wn(t){return"quat("+t[0]+", "+t[1]+", "+t[2]+", "+t[3]+")"}var Yn=Gt,Hn=qt,$n=Ft,Jn=Ut,Kn=Vt,Qn=Cn,Zn=Zt,Xn=sn,te=fn,ne=en,ee=ne,re=rn,oe=re,ie=an,ue=mn,ae=vn,se=function(){var t=Bt["create"](),n=Bt["fromValues"](1,0,0),e=Bt["fromValues"](0,1,0);return function(r,o,i){var u=Bt["dot"](o,i);return u<-.999999?(Bt["cross"](t,n,o),Bt["len"](t)<1e-6&&Bt["cross"](t,e,o),Bt["normalize"](t,t),An(r,t,Math.PI),r):u>.999999?(r[0]=0,r[1]=0,r[2]=0,r[3]=1,r):(Bt["cross"](t,o,i),r[0]=t[0],r[1]=t[1],r[2]=t[2],r[3]=1+u,ie(r,r))}}(),ce=function(){var t=Sn(),n=Sn();return function(e,r,o,i,u,a){return Gn(t,r,u,a),Gn(n,o,i,a),Gn(e,t,n,2*a*(1-a)),e}}(),fe=function(){var t=s();return function(n,e,r,o){return t[0]=r[0],t[3]=r[1],t[6]=r[2],t[1]=o[0],t[4]=o[1],t[7]=o[2],t[2]=-e[0],t[5]=-e[1],t[8]=-e[2],ie(n,Vn(n,t))}}(),he=e("6711")},"2cac":function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.BindingWhenOnSyntax=void 0;var r=e("e34e"),o=e("cf81"),i=function(){function t(t){this._binding=t,this._bindingWhenSyntax=new o.BindingWhenSyntax(this._binding),this._bindingOnSyntax=new r.BindingOnSyntax(this._binding)}return t.prototype.when=function(t){return this._bindingWhenSyntax.when(t)},t.prototype.whenTargetNamed=function(t){return this._bindingWhenSyntax.whenTargetNamed(t)},t.prototype.whenTargetIsDefault=function(){return this._bindingWhenSyntax.whenTargetIsDefault()},t.prototype.whenTargetTagged=function(t,n){return this._bindingWhenSyntax.whenTargetTagged(t,n)},t.prototype.whenInjectedInto=function(t){return this._bindingWhenSyntax.whenInjectedInto(t)},t.prototype.whenParentNamed=function(t){return this._bindingWhenSyntax.whenParentNamed(t)},t.prototype.whenParentTagged=function(t,n){return this._bindingWhenSyntax.whenParentTagged(t,n)},t.prototype.whenAnyAncestorIs=function(t){return this._bindingWhenSyntax.whenAnyAncestorIs(t)},t.prototype.whenNoAncestorIs=function(t){return this._bindingWhenSyntax.whenNoAncestorIs(t)},t.prototype.whenAnyAncestorNamed=function(t){return this._bindingWhenSyntax.whenAnyAncestorNamed(t)},t.prototype.whenAnyAncestorTagged=function(t,n){return this._bindingWhenSyntax.whenAnyAncestorTagged(t,n)},t.prototype.whenNoAncestorNamed=function(t){return this._bindingWhenSyntax.whenNoAncestorNamed(t)},t.prototype.whenNoAncestorTagged=function(t,n){return this._bindingWhenSyntax.whenNoAncestorTagged(t,n)},t.prototype.whenAnyAncestorMatches=function(t){return this._bindingWhenSyntax.whenAnyAncestorMatches(t)},t.prototype.whenNoAncestorMatches=function(t){return this._bindingWhenSyntax.whenNoAncestorMatches(t)},t.prototype.onActivation=function(t){return this._bindingOnSyntax.onActivation(t)},t}();n.BindingWhenOnSyntax=i},"2e0b":function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.Binding=void 0;var r=e("155f"),o=e("77d3"),i=function(){function t(t,n){this.id=o.id(),this.activated=!1,this.serviceIdentifier=t,this.scope=n,this.type=r.BindingTypeEnum.Invalid,this.constraint=function(t){return!0},this.implementationType=null,this.cache=null,this.factory=null,this.provider=null,this.onActivation=null,this.dynamicValue=null}return t.prototype.clone=function(){var n=new t(this.serviceIdentifier,this.scope);return n.activated=n.scope===r.BindingScopeEnum.Singleton&&this.activated,n.implementationType=this.implementationType,n.dynamicValue=this.dynamicValue,n.scope=this.scope,n.type=this.type,n.factory=this.factory,n.provider=this.provider,n.constraint=this.constraint,n.onActivation=this.onActivation,n.cache=this.cache,n},t}();n.Binding=i},"2f6e":function(t,n){function e(t,n){n=n||1;var e=2*Math.random()*Math.PI;return t[0]=Math.cos(e)*n,t[1]=Math.sin(e)*n,t}t.exports=e},"305e":function(t,n,e){t.exports=e("bbaa")},"30e3":function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.STACK_OVERFLOW=n.CIRCULAR_DEPENDENCY_IN_FACTORY=n.POST_CONSTRUCT_ERROR=n.MULTIPLE_POST_CONSTRUCT_METHODS=n.CONTAINER_OPTIONS_INVALID_SKIP_BASE_CHECK=n.CONTAINER_OPTIONS_INVALID_AUTO_BIND_INJECTABLE=n.CONTAINER_OPTIONS_INVALID_DEFAULT_SCOPE=n.CONTAINER_OPTIONS_MUST_BE_AN_OBJECT=n.ARGUMENTS_LENGTH_MISMATCH=n.INVALID_DECORATOR_OPERATION=n.INVALID_TO_SELF_VALUE=n.INVALID_FUNCTION_BINDING=n.INVALID_MIDDLEWARE_RETURN=n.NO_MORE_SNAPSHOTS_AVAILABLE=n.INVALID_BINDING_TYPE=n.NOT_IMPLEMENTED=n.CIRCULAR_DEPENDENCY=n.UNDEFINED_INJECT_ANNOTATION=n.MISSING_INJECT_ANNOTATION=n.MISSING_INJECTABLE_ANNOTATION=n.NOT_REGISTERED=n.CANNOT_UNBIND=n.AMBIGUOUS_MATCH=n.KEY_NOT_FOUND=n.NULL_ARGUMENT=n.DUPLICATED_METADATA=n.DUPLICATED_INJECTABLE_DECORATOR=void 0,n.DUPLICATED_INJECTABLE_DECORATOR="Cannot apply @injectable decorator multiple times.",n.DUPLICATED_METADATA="Metadata key was used more than once in a parameter:",n.NULL_ARGUMENT="NULL argument",n.KEY_NOT_FOUND="Key Not Found",n.AMBIGUOUS_MATCH="Ambiguous match found for serviceIdentifier:",n.CANNOT_UNBIND="Could not unbind serviceIdentifier:",n.NOT_REGISTERED="No matching bindings found for serviceIdentifier:",n.MISSING_INJECTABLE_ANNOTATION="Missing required @injectable annotation in:",n.MISSING_INJECT_ANNOTATION="Missing required @inject or @multiInject annotation in:";var r=function(t){return"@inject called with undefined this could mean that the class "+t+" has a circular dependency problem. You can use a LazyServiceIdentifer to  overcome this limitation."};n.UNDEFINED_INJECT_ANNOTATION=r,n.CIRCULAR_DEPENDENCY="Circular dependency found:",n.NOT_IMPLEMENTED="Sorry, this feature is not fully implemented yet.",n.INVALID_BINDING_TYPE="Invalid binding type:",n.NO_MORE_SNAPSHOTS_AVAILABLE="No snapshot available to restore.",n.INVALID_MIDDLEWARE_RETURN="Invalid return type in middleware. Middleware must return!",n.INVALID_FUNCTION_BINDING="Value provided to function binding must be a function!",n.INVALID_TO_SELF_VALUE="The toSelf function can only be applied when a constructor is used as service identifier",n.INVALID_DECORATOR_OPERATION="The @inject @multiInject @tagged and @named decorators must be applied to the parameters of a class constructor or a class property.";var o=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return"The number of constructor arguments in the derived class "+t[0]+" must be >= than the number of constructor arguments of its base class."};n.ARGUMENTS_LENGTH_MISMATCH=o,n.CONTAINER_OPTIONS_MUST_BE_AN_OBJECT="Invalid Container constructor argument. Container options must be an object.",n.CONTAINER_OPTIONS_INVALID_DEFAULT_SCOPE="Invalid Container option. Default scope must be a string ('singleton' or 'transient').",n.CONTAINER_OPTIONS_INVALID_AUTO_BIND_INJECTABLE="Invalid Container option. Auto bind injectable must be a boolean",n.CONTAINER_OPTIONS_INVALID_SKIP_BASE_CHECK="Invalid Container option. Skip base check must be a boolean",n.MULTIPLE_POST_CONSTRUCT_METHODS="Cannot apply @postConstruct decorator multiple times in the same class";var i=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return"@postConstruct error in class "+t[0]+": "+t[1]};n.POST_CONSTRUCT_ERROR=i;var u=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return"It looks like there is a circular dependency in one of the '"+t[0]+"' bindings. Please investigate bindings withservice identifier '"+t[1]+"'."};n.CIRCULAR_DEPENDENCY_IN_FACTORY=u,n.STACK_OVERFLOW="Maximum call stack size exceeded"},"332e":function(t,n){function e(t,n,e){return t[0]=n[0]*e,t[1]=n[1]*e,t}t.exports=e},"339b":function(t,n){function e(t,n){var e=n[0]-t[0],r=n[1]-t[1];return Math.sqrt(e*e+r*r)}t.exports=e},3443:function(t,n){t.exports=1e-6},3761:function(t,n){function e(t,n,e){var r=n[0]*e[1]-n[1]*e[0];return t[0]=t[1]=0,t[2]=r,t}t.exports=e},"3cc3":function(t,n){function e(t,n){return t[0]=n[0],t[1]=n[1],t}t.exports=e},"40ec":function(t,n){function e(t,n){return t[0]=1/n[0],t[1]=1/n[1],t}t.exports=e},4516:function(t,n){function e(t,n){return t[0]===n[0]&&t[1]===n[1]}t.exports=e},"451f":function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.typeConstraint=n.namedConstraint=n.taggedConstraint=n.traverseAncerstors=void 0;var r=e("c5f4"),o=e("1979"),i=function(t,n){var e=t.parentRequest;return null!==e&&(!!n(e)||i(e,n))};n.traverseAncerstors=i;var u=function(t){return function(n){var e=function(e){return null!==e&&null!==e.target&&e.target.matchesTag(t)(n)};return e.metaData=new o.Metadata(t,n),e}};n.taggedConstraint=u;var a=u(r.NAMED_TAG);n.namedConstraint=a;var s=function(t){return function(n){var e=null;if(null!==n){if(e=n.bindings[0],"string"===typeof t){var r=e.serviceIdentifier;return r===t}var o=n.bindings[0].implementationType;return t===o}return!1}};n.typeConstraint=s},4869:function(t,n){function e(t){var n=t[0],e=t[1];return n*n+e*e}t.exports=e},"4a46":function(t,n){function e(t,n){return t[0]=Math.round(n[0]),t[1]=Math.round(n[1]),t}t.exports=e},"4a4f":function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.postConstruct=void 0;var r=e("30e3"),o=e("c5f4"),i=e("1979");function u(){return function(t,n,e){var u=new i.Metadata(o.POST_CONSTRUCT,n);if(Reflect.hasOwnMetadata(o.POST_CONSTRUCT,t.constructor))throw new Error(r.MULTIPLE_POST_CONSTRUCT_METHODS);Reflect.defineMetadata(o.POST_CONSTRUCT,u,t.constructor)}}n.postConstruct=u},"4af6":function(t,n,e){t.exports=e("06ed")},"4bcc":function(t,n){function e(t){var n=new Float32Array(2);return n[0]=t[0],n[1]=t[1],n}t.exports=e},"4f06":function(t,n){function e(t,n,e){var r=n[0],o=n[1];return t[0]=e[0]*r+e[3]*o+e[6],t[1]=e[1]*r+e[4]*o+e[7],t}t.exports=e},"51cb":function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0});var r=Symbol.for("INJECTION");function o(t,n,e,o){function i(){return o&&!Reflect.hasMetadata(r,this,n)&&Reflect.defineMetadata(r,e(),this,n),Reflect.hasMetadata(r,this,n)?Reflect.getMetadata(r,this,n):e()}function u(t){Reflect.defineMetadata(r,t,this,n)}Object.defineProperty(t,n,{configurable:!0,enumerable:!0,get:i,set:u})}function i(t,n){return function(e){return function(r,i){var u=function(){return t.get(e)};o(r,i,u,n)}}}function u(t,n){return function(e,r){return function(i,u){var a=function(){return t.getNamed(e,r)};o(i,u,a,n)}}}function a(t,n){return function(e,r,i){return function(u,a){var s=function(){return t.getTagged(e,r,i)};o(u,a,s,n)}}}function s(t,n){return function(e){return function(r,i){var u=function(){return t.getAll(e)};o(r,i,u,n)}}}n.makePropertyInjectDecorator=i,n.makePropertyInjectNamedDecorator=u,n.makePropertyInjectTaggedDecorator=a,n.makePropertyMultiInjectDecorator=s},"51de":function(t,n,e){"use strict";function r(t){return ArrayBuffer.isView(t)&&!(t instanceof DataView)}e.d(n,"b",(function(){return r})),n["a"]=function(t,n){n||(n=[]);var e,r=t?Math.min(n.length,t.length):0,o=n.slice();return function(i){for(e=0;e<r;++e)o[e]=t[e]*(1-i)+n[e]*i;return o}}},"624f":function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.inject=n.LazyServiceIdentifer=void 0;var r=e("30e3"),o=e("c5f4"),i=e("1979"),u=e("66d7"),a=function(){function t(t){this._cb=t}return t.prototype.unwrap=function(){return this._cb()},t}();function s(t){return function(n,e,a){if(void 0===t)throw new Error(r.UNDEFINED_INJECT_ANNOTATION(n.name));var s=new i.Metadata(o.INJECT_TAG,t);"number"===typeof a?u.tagParameter(n,e,a,s):u.tagProperty(n,e,s)}}n.LazyServiceIdentifer=a,n.inject=s},"62bf":function(t,n,e){t.exports=e("4869")},"66d7":function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.tagProperty=n.tagParameter=n.decorate=void 0;var r=e("30e3"),o=e("c5f4");function i(t,n,e,r){var i=o.TAGGED;a(i,t,n,r,e)}function u(t,n,e){var r=o.TAGGED_PROP;a(r,t.constructor,n,e)}function a(t,n,e,o,i){var u={},a="number"===typeof i,s=void 0!==i&&a?i.toString():e;if(a&&void 0!==e)throw new Error(r.INVALID_DECORATOR_OPERATION);Reflect.hasOwnMetadata(t,n)&&(u=Reflect.getMetadata(t,n));var c=u[s];if(Array.isArray(c))for(var f=0,h=c;f<h.length;f++){var d=h[f];if(d.key===o.key)throw new Error(r.DUPLICATED_METADATA+" "+d.key.toString())}else c=[];c.push(o),u[s]=c,Reflect.defineMetadata(t,u,n)}function s(t,n){Reflect.decorate(t,n)}function c(t,n){return function(e,r){n(e,r,t)}}function f(t,n,e){"number"===typeof e?s([c(e,t)],n):"string"===typeof e?Reflect.decorate([t],n,e):s([t],n)}n.tagParameter=i,n.tagProperty=u,n.decorate=f},6711:function(t,n,e){"use strict";e.r(n),e.d(n,"create",(function(){return o})),e.d(n,"clone",(function(){return i})),e.d(n,"fromValues",(function(){return u})),e.d(n,"copy",(function(){return a})),e.d(n,"set",(function(){return s})),e.d(n,"add",(function(){return c})),e.d(n,"subtract",(function(){return f})),e.d(n,"multiply",(function(){return h})),e.d(n,"divide",(function(){return d})),e.d(n,"ceil",(function(){return l})),e.d(n,"floor",(function(){return g})),e.d(n,"min",(function(){return p})),e.d(n,"max",(function(){return m})),e.d(n,"round",(function(){return v})),e.d(n,"scale",(function(){return y})),e.d(n,"scaleAndAdd",(function(){return w})),e.d(n,"distance",(function(){return b})),e.d(n,"squaredDistance",(function(){return M})),e.d(n,"length",(function(){return _})),e.d(n,"squaredLength",(function(){return E})),e.d(n,"negate",(function(){return x})),e.d(n,"inverse",(function(){return N})),e.d(n,"normalize",(function(){return S})),e.d(n,"dot",(function(){return T})),e.d(n,"cross",(function(){return A})),e.d(n,"lerp",(function(){return I})),e.d(n,"random",(function(){return O})),e.d(n,"transformMat2",(function(){return C})),e.d(n,"transformMat2d",(function(){return j})),e.d(n,"transformMat3",(function(){return R})),e.d(n,"transformMat4",(function(){return k})),e.d(n,"rotate",(function(){return P})),e.d(n,"angle",(function(){return D})),e.d(n,"zero",(function(){return B})),e.d(n,"str",(function(){return L})),e.d(n,"exactEquals",(function(){return G})),e.d(n,"equals",(function(){return q})),e.d(n,"len",(function(){return F})),e.d(n,"sub",(function(){return U})),e.d(n,"mul",(function(){return V})),e.d(n,"div",(function(){return z})),e.d(n,"dist",(function(){return W})),e.d(n,"sqrDist",(function(){return Y})),e.d(n,"sqrLen",(function(){return H})),e.d(n,"forEach",(function(){return $}));var r=e("c94d");function o(){var t=new r["a"](2);return r["a"]!=Float32Array&&(t[0]=0,t[1]=0),t}function i(t){var n=new r["a"](2);return n[0]=t[0],n[1]=t[1],n}function u(t,n){var e=new r["a"](2);return e[0]=t,e[1]=n,e}function a(t,n){return t[0]=n[0],t[1]=n[1],t}function s(t,n,e){return t[0]=n,t[1]=e,t}function c(t,n,e){return t[0]=n[0]+e[0],t[1]=n[1]+e[1],t}function f(t,n,e){return t[0]=n[0]-e[0],t[1]=n[1]-e[1],t}function h(t,n,e){return t[0]=n[0]*e[0],t[1]=n[1]*e[1],t}function d(t,n,e){return t[0]=n[0]/e[0],t[1]=n[1]/e[1],t}function l(t,n){return t[0]=Math.ceil(n[0]),t[1]=Math.ceil(n[1]),t}function g(t,n){return t[0]=Math.floor(n[0]),t[1]=Math.floor(n[1]),t}function p(t,n,e){return t[0]=Math.min(n[0],e[0]),t[1]=Math.min(n[1],e[1]),t}function m(t,n,e){return t[0]=Math.max(n[0],e[0]),t[1]=Math.max(n[1],e[1]),t}function v(t,n){return t[0]=Math.round(n[0]),t[1]=Math.round(n[1]),t}function y(t,n,e){return t[0]=n[0]*e,t[1]=n[1]*e,t}function w(t,n,e,r){return t[0]=n[0]+e[0]*r,t[1]=n[1]+e[1]*r,t}function b(t,n){var e=n[0]-t[0],r=n[1]-t[1];return Math.hypot(e,r)}function M(t,n){var e=n[0]-t[0],r=n[1]-t[1];return e*e+r*r}function _(t){var n=t[0],e=t[1];return Math.hypot(n,e)}function E(t){var n=t[0],e=t[1];return n*n+e*e}function x(t,n){return t[0]=-n[0],t[1]=-n[1],t}function N(t,n){return t[0]=1/n[0],t[1]=1/n[1],t}function S(t,n){var e=n[0],r=n[1],o=e*e+r*r;return o>0&&(o=1/Math.sqrt(o)),t[0]=n[0]*o,t[1]=n[1]*o,t}function T(t,n){return t[0]*n[0]+t[1]*n[1]}function A(t,n,e){var r=n[0]*e[1]-n[1]*e[0];return t[0]=t[1]=0,t[2]=r,t}function I(t,n,e,r){var o=n[0],i=n[1];return t[0]=o+r*(e[0]-o),t[1]=i+r*(e[1]-i),t}function O(t,n){n=n||1;var e=2*r["c"]()*Math.PI;return t[0]=Math.cos(e)*n,t[1]=Math.sin(e)*n,t}function C(t,n,e){var r=n[0],o=n[1];return t[0]=e[0]*r+e[2]*o,t[1]=e[1]*r+e[3]*o,t}function j(t,n,e){var r=n[0],o=n[1];return t[0]=e[0]*r+e[2]*o+e[4],t[1]=e[1]*r+e[3]*o+e[5],t}function R(t,n,e){var r=n[0],o=n[1];return t[0]=e[0]*r+e[3]*o+e[6],t[1]=e[1]*r+e[4]*o+e[7],t}function k(t,n,e){var r=n[0],o=n[1];return t[0]=e[0]*r+e[4]*o+e[12],t[1]=e[1]*r+e[5]*o+e[13],t}function P(t,n,e,r){var o=n[0]-e[0],i=n[1]-e[1],u=Math.sin(r),a=Math.cos(r);return t[0]=o*a-i*u+e[0],t[1]=o*u+i*a+e[1],t}function D(t,n){var e=t[0],r=t[1],o=n[0],i=n[1],u=Math.sqrt(e*e+r*r)*Math.sqrt(o*o+i*i),a=u&&(e*o+r*i)/u;return Math.acos(Math.min(Math.max(a,-1),1))}function B(t){return t[0]=0,t[1]=0,t}function L(t){return"vec2("+t[0]+", "+t[1]+")"}function G(t,n){return t[0]===n[0]&&t[1]===n[1]}function q(t,n){var e=t[0],o=t[1],i=n[0],u=n[1];return Math.abs(e-i)<=r["b"]*Math.max(1,Math.abs(e),Math.abs(i))&&Math.abs(o-u)<=r["b"]*Math.max(1,Math.abs(o),Math.abs(u))}var F=_,U=f,V=h,z=d,W=b,Y=M,H=E,$=function(){var t=o();return function(n,e,r,o,i,u){var a,s;for(e||(e=2),r||(r=0),s=o?Math.min(o*e+r,n.length):n.length,a=r;a<s;a+=e)t[0]=n[a],t[1]=n[a+1],i(t,t,u),n[a]=t[0],n[a+1]=t[1];return n}}()},6730:function(t,n,e){"use strict";e.d(n,"b",(function(){return i}));var r=e("ea1d"),o=e("51de");function i(t,n){var e,o=n?n.length:0,i=t?Math.min(o,t.length):0,u=new Array(i),a=new Array(o);for(e=0;e<i;++e)u[e]=Object(r["a"])(t[e],n[e]);for(;e<o;++e)a[e]=n[e];return function(t){for(e=0;e<i;++e)a[e]=u[e](t);return a}}n["a"]=function(t,n){return(Object(o["b"])(n)?o["a"]:i)(t,n)}},"6730a":function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.named=void 0;var r=e("c5f4"),o=e("1979"),i=e("66d7");function u(t){return function(n,e,u){var a=new o.Metadata(r.NAMED_TAG,t);"number"===typeof u?i.tagParameter(n,e,u,a):i.tagProperty(n,e,a)}}n.named=u},"68f1":function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.resolve=void 0;var r=e("30e3"),o=e("155f"),i=e("a8af"),u=e("ba33"),a=e("7122"),s=function(t,n,e){try{return e()}catch(o){throw i.isStackOverflowExeption(o)?new Error(r.CIRCULAR_DEPENDENCY_IN_FACTORY(t,n.toString())):o}},c=function(t){return function(n){n.parentContext.setCurrentRequest(n);var e=n.bindings,i=n.childRequests,f=n.target&&n.target.isArray(),h=!n.parentRequest||!n.parentRequest.target||!n.target||!n.parentRequest.target.matchesArray(n.target.serviceIdentifier);if(f&&h)return i.map((function(n){var e=c(t);return e(n)}));var d=null;if(!n.target.isOptional()||0!==e.length){var l=e[0],g=l.scope===o.BindingScopeEnum.Singleton,p=l.scope===o.BindingScopeEnum.Request;if(g&&l.activated)return l.cache;if(p&&null!==t&&t.has(l.id))return t.get(l.id);if(l.type===o.BindingTypeEnum.ConstantValue)d=l.cache,l.activated=!0;else if(l.type===o.BindingTypeEnum.Function)d=l.cache,l.activated=!0;else if(l.type===o.BindingTypeEnum.Constructor)d=l.implementationType;else if(l.type===o.BindingTypeEnum.DynamicValue&&null!==l.dynamicValue)d=s("toDynamicValue",l.serviceIdentifier,(function(){return l.dynamicValue(n.parentContext)}));else if(l.type===o.BindingTypeEnum.Factory&&null!==l.factory)d=s("toFactory",l.serviceIdentifier,(function(){return l.factory(n.parentContext)}));else if(l.type===o.BindingTypeEnum.Provider&&null!==l.provider)d=s("toProvider",l.serviceIdentifier,(function(){return l.provider(n.parentContext)}));else{if(l.type!==o.BindingTypeEnum.Instance||null===l.implementationType){var m=u.getServiceIdentifierAsString(n.serviceIdentifier);throw new Error(r.INVALID_BINDING_TYPE+" "+m)}d=a.resolveInstance(l.implementationType,i,c(t))}return"function"===typeof l.onActivation&&(d=l.onActivation(n.parentContext,d)),g&&(l.cache=d,l.activated=!0),p&&null!==t&&!t.has(l.id)&&t.set(l.id,d),d}}};function f(t){var n=c(t.plan.rootRequest.requestScope);return n(t.plan.rootRequest)}n.resolve=f},"6bc6":function(t,n){function e(t,n){return t[0]*n[0]+t[1]*n[1]}t.exports=e},"6c57":function(t,n,e){var r=e("23e7"),o=e("da84");r({global:!0},{globalThis:o})},"6e53":function(t,n){function e(t,n,e){var r=n[0],o=n[1];return t[0]=e[0]*r+e[2]*o+e[4],t[1]=e[1]*r+e[3]*o+e[5],t}t.exports=e},"706c":function(t,n){function e(t,n,e){return t[0]=n[0]-e[0],t[1]=n[1]-e[1],t}t.exports=e},7122:function(t,n,e){"use strict";var r=this&&this.__spreadArray||function(t,n){for(var e=0,r=n.length,o=t.length;e<r;e++,o++)t[o]=n[e];return t};Object.defineProperty(n,"__esModule",{value:!0}),n.resolveInstance=void 0;var o=e("30e3"),i=e("155f"),u=e("c5f4");function a(t,n,e){var r=n.filter((function(t){return null!==t.target&&t.target.type===i.TargetTypeEnum.ClassProperty})),o=r.map(e);return r.forEach((function(n,e){var r="";r=n.target.name.value();var i=o[e];t[r]=i})),t}function s(t,n){return new(t.bind.apply(t,r([void 0],n)))}function c(t,n){if(Reflect.hasMetadata(u.POST_CONSTRUCT,t)){var e=Reflect.getMetadata(u.POST_CONSTRUCT,t);try{n[e.value]()}catch(r){throw new Error(o.POST_CONSTRUCT_ERROR(t.name,r.message))}}}function f(t,n,e){var r=null;if(n.length>0){var o=n.filter((function(t){return null!==t.target&&t.target.type===i.TargetTypeEnum.ConstructorArgument})),u=o.map(e);r=s(t,u),r=a(r,n,e)}else r=new t;return c(t,r),r}n.resolveInstance=f},"719e":function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.injectable=void 0;var r=e("30e3"),o=e("c5f4");function i(){return function(t){if(Reflect.hasOwnMetadata(o.PARAM_TYPES,t))throw new Error(r.DUPLICATED_INJECTABLE_DECORATOR);var n=Reflect.getMetadata(o.DESIGN_PARAM_TYPES,t)||[];return Reflect.defineMetadata(o.PARAM_TYPES,n,t),t}}n.injectable=i},"74e5":function(t,n){function e(t,n,e){return t[0]=Math.min(n[0],e[0]),t[1]=Math.min(n[1],e[1]),t}t.exports=e},"74f4":function(t,n,e){"use strict";e.d(n,"a",(function(){return v}));var r,o,i=0,u=0,a=0,s=1e3,c=0,f=0,h=0,d="object"===typeof performance&&performance.now?performance:Date,l="object"===typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function g(){return f||(l(p),f=d.now()+h)}function p(){f=0}function m(){this._call=this._time=this._next=null}function v(t,n,e){var r=new m;return r.restart(t,n,e),r}function y(){g(),++i;var t,n=r;while(n)(t=f-n._time)>=0&&n._call.call(null,t),n=n._next;--i}function w(){f=(c=d.now())+h,i=u=0;try{y()}finally{i=0,M(),f=0}}function b(){var t=d.now(),n=t-c;n>s&&(h-=n,c=t)}function M(){var t,n,e=r,i=1/0;while(e)e._call?(i>e._time&&(i=e._time),t=e,e=e._next):(n=e._next,e._next=null,e=t?t._next=n:r=n);o=t,_(i)}function _(t){if(!i){u&&(u=clearTimeout(u));var n=t-f;n>24?(t<1/0&&(u=setTimeout(w,t-d.now()-h)),a&&(a=clearInterval(a))):(a||(c=d.now(),a=setInterval(b,s)),i=1,l(w))}}m.prototype=v.prototype={constructor:m,restart:function(t,n,e){if("function"!==typeof t)throw new TypeError("callback is not a function");e=(null==e?g():+e)+(null==n?0:+n),this._next||o===this||(o?o._next=this:r=this,o=this),this._call=t,this._time=e,_()},stop:function(){this._call&&(this._call=null,this._time=1/0,_())}}},"757d":function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.Target=void 0;var r=e("c5f4"),o=e("77d3"),i=e("1979"),u=e("cf13"),a=function(){function t(t,n,e,a){this.id=o.id(),this.type=t,this.serviceIdentifier=e,this.name=new u.QueryableString(n||""),this.metadata=new Array;var s=null;"string"===typeof a?s=new i.Metadata(r.NAMED_TAG,a):a instanceof i.Metadata&&(s=a),null!==s&&this.metadata.push(s)}return t.prototype.hasTag=function(t){for(var n=0,e=this.metadata;n<e.length;n++){var r=e[n];if(r.key===t)return!0}return!1},t.prototype.isArray=function(){return this.hasTag(r.MULTI_INJECT_TAG)},t.prototype.matchesArray=function(t){return this.matchesTag(r.MULTI_INJECT_TAG)(t)},t.prototype.isNamed=function(){return this.hasTag(r.NAMED_TAG)},t.prototype.isTagged=function(){return this.metadata.some((function(t){return r.NON_CUSTOM_TAG_KEYS.every((function(n){return t.key!==n}))}))},t.prototype.isOptional=function(){return this.matchesTag(r.OPTIONAL_TAG)(!0)},t.prototype.getNamedTag=function(){return this.isNamed()?this.metadata.filter((function(t){return t.key===r.NAMED_TAG}))[0]:null},t.prototype.getCustomTags=function(){return this.isTagged()?this.metadata.filter((function(t){return r.NON_CUSTOM_TAG_KEYS.every((function(n){return t.key!==n}))})):null},t.prototype.matchesNamedTag=function(t){return this.matchesTag(r.NAMED_TAG)(t)},t.prototype.matchesTag=function(t){var n=this;return function(e){for(var r=0,o=n.metadata;r<o.length;r++){var i=o[r];if(i.key===t&&i.value===e)return!0}return!1}},t}();n.Target=a},7685:function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.BindingCount=void 0;var r={MultipleBindingsAvailable:2,NoBindingsAvailable:0,OnlyOneBindingAvailable:1};n.BindingCount=r},"771c":function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.AsyncContainerModule=n.ContainerModule=void 0;var r=e("77d3"),o=function(){function t(t){this.id=r.id(),this.registry=t}return t}();n.ContainerModule=o;var i=function(){function t(t){this.id=r.id(),this.registry=t}return t}();n.AsyncContainerModule=i},"77d3":function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.id=void 0;var r=0;function o(){return r++}n.id=o},"7b46":function(t,n,e){"use strict";(function(t){e.d(n,"a",(function(){return g}));var r=function(t,n,e){if(e||2===arguments.length)for(var r,o=0,i=n.length;o<i;o++)!r&&o in n||(r||(r=Array.prototype.slice.call(n,0,o)),r[o]=n[o]);return t.concat(r||Array.prototype.slice.call(n))},o=function(){function t(t,n,e){this.name=t,this.version=n,this.os=e,this.type="browser"}return t}(),i=function(){function n(n){this.version=n,this.type="node",this.name="node",this.os=t.platform}return n}(),u=function(){function t(t,n,e,r){this.name=t,this.version=n,this.os=e,this.bot=r,this.type="bot-device"}return t}(),a=function(){function t(){this.type="bot",this.bot=!0,this.name="bot",this.version=null,this.os=null}return t}(),s=function(){function t(){this.type="react-native",this.name="react-native",this.version=null,this.os=null}return t}(),c=/alexa|bot|crawl(er|ing)|facebookexternalhit|feedburner|google web preview|nagios|postrank|pingdom|slurp|spider|yahoo!|yandex/,f=/(nuhk|curl|Googlebot|Yammybot|Openbot|Slurp|MSNBot|Ask\ Jeeves\/Teoma|ia_archiver)/,h=3,d=[["aol",/AOLShield\/([0-9\._]+)/],["edge",/Edge\/([0-9\._]+)/],["edge-ios",/EdgiOS\/([0-9\._]+)/],["yandexbrowser",/YaBrowser\/([0-9\._]+)/],["kakaotalk",/KAKAOTALK\s([0-9\.]+)/],["samsung",/SamsungBrowser\/([0-9\.]+)/],["silk",/\bSilk\/([0-9._-]+)\b/],["miui",/MiuiBrowser\/([0-9\.]+)$/],["beaker",/BeakerBrowser\/([0-9\.]+)/],["edge-chromium",/EdgA?\/([0-9\.]+)/],["chromium-webview",/(?!Chrom.*OPR)wv\).*Chrom(?:e|ium)\/([0-9\.]+)(:?\s|$)/],["chrome",/(?!Chrom.*OPR)Chrom(?:e|ium)\/([0-9\.]+)(:?\s|$)/],["phantomjs",/PhantomJS\/([0-9\.]+)(:?\s|$)/],["crios",/CriOS\/([0-9\.]+)(:?\s|$)/],["firefox",/Firefox\/([0-9\.]+)(?:\s|$)/],["fxios",/FxiOS\/([0-9\.]+)/],["opera-mini",/Opera Mini.*Version\/([0-9\.]+)/],["opera",/Opera\/([0-9\.]+)(?:\s|$)/],["opera",/OPR\/([0-9\.]+)(:?\s|$)/],["pie",/^Microsoft Pocket Internet Explorer\/(\d+\.\d+)$/],["pie",/^Mozilla\/\d\.\d+\s\(compatible;\s(?:MSP?IE|MSInternet Explorer) (\d+\.\d+);.*Windows CE.*\)$/],["netfront",/^Mozilla\/\d\.\d+.*NetFront\/(\d.\d)/],["ie",/Trident\/7\.0.*rv\:([0-9\.]+).*\).*Gecko$/],["ie",/MSIE\s([0-9\.]+);.*Trident\/[4-7].0/],["ie",/MSIE\s(7\.0)/],["bb10",/BB10;\sTouch.*Version\/([0-9\.]+)/],["android",/Android\s([0-9\.]+)/],["ios",/Version\/([0-9\._]+).*Mobile.*Safari.*/],["safari",/Version\/([0-9\._]+).*Safari/],["facebook",/FB[AS]V\/([0-9\.]+)/],["instagram",/Instagram\s([0-9\.]+)/],["ios-webview",/AppleWebKit\/([0-9\.]+).*Mobile/],["ios-webview",/AppleWebKit\/([0-9\.]+).*Gecko\)$/],["curl",/^curl\/([0-9\.]+)$/],["searchbot",c]],l=[["iOS",/iP(hone|od|ad)/],["Android OS",/Android/],["BlackBerry OS",/BlackBerry|BB10/],["Windows Mobile",/IEMobile/],["Amazon OS",/Kindle/],["Windows 3.11",/Win16/],["Windows 95",/(Windows 95)|(Win95)|(Windows_95)/],["Windows 98",/(Windows 98)|(Win98)/],["Windows 2000",/(Windows NT 5.0)|(Windows 2000)/],["Windows XP",/(Windows NT 5.1)|(Windows XP)/],["Windows Server 2003",/(Windows NT 5.2)/],["Windows Vista",/(Windows NT 6.0)/],["Windows 7",/(Windows NT 6.1)/],["Windows 8",/(Windows NT 6.2)/],["Windows 8.1",/(Windows NT 6.3)/],["Windows 10",/(Windows NT 10.0)/],["Windows ME",/Windows ME/],["Windows CE",/Windows CE|WinCE|Microsoft Pocket Internet Explorer/],["Open BSD",/OpenBSD/],["Sun OS",/SunOS/],["Chrome OS",/CrOS/],["Linux",/(Linux)|(X11)/],["Mac OS",/(Mac_PowerPC)|(Macintosh)/],["QNX",/QNX/],["BeOS",/BeOS/],["OS/2",/OS\/2/]];function g(t){return t?m(t):"undefined"===typeof document&&"undefined"!==typeof navigator&&"ReactNative"===navigator.product?new s:"undefined"!==typeof navigator?m(navigator.userAgent):y()}function p(t){return""!==t&&d.reduce((function(n,e){var r=e[0],o=e[1];if(n)return n;var i=o.exec(t);return!!i&&[r,i]}),!1)}function m(t){var n=p(t);if(!n)return null;var e=n[0],i=n[1];if("searchbot"===e)return new a;var s=i[1]&&i[1].split(".").join("_").split("_").slice(0,3);s?s.length<h&&(s=r(r([],s,!0),w(h-s.length),!0)):s=[];var c=s.join("."),d=v(t),l=f.exec(t);return l&&l[1]?new u(e,c,d,l[1]):new o(e,c,d)}function v(t){for(var n=0,e=l.length;n<e;n++){var r=l[n],o=r[0],i=r[1],u=i.exec(t);if(u)return o}return null}function y(){var n="undefined"!==typeof t&&t.version;return n?new i(t.version.slice(1)):null}function w(t){for(var n=[],e=0;e<t;e++)n.push("0");return n}}).call(this,e("4362"))},"7dba":function(t,n,e){"use strict";var r=this&&this.__spreadArray||function(t,n){for(var e=0,r=n.length,o=t.length;e<r;e++,o++)t[o]=n[e];return t};Object.defineProperty(n,"__esModule",{value:!0}),n.getFunctionName=n.getBaseClassDependencyCount=n.getDependencies=void 0;var o=e("624f"),i=e("30e3"),u=e("155f"),a=e("c5f4"),s=e("ba33");Object.defineProperty(n,"getFunctionName",{enumerable:!0,get:function(){return s.getFunctionName}});var c=e("757d");function f(t,n){var e=s.getFunctionName(n),r=h(t,e,n,!1);return r}function h(t,n,e,o){var u=t.getConstructorMetadata(e),a=u.compilerGeneratedMetadata;if(void 0===a){var s=i.MISSING_INJECTABLE_ANNOTATION+" "+n+".";throw new Error(s)}var c=u.userGeneratedMetadata,f=Object.keys(c),h=0===e.length&&f.length>0,d=f.length>e.length,p=h||d?f.length:e.length,m=l(o,n,a,c,p),v=g(t,e),y=r(r([],m),v);return y}function d(t,n,e,r,a){var s=a[t.toString()]||[],f=m(s),h=!0!==f.unmanaged,d=r[t],l=f.inject||f.multiInject;if(d=l||d,d instanceof o.LazyServiceIdentifer&&(d=d.unwrap()),h){var g=d===Object,p=d===Function,v=void 0===d,y=g||p||v;if(!n&&y){var w=i.MISSING_INJECT_ANNOTATION+" argument "+t+" in class "+e+".";throw new Error(w)}var b=new c.Target(u.TargetTypeEnum.ConstructorArgument,f.targetName,d);return b.metadata=s,b}return null}function l(t,n,e,r,o){for(var i=[],u=0;u<o;u++){var a=u,s=d(a,t,n,e,r);null!==s&&i.push(s)}return i}function g(t,n){for(var e=t.getPropertiesMetadata(n),o=[],i=Object.keys(e),a=0,s=i;a<s.length;a++){var f=s[a],h=e[f],d=m(e[f]),l=d.targetName||f,p=d.inject||d.multiInject,v=new c.Target(u.TargetTypeEnum.ClassProperty,l,p);v.metadata=h,o.push(v)}var y=Object.getPrototypeOf(n.prototype).constructor;if(y!==Object){var w=g(t,y);o=r(r([],o),w)}return o}function p(t,n){var e=Object.getPrototypeOf(n.prototype).constructor;if(e!==Object){var r=s.getFunctionName(e),o=h(t,r,e,!0),i=o.map((function(t){return t.metadata.filter((function(t){return t.key===a.UNMANAGED_TAG}))})),u=[].concat.apply([],i).length,c=o.length-u;return c>0?c:p(t,e)}return 0}function m(t){var n={};return t.forEach((function(t){n[t.key.toString()]=t.value})),{inject:n[a.INJECT_TAG],multiInject:n[a.MULTI_INJECT_TAG],targetName:n[a.NAME_TAG],unmanaged:n[a.UNMANAGED_TAG]}}n.getDependencies=f,n.getBaseClassDependencyCount=p},8336:function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.BindingToSyntax=void 0;var r=e("30e3"),o=e("155f"),i=e("0fd9"),u=e("2cac"),a=function(){function t(t){this._binding=t}return t.prototype.to=function(t){return this._binding.type=o.BindingTypeEnum.Instance,this._binding.implementationType=t,new i.BindingInWhenOnSyntax(this._binding)},t.prototype.toSelf=function(){if("function"!==typeof this._binding.serviceIdentifier)throw new Error(""+r.INVALID_TO_SELF_VALUE);var t=this._binding.serviceIdentifier;return this.to(t)},t.prototype.toConstantValue=function(t){return this._binding.type=o.BindingTypeEnum.ConstantValue,this._binding.cache=t,this._binding.dynamicValue=null,this._binding.implementationType=null,this._binding.scope=o.BindingScopeEnum.Singleton,new u.BindingWhenOnSyntax(this._binding)},t.prototype.toDynamicValue=function(t){return this._binding.type=o.BindingTypeEnum.DynamicValue,this._binding.cache=null,this._binding.dynamicValue=t,this._binding.implementationType=null,new i.BindingInWhenOnSyntax(this._binding)},t.prototype.toConstructor=function(t){return this._binding.type=o.BindingTypeEnum.Constructor,this._binding.implementationType=t,this._binding.scope=o.BindingScopeEnum.Singleton,new u.BindingWhenOnSyntax(this._binding)},t.prototype.toFactory=function(t){return this._binding.type=o.BindingTypeEnum.Factory,this._binding.factory=t,this._binding.scope=o.BindingScopeEnum.Singleton,new u.BindingWhenOnSyntax(this._binding)},t.prototype.toFunction=function(t){if("function"!==typeof t)throw new Error(r.INVALID_FUNCTION_BINDING);var n=this.toConstantValue(t);return this._binding.type=o.BindingTypeEnum.Function,this._binding.scope=o.BindingScopeEnum.Singleton,n},t.prototype.toAutoFactory=function(t){return this._binding.type=o.BindingTypeEnum.Factory,this._binding.factory=function(n){var e=function(){return n.container.get(t)};return e},this._binding.scope=o.BindingScopeEnum.Singleton,new u.BindingWhenOnSyntax(this._binding)},t.prototype.toProvider=function(t){return this._binding.type=o.BindingTypeEnum.Provider,this._binding.provider=t,this._binding.scope=o.BindingScopeEnum.Singleton,new u.BindingWhenOnSyntax(this._binding)},t.prototype.toService=function(t){this.toDynamicValue((function(n){return n.container.get(t)}))},t}();n.BindingToSyntax=a},"87b3":function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.getBindingDictionary=n.createMockRequest=n.plan=void 0;var r=e("7685"),o=e("30e3"),i=e("155f"),u=e("c5f4"),a=e("a8af"),s=e("ba33"),c=e("a32f"),f=e("1979"),h=e("c8c0"),d=e("7dba"),l=e("c622"),g=e("757d");function p(t){return t._bindingDictionary}function m(t,n,e,r,o,i){var a=t?u.MULTI_INJECT_TAG:u.INJECT_TAG,s=new f.Metadata(a,e),c=new g.Target(n,r,e,s);if(void 0!==o){var h=new f.Metadata(o,i);c.metadata.push(h)}return c}function v(t,n,e,o,i){var u=b(e.container,i.serviceIdentifier),a=[];return u.length===r.BindingCount.NoBindingsAvailable&&e.container.options.autoBindInjectable&&"function"===typeof i.serviceIdentifier&&t.getConstructorMetadata(i.serviceIdentifier).compilerGeneratedMetadata&&(e.container.bind(i.serviceIdentifier).toSelf(),u=b(e.container,i.serviceIdentifier)),a=n?u:u.filter((function(t){var n=new l.Request(t.serviceIdentifier,e,o,t,i);return t.constraint(n)})),y(i.serviceIdentifier,a,i,e.container),a}function y(t,n,e,i){switch(n.length){case r.BindingCount.NoBindingsAvailable:if(e.isOptional())return n;var u=s.getServiceIdentifierAsString(t),a=o.NOT_REGISTERED;throw a+=s.listMetadataForTarget(u,e),a+=s.listRegisteredBindingsForServiceIdentifier(i,u,b),new Error(a);case r.BindingCount.OnlyOneBindingAvailable:if(!e.isArray())return n;case r.BindingCount.MultipleBindingsAvailable:default:if(e.isArray())return n;u=s.getServiceIdentifierAsString(t),a=o.AMBIGUOUS_MATCH+" "+u;throw a+=s.listRegisteredBindingsForServiceIdentifier(i,u,b),new Error(a)}}function w(t,n,e,r,u,a){var s,c;if(null===u){s=v(t,n,r,null,a),c=new l.Request(e,r,null,s,a);var f=new h.Plan(r,c);r.addPlan(f)}else s=v(t,n,r,u,a),c=u.addChildRequest(a.serviceIdentifier,s,a);s.forEach((function(n){var e=null;if(a.isArray())e=c.addChildRequest(n.serviceIdentifier,n,a);else{if(n.cache)return;e=c}if(n.type===i.BindingTypeEnum.Instance&&null!==n.implementationType){var u=d.getDependencies(t,n.implementationType);if(!r.container.options.skipBaseClassChecks){var s=d.getBaseClassDependencyCount(t,n.implementationType);if(u.length<s){var f=o.ARGUMENTS_LENGTH_MISMATCH(d.getFunctionName(n.implementationType));throw new Error(f)}}u.forEach((function(n){w(t,!1,n.serviceIdentifier,r,e,n)}))}}))}function b(t,n){var e=[],r=p(t);return r.hasKey(n)?e=r.get(n):null!==t.parent&&(e=b(t.parent,n)),e}function M(t,n,e,r,o,i,u,f){void 0===f&&(f=!1);var h=new c.Context(n),d=m(e,r,o,"",i,u);try{return w(t,f,o,h,null,d),h}catch(l){throw a.isStackOverflowExeption(l)&&h.plan&&s.circularDependencyToException(h.plan.rootRequest),l}}function _(t,n,e,r){var o=new g.Target(i.TargetTypeEnum.Variable,"",n,new f.Metadata(e,r)),u=new c.Context(t),a=new l.Request(n,u,null,[],o);return a}n.getBindingDictionary=p,n.plan=M,n.createMockRequest=_},"8c88":function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.multiInject=void 0;var r=e("c5f4"),o=e("1979"),i=e("66d7");function u(t){return function(n,e,u){var a=new o.Metadata(r.MULTI_INJECT_TAG,t);"number"===typeof u?i.tagParameter(n,e,u,a):i.tagProperty(n,e,a)}}n.multiInject=u},"8d8c":function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.optional=void 0;var r=e("c5f4"),o=e("1979"),i=e("66d7");function u(){return function(t,n,e){var u=new o.Metadata(r.OPTIONAL_TAG,!0);"number"===typeof e?i.tagParameter(t,n,e,u):i.tagProperty(t,n,u)}}n.optional=u},"9f62":function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.unmanaged=void 0;var r=e("c5f4"),o=e("1979"),i=e("66d7");function u(){return function(t,n,e){var u=new o.Metadata(r.UNMANAGED_TAG,!0);i.tagParameter(t,n,e,u)}}n.unmanaged=u},"9fe7":function(t,n,e){"use strict";e.r(n),e.d(n,"create",(function(){return o})),e.d(n,"clone",(function(){return i})),e.d(n,"length",(function(){return u})),e.d(n,"fromValues",(function(){return a})),e.d(n,"copy",(function(){return s})),e.d(n,"set",(function(){return c})),e.d(n,"add",(function(){return f})),e.d(n,"subtract",(function(){return h})),e.d(n,"multiply",(function(){return d})),e.d(n,"divide",(function(){return l})),e.d(n,"ceil",(function(){return g})),e.d(n,"floor",(function(){return p})),e.d(n,"min",(function(){return m})),e.d(n,"max",(function(){return v})),e.d(n,"round",(function(){return y})),e.d(n,"scale",(function(){return w})),e.d(n,"scaleAndAdd",(function(){return b})),e.d(n,"distance",(function(){return M})),e.d(n,"squaredDistance",(function(){return _})),e.d(n,"squaredLength",(function(){return E})),e.d(n,"negate",(function(){return x})),e.d(n,"inverse",(function(){return N})),e.d(n,"normalize",(function(){return S})),e.d(n,"dot",(function(){return T})),e.d(n,"cross",(function(){return A})),e.d(n,"lerp",(function(){return I})),e.d(n,"hermite",(function(){return O})),e.d(n,"bezier",(function(){return C})),e.d(n,"random",(function(){return j})),e.d(n,"transformMat4",(function(){return R})),e.d(n,"transformMat3",(function(){return k})),e.d(n,"transformQuat",(function(){return P})),e.d(n,"rotateX",(function(){return D})),e.d(n,"rotateY",(function(){return B})),e.d(n,"rotateZ",(function(){return L})),e.d(n,"angle",(function(){return G})),e.d(n,"zero",(function(){return q})),e.d(n,"str",(function(){return F})),e.d(n,"exactEquals",(function(){return U})),e.d(n,"equals",(function(){return V})),e.d(n,"sub",(function(){return z})),e.d(n,"mul",(function(){return W})),e.d(n,"div",(function(){return Y})),e.d(n,"dist",(function(){return H})),e.d(n,"sqrDist",(function(){return $})),e.d(n,"len",(function(){return J})),e.d(n,"sqrLen",(function(){return K})),e.d(n,"forEach",(function(){return Q}));var r=e("c94d");function o(){var t=new r["a"](3);return r["a"]!=Float32Array&&(t[0]=0,t[1]=0,t[2]=0),t}function i(t){var n=new r["a"](3);return n[0]=t[0],n[1]=t[1],n[2]=t[2],n}function u(t){var n=t[0],e=t[1],r=t[2];return Math.hypot(n,e,r)}function a(t,n,e){var o=new r["a"](3);return o[0]=t,o[1]=n,o[2]=e,o}function s(t,n){return t[0]=n[0],t[1]=n[1],t[2]=n[2],t}function c(t,n,e,r){return t[0]=n,t[1]=e,t[2]=r,t}function f(t,n,e){return t[0]=n[0]+e[0],t[1]=n[1]+e[1],t[2]=n[2]+e[2],t}function h(t,n,e){return t[0]=n[0]-e[0],t[1]=n[1]-e[1],t[2]=n[2]-e[2],t}function d(t,n,e){return t[0]=n[0]*e[0],t[1]=n[1]*e[1],t[2]=n[2]*e[2],t}function l(t,n,e){return t[0]=n[0]/e[0],t[1]=n[1]/e[1],t[2]=n[2]/e[2],t}function g(t,n){return t[0]=Math.ceil(n[0]),t[1]=Math.ceil(n[1]),t[2]=Math.ceil(n[2]),t}function p(t,n){return t[0]=Math.floor(n[0]),t[1]=Math.floor(n[1]),t[2]=Math.floor(n[2]),t}function m(t,n,e){return t[0]=Math.min(n[0],e[0]),t[1]=Math.min(n[1],e[1]),t[2]=Math.min(n[2],e[2]),t}function v(t,n,e){return t[0]=Math.max(n[0],e[0]),t[1]=Math.max(n[1],e[1]),t[2]=Math.max(n[2],e[2]),t}function y(t,n){return t[0]=Math.round(n[0]),t[1]=Math.round(n[1]),t[2]=Math.round(n[2]),t}function w(t,n,e){return t[0]=n[0]*e,t[1]=n[1]*e,t[2]=n[2]*e,t}function b(t,n,e,r){return t[0]=n[0]+e[0]*r,t[1]=n[1]+e[1]*r,t[2]=n[2]+e[2]*r,t}function M(t,n){var e=n[0]-t[0],r=n[1]-t[1],o=n[2]-t[2];return Math.hypot(e,r,o)}function _(t,n){var e=n[0]-t[0],r=n[1]-t[1],o=n[2]-t[2];return e*e+r*r+o*o}function E(t){var n=t[0],e=t[1],r=t[2];return n*n+e*e+r*r}function x(t,n){return t[0]=-n[0],t[1]=-n[1],t[2]=-n[2],t}function N(t,n){return t[0]=1/n[0],t[1]=1/n[1],t[2]=1/n[2],t}function S(t,n){var e=n[0],r=n[1],o=n[2],i=e*e+r*r+o*o;return i>0&&(i=1/Math.sqrt(i)),t[0]=n[0]*i,t[1]=n[1]*i,t[2]=n[2]*i,t}function T(t,n){return t[0]*n[0]+t[1]*n[1]+t[2]*n[2]}function A(t,n,e){var r=n[0],o=n[1],i=n[2],u=e[0],a=e[1],s=e[2];return t[0]=o*s-i*a,t[1]=i*u-r*s,t[2]=r*a-o*u,t}function I(t,n,e,r){var o=n[0],i=n[1],u=n[2];return t[0]=o+r*(e[0]-o),t[1]=i+r*(e[1]-i),t[2]=u+r*(e[2]-u),t}function O(t,n,e,r,o,i){var u=i*i,a=u*(2*i-3)+1,s=u*(i-2)+i,c=u*(i-1),f=u*(3-2*i);return t[0]=n[0]*a+e[0]*s+r[0]*c+o[0]*f,t[1]=n[1]*a+e[1]*s+r[1]*c+o[1]*f,t[2]=n[2]*a+e[2]*s+r[2]*c+o[2]*f,t}function C(t,n,e,r,o,i){var u=1-i,a=u*u,s=i*i,c=a*u,f=3*i*a,h=3*s*u,d=s*i;return t[0]=n[0]*c+e[0]*f+r[0]*h+o[0]*d,t[1]=n[1]*c+e[1]*f+r[1]*h+o[1]*d,t[2]=n[2]*c+e[2]*f+r[2]*h+o[2]*d,t}function j(t,n){n=n||1;var e=2*r["c"]()*Math.PI,o=2*r["c"]()-1,i=Math.sqrt(1-o*o)*n;return t[0]=Math.cos(e)*i,t[1]=Math.sin(e)*i,t[2]=o*n,t}function R(t,n,e){var r=n[0],o=n[1],i=n[2],u=e[3]*r+e[7]*o+e[11]*i+e[15];return u=u||1,t[0]=(e[0]*r+e[4]*o+e[8]*i+e[12])/u,t[1]=(e[1]*r+e[5]*o+e[9]*i+e[13])/u,t[2]=(e[2]*r+e[6]*o+e[10]*i+e[14])/u,t}function k(t,n,e){var r=n[0],o=n[1],i=n[2];return t[0]=r*e[0]+o*e[3]+i*e[6],t[1]=r*e[1]+o*e[4]+i*e[7],t[2]=r*e[2]+o*e[5]+i*e[8],t}function P(t,n,e){var r=e[0],o=e[1],i=e[2],u=e[3],a=n[0],s=n[1],c=n[2],f=o*c-i*s,h=i*a-r*c,d=r*s-o*a,l=o*d-i*h,g=i*f-r*d,p=r*h-o*f,m=2*u;return f*=m,h*=m,d*=m,l*=2,g*=2,p*=2,t[0]=a+f+l,t[1]=s+h+g,t[2]=c+d+p,t}function D(t,n,e,r){var o=[],i=[];return o[0]=n[0]-e[0],o[1]=n[1]-e[1],o[2]=n[2]-e[2],i[0]=o[0],i[1]=o[1]*Math.cos(r)-o[2]*Math.sin(r),i[2]=o[1]*Math.sin(r)+o[2]*Math.cos(r),t[0]=i[0]+e[0],t[1]=i[1]+e[1],t[2]=i[2]+e[2],t}function B(t,n,e,r){var o=[],i=[];return o[0]=n[0]-e[0],o[1]=n[1]-e[1],o[2]=n[2]-e[2],i[0]=o[2]*Math.sin(r)+o[0]*Math.cos(r),i[1]=o[1],i[2]=o[2]*Math.cos(r)-o[0]*Math.sin(r),t[0]=i[0]+e[0],t[1]=i[1]+e[1],t[2]=i[2]+e[2],t}function L(t,n,e,r){var o=[],i=[];return o[0]=n[0]-e[0],o[1]=n[1]-e[1],o[2]=n[2]-e[2],i[0]=o[0]*Math.cos(r)-o[1]*Math.sin(r),i[1]=o[0]*Math.sin(r)+o[1]*Math.cos(r),i[2]=o[2],t[0]=i[0]+e[0],t[1]=i[1]+e[1],t[2]=i[2]+e[2],t}function G(t,n){var e=t[0],r=t[1],o=t[2],i=n[0],u=n[1],a=n[2],s=Math.sqrt(e*e+r*r+o*o),c=Math.sqrt(i*i+u*u+a*a),f=s*c,h=f&&T(t,n)/f;return Math.acos(Math.min(Math.max(h,-1),1))}function q(t){return t[0]=0,t[1]=0,t[2]=0,t}function F(t){return"vec3("+t[0]+", "+t[1]+", "+t[2]+")"}function U(t,n){return t[0]===n[0]&&t[1]===n[1]&&t[2]===n[2]}function V(t,n){var e=t[0],o=t[1],i=t[2],u=n[0],a=n[1],s=n[2];return Math.abs(e-u)<=r["b"]*Math.max(1,Math.abs(e),Math.abs(u))&&Math.abs(o-a)<=r["b"]*Math.max(1,Math.abs(o),Math.abs(a))&&Math.abs(i-s)<=r["b"]*Math.max(1,Math.abs(i),Math.abs(s))}var z=h,W=d,Y=l,H=M,$=_,J=u,K=E,Q=function(){var t=o();return function(n,e,r,o,i,u){var a,s;for(e||(e=3),r||(r=0),s=o?Math.min(o*e+r,n.length):n.length,a=r;a<s;a+=e)t[0]=n[a],t[1]=n[a+1],t[2]=n[a+2],i(t,t,u),n[a]=t[0],n[a+1]=t[1],n[a+2]=t[2];return n}}()},a1a5:function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.targetName=void 0;var r=e("c5f4"),o=e("1979"),i=e("66d7");function u(t){return function(n,e,u){var a=new o.Metadata(r.NAME_TAG,t);i.tagParameter(n,e,u,a)}}n.targetName=u},a32f:function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.Context=void 0;var r=e("77d3"),o=function(){function t(t){this.id=r.id(),this.container=t}return t.prototype.addPlan=function(t){this.plan=t},t.prototype.setCurrentRequest=function(t){this.currentRequest=t},t}();n.Context=o},a654:function(t,n,e){t.exports=e("0b70")},a8af:function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.isStackOverflowExeption=void 0;var r=e("30e3");function o(t){return t instanceof RangeError||t.message===r.STACK_OVERFLOW}n.isStackOverflowExeption=o},b23e:function(t,n){function e(t,n,e){var r=Math.cos(e),o=Math.sin(e),i=n[0],u=n[1];return t[0]=i*r-u*o,t[1]=i*o+u*r,t}t.exports=e},b4ae:function(t,n,e){"use strict";e.r(n),e.d(n,"AbstractMatrix",(function(){return V})),e.d(n,"default",(function(){return Y})),e.d(n,"Matrix",(function(){return Y})),e.d(n,"MatrixColumnView",(function(){return $})),e.d(n,"MatrixColumnSelectionView",(function(){return J})),e.d(n,"MatrixFlipColumnView",(function(){return K})),e.d(n,"MatrixFlipRowView",(function(){return Q})),e.d(n,"MatrixRowView",(function(){return Z})),e.d(n,"MatrixRowSelectionView",(function(){return X})),e.d(n,"MatrixSelectionView",(function(){return tt})),e.d(n,"MatrixSubView",(function(){return nt})),e.d(n,"MatrixTransposeView",(function(){return et})),e.d(n,"wrap",(function(){return it})),e.d(n,"WrapperMatrix1D",(function(){return rt})),e.d(n,"WrapperMatrix2D",(function(){return ot})),e.d(n,"solve",(function(){return ht})),e.d(n,"inverse",(function(){return ft})),e.d(n,"determinant",(function(){return dt})),e.d(n,"linearDependencies",(function(){return pt})),e.d(n,"pseudoInverse",(function(){return mt})),e.d(n,"covariance",(function(){return vt})),e.d(n,"correlation",(function(){return yt})),e.d(n,"SingularValueDecomposition",(function(){return ct})),e.d(n,"SVD",(function(){return ct})),e.d(n,"EigenvalueDecomposition",(function(){return wt})),e.d(n,"EVD",(function(){return wt})),e.d(n,"CholeskyDecomposition",(function(){return Nt})),e.d(n,"CHO",(function(){return Nt})),e.d(n,"LuDecomposition",(function(){return ut})),e.d(n,"LU",(function(){return ut})),e.d(n,"QrDecomposition",(function(){return st})),e.d(n,"QR",(function(){return st})),e.d(n,"Nipals",(function(){return St})),e.d(n,"NIPALS",(function(){return St}));const r=Object.prototype.toString;function o(t){return r.call(t).endsWith("Array]")}function i(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!o(t))throw new TypeError("input must be an array");if(0===t.length)throw new TypeError("input must not be empty");var e=n.fromIndex,r=void 0===e?0:e,i=n.toIndex,u=void 0===i?t.length:i;if(r<0||r>=t.length||!Number.isInteger(r))throw new Error("fromIndex must be a positive integer smaller than length");if(u<=r||u>t.length||!Number.isInteger(u))throw new Error("toIndex must be an integer greater than fromIndex and at most equal to length");for(var a=t[r],s=r+1;s<u;s++)t[s]>a&&(a=t[s]);return a}function u(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!o(t))throw new TypeError("input must be an array");if(0===t.length)throw new TypeError("input must not be empty");var e=n.fromIndex,r=void 0===e?0:e,i=n.toIndex,u=void 0===i?t.length:i;if(r<0||r>=t.length||!Number.isInteger(r))throw new Error("fromIndex must be a positive integer smaller than length");if(u<=r||u>t.length||!Number.isInteger(u))throw new Error("toIndex must be an integer greater than fromIndex and at most equal to length");for(var a=t[r],s=r+1;s<u;s++)t[s]<a&&(a=t[s]);return a}function a(t){var n,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!o(t))throw new TypeError("input must be an array");if(0===t.length)throw new TypeError("input must not be empty");if(void 0!==e.output){if(!o(e.output))throw new TypeError("output option must be an array if specified");n=e.output}else n=new Array(t.length);var r=u(t),a=i(t);if(r===a)throw new RangeError("minimum and maximum input values are equal. Cannot rescale a constant array");var s=e.min,c=void 0===s?e.autoMinMax?r:0:s,f=e.max,h=void 0===f?e.autoMinMax?a:1:f;if(c>=h)throw new RangeError("min option must be smaller than max option");for(var d=(h-c)/(a-r),l=0;l<t.length;l++)n[l]=(t[l]-r)*d+c;return n}const s=" ".repeat(2),c=" ".repeat(4);function f(){return h(this)}function h(t,n={}){const{maxRows:e=15,maxColumns:r=10,maxNumSize:o=8}=n;return`${t.constructor.name} {\n${s}[\n${c}${d(t,e,r,o)}\n${s}]\n${s}rows: ${t.rows}\n${s}columns: ${t.columns}\n}`}function d(t,n,e,r){const{rows:o,columns:i}=t,u=Math.min(o,n),a=Math.min(i,e),s=[];for(let c=0;c<u;c++){let n=[];for(let e=0;e<a;e++)n.push(l(t.get(c,e),r));s.push(""+n.join(" "))}return a!==i&&(s[s.length-1]+=` ... ${i-e} more columns`),u!==o&&s.push(`... ${o-n} more rows`),s.join("\n"+c)}function l(t,n){const e=String(t);if(e.length<=n)return e.padEnd(n," ");const r=t.toPrecision(n-2);if(r.length<=n)return r;const o=t.toExponential(n-2),i=o.indexOf("e"),u=o.slice(i);return o.slice(0,n-u.length)+u}function g(t,n){t.prototype.add=function(t){return"number"===typeof t?this.addS(t):this.addM(t)},t.prototype.addS=function(t){for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)this.set(n,e,this.get(n,e)+t);return this},t.prototype.addM=function(t){if(t=n.checkMatrix(t),this.rows!==t.rows||this.columns!==t.columns)throw new RangeError("Matrices dimensions must be equal");for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)this.set(n,e,this.get(n,e)+t.get(n,e));return this},t.add=function(t,e){const r=new n(t);return r.add(e)},t.prototype.sub=function(t){return"number"===typeof t?this.subS(t):this.subM(t)},t.prototype.subS=function(t){for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)this.set(n,e,this.get(n,e)-t);return this},t.prototype.subM=function(t){if(t=n.checkMatrix(t),this.rows!==t.rows||this.columns!==t.columns)throw new RangeError("Matrices dimensions must be equal");for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)this.set(n,e,this.get(n,e)-t.get(n,e));return this},t.sub=function(t,e){const r=new n(t);return r.sub(e)},t.prototype.subtract=t.prototype.sub,t.prototype.subtractS=t.prototype.subS,t.prototype.subtractM=t.prototype.subM,t.subtract=t.sub,t.prototype.mul=function(t){return"number"===typeof t?this.mulS(t):this.mulM(t)},t.prototype.mulS=function(t){for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)this.set(n,e,this.get(n,e)*t);return this},t.prototype.mulM=function(t){if(t=n.checkMatrix(t),this.rows!==t.rows||this.columns!==t.columns)throw new RangeError("Matrices dimensions must be equal");for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)this.set(n,e,this.get(n,e)*t.get(n,e));return this},t.mul=function(t,e){const r=new n(t);return r.mul(e)},t.prototype.multiply=t.prototype.mul,t.prototype.multiplyS=t.prototype.mulS,t.prototype.multiplyM=t.prototype.mulM,t.multiply=t.mul,t.prototype.div=function(t){return"number"===typeof t?this.divS(t):this.divM(t)},t.prototype.divS=function(t){for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)this.set(n,e,this.get(n,e)/t);return this},t.prototype.divM=function(t){if(t=n.checkMatrix(t),this.rows!==t.rows||this.columns!==t.columns)throw new RangeError("Matrices dimensions must be equal");for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)this.set(n,e,this.get(n,e)/t.get(n,e));return this},t.div=function(t,e){const r=new n(t);return r.div(e)},t.prototype.divide=t.prototype.div,t.prototype.divideS=t.prototype.divS,t.prototype.divideM=t.prototype.divM,t.divide=t.div,t.prototype.mod=function(t){return"number"===typeof t?this.modS(t):this.modM(t)},t.prototype.modS=function(t){for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)this.set(n,e,this.get(n,e)%t);return this},t.prototype.modM=function(t){if(t=n.checkMatrix(t),this.rows!==t.rows||this.columns!==t.columns)throw new RangeError("Matrices dimensions must be equal");for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)this.set(n,e,this.get(n,e)%t.get(n,e));return this},t.mod=function(t,e){const r=new n(t);return r.mod(e)},t.prototype.modulus=t.prototype.mod,t.prototype.modulusS=t.prototype.modS,t.prototype.modulusM=t.prototype.modM,t.modulus=t.mod,t.prototype.and=function(t){return"number"===typeof t?this.andS(t):this.andM(t)},t.prototype.andS=function(t){for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)this.set(n,e,this.get(n,e)&t);return this},t.prototype.andM=function(t){if(t=n.checkMatrix(t),this.rows!==t.rows||this.columns!==t.columns)throw new RangeError("Matrices dimensions must be equal");for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)this.set(n,e,this.get(n,e)&t.get(n,e));return this},t.and=function(t,e){const r=new n(t);return r.and(e)},t.prototype.or=function(t){return"number"===typeof t?this.orS(t):this.orM(t)},t.prototype.orS=function(t){for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)this.set(n,e,this.get(n,e)|t);return this},t.prototype.orM=function(t){if(t=n.checkMatrix(t),this.rows!==t.rows||this.columns!==t.columns)throw new RangeError("Matrices dimensions must be equal");for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)this.set(n,e,this.get(n,e)|t.get(n,e));return this},t.or=function(t,e){const r=new n(t);return r.or(e)},t.prototype.xor=function(t){return"number"===typeof t?this.xorS(t):this.xorM(t)},t.prototype.xorS=function(t){for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)this.set(n,e,this.get(n,e)^t);return this},t.prototype.xorM=function(t){if(t=n.checkMatrix(t),this.rows!==t.rows||this.columns!==t.columns)throw new RangeError("Matrices dimensions must be equal");for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)this.set(n,e,this.get(n,e)^t.get(n,e));return this},t.xor=function(t,e){const r=new n(t);return r.xor(e)},t.prototype.leftShift=function(t){return"number"===typeof t?this.leftShiftS(t):this.leftShiftM(t)},t.prototype.leftShiftS=function(t){for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)this.set(n,e,this.get(n,e)<<t);return this},t.prototype.leftShiftM=function(t){if(t=n.checkMatrix(t),this.rows!==t.rows||this.columns!==t.columns)throw new RangeError("Matrices dimensions must be equal");for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)this.set(n,e,this.get(n,e)<<t.get(n,e));return this},t.leftShift=function(t,e){const r=new n(t);return r.leftShift(e)},t.prototype.signPropagatingRightShift=function(t){return"number"===typeof t?this.signPropagatingRightShiftS(t):this.signPropagatingRightShiftM(t)},t.prototype.signPropagatingRightShiftS=function(t){for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)this.set(n,e,this.get(n,e)>>t);return this},t.prototype.signPropagatingRightShiftM=function(t){if(t=n.checkMatrix(t),this.rows!==t.rows||this.columns!==t.columns)throw new RangeError("Matrices dimensions must be equal");for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)this.set(n,e,this.get(n,e)>>t.get(n,e));return this},t.signPropagatingRightShift=function(t,e){const r=new n(t);return r.signPropagatingRightShift(e)},t.prototype.rightShift=function(t){return"number"===typeof t?this.rightShiftS(t):this.rightShiftM(t)},t.prototype.rightShiftS=function(t){for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)this.set(n,e,this.get(n,e)>>>t);return this},t.prototype.rightShiftM=function(t){if(t=n.checkMatrix(t),this.rows!==t.rows||this.columns!==t.columns)throw new RangeError("Matrices dimensions must be equal");for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)this.set(n,e,this.get(n,e)>>>t.get(n,e));return this},t.rightShift=function(t,e){const r=new n(t);return r.rightShift(e)},t.prototype.zeroFillRightShift=t.prototype.rightShift,t.prototype.zeroFillRightShiftS=t.prototype.rightShiftS,t.prototype.zeroFillRightShiftM=t.prototype.rightShiftM,t.zeroFillRightShift=t.rightShift,t.prototype.not=function(){for(let t=0;t<this.rows;t++)for(let n=0;n<this.columns;n++)this.set(t,n,~this.get(t,n));return this},t.not=function(t){const e=new n(t);return e.not()},t.prototype.abs=function(){for(let t=0;t<this.rows;t++)for(let n=0;n<this.columns;n++)this.set(t,n,Math.abs(this.get(t,n)));return this},t.abs=function(t){const e=new n(t);return e.abs()},t.prototype.acos=function(){for(let t=0;t<this.rows;t++)for(let n=0;n<this.columns;n++)this.set(t,n,Math.acos(this.get(t,n)));return this},t.acos=function(t){const e=new n(t);return e.acos()},t.prototype.acosh=function(){for(let t=0;t<this.rows;t++)for(let n=0;n<this.columns;n++)this.set(t,n,Math.acosh(this.get(t,n)));return this},t.acosh=function(t){const e=new n(t);return e.acosh()},t.prototype.asin=function(){for(let t=0;t<this.rows;t++)for(let n=0;n<this.columns;n++)this.set(t,n,Math.asin(this.get(t,n)));return this},t.asin=function(t){const e=new n(t);return e.asin()},t.prototype.asinh=function(){for(let t=0;t<this.rows;t++)for(let n=0;n<this.columns;n++)this.set(t,n,Math.asinh(this.get(t,n)));return this},t.asinh=function(t){const e=new n(t);return e.asinh()},t.prototype.atan=function(){for(let t=0;t<this.rows;t++)for(let n=0;n<this.columns;n++)this.set(t,n,Math.atan(this.get(t,n)));return this},t.atan=function(t){const e=new n(t);return e.atan()},t.prototype.atanh=function(){for(let t=0;t<this.rows;t++)for(let n=0;n<this.columns;n++)this.set(t,n,Math.atanh(this.get(t,n)));return this},t.atanh=function(t){const e=new n(t);return e.atanh()},t.prototype.cbrt=function(){for(let t=0;t<this.rows;t++)for(let n=0;n<this.columns;n++)this.set(t,n,Math.cbrt(this.get(t,n)));return this},t.cbrt=function(t){const e=new n(t);return e.cbrt()},t.prototype.ceil=function(){for(let t=0;t<this.rows;t++)for(let n=0;n<this.columns;n++)this.set(t,n,Math.ceil(this.get(t,n)));return this},t.ceil=function(t){const e=new n(t);return e.ceil()},t.prototype.clz32=function(){for(let t=0;t<this.rows;t++)for(let n=0;n<this.columns;n++)this.set(t,n,Math.clz32(this.get(t,n)));return this},t.clz32=function(t){const e=new n(t);return e.clz32()},t.prototype.cos=function(){for(let t=0;t<this.rows;t++)for(let n=0;n<this.columns;n++)this.set(t,n,Math.cos(this.get(t,n)));return this},t.cos=function(t){const e=new n(t);return e.cos()},t.prototype.cosh=function(){for(let t=0;t<this.rows;t++)for(let n=0;n<this.columns;n++)this.set(t,n,Math.cosh(this.get(t,n)));return this},t.cosh=function(t){const e=new n(t);return e.cosh()},t.prototype.exp=function(){for(let t=0;t<this.rows;t++)for(let n=0;n<this.columns;n++)this.set(t,n,Math.exp(this.get(t,n)));return this},t.exp=function(t){const e=new n(t);return e.exp()},t.prototype.expm1=function(){for(let t=0;t<this.rows;t++)for(let n=0;n<this.columns;n++)this.set(t,n,Math.expm1(this.get(t,n)));return this},t.expm1=function(t){const e=new n(t);return e.expm1()},t.prototype.floor=function(){for(let t=0;t<this.rows;t++)for(let n=0;n<this.columns;n++)this.set(t,n,Math.floor(this.get(t,n)));return this},t.floor=function(t){const e=new n(t);return e.floor()},t.prototype.fround=function(){for(let t=0;t<this.rows;t++)for(let n=0;n<this.columns;n++)this.set(t,n,Math.fround(this.get(t,n)));return this},t.fround=function(t){const e=new n(t);return e.fround()},t.prototype.log=function(){for(let t=0;t<this.rows;t++)for(let n=0;n<this.columns;n++)this.set(t,n,Math.log(this.get(t,n)));return this},t.log=function(t){const e=new n(t);return e.log()},t.prototype.log1p=function(){for(let t=0;t<this.rows;t++)for(let n=0;n<this.columns;n++)this.set(t,n,Math.log1p(this.get(t,n)));return this},t.log1p=function(t){const e=new n(t);return e.log1p()},t.prototype.log10=function(){for(let t=0;t<this.rows;t++)for(let n=0;n<this.columns;n++)this.set(t,n,Math.log10(this.get(t,n)));return this},t.log10=function(t){const e=new n(t);return e.log10()},t.prototype.log2=function(){for(let t=0;t<this.rows;t++)for(let n=0;n<this.columns;n++)this.set(t,n,Math.log2(this.get(t,n)));return this},t.log2=function(t){const e=new n(t);return e.log2()},t.prototype.round=function(){for(let t=0;t<this.rows;t++)for(let n=0;n<this.columns;n++)this.set(t,n,Math.round(this.get(t,n)));return this},t.round=function(t){const e=new n(t);return e.round()},t.prototype.sign=function(){for(let t=0;t<this.rows;t++)for(let n=0;n<this.columns;n++)this.set(t,n,Math.sign(this.get(t,n)));return this},t.sign=function(t){const e=new n(t);return e.sign()},t.prototype.sin=function(){for(let t=0;t<this.rows;t++)for(let n=0;n<this.columns;n++)this.set(t,n,Math.sin(this.get(t,n)));return this},t.sin=function(t){const e=new n(t);return e.sin()},t.prototype.sinh=function(){for(let t=0;t<this.rows;t++)for(let n=0;n<this.columns;n++)this.set(t,n,Math.sinh(this.get(t,n)));return this},t.sinh=function(t){const e=new n(t);return e.sinh()},t.prototype.sqrt=function(){for(let t=0;t<this.rows;t++)for(let n=0;n<this.columns;n++)this.set(t,n,Math.sqrt(this.get(t,n)));return this},t.sqrt=function(t){const e=new n(t);return e.sqrt()},t.prototype.tan=function(){for(let t=0;t<this.rows;t++)for(let n=0;n<this.columns;n++)this.set(t,n,Math.tan(this.get(t,n)));return this},t.tan=function(t){const e=new n(t);return e.tan()},t.prototype.tanh=function(){for(let t=0;t<this.rows;t++)for(let n=0;n<this.columns;n++)this.set(t,n,Math.tanh(this.get(t,n)));return this},t.tanh=function(t){const e=new n(t);return e.tanh()},t.prototype.trunc=function(){for(let t=0;t<this.rows;t++)for(let n=0;n<this.columns;n++)this.set(t,n,Math.trunc(this.get(t,n)));return this},t.trunc=function(t){const e=new n(t);return e.trunc()},t.pow=function(t,e){const r=new n(t);return r.pow(e)},t.prototype.pow=function(t){return"number"===typeof t?this.powS(t):this.powM(t)},t.prototype.powS=function(t){for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)this.set(n,e,Math.pow(this.get(n,e),t));return this},t.prototype.powM=function(t){if(t=n.checkMatrix(t),this.rows!==t.rows||this.columns!==t.columns)throw new RangeError("Matrices dimensions must be equal");for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)this.set(n,e,Math.pow(this.get(n,e),t.get(n,e)));return this}}function p(t,n,e){let r=e?t.rows:t.rows-1;if(n<0||n>r)throw new RangeError("Row index out of range")}function m(t,n,e){let r=e?t.columns:t.columns-1;if(n<0||n>r)throw new RangeError("Column index out of range")}function v(t,n){if(n.to1DArray&&(n=n.to1DArray()),n.length!==t.columns)throw new RangeError("vector size must be the same as the number of columns");return n}function y(t,n){if(n.to1DArray&&(n=n.to1DArray()),n.length!==t.rows)throw new RangeError("vector size must be the same as the number of rows");return n}function w(t,n){if(!o(n))throw new TypeError("row indices must be an array");for(let e=0;e<n.length;e++)if(n[e]<0||n[e]>=t.rows)throw new RangeError("row indices are out of range")}function b(t,n){if(!o(n))throw new TypeError("column indices must be an array");for(let e=0;e<n.length;e++)if(n[e]<0||n[e]>=t.columns)throw new RangeError("column indices are out of range")}function M(t,n,e,r,o){if(5!==arguments.length)throw new RangeError("expected 4 arguments");if(E("startRow",n),E("endRow",e),E("startColumn",r),E("endColumn",o),n>e||r>o||n<0||n>=t.rows||e<0||e>=t.rows||r<0||r>=t.columns||o<0||o>=t.columns)throw new RangeError("Submatrix indices are out of range")}function _(t,n=0){let e=[];for(let r=0;r<t;r++)e.push(n);return e}function E(t,n){if("number"!==typeof n)throw new TypeError(t+" must be a number")}function x(t){if(t.isEmpty())throw new Error("Empty matrix has no elements to index")}function N(t){let n=_(t.rows);for(let e=0;e<t.rows;++e)for(let r=0;r<t.columns;++r)n[e]+=t.get(e,r);return n}function S(t){let n=_(t.columns);for(let e=0;e<t.rows;++e)for(let r=0;r<t.columns;++r)n[r]+=t.get(e,r);return n}function T(t){let n=0;for(let e=0;e<t.rows;e++)for(let r=0;r<t.columns;r++)n+=t.get(e,r);return n}function A(t){let n=_(t.rows,1);for(let e=0;e<t.rows;++e)for(let r=0;r<t.columns;++r)n[e]*=t.get(e,r);return n}function I(t){let n=_(t.columns,1);for(let e=0;e<t.rows;++e)for(let r=0;r<t.columns;++r)n[r]*=t.get(e,r);return n}function O(t){let n=1;for(let e=0;e<t.rows;e++)for(let r=0;r<t.columns;r++)n*=t.get(e,r);return n}function C(t,n,e){const r=t.rows,o=t.columns,i=[];for(let u=0;u<r;u++){let r=0,a=0,s=0;for(let n=0;n<o;n++)s=t.get(u,n)-e[u],r+=s,a+=s*s;n?i.push((a-r*r/o)/(o-1)):i.push((a-r*r/o)/o)}return i}function j(t,n,e){const r=t.rows,o=t.columns,i=[];for(let u=0;u<o;u++){let o=0,a=0,s=0;for(let n=0;n<r;n++)s=t.get(n,u)-e[u],o+=s,a+=s*s;n?i.push((a-o*o/r)/(r-1)):i.push((a-o*o/r)/r)}return i}function R(t,n,e){const r=t.rows,o=t.columns,i=r*o;let u=0,a=0,s=0;for(let c=0;c<r;c++)for(let n=0;n<o;n++)s=t.get(c,n)-e,u+=s,a+=s*s;return n?(a-u*u/i)/(i-1):(a-u*u/i)/i}function k(t,n){for(let e=0;e<t.rows;e++)for(let r=0;r<t.columns;r++)t.set(e,r,t.get(e,r)-n[e])}function P(t,n){for(let e=0;e<t.rows;e++)for(let r=0;r<t.columns;r++)t.set(e,r,t.get(e,r)-n[r])}function D(t,n){for(let e=0;e<t.rows;e++)for(let r=0;r<t.columns;r++)t.set(e,r,t.get(e,r)-n)}function B(t){const n=[];for(let e=0;e<t.rows;e++){let r=0;for(let n=0;n<t.columns;n++)r+=Math.pow(t.get(e,n),2)/(t.columns-1);n.push(Math.sqrt(r))}return n}function L(t,n){for(let e=0;e<t.rows;e++)for(let r=0;r<t.columns;r++)t.set(e,r,t.get(e,r)/n[e])}function G(t){const n=[];for(let e=0;e<t.columns;e++){let r=0;for(let n=0;n<t.rows;n++)r+=Math.pow(t.get(n,e),2)/(t.rows-1);n.push(Math.sqrt(r))}return n}function q(t,n){for(let e=0;e<t.rows;e++)for(let r=0;r<t.columns;r++)t.set(e,r,t.get(e,r)/n[r])}function F(t){const n=t.size-1;let e=0;for(let r=0;r<t.columns;r++)for(let o=0;o<t.rows;o++)e+=Math.pow(t.get(o,r),2)/n;return Math.sqrt(e)}function U(t,n){for(let e=0;e<t.rows;e++)for(let r=0;r<t.columns;r++)t.set(e,r,t.get(e,r)/n)}class V{static from1DArray(t,n,e){let r=t*n;if(r!==e.length)throw new RangeError("data length does not match given dimensions");let o=new Y(t,n);for(let i=0;i<t;i++)for(let t=0;t<n;t++)o.set(i,t,e[i*n+t]);return o}static rowVector(t){let n=new Y(1,t.length);for(let e=0;e<t.length;e++)n.set(0,e,t[e]);return n}static columnVector(t){let n=new Y(t.length,1);for(let e=0;e<t.length;e++)n.set(e,0,t[e]);return n}static zeros(t,n){return new Y(t,n)}static ones(t,n){return new Y(t,n).fill(1)}static rand(t,n,e={}){if("object"!==typeof e)throw new TypeError("options must be an object");const{random:r=Math.random}=e;let o=new Y(t,n);for(let i=0;i<t;i++)for(let t=0;t<n;t++)o.set(i,t,r());return o}static randInt(t,n,e={}){if("object"!==typeof e)throw new TypeError("options must be an object");const{min:r=0,max:o=1e3,random:i=Math.random}=e;if(!Number.isInteger(r))throw new TypeError("min must be an integer");if(!Number.isInteger(o))throw new TypeError("max must be an integer");if(r>=o)throw new RangeError("min must be smaller than max");let u=o-r,a=new Y(t,n);for(let s=0;s<t;s++)for(let t=0;t<n;t++){let n=r+Math.round(i()*u);a.set(s,t,n)}return a}static eye(t,n,e){void 0===n&&(n=t),void 0===e&&(e=1);let r=Math.min(t,n),o=this.zeros(t,n);for(let i=0;i<r;i++)o.set(i,i,e);return o}static diag(t,n,e){let r=t.length;void 0===n&&(n=r),void 0===e&&(e=n);let o=Math.min(r,n,e),i=this.zeros(n,e);for(let u=0;u<o;u++)i.set(u,u,t[u]);return i}static min(t,n){t=this.checkMatrix(t),n=this.checkMatrix(n);let e=t.rows,r=t.columns,o=new Y(e,r);for(let i=0;i<e;i++)for(let e=0;e<r;e++)o.set(i,e,Math.min(t.get(i,e),n.get(i,e)));return o}static max(t,n){t=this.checkMatrix(t),n=this.checkMatrix(n);let e=t.rows,r=t.columns,o=new this(e,r);for(let i=0;i<e;i++)for(let e=0;e<r;e++)o.set(i,e,Math.max(t.get(i,e),n.get(i,e)));return o}static checkMatrix(t){return V.isMatrix(t)?t:new Y(t)}static isMatrix(t){return null!=t&&"Matrix"===t.klass}get size(){return this.rows*this.columns}apply(t){if("function"!==typeof t)throw new TypeError("callback must be a function");for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)t.call(this,n,e);return this}to1DArray(){let t=[];for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)t.push(this.get(n,e));return t}to2DArray(){let t=[];for(let n=0;n<this.rows;n++){t.push([]);for(let e=0;e<this.columns;e++)t[n].push(this.get(n,e))}return t}toJSON(){return this.to2DArray()}isRowVector(){return 1===this.rows}isColumnVector(){return 1===this.columns}isVector(){return 1===this.rows||1===this.columns}isSquare(){return this.rows===this.columns}isEmpty(){return 0===this.rows||0===this.columns}isSymmetric(){if(this.isSquare()){for(let t=0;t<this.rows;t++)for(let n=0;n<=t;n++)if(this.get(t,n)!==this.get(n,t))return!1;return!0}return!1}isEchelonForm(){let t=0,n=0,e=-1,r=!0,o=!1;while(t<this.rows&&r){n=0,o=!1;while(n<this.columns&&!1===o)0===this.get(t,n)?n++:1===this.get(t,n)&&n>e?(o=!0,e=n):(r=!1,o=!0);t++}return r}isReducedEchelonForm(){let t=0,n=0,e=-1,r=!0,o=!1;while(t<this.rows&&r){n=0,o=!1;while(n<this.columns&&!1===o)0===this.get(t,n)?n++:1===this.get(t,n)&&n>e?(o=!0,e=n):(r=!1,o=!0);for(let e=n+1;e<this.rows;e++)0!==this.get(t,e)&&(r=!1);t++}return r}echelonForm(){let t=this.clone(),n=0,e=0;while(n<t.rows&&e<t.columns){let r=n;for(let o=n;o<t.rows;o++)t.get(o,e)>t.get(r,e)&&(r=o);if(0===t.get(r,e))e++;else{t.swapRows(n,r);let o=t.get(n,e);for(let r=e;r<t.columns;r++)t.set(n,r,t.get(n,r)/o);for(let r=n+1;r<t.rows;r++){let o=t.get(r,e)/t.get(n,e);t.set(r,e,0);for(let i=e+1;i<t.columns;i++)t.set(r,i,t.get(r,i)-t.get(n,i)*o)}n++,e++}}return t}reducedEchelonForm(){let t=this.echelonForm(),n=t.columns,e=t.rows,r=e-1;while(r>=0)if(0===t.maxRow(r))r--;else{let o=0,i=!1;while(o<e&&!1===i)1===t.get(r,o)?i=!0:o++;for(let e=0;e<r;e++){let i=t.get(e,o);for(let u=o;u<n;u++){let n=t.get(e,u)-i*t.get(r,u);t.set(e,u,n)}}r--}return t}set(){throw new Error("set method is unimplemented")}get(){throw new Error("get method is unimplemented")}repeat(t={}){if("object"!==typeof t)throw new TypeError("options must be an object");const{rows:n=1,columns:e=1}=t;if(!Number.isInteger(n)||n<=0)throw new TypeError("rows must be a positive integer");if(!Number.isInteger(e)||e<=0)throw new TypeError("columns must be a positive integer");let r=new Y(this.rows*n,this.columns*e);for(let o=0;o<n;o++)for(let t=0;t<e;t++)r.setSubMatrix(this,this.rows*o,this.columns*t);return r}fill(t){for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)this.set(n,e,t);return this}neg(){return this.mulS(-1)}getRow(t){p(this,t);let n=[];for(let e=0;e<this.columns;e++)n.push(this.get(t,e));return n}getRowVector(t){return Y.rowVector(this.getRow(t))}setRow(t,n){p(this,t),n=v(this,n);for(let e=0;e<this.columns;e++)this.set(t,e,n[e]);return this}swapRows(t,n){p(this,t),p(this,n);for(let e=0;e<this.columns;e++){let r=this.get(t,e);this.set(t,e,this.get(n,e)),this.set(n,e,r)}return this}getColumn(t){m(this,t);let n=[];for(let e=0;e<this.rows;e++)n.push(this.get(e,t));return n}getColumnVector(t){return Y.columnVector(this.getColumn(t))}setColumn(t,n){m(this,t),n=y(this,n);for(let e=0;e<this.rows;e++)this.set(e,t,n[e]);return this}swapColumns(t,n){m(this,t),m(this,n);for(let e=0;e<this.rows;e++){let r=this.get(e,t);this.set(e,t,this.get(e,n)),this.set(e,n,r)}return this}addRowVector(t){t=v(this,t);for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)this.set(n,e,this.get(n,e)+t[e]);return this}subRowVector(t){t=v(this,t);for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)this.set(n,e,this.get(n,e)-t[e]);return this}mulRowVector(t){t=v(this,t);for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)this.set(n,e,this.get(n,e)*t[e]);return this}divRowVector(t){t=v(this,t);for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)this.set(n,e,this.get(n,e)/t[e]);return this}addColumnVector(t){t=y(this,t);for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)this.set(n,e,this.get(n,e)+t[n]);return this}subColumnVector(t){t=y(this,t);for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)this.set(n,e,this.get(n,e)-t[n]);return this}mulColumnVector(t){t=y(this,t);for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)this.set(n,e,this.get(n,e)*t[n]);return this}divColumnVector(t){t=y(this,t);for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)this.set(n,e,this.get(n,e)/t[n]);return this}mulRow(t,n){p(this,t);for(let e=0;e<this.columns;e++)this.set(t,e,this.get(t,e)*n);return this}mulColumn(t,n){m(this,t);for(let e=0;e<this.rows;e++)this.set(e,t,this.get(e,t)*n);return this}max(t){if(this.isEmpty())return NaN;switch(t){case"row":{const t=new Array(this.rows).fill(Number.NEGATIVE_INFINITY);for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)this.get(n,e)>t[n]&&(t[n]=this.get(n,e));return t}case"column":{const t=new Array(this.columns).fill(Number.NEGATIVE_INFINITY);for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)this.get(n,e)>t[e]&&(t[e]=this.get(n,e));return t}case void 0:{let t=this.get(0,0);for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)this.get(n,e)>t&&(t=this.get(n,e));return t}default:throw new Error("invalid option: "+t)}}maxIndex(){x(this);let t=this.get(0,0),n=[0,0];for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)this.get(e,r)>t&&(t=this.get(e,r),n[0]=e,n[1]=r);return n}min(t){if(this.isEmpty())return NaN;switch(t){case"row":{const t=new Array(this.rows).fill(Number.POSITIVE_INFINITY);for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)this.get(n,e)<t[n]&&(t[n]=this.get(n,e));return t}case"column":{const t=new Array(this.columns).fill(Number.POSITIVE_INFINITY);for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)this.get(n,e)<t[e]&&(t[e]=this.get(n,e));return t}case void 0:{let t=this.get(0,0);for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)this.get(n,e)<t&&(t=this.get(n,e));return t}default:throw new Error("invalid option: "+t)}}minIndex(){x(this);let t=this.get(0,0),n=[0,0];for(let e=0;e<this.rows;e++)for(let r=0;r<this.columns;r++)this.get(e,r)<t&&(t=this.get(e,r),n[0]=e,n[1]=r);return n}maxRow(t){if(p(this,t),this.isEmpty())return NaN;let n=this.get(t,0);for(let e=1;e<this.columns;e++)this.get(t,e)>n&&(n=this.get(t,e));return n}maxRowIndex(t){p(this,t),x(this);let n=this.get(t,0),e=[t,0];for(let r=1;r<this.columns;r++)this.get(t,r)>n&&(n=this.get(t,r),e[1]=r);return e}minRow(t){if(p(this,t),this.isEmpty())return NaN;let n=this.get(t,0);for(let e=1;e<this.columns;e++)this.get(t,e)<n&&(n=this.get(t,e));return n}minRowIndex(t){p(this,t),x(this);let n=this.get(t,0),e=[t,0];for(let r=1;r<this.columns;r++)this.get(t,r)<n&&(n=this.get(t,r),e[1]=r);return e}maxColumn(t){if(m(this,t),this.isEmpty())return NaN;let n=this.get(0,t);for(let e=1;e<this.rows;e++)this.get(e,t)>n&&(n=this.get(e,t));return n}maxColumnIndex(t){m(this,t),x(this);let n=this.get(0,t),e=[0,t];for(let r=1;r<this.rows;r++)this.get(r,t)>n&&(n=this.get(r,t),e[0]=r);return e}minColumn(t){if(m(this,t),this.isEmpty())return NaN;let n=this.get(0,t);for(let e=1;e<this.rows;e++)this.get(e,t)<n&&(n=this.get(e,t));return n}minColumnIndex(t){m(this,t),x(this);let n=this.get(0,t),e=[0,t];for(let r=1;r<this.rows;r++)this.get(r,t)<n&&(n=this.get(r,t),e[0]=r);return e}diag(){let t=Math.min(this.rows,this.columns),n=[];for(let e=0;e<t;e++)n.push(this.get(e,e));return n}norm(t="frobenius"){let n=0;if("max"===t)return this.max();if("frobenius"===t){for(let t=0;t<this.rows;t++)for(let e=0;e<this.columns;e++)n+=this.get(t,e)*this.get(t,e);return Math.sqrt(n)}throw new RangeError("unknown norm type: "+t)}cumulativeSum(){let t=0;for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)t+=this.get(n,e),this.set(n,e,t);return this}dot(t){V.isMatrix(t)&&(t=t.to1DArray());let n=this.to1DArray();if(n.length!==t.length)throw new RangeError("vectors do not have the same size");let e=0;for(let r=0;r<n.length;r++)e+=n[r]*t[r];return e}mmul(t){t=Y.checkMatrix(t);let n=this.rows,e=this.columns,r=t.columns,o=new Y(n,r),i=new Float64Array(e);for(let u=0;u<r;u++){for(let n=0;n<e;n++)i[n]=t.get(n,u);for(let t=0;t<n;t++){let n=0;for(let r=0;r<e;r++)n+=this.get(t,r)*i[r];o.set(t,u,n)}}return o}strassen2x2(t){t=Y.checkMatrix(t);let n=new Y(2,2);const e=this.get(0,0),r=t.get(0,0),o=this.get(0,1),i=t.get(0,1),u=this.get(1,0),a=t.get(1,0),s=this.get(1,1),c=t.get(1,1),f=(e+s)*(r+c),h=(u+s)*r,d=e*(i-c),l=s*(a-r),g=(e+o)*c,p=(u-e)*(r+i),m=(o-s)*(a+c),v=f+l-g+m,y=d+g,w=h+l,b=f-h+d+p;return n.set(0,0,v),n.set(0,1,y),n.set(1,0,w),n.set(1,1,b),n}strassen3x3(t){t=Y.checkMatrix(t);let n=new Y(3,3);const e=this.get(0,0),r=this.get(0,1),o=this.get(0,2),i=this.get(1,0),u=this.get(1,1),a=this.get(1,2),s=this.get(2,0),c=this.get(2,1),f=this.get(2,2),h=t.get(0,0),d=t.get(0,1),l=t.get(0,2),g=t.get(1,0),p=t.get(1,1),m=t.get(1,2),v=t.get(2,0),y=t.get(2,1),w=t.get(2,2),b=(e+r+o-i-u-c-f)*p,M=(e-i)*(-d+p),_=u*(-h+d+g-p-m-v+w),E=(-e+i+u)*(h-d+p),x=(i+u)*(-h+d),N=e*h,S=(-e+s+c)*(h-l+m),T=(-e+s)*(l-m),A=(s+c)*(-h+l),I=(e+r+o-u-a-s-c)*m,O=c*(-h+l+g-p-m-v+y),C=(-o+c+f)*(p+v-y),j=(o-f)*(p-y),R=o*v,k=(c+f)*(-v+y),P=(-o+u+a)*(m+v-w),D=(o-a)*(m-w),B=(u+a)*(-v+w),L=r*g,G=a*y,q=i*l,F=s*d,U=f*w,V=N+R+L,z=b+E+x+N+C+R+k,W=N+S+A+I+R+P+B,H=M+_+E+N+R+P+D,$=M+E+x+N+G,J=R+P+D+B+q,K=N+S+T+O+C+j+R,Q=C+j+R+k+F,Z=N+S+T+A+U;return n.set(0,0,V),n.set(0,1,z),n.set(0,2,W),n.set(1,0,H),n.set(1,1,$),n.set(1,2,J),n.set(2,0,K),n.set(2,1,Q),n.set(2,2,Z),n}mmulStrassen(t){t=Y.checkMatrix(t);let n=this.clone(),e=n.rows,r=n.columns,o=t.rows,i=t.columns;function u(t,n,e){let r=t.rows,o=t.columns;if(r===n&&o===e)return t;{let r=V.zeros(n,e);return r=r.setSubMatrix(t,0,0),r}}r!==o&&console.warn(`Multiplying ${e} x ${r} and ${o} x ${i} matrix: dimensions do not match.`);let a=Math.max(e,o),s=Math.max(r,i);function c(t,n,e,r){if(e<=512||r<=512)return t.mmul(n);e%2===1&&r%2===1?(t=u(t,e+1,r+1),n=u(n,e+1,r+1)):e%2===1?(t=u(t,e+1,r),n=u(n,e+1,r)):r%2===1&&(t=u(t,e,r+1),n=u(n,e,r+1));let o=parseInt(t.rows/2,10),i=parseInt(t.columns/2,10),a=t.subMatrix(0,o-1,0,i-1),s=n.subMatrix(0,o-1,0,i-1),f=t.subMatrix(0,o-1,i,t.columns-1),h=n.subMatrix(0,o-1,i,n.columns-1),d=t.subMatrix(o,t.rows-1,0,i-1),l=n.subMatrix(o,n.rows-1,0,i-1),g=t.subMatrix(o,t.rows-1,i,t.columns-1),p=n.subMatrix(o,n.rows-1,i,n.columns-1),m=c(V.add(a,g),V.add(s,p),o,i),v=c(V.add(d,g),s,o,i),y=c(a,V.sub(h,p),o,i),w=c(g,V.sub(l,s),o,i),b=c(V.add(a,f),p,o,i),M=c(V.sub(d,a),V.add(s,h),o,i),_=c(V.sub(f,g),V.add(l,p),o,i),E=V.add(m,w);E.sub(b),E.add(_);let x=V.add(y,b),N=V.add(v,w),S=V.sub(m,v);S.add(y),S.add(M);let T=V.zeros(2*E.rows,2*E.columns);return T=T.setSubMatrix(E,0,0),T=T.setSubMatrix(x,E.rows,0),T=T.setSubMatrix(N,0,E.columns),T=T.setSubMatrix(S,E.rows,E.columns),T.subMatrix(0,e-1,0,r-1)}return n=u(n,a,s),t=u(t,a,s),c(n,t,a,s)}scaleRows(t={}){if("object"!==typeof t)throw new TypeError("options must be an object");const{min:n=0,max:e=1}=t;if(!Number.isFinite(n))throw new TypeError("min must be a number");if(!Number.isFinite(e))throw new TypeError("max must be a number");if(n>=e)throw new RangeError("min must be smaller than max");let r=new Y(this.rows,this.columns);for(let o=0;o<this.rows;o++){const t=this.getRow(o);t.length>0&&a(t,{min:n,max:e,output:t}),r.setRow(o,t)}return r}scaleColumns(t={}){if("object"!==typeof t)throw new TypeError("options must be an object");const{min:n=0,max:e=1}=t;if(!Number.isFinite(n))throw new TypeError("min must be a number");if(!Number.isFinite(e))throw new TypeError("max must be a number");if(n>=e)throw new RangeError("min must be smaller than max");let r=new Y(this.rows,this.columns);for(let o=0;o<this.columns;o++){const t=this.getColumn(o);t.length&&a(t,{min:n,max:e,output:t}),r.setColumn(o,t)}return r}flipRows(){const t=Math.ceil(this.columns/2);for(let n=0;n<this.rows;n++)for(let e=0;e<t;e++){let t=this.get(n,e),r=this.get(n,this.columns-1-e);this.set(n,e,r),this.set(n,this.columns-1-e,t)}return this}flipColumns(){const t=Math.ceil(this.rows/2);for(let n=0;n<this.columns;n++)for(let e=0;e<t;e++){let t=this.get(e,n),r=this.get(this.rows-1-e,n);this.set(e,n,r),this.set(this.rows-1-e,n,t)}return this}kroneckerProduct(t){t=Y.checkMatrix(t);let n=this.rows,e=this.columns,r=t.rows,o=t.columns,i=new Y(n*r,e*o);for(let u=0;u<n;u++)for(let n=0;n<e;n++)for(let e=0;e<r;e++)for(let a=0;a<o;a++)i.set(r*u+e,o*n+a,this.get(u,n)*t.get(e,a));return i}kroneckerSum(t){if(t=Y.checkMatrix(t),!this.isSquare()||!t.isSquare())throw new Error("Kronecker Sum needs two Square Matrices");let n=this.rows,e=t.rows,r=this.kroneckerProduct(Y.eye(e,e)),o=Y.eye(n,n).kroneckerProduct(t);return r.add(o)}transpose(){let t=new Y(this.columns,this.rows);for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)t.set(e,n,this.get(n,e));return t}sortRows(t=z){for(let n=0;n<this.rows;n++)this.setRow(n,this.getRow(n).sort(t));return this}sortColumns(t=z){for(let n=0;n<this.columns;n++)this.setColumn(n,this.getColumn(n).sort(t));return this}subMatrix(t,n,e,r){M(this,t,n,e,r);let o=new Y(n-t+1,r-e+1);for(let i=t;i<=n;i++)for(let n=e;n<=r;n++)o.set(i-t,n-e,this.get(i,n));return o}subMatrixRow(t,n,e){if(void 0===n&&(n=0),void 0===e&&(e=this.columns-1),n>e||n<0||n>=this.columns||e<0||e>=this.columns)throw new RangeError("Argument out of range");let r=new Y(t.length,e-n+1);for(let o=0;o<t.length;o++)for(let i=n;i<=e;i++){if(t[o]<0||t[o]>=this.rows)throw new RangeError("Row index out of range: "+t[o]);r.set(o,i-n,this.get(t[o],i))}return r}subMatrixColumn(t,n,e){if(void 0===n&&(n=0),void 0===e&&(e=this.rows-1),n>e||n<0||n>=this.rows||e<0||e>=this.rows)throw new RangeError("Argument out of range");let r=new Y(e-n+1,t.length);for(let o=0;o<t.length;o++)for(let i=n;i<=e;i++){if(t[o]<0||t[o]>=this.columns)throw new RangeError("Column index out of range: "+t[o]);r.set(i-n,o,this.get(i,t[o]))}return r}setSubMatrix(t,n,e){if(t=Y.checkMatrix(t),t.isEmpty())return this;let r=n+t.rows-1,o=e+t.columns-1;M(this,n,r,e,o);for(let i=0;i<t.rows;i++)for(let r=0;r<t.columns;r++)this.set(n+i,e+r,t.get(i,r));return this}selection(t,n){w(this,t),b(this,n);let e=new Y(t.length,n.length);for(let r=0;r<t.length;r++){let o=t[r];for(let t=0;t<n.length;t++){let i=n[t];e.set(r,t,this.get(o,i))}}return e}trace(){let t=Math.min(this.rows,this.columns),n=0;for(let e=0;e<t;e++)n+=this.get(e,e);return n}clone(){let t=new Y(this.rows,this.columns);for(let n=0;n<this.rows;n++)for(let e=0;e<this.columns;e++)t.set(n,e,this.get(n,e));return t}sum(t){switch(t){case"row":return N(this);case"column":return S(this);case void 0:return T(this);default:throw new Error("invalid option: "+t)}}product(t){switch(t){case"row":return A(this);case"column":return I(this);case void 0:return O(this);default:throw new Error("invalid option: "+t)}}mean(t){const n=this.sum(t);switch(t){case"row":for(let t=0;t<this.rows;t++)n[t]/=this.columns;return n;case"column":for(let t=0;t<this.columns;t++)n[t]/=this.rows;return n;case void 0:return n/this.size;default:throw new Error("invalid option: "+t)}}variance(t,n={}){if("object"===typeof t&&(n=t,t=void 0),"object"!==typeof n)throw new TypeError("options must be an object");const{unbiased:e=!0,mean:r=this.mean(t)}=n;if("boolean"!==typeof e)throw new TypeError("unbiased must be a boolean");switch(t){case"row":if(!o(r))throw new TypeError("mean must be an array");return C(this,e,r);case"column":if(!o(r))throw new TypeError("mean must be an array");return j(this,e,r);case void 0:if("number"!==typeof r)throw new TypeError("mean must be a number");return R(this,e,r);default:throw new Error("invalid option: "+t)}}standardDeviation(t,n){"object"===typeof t&&(n=t,t=void 0);const e=this.variance(t,n);if(void 0===t)return Math.sqrt(e);for(let r=0;r<e.length;r++)e[r]=Math.sqrt(e[r]);return e}center(t,n={}){if("object"===typeof t&&(n=t,t=void 0),"object"!==typeof n)throw new TypeError("options must be an object");const{center:e=this.mean(t)}=n;switch(t){case"row":if(!o(e))throw new TypeError("center must be an array");return k(this,e),this;case"column":if(!o(e))throw new TypeError("center must be an array");return P(this,e),this;case void 0:if("number"!==typeof e)throw new TypeError("center must be a number");return D(this,e),this;default:throw new Error("invalid option: "+t)}}scale(t,n={}){if("object"===typeof t&&(n=t,t=void 0),"object"!==typeof n)throw new TypeError("options must be an object");let e=n.scale;switch(t){case"row":if(void 0===e)e=B(this);else if(!o(e))throw new TypeError("scale must be an array");return L(this,e),this;case"column":if(void 0===e)e=G(this);else if(!o(e))throw new TypeError("scale must be an array");return q(this,e),this;case void 0:if(void 0===e)e=F(this);else if("number"!==typeof e)throw new TypeError("scale must be a number");return U(this,e),this;default:throw new Error("invalid option: "+t)}}toString(t){return h(this,t)}}function z(t,n){return t-n}function W(t){return t.every(t=>"number"===typeof t)}V.prototype.klass="Matrix","undefined"!==typeof Symbol&&(V.prototype[Symbol.for("nodejs.util.inspect.custom")]=f),V.random=V.rand,V.randomInt=V.randInt,V.diagonal=V.diag,V.prototype.diagonal=V.prototype.diag,V.identity=V.eye,V.prototype.negate=V.prototype.neg,V.prototype.tensorProduct=V.prototype.kroneckerProduct;class Y extends V{constructor(t,n){if(super(),Y.isMatrix(t))return t.clone();if(Number.isInteger(t)&&t>=0){if(this.data=[],!(Number.isInteger(n)&&n>=0))throw new TypeError("nColumns must be a positive integer");for(let e=0;e<t;e++)this.data.push(new Float64Array(n))}else{if(!o(t))throw new TypeError("First argument must be a positive number or an array");{const e=t;if(t=e.length,n=t?e[0].length:0,"number"!==typeof n)throw new TypeError("Data must be a 2D array with at least one element");this.data=[];for(let r=0;r<t;r++){if(e[r].length!==n)throw new RangeError("Inconsistent array dimensions");if(!W(e[r]))throw new TypeError("Input data contains non-numeric values");this.data.push(Float64Array.from(e[r]))}}}this.rows=t,this.columns=n}set(t,n,e){return this.data[t][n]=e,this}get(t,n){return this.data[t][n]}removeRow(t){return p(this,t),this.data.splice(t,1),this.rows-=1,this}addRow(t,n){return void 0===n&&(n=t,t=this.rows),p(this,t,!0),n=Float64Array.from(v(this,n)),this.data.splice(t,0,n),this.rows+=1,this}removeColumn(t){m(this,t);for(let n=0;n<this.rows;n++){const e=new Float64Array(this.columns-1);for(let r=0;r<t;r++)e[r]=this.data[n][r];for(let r=t+1;r<this.columns;r++)e[r-1]=this.data[n][r];this.data[n]=e}return this.columns-=1,this}addColumn(t,n){"undefined"===typeof n&&(n=t,t=this.columns),m(this,t,!0),n=y(this,n);for(let e=0;e<this.rows;e++){const r=new Float64Array(this.columns+1);let o=0;for(;o<t;o++)r[o]=this.data[e][o];for(r[o++]=n[e];o<this.columns+1;o++)r[o]=this.data[e][o-1];this.data[e]=r}return this.columns+=1,this}}g(V,Y);class H extends V{constructor(t,n,e){super(),this.matrix=t,this.rows=n,this.columns=e}}class $ extends H{constructor(t,n){m(t,n),super(t,t.rows,1),this.column=n}set(t,n,e){return this.matrix.set(t,this.column,e),this}get(t){return this.matrix.get(t,this.column)}}class J extends H{constructor(t,n){b(t,n),super(t,t.rows,n.length),this.columnIndices=n}set(t,n,e){return this.matrix.set(t,this.columnIndices[n],e),this}get(t,n){return this.matrix.get(t,this.columnIndices[n])}}class K extends H{constructor(t){super(t,t.rows,t.columns)}set(t,n,e){return this.matrix.set(t,this.columns-n-1,e),this}get(t,n){return this.matrix.get(t,this.columns-n-1)}}class Q extends H{constructor(t){super(t,t.rows,t.columns)}set(t,n,e){return this.matrix.set(this.rows-t-1,n,e),this}get(t,n){return this.matrix.get(this.rows-t-1,n)}}class Z extends H{constructor(t,n){p(t,n),super(t,1,t.columns),this.row=n}set(t,n,e){return this.matrix.set(this.row,n,e),this}get(t,n){return this.matrix.get(this.row,n)}}class X extends H{constructor(t,n){w(t,n),super(t,n.length,t.columns),this.rowIndices=n}set(t,n,e){return this.matrix.set(this.rowIndices[t],n,e),this}get(t,n){return this.matrix.get(this.rowIndices[t],n)}}class tt extends H{constructor(t,n,e){w(t,n),b(t,e),super(t,n.length,e.length),this.rowIndices=n,this.columnIndices=e}set(t,n,e){return this.matrix.set(this.rowIndices[t],this.columnIndices[n],e),this}get(t,n){return this.matrix.get(this.rowIndices[t],this.columnIndices[n])}}class nt extends H{constructor(t,n,e,r,o){M(t,n,e,r,o),super(t,e-n+1,o-r+1),this.startRow=n,this.startColumn=r}set(t,n,e){return this.matrix.set(this.startRow+t,this.startColumn+n,e),this}get(t,n){return this.matrix.get(this.startRow+t,this.startColumn+n)}}class et extends H{constructor(t){super(t,t.columns,t.rows)}set(t,n,e){return this.matrix.set(n,t,e),this}get(t,n){return this.matrix.get(n,t)}}class rt extends V{constructor(t,n={}){const{rows:e=1}=n;if(t.length%e!==0)throw new Error("the data length is not divisible by the number of rows");super(),this.rows=e,this.columns=t.length/e,this.data=t}set(t,n,e){let r=this._calculateIndex(t,n);return this.data[r]=e,this}get(t,n){let e=this._calculateIndex(t,n);return this.data[e]}_calculateIndex(t,n){return t*this.columns+n}}class ot extends V{constructor(t){super(),this.data=t,this.rows=t.length,this.columns=t[0].length}set(t,n,e){return this.data[t][n]=e,this}get(t,n){return this.data[t][n]}}function it(t,n){if(o(t))return t[0]&&o(t[0])?new ot(t):new rt(t,n);throw new Error("the argument is not an array")}class ut{constructor(t){t=ot.checkMatrix(t);let n,e,r,o,i,u,a,s,c,f=t.clone(),h=f.rows,d=f.columns,l=new Float64Array(h),g=1;for(n=0;n<h;n++)l[n]=n;for(s=new Float64Array(h),e=0;e<d;e++){for(n=0;n<h;n++)s[n]=f.get(n,e);for(n=0;n<h;n++){for(c=Math.min(n,e),i=0,r=0;r<c;r++)i+=f.get(n,r)*s[r];s[n]-=i,f.set(n,e,s[n])}for(o=e,n=e+1;n<h;n++)Math.abs(s[n])>Math.abs(s[o])&&(o=n);if(o!==e){for(r=0;r<d;r++)u=f.get(o,r),f.set(o,r,f.get(e,r)),f.set(e,r,u);a=l[o],l[o]=l[e],l[e]=a,g=-g}if(e<h&&0!==f.get(e,e))for(n=e+1;n<h;n++)f.set(n,e,f.get(n,e)/f.get(e,e))}this.LU=f,this.pivotVector=l,this.pivotSign=g}isSingular(){let t=this.LU,n=t.columns;for(let e=0;e<n;e++)if(0===t.get(e,e))return!0;return!1}solve(t){t=Y.checkMatrix(t);let n=this.LU,e=n.rows;if(e!==t.rows)throw new Error("Invalid matrix dimensions");if(this.isSingular())throw new Error("LU matrix is singular");let r,o,i,u=t.columns,a=t.subMatrixRow(this.pivotVector,0,u-1),s=n.columns;for(i=0;i<s;i++)for(r=i+1;r<s;r++)for(o=0;o<u;o++)a.set(r,o,a.get(r,o)-a.get(i,o)*n.get(r,i));for(i=s-1;i>=0;i--){for(o=0;o<u;o++)a.set(i,o,a.get(i,o)/n.get(i,i));for(r=0;r<i;r++)for(o=0;o<u;o++)a.set(r,o,a.get(r,o)-a.get(i,o)*n.get(r,i))}return a}get determinant(){let t=this.LU;if(!t.isSquare())throw new Error("Matrix must be square");let n=this.pivotSign,e=t.columns;for(let r=0;r<e;r++)n*=t.get(r,r);return n}get lowerTriangularMatrix(){let t=this.LU,n=t.rows,e=t.columns,r=new Y(n,e);for(let o=0;o<n;o++)for(let n=0;n<e;n++)o>n?r.set(o,n,t.get(o,n)):o===n?r.set(o,n,1):r.set(o,n,0);return r}get upperTriangularMatrix(){let t=this.LU,n=t.rows,e=t.columns,r=new Y(n,e);for(let o=0;o<n;o++)for(let n=0;n<e;n++)o<=n?r.set(o,n,t.get(o,n)):r.set(o,n,0);return r}get pivotPermutationVector(){return Array.from(this.pivotVector)}}function at(t,n){let e=0;return Math.abs(t)>Math.abs(n)?(e=n/t,Math.abs(t)*Math.sqrt(1+e*e)):0!==n?(e=t/n,Math.abs(n)*Math.sqrt(1+e*e)):0}class st{constructor(t){t=ot.checkMatrix(t);let n,e,r,o,i=t.clone(),u=t.rows,a=t.columns,s=new Float64Array(a);for(r=0;r<a;r++){let t=0;for(n=r;n<u;n++)t=at(t,i.get(n,r));if(0!==t){for(i.get(r,r)<0&&(t=-t),n=r;n<u;n++)i.set(n,r,i.get(n,r)/t);for(i.set(r,r,i.get(r,r)+1),e=r+1;e<a;e++){for(o=0,n=r;n<u;n++)o+=i.get(n,r)*i.get(n,e);for(o=-o/i.get(r,r),n=r;n<u;n++)i.set(n,e,i.get(n,e)+o*i.get(n,r))}}s[r]=-t}this.QR=i,this.Rdiag=s}solve(t){t=Y.checkMatrix(t);let n=this.QR,e=n.rows;if(t.rows!==e)throw new Error("Matrix row dimensions must agree");if(!this.isFullRank())throw new Error("Matrix is rank deficient");let r,o,i,u,a=t.columns,s=t.clone(),c=n.columns;for(i=0;i<c;i++)for(o=0;o<a;o++){for(u=0,r=i;r<e;r++)u+=n.get(r,i)*s.get(r,o);for(u=-u/n.get(i,i),r=i;r<e;r++)s.set(r,o,s.get(r,o)+u*n.get(r,i))}for(i=c-1;i>=0;i--){for(o=0;o<a;o++)s.set(i,o,s.get(i,o)/this.Rdiag[i]);for(r=0;r<i;r++)for(o=0;o<a;o++)s.set(r,o,s.get(r,o)-s.get(i,o)*n.get(r,i))}return s.subMatrix(0,c-1,0,a-1)}isFullRank(){let t=this.QR.columns;for(let n=0;n<t;n++)if(0===this.Rdiag[n])return!1;return!0}get upperTriangularMatrix(){let t,n,e=this.QR,r=e.columns,o=new Y(r,r);for(t=0;t<r;t++)for(n=0;n<r;n++)t<n?o.set(t,n,e.get(t,n)):t===n?o.set(t,n,this.Rdiag[t]):o.set(t,n,0);return o}get orthogonalMatrix(){let t,n,e,r,o=this.QR,i=o.rows,u=o.columns,a=new Y(i,u);for(e=u-1;e>=0;e--){for(t=0;t<i;t++)a.set(t,e,0);for(a.set(e,e,1),n=e;n<u;n++)if(0!==o.get(e,e)){for(r=0,t=e;t<i;t++)r+=o.get(t,e)*a.get(t,n);for(r=-r/o.get(e,e),t=e;t<i;t++)a.set(t,n,a.get(t,n)+r*o.get(t,e))}}return a}}class ct{constructor(t,n={}){if(t=ot.checkMatrix(t),t.isEmpty())throw new Error("Matrix must be non-empty");let e=t.rows,r=t.columns;const{computeLeftSingularVectors:o=!0,computeRightSingularVectors:i=!0,autoTranspose:u=!1}=n;let a,s=Boolean(o),c=Boolean(i),f=!1;if(e<r)if(u){a=t.transpose(),e=a.rows,r=a.columns,f=!0;let n=s;s=c,c=n}else a=t.clone(),console.warn("Computing SVD on a matrix with more columns than rows. Consider enabling autoTranspose");else a=t.clone();let h=Math.min(e,r),d=Math.min(e+1,r),l=new Float64Array(d),g=new Y(e,h),p=new Y(r,r),m=new Float64Array(r),v=new Float64Array(e),y=new Float64Array(d);for(let S=0;S<d;S++)y[S]=S;let w=Math.min(e-1,r),b=Math.max(0,Math.min(r-2,e)),M=Math.max(w,b);for(let S=0;S<M;S++){if(S<w){l[S]=0;for(let t=S;t<e;t++)l[S]=at(l[S],a.get(t,S));if(0!==l[S]){a.get(S,S)<0&&(l[S]=-l[S]);for(let t=S;t<e;t++)a.set(t,S,a.get(t,S)/l[S]);a.set(S,S,a.get(S,S)+1)}l[S]=-l[S]}for(let t=S+1;t<r;t++){if(S<w&&0!==l[S]){let n=0;for(let r=S;r<e;r++)n+=a.get(r,S)*a.get(r,t);n=-n/a.get(S,S);for(let r=S;r<e;r++)a.set(r,t,a.get(r,t)+n*a.get(r,S))}m[t]=a.get(S,t)}if(s&&S<w)for(let t=S;t<e;t++)g.set(t,S,a.get(t,S));if(S<b){m[S]=0;for(let t=S+1;t<r;t++)m[S]=at(m[S],m[t]);if(0!==m[S]){m[S+1]<0&&(m[S]=0-m[S]);for(let t=S+1;t<r;t++)m[t]/=m[S];m[S+1]+=1}if(m[S]=-m[S],S+1<e&&0!==m[S]){for(let t=S+1;t<e;t++)v[t]=0;for(let t=S+1;t<e;t++)for(let n=S+1;n<r;n++)v[t]+=m[n]*a.get(t,n);for(let t=S+1;t<r;t++){let n=-m[t]/m[S+1];for(let r=S+1;r<e;r++)a.set(r,t,a.get(r,t)+n*v[r])}}if(c)for(let t=S+1;t<r;t++)p.set(t,S,m[t])}}let _=Math.min(r,e+1);if(w<r&&(l[w]=a.get(w,w)),e<_&&(l[_-1]=0),b+1<_&&(m[b]=a.get(b,_-1)),m[_-1]=0,s){for(let t=w;t<h;t++){for(let n=0;n<e;n++)g.set(n,t,0);g.set(t,t,1)}for(let t=w-1;t>=0;t--)if(0!==l[t]){for(let n=t+1;n<h;n++){let r=0;for(let o=t;o<e;o++)r+=g.get(o,t)*g.get(o,n);r=-r/g.get(t,t);for(let o=t;o<e;o++)g.set(o,n,g.get(o,n)+r*g.get(o,t))}for(let n=t;n<e;n++)g.set(n,t,-g.get(n,t));g.set(t,t,1+g.get(t,t));for(let n=0;n<t-1;n++)g.set(n,t,0)}else{for(let n=0;n<e;n++)g.set(n,t,0);g.set(t,t,1)}}if(c)for(let S=r-1;S>=0;S--){if(S<b&&0!==m[S])for(let t=S+1;t<r;t++){let n=0;for(let e=S+1;e<r;e++)n+=p.get(e,S)*p.get(e,t);n=-n/p.get(S+1,S);for(let e=S+1;e<r;e++)p.set(e,t,p.get(e,t)+n*p.get(e,S))}for(let t=0;t<r;t++)p.set(t,S,0);p.set(S,S,1)}let E=_-1,x=0,N=Number.EPSILON;while(_>0){let t,n;for(t=_-2;t>=-1;t--){if(-1===t)break;const n=Number.MIN_VALUE+N*Math.abs(l[t]+Math.abs(l[t+1]));if(Math.abs(m[t])<=n||Number.isNaN(m[t])){m[t]=0;break}}if(t===_-2)n=4;else{let e;for(e=_-1;e>=t;e--){if(e===t)break;let n=(e!==_?Math.abs(m[e]):0)+(e!==t+1?Math.abs(m[e-1]):0);if(Math.abs(l[e])<=N*n){l[e]=0;break}}e===t?n=3:e===_-1?n=1:(n=2,t=e)}switch(t++,n){case 1:{let n=m[_-2];m[_-2]=0;for(let e=_-2;e>=t;e--){let o=at(l[e],n),i=l[e]/o,u=n/o;if(l[e]=o,e!==t&&(n=-u*m[e-1],m[e-1]=i*m[e-1]),c)for(let t=0;t<r;t++)o=i*p.get(t,e)+u*p.get(t,_-1),p.set(t,_-1,-u*p.get(t,e)+i*p.get(t,_-1)),p.set(t,e,o)}break}case 2:{let n=m[t-1];m[t-1]=0;for(let r=t;r<_;r++){let o=at(l[r],n),i=l[r]/o,u=n/o;if(l[r]=o,n=-u*m[r],m[r]=i*m[r],s)for(let n=0;n<e;n++)o=i*g.get(n,r)+u*g.get(n,t-1),g.set(n,t-1,-u*g.get(n,r)+i*g.get(n,t-1)),g.set(n,r,o)}break}case 3:{const n=Math.max(Math.abs(l[_-1]),Math.abs(l[_-2]),Math.abs(m[_-2]),Math.abs(l[t]),Math.abs(m[t])),o=l[_-1]/n,i=l[_-2]/n,u=m[_-2]/n,a=l[t]/n,f=m[t]/n,h=((i+o)*(i-o)+u*u)/2,d=o*u*(o*u);let v=0;0===h&&0===d||(v=h<0?0-Math.sqrt(h*h+d):Math.sqrt(h*h+d),v=d/(h+v));let y=(a+o)*(a-o)+v,w=a*f;for(let b=t;b<_-1;b++){let n=at(y,w);0===n&&(n=Number.MIN_VALUE);let o=y/n,i=w/n;if(b!==t&&(m[b-1]=n),y=o*l[b]+i*m[b],m[b]=o*m[b]-i*l[b],w=i*l[b+1],l[b+1]=o*l[b+1],c)for(let t=0;t<r;t++)n=o*p.get(t,b)+i*p.get(t,b+1),p.set(t,b+1,-i*p.get(t,b)+o*p.get(t,b+1)),p.set(t,b,n);if(n=at(y,w),0===n&&(n=Number.MIN_VALUE),o=y/n,i=w/n,l[b]=n,y=o*m[b]+i*l[b+1],l[b+1]=-i*m[b]+o*l[b+1],w=i*m[b+1],m[b+1]=o*m[b+1],s&&b<e-1)for(let t=0;t<e;t++)n=o*g.get(t,b)+i*g.get(t,b+1),g.set(t,b+1,-i*g.get(t,b)+o*g.get(t,b+1)),g.set(t,b,n)}m[_-2]=y,x+=1;break}case 4:if(l[t]<=0&&(l[t]=l[t]<0?-l[t]:0,c))for(let n=0;n<=E;n++)p.set(n,t,-p.get(n,t));while(t<E){if(l[t]>=l[t+1])break;let n=l[t];if(l[t]=l[t+1],l[t+1]=n,c&&t<r-1)for(let e=0;e<r;e++)n=p.get(e,t+1),p.set(e,t+1,p.get(e,t)),p.set(e,t,n);if(s&&t<e-1)for(let r=0;r<e;r++)n=g.get(r,t+1),g.set(r,t+1,g.get(r,t)),g.set(r,t,n);t++}x=0,_--;break}}if(f){let t=p;p=g,g=t}this.m=e,this.n=r,this.s=l,this.U=g,this.V=p}solve(t){let n=t,e=this.threshold,r=this.s.length,o=Y.zeros(r,r);for(let h=0;h<r;h++)Math.abs(this.s[h])<=e?o.set(h,h,0):o.set(h,h,1/this.s[h]);let i=this.U,u=this.rightSingularVectors,a=u.mmul(o),s=u.rows,c=i.rows,f=Y.zeros(s,c);for(let h=0;h<s;h++)for(let t=0;t<c;t++){let n=0;for(let e=0;e<r;e++)n+=a.get(h,e)*i.get(t,e);f.set(h,t,n)}return f.mmul(n)}solveForDiagonal(t){return this.solve(Y.diag(t))}inverse(){let t=this.V,n=this.threshold,e=t.rows,r=t.columns,o=new Y(e,this.s.length);for(let c=0;c<e;c++)for(let e=0;e<r;e++)Math.abs(this.s[e])>n&&o.set(c,e,t.get(c,e)/this.s[e]);let i=this.U,u=i.rows,a=i.columns,s=new Y(e,u);for(let c=0;c<e;c++)for(let t=0;t<u;t++){let n=0;for(let e=0;e<a;e++)n+=o.get(c,e)*i.get(t,e);s.set(c,t,n)}return s}get condition(){return this.s[0]/this.s[Math.min(this.m,this.n)-1]}get norm2(){return this.s[0]}get rank(){let t=Math.max(this.m,this.n)*this.s[0]*Number.EPSILON,n=0,e=this.s;for(let r=0,o=e.length;r<o;r++)e[r]>t&&n++;return n}get diagonal(){return Array.from(this.s)}get threshold(){return Number.EPSILON/2*Math.max(this.m,this.n)*this.s[0]}get leftSingularVectors(){return this.U}get rightSingularVectors(){return this.V}get diagonalMatrix(){return Y.diag(this.s)}}function ft(t,n=!1){return t=ot.checkMatrix(t),n?new ct(t).inverse():ht(t,Y.eye(t.rows))}function ht(t,n,e=!1){return t=ot.checkMatrix(t),n=ot.checkMatrix(n),e?new ct(t).solve(n):t.isSquare()?new ut(t).solve(n):new st(t).solve(n)}function dt(t){if(t=Y.checkMatrix(t),t.isSquare()){if(0===t.columns)return 1;let n,e,r,o;if(2===t.columns)return n=t.get(0,0),e=t.get(0,1),r=t.get(1,0),o=t.get(1,1),n*o-e*r;if(3===t.columns){let o,i,u;return o=new tt(t,[1,2],[1,2]),i=new tt(t,[1,2],[0,2]),u=new tt(t,[1,2],[0,1]),n=t.get(0,0),e=t.get(0,1),r=t.get(0,2),n*dt(o)-e*dt(i)+r*dt(u)}return new ut(t).determinant}throw Error("determinant can only be calculated for a square matrix")}function lt(t,n){let e=[];for(let r=0;r<t;r++)r!==n&&e.push(r);return e}function gt(t,n,e,r=1e-9,o=1e-9){if(t>o)return new Array(n.rows+1).fill(0);{let t=n.addRow(e,[0]);for(let n=0;n<t.rows;n++)Math.abs(t.get(n,0))<r&&t.set(n,0,0);return t.to1DArray()}}function pt(t,n={}){const{thresholdValue:e=1e-9,thresholdError:r=1e-9}=n;t=Y.checkMatrix(t);let o=t.rows,i=new Y(o,o);for(let u=0;u<o;u++){let n=Y.columnVector(t.getRow(u)),a=t.subMatrixRow(lt(o,u)).transpose(),s=new ct(a),c=s.solve(n),f=Y.sub(n,a.mmul(c)).abs().max();i.setRow(u,gt(f,c,u,e,r))}return i}function mt(t,n=Number.EPSILON){if(t=Y.checkMatrix(t),t.isEmpty())return t.transpose();let e=new ct(t,{autoTranspose:!0}),r=e.leftSingularVectors,o=e.rightSingularVectors,i=e.diagonal;for(let u=0;u<i.length;u++)Math.abs(i[u])>n?i[u]=1/i[u]:i[u]=0;return o.mmul(Y.diag(i).mmul(r.transpose()))}function vt(t,n=t,e={}){t=new Y(t);let r=!1;if("object"!==typeof n||Y.isMatrix(n)||o(n)?n=new Y(n):(e=n,n=t,r=!0),t.rows!==n.rows)throw new TypeError("Both matrices must have the same number of rows");const{center:i=!0}=e;i&&(t=t.center("column"),r||(n=n.center("column")));const u=t.transpose().mmul(n);for(let o=0;o<u.rows;o++)for(let n=0;n<u.columns;n++)u.set(o,n,u.get(o,n)*(1/(t.rows-1)));return u}function yt(t,n=t,e={}){t=new Y(t);let r=!1;if("object"!==typeof n||Y.isMatrix(n)||o(n)?n=new Y(n):(e=n,n=t,r=!0),t.rows!==n.rows)throw new TypeError("Both matrices must have the same number of rows");const{center:i=!0,scale:u=!0}=e;i&&(t.center("column"),r||n.center("column")),u&&(t.scale("column"),r||n.scale("column"));const a=t.standardDeviation("column",{unbiased:!0}),s=r?a:n.standardDeviation("column",{unbiased:!0}),c=t.transpose().mmul(n);for(let o=0;o<c.rows;o++)for(let n=0;n<c.columns;n++)c.set(o,n,c.get(o,n)*(1/(a[o]*s[n]))*(1/(t.rows-1)));return c}class wt{constructor(t,n={}){const{assumeSymmetric:e=!1}=n;if(t=ot.checkMatrix(t),!t.isSquare())throw new Error("Matrix is not a square matrix");if(t.isEmpty())throw new Error("Matrix must be non-empty");let r,o,i=t.columns,u=new Y(i,i),a=new Float64Array(i),s=new Float64Array(i),c=t,f=!1;if(f=!!e||t.isSymmetric(),f){for(r=0;r<i;r++)for(o=0;o<i;o++)u.set(r,o,c.get(r,o));bt(i,s,a,u),Mt(i,s,a,u)}else{let t=new Y(i,i),n=new Float64Array(i);for(o=0;o<i;o++)for(r=0;r<i;r++)t.set(r,o,c.get(r,o));_t(i,t,n,u),Et(i,s,a,u,t)}this.n=i,this.e=s,this.d=a,this.V=u}get realEigenvalues(){return Array.from(this.d)}get imaginaryEigenvalues(){return Array.from(this.e)}get eigenvectorMatrix(){return this.V}get diagonalMatrix(){let t,n,e=this.n,r=this.e,o=this.d,i=new Y(e,e);for(t=0;t<e;t++){for(n=0;n<e;n++)i.set(t,n,0);i.set(t,t,o[t]),r[t]>0?i.set(t,t+1,r[t]):r[t]<0&&i.set(t,t-1,r[t])}return i}}function bt(t,n,e,r){let o,i,u,a,s,c,f,h;for(s=0;s<t;s++)e[s]=r.get(t-1,s);for(a=t-1;a>0;a--){for(h=0,u=0,c=0;c<a;c++)h+=Math.abs(e[c]);if(0===h)for(n[a]=e[a-1],s=0;s<a;s++)e[s]=r.get(a-1,s),r.set(a,s,0),r.set(s,a,0);else{for(c=0;c<a;c++)e[c]/=h,u+=e[c]*e[c];for(o=e[a-1],i=Math.sqrt(u),o>0&&(i=-i),n[a]=h*i,u-=o*i,e[a-1]=o-i,s=0;s<a;s++)n[s]=0;for(s=0;s<a;s++){for(o=e[s],r.set(s,a,o),i=n[s]+r.get(s,s)*o,c=s+1;c<=a-1;c++)i+=r.get(c,s)*e[c],n[c]+=r.get(c,s)*o;n[s]=i}for(o=0,s=0;s<a;s++)n[s]/=u,o+=n[s]*e[s];for(f=o/(u+u),s=0;s<a;s++)n[s]-=f*e[s];for(s=0;s<a;s++){for(o=e[s],i=n[s],c=s;c<=a-1;c++)r.set(c,s,r.get(c,s)-(o*n[c]+i*e[c]));e[s]=r.get(a-1,s),r.set(a,s,0)}}e[a]=u}for(a=0;a<t-1;a++){if(r.set(t-1,a,r.get(a,a)),r.set(a,a,1),u=e[a+1],0!==u){for(c=0;c<=a;c++)e[c]=r.get(c,a+1)/u;for(s=0;s<=a;s++){for(i=0,c=0;c<=a;c++)i+=r.get(c,a+1)*r.get(c,s);for(c=0;c<=a;c++)r.set(c,s,r.get(c,s)-i*e[c])}}for(c=0;c<=a;c++)r.set(c,a+1,0)}for(s=0;s<t;s++)e[s]=r.get(t-1,s),r.set(t-1,s,0);r.set(t-1,t-1,1),n[0]=0}function Mt(t,n,e,r){let o,i,u,a,s,c,f,h,d,l,g,p,m,v,y,w,b;for(u=1;u<t;u++)n[u-1]=n[u];n[t-1]=0;let M=0,_=0,E=Number.EPSILON;for(c=0;c<t;c++){_=Math.max(_,Math.abs(e[c])+Math.abs(n[c])),f=c;while(f<t){if(Math.abs(n[f])<=E*_)break;f++}if(f>c){b=0;do{for(b+=1,o=e[c],h=(e[c+1]-o)/(2*n[c]),d=at(h,1),h<0&&(d=-d),e[c]=n[c]/(h+d),e[c+1]=n[c]*(h+d),l=e[c+1],i=o-e[c],u=c+2;u<t;u++)e[u]-=i;for(M+=i,h=e[f],g=1,p=g,m=g,v=n[c+1],y=0,w=0,u=f-1;u>=c;u--)for(m=p,p=g,w=y,o=g*n[u],i=g*h,d=at(h,n[u]),n[u+1]=y*d,y=n[u]/d,g=h/d,h=g*e[u]-y*o,e[u+1]=i+y*(g*o+y*e[u]),s=0;s<t;s++)i=r.get(s,u+1),r.set(s,u+1,y*r.get(s,u)+g*i),r.set(s,u,g*r.get(s,u)-y*i);h=-y*w*m*v*n[c]/l,n[c]=y*h,e[c]=g*h}while(Math.abs(n[c])>E*_)}e[c]=e[c]+M,n[c]=0}for(u=0;u<t-1;u++){for(s=u,h=e[u],a=u+1;a<t;a++)e[a]<h&&(s=a,h=e[a]);if(s!==u)for(e[s]=e[u],e[u]=h,a=0;a<t;a++)h=r.get(a,u),r.set(a,u,r.get(a,s)),r.set(a,s,h)}}function _t(t,n,e,r){let o,i,u,a,s,c,f,h=0,d=t-1;for(c=h+1;c<=d-1;c++){for(f=0,a=c;a<=d;a++)f+=Math.abs(n.get(a,c-1));if(0!==f){for(u=0,a=d;a>=c;a--)e[a]=n.get(a,c-1)/f,u+=e[a]*e[a];for(i=Math.sqrt(u),e[c]>0&&(i=-i),u-=e[c]*i,e[c]=e[c]-i,s=c;s<t;s++){for(o=0,a=d;a>=c;a--)o+=e[a]*n.get(a,s);for(o/=u,a=c;a<=d;a++)n.set(a,s,n.get(a,s)-o*e[a])}for(a=0;a<=d;a++){for(o=0,s=d;s>=c;s--)o+=e[s]*n.get(a,s);for(o/=u,s=c;s<=d;s++)n.set(a,s,n.get(a,s)-o*e[s])}e[c]=f*e[c],n.set(c,c-1,f*i)}}for(a=0;a<t;a++)for(s=0;s<t;s++)r.set(a,s,a===s?1:0);for(c=d-1;c>=h+1;c--)if(0!==n.get(c,c-1)){for(a=c+1;a<=d;a++)e[a]=n.get(a,c-1);for(s=c;s<=d;s++){for(i=0,a=c;a<=d;a++)i+=e[a]*r.get(a,s);for(i=i/e[c]/n.get(c,c-1),a=c;a<=d;a++)r.set(a,s,r.get(a,s)+i*e[a])}}}function Et(t,n,e,r,o){let i,u,a,s,c,f,h,d,l,g,p,m,v,y,w,b=t-1,M=0,_=t-1,E=Number.EPSILON,x=0,N=0,S=0,T=0,A=0,I=0,O=0,C=0;for(i=0;i<t;i++)for((i<M||i>_)&&(e[i]=o.get(i,i),n[i]=0),u=Math.max(i-1,0);u<t;u++)N+=Math.abs(o.get(i,u));while(b>=M){s=b;while(s>M){if(I=Math.abs(o.get(s-1,s-1))+Math.abs(o.get(s,s)),0===I&&(I=N),Math.abs(o.get(s,s-1))<E*I)break;s--}if(s===b)o.set(b,b,o.get(b,b)+x),e[b]=o.get(b,b),n[b]=0,b--,C=0;else if(s===b-1){if(h=o.get(b,b-1)*o.get(b-1,b),S=(o.get(b-1,b-1)-o.get(b,b))/2,T=S*S+h,O=Math.sqrt(Math.abs(T)),o.set(b,b,o.get(b,b)+x),o.set(b-1,b-1,o.get(b-1,b-1)+x),d=o.get(b,b),T>=0){for(O=S>=0?S+O:S-O,e[b-1]=d+O,e[b]=e[b-1],0!==O&&(e[b]=d-h/O),n[b-1]=0,n[b]=0,d=o.get(b,b-1),I=Math.abs(d)+Math.abs(O),S=d/I,T=O/I,A=Math.sqrt(S*S+T*T),S/=A,T/=A,u=b-1;u<t;u++)O=o.get(b-1,u),o.set(b-1,u,T*O+S*o.get(b,u)),o.set(b,u,T*o.get(b,u)-S*O);for(i=0;i<=b;i++)O=o.get(i,b-1),o.set(i,b-1,T*O+S*o.get(i,b)),o.set(i,b,T*o.get(i,b)-S*O);for(i=M;i<=_;i++)O=r.get(i,b-1),r.set(i,b-1,T*O+S*r.get(i,b)),r.set(i,b,T*r.get(i,b)-S*O)}else e[b-1]=d+S,e[b]=d+S,n[b-1]=O,n[b]=-O;b-=2,C=0}else{if(d=o.get(b,b),l=0,h=0,s<b&&(l=o.get(b-1,b-1),h=o.get(b,b-1)*o.get(b-1,b)),10===C){for(x+=d,i=M;i<=b;i++)o.set(i,i,o.get(i,i)-d);I=Math.abs(o.get(b,b-1))+Math.abs(o.get(b-1,b-2)),d=l=.75*I,h=-.4375*I*I}if(30===C&&(I=(l-d)/2,I=I*I+h,I>0)){for(I=Math.sqrt(I),l<d&&(I=-I),I=d-h/((l-d)/2+I),i=M;i<=b;i++)o.set(i,i,o.get(i,i)-I);x+=I,d=l=h=.964}C+=1,c=b-2;while(c>=s){if(O=o.get(c,c),A=d-O,I=l-O,S=(A*I-h)/o.get(c+1,c)+o.get(c,c+1),T=o.get(c+1,c+1)-O-A-I,A=o.get(c+2,c+1),I=Math.abs(S)+Math.abs(T)+Math.abs(A),S/=I,T/=I,A/=I,c===s)break;if(Math.abs(o.get(c,c-1))*(Math.abs(T)+Math.abs(A))<E*(Math.abs(S)*(Math.abs(o.get(c-1,c-1))+Math.abs(O)+Math.abs(o.get(c+1,c+1)))))break;c--}for(i=c+2;i<=b;i++)o.set(i,i-2,0),i>c+2&&o.set(i,i-3,0);for(a=c;a<=b-1;a++){if(y=a!==b-1,a!==c&&(S=o.get(a,a-1),T=o.get(a+1,a-1),A=y?o.get(a+2,a-1):0,d=Math.abs(S)+Math.abs(T)+Math.abs(A),0!==d&&(S/=d,T/=d,A/=d)),0===d)break;if(I=Math.sqrt(S*S+T*T+A*A),S<0&&(I=-I),0!==I){for(a!==c?o.set(a,a-1,-I*d):s!==c&&o.set(a,a-1,-o.get(a,a-1)),S+=I,d=S/I,l=T/I,O=A/I,T/=S,A/=S,u=a;u<t;u++)S=o.get(a,u)+T*o.get(a+1,u),y&&(S+=A*o.get(a+2,u),o.set(a+2,u,o.get(a+2,u)-S*O)),o.set(a,u,o.get(a,u)-S*d),o.set(a+1,u,o.get(a+1,u)-S*l);for(i=0;i<=Math.min(b,a+3);i++)S=d*o.get(i,a)+l*o.get(i,a+1),y&&(S+=O*o.get(i,a+2),o.set(i,a+2,o.get(i,a+2)-S*A)),o.set(i,a,o.get(i,a)-S),o.set(i,a+1,o.get(i,a+1)-S*T);for(i=M;i<=_;i++)S=d*r.get(i,a)+l*r.get(i,a+1),y&&(S+=O*r.get(i,a+2),r.set(i,a+2,r.get(i,a+2)-S*A)),r.set(i,a,r.get(i,a)-S),r.set(i,a+1,r.get(i,a+1)-S*T)}}}}if(0!==N){for(b=t-1;b>=0;b--)if(S=e[b],T=n[b],0===T)for(s=b,o.set(b,b,1),i=b-1;i>=0;i--){for(h=o.get(i,i)-S,A=0,u=s;u<=b;u++)A+=o.get(i,u)*o.get(u,b);if(n[i]<0)O=h,I=A;else if(s=i,0===n[i]?o.set(i,b,0!==h?-A/h:-A/(E*N)):(d=o.get(i,i+1),l=o.get(i+1,i),T=(e[i]-S)*(e[i]-S)+n[i]*n[i],f=(d*I-O*A)/T,o.set(i,b,f),o.set(i+1,b,Math.abs(d)>Math.abs(O)?(-A-h*f)/d:(-I-l*f)/O)),f=Math.abs(o.get(i,b)),E*f*f>1)for(u=i;u<=b;u++)o.set(u,b,o.get(u,b)/f)}else if(T<0)for(s=b-1,Math.abs(o.get(b,b-1))>Math.abs(o.get(b-1,b))?(o.set(b-1,b-1,T/o.get(b,b-1)),o.set(b-1,b,-(o.get(b,b)-S)/o.get(b,b-1))):(w=xt(0,-o.get(b-1,b),o.get(b-1,b-1)-S,T),o.set(b-1,b-1,w[0]),o.set(b-1,b,w[1])),o.set(b,b-1,0),o.set(b,b,1),i=b-2;i>=0;i--){for(g=0,p=0,u=s;u<=b;u++)g+=o.get(i,u)*o.get(u,b-1),p+=o.get(i,u)*o.get(u,b);if(h=o.get(i,i)-S,n[i]<0)O=h,A=g,I=p;else if(s=i,0===n[i]?(w=xt(-g,-p,h,T),o.set(i,b-1,w[0]),o.set(i,b,w[1])):(d=o.get(i,i+1),l=o.get(i+1,i),m=(e[i]-S)*(e[i]-S)+n[i]*n[i]-T*T,v=2*(e[i]-S)*T,0===m&&0===v&&(m=E*N*(Math.abs(h)+Math.abs(T)+Math.abs(d)+Math.abs(l)+Math.abs(O))),w=xt(d*A-O*g+T*p,d*I-O*p-T*g,m,v),o.set(i,b-1,w[0]),o.set(i,b,w[1]),Math.abs(d)>Math.abs(O)+Math.abs(T)?(o.set(i+1,b-1,(-g-h*o.get(i,b-1)+T*o.get(i,b))/d),o.set(i+1,b,(-p-h*o.get(i,b)-T*o.get(i,b-1))/d)):(w=xt(-A-l*o.get(i,b-1),-I-l*o.get(i,b),O,T),o.set(i+1,b-1,w[0]),o.set(i+1,b,w[1]))),f=Math.max(Math.abs(o.get(i,b-1)),Math.abs(o.get(i,b))),E*f*f>1)for(u=i;u<=b;u++)o.set(u,b-1,o.get(u,b-1)/f),o.set(u,b,o.get(u,b)/f)}for(i=0;i<t;i++)if(i<M||i>_)for(u=i;u<t;u++)r.set(i,u,o.get(i,u));for(u=t-1;u>=M;u--)for(i=M;i<=_;i++){for(O=0,a=M;a<=Math.min(u,_);a++)O+=r.get(i,a)*o.get(a,u);r.set(i,u,O)}}}function xt(t,n,e,r){let o,i;return Math.abs(e)>Math.abs(r)?(o=r/e,i=e+o*r,[(t+o*n)/i,(n-o*t)/i]):(o=e/r,i=r+o*e,[(o*t+n)/i,(o*n-t)/i])}class Nt{constructor(t){if(t=ot.checkMatrix(t),!t.isSymmetric())throw new Error("Matrix is not symmetric");let n,e,r,o=t,i=o.rows,u=new Y(i,i),a=!0;for(e=0;e<i;e++){let t=0;for(r=0;r<e;r++){let i=0;for(n=0;n<r;n++)i+=u.get(r,n)*u.get(e,n);i=(o.get(e,r)-i)/u.get(r,r),u.set(e,r,i),t+=i*i}for(t=o.get(e,e)-t,a&=t>0,u.set(e,e,Math.sqrt(Math.max(t,0))),r=e+1;r<i;r++)u.set(e,r,0)}this.L=u,this.positiveDefinite=Boolean(a)}isPositiveDefinite(){return this.positiveDefinite}solve(t){t=ot.checkMatrix(t);let n=this.L,e=n.rows;if(t.rows!==e)throw new Error("Matrix dimensions do not match");if(!1===this.isPositiveDefinite())throw new Error("Matrix is not positive definite");let r,o,i,u=t.columns,a=t.clone();for(i=0;i<e;i++)for(o=0;o<u;o++){for(r=0;r<i;r++)a.set(i,o,a.get(i,o)-a.get(r,o)*n.get(i,r));a.set(i,o,a.get(i,o)/n.get(i,i))}for(i=e-1;i>=0;i--)for(o=0;o<u;o++){for(r=i+1;r<e;r++)a.set(i,o,a.get(i,o)-a.get(r,o)*n.get(r,i));a.set(i,o,a.get(i,o)/n.get(i,i))}return a}get lowerTriangularMatrix(){return this.L}}class St{constructor(t,n={}){t=ot.checkMatrix(t);let{Y:e}=n;const{scaleScores:r=!1,maxIterations:i=1e3,terminationCriteria:u=1e-10}=n;let a;if(e){if(e=o(e)&&"number"===typeof e[0]?Y.columnVector(e):ot.checkMatrix(e),e.rows!==t.rows)throw new Error("Y should have the same number of rows as X");a=e.getColumnVector(0)}else a=t.getColumnVector(0);let s,c,f,h,d=1;for(let o=0;o<i&&d>u;o++)f=t.transpose().mmul(a).div(a.transpose().mmul(a).get(0,0)),f=f.div(f.norm()),s=t.mmul(f).div(f.transpose().mmul(f).get(0,0)),o>0&&(d=s.clone().sub(h).pow(2).sum()),h=s.clone(),e?(c=e.transpose().mmul(s).div(s.transpose().mmul(s).get(0,0)),c=c.div(c.norm()),a=e.mmul(c).div(c.transpose().mmul(c).get(0,0))):a=s;if(e){let n=t.transpose().mmul(s).div(s.transpose().mmul(s).get(0,0));n=n.div(n.norm());let r=t.clone().sub(s.clone().mmul(n.transpose())),o=a.transpose().mmul(s).div(s.transpose().mmul(s).get(0,0)),i=e.clone().sub(s.clone().mulS(o.get(0,0)).mmul(c.transpose()));this.t=s,this.p=n.transpose(),this.w=f.transpose(),this.q=c,this.u=a,this.s=s.transpose().mmul(s),this.xResidual=r,this.yResidual=i,this.betas=o}else this.w=f.transpose(),this.s=s.transpose().mmul(s).sqrt(),this.t=r?s.clone().div(this.s.get(0,0)):s,this.xResidual=t.sub(s.mmul(f.transpose()))}}},b7c3:function(t,n){function e(t,n){var e=n[0],r=n[1],o=e*e+r*r;return o>0&&(o=1/Math.sqrt(o),t[0]=n[0]*o,t[1]=n[1]*o),t}t.exports=e},b8f0:function(t,n,e){t.exports=e("706c")},ba33:function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.circularDependencyToException=n.listMetadataForTarget=n.listRegisteredBindingsForServiceIdentifier=n.getServiceIdentifierAsString=n.getFunctionName=void 0;var r=e("30e3");function o(t){if("function"===typeof t){var n=t;return n.name}if("symbol"===typeof t)return t.toString();n=t;return n}function i(t,n,e){var r="",o=e(t,n);return 0!==o.length&&(r="\nRegistered bindings:",o.forEach((function(t){var n="Object";null!==t.implementationType&&(n=f(t.implementationType)),r=r+"\n "+n,t.constraint.metaData&&(r=r+" - "+t.constraint.metaData)}))),r}function u(t,n){return null!==t.parentRequest&&(t.parentRequest.serviceIdentifier===n||u(t.parentRequest,n))}function a(t){function n(t,e){void 0===e&&(e=[]);var r=o(t.serviceIdentifier);return e.push(r),null!==t.parentRequest?n(t.parentRequest,e):e}var e=n(t);return e.reverse().join(" --\x3e ")}function s(t){t.childRequests.forEach((function(t){if(u(t,t.serviceIdentifier)){var n=a(t);throw new Error(r.CIRCULAR_DEPENDENCY+" "+n)}s(t)}))}function c(t,n){if(n.isTagged()||n.isNamed()){var e="",r=n.getNamedTag(),o=n.getCustomTags();return null!==r&&(e+=r.toString()+"\n"),null!==o&&o.forEach((function(t){e+=t.toString()+"\n"}))," "+t+"\n "+t+" - "+e}return" "+t}function f(t){if(t.name)return t.name;var n=t.toString(),e=n.match(/^function\s*([^\s(]+)/);return e?e[1]:"Anonymous function: "+n}n.getServiceIdentifierAsString=o,n.listRegisteredBindingsForServiceIdentifier=i,n.circularDependencyToException=s,n.listMetadataForTarget=c,n.getFunctionName=f},bbaa:function(t,n){function e(t,n,e){return t[0]=n[0]*e[0],t[1]=n[1]*e[1],t}t.exports=e},be2a:function(t,n,e){t.exports=e("c848")},c278:function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.MetadataReader=void 0;var r=e("c5f4"),o=function(){function t(){}return t.prototype.getConstructorMetadata=function(t){var n=Reflect.getMetadata(r.PARAM_TYPES,t),e=Reflect.getMetadata(r.TAGGED,t);return{compilerGeneratedMetadata:n,userGeneratedMetadata:e||{}}},t.prototype.getPropertiesMetadata=function(t){var n=Reflect.getMetadata(r.TAGGED_PROP,t)||[];return n},t}();n.MetadataReader=o},c435:function(t,n){function e(t,n,e,r){var o=n[0],i=n[1];return t[0]=o+r*(e[0]-o),t[1]=i+r*(e[1]-i),t}t.exports=e},c5f4:function(t,n,e){"use strict";function r(){return[n.INJECT_TAG,n.MULTI_INJECT_TAG,n.NAME_TAG,n.UNMANAGED_TAG,n.NAMED_TAG,n.OPTIONAL_TAG]}Object.defineProperty(n,"__esModule",{value:!0}),n.NON_CUSTOM_TAG_KEYS=n.POST_CONSTRUCT=n.DESIGN_PARAM_TYPES=n.PARAM_TYPES=n.TAGGED_PROP=n.TAGGED=n.MULTI_INJECT_TAG=n.INJECT_TAG=n.OPTIONAL_TAG=n.UNMANAGED_TAG=n.NAME_TAG=n.NAMED_TAG=void 0,n.NAMED_TAG="named",n.NAME_TAG="name",n.UNMANAGED_TAG="unmanaged",n.OPTIONAL_TAG="optional",n.INJECT_TAG="inject",n.MULTI_INJECT_TAG="multi_inject",n.TAGGED="inversify:tagged",n.TAGGED_PROP="inversify:tagged_props",n.PARAM_TYPES="inversify:paramtypes",n.DESIGN_PARAM_TYPES="design:paramtypes",n.POST_CONSTRUCT="post_construct",n.NON_CUSTOM_TAG_KEYS=r()},c622:function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.Request=void 0;var r=e("77d3"),o=function(){function t(t,n,e,o,i){this.id=r.id(),this.serviceIdentifier=t,this.parentContext=n,this.parentRequest=e,this.target=i,this.childRequests=[],this.bindings=Array.isArray(o)?o:[o],this.requestScope=null===e?new Map:null}return t.prototype.addChildRequest=function(n,e,r){var o=new t(n,this.parentContext,this,e,r);return this.childRequests.push(o),o},t}();n.Request=o},c848:function(t,n){function e(t){var n=t[0],e=t[1];return Math.sqrt(n*n+e*e)}t.exports=e},c8c0:function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.Plan=void 0;var r=function(){function t(t,n){this.parentContext=t,this.rootRequest=n}return t}();n.Plan=r},c94d:function(t,n,e){"use strict";e.d(n,"b",(function(){return r})),e.d(n,"a",(function(){return o})),e.d(n,"c",(function(){return i}));var r=1e-6,o="undefined"!==typeof Float32Array?Float32Array:Array,i=Math.random;Math.PI;Math.hypot||(Math.hypot=function(){var t=0,n=arguments.length;while(n--)t+=arguments[n]*arguments[n];return Math.sqrt(t)})},cb29:function(t,n,e){var r=e("23e7"),o=e("81d5"),i=e("44d2");r({target:"Array",proto:!0},{fill:o}),i("fill")},ccbc:function(t,n){function e(t,n){return t[0]=Math.ceil(n[0]),t[1]=Math.ceil(n[1]),t}t.exports=e},cf13:function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.QueryableString=void 0;var r=function(){function t(t){this.str=t}return t.prototype.startsWith=function(t){return 0===this.str.indexOf(t)},t.prototype.endsWith=function(t){var n="",e=t.split("").reverse().join("");return n=this.str.split("").reverse().join(""),this.startsWith.call({str:n},e)},t.prototype.contains=function(t){return-1!==this.str.indexOf(t)},t.prototype.equals=function(t){return this.str===t},t.prototype.value=function(){return this.str},t}();n.QueryableString=r},cf81:function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.BindingWhenSyntax=void 0;var r=e("e34e"),o=e("451f"),i=function(){function t(t){this._binding=t}return t.prototype.when=function(t){return this._binding.constraint=t,new r.BindingOnSyntax(this._binding)},t.prototype.whenTargetNamed=function(t){return this._binding.constraint=o.namedConstraint(t),new r.BindingOnSyntax(this._binding)},t.prototype.whenTargetIsDefault=function(){return this._binding.constraint=function(t){var n=null!==t.target&&!t.target.isNamed()&&!t.target.isTagged();return n},new r.BindingOnSyntax(this._binding)},t.prototype.whenTargetTagged=function(t,n){return this._binding.constraint=o.taggedConstraint(t)(n),new r.BindingOnSyntax(this._binding)},t.prototype.whenInjectedInto=function(t){return this._binding.constraint=function(n){return o.typeConstraint(t)(n.parentRequest)},new r.BindingOnSyntax(this._binding)},t.prototype.whenParentNamed=function(t){return this._binding.constraint=function(n){return o.namedConstraint(t)(n.parentRequest)},new r.BindingOnSyntax(this._binding)},t.prototype.whenParentTagged=function(t,n){return this._binding.constraint=function(e){return o.taggedConstraint(t)(n)(e.parentRequest)},new r.BindingOnSyntax(this._binding)},t.prototype.whenAnyAncestorIs=function(t){return this._binding.constraint=function(n){return o.traverseAncerstors(n,o.typeConstraint(t))},new r.BindingOnSyntax(this._binding)},t.prototype.whenNoAncestorIs=function(t){return this._binding.constraint=function(n){return!o.traverseAncerstors(n,o.typeConstraint(t))},new r.BindingOnSyntax(this._binding)},t.prototype.whenAnyAncestorNamed=function(t){return this._binding.constraint=function(n){return o.traverseAncerstors(n,o.namedConstraint(t))},new r.BindingOnSyntax(this._binding)},t.prototype.whenNoAncestorNamed=function(t){return this._binding.constraint=function(n){return!o.traverseAncerstors(n,o.namedConstraint(t))},new r.BindingOnSyntax(this._binding)},t.prototype.whenAnyAncestorTagged=function(t,n){return this._binding.constraint=function(e){return o.traverseAncerstors(e,o.taggedConstraint(t)(n))},new r.BindingOnSyntax(this._binding)},t.prototype.whenNoAncestorTagged=function(t,n){return this._binding.constraint=function(e){return!o.traverseAncerstors(e,o.taggedConstraint(t)(n))},new r.BindingOnSyntax(this._binding)},t.prototype.whenAnyAncestorMatches=function(t){return this._binding.constraint=function(n){return o.traverseAncerstors(n,t)},new r.BindingOnSyntax(this._binding)},t.prototype.whenNoAncestorMatches=function(t){return this._binding.constraint=function(n){return!o.traverseAncerstors(n,t)},new r.BindingOnSyntax(this._binding)},t}();n.BindingWhenSyntax=i},d204:function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.tagged=void 0;var r=e("1979"),o=e("66d7");function i(t,n){return function(e,i,u){var a=new r.Metadata(t,n);"number"===typeof u?o.tagParameter(e,i,u,a):o.tagProperty(e,i,a)}}n.tagged=i},d865:function(t,n){function e(t,n){var e=new Float32Array(2);return e[0]=t,e[1]=n,e}t.exports=e},df4b:function(t,n){function e(t,n,e){var r=n[0],o=n[1];return t[0]=e[0]*r+e[2]*o,t[1]=e[1]*r+e[3]*o,t}t.exports=e},e079:function(t,n){function e(t,n,e){return t[0]=n,t[1]=e,t}t.exports=e},e1c6:function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.multiBindToService=n.getServiceIdentifierAsString=n.typeConstraint=n.namedConstraint=n.taggedConstraint=n.traverseAncerstors=n.decorate=n.id=n.MetadataReader=n.postConstruct=n.targetName=n.multiInject=n.unmanaged=n.optional=n.LazyServiceIdentifer=n.inject=n.named=n.tagged=n.injectable=n.ContainerModule=n.AsyncContainerModule=n.TargetTypeEnum=n.BindingTypeEnum=n.BindingScopeEnum=n.Container=n.METADATA_KEY=void 0;var r=e("c5f4");n.METADATA_KEY=r;var o=e("f457");Object.defineProperty(n,"Container",{enumerable:!0,get:function(){return o.Container}});var i=e("155f");Object.defineProperty(n,"BindingScopeEnum",{enumerable:!0,get:function(){return i.BindingScopeEnum}}),Object.defineProperty(n,"BindingTypeEnum",{enumerable:!0,get:function(){return i.BindingTypeEnum}}),Object.defineProperty(n,"TargetTypeEnum",{enumerable:!0,get:function(){return i.TargetTypeEnum}});var u=e("771c");Object.defineProperty(n,"AsyncContainerModule",{enumerable:!0,get:function(){return u.AsyncContainerModule}}),Object.defineProperty(n,"ContainerModule",{enumerable:!0,get:function(){return u.ContainerModule}});var a=e("719e");Object.defineProperty(n,"injectable",{enumerable:!0,get:function(){return a.injectable}});var s=e("d204");Object.defineProperty(n,"tagged",{enumerable:!0,get:function(){return s.tagged}});var c=e("6730a");Object.defineProperty(n,"named",{enumerable:!0,get:function(){return c.named}});var f=e("624f");Object.defineProperty(n,"inject",{enumerable:!0,get:function(){return f.inject}}),Object.defineProperty(n,"LazyServiceIdentifer",{enumerable:!0,get:function(){return f.LazyServiceIdentifer}});var h=e("8d8c");Object.defineProperty(n,"optional",{enumerable:!0,get:function(){return h.optional}});var d=e("9f62");Object.defineProperty(n,"unmanaged",{enumerable:!0,get:function(){return d.unmanaged}});var l=e("8c88");Object.defineProperty(n,"multiInject",{enumerable:!0,get:function(){return l.multiInject}});var g=e("a1a5");Object.defineProperty(n,"targetName",{enumerable:!0,get:function(){return g.targetName}});var p=e("4a4f");Object.defineProperty(n,"postConstruct",{enumerable:!0,get:function(){return p.postConstruct}});var m=e("c278");Object.defineProperty(n,"MetadataReader",{enumerable:!0,get:function(){return m.MetadataReader}});var v=e("77d3");Object.defineProperty(n,"id",{enumerable:!0,get:function(){return v.id}});var y=e("66d7");Object.defineProperty(n,"decorate",{enumerable:!0,get:function(){return y.decorate}});var w=e("451f");Object.defineProperty(n,"traverseAncerstors",{enumerable:!0,get:function(){return w.traverseAncerstors}}),Object.defineProperty(n,"taggedConstraint",{enumerable:!0,get:function(){return w.taggedConstraint}}),Object.defineProperty(n,"namedConstraint",{enumerable:!0,get:function(){return w.namedConstraint}}),Object.defineProperty(n,"typeConstraint",{enumerable:!0,get:function(){return w.typeConstraint}});var b=e("ba33");Object.defineProperty(n,"getServiceIdentifierAsString",{enumerable:!0,get:function(){return b.getServiceIdentifierAsString}});var M=e("efc5");Object.defineProperty(n,"multiBindToService",{enumerable:!0,get:function(){return M.multiBindToService}})},e213:function(t,n,e){t.exports={EPSILON:e("3443"),create:e("123d"),clone:e("4bcc"),fromValues:e("d865"),copy:e("3cc3"),set:e("e079"),equals:e("1788"),exactEquals:e("4516"),add:e("fac2"),subtract:e("706c"),sub:e("b8f0"),multiply:e("bbaa"),mul:e("305e"),divide:e("06ed"),div:e("4af6"),inverse:e("40ec"),min:e("74e5"),max:e("f80d"),rotate:e("b23e"),floor:e("0aae"),ceil:e("ccbc"),round:e("4a46"),scale:e("332e"),scaleAndAdd:e("fa7f"),distance:e("339b"),dist:e("086a"),squaredDistance:e("0b70"),sqrDist:e("a654"),length:e("c848"),len:e("be2a"),squaredLength:e("4869"),sqrLen:e("62bf"),negate:e("033c"),normalize:e("b7c3"),dot:e("6bc6"),cross:e("3761"),lerp:e("c435"),random:e("2f6e"),transformMat2:e("df4b"),transformMat2d:e("6e53"),transformMat3:e("4f06"),transformMat4:e("0043"),forEach:e("14a2"),limit:e("e6b9")}},e34e:function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.BindingOnSyntax=void 0;var r=e("cf81"),o=function(){function t(t){this._binding=t}return t.prototype.onActivation=function(t){return this._binding.onActivation=t,new r.BindingWhenSyntax(this._binding)},t}();n.BindingOnSyntax=o},e6b9:function(t,n){function e(t,n,e){var r=n[0]*n[0]+n[1]*n[1];if(r>e*e){var o=Math.sqrt(r);t[0]=n[0]/o*e,t[1]=n[1]/o*e}else t[0]=n[0],t[1]=n[1];return t}t.exports=e},e801:function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.BindingInSyntax=void 0;var r=e("155f"),o=e("2cac"),i=function(){function t(t){this._binding=t}return t.prototype.inRequestScope=function(){return this._binding.scope=r.BindingScopeEnum.Request,new o.BindingWhenOnSyntax(this._binding)},t.prototype.inSingletonScope=function(){return this._binding.scope=r.BindingScopeEnum.Singleton,new o.BindingWhenOnSyntax(this._binding)},t.prototype.inTransientScope=function(){return this._binding.scope=r.BindingScopeEnum.Transient,new o.BindingWhenOnSyntax(this._binding)},t}();n.BindingInSyntax=i},ea1d:function(t,n,e){"use strict";var r=function(t,n,e){t.prototype=n.prototype=e,e.constructor=t};function o(t,n){var e=Object.create(t.prototype);for(var r in n)e[r]=n[r];return e}function i(){}var u=.7,a=1/u,s="\\s*([+-]?\\d+)\\s*",c="\\s*([+-]?\\d*\\.?\\d+(?:[eE][+-]?\\d+)?)\\s*",f="\\s*([+-]?\\d*\\.?\\d+(?:[eE][+-]?\\d+)?)%\\s*",h=/^#([0-9a-f]{3,8})$/,d=new RegExp("^rgb\\("+[s,s,s]+"\\)$"),l=new RegExp("^rgb\\("+[f,f,f]+"\\)$"),g=new RegExp("^rgba\\("+[s,s,s,c]+"\\)$"),p=new RegExp("^rgba\\("+[f,f,f,c]+"\\)$"),m=new RegExp("^hsl\\("+[c,f,f]+"\\)$"),v=new RegExp("^hsla\\("+[c,f,f,c]+"\\)$"),y={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function w(){return this.rgb().formatHex()}function b(){return j(this).formatHsl()}function M(){return this.rgb().formatRgb()}function _(t){var n,e;return t=(t+"").trim().toLowerCase(),(n=h.exec(t))?(e=n[1].length,n=parseInt(n[1],16),6===e?E(n):3===e?new T(n>>8&15|n>>4&240,n>>4&15|240&n,(15&n)<<4|15&n,1):8===e?x(n>>24&255,n>>16&255,n>>8&255,(255&n)/255):4===e?x(n>>12&15|n>>8&240,n>>8&15|n>>4&240,n>>4&15|240&n,((15&n)<<4|15&n)/255):null):(n=d.exec(t))?new T(n[1],n[2],n[3],1):(n=l.exec(t))?new T(255*n[1]/100,255*n[2]/100,255*n[3]/100,1):(n=g.exec(t))?x(n[1],n[2],n[3],n[4]):(n=p.exec(t))?x(255*n[1]/100,255*n[2]/100,255*n[3]/100,n[4]):(n=m.exec(t))?C(n[1],n[2]/100,n[3]/100,1):(n=v.exec(t))?C(n[1],n[2]/100,n[3]/100,n[4]):y.hasOwnProperty(t)?E(y[t]):"transparent"===t?new T(NaN,NaN,NaN,0):null}function E(t){return new T(t>>16&255,t>>8&255,255&t,1)}function x(t,n,e,r){return r<=0&&(t=n=e=NaN),new T(t,n,e,r)}function N(t){return t instanceof i||(t=_(t)),t?(t=t.rgb(),new T(t.r,t.g,t.b,t.opacity)):new T}function S(t,n,e,r){return 1===arguments.length?N(t):new T(t,n,e,null==r?1:r)}function T(t,n,e,r){this.r=+t,this.g=+n,this.b=+e,this.opacity=+r}function A(){return"#"+O(this.r)+O(this.g)+O(this.b)}function I(){var t=this.opacity;return t=isNaN(t)?1:Math.max(0,Math.min(1,t)),(1===t?"rgb(":"rgba(")+Math.max(0,Math.min(255,Math.round(this.r)||0))+", "+Math.max(0,Math.min(255,Math.round(this.g)||0))+", "+Math.max(0,Math.min(255,Math.round(this.b)||0))+(1===t?")":", "+t+")")}function O(t){return t=Math.max(0,Math.min(255,Math.round(t)||0)),(t<16?"0":"")+t.toString(16)}function C(t,n,e,r){return r<=0?t=n=e=NaN:e<=0||e>=1?t=n=NaN:n<=0&&(t=NaN),new k(t,n,e,r)}function j(t){if(t instanceof k)return new k(t.h,t.s,t.l,t.opacity);if(t instanceof i||(t=_(t)),!t)return new k;if(t instanceof k)return t;t=t.rgb();var n=t.r/255,e=t.g/255,r=t.b/255,o=Math.min(n,e,r),u=Math.max(n,e,r),a=NaN,s=u-o,c=(u+o)/2;return s?(a=n===u?(e-r)/s+6*(e<r):e===u?(r-n)/s+2:(n-e)/s+4,s/=c<.5?u+o:2-u-o,a*=60):s=c>0&&c<1?0:a,new k(a,s,c,t.opacity)}function R(t,n,e,r){return 1===arguments.length?j(t):new k(t,n,e,null==r?1:r)}function k(t,n,e,r){this.h=+t,this.s=+n,this.l=+e,this.opacity=+r}function P(t,n,e){return 255*(t<60?n+(e-n)*t/60:t<180?e:t<240?n+(e-n)*(240-t)/60:n)}function D(t,n,e,r,o){var i=t*t,u=i*t;return((1-3*t+3*i-u)*n+(4-6*i+3*u)*e+(1+3*t+3*i-3*u)*r+u*o)/6}r(i,_,{copy:function(t){return Object.assign(new this.constructor,this,t)},displayable:function(){return this.rgb().displayable()},hex:w,formatHex:w,formatHsl:b,formatRgb:M,toString:M}),r(T,S,o(i,{brighter:function(t){return t=null==t?a:Math.pow(a,t),new T(this.r*t,this.g*t,this.b*t,this.opacity)},darker:function(t){return t=null==t?u:Math.pow(u,t),new T(this.r*t,this.g*t,this.b*t,this.opacity)},rgb:function(){return this},displayable:function(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:A,formatHex:A,formatRgb:I,toString:I})),r(k,R,o(i,{brighter:function(t){return t=null==t?a:Math.pow(a,t),new k(this.h,this.s,this.l*t,this.opacity)},darker:function(t){return t=null==t?u:Math.pow(u,t),new k(this.h,this.s,this.l*t,this.opacity)},rgb:function(){var t=this.h%360+360*(this.h<0),n=isNaN(t)||isNaN(this.s)?0:this.s,e=this.l,r=e+(e<.5?e:1-e)*n,o=2*e-r;return new T(P(t>=240?t-240:t+120,o,r),P(t,o,r),P(t<120?t+240:t-120,o,r),this.opacity)},displayable:function(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl:function(){var t=this.opacity;return t=isNaN(t)?1:Math.max(0,Math.min(1,t)),(1===t?"hsl(":"hsla(")+(this.h||0)+", "+100*(this.s||0)+"%, "+100*(this.l||0)+"%"+(1===t?")":", "+t+")")}}));var B=function(t){var n=t.length-1;return function(e){var r=e<=0?e=0:e>=1?(e=1,n-1):Math.floor(e*n),o=t[r],i=t[r+1],u=r>0?t[r-1]:2*o-i,a=r<n-1?t[r+2]:2*i-o;return D((e-r/n)*n,u,o,i,a)}},L=function(t){var n=t.length;return function(e){var r=Math.floor(((e%=1)<0?++e:e)*n),o=t[(r+n-1)%n],i=t[r%n],u=t[(r+1)%n],a=t[(r+2)%n];return D((e-r/n)*n,o,i,u,a)}},G=function(t){return function(){return t}};function q(t,n){return function(e){return t+e*n}}function F(t,n,e){return t=Math.pow(t,e),n=Math.pow(n,e)-t,e=1/e,function(r){return Math.pow(t+r*n,e)}}function U(t){return 1===(t=+t)?V:function(n,e){return e-n?F(n,e,t):G(isNaN(n)?e:n)}}function V(t,n){var e=n-t;return e?q(t,e):G(isNaN(t)?n:t)}var z=function t(n){var e=U(n);function r(t,n){var r=e((t=S(t)).r,(n=S(n)).r),o=e(t.g,n.g),i=e(t.b,n.b),u=V(t.opacity,n.opacity);return function(n){return t.r=r(n),t.g=o(n),t.b=i(n),t.opacity=u(n),t+""}}return r.gamma=t,r}(1);function W(t){return function(n){var e,r,o=n.length,i=new Array(o),u=new Array(o),a=new Array(o);for(e=0;e<o;++e)r=S(n[e]),i[e]=r.r||0,u[e]=r.g||0,a[e]=r.b||0;return i=t(i),u=t(u),a=t(a),r.opacity=1,function(t){return r.r=i(t),r.g=u(t),r.b=a(t),r+""}}}W(B),W(L);var Y=e("6730"),H=function(t,n){var e=new Date;return t=+t,n=+n,function(r){return e.setTime(t*(1-r)+n*r),e}},$=function(t,n){return t=+t,n=+n,function(e){return t*(1-e)+n*e}},J=function(t,n){var e,r={},o={};for(e in null!==t&&"object"===typeof t||(t={}),null!==n&&"object"===typeof n||(n={}),n)e in t?r[e]=et(t[e],n[e]):o[e]=n[e];return function(t){for(e in r)o[e]=r[e](t);return o}},K=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,Q=new RegExp(K.source,"g");function Z(t){return function(){return t}}function X(t){return function(n){return t(n)+""}}var tt=function(t,n){var e,r,o,i=K.lastIndex=Q.lastIndex=0,u=-1,a=[],s=[];t+="",n+="";while((e=K.exec(t))&&(r=Q.exec(n)))(o=r.index)>i&&(o=n.slice(i,o),a[u]?a[u]+=o:a[++u]=o),(e=e[0])===(r=r[0])?a[u]?a[u]+=r:a[++u]=r:(a[++u]=null,s.push({i:u,x:$(e,r)})),i=Q.lastIndex;return i<n.length&&(o=n.slice(i),a[u]?a[u]+=o:a[++u]=o),a.length<2?s[0]?X(s[0].x):Z(n):(n=s.length,function(t){for(var e,r=0;r<n;++r)a[(e=s[r]).i]=e.x(t);return a.join("")})},nt=e("51de"),et=n["a"]=function(t,n){var e,r=typeof n;return null==n||"boolean"===r?G(n):("number"===r?$:"string"===r?(e=_(n))?(n=e,z):tt:n instanceof _?z:n instanceof Date?H:Object(nt["b"])(n)?nt["a"]:Array.isArray(n)?Y["b"]:"function"!==typeof n.valueOf&&"function"!==typeof n.toString||isNaN(n)?J:$)(t,n)}},efc5:function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.multiBindToService=void 0;var r=function(t){return function(n){return function(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];return e.forEach((function(e){return t.bind(e).toService(n)}))}}};n.multiBindToService=r},f457:function(t,n,e){"use strict";var r=this&&this.__awaiter||function(t,n,e,r){function o(t){return t instanceof e?t:new e((function(n){n(t)}))}return new(e||(e=Promise))((function(e,i){function u(t){try{s(r.next(t))}catch(n){i(n)}}function a(t){try{s(r["throw"](t))}catch(n){i(n)}}function s(t){t.done?e(t.value):o(t.value).then(u,a)}s((r=r.apply(t,n||[])).next())}))},o=this&&this.__generator||function(t,n){var e,r,o,i,u={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(t){return function(n){return s([t,n])}}function s(i){if(e)throw new TypeError("Generator is already executing.");while(u)try{if(e=1,r&&(o=2&i[0]?r["return"]:i[0]?r["throw"]||((o=r["return"])&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return u.label++,{value:i[1],done:!1};case 5:u.label++,r=i[1],i=[0];continue;case 7:i=u.ops.pop(),u.trys.pop();continue;default:if(o=u.trys,!(o=o.length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){u=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){u.label=i[1];break}if(6===i[0]&&u.label<o[1]){u.label=o[1],o=i;break}if(o&&u.label<o[2]){u.label=o[2],u.ops.push(i);break}o[2]&&u.ops.pop(),u.trys.pop();continue}i=n.call(t,u)}catch(a){i=[6,a],r=0}finally{e=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}},i=this&&this.__spreadArray||function(t,n){for(var e=0,r=n.length,o=t.length;e<r;e++,o++)t[o]=n[e];return t};Object.defineProperty(n,"__esModule",{value:!0}),n.Container=void 0;var u=e("2e0b"),a=e("30e3"),s=e("155f"),c=e("c5f4"),f=e("c278"),h=e("87b3"),d=e("68f1"),l=e("8336"),g=e("77d3"),p=e("ba33"),m=e("1e94"),v=e("0300"),y=function(){function t(t){this._appliedMiddleware=[];var n=t||{};if("object"!==typeof n)throw new Error(""+a.CONTAINER_OPTIONS_MUST_BE_AN_OBJECT);if(void 0===n.defaultScope)n.defaultScope=s.BindingScopeEnum.Transient;else if(n.defaultScope!==s.BindingScopeEnum.Singleton&&n.defaultScope!==s.BindingScopeEnum.Transient&&n.defaultScope!==s.BindingScopeEnum.Request)throw new Error(""+a.CONTAINER_OPTIONS_INVALID_DEFAULT_SCOPE);if(void 0===n.autoBindInjectable)n.autoBindInjectable=!1;else if("boolean"!==typeof n.autoBindInjectable)throw new Error(""+a.CONTAINER_OPTIONS_INVALID_AUTO_BIND_INJECTABLE);if(void 0===n.skipBaseClassChecks)n.skipBaseClassChecks=!1;else if("boolean"!==typeof n.skipBaseClassChecks)throw new Error(""+a.CONTAINER_OPTIONS_INVALID_SKIP_BASE_CHECK);this.options={autoBindInjectable:n.autoBindInjectable,defaultScope:n.defaultScope,skipBaseClassChecks:n.skipBaseClassChecks},this.id=g.id(),this._bindingDictionary=new v.Lookup,this._snapshots=[],this._middleware=null,this.parent=null,this._metadataReader=new f.MetadataReader}return t.merge=function(n,e){for(var r=[],o=2;o<arguments.length;o++)r[o-2]=arguments[o];var u=new t,a=i([n,e],r).map((function(t){return h.getBindingDictionary(t)})),s=h.getBindingDictionary(u);function c(t,n){t.traverse((function(t,e){e.forEach((function(t){n.add(t.serviceIdentifier,t.clone())}))}))}return a.forEach((function(t){c(t,s)})),u},t.prototype.load=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];for(var e=this._getContainerModuleHelpersFactory(),r=0,o=t;r<o.length;r++){var i=o[r],u=e(i.id);i.registry(u.bindFunction,u.unbindFunction,u.isboundFunction,u.rebindFunction)}},t.prototype.loadAsync=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return r(this,void 0,void 0,(function(){var n,e,r,i,u;return o(this,(function(o){switch(o.label){case 0:n=this._getContainerModuleHelpersFactory(),e=0,r=t,o.label=1;case 1:return e<r.length?(i=r[e],u=n(i.id),[4,i.registry(u.bindFunction,u.unbindFunction,u.isboundFunction,u.rebindFunction)]):[3,4];case 2:o.sent(),o.label=3;case 3:return e++,[3,1];case 4:return[2]}}))}))},t.prototype.unload=function(){for(var t=this,n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];var r=function(t){return function(n){return n.moduleId===t}};n.forEach((function(n){var e=r(n.id);t._bindingDictionary.removeByCondition(e)}))},t.prototype.bind=function(t){var n=this.options.defaultScope||s.BindingScopeEnum.Transient,e=new u.Binding(t,n);return this._bindingDictionary.add(t,e),new l.BindingToSyntax(e)},t.prototype.rebind=function(t){return this.unbind(t),this.bind(t)},t.prototype.unbind=function(t){try{this._bindingDictionary.remove(t)}catch(n){throw new Error(a.CANNOT_UNBIND+" "+p.getServiceIdentifierAsString(t))}},t.prototype.unbindAll=function(){this._bindingDictionary=new v.Lookup},t.prototype.isBound=function(t){var n=this._bindingDictionary.hasKey(t);return!n&&this.parent&&(n=this.parent.isBound(t)),n},t.prototype.isBoundNamed=function(t,n){return this.isBoundTagged(t,c.NAMED_TAG,n)},t.prototype.isBoundTagged=function(t,n,e){var r=!1;if(this._bindingDictionary.hasKey(t)){var o=this._bindingDictionary.get(t),i=h.createMockRequest(this,t,n,e);r=o.some((function(t){return t.constraint(i)}))}return!r&&this.parent&&(r=this.parent.isBoundTagged(t,n,e)),r},t.prototype.snapshot=function(){this._snapshots.push(m.ContainerSnapshot.of(this._bindingDictionary.clone(),this._middleware))},t.prototype.restore=function(){var t=this._snapshots.pop();if(void 0===t)throw new Error(a.NO_MORE_SNAPSHOTS_AVAILABLE);this._bindingDictionary=t.bindings,this._middleware=t.middleware},t.prototype.createChild=function(n){var e=new t(n||this.options);return e.parent=this,e},t.prototype.applyMiddleware=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];this._appliedMiddleware=this._appliedMiddleware.concat(t);var e=this._middleware?this._middleware:this._planAndResolve();this._middleware=t.reduce((function(t,n){return n(t)}),e)},t.prototype.applyCustomMetadataReader=function(t){this._metadataReader=t},t.prototype.get=function(t){return this._get(!1,!1,s.TargetTypeEnum.Variable,t)},t.prototype.getTagged=function(t,n,e){return this._get(!1,!1,s.TargetTypeEnum.Variable,t,n,e)},t.prototype.getNamed=function(t,n){return this.getTagged(t,c.NAMED_TAG,n)},t.prototype.getAll=function(t){return this._get(!0,!0,s.TargetTypeEnum.Variable,t)},t.prototype.getAllTagged=function(t,n,e){return this._get(!1,!0,s.TargetTypeEnum.Variable,t,n,e)},t.prototype.getAllNamed=function(t,n){return this.getAllTagged(t,c.NAMED_TAG,n)},t.prototype.resolve=function(t){var n=this.createChild();return n.bind(t).toSelf(),this._appliedMiddleware.forEach((function(t){n.applyMiddleware(t)})),n.get(t)},t.prototype._getContainerModuleHelpersFactory=function(){var t=this,n=function(t,n){t._binding.moduleId=n},e=function(e){return function(r){var o=t.bind.bind(t),i=o(r);return n(i,e),i}},r=function(n){return function(n){var e=t.unbind.bind(t);e(n)}},o=function(n){return function(n){var e=t.isBound.bind(t);return e(n)}},i=function(e){return function(r){var o=t.rebind.bind(t),i=o(r);return n(i,e),i}};return function(t){return{bindFunction:e(t),isboundFunction:o(t),rebindFunction:i(t),unbindFunction:r(t)}}},t.prototype._get=function(t,n,e,r,o,i){var u=null,s={avoidConstraints:t,contextInterceptor:function(t){return t},isMultiInject:n,key:o,serviceIdentifier:r,targetType:e,value:i};if(this._middleware){if(u=this._middleware(s),void 0===u||null===u)throw new Error(a.INVALID_MIDDLEWARE_RETURN)}else u=this._planAndResolve()(s);return u},t.prototype._planAndResolve=function(){var t=this;return function(n){var e=h.plan(t._metadataReader,t,n.isMultiInject,n.targetType,n.serviceIdentifier,n.key,n.value,n.avoidConstraints);e=n.contextInterceptor(e);var r=d.resolve(e);return r}},t}();n.Container=y},f80d:function(t,n){function e(t,n,e){return t[0]=Math.max(n[0],e[0]),t[1]=Math.max(n[1],e[1]),t}t.exports=e},fa7f:function(t,n){function e(t,n,e,r){return t[0]=n[0]+e[0]*r,t[1]=n[1]+e[1]*r,t}t.exports=e},fac2:function(t,n){function e(t,n,e){return t[0]=n[0]+e[0],t[1]=n[1]+e[1],t}t.exports=e},fb77:function(t,n,e){"use strict";function r(t){return+t}function o(t){return t*t}function i(t){return t*(2-t)}function u(t){return((t*=2)<=1?t*t:--t*(2-t)+1)/2}function a(t){return t*t*t}function s(t){return--t*t*t+1}function c(t){return((t*=2)<=1?t*t*t:(t-=2)*t*t+2)/2}e.r(n),e.d(n,"easeLinear",(function(){return r})),e.d(n,"easeQuad",(function(){return u})),e.d(n,"easeQuadIn",(function(){return o})),e.d(n,"easeQuadOut",(function(){return i})),e.d(n,"easeQuadInOut",(function(){return u})),e.d(n,"easeCubic",(function(){return c})),e.d(n,"easeCubicIn",(function(){return a})),e.d(n,"easeCubicOut",(function(){return s})),e.d(n,"easeCubicInOut",(function(){return c})),e.d(n,"easePoly",(function(){return l})),e.d(n,"easePolyIn",(function(){return h})),e.d(n,"easePolyOut",(function(){return d})),e.d(n,"easePolyInOut",(function(){return l})),e.d(n,"easeSin",(function(){return y})),e.d(n,"easeSinIn",(function(){return m})),e.d(n,"easeSinOut",(function(){return v})),e.d(n,"easeSinInOut",(function(){return y})),e.d(n,"easeExp",(function(){return _})),e.d(n,"easeExpIn",(function(){return b})),e.d(n,"easeExpOut",(function(){return M})),e.d(n,"easeExpInOut",(function(){return _})),e.d(n,"easeCircle",(function(){return N})),e.d(n,"easeCircleIn",(function(){return E})),e.d(n,"easeCircleOut",(function(){return x})),e.d(n,"easeCircleInOut",(function(){return N})),e.d(n,"easeBounce",(function(){return B})),e.d(n,"easeBounceIn",(function(){return D})),e.d(n,"easeBounceOut",(function(){return B})),e.d(n,"easeBounceInOut",(function(){return L})),e.d(n,"easeBack",(function(){return U})),e.d(n,"easeBackIn",(function(){return q})),e.d(n,"easeBackOut",(function(){return F})),e.d(n,"easeBackInOut",(function(){return U})),e.d(n,"easeElastic",(function(){return H})),e.d(n,"easeElasticIn",(function(){return Y})),e.d(n,"easeElasticOut",(function(){return H})),e.d(n,"easeElasticInOut",(function(){return $}));var f=3,h=function t(n){function e(t){return Math.pow(t,n)}return n=+n,e.exponent=t,e}(f),d=function t(n){function e(t){return 1-Math.pow(1-t,n)}return n=+n,e.exponent=t,e}(f),l=function t(n){function e(t){return((t*=2)<=1?Math.pow(t,n):2-Math.pow(2-t,n))/2}return n=+n,e.exponent=t,e}(f),g=Math.PI,p=g/2;function m(t){return 1===+t?1:1-Math.cos(t*p)}function v(t){return Math.sin(t*p)}function y(t){return(1-Math.cos(g*t))/2}function w(t){return 1.0009775171065494*(Math.pow(2,-10*t)-.0009765625)}function b(t){return w(1-+t)}function M(t){return 1-w(t)}function _(t){return((t*=2)<=1?w(1-t):2-w(t-1))/2}function E(t){return 1-Math.sqrt(1-t*t)}function x(t){return Math.sqrt(1- --t*t)}function N(t){return((t*=2)<=1?1-Math.sqrt(1-t*t):Math.sqrt(1-(t-=2)*t)+1)/2}var S=4/11,T=6/11,A=8/11,I=3/4,O=9/11,C=10/11,j=15/16,R=21/22,k=63/64,P=1/S/S;function D(t){return 1-B(1-t)}function B(t){return(t=+t)<S?P*t*t:t<A?P*(t-=T)*t+I:t<C?P*(t-=O)*t+j:P*(t-=R)*t+k}function L(t){return((t*=2)<=1?1-B(1-t):B(t-1)+1)/2}var G=1.70158,q=function t(n){function e(t){return(t=+t)*t*(n*(t-1)+t)}return n=+n,e.overshoot=t,e}(G),F=function t(n){function e(t){return--t*t*((t+1)*n+t)+1}return n=+n,e.overshoot=t,e}(G),U=function t(n){function e(t){return((t*=2)<1?t*t*((n+1)*t-n):(t-=2)*t*((n+1)*t+n)+2)/2}return n=+n,e.overshoot=t,e}(G),V=2*Math.PI,z=1,W=.3,Y=function t(n,e){var r=Math.asin(1/(n=Math.max(1,n)))*(e/=V);function o(t){return n*w(- --t)*Math.sin((r-t)/e)}return o.amplitude=function(n){return t(n,e*V)},o.period=function(e){return t(n,e)},o}(z,W),H=function t(n,e){var r=Math.asin(1/(n=Math.max(1,n)))*(e/=V);function o(t){return 1-n*w(t=+t)*Math.sin((t+r)/e)}return o.amplitude=function(n){return t(n,e*V)},o.period=function(e){return t(n,e)},o}(z,W),$=function t(n,e){var r=Math.asin(1/(n=Math.max(1,n)))*(e/=V);function o(t){return((t=2*t-1)<0?n*w(-t)*Math.sin((r-t)/e):2-n*w(t)*Math.sin((r+t)/e))/2}return o.amplitude=function(n){return t(n,e*V)},o.period=function(e){return t(n,e)},o}(z,W)}}]);