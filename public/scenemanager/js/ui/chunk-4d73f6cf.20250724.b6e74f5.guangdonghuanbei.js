(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4d73f6cf"],{"00a3":function(t,e,n){"use strict";n.d(e,"b",(function(){return c})),n.d(e,"a",(function(){return d}));var i=n("11f7"),o=n("30db"),r=n("0f70"),s=n("e757"),a=n("5d28"),u=n("a666"),l=n("0a31");function c(t,e){var n=new a["b"](e);return n.preventDefault(),{leftButton:n.leftButton,buttons:n.buttons,posx:n.posx,posy:n.posy}}var d=function(){function t(){this._hooks=new u["b"],this._mouseMoveEventMerger=null,this._mouseMoveCallback=null,this._onStopCallback=null}return t.prototype.dispose=function(){this.stopMonitoring(!1),this._hooks.dispose()},t.prototype.stopMonitoring=function(t){if(this.isMonitoring()){this._hooks.clear(),this._mouseMoveEventMerger=null,this._mouseMoveCallback=null;var e=this._onStopCallback;this._onStopCallback=null,t&&e&&e()}},t.prototype.isMonitoring=function(){return!!this._mouseMoveEventMerger},t.prototype.startMonitoring=function(t,e,n,u,c){var d=this;if(!this.isMonitoring()){this._mouseMoveEventMerger=n,this._mouseMoveCallback=u,this._onStopCallback=c;var h=s["a"].getSameOriginWindowChain(),p=o["c"]&&l["a"].pointerEvents?"pointermove":"mousemove",f=o["c"]&&l["a"].pointerEvents?"pointerup":"mouseup",g=h.map((function(t){return t.window.document})),m=i["E"](t);m&&g.unshift(m);for(var v=0,b=g;v<b.length;v++){var y=b[v];this._hooks.add(i["m"](y,p,(function(t){r["i"]||t.buttons===e?d._mouseMoveCallback(t):d.stopMonitoring(!0)}),(function(t,e){return d._mouseMoveEventMerger(t,e)}))),this._hooks.add(i["j"](y,f,(function(t){return d.stopMonitoring(!0)})))}if(s["a"].hasDifferentOriginAncestor()){var w=h[h.length-1];this._hooks.add(i["j"](w.window.document,"mouseout",(function(t){var e=new a["b"](t);"html"===e.target.tagName.toLowerCase()&&d.stopMonitoring(!0)}))),this._hooks.add(i["j"](w.window.document,"mouseover",(function(t){var e=new a["b"](t);"html"===e.target.tagName.toLowerCase()&&d.stopMonitoring(!0)}))),this._hooks.add(i["j"](w.window.document.body,"mouseleave",(function(t){d.stopMonitoring(!0)})))}}},t}()},"07ac":function(t,e,n){var i=n("23e7"),o=n("6f53").values;i({target:"Object",stat:!0},{values:function(t){return o(t)}})},"08768":function(t,e,n){},"0a31":function(t,e,n){"use strict";n.d(e,"a",(function(){return r}));var i=n("0f70"),o=n("30db"),r={clipboard:{writeText:o["f"]||document.queryCommandSupported&&document.queryCommandSupported("copy")||!!(navigator&&navigator.clipboard&&navigator.clipboard.writeText),readText:o["f"]||!!(navigator&&navigator.clipboard&&navigator.clipboard.readText),richText:function(){if(i["i"])return!1;if(i["e"]){var t=navigator.userAgent.indexOf("Edge/"),e=parseInt(navigator.userAgent.substring(t+5,navigator.userAgent.indexOf(".",t)),10);if(!e||e>=12&&e<=16)return!1}return!0}()},keyboard:function(){return o["f"]||i["l"]?0:navigator.keyboard||i["k"]?1:2}(),touch:"ontouchstart"in window||navigator.maxTouchPoints>0||window.navigator.msMaxTouchPoints>0,pointerEvents:window.PointerEvent&&("ontouchstart"in window||window.navigator.maxTouchPoints>0||navigator.maxTouchPoints>0||window.navigator.msMaxTouchPoints>0)}},"0f70":function(t,e,n){"use strict";n.d(e,"c",(function(){return r})),n.d(e,"b",(function(){return s})),n.d(e,"o",(function(){return a})),n.d(e,"a",(function(){return u})),n.d(e,"i",(function(){return c})),n.d(e,"e",(function(){return d})),n.d(e,"f",(function(){return h})),n.d(e,"h",(function(){return p})),n.d(e,"m",(function(){return f})),n.d(e,"d",(function(){return g})),n.d(e,"k",(function(){return m})),n.d(e,"n",(function(){return v})),n.d(e,"j",(function(){return b})),n.d(e,"g",(function(){return y})),n.d(e,"l",(function(){return w}));var i=n("308f"),o=function(){function t(){this._zoomLevel=0,this._lastZoomLevelChangeTime=0,this._onDidChangeZoomLevel=new i["a"],this.onDidChangeZoomLevel=this._onDidChangeZoomLevel.event}return t.prototype.getZoomLevel=function(){return this._zoomLevel},t.prototype.getTimeSinceLastZoomLevelChanged=function(){return Date.now()-this._lastZoomLevelChangeTime},t.prototype.getPixelRatio=function(){var t=document.createElement("canvas").getContext("2d"),e=window.devicePixelRatio||1,n=t.webkitBackingStorePixelRatio||t.mozBackingStorePixelRatio||t.msBackingStorePixelRatio||t.oBackingStorePixelRatio||t.backingStorePixelRatio||1;return e/n},t.INSTANCE=new t,t}();function r(){return o.INSTANCE.getZoomLevel()}function s(){return o.INSTANCE.getTimeSinceLastZoomLevelChanged()}function a(t){return o.INSTANCE.onDidChangeZoomLevel(t)}function u(){return o.INSTANCE.getPixelRatio()}var l=navigator.userAgent,c=l.indexOf("Trident")>=0,d=l.indexOf("Edge/")>=0,h=c||d,p=l.indexOf("Firefox")>=0,f=l.indexOf("AppleWebKit")>=0,g=l.indexOf("Chrome")>=0,m=!g&&l.indexOf("Safari")>=0,v=!g&&!m&&f,b=l.indexOf("iPad")>=0||m&&navigator.maxTouchPoints>0,y=d&&l.indexOf("WebView/")>=0,w=window.matchMedia&&window.matchMedia("(display-mode: standalone)").matches},"11f7":function(t,e,n){"use strict";n.d(e,"t",(function(){return b})),n.d(e,"R",(function(){return y})),n.d(e,"M",(function(){return w})),n.d(e,"I",(function(){return O})),n.d(e,"f",(function(){return D})),n.d(e,"g",(function(){return k})),n.d(e,"P",(function(){return x})),n.d(e,"Q",(function(){return S})),n.d(e,"Y",(function(){return N})),n.d(e,"j",(function(){return T})),n.d(e,"o",(function(){return P})),n.d(e,"n",(function(){return F})),n.d(e,"h",(function(){return j})),n.d(e,"i",(function(){return M})),n.d(e,"k",(function(){return L})),n.d(e,"l",(function(){return V})),n.d(e,"U",(function(){return R})),n.d(e,"W",(function(){return H})),n.d(e,"m",(function(){return X})),n.d(e,"z",(function(){return G})),n.d(e,"y",(function(){return Z})),n.d(e,"b",(function(){return Q})),n.d(e,"F",(function(){return $})),n.d(e,"C",(function(){return tt})),n.d(e,"e",(function(){return et})),n.d(e,"H",(function(){return nt})),n.d(e,"B",(function(){return it})),n.d(e,"A",(function(){return ot})),n.d(e,"G",(function(){return rt})),n.d(e,"K",(function(){return st})),n.d(e,"x",(function(){return at})),n.d(e,"N",(function(){return lt})),n.d(e,"E",(function(){return ct})),n.d(e,"w",(function(){return dt})),n.d(e,"v",(function(){return gt})),n.d(e,"O",(function(){return mt})),n.d(e,"L",(function(){return vt})),n.d(e,"d",(function(){return bt})),n.d(e,"c",(function(){return yt})),n.d(e,"V",(function(){return wt})),n.d(e,"T",(function(){return _t})),n.d(e,"Z",(function(){return Ct})),n.d(e,"q",(function(){return Ot})),n.d(e,"a",(function(){return St})),n.d(e,"X",(function(){return Nt})),n.d(e,"J",(function(){return Bt})),n.d(e,"S",(function(){return At})),n.d(e,"D",(function(){return It})),n.d(e,"u",(function(){return Pt})),n.d(e,"ab",(function(){return Ft})),n.d(e,"p",(function(){return jt})),n.d(e,"s",(function(){return Mt})),n.d(e,"r",(function(){return Lt}));var i=n("0f70"),o=n("e32d"),r=n("b835"),s=n("5d28"),a=n("5fe7"),u=n("fdcc"),l=n("308f"),c=n("a666"),d=n("30db"),h=n("e8e3"),p=n("b589"),f=n("0a31"),g=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},t(e,n)};return function(e,n){function i(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),m=function(){return m=Object.assign||function(t){for(var e,n=1,i=arguments.length;n<i;n++)for(var o in e=arguments[n],e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},m.apply(this,arguments)},v=function(){for(var t=0,e=0,n=arguments.length;e<n;e++)t+=arguments[e].length;var i=Array(t),o=0;for(e=0;e<n;e++)for(var r=arguments[e],s=0,a=r.length;s<a;s++,o++)i[o]=r[s];return i};function b(t){while(t.firstChild)t.removeChild(t.firstChild)}function y(t){t.parentNode&&t.parentNode.removeChild(t)}function w(t){while(t){if(t===document.body)return!0;t=t.parentNode||t.host}return!1}var _=new(function(){function t(){this._lastStart=-1,this._lastEnd=-1}return t.prototype._findClassName=function(t,e){var n=t.className;if(n){e=e.trim();var i=n.length,o=e.length;if(0!==o)if(i<o)this._lastStart=-1;else{if(n===e)return this._lastStart=0,void(this._lastEnd=i);var r,s=-1;while((s=n.indexOf(e,s+1))>=0){if(r=s+o,(0===s||32===n.charCodeAt(s-1))&&32===n.charCodeAt(r))return this._lastStart=s,void(this._lastEnd=r+1);if(s>0&&32===n.charCodeAt(s-1)&&r===i)return this._lastStart=s-1,void(this._lastEnd=r);if(0===s&&r===i)return this._lastStart=0,void(this._lastEnd=r)}this._lastStart=-1}else this._lastStart=-1}else this._lastStart=-1},t.prototype.hasClass=function(t,e){return this._findClassName(t,e),-1!==this._lastStart},t.prototype.addClasses=function(t){for(var e=this,n=[],i=1;i<arguments.length;i++)n[i-1]=arguments[i];n.forEach((function(n){return n.split(" ").forEach((function(n){return e.addClass(t,n)}))}))},t.prototype.addClass=function(t,e){t.className?(this._findClassName(t,e),-1===this._lastStart&&(t.className=t.className+" "+e)):t.className=e},t.prototype.removeClass=function(t,e){this._findClassName(t,e),-1!==this._lastStart&&(t.className=t.className.substring(0,this._lastStart)+t.className.substring(this._lastEnd))},t.prototype.removeClasses=function(t){for(var e=this,n=[],i=1;i<arguments.length;i++)n[i-1]=arguments[i];n.forEach((function(n){return n.split(" ").forEach((function(n){return e.removeClass(t,n)}))}))},t.prototype.toggleClass=function(t,e,n){this._findClassName(t,e),-1===this._lastStart||void 0!==n&&n||this.removeClass(t,e),-1!==this._lastStart||void 0!==n&&!n||this.addClass(t,e)},t}()),E=new(function(){function t(){}return t.prototype.hasClass=function(t,e){return Boolean(e)&&t.classList&&t.classList.contains(e)},t.prototype.addClasses=function(t){for(var e=this,n=[],i=1;i<arguments.length;i++)n[i-1]=arguments[i];n.forEach((function(n){return n.split(" ").forEach((function(n){return e.addClass(t,n)}))}))},t.prototype.addClass=function(t,e){e&&t.classList&&t.classList.add(e)},t.prototype.removeClass=function(t,e){e&&t.classList&&t.classList.remove(e)},t.prototype.removeClasses=function(t){for(var e=this,n=[],i=1;i<arguments.length;i++)n[i-1]=arguments[i];n.forEach((function(n){return n.split(" ").forEach((function(n){return e.removeClass(t,n)}))}))},t.prototype.toggleClass=function(t,e,n){t.classList&&t.classList.toggle(e,n)},t}()),C=i["i"]?_:E,O=C.hasClass.bind(C),D=C.addClass.bind(C),k=C.addClasses.bind(C),x=C.removeClass.bind(C),S=C.removeClasses.bind(C),N=C.toggleClass.bind(C),B=function(){function t(t,e,n,i){this._node=t,this._type=e,this._handler=n,this._options=i||!1,this._node.addEventListener(this._type,this._handler,this._options)}return t.prototype.dispose=function(){this._handler&&(this._node.removeEventListener(this._type,this._handler,this._options),this._node=null,this._handler=null)},t}();function T(t,e,n,i){return new B(t,e,n,i)}function A(t){return function(e){return t(new s["b"](e))}}function I(t){return function(e){return t(new r["a"](e))}}var P=function(t,e,n,i){var o=n;return"click"===e||"mousedown"===e?o=A(n):"keydown"!==e&&"keypress"!==e&&"keyup"!==e||(o=I(n)),T(t,e,o,i)},F=function(t,e,n){var i=A(e);return j(t,i,n)};function j(t,e,n){return T(t,d["c"]&&f["a"].pointerEvents?bt.POINTER_DOWN:bt.MOUSE_DOWN,e,n)}function M(t,e,n){return T(t,d["c"]&&f["a"].pointerEvents?bt.POINTER_UP:bt.MOUSE_UP,e,n)}function L(t,e){return T(t,"mouseout",(function(n){var i=n.relatedTarget;while(i&&i!==t)i=i.parentNode;i!==t&&e(n)}))}function V(t,e){return T(t,"pointerout",(function(n){var i=n.relatedTarget;while(i&&i!==t)i=i.parentNode;i!==t&&e(n)}))}var R,H,W=null;function K(t){if(!W){var e=function(t){return setTimeout((function(){return t((new Date).getTime())}),0)};W=self.requestAnimationFrame||self.msRequestAnimationFrame||self.webkitRequestAnimationFrame||self.mozRequestAnimationFrame||self.oRequestAnimationFrame||e}return W.call(self,t)}var U=function(){function t(t,e){void 0===e&&(e=0),this._runner=t,this.priority=e,this._canceled=!1}return t.prototype.dispose=function(){this._canceled=!0},t.prototype.execute=function(){if(!this._canceled)try{this._runner()}catch(t){Object(u["e"])(t)}},t.sort=function(t,e){return e.priority-t.priority},t}();(function(){var t=[],e=null,n=!1,i=!1,o=function(){n=!1,e=t,t=[],i=!0;while(e.length>0){e.sort(U.sort);var o=e.shift();o.execute()}i=!1};H=function(e,i){void 0===i&&(i=0);var r=new U(e,i);return t.push(r),n||(n=!0,K(o)),r},R=function(t,n){if(i){var o=new U(t,n);return e.push(o),o}return H(t,n)}})();var q=16,Y=function(t,e){return e},z=function(t){function e(e,n,i,o,r){void 0===o&&(o=Y),void 0===r&&(r=q);var s=t.call(this)||this,u=null,l=0,c=s._register(new a["e"]),d=function(){l=(new Date).getTime(),i(u),u=null};return s._register(T(e,n,(function(t){u=o(u,t);var e=(new Date).getTime()-l;e>=r?(c.cancel(),d()):c.setIfNotSet(d,r-e)}))),s}return g(e,t),e}(c["a"]);function X(t,e,n,i,o){return new z(t,e,n,i,o)}function G(t){return document.defaultView.getComputedStyle(t,null)}function Z(t){if(t!==document.body)return new Q(t.clientWidth,t.clientHeight);if(d["c"]&&window.visualViewport){var e=window.visualViewport.width,n=window.visualViewport.height-(i["l"]?24:0);return new Q(e,n)}if(window.innerWidth&&window.innerHeight)return new Q(window.innerWidth,window.innerHeight);if(document.body&&document.body.clientWidth&&document.body.clientHeight)return new Q(document.body.clientWidth,document.body.clientHeight);if(document.documentElement&&document.documentElement.clientWidth&&document.documentElement.clientHeight)return new Q(document.documentElement.clientWidth,document.documentElement.clientHeight);throw new Error("Unable to figure out browser width and height")}var J=function(){function t(){}return t.convertToPixels=function(t,e){return parseFloat(e)||0},t.getDimension=function(e,n,i){var o=G(e),r="0";return o&&(r=o.getPropertyValue?o.getPropertyValue(n):o.getAttribute(i)),t.convertToPixels(e,r)},t.getBorderLeftWidth=function(e){return t.getDimension(e,"border-left-width","borderLeftWidth")},t.getBorderRightWidth=function(e){return t.getDimension(e,"border-right-width","borderRightWidth")},t.getBorderTopWidth=function(e){return t.getDimension(e,"border-top-width","borderTopWidth")},t.getBorderBottomWidth=function(e){return t.getDimension(e,"border-bottom-width","borderBottomWidth")},t.getPaddingLeft=function(e){return t.getDimension(e,"padding-left","paddingLeft")},t.getPaddingRight=function(e){return t.getDimension(e,"padding-right","paddingRight")},t.getPaddingTop=function(e){return t.getDimension(e,"padding-top","paddingTop")},t.getPaddingBottom=function(e){return t.getDimension(e,"padding-bottom","paddingBottom")},t.getMarginLeft=function(e){return t.getDimension(e,"margin-left","marginLeft")},t.getMarginTop=function(e){return t.getDimension(e,"margin-top","marginTop")},t.getMarginRight=function(e){return t.getDimension(e,"margin-right","marginRight")},t.getMarginBottom=function(e){return t.getDimension(e,"margin-bottom","marginBottom")},t}(),Q=function(){function t(t,e){this.width=t,this.height=e}return t}();function $(t){var e=t.offsetParent,n=t.offsetTop,i=t.offsetLeft;while(null!==(t=t.parentNode)&&t!==document.body&&t!==document.documentElement){n-=t.scrollTop;var o=ut(t)?null:G(t);o&&(i-="rtl"!==o.direction?t.scrollLeft:-t.scrollLeft),t===e&&(i+=J.getBorderLeftWidth(t),n+=J.getBorderTopWidth(t),n+=t.offsetTop,i+=t.offsetLeft,e=t.offsetParent)}return{left:i,top:n}}function tt(t){var e=t.getBoundingClientRect();return{left:e.left+et.scrollX,top:e.top+et.scrollY,width:e.width,height:e.height}}var et=new(function(){function t(){}return Object.defineProperty(t.prototype,"scrollX",{get:function(){return"number"===typeof window.scrollX?window.scrollX:document.body.scrollLeft+document.documentElement.scrollLeft},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"scrollY",{get:function(){return"number"===typeof window.scrollY?window.scrollY:document.body.scrollTop+document.documentElement.scrollTop},enumerable:!0,configurable:!0}),t}());function nt(t){var e=J.getMarginLeft(t)+J.getMarginRight(t);return t.offsetWidth+e}function it(t){var e=J.getBorderLeftWidth(t)+J.getBorderRightWidth(t),n=J.getPaddingLeft(t)+J.getPaddingRight(t);return t.offsetWidth-e-n}function ot(t){var e=J.getBorderTopWidth(t)+J.getBorderBottomWidth(t),n=J.getPaddingTop(t)+J.getPaddingBottom(t);return t.offsetHeight-e-n}function rt(t){var e=J.getMarginTop(t)+J.getMarginBottom(t);return t.offsetHeight+e}function st(t,e){while(t){if(t===e)return!0;t=t.parentNode}return!1}function at(t,e,n){while(t&&t.nodeType===t.ELEMENT_NODE){if(O(t,e))return t;if(n)if("string"===typeof n){if(O(t,n))return null}else if(t===n)return null;t=t.parentNode}return null}function ut(t){return t&&!!t.host&&!!t.mode}function lt(t){return!!ct(t)}function ct(t){while(t.parentNode){if(t===document.body)return null;t=t.parentNode}return ut(t)?t:null}function dt(t){void 0===t&&(t=document.getElementsByTagName("head")[0]);var e=document.createElement("style");return e.type="text/css",e.media="screen",t.appendChild(e),e}var ht=null;function pt(){return ht||(ht=dt()),ht}function ft(t){return t&&t.sheet&&t.sheet.rules?t.sheet.rules:t&&t.sheet&&t.sheet.cssRules?t.sheet.cssRules:[]}function gt(t,e,n){void 0===n&&(n=pt()),n&&e&&n.sheet.insertRule(t+"{"+e+"}",0)}function mt(t,e){if(void 0===e&&(e=pt()),e){for(var n=ft(e),i=[],o=0;o<n.length;o++){var r=n[o];-1!==r.selectorText.indexOf(t)&&i.push(o)}for(o=i.length-1;o>=0;o--)e.sheet.deleteRule(i[o])}}function vt(t){return"object"===typeof HTMLElement?t instanceof HTMLElement:t&&"object"===typeof t&&1===t.nodeType&&"string"===typeof t.nodeName}var bt={CLICK:"click",DBLCLICK:"dblclick",MOUSE_UP:"mouseup",MOUSE_DOWN:"mousedown",MOUSE_OVER:"mouseover",MOUSE_MOVE:"mousemove",MOUSE_OUT:"mouseout",MOUSE_ENTER:"mouseenter",MOUSE_LEAVE:"mouseleave",POINTER_UP:"pointerup",POINTER_DOWN:"pointerdown",POINTER_MOVE:"pointermove",CONTEXT_MENU:"contextmenu",WHEEL:"wheel",KEY_DOWN:"keydown",KEY_PRESS:"keypress",KEY_UP:"keyup",LOAD:"load",BEFORE_UNLOAD:"beforeunload",UNLOAD:"unload",ABORT:"abort",ERROR:"error",RESIZE:"resize",SCROLL:"scroll",FULLSCREEN_CHANGE:"fullscreenchange",WK_FULLSCREEN_CHANGE:"webkitfullscreenchange",SELECT:"select",CHANGE:"change",SUBMIT:"submit",RESET:"reset",FOCUS:"focus",FOCUS_IN:"focusin",FOCUS_OUT:"focusout",BLUR:"blur",INPUT:"input",STORAGE:"storage",DRAG_START:"dragstart",DRAG:"drag",DRAG_ENTER:"dragenter",DRAG_LEAVE:"dragleave",DRAG_OVER:"dragover",DROP:"drop",DRAG_END:"dragend",ANIMATION_START:i["m"]?"webkitAnimationStart":"animationstart",ANIMATION_END:i["m"]?"webkitAnimationEnd":"animationend",ANIMATION_ITERATION:i["m"]?"webkitAnimationIteration":"animationiteration"},yt={stop:function(t,e){t.preventDefault?t.preventDefault():t.returnValue=!1,e&&(t.stopPropagation?t.stopPropagation():t.cancelBubble=!0)}};function wt(t){for(var e=[],n=0;t&&t.nodeType===t.ELEMENT_NODE;n++)e[n]=t.scrollTop,t=t.parentNode;return e}function _t(t,e){for(var n=0;t&&t.nodeType===t.ELEMENT_NODE;n++)t.scrollTop!==e[n]&&(t.scrollTop=e[n]),t=t.parentNode}var Et=function(t){function e(e){var n=t.call(this)||this;n._onDidFocus=n._register(new l["a"]),n.onDidFocus=n._onDidFocus.event,n._onDidBlur=n._register(new l["a"]),n.onDidBlur=n._onDidBlur.event;var i=st(document.activeElement,e),r=!1,s=function(){r=!1,i||(i=!0,n._onDidFocus.fire())},a=function(){i&&(r=!0,window.setTimeout((function(){r&&(r=!1,i=!1,n._onDidBlur.fire())}),0))};return n._refreshStateHandler=function(){var t=st(document.activeElement,e);t!==i&&(i?a():s())},n._register(Object(o["a"])(e,bt.FOCUS,!0)(s)),n._register(Object(o["a"])(e,bt.BLUR,!0)(a)),n}return g(e,t),e}(c["a"]);function Ct(t){return new Et(t)}function Ot(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return e.forEach((function(e){return t.appendChild(e)})),e[e.length-1]}var Dt,kt=/([\w\-]+)?(#([\w\-]+))?((.([\w\-]+))*)/;function xt(t,e,n){for(var i=[],o=3;o<arguments.length;o++)i[o-3]=arguments[o];var r=kt.exec(e);if(!r)throw new Error("Bad use of emmet");n=m({},n||{});var s,a=r[1]||"div";return s=t!==Dt.HTML?document.createElementNS(t,a):document.createElement(a),r[3]&&(s.id=r[3]),r[4]&&(s.className=r[4].replace(/\./g," ").trim()),Object.keys(n).forEach((function(t){var e=n[t];"undefined"!==typeof e&&(/^on\w+$/.test(t)?s[t]=e:"selected"===t?e&&s.setAttribute(t,"true"):s.setAttribute(t,e))})),Object(h["d"])(i).forEach((function(t){t instanceof Node?s.appendChild(t):s.appendChild(document.createTextNode(t))})),s}function St(t,e){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];return xt.apply(void 0,v([Dt.HTML,t,e],n))}function Nt(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var n=0,i=t;n<i.length;n++){var o=i[n];o.style.display="",o.removeAttribute("aria-hidden")}}function Bt(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var n=0,i=t;n<i.length;n++){var o=i[n];o.style.display="none",o.setAttribute("aria-hidden","true")}}function Tt(t,e){while(t&&t.nodeType===t.ELEMENT_NODE){if(t instanceof HTMLElement&&t.hasAttribute(e))return t;t=t.parentNode}return null}function At(t){if(t&&t.hasAttribute("tabIndex")){if(document.activeElement===t){var e=Tt(t.parentElement,"tabIndex");e&&e.focus()}t.removeAttribute("tabindex")}}function It(t){return Array.prototype.slice.call(document.getElementsByTagName(t),0)}function Pt(t){var e=window.devicePixelRatio*t;return Math.max(1,Math.floor(e))/window.devicePixelRatio}function Ft(t){if(d["f"]||i["g"])window.open(t);else{var e=window.open();e&&(e.opener=null,e.location.href=t)}}function jt(t){var e=function(){t(),n=H(e)},n=H(e);return Object(c["h"])((function(){return n.dispose()}))}function Mt(t){return t&&p["b"].vscodeRemote===t.scheme?p["a"].rewrite(t):t}function Lt(t){return t?"url('"+Mt(t).toString(!0).replace(/'/g,"%27")+"')":"url('')"}(function(t){t["HTML"]="http://www.w3.org/1999/xhtml",t["SVG"]="http://www.w3.org/2000/svg"})(Dt||(Dt={})),St.SVG=function(t,e){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];return xt.apply(void 0,v([Dt.SVG,t,e],n))},p["a"].setPreferredWebSchema(/^https:/.test(window.location.href)?"https":"http")},"18a5":function(t,e,n){"use strict";var i=n("23e7"),o=n("857a"),r=n("af03");i({target:"String",proto:!0,forced:r("anchor")},{anchor:function(t){return o(this,"a","name",t)}})},"2a04":function(t,e,n){},"31df":function(t,e,n){"use strict";n.d(e,"c",(function(){return o})),n.d(e,"b",(function(){return r})),n.d(e,"a",(function(){return s}));var i=n("11f7");function o(t,e){void 0===e&&(e={});var n=s(e);return n.textContent=t,n}function r(t,e){void 0===e&&(e={});var n=s(e);return u(n,l(t),e.actionHandler),n}function s(t){var e=t.inline?"span":"div",n=document.createElement(e);return t.className&&(n.className=t.className),n}var a=function(){function t(t){this.source=t,this.index=0}return t.prototype.eos=function(){return this.index>=this.source.length},t.prototype.next=function(){var t=this.peek();return this.advance(),t},t.prototype.peek=function(){return this.source[this.index]},t.prototype.advance=function(){this.index++},t}();function u(t,e,n){var o;if(2===e.type)o=document.createTextNode(e.content||"");else if(3===e.type)o=document.createElement("b");else if(4===e.type)o=document.createElement("i");else if(5===e.type&&n){var r=document.createElement("a");r.href="#",n.disposeables.add(i["o"](r,"click",(function(t){n.callback(String(e.index),t)}))),o=r}else 7===e.type?o=document.createElement("br"):1===e.type&&(o=t);o&&t!==o&&t.appendChild(o),o&&Array.isArray(e.children)&&e.children.forEach((function(t){u(o,t,n)}))}function l(t){var e={type:1,children:[]},n=0,i=e,o=[],r=new a(t);while(!r.eos()){var s=r.next(),u="\\"===s&&0!==d(r.peek());if(u&&(s=r.next()),!u&&c(s)&&s===r.peek()){r.advance(),2===i.type&&(i=o.pop());var l=d(s);if(i.type===l||5===i.type&&6===l)i=o.pop();else{var h={type:l,children:[]};5===l&&(h.index=n,n++),i.children.push(h),o.push(i),i=h}}else if("\n"===s)2===i.type&&(i=o.pop()),i.children.push({type:7});else if(2!==i.type){var p={type:2,content:s};i.children.push(p),o.push(i),i=p}else i.content+=s}return 2===i.type&&(i=o.pop()),o.length,e}function c(t){return 0!==d(t)}function d(t){switch(t){case"*":return 3;case"_":return 4;case"[":return 5;case"]":return 6;default:return 0}}},"35b3":function(t,e,n){var i=n("23e7");i({target:"Number",stat:!0},{EPSILON:Math.pow(2,-52)})},3813:function(t,e,n){"use strict";n.d(e,"b",(function(){return l})),n.d(e,"a",(function(){return c})),n.d(e,"c",(function(){return d}));n("5029");var i,o,r,s=n("dff7"),a=n("30db"),u=n("11f7");function l(t){i=document.createElement("div"),i.className="monaco-aria-container",o=document.createElement("div"),o.className="monaco-alert",o.setAttribute("role","alert"),o.setAttribute("aria-atomic","true"),i.appendChild(o),r=document.createElement("div"),r.className="monaco-status",r.setAttribute("role","status"),r.setAttribute("aria-atomic","true"),i.appendChild(r),t.appendChild(i)}function c(t,e){f(o,t,e)}function d(t,e){a["e"]?c(t,e):f(r,t,e)}var h=0,p=void 0;function f(t,e,n){if(i){if(!n)switch(p===e?h++:(p=e,h=0),h){case 0:break;case 1:e=s["a"]("repeated","{0} (occurred again)",e);break;default:e=s["a"]("repeatedNtimes","{0} (occurred {1} times)",e,h);break}u["t"](t),t.textContent=e,t.style.visibility="hidden",t.style.visibility="visible"}}},"3bfb":function(t,e,n){"use strict";n.d(e,"a",(function(){return u}));n("54f2");var i=n("11f7"),o=n("3742"),r=n("ceb8"),s=n("aa3d"),a={badgeBackground:r["a"].fromHex("#4D4D4D"),badgeForeground:r["a"].fromHex("#FFFFFF")},u=function(){function t(t,e){this.count=0,this.options=e||Object.create(null),Object(s["g"])(this.options,a,!1),this.badgeBackground=this.options.badgeBackground,this.badgeForeground=this.options.badgeForeground,this.badgeBorder=this.options.badgeBorder,this.element=Object(i["q"])(t,Object(i["a"])(".monaco-count-badge")),this.countFormat=this.options.countFormat||"{0}",this.titleFormat=this.options.titleFormat||"",this.setCount(this.options.count||0)}return t.prototype.setCount=function(t){this.count=t,this.render()},t.prototype.setTitleFormat=function(t){this.titleFormat=t,this.render()},t.prototype.render=function(){this.element.textContent=Object(o["r"])(this.countFormat,this.count),this.element.title=Object(o["r"])(this.titleFormat,this.count),this.applyStyles()},t.prototype.style=function(t){this.badgeBackground=t.badgeBackground,this.badgeForeground=t.badgeForeground,this.badgeBorder=t.badgeBorder,this.applyStyles()},t.prototype.applyStyles=function(){if(this.element){var t=this.badgeBackground?this.badgeBackground.toString():"",e=this.badgeForeground?this.badgeForeground.toString():"",n=this.badgeBorder?this.badgeBorder.toString():"";this.element.style.backgroundColor=t,this.element.style.color=e,this.element.style.borderWidth=n?"1px":"",this.element.style.borderStyle=n?"solid":"",this.element.style.borderColor=n}},t}()},"4d3d":function(t,e,n){},5029:function(t,e,n){},"54f2":function(t,e,n){},"5aa5":function(t,e,n){"use strict";n.d(e,"c",(function(){return g})),n.d(e,"d",(function(){return m})),n.d(e,"b",(function(){return v})),n.d(e,"a",(function(){return y}));n("c84a");var i=n("30db"),o=n("dff7"),r=n("a666"),s=n("f070"),a=n("11f7"),u=n("ef8e"),l=n("a60f"),c=n("b835"),d=n("308f"),h=n("650e"),p=n("0f70"),f=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},t(e,n)};return function(e,n){function i(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),g=function(t){function e(e,n,i){var o=t.call(this)||this;return o.options=i,o._context=e||o,o._action=n,n instanceof s["a"]&&o._register(n.onDidChange((function(t){o.element&&o.handleActionChangeEvent(t)}))),o}return f(e,t),e.prototype.handleActionChangeEvent=function(t){void 0!==t.enabled&&this.updateEnabled(),void 0!==t.checked&&this.updateChecked(),void 0!==t.class&&this.updateClass(),void 0!==t.label&&(this.updateLabel(),this.updateTooltip()),void 0!==t.tooltip&&this.updateTooltip()},Object.defineProperty(e.prototype,"actionRunner",{get:function(){return this._actionRunner||(this._actionRunner=this._register(new s["b"])),this._actionRunner},set:function(t){this._actionRunner=t},enumerable:!0,configurable:!0}),e.prototype.getAction=function(){return this._action},e.prototype.isEnabled=function(){return this._action.enabled},e.prototype.setActionContext=function(t){this._context=t},e.prototype.render=function(t){var e=this,n=this.element=t;this._register(l["b"].addTarget(t));var o=this.options&&this.options.draggable;o&&(t.draggable=!0,p["h"]&&this._register(a["j"](t,a["d"].DRAG_START,(function(t){var n;return null===(n=t.dataTransfer)||void 0===n?void 0:n.setData(h["a"].TEXT,e._action.label)})))),this._register(a["j"](n,l["a"].Tap,(function(t){return e.onClick(t)}))),this._register(a["j"](n,a["d"].MOUSE_DOWN,(function(t){o||a["c"].stop(t,!0),e._action.enabled&&0===t.button&&a["f"](n,"active")}))),this._register(a["j"](n,a["d"].CLICK,(function(t){a["c"].stop(t,!0),e.options&&e.options.isMenu?e.onClick(t):i["i"]((function(){return e.onClick(t)}))}))),this._register(a["j"](n,a["d"].DBLCLICK,(function(t){a["c"].stop(t,!0)}))),[a["d"].MOUSE_UP,a["d"].MOUSE_OUT].forEach((function(t){e._register(a["j"](n,t,(function(t){a["c"].stop(t),a["P"](n,"active")})))}))},e.prototype.onClick=function(t){var e;a["c"].stop(t,!0),u["l"](this._context)?e=t:(e=this._context,u["i"](e)&&(e.event=t)),this.actionRunner.run(this._action,e)},e.prototype.focus=function(){this.element&&(this.element.focus(),a["f"](this.element,"focused"))},e.prototype.blur=function(){this.element&&(this.element.blur(),a["P"](this.element,"focused"))},e.prototype.updateEnabled=function(){},e.prototype.updateLabel=function(){},e.prototype.updateTooltip=function(){},e.prototype.updateClass=function(){},e.prototype.updateChecked=function(){},e.prototype.dispose=function(){this.element&&(a["R"](this.element),this.element=void 0),t.prototype.dispose.call(this)},e}(r["a"]),m=function(t){function e(n){var i=t.call(this,e.ID,n,n?"separator text":"separator")||this;return i.checked=!1,i.enabled=!1,i}return f(e,t),e.ID="vs.actions.separator",e}(s["a"]),v=function(t){function e(e,n,i){void 0===i&&(i={});var o=t.call(this,e,n,i)||this;return o.options=i,o.options.icon=void 0!==i.icon&&i.icon,o.options.label=void 0===i.label||i.label,o.cssClass="",o}return f(e,t),e.prototype.render=function(e){t.prototype.render.call(this,e),this.element&&(this.label=a["q"](this.element,a["a"]("a.action-label"))),this.label&&(this._action.id===m.ID?this.label.setAttribute("role","presentation"):this.options.isMenu?this.label.setAttribute("role","menuitem"):this.label.setAttribute("role","button")),this.options.label&&this.options.keybinding&&this.element&&(a["q"](this.element,a["a"]("span.keybinding")).textContent=this.options.keybinding),this.updateClass(),this.updateLabel(),this.updateTooltip(),this.updateEnabled(),this.updateChecked()},e.prototype.focus=function(){t.prototype.focus.call(this),this.label&&this.label.focus()},e.prototype.updateLabel=function(){this.options.label&&this.label&&(this.label.textContent=this.getAction().label)},e.prototype.updateTooltip=function(){var t=null;this.getAction().tooltip?t=this.getAction().tooltip:!this.options.label&&this.getAction().label&&this.options.icon&&(t=this.getAction().label,this.options.keybinding&&(t=o["a"]({key:"titleLabel",comment:["action title","action keybinding"]},"{0} ({1})",t,this.options.keybinding))),t&&this.label&&(this.label.title=t)},e.prototype.updateClass=function(){this.cssClass&&this.label&&a["Q"](this.label,this.cssClass),this.options.icon?(this.cssClass=this.getAction().class,this.label&&(a["f"](this.label,"codicon"),this.cssClass&&a["g"](this.label,this.cssClass)),this.updateEnabled()):this.label&&a["P"](this.label,"codicon")},e.prototype.updateEnabled=function(){this.getAction().enabled?(this.label&&(this.label.removeAttribute("aria-disabled"),a["P"](this.label,"disabled"),this.label.tabIndex=0),this.element&&a["P"](this.element,"disabled")):(this.label&&(this.label.setAttribute("aria-disabled","true"),a["f"](this.label,"disabled"),a["S"](this.label)),this.element&&a["f"](this.element,"disabled"))},e.prototype.updateChecked=function(){this.label&&(this.getAction().checked?a["f"](this.label,"checked"):a["P"](this.label,"checked"))},e}(g),b={orientation:0,context:null,triggerKeys:{keys:[3,10],keyDown:!1}},y=function(t){function e(e,n){void 0===n&&(n=b);var i,o,r=t.call(this)||this;switch(r._onDidBlur=r._register(new d["a"]),r.onDidBlur=r._onDidBlur.event,r._onDidCancel=r._register(new d["a"]),r.onDidCancel=r._onDidCancel.event,r._onDidRun=r._register(new d["a"]),r.onDidRun=r._onDidRun.event,r._onDidBeforeRun=r._register(new d["a"]),r.onDidBeforeRun=r._onDidBeforeRun.event,r.options=n,r._context=n.context,r.options.triggerKeys||(r.options.triggerKeys=b.triggerKeys),r.options.actionRunner?r._actionRunner=r.options.actionRunner:(r._actionRunner=new s["b"],r._register(r._actionRunner)),r._register(r._actionRunner.onDidRun((function(t){return r._onDidRun.fire(t)}))),r._register(r._actionRunner.onDidBeforeRun((function(t){return r._onDidBeforeRun.fire(t)}))),r.viewItems=[],r.focusedItem=void 0,r.domNode=document.createElement("div"),r.domNode.className="monaco-action-bar",!1!==n.animated&&a["f"](r.domNode,"animated"),r.options.orientation){case 0:i=15,o=17;break;case 1:i=17,o=15,r.domNode.className+=" reverse";break;case 2:i=16,o=18,r.domNode.className+=" vertical";break;case 3:i=18,o=16,r.domNode.className+=" vertical reverse";break}return r._register(a["j"](r.domNode,a["d"].KEY_DOWN,(function(t){var e=new c["a"](t),n=!0;e.equals(i)?r.focusPrevious():e.equals(o)?r.focusNext():e.equals(9)?r.cancel():r.isTriggerKeyEvent(e)?r.options.triggerKeys&&r.options.triggerKeys.keyDown&&r.doTrigger(e):n=!1,n&&(e.preventDefault(),e.stopPropagation())}))),r._register(a["j"](r.domNode,a["d"].KEY_UP,(function(t){var e=new c["a"](t);r.isTriggerKeyEvent(e)?(r.options.triggerKeys&&!r.options.triggerKeys.keyDown&&r.doTrigger(e),e.preventDefault(),e.stopPropagation()):(e.equals(2)||e.equals(1026))&&r.updateFocusedItem()}))),r.focusTracker=r._register(a["Z"](r.domNode)),r._register(r.focusTracker.onDidBlur((function(){document.activeElement!==r.domNode&&a["K"](document.activeElement,r.domNode)||(r._onDidBlur.fire(),r.focusedItem=void 0)}))),r._register(r.focusTracker.onDidFocus((function(){return r.updateFocusedItem()}))),r.actionsList=document.createElement("ul"),r.actionsList.className="actions-container",r.actionsList.setAttribute("role","toolbar"),r.options.ariaLabel&&r.actionsList.setAttribute("aria-label",r.options.ariaLabel),r.domNode.appendChild(r.actionsList),e.appendChild(r.domNode),r}return f(e,t),e.prototype.isTriggerKeyEvent=function(t){var e=!1;return this.options.triggerKeys&&this.options.triggerKeys.keys.forEach((function(n){e=e||t.equals(n)})),e},e.prototype.updateFocusedItem=function(){for(var t=0;t<this.actionsList.children.length;t++){var e=this.actionsList.children[t];if(a["K"](document.activeElement,e)){this.focusedItem=t;break}}},Object.defineProperty(e.prototype,"context",{get:function(){return this._context},set:function(t){this._context=t,this.viewItems.forEach((function(e){return e.setActionContext(t)}))},enumerable:!0,configurable:!0}),e.prototype.getContainer=function(){return this.domNode},e.prototype.push=function(t,e){var n=this;void 0===e&&(e={});var i=Array.isArray(t)?t:[t],o=u["h"](e.index)?e.index:null;i.forEach((function(t){var i,r=document.createElement("li");r.className="action-item",r.setAttribute("role","presentation"),n._register(a["j"](r,a["d"].CONTEXT_MENU,(function(t){t.preventDefault(),t.stopPropagation()}))),n.options.actionViewItemProvider&&(i=n.options.actionViewItemProvider(t)),i||(i=new v(n.context,t,e)),i.actionRunner=n._actionRunner,i.setActionContext(n.context),i.render(r),null===o||o<0||o>=n.actionsList.children.length?(n.actionsList.appendChild(r),n.viewItems.push(i)):(n.actionsList.insertBefore(r,n.actionsList.children[o]),n.viewItems.splice(o,0,i),o++)}))},e.prototype.clear=function(){this.viewItems=Object(r["f"])(this.viewItems),a["t"](this.actionsList)},e.prototype.isEmpty=function(){return 0===this.viewItems.length},e.prototype.focus=function(t){var e=!1,n=void 0;void 0===t?e=!0:"number"===typeof t?n=t:"boolean"===typeof t&&(e=t),e&&"undefined"===typeof this.focusedItem?(this.focusedItem=this.viewItems.length-1,this.focusNext()):(void 0!==n&&(this.focusedItem=n),this.updateFocus())},e.prototype.focusNext=function(){"undefined"===typeof this.focusedItem&&(this.focusedItem=this.viewItems.length-1);var t,e=this.focusedItem;do{this.focusedItem=(this.focusedItem+1)%this.viewItems.length,t=this.viewItems[this.focusedItem]}while(this.focusedItem!==e&&!t.isEnabled());this.focusedItem!==e||t.isEnabled()||(this.focusedItem=void 0),this.updateFocus()},e.prototype.focusPrevious=function(){"undefined"===typeof this.focusedItem&&(this.focusedItem=0);var t,e=this.focusedItem;do{this.focusedItem=this.focusedItem-1,this.focusedItem<0&&(this.focusedItem=this.viewItems.length-1),t=this.viewItems[this.focusedItem]}while(this.focusedItem!==e&&!t.isEnabled());this.focusedItem!==e||t.isEnabled()||(this.focusedItem=void 0),this.updateFocus(!0)},e.prototype.updateFocus=function(t,e){"undefined"===typeof this.focusedItem&&this.actionsList.focus({preventScroll:e});for(var n=0;n<this.viewItems.length;n++){var i=this.viewItems[n],o=i;n===this.focusedItem?u["g"](o.isEnabled)&&(o.isEnabled()&&u["g"](o.focus)?o.focus(t):this.actionsList.focus({preventScroll:e})):u["g"](o.blur)&&o.blur()}},e.prototype.doTrigger=function(t){if("undefined"!==typeof this.focusedItem){var e=this.viewItems[this.focusedItem];if(e instanceof g){var n=null===e._context||void 0===e._context?t:e._context;this.run(e._action,n)}}},e.prototype.cancel=function(){document.activeElement instanceof HTMLElement&&document.activeElement.blur(),this._onDidCancel.fire()},e.prototype.run=function(t,e){return this._actionRunner.run(t,e)},e.prototype.dispose=function(){Object(r["f"])(this.viewItems),this.viewItems=[],a["R"](this.getContainer()),t.prototype.dispose.call(this)},e}(r["a"])},"5cdb":function(t,e,n){},"5d28":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"a",(function(){return u})),n.d(e,"c",(function(){return l}));var i=n("0f70"),o=n("e757"),r=n("30db"),s=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},t(e,n)};return function(e,n){function i(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),a=function(){function t(t){this.timestamp=Date.now(),this.browserEvent=t,this.leftButton=0===t.button,this.middleButton=1===t.button,this.rightButton=2===t.button,this.buttons=t.buttons,this.target=t.target,this.detail=t.detail||1,"dblclick"===t.type&&(this.detail=2),this.ctrlKey=t.ctrlKey,this.shiftKey=t.shiftKey,this.altKey=t.altKey,this.metaKey=t.metaKey,"number"===typeof t.pageX?(this.posx=t.pageX,this.posy=t.pageY):(this.posx=t.clientX+document.body.scrollLeft+document.documentElement.scrollLeft,this.posy=t.clientY+document.body.scrollTop+document.documentElement.scrollTop);var e=o["a"].getPositionOfChildWindowRelativeToAncestorWindow(self,t.view);this.posx-=e.left,this.posy-=e.top}return t.prototype.preventDefault=function(){this.browserEvent.preventDefault&&this.browserEvent.preventDefault()},t.prototype.stopPropagation=function(){this.browserEvent.stopPropagation&&this.browserEvent.stopPropagation()},t}(),u=function(t){function e(e){var n=t.call(this,e)||this;return n.dataTransfer=e.dataTransfer,n}return s(e,t),e}(a),l=function(){function t(t,e,n){if(void 0===e&&(e=0),void 0===n&&(n=0),this.browserEvent=t||null,this.target=t?t.target||t.targetNode||t.srcElement:null,this.deltaY=n,this.deltaX=e,t){var o=t,s=t;if("undefined"!==typeof o.wheelDeltaY)this.deltaY=o.wheelDeltaY/120;else if("undefined"!==typeof s.VERTICAL_AXIS&&s.axis===s.VERTICAL_AXIS)this.deltaY=-s.detail/3;else if("wheel"===t.type){var a=t;a.deltaMode===a.DOM_DELTA_LINE?this.deltaY=-t.deltaY:this.deltaY=-t.deltaY/40}if("undefined"!==typeof o.wheelDeltaX)i["k"]&&r["h"]?this.deltaX=-o.wheelDeltaX/120:this.deltaX=o.wheelDeltaX/120;else if("undefined"!==typeof s.HORIZONTAL_AXIS&&s.axis===s.HORIZONTAL_AXIS)this.deltaX=-t.detail/3;else if("wheel"===t.type){a=t;a.deltaMode===a.DOM_DELTA_LINE?this.deltaX=-t.deltaX:this.deltaX=-t.deltaX/40}0===this.deltaY&&0===this.deltaX&&t.wheelDelta&&(this.deltaY=t.wheelDelta/120)}}return t.prototype.preventDefault=function(){this.browserEvent&&this.browserEvent.preventDefault&&this.browserEvent.preventDefault()},t.prototype.stopPropagation=function(){this.browserEvent&&this.browserEvent.stopPropagation&&this.browserEvent.stopPropagation()},t}()},"650e":function(t,e,n){"use strict";n.d(e,"a",(function(){return i})),n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return r}));var i={RESOURCES:"ResourceURLs",DOWNLOAD_URL:"DownloadURL",FILES:"Files",TEXT:"text/plain"},o=function(){function t(t){this.data=t}return t.prototype.update=function(){},t.prototype.getData=function(){return this.data},t}(),r={CurrentDragAndDropData:void 0}},6653:function(t,e,n){"use strict";n.d(e,"a",(function(){return o})),n.d(e,"b",(function(){return r}));var i=n("11f7"),o=function(){function t(t){this.domNode=t,this._maxWidth=-1,this._width=-1,this._height=-1,this._top=-1,this._left=-1,this._bottom=-1,this._right=-1,this._fontFamily="",this._fontWeight="",this._fontSize=-1,this._fontFeatureSettings="",this._lineHeight=-1,this._letterSpacing=-100,this._className="",this._display="",this._position="",this._visibility="",this._backgroundColor="",this._layerHint=!1,this._contain="none"}return t.prototype.setMaxWidth=function(t){this._maxWidth!==t&&(this._maxWidth=t,this.domNode.style.maxWidth=this._maxWidth+"px")},t.prototype.setWidth=function(t){this._width!==t&&(this._width=t,this.domNode.style.width=this._width+"px")},t.prototype.setHeight=function(t){this._height!==t&&(this._height=t,this.domNode.style.height=this._height+"px")},t.prototype.setTop=function(t){this._top!==t&&(this._top=t,this.domNode.style.top=this._top+"px")},t.prototype.unsetTop=function(){-1!==this._top&&(this._top=-1,this.domNode.style.top="")},t.prototype.setLeft=function(t){this._left!==t&&(this._left=t,this.domNode.style.left=this._left+"px")},t.prototype.setBottom=function(t){this._bottom!==t&&(this._bottom=t,this.domNode.style.bottom=this._bottom+"px")},t.prototype.setRight=function(t){this._right!==t&&(this._right=t,this.domNode.style.right=this._right+"px")},t.prototype.setFontFamily=function(t){this._fontFamily!==t&&(this._fontFamily=t,this.domNode.style.fontFamily=this._fontFamily)},t.prototype.setFontWeight=function(t){this._fontWeight!==t&&(this._fontWeight=t,this.domNode.style.fontWeight=this._fontWeight)},t.prototype.setFontSize=function(t){this._fontSize!==t&&(this._fontSize=t,this.domNode.style.fontSize=this._fontSize+"px")},t.prototype.setFontFeatureSettings=function(t){this._fontFeatureSettings!==t&&(this._fontFeatureSettings=t,this.domNode.style.fontFeatureSettings=this._fontFeatureSettings)},t.prototype.setLineHeight=function(t){this._lineHeight!==t&&(this._lineHeight=t,this.domNode.style.lineHeight=this._lineHeight+"px")},t.prototype.setLetterSpacing=function(t){this._letterSpacing!==t&&(this._letterSpacing=t,this.domNode.style.letterSpacing=this._letterSpacing+"px")},t.prototype.setClassName=function(t){this._className!==t&&(this._className=t,this.domNode.className=this._className)},t.prototype.toggleClassName=function(t,e){i["Y"](this.domNode,t,e),this._className=this.domNode.className},t.prototype.setDisplay=function(t){this._display!==t&&(this._display=t,this.domNode.style.display=this._display)},t.prototype.setPosition=function(t){this._position!==t&&(this._position=t,this.domNode.style.position=this._position)},t.prototype.setVisibility=function(t){this._visibility!==t&&(this._visibility=t,this.domNode.style.visibility=this._visibility)},t.prototype.setBackgroundColor=function(t){this._backgroundColor!==t&&(this._backgroundColor=t,this.domNode.style.backgroundColor=this._backgroundColor)},t.prototype.setLayerHinting=function(t){this._layerHint!==t&&(this._layerHint=t,this.domNode.style.transform=this._layerHint?"translate3d(0px, 0px, 0px)":"")},t.prototype.setContain=function(t){this._contain!==t&&(this._contain=t,this.domNode.style.contain=this._contain)},t.prototype.setAttribute=function(t,e){this.domNode.setAttribute(t,e)},t.prototype.removeAttribute=function(t){this.domNode.removeAttribute(t)},t.prototype.appendChild=function(t){this.domNode.appendChild(t.domNode)},t.prototype.removeChild=function(t){this.domNode.removeChild(t.domNode)},t}();function r(t){return new o(t)}},"72a7":function(t,e,n){"use strict";n.d(e,"a",(function(){return P})),n.d(e,"f",(function(){return M})),n.d(e,"e",(function(){return L})),n.d(e,"d",(function(){return W})),n.d(e,"b",(function(){return K})),n.d(e,"c",(function(){return $}));n("e2b8");var i,o=n("dff7"),r=n("a666"),s=n("ef8e"),a=n("e8e3"),u=n("6424"),l=n("11f7"),c=n("30db"),d=n("a60f"),h=n("b835"),p=n("308f"),f=n("e32d"),g=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},t(e,n)};return function(e,n){function i(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}();(function(t){t["TREE"]="tree",t["FORM"]="form"})(i||(i={}));var m=function(t){function e(e,n){return t.call(this,"ListError ["+e+"] "+n)||this}return g(e,t),e}(Error),v=n("7de1"),b=n("ceb8"),y=n("aa3d"),w=function(){function t(t){this.spliceables=t}return t.prototype.splice=function(t,e,n){this.spliceables.forEach((function(i){return i.splice(t,e,n)}))},t}(),_=n("49d9"),E=n("7e93"),C=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},t(e,n)};return function(e,n){function i(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),O=function(){return O=Object.assign||function(t){for(var e,n=1,i=arguments.length;n<i;n++)for(var o in e=arguments[n],e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},O.apply(this,arguments)},D=function(t,e,n,i){var o,r=arguments.length,s=r<3?e:null===i?i=Object.getOwnPropertyDescriptor(e,n):i;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)s=Reflect.decorate(t,e,n,i);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(r<3?o(s):r>3?o(e,n,s):o(e,n))||s);return r>3&&s&&Object.defineProperty(e,n,s),s},k=function(){for(var t=0,e=0,n=arguments.length;e<n;e++)t+=arguments[e].length;var i=Array(t),o=0;for(e=0;e<n;e++)for(var r=arguments[e],s=0,a=r.length;s<a;s++,o++)i[o]=r[s];return i},x=function(){function t(t){this.trait=t,this.renderedElements=[]}return Object.defineProperty(t.prototype,"templateId",{get:function(){return"template:"+this.trait.trait},enumerable:!0,configurable:!0}),t.prototype.renderTemplate=function(t){return t},t.prototype.renderElement=function(t,e,n){var i=Object(a["k"])(this.renderedElements,(function(t){return t.templateData===n}));if(i>=0){var o=this.renderedElements[i];this.trait.unrender(n),o.index=e}else{o={index:e,templateData:n};this.renderedElements.push(o)}this.trait.renderIndex(e,n)},t.prototype.splice=function(t,e,n){for(var i=[],o=0,r=this.renderedElements;o<r.length;o++){var s=r[o];s.index<t?i.push(s):s.index>=t+e&&i.push({index:s.index+n-e,templateData:s.templateData})}this.renderedElements=i},t.prototype.renderIndexes=function(t){for(var e=0,n=this.renderedElements;e<n.length;e++){var i=n[e],o=i.index,r=i.templateData;t.indexOf(o)>-1&&this.trait.renderIndex(o,r)}},t.prototype.disposeTemplate=function(t){var e=Object(a["k"])(this.renderedElements,(function(e){return e.templateData===t}));e<0||this.renderedElements.splice(e,1)},t}(),S=function(){function t(t){this._trait=t,this.indexes=[],this.sortedIndexes=[],this._onChange=new p["a"],this.onChange=this._onChange.event}return Object.defineProperty(t.prototype,"trait",{get:function(){return this._trait},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"renderer",{get:function(){return new x(this)},enumerable:!0,configurable:!0}),t.prototype.splice=function(t,e,n){var i=n.length-e,o=t+e,r=k(this.sortedIndexes.filter((function(e){return e<t})),n.map((function(e,n){return e?n+t:-1})).filter((function(t){return-1!==t})),this.sortedIndexes.filter((function(t){return t>=o})).map((function(t){return t+i})));this.renderer.splice(t,e,n.length),this._set(r,r)},t.prototype.renderIndex=function(t,e){l["Y"](e,this._trait,this.contains(t))},t.prototype.unrender=function(t){l["P"](t,this._trait)},t.prototype.set=function(t,e){return this._set(t,k(t).sort(G),e)},t.prototype._set=function(t,e,n){var i=this.indexes,o=this.sortedIndexes;this.indexes=t,this.sortedIndexes=e;var r=z(o,t);return this.renderer.renderIndexes(r),this._onChange.fire({indexes:t,browserEvent:n}),i},t.prototype.get=function(){return this.indexes},t.prototype.contains=function(t){return Object(a["c"])(this.sortedIndexes,t,G)>=0},t.prototype.dispose=function(){Object(r["f"])(this._onChange)},D([u["a"]],t.prototype,"renderer",null),t}(),N=function(t){function e(){return t.call(this,"focused")||this}return C(e,t),e.prototype.renderIndex=function(e,n){t.prototype.renderIndex.call(this,e,n),this.contains(e)?n.setAttribute("aria-selected","true"):n.removeAttribute("aria-selected")},e}(S),B=function(){function t(t,e,n){this.trait=t,this.view=e,this.identityProvider=n}return t.prototype.splice=function(t,e,n){var i=this;if(!this.identityProvider)return this.trait.splice(t,e,n.map((function(){return!1})));var o=this.trait.get().map((function(t){return i.identityProvider.getId(i.view.element(t)).toString()})),r=n.map((function(t){return o.indexOf(i.identityProvider.getId(t).toString())>-1}));this.trait.splice(t,e,r)},t}();function T(t){return"INPUT"===t.tagName||"TEXTAREA"===t.tagName}var A,I=function(){function t(t,e,n){this.list=t,this.view=e,this.disposables=new r["b"];var i=!1!==n.multipleSelectionSupport;this.openController=n.openController||H;var o=p["b"].chain(Object(f["a"])(e.domNode,"keydown")).filter((function(t){return!T(t.target)})).map((function(t){return new h["a"](t)}));o.filter((function(t){return 3===t.keyCode})).on(this.onEnter,this,this.disposables),o.filter((function(t){return 16===t.keyCode})).on(this.onUpArrow,this,this.disposables),o.filter((function(t){return 18===t.keyCode})).on(this.onDownArrow,this,this.disposables),o.filter((function(t){return 11===t.keyCode})).on(this.onPageUpArrow,this,this.disposables),o.filter((function(t){return 12===t.keyCode})).on(this.onPageDownArrow,this,this.disposables),o.filter((function(t){return 9===t.keyCode})).on(this.onEscape,this,this.disposables),i&&o.filter((function(t){return(c["e"]?t.metaKey:t.ctrlKey)&&31===t.keyCode})).on(this.onCtrlA,this,this.disposables)}return t.prototype.onEnter=function(t){t.preventDefault(),t.stopPropagation(),this.list.setSelection(this.list.getFocus(),t.browserEvent),this.openController.shouldOpen(t.browserEvent)&&this.list.open(this.list.getFocus(),t.browserEvent)},t.prototype.onUpArrow=function(t){t.preventDefault(),t.stopPropagation(),this.list.focusPrevious(1,!1,t.browserEvent),this.list.reveal(this.list.getFocus()[0]),this.view.domNode.focus()},t.prototype.onDownArrow=function(t){t.preventDefault(),t.stopPropagation(),this.list.focusNext(1,!1,t.browserEvent),this.list.reveal(this.list.getFocus()[0]),this.view.domNode.focus()},t.prototype.onPageUpArrow=function(t){t.preventDefault(),t.stopPropagation(),this.list.focusPreviousPage(t.browserEvent),this.list.reveal(this.list.getFocus()[0]),this.view.domNode.focus()},t.prototype.onPageDownArrow=function(t){t.preventDefault(),t.stopPropagation(),this.list.focusNextPage(t.browserEvent),this.list.reveal(this.list.getFocus()[0]),this.view.domNode.focus()},t.prototype.onCtrlA=function(t){t.preventDefault(),t.stopPropagation(),this.list.setSelection(Object(a["u"])(this.list.length),t.browserEvent),this.view.domNode.focus()},t.prototype.onEscape=function(t){t.preventDefault(),t.stopPropagation(),this.list.setSelection([],t.browserEvent),this.view.domNode.focus()},t.prototype.dispose=function(){this.disposables.dispose()},t}();(function(t){t[t["Idle"]=0]="Idle",t[t["Typing"]=1]="Typing"})(A||(A={}));var P=new(function(){function t(){}return t.prototype.mightProducePrintableCharacter=function(t){return!(t.ctrlKey||t.metaKey||t.altKey)&&(t.keyCode>=31&&t.keyCode<=56||t.keyCode>=21&&t.keyCode<=30||t.keyCode>=93&&t.keyCode<=102||t.keyCode>=80&&t.keyCode<=90)},t}()),F=function(){function t(t,e,n,i){this.list=t,this.view=e,this.keyboardNavigationLabelProvider=n,this.delegate=i,this.enabled=!1,this.state=A.Idle,this.automaticKeyboardNavigation=!0,this.triggered=!1,this.enabledDisposables=new r["b"],this.disposables=new r["b"],this.updateOptions(t.options)}return t.prototype.updateOptions=function(t){var e="undefined"===typeof t.enableKeyboardNavigation||!!t.enableKeyboardNavigation;e?this.enable():this.disable(),"undefined"!==typeof t.automaticKeyboardNavigation&&(this.automaticKeyboardNavigation=t.automaticKeyboardNavigation)},t.prototype.enable=function(){var t=this;if(!this.enabled){var e=p["b"].chain(Object(f["a"])(this.view.domNode,"keydown")).filter((function(t){return!T(t.target)})).filter((function(){return t.automaticKeyboardNavigation||t.triggered})).map((function(t){return new h["a"](t)})).filter((function(e){return t.delegate.mightProducePrintableCharacter(e)})).forEach((function(t){t.stopPropagation(),t.preventDefault()})).map((function(t){return t.browserEvent.key})).event,n=p["b"].debounce(e,(function(){return null}),800),i=p["b"].reduce(p["b"].any(e,n),(function(t,e){return null===e?null:(t||"")+e}));i(this.onInput,this,this.enabledDisposables),this.enabled=!0,this.triggered=!1}},t.prototype.disable=function(){this.enabled&&(this.enabledDisposables.clear(),this.enabled=!1,this.triggered=!1)},t.prototype.onInput=function(t){if(!t)return this.state=A.Idle,void(this.triggered=!1);var e=this.list.getFocus(),n=e.length>0?e[0]:0,i=this.state===A.Idle?1:0;this.state=A.Typing;for(var o=0;o<this.list.length;o++){var r=(n+o+i)%this.list.length,s=this.keyboardNavigationLabelProvider.getKeyboardNavigationLabel(this.view.element(r)),a=s&&s.toString();if("undefined"===typeof a||Object(E["g"])(t,a))return this.list.setFocus([r]),void this.list.reveal(r)}},t.prototype.dispose=function(){this.disable(),this.enabledDisposables.dispose(),this.disposables.dispose()},t}(),j=function(){function t(t,e){this.list=t,this.view=e,this.disposables=new r["b"];var n=p["b"].chain(Object(f["a"])(e.domNode,"keydown")).filter((function(t){return!T(t.target)})).map((function(t){return new h["a"](t)}));n.filter((function(t){return 2===t.keyCode&&!t.ctrlKey&&!t.metaKey&&!t.shiftKey&&!t.altKey})).on(this.onTab,this,this.disposables)}return t.prototype.onTab=function(t){if(t.target===this.view.domNode){var e=this.list.getFocus();if(0!==e.length){var n=this.view.domElement(e[0]);if(n){var i=n.querySelector("[tabIndex]");if(i&&i instanceof HTMLElement&&-1!==i.tabIndex){var o=window.getComputedStyle(i);"hidden"!==o.visibility&&"none"!==o.display&&(t.preventDefault(),t.stopPropagation(),i.focus())}}}}},t.prototype.dispose=function(){this.disposables.dispose()},t}();function M(t){return c["e"]?t.browserEvent.metaKey:t.browserEvent.ctrlKey}function L(t){return t.browserEvent.shiftKey}function V(t){return t instanceof MouseEvent&&2===t.button}var R={isSelectionSingleChangeEvent:M,isSelectionRangeChangeEvent:L},H={shouldOpen:function(t){return!(t instanceof MouseEvent)||!V(t)}},W=function(){function t(t){this.list=t,this.disposables=new r["b"],this.multipleSelectionSupport=!(!1===t.options.multipleSelectionSupport),this.multipleSelectionSupport&&(this.multipleSelectionController=t.options.multipleSelectionController||R),this.openController=t.options.openController||H,this.mouseSupport="undefined"===typeof t.options.mouseSupport||!!t.options.mouseSupport,this.mouseSupport&&(t.onMouseDown(this.onMouseDown,this,this.disposables),t.onContextMenu(this.onContextMenu,this,this.disposables),t.onMouseDblClick(this.onDoubleClick,this,this.disposables),t.onTouchStart(this.onMouseDown,this,this.disposables),this.disposables.add(d["b"].addTarget(t.getHTMLElement()))),t.onMouseClick(this.onPointer,this,this.disposables),t.onMouseMiddleClick(this.onPointer,this,this.disposables),t.onTap(this.onPointer,this,this.disposables)}return t.prototype.isSelectionSingleChangeEvent=function(t){return this.multipleSelectionController?this.multipleSelectionController.isSelectionSingleChangeEvent(t):c["e"]?t.browserEvent.metaKey:t.browserEvent.ctrlKey},t.prototype.isSelectionRangeChangeEvent=function(t){return this.multipleSelectionController?this.multipleSelectionController.isSelectionRangeChangeEvent(t):t.browserEvent.shiftKey},t.prototype.isSelectionChangeEvent=function(t){return this.isSelectionSingleChangeEvent(t)||this.isSelectionRangeChangeEvent(t)},t.prototype.onMouseDown=function(t){document.activeElement!==t.browserEvent.target&&this.list.domFocus()},t.prototype.onContextMenu=function(t){var e="undefined"===typeof t.index?[]:[t.index];this.list.setFocus(e,t.browserEvent)},t.prototype.onPointer=function(t){if(this.mouseSupport&&!T(t.browserEvent.target)){var e=this.list.getFocus()[0],n=this.list.getSelection();e=void 0===e?n[0]:e;var i=t.index;if("undefined"===typeof i)return this.list.setFocus([],t.browserEvent),void this.list.setSelection([],t.browserEvent);if(this.multipleSelectionSupport&&this.isSelectionRangeChangeEvent(t))return this.changeSelection(t,e);if(this.multipleSelectionSupport&&this.isSelectionChangeEvent(t))return this.changeSelection(t,e);this.list.setFocus([i],t.browserEvent),V(t.browserEvent)||(this.list.setSelection([i],t.browserEvent),this.openController.shouldOpen(t.browserEvent)&&this.list.open([i],t.browserEvent))}},t.prototype.onDoubleClick=function(t){if(!T(t.browserEvent.target)&&(!this.multipleSelectionSupport||!this.isSelectionChangeEvent(t))){var e=this.list.getFocus();this.list.setSelection(e,t.browserEvent),this.list.pin(e)}},t.prototype.changeSelection=function(t,e){var n=t.index;if(this.isSelectionRangeChangeEvent(t)&&void 0!==e){var i=Math.min(e,n),o=Math.max(e,n),r=Object(a["u"])(i,o+1),s=this.list.getSelection(),u=Y(z(s,[e]),e);if(0===u.length)return;var l=z(r,X(s,u));this.list.setSelection(l,t.browserEvent)}else if(this.isSelectionSingleChangeEvent(t)){s=this.list.getSelection(),l=s.filter((function(t){return t!==n}));this.list.setFocus([n]),s.length===l.length?this.list.setSelection(k(l,[n]),t.browserEvent):this.list.setSelection(l,t.browserEvent)}},t.prototype.dispose=function(){this.disposables.dispose()},t}(),K=function(){function t(t,e){this.styleElement=t,this.selectorSuffix=e}return t.prototype.style=function(t){var e=this.selectorSuffix&&"."+this.selectorSuffix,n=[];t.listBackground&&(t.listBackground.isOpaque()?n.push(".monaco-list"+e+" .monaco-list-rows { background: "+t.listBackground+"; }"):c["e"]||console.warn("List with id '"+this.selectorSuffix+"' was styled with a non-opaque background color. This will break sub-pixel antialiasing.")),t.listFocusBackground&&(n.push(".monaco-list"+e+":focus .monaco-list-row.focused { background-color: "+t.listFocusBackground+"; }"),n.push(".monaco-list"+e+":focus .monaco-list-row.focused:hover { background-color: "+t.listFocusBackground+"; }")),t.listFocusForeground&&n.push(".monaco-list"+e+":focus .monaco-list-row.focused { color: "+t.listFocusForeground+"; }"),t.listActiveSelectionBackground&&(n.push(".monaco-list"+e+":focus .monaco-list-row.selected { background-color: "+t.listActiveSelectionBackground+"; }"),n.push(".monaco-list"+e+":focus .monaco-list-row.selected:hover { background-color: "+t.listActiveSelectionBackground+"; }")),t.listActiveSelectionForeground&&n.push(".monaco-list"+e+":focus .monaco-list-row.selected { color: "+t.listActiveSelectionForeground+"; }"),t.listFocusAndSelectionBackground&&n.push("\n\t\t\t\t.monaco-drag-image,\n\t\t\t\t.monaco-list"+e+":focus .monaco-list-row.selected.focused { background-color: "+t.listFocusAndSelectionBackground+"; }\n\t\t\t"),t.listFocusAndSelectionForeground&&n.push("\n\t\t\t\t.monaco-drag-image,\n\t\t\t\t.monaco-list"+e+":focus .monaco-list-row.selected.focused { color: "+t.listFocusAndSelectionForeground+"; }\n\t\t\t"),t.listInactiveFocusBackground&&(n.push(".monaco-list"+e+" .monaco-list-row.focused { background-color:  "+t.listInactiveFocusBackground+"; }"),n.push(".monaco-list"+e+" .monaco-list-row.focused:hover { background-color:  "+t.listInactiveFocusBackground+"; }")),t.listInactiveSelectionBackground&&(n.push(".monaco-list"+e+" .monaco-list-row.selected { background-color:  "+t.listInactiveSelectionBackground+"; }"),n.push(".monaco-list"+e+" .monaco-list-row.selected:hover { background-color:  "+t.listInactiveSelectionBackground+"; }")),t.listInactiveSelectionForeground&&n.push(".monaco-list"+e+" .monaco-list-row.selected { color: "+t.listInactiveSelectionForeground+"; }"),t.listHoverBackground&&n.push(".monaco-list"+e+":not(.drop-target) .monaco-list-row:hover:not(.selected):not(.focused) { background-color:  "+t.listHoverBackground+"; }"),t.listHoverForeground&&n.push(".monaco-list"+e+" .monaco-list-row:hover:not(.selected):not(.focused) { color:  "+t.listHoverForeground+"; }"),t.listSelectionOutline&&n.push(".monaco-list"+e+" .monaco-list-row.selected { outline: 1px dotted "+t.listSelectionOutline+"; outline-offset: -1px; }"),t.listFocusOutline&&n.push("\n\t\t\t\t.monaco-drag-image,\n\t\t\t\t.monaco-list"+e+":focus .monaco-list-row.focused { outline: 1px solid "+t.listFocusOutline+"; outline-offset: -1px; }\n\t\t\t"),t.listInactiveFocusOutline&&n.push(".monaco-list"+e+" .monaco-list-row.focused { outline: 1px dotted "+t.listInactiveFocusOutline+"; outline-offset: -1px; }"),t.listHoverOutline&&n.push(".monaco-list"+e+" .monaco-list-row:hover { outline: 1px dashed "+t.listHoverOutline+"; outline-offset: -1px; }"),t.listDropBackground&&n.push("\n\t\t\t\t.monaco-list"+e+".drop-target,\n\t\t\t\t.monaco-list"+e+" .monaco-list-rows.drop-target,\n\t\t\t\t.monaco-list"+e+" .monaco-list-row.drop-target { background-color: "+t.listDropBackground+" !important; color: inherit !important; }\n\t\t\t"),t.listFilterWidgetBackground&&n.push(".monaco-list-type-filter { background-color: "+t.listFilterWidgetBackground+" }"),t.listFilterWidgetOutline&&n.push(".monaco-list-type-filter { border: 1px solid "+t.listFilterWidgetOutline+"; }"),t.listFilterWidgetNoMatchesOutline&&n.push(".monaco-list-type-filter.no-matches { border: 1px solid "+t.listFilterWidgetNoMatchesOutline+"; }"),t.listMatchesShadow&&n.push(".monaco-list-type-filter { box-shadow: 1px 1px 1px "+t.listMatchesShadow+"; }");var i=n.join("\n");i!==this.styleElement.innerHTML&&(this.styleElement.innerHTML=i)},t}(),U={listFocusBackground:b["a"].fromHex("#7FB0D0"),listActiveSelectionBackground:b["a"].fromHex("#0E639C"),listActiveSelectionForeground:b["a"].fromHex("#FFFFFF"),listFocusAndSelectionBackground:b["a"].fromHex("#094771"),listFocusAndSelectionForeground:b["a"].fromHex("#FFFFFF"),listInactiveSelectionBackground:b["a"].fromHex("#3F3F46"),listHoverBackground:b["a"].fromHex("#2A2D2E"),listDropBackground:b["a"].fromHex("#383B3D"),treeIndentGuidesStroke:b["a"].fromHex("#a9a9a9")},q={keyboardSupport:!0,mouseSupport:!0,multipleSelectionSupport:!0,dnd:{getDragURI:function(){return null},onDragStart:function(){},onDragOver:function(){return!1},drop:function(){}},ariaRootRole:i.TREE};function Y(t,e){var n=t.indexOf(e);if(-1===n)return[];var i=[],o=n-1;while(o>=0&&t[o]===e-(n-o))i.push(t[o--]);i.reverse(),o=n;while(o<t.length&&t[o]===e+(o-n))i.push(t[o++]);return i}function z(t,e){var n=[],i=0,o=0;while(i<t.length||o<e.length)if(i>=t.length)n.push(e[o++]);else if(o>=e.length)n.push(t[i++]);else{if(t[i]===e[o]){n.push(t[i]),i++,o++;continue}t[i]<e[o]?n.push(t[i++]):n.push(e[o++])}return n}function X(t,e){var n=[],i=0,o=0;while(i<t.length||o<e.length)if(i>=t.length)n.push(e[o++]);else if(o>=e.length)n.push(t[i++]);else{if(t[i]===e[o]){i++,o++;continue}t[i]<e[o]?n.push(t[i++]):o++}return n}var G=function(t,e){return t-e},Z=function(){function t(t,e){this._templateId=t,this.renderers=e}return Object.defineProperty(t.prototype,"templateId",{get:function(){return this._templateId},enumerable:!0,configurable:!0}),t.prototype.renderTemplate=function(t){return this.renderers.map((function(e){return e.renderTemplate(t)}))},t.prototype.renderElement=function(t,e,n,i){for(var o=0,r=0,s=this.renderers;r<s.length;r++){var a=s[r];a.renderElement(t,e,n[o++],i)}},t.prototype.disposeElement=function(t,e,n,i){for(var o=0,r=0,s=this.renderers;r<s.length;r++){var a=s[r];a.disposeElement&&a.disposeElement(t,e,n[o],i),o+=1}},t.prototype.disposeTemplate=function(t){for(var e=0,n=0,i=this.renderers;n<i.length;n++){var o=i[n];o.disposeTemplate(t[e++])}},t}(),J=function(){function t(t){this.accessibilityProvider=t,this.templateId="a18n"}return t.prototype.renderTemplate=function(t){return t},t.prototype.renderElement=function(t,e,n){var i=this.accessibilityProvider.getAriaLabel(t);i?n.setAttribute("aria-label",i):n.removeAttribute("aria-label");var o=this.accessibilityProvider.getAriaLevel&&this.accessibilityProvider.getAriaLevel(t);"number"===typeof o?n.setAttribute("aria-level",""+o):n.removeAttribute("aria-level")},t.prototype.disposeTemplate=function(t){},t}(),Q=function(){function t(t,e){this.list=t,this.dnd=e}return t.prototype.getDragElements=function(t){var e=this.list.getSelectedElements(),n=e.indexOf(t)>-1?e:[t];return n},t.prototype.getDragURI=function(t){return this.dnd.getDragURI(t)},t.prototype.getDragLabel=function(t,e){if(this.dnd.getDragLabel)return this.dnd.getDragLabel(t,e)},t.prototype.onDragStart=function(t,e){this.dnd.onDragStart&&this.dnd.onDragStart(t,e)},t.prototype.onDragOver=function(t,e,n,i){return this.dnd.onDragOver(t,e,n,i)},t.prototype.onDragEnd=function(t){this.dnd.onDragEnd&&this.dnd.onDragEnd(t)},t.prototype.drop=function(t,e,n,i){this.dnd.drop(t,e,n,i)},t}(),$=function(){function t(t,e,n,s,a){void 0===a&&(a=q),this.user=t,this._options=a,this.eventBufferer=new p["c"],this.disposables=new r["b"],this._onDidOpen=new p["a"],this.onDidOpen=this._onDidOpen.event,this._onDidPin=new p["a"],this.didJustPressContextMenuKey=!1,this._onDidDispose=new p["a"],this.onDidDispose=this._onDidDispose.event,this.focus=new N,this.selection=new S("selected"),Object(y["g"])(a,U,!1);var u=[this.focus.renderer,this.selection.renderer];this.accessibilityProvider=a.accessibilityProvider,this.accessibilityProvider&&(u.push(new J(this.accessibilityProvider)),this.accessibilityProvider.onDidChangeActiveDescendant&&this.accessibilityProvider.onDidChangeActiveDescendant(this.onDidChangeActiveDescendant,this,this.disposables)),s=s.map((function(t){return new Z(t.templateId,k(u,[t]))}));var c=O(O({},a),{dnd:a.dnd&&new Q(this,a.dnd)});if(this.view=new v["b"](e,n,s,c),"string"!==typeof a.ariaRole?this.view.domNode.setAttribute("role",i.TREE):this.view.domNode.setAttribute("role",a.ariaRole),a.styleController)this.styleController=a.styleController(this.view.domId);else{var d=l["w"](this.view.domNode);this.styleController=new K(d,this.view.domId)}if(this.spliceable=new w([new B(this.focus,this.view,a.identityProvider),new B(this.selection,this.view,a.identityProvider),this.view]),this.disposables.add(this.focus),this.disposables.add(this.selection),this.disposables.add(this.view),this.disposables.add(this._onDidDispose),this.onDidFocus=p["b"].map(Object(f["a"])(this.view.domNode,"focus",!0),(function(){return null})),this.onDidBlur=p["b"].map(Object(f["a"])(this.view.domNode,"blur",!0),(function(){return null})),this.disposables.add(new j(this,this.view)),"boolean"!==typeof a.keyboardSupport||a.keyboardSupport){var h=new I(this,this.view,a);this.disposables.add(h)}if(a.keyboardNavigationLabelProvider){var g=a.keyboardNavigationDelegate||P;this.typeLabelController=new F(this,this.view,a.keyboardNavigationLabelProvider,g),this.disposables.add(this.typeLabelController)}this.disposables.add(this.createMouseController(a)),this.onFocusChange(this._onFocusChange,this,this.disposables),this.onSelectionChange(this._onSelectionChange,this,this.disposables),a.ariaLabel&&this.view.domNode.setAttribute("aria-label",Object(o["a"])("aria list","{0}. Use the navigation keys to navigate.",a.ariaLabel))}return Object.defineProperty(t.prototype,"onFocusChange",{get:function(){var t=this;return p["b"].map(this.eventBufferer.wrapEvent(this.focus.onChange),(function(e){return t.toListEvent(e)}))},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"onSelectionChange",{get:function(){var t=this;return p["b"].map(this.eventBufferer.wrapEvent(this.selection.onChange),(function(e){return t.toListEvent(e)}))},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"domId",{get:function(){return this.view.domId},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"onMouseClick",{get:function(){return this.view.onMouseClick},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"onMouseDblClick",{get:function(){return this.view.onMouseDblClick},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"onMouseMiddleClick",{get:function(){return this.view.onMouseMiddleClick},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"onMouseDown",{get:function(){return this.view.onMouseDown},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"onTouchStart",{get:function(){return this.view.onTouchStart},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"onTap",{get:function(){return this.view.onTap},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"onContextMenu",{get:function(){var t=this,e=p["b"].chain(Object(f["a"])(this.view.domNode,"keydown")).map((function(t){return new h["a"](t)})).filter((function(e){return t.didJustPressContextMenuKey=58===e.keyCode||e.shiftKey&&68===e.keyCode})).filter((function(t){return t.preventDefault(),t.stopPropagation(),!1})).event,n=p["b"].chain(Object(f["a"])(this.view.domNode,"keyup")).filter((function(){var e=t.didJustPressContextMenuKey;return t.didJustPressContextMenuKey=!1,e})).filter((function(){return t.getFocus().length>0&&!!t.view.domElement(t.getFocus()[0])})).map((function(e){var n=t.getFocus()[0],i=t.view.element(n),o=t.view.domElement(n);return{index:n,element:i,anchor:o,browserEvent:e}})).event,i=p["b"].chain(this.view.onContextMenu).filter((function(){return!t.didJustPressContextMenuKey})).map((function(t){var e=t.element,n=t.index,i=t.browserEvent;return{element:e,index:n,anchor:{x:i.clientX+1,y:i.clientY},browserEvent:i}})).event;return p["b"].any(e,n,i)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"onKeyDown",{get:function(){return Object(f["a"])(this.view.domNode,"keydown")},enumerable:!0,configurable:!0}),t.prototype.createMouseController=function(t){return new W(this)},t.prototype.updateOptions=function(t){void 0===t&&(t={}),this._options=O(O({},this._options),t),this.typeLabelController&&this.typeLabelController.updateOptions(this._options)},Object.defineProperty(t.prototype,"options",{get:function(){return this._options},enumerable:!0,configurable:!0}),t.prototype.splice=function(t,e,n){var i=this;if(void 0===n&&(n=[]),t<0||t>this.view.length)throw new m(this.user,"Invalid start index: "+t);if(e<0)throw new m(this.user,"Invalid delete count: "+e);0===e&&0===n.length||this.eventBufferer.bufferEvents((function(){return i.spliceable.splice(t,e,n)}))},t.prototype.rerender=function(){this.view.rerender()},t.prototype.element=function(t){return this.view.element(t)},Object.defineProperty(t.prototype,"length",{get:function(){return this.view.length},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"contentHeight",{get:function(){return this.view.contentHeight},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"scrollTop",{get:function(){return this.view.getScrollTop()},set:function(t){this.view.setScrollTop(t)},enumerable:!0,configurable:!0}),t.prototype.domFocus=function(){this.view.domNode.focus()},t.prototype.layout=function(t,e){this.view.layout(t,e)},t.prototype.setSelection=function(t,e){for(var n=0,i=t;n<i.length;n++){var o=i[n];if(o<0||o>=this.length)throw new m(this.user,"Invalid index "+o)}this.selection.set(t,e)},t.prototype.getSelection=function(){return this.selection.get()},t.prototype.getSelectedElements=function(){var t=this;return this.getSelection().map((function(e){return t.view.element(e)}))},t.prototype.setFocus=function(t,e){for(var n=0,i=t;n<i.length;n++){var o=i[n];if(o<0||o>=this.length)throw new m(this.user,"Invalid index "+o)}this.focus.set(t,e)},t.prototype.focusNext=function(t,e,n,i){if(void 0===t&&(t=1),void 0===e&&(e=!1),0!==this.length){var o=this.focus.get(),r=this.findNextIndex(o.length>0?o[0]+t:0,e,i);r>-1&&this.setFocus([r],n)}},t.prototype.focusPrevious=function(t,e,n,i){if(void 0===t&&(t=1),void 0===e&&(e=!1),0!==this.length){var o=this.focus.get(),r=this.findPreviousIndex(o.length>0?o[0]-t:0,e,i);r>-1&&this.setFocus([r],n)}},t.prototype.focusNextPage=function(t,e){var n=this,i=this.view.indexAt(this.view.getScrollTop()+this.view.renderHeight);i=0===i?0:i-1;var o=this.view.element(i),r=this.getFocusedElements()[0];if(r!==o){var s=this.findPreviousIndex(i,!1,e);s>-1&&r!==this.view.element(s)?this.setFocus([s],t):this.setFocus([i],t)}else{var a=this.view.getScrollTop();this.view.setScrollTop(a+this.view.renderHeight-this.view.elementHeight(i)),this.view.getScrollTop()!==a&&setTimeout((function(){return n.focusNextPage(t,e)}),0)}},t.prototype.focusPreviousPage=function(t,e){var n,i=this,o=this.view.getScrollTop();n=0===o?this.view.indexAt(o):this.view.indexAfter(o-1);var r=this.view.element(n),s=this.getFocusedElements()[0];if(s!==r){var a=this.findNextIndex(n,!1,e);a>-1&&s!==this.view.element(a)?this.setFocus([a],t):this.setFocus([n],t)}else{var u=o;this.view.setScrollTop(o-this.view.renderHeight),this.view.getScrollTop()!==u&&setTimeout((function(){return i.focusPreviousPage(t,e)}),0)}},t.prototype.focusLast=function(t,e){if(0!==this.length){var n=this.findPreviousIndex(this.length-1,!1,e);n>-1&&this.setFocus([n],t)}},t.prototype.focusFirst=function(t,e){if(0!==this.length){var n=this.findNextIndex(0,!1,e);n>-1&&this.setFocus([n],t)}},t.prototype.findNextIndex=function(t,e,n){void 0===e&&(e=!1);for(var i=0;i<this.length;i++){if(t>=this.length&&!e)return-1;if(t%=this.length,!n||n(this.element(t)))return t;t++}return-1},t.prototype.findPreviousIndex=function(t,e,n){void 0===e&&(e=!1);for(var i=0;i<this.length;i++){if(t<0&&!e)return-1;if(t=(this.length+t%this.length)%this.length,!n||n(this.element(t)))return t;t--}return-1},t.prototype.getFocus=function(){return this.focus.get()},t.prototype.getFocusedElements=function(){var t=this;return this.getFocus().map((function(e){return t.view.element(e)}))},t.prototype.reveal=function(t,e){if(t<0||t>=this.length)throw new m(this.user,"Invalid index "+t);var n=this.view.getScrollTop(),i=this.view.elementTop(t),o=this.view.elementHeight(t);if(Object(s["h"])(e)){var r=o-this.view.renderHeight;this.view.setScrollTop(r*Object(_["a"])(e,0,1)+i)}else{var a=i+o,u=n+this.view.renderHeight;i<n&&a>=u||(i<n?this.view.setScrollTop(i):a>=u&&this.view.setScrollTop(a-this.view.renderHeight))}},t.prototype.getRelativeTop=function(t){if(t<0||t>=this.length)throw new m(this.user,"Invalid index "+t);var e=this.view.getScrollTop(),n=this.view.elementTop(t),i=this.view.elementHeight(t);if(n<e||n+i>e+this.view.renderHeight)return null;var o=i-this.view.renderHeight;return Math.abs((e-n)/o)},t.prototype.getHTMLElement=function(){return this.view.domNode},t.prototype.open=function(t,e){for(var n=this,i=0,o=t;i<o.length;i++){var r=o[i];if(r<0||r>=this.length)throw new m(this.user,"Invalid index "+r)}this._onDidOpen.fire({indexes:t,elements:t.map((function(t){return n.view.element(t)})),browserEvent:e})},t.prototype.pin=function(t,e){for(var n=this,i=0,o=t;i<o.length;i++){var r=o[i];if(r<0||r>=this.length)throw new m(this.user,"Invalid index "+r)}this._onDidPin.fire({indexes:t,elements:t.map((function(t){return n.view.element(t)})),browserEvent:e})},t.prototype.style=function(t){this.styleController.style(t)},t.prototype.toListEvent=function(t){var e=this,n=t.indexes,i=t.browserEvent;return{indexes:n,elements:n.map((function(t){return e.view.element(t)})),browserEvent:i}},t.prototype._onFocusChange=function(){var t=this.focus.get();l["Y"](this.view.domNode,"element-focused",t.length>0),this.onDidChangeActiveDescendant()},t.prototype.onDidChangeActiveDescendant=function(){var t,e=this.focus.get();if(e.length>0){var n=void 0;(null===(t=this.accessibilityProvider)||void 0===t?void 0:t.getActiveDescendantId)&&(n=this.accessibilityProvider.getActiveDescendantId(this.view.element(e[0]))),this.view.domNode.setAttribute("aria-activedescendant",n||this.view.getElementDomId(e[0]))}else this.view.domNode.removeAttribute("aria-activedescendant")},t.prototype._onSelectionChange=function(){var t=this.selection.get();l["Y"](this.view.domNode,"selection-none",0===t.length),l["Y"](this.view.domNode,"selection-single",1===t.length),l["Y"](this.view.domNode,"selection-multiple",t.length>1)},t.prototype.dispose=function(){this._onDidDispose.fire(),this.disposables.dispose(),this._onDidOpen.dispose(),this._onDidPin.dispose(),this._onDidDispose.dispose()},D([u["a"]],t.prototype,"onFocusChange",null),D([u["a"]],t.prototype,"onSelectionChange",null),D([u["a"]],t.prototype,"onContextMenu",null),t}()},"7a99":function(t,e,n){},"7de1":function(t,e,n){"use strict";n.d(e,"a",(function(){return k})),n.d(e,"b",(function(){return B}));var i=n("aa3d"),o=n("a666"),r=n("a60f"),s=n("11f7"),a=n("308f"),u=n("e32d"),l=n("1898"),c=n("9ee1");function d(t,e){for(var n=[],i=0,o=e;i<o.length;i++){var r=o[i];if(!(t.start>=r.range.end)){if(t.end<r.range.start)break;var s=c["a"].intersect(t,r.range);c["a"].isEmpty(s)||n.push({range:s,size:r.size})}}return n}function h(t,e){var n=t.start,i=t.end;return{start:n+e,end:i+e}}function p(t){for(var e=[],n=null,i=0,o=t;i<o.length;i++){var r=o[i],s=r.range.start,a=r.range.end,u=r.size;n&&u===n.size?n.range.end=a:(n={range:{start:s,end:a},size:u},e.push(n))}return e}function f(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return p(t.reduce((function(t,e){return t.concat(e)}),[]))}var g=function(){function t(){this.groups=[],this._size=0}return t.prototype.splice=function(t,e,n){void 0===n&&(n=[]);var i=n.length-e,o=d({start:0,end:t},this.groups),r=d({start:t+e,end:Number.POSITIVE_INFINITY},this.groups).map((function(t){return{range:h(t.range,i),size:t.size}})),s=n.map((function(e,n){return{range:{start:t+n,end:t+n+1},size:e.size}}));this.groups=f(o,s,r),this._size=this.groups.reduce((function(t,e){return t+e.size*(e.range.end-e.range.start)}),0)},Object.defineProperty(t.prototype,"count",{get:function(){var t=this.groups.length;return t?this.groups[t-1].range.end:0},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"size",{get:function(){return this._size},enumerable:!0,configurable:!0}),t.prototype.indexAt=function(t){if(t<0)return-1;for(var e=0,n=0,i=0,o=this.groups;i<o.length;i++){var r=o[i],s=r.range.end-r.range.start,a=n+s*r.size;if(t<a)return e+Math.floor((t-n)/r.size);e+=s,n=a}return e},t.prototype.indexAfter=function(t){return Math.min(this.indexAt(t)+1,this.count)},t.prototype.positionAt=function(t){if(t<0)return-1;for(var e=0,n=0,i=0,o=this.groups;i<o.length;i++){var r=o[i],s=r.range.end-r.range.start,a=n+s;if(t<a)return e+(t-n)*r.size;e+=s*r.size,n=a}return-1},t}();function m(t){try{t.parentElement&&t.parentElement.removeChild(t)}catch(e){}}var v=function(){function t(t){this.renderers=t,this.cache=new Map}return t.prototype.alloc=function(t){var e=this.getTemplateCache(t).pop();if(!e){var n=Object(s["a"])(".monaco-list-row"),i=this.getRenderer(t),o=i.renderTemplate(n);e={domNode:n,templateId:t,templateData:o}}return e},t.prototype.release=function(t){t&&this.releaseRow(t)},t.prototype.releaseRow=function(t){var e=t.domNode,n=t.templateId;e&&(Object(s["P"])(e,"scrolling"),m(e));var i=this.getTemplateCache(n);i.push(t)},t.prototype.getTemplateCache=function(t){var e=this.cache.get(t);return e||(e=[],this.cache.set(t,e)),e},t.prototype.dispose=function(){var t=this;this.cache.forEach((function(e,n){for(var i=0,o=e;i<o.length;i++){var r=o[i],s=t.getRenderer(n);s.disposeTemplate(r.templateData),r.domNode=null,r.templateData=null}})),this.cache.clear()},t.prototype.getRenderer=function(t){var e=this.renderers.get(t);if(!e)throw new Error("No renderer found for "+t);return e},t}(),b=n("6424"),y=n("e8e3"),w=n("650e"),_=n("5fe7"),E=n("0f70"),C=function(t,e,n,i){var o,r=arguments.length,s=r<3?e:null===i?i=Object.getOwnPropertyDescriptor(e,n):i;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)s=Reflect.decorate(t,e,n,i);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(r<3?o(s):r>3?o(e,n,s):o(e,n))||s);return r>3&&s&&Object.defineProperty(e,n,s),s},O=function(){for(var t=0,e=0,n=arguments.length;e<n;e++)t+=arguments[e].length;var i=Array(t),o=0;for(e=0;e<n;e++)for(var r=arguments[e],s=0,a=r.length;s<a;s++,o++)i[o]=r[s];return i},D={useShadows:!0,verticalScrollMode:1,setRowLineHeight:!0,supportDynamicHeights:!1,dnd:{getDragElements:function(t){return[t]},getDragURI:function(){return null},onDragStart:function(){},onDragOver:function(){return!1},drop:function(){}},horizontalScrolling:!1},k=function(){function t(t){this.elements=t}return t.prototype.update=function(){},t.prototype.getData=function(){return this.elements},t}(),x=function(){function t(t){this.elements=t}return t.prototype.update=function(){},t.prototype.getData=function(){return this.elements},t}(),S=function(){function t(){this.types=[],this.files=[]}return t.prototype.update=function(t){var e;if(t.types&&(e=this.types).splice.apply(e,O([0,this.types.length],t.types)),t.files){this.files.splice(0,this.files.length);for(var n=0;n<t.files.length;n++){var i=t.files.item(n);i&&(i.size||i.type)&&this.files.push(i)}}},t.prototype.getData=function(){return{types:this.types,files:this.files}},t}();function N(t,e){return Array.isArray(t)&&Array.isArray(e)?Object(y["g"])(t,e):t===e}var B=function(){function t(e,n,c,d){var h=this;if(void 0===d&&(d=D),this.virtualDelegate=n,this.domId="list_id_"+ ++t.InstanceCount,this.renderers=new Map,this.renderWidth=0,this._scrollHeight=0,this.scrollableElementUpdateDisposable=null,this.scrollableElementWidthDelayer=new _["a"](50),this.splicing=!1,this.dragOverAnimationStopDisposable=o["a"].None,this.dragOverMouseY=0,this.canDrop=!1,this.currentDragFeedbackDisposable=o["a"].None,this.onDragLeaveTimeout=o["a"].None,this.disposables=new o["b"],this._onDidChangeContentHeight=new a["a"],d.horizontalScrolling&&d.supportDynamicHeights)throw new Error("Horizontal scrolling and dynamic heights not supported simultaneously");this.items=[],this.itemId=0,this.rangeMap=new g;for(var p=0,f=c;p<f.length;p++){var m=f[p];this.renderers.set(m.templateId,m)}this.cache=this.disposables.add(new v(this.renderers)),this.lastRenderTop=0,this.lastRenderHeight=0,this.domNode=document.createElement("div"),this.domNode.className="monaco-list",s["f"](this.domNode,this.domId),this.domNode.tabIndex=0,s["Y"](this.domNode,"mouse-support","boolean"!==typeof d.mouseSupport||d.mouseSupport),this.horizontalScrolling=Object(i["f"])(d,(function(t){return t.horizontalScrolling}),D.horizontalScrolling),s["Y"](this.domNode,"horizontal-scrolling",this.horizontalScrolling),this.additionalScrollHeight="undefined"===typeof d.additionalScrollHeight?0:d.additionalScrollHeight,this.ariaProvider=d.ariaProvider||{getSetSize:function(t,e,n){return n},getPosInSet:function(t,e){return e+1}},this.rowsContainer=document.createElement("div"),this.rowsContainer.className="monaco-list-rows",this.rowsContainer.style.transform="translate3d(0px, 0px, 0px)",this.disposables.add(r["b"].addTarget(this.rowsContainer)),this.scrollableElement=this.disposables.add(new l["b"](this.rowsContainer,{alwaysConsumeMouseWheel:!0,horizontal:this.horizontalScrolling?1:2,vertical:Object(i["f"])(d,(function(t){return t.verticalScrollMode}),D.verticalScrollMode),useShadows:Object(i["f"])(d,(function(t){return t.useShadows}),D.useShadows)})),this.domNode.appendChild(this.scrollableElement.getDomNode()),e.appendChild(this.domNode),this.scrollableElement.onScroll(this.onScroll,this,this.disposables),Object(u["a"])(this.rowsContainer,r["a"].Change)(this.onTouchChange,this,this.disposables),Object(u["a"])(this.scrollableElement.getDomNode(),"scroll")((function(t){return t.target.scrollTop=0}),null,this.disposables),a["b"].map(Object(u["a"])(this.domNode,"dragover"),(function(t){return h.toDragEvent(t)}))(this.onDragOver,this,this.disposables),a["b"].map(Object(u["a"])(this.domNode,"drop"),(function(t){return h.toDragEvent(t)}))(this.onDrop,this,this.disposables),Object(u["a"])(this.domNode,"dragleave")(this.onDragLeave,this,this.disposables),Object(u["a"])(window,"dragend")(this.onDragEnd,this,this.disposables),this.setRowLineHeight=Object(i["f"])(d,(function(t){return t.setRowLineHeight}),D.setRowLineHeight),this.supportDynamicHeights=Object(i["f"])(d,(function(t){return t.supportDynamicHeights}),D.supportDynamicHeights),this.dnd=Object(i["f"])(d,(function(t){return t.dnd}),D.dnd),this.layout()}return Object.defineProperty(t.prototype,"contentHeight",{get:function(){return this.rangeMap.size},enumerable:!0,configurable:!0}),t.prototype.splice=function(t,e,n){if(void 0===n&&(n=[]),this.splicing)throw new Error("Can't run recursive splices.");this.splicing=!0;try{return this._splice(t,e,n)}finally{this.splicing=!1,this._onDidChangeContentHeight.fire(this.contentHeight)}},t.prototype._splice=function(t,e,n){var i,r=this;void 0===n&&(n=[]);for(var s=this.getRenderRange(this.lastRenderTop,this.lastRenderHeight),a={start:t,end:t+e},u=c["a"].intersect(s,a),l=u.start;l<u.end;l++)this.removeItemFromDOM(l);var d,p={start:t+e,end:this.items.length},f=c["a"].intersect(p,s),m=c["a"].relativeComplement(p,s),v=n.map((function(t){return{id:String(r.itemId++),element:t,templateId:r.virtualDelegate.getTemplateId(t),size:r.virtualDelegate.getHeight(t),width:void 0,hasDynamicHeight:!!r.virtualDelegate.hasDynamicHeight&&r.virtualDelegate.hasDynamicHeight(t),lastDynamicHeightWidth:void 0,row:null,uri:void 0,dropTarget:!1,dragStartDisposable:o["a"].None}}));0===t&&e>=this.items.length?(this.rangeMap=new g,this.rangeMap.splice(0,0,v),this.items=v,d=[]):(this.rangeMap.splice(t,e,v),d=(i=this.items).splice.apply(i,O([t,e],v)));var b=n.length-e,y=this.getRenderRange(this.lastRenderTop,this.lastRenderHeight),w=h(f,b),_=c["a"].intersect(y,w);for(l=_.start;l<_.end;l++)this.updateItemInDOM(this.items[l],l);for(var E=c["a"].relativeComplement(w,y),C=0,D=E;C<D.length;C++){var k=D[C];for(l=k.start;l<k.end;l++)this.removeItemFromDOM(l)}for(var x=m.map((function(t){return h(t,b)})),S={start:t,end:t+n.length},N=O([S],x).map((function(t){return c["a"].intersect(y,t)})),B=this.getNextToLastElement(N),T=0,A=N;T<A.length;T++)for(k=A[T],l=k.start;l<k.end;l++)this.insertItemInDOM(l,B);return this.eventuallyUpdateScrollDimensions(),this.supportDynamicHeights&&this._rerender(this.scrollTop,this.renderHeight),d.map((function(t){return t.element}))},t.prototype.eventuallyUpdateScrollDimensions=function(){var t=this;this._scrollHeight=this.contentHeight,this.rowsContainer.style.height=this._scrollHeight+"px",this.scrollableElementUpdateDisposable||(this.scrollableElementUpdateDisposable=s["W"]((function(){t.scrollableElement.setScrollDimensions({scrollHeight:t.scrollHeight}),t.updateScrollWidth(),t.scrollableElementUpdateDisposable=null})))},t.prototype.eventuallyUpdateScrollWidth=function(){var t=this;this.horizontalScrolling&&this.scrollableElementWidthDelayer.trigger((function(){return t.updateScrollWidth()}))},t.prototype.updateScrollWidth=function(){if(this.horizontalScrolling){0===this.items.length&&this.scrollableElement.setScrollDimensions({scrollWidth:0});for(var t=0,e=0,n=this.items;e<n.length;e++){var i=n[e];"undefined"!==typeof i.width&&(t=Math.max(t,i.width))}this.scrollWidth=t,this.scrollableElement.setScrollDimensions({scrollWidth:t+10})}},t.prototype.rerender=function(){if(this.supportDynamicHeights){for(var t=0,e=this.items;t<e.length;t++){var n=e[t];n.lastDynamicHeightWidth=void 0}this._rerender(this.lastRenderTop,this.lastRenderHeight)}},Object.defineProperty(t.prototype,"length",{get:function(){return this.items.length},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"renderHeight",{get:function(){var t=this.scrollableElement.getScrollDimensions();return t.height},enumerable:!0,configurable:!0}),t.prototype.element=function(t){return this.items[t].element},t.prototype.domElement=function(t){var e=this.items[t].row;return e&&e.domNode},t.prototype.elementHeight=function(t){return this.items[t].size},t.prototype.elementTop=function(t){return this.rangeMap.positionAt(t)},t.prototype.indexAt=function(t){return this.rangeMap.indexAt(t)},t.prototype.indexAfter=function(t){return this.rangeMap.indexAfter(t)},t.prototype.layout=function(t,e){var n={height:"number"===typeof t?t:s["A"](this.domNode)};this.scrollableElementUpdateDisposable&&(this.scrollableElementUpdateDisposable.dispose(),this.scrollableElementUpdateDisposable=null,n.scrollHeight=this.scrollHeight),this.scrollableElement.setScrollDimensions(n),"undefined"!==typeof e&&(this.renderWidth=e,this.supportDynamicHeights&&this._rerender(this.scrollTop,this.renderHeight),this.horizontalScrolling&&this.scrollableElement.setScrollDimensions({width:"number"===typeof e?e:s["B"](this.domNode)}))},t.prototype.render=function(t,e,n,i){for(var o=this.getRenderRange(this.lastRenderTop,this.lastRenderHeight),r=this.getRenderRange(t,e),s=c["a"].relativeComplement(r,o),a=c["a"].relativeComplement(o,r),u=this.getNextToLastElement(s),l=0,d=s;l<d.length;l++)for(var h=d[l],p=h.start;p<h.end;p++)this.insertItemInDOM(p,u);for(var f=0,g=a;f<g.length;f++)for(h=g[f],p=h.start;p<h.end;p++)this.removeItemFromDOM(p);this.rowsContainer.style.left="-"+n+"px",this.rowsContainer.style.top="-"+t+"px",this.horizontalScrolling&&(this.rowsContainer.style.width=Math.max(i,this.renderWidth)+"px"),this.lastRenderTop=t,this.lastRenderHeight=e},t.prototype.insertItemInDOM=function(t,e){var n=this,i=this.items[t];if(!i.row){i.row=this.cache.alloc(i.templateId);var o=this.ariaProvider.getRole?this.ariaProvider.getRole(i.element):"treeitem";i.row.domNode.setAttribute("role",o);var r=this.ariaProvider.isChecked?this.ariaProvider.isChecked(i.element):void 0;"undefined"!==typeof r&&i.row.domNode.setAttribute("aria-checked",String(r))}i.row.domNode.parentElement||(e?this.rowsContainer.insertBefore(i.row.domNode,e):this.rowsContainer.appendChild(i.row.domNode)),this.updateItemInDOM(i,t);var s=this.renderers.get(i.templateId);if(!s)throw new Error("No renderer found for template id "+i.templateId);s&&s.renderElement(i.element,t,i.row.templateData,i.size);var a=this.dnd.getDragURI(i.element);if(i.dragStartDisposable.dispose(),i.row.domNode.draggable=!!a,a){var l=Object(u["a"])(i.row.domNode,"dragstart");i.dragStartDisposable=l((function(t){return n.onDragStart(i.element,a,t)}))}this.horizontalScrolling&&(this.measureItemWidth(i),this.eventuallyUpdateScrollWidth())},t.prototype.measureItemWidth=function(t){if(t.row&&t.row.domNode){t.row.domNode.style.width=E["h"]?"-moz-fit-content":"fit-content",t.width=s["B"](t.row.domNode);var e=window.getComputedStyle(t.row.domNode);e.paddingLeft&&(t.width+=parseFloat(e.paddingLeft)),e.paddingRight&&(t.width+=parseFloat(e.paddingRight)),t.row.domNode.style.width=""}},t.prototype.updateItemInDOM=function(t,e){t.row.domNode.style.top=this.elementTop(e)+"px",t.row.domNode.style.height=t.size+"px",this.setRowLineHeight&&(t.row.domNode.style.lineHeight=t.size+"px"),t.row.domNode.setAttribute("data-index",""+e),t.row.domNode.setAttribute("data-last-element",e===this.length-1?"true":"false"),t.row.domNode.setAttribute("aria-setsize",String(this.ariaProvider.getSetSize(t.element,e,this.length))),t.row.domNode.setAttribute("aria-posinset",String(this.ariaProvider.getPosInSet(t.element,e))),t.row.domNode.setAttribute("id",this.getElementDomId(e)),s["Y"](t.row.domNode,"drop-target",t.dropTarget)},t.prototype.removeItemFromDOM=function(t){var e=this.items[t];e.dragStartDisposable.dispose();var n=this.renderers.get(e.templateId);n&&n.disposeElement&&n.disposeElement(e.element,t,e.row.templateData,e.size),this.cache.release(e.row),e.row=null,this.horizontalScrolling&&this.eventuallyUpdateScrollWidth()},t.prototype.getScrollTop=function(){var t=this.scrollableElement.getScrollPosition();return t.scrollTop},t.prototype.setScrollTop=function(t){this.scrollableElementUpdateDisposable&&(this.scrollableElementUpdateDisposable.dispose(),this.scrollableElementUpdateDisposable=null,this.scrollableElement.setScrollDimensions({scrollHeight:this.scrollHeight})),this.scrollableElement.setScrollPosition({scrollTop:t})},Object.defineProperty(t.prototype,"scrollTop",{get:function(){return this.getScrollTop()},set:function(t){this.setScrollTop(t)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"scrollHeight",{get:function(){return this._scrollHeight+(this.horizontalScrolling?10:0)+this.additionalScrollHeight},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"onMouseClick",{get:function(){var t=this;return a["b"].map(Object(u["a"])(this.domNode,"click"),(function(e){return t.toMouseEvent(e)}))},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"onMouseDblClick",{get:function(){var t=this;return a["b"].map(Object(u["a"])(this.domNode,"dblclick"),(function(e){return t.toMouseEvent(e)}))},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"onMouseMiddleClick",{get:function(){var t=this;return a["b"].filter(a["b"].map(Object(u["a"])(this.domNode,"auxclick"),(function(e){return t.toMouseEvent(e)})),(function(t){return 1===t.browserEvent.button}))},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"onMouseDown",{get:function(){var t=this;return a["b"].map(Object(u["a"])(this.domNode,"mousedown"),(function(e){return t.toMouseEvent(e)}))},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"onContextMenu",{get:function(){var t=this;return a["b"].map(Object(u["a"])(this.domNode,"contextmenu"),(function(e){return t.toMouseEvent(e)}))},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"onTouchStart",{get:function(){var t=this;return a["b"].map(Object(u["a"])(this.domNode,"touchstart"),(function(e){return t.toTouchEvent(e)}))},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"onTap",{get:function(){var t=this;return a["b"].map(Object(u["a"])(this.rowsContainer,r["a"].Tap),(function(e){return t.toGestureEvent(e)}))},enumerable:!0,configurable:!0}),t.prototype.toMouseEvent=function(t){var e=this.getItemIndexFromEventTarget(t.target||null),n="undefined"===typeof e?void 0:this.items[e],i=n&&n.element;return{browserEvent:t,index:e,element:i}},t.prototype.toTouchEvent=function(t){var e=this.getItemIndexFromEventTarget(t.target||null),n="undefined"===typeof e?void 0:this.items[e],i=n&&n.element;return{browserEvent:t,index:e,element:i}},t.prototype.toGestureEvent=function(t){var e=this.getItemIndexFromEventTarget(t.initialTarget||null),n="undefined"===typeof e?void 0:this.items[e],i=n&&n.element;return{browserEvent:t,index:e,element:i}},t.prototype.toDragEvent=function(t){var e=this.getItemIndexFromEventTarget(t.target||null),n="undefined"===typeof e?void 0:this.items[e],i=n&&n.element;return{browserEvent:t,index:e,element:i}},t.prototype.onScroll=function(t){try{this.render(t.scrollTop,t.height,t.scrollLeft,t.scrollWidth),this.supportDynamicHeights&&this._rerender(t.scrollTop,t.height)}catch(e){throw console.error("Got bad scroll event:",t),e}},t.prototype.onTouchChange=function(t){t.preventDefault(),t.stopPropagation(),this.scrollTop-=t.translationY},t.prototype.onDragStart=function(t,e,n){if(n.dataTransfer){var i=this.dnd.getDragElements(t);if(n.dataTransfer.effectAllowed="copyMove",n.dataTransfer.setData(w["a"].RESOURCES,JSON.stringify([e])),n.dataTransfer.setDragImage){var o=void 0;this.dnd.getDragLabel&&(o=this.dnd.getDragLabel(i,n)),"undefined"===typeof o&&(o=String(i.length));var r=s["a"](".monaco-drag-image");r.textContent=o,document.body.appendChild(r),n.dataTransfer.setDragImage(r,-10,-10),setTimeout((function(){return document.body.removeChild(r)}),0)}this.currentDragData=new k(i),w["c"].CurrentDragAndDropData=new x(i),this.dnd.onDragStart&&this.dnd.onDragStart(this.currentDragData,n)}},t.prototype.onDragOver=function(t){var e=this;if(t.browserEvent.preventDefault(),this.onDragLeaveTimeout.dispose(),w["c"].CurrentDragAndDropData&&"vscode-ui"===w["c"].CurrentDragAndDropData.getData())return!1;if(this.setupDragAndDropScrollTopAnimation(t.browserEvent),!t.browserEvent.dataTransfer)return!1;if(!this.currentDragData)if(w["c"].CurrentDragAndDropData)this.currentDragData=w["c"].CurrentDragAndDropData;else{if(!t.browserEvent.dataTransfer.types)return!1;this.currentDragData=new S}var n,i=this.dnd.onDragOver(this.currentDragData,t.element,t.index,t.browserEvent);if(this.canDrop="boolean"===typeof i?i:i.accept,!this.canDrop)return this.currentDragFeedback=void 0,this.currentDragFeedbackDisposable.dispose(),!1;if(t.browserEvent.dataTransfer.dropEffect="boolean"!==typeof i&&0===i.effect?"copy":"move",n="boolean"!==typeof i&&i.feedback?i.feedback:"undefined"===typeof t.index?[-1]:[t.index],n=Object(y["e"])(n).filter((function(t){return t>=-1&&t<e.length})).sort((function(t,e){return t-e})),n=-1===n[0]?[-1]:n,N(this.currentDragFeedback,n))return!0;if(this.currentDragFeedback=n,this.currentDragFeedbackDisposable.dispose(),-1===n[0])s["f"](this.domNode,"drop-target"),s["f"](this.rowsContainer,"drop-target"),this.currentDragFeedbackDisposable=Object(o["h"])((function(){s["P"](e.domNode,"drop-target"),s["P"](e.rowsContainer,"drop-target")}));else{for(var r=0,a=n;r<a.length;r++){var u=a[r],l=this.items[u];l.dropTarget=!0,l.row&&l.row.domNode&&s["f"](l.row.domNode,"drop-target")}this.currentDragFeedbackDisposable=Object(o["h"])((function(){for(var t=0,i=n;t<i.length;t++){var o=i[t],r=e.items[o];r.dropTarget=!1,r.row&&r.row.domNode&&s["P"](r.row.domNode,"drop-target")}}))}return!0},t.prototype.onDragLeave=function(){var t=this;this.onDragLeaveTimeout.dispose(),this.onDragLeaveTimeout=Object(_["g"])((function(){return t.clearDragOverFeedback()}),100)},t.prototype.onDrop=function(t){if(this.canDrop){var e=this.currentDragData;this.teardownDragAndDropScrollTopAnimation(),this.clearDragOverFeedback(),this.currentDragData=void 0,w["c"].CurrentDragAndDropData=void 0,e&&t.browserEvent.dataTransfer&&(t.browserEvent.preventDefault(),e.update(t.browserEvent.dataTransfer),this.dnd.drop(e,t.element,t.index,t.browserEvent))}},t.prototype.onDragEnd=function(t){this.canDrop=!1,this.teardownDragAndDropScrollTopAnimation(),this.clearDragOverFeedback(),this.currentDragData=void 0,w["c"].CurrentDragAndDropData=void 0,this.dnd.onDragEnd&&this.dnd.onDragEnd(t)},t.prototype.clearDragOverFeedback=function(){this.currentDragFeedback=void 0,this.currentDragFeedbackDisposable.dispose(),this.currentDragFeedbackDisposable=o["a"].None},t.prototype.setupDragAndDropScrollTopAnimation=function(t){var e=this;if(!this.dragOverAnimationDisposable){var n=s["F"](this.domNode).top;this.dragOverAnimationDisposable=s["p"](this.animateDragAndDropScrollTop.bind(this,n))}this.dragOverAnimationStopDisposable.dispose(),this.dragOverAnimationStopDisposable=Object(_["g"])((function(){e.dragOverAnimationDisposable&&(e.dragOverAnimationDisposable.dispose(),e.dragOverAnimationDisposable=void 0)}),1e3),this.dragOverMouseY=t.pageY},t.prototype.animateDragAndDropScrollTop=function(t){if(void 0!==this.dragOverMouseY){var e=this.dragOverMouseY-t,n=this.renderHeight-35;e<35?this.scrollTop+=Math.max(-14,Math.floor(.3*(e-35))):e>n&&(this.scrollTop+=Math.min(14,Math.floor(.3*(e-n))))}},t.prototype.teardownDragAndDropScrollTopAnimation=function(){this.dragOverAnimationStopDisposable.dispose(),this.dragOverAnimationDisposable&&(this.dragOverAnimationDisposable.dispose(),this.dragOverAnimationDisposable=void 0)},t.prototype.getItemIndexFromEventTarget=function(t){var e=t;while(e instanceof HTMLElement&&e!==this.rowsContainer){var n=e.getAttribute("data-index");if(n){var i=Number(n);if(!isNaN(i))return i}e=e.parentElement}},t.prototype.getRenderRange=function(t,e){return{start:this.rangeMap.indexAt(t),end:this.rangeMap.indexAfter(t+e-1)}},t.prototype._rerender=function(t,e){var n,i,o=this.getRenderRange(t,e);t===this.elementTop(o.start)?(n=o.start,i=0):o.end-o.start>1&&(n=o.start+1,i=this.elementTop(n)-t);var r=0;while(1){for(var s=this.getRenderRange(t,e),a=!1,u=s.start;u<s.end;u++){var l=this.probeDynamicHeight(u);0!==l&&this.rangeMap.splice(u,1,[this.items[u]]),r+=l,a=a||0!==l}if(!a){0!==r&&this.eventuallyUpdateScrollDimensions();for(var d=c["a"].relativeComplement(o,s),h=0,p=d;h<p.length;h++){var f=p[h];for(u=f.start;u<f.end;u++)this.items[u].row&&this.removeItemFromDOM(u)}for(var g=c["a"].relativeComplement(s,o),m=0,v=g;m<v.length;m++)for(f=v[m],u=f.start;u<f.end;u++){var b=u+1,y=b<this.items.length?this.items[b].row:null,w=y?y.domNode:null;this.insertItemInDOM(u,w)}for(u=s.start;u<s.end;u++)this.items[u].row&&this.updateItemInDOM(this.items[u],u);return"number"===typeof n&&(this.scrollTop=this.elementTop(n)-i),void this._onDidChangeContentHeight.fire(this.contentHeight)}}},t.prototype.probeDynamicHeight=function(t){var e=this.items[t];if(!e.hasDynamicHeight||e.lastDynamicHeightWidth===this.renderWidth)return 0;var n=e.size,i=this.cache.alloc(e.templateId);i.domNode.style.height="",this.rowsContainer.appendChild(i.domNode);var o=this.renderers.get(e.templateId);return o&&(o.renderElement(e.element,t,i.templateData,void 0),o.disposeElement&&o.disposeElement(e.element,t,i.templateData,void 0)),e.size=i.domNode.offsetHeight,this.virtualDelegate.setDynamicHeight&&this.virtualDelegate.setDynamicHeight(e.element,e.size),e.lastDynamicHeightWidth=this.renderWidth,this.rowsContainer.removeChild(i.domNode),this.cache.release(i),e.size-n},t.prototype.getNextToLastElement=function(t){var e=t[t.length-1];if(!e)return null;var n=this.items[e.end];return n&&n.row?n.row.domNode:null},t.prototype.getElementDomId=function(t){return this.domId+"_"+t},t.prototype.dispose=function(){if(this.items){for(var t=0,e=this.items;t<e.length;t++){var n=e[t];if(n.row){var i=this.renderers.get(n.row.templateId);i&&i.disposeTemplate(n.row.templateData)}}this.items=[]}this.domNode&&this.domNode.parentNode&&this.domNode.parentNode.removeChild(this.domNode),Object(o["f"])(this.disposables)},t.InstanceCount=0,C([b["a"]],t.prototype,"onMouseClick",null),C([b["a"]],t.prototype,"onMouseDblClick",null),C([b["a"]],t.prototype,"onMouseMiddleClick",null),C([b["a"]],t.prototype,"onMouseDown",null),C([b["a"]],t.prototype,"onContextMenu",null),C([b["a"]],t.prototype,"onTouchStart",null),C([b["a"]],t.prototype,"onTap",null),t}()},"857a":function(t,e,n){var i=n("e330"),o=n("1d80"),r=n("577e"),s=/"/g,a=i("".replace);t.exports=function(t,e,n,i){var u=r(o(t)),l="<"+e;return""!==n&&(l+=" "+n+'="'+a(r(i),s,"&quot;")+'"'),l+">"+u+"</"+e+">"}},8899:function(t,e,n){},"8bf4":function(t,e,n){},"8daa":function(t,e,n){"use strict";n.d(e,"a",(function(){return u}));n("abf23");var i=n("aa3d"),o=n("8b4e"),r=n("11f7"),s=n("dff7"),a=r["a"],u=function(){function t(t,e,n){this.os=e,this.options=n,this.domNode=r["q"](t,a(".monaco-keybinding")),this.didEverRender=!1,t.appendChild(this.domNode)}return t.prototype.set=function(e,n){this.didEverRender&&this.keybinding===e&&t.areSame(this.matches,n)||(this.keybinding=e,this.matches=n,this.render())},t.prototype.render=function(){if(r["t"](this.domNode),this.keybinding){var t=this.keybinding.getParts(),e=t[0],n=t[1];e&&this.renderPart(this.domNode,e,this.matches?this.matches.firstPart:null),n&&(r["q"](this.domNode,a("span.monaco-keybinding-key-chord-separator",void 0," ")),this.renderPart(this.domNode,n,this.matches?this.matches.chordPart:null)),this.domNode.title=this.keybinding.getAriaLabel()||""}else this.options&&this.options.renderUnboundKeybindings&&this.renderUnbound(this.domNode);this.didEverRender=!0},t.prototype.renderPart=function(t,e,n){var i=o["b"].modifierLabels[this.os];e.ctrlKey&&this.renderKey(t,i.ctrlKey,Boolean(null===n||void 0===n?void 0:n.ctrlKey),i.separator),e.shiftKey&&this.renderKey(t,i.shiftKey,Boolean(null===n||void 0===n?void 0:n.shiftKey),i.separator),e.altKey&&this.renderKey(t,i.altKey,Boolean(null===n||void 0===n?void 0:n.altKey),i.separator),e.metaKey&&this.renderKey(t,i.metaKey,Boolean(null===n||void 0===n?void 0:n.metaKey),i.separator);var r=e.keyLabel;r&&this.renderKey(t,r,Boolean(null===n||void 0===n?void 0:n.keyCode),"")},t.prototype.renderKey=function(t,e,n,i){r["q"](t,a("span.monaco-keybinding-key"+(n?".highlight":""),void 0,e)),i&&r["q"](t,a("span.monaco-keybinding-key-separator",void 0,i))},t.prototype.renderUnbound=function(t){r["q"](t,a("span.monaco-keybinding-key",void 0,Object(s["a"])("unbound","Unbound")))},t.areSame=function(t,e){return t===e||!t&&!e||!!t&&!!e&&Object(i["e"])(t.firstPart,e.firstPart)&&Object(i["e"])(t.chordPart,e.chordPart)},t}()},"93be":function(t,e,n){"use strict";n("5cdb"),n("7a99")},"9cfc":function(t,e,n){"use strict";n.d(e,"a",(function(){return r}));var i=n("da0c"),o=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},t(e,n)};return function(e,n){function i(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),r=function(t){function e(e,n){var i=t.call(this,e,n,"contextsubmenu")||this;return i.entries=n,i}return o(e,t),e}(i["b"])},a60f:function(t,e,n){"use strict";n.d(e,"a",(function(){return i})),n.d(e,"b",(function(){return c}));var i,o=n("e8e3"),r=n("a666"),s=n("11f7"),a=n("6424"),u=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},t(e,n)};return function(e,n){function i(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),l=function(t,e,n,i){var o,r=arguments.length,s=r<3?e:null===i?i=Object.getOwnPropertyDescriptor(e,n):i;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)s=Reflect.decorate(t,e,n,i);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(r<3?o(s):r>3?o(e,n,s):o(e,n))||s);return r>3&&s&&Object.defineProperty(e,n,s),s};(function(t){t.Tap="-monaco-gesturetap",t.Change="-monaco-gesturechange",t.Start="-monaco-gesturestart",t.End="-monaco-gesturesend",t.Contextmenu="-monaco-gesturecontextmenu"})(i||(i={}));var c=function(t){function e(){var e=t.call(this)||this;return e.dispatched=!1,e.activeTouches={},e.handle=null,e.targets=[],e.ignoreTargets=[],e._lastSetTapCountTime=0,e._register(s["j"](document,"touchstart",(function(t){return e.onTouchStart(t)}))),e._register(s["j"](document,"touchend",(function(t){return e.onTouchEnd(t)}))),e._register(s["j"](document,"touchmove",(function(t){return e.onTouchMove(t)}))),e}return u(e,t),e.addTarget=function(t){return e.isTouchDevice()?(e.INSTANCE||(e.INSTANCE=new e),e.INSTANCE.targets.push(t),{dispose:function(){e.INSTANCE.targets=e.INSTANCE.targets.filter((function(e){return e!==t}))}}):r["a"].None},e.ignoreTarget=function(t){return e.isTouchDevice()?(e.INSTANCE||(e.INSTANCE=new e),e.INSTANCE.ignoreTargets.push(t),{dispose:function(){e.INSTANCE.ignoreTargets=e.INSTANCE.ignoreTargets.filter((function(e){return e!==t}))}}):r["a"].None},e.isTouchDevice=function(){return"ontouchstart"in window||navigator.maxTouchPoints>0||window.navigator.msMaxTouchPoints>0},e.prototype.dispose=function(){this.handle&&(this.handle.dispose(),this.handle=null),t.prototype.dispose.call(this)},e.prototype.onTouchStart=function(t){var e=Date.now();this.handle&&(this.handle.dispose(),this.handle=null);for(var n=0,o=t.targetTouches.length;n<o;n++){var r=t.targetTouches.item(n);this.activeTouches[r.identifier]={id:r.identifier,initialTarget:r.target,initialTimeStamp:e,initialPageX:r.pageX,initialPageY:r.pageY,rollingTimestamps:[e],rollingPageX:[r.pageX],rollingPageY:[r.pageY]};var s=this.newGestureEvent(i.Start,r.target);s.pageX=r.pageX,s.pageY=r.pageY,this.dispatchEvent(s)}this.dispatched&&(t.preventDefault(),t.stopPropagation(),this.dispatched=!1)},e.prototype.onTouchEnd=function(t){for(var n=Date.now(),r=Object.keys(this.activeTouches).length,s=function(s,u){var l=t.changedTouches.item(s);if(!a.activeTouches.hasOwnProperty(String(l.identifier)))return console.warn("move of an UNKNOWN touch",l),"continue";var c=a.activeTouches[l.identifier],d=Date.now()-c.initialTimeStamp;if(d<e.HOLD_DELAY&&Math.abs(c.initialPageX-o["v"](c.rollingPageX))<30&&Math.abs(c.initialPageY-o["v"](c.rollingPageY))<30){var h=a.newGestureEvent(i.Tap,c.initialTarget);h.pageX=o["v"](c.rollingPageX),h.pageY=o["v"](c.rollingPageY),a.dispatchEvent(h)}else if(d>=e.HOLD_DELAY&&Math.abs(c.initialPageX-o["v"](c.rollingPageX))<30&&Math.abs(c.initialPageY-o["v"](c.rollingPageY))<30){h=a.newGestureEvent(i.Contextmenu,c.initialTarget);h.pageX=o["v"](c.rollingPageX),h.pageY=o["v"](c.rollingPageY),a.dispatchEvent(h)}else if(1===r){var p=o["v"](c.rollingPageX),f=o["v"](c.rollingPageY),g=o["v"](c.rollingTimestamps)-c.rollingTimestamps[0],m=p-c.rollingPageX[0],v=f-c.rollingPageY[0],b=a.targets.filter((function(t){return c.initialTarget instanceof Node&&t.contains(c.initialTarget)}));a.inertia(b,n,Math.abs(m)/g,m>0?1:-1,p,Math.abs(v)/g,v>0?1:-1,f)}a.dispatchEvent(a.newGestureEvent(i.End,c.initialTarget)),delete a.activeTouches[l.identifier]},a=this,u=0,l=t.changedTouches.length;u<l;u++)s(u,l);this.dispatched&&(t.preventDefault(),t.stopPropagation(),this.dispatched=!1)},e.prototype.newGestureEvent=function(t,e){var n=document.createEvent("CustomEvent");return n.initEvent(t,!1,!0),n.initialTarget=e,n.tapCount=0,n},e.prototype.dispatchEvent=function(t){var n=this;if(t.type===i.Tap){var o=(new Date).getTime(),r=0;r=o-this._lastSetTapCountTime>e.CLEAR_TAP_COUNT_TIME?1:2,this._lastSetTapCountTime=o,t.tapCount=r}else t.type!==i.Change&&t.type!==i.Contextmenu||(this._lastSetTapCountTime=0);for(var s=0;s<this.ignoreTargets.length;s++)if(t.initialTarget instanceof Node&&this.ignoreTargets[s].contains(t.initialTarget))return;this.targets.forEach((function(e){t.initialTarget instanceof Node&&e.contains(t.initialTarget)&&(e.dispatchEvent(t),n.dispatched=!0)}))},e.prototype.inertia=function(t,n,o,r,a,u,l,c){var d=this;this.handle=s["W"]((function(){var s=Date.now(),h=s-n,p=0,f=0,g=!0;o+=e.SCROLL_FRICTION*h,u+=e.SCROLL_FRICTION*h,o>0&&(g=!1,p=r*o*h),u>0&&(g=!1,f=l*u*h);var m=d.newGestureEvent(i.Change);m.translationX=p,m.translationY=f,t.forEach((function(t){return t.dispatchEvent(m)})),g||d.inertia(t,s,o,r,a+p,u,l,c+f)}))},e.prototype.onTouchMove=function(t){for(var e=Date.now(),n=0,r=t.changedTouches.length;n<r;n++){var s=t.changedTouches.item(n);if(this.activeTouches.hasOwnProperty(String(s.identifier))){var a=this.activeTouches[s.identifier],u=this.newGestureEvent(i.Change,a.initialTarget);u.translationX=s.pageX-o["v"](a.rollingPageX),u.translationY=s.pageY-o["v"](a.rollingPageY),u.pageX=s.pageX,u.pageY=s.pageY,this.dispatchEvent(u),a.rollingPageX.length>3&&(a.rollingPageX.shift(),a.rollingPageY.shift(),a.rollingTimestamps.shift()),a.rollingPageX.push(s.pageX),a.rollingPageY.push(s.pageY),a.rollingTimestamps.push(e)}else console.warn("end of an UNKNOWN touch",s)}this.dispatched&&(t.preventDefault(),t.stopPropagation(),this.dispatched=!1)},e.SCROLL_FRICTION=-.005,e.HOLD_DELAY=700,e.CLEAR_TAP_COUNT_TIME=400,l([a["a"]],e,"isTouchDevice",null),e}(r["a"])},abf23:function(t,e,n){},af03:function(t,e,n){var i=n("d039");t.exports=function(t){return i((function(){var e=""[t]('"');return e!==e.toLowerCase()||e.split('"').length>3}))}},b835:function(t,e,n){"use strict";n.d(e,"a",(function(){return p}));var i=n("0f70"),o=n("fe45"),r=n("30db"),s=new Array(230),a=new Array(112);function u(t){if(t.charCode){var e=String.fromCharCode(t.charCode).toUpperCase();return o["b"].fromString(e)}return s[t.keyCode]||0}(function(){for(var t=0;t<a.length;t++)a[t]=-1;function e(t,e){s[t]=e,a[e]=t}e(3,7),e(8,1),e(9,2),e(13,3),e(16,4),e(17,5),e(18,6),e(19,7),e(20,8),e(27,9),e(32,10),e(33,11),e(34,12),e(35,13),e(36,14),e(37,15),e(38,16),e(39,17),e(40,18),e(45,19),e(46,20),e(48,21),e(49,22),e(50,23),e(51,24),e(52,25),e(53,26),e(54,27),e(55,28),e(56,29),e(57,30),e(65,31),e(66,32),e(67,33),e(68,34),e(69,35),e(70,36),e(71,37),e(72,38),e(73,39),e(74,40),e(75,41),e(76,42),e(77,43),e(78,44),e(79,45),e(80,46),e(81,47),e(82,48),e(83,49),e(84,50),e(85,51),e(86,52),e(87,53),e(88,54),e(89,55),e(90,56),e(93,58),e(96,93),e(97,94),e(98,95),e(99,96),e(100,97),e(101,98),e(102,99),e(103,100),e(104,101),e(105,102),e(106,103),e(107,104),e(108,105),e(109,106),e(110,107),e(111,108),e(112,59),e(113,60),e(114,61),e(115,62),e(116,63),e(117,64),e(118,65),e(119,66),e(120,67),e(121,68),e(122,69),e(123,70),e(124,71),e(125,72),e(126,73),e(127,74),e(128,75),e(129,76),e(130,77),e(144,78),e(145,79),e(186,80),e(187,81),e(188,82),e(189,83),e(190,84),e(191,85),e(192,86),e(193,110),e(194,111),e(219,87),e(220,88),e(221,89),e(222,90),e(223,91),e(226,92),e(229,109),i["i"]?e(91,57):i["h"]?(e(59,80),e(107,81),e(109,83),r["e"]&&e(224,57)):i["m"]&&(e(91,57),r["e"]?e(93,57):e(92,57))})();var l=r["e"]?256:2048,c=512,d=1024,h=r["e"]?2048:256,p=function(){function t(t){this._standardKeyboardEventBrand=!0;var e=t;this.browserEvent=e,this.target=e.target,this.ctrlKey=e.ctrlKey,this.shiftKey=e.shiftKey,this.altKey=e.altKey,this.metaKey=e.metaKey,this.keyCode=u(e),this.code=e.code,this.ctrlKey=this.ctrlKey||5===this.keyCode,this.altKey=this.altKey||6===this.keyCode,this.shiftKey=this.shiftKey||4===this.keyCode,this.metaKey=this.metaKey||57===this.keyCode,this._asKeybinding=this._computeKeybinding(),this._asRuntimeKeybinding=this._computeRuntimeKeybinding()}return t.prototype.preventDefault=function(){this.browserEvent&&this.browserEvent.preventDefault&&this.browserEvent.preventDefault()},t.prototype.stopPropagation=function(){this.browserEvent&&this.browserEvent.stopPropagation&&this.browserEvent.stopPropagation()},t.prototype.toKeybinding=function(){return this._asRuntimeKeybinding},t.prototype.equals=function(t){return this._asKeybinding===t},t.prototype._computeKeybinding=function(){var t=0;5!==this.keyCode&&4!==this.keyCode&&6!==this.keyCode&&57!==this.keyCode&&(t=this.keyCode);var e=0;return this.ctrlKey&&(e|=l),this.altKey&&(e|=c),this.shiftKey&&(e|=d),this.metaKey&&(e|=h),e|=t,e},t.prototype._computeRuntimeKeybinding=function(){var t=0;return 5!==this.keyCode&&4!==this.keyCode&&6!==this.keyCode&&57!==this.keyCode&&(t=this.keyCode),new o["e"](this.ctrlKey,this.shiftKey,this.altKey,this.metaKey,t)},t}()},bcac:function(t,e,n){"use strict";n.d(e,"a",(function(){return c}));n("4d3d");var i=n("11f7"),o=n("30db"),r=n("a666"),s=n("9ee1"),a=n("0a31"),u=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},t(e,n)};return function(e,n){function i(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}();function l(t,e,n){var i=n.offset+n.size;return 0===n.position?e<=t-i?i:e<=n.offset?n.offset-e:Math.max(t-e,0):e<=n.offset?n.offset-e:e<=t-i?i:0}var c=function(t){function e(e){var n=t.call(this)||this;return n.container=null,n.delegate=null,n.toDisposeOnClean=r["a"].None,n.toDisposeOnSetContainer=r["a"].None,n.view=i["a"](".context-view"),i["J"](n.view),n.setContainer(e),n._register(Object(r["h"])((function(){return n.setContainer(null)}))),n}return u(e,t),e.prototype.setContainer=function(t){var n=this;if(this.container&&(this.toDisposeOnSetContainer.dispose(),this.container.removeChild(this.view),this.container=null),t){this.container=t,this.container.appendChild(this.view);var o=new r["b"];e.BUBBLE_UP_EVENTS.forEach((function(t){o.add(i["o"](n.container,t,(function(t){n.onDOMEvent(t,!1)})))})),e.BUBBLE_DOWN_EVENTS.forEach((function(t){o.add(i["o"](n.container,t,(function(t){n.onDOMEvent(t,!0)}),!0))})),this.toDisposeOnSetContainer=o}},e.prototype.show=function(t){this.isVisible()&&this.hide(),i["t"](this.view),this.view.className="context-view",this.view.style.top="0px",this.view.style.left="0px",i["X"](this.view),this.toDisposeOnClean=t.render(this.view)||r["a"].None,this.delegate=t,this.doLayout(),this.delegate.focus&&this.delegate.focus()},e.prototype.layout=function(){this.isVisible()&&(!1!==this.delegate.canRelayout||o["c"]&&a["a"].pointerEvents?(this.delegate.layout&&this.delegate.layout(),this.doLayout()):this.hide())},e.prototype.doLayout=function(){if(this.isVisible()){var t,e=this.delegate.getAnchor();if(i["L"](e)){var n=i["C"](e);t={top:n.top,left:n.left,width:n.width,height:n.height}}else t={top:e.y,left:e.x,width:e.width||1,height:e.height||2};var o,r=i["H"](this.view),a=i["G"](this.view),u=this.delegate.anchorPosition||0,c=this.delegate.anchorAlignment||0,d={offset:t.top-window.pageYOffset,size:t.height,position:0===u?0:1};o=0===c?{offset:t.left,size:0,position:0}:{offset:t.left+t.width,size:0,position:1};var h=l(window.innerHeight,a,d)+window.pageYOffset;s["a"].intersects({start:h,end:h+a},{start:d.offset,end:d.offset+d.size})&&(o.size=t.width,1===c&&(o.offset=t.left));var p=l(window.innerWidth,r,o);i["Q"](this.view,"top","bottom","left","right"),i["f"](this.view,0===u?"bottom":"top"),i["f"](this.view,0===c?"left":"right");var f=i["C"](this.container);this.view.style.top=h-f.top+"px",this.view.style.left=p-f.left+"px",this.view.style.width="initial"}},e.prototype.hide=function(t){var e=this.delegate;this.delegate=null,(null===e||void 0===e?void 0:e.onHide)&&e.onHide(t),this.toDisposeOnClean.dispose(),i["J"](this.view)},e.prototype.isVisible=function(){return!!this.delegate},e.prototype.onDOMEvent=function(t,e){this.delegate&&(this.delegate.onDOMEvent?this.delegate.onDOMEvent(t,document.activeElement):e&&!i["K"](t.target,this.container)&&this.hide())},e.prototype.dispose=function(){this.hide(),t.prototype.dispose.call(this)},e.BUBBLE_UP_EVENTS=["click","keydown","focus","blur"],e.BUBBLE_DOWN_EVENTS=["click"],e}(r["a"])},c39c:function(t,e,n){"use strict";n.d(e,"a",(function(){return m}));var i=n("11f7"),o=n("31df"),r=n("fdcc"),s=n("78bc"),a=n("9c3e"),u=n("4acc"),l=n("42fe"),c=n("438a"),d=n("aa3d"),h=n("3742"),p=n("6d8e"),f=n("b589"),g=n("561a");function m(t,e){void 0===e&&(e={});var n,m=Object(o["a"])(e),v=function(e){var n;try{n=Object(c["a"])(decodeURIComponent(e))}catch(i){}return n?(n=Object(d["b"])(n,(function(e){return t.uris&&t.uris[e]?p["a"].revive(t.uris[e]):void 0})),encodeURIComponent(JSON.stringify(n))):e},b=function(e,n){var o=t.uris&&t.uris[e];if(!o)return e;var r=p["a"].revive(o);return p["a"].parse(e).toString()===r.toString()?e:(n&&(r=i["s"](r)),r.query&&(r=r.with({query:v(r.query)})),r.toString(!0))},y=new Promise((function(t){return n=t})),w=new u["a"];w.image=function(t,e,n){var i,o=[],r=[];return t&&(i=Object(s["d"])(t),t=i.href,o=i.dimensions,t=b(t,!0),r.push('src="'+t+'"')),n&&r.push('alt="'+n+'"'),e&&r.push('title="'+e+'"'),o.length&&(r=r.concat(o)),"<img "+r.join(" ")+">"},w.link=function(e,n,i){return e===i&&(i=Object(s["e"])(i)),e=b(e,!1),n=Object(s["e"])(n),e=Object(s["e"])(e),!e||e.match(/^data:|javascript:/i)||e.match(/^command:/i)&&!t.isTrusted||e.match(/^command:(\/\/\/)?_workbench\.downloadResource/i)?i:(e=e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#39;"),'<a href="#" data-href="'+e+'" title="'+(n||e)+'">'+i+"</a>")},w.paragraph=function(e){return"<p>"+(t.supportThemeIcons?Object(g["c"])(e):e)+"</p>"},e.codeBlockRenderer&&(w.code=function(t,n){var i=e.codeBlockRenderer(n,t),o=a["b"].nextId(),r=Promise.all([i,y]).then((function(t){var e=t[0],n=m.querySelector('div[data-code="'+o+'"]');n&&(n.innerHTML=e)})).catch((function(t){}));return e.codeBlockRenderCallback&&r.then(e.codeBlockRenderCallback),'<div class="code" data-code="'+o+'">'+Object(h["o"])(t)+"</div>"});var _=e.actionHandler;_&&_.disposeables.add(i["o"](m,"click",(function(t){var e=t.target;if("A"===e.tagName||(e=e.parentElement,e&&"A"===e.tagName))try{var n=e.dataset["href"];n&&_.callback(n,t)}catch(i){Object(r["e"])(i)}finally{t.preventDefault()}})));var E={sanitize:!0,renderer:w},C=[f["b"].http,f["b"].https,f["b"].mailto,f["b"].data,f["b"].file,f["b"].vscodeRemote,f["b"].vscodeRemoteResource];t.isTrusted&&C.push(f["b"].command);var O=u["b"](t.supportThemeIcons?Object(g["b"])(t.value):t.value,E);return m.innerHTML=Object(l["a"])(O,{allowedSchemes:C,allowedAttributes:{a:["href","name","target","data-href"],iframe:["allowfullscreen","frameborder","src"],img:["src","title","alt","width","height"],div:["class","data-code"],span:["class"]}}),n(),m}},c4e3:function(t,e,n){"use strict";n.d(e,"a",(function(){return c}));n("2a04");var i=n("11f7"),o=n("ee56"),r=n("a666"),s=n("9ee1"),a=n("aa3d"),u=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},t(e,n)};return function(e,n){function i(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),l=function(){function t(t){this._element=t}return Object.defineProperty(t.prototype,"element",{get:function(){return this._element},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"textContent",{set:function(t){this.disposed||t===this._textContent||(this._textContent=t,this._element.textContent=t)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"className",{set:function(t){this.disposed||t===this._className||(this._className=t,this._element.className=t)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"title",{set:function(t){this.disposed||t===this._title||(this._title=t,this._title?this._element.title=t:this._element.removeAttribute("title"))},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"empty",{set:function(t){this.disposed||t===this._empty||(this._empty=t,this._element.style.marginLeft=t?"0":"")},enumerable:!0,configurable:!0}),t.prototype.dispose=function(){this.disposed=!0},t}(),c=function(t){function e(e,n){var r=t.call(this)||this;r.domNode=r._register(new l(i["q"](e,i["a"](".monaco-icon-label"))));var s=i["q"](r.domNode.element,i["a"](".monaco-icon-label-container")),a=i["q"](s,i["a"]("span.monaco-icon-name-container"));return r.descriptionContainer=r._register(new l(i["q"](s,i["a"]("span.monaco-icon-description-container")))),(null===n||void 0===n?void 0:n.supportHighlights)?r.nameNode=new p(a,!!n.supportCodicons):r.nameNode=new d(a),(null===n||void 0===n?void 0:n.supportDescriptionHighlights)?r.descriptionNodeFactory=function(){return new o["a"](i["q"](r.descriptionContainer.element,i["a"]("span.label-description")),!!n.supportCodicons)}:r.descriptionNodeFactory=function(){return r._register(new l(i["q"](r.descriptionContainer.element,i["a"]("span.label-description"))))},r}return u(e,t),e.prototype.setLabel=function(t,e,n){var i=["monaco-icon-label"];n&&(n.extraClasses&&i.push.apply(i,n.extraClasses),n.italic&&i.push("italic")),this.domNode.className=i.join(" "),this.domNode.title=(null===n||void 0===n?void 0:n.title)||"",this.nameNode.setLabel(t,n),(e||this.descriptionNode)&&(this.descriptionNode||(this.descriptionNode=this.descriptionNodeFactory()),this.descriptionNode instanceof o["a"]?(this.descriptionNode.set(e||"",n?n.descriptionMatches:void 0),(null===n||void 0===n?void 0:n.descriptionTitle)?this.descriptionNode.element.title=n.descriptionTitle:this.descriptionNode.element.removeAttribute("title")):(this.descriptionNode.textContent=e||"",this.descriptionNode.title=(null===n||void 0===n?void 0:n.descriptionTitle)||"",this.descriptionNode.empty=!e))},e}(r["a"]),d=function(){function t(t){this.container=t,this.label=void 0,this.singleLabel=void 0}return t.prototype.setLabel=function(t,e){if(this.label!==t||!Object(a["e"])(this.options,e))if(this.label=t,this.options=e,"string"===typeof t)this.singleLabel||(this.container.innerHTML="",i["P"](this.container,"multiple"),this.singleLabel=i["q"](this.container,i["a"]("a.label-name",{id:null===e||void 0===e?void 0:e.domId}))),this.singleLabel.textContent=t;else{this.container.innerHTML="",i["f"](this.container,"multiple"),this.singleLabel=void 0;for(var n=0;n<t.length;n++){var o=t[n],r=(null===e||void 0===e?void 0:e.domId)&&(null===e||void 0===e?void 0:e.domId)+"_"+n;i["q"](this.container,i["a"]("a.label-name",{id:r,"data-icon-label-count":t.length,"data-icon-label-index":n},o)),n<t.length-1&&i["q"](this.container,i["a"]("span.label-separator",void 0,(null===e||void 0===e?void 0:e.separator)||"/"))}}},t}();function h(t,e,n){if(n){var i=0;return t.map((function(t){var o={start:i,end:i+t.length},r=n.map((function(t){return s["a"].intersect(o,t)})).filter((function(t){return!s["a"].isEmpty(t)})).map((function(t){var e=t.start,n=t.end;return{start:e-i,end:n-i}}));return i=o.end+e.length,r}))}}var p=function(){function t(t,e){this.container=t,this.supportCodicons=e,this.label=void 0,this.singleLabel=void 0}return t.prototype.setLabel=function(t,e){if(this.label!==t||!Object(a["e"])(this.options,e))if(this.label=t,this.options=e,"string"===typeof t)this.singleLabel||(this.container.innerHTML="",i["P"](this.container,"multiple"),this.singleLabel=new o["a"](i["q"](this.container,i["a"]("a.label-name",{id:null===e||void 0===e?void 0:e.domId})),this.supportCodicons)),this.singleLabel.set(t,null===e||void 0===e?void 0:e.matches,null===e||void 0===e?void 0:e.title,null===e||void 0===e?void 0:e.labelEscapeNewLines);else{this.container.innerHTML="",i["f"](this.container,"multiple"),this.singleLabel=void 0;for(var n=(null===e||void 0===e?void 0:e.separator)||"/",r=h(t,n,null===e||void 0===e?void 0:e.matches),s=0;s<t.length;s++){var u=t[s],l=r?r[s]:void 0,c=(null===e||void 0===e?void 0:e.domId)&&(null===e||void 0===e?void 0:e.domId)+"_"+s,d=i["a"]("a.label-name",{id:c,"data-icon-label-count":t.length,"data-icon-label-index":s}),p=new o["a"](i["q"](this.container,d),this.supportCodicons);p.set(u,l,null===e||void 0===e?void 0:e.title,null===e||void 0===e?void 0:e.labelEscapeNewLines),s<t.length-1&&i["q"](d,i["a"]("span.label-separator",void 0,n))}}},t}()},c84a:function(t,e,n){},caa1:function(t,e,n){},d3ef:function(t,e,n){"use strict";n.d(e,"b",(function(){return y})),n.d(e,"a",(function(){return w}));n("8bf4");var i=n("dff7"),o=n("0f70"),r=n("11f7"),s=n("31df"),a=n("3813"),u=n("5aa5"),l=n("308f"),c=n("1b7d"),d=n("ceb8"),h=n("aa3d"),p=n("4828"),f=n("1898"),g=n("e32d"),m=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},t(e,n)};return function(e,n){function i(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),v=r["a"],b={inputBackground:d["a"].fromHex("#3C3C3C"),inputForeground:d["a"].fromHex("#CCCCCC"),inputValidationInfoBorder:d["a"].fromHex("#55AAFF"),inputValidationInfoBackground:d["a"].fromHex("#063B49"),inputValidationWarningBorder:d["a"].fromHex("#B89500"),inputValidationWarningBackground:d["a"].fromHex("#352A05"),inputValidationErrorBorder:d["a"].fromHex("#BE1100"),inputValidationErrorBackground:d["a"].fromHex("#5A1D1D")},y=function(t){function e(e,n,i){var s=t.call(this)||this;s.state="idle",s.maxHeight=Number.POSITIVE_INFINITY,s._onDidChange=s._register(new l["a"]),s.onDidChange=s._onDidChange.event,s._onDidHeightChange=s._register(new l["a"]),s.onDidHeightChange=s._onDidHeightChange.event,s.contextViewProvider=n,s.options=i||Object.create(null),Object(h["g"])(s.options,b,!1),s.message=null,s.placeholder=s.options.placeholder||"",s.ariaLabel=s.options.ariaLabel||"",s.inputBackground=s.options.inputBackground,s.inputForeground=s.options.inputForeground,s.inputBorder=s.options.inputBorder,s.inputValidationInfoBorder=s.options.inputValidationInfoBorder,s.inputValidationInfoBackground=s.options.inputValidationInfoBackground,s.inputValidationInfoForeground=s.options.inputValidationInfoForeground,s.inputValidationWarningBorder=s.options.inputValidationWarningBorder,s.inputValidationWarningBackground=s.options.inputValidationWarningBackground,s.inputValidationWarningForeground=s.options.inputValidationWarningForeground,s.inputValidationErrorBorder=s.options.inputValidationErrorBorder,s.inputValidationErrorBackground=s.options.inputValidationErrorBackground,s.inputValidationErrorForeground=s.options.inputValidationErrorForeground,s.options.validationOptions&&(s.validation=s.options.validationOptions.validation),s.element=r["q"](e,v(".monaco-inputbox.idle"));var a=s.options.flexibleHeight?"textarea":"input",c=r["q"](s.element,v(".wrapper"));if(s.input=r["q"](c,v(a+".input.empty")),s.input.setAttribute("autocorrect","off"),s.input.setAttribute("autocapitalize","off"),s.input.setAttribute("spellcheck","false"),s.onfocus(s.input,(function(){return r["f"](s.element,"synthetic-focus")})),s.onblur(s.input,(function(){return r["P"](s.element,"synthetic-focus")})),s.options.flexibleHeight){s.maxHeight="number"===typeof s.options.flexibleMaxHeight?s.options.flexibleMaxHeight:Number.POSITIVE_INFINITY,s.mirror=r["q"](c,v("div.mirror")),s.mirror.innerHTML="&#160;",s.scrollableElement=new f["b"](s.element,{vertical:1}),s.options.flexibleWidth&&(s.input.setAttribute("wrap","off"),s.mirror.style.whiteSpace="pre",s.mirror.style.wordWrap="initial"),r["q"](e,s.scrollableElement.getDomNode()),s._register(s.scrollableElement),s._register(s.scrollableElement.onScroll((function(t){return s.input.scrollTop=t.scrollTop})));var d=l["b"].filter(Object(g["a"])(document,"selectionchange"),(function(){var t=document.getSelection();return(null===t||void 0===t?void 0:t.anchorNode)===c}));s._register(d(s.updateScrollDimensions,s)),s._register(s.onDidHeightChange(s.updateScrollDimensions,s))}else s.input.type=s.options.type||"text",s.input.setAttribute("wrap","off");return s.ariaLabel&&s.input.setAttribute("aria-label",s.ariaLabel),s.placeholder&&s.setPlaceHolder(s.placeholder),s.oninput(s.input,(function(){return s.onValueChange()})),s.onblur(s.input,(function(){return s.onBlur()})),s.onfocus(s.input,(function(){return s.onFocus()})),s.placeholder&&o["i"]&&s.onclick(s.input,(function(t){r["c"].stop(t,!0),s.input.focus()})),s.ignoreGesture(s.input),setTimeout((function(){return s.updateMirror()}),0),s.options.actions&&(s.actionbar=s._register(new u["a"](s.element)),s.actionbar.push(s.options.actions,{icon:!0,label:!1})),s.applyStyles(),s}return m(e,t),e.prototype.onBlur=function(){this._hideMessage()},e.prototype.onFocus=function(){this._showMessage()},e.prototype.setPlaceHolder=function(t){this.placeholder=t,this.input.setAttribute("placeholder",t),this.input.title=t},e.prototype.setAriaLabel=function(t){this.ariaLabel=t,t?this.input.setAttribute("aria-label",this.ariaLabel):this.input.removeAttribute("aria-label")},Object.defineProperty(e.prototype,"inputElement",{get:function(){return this.input},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"value",{get:function(){return this.input.value},set:function(t){this.input.value!==t&&(this.input.value=t,this.onValueChange())},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"height",{get:function(){return"number"===typeof this.cachedHeight?this.cachedHeight:r["G"](this.element)},enumerable:!0,configurable:!0}),e.prototype.focus=function(){this.input.focus()},e.prototype.blur=function(){this.input.blur()},e.prototype.hasFocus=function(){return document.activeElement===this.input},e.prototype.select=function(t){void 0===t&&(t=null),this.input.select(),t&&this.input.setSelectionRange(t.start,t.end)},e.prototype.enable=function(){this.input.removeAttribute("disabled")},e.prototype.disable=function(){this.blur(),this.input.disabled=!0,this._hideMessage()},Object.defineProperty(e.prototype,"width",{get:function(){return r["H"](this.input)},set:function(t){if(this.options.flexibleHeight&&this.options.flexibleWidth){var e=0;if(this.mirror){var n=parseFloat(this.mirror.style.paddingLeft||"")||0,i=parseFloat(this.mirror.style.paddingRight||"")||0;e=n+i}this.input.style.width=t-e+"px"}else this.input.style.width=t+"px";this.mirror&&(this.mirror.style.width=t+"px")},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"paddingRight",{set:function(t){this.options.flexibleHeight&&this.options.flexibleWidth?this.input.style.width="calc(100% - "+t+"px)":this.input.style.paddingRight=t+"px",this.mirror&&(this.mirror.style.paddingRight=t+"px")},enumerable:!0,configurable:!0}),e.prototype.updateScrollDimensions=function(){if("number"===typeof this.cachedContentHeight&&"number"===typeof this.cachedHeight&&this.scrollableElement){var t=this.cachedContentHeight,e=this.cachedHeight,n=this.input.scrollTop;this.scrollableElement.setScrollDimensions({scrollHeight:t,height:e}),this.scrollableElement.setScrollPosition({scrollTop:n})}},e.prototype.showMessage=function(t,e){this.message=t,r["P"](this.element,"idle"),r["P"](this.element,"info"),r["P"](this.element,"warning"),r["P"](this.element,"error"),r["f"](this.element,this.classForType(t.type));var n,o=this.stylesForType(this.message.type);this.element.style.border=o.border?"1px solid "+o.border:"",n=3===t.type?i["a"]("alertErrorMessage","Error: {0}",t.content):2===t.type?i["a"]("alertWarningMessage","Warning: {0}",t.content):i["a"]("alertInfoMessage","Info: {0}",t.content),a["a"](n),(this.hasFocus()||e)&&this._showMessage()},e.prototype.hideMessage=function(){this.message=null,r["P"](this.element,"info"),r["P"](this.element,"warning"),r["P"](this.element,"error"),r["f"](this.element,"idle"),this._hideMessage(),this.applyStyles()},e.prototype.validate=function(){var t=null;return this.validation&&(t=this.validation(this.value),t?(this.inputElement.setAttribute("aria-invalid","true"),this.showMessage(t)):this.inputElement.hasAttribute("aria-invalid")&&(this.inputElement.removeAttribute("aria-invalid"),this.hideMessage())),!t},e.prototype.stylesForType=function(t){switch(t){case 1:return{border:this.inputValidationInfoBorder,background:this.inputValidationInfoBackground,foreground:this.inputValidationInfoForeground};case 2:return{border:this.inputValidationWarningBorder,background:this.inputValidationWarningBackground,foreground:this.inputValidationWarningForeground};default:return{border:this.inputValidationErrorBorder,background:this.inputValidationErrorBackground,foreground:this.inputValidationErrorForeground}}},e.prototype.classForType=function(t){switch(t){case 1:return"info";case 2:return"warning";default:return"error"}},e.prototype._showMessage=function(){var t=this;if(this.contextViewProvider&&this.message){var e,n=function(){return e.style.width=r["H"](t.element)+"px"};this.contextViewProvider.showContextView({getAnchor:function(){return t.element},anchorAlignment:1,render:function(i){if(!t.message)return null;e=r["q"](i,v(".monaco-inputbox-container")),n();var o={inline:!0,className:"monaco-inputbox-message"},a=t.message.formatContent?Object(s["b"])(t.message.content,o):Object(s["c"])(t.message.content,o);r["f"](a,t.classForType(t.message.type));var u=t.stylesForType(t.message.type);return a.style.backgroundColor=u.background?u.background.toString():"",a.style.color=u.foreground?u.foreground.toString():"",a.style.border=u.border?"1px solid "+u.border:"",r["q"](e,a),null},onHide:function(){t.state="closed"},layout:n}),this.state="open"}},e.prototype._hideMessage=function(){this.contextViewProvider&&("open"===this.state&&this.contextViewProvider.hideContextView(),this.state="idle")},e.prototype.onValueChange=function(){this._onDidChange.fire(this.value),this.validate(),this.updateMirror(),r["Y"](this.input,"empty",!this.value),"open"===this.state&&this.contextViewProvider&&this.contextViewProvider.layout()},e.prototype.updateMirror=function(){if(this.mirror){var t=this.value,e=t.charCodeAt(t.length-1),n=10===e?" ":"",i=t+n;i?this.mirror.textContent=t+n:this.mirror.innerHTML="&#160;",this.layout()}},e.prototype.style=function(t){this.inputBackground=t.inputBackground,this.inputForeground=t.inputForeground,this.inputBorder=t.inputBorder,this.inputValidationInfoBackground=t.inputValidationInfoBackground,this.inputValidationInfoForeground=t.inputValidationInfoForeground,this.inputValidationInfoBorder=t.inputValidationInfoBorder,this.inputValidationWarningBackground=t.inputValidationWarningBackground,this.inputValidationWarningForeground=t.inputValidationWarningForeground,this.inputValidationWarningBorder=t.inputValidationWarningBorder,this.inputValidationErrorBackground=t.inputValidationErrorBackground,this.inputValidationErrorForeground=t.inputValidationErrorForeground,this.inputValidationErrorBorder=t.inputValidationErrorBorder,this.applyStyles()},e.prototype.applyStyles=function(){var t=this.inputBackground?this.inputBackground.toString():"",e=this.inputForeground?this.inputForeground.toString():"",n=this.inputBorder?this.inputBorder.toString():"";this.element.style.backgroundColor=t,this.element.style.color=e,this.input.style.backgroundColor=t,this.input.style.color=e,this.element.style.borderWidth=n?"1px":"",this.element.style.borderStyle=n?"solid":"",this.element.style.borderColor=n},e.prototype.layout=function(){if(this.mirror){var t=this.cachedContentHeight;this.cachedContentHeight=r["G"](this.mirror),t!==this.cachedContentHeight&&(this.cachedHeight=Math.min(this.cachedContentHeight,this.maxHeight),this.input.style.height=this.cachedHeight+"px",this._onDidHeightChange.fire(this.cachedContentHeight))}},e.prototype.insertAtCursor=function(t){var e=this.inputElement,n=e.selectionStart,i=e.selectionEnd,o=e.value;null!==n&&null!==i&&(this.value=o.substr(0,n)+t+o.substr(i),e.setSelectionRange(n+1,n+1),this.layout())},e.prototype.dispose=function(){this._hideMessage(),this.message=null,this.actionbar&&this.actionbar.dispose(),t.prototype.dispose.call(this)},e}(c["a"]),w=function(t){function e(e,n,i){var o=t.call(this,e,n,i)||this;return o.history=new p["a"](i.history,100),o}return m(e,t),e.prototype.addToHistory=function(){this.value&&this.value!==this.getCurrentValue()&&this.history.add(this.value)},e.prototype.showNextValue=function(){this.history.has(this.value)||this.addToHistory();var t=this.getNextValue();t&&(t=t===this.value?this.getNextValue():t),t&&(this.value=t,a["c"](this.value))},e.prototype.showPreviousValue=function(){this.history.has(this.value)||this.addToHistory();var t=this.getPreviousValue();t&&(t=t===this.value?this.getPreviousValue():t),t&&(this.value=t,a["c"](this.value))},e.prototype.getCurrentValue=function(){var t=this.history.current();return t||(t=this.history.last(),this.history.next()),t},e.prototype.getPreviousValue=function(){return this.history.previous()||this.history.first()},e.prototype.getNextValue=function(){return this.history.next()||this.history.last()},e}(y)},d5e2:function(t,e,n){"use strict";n.d(e,"a",(function(){return p}));n("caa1");var i=n("dff7"),o=n("11f7"),r=n("d3ef"),s=n("1b7d"),a=n("308f"),u=n("fbcf"),l=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},t(e,n)};return function(e,n){function i(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),c=i["a"]("defaultLabel","input"),d=i["a"]("label.preserveCaseCheckbox","Preserve Case"),h=function(t){function e(e){return t.call(this,{actionClassName:"codicon-preserve-case",title:d+e.appendTitle,isChecked:e.isChecked,inputActiveOptionBorder:e.inputActiveOptionBorder,inputActiveOptionBackground:e.inputActiveOptionBackground})||this}return l(e,t),e}(u["a"]),p=function(t){function e(e,n,i,s){var u=t.call(this)||this;u._showOptionButtons=i,u.fixFocusOnOptionClickEnabled=!0,u.cachedOptionsWidth=0,u._onDidOptionChange=u._register(new a["a"]),u.onDidOptionChange=u._onDidOptionChange.event,u._onKeyDown=u._register(new a["a"]),u.onKeyDown=u._onKeyDown.event,u._onMouseDown=u._register(new a["a"]),u._onInput=u._register(new a["a"]),u._onKeyUp=u._register(new a["a"]),u._onPreserveCaseKeyDown=u._register(new a["a"]),u.onPreserveCaseKeyDown=u._onPreserveCaseKeyDown.event,u.contextViewProvider=n,u.placeholder=s.placeholder||"",u.validation=s.validation,u.label=s.label||c,u.inputActiveOptionBorder=s.inputActiveOptionBorder,u.inputActiveOptionBackground=s.inputActiveOptionBackground,u.inputBackground=s.inputBackground,u.inputForeground=s.inputForeground,u.inputBorder=s.inputBorder,u.inputValidationInfoBorder=s.inputValidationInfoBorder,u.inputValidationInfoBackground=s.inputValidationInfoBackground,u.inputValidationInfoForeground=s.inputValidationInfoForeground,u.inputValidationWarningBorder=s.inputValidationWarningBorder,u.inputValidationWarningBackground=s.inputValidationWarningBackground,u.inputValidationWarningForeground=s.inputValidationWarningForeground,u.inputValidationErrorBorder=s.inputValidationErrorBorder,u.inputValidationErrorBackground=s.inputValidationErrorBackground,u.inputValidationErrorForeground=s.inputValidationErrorForeground;var l=s.history||[],d=!!s.flexibleHeight,p=!!s.flexibleWidth,f=s.flexibleMaxHeight;u.domNode=document.createElement("div"),o["f"](u.domNode,"monaco-findInput"),u.inputBox=u._register(new r["a"](u.domNode,u.contextViewProvider,{ariaLabel:u.label||"",placeholder:u.placeholder||"",validationOptions:{validation:u.validation},inputBackground:u.inputBackground,inputForeground:u.inputForeground,inputBorder:u.inputBorder,inputValidationInfoBackground:u.inputValidationInfoBackground,inputValidationInfoForeground:u.inputValidationInfoForeground,inputValidationInfoBorder:u.inputValidationInfoBorder,inputValidationWarningBackground:u.inputValidationWarningBackground,inputValidationWarningForeground:u.inputValidationWarningForeground,inputValidationWarningBorder:u.inputValidationWarningBorder,inputValidationErrorBackground:u.inputValidationErrorBackground,inputValidationErrorForeground:u.inputValidationErrorForeground,inputValidationErrorBorder:u.inputValidationErrorBorder,history:l,flexibleHeight:d,flexibleWidth:p,flexibleMaxHeight:f})),u.preserveCase=u._register(new h({appendTitle:"",isChecked:!1,inputActiveOptionBorder:u.inputActiveOptionBorder,inputActiveOptionBackground:u.inputActiveOptionBackground})),u._register(u.preserveCase.onChange((function(t){u._onDidOptionChange.fire(t),!t&&u.fixFocusOnOptionClickEnabled&&u.inputBox.focus(),u.validate()}))),u._register(u.preserveCase.onKeyDown((function(t){u._onPreserveCaseKeyDown.fire(t)}))),u._showOptionButtons?u.cachedOptionsWidth=u.preserveCase.width():u.cachedOptionsWidth=0;var g=[u.preserveCase.domNode];u.onkeydown(u.domNode,(function(t){if(t.equals(15)||t.equals(17)||t.equals(9)){var e=g.indexOf(document.activeElement);if(e>=0){var n=-1;t.equals(17)?n=(e+1)%g.length:t.equals(15)&&(n=0===e?g.length-1:e-1),t.equals(9)?g[e].blur():n>=0&&g[n].focus(),o["c"].stop(t,!0)}}}));var m=document.createElement("div");return m.className="controls",m.style.display=u._showOptionButtons?"block":"none",m.appendChild(u.preserveCase.domNode),u.domNode.appendChild(m),e&&e.appendChild(u.domNode),u.onkeydown(u.inputBox.inputElement,(function(t){return u._onKeyDown.fire(t)})),u.onkeyup(u.inputBox.inputElement,(function(t){return u._onKeyUp.fire(t)})),u.oninput(u.inputBox.inputElement,(function(t){return u._onInput.fire()})),u.onmousedown(u.inputBox.inputElement,(function(t){return u._onMouseDown.fire(t)})),u}return l(e,t),e.prototype.enable=function(){o["P"](this.domNode,"disabled"),this.inputBox.enable(),this.preserveCase.enable()},e.prototype.disable=function(){o["f"](this.domNode,"disabled"),this.inputBox.disable(),this.preserveCase.disable()},e.prototype.setEnabled=function(t){t?this.enable():this.disable()},e.prototype.style=function(t){this.inputActiveOptionBorder=t.inputActiveOptionBorder,this.inputActiveOptionBackground=t.inputActiveOptionBackground,this.inputBackground=t.inputBackground,this.inputForeground=t.inputForeground,this.inputBorder=t.inputBorder,this.inputValidationInfoBackground=t.inputValidationInfoBackground,this.inputValidationInfoForeground=t.inputValidationInfoForeground,this.inputValidationInfoBorder=t.inputValidationInfoBorder,this.inputValidationWarningBackground=t.inputValidationWarningBackground,this.inputValidationWarningForeground=t.inputValidationWarningForeground,this.inputValidationWarningBorder=t.inputValidationWarningBorder,this.inputValidationErrorBackground=t.inputValidationErrorBackground,this.inputValidationErrorForeground=t.inputValidationErrorForeground,this.inputValidationErrorBorder=t.inputValidationErrorBorder,this.applyStyles()},e.prototype.applyStyles=function(){if(this.domNode){var t={inputActiveOptionBorder:this.inputActiveOptionBorder,inputActiveOptionBackground:this.inputActiveOptionBackground};this.preserveCase.style(t);var e={inputBackground:this.inputBackground,inputForeground:this.inputForeground,inputBorder:this.inputBorder,inputValidationInfoBackground:this.inputValidationInfoBackground,inputValidationInfoForeground:this.inputValidationInfoForeground,inputValidationInfoBorder:this.inputValidationInfoBorder,inputValidationWarningBackground:this.inputValidationWarningBackground,inputValidationWarningForeground:this.inputValidationWarningForeground,inputValidationWarningBorder:this.inputValidationWarningBorder,inputValidationErrorBackground:this.inputValidationErrorBackground,inputValidationErrorForeground:this.inputValidationErrorForeground,inputValidationErrorBorder:this.inputValidationErrorBorder};this.inputBox.style(e)}},e.prototype.select=function(){this.inputBox.select()},e.prototype.focus=function(){this.inputBox.focus()},e.prototype.getPreserveCase=function(){return this.preserveCase.checked},e.prototype.setPreserveCase=function(t){this.preserveCase.checked=t},e.prototype.focusOnPreserve=function(){this.preserveCase.focus()},e.prototype.validate=function(){this.inputBox&&this.inputBox.validate()},Object.defineProperty(e.prototype,"width",{set:function(t){this.inputBox.paddingRight=this.cachedOptionsWidth,this.inputBox.width=t,this.domNode.style.width=t+"px"},enumerable:!0,configurable:!0}),e.prototype.dispose=function(){t.prototype.dispose.call(this)},e}(s["a"])},da0c:function(t,e,n){"use strict";n.d(e,"b",(function(){return b})),n.d(e,"a",(function(){return y}));n("08768");var i,o=n("dff7"),r=n("3742"),s=n("f070"),a=n("5aa5"),u=n("11f7"),l=n("b835"),c=n("5fe7"),d=n("a666"),h=n("1898"),p=n("30db"),f=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},t(e,n)};return function(e,n){function i(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),g=function(){for(var t=0,e=0,n=arguments.length;e<n;e++)t+=arguments[e].length;var i=Array(t),o=0;for(e=0;e<n;e++)for(var r=arguments[e],s=0,a=r.length;s<a;s++,o++)i[o]=r[s];return i},m=/\(&([^\s&])\)|(^|[^&])&([^\s&])/,v=/(&amp;)?(&amp;)([^\s&])/g;(function(t){t[t["Right"]=0]="Right",t[t["Left"]=1]="Left"})(i||(i={}));var b=function(t){function e(e,n,i){var o=t.call(this,i||"submenu",e,"",!0)||this;return o.entries=n,o}return f(e,t),e}(s["a"]),y=function(t){function e(e,n,i){void 0===i&&(i={});var o=this;Object(u["f"])(e,"monaco-menu-container"),e.setAttribute("role","presentation");var r=document.createElement("div");Object(u["f"])(r,"monaco-menu"),r.setAttribute("role","presentation"),o=t.call(this,r,{orientation:2,actionViewItemProvider:function(t){return o.doGetActionViewItem(t,i,s)},context:i.context,actionRunner:i.actionRunner,ariaLabel:i.ariaLabel,triggerKeys:{keys:g([3],p["e"]?[10]:[]),keyDown:!0}})||this,o.menuElement=r,o.actionsList.setAttribute("role","menu"),o.actionsList.tabIndex=0,o.menuDisposables=o._register(new d["b"]),Object(u["j"])(r,u["d"].KEY_DOWN,(function(t){var e=new l["a"](t);e.equals(2)&&t.preventDefault()})),i.enableMnemonics&&o.menuDisposables.add(Object(u["j"])(r,u["d"].KEY_DOWN,(function(t){var e=t.key.toLocaleLowerCase();if(o.mnemonics.has(e)){u["c"].stop(t,!0);var n=o.mnemonics.get(e);if(1===n.length&&(n[0]instanceof _&&n[0].container&&o.focusItemByElement(n[0].container),n[0].onClick(t)),n.length>1){var i=n.shift();i&&i.container&&(o.focusItemByElement(i.container),n.push(i)),o.mnemonics.set(e,n)}}}))),p["d"]&&o._register(Object(u["j"])(r,u["d"].KEY_DOWN,(function(t){var e=new l["a"](t);e.equals(14)||e.equals(11)?(o.focusedItem=o.viewItems.length-1,o.focusNext(),u["c"].stop(t,!0)):(e.equals(13)||e.equals(12))&&(o.focusedItem=0,o.focusPrevious(),u["c"].stop(t,!0))}))),o._register(Object(u["j"])(o.domNode,u["d"].MOUSE_OUT,(function(t){var e=t.relatedTarget;Object(u["K"])(e,o.domNode)||(o.focusedItem=void 0,o.updateFocus(),t.stopPropagation())}))),o._register(Object(u["j"])(o.actionsList,u["d"].MOUSE_OVER,(function(t){var e=t.target;if(e&&Object(u["K"])(e,o.actionsList)&&e!==o.actionsList){while(e.parentElement!==o.actionsList&&null!==e.parentElement)e=e.parentElement;if(Object(u["I"])(e,"action-item")){var n=o.focusedItem;o.setFocusedItem(e),n!==o.focusedItem&&o.updateFocus()}}})));var s={parent:o};o.mnemonics=new Map,o.scrollableElement=o._register(new h["a"](r,{alwaysConsumeMouseWheel:!0,horizontal:2,vertical:3,verticalScrollbarSize:7,handleMouseWheel:!0,useShadows:!0}));var a=o.scrollableElement.getDomNode();return a.style.position="",o._register(Object(u["j"])(a,u["d"].MOUSE_UP,(function(t){t.preventDefault()}))),r.style.maxHeight=Math.max(10,window.innerHeight-e.getBoundingClientRect().top-30)+"px",o.push(n,{icon:!0,label:!0,isMenu:!0}),e.appendChild(o.scrollableElement.getDomNode()),o.scrollableElement.scanDomNode(),o.viewItems.filter((function(t){return!(t instanceof E)})).forEach((function(t,e,n){t.updatePositionInSet(e+1,n.length)})),o}return f(e,t),e.prototype.style=function(t){var e=this.getContainer(),n=t.foregroundColor?""+t.foregroundColor:"",i=t.backgroundColor?""+t.backgroundColor:"",o=t.borderColor?"1px solid "+t.borderColor:"",r=t.shadowColor?"0 2px 4px "+t.shadowColor:"";e.style.border=o,this.domNode.style.color=n,this.domNode.style.backgroundColor=i,e.style.boxShadow=r,this.viewItems&&this.viewItems.forEach((function(e){(e instanceof w||e instanceof E)&&e.style(t)}))},e.prototype.getContainer=function(){return this.scrollableElement.getDomNode()},Object.defineProperty(e.prototype,"onScroll",{get:function(){return this.scrollableElement.onScroll},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"scrollOffset",{get:function(){return this.menuElement.scrollTop},enumerable:!0,configurable:!0}),e.prototype.focusItemByElement=function(t){var e=this.focusedItem;this.setFocusedItem(t),e!==this.focusedItem&&this.updateFocus()},e.prototype.setFocusedItem=function(t){for(var e=0;e<this.actionsList.children.length;e++){var n=this.actionsList.children[e];if(t===n){this.focusedItem=e;break}}},e.prototype.updateFocus=function(e){t.prototype.updateFocus.call(this,e,!0),"undefined"!==typeof this.focusedItem&&this.scrollableElement.setScrollPosition({scrollTop:Math.round(this.menuElement.scrollTop)})},e.prototype.doGetActionViewItem=function(t,e,n){if(t instanceof a["d"])return new E(e.context,t,{icon:!0});if(t instanceof b){var i=new _(t,t.entries,n,e);if(e.enableMnemonics){var o=i.getMnemonic();if(o&&i.isEnabled()){var r=[];this.mnemonics.has(o)&&(r=this.mnemonics.get(o)),r.push(i),this.mnemonics.set(o,r)}}return i}var s={enableMnemonics:e.enableMnemonics};if(e.getKeyBinding){var u=e.getKeyBinding(t);if(u){var l=u.getLabel();l&&(s.keybinding=l)}}i=new w(e.context,t,s);if(e.enableMnemonics){o=i.getMnemonic();if(o&&i.isEnabled()){r=[];this.mnemonics.has(o)&&(r=this.mnemonics.get(o)),r.push(i),this.mnemonics.set(o,r)}}return i},e}(a["a"]),w=function(t){function e(e,n,i){void 0===i&&(i={});var o=this;if(i.isMenu=!0,o=t.call(this,n,n,i)||this,o.options=i,o.options.icon=void 0!==i.icon&&i.icon,o.options.label=void 0===i.label||i.label,o.cssClass="",o.options.label&&i.enableMnemonics){var r=o.getAction().label;if(r){var s=m.exec(r);s&&(o.mnemonic=(s[1]?s[1]:s[3]).toLocaleLowerCase())}}return o.runOnceToEnableMouseUp=new c["d"]((function(){o.element&&o._register(Object(u["j"])(o.element,u["d"].MOUSE_UP,(function(t){t.defaultPrevented||(u["c"].stop(t,!0),o.onClick(t))})))}),100),o._register(o.runOnceToEnableMouseUp),o}return f(e,t),e.prototype.render=function(e){t.prototype.render.call(this,e),this.element&&(this.container=e,this.item=Object(u["q"])(this.element,Object(u["a"])("a.action-menu-item")),this._action.id===a["d"].ID?this.item.setAttribute("role","presentation"):(this.item.setAttribute("role","menuitem"),this.mnemonic&&this.item.setAttribute("aria-keyshortcuts",""+this.mnemonic)),this.check=Object(u["q"])(this.item,Object(u["a"])("span.menu-item-check.codicon.codicon-check")),this.check.setAttribute("role","none"),this.label=Object(u["q"])(this.item,Object(u["a"])("span.action-label")),this.options.label&&this.options.keybinding&&(Object(u["q"])(this.item,Object(u["a"])("span.keybinding")).textContent=this.options.keybinding),this.runOnceToEnableMouseUp.schedule(),this.updateClass(),this.updateLabel(),this.updateTooltip(),this.updateEnabled(),this.updateChecked())},e.prototype.blur=function(){t.prototype.blur.call(this),this.applyStyle()},e.prototype.focus=function(){t.prototype.focus.call(this),this.item&&this.item.focus(),this.applyStyle()},e.prototype.updatePositionInSet=function(t,e){this.item&&(this.item.setAttribute("aria-posinset",""+t),this.item.setAttribute("aria-setsize",""+e))},e.prototype.updateLabel=function(){if(this.options.label){var t=this.getAction().label;if(t){var e=C(t);this.options.enableMnemonics||(t=e),this.label&&this.label.setAttribute("aria-label",e.replace(/&&/g,"&"));var n=m.exec(t);if(n){t=r["o"](t),v.lastIndex=0;var i=v.exec(t);while(i&&i[1])i=v.exec(t);i&&(t=t.substr(0,i.index)+'<u aria-hidden="true">'+i[3]+"</u>"+t.substr(i.index+i[0].length)),t=t.replace(/&amp;&amp;/g,"&amp;"),this.item&&this.item.setAttribute("aria-keyshortcuts",(n[1]?n[1]:n[3]).toLocaleLowerCase())}else t=t.replace(/&&/g,"&")}this.label&&(this.label.innerHTML=t.trim())}},e.prototype.updateTooltip=function(){var t=null;this.getAction().tooltip?t=this.getAction().tooltip:!this.options.label&&this.getAction().label&&this.options.icon&&(t=this.getAction().label,this.options.keybinding&&(t=o["a"]({key:"titleLabel",comment:["action title","action keybinding"]},"{0} ({1})",t,this.options.keybinding))),t&&this.item&&(this.item.title=t)},e.prototype.updateClass=function(){this.cssClass&&this.item&&Object(u["Q"])(this.item,this.cssClass),this.options.icon&&this.label?(this.cssClass=this.getAction().class||"",Object(u["f"])(this.label,"icon"),this.cssClass&&Object(u["g"])(this.label,this.cssClass),this.updateEnabled()):this.label&&Object(u["P"])(this.label,"icon")},e.prototype.updateEnabled=function(){this.getAction().enabled?(this.element&&Object(u["P"])(this.element,"disabled"),this.item&&(Object(u["P"])(this.item,"disabled"),this.item.tabIndex=0)):(this.element&&Object(u["f"])(this.element,"disabled"),this.item&&(Object(u["f"])(this.item,"disabled"),Object(u["S"])(this.item)))},e.prototype.updateChecked=function(){this.item&&(this.getAction().checked?(Object(u["f"])(this.item,"checked"),this.item.setAttribute("role","menuitemcheckbox"),this.item.setAttribute("aria-checked","true")):(Object(u["P"])(this.item,"checked"),this.item.setAttribute("role","menuitem"),this.item.setAttribute("aria-checked","false")))},e.prototype.getMnemonic=function(){return this.mnemonic},e.prototype.applyStyle=function(){if(this.menuStyle){var t=this.element&&Object(u["I"])(this.element,"focused"),e=t&&this.menuStyle.selectionForegroundColor?this.menuStyle.selectionForegroundColor:this.menuStyle.foregroundColor,n=t&&this.menuStyle.selectionBackgroundColor?this.menuStyle.selectionBackgroundColor:void 0,i=t&&this.menuStyle.selectionBorderColor?"thin solid "+this.menuStyle.selectionBorderColor:"";this.item&&(this.item.style.color=e?e.toString():"",this.item.style.backgroundColor=n?n.toString():""),this.check&&(this.check.style.color=e?e.toString():""),this.container&&(this.container.style.border=i)}},e.prototype.style=function(t){this.menuStyle=t,this.applyStyle()},e}(a["c"]),_=function(t){function e(e,n,o,r){var s=t.call(this,e,e,r)||this;return s.submenuActions=n,s.parentData=o,s.submenuOptions=r,s.mysubmenu=null,s.submenuDisposables=s._register(new d["b"]),s.mouseOver=!1,s.expandDirection=r&&void 0!==r.expandDirection?r.expandDirection:i.Right,s.showScheduler=new c["d"]((function(){s.mouseOver&&(s.cleanupExistingSubmenu(!1),s.createSubmenu(!1))}),250),s.hideScheduler=new c["d"]((function(){s.element&&!Object(u["K"])(document.activeElement,s.element)&&s.parentData.submenu===s.mysubmenu&&(s.parentData.parent.focus(!1),s.cleanupExistingSubmenu(!0))}),750),s}return f(e,t),e.prototype.render=function(e){var n=this;t.prototype.render.call(this,e),this.element&&(this.item&&(Object(u["f"])(this.item,"monaco-submenu-item"),this.item.setAttribute("aria-haspopup","true"),this.updateAriaExpanded("false"),this.submenuIndicator=Object(u["q"])(this.item,Object(u["a"])("span.submenu-indicator.codicon.codicon-chevron-right")),this.submenuIndicator.setAttribute("aria-hidden","true")),this._register(Object(u["j"])(this.element,u["d"].KEY_UP,(function(t){var e=new l["a"](t);(e.equals(17)||e.equals(3))&&(u["c"].stop(t,!0),n.createSubmenu(!0))}))),this._register(Object(u["j"])(this.element,u["d"].KEY_DOWN,(function(t){var e=new l["a"](t);document.activeElement===n.item&&(e.equals(17)||e.equals(3))&&u["c"].stop(t,!0)}))),this._register(Object(u["j"])(this.element,u["d"].MOUSE_OVER,(function(t){n.mouseOver||(n.mouseOver=!0,n.showScheduler.schedule())}))),this._register(Object(u["j"])(this.element,u["d"].MOUSE_LEAVE,(function(t){n.mouseOver=!1}))),this._register(Object(u["j"])(this.element,u["d"].FOCUS_OUT,(function(t){n.element&&!Object(u["K"])(document.activeElement,n.element)&&n.hideScheduler.schedule()}))),this._register(this.parentData.parent.onScroll((function(){n.parentData.parent.focus(!1),n.cleanupExistingSubmenu(!1)}))))},e.prototype.onClick=function(t){u["c"].stop(t,!0),this.cleanupExistingSubmenu(!1),this.createSubmenu(!0)},e.prototype.cleanupExistingSubmenu=function(t){this.parentData.submenu&&(t||this.parentData.submenu!==this.mysubmenu)&&(this.parentData.submenu.dispose(),this.parentData.submenu=void 0,this.updateAriaExpanded("false"),this.submenuContainer&&(this.submenuDisposables.clear(),this.submenuContainer=void 0))},e.prototype.createSubmenu=function(t){var e=this;if(void 0===t&&(t=!0),this.element)if(this.parentData.submenu)this.parentData.submenu.focus(!1);else{this.updateAriaExpanded("true"),this.submenuContainer=Object(u["q"])(this.element,Object(u["a"])("div.monaco-submenu")),Object(u["g"])(this.submenuContainer,"menubar-menu-items-holder","context-view");var n=getComputedStyle(this.parentData.parent.domNode),o=parseFloat(n.paddingTop||"0")||0;this.submenuContainer.style.top=this.element.offsetTop-this.parentData.parent.scrollOffset-o+"px",this.parentData.submenu=new y(this.submenuContainer,this.submenuActions,this.submenuOptions),this.menuStyle&&this.parentData.submenu.style(this.menuStyle);var r=this.element.getBoundingClientRect(),s=this.submenuContainer.getBoundingClientRect();this.expandDirection===i.Right?window.innerWidth<=r.right+s.width?(this.submenuContainer.style.left="10px",this.submenuContainer.style.top=this.element.offsetTop-this.parentData.parent.scrollOffset+r.height+"px"):(this.submenuContainer.style.left=this.element.offsetWidth+"px",this.submenuContainer.style.top=this.element.offsetTop-this.parentData.parent.scrollOffset-o+"px"):this.expandDirection===i.Left&&(this.submenuContainer.style.right=this.element.offsetWidth+"px",this.submenuContainer.style.left="auto",this.submenuContainer.style.top=this.element.offsetTop-this.parentData.parent.scrollOffset-o+"px"),this.submenuDisposables.add(Object(u["j"])(this.submenuContainer,u["d"].KEY_UP,(function(t){var n=new l["a"](t);n.equals(15)&&(u["c"].stop(t,!0),e.parentData.parent.focus(),e.cleanupExistingSubmenu(!0))}))),this.submenuDisposables.add(Object(u["j"])(this.submenuContainer,u["d"].KEY_DOWN,(function(t){var e=new l["a"](t);e.equals(15)&&u["c"].stop(t,!0)}))),this.submenuDisposables.add(this.parentData.submenu.onDidCancel((function(){e.parentData.parent.focus(),e.cleanupExistingSubmenu(!0)}))),this.parentData.submenu.focus(t),this.mysubmenu=this.parentData.submenu}},e.prototype.updateAriaExpanded=function(t){var e;this.item&&(null===(e=this.item)||void 0===e||e.setAttribute("aria-expanded",t))},e.prototype.applyStyle=function(){if(t.prototype.applyStyle.call(this),this.menuStyle){var e=this.element&&Object(u["I"])(this.element,"focused"),n=e&&this.menuStyle.selectionForegroundColor?this.menuStyle.selectionForegroundColor:this.menuStyle.foregroundColor;this.submenuIndicator&&(this.submenuIndicator.style.color=n?""+n:""),this.parentData.submenu&&this.parentData.submenu.style(this.menuStyle)}},e.prototype.dispose=function(){t.prototype.dispose.call(this),this.hideScheduler.dispose(),this.mysubmenu&&(this.mysubmenu.dispose(),this.mysubmenu=null),this.submenuContainer&&(this.submenuContainer=void 0)},e}(w),E=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return f(e,t),e.prototype.style=function(t){this.label&&(this.label.style.borderBottomColor=t.separatorColor?""+t.separatorColor:"")},e}(a["b"]);function C(t){var e=m,n=e.exec(t);if(!n)return t;var i=!n[1];return t.replace(e,i?"$2$3":"").trim()}},e2b8:function(t,e,n){},e32d:function(t,e,n){"use strict";n.d(e,"a",(function(){return o})),n.d(e,"b",(function(){return r}));var i=n("308f"),o=function(t,e,n){var o=function(t){return r.fire(t)},r=new i["a"]({onFirstListenerAdd:function(){t.addEventListener(e,o,n)},onLastListenerRemove:function(){t.removeEventListener(e,o,n)}});return r.event};function r(t){return i["b"].map(t,(function(t){return t.preventDefault(),t.stopPropagation(),t}))}},e473:function(t,e,n){"use strict";n.d(e,"a",(function(){return l})),n.d(e,"c",(function(){return c})),n.d(e,"b",(function(){return d}));var i=n("fbcf"),o=n("dff7"),r=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},t(e,n)};return function(e,n){function i(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),s=o["a"]("caseDescription","Match Case"),a=o["a"]("wordsDescription","Match Whole Word"),u=o["a"]("regexDescription","Use Regular Expression"),l=function(t){function e(e){return t.call(this,{actionClassName:"codicon-case-sensitive",title:s+e.appendTitle,isChecked:e.isChecked,inputActiveOptionBorder:e.inputActiveOptionBorder,inputActiveOptionBackground:e.inputActiveOptionBackground})||this}return r(e,t),e}(i["a"]),c=function(t){function e(e){return t.call(this,{actionClassName:"codicon-whole-word",title:a+e.appendTitle,isChecked:e.isChecked,inputActiveOptionBorder:e.inputActiveOptionBorder,inputActiveOptionBackground:e.inputActiveOptionBackground})||this}return r(e,t),e}(i["a"]),d=function(t){function e(e){return t.call(this,{actionClassName:"codicon-regex",title:u+e.appendTitle,isChecked:e.isChecked,inputActiveOptionBorder:e.inputActiveOptionBorder,inputActiveOptionBackground:e.inputActiveOptionBackground})||this}return r(e,t),e}(i["a"])},e757:function(t,e,n){"use strict";n.d(e,"a",(function(){return a}));var i=!1,o=null;function r(t){if(!t.parent||t.parent===t)return null;try{var e=t.location,n=t.parent.location;if(e.protocol!==n.protocol||e.hostname!==n.hostname||e.port!==n.port)return i=!0,null}catch(o){return i=!0,null}return t.parent}function s(t,e){for(var n,i=t.document.getElementsByTagName("iframe"),o=0,r=i.length;o<r;o++)if(n=i[o],n.contentWindow===e)return n;return null}var a=function(){function t(){}return t.getSameOriginWindowChain=function(){if(!o){o=[];var t,e=window;do{t=r(e),t?o.push({window:e,iframeElement:s(t,e)}):o.push({window:e,iframeElement:null}),e=t}while(e)}return o.slice(0)},t.hasDifferentOriginAncestor=function(){return o||this.getSameOriginWindowChain(),i},t.getPositionOfChildWindowRelativeToAncestorWindow=function(t,e){if(!e||t===e)return{top:0,left:0};for(var n=0,i=0,o=this.getSameOriginWindowChain(),r=0,s=o;r<s.length;r++){var a=s[r];if(a.window===e)break;if(!a.iframeElement)break;var u=a.iframeElement.getBoundingClientRect();n+=u.top,i+=u.left}return{top:n,left:i}},t}()},e818:function(t,e,n){"use strict";n.d(e,"a",(function(){return d}));n("caa1");var i=n("dff7"),o=n("11f7"),r=n("d3ef"),s=n("1b7d"),a=n("308f"),u=n("e473"),l=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},t(e,n)};return function(e,n){function i(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),c=i["a"]("defaultLabel","input"),d=function(t){function e(e,n,i,s){var l=t.call(this)||this;l._showOptionButtons=i,l.fixFocusOnOptionClickEnabled=!0,l._onDidOptionChange=l._register(new a["a"]),l.onDidOptionChange=l._onDidOptionChange.event,l._onKeyDown=l._register(new a["a"]),l.onKeyDown=l._onKeyDown.event,l._onMouseDown=l._register(new a["a"]),l.onMouseDown=l._onMouseDown.event,l._onInput=l._register(new a["a"]),l._onKeyUp=l._register(new a["a"]),l._onCaseSensitiveKeyDown=l._register(new a["a"]),l.onCaseSensitiveKeyDown=l._onCaseSensitiveKeyDown.event,l._onRegexKeyDown=l._register(new a["a"]),l.onRegexKeyDown=l._onRegexKeyDown.event,l._lastHighlightFindOptions=0,l.contextViewProvider=n,l.placeholder=s.placeholder||"",l.validation=s.validation,l.label=s.label||c,l.inputActiveOptionBorder=s.inputActiveOptionBorder,l.inputActiveOptionBackground=s.inputActiveOptionBackground,l.inputBackground=s.inputBackground,l.inputForeground=s.inputForeground,l.inputBorder=s.inputBorder,l.inputValidationInfoBorder=s.inputValidationInfoBorder,l.inputValidationInfoBackground=s.inputValidationInfoBackground,l.inputValidationInfoForeground=s.inputValidationInfoForeground,l.inputValidationWarningBorder=s.inputValidationWarningBorder,l.inputValidationWarningBackground=s.inputValidationWarningBackground,l.inputValidationWarningForeground=s.inputValidationWarningForeground,l.inputValidationErrorBorder=s.inputValidationErrorBorder,l.inputValidationErrorBackground=s.inputValidationErrorBackground,l.inputValidationErrorForeground=s.inputValidationErrorForeground;var d=s.appendCaseSensitiveLabel||"",h=s.appendWholeWordsLabel||"",p=s.appendRegexLabel||"",f=s.history||[],g=!!s.flexibleHeight,m=!!s.flexibleWidth,v=s.flexibleMaxHeight;l.domNode=document.createElement("div"),o["f"](l.domNode,"monaco-findInput"),l.inputBox=l._register(new r["a"](l.domNode,l.contextViewProvider,{placeholder:l.placeholder||"",ariaLabel:l.label||"",validationOptions:{validation:l.validation},inputBackground:l.inputBackground,inputForeground:l.inputForeground,inputBorder:l.inputBorder,inputValidationInfoBackground:l.inputValidationInfoBackground,inputValidationInfoForeground:l.inputValidationInfoForeground,inputValidationInfoBorder:l.inputValidationInfoBorder,inputValidationWarningBackground:l.inputValidationWarningBackground,inputValidationWarningForeground:l.inputValidationWarningForeground,inputValidationWarningBorder:l.inputValidationWarningBorder,inputValidationErrorBackground:l.inputValidationErrorBackground,inputValidationErrorForeground:l.inputValidationErrorForeground,inputValidationErrorBorder:l.inputValidationErrorBorder,history:f,flexibleHeight:g,flexibleWidth:m,flexibleMaxHeight:v})),l.regex=l._register(new u["b"]({appendTitle:p,isChecked:!1,inputActiveOptionBorder:l.inputActiveOptionBorder,inputActiveOptionBackground:l.inputActiveOptionBackground})),l._register(l.regex.onChange((function(t){l._onDidOptionChange.fire(t),!t&&l.fixFocusOnOptionClickEnabled&&l.inputBox.focus(),l.validate()}))),l._register(l.regex.onKeyDown((function(t){l._onRegexKeyDown.fire(t)}))),l.wholeWords=l._register(new u["c"]({appendTitle:h,isChecked:!1,inputActiveOptionBorder:l.inputActiveOptionBorder,inputActiveOptionBackground:l.inputActiveOptionBackground})),l._register(l.wholeWords.onChange((function(t){l._onDidOptionChange.fire(t),!t&&l.fixFocusOnOptionClickEnabled&&l.inputBox.focus(),l.validate()}))),l.caseSensitive=l._register(new u["a"]({appendTitle:d,isChecked:!1,inputActiveOptionBorder:l.inputActiveOptionBorder,inputActiveOptionBackground:l.inputActiveOptionBackground})),l._register(l.caseSensitive.onChange((function(t){l._onDidOptionChange.fire(t),!t&&l.fixFocusOnOptionClickEnabled&&l.inputBox.focus(),l.validate()}))),l._register(l.caseSensitive.onKeyDown((function(t){l._onCaseSensitiveKeyDown.fire(t)}))),l._showOptionButtons&&(l.inputBox.paddingRight=l.caseSensitive.width()+l.wholeWords.width()+l.regex.width());var b=[l.caseSensitive.domNode,l.wholeWords.domNode,l.regex.domNode];l.onkeydown(l.domNode,(function(t){if(t.equals(15)||t.equals(17)||t.equals(9)){var e=b.indexOf(document.activeElement);if(e>=0){var n=-1;t.equals(17)?n=(e+1)%b.length:t.equals(15)&&(n=0===e?b.length-1:e-1),t.equals(9)?b[e].blur():n>=0&&b[n].focus(),o["c"].stop(t,!0)}}}));var y=document.createElement("div");return y.className="controls",y.style.display=l._showOptionButtons?"block":"none",y.appendChild(l.caseSensitive.domNode),y.appendChild(l.wholeWords.domNode),y.appendChild(l.regex.domNode),l.domNode.appendChild(y),e&&e.appendChild(l.domNode),l.onkeydown(l.inputBox.inputElement,(function(t){return l._onKeyDown.fire(t)})),l.onkeyup(l.inputBox.inputElement,(function(t){return l._onKeyUp.fire(t)})),l.oninput(l.inputBox.inputElement,(function(t){return l._onInput.fire()})),l.onmousedown(l.inputBox.inputElement,(function(t){return l._onMouseDown.fire(t)})),l}return l(e,t),e.prototype.enable=function(){o["P"](this.domNode,"disabled"),this.inputBox.enable(),this.regex.enable(),this.wholeWords.enable(),this.caseSensitive.enable()},e.prototype.disable=function(){o["f"](this.domNode,"disabled"),this.inputBox.disable(),this.regex.disable(),this.wholeWords.disable(),this.caseSensitive.disable()},e.prototype.setFocusInputOnOptionClick=function(t){this.fixFocusOnOptionClickEnabled=t},e.prototype.setEnabled=function(t){t?this.enable():this.disable()},e.prototype.getValue=function(){return this.inputBox.value},e.prototype.setValue=function(t){this.inputBox.value!==t&&(this.inputBox.value=t)},e.prototype.style=function(t){this.inputActiveOptionBorder=t.inputActiveOptionBorder,this.inputActiveOptionBackground=t.inputActiveOptionBackground,this.inputBackground=t.inputBackground,this.inputForeground=t.inputForeground,this.inputBorder=t.inputBorder,this.inputValidationInfoBackground=t.inputValidationInfoBackground,this.inputValidationInfoForeground=t.inputValidationInfoForeground,this.inputValidationInfoBorder=t.inputValidationInfoBorder,this.inputValidationWarningBackground=t.inputValidationWarningBackground,this.inputValidationWarningForeground=t.inputValidationWarningForeground,this.inputValidationWarningBorder=t.inputValidationWarningBorder,this.inputValidationErrorBackground=t.inputValidationErrorBackground,this.inputValidationErrorForeground=t.inputValidationErrorForeground,this.inputValidationErrorBorder=t.inputValidationErrorBorder,this.applyStyles()},e.prototype.applyStyles=function(){if(this.domNode){var t={inputActiveOptionBorder:this.inputActiveOptionBorder,inputActiveOptionBackground:this.inputActiveOptionBackground};this.regex.style(t),this.wholeWords.style(t),this.caseSensitive.style(t);var e={inputBackground:this.inputBackground,inputForeground:this.inputForeground,inputBorder:this.inputBorder,inputValidationInfoBackground:this.inputValidationInfoBackground,inputValidationInfoForeground:this.inputValidationInfoForeground,inputValidationInfoBorder:this.inputValidationInfoBorder,inputValidationWarningBackground:this.inputValidationWarningBackground,inputValidationWarningForeground:this.inputValidationWarningForeground,inputValidationWarningBorder:this.inputValidationWarningBorder,inputValidationErrorBackground:this.inputValidationErrorBackground,inputValidationErrorForeground:this.inputValidationErrorForeground,inputValidationErrorBorder:this.inputValidationErrorBorder};this.inputBox.style(e)}},e.prototype.select=function(){this.inputBox.select()},e.prototype.focus=function(){this.inputBox.focus()},e.prototype.getCaseSensitive=function(){return this.caseSensitive.checked},e.prototype.setCaseSensitive=function(t){this.caseSensitive.checked=t},e.prototype.getWholeWords=function(){return this.wholeWords.checked},e.prototype.setWholeWords=function(t){this.wholeWords.checked=t},e.prototype.getRegex=function(){return this.regex.checked},e.prototype.setRegex=function(t){this.regex.checked=t,this.validate()},e.prototype.focusOnCaseSensitive=function(){this.caseSensitive.focus()},e.prototype.highlightFindOptions=function(){o["P"](this.domNode,"highlight-"+this._lastHighlightFindOptions),this._lastHighlightFindOptions=1-this._lastHighlightFindOptions,o["f"](this.domNode,"highlight-"+this._lastHighlightFindOptions)},e.prototype.validate=function(){this.inputBox.validate()},e.prototype.clearMessage=function(){this.inputBox.hideMessage()},e}(s["a"])},ee56:function(t,e,n){"use strict";n.d(e,"a",(function(){return s}));var i=n("aa3d"),o=n("561a"),r=n("3742"),s=function(){function t(t,e){this.supportCodicons=e,this.text="",this.title="",this.highlights=[],this.didEverRender=!1,this.domNode=document.createElement("span"),this.domNode.className="monaco-highlighted-label",t.appendChild(this.domNode)}return Object.defineProperty(t.prototype,"element",{get:function(){return this.domNode},enumerable:!0,configurable:!0}),t.prototype.set=function(e,n,o,r){void 0===n&&(n=[]),void 0===o&&(o=""),e||(e=""),r&&(e=t.escapeNewLines(e,n)),this.didEverRender&&this.text===e&&this.title===o&&i["e"](this.highlights,n)||(Array.isArray(n)||(n=[]),this.text=e,this.title=o,this.highlights=n,this.render())},t.prototype.render=function(){for(var t="",e=0,n=0,i=this.highlights;n<i.length;n++){var s=i[n];if(s.end!==s.start){if(e<s.start){t+="<span>";var a=this.text.substring(e,s.start);t+=this.supportCodicons?Object(o["c"])(Object(r["o"])(a)):Object(r["o"])(a),t+="</span>",e=s.end}s.extraClasses?t+='<span class="highlight '+s.extraClasses+'">':t+='<span class="highlight">';var u=this.text.substring(s.start,s.end);t+=this.supportCodicons?Object(o["c"])(Object(r["o"])(u)):Object(r["o"])(u),t+="</span>",e=s.end}}if(e<this.text.length){t+="<span>";u=this.text.substring(e);t+=this.supportCodicons?Object(o["c"])(Object(r["o"])(u)):Object(r["o"])(u),t+="</span>"}this.domNode.innerHTML=t,this.title?this.domNode.title=this.title:this.domNode.removeAttribute("title"),this.didEverRender=!0},t.escapeNewLines=function(t,e){var n=0,i=0;return t.replace(/\r\n|\r|\n/g,(function(t,o){i="\r\n"===t?-1:0,o+=n;for(var r=0,s=e;r<s.length;r++){var a=s[r];a.end<=o||(a.start>=o&&(a.start+=i),a.end>=o&&(a.end+=i))}return n+=i,"⏎"}))},t}()},fbcf:function(t,e,n){"use strict";n.d(e,"a",(function(){return c}));n("8899");var i=n("11f7"),o=n("1b7d"),r=n("ceb8"),s=n("308f"),a=n("aa3d"),u=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},t(e,n)};return function(e,n){function i(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),l={inputActiveOptionBorder:r["a"].fromHex("#007ACC00"),inputActiveOptionBackground:r["a"].fromHex("#0E639C50")},c=function(t){function e(e){var n=t.call(this)||this;return n._onChange=n._register(new s["a"]),n.onChange=n._onChange.event,n._onKeyDown=n._register(new s["a"]),n.onKeyDown=n._onKeyDown.event,n._opts=a["c"](e),a["g"](n._opts,l,!1),n._checked=n._opts.isChecked,n.domNode=document.createElement("div"),n.domNode.title=n._opts.title,n.domNode.className="monaco-custom-checkbox codicon "+(n._opts.actionClassName||"")+" "+(n._checked?"checked":"unchecked"),n.domNode.tabIndex=0,n.domNode.setAttribute("role","checkbox"),n.domNode.setAttribute("aria-checked",String(n._checked)),n.domNode.setAttribute("aria-label",n._opts.title),n.applyStyles(),n.onclick(n.domNode,(function(t){n.checked=!n._checked,n._onChange.fire(!1),t.preventDefault()})),n.ignoreGesture(n.domNode),n.onkeydown(n.domNode,(function(t){if(10===t.keyCode||3===t.keyCode)return n.checked=!n._checked,n._onChange.fire(!0),void t.preventDefault();n._onKeyDown.fire(t)})),n}return u(e,t),Object.defineProperty(e.prototype,"enabled",{get:function(){return"true"!==this.domNode.getAttribute("aria-disabled")},enumerable:!0,configurable:!0}),e.prototype.focus=function(){this.domNode.focus()},Object.defineProperty(e.prototype,"checked",{get:function(){return this._checked},set:function(t){this._checked=t,this.domNode.setAttribute("aria-checked",String(this._checked)),this._checked?this.domNode.classList.add("checked"):this.domNode.classList.remove("checked"),this.applyStyles()},enumerable:!0,configurable:!0}),e.prototype.width=function(){return 22},e.prototype.style=function(t){t.inputActiveOptionBorder&&(this._opts.inputActiveOptionBorder=t.inputActiveOptionBorder),t.inputActiveOptionBackground&&(this._opts.inputActiveOptionBackground=t.inputActiveOptionBackground),this.applyStyles()},e.prototype.applyStyles=function(){this.domNode&&(this.domNode.style.borderColor=this._checked&&this._opts.inputActiveOptionBorder?this._opts.inputActiveOptionBorder.toString():"transparent",this.domNode.style.backgroundColor=this._checked&&this._opts.inputActiveOptionBackground?this._opts.inputActiveOptionBackground.toString():"transparent")},e.prototype.enable=function(){this.domNode.tabIndex=0,this.domNode.setAttribute("aria-disabled",String(!1))},e.prototype.disable=function(){i["S"](this.domNode),this.domNode.setAttribute("aria-disabled",String(!0))},e}(o["a"])}}]);