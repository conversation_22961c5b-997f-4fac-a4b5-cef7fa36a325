(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-43c5d891"],{1885:function(e,t,a){"use strict";a("e6fe")},"3d55":function(e,t,a){},"857a":function(e,t,a){var n=a("e330"),o=a("1d80"),i=a("577e"),l=/"/g,s=n("".replace);e.exports=function(e,t,a,n){var r=i(o(e)),c="<"+t;return""!==a&&(c+=" "+a+'="'+s(i(n),l,"&quot;")+'"'),c+">"+r+"</"+t+">"}},af03:function(e,t,a){var n=a("d039");e.exports=function(e){return n((function(){var t=""[e]('"');return t!==t.toLowerCase()||t.split('"').length>3}))}},c96a:function(e,t,a){"use strict";var n=a("23e7"),o=a("857a"),i=a("af03");n({target:"String",proto:!0,forced:i("small")},{small:function(){return o(this,"small","","")}})},e6fe:function(e,t,a){},e8c9:function(e,t,a){"use strict";a("3d55")},f45e:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"bottom-menu-container"},[e._l(e.menuList,(function(t,n){return[a("div",{key:t.label,staticClass:"menu-item cursor-btn",class:{active:e.activeMenu.includes(n)&&t.highlight},on:{click:function(a){return e.toggleMenu(t)}}},[a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:t.name,placement:"top"}},[t.colorPicker?a("el-color-picker",{attrs:{"popper-class":"bottom-menu-color-picker",size:"mini"},on:{change:e.setMarkupColor},model:{value:e.selectColor,callback:function(t){e.selectColor=t},expression:"selectColor"}}):a("span",[a("CommonSVG",{attrs:{color:"#FFFFFF","icon-class":t.icon,size:16}})],1)],1)],1),t.afterBar?a("div",{staticClass:"vertical-split-line"}):e._e(),t.children?[a("transition",{attrs:{name:"sliderFade"}},[a("div",{directives:[{name:"show",rawName:"v-show",value:t.show,expression:"item.show"}],staticClass:"item-slider"},e._l(t.children,(function(n){return a("span",{key:n.label,staticClass:"cursor-btn child-menu",on:{click:function(a){return e.toggleChildMenu(n,t)}}},[a("CommonSVG",{attrs:{color:e.activeChildMenu[t.label]==n.label?"var(--theme)":"#FFFFFF",size:n.size,"icon-class":n.icon}})],1)})),0)])]:e._e()]}))],2)},o=[],i=(a("d3b7"),a("3ca3"),a("ddb0"),a("c96a"),a("caad"),a("2532"),{name:"MarkupMenu",components:{CommonSVG:function(){return a.e("chunk-51f90655").then(a.bind(null,"17d0"))}},data:function(){return{menuList:[{id:0,name:this.$t("bottomMenu.markup.tool.name"),label:"tool",icon:"notation_feature",show:!0,highlight:!0,children:[{name:this.$t("bottomMenu.markup.tool.extend.label"),label:"rectangle",icon:"tree_hidden_feature",size:20},{name:this.$t("bottomMenu.markup.tool.extend.label1"),label:"circular",icon:"round_feature",size:20},{name:this.$t("bottomMenu.markup.tool.extend.label2"),label:"text",icon:"text_feature",size:20},{name:this.$t("bottomMenu.markup.tool.extend.label3"),label:"arrow",icon:"arrow_feature",size:20},{name:this.$t("bottomMenu.markup.tool.extend.label4"),label:"line",icon:"free_marking_feature",size:20}]},{id:1,name:this.$t("bottomMenu.markup.brush.name"),label:"brush",icon:"line_coarse_feature",show:!1,highlight:!0,children:[{name:this.$t("bottomMenu.markup.brush.extend.label"),label:"small",icon:"point",size:16},{name:this.$t("bottomMenu.markup.brush.extend.label1"),label:"middle",icon:"point",size:24},{name:this.$t("bottomMenu.markup.brush.extend.label2"),label:"big",icon:"point",size:32}]},{id:2,name:this.$t("formRelational.color.label"),label:"color",icon:"rectangular",afterBar:!0,colorPicker:!0},{id:3,name:this.$t("bottomMenu.markup.clear"),label:"clear",icon:"clear_feature"},{id:5,name:this.$t("menuIconName.exit"),label:"quit",icon:"quit"},{id:4,name:this.$t("menuIconName.save"),label:"save",icon:"save_feature"}],selectColor:"#409eff",activeMenu:[0],brushSize:{small:2,middle:4,big:8},activeChildMenu:{tool:"rectangle",brush:"small"}}},created:function(){try{window.scene.mv.tools.markup.destroyMarkup()}catch(e){}},mounted:function(){window.scene.mv.tools.markup.drawRectangle(4,this.selectColor,this.brushSize.small)},methods:{setMarkupColor:function(e){var t=this.activeChildMenu.tool,a=this.brushSize[this.activeChildMenu.brush];this.handleMarkupFn(t,a)},toggleChildMenu:function(e,t){this.activeChildMenu[t.label]=e.label;var a=this.brushSize[this.activeChildMenu.brush];this.handleMarkupFn(e.label,a)},handleMarkupFn:function(e,t){switch(e){case"rectangle":window.scene.mv.tools.markup.drawRectangle(4,this.selectColor,t);break;case"circular":window.scene.mv.tools.markup.drawCircle(this.selectColor,t);break;case"text":window.scene.mv.tools.markup.drawText(this.selectColor,6*t,"SimSun");break;case"arrow":window.scene.mv.tools.markup.drawArrow(this.selectColor);break;case"line":window.scene.mv.tools.markup.drawPath(this.selectColor,t);break;case"small":case"middle":case"big":var a=this.activeChildMenu.tool,n=this.brushSize[e];this.handleMarkupFn(a,n);break}},toggleMenu:function(e){var t=e.id;switch(this.activeMenu.includes(t)?this.$set(this.activeMenu,t,-1):this.$set(this.activeMenu,t,t),e.hasOwnProperty("show")&&this.$set(this.menuList[t],"show",!this.menuList[t].show),e.label){case"clear":window.scene.mv.tools.markup.clear();break;case"save":this.$emit("saveData");break;case"quit":this.$store.commit("toggleBottomMenuActive","markup"),this.$store.commit("setActivedType","");break}}},beforeDestroy:function(){window.scene.mv.tools.markup.clear(),window.scene.mv.tools.markup.endEditMode(),window.scene.mv.tools.markup.destroyMarkup(),window.scene.render()}}),l=i,s=(a("e8c9"),a("1885"),a("2877")),r=Object(s["a"])(l,n,o,!1,null,"4d88f5a4",null);t["default"]=r.exports}}]);