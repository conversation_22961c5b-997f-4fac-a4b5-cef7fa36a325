(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6a16d5c3"],{"1f84":function(e,t,n){"use strict";n.r(t),n.d(t,"GotoDefinitionAtPositionEditorContribution",(function(){return k}));n("eb68");var o=n("dff7"),i=n("5fe7"),r=n("fdcc"),s=n("78bc"),a=n("5818"),c=n("6a89"),u=n("b707"),d=n("b2cc"),l=n("bd13"),h=n("a666"),f=n("b78f"),p=n("b7d0"),g=n("303e"),_=n("bc04"),m=n("f187"),v=n("6816"),b=n("7061"),y=n("ef8e"),w=function(e,t,n,o){var i,r=arguments.length,s=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)s=Reflect.decorate(e,t,n,o);else for(var a=e.length-1;a>=0;a--)(i=e[a])&&(s=(r<3?i(s):r>3?i(t,n,s):i(t,n))||s);return r>3&&s&&Object.defineProperty(t,n,s),s},C=function(e,t){return function(n,o){t(n,o,e)}},k=function(){function e(e,t,n){var o=this;this.textModelResolverService=t,this.modeService=n,this.toUnhook=new h["b"],this.toUnhookForKeyboard=new h["b"],this.linkDecorations=[],this.currentWordAtPosition=null,this.previousPromise=null,this.editor=e;var i=new v["a"](e);this.toUnhook.add(i),this.toUnhook.add(i.onMouseMoveOrRelevantKeyDown((function(e){var t=e[0],n=e[1];o.startFindDefinitionFromMouse(t,Object(y["n"])(n))}))),this.toUnhook.add(i.onExecute((function(e){o.isEnabled(e)&&o.gotoDefinition(e.target.position,e.hasSideBySideModifier).then((function(){o.removeLinkDecorations()}),(function(e){o.removeLinkDecorations(),Object(r["e"])(e)}))}))),this.toUnhook.add(i.onCancel((function(){o.removeLinkDecorations(),o.currentWordAtPosition=null})))}return e.get=function(t){return t.getContribution(e.ID)},e.prototype.startFindDefinitionFromCursor=function(e){var t=this;return this.startFindDefinition(e).then((function(){t.toUnhookForKeyboard.add(t.editor.onDidChangeCursorPosition((function(){t.currentWordAtPosition=null,t.removeLinkDecorations(),t.toUnhookForKeyboard.clear()}))),t.toUnhookForKeyboard.add(t.editor.onKeyDown((function(e){e&&(t.currentWordAtPosition=null,t.removeLinkDecorations(),t.toUnhookForKeyboard.clear())})))}))},e.prototype.startFindDefinitionFromMouse=function(e,t){if(!(9===e.target.type&&this.linkDecorations.length>0)){if(!this.editor.hasModel()||!this.isEnabled(e,t))return this.currentWordAtPosition=null,void this.removeLinkDecorations();var n=e.target.position;this.startFindDefinition(n)}},e.prototype.startFindDefinition=function(e){var t,n=this;this.toUnhookForKeyboard.clear();var a=e?null===(t=this.editor.getModel())||void 0===t?void 0:t.getWordAtPosition(e):null;if(!a)return this.currentWordAtPosition=null,this.removeLinkDecorations(),Promise.resolve(0);if(this.currentWordAtPosition&&this.currentWordAtPosition.startColumn===a.startColumn&&this.currentWordAtPosition.endColumn===a.endColumn&&this.currentWordAtPosition.word===a.word)return Promise.resolve(0);this.currentWordAtPosition=a;var u=new _["a"](this.editor,15);return this.previousPromise&&(this.previousPromise.cancel(),this.previousPromise=null),this.previousPromise=Object(i["f"])((function(t){return n.findDefinition(e,t)})),this.previousPromise.then((function(t){if(t&&t.length&&u.validate(n.editor))if(t.length>1)n.addDecoration(new c["a"](e.lineNumber,a.startColumn,e.lineNumber,a.endColumn),(new s["a"]).appendText(o["a"]("multipleResults","Click to show {0} definitions.",t.length)));else{var i=t[0];if(!i.uri)return;n.textModelResolverService.createModelReference(i.uri).then((function(t){if(t.object&&t.object.textEditorModel){var o=t.object.textEditorModel,r=i.range.startLineNumber;if(r<1||r>o.getLineCount())t.dispose();else{var u,d=n.getPreviewValue(o,r,i);u=i.originSelectionRange?c["a"].lift(i.originSelectionRange):new c["a"](e.lineNumber,a.startColumn,e.lineNumber,a.endColumn);var l=n.modeService.getModeIdByFilepathOrFirstLine(o.uri);n.addDecoration(u,(new s["a"]).appendCodeblock(l||"",d)),t.dispose()}}else t.dispose()}))}else n.removeLinkDecorations()})).then(void 0,r["e"])},e.prototype.getPreviewValue=function(t,n,o){var i=o.targetSelectionRange?o.range:this.getPreviewRangeBasedOnBrackets(t,n),r=i.endLineNumber-i.startLineNumber;r>=e.MAX_SOURCE_PREVIEW_LINES&&(i=this.getPreviewRangeBasedOnIndentation(t,n));var s=this.stripIndentationFromPreviewRange(t,n,i);return s},e.prototype.stripIndentationFromPreviewRange=function(e,t,n){for(var o=e.getLineFirstNonWhitespaceColumn(t),i=o,r=t+1;r<n.endLineNumber;r++){var s=e.getLineFirstNonWhitespaceColumn(r);i=Math.min(i,s)}var a=e.getValueInRange(n).replace(new RegExp("^\\s{"+(i-1)+"}","gm"),"").trim();return a},e.prototype.getPreviewRangeBasedOnIndentation=function(t,n){for(var o=t.getLineFirstNonWhitespaceColumn(n),i=Math.min(t.getLineCount(),n+e.MAX_SOURCE_PREVIEW_LINES),r=n+1;r<i;r++){var s=t.getLineFirstNonWhitespaceColumn(r);if(o===s)break}return new c["a"](n,1,r+1,1)},e.prototype.getPreviewRangeBasedOnBrackets=function(t,n){var o=Math.min(t.getLineCount(),n+e.MAX_SOURCE_PREVIEW_LINES),i=[],r=!0,s=t.findNextBracket(new b["a"](n,1));while(null!==s){if(0===i.length)i.push(s);else{var a=i[i.length-1];if(a.open[0]===s.open[0]&&a.isOpen&&!s.isOpen?i.pop():i.push(s),0===i.length){if(!r)return new c["a"](n,1,s.range.endLineNumber+1,1);r=!1}}var u=t.getLineMaxColumn(n),d=s.range.endLineNumber,l=s.range.endColumn;if(u===s.range.endColumn&&(d++,l=1),d>o)return new c["a"](n,1,o+1,1);s=t.findNextBracket(new b["a"](d,l))}return new c["a"](n,1,o+1,1)},e.prototype.addDecoration=function(e,t){var n={range:e,options:{inlineClassName:"goto-definition-link",hoverMessage:t}};this.linkDecorations=this.editor.deltaDecorations(this.linkDecorations,[n])},e.prototype.removeLinkDecorations=function(){this.linkDecorations.length>0&&(this.linkDecorations=this.editor.deltaDecorations(this.linkDecorations,[]))},e.prototype.isEnabled=function(e,t){return this.editor.hasModel()&&e.isNoneOrSingleMouseDown&&6===e.target.type&&(e.hasTriggerModifier||!!t&&t.keyCodeIsTriggerKey)&&u["f"].has(this.editor.getModel())},e.prototype.findDefinition=function(e,t){var n=this.editor.getModel();return n?Object(l["b"])(n,e,t):Promise.resolve(null)},e.prototype.gotoDefinition=function(e,t){var n=this;this.editor.setPosition(e);var o=new m["DefinitionAction"]({openToSide:t,openInPeek:!1,muteMessage:!0},{alias:"",label:"",id:"",precondition:void 0});return this.editor.invokeWithinContext((function(e){return o.run(e,n.editor)}))},e.prototype.dispose=function(){this.toUnhook.dispose()},e.ID="editor.contrib.gotodefinitionatposition",e.MAX_SOURCE_PREVIEW_LINES=8,e=w([C(1,f["a"]),C(2,a["a"])],e),e}();Object(d["h"])(k.ID,k),Object(p["e"])((function(e,t){var n=e.getColor(g["n"]);n&&t.addRule(".monaco-editor .goto-definition-link { color: "+n+" !important; }")}))},"29a1":function(e,t,n){},"418f":function(e,t,n){"use strict";n.d(t,"a",(function(){return le}));var o=n("dff7"),i=n("fdcc"),r=n("a666"),s=n("5717"),a=n("0a0f"),c=n("4fc3"),u=n("fbba"),d=n("03e8"),l=n("f68e"),h=(n("29a1"),n("11f7")),f=n("ceb8"),p=n("308f"),g=n("b589"),_=n("82c9"),m=n("d379"),v=n("6a89"),b=n("b57f"),y=n("b78f"),w=n("c4e3"),C=n("3bfb"),k=n("47cb"),O=n("b7d0"),M=n("a6d7"),D=n("debc"),P=n("6dec"),S=n("7e93"),x=n("ee56"),R=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),E=function(e,t,n,o){var i,r=arguments.length,s=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)s=Reflect.decorate(e,t,n,o);else for(var a=e.length-1;a>=0;a--)(i=e[a])&&(s=(r<3?i(s):r>3?i(t,n,s):i(t,n))||s);return r>3&&s&&Object.defineProperty(t,n,s),s},I=function(e,t){return function(n,o){t(n,o,e)}},N=function(){function e(e){this._resolverService=e}return e.prototype.hasChildren=function(e){return e instanceof l["c"]||e instanceof l["a"]&&!e.failure},e.prototype.getChildren=function(e){if(e instanceof l["c"])return e.groups;if(e instanceof l["a"])return e.resolve(this._resolverService).then((function(e){return e.children}));throw new Error("bad tree")},e=E([I(0,y["a"])],e),e}(),j=function(){function e(){}return e.prototype.getHeight=function(){return 23},e.prototype.getTemplateId=function(e){return e instanceof l["a"]?A.id:K.id},e}(),T=function(){function e(e){this._keybindingService=e}return e.prototype.getKeyboardNavigationLabel=function(e){if(e instanceof l["b"]){var t=e.parent.preview,n=t&&t.preview(e.range);if(n)return n.value}return Object(_["b"])(e.uri)},e=E([I(0,P["a"])],e),e}(),L=function(){function e(){}return e.prototype.getId=function(e){return e instanceof l["b"]?e.id:e.uri},e}(),F=function(e){function t(t,n,o){var i=e.call(this)||this;i._uriLabel=n;var r=document.createElement("div");return h["f"](r,"reference-file"),i.file=i._register(new w["a"](r,{supportHighlights:!0})),i.badge=new C["a"](h["q"](r,h["a"](".count"))),i._register(Object(M["a"])(i.badge,o)),t.appendChild(r),i}return R(t,e),t.prototype.set=function(e,t){var n=Object(_["d"])(e.uri);this.file.setLabel(Object(D["a"])(e.uri),this._uriLabel.getUriLabel(n,{relative:!0}),{title:this._uriLabel.getUriLabel(e.uri),matches:t});var i=e.children.length;this.badge.setCount(i),e.failure?this.badge.setTitleFormat(Object(o["a"])("referencesFailre","Failed to resolve file.")):i>1?this.badge.setTitleFormat(Object(o["a"])("referencesCount","{0} references",i)):this.badge.setTitleFormat(Object(o["a"])("referenceCount","{0} reference",i))},t=E([I(1,k["a"]),I(2,O["c"])],t),t}(r["a"]),A=function(){function e(t){this._instantiationService=t,this.templateId=e.id}return e.prototype.renderTemplate=function(e){return this._instantiationService.createInstance(F,e)},e.prototype.renderElement=function(e,t,n){n.set(e.element,Object(S["c"])(e.filterData))},e.prototype.disposeTemplate=function(e){e.dispose()},e.id="FileReferencesRenderer",e=E([I(0,a["a"])],e),e}(),W=function(){function e(e){this.label=new x["a"](e,!1)}return e.prototype.set=function(e,t){var n=e.parent.preview,o=n&&n.preview(e.range);if(o){var i=o.value,r=o.highlight;t&&!S["a"].isDefault(t)?(h["Y"](this.label.element,"referenceMatch",!1),this.label.set(i,Object(S["c"])(t))):(h["Y"](this.label.element,"referenceMatch",!0),this.label.set(i,[r]))}else this.label.set(Object(_["b"])(e.uri)+":"+(e.range.startLineNumber+1)+":"+(e.range.startColumn+1))},e}(),K=function(){function e(){this.templateId=e.id}return e.prototype.renderTemplate=function(e){return new W(e)},e.prototype.renderElement=function(e,t,n){n.set(e.element,e.filterData)},e.prototype.disposeTemplate=function(){},e.id="OneReferenceRenderer",e}(),H=function(){function e(){}return e.prototype.getAriaLabel=function(e){return e.ariaMessage},e}(),V=n("93d9"),U=n("303e"),B=n("88d4"),G=n("e385"),q=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),z=function(e,t,n,o){var i,r=arguments.length,s=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)s=Reflect.decorate(e,t,n,o);else for(var a=e.length-1;a>=0;a--)(i=e[a])&&(s=(r<3?i(s):r>3?i(t,n,s):i(t,n))||s);return r>3&&s&&Object.defineProperty(t,n,s),s},Z=function(e,t){return function(n,o){t(n,o,e)}},J=function(e,t,n,o){function i(e){return e instanceof n?e:new n((function(t){t(e)}))}return new(n||(n=Promise))((function(n,r){function s(e){try{c(o.next(e))}catch(t){r(t)}}function a(e){try{c(o["throw"](e))}catch(t){r(t)}}function c(e){e.done?n(e.value):i(e.value).then(s,a)}c((o=o.apply(e,t||[])).next())}))},X=function(e,t){var n,o,i,r,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function a(e){return function(t){return c([e,t])}}function c(r){if(n)throw new TypeError("Generator is already executing.");while(s)try{if(n=1,o&&(i=2&r[0]?o["return"]:r[0]?o["throw"]||((i=o["return"])&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return s.label++,{value:r[1],done:!1};case 5:s.label++,o=r[1],r=[0];continue;case 7:r=s.ops.pop(),s.trys.pop();continue;default:if(i=s.trys,!(i=i.length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){s=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){s.label=r[1];break}if(6===r[0]&&s.label<i[1]){s.label=i[1],i=r;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(r);break}i[2]&&s.ops.pop(),s.trys.pop();continue}r=t.call(e,s)}catch(a){r=[6,a],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}},Q=function(){function e(e,t){var n=this;this._editor=e,this._model=t,this._decorations=new Map,this._decorationIgnoreSet=new Set,this._callOnDispose=new r["b"],this._callOnModelChange=new r["b"],this._callOnDispose.add(this._editor.onDidChangeModel((function(){return n._onModelChanged()}))),this._onModelChanged()}return e.prototype.dispose=function(){this._callOnModelChange.dispose(),this._callOnDispose.dispose(),this.removeDecorations()},e.prototype._onModelChanged=function(){this._callOnModelChange.clear();var e=this._editor.getModel();if(e)for(var t=0,n=this._model.groups;t<n.length;t++){var o=n[t];if(Object(_["e"])(o.uri,e.uri))return void this._addDecorations(o)}},e.prototype._addDecorations=function(t){var n=this;if(this._editor.hasModel()){this._callOnModelChange.add(this._editor.getModel().onDidChangeDecorations((function(e){return n._onDecorationChanged()})));for(var o=[],i=[],r=0,s=t.children.length;r<s;r++){var a=t.children[r];this._decorationIgnoreSet.has(a.id)||(o.push({range:a.range,options:e.DecorationOptions}),i.push(r))}var c=this._editor.deltaDecorations([],o);for(r=0;r<c.length;r++)this._decorations.set(c[r],t.children[i[r]])}},e.prototype._onDecorationChanged=function(){var e=this,t=[],n=this._editor.getModel();if(n){this._decorations.forEach((function(o,i){var r=n.getDecorationRange(i);if(r){var s=!1;if(!v["a"].equalsRange(r,o.range)){if(v["a"].spansMultipleLines(r))s=!0;else{var a=o.range.endColumn-o.range.startColumn,c=r.endColumn-r.startColumn;a!==c&&(s=!0)}s?(e._decorationIgnoreSet.add(o.id),t.push(i)):o.range=r}}}));for(var o=0,i=t.length;o<i;o++)this._decorations.delete(t[o]);this._editor.deltaDecorations(t,[])}},e.prototype.removeDecorations=function(){var e=[];this._decorations.forEach((function(t,n){e.push(n)})),this._editor.deltaDecorations(e,[]),this._decorations.clear()},e.DecorationOptions=b["a"].register({stickiness:1,className:"reference-decoration"}),e}(),Y=function(){function e(){this.ratio=.7,this.heightInLines=18}return e.fromJSON=function(e){var t,n;try{var o=JSON.parse(e);t=o.ratio,n=o.heightInLines}catch(i){}return{ratio:t||.7,heightInLines:n||18}},e}(),$=function(e){function t(t,n,o,i,s,a,c,u){var d=e.call(this,t,{showFrame:!1,showArrow:!0,isResizeable:!0,isAccessible:!0})||this;return d._defaultTreeKeyboardSupport=n,d.layoutData=o,d._textModelResolverService=s,d._instantiationService=a,d._peekViewService=c,d._uriLabel=u,d._disposeOnNewModel=new r["b"],d._callOnDispose=new r["b"],d._onDidSelectReference=new p["a"],d.onDidSelectReference=d._onDidSelectReference.event,d._dim={height:0,width:0},d._applyTheme(i.getTheme()),d._callOnDispose.add(i.onThemeChange(d._applyTheme.bind(d))),d._peekViewService.addExclusiveWidget(t,d),d.create(),d}return q(t,e),t.prototype.dispose=function(){this.setModel(void 0),this._callOnDispose.dispose(),this._disposeOnNewModel.dispose(),Object(r["f"])(this._preview),Object(r["f"])(this._previewNotAvailableMessage),Object(r["f"])(this._tree),Object(r["f"])(this._previewModelReference),this._splitView.dispose(),e.prototype.dispose.call(this)},t.prototype._applyTheme=function(e){var t=e.getColor(B["e"])||f["a"].transparent;this.style({arrowColor:t,frameColor:t,headerBackgroundColor:e.getColor(B["p"])||f["a"].transparent,primaryHeadingColor:e.getColor(B["q"]),secondaryHeadingColor:e.getColor(B["r"])})},t.prototype.show=function(t){this.editor.revealRangeInCenterIfOutsideViewport(t,0),e.prototype.show.call(this,t,this.layoutData.heightInLines||18)},t.prototype.focusOnReferenceTree=function(){this._tree.domFocus()},t.prototype.focusOnPreviewEditor=function(){this._preview.focus()},t.prototype.isPreviewEditorFocused=function(){return this._preview.hasTextFocus()},t.prototype._onTitleClick=function(e){this._preview&&this._preview.getModel()&&this._onDidSelectReference.fire({element:this._getFocusedReference(),kind:e.ctrlKey||e.metaKey||e.altKey?"side":"open",source:"title"})},t.prototype._fillBody=function(e){var t=this;this.setCssClass("reference-zone-widget"),this._messageContainer=h["q"](e,h["a"]("div.messages")),h["J"](this._messageContainer),this._splitView=new G["b"](e,{orientation:1}),this._previewContainer=h["q"](e,h["a"]("div.preview.inline"));var n={scrollBeyondLastLine:!1,scrollbar:{verticalScrollbarSize:14,horizontal:"auto",useShadows:!0,verticalHasArrows:!1,horizontalHasArrows:!1,alwaysConsumeMouseWheel:!1},overviewRulerLanes:2,fixedOverflowWidgets:!0,minimap:{enabled:!1}};this._preview=this._instantiationService.createInstance(m["a"],this._previewContainer,n,this.editor),h["J"](this._previewContainer),this._previewNotAvailableMessage=b["b"].createFromString(o["a"]("missingPreviewMessage","no preview available")),this._treeContainer=h["q"](e,h["a"]("div.ref-tree.inline"));var i={ariaLabel:o["a"]("treeAriaLabel","References"),keyboardSupport:this._defaultTreeKeyboardSupport,accessibilityProvider:new H,keyboardNavigationLabelProvider:this._instantiationService.createInstance(T),identityProvider:new L,overrideStyles:{listBackground:B["j"]}};this._tree=this._instantiationService.createInstance(V["c"],"ReferencesWidget",this._treeContainer,new j,[this._instantiationService.createInstance(A),this._instantiationService.createInstance(K)],this._instantiationService.createInstance(N),i),this._splitView.addView({onDidChange:p["b"].None,element:this._previewContainer,minimumSize:200,maximumSize:Number.MAX_VALUE,layout:function(e){t._preview.layout({height:t._dim.height,width:e})}},G["a"].Distribute),this._splitView.addView({onDidChange:p["b"].None,element:this._treeContainer,minimumSize:100,maximumSize:Number.MAX_VALUE,layout:function(e){t._treeContainer.style.height=t._dim.height+"px",t._treeContainer.style.width=e+"px",t._tree.layout(t._dim.height,e)}},G["a"].Distribute),this._disposables.add(this._splitView.onDidSashChange((function(){t._dim.width&&(t.layoutData.ratio=t._splitView.getViewSize(0)/t._dim.width)}),void 0));var r=function(e,n){e instanceof l["b"]&&("show"===n&&t._revealReference(e,!1),t._onDidSelectReference.fire({element:e,kind:n,source:"tree"}))};this._tree.onDidChangeFocus((function(e){r(e.elements[0],"show")})),this._tree.onDidOpen((function(e){e.browserEvent instanceof MouseEvent&&(e.browserEvent.ctrlKey||e.browserEvent.metaKey||e.browserEvent.altKey)?r(e.elements[0],"side"):e.browserEvent instanceof KeyboardEvent||e.browserEvent instanceof MouseEvent&&2===e.browserEvent.detail||2===e.browserEvent.tapCount?r(e.elements[0],"goto"):r(e.elements[0],"show")})),h["J"](this._treeContainer)},t.prototype._onWidth=function(e){this._dim&&this._doLayoutBody(this._dim.height,e)},t.prototype._doLayoutBody=function(t,n){e.prototype._doLayoutBody.call(this,t,n),this._dim={height:t,width:n},this.layoutData.heightInLines=this._viewZone?this._viewZone.heightInLines:this.layoutData.heightInLines,this._splitView.layout(n),this._splitView.resizeView(0,n*this.layoutData.ratio)},t.prototype.setSelection=function(e){var t=this;return this._revealReference(e,!0).then((function(){t._model&&(t._tree.setSelection([e]),t._tree.setFocus([e]))}))},t.prototype.setModel=function(e){return this._disposeOnNewModel.clear(),this._model=e,this._model?this._onNewModel():Promise.resolve()},t.prototype._onNewModel=function(){var e=this;return this._model?this._model.isEmpty?(this.setTitle(""),this._messageContainer.innerHTML=o["a"]("noResults","No results"),h["X"](this._messageContainer),Promise.resolve(void 0)):(h["J"](this._messageContainer),this._decorationsManager=new Q(this._preview,this._model),this._disposeOnNewModel.add(this._decorationsManager),this._disposeOnNewModel.add(this._model.onDidChangeReferenceRange((function(t){return e._tree.rerender(t)}))),this._disposeOnNewModel.add(this._preview.onMouseDown((function(t){var n=t.event,o=t.target;if(2===n.detail){var i=e._getFocusedReference();i&&e._onDidSelectReference.fire({element:{uri:i.uri,range:o.range},kind:n.ctrlKey||n.metaKey||n.altKey?"side":"open",source:"editor"})}}))),h["f"](this.container,"results-loaded"),h["X"](this._treeContainer),h["X"](this._previewContainer),this._splitView.layout(this._dim.width),this.focusOnReferenceTree(),this._tree.setInput(1===this._model.groups.length?this._model.groups[0]:this._model)):Promise.resolve(void 0)},t.prototype._getFocusedReference=function(){var e=this._tree.getFocus()[0];return e instanceof l["b"]?e:e instanceof l["a"]&&e.children.length>0?e.children[0]:void 0},t.prototype._revealReference=function(e,t){return J(this,void 0,void 0,(function(){var n,i,s,a,c;return X(this,(function(u){switch(u.label){case 0:return this._revealedReference===e?[2]:(this._revealedReference=e,e.uri.scheme!==g["b"].inMemory?this.setTitle(Object(_["c"])(e.uri),this._uriLabel.getUriLabel(Object(_["d"])(e.uri))):this.setTitle(o["a"]("peekView.alternateTitle","References")),n=this._textModelResolverService.createModelReference(e.uri),this._tree.getInput()!==e.parent?[3,1]:(this._tree.reveal(e),[3,3]));case 1:return t&&this._tree.reveal(e.parent),[4,this._tree.expand(e.parent)];case 2:u.sent(),this._tree.reveal(e),u.label=3;case 3:return[4,n];case 4:return i=u.sent(),this._model?(Object(r["f"])(this._previewModelReference),s=i.object,s?(a=this._preview.getModel()===s.textEditorModel?0:1,c=v["a"].lift(e.range).collapseToStart(),this._previewModelReference=i,this._preview.setModel(s.textEditorModel),this._preview.setSelection(c),this._preview.revealRangeInCenter(c,a)):(this._preview.setModel(this._previewNotAvailableMessage),i.dispose()),[2]):(i.dispose(),[2])}}))}))},t=z([Z(3,O["c"]),Z(4,y["a"]),Z(5,a["a"]),Z(6,B["a"]),Z(7,k["a"])],t),t}(B["c"]);Object(O["e"])((function(e,t){var n=e.getColor(B["m"]);n&&t.addRule(".monaco-editor .reference-zone-widget .ref-tree .referenceMatch .highlight { background-color: "+n+"; }");var o=e.getColor(B["h"]);o&&t.addRule(".monaco-editor .reference-zone-widget .preview .reference-decoration { background-color: "+o+"; }");var i=e.getColor(B["i"]);i&&t.addRule(".monaco-editor .reference-zone-widget .preview .reference-decoration { border: 2px solid "+i+"; box-sizing: border-box; }");var r=e.getColor(U["b"]);r&&t.addRule(".monaco-editor .reference-zone-widget .ref-tree .referenceMatch .highlight { border: 1px dotted "+r+"; box-sizing: border-box; }");var s=e.getColor(B["j"]);s&&t.addRule(".monaco-editor .reference-zone-widget .ref-tree { background-color: "+s+"; }");var a=e.getColor(B["l"]);a&&t.addRule(".monaco-editor .reference-zone-widget .ref-tree { color: "+a+"; }");var c=e.getColor(B["k"]);c&&t.addRule(".monaco-editor .reference-zone-widget .ref-tree .reference-file { color: "+c+"; }");var u=e.getColor(B["n"]);u&&t.addRule(".monaco-editor .reference-zone-widget .ref-tree .monaco-list:focus .monaco-list-rows > .monaco-list-row.selected:not(.highlighted) { background-color: "+u+"; }");var d=e.getColor(B["o"]);d&&t.addRule(".monaco-editor .reference-zone-widget .ref-tree .monaco-list:focus .monaco-list-rows > .monaco-list-row.selected:not(.highlighted) { color: "+d+" !important; }");var l=e.getColor(B["f"]);l&&t.addRule(".monaco-editor .reference-zone-widget .preview .monaco-editor .monaco-editor-background,.monaco-editor .reference-zone-widget .preview .monaco-editor .inputarea.ime-input {\tbackground-color: "+l+";}");var h=e.getColor(B["g"]);h&&t.addRule(".monaco-editor .reference-zone-widget .preview .monaco-editor .margin {\tbackground-color: "+h+";}")}));var ee=n("7061"),te=n("b0cd"),ne=n("5fe7"),oe=n("9eb8"),ie=n("fe45"),re=n("9e74"),se=function(e,t,n,o){var i,r=arguments.length,s=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)s=Reflect.decorate(e,t,n,o);else for(var a=e.length-1;a>=0;a--)(i=e[a])&&(s=(r<3?i(s):r>3?i(t,n,s):i(t,n))||s);return r>3&&s&&Object.defineProperty(t,n,s),s},ae=function(e,t){return function(n,o){t(n,o,e)}},ce=function(e,t,n,o){function i(e){return e instanceof n?e:new n((function(t){t(e)}))}return new(n||(n=Promise))((function(n,r){function s(e){try{c(o.next(e))}catch(t){r(t)}}function a(e){try{c(o["throw"](e))}catch(t){r(t)}}function c(e){e.done?n(e.value):i(e.value).then(s,a)}c((o=o.apply(e,t||[])).next())}))},ue=function(e,t){var n,o,i,r,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function a(e){return function(t){return c([e,t])}}function c(r){if(n)throw new TypeError("Generator is already executing.");while(s)try{if(n=1,o&&(i=2&r[0]?o["return"]:r[0]?o["throw"]||((i=o["return"])&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return s.label++,{value:r[1],done:!1};case 5:s.label++,o=r[1],r=[0];continue;case 7:r=s.ops.pop(),s.trys.pop();continue;default:if(i=s.trys,!(i=i.length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){s=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){s.label=r[1];break}if(6===r[0]&&s.label<i[1]){s.label=i[1],i=r;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(r);break}i[2]&&s.ops.pop(),s.trys.pop();continue}r=t.call(e,s)}catch(a){r=[6,a],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}},de=new c["d"]("referenceSearchVisible",!1),le=function(){function e(e,t,n,o,i,s,a,c){this._defaultTreeKeyboardSupport=e,this._editor=t,this._editorService=o,this._notificationService=i,this._instantiationService=s,this._storageService=a,this._configurationService=c,this._disposables=new r["b"],this._requestIdPool=0,this._ignoreModelChangeEvent=!1,this._referenceSearchVisible=de.bindTo(n)}return e.get=function(t){return t.getContribution(e.ID)},e.prototype.dispose=function(){this._referenceSearchVisible.reset(),this._disposables.dispose(),Object(r["f"])(this._widget),Object(r["f"])(this._model),this._widget=void 0,this._model=void 0},e.prototype.toggleWidget=function(e,t,n){var i,r=this;if(this._widget&&(i=this._widget.position),this.closeWidget(),!i||!e.containsPosition(i)){this._peekMode=n,this._referenceSearchVisible.set(!0),this._disposables.add(this._editor.onDidChangeModelLanguage((function(){r.closeWidget()}))),this._disposables.add(this._editor.onDidChangeModel((function(){r._ignoreModelChangeEvent||r.closeWidget()})));var s="peekViewLayout",a=Y.fromJSON(this._storageService.get(s,0,"{}"));this._widget=this._instantiationService.createInstance($,this._editor,this._defaultTreeKeyboardSupport,a),this._widget.setTitle(o["a"]("labelLoading","Loading...")),this._widget.show(e),this._disposables.add(this._widget.onDidClose((function(){t.cancel(),r._widget&&(r._storageService.store(s,JSON.stringify(r._widget.layoutData),0),r._widget=void 0),r.closeWidget()}))),this._disposables.add(this._widget.onDidSelectReference((function(e){var t=e.element,o=e.kind;if(t)switch(o){case"open":"editor"===e.source&&r._configurationService.getValue("editor.stablePeek")||r.openReference(t,!1);break;case"side":r.openReference(t,!0);break;case"goto":n?r._gotoReference(t):r.openReference(t,!1);break}})));var c=++this._requestIdPool;t.then((function(t){if(c===r._requestIdPool&&r._widget)return r._model&&r._model.dispose(),r._model=t,r._widget.setModel(r._model).then((function(){if(r._widget&&r._model&&r._editor.hasModel()){r._model.isEmpty?r._widget.setMetaTitle(""):r._widget.setMetaTitle(o["a"]("metaTitle.N","{0} ({1})",r._model.title,r._model.references.length));var t=r._editor.getModel().uri,n=new ee["a"](e.startLineNumber,e.startColumn),i=r._model.nearestReference(t,n);if(i)return r._widget.setSelection(i).then((function(){r._widget&&"editor"===r._editor.getOption(65)&&r._widget.focusOnPreviewEditor()}))}}))}),(function(e){r._notificationService.error(e)}))}},e.prototype.changeFocusBetweenPreviewAndReferences=function(){this._widget&&(this._widget.isPreviewEditorFocused()?this._widget.focusOnReferenceTree():this._widget.focusOnPreviewEditor())},e.prototype.goToNextOrPreviousReference=function(e){return ce(this,void 0,void 0,(function(){var t,n,o,i,r;return ue(this,(function(s){switch(s.label){case 0:return this._editor.hasModel()&&this._model&&this._widget?(t=this._widget.position,t?(n=this._model.nearestReference(this._editor.getModel().uri,t),n?(o=this._model.nextOrPreviousReference(n,e),i=this._editor.hasTextFocus(),r=this._widget.isPreviewEditorFocused(),[4,this._widget.setSelection(o)]):[2]):[2]):[2];case 1:return s.sent(),[4,this._gotoReference(o)];case 2:return s.sent(),i?this._editor.focus():this._widget&&r&&this._widget.focusOnPreviewEditor(),[2]}}))}))},e.prototype.closeWidget=function(e){void 0===e&&(e=!0),this._referenceSearchVisible.reset(),this._disposables.clear(),Object(r["f"])(this._widget),Object(r["f"])(this._model),this._widget=void 0,this._model=void 0,e&&this._editor.focus(),this._requestIdPool+=1},e.prototype._gotoReference=function(t){var n=this;this._widget&&this._widget.hide(),this._ignoreModelChangeEvent=!0;var o=v["a"].lift(t.range).collapseToStart();return this._editorService.openCodeEditor({resource:t.uri,options:{selection:o}},this._editor).then((function(t){var i;if(n._ignoreModelChangeEvent=!1,t&&n._widget)if(n._editor===t)n._widget.show(o),n._widget.focusOnReferenceTree();else{var r=e.get(t),s=n._model.clone();n.closeWidget(),t.focus(),r.toggleWidget(o,Object(ne["f"])((function(e){return Promise.resolve(s)})),null!==(i=n._peekMode)&&void 0!==i&&i)}else n.closeWidget()}),(function(e){n._ignoreModelChangeEvent=!1,Object(i["e"])(e)}))},e.prototype.openReference=function(e,t){t||this.closeWidget();var n=e.uri,o=e.range;this._editorService.openCodeEditor({resource:n,options:{selection:o}},this._editor,t)},e.ID="editor.contrib.referencesController",e=se([ae(2,c["c"]),ae(3,s["a"]),ae(4,te["a"]),ae(5,a["a"]),ae(6,d["a"]),ae(7,u["a"])],e),e}();function he(e,t){var n=Object(B["d"])(e);if(n){var o=le.get(n);o&&t(o)}}oe["a"].registerCommandAndKeybindingRule({id:"togglePeekWidgetFocus",weight:100,primary:Object(ie["a"])(2089,60),when:c["a"].or(de,B["b"].inPeekEditor),handler:function(e){he(e,(function(e){e.changeFocusBetweenPreviewAndReferences()}))}}),oe["a"].registerCommandAndKeybindingRule({id:"goToNextReference",weight:90,primary:62,secondary:[70],when:c["a"].or(de,B["b"].inPeekEditor),handler:function(e){he(e,(function(e){e.goToNextOrPreviousReference(!0)}))}}),oe["a"].registerCommandAndKeybindingRule({id:"goToPreviousReference",weight:90,primary:1086,secondary:[1094],when:c["a"].or(de,B["b"].inPeekEditor),handler:function(e){he(e,(function(e){e.goToNextOrPreviousReference(!1)}))}}),re["a"].registerCommandAlias("goToNextReferenceFromEmbeddedEditor","goToNextReference"),re["a"].registerCommandAlias("goToPreviousReferenceFromEmbeddedEditor","goToPreviousReference"),re["a"].registerCommandAlias("closeReferenceSearchEditor","closeReferenceSearch"),re["a"].registerCommand("closeReferenceSearch",(function(e){return he(e,(function(e){return e.closeWidget()}))})),oe["a"].registerKeybindingRule({id:"closeReferenceSearch",weight:-1,primary:9,secondary:[1033],when:c["a"].and(B["b"].inPeekEditor,c["a"].not("config.editor.stablePeek"))}),oe["a"].registerKeybindingRule({id:"closeReferenceSearch",weight:250,primary:9,secondary:[1033],when:c["a"].and(de,c["a"].not("config.editor.stablePeek"))}),oe["a"].registerCommandAndKeybindingRule({id:"openReferenceToSide",weight:100,primary:2051,mac:{primary:259},when:c["a"].and(de,V["d"]),handler:function(e){var t,n=e.get(V["a"]),o=null===(t=n.lastFocusedList)||void 0===t?void 0:t.getFocus();Array.isArray(o)&&o[0]instanceof l["b"]&&he(e,(function(e){return e.openReference(o[0],!0)}))}}),re["a"].registerCommand("openReference",(function(e){var t,n=e.get(V["a"]),o=null===(t=n.lastFocusedList)||void 0===t?void 0:t.getFocus();Array.isArray(o)&&o[0]instanceof l["b"]&&he(e,(function(e){return e.openReference(o[0],!1)}))}))},6816:function(e,t,n){"use strict";n.d(t,"a",(function(){return f}));var o=n("0f70"),i=n("a666"),r=n("308f"),s=n("30db"),a=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}();function c(e,t){return!!e[t]}var u=function(){function e(e,t){this.target=e.target,this.hasTriggerModifier=c(e.event,t.triggerModifier),this.hasSideBySideModifier=c(e.event,t.triggerSideBySideModifier),this.isNoneOrSingleMouseDown=o["i"]||e.event.detail<=1}return e}(),d=function(){function e(e,t){this.keyCodeIsTriggerKey=e.keyCode===t.triggerKey,this.keyCodeIsSideBySideKey=e.keyCode===t.triggerSideBySideKey,this.hasTriggerModifier=c(e,t.triggerModifier)}return e}(),l=function(){function e(e,t,n,o){this.triggerKey=e,this.triggerModifier=t,this.triggerSideBySideKey=n,this.triggerSideBySideModifier=o}return e.prototype.equals=function(e){return this.triggerKey===e.triggerKey&&this.triggerModifier===e.triggerModifier&&this.triggerSideBySideKey===e.triggerSideBySideKey&&this.triggerSideBySideModifier===e.triggerSideBySideModifier},e}();function h(e){return"altKey"===e?s["e"]?new l(57,"metaKey",6,"altKey"):new l(5,"ctrlKey",6,"altKey"):s["e"]?new l(6,"altKey",57,"metaKey"):new l(6,"altKey",5,"ctrlKey")}var f=function(e){function t(t){var n=e.call(this)||this;return n._onMouseMoveOrRelevantKeyDown=n._register(new r["a"]),n.onMouseMoveOrRelevantKeyDown=n._onMouseMoveOrRelevantKeyDown.event,n._onExecute=n._register(new r["a"]),n.onExecute=n._onExecute.event,n._onCancel=n._register(new r["a"]),n.onCancel=n._onCancel.event,n._editor=t,n._opts=h(n._editor.getOption(59)),n.lastMouseMoveEvent=null,n.hasTriggerKeyOnMouseDown=!1,n._register(n._editor.onDidChangeConfiguration((function(e){if(e.hasChanged(59)){var t=h(n._editor.getOption(59));if(n._opts.equals(t))return;n._opts=t,n.lastMouseMoveEvent=null,n.hasTriggerKeyOnMouseDown=!1,n._onCancel.fire()}}))),n._register(n._editor.onMouseMove((function(e){return n.onEditorMouseMove(new u(e,n._opts))}))),n._register(n._editor.onMouseDown((function(e){return n.onEditorMouseDown(new u(e,n._opts))}))),n._register(n._editor.onMouseUp((function(e){return n.onEditorMouseUp(new u(e,n._opts))}))),n._register(n._editor.onKeyDown((function(e){return n.onEditorKeyDown(new d(e,n._opts))}))),n._register(n._editor.onKeyUp((function(e){return n.onEditorKeyUp(new d(e,n._opts))}))),n._register(n._editor.onMouseDrag((function(){return n.resetHandler()}))),n._register(n._editor.onDidChangeCursorSelection((function(e){return n.onDidChangeCursorSelection(e)}))),n._register(n._editor.onDidChangeModel((function(e){return n.resetHandler()}))),n._register(n._editor.onDidChangeModelContent((function(){return n.resetHandler()}))),n._register(n._editor.onDidScrollChange((function(e){(e.scrollTopChanged||e.scrollLeftChanged)&&n.resetHandler()}))),n}return a(t,e),t.prototype.onDidChangeCursorSelection=function(e){e.selection&&e.selection.startColumn!==e.selection.endColumn&&this.resetHandler()},t.prototype.onEditorMouseMove=function(e){this.lastMouseMoveEvent=e,this._onMouseMoveOrRelevantKeyDown.fire([e,null])},t.prototype.onEditorMouseDown=function(e){this.hasTriggerKeyOnMouseDown=e.hasTriggerModifier},t.prototype.onEditorMouseUp=function(e){this.hasTriggerKeyOnMouseDown&&this._onExecute.fire(e)},t.prototype.onEditorKeyDown=function(e){this.lastMouseMoveEvent&&(e.keyCodeIsTriggerKey||e.keyCodeIsSideBySideKey&&e.hasTriggerModifier)?this._onMouseMoveOrRelevantKeyDown.fire([this.lastMouseMoveEvent,e]):e.hasTriggerModifier&&this._onCancel.fire()},t.prototype.onEditorKeyUp=function(e){e.keyCodeIsTriggerKey&&this._onCancel.fire()},t.prototype.resetHandler=function(){this.lastMouseMoveEvent=null,this.hasTriggerKeyOnMouseDown=!1,this._onCancel.fire()},t}(i["a"])},"6df4":function(e,t,n){"use strict";n.r(t);var o=n("dff7"),i=n("b2cc"),r=n("62bd"),s=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),a=function(e){function t(){return e.call(this,{id:"editor.action.fontZoomIn",label:o["a"]("EditorFontZoomIn.label","Editor Font Zoom In"),alias:"Editor Font Zoom In",precondition:void 0})||this}return s(t,e),t.prototype.run=function(e,t){r["a"].setZoomLevel(r["a"].getZoomLevel()+1)},t}(i["b"]),c=function(e){function t(){return e.call(this,{id:"editor.action.fontZoomOut",label:o["a"]("EditorFontZoomOut.label","Editor Font Zoom Out"),alias:"Editor Font Zoom Out",precondition:void 0})||this}return s(t,e),t.prototype.run=function(e,t){r["a"].setZoomLevel(r["a"].getZoomLevel()-1)},t}(i["b"]),u=function(e){function t(){return e.call(this,{id:"editor.action.fontZoomReset",label:o["a"]("EditorFontZoomReset.label","Editor Font Zoom Reset"),alias:"Editor Font Zoom Reset",precondition:void 0})||this}return s(t,e),t.prototype.run=function(e,t){r["a"].setZoomLevel(0)},t}(i["b"]);Object(i["f"])(a),Object(i["f"])(c),Object(i["f"])(u)},7082:function(e,t,n){"use strict";n.r(t);var o=n("e8e3"),i=n("2504"),r=n("fe45"),s=n("a666"),a=n("b2cc"),c=n("5717"),u=n("3170"),d=n("6a89"),l=n("c101"),h=n("b707"),f=n("a40b"),p=n("3813"),g=n("fdcc"),_=n("6d8e"),m=n("bc04"),v=n("b055"),b=n("7061"),y=n("8025"),w=n("1b69"),C=n("d3f4"),k=function(){function e(){}return e._handleEolEdits=function(e,t){for(var n=void 0,o=[],i=0,r=t;i<r.length;i++){var s=r[i];"number"===typeof s.eol&&(n=s.eol),s.range&&"string"===typeof s.text&&o.push(s)}return"number"===typeof n&&e.hasModel()&&e.getModel().pushEOL(n),o},e._isFullModelReplaceEdit=function(e,t){if(!e.hasModel())return!1;var n=e.getModel(),o=n.validateRange(t.range),i=n.getFullModelRange();return i.equalsRange(o)},e.execute=function(t,n){t.pushUndoStop();var o=e._handleEolEdits(t,n);1===o.length&&e._isFullModelReplaceEdit(t,o[0])?t.executeEdits("formatEditsCommand",o.map((function(e){return C["a"].replace(d["a"].lift(e.range),e.text)}))):t.executeEdits("formatEditsCommand",o.map((function(e){return C["a"].replaceMove(d["a"].lift(e.range),e.text)}))),t.pushUndoStop()},e}(),O=n("dff7"),M=n("60d77"),D=n("0a0f"),P=n("db88"),S=n("9e74"),x=n("ef8e"),R=function(e,t,n,o){function i(e){return e instanceof n?e:new n((function(t){t(e)}))}return new(n||(n=Promise))((function(n,r){function s(e){try{c(o.next(e))}catch(t){r(t)}}function a(e){try{c(o["throw"](e))}catch(t){r(t)}}function c(e){e.done?n(e.value):i(e.value).then(s,a)}c((o=o.apply(e,t||[])).next())}))},E=function(e,t){var n,o,i,r,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function a(e){return function(t){return c([e,t])}}function c(r){if(n)throw new TypeError("Generator is already executing.");while(s)try{if(n=1,o&&(i=2&r[0]?o["return"]:r[0]?o["throw"]||((i=o["return"])&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return s.label++,{value:r[1],done:!1};case 5:s.label++,o=r[1],r=[0];continue;case 7:r=s.ops.pop(),s.trys.pop();continue;default:if(i=s.trys,!(i=i.length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){s=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){s.label=r[1];break}if(6===r[0]&&s.label<i[1]){s.label=i[1],i=r;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(r);break}i[2]&&s.ops.pop(),s.trys.pop();continue}r=t.call(e,s)}catch(a){r=[6,a],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}};function I(e){if(e=e.filter((function(e){return e.range})),e.length){for(var t=e[0].range,n=1;n<e.length;n++)t=d["a"].plusRange(t,e[n].range);var o=t.startLineNumber,i=t.endLineNumber;o===i?1===e.length?Object(p["a"])(O["a"]("hint11","Made 1 formatting edit on line {0}",o)):Object(p["a"])(O["a"]("hintn1","Made {0} formatting edits on line {1}",e.length,o)):1===e.length?Object(p["a"])(O["a"]("hint1n","Made 1 formatting edit between lines {0} and {1}",o,i)):Object(p["a"])(O["a"]("hintnn","Made {0} formatting edits between lines {1} and {2}",e.length,o,i))}}function N(e){for(var t=[],n=new Set,o=h["g"].ordered(e),i=0,r=o;i<r.length;i++){var s=r[i];t.push(s),s.extensionId&&n.add(M["a"].toKey(s.extensionId))}for(var a=h["j"].ordered(e),c=function(e){if(e.extensionId){if(n.has(M["a"].toKey(e.extensionId)))return"continue";n.add(M["a"].toKey(e.extensionId))}t.push({displayName:e.displayName,extensionId:e.extensionId,provideDocumentFormattingEdits:function(t,n,o){return e.provideDocumentRangeFormattingEdits(t,t.getFullModelRange(),n,o)}})},u=0,d=a;u<d.length;u++){s=d[u];c(s)}return t}var j=function(){function e(){}return e.select=function(t,n,o){return R(this,void 0,void 0,(function(){var i;return E(this,(function(r){switch(r.label){case 0:return 0===t.length?[2,void 0]:(i=e._selectors.iterator().next().value,i?[4,i(t,n,o)]:[3,2]);case 1:return[2,r.sent()];case 2:return[2,t[0]]}}))}))},e._selectors=new P["a"],e}();function T(e,t,n,o,i){return R(this,void 0,void 0,(function(){var r,s,a,c;return E(this,(function(u){switch(u.label){case 0:return r=e.get(D["a"]),s=Object(v["a"])(t)?t.getModel():t,a=h["j"].ordered(s),[4,j.select(a,s,o)];case 1:return c=u.sent(),c?[4,r.invokeFunction(L,c,t,n,i)]:[3,3];case 2:u.sent(),u.label=3;case 3:return[2]}}))}))}function L(e,t,n,o,i){return R(this,void 0,void 0,(function(){var r,s,a,c,u,l,h;return E(this,(function(p){switch(p.label){case 0:r=e.get(f["a"]),Object(v["a"])(n)?(s=n.getModel(),a=new m["b"](n,5,i)):(s=n,a=new m["d"](n,i)),p.label=1;case 1:return p.trys.push([1,,4,5]),[4,t.provideDocumentRangeFormattingEdits(s,o,s.getFormattingOptions(),a.token)];case 2:return u=p.sent(),[4,r.computeMoreMinimalEdits(s.uri,u)];case 3:return c=p.sent(),a.token.isCancellationRequested?[2,!0]:[3,5];case 4:return a.dispose(),[7];case 5:return c&&0!==c.length?(Object(v["a"])(n)?(k.execute(n,c),I(c),n.pushUndoStop(),n.revealPositionInCenterIfOutsideViewport(n.getPosition(),1)):(l=c[0].range,h=new y["a"](l.startLineNumber,l.startColumn,l.endLineNumber,l.endColumn),s.pushEditOperations([h],c.map((function(e){return{text:e.text,range:d["a"].lift(e.range),forceMoveMarkers:!0}})),(function(e){for(var t=0,n=e;t<n.length;t++){var o=n[t].range;if(d["a"].areIntersectingOrTouching(o,h))return[new y["a"](o.startLineNumber,o.startColumn,o.endLineNumber,o.endColumn)]}return null}))),[2,!0]):[2,!1]}}))}))}function F(e,t,n,o){return R(this,void 0,void 0,(function(){var i,r,s,a;return E(this,(function(c){switch(c.label){case 0:return i=e.get(D["a"]),r=Object(v["a"])(t)?t.getModel():t,s=N(r),[4,j.select(s,r,n)];case 1:return a=c.sent(),a?[4,i.invokeFunction(A,a,t,n,o)]:[3,3];case 2:c.sent(),c.label=3;case 3:return[2]}}))}))}function A(e,t,n,o,i){return R(this,void 0,void 0,(function(){var r,s,a,c,u,l,h;return E(this,(function(p){switch(p.label){case 0:r=e.get(f["a"]),Object(v["a"])(n)?(s=n.getModel(),a=new m["b"](n,5,i)):(s=n,a=new m["d"](n,i)),p.label=1;case 1:return p.trys.push([1,,4,5]),[4,t.provideDocumentFormattingEdits(s,s.getFormattingOptions(),a.token)];case 2:return u=p.sent(),[4,r.computeMoreMinimalEdits(s.uri,u)];case 3:return c=p.sent(),a.token.isCancellationRequested?[2,!0]:[3,5];case 4:return a.dispose(),[7];case 5:return c&&0!==c.length?(Object(v["a"])(n)?(k.execute(n,c),2!==o&&(I(c),n.pushUndoStop(),n.revealPositionInCenterIfOutsideViewport(n.getPosition(),1))):(l=c[0].range,h=new y["a"](l.startLineNumber,l.startColumn,l.endLineNumber,l.endColumn),s.pushEditOperations([h],c.map((function(e){return{text:e.text,range:d["a"].lift(e.range),forceMoveMarkers:!0}})),(function(e){for(var t=0,n=e;t<n.length;t++){var o=n[t].range;if(d["a"].areIntersectingOrTouching(o,h))return[new y["a"](o.startLineNumber,o.startColumn,o.endLineNumber,o.endColumn)]}return null}))),[2,!0]):[2,!1]}}))}))}function W(e,t,n,i,r){return R(this,void 0,void 0,(function(){var s,a,c,u,d;return E(this,(function(l){switch(l.label){case 0:s=h["j"].ordered(t),a=0,c=s,l.label=1;case 1:return a<c.length?(u=c[a],[4,Promise.resolve(u.provideDocumentRangeFormattingEdits(t,n,i,r)).catch(g["f"])]):[3,5];case 2:return d=l.sent(),Object(o["q"])(d)?[4,e.computeMoreMinimalEdits(t.uri,d)]:[3,4];case 3:return[2,l.sent()];case 4:return a++,[3,1];case 5:return[2,void 0]}}))}))}function K(e,t,n,i){return R(this,void 0,void 0,(function(){var r,s,a,c,u;return E(this,(function(d){switch(d.label){case 0:r=N(t),s=0,a=r,d.label=1;case 1:return s<a.length?(c=a[s],[4,Promise.resolve(c.provideDocumentFormattingEdits(t,n,i)).catch(g["f"])]):[3,5];case 2:return u=d.sent(),Object(o["q"])(u)?[4,e.computeMoreMinimalEdits(t.uri,u)]:[3,4];case 3:return[2,d.sent()];case 4:return s++,[3,1];case 5:return[2,void 0]}}))}))}function H(e,t,n,o,r){var s=h["t"].ordered(t);return 0===s.length||s[0].autoFormatTriggerCharacters.indexOf(o)<0?Promise.resolve(void 0):Promise.resolve(s[0].provideOnTypeFormattingEdits(t,n,o,r,i["a"].None)).catch(g["f"]).then((function(n){return e.computeMoreMinimalEdits(t.uri,n)}))}S["a"].registerCommand("_executeFormatRangeProvider",(function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var o=t[0],r=t[1],s=t[2];Object(x["a"])(_["a"].isUri(o)),Object(x["a"])(d["a"].isIRange(r));var a=e.get(w["a"]).getModel(o);if(!a)throw Object(g["b"])("resource");return W(e.get(f["a"]),a,d["a"].lift(r),s,i["a"].None)})),S["a"].registerCommand("_executeFormatDocumentProvider",(function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var o=t[0],r=t[1];Object(x["a"])(_["a"].isUri(o));var s=e.get(w["a"]).getModel(o);if(!s)throw Object(g["b"])("resource");return K(e.get(f["a"]),s,r,i["a"].None)})),S["a"].registerCommand("_executeFormatOnTypeProvider",(function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var o=t[0],i=t[1],r=t[2],s=t[3];Object(x["a"])(_["a"].isUri(o)),Object(x["a"])(b["a"].isIPosition(i)),Object(x["a"])("string"===typeof r);var a=e.get(w["a"]).getModel(o);if(!a)throw Object(g["b"])("resource");return H(e.get(f["a"]),a,b["a"].lift(i),r,s)}));var V=n("4fc3"),U=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),B=function(e,t,n,o){var i,r=arguments.length,s=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)s=Reflect.decorate(e,t,n,o);else for(var a=e.length-1;a>=0;a--)(i=e[a])&&(s=(r<3?i(s):r>3?i(t,n,s):i(t,n))||s);return r>3&&s&&Object.defineProperty(t,n,s),s},G=function(e,t){return function(n,o){t(n,o,e)}},q=function(e,t,n,o){function i(e){return e instanceof n?e:new n((function(t){t(e)}))}return new(n||(n=Promise))((function(n,r){function s(e){try{c(o.next(e))}catch(t){r(t)}}function a(e){try{c(o["throw"](e))}catch(t){r(t)}}function c(e){e.done?n(e.value):i(e.value).then(s,a)}c((o=o.apply(e,t||[])).next())}))},z=function(e,t){var n,o,i,r,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function a(e){return function(t){return c([e,t])}}function c(r){if(n)throw new TypeError("Generator is already executing.");while(s)try{if(n=1,o&&(i=2&r[0]?o["return"]:r[0]?o["throw"]||((i=o["return"])&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return s.label++,{value:r[1],done:!1};case 5:s.label++,o=r[1],r=[0];continue;case 7:r=s.ops.pop(),s.trys.pop();continue;default:if(i=s.trys,!(i=i.length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){s=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){s.label=r[1];break}if(6===r[0]&&s.label<i[1]){s.label=i[1],i=r;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(r);break}i[2]&&s.ops.pop(),s.trys.pop();continue}r=t.call(e,s)}catch(a){r=[6,a],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}},Z=function(){function e(e,t){var n=this;this._workerService=t,this._callOnDispose=new s["b"],this._callOnModel=new s["b"],this._editor=e,this._callOnDispose.add(e.onDidChangeConfiguration((function(){return n._update()}))),this._callOnDispose.add(e.onDidChangeModel((function(){return n._update()}))),this._callOnDispose.add(e.onDidChangeModelLanguage((function(){return n._update()}))),this._callOnDispose.add(h["t"].onDidChange(this._update,this))}return e.prototype.dispose=function(){this._callOnDispose.dispose(),this._callOnModel.dispose()},e.prototype._update=function(){var e=this;if(this._callOnModel.clear(),this._editor.getOption(39)&&this._editor.hasModel()){var t=this._editor.getModel(),n=h["t"].ordered(t)[0];if(n&&n.autoFormatTriggerCharacters){for(var o=new u["b"],i=0,r=n.autoFormatTriggerCharacters;i<r.length;i++){var s=r[i];o.add(s.charCodeAt(0))}this._callOnModel.add(this._editor.onDidType((function(t){var n=t.charCodeAt(t.length-1);o.has(n)&&e._trigger(String.fromCharCode(n))})))}}},e.prototype._trigger=function(e){var t=this;if(this._editor.hasModel()&&!(this._editor.getSelections().length>1)){var n=this._editor.getModel(),i=this._editor.getPosition(),r=!1,s=this._editor.onDidChangeModelContent((function(e){if(e.isFlush)return r=!0,void s.dispose();for(var t=0,n=e.changes.length;t<n;t++){var o=e.changes[t];if(o.range.endLineNumber<=i.lineNumber)return r=!0,void s.dispose()}}));H(this._workerService,n,i,e,n.getFormattingOptions()).then((function(e){s.dispose(),r||Object(o["q"])(e)&&(k.execute(t._editor,e),I(e))}),(function(e){throw s.dispose(),e}))}},e.ID="editor.contrib.autoFormat",e=B([G(1,f["a"])],e),e}(),J=function(){function e(e,t){var n=this;this.editor=e,this._instantiationService=t,this._callOnDispose=new s["b"],this._callOnModel=new s["b"],this._callOnDispose.add(e.onDidChangeConfiguration((function(){return n._update()}))),this._callOnDispose.add(e.onDidChangeModel((function(){return n._update()}))),this._callOnDispose.add(e.onDidChangeModelLanguage((function(){return n._update()}))),this._callOnDispose.add(h["j"].onDidChange(this._update,this))}return e.prototype.dispose=function(){this._callOnDispose.dispose(),this._callOnModel.dispose()},e.prototype._update=function(){var e=this;this._callOnModel.clear(),this.editor.getOption(38)&&this.editor.hasModel()&&h["j"].has(this.editor.getModel())&&this._callOnModel.add(this.editor.onDidPaste((function(t){var n=t.range;return e._trigger(n)})))},e.prototype._trigger=function(e){this.editor.hasModel()&&(this.editor.getSelections().length>1||this._instantiationService.invokeFunction(T,this.editor,e,2,i["a"].None).catch(g["e"]))},e.ID="editor.contrib.formatOnPaste",e=B([G(1,D["a"])],e),e}(),X=function(e){function t(){return e.call(this,{id:"editor.action.formatDocument",label:O["a"]("formatDocument.label","Format Document"),alias:"Format Document",precondition:V["a"].and(l["a"].writable,l["a"].hasDocumentFormattingProvider),kbOpts:{kbExpr:V["a"].and(l["a"].editorTextFocus,l["a"].hasDocumentFormattingProvider),primary:1572,linux:{primary:3111},weight:100},contextMenuOpts:{when:l["a"].hasDocumentFormattingProvider,group:"1_modification",order:1.3}})||this}return U(t,e),t.prototype.run=function(e,t){return q(this,void 0,void 0,(function(){var n;return z(this,(function(o){switch(o.label){case 0:return t.hasModel()?(n=e.get(D["a"]),[4,n.invokeFunction(F,t,1,i["a"].None)]):[3,2];case 1:o.sent(),o.label=2;case 2:return[2]}}))}))},t}(a["b"]),Q=function(e){function t(){return e.call(this,{id:"editor.action.formatSelection",label:O["a"]("formatSelection.label","Format Selection"),alias:"Format Selection",precondition:V["a"].and(l["a"].writable,l["a"].hasDocumentSelectionFormattingProvider),kbOpts:{kbExpr:V["a"].and(l["a"].editorTextFocus,l["a"].hasDocumentSelectionFormattingProvider),primary:Object(r["a"])(2089,2084),weight:100},contextMenuOpts:{when:V["a"].and(l["a"].hasDocumentSelectionFormattingProvider,l["a"].hasNonEmptySelection),group:"1_modification",order:1.31}})||this}return U(t,e),t.prototype.run=function(e,t){return q(this,void 0,void 0,(function(){var n,o,r;return z(this,(function(s){switch(s.label){case 0:return t.hasModel()?(n=e.get(D["a"]),o=t.getModel(),r=t.getSelection(),r.isEmpty()&&(r=new d["a"](r.startLineNumber,1,r.startLineNumber,o.getLineMaxColumn(r.startLineNumber))),[4,n.invokeFunction(T,t,r,1,i["a"].None)]):[2];case 1:return s.sent(),[2]}}))}))},t}(a["b"]);Object(a["h"])(Z.ID,Z),Object(a["h"])(J.ID,J),Object(a["f"])(X),Object(a["f"])(Q),S["a"].registerCommand("editor.action.format",(function(e){return q(void 0,void 0,void 0,(function(){var t,n;return z(this,(function(o){switch(o.label){case 0:return t=e.get(c["a"]).getFocusedCodeEditor(),t&&t.hasModel()?(n=e.get(S["b"]),t.getSelection().isEmpty()?[4,n.executeCommand("editor.action.formatDocument")]:[3,2]):[2];case 1:return o.sent(),[3,4];case 2:return[4,n.executeCommand("editor.action.formatSelection")];case 3:o.sent(),o.label=4;case 4:return[2]}}))}))}))},"958f":function(e,t,n){"use strict";n.r(t),n.d(t,"MarkerController",(function(){return B})),n.d(t,"NextMarkerAction",(function(){return q}));var o=n("dff7"),i=n("308f"),r=n("a666"),s=n("4fc3"),a=n("b400"),c=n("6a89"),u=n("b2cc"),d=n("b7d0"),l=n("c101"),h=(n("fe86"),n("11f7")),f=n("303e"),p=n("ceb8"),g=n("1898"),_=n("debc"),m=n("e8e3"),v=n("88d4"),b=n("82c9"),y=n("4efb"),w=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),C=function(){function e(e,t,n,o){var i=this;this._openerService=o,this._lines=0,this._longestLineLength=0,this._relatedDiagnostics=new WeakMap,this._disposables=new r["b"],this._editor=t;var s=document.createElement("div");s.className="descriptioncontainer",s.setAttribute("aria-live","assertive"),s.setAttribute("role","alert"),this._messageBlock=document.createElement("div"),h["f"](this._messageBlock,"message"),s.appendChild(this._messageBlock),this._relatedBlock=document.createElement("div"),s.appendChild(this._relatedBlock),this._disposables.add(h["o"](this._relatedBlock,"click",(function(e){e.preventDefault();var t=i._relatedDiagnostics.get(e.target);t&&n(t)}))),this._scrollable=new g["b"](s,{horizontal:1,vertical:1,useShadows:!1,horizontalScrollbarSize:3,verticalScrollbarSize:3}),e.appendChild(this._scrollable.getDomNode()),this._disposables.add(this._scrollable.onScroll((function(e){s.style.left="-"+e.scrollLeft+"px",s.style.top="-"+e.scrollTop+"px"}))),this._disposables.add(this._scrollable)}return e.prototype.dispose=function(){Object(r["f"])(this._disposables)},e.prototype.update=function(e){var t=this,n=e.source,o=e.message,i=e.relatedInformation,r=e.code,s=((null===n||void 0===n?void 0:n.length)||0)+"()".length;r&&(s+="string"===typeof r?r.length:r.value.length);var a=o.split(/\r\n|\r|\n/g);this._lines=a.length,this._longestLineLength=0;for(var c=0,u=a;c<u.length;c++){var d=u[c];this._longestLineLength=Math.max(d.length+s,this._longestLineLength)}h["t"](this._messageBlock),this._editor.applyFontInfo(this._messageBlock);for(var l=this._messageBlock,f=0,p=a;f<p.length;f++){d=p[f];l=document.createElement("div"),l.innerText=d,""===d&&(l.style.height=this._messageBlock.style.lineHeight),this._messageBlock.appendChild(l)}if(n||r){var g=document.createElement("span");if(h["f"](g,"details"),l.appendChild(g),n){var v=document.createElement("span");v.innerText=n,h["f"](v,"source"),g.appendChild(v)}if(r)if("string"===typeof r){var b=document.createElement("span");b.innerText="("+r+")",h["f"](b,"code"),g.appendChild(b)}else{this._codeLink=h["a"]("a.code-link"),this._codeLink.setAttribute("href",""+r.link.toString()),this._codeLink.onclick=function(e){t._openerService.open(r.link),e.preventDefault(),e.stopPropagation()};b=h["q"](this._codeLink,h["a"]("span"));b.innerText=r.value,g.appendChild(this._codeLink)}}if(h["t"](this._relatedBlock),this._editor.applyFontInfo(this._relatedBlock),Object(m["q"])(i)){var y=this._relatedBlock.appendChild(document.createElement("div"));y.style.paddingTop=Math.floor(.66*this._editor.getOption(49))+"px",this._lines+=1;for(var w=0,C=i;w<C.length;w++){var k=C[w],O=document.createElement("div"),M=document.createElement("a");h["f"](M,"filename"),M.innerHTML=Object(_["a"])(k.resource)+"("+k.startLineNumber+", "+k.startColumn+"): ",M.title=Object(_["b"])(k.resource,void 0),this._relatedDiagnostics.set(M,k);var D=document.createElement("span");D.innerText=k.message,O.appendChild(M),O.appendChild(D),this._lines+=1,y.appendChild(O)}}var P=this._editor.getOption(34),S=Math.ceil(P.typicalFullwidthCharacterWidth*this._longestLineLength*.75),x=P.lineHeight*this._lines;this._scrollable.setScrollDimensions({scrollWidth:S,scrollHeight:x})},e.prototype.layout=function(e,t){this._scrollable.getDomNode().style.height=e+"px",this._scrollable.getDomNode().style.width=t+"px",this._scrollable.setScrollDimensions({width:t,height:e})},e.prototype.getHeightInLines=function(){return Math.min(17,this._lines)},e}(),k=function(e){function t(t,n,o,s){var c=e.call(this,t,{showArrow:!0,showFrame:!0,isAccessible:!0})||this;return c.actions=n,c._themeService=o,c._openerService=s,c._callOnDispose=new r["b"],c._onDidSelectRelatedInformation=new i["a"],c.onDidSelectRelatedInformation=c._onDidSelectRelatedInformation.event,c._severity=a["c"].Warning,c._backgroundColor=p["a"].white,c._applyTheme(o.getTheme()),c._callOnDispose.add(o.onThemeChange(c._applyTheme.bind(c))),c.create(),c}return w(t,e),t.prototype._applyTheme=function(e){this._backgroundColor=e.getColor(R);var t=P;this._severity===a["c"].Warning?t=S:this._severity===a["c"].Info&&(t=x);var n=e.getColor(t);this.style({arrowColor:n,frameColor:n,headerBackgroundColor:this._backgroundColor,primaryHeadingColor:e.getColor(v["q"]),secondaryHeadingColor:e.getColor(v["r"])})},t.prototype._applyStyles=function(){this._parentContainer&&(this._parentContainer.style.backgroundColor=this._backgroundColor?this._backgroundColor.toString():""),e.prototype._applyStyles.call(this)},t.prototype.dispose=function(){this._callOnDispose.dispose(),e.prototype.dispose.call(this)},t.prototype._fillHead=function(t){e.prototype._fillHead.call(this,t),this._actionbarWidget.push(this.actions,{label:!1,icon:!0,index:0})},t.prototype._fillTitleIcon=function(e){this._icon=h["q"](e,h["a"](""))},t.prototype._getActionBarOptions=function(){return{orientation:0}},t.prototype._fillBody=function(e){var t=this;this._parentContainer=e,h["f"](e,"marker-widget"),this._parentContainer.tabIndex=0,this._parentContainer.setAttribute("role","tooltip"),this._container=document.createElement("div"),e.appendChild(this._container),this._message=new C(this._container,this.editor,(function(e){return t._onDidSelectRelatedInformation.fire(e)}),this._openerService),this._disposables.add(this._message)},t.prototype.show=function(e,t){throw new Error("call showAtMarker")},t.prototype.showAtMarker=function(t,n,i){this._container.classList.remove("stale"),this._message.update(t),this._severity=t.severity,this._applyTheme(this._themeService.getTheme());var r=c["a"].lift(t),s=this.editor.getPosition(),u=s&&r.containsPosition(s)?s:r.getStartPosition();e.prototype.show.call(this,u,this.computeRequiredHeight());var d=this.editor.getModel();if(d){var l=i>1?o["a"]("problems","{0} of {1} problems",n,i):o["a"]("change","{0} of {1} problem",n,i);this.setTitle(Object(b["b"])(d.uri),l)}this._icon.className="codicon "+y["a"].className(a["c"].toSeverity(this._severity)),this.editor.revealPositionInCenter(u,0),this.editor.focus()},t.prototype.updateMarker=function(e){this._container.classList.remove("stale"),this._message.update(e)},t.prototype.showStale=function(){this._container.classList.add("stale"),this._relayout()},t.prototype._doLayoutBody=function(t,n){e.prototype._doLayoutBody.call(this,t,n),this._heightInPixel=t,this._message.layout(t,n),this._container.style.height=t+"px"},t.prototype._onWidth=function(e){this._message.layout(this._heightInPixel,e)},t.prototype._relayout=function(){e.prototype._relayout.call(this,this.computeRequiredHeight())},t.prototype.computeRequiredHeight=function(){return 3+this._message.getHeightInLines()},t}(v["c"]),O=Object(f["Kb"])(f["q"],f["p"]),M=Object(f["Kb"])(f["P"],f["O"]),D=Object(f["Kb"])(f["H"],f["G"]),P=Object(f["Tb"])("editorMarkerNavigationError.background",{dark:O,light:O,hc:O},o["a"]("editorMarkerNavigationError","Editor marker navigation widget error color.")),S=Object(f["Tb"])("editorMarkerNavigationWarning.background",{dark:M,light:M,hc:M},o["a"]("editorMarkerNavigationWarning","Editor marker navigation widget warning color.")),x=Object(f["Tb"])("editorMarkerNavigationInfo.background",{dark:D,light:D,hc:D},o["a"]("editorMarkerNavigationInfo","Editor marker navigation widget info color.")),R=Object(f["Tb"])("editorMarkerNavigation.background",{dark:"#2D2D30",light:p["a"].white,hc:"#0C141F"},o["a"]("editorMarkerNavigationBackground","Editor marker navigation widget background."));Object(d["e"])((function(e,t){var n=e.getColor(f["ec"]);n&&(t.addRule(".monaco-editor .marker-widget a { color: "+n+"; }"),t.addRule(".monaco-editor .marker-widget a.code-link span:hover { color: "+n+"; }"))}));var E=n("3742"),I=n("5717"),N=n("fdcc"),j=n("7e32"),T=n("f070"),L=n("6dec"),F=n("5bd7"),A=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),W=function(e,t,n,o){var i,r=arguments.length,s=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)s=Reflect.decorate(e,t,n,o);else for(var a=e.length-1;a>=0;a--)(i=e[a])&&(s=(r<3?i(s):r>3?i(t,n,s):i(t,n))||s);return r>3&&s&&Object.defineProperty(t,n,s),s},K=function(e,t){return function(n,o){t(n,o,e)}},H=function(e,t,n,o){function i(e){return e instanceof n?e:new n((function(t){t(e)}))}return new(n||(n=Promise))((function(n,r){function s(e){try{c(o.next(e))}catch(t){r(t)}}function a(e){try{c(o["throw"](e))}catch(t){r(t)}}function c(e){e.done?n(e.value):i(e.value).then(s,a)}c((o=o.apply(e,t||[])).next())}))},V=function(e,t){var n,o,i,r,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function a(e){return function(t){return c([e,t])}}function c(r){if(n)throw new TypeError("Generator is already executing.");while(s)try{if(n=1,o&&(i=2&r[0]?o["return"]:r[0]?o["throw"]||((i=o["return"])&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return s.label++,{value:r[1],done:!1};case 5:s.label++,o=r[1],r=[0];continue;case 7:r=s.ops.pop(),s.trys.pop();continue;default:if(i=s.trys,!(i=i.length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){s=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){s.label=r[1];break}if(6===r[0]&&s.label<i[1]){s.label=i[1],i=r;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(r);break}i[2]&&s.ops.pop(),s.trys.pop();continue}r=t.call(e,s)}catch(a){r=[6,a],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}},U=function(){function e(e,t){var n=this;this._toUnbind=new r["b"],this._editor=e,this._markers=[],this._nextIdx=-1,this._ignoreSelectionChange=!1,this._onCurrentMarkerChanged=new i["a"],this._onMarkerSetChanged=new i["a"],this.setMarkers(t),this._toUnbind.add(this._editor.onDidDispose((function(){return n.dispose()}))),this._toUnbind.add(this._editor.onDidChangeCursorPosition((function(){n._ignoreSelectionChange||n.currentMarker&&n._editor.getPosition()&&c["a"].containsPosition(n.currentMarker,n._editor.getPosition())||(n._nextIdx=-1)})))}return Object.defineProperty(e.prototype,"onCurrentMarkerChanged",{get:function(){return this._onCurrentMarkerChanged.event},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"onMarkerSetChanged",{get:function(){return this._onMarkerSetChanged.event},enumerable:!0,configurable:!0}),e.prototype.setMarkers=function(e){var t=this._nextIdx>=0?this._markers[this._nextIdx]:void 0;this._markers=e||[],this._markers.sort(G.compareMarker),this._nextIdx=t?Math.max(-1,Object(m["c"])(this._markers,t,G.compareMarker)):-1,this._onMarkerSetChanged.fire(this)},e.prototype.withoutWatchingEditorPosition=function(e){this._ignoreSelectionChange=!0;try{e()}finally{this._ignoreSelectionChange=!1}},e.prototype._initIdx=function(e){for(var t=!1,n=this._editor.getPosition(),o=0;o<this._markers.length;o++){var i=c["a"].lift(this._markers[o]);if(i.isEmpty()&&this._editor.getModel()){var r=this._editor.getModel().getWordAtPosition(i.getStartPosition());r&&(i=new c["a"](i.startLineNumber,r.startColumn,i.startLineNumber,r.endColumn))}if(n&&(i.containsPosition(n)||n.isBeforeOrEqual(i.getStartPosition()))){this._nextIdx=o,t=!0;break}}t||(this._nextIdx=e?0:this._markers.length-1),this._nextIdx<0&&(this._nextIdx=this._markers.length-1)},Object.defineProperty(e.prototype,"currentMarker",{get:function(){return this.canNavigate()?this._markers[this._nextIdx]:void 0},set:function(e){var t=this._nextIdx;this._nextIdx=-1,e&&(this._nextIdx=this.indexOf(e)),this._nextIdx!==t&&this._onCurrentMarkerChanged.fire(e)},enumerable:!0,configurable:!0}),e.prototype.move=function(e,t){if(!this.canNavigate())return this._onCurrentMarkerChanged.fire(void 0),!t;var n=this._nextIdx,o=!1;if(-1===this._nextIdx?this._initIdx(e):e?t||this._nextIdx+1<this._markers.length?this._nextIdx=(this._nextIdx+1)%this._markers.length:o=!0:e||(t||this._nextIdx>0?this._nextIdx=(this._nextIdx-1+this._markers.length)%this._markers.length:o=!0),n!==this._nextIdx){var i=this._markers[this._nextIdx];this._onCurrentMarkerChanged.fire(i)}return o},e.prototype.canNavigate=function(){return this._markers.length>0},e.prototype.findMarkerAtPosition=function(e){return Object(m["h"])(this._markers,(function(t){return c["a"].containsPosition(t,e)}))},Object.defineProperty(e.prototype,"total",{get:function(){return this._markers.length},enumerable:!0,configurable:!0}),e.prototype.indexOf=function(e){return 1+this._markers.indexOf(e)},e.prototype.dispose=function(){this._toUnbind.dispose()},e}(),B=function(){function e(e,t,n,o,i,s,a){this._markerService=t,this._contextKeyService=n,this._themeService=o,this._editorService=i,this._keybindingService=s,this._openerService=a,this._model=null,this._widget=null,this._disposeOnClose=new r["b"],this._editor=e,this._widgetVisible=X.bindTo(this._contextKeyService)}return e.get=function(t){return t.getContribution(e.ID)},e.prototype.dispose=function(){this._cleanUp(),this._disposeOnClose.dispose()},e.prototype._cleanUp=function(){this._widgetVisible.reset(),this._disposeOnClose.clear(),this._widget=null,this._model=null},e.prototype.getOrCreateModel=function(){var e=this;if(this._model)return this._model;var t=this._getMarkers();this._model=new U(this._editor,t),this._markerService.onMarkerChanged(this._onMarkerChanged,this,this._disposeOnClose);var n=this._keybindingService.lookupKeybinding(z.ID),o=this._keybindingService.lookupKeybinding(q.ID),i=[new T["a"](q.ID,q.LABEL+(o?" ("+o.getLabel()+")":""),"show-next-problem codicon-chevron-down",this._model.canNavigate(),(function(){return H(e,void 0,void 0,(function(){return V(this,(function(e){return this._model&&this._model.move(!0,!0),[2]}))}))})),new T["a"](z.ID,z.LABEL+(n?" ("+n.getLabel()+")":""),"show-previous-problem codicon-chevron-up",this._model.canNavigate(),(function(){return H(e,void 0,void 0,(function(){return V(this,(function(e){return this._model&&this._model.move(!1,!0),[2]}))}))}))];this._widget=new k(this._editor,i,this._themeService,this._openerService),this._widgetVisible.set(!0),this._widget.onDidClose((function(){return e.closeMarkersNavigation()}),this,this._disposeOnClose),this._disposeOnClose.add(this._model),this._disposeOnClose.add(this._widget);for(var r=0,s=i;r<s.length;r++){var a=s[r];this._disposeOnClose.add(a)}return this._disposeOnClose.add(this._widget.onDidSelectRelatedInformation((function(t){e._editorService.openCodeEditor({resource:t.resource,options:{pinned:!0,revealIfOpened:!0,selection:c["a"].lift(t).collapseToStart()}},e._editor).then(void 0,N["e"]),e.closeMarkersNavigation(!1)}))),this._disposeOnClose.add(this._editor.onDidChangeModel((function(){return e._cleanUp()}))),this._disposeOnClose.add(this._model.onCurrentMarkerChanged((function(t){t&&e._model?e._model.withoutWatchingEditorPosition((function(){e._widget&&e._model&&e._widget.showAtMarker(t,e._model.indexOf(t),e._model.total)})):e._cleanUp()}))),this._disposeOnClose.add(this._model.onMarkerSetChanged((function(){if(e._widget&&e._widget.position&&e._model){var t=e._model.findMarkerAtPosition(e._widget.position);t?e._widget.updateMarker(t):e._widget.showStale()}}))),this._model},e.prototype.closeMarkersNavigation=function(e){void 0===e&&(e=!0),this._cleanUp(),e&&this._editor.focus()},e.prototype.show=function(e){var t=this.getOrCreateModel();t.currentMarker=e},e.prototype._onMarkerChanged=function(e){var t=this._editor.getModel();t&&this._model&&e.some((function(e){return Object(b["e"])(t.uri,e)}))&&this._model.setMarkers(this._getMarkers())},e.prototype._getMarkers=function(){var e=this._editor.getModel();return e?this._markerService.read({resource:e.uri,severities:a["c"].Error|a["c"].Warning|a["c"].Info}):[]},e.ID="editor.contrib.markerController",e=W([K(1,a["b"]),K(2,s["c"]),K(3,d["c"]),K(4,I["a"]),K(5,L["a"]),K(6,F["a"])],e),e}(),G=function(e){function t(t,n,o){var i=e.call(this,o)||this;return i._isNext=t,i._multiFile=n,i}return A(t,e),t.prototype.run=function(e,n){var o=this,i=e.get(a["b"]),r=e.get(I["a"]),s=B.get(n);if(!s)return Promise.resolve(void 0);var c=s.getOrCreateModel(),u=c.move(this._isNext,!this._multiFile);if(!u||!this._multiFile)return Promise.resolve(void 0);var d=i.read({severities:a["c"].Error|a["c"].Warning|a["c"].Info}).sort(t.compareMarker);if(0===d.length)return Promise.resolve(void 0);var l=n.getModel();if(!l)return Promise.resolve(void 0);var h=c.currentMarker||{resource:l.uri,severity:a["c"].Error,startLineNumber:1,startColumn:1,endLineNumber:1,endColumn:1},f=Object(m["c"])(d,h,t.compareMarker);f<0?(f=~f,f%=d.length):f=this._isNext?(f+1)%d.length:(f+d.length-1)%d.length;var p=d[f];return Object(b["e"])(p.resource,l.uri)?(c.move(this._isNext,!0),Promise.resolve(void 0)):(s.closeMarkersNavigation(),r.openCodeEditor({resource:p.resource,options:{pinned:!1,revealIfOpened:!0,revealInCenterIfOutsideViewport:!0,selection:p}},n).then((function(e){if(e)return e.getAction(o.id).run()})))},t.compareMarker=function(e,t){var n=Object(E["e"])(e.resource.toString(),t.resource.toString());return 0===n&&(n=a["c"].compare(e.severity,t.severity)),0===n&&(n=c["a"].compareRangesUsingStarts(e,t)),n},t}(u["b"]),q=function(e){function t(){return e.call(this,!0,!1,{id:t.ID,label:t.LABEL,alias:"Go to Next Problem (Error, Warning, Info)",precondition:l["a"].writable,kbOpts:{kbExpr:l["a"].focus,primary:578,weight:100}})||this}return A(t,e),t.ID="editor.action.marker.next",t.LABEL=o["a"]("markerAction.next.label","Go to Next Problem (Error, Warning, Info)"),t}(G),z=function(e){function t(){return e.call(this,!1,!1,{id:t.ID,label:t.LABEL,alias:"Go to Previous Problem (Error, Warning, Info)",precondition:l["a"].writable,kbOpts:{kbExpr:l["a"].focus,primary:1602,weight:100}})||this}return A(t,e),t.ID="editor.action.marker.prev",t.LABEL=o["a"]("markerAction.previous.label","Go to Previous Problem (Error, Warning, Info)"),t}(G),Z=function(e){function t(){return e.call(this,!0,!0,{id:"editor.action.marker.nextInFiles",label:o["a"]("markerAction.nextInFiles.label","Go to Next Problem in Files (Error, Warning, Info)"),alias:"Go to Next Problem in Files (Error, Warning, Info)",precondition:l["a"].writable,kbOpts:{kbExpr:l["a"].focus,primary:66,weight:100}})||this}return A(t,e),t}(G),J=function(e){function t(){return e.call(this,!1,!0,{id:"editor.action.marker.prevInFiles",label:o["a"]("markerAction.previousInFiles.label","Go to Previous Problem in Files (Error, Warning, Info)"),alias:"Go to Previous Problem in Files (Error, Warning, Info)",precondition:l["a"].writable,kbOpts:{kbExpr:l["a"].focus,primary:1090,weight:100}})||this}return A(t,e),t}(G);Object(u["h"])(B.ID,B),Object(u["f"])(q),Object(u["f"])(z),Object(u["f"])(Z),Object(u["f"])(J);var X=new s["d"]("markersNavigationVisible",!1),Q=u["c"].bindToContribution(B.get);Object(u["g"])(new Q({id:"closeMarkersNavigation",precondition:X,handler:function(e){return e.closeMarkersNavigation()},kbOpts:{weight:150,kbExpr:l["a"].focus,primary:9,secondary:[1033]}})),j["c"].appendMenuItem(19,{group:"6_problem_nav",command:{id:"editor.action.marker.nextInFiles",title:o["a"]({key:"miGotoNextProblem",comment:["&& denotes a mnemonic"]},"Next &&Problem")},order:1}),j["c"].appendMenuItem(19,{group:"6_problem_nav",command:{id:"editor.action.marker.prevInFiles",title:o["a"]({key:"miGotoPreviousProblem",comment:["&& denotes a mnemonic"]},"Previous &&Problem")},order:2})},aee8:function(e,t,n){"use strict";n.r(t),n.d(t,"ModesHoverController",(function(){return ae}));n("b805");var o=n("dff7"),i=n("fe45"),r=n("a666"),s=n("b2cc"),a=n("6a89"),c=n("c101"),u=n("5818"),d=n("11f7"),l=n("2504"),h=n("ceb8"),f=n("78bc"),p=n("7061"),g=n("b57f"),_=n("b707"),m=n("6483"),v=n("92a6"),b=n("aed7"),y=n("58db"),w=n("e8e3"),C=n("fdcc");function k(e,t,n){var o=_["p"].ordered(e),i=o.map((function(o){return Promise.resolve(o.provideHover(e,t,n)).then((function(e){return e&&O(e)?e:void 0}),(function(e){Object(C["f"])(e)}))}));return Promise.all(i).then(w["d"])}function O(e){var t="undefined"!==typeof e.range,n="undefined"!==typeof e.contents&&e.contents&&e.contents.length>0;return t&&n}Object(s["k"])("_executeHoverProvider",(function(e,t){return k(e,t,l["a"].None)}));var M=n("5fe7"),D=function(){function e(e,t,n,o,i){var r=this;this._computer=e,this._state=0,this._hoverTime=i,this._firstWaitScheduler=new M["d"]((function(){return r._triggerAsyncComputation()}),0),this._secondWaitScheduler=new M["d"]((function(){return r._triggerSyncComputation()}),0),this._loadingMessageScheduler=new M["d"]((function(){return r._showLoadingMessage()}),0),this._asyncComputationPromise=null,this._asyncComputationPromiseDone=!1,this._completeCallback=t,this._errorCallback=n,this._progressCallback=o}return e.prototype.setHoverTime=function(e){this._hoverTime=e},e.prototype._firstWaitTime=function(){return this._hoverTime/2},e.prototype._secondWaitTime=function(){return this._hoverTime/2},e.prototype._loadingMessageTime=function(){return 3*this._hoverTime},e.prototype._triggerAsyncComputation=function(){var e=this;this._state=2,this._secondWaitScheduler.schedule(this._secondWaitTime()),this._computer.computeAsync?(this._asyncComputationPromiseDone=!1,this._asyncComputationPromise=Object(M["f"])((function(t){return e._computer.computeAsync(t)})),this._asyncComputationPromise.then((function(t){e._asyncComputationPromiseDone=!0,e._withAsyncResult(t)}),(function(t){return e._onError(t)}))):this._asyncComputationPromiseDone=!0},e.prototype._triggerSyncComputation=function(){this._computer.computeSync&&this._computer.onResult(this._computer.computeSync(),!0),this._asyncComputationPromiseDone?(this._state=0,this._onComplete(this._computer.getResult())):(this._state=3,this._onProgress(this._computer.getResult()))},e.prototype._showLoadingMessage=function(){3===this._state&&this._onProgress(this._computer.getResultWithLoadingMessage())},e.prototype._withAsyncResult=function(e){e&&this._computer.onResult(e,!1),3===this._state&&(this._state=0,this._onComplete(this._computer.getResult()))},e.prototype._onComplete=function(e){this._completeCallback&&this._completeCallback(e)},e.prototype._onError=function(e){this._errorCallback?this._errorCallback(e):Object(C["e"])(e)},e.prototype._onProgress=function(e){this._progressCallback&&this._progressCallback(e)},e.prototype.start=function(e){if(0===e)0===this._state&&(this._state=1,this._firstWaitScheduler.schedule(this._firstWaitTime()),this._loadingMessageScheduler.schedule(this._loadingMessageTime()));else switch(this._state){case 0:this._triggerAsyncComputation(),this._secondWaitScheduler.cancel(),this._triggerSyncComputation();break;case 2:this._secondWaitScheduler.cancel(),this._triggerSyncComputation();break}},e.prototype.cancel=function(){this._loadingMessageScheduler.cancel(),1===this._state&&this._firstWaitScheduler.cancel(),2===this._state&&(this._secondWaitScheduler.cancel(),this._asyncComputationPromise&&(this._asyncComputationPromise.cancel(),this._asyncComputationPromise=null)),3===this._state&&this._asyncComputationPromise&&(this._asyncComputationPromise.cancel(),this._asyncComputationPromise=null),this._state=0},e}(),P=n("1898"),S=n("1b7d"),x=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),R=function(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;var o=Array(e),i=0;for(t=0;t<n;t++)for(var r=arguments[t],s=0,a=r.length;s<a;s++,i++)o[i]=r[s];return o},E=function(e){function t(t,n){var o=e.call(this)||this;return o.allowEditorOverflow=!0,o._id=t,o._editor=n,o._isVisible=!1,o._stoleFocus=!1,o._containerDomNode=document.createElement("div"),o._containerDomNode.className="monaco-editor-hover hidden",o._containerDomNode.tabIndex=0,o._domNode=document.createElement("div"),o._domNode.className="monaco-editor-hover-content",o.scrollbar=new P["a"](o._domNode,{}),o._register(o.scrollbar),o._containerDomNode.appendChild(o.scrollbar.getDomNode()),o.onkeydown(o._containerDomNode,(function(e){e.equals(9)&&o.hide()})),o._register(o._editor.onDidChangeConfiguration((function(e){e.hasChanged(34)&&o.updateFont()}))),o._editor.onDidLayoutChange((function(e){return o.layout()})),o.layout(),o._editor.addContentWidget(o),o._showAtPosition=null,o._showAtRange=null,o._stoleFocus=!1,o}return x(t,e),Object.defineProperty(t.prototype,"isVisible",{get:function(){return this._isVisible},set:function(e){this._isVisible=e,Object(d["Y"])(this._containerDomNode,"hidden",!this._isVisible)},enumerable:!0,configurable:!0}),t.prototype.getId=function(){return this._id},t.prototype.getDomNode=function(){return this._containerDomNode},t.prototype.showAt=function(e,t,n){this._showAtPosition=e,this._showAtRange=t,this.isVisible=!0,this._editor.layoutContentWidget(this),this._editor.render(),this._stoleFocus=n,n&&this._containerDomNode.focus()},t.prototype.hide=function(){this.isVisible&&(this.isVisible=!1,this._editor.layoutContentWidget(this),this._stoleFocus&&this._editor.focus())},t.prototype.getPosition=function(){return this.isVisible?{position:this._showAtPosition,range:this._showAtRange,preference:[1,2]}:null},t.prototype.dispose=function(){this._editor.removeContentWidget(this),e.prototype.dispose.call(this)},t.prototype.updateFont=function(){var e=this,t=Array.prototype.slice.call(this._domNode.getElementsByClassName("code"));t.forEach((function(t){return e._editor.applyFontInfo(t)}))},t.prototype.updateContents=function(e){this._domNode.textContent="",this._domNode.appendChild(e),this.updateFont(),this._editor.layoutContentWidget(this),this.onContentsChange()},t.prototype.onContentsChange=function(){this.scrollbar.scanDomNode()},t.prototype.layout=function(){var e=Math.max(this._editor.getLayoutInfo().height/4,250),t=this._editor.getOption(34),n=t.fontSize,o=t.lineHeight;this._domNode.style.fontSize=n+"px",this._domNode.style.lineHeight=o+"px",this._domNode.style.maxHeight=e+"px",this._domNode.style.maxWidth=Math.max(.66*this._editor.getLayoutInfo().width,500)+"px"},t}(S["a"]),I=function(e){function t(t,n){var o=e.call(this)||this;return o._id=t,o._editor=n,o._isVisible=!1,o._domNode=document.createElement("div"),o._domNode.className="monaco-editor-hover hidden",o._domNode.setAttribute("aria-hidden","true"),o._domNode.setAttribute("role","presentation"),o._showAtLineNumber=-1,o._register(o._editor.onDidChangeConfiguration((function(e){e.hasChanged(34)&&o.updateFont()}))),o._editor.addOverlayWidget(o),o}return x(t,e),Object.defineProperty(t.prototype,"isVisible",{get:function(){return this._isVisible},set:function(e){this._isVisible=e,Object(d["Y"])(this._domNode,"hidden",!this._isVisible)},enumerable:!0,configurable:!0}),t.prototype.getId=function(){return this._id},t.prototype.getDomNode=function(){return this._domNode},t.prototype.showAt=function(e){this._showAtLineNumber=e,this.isVisible||(this.isVisible=!0);var t=this._editor.getLayoutInfo(),n=this._editor.getTopForLineNumber(this._showAtLineNumber),o=this._editor.getScrollTop(),i=this._editor.getOption(49),r=this._domNode.clientHeight,s=n-o-(r-i)/2;this._domNode.style.left=t.glyphMarginLeft+t.glyphMarginWidth+"px",this._domNode.style.top=Math.max(Math.round(s),0)+"px"},t.prototype.hide=function(){this.isVisible&&(this.isVisible=!1)},t.prototype.getPosition=function(){return null},t.prototype.dispose=function(){this._editor.removeOverlayWidget(this),e.prototype.dispose.call(this)},t.prototype.updateFont=function(){var e=this,t=Array.prototype.slice.call(this._domNode.getElementsByTagName("code")),n=Array.prototype.slice.call(this._domNode.getElementsByClassName("code"));R(t,n).forEach((function(t){return e._editor.applyFontInfo(t)}))},t.prototype.updateContents=function(e){this._domNode.textContent="",this._domNode.appendChild(e),this.updateFont()},t}(S["a"]),N=n("dea0"),j=n("b7d0"),T=n("b400"),L=n("82c9"),F=n("5bd7"),A=n("958f"),W=n("8495"),K=n("0b54"),H=n("9e56"),V=n("303e"),U=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),B=function(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;var o=Array(e),i=0;for(t=0;t<n;t++)for(var r=arguments[t],s=0,a=r.length;s<a;s++,i++)o[i]=r[s];return o},G=d["a"],q=function(){function e(e,t,n){this.range=e,this.color=t,this.provider=n}return e}(),z=function(){function e(e,t){this.range=e,this.marker=t}return e}(),Z=function(){function e(e,t){this._markerDecorationsService=t,this._editor=e,this._result=[]}return e.prototype.setRange=function(e){this._range=e,this._result=[]},e.prototype.clearResult=function(){this._result=[]},e.prototype.computeAsync=function(e){if(!this._editor.hasModel()||!this._range)return Promise.resolve([]);var t=this._editor.getModel();return _["p"].has(t)?k(t,new p["a"](this._range.startLineNumber,this._range.startColumn),e):Promise.resolve([])},e.prototype.computeSync=function(){var e=this;if(!this._editor.hasModel()||!this._range)return[];var t=this._editor.getModel(),n=this._range.startLineNumber;if(n>this._editor.getModel().getLineCount())return[];var o=v["ColorDetector"].get(this._editor),i=t.getLineMaxColumn(n),r=this._editor.getLineDecorations(n),s=!1,c=this._range,u=r.map((function(r){var u=r.range.startLineNumber===n?r.range.startColumn:1,d=r.range.endLineNumber===n?r.range.endColumn:i;if(u>c.startColumn||c.endColumn>d)return null;var l=new a["a"](c.startLineNumber,u,c.startLineNumber,d),h=e._markerDecorationsService.getMarker(t,r);if(h)return new z(l,h);var p=o.getColorData(r.range.getStartPosition());if(!s&&p){s=!0;var g=p.colorInfo,_=g.color,m=g.range;return new q(m,_,p.provider)}if(Object(f["b"])(r.options.hoverMessage))return null;var v=r.options.hoverMessage?Object(w["b"])(r.options.hoverMessage):[];return{contents:v,range:l}}));return Object(w["d"])(u)},e.prototype.onResult=function(e,t){this._result=t?e.concat(this._result.sort((function(e,t){return e instanceof q?-1:t instanceof q?1:0}))):this._result.concat(e)},e.prototype.getResult=function(){return this._result.slice(0)},e.prototype.getResultWithLoadingMessage=function(){return this._result.slice(0).concat([this._getLoadingMessage()])},e.prototype._getLoadingMessage=function(){return{range:this._range,contents:[(new f["a"]).appendText(o["a"]("modesContentHover.loading","Loading..."))]}},e}(),J={type:2,filter:{include:H["b"].QuickFix}},X=function(e){function t(n,o,i,s,a,c){void 0===c&&(c=F["b"]);var u=e.call(this,t.ID,n)||this;return u._themeService=i,u._keybindingService=s,u._modeService=a,u._openerService=c,u.renderDisposable=u._register(new r["d"]),u._messages=[],u._lastRange=null,u._computer=new Z(u._editor,o),u._highlightDecorations=[],u._isChangingDecorations=!1,u._shouldFocus=!1,u._colorPicker=null,u._hoverOperation=new D(u._computer,(function(e){return u._withResult(e,!0)}),null,(function(e){return u._withResult(e,!1)}),u._editor.getOption(44).delay),u._register(d["o"](u.getDomNode(),d["d"].FOCUS,(function(){u._colorPicker&&d["f"](u.getDomNode(),"colorpicker-hover")}))),u._register(d["o"](u.getDomNode(),d["d"].BLUR,(function(){d["P"](u.getDomNode(),"colorpicker-hover")}))),u._register(n.onDidChangeConfiguration((function(e){u._hoverOperation.setHoverTime(u._editor.getOption(44).delay)}))),u._register(_["B"].onDidChange((function(e){u.isVisible&&u._lastRange&&u._messages.length>0&&(u._domNode.textContent="",u._renderMessages(u._lastRange,u._messages))}))),u}return U(t,e),t.prototype.dispose=function(){this._hoverOperation.cancel(),e.prototype.dispose.call(this)},t.prototype.onModelDecorationsChanged=function(){this._isChangingDecorations||this.isVisible&&(this._hoverOperation.cancel(),this._computer.clearResult(),this._colorPicker||this._hoverOperation.start(0))},t.prototype.startShowingAt=function(e,t,n){if(!this._lastRange||!this._lastRange.equalsRange(e)){if(this._hoverOperation.cancel(),this.isVisible)if(this._showAtPosition&&this._showAtPosition.lineNumber===e.startLineNumber){for(var o=[],i=0,r=this._messages.length;i<r;i++){var s=this._messages[i],a=s.range;a&&a.startColumn<=e.startColumn&&a.endColumn>=e.endColumn&&o.push(s)}if(o.length>0){if(Q(o,this._messages))return;this._renderMessages(e,o)}else this.hide()}else this.hide();this._lastRange=e,this._computer.setRange(e),this._shouldFocus=n,this._hoverOperation.start(t)}},t.prototype.hide=function(){this._lastRange=null,this._hoverOperation.cancel(),e.prototype.hide.call(this),this._isChangingDecorations=!0,this._highlightDecorations=this._editor.deltaDecorations(this._highlightDecorations,[]),this._isChangingDecorations=!1,this.renderDisposable.clear(),this._colorPicker=null},t.prototype.isColorPickerVisible=function(){return!!this._colorPicker},t.prototype._withResult=function(e,t){this._messages=e,this._lastRange&&this._messages.length>0?this._renderMessages(this._lastRange,this._messages):t&&this.hide()},t.prototype._renderMessages=function(e,n){var o=this;this.renderDisposable.dispose(),this._colorPicker=null;var i=1073741824,s=n[0].range?a["a"].lift(n[0].range):null,c=document.createDocumentFragment(),u=!0,g=!1,_=new r["b"],v=[];if(n.forEach((function(e){if(e.range)if(i=Math.min(i,e.range.startColumn),s=s?a["a"].plusRange(s,e.range):a["a"].lift(e.range),e instanceof q){g=!0;var t=e.color,n=t.red,p=t.green,w=t.blue,C=t.alpha,k=new h["c"](Math.round(255*n),Math.round(255*p),Math.round(255*w),C),O=new h["a"](k);if(!o._editor.hasModel())return;var M=o._editor.getModel(),D=new a["a"](e.range.startLineNumber,e.range.startColumn,e.range.endLineNumber,e.range.endColumn),P={range:e.range,color:e.color},S=new b["a"](O,[],0),x=new y["a"](c,S,o._editor.getOption(105),o._themeService);Object(m["a"])(M,P,e.provider,l["a"].None).then((function(t){if(S.colorPresentations=t||[],o._editor.hasModel()){var n=o._editor.getModel().getValueInRange(e.range);S.guessColorPresentation(O,n);var i=function(){var e,t;S.presentation.textEdit?(e=[S.presentation.textEdit],t=new a["a"](S.presentation.textEdit.range.startLineNumber,S.presentation.textEdit.range.startColumn,S.presentation.textEdit.range.endLineNumber,S.presentation.textEdit.range.endColumn),t=t.setEndPosition(t.endLineNumber,t.startColumn+S.presentation.textEdit.text.length)):(e=[{identifier:null,range:D,text:S.presentation.label,forceMoveMarkers:!1}],t=D.setEndPosition(D.endLineNumber,D.startColumn+S.presentation.label.length)),o._editor.pushUndoStop(),o._editor.executeEdits("colorpicker",e),S.presentation.additionalTextEdits&&(e=B(S.presentation.additionalTextEdits),o._editor.executeEdits("colorpicker",e),o.hide()),o._editor.pushUndoStop(),D=t},s=function(t){return Object(m["a"])(M,{range:D,color:{red:t.rgba.r/255,green:t.rgba.g/255,blue:t.rgba.b/255,alpha:t.rgba.a}},e.provider,l["a"].None).then((function(e){S.colorPresentations=e||[]}))},u=S.onColorFlushed((function(e){s(e).then(i)})),d=S.onDidChangeColor(s);o._colorPicker=x,o.showAt(D.getStartPosition(),D,o._shouldFocus),o.updateContents(c),o._colorPicker.layout(),o.renderDisposable.value=Object(r["e"])(u,d,x,_)}}))}else e instanceof z?(v.push(e),u=!1):e.contents.filter((function(e){return!Object(f["b"])(e)})).forEach((function(e){var t=G("div.hover-row.markdown-hover"),n=d["q"](t,G("div.hover-contents")),i=_.add(new N["a"](o._editor,o._modeService,o._openerService));_.add(i.onDidRenderCodeBlock((function(){n.className="hover-contents code-hover-contents",o.onContentsChange()})));var r=_.add(i.render(e));n.appendChild(r.element),c.appendChild(t),u=!1}))})),v.length){v.forEach((function(e){return c.appendChild(o.renderMarkerHover(e))}));var w=1===v.length?v[0]:v.sort((function(e,t){return T["c"].compare(e.marker.severity,t.marker.severity)}))[0];c.appendChild(this.renderMarkerStatusbar(w))}g||u||(this.showAt(new p["a"](e.startLineNumber,i),s,this._shouldFocus),this.updateContents(c)),this._isChangingDecorations=!0,this._highlightDecorations=this._editor.deltaDecorations(this._highlightDecorations,s?[{range:s,options:t._DECORATION_OPTIONS}]:[]),this._isChangingDecorations=!1},t.prototype.renderMarkerHover=function(e){var t=this,n=G("div.hover-row"),o=d["q"](n,G("div.marker.hover-contents")),i=e.marker,r=i.source,s=i.message,a=i.code,c=i.relatedInformation;this._editor.applyFontInfo(o);var u=d["q"](o,G("span"));if(u.style.whiteSpace="pre-wrap",u.innerText=s,r||a)if("string"===typeof a){var l=d["q"](o,G("span"));l.style.opacity="0.6",l.style.paddingLeft="6px",l.innerText=r&&a?r+"("+a+")":r||"("+a+")"}else if(a){var h=G("span");if(r){var f=d["q"](h,G("span"));f.innerText=r}this._codeLink=d["q"](h,G("a.code-link")),this._codeLink.setAttribute("href",a.link.toString()),this._codeLink.onclick=function(e){t._openerService.open(a.link),e.preventDefault(),e.stopPropagation()};var p=d["q"](this._codeLink,G("span"));p.innerText=a.value;l=d["q"](o,h);l.style.opacity="0.6",l.style.paddingLeft="6px"}if(Object(w["q"])(c))for(var g=function(e,n,i,r){var s=d["q"](o,G("div"));s.style.marginTop="8px";var a=d["q"](s,G("a"));a.innerText=Object(L["b"])(n)+"("+i+", "+r+"): ",a.style.cursor="pointer",a.onclick=function(e){e.stopPropagation(),e.preventDefault(),t._openerService&&t._openerService.open(n.with({fragment:i+","+r}),{fromUserGesture:!0}).catch(C["e"])};var c=d["q"](s,G("span"));c.innerText=e,_._editor.applyFontInfo(c)},_=this,m=0,v=c;m<v.length;m++){var b=v[m],y=b.message,k=b.resource,O=b.startLineNumber,M=b.startColumn;g(y,k,O,M)}return n},t.prototype.renderMarkerStatusbar=function(e){var t=this,n=G("div.hover-row.status-bar"),i=new r["b"],s=d["q"](n,G("div.actions"));e.marker.severity!==T["c"].Error&&e.marker.severity!==T["c"].Warning&&e.marker.severity!==T["c"].Info||i.add(this.renderAction(s,{label:o["a"]("peek problem","Peek Problem"),commandId:A["NextMarkerAction"].ID,run:function(){t.hide(),A["MarkerController"].get(t._editor).show(e.marker),t._editor.focus()}}));var a=d["q"](s,G("div"));a.style.opacity="0",a.style.transition="opacity 0.2s",setTimeout((function(){return a.style.opacity="1"}),200),a.textContent=o["a"]("checkingForQuickFixes","Checking for quick fixes..."),i.add(Object(r["h"])((function(){return a.remove()})));var c=this.getCodeActions(e.marker);return i.add(Object(r["h"])((function(){return c.cancel()}))),c.then((function(e){if(a.style.transition="",a.style.opacity="1",!e.validActions.length)return e.dispose(),void(a.textContent=o["a"]("noQuickFixes","No quick fixes available"));a.remove();var n=!1;i.add(Object(r["h"])((function(){n||e.dispose()}))),i.add(t.renderAction(s,{label:o["a"]("quick fixes","Quick Fix..."),commandId:K["e"].Id,run:function(o){n=!0;var i=K["f"].get(t._editor),r=d["C"](o);i.showCodeActions(J,e,{x:r.left+6,y:r.top+r.height+6})}}))})),this.renderDisposable.value=i,n},t.prototype.getCodeActions=function(e){var t=this;return Object(M["f"])((function(n){return Object(W["c"])(t._editor.getModel(),new a["a"](e.startLineNumber,e.startColumn,e.endLineNumber,e.endColumn),J,n)}))},t.prototype.renderAction=function(e,t){var n=d["q"](e,G("div.action-container")),o=d["q"](n,G("a.action"));t.iconClass&&d["q"](o,G("span.icon."+t.iconClass));var i=d["q"](o,G("span"));i.textContent=t.label;var r=this._keybindingService.lookupKeybinding(t.commandId);return r&&(i.title=t.label+" ("+r.getLabel()+")"),d["j"](n,d["d"].CLICK,(function(e){e.stopPropagation(),e.preventDefault(),t.run(n)}))},t.ID="editor.contrib.modesContentHoverWidget",t._DECORATION_OPTIONS=g["a"].register({className:"hoverHighlight"}),t}(E);function Q(e,t){if(!e&&t||e&&!t||e.length!==t.length)return!1;for(var n=0;n<e.length;n++){var o=e[n],i=t[n];if(o instanceof z&&i instanceof z)return T["a"].makeKey(o.marker)===T["a"].makeKey(i.marker);if(o instanceof q||i instanceof q)return!1;if(o instanceof z||i instanceof z)return!1;if(!Object(f["c"])(o.contents,i.contents))return!1}return!0}Object(j["e"])((function(e,t){var n=e.getColor(V["ec"]);n&&t.addRule(".monaco-editor-hover .hover-contents a.code-link span:hover { color: "+n+"; }")}));var Y=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),$=function(){function e(e){this._editor=e,this._lineNumber=-1,this._result=[]}return e.prototype.setLineNumber=function(e){this._lineNumber=e,this._result=[]},e.prototype.clearResult=function(){this._result=[]},e.prototype.computeSync=function(){var e=function(e){return{value:e}},t=this._editor.getLineDecorations(this._lineNumber),n=[];if(!t)return n;for(var o=0,i=t;o<i.length;o++){var r=i[o];if(r.options.glyphMarginClassName){var s=r.options.glyphMarginHoverMessage;s&&!Object(f["b"])(s)&&n.push.apply(n,Object(w["b"])(s).map(e))}}return n},e.prototype.onResult=function(e,t){this._result=this._result.concat(e)},e.prototype.getResult=function(){return this._result},e.prototype.getResultWithLoadingMessage=function(){return this.getResult()},e}(),ee=function(e){function t(n,o,i){void 0===i&&(i=F["b"]);var s=e.call(this,t.ID,n)||this;return s._renderDisposeables=s._register(new r["b"]),s._messages=[],s._lastLineNumber=-1,s._markdownRenderer=s._register(new N["a"](s._editor,o,i)),s._computer=new $(s._editor),s._hoverOperation=new D(s._computer,(function(e){return s._withResult(e)}),void 0,(function(e){return s._withResult(e)}),300),s}return Y(t,e),t.prototype.dispose=function(){this._hoverOperation.cancel(),e.prototype.dispose.call(this)},t.prototype.onModelDecorationsChanged=function(){this.isVisible&&(this._hoverOperation.cancel(),this._computer.clearResult(),this._hoverOperation.start(0))},t.prototype.startShowingAt=function(e){this._lastLineNumber!==e&&(this._hoverOperation.cancel(),this.hide(),this._lastLineNumber=e,this._computer.setLineNumber(e),this._hoverOperation.start(0))},t.prototype.hide=function(){this._lastLineNumber=-1,this._hoverOperation.cancel(),e.prototype.hide.call(this)},t.prototype._withResult=function(e){this._messages=e,this._messages.length>0?this._renderMessages(this._lastLineNumber,this._messages):this.hide()},t.prototype._renderMessages=function(e,t){this._renderDisposeables.clear();for(var n=document.createDocumentFragment(),o=0,i=t;o<i.length;o++){var r=i[o],s=this._markdownRenderer.render(r.value);this._renderDisposeables.add(s),n.appendChild(Object(d["a"])("div.hover-row",void 0,s.element))}this.updateContents(n),this.showAt(e)},t.ID="editor.contrib.modesGlyphHoverWidget",t}(I),te=n("efdb"),ne=n("6dec"),oe=n("1f84"),ie=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),re=function(e,t,n,o){var i,r=arguments.length,s=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)s=Reflect.decorate(e,t,n,o);else for(var a=e.length-1;a>=0;a--)(i=e[a])&&(s=(r<3?i(s):r>3?i(t,n,s):i(t,n))||s);return r>3&&s&&Object.defineProperty(t,n,s),s},se=function(e,t){return function(n,o){t(n,o,e)}},ae=function(){function e(e,t,n,o,i,s){var a=this;this._editor=e,this._openerService=t,this._modeService=n,this._markerDecorationsService=o,this._keybindingService=i,this._themeService=s,this._toUnhook=new r["b"],this._contentWidget=new r["d"],this._glyphWidget=new r["d"],this._isMouseDown=!1,this._hoverClicked=!1,this._hookEvents(),this._didChangeConfigurationHandler=this._editor.onDidChangeConfiguration((function(e){e.hasChanged(44)&&(a._hideWidgets(),a._unhookEvents(),a._hookEvents())}))}return Object.defineProperty(e.prototype,"contentWidget",{get:function(){return this._contentWidget.value||this._createHoverWidgets(),this._contentWidget.value},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"glyphWidget",{get:function(){return this._glyphWidget.value||this._createHoverWidgets(),this._glyphWidget.value},enumerable:!0,configurable:!0}),e.get=function(t){return t.getContribution(e.ID)},e.prototype._hookEvents=function(){var e=this,t=function(){return e._hideWidgets()},n=this._editor.getOption(44);this._isHoverEnabled=n.enabled,this._isHoverSticky=n.sticky,this._isHoverEnabled?(this._toUnhook.add(this._editor.onMouseDown((function(t){return e._onEditorMouseDown(t)}))),this._toUnhook.add(this._editor.onMouseUp((function(t){return e._onEditorMouseUp(t)}))),this._toUnhook.add(this._editor.onMouseMove((function(t){return e._onEditorMouseMove(t)}))),this._toUnhook.add(this._editor.onKeyDown((function(t){return e._onKeyDown(t)}))),this._toUnhook.add(this._editor.onDidChangeModelDecorations((function(){return e._onModelDecorationsChanged()})))):this._toUnhook.add(this._editor.onMouseMove(t)),this._toUnhook.add(this._editor.onMouseLeave(t)),this._toUnhook.add(this._editor.onDidChangeModel(t)),this._toUnhook.add(this._editor.onDidScrollChange((function(t){return e._onEditorScrollChanged(t)})))},e.prototype._unhookEvents=function(){this._toUnhook.clear()},e.prototype._onModelDecorationsChanged=function(){this.contentWidget.onModelDecorationsChanged(),this.glyphWidget.onModelDecorationsChanged()},e.prototype._onEditorScrollChanged=function(e){(e.scrollTopChanged||e.scrollLeftChanged)&&this._hideWidgets()},e.prototype._onEditorMouseDown=function(e){this._isMouseDown=!0;var t=e.target.type;9!==t||e.target.detail!==X.ID?12===t&&e.target.detail===ee.ID||(12!==t&&e.target.detail!==ee.ID&&(this._hoverClicked=!1),this._hideWidgets()):this._hoverClicked=!0},e.prototype._onEditorMouseUp=function(e){this._isMouseDown=!1},e.prototype._onEditorMouseMove=function(e){var t=e.target.type;if(!(this._isMouseDown&&this._hoverClicked&&this.contentWidget.isColorPickerVisible())&&(!this._isHoverSticky||9!==t||e.target.detail!==X.ID)&&(!this._isHoverSticky||12!==t||e.target.detail!==ee.ID)){if(7===t){var n=this._editor.getOption(34).typicalHalfwidthCharacterWidth/2,o=e.target.detail;o&&!o.isAfterLines&&"number"===typeof o.horizontalDistanceToText&&o.horizontalDistanceToText<n&&(t=6)}6===t?(this.glyphWidget.hide(),this._isHoverEnabled&&e.target.range&&this.contentWidget.startShowingAt(e.target.range,0,!1)):2===t?(this.contentWidget.hide(),this._isHoverEnabled&&e.target.position&&this.glyphWidget.startShowingAt(e.target.position.lineNumber)):this._hideWidgets()}},e.prototype._onKeyDown=function(e){5!==e.keyCode&&6!==e.keyCode&&57!==e.keyCode&&4!==e.keyCode&&this._hideWidgets()},e.prototype._hideWidgets=function(){!this._glyphWidget.value||!this._contentWidget.value||this._isMouseDown&&this._hoverClicked&&this._contentWidget.value.isColorPickerVisible()||(this._glyphWidget.value.hide(),this._contentWidget.value.hide())},e.prototype._createHoverWidgets=function(){this._contentWidget.value=new X(this._editor,this._markerDecorationsService,this._themeService,this._keybindingService,this._modeService,this._openerService),this._glyphWidget.value=new ee(this._editor,this._modeService,this._openerService)},e.prototype.showContentHover=function(e,t,n){this.contentWidget.startShowingAt(e,t,n)},e.prototype.dispose=function(){this._unhookEvents(),this._toUnhook.dispose(),this._didChangeConfigurationHandler.dispose(),this._glyphWidget.dispose(),this._contentWidget.dispose()},e.ID="editor.contrib.hover",e=re([se(1,F["a"]),se(2,u["a"]),se(3,te["a"]),se(4,ne["a"]),se(5,j["c"])],e),e}(),ce=function(e){function t(){return e.call(this,{id:"editor.action.showHover",label:o["a"]({key:"showHover",comment:["Label for action that will trigger the showing of a hover in the editor.","This allows for users to show the hover without using the mouse."]},"Show Hover"),alias:"Show Hover",precondition:void 0,kbOpts:{kbExpr:c["a"].editorTextFocus,primary:Object(i["a"])(2089,2087),weight:100}})||this}return ie(t,e),t.prototype.run=function(e,t){if(t.hasModel()){var n=ae.get(t);if(n){var o=t.getPosition(),i=new a["a"](o.lineNumber,o.column,o.lineNumber,o.column),r=2===t.getOption(2);n.showContentHover(i,1,r)}}},t}(s["b"]),ue=function(e){function t(){return e.call(this,{id:"editor.action.showDefinitionPreviewHover",label:o["a"]({key:"showDefinitionPreviewHover",comment:["Label for action that will trigger the showing of definition preview hover in the editor.","This allows for users to show the definition preview hover without using the mouse."]},"Show Definition Preview Hover"),alias:"Show Definition Preview Hover",precondition:void 0})||this}return ie(t,e),t.prototype.run=function(e,t){var n=ae.get(t);if(n){var o=t.getPosition();if(o){var i=new a["a"](o.lineNumber,o.column,o.lineNumber,o.column),r=oe["GotoDefinitionAtPositionEditorContribution"].get(t),s=r.startFindDefinitionFromCursor(o);s?s.then((function(){n.showContentHover(i,1,!0)})):n.showContentHover(i,1,!0)}}},t}(s["b"]);Object(s["h"])(ae.ID,ae),Object(s["f"])(ce),Object(s["f"])(ue),Object(j["e"])((function(e,t){var n=e.getColor(V["D"]);n&&t.addRule(".monaco-editor .hoverHighlight { background-color: "+n+"; }");var o=e.getColor(V["A"]);o&&t.addRule(".monaco-editor .monaco-editor-hover { background-color: "+o+"; }");var i=e.getColor(V["B"]);i&&(t.addRule(".monaco-editor .monaco-editor-hover { border: 1px solid "+i+"; }"),t.addRule(".monaco-editor .monaco-editor-hover .hover-row:not(:first-child):not(:empty) { border-top: 1px solid "+i.transparent(.5)+"; }"),t.addRule(".monaco-editor .monaco-editor-hover hr { border-top: 1px solid "+i.transparent(.5)+"; }"),t.addRule(".monaco-editor .monaco-editor-hover hr { border-bottom: 0px solid "+i.transparent(.5)+"; }"));var r=e.getColor(V["ec"]);r&&t.addRule(".monaco-editor .monaco-editor-hover a { color: "+r+"; }");var s=e.getColor(V["C"]);s&&t.addRule(".monaco-editor .monaco-editor-hover { color: "+s+"; }");var a=e.getColor(V["E"]);a&&t.addRule(".monaco-editor .monaco-editor-hover .hover-row .actions { background-color: "+a+"; }");var c=e.getColor(V["dc"]);c&&t.addRule(".monaco-editor .monaco-editor-hover code { background-color: "+c+"; }")}))},b805:function(e,t,n){},bd13:function(e,t,n){"use strict";n.d(t,"b",(function(){return l})),n.d(t,"a",(function(){return h})),n.d(t,"c",(function(){return f})),n.d(t,"e",(function(){return p})),n.d(t,"d",(function(){return g}));var o=n("e8e3"),i=n("2504"),r=n("fdcc"),s=n("b2cc"),a=n("b707"),c=function(e,t,n,o){function i(e){return e instanceof n?e:new n((function(t){t(e)}))}return new(n||(n=Promise))((function(n,r){function s(e){try{c(o.next(e))}catch(t){r(t)}}function a(e){try{c(o["throw"](e))}catch(t){r(t)}}function c(e){e.done?n(e.value):i(e.value).then(s,a)}c((o=o.apply(e,t||[])).next())}))},u=function(e,t){var n,o,i,r,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function a(e){return function(t){return c([e,t])}}function c(r){if(n)throw new TypeError("Generator is already executing.");while(s)try{if(n=1,o&&(i=2&r[0]?o["return"]:r[0]?o["throw"]||((i=o["return"])&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return s.label++,{value:r[1],done:!1};case 5:s.label++,o=r[1],r=[0];continue;case 7:r=s.ops.pop(),s.trys.pop();continue;default:if(i=s.trys,!(i=i.length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){s=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){s.label=r[1];break}if(6===r[0]&&s.label<i[1]){s.label=i[1],i=r;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(r);break}i[2]&&s.ops.pop(),s.trys.pop();continue}r=t.call(e,s)}catch(a){r=[6,a],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}};function d(e,t,n,i){var s=n.ordered(e),a=s.map((function(n){return Promise.resolve(i(n,e,t)).then(void 0,(function(e){Object(r["f"])(e)}))}));return Promise.all(a).then(o["m"]).then(o["d"])}function l(e,t,n){return d(e,t,a["f"],(function(e,t,o){return e.provideDefinition(t,o,n)}))}function h(e,t,n){return d(e,t,a["e"],(function(e,t,o){return e.provideDeclaration(t,o,n)}))}function f(e,t,n){return d(e,t,a["q"],(function(e,t,o){return e.provideImplementation(t,o,n)}))}function p(e,t,n){return d(e,t,a["C"],(function(e,t,o){return e.provideTypeDefinition(t,o,n)}))}function g(e,t,n,o){var i=this;return d(e,t,a["u"],(function(e,t,r){return c(i,void 0,void 0,(function(){var i,s;return u(this,(function(a){switch(a.label){case 0:return[4,e.provideReferences(t,r,{includeDeclaration:!0},o)];case 1:return i=a.sent(),n&&i&&2===i.length?[4,e.provideReferences(t,r,{includeDeclaration:!1},o)]:[2,i];case 2:return s=a.sent(),s&&1===s.length?[2,s]:[2,i]}}))}))}))}Object(s["k"])("_executeDefinitionProvider",(function(e,t){return l(e,t,i["a"].None)})),Object(s["k"])("_executeDeclarationProvider",(function(e,t){return h(e,t,i["a"].None)})),Object(s["k"])("_executeImplementationProvider",(function(e,t){return f(e,t,i["a"].None)})),Object(s["k"])("_executeTypeDefinitionProvider",(function(e,t){return p(e,t,i["a"].None)})),Object(s["k"])("_executeReferenceProvider",(function(e,t){return g(e,t,!1,i["a"].None)}))},eb68:function(e,t,n){},f187:function(e,t,n){"use strict";n.r(t),n.d(t,"DefinitionAction",(function(){return ne}));var o=n("3813"),i=n("5fe7"),r=n("fe45"),s=n("30db"),a=n("b055"),c=n("b2cc"),u=n("5717"),d=n("7061"),l=n("6a89"),h=n("c101"),f=n("b707"),p=n("351f"),g=n("88d4"),_=n("418f"),m=n("f68e"),v=n("dff7"),b=n("7e32"),y=n("4fc3"),w=n("b0cd"),C=n("b539"),k=n("bd13"),O=n("9e74"),M=n("bc04"),D=n("0a0f"),P=n("f5f3"),S=n("9eb8"),x=n("a666"),R=n("308f"),E=n("6dec"),I=n("82c9"),N=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),j=function(e,t,n,o){var i,r=arguments.length,s=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)s=Reflect.decorate(e,t,n,o);else for(var a=e.length-1;a>=0;a--)(i=e[a])&&(s=(r<3?i(s):r>3?i(t,n,s):i(t,n))||s);return r>3&&s&&Object.defineProperty(t,n,s),s},T=function(e,t){return function(n,o){t(n,o,e)}},L=new y["d"]("hasSymbols",!1),F=Object(D["c"])("ISymbolNavigationService"),A=function(){function e(e,t,n,o){this._editorService=t,this._notificationService=n,this._keybindingService=o,this._currentModel=void 0,this._currentIdx=-1,this._ignoreEditorChange=!1,this._ctxHasSymbols=L.bindTo(e)}return e.prototype.reset=function(){this._ctxHasSymbols.reset(),Object(x["f"])(this._currentState),Object(x["f"])(this._currentMessage),this._currentModel=void 0,this._currentIdx=-1},e.prototype.put=function(e){var t=this,n=e.parent.parent;if(n.references.length<=1)this.reset();else{this._currentModel=n,this._currentIdx=n.references.indexOf(e),this._ctxHasSymbols.set(!0),this._showMessage();var o=new z(this._editorService),i=o.onDidChange((function(e){if(!t._ignoreEditorChange){var o=t._editorService.getActiveCodeEditor();if(o){var i=o.getModel(),r=o.getPosition();if(i&&r){for(var s=!1,a=!1,c=0,u=n.references;c<u.length;c++){var d=u[c];if(Object(I["e"])(d.uri,i.uri))s=!0,a=a||l["a"].containsPosition(d.range,r);else if(s)break}s&&a||t.reset()}}}}));this._currentState=Object(x["e"])(o,i)}},e.prototype.revealNext=function(e){var t=this;if(!this._currentModel)return Promise.resolve();this._currentIdx+=1,this._currentIdx%=this._currentModel.references.length;var n=this._currentModel.references[this._currentIdx];return this._showMessage(),this._ignoreEditorChange=!0,this._editorService.openCodeEditor({resource:n.uri,options:{selection:l["a"].collapseToStart(n.range),revealInCenterIfOutsideViewport:!0}},e).finally((function(){t._ignoreEditorChange=!1}))},e.prototype._showMessage=function(){Object(x["f"])(this._currentMessage);var e=this._keybindingService.lookupKeybinding("editor.gotoNextSymbolFromResult"),t=e?Object(v["a"])("location.kb","Symbol {0} of {1}, {2} for next",this._currentIdx+1,this._currentModel.references.length,e.getLabel()):Object(v["a"])("location","Symbol {0} of {1}",this._currentIdx+1,this._currentModel.references.length);this._currentMessage=this._notificationService.status(t)},e=j([T(0,y["c"]),T(1,u["a"]),T(2,w["a"]),T(3,E["a"])],e),e}();Object(P["b"])(F,A,!0),Object(c["g"])(new(function(e){function t(){return e.call(this,{id:"editor.gotoNextSymbolFromResult",precondition:L,kbOpts:{weight:100,primary:70}})||this}return N(t,e),t.prototype.runEditorCommand=function(e,t){return e.get(F).revealNext(t)},t}(c["c"]))),S["a"].registerCommandAndKeybindingRule({id:"editor.gotoNextSymbolFromResult.cancel",weight:100,when:L,primary:9,handler:function(e){e.get(F).reset()}});var W,K,H,V,U,B,G,q,z=function(){function e(e){this._listener=new Map,this._disposables=new x["b"],this._onDidChange=new R["a"],this.onDidChange=this._onDidChange.event,this._disposables.add(e.onCodeEditorRemove(this._onDidRemoveEditor,this)),this._disposables.add(e.onCodeEditorAdd(this._onDidAddEditor,this)),e.listCodeEditors().forEach(this._onDidAddEditor,this)}return e.prototype.dispose=function(){this._disposables.dispose(),this._onDidChange.dispose(),this._listener.forEach(x["f"])},e.prototype._onDidAddEditor=function(e){var t=this;this._listener.set(e,Object(x["e"])(e.onDidChangeCursorPosition((function(n){return t._onDidChange.fire({editor:e})})),e.onDidChangeModelContent((function(n){return t._onDidChange.fire({editor:e})}))))},e.prototype._onDidRemoveEditor=function(e){Object(x["f"])(this._listener.get(e)),this._listener.delete(e)},e=j([T(0,u["a"])],e),e}(),Z=n("0f70"),J=n("6d8e"),X=n("ef8e"),Q=n("d379"),Y=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),$=function(e,t,n,o){function i(e){return e instanceof n?e:new n((function(t){t(e)}))}return new(n||(n=Promise))((function(n,r){function s(e){try{c(o.next(e))}catch(t){r(t)}}function a(e){try{c(o["throw"](e))}catch(t){r(t)}}function c(e){e.done?n(e.value):i(e.value).then(s,a)}c((o=o.apply(e,t||[])).next())}))},ee=function(e,t){var n,o,i,r,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function a(e){return function(t){return c([e,t])}}function c(r){if(n)throw new TypeError("Generator is already executing.");while(s)try{if(n=1,o&&(i=2&r[0]?o["return"]:r[0]?o["throw"]||((i=o["return"])&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return s.label++,{value:r[1],done:!1};case 5:s.label++,o=r[1],r=[0];continue;case 7:r=s.ops.pop(),s.trys.pop();continue;default:if(i=s.trys,!(i=i.length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){s=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){s.label=r[1];break}if(6===r[0]&&s.label<i[1]){s.label=i[1],i=r;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(r);break}i[2]&&s.ops.pop(),s.trys.pop();continue}r=t.call(e,s)}catch(a){r=[6,a],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}};b["c"].appendMenuItem(7,{submenu:8,title:v["a"]("peek.submenu","Peek"),group:"navigation",order:100});var te=function(e){function t(t,n){var o=e.call(this,n)||this;return o._configuration=t,o}return Y(t,e),t.prototype.run=function(e,t){var n=this;if(!t.hasModel())return Promise.resolve(void 0);var r=e.get(w["a"]),s=e.get(u["a"]),a=e.get(C["a"]),c=e.get(F),d=t.getModel(),l=t.getPosition(),h=new M["b"](t,5),f=Object(i["j"])(this._getLocationModel(d,l,h.token),h.token).then((function(e){return $(n,void 0,void 0,(function(){var n,i,r,a;return ee(this,(function(u){if(!e||h.token.isCancellationRequested)return[2];if(Object(o["a"])(e.ariaMessage),e.referenceAt(d.uri,l)&&(i=this._getAlternativeCommand(t),i!==this.id&&(n=t.getAction(i))),r=e.references.length,0===r)this._configuration.muteMessage||(a=d.getWordAtPosition(l),p["a"].get(t).showMessage(this._getNoResultFoundMessage(a),l));else{if(1!==r||!n)return[2,this._onResult(s,c,t,e)];n.run()}return[2]}))}))}),(function(e){r.error(e)})).finally((function(){h.dispose()}));return a.showWhile(f,250),f},t.prototype._onResult=function(e,t,n,o){return $(this,void 0,void 0,(function(){var i,r,s,a;return ee(this,(function(c){switch(c.label){case 0:return i=this._getGoToPreference(n),n instanceof Q["a"]||!(this._configuration.openInPeek||"peek"===i&&o.references.length>1)?[3,1]:(this._openInPeek(n,o),[3,3]);case 1:return r=o.firstReference(),s=o.references.length>1&&"gotoAndPeek"===i,[4,this._openReference(n,e,r,this._configuration.openToSide,!s)];case 2:a=c.sent(),s&&a?this._openInPeek(a,o):o.dispose(),"goto"===i&&t.put(r),c.label=3;case 3:return[2]}}))}))},t.prototype._openReference=function(e,t,n,o,i){return $(this,void 0,void 0,(function(){var r,s,a,c;return ee(this,(function(u){switch(u.label){case 0:return r=void 0,Object(f["G"])(n)&&(r=n.targetSelectionRange),r||(r=n.range),[4,t.openCodeEditor({resource:n.uri,options:{selection:l["a"].collapseToStart(r),revealInCenterIfOutsideViewport:!0}},e,o)];case 1:return s=u.sent(),s?(i&&(a=s.getModel(),c=s.deltaDecorations([],[{range:r,options:{className:"symbolHighlight"}}]),setTimeout((function(){s.getModel()===a&&s.deltaDecorations(c,[])}),350)),[2,s]):[2,void 0]}}))}))},t.prototype._openInPeek=function(e,t){var n=_["a"].get(e);n&&e.hasModel()?n.toggleWidget(e.getSelection(),Object(i["f"])((function(e){return Promise.resolve(t)})),this._configuration.openInPeek):t.dispose()},t}(c["b"]),ne=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return Y(t,e),t.prototype._getLocationModel=function(e,t,n){return $(this,void 0,void 0,(function(){var o;return ee(this,(function(i){switch(i.label){case 0:return o=m["c"].bind,[4,Object(k["b"])(e,t,n)];case 1:return[2,new(o.apply(m["c"],[void 0,i.sent(),v["a"]("def.title","Definitions")]))]}}))}))},t.prototype._getNoResultFoundMessage=function(e){return e&&e.word?v["a"]("noResultWord","No definition found for '{0}'",e.word):v["a"]("generic.noResults","No definition found")},t.prototype._getAlternativeCommand=function(e){return e.getOption(41).alternativeDefinitionCommand},t.prototype._getGoToPreference=function(e){return e.getOption(41).multipleDefinitions},t}(te),oe=s["g"]&&!Z["l"]?2118:70;Object(c["f"])((W=function(e){function t(){var n=e.call(this,{openToSide:!1,openInPeek:!1,muteMessage:!1},{id:t.id,label:v["a"]("actions.goToDecl.label","Go to Definition"),alias:"Go to Definition",precondition:y["a"].and(h["a"].hasDefinitionProvider,h["a"].isInEmbeddedEditor.toNegated()),kbOpts:{kbExpr:h["a"].editorTextFocus,primary:oe,weight:100},contextMenuOpts:{group:"navigation",order:1.1},menuOpts:{menuId:19,group:"4_symbol_nav",order:2,title:v["a"]({key:"miGotoDefinition",comment:["&& denotes a mnemonic"]},"Go to &&Definition")}})||this;return O["a"].registerCommandAlias("editor.action.goToDeclaration",t.id),n}return Y(t,e),t}(ne),W.id="editor.action.revealDefinition",W)),Object(c["f"])((K=function(e){function t(){var n=e.call(this,{openToSide:!0,openInPeek:!1,muteMessage:!1},{id:t.id,label:v["a"]("actions.goToDeclToSide.label","Open Definition to the Side"),alias:"Open Definition to the Side",precondition:y["a"].and(h["a"].hasDefinitionProvider,h["a"].isInEmbeddedEditor.toNegated()),kbOpts:{kbExpr:h["a"].editorTextFocus,primary:Object(r["a"])(2089,oe),weight:100}})||this;return O["a"].registerCommandAlias("editor.action.openDeclarationToTheSide",t.id),n}return Y(t,e),t}(ne),K.id="editor.action.revealDefinitionAside",K)),Object(c["f"])((H=function(e){function t(){var n=e.call(this,{openToSide:!1,openInPeek:!0,muteMessage:!1},{id:t.id,label:v["a"]("actions.previewDecl.label","Peek Definition"),alias:"Peek Definition",precondition:y["a"].and(h["a"].hasDefinitionProvider,g["b"].notInPeekEditor,h["a"].isInEmbeddedEditor.toNegated()),kbOpts:{kbExpr:h["a"].editorTextFocus,primary:582,linux:{primary:3140},weight:100},contextMenuOpts:{menuId:8,group:"peek",order:2}})||this;return O["a"].registerCommandAlias("editor.action.previewDeclaration",t.id),n}return Y(t,e),t}(ne),H.id="editor.action.peekDefinition",H));var ie=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return Y(t,e),t.prototype._getLocationModel=function(e,t,n){return $(this,void 0,void 0,(function(){var o;return ee(this,(function(i){switch(i.label){case 0:return o=m["c"].bind,[4,Object(k["a"])(e,t,n)];case 1:return[2,new(o.apply(m["c"],[void 0,i.sent(),v["a"]("decl.title","Declarations")]))]}}))}))},t.prototype._getNoResultFoundMessage=function(e){return e&&e.word?v["a"]("decl.noResultWord","No declaration found for '{0}'",e.word):v["a"]("decl.generic.noResults","No declaration found")},t.prototype._getAlternativeCommand=function(e){return e.getOption(41).alternativeDeclarationCommand},t.prototype._getGoToPreference=function(e){return e.getOption(41).multipleDeclarations},t}(te);Object(c["f"])((V=function(e){function t(){return e.call(this,{openToSide:!1,openInPeek:!1,muteMessage:!1},{id:t.id,label:v["a"]("actions.goToDeclaration.label","Go to Declaration"),alias:"Go to Declaration",precondition:y["a"].and(h["a"].hasDeclarationProvider,h["a"].isInEmbeddedEditor.toNegated()),contextMenuOpts:{group:"navigation",order:1.3},menuOpts:{menuId:19,group:"4_symbol_nav",order:3,title:v["a"]({key:"miGotoDeclaration",comment:["&& denotes a mnemonic"]},"Go to &&Declaration")}})||this}return Y(t,e),t.prototype._getNoResultFoundMessage=function(e){return e&&e.word?v["a"]("decl.noResultWord","No declaration found for '{0}'",e.word):v["a"]("decl.generic.noResults","No declaration found")},t}(ie),V.id="editor.action.revealDeclaration",V)),Object(c["f"])(function(e){function t(){return e.call(this,{openToSide:!1,openInPeek:!0,muteMessage:!1},{id:"editor.action.peekDeclaration",label:v["a"]("actions.peekDecl.label","Peek Declaration"),alias:"Peek Declaration",precondition:y["a"].and(h["a"].hasDeclarationProvider,g["b"].notInPeekEditor,h["a"].isInEmbeddedEditor.toNegated()),contextMenuOpts:{menuId:8,group:"peek",order:3}})||this}return Y(t,e),t}(ie));var re=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return Y(t,e),t.prototype._getLocationModel=function(e,t,n){return $(this,void 0,void 0,(function(){var o;return ee(this,(function(i){switch(i.label){case 0:return o=m["c"].bind,[4,Object(k["e"])(e,t,n)];case 1:return[2,new(o.apply(m["c"],[void 0,i.sent(),v["a"]("typedef.title","Type Definitions")]))]}}))}))},t.prototype._getNoResultFoundMessage=function(e){return e&&e.word?v["a"]("goToTypeDefinition.noResultWord","No type definition found for '{0}'",e.word):v["a"]("goToTypeDefinition.generic.noResults","No type definition found")},t.prototype._getAlternativeCommand=function(e){return e.getOption(41).alternativeTypeDefinitionCommand},t.prototype._getGoToPreference=function(e){return e.getOption(41).multipleTypeDefinitions},t}(te);Object(c["f"])((U=function(e){function t(){return e.call(this,{openToSide:!1,openInPeek:!1,muteMessage:!1},{id:t.ID,label:v["a"]("actions.goToTypeDefinition.label","Go to Type Definition"),alias:"Go to Type Definition",precondition:y["a"].and(h["a"].hasTypeDefinitionProvider,h["a"].isInEmbeddedEditor.toNegated()),kbOpts:{kbExpr:h["a"].editorTextFocus,primary:0,weight:100},contextMenuOpts:{group:"navigation",order:1.4},menuOpts:{menuId:19,group:"4_symbol_nav",order:3,title:v["a"]({key:"miGotoTypeDefinition",comment:["&& denotes a mnemonic"]},"Go to &&Type Definition")}})||this}return Y(t,e),t}(re),U.ID="editor.action.goToTypeDefinition",U)),Object(c["f"])((B=function(e){function t(){return e.call(this,{openToSide:!1,openInPeek:!0,muteMessage:!1},{id:t.ID,label:v["a"]("actions.peekTypeDefinition.label","Peek Type Definition"),alias:"Peek Type Definition",precondition:y["a"].and(h["a"].hasTypeDefinitionProvider,g["b"].notInPeekEditor,h["a"].isInEmbeddedEditor.toNegated()),contextMenuOpts:{menuId:8,group:"peek",order:4}})||this}return Y(t,e),t}(re),B.ID="editor.action.peekTypeDefinition",B));var se=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return Y(t,e),t.prototype._getLocationModel=function(e,t,n){return $(this,void 0,void 0,(function(){var o;return ee(this,(function(i){switch(i.label){case 0:return o=m["c"].bind,[4,Object(k["c"])(e,t,n)];case 1:return[2,new(o.apply(m["c"],[void 0,i.sent(),v["a"]("impl.title","Implementations")]))]}}))}))},t.prototype._getNoResultFoundMessage=function(e){return e&&e.word?v["a"]("goToImplementation.noResultWord","No implementation found for '{0}'",e.word):v["a"]("goToImplementation.generic.noResults","No implementation found")},t.prototype._getAlternativeCommand=function(e){return e.getOption(41).alternativeImplementationCommand},t.prototype._getGoToPreference=function(e){return e.getOption(41).multipleImplementations},t}(te);Object(c["f"])((G=function(e){function t(){return e.call(this,{openToSide:!1,openInPeek:!1,muteMessage:!1},{id:t.ID,label:v["a"]("actions.goToImplementation.label","Go to Implementations"),alias:"Go to Implementations",precondition:y["a"].and(h["a"].hasImplementationProvider,h["a"].isInEmbeddedEditor.toNegated()),kbOpts:{kbExpr:h["a"].editorTextFocus,primary:2118,weight:100},menuOpts:{menuId:19,group:"4_symbol_nav",order:4,title:v["a"]({key:"miGotoImplementation",comment:["&& denotes a mnemonic"]},"Go to &&Implementations")},contextMenuOpts:{group:"navigation",order:1.45}})||this}return Y(t,e),t}(se),G.ID="editor.action.goToImplementation",G)),Object(c["f"])((q=function(e){function t(){return e.call(this,{openToSide:!1,openInPeek:!0,muteMessage:!1},{id:t.ID,label:v["a"]("actions.peekImplementation.label","Peek Implementations"),alias:"Peek Implementations",precondition:y["a"].and(h["a"].hasImplementationProvider,g["b"].notInPeekEditor,h["a"].isInEmbeddedEditor.toNegated()),kbOpts:{kbExpr:h["a"].editorTextFocus,primary:3142,weight:100},contextMenuOpts:{menuId:8,group:"peek",order:5}})||this}return Y(t,e),t}(se),q.ID="editor.action.peekImplementation",q));var ae=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return Y(t,e),t.prototype._getNoResultFoundMessage=function(e){return e?v["a"]("references.no","No references found for '{0}'",e.word):v["a"]("references.noGeneric","No references found")},t.prototype._getAlternativeCommand=function(e){return e.getOption(41).alternativeReferenceCommand},t.prototype._getGoToPreference=function(e){return e.getOption(41).multipleReferences},t}(te);Object(c["f"])(function(e){function t(){return e.call(this,{openToSide:!1,openInPeek:!1,muteMessage:!1},{id:"editor.action.goToReferences",label:v["a"]("goToReferences.label","Go to References"),alias:"Go to References",precondition:y["a"].and(h["a"].hasReferenceProvider,g["b"].notInPeekEditor,h["a"].isInEmbeddedEditor.toNegated()),kbOpts:{kbExpr:h["a"].editorTextFocus,primary:1094,weight:100},contextMenuOpts:{group:"navigation",order:1.45},menuOpts:{menuId:19,group:"4_symbol_nav",order:5,title:v["a"]({key:"miGotoReference",comment:["&& denotes a mnemonic"]},"Go to &&References")}})||this}return Y(t,e),t.prototype._getLocationModel=function(e,t,n){return $(this,void 0,void 0,(function(){var o;return ee(this,(function(i){switch(i.label){case 0:return o=m["c"].bind,[4,Object(k["d"])(e,t,!0,n)];case 1:return[2,new(o.apply(m["c"],[void 0,i.sent(),v["a"]("ref.title","References")]))]}}))}))},t}(ae)),Object(c["f"])(function(e){function t(){return e.call(this,{openToSide:!1,openInPeek:!0,muteMessage:!1},{id:"editor.action.referenceSearch.trigger",label:v["a"]("references.action.label","Peek References"),alias:"Peek References",precondition:y["a"].and(h["a"].hasReferenceProvider,g["b"].notInPeekEditor,h["a"].isInEmbeddedEditor.toNegated()),contextMenuOpts:{menuId:8,group:"peek",order:6}})||this}return Y(t,e),t.prototype._getLocationModel=function(e,t,n){return $(this,void 0,void 0,(function(){var o;return ee(this,(function(i){switch(i.label){case 0:return o=m["c"].bind,[4,Object(k["d"])(e,t,!1,n)];case 1:return[2,new(o.apply(m["c"],[void 0,i.sent(),v["a"]("ref.title","References")]))]}}))}))},t}(ae));var ce=function(e){function t(t,n,o){var i=e.call(this,t,{id:"editor.action.goToLocation",label:v["a"]("label.generic","Go To Any Symbol"),alias:"Go To Any Symbol",precondition:y["a"].and(g["b"].notInPeekEditor,h["a"].isInEmbeddedEditor.toNegated())})||this;return i._references=n,i._gotoMultipleBehaviour=o,i}return Y(t,e),t.prototype._getLocationModel=function(e,t,n){return $(this,void 0,void 0,(function(){return ee(this,(function(e){return[2,new m["c"](this._references,v["a"]("generic.title","Locations"))]}))}))},t.prototype._getNoResultFoundMessage=function(e){return e&&v["a"]("generic.noResult","No results for '{0}'",e.word)||""},t.prototype._getGoToPreference=function(e){var t;return null!==(t=this._gotoMultipleBehaviour)&&void 0!==t?t:e.getOption(41).multipleReferences},t.prototype._getAlternativeCommand=function(){return""},t}(te);O["a"].registerCommand({id:"editor.action.goToLocations",description:{description:"Go to locations from a position in a file",args:[{name:"uri",description:"The text document in which to start",constraint:J["a"]},{name:"position",description:"The position at which to start",constraint:d["a"].isIPosition},{name:"locations",description:"An array of locations.",constraint:Array},{name:"multiple",description:"Define what to do when having multiple results, either `peek`, `gotoAndPeek`, or `goto"}]},handler:function(e,t,n,o,i,r){return $(void 0,void 0,void 0,(function(){var s,c;return ee(this,(function(l){switch(l.label){case 0:return Object(X["a"])(J["a"].isUri(t)),Object(X["a"])(d["a"].isIPosition(n)),Object(X["a"])(Array.isArray(o)),Object(X["a"])("undefined"===typeof i||"string"===typeof i),Object(X["a"])("undefined"===typeof r||"boolean"===typeof r),s=e.get(u["a"]),[4,s.openCodeEditor({resource:t},s.getFocusedCodeEditor())];case 1:return c=l.sent(),Object(a["a"])(c)?(c.setPosition(n),c.revealPositionInCenterIfOutsideViewport(n,0),[2,c.invokeWithinContext((function(e){var t=new ce({muteMessage:!0,openInPeek:Boolean(r),openToSide:!1},o,i);e.get(D["a"]).invokeFunction(t.run.bind(t),c)}))]):[2]}}))}))}}),O["a"].registerCommand({id:"editor.action.peekLocations",description:{description:"Peek locations from a position in a file",args:[{name:"uri",description:"The text document in which to start",constraint:J["a"]},{name:"position",description:"The position at which to start",constraint:d["a"].isIPosition},{name:"locations",description:"An array of locations.",constraint:Array},{name:"multiple",description:"Define what to do when having multiple results, either `peek`, `gotoAndPeek`, or `goto"}]},handler:function(e,t,n,o,i){return $(void 0,void 0,void 0,(function(){return ee(this,(function(r){return e.get(O["b"]).executeCommand("editor.action.goToLocations",t,n,o,i,!0),[2]}))}))}}),O["a"].registerCommand({id:"editor.action.findReferences",handler:function(e,t,n){Object(X["a"])(J["a"].isUri(t)),Object(X["a"])(d["a"].isIPosition(n));var o=e.get(u["a"]);return o.openCodeEditor({resource:t},o.getFocusedCodeEditor()).then((function(e){if(Object(a["a"])(e)&&e.hasModel()){var t=_["a"].get(e);if(t){var o=Object(i["f"])((function(t){return Object(k["d"])(e.getModel(),d["a"].lift(n),!1,t).then((function(e){return new m["c"](e,v["a"]("ref.title","References"))}))})),r=new l["a"](n.lineNumber,n.column,n.lineNumber,n.column);return Promise.resolve(t.toggleWidget(r,o,!1))}}}))}}),O["a"].registerCommandAlias("editor.action.showReferences","editor.action.peekLocations")},f68e:function(e,t,n){"use strict";n.d(t,"b",(function(){return d})),n.d(t,"a",(function(){return h})),n.d(t,"c",(function(){return f}));var o=n("dff7"),i=n("308f"),r=n("82c9"),s=n("a666"),a=n("3742"),c=n("9c3e"),u=n("6a89"),d=function(){function e(e,t,n,o){this.isProviderFirst=e,this.parent=t,this._range=n,this._rangeCallback=o,this.id=c["b"].nextId()}return Object.defineProperty(e.prototype,"uri",{get:function(){return this.parent.uri},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"range",{get:function(){return this._range},set:function(e){this._range=e,this._rangeCallback(this)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"ariaMessage",{get:function(){return Object(o["a"])("aria.oneReference","symbol in {0} on line {1} at column {2}",Object(r["b"])(this.uri),this.range.startLineNumber,this.range.startColumn)},enumerable:!0,configurable:!0}),e}(),l=function(){function e(e){this._modelReference=e}return e.prototype.dispose=function(){this._modelReference.dispose()},e.prototype.preview=function(e,t){void 0===t&&(t=8);var n=this._modelReference.object.textEditorModel;if(n){var o=e.startLineNumber,i=e.startColumn,r=e.endLineNumber,s=e.endColumn,a=n.getWordUntilPosition({lineNumber:o,column:i-t}),c=new u["a"](o,a.startColumn,o,i),d=new u["a"](r,s,r,1073741824),l=n.getValueInRange(c).replace(/^\s+/,""),h=n.getValueInRange(e),f=n.getValueInRange(d).replace(/\s+$/,"");return{value:l+h+f,highlight:{start:l.length,end:l.length+h.length}}}},e}(),h=function(){function e(e,t){this.parent=e,this.uri=t,this.children=[]}return e.prototype.dispose=function(){Object(s["f"])(this._preview),this._preview=void 0},Object.defineProperty(e.prototype,"preview",{get:function(){return this._preview},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"failure",{get:function(){return this._loadFailure},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"ariaMessage",{get:function(){var e=this.children.length;return 1===e?Object(o["a"])("aria.fileReferences.1","1 symbol in {0}, full path {1}",Object(r["b"])(this.uri),this.uri.fsPath):Object(o["a"])("aria.fileReferences.N","{0} symbols in {1}, full path {2}",e,Object(r["b"])(this.uri),this.uri.fsPath)},enumerable:!0,configurable:!0}),e.prototype.resolve=function(e){var t=this;return this._resolved?Promise.resolve(this):Promise.resolve(e.createModelReference(this.uri).then((function(e){var n=e.object;if(!n)throw e.dispose(),new Error;return t._preview=new l(e),t._resolved=!0,t}),(function(e){return t.children.length=0,t._resolved=!0,t._loadFailure=e,t})))},e}(),f=function(){function e(t,n){var o=this;this._disposables=new s["b"],this.groups=[],this.references=[],this._onDidChangeReferenceRange=new i["a"],this.onDidChangeReferenceRange=this._onDidChangeReferenceRange.event,this._links=t,this._title=n;var r,a=t[0];t.sort(e._compareReferences);for(var c=0,l=t;c<l.length;c++){var f=l[c];if(r&&r.uri.toString()===f.uri.toString()||(r=new h(this,f.uri),this.groups.push(r)),0===r.children.length||!u["a"].equalsRange(f.range,r.children[r.children.length-1].range)){var p=new d(a===f,r,f.targetSelectionRange||f.range,(function(e){return o._onDidChangeReferenceRange.fire(e)}));this.references.push(p),r.children.push(p)}}}return e.prototype.dispose=function(){Object(s["f"])(this.groups),this._disposables.dispose(),this._onDidChangeReferenceRange.dispose(),this.groups.length=0},e.prototype.clone=function(){return new e(this._links,this._title)},Object.defineProperty(e.prototype,"title",{get:function(){return this._title},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"isEmpty",{get:function(){return 0===this.groups.length},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"ariaMessage",{get:function(){return this.isEmpty?Object(o["a"])("aria.result.0","No results found"):1===this.references.length?Object(o["a"])("aria.result.1","Found 1 symbol in {0}",this.references[0].uri.fsPath):1===this.groups.length?Object(o["a"])("aria.result.n1","Found {0} symbols in {1}",this.references.length,this.groups[0].uri.fsPath):Object(o["a"])("aria.result.nm","Found {0} symbols in {1} files",this.references.length,this.groups.length)},enumerable:!0,configurable:!0}),e.prototype.nextOrPreviousReference=function(e,t){var n=e.parent,o=n.children.indexOf(e),i=n.children.length,r=n.parent.groups.length;return 1===r||t&&o+1<i||!t&&o>0?(o=t?(o+1)%i:(o+i-1)%i,n.children[o]):(o=n.parent.groups.indexOf(n),t?(o=(o+1)%r,n.parent.groups[o].children[0]):(o=(o+r-1)%r,n.parent.groups[o].children[n.parent.groups[o].children.length-1]))},e.prototype.nearestReference=function(e,t){var n=this.references.map((function(n,o){return{idx:o,prefixLen:a["c"](n.uri.toString(),e.toString()),offsetDist:100*Math.abs(n.range.startLineNumber-t.lineNumber)+Math.abs(n.range.startColumn-t.column)}})).sort((function(e,t){return e.prefixLen>t.prefixLen?-1:e.prefixLen<t.prefixLen?1:e.offsetDist<t.offsetDist?-1:e.offsetDist>t.offsetDist?1:0}))[0];if(n)return this.references[n.idx]},e.prototype.referenceAt=function(e,t){for(var n=0,o=this.references;n<o.length;n++){var i=o[n];if(i.uri.toString()===e.toString()&&u["a"].containsPosition(i.range,t))return i}},e.prototype.firstReference=function(){for(var e=0,t=this.references;e<t.length;e++){var n=t[e];if(n.isProviderFirst)return n}return this.references[0]},e._compareReferences=function(e,t){return a["e"](e.uri.toString(),t.uri.toString())||u["a"].compareRangesUsingStarts(e.range,t.range)},e}()},fe86:function(e,t,n){}}]);