(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a5d8afde","chunk-00e7bce4"],{"029c":function(t,e,i){"use strict";i("0a32")},"03e87":function(t,e,i){"use strict";i("b0d9")},"07ac":function(t,e,i){var a=i("23e7"),n=i("6f53").values;a({target:"Object",stat:!0},{values:function(t){return n(t)}})},"0a32":function(t,e,i){},"14d2":function(t,e,i){"use strict";i("e07b")},"1a0b":function(t,e,i){"use strict";i("c93e")},"498a":function(t,e,i){"use strict";var a=i("23e7"),n=i("58a8").trim,s=i("c8d2");a({target:"String",proto:!0,forced:s("trim")},{trim:function(){return n(this)}})},"49da":function(t,e,i){"use strict";i("6f23")},"688b":function(t,e,i){},"6f23":function(t,e,i){},b0d9:function(t,e,i){},b733:function(t,e,i){"use strict";i("688b")},c7fe:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("dialogComp",{staticClass:"sourceDom",attrs:{hideHeader:!1,needClose:"true",zIndex:"4",position:"fixed",draw:!1,top:0,right:0,bottom:0,left:0,drag:!1,title:t.$t("dialog.choiceSet.label3"),width:400,height:150,type:"detailInfo"},on:{close:t.closeAddDialog},scopedSlots:t._u([{key:"center",fn:function(){return[i("div",{staticClass:"add-container"},[i("el-input",{class:{"is-error":t.addFormDialog.inputError},attrs:{size:"small",placeholder:t.$t("dialog.choiceSet.placeholder")},model:{value:t.addFormDialog.name,callback:function(e){t.$set(t.addFormDialog,"name",e)},expression:"addFormDialog.name"}}),i("div",{staticClass:"bottom-btn"},[i("span",{staticClass:"cursor-btn cancel margin-right-8",on:{click:t.closeAddDialog}},[t._v(" "+t._s(t.$t("messageTips.cancel"))+" ")]),i("span",{staticClass:"cursor-btn confirm",on:{click:t.saveData}},[t._v(" "+t._s(t.$t("menuIconName.confirm"))+" ")])])],1)]},proxy:!0}])})},n=[],s=(i("b0c0"),i("d3b7"),i("159b"),i("e9c4"),{name:"",props:["currentModifyItem"],data:function(){return{addFormDialog:{menuState:!1,dialogState:!1,name:"",inputError:!1}}},created:function(){this.currentModifyItem&&(this.addFormDialog.name=this.currentModifyItem.name)},methods:{closeAddDialog:function(){this.addFormDialog.name="",this.$store.commit("setActivedType",""),this.$emit("closeAddDialog")},saveData:function(){var t=this;if(""==this.addFormDialog.name)return this.$message.error(this.$t("messageTips.nameNotEmpty")),void(this.addFormDialog.inputError=!0);var e=0;if(window.scene.selectionSets.forEach((function(i){i.name==t.addFormDialog.name&&e++})),e>0)return this.$message.error(this.addFormDialog.name+" "+this.$t("messageTips.nameAlreadyExists")),!1;if(this.currentModifyItem){window.scene.removeSelectionSet(this.currentModifyItem.name);var i=this.currentModifyItem,a=i.color,n=i.opacity,s=i.ids;window.scene.addSelectionSet(this.addFormDialog.name,s,a,n),this.$message.success(this.$t("messageTips.renameSuccess"))}else{var o={};window.scene.getSelection().forEach((function(t){if(o[t.model.id])o[t.model.id].push(t.id);else{var e=[];e.push(t.id),o[t.model.id]=e}})),window.scene.addSelectionSet(this.addFormDialog.name,o,"",""),this.$message.success(this.$t("messageTips.createdSuccess")),window.scene.clearSelection(),window.scene.render()}var r=[];window.scene.selectionSets.forEach((function(t){r.push(JSON.parse(JSON.parse(JSON.stringify(t))))})),this.$store.commit("setSelectionSets",r),this.closeAddDialog()}}}),o=s,r=(i("029c"),i("b733"),i("2877")),c=Object(r["a"])(o,a,n,!1,null,"21597eda",null);e["default"]=c.exports},c8d2:function(t,e,i){var a=i("5e77").PROPER,n=i("d039"),s=i("5899"),o="​᠎";t.exports=function(t){return n((function(){return!!s[t]()||o[t]()!==o||a&&s[t].name!==t}))}},c93e:function(t,e,i){},cb29:function(t,e,i){var a=i("23e7"),n=i("81d5"),s=i("44d2");a({target:"Array",proto:!0},{fill:n}),s("fill")},e07b:function(t,e,i){},f3bc:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("dialogComp",{ref:"sceneManageRef",staticClass:"sceneManageDom",attrs:{needClose:!1,zIndex:99,draw:!1,left:t.dialogLeft,top:"100%",drag:!1,title:t.$t("dialog.sceneManage.name"),icon:"icon-details",width:t.dialogWidth,height:t.dialogHeight+"%",borderRadius:"0",type:"detailInfo",hideHeader:!0},scopedSlots:t._u([{key:"center",fn:function(){return[i("div",{staticClass:"scene-container"},[t.onlyPreview?i("div",{staticClass:"tab-wrap"},[i("div",{staticClass:"tab-wrap-content"},t._l(t.menuData,(function(e){return i("div",{key:e.name,staticClass:"tab-item",class:{active:t.localActiveTab===e.name},on:{click:function(i){return t.changeMenuName(e.name)}}},[t._v(" "+t._s(e.label)+" ")])})),0)]):t._e(),i("div",{staticClass:"menu-content"},[i("div",{directives:[{name:"show",rawName:"v-show",value:"element"===t.localActiveTab,expression:"localActiveTab === 'element'"}],staticClass:"tab-content-wrapper"},[i("elementList",{attrs:{compKey:t.compKey,isShare:t.isShare,isVothingScenemanager:t.isVothingScenemanager},on:{setInteractiveData:t.setInteractiveData,setStructureTree:t.setStructureTree,setCurrentViewData:t.setCurrentViewData}})],1),t.onlyPreview?["viewpoint"===t.localActiveTab?i("div",{staticClass:"tab-content-wrapper"},[i("viewpoint",{ref:"viewpoint"})],1):t._e(),"markup"===t.localActiveTab?i("div",{staticClass:"tab-content-wrapper"},[i("markup",{ref:"markup"})],1):t._e(),"animation"===t.localActiveTab?i("div",{staticClass:"tab-content-wrapper"},[i("animation",{ref:"animation"})],1):t._e(),"trigger"===t.localActiveTab?i("div",{staticClass:"tab-content-wrapper"},[i("trigger",{ref:"trigger",attrs:{compKey:t.compKey}})],1):t._e(),"pathAnimation"===t.localActiveTab?i("div",{staticClass:"tab-content-wrapper path-animation-wrap"},[i("pathAnimation",{ref:"pathAnimation"})],1):t._e(),"screening"===t.localActiveTab?i("div",{staticClass:"tab-content-wrapper"},[i("filterComponent",{ref:"screening"})],1):t._e()]:t._e()],2)]),i("div",{staticClass:"switch-btn",on:{click:function(e){return t.switchDialog()}}},[t.dialogLeft>=0?i("i",{staticClass:"el-icon-caret-left el-icon"}):i("i",{staticClass:"el-icon-caret-right el-icon"})]),t.addFormDialog.dialogState?i("addViewpointMarkupDialog",{attrs:{addType:"viewpoint",isEditName:t.isEditName,inputValue:t.addFormDialog.name},on:{closeAddDialog:t.closeAddDialog,saveData:t.saveData}}):t._e()]},proxy:!0}])})},n=[],s=i("c7eb"),o=i("1da1"),r=(i("d3b7"),i("159b"),i("b0c0"),function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"filter-element-wrap"},[i("div",{staticClass:"all-color"},[i("div",{staticClass:"btn cursor-btn",on:{click:function(e){return t.allSetColor()}}},[t._v(" "+t._s(t.allSetColorState?t.$t("dialog.choiceSet.label7"):t.$t("dialog.choiceSet.label4"))+" ")])]),i("div",{staticClass:"list-wrap"},t._l(t.selectionList,(function(e){return i("div",{key:e.name,staticClass:"list-item",on:{click:function(i){return i.stopPropagation(),t.selectitem(e)}}},[i("div",{staticClass:"name"},[i("CommonSVG",{attrs:{size:16,"icon-class":"tb_screeningicon"}}),i("span",{staticClass:"text"},[t._v(t._s(e.name))])],1),i("div",{staticClass:"handle"},[i("div",{staticClass:"isHide"},[t.isEdit?i("el-tooltip",{attrs:{enterable:!1,"open-delay":100,effect:"dark",content:t.$t("menuIconName.delete"),placement:"top"}},[i("span",{staticClass:"menu-item cursor-btn",staticStyle:{"margin-top":"-2px"}},[i("CommonSVG",{attrs:{size:16,"icon-class":"delete"},nativeOn:{click:function(i){return i.stopPropagation(),t.handleitem(e,"remove")}}})],1)]):t._e(),t.isEdit?i("div",{staticClass:"menu-item setting-wrap"},[i("span",{staticClass:"cursor-btn"},[i("CommonSVG",{attrs:{size:18,"icon-class":"set_up_feature"}})],1),i("div",{staticClass:"dropdown"},[i("div",{staticClass:"dropdown-menu"},[i("div",{staticClass:"drop-item",on:{click:function(i){return i.stopPropagation(),t.handleitem(e,"rename")}}},[i("CommonSVG",{attrs:{color:"#DCDCDC",size:"16","icon-class":"amend_feature"}}),i("span",{staticClass:"text"},[t._v(t._s(t.$t("menuIconName.rename")))])],1),i("div",{staticClass:"drop-item",on:{click:function(i){return i.stopPropagation(),t.handleitem(e,"update")}}},[i("CommonSVG",{attrs:{color:"#DCDCDC",size:"16","icon-class":"tb_remove_active"}}),i("span",{staticClass:"text"},[t._v(t._s(t.$t("menuIconName.update")))])],1),i("div",{staticClass:"drop-item",on:{click:function(i){return i.stopPropagation(),t.handleitem(e,"addElement")}}},[i("CommonSVG",{attrs:{color:"#DCDCDC",size:"16","icon-class":"once"}}),i("span",{staticClass:"text"},[t._v(t._s(t.$t("dialog.choiceSet.label6")))])],1)])])]):t._e(),e.color?[t.isEdit&&t.setColorArr.includes(e.name)?i("el-tooltip",{attrs:{enterable:!1,"open-delay":100,effect:"dark",content:t.$t("dialog.choiceSet.label5"),placement:"top"}},[i("span",{staticClass:"menu-item cursor-btn",staticStyle:{"margin-top":"-2px"}},[i("CommonSVG",{attrs:{color:"var(--theme)",size:18,"icon-class":"tint_feature"},nativeOn:{click:function(i){return i.stopPropagation(),t.handleitem(e,"resetColor")}}})],1)]):t._e(),t.isEdit&&!t.setColorArr.includes(e.name)?i("el-tooltip",{attrs:{enterable:!1,"open-delay":100,effect:"dark",content:t.$t("menuIconName.color"),placement:"top"}},[i("span",{staticClass:"menu-item cursor-btn",staticStyle:{"margin-top":"-2px"}},[i("CommonSVG",{attrs:{size:18,color:"#98A2B3","icon-class":"tint_feature"},nativeOn:{click:function(i){return i.stopPropagation(),t.handleitem(e,"setColor")}}})],1)]):t._e()]:t._e()],2),i("span",{on:{click:function(t){t.stopPropagation()}}},[i("el-color-picker",{attrs:{"show-alpha":"",size:"mini"},on:{change:function(i){return t.setMarkupColor(i,e)}},model:{value:e.selectColor,callback:function(i){t.$set(e,"selectColor",i)},expression:"item.selectColor"}})],1)])])})),0),t.addFormDialog.dialogState?i("AddScreeningDialog",{attrs:{currentModifyItem:t.currentModifyItem},on:{closeAddDialog:t.closeAddDialog}}):t._e()],1)}),c=[],l=i("3835"),m=(i("3ca3"),i("ddb0"),i("d81d"),i("ac1f"),i("5319"),i("1276"),i("99af"),i("a9e3"),i("caad"),i("2532"),i("a630"),i("a434"),i("e9c4"),i("07ac"),i("c7fe")),d={name:"",data:function(){return{currentModifyItem:null,addFormDialog:{dialogState:!1,name:"",inputError:!1},setColorArr:[],allSetColorState:!1}},components:{CommonSVG:function(){return i.e("chunk-51f90655").then(i.bind(null,"17d0"))},AddScreeningDialog:m["default"]},computed:{isEdit:function(){return this.$store.state.scene.sceneEditMode},selectionList:function(){var t=this.$store.state.selectionSets.selectionList;return t.map((function(t){if(t.color){var e=t.color.replace(/[(|)|rgb]/g,""),i=e.split(","),a=Object(l["a"])(i,3),n=a[0],s=a[1],o=a[2];t.selectColor="rgba(".concat(n,",").concat(s,",").concat(o,", ").concat(t.opacity,")")}else t.selectColor="";return t})),t}},mounted:function(){this.init(),this.$bus.on("onSetFilterComponentState",this.setListColorState)},beforeDestroy:function(){this.$bus.off("onSetFilterComponentState",this.setListColorState)},methods:{setListColorState:function(){this.setColorArr=[]},setMarkupColor:function(t,e){var i,a,n,s;if(t){var o=t.replace(/[(|)|rgba]/g,""),r=o.split(","),c=Object(l["a"])(r,4);i=c[0],a=c[1],n=c[2],s=c[3]}var m=Object.assign(e,{color:i?"rgb(".concat(i,",").concat(a,",").concat(n,")"):"",opacity:i?Number(s):""});if(window.scene.addSelectionSet(m.name,m.ids,m.color,m.opacity),this.setColorArr.includes(e.name)){var d=Array.from(window.scene.features.keys()),u=[];for(var p in m.ids)d.includes(p)&&(u=u.concat(m.ids[p]));if(u.length){var h=window.scene.createArrayObject(u);h.setColor(m.color,m.opacity),window.scene.render()}}if(t)this.handleitem(e,"setColor");else{var f=this.setColorArr.indexOf(m.name);for(var g in this.setColorArr.splice(f,1),m.ids)m.ids[g].forEach((function(t){window.scene.findObject(t).resetColor()}));window.scene.render()}this.init()},closeAddDialog:function(){this.addFormDialog.dialogState=!1,this.addFormDialog.name=""},init:function(){var t=[];window.scene.selectionSets.forEach((function(e){t.push(JSON.parse(JSON.parse(JSON.stringify(e))))})),this.$store.commit("setSelectionSets",t)},allSetColor:function(){var t=this;if(this.allSetColorState){var e=[];if(this.setColorArr.forEach((function(t){var i=window.scene.selectionSets.get(t),a=Object.values(i.ids);a.forEach((function(t){e=e.concat(t)}))})),e.length){var i=window.scene.createArrayObject(e);i.resetColor(),this.allSetColorState=!1,this.setColorArr=[],this.$nextTick((function(){window.scene.render()}))}}else window.scene.selectionSets.forEach((function(e){var i=JSON.parse(JSON.parse(JSON.stringify(e)));if(i.color){t.setColorArr.includes(i.name)||t.setColorArr.push(i.name);var a=[];for(var n in i.ids)a=a.concat(i.ids[n]);if(a.length){var s=window.scene.createArrayObject(a);s.setColor(i.color,i.opacity)}window.scene.render(),t.allSetColorState=!0}}))},selectitem:function(t){var e=Array.from(window.scene.features.keys());for(var i in t.ids)e.includes(i)&&t.ids[i].forEach((function(t){var e=window.scene.findObject(t);e&&(e.selected=!0)}));this.$store.commit("toogleElementMenu","model"),window.scene.render()},handleitem:function(t,e){var i=this;switch(e){case"remove":return void this.$confirm(this.$t("messageTips.deleteSomething",{name:t.name}),this.$t("messageTips.tipsTitle"),{confirmButtonText:this.$t("menuIconName.confirm"),cancelButtonText:this.$t("messageTips.cancel"),type:"warning"}).then((function(){window.scene.removeSelectionSet(t.name),i.$message({type:"success",message:i.$t("messageTips.deleteSuccess")}),i.init()})).catch((function(){}));case"rename":return this.currentModifyItem=t,this.addFormDialog.name=t.name,void(this.addFormDialog.dialogState=!0);case"update":var a=window.scene.selectedObjects;if(0===a.size)return void this.$message.info(this.$t("dialog.choiceSet.message1"));for(var n in t.ids)t.ids[n].forEach((function(t){window.scene.findObject(t).resetColor()}));var s={};window.scene.getSelection().forEach((function(t){if(s[t.model.id])s[t.model.id].push(t.id);else{var e=[];e.push(t.id),s[t.model.id]=e}}));var o=Object.assign(t,{ids:s});window.scene.addSelectionSet(o.name,o.ids,o.color,o.opacity),-1!==this.setColorArr.indexOf(t.name)&&this.handleitem(o,"setColor"),this.$message.success(this.$t("messageTips.updateSuccess")),window.scene.clearSelection(),this.$store.commit("toogleElementMenu","");break;case"addElement":var r=window.scene.selectedObjects;if(0===r.size)return void this.$message.info(this.$t("dialog.choiceSet.message2"));for(var c in t.ids)t.ids[c].forEach((function(t){window.scene.findObject(t).resetColor()}));var l=t.ids;r.forEach((function(t){if(l[t.model.id])l[t.model.id].push(t.id);else{var e=[];e.push(t.id),l[t.model.id]=e}}));var m=Object.assign(t,{ids:l});window.scene.addSelectionSet(m.name,m.ids,m.color,m.opacity),-1!==this.setColorArr.indexOf(t.name)&&this.handleitem(m,"setColor"),this.$message.success(this.$t("dialog.choiceSet.message3")),window.scene.clearSelection(),this.$store.commit("toogleElementMenu","");break;case"setColor":var d=Array.from(window.scene.features.keys()),u=[];for(var p in t.ids)d.includes(p)&&(u=u.concat(t.ids[p]));if(u.length){var h=window.scene.createArrayObject(u);h.setColor(t.color,t.opacity)}-1===this.setColorArr.indexOf(t.name)&&this.setColorArr.push(t.name),window.scene.clearSelection(),window.scene.render();break;case"resetColor":for(var f in t.ids)t.ids[f].forEach((function(t){window.scene.findObject(t).resetColor()}));var g=this.setColorArr.indexOf(t.name);this.setColorArr.splice(g,1),window.scene.render();break}this.init()}}},u=d,p=(i("03e87"),i("14d2"),i("2877")),h=Object(p["a"])(u,r,c,!1,null,"922a8e26",null),f=h.exports,g=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"top-search"},[i("el-input",{attrs:{size:"small",clearable:"",placeholder:t.$t("others.search")},on:{input:t.setComputedTreeDatas},model:{value:t.searchKeyword,callback:function(e){t.searchKeyword=e},expression:"searchKeyword"}},[i("i",{staticClass:"el-input__icon el-icon-search",attrs:{slot:"prefix"},slot:"prefix"})])],1),i("div",{staticClass:"bottom-tree-wrapper"},[i("div",t._b({staticClass:"bottom-tree"},"div",{"data-update":t.needUpdateScene},!1),t._l(t.computedTreeDatas,(function(e,a){return i("div",{key:a,staticClass:"tree-list"},[e.datas.length?i("div",{staticClass:"list-title item-hover flex-box",on:{click:function(t){e.listState=!e.listState}}},[i("div",{staticClass:"element-name"},[i("el-tooltip",{attrs:{enterable:!1,content:e.listState?t.$t("others.collapse"):t.$t("others.expand"),effect:"dark",placement:"top"}},[e.listState?i("i",{staticClass:"el-icon-caret-top mg-r4"}):i("i",{staticClass:"el-icon-caret-bottom mg-r4"})]),i("span",{staticStyle:{"margin-right":"4px"}},[i("CommonSVG",{attrs:{"icon-class":e.icon,size:16}})],1),t._v(" "+t._s(e.name)+" ")],1),"underlay"!==e.label&&t.isEdit?i("div",{staticClass:"handle-all-feature"},[i("el-tooltip",{attrs:{enterable:!1,content:t.$t("menuIconName.remove"),effect:"dark",placement:"top"}},[i("span",{staticClass:"margin-left-10"},[i("CommonSVG",{attrs:{size:16,"icon-class":"delete"},nativeOn:{click:function(i){return i.stopPropagation(),t.removeBatchFeature(e)}}})],1)])],1):t._e()]):t._e(),i("el-collapse-transition",[i("div",{directives:[{name:"show",rawName:"v-show",value:e.listState,expression:"treeDatas.listState"}],staticClass:"list-content"},t._l(e.datas,(function(n,s){return i("div",{key:n.label,staticClass:"content-item"},[i("div",{staticClass:"list-title",staticStyle:{width:"100%",padding:"0"}},[i("div",{staticClass:"item",class:{featureSelected:t.currentSelectFeatureIDs.includes(n.id)},staticStyle:{width:"100%"},on:{click:function(e){return t.toggleTreeNodel(n)}}},[i("div",{staticClass:"item-left item-hover"},["model"===a?i("el-tooltip",{attrs:{enterable:!1,content:n.listState?t.$t("others.collapse"):t.$t("others.expand"),effect:"dark",placement:"top"}},[n.listState?i("i",{staticClass:"el-icon-caret-top"}):i("i",{staticClass:"el-icon-caret-bottom"})]):t._e(),i("span",{staticClass:"el-icon iconfont icon-tuceng",style:"padding-left: "+("model"===a?24:38)+"px;min-width:40px"}),i("el-tooltip",{attrs:{enterable:!1,"open-delay":500,effect:"dark",content:""==n.name?e.name+s:n.name,placement:"top"}},[i("span",[t._v(t._s(""==n.name?e.name+s:n.name))])])],1),i("div",{staticClass:"item-right pd-r0",style:t.modelSectionMenuStyle(s,a)},[!t.isEdit||t.isShare||t.isVothingScenemanager?t._e():i("el-tooltip",{attrs:{enterable:!1,effect:"dark",content:t.$t("menuIconName.remove"),placement:"top"}},[i("span",{staticClass:"menu-item",staticStyle:{"margin-top":"-2px"}},[i("CommonSVG",{attrs:{size:16,"icon-class":"delete"},nativeOn:{click:function(e){return e.stopPropagation(),t.handleViewsSubmenu(n,s,"remove")}}})],1)]),!t.isEdit||t.isShare||t.isVothingScenemanager||!n.data||n.data.assetLabel?t._e():i("el-tooltip",{attrs:{enterable:!1,effect:"dark",content:t.$t("menuIconName.setUp"),placement:"top"}},[i("span",{staticClass:"menu-item"},[i("CommonSVG",{attrs:{size:18,"icon-class":"set_up_feature"},nativeOn:{click:function(e){return e.stopPropagation(),t.handleViewsSubmenu(n,s,"setting")}}})],1)]),"model"===a?[!t.isEdit||t.isShare||t.isVothingScenemanager?t._e():i("el-tooltip",{attrs:{enterable:!1,effect:"dark",content:t.$t("menuIconName.filter"),placement:"top"}},[i("span",{staticClass:"menu-item"},[i("CommonSVG",{attrs:{size:18,"icon-class":"filtration_feature"},nativeOn:{click:function(e){return e.stopPropagation(),t.handleViewsSubmenu(n,s,"filter")}}})],1)])]:t._e(),t.unwantedLocationMenuType.includes(a)?t._e():[i("el-tooltip",{attrs:{"open-delay":500,enterable:!1,effect:"dark",content:t.$t("menuIconName.location"),placement:"top"}},[i("span",{staticClass:"menu-item"},[i("CommonSVG",{attrs:{"icon-class":"focusing_feature",size:16},nativeOn:{click:function(e){return e.stopPropagation(),t.handleViewsSubmenu(n,s,"location")}}})],1)])],"model"==a?[i("el-tooltip",{attrs:{enterable:!1,"open-delay":500,effect:"dark",content:t.$t("menuIconName.section"),placement:"top"}},[i("span",{staticClass:"menu-item"},[i("CommonSVG",{attrs:{color:t.aboutSection.sectionActivated&&-1==t.aboutSection.index&&t.aboutSection.parentIndex==s?"var(--theme)":"#98A2B3","icon-class":"tree_cutting_feature",size:16},nativeOn:{click:function(e){return e.stopPropagation(),t.handleViewsSubmenu(n,s,"section")}}})],1)])]:t._e(),n.visible?i("el-tooltip",{attrs:{enterable:!1,effect:"dark",content:t.$t("others.visible"),placement:"top"}},[i("span",{staticClass:"menu-item"},[i("CommonSVG",{attrs:{size:16,"icon-class":"tree_visible_feature"},nativeOn:{click:function(e){return e.stopPropagation(),t.handleViewsSubmenu(n,s,"hide")}}})],1)]):t._e(),n.visible?t._e():i("el-tooltip",{attrs:{enterable:!1,effect:"dark",content:t.$t("others.visible1"),placement:"top"}},[i("span",{staticClass:"menu-item"},[i("CommonSVG",{attrs:{size:16,color:"#475059","icon-class":"tree_hidden_feature"},nativeOn:{click:function(e){return e.stopPropagation(),t.handleViewsSubmenu(n,s,"show")}}})],1)])],2)])]),"model"==a?[i("el-collapse-transition",[i("div",{directives:[{name:"show",rawName:"v-show",value:n.listState,expression:"data.listState"}],staticClass:"list-content"},[t._l(n.formatViews,(function(e,a){return i("div",{key:a},[e.children.length?[i("div",{staticClass:"list-title pd-r0",on:{click:function(t){e.listState=!e.listState}}},[i("div",{staticClass:"flex item-hover"},[i("el-tooltip",{attrs:{enterable:!1,content:e.listState?t.$t("others.collapse"):t.$t("others.expand"),effect:"dark",placement:"top"}},[e.listState?i("i",{staticClass:"el-icon-caret-top"}):i("i",{staticClass:"el-icon-caret-bottom"})]),i("CommonSVG",{staticStyle:{"margin-left":"40px"},attrs:{"icon-class":"view-icon-"+e.icon,color:"#FFFFFF",size:16}}),t._v(" "+t._s(e.label)+" ")],1),i("div",{staticClass:"right-icon"},[e.visible>0?i("el-tooltip",{attrs:{enterable:!1,effect:"dark",content:t.$t("menuIconName.hideView"),placement:"top"}},[e.visible>0?i("span",{staticClass:"menu-item"},[i("CommonSVG",{attrs:{size:16,"icon-class":"tree_visible_feature"},nativeOn:{click:function(i){return i.stopPropagation(),t.hideAllView(e,"hide")}}})],1):t._e()]):t._e(),e.visible?t._e():i("el-tooltip",{attrs:{enterable:!1,effect:"dark",content:t.$t("menuIconName.showView"),placement:"top"}},[e.visible?t._e():i("span",{staticClass:"menu-item"},[i("CommonSVG",{attrs:{size:16,color:"#475059","icon-class":"tree_hidden_feature"},nativeOn:{click:function(i){return i.stopPropagation(),t.hideAllView(e,"show")}}})],1)])],1)]),i("el-collapse-transition",[i("div",{directives:[{name:"show",rawName:"v-show",value:e.listState,expression:"item.listState"}],staticClass:"list-content"},t._l(e.children,(function(n,s){return i("div",{key:n.id,staticClass:"content-item"},[i("div",{staticClass:"item",staticStyle:{"padding-left":"94px"}},[i("div",{staticClass:"item-left"},[i("el-tooltip",{attrs:{enterable:!1,"open-delay":500,effect:"dark",content:n.name,placement:"top"}},[i("span",[t._v(t._s(n.name))])])],1),i("div",{staticClass:"item-right",style:t.modelViewMenuStyle(a,s)},["loaded"==n.status?i("el-tooltip",{attrs:{enterable:!1,"open-delay":500,effect:"dark",content:t.toolTipContent(n),placement:"top"}},["loaded"==n.status?i("span",{staticClass:"menu-item"},[i("CommonSVG",{attrs:{"icon-class":"tree_structure_feature",size:16},nativeOn:{click:function(e){return e.stopPropagation(),t.handleViewsSubmenu(n,a,"viewTree")}}})],1):t._e()]):t._e(),"loaded"==n.status?i("el-tooltip",{attrs:{enterable:!1,"open-delay":500,effect:"dark",content:t.$t("menuIconName.section"),placement:"top"}},["loaded"==n.status?i("span",{staticClass:"menu-item"},[i("CommonSVG",{attrs:{"icon-class":"tree_cutting_feature",color:t.aboutSection.sectionActivated&&t.aboutSection.index==a&&t.aboutSection.parentIndex==s?"var(--theme)":"#98A2B3",size:16},nativeOn:{click:function(e){e.stopPropagation(),t.aboutSection.parentIndex=s,t.handleViewsSubmenu(n,a,"section")}}})],1):t._e()]):t._e(),"loaded"==n.status&&"2D"===e.type&&"Section"!==n.category&&"Elevation"!==n.category?i("el-tooltip",{attrs:{enterable:!1,"open-delay":500,effect:"dark",content:t.$t("menuIconName.interactive2D3D"),placement:"top"}},["loaded"==n.status?i("span",{staticClass:"menu-item"},[i("CommonSVG",{attrs:{"icon-class":"view_icon_sheet",color:"#98A2B3",size:16},nativeOn:{click:function(e){e.stopPropagation(),t.aboutSection.parentIndex=s,t.handleViewsSubmenu(n,a,"interactive")}}})],1):t._e()]):t._e(),"loaded"==n.status?i("el-tooltip",{attrs:{enterable:!1,"open-delay":500,effect:"dark",content:t.$t("menuIconName.uninstallView"),placement:"top"}},["loaded"==n.status?i("span",{staticClass:"menu-item"},[i("CommonSVG",{attrs:{"icon-class":"point",color:"#67C23A",size:16},nativeOn:{click:function(e){return t.handleViewsSubmenu(n,a,"dispose")}}})],1):t._e()]):t._e(),"disposed"==n.status?i("el-tooltip",{attrs:{enterable:!1,"open-delay":500,effect:"dark",content:t.$t("menuIconName.loadView"),placement:"top"}},["disposed"==n.status?i("span",{staticClass:"menu-item"},[i("CommonSVG",{attrs:{"icon-class":"point",color:"#F56C6C",size:16},nativeOn:{click:function(i){return t.handleViewsSubmenu(n,a,"load",e)}}})],1):t._e()]):t._e(),"loaded"==n.status?[e.visible&&n.visible?i("el-tooltip",{attrs:{enterable:!1,effect:"dark",content:t.$t("menuIconName.hideView"),placement:"top"}},[e.visible&&n.visible?i("span",{staticClass:"menu-item"},[i("CommonSVG",{attrs:{size:16,"icon-class":"tree_visible_feature"},nativeOn:{click:function(i){return t.handleViewsSubmenu(n,a,"hide",e)}}})],1):t._e()]):t._e(),n.visible&&e.visible?t._e():i("el-tooltip",{attrs:{enterable:!1,effect:"dark",content:t.$t("menuIconName.showView"),placement:"top"}},[n.visible&&e.visible?t._e():i("span",{staticClass:"menu-item"},[i("CommonSVG",{attrs:{size:16,color:"#475059","icon-class":"tree_hidden_feature"},nativeOn:{click:function(i){return t.handleViewsSubmenu(n,a,"show",e)}}})],1)])]:t._e()],2)])])})),0)])]:t._e()],2)})),t._l(n.structureTree,(function(e){return i("div",{key:e.id,staticClass:"structure-item"},[[i("div",{staticClass:"item",on:{click:function(i){return t.setStructureTree(e.id,n)}}},[i("div",{staticClass:"item-left"},[i("span",{staticStyle:{"padding-left":"54px"}},[i("CommonSVG",{attrs:{"icon-class":e.icon,color:"#fff",size:16}})],1),t._v(" "+t._s(e.name)+" ")]),i("div",{staticClass:"item-right"},[i("span",{staticClass:"tree-icon"},[i("el-tooltip",{attrs:{enterable:!1,"open-delay":500,effect:"dark",content:t.$t("dialog.systemTree.name"),placement:"top"}},[i("span",{staticClass:"menu-item"},[i("CommonSVG",{attrs:{"icon-class":"tree_structure_feature",size:16},nativeOn:{click:function(i){return i.stopPropagation(),t.setStructureTree(e.id,n)}}})],1)])],1),"system"===e.id?i("el-tooltip",{attrs:{enterable:!1,"open-delay":500,effect:"dark",content:t.$t("menuIconName.topo"),placement:"top"}},[i("span",{staticClass:"menu-item"},[i("CommonSVG",{attrs:{color:"#98A3B3","icon-class":"gplot_feature",size:16},nativeOn:{click:function(e){return e.stopPropagation(),t.opentopo(n)}}})],1)]):[e.visible?i("el-tooltip",{attrs:{enterable:!1,effect:"dark",content:t.$t("others.visible"),placement:"top"}},[i("span",{staticClass:"menu-item"},[i("CommonSVG",{attrs:{size:16,"icon-class":"tree_visible_feature"},nativeOn:{click:function(i){return i.stopPropagation(),t.handleStructureVisible(n,e,"hide")}}})],1)]):t._e(),e.visible?t._e():i("el-tooltip",{attrs:{enterable:!1,effect:"dark",content:t.$t("others.visible1"),placement:"top"}},[i("span",{staticClass:"menu-item"},[i("CommonSVG",{attrs:{size:16,color:"#475059","icon-class":"tree_hidden_feature"},nativeOn:{click:function(i){return i.stopPropagation(),t.handleStructureVisible(n,e,"show")}}})],1)])]],2)])]],2)}))],2)])]:t._e()],2)})),0)])],1)})),0)])])},v=[],w=i("ade3"),b=i("5530"),S=(i("b64b"),i("00b4"),i("4de4"),i("cb29"),i("c740"),i("7db0"),i("b995")),$=i("2909"),y=i("ed08"),D={data:function(){return{postDataCurrentElement:[],postDataIot:[]}},computed:{postDataWidgetsArr:function(){return this.$store.state.scene.sceneList||[]}},methods:{setVothingPostData:function(){var t=this;this.$watch((function(){return this.$store.state.websocket.allSocketObj}),(function(e){if(e){var i=window.scene.features;i.forEach((function(i){var a=i.dataKey?i.dataKey:i.type+"-"+i.id;for(var n in t.postDataWidgetsArr)"element"===t.postDataWidgetsArr[n].type&&t.postDataWidgetsArr[n].elementid===a&&function(){t.postDataCurrentElement=t.postDataWidgetsArr[n];var a=[];Object.keys(e).forEach((function(i){var n=t.postDataCurrentElement.data.widgets.entity;n&&n.map((function(t){i===t.id&&a.push(e[i])}))})),t.postDataIot={id:t.postDataCurrentElement.elementid,data:a},"annotation"==t.postDataCurrentElement.data.elementType?t.setAnnotation(i):"heatmap"==t.postDataCurrentElement.data.elementType?t.setHeatmap(i):t.setOtherElement(i)}()}))}}),{deep:!0})},setAnnotation:function(t){var e=this.postDataIot.data;if(e&&e[0]&&e[0].DataDetails){var i=[],a=this.postDataCurrentElement.data.widgets.entityProperty;a&&(Object.keys(a).forEach((function(t){var e;a[t]&&(e=i).push.apply(e,Object($["a"])(a[t]))})),i=Object(y["a"])(i,"name"));var n={title:"",dataList:[]};n.title=this.postDataCurrentElement.data.title,e.forEach((function(t){t&&t.DataDetails.forEach((function(e){i.forEach((function(i){if(i.name===e.PropertyName){var a={};a["key"]=e.PropertyName+" ("+t.DeviceDetail.DeviceName+")",a["value"]=e.Value,n.dataList.push(a)}}))}))}));var s=JSON.parse(JSON.stringify(t.data));s.content.dataList=n.dataList,window.scene.paused=!0,window.scene.postData(s,t.dataKey),window.scene.paused=!1}},setHeatmap:function(t){var e=this,i=this.postDataIot.data;if(i&&i[0]&&i[0].DataDetails){var a={},n=this.postDataCurrentElement.data.widgets.relationArr;i.forEach((function(i){i&&n.forEach((function(n){n.chosedEntity.id===i.DeviceDetail.DeviceId&&i.DataDetails.forEach((function(i){if(n.chosedProperty.name===i.PropertyName){var s=parseFloat(i.Value+""),o=isNaN(s);if(o)return;if(""!==n.javascriptCodes){var r=n.javascriptCodes,c={position:[parseFloat(n.relationData.relationKey.value.x+""),parseFloat(n.relationData.relationKey.value.y+"")],value:e.fn(r,s)};a=c}else{var l={position:[parseFloat(n.relationData.relationKey.value.x+""),parseFloat(n.relationData.relationKey.value.y+"")],value:s};a=l}var m=JSON.parse(JSON.stringify(t.data));m.points[n.relationData.relationKey.index]=a,window.scene.paused=!0,window.scene.postData(m,t.dataKey),window.scene.paused=!1}}))}))}))}},setOtherElement:function(t){var e=this,i=this.postDataIot.data;if(i&&i[0]&&i[0].DataDetails){var a={},n=this.postDataCurrentElement.data.widgets.relationArr;i.forEach((function(t){t&&n.forEach((function(i){i.chosedEntity.id===t.DeviceDetail.DeviceId&&t.DataDetails.forEach((function(t){if(i.chosedProperty.name===t.PropertyName){var n=parseFloat(t.Value+""),s=isNaN(n);if(s)return;if(""!==i.javascriptCodes){var o=i.javascriptCodes;a[i.relationData.type]=e.fn(o,n)}else{if("color"===i.relationData.type)return;a[i.relationData.type]=n}}}))}))}));var s=JSON.parse(JSON.stringify(t.data));s=a,window.scene.paused=!0,window.scene.postData(s,t.dataKey),window.scene.paused=!1}},fn:function(t,e){var i=t+" return value",a=new Function("data","value",i),n=null;return a(e,n)}}},C=i("2f62"),A={name:"elementList",props:["compKey","isShare","isVothingScenemanager"],mixins:[D],data:function(){return{searchKeyword:"",computedTreeDatas:{},needUpdateScene:0,setSceneManageTree:null,regForEmpty:/^\s*$/,setSceneFirstGetDataFromAjax:null,aboutSection:{parentIndex:-1,index:-1,lastID:"",sectionActivated:!1},unwantedLocationMenuType:["underlay","skybox","_3dBuilding","dem"]}},computed:Object(b["a"])(Object(b["a"])({},Object(C["b"])({selectedSectionData:function(t){return t.menuList.selectedSectionData},sectionLoaded:function(t){return t.menuList.sectionLoaded},isEdit:function(t){return t.scene.sceneEditMode},sectionSelectData:function(t){return t.menuList.sectionSelectData},currentSelectFeatureIDs:function(t){return t.scene.currentSelectFeatureIDs}})),{},{modelViewMenuStyle:function(){return function(t,e){var i="";return i=this.aboutSection.sectionActivated&&t==this.aboutSection.index&&this.aboutSection.parentIndex==e?"min-width:75px":"min-width:45px",i}},modelSectionMenuStyle:function(){return function(t,e){var i="";return this.aboutSection.sectionActivated&&-1==this.aboutSection.index&&this.aboutSection.parentIndex==t&&"model"==e&&(i="min-width:50px"),i}},allAnimations:function(){return this.$store.getters.allAnimations}}),watch:{sectionSelectData:function(t){0==t.length&&(this.aboutSection.lastID="",this.aboutSection.parentIndex=-1,this.aboutSection.sectionActivated=!1)},compKey:{deep:!0,handler:function(t,e){"pathAnimation"!==this.compKey.type&&this.setSceneManageTree(this.compKey),this.setComputedTreeDatas()}}},components:{CommonSVG:function(){return i.e("chunk-51f90655").then(i.bind(null,"17d0"))}},mounted:function(){var t=this,e=Object(S["a"])(window.scene),i=e.sceneManageTreeDatas,a=e.setSceneManageTree,n=e.setSceneFirstGetDataFromAjax;this.sceneManageTreeDatas=i,this.setSceneManageTree=a,this.setSceneFirstGetDataFromAjax=n;var s=this.$watch((function(){return this.$store.state.scene.sceneTreeDataAjax}),(function(e){if(s&&window.scene.features.size>0)s();else if("{}"!=e&&""!=e&&void 0!=e&&null!=e){try{window.scene.fromJSON(e),window.scene.load(),setTimeout((function(){t.setSceneFirstGetDataFromAjax(e),t.setComputedTreeDatas(),t.setVothingPostData()}),1e3)}catch(i){console.error(i),setInterval((function(){t.$message.error(t.$t("messageTips.fromJSONError"))}),5e3),window.parent.postMessage("fromJSONError","*")}s&&s()}}),{immediate:!0});this.setComputedTreeDatas(),this.$bus.on("synchronizeViewVisibleStatus",this.handleSynchronizeViewVisibleStatus)},methods:{toolTipContent:function(t){var e=t.id,i="";switch(e){case"Space":i=this.$t("dialog.spaceTree.name");break;case"Area":i=this.$t("dialog.areaTree.name");break;default:i=this.$t("dialog.viewTree.name");break}return i},setComputedTreeDatas:function(){var t=this,e={},i=this;if(this.sceneManageTreeDatas&&function(){for(var i=t.searchKeyword,a=t.sceneManageTreeDatas,n=t.regForEmpty,s=0,o=Object.keys(a);s<o.length;s++){var r,c=o[s],l=a[c];if(null!==l&&void 0!==l&&null!==(r=l.datas)&&void 0!==r&&r.length)if(i&&!n.test(i)){var m=l.datas.filter((function(t){return-1!==t.name.indexOf(i)}));null!==m&&void 0!==m&&m.length&&(e[c]=Object(b["a"])(Object(b["a"])({},l),{},{datas:m}))}else e[c]=a[c]}}(),e.model&&e.model.datas.forEach((function(e,a){var n,s=[],o=[],r=[];null===(n=e.views)||void 0===n||n.forEach((function(t){"3D"===t.type?s.push(t):"2D"===t.type&&("Grid"===t.category?o.push(t):r.push(t))}));var c=0;s.forEach((function(t){t.visible&&(c+=1)}));var l=0;r.forEach((function(t){t.visible&&(l+=1)}));var m=0;o.forEach((function(t){t.visible&&(m+=1)}));var d,u=[{label:t.$t("featureDatas.model.viewTypes[0]"),icon:"3D",visible:c,children:s,listState:!0,type:"3D"},{label:t.$t("featureDatas.model.viewTypes[1]"),icon:"Plan",visible:l,children:r,listState:!0,type:"2D"},{label:t.$t("featureDatas.model.viewTypes[2]"),icon:"Grid",visible:m,children:o,listState:!0,type:"Grid"}];if(e.id&&(d=window.scene.features.get(e.id)),d&&(i.$set(e,"formatViews",u),d.systems&&d.systems.length)){var p=[{id:"system",name:t.$t("featureDatas.model.viewTypes[3]"),icon:"tree_system_feature"}];i.$set(e,"structureTree",p)}})),e.dem){var a=window.scene.features.get("dem").sphere;a?this.unwantedLocationMenuType.includes("dem")&&this.unwantedLocationMenuType.pop():this.unwantedLocationMenuType.includes("dem")||this.unwantedLocationMenuType.push("dem")}this.computedTreeDatas=e},closeCurrentElementDialog:function(t){var e=this.$store.state.scene.dragOverData;""!=this.$store.state.widget.settingActive&&e.type==t.type&&e.id==t.id&&this.$store.commit("toggleSettingActive","closeSetting")},hideAllView:function(t,e){"show"===e?t.children.forEach((function(e){if(e.parentID){var i=window.scene.features.get(e.parentID).views.get(e.id);i.visible=!0,t.visible=!0,e.visible=!0}else{var a=window.scene.features.get(e.id);a.visible=!0,t.visible=!0,e.visible=!0}})):(t.children.forEach((function(e){if(e.parentID){var i=window.scene.features.get(e.parentID).views.get(e.id);i.visible=!1,t.visible=!1,e.visible=!1}else{var a=window.scene.features.get(e.id);a.visible=!1,t.visible=!1,e.visible=!1}})),window.scene.selectedObjects.size>0&&(this.$store.commit("toogleElementMenu",""),this.$store.commit("selectElementData",{})),window.scene.clearSelection()),window.scene.render()},handleViewsSubmenu:function(t,e,i,a){var n=this;if(this.$bus.emit("eventoff"),window.offCheckedCoordinate&&window.offCheckedCoordinate(),window.offCheckedCoordinate&&(window.offCheckedCoordinate=null,delete window.offCheckedCoordinate),"location"!==i&&this.sectionLoaded)this.$message.error(this.$t("messageTips.exitSection"));else{switch(i){case"show":if(a){if(a.visible)if(t.parentID){var s=window.scene.features.get(t.parentID).views.get(t.id);s.visible=!0,t.visible=!0}else{var o=window.scene.features.get(t.id);o.visible=!0,t.visible=!0}}else if(t.parentID){var r=window.scene.features.get(t.parentID).views.get(t.id);r.visible=!0,t.visible=!0}else{var c=window.scene.features.get(t.id);c.visible=!0,t.visible=!0}t.formatViews&&t.formatViews.forEach((function(t){n.hideAllView(t,"show")})),"underlay"==t.type&&this.$store.commit("setSceneUnderlayStatus",{underlayState:!0});break;case"hide":if(t.parentID){var l=window.scene.features.get(t.parentID).views.get(t.id);l.visible=!1,t.visible=!1}else{var m=window.scene.features.get(t.id);m.visible=!1,t.visible=!1}t.formatViews&&t.formatViews.forEach((function(t){n.hideAllView(t,"hide")})),"underlay"==t.type&&this.$store.commit("setSceneUnderlayStatus",{underlayState:!1}),window.scene.selectedObjects.size>0&&(this.$store.commit("toogleElementMenu",""),this.$store.commit("selectElementData",{})),window.scene.clearSelection();break;case"location":var d=window.scene.features.get(t.id);window.scene.fit2Feature(d);break;case"load":var u=window.scene.features.get(t.parentID),p=window.scene.features.get(t.parentID).views.get(t.id);p.visible=!0,u.activeView(t.id),this.$nextTick((function(){n.$set(t,"status","loaded"),t.visible=!0,a.visible||(a.visible=!0)})),u._2DMode&&a.children.forEach((function(e){if(e.id!=t.id){var i=u.views.get(e.id),a=i.loaded?"loaded":"disposed";n.$set(e,"status",a)}}));break;case"dispose":var h=window.scene.features.get(t.parentID).views.get(t.id);h.dispose(),this.$nextTick((function(){n.$set(t,"status","disposed"),t.visible=!1;var e=n.$store.state.dialog.activeDialog;-1!==e.indexOf("interactive")&&n.$store.commit("toggleActiveDialog","interactive"),-1!==e.indexOf("viewTree")&&n.$store.commit("toggleActiveDialog","viewTree"),n.aboutSection.lastID==t.id&&(n.aboutSection.sectionActivated&&window.scene.mv.tools.clip.dispose(),n.aboutSection.lastID="",n.aboutSection.parentIndex=-1,n.aboutSection.sectionActivated=!1)}));break;case"tree":this.$emit("setCurrentModelData",[t,Date.parse(new Date)]);var f=this.$store.state.dialog.activeDialog;-1==f.indexOf("modelTree")&&this.$store.commit("toggleActiveDialog","modelTree");break;case"remove":this.$confirm(this.$t("messageTips.removeFeature",{name:t.name}),this.$t("messageTips.tipsTitle"),{confirmButtonText:this.$t("messageTips.confirm"),cancelButtonText:this.$t("messageTips.cancel"),type:"warning"}).then((function(){n.removeAllRelatedAnimations(t);var i=window.scene.features.get(t.id),a=null;a="annotation"==t.type&&t.data.assetLabel?n.computedTreeDatas["assetLabel"].datas:n.computedTreeDatas[t.type].datas,a.splice(e,1);try{var s=window.scene.getSelection();s.length&&s.forEach((function(t){t.selected=!1})),i.dispose()}catch(c){console.warn(c)}n.$store.commit("toogleElementMenu",""),n.$store.commit("selectElementData",{}),setTimeout((function(){n.$nextTick((function(){window.scene.render()}))}),200);var o=n.$store.state.scene.sceneList||[];for(var r in o)"element"===o[r].type&&o[r].elementid===t.dataKey&&n.$store.commit("deleteElementID",t.dataKey);n.closeCurrentElementDialog(t),"underlay"==t.type&&n.$store.commit("setSceneUnderlayStatus",{underlayState:!1}),n.$store.commit("listenAnnotation")})).catch((function(t){console.warn(t)}));break;case"filter":if(""!=this.$store.state.widget.settingActive)return void this.$message(this.$t("messageTips.exitEditingStatus"));this.$store.commit("setCurrentFilterId",t.id),this.$store.commit("toggleActiveDialog","filterCondition");break;case"setting":var g=window.scene.features.get(t.id);this.$store.commit("toggleActiveDialog","empty"),this.$emit("resetCurrentElementSelected"),this.$store.commit("toogleElementMenu","");var v=this.$store.state.scene.dragOverData;if(""!=this.$store.state.widget.settingActive&&t.id!=v.id)return void this.$message(this.$t("messageTips.exitEdit"));"polygon"==t.type&&(g.data.tileExcavation?(t.title=this.$t("featureDatas.polygon.extend.tileExcavation.name"),t.subType="tileExcavation"):g.data.extrude?(t.title=this.$t("featureDatas.polygon.extend.extrude.name"),t.subType="extrude"):g.data.terrainFlatten?(t.title=this.$t("featureDatas.polygon.extend.flatten.name"),t.subType="flatten"):g.data.terrainExcavation?(t.title=this.$t("featureDatas.polygon.extend.excavation.name"),t.subType="excavation"):g.data.tileFlatten?(t.title=this.$t("featureDatas.polygon.extend.tileFlatten.name"),t.subType="tileFlatten"):"demOpacity"===g.data.subType?(t.title=this.$t("featureDatas.polygon.extend.demOpacity.name"),t.subType="demOpacity"):g.data.water?(t.title=this.$t("featureDatas.polygon.extend.water.name"),t.subType="water"):g.data.fill&&g.data.environment?(t.title=this.$t("featureDatas.polygon.extend.environment.name"),t.subType="environment"):(t.title=this.$t("featureDatas.polygon.extend.surface.name"),t.subType="surface"));var b=JSON.parse(JSON.stringify(t)),S=JSON.parse(JSON.parse(JSON.stringify(g)));switch(Object.assign(b,S),void 0!=g.data&&(b.data=g.data),t.type){case"gltf":case"fbx":b.scale=g.scale;break;case"model":t.currentVersion=g.currentVersion;var $=JSON.parse(JSON.parse(JSON.stringify(g)));Object.assign(t,$),g.correction&&(b.correction=JSON.parse(JSON.stringify(g.correction)));break}this.$store.commit("saveFeatureRawData",JSON.stringify(b)),this.$store.commit("saveDragOverData",t),this.$nextTick((function(){"underlay"==t.type||"skybox"==t.type?(n.$store.commit("toggleActiveDialog","Source"),n.$store.commit("toggleSettingActive","closeSetting")):n.$store.commit("toggleSettingActive",t.type)}));break;case"section":var y=null;if(this.aboutSection.index=-1,this.aboutSection.sectionActivated&&(window.scene.mv.tools.clip.dispose(),this.$store.commit("toogleSectionLoaded",!1),"sectionPlaneing"!==this.bottomMenuActive&&"sectioning"!==this.bottomMenuActive||this.$store.commit("toggleBottomMenuActive",""),this.$store.commit("setSectionValue","")),this.aboutSection.lastID==t.id){this.aboutSection.lastID="",this.aboutSection.parentIndex=-1,this.aboutSection.sectionActivated=!1;break}window.scene.mv._THREE;if(t.parentID){this.aboutSection.index=e;var D=window.scene.features.get(t.parentID);if(y=D.views.get(t.id),"3D"==t.category){"sectioning"!==this.bottomMenuActive&&(this.$store.commit("toggleBottomMenuActive","sectioning"),this.$store.commit("setSectionValue","box")),this.$store.commit("setSelectedSectionData",[]);var C=[];this.selectedSectionData.includes(t.parentID)&&(C=this.selectedSectionData),C.push(t.parentID),this.$store.commit("setSelectedSectionData",C)}else{var A=[];this.selectedSectionData[t.parentID]&&(A=this.selectedSectionData[t.parentID]),A.push(t.id);var k=Object.assign(this.selectedSectionData,Object(w["a"])({},t.parentID,A));this.$store.commit("setSelectedSectionData",k),this.$store.commit("setPlaneSelectionId",y.id),this.$store.commit("updateSectionRandom",(new Date).getTime()),"sectionPlaneing"!==this.bottomMenuActive&&(this.$store.commit("toggleBottomMenuActive","sectionPlaneing"),this.$store.commit("setSectionValue","plane"))}}else{this.aboutSection.parentIndex=e,"sectioning"!==this.bottomMenuActive&&(this.$store.commit("toggleBottomMenuActive","sectioning"),this.$store.commit("setSectionValue","box")),this.$store.commit("setSelectedSectionData",[]);var T=[];this.selectedSectionData.includes(t.id)&&(T=this.selectedSectionData),T.push(t.id),this.$store.commit("setSelectedSectionData",T)}this.$store.commit("toogleSectionLoaded",!0),this.aboutSection.sectionActivated=!0,this.aboutSection.lastID=t.id;break;case"viewTree":var _=t;_.formerDataIndex=e,this.$emit("setCurrentViewData",[_,Date.parse(new Date)]);var x=this.$store.state.dialog.activeDialog;-1==x.indexOf("viewTree")&&this.$store.commit("toggleActiveDialog","viewTree");break;case"interactive":var E=window.scene.features.get(t.parentID),O=Object.assign(t,{offset:E.offset,origin:E.origin,rotation:E.rotation,altitude:E.altitude});this.$emit("setInteractiveData",[O,Date.parse(new Date)]);var V=this.$store.state.dialog.activeDialog;-1==V.indexOf("interactive")&&this.$store.commit("toggleActiveDialog","interactive");break}window.scene.render(),this.needUpdateScene=Date.parse(new Date)}},setStructureTree:function(t,e){this.$emit("setStructureTree",{type:t,modelId:e.id})},opentopo:function(t){var e=this;this.$store.commit("toggleTopologyActive","closeTopology");var i=window.scene.features.get(t.id);try{i.load("system").then((function(){e.$store.commit("toggleTopologyActive","openTopology")}))}catch(a){i.loadSystem().then((function(){e.$store.commit("toggleTopologyActive","openTopology")}))}this.$nextTick((function(){window.scene.snapViewpoint().then((function(t){var i=JSON.parse(JSON.stringify(t));e.$store.commit("setViewpoint",i)})),e.$store.commit("saveTopoCurrentModel",t)}))},handleStructureVisible:function(t,e,i){var a=window.scene.features.get(t.id);"hide"===i?(e.visible=!1,a[e.id]=!1):(e.visible=!0,a[e.id]=!0),window.scene.render()},toggleTreeNodel:function(t){t.listState=!t.listState},handleAllFeatureMenu:function(t,e){switch(t){case"hide":e.visible=!1,e.datas.length&&e.datas.forEach((function(t){return t.visible=!1}));break;case"show":e.visible=!0,e.datas.length&&e.datas.forEach((function(t){return t.visible=!0}));break;case"remove":this.removeBatchFeature(e)}},removeBatchFeature:function(t){var e=this;this.$confirm(this.$t("messageTips.removeFeature",{name:t.name}),this.$t("messageTips.tipsTitle"),{confirmButtonText:this.$t("messageTips.confirm"),cancelButtonText:this.$t("messageTips.cancel"),type:"warning"}).then((function(){t.datas.forEach((function(t){e.removeAllRelatedAnimations(t);var i=window.scene.features.get(t.id);try{var a=window.scene.getSelection();a.length&&a.forEach((function(t){t.selected=!1})),i.dispose()}catch(o){console.warn(o)}var n=e.$store.state.scene.sceneList||[];for(var s in n)"element"===n[s].type&&n[s].elementid===t.dataKey&&e.$store.commit("deleteElementID",t.dataKey)})),e.computedTreeDatas[t.label].datas=[],e.$store.commit("toogleElementMenu",""),e.$store.commit("selectElementData",{}),setTimeout((function(){e.$nextTick((function(){window.scene.render()}))}),200);var i=e.$store.state.widget.settingActive;""!=i&&i==t.label&&e.$store.commit("toggleSettingActive","closeSetting"),e.$store.commit("listenAnnotation")})).catch((function(t){console.warn(t)}))},removeAllRelatedAnimations:function(t){var e=this.allAnimations.filter((function(e){return e.modelId===t.id}));null!==e&&void 0!==e&&e.length&&this.$store.commit("removeAnimationBoth",e.map((function(t){return t.animationName})))},stopAllRelatedAnimations:function(t){var e=this.allAnimations.filter((function(e){return e.modelId===t.id}));null!==e&&void 0!==e&&e.length&&this.$store.commit("stopAnimationBoth",e.map((function(t){return t.animationName})))},handleSynchronizeViewVisibleStatus:function(t){var e=this.computedTreeDatas.model.datas.findIndex((function(e){return e.id==t[0].parentID})),i=this.computedTreeDatas.model.datas[e].formatViews[t[0].formerDataIndex].children.find((function(e){return e.id==t[0].id}));i.visible=t[1]}}},k=A,T=(i("1a0b"),Object(p["a"])(k,g,v,!1,null,"6e482b94",null)),_=T.exports,x=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"top-search"},[i("el-input",{attrs:{size:"small",clearable:"",placeholder:t.$t("others.search")},model:{value:t.searchKeyword,callback:function(e){t.searchKeyword=e},expression:"searchKeyword"}},[i("i",{staticClass:"el-input__icon el-icon-search",attrs:{slot:"prefix"},slot:"prefix"})])],1),i("div",{staticClass:"bottom-tree-wrapper"},[i("div",{staticClass:"setup-content"},t._l(t.viewpointDatasFiltered,(function(e,a){return i("div",{key:e.id+a,staticClass:"viewpoints-list",on:{click:function(i){return t.restoreData(e)}}},[e.default?i("div",{staticClass:"list-mark"},[t._v(" "+t._s(t.$t("others.default"))+" ")]):t._e(),i("img",{attrs:{src:e.thumbnail,alt:""}}),i("div",{staticClass:"list-bottom"},[i("span",{staticClass:"left-title"},[t._v(" "+t._s(e.name||t.$t("topToolBarMenu.advanced.children.viewpoint.name")+a)+" ")]),t.isEdit?i("div",{staticClass:"right-menu"},[i("el-tooltip",{attrs:{effect:"dark",content:t.$t("menuIconName.rename"),placement:"top"}},[i("span",{on:{click:function(i){return t.handleListMenu(e,a,"bianji")}}},[i("CommonSVG",{attrs:{color:"#98A2B3","icon-class":"amend_feature",size:16}})],1)]),i("el-tooltip",{attrs:{effect:"dark",content:t.$t("menuIconName.delete"),placement:"top"}},[i("span",{on:{click:function(i){return i.stopPropagation(),t.handleListMenu(e,a,"delete")}}},[i("CommonSVG",{attrs:{color:"#98A2B3","icon-class":"delete",size:16}})],1)])],1):t._e()])])})),0)]),t.addFormDialog.dialogState?i("addViewpointMarkupDialog",{attrs:{addType:"viewpoint",isEditName:t.isEditName,inputValue:t.addFormDialog.name},on:{closeAddDialog:t.closeAddDialog,saveData:t.saveData}}):t._e()],1)},E=[],O=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("dialogComp",{attrs:{hideHeader:!1,needClose:"true",zIndex:"100",position:"fixed",draw:!1,top:0,right:0,bottom:0,left:0,drag:!1,title:t.isEditName?t.$t("menuIconName.edit1"):t.$t("menuIconName.add")+t.contentForaddType,width:300,height:150,type:"detailInfo"},on:{close:t.closeAddDialog},scopedSlots:t._u([{key:"center",fn:function(){return[i("div",{staticClass:"add-container"},[i("el-input",{ref:"inputForAddDialog",class:{"is-error":t.addFormDialog.inputError},attrs:{size:"small",placeholder:t.$t("others.inputName2",{name:t.contentForaddType})},scopedSlots:t._u([{key:"prepend",fn:function(){return[i("span",[t._v(t._s(t.contentForaddType+t.$t("others.name")))])]},proxy:!0}]),model:{value:t.addFormDialog.name,callback:function(e){t.$set(t.addFormDialog,"name","string"===typeof e?e.trim():e)},expression:"addFormDialog.name"}}),i("div",{staticClass:"bottom-btn"},[i("span",{staticClass:"cursor-btn cancel margin-right-8",on:{click:t.closeAddDialog}},[t._v(" "+t._s(t.$t("messageTips.cancel"))+" ")]),i("span",{staticClass:"cursor-btn confirm",on:{click:t.saveData}},[t._v(" "+t._s(t.$t("menuIconName.confirm"))+" ")])])],1)]},proxy:!0}])})},V=[],I=(i("498a"),{name:"",props:["addType","isEditName","inputValue"],data:function(){return{regForEmpty:/^\s*$/,addFormDialog:{menuState:!1,dialogState:!1,name:"",inputError:!1,curIndex:""}}},computed:{contentForaddType:function(){switch(this.addType){case"markup":return this.$t("topToolBarMenu.advanced.children.markup.name");case"viewpoint":return this.$t("topToolBarMenu.advanced.children.viewpoint.name");case"animation":return this.$t("topToolBarMenu.advanced.children.animation.name");default:return""}}},mounted:function(){var t=this;this.inputValue&&(this.addFormDialog.name=this.inputValue),this.$nextTick((function(){var e;null===(e=t.$refs.inputForAddDialog)||void 0===e||e.focus()}))},methods:{beforeValidate:function(){var t=this;if(!this.addFormDialog.name||this.regForEmpty.test(this.addFormDialog.name))return this.$message.error(this.$t("messageTips.nameNotEmpty")),this.addFormDialog.inputError=!0,!1;this.addFormDialog.name=this.addFormDialog.name.trim();var e=this.addType;if("markup"===e||"viewpoint"===e){if(0==window.scene.viewpoints.length)return!0;var i=window.scene.viewpoints.findIndex((function(e){return e.name==t.addFormDialog.name}));return!(i>=0)||(this.$message.error(this.addFormDialog.name+" "+this.$t("messageTips.nameAlreadyExists")),!1)}switch(e){case"animation":case"trigger":case"pathAnimation":case"screening":default:break}return!0},saveData:function(){var t=this.beforeValidate();if(!t)return!1;this.$emit("saveData",{name:this.addFormDialog.name})},closeAddDialog:function(){this.$emit("closeAddDialog")}}}),F=I,N=Object(p["a"])(F,O,V,!1,null,"1268def3",null),M=N.exports,P={name:"",data:function(){return{searchKeyword:"",regForEmpty:/^\s*$/,isEditName:!1,addFormDialog:{menuState:!1,dialogState:!1,name:"",inputError:!1,curIndex:""}}},computed:{viewpointDatas:function(){return this.$store.state.viewpoint.viewpointDatas},isEdit:function(){return this.$store.state.scene.sceneEditMode},viewpointDatasFiltered:function(){var t=this.searchKeyword,e=this.regForEmpty,i=this.viewpointDatas;return!t||e.test(t)?i:i.filter((function(e){return e.name.includes(t)}))}},components:{CommonSVG:function(){return i.e("chunk-51f90655").then(i.bind(null,"17d0"))},addViewpointMarkupDialog:M},mounted:function(){this.getSceneViewpoints()},methods:{restoreData:function(t){window.scene.mv.tools.markup.clear(),window.scene.restoreViewpoint(t),window.scene.render()},getSceneViewpoints:function(){var t=[];window.scene.viewpoints.length>0&&(window.scene.viewpoints.forEach((function(e){var i=e.type;"viewpoint"===i&&t.unshift(e)})),this.$store.commit("getSceneViewpointList",t))},saveData:function(t){var e=this;return Object(o["a"])(Object(s["a"])().mark((function i(){var a,n,o;return Object(s["a"])().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(a=e.$loading({lock:!0,text:e.$t("others.prepare"),spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),n=null,!e.isEditName){i.next=12;break}return o=e.addFormDialog.curIndex,e.viewpointDatas[o].name=t.name,a.close(),e.addFormDialog.dialogState=!1,e.addFormDialog.curIndex="",e.addFormDialog.name="",e.isEditName=!1,e.getSceneViewpoints(),i.abrupt("return");case 12:return i.next=14,window.scene.snapViewpoint().catch((function(t){a.close(),e.addFormDialog.dialogState=!1}));case 14:n=i.sent,n.name=t.name,n.save(window.scene),e.$notify({title:e.$t("topToolBarMenu.advanced.children.viewpoint.name")+" "+n.name,message:e.$t("others.added"),type:"success"}),a.close(),e.$store.commit("setActivedType",""),e.getSceneViewpoints(),e.closeAddDialog();case 22:case"end":return i.stop()}}),i)})))()},closeAddDialog:function(){this.addFormDialog.dialogState=!1,this.addFormDialog.name="",this.addFormDialog.curIndex="",this.isEditName=!1,this.$store.commit("setActivedType","")},handleListMenu:function(t,e,i){var a=this;switch(i){case"default":var n=this.viewpointDatas.findIndex((function(t){return!0===t.default}));n>=0&&(this.viewpointDatas[n].default=!1),e!==n&&(t.default=!t.default);break;case"delete":this.$confirm(this.$t("messageTips.deleteSomething",{name:t.name}),this.$t("messageTips.tipsTitle"),{confirmButtonText:this.$t("menuIconName.confirm"),cancelButtonText:this.$t("messageTips.cancel"),type:"warning"}).then((function(){var i=a.viewpointDatas;i.splice(e,1),a.$store.commit("getSceneViewpointList",i);var n=window.scene.viewpoints.findIndex((function(e){return e.id==t.id}));window.scene.viewpoints.splice(n,1),a.$message({type:"success",message:a.$t("messageTips.deleteSuccess")})})).catch((function(){}));break;case"bianji":this.addFormDialog.dialogState=!0,this.addFormDialog.name=t.name,this.addFormDialog.curIndex=e,this.isEditName=!0}}}},L=P,G=Object(p["a"])(L,x,E,!1,null,"5aa4c078",null),j=G.exports,z=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.pathAnimationList.length?i("div",{staticClass:"bottom-tree-wrapper cus"},[i("div",{staticClass:"bottom-tree"},[i("div",{staticClass:"tree-list"},[i("el-collapse-transition",[i("div",{staticClass:"list-content"},t._l(t.pathAnimationList,(function(e,a){return i("div",{key:e.id,staticClass:"content-item",on:{click:function(i){return i.stopPropagation(),t.setPathAnimation(e)}}},[i("div",{staticClass:"item",class:{isActivated:t.curPathAnimationId===e.id}},[i("div",{staticClass:"item-left"},[i("CommonSVG",{attrs:{"icon-class":"layer",color:"#fff",size:18}}),i("el-tooltip",{attrs:{enterable:!1,"open-delay":500,effect:"dark",content:e.name,placement:"top"}},[i("span",[t._v(" "+t._s(e.name)+" "),e.name===t.$t("topToolBarMenu.advanced.children.pathAnimation.name")?[t._v(" -"+t._s(a+1)+" ")]:t._e()],2)])],1),i("div",{staticClass:"item-right"},[t.isEdit?i("el-tooltip",{attrs:{enterable:!1,"open-delay":100,effect:"dark",content:t.$t("menuIconName.delete"),placement:"top"}},[i("span",{staticClass:"menu-item",staticStyle:{"margin-top":"-2px"}},[i("CommonSVG",{attrs:{size:16,"icon-class":"delete"},nativeOn:{click:function(i){return i.stopPropagation(),t.handlePathAnimation(e,"remove")}}})],1)]):t._e(),t.isEdit?i("el-tooltip",{attrs:{enterable:!1,"open-delay":100,effect:"dark",content:t.$t("menuIconName.setUp"),placement:"top"}},[i("span",{staticClass:"menu-item"},[i("CommonSVG",{attrs:{size:20,"icon-class":"set_up_feature"},nativeOn:{click:function(i){return i.stopPropagation(),t.handlePathAnimation(e,"setting")}}})],1)]):t._e(),i("el-tooltip",{attrs:{enterable:!1,"open-delay":100,effect:"dark",content:e.isRunning?t.$t("formRelational.animate.pause"):t.$t("formRelational.animate.play"),placement:"top"}},[i("span",{staticClass:"menu-item",staticStyle:{"margin-top":"-2px"}},[i("CommonSVG",{attrs:{size:16,color:e.isRunning?"var(--theme)":"#98A2B3","icon-class":e.isRunning?"suspend_feature":"bofang"},nativeOn:{click:function(i){return i.stopPropagation(),t.handlePathAnimation(e,"play")}}})],1)]),i("el-tooltip",{attrs:{enterable:!1,"open-delay":100,effect:"dark",content:t.$t("menuIconName.exit"),placement:"top"}},[i("span",{staticClass:"menu-item",staticStyle:{"margin-top":"-2px"}},[i("CommonSVG",{attrs:{size:16,"icon-class":"quit"},nativeOn:{click:function(i){return i.stopPropagation(),t.handlePathAnimation(e,"stop")}}})],1)])],1)])])})),0)])],1)])]):t._e()])},B=[],R={name:"",data:function(){return{curPathAnimationId:"",curPathAnimationObj:{},isPause:!1,pathAnimationStateEvents:!1,restartPathAnimation:!1}},computed:{isEdit:function(){return this.$store.state.scene.sceneEditMode},pathAnimationList:function(){return this.$store.state.pathAnimation.pathAnimationList},_curPathanimationId:function(){return this.$store.state.pathAnimation.curPathanimationId}},components:{CommonSVG:function(){return i.e("chunk-51f90655").then(i.bind(null,"17d0"))}},mounted:function(){this.init()},methods:{init:function(){var t=window.scene.pathRoamings,e=[];t.forEach((function(t){t.isRunning=!1,e.push(t)})),this.$store.commit("setPathAnimationList",e)},setPathAnimation:function(t){if(""==this.$store.state.widget.settingActive){var e=this.$store.state.pathAnimation.roamActive;if(e)this.$message(this.$t("messageTips.exitRoam"));else{var i=window.scene.findPathRoaming(t.id);this.curPathAnimationObj=t,this.curPathAnimationId=t.id,this.pathAnimationStateEvents&&window.scene.mv.events.pathroamStateChange.off("change",this.pathAnimationState);var a=this.$store.state.dialog.activeDialog;-1!==a.indexOf("pathAnimation")&&this.$store.commit("toggleActiveDialog","pathAnimation");var n=this.$store.state.pathAnimation.editPathAnimation;n&&(window.scene.mv.tools.transform.deactive(),this.$store.commit("toogleEditPathAnimation",!1)),this.restartPathAnimation=!1,this.$store.commit("setCurPathanimationId",t.id),window.scene.mv.tools.draw.deactive(),window.scene.mv.tools.pathRoam.init(i),this.$store.commit("tooglePathanimationActive",!0)}}else this.$message(this.$t("messageTips.exitEditingStatus"))},handlePathAnimation:function(t,e){var i=this,a=this.$store.state.pathAnimation.roamActive;if(a)this.$message(this.$t("messageTips.exitRoam"));else switch(window.scene.mv.events.pathroamStateChange.on("change",this.pathAnimationState),this.pathAnimationStateEvents=!0,e){case"remove":if(""!=this.$store.state.widget.settingActive)return void this.$message(this.$t("messageTips.exitEditingStatus"));this.$confirm(this.$t("dialog.pathAnimation.message",{name:t.name}),this.$t("messageTips.tipsTitle"),{confirmButtonText:this.$t("menuIconName.confirm"),cancelButtonText:this.$t("messageTips.cancel"),type:"warning"}).then((function(){window.scene.removePathRoaming(t.id);var e=window.scene.pathRoamings,a=i.$store.state.pathAnimation.pathanimationActive;a&&(window.scene.mv.tools.pathRoam.stop(),window.scene.mv.tools.pathRoam.clear(),i.$store.commit("tooglePathanimationActive",!1)),i.curPathAnimationObj={},t.isRunning=!1,i.isPause=!1,i.pathAnimationStateEvents=!1,window.scene.mv.events.pathroamStateChange.off("change",i.pathAnimationState);var n=[];e.forEach((function(t){n.push(t)})),i.$store.commit("setPathAnimationList",n),i.$store.commit("setCurPathanimationId","")})).catch((function(){}));break;case"setting":if(this.$store.commit("toogleElementMenu",""),this.$store.commit("selectElementData",{}),""!=this.$store.state.widget.settingActive)return void this.$message(this.$t("messageTips.exitEditingStatus"));var n=this.$store.state.pathAnimation.pathanimationActive;(!this.curPathAnimationObj.id||this.curPathAnimationObj.id&&this.curPathAnimationObj.id===t.id)&&(this.$store.commit("setCurPathanimationId",""),n&&(window.scene.mv.tools.pathRoam.stop(),window.scene.mv.tools.pathRoam.clear()),this.curPathAnimationObj={},t.isRunning=!1,this.isPause=!1,this.pathAnimationStateEvents=!1,window.scene.mv.events.pathroamStateChange.off("change",this.pathAnimationState)),this.$store.commit("saveFeatureRawData",JSON.stringify(t)),t.type="pathAnimation",window.scene.mv.tools.draw.active(),window.scene.mv.tools.draw.startDrawLine(t.positions),this.$store.commit("saveDragOverData",t),this.$store.commit("toggleSettingActive","pathAnimation"),this.$store.commit("setCurPathanimationId",t.id),this.curPathAnimationObj={},this.isPause=!1,this.restartPathAnimation=!1;break;case"play":if(""!=this.$store.state.widget.settingActive)return void this.$message(this.$t("messageTips.exitEditingStatus"));this.curPathAnimationObj.id||(this.curPathAnimationObj=JSON.parse(JSON.stringify(t)),this.setPathAnimation(t)),!this.curPathAnimationObj.id||this.curPathAnimationObj.id&&this.curPathAnimationObj.id===t.id?t.isRunning?(window.scene.mv.tools.pathRoam.pause(),t.isRunning=!1,this.isPause=!0,this.restartPathAnimation=!1):(this.restartPathAnimation?window.scene.mv.tools.pathRoam.restart():this.isPause?window.scene.mv.tools.pathRoam.resume():window.scene.mv.tools.pathRoam.start(),t.isRunning=!0):this.$message(this.$t("messageTips.exitPathRoam"));break;case"stop":var s=this.$store.state.pathAnimation.pathanimationActive;!this.curPathAnimationObj.id||this.curPathAnimationObj.id&&this.curPathAnimationObj.id===t.id?(this.$store.commit("setCurPathanimationId",""),s&&(window.scene.mv.tools.pathRoam.stop(),window.scene.mv.tools.pathRoam.clear()),this.curPathAnimationObj={},this.curPathAnimationId="",t.isRunning=!1,this.isPause=!1,this.pathAnimationStateEvents=!1,window.scene.mv.events.pathroamStateChange.off("change",this.pathAnimationState),this.$store.commit("tooglePathanimationActive",!1)):this.$message("请先退出当前路径动画!");break;default:break}},pathAnimationState:function(t){1===t&&(this.restartPathAnimation=!0,this.curPathAnimationObj.isRunning=!1,this.pathAnimationStateEvents=!1,window.scene.mv.events.pathroamStateChange.off("change",this.pathAnimationState))}}},J=R,K=Object(p["a"])(J,z,B,!1,null,"281ab39c",null),H=K.exports,U=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.animationGroupsLen?i("div",{staticClass:"bottom-tree-wrapper cus"},[i("div",{staticClass:"bottom-tree"},t._l(t.animationCategoties,(function(e){return i("div",{key:e.key,staticClass:"tree-list"},[e.groups.length?i("div",{staticClass:"list-title item-hover",on:{click:function(i){t.listStates[e.key]=!t.listStates[e.key]}}},[i("el-tooltip",{attrs:{enterable:!1,content:t.listStates[e.key]?t.$t("others.collapse"):t.$t("others.expand"),effect:"dark",placement:"top"}},[t.listStates[e.key]?i("i",{staticClass:"el-icon-caret-top mg-r4"}):i("i",{staticClass:"el-icon-caret-bottom mg-r4"})]),i("div",[i("span",{staticStyle:{"margin-right":"4px"}},[i("CommonSVG",{attrs:{"icon-class":e.icon,size:18}})],1),i("span",{staticStyle:{"padding-left":"4px"}},[t._v(t._s(e.name))])]),e.withAllControl?i("div",{staticClass:"toggleAllAnimation"},[i("el-tooltip",{attrs:{enterable:!1,"open-delay":200,effect:"dark",content:t.animationGroupsAnimating.length?t.$t("animate.tooltip14")+e.name:t.$t("animate.tooltip15")+e.name,placement:"right"}},[i("span",{staticClass:"menu-item"},[i("CommonSVG",{attrs:{size:18,"icon-class":t.animationGroupsAnimating.length?"ani_stop_feature":"ani_play_feature"},nativeOn:{click:function(i){return i.stopPropagation(),t.toggleAllAnimationGroups(e)}}})],1)])],1):t._e()],1):t._e(),i("el-collapse-transition",[i("div",{directives:[{name:"show",rawName:"v-show",value:e.groups&&t.listStates[e.key],expression:"aniCategory.groups && listStates[aniCategory.key]"}],staticClass:"list-content"},t._l(e.groups,(function(e){return i("div",{key:e.groupName,staticClass:"content-item"},[i("div",{staticClass:"item",class:{isActivated:e.groupName===t.$store.state.animation.animationGroupNameInEdit},staticStyle:{"padding-left":"40px"}},[i("div",{staticClass:"item-left"},[i("CommonSVG",{attrs:{"icon-class":"layer",color:"#fff",size:18}}),i("el-tooltip",{attrs:{enterable:!1,"open-delay":500,effect:"dark",content:e.groupName,placement:"top"}},[i("span",[t._v(t._s(e.groupName))])])],1),i("div",{staticClass:"item-right"},[t.isEdit?i("el-tooltip",{attrs:{enterable:!1,"open-delay":500,effect:"dark",content:t.$t("menuIconName.remove"),placement:"top"}},[i("span",{staticClass:"menu-item",staticStyle:{"margin-top":"-2px"}},[i("CommonSVG",{attrs:{size:16,"icon-class":"delete"},nativeOn:{click:function(i){return i.stopPropagation(),t.handleAnimationClick(e,"remove")}}})],1)]):t._e(),t.isEdit?i("el-tooltip",{attrs:{enterable:!1,"open-delay":500,effect:"dark",content:t.$t("menuIconName.setUp"),placement:"top"}},[i("span",{staticClass:"menu-item"},[i("CommonSVG",{attrs:{size:17,"icon-class":"set_up_feature"},nativeOn:{click:function(i){return i.stopPropagation(),t.handleAnimationClick(e,"setting")}}})],1)]):t._e(),i("el-tooltip",{attrs:{enterable:!1,"open-delay":500,effect:"dark",content:e.isSomeAnimating?t.$t("formRelational.animate.stop"):t.$t("formRelational.animate.play"),placement:"top"}},[i("span",{staticClass:"menu-item",staticStyle:{"margin-top":"-2px"}},[i("CommonSVG",{attrs:{size:16,"icon-class":e.isSomeAnimating?"ani_stop_feature":"ani_play_feature"},nativeOn:{click:function(i){return i.stopPropagation(),t.handleAnimationClick(e,"togglePlay")}}})],1)])],1)])])})),0)])],1)})),0)]):t._e()])},W=[],q={name:"animation",data:function(){return{isAnimationTimelineSettled:!1,listStates:{element:!0,camera:!0}}},computed:{animationGroupsAnimating:function(){return this.$store.state.animation.animationGroups.filter((function(t){return!0===t.isElementAnimationGroup&&t.animationList.some((function(t){return!0===t.isAnimating}))}))},isEdit:function(){return this.$store.state.scene.sceneEditMode},animationGroupsLen:function(){return this.$store.state.animation.animationGroups.length},animationCategoties:function(){return[{key:"element",name:this.$t("animate.label16"),icon:"animation_duixiang",groups:this.$store.getters.elementAnimationGroups,withAllControl:!0},{key:"camera",name:this.$t("animate.label17"),icon:"camera_animation_advanced",groups:this.$store.getters.cameraAnimationGroups,withAllControl:!1}]}},components:{CommonSVG:function(){return i.e("chunk-51f90655").then(i.bind(null,"17d0"))}},created:function(){this.$bus.on("AnimationTimeLineSettled",this.onAnimationTimeLineSettled),this.$bus.on("AnimationTimeLineDestroyed",this.onAnimationTimeLineDestroyed)},mounted:function(){this.restoreAnimationStoreFromJSON()},beforeDestroy:function(){this.$bus.off("AnimationTimeLineSettled",this.onAnimationTimeLineSettled),this.$bus.off("AnimationTimeLineDestroyed",this.onAnimationTimeLineDestroyed)},methods:{toggleAllAnimationGroups:function(t){var e=this;"element"===(null===t||void 0===t?void 0:t.key)&&(this.animationGroupsAnimating.length?this.animationGroupsAnimating.forEach((function(t){e.$store.commit("stopAnimationGroup",{group:t})})):t.groups.forEach((function(t){e.$store.commit("playAnimationGroup",{group:t})})))},onAnimationTimeLineSettled:function(){this.isAnimationTimelineSettled=!0},onAnimationTimeLineDestroyed:function(){this.isAnimationTimelineSettled=!1},getTargetFromScene:function(t,e){if(t&&e)return"element"===e?window.scene.findObject(t):window.scene.findFeature(t)},handleAnimationClick:function(t,e){var i=this,a=window.scene,n=t.animationList,s=(n.length,t.groupName);switch(e){case"remove":var o=this.$store.state.animation.animationGroupNameInEdit,r=this.$store.state.animation.isTimeLineVisible;if(r&&o)return void this.$message.warning("当前有处于编辑态的动画组,请先退出");this.$confirm("确定要删除动画组:【".concat(s,"】吗?"),"删除动画组",{confirmButtonText:this.$t("menuIconName.confirm"),cancelButtonText:this.$t("messageTips.cancel"),type:"warning"}).then((function(){i.$store.commit("removeAnimationGroupRecover",[t])})).catch((function(){}));break;case"setting":this.$store.commit("toogleElementMenu",""),this.$store.commit("selectElementData",{});var c=this.$store.state.animation.animationGroupNameInEdit,l=this.$store.state.animation.isTimeLineVisible;if(l&&c)return void this.$message.warning("当前有处于编辑态的动画组,请先退出");if(this.isAnimationTimelineSettled)this.$store.commit("setAnimationSubtype",t.isElementAnimationGroup?"animation_element":"animation_camera"),this.$bus.emit("RestoreAnimation",t);else{var m=this.$watch("isAnimationTimelineSettled",(function(e,i){e&&(this.$store.commit("setAnimationSubtype",t.isElementAnimationGroup?"animation_element":"animation_camera"),this.$bus.emit("RestoreAnimation",t),m())}));this.$store.commit("setIsTimeLineVisible",!0)}break;case"togglePlay":t.animationList.some((function(t){return!0===t.isAnimating}))?this.$store.commit("stopAnimationGroup",{group:t}):this.$store.commit("playAnimationGroup",{group:t}),a.render();break;default:break}},restoreAnimationStoreFromJSON:function(){var t=this,e=window.scene,i=Array.from(e.animations.values()).filter((function(t){return!t.fromFeature})),a=[];if(null!==i&&void 0!==i&&i.length){var n=function(e){var i=e.userData;if(i){var n=i.belongToGroup,s=(i.offsetData,i.zeroTimeInfo,a.find((function(t){return t.groupName===n})));if(!s){var o={groupName:n||"未命名动画组",targetsBakMap:{},animationList:[],isElementAnimationGroup:!1,isSomeAnimating:!1};s=o,a.push(s)}var r=e.rootObjectID,c=e.rootType;s.isElementAnimationGroup="camera"!==r;var l=t.getTargetFromScene(r,"object"===c?"element":"feature");if(l){if(s.isElementAnimationGroup){var m=JSON.parse(l.snap());m.position=l.position,s.targetsBakMap[r]=JSON.stringify(m)}else s.targetsBakMap[r]=JSON.stringify(window.scene.getCameraInfo());s.animationList.push({id:r,type:l.type,isAnimating:!!s.isElementAnimationGroup&&(l.animating||l._animating),animationName:e.name})}}};i.forEach((function(t){n(t)}));var s=this.$store;a.forEach((function(t){s.commit("addOrUpdateAnimationGroups",[{groupName:t.groupName,animationList:t.animationList,targetsBakMap:t.targetsBakMap,isElementAnimationGroup:t.isElementAnimationGroup,isSomeAnimating:t.animationList.some((function(t){return!0===t.isAnimating}))}])}))}}}},Q=q,X=Object(p["a"])(Q,U,W,!1,null,null,null),Y=X.exports,Z=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"tab-content-wrapper"},[i("div",{staticClass:"bottom-tree-wrapper cus"},[i("div",{staticClass:"bottom-tree"},[i("div",{staticClass:"tree-list"},[i("div",{staticClass:"list-content"},t._l(t.triggerList,(function(e,a){return i("div",{key:e.id,staticClass:"content-item"},[i("div",{staticClass:"item"},[i("div",{staticClass:"item-left"},[i("CommonSVG",{attrs:{"icon-class":"layer",color:"#fff",size:18}}),i("el-tooltip",{attrs:{enterable:!1,"open-delay":500,effect:"dark",content:e.name,placement:"top"}},[i("span",[t._v(t._s(e.name))])])],1),i("div",{staticClass:"item-right"},[t.isEdit?i("el-tooltip",{attrs:{enterable:!1,"open-delay":100,effect:"dark",content:t.$t("menuIconName.setUp"),placement:"top"}},[i("span",{staticClass:"menu-item"},[i("CommonSVG",{attrs:{size:20,"icon-class":"set_up_feature"},nativeOn:{click:function(i){return i.stopPropagation(),t.handleTriggerListMenu(e,a,"setting")}}})],1)]):t._e(),t.isEdit?i("el-tooltip",{attrs:{enterable:!1,"open-delay":100,effect:"dark",content:t.$t("menuIconName.remove"),placement:"top"}},[i("span",{staticClass:"menu-item",staticStyle:{"margin-top":"-2px"}},[i("CommonSVG",{attrs:{size:16,"icon-class":"delete"},nativeOn:{click:function(i){return i.stopPropagation(),t.handleTriggerListMenu(e,a,"delete")}}})],1)]):t._e()],1)])])})),0)])])])])},tt=[],et={name:"trigger",props:["compKey"],data:function(){return{}},computed:{isEdit:function(){return this.$store.state.scene.sceneEditMode},triggerList:function(){return this.$store.state.trigger.triggerList}},components:{CommonSVG:function(){return i.e("chunk-51f90655").then(i.bind(null,"17d0"))}},watch:{compKey:{deep:!0,handler:function(t,e){this.setSceneTrigger()}}},mounted:function(){this.setSceneTrigger()},methods:{setSceneTrigger:function(){this.$store.commit("getTriggerList")},handleTriggerListMenu:function(t,e,i){var a=this;switch(i){case"setting":var n=window.scene.triggers.get(t.id);this.$store.commit("toggleActiveDialog","empty"),this.$emit("resetCurrentElementSelected"),this.$store.commit("toogleElementMenu","");var s=this.$store.state.scene.dragOverData;if(""!=this.$store.state.widget.settingActive&&t.id!=s.id)return void this.$message(this.$t("messageTips.exitEditingStatus"));var o=JSON.parse(JSON.parse(JSON.stringify(n)));o.subType=o.type,o.type="trigger","sensor"==o.subType&&(o.object={id:n.id,type:n.type}),this.$store.commit("saveFeatureRawData",JSON.stringify(o)),this.$store.commit("saveDragOverData",o),this.$nextTick((function(){a.$store.commit("toggleSettingActive",o.type)}));break;case"delete":this.$confirm(this.$t("featureSetting.triggerConditions.message3",{name:t.name}),this.$t("messageTips.tipsTitle"),{confirmButtonText:this.$t("menuIconName.confirm"),cancelButtonText:this.$t("messageTips.cancel"),type:"warning"}).then((function(){window.scene.removeTrigger(t.id),a.$store.commit("getTriggerList")})).catch((function(){}));break}}}},it=et,at=Object(p["a"])(it,Z,tt,!1,null,"8956b25c",null),nt=at.exports,st=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"top-search"},[i("el-input",{attrs:{size:"small",clearable:"",placeholder:t.$t("others.search")},model:{value:t.searchKeyword,callback:function(e){t.searchKeyword=e},expression:"searchKeyword"}},[i("i",{staticClass:"el-input__icon el-icon-search",attrs:{slot:"prefix"},slot:"prefix"})])],1),i("div",{staticClass:"bottom-tree-wrapper"},[i("div",{staticClass:"setup-content"},t._l(t.markupDatasFiltered,(function(e,a){return i("div",{key:e.id+a,staticClass:"viewpoints-list",on:{click:function(i){return t.restoreData(e)}}},[e.default?i("div",{staticClass:"list-mark"},[t._v(" "+t._s(t.$t("others.default"))+" ")]):t._e(),i("img",{attrs:{src:e.thumbnail,alt:""}}),i("div",{staticClass:"list-bottom"},[i("span",{staticClass:"left-title"},[t._v(" "+t._s(e.name||t.$t("topToolBarMenu.advanced.children.markup.name")+a)+" ")]),t.isEdit?i("div",{staticClass:"right-menu"},[i("el-tooltip",{attrs:{effect:"dark",content:t.$t("menuIconName.rename"),placement:"top"}},[i("span",{on:{click:function(i){return t.handleListMenu(e,a,"bianji")}}},[i("CommonSVG",{attrs:{color:"#98A2B3","icon-class":"amend_feature",size:16}})],1)]),i("el-tooltip",{attrs:{effect:"dark",content:t.$t("menuIconName.delete"),placement:"top"}},[i("span",{on:{click:function(i){return i.stopPropagation(),t.handleListMenu(e,a,"delete")}}},[i("CommonSVG",{attrs:{color:"#98A2B3","icon-class":"delete",size:16}})],1)])],1):t._e()])])})),0)]),t.addFormDialog.dialogState?i("addViewpointMarkupDialog",{attrs:{addType:"markup",isEditName:t.isEditName,inputValue:t.addFormDialog.name},on:{closeAddDialog:t.closeAddDialog,saveData:t.saveData}}):t._e(),i("transition",{attrs:{"enter-active-class":"animate__animated animate__fadeInUp"}},[t.isMarkupComponentVisible?i("MarkupMenu",{on:{saveData:t.onSaveMarkupData}}):t._e()],1)],1)},ot=[],rt={name:"",data:function(){return{searchKeyword:"",regForEmpty:/^\s*$/,isEditName:!1,addFormDialog:{menuState:!1,dialogState:!1,name:"",inputError:!1,curIndex:""}}},components:{CommonSVG:function(){return i.e("chunk-51f90655").then(i.bind(null,"17d0"))},MarkupMenu:function(){return i.e("chunk-43c5d891").then(i.bind(null,"f45e"))},addViewpointMarkupDialog:M},computed:{bottomMenuActive:function(){return this.$store.state.menuList.bottomMenuActive},isMarkupComponentVisible:function(){return"markup"===this.bottomMenuActive},markupDatas:function(){return this.$store.state.markup.markupDatas},isEdit:function(){return this.$store.state.scene.sceneEditMode},markupDatasFiltered:function(){var t=this.searchKeyword,e=this.regForEmpty,i=this.markupDatas;return!t||e.test(t)?i:i.filter((function(e){return e.name.includes(t)}))}},mounted:function(){this.getSceneViewpoints()},methods:{onSaveMarkupData:function(){this.addFormDialog.dialogState=!0},getSceneViewpoints:function(){var t=[];window.scene.viewpoints.length>0&&(window.scene.viewpoints.forEach((function(e){var i=e.type;"markup"===i&&t.unshift(e)})),this.$store.commit("getSceneMarkupList",t))},restoreData:function(t){window.scene.mv.tools.markup.clear(),window.scene.restoreMarkup(t),window.scene.render()},saveData:function(t){var e=this;return Object(o["a"])(Object(s["a"])().mark((function i(){var a,n,o;return Object(s["a"])().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(a=e.$loading({lock:!0,text:e.$t("others.prepare"),spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),n=null,!e.isEditName){i.next=12;break}return o=e.addFormDialog.curIndex,e.markupDatas[o].name=t.name,a.close(),e.addFormDialog.dialogState=!1,e.addFormDialog.curIndex="",e.addFormDialog.name="",e.getSceneViewpoints(),e.isEditName=!1,i.abrupt("return");case 12:return i.next=14,window.scene.snapMarkup().catch((function(t){a.close(),e.addFormDialog.dialogState=!1}));case 14:n=i.sent,e.$store.commit("toggleBottomMenuActive","markup"),n.name=t.name,n.save(window.scene),e.$notify({title:e.$t("topToolBarMenu.advanced.children.markup.name")+" "+n.name,message:e.$t("others.added"),type:"success"}),a.close(),e.$store.commit("setActivedType",""),e.getSceneViewpoints(),e.closeAddDialog();case 23:case"end":return i.stop()}}),i)})))()},closeAddDialog:function(){this.addFormDialog.dialogState=!1,this.addFormDialog.name="",this.addFormDialog.curIndex="",this.isEditName=!1,this.$store.commit("setActivedType","")},handleListMenu:function(t,e,i){var a=this;switch(i){case"delete":this.$confirm(this.$t("messageTips.deleteSomething",{name:t.name}),this.$t("messageTips.tipsTitle"),{confirmButtonText:this.$t("menuIconName.confirm"),cancelButtonText:this.$t("messageTips.cancel"),type:"warning"}).then((function(){var i=a.markupDatas;i.splice(e,1),a.$store.commit("getSceneMarkupList",i);var n=window.scene.viewpoints.findIndex((function(e){return e.id==t.id}));window.scene.viewpoints.splice(n,1),a.$message({type:"success",message:a.$t("messageTips.deleteSuccess")})})).catch((function(){}));break;case"bianji":this.addFormDialog.dialogState=!0,this.addFormDialog.name=t.name,this.addFormDialog.curIndex=e,this.isEditName=!0}}}},ct=rt,lt=Object(p["a"])(ct,st,ot,!1,null,"58d8734c",null),mt=lt.exports,dt={name:"SceneManageView",components:{filterComponent:f,elementList:_,viewpoint:j,pathAnimation:H,animation:Y,trigger:nt,markup:mt,addViewpointMarkupDialog:M},props:["compKey","isShare","isVothingScenemanager"],data:function(){return{dialogWidth:300,dialogLeft:-300,dialogHeight:100,currentViewData:[],addFormDialog:{menuState:!1,dialogState:!1,name:"",inputError:!1,curIndex:""},executingAddHandler:"",isAnimationTimelineMounted:!1,isEditName:!1,menuData:[{label:this.$t("topToolBarMenu.features"),name:"element"},{label:this.$t("topToolBarMenu.advanced.children.viewpoint.name"),name:"viewpoint"},{label:this.$t("topToolBarMenu.advanced.children.markup.name"),name:"markup"},{label:this.$t("topToolBarMenu.advanced.children.animation.name"),name:"animation"},{label:this.$t("topToolBarMenu.advanced.children.trigger.name"),name:"trigger"},{label:this.$t("topToolBarMenu.advanced.children.pathAnimation.name"),name:"pathAnimation"},{label:this.$t("topToolBarMenu.advanced.children.screening.name"),name:"screening"}]}},created:function(){var t=this;this.changeManageSize(),window.onresize=function(){t.changeManageSize()}},mounted:function(){},computed:{selectedSectionData:function(){return this.$store.state.menuList.selectedSectionData},sectionLoaded:function(){return this.$store.state.menuList.sectionLoaded},onlyPreview:function(){return this.$store.state.scene.onlyPreview},isEdit:function(){return this.$store.state.scene.sceneEditMode},bottomMenuActive:function(){return this.$store.state.menuList.bottomMenuActive},localActiveTab:function(){return this.$store.state.sceneSetting.validActivedType}},watch:{"$store.state.scene.sceneEditMode":{handler:function(t){t?this.changeManageSize():this.dialogHeight=100},immediate:!0}},methods:{setStructureTree:function(t){this.$emit("setStructureTree",t)},setCurrentViewData:function(t){this.currentViewData[0]=t[0],this.$set(this.currentViewData,1,t[1]),this.$emit("setCurrentViewData",t)},setInteractiveData:function(t){this.$emit("setInteractiveData",t)},changeManageSize:function(){var t=document.body.clientHeight,e=this.$store.state.scene.topToolBarHeight;this.dialogHeight=100-e/(t/100)},getSceneViewpoints:function(){var t=this,e=[],i=[];window.scene.viewpoints.length>0&&window.scene.viewpoints.forEach((function(a){var n=a.type;"markup"==n?(e.unshift(a),t.$store.commit("getSceneMarkupList",e)):"viewpoint"===n&&(i.unshift(a),t.$store.commit("getSceneViewpointList",i))}))},switchDialog:function(){var t=this,e=document.querySelector(".sourceDom"),i=[];null!=e&&(e.style.transition="left 0.6s",setTimeout((function(){e.style.transition=""}),1e3)),null!==i&&void 0!==i&&i.length&&(i.forEach((function(t){t.style.transition="all 0.6s"})),setTimeout((function(){i.forEach((function(t){t.style.transition=""}))}),1e3)),this.dialogWidth=300,this.$nextTick((function(){t.dialogLeft<0?(t.dialogLeft=0,null!=e&&(e.style.left="335px"),null!==i&&void 0!==i&&i.length&&i.forEach((function(t){t.style.left="300px",t.style.width="calc(100% - 300px)"}))):(t.dialogLeft=-300,null!=e&&(e.style.left="30px"),null!==i&&void 0!==i&&i.length&&i.forEach((function(t){t.style.left=0,t.style.width="100%"})))}))},changeMenuName:function(t,e){this.getIfCanChangeTab()&&(window.offCheckedCoordinate&&window.offCheckedCoordinate(),window.offCheckedCoordinate&&(window.offCheckedCoordinate=null,delete window.offCheckedCoordinate),this.$bus.emit("eventoff"),this.$store.commit("setActivedType",""),"markup"!==t?(this.executingAddHandler="","markup"===this.$store.state.menuList.bottomMenuActive&&this.$store.commit("toggleBottomMenuActive","markup")):"viewpoint"!==t&&(this.addFormDialog.dialogState=!1),"animation"!==t&&(this.$store.commit("setAnimationGroupNameInEdit",null),this.$store.commit("setIsTimeLineVisible",!1)),this.$store.commit("setValidActivedType",t))},getIfCanChangeTab:function(){return!this.$store.state.animation.isTimeLineVisible||(this.$message(this.$t("messageTips.exitEditingAnimate")),!1)},handleAddObject:function(t){var e=this;switch(this.executingAddHandler=null!==t&&void 0!==t?t:"",t){case"markup":this.$store.commit("toggleBottomMenuActive","markup"),this.$store.commit("toggleSettingActive","closeSetting");break;case"viewpoint":this.addFormDialog.dialogState=!0;break;case"animation":if(this.$store.commit("setActivedType",""),"animation"!==this.localActiveTab){this.$store.commit("setValidActivedType","animation"),this.$nextTick((function(){e.handleAddObject("animation")}));break}this.$store.commit("setIsTimeLineVisible",!0)}},saveData:function(t){var e=this;return Object(o["a"])(Object(s["a"])().mark((function i(){var a,n,o,r,c,l;return Object(s["a"])().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(a=e.$loading({lock:!0,text:e.$t("others.prepare"),spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),n=e.localActiveTab,"markup"!==n&&"viewpoint"!==n){i.next=45;break}if(o=null,r="","markup"!==n){i.next=23;break}if(r=e.$t("topToolBarMenu.advanced.children.markup.name"),!e.isEditName){i.next=17;break}return c=e.addFormDialog.curIndex,e.markupDatas[c].name=t.name,a.close(),e.addFormDialog.dialogState=!1,e.addFormDialog.curIndex="",e.addFormDialog.name="",e.getSceneViewpoints(),e.isEditName=!1,i.abrupt("return");case 17:return i.next=19,window.scene.snapMarkup().catch((function(t){a.close(),e.addFormDialog.dialogState=!1}));case 19:o=i.sent,e.$store.commit("toggleBottomMenuActive","markup"),i.next=37;break;case 23:if(r=e.$t("topToolBarMenu.advanced.children.viewpoint.name"),!e.isEditName){i.next=34;break}return l=e.addFormDialog.curIndex,e.viewpointDatas[l].name=t.name,a.close(),e.addFormDialog.dialogState=!1,e.addFormDialog.curIndex="",e.addFormDialog.name="",e.isEditName=!1,e.getSceneViewpoints(),i.abrupt("return");case 34:return i.next=36,window.scene.snapViewpoint().catch((function(t){a.close(),e.addFormDialog.dialogState=!1}));case 36:o=i.sent;case 37:o.name=t.name,o.save(window.scene),e.$notify({title:r+" "+o.name,message:e.$t("others.added"),type:"success"}),a.close(),e.$store.commit("setActivedType",""),e.getSceneViewpoints(),i.next=50;break;case 45:i.t0=n,i.next=("animation"===i.t0||"trigger"===i.t0||"pathAnimation"===i.t0||i.t0,48);break;case 48:return i.abrupt("break",49);case 49:a.close();case 50:e.closeAddDialog();case 51:case"end":return i.stop()}}),i)})))()},closeAddDialog:function(){this.addFormDialog.dialogState=!1,this.addFormDialog.name="",this.addFormDialog.curIndex="",this.isEditName=!1,this.$store.commit("setActivedType","")}}},ut=dt,pt=(i("49da"),Object(p["a"])(ut,a,n,!1,null,null,null));e["default"]=pt.exports}}]);