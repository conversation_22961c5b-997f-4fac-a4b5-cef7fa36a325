(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-c54a3b42"],{"6f59":function(e,t,n){},"80a9":function(e,t,n){"use strict";n.r(t);var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("dialogComp",{attrs:{hideHeader:!1,needClose:"true",zIndex:"4",draw:!0,right:10,drag:!0,title:e.$t("dialog.hierarchies.name"),icon:"icon-details",width:360,dragTopOffset:e.dragTopOffset,height:e.attrHeight,type:"detailInfo",top:e.attrTop},on:{close:e.closeDialog},scopedSlots:e._u([{key:"center",fn:function(){return[n("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"hierarchies-tree-container h100",attrs:{"element-loading-spinner":"el-icon-loading","element-loading-background":"rgba(0, 0, 0, 0.8)"}},[n("div",{staticClass:"tree-content"},[n("el-tree",{attrs:{data:e.treeDatas,"node-key":"id","expand-on-click-node":!1,"default-expanded-keys":e.expandedID,props:e.defaultTreeProps},on:{"node-click":e.handleNodeClick},scopedSlots:e._u([{key:"default",fn:function(t){t.node;var i=t.data;return n("div",{staticClass:"hierarchies-custom-tree"},[n("span",{class:{currentNodel:i.current}},[e._v(" "+e._s(i.name)+" ")])])}}])})],1)])]},proxy:!0}])})},a=[],r=(n("caad"),n("2532"),n("b0c0"),n("d3b7"),n("159b"),n("a630"),n("3ca3"),n("ddb0"),{name:"hierarchies",data:function(){return{detailsData:[],attrHeight:500,attrTop:125,loading:!0,topParent:null,treeDatas:[],defaultTreeProps:{children:"children",label:"name"},currentElementID:"",expandedID:[]}},computed:{dragTopOffset:function(){return this.$store.state.scene.topToolBarHeight},activeDialog:function(){return this.$store.state.dialog.activeDialog}},created:function(){this.setHierarchiesTree()},methods:{handleNodeClick:function(e){window.scene.clearSelection();var t=window.scene.findObject(e.id);t&&(t.selected=!0),window.scene.render(),this.activeDialog.includes("attribute")||this.$store.commit("saveAttrHierarchiesData",e),this.$bus.emit("ElementTreeNodeClick",e)},setHierarchiesTree:function(){this.loading=!0,this.expandedID=[];var e=window.scene.getSelection()[0],t=[{id:"",name:"",children:[]}];e.parentObject?(this.expandedID.push(e.parentObject.id),t[0].id=e.parentObject.id,t[0].name=e.parentObject.name,e.parentObject.children.forEach((function(n){t[0].children.push({id:n.id,name:n.name,current:e.id==n.id})})),this.setParentTree(e.parentObject,t)):(t[0].id=e.id,t[0].name=e.name),this.treeDatas=t,t=[],this.$nextTick((function(){document.querySelector(".currentNodel").scrollIntoView({block:"center",inline:"nearest"})})),this.loading=!1},setParentTree:function(e,t){if(e.parentObject){this.expandedID.push(e.parentObject.id);var n={id:e.parentObject.id,name:e.parentObject.name,children:[]};n.children.push(t[0]),t[0]=n,this.setParentTree(e.parentObject,t)}},setHierarchiesTree_old:function(){this.loading=!0,this.expandedID=[];var e=null,t=window.scene.getSelection()[0];this.currentElementID=t.id,t.parentObject?(this.expandedID.push(t.parentObject.id),this.getTopParent(t.parentObject),e=this.topParent):e=t;var n=[{id:e.id,name:e.name,children:[]}];this.setChildrenData(e.children,n[0]),this.treeDatas=n,this.topParent=null,n=[],this.currentElementID="",this.loading=!1},getTopParent:function(e){e.parentObject?(this.expandedID.push(e.parentObject.id),this.getTopParent(e.parentObject)):(this.expandedID.push(e.id),this.topParent=e)},setChildrenData:function(e,t){var n=this,i=Array.from(e.values());i.forEach((function(e,i){t.children[i]={id:e.id,name:e.name,children:[]},n.currentElementID===e.id&&(t.children[i].current=!0),e.children.size>0&&n.setChildrenData(e.children,t.children[i])}))},closeDialog:function(){this.$store.commit("toggleActiveDialog","hierarchies")}},beforeDestroy:function(){this.$store.commit("saveAttrHierarchiesData",null)}}),c=r,d=(n("f5dc"),n("2877")),s=Object(d["a"])(c,i,a,!1,null,"2401e0e3",null);t["default"]=s.exports},f5dc:function(e,t,n){"use strict";n("6f59")}}]);