(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1798c795","chunk-00e7bce4"],{"029c":function(e,t,i){"use strict";i("0a32")},"03e87":function(e,t,i){"use strict";i("b0d9")},"07ac":function(e,t,i){var n=i("23e7"),a=i("6f53").values;n({target:"Object",stat:!0},{values:function(e){return a(e)}})},"0a32":function(e,t,i){},"12d3":function(e,t,i){},"14d2":function(e,t,i){"use strict";i("e07b")},"498a":function(e,t,i){"use strict";var n=i("23e7"),a=i("58a8").trim,o=i("c8d2");n({target:"String",proto:!0,forced:o("trim")},{trim:function(){return a(this)}})},"49da":function(e,t,i){"use strict";i("6f23")},"688b":function(e,t,i){},"6f23":function(e,t,i){},"9bf3":function(e,t,i){"use strict";i("12d3")},b0d9:function(e,t,i){},b733:function(e,t,i){"use strict";i("688b")},c7fe:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("dialogComp",{staticClass:"sourceDom",attrs:{hideHeader:!1,needClose:"true",zIndex:"4",position:"fixed",draw:!1,top:0,right:0,bottom:0,left:0,drag:!1,title:e.$t("dialog.choiceSet.label3"),width:400,height:150,type:"detailInfo"},on:{close:e.closeAddDialog},scopedSlots:e._u([{key:"center",fn:function(){return[i("div",{staticClass:"add-container"},[i("el-input",{class:{"is-error":e.addFormDialog.inputError},attrs:{size:"small",placeholder:e.$t("dialog.choiceSet.placeholder")},model:{value:e.addFormDialog.name,callback:function(t){e.$set(e.addFormDialog,"name",t)},expression:"addFormDialog.name"}}),i("div",{staticClass:"bottom-btn"},[i("span",{staticClass:"cursor-btn cancel margin-right-8",on:{click:e.closeAddDialog}},[e._v(" "+e._s(e.$t("messageTips.cancel"))+" ")]),i("span",{staticClass:"cursor-btn confirm",on:{click:e.saveData}},[e._v(" "+e._s(e.$t("menuIconName.confirm"))+" ")])])],1)]},proxy:!0}])})},a=[],o=(i("b0c0"),i("d3b7"),i("159b"),i("e9c4"),{name:"",props:["currentModifyItem"],data:function(){return{addFormDialog:{menuState:!1,dialogState:!1,name:"",inputError:!1}}},created:function(){this.currentModifyItem&&(this.addFormDialog.name=this.currentModifyItem.name)},methods:{closeAddDialog:function(){this.addFormDialog.name="",this.$store.commit("setActivedType",""),this.$emit("closeAddDialog")},saveData:function(){var e=this;if(""==this.addFormDialog.name)return this.$message.error(this.$t("messageTips.nameNotEmpty")),void(this.addFormDialog.inputError=!0);var t=0;if(window.scene.selectionSets.forEach((function(i){i.name==e.addFormDialog.name&&t++})),t>0)return this.$message.error(this.addFormDialog.name+" "+this.$t("messageTips.nameAlreadyExists")),!1;if(this.currentModifyItem){window.scene.removeSelectionSet(this.currentModifyItem.name);var i=this.currentModifyItem,n=i.color,a=i.opacity,o=i.ids;window.scene.addSelectionSet(this.addFormDialog.name,o,n,a),this.$message.success(this.$t("messageTips.renameSuccess"))}else{var s={};window.scene.getSelection().forEach((function(e){if(s[e.model.id])s[e.model.id].push(e.id);else{var t=[];t.push(e.id),s[e.model.id]=t}})),window.scene.addSelectionSet(this.addFormDialog.name,s,"",""),this.$message.success(this.$t("messageTips.createdSuccess")),window.scene.clearSelection(),window.scene.render()}var r=[];window.scene.selectionSets.forEach((function(e){r.push(JSON.parse(JSON.parse(JSON.stringify(e))))})),this.$store.commit("setSelectionSets",r),this.closeAddDialog()}}}),s=o,r=(i("029c"),i("b733"),i("2877")),c=Object(r["a"])(s,n,a,!1,null,"21597eda",null);t["default"]=c.exports},c8d2:function(e,t,i){var n=i("5e77").PROPER,a=i("d039"),o=i("5899"),s="​᠎";e.exports=function(e){return a((function(){return!!o[e]()||s[e]()!==s||n&&o[e].name!==e}))}},cb29:function(e,t,i){var n=i("23e7"),a=i("81d5"),o=i("44d2");n({target:"Array",proto:!0},{fill:a}),o("fill")},e07b:function(e,t,i){},f3bc:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("dialogComp",{ref:"sceneManageRef",staticClass:"sceneManageDom",attrs:{needClose:!1,zIndex:99,draw:!1,left:e.dialogLeft,top:"100%",drag:!1,title:e.$t("dialog.sceneManage.name"),icon:"icon-details",width:e.dialogWidth,height:e.dialogHeight+"%",borderRadius:"0",type:"detailInfo",hideHeader:!0},scopedSlots:e._u([{key:"center",fn:function(){return[i("div",{staticClass:"scene-container"},[e.onlyPreview?i("div",{staticClass:"tab-wrap"},[i("div",{staticClass:"tab-wrap-content"},e._l(e.menuData,(function(t){return t.show?i("div",{key:t.name,staticClass:"tab-item",class:{active:e.localActiveTab===t.name},on:{click:function(i){return e.changeMenuName(t.name)}}},[e._v(" "+e._s(t.label)+" ")]):e._e()})),0)]):e._e(),i("div",{staticClass:"menu-content"},[i("div",{directives:[{name:"show",rawName:"v-show",value:"element"===e.localActiveTab,expression:"localActiveTab === 'element'"}],staticClass:"tab-content-wrapper"},[i("elementList",{ref:"featureList",attrs:{compKey:e.compKey,isShare:e.isShare,isVothingScenemanager:e.isVothingScenemanager},on:{setInteractiveData:e.setInteractiveData,setStructureTree:e.setStructureTree,setCurrentViewData:e.setCurrentViewData}})],1),e.onlyPreview?["viewpoint"===e.localActiveTab?i("div",{staticClass:"tab-content-wrapper"},[i("viewpoint",{ref:"viewpoint"})],1):e._e(),"markup"===e.localActiveTab?i("div",{staticClass:"tab-content-wrapper"},[i("markup",{ref:"markup"})],1):e._e(),"animation"===e.localActiveTab?i("div",{staticClass:"tab-content-wrapper"},[i("animation",{ref:"animation"})],1):e._e(),"trigger"===e.localActiveTab?i("div",{staticClass:"tab-content-wrapper"},[i("trigger",{ref:"trigger",attrs:{compKey:e.compKey}})],1):e._e(),"pathAnimation"===e.localActiveTab?i("div",{staticClass:"tab-content-wrapper path-animation-wrap"},[i("pathAnimation",{ref:"pathAnimation"})],1):e._e(),"screening"===e.localActiveTab?i("div",{staticClass:"tab-content-wrapper"},[i("filterComponent",{ref:"screening"})],1):e._e()]:e._e()],2)]),i("div",{staticClass:"switch-btn",staticStyle:{display:"none"},on:{click:function(t){return e.switchDialog()}}},[e.dialogLeft>=0?i("i",{staticClass:"el-icon-caret-left el-icon"}):i("i",{staticClass:"el-icon-caret-right el-icon"})]),e.addFormDialog.dialogState?i("addViewpointMarkupDialog",{attrs:{addType:"viewpoint",isEditName:e.isEditName,inputValue:e.addFormDialog.name},on:{closeAddDialog:e.closeAddDialog,saveData:e.saveData}}):e._e(),i("div",{staticClass:"dragLine",on:{mousedown:function(t){return e.dragChangeViewWidth()}}})]},proxy:!0}])})},a=[],o=i("c7eb"),s=i("1da1"),r=(i("d3b7"),i("159b"),i("caad"),i("2532"),i("b0c0"),function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"filter-element-wrap"},[i("div",{staticClass:"all-color"},[i("div",{staticClass:"btn cursor-btn",on:{click:function(t){return e.allSetColor()}}},[e._v(" "+e._s(e.allSetColorState?e.$t("dialog.choiceSet.label7"):e.$t("dialog.choiceSet.label4"))+" ")])]),i("div",{staticClass:"list-wrap"},e._l(e.selectionList,(function(t){return i("div",{key:t.name,staticClass:"list-item",on:{click:function(i){return i.stopPropagation(),e.selectitem(t)}}},[i("div",{staticClass:"name"},[i("CommonSVG",{attrs:{size:16,"icon-class":"tb_screeningicon"}}),i("span",{staticClass:"text"},[e._v(e._s(t.name))])],1),i("div",{staticClass:"handle"},[i("div",{staticClass:"isHide"},[e.isEdit?i("el-tooltip",{attrs:{enterable:!1,"open-delay":100,effect:"dark",content:e.$t("menuIconName.delete"),placement:"top"}},[i("span",{staticClass:"menu-item cursor-btn",staticStyle:{"margin-top":"-2px"}},[i("CommonSVG",{attrs:{size:16,"icon-class":"delete"},nativeOn:{click:function(i){return i.stopPropagation(),e.handleitem(t,"remove")}}})],1)]):e._e(),e.isEdit?i("div",{staticClass:"menu-item setting-wrap"},[i("span",{staticClass:"cursor-btn"},[i("CommonSVG",{attrs:{size:18,"icon-class":"set_up_feature"}})],1),i("div",{staticClass:"dropdown"},[i("div",{staticClass:"dropdown-menu"},[i("div",{staticClass:"drop-item",on:{click:function(i){return i.stopPropagation(),e.handleitem(t,"rename")}}},[i("CommonSVG",{attrs:{color:"#DCDCDC",size:"16","icon-class":"amend_feature"}}),i("span",{staticClass:"text"},[e._v(e._s(e.$t("menuIconName.rename")))])],1),i("div",{staticClass:"drop-item",on:{click:function(i){return i.stopPropagation(),e.handleitem(t,"update")}}},[i("CommonSVG",{attrs:{color:"#DCDCDC",size:"16","icon-class":"tb_remove_active"}}),i("span",{staticClass:"text"},[e._v(e._s(e.$t("menuIconName.update")))])],1),i("div",{staticClass:"drop-item",on:{click:function(i){return i.stopPropagation(),e.handleitem(t,"addElement")}}},[i("CommonSVG",{attrs:{color:"#DCDCDC",size:"16","icon-class":"once"}}),i("span",{staticClass:"text"},[e._v(e._s(e.$t("dialog.choiceSet.label6")))])],1)])])]):e._e(),t.color?[e.isEdit&&e.setColorArr.includes(t.name)?i("el-tooltip",{attrs:{enterable:!1,"open-delay":100,effect:"dark",content:e.$t("dialog.choiceSet.label5"),placement:"top"}},[i("span",{staticClass:"menu-item cursor-btn",staticStyle:{"margin-top":"-2px"}},[i("CommonSVG",{attrs:{color:"var(--theme)",size:18,"icon-class":"tint_feature"},nativeOn:{click:function(i){return i.stopPropagation(),e.handleitem(t,"resetColor")}}})],1)]):e._e(),e.isEdit&&!e.setColorArr.includes(t.name)?i("el-tooltip",{attrs:{enterable:!1,"open-delay":100,effect:"dark",content:e.$t("menuIconName.color"),placement:"top"}},[i("span",{staticClass:"menu-item cursor-btn",staticStyle:{"margin-top":"-2px"}},[i("CommonSVG",{attrs:{size:18,color:"#98A2B3","icon-class":"tint_feature"},nativeOn:{click:function(i){return i.stopPropagation(),e.handleitem(t,"setColor")}}})],1)]):e._e()]:e._e()],2),i("span",{on:{click:function(e){e.stopPropagation()}}},[i("el-color-picker",{attrs:{"show-alpha":"",size:"mini"},on:{change:function(i){return e.setMarkupColor(i,t)}},model:{value:t.selectColor,callback:function(i){e.$set(t,"selectColor",i)},expression:"item.selectColor"}})],1)])])})),0),e.addFormDialog.dialogState?i("AddScreeningDialog",{attrs:{currentModifyItem:e.currentModifyItem},on:{closeAddDialog:e.closeAddDialog}}):e._e()],1)}),c=[],l=i("3835"),d=(i("3ca3"),i("ddb0"),i("d81d"),i("ac1f"),i("5319"),i("1276"),i("99af"),i("a9e3"),i("a630"),i("a434"),i("e9c4"),i("07ac"),i("c7fe")),m={name:"",data:function(){return{currentModifyItem:null,addFormDialog:{dialogState:!1,name:"",inputError:!1},setColorArr:[],allSetColorState:!1}},components:{CommonSVG:function(){return i.e("chunk-51f90655").then(i.bind(null,"17d0"))},AddScreeningDialog:d["default"]},computed:{isEdit:function(){return this.$store.state.scene.sceneEditMode},selectionList:function(){var e=this.$store.state.selectionSets.selectionList;return e.map((function(e){if(e.color){var t=e.color.replace(/[(|)|rgb]/g,""),i=t.split(","),n=Object(l["a"])(i,3),a=n[0],o=n[1],s=n[2];e.selectColor="rgba(".concat(a,",").concat(o,",").concat(s,", ").concat(e.opacity,")")}else e.selectColor="";return e})),e}},mounted:function(){this.init(),this.$bus.on("onSetFilterComponentState",this.setListColorState)},beforeDestroy:function(){this.$bus.off("onSetFilterComponentState",this.setListColorState)},methods:{setListColorState:function(){this.setColorArr=[]},setMarkupColor:function(e,t){var i,n,a,o;if(e){var s=e.replace(/[(|)|rgba]/g,""),r=s.split(","),c=Object(l["a"])(r,4);i=c[0],n=c[1],a=c[2],o=c[3]}var d=Object.assign(t,{color:i?"rgb(".concat(i,",").concat(n,",").concat(a,")"):"",opacity:i?Number(o):""});if(window.scene.addSelectionSet(d.name,d.ids,d.color,d.opacity),this.setColorArr.includes(t.name)){var m=Array.from(window.scene.features.keys()),u=[];for(var p in d.ids)m.includes(p)&&(u=u.concat(d.ids[p]));if(u.length){var h=window.scene.createArrayObject(u);h.setColor(d.color,d.opacity),window.scene.render()}}if(e)this.handleitem(t,"setColor");else{var f=this.setColorArr.indexOf(d.name);for(var g in this.setColorArr.splice(f,1),d.ids)d.ids[g].forEach((function(e){window.scene.findObject(e).resetColor()}));window.scene.render()}this.init()},closeAddDialog:function(){this.addFormDialog.dialogState=!1,this.addFormDialog.name=""},init:function(){var e=[];window.scene.selectionSets.forEach((function(t){e.push(JSON.parse(JSON.parse(JSON.stringify(t))))})),this.$store.commit("setSelectionSets",e)},allSetColor:function(){var e=this;if(this.allSetColorState){var t=[];if(this.setColorArr.forEach((function(e){var i=window.scene.selectionSets.get(e),n=Object.values(i.ids);n.forEach((function(e){t=t.concat(e)}))})),t.length){var i=window.scene.createArrayObject(t);i.resetColor(),this.allSetColorState=!1,this.setColorArr=[],this.$nextTick((function(){window.scene.render()}))}}else window.scene.selectionSets.forEach((function(t){var i=JSON.parse(JSON.parse(JSON.stringify(t)));if(i.color){e.setColorArr.includes(i.name)||e.setColorArr.push(i.name);var n=[];for(var a in i.ids)n=n.concat(i.ids[a]);if(n.length){var o=window.scene.createArrayObject(n);o.setColor(i.color,i.opacity)}window.scene.render(),e.allSetColorState=!0}}))},selectitem:function(e){var t=Array.from(window.scene.features.keys());for(var i in e.ids)t.includes(i)&&e.ids[i].forEach((function(e){var t=window.scene.findObject(e);t&&(t.selected=!0)}));this.$store.commit("toogleElementMenu","model"),window.scene.render()},handleitem:function(e,t){var i=this;switch(t){case"remove":return void this.$confirm(this.$t("messageTips.deleteSomething",{name:e.name}),this.$t("messageTips.tipsTitle"),{confirmButtonText:this.$t("menuIconName.confirm"),cancelButtonText:this.$t("messageTips.cancel"),type:"warning"}).then((function(){window.scene.removeSelectionSet(e.name),i.$message({type:"success",message:i.$t("messageTips.deleteSuccess")}),i.init()})).catch((function(){}));case"rename":return this.currentModifyItem=e,this.addFormDialog.name=e.name,void(this.addFormDialog.dialogState=!0);case"update":var n=window.scene.selectedObjects;if(0===n.size)return void this.$message.info(this.$t("dialog.choiceSet.message1"));for(var a in e.ids)e.ids[a].forEach((function(e){window.scene.findObject(e).resetColor()}));var o={};window.scene.getSelection().forEach((function(e){if(o[e.model.id])o[e.model.id].push(e.id);else{var t=[];t.push(e.id),o[e.model.id]=t}}));var s=Object.assign(e,{ids:o});window.scene.addSelectionSet(s.name,s.ids,s.color,s.opacity),-1!==this.setColorArr.indexOf(e.name)&&this.handleitem(s,"setColor"),this.$message.success(this.$t("messageTips.updateSuccess")),window.scene.clearSelection(),this.$store.commit("toogleElementMenu","");break;case"addElement":var r=window.scene.selectedObjects;if(0===r.size)return void this.$message.info(this.$t("dialog.choiceSet.message2"));for(var c in e.ids)e.ids[c].forEach((function(e){window.scene.findObject(e).resetColor()}));var l=e.ids;r.forEach((function(e){if(l[e.model.id])l[e.model.id].push(e.id);else{var t=[];t.push(e.id),l[e.model.id]=t}}));var d=Object.assign(e,{ids:l});window.scene.addSelectionSet(d.name,d.ids,d.color,d.opacity),-1!==this.setColorArr.indexOf(e.name)&&this.handleitem(d,"setColor"),this.$message.success(this.$t("dialog.choiceSet.message3")),window.scene.clearSelection(),this.$store.commit("toogleElementMenu","");break;case"setColor":var m=Array.from(window.scene.features.keys()),u=[];for(var p in e.ids)m.includes(p)&&(u=u.concat(e.ids[p]));if(u.length){var h=window.scene.createArrayObject(u);h.setColor(e.color,e.opacity)}-1===this.setColorArr.indexOf(e.name)&&this.setColorArr.push(e.name),window.scene.clearSelection(),window.scene.render();break;case"resetColor":for(var f in e.ids)e.ids[f].forEach((function(e){window.scene.findObject(e).resetColor()}));var g=this.setColorArr.indexOf(e.name);this.setColorArr.splice(g,1),window.scene.render();break}this.init()}}},u=m,p=(i("03e87"),i("14d2"),i("2877")),h=Object(p["a"])(u,r,c,!1,null,"922a8e26",null),f=h.exports,g=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("div",{staticClass:"top-search"},[i("el-input",{attrs:{size:"small",clearable:"",placeholder:e.$t("others.search")},on:{input:e.setComputedTreeDatas},model:{value:e.searchKeyword,callback:function(t){e.searchKeyword=t},expression:"searchKeyword"}},[i("i",{staticClass:"el-input__icon el-icon-search",attrs:{slot:"prefix"},slot:"prefix"})])],1),i("div",{staticClass:"bottom-tree-wrapper",class:""!=e.settingShow?"objectSetting-visible":""},[i("div",e._b({staticClass:"bottom-tree"},"div",{"data-update":e.needUpdateScene},!1),e._l(e.computedTreeDatas,(function(t,n){return i("div",{key:n,staticClass:"tree-list"},[t.datas.length?i("div",{staticClass:"list-title item-hover flex-box"},[i("div",{staticClass:"element-name"},[i("el-tooltip",{attrs:{enterable:!1,content:t.listState?e.$t("others.collapse"):e.$t("others.expand"),effect:"dark",placement:"top"}},[t.listState?i("i",{staticClass:"el-icon-caret-top mg-r4",on:{click:function(e){t.listState=!1}}}):i("i",{staticClass:"el-icon-caret-bottom mg-r4",on:{click:function(e){t.listState=!0}}})]),i("span",{staticStyle:{"margin-right":"4px"}},[i("CommonSVG",{attrs:{"icon-class":t.icon,size:16}})],1),e._v(" "+e._s(t.name)+" ")],1),"underlay"!==t.label&&e.isEdit?i("div",{staticClass:"handle-all-feature"},[i("el-tooltip",{attrs:{enterable:!1,content:e.$t("menuIconName.remove"),effect:"dark",placement:"top"}},[i("span",{staticClass:"margin-left-10"},[i("CommonSVG",{attrs:{size:16,"icon-class":"delete"},nativeOn:{click:function(i){return i.stopPropagation(),e.removeBatchFeature(t)}}})],1)])],1):e._e()]):e._e(),i("el-collapse-transition",[i("div",{directives:[{name:"show",rawName:"v-show",value:t.listState,expression:"treeDatas.listState"}],staticClass:"list-content"},e._l(t.datas,(function(a,o){return i("div",{key:a.label,staticClass:"content-item"},[i("div",{staticClass:"list-title",staticStyle:{width:"100%",padding:"0"}},[i("div",{staticClass:"item",class:[{featureSelected:e.currentSelectFeatureIDs.includes(a.id)},{"item-more-hover":e.modelExtendMenu.activeEq==o&&"model"==n}],staticStyle:{width:"100%"}},[i("div",{staticClass:"item-left item-hover"},[a.visible?i("el-tooltip",{attrs:{enterable:!1,effect:"dark",content:e.$t("others.visible"),placement:"top"}},[i("span",{staticClass:"menu-item",staticStyle:{opacity:"1","flex-shrink":"0"}},[i("CommonSVG",{attrs:{size:16,"icon-class":"tree_visible_feature"},nativeOn:{click:function(t){return t.stopPropagation(),e.handleViewsSubmenu(a,o,"hide")}}})],1)]):e._e(),a.visible?e._e():i("el-tooltip",{attrs:{enterable:!1,effect:"dark",content:e.$t("others.visible1"),placement:"top"}},[i("span",{staticClass:"menu-item",staticStyle:{opacity:"1","flex-shrink":"0"}},[i("CommonSVG",{attrs:{size:16,"icon-class":"tree_hidden_feature"},nativeOn:{click:function(t){return t.stopPropagation(),e.handleViewsSubmenu(a,o,"show")}}})],1)]),"model"===n?i("el-tooltip",{staticClass:"visibleIcon",attrs:{enterable:!1,content:a.listState?e.$t("others.collapse"):e.$t("others.expand"),effect:"dark",placement:"top"}},[a.listState?i("i",{staticClass:"el-icon-caret-top",on:{click:function(e){a.listState=!1}}}):i("i",{staticClass:"el-icon-caret-bottom",on:{click:function(e){a.listState=!0}}})]):e._e(),i("span",{staticClass:"el-icon iconfont icon-tuceng",staticStyle:{"padding-left":"10px","min-width":"24px"}}),i("el-tooltip",{attrs:{enterable:!1,"open-delay":500,effect:"dark",content:""==a.name?t.name+o:a.name,placement:"top"}},[i("span",[e._v(e._s(""==a.name?t.name+o:a.name))])])],1),i("div",{staticClass:"item-right pd-r0",style:e.modelSectionMenuStyle(o,n)},[!e.isEdit||e.isShare||e.isVothingScenemanager?e._e():i("el-tooltip",{attrs:{enterable:!1,effect:"dark",content:e.$t("menuIconName.remove"),placement:"top"}},[i("span",{staticClass:"menu-item",staticStyle:{"margin-top":"-2px"}},[i("CommonSVG",{attrs:{size:16,"icon-class":"delete"},nativeOn:{click:function(t){return t.stopPropagation(),e.handleViewsSubmenu(a,o,"remove")}}})],1)]),!e.isEdit||e.isShare||e.isVothingScenemanager||!a.data||a.data.assetLabel?e._e():i("el-tooltip",{attrs:{enterable:!1,effect:"dark",content:e.$t("menuIconName.setUp"),placement:"top"}},[i("span",{staticClass:"menu-item"},[i("CommonSVG",{attrs:{size:18,"icon-class":"set_up_feature"},nativeOn:{click:function(t){return t.stopPropagation(),e.handleViewsSubmenu(a,o,"setting")}}})],1)]),e.unwantedLocationMenuType.includes(n)?e._e():[i("el-tooltip",{attrs:{"open-delay":500,enterable:!1,effect:"dark",content:e.$t("menuIconName.location"),placement:"top"}},[i("span",{staticClass:"menu-item"},[i("CommonSVG",{attrs:{"icon-class":"focusing_feature",size:16},nativeOn:{click:function(t){return t.stopPropagation(),e.handleViewsSubmenu(a,o,"location")}}})],1)])],"model"==n?[i("el-tooltip",{attrs:{enterable:!1,effect:"dark",content:e.$t("others.more"),placement:"top"}},[i("span",{staticClass:"menu-item"},[i("CommonSVG",{staticClass:"model-extend-more",attrs:{size:18,color:e.modelExtendMenu.activeEq==o?"var(--theme)":"#98A2B3","icon-class":"more_y"},nativeOn:{mouseenter:function(t){return t.stopPropagation(),e.handleMoreMenuEnter(a,o)},mouseleave:function(t){return t.stopPropagation(),e.handleMoreMenuLeave()}}})],1)])]:e._e()],2)])]),"model"==n?[i("el-collapse-transition",[i("div",{directives:[{name:"show",rawName:"v-show",value:a.listState,expression:"data.listState"}],staticClass:"list-content"},[e._l(a.formatViews,(function(t,n){return i("div",{key:n},[t.children.length?[i("div",{staticClass:"list-title pd-r0"},[i("div",{staticClass:"flex item-hover"},[t.visible>0?i("el-tooltip",{attrs:{enterable:!1,effect:"dark",content:e.$t("menuIconName.hideView"),placement:"top"}},[t.visible>0?i("span",{staticClass:"menu-item",staticStyle:{opacity:"1","flex-shrink":"0"}},[i("CommonSVG",{staticStyle:{"margin-right":"8px"},attrs:{size:16,"icon-class":"tree_visible_feature"},nativeOn:{click:function(i){return i.stopPropagation(),e.hideAllView(t,"hide")}}})],1):e._e()]):e._e(),t.visible?e._e():i("el-tooltip",{attrs:{enterable:!1,effect:"dark",content:e.$t("menuIconName.showView"),placement:"top"}},[t.visible?e._e():i("span",{staticClass:"menu-item",staticStyle:{opacity:"1","flex-shrink":"0"}},[i("CommonSVG",{staticStyle:{"margin-right":"8px"},attrs:{size:16,"icon-class":"tree_hidden_feature"},nativeOn:{click:function(i){return i.stopPropagation(),e.hideAllView(t,"show")}}})],1)]),i("el-tooltip",{staticClass:"visibleIcon",attrs:{enterable:!1,content:t.listState?e.$t("others.collapse"):e.$t("others.expand"),effect:"dark",placement:"top"}},[t.listState?i("i",{staticClass:"el-icon-caret-top",on:{click:function(e){t.listState=!1}}}):i("i",{staticClass:"el-icon-caret-bottom",on:{click:function(e){t.listState=!0}}})]),i("CommonSVG",{staticStyle:{margin:"0 5px 0 25px"},attrs:{"icon-class":"view_icon_"+t.icon,color:"#FFFFFF",size:16}}),e._v(" "+e._s(t.label)+" ")],1),i("div",{staticClass:"right-icon"})]),i("el-collapse-transition",[i("div",{directives:[{name:"show",rawName:"v-show",value:t.listState,expression:"item.listState"}],staticClass:"list-content"},e._l(t.children,(function(o,s){return i("div",{key:o.id,staticClass:"content-item"},[i("div",{staticClass:"item"},[i("div",{staticClass:"item-left"},["loaded"==o.status?[t.visible&&o.visible?i("el-tooltip",{attrs:{enterable:!1,effect:"dark",content:e.$t("menuIconName.hideView"),placement:"top"}},[t.visible&&o.visible?i("span",{staticClass:"menu-item",staticStyle:{"margin-right":"58px","flex-shrink":"0"}},[i("CommonSVG",{attrs:{size:16,"icon-class":"tree_visible_feature"},nativeOn:{click:function(i){return e.handleViewsSubmenu(o,n,"hide",t)}}})],1):e._e()]):e._e(),o.visible&&t.visible?e._e():i("el-tooltip",{attrs:{enterable:!1,effect:"dark",content:e.$t("menuIconName.showView"),placement:"top"}},[o.visible&&t.visible?e._e():i("span",{staticClass:"menu-item",staticStyle:{"margin-right":"58px","flex-shrink":"0"}},[i("CommonSVG",{attrs:{size:16,"icon-class":"tree_hidden_feature"},nativeOn:{click:function(i){return e.handleViewsSubmenu(o,n,"show",t)}}})],1)])]:i("div",{staticClass:"model-view-bmp"}),i("el-tooltip",{attrs:{enterable:!1,"open-delay":500,effect:"dark",content:o.name,placement:"top"}},[i("span",[e._v(e._s(o.name))])])],2),i("div",{staticClass:"item-right",style:e.modelViewMenuStyle(n,s)},["loaded"==o.status?i("el-tooltip",{attrs:{enterable:!1,"open-delay":500,effect:"dark",content:e.toolTipContent(o),placement:"top"}},["loaded"==o.status?i("span",{staticClass:"menu-item"},[i("CommonSVG",{attrs:{"icon-class":"tree_structure_feature",size:16},nativeOn:{click:function(t){return t.stopPropagation(),e.handleViewsSubmenu(o,n,"viewTree")}}})],1):e._e()]):e._e(),"loaded"==o.status?i("el-tooltip",{attrs:{enterable:!1,"open-delay":500,effect:"dark",content:e.$t("menuIconName.section"),placement:"top"}},["loaded"==o.status?i("span",{staticClass:"menu-item"},[i("CommonSVG",{attrs:{"icon-class":"tree_cutting_feature",color:e.aboutSection.sectionActivated&&e.aboutSection.index==n&&e.aboutSection.parentIndex==s?"var(--theme)":"#98A2B3",size:16},nativeOn:{click:function(t){t.stopPropagation(),e.aboutSection.parentIndex=s,e.handleViewsSubmenu(o,n,"section")}}})],1):e._e()]):e._e(),a._2DMode||"loaded"!=o.status||"2D"!==t.type||"Section"===o.category||"Elevation"===o.category?e._e():i("el-tooltip",{attrs:{enterable:!1,"open-delay":500,effect:"dark",content:e.$t("menuIconName.interactive2D3D"),placement:"top"}},["loaded"==o.status?i("span",{staticClass:"menu-item"},[i("CommonSVG",{attrs:{"icon-class":"view_icon_Sheet",color:"#98A2B3",size:16},nativeOn:{click:function(t){t.stopPropagation(),e.aboutSection.parentIndex=s,e.handleViewsSubmenu(o,n,"interactive")}}})],1):e._e()]),"loaded"==o.status?i("el-tooltip",{attrs:{enterable:!1,"open-delay":500,effect:"dark",content:e.$t("menuIconName.uninstallView"),placement:"top"}},["loaded"==o.status?i("span",{staticClass:"menu-item"},[i("CommonSVG",{attrs:{"icon-class":"point",color:"#67C23A",size:16},nativeOn:{click:function(t){return e.handleViewsSubmenu(o,n,"dispose")}}})],1):e._e()]):e._e(),"disposed"==o.status?i("el-tooltip",{attrs:{enterable:!1,"open-delay":500,effect:"dark",content:e.$t("menuIconName.loadView"),placement:"top"}},["disposed"==o.status?i("span",{staticClass:"menu-item"},[i("CommonSVG",{attrs:{"icon-class":"point",color:"#F56C6C",size:16},nativeOn:{click:function(i){return e.handleViewsSubmenu(o,n,"load",t)}}})],1):e._e()]):e._e()],1)])])})),0)])]:e._e()],2)})),e._l(a.structureTree,(function(t){return i("div",{key:t.id,staticClass:"structure-item"},[[i("div",{staticClass:"item",on:{click:function(i){return e.setStructureTree(t.id,a)}}},[i("div",{staticClass:"item-left"},[i("span",{staticStyle:{"padding-left":"54px"}},[i("CommonSVG",{attrs:{"icon-class":t.icon,color:"#fff",size:16}})],1),e._v(" "+e._s(t.name)+" ")]),i("div",{staticClass:"item-right"},[i("span",{staticClass:"tree-icon"},[i("el-tooltip",{attrs:{enterable:!1,"open-delay":500,effect:"dark",content:e.$t("dialog.systemTree.name"),placement:"top"}},[i("span",{staticClass:"menu-item"},[i("CommonSVG",{attrs:{"icon-class":"tree_structure_feature",size:16},nativeOn:{click:function(i){return i.stopPropagation(),e.setStructureTree(t.id,a)}}})],1)])],1),"system"===t.id?i("el-tooltip",{attrs:{enterable:!1,"open-delay":500,effect:"dark",content:e.$t("menuIconName.topo"),placement:"top"}},[i("span",{staticClass:"menu-item"},[i("CommonSVG",{attrs:{color:"#98A3B3","icon-class":"gplot_feature",size:16},nativeOn:{click:function(t){return t.stopPropagation(),e.opentopo(a)}}})],1)]):[t.visible?i("el-tooltip",{attrs:{enterable:!1,effect:"dark",content:e.$t("others.visible"),placement:"top"}},[i("span",{staticClass:"menu-item"},[i("CommonSVG",{attrs:{size:16,"icon-class":"tree_visible_feature"},nativeOn:{click:function(i){return i.stopPropagation(),e.handleStructureVisible(a,t,"hide")}}})],1)]):e._e(),t.visible?e._e():i("el-tooltip",{attrs:{enterable:!1,effect:"dark",content:e.$t("others.visible1"),placement:"top"}},[i("span",{staticClass:"menu-item"},[i("CommonSVG",{attrs:{size:16,"icon-class":"tree_hidden_feature"},nativeOn:{click:function(i){return i.stopPropagation(),e.handleStructureVisible(a,t,"show")}}})],1)])]],2)])]],2)}))],2)])]:e._e()],2)})),0)])],1)})),0)]),i("div",{staticClass:"model-extend-menu",on:{mouseleave:function(t){return e.handleMoreMenuLeave()},mouseover:function(t){return e.handleExtendMenuHover()}}},[i("el-tooltip",{attrs:{enterable:!1,effect:"dark",content:e.$t("menuIconName.filter"),placement:"top"}},[i("span",{staticClass:"menu-item cursor-btn"},[i("CommonSVG",{attrs:{size:18,color:"#98A2B3","icon-class":"filtration_feature"},nativeOn:{click:function(t){return t.stopPropagation(),e.handleViewsSubmenu(e.modelExtendMenu.data,e.modelExtendMenu.activeEq,"filter")}}})],1)]),i("el-tooltip",{attrs:{enterable:!1,"open-delay":500,effect:"dark",content:e.$t("menuIconName.section"),placement:"top"}},[i("span",{staticClass:"menu-item cursor-btn"},[i("CommonSVG",{attrs:{color:e.aboutSection.sectionActivated&&-1==e.aboutSection.index&&e.aboutSection.parentIndex==e.eq?"var(--theme)":"#98A2B3","icon-class":"tree_cutting_feature",size:16},nativeOn:{click:function(t){return t.stopPropagation(),e.handleViewsSubmenu(e.modelExtendMenu.data,e.modelExtendMenu.activeEq,"section")}}})],1)]),e.modelExtendMenu.data&&e.modelExtendMenu.data.hasTimeseries?i("el-tooltip",{attrs:{enterable:!1,"open-delay":500,effect:"dark",content:e.$t("featureSetting.timeSeries.name"),placement:"top"}},[i("span",{staticClass:"menu-item cursor-btn"},[i("CommonSVG",{attrs:{color:"#98A2B3","icon-class":"time_series",size:16},nativeOn:{click:function(t){return t.stopPropagation(),e.handleViewsSubmenu(e.modelExtendMenu.data,e.modelExtendMenu.activeEq,"timeSeries")}}})],1)]):e._e()],1)])},v=[],w=i("ade3"),b=i("5530"),S=(i("b64b"),i("00b4"),i("4de4"),i("cb29"),i("c740"),i("7db0"),i("b995")),$=i("2909"),y=i("ed08"),D={data:function(){return{postDataCurrentElement:[],postDataIot:[]}},computed:{postDataWidgetsArr:function(){return this.$store.state.scene.sceneList||[]}},methods:{setVothingPostData:function(){var e=this;this.$watch((function(){return this.$store.state.websocket.allSocketObj}),(function(t){if(t){var i=window.scene.features;i.forEach((function(i){var n=i.dataKey?i.dataKey:i.type+"-"+i.id;for(var a in e.postDataWidgetsArr)"element"===e.postDataWidgetsArr[a].type&&e.postDataWidgetsArr[a].elementid===n&&function(){e.postDataCurrentElement=e.postDataWidgetsArr[a];var n=[];Object.keys(t).forEach((function(i){var a=e.postDataCurrentElement.data.widgets.entity;a&&a.map((function(e){i===e.id&&n.push(t[i])}))})),e.postDataIot={id:e.postDataCurrentElement.elementid,data:n},"annotation"==e.postDataCurrentElement.data.elementType?e.setAnnotation(i):"heatmap"==e.postDataCurrentElement.data.elementType?e.setHeatmap(i):e.setOtherElement(i)}()}))}}),{deep:!0})},setAnnotation:function(e){var t=this.postDataIot.data;if(t&&t[0]&&t[0].DataDetails){var i=[],n=this.postDataCurrentElement.data.widgets.entityProperty;n&&(Object.keys(n).forEach((function(e){var t;n[e]&&(t=i).push.apply(t,Object($["a"])(n[e]))})),i=Object(y["a"])(i,"name"));var a={title:"",dataList:[]};a.title=this.postDataCurrentElement.data.title,t.forEach((function(e){e&&e.DataDetails.forEach((function(t){i.forEach((function(i){if(i.name===t.PropertyName){var n={};n["key"]=t.PropertyName+" ("+e.DeviceDetail.DeviceName+")",n["value"]=t.Value,a.dataList.push(n)}}))}))}));var o=JSON.parse(JSON.stringify(e.data));o.content.dataList=a.dataList,window.scene.paused=!0,window.scene.postData(o,e.dataKey),window.scene.paused=!1}},setHeatmap:function(e){var t=this,i=this.postDataIot.data;if(i&&i[0]&&i[0].DataDetails){var n={},a=this.postDataCurrentElement.data.widgets.relationArr;i.forEach((function(i){i&&a.forEach((function(a){a.chosedEntity.id===i.DeviceDetail.DeviceId&&i.DataDetails.forEach((function(i){if(a.chosedProperty.name===i.PropertyName){var o=parseFloat(i.Value+""),s=isNaN(o);if(s)return;if(""!==a.javascriptCodes){var r=a.javascriptCodes,c={position:[parseFloat(a.relationData.relationKey.value.x+""),parseFloat(a.relationData.relationKey.value.y+"")],value:t.fn(r,o)};n=c}else{var l={position:[parseFloat(a.relationData.relationKey.value.x+""),parseFloat(a.relationData.relationKey.value.y+"")],value:o};n=l}var d=JSON.parse(JSON.stringify(e.data));d.points[a.relationData.relationKey.index]=n,window.scene.paused=!0,window.scene.postData(d,e.dataKey),window.scene.paused=!1}}))}))}))}},setOtherElement:function(e){var t=this,i=this.postDataIot.data;if(i&&i[0]&&i[0].DataDetails){var n={},a=this.postDataCurrentElement.data.widgets.relationArr;i.forEach((function(e){e&&a.forEach((function(i){i.chosedEntity.id===e.DeviceDetail.DeviceId&&e.DataDetails.forEach((function(e){if(i.chosedProperty.name===e.PropertyName){var a=parseFloat(e.Value+""),o=isNaN(a);if(o)return;if(""!==i.javascriptCodes){var s=i.javascriptCodes;n[i.relationData.type]=t.fn(s,a)}else{if("color"===i.relationData.type)return;n[i.relationData.type]=a}}}))}))}));var o=JSON.parse(JSON.stringify(e.data));o=n,window.scene.paused=!0,window.scene.postData(o,e.dataKey),window.scene.paused=!1}},fn:function(e,t){var i=e+" return value",n=new Function("data","value",i),a=null;return n(t,a)}}},C=i("2f62"),A={name:"elementList",props:["compKey","isShare","isVothingScenemanager"],mixins:[D],data:function(){return{searchKeyword:"",computedTreeDatas:{},needUpdateScene:0,setSceneManageTree:null,regForEmpty:/^\s*$/,setSceneFirstGetDataFromAjax:null,aboutSection:{parentIndex:-1,index:-1,lastID:"",sectionActivated:!1},unwantedLocationMenuType:["underlay","skybox","_3dBuilding","dem"],modelExtendMenu:{activeEq:-1,data:null,keep:!1}}},computed:Object(b["a"])(Object(b["a"])({},Object(C["b"])({selectedSectionData:function(e){return e.menuList.selectedSectionData},sectionLoaded:function(e){return e.menuList.sectionLoaded},isEdit:function(e){return e.scene.sceneEditMode},sectionSelectData:function(e){return e.menuList.sectionSelectData},currentSelectFeatureIDs:function(e){return e.scene.currentSelectFeatureIDs},settingShow:function(e){return e.widget.settingActive}})),{},{modelViewMenuStyle:function(){return function(e,t){var i="";return i=this.aboutSection.sectionActivated&&e==this.aboutSection.index&&this.aboutSection.parentIndex==t?"min-width:60px":"min-width:30px",i}},modelSectionMenuStyle:function(){return function(e,t){var i="";return this.aboutSection.sectionActivated&&-1==this.aboutSection.index&&this.aboutSection.parentIndex==e&&"model"==t&&(i="min-width:50px"),i}},allAnimations:function(){return this.$store.getters.allAnimations}}),watch:{sectionSelectData:function(e){0==e.length&&(this.aboutSection.lastID="",this.aboutSection.parentIndex=-1,this.aboutSection.sectionActivated=!1)},compKey:{deep:!0,handler:function(e,t){"pathAnimation"!==this.compKey.type&&this.setSceneManageTree(this.compKey),this.setComputedTreeDatas()}}},components:{CommonSVG:function(){return i.e("chunk-51f90655").then(i.bind(null,"17d0"))}},created:function(){},mounted:function(){var e=this,t=Object(S["a"])(window.scene),i=t.sceneManageTreeDatas,n=t.setSceneManageTree,a=t.setSceneFirstGetDataFromAjax;this.sceneManageTreeDatas=i,this.setSceneManageTree=n,this.setSceneFirstGetDataFromAjax=a;var o=this.$watch((function(){return this.$store.state.scene.sceneTreeDataAjax}),(function(t){if(o&&window.scene.features.size>0)o();else if("{}"!=t&&""!=t&&void 0!=t&&null!=t){try{window.scene.fromJSON(t),window.scene.load(),setTimeout((function(){e.setSceneFirstGetDataFromAjax(t),e.setComputedTreeDatas(),e.setVothingPostData()}),1e3)}catch(i){console.error(i),setInterval((function(){e.$message.error(e.$t("messageTips.fromJSONError"))}),5e3),window.parent.postMessage("fromJSONError","*")}o&&o()}}),{immediate:!0});this.setComputedTreeDatas(),this.$bus.on("synchronizeViewVisibleStatus",this.handleSynchronizeViewVisibleStatus)},methods:{toolTipContent:function(e){var t=e.id,i="";switch(t){case"Space":i=this.$t("dialog.spaceTree.name");break;case"Area":i=this.$t("dialog.areaTree.name");break;default:i=this.$t("dialog.viewTree.name");break}return i},setComputedTreeDatas:function(){var e=this,t={},i=this;if(this.sceneManageTreeDatas&&function(){for(var i=e.searchKeyword,n=e.sceneManageTreeDatas,a=e.regForEmpty,o=0,s=Object.keys(n);o<s.length;o++){var r,c=s[o],l=n[c];if(null!==l&&void 0!==l&&null!==(r=l.datas)&&void 0!==r&&r.length)if(i&&!a.test(i)){var d=l.datas.filter((function(e){return-1!==e.name.indexOf(i)}));null!==d&&void 0!==d&&d.length&&(t[c]=Object(b["a"])(Object(b["a"])({},l),{},{datas:d}))}else t[c]=n[c]}}(),t.model&&t.model.datas.forEach((function(t,n){var a,o=[],s=[],r=[];null===(a=t.views)||void 0===a||a.forEach((function(e){"3D"===e.type?o.push(e):"2D"===e.type&&("Grid"===e.category?s.push(e):r.push(e))}));var c=0;o.forEach((function(e){e.visible&&(c+=1)}));var l=0;r.forEach((function(e){e.visible&&(l+=1)}));var d=0;s.forEach((function(e){e.visible&&(d+=1)}));var m,u=[{label:e.$t("featureDatas.model.viewTypes[0]"),icon:"3D",visible:c,children:o,listState:!0,type:"3D"},{label:e.$t("featureDatas.model.viewTypes[1]"),icon:"Plan",visible:l,children:r,listState:!0,type:"2D"},{label:e.$t("featureDatas.model.viewTypes[2]"),icon:"Grid",visible:d,children:s,listState:!0,type:"Grid"}];if(t.id&&(m=window.scene.features.get(t.id)),m&&(i.$set(t,"formatViews",u),m.hasSystem)){var p=[{id:"system",name:e.$t("featureDatas.model.viewTypes[3]"),icon:"tree_system_feature"}];i.$set(t,"structureTree",p)}})),t.dem){var n=window.scene.features.get("dem").sphere;n?this.unwantedLocationMenuType.includes("dem")&&this.unwantedLocationMenuType.pop():this.unwantedLocationMenuType.includes("dem")||this.unwantedLocationMenuType.push("dem")}this.computedTreeDatas=t},closeCurrentElementDialog:function(e){var t=this.$store.state.scene.dragOverData;""!=this.settingShow&&t.type==e.type&&t.id==e.id&&this.$store.commit("toggleSettingActive","closeSetting")},hideAllView:function(e,t){"show"===t?e.children.forEach((function(t){if(t.parentID){var i=window.scene.features.get(t.parentID).views.get(t.id);i.visible=!0,e.visible=!0,t.visible=!0}else{var n=window.scene.features.get(t.id);n.visible=!0,e.visible=!0,t.visible=!0}})):(e.children.forEach((function(t){if(t.parentID){var i=window.scene.features.get(t.parentID).views.get(t.id);i.visible=!1,e.visible=!1,t.visible=!1}else{var n=window.scene.features.get(t.id);n.visible=!1,e.visible=!1,t.visible=!1}})),window.scene.selectedObjects.size>0&&(this.$store.commit("toogleElementMenu",""),this.$store.commit("selectElementData",{})),window.scene.clearSelection()),window.scene.render()},handleViewsSubmenu:function(e,t,i,n,a){var o=this;if(""!=this.settingShow){var s=["filter","timeSeries","section","interactive"];if(s.includes(i))return void this.$message(this.$t("messageTips.exitEditingStatus"))}if(this.$bus.emit("eventoff"),window.offCheckedCoordinate&&window.offCheckedCoordinate(),window.offCheckedCoordinate&&(window.offCheckedCoordinate=null,delete window.offCheckedCoordinate),"location"!==i&&this.sectionLoaded)this.$message.error(this.$t("messageTips.exitSection"));else{switch(i){case"show":if(n){if(n.visible)if(e.parentID){var r=window.scene.features.get(e.parentID).views.get(e.id);r.visible=!0,e.visible=!0}else{var c=window.scene.features.get(e.id);c.visible=!0,e.visible=!0}}else if(e.parentID){var l=window.scene.features.get(e.parentID).views.get(e.id);l.visible=!0,e.visible=!0}else{var d=window.scene.features.get(e.id);d.visible=!0,e.visible=!0}e.formatViews&&e.formatViews.forEach((function(e){o.hideAllView(e,"show")})),"underlay"==e.type&&this.$store.commit("setSceneUnderlayStatus",{underlayState:!0});break;case"hide":if(e.parentID){var m=window.scene.features.get(e.parentID).views.get(e.id);m.visible=!1,e.visible=!1}else{var u=window.scene.features.get(e.id);u.visible=!1,e.visible=!1}e.formatViews&&e.formatViews.forEach((function(e){o.hideAllView(e,"hide")})),"underlay"==e.type&&this.$store.commit("setSceneUnderlayStatus",{underlayState:!1}),window.scene.selectedObjects.size>0&&(this.$store.commit("toogleElementMenu",""),this.$store.commit("selectElementData",{})),window.scene.clearSelection();break;case"location":var p=window.scene.features.get(e.id);window.scene.fit2Feature(p);break;case"load":var h=window.scene.features.get(e.parentID),f=window.scene.features.get(e.parentID).views.get(e.id);f.visible=!0,h.activeView(e.id),this.$nextTick((function(){o.$set(e,"status","loaded"),e.visible=!0,n.visible||(n.visible=!0)})),h._2DMode&&n.children.forEach((function(t){if(t.id!=e.id){var i=h.views.get(t.id),n=i.loaded?"loaded":"disposed";o.$set(t,"status",n)}}));break;case"dispose":var g=window.scene.features.get(e.parentID).views.get(e.id);g.dispose(),this.$nextTick((function(){o.$set(e,"status","disposed"),e.visible=!1;var t=o.$store.state.dialog.activeDialog;-1!==t.indexOf("interactive")&&o.$store.commit("toggleActiveDialog","interactive"),-1!==t.indexOf("viewTree")&&o.$store.commit("toggleActiveDialog","viewTree"),o.aboutSection.lastID==e.id&&(o.aboutSection.sectionActivated&&window.scene.mv.tools.clip.dispose(),o.aboutSection.lastID="",o.aboutSection.parentIndex=-1,o.aboutSection.sectionActivated=!1)}));break;case"tree":this.$emit("setCurrentModelData",[e,Date.parse(new Date)]);var v=this.$store.state.dialog.activeDialog;-1==v.indexOf("modelTree")&&this.$store.commit("toggleActiveDialog","modelTree");break;case"remove":this.$confirm(this.$t("messageTips.removeFeature",{name:e.name}),this.$t("messageTips.tipsTitle"),{confirmButtonText:this.$t("messageTips.confirm"),cancelButtonText:this.$t("messageTips.cancel"),type:"warning"}).then((function(){o.removeAllRelatedAnimations(e);var i=window.scene.features.get(e.id),n=null;n="annotation"==e.type&&e.data.assetLabel?o.computedTreeDatas["assetLabel"].datas:o.computedTreeDatas[e.type].datas,n.splice(t,1);try{var a=window.scene.getSelection();a.length&&a.forEach((function(e){e.selected=!1})),i.dispose()}catch(c){console.warn(c)}o.$store.commit("toogleElementMenu",""),o.$store.commit("selectElementData",{}),setTimeout((function(){o.$nextTick((function(){window.scene.render()}))}),200);var s=o.$store.state.scene.sceneList||[];for(var r in s)"element"===s[r].type&&s[r].elementid===e.dataKey&&o.$store.commit("deleteElementID",e.dataKey);o.closeCurrentElementDialog(e),"underlay"==e.type&&o.$store.commit("setSceneUnderlayStatus",{underlayState:!1}),o.$store.commit("listenAnnotation")})).catch((function(e){console.warn(e)}));break;case"filter":this.$store.commit("setCurrentFilterId",e.id),this.$store.commit("toggleActiveDialog","filterCondition");break;case"setting":var b=this.$store.state.scene.dragOverData;if(""!=this.settingShow)return void(e.id!=b.id&&this.$message(this.$t("messageTips.exitEdit")));var S=window.scene.features.get(e.id);this.$store.commit("toggleActiveDialog","empty"),this.$emit("resetCurrentElementSelected"),this.$store.commit("toogleElementMenu",""),"polygon"==e.type&&(S.data.tileExcavation?(e.title=this.$t("featureDatas.polygon.extend.tileExcavation.name"),e.subType="tileExcavation"):S.data.extrude?(e.title=this.$t("featureDatas.polygon.extend.extrude.name"),e.subType="extrude"):S.data.terrainFlatten?(e.title=this.$t("featureDatas.polygon.extend.flatten.name"),e.subType="flatten"):S.data.terrainExcavation?(e.title=this.$t("featureDatas.polygon.extend.excavation.name"),e.subType="excavation"):S.data.tileFlatten?(e.title=this.$t("featureDatas.polygon.extend.tileFlatten.name"),e.subType="tileFlatten"):"demOpacity"===S.data.subType?(e.title=this.$t("featureDatas.polygon.extend.demOpacity.name"),e.subType="demOpacity"):S.data.water?(e.title=this.$t("featureDatas.polygon.extend.water.name"),e.subType="water"):S.data.fill&&S.data.environment?(e.title=this.$t("featureDatas.polygon.extend.environment.name"),e.subType="environment"):(e.title=this.$t("featureDatas.polygon.extend.surface.name"),e.subType="surface"));var $=JSON.parse(JSON.stringify(e)),y=JSON.parse(JSON.parse(JSON.stringify(S)));switch(Object.assign($,y),void 0!=S.data&&($.data=S.data),e.type){case"gltf":case"fbx":$.scale=S.scale;break;case"model":e.currentVersion=S.currentVersion,Object.assign(e,y),S.correction&&($.correction=JSON.parse(JSON.stringify(S.correction))),e.hideFormButton&&(e.hideFormButton=!1);break;case"_3dTiles":S.correction&&($.correction=JSON.parse(JSON.stringify(S.correction)));break}this.$store.commit("saveFeatureRawData",JSON.stringify($)),this.$store.commit("saveDragOverData",e),this.$nextTick((function(){"underlay"==e.type||"skybox"==e.type?(o.$store.commit("toggleActiveDialog","Source"),o.$store.commit("toggleSettingActive","closeSetting")):o.$store.commit("toggleSettingActive",e.type)}));break;case"section":var D=null;if(this.aboutSection.index=-1,this.aboutSection.sectionActivated&&(window.scene.mv.tools.clip.dispose(),this.$store.commit("toogleSectionLoaded",!1),"sectionPlaneing"!==this.bottomMenuActive&&"sectioning"!==this.bottomMenuActive||this.$store.commit("toggleBottomMenuActive",""),this.$store.commit("setSectionValue","")),this.aboutSection.lastID==e.id){this.aboutSection.lastID="",this.aboutSection.parentIndex=-1,this.aboutSection.sectionActivated=!1;break}window.scene.mv._THREE;if(e.parentID){this.aboutSection.index=t;var C=window.scene.features.get(e.parentID);if(D=C.views.get(e.id),"3D"==e.category){"sectioning"!==this.bottomMenuActive&&(this.$store.commit("toggleBottomMenuActive","sectioning"),this.$store.commit("setSectionValue","box")),this.$store.commit("setSelectedSectionData",[]);var A=[];this.selectedSectionData.includes(e.parentID)&&(A=this.selectedSectionData),A.push(e.parentID),this.$store.commit("setSelectedSectionData",A)}else{var k=[];this.selectedSectionData[e.parentID]&&(k=this.selectedSectionData[e.parentID]),k.push(e.id);var x=Object.assign(this.selectedSectionData,Object(w["a"])({},e.parentID,k));this.$store.commit("setSelectedSectionData",x),this.$store.commit("setPlaneSelectionId",D.id),this.$store.commit("updateSectionRandom",(new Date).getTime()),"sectionPlaneing"!==this.bottomMenuActive&&(this.$store.commit("toggleBottomMenuActive","sectionPlaneing"),this.$store.commit("setSectionValue","plane"))}}else{this.aboutSection.parentIndex=t,"sectioning"!==this.bottomMenuActive&&(this.$store.commit("toggleBottomMenuActive","sectioning"),this.$store.commit("setSectionValue","box")),this.$store.commit("setSelectedSectionData",[]);var E=[];this.selectedSectionData.includes(e.id)&&(E=this.selectedSectionData),E.push(e.id),this.$store.commit("setSelectedSectionData",E)}this.$store.commit("toogleSectionLoaded",!0),this.aboutSection.sectionActivated=!0,this.aboutSection.lastID=e.id;break;case"viewTree":var T=e;T.formerDataIndex=t,this.$emit("setCurrentViewData",[T,Date.parse(new Date)]);var _=this.$store.state.dialog.activeDialog;-1==_.indexOf("viewTree")&&this.$store.commit("toggleActiveDialog","viewTree");break;case"interactive":var O=window.scene.features.get(e.parentID),M=Object.assign(e,{offset:O.offset,origin:O.origin,rotation:O.rotation,altitude:O.altitude});this.$emit("setInteractiveData",[M,Date.parse(new Date)]);var V=this.$store.state.dialog.activeDialog;-1==V.indexOf("interactive")&&this.$store.commit("toggleActiveDialog","interactive");break;case"timeSeries":var I=window.scene.features.get(e.id);this.$store.commit("toggleActiveDialog","empty"),this.$emit("resetCurrentElementSelected"),this.$store.commit("toogleElementMenu","");var F=this.$store.state.scene.dragOverData;if(""!=this.settingShow&&e.id!=F.id)return void this.$message(this.$t("messageTips.exitEdit"));e.hideFormButton=!0,e.currentVersion=I.currentVersion,this.$store.commit("saveDragOverData",e),this.$nextTick((function(){o.$store.commit("toggleSettingActive",i)}));break}window.scene.render(),this.needUpdateScene=Date.parse(new Date)}},setStructureTree:function(e,t){this.$emit("setStructureTree",{type:e,modelId:t.id})},opentopo:function(e){var t=this;this.$store.commit("toggleTopologyActive","closeTopology");var i=window.scene.features.get(e.id);try{i.load("system").then((function(){t.$store.commit("toggleTopologyActive","openTopology")}))}catch(n){i.loadSystem().then((function(){t.$store.commit("toggleTopologyActive","openTopology")}))}this.$nextTick((function(){window.scene.snapViewpoint().then((function(e){var i=JSON.parse(JSON.stringify(e));t.$store.commit("setViewpoint",i)})),t.$store.commit("saveTopoCurrentModel",e)}))},handleStructureVisible:function(e,t,i){var n=window.scene.features.get(e.id);"hide"===i?(t.visible=!1,n[t.id]=!1):(t.visible=!0,n[t.id]=!0),window.scene.render()},toggleTreeNodel:function(e){e.listState=!e.listState},handleAllFeatureMenu:function(e,t){switch(e){case"hide":t.visible=!1,t.datas.length&&t.datas.forEach((function(e){return e.visible=!1}));break;case"show":t.visible=!0,t.datas.length&&t.datas.forEach((function(e){return e.visible=!0}));break;case"remove":this.removeBatchFeature(t)}},removeBatchFeature:function(e){var t=this;this.$confirm(this.$t("messageTips.removeFeature",{name:e.name}),this.$t("messageTips.tipsTitle"),{confirmButtonText:this.$t("messageTips.confirm"),cancelButtonText:this.$t("messageTips.cancel"),type:"warning"}).then((function(){e.datas.forEach((function(e){t.removeAllRelatedAnimations(e);var i=window.scene.features.get(e.id);try{var n=window.scene.getSelection();n.length&&n.forEach((function(e){e.selected=!1})),i.dispose()}catch(s){console.warn(s)}var a=t.$store.state.scene.sceneList||[];for(var o in a)"element"===a[o].type&&a[o].elementid===e.dataKey&&t.$store.commit("deleteElementID",e.dataKey)})),t.computedTreeDatas[e.label].datas=[],t.$store.commit("toogleElementMenu",""),t.$store.commit("selectElementData",{}),setTimeout((function(){t.$nextTick((function(){window.scene.render()}))}),200);var i=t.$store.state.widget.settingActive;""!=i&&i==e.label&&t.$store.commit("toggleSettingActive","closeSetting"),t.$store.commit("listenAnnotation")})).catch((function(e){console.warn(e)}))},removeAllRelatedAnimations:function(e){var t=this.allAnimations.filter((function(t){return t.modelId===e.id}));null!==t&&void 0!==t&&t.length&&this.$store.commit("removeAnimationBoth",t.map((function(e){return e.animationName})))},stopAllRelatedAnimations:function(e){var t=this.allAnimations.filter((function(t){return t.modelId===e.id}));null!==t&&void 0!==t&&t.length&&this.$store.commit("stopAnimationBoth",t.map((function(e){return e.animationName})))},handleSynchronizeViewVisibleStatus:function(e){var t=this.computedTreeDatas.model.datas.findIndex((function(t){return t.id==e[0].parentID})),i=this.computedTreeDatas.model.datas[t].formatViews[e[0].formerDataIndex].children.find((function(t){return t.id==e[0].id}));i.visible=e[1]},outClickCloseModelExtend:function(){var e=this;document.addEventListener("click",(function(t){var i=document.querySelector(".model-extend-menu"),n=document.querySelector(".model-extend-more");t.target===n||t.target===i||i.contains(t.target)||(i.removeAttribute("style"),e.modelExtendMenu.activeEq=-1)}))},modelExtendMenuHide:function(){document.querySelector(".model-extend-menu").removeAttribute("style"),this.modelExtendMenu.activeEq=-1,this.modelExtendMenu.keep=!1},handleMoreMenuLeave:function(){var e=this;this.modelExtendMenu.keep=!1,setTimeout((function(){e.modelExtendMenu.keep||e.modelExtendMenuHide()}),200)},handleMoreMenuEnter:function(e,t){if("model"==e.type){var i=window.scene.features.get(e.id);e.hasTimeseries=i.hasTimeseries}this.modelExtendMenu.activeEq=t,this.modelExtendMenu.data=e,this.modelExtendMenu.keep=!0;var n=event.target.closest(".menu-item").getBoundingClientRect().top,a=event.target.closest(".menu-item").getBoundingClientRect().right;document.querySelector(".model-extend-menu").setAttribute("style","top:".concat(n,"px;left:").concat(a,"px"))},handleExtendMenuHover:function(){this.modelExtendMenu.keep=!0}}},k=A,x=(i("9bf3"),Object(p["a"])(k,g,v,!1,null,"61a6ff27",null)),E=x.exports,T=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("div",{staticClass:"top-search"},[i("el-input",{attrs:{size:"small",clearable:"",placeholder:e.$t("others.search")},model:{value:e.searchKeyword,callback:function(t){e.searchKeyword=t},expression:"searchKeyword"}},[i("i",{staticClass:"el-input__icon el-icon-search",attrs:{slot:"prefix"},slot:"prefix"})])],1),i("div",{staticClass:"bottom-tree-wrapper"},[i("div",{staticClass:"setup-content"},e._l(e.viewpointDatasFiltered,(function(t,n){return i("div",{key:t.id+n,staticClass:"viewpoints-list",on:{click:function(i){return e.restoreData(t)}}},[t.default?i("div",{staticClass:"list-mark"},[e._v(" "+e._s(e.$t("others.default"))+" ")]):e._e(),i("img",{attrs:{src:t.thumbnail,alt:""}}),i("div",{staticClass:"list-bottom"},[i("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",enterable:!1,content:t.name||e.$t("topToolBarMenu.advanced.children.viewpoint.name")+n,placement:"top"}},[i("span",{staticClass:"left-title"},[e._v(" "+e._s(t.name||e.$t("topToolBarMenu.advanced.children.viewpoint.name")+n)+" ")])]),e.isEdit?i("div",{staticClass:"right-menu"},[i("el-tooltip",{attrs:{effect:"dark",content:e.$t("menuIconName.rename"),placement:"top"}},[i("span",{on:{click:function(i){return e.handleListMenu(t,n,"bianji")}}},[i("CommonSVG",{attrs:{color:"#98A2B3","icon-class":"amend_feature",size:16}})],1)]),i("el-tooltip",{attrs:{effect:"dark",content:e.$t("menuIconName.delete"),placement:"top"}},[i("span",{on:{click:function(i){return i.stopPropagation(),e.handleListMenu(t,n,"delete")}}},[i("CommonSVG",{attrs:{color:"#98A2B3","icon-class":"delete",size:16}})],1)])],1):e._e()],1)])})),0)]),e.addFormDialog.dialogState?i("addViewpointMarkupDialog",{attrs:{addType:"viewpoint",isEditName:e.isEditName,inputValue:e.addFormDialog.name},on:{closeAddDialog:e.closeAddDialog,saveData:e.saveData}}):e._e()],1)},_=[],O=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("dialogComp",{attrs:{hideHeader:!1,needClose:"true",zIndex:"100",position:"fixed",draw:!1,top:0,right:0,bottom:0,left:0,drag:!1,title:e.isEditName?e.$t("menuIconName.edit1"):e.$t("menuIconName.add")+e.contentForaddType,width:300,height:150,type:"detailInfo"},on:{close:e.closeAddDialog},scopedSlots:e._u([{key:"center",fn:function(){return[i("div",{staticClass:"add-container"},[i("el-input",{ref:"inputForAddDialog",class:{"is-error":e.addFormDialog.inputError},attrs:{size:"small",placeholder:e.$t("others.inputName2",{name:e.contentForaddType})},scopedSlots:e._u([{key:"prepend",fn:function(){return[i("span",[e._v(e._s(e.contentForaddType+e.$t("others.name")))])]},proxy:!0}]),model:{value:e.addFormDialog.name,callback:function(t){e.$set(e.addFormDialog,"name","string"===typeof t?t.trim():t)},expression:"addFormDialog.name"}}),i("div",{staticClass:"bottom-btn"},[i("span",{staticClass:"cursor-btn cancel margin-right-8",on:{click:e.closeAddDialog}},[e._v(" "+e._s(e.$t("messageTips.cancel"))+" ")]),i("span",{staticClass:"cursor-btn confirm",on:{click:e.saveData}},[e._v(" "+e._s(e.$t("menuIconName.confirm"))+" ")])])],1)]},proxy:!0}])})},M=[],V=(i("498a"),{name:"",props:["addType","isEditName","inputValue"],data:function(){return{regForEmpty:/^\s*$/,addFormDialog:{menuState:!1,dialogState:!1,name:"",inputError:!1,curIndex:""}}},computed:{contentForaddType:function(){switch(this.addType){case"markup":return this.$t("topToolBarMenu.advanced.children.markup.name");case"viewpoint":return this.$t("topToolBarMenu.advanced.children.viewpoint.name");case"animation":return this.$t("topToolBarMenu.advanced.children.animation.name");default:return""}}},mounted:function(){var e=this;this.inputValue&&(this.addFormDialog.name=this.inputValue),this.$nextTick((function(){var t;null===(t=e.$refs.inputForAddDialog)||void 0===t||t.focus()}))},methods:{beforeValidate:function(){var e=this;if(!this.addFormDialog.name||this.regForEmpty.test(this.addFormDialog.name))return this.$message.error(this.$t("messageTips.nameNotEmpty")),this.addFormDialog.inputError=!0,!1;this.addFormDialog.name=this.addFormDialog.name.trim();var t=this.addType;if("markup"===t||"viewpoint"===t){if(0==window.scene.viewpoints.length)return!0;var i=window.scene.viewpoints.findIndex((function(t){return t.name==e.addFormDialog.name}));return!(i>=0)||(this.$message.error(this.addFormDialog.name+" "+this.$t("messageTips.nameAlreadyExists")),!1)}switch(t){case"animation":case"trigger":case"pathAnimation":case"screening":default:break}return!0},saveData:function(){var e=this.beforeValidate();if(!e)return!1;this.$emit("saveData",{name:this.addFormDialog.name})},closeAddDialog:function(){this.$emit("closeAddDialog")}}}),I=V,F=Object(p["a"])(I,O,M,!1,null,"1268def3",null),N=F.exports,P={name:"",data:function(){return{searchKeyword:"",regForEmpty:/^\s*$/,isEditName:!1,addFormDialog:{menuState:!1,dialogState:!1,name:"",inputError:!1,curIndex:""}}},computed:{viewpointDatas:function(){return this.$store.state.viewpoint.viewpointDatas},isEdit:function(){return this.$store.state.scene.sceneEditMode},viewpointDatasFiltered:function(){var e=this.searchKeyword,t=this.regForEmpty,i=this.viewpointDatas;return!e||t.test(e)?i:i.filter((function(t){return t.name.includes(e)}))}},components:{CommonSVG:function(){return i.e("chunk-51f90655").then(i.bind(null,"17d0"))},addViewpointMarkupDialog:N},mounted:function(){this.getSceneViewpoints()},methods:{restoreData:function(e){window.scene.mv.tools.markup.clear(),window.scene.restoreViewpoint(e),window.scene.render()},getSceneViewpoints:function(){var e=[];window.scene.viewpoints.length>0&&(window.scene.viewpoints.forEach((function(t){var i=t.type;"viewpoint"===i&&e.unshift(t)})),this.$store.commit("getSceneViewpointList",e))},saveData:function(e){var t=this;return Object(s["a"])(Object(o["a"])().mark((function i(){var n,a,s;return Object(o["a"])().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(n=t.$loading({lock:!0,text:t.$t("others.prepare"),spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),a=null,!t.isEditName){i.next=12;break}return s=t.addFormDialog.curIndex,t.viewpointDatas[s].name=e.name,n.close(),t.addFormDialog.dialogState=!1,t.addFormDialog.curIndex="",t.addFormDialog.name="",t.isEditName=!1,t.getSceneViewpoints(),i.abrupt("return");case 12:return i.next=14,window.scene.snapViewpoint().catch((function(e){n.close(),t.addFormDialog.dialogState=!1}));case 14:a=i.sent,a.name=e.name,a.save(window.scene),t.$notify({title:t.$t("topToolBarMenu.advanced.children.viewpoint.name")+" "+a.name,message:t.$t("others.added"),type:"success"}),n.close(),t.$store.commit("setActivedType",""),t.getSceneViewpoints(),t.closeAddDialog();case 22:case"end":return i.stop()}}),i)})))()},closeAddDialog:function(){this.addFormDialog.dialogState=!1,this.addFormDialog.name="",this.addFormDialog.curIndex="",this.isEditName=!1,this.$store.commit("setActivedType","")},handleListMenu:function(e,t,i){var n=this;switch(i){case"default":var a=this.viewpointDatas.findIndex((function(e){return!0===e.default}));a>=0&&(this.viewpointDatas[a].default=!1),t!==a&&(e.default=!e.default);break;case"delete":this.$confirm(this.$t("messageTips.deleteSomething",{name:e.name}),this.$t("messageTips.tipsTitle"),{confirmButtonText:this.$t("menuIconName.confirm"),cancelButtonText:this.$t("messageTips.cancel"),type:"warning"}).then((function(){var i=n.viewpointDatas;i.splice(t,1),n.$store.commit("getSceneViewpointList",i);var a=window.scene.viewpoints.findIndex((function(t){return t.id==e.id}));window.scene.viewpoints.splice(a,1),n.$message({type:"success",message:n.$t("messageTips.deleteSuccess")})})).catch((function(){}));break;case"bianji":this.addFormDialog.dialogState=!0,this.addFormDialog.name=e.name,this.addFormDialog.curIndex=t,this.isEditName=!0}}}},L=P,G=Object(p["a"])(L,T,_,!1,null,"6aa90be4",null),j=G.exports,z=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[e.pathAnimationList.length?i("div",{staticClass:"bottom-tree-wrapper cus"},[i("div",{staticClass:"bottom-tree"},[i("div",{staticClass:"tree-list"},[i("el-collapse-transition",[i("div",{staticClass:"list-content"},e._l(e.pathAnimationList,(function(t,n){return i("div",{key:t.id,staticClass:"content-item"},[i("div",{staticClass:"item",class:{isActivated:e.curPathAnimationId===t.id}},[i("div",{staticClass:"item-left"},[i("CommonSVG",{attrs:{"icon-class":"layer",color:"#fff",size:18}}),i("el-tooltip",{attrs:{enterable:!1,"open-delay":500,effect:"dark",content:t.name,placement:"top"}},[i("span",[e._v(" "+e._s(t.name)+" "),t.name===e.$t("topToolBarMenu.advanced.children.pathAnimation.name")?[e._v(" -"+e._s(n+1)+" ")]:e._e()],2)])],1),i("div",{staticClass:"item-right"},[e.isEdit?i("el-tooltip",{attrs:{enterable:!1,"open-delay":100,effect:"dark",content:e.$t("menuIconName.delete"),placement:"top"}},[i("span",{staticClass:"menu-item",staticStyle:{"margin-top":"-2px"}},[i("CommonSVG",{attrs:{size:16,"icon-class":"delete"},nativeOn:{click:function(i){return i.stopPropagation(),e.handlePathAnimation(t,"remove")}}})],1)]):e._e(),e.isEdit?i("el-tooltip",{attrs:{enterable:!1,"open-delay":100,effect:"dark",content:e.$t("menuIconName.setUp"),placement:"top"}},[i("span",{staticClass:"menu-item"},[i("CommonSVG",{attrs:{size:20,"icon-class":"set_up_feature"},nativeOn:{click:function(i){return i.stopPropagation(),e.handlePathAnimation(t,"setting")}}})],1)]):e._e(),i("el-tooltip",{attrs:{enterable:!1,"open-delay":100,effect:"dark",content:t.isRunning?e.$t("formRelational.animate.pause"):e.$t("formRelational.animate.play"),placement:"top"}},[i("span",{staticClass:"menu-item",staticStyle:{"margin-top":"-2px"}},[i("CommonSVG",{attrs:{size:16,color:t.isRunning?"var(--theme)":"#98A2B3","icon-class":t.isRunning?"suspend_feature":"bofang"},nativeOn:{click:function(i){return i.stopPropagation(),e.handlePathAnimation(t,"play")}}})],1)]),i("el-tooltip",{attrs:{enterable:!1,"open-delay":100,effect:"dark",content:e.$t("menuIconName.exit"),placement:"top"}},[i("span",{staticClass:"menu-item",staticStyle:{"margin-top":"-2px"}},[i("CommonSVG",{attrs:{size:16,"icon-class":"quit"},nativeOn:{click:function(i){return i.stopPropagation(),e.handlePathAnimation(t,"stop")}}})],1)])],1)])])})),0)])],1)])]):e._e()])},B=[],R={name:"",data:function(){return{curPathAnimationId:"",curPathAnimationObj:{},isPause:!1,pathAnimationStateEvents:!1,restartPathAnimation:!1}},computed:{isEdit:function(){return this.$store.state.scene.sceneEditMode},pathAnimationList:function(){return this.$store.state.pathAnimation.pathAnimationList},_curPathanimationId:function(){return this.$store.state.pathAnimation.curPathanimationId}},components:{CommonSVG:function(){return i.e("chunk-51f90655").then(i.bind(null,"17d0"))}},mounted:function(){this.init()},methods:{init:function(){var e=window.scene.pathRoamings,t=[];e.forEach((function(e){e.isRunning=!1,t.push(e)})),this.$store.commit("setPathAnimationList",t)},setPathAnimation:function(e){if(""==this.$store.state.widget.settingActive){var t=this.$store.state.pathAnimation.roamActive;if(t)this.$message(this.$t("messageTips.exitRoam"));else{var i=window.scene.findPathRoaming(e.id);this.curPathAnimationObj=e,this.curPathAnimationId=e.id,this.pathAnimationStateEvents&&window.scene.mv.events.pathroamStateChange.off("change",this.pathAnimationState);var n=this.$store.state.dialog.activeDialog;-1!==n.indexOf("pathAnimation")&&this.$store.commit("toggleActiveDialog","pathAnimation");var a=this.$store.state.pathAnimation.editPathAnimation;a&&(window.scene.mv.tools.transform.deactive(),this.$store.commit("toogleEditPathAnimation",!1)),this.restartPathAnimation=!1,this.$store.commit("setCurPathanimationId",e.id),window.scene.mv.tools.draw.deactive(),window.scene.mv.tools.pathRoam.init(i),this.$store.commit("tooglePathanimationActive",!0)}}else this.$message(this.$t("messageTips.exitEditingStatus"))},handlePathAnimation:function(e,t){var i=this,n=this.$store.state.pathAnimation.roamActive;if(n)this.$message(this.$t("messageTips.exitRoam"));else switch(window.scene.mv.events.pathroamStateChange.on("change",this.pathAnimationState),this.pathAnimationStateEvents=!0,t){case"remove":if(""!=this.$store.state.widget.settingActive)return void this.$message(this.$t("messageTips.exitEditingStatus"));this.$confirm(this.$t("dialog.pathAnimation.message",{name:e.name}),this.$t("messageTips.tipsTitle"),{confirmButtonText:this.$t("menuIconName.confirm"),cancelButtonText:this.$t("messageTips.cancel"),type:"warning"}).then((function(){window.scene.removePathRoaming(e.id);var t=window.scene.pathRoamings,n=i.$store.state.pathAnimation.pathanimationActive;n&&(window.scene.mv.tools.pathRoam.stop(),window.scene.mv.tools.pathRoam.clear(),i.$store.commit("tooglePathanimationActive",!1)),i.curPathAnimationObj={},e.isRunning=!1,i.isPause=!1,i.pathAnimationStateEvents=!1,window.scene.mv.events.pathroamStateChange.off("change",i.pathAnimationState);var a=[];t.forEach((function(e){a.push(e)})),i.$store.commit("setPathAnimationList",a),i.$store.commit("setCurPathanimationId","")})).catch((function(){}));break;case"setting":if(this.$store.commit("toogleElementMenu",""),this.$store.commit("selectElementData",{}),""!=this.$store.state.widget.settingActive)return void this.$message(this.$t("messageTips.exitEditingStatus"));var a=this.$store.state.pathAnimation.pathanimationActive;(!this.curPathAnimationObj.id||this.curPathAnimationObj.id&&this.curPathAnimationObj.id===e.id)&&(this.$store.commit("setCurPathanimationId",""),a&&(window.scene.mv.tools.pathRoam.stop(),window.scene.mv.tools.pathRoam.clear()),this.curPathAnimationObj={},e.isRunning=!1,this.isPause=!1,this.pathAnimationStateEvents=!1,window.scene.mv.events.pathroamStateChange.off("change",this.pathAnimationState)),this.$store.commit("saveFeatureRawData",JSON.stringify(e)),e.type="pathAnimation",window.scene.mv.tools.draw.active(),window.scene.mv.tools.draw.startDrawLine(e.positions),this.$store.commit("saveDragOverData",e),this.$store.commit("toggleSettingActive","pathAnimation"),this.$store.commit("setCurPathanimationId",e.id),this.curPathAnimationObj={},this.isPause=!1,this.restartPathAnimation=!1;break;case"play":if(""!=this.$store.state.widget.settingActive)return void this.$message(this.$t("messageTips.exitEditingStatus"));this.curPathAnimationObj.id||(this.curPathAnimationObj=JSON.parse(JSON.stringify(e)),this.setPathAnimation(e)),!this.curPathAnimationObj.id||this.curPathAnimationObj.id&&this.curPathAnimationObj.id===e.id?e.isRunning?(window.scene.mv.tools.pathRoam.pause(),e.isRunning=!1,this.isPause=!0,this.restartPathAnimation=!1):(this.restartPathAnimation?window.scene.mv.tools.pathRoam.restart():this.isPause?window.scene.mv.tools.pathRoam.resume():window.scene.mv.tools.pathRoam.start(),e.isRunning=!0):this.$message(this.$t("messageTips.exitPathRoam"));break;case"stop":var o=this.$store.state.pathAnimation.pathanimationActive;if(!this.curPathAnimationObj.id||this.curPathAnimationObj.id&&this.curPathAnimationObj.id===e.id){if(this.$store.commit("setCurPathanimationId",""),o&&(window.scene.mv.tools.pathRoam.stop(),window.scene.mv.tools.pathRoam.clear()),this.curPathAnimationObj={},this.curPathAnimationId="",e.isRunning=!1,this.isPause=!1,this.pathAnimationStateEvents=!1,window.scene.mv.events.pathroamStateChange.off("change",this.pathAnimationState),this.$store.commit("tooglePathanimationActive",!1),1==window.scene.features.size){var s=scene.features.values().next().value;"model"==s.type&&s._2DMode&&window.scene.mv.controller.rotateTo(0,-Math.PI,!1)}}else this.$message("请先退出当前路径动画!");break;default:break}},pathAnimationState:function(e){1===e&&(this.restartPathAnimation=!0,this.curPathAnimationObj.isRunning=!1,this.pathAnimationStateEvents=!1,window.scene.mv.events.pathroamStateChange.off("change",this.pathAnimationState))}}},J=R,K=Object(p["a"])(J,z,B,!1,null,"0b25d2db",null),H=K.exports,q=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[e.animationGroupsLen?i("div",{staticClass:"bottom-tree-wrapper cus"},[i("div",{staticClass:"bottom-tree"},e._l(e.animationCategoties,(function(t){return i("div",{key:t.key,staticClass:"tree-list"},[t.groups.length?i("div",{staticClass:"list-title item-hover",on:{click:function(i){e.listStates[t.key]=!e.listStates[t.key]}}},[i("el-tooltip",{attrs:{enterable:!1,content:e.listStates[t.key]?e.$t("others.collapse"):e.$t("others.expand"),effect:"dark",placement:"top"}},[e.listStates[t.key]?i("i",{staticClass:"el-icon-caret-top mg-r4"}):i("i",{staticClass:"el-icon-caret-bottom mg-r4"})]),i("div",[i("span",{staticStyle:{"margin-right":"4px"}},[i("CommonSVG",{attrs:{"icon-class":t.icon,size:18}})],1),i("span",{staticStyle:{"padding-left":"4px"}},[e._v(e._s(t.name))])]),t.withAllControl?i("div",{staticClass:"toggleAllAnimation"},[i("el-tooltip",{attrs:{enterable:!1,"open-delay":200,effect:"dark",content:e.animationGroupsAnimating.length?e.$t("animate.tooltip14")+t.name:e.$t("animate.tooltip15")+t.name,placement:"right"}},[i("span",{staticClass:"menu-item"},[i("CommonSVG",{attrs:{size:18,"icon-class":e.animationGroupsAnimating.length?"ani_stop_feature":"ani_play_feature"},nativeOn:{click:function(i){return i.stopPropagation(),e.toggleAllAnimationGroups(t)}}})],1)])],1):e._e()],1):e._e(),i("el-collapse-transition",[i("div",{directives:[{name:"show",rawName:"v-show",value:t.groups&&e.listStates[t.key],expression:"aniCategory.groups && listStates[aniCategory.key]"}],staticClass:"list-content"},e._l(t.groups,(function(t){return i("div",{key:t.groupName,staticClass:"content-item"},[i("div",{staticClass:"item",class:{isActivated:t.groupName===e.$store.state.animation.animationGroupNameInEdit},staticStyle:{"padding-left":"40px"}},[i("div",{staticClass:"item-left"},[i("CommonSVG",{attrs:{"icon-class":"layer",color:"#fff",size:18}}),i("el-tooltip",{attrs:{enterable:!1,"open-delay":500,effect:"dark",content:t.groupName,placement:"top"}},[i("span",[e._v(e._s(t.groupName))])])],1),i("div",{staticClass:"item-right"},[e.isEdit?i("el-tooltip",{attrs:{enterable:!1,"open-delay":500,effect:"dark",content:e.$t("menuIconName.remove"),placement:"top"}},[i("span",{staticClass:"menu-item",staticStyle:{"margin-top":"-2px"}},[i("CommonSVG",{attrs:{size:16,"icon-class":"delete"},nativeOn:{click:function(i){return i.stopPropagation(),e.handleAnimationClick(t,"remove")}}})],1)]):e._e(),e.isEdit?i("el-tooltip",{attrs:{enterable:!1,"open-delay":500,effect:"dark",content:e.$t("menuIconName.setUp"),placement:"top"}},[i("span",{staticClass:"menu-item"},[i("CommonSVG",{attrs:{size:17,"icon-class":"set_up_feature"},nativeOn:{click:function(i){return i.stopPropagation(),e.handleAnimationClick(t,"setting")}}})],1)]):e._e(),i("el-tooltip",{attrs:{enterable:!1,"open-delay":500,effect:"dark",content:t.isSomeAnimating?e.$t("formRelational.animate.stop"):e.$t("formRelational.animate.play"),placement:"top"}},[i("span",{staticClass:"menu-item",staticStyle:{"margin-top":"-2px"}},[i("CommonSVG",{attrs:{size:16,"icon-class":t.isSomeAnimating?"ani_stop_feature":"ani_play_feature"},nativeOn:{click:function(i){return i.stopPropagation(),e.handleAnimationClick(t,"togglePlay")}}})],1)])],1)])])})),0)])],1)})),0)]):e._e()])},W=[],U={name:"animation",data:function(){return{isAnimationTimelineSettled:!1,listStates:{element:!0,camera:!0}}},computed:{animationGroupsAnimating:function(){return this.$store.state.animation.animationGroups.filter((function(e){return!0===e.isElementAnimationGroup&&e.animationList.some((function(e){return!0===e.isAnimating}))}))},isEdit:function(){return this.$store.state.scene.sceneEditMode},animationGroupsLen:function(){return this.$store.state.animation.animationGroups.length},animationCategoties:function(){return[{key:"element",name:this.$t("animate.label16"),icon:"animation_duixiang",groups:this.$store.getters.elementAnimationGroups,withAllControl:!0},{key:"camera",name:this.$t("animate.label17"),icon:"camera_animation_advanced",groups:this.$store.getters.cameraAnimationGroups,withAllControl:!1}]}},components:{CommonSVG:function(){return i.e("chunk-51f90655").then(i.bind(null,"17d0"))}},created:function(){this.$bus.on("AnimationTimeLineSettled",this.onAnimationTimeLineSettled),this.$bus.on("AnimationTimeLineDestroyed",this.onAnimationTimeLineDestroyed)},mounted:function(){this.restoreAnimationStoreFromJSON()},beforeDestroy:function(){this.$bus.off("AnimationTimeLineSettled",this.onAnimationTimeLineSettled),this.$bus.off("AnimationTimeLineDestroyed",this.onAnimationTimeLineDestroyed)},methods:{toggleAllAnimationGroups:function(e){var t=this;"element"===(null===e||void 0===e?void 0:e.key)&&(this.animationGroupsAnimating.length?this.animationGroupsAnimating.forEach((function(e){t.$store.commit("stopAnimationGroup",{group:e})})):e.groups.forEach((function(e){t.$store.commit("playAnimationGroup",{group:e})})))},onAnimationTimeLineSettled:function(){this.isAnimationTimelineSettled=!0},onAnimationTimeLineDestroyed:function(){this.isAnimationTimelineSettled=!1},getTargetFromScene:function(e,t){if(e&&t)return"element"===t?window.scene.findObject(e):window.scene.findFeature(e)},handleAnimationClick:function(e,t){var i=this,n=window.scene,a=e.animationList,o=(a.length,e.groupName);switch(t){case"remove":var s=this.$store.state.animation.animationGroupNameInEdit,r=this.$store.state.animation.isTimeLineVisible;if(r&&s)return void this.$message.warning("当前有处于编辑态的动画组,请先退出");this.$confirm("确定要删除动画组:【".concat(o,"】吗?"),"删除动画组",{confirmButtonText:this.$t("menuIconName.confirm"),cancelButtonText:this.$t("messageTips.cancel"),type:"warning"}).then((function(){i.$store.commit("removeAnimationGroupRecover",[e])})).catch((function(){}));break;case"setting":this.$store.commit("toogleElementMenu",""),this.$store.commit("selectElementData",{});var c=this.$store.state.animation.animationGroupNameInEdit,l=this.$store.state.animation.isTimeLineVisible;if(l&&c)return void this.$message.warning("当前有处于编辑态的动画组,请先退出");if(this.isAnimationTimelineSettled)this.$store.commit("setAnimationSubtype",e.isElementAnimationGroup?"animation_element":"animation_camera"),this.$bus.emit("RestoreAnimation",e);else{var d=this.$watch("isAnimationTimelineSettled",(function(t,i){t&&(this.$store.commit("setAnimationSubtype",e.isElementAnimationGroup?"animation_element":"animation_camera"),this.$bus.emit("RestoreAnimation",e),d())}));this.$store.commit("setIsTimeLineVisible",!0)}break;case"togglePlay":e.animationList.some((function(e){return!0===e.isAnimating}))?this.$store.commit("stopAnimationGroup",{group:e}):this.$store.commit("playAnimationGroup",{group:e}),n.render();break;default:break}},restoreAnimationStoreFromJSON:function(){var e=this,t=window.scene,i=Array.from(t.animations.values()).filter((function(e){return!e.fromFeature})),n=[];if(null!==i&&void 0!==i&&i.length){var a=function(t){var i=t.userData;if(i){var a=i.belongToGroup,o=(i.offsetData,i.zeroTimeInfo,n.find((function(e){return e.groupName===a})));if(!o){var s={groupName:a||"未命名动画组",targetsBakMap:{},animationList:[],isElementAnimationGroup:!1,isSomeAnimating:!1};o=s,n.push(o)}var r=t.rootObjectID,c=t.rootType;o.isElementAnimationGroup="camera"!==r;var l=e.getTargetFromScene(r,"object"===c?"element":"feature");if(l){if(o.isElementAnimationGroup){var d=JSON.parse(l.snap());d.position=l.position,o.targetsBakMap[r]=JSON.stringify(d)}else o.targetsBakMap[r]=JSON.stringify(window.scene.getCameraInfo());o.animationList.push({id:r,type:l.type,isAnimating:!!o.isElementAnimationGroup&&(l.animating||l._animating),animationName:t.name})}}};i.forEach((function(e){a(e)}));var o=this.$store;n.forEach((function(e){o.commit("addOrUpdateAnimationGroups",[{groupName:e.groupName,animationList:e.animationList,targetsBakMap:e.targetsBakMap,isElementAnimationGroup:e.isElementAnimationGroup,isSomeAnimating:e.animationList.some((function(e){return!0===e.isAnimating}))}])}))}}}},X=U,Q=Object(p["a"])(X,q,W,!1,null,null,null),Y=Q.exports,Z=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"tab-content-wrapper"},[i("div",{staticClass:"bottom-tree-wrapper cus"},[i("div",{staticClass:"bottom-tree"},[i("div",{staticClass:"tree-list"},[i("div",{staticClass:"list-content"},e._l(e.triggerList,(function(t,n){return i("div",{key:t.id,staticClass:"content-item"},[i("div",{staticClass:"item"},[i("div",{staticClass:"item-left"},[i("CommonSVG",{attrs:{"icon-class":"layer",color:"#fff",size:18}}),i("el-tooltip",{attrs:{enterable:!1,"open-delay":500,effect:"dark",content:t.name,placement:"top"}},[i("span",[e._v(e._s(t.name))])])],1),i("div",{staticClass:"item-right"},[e.isEdit?i("el-tooltip",{attrs:{enterable:!1,"open-delay":100,effect:"dark",content:e.$t("menuIconName.setUp"),placement:"top"}},[i("span",{staticClass:"menu-item"},[i("CommonSVG",{attrs:{size:20,"icon-class":"set_up_feature"},nativeOn:{click:function(i){return i.stopPropagation(),e.handleTriggerListMenu(t,n,"setting")}}})],1)]):e._e(),e.isEdit?i("el-tooltip",{attrs:{enterable:!1,"open-delay":100,effect:"dark",content:e.$t("menuIconName.remove"),placement:"top"}},[i("span",{staticClass:"menu-item",staticStyle:{"margin-top":"-2px"}},[i("CommonSVG",{attrs:{size:16,"icon-class":"delete"},nativeOn:{click:function(i){return i.stopPropagation(),e.handleTriggerListMenu(t,n,"delete")}}})],1)]):e._e()],1)])])})),0)])])])])},ee=[],te={name:"trigger",props:["compKey"],data:function(){return{}},computed:{isEdit:function(){return this.$store.state.scene.sceneEditMode},triggerList:function(){return this.$store.state.trigger.triggerList}},components:{CommonSVG:function(){return i.e("chunk-51f90655").then(i.bind(null,"17d0"))}},watch:{compKey:{deep:!0,handler:function(e,t){this.setSceneTrigger()}}},mounted:function(){this.setSceneTrigger()},methods:{setSceneTrigger:function(){this.$store.commit("getTriggerList")},handleTriggerListMenu:function(e,t,i){var n=this;switch(i){case"setting":var a=window.scene.triggers.get(e.id);this.$store.commit("toggleActiveDialog","empty"),this.$emit("resetCurrentElementSelected"),this.$store.commit("toogleElementMenu","");var o=this.$store.state.scene.dragOverData;if(""!=this.$store.state.widget.settingActive&&e.id!=o.id)return void this.$message(this.$t("messageTips.exitEditingStatus"));var s=JSON.parse(JSON.parse(JSON.stringify(a)));s.subType=s.type,s.type="trigger","sensor"==s.subType&&(s.object={id:a.id,type:a.type}),this.$store.commit("saveFeatureRawData",JSON.stringify(s)),this.$store.commit("saveDragOverData",s),this.$nextTick((function(){n.$store.commit("toggleSettingActive",s.type)}));break;case"delete":this.$confirm(this.$t("featureSetting.triggerConditions.message3",{name:e.name}),this.$t("messageTips.tipsTitle"),{confirmButtonText:this.$t("menuIconName.confirm"),cancelButtonText:this.$t("messageTips.cancel"),type:"warning"}).then((function(){window.scene.removeTrigger(e.id),n.$store.commit("getTriggerList")})).catch((function(){}));break}}}},ie=te,ne=Object(p["a"])(ie,Z,ee,!1,null,"8956b25c",null),ae=ne.exports,oe=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("div",{staticClass:"top-search"},[i("el-input",{attrs:{size:"small",clearable:"",placeholder:e.$t("others.search")},model:{value:e.searchKeyword,callback:function(t){e.searchKeyword=t},expression:"searchKeyword"}},[i("i",{staticClass:"el-input__icon el-icon-search",attrs:{slot:"prefix"},slot:"prefix"})])],1),i("div",{staticClass:"bottom-tree-wrapper"},[i("div",{staticClass:"setup-content"},e._l(e.markupDatasFiltered,(function(t,n){return i("div",{key:t.id+n,staticClass:"viewpoints-list",on:{click:function(i){return e.restoreData(t)}}},[t.default?i("div",{staticClass:"list-mark"},[e._v(" "+e._s(e.$t("others.default"))+" ")]):e._e(),i("img",{attrs:{src:t.thumbnail,alt:""}}),i("div",{staticClass:"list-bottom"},[i("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",enterable:!1,content:t.name||e.$t("topToolBarMenu.advanced.children.markup.name")+n,placement:"top"}},[i("span",{staticClass:"left-title"},[e._v(" "+e._s(t.name||e.$t("topToolBarMenu.advanced.children.markup.name")+n)+" ")])]),e.isEdit?i("div",{staticClass:"right-menu"},[i("el-tooltip",{attrs:{effect:"dark",content:e.$t("menuIconName.rename"),placement:"top"}},[i("span",{on:{click:function(i){return e.handleListMenu(t,n,"bianji")}}},[i("CommonSVG",{attrs:{color:"#98A2B3","icon-class":"amend_feature",size:16}})],1)]),i("el-tooltip",{attrs:{effect:"dark",content:e.$t("menuIconName.delete"),placement:"top"}},[i("span",{on:{click:function(i){return i.stopPropagation(),e.handleListMenu(t,n,"delete")}}},[i("CommonSVG",{attrs:{color:"#98A2B3","icon-class":"delete",size:16}})],1)])],1):e._e()],1)])})),0)]),e.addFormDialog.dialogState?i("addViewpointMarkupDialog",{attrs:{addType:"markup",isEditName:e.isEditName,inputValue:e.addFormDialog.name},on:{closeAddDialog:e.closeAddDialog,saveData:e.saveData}}):e._e(),i("transition",{attrs:{"enter-active-class":"animate__animated animate__fadeInUp"}},[e.isMarkupComponentVisible?i("MarkupMenu",{on:{saveData:e.onSaveMarkupData}}):e._e()],1)],1)},se=[],re={name:"",data:function(){return{searchKeyword:"",regForEmpty:/^\s*$/,isEditName:!1,addFormDialog:{menuState:!1,dialogState:!1,name:"",inputError:!1,curIndex:""}}},components:{CommonSVG:function(){return i.e("chunk-51f90655").then(i.bind(null,"17d0"))},MarkupMenu:function(){return i.e("chunk-43c5d891").then(i.bind(null,"f45e"))},addViewpointMarkupDialog:N},computed:{bottomMenuActive:function(){return this.$store.state.menuList.bottomMenuActive},isMarkupComponentVisible:function(){return"markup"===this.bottomMenuActive},markupDatas:function(){return this.$store.state.markup.markupDatas},isEdit:function(){return this.$store.state.scene.sceneEditMode},markupDatasFiltered:function(){var e=this.searchKeyword,t=this.regForEmpty,i=this.markupDatas;return!e||t.test(e)?i:i.filter((function(t){return t.name.includes(e)}))}},mounted:function(){this.getSceneViewpoints()},methods:{onSaveMarkupData:function(){this.addFormDialog.dialogState=!0},getSceneViewpoints:function(){var e=[];window.scene.viewpoints.length>0&&(window.scene.viewpoints.forEach((function(t){var i=t.type;"markup"===i&&e.unshift(t)})),this.$store.commit("getSceneMarkupList",e))},restoreData:function(e){window.scene.mv.tools.markup.clear(),window.scene.restoreMarkup(e),window.scene.render()},saveData:function(e){var t=this;return Object(s["a"])(Object(o["a"])().mark((function i(){var n,a,s;return Object(o["a"])().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(n=t.$loading({lock:!0,text:t.$t("others.prepare"),spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),a=null,!t.isEditName){i.next=12;break}return s=t.addFormDialog.curIndex,t.markupDatas[s].name=e.name,n.close(),t.addFormDialog.dialogState=!1,t.addFormDialog.curIndex="",t.addFormDialog.name="",t.getSceneViewpoints(),t.isEditName=!1,i.abrupt("return");case 12:return i.next=14,window.scene.snapMarkup().catch((function(e){n.close(),t.addFormDialog.dialogState=!1}));case 14:a=i.sent,t.$store.commit("toggleBottomMenuActive","markup"),a.name=e.name,a.save(window.scene),t.$notify({title:t.$t("topToolBarMenu.advanced.children.markup.name")+" "+a.name,message:t.$t("others.added"),type:"success"}),window.dispatchEvent(new CustomEvent("markupSaved",{detail:"markupSaved"})),n.close(),t.$store.commit("setActivedType",""),t.getSceneViewpoints(),t.closeAddDialog();case 24:case"end":return i.stop()}}),i)})))()},closeAddDialog:function(){this.addFormDialog.dialogState=!1,this.addFormDialog.name="",this.addFormDialog.curIndex="",this.isEditName=!1,this.$store.commit("setActivedType","")},handleListMenu:function(e,t,i){var n=this;switch(i){case"delete":this.$confirm(this.$t("messageTips.deleteSomething",{name:e.name}),this.$t("messageTips.tipsTitle"),{confirmButtonText:this.$t("menuIconName.confirm"),cancelButtonText:this.$t("messageTips.cancel"),type:"warning"}).then((function(){var i=n.markupDatas;i.splice(t,1),n.$store.commit("getSceneMarkupList",i);var a=window.scene.viewpoints.findIndex((function(t){return t.id==e.id}));window.scene.viewpoints.splice(a,1),n.$message({type:"success",message:n.$t("messageTips.deleteSuccess")})})).catch((function(){}));break;case"bianji":this.addFormDialog.dialogState=!0,this.addFormDialog.name=e.name,this.addFormDialog.curIndex=t,this.isEditName=!0}}}},ce=re,le=Object(p["a"])(ce,oe,se,!1,null,"34d8ee69",null),de=le.exports,me={name:"SceneManageView",components:{filterComponent:f,elementList:E,viewpoint:j,pathAnimation:H,animation:Y,trigger:ae,markup:de,addViewpointMarkupDialog:N},props:["compKey","isShare","isVothingScenemanager"],data:function(){return{dialogWidth:300,dialogLeft:-300,dialogHeight:100,currentViewData:[],addFormDialog:{menuState:!1,dialogState:!1,name:"",inputError:!1,curIndex:""},executingAddHandler:"",isAnimationTimelineMounted:!1,isEditName:!1,menuData:[{label:this.$t("topToolBarMenu.features"),name:"element",show:!0},{label:this.$t("topToolBarMenu.advanced.children.viewpoint.name"),name:"viewpoint",show:!0},{label:this.$t("topToolBarMenu.advanced.children.markup.name"),name:"markup",show:!0},{label:this.$t("topToolBarMenu.advanced.children.animation.name"),name:"animation",show:!0},{label:this.$t("topToolBarMenu.advanced.children.trigger.name"),name:"trigger",show:!0},{label:this.$t("topToolBarMenu.advanced.children.pathAnimation.name"),name:"pathAnimation",show:!0},{label:this.$t("topToolBarMenu.advanced.children.screening.name"),name:"screening",show:!0}]}},created:function(){var e=this;this.dialogLeft=-1*this.dialogWidth,this.changeManageSize(),window.onresize=function(){e.changeManageSize()}},mounted:function(){},computed:{selectedSectionData:function(){return this.$store.state.menuList.selectedSectionData},sectionLoaded:function(){return this.$store.state.menuList.sectionLoaded},onlyPreview:function(){return this.$store.state.scene.onlyPreview},isEdit:function(){return this.$store.state.scene.sceneEditMode},bottomMenuActive:function(){return this.$store.state.menuList.bottomMenuActive},localActiveTab:function(){return this.$store.state.sceneSetting.validActivedType},hideSceneManageMenuOnlyPreview:function(){return this.$store.state.scene.hideSceneManageMenuOnlyPreview}},watch:{"$store.state.scene.sceneEditMode":{handler:function(e){e?this.changeManageSize():this.dialogHeight=100},immediate:!0},hideSceneManageMenuOnlyPreview:function(e){this.setMenuData(e)}},methods:{setMenuData:function(e){this.menuData.forEach((function(t){e.includes(t.name)?t.show=!1:t.show=!0}))},setStructureTree:function(e){this.$emit("setStructureTree",e)},setCurrentViewData:function(e){this.currentViewData[0]=e[0],this.$set(this.currentViewData,1,e[1]),this.$emit("setCurrentViewData",e)},setInteractiveData:function(e){this.$emit("setInteractiveData",e)},changeManageSize:function(){var e=document.body.clientHeight,t=this.$store.state.scene.topToolBarHeight;this.dialogHeight=100-t/(e/100)},getSceneViewpoints:function(){var e=this,t=[],i=[];window.scene.viewpoints.length>0&&window.scene.viewpoints.forEach((function(n){var a=n.type;"markup"==a?(t.unshift(n),e.$store.commit("getSceneMarkupList",t)):"viewpoint"===a&&(i.unshift(n),e.$store.commit("getSceneViewpointList",i))}))},switchDialog:function(){var e=this,t=document.querySelector(".sourceDom"),i=[];null!=t&&(t.style.transition="left 0.6s",setTimeout((function(){t.style.transition=""}),1e3)),null!==i&&void 0!==i&&i.length&&(i.forEach((function(e){e.style.transition="all 0.6s"})),setTimeout((function(){i.forEach((function(e){e.style.transition=""}))}),1e3)),this.$nextTick((function(){e.dialogLeft<0?(e.dialogLeft=0,null!=t&&(t.style.left="".concat(e.dialogWidth+35,"px")),null!==i&&void 0!==i&&i.length&&i.forEach((function(e){e.style.left="300px",e.style.width="calc(100% - 300px)"}))):(e.dialogLeft=-1*e.dialogWidth,null!=t&&(t.style.left="30px"),null!==i&&void 0!==i&&i.length&&i.forEach((function(e){e.style.left=0,e.style.width="100%"})))}))},changeMenuName:function(e,t){this.getIfCanChangeTab()&&(window.offCheckedCoordinate&&window.offCheckedCoordinate(),window.offCheckedCoordinate&&(window.offCheckedCoordinate=null,delete window.offCheckedCoordinate),this.$bus.emit("eventoff"),this.$store.commit("setActivedType",""),"markup"!==e?(this.executingAddHandler="","markup"===this.$store.state.menuList.bottomMenuActive&&this.$store.commit("toggleBottomMenuActive","markup")):"viewpoint"!==e&&(this.addFormDialog.dialogState=!1),"animation"!==e&&(this.$store.commit("setAnimationGroupNameInEdit",null),this.$store.commit("setIsTimeLineVisible",!1)),this.$store.commit("setValidActivedType",e))},getIfCanChangeTab:function(){return!this.$store.state.animation.isTimeLineVisible||(this.$message(this.$t("messageTips.exitEditingAnimate")),!1)},handleAddObject:function(e){var t=this;switch(this.executingAddHandler=null!==e&&void 0!==e?e:"",e){case"markup":this.$store.commit("toggleBottomMenuActive","markup"),this.$store.commit("toggleSettingActive","closeSetting");break;case"viewpoint":this.addFormDialog.dialogState=!0;break;case"animation":if(this.$store.commit("setActivedType",""),"animation"!==this.localActiveTab){this.$store.commit("setValidActivedType","animation"),this.$nextTick((function(){t.handleAddObject("animation")}));break}this.$store.commit("setIsTimeLineVisible",!0)}},saveData:function(e){var t=this;return Object(s["a"])(Object(o["a"])().mark((function i(){var n,a,s,r,c,l;return Object(o["a"])().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(n=t.$loading({lock:!0,text:t.$t("others.prepare"),spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),a=t.localActiveTab,"markup"!==a&&"viewpoint"!==a){i.next=45;break}if(s=null,r="","markup"!==a){i.next=23;break}if(r=t.$t("topToolBarMenu.advanced.children.markup.name"),!t.isEditName){i.next=17;break}return c=t.addFormDialog.curIndex,t.markupDatas[c].name=e.name,n.close(),t.addFormDialog.dialogState=!1,t.addFormDialog.curIndex="",t.addFormDialog.name="",t.getSceneViewpoints(),t.isEditName=!1,i.abrupt("return");case 17:return i.next=19,window.scene.snapMarkup().catch((function(e){n.close(),t.addFormDialog.dialogState=!1}));case 19:s=i.sent,t.$store.commit("toggleBottomMenuActive","markup"),i.next=37;break;case 23:if(r=t.$t("topToolBarMenu.advanced.children.viewpoint.name"),!t.isEditName){i.next=34;break}return l=t.addFormDialog.curIndex,t.viewpointDatas[l].name=e.name,n.close(),t.addFormDialog.dialogState=!1,t.addFormDialog.curIndex="",t.addFormDialog.name="",t.isEditName=!1,t.getSceneViewpoints(),i.abrupt("return");case 34:return i.next=36,window.scene.snapViewpoint().catch((function(e){n.close(),t.addFormDialog.dialogState=!1}));case 36:s=i.sent;case 37:s.name=e.name,s.save(window.scene),t.$notify({title:r+" "+s.name,message:t.$t("others.added"),type:"success"}),n.close(),t.$store.commit("setActivedType",""),t.getSceneViewpoints(),i.next=50;break;case 45:i.t0=a,i.next=("animation"===i.t0||"trigger"===i.t0||"pathAnimation"===i.t0||i.t0,48);break;case 48:return i.abrupt("break",49);case 49:n.close();case 50:t.closeAddDialog();case 51:case"end":return i.stop()}}),i)})))()},closeAddDialog:function(){this.addFormDialog.dialogState=!1,this.addFormDialog.name="",this.addFormDialog.curIndex="",this.isEditName=!1,this.$store.commit("setActivedType","")},dragChangeViewWidth:function(){var e=this;document.body.style.cursor="col-resize",document.getElementById("renderDom").style.pointerEvents="none";var t=document.body.clientWidth/2;document.onmousemove=function(i){var n=i.clientX;n<300&&(n=300),n>t&&(n=t),e.dialogWidth=n},document.onmouseup=function(){document.onmousemove=null,document.onmouseup=null,document.body.style.cursor="",document.getElementById("renderDom").style.pointerEvents=""}}}},ue=me,pe=(i("49da"),Object(p["a"])(ue,n,a,!1,null,null,null));t["default"]=pe.exports}}]);