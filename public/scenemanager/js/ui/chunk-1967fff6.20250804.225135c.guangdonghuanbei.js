(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1967fff6"],{"63b8":function(e,t,s){"use strict";s("7450")},7450:function(e,t,s){},ecf1:function(e,t,s){"use strict";s.r(t);var a=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("dialogComp",{attrs:{hideHeader:!1,needClose:"true",zIndex:"4",draw:!0,right:10,drag:!0,title:e.$t("sceneMainMenu.statefulElements.name"),icon:"icon-details",width:360,minWidth:340,dragTopOffset:e.dragTopOffset,height:"70%",type:"detailInfo",top:e.attrTop},on:{close:e.closeDialog},scopedSlots:e._u([{key:"center",fn:function(){return[s("div",{staticClass:"stateful-container"},[s("div",{staticClass:"menu-tabs"},e._l(e.tabs,(function(t,a){return s("span",{key:t.type,class:{active:e.activeTab==a},on:{click:function(t){return e.toggleTabs(a)}}},[e._v(" "+e._s(t.title)+" ")])})),0),e._l(e.tabs,(function(t,a){return s("div",{directives:[{name:"show",rawName:"v-show",value:e.activeTab==a,expression:"activeTab==index"}],key:t.type,staticClass:"content"},[0===t.elementDatas.length?s("div",{staticClass:"text-center"},[e._v(" "+e._s(e.$t("others.emptyData"))+" ")]):[s("div",{staticClass:"list-content virtually-list-content",on:{scroll:function(t){return e.updateVisibleList(a)}}},[s("div",{staticClass:"item t-header"},[s("span",{staticClass:"item-checkbox"},[s("el-checkbox",{attrs:{indeterminate:t.isIndeterminate},on:{change:e.handleCheckAllChange},model:{value:t.checkAll,callback:function(s){e.$set(t,"checkAll",s)},expression:"li.checkAll"}})],1),s("span",{staticClass:"item-text"},[e._v(" "+e._s(e.$t("dialog.attribute.table.label3"))+" ")]),s("span",{staticClass:"item-text"},[e._v(" "+e._s(e.$t("dialog.attribute.table.label2"))+" ")])]),s("div",{staticClass:"virtually-item-content"},e._l(t.visibleList,(function(t){return s("div",{key:t.id,staticClass:"item"},[s("span",{staticClass:"item-checkbox"},[s("el-checkbox",{on:{change:function(s){return e.handleCheckedElementChange(t)}},model:{value:t.checked,callback:function(s){e.$set(t,"checked",s)},expression:"item.checked"}})],1),s("span",{staticClass:"item-text",attrs:{title:t.id}},[e._v(e._s(t.id))]),s("span",{staticClass:"item-text",attrs:{title:t.name}},[e._v(e._s(t.name))])])})),0)]),s("div",{staticClass:"bottom-menu"},[s("div",[e._v(e._s(e.$t("formRelational.element.message",{num:t.elementDatas.length})))]),s("div",[1==a?s("span",{staticClass:"cursor-btn",on:{click:function(t){return e.handlerCheckedElementsEvents("selected")}}},[e._v(" "+e._s(e.$t("menuIconName.selected"))+" ")]):e._e(),1==a?s("span",{staticClass:"cursor-btn margin-left-15",on:{click:function(t){return e.handlerCheckedElementsEvents("zoom")}}},[e._v(" "+e._s(e.$t("menuIconName.zoom"))+" ")]):e._e(),s("span",{staticClass:"cursor-btn margin-left-15",on:{click:function(t){return e.handlerCheckedElementsEvents("reset")}}},[e._v(" "+e._s(e.$t("menuIconName.reset1"))+" ")])])])]],2)}))],2)]},proxy:!0}])})},i=[],n=(s("d3b7"),s("159b"),s("c740"),s("a434"),s("b0c0"),s("fb6a"),s("ed08"),{name:"StatefulElements",data:function(){return{tabs:[{title:this.$t("dialog.statefulElements.label"),type:"hide",checkAll:!1,isIndeterminate:!1,elementDatas:[],checkedElements:[],visibleList:[]},{title:this.$t("dialog.statefulElements.label1"),type:"color",checkAll:!1,isIndeterminate:!1,elementDatas:[],checkedElements:[],visibleList:[]}],activeTab:0,attrHeight:500,attrTop:170}},created:function(){this.setDialogSize(),this.setStatefulElementsDatas(),this.$bus.on("onSetStatefulElementsDatas",this.setStatefulElementsDatas)},computed:{dragTopOffset:function(){return this.$store.state.scene.topToolBarHeight}},methods:{handlerCheckedElementsEvents:function(e){var t=this;if(0!=this.tabs[this.activeTab].checkedElements.length){var s=window.scene.createArrayObject(this.tabs[this.activeTab].checkedElements);switch(e){case"selected":s.select(),this.$store.commit("toogleElementMenu","model");break;case"zoom":window.scene.fit(this.tabs[this.activeTab].checkedElements);break;case"reset":0==this.activeTab?s.show():1==this.activeTab&&s.resetColor(),this.tabs[this.activeTab].checkedElements.forEach((function(e){var s=t.tabs[t.activeTab].elementDatas.findIndex((function(t){return t.id==e}));t.tabs[t.activeTab].elementDatas.splice(s,1),t.tabs[t.activeTab].visibleList.splice(s,1)})),this.tabs[this.activeTab].checkedElements=[],this.tabs[this.activeTab].checkAll=!1,this.tabs[this.activeTab].isIndeterminate=!1,this.$nextTick((function(){t.updateVisibleList(t.activeTab)}));break}setTimeout((function(){window.scene.render()}),500)}else this.$message.error(this.$t("messageTips.checkOne"))},handleCheckedElementChange:function(e){var t=this,s=this.tabs[this.activeTab].checkedElements.indexOf(e.id);s<0?this.tabs[this.activeTab].checkedElements.push(e.id):this.tabs[this.activeTab].checkedElements.splice(s,1),this.$nextTick((function(){t.tabs[t.activeTab].isIndeterminate=t.tabs[t.activeTab].checkedElements.length>0&&t.tabs[t.activeTab].checkedElements.length<t.tabs[t.activeTab].elementDatas.length,t.tabs[t.activeTab].checkAll=t.tabs[t.activeTab].checkedElements.length===t.tabs[t.activeTab].elementDatas.length}))},handleCheckAllChange:function(e){var t=this;e?this.tabs[this.activeTab].elementDatas.forEach((function(e){t.tabs[t.activeTab].checkedElements.push(e.id),e.checked=!0})):(this.tabs[this.activeTab].checkedElements=[],this.tabs[this.activeTab].elementDatas.forEach((function(e){e.checked=!1}))),this.tabs[this.activeTab].isIndeterminate=!1},setStatefulElementsDatas:function(){var e=this;window.scene.hiddenObjects.size>0?window.scene.hiddenObjects.forEach((function(t){var s=e.tabs[0].elementDatas.findIndex((function(e){return e.id===t.id}));s<0&&e.tabs[0].elementDatas.push({checked:!1,id:t.id,name:t.name})})):this.tabs[0].elementDatas=[],window.scene.coloredObjects.size>0?window.scene.coloredObjects.forEach((function(t){var s=e.tabs[1].elementDatas.findIndex((function(e){return e.id===t.id}));s<0&&e.tabs[1].elementDatas.push({checked:!1,id:t.id,name:t.name})})):this.tabs[1].elementDatas=[],this.$nextTick((function(){e.updateVisibleList(e.activeTab)}))},updateVisibleList:function(e){var t=document.querySelectorAll(".stateful-container .content")[e].querySelector(".virtually-list-content"),s=document.querySelectorAll(".stateful-container .content")[e].querySelector(".virtually-item-content"),a=document.querySelectorAll(".stateful-container .content")[e].querySelector(".t-header");if(t&&s&&a){var i=Math.floor(t.scrollTop/40),n=i+Math.ceil(t.clientHeight/40)+4;this.tabs[e].visibleList=this.tabs[e].elementDatas.slice(i,n),s.style.top="".concat(40*i+40,"px"),a.style.top="".concat(t.scrollTop,"px")}},toggleTabs:function(e){var t=this;this.activeTab=e,this.$nextTick((function(){t.updateVisibleList(e)}))},closeDialog:function(){this.$store.commit("toggleActiveDialog","statefulElements"),this.$store.commit("toggleMenuActive","statefulElements")},setDialogSize:function(){document.body.clientHeight;var e=document.querySelector(".desktop-view").clientHeight;this.attrTop=0==e?50:e+10}},beforeDestroy:function(){this.$bus.off("onSetStatefulElementsDatas",this.setStatefulElementsDatas)}}),c=n,l=(s("63b8"),s("2877")),o=Object(l["a"])(c,a,i,!1,null,"5083a9dc",null);t["default"]=o.exports}}]);