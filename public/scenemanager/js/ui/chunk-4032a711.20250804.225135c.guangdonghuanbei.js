(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4032a711"],{"23bf":function(t,i,e){},"28fd":function(t,i,e){"use strict";e("23bf")},9585:function(t,i,e){"use strict";e.r(i);var n=function(){var t=this,i=t.$createElement,e=t._self._c||i;return e("dialogComp",{staticClass:"defaultViewPointDom",attrs:{position:"fixed",hideHeader:!1,zIndex:"5",right:t.dialogRight,drag:!1,title:t.$t("topToolBarMenu.advanced.children.viewpoint.name"),icon:"icon-details",width:t.dialogWidth,height:"100%",top:0,type:"detailInfo",dragTopOffset:t.dragTopOffset},on:{close:t.closeDialog},scopedSlots:t._u([{key:"center",fn:function(){return[e("div",{staticClass:"setup-content viewpoints-content"},[t._l(t.viewpointDatas,(function(i,n){return e("div",{key:i.id+n,staticClass:"viewpoints-list",on:{click:function(e){return t.restoreData(i)}}},[e("img",{attrs:{src:i.thumbnail,alt:""}}),e("div",{staticClass:"list-bottom"},[e("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",enterable:!1,content:i.name||t.$t("topToolBarMenu.advanced.children.viewpoint.name")+n,placement:"top"}},[e("span",{staticClass:"left-title"},[t._v(" "+t._s(i.name||t.$t("topToolBarMenu.advanced.children.viewpoint.name")+n)+" ")])])],1)])})),0==t.viewpointDatas.length?e("div",{staticClass:"text-center margin-top-15 "},[t._v(" 暂无数据 ")]):t._e()],2),e("div",{staticClass:"switch-btn",on:{click:function(i){return t.switchDialog()}}},[t.dialogRight>=0?e("i",{staticClass:"el-icon-caret-right el-icon"}):e("i",{staticClass:"el-icon-caret-left el-icon"})])]},proxy:!0}])})},o=[],a=(e("d3b7"),e("159b"),{data:function(){return{viewpointDatas:[],dialogRight:-300,dialogWidth:300}},computed:{dragTopOffset:function(){return this.$store.state.scene.topToolBarHeight}},created:function(){this.dialogRight=-1*this.dialogWidth,this.getSceneViewpoints()},mounted:function(){var t=this;this.$nextTick((function(){t.switchDialog()}))},methods:{switchDialog:function(){var t=this;this.$nextTick((function(){t.dialogRight<0?t.dialogRight=0:t.dialogRight=-1*t.dialogWidth}))},restoreData:function(t){window.scene.mv.tools.markup.clear(),window.scene.restoreViewpoint(t),window.scene.render()},getSceneViewpoints:function(){var t=this;this.viewpointDatas=[],window.scene.viewpoints.length>0&&window.scene.viewpoints.forEach((function(i){"viewpoint"===i.type&&t.viewpointDatas.unshift(i)}))}}}),s=a,c=(e("28fd"),e("2877")),l=Object(c["a"])(s,n,o,!1,null,"b62f3098",null);i["default"]=l.exports}}]);