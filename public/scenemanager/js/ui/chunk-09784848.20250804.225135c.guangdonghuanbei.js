(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-09784848"],{"0bfb":function(t,n,e){"use strict";function r(t){var n,e=this,r=!1;return function(){return r||(r=!0,n=t.apply(e,arguments)),n}}e.d(n,"a",(function(){return r}))},1569:function(t,n,e){"use strict";function r(t,n){if(!t)throw new Error(n?"Assertion failed ("+n+")":"Assertion Failed")}e.d(n,"a",(function(){return r}))},"1b0e":function(t,n,e){"use strict";e.d(n,"b",(function(){return u})),e.d(n,"a",(function(){return f}));var r=function(){function t(t,n,e,r){this.originalStart=t,this.originalLength=n,this.modifiedStart=e,this.modifiedLength=r}return t.prototype.getOriginalEnd=function(){return this.originalStart+this.originalLength},t.prototype.getModifiedEnd=function(){return this.modifiedStart+this.modifiedLength},t}(),i=e("eda7"),o=function(){function t(t){this.source=t}return t.prototype.getElements=function(){for(var t=this.source,n=new Int32Array(t.length),e=0,r=t.length;e<r;e++)n[e]=t.charCodeAt(e);return n},t}();function u(t,n,e){return new f(new o(t),new o(n)).ComputeDiff(e).changes}var a=function(){function t(){}return t.Assert=function(t,n){if(!t)throw new Error(n)},t}(),s=function(){function t(){}return t.Copy=function(t,n,e,r,i){for(var o=0;o<i;o++)e[r+o]=t[n+o]},t.Copy2=function(t,n,e,r,i){for(var o=0;o<i;o++)e[r+o]=t[n+o]},t}(),c=function(){function t(){this.m_changes=[],this.m_originalStart=1073741824,this.m_modifiedStart=1073741824,this.m_originalCount=0,this.m_modifiedCount=0}return t.prototype.MarkNextChange=function(){(this.m_originalCount>0||this.m_modifiedCount>0)&&this.m_changes.push(new r(this.m_originalStart,this.m_originalCount,this.m_modifiedStart,this.m_modifiedCount)),this.m_originalCount=0,this.m_modifiedCount=0,this.m_originalStart=1073741824,this.m_modifiedStart=1073741824},t.prototype.AddOriginalElement=function(t,n){this.m_originalStart=Math.min(this.m_originalStart,t),this.m_modifiedStart=Math.min(this.m_modifiedStart,n),this.m_originalCount++},t.prototype.AddModifiedElement=function(t,n){this.m_originalStart=Math.min(this.m_originalStart,t),this.m_modifiedStart=Math.min(this.m_modifiedStart,n),this.m_modifiedCount++},t.prototype.getChanges=function(){return(this.m_originalCount>0||this.m_modifiedCount>0)&&this.MarkNextChange(),this.m_changes},t.prototype.getReverseChanges=function(){return(this.m_originalCount>0||this.m_modifiedCount>0)&&this.MarkNextChange(),this.m_changes.reverse(),this.m_changes},t}(),f=function(){function t(n,e,r){void 0===r&&(r=null),this.ContinueProcessingPredicate=r;var i=t._getElements(n),o=i[0],u=i[1],a=i[2],s=t._getElements(e),c=s[0],f=s[1],l=s[2];this._hasStrings=a&&l,this._originalStringElements=o,this._originalElementsOrHash=u,this._modifiedStringElements=c,this._modifiedElementsOrHash=f,this.m_forwardHistory=[],this.m_reverseHistory=[]}return t._isStringArray=function(t){return t.length>0&&"string"===typeof t[0]},t._getElements=function(n){var e=n.getElements();if(t._isStringArray(e)){for(var r=new Int32Array(e.length),o=0,u=e.length;o<u;o++)r[o]=Object(i["b"])(e[o],0);return[e,r,!0]}return e instanceof Int32Array?[[],e,!1]:[[],new Int32Array(e),!1]},t.prototype.ElementsAreEqual=function(t,n){return this._originalElementsOrHash[t]===this._modifiedElementsOrHash[n]&&(!this._hasStrings||this._originalStringElements[t]===this._modifiedStringElements[n])},t.prototype.OriginalElementsAreEqual=function(t,n){return this._originalElementsOrHash[t]===this._originalElementsOrHash[n]&&(!this._hasStrings||this._originalStringElements[t]===this._originalStringElements[n])},t.prototype.ModifiedElementsAreEqual=function(t,n){return this._modifiedElementsOrHash[t]===this._modifiedElementsOrHash[n]&&(!this._hasStrings||this._modifiedStringElements[t]===this._modifiedStringElements[n])},t.prototype.ComputeDiff=function(t){return this._ComputeDiff(0,this._originalElementsOrHash.length-1,0,this._modifiedElementsOrHash.length-1,t)},t.prototype._ComputeDiff=function(t,n,e,r,i){var o=[!1],u=this.ComputeDiffRecursive(t,n,e,r,o);return i&&(u=this.PrettifyChanges(u)),{quitEarly:o[0],changes:u}},t.prototype.ComputeDiffRecursive=function(t,n,e,i,o){o[0]=!1;while(t<=n&&e<=i&&this.ElementsAreEqual(t,e))t++,e++;while(n>=t&&i>=e&&this.ElementsAreEqual(n,i))n--,i--;if(t>n||e>i){var u=void 0;return e<=i?(a.Assert(t===n+1,"originalStart should only be one more than originalEnd"),u=[new r(t,0,e,i-e+1)]):t<=n?(a.Assert(e===i+1,"modifiedStart should only be one more than modifiedEnd"),u=[new r(t,n-t+1,e,0)]):(a.Assert(t===n+1,"originalStart should only be one more than originalEnd"),a.Assert(e===i+1,"modifiedStart should only be one more than modifiedEnd"),u=[]),u}var s=[0],c=[0],f=this.ComputeRecursionPoint(t,n,e,i,s,c,o),l=s[0],h=c[0];if(null!==f)return f;if(!o[0]){var d=this.ComputeDiffRecursive(t,l,e,h,o),p=[];return p=o[0]?[new r(l+1,n-(l+1)+1,h+1,i-(h+1)+1)]:this.ComputeDiffRecursive(l+1,n,h+1,i,o),this.ConcatenateChanges(d,p)}return[new r(t,n-t+1,e,i-e+1)]},t.prototype.WALKTRACE=function(t,n,e,i,o,u,a,s,f,l,h,d,p,g,v,m,_,y){var b=null,w=null,C=new c,A=n,E=e,k=p[0]-m[0]-i,L=-1073741824,S=this.m_forwardHistory.length-1;do{var x=k+t;x===A||x<E&&f[x-1]<f[x+1]?(h=f[x+1],g=h-k-i,h<L&&C.MarkNextChange(),L=h,C.AddModifiedElement(h+1,g),k=x+1-t):(h=f[x-1]+1,g=h-k-i,h<L&&C.MarkNextChange(),L=h-1,C.AddOriginalElement(h,g+1),k=x-1-t),S>=0&&(f=this.m_forwardHistory[S],t=f[0],A=1,E=f.length-1)}while(--S>=-1);if(b=C.getReverseChanges(),y[0]){var O=p[0]+1,M=m[0]+1;if(null!==b&&b.length>0){var P=b[b.length-1];O=Math.max(O,P.getOriginalEnd()),M=Math.max(M,P.getModifiedEnd())}w=[new r(O,d-O+1,M,v-M+1)]}else{C=new c,A=u,E=a,k=p[0]-m[0]-s,L=1073741824,S=_?this.m_reverseHistory.length-1:this.m_reverseHistory.length-2;do{x=k+o;x===A||x<E&&l[x-1]>=l[x+1]?(h=l[x+1]-1,g=h-k-s,h>L&&C.MarkNextChange(),L=h+1,C.AddOriginalElement(h+1,g+1),k=x+1-o):(h=l[x-1],g=h-k-s,h>L&&C.MarkNextChange(),L=h,C.AddModifiedElement(h+1,g+1),k=x-1-o),S>=0&&(l=this.m_reverseHistory[S],o=l[0],A=1,E=l.length-1)}while(--S>=-1);w=C.getChanges()}return this.ConcatenateChanges(b,w)},t.prototype.ComputeRecursionPoint=function(t,n,e,i,o,u,a){var c=0,f=0,l=0,h=0,d=0,p=0;t--,e--,o[0]=0,u[0]=0,this.m_forwardHistory=[],this.m_reverseHistory=[];var g=n-t+(i-e),v=g+1,m=new Int32Array(v),_=new Int32Array(v),y=i-e,b=n-t,w=t-e,C=n-i,A=b-y,E=A%2===0;m[y]=t,_[b]=n,a[0]=!1;for(var k=1;k<=g/2+1;k++){var L=0,S=0;l=this.ClipDiagonalBound(y-k,k,y,v),h=this.ClipDiagonalBound(y+k,k,y,v);for(var x=l;x<=h;x+=2){c=x===l||x<h&&m[x-1]<m[x+1]?m[x+1]:m[x-1]+1,f=c-(x-y)-w;var O=c;while(c<n&&f<i&&this.ElementsAreEqual(c+1,f+1))c++,f++;if(m[x]=c,c+f>L+S&&(L=c,S=f),!E&&Math.abs(x-b)<=k-1&&c>=_[x])return o[0]=c,u[0]=f,O<=_[x]&&k<=1448?this.WALKTRACE(y,l,h,w,b,d,p,C,m,_,c,n,o,f,i,u,E,a):null}var M=(L-t+(S-e)-k)/2;if(null!==this.ContinueProcessingPredicate&&!this.ContinueProcessingPredicate(L,M))return a[0]=!0,o[0]=L,u[0]=S,M>0&&k<=1448?this.WALKTRACE(y,l,h,w,b,d,p,C,m,_,c,n,o,f,i,u,E,a):(t++,e++,[new r(t,n-t+1,e,i-e+1)]);d=this.ClipDiagonalBound(b-k,k,b,v),p=this.ClipDiagonalBound(b+k,k,b,v);for(x=d;x<=p;x+=2){c=x===d||x<p&&_[x-1]>=_[x+1]?_[x+1]-1:_[x-1],f=c-(x-b)-C;O=c;while(c>t&&f>e&&this.ElementsAreEqual(c,f))c--,f--;if(_[x]=c,E&&Math.abs(x-y)<=k&&c<=m[x])return o[0]=c,u[0]=f,O>=m[x]&&k<=1448?this.WALKTRACE(y,l,h,w,b,d,p,C,m,_,c,n,o,f,i,u,E,a):null}if(k<=1447){var P=new Int32Array(h-l+2);P[0]=y-l+1,s.Copy2(m,l,P,1,h-l+1),this.m_forwardHistory.push(P),P=new Int32Array(p-d+2),P[0]=b-d+1,s.Copy2(_,d,P,1,p-d+1),this.m_reverseHistory.push(P)}}return this.WALKTRACE(y,l,h,w,b,d,p,C,m,_,c,n,o,f,i,u,E,a)},t.prototype.PrettifyChanges=function(t){for(var n=0;n<t.length;n++){var e=t[n],r=n<t.length-1?t[n+1].originalStart:this._originalElementsOrHash.length,i=n<t.length-1?t[n+1].modifiedStart:this._modifiedElementsOrHash.length,o=e.originalLength>0,u=e.modifiedLength>0;while(e.originalStart+e.originalLength<r&&e.modifiedStart+e.modifiedLength<i&&(!o||this.OriginalElementsAreEqual(e.originalStart,e.originalStart+e.originalLength))&&(!u||this.ModifiedElementsAreEqual(e.modifiedStart,e.modifiedStart+e.modifiedLength)))e.originalStart++,e.modifiedStart++;var a=[null];n<t.length-1&&this.ChangesOverlap(t[n],t[n+1],a)&&(t[n]=a[0],t.splice(n+1,1),n--)}for(n=t.length-1;n>=0;n--){e=t[n],r=0,i=0;if(n>0){var s=t[n-1];s.originalLength>0&&(r=s.originalStart+s.originalLength),s.modifiedLength>0&&(i=s.modifiedStart+s.modifiedLength)}o=e.originalLength>0,u=e.modifiedLength>0;for(var c=0,f=this._boundaryScore(e.originalStart,e.originalLength,e.modifiedStart,e.modifiedLength),l=1;;l++){var h=e.originalStart-l,d=e.modifiedStart-l;if(h<r||d<i)break;if(o&&!this.OriginalElementsAreEqual(h,h+e.originalLength))break;if(u&&!this.ModifiedElementsAreEqual(d,d+e.modifiedLength))break;var p=this._boundaryScore(h,e.originalLength,d,e.modifiedLength);p>f&&(f=p,c=l)}e.originalStart-=c,e.modifiedStart-=c}return t},t.prototype._OriginalIsBoundary=function(t){return t<=0||t>=this._originalElementsOrHash.length-1||this._hasStrings&&/^\s*$/.test(this._originalStringElements[t])},t.prototype._OriginalRegionIsBoundary=function(t,n){if(this._OriginalIsBoundary(t)||this._OriginalIsBoundary(t-1))return!0;if(n>0){var e=t+n;if(this._OriginalIsBoundary(e-1)||this._OriginalIsBoundary(e))return!0}return!1},t.prototype._ModifiedIsBoundary=function(t){return t<=0||t>=this._modifiedElementsOrHash.length-1||this._hasStrings&&/^\s*$/.test(this._modifiedStringElements[t])},t.prototype._ModifiedRegionIsBoundary=function(t,n){if(this._ModifiedIsBoundary(t)||this._ModifiedIsBoundary(t-1))return!0;if(n>0){var e=t+n;if(this._ModifiedIsBoundary(e-1)||this._ModifiedIsBoundary(e))return!0}return!1},t.prototype._boundaryScore=function(t,n,e,r){var i=this._OriginalRegionIsBoundary(t,n)?1:0,o=this._ModifiedRegionIsBoundary(e,r)?1:0;return i+o},t.prototype.ConcatenateChanges=function(t,n){var e=[];if(0===t.length||0===n.length)return n.length>0?n:t;if(this.ChangesOverlap(t[t.length-1],n[0],e)){var r=new Array(t.length+n.length-1);return s.Copy(t,0,r,0,t.length-1),r[t.length-1]=e[0],s.Copy(n,1,r,t.length,n.length-1),r}r=new Array(t.length+n.length);return s.Copy(t,0,r,0,t.length),s.Copy(n,0,r,t.length,n.length),r},t.prototype.ChangesOverlap=function(t,n,e){if(a.Assert(t.originalStart<=n.originalStart,"Left change is not less than or equal to right change"),a.Assert(t.modifiedStart<=n.modifiedStart,"Left change is not less than or equal to right change"),t.originalStart+t.originalLength>=n.originalStart||t.modifiedStart+t.modifiedLength>=n.modifiedStart){var i=t.originalStart,o=t.originalLength,u=t.modifiedStart,s=t.modifiedLength;return t.originalStart+t.originalLength>=n.originalStart&&(o=n.originalStart+n.originalLength-t.originalStart),t.modifiedStart+t.modifiedLength>=n.modifiedStart&&(s=n.modifiedStart+n.modifiedLength-t.modifiedStart),e[0]=new r(i,o,u,s),!0}return e[0]=null,!1},t.prototype.ClipDiagonalBound=function(t,n,e,r){if(t>=0&&t<r)return t;var i=e,o=r-e-1,u=n%2===0;if(t<0){var a=i%2===0;return u===a?0:1}var s=o%2===0;return u===s?r-1:r-2},t}()},2504:function(t,n,e){"use strict";e.d(n,"a",(function(){return r})),e.d(n,"b",(function(){return a}));var r,i=e("308f"),o=Object.freeze((function(t,n){var e=setTimeout(t.bind(n),0);return{dispose:function(){clearTimeout(e)}}}));(function(t){function n(n){return n===t.None||n===t.Cancelled||(n instanceof u||!(!n||"object"!==typeof n)&&("boolean"===typeof n.isCancellationRequested&&"function"===typeof n.onCancellationRequested))}t.isCancellationToken=n,t.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:i["b"].None}),t.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:o})})(r||(r={}));var u=function(){function t(){this._isCancelled=!1,this._emitter=null}return t.prototype.cancel=function(){this._isCancelled||(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))},Object.defineProperty(t.prototype,"isCancellationRequested",{get:function(){return this._isCancelled},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"onCancellationRequested",{get:function(){return this._isCancelled?o:(this._emitter||(this._emitter=new i["a"]),this._emitter.event)},enumerable:!0,configurable:!0}),t.prototype.dispose=function(){this._emitter&&(this._emitter.dispose(),this._emitter=null)},t}(),a=function(){function t(t){this._token=void 0,this._parentListener=void 0,this._parentListener=t&&t.onCancellationRequested(this.cancel,this)}return Object.defineProperty(t.prototype,"token",{get:function(){return this._token||(this._token=new u),this._token},enumerable:!0,configurable:!0}),t.prototype.cancel=function(){this._token?this._token instanceof u&&this._token.cancel():this._token=r.Cancelled},t.prototype.dispose=function(t){void 0===t&&(t=!1),t&&this.cancel(),this._parentListener&&this._parentListener.dispose(),this._token?this._token instanceof u&&this._token.dispose():this._token=r.None},t}()},"308f":function(t,n,e){"use strict";e.d(n,"b",(function(){return r})),e.d(n,"a",(function(){return l})),e.d(n,"e",(function(){return h})),e.d(n,"d",(function(){return d})),e.d(n,"c",(function(){return p})),e.d(n,"f",(function(){return g}));var r,i=e("fdcc"),o=e("0bfb"),u=e("a666"),a=e("db88"),s=function(){var t=function(n,e){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,n){t.__proto__=n}||function(t,n){for(var e in n)n.hasOwnProperty(e)&&(t[e]=n[e])},t(n,e)};return function(n,e){function r(){this.constructor=n}t(n,e),n.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}}();(function(t){function n(t){return function(n,e,r){void 0===e&&(e=null);var i,o=!1;return i=t((function(t){if(!o)return i?i.dispose():o=!0,n.call(e,t)}),null,r),o&&i.dispose(),i}}function e(t,n){return c((function(e,r,i){return void 0===r&&(r=null),t((function(t){return e.call(r,n(t))}),null,i)}))}function r(t,n){return c((function(e,r,i){return void 0===r&&(r=null),t((function(t){n(t),e.call(r,t)}),null,i)}))}function i(t,n){return c((function(e,r,i){return void 0===r&&(r=null),t((function(t){return n(t)&&e.call(r,t)}),null,i)}))}function o(t){return t}function a(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return function(n,e,r){return void 0===e&&(e=null),u["e"].apply(void 0,t.map((function(t){return t((function(t){return n.call(e,t)}),null,r)})))}}function s(t,n,r){var i=r;return e(t,(function(t){return i=n(i,t),i}))}function c(t){var n,e=new l({onFirstListenerAdd:function(){n=t(e.fire,e)},onLastListenerRemove:function(){n.dispose()}});return e.event}function f(t,n,e,r,i){var o;void 0===e&&(e=100),void 0===r&&(r=!1);var u=void 0,a=void 0,s=0,c=new l({leakWarningThreshold:i,onFirstListenerAdd:function(){o=t((function(t){s++,u=n(u,t),r&&!a&&(c.fire(u),u=void 0),clearTimeout(a),a=setTimeout((function(){var t=u;u=void 0,a=void 0,(!r||s>1)&&c.fire(t),s=0}),e)}))},onLastListenerRemove:function(){o.dispose()}});return c.event}function h(t){var r=(new Date).getTime();return e(n(t),(function(t){return(new Date).getTime()-r}))}function d(t){var n,e=!0;return i(t,(function(t){var r=e||t!==n;return e=!1,n=t,r}))}function p(t,n,e){void 0===n&&(n=!1),void 0===e&&(e=[]);var r=e.slice(),i=t((function(t){r?r.push(t):u.fire(t)})),o=function(){r&&r.forEach((function(t){return u.fire(t)})),r=null},u=new l({onFirstListenerAdd:function(){i||(i=t((function(t){return u.fire(t)})))},onFirstListenerDidAdd:function(){r&&(n?setTimeout(o):o())},onLastListenerRemove:function(){i&&i.dispose(),i=null}});return u.event}t.None=function(){return u["a"].None},t.once=n,t.map=e,t.forEach=r,t.filter=i,t.signal=o,t.any=a,t.reduce=s,t.snapshot=c,t.debounce=f,t.stopwatch=h,t.latch=d,t.buffer=p;var g=function(){function t(t){this.event=t}return t.prototype.map=function(n){return new t(e(this.event,n))},t.prototype.forEach=function(n){return new t(r(this.event,n))},t.prototype.filter=function(n){return new t(i(this.event,n))},t.prototype.reduce=function(n,e){return new t(s(this.event,n,e))},t.prototype.latch=function(){return new t(d(this.event))},t.prototype.debounce=function(n,e,r,i){return void 0===e&&(e=100),void 0===r&&(r=!1),new t(f(this.event,n,e,r,i))},t.prototype.on=function(t,n,e){return this.event(t,n,e)},t.prototype.once=function(t,e,r){return n(this.event)(t,e,r)},t}();function v(t){return new g(t)}function m(t,n,e){void 0===e&&(e=function(t){return t});var r=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return u.fire(e.apply(void 0,t))},i=function(){return t.on(n,r)},o=function(){return t.removeListener(n,r)},u=new l({onFirstListenerAdd:i,onLastListenerRemove:o});return u.event}function _(t,n,e){void 0===e&&(e=function(t){return t});var r=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return u.fire(e.apply(void 0,t))},i=function(){return t.addEventListener(n,r)},o=function(){return t.removeEventListener(n,r)},u=new l({onFirstListenerAdd:i,onLastListenerRemove:o});return u.event}function y(t){var n=new l,e=!1;return t.then(void 0,(function(){return null})).then((function(){e?n.fire(void 0):setTimeout((function(){return n.fire(void 0)}),0)})),e=!0,n.event}function b(t){return new Promise((function(e){return n(t)(e)}))}t.chain=v,t.fromNodeEventEmitter=m,t.fromDOMEventEmitter=_,t.fromPromise=y,t.toPromise=b})(r||(r={}));var c=-1,f=function(){function t(t,n){void 0===n&&(n=Math.random().toString(18).slice(2,5)),this.customThreshold=t,this.name=n,this._warnCountdown=0}return t.prototype.dispose=function(){this._stacks&&this._stacks.clear()},t.prototype.check=function(t){var n=this,e=c;if("number"===typeof this.customThreshold&&(e=this.customThreshold),!(e<=0||t<e)){this._stacks||(this._stacks=new Map);var r=(new Error).stack.split("\n").slice(3).join("\n"),i=this._stacks.get(r)||0;if(this._stacks.set(r,i+1),this._warnCountdown-=1,this._warnCountdown<=0){var o;this._warnCountdown=.5*e;var u=0;this._stacks.forEach((function(t,n){(!o||u<t)&&(o=n,u=t)})),console.warn("["+this.name+"] potential listener LEAK detected, having "+t+" listeners already. MOST frequent listener ("+u+"):"),console.warn(o)}return function(){var t=n._stacks.get(r)||0;n._stacks.set(r,t-1)}}},t}(),l=function(){function t(t){this._disposed=!1,this._options=t,this._leakageMon=c>0?new f(this._options&&this._options.leakWarningThreshold):void 0}return Object.defineProperty(t.prototype,"event",{get:function(){var n=this;return this._event||(this._event=function(e,r,i){n._listeners||(n._listeners=new a["a"]);var o=n._listeners.isEmpty();o&&n._options&&n._options.onFirstListenerAdd&&n._options.onFirstListenerAdd(n);var s,c,f=n._listeners.push(r?[e,r]:e);return o&&n._options&&n._options.onFirstListenerDidAdd&&n._options.onFirstListenerDidAdd(n),n._options&&n._options.onListenerDidAdd&&n._options.onListenerDidAdd(n,e,r),n._leakageMon&&(s=n._leakageMon.check(n._listeners.size)),c={dispose:function(){if(s&&s(),c.dispose=t._noop,!n._disposed&&(f(),n._options&&n._options.onLastListenerRemove)){var e=n._listeners&&!n._listeners.isEmpty();e||n._options.onLastListenerRemove(n)}}},i instanceof u["b"]?i.add(c):Array.isArray(i)&&i.push(c),c}),this._event},enumerable:!0,configurable:!0}),t.prototype.fire=function(t){if(this._listeners){this._deliveryQueue||(this._deliveryQueue=new a["a"]);for(var n=this._listeners.iterator(),e=n.next();!e.done;e=n.next())this._deliveryQueue.push([e.value,t]);while(this._deliveryQueue.size>0){var r=this._deliveryQueue.shift(),o=r[0],u=r[1];try{"function"===typeof o?o.call(void 0,u):o[0].call(o[1],u)}catch(e){Object(i["e"])(e)}}}},t.prototype.dispose=function(){this._listeners&&this._listeners.clear(),this._deliveryQueue&&this._deliveryQueue.clear(),this._leakageMon&&this._leakageMon.dispose(),this._disposed=!0},t._noop=function(){},t}(),h=function(t){function n(n){var e=t.call(this,n)||this;return e._isPaused=0,e._eventQueue=new a["a"],e._mergeFn=n&&n.merge,e}return s(n,t),n.prototype.pause=function(){this._isPaused++},n.prototype.resume=function(){if(0!==this._isPaused&&0===--this._isPaused)if(this._mergeFn){var n=this._eventQueue.toArray();this._eventQueue.clear(),t.prototype.fire.call(this,this._mergeFn(n))}else while(!this._isPaused&&0!==this._eventQueue.size)t.prototype.fire.call(this,this._eventQueue.shift())},n.prototype.fire=function(n){this._listeners&&(0!==this._isPaused?this._eventQueue.push(n):t.prototype.fire.call(this,n))},n}(l),d=function(){function t(){var t=this;this.hasListeners=!1,this.events=[],this.emitter=new l({onFirstListenerAdd:function(){return t.onFirstListenerAdd()},onLastListenerRemove:function(){return t.onLastListenerRemove()}})}return Object.defineProperty(t.prototype,"event",{get:function(){return this.emitter.event},enumerable:!0,configurable:!0}),t.prototype.add=function(t){var n=this,e={event:t,listener:null};this.events.push(e),this.hasListeners&&this.hook(e);var r=function(){n.hasListeners&&n.unhook(e);var t=n.events.indexOf(e);n.events.splice(t,1)};return Object(u["h"])(Object(o["a"])(r))},t.prototype.onFirstListenerAdd=function(){var t=this;this.hasListeners=!0,this.events.forEach((function(n){return t.hook(n)}))},t.prototype.onLastListenerRemove=function(){var t=this;this.hasListeners=!1,this.events.forEach((function(n){return t.unhook(n)}))},t.prototype.hook=function(t){var n=this;t.listener=t.event((function(t){return n.emitter.fire(t)}))},t.prototype.unhook=function(t){t.listener&&t.listener.dispose(),t.listener=null},t.prototype.dispose=function(){this.emitter.dispose()},t}(),p=function(){function t(){this.buffers=[]}return t.prototype.wrapEvent=function(t){var n=this;return function(e,r,i){return t((function(t){var i=n.buffers[n.buffers.length-1];i?i.push((function(){return e.call(r,t)})):e.call(r,t)}),void 0,i)}},t.prototype.bufferEvents=function(t){var n=[];this.buffers.push(n);var e=t();return this.buffers.pop(),n.forEach((function(t){return t()})),e},t}(),g=function(){function t(){var t=this;this.listening=!1,this.inputEvent=r.None,this.inputEventListener=u["a"].None,this.emitter=new l({onFirstListenerDidAdd:function(){t.listening=!0,t.inputEventListener=t.inputEvent(t.emitter.fire,t.emitter)},onLastListenerRemove:function(){t.listening=!1,t.inputEventListener.dispose()}}),this.event=this.emitter.event}return Object.defineProperty(t.prototype,"input",{set:function(t){this.inputEvent=t,this.listening&&(this.inputEventListener.dispose(),this.inputEventListener=t(this.emitter.fire,this.emitter))},enumerable:!0,configurable:!0}),t.prototype.dispose=function(){this.inputEventListener.dispose(),this.emitter.dispose()},t}()},"3d37":function(t,n,e){"use strict";e.d(n,"c",(function(){return o})),e.d(n,"a",(function(){return u})),e.d(n,"b",(function(){return a}));var r=e("3742"),i=e("32b8");function o(t){return t.replace(/[\\/]/g,i["posix"].sep)}function u(t,n,e,o){if(void 0===o&&(o=i["sep"]),t===n)return!0;if(!t||!n)return!1;if(n.length>t.length)return!1;if(e){var u=Object(r["O"])(t,n);if(!u)return!1;if(n.length===t.length)return!0;var a=n.length;return n.charAt(n.length-1)===o&&a--,t.charAt(a)===o}return n.charAt(n.length-1)!==o&&(n+=o),0===t.indexOf(n)}function a(t){return t>=65&&t<=90||t>=97&&t<=122}},"42fe":function(t,n,e){"use strict";var r;let i;e.d(n,"a",(function(){return o})),function(){function t(n,e,i){function o(a,s){if(!e[a]){if(!n[a]){var c="function"==typeof r&&r;if(!s&&c)return r(a,!0);if(u)return u(a,!0);var f=new Error("Cannot find module '"+a+"'");throw f.code="MODULE_NOT_FOUND",f}var l=e[a]={exports:{}};n[a][0].call(l.exports,(function(t){var e=n[a][1][t];return o(e||t)}),l,l.exports,t,n,e,i)}return e[a].exports}for(var u="function"==typeof r&&r,a=0;a<i.length;a++)o(i[a]);return o}return t}()({1:[function(t,n,e){var r=t("./toMap"),i=["background","base","cite","href","longdesc","src","usemap"];n.exports={uris:r(i)}},{"./toMap":10}],2:[function(t,n,e){var r={allowedAttributes:{"*":["title","accesskey"],a:["href","name","target","aria-label"],iframe:["allowfullscreen","frameborder","src"],img:["src","alt","title","aria-label"]},allowedClasses:{},allowedSchemes:["http","https","mailto"],allowedTags:["a","abbr","article","b","blockquote","br","caption","code","del","details","div","em","h1","h2","h3","h4","h5","h6","hr","i","img","ins","kbd","li","main","mark","ol","p","pre","section","span","strike","strong","sub","summary","sup","table","tbody","td","th","thead","tr","u","ul"],filter:null};n.exports=r},{}],3:[function(t,n,e){var r=t("./toMap"),i=["area","br","col","hr","img","wbr","input","base","basefont","link","meta"];n.exports={voids:r(i)}},{"./toMap":10}],4:[function(t,n,e){t("he");var r=t("assignment"),o=t("./parser"),u=t("./sanitizer"),a=t("./defaults");function s(t,n,e){var i=[],s=!0===e?n:r({},a,n),c=u(i,s);return o(t,c),i.join("")}s.defaults=a,n.exports=s,i=s},{"./defaults":2,"./parser":7,"./sanitizer":8,assignment:6,he:9}],5:[function(t,n,e){n.exports=function(t){return"string"===typeof t?t.toLowerCase():t}},{}],6:[function(t,n,e){function r(t){var n,e,i=Array.prototype.slice.call(arguments,1);while(i.length)for(e in n=i.shift(),n)n.hasOwnProperty(e)&&("[object Object]"===Object.prototype.toString.call(t[e])?t[e]=r(t[e],n[e]):t[e]=n[e]);return t}n.exports=r},{}],7:[function(t,n,e){var r=t("he"),i=t("./lowercase"),o=(t("./attributes"),t("./elements")),u=/^<\s*([\w:-]+)((?:\s+[\w:-]+(?:\s*=\s*(?:(?:"[^"]*")|(?:'[^']*')|[^>\s]+))?)*)\s*(\/?)\s*>/,a=/^<\s*\/\s*([\w:-]+)[^>]*>/,s=/([\w:-]+)(?:\s*=\s*(?:(?:"((?:[^"])*)")|(?:'((?:[^'])*)')|([^>\s]+)))?/g,c=/^</,f=/^<\s*\//;function l(){var t=[];return t.lastItem=function(){return t[t.length-1]},t}function h(t,n){var e,h=l(),d=t;while(t)p();function p(){e=!0,g();var n=t===d;d=t,n&&(t="")}function g(){"\x3c!--"===t.substr(0,4)?m():f.test(t)?v(a,b):c.test(t)&&v(u,y),_()}function v(n,r){var i=t.match(n);i&&(t=t.substring(i[0].length),i[0].replace(n,r),e=!1)}function m(){var r=t.indexOf("--\x3e");r>=0&&(n.comment&&n.comment(t.substring(4,r)),t=t.substring(r+3),e=!1)}function _(){if(e){var r,i=t.indexOf("<");i>=0?(r=t.substring(0,i),t=t.substring(i)):(r=t,t=""),n.chars&&n.chars(r)}}function y(t,e,u,a){var c={},f=i(e),l=o.voids[f]||!!a;function d(t,n,e,i,o){c[n]=void 0===e&&void 0===i&&void 0===o?void 0:r.decode(e||i||o||"")}u.replace(s,d),l||h.push(f),n.start&&n.start(f,c,l)}function b(t,e){var r,o=0,u=i(e);if(u)for(o=h.length-1;o>=0;o--)if(h[o]===u)break;if(o>=0){for(r=h.length-1;r>=o;r--)n.end&&n.end(h[r]);h.length=o}}b()}n.exports=h},{"./attributes":1,"./elements":3,"./lowercase":5,he:9}],8:[function(t,n,e){var r=t("he"),i=t("./lowercase"),o=t("./attributes"),u=t("./elements");function a(t,n){var e,a=n||{};return g(),{start:c,end:f,chars:h};function s(n){t.push(n)}function c(t,n,u){var c=i(t);function f(t){var e,u=n[t],f=(a.allowedClasses||{})[c]||[],h=(a.allowedAttributes||{})[c]||[];h=h.concat((a.allowedAttributes||{})["*"]||[]);var d=i(t);function p(t){return f&&-1!==f.indexOf(t)}"class"===d&&-1===h.indexOf(d)?(u=u.split(" ").filter(p).join(" ").trim(),e=u.length):e=-1!==h.indexOf(d)&&(!0!==o.uris[d]||l(u)),e&&(s(" "),s(t),"string"===typeof u&&(s('="'),s(r.encode(u)),s('"')))}e.ignoring?d(c):-1!==(a.allowedTags||[]).indexOf(c)&&(!a.filter||a.filter({tag:c,attrs:n}))?(s("<"),s(c),Object.keys(n).forEach(f),s(u?"/>":">")):d(c)}function f(t){var n=i(t),r=-1!==(a.allowedTags||[]).indexOf(n);r&&!1===e.ignoring?(s("</"),s(n),s(">")):p(n)}function l(t){var n=t[0];if("#"===n||"/"===n)return!0;var e=t.indexOf(":");if(-1===e)return!0;var r=t.indexOf("?");if(-1!==r&&e>r)return!0;var i=t.indexOf("#");return-1!==i&&e>i||a.allowedSchemes.some(o);function o(n){return 0===t.indexOf(n+":")}}function h(t){!1===e.ignoring&&s(a.transformText?a.transformText(t):t)}function d(t){u.voids[t]||(!1===e.ignoring?e={ignoring:t,depth:1}:e.ignoring===t&&e.depth++)}function p(t){e.ignoring===t&&--e.depth<=0&&g()}function g(){e={ignoring:!1,depth:0}}}n.exports=a},{"./attributes":1,"./elements":3,"./lowercase":5,he:9}],9:[function(t,n,e){var r={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},i={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"},o=/(&amp;|&lt;|&gt;|&quot;|&#39;)/g,u=/[&<>"']/g;function a(t){return r[t]}function s(t){return i[t]}function c(t){return null==t?"":String(t).replace(u,a)}function f(t){return null==t?"":String(t).replace(o,s)}c.options=f.options={},n.exports={encode:c,escape:c,decode:f,unescape:f,version:"1.0.0-browser"}},{}],10:[function(t,n,e){function r(t){return t.reduce(i,{})}function i(t,n){return t[n]=!0,t}n.exports=r},{}]},{},[4]);var o=i},4828:function(t,n,e){"use strict";e.d(n,"a",(function(){return i}));var r=e("258a"),i=function(){function t(t,n){void 0===t&&(t=[]),void 0===n&&(n=10),this._initialize(t),this._limit=n,this._onChange()}return t.prototype.add=function(t){this._history.delete(t),this._history.add(t),this._onChange()},t.prototype.next=function(){return this._navigator.next()},t.prototype.previous=function(){return this._navigator.previous()},t.prototype.current=function(){return this._navigator.current()},t.prototype.parent=function(){return null},t.prototype.first=function(){return this._navigator.first()},t.prototype.last=function(){return this._navigator.last()},t.prototype.has=function(t){return this._history.has(t)},t.prototype._onChange=function(){this._reduceToLimit();var t=this._elements;this._navigator=new r["b"](t,0,t.length,t.length)},t.prototype._reduceToLimit=function(){var t=this._elements;t.length>this._limit&&this._initialize(t.slice(t.length-this._limit))},t.prototype._initialize=function(t){this._history=new Set;for(var n=0,e=t;n<e.length;n++){var r=e[n];this._history.add(r)}},Object.defineProperty(t.prototype,"_elements",{get:function(){var t=[];return this._history.forEach((function(n){return t.push(n)})),t},enumerable:!0,configurable:!0}),t}()},"561a":function(t,n,e){"use strict";e.d(n,"a",(function(){return i})),e.d(n,"b",(function(){return u})),e.d(n,"c",(function(){return s}));var r=/(\\)?\$\([a-z0-9\-]+?(?:~[a-z0-9\-]*?)?\)/gi;function i(t){return t.replace(r,(function(t,n){return n?t:"\\"+t}))}var o=/\\\$\([a-z0-9\-]+?(?:~[a-z0-9\-]*?)?\)/gi;function u(t){return t.replace(o,(function(t){return"\\"+t}))}var a=/(\\)?\$\((([a-z0-9\-]+?)(?:~([a-z0-9\-]*?))?)\)/gi;function s(t){return t.replace(a,(function(t,n,e,r,i){return n?"$("+e+")":'<span class="codicon codicon-'+r+(i?" codicon-animation-"+i:"")+'"></span>'}))}},"5fe7":function(t,n,e){"use strict";e.d(n,"i",(function(){return u})),e.d(n,"f",(function(){return a})),e.d(n,"j",(function(){return s})),e.d(n,"a",(function(){return c})),e.d(n,"l",(function(){return f})),e.d(n,"g",(function(){return l})),e.d(n,"h",(function(){return h})),e.d(n,"e",(function(){return p})),e.d(n,"c",(function(){return g})),e.d(n,"d",(function(){return v})),e.d(n,"k",(function(){return d})),e.d(n,"b",(function(){return m}));var r=e("2504"),i=e("fdcc"),o=e("a666");function u(t){return t&&"function"===typeof t.then}function a(t){var n=new r["b"],e=t(n.token),o=new Promise((function(t,r){n.token.onCancellationRequested((function(){r(i["a"]())})),Promise.resolve(e).then((function(e){n.dispose(),t(e)}),(function(t){n.dispose(),r(t)}))}));return new(function(){function t(){}return t.prototype.cancel=function(){n.cancel()},t.prototype.then=function(t,n){return o.then(t,n)},t.prototype.catch=function(t){return this.then(void 0,t)},t.prototype.finally=function(t){return o.finally(t)},t}())}function s(t,n,e){return Promise.race([t,new Promise((function(t){return n.onCancellationRequested((function(){return t(e)}))}))])}var c=function(){function t(t){this.defaultDelay=t,this.timeout=null,this.completionPromise=null,this.doResolve=null,this.doReject=null,this.task=null}return t.prototype.trigger=function(t,n){var e=this;return void 0===n&&(n=this.defaultDelay),this.task=t,this.cancelTimeout(),this.completionPromise||(this.completionPromise=new Promise((function(t,n){e.doResolve=t,e.doReject=n})).then((function(){if(e.completionPromise=null,e.doResolve=null,e.task){var t=e.task;return e.task=null,t()}}))),this.timeout=setTimeout((function(){e.timeout=null,e.doResolve&&e.doResolve(null)}),n),this.completionPromise},t.prototype.isTriggered=function(){return null!==this.timeout},t.prototype.cancel=function(){this.cancelTimeout(),this.completionPromise&&(this.doReject&&this.doReject(i["a"]()),this.completionPromise=null)},t.prototype.cancelTimeout=function(){null!==this.timeout&&(clearTimeout(this.timeout),this.timeout=null)},t.prototype.dispose=function(){this.cancelTimeout()},t}();function f(t,n){return n?new Promise((function(e,r){var o=setTimeout(e,t);n.onCancellationRequested((function(){clearTimeout(o),r(i["a"]())}))})):a((function(n){return f(t,n)}))}function l(t,n){void 0===n&&(n=0);var e=setTimeout(t,n);return Object(o["h"])((function(){return clearTimeout(e)}))}function h(t,n,e){void 0===n&&(n=function(t){return!!t}),void 0===e&&(e=null);var r=0,i=t.length,o=function(){if(r>=i)return Promise.resolve(e);var u=t[r++],a=Promise.resolve(u());return a.then((function(t){return n(t)?Promise.resolve(t):o()}))};return o()}var d,p=function(){function t(t,n){this._token=-1,"function"===typeof t&&"number"===typeof n&&this.setIfNotSet(t,n)}return t.prototype.dispose=function(){this.cancel()},t.prototype.cancel=function(){-1!==this._token&&(clearTimeout(this._token),this._token=-1)},t.prototype.cancelAndSet=function(t,n){var e=this;this.cancel(),this._token=setTimeout((function(){e._token=-1,t()}),n)},t.prototype.setIfNotSet=function(t,n){var e=this;-1===this._token&&(this._token=setTimeout((function(){e._token=-1,t()}),n))},t}(),g=function(){function t(){this._token=-1}return t.prototype.dispose=function(){this.cancel()},t.prototype.cancel=function(){-1!==this._token&&(clearInterval(this._token),this._token=-1)},t.prototype.cancelAndSet=function(t,n){this.cancel(),this._token=setInterval((function(){t()}),n)},t}(),v=function(){function t(t,n){this.timeoutToken=-1,this.runner=t,this.timeout=n,this.timeoutHandler=this.onTimeout.bind(this)}return t.prototype.dispose=function(){this.cancel(),this.runner=null},t.prototype.cancel=function(){this.isScheduled()&&(clearTimeout(this.timeoutToken),this.timeoutToken=-1)},t.prototype.schedule=function(t){void 0===t&&(t=this.timeout),this.cancel(),this.timeoutToken=setTimeout(this.timeoutHandler,t)},t.prototype.isScheduled=function(){return-1!==this.timeoutToken},t.prototype.onTimeout=function(){this.timeoutToken=-1,this.runner&&this.doRun()},t.prototype.doRun=function(){this.runner&&this.runner()},t}();(function(){if("function"!==typeof requestIdleCallback||"function"!==typeof cancelIdleCallback){var t=Object.freeze({didTimeout:!0,timeRemaining:function(){return 15}});d=function(n){var e=setTimeout((function(){return n(t)})),r=!1;return{dispose:function(){r||(r=!0,clearTimeout(e))}}}}else d=function(t,n){var e=requestIdleCallback(t,"number"===typeof n?{timeout:n}:void 0),r=!1;return{dispose:function(){r||(r=!0,cancelIdleCallback(e))}}}})();var m=function(){function t(t){var n=this;this._didRun=!1,this._executor=function(){try{n._value=t()}catch(e){n._error=e}finally{n._didRun=!0}},this._handle=d((function(){return n._executor()}))}return t.prototype.dispose=function(){this._handle.dispose()},t.prototype.getValue=function(){if(this._didRun||(this._handle.dispose(),this._executor()),this._error)throw this._error;return this._value},t}()},6424:function(t,n,e){"use strict";e.d(n,"a",(function(){return o}));var r=0;function i(){var t="$memoize"+r++,n=void 0,e=function(e,r,i){var o=null,u=null;if("function"===typeof i.value?(o="value",u=i.value,0!==u.length&&console.warn("Memoize should only be used in functions with zero parameters")):"function"===typeof i.get&&(o="get",u=i.get),!u)throw new Error("not supported");var a=t+":"+r;i[o]=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return n=this,this.hasOwnProperty(a)||Object.defineProperty(this,a,{configurable:!0,enumerable:!1,writable:!0,value:u.apply(this,t)}),this[a]}};return e.clear=function(){"undefined"!==typeof n&&Object.getOwnPropertyNames(n).forEach((function(e){0===e.indexOf(t)&&delete n[e]}))},e}function o(t,n,e){return i()(t,n,e)}},"78bc":function(t,n,e){"use strict";e.d(n,"a",(function(){return o})),e.d(n,"b",(function(){return u})),e.d(n,"c",(function(){return s})),e.d(n,"e",(function(){return f})),e.d(n,"d",(function(){return l}));var r=e("e8e3"),i=e("561a"),o=function(){function t(t,n){var e,r;void 0===t&&(t=""),void 0===n&&(n=!1),this._value=t,"boolean"===typeof n?(this._isTrusted=n,this._supportThemeIcons=!1):(this._isTrusted=null!==(e=n.isTrusted)&&void 0!==e&&e,this._supportThemeIcons=null!==(r=n.supportThemeIcons)&&void 0!==r&&r)}return Object.defineProperty(t.prototype,"value",{get:function(){return this._value},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"isTrusted",{get:function(){return this._isTrusted},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"supportThemeIcons",{get:function(){return this._supportThemeIcons},enumerable:!0,configurable:!0}),t.prototype.appendText=function(t){return this._value+=(this._supportThemeIcons?Object(i["a"])(t):t).replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&").replace("\n","\n\n"),this},t.prototype.appendMarkdown=function(t){return this._value+=t,this},t.prototype.appendCodeblock=function(t,n){return this._value+="\n```",this._value+=t,this._value+="\n",this._value+=n,this._value+="\n```\n",this},t}();function u(t){return a(t)?!t.value:!Array.isArray(t)||t.every(u)}function a(t){return t instanceof o||!(!t||"object"!==typeof t)&&("string"===typeof t.value&&("boolean"===typeof t.isTrusted||void 0===t.isTrusted)&&("boolean"===typeof t.supportThemeIcons||void 0===t.supportThemeIcons))}function s(t,n){return!t&&!n||!(!t||!n)&&(Array.isArray(t)&&Array.isArray(n)?Object(r["g"])(t,n,c):!(!a(t)||!a(n))&&c(t,n))}function c(t,n){return t===n||!(!t||!n)&&(t.value===n.value&&t.isTrusted===n.isTrusted&&t.supportThemeIcons===n.supportThemeIcons)}function f(t){return t?t.replace(/\\([\\`*_{}[\]()#+\-.!])/g,"$1"):t}function l(t){var n=[],e=t.split("|").map((function(t){return t.trim()}));t=e[0];var r=e[1];if(r){var i=/height=(\d+)/.exec(r),o=/width=(\d+)/.exec(r),u=i?i[1]:"",a=o?o[1]:"",s=isFinite(parseInt(a)),c=isFinite(parseInt(u));s&&n.push('width="'+a+'"'),c&&n.push('height="'+u+'"')}return{href:t,dimensions:n}}},"7e93":function(t,n,e){"use strict";e.d(n,"g",(function(){return u})),e.d(n,"f",(function(){return x})),e.d(n,"b",(function(){return O})),e.d(n,"c",(function(){return M})),e.d(n,"a",(function(){return T})),e.d(n,"d",(function(){return G})),e.d(n,"e",(function(){return Y}));var r=e("4035"),i=e("3742");function o(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return function(n,e){for(var r=0,i=t.length;r<i;r++){var o=t[r](n,e);if(o)return o}return null}}var u=a.bind(void 0,!0);function a(t,n,e){return!e||e.length<n.length?null:(r=t?i["O"](e,n):0===e.indexOf(n),r?n.length>0?[{start:0,end:n.length}]:[]:null);var r}function s(t,n){var e=n.toLowerCase().indexOf(t.toLowerCase());return-1===e?null:[{start:e,end:e+t.length}]}function c(t,n){return f(t.toLowerCase(),n.toLowerCase(),0,0)}function f(t,n,e,r){if(e===t.length)return[];if(r===n.length)return null;if(t[e]===n[r]){var i=null;return(i=f(t,n,e+1,r+1))?m({start:r,end:r+1},i):null}return f(t,n,e,r+1)}function l(t){return 97<=t&&t<=122}function h(t){return 65<=t&&t<=90}function d(t){return 48<=t&&t<=57}function p(t){return 32===t||9===t||10===t||13===t}var g=new Set;function v(t){return l(t)||h(t)||d(t)}function m(t,n){return 0===n.length?n=[t]:t.end===n[0].start?n[0].start=t.start:n.unshift(t),n}function _(t,n){for(var e=n;e<t.length;e++){var r=t.charCodeAt(e);if(h(r)||d(r)||e>0&&!v(t.charCodeAt(e-1)))return e}return t.length}function y(t,n,e,r){if(e===t.length)return[];if(r===n.length)return null;if(t[e]!==n[r].toLowerCase())return null;var i=null,o=r+1;i=y(t,n,e+1,r+1);while(!i&&(o=_(n,o))<n.length)i=y(t,n,e+1,o),o++;return null===i?null:m({start:r,end:r+1},i)}function b(t){for(var n=0,e=0,r=0,i=0,o=0,u=0;u<t.length;u++)o=t.charCodeAt(u),h(o)&&n++,l(o)&&e++,v(o)&&r++,d(o)&&i++;var a=n/t.length,s=e/t.length,c=r/t.length,f=i/t.length;return{upperPercent:a,lowerPercent:s,alphaPercent:c,numericPercent:f}}function w(t){var n=t.upperPercent,e=t.lowerPercent;return 0===e&&n>.6}function C(t){var n=t.upperPercent,e=t.lowerPercent,r=t.alphaPercent,i=t.numericPercent;return e>.2&&n<.8&&r>.6&&i<.2}function A(t){for(var n=0,e=0,r=0,i=0,o=0;o<t.length;o++)r=t.charCodeAt(o),h(r)&&n++,l(r)&&e++,p(r)&&i++;return 0!==n&&0!==e||0!==i?n<=5:t.length<=30}function E(t,n){if(!n)return null;if(n=n.trim(),0===n.length)return null;if(!A(t))return null;if(n.length>60)return null;var e=b(n);if(!C(e)){if(!w(e))return null;n=n.toLowerCase()}var r=null,i=0;t=t.toLowerCase();while(i<n.length&&null===(r=y(t,n,0,i)))i=_(n,i+1);return r}"`~!@#$%^&*()-=+[{]}\\|;:'\",.<>/?".split("").forEach((function(t){return g.add(t.charCodeAt(0))}));var k=o(u,E,s),L=o(u,E,c),S=new r["a"](1e4);function x(t,n,e){if(void 0===e&&(e=!1),"string"!==typeof t||"string"!==typeof n)return null;var r=S.get(t);r||(r=new RegExp(i["k"](t),"i"),S.set(t,r));var o=r.exec(n);return o?[{start:o.index,end:o.index+o[0].length}]:e?L(t,n):k(t,n)}function O(t,n,e,r,i,o){var u=G(t,n,0,r,i,0,!0);if(u)return u;for(var a=0,s=0,c=o,f=0;f<n.length&&f<P;++f){var l=i.indexOf(n.charAt(f),c);if(l>=0)s+=1,a+=Math.pow(2,l),c=l+1;else if(0!==a)break}return[s,a,o]}function M(t){if("undefined"===typeof t)return[];for(var n=t[1].toString(2),e=t[2],r=[],i=e;i<P;i++)if("1"===n[n.length-(i+1)]){var o=r[r.length-1];o&&o.end===i?o.end=i+1:r.push({start:i,end:i+1})}return r}var P=128;function R(){for(var t=[],n=[0],e=1;e<=P;e++)n.push(-e);for(e=0;e<=P;e++){var r=n.slice(0);r[0]=-e,t.push(r)}return t}var T,j=R(),I=R(),B=R(),D=!1;function q(t,n,e,r,i){function o(t,n,e){void 0===e&&(e=" ");while(t.length<n)t=e+t;return t}for(var u=" |   |"+r.split("").map((function(t){return o(t,3)})).join("|")+"\n",a=0;a<=e;a++)u+=0===a?" |":n[a-1]+"|",u+=t[a].slice(0,i+1).map((function(t){return o(t.toString(),3)})).join("|")+"\n";return u}function F(t,n,e,r){t=t.substr(n),e=e.substr(r),console.log(q(j,t,t.length,e,e.length)),console.log(q(B,t,t.length,e,e.length)),console.log(q(I,t,t.length,e,e.length))}function H(t,n){if(n<0||n>=t.length)return!1;var e=t.charCodeAt(n);switch(e){case 95:case 45:case 46:case 32:case 47:case 92:case 39:case 34:case 58:case 36:return!0;default:return!1}}function z(t,n){if(n<0||n>=t.length)return!1;var e=t.charCodeAt(n);switch(e){case 32:case 9:return!0;default:return!1}}function N(t,n,e){return n[t]!==e[t]}function $(t,n,e,r,i,o){while(n<e&&i<o)t[n]===r[i]&&(n+=1),i+=1;return n===e}function G(t,n,e,r,i,o,u){var a=t.length>P?P:t.length,s=r.length>P?P:r.length;if(!(e>=a||o>=s||a-e>s-o)&&$(n,e,a,i,o,s)){var c=1,f=1,l=e,h=o;for(c=1,l=e;l<a;c++,l++)for(f=1,h=o;h<s;f++,h++){var d=Q(t,n,l,e,r,i,h);I[c][f]=d;var p=j[c-1][f-1]+(d>1?1:d),g=j[c-1][f]+-1,v=j[c][f-1]+-1;v>=g?v>p?(j[c][f]=v,B[c][f]=4):v===p?(j[c][f]=v,B[c][f]=6):(j[c][f]=p,B[c][f]=2):g>p?(j[c][f]=g,B[c][f]=1):g===p?(j[c][f]=g,B[c][f]=3):(j[c][f]=p,B[c][f]=2)}if(D&&F(t,e,r,o),W=0,U=-100,J=o,V=u,X(c-1,f-1,a===s?1:0,0,!1),0!==W)return[U,K,o]}}function Q(t,n,e,r,i,o,u){return n[e]!==o[u]?-1:u===e-r?t[e]===i[u]?7:5:!N(u,i,o)||0!==u&&N(u-1,i,o)?!H(o,u)||0!==u&&H(o,u-1)?H(o,u-1)||z(o,u-1)?5:1:5:t[e]===i[u]?7:5}(function(t){function n(t){return!t||-100===t[0]&&0===t[1]&&0===t[2]}t.Default=Object.freeze([-100,0,0]),t.isDefault=n})(T||(T={}));var W=0,K=0,U=0,J=0,V=!1;function X(t,n,e,r,i){if(!(W>=10||e<-25)){var o=0;while(t>0&&n>0){var u=I[t][n],a=B[t][n];if(4===a)n-=1,i?e-=5:0!==r&&(e-=1),i=!1,o=0;else{if(!(2&a))return;if(4&a&&X(t,n-1,0!==r?e-1:e,r,i),e+=u,t-=1,n-=1,i=!0,r+=Math.pow(2,n+J),1===u){if(o+=1,0===t&&!V)return}else e+=1+o*(u-1),o=0}}e-=n>=3?9:3*n,W+=1,e>U&&(U=e,K=r)}}function Y(t,n,e,r,i,o,u){return Z(t,n,e,r,i,o,!0,u)}function Z(t,n,e,r,i,o,u,a){var s=G(t,n,e,r,i,o,a);if(s&&!u)return s;if(t.length>=3)for(var c=Math.min(7,t.length-1),f=e+1;f<c;f++){var l=tt(t,f);if(l){var h=G(l,l.toLowerCase(),e,r,i,o,a);h&&(h[0]-=3,(!s||h[0]>s[0])&&(s=h))}}return s}function tt(t,n){if(!(n+1>=t.length)){var e=t[n],r=t[n+1];if(e!==r)return t.slice(0,n)+r+e+t.slice(n+2)}}},9768:function(t,n,e){"use strict";e.d(n,"a",(function(){return T}));var r=e("e8e3"),i=e("3742"),o=e("3d37"),u=e("32b8"),a=e("4035"),s=e("5fe7"),c="**",f="/",l="[/\\\\]",h="[^/\\\\]",d=/\//g;function p(t){switch(t){case 0:return"";case 1:return h+"*?";default:return"(?:"+l+"|"+h+"+"+l+"|"+l+h+"+)*?"}}function g(t,n){if(!t)return[];for(var e=[],r=!1,i=!1,o="",u=0,a=t;u<a.length;u++){var s=a[u];switch(s){case n:if(!r&&!i){e.push(o),o="";continue}break;case"{":r=!0;break;case"}":r=!1;break;case"[":i=!0;break;case"]":i=!1;break}o+=s}return o&&e.push(o),e}function v(t){if(!t)return"";var n="",e=g(t,f);if(e.every((function(t){return t===c})))n=".*";else{var r=!1;e.forEach((function(t,o){if(t!==c){for(var u=!1,a="",s=!1,d="",m=0,_=t;m<_.length;m++){var y=_[m];if("}"!==y&&u)a+=y;else if(!s||"]"===y&&d)switch(y){case"{":u=!0;continue;case"[":s=!0;continue;case"}":var b=g(a,","),w="(?:"+b.map((function(t){return v(t)})).join("|")+")";n+=w,u=!1,a="";break;case"]":n+="["+d+"]",s=!1,d="";break;case"?":n+=h;continue;case"*":n+=p(1);continue;default:n+=i["p"](y)}else{var C=void 0;C="-"===y?y:"^"!==y&&"!"!==y||d?y===f?"":i["p"](y):"^",d+=C}}o<e.length-1&&(e[o+1]!==c||o+2<e.length)&&(n+=l),r=!1}else r||(n+=p(2),r=!0)}))}return n}var m=/^\*\*\/\*\.[\w\.-]+$/,_=/^\*\*\/([\w\.-]+)\/?$/,y=/^{\*\*\/[\*\.]?[\w\.-]+\/?(,\*\*\/[\*\.]?[\w\.-]+\/?)*}$/,b=/^{\*\*\/[\*\.]?[\w\.-]+(\/(\*\*)?)?(,\*\*\/[\*\.]?[\w\.-]+(\/(\*\*)?)?)*}$/,w=/^\*\*((\/[\w\.-]+)+)\/?$/,C=/^([\w\.-]+(\/[\w\.-]+)*)\/?$/,A=new a["a"](1e4),E=function(){return!1},k=function(){return null};function L(t,n){if(!t)return k;var e;e="string"!==typeof t?t.pattern:t,e=e.trim();var r,o=e+"_"+!!n.trimForExclusions,u=A.get(o);if(u)return S(u,t);if(m.test(e)){var a=e.substr(4);u=function(t,n){return"string"===typeof t&&i["m"](t,a)?e:null}}else u=(r=_.exec(x(e,n)))?O(r[1],e):(n.trimForExclusions?b:y).test(e)?M(e,n):(r=w.exec(x(e,n)))?P(r[1].substr(1),e,!0):(r=C.exec(x(e,n)))?P(r[1],e,!1):R(e);return A.set(o,u),S(u,t)}function S(t,n){return"string"===typeof n?t:function(e,r){return o["a"](e,n.base)?t(u["relative"](n.base,e),r):null}}function x(t,n){return n.trimForExclusions&&i["m"](t,"/**")?t.substr(0,t.length-2):t}function O(t,n){var e="/"+t,r="\\"+t,o=function(o,u){return"string"!==typeof o?null:u?u===t?n:null:o===t||i["m"](o,e)||i["m"](o,r)?n:null},u=[t];return o.basenames=u,o.patterns=[n],o.allBasenames=u,o}function M(t,n){var e=q(t.slice(1,-1).split(",").map((function(t){return L(t,n)})).filter((function(t){return t!==k})),t),i=e.length;if(!i)return k;if(1===i)return e[0];var o=function(n,r){for(var i=0,o=e.length;i<o;i++)if(e[i](n,r))return t;return null},u=r["j"](e,(function(t){return!!t.allBasenames}));u&&(o.allBasenames=u.allBasenames);var a=e.reduce((function(t,n){return n.allPaths?t.concat(n.allPaths):t}),[]);return a.length&&(o.allPaths=a),o}function P(t,n,e){var r=u["sep"]!==u["posix"].sep?t.replace(d,u["sep"]):t,o=u["sep"]+r,a=e?function(t,e){return"string"!==typeof t||t!==r&&!i["m"](t,o)?null:n}:function(t,e){return"string"===typeof t&&t===r?n:null};return a.allPaths=[(e?"*/":"./")+t],a}function R(t){try{var n=new RegExp("^"+v(t)+"$");return function(e,r){return n.lastIndex=0,"string"===typeof e&&n.test(e)?t:null}}catch(e){return k}}function T(t,n,e){return!(!t||"string"!==typeof n)&&j(t)(n,void 0,e)}function j(t,n){if(void 0===n&&(n={}),!t)return E;if("string"===typeof t||I(t)){var e=L(t,n);if(e===k)return E;var r=function(t,n){return!!e(t,n)};return e.allBasenames&&(r.allBasenames=e.allBasenames),e.allPaths&&(r.allPaths=e.allPaths),r}return B(t,n)}function I(t){var n=t;return n&&"string"===typeof n.base&&"string"===typeof n.pattern}function B(t,n){var e=q(Object.getOwnPropertyNames(t).map((function(e){return D(e,t[e],n)})).filter((function(t){return t!==k}))),i=e.length;if(!i)return k;if(!e.some((function(t){return!!t.requiresSiblings}))){if(1===i)return e[0];var o=function(t,n){for(var r=0,i=e.length;r<i;r++){var o=e[r](t,n);if(o)return o}return null},a=r["j"](e,(function(t){return!!t.allBasenames}));a&&(o.allBasenames=a.allBasenames);var s=e.reduce((function(t,n){return n.allPaths?t.concat(n.allPaths):t}),[]);return s.length&&(o.allPaths=s),o}var c=function(t,n,r){for(var i=void 0,o=0,a=e.length;o<a;o++){var s=e[o];s.requiresSiblings&&r&&(n||(n=u["basename"](t)),i||(i=n.substr(0,n.length-u["extname"](t).length)));var c=s(t,n,i,r);if(c)return c}return null},f=r["j"](e,(function(t){return!!t.allBasenames}));f&&(c.allBasenames=f.allBasenames);var l=e.reduce((function(t,n){return n.allPaths?t.concat(n.allPaths):t}),[]);return l.length&&(c.allPaths=l),c}function D(t,n,e){if(!1===n)return k;var r=L(t,e);if(r===k)return k;if("boolean"===typeof n)return r;if(n){var i=n.when;if("string"===typeof i){var o=function(n,e,o,u){if(!u||!r(n,e))return null;var a=i.replace("$(basename)",o),c=u(a);return Object(s["i"])(c)?c.then((function(n){return n?t:null})):c?t:null};return o.requiresSiblings=!0,o}}return r}function q(t,n){var e=t.filter((function(t){return!!t.basenames}));if(e.length<2)return t;var r,i=e.reduce((function(t,n){var e=n.basenames;return e?t.concat(e):t}),[]);if(n){r=[];for(var o=0,u=i.length;o<u;o++)r.push(n)}else r=e.reduce((function(t,n){var e=n.patterns;return e?t.concat(e):t}),[]);var a=function(t,n){if("string"!==typeof t)return null;if(!n){var e=void 0;for(e=t.length;e>0;e--){var o=t.charCodeAt(e-1);if(47===o||92===o)break}n=t.substr(e)}var u=i.indexOf(n);return-1!==u?r[u]:null};a.basenames=i,a.patterns=r,a.allBasenames=i;var s=t.filter((function(t){return!t.basenames}));return s.push(a),s}},"9c3e":function(t,n,e){"use strict";e.d(n,"a",(function(){return r})),e.d(n,"b",(function(){return i}));var r=function(){function t(t){this._prefix=t,this._lastId=0}return t.prototype.nextId=function(){return this._prefix+ ++this._lastId},t}(),i=new r("id#")},be5f:function(t,n,e){"use strict";e.d(n,"d",(function(){return i})),e.d(n,"b",(function(){return o})),e.d(n,"c",(function(){return u})),e.d(n,"a",(function(){return a}));var r=Object.prototype.hasOwnProperty;function i(t){var n=[];for(var e in t)r.call(t,e)&&n.push(t[e]);return n}function o(t){for(var n in t)if(r.call(t,n))return t[n]}function u(t,n){var e=function(e){if(r.call(t,e)){var i=n({key:e,value:t[e]},(function(){delete t[e]}));if(!1===i)return{value:void 0}}};for(var i in t){var o=e(i);if("object"===typeof o)return o.value}}var a=function(){function t(){this.map=new Map}return t.prototype.add=function(t,n){var e=this.map.get(t);e||(e=new Set,this.map.set(t,e)),e.add(n)},t.prototype.delete=function(t,n){var e=this.map.get(t);e&&(e.delete(n),0===e.size&&this.map.delete(t))},t.prototype.forEach=function(t,n){var e=this.map.get(t);e&&e.forEach(n)},t}()},ceb8:function(t,n,e){"use strict";function r(t,n){var e=Math.pow(10,n);return Math.round(t*e)/e}e.d(n,"c",(function(){return i})),e.d(n,"b",(function(){return u})),e.d(n,"a",(function(){return a}));var i=function(){function t(t,n,e,i){void 0===i&&(i=1),this.r=0|Math.min(255,Math.max(0,t)),this.g=0|Math.min(255,Math.max(0,n)),this.b=0|Math.min(255,Math.max(0,e)),this.a=r(Math.max(Math.min(1,i),0),3)}return t.equals=function(t,n){return t.r===n.r&&t.g===n.g&&t.b===n.b&&t.a===n.a},t}(),o=function(){function t(t,n,e,i){this.h=0|Math.max(Math.min(360,t),0),this.s=r(Math.max(Math.min(1,n),0),3),this.l=r(Math.max(Math.min(1,e),0),3),this.a=r(Math.max(Math.min(1,i),0),3)}return t.equals=function(t,n){return t.h===n.h&&t.s===n.s&&t.l===n.l&&t.a===n.a},t.fromRGBA=function(n){var e=n.r/255,r=n.g/255,i=n.b/255,o=n.a,u=Math.max(e,r,i),a=Math.min(e,r,i),s=0,c=0,f=(a+u)/2,l=u-a;if(l>0){switch(c=Math.min(f<=.5?l/(2*f):l/(2-2*f),1),u){case e:s=(r-i)/l+(r<i?6:0);break;case r:s=(i-e)/l+2;break;case i:s=(e-r)/l+4;break}s*=60,s=Math.round(s)}return new t(s,c,f,o)},t._hue2rgb=function(t,n,e){return e<0&&(e+=1),e>1&&(e-=1),e<1/6?t+6*(n-t)*e:e<.5?n:e<2/3?t+(n-t)*(2/3-e)*6:t},t.toRGBA=function(n){var e,r,o,u=n.h/360,a=n.s,s=n.l,c=n.a;if(0===a)e=r=o=s;else{var f=s<.5?s*(1+a):s+a-s*a,l=2*s-f;e=t._hue2rgb(l,f,u+1/3),r=t._hue2rgb(l,f,u),o=t._hue2rgb(l,f,u-1/3)}return new i(Math.round(255*e),Math.round(255*r),Math.round(255*o),c)},t}(),u=function(){function t(t,n,e,i){this.h=0|Math.max(Math.min(360,t),0),this.s=r(Math.max(Math.min(1,n),0),3),this.v=r(Math.max(Math.min(1,e),0),3),this.a=r(Math.max(Math.min(1,i),0),3)}return t.equals=function(t,n){return t.h===n.h&&t.s===n.s&&t.v===n.v&&t.a===n.a},t.fromRGBA=function(n){var e,r=n.r/255,i=n.g/255,o=n.b/255,u=Math.max(r,i,o),a=Math.min(r,i,o),s=u-a,c=0===u?0:s/u;return e=0===s?0:u===r?((i-o)/s%6+6)%6:u===i?(o-r)/s+2:(r-i)/s+4,new t(Math.round(60*e),c,u,n.a)},t.toRGBA=function(t){var n=t.h,e=t.s,r=t.v,o=t.a,u=r*e,a=u*(1-Math.abs(n/60%2-1)),s=r-u,c=[0,0,0],f=c[0],l=c[1],h=c[2];return n<60?(f=u,l=a):n<120?(f=a,l=u):n<180?(l=u,h=a):n<240?(l=a,h=u):n<300?(f=a,h=u):n<360&&(f=u,h=a),f=Math.round(255*(f+s)),l=Math.round(255*(l+s)),h=Math.round(255*(h+s)),new i(f,l,h,o)},t}(),a=function(){function t(t){if(!t)throw new Error("Color needs a value");if(t instanceof i)this.rgba=t;else if(t instanceof o)this._hsla=t,this.rgba=o.toRGBA(t);else{if(!(t instanceof u))throw new Error("Invalid color ctor argument");this._hsva=t,this.rgba=u.toRGBA(t)}}return t.fromHex=function(n){return t.Format.CSS.parseHex(n)||t.red},Object.defineProperty(t.prototype,"hsla",{get:function(){return this._hsla?this._hsla:o.fromRGBA(this.rgba)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"hsva",{get:function(){return this._hsva?this._hsva:u.fromRGBA(this.rgba)},enumerable:!0,configurable:!0}),t.prototype.equals=function(t){return!!t&&i.equals(this.rgba,t.rgba)&&o.equals(this.hsla,t.hsla)&&u.equals(this.hsva,t.hsva)},t.prototype.getRelativeLuminance=function(){var n=t._relativeLuminanceForComponent(this.rgba.r),e=t._relativeLuminanceForComponent(this.rgba.g),i=t._relativeLuminanceForComponent(this.rgba.b),o=.2126*n+.7152*e+.0722*i;return r(o,4)},t._relativeLuminanceForComponent=function(t){var n=t/255;return n<=.03928?n/12.92:Math.pow((n+.055)/1.055,2.4)},t.prototype.isLighter=function(){var t=(299*this.rgba.r+587*this.rgba.g+114*this.rgba.b)/1e3;return t>=128},t.prototype.isLighterThan=function(t){var n=this.getRelativeLuminance(),e=t.getRelativeLuminance();return n>e},t.prototype.isDarkerThan=function(t){var n=this.getRelativeLuminance(),e=t.getRelativeLuminance();return n<e},t.prototype.lighten=function(n){return new t(new o(this.hsla.h,this.hsla.s,this.hsla.l+this.hsla.l*n,this.hsla.a))},t.prototype.darken=function(n){return new t(new o(this.hsla.h,this.hsla.s,this.hsla.l-this.hsla.l*n,this.hsla.a))},t.prototype.transparent=function(n){var e=this.rgba,r=e.r,o=e.g,u=e.b,a=e.a;return new t(new i(r,o,u,a*n))},t.prototype.isTransparent=function(){return 0===this.rgba.a},t.prototype.isOpaque=function(){return 1===this.rgba.a},t.prototype.opposite=function(){return new t(new i(255-this.rgba.r,255-this.rgba.g,255-this.rgba.b,this.rgba.a))},t.prototype.toString=function(){return""+t.Format.CSS.format(this)},t.getLighterColor=function(t,n,e){if(t.isLighterThan(n))return t;e=e||.5;var r=t.getRelativeLuminance(),i=n.getRelativeLuminance();return e=e*(i-r)/i,t.lighten(e)},t.getDarkerColor=function(t,n,e){if(t.isDarkerThan(n))return t;e=e||.5;var r=t.getRelativeLuminance(),i=n.getRelativeLuminance();return e=e*(r-i)/r,t.darken(e)},t.white=new t(new i(255,255,255,1)),t.black=new t(new i(0,0,0,1)),t.red=new t(new i(255,0,0,1)),t.blue=new t(new i(0,0,255,1)),t.cyan=new t(new i(0,255,255,1)),t.lightgrey=new t(new i(211,211,211,1)),t.transparent=new t(new i(0,0,0,0)),t}();(function(t){(function(n){(function(n){function e(n){return 1===n.rgba.a?"rgb("+n.rgba.r+", "+n.rgba.g+", "+n.rgba.b+")":t.Format.CSS.formatRGBA(n)}function r(t){return"rgba("+t.rgba.r+", "+t.rgba.g+", "+t.rgba.b+", "+ +t.rgba.a.toFixed(2)+")"}function o(n){return 1===n.hsla.a?"hsl("+n.hsla.h+", "+(100*n.hsla.s).toFixed(2)+"%, "+(100*n.hsla.l).toFixed(2)+"%)":t.Format.CSS.formatHSLA(n)}function u(t){return"hsla("+t.hsla.h+", "+(100*t.hsla.s).toFixed(2)+"%, "+(100*t.hsla.l).toFixed(2)+"%, "+t.hsla.a.toFixed(2)+")"}function a(t){var n=t.toString(16);return 2!==n.length?"0"+n:n}function s(t){return"#"+a(t.rgba.r)+a(t.rgba.g)+a(t.rgba.b)}function c(n,e){return void 0===e&&(e=!1),e&&1===n.rgba.a?t.Format.CSS.formatHex(n):"#"+a(n.rgba.r)+a(n.rgba.g)+a(n.rgba.b)+a(Math.round(255*n.rgba.a))}function f(n){return n.isOpaque()?t.Format.CSS.formatHex(n):t.Format.CSS.formatRGBA(n)}function l(n){var e=n.length;if(0===e)return null;if(35!==n.charCodeAt(0))return null;if(7===e){var r=16*h(n.charCodeAt(1))+h(n.charCodeAt(2)),o=16*h(n.charCodeAt(3))+h(n.charCodeAt(4)),u=16*h(n.charCodeAt(5))+h(n.charCodeAt(6));return new t(new i(r,o,u,1))}if(9===e){r=16*h(n.charCodeAt(1))+h(n.charCodeAt(2)),o=16*h(n.charCodeAt(3))+h(n.charCodeAt(4)),u=16*h(n.charCodeAt(5))+h(n.charCodeAt(6));var a=16*h(n.charCodeAt(7))+h(n.charCodeAt(8));return new t(new i(r,o,u,a/255))}if(4===e){r=h(n.charCodeAt(1)),o=h(n.charCodeAt(2)),u=h(n.charCodeAt(3));return new t(new i(16*r+r,16*o+o,16*u+u))}if(5===e){r=h(n.charCodeAt(1)),o=h(n.charCodeAt(2)),u=h(n.charCodeAt(3)),a=h(n.charCodeAt(4));return new t(new i(16*r+r,16*o+o,16*u+u,(16*a+a)/255))}return null}function h(t){switch(t){case 48:return 0;case 49:return 1;case 50:return 2;case 51:return 3;case 52:return 4;case 53:return 5;case 54:return 6;case 55:return 7;case 56:return 8;case 57:return 9;case 97:return 10;case 65:return 10;case 98:return 11;case 66:return 11;case 99:return 12;case 67:return 12;case 100:return 13;case 68:return 13;case 101:return 14;case 69:return 14;case 102:return 15;case 70:return 15}return 0}n.formatRGB=e,n.formatRGBA=r,n.formatHSL=o,n.formatHSLA=u,n.formatHex=s,n.formatHexA=c,n.format=f,n.parseHex=l})(n.CSS||(n.CSS={}))})(t.Format||(t.Format={}))})(a||(a={}))},e8e3:function(t,n,e){"use strict";function r(t,n){return void 0===n&&(n=0),t[t.length-(1+n)]}function i(t){if(0===t.length)throw new Error("Invalid tail call");return[t.slice(0,t.length-1),t[t.length-1]]}function o(t,n,e){if(void 0===e&&(e=function(t,n){return t===n}),t===n)return!0;if(!t||!n)return!1;if(t.length!==n.length)return!1;for(var r=0,i=t.length;r<i;r++)if(!e(t[r],n[r]))return!1;return!0}function u(t,n,e){var r=0,i=t.length-1;while(r<=i){var o=(r+i)/2|0,u=e(t[o],n);if(u<0)r=o+1;else{if(!(u>0))return o;i=o-1}}return-(r+1)}function a(t,n){var e=0,r=t.length;if(0===r)return 0;while(e<r){var i=Math.floor((e+r)/2);n(t[i])?r=i:e=i+1}return e}function s(t,n){return f(t,n,0,t.length-1,[]),t}function c(t,n,e,r,i,o){for(var u=e,a=r+1,s=e;s<=i;s++)o[s]=t[s];for(s=e;s<=i;s++)u>r?t[s]=o[a++]:a>i?t[s]=o[u++]:n(o[a],o[u])<0?t[s]=o[a++]:t[s]=o[u++]}function f(t,n,e,r,i){if(!(r<=e)){var o=e+(r-e)/2|0;f(t,n,e,o,i),f(t,n,o+1,r,i),n(t[o],t[o+1])<=0||c(t,n,e,o,r,i)}}function l(t,n){for(var e=[],r=void 0,i=0,o=s(t.slice(0),n);i<o.length;i++){var u=o[i];r&&0===n(r[0],u)?r.push(u):(r=[u],e.push(r))}return e}function h(t){return t.filter((function(t){return!!t}))}function d(t){return!Array.isArray(t)||0===t.length}function p(t){return Array.isArray(t)&&t.length>0}function g(t,n){if(!n)return t.filter((function(n,e){return t.indexOf(n)===e}));var e=Object.create(null);return t.filter((function(t){var r=n(t);return!e[r]&&(e[r]=!0,!0)}))}function v(t){var n=new Set;return t.filter((function(t){return!n.has(t)&&(n.add(t),!0)}))}function m(t){var n=[];return t.forEach((function(t){return n.push(t)})),n}function _(t,n){for(var e=0;e<t.length;e++){var r=t[e];if(n(r))return e}return-1}function y(t,n,e){void 0===e&&(e=void 0);var r=_(t,n);return r<0?e:t[r]}function b(t,n){return t.length>0?t[0]:n}function w(t){var n;return(n=[]).concat.apply(n,t)}function C(t,n){var e="number"===typeof n?t:0;"number"===typeof n?e=t:(e=0,n=t);var r=[];if(e<=n)for(var i=e;i<n;i++)r.push(i);else for(i=e;i>n;i--)r.push(i);return r}function A(t,n,e){var r=t.slice(0,n),i=t.slice(n);return r.concat(e,i)}function E(t,n){var e=t.indexOf(n);e>-1&&(t.splice(e,1),t.unshift(n))}function k(t,n){var e=t.indexOf(n);e>-1&&(t.splice(e,1),t.push(n))}function L(t,n){for(var e=0;e<t.length;e++){var r=t[e];if(n(r,e,t))return r}}function S(t){return Array.isArray(t)?t:[t]}e.d(n,"v",(function(){return r})),e.d(n,"w",(function(){return i})),e.d(n,"g",(function(){return o})),e.d(n,"c",(function(){return u})),e.d(n,"i",(function(){return a})),e.d(n,"r",(function(){return s})),e.d(n,"o",(function(){return l})),e.d(n,"d",(function(){return h})),e.d(n,"p",(function(){return d})),e.d(n,"q",(function(){return p})),e.d(n,"e",(function(){return g})),e.d(n,"f",(function(){return v})),e.d(n,"n",(function(){return m})),e.d(n,"k",(function(){return _})),e.d(n,"j",(function(){return y})),e.d(n,"l",(function(){return b})),e.d(n,"m",(function(){return w})),e.d(n,"u",(function(){return C})),e.d(n,"a",(function(){return A})),e.d(n,"t",(function(){return E})),e.d(n,"s",(function(){return k})),e.d(n,"h",(function(){return L})),e.d(n,"b",(function(){return S}))},eda7:function(t,n,e){"use strict";function r(t,n){switch(void 0===n&&(n=0),typeof t){case"object":return null===t?i(349,n):Array.isArray(t)?a(t,n):s(t,n);case"string":return u(t,n);case"boolean":return o(t,n);case"number":return i(t,n);case"undefined":return i(0,937);default:return i(0,617)}}function i(t,n){return(n<<5)-n+t|0}function o(t,n){return i(t?433:863,n)}function u(t,n){n=i(149417,n);for(var e=0,r=t.length;e<r;e++)n=i(t.charCodeAt(e),n);return n}function a(t,n){return n=i(104579,n),t.reduce((function(t,n){return r(n,t)}),n)}function s(t,n){return n=i(181387,n),Object.keys(t).sort().reduce((function(n,e){return n=u(e,n),r(t[e],n)}),n)}e.d(n,"a",(function(){return r})),e.d(n,"b",(function(){return u}))},f070:function(t,n,e){"use strict";e.d(n,"a",(function(){return s})),e.d(n,"b",(function(){return c}));var r=e("a666"),i=e("308f"),o=function(){var t=function(n,e){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,n){t.__proto__=n}||function(t,n){for(var e in n)n.hasOwnProperty(e)&&(t[e]=n[e])},t(n,e)};return function(n,e){function r(){this.constructor=n}t(n,e),n.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}}(),u=function(t,n,e,r){function i(t){return t instanceof e?t:new e((function(n){n(t)}))}return new(e||(e=Promise))((function(e,o){function u(t){try{s(r.next(t))}catch(n){o(n)}}function a(t){try{s(r["throw"](t))}catch(n){o(n)}}function s(t){t.done?e(t.value):i(t.value).then(u,a)}s((r=r.apply(t,n||[])).next())}))},a=function(t,n){var e,r,i,o,u={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(t){return function(n){return s([t,n])}}function s(o){if(e)throw new TypeError("Generator is already executing.");while(u)try{if(e=1,r&&(i=2&o[0]?r["return"]:o[0]?r["throw"]||((i=r["return"])&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return u.label++,{value:o[1],done:!1};case 5:u.label++,r=o[1],o=[0];continue;case 7:o=u.ops.pop(),u.trys.pop();continue;default:if(i=u.trys,!(i=i.length>0&&i[i.length-1])&&(6===o[0]||2===o[0])){u=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){u.label=o[1];break}if(6===o[0]&&u.label<i[1]){u.label=i[1],i=o;break}if(i&&u.label<i[2]){u.label=i[2],u.ops.push(o);break}i[2]&&u.ops.pop(),u.trys.pop();continue}o=n.call(t,u)}catch(a){o=[6,a],r=0}finally{e=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}},s=function(t){function n(n,e,r,o,u){void 0===e&&(e=""),void 0===r&&(r=""),void 0===o&&(o=!0);var a=t.call(this)||this;return a._onDidChange=a._register(new i["a"]),a.onDidChange=a._onDidChange.event,a._enabled=!0,a._checked=!1,a._id=n,a._label=e,a._cssClass=r,a._enabled=o,a._actionCallback=u,a}return o(n,t),Object.defineProperty(n.prototype,"id",{get:function(){return this._id},enumerable:!0,configurable:!0}),Object.defineProperty(n.prototype,"label",{get:function(){return this._label},set:function(t){this._setLabel(t)},enumerable:!0,configurable:!0}),n.prototype._setLabel=function(t){this._label!==t&&(this._label=t,this._onDidChange.fire({label:t}))},Object.defineProperty(n.prototype,"tooltip",{get:function(){return this._tooltip||""},set:function(t){this._setTooltip(t)},enumerable:!0,configurable:!0}),n.prototype._setTooltip=function(t){this._tooltip!==t&&(this._tooltip=t,this._onDidChange.fire({tooltip:t}))},Object.defineProperty(n.prototype,"class",{get:function(){return this._cssClass},set:function(t){this._setClass(t)},enumerable:!0,configurable:!0}),n.prototype._setClass=function(t){this._cssClass!==t&&(this._cssClass=t,this._onDidChange.fire({class:t}))},Object.defineProperty(n.prototype,"enabled",{get:function(){return this._enabled},set:function(t){this._setEnabled(t)},enumerable:!0,configurable:!0}),n.prototype._setEnabled=function(t){this._enabled!==t&&(this._enabled=t,this._onDidChange.fire({enabled:t}))},Object.defineProperty(n.prototype,"checked",{get:function(){return this._checked},set:function(t){this._setChecked(t)},enumerable:!0,configurable:!0}),n.prototype._setChecked=function(t){this._checked!==t&&(this._checked=t,this._onDidChange.fire({checked:t}))},n.prototype.run=function(t,n){return this._actionCallback?this._actionCallback(t):Promise.resolve(!0)},n}(r["a"]),c=function(t){function n(){var n=null!==t&&t.apply(this,arguments)||this;return n._onDidBeforeRun=n._register(new i["a"]),n.onDidBeforeRun=n._onDidBeforeRun.event,n._onDidRun=n._register(new i["a"]),n.onDidRun=n._onDidRun.event,n}return o(n,t),n.prototype.run=function(t,n){return u(this,void 0,void 0,(function(){var e,r;return a(this,(function(i){switch(i.label){case 0:if(!t.enabled)return[2,Promise.resolve(null)];this._onDidBeforeRun.fire({action:t}),i.label=1;case 1:return i.trys.push([1,3,,4]),[4,this.runAction(t,n)];case 2:return e=i.sent(),this._onDidRun.fire({action:t,result:e}),[3,4];case 3:return r=i.sent(),this._onDidRun.fire({action:t,error:r}),[3,4];case 4:return[2]}}))}))},n.prototype.runAction=function(t,n){var e=n?t.run(n):t.run();return Promise.resolve(e)},n}(r["a"])},fdcc:function(t,n,e){"use strict";e.d(n,"e",(function(){return o})),e.d(n,"f",(function(){return u})),e.d(n,"g",(function(){return a})),e.d(n,"d",(function(){return c})),e.d(n,"a",(function(){return f})),e.d(n,"b",(function(){return l})),e.d(n,"c",(function(){return h}));var r=function(){function t(){this.listeners=[],this.unexpectedErrorHandler=function(t){setTimeout((function(){if(t.stack)throw new Error(t.message+"\n\n"+t.stack);throw t}),0)}}return t.prototype.emit=function(t){this.listeners.forEach((function(n){n(t)}))},t.prototype.onUnexpectedError=function(t){this.unexpectedErrorHandler(t),this.emit(t)},t.prototype.onUnexpectedExternalError=function(t){this.unexpectedErrorHandler(t)},t}(),i=new r;function o(t){c(t)||i.onUnexpectedError(t)}function u(t){c(t)||i.onUnexpectedExternalError(t)}function a(t){if(t instanceof Error){var n=t.name,e=t.message,r=t.stacktrace||t.stack;return{$isError:!0,name:n,message:e,stack:r}}return t}var s="Canceled";function c(t){return t instanceof Error&&t.name===s&&t.message===s}function f(){var t=new Error(s);return t.name=t.message,t}function l(t){return t?new Error("Illegal argument: "+t):new Error("Illegal argument")}function h(t){return t?new Error("Illegal state: "+t):new Error("Illegal state")}}}]);