(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5336721c"],{1924:function(t,e,i){"use strict";var n=i("6edb"),r=i("8937"),a=i("2974");function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}function s(t){if("string"!==typeof t)return t;var e=function(t){if("string"!==typeof t)return t;try{return JSON.parse(t.trim())}catch(e){return t.trim()}},i=e(t);if("string"!==typeof i)return i;var n=function(t){return t[t.length-1]},r=t.trim(),a=[],o=[],s=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return t.some((function(t){return n(o)===t}))},c=function(){return n(a)},l=null,u=0,h="";while(u<r.length){var f=r[u],d=s('"',"'");if(d||f.trim()){var p="\\"===r[u-1],v=s("}"),g=s("]"),m=s(","),y=c();if(d)if(n(o)!==f||p)h+=f;else{o.pop();var b=e(h);y.push(b),l=b,h=""}else if(g&&","===f)h&&(y.push(e(h)),h="");else if(v&&":"===f)o.push(","),h&&(y.push(h),h="");else if(m&&","===f)h&&(y.push(e(h)),h=""),o.pop();else if("}"===f&&(v||m)){h&&(y.push(e(h)),h=""),m&&o.pop();for(var x={},S=1;S<y.length;S+=2)x[y[S-1]]=y[S];a.pop(),a.length&&n(a).push(x),o.pop(),l=x}else"]"===f&&g?(h&&(y.push(e(h)),h=""),a.pop(),a.length&&n(a).push(y),o.pop(),l=y):"{"===f?(a.push([]),o.push("}")):"["===f?(a.push([]),o.push("]")):'"'===f?o.push('"'):"'"===f?o.push("'"):h+=f;u+=1}else u+=1}return l||h}var c=function(t){return t.split("-").reduce((function(t,e){return t+e.charAt(0).toUpperCase()+e.slice(1)}))},l=function(t){return function(e){var i=t.length,n=[],a=0,o="";while(a<i)if("{"===t[a]&&"{"===t[a+1])n.push(o),o="",a+=2;else if("}"===t[a]&&"}"===t[a+1]){if(n.length){var s=n.pop();o=Object(r["get"])(e,o,s.endsWith("=")?'"{'.concat(o,'}"'):o),n.push(s+o)}a+=2,o=""}else o+=t[a],a+=1;return n.push(o),n.map((function(t,e){return n[e-1]&&n[e-1].endsWith("=")?'"{'.concat(t,'}"'):t})).join("")}};function u(t,e){var i={},r=t.getAttributeNames&&t.getAttributeNames()||[],a=t.children&&Array.from(t.children).map((function(t){return u(t,e)})),l={},h=t.tagName?t.tagName.toLowerCase():"group";return"text"===h&&(i.text=t.innerText),l.type=h,"img"===h&&(l.type="image"),Array.from(r).forEach((function(e){var r=c(e),a=t.getAttribute(e);try{if("style"===r||"attrs"===r){var o=s(a);i=Object(n["a"])(Object(n["a"])({},i),o)}else l[r]=s(a)}catch(u){if("style"===r)throw u;l[r]=a}})),l.attrs=i,e&&e.style&&l.name&&"object"===o(e.style[l.name])&&(l.attrs=Object(n["a"])(Object(n["a"])({},l.attrs),e.style[l.name])),e&&e.style&&l.keyshape&&(l.attrs=Object(n["a"])(Object(n["a"])({},l.attrs),e.style)),a.length&&(l.children=a),l}function h(t,e,i){var r,o,s=t.attrs,c=void 0===s?{}:s,l={x:e.x||0,y:e.y||0,width:i.width||0,height:i.height||0};switch(t.type){case"maker":case"circle":c.r&&(o=2*c.r,r=2*c.r);break;case"text":c.text&&(o=Object(a["getTextSize"])(c.text,c.fontSize||12)[0],r=16,l.y+=r,l.height=r,l.width=o,t.attrs=Object(n["a"])({fontSize:12,fill:"#000"},c));break;default:c.width&&(o=c.width),c.height&&(r=c.height)}return r>=0&&(l.height=r),o>=0&&(l.width=o),c.marginTop&&(l.y+=c.marginTop),c.marginLeft&&(l.x+=c.marginLeft),l}function f(t,e){var i;void 0===e&&(e={x:0,y:0});var r=Object(n["a"])({x:0,y:0,width:0,height:0},e);if(null===(i=t.children)||void 0===i?void 0:i.length){var a=t.attrs,o=void 0===a?{}:a,s=o.marginTop,c=Object(n["a"])({},e);s&&(c.y+=s);for(var l=0;l<t.children.length;l++){t.children[l].attrs.key="".concat(o.key||"root"," -").concat(l," ");var u=f(t.children[l],c);if(u.bbox){var d=u.bbox;"inline"===u.attrs.next?c.x+=u.bbox.width:c.y+=u.bbox.height,d.width+d.x>r.width&&(r.width=d.width+d.x),d.height+d.y>r.height&&(r.height=d.height+d.y)}}}return t.bbox=h(t,e,r),t.attrs=Object(n["a"])(Object(n["a"])({},t.attrs),t.bbox),t}function d(t,e){var i,n,r,a,o=(t||{}).type,s=((null===e||void 0===e?void 0:e.attrs)||{}).key;if(s&&t&&(t.attrs.key=s),!t&&e)return{action:"delete",val:e,type:o,key:s};if(t&&!e)return{action:"add",val:t,type:o};if(!t&&!e)return{action:"same",type:o};var c=[];if((null===(i=t.children)||void 0===i?void 0:i.length)>0||(null===(n=e.children)||void 0===n?void 0:n.length)>0)for(var l=Math.max(null===(r=t.children)||void 0===r?void 0:r.length,null===(a=e.children)||void 0===a?void 0:a.length),u=e.children||[],h=t.children||[],f=0;f<l;f+=1)c.push(d(h[f],u[f]));var p=Object.keys(e.attrs),v=Object.keys(t.attrs);return e.type!==t.type?{action:"restructure",nowTarget:t,formerTarget:e,key:s,children:c}:p.filter((function(t){return"children"!==t})).some((function(i){return t.attrs[i]!==e.attrs[i]||!v.includes(i)}))?{action:"change",val:t,children:c,type:o,key:s}:{action:"same",children:c,type:o,key:s}}function p(t){var e={},i=function(e){var i="function"===typeof t?t(e):t,n=l(i)(e),r=document.createElement("div");r.innerHTML=n;var a=r.children[0],o=f(u(a,e));return r.remove(),o};return{draw:function(t,r){var a=i(t),o=r,s=function t(e){var i=e.attrs,a=void 0===i?{}:i,s=e.bbox,c=e.type,l=e.children,u=Object(n["c"])(e,["attrs","bbox","type","children"]);if("group"!==e.type){var h=r.addShape(e.type,Object(n["a"])({attrs:a,origin:{bbox:s,type:c,children:l}},u));e.keyshape&&(o=h)}e.children&&e.children.forEach((function(e){return t(e)}))};return s(a),e[t.id]=[a],o},update:function(t,r){e[t.id]||(e[t.id]=[]);var a=r.getContainer(),o=a.get("children"),s=i(t),c=e[t.id].pop(),l=d(s,c),u=function t(e){var i;"group"!==e.type&&a.addShape(e.type,{attrs:e.attrs}),(null===(i=e.children)||void 0===i?void 0:i.length)&&e.children.map((function(e){return t(e)}))},h=function t(e){var i,n=o.find((function(t){return t.attrs.key===e.attrs.key}));n&&a.removeChild(n),(null===(i=e.children)||void 0===i?void 0:i.length)&&e.children.map((function(e){return t(e)}))},f=function t(e){var i=e.key;if("group"!==e.type){var a=o.find((function(t){return t.attrs.key===i}));switch(e.action){case"change":if(a){var s=e.val.keyshape?r.getOriginStyle():{};a.attr(Object(n["a"])(Object(n["a"])({},s),e.val.attrs))}break;case"add":u(e.val);break;case"delete":h(e.val);break;case"restructure":h(e.formerTarget),u(e.nowTarget);break;default:break}}e.children&&e.children.forEach((function(e){return t(e)}))};f(l),e[t.id].push(s)},getAnchorPoints:function(){return[[0,.5],[1,.5],[.5,1],[.5,0]]}}}var v={};function g(t){return v[t]||(v[t]=Object(r["upperFirst"])(t)),v[t]}var m={defaultShapeType:"defaultType",className:null,getShape:function(t){var e=this,i=e[t]||e[e.defaultShapeType]||e["simple-circle"];return i},draw:function(t,e,i){var n=this.getShape(t);i["shapeMap"]={};var r=n.draw(e,i);return n.afterDraw&&n.afterDraw(e,i,r),r},baseUpdate:function(t,e,i,n){var r,a,o=this.getShape(t);o.update&&(o.mergeStyle=null===(r=o.getOptions)||void 0===r?void 0:r.call(o,e,n),null===(a=o.update)||void 0===a||a.call(o,e,i,n)),o.afterUpdate&&o.afterUpdate(e,i)},setState:function(t,e,i,n){var r=this.getShape(t);r.setState(e,i,n)},shouldUpdate:function(t){var e=this.getShape(t);return!!e.update},getControlPoints:function(t,e){var i=this.getShape(t);return i.getControlPoints(e)},getAnchorPoints:function(t,e){var i=this.getShape(t);return i.getAnchorPoints(e)}},y={options:{},draw:function(t,e){return this.drawShape(t,e)},drawShape:function(){},afterDraw:function(){},afterUpdate:function(){},setState:function(){},getControlPoints:function(t){return t.controlPoints},getAnchorPoints:function(t){var e=this.options.anchorPoints,i=t.anchorPoints||e;return i}},b=function(){function t(){}return t.registerFactory=function(e,i){var r=g(e),a=m,o=Object(n["a"])(Object(n["a"])({},a),i);return t[r]=o,o.className=r,o},t.getFactory=function(e){var i=g(e);return t[i]},t.registerNode=function(e,i,r){var a,o=t.Node;if("string"===typeof i||"function"===typeof i){var s=p(i);a=Object(n["a"])(Object(n["a"])({},o.getShape("single-node")),s)}else if(i.jsx){var c=i.jsx;s=p(c);a=Object(n["a"])(Object(n["a"])(Object(n["a"])({},o.getShape("single-node")),s),i)}else{o.getShape(r);var l=r?o.getShape(r):y;a=Object(n["a"])(Object(n["a"])({},l),i)}return a.type=e,a.itemType="node",o[e]=a,a},t.registerEdge=function(e,i,r){var a=t.Edge,o=r?a.getShape(r):y,s=Object(n["a"])(Object(n["a"])({},o),i);return s.type=e,s.itemType="edge",a[e]=s,s},t.registerCombo=function(e,i,r){var a=t.Combo,o=r?a.getShape(r):y,s=Object(n["a"])(Object(n["a"])({},o),i);return s.type=e,s.itemType="combo",a[e]=s,s},t}();e["a"]=b;b.registerFactory("node",{defaultShapeType:"circle"}),b.registerFactory("edge",{defaultShapeType:"line"}),b.registerFactory("combo",{defaultShapeType:"circle"})},"28d5":function(t,e,i){"use strict";var n=i("8937"),r={getDefaultCfg:function(){return{}},getEvents:function(){return{}},updateCfg:function(t){return Object.assign(this,t),!0},shouldBegin:function(){return!0},shouldUpdate:function(){return!0},shouldEnd:function(){return!0},bind:function(t){var e=this,i=this.events;this.graph=t,"drag-canvas"!==this.type&&"brush-select"!==this.type&&"lasso-select"!==this.type||t.get("canvas").set("draggable",!0),Object(n["each"])(i,(function(e,i){t.on(i,e)})),document.addEventListener("visibilitychange",(function(){e.keydown=!1}))},unbind:function(t){var e=this.events,i=t.get("canvas").get("draggable");"drag-canvas"!==this.type&&"brush-select"!==this.type&&"lasso-select"!==this.type||t.get("canvas").set("draggable",!1),Object(n["each"])(e,(function(e,i){t.off(i,e)})),t.get("canvas").set("draggable",i)},get:function(t){return this[t]},set:function(t,e){return this[t]=e,this}},a=function(){function t(){}return t.registerBehavior=function(e,i){if(!i)throw new Error("please specify handler for this behavior: ".concat(e));var a=Object(n["clone"])(r);Object.assign(a,i);var o=function(t){var e=this;Object.assign(this,this.getDefaultCfg(),t);var i=this.getEvents();this.events=null;var r={};i&&(Object(n["each"])(i,(function(t,i){r[i]=Object(n["wrapBehavior"])(e,t)})),this.events=r)};o.prototype=a,t.types[e]=o},t.hasBehavior=function(e){return!!t.types[e]},t.getBehavior=function(e){return t.types[e]},t.types={},t}();e["a"]=a},"2e0c":function(t,e,i){"use strict";var n="rgb(95, 149, 255)",r="rgb(255, 255, 255)",a="rgb(0, 0, 0)",o="rgb(247, 250, 255)",s="rgb(239, 244, 255)",c="rgb(253, 253, 253)",l="rgb(250, 250, 250)",u="rgb(224, 224, 224)",h="rgb(234, 234, 234)",f="rgb(245, 245, 245)",d="rgb(191, 213, 255)",p="#4572d9",v="rgb(223, 234, 255)",g={mainStroke:n,mainFill:s,activeStroke:n,activeFill:o,inactiveStroke:d,inactiveFill:o,selectedStroke:n,selectedFill:r,highlightStroke:p,highlightFill:v,disableStroke:u,disableFill:l,edgeMainStroke:u,edgeActiveStroke:n,edgeInactiveStroke:h,edgeSelectedStroke:n,edgeHighlightStroke:n,edgeDisableStroke:f,comboMainStroke:u,comboMainFill:c,comboActiveStroke:n,comboActiveFill:o,comboInactiveStroke:u,comboInactiveFill:c,comboSelectedStroke:n,comboSelectedFill:c,comboHighlightStroke:p,comboHighlightFill:c,comboDisableStroke:h,comboDisableFill:l};e["a"]={version:"0.6.15",rootContainerClassName:"root-container",nodeContainerClassName:"node-container",edgeContainerClassName:"edge-container",comboContainerClassName:"combo-container",delegateContainerClassName:"delegate-container",defaultLoopPosition:"top",nodeLabel:{style:{fill:"#000",fontSize:12,textAlign:"center",textBaseline:"middle"},offset:4},defaultNode:{type:"circle",style:{lineWidth:1,stroke:g.mainStroke,fill:s},size:20,color:g.mainStroke,linkPoints:{size:8,lineWidth:1,fill:g.activeFill,stroke:g.activeStroke}},nodeStateStyles:{active:{fill:g.activeFill,stroke:g.activeStroke,lineWidth:2,shadowColor:g.mainStroke,shadowBlur:10},selected:{fill:g.selectedFill,stroke:g.selectedStroke,lineWidth:4,shadowColor:g.selectedStroke,shadowBlur:10,"text-shape":{fontWeight:500}},highlight:{fill:g.highlightFill,stroke:g.highlightStroke,lineWidth:2,"text-shape":{fontWeight:500}},inactive:{fill:g.inactiveFill,stroke:g.inactiveStroke,lineWidth:1},disable:{fill:g.disableFill,stroke:g.disableStroke,lineWidth:1}},edgeLabel:{style:{fill:a,textAlign:"center",textBaseline:"middle",fontSize:12}},defaultEdge:{type:"line",size:1,style:{stroke:g.edgeMainStroke,lineAppendWidth:2},color:g.edgeMainStroke},edgeStateStyles:{active:{stroke:g.edgeActiveStroke,lineWidth:1},selected:{stroke:g.edgeSelectedStroke,lineWidth:2,shadowColor:g.edgeSelectedStroke,shadowBlur:10,"text-shape":{fontWeight:500}},highlight:{stroke:g.edgeHighlightStroke,lineWidth:2,"text-shape":{fontWeight:500}},inactive:{stroke:g.edgeInactiveStroke,lineWidth:1},disable:{stroke:g.edgeDisableStroke,lineWidth:1}},comboLabel:{style:{fill:a,textBaseline:"middle",fontSize:12},refY:10,refX:10},defaultCombo:{type:"circle",style:{fill:g.comboMainFill,lineWidth:1,stroke:g.comboMainStroke,r:5,width:20,height:10},size:[20,5],color:g.comboMainStroke,padding:[25,20,15,20]},comboStateStyles:{active:{stroke:g.comboActiveStroke,lineWidth:1,fill:g.comboActiveFill},selected:{stroke:g.comboSelectedStroke,lineWidth:2,fill:g.comboSelectedFill,shadowColor:g.comboSelectedStroke,shadowBlur:10,"text-shape":{fontWeight:500}},highlight:{stroke:g.comboHighlightStroke,lineWidth:2,fill:g.comboHighlightFill,"text-shape":{fontWeight:500}},inactive:{stroke:g.comboInactiveStroke,fill:g.comboInactiveFill,lineWidth:1},disable:{stroke:g.comboDisableStroke,fill:g.comboDisableFill,lineWidth:1}},delegateStyle:{fill:"#F3F9FF",fillOpacity:.5,stroke:"#1890FF",strokeOpacity:.9,lineDash:[5,5]},windowFontFamily:"undefined"!==typeof window&&window.getComputedStyle&&document.body&&window.getComputedStyle(document.body,null).getPropertyValue("font-family")||"Arial, sans-serif"}},"3e1e":function(t,e,i){"use strict";var n=i("28d5");e["a"]=n["a"]},"737c":function(t,e,i){"use strict";i.d(e,"a",(function(){return r}));var n=function(t,e,i){return(t.y-i.y)*(e.x-i.x)-(t.x-i.x)*(e.y-i.y)},r=function(t){var e=t.map((function(t){return{x:t.getModel().x,y:t.getModel().y}}));if(e.sort((function(t,e){return t.x===e.x?t.y-e.y:t.x-e.x})),1===e.length)return e;for(var i=[],r=0;r<e.length;r++){while(i.length>=2&&n(i[i.length-2],i[i.length-1],e[r])<=0)i.pop();i.push(e[r])}var a=[];for(r=e.length-1;r>=0;r--){while(a.length>=2&&n(a[a.length-2],a[a.length-1],e[r])<=0)a.pop();a.push(e[r])}a.pop(),i.pop();var o=i.concat(a);return o}},a196:function(t,e,i){"use strict";i.r(e),i.d(e,"World",(function(){return vr})),i.d(e,"Kernel",(function(){return ue})),i.d(e,"Camera",(function(){return L})),i.d(e,"Renderable",(function(){return fe})),i.d(e,"Geometry",(function(){return z})),i.d(e,"Material",(function(){return he}));var n=i("c86f"),r=i.n(n),a=i("970b"),o=i.n(a),s=i("5bc3"),c=i.n(s),l=i("53ec"),u=i.n(l),h=(i("d400"),i("1a3d")),f=i("20e7"),d=i("e1c6");function p(t){return"number"===typeof t}function v(t){return void 0===t?0:t>360||t<-360?t%360:t}function g(t,e,i){return p(t)?f["e"].fromValues(t,e,i):3===t.length?f["e"].clone(t):f["e"].fromValues(t[0],t[1],t[2])}var m,y,b,x,S,O,w,k,j,C,I=function(){function t(e,i){o()(this,t),this.name=void 0,this.matrix=void 0,this.right=void 0,this.up=void 0,this.forward=void 0,this.position=void 0,this.focalPoint=void 0,this.distanceVector=void 0,this.distance=void 0,this.dollyingStep=void 0,this.azimuth=0,this.elevation=0,this.roll=0,this.relAzimuth=0,this.relElevation=0,this.relRoll=0,this.name=e,this.matrix=f["b"].clone(i.matrix),this.right=f["e"].clone(i.right),this.up=f["e"].clone(i.up),this.forward=f["e"].clone(i.forward),this.position=f["e"].clone(i.position),this.focalPoint=f["e"].clone(i.focalPoint),this.distanceVector=f["e"].clone(i.distanceVector),this.azimuth=i.azimuth,this.elevation=i.elevation,this.roll=i.roll,this.relAzimuth=i.relAzimuth,this.relElevation=i.relElevation,this.relRoll=i.relRoll,this.dollyingStep=i.dollyingStep,this.distance=i.distance}return c()(t,[{key:"getPosition",value:function(){return this.position}},{key:"getFocalPoint",value:function(){return this.focalPoint}},{key:"getRoll",value:function(){return this.roll}},{key:"retrieve",value:function(t){t.matrix=f["b"].copy(t.matrix,this.matrix),t.right=f["e"].copy(t.right,this.right),t.up=f["e"].copy(t.up,this.up),t.forward=f["e"].copy(t.forward,this.forward),t.position=f["e"].copy(t.position,this.position),t.focalPoint=f["e"].copy(t.focalPoint,this.focalPoint),t.distanceVector=f["e"].copy(t.distanceVector,this.distanceVector),t.azimuth=this.azimuth,t.elevation=this.elevation,t.roll=this.roll,t.relAzimuth=this.relAzimuth,t.relElevation=this.relElevation,t.relRoll=this.relRoll,t.dollyingStep=this.dollyingStep,t.distance=this.distance}}]),t}();(function(t){t["ORBITING"]="ORBITING",t["EXPLORING"]="EXPLORING",t["TRACKING"]="TRACKING"})(k||(k={})),function(t){t["DEFAULT"]="DEFAULT",t["ROTATIONAL"]="ROTATIONAL",t["TRANSLATIONAL"]="TRANSLATIONAL",t["CINEMATIC"]="CINEMATIC"}(j||(j={})),function(t){t["ORTHOGRAPHIC"]="ORTHOGRAPHIC",t["PERSPECTIVE"]="PERSPECTIVE"}(C||(C={}));var P,A,M,E,_,R,N,T=Math.PI/180,F=180/Math.PI,L=(m=Object(d["injectable"])(),y=Object(d["inject"])(h["IDENTIFIER"].InteractorService),m((w=O=function(){function t(){o()(this,t),this.matrix=f["b"].create(),this.right=f["e"].fromValues(1,0,0),this.up=f["e"].fromValues(0,1,0),this.forward=f["e"].fromValues(0,0,1),this.position=f["e"].fromValues(0,0,1),this.focalPoint=f["e"].fromValues(0,0,0),this.distanceVector=f["e"].fromValues(0,0,0),this.distance=1,this.azimuth=0,this.elevation=0,this.roll=0,this.relAzimuth=0,this.relElevation=0,this.relRoll=0,this.dollyingStep=0,this.maxDistance=1/0,this.minDistance=-1/0,this.rotateWorld=!1,r()(this,"interactor",S,this),this.fov=30,this.near=.1,this.far=1e4,this.aspect=1,this.left=void 0,this.rright=void 0,this.top=void 0,this.bottom=void 0,this.zoom=1,this.perspective=f["b"].create(),this.view=void 0,this.following=void 0,this.type=k.EXPLORING,this.trackingMode=j.DEFAULT,this.projectionMode=C.PERSPECTIVE,this.frustum=new h["Frustum"],this.landmarks=[],this.landmarkAnimationID=void 0}return c()(t,[{key:"clone",value:function(){var e=new t;return e.setType(this.type,void 0),e.interactor=this.interactor,e}},{key:"getProjectionMode",value:function(){return this.projectionMode}},{key:"getPerspective",value:function(){return this.perspective}},{key:"getFrustum",value:function(){return this.frustum}},{key:"getPosition",value:function(){return this.position}},{key:"setType",value:function(t,e){return this.type=t,this.type===k.EXPLORING?this.setWorldRotation(!0):this.setWorldRotation(!1),this._getAngles(),this.type===k.TRACKING&&void 0!==e&&this.setTrackingMode(e),this}},{key:"setProjectionMode",value:function(t){return this.projectionMode=t,this}},{key:"setTrackingMode",value:function(t){if(this.type!==k.TRACKING)throw new Error("Impossible to set a tracking mode if the camera is not of tracking type");return this.trackingMode=t,this}},{key:"setWorldRotation",value:function(t){this.rotateWorld=t,this._getAngles()}},{key:"getViewTransform",value:function(){return f["b"].invert(f["b"].create(),this.matrix)}},{key:"getWorldTransform",value:function(){return this.matrix}},{key:"setMatrix",value:function(t){return this.matrix=t,this._update(),this}},{key:"setAspect",value:function(t){return this.setPerspective(this.near,this.far,this.fov,t),this}},{key:"setViewOffset",value:function(t,e,i,n,r,a){return this.aspect=t/e,void 0===this.view&&(this.view={enabled:!0,fullWidth:1,fullHeight:1,offsetX:0,offsetY:0,width:1,height:1}),this.view.enabled=!0,this.view.fullWidth=t,this.view.fullHeight=e,this.view.offsetX=i,this.view.offsetY=n,this.view.width=r,this.view.height=a,this.projectionMode===C.PERSPECTIVE?this.setPerspective(this.near,this.far,this.fov,this.aspect):this.setOrthographic(this.left,this.rright,this.top,this.bottom,this.near,this.far),this}},{key:"clearViewOffset",value:function(){return void 0!==this.view&&(this.view.enabled=!1),this.projectionMode===C.PERSPECTIVE?this.setPerspective(this.near,this.far,this.fov,this.aspect):this.setOrthographic(this.left,this.rright,this.top,this.bottom,this.near,this.far),this}},{key:"setPerspective",value:function(t,e,i,n){return this.projectionMode=C.PERSPECTIVE,this.fov=i,this.near=t,this.far=e,this.aspect=n,f["b"].perspective(this.perspective,this.fov*T,this.aspect,this.near,this.far),this}},{key:"setOrthographic",value:function(t,e,i,n,r,a){this.projectionMode=C.ORTHOGRAPHIC,this.rright=e,this.left=t,this.top=i,this.bottom=n,this.near=r,this.far=a;var o=(this.rright-this.left)/(2*this.zoom),s=(this.top-this.bottom)/(2*this.zoom),c=(this.rright+this.left)/2,l=(this.top+this.bottom)/2,u=c-o,h=c+o,d=l+s,p=l-s;if(void 0!==this.view&&this.view.enabled){var v=(this.rright-this.left)/this.view.fullWidth/this.zoom,g=(this.top-this.bottom)/this.view.fullHeight/this.zoom;u+=v*this.view.offsetX,h=u+v*this.view.width,d-=g*this.view.offsetY,p=d-g*this.view.height}return f["b"].ortho(this.perspective,u,h,d,p,r,a),this}},{key:"setPosition",value:function(t,e,i){return this._setPosition(t,e,i),this.setFocalPoint(this.focalPoint),this}},{key:"setFocalPoint",value:function(t,e,i){var n=f["e"].fromValues(0,1,0);if(this.focalPoint=g(t,e,i),this.trackingMode===j.CINEMATIC){var r=f["e"].subtract(f["e"].create(),this.focalPoint,this.position);t=r[0],e=r[1],i=r[2];var a=f["e"].length(r),o=Math.asin(e/a)*F,s=90+Math.atan2(i,t)*F,c=f["b"].create();f["b"].rotateY(c,c,s*T),f["b"].rotateX(c,c,o*T),n=f["e"].transformMat4(f["e"].create(),[0,1,0],c)}return f["b"].invert(this.matrix,f["b"].lookAt(f["b"].create(),this.position,this.focalPoint,n)),this._getAxes(),this._getDistance(),this._getAngles(),this}},{key:"setDistance",value:function(t){if(!(this.distance===t||t<0)){this.distance=t,this.distance<2e-4&&(this.distance=2e-4),this.dollyingStep=this.distance/100;var e=f["e"].create();t=this.distance;var i=this.forward,n=this.focalPoint;return e[0]=t*i[0]+n[0],e[1]=t*i[1]+n[1],e[2]=t*i[2]+n[2],this._setPosition(e),this}}},{key:"setMaxDistance",value:function(t){return this.maxDistance=t,this}},{key:"setMinDistance",value:function(t){return this.minDistance=t,this}},{key:"changeAzimuth",value:function(t){return this.setAzimuth(this.azimuth+t),this}},{key:"changeElevation",value:function(t){return this.setElevation(this.elevation+t),this}},{key:"changeRoll",value:function(t){return this.setRoll(this.roll+t),this}},{key:"setAzimuth",value:function(t){return this.azimuth=v(t),this.computeMatrix(),this._getAxes(),this.type===k.ORBITING||this.type===k.EXPLORING?this._getPosition():this.type===k.TRACKING&&this._getFocalPoint(),this}},{key:"getAzimuth",value:function(){return this.azimuth}},{key:"setElevation",value:function(t){return this.elevation=v(t),this.computeMatrix(),this._getAxes(),this.type===k.ORBITING||this.type===k.EXPLORING?this._getPosition():this.type===k.TRACKING&&this._getFocalPoint(),this}},{key:"setRoll",value:function(t){return this.roll=v(t),this.computeMatrix(),this._getAxes(),this.type===k.ORBITING||this.type===k.EXPLORING?this._getPosition():this.type===k.TRACKING&&this._getFocalPoint(),this}},{key:"rotate",value:function(t,e,i){if(this.type===k.EXPLORING){t=v(t),e=v(e),i=v(i);var n=f["c"].setAxisAngle(f["c"].create(),[1,0,0],(this.rotateWorld?1:-1)*e*T),r=f["c"].setAxisAngle(f["c"].create(),[0,1,0],(this.rotateWorld?1:-1)*t*T),a=f["c"].setAxisAngle(f["c"].create(),[0,0,1],i*T),o=f["c"].multiply(f["c"].create(),r,n);o=f["c"].multiply(f["c"].create(),o,a);var s=f["b"].fromQuat(f["b"].create(),o);f["b"].translate(this.matrix,this.matrix,[0,0,-this.distance]),f["b"].multiply(this.matrix,this.matrix,s),f["b"].translate(this.matrix,this.matrix,[0,0,this.distance])}else{if(Math.abs(this.elevation+e)>90)return;this.relElevation=v(e),this.relAzimuth=v(t),this.relRoll=v(i),this.elevation+=this.relElevation,this.azimuth+=this.relAzimuth,this.roll+=this.relRoll,this.computeMatrix()}return this._getAxes(),this.type===k.ORBITING||this.type===k.EXPLORING?this._getPosition():this.type===k.TRACKING&&this._getFocalPoint(),this._update(),this}},{key:"pan",value:function(t,e){var i=g(t,e,0),n=f["e"].clone(this.position);return f["e"].add(n,n,f["e"].scale(f["e"].create(),this.right,i[0])),f["e"].add(n,n,f["e"].scale(f["e"].create(),this.up,i[1])),this._setPosition(n),this}},{key:"dolly",value:function(t){var e=this.forward,i=f["e"].clone(this.position),n=t*this.dollyingStep,r=this.distance+t*this.dollyingStep;return n=Math.max(Math.min(r,this.maxDistance),this.minDistance)-this.distance,i[0]+=n*e[0],i[1]+=n*e[1],i[2]+=n*e[2],this._setPosition(i),this.type===k.ORBITING||this.type===k.EXPLORING?this._getDistance():this.type===k.TRACKING&&f["e"].add(this.focalPoint,i,this.distanceVector),this}},{key:"createLandmark",value:function(t,e){var i=this.clone();i.setPosition(e.position),i.setFocalPoint(e.focalPoint),void 0!==e.roll&&i.setRoll(e.roll);var n=new I(t,i);return this.landmarks.push(n),n}},{key:"setLandmark",value:function(t){var e=new I(t,this);return this.landmarks.push(e),this}},{key:"gotoLandmark",value:function(t){var e=this,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e3,n=this.landmarks.find((function(e){return e.name===t}));if(n){if(0===i)return void n.retrieve(this);void 0!==this.landmarkAnimationID&&window.cancelAnimationFrame(this.landmarkAnimationID),this.interactor.disconnect();var r,a=n.getPosition(),o=n.getFocalPoint(),s=n.getRoll(),c=function t(n){void 0===r&&(r=n);var c=n-r,l=(1-Math.cos(c/i*Math.PI))/2,u=f["e"].create(),h=f["e"].create(),d=0;f["e"].lerp(u,e.focalPoint,o,l),f["e"].lerp(h,e.position,a,l),d=e.roll*(1-l)+s*l,e.setFocalPoint(u),e.setPosition(h),e.setRoll(d),e.computeMatrix();var p=f["e"].dist(u,o)+f["e"].dist(h,a);if(!(p>.01))return e.setFocalPoint(u),e.setPosition(h),e.setRoll(d),e.computeMatrix(),void e.interactor.connect();c<i&&(e.landmarkAnimationID=window.requestAnimationFrame(t))};window.requestAnimationFrame(c)}}},{key:"_update",value:function(){this._getAxes(),this._getPosition(),this._getDistance(),this._getAngles()}},{key:"computeMatrix",value:function(){var t,e,i=f["c"].setAxisAngle(f["c"].create(),[0,0,1],this.roll*T);f["b"].identity(this.matrix),t=f["c"].setAxisAngle(f["c"].create(),[1,0,0],(this.rotateWorld&&this.type!==k.TRACKING||this.type===k.TRACKING?1:-1)*this.elevation*T),e=f["c"].setAxisAngle(f["c"].create(),[0,1,0],(this.rotateWorld&&this.type!==k.TRACKING||this.type===k.TRACKING?1:-1)*this.azimuth*T);var n=f["c"].multiply(f["c"].create(),e,t);n=f["c"].multiply(f["c"].create(),n,i);var r=f["b"].fromQuat(f["b"].create(),n);this.type===k.ORBITING||this.type===k.EXPLORING?(f["b"].translate(this.matrix,this.matrix,this.focalPoint),f["b"].multiply(this.matrix,this.matrix,r),f["b"].translate(this.matrix,this.matrix,[0,0,this.distance])):this.type===k.TRACKING&&(f["b"].translate(this.matrix,this.matrix,this.position),f["b"].multiply(this.matrix,this.matrix,r))}},{key:"_setPosition",value:function(t,e,i){this.position=g(t,e,i);var n=this.matrix;n[12]=this.position[0],n[13]=this.position[1],n[14]=this.position[2],n[15]=1}},{key:"_getAxes",value:function(){f["e"].copy(this.right,g(f["f"].transformMat4(f["f"].create(),[1,0,0,0],this.matrix))),f["e"].copy(this.up,g(f["f"].transformMat4(f["f"].create(),[0,1,0,0],this.matrix))),f["e"].copy(this.forward,g(f["f"].transformMat4(f["f"].create(),[0,0,1,0],this.matrix))),f["e"].normalize(this.right,this.right),f["e"].normalize(this.up,this.up),f["e"].normalize(this.forward,this.forward)}},{key:"_getAngles",value:function(){var t=this.distanceVector[0],e=this.distanceVector[1],i=this.distanceVector[2],n=f["e"].length(this.distanceVector);if(0===n)return this.elevation=0,void(this.azimuth=0);this.type===k.TRACKING||this.rotateWorld?(this.elevation=Math.asin(e/n)*F,this.azimuth=Math.atan2(-t,-i)*F):(this.elevation=-Math.asin(e/n)*F,this.azimuth=-Math.atan2(-t,-i)*F)}},{key:"_getPosition",value:function(){f["e"].copy(this.position,g(f["f"].transformMat4(f["f"].create(),[0,0,0,1],this.matrix))),this._getDistance()}},{key:"_getFocalPoint",value:function(){f["e"].transformMat3(this.distanceVector,[0,0,-this.distance],f["a"].fromMat4(f["a"].create(),this.matrix)),f["e"].add(this.focalPoint,this.position,this.distanceVector),this._getDistance()}},{key:"_getDistance",value:function(){this.distanceVector=f["e"].subtract(f["e"].create(),this.focalPoint,this.position),this.distance=f["e"].length(this.distanceVector),this.dollyingStep=this.distance/100}}]),t}(),O.ProjectionMode={ORTHOGRAPHIC:"ORTHOGRAPHIC",PERSPECTIVE:"PERSPECTIVE"},x=w,S=u()(x.prototype,"interactor",[y],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),b=x))||b),z=(P=Object(d["injectable"])(),A=Object(d["inject"])(h["IDENTIFIER"].GeometryComponentManager),P((N=R=function(){function t(){o()(this,t),this.config=void 0,r()(this,"geometry",_,this),this.entity=void 0,this.component=void 0}return c()(t,[{key:"getEntity",value:function(){return this.entity}},{key:"getComponent",value:function(){return this.component}},{key:"setConfig",value:function(t){this.config=t}},{key:"setEntity",value:function(t){this.entity=t,this.component=this.geometry.create(t),this.component.entity=t,this.onEntityCreated()}},{key:"onEntityCreated",value:function(){}}]),t}(),R.BOX="box",R.SPHERE="sphere",R.PLANE="plane",R.MERGED="merged",E=N,_=u()(E.prototype,"geometry",[A],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),M=E))||M),B=i("a34a"),D=i.n(B),G=i("c973"),V=i.n(G),W=i("9523"),U=i.n(W),X=i("5e45");function Y(){if("undefined"!==typeof document)return document.createElement("canvas");throw new Error("Cannot create a canvas in this context")}var H={}.toString,K=function(t,e){return H.call(t)==="[object "+e+"]"},q=K,Q=function(t){return Array.isArray?Array.isArray(t):q(t,"Array")},J=i("7037"),Z=i.n(J),$=9007199254740991,tt="[object Arguments]",et="[object Array]",it="[object Boolean]",nt="[object Date]",rt="[object Error]",at="[object Function]",ot="[object Map]",st="[object Number]",ct="[object Object]",lt="[object RegExp]",ut="[object Set]",ht="[object String]",ft="[object WeakMap]",dt="[object ArrayBuffer]",pt="[object DataView]",vt="[object Float32Array]",gt="[object Float64Array]",mt="[object Int8Array]",yt="[object Int16Array]",bt="[object Int32Array]",xt="[object Uint8Array]",St="[object Uint8ClampedArray]",Ot="[object Uint16Array]",wt="[object Uint32Array]",kt={};kt[vt]=kt[gt]=kt[mt]=kt[yt]=kt[bt]=kt[xt]=kt[St]=kt[Ot]=kt[wt]=!0,kt[tt]=kt[et]=kt[dt]=kt[it]=kt[pt]=kt[nt]=kt[rt]=kt[at]=kt[ot]=kt[st]=kt[ct]=kt[lt]=kt[ut]=kt[ht]=kt[ft]=!1;var jt=Object.prototype,Ct=jt.toString;function It(t){return At(t)&&Pt(t.length)&&!!kt[Ct.call(t)]}function Pt(t){return"number"===typeof t&&t>-1&&t%1===0&&t<=$}function At(t){return!!t&&"object"===Z()(t)}var Mt,Et,_t,Rt,Nt,Tt,Ft,Lt,zt=It;function Bt(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,n)}return i}function Dt(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?Bt(Object(i),!0).forEach((function(e){U()(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):Bt(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}var Gt,Vt,Wt,Ut,Xt,Yt,Ht,Kt,qt,Qt,Jt,Zt,$t,te,ee,ie,ne,re,ae,oe,se,ce,le,ue=(Mt=Object(d["injectable"])(),Et=Object(d["inject"])(h["IDENTIFIER"].RenderEngine),_t=Object(d["inject"])(h["IDENTIFIER"].ConfigService),Mt((Lt=function(){function t(){o()(this,t),r()(this,"engine",Tt,this),r()(this,"configService",Ft,this),this.entity=Object(h["createEntity"])(),this.model=void 0,this.dirty=!0,this.compiledBundle=void 0,this.initPromise=void 0}return c()(t,[{key:"init",value:function(){var t=this.configService.get(),e=t.canvas,i=t.engineOptions;this.initPromise=this.engine.init(Dt({canvas:e||Y(),swapChainFormat:X["TextureFormat"].BGRA8Unorm,antialiasing:!1},i))}},{key:"setBundle",value:function(t){this.compiledBundle=JSON.parse(JSON.stringify(t))}},{key:"setDispatch",value:function(t){return this.compiledBundle.context&&(this.compiledBundle.context.dispatch=t),this}},{key:"setMaxIteration",value:function(t){return this.compiledBundle.context&&(this.compiledBundle.context.maxIteration=t),this}},{key:"setBinding",value:function(t,e){var i=this;if("string"===typeof t){var n=p(e)||zt(e)||Q(e);if(this.compiledBundle&&this.compiledBundle.context){var r=this.compiledBundle.context.defines.find((function(e){return e.name===t}));if(r)return r.value=e,this;var a=this.compiledBundle.context.uniforms.find((function(e){return e.name===t}));a&&(n?(a.data=e,a.isReferer=!1,a.storageClass===h["STORAGE_CLASS"].Uniform?this.model&&this.model.updateUniform(t,e):this.model&&this.model.updateBuffer(t,e)):(a.isReferer=!0,a.data=e))}}else Object.keys(t).forEach((function(e){i.setBinding(e,t[e])}));return this}},{key:"execute",value:function(){var t=V()(D.a.mark((function t(){var e,i,n=this,r=arguments;return D.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e=r.length>0&&void 0!==r[0]?r[0]:1,!this.dirty){t.next=6;break}return this.compiledBundle.context&&(e>1?this.compiledBundle.context.maxIteration=e:this.compiledBundle.context.maxIteration++),t.next=5,this.compile();case 5:this.dirty=!1;case 6:for(this.engine.beginFrame(),this.engine.clear({}),this.compiledBundle.context&&this.compiledBundle.context.uniforms.filter((function(t){var e=t.isReferer;return e})).forEach((function(t){var e=t.data,i=t.name;n.model.confirmInput(e.model,i)})),i=0;i<e;i++)this.model.run();return this.engine.endFrame(),t.abrupt("return",this);case 12:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}()},{key:"getOutput",value:function(){var t=V()(D.a.mark((function t(){return D.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.abrupt("return",this.model.readData());case 1:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}()},{key:"compile",value:function(){var t=V()(D.a.mark((function t(){var e,i,n;return D.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,this.initPromise;case 2:return e=Dt({},this.compiledBundle.context),i=this.engine.supportWebGPU?this.engine.useWGSL?h["Target"].WGSL:h["Target"].GLSL450:h["Target"].GLSL100,n=this.compiledBundle.shaders[i],e.defines.filter((function(t){return t.runtime})).forEach((function(t){var e="".concat(h["DefineValuePlaceholder"]).concat(t.name);n=n.replace(e,"".concat(t.value))})),e.shader=n,e.uniforms.forEach((function(t){if(!t.data&&t.storageClass===h["STORAGE_CLASS"].StorageBuffer){var i=1;t.type===h["AST_TOKEN_TYPES"].FloatArray?i=1:t.type===h["AST_TOKEN_TYPES"].Vector4FloatArray&&(i=4),t.data=new Float32Array(e.output.length*i).fill(0)}})),this.compiledBundle.context=e,t.next=11,this.engine.createComputeModel(this.compiledBundle.context);case 11:this.model=t.sent;case 12:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}()}]),t}(),Nt=Lt,Tt=u()(Nt.prototype,"engine",[Et],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),Ft=u()(Nt.prototype,"configService",[_t],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),Rt=Nt))||Rt),he=(Gt=Object(d["injectable"])(),Vt=Object(d["inject"])(h["IDENTIFIER"].MaterialComponentManager),Gt((Ht=Yt=function(){function t(){o()(this,t),this.config=void 0,r()(this,"material",Xt,this),this.entity=void 0,this.component=void 0}return c()(t,[{key:"getEntity",value:function(){return this.entity}},{key:"getComponent",value:function(){return this.component}},{key:"setConfig",value:function(t){this.config=t}},{key:"setEntity",value:function(t,e){this.entity=t,this.component=this.material.create(t),this.component.entity=t,this.component.type=e,this.onEntityCreated()}},{key:"onEntityCreated",value:function(){}}]),t}(),Yt.BASIC="basic",Ut=Ht,Xt=u()(Ut.prototype,"material",[Vt],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),Wt=Ut))||Wt),fe=(Kt=Object(d["injectable"])(),qt=Object(d["inject"])(h["IDENTIFIER"].MeshComponentManager),Qt=Object(d["inject"])(h["IDENTIFIER"].CullableComponentManager),Jt=Object(d["inject"])(h["IDENTIFIER"].TransformComponentManager),Zt=Object(d["inject"])(h["IDENTIFIER"].Systems),$t=Object(d["named"])(h["IDENTIFIER"].SceneGraphSystem),Kt((se=oe=function(){function t(){o()(this,t),this.attributes={},this.config=void 0,r()(this,"mesh",ie,this),r()(this,"cullable",ne,this),r()(this,"transform",re,this),r()(this,"sceneGraphSystem",ae,this),this.meshComponent=void 0,this.transformComponent=void 0,this.entity=void 0}return c()(t,[{key:"getEntity",value:function(){return this.entity}},{key:"getTransformComponent",value:function(){return this.transformComponent}},{key:"getMeshComponent",value:function(){return this.meshComponent}},{key:"setConfig",value:function(t){this.config=t}},{key:"setEntity",value:function(t){this.entity=t,this.cullable.create(t),this.meshComponent=this.mesh.create(t),this.transformComponent=this.transform.create(t),this.onEntityCreated()}},{key:"setMaterial",value:function(t){return this.meshComponent.material=t,this}},{key:"setGeometry",value:function(t){return this.meshComponent.geometry=t,this}},{key:"setAttributes",value:function(t){var e=this;Object.keys(t).forEach((function(i){void 0!==t[i]&&t[i]!==e.attributes[i]&&(e.onAttributeChanged({name:i,data:t[i]}),e.attributes[i]=t[i])}))}},{key:"setVisible",value:function(t){var e=this;return this.meshComponent.visible=t,this.meshComponent.children.forEach((function(i){var n=e.mesh.getComponentByEntity(i);n&&(n.visible=t)})),this}},{key:"isVisible",value:function(){return this.meshComponent.visible}},{key:"attach",value:function(t){return this.sceneGraphSystem.attach(this.entity,t.entity),this}},{key:"detach",value:function(){return this.sceneGraphSystem.detach(this.entity),this}},{key:"detachChildren",value:function(){return this.sceneGraphSystem.detachChildren(this.entity),this}},{key:"onEntityCreated",value:function(){}},{key:"onAttributeChanged",value:function(t){var e=t.name,i=t.data;this.meshComponent&&this.meshComponent.material&&this.meshComponent.material.setUniform(this.convertAttributeName2UniformName(e),i)}},{key:"convertAttributeName2UniformName",value:function(t){return t}}]),t}(),oe.POINT="point",oe.LINE="line",oe.GRID="grid",ee=se,ie=u()(ee.prototype,"mesh",[qt],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),ne=u()(ee.prototype,"cullable",[Qt],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),re=u()(ee.prototype,"transform",[Jt],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),ae=u()(ee.prototype,"sceneGraphSystem",[Zt,$t],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),te=ee))||te),de=i("d26b"),pe=i("278c"),ve=i.n(pe),ge=i("ed6d"),me=i.n(ge),ye=i("6b58"),be=i.n(ye),xe=i("36c6"),Se=i.n(xe);function Oe(t){var e=we();return function(){var i,n=Se()(t);if(e){var r=Se()(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return be()(this,i)}}function we(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}var ke,je,Ce=4/64,Ie=1-2*Ce,Pe=(ce=Object(d["injectable"])(),ce(le=function(t){me()(i,t);var e=Oe(i);function i(){return o()(this,i),e.apply(this,arguments)}return c()(i,[{key:"onEntityCreated",value:function(){var t=this.config,e=t.widthSegments,i=void 0===e?1:e,n=t.heightSegments,r=void 0===n?1:n,a=t.depthSegments,o=void 0===a?1:a,s=t.halfExtents,c=void 0===s?f["e"].fromValues(.5,.5,.5):s,l=i,u=r,d=o,p=ve()(c,3),v=p[0],g=p[1],m=p[2],y=[f["e"].fromValues(-v,-g,m),f["e"].fromValues(v,-g,m),f["e"].fromValues(v,g,m),f["e"].fromValues(-v,g,m),f["e"].fromValues(v,-g,-m),f["e"].fromValues(-v,-g,-m),f["e"].fromValues(-v,g,-m),f["e"].fromValues(v,g,-m)],b=[[0,1,3],[4,5,7],[3,2,6],[1,0,4],[1,4,2],[5,0,6]],x=[[0,0,1],[0,0,-1],[0,1,0],[0,-1,0],[1,0,0],[-1,0,0]],S={FRONT:0,BACK:1,TOP:2,BOTTOM:3,RIGHT:4,LEFT:5},O=[],w=[],k=[],j=[],C=[],I=0,P=function(t,e,i){var n,r,a,o;for(a=0;a<=e;a++)for(o=0;o<=i;o++){var s=f["e"].create(),c=f["e"].create(),l=f["e"].create(),u=f["e"].create();f["e"].lerp(s,y[b[t][0]],y[b[t][1]],a/e),f["e"].lerp(c,y[b[t][0]],y[b[t][2]],o/i),f["e"].sub(l,c,y[b[t][0]]),f["e"].add(u,s,l),n=a/e,r=o/i,O.push(u[0],u[1],u[2]),w.push(x[t][0],x[t][1],x[t][2]),k.push(n,r),n/=3,r/=3,n=n*Ie+Ce,r=r*Ie+Ce,n+=t%3/3,r+=Math.floor(t/3)/3,j.push(n,r),a<e&&o<i&&(C.push(I+i+1,I+1,I),C.push(I+i+1,I+i+2,I+1)),I++}};P(S.FRONT,l,u),P(S.BACK,l,u),P(S.TOP,l,d),P(S.BOTTOM,l,d),P(S.RIGHT,d,u),P(S.LEFT,d,u);var A=Object(h["generateAABBFromVertices"])(O),M=this.getComponent();M.indices=Uint32Array.from(C),M.aabb=A,M.vertexCount=I,M.attributes=[{dirty:!0,name:"position",data:Float32Array.from(O),arrayStride:12,stepMode:"vertex",attributes:[{shaderLocation:0,offset:0,format:"float3"}]},{dirty:!0,name:"normal",data:Float32Array.from(w),arrayStride:12,stepMode:"vertex",attributes:[{shaderLocation:1,offset:0,format:"float3"}]},{dirty:!0,name:"uv",data:Float32Array.from(k),arrayStride:8,stepMode:"vertex",attributes:[{shaderLocation:2,offset:0,format:"float2"}]}]}}]),i}(z))||le),Ae=i("448a"),Me=i.n(Ae);function Ee(t,e){if(!t&&!e)throw new Error("Please specify valid arguments for parameters a and b.");if(!e||0===e.length)return t;if(!t||0===t.length)return e;if(Object.prototype.toString.call(t)!==Object.prototype.toString.call(e))throw new Error("The types of the two arguments passed for parameters a and b do not match.");var i=new t.constructor(t.length+e.length);return i.set(t),i.set(e,t.length),i}function _e(t){var e=Re();return function(){var i,n=Se()(t);if(e){var r=Se()(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return be()(this,i)}}function Re(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}var Ne,Te,Fe=(ke=Object(d["injectable"])(),ke(je=function(t){me()(i,t);var e=_e(i);function i(){return o()(this,i),e.apply(this,arguments)}return c()(i,[{key:"onEntityCreated",value:function(){var t=this.config.geometries,e=void 0===t?[]:t,i=this.getComponent();i.aabb=new h["AABB"];var n=[],r=[],a=0;e.forEach((function(t){var e=t.aabb,o=t.indices,s=t.vertexCount,c=t.attributes;i.aabb.add(e),i.vertexCount+=s,o&&r.push.apply(r,Me()(o.map((function(t){return t+a})))),a+=s,c.forEach((function(t,e){n[e]?t.data&&(p(t.data)?n[e].push(t.data):zt(t.data)?n[e].data=Ee(n[e].data,t.data):n[e].data=n[e].data.concat(t.data)):(n[e]=t,n[e].dirty=!0)}))})),i.attributes=n,i.indices=Uint32Array.from(r),i.dirty=!0}}]),i}(z))||je);function Le(t){var e=ze();return function(){var i,n=Se()(t);if(e){var r=Se()(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return be()(this,i)}}function ze(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}var Be,De,Ge=(Ne=Object(d["injectable"])(),Ne(Te=function(t){me()(i,t);var e=Le(i);function i(){return o()(this,i),e.apply(this,arguments)}return c()(i,[{key:"onEntityCreated",value:function(){for(var t=this.config,e=t.halfExtents,i=void 0===e?[.5,.5]:e,n=t.widthSegments,r=void 0===n?5:n,a=t.lengthSegments,o=void 0===a?5:a,s=[],c=[],l=[],u=[],f=0,d=0;d<=r;d++)for(var p=0;p<=o;p++){var v=-i[0]+2*i[0]*d/r,g=0,m=-(-i[1]+2*i[1]*p/o),y=d/r,b=p/o;s.push(v,g,m),c.push(0,1,0),l.push(y,b),d<r&&p<o&&(u.push(f+o+1,f+1,f),u.push(f+o+1,f+o+2,f+1)),f++}var x=Object(h["generateAABBFromVertices"])(s),S=this.getComponent();S.indices=Uint32Array.from(u),S.aabb=x,S.vertexCount=f,S.attributes=[{dirty:!0,name:"position",data:Float32Array.from(s),arrayStride:12,stepMode:"vertex",attributes:[{shaderLocation:0,offset:0,format:"float3"}]},{dirty:!0,name:"normal",data:Float32Array.from(c),arrayStride:12,stepMode:"vertex",attributes:[{shaderLocation:1,offset:0,format:"float3"}]},{dirty:!0,name:"uv",data:Float32Array.from(l),arrayStride:8,stepMode:"vertex",attributes:[{shaderLocation:2,offset:0,format:"float2"}]}]}}]),i}(z))||Te);function Ve(t){var e=We();return function(){var i,n=Se()(t);if(e){var r=Se()(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return be()(this,i)}}function We(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}var Ue,Xe,Ye,He,Ke,qe,Qe,Je,Ze=(Be=Object(d["injectable"])(),Be(De=function(t){me()(i,t);var e=Ve(i);function i(){return o()(this,i),e.apply(this,arguments)}return c()(i,[{key:"onEntityCreated",value:function(){for(var t=this.config,e=t.radius,i=void 0===e?.5:e,n=t.latitudeBands,r=void 0===n?16:n,a=t.longitudeBands,o=void 0===a?16:a,s=[],c=[],l=[],u=[],f=0;f<=r;f++)for(var d=f*Math.PI/r,p=Math.sin(d),v=Math.cos(d),g=0;g<=o;g++){var m=2*g*Math.PI/o-Math.PI/2,y=Math.sin(m),b=Math.cos(m),x=b*p,S=v,O=y*p,w=1-g/o,k=1-f/r;s.push(x*i,S*i,O*i),c.push(x,S,O),l.push(w,k)}for(var j=0;j<r;++j)for(var C=0;C<o;++C){var I=j*(o+1)+C,P=I+o+1;u.push(I+1,P,I),u.push(I+1,P+1,P)}var A=Object(h["generateAABBFromVertices"])(s),M=this.getComponent();M.indices=Uint32Array.from(u),M.aabb=A,M.vertexCount=s.length/3,M.attributes=[{dirty:!0,name:"position",data:Float32Array.from(s),arrayStride:12,stepMode:"vertex",attributes:[{shaderLocation:0,offset:0,format:"float3"}]},{dirty:!0,name:"normal",data:Float32Array.from(c),arrayStride:12,stepMode:"vertex",attributes:[{shaderLocation:1,offset:0,format:"float3"}]},{dirty:!0,name:"uv",data:Float32Array.from(l),arrayStride:8,stepMode:"vertex",attributes:[{shaderLocation:2,offset:0,format:"float2"}]}]}}]),i}(z))||De),$e=i("3c96"),ti=i.n($e);function ei(t){var e=ii();return function(){var i,n=Se()(t);if(e){var r=Se()(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return be()(this,i)}}function ii(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}var ni,ri,ai,oi,si,ci,li,ui,hi,fi,di,pi,vi='varying vec4 fragColor;\n\n#pragma include "uv.frag.declaration"\n#pragma include "map.frag.declaration"\n\nvoid main() {\n  vec4 diffuseColor = fragColor;\n\n  #pragma include "map.frag.main"\n\n  gl_FragColor = diffuseColor;\n}',gi='attribute vec3 position;\nattribute vec3 normal;\n\nuniform mat4 projectionMatrix;\nuniform mat4 modelViewMatrix;\nuniform vec4 color;\n\nvarying vec4 fragColor;\n\n#pragma include "uv.vert.declaration"\n\nvoid main() {\n  gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n  fragColor = color;\n\n  #pragma include "uv.vert.main"\n}',mi="// layout(set = 0, binding = 1) uniform WireframeUniforms {\n//   float lineWidth;\n//   vec4 lineColor;\n// } wireframe;\n\nlayout(location = 0) in vec4 fragColor;\n// layout(location = 1) in vec3 v_Barycentric;\n\nlayout(location = 0) out vec4 outColor;\n\n// wireframe\n// float edgeFactor() {\n//   vec3 d = fwidth(v_Barycentric);\n//   vec3 a3 = smoothstep(vec3(0.0), d * wireframe.lineWidth, v_Barycentric);\n//   return min(min(a3.x, a3.y), a3.z);\n// }\n\nvoid main() {\n  // outColor = mix(fragColor, wireframe.lineColor, (1.0 - edgeFactor()));\n  outColor = fragColor;\n}",yi="layout(set = 0, binding = 0) uniform Uniforms {\n  vec4 color;\n  mat4 projectionMatrix;\n  mat4 modelViewMatrix;\n} uniforms;\n\nlayout(location = 0) in vec3 position;\n// layout(location = 1) in vec3 barycentric;\n\nlayout(location = 0) out vec4 fragColor;\n// layout(location = 1) out vec3 v_Barycentric;\n\nvoid main() {\n  gl_Position = uniforms.projectionMatrix * uniforms.modelViewMatrix * vec4(position, 1.0);\n  fragColor = uniforms.color;\n  // v_Barycentric = barycentric;\n}",bi=(Ue=Object(d["injectable"])(),Xe=Object(d["inject"])(h["IDENTIFIER"].RenderEngine),Ye=Object(d["inject"])(h["IDENTIFIER"].ShaderModuleService),Ue((Je=function(t){me()(i,t);var e=ei(i);function i(){var t;o()(this,i);for(var n=arguments.length,a=new Array(n),s=0;s<n;s++)a[s]=arguments[s];return t=e.call.apply(e,[this].concat(a)),r()(t,"engine",qe,ti()(t)),r()(t,"shaderModuleService",Qe,ti()(t)),t}return c()(i,[{key:"onEntityCreated",value:function(){var t=this.getComponent(),e=this.engine.supportWebGPU?yi:gi,i=this.engine.supportWebGPU?mi:vi;this.shaderModuleService.registerModule("material-basic",{vs:e,fs:i});var n=this.shaderModuleService.getModule("material-basic"),r=n.vs,a=n.fs,o=n.uniforms;t.vertexShaderGLSL=r,t.fragmentShaderGLSL=a,t.setUniform(o),this.config.map&&(t.setDefines({USE_UV:1,USE_MAP:1}),t.setUniform({map:this.config.map,uvTransform:f["a"].create()}))}}]),i}(he),Ke=Je,qe=u()(Ke.prototype,"engine",[Xe],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),Qe=u()(Ke.prototype,"shaderModuleService",[Ye],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),He=Ke))||He);function xi(t){var e=Si();return function(){var i,n=Se()(t);if(e){var r=Se()(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return be()(this,i)}}function Si(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}var Oi="// generate grid, borrow from clay.gl viewer\n// @see https://github.com/pissang/clay-viewer/blob/master/src/graphic/ground.glsl\n#extension GL_OES_standard_derivatives : enable\n\nvarying vec3 v_Position;\n// varying vec3 v_Normal;\n\nuniform float u_GridSize : 5;\nuniform float u_GridSize2 : .5;\nuniform vec4 u_GridColor : [0, 0, 0, 1];\nuniform vec4 u_GridColor2 : [0.3, 0.3, 0.3, 1];\nuniform bool u_GridEnabled : true;\n\n// uniform vec3 u_LightDirection;\n// uniform vec3 u_LightColor;\n// uniform vec3 u_Camera;\n\nvoid main() {\n  // vec3 n = v_Normal;\n  // vec3 l = normalize(u_LightDirection);\n  // float NdotL = clamp(dot(n, l), 0.001, 1.0);\n\n  gl_FragColor = vec4(1.);\n\n  if (u_GridEnabled) {\n    float wx = v_Position.x;\n    float wz = v_Position.z;\n    // float x0 = abs(fract(wx / u_GridSize - 0.5) - 0.5) / fwidth(wx) * u_GridSize / 2.0;\n    // float z0 = abs(fract(wz / u_GridSize - 0.5) - 0.5) / fwidth(wz) * u_GridSize / 2.0;\n\n    float x1 = abs(fract(wx / u_GridSize2 - 0.5) - 0.5) / fwidth(wx) * u_GridSize2;\n    float z1 = abs(fract(wz / u_GridSize2 - 0.5) - 0.5) / fwidth(wz) * u_GridSize2;\n\n    // float v0 = 1.0 - clamp(min(x0, z0), 0.0, 1.0);\n    float v1 = 1.0 - clamp(min(x1, z1), 0.0, 1.0);\n    // if (v0 > 0.1) {\n        // gl_FragColor = mix(gl_FragColor, u_GridColor, v0);\n    // }\n    // else {\n        gl_FragColor = mix(gl_FragColor, u_GridColor2, v1);\n    // }\n  }\n\n  // float shadowFactor = calcShadow(u_ShadowMap, v_PositionFromLight, l, n);\n  // vec3 diffuseColor = u_LightColor * NdotL * shadowFactor;\n\n  // gl_FragColor.rgb *= diffuseColor;\n}",wi="attribute vec3 a_Position;\n\nvarying vec3 v_Position;\n\nuniform mat4 projectionMatrix;\nuniform mat4 modelViewMatrix;\n\nvoid main() {\n  v_Position = a_Position;\n  gl_Position = projectionMatrix * modelViewMatrix * vec4(a_Position, 1.);\n}",ki=(ni=Object(d["injectable"])(),ri=Object(d["inject"])(h["IDENTIFIER"].Systems),ai=Object(d["named"])(h["IDENTIFIER"].MaterialSystem),oi=Object(d["inject"])(h["IDENTIFIER"].Systems),si=Object(d["named"])(h["IDENTIFIER"].GeometrySystem),ci=Object(d["inject"])(h["IDENTIFIER"].ShaderModuleService),ni((pi=function(t){me()(i,t);var e=xi(i);function i(){var t;o()(this,i);for(var n=arguments.length,a=new Array(n),s=0;s<n;s++)a[s]=arguments[s];return t=e.call.apply(e,[this].concat(a)),r()(t,"materialSystem",hi,ti()(t)),r()(t,"geometrySystem",fi,ti()(t)),r()(t,"shaderModuleService",di,ti()(t)),t}return c()(i,[{key:"onAttributeChanged",value:function(t){var e=t.name,i=t.data,n=this.getMeshComponent();n&&n.material&&("gridColor"===e?(n.material.setUniform("u_GridColor",i),n.material.setUniform("u_GridColor2",i)):"gridSize"===e&&(n.material.setUniform("u_GridSize",i),n.material.setUniform("u_GridSize2",i)))}},{key:"onEntityCreated",value:function(){this.shaderModuleService.registerModule("grid",{vs:wi,fs:Oi});var t=this.shaderModuleService.getModule("grid"),e=t.vs,i=t.fs,n=t.uniforms,r=this.materialSystem.createShaderMaterial({vertexShader:e,fragmentShader:i});this.setMaterial(r);var a=this.geometrySystem.createBufferGeometry({vertexCount:4});this.setGeometry(a),r.setCull({enable:!1,face:h["gl"].BACK}).setDepth({enable:!0,func:h["gl"].LESS}),r.setUniform(n),this.setAttributes({gridColor:this.config.gridColor,gridSize:this.config.gridSize}),a.setIndex([0,3,2,2,1,0]),a.setAttribute("a_Position",Float32Array.from([-4,-1,-4,4,-1,-4,4,-1,4,-4,-1,4]),{arrayStride:8,stepMode:"vertex",attributes:[{shaderLocation:0,offset:0,format:"float2"}]})}}]),i}(fe),ui=pi,hi=u()(ui.prototype,"materialSystem",[ri,ai],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),fi=u()(ui.prototype,"geometrySystem",[oi,si],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),di=u()(ui.prototype,"shaderModuleService",[ci],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),li=ui))||li),ji=i("e213"),Ci=i("91a7");function Ii(t,e,i,n,r){Pi(e,n,-r),Pi(e,n,r),t.push(i),t.push(i)}function Pi(t,e,i){t.push([[e[0],e[1]],i])}var Ai,Mi,Ei,_i,Ri,Ni,Ti,Fi,Li,zi,Bi,Di,Gi=function(t,e,i){var n=[0,0],r=[0,0],a=[0,0],o=[0,0],s=-1,c=!1,l=null,u=Object(ji["create"])(),h=i||0,f=3,d=[],p=[],v=[],g=[0,0];e&&(t=t.slice(),t.push(t[0]));for(var m=t.length,y=1;y<m;y++){var b=h,x=t[y-1],S=t[y],O=y<t.length-1?t[y+1]:null;if(g.push(y/m,y/m),Object(Ci["direction"])(n,S,x),l||(l=[0,0],Object(Ci["normal"])(l,n)),c||(c=!0,Ii(p,d,x,l,1)),v.push([b+0,b+1,b+2]),O){Object(Ci["direction"])(r,O,S);var w=Object(Ci["computeMiter"])(a,o,n,r,1),k=Object(ji["dot"])(a,l)<0?-1:1,j=w>f;if(!isFinite(w)){Object(Ci["normal"])(l,n),Ii(p,d,S,l,1),v.push(1===s?[b,b+2,b+3]:[b+2,b+1,b+3]),h+=2,s=k;continue}j?(w=f,g.push(y/m),Pi(d,l,-k),p.push(S),Pi(d,o,w*k),p.push(S),v.push(s!==-k?[b,b+2,b+3]:[b+2,b+1,b+3]),v.push([b+2,b+3,b+4]),Object(Ci["normal"])(u,r),Object(ji["copy"])(l,u),Pi(d,l,-k),p.push(S),h+=3):(Ii(p,d,S,o,w),v.push(1===s?[b,b+2,b+3]:[b+2,b+1,b+3]),k=-1,Object(ji["copy"])(l,o),h+=2),s=k}else Object(Ci["normal"])(l,n),Ii(p,d,S,l,1),v.push(1===s?[b,b+2,b+3]:[b+2,b+1,b+3]),h+=2}return{normals:d,attrIndex:v,attrPos:p,attrCounters:g}};function Vi(t){var e=Wi();return function(){var i,n=Se()(t);if(e){var r=Se()(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return be()(this,i)}}function Wi(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}var Ui,Xi,Yi,Hi,Ki,qi,Qi,Ji,Zi,$i,tn,en,nn="uniform float u_dash_array : 0.02;\nuniform float u_dash_offset : 0;\nuniform float u_dash_ratio : 0;\nuniform float u_thickness : 0.02;\n\nvarying vec4 v_color;\nvarying vec2 v_normal;\nvarying float v_counters;\n\nvoid main() {\n    float blur = 1. - smoothstep(0.98, 1., length(v_normal));\n\n    gl_FragColor = v_color;\n    gl_FragColor.a *= blur * ceil(mod(v_counters + u_dash_offset, u_dash_array) - (u_dash_array * u_dash_ratio));\n}",rn="attribute vec2 a_pos;\nattribute vec4 a_color;\nattribute float a_line_miter;\nattribute vec2 a_line_normal;\nattribute float a_counters;\n\nuniform mat4 projectionMatrix;\nuniform mat4 modelViewMatrix;\nuniform float u_thickness : 0.02;\nuniform vec2 u_viewport;\n\nvarying vec4 v_color;\nvarying vec2 v_normal;\nvarying float v_counters;\n\nvoid main() {\n  v_color = a_color;\n  v_counters = a_counters;\n\n  vec3 normal = normalize(vec3(a_line_normal, 0.0));\n\n  vec4 offset = vec4(normal * u_thickness / 2.0 * a_line_miter, 0.0);\n\n  v_normal = vec2(normal * sign(a_line_miter));\n\n  gl_Position = projectionMatrix * modelViewMatrix * vec4(a_pos, 0.0, 1.0) + offset;\n}\n",an=(Ai=Object(d["injectable"])(),Mi=Object(d["inject"])(h["IDENTIFIER"].Systems),Ei=Object(d["named"])(h["IDENTIFIER"].MaterialSystem),_i=Object(d["inject"])(h["IDENTIFIER"].Systems),Ri=Object(d["named"])(h["IDENTIFIER"].GeometrySystem),Ni=Object(d["inject"])(h["IDENTIFIER"].ShaderModuleService),Ai((Di=function(t){me()(i,t);var e=Vi(i);function i(){var t;o()(this,i);for(var n=arguments.length,a=new Array(n),s=0;s<n;s++)a[s]=arguments[s];return t=e.call.apply(e,[this].concat(a)),r()(t,"materialSystem",Li,ti()(t)),r()(t,"geometrySystem",zi,ti()(t)),r()(t,"shaderModuleService",Bi,ti()(t)),t.vertexCount=void 0,t}return c()(i,[{key:"onAttributeChanged",value:function(t){var e=t.name,i=t.data,n=this.getMeshComponent();if(n&&n.material)switch(e){case"dashArray":n.material.setUniform("u_dash_array",i);break;case"dashOffset":n.material.setUniform("u_dash_offset",i);break;case"dashRatio":n.material.setUniform("u_dash_ratio",i);break;case"thickness":n.material.setUniform("u_thickness",i);break;case"color":var r=new Array(this.vertexCount).fill(void 0).map((function(){return i})).reduce((function(t,e){return[].concat(Me()(t),Me()(e))}),[]);n.geometry.setAttribute("a_color",Float32Array.from(r),{arrayStride:16,stepMode:"vertex",attributes:[{shaderLocation:1,offset:0,format:"float4"}]});break}}},{key:"onEntityCreated",value:function(){var t=this;this.shaderModuleService.registerModule("line",{vs:rn,fs:nn});var e=this.shaderModuleService.getModule("line"),i=e.vs,n=e.fs,r=e.uniforms,a=this.materialSystem.createShaderMaterial({vertexShader:i,fragmentShader:n}),o=Gi(this.config.points,!1),s=o.normals,c=o.attrIndex,l=o.attrPos,u=o.attrCounters,f=l.length;this.vertexCount=f;var d=this.geometrySystem.createBufferGeometry({vertexCount:f});this.setMaterial(a),this.setGeometry(d),a.setCull({enable:!1,face:h["gl"].BACK}).setUniform(r),this.setAttributes({dashArray:this.config.dashArray,dashOffset:this.config.dashOffset,dashRatio:this.config.dashRatio,thickness:this.config.thickness});var p=[],v=[];s.forEach((function(t){var e=t[0],i=t[1];p.push([e[0],e[1]]),v.push(i)})),d.setIndex(c.reduce((function(t,e){return[].concat(Me()(t),Me()(e))}),[])),d.setAttribute("a_pos",Float32Array.from(l.reduce((function(t,e){return[].concat(Me()(t),Me()(e))}),[])),{arrayStride:8,stepMode:"vertex",attributes:[{shaderLocation:0,offset:0,format:"float2"}]});var g=new Array(f).fill(void 0).map((function(){return Me()(t.config.color)})).reduce((function(t,e){return[].concat(Me()(t),Me()(e))}),[]);d.setAttribute("a_color",Float32Array.from(g),{arrayStride:16,stepMode:"vertex",attributes:[{shaderLocation:1,offset:0,format:"float4"}]}),d.setAttribute("a_line_miter",Float32Array.from(v),{arrayStride:4,stepMode:"vertex",attributes:[{shaderLocation:2,offset:0,format:"float"}]}),d.setAttribute("a_line_normal",Float32Array.from(p.reduce((function(t,e){return[].concat(Me()(t),Me()(e))}),[])),{arrayStride:8,stepMode:"vertex",attributes:[{shaderLocation:3,offset:0,format:"float2"}]}),d.setAttribute("a_counters",Float32Array.from(u),{arrayStride:4,stepMode:"vertex",attributes:[{shaderLocation:4,offset:0,format:"float"}]})}}]),i}(fe),Fi=Di,Li=u()(Fi.prototype,"materialSystem",[Mi,Ei],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),zi=u()(Fi.prototype,"geometrySystem",[_i,Ri],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),Bi=u()(Fi.prototype,"shaderModuleService",[Ni],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),Ti=Fi))||Ti);function on(t){return[t+1&255,t+1>>8&255,t+1>>8>>8&255]}function sn(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,n)}return i}function cn(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?sn(Object(i),!0).forEach((function(e){U()(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):sn(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}function ln(t){var e=un();return function(){var i,n=Se()(t);if(e){var r=Se()(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return be()(this,i)}}function un(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}var hn,fn,dn,pn,vn,gn,mn,yn,bn,xn,Sn="uniform float u_blur : 0.05;\nuniform float u_opacity : 0.7;\nuniform float u_stroke_width : 0.01;\nuniform vec4 u_stroke_color : [0, 0, 0, 0];\nuniform float u_stroke_opacity : 1;\n\nvarying vec4 v_color;\nvarying vec4 v_data;\nvarying float v_radius;\n\n#pragma include \"sdf2d\"\n#pragma include \"picking\"\n\nvoid main() {\n  int shape = int(floor(v_data.w + 0.5));\n\n  float antialiasblur = v_data.z;\n  float antialiased_blur = -max(u_blur, antialiasblur);\n  float r = v_radius / (v_radius + u_stroke_width);\n\n  float outer_df;\n  float inner_df;\n  // 'circle', 'triangle', 'square', 'pentagon', 'hexagon', 'octogon', 'hexagram', 'rhombus', 'vesica'\n  // if (shape == 0) {\n    outer_df = sdCircle(v_data.xy, 1.0);\n    inner_df = sdCircle(v_data.xy, r);\n  // } else if (shape == 1) {\n  //   outer_df = sdEquilateralTriangle(1.1 * v_data.xy);\n  //   inner_df = sdEquilateralTriangle(1.1 / r * v_data.xy);\n  // } else if (shape == 2) {\n  //   outer_df = sdBox(v_data.xy, vec2(1.));\n  //   inner_df = sdBox(v_data.xy, vec2(r));\n  // } else if (shape == 3) {\n  //   outer_df = sdPentagon(v_data.xy, 0.8);\n  //   inner_df = sdPentagon(v_data.xy, r * 0.8);\n  // } else if (shape == 4) {\n  //   outer_df = sdHexagon(v_data.xy, 0.8);\n  //   inner_df = sdHexagon(v_data.xy, r * 0.8);\n  // } else if (shape == 5) {\n  //   outer_df = sdOctogon(v_data.xy, 1.0);\n  //   inner_df = sdOctogon(v_data.xy, r);\n  // } else if (shape == 6) {\n  //   outer_df = sdHexagram(v_data.xy, 0.52);\n  //   inner_df = sdHexagram(v_data.xy, r * 0.52);\n  // } else if (shape == 7) {\n  //   outer_df = sdRhombus(v_data.xy, vec2(1.0));\n  //   inner_df = sdRhombus(v_data.xy, vec2(r));\n  // } else if (shape == 8) {\n  //   outer_df = sdVesica(v_data.xy, 1.1, 0.8);\n  //   inner_df = sdVesica(v_data.xy, r * 1.1, r * 0.8);\n  // }\n\n  float opacity_t = smoothstep(0.0, antialiased_blur, outer_df);\n\n  float color_t = u_stroke_width < 0.01 ? 0.0 : smoothstep(\n    antialiased_blur,\n    0.0,\n    inner_df\n  );\n  vec4 strokeColor = u_stroke_color == vec4(0) ? v_color : u_stroke_color;\n\n  gl_FragColor = mix(vec4(v_color.rgb, v_color.a * u_opacity), strokeColor * u_stroke_opacity, color_t);\n  gl_FragColor.a = gl_FragColor.a * opacity_t;\n\n  gl_FragColor = filterColor(gl_FragColor);\n}",On='attribute vec2 position;\nattribute vec4 color;\nattribute float shape;\nattribute vec2 offset;\nattribute float size;\n\nuniform mat4 projectionMatrix;\nuniform mat4 modelViewMatrix;\n\nuniform float u_stroke_width : 0.01;\nuniform float u_device_pixel_ratio;\nuniform vec2 u_viewport;\n\nvarying vec4 v_color;\nvarying vec4 v_data;\nvarying float v_radius;\n\n#pragma include "picking"\n\nvoid main() {\n  v_color = color;\n  v_radius = size;\n\n  lowp float antialiasblur = 1.0 / u_device_pixel_ratio * (size + u_stroke_width);\n\n  // construct point coords\n  v_data = vec4(position, antialiasblur, shape);\n\n  gl_Position = projectionMatrix * modelViewMatrix\n    * vec4(position * size + offset, 0.0, 1.0);\n\n  setPickingColor(a_PickingColor);\n}',wn=["circle","triangle","square","pentagon","hexagon","octogon","hexagram","rhombus","vesica"],kn=(Ui=Object(d["injectable"])(),Xi=Object(d["inject"])(h["IDENTIFIER"].Systems),Yi=Object(d["named"])(h["IDENTIFIER"].MaterialSystem),Hi=Object(d["inject"])(h["IDENTIFIER"].Systems),Ki=Object(d["named"])(h["IDENTIFIER"].GeometrySystem),qi=Object(d["inject"])(h["IDENTIFIER"].ShaderModuleService),Ui((en=function(t){me()(i,t);var e=ln(i);function i(){var t;o()(this,i);for(var n=arguments.length,a=new Array(n),s=0;s<n;s++)a[s]=arguments[s];return t=e.call.apply(e,[this].concat(a)),r()(t,"materialSystem",Zi,ti()(t)),r()(t,"geometrySystem",$i,ti()(t)),r()(t,"shaderModuleService",tn,ti()(t)),t}return c()(i,[{key:"onAttributeChanged",value:function(t){var e=t.name,i=t.data,n=this.getMeshComponent();n&&n.material&&("strokeWidth"===e?n.material.setUniform("u_stroke_width",i):"strokeColor"===e?n.material.setUniform("u_stroke_color",i):"strokeOpacity"===e?n.material.setUniform("u_stroke_opacity",i):"opacity"===e?n.material.setUniform("u_opacity",i):"blur"===e&&n.material.setUniform("u_blur",i))}},{key:"onEntityCreated",value:function(){this.shaderModuleService.registerModule("grid",{vs:On,fs:Sn});var t=this.shaderModuleService.getModule("grid"),e=t.vs,i=t.fs,n=t.uniforms,r=this.materialSystem.createShaderMaterial({vertexShader:e,fragmentShader:i,cull:{enable:!1},depth:{enable:!1},blend:{enable:!0,func:{srcRGB:h["gl"].SRC_ALPHA,dstRGB:h["gl"].ONE_MINUS_SRC_ALPHA,srcAlpha:1,dstAlpha:1}}});r.setUniform(cn({u_device_pixel_ratio:window.devicePixelRatio},n));var a=this.buildAttributes(),o=this.geometrySystem.createInstancedBufferGeometry({maxInstancedCount:a.instancedOffsets.length/2,vertexCount:6});o.setIndex([0,2,1,0,3,2]),o.setAttribute("position",Float32Array.from(a.positions),{arrayStride:8,stepMode:"vertex",attributes:[{shaderLocation:0,offset:0,format:"float2"}]}),o.setAttribute("offset",Float32Array.from(a.instancedOffsets),{arrayStride:8,stepMode:"instance",attributes:[{shaderLocation:1,offset:0,format:"float2"}]}),o.setAttribute("color",Float32Array.from(a.instancedColors),{arrayStride:16,stepMode:"instance",attributes:[{shaderLocation:2,offset:0,format:"float4"}]}),o.setAttribute("size",Float32Array.from(a.instancedSizes),{arrayStride:4,stepMode:"instance",attributes:[{shaderLocation:3,offset:0,format:"float"}]}),o.setAttribute("shape",Float32Array.from(a.instancedShapes),{arrayStride:4,stepMode:"instance",attributes:[{shaderLocation:4,offset:0,format:"float"}]}),o.setAttribute("a_PickingColor",Float32Array.from(a.instancedPickingColors),{arrayStride:12,stepMode:"instance",attributes:[{shaderLocation:6,offset:0,format:"float3"}]}),this.setMaterial(r),this.setGeometry(o)}},{key:"buildAttribute",value:function(t,e,i){var n,r,a,o;(n=e.instancedPickingColors).push.apply(n,Me()(on(t.id||i))),e.instancedShapes.push(wn.indexOf(t.shape||"circle")),(r=e.instancedColors).push.apply(r,Me()(t.color||[1,0,0,1])),(a=e.instancedOffsets).push.apply(a,Me()(t.position||[0,0])),(o=e.instancedSizes).push.apply(o,Me()(t.size||[.2,.2]))}},{key:"buildAttributes",value:function(){var t=this,e={positions:[1,1,1,-1,-1,-1,-1,1],instancedOffsets:[],instancedColors:[],instancedSizes:[],instancedShapes:[],instancedPickingColors:[]};return Array.isArray(this.config)?this.config.forEach((function(i,n){t.buildAttribute(i,e,n)})):this.buildAttribute(this.config,e,0),e}}]),i}(fe),Ji=en,Zi=u()(Ji.prototype,"materialSystem",[Xi,Yi],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),$i=u()(Ji.prototype,"geometrySystem",[Hi,Ki],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),tn=u()(Ji.prototype,"shaderModuleService",[qi],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),Qi=Ji))||Qi);function jn(t,e){var i;if("undefined"===typeof Symbol||null==t[Symbol.iterator]){if(Array.isArray(t)||(i=Cn(t))||e&&t&&"number"===typeof t.length){i&&(t=i);var n=0,r=function(){};return{s:r,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,s=!1;return{s:function(){i=t[Symbol.iterator]()},n:function(){var t=i.next();return o=t.done,t},e:function(t){s=!0,a=t},f:function(){try{o||null==i.return||i.return()}finally{if(s)throw a}}}}function Cn(t,e){if(t){if("string"===typeof t)return In(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?In(t,e):void 0}}function In(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=new Array(e);i<e;i++)n[i]=t[i];return n}var Pn,An,Mn,En,_n,Rn,Nn,Tn,Fn,Ln,zn,Bn,Dn,Gn,Vn="#ifdef USE_MAP\n  uniform sampler2D map;\n#endif",Wn="#ifdef USE_MAP\n  vec4 texelColor = texture2D(map, vUv);\n  // texelColor = mapTexelToLinear(texelColor);\n  diffuseColor *= texelColor;\n#endif",Un="#if (defined( USE_UV ) && ! defined( UVS_VERTEX_ONLY ))\n  varying vec2 vUv;\n#endif",Xn="#ifdef USE_UV\n  attribute vec2 uv;\n\t#ifdef UVS_VERTEX_ONLY\n    vec2 vUv;\n\t#else\n\t\tvarying vec2 vUv;\n\t#endif\n\tuniform mat3 uvTransform;\n#endif",Yn="#ifdef USE_UV\n  vUv = (uvTransform * vec3(uv, 1)).xy;\n#endif",Hn=(hn=Object(d["injectable"])(),fn=Object(d["inject"])(h["IDENTIFIER"].RenderEngine),dn=Object(d["inject"])(h["IDENTIFIER"].ShaderModuleService),pn=Object(d["inject"])(h["IDENTIFIER"].ConfigService),hn((xn=function(){function t(){o()(this,t),this.container=void 0,r()(this,"engine",mn,this),r()(this,"shaderModule",yn,this),r()(this,"configService",bn,this),this.inited=!1,this.rendering=!1,this.pendings=[],this.views=[],this.size=void 0}return c()(t,[{key:"init",value:function(){var t=V()(D.a.mark((function t(){var e,i,n,r,a;return D.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.shaderModule.registerBuiltinModules(),this.shaderModule.registerModule("uv.vert.declaration",{vs:Xn}),this.shaderModule.registerModule("uv.vert.main",{vs:Yn}),this.shaderModule.registerModule("uv.frag.declaration",{fs:Un}),this.shaderModule.registerModule("map.frag.declaration",{fs:Vn}),this.shaderModule.registerModule("map.frag.main",{fs:Wn}),e=this.container.getAll(h["IDENTIFIER"].Systems),i=this.configService.get(),!i.canvas){t.next=30;break}return t.next=11,this.engine.init({canvas:i.canvas,swapChainFormat:X["TextureFormat"].BGRA8Unorm,antialiasing:!1});case 11:n=jn(e),t.prev=12,n.s();case 14:if((r=n.n()).done){t.next=21;break}if(a=r.value,!a.initialize){t.next=19;break}return t.next=19,a.initialize();case 19:t.next=14;break;case 21:t.next=26;break;case 23:t.prev=23,t.t0=t["catch"](12),n.e(t.t0);case 26:return t.prev=26,n.f(),t.finish(26);case 29:this.inited=!0;case 30:case"end":return t.stop()}}),t,this,[[12,23,26,29]])})));function e(){return t.apply(this,arguments)}return e}()},{key:"render",value:function(){var t=V()(D.a.mark((function t(){var e,i,n,r,a,o,s,c=arguments;return D.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.inited&&!this.rendering){t.next=2;break}return t.abrupt("return");case 2:for(this.pendings.length&&this.pendings.forEach((function(t){t()})),this.rendering=!0,this.engine.beginFrame(),e=this.container.getAll(h["IDENTIFIER"].Systems),i=c.length,n=new Array(i),r=0;r<i;r++)n[r]=c[r];a=jn(e),t.prev=8,a.s();case 10:if((o=a.n()).done){t.next=17;break}if(s=o.value,!s.execute){t.next=15;break}return t.next=15,s.execute(n);case 15:t.next=10;break;case 17:t.next=22;break;case 19:t.prev=19,t.t0=t["catch"](8),a.e(t.t0);case 22:return t.prev=22,a.f(),t.finish(22);case 25:this.engine.endFrame(),this.rendering=!1;case 27:case"end":return t.stop()}}),t,this,[[8,19,22,25]])})));function e(){return t.apply(this,arguments)}return e}()},{key:"clear",value:function(t){var e=this;return this.inited?this.engine.clear(t):this.pendings.unshift((function(){e.engine.clear(t),e.pendings.shift()})),this}},{key:"setSize",value:function(t){var e=t.width,i=t.height,n=this.engine.getCanvas();return this.size={width:e,height:i},n.width=e,n.height=i,this}},{key:"getSize",value:function(){return this.size}}]),t}(),gn=xn,mn=u()(gn.prototype,"engine",[fn],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),yn=u()(gn.prototype,"shaderModule",[dn],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),bn=u()(gn.prototype,"configService",[pn],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),vn=gn))||vn),Kn=(Pn=Object(d["injectable"])(),Pn((Mn=function(){function t(){o()(this,t),this.entities=[]}return c()(t,[{key:"getEntities",value:function(){return this.entities}},{key:"addRenderable",value:function(t){return this.addEntity(t.getEntity()),this}},{key:"removeRenderable",value:function(t){return this.removeEntity(t.getEntity()),this}},{key:"addLight",value:function(){}},{key:"addEntity",value:function(t){return-1===this.entities.indexOf(t)&&this.entities.push(t),this}},{key:"removeEntity",value:function(t){var e=this.entities.indexOf(t);return this.entities.splice(e,1),this}}]),t}(),An=Mn))||An),qn=(En=Object(d["injectable"])(),En((Rn=function(){function t(){o()(this,t),this.cache={}}return c()(t,[{key:"get",value:function(t){return this.cache[t]}},{key:"set",value:function(t,e){this.cache[t]=e}}]),t}(),_n=Rn))||_n);function Qn(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,n)}return i}function Jn(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?Qn(Object(i),!0).forEach((function(e){U()(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):Qn(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}var Zn,$n,tr,er,ir,nr,rr,ar,or,sr,cr,lr,ur,hr=(Nn=Object(d["injectable"])(),Tn=Object(d["inject"])(qn),Fn=Object(d["inject"])(h["IDENTIFIER"].RenderEngine),Nn((Gn=function(){function t(){o()(this,t),r()(this,"textureCache",Bn,this),r()(this,"engine",Dn,this),this.config=void 0,this.loaded=!1,this.texture=void 0}return c()(t,[{key:"setConfig",value:function(t){this.config=t}},{key:"isLoaded",value:function(){return this.loaded}},{key:"load",value:function(){var t=V()(D.a.mark((function t(){var e=this;return D.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!this.config.url){t.next=4;break}return t.abrupt("return",new Promise((function(t,i){var n=e.textureCache.get(e.config.url);if(n)t(n);else{var r=new Image;r.crossOrigin="Anonymous",r.src=e.config.url,r.onload=function(){var i=e.engine.createTexture2D(Jn(Jn({},e.config),{},{data:r,width:r.width,height:r.height,flipY:!0}));e.textureCache.set(e.config.url,i),e.texture=i,e.loaded=!0,t(i)},r.onerror=function(){i()}}})));case 4:return this.loaded=!0,this.texture=this.engine.createTexture2D(this.config),t.abrupt("return",this.texture);case 7:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}()}]),t}(),zn=Gn,Bn=u()(zn.prototype,"textureCache",[Tn],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),Dn=u()(zn.prototype,"engine",[Fn],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),Ln=zn))||Ln),fr=(Zn=Object(d["injectable"])(),$n=Object(d["inject"])(h["IDENTIFIER"].Systems),tr=Object(d["named"])(h["IDENTIFIER"].RendererSystem),Zn((rr=function(){function t(){o()(this,t),r()(this,"rendererSystem",nr,this),this.camera=void 0,this.scene=void 0,this.viewport={x:0,y:0,width:0,height:0},this.clearColor=[1,1,1,1]}return c()(t,[{key:"getCamera",value:function(){return this.camera}},{key:"getScene",value:function(){return this.scene}},{key:"getViewport",value:function(){return this.viewport}},{key:"getClearColor",value:function(){return this.clearColor}},{key:"setCamera",value:function(t){return this.camera=t,this}},{key:"setScene",value:function(t){return this.scene=t,this}},{key:"setViewport",value:function(t){return this.viewport=t,this}},{key:"setClearColor",value:function(t){return this.clearColor=t,this}},{key:"pick",value:function(t){return this.rendererSystem.pick(t,this)}}]),t}(),ir=rr,nr=u()(ir.prototype,"rendererSystem",[$n,tr],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),er=ir))||er);function dr(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,n)}return i}function pr(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?dr(Object(i),!0).forEach((function(e){U()(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):dr(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}var vr=(ar=Object(d["injectable"])(),or=Object(d["inject"])(h["IDENTIFIER"].ConfigService),ar((ur=function(){function t(){o()(this,t),r()(this,"configService",lr,this),this.container=void 0}return c()(t,[{key:"getEngine",value:function(){var t=V()(D.a.mark((function t(){var e,i,n,r;return D.a.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e=this.container.get(h["IDENTIFIER"].RenderEngine),i=this.configService.get(),n=i.canvas,r=i.engineOptions,t.next=4,e.init(pr({canvas:n||Y(),swapChainFormat:X["TextureFormat"].BGRA8Unorm,antialiasing:!1},r));case 4:return t.abrupt("return",e);case 5:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}()},{key:"getTransformComponent",value:function(t){var e=this.container.get(h["IDENTIFIER"].TransformComponentManager);return e.getComponentByEntity(t)}},{key:"getMeshComponent",value:function(t){var e=this.container.get(h["IDENTIFIER"].MeshComponentManager);return e.getComponentByEntity(t)}},{key:"setConfig",value:function(t){this.configService.set(t)}},{key:"setContainer",value:function(t){this.container=t}},{key:"getContainer",value:function(){return this.container}},{key:"createEntity",value:function(){return Object(h["createEntity"])()}},{key:"createScene",value:function(){return this.container.get(Kn)}},{key:"createCamera",value:function(){return this.container.get(L)}},{key:"createView",value:function(){return this.container.get(fr)}},{key:"createRenderable",value:function(t,e){var i=t?this.container.getNamed(h["IDENTIFIER"].Renderable,t):this.container.get(fe),n=Object(h["createEntity"])();return i.setConfig(e||{}),i.setEntity(n),i}},{key:"createGeometry",value:function(t,e){var i=this.container.getNamed(h["IDENTIFIER"].Geometry,t),n=Object(h["createEntity"])();return i.setConfig(e||{}),i.setEntity(n),i.getComponent()}},{key:"createMaterial",value:function(t,e){var i=this.container.getNamed(h["IDENTIFIER"].Material,t),n=Object(h["createEntity"])();return i.setConfig(e||{}),i.setEntity(n,t),i.getComponent()}},{key:"createTexture2D",value:function(t){var e=this.container.get(hr);return e.setConfig(t),e}},{key:"createBufferGeometry",value:function(t){var e=this.container.getNamed(h["IDENTIFIER"].Systems,h["IDENTIFIER"].GeometrySystem);return e.createBufferGeometry(t)}},{key:"createInstancedBufferGeometry",value:function(t){var e=this.container.getNamed(h["IDENTIFIER"].Systems,h["IDENTIFIER"].GeometrySystem);return e.createInstancedBufferGeometry(t)}},{key:"createShaderMaterial",value:function(t){var e=this.container.getNamed(h["IDENTIFIER"].Systems,h["IDENTIFIER"].MaterialSystem);return e.createShaderMaterial(t)}},{key:"createKernel",value:function(t){var e=this.container.get(ue);return"string"===typeof t?e.setBundle(JSON.parse(t)):e.setBundle(t),e.init(),e}},{key:"createRenderer",value:function(){var t=this.container.get(Hn);return t.container=this.container,t.init(),t}},{key:"destroy",value:function(){var t=this.container.getAll(h["IDENTIFIER"].Systems);t.forEach((function(t){t.tearDown&&t.tearDown()}));var e=this.container.get(h["IDENTIFIER"].RenderEngine);e.destroy();var i=this.container.get(h["IDENTIFIER"].InteractorService);i.destroy()}}],[{key:"create",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=Object(h["createWorldContainer"])(),n=navigator.gpu?de["b"]:de["a"];i.isBound(h["IDENTIFIER"].RenderEngine)||i.bind(h["IDENTIFIER"].RenderEngine).to(n).inSingletonScope(),i.bind(Hn).toSelf(),i.bind(ue).toSelf(),i.bind(fe).toSelf(),i.bind(fr).toSelf(),i.bind(L).toSelf(),i.bind(Kn).toSelf(),i.bind(t).toSelf(),i.bind(qn).toSelf(),i.bind(hr).toSelf(),i.bind(h["IDENTIFIER"].Geometry).to(Pe).whenTargetNamed(z.BOX),i.bind(h["IDENTIFIER"].Geometry).to(Ze).whenTargetNamed(z.SPHERE),i.bind(h["IDENTIFIER"].Geometry).to(Ge).whenTargetNamed(z.PLANE),i.bind(h["IDENTIFIER"].Geometry).to(Fe).whenTargetNamed(z.MERGED),i.bind(h["IDENTIFIER"].Material).to(bi).whenTargetNamed(he.BASIC),i.bind(h["IDENTIFIER"].Renderable).to(kn).whenTargetNamed(fe.POINT),i.bind(h["IDENTIFIER"].Renderable).to(an).whenTargetNamed(fe.LINE),i.bind(h["IDENTIFIER"].Renderable).to(ki).whenTargetNamed(fe.GRID);var r=i.get(t);return r.setContainer(i),r.setConfig(e),r}}]),t}(),cr=ur,lr=u()(cr.prototype,"configService",[or],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),sr=cr))||sr)},e003:function(t,e,i){"use strict";i.d(e,"a",(function(){return h}));var n=i("adc7"),r={maxRoutingIterations:100,maxMarchingIterations:100,pixelGroupSize:2,edgeR0:10,edgeR1:10,nodeR0:5,nodeR1:10,morphBuffer:5,threshold:.001,skip:16,nodeInfluenceFactor:1,edgeInfluenceFactor:1,negativeNodeInfluenceFactor:-.5};function a(t,e,i){var n=!1,r=function(t,i){return e.cells[t+i*e.width]},a=function(t,e){var n=0;return r(t-1,e-1)>=i&&(n+=1),r(t,e-1)>i&&(n+=2),r(t-1,e)>i&&(n+=4),r(t,e)>i&&(n+=8),n},o=function(i,n){for(var r,o,s=i,c=n,l=0;l<e.width*e.height;l++){if(r=s,o=c,t.findIndex((function(t){return t.x===s&&t.y===c}))>-1){if(t[0].x===s&&t[0].y===c)return!0}else t.push({x:s,y:c});var u=a(s,c);switch(u){case-1:return console.warn("Marched out of bounds"),!0;case 0:case 3:case 2:case 7:s++;break;case 12:case 14:case 4:s--;break;case 6:0===r&&(-1===o?s-=1:s+=1);break;case 1:case 13:case 5:c--;break;case 9:1===r&&(0===o?c-=1:c+=1);break;case 10:case 8:case 11:c++;break;default:return console.warn("Marching squares invalid state: ".concat(u)),!0}}};this.march=function(){for(var t=0;t<e.width&&!n;t+=1)for(var s=0;s<e.height&&!n;s+=1)r(t,s)>i&&15!==a(t,s)&&(n=o(t,s));return n}}var o=function(t,e,i){var n=Math.ceil(t/i),r=Math.ceil(e/i),a=new Float32Array(Math.max(0,n*r)).fill(0);return{cells:a,width:n,height:r}},s=function(t,e,i){var r=null,a=Number.POSITIVE_INFINITY;return e.forEach((function(e){var o={x:t.getModel().x,y:t.getModel().y},s={x:e.getModel().x,y:e.getModel().y},c=Object(n["squareDist"])(o,s),l=new n["Line"](o.x,o.y,s.x,s.y),u=i.reduce((function(t,e){return Object(n["fractionToLine"])(e,l)>0?t+1:t}),0);c*Math.pow(u+1,2)<a&&(r=e,a=c*Math.pow(u+1,2))})),r},c=function(t,e){var i=Number.POSITIVE_INFINITY,r=null;return t.forEach((function(t){var a=Object(n["fractionToLine"])(t,e);a>=0&&a<i&&(r=t,i=a)})),r},l=function(t,e,i,r){var a=[],o=[];o.push(t);var s=!0,l=0,u=function(t,e){var i=!1;return e.forEach((function(e){i||(Object(n["isPointsOverlap"])(t,{x:e.x1,y:e.y1})||Object(n["isPointsOverlap"])(t,{x:e.x2,y:e.y2}))&&(i=!0)})),i},h=function(t,e){for(var i=0,r=e;i<r.length;i++){var a=r[i],o=a.getBBox(),s=[[o.x,o.y],[o.x+o.width,o.y],[o.x,o.y+o.height],[o.x+o.width,o.y+o.height]];if(Object(n["isPointInPolygon"])(s,t.x,t.y))return!0}return!1};while(s&&l<i){s=!1;var f=function(){var t=o.pop(),i=c(e,t);if(i){var f=Object(n["itemIntersectByLine"])(i,t),d=f[0],v=f[1];if(2===v){var g=function(c){var l=r,f=p(i,l,d,c),v=u(f,o)||u(f,a),g=h(f,e);while(!v&&g&&l>=1)l/=1.5,f=p(i,l,d,c),v=u(f,o)||u(f,a),g=h(f,e);!f||v||c&&g||(o.push(new n["Line"](t.x1,t.y1,f.x,f.y)),o.push(new n["Line"](f.x,f.y,t.x2,t.y2)),s=!0)};g(!0),s||g(!1)}}s||a.push(t),l+=1};while(!s&&o.length)f()}while(o.length)a.push(o.pop());return a};function u(t,e,i,r,a){var o=s(t,i,e);if(null===o)return[];var u=function(t){var i=[];while(t.length>0){var r=t.pop();if(0===t.length){i.push(r);break}var a=t.pop(),o=new n["Line"](r.x1,r.y1,a.x2,a.y2),s=c(e,o);s?(i.push(r),t.push(a)):t.push(o)}return i},h=new n["Line"](t.getModel().x,t.getModel().y,o.getModel().x,o.getModel().y),f=l(h,e,r,a),d=u(f);return d}var h=function(t,e,i){var s=Object.assign(r,i),c=Object(n["getPointsCenter"])(t.map((function(t){return{x:t.getModel().x,y:t.getModel().y}})));t=t.sort((function(t,e){return Object(n["squareDist"])({x:t.getModel().x,y:t.getModel().y},c)-Object(n["squareDist"])({x:e.getModel().x,y:e.getModel().y},c)}));var l=[],h=[];t.forEach((function(t){var i=u(t,e,l,s.maxRoutingIterations,s.morphBuffer);i.forEach((function(t){h.push(t)})),l.push(t)}));for(var p=f(t,h,s.nodeR0),v=o(p.width,p.height,s.pixelGroupSize),g=[],m=[],y=0;y<s.maxMarchingIterations;y++)if(d(t,e,h,p,v,s),g=[],m=[],new a(g,v,s.threshold).march()){var b=g.map((function(t){return{x:Math.round(t.x*s.pixelGroupSize+p.minX),y:Math.round(t.y*s.pixelGroupSize+p.minY)}}));if(b){var x=b.length;if(s.skip>1){x=Math.floor(b.length/s.skip);while(x<3&&s.skip>1)s.skip-=1,x=Math.floor(b.length/s.skip)}for(var S=0,O=0;O<x;O+=1,S+=s.skip)m.push({x:b[S].x,y:b[S].y})}var w=function(){for(var e=0,i=t;e<i.length;e++){var r=i[e],a=m.map((function(t){return[t.x,t.y]}));if(!Object(n["isPointInPolygon"])(a,r.getBBox().centerX,r.getBBox().centerY))return!1}return!0};if(m&&w())return m;if(s.threshold*=.9,y<=.5*s.maxMarchingIterations)s.memberInfluenceFactor*=1.2,s.edgeInfluenceFactor*=1.2;else{if(!(0!==s.nonMemberInfluenceFactor&&e.length>0))break;s.nonMemberInfluenceFactor*=.8}}return m};function f(t,e,i){var n={minX:Number.POSITIVE_INFINITY,minY:Number.POSITIVE_INFINITY,maxX:Number.NEGATIVE_INFINITY,maxY:Number.NEGATIVE_INFINITY,width:0,height:0,x:0,y:0},r=[];t.forEach((function(t){r.push(t.getBBox())})),e.forEach((function(t){r.push(t.getBBox())}));for(var a=0,o=r;a<o.length;a++){var s=o[a];n.minX=(s.minX<n.minX?s.minX:n.minX)-i,n.minY=(s.minY<n.minY?s.minY:n.minY)-i,n.maxX=(s.maxX>n.maxX?s.maxX:n.maxX)+i,n.maxY=(s.maxY>n.maxY?s.maxY:n.maxY)+i}return n.width=n.maxX-n.minX,n.height=n.maxY-n.minY,n.x=n.minX,n.y=n.minY,n}function d(t,e,i,r,a,o){function s(t,e){var i=Math.floor((t-e)/o.pixelGroupSize);return i<0?0:i}function c(t,e){return t*o.pixelGroupSize+e}var l=(o.nodeR0-o.nodeR1)*(o.nodeR0-o.nodeR1),u=(o.edgeR0-o.edgeR1)*(o.edgeR0-o.edgeR1),h=function(t,e){var i=Math.min(s(t.minX,e+r.minX),a.width),n=Math.min(s(t.minY,e+r.minY),a.height),o=Math.min(s(t.maxX,-e+r.minX),a.width),c=Math.min(s(t.maxY,-e+r.minY),a.height);return[i,n,o,c]},f=function(t,e){for(var i=t.getBBox(),s=h(i,o.nodeR1),l=s[0],u=s[1],f=s[2],d=s[3],p=u;p<d;p+=1)for(var v=l;v<f;v+=1)if(!(e<0&&a[v+p*a.width]<=0)){var g=c(v,r.minX),m=c(p,r.minY),y=Object(n["pointRectSquareDist"])({x:g,y:m},{x:i.minX,y:i.minY,width:i.width,height:i.height});if(y<Math.pow(o.nodeR1,2)){var b=Math.sqrt(y)-o.nodeR1;a.cells[v+p*a.width]+=e*b*b}}},d=function(t,e){for(var i=t.getBBox(),s=h(i,o.edgeR1),l=s[0],u=s[1],f=s[2],d=s[3],p=u;p<d;p+=1)for(var v=l;v<f;v+=1)if(!(e<0&&a.cells[v+p*a.width]<=0)){var g=c(v,r.minX),m=c(p,r.minY),y=Object(n["pointLineSquareDist"])({x:g,y:m},t);if(y<Math.pow(o.edgeR1,2)){var b=Math.sqrt(y)-o.edgeR1;a.cells[v+p*a.width]+=e*b*b}}};o.nodeInfluenceFactor&&t.forEach((function(t){f(t,o.nodeInfluenceFactor/l)})),o.edgeInfluenceFactor&&i.forEach((function(t){d(t,o.edgeInfluenceFactor/u)})),o.negativeNodeInfluenceFactor&&e.forEach((function(t){f(t,o.negativeNodeInfluenceFactor/l)}))}function p(t,e,i,n){var r=t.getBBox(),a=i[0],o=i[1],s=i[2],c=i[3],l={topLeft:{x:r.minX-e,y:r.minY-e},topRight:{x:r.maxX+e,y:r.minY-e},bottomLeft:{x:r.minX-e,y:r.maxY+e},bottomRight:{x:r.maxX+e,y:r.maxY+e}},u=r.height*r.width;function h(t,e){return r.width*(.5*(t.y-r.minY+(e.y-r.minY)))}if(o){if(a)return n?l.topLeft:l.bottomRight;if(s)return n?l.bottomLeft:l.topRight;var f=h(o,c);return f<.5*u?o.y>c.y?n?l.topLeft:l.bottomRight:n?l.topRight:l.bottomLeft:o.y<c.y?n?l.bottomLeft:l.topRight:n?l.bottomRight:l.topLeft}if(c){if(a)return n?l.topRight:l.bottomLeft;if(s)return n?l.bottomRight:l.topLeft}var d=h(a,s);return d<.5*u?a.x>s.x?n?l.topLeft:l.bottomRight:n?l.bottomLeft:l.topRight:a.x<s.x?n?l.topRight:l.bottomLeft:n?l.bottomRight:l.topLeft}},edbf:function(t,e,i){"use strict";i.d(e,"a",(function(){return M})),i.d(e,"b",(function(){return E}));var n=i("1924"),r=i("6edb"),a=i("8937"),o=i("6618"),s=i("2e0c"),c=i("e897"),l=i("2974");function u(t){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}var h=c["a"].transform,f="-shape",d="-label",p=["startArrow","endArrow"],v={lineWidth:1,stroke:void 0,fill:void 0,lineAppendWidth:1,opacity:void 0,strokeOpacity:void 0,fillOpacity:void 0,x:0,y:0,r:10,width:20,height:20,shadowColor:void 0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0},g={lineWidth:1,stroke:"#000",lineDash:void 0,startArrow:!1,endArrow:!1,opacity:void 0,strokeOpacity:void 0,fillOpacity:void 0,shadowColor:void 0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0},m={edge:g,node:v,combo:v},y="-label-bg",b={options:{labelCfg:{style:{fontFamily:s["a"].windowFontFamily}},descriptionCfg:{style:{fontFamily:s["a"].windowFontFamily}}},itemType:"",type:"",getCustomConfig:function(t){return{}},getOptions:function(t,e){return"move"===e||(null===e||void 0===e?void 0:e.includes("bbox"))?{}:Object(a["deepMix"])({},this.options,this.getCustomConfig(t)||{},t)},draw:function(t,e){e["shapeMap"]={},this.mergeStyle=this.getOptions(t);var i=this.drawShape(t,e);if(i.set("className",this.itemType+f),e["shapeMap"][this.itemType+f]=i,t.label){var n=this.drawLabel(t,e);n.set("className",this.itemType+d),e["shapeMap"][this.itemType+d]=n}return i},afterDraw:function(t,e,i){},drawShape:function(t,e){return null},drawLabel:function(t,e){var i=(this.mergeStyle||this.getOptions(t)||{}).labelCfg,n=i||{},r=this.getLabelStyle(t,n,e),a=r.rotate;delete r.rotate;var o=e.addShape("text",{attrs:r,draggable:!0,className:"text-shape",name:"text-shape",labelRelated:!0});if(e["shapeMap"]["text-shape"]=o,!isNaN(a)&&""!==a){var s=o.getBBox(),c=[1,0,0,0,1,0,0,0,1];if(r.rotateCenter)switch(r.rotateCenter){case"center":c=h(c,[["t",-s.width/2,-s.height/2],["r",a],["t",s.width/2,s.height/2]]);break;case"lefttop":c=h(c,[["t",-r.x,-r.y],["r",a],["t",r.x,r.y]]);break;case"leftcenter":c=h(c,[["t",-r.x,-r.y-s.height/2],["r",a],["t",r.x,r.y+s.height/2]]);break;default:c=h(c,[["t",-s.width/2,-s.height/2],["r",a],["t",s.width/2,s.height/2]]);break}else c=h(c,[["t",-r.x,-r.y-s.height/2],["r",a],["t",r.x,r.y+s.height/2]]);o.setMatrix(c)}if(r.background){var l=this.drawLabelBg(t,e,o),u=this.itemType+y;l.set("classname",u),e["shapeMap"][u]=l,o.toFront()}return o},drawLabelBg:function(t,e,i){var n=this.options.labelCfg,r=Object(a["mix"])({},n,t.labelCfg),o=this.getLabelBgStyleByPosition(i,r),s=e.addShape("rect",{name:"text-bg-shape",attrs:o,labelRelated:!0});return e["shapeMap"]["text-bg-shape"]=s,s},getLabelStyleByPosition:function(t,e,i){return{text:t.label}},getLabelBgStyleByPosition:function(t,e){return{}},getLabelStyle:function(t,e,i){var n=this.getLabelStyleByPosition(t,e,i),a="".concat(this.itemType,"Label"),o=s["a"][a]?s["a"][a].style:null;return Object(r["a"])(Object(r["a"])(Object(r["a"])({},o),n),e.style)},getShapeStyle:function(t){return t.style},update:function(t,e,i){this.updateShapeStyle(t,e,i),this.updateLabel(t,e,i)},updateShapeStyle:function(t,e,i){var n,r=e.getContainer(),o=e.getKeyShape(),s=Object(a["mix"])({},o.attr(),t.style),c=function(t){var e,i=s[t];if(Object(a["isPlainObject"])(i)){var c=(null===(n=r["shapeMap"])||void 0===n?void 0:n[t])||r.find((function(e){return e.get("name")===t}));null===c||void 0===c||c.attr(i)}else o.attr((e={},e[t]=i,e))};for(var l in s)c(l)},updateLabel:function(t,e,i){var n,o;if(t.label||""===t.label){var s=e.getContainer(),c=(this.mergeStyle||this.getOptions({},i)||{}).labelCfg,l=void 0===c?{}:c,u=this.itemType+d,f=s["shapeMap"][u]||s.find((function(t){return t.get("className")===u})),p=this.itemType+y,v=s["shapeMap"][p]||s.find((function(t){return t.get("className")===p}));if(f){(!i||"bbox|label"===i||"edge"===this.itemType&&"style"!==i)&&(l=Object(a["deepMix"])(l,t.labelCfg));var g=this.getLabelStyleByPosition(t,l,s),m=null===(n=t.labelCfg)||void 0===n?void 0:n.style,b=Object(r["a"])(Object(r["a"])({},g),m),x=b.rotate;if(delete b.rotate,isNaN(x)||""===x)1!==(null===(o=f.getMatrix())||void 0===o?void 0:o[4])&&f.resetMatrix(),f.attr(b);else{var S=[1,0,0,0,1,0,0,0,1];S=h(S,[["t",-b.x,-b.y],["r",x],["t",b.x,b.y]]),b.matrix=S,f.attr(b)}if(v)if(b.background){var O=this.getLabelBgStyleByPosition(f,l);v.attr(O)}else s.removeChild(v);else b.background&&(v=this.drawLabelBg(t,s,f),v.set("classname",p),s["shapeMap"][p]=v,f.toFront())}else{var w=this.drawLabel(t,s);w.set("className",u),s["shapeMap"][u]=w}}},afterUpdate:function(t,e){},setState:function(t,e,i){var n,r,o,s=i.get("keyShape");if(s&&!s.destroyed){var c=i.getType(),h=Object(a["isBoolean"])(e)?t:"".concat(t,":").concat(e),f=this.getStateStyle(h,i),d=i.getStateStyle(h);if(d||f){var v=Object(a["mix"])({},d||f),g=i.getContainer(),y={x:1,y:1,cx:1,cy:1,matrix:1};if("combo"===c&&(y.r=1,y.width=1,y.height=1),e){var b=function(t){var e,i=v[t];if(Object(a["isPlainObject"])(i)&&!p.includes(t)){var n=(null===(o=g["shapeMap"])||void 0===o?void 0:o[t])||g.find((function(e){return e.get("name")===t}));null===n||void 0===n||n.attr(i)}else s.attr((e={},e[t]=i,e))};for(var x in v)b(x)}else{var S=Object(l["cloneBesidesImg"])(i.getCurrentStatesStyle()),O=i.getModel(),w=Object(a["mix"])({},O.style,Object(l["cloneBesidesImg"])(i.getOriginStyle())),k=s.get("name"),j=s.attr(),C={};Object.keys(j).forEach((function(t){if("img"!==t){var e=j[t];e&&"object"===u(e)?C[t]=Object(a["clone"])(e):C[t]=e}}));var I={},P=function(t){var e=v[t];if(Object(a["isPlainObject"])(e)&&!p.includes(t)){var i=g["shapeMap"][t]||g.find((function(e){return e.get("name")===t}));if(i){var n=Object(l["cloneBesidesImg"])(i.attr());Object(a["each"])(e,(function(e,r){if(t===k&&C[r]&&!y[r]){delete C[r];var a=w[t][r]||m[c][r];s.attr(r,a)}else if(n[r]||0===n[r]){delete n[r];var o=w[t][r]||m[c][r];i.attr(r,o)}})),I[t]=n}}else if(C[t]&&!y[t]){delete C[t];var r=w[t]||(w[k]?w[k][t]:void 0)||m[c][t];s.attr(t,r)}};for(var A in v)P(A);for(var x in k?I[k]=C:Object(a["mix"])(I,C),S)if(!y[x]){var M=S[x];Object(a["isPlainObject"])(M)&&!p.includes(x)||(k?(Object(a["mix"])(w[k],(r={},r[x]=M,r)),delete w[x]):Object(a["mix"])(w,(n={},n[x]=M,n)),delete S[x])}var E={};Object(a["deepMix"])(E,w,I,S);var _=!1,R=function(t){var e,i,n=E[t];if(Object(a["isPlainObject"])(n)&&!p.includes(t)){var r=g["shapeMap"][t]||g.find((function(e){return e.get("name")===t}));r&&(("text"===r.get("type")||r.get("labelRelated"))&&(delete n.x,delete n.y,delete n.matrix),t===k&&("combo"===c&&(delete n.r,delete n.width,delete n.height),_=!0),r.attr(n))}else if(!_){var o=n||m[c][t];"combo"===c?k||s.attr((e={},e[t]=o,e)):s.attr((i={},i[t]=o,i))}};for(var N in E)R(N)}}}},getStateStyle:function(t,e){var i=e.getModel(),n=e.getType(),r=this.getOptions(i),o=r.stateStyles,s=r.style,c=void 0===s?{}:s,l=i.stateStyles?i.stateStyles[t]:o&&o[t];return"combo"===n?Object(a["clone"])(l):Object(a["mix"])({},c,l)},getControlPoints:function(t){return t.controlPoints},getAnchorPoints:function(t){var e,i,n=(null===t||void 0===t?void 0:t.anchorPoints)||(null===(e=this.getCustomConfig(t))||void 0===e?void 0:e.anchorPoints)||(null===(i=this.options)||void 0===i?void 0:i.anchorPoints);return n}},x={itemType:"node",shapeType:"single-node",labelPosition:"center",offset:s["a"].nodeLabel.offset,getSize:function(t){var e,i=(null===(e=this.mergeStyle)||void 0===e?void 0:e.size)||t.size||this.getOptions({}).size||s["a"].defaultNode.size;return Object(a["isArray"])(i)&&1===i.length&&(i=[i[0],i[0]]),Object(a["isArray"])(i)||(i=[i,i]),i},getLabelStyleByPosition:function(t,e){var i=e.maxLength,n=t.label;i&&(n=Object(l["truncateLabelByLength"])(n,i));var r=e.position||this.labelPosition;if("center"===r)return{x:0,y:0,text:n,textBaseline:"middle",textAlign:"center"};var o=e.offset;Object(a["isNil"])(o)&&(o=this.offset);var s,c=this.getSize(t);switch(r){case"top":s={x:0,y:-c[1]/2-o,textBaseline:"bottom",textAlign:"center"};break;case"bottom":s={x:0,y:c[1]/2+o,textBaseline:"top",textAlign:"center"};break;case"left":s={x:-c[0]/2-o,y:0,textBaseline:"middle",textAlign:"right"};break;default:s={x:c[0]/2+o,y:0,textBaseline:"middle",textAlign:"left"};break}return s.text=n,s},getLabelBgStyleByPosition:function(t,e){var i;if(!t)return{};var n=null===(i=e.style)||void 0===i?void 0:i.background;if(!n)return{};var a=t.getBBox(),s=Object(o["formatPadding"])(n.padding),c=a.width+s[1]+s[3],l=a.height+s[0]+s[2];return Object(r["a"])(Object(r["a"])({x:a.minX-s[3],y:a.minY-s[0]},n),{width:c,height:l})},drawShape:function(t,e){var i=this.shapeType,n=this.getShapeStyle(t),r=e.addShape(i,{attrs:n,draggable:!0,name:"node-shape"});return e["shapeMap"]["node-shape"]=r,r},updateLinkPoints:function(t,e){var i,n=(this.mergeStyle||this.getOptions(t)).linkPoints,o=e["shapeMap"]["link-point-left"]||e.find((function(t){return"link-point-left"===t.get("className")})),s=e["shapeMap"]["link-point-right"]||e.find((function(t){return"link-point-right"===t.get("className")})),c=e["shapeMap"]["link-point-top"]||e.find((function(t){return"link-point-top"===t.get("className")})),l=e["shapeMap"]["link-point-bottom"]||e.find((function(t){return"link-point-bottom"===t.get("className")}));o&&(i=o.attr()),s&&!i&&(i=s.attr()),c&&!i&&(i=c.attr()),l&&!i&&(i=l.attr()),i||(i=n);var u=Object(a["mix"])({},i,t.linkPoints),h=u.fill,f=u.stroke,d=u.lineWidth,p=u.size/2;p||(p=u.r);var v=t.linkPoints?t.linkPoints:{left:void 0,right:void 0,top:void 0,bottom:void 0},g=v.left,m=v.right,y=v.top,b=v.bottom,x=this.getSize(t),S=x[0],O=x[1],w={r:p,fill:h,stroke:f,lineWidth:d};if(o)g||void 0===g?o.attr(Object(r["a"])(Object(r["a"])({},w),{x:-S/2,y:0})):(o.remove(),delete e["shapeMap"]["link-point-left"]);else if(g){var k="link-point-left";e["shapeMap"][k]=e.addShape("circle",{attrs:Object(r["a"])(Object(r["a"])({},w),{x:-S/2,y:0}),className:k,name:k,isAnchorPoint:!0})}if(s)m||void 0===m||(s.remove(),delete e["shapeMap"]["link-point-right"]),s.attr(Object(r["a"])(Object(r["a"])({},w),{x:S/2,y:0}));else if(m){var j="link-point-right";e["shapeMap"][j]=e.addShape("circle",{attrs:Object(r["a"])(Object(r["a"])({},w),{x:S/2,y:0}),className:j,name:j,isAnchorPoint:!0})}if(c)y||void 0===y||(c.remove(),delete e["shapeMap"]["link-point-top"]),c.attr(Object(r["a"])(Object(r["a"])({},w),{x:0,y:-O/2}));else if(y){var C="link-point-top";e["shapeMap"][C]=e.addShape("circle",{attrs:Object(r["a"])(Object(r["a"])({},w),{x:0,y:-O/2}),className:C,name:C,isAnchorPoint:!0})}if(l)b||void 0===b?l.attr(Object(r["a"])(Object(r["a"])({},w),{x:0,y:O/2})):(l.remove(),delete e["shapeMap"]["link-point-bottom"]);else if(b){var I="link-point-bottom";e["shapeMap"][I]=e.addShape("circle",{attrs:Object(r["a"])(Object(r["a"])({},w),{x:0,y:O/2}),className:I,name:I,isAnchorPoint:!0})}},updateShape:function(t,e,i,n,a){var o=e.get("keyShape");o.attr(Object(r["a"])({},i)),this.updateLabel(t,e,a),n&&this.updateIcon(t,e)},updateIcon:function(t,e){var i=this,n=e.getContainer(),o=(this.mergeStyle||this.getOptions(t)).icon,s=t.icon?t.icon:{show:void 0,text:void 0},c=s.show,l=s.text,u=n["shapeMap"]["".concat(this.type,"-icon")]||n.find((function(t){return t.get("name")==="".concat(i.type,"-icon")}));if(u)if(c||void 0===c){var h=Object(a["mix"])({},u.attr(),o),f=h.width,d=void 0===f?20:f,p=h.height,v=void 0===p?20:p;("iconfont"===h.fontFamily||h.hasOwnProperty("text"))&&(d=0,v=0),u.attr(Object(r["a"])(Object(r["a"])({},h),{x:-d/2,y:-v/2}))}else u.remove(),delete n["shapeMap"]["".concat(this.type,"-icon")];else if(c){var g="".concat(this.type,"-icon");if(l)n["shapeMap"][g]=n.addShape("text",{attrs:Object(r["a"])({x:0,y:0,fontSize:12,fill:"#000",stroke:"#000",textBaseline:"middle",textAlign:"center"},o),className:g,name:g});else{d=o.width,v=o.height;n["shapeMap"][g]=n.addShape("image",{attrs:Object(r["a"])(Object(r["a"])({},o),{x:-d/2,y:-v/2}),className:g,name:g})}var m=n["shapeMap"]["node-label"]||n.find((function(t){return"node-label"===t.get("name")}));m&&m.toFront()}}},S=Object(r["a"])(Object(r["a"])({},b),x);n["a"].registerNode("single-node",S);var O=i("adc7"),w=i("ae60"),k="edge-shape";function j(t){var e=t;return"start"===t?e="end":"end"===t&&(e="start"),e}var C={itemType:"edge",labelPosition:"center",refX:0,refY:0,labelAutoRotate:!1,options:{size:s["a"].defaultEdge.size,style:{x:0,y:0,stroke:s["a"].defaultEdge.style.stroke,lineAppendWidth:s["a"].defaultEdge.style.lineAppendWidth},labelCfg:{style:{fill:s["a"].edgeLabel.style.fill,fontSize:s["a"].edgeLabel.style.fontSize,fontFamily:s["a"].windowFontFamily}},stateStyles:Object(r["a"])({},s["a"].edgeStateStyles)},getPath:function(t){var e=[];return Object(a["each"])(t,(function(t,i){0===i?e.push(["M",t.x,t.y]):e.push(["L",t.x,t.y])})),e},getShapeStyle:function(t){var e=this.options.style,i={stroke:t.color},n=Object(a["mix"])({},e,i,t.style),r=t.size||s["a"].defaultEdge.size;t=this.getPathPoints(t);var o=t.startPoint,c=t.endPoint,l=this.getControlPoints(t),u=[o];l&&(u=u.concat(l)),u.push(c);var h=this.getPath(u),f=Object(a["mix"])({},s["a"].defaultEdge.style,{stroke:s["a"].defaultEdge.color,lineWidth:r,path:h},n);return f},updateShapeStyle:function(t,e,i){var n,o=e.getContainer(),s=(null===(n=e.getKeyShape)||void 0===n?void 0:n.call(e))||o["shapeMap"]["edge-shape"],c=t.size;t=this.getPathPoints(t);var l=t.startPoint,u=t.endPoint,h=this.getControlPoints(t),f=[l];h&&(f=f.concat(h)),f.push(u);var d=s.attr(),p=t.style||{};void 0===p.stroke&&(p.stroke=t.color);var v=t.sourceNode,g=t.targetNode,m={radius:p.radius};h||(m={source:v,target:g,offset:p.offset,radius:p.radius});var y=this.getPath(f,m),b={};"move"===i?b={path:y}:(d.endArrow&&!1===p.endArrow&&(t.style.endArrow={path:""}),d.startArrow&&!1===p.startArrow&&(t.style.startArrow={path:""}),b=Object(r["a"])({},t.style),void 0===b.lineWidth&&(b.lineWdith=(Object(a["isNumber"])(c)?c:null===c||void 0===c?void 0:c[0])||d.lineWidth),void 0===b.path&&(b.path=y),void 0===b.stroke&&(b.stroke=d.stroke||t.color)),s&&s.attr(b)},getLabelStyleByPosition:function(t,e,i){var n,r=e.position||this.labelPosition,o={},s=null===i||void 0===i?void 0:i["shapeMap"][k];n="start"===r?0:"end"===r?1:.5;var c,u=e.refX||this.refX,h=e.refY||this.refY;if(t.startPoint.x===t.endPoint.x&&t.startPoint.y===t.endPoint.y)return o.x=t.startPoint.x+u,o.y=t.startPoint.y+h,o.text=t.label,o;c=Object(a["isNil"])(e.autoRotate)?this.labelAutoRotate:e.autoRotate;var f=Object(l["getLabelPosition"])(s,n,u,h,c);return o.x=f.x,o.y=f.y,o.rotate=f.rotate,o.textAlign=this._getTextAlign(r,f.angle),o.text=t.label,o},getLabelBgStyleByPosition:function(t,e){if(!t)return{};var i=t.getBBox(),n=e.style&&e.style.background;if(!n)return{};var o,s=n.padding,c=i.width+s[1]+s[3],l=i.height+s[0]+s[2],u=Object(r["a"])(Object(r["a"])({},n),{width:c,height:l,x:i.minX-s[3],y:i.minY-s[0],matrix:[1,0,0,0,1,0,0,0,1]});return o=Object(a["isNil"])(e.autoRotate)?this.labelAutoRotate:e.autoRotate,o&&(u.matrix=t.attr("matrix")||[1,0,0,0,1,0,0,0,1]),u},_getTextAlign:function(t,e){var i="center";return e?(e%=2*Math.PI,"center"!==t&&(i=e>=0&&e<=Math.PI/2||e>=1.5*Math.PI&&e<2*Math.PI?t:j(t)),i):t},getControlPoints:function(t){return t.controlPoints},getPathPoints:function(t){return t},drawShape:function(t,e){var i=this.getShapeStyle(t),n=e.addShape("path",{className:k,name:k,attrs:i});return e["shapeMap"][k]=n,n},drawLabel:function(t,e){var i=this.options.labelCfg,n=Object(a["deepMix"])({},i,t.labelCfg),r=this.getLabelStyle(t,n,e),o=r.rotate;delete r.rotate;var s=e.addShape("text",{attrs:r,name:"text-shape",labelRelated:!0});if(e["shapeMap"]["text-shape"]=s,isNaN(o)||""===o||s.rotateAtStart(o),r.background){var c=this.drawLabelBg(t,e,s,r,o),l=this.itemType+y;c.set("classname",l),e["shapeMap"][l]=c,s.toFront()}return s},drawLabelBg:function(t,e,i,n,r){var o=this.options.labelCfg,s=Object(a["deepMix"])({},o,t.labelCfg),c=this.getLabelBgStyleByPosition(i,s),l=e.addShape("rect",{name:"text-bg-shape",attrs:c,labelRelated:!0});return e["shapeMap"]["text-bg-shape"]=l,l}},I=Object(r["a"])(Object(r["a"])({},b),C);n["a"].registerEdge("single-edge",I),n["a"].registerEdge("line",{getControlPoints:function(){}},"single-edge"),n["a"].registerEdge("spline",{getPath:function(t){var e=Object(w["getSpline"])(t);return e}},"single-edge"),n["a"].registerEdge("arc",{curveOffset:20,clockwise:1,getControlPoints:function(t){var e,i,n=t.startPoint,r=t.endPoint,o={x:(n.x+r.x)/2,y:(n.y+r.y)/2};if(void 0!==t.controlPoints){if(i=t.controlPoints[0],e=Object(O["getCircleCenterByPoints"])(n,i,r),n.x<=r.x&&n.y>r.y?this.clockwise=e.x>i.x?0:1:n.x<=r.x&&n.y<r.y?this.clockwise=e.x>i.x?1:0:n.x>r.x&&n.y<=r.y?this.clockwise=e.y<i.y?0:1:this.clockwise=e.y<i.y?1:0,(i.x-n.x)/(i.y-n.y)===(r.x-n.x)/(r.y-n.y))return[]}else{void 0===t.curveOffset&&(t.curveOffset=this.curveOffset),Object(a["isArray"])(t.curveOffset)&&(t.curveOffset=t.curveOffset[0]),t.curveOffset<0?this.clockwise=0:this.clockwise=1;var s={x:r.x-n.x,y:r.y-n.y},c=Math.atan2(s.y,s.x);i={x:t.curveOffset*Math.cos(-Math.PI/2+c)+o.x,y:t.curveOffset*Math.sin(-Math.PI/2+c)+o.y},e=Object(O["getCircleCenterByPoints"])(n,i,r)}var l=Object(O["distance"])(n,e),u=[{x:l,y:l}];return u},getPath:function(t){var e=[];return e.push(["M",t[0].x,t[0].y]),2===t.length?e.push(["L",t[1].x,t[1].y]):e.push(["A",t[1].x,t[1].y,0,0,this.clockwise,t[2].x,t[2].y]),e}},"single-edge"),n["a"].registerEdge("quadratic",{curvePosition:.5,curveOffset:-20,getControlPoints:function(t){var e=t.controlPoints;if(!e||!e.length){var i=t.startPoint,n=t.endPoint;void 0===t.curveOffset&&(t.curveOffset=this.curveOffset),void 0===t.curvePosition&&(t.curvePosition=this.curvePosition),Object(a["isArray"])(this.curveOffset)&&(t.curveOffset=t.curveOffset[0]),Object(a["isArray"])(this.curvePosition)&&(t.curvePosition=t.curveOffset[0]);var r=Object(w["getControlPoint"])(i,n,t.curvePosition,t.curveOffset);e=[r]}return e},getPath:function(t){var e=[];return e.push(["M",t[0].x,t[0].y]),e.push(["Q",t[1].x,t[1].y,t[2].x,t[2].y]),e}},"single-edge"),n["a"].registerEdge("cubic",{curvePosition:[.5,.5],curveOffset:[-20,20],getControlPoints:function(t){var e=t.controlPoints;if(void 0===t.curveOffset&&(t.curveOffset=this.curveOffset),void 0===t.curvePosition&&(t.curvePosition=this.curvePosition),Object(a["isNumber"])(t.curveOffset)&&(t.curveOffset=[t.curveOffset,-t.curveOffset]),Object(a["isNumber"])(t.curvePosition)&&(t.curvePosition=[t.curvePosition,1-t.curvePosition]),!e||!e.length||e.length<2){var i=t.startPoint,n=t.endPoint,r=Object(w["getControlPoint"])(i,n,t.curvePosition[0],t.curveOffset[0]),o=Object(w["getControlPoint"])(i,n,t.curvePosition[1],t.curveOffset[1]);e=[r,o]}return e},getPath:function(t){var e=[];return e.push(["M",t[0].x,t[0].y]),e.push(["C",t[1].x,t[1].y,t[2].x,t[2].y,t[3].x,t[3].y]),e}},"single-edge"),n["a"].registerEdge("cubic-vertical",{curvePosition:[.5,.5],minCurveOffset:[0,0],curveOffset:void 0,getControlPoints:function(t){var e=t.startPoint,i=t.endPoint;void 0===t.curvePosition&&(t.curvePosition=this.curvePosition),void 0===t.curveOffset&&(t.curveOffset=this.curveOffset),void 0===t.minCurveOffset&&(t.minCurveOffset=this.minCurveOffset),Object(a["isNumber"])(t.curveOffset)&&(t.curveOffset=[t.curveOffset,-t.curveOffset]),Object(a["isNumber"])(t.minCurveOffset)&&(t.minCurveOffset=[t.minCurveOffset,-t.minCurveOffset]),Object(a["isNumber"])(t.curvePosition)&&(t.curvePosition=[t.curvePosition,1-t.curvePosition]);var n=i.y-e.y,r=[0,0];t.curveOffset?r=t.curveOffset:Math.abs(n)<Math.abs(t.minCurveOffset[0])&&(r=t.minCurveOffset);var o={x:e.x,y:e.y+n*this.curvePosition[0]+r[0]},s={x:i.x,y:i.y-n*this.curvePosition[1]+r[1]};return[o,s]}},"cubic"),n["a"].registerEdge("cubic-horizontal",{curvePosition:[.5,.5],minCurveOffset:[0,0],curveOffset:void 0,getControlPoints:function(t){var e=t.startPoint,i=t.endPoint;void 0===t.curvePosition&&(t.curvePosition=this.curvePosition),void 0===t.curveOffset&&(t.curveOffset=this.curveOffset),void 0===t.minCurveOffset&&(t.minCurveOffset=this.minCurveOffset),Object(a["isNumber"])(t.curveOffset)&&(t.curveOffset=[t.curveOffset,-t.curveOffset]),Object(a["isNumber"])(t.minCurveOffset)&&(t.minCurveOffset=[t.minCurveOffset,-t.minCurveOffset]),Object(a["isNumber"])(t.curvePosition)&&(t.curvePosition=[t.curvePosition,1-t.curvePosition]);var n=i.x-e.x,r=[0,0];t.curveOffset?r=t.curveOffset:Math.abs(n)<Math.abs(t.minCurveOffset[0])&&(r=t.minCurveOffset);var o={x:e.x+n*this.curvePosition[0]+r[0],y:e.y},s={x:i.x-n*this.curvePosition[1]+r[1],y:i.y},c=[o,s];return c}},"cubic"),n["a"].registerEdge("loop",{getPathPoints:function(t){return Object(l["getLoopCfgs"])(t)},getControlPoints:function(t){return t.controlPoints},afterDraw:function(t){t.controlPoints=void 0},afterUpdate:function(t){t.controlPoints=void 0}},"cubic");var P={itemType:"combo",shapeType:"single-combo",labelPosition:"top",refX:s["a"].comboLabel.refX,refY:s["a"].comboLabel.refY,options:{style:{stroke:s["a"].defaultCombo.style.stroke,fill:s["a"].defaultCombo.style.fill,lineWidth:s["a"].defaultCombo.style.lineWidth},labelCfg:{style:{fill:s["a"].comboLabel.style.fill,fontSize:s["a"].comboLabel.style.fontSize,fontFamily:s["a"].windowFontFamily}},stateStyles:Object(r["a"])({},s["a"].comboStateStyles),collapsedSubstituteIcon:{show:!1,img:"https://gw.alipayobjects.com/mdn/rms_f8c6a0/afts/img/A*RsnHRqLfJn4AAAAAAAAAAAAAARQnAQ"}},getSize:function(t){var e=Object(a["clone"])(t.size||this.options.size||s["a"].defaultCombo.size);return Object(a["isArray"])(e)&&1===e.length&&(e=[e[0],e[0]]),Object(a["isArray"])(e)||(e=[e,e]),e},getLabelStyleByPosition:function(t,e){var i=e.position||this.labelPosition,n=t.style,r=t.padding||this.options.padding;Object(a["isArray"])(r)&&(r=r[0]);var o=e.refX,s=e.refY;Object(a["isNil"])(o)&&(o=this.refX),Object(a["isNil"])(s)&&(s=this.refY);var c,l=this.getSize(t),u=Math.max(n.r,l[0]/2)||l[0]/2,h=u+r;switch(i){case"top":c={x:0,y:-h-s,textBaseline:"bottom",textAlign:"center"};break;case"bottom":c={x:0,y:h+s,textBaseline:"bottom",textAlign:"center"};break;case"left":c={x:-h+o,y:0,textAlign:"left"};break;case"center":c={x:0,y:0,text:t.label,textAlign:"center"};break;default:c={x:h+o,y:0,textAlign:"right"};break}return c.text=t.label,c},drawShape:function(t,e){var i=this.shapeType,n=this.getShapeStyle(t),r=e.addShape(i,{attrs:n,draggable:!0,name:"combo-shape"});return r},updateCollapsedIcon:function(t,e,i){var n=t.collapsed,a=t.collapsedSubstituteIcon,o=void 0===a?{}:a,s=Object.assign({},this.options.collapsedSubstituteIcon,o),c=s.show,l=s.img,u=s.width,h=s.height,f=e.getContainer(),d=f.find((function(t){return"combo-collapsed-substitute-icon"===t.get("name")})),p=d&&!d.destroyed;if(n&&c)if(p)d.show();else{var v={width:u||2*i.r||i.width,height:h||2*i.r||i.height};f.addShape("image",{attrs:Object(r["a"])({img:l,x:-v.width/2,y:-v.height/2},v),name:"combo-collapsed-substitute-icon",draggable:!0})}else p&&d.hide()},updateShape:function(t,e,i){var n=this,a=e.get("keyShape"),o=e.get("animate"),s=o&&(void 0===t.animate?this.options.animate:t.animate);s&&a.animate?(t.collapsed||this.updateCollapsedIcon(t,e,i),a.animate(i,{duration:200,easing:"easeLinear",callback:function(){t.collapsed&&n.updateCollapsedIcon(t,e,i)}})):(a.attr(Object(r["a"])({},i)),this.updateCollapsedIcon(t,e,i)),this.updateLabel(t,e)}},A=Object(r["a"])(Object(r["a"])({},b),P);n["a"].registerCombo("single-combo",A),n["a"].registerCombo("circle",{options:{size:[s["a"].defaultCombo.size[0],s["a"].defaultCombo.size[0]],padding:s["a"].defaultCombo.padding[0],animate:!0,style:{stroke:s["a"].defaultCombo.style.stroke,fill:s["a"].defaultCombo.style.fill,lineWidth:s["a"].defaultCombo.style.lineWidth},labelCfg:{style:{fill:s["a"].comboLabel.style.fill,fontSize:s["a"].comboLabel.style.fontSize},refX:0,refY:0},stateStyles:Object(r["a"])({},s["a"].comboStateStyles),collapsedSubstituteIcon:{show:!1,img:"https://gw.alipayobjects.com/mdn/rms_f8c6a0/afts/img/A*RsnHRqLfJn4AAAAAAAAAAAAAARQnAQ"}},shapeType:"circle",labelPosition:"top",drawShape:function(t,e){var i=this.getShapeStyle(t);delete i.height,delete i.width;var n=e.addShape("circle",{attrs:i,className:"circle-combo",name:"circle-combo",draggable:!0});return n},getShapeStyle:function(t){var e=this.options.style,i=t.padding||this.options.padding;Object(a["isArray"])(i)&&(i=i[0]);var n,o={stroke:t.color},c=Object(a["mix"])({},e,o,t.style);if(t.fixSize)n=Object(a["isNumber"])(t.fixSize)?t.fixSize:t.fixSize[0];else{var l=this.getSize(t);n=!Object(a["isNumber"])(c.r)||isNaN(c.r)?l[0]/2||s["a"].defaultCombo.style.r:Math.max(c.r,l[0]/2)||l[0]/2}c.r=n+i;var u=Object(r["a"])({x:0,y:0},c);return t.style?t.style.r=n:t.style={r:n},u},update:function(t,e){var i=this.getSize(t),n=t.padding||this.options.padding;Object(a["isArray"])(n)&&(n=n[0]);var r,o=Object(a["clone"])(t.style);r=t.fixSize?Object(a["isNumber"])(t.fixSize)?t.fixSize:t.fixSize[0]:Math.max(o.r,i[0]/2)||i[0]/2,o.r=r+n;var s=e.get("sizeCache");s&&(s.r=o.r);var c={stroke:t.color},l=e.get("keyShape"),u=Object(a["mix"])({},l.attr(),c,o);t.style?t.style.r=r:t.style={r:r},this.updateShape(t,e,u,!0)}},"single-combo"),n["a"].registerCombo("rect",{options:{size:[40,5],padding:[25,20,15,20],animate:!0,style:{radius:0,stroke:s["a"].defaultCombo.style.stroke,fill:s["a"].defaultCombo.style.fill,lineWidth:s["a"].defaultCombo.style.lineWidth},labelCfg:{style:{fill:s["a"].comboLabel.style.fill,fontSize:s["a"].comboLabel.style.fontSize,fontFamily:s["a"].windowFontFamily}},anchorPoints:[[0,.5],[1,.5]],stateStyles:Object(r["a"])({},s["a"].comboStateStyles),collapsedSubstituteIcon:{show:!1,img:"https://gw.alipayobjects.com/mdn/rms_f8c6a0/afts/img/A*RsnHRqLfJn4AAAAAAAAAAAAAARQnAQ"}},shapeType:"rect",labelPosition:"top",drawShape:function(t,e){var i=this.getShapeStyle(t),n=e.addShape("rect",{attrs:i,className:"rect-combo",name:"rect-combo",draggable:!0});return n},getLabelStyleByPosition:function(t,e){var i=e.position||this.labelPosition,n=t.style,r=t.padding||this.options.padding;Object(a["isNumber"])(r)&&(r=[r,r,r,r]);var o=e.refX,s=e.refY;Object(a["isNil"])(o)&&(o=this.refX),Object(a["isNil"])(s)&&(s=this.refY);var c,l=n.width/2+r[3],u=n.height/2+r[0];switch(i){case"top":c={x:0-l+o,y:0-u+s,textBaseline:"top",textAlign:"left"};break;case"bottom":c={x:0,y:u+s,textBaseline:"bottom",textAlign:"center"};break;case"left":c={x:0-l+s,y:0,textAlign:"left"};break;case"center":c={x:0,y:0,text:t.label,textAlign:"center"};break;default:c={x:l+o,y:0,textAlign:"right"};break}return c.text=t.label,c},getShapeStyle:function(t){var e=this.options.style,i=t.padding||this.options.padding;Object(a["isNumber"])(i)&&(i=[i,i,i,i]);var n,o,c={stroke:t.color},l=Object(a["mix"])({},e,c,t.style),u=this.getSize(t),h=t.collapsed&&t.fixCollapseSize?t.fixCollapseSize:t.fixSize;h?Object(a["isNumber"])(h)?(n=h,o=h):(n=h[0],o=h[1]):(n=!Object(a["isNumber"])(l.width)||isNaN(l.width)?u[0]||s["a"].defaultCombo.style.width:Math.max(l.width,u[0])||u[0],o=!Object(a["isNumber"])(l.height)||isNaN(l.height)?u[1]||s["a"].defaultCombo.style.height:Math.max(l.height,u[1])||u[1]);var f=-n/2-i[3],d=-o/2-i[0];l.width=n+i[1]+i[3],l.height=o+i[0]+i[2];var p=Object(r["a"])({x:f,y:d},l);return t.style?(t.style.width=n,t.style.height=o):t.style={width:n,height:o},p},update:function(t,e){var i=this.getSize(t),n=t.padding||this.options.padding;Object(a["isNumber"])(n)&&(n=[n,n,n,n]);var r,o,s=Object(a["clone"])(t.style),c=t.collapsed&&t.fixCollapseSize?t.fixCollapseSize:t.fixSize;c?Object(a["isNumber"])(c)?(r=c,o=c):(r=c[0],o=c[1]):(r=Math.max(s.width,i[0])||i[0],o=Math.max(s.height,i[1])||i[1]),s.width=r+n[1]+n[3],s.height=o+n[0]+n[2];var l=e.get("sizeCache");l&&(l.width=s.width,l.height=s.height),s.x=-r/2-n[3],s.y=-o/2-n[0];var u={stroke:t.color},h=e.get("keyShape"),f=Object(a["mix"])({},h.attr(),u,s);t.style?(t.style.width=r,t.style.height=o):t.style={width:r,height:o},this.updateShape(t,e,f,!1)}},"single-combo"),n["a"].registerNode("simple-circle",{options:{size:s["a"].defaultNode.size,style:{x:0,y:0,stroke:s["a"].defaultNode.style.stroke,fill:s["a"].defaultNode.style.fill,lineWidth:s["a"].defaultNode.style.lineWidth},labelCfg:{style:{fill:s["a"].nodeLabel.style.fill,fontSize:s["a"].nodeLabel.style.fontSize,fontFamily:s["a"].windowFontFamily}},stateStyles:Object(r["a"])({},s["a"].nodeStateStyles)},shapeType:"simple-circle",labelPosition:"center",shapeMap:{},drawShape:function(t,e){var i=this.getShapeStyle(t),n="".concat(this.type,"-keyShape"),r=e.addShape("circle",{attrs:i,className:"".concat(this.type,"-keyShape"),name:n,draggable:!0});return e["shapeMap"][n]=r,r},getShapeStyle:function(t){var e=(this.mergeStyle||this.getOptions(t)).style,i={stroke:t.color},n=Object(a["deepMix"])({},e,i),o=this.getSize(t),s=o[0]/2,c=Object(r["a"])({x:0,y:0,r:s},n);return c},update:function(t,e,i){var n=this.getSize(t),r={stroke:t.color,r:n[0]/2},o=e.get("keyShape"),s=Object(a["deepMix"])({},o.attr(),r,t.style);this.updateShape(t,e,s,!0,i)}},"single-node"),n["a"].registerNode("simple-rect",{options:{size:[100,30],style:{radius:0,stroke:s["a"].defaultNode.style.stroke,fill:s["a"].defaultNode.style.fill,lineWidth:s["a"].defaultNode.style.lineWidth},labelCfg:{style:{fill:s["a"].nodeLabel.style.fill,fontSize:s["a"].nodeLabel.style.fontSize,fontFamily:s["a"].windowFontFamily}},anchorPoints:[[0,.5],[1,.5]],stateStyles:Object(r["a"])({},s["a"].nodeStateStyles)},shapeType:"simple-rect",labelPosition:"center",drawShape:function(t,e){var i=this.getShapeStyle(t),n=e.addShape("rect",{attrs:i,className:"".concat(this.type,"-keyShape"),name:"".concat(this.type,"-keyShape"),draggable:!0});return n},getShapeStyle:function(t){var e=(this.mergeStyle||this.getOptions(t)).style,i={stroke:t.color},n=Object(a["mix"])({},e,i),o=this.getSize(t),s=n.width||o[0],c=n.height||o[1],l=Object(r["a"])({x:-s/2,y:-c/2,width:s,height:c},n);return l},update:function(t,e,i){e.getContainer();var n=(this.mergeStyle||this.getOptions(t)).style,r=this.getSize(t),o=e.get("keyShape");t.size||(r[0]=o.attr("width")||n.width,r[1]=o.attr("height")||n.height);var s={stroke:t.color,x:-r[0]/2,y:-r[1]/2,width:r[0],height:r[1]},c=Object(a["mix"])({},n,o.attr(),s);c=Object(a["mix"])(c,t.style),this.updateShape(t,e,c,!1,i)}},"single-node"),n["a"].registerNode("image",{options:{img:"https://gw.alipayobjects.com/mdn/rms_f8c6a0/afts/img/A*eD7nT6tmYgAAAAAAAAAAAABkARQnAQ",size:200,labelCfg:{style:{fontFamily:s["a"].windowFontFamily}},clipCfg:{show:!1,type:"circle",r:50,rx:50,ry:35,width:50,height:35,points:[[30,12],[12,30],[30,48],[48,30]],path:[["M",25,25],["L",50,25],["A",12.5,12.5,0,1,1,50,50],["A",12.5,12.5,0,1,0,50,50],["L",25,75],["Z"]],x:0,y:0}},shapeType:"image",labelPosition:"bottom",drawShape:function(t,e){var i=this.shapeType,n=this.getShapeStyle(t);delete n.fill;var r=e.addShape(i,{attrs:n,className:"".concat(this.type,"-keyShape"),name:"".concat(this.type,"-keyShape"),draggable:!0});return this.drawClip(t,r),r},drawClip:function(t,e){var i=(this.mergeStyle||this.getOptions(t)).clipCfg;if(i.show){var n=i.type,a=i.x,o=i.y,s=i.style;if("circle"===n){var c=i.r;e.setClip({type:"circle",attrs:Object(r["a"])({r:c,x:a,y:o},s)})}else if("rect"===n){var l=i.width,u=i.height,h=a-l/2,f=o-u/2;e.setClip({type:"rect",attrs:Object(r["a"])({x:h,y:f,width:l,height:u},s)})}else if("ellipse"===n){var d=i.rx,p=i.ry;e.setClip({type:"ellipse",attrs:Object(r["a"])({x:a,y:o,rx:d,ry:p},s)})}else if("polygon"===n){var v=i.points;e.setClip({type:"polygon",attrs:Object(r["a"])({points:v},s)})}else if("path"===n){var g=i.path;e.setClip({type:"path",attrs:Object(r["a"])({path:g},s)})}}},getShapeStyle:function(t){var e=this.mergeStyle||this.getOptions(t),i=e.style,n=e.img,a=this.getSize(t),o=a[0],s=a[1];i&&(o=i.width||a[0],s=i.height||a[1]);var c=Object(r["a"])({x:-o/2,y:-s/2,width:o,height:s,img:n},i);return c},updateShapeStyle:function(t,e){var i=e.getContainer(),n="".concat(this.itemType,"-shape"),r=i["shapeMap"][n]||i.find((function(t){return t.get("className")===n}))||e.getKeyShape(),a=this.getShapeStyle(t);r&&!r.destroyed&&r.attr(a)}},"single-node");var M={triangle:function(t,e,i){void 0===t&&(t=10),void 0===e&&(e=15),void 0===i&&(i=0);var n=2*i,r="M ".concat(n,",0 L ").concat(n+e,",-").concat(t/2," L ").concat(n+e,",").concat(t/2," Z");return r},vee:function(t,e,i){void 0===t&&(t=15),void 0===e&&(e=20),void 0===i&&(i=0);var n=2*i,r="M ".concat(n,",0 L ").concat(n+e,",-").concat(t/2,"\n        L ").concat(n+2*e/3,",0 L ").concat(n+e,",").concat(t/2," Z");return r},circle:function(t,e){void 0===t&&(t=5),void 0===e&&(e=0);var i=2*e,n="M ".concat(i,", 0\n            a ").concat(t,",").concat(t," 0 1,0 ").concat(2*t,",0\n            a ").concat(t,",").concat(t," 0 1,0 ").concat(2*-t,",0");return n},rect:function(t,e,i){void 0===t&&(t=10),void 0===e&&(e=10),void 0===i&&(i=0);var n=2*i,r="M ".concat(n,",").concat(-t/2," \n        L ").concat(n+e,",").concat(-t/2," \n        L ").concat(n+e,",").concat(t/2," \n        L ").concat(n,",").concat(t/2," Z");return r},diamond:function(t,e,i){void 0===t&&(t=15),void 0===e&&(e=15),void 0===i&&(i=0);var n=2*i,r="M ".concat(n,",0 \n        L ").concat(n+e/2,",").concat(-t/2," \n        L ").concat(n+e,",0 \n        L ").concat(n+e/2,",").concat(t/2," Z");return r},triangleRect:function(t,e,i,n,r,a){void 0===t&&(t=15),void 0===e&&(e=15),void 0===i&&(i=15),void 0===n&&(n=3),void 0===r&&(r=5),void 0===a&&(a=0);var o=2*a,s=o+e+r,c="M ".concat(o,",0 L ").concat(o+e,",-").concat(t/2," L ").concat(o+e,",").concat(t/2," Z\n            M ").concat(s,", -").concat(i/2,"\n            L ").concat(s+n," -").concat(i/2,"\n            L ").concat(s+n," ").concat(i/2,"\n            L ").concat(s," ").concat(i/2,"\n            Z");return c}},E={collapse:function(t,e,i){return[["M",t-i,e],["a",i,i,0,1,0,2*i,0],["a",i,i,0,1,0,2*-i,0],["M",t-i+4,e],["L",t+i-4,e]]},expand:function(t,e,i){return[["M",t-i,e],["a",i,i,0,1,0,2*i,0],["a",i,i,0,1,0,2*-i,0],["M",t-i+4,e],["L",t-i+2*i-4,e],["M",t-i+i,e-i+4],["L",t,e+i-4]]},upTriangle:function(t,e,i){var n=i*Math.cos(Math.PI/6),r=i*Math.sin(Math.PI/6);return[["M",t-n,e+r],["L",t+n,e+r],["L",t,e-i],["Z"]]},downTriangle:function(t,e,i){var n=i*Math.cos(Math.PI/6),r=i*Math.sin(Math.PI/6);return[["M",t-n,e-r],["L",t+n,e-r],["L",t,e+i],["Z"]]}};e["c"]=n["a"]}}]);