(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6633cd95"],{"099d":function(t,e,n){"use strict";function i(t){return t<0?0:t>255?255:0|t}function r(t){return t<0?0:t>4294967295?4294967295:0|t}n.d(e,"b",(function(){return i})),n.d(e,"a",(function(){return r}))},"10b9":function(t,e,n){"use strict";n.d(e,"a",(function(){return r}));var i=n("3742");function r(t,e){if(t&&""!==t[0]){var n=o(t,e,"-"),r=o(t,e,"_");return n&&!r?s(t,e,"-"):!n&&r?s(t,e,"_"):t[0].toUpperCase()===t[0]?e.toUpperCase():t[0].toLowerCase()===t[0]?e.toLowerCase():i["j"](t[0][0])?e[0].toUpperCase()+e.substr(1):e}return e}function o(t,e,n){var i=-1!==t[0].indexOf(n)&&-1!==e.indexOf(n);return i&&t[0].split(n).length===e.split(n).length}function s(t,e,n){var i=e.split(n),o=t[0].split(n),s="";return i.forEach((function(t,e){s+=r([o[e]],t)+n})),s.slice(0,-1)}},"1b1f":function(t,e,n){"use strict";n.d(e,"b",(function(){return l})),n.d(e,"a",(function(){return p}));var i=n("fdcc"),r=n("a666"),o=n("30db"),s=n("ef8e"),a=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},t(e,n)};return function(e,n){function i(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),h="$initialize",u=!1;function l(t){o["g"]&&(u||(u=!0,console.warn("Could not create web worker(s). Falling back to loading web worker code in main thread, which might cause UI freezes. Please see https://github.com/Microsoft/monaco-editor#faq")),console.warn(t.message))}var c=function(){function t(t){this._workerId=-1,this._handler=t,this._lastSentReq=0,this._pendingReplies=Object.create(null)}return t.prototype.setWorkerId=function(t){this._workerId=t},t.prototype.sendMessage=function(t,e){var n=this,i=String(++this._lastSentReq);return new Promise((function(r,o){n._pendingReplies[i]={resolve:r,reject:o},n._send({vsWorker:n._workerId,req:i,method:t,args:e})}))},t.prototype.handleMessage=function(t){t&&t.vsWorker&&(-1!==this._workerId&&t.vsWorker!==this._workerId||this._handleMessage(t))},t.prototype._handleMessage=function(t){var e=this;if(t.seq){var n=t;if(!this._pendingReplies[n.seq])return void console.warn("Got reply to unknown seq");var r=this._pendingReplies[n.seq];if(delete this._pendingReplies[n.seq],n.err){var o=n.err;return n.err.$isError&&(o=new Error,o.name=n.err.name,o.message=n.err.message,o.stack=n.err.stack),void r.reject(o)}r.resolve(n.res)}else{var s=t,a=s.req,h=this._handler.handleMessage(s.method,s.args);h.then((function(t){e._send({vsWorker:e._workerId,seq:a,res:t,err:void 0})}),(function(t){t.detail instanceof Error&&(t.detail=Object(i["g"])(t.detail)),e._send({vsWorker:e._workerId,seq:a,res:void 0,err:Object(i["g"])(t)})}))}},t.prototype._send=function(t){var e=[];if(t.req)for(var n=t,i=0;i<n.args.length;i++)n.args[i]instanceof ArrayBuffer&&e.push(n.args[i]);else{n=t;n.res instanceof ArrayBuffer&&e.push(n.res)}this._handler.sendMessage(t,e)},t}(),p=function(t){function e(e,n,i){var r=t.call(this)||this,o=null;r._worker=r._register(e.create("vs/base/common/worker/simpleWorker",(function(t){r._protocol.handleMessage(t)}),(function(t){o&&o(t)}))),r._protocol=new c({sendMessage:function(t,e){r._worker.postMessage(t,e)},handleMessage:function(t,e){if("function"!==typeof i[t])return Promise.reject(new Error("Missing method "+t+" on main thread host."));try{return Promise.resolve(i[t].apply(i,e))}catch(n){return Promise.reject(n)}}}),r._protocol.setWorkerId(r._worker.getId());var a=null;"undefined"!==typeof self.require&&"function"===typeof self.require.getConfig?a=self.require.getConfig():"undefined"!==typeof self.requirejs&&(a=self.requirejs.s.contexts._.config);var u=s["c"](i);r._onModuleLoaded=r._protocol.sendMessage(h,[r._worker.getId(),JSON.parse(JSON.stringify(a)),n,u]);var l=function(t,e){return r._request(t,e)};return r._lazyProxy=new Promise((function(t,e){o=e,r._onModuleLoaded.then((function(e){t(s["b"](e,l))}),(function(t){e(t),r._onError("Worker failed to load "+n,t)}))})),r}return a(e,t),e.prototype.getProxyObject=function(){return this._lazyProxy},e.prototype._request=function(t,e){var n=this;return new Promise((function(i,r){n._onModuleLoaded.then((function(){n._protocol.sendMessage(t,e).then(i,r)}),r)}))},e.prototype._onError=function(t,e){console.error(t),console.info(e)},e}(r["a"]);(function(){function t(t,e){var n=this;this._requestHandlerFactory=e,this._requestHandler=null,this._protocol=new c({sendMessage:function(e,n){t(e,n)},handleMessage:function(t,e){return n._handleMessage(t,e)}})}t.prototype.onmessage=function(t){this._protocol.handleMessage(t)},t.prototype._handleMessage=function(t,e){if(t===h)return this.initialize(e[0],e[1],e[2],e[3]);if(!this._requestHandler||"function"!==typeof this._requestHandler[t])return Promise.reject(new Error("Missing requestHandler or method: "+t));try{return Promise.resolve(this._requestHandler[t].apply(this._requestHandler,e))}catch(n){return Promise.reject(n)}},t.prototype.initialize=function(t,e,n,i){var r=this;this._protocol.setWorkerId(t);var o=function(t,e){return r._protocol.sendMessage(t,e)},a=s["b"](i,o);return this._requestHandlerFactory?(this._requestHandler=this._requestHandlerFactory(a),Promise.resolve(s["c"](this._requestHandler))):(e&&("undefined"!==typeof e.baseUrl&&delete e["baseUrl"],"undefined"!==typeof e.paths&&"undefined"!==typeof e.paths.vs&&delete e.paths["vs"],e.catchError=!0,self.require.config(e)),new Promise((function(t,e){self.require([n],(function(n){r._requestHandler=n.create(a),r._requestHandler?t(s["c"](r._requestHandler)):e(new Error("No RequestHandler!"))}),e)})))}})()},"258a":function(t,e,n){"use strict";n.d(e,"c",(function(){return o})),n.d(e,"d",(function(){return i})),n.d(e,"f",(function(){return a})),n.d(e,"a",(function(){return h})),n.d(e,"b",(function(){return u})),n.d(e,"e",(function(){return l}));var i,r=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},t(e,n)};return function(e,n){function i(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),o={done:!0,value:void 0};(function(t){var e={next:function(){return o}};function n(){return e}function i(t){var e=!1;return{next:function(){return e?o:(e=!0,{done:!1,value:t})}}}function r(t,e,n){return void 0===e&&(e=0),void 0===n&&(n=t.length),{next:function(){return e>=n?o:{done:!1,value:t[e++]}}}}function a(t){return{next:function(){var e=t.next();return e.done?o:{done:!1,value:e.value}}}}function h(e){return e?Array.isArray(e)?t.fromArray(e):e:t.empty()}function u(t,e){return{next:function(){var n=t.next();return n.done?o:{done:!1,value:e(n.value)}}}}function l(t,e){return{next:function(){while(1){var n=t.next();if(n.done)return o;if(e(n.value))return{done:!1,value:n.value}}}}}function c(t,e){for(var n=t.next();!n.done;n=t.next())e(n.value)}function p(t,e){void 0===e&&(e=Number.POSITIVE_INFINITY);var n=[];if(0===e)return n;for(var i=0,r=t.next();!r.done;r=t.next())if(n.push(r.value),++i>=e)break;return n}function d(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var n=0;return{next:function(){if(n>=t.length)return o;var e=t[n],i=e.next();return i.done?(n++,this.next()):i}}}function f(t){return new s(t)}t.empty=n,t.single=i,t.fromArray=r,t.fromNativeIterator=a,t.from=h,t.map=u,t.filter=l,t.forEach=c,t.collect=p,t.concat=d,t.chain=f})(i||(i={}));var s=function(){function t(t){this.it=t}return t.prototype.next=function(){return this.it.next()},t}();function a(t){return Array.isArray(t)?i.fromArray(t):t||i.empty()}var h=function(){function t(t,e,n,i){void 0===e&&(e=0),void 0===n&&(n=t.length),void 0===i&&(i=e-1),this.items=t,this.start=e,this.end=n,this.index=i}return t.prototype.first=function(){return this.index=this.start,this.current()},t.prototype.next=function(){return this.index=Math.min(this.index+1,this.end),this.current()},t.prototype.current=function(){return this.index===this.start-1||this.index===this.end?null:this.items[this.index]},t}(),u=function(t){function e(e,n,i,r){return void 0===n&&(n=0),void 0===i&&(i=e.length),void 0===r&&(r=n-1),t.call(this,e,n,i,r)||this}return r(e,t),e.prototype.current=function(){return t.prototype.current.call(this)},e.prototype.previous=function(){return this.index=Math.max(this.index-1,this.start-1),this.current()},e.prototype.first=function(){return this.index=this.start,this.current()},e.prototype.last=function(){return this.index=this.end-1,this.current()},e.prototype.parent=function(){return null},e}(h),l=function(){function t(t,e){this.iterator=t,this.fn=e}return t.prototype.next=function(){return this.fn(this.iterator.next())},t}()},"30db":function(t,e,n){"use strict";(function(t,i){n.d(e,"h",(function(){return y})),n.d(e,"e",(function(){return v})),n.d(e,"d",(function(){return b})),n.d(e,"f",(function(){return _})),n.d(e,"g",(function(){return w})),n.d(e,"c",(function(){return x})),n.d(e,"b",(function(){return C})),n.d(e,"i",(function(){return k})),n.d(e,"a",(function(){return I}));var r="en",o=!1,s=!1,a=!1,h=!1,u=!1,l=!1,c=void 0,p=void 0,d="undefined"!==typeof t&&"undefined"!==typeof t.versions&&"undefined"!==typeof t.versions.electron&&"renderer"===t.type;if("object"!==typeof navigator||d){if("object"===typeof t){o="win32"===t.platform,s="darwin"===t.platform,a="linux"===t.platform,c=r,r;var f=Object({NODE_ENV:"production",VUE_APP_MULTIVERSE_PATH:"./",VUE_APP_UIVERSION:"2024.10.17.cd0c19c",BASE_URL:""})["VSCODE_NLS_CONFIG"];if(f)try{var g=JSON.parse(f),m=g.availableLanguages["*"];c=g.locale,m||r,g._translationsConfigFile}catch(A){}h=!0}}else p=navigator.userAgent,o=p.indexOf("Windows")>=0,s=p.indexOf("Macintosh")>=0,l=p.indexOf("Macintosh")>=0&&!!navigator.maxTouchPoints&&navigator.maxTouchPoints>0,a=p.indexOf("Linux")>=0,u=!0,c=navigator.language,c;var y=o,v=s,b=a,_=h,w=u,x=l,D="object"===typeof self?self:"object"===typeof i?i:{},C=D,k=function(){if(C.setImmediate)return C.setImmediate.bind(C);if("function"===typeof C.postMessage&&!C.importScripts){var e=[];C.addEventListener("message",(function(t){if(t.data&&t.data.vscodeSetImmediateId)for(var n=0,i=e.length;n<i;n++){var r=e[n];if(r.id===t.data.vscodeSetImmediateId)return e.splice(n,1),void r.callback()}}));var n=0;return function(t){var i=++n;e.push({id:i,callback:t}),C.postMessage({vscodeSetImmediateId:i},"*")}}if("undefined"!==typeof t&&"function"===typeof t.nextTick)return t.nextTick.bind(t);var i=Promise.resolve();return function(t){return i.then(t)}}(),I=s?2:o?1:3}).call(this,n("4362"),n("c8ba"))},"32b8":function(t,e,n){"use strict";n.r(e),n.d(e,"win32",(function(){return w})),n.d(e,"posix",(function(){return x})),n.d(e,"normalize",(function(){return D})),n.d(e,"join",(function(){return C})),n.d(e,"relative",(function(){return k})),n.d(e,"dirname",(function(){return I})),n.d(e,"basename",(function(){return A})),n.d(e,"extname",(function(){return E})),n.d(e,"sep",(function(){return S}));var i=n("c317"),r=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},t(e,n)};return function(e,n){function i(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),o=65,s=97,a=90,h=122,u=46,l=47,c=92,p=58,d=63,f=function(t){function e(e,n,i){var r,o=this;"string"===typeof n&&0===n.indexOf("not ")?(r="must not be",n=n.replace(/^not /,"")):r="must be";var s=-1!==e.indexOf(".")?"property":"argument",a='The "'+e+'" '+s+" "+r+" of type "+n;return a+=". Received type "+typeof i,o=t.call(this,a)||this,o.code="ERR_INVALID_ARG_TYPE",o}return r(e,t),e}(Error);function g(t,e){if("string"!==typeof t)throw new f(e,"string",t)}function m(t){return t===l||t===c}function y(t){return t===l}function v(t){return t>=o&&t<=a||t>=s&&t<=h}function b(t,e,n,i){for(var r,o="",s=0,a=-1,h=0,c=0;c<=t.length;++c){if(c<t.length)r=t.charCodeAt(c);else{if(i(r))break;r=l}if(i(r)){if(a===c-1||1===h);else if(a!==c-1&&2===h){if(o.length<2||2!==s||o.charCodeAt(o.length-1)!==u||o.charCodeAt(o.length-2)!==u){if(o.length>2){var p=o.lastIndexOf(n);-1===p?(o="",s=0):(o=o.slice(0,p),s=o.length-1-o.lastIndexOf(n)),a=c,h=0;continue}if(2===o.length||1===o.length){o="",s=0,a=c,h=0;continue}}e&&(o.length>0?o+=n+"..":o="..",s=2)}else o.length>0?o+=n+t.slice(a+1,c):o=t.slice(a+1,c),s=c-a-1;a=c,h=0}else r===u&&-1!==h?++h:h=-1}return o}function _(t,e){var n=e.dir||e.root,i=e.base||(e.name||"")+(e.ext||"");return n?n===e.root?n+i:n+t+i:i}var w={resolve:function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var n="",r="",o=!1,s=t.length-1;s>=-1;s--){var a=void 0;if(s>=0?a=t[s]:n?(a=i["b"]["="+n]||i["a"](),void 0!==a&&a.slice(0,3).toLowerCase()===n.toLowerCase()+"\\"||(a=n+"\\")):a=i["a"](),g(a,"path"),0!==a.length){var h=a.length,u=0,l="",c=!1,d=a.charCodeAt(0);if(h>1)if(m(d))if(c=!0,m(a.charCodeAt(1))){for(var f=2,y=f;f<h;++f)if(m(a.charCodeAt(f)))break;if(f<h&&f!==y){var _=a.slice(y,f);for(y=f;f<h;++f)if(!m(a.charCodeAt(f)))break;if(f<h&&f!==y){for(y=f;f<h;++f)if(m(a.charCodeAt(f)))break;f===h?(l="\\\\"+_+"\\"+a.slice(y),u=f):f!==y&&(l="\\\\"+_+"\\"+a.slice(y,f),u=f)}}}else u=1;else v(d)&&a.charCodeAt(1)===p&&(l=a.slice(0,2),u=2,h>2&&m(a.charCodeAt(2))&&(c=!0,u=3));else m(d)&&(u=1,c=!0);if(!(l.length>0&&n.length>0&&l.toLowerCase()!==n.toLowerCase())&&(0===n.length&&l.length>0&&(n=l),o||(r=a.slice(u)+"\\"+r,o=c),n.length>0&&o))break}}return r=b(r,!o,"\\",m),n+(o?"\\":"")+r||"."},normalize:function(t){g(t,"path");var e=t.length;if(0===e)return".";var n,i,r=0,o=!1,s=t.charCodeAt(0);if(e>1)if(m(s))if(o=!0,m(t.charCodeAt(1))){for(var a=2,h=a;a<e;++a)if(m(t.charCodeAt(a)))break;if(a<e&&a!==h){var u=t.slice(h,a);for(h=a;a<e;++a)if(!m(t.charCodeAt(a)))break;if(a<e&&a!==h){for(h=a;a<e;++a)if(m(t.charCodeAt(a)))break;if(a===e)return"\\\\"+u+"\\"+t.slice(h)+"\\";a!==h&&(n="\\\\"+u+"\\"+t.slice(h,a),r=a)}}}else r=1;else v(s)&&t.charCodeAt(1)===p&&(n=t.slice(0,2),r=2,e>2&&m(t.charCodeAt(2))&&(o=!0,r=3));else if(m(s))return"\\";return i=r<e?b(t.slice(r),!o,"\\",m):"",0!==i.length||o||(i="."),i.length>0&&m(t.charCodeAt(e-1))&&(i+="\\"),void 0===n?o?i.length>0?"\\"+i:"\\":i.length>0?i:"":o?i.length>0?n+"\\"+i:n+"\\":i.length>0?n+i:n},isAbsolute:function(t){g(t,"path");var e=t.length;if(0===e)return!1;var n=t.charCodeAt(0);return!!m(n)||!!(v(n)&&e>2&&t.charCodeAt(1)===p&&m(t.charCodeAt(2)))},join:function(){for(var t,e,n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];if(0===n.length)return".";for(var r=0;r<n.length;++r){var o=n[r];g(o,"path"),o.length>0&&(void 0===t?t=e=o:t+="\\"+o)}if(void 0===t)return".";var s=!0,a=0;if("string"===typeof e&&m(e.charCodeAt(0))){++a;var h=e.length;h>1&&m(e.charCodeAt(1))&&(++a,h>2&&(m(e.charCodeAt(2))?++a:s=!1))}if(s){for(;a<t.length;++a)if(!m(t.charCodeAt(a)))break;a>=2&&(t="\\"+t.slice(a))}return w.normalize(t)},relative:function(t,e){if(g(t,"from"),g(e,"to"),t===e)return"";var n=w.resolve(t),i=w.resolve(e);if(n===i)return"";if(t=n.toLowerCase(),e=i.toLowerCase(),t===e)return"";for(var r=0;r<t.length;++r)if(t.charCodeAt(r)!==c)break;for(var o=t.length;o-1>r;--o)if(t.charCodeAt(o-1)!==c)break;for(var s=o-r,a=0;a<e.length;++a)if(e.charCodeAt(a)!==c)break;for(var h=e.length;h-1>a;--h)if(e.charCodeAt(h-1)!==c)break;for(var u=h-a,l=s<u?s:u,p=-1,d=0;d<=l;++d){if(d===l){if(u>l){if(e.charCodeAt(a+d)===c)return i.slice(a+d+1);if(2===d)return i.slice(a+d)}s>l&&(t.charCodeAt(r+d)===c?p=d:2===d&&(p=3));break}var f=t.charCodeAt(r+d),m=e.charCodeAt(a+d);if(f!==m)break;f===c&&(p=d)}if(d!==l&&-1===p)return i;var y="";for(-1===p&&(p=0),d=r+p+1;d<=o;++d)d!==o&&t.charCodeAt(d)!==c||(0===y.length?y+="..":y+="\\..");return y.length>0?y+i.slice(a+p,h):(a+=p,i.charCodeAt(a)===c&&++a,i.slice(a,h))},toNamespacedPath:function(t){if("string"!==typeof t)return t;if(0===t.length)return"";var e=w.resolve(t);if(e.length>=3)if(e.charCodeAt(0)===c){if(e.charCodeAt(1)===c){var n=e.charCodeAt(2);if(n!==d&&n!==u)return"\\\\?\\UNC\\"+e.slice(2)}}else if(v(e.charCodeAt(0))&&e.charCodeAt(1)===p&&e.charCodeAt(2)===c)return"\\\\?\\"+e;return t},dirname:function(t){g(t,"path");var e=t.length;if(0===e)return".";var n=-1,i=-1,r=!0,o=0,s=t.charCodeAt(0);if(e>1)if(m(s)){if(n=o=1,m(t.charCodeAt(1))){for(var a=2,h=a;a<e;++a)if(m(t.charCodeAt(a)))break;if(a<e&&a!==h){for(h=a;a<e;++a)if(!m(t.charCodeAt(a)))break;if(a<e&&a!==h){for(h=a;a<e;++a)if(m(t.charCodeAt(a)))break;if(a===e)return t;a!==h&&(n=o=a+1)}}}}else v(s)&&t.charCodeAt(1)===p&&(n=o=2,e>2&&m(t.charCodeAt(2))&&(n=o=3));else if(m(s))return t;for(var u=e-1;u>=o;--u)if(m(t.charCodeAt(u))){if(!r){i=u;break}}else r=!1;if(-1===i){if(-1===n)return".";i=n}return t.slice(0,i)},basename:function(t,e){void 0!==e&&g(e,"ext"),g(t,"path");var n,i=0,r=-1,o=!0;if(t.length>=2){var s=t.charCodeAt(0);v(s)&&t.charCodeAt(1)===p&&(i=2)}if(void 0!==e&&e.length>0&&e.length<=t.length){if(e.length===t.length&&e===t)return"";var a=e.length-1,h=-1;for(n=t.length-1;n>=i;--n){var u=t.charCodeAt(n);if(m(u)){if(!o){i=n+1;break}}else-1===h&&(o=!1,h=n+1),a>=0&&(u===e.charCodeAt(a)?-1===--a&&(r=n):(a=-1,r=h))}return i===r?r=h:-1===r&&(r=t.length),t.slice(i,r)}for(n=t.length-1;n>=i;--n)if(m(t.charCodeAt(n))){if(!o){i=n+1;break}}else-1===r&&(o=!1,r=n+1);return-1===r?"":t.slice(i,r)},extname:function(t){g(t,"path");var e=0,n=-1,i=0,r=-1,o=!0,s=0;t.length>=2&&t.charCodeAt(1)===p&&v(t.charCodeAt(0))&&(e=i=2);for(var a=t.length-1;a>=e;--a){var h=t.charCodeAt(a);if(m(h)){if(!o){i=a+1;break}}else-1===r&&(o=!1,r=a+1),h===u?-1===n?n=a:1!==s&&(s=1):-1!==n&&(s=-1)}return-1===n||-1===r||0===s||1===s&&n===r-1&&n===i+1?"":t.slice(n,r)},format:function(t){if(null===t||"object"!==typeof t)throw new f("pathObject","Object",t);return _("\\",t)},parse:function(t){g(t,"path");var e={root:"",dir:"",base:"",ext:"",name:""};if(0===t.length)return e;var n=t.length,i=0,r=t.charCodeAt(0);if(n>1){if(m(r)){if(i=1,m(t.charCodeAt(1))){for(var o=2,s=o;o<n;++o)if(m(t.charCodeAt(o)))break;if(o<n&&o!==s){for(s=o;o<n;++o)if(!m(t.charCodeAt(o)))break;if(o<n&&o!==s){for(s=o;o<n;++o)if(m(t.charCodeAt(o)))break;o===n?i=o:o!==s&&(i=o+1)}}}}else if(v(r)&&t.charCodeAt(1)===p){if(i=2,!(n>2))return e.root=e.dir=t,e;if(m(t.charCodeAt(2))){if(3===n)return e.root=e.dir=t,e;i=3}}}else if(m(r))return e.root=e.dir=t,e;i>0&&(e.root=t.slice(0,i));for(var a=-1,h=i,l=-1,c=!0,d=t.length-1,f=0;d>=i;--d)if(r=t.charCodeAt(d),m(r)){if(!c){h=d+1;break}}else-1===l&&(c=!1,l=d+1),r===u?-1===a?a=d:1!==f&&(f=1):-1!==a&&(f=-1);return-1===a||-1===l||0===f||1===f&&a===l-1&&a===h+1?-1!==l&&(e.base=e.name=t.slice(h,l)):(e.name=t.slice(h,a),e.base=t.slice(h,l),e.ext=t.slice(a,l)),e.dir=h>0&&h!==i?t.slice(0,h-1):e.root,e},sep:"\\",delimiter:";",win32:null,posix:null},x={resolve:function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var n="",r=!1,o=t.length-1;o>=-1&&!r;o--){var s=void 0;s=o>=0?t[o]:i["a"](),g(s,"path"),0!==s.length&&(n=s+"/"+n,r=s.charCodeAt(0)===l)}return n=b(n,!r,"/",y),r?n.length>0?"/"+n:"/":n.length>0?n:"."},normalize:function(t){if(g(t,"path"),0===t.length)return".";var e=t.charCodeAt(0)===l,n=t.charCodeAt(t.length-1)===l;return t=b(t,!e,"/",y),0!==t.length||e||(t="."),t.length>0&&n&&(t+="/"),e?"/"+t:t},isAbsolute:function(t){return g(t,"path"),t.length>0&&t.charCodeAt(0)===l},join:function(){for(var t,e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];if(0===e.length)return".";for(var i=0;i<e.length;++i){var r=arguments[i];g(r,"path"),r.length>0&&(void 0===t?t=r:t+="/"+r)}return void 0===t?".":x.normalize(t)},relative:function(t,e){if(g(t,"from"),g(e,"to"),t===e)return"";if(t=x.resolve(t),e=x.resolve(e),t===e)return"";for(var n=1;n<t.length;++n)if(t.charCodeAt(n)!==l)break;for(var i=t.length,r=i-n,o=1;o<e.length;++o)if(e.charCodeAt(o)!==l)break;for(var s=e.length,a=s-o,h=r<a?r:a,u=-1,c=0;c<=h;++c){if(c===h){if(a>h){if(e.charCodeAt(o+c)===l)return e.slice(o+c+1);if(0===c)return e.slice(o+c)}else r>h&&(t.charCodeAt(n+c)===l?u=c:0===c&&(u=0));break}var p=t.charCodeAt(n+c),d=e.charCodeAt(o+c);if(p!==d)break;p===l&&(u=c)}var f="";for(c=n+u+1;c<=i;++c)c!==i&&t.charCodeAt(c)!==l||(0===f.length?f+="..":f+="/..");return f.length>0?f+e.slice(o+u):(o+=u,e.charCodeAt(o)===l&&++o,e.slice(o))},toNamespacedPath:function(t){return t},dirname:function(t){if(g(t,"path"),0===t.length)return".";for(var e=t.charCodeAt(0)===l,n=-1,i=!0,r=t.length-1;r>=1;--r)if(t.charCodeAt(r)===l){if(!i){n=r;break}}else i=!1;return-1===n?e?"/":".":e&&1===n?"//":t.slice(0,n)},basename:function(t,e){void 0!==e&&g(e,"ext"),g(t,"path");var n,i=0,r=-1,o=!0;if(void 0!==e&&e.length>0&&e.length<=t.length){if(e.length===t.length&&e===t)return"";var s=e.length-1,a=-1;for(n=t.length-1;n>=0;--n){var h=t.charCodeAt(n);if(h===l){if(!o){i=n+1;break}}else-1===a&&(o=!1,a=n+1),s>=0&&(h===e.charCodeAt(s)?-1===--s&&(r=n):(s=-1,r=a))}return i===r?r=a:-1===r&&(r=t.length),t.slice(i,r)}for(n=t.length-1;n>=0;--n)if(t.charCodeAt(n)===l){if(!o){i=n+1;break}}else-1===r&&(o=!1,r=n+1);return-1===r?"":t.slice(i,r)},extname:function(t){g(t,"path");for(var e=-1,n=0,i=-1,r=!0,o=0,s=t.length-1;s>=0;--s){var a=t.charCodeAt(s);if(a!==l)-1===i&&(r=!1,i=s+1),a===u?-1===e?e=s:1!==o&&(o=1):-1!==e&&(o=-1);else if(!r){n=s+1;break}}return-1===e||-1===i||0===o||1===o&&e===i-1&&e===n+1?"":t.slice(e,i)},format:function(t){if(null===t||"object"!==typeof t)throw new f("pathObject","Object",t);return _("/",t)},parse:function(t){g(t,"path");var e={root:"",dir:"",base:"",ext:"",name:""};if(0===t.length)return e;var n,i=t.charCodeAt(0)===l;i?(e.root="/",n=1):n=0;for(var r=-1,o=0,s=-1,a=!0,h=t.length-1,c=0;h>=n;--h){var p=t.charCodeAt(h);if(p!==l)-1===s&&(a=!1,s=h+1),p===u?-1===r?r=h:1!==c&&(c=1):-1!==r&&(c=-1);else if(!a){o=h+1;break}}return-1===r||-1===s||0===c||1===c&&r===s-1&&r===o+1?-1!==s&&(e.base=e.name=0===o&&i?t.slice(1,s):t.slice(o,s)):(0===o&&i?(e.name=t.slice(1,r),e.base=t.slice(1,s)):(e.name=t.slice(o,r),e.base=t.slice(o,s)),e.ext=t.slice(r,s)),o>0?e.dir=t.slice(0,o-1):i&&(e.dir="/"),e},sep:"/",delimiter:":",win32:null,posix:null};x.win32=w.win32=w,x.posix=w.posix=x;var D="win32"===i["c"]?w.normalize:x.normalize,C="win32"===i["c"]?w.join:x.join,k="win32"===i["c"]?w.relative:x.relative,I="win32"===i["c"]?w.dirname:x.dirname,A="win32"===i["c"]?w.basename:x.basename,E="win32"===i["c"]?w.extname:x.extname,S="win32"===i["c"]?w.sep:x.sep},3742:function(t,e,n){"use strict";function i(t){return!t||"string"!==typeof t||0===t.trim().length}function r(t,e,n){void 0===n&&(n="0");for(var i=""+t,r=[i],o=i.length;o<e;o++)r.push(n);return r.reverse().join("")}n.d(e,"x",(function(){return i})),n.d(e,"F",(function(){return r})),n.d(e,"r",(function(){return s})),n.d(e,"o",(function(){return a})),n.d(e,"p",(function(){return h})),n.d(e,"Q",(function(){return u})),n.d(e,"K",(function(){return c})),n.d(e,"k",(function(){return p})),n.d(e,"N",(function(){return d})),n.d(e,"m",(function(){return f})),n.d(e,"l",(function(){return g})),n.d(e,"I",(function(){return m})),n.d(e,"H",(function(){return y})),n.d(e,"q",(function(){return v})),n.d(e,"t",(function(){return b})),n.d(e,"D",(function(){return _})),n.d(e,"e",(function(){return w})),n.d(e,"f",(function(){return x})),n.d(e,"B",(function(){return D})),n.d(e,"C",(function(){return C})),n.d(e,"n",(function(){return I})),n.d(e,"O",(function(){return E})),n.d(e,"c",(function(){return S})),n.d(e,"d",(function(){return T})),n.d(e,"z",(function(){return P})),n.d(e,"A",(function(){return O})),n.d(e,"u",(function(){return F})),n.d(e,"E",(function(){return L})),n.d(e,"G",(function(){return M})),n.d(e,"i",(function(){return B})),n.d(e,"g",(function(){return H})),n.d(e,"v",(function(){return q})),n.d(e,"h",(function(){return z})),n.d(e,"y",(function(){return U})),n.d(e,"w",(function(){return W})),n.d(e,"a",(function(){return V})),n.d(e,"P",(function(){return $})),n.d(e,"L",(function(){return G})),n.d(e,"J",(function(){return Z})),n.d(e,"j",(function(){return Y})),n.d(e,"M",(function(){return J})),n.d(e,"s",(function(){return X})),n.d(e,"b",(function(){return Q}));var o=/{(\d+)}/g;function s(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return 0===e.length?t:t.replace(o,(function(t,n){var i=parseInt(n,10);return isNaN(i)||i<0||i>=e.length?t:e[i]}))}function a(t){return t.replace(/[<>&]/g,(function(t){switch(t){case"<":return"&lt;";case">":return"&gt;";case"&":return"&amp;";default:return t}}))}function h(t){return t.replace(/[\\\{\}\*\+\?\|\^\$\.\[\]\(\)]/g,"\\$&")}function u(t,e){void 0===e&&(e=" ");var n=l(t,e);return c(n,e)}function l(t,e){if(!t||!e)return t;var n=e.length;if(0===n||0===t.length)return t;var i=0;while(t.indexOf(e,i)===i)i+=n;return t.substring(i)}function c(t,e){if(!t||!e)return t;var n=e.length,i=t.length;if(0===n||0===i)return t;var r=i,o=-1;while(1){if(o=t.lastIndexOf(e,r-1),-1===o||o+n!==r)break;if(0===o)return"";r=o}return t.substring(0,r)}function p(t){return t.replace(/[\-\\\{\}\+\?\|\^\$\.\,\[\]\(\)\#\s]/g,"\\$&").replace(/[\*]/g,".*")}function d(t,e){if(t.length<e.length)return!1;if(t===e)return!0;for(var n=0;n<e.length;n++)if(t[n]!==e[n])return!1;return!0}function f(t,e){var n=t.length-e.length;return n>0?t.indexOf(e,n)===n:0===n&&t===e}function g(t,e,n){if(void 0===n&&(n={}),!t)throw new Error("Cannot create regex from empty string");e||(t=h(t)),n.wholeWord&&(/\B/.test(t.charAt(0))||(t="\\b"+t),/\B/.test(t.charAt(t.length-1))||(t+="\\b"));var i="";return n.global&&(i+="g"),n.matchCase||(i+="i"),n.multiline&&(i+="m"),n.unicode&&(i+="u"),new RegExp(t,i)}function m(t){if("^"===t.source||"^$"===t.source||"$"===t.source||"^\\s*$"===t.source)return!1;var e=t.exec("");return!(!e||0!==t.lastIndex)}function y(t){return(t.global?"g":"")+(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")}function v(t){for(var e=0,n=t.length;e<n;e++){var i=t.charCodeAt(e);if(32!==i&&9!==i)return e}return-1}function b(t,e,n){void 0===e&&(e=0),void 0===n&&(n=t.length);for(var i=e;i<n;i++){var r=t.charCodeAt(i);if(32!==r&&9!==r)return t.substring(e,i)}return t.substring(e,n)}function _(t,e){void 0===e&&(e=t.length-1);for(var n=e;n>=0;n--){var i=t.charCodeAt(n);if(32!==i&&9!==i)return n}return-1}function w(t,e){return t<e?-1:t>e?1:0}function x(t,e){for(var n=Math.min(t.length,e.length),i=0;i<n;i++){var r=t.charCodeAt(i),o=e.charCodeAt(i);if(r!==o){C(r)&&(r+=32),C(o)&&(o+=32);var s=r-o;if(0!==s)return D(r)&&D(o)?s:w(t.toLowerCase(),e.toLowerCase())}}return t.length<e.length?-1:t.length>e.length?1:0}function D(t){return t>=97&&t<=122}function C(t){return t>=65&&t<=90}function k(t){return D(t)||C(t)}function I(t,e){return t.length===e.length&&A(t,e)}function A(t,e,n){void 0===n&&(n=t.length);for(var i=0;i<n;i++){var r=t.charCodeAt(i),o=e.charCodeAt(i);if(r!==o)if(k(r)&&k(o)){var s=Math.abs(r-o);if(0!==s&&32!==s)return!1}else if(String.fromCharCode(r).toLowerCase()!==String.fromCharCode(o).toLowerCase())return!1}return!0}function E(t,e){var n=e.length;return!(e.length>t.length)&&A(t,e,n)}function S(t,e){var n,i=Math.min(t.length,e.length);for(n=0;n<i;n++)if(t.charCodeAt(n)!==e.charCodeAt(n))return n;return i}function T(t,e){var n,i=Math.min(t.length,e.length),r=t.length-1,o=e.length-1;for(n=0;n<i;n++)if(t.charCodeAt(r-n)!==e.charCodeAt(o-n))return n;return i}function P(t){return 55296<=t&&t<=56319}function O(t){return 56320<=t&&t<=57343}function F(t,e,n){var i=t.charCodeAt(n);if(P(i)&&n+1<e){var r=t.charCodeAt(n+1);if(O(r))return r-56320+(i-55296<<10)+65536}return i}function R(t,e){var n=t.charCodeAt(e-1);if(O(n)&&e>1){var i=t.charCodeAt(e-2);if(P(i))return n-56320+(i-55296<<10)+65536}return n}function L(t,e){var n=tt.getInstance(),i=e,r=t.length,o=F(t,r,e);e+=o>=65536?2:1;var s=n.getGraphemeBreakType(o);while(e<r){var a=F(t,r,e),h=n.getGraphemeBreakType(a);if(Q(s,h))break;e+=a>=65536?2:1,s=h}return e-i}function M(t,e){var n=tt.getInstance(),i=e,r=R(t,e);e-=r>=65536?2:1;var o=n.getGraphemeBreakType(r);while(e>0){var s=R(t,e),a=n.getGraphemeBreakType(s);if(Q(a,o))break;e-=s>=65536?2:1,o=a}return i-e}var j=/(?:[\u05BE\u05C0\u05C3\u05C6\u05D0-\u05F4\u0608\u060B\u060D\u061B-\u064A\u066D-\u066F\u0671-\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u0710\u0712-\u072F\u074D-\u07A5\u07B1-\u07EA\u07F4\u07F5\u07FA-\u0815\u081A\u0824\u0828\u0830-\u0858\u085E-\u08BD\u200F\uFB1D\uFB1F-\uFB28\uFB2A-\uFD3D\uFD50-\uFDFC\uFE70-\uFEFC]|\uD802[\uDC00-\uDD1B\uDD20-\uDE00\uDE10-\uDE33\uDE40-\uDEE4\uDEEB-\uDF35\uDF40-\uDFFF]|\uD803[\uDC00-\uDCFF]|\uD83A[\uDC00-\uDCCF\uDD00-\uDD43\uDD50-\uDFFF]|\uD83B[\uDC00-\uDEBB])/;function B(t){return j.test(t)}var N=/(?:[\u231A\u231B\u23F0\u23F3\u2600-\u27BF\u2B50\u2B55]|\uD83C[\uDDE6-\uDDFF\uDF00-\uDFFF]|\uD83D[\uDC00-\uDE4F\uDE80-\uDEFC\uDFE0-\uDFEB]|\uD83E[\uDD00-\uDDFF\uDE70-\uDE73\uDE78-\uDE82\uDE90-\uDE95])/;function H(t){return N.test(t)}var K=/^[\t\n\r\x20-\x7E]*$/;function q(t){return K.test(t)}function z(t){for(var e=0,n=t.length;e<n;e++)if(U(t.charCodeAt(e)))return!0;return!1}function U(t){return t=+t,t>=11904&&t<=55215||t>=63744&&t<=64255||t>=65281&&t<=65374}function W(t){return t>=127462&&t<=127487||t>=9728&&t<=10175||t>=127744&&t<=128591||t>=128640&&t<=128764||t>=128992&&t<=129003||t>=129280&&t<=129535||t>=129648&&t<=129651||t>=129656&&t<=129666||t>=129680&&t<=129685}var V=String.fromCharCode(65279);function $(t){return!!(t&&t.length>0&&65279===t.charCodeAt(0))}function G(t){return btoa(encodeURIComponent(t))}function Z(t,e){for(var n="",i=0;i<e;i++)n+=t;return n}function Y(t,e){return void 0===e&&(e=!1),!!t&&(e&&(t=t.replace(/\\./g,"")),t.toLowerCase()!==t)}function J(t){var e=26;return t%=2*e,t<e?String.fromCharCode(97+t):String.fromCharCode(65+t-e)}function X(t){var e=tt.getInstance();return e.getGraphemeBreakType(t)}function Q(t,e){return 0===t?5!==e&&7!==e:(2!==t||3!==e)&&(4===t||2===t||3===t||(4===e||2===e||3===e||(8!==t||8!==e&&9!==e&&11!==e&&12!==e)&&((11!==t&&9!==t||9!==e&&10!==e)&&((12!==t&&10!==t||10!==e)&&(5!==e&&13!==e&&(7!==e&&(1!==t&&((13!==t||14!==e)&&(6!==t||6!==e)))))))))}var tt=function(){function t(){this._data=et()}return t.getInstance=function(){return t._INSTANCE||(t._INSTANCE=new t),t._INSTANCE},t.prototype.getGraphemeBreakType=function(t){if(t<32)return 10===t?3:13===t?2:4;if(t<127)return 0;var e=this._data,n=e.length/3,i=1;while(i<=n)if(t<e[3*i])i*=2;else{if(!(t>e[3*i+1]))return e[3*i+2];i=2*i+1}return 0},t._INSTANCE=null,t}();function et(){return JSON.parse("[0,0,0,51592,51592,11,44424,44424,11,72251,72254,5,7150,7150,7,48008,48008,11,55176,55176,11,128420,128420,14,3276,3277,5,9979,9980,14,46216,46216,11,49800,49800,11,53384,53384,11,70726,70726,5,122915,122916,5,129320,129327,14,2558,2558,5,5906,5908,5,9762,9763,14,43360,43388,8,45320,45320,11,47112,47112,11,48904,48904,11,50696,50696,11,52488,52488,11,54280,54280,11,70082,70083,1,71350,71350,7,73111,73111,5,127892,127893,14,128726,128727,14,129473,129474,14,2027,2035,5,2901,2902,5,3784,3789,5,6754,6754,5,8418,8420,5,9877,9877,14,11088,11088,14,44008,44008,5,44872,44872,11,45768,45768,11,46664,46664,11,47560,47560,11,48456,48456,11,49352,49352,11,50248,50248,11,51144,51144,11,52040,52040,11,52936,52936,11,53832,53832,11,54728,54728,11,69811,69814,5,70459,70460,5,71096,71099,7,71998,71998,5,72874,72880,5,119149,119149,7,127374,127374,14,128335,128335,14,128482,128482,14,128765,128767,14,129399,129400,14,129680,129685,14,1476,1477,5,2377,2380,7,2759,2760,5,3137,3140,7,3458,3459,7,4153,4154,5,6432,6434,5,6978,6978,5,7675,7679,5,9723,9726,14,9823,9823,14,9919,9923,14,10035,10036,14,42736,42737,5,43596,43596,5,44200,44200,11,44648,44648,11,45096,45096,11,45544,45544,11,45992,45992,11,46440,46440,11,46888,46888,11,47336,47336,11,47784,47784,11,48232,48232,11,48680,48680,11,49128,49128,11,49576,49576,11,50024,50024,11,50472,50472,11,50920,50920,11,51368,51368,11,51816,51816,11,52264,52264,11,52712,52712,11,53160,53160,11,53608,53608,11,54056,54056,11,54504,54504,11,54952,54952,11,68108,68111,5,69933,69940,5,70197,70197,7,70498,70499,7,70845,70845,5,71229,71229,5,71727,71735,5,72154,72155,5,72344,72345,5,73023,73029,5,94095,94098,5,121403,121452,5,126981,127182,14,127538,127546,14,127990,127990,14,128391,128391,14,128445,128449,14,128500,128505,14,128752,128752,14,129160,129167,14,129356,129356,14,129432,129442,14,129648,129651,14,129751,131069,14,173,173,4,1757,1757,1,2274,2274,1,2494,2494,5,2641,2641,5,2876,2876,5,3014,3016,7,3262,3262,7,3393,3396,5,3570,3571,7,3968,3972,5,4228,4228,7,6086,6086,5,6679,6680,5,6912,6915,5,7080,7081,5,7380,7392,5,8252,8252,14,9096,9096,14,9748,9749,14,9784,9786,14,9833,9850,14,9890,9894,14,9938,9938,14,9999,9999,14,10085,10087,14,12349,12349,14,43136,43137,7,43454,43456,7,43755,43755,7,44088,44088,11,44312,44312,11,44536,44536,11,44760,44760,11,44984,44984,11,45208,45208,11,45432,45432,11,45656,45656,11,45880,45880,11,46104,46104,11,46328,46328,11,46552,46552,11,46776,46776,11,47000,47000,11,47224,47224,11,47448,47448,11,47672,47672,11,47896,47896,11,48120,48120,11,48344,48344,11,48568,48568,11,48792,48792,11,49016,49016,11,49240,49240,11,49464,49464,11,49688,49688,11,49912,49912,11,50136,50136,11,50360,50360,11,50584,50584,11,50808,50808,11,51032,51032,11,51256,51256,11,51480,51480,11,51704,51704,11,51928,51928,11,52152,52152,11,52376,52376,11,52600,52600,11,52824,52824,11,53048,53048,11,53272,53272,11,53496,53496,11,53720,53720,11,53944,53944,11,54168,54168,11,54392,54392,11,54616,54616,11,54840,54840,11,55064,55064,11,65438,65439,5,69633,69633,5,69837,69837,1,70018,70018,7,70188,70190,7,70368,70370,7,70465,70468,7,70712,70719,5,70835,70840,5,70850,70851,5,71132,71133,5,71340,71340,7,71458,71461,5,71985,71989,7,72002,72002,7,72193,72202,5,72281,72283,5,72766,72766,7,72885,72886,5,73104,73105,5,92912,92916,5,113824,113827,4,119173,119179,5,121505,121519,5,125136,125142,5,127279,127279,14,127489,127490,14,127570,127743,14,127900,127901,14,128254,128254,14,128369,128370,14,128400,128400,14,128425,128432,14,128468,128475,14,128489,128494,14,128715,128720,14,128745,128745,14,128759,128760,14,129004,129023,14,129296,129304,14,129340,129342,14,129388,129392,14,129404,129407,14,129454,129455,14,129485,129487,14,129659,129663,14,129719,129727,14,917536,917631,5,13,13,2,1160,1161,5,1564,1564,4,1807,1807,1,2085,2087,5,2363,2363,7,2402,2403,5,2507,2508,7,2622,2624,7,2691,2691,7,2786,2787,5,2881,2884,5,3006,3006,5,3072,3072,5,3170,3171,5,3267,3268,7,3330,3331,7,3406,3406,1,3538,3540,5,3655,3662,5,3897,3897,5,4038,4038,5,4184,4185,5,4352,4447,8,6068,6069,5,6155,6157,5,6448,6449,7,6742,6742,5,6783,6783,5,6966,6970,5,7042,7042,7,7143,7143,7,7212,7219,5,7412,7412,5,8206,8207,4,8294,8303,4,8596,8601,14,9410,9410,14,9742,9742,14,9757,9757,14,9770,9770,14,9794,9794,14,9828,9828,14,9855,9855,14,9882,9882,14,9900,9903,14,9929,9933,14,9963,9967,14,9987,9988,14,10006,10006,14,10062,10062,14,10175,10175,14,11744,11775,5,42607,42607,5,43043,43044,7,43263,43263,5,43444,43445,7,43569,43570,5,43698,43700,5,43766,43766,5,44032,44032,11,44144,44144,11,44256,44256,11,44368,44368,11,44480,44480,11,44592,44592,11,44704,44704,11,44816,44816,11,44928,44928,11,45040,45040,11,45152,45152,11,45264,45264,11,45376,45376,11,45488,45488,11,45600,45600,11,45712,45712,11,45824,45824,11,45936,45936,11,46048,46048,11,46160,46160,11,46272,46272,11,46384,46384,11,46496,46496,11,46608,46608,11,46720,46720,11,46832,46832,11,46944,46944,11,47056,47056,11,47168,47168,11,47280,47280,11,47392,47392,11,47504,47504,11,47616,47616,11,47728,47728,11,47840,47840,11,47952,47952,11,48064,48064,11,48176,48176,11,48288,48288,11,48400,48400,11,48512,48512,11,48624,48624,11,48736,48736,11,48848,48848,11,48960,48960,11,49072,49072,11,49184,49184,11,49296,49296,11,49408,49408,11,49520,49520,11,49632,49632,11,49744,49744,11,49856,49856,11,49968,49968,11,50080,50080,11,50192,50192,11,50304,50304,11,50416,50416,11,50528,50528,11,50640,50640,11,50752,50752,11,50864,50864,11,50976,50976,11,51088,51088,11,51200,51200,11,51312,51312,11,51424,51424,11,51536,51536,11,51648,51648,11,51760,51760,11,51872,51872,11,51984,51984,11,52096,52096,11,52208,52208,11,52320,52320,11,52432,52432,11,52544,52544,11,52656,52656,11,52768,52768,11,52880,52880,11,52992,52992,11,53104,53104,11,53216,53216,11,53328,53328,11,53440,53440,11,53552,53552,11,53664,53664,11,53776,53776,11,53888,53888,11,54000,54000,11,54112,54112,11,54224,54224,11,54336,54336,11,54448,54448,11,54560,54560,11,54672,54672,11,54784,54784,11,54896,54896,11,55008,55008,11,55120,55120,11,64286,64286,5,66272,66272,5,68900,68903,5,69762,69762,7,69817,69818,5,69927,69931,5,70003,70003,5,70070,70078,5,70094,70094,7,70194,70195,7,70206,70206,5,70400,70401,5,70463,70463,7,70475,70477,7,70512,70516,5,70722,70724,5,70832,70832,5,70842,70842,5,70847,70848,5,71088,71089,7,71102,71102,7,71219,71226,5,71231,71232,5,71342,71343,7,71453,71455,5,71463,71467,5,71737,71738,5,71995,71996,5,72000,72000,7,72145,72147,7,72160,72160,5,72249,72249,7,72273,72278,5,72330,72342,5,72752,72758,5,72850,72871,5,72882,72883,5,73018,73018,5,73031,73031,5,73109,73109,5,73461,73462,7,94031,94031,5,94192,94193,7,119142,119142,7,119155,119162,4,119362,119364,5,121476,121476,5,122888,122904,5,123184,123190,5,126976,126979,14,127184,127231,14,127344,127345,14,127405,127461,14,127514,127514,14,127561,127567,14,127778,127779,14,127896,127896,14,127985,127986,14,127995,127999,5,128326,128328,14,128360,128366,14,128378,128378,14,128394,128397,14,128405,128406,14,128422,128423,14,128435,128443,14,128453,128464,14,128479,128480,14,128484,128487,14,128496,128498,14,128640,128709,14,128723,128724,14,128736,128741,14,128747,128748,14,128755,128755,14,128762,128762,14,128981,128991,14,129096,129103,14,129292,129292,14,129311,129311,14,129329,129330,14,129344,129349,14,129360,129374,14,129394,129394,14,129402,129402,14,129413,129425,14,129445,129450,14,129466,129471,14,129483,129483,14,129511,129535,14,129653,129655,14,129667,129670,14,129705,129711,14,129731,129743,14,917505,917505,4,917760,917999,5,10,10,3,127,159,4,768,879,5,1471,1471,5,1536,1541,1,1648,1648,5,1767,1768,5,1840,1866,5,2070,2073,5,2137,2139,5,2307,2307,7,2366,2368,7,2382,2383,7,2434,2435,7,2497,2500,5,2519,2519,5,2563,2563,7,2631,2632,5,2677,2677,5,2750,2752,7,2763,2764,7,2817,2817,5,2879,2879,5,2891,2892,7,2914,2915,5,3008,3008,5,3021,3021,5,3076,3076,5,3146,3149,5,3202,3203,7,3264,3265,7,3271,3272,7,3298,3299,5,3390,3390,5,3402,3404,7,3426,3427,5,3535,3535,5,3544,3550,7,3635,3635,7,3763,3763,7,3893,3893,5,3953,3966,5,3981,3991,5,4145,4145,7,4157,4158,5,4209,4212,5,4237,4237,5,4520,4607,10,5970,5971,5,6071,6077,5,6089,6099,5,6277,6278,5,6439,6440,5,6451,6456,7,6683,6683,5,6744,6750,5,6765,6770,7,6846,6846,5,6964,6964,5,6972,6972,5,7019,7027,5,7074,7077,5,7083,7085,5,7146,7148,7,7154,7155,7,7222,7223,5,7394,7400,5,7416,7417,5,8204,8204,5,8233,8233,4,8288,8292,4,8413,8416,5,8482,8482,14,8986,8987,14,9193,9203,14,9654,9654,14,9733,9733,14,9745,9745,14,9752,9752,14,9760,9760,14,9766,9766,14,9774,9775,14,9792,9792,14,9800,9811,14,9825,9826,14,9831,9831,14,9852,9853,14,9872,9873,14,9880,9880,14,9885,9887,14,9896,9897,14,9906,9916,14,9926,9927,14,9936,9936,14,9941,9960,14,9974,9974,14,9982,9985,14,9992,9997,14,10002,10002,14,10017,10017,14,10055,10055,14,10071,10071,14,10145,10145,14,11013,11015,14,11503,11505,5,12334,12335,5,12951,12951,14,42612,42621,5,43014,43014,5,43047,43047,7,43204,43205,5,43335,43345,5,43395,43395,7,43450,43451,7,43561,43566,5,43573,43574,5,43644,43644,5,43710,43711,5,43758,43759,7,44005,44005,5,44012,44012,7,44060,44060,11,44116,44116,11,44172,44172,11,44228,44228,11,44284,44284,11,44340,44340,11,44396,44396,11,44452,44452,11,44508,44508,11,44564,44564,11,44620,44620,11,44676,44676,11,44732,44732,11,44788,44788,11,44844,44844,11,44900,44900,11,44956,44956,11,45012,45012,11,45068,45068,11,45124,45124,11,45180,45180,11,45236,45236,11,45292,45292,11,45348,45348,11,45404,45404,11,45460,45460,11,45516,45516,11,45572,45572,11,45628,45628,11,45684,45684,11,45740,45740,11,45796,45796,11,45852,45852,11,45908,45908,11,45964,45964,11,46020,46020,11,46076,46076,11,46132,46132,11,46188,46188,11,46244,46244,11,46300,46300,11,46356,46356,11,46412,46412,11,46468,46468,11,46524,46524,11,46580,46580,11,46636,46636,11,46692,46692,11,46748,46748,11,46804,46804,11,46860,46860,11,46916,46916,11,46972,46972,11,47028,47028,11,47084,47084,11,47140,47140,11,47196,47196,11,47252,47252,11,47308,47308,11,47364,47364,11,47420,47420,11,47476,47476,11,47532,47532,11,47588,47588,11,47644,47644,11,47700,47700,11,47756,47756,11,47812,47812,11,47868,47868,11,47924,47924,11,47980,47980,11,48036,48036,11,48092,48092,11,48148,48148,11,48204,48204,11,48260,48260,11,48316,48316,11,48372,48372,11,48428,48428,11,48484,48484,11,48540,48540,11,48596,48596,11,48652,48652,11,48708,48708,11,48764,48764,11,48820,48820,11,48876,48876,11,48932,48932,11,48988,48988,11,49044,49044,11,49100,49100,11,49156,49156,11,49212,49212,11,49268,49268,11,49324,49324,11,49380,49380,11,49436,49436,11,49492,49492,11,49548,49548,11,49604,49604,11,49660,49660,11,49716,49716,11,49772,49772,11,49828,49828,11,49884,49884,11,49940,49940,11,49996,49996,11,50052,50052,11,50108,50108,11,50164,50164,11,50220,50220,11,50276,50276,11,50332,50332,11,50388,50388,11,50444,50444,11,50500,50500,11,50556,50556,11,50612,50612,11,50668,50668,11,50724,50724,11,50780,50780,11,50836,50836,11,50892,50892,11,50948,50948,11,51004,51004,11,51060,51060,11,51116,51116,11,51172,51172,11,51228,51228,11,51284,51284,11,51340,51340,11,51396,51396,11,51452,51452,11,51508,51508,11,51564,51564,11,51620,51620,11,51676,51676,11,51732,51732,11,51788,51788,11,51844,51844,11,51900,51900,11,51956,51956,11,52012,52012,11,52068,52068,11,52124,52124,11,52180,52180,11,52236,52236,11,52292,52292,11,52348,52348,11,52404,52404,11,52460,52460,11,52516,52516,11,52572,52572,11,52628,52628,11,52684,52684,11,52740,52740,11,52796,52796,11,52852,52852,11,52908,52908,11,52964,52964,11,53020,53020,11,53076,53076,11,53132,53132,11,53188,53188,11,53244,53244,11,53300,53300,11,53356,53356,11,53412,53412,11,53468,53468,11,53524,53524,11,53580,53580,11,53636,53636,11,53692,53692,11,53748,53748,11,53804,53804,11,53860,53860,11,53916,53916,11,53972,53972,11,54028,54028,11,54084,54084,11,54140,54140,11,54196,54196,11,54252,54252,11,54308,54308,11,54364,54364,11,54420,54420,11,54476,54476,11,54532,54532,11,54588,54588,11,54644,54644,11,54700,54700,11,54756,54756,11,54812,54812,11,54868,54868,11,54924,54924,11,54980,54980,11,55036,55036,11,55092,55092,11,55148,55148,11,55216,55238,9,65056,65071,5,65529,65531,4,68097,68099,5,68159,68159,5,69446,69456,5,69688,69702,5,69808,69810,7,69815,69816,7,69821,69821,1,69888,69890,5,69932,69932,7,69957,69958,7,70016,70017,5,70067,70069,7,70079,70080,7,70089,70092,5,70095,70095,5,70191,70193,5,70196,70196,5,70198,70199,5,70367,70367,5,70371,70378,5,70402,70403,7,70462,70462,5,70464,70464,5,70471,70472,7,70487,70487,5,70502,70508,5,70709,70711,7,70720,70721,7,70725,70725,7,70750,70750,5,70833,70834,7,70841,70841,7,70843,70844,7,70846,70846,7,70849,70849,7,71087,71087,5,71090,71093,5,71100,71101,5,71103,71104,5,71216,71218,7,71227,71228,7,71230,71230,7,71339,71339,5,71341,71341,5,71344,71349,5,71351,71351,5,71456,71457,7,71462,71462,7,71724,71726,7,71736,71736,7,71984,71984,5,71991,71992,7,71997,71997,7,71999,71999,1,72001,72001,1,72003,72003,5,72148,72151,5,72156,72159,7,72164,72164,7,72243,72248,5,72250,72250,1,72263,72263,5,72279,72280,7,72324,72329,1,72343,72343,7,72751,72751,7,72760,72765,5,72767,72767,5,72873,72873,7,72881,72881,7,72884,72884,7,73009,73014,5,73020,73021,5,73030,73030,1,73098,73102,7,73107,73108,7,73110,73110,7,73459,73460,5,78896,78904,4,92976,92982,5,94033,94087,7,94180,94180,5,113821,113822,5,119141,119141,5,119143,119145,5,119150,119154,5,119163,119170,5,119210,119213,5,121344,121398,5,121461,121461,5,121499,121503,5,122880,122886,5,122907,122913,5,122918,122922,5,123628,123631,5,125252,125258,5,126980,126980,14,127183,127183,14,127245,127247,14,127340,127343,14,127358,127359,14,127377,127386,14,127462,127487,6,127491,127503,14,127535,127535,14,127548,127551,14,127568,127569,14,127744,127777,14,127780,127891,14,127894,127895,14,127897,127899,14,127902,127984,14,127987,127989,14,127991,127994,14,128000,128253,14,128255,128317,14,128329,128334,14,128336,128359,14,128367,128368,14,128371,128377,14,128379,128390,14,128392,128393,14,128398,128399,14,128401,128404,14,128407,128419,14,128421,128421,14,128424,128424,14,128433,128434,14,128444,128444,14,128450,128452,14,128465,128467,14,128476,128478,14,128481,128481,14,128483,128483,14,128488,128488,14,128495,128495,14,128499,128499,14,128506,128591,14,128710,128714,14,128721,128722,14,128725,128725,14,128728,128735,14,128742,128744,14,128746,128746,14,128749,128751,14,128753,128754,14,128756,128758,14,128761,128761,14,128763,128764,14,128884,128895,14,128992,129003,14,129036,129039,14,129114,129119,14,129198,129279,14,129293,129295,14,129305,129310,14,129312,129319,14,129328,129328,14,129331,129338,14,129343,129343,14,129351,129355,14,129357,129359,14,129375,129387,14,129393,129393,14,129395,129398,14,129401,129401,14,129403,129403,14,129408,129412,14,129426,129431,14,129443,129444,14,129451,129453,14,129456,129465,14,129472,129472,14,129475,129482,14,129484,129484,14,129488,129510,14,129536,129647,14,129652,129652,14,129656,129658,14,129664,129666,14,129671,129679,14,129686,129704,14,129712,129718,14,129728,129730,14,129744,129750,14,917504,917504,4,917506,917535,4,917632,917759,4,918000,921599,4,0,9,4,11,12,4,14,31,4,169,169,14,174,174,14,1155,1159,5,1425,1469,5,1473,1474,5,1479,1479,5,1552,1562,5,1611,1631,5,1750,1756,5,1759,1764,5,1770,1773,5,1809,1809,5,1958,1968,5,2045,2045,5,2075,2083,5,2089,2093,5,2259,2273,5,2275,2306,5,2362,2362,5,2364,2364,5,2369,2376,5,2381,2381,5,2385,2391,5,2433,2433,5,2492,2492,5,2495,2496,7,2503,2504,7,2509,2509,5,2530,2531,5,2561,2562,5,2620,2620,5,2625,2626,5,2635,2637,5,2672,2673,5,2689,2690,5,2748,2748,5,2753,2757,5,2761,2761,7,2765,2765,5,2810,2815,5,2818,2819,7,2878,2878,5,2880,2880,7,2887,2888,7,2893,2893,5,2903,2903,5,2946,2946,5,3007,3007,7,3009,3010,7,3018,3020,7,3031,3031,5,3073,3075,7,3134,3136,5,3142,3144,5,3157,3158,5,3201,3201,5,3260,3260,5,3263,3263,5,3266,3266,5,3270,3270,5,3274,3275,7,3285,3286,5,3328,3329,5,3387,3388,5,3391,3392,7,3398,3400,7,3405,3405,5,3415,3415,5,3457,3457,5,3530,3530,5,3536,3537,7,3542,3542,5,3551,3551,5,3633,3633,5,3636,3642,5,3761,3761,5,3764,3772,5,3864,3865,5,3895,3895,5,3902,3903,7,3967,3967,7,3974,3975,5,3993,4028,5,4141,4144,5,4146,4151,5,4155,4156,7,4182,4183,7,4190,4192,5,4226,4226,5,4229,4230,5,4253,4253,5,4448,4519,9,4957,4959,5,5938,5940,5,6002,6003,5,6070,6070,7,6078,6085,7,6087,6088,7,6109,6109,5,6158,6158,4,6313,6313,5,6435,6438,7,6441,6443,7,6450,6450,5,6457,6459,5,6681,6682,7,6741,6741,7,6743,6743,7,6752,6752,5,6757,6764,5,6771,6780,5,6832,6845,5,6847,6848,5,6916,6916,7,6965,6965,5,6971,6971,7,6973,6977,7,6979,6980,7,7040,7041,5,7073,7073,7,7078,7079,7,7082,7082,7,7142,7142,5,7144,7145,5,7149,7149,5,7151,7153,5,7204,7211,7,7220,7221,7,7376,7378,5,7393,7393,7,7405,7405,5,7415,7415,7,7616,7673,5,8203,8203,4,8205,8205,13,8232,8232,4,8234,8238,4,8265,8265,14,8293,8293,4,8400,8412,5,8417,8417,5,8421,8432,5,8505,8505,14,8617,8618,14,9000,9000,14,9167,9167,14,9208,9210,14,9642,9643,14,9664,9664,14,9728,9732,14,9735,9741,14,9743,9744,14,9746,9746,14,9750,9751,14,9753,9756,14,9758,9759,14,9761,9761,14,9764,9765,14,9767,9769,14,9771,9773,14,9776,9783,14,9787,9791,14,9793,9793,14,9795,9799,14,9812,9822,14,9824,9824,14,9827,9827,14,9829,9830,14,9832,9832,14,9851,9851,14,9854,9854,14,9856,9861,14,9874,9876,14,9878,9879,14,9881,9881,14,9883,9884,14,9888,9889,14,9895,9895,14,9898,9899,14,9904,9905,14,9917,9918,14,9924,9925,14,9928,9928,14,9934,9935,14,9937,9937,14,9939,9940,14,9961,9962,14,9968,9973,14,9975,9978,14,9981,9981,14,9986,9986,14,9989,9989,14,9998,9998,14,10000,10001,14,10004,10004,14,10013,10013,14,10024,10024,14,10052,10052,14,10060,10060,14,10067,10069,14,10083,10084,14,10133,10135,14,10160,10160,14,10548,10549,14,11035,11036,14,11093,11093,14,11647,11647,5,12330,12333,5,12336,12336,14,12441,12442,5,12953,12953,14,42608,42610,5,42654,42655,5,43010,43010,5,43019,43019,5,43045,43046,5,43052,43052,5,43188,43203,7,43232,43249,5,43302,43309,5,43346,43347,7,43392,43394,5,43443,43443,5,43446,43449,5,43452,43453,5,43493,43493,5,43567,43568,7,43571,43572,7,43587,43587,5,43597,43597,7,43696,43696,5,43703,43704,5,43713,43713,5,43756,43757,5,43765,43765,7,44003,44004,7,44006,44007,7,44009,44010,7,44013,44013,5,44033,44059,12,44061,44087,12,44089,44115,12,44117,44143,12,44145,44171,12,44173,44199,12,44201,44227,12,44229,44255,12,44257,44283,12,44285,44311,12,44313,44339,12,44341,44367,12,44369,44395,12,44397,44423,12,44425,44451,12,44453,44479,12,44481,44507,12,44509,44535,12,44537,44563,12,44565,44591,12,44593,44619,12,44621,44647,12,44649,44675,12,44677,44703,12,44705,44731,12,44733,44759,12,44761,44787,12,44789,44815,12,44817,44843,12,44845,44871,12,44873,44899,12,44901,44927,12,44929,44955,12,44957,44983,12,44985,45011,12,45013,45039,12,45041,45067,12,45069,45095,12,45097,45123,12,45125,45151,12,45153,45179,12,45181,45207,12,45209,45235,12,45237,45263,12,45265,45291,12,45293,45319,12,45321,45347,12,45349,45375,12,45377,45403,12,45405,45431,12,45433,45459,12,45461,45487,12,45489,45515,12,45517,45543,12,45545,45571,12,45573,45599,12,45601,45627,12,45629,45655,12,45657,45683,12,45685,45711,12,45713,45739,12,45741,45767,12,45769,45795,12,45797,45823,12,45825,45851,12,45853,45879,12,45881,45907,12,45909,45935,12,45937,45963,12,45965,45991,12,45993,46019,12,46021,46047,12,46049,46075,12,46077,46103,12,46105,46131,12,46133,46159,12,46161,46187,12,46189,46215,12,46217,46243,12,46245,46271,12,46273,46299,12,46301,46327,12,46329,46355,12,46357,46383,12,46385,46411,12,46413,46439,12,46441,46467,12,46469,46495,12,46497,46523,12,46525,46551,12,46553,46579,12,46581,46607,12,46609,46635,12,46637,46663,12,46665,46691,12,46693,46719,12,46721,46747,12,46749,46775,12,46777,46803,12,46805,46831,12,46833,46859,12,46861,46887,12,46889,46915,12,46917,46943,12,46945,46971,12,46973,46999,12,47001,47027,12,47029,47055,12,47057,47083,12,47085,47111,12,47113,47139,12,47141,47167,12,47169,47195,12,47197,47223,12,47225,47251,12,47253,47279,12,47281,47307,12,47309,47335,12,47337,47363,12,47365,47391,12,47393,47419,12,47421,47447,12,47449,47475,12,47477,47503,12,47505,47531,12,47533,47559,12,47561,47587,12,47589,47615,12,47617,47643,12,47645,47671,12,47673,47699,12,47701,47727,12,47729,47755,12,47757,47783,12,47785,47811,12,47813,47839,12,47841,47867,12,47869,47895,12,47897,47923,12,47925,47951,12,47953,47979,12,47981,48007,12,48009,48035,12,48037,48063,12,48065,48091,12,48093,48119,12,48121,48147,12,48149,48175,12,48177,48203,12,48205,48231,12,48233,48259,12,48261,48287,12,48289,48315,12,48317,48343,12,48345,48371,12,48373,48399,12,48401,48427,12,48429,48455,12,48457,48483,12,48485,48511,12,48513,48539,12,48541,48567,12,48569,48595,12,48597,48623,12,48625,48651,12,48653,48679,12,48681,48707,12,48709,48735,12,48737,48763,12,48765,48791,12,48793,48819,12,48821,48847,12,48849,48875,12,48877,48903,12,48905,48931,12,48933,48959,12,48961,48987,12,48989,49015,12,49017,49043,12,49045,49071,12,49073,49099,12,49101,49127,12,49129,49155,12,49157,49183,12,49185,49211,12,49213,49239,12,49241,49267,12,49269,49295,12,49297,49323,12,49325,49351,12,49353,49379,12,49381,49407,12,49409,49435,12,49437,49463,12,49465,49491,12,49493,49519,12,49521,49547,12,49549,49575,12,49577,49603,12,49605,49631,12,49633,49659,12,49661,49687,12,49689,49715,12,49717,49743,12,49745,49771,12,49773,49799,12,49801,49827,12,49829,49855,12,49857,49883,12,49885,49911,12,49913,49939,12,49941,49967,12,49969,49995,12,49997,50023,12,50025,50051,12,50053,50079,12,50081,50107,12,50109,50135,12,50137,50163,12,50165,50191,12,50193,50219,12,50221,50247,12,50249,50275,12,50277,50303,12,50305,50331,12,50333,50359,12,50361,50387,12,50389,50415,12,50417,50443,12,50445,50471,12,50473,50499,12,50501,50527,12,50529,50555,12,50557,50583,12,50585,50611,12,50613,50639,12,50641,50667,12,50669,50695,12,50697,50723,12,50725,50751,12,50753,50779,12,50781,50807,12,50809,50835,12,50837,50863,12,50865,50891,12,50893,50919,12,50921,50947,12,50949,50975,12,50977,51003,12,51005,51031,12,51033,51059,12,51061,51087,12,51089,51115,12,51117,51143,12,51145,51171,12,51173,51199,12,51201,51227,12,51229,51255,12,51257,51283,12,51285,51311,12,51313,51339,12,51341,51367,12,51369,51395,12,51397,51423,12,51425,51451,12,51453,51479,12,51481,51507,12,51509,51535,12,51537,51563,12,51565,51591,12,51593,51619,12,51621,51647,12,51649,51675,12,51677,51703,12,51705,51731,12,51733,51759,12,51761,51787,12,51789,51815,12,51817,51843,12,51845,51871,12,51873,51899,12,51901,51927,12,51929,51955,12,51957,51983,12,51985,52011,12,52013,52039,12,52041,52067,12,52069,52095,12,52097,52123,12,52125,52151,12,52153,52179,12,52181,52207,12,52209,52235,12,52237,52263,12,52265,52291,12,52293,52319,12,52321,52347,12,52349,52375,12,52377,52403,12,52405,52431,12,52433,52459,12,52461,52487,12,52489,52515,12,52517,52543,12,52545,52571,12,52573,52599,12,52601,52627,12,52629,52655,12,52657,52683,12,52685,52711,12,52713,52739,12,52741,52767,12,52769,52795,12,52797,52823,12,52825,52851,12,52853,52879,12,52881,52907,12,52909,52935,12,52937,52963,12,52965,52991,12,52993,53019,12,53021,53047,12,53049,53075,12,53077,53103,12,53105,53131,12,53133,53159,12,53161,53187,12,53189,53215,12,53217,53243,12,53245,53271,12,53273,53299,12,53301,53327,12,53329,53355,12,53357,53383,12,53385,53411,12,53413,53439,12,53441,53467,12,53469,53495,12,53497,53523,12,53525,53551,12,53553,53579,12,53581,53607,12,53609,53635,12,53637,53663,12,53665,53691,12,53693,53719,12,53721,53747,12,53749,53775,12,53777,53803,12,53805,53831,12,53833,53859,12,53861,53887,12,53889,53915,12,53917,53943,12,53945,53971,12,53973,53999,12,54001,54027,12,54029,54055,12,54057,54083,12,54085,54111,12,54113,54139,12,54141,54167,12,54169,54195,12,54197,54223,12,54225,54251,12,54253,54279,12,54281,54307,12,54309,54335,12,54337,54363,12,54365,54391,12,54393,54419,12,54421,54447,12,54449,54475,12,54477,54503,12,54505,54531,12,54533,54559,12,54561,54587,12,54589,54615,12,54617,54643,12,54645,54671,12,54673,54699,12,54701,54727,12,54729,54755,12,54757,54783,12,54785,54811,12,54813,54839,12,54841,54867,12,54869,54895,12,54897,54923,12,54925,54951,12,54953,54979,12,54981,55007,12,55009,55035,12,55037,55063,12,55065,55091,12,55093,55119,12,55121,55147,12,55149,55175,12,55177,55203,12,55243,55291,10,65024,65039,5,65279,65279,4,65520,65528,4,66045,66045,5,66422,66426,5,68101,68102,5,68152,68154,5,68325,68326,5,69291,69292,5,69632,69632,7,69634,69634,7,69759,69761,5]")}},4035:function(t,e,n){"use strict";n.d(e,"e",(function(){return r})),n.d(e,"d",(function(){return o})),n.d(e,"c",(function(){return u})),n.d(e,"b",(function(){return l})),n.d(e,"a",(function(){return p}));var i=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},t(e,n)};return function(e,n){function i(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}();function r(t){var e=[];return t.forEach((function(t){return e.push(t)})),e}function o(t){var e=[];return t.forEach((function(t,n){return e.push(n)})),e}var s=function(){function t(){this._value="",this._pos=0}return t.prototype.reset=function(t){return this._value=t,this._pos=0,this},t.prototype.next=function(){return this._pos+=1,this},t.prototype.hasNext=function(){return this._pos<this._value.length-1},t.prototype.cmp=function(t){var e=t.charCodeAt(0),n=this._value.charCodeAt(this._pos);return e-n},t.prototype.value=function(){return this._value[this._pos]},t}(),a=function(){function t(t){void 0===t&&(t=!0),this._splitOnBackslash=t}return t.prototype.reset=function(t){return this._value=t.replace(/\\$|\/$/,""),this._from=0,this._to=0,this.next()},t.prototype.hasNext=function(){return this._to<this._value.length},t.prototype.next=function(){this._from=this._to;for(var t=!0;this._to<this._value.length;this._to++){var e=this._value.charCodeAt(this._to);if(47===e||this._splitOnBackslash&&92===e){if(!t)break;this._from++}else t=!1}return this},t.prototype.cmp=function(t){var e=0,n=t.length,i=this._from;while(e<n&&i<this._to){var r=t.charCodeAt(e)-this._value.charCodeAt(i);if(0!==r)return r;e+=1,i+=1}return n===this._to-this._from?0:e<n?-1:1},t.prototype.value=function(){return this._value.substring(this._from,this._to)},t}(),h=function(){function t(){}return t}(),u=function(){function t(t){this._iter=t}return t.forPaths=function(){return new t(new a)},t.forStrings=function(){return new t(new s)},t.prototype.clear=function(){this._root=void 0},t.prototype.set=function(t,e){var n,i=this._iter.reset(t);this._root||(this._root=new h,this._root.segment=i.value()),n=this._root;while(1){var r=i.cmp(n.segment);if(r>0)n.left||(n.left=new h,n.left.segment=i.value()),n=n.left;else if(r<0)n.right||(n.right=new h,n.right.segment=i.value()),n=n.right;else{if(!i.hasNext())break;i.next(),n.mid||(n.mid=new h,n.mid.segment=i.value()),n=n.mid}}var o=n.value;return n.value=e,n.key=t,o},t.prototype.get=function(t){var e=this._iter.reset(t),n=this._root;while(n){var i=e.cmp(n.segment);if(i>0)n=n.left;else if(i<0)n=n.right;else{if(!e.hasNext())break;e.next(),n=n.mid}}return n?n.value:void 0},t.prototype.findSubstr=function(t){var e=this._iter.reset(t),n=this._root,i=void 0;while(n){var r=e.cmp(n.segment);if(r>0)n=n.left;else if(r<0)n=n.right;else{if(!e.hasNext())break;e.next(),i=n.value||i,n=n.mid}}return n&&n.value||i},t.prototype.forEach=function(t){this._forEach(this._root,t)},t.prototype._forEach=function(t,e){t&&(this._forEach(t.left,e),t.value&&e(t.value,t.key),this._forEach(t.mid,e),this._forEach(t.right,e))},t}(),l=function(){function t(){this.map=new Map,this.ignoreCase=!1}return t.prototype.set=function(t,e){this.map.set(this.toKey(t),e)},t.prototype.get=function(t){return this.map.get(this.toKey(t))},t.prototype.toKey=function(t){var e=t.toString();return this.ignoreCase&&(e=e.toLowerCase()),e},t}(),c=function(){function t(){this._map=new Map,this._head=void 0,this._tail=void 0,this._size=0}return t.prototype.clear=function(){this._map.clear(),this._head=void 0,this._tail=void 0,this._size=0},Object.defineProperty(t.prototype,"size",{get:function(){return this._size},enumerable:!0,configurable:!0}),t.prototype.get=function(t,e){void 0===e&&(e=0);var n=this._map.get(t);if(n)return 0!==e&&this.touch(n,e),n.value},t.prototype.set=function(t,e,n){void 0===n&&(n=0);var i=this._map.get(t);if(i)i.value=e,0!==n&&this.touch(i,n);else{switch(i={key:t,value:e,next:void 0,previous:void 0},n){case 0:this.addItemLast(i);break;case 1:this.addItemFirst(i);break;case 2:this.addItemLast(i);break;default:this.addItemLast(i);break}this._map.set(t,i),this._size++}},t.prototype.delete=function(t){return!!this.remove(t)},t.prototype.remove=function(t){var e=this._map.get(t);if(e)return this._map.delete(t),this.removeItem(e),this._size--,e.value},t.prototype.forEach=function(t,e){var n=this._head;while(n)e?t.bind(e)(n.value,n.key,this):t(n.value,n.key,this),n=n.next},t.prototype.trimOld=function(t){if(!(t>=this.size))if(0!==t){var e=this._head,n=this.size;while(e&&n>t)this._map.delete(e.key),e=e.next,n--;this._head=e,this._size=n,e&&(e.previous=void 0)}else this.clear()},t.prototype.addItemFirst=function(t){if(this._head||this._tail){if(!this._head)throw new Error("Invalid list");t.next=this._head,this._head.previous=t}else this._tail=t;this._head=t},t.prototype.addItemLast=function(t){if(this._head||this._tail){if(!this._tail)throw new Error("Invalid list");t.previous=this._tail,this._tail.next=t}else this._head=t;this._tail=t},t.prototype.removeItem=function(t){if(t===this._head&&t===this._tail)this._head=void 0,this._tail=void 0;else if(t===this._head){if(!t.next)throw new Error("Invalid list");t.next.previous=void 0,this._head=t.next}else if(t===this._tail){if(!t.previous)throw new Error("Invalid list");t.previous.next=void 0,this._tail=t.previous}else{var e=t.next,n=t.previous;if(!e||!n)throw new Error("Invalid list");e.previous=n,n.next=e}t.next=void 0,t.previous=void 0},t.prototype.touch=function(t,e){if(!this._head||!this._tail)throw new Error("Invalid list");if(1===e||2===e)if(1===e){if(t===this._head)return;var n=t.next,i=t.previous;t===this._tail?(i.next=void 0,this._tail=i):(n.previous=i,i.next=n),t.previous=void 0,t.next=this._head,this._head.previous=t,this._head=t}else if(2===e){if(t===this._tail)return;n=t.next,i=t.previous;t===this._head?(n.previous=void 0,this._head=n):(n.previous=i,i.next=n),t.next=void 0,t.previous=this._tail,this._tail.next=t,this._tail=t}},t.prototype.toJSON=function(){var t=[];return this.forEach((function(e,n){t.push([n,e])})),t},t}(),p=function(t){function e(e,n){void 0===n&&(n=1);var i=t.call(this)||this;return i._limit=e,i._ratio=Math.min(Math.max(0,n),1),i}return i(e,t),e.prototype.get=function(e){return t.prototype.get.call(this,e,2)},e.prototype.peek=function(e){return t.prototype.get.call(this,e,0)},e.prototype.set=function(e,n){t.prototype.set.call(this,e,n,2),this.checkTrim()},e.prototype.checkTrim=function(){this.size>this._limit&&this.trimOld(Math.round(this._limit*this._ratio))},e}(c)},"41d2":function(t,e,n){"use strict";n.d(e,"a",(function(){return i}));var i=function(){function t(t){this.executor=t,this._didRun=!1}return t.prototype.getValue=function(){if(!this._didRun)try{this._value=this.executor()}catch(t){this._error=t}finally{this._didRun=!0}if(this._error)throw this._error;return this._value},Object.defineProperty(t.prototype,"rawValue",{get:function(){return this._value},enumerable:!0,configurable:!0}),t}()},"42e3":function(t,e,n){"use strict";n.d(e,"a",(function(){return a}));var i=n("308f"),r=n("a666"),o=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},t(e,n)};return function(e,n){function i(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),s=function(){function t(t,e,n,i,r,o){t|=0,e|=0,n|=0,i|=0,r|=0,o|=0,t<0&&(t=0),n+t>e&&(n=e-t),n<0&&(n=0),i<0&&(i=0),o+i>r&&(o=r-i),o<0&&(o=0),this.width=t,this.scrollWidth=e,this.scrollLeft=n,this.height=i,this.scrollHeight=r,this.scrollTop=o}return t.prototype.equals=function(t){return this.width===t.width&&this.scrollWidth===t.scrollWidth&&this.scrollLeft===t.scrollLeft&&this.height===t.height&&this.scrollHeight===t.scrollHeight&&this.scrollTop===t.scrollTop},t.prototype.withScrollDimensions=function(e){return new t("undefined"!==typeof e.width?e.width:this.width,"undefined"!==typeof e.scrollWidth?e.scrollWidth:this.scrollWidth,this.scrollLeft,"undefined"!==typeof e.height?e.height:this.height,"undefined"!==typeof e.scrollHeight?e.scrollHeight:this.scrollHeight,this.scrollTop)},t.prototype.withScrollPosition=function(e){return new t(this.width,this.scrollWidth,"undefined"!==typeof e.scrollLeft?e.scrollLeft:this.scrollLeft,this.height,this.scrollHeight,"undefined"!==typeof e.scrollTop?e.scrollTop:this.scrollTop)},t.prototype.createScrollEvent=function(t){var e=this.width!==t.width,n=this.scrollWidth!==t.scrollWidth,i=this.scrollLeft!==t.scrollLeft,r=this.height!==t.height,o=this.scrollHeight!==t.scrollHeight,s=this.scrollTop!==t.scrollTop;return{width:this.width,scrollWidth:this.scrollWidth,scrollLeft:this.scrollLeft,height:this.height,scrollHeight:this.scrollHeight,scrollTop:this.scrollTop,widthChanged:e,scrollWidthChanged:n,scrollLeftChanged:i,heightChanged:r,scrollHeightChanged:o,scrollTopChanged:s}},t}(),a=function(t){function e(e,n){var r=t.call(this)||this;return r._onScroll=r._register(new i["a"]),r.onScroll=r._onScroll.event,r._smoothScrollDuration=e,r._scheduleAtNextAnimationFrame=n,r._state=new s(0,0,0,0,0,0),r._smoothScrolling=null,r}return o(e,t),e.prototype.dispose=function(){this._smoothScrolling&&(this._smoothScrolling.dispose(),this._smoothScrolling=null),t.prototype.dispose.call(this)},e.prototype.setSmoothScrollDuration=function(t){this._smoothScrollDuration=t},e.prototype.validateScrollPosition=function(t){return this._state.withScrollPosition(t)},e.prototype.getScrollDimensions=function(){return this._state},e.prototype.setScrollDimensions=function(t){var e=this._state.withScrollDimensions(t);this._setState(e),this._smoothScrolling&&this._smoothScrolling.acceptScrollDimensions(this._state)},e.prototype.getFutureScrollPosition=function(){return this._smoothScrolling?this._smoothScrolling.to:this._state},e.prototype.getCurrentScrollPosition=function(){return this._state},e.prototype.setScrollPositionNow=function(t){var e=this._state.withScrollPosition(t);this._smoothScrolling&&(this._smoothScrolling.dispose(),this._smoothScrolling=null),this._setState(e)},e.prototype.setScrollPositionSmooth=function(t){var e=this;if(0===this._smoothScrollDuration)return this.setScrollPositionNow(t);if(this._smoothScrolling){t={scrollLeft:"undefined"===typeof t.scrollLeft?this._smoothScrolling.to.scrollLeft:t.scrollLeft,scrollTop:"undefined"===typeof t.scrollTop?this._smoothScrolling.to.scrollTop:t.scrollTop};var n=this._state.withScrollPosition(t);if(this._smoothScrolling.to.scrollLeft===n.scrollLeft&&this._smoothScrolling.to.scrollTop===n.scrollTop)return;var i=this._smoothScrolling.combine(this._state,n,this._smoothScrollDuration);this._smoothScrolling.dispose(),this._smoothScrolling=i}else{n=this._state.withScrollPosition(t);this._smoothScrolling=c.start(this._state,n,this._smoothScrollDuration)}this._smoothScrolling.animationFrameDisposable=this._scheduleAtNextAnimationFrame((function(){e._smoothScrolling&&(e._smoothScrolling.animationFrameDisposable=null,e._performSmoothScrolling())}))},e.prototype._performSmoothScrolling=function(){var t=this;if(this._smoothScrolling){var e=this._smoothScrolling.tick(),n=this._state.withScrollPosition(e);if(this._setState(n),e.isDone)return this._smoothScrolling.dispose(),void(this._smoothScrolling=null);this._smoothScrolling.animationFrameDisposable=this._scheduleAtNextAnimationFrame((function(){t._smoothScrolling&&(t._smoothScrolling.animationFrameDisposable=null,t._performSmoothScrolling())}))}},e.prototype._setState=function(t){var e=this._state;e.equals(t)||(this._state=t,this._onScroll.fire(this._state.createScrollEvent(e)))},e}(r["a"]),h=function(){function t(t,e,n){this.scrollLeft=t,this.scrollTop=e,this.isDone=n}return t}();function u(t,e){var n=e-t;return function(e){return t+n*d(e)}}function l(t,e,n){return function(i){return i<n?t(i/n):e((i-n)/(1-n))}}var c=function(){function t(t,e,n,i){this.from=t,this.to=e,this.duration=i,this._startTime=n,this.animationFrameDisposable=null,this._initAnimations()}return t.prototype._initAnimations=function(){this.scrollLeft=this._initAnimation(this.from.scrollLeft,this.to.scrollLeft,this.to.width),this.scrollTop=this._initAnimation(this.from.scrollTop,this.to.scrollTop,this.to.height)},t.prototype._initAnimation=function(t,e,n){var i=Math.abs(t-e);if(i>2.5*n){var r=void 0,o=void 0;return t<e?(r=t+.75*n,o=e-.75*n):(r=t-.75*n,o=e+.75*n),l(u(t,r),u(o,e),.33)}return u(t,e)},t.prototype.dispose=function(){null!==this.animationFrameDisposable&&(this.animationFrameDisposable.dispose(),this.animationFrameDisposable=null)},t.prototype.acceptScrollDimensions=function(t){this.to=t.withScrollPosition(this.to),this._initAnimations()},t.prototype.tick=function(){return this._tick(Date.now())},t.prototype._tick=function(t){var e=(t-this._startTime)/this.duration;if(e<1){var n=this.scrollLeft(e),i=this.scrollTop(e);return new h(n,i,!1)}return new h(this.to.scrollLeft,this.to.scrollTop,!0)},t.prototype.combine=function(e,n,i){return t.start(e,n,i)},t.start=function(e,n,i){i+=10;var r=Date.now()-10;return new t(e,n,r,i)},t}();function p(t){return Math.pow(t,3)}function d(t){return 1-p(1-t)}},"438a":function(t,e,n){"use strict";n.d(e,"a",(function(){return r}));var i=n("6d8e");function r(t){var e=JSON.parse(t);return e=o(e),e}function o(t,e){if(void 0===e&&(e=0),!t||e>200)return t;if("object"===typeof t){switch(t.$mid){case 1:return i["a"].revive(t);case 2:return new RegExp(t.source,t.flags)}for(var n in t)Object.hasOwnProperty.call(t,n)&&(t[n]=o(t[n],e+1))}return t}},"469c":function(t,e,n){"use strict";n.d(e,"a",(function(){return f})),n.d(e,"b",(function(){return g})),n.d(e,"c",(function(){return _}));var i=n("dff7"),r=n("ef8e"),o=n("c4e3"),s=n("5aa5"),a=n("ee56"),h=n("11f7"),u=n("8daa"),l=n("30db"),c=n("e8e3"),p=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},t(e,n)};return function(e,n){function i(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),d=0,f=function(){function t(t){void 0===t&&(t=[]),this.id=(d++).toString(),this.labelHighlights=t,this.descriptionHighlights=[]}return t.prototype.getId=function(){return this.id},t.prototype.getLabel=function(){},t.prototype.getLabelOptions=function(){},t.prototype.getAriaLabel=function(){return Object(c["d"])([this.getLabel(),this.getDescription(),this.getDetail()]).join(", ")},t.prototype.getDetail=function(){},t.prototype.getIcon=function(){},t.prototype.getDescription=function(){},t.prototype.getTooltip=function(){},t.prototype.getDescriptionTooltip=function(){},t.prototype.getKeybinding=function(){},t.prototype.isHidden=function(){return!!this.hidden},t.prototype.setHighlights=function(t,e,n){this.labelHighlights=t,this.descriptionHighlights=e,this.detailHighlights=n},t.prototype.getHighlights=function(){return[this.labelHighlights,this.descriptionHighlights,this.detailHighlights]},t.prototype.run=function(t,e){return!1},t}(),g=function(t){function e(e,n,i){var r=t.call(this)||this;return r.entry=e,r.groupLabel=n,r.withBorder=i,r}return p(e,t),e.prototype.getGroupLabel=function(){return this.groupLabel},e.prototype.setGroupLabel=function(t){this.groupLabel=t},e.prototype.showBorder=function(){return!!this.withBorder},e.prototype.setShowBorder=function(t){this.withBorder=t},e.prototype.getLabel=function(){return this.entry?this.entry.getLabel():t.prototype.getLabel.call(this)},e.prototype.getLabelOptions=function(){return this.entry?this.entry.getLabelOptions():t.prototype.getLabelOptions.call(this)},e.prototype.getAriaLabel=function(){return this.entry?this.entry.getAriaLabel():t.prototype.getAriaLabel.call(this)},e.prototype.getDetail=function(){return this.entry?this.entry.getDetail():t.prototype.getDetail.call(this)},e.prototype.getIcon=function(){return this.entry?this.entry.getIcon():t.prototype.getIcon.call(this)},e.prototype.getDescription=function(){return this.entry?this.entry.getDescription():t.prototype.getDescription.call(this)},e.prototype.getHighlights=function(){return this.entry?this.entry.getHighlights():t.prototype.getHighlights.call(this)},e.prototype.isHidden=function(){return this.entry?this.entry.isHidden():t.prototype.isHidden.call(this)},e.prototype.setHighlights=function(e,n,i){this.entry?this.entry.setHighlights(e,n,i):t.prototype.setHighlights.call(this,e,n,i)},e.prototype.run=function(e,n){return this.entry?this.entry.run(e,n):t.prototype.run.call(this,e,n)},e}(f),m=function(){function t(){}return t.prototype.hasActions=function(t,e){return!1},t.prototype.getActions=function(t,e){return null},t}(),y="quickOpenEntry",v="quickOpenEntryGroup",b=function(){function t(t,e){void 0===t&&(t=new m),this.actionProvider=t,this.actionRunner=e}return t.prototype.getHeight=function(t){return t.getDetail()?44:22},t.prototype.getTemplateId=function(t){return t instanceof g?v:y},t.prototype.renderTemplate=function(t,e,n){var i=document.createElement("div");h["f"](i,"sub-content"),e.appendChild(i);var r=h["a"](".quick-open-row"),c=h["a"](".quick-open-row"),p=h["a"](".quick-open-entry",void 0,r,c);i.appendChild(p);var d=document.createElement("span");r.appendChild(d);var f=new o["a"](r,{supportHighlights:!0,supportDescriptionHighlights:!0,supportCodicons:!0}),g=document.createElement("span");r.appendChild(g),h["f"](g,"quick-open-entry-keybinding");var m=new u["a"](g,l["a"]),y=document.createElement("div");c.appendChild(y),h["f"](y,"quick-open-entry-meta");var b,_=new a["a"](y,!0);t===v&&(b=document.createElement("div"),h["f"](b,"results-group"),e.appendChild(b)),h["f"](e,"actions");var w=document.createElement("div");h["f"](w,"primary-action-bar"),e.appendChild(w);var x=new s["a"](w,{actionRunner:this.actionRunner});return{container:e,entry:p,icon:d,label:f,detail:_,keybinding:m,group:b,actionBar:x}},t.prototype.renderElement=function(t,e,n,i){this.actionProvider.hasActions(null,t)?h["f"](n.container,"has-actions"):h["P"](n.container,"has-actions"),n.actionBar.context=t;var r=this.actionProvider.getActions(null,t);if(n.actionBar.isEmpty()&&r&&r.length>0?n.actionBar.push(r,{icon:!0,label:!1}):n.actionBar.isEmpty()||r&&0!==r.length||n.actionBar.clear(),t instanceof g&&t.getGroupLabel()?h["f"](n.container,"has-group-label"):h["P"](n.container,"has-group-label"),t instanceof g){var o=t,s=n;o.showBorder()?(h["f"](s.container,"results-group-separator"),i.pickerGroupBorder&&(s.container.style.borderTopColor=i.pickerGroupBorder.toString())):(h["P"](s.container,"results-group-separator"),s.container.style.borderTopColor="");var a=o.getGroupLabel()||"";s.group&&(s.group.textContent=a,i.pickerGroupForeground&&(s.group.style.color=i.pickerGroupForeground.toString()))}if(t instanceof f){var u=t.getHighlights(),l=u[0],c=u[1],p=u[2],d=t.getIcon()?"quick-open-entry-icon "+t.getIcon():"";n.icon.className=d;var m=t.getLabelOptions()||Object.create(null);m.matches=l||[],m.title=t.getTooltip(),m.descriptionTitle=t.getDescriptionTooltip()||t.getDescription(),m.descriptionMatches=c||[],n.label.setLabel(t.getLabel()||"",t.getDescription(),m),n.detail.set(t.getDetail(),p),n.keybinding.set(t.getKeybinding())}},t.prototype.disposeTemplate=function(t,e){e.actionBar.dispose(),e.actionBar=null,e.container=null,e.entry=null,e.keybinding=null,e.detail=null,e.group=null,e.icon=null,e.label.dispose(),e.label=null},t}(),_=function(){function t(t,e){void 0===t&&(t=[]),void 0===e&&(e=new m),this._entries=t,this._dataSource=this,this._renderer=new b(e),this._filter=this,this._runner=this,this._accessibilityProvider=this}return Object.defineProperty(t.prototype,"entries",{get:function(){return this._entries},set:function(t){this._entries=t},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"dataSource",{get:function(){return this._dataSource},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"renderer",{get:function(){return this._renderer},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"filter",{get:function(){return this._filter},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"runner",{get:function(){return this._runner},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"accessibilityProvider",{get:function(){return this._accessibilityProvider},enumerable:!0,configurable:!0}),t.prototype.getId=function(t){return t.getId()},t.prototype.getLabel=function(t){return r["o"](t.getLabel())},t.prototype.getAriaLabel=function(t){var e=t.getAriaLabel();return e?i["a"]("quickOpenAriaLabelEntry","{0}, picker",t.getAriaLabel()):i["a"]("quickOpenAriaLabel","picker")},t.prototype.isVisible=function(t){return!t.isHidden()},t.prototype.run=function(t,e,n){return t.run(e,n)},t}()},"49d9":function(t,e,n){"use strict";function i(t,e,n){return Math.min(Math.max(t,e),n)}n.d(e,"a",(function(){return i}))},"4acc":function(t,e,n){"use strict";var i;n.d(e,"a",(function(){return r})),n.d(e,"b",(function(){return o})),function(t){var e={newline:/^\n+/,code:/^( {4}[^\n]+\n*)+/,fences:y,hr:/^ {0,3}((?:- *){3,}|(?:_ *){3,}|(?:\* *){3,})(?:\n+|$)/,heading:/^ *(#{1,6}) *([^\n]+?) *(?:#+ *)?(?:\n+|$)/,nptable:y,blockquote:/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/,list:/^( {0,3})(bull) [\s\S]+?(?:hr|def|\n{2,}(?! )(?!\1bull )\n*|\s*$)/,html:"^ {0,3}(?:<(script|pre|style)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?\\?>\\n*|<![A-Z][\\s\\S]*?>\\n*|<!\\[CDATA\\[[\\s\\S]*?\\]\\]>\\n*|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:\\n{2,}|$)|<(?!script|pre|style)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:\\n{2,}|$)|</(?!script|pre|style)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:\\n{2,}|$))",def:/^ {0,3}\[(label)\]: *\n? *<?([^\s>]+)>?(?:(?: +\n? *| *\n *)(title))? *(?:\n+|$)/,table:y,lheading:/^([^\n]+)\n *(=|-){2,} *(?:\n+|$)/,paragraph:/^([^\n]+(?:\n(?!hr|heading|lheading| {0,3}>|<\/?(?:tag)(?: +|\n|\/?>)|<(?:script|pre|style|!--))[^\n]+)*)/,text:/^[^\n]+/};function n(t){this.tokens=[],this.tokens.links=Object.create(null),this.options=t||x.defaults,this.rules=e.normal,this.options.pedantic?this.rules=e.pedantic:this.options.gfm&&(this.options.tables?this.rules=e.tables:this.rules=e.gfm)}e._label=/(?!\s*\])(?:\\[\[\]]|[^\[\]])+/,e._title=/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/,e.def=p(e.def).replace("label",e._label).replace("title",e._title).getRegex(),e.bullet=/(?:[*+-]|\d{1,9}\.)/,e.item=/^( *)(bull) ?[^\n]*(?:\n(?!\1bull ?)[^\n]*)*/,e.item=p(e.item,"gm").replace(/bull/g,e.bullet).getRegex(),e.list=p(e.list).replace(/bull/g,e.bullet).replace("hr","\\n+(?=\\1?(?:(?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$))").replace("def","\\n+(?="+e.def.source+")").getRegex(),e._tag="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",e._comment=/<!--(?!-?>)[\s\S]*?-->/,e.html=p(e.html,"i").replace("comment",e._comment).replace("tag",e._tag).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),e.paragraph=p(e.paragraph).replace("hr",e.hr).replace("heading",e.heading).replace("lheading",e.lheading).replace("tag",e._tag).getRegex(),e.blockquote=p(e.blockquote).replace("paragraph",e.paragraph).getRegex(),e.normal=v({},e),e.gfm=v({},e.normal,{fences:/^ {0,3}(`{3,}|~{3,})([^`\n]*)\n(?:|([\s\S]*?)\n)(?: {0,3}\1[~`]* *(?:\n+|$)|$)/,paragraph:/^/,heading:/^ *(#{1,6}) +([^\n]+?) *#* *(?:\n+|$)/}),e.gfm.paragraph=p(e.paragraph).replace("(?!","(?!"+e.gfm.fences.source.replace("\\1","\\2")+"|"+e.list.source.replace("\\1","\\3")+"|").getRegex(),e.tables=v({},e.gfm,{nptable:/^ *([^|\n ].*\|.*)\n *([-:]+ *\|[-| :]*)(?:\n((?:.*[^>\n ].*(?:\n|$))*)\n*|$)/,table:/^ *\|(.+)\n *\|?( *[-:]+[-| :]*)(?:\n((?: *[^>\n ].*(?:\n|$))*)\n*|$)/}),e.pedantic=v({},e.normal,{html:p("^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:\"[^\"]*\"|'[^']*'|\\s[^'\"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))").replace("comment",e._comment).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/}),n.rules=e,n.lex=function(t,e){var i=new n(e);return i.lex(t)},n.prototype.lex=function(t){return t=t.replace(/\r\n|\r/g,"\n").replace(/\t/g,"    ").replace(/\u00a0/g," ").replace(/\u2424/g,"\n"),this.token(t,!0)},n.prototype.token=function(t,n){var i,r,o,s,a,h,u,l,c,p,d,f,g,m,y,v;t=t.replace(/^ +$/gm,"");while(t)if((o=this.rules.newline.exec(t))&&(t=t.substring(o[0].length),o[0].length>1&&this.tokens.push({type:"space"})),o=this.rules.code.exec(t))t=t.substring(o[0].length),o=o[0].replace(/^ {4}/gm,""),this.tokens.push({type:"code",text:this.options.pedantic?o:_(o,"\n")});else if(o=this.rules.fences.exec(t))t=t.substring(o[0].length),this.tokens.push({type:"code",lang:o[2]?o[2].trim():o[2],text:o[3]||""});else if(o=this.rules.heading.exec(t))t=t.substring(o[0].length),this.tokens.push({type:"heading",depth:o[1].length,text:o[2]});else if((o=this.rules.nptable.exec(t))&&(h={type:"table",header:b(o[1].replace(/^ *| *\| *$/g,"")),align:o[2].replace(/^ *|\| *$/g,"").split(/ *\| */),cells:o[3]?o[3].replace(/\n$/,"").split("\n"):[]},h.header.length===h.align.length)){for(t=t.substring(o[0].length),d=0;d<h.align.length;d++)/^ *-+: *$/.test(h.align[d])?h.align[d]="right":/^ *:-+: *$/.test(h.align[d])?h.align[d]="center":/^ *:-+ *$/.test(h.align[d])?h.align[d]="left":h.align[d]=null;for(d=0;d<h.cells.length;d++)h.cells[d]=b(h.cells[d],h.header.length);this.tokens.push(h)}else if(o=this.rules.hr.exec(t))t=t.substring(o[0].length),this.tokens.push({type:"hr"});else if(o=this.rules.blockquote.exec(t))t=t.substring(o[0].length),this.tokens.push({type:"blockquote_start"}),o=o[0].replace(/^ *> ?/gm,""),this.token(o,n),this.tokens.push({type:"blockquote_end"});else if(o=this.rules.list.exec(t)){for(t=t.substring(o[0].length),s=o[2],m=s.length>1,u={type:"list_start",ordered:m,start:m?+s:"",loose:!1},this.tokens.push(u),o=o[0].match(this.rules.item),l=[],i=!1,g=o.length,d=0;d<g;d++)h=o[d],p=h.length,h=h.replace(/^ *([*+-]|\d+\.) */,""),~h.indexOf("\n ")&&(p-=h.length,h=this.options.pedantic?h.replace(/^ {1,4}/gm,""):h.replace(new RegExp("^ {1,"+p+"}","gm"),"")),d!==g-1&&(a=e.bullet.exec(o[d+1])[0],(s.length>1?1===a.length:a.length>1||this.options.smartLists&&a!==s)&&(t=o.slice(d+1).join("\n")+t,d=g-1)),r=i||/\n\n(?!\s*$)/.test(h),d!==g-1&&(i="\n"===h.charAt(h.length-1),r||(r=i)),r&&(u.loose=!0),y=/^\[[ xX]\] /.test(h),v=void 0,y&&(v=" "!==h[1],h=h.replace(/^\[[ xX]\] +/,"")),c={type:"list_item_start",task:y,checked:v,loose:r},l.push(c),this.tokens.push(c),this.token(h,!1),this.tokens.push({type:"list_item_end"});if(u.loose)for(g=l.length,d=0;d<g;d++)l[d].loose=!0;this.tokens.push({type:"list_end"})}else if(o=this.rules.html.exec(t))t=t.substring(o[0].length),this.tokens.push({type:this.options.sanitize?"paragraph":"html",pre:!this.options.sanitizer&&("pre"===o[1]||"script"===o[1]||"style"===o[1]),text:o[0]});else if(n&&(o=this.rules.def.exec(t)))t=t.substring(o[0].length),o[3]&&(o[3]=o[3].substring(1,o[3].length-1)),f=o[1].toLowerCase().replace(/\s+/g," "),this.tokens.links[f]||(this.tokens.links[f]={href:o[2],title:o[3]});else if((o=this.rules.table.exec(t))&&(h={type:"table",header:b(o[1].replace(/^ *| *\| *$/g,"")),align:o[2].replace(/^ *|\| *$/g,"").split(/ *\| */),cells:o[3]?o[3].replace(/\n$/,"").split("\n"):[]},h.header.length===h.align.length)){for(t=t.substring(o[0].length),d=0;d<h.align.length;d++)/^ *-+: *$/.test(h.align[d])?h.align[d]="right":/^ *:-+: *$/.test(h.align[d])?h.align[d]="center":/^ *:-+ *$/.test(h.align[d])?h.align[d]="left":h.align[d]=null;for(d=0;d<h.cells.length;d++)h.cells[d]=b(h.cells[d].replace(/^ *\| *| *\| *$/g,""),h.header.length);this.tokens.push(h)}else if(o=this.rules.lheading.exec(t))t=t.substring(o[0].length),this.tokens.push({type:"heading",depth:"="===o[2]?1:2,text:o[1]});else if(n&&(o=this.rules.paragraph.exec(t)))t=t.substring(o[0].length),this.tokens.push({type:"paragraph",text:"\n"===o[1].charAt(o[1].length-1)?o[1].slice(0,-1):o[1]});else if(o=this.rules.text.exec(t))t=t.substring(o[0].length),this.tokens.push({type:"text",text:o[0]});else if(t)throw new Error("Infinite loop on byte: "+t.charCodeAt(0));return this.tokens};var r={escape:/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,autolink:/^<(scheme:[^\s\x00-\x1f<>]*|email)>/,url:y,tag:"^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>",link:/^!?\[(label)\]\(href(?:\s+(title))?\s*\)/,reflink:/^!?\[(label)\]\[(?!\s*\])((?:\\[\[\]]?|[^\[\]\\])+)\]/,nolink:/^!?\[(?!\s*\])((?:\[[^\[\]]*\]|\\[\[\]]|[^\[\]])*)\](?:\[\])?/,strong:/^__([^\s_])__(?!_)|^\*\*([^\s*])\*\*(?!\*)|^__([^\s][\s\S]*?[^\s])__(?!_)|^\*\*([^\s][\s\S]*?[^\s])\*\*(?!\*)/,em:/^_([^\s_])_(?!_)|^\*([^\s*"<\[])\*(?!\*)|^_([^\s][\s\S]*?[^\s_])_(?!_|[^\spunctuation])|^_([^\s_][\s\S]*?[^\s])_(?!_|[^\spunctuation])|^\*([^\s"<\[][\s\S]*?[^\s*])\*(?!\*)|^\*([^\s*"<\[][\s\S]*?[^\s])\*(?!\*)/,code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,br:/^( {2,}|\\)\n(?!\s*$)/,del:y,text:/^(`+|[^`])(?:[\s\S]*?(?:(?=[\\<!\[`*]|\b_|$)|[^ ](?= {2,}\n))|(?= {2,}\n))/};function o(t,e){if(this.options=e||x.defaults,this.links=t,this.rules=r.normal,this.renderer=this.options.renderer||new s,this.renderer.options=this.options,!this.links)throw new Error("Tokens array requires a `links` property.");this.options.pedantic?this.rules=r.pedantic:this.options.gfm&&(this.options.breaks?this.rules=r.breaks:this.rules=r.gfm)}function s(t){this.options=t||x.defaults}function a(){}function h(t){this.tokens=[],this.token=null,this.options=t||x.defaults,this.options.renderer=this.options.renderer||new s,this.renderer=this.options.renderer,this.renderer.options=this.options,this.slugger=new u}function u(){this.seen={}}function l(t,e){if(e){if(l.escapeTest.test(t))return t.replace(l.escapeReplace,(function(t){return l.replacements[t]}))}else if(l.escapeTestNoEncode.test(t))return t.replace(l.escapeReplaceNoEncode,(function(t){return l.replacements[t]}));return t}function c(t){return t.replace(/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi,(function(t,e){return e=e.toLowerCase(),"colon"===e?":":"#"===e.charAt(0)?"x"===e.charAt(1)?String.fromCharCode(parseInt(e.substring(2),16)):String.fromCharCode(+e.substring(1)):""}))}function p(t,e){return t=t.source||t,e=e||"",{replace:function(e,n){return n=n.source||n,n=n.replace(/(^|[^\[])\^/g,"$1"),t=t.replace(e,n),this},getRegex:function(){return new RegExp(t,e)}}}function d(t,e,n){if(t){try{var i=decodeURIComponent(c(n)).replace(/[^\w:]/g,"").toLowerCase()}catch(r){return null}if(0===i.indexOf("javascript:")||0===i.indexOf("vbscript:")||0===i.indexOf("data:"))return null}e&&!m.test(n)&&(n=f(e,n));try{n=encodeURI(n).replace(/%25/g,"%")}catch(r){return null}return n}function f(t,e){return g[" "+t]||(/^[^:]+:\/*[^/]*$/.test(t)?g[" "+t]=t+"/":g[" "+t]=_(t,"/",!0)),t=g[" "+t],"//"===e.slice(0,2)?t.replace(/:[\s\S]*/,":")+e:"/"===e.charAt(0)?t.replace(/(:\/*[^/]*)[\s\S]*/,"$1")+e:t+e}r._punctuation="!\"#$%&'()*+,\\-./:;<=>?@\\[^_{|}~",r.em=p(r.em).replace(/punctuation/g,r._punctuation).getRegex(),r._escapes=/\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/g,r._scheme=/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/,r._email=/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/,r.autolink=p(r.autolink).replace("scheme",r._scheme).replace("email",r._email).getRegex(),r._attribute=/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/,r.tag=p(r.tag).replace("comment",e._comment).replace("attribute",r._attribute).getRegex(),r._label=/(?:\[[^\[\]]*\]|\\[\[\]]?|`[^`]*`|`(?!`)|[^\[\]\\`])*?/,r._href=/\s*(<(?:\\[<>]?|[^\s<>\\])*>|[^\s\x00-\x1f]*)/,r._title=/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/,r.link=p(r.link).replace("label",r._label).replace("href",r._href).replace("title",r._title).getRegex(),r.reflink=p(r.reflink).replace("label",r._label).getRegex(),r.normal=v({},r),r.pedantic=v({},r.normal,{strong:/^__(?=\S)([\s\S]*?\S)__(?!_)|^\*\*(?=\S)([\s\S]*?\S)\*\*(?!\*)/,em:/^_(?=\S)([\s\S]*?\S)_(?!_)|^\*(?=\S)([\s\S]*?\S)\*(?!\*)/,link:p(/^!?\[(label)\]\((.*?)\)/).replace("label",r._label).getRegex(),reflink:p(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",r._label).getRegex()}),r.gfm=v({},r.normal,{escape:p(r.escape).replace("])","~|])").getRegex(),_extended_email:/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/,url:/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,_backpedal:/(?:[^?!.,:;*_~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_~)]+(?!$))+/,del:/^~+(?=\S)([\s\S]*?\S)~+/,text:/^(`+|[^`])(?:[\s\S]*?(?:(?=[\\<!\[`*~]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@))|(?= {2,}\n|[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@))/}),r.gfm.url=p(r.gfm.url,"i").replace("email",r.gfm._extended_email).getRegex(),r.breaks=v({},r.gfm,{br:p(r.br).replace("{2,}","*").getRegex(),text:p(r.gfm.text).replace(/\{2,\}/g,"*").getRegex()}),o.rules=r,o.output=function(t,e,n){var i=new o(e,n);return i.output(t)},o.prototype.output=function(t){var e,n,i,r,s,a,h="";while(t)if(s=this.rules.escape.exec(t))t=t.substring(s[0].length),h+=l(s[1]);else if(s=this.rules.tag.exec(t))!this.inLink&&/^<a /i.test(s[0])?this.inLink=!0:this.inLink&&/^<\/a>/i.test(s[0])&&(this.inLink=!1),!this.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(s[0])?this.inRawBlock=!0:this.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(s[0])&&(this.inRawBlock=!1),t=t.substring(s[0].length),h+=this.options.sanitize?this.options.sanitizer?this.options.sanitizer(s[0]):l(s[0]):s[0];else if(s=this.rules.link.exec(t)){var u=w(s[2],"()");if(u>-1){var c=s[0].length-(s[2].length-u)-(s[3]||"").length;s[2]=s[2].substring(0,u),s[0]=s[0].substring(0,c).trim(),s[3]=""}t=t.substring(s[0].length),this.inLink=!0,i=s[2],this.options.pedantic?(e=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(i),e?(i=e[1],r=e[3]):r=""):r=s[3]?s[3].slice(1,-1):"",i=i.trim().replace(/^<([\s\S]*)>$/,"$1"),h+=this.outputLink(s,{href:o.escapes(i),title:o.escapes(r)}),this.inLink=!1}else if((s=this.rules.reflink.exec(t))||(s=this.rules.nolink.exec(t))){if(t=t.substring(s[0].length),e=(s[2]||s[1]).replace(/\s+/g," "),e=this.links[e.toLowerCase()],!e||!e.href){h+=s[0].charAt(0),t=s[0].substring(1)+t;continue}this.inLink=!0,h+=this.outputLink(s,e),this.inLink=!1}else if(s=this.rules.strong.exec(t))t=t.substring(s[0].length),h+=this.renderer.strong(this.output(s[4]||s[3]||s[2]||s[1]));else if(s=this.rules.em.exec(t))t=t.substring(s[0].length),h+=this.renderer.em(this.output(s[6]||s[5]||s[4]||s[3]||s[2]||s[1]));else if(s=this.rules.code.exec(t))t=t.substring(s[0].length),h+=this.renderer.codespan(l(s[2].trim(),!0));else if(s=this.rules.br.exec(t))t=t.substring(s[0].length),h+=this.renderer.br();else if(s=this.rules.del.exec(t))t=t.substring(s[0].length),h+=this.renderer.del(this.output(s[1]));else if(s=this.rules.autolink.exec(t))t=t.substring(s[0].length),"@"===s[2]?(n=l(this.mangle(s[1])),i="mailto:"+n):(n=l(s[1]),i=n),h+=this.renderer.link(i,null,n);else if(this.inLink||!(s=this.rules.url.exec(t))){if(s=this.rules.text.exec(t))t=t.substring(s[0].length),this.inRawBlock?h+=this.renderer.text(s[0]):h+=this.renderer.text(l(this.smartypants(s[0])));else if(t)throw new Error("Infinite loop on byte: "+t.charCodeAt(0))}else{if("@"===s[2])n=l(s[0]),i="mailto:"+n;else{do{a=s[0],s[0]=this.rules._backpedal.exec(s[0])[0]}while(a!==s[0]);n=l(s[0]),i="www."===s[1]?"http://"+n:n}t=t.substring(s[0].length),h+=this.renderer.link(i,null,n)}return h},o.escapes=function(t){return t?t.replace(o.rules._escapes,"$1"):t},o.prototype.outputLink=function(t,e){var n=e.href,i=e.title?l(e.title):null;return"!"!==t[0].charAt(0)?this.renderer.link(n,i,this.output(t[1])):this.renderer.image(n,i,l(t[1]))},o.prototype.smartypants=function(t){return this.options.smartypants?t.replace(/---/g,"—").replace(/--/g,"–").replace(/(^|[-\u2014/(\[{"\s])'/g,"$1‘").replace(/'/g,"’").replace(/(^|[-\u2014/(\[{\u2018\s])"/g,"$1“").replace(/"/g,"”").replace(/\.{3}/g,"…"):t},o.prototype.mangle=function(t){if(!this.options.mangle)return t;for(var e,n="",i=t.length,r=0;r<i;r++)e=t.charCodeAt(r),Math.random()>.5&&(e="x"+e.toString(16)),n+="&#"+e+";";return n},s.prototype.code=function(t,e,n){var i=(e||"").match(/\S*/)[0];if(this.options.highlight){var r=this.options.highlight(t,i);null!=r&&r!==t&&(n=!0,t=r)}return i?'<pre><code class="'+this.options.langPrefix+l(i,!0)+'">'+(n?t:l(t,!0))+"</code></pre>\n":"<pre><code>"+(n?t:l(t,!0))+"</code></pre>"},s.prototype.blockquote=function(t){return"<blockquote>\n"+t+"</blockquote>\n"},s.prototype.html=function(t){return t},s.prototype.heading=function(t,e,n,i){return this.options.headerIds?"<h"+e+' id="'+this.options.headerPrefix+i.slug(n)+'">'+t+"</h"+e+">\n":"<h"+e+">"+t+"</h"+e+">\n"},s.prototype.hr=function(){return this.options.xhtml?"<hr/>\n":"<hr>\n"},s.prototype.list=function(t,e,n){var i=e?"ol":"ul",r=e&&1!==n?' start="'+n+'"':"";return"<"+i+r+">\n"+t+"</"+i+">\n"},s.prototype.listitem=function(t){return"<li>"+t+"</li>\n"},s.prototype.checkbox=function(t){return"<input "+(t?'checked="" ':"")+'disabled="" type="checkbox"'+(this.options.xhtml?" /":"")+"> "},s.prototype.paragraph=function(t){return"<p>"+t+"</p>\n"},s.prototype.table=function(t,e){return e&&(e="<tbody>"+e+"</tbody>"),"<table>\n<thead>\n"+t+"</thead>\n"+e+"</table>\n"},s.prototype.tablerow=function(t){return"<tr>\n"+t+"</tr>\n"},s.prototype.tablecell=function(t,e){var n=e.header?"th":"td",i=e.align?"<"+n+' align="'+e.align+'">':"<"+n+">";return i+t+"</"+n+">\n"},s.prototype.strong=function(t){return"<strong>"+t+"</strong>"},s.prototype.em=function(t){return"<em>"+t+"</em>"},s.prototype.codespan=function(t){return"<code>"+t+"</code>"},s.prototype.br=function(){return this.options.xhtml?"<br/>":"<br>"},s.prototype.del=function(t){return"<del>"+t+"</del>"},s.prototype.link=function(t,e,n){if(t=d(this.options.sanitize,this.options.baseUrl,t),null===t)return n;var i='<a href="'+l(t)+'"';return e&&(i+=' title="'+e+'"'),i+=">"+n+"</a>",i},s.prototype.image=function(t,e,n){if(t=d(this.options.sanitize,this.options.baseUrl,t),null===t)return n;var i='<img src="'+t+'" alt="'+n+'"';return e&&(i+=' title="'+e+'"'),i+=this.options.xhtml?"/>":">",i},s.prototype.text=function(t){return t},a.prototype.strong=a.prototype.em=a.prototype.codespan=a.prototype.del=a.prototype.text=function(t){return t},a.prototype.link=a.prototype.image=function(t,e,n){return""+n},a.prototype.br=function(){return""},h.parse=function(t,e){var n=new h(e);return n.parse(t)},h.prototype.parse=function(t){this.inline=new o(t.links,this.options),this.inlineText=new o(t.links,v({},this.options,{renderer:new a})),this.tokens=t.reverse();var e="";while(this.next())e+=this.tok();return e},h.prototype.next=function(){return this.token=this.tokens.pop()},h.prototype.peek=function(){return this.tokens[this.tokens.length-1]||0},h.prototype.parseText=function(){var t=this.token.text;while("text"===this.peek().type)t+="\n"+this.next().text;return this.inline.output(t)},h.prototype.tok=function(){switch(this.token.type){case"space":return"";case"hr":return this.renderer.hr();case"heading":return this.renderer.heading(this.inline.output(this.token.text),this.token.depth,c(this.inlineText.output(this.token.text)),this.slugger);case"code":return this.renderer.code(this.token.text,this.token.lang,this.token.escaped);case"table":var t,e,n,i,r="",o="";for(n="",t=0;t<this.token.header.length;t++)n+=this.renderer.tablecell(this.inline.output(this.token.header[t]),{header:!0,align:this.token.align[t]});for(r+=this.renderer.tablerow(n),t=0;t<this.token.cells.length;t++){for(e=this.token.cells[t],n="",i=0;i<e.length;i++)n+=this.renderer.tablecell(this.inline.output(e[i]),{header:!1,align:this.token.align[i]});o+=this.renderer.tablerow(n)}return this.renderer.table(r,o);case"blockquote_start":o="";while("blockquote_end"!==this.next().type)o+=this.tok();return this.renderer.blockquote(o);case"list_start":o="";var s=this.token.ordered,a=this.token.start;while("list_end"!==this.next().type)o+=this.tok();return this.renderer.list(o,s,a);case"list_item_start":o="";var h=this.token.loose,u=this.token.checked,l=this.token.task;this.token.task&&(o+=this.renderer.checkbox(u));while("list_item_end"!==this.next().type)o+=h||"text"!==this.token.type?this.tok():this.parseText();return this.renderer.listitem(o,l,u);case"html":return this.renderer.html(this.token.text);case"paragraph":return this.renderer.paragraph(this.inline.output(this.token.text));case"text":return this.renderer.paragraph(this.parseText());default:var p='Token with "'+this.token.type+'" type was not found.';if(!this.options.silent)throw new Error(p);console.log(p)}},u.prototype.slug=function(t){var e=t.toLowerCase().trim().replace(/[\u2000-\u206F\u2E00-\u2E7F\\'!"#$%&()*+,./:;<=>?@[\]^`{|}~]/g,"").replace(/\s/g,"-");if(this.seen.hasOwnProperty(e)){var n=e;do{this.seen[n]++,e=n+"-"+this.seen[n]}while(this.seen.hasOwnProperty(e))}return this.seen[e]=0,e},l.escapeTest=/[&<>"']/,l.escapeReplace=/[&<>"']/g,l.replacements={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},l.escapeTestNoEncode=/[<>"']|&(?!#?\w+;)/,l.escapeReplaceNoEncode=/[<>"']|&(?!#?\w+;)/g;var g={},m=/^$|^[a-z][a-z0-9+.-]*:|^[?#]/i;function y(){}function v(t){for(var e,n,i=1;i<arguments.length;i++)for(n in e=arguments[i],e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}function b(t,e){var n=t.replace(/\|/g,(function(t,e,n){var i=!1,r=e;while(--r>=0&&"\\"===n[r])i=!i;return i?"|":" |"})),i=n.split(/ \|/),r=0;if(i.length>e)i.splice(e);else while(i.length<e)i.push("");for(;r<i.length;r++)i[r]=i[r].trim().replace(/\\\|/g,"|");return i}function _(t,e,n){if(0===t.length)return"";var i=0;while(i<t.length){var r=t.charAt(t.length-i-1);if(r!==e||n){if(r===e||!n)break;i++}else i++}return t.substr(0,t.length-i)}function w(t,e){if(-1===t.indexOf(e[1]))return-1;for(var n=0,i=0;i<t.length;i++)if("\\"===t[i])i++;else if(t[i]===e[0])n++;else if(t[i]===e[1]&&(n--,n<0))return i;return-1}function x(t,e,i){if("undefined"===typeof t||null===t)throw new Error("marked(): input parameter is undefined or null");if("string"!==typeof t)throw new Error("marked(): input parameter is of type "+Object.prototype.toString.call(t)+", string expected");if(i||"function"===typeof e){i||(i=e,e=null),e=v({},x.defaults,e||{});var r,o,s=e.highlight,a=0;try{r=n.lex(t,e)}catch(c){return i(c)}o=r.length;var u=function(t){if(t)return e.highlight=s,i(t);var n;try{n=h.parse(r,e)}catch(c){t=c}return e.highlight=s,t?i(t):i(null,n)};if(!s||s.length<3)return u();if(delete e.highlight,!o)return u();for(;a<r.length;a++)(function(t){"code"!==t.type?--o||u():s(t.text,t.lang,(function(e,n){return e?u(e):null==n||n===t.text?--o||u():(t.text=n,t.escaped=!0,void(--o||u()))}))})(r[a])}else try{return e&&(e=v({},x.defaults,e)),h.parse(n.lex(t,e),e)}catch(c){if(c.message+="\nPlease report this to https://github.com/markedjs/marked.",(e||x.defaults).silent)return"<p>An error occurred:</p><pre>"+l(c.message+"",!0)+"</pre>";throw c}}y.exec=y,x.options=x.setOptions=function(t){return v(x.defaults,t),x},x.getDefaults=function(){return{baseUrl:null,breaks:!1,gfm:!0,headerIds:!0,headerPrefix:"",highlight:null,langPrefix:"language-",mangle:!0,pedantic:!1,renderer:new s,sanitize:!1,sanitizer:null,silent:!1,smartLists:!1,smartypants:!1,tables:!0,xhtml:!1}},x.defaults=x.getDefaults(),x.Parser=h,x.parser=h.parse,x.Renderer=s,x.TextRenderer=a,x.Lexer=n,x.lexer=n.lex,x.InlineLexer=o,x.inlineLexer=o.output,x.Slugger=u,x.parse=x,i=x}.call(void 0);i.Parser,i.parser;var r=i.Renderer,o=(i.TextRenderer,i.Lexer,i.lexer,i.InlineLexer,i.inlineLexer,i.parse)},"4b76":function(t,e,n){"use strict";var i,r=n("dff7"),o=n("3742");(function(t){t[t["Ignore"]=0]="Ignore",t[t["Info"]=1]="Info",t[t["Warning"]=2]="Warning",t[t["Error"]=3]="Error"})(i||(i={})),function(t){var e="error",n="warning",i="warn",s="info",a=Object.create(null);function h(r){return r?o["n"](e,r)?t.Error:o["n"](n,r)||o["n"](i,r)?t.Warning:o["n"](s,r)?t.Info:t.Ignore:t.Ignore}a[t.Error]=r["a"]("sev.error","Error"),a[t.Warning]=r["a"]("sev.warning","Warning"),a[t.Info]=r["a"]("sev.info","Info"),t.fromValue=h}(i||(i={})),e["a"]=i},"52c8":function(t,e,n){},"6d8e":function(t,e,n){"use strict";n.d(e,"a",(function(){return g}));var i,r=n("30db"),o=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},t(e,n)};return function(e,n){function i(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),s=/^\w[\w\d+.-]*$/,a=/^\//,h=/^\/\//;function u(t,e){if(!t.scheme&&e)throw new Error('[UriError]: Scheme is missing: {scheme: "", authority: "'+t.authority+'", path: "'+t.path+'", query: "'+t.query+'", fragment: "'+t.fragment+'"}');if(t.scheme&&!s.test(t.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(t.path)if(t.authority){if(!a.test(t.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(h.test(t.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}function l(t,e){return t||e?t:"file"}function c(t,e){switch(t){case"https":case"http":case"file":e?e[0]!==d&&(e=d+e):e=d;break}return e}var p="",d="/",f=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/,g=function(){function t(t,e,n,i,r,o){void 0===o&&(o=!1),"object"===typeof t?(this.scheme=t.scheme||p,this.authority=t.authority||p,this.path=t.path||p,this.query=t.query||p,this.fragment=t.fragment||p):(this.scheme=l(t,o),this.authority=e||p,this.path=c(this.scheme,n||p),this.query=i||p,this.fragment=r||p,u(this,o))}return t.isUri=function(e){return e instanceof t||!!e&&("string"===typeof e.authority&&"string"===typeof e.fragment&&"string"===typeof e.path&&"string"===typeof e.query&&"string"===typeof e.scheme&&"function"===typeof e.fsPath&&"function"===typeof e.with&&"function"===typeof e.toString)},Object.defineProperty(t.prototype,"fsPath",{get:function(){return w(this)},enumerable:!0,configurable:!0}),t.prototype.with=function(t){if(!t)return this;var e=t.scheme,n=t.authority,i=t.path,r=t.query,o=t.fragment;return void 0===e?e=this.scheme:null===e&&(e=p),void 0===n?n=this.authority:null===n&&(n=p),void 0===i?i=this.path:null===i&&(i=p),void 0===r?r=this.query:null===r&&(r=p),void 0===o?o=this.fragment:null===o&&(o=p),e===this.scheme&&n===this.authority&&i===this.path&&r===this.query&&o===this.fragment?this:new y(e,n,i,r,o)},t.parse=function(t,e){void 0===e&&(e=!1);var n=f.exec(t);return n?new y(n[2]||p,k(n[4]||p),k(n[5]||p),k(n[7]||p),k(n[9]||p),e):new y(p,p,p,p,p)},t.file=function(t){var e=p;if(r["h"]&&(t=t.replace(/\\/g,d)),t[0]===d&&t[1]===d){var n=t.indexOf(d,2);-1===n?(e=t.substring(2),t=d):(e=t.substring(2,n),t=t.substring(n)||d)}return new y("file",e,t,p,p)},t.from=function(t){return new y(t.scheme,t.authority,t.path,t.query,t.fragment)},t.prototype.toString=function(t){return void 0===t&&(t=!1),x(this,t)},t.prototype.toJSON=function(){return this},t.revive=function(e){if(e){if(e instanceof t)return e;var n=new y(e);return n._formatted=e.external,n._fsPath=e._sep===m?e.fsPath:null,n}return e},t}(),m=r["h"]?1:void 0,y=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e._formatted=null,e._fsPath=null,e}return o(e,t),Object.defineProperty(e.prototype,"fsPath",{get:function(){return this._fsPath||(this._fsPath=w(this)),this._fsPath},enumerable:!0,configurable:!0}),e.prototype.toString=function(t){return void 0===t&&(t=!1),t?x(this,!0):(this._formatted||(this._formatted=x(this,!1)),this._formatted)},e.prototype.toJSON=function(){var t={$mid:1};return this._fsPath&&(t.fsPath=this._fsPath,t._sep=m),this._formatted&&(t.external=this._formatted),this.path&&(t.path=this.path),this.scheme&&(t.scheme=this.scheme),this.authority&&(t.authority=this.authority),this.query&&(t.query=this.query),this.fragment&&(t.fragment=this.fragment),t},e}(g),v=(i={},i[58]="%3A",i[47]="%2F",i[63]="%3F",i[35]="%23",i[91]="%5B",i[93]="%5D",i[64]="%40",i[33]="%21",i[36]="%24",i[38]="%26",i[39]="%27",i[40]="%28",i[41]="%29",i[42]="%2A",i[43]="%2B",i[44]="%2C",i[59]="%3B",i[61]="%3D",i[32]="%20",i);function b(t,e){for(var n=void 0,i=-1,r=0;r<t.length;r++){var o=t.charCodeAt(r);if(o>=97&&o<=122||o>=65&&o<=90||o>=48&&o<=57||45===o||46===o||95===o||126===o||e&&47===o)-1!==i&&(n+=encodeURIComponent(t.substring(i,r)),i=-1),void 0!==n&&(n+=t.charAt(r));else{void 0===n&&(n=t.substr(0,r));var s=v[o];void 0!==s?(-1!==i&&(n+=encodeURIComponent(t.substring(i,r)),i=-1),n+=s):-1===i&&(i=r)}}return-1!==i&&(n+=encodeURIComponent(t.substring(i))),void 0!==n?n:t}function _(t){for(var e=void 0,n=0;n<t.length;n++){var i=t.charCodeAt(n);35===i||63===i?(void 0===e&&(e=t.substr(0,n)),e+=v[i]):void 0!==e&&(e+=t[n])}return void 0!==e?e:t}function w(t){var e;return e=t.authority&&t.path.length>1&&"file"===t.scheme?"//"+t.authority+t.path:47===t.path.charCodeAt(0)&&(t.path.charCodeAt(1)>=65&&t.path.charCodeAt(1)<=90||t.path.charCodeAt(1)>=97&&t.path.charCodeAt(1)<=122)&&58===t.path.charCodeAt(2)?t.path[1].toLowerCase()+t.path.substr(2):t.path,r["h"]&&(e=e.replace(/\//g,"\\")),e}function x(t,e){var n=e?_:b,i="",r=t.scheme,o=t.authority,s=t.path,a=t.query,h=t.fragment;if(r&&(i+=r,i+=":"),(o||"file"===r)&&(i+=d,i+=d),o){var u=o.indexOf("@");if(-1!==u){var l=o.substr(0,u);o=o.substr(u+1),u=l.indexOf(":"),-1===u?i+=n(l,!1):(i+=n(l.substr(0,u),!1),i+=":",i+=n(l.substr(u+1),!1)),i+="@"}o=o.toLowerCase(),u=o.indexOf(":"),-1===u?i+=n(o,!1):(i+=n(o.substr(0,u),!1),i+=o.substr(u))}if(s){if(s.length>=3&&47===s.charCodeAt(0)&&58===s.charCodeAt(2)){var c=s.charCodeAt(1);c>=65&&c<=90&&(s="/"+String.fromCharCode(c+32)+":"+s.substr(3))}else if(s.length>=2&&58===s.charCodeAt(1)){c=s.charCodeAt(0);c>=65&&c<=90&&(s=String.fromCharCode(c+32)+":"+s.substr(2))}i+=n(s,!0)}return a&&(i+="?",i+=n(a,!1)),h&&(i+="#",i+=e?h:b(h,!1)),i}function D(t){try{return decodeURIComponent(t)}catch(i){return t.length>3?t.substr(0,3)+D(t.substr(3)):t}}var C=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function k(t){return t.match(C)?t.replace(C,(function(t){return D(t)})):t}},7257:function(t,e,n){"use strict";n.r(e);var i=n("fa12");Object(i["a"])({id:"javascript",extensions:[".js",".es6",".jsx"],firstLine:"^#!.*\\bnode",filenames:["jakefile"],aliases:["JavaScript","javascript","js"],mimetypes:["text/javascript"],loader:function(){return n.e("chunk-2d2308b7").then(n.bind(null,"ed79"))}})},"73d3":function(t,e,n){"use strict";n.r(e);var i=n("fa12");Object(i["a"])({id:"scss",extensions:[".scss"],aliases:["Sass","sass","scss"],mimetypes:["text/x-scss","text/scss"],loader:function(){return n.e("chunk-2d0bff92").then(n.bind(null,"4099"))}})},"82c9":function(t,e,n){"use strict";n.d(e,"c",(function(){return p})),n.d(e,"e",(function(){return f})),n.d(e,"b",(function(){return g})),n.d(e,"d",(function(){return m})),n.d(e,"f",(function(){return y})),n.d(e,"g",(function(){return v})),n.d(e,"h",(function(){return _})),n.d(e,"a",(function(){return i}));var i,r=n("3d37"),o=n("32b8"),s=n("6d8e"),a=n("3742"),h=n("b589"),u=n("30db"),l=function(){for(var t=0,e=0,n=arguments.length;e<n;e++)t+=arguments[e].length;var i=Array(t),r=0;for(e=0;e<n;e++)for(var o=arguments[e],s=0,a=o.length;s<a;s++,r++)i[r]=o[s];return i};function c(t){return!t||t.scheme!==h["b"].file||!u["d"]}function p(t){return g(t)||t.authority}function d(t,e){return t===e||Object(a["n"])(t,e)}function f(t,e,n){if(void 0===n&&(n=c(t)),t===e)return!0;if(!t||!e)return!1;if(t.scheme!==e.scheme||!d(t.authority,e.authority))return!1;var i=t.path||"/",r=e.path||"/";return i===r||n&&Object(a["n"])(i||"/",r||"/")}function g(t){return o["posix"].basename(t.path)}function m(t){if(0===t.path.length)return t;if(t.scheme===h["b"].file)return s["a"].file(o["dirname"](b(t)));var e=o["posix"].dirname(t.path);return t.authority&&e.length&&47!==e.charCodeAt(0)&&(console.error('dirname("'+t.toString+")) resulted in a relative path"),e="/"),t.with({path:e})}function y(t){for(var e,n,i=[],r=1;r<arguments.length;r++)i[r-1]=arguments[r];return n=t.scheme===h["b"].file?s["a"].file(o["join"].apply(o,l([b(t)],i))).path:(e=o["posix"]).join.apply(e,l([t.path||"/"],i)),t.with({path:n})}function v(t){return t.path.length?(e=t.scheme===h["b"].file?s["a"].file(o["normalize"](b(t))).path:o["posix"].normalize(t.path),t.with({path:e})):t;var e}function b(t){var e,n=t.path;return e=t.authority&&n.length>1&&t.scheme===h["b"].file?"//"+t.authority+n:u["h"]&&47===n.charCodeAt(0)&&r["b"](n.charCodeAt(1))&&58===n.charCodeAt(2)?n.substr(1):n,u["h"]&&(e=e.replace(/\//g,"\\")),e}function _(t,e,n){if(void 0===n&&(n=c(t)),t.scheme===e.scheme&&d(t.authority,e.authority)){if(t.scheme===h["b"].file){var i=o["relative"](t.path,e.path);return u["h"]?r["c"](i):i}var s=t.path||"/",a=e.path||"/";if(n){for(var l=0,p=Math.min(s.length,a.length);l<p;l++)if(s.charCodeAt(l)!==a.charCodeAt(l)&&s.charAt(l).toLowerCase()!==a.charAt(l).toLowerCase())break;s=a.substr(0,l)+s.substr(l)}return o["posix"].relative(s,a)}}(function(t){function e(e){var n=new Map,i=e.path.substring(e.path.indexOf(";")+1,e.path.lastIndexOf(";"));i.split(";").forEach((function(t){var e=t.split(":"),i=e[0],r=e[1];i&&r&&n.set(i,r)}));var r=e.path.substring(0,e.path.indexOf(";"));return r&&n.set(t.META_DATA_MIME,r),n}t.META_DATA_LABEL="label",t.META_DATA_DESCRIPTION="description",t.META_DATA_SIZE="size",t.META_DATA_MIME="mime",t.parseMetaData=e})(i||(i={}))},8457:function(t,e,n){"use strict";n.r(e);var i=n("fa12");Object(i["a"])({id:"html",extensions:[".html",".htm",".shtml",".xhtml",".mdoc",".jsp",".asp",".aspx",".jshtm"],aliases:["HTML","htm","html","xhtml"],mimetypes:["text/html","text/x-jshtm","text/template","text/ng-template"],loader:function(){return n.e("chunk-2d20ff23").then(n.bind(null,"b692"))}})},"86d7":function(t,e,n){"use strict";n.d(e,"a",(function(){return pt}));n("52c8");var i=n("dff7"),r=n("30db"),o=n("ef8e"),s=function(){function t(t){this.modelProvider=Object(o["g"])(t.getModel)?t:{getModel:function(){return t}}}return t.prototype.getId=function(t,e){if(!e)return null;var n=this.modelProvider.getModel();return n===e?"__root__":n.dataSource.getId(e)},t.prototype.hasChildren=function(t,e){var n=this.modelProvider.getModel();return!!(n&&n===e&&n.entries.length>0)},t.prototype.getChildren=function(t,e){var n=this.modelProvider.getModel();return Promise.resolve(n===e?n.entries:[])},t.prototype.getParent=function(t,e){return Promise.resolve(null)},t}(),a=function(){function t(t){this.modelProvider=t}return t.prototype.getAriaLabel=function(t,e){var n=this.modelProvider.getModel();return n.accessibilityProvider?n.accessibilityProvider.getAriaLabel(e):null},t.prototype.getPosInSet=function(t,e){var n=this.modelProvider.getModel(),i=0;if(n.filter)for(var r=0,o=n.entries;r<o.length;r++){var s=o[r];if(n.filter.isVisible(s)&&i++,s===e)break}else i=n.entries.indexOf(e)+1;return String(i)},t.prototype.getSetSize=function(){var t=this.modelProvider.getModel(),e=0;if(t.filter)for(var n=0,i=t.entries;n<i.length;n++){var r=i[n];t.filter.isVisible(r)&&e++}else e=t.entries.length;return String(e)},t}(),h=function(){function t(t){this.modelProvider=t}return t.prototype.isVisible=function(t,e){var n=this.modelProvider.getModel();return!n.filter||n.filter.isVisible(e)},t}(),u=function(){function t(t,e){this.modelProvider=t,this.styles=e}return t.prototype.updateStyles=function(t){this.styles=t},t.prototype.getHeight=function(t,e){var n=this.modelProvider.getModel();return n.renderer.getHeight(e)},t.prototype.getTemplateId=function(t,e){var n=this.modelProvider.getModel();return n.renderer.getTemplateId(e)},t.prototype.renderTemplate=function(t,e,n){var i=this.modelProvider.getModel();return i.renderer.renderTemplate(e,n,this.styles)},t.prototype.renderElement=function(t,e,n,i){var r=this.modelProvider.getModel();r.renderer.renderElement(e,n,i,this.styles)},t.prototype.disposeTemplate=function(t,e,n){var i=this.modelProvider.getModel();i.renderer.disposeTemplate(e,n)},t}(),l=n("d3ef"),c=(n("bcc1"),n("fdcc")),p=n("11f7"),d=n("fe45"),f=function(){function t(){this._arr=[]}return t.prototype.set=function(t,e){this._arr.push({keybinding:Object(d["f"])(t,r["a"]),callback:e})},t.prototype.dispatch=function(t){for(var e=this._arr.length-1;e>=0;e--){var n=this._arr[e];if(t.toChord().equals(n.keybinding))return n.callback}return null},t}(),g=function(){function t(t){var e=this;void 0===t&&(t={clickBehavior:0,keyboardSupport:!0,openMode:0}),this.options=t,this.downKeyBindingDispatcher=new f,this.upKeyBindingDispatcher=new f,("boolean"!==typeof t.keyboardSupport||t.keyboardSupport)&&(this.downKeyBindingDispatcher.set(16,(function(t,n){return e.onUp(t,n)})),this.downKeyBindingDispatcher.set(18,(function(t,n){return e.onDown(t,n)})),this.downKeyBindingDispatcher.set(15,(function(t,n){return e.onLeft(t,n)})),this.downKeyBindingDispatcher.set(17,(function(t,n){return e.onRight(t,n)})),r["e"]&&(this.downKeyBindingDispatcher.set(2064,(function(t,n){return e.onLeft(t,n)})),this.downKeyBindingDispatcher.set(300,(function(t,n){return e.onDown(t,n)})),this.downKeyBindingDispatcher.set(302,(function(t,n){return e.onUp(t,n)}))),this.downKeyBindingDispatcher.set(11,(function(t,n){return e.onPageUp(t,n)})),this.downKeyBindingDispatcher.set(12,(function(t,n){return e.onPageDown(t,n)})),this.downKeyBindingDispatcher.set(14,(function(t,n){return e.onHome(t,n)})),this.downKeyBindingDispatcher.set(13,(function(t,n){return e.onEnd(t,n)})),this.downKeyBindingDispatcher.set(10,(function(t,n){return e.onSpace(t,n)})),this.downKeyBindingDispatcher.set(9,(function(t,n){return e.onEscape(t,n)})),this.upKeyBindingDispatcher.set(3,this.onEnter.bind(this)),this.upKeyBindingDispatcher.set(2051,this.onEnter.bind(this)))}return t.prototype.onMouseDown=function(t,e,n,i){if(void 0===i&&(i="mouse"),0===this.options.clickBehavior&&(n.leftButton||n.middleButton)){if(n.target){if(n.target.tagName&&"input"===n.target.tagName.toLowerCase())return!1;if(p["x"](n.target,"scrollbar","monaco-tree"))return!1;if(p["x"](n.target,"monaco-action-bar","row"))return!1}return this.onLeftClick(t,e,n,i)}return!1},t.prototype.onClick=function(t,e,n){var i=r["e"];return i&&n.ctrlKey?(n.preventDefault(),n.stopPropagation(),!1):(!n.target||!n.target.tagName||"input"!==n.target.tagName.toLowerCase())&&((0!==this.options.clickBehavior||!n.leftButton&&!n.middleButton)&&this.onLeftClick(t,e,n))},t.prototype.onLeftClick=function(t,e,n,i){void 0===i&&(i="mouse");var r=n,o={origin:i,originalEvent:n,didClickOnTwistie:this.isClickOnTwistie(r)};if(t.getInput()===e)t.clearFocus(o),t.clearSelection(o);else{var s=n&&r.browserEvent&&"mousedown"===r.browserEvent.type&&1===r.browserEvent.detail;s||n.preventDefault(),n.stopPropagation(),t.domFocus(),t.setSelection([e],o),t.setFocus(e,o),this.shouldToggleExpansion(e,r,i)&&(t.isExpanded(e)?t.collapse(e).then(void 0,c["e"]):t.expand(e).then(void 0,c["e"]))}return!0},t.prototype.shouldToggleExpansion=function(t,e,n){var i="mouse"===n&&2===e.detail;return this.openOnSingleClick||i||this.isClickOnTwistie(e)},Object.defineProperty(t.prototype,"openOnSingleClick",{get:function(){return 0===this.options.openMode},enumerable:!0,configurable:!0}),t.prototype.isClickOnTwistie=function(t){var e=t.target;if(!p["I"](e,"content"))return!1;var n=window.getComputedStyle(e,":before");if("none"===n.backgroundImage||"none"===n.display)return!1;var i=parseInt(n.width)+parseInt(n.paddingRight);return t.browserEvent.offsetX<=i},t.prototype.onContextMenu=function(t,e,n){return n.target&&n.target.tagName&&"input"===n.target.tagName.toLowerCase()||n&&(n.preventDefault(),n.stopPropagation()),!1},t.prototype.onTap=function(t,e,n){var i=n.initialTarget;return(!i||!i.tagName||"input"!==i.tagName.toLowerCase())&&this.onLeftClick(t,e,n,"touch")},t.prototype.onKeyDown=function(t,e){return this.onKey(this.downKeyBindingDispatcher,t,e)},t.prototype.onKeyUp=function(t,e){return this.onKey(this.upKeyBindingDispatcher,t,e)},t.prototype.onKey=function(t,e,n){var i=t.dispatch(n.toKeybinding());return!(!i||!i(e,n))&&(n.preventDefault(),n.stopPropagation(),!0)},t.prototype.onUp=function(t,e){var n={origin:"keyboard",originalEvent:e};return t.getHighlight()?t.clearHighlight(n):(t.focusPrevious(1,n),t.reveal(t.getFocus()).then(void 0,c["e"])),!0},t.prototype.onPageUp=function(t,e){var n={origin:"keyboard",originalEvent:e};return t.getHighlight()?t.clearHighlight(n):(t.focusPreviousPage(n),t.reveal(t.getFocus()).then(void 0,c["e"])),!0},t.prototype.onDown=function(t,e){var n={origin:"keyboard",originalEvent:e};return t.getHighlight()?t.clearHighlight(n):(t.focusNext(1,n),t.reveal(t.getFocus()).then(void 0,c["e"])),!0},t.prototype.onPageDown=function(t,e){var n={origin:"keyboard",originalEvent:e};return t.getHighlight()?t.clearHighlight(n):(t.focusNextPage(n),t.reveal(t.getFocus()).then(void 0,c["e"])),!0},t.prototype.onHome=function(t,e){var n={origin:"keyboard",originalEvent:e};return t.getHighlight()?t.clearHighlight(n):(t.focusFirst(n),t.reveal(t.getFocus()).then(void 0,c["e"])),!0},t.prototype.onEnd=function(t,e){var n={origin:"keyboard",originalEvent:e};return t.getHighlight()?t.clearHighlight(n):(t.focusLast(n),t.reveal(t.getFocus()).then(void 0,c["e"])),!0},t.prototype.onLeft=function(t,e){var n={origin:"keyboard",originalEvent:e};if(t.getHighlight())t.clearHighlight(n);else{var i=t.getFocus();t.collapse(i).then((function(e){if(i&&!e)return t.focusParent(n),t.reveal(t.getFocus())})).then(void 0,c["e"])}return!0},t.prototype.onRight=function(t,e){var n={origin:"keyboard",originalEvent:e};if(t.getHighlight())t.clearHighlight(n);else{var i=t.getFocus();t.expand(i).then((function(e){if(i&&!e)return t.focusFirstChild(n),t.reveal(t.getFocus())})).then(void 0,c["e"])}return!0},t.prototype.onEnter=function(t,e){var n={origin:"keyboard",originalEvent:e};if(t.getHighlight())return!1;var i=t.getFocus();return i&&t.setSelection([i],n),!0},t.prototype.onSpace=function(t,e){if(t.getHighlight())return!1;var n=t.getFocus();return n&&t.toggleExpansion(n),!0},t.prototype.onEscape=function(t,e){var n={origin:"keyboard",originalEvent:e};return t.getHighlight()?(t.clearHighlight(n),!0):t.getSelection().length?(t.clearSelection(n),!0):!!t.getFocus()&&(t.clearFocus(n),!0)},t}(),m=function(){function t(){}return t.prototype.getDragURI=function(t,e){return null},t.prototype.onDragStart=function(t,e,n){},t.prototype.onDragOver=function(t,e,n,i){return null},t.prototype.drop=function(t,e,n,i){},t}(),y=function(){function t(){}return t.prototype.isVisible=function(t,e){return!0},t}(),v=function(){function t(){}return t.prototype.getAriaLabel=function(t,e){return null},t}(),b=function(){function t(t,e){this.styleElement=t,this.selectorSuffix=e}return t.prototype.style=function(t){var e=this.selectorSuffix?"."+this.selectorSuffix:"",n=[];t.listFocusBackground&&n.push(".monaco-tree"+e+".focused .monaco-tree-rows > .monaco-tree-row.focused:not(.highlighted) { background-color: "+t.listFocusBackground+"; }"),t.listFocusForeground&&n.push(".monaco-tree"+e+".focused .monaco-tree-rows > .monaco-tree-row.focused:not(.highlighted) { color: "+t.listFocusForeground+"; }"),t.listActiveSelectionBackground&&n.push(".monaco-tree"+e+".focused .monaco-tree-rows > .monaco-tree-row.selected:not(.highlighted) { background-color: "+t.listActiveSelectionBackground+"; }"),t.listActiveSelectionForeground&&n.push(".monaco-tree"+e+".focused .monaco-tree-rows > .monaco-tree-row.selected:not(.highlighted) { color: "+t.listActiveSelectionForeground+"; }"),t.listFocusAndSelectionBackground&&n.push("\n\t\t\t\t.monaco-tree-drag-image,\n\t\t\t\t.monaco-tree"+e+".focused .monaco-tree-rows > .monaco-tree-row.focused.selected:not(.highlighted) { background-color: "+t.listFocusAndSelectionBackground+"; }\n\t\t\t"),t.listFocusAndSelectionForeground&&n.push("\n\t\t\t\t.monaco-tree-drag-image,\n\t\t\t\t.monaco-tree"+e+".focused .monaco-tree-rows > .monaco-tree-row.focused.selected:not(.highlighted) { color: "+t.listFocusAndSelectionForeground+"; }\n\t\t\t"),t.listInactiveSelectionBackground&&n.push(".monaco-tree"+e+" .monaco-tree-rows > .monaco-tree-row.selected:not(.highlighted) { background-color: "+t.listInactiveSelectionBackground+"; }"),t.listInactiveSelectionForeground&&n.push(".monaco-tree"+e+" .monaco-tree-rows > .monaco-tree-row.selected:not(.highlighted) { color: "+t.listInactiveSelectionForeground+"; }"),t.listHoverBackground&&n.push(".monaco-tree"+e+" .monaco-tree-rows > .monaco-tree-row:hover:not(.highlighted):not(.selected):not(.focused) { background-color: "+t.listHoverBackground+"; }"),t.listHoverForeground&&n.push(".monaco-tree"+e+" .monaco-tree-rows > .monaco-tree-row:hover:not(.highlighted):not(.selected):not(.focused) { color: "+t.listHoverForeground+"; }"),t.listDropBackground&&n.push("\n\t\t\t\t.monaco-tree"+e+" .monaco-tree-wrapper.drop-target,\n\t\t\t\t.monaco-tree"+e+" .monaco-tree-rows > .monaco-tree-row.drop-target { background-color: "+t.listDropBackground+" !important; color: inherit !important; }\n\t\t\t"),t.listFocusOutline&&n.push("\n\t\t\t\t.monaco-tree-drag-image\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{ border: 1px solid "+t.listFocusOutline+"; background: #000; }\n\t\t\t\t.monaco-tree"+e+" .monaco-tree-rows > .monaco-tree-row \t\t\t\t\t\t\t\t\t\t\t\t\t\t{ border: 1px solid transparent; }\n\t\t\t\t.monaco-tree"+e+".focused .monaco-tree-rows > .monaco-tree-row.focused:not(.highlighted) \t\t\t\t\t\t{ border: 1px dotted "+t.listFocusOutline+"; }\n\t\t\t\t.monaco-tree"+e+".focused .monaco-tree-rows > .monaco-tree-row.selected:not(.highlighted) \t\t\t\t\t\t{ border: 1px solid "+t.listFocusOutline+"; }\n\t\t\t\t.monaco-tree"+e+" .monaco-tree-rows > .monaco-tree-row.selected:not(.highlighted)  \t\t\t\t\t\t\t{ border: 1px solid "+t.listFocusOutline+"; }\n\t\t\t\t.monaco-tree"+e+" .monaco-tree-rows > .monaco-tree-row:hover:not(.highlighted):not(.selected):not(.focused)  \t{ border: 1px dashed "+t.listFocusOutline+"; }\n\t\t\t\t.monaco-tree"+e+" .monaco-tree-wrapper.drop-target,\n\t\t\t\t.monaco-tree"+e+" .monaco-tree-rows > .monaco-tree-row.drop-target\t\t\t\t\t\t\t\t\t\t\t\t{ border: 1px dashed "+t.listFocusOutline+"; }\n\t\t\t");var i=n.join("\n");i!==this.styleElement.innerHTML&&(this.styleElement.innerHTML=i)},t}(),_=n("1569"),w=n("a666"),x=n("308f"),D=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},t(e,n)};return function(e,n){function i(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),C=function(){function t(t){this._onDispose=new x["a"],this.onDispose=this._onDispose.event,this._item=t}return Object.defineProperty(t.prototype,"item",{get:function(){return this._item},enumerable:!0,configurable:!0}),t.prototype.dispose=function(){this._onDispose&&(this._onDispose.fire(),this._onDispose.dispose(),this._onDispose=void 0)},t}(),k=function(){function t(){this.locks=Object.create({})}return t.prototype.isLocked=function(t){return!!this.locks[t.id]},t.prototype.run=function(t,e){var n,i=this,r=this.getLock(t);return new Promise(r?function(n,o){x["b"].once(r.onDispose)((function(){return i.run(t,e).then(n,o)}))}:function(r,o){if(t.isDisposed())return o(new Error("Item is disposed."));var s=i.locks[t.id]=new C(t);return n=e().then((function(e){return delete i.locks[t.id],s.dispose(),e})).then(r,o),n})},t.prototype.getLock=function(t){var e;for(e in this.locks){var n=this.locks[e];if(t.intersects(n.item))return n}return null},t}(),I=function(){function t(){this._isDisposed=!1,this._onDidRevealItem=new x["d"],this.onDidRevealItem=this._onDidRevealItem.event,this._onExpandItem=new x["d"],this.onExpandItem=this._onExpandItem.event,this._onDidExpandItem=new x["d"],this.onDidExpandItem=this._onDidExpandItem.event,this._onCollapseItem=new x["d"],this.onCollapseItem=this._onCollapseItem.event,this._onDidCollapseItem=new x["d"],this.onDidCollapseItem=this._onDidCollapseItem.event,this._onDidAddTraitItem=new x["d"],this.onDidAddTraitItem=this._onDidAddTraitItem.event,this._onDidRemoveTraitItem=new x["d"],this.onDidRemoveTraitItem=this._onDidRemoveTraitItem.event,this._onDidRefreshItem=new x["d"],this.onDidRefreshItem=this._onDidRefreshItem.event,this._onRefreshItemChildren=new x["d"],this.onRefreshItemChildren=this._onRefreshItemChildren.event,this._onDidRefreshItemChildren=new x["d"],this.onDidRefreshItemChildren=this._onDidRefreshItemChildren.event,this._onDidDisposeItem=new x["d"],this.onDidDisposeItem=this._onDidDisposeItem.event,this.items={}}return t.prototype.register=function(t){_["a"](!this.isRegistered(t.id),"item already registered: "+t.id);var e=Object(w["e"])(this._onDidRevealItem.add(t.onDidReveal),this._onExpandItem.add(t.onExpand),this._onDidExpandItem.add(t.onDidExpand),this._onCollapseItem.add(t.onCollapse),this._onDidCollapseItem.add(t.onDidCollapse),this._onDidAddTraitItem.add(t.onDidAddTrait),this._onDidRemoveTraitItem.add(t.onDidRemoveTrait),this._onDidRefreshItem.add(t.onDidRefresh),this._onRefreshItemChildren.add(t.onRefreshChildren),this._onDidRefreshItemChildren.add(t.onDidRefreshChildren),this._onDidDisposeItem.add(t.onDidDispose));this.items[t.id]={item:t,disposable:e}},t.prototype.deregister=function(t){_["a"](this.isRegistered(t.id),"item not registered: "+t.id),this.items[t.id].disposable.dispose(),delete this.items[t.id]},t.prototype.isRegistered=function(t){return this.items.hasOwnProperty(t)},t.prototype.getItem=function(t){var e=this.items[t];return e?e.item:null},t.prototype.dispose=function(){this.items={},this._onDidRevealItem.dispose(),this._onExpandItem.dispose(),this._onDidExpandItem.dispose(),this._onCollapseItem.dispose(),this._onDidCollapseItem.dispose(),this._onDidAddTraitItem.dispose(),this._onDidRemoveTraitItem.dispose(),this._onDidRefreshItem.dispose(),this._onRefreshItemChildren.dispose(),this._onDidRefreshItemChildren.dispose(),this._isDisposed=!0},t.prototype.isDisposed=function(){return this._isDisposed},t}(),A=function(){function t(t,e,n,i,r){this._onDidCreate=new x["a"],this._onDidReveal=new x["a"],this.onDidReveal=this._onDidReveal.event,this._onExpand=new x["a"],this.onExpand=this._onExpand.event,this._onDidExpand=new x["a"],this.onDidExpand=this._onDidExpand.event,this._onCollapse=new x["a"],this.onCollapse=this._onCollapse.event,this._onDidCollapse=new x["a"],this.onDidCollapse=this._onDidCollapse.event,this._onDidAddTrait=new x["a"],this.onDidAddTrait=this._onDidAddTrait.event,this._onDidRemoveTrait=new x["a"],this.onDidRemoveTrait=this._onDidRemoveTrait.event,this._onDidRefresh=new x["a"],this.onDidRefresh=this._onDidRefresh.event,this._onRefreshChildren=new x["a"],this.onRefreshChildren=this._onRefreshChildren.event,this._onDidRefreshChildren=new x["a"],this.onDidRefreshChildren=this._onDidRefreshChildren.event,this._onDidDispose=new x["a"],this.onDidDispose=this._onDidDispose.event,this.registry=e,this.context=n,this.lock=i,this.element=r,this.id=t,this.registry.register(this),this.doesHaveChildren=this.context.dataSource.hasChildren(this.context.tree,this.element),this.needsChildrenRefresh=!0,this.parent=null,this.previous=null,this.next=null,this.firstChild=null,this.lastChild=null,this.traits={},this.depth=0,this.expanded=!(!this.context.dataSource.shouldAutoexpand||!this.context.dataSource.shouldAutoexpand(this.context.tree,r)),this._onDidCreate.fire(this),this.visible=this._isVisible(),this.height=this._getHeight(),this._isDisposed=!1}return t.prototype.getElement=function(){return this.element},t.prototype.hasChildren=function(){return this.doesHaveChildren},t.prototype.getDepth=function(){return this.depth},t.prototype.isVisible=function(){return this.visible},t.prototype.setVisible=function(t){this.visible=t},t.prototype.isExpanded=function(){return this.expanded},t.prototype._setExpanded=function(t){this.expanded=t},t.prototype.reveal=function(t){void 0===t&&(t=null);var e={item:this,relativeTop:t};this._onDidReveal.fire(e)},t.prototype.expand=function(){var t=this;if(this.isExpanded()||!this.doesHaveChildren||this.lock.isLocked(this))return Promise.resolve(!1);var e=this.lock.run(this,(function(){if(t.isExpanded()||!t.doesHaveChildren)return Promise.resolve(!1);var e,n={item:t};return t._onExpand.fire(n),e=t.needsChildrenRefresh?t.refreshChildren(!1,!0,!0):Promise.resolve(null),e.then((function(){return t._setExpanded(!0),t._onDidExpand.fire(n),!0}))}));return e.then((function(e){return!t.isDisposed()&&(t.context.options.autoExpandSingleChildren&&e&&null!==t.firstChild&&t.firstChild===t.lastChild&&t.firstChild.isVisible()?t.firstChild.expand().then((function(){return!0})):e)}))},t.prototype.collapse=function(t){var e=this;if(void 0===t&&(t=!1),t){var n=Promise.resolve(null);return this.forEachChild((function(t){n=n.then((function(){return t.collapse(!0)}))})),n.then((function(){return e.collapse(!1)}))}return!this.isExpanded()||this.lock.isLocked(this)?Promise.resolve(!1):this.lock.run(this,(function(){var t={item:e};return e._onCollapse.fire(t),e._setExpanded(!1),e._onDidCollapse.fire(t),Promise.resolve(!0)}))},t.prototype.addTrait=function(t){var e={item:this,trait:t};this.traits[t]=!0,this._onDidAddTrait.fire(e)},t.prototype.removeTrait=function(t){var e={item:this,trait:t};delete this.traits[t],this._onDidRemoveTrait.fire(e)},t.prototype.hasTrait=function(t){return this.traits[t]||!1},t.prototype.getAllTraits=function(){var t,e=[];for(t in this.traits)this.traits.hasOwnProperty(t)&&this.traits[t]&&e.push(t);return e},t.prototype.getHeight=function(){return this.height},t.prototype.refreshChildren=function(e,n,i){var r=this;if(void 0===n&&(n=!1),void 0===i&&(i=!1),!i&&!this.isExpanded()){var o=function(t){t.needsChildrenRefresh=!0,t.forEachChild(o)};return o(this),Promise.resolve(this)}this.needsChildrenRefresh=!1;var s=function(){var i,o={item:r,isNested:n};r._onRefreshChildren.fire(o),i=r.doesHaveChildren?r.context.dataSource.getChildren(r.context.tree,r.element):Promise.resolve([]);var s=i.then((function(n){if(r.isDisposed()||r.registry.isDisposed())return Promise.resolve(null);if(!Array.isArray(n))return Promise.reject(new Error("Please return an array of children."));n=n?n.slice(0):[],n=r.sort(n);var i={};while(null!==r.firstChild)i[r.firstChild.id]=r.firstChild,r.removeChild(r.firstChild);for(var o=0,s=n.length;o<s;o++){var a=n[o],h=r.context.dataSource.getId(r.context.tree,a),u=i[h]||new t(h,r.registry,r.context,r.lock,a);u.element=a,e&&(u.needsChildrenRefresh=e),delete i[h],r.addChild(u)}for(var l in i)i.hasOwnProperty(l)&&i[l].dispose();return e?Promise.all(r.mapEachChild((function(t){return t.doRefresh(e,!0)}))):Promise.all(r.mapEachChild((function(t){return t.isExpanded()&&t.needsChildrenRefresh?t.doRefresh(e,!0):(t.updateVisibility(),Promise.resolve(null))})))}));return s.then(void 0,c["e"]).then((function(){return r._onDidRefreshChildren.fire(o)}))};return n?s():this.lock.run(this,s)},t.prototype.doRefresh=function(t,e){return void 0===e&&(e=!1),this.doesHaveChildren=this.context.dataSource.hasChildren(this.context.tree,this.element),this.height=this._getHeight(),this.updateVisibility(),this._onDidRefresh.fire(this),this.refreshChildren(t,e)},t.prototype.updateVisibility=function(){this.setVisible(this._isVisible())},t.prototype.refresh=function(t){return this.doRefresh(t)},t.prototype.getNavigator=function(){return new S(this)},t.prototype.intersects=function(t){return this.isAncestorOf(t)||t.isAncestorOf(this)},t.prototype.isAncestorOf=function(t){var e=t;while(e){if(e.id===this.id)return!0;e=e.parent}return!1},t.prototype.addChild=function(t,e){void 0===e&&(e=this.lastChild);var n=null===this.firstChild,i=null===e,r=e===this.lastChild;if(n)this.firstChild=this.lastChild=t,t.next=t.previous=null;else if(i){if(!this.firstChild)throw new Error("Invalid tree state");this.firstChild.previous=t,t.next=this.firstChild,t.previous=null,this.firstChild=t}else if(r){if(!this.lastChild)throw new Error("Invalid tree state");this.lastChild.next=t,t.next=null,t.previous=this.lastChild,this.lastChild=t}else{if(t.previous=e,!e)throw new Error("Invalid tree state");if(t.next=e.next,!e.next)throw new Error("Invalid tree state");e.next.previous=t,e.next=t}t.parent=this,t.depth=this.depth+1},t.prototype.removeChild=function(t){var e=this.firstChild===t,n=this.lastChild===t;if(e&&n)this.firstChild=this.lastChild=null;else if(e){if(!t.next)throw new Error("Invalid tree state");t.next.previous=null,this.firstChild=t.next}else if(n){if(!t.previous)throw new Error("Invalid tree state");t.previous.next=null,this.lastChild=t.previous}else{if(!t.next)throw new Error("Invalid tree state");if(t.next.previous=t.previous,!t.previous)throw new Error("Invalid tree state");t.previous.next=t.next}t.parent=null,t.depth=NaN},t.prototype.forEachChild=function(t){var e,n=this.firstChild;while(n)e=n.next,t(n),n=e},t.prototype.mapEachChild=function(t){var e=[];return this.forEachChild((function(n){e.push(t(n))})),e},t.prototype.sort=function(t){var e=this,n=this.context.sorter;return n?t.sort((function(t,i){return n.compare(e.context.tree,t,i)})):t},t.prototype._getHeight=function(){return this.context.renderer?this.context.renderer.getHeight(this.context.tree,this.element):0},t.prototype._isVisible=function(){return!!this.context.filter&&this.context.filter.isVisible(this.context.tree,this.element)},t.prototype.isDisposed=function(){return this._isDisposed},t.prototype.dispose=function(){this.forEachChild((function(t){return t.dispose()})),this.parent=null,this.previous=null,this.next=null,this.firstChild=null,this.lastChild=null,this._onDidDispose.fire(this),this.registry.deregister(this),this._onDidCreate.dispose(),this._onDidReveal.dispose(),this._onExpand.dispose(),this._onDidExpand.dispose(),this._onCollapse.dispose(),this._onDidCollapse.dispose(),this._onDidAddTrait.dispose(),this._onDidRemoveTrait.dispose(),this._onDidRefresh.dispose(),this._onRefreshChildren.dispose(),this._onDidRefreshChildren.dispose(),this._onDidDispose.dispose(),this._isDisposed=!0},t}(),E=function(t){function e(e,n,i,r,o){return t.call(this,e,n,i,r,o)||this}return D(e,t),e.prototype.isVisible=function(){return!1},e.prototype.setVisible=function(t){},e.prototype.isExpanded=function(){return!0},e.prototype._setExpanded=function(t){},e.prototype._getHeight=function(){return 0},e.prototype._isVisible=function(){return!1},e}(A),S=function(){function t(t,e){void 0===e&&(e=!0),this.item=t,this.start=e?t:null}return t.lastDescendantOf=function(e){return e?e instanceof E?t.lastDescendantOf(e.lastChild):e.isVisible()?e.isExpanded()&&null!==e.lastChild?t.lastDescendantOf(e.lastChild):e:t.lastDescendantOf(e.previous):null},t.prototype.current=function(){return this.item||null},t.prototype.next=function(){if(this.item)do{if((this.item instanceof E||this.item.isVisible()&&this.item.isExpanded())&&this.item.firstChild)this.item=this.item.firstChild;else if(this.item===this.start)this.item=null;else{while(this.item&&this.item!==this.start&&!this.item.next)this.item=this.item.parent;this.item===this.start&&(this.item=null),this.item=this.item?this.item.next:null}}while(this.item&&!this.item.isVisible());return this.item||null},t.prototype.previous=function(){if(this.item)do{var e=t.lastDescendantOf(this.item.previous);e?this.item=e:this.item.parent&&this.item.parent!==this.start&&this.item.parent.isVisible()?this.item=this.item.parent:this.item=null}while(this.item&&!this.item.isVisible());return this.item||null},t.prototype.parent=function(){if(this.item){var t=this.item.parent;t&&t!==this.start&&t.isVisible()?this.item=t:this.item=null}return this.item||null},t.prototype.first=function(){return this.item=this.start,this.next(),this.item||null},t.prototype.last=function(){return t.lastDescendantOf(this.start)},t}(),T=function(){function t(t){this.registry=new I,this.registryDisposable=w["a"].None,this._onSetInput=new x["a"],this.onSetInput=this._onSetInput.event,this._onDidSetInput=new x["a"],this.onDidSetInput=this._onDidSetInput.event,this._onRefresh=new x["a"],this.onRefresh=this._onRefresh.event,this._onDidRefresh=new x["a"],this.onDidRefresh=this._onDidRefresh.event,this._onDidHighlight=new x["a"],this.onDidHighlight=this._onDidHighlight.event,this._onDidSelect=new x["a"],this.onDidSelect=this._onDidSelect.event,this._onDidFocus=new x["a"],this.onDidFocus=this._onDidFocus.event,this._onDidRevealItem=new x["f"],this.onDidRevealItem=this._onDidRevealItem.event,this._onExpandItem=new x["f"],this.onExpandItem=this._onExpandItem.event,this._onDidExpandItem=new x["f"],this.onDidExpandItem=this._onDidExpandItem.event,this._onCollapseItem=new x["f"],this.onCollapseItem=this._onCollapseItem.event,this._onDidCollapseItem=new x["f"],this.onDidCollapseItem=this._onDidCollapseItem.event,this._onDidAddTraitItem=new x["f"],this.onDidAddTraitItem=this._onDidAddTraitItem.event,this._onDidRemoveTraitItem=new x["f"],this.onDidRemoveTraitItem=this._onDidRemoveTraitItem.event,this._onDidRefreshItem=new x["f"],this.onDidRefreshItem=this._onDidRefreshItem.event,this._onRefreshItemChildren=new x["f"],this.onRefreshItemChildren=this._onRefreshItemChildren.event,this._onDidRefreshItemChildren=new x["f"],this.onDidRefreshItemChildren=this._onDidRefreshItemChildren.event,this._onDidDisposeItem=new x["f"],this.context=t,this.input=null,this.traitsToItems={}}return t.prototype.setInput=function(t){var e=this,n={item:this.input};this._onSetInput.fire(n),this.setSelection([]),this.setFocus(),this.setHighlight(),this.lock=new k,this.input&&this.input.dispose(),this.registry&&(this.registry.dispose(),this.registryDisposable.dispose()),this.registry=new I,this._onDidRevealItem.input=this.registry.onDidRevealItem,this._onExpandItem.input=this.registry.onExpandItem,this._onDidExpandItem.input=this.registry.onDidExpandItem,this._onCollapseItem.input=this.registry.onCollapseItem,this._onDidCollapseItem.input=this.registry.onDidCollapseItem,this._onDidAddTraitItem.input=this.registry.onDidAddTraitItem,this._onDidRemoveTraitItem.input=this.registry.onDidRemoveTraitItem,this._onDidRefreshItem.input=this.registry.onDidRefreshItem,this._onRefreshItemChildren.input=this.registry.onRefreshItemChildren,this._onDidRefreshItemChildren.input=this.registry.onDidRefreshItemChildren,this._onDidDisposeItem.input=this.registry.onDidDisposeItem,this.registryDisposable=this.registry.onDidDisposeItem((function(t){return t.getAllTraits().forEach((function(n){return delete e.traitsToItems[n][t.id]}))}));var i=this.context.dataSource.getId(this.context.tree,t);return this.input=new E(i,this.registry,this.context,this.lock,t),n={item:this.input},this._onDidSetInput.fire(n),this.refresh(this.input)},t.prototype.getInput=function(){return this.input?this.input.getElement():null},t.prototype.refresh=function(t,e){var n=this;void 0===t&&(t=null),void 0===e&&(e=!0);var i=this.getItem(t);if(!i)return Promise.resolve(null);var r={item:i,recursive:e};return this._onRefresh.fire(r),i.refresh(e).then((function(){n._onDidRefresh.fire(r)}))},t.prototype.expand=function(t){var e=this.getItem(t);return e?e.expand():Promise.resolve(!1)},t.prototype.collapse=function(t,e){void 0===e&&(e=!1);var n=this.getItem(t);return n?n.collapse(e):Promise.resolve(!1)},t.prototype.toggleExpansion=function(t,e){return void 0===e&&(e=!1),this.isExpanded(t)?this.collapse(t,e):this.expand(t)},t.prototype.isExpanded=function(t){var e=this.getItem(t);return!!e&&e.isExpanded()},t.prototype.reveal=function(t,e){var n=this;return void 0===e&&(e=null),this.resolveUnknownParentChain(t).then((function(t){var e=Promise.resolve(null);return t.forEach((function(t){e=e.then((function(){return n.expand(t)}))})),e})).then((function(){var i=n.getItem(t);if(i)return i.reveal(e)}))},t.prototype.resolveUnknownParentChain=function(t){var e=this;return this.context.dataSource.getParent(this.context.tree,t).then((function(t){return t?e.resolveUnknownParentChain(t).then((function(e){return e.push(t),e})):Promise.resolve([])}))},t.prototype.setHighlight=function(t,e){this.setTraits("highlighted",t?[t]:[]);var n={highlight:this.getHighlight(),payload:e};this._onDidHighlight.fire(n)},t.prototype.getHighlight=function(t){void 0===t&&(t=!1);var e=this.getElementsWithTrait("highlighted",t);return 0===e.length?null:e[0]},t.prototype.setSelection=function(t,e){this.setTraits("selected",t);var n={selection:this.getSelection(),payload:e};this._onDidSelect.fire(n)},t.prototype.getSelection=function(t){return void 0===t&&(t=!1),this.getElementsWithTrait("selected",t)},t.prototype.setFocus=function(t,e){this.setTraits("focused",t?[t]:[]);var n={focus:this.getFocus(),payload:e};this._onDidFocus.fire(n)},t.prototype.getFocus=function(t){void 0===t&&(t=!1);var e=this.getElementsWithTrait("focused",t);return 0===e.length?null:e[0]},t.prototype.focusNext=function(t,e){void 0===t&&(t=1);for(var n,i=this.getFocus()||this.input,r=this.getNavigator(i,!1),o=0;o<t;o++){if(n=r.next(),!n)break;i=n}this.setFocus(i,e)},t.prototype.focusPrevious=function(t,e){void 0===t&&(t=1);for(var n,i=this.getFocus()||this.input,r=this.getNavigator(i,!1),o=0;o<t;o++){if(n=r.previous(),!n)break;i=n}this.setFocus(i,e)},t.prototype.focusParent=function(t){var e=this.getFocus()||this.input,n=this.getNavigator(e,!1),i=n.parent();i&&this.setFocus(i,t)},t.prototype.focusFirstChild=function(t){var e=this.getItem(this.getFocus()||this.input),n=this.getNavigator(e,!1),i=n.next(),r=n.parent();r===e&&this.setFocus(i,t)},t.prototype.focusFirst=function(t,e){this.focusNth(0,t,e)},t.prototype.focusNth=function(t,e,n){for(var i=this.getParent(n),r=this.getNavigator(i),o=r.first(),s=0;s<t;s++)o=r.next();o&&this.setFocus(o,e)},t.prototype.focusLast=function(t,e){var n,i=this.getParent(e);if(e&&i)n=i.lastChild;else{var r=this.getNavigator(i);n=r.last()}n&&this.setFocus(n,t)},t.prototype.getParent=function(t){if(t){var e=this.getItem(t);if(e&&e.parent)return e.parent}return this.getItem(this.input)},t.prototype.getNavigator=function(t,e){return void 0===t&&(t=null),void 0===e&&(e=!0),new S(this.getItem(t),e)},t.prototype.getItem=function(t){return void 0===t&&(t=null),null===t?this.input:t instanceof A?t:"string"===typeof t?this.registry.getItem(t):this.registry.getItem(this.context.dataSource.getId(this.context.tree,t))},t.prototype.removeTraits=function(t,e){var n,i,r=this.traitsToItems[t]||{};if(0===e.length){for(i in r)r.hasOwnProperty(i)&&(n=r[i],n.removeTrait(t));delete this.traitsToItems[t]}else for(var o=0,s=e.length;o<s;o++)n=this.getItem(e[o]),n&&(n.removeTrait(t),delete r[n.id])},t.prototype.setTraits=function(t,e){if(0===e.length)this.removeTraits(t,e);else{for(var n={},i=void 0,r=0,o=e.length;r<o;r++)i=this.getItem(e[r]),i&&(n[i.id]=i);var s=this.traitsToItems[t]||{},a=[],h=void 0;for(h in s)s.hasOwnProperty(h)&&(n.hasOwnProperty(h)?delete n[h]:a.push(s[h]));for(r=0,o=a.length;r<o;r++)i=a[r],i.removeTrait(t),delete s[i.id];for(h in n)n.hasOwnProperty(h)&&(i=n[h],i.addTrait(t),s[h]=i);this.traitsToItems[t]=s}},t.prototype.getElementsWithTrait=function(t,e){var n,i=[],r=this.traitsToItems[t]||{};for(n in r)r.hasOwnProperty(n)&&(r[n].isVisible()||e)&&i.push(r[n].getElement());return i},t.prototype.dispose=function(){this.registry.dispose(),this._onSetInput.dispose(),this._onDidSetInput.dispose(),this._onRefresh.dispose(),this._onDidRefresh.dispose(),this._onDidHighlight.dispose(),this._onDidSelect.dispose(),this._onDidFocus.dispose(),this._onDidRevealItem.dispose(),this._onExpandItem.dispose(),this._onDidExpandItem.dispose(),this._onCollapseItem.dispose(),this._onDidCollapseItem.dispose(),this._onDidAddTraitItem.dispose(),this._onDidRemoveTraitItem.dispose(),this._onDidRefreshItem.dispose(),this._onRefreshItemChildren.dispose(),this._onDidRefreshItemChildren.dispose(),this._onDidDisposeItem.dispose()},t}(),P=n("0f70"),O=n("1b0e"),F=n("a60f"),R=n("3742"),L=n("5d28"),M=n("b835"),j=function(){function t(t){this.elements=t}return t.prototype.update=function(t){},t.prototype.getData=function(){return this.elements},t}(),B=function(){function t(t){this.elements=t}return t.prototype.update=function(t){},t.prototype.getData=function(){return this.elements},t}(),N=function(){function t(){this.types=[],this.files=[]}return t.prototype.update=function(t){t.types&&(this.types=[],Array.prototype.push.apply(this.types,t.types)),t.files&&(this.files=[],Array.prototype.push.apply(this.files,t.files),this.files=this.files.filter((function(t){return t.size||t.type})))},t.prototype.getData=function(){return{types:this.types,files:this.files}},t}(),H=n("258a"),K=n("1898"),q=function(){function t(){this.heightMap=[],this.indexes={}}return t.prototype.getContentHeight=function(){var t=this.heightMap[this.heightMap.length-1];return t?t.top+t.height:0},t.prototype.onInsertItems=function(t,e){void 0===e&&(e=null);var n,i,r,o,s=null,a=0;if(null===e)i=0,o=0;else{if(i=this.indexes[e]+1,n=this.heightMap[i-1],!n)return void console.error("view item doesnt exist");o=n.top+n.height}var h=this.heightMap.splice.bind(this.heightMap,i,0),u=[];while(s=t.next())n=this.createViewItem(s),n.top=o+a,this.indexes[s.id]=i++,u.push(n),a+=n.height;for(h.apply(this.heightMap,u),r=i;r<this.heightMap.length;r++)n=this.heightMap[r],n.top+=a,this.indexes[n.model.id]=r;for(r=u.length-1;r>=0;r--)this.onInsertItem(u[r]);for(r=this.heightMap.length-1;r>=i;r--)this.onRefreshItem(this.heightMap[r]);return a},t.prototype.onInsertItem=function(t){},t.prototype.onRemoveItems=function(t){var e,n=null,i=null,r=0,o=0;while(n=t.next()){if(r=this.indexes[n],e=this.heightMap[r],!e)return void console.error("view item doesnt exist");o-=e.height,delete this.indexes[n],this.onRemoveItem(e),null===i&&(i=r)}if(0!==o&&null!==i)for(this.heightMap.splice(i,r-i+1),r=i;r<this.heightMap.length;r++)e=this.heightMap[r],e.top+=o,this.indexes[e.model.id]=r,this.onRefreshItem(e)},t.prototype.onRemoveItem=function(t){},t.prototype.onRefreshItemSet=function(t){var e=this,n=t.sort((function(t,n){return e.indexes[t.id]-e.indexes[n.id]}));this.onRefreshItems(new H["a"](n))},t.prototype.onRefreshItems=function(t){var e,n,i,r=null,o=null,s=0;while(r=t.next()){for(i=this.indexes[r.id];0!==s&&null!==o&&o<i;o++)e=this.heightMap[o],e.top+=s,this.onRefreshItem(e);e=this.heightMap[i],n=r.getHeight(),e.top+=s,s+=n-e.height,e.height=n,this.onRefreshItem(e,!0),o=i+1}if(0!==s&&null!==o)for(;o<this.heightMap.length;o++)e=this.heightMap[o],e.top+=s,this.onRefreshItem(e)},t.prototype.onRefreshItem=function(t,e){void 0===e&&(e=!1)},t.prototype.indexAt=function(t){var e,n,i=0,r=this.heightMap.length;while(i<r)if(e=Math.floor((i+r)/2),n=this.heightMap[e],t<n.top)r=e;else{if(!(t>=n.top+n.height))return e;if(i===e)break;i=e}return this.heightMap.length},t.prototype.indexAfter=function(t){return Math.min(this.indexAt(t)+1,this.heightMap.length)},t.prototype.itemAtIndex=function(t){return this.heightMap[t]},t.prototype.itemAfter=function(t){return this.heightMap[this.indexes[t.model.id]+1]||null},t.prototype.createViewItem=function(t){throw new Error("not implemented")},t.prototype.dispose=function(){this.heightMap=[],this.indexes={}},t}(),z=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},t(e,n)};return function(e,n){function i(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),U=function(){function t(t,e,n){this._posx=t,this._posy=e,this._target=n}return t.prototype.preventDefault=function(){},t.prototype.stopPropagation=function(){},Object.defineProperty(t.prototype,"target",{get:function(){return this._target},enumerable:!0,configurable:!0}),t}(),W=function(t){function e(e){var n=t.call(this,e.posx,e.posy,e.target)||this;return n.originalEvent=e,n}return z(e,t),e.prototype.preventDefault=function(){this.originalEvent.preventDefault()},e.prototype.stopPropagation=function(){this.originalEvent.stopPropagation()},e}(U),V=function(t){function e(e,n,i){var r=t.call(this,e,n,i.target)||this;return r.originalEvent=i,r}return z(e,t),e.prototype.preventDefault=function(){this.originalEvent.preventDefault()},e.prototype.stopPropagation=function(){this.originalEvent.stopPropagation()},e}(U),$=n("650e"),G=n("5fe7"),Z=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},t(e,n)};return function(e,n){function i(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}();function Y(t){try{t.parentElement.removeChild(t)}catch(e){}}var J=function(){function t(t){this.context=t,this._cache={"":[]}}return t.prototype.alloc=function(t){var e=this.cache(t).pop();if(!e){var n=document.createElement("div");n.className="content";var i=document.createElement("div");i.appendChild(n);var r=null;try{r=this.context.renderer.renderTemplate(this.context.tree,t,n)}catch(o){console.error("Tree usage error: exception while rendering template"),console.error(o)}e={element:i,templateId:t,templateData:r}}return e},t.prototype.release=function(t,e){Y(e.element),this.cache(t).push(e)},t.prototype.cache=function(t){return this._cache[t]||(this._cache[t]=[])},t.prototype.garbageCollect=function(){var t=this;this._cache&&Object.keys(this._cache).forEach((function(e){t._cache[e].forEach((function(n){t.context.renderer.disposeTemplate(t.context.tree,e,n.templateData),n.element=null,n.templateData=null})),delete t._cache[e]}))},t.prototype.dispose=function(){this.garbageCollect(),this._cache=null},t}(),X=function(){function t(t,e){var n=this;this.width=0,this.needsRender=!1,this.uri=null,this.unbindDragStart=w["a"].None,this._draggable=!1,this.context=t,this.model=e,this.id=this.model.id,this.row=null,this.top=0,this.height=e.getHeight(),this._styles={},e.getAllTraits().forEach((function(t){return n._styles[t]=!0})),e.isExpanded()&&this.addClass("expanded")}return Object.defineProperty(t.prototype,"expanded",{set:function(t){t?this.addClass("expanded"):this.removeClass("expanded")},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"loading",{set:function(t){t?this.addClass("codicon-loading"):this.removeClass("codicon-loading")},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"draggable",{get:function(){return this._draggable},set:function(t){this._draggable=t,this.render(!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"dropTarget",{set:function(t){t?this.addClass("drop-target"):this.removeClass("drop-target")},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"element",{get:function(){return this.row&&this.row.element},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"templateId",{get:function(){return this._templateId||(this._templateId=this.context.renderer.getTemplateId&&this.context.renderer.getTemplateId(this.context.tree,this.model.getElement()))},enumerable:!0,configurable:!0}),t.prototype.addClass=function(t){this._styles[t]=!0,this.render(!0)},t.prototype.removeClass=function(t){delete this._styles[t],this.render(!0)},t.prototype.render=function(t){var e=this;if(void 0===t&&(t=!1),this.model&&this.element){var n=["monaco-tree-row"];n.push.apply(n,Object.keys(this._styles)),this.model.hasChildren()&&n.push("has-children"),this.element.className=n.join(" "),this.element.draggable=this.draggable,this.element.style.height=this.height+"px",this.element.setAttribute("role","treeitem");var i=this.context.accessibilityProvider,r=i.getAriaLabel(this.context.tree,this.model.getElement());if(r&&this.element.setAttribute("aria-label",r),i.getPosInSet&&i.getSetSize&&(this.element.setAttribute("aria-setsize",i.getSetSize()),this.element.setAttribute("aria-posinset",i.getPosInSet(this.context.tree,this.model.getElement()))),this.model.hasTrait("focused")){var o=R["L"](this.model.id);this.element.setAttribute("aria-selected","true"),this.element.setAttribute("id",o)}else this.element.setAttribute("aria-selected","false"),this.element.removeAttribute("id");this.model.hasChildren()?this.element.setAttribute("aria-expanded",String(!!this._styles["expanded"])):this.element.removeAttribute("aria-expanded"),this.element.setAttribute("aria-level",String(this.model.getDepth())),this.context.options.paddingOnRow?this.element.style.paddingLeft=this.context.options.twistiePixels+(this.model.getDepth()-1)*this.context.options.indentPixels+"px":(this.element.style.paddingLeft=(this.model.getDepth()-1)*this.context.options.indentPixels+"px",this.row.element.firstElementChild.style.paddingLeft=this.context.options.twistiePixels+"px");var s=this.context.dnd.getDragURI(this.context.tree,this.model.getElement());if(s!==this.uri&&(this.unbindDragStart&&this.unbindDragStart.dispose(),s?(this.uri=s,this.draggable=!0,this.unbindDragStart=p["j"](this.element,"dragstart",(function(t){e.onDragStart(t)}))):this.uri=null),!t&&this.element){var a=0;if(this.context.horizontalScrolling){var h=window.getComputedStyle(this.element);a=parseFloat(h.paddingLeft)}this.context.horizontalScrolling&&(this.element.style.width=P["h"]?"-moz-fit-content":"fit-content");try{this.context.renderer.renderElement(this.context.tree,this.model.getElement(),this.templateId,this.row.templateData)}catch(u){console.error("Tree usage error: exception while rendering element"),console.error(u)}this.context.horizontalScrolling&&(this.width=p["B"](this.element)+a,this.element.style.width="")}}},t.prototype.insertInDOM=function(t,e){if(this.row||(this.row=this.context.cache.alloc(this.templateId),this.element[et.BINDING]=this),!this.element.parentElement){if(null===e)t.appendChild(this.element);else try{t.insertBefore(this.element,e)}catch(n){console.warn("Failed to locate previous tree element"),t.appendChild(this.element)}this.render()}},t.prototype.removeFromDOM=function(){this.row&&(this.unbindDragStart.dispose(),this.uri=null,this.element[et.BINDING]=null,this.context.cache.release(this.templateId,this.row),this.row=null)},t.prototype.dispose=function(){this.row=null},t}(),Q=function(t){function e(e,n,i){var r=t.call(this,e,n)||this;return r.row={element:i,templateData:null,templateId:null},r}return Z(e,t),e.prototype.render=function(){if(this.model&&this.element){var t=["monaco-tree-wrapper"];t.push.apply(t,Object.keys(this._styles)),this.model.hasChildren()&&t.push("has-children"),this.element.className=t.join(" ")}},e.prototype.insertInDOM=function(t,e){},e.prototype.removeFromDOM=function(){},e}(X);function tt(t,e){return!t&&!e||!(!t||!e)&&(t.accept===e.accept&&(t.bubble===e.bubble&&t.effect===e.effect))}var et=function(t){function e(n,i){var r=t.call(this)||this;r.model=null,r.lastPointerType="",r.lastClickTimeStamp=0,r.contentWidthUpdateDelayer=new G["a"](50),r.isRefreshing=!1,r.refreshingPreviousChildrenIds={},r.currentDragAndDropData=null,r.currentDropTarget=null,r.currentDropTargets=null,r.currentDropDisposable=w["a"].None,r.gestureDisposable=w["a"].None,r.dragAndDropScrollInterval=null,r.dragAndDropScrollTimeout=null,r.dragAndDropMouseY=null,r.highlightedItemWasDraggable=!1,r.onHiddenScrollTop=null,r._onDOMFocus=new x["a"],r.onDOMFocus=r._onDOMFocus.event,r._onDOMBlur=new x["a"],r._onDidScroll=new x["a"],e.counter++,r.instance=e.counter;var o="undefined"===typeof n.options.horizontalScrollMode?2:n.options.horizontalScrollMode;r.horizontalScrolling=2!==o,r.context={dataSource:n.dataSource,renderer:n.renderer,controller:n.controller,dnd:n.dnd,filter:n.filter,sorter:n.sorter,tree:n.tree,accessibilityProvider:n.accessibilityProvider,options:n.options,cache:new J(n),horizontalScrolling:r.horizontalScrolling},r.modelListeners=[],r.viewListeners=[],r.items={},r.domNode=document.createElement("div"),r.domNode.className="monaco-tree no-focused-item monaco-tree-instance-"+r.instance,r.domNode.tabIndex=n.options.preventRootFocus?-1:0,r.styleElement=p["w"](r.domNode),r.treeStyler=n.styler||new b(r.styleElement,"monaco-tree-instance-"+r.instance),r.domNode.setAttribute("role","tree"),r.context.options.ariaLabel&&r.domNode.setAttribute("aria-label",r.context.options.ariaLabel),r.context.options.alwaysFocused&&p["f"](r.domNode,"focused"),r.context.options.paddingOnRow||p["f"](r.domNode,"no-row-padding"),r.wrapper=document.createElement("div"),r.wrapper.className="monaco-tree-wrapper",r.scrollableElement=new K["b"](r.wrapper,{alwaysConsumeMouseWheel:!0,horizontal:o,vertical:"undefined"!==typeof n.options.verticalScrollMode?n.options.verticalScrollMode:1,useShadows:n.options.useShadows}),r.scrollableElement.onScroll((function(t){r.render(t.scrollTop,t.height,t.scrollLeft,t.width,t.scrollWidth),r._onDidScroll.fire()})),P["i"]?(r.wrapper.style.msTouchAction="none",r.wrapper.style.msContentZooming="none"):r.gestureDisposable=F["b"].addTarget(r.wrapper),r.rowsContainer=document.createElement("div"),r.rowsContainer.className="monaco-tree-rows",n.options.showTwistie&&(r.rowsContainer.className+=" show-twisties");var s=p["Z"](r.domNode);return r.viewListeners.push(s.onDidFocus((function(){return r.onFocus()}))),r.viewListeners.push(s.onDidBlur((function(){return r.onBlur()}))),r.viewListeners.push(s),r.viewListeners.push(p["j"](r.domNode,"keydown",(function(t){return r.onKeyDown(t)}))),r.viewListeners.push(p["j"](r.domNode,"keyup",(function(t){return r.onKeyUp(t)}))),r.viewListeners.push(p["j"](r.domNode,"mousedown",(function(t){return r.onMouseDown(t)}))),r.viewListeners.push(p["j"](r.domNode,"mouseup",(function(t){return r.onMouseUp(t)}))),r.viewListeners.push(p["j"](r.wrapper,"auxclick",(function(t){t&&1===t.button&&r.onMouseMiddleClick(t)}))),r.viewListeners.push(p["j"](r.wrapper,"click",(function(t){return r.onClick(t)}))),r.viewListeners.push(p["j"](r.domNode,"contextmenu",(function(t){return r.onContextMenu(t)}))),r.viewListeners.push(p["j"](r.wrapper,F["a"].Tap,(function(t){return r.onTap(t)}))),r.viewListeners.push(p["j"](r.wrapper,F["a"].Change,(function(t){return r.onTouchChange(t)}))),P["i"]&&(r.viewListeners.push(p["j"](r.wrapper,"MSPointerDown",(function(t){return r.onMsPointerDown(t)}))),r.viewListeners.push(p["j"](r.wrapper,"MSGestureTap",(function(t){return r.onMsGestureTap(t)}))),r.viewListeners.push(p["m"](r.wrapper,"MSGestureChange",(function(t){return r.onThrottledMsGestureChange(t)}),(function(t,e){e.stopPropagation(),e.preventDefault();var n={translationY:e.translationY,translationX:e.translationX};return t&&(n.translationY+=t.translationY,n.translationX+=t.translationX),n})))),r.viewListeners.push(p["j"](window,"dragover",(function(t){return r.onDragOver(t)}))),r.viewListeners.push(p["j"](r.wrapper,"drop",(function(t){return r.onDrop(t)}))),r.viewListeners.push(p["j"](window,"dragend",(function(t){return r.onDragEnd(t)}))),r.viewListeners.push(p["j"](window,"dragleave",(function(t){return r.onDragOver(t)}))),r.wrapper.appendChild(r.rowsContainer),r.domNode.appendChild(r.scrollableElement.getDomNode()),i.appendChild(r.domNode),r.lastRenderTop=0,r.lastRenderHeight=0,r.didJustPressContextMenuKey=!1,r.currentDropTarget=null,r.currentDropTargets=[],r.shouldInvalidateDropReaction=!1,r.dragAndDropScrollInterval=null,r.dragAndDropScrollTimeout=null,r.onRowsChanged(),r.layout(),r.setupMSGesture(),r.applyStyles(n.options),r}return Z(e,t),e.prototype.applyStyles=function(t){this.treeStyler.style(t)},e.prototype.createViewItem=function(t){return new X(this.context,t)},e.prototype.getHTMLElement=function(){return this.domNode},e.prototype.focus=function(){this.domNode.focus()},e.prototype.isFocused=function(){return document.activeElement===this.domNode},e.prototype.blur=function(){this.domNode.blur()},e.prototype.setupMSGesture=function(){var t=this;window.MSGesture&&(this.msGesture=new MSGesture,setTimeout((function(){return t.msGesture.target=t.wrapper}),100))},e.prototype.isTreeVisible=function(){return null===this.onHiddenScrollTop},e.prototype.layout=function(t,e){this.isTreeVisible()&&(this.viewHeight=t||p["A"](this.wrapper),this.scrollHeight=this.getContentHeight(),this.horizontalScrolling&&(this.viewWidth=e||p["B"](this.wrapper)))},e.prototype.render=function(t,e,n,i,r){var o,s,a=t,h=t+e,u=this.lastRenderTop+this.lastRenderHeight;for(o=this.indexAfter(h)-1,s=this.indexAt(Math.max(u,a));o>=s;o--)this.insertItemInDOM(this.itemAtIndex(o));for(o=Math.min(this.indexAt(this.lastRenderTop),this.indexAfter(h))-1,s=this.indexAt(a);o>=s;o--)this.insertItemInDOM(this.itemAtIndex(o));for(o=this.indexAt(this.lastRenderTop),s=Math.min(this.indexAt(a),this.indexAfter(u));o<s;o++)this.removeItemFromDOM(this.itemAtIndex(o));for(o=Math.max(this.indexAfter(h),this.indexAt(this.lastRenderTop)),s=this.indexAfter(u);o<s;o++)this.removeItemFromDOM(this.itemAtIndex(o));var l=this.itemAtIndex(this.indexAt(a));l&&(this.rowsContainer.style.top=l.top-a+"px"),this.horizontalScrolling&&(this.rowsContainer.style.left=-n+"px",this.rowsContainer.style.width=Math.max(r,i)+"px"),this.lastRenderTop=a,this.lastRenderHeight=h-a},e.prototype.setModel=function(t){this.releaseModel(),this.model=t,this.model.onRefresh(this.onRefreshing,this,this.modelListeners),this.model.onDidRefresh(this.onRefreshed,this,this.modelListeners),this.model.onSetInput(this.onClearingInput,this,this.modelListeners),this.model.onDidSetInput(this.onSetInput,this,this.modelListeners),this.model.onDidFocus(this.onModelFocusChange,this,this.modelListeners),this.model.onRefreshItemChildren(this.onItemChildrenRefreshing,this,this.modelListeners),this.model.onDidRefreshItemChildren(this.onItemChildrenRefreshed,this,this.modelListeners),this.model.onDidRefreshItem(this.onItemRefresh,this,this.modelListeners),this.model.onExpandItem(this.onItemExpanding,this,this.modelListeners),this.model.onDidExpandItem(this.onItemExpanded,this,this.modelListeners),this.model.onCollapseItem(this.onItemCollapsing,this,this.modelListeners),this.model.onDidRevealItem(this.onItemReveal,this,this.modelListeners),this.model.onDidAddTraitItem(this.onItemAddTrait,this,this.modelListeners),this.model.onDidRemoveTraitItem(this.onItemRemoveTrait,this,this.modelListeners)},e.prototype.onRefreshing=function(){this.isRefreshing=!0},e.prototype.onRefreshed=function(){this.isRefreshing=!1,this.onRowsChanged()},e.prototype.onRowsChanged=function(t){void 0===t&&(t=this.scrollTop),this.isRefreshing||(this.scrollTop=t,this.updateScrollWidth())},e.prototype.updateScrollWidth=function(){var t=this;this.horizontalScrolling&&this.contentWidthUpdateDelayer.trigger((function(){for(var e=Object.keys(t.items),n=0,i=0,r=e;i<r.length;i++){var o=r[i];n=Math.max(n,t.items[o].width)}t.scrollWidth=n+10}))},e.prototype.focusNextPage=function(t){var e=this,n=this.indexAt(this.scrollTop+this.viewHeight);n=0===n?0:n-1;var i=this.itemAtIndex(n).model.getElement(),r=this.model.getFocus();if(r!==i)this.model.setFocus(i,t);else{var o=this.scrollTop;this.scrollTop+=this.viewHeight,this.scrollTop!==o&&setTimeout((function(){e.focusNextPage(t)}),0)}},e.prototype.focusPreviousPage=function(t){var e,n=this;e=0===this.scrollTop?this.indexAt(this.scrollTop):this.indexAfter(this.scrollTop-1);var i=this.itemAtIndex(e).model.getElement(),r=this.model.getFocus();if(r!==i)this.model.setFocus(i,t);else{var o=this.scrollTop;this.scrollTop-=this.viewHeight,this.scrollTop!==o&&setTimeout((function(){n.focusPreviousPage(t)}),0)}},Object.defineProperty(e.prototype,"viewHeight",{get:function(){var t=this.scrollableElement.getScrollDimensions();return t.height},set:function(t){this.scrollableElement.setScrollDimensions({height:t})},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"scrollHeight",{set:function(t){t+=this.horizontalScrolling?10:0,this.scrollableElement.setScrollDimensions({scrollHeight:t})},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"viewWidth",{get:function(){var t=this.scrollableElement.getScrollDimensions();return t.width},set:function(t){this.scrollableElement.setScrollDimensions({width:t})},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"scrollWidth",{set:function(t){this.scrollableElement.setScrollDimensions({scrollWidth:t})},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"scrollTop",{get:function(){var t=this.scrollableElement.getScrollPosition();return t.scrollTop},set:function(t){var e=this.getContentHeight()+(this.horizontalScrolling?10:0);this.scrollableElement.setScrollDimensions({scrollHeight:e}),this.scrollableElement.setScrollPosition({scrollTop:t})},enumerable:!0,configurable:!0}),e.prototype.onClearingInput=function(t){var e=t.item;e&&(this.onRemoveItems(new H["e"](e.getNavigator(),(function(t){return t&&t.id}))),this.onRowsChanged())},e.prototype.onSetInput=function(t){this.context.cache.garbageCollect(),this.inputItem=new Q(this.context,t.item,this.wrapper)},e.prototype.onItemChildrenRefreshing=function(t){var n=t.item,i=this.items[n.id];if(i&&this.context.options.showLoading&&(i.loadingTimer=setTimeout((function(){i.loadingTimer=0,i.loading=!0}),e.LOADING_DECORATION_DELAY)),!t.isNested){var r=[],o=n.getNavigator(),s=void 0;while(s=o.next())r.push(s.id);this.refreshingPreviousChildrenIds[n.id]=r}},e.prototype.onItemChildrenRefreshed=function(t){var e=this,n=t.item,i=this.items[n.id];if(i&&(i.loadingTimer&&(clearTimeout(i.loadingTimer),i.loadingTimer=0),i.loading=!1),!t.isNested){var r=this.refreshingPreviousChildrenIds[n.id],o=[],s=n.getNavigator(),a=void 0;while(a=s.next())o.push(a);var h=Math.abs(r.length-o.length)>1e3,u=[],l=!1;if(!h){var c=new O["a"]({getElements:function(){return r}},{getElements:function(){return o.map((function(t){return t.id}))}},null);u=c.ComputeDiff(!1).changes,l=u.some((function(t){if(t.modifiedLength>0)for(var n=t.modifiedStart,i=t.modifiedStart+t.modifiedLength;n<i;n++)if(e.items.hasOwnProperty(o[n].id))return!0;return!1}))}if(!h&&!l&&u.length<50)for(var p=0,d=u;p<d.length;p++){var f=d[p];if(f.originalLength>0&&this.onRemoveItems(new H["a"](r,f.originalStart,f.originalStart+f.originalLength)),f.modifiedLength>0){var g=o[f.modifiedStart-1]||n;g=g.getDepth()>0?g:null,this.onInsertItems(new H["a"](o,f.modifiedStart,f.modifiedStart+f.modifiedLength),g?g.id:null)}}else(h||u.length)&&(this.onRemoveItems(new H["a"](r)),this.onInsertItems(new H["a"](o),n.getDepth()>0?n.id:null));(h||u.length)&&this.onRowsChanged()}},e.prototype.onItemRefresh=function(t){this.onItemsRefresh([t])},e.prototype.onItemsRefresh=function(t){var e=this;this.onRefreshItemSet(t.filter((function(t){return e.items.hasOwnProperty(t.id)}))),this.onRowsChanged()},e.prototype.onItemExpanding=function(t){var e=this.items[t.item.id];e&&(e.expanded=!0)},e.prototype.onItemExpanded=function(t){var e=t.item,n=this.items[e.id];if(n){n.expanded=!0;var i=this.onInsertItems(e.getNavigator(),e.id)||0,r=this.scrollTop;n.top+n.height<=this.scrollTop&&(r+=i),this.onRowsChanged(r)}},e.prototype.onItemCollapsing=function(t){var e=t.item,n=this.items[e.id];n&&(n.expanded=!1,this.onRemoveItems(new H["e"](e.getNavigator(),(function(t){return t&&t.id}))),this.onRowsChanged())},e.prototype.onItemReveal=function(t){var e=t.item,n=t.relativeTop,i=this.items[e.id];if(i)if(null!==n){n=n<0?0:n,n=n>1?1:n;var r=i.height-this.viewHeight;this.scrollTop=r*n+i.top}else{var o=i.top+i.height,s=this.scrollTop+this.viewHeight;i.top<this.scrollTop?this.scrollTop=i.top:o>=s&&(this.scrollTop=o-this.viewHeight)}},e.prototype.onItemAddTrait=function(t){var e=t.item,n=t.trait,i=this.items[e.id];i&&i.addClass(n),"highlighted"===n&&(p["f"](this.domNode,n),i&&(this.highlightedItemWasDraggable=!!i.draggable,i.draggable&&(i.draggable=!1)))},e.prototype.onItemRemoveTrait=function(t){var e=t.item,n=t.trait,i=this.items[e.id];i&&i.removeClass(n),"highlighted"===n&&(p["P"](this.domNode,n),this.highlightedItemWasDraggable&&(i.draggable=!0),this.highlightedItemWasDraggable=!1)},e.prototype.onModelFocusChange=function(){var t=this.model&&this.model.getFocus();p["Y"](this.domNode,"no-focused-item",!t),t?this.domNode.setAttribute("aria-activedescendant",R["L"](this.context.dataSource.getId(this.context.tree,t))):this.domNode.removeAttribute("aria-activedescendant")},e.prototype.onInsertItem=function(t){var e=this;t.onDragStart=function(n){e.onDragStart(t,n)},t.needsRender=!0,this.refreshViewItem(t),this.items[t.id]=t},e.prototype.onRefreshItem=function(t,e){void 0===e&&(e=!1),t.needsRender=t.needsRender||e,this.refreshViewItem(t)},e.prototype.onRemoveItem=function(t){this.removeItemFromDOM(t),t.dispose(),delete this.items[t.id]},e.prototype.refreshViewItem=function(t){t.render(),this.shouldBeRendered(t)?this.insertItemInDOM(t):this.removeItemFromDOM(t)},e.prototype.onClick=function(t){if(!this.lastPointerType||"mouse"===this.lastPointerType){var e=new L["b"](t),n=this.getItemAround(e.target);n&&(P["i"]&&Date.now()-this.lastClickTimeStamp<300&&(e.detail=2),this.lastClickTimeStamp=Date.now(),this.context.controller.onClick(this.context.tree,n.model.getElement(),e))}},e.prototype.onMouseMiddleClick=function(t){if(this.context.controller.onMouseMiddleClick){var e=new L["b"](t),n=this.getItemAround(e.target);n&&this.context.controller.onMouseMiddleClick(this.context.tree,n.model.getElement(),e)}},e.prototype.onMouseDown=function(t){if(this.didJustPressContextMenuKey=!1,this.context.controller.onMouseDown&&(!this.lastPointerType||"mouse"===this.lastPointerType)){var e=new L["b"](t);if(!(e.ctrlKey&&r["f"]&&r["e"])){var n=this.getItemAround(e.target);n&&this.context.controller.onMouseDown(this.context.tree,n.model.getElement(),e)}}},e.prototype.onMouseUp=function(t){if(this.context.controller.onMouseUp&&(!this.lastPointerType||"mouse"===this.lastPointerType)){var e=new L["b"](t);if(!(e.ctrlKey&&r["f"]&&r["e"])){var n=this.getItemAround(e.target);n&&this.context.controller.onMouseUp(this.context.tree,n.model.getElement(),e)}}},e.prototype.onTap=function(t){var e=this.getItemAround(t.initialTarget);e&&this.context.controller.onTap(this.context.tree,e.model.getElement(),t)},e.prototype.onTouchChange=function(t){t.preventDefault(),t.stopPropagation(),this.scrollTop-=t.translationY},e.prototype.onContextMenu=function(t){var e,n;if(t instanceof KeyboardEvent||this.didJustPressContextMenuKey){this.didJustPressContextMenuKey=!1;var i=new M["a"](t);n=this.model.getFocus();var r=void 0;if(n){var o=this.context.dataSource.getId(this.context.tree,n),s=this.items[o];r=p["C"](s.element)}else n=this.model.getInput(),r=p["C"](this.inputItem.element);e=new V(r.left+r.width,r.top,i)}else{var a=new L["b"](t),h=this.getItemAround(a.target);if(!h)return;n=h.model.getElement(),e=new W(a)}this.context.controller.onContextMenu(this.context.tree,n,e)},e.prototype.onKeyDown=function(t){var e=new M["a"](t);this.didJustPressContextMenuKey=58===e.keyCode||e.shiftKey&&68===e.keyCode,e.target&&e.target.tagName&&"input"===e.target.tagName.toLowerCase()||(this.didJustPressContextMenuKey&&(e.preventDefault(),e.stopPropagation()),this.context.controller.onKeyDown(this.context.tree,e))},e.prototype.onKeyUp=function(t){this.didJustPressContextMenuKey&&this.onContextMenu(t),this.didJustPressContextMenuKey=!1,this.context.controller.onKeyUp(this.context.tree,new M["a"](t))},e.prototype.onDragStart=function(t,e){if(!this.model.getHighlight()){var n,i=t.model.getElement(),r=this.model.getSelection();if(n=r.indexOf(i)>-1?r:[i],e.dataTransfer.effectAllowed="copyMove",e.dataTransfer.setData($["a"].RESOURCES,JSON.stringify([t.uri])),e.dataTransfer.setDragImage){var o=void 0;o=this.context.dnd.getDragLabel?this.context.dnd.getDragLabel(this.context.tree,n):String(n.length);var s=document.createElement("div");s.className="monaco-tree-drag-image",s.textContent=o,document.body.appendChild(s),e.dataTransfer.setDragImage(s,-10,-10),setTimeout((function(){return document.body.removeChild(s)}),0)}this.currentDragAndDropData=new j(n),$["c"].CurrentDragAndDropData=new B(n),this.context.dnd.onDragStart(this.context.tree,this.currentDragAndDropData,new L["a"](e))}},e.prototype.setupDragAndDropScrollInterval=function(){var t=this,e=p["F"](this.wrapper).top;this.dragAndDropScrollInterval||(this.dragAndDropScrollInterval=window.setInterval((function(){if(null!==t.dragAndDropMouseY){var n=t.dragAndDropMouseY-e,i=0,r=t.viewHeight-35;n<35?i=Math.max(-14,.2*(n-35)):n>r&&(i=Math.min(14,.2*(n-r))),t.scrollTop+=i}}),10),this.cancelDragAndDropScrollTimeout(),this.dragAndDropScrollTimeout=window.setTimeout((function(){t.cancelDragAndDropScrollInterval(),t.dragAndDropScrollTimeout=null}),1e3))},e.prototype.cancelDragAndDropScrollInterval=function(){this.dragAndDropScrollInterval&&(window.clearInterval(this.dragAndDropScrollInterval),this.dragAndDropScrollInterval=null),this.cancelDragAndDropScrollTimeout()},e.prototype.cancelDragAndDropScrollTimeout=function(){this.dragAndDropScrollTimeout&&(window.clearTimeout(this.dragAndDropScrollTimeout),this.dragAndDropScrollTimeout=null)},e.prototype.onDragOver=function(t){var e=this;t.preventDefault();var n,i=new L["a"](t),r=this.getItemAround(i.target);if(!r||0===i.posx&&0===i.posy&&i.browserEvent.type===p["d"].DRAG_LEAVE)return this.currentDropTarget&&(this.currentDropTargets.forEach((function(t){return t.dropTarget=!1})),this.currentDropTargets=[],this.currentDropDisposable.dispose()),this.cancelDragAndDropScrollInterval(),this.currentDropTarget=null,this.currentDropElement=null,this.dragAndDropMouseY=null,!1;if(this.setupDragAndDropScrollInterval(),this.dragAndDropMouseY=i.posy,!this.currentDragAndDropData)if($["c"].CurrentDragAndDropData)this.currentDragAndDropData=$["c"].CurrentDragAndDropData;else{if(!i.dataTransfer.types)return!1;this.currentDragAndDropData=new N}this.currentDragAndDropData.update(i.browserEvent.dataTransfer);var o,s=r.model;do{if(n=s?s.getElement():this.model.getInput(),o=this.context.dnd.onDragOver(this.context.tree,this.currentDragAndDropData,n,i),!o||1!==o.bubble)break;s=s&&s.parent}while(s);if(!s)return this.currentDropElement=null,!1;var a=o&&o.accept;a?(this.currentDropElement=s.getElement(),i.preventDefault(),i.dataTransfer.dropEffect=0===o.effect?"copy":"move"):this.currentDropElement=null;var h=s.id===this.inputItem.id?this.inputItem:this.items[s.id];if((this.shouldInvalidateDropReaction||this.currentDropTarget!==h||!tt(this.currentDropElementReaction,o))&&(this.shouldInvalidateDropReaction=!1,this.currentDropTarget&&(this.currentDropTargets.forEach((function(t){return t.dropTarget=!1})),this.currentDropTargets=[],this.currentDropDisposable.dispose()),this.currentDropTarget=h,this.currentDropElementReaction=o,a)){if(this.currentDropTarget&&(this.currentDropTarget.dropTarget=!0,this.currentDropTargets.push(this.currentDropTarget)),0===o.bubble){var u=s.getNavigator(),l=void 0;while(l=u.next())r=this.items[l.id],r&&(r.dropTarget=!0,this.currentDropTargets.push(r))}if(o.autoExpand){var c=Object(G["l"])(500);this.currentDropDisposable=w["h"]((function(){return c.cancel()})),c.then((function(){return e.context.tree.expand(e.currentDropElement)})).then((function(){return e.shouldInvalidateDropReaction=!0}))}}return!0},e.prototype.onDrop=function(t){if(this.currentDropElement){var e=new L["a"](t);e.preventDefault(),this.currentDragAndDropData.update(e.browserEvent.dataTransfer),this.context.dnd.drop(this.context.tree,this.currentDragAndDropData,this.currentDropElement,e),this.onDragEnd(t)}this.cancelDragAndDropScrollInterval()},e.prototype.onDragEnd=function(t){this.currentDropTarget&&(this.currentDropTargets.forEach((function(t){return t.dropTarget=!1})),this.currentDropTargets=[]),this.currentDropDisposable.dispose(),this.cancelDragAndDropScrollInterval(),this.currentDragAndDropData=null,$["c"].CurrentDragAndDropData=void 0,this.currentDropElement=null,this.currentDropTarget=null,this.dragAndDropMouseY=null},e.prototype.onFocus=function(){this.context.options.alwaysFocused||p["f"](this.domNode,"focused"),this._onDOMFocus.fire()},e.prototype.onBlur=function(){this.context.options.alwaysFocused||p["P"](this.domNode,"focused"),this.domNode.removeAttribute("aria-activedescendant"),this._onDOMBlur.fire()},e.prototype.onMsPointerDown=function(t){if(this.msGesture){var e=t.pointerType;e!==(t.MSPOINTER_TYPE_MOUSE||"mouse")?e===(t.MSPOINTER_TYPE_TOUCH||"touch")&&(this.lastPointerType="touch",t.stopPropagation(),t.preventDefault(),this.msGesture.addPointer(t.pointerId)):this.lastPointerType="mouse"}},e.prototype.onThrottledMsGestureChange=function(t){this.scrollTop-=t.translationY},e.prototype.onMsGestureTap=function(t){t.initialTarget=document.elementFromPoint(t.clientX,t.clientY),this.onTap(t)},e.prototype.insertItemInDOM=function(t){var e=null,n=this.itemAfter(t);n&&n.element&&(e=n.element),t.insertInDOM(this.rowsContainer,e)},e.prototype.removeItemFromDOM=function(t){t&&t.removeFromDOM()},e.prototype.shouldBeRendered=function(t){return t.top<this.lastRenderTop+this.lastRenderHeight&&t.top+t.height>this.lastRenderTop},e.prototype.getItemAround=function(t){var n=this.inputItem,i=t;do{if(i[e.BINDING]&&(n=i[e.BINDING]),i===this.wrapper||i===this.domNode)return n;if(i===this.scrollableElement.getDomNode()||i===document.body)return}while(i=i.parentElement)},e.prototype.releaseModel=function(){this.model&&(this.modelListeners=w["f"](this.modelListeners),this.model=null)},e.prototype.dispose=function(){var e=this;this.scrollableElement.dispose(),this.releaseModel(),this.viewListeners=w["f"](this.viewListeners),this._onDOMFocus.dispose(),this._onDOMBlur.dispose(),this.domNode.parentNode&&this.domNode.parentNode.removeChild(this.domNode),this.items&&Object.keys(this.items).forEach((function(t){return e.items[t].removeFromDOM()})),this.context.cache&&this.context.cache.dispose(),this.gestureDisposable.dispose(),t.prototype.dispose.call(this)},e.BINDING="monaco-tree-row",e.LOADING_DECORATION_DELAY=800,e.counter=0,e}(q),nt=n("ceb8"),it=n("aa3d"),rt=function(){function t(t,e,n){if(void 0===n&&(n={}),this.tree=t,this.configuration=e,this.options=n,!e.dataSource)throw new Error("You must provide a Data Source to the tree.");this.dataSource=e.dataSource,this.renderer=e.renderer,this.controller=e.controller||new g({clickBehavior:1,keyboardSupport:"boolean"!==typeof n.keyboardSupport||n.keyboardSupport}),this.dnd=e.dnd||new m,this.filter=e.filter||new y,this.sorter=e.sorter,this.accessibilityProvider=e.accessibilityProvider||new v,this.styler=e.styler}return t}(),ot={listFocusBackground:nt["a"].fromHex("#073655"),listActiveSelectionBackground:nt["a"].fromHex("#0E639C"),listActiveSelectionForeground:nt["a"].fromHex("#FFFFFF"),listFocusAndSelectionBackground:nt["a"].fromHex("#094771"),listFocusAndSelectionForeground:nt["a"].fromHex("#FFFFFF"),listInactiveSelectionBackground:nt["a"].fromHex("#3F3F46"),listHoverBackground:nt["a"].fromHex("#2A2D2E"),listDropBackground:nt["a"].fromHex("#383B3D")},st=function(){function t(t,e,n){void 0===n&&(n={}),this._onDidChangeFocus=new x["f"],this.onDidChangeFocus=this._onDidChangeFocus.event,this._onDidChangeSelection=new x["f"],this.onDidChangeSelection=this._onDidChangeSelection.event,this._onHighlightChange=new x["f"],this._onDidExpandItem=new x["f"],this._onDidCollapseItem=new x["f"],this._onDispose=new x["a"],this.onDidDispose=this._onDispose.event,this.container=t,Object(it["g"])(n,ot,!1),n.twistiePixels="number"===typeof n.twistiePixels?n.twistiePixels:32,n.showTwistie=!1!==n.showTwistie,n.indentPixels="number"===typeof n.indentPixels?n.indentPixels:12,n.alwaysFocused=!0===n.alwaysFocused,n.useShadows=!1!==n.useShadows,n.paddingOnRow=!1!==n.paddingOnRow,n.showLoading=!1!==n.showLoading,this.context=new rt(this,e,n),this.model=new T(this.context),this.view=new et(this.context,this.container),this.view.setModel(this.model),this._onDidChangeFocus.input=this.model.onDidFocus,this._onDidChangeSelection.input=this.model.onDidSelect,this._onHighlightChange.input=this.model.onDidHighlight,this._onDidExpandItem.input=this.model.onDidExpandItem,this._onDidCollapseItem.input=this.model.onDidCollapseItem}return t.prototype.style=function(t){this.view.applyStyles(t)},Object.defineProperty(t.prototype,"onDidFocus",{get:function(){return this.view.onDOMFocus},enumerable:!0,configurable:!0}),t.prototype.getHTMLElement=function(){return this.view.getHTMLElement()},t.prototype.layout=function(t,e){this.view.layout(t,e)},t.prototype.domFocus=function(){this.view.focus()},t.prototype.isDOMFocused=function(){return this.view.isFocused()},t.prototype.domBlur=function(){this.view.blur()},t.prototype.setInput=function(t){return this.model.setInput(t)},t.prototype.getInput=function(){return this.model.getInput()},t.prototype.expand=function(t){return this.model.expand(t)},t.prototype.collapse=function(t,e){return void 0===e&&(e=!1),this.model.collapse(t,e)},t.prototype.toggleExpansion=function(t,e){return void 0===e&&(e=!1),this.model.toggleExpansion(t,e)},t.prototype.isExpanded=function(t){return this.model.isExpanded(t)},t.prototype.reveal=function(t,e){return void 0===e&&(e=null),this.model.reveal(t,e)},t.prototype.getHighlight=function(){return this.model.getHighlight()},t.prototype.clearHighlight=function(t){this.model.setHighlight(null,t)},t.prototype.setSelection=function(t,e){this.model.setSelection(t,e)},t.prototype.getSelection=function(){return this.model.getSelection()},t.prototype.clearSelection=function(t){this.model.setSelection([],t)},t.prototype.setFocus=function(t,e){this.model.setFocus(t,e)},t.prototype.getFocus=function(){return this.model.getFocus()},t.prototype.focusNext=function(t,e){this.model.focusNext(t,e)},t.prototype.focusPrevious=function(t,e){this.model.focusPrevious(t,e)},t.prototype.focusParent=function(t){this.model.focusParent(t)},t.prototype.focusFirstChild=function(t){this.model.focusFirstChild(t)},t.prototype.focusFirst=function(t,e){this.model.focusFirst(t,e)},t.prototype.focusNth=function(t,e){this.model.focusNth(t,e)},t.prototype.focusLast=function(t,e){this.model.focusLast(t,e)},t.prototype.focusNextPage=function(t){this.view.focusNextPage(t)},t.prototype.focusPreviousPage=function(t){this.view.focusPreviousPage(t)},t.prototype.clearFocus=function(t){this.model.setFocus(null,t)},t.prototype.dispose=function(){this._onDispose.fire(),this.model.dispose(),this.view.dispose(),this._onDidChangeFocus.dispose(),this._onDidChangeSelection.dispose(),this._onHighlightChange.dispose(),this._onDidExpandItem.dispose(),this._onDidCollapseItem.dispose(),this._onDispose.dispose()},t}(),at=n("3be3"),ht=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},t(e,n)};return function(e,n){function i(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),ut=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return ht(e,t),e.prototype.onContextMenu=function(e,n,i){return r["e"]?this.onLeftClick(e,n,i):t.prototype.onContextMenu.call(this,e,n,i)},e}(g),lt={background:nt["a"].fromHex("#1E1E1E"),foreground:nt["a"].fromHex("#CCCCCC"),pickerGroupForeground:nt["a"].fromHex("#0097FB"),pickerGroupBorder:nt["a"].fromHex("#3F3F46"),widgetShadow:nt["a"].fromHex("#000000"),progressBarBackground:nt["a"].fromHex("#0E70C0")},ct=i["a"]("quickOpenAriaLabel","Quick picker. Type to narrow down results."),pt=function(t){function e(e,n,i){var r=t.call(this)||this;return r.isDisposed=!1,r.container=e,r.callbacks=n,r.options=i,r.styles=i||Object.create(null),Object(it["g"])(r.styles,lt,!1),r.model=null,r}return ht(e,t),e.prototype.getModel=function(){return this.model},e.prototype.create=function(){var t=this;this.element=document.createElement("div"),p["f"](this.element,"monaco-quick-open-widget"),this.container.appendChild(this.element),this._register(p["j"](this.element,p["d"].CONTEXT_MENU,(function(t){return p["c"].stop(t,!0)}))),this._register(p["j"](this.element,p["d"].FOCUS,(function(e){return t.gainingFocus()}),!0)),this._register(p["j"](this.element,p["d"].BLUR,(function(e){return t.loosingFocus(e)}),!0)),this._register(p["j"](this.element,p["d"].KEY_DOWN,(function(e){var n=new M["a"](e);if(9===n.keyCode)p["c"].stop(e,!0),t.hide(2);else if(2===n.keyCode&&!n.altKey&&!n.ctrlKey&&!n.metaKey){var i=e.currentTarget.querySelectorAll("input, .monaco-tree, .monaco-tree-row.focused .action-label.icon");n.shiftKey&&n.target===i[0]?(p["c"].stop(e,!0),i[i.length-1].focus()):n.shiftKey||n.target!==i[i.length-1]||(p["c"].stop(e,!0),i[0].focus())}}))),this.progressBar=this._register(new at["a"](this.element,{progressBarBackground:this.styles.progressBarBackground})),this.progressBar.hide(),this.inputContainer=document.createElement("div"),p["f"](this.inputContainer,"quick-open-input"),this.element.appendChild(this.inputContainer),this.inputBox=this._register(new l["b"](this.inputContainer,void 0,{placeholder:this.options.inputPlaceHolder||"",ariaLabel:ct,inputBackground:this.styles.inputBackground,inputForeground:this.styles.inputForeground,inputBorder:this.styles.inputBorder,inputValidationInfoBackground:this.styles.inputValidationInfoBackground,inputValidationInfoForeground:this.styles.inputValidationInfoForeground,inputValidationInfoBorder:this.styles.inputValidationInfoBorder,inputValidationWarningBackground:this.styles.inputValidationWarningBackground,inputValidationWarningForeground:this.styles.inputValidationWarningForeground,inputValidationWarningBorder:this.styles.inputValidationWarningBorder,inputValidationErrorBackground:this.styles.inputValidationErrorBackground,inputValidationErrorForeground:this.styles.inputValidationErrorForeground,inputValidationErrorBorder:this.styles.inputValidationErrorBorder})),this.inputElement=this.inputBox.inputElement,this.inputElement.setAttribute("role","combobox"),this.inputElement.setAttribute("aria-haspopup","false"),this.inputElement.setAttribute("aria-autocomplete","list"),this._register(p["j"](this.inputBox.inputElement,p["d"].INPUT,(function(e){return t.onType()}))),this._register(p["j"](this.inputBox.inputElement,p["d"].KEY_DOWN,(function(e){var n=new M["a"](e),i=t.shouldOpenInBackground(n);if(2!==n.keyCode)if(18===n.keyCode||16===n.keyCode||12===n.keyCode||11===n.keyCode)p["c"].stop(e,!0),t.navigateInTree(n.keyCode,n.shiftKey),t.inputBox.inputElement.selectionStart===t.inputBox.inputElement.selectionEnd&&(t.inputBox.inputElement.selectionStart=t.inputBox.value.length);else if(3===n.keyCode||i){p["c"].stop(e,!0);var r=t.tree.getFocus();r&&t.elementSelected(r,e,i?2:1)}}))),this.resultCount=document.createElement("div"),p["f"](this.resultCount,"quick-open-result-count"),this.resultCount.setAttribute("aria-live","polite"),this.resultCount.setAttribute("aria-atomic","true"),this.element.appendChild(this.resultCount),this.treeContainer=document.createElement("div"),p["f"](this.treeContainer,"quick-open-tree"),this.element.appendChild(this.treeContainer);var e=this.options.treeCreator||function(t,e,n){return new st(t,e,n)};return this.tree=this._register(e(this.treeContainer,{dataSource:new s(this),controller:new ut({clickBehavior:1,keyboardSupport:this.options.keyboardSupport}),renderer:this.renderer=new u(this,this.styles),filter:new h(this),accessibilityProvider:new a(this)},{twistiePixels:11,indentPixels:0,alwaysFocused:!0,verticalScrollMode:3,horizontalScrollMode:2,ariaLabel:i["a"]("treeAriaLabel","Quick Picker"),keyboardSupport:this.options.keyboardSupport,preventRootFocus:!1})),this.treeElement=this.tree.getHTMLElement(),this._register(this.tree.onDidChangeFocus((function(e){t.elementFocused(e.focus,e)}))),this._register(this.tree.onDidChangeSelection((function(e){if(e.selection&&e.selection.length>0){var n=e.payload&&e.payload.originalEvent instanceof L["b"]?e.payload.originalEvent:void 0,i=!!n&&t.shouldOpenInBackground(n);t.elementSelected(e.selection[0],e,i?2:1)}}))),this._register(p["j"](this.treeContainer,p["d"].KEY_DOWN,(function(e){var n=new M["a"](e);if(t.quickNavigateConfiguration)if(18===n.keyCode||16===n.keyCode||12===n.keyCode||11===n.keyCode)p["c"].stop(e,!0),t.navigateInTree(n.keyCode);else if(3===n.keyCode){p["c"].stop(e,!0);var i=t.tree.getFocus();i&&t.elementSelected(i,e)}}))),this._register(p["j"](this.treeContainer,p["d"].KEY_UP,(function(e){var n=new M["a"](e),i=n.keyCode;if(t.quickNavigateConfiguration){var r=t.quickNavigateConfiguration.keybindings,o=r.some((function(t){var e=t.getParts(),r=e[0],o=e[1];return!o&&(r.shiftKey&&4===i?!(n.ctrlKey||n.altKey||n.metaKey):!(!r.altKey||6!==i)||(!(!r.ctrlKey||5!==i)||!(!r.metaKey||57!==i)))}));if(o){var s=t.tree.getFocus();s&&t.elementSelected(s,e)}}}))),this.layoutDimensions&&this.layout(this.layoutDimensions),this.applyStyles(),this._register(p["j"](this.treeContainer,p["d"].KEY_DOWN,(function(e){var n=new M["a"](e);t.quickNavigateConfiguration||18!==n.keyCode&&16!==n.keyCode&&12!==n.keyCode&&11!==n.keyCode||(p["c"].stop(e,!0),t.navigateInTree(n.keyCode,n.shiftKey),t.treeElement.focus())}))),this.element},e.prototype.style=function(t){this.styles=t,this.applyStyles()},e.prototype.applyStyles=function(){if(this.element){var t=this.styles.foreground?this.styles.foreground.toString():"",e=this.styles.background?this.styles.background.toString():"",n=this.styles.borderColor?this.styles.borderColor.toString():"",i=this.styles.widgetShadow?this.styles.widgetShadow.toString():"";this.element.style.color=t,this.element.style.backgroundColor=e,this.element.style.borderColor=n,this.element.style.borderWidth=n?"1px":"",this.element.style.borderStyle=n?"solid":"",this.element.style.boxShadow=i?"0 5px 8px "+i:""}this.progressBar&&this.progressBar.style({progressBarBackground:this.styles.progressBarBackground}),this.inputBox&&this.inputBox.style({inputBackground:this.styles.inputBackground,inputForeground:this.styles.inputForeground,inputBorder:this.styles.inputBorder,inputValidationInfoBackground:this.styles.inputValidationInfoBackground,inputValidationInfoForeground:this.styles.inputValidationInfoForeground,inputValidationInfoBorder:this.styles.inputValidationInfoBorder,inputValidationWarningBackground:this.styles.inputValidationWarningBackground,inputValidationWarningForeground:this.styles.inputValidationWarningForeground,inputValidationWarningBorder:this.styles.inputValidationWarningBorder,inputValidationErrorBackground:this.styles.inputValidationErrorBackground,inputValidationErrorForeground:this.styles.inputValidationErrorForeground,inputValidationErrorBorder:this.styles.inputValidationErrorBorder}),this.tree&&!this.options.treeCreator&&this.tree.style(this.styles),this.renderer&&this.renderer.updateStyles(this.styles)},e.prototype.shouldOpenInBackground=function(t){if(t instanceof M["a"]){if(17!==t.keyCode)return!1;if(t.metaKey||t.ctrlKey||t.shiftKey||t.altKey)return!1;var e=this.inputBox.inputElement;return e.selectionEnd===this.inputBox.value.length&&e.selectionStart===e.selectionEnd}return t.middleButton},e.prototype.onType=function(){var t=this.inputBox.value;this.helpText&&(t?p["J"](this.helpText):p["X"](this.helpText)),this.callbacks.onType(t)},e.prototype.navigateInTree=function(t,e){var n=this.tree.getInput(),i=n?n.entries:[],r=this.tree.getFocus();switch(t){case 18:this.tree.focusNext();break;case 16:this.tree.focusPrevious();break;case 12:this.tree.focusNextPage();break;case 11:this.tree.focusPreviousPage();break;case 2:e?this.tree.focusPrevious():this.tree.focusNext();break}var o=this.tree.getFocus();i.length>1&&r===o&&(16===t||2===t&&e?this.tree.focusLast():(18===t||2===t&&!e)&&this.tree.focusFirst()),o=this.tree.getFocus(),o&&this.tree.reveal(o)},e.prototype.elementFocused=function(t,e){if(t&&this.isVisible()){var n=this.treeElement.getAttribute("aria-activedescendant");n?this.inputElement.setAttribute("aria-activedescendant",n):this.inputElement.removeAttribute("aria-activedescendant");var i={event:e,keymods:this.extractKeyMods(e),quickNavigateConfiguration:this.quickNavigateConfiguration};this.model.runner.run(t,0,i)}},e.prototype.elementSelected=function(t,e,n){var i=!0;if(this.isVisible()){var r=n||1,o={event:e,keymods:this.extractKeyMods(e),quickNavigateConfiguration:this.quickNavigateConfiguration};i=this.model.runner.run(t,r,o)}i&&this.hide(0)},e.prototype.extractKeyMods=function(t){return{ctrlCmd:t&&(t.ctrlKey||t.metaKey||t.payload&&t.payload.originalEvent&&(t.payload.originalEvent.ctrlKey||t.payload.originalEvent.metaKey)),alt:t&&(t.altKey||t.payload&&t.payload.originalEvent&&t.payload.originalEvent.altKey)}},e.prototype.show=function(t,e){this.visible=!0,this.isLoosingFocus=!1,this.quickNavigateConfiguration=e?e.quickNavigateConfiguration:void 0,this.quickNavigateConfiguration?(p["J"](this.inputContainer),p["X"](this.element),this.tree.domFocus()):(p["X"](this.inputContainer),p["X"](this.element),this.inputBox.focus()),this.helpText&&(this.quickNavigateConfiguration||o["j"](t)?p["J"](this.helpText):p["X"](this.helpText)),o["j"](t)?this.doShowWithPrefix(t):(e&&e.value&&this.restoreLastInput(e.value),this.doShowWithInput(t,e&&e.autoFocus?e.autoFocus:{})),e&&e.inputSelection&&!this.quickNavigateConfiguration&&this.inputBox.select(e.inputSelection),this.callbacks.onShow&&this.callbacks.onShow()},e.prototype.restoreLastInput=function(t){this.inputBox.value=t,this.inputBox.select(),this.callbacks.onType(t)},e.prototype.doShowWithPrefix=function(t){this.inputBox.value=t,this.callbacks.onType(t)},e.prototype.doShowWithInput=function(t,e){this.setInput(t,e)},e.prototype.setInputAndLayout=function(t,e){var n=this;this.treeContainer.style.height=this.getHeight(t)+"px",this.tree.setInput(null).then((function(){return n.model=t,n.inputElement.setAttribute("aria-haspopup",String(t&&t.entries&&t.entries.length>0)),n.tree.setInput(t)})).then((function(){n.tree.layout();var i=t?t.entries.filter((function(e){return n.isElementVisible(t,e)})):[];n.updateResultCount(i.length),i.length&&n.autoFocus(t,i,e)}))},e.prototype.isElementVisible=function(t,e){return!t.filter||t.filter.isVisible(e)},e.prototype.autoFocus=function(t,e,n){if(void 0===n&&(n={}),n.autoFocusPrefixMatch){for(var i=void 0,r=void 0,o=n.autoFocusPrefixMatch,s=o.toLowerCase(),a=0,h=e;a<h.length;a++){var u=h[a],l=t.dataSource.getLabel(u)||"";if(i||0!==l.indexOf(o)?r||0!==l.toLowerCase().indexOf(s)||(r=u):i=u,i&&r)break}var c=i||r;if(c)return this.tree.setFocus(c),void this.tree.reveal(c,.5)}n.autoFocusFirstEntry?(this.tree.focusFirst(),this.tree.reveal(this.tree.getFocus())):"number"===typeof n.autoFocusIndex?e.length>n.autoFocusIndex&&(this.tree.focusNth(n.autoFocusIndex),this.tree.reveal(this.tree.getFocus())):n.autoFocusSecondEntry?e.length>1&&this.tree.focusNth(1):n.autoFocusLastEntry&&e.length>1&&(this.tree.focusLast(),this.tree.reveal(this.tree.getFocus()))},e.prototype.getHeight=function(t){var n=this,i=t.renderer;if(!t){var r=i.getHeight(null);return this.options.minItemsToShow?this.options.minItemsToShow*r:0}var o,s=0;this.layoutDimensions&&this.layoutDimensions.height&&(o=.4*(this.layoutDimensions.height-50)),(!o||o>e.MAX_ITEMS_HEIGHT)&&(o=e.MAX_ITEMS_HEIGHT);for(var a=t.entries.filter((function(e){return n.isElementVisible(t,e)})),h=this.options.maxItemsToShow||a.length,u=0;u<h&&u<a.length;u++){var l=i.getHeight(a[u]);if(!(s+l<=o))break;s+=l}return s},e.prototype.updateResultCount=function(t){this.resultCount.textContent=i["a"]({key:"quickInput.visibleCount",comment:["This tells the user how many items are shown in a list of items to select from. The items can be anything. Currently not visible, but read by screen readers."]},"{0} Results",t)},e.prototype.hide=function(t){this.isVisible()&&(this.visible=!1,p["J"](this.element),this.element.blur(),this.inputBox.value="",this.tree.setInput(null),this.inputElement.setAttribute("aria-haspopup","false"),this.treeContainer.style.height=(this.options.minItemsToShow?22*this.options.minItemsToShow:0)+"px",this.progressBar.stop().hide(),this.tree.isDOMFocused()?this.tree.domBlur():this.inputBox.hasFocus()&&this.inputBox.blur(),0===t?this.callbacks.onOk():this.callbacks.onCancel(),this.callbacks.onHide&&this.callbacks.onHide(t))},e.prototype.setInput=function(t,e,n){this.isVisible()&&(this.getInput()&&this.onInputChanging(),this.setInputAndLayout(t,e),this.inputBox&&this.inputBox.setAriaLabel(n||ct))},e.prototype.onInputChanging=function(){var t=this;this.inputChangingTimeoutHandle&&(clearTimeout(this.inputChangingTimeoutHandle),this.inputChangingTimeoutHandle=null),p["f"](this.element,"content-changing"),this.inputChangingTimeoutHandle=setTimeout((function(){p["P"](t.element,"content-changing")}),500)},e.prototype.getInput=function(){return this.tree.getInput()},e.prototype.isVisible=function(){return this.visible},e.prototype.layout=function(t){this.layoutDimensions=t;var n=Math.min(.62*this.layoutDimensions.width,e.MAX_WIDTH);this.element&&(this.element.style.width=n+"px",this.element.style.marginLeft="-"+n/2+"px",this.inputContainer.style.width=n-12+"px")},e.prototype.gainingFocus=function(){this.isLoosingFocus=!1},e.prototype.loosingFocus=function(t){var e=this;if(this.isVisible()){var n=t.relatedTarget;!this.quickNavigateConfiguration&&p["K"](n,this.element)||(this.isLoosingFocus=!0,setTimeout((function(){if(e.isLoosingFocus&&!e.isDisposed){var t=e.callbacks.onFocusLost&&e.callbacks.onFocusLost();t||e.hide(1)}}),0))}},e.prototype.dispose=function(){t.prototype.dispose.call(this),this.isDisposed=!0},e.MAX_WIDTH=600,e.MAX_ITEMS_HEIGHT=440,e}(w["a"])},"8b4e":function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"a",(function(){return s}));var i=n("dff7"),r=function(){function t(t,e,n){void 0===n&&(n=e),this.modifierLabels=[null],this.modifierLabels[2]=t,this.modifierLabels[1]=e,this.modifierLabels[3]=n}return t.prototype.toLabel=function(t,e,n){if(0===e.length)return null;for(var i=[],r=0,o=e.length;r<o;r++){var s=e[r],h=n(s);if(null===h)return null;i[r]=a(s,h,this.modifierLabels[t])}return i.join(" ")},t}(),o=new r({ctrlKey:"⌃",shiftKey:"⇧",altKey:"⌥",metaKey:"⌘",separator:""},{ctrlKey:i["a"]({key:"ctrlKey",comment:["This is the short form for the Control key on the keyboard"]},"Ctrl"),shiftKey:i["a"]({key:"shiftKey",comment:["This is the short form for the Shift key on the keyboard"]},"Shift"),altKey:i["a"]({key:"altKey",comment:["This is the short form for the Alt key on the keyboard"]},"Alt"),metaKey:i["a"]({key:"windowsKey",comment:["This is the short form for the Windows key on the keyboard"]},"Windows"),separator:"+"},{ctrlKey:i["a"]({key:"ctrlKey",comment:["This is the short form for the Control key on the keyboard"]},"Ctrl"),shiftKey:i["a"]({key:"shiftKey",comment:["This is the short form for the Shift key on the keyboard"]},"Shift"),altKey:i["a"]({key:"altKey",comment:["This is the short form for the Alt key on the keyboard"]},"Alt"),metaKey:i["a"]({key:"superKey",comment:["This is the short form for the Super key on the keyboard"]},"Super"),separator:"+"}),s=new r({ctrlKey:i["a"]({key:"ctrlKey.long",comment:["This is the long form for the Control key on the keyboard"]},"Control"),shiftKey:i["a"]({key:"shiftKey.long",comment:["This is the long form for the Shift key on the keyboard"]},"Shift"),altKey:i["a"]({key:"altKey.long",comment:["This is the long form for the Alt key on the keyboard"]},"Alt"),metaKey:i["a"]({key:"cmdKey.long",comment:["This is the long form for the Command key on the keyboard"]},"Command"),separator:"+"},{ctrlKey:i["a"]({key:"ctrlKey.long",comment:["This is the long form for the Control key on the keyboard"]},"Control"),shiftKey:i["a"]({key:"shiftKey.long",comment:["This is the long form for the Shift key on the keyboard"]},"Shift"),altKey:i["a"]({key:"altKey.long",comment:["This is the long form for the Alt key on the keyboard"]},"Alt"),metaKey:i["a"]({key:"windowsKey.long",comment:["This is the long form for the Windows key on the keyboard"]},"Windows"),separator:"+"},{ctrlKey:i["a"]({key:"ctrlKey.long",comment:["This is the long form for the Control key on the keyboard"]},"Control"),shiftKey:i["a"]({key:"shiftKey.long",comment:["This is the long form for the Shift key on the keyboard"]},"Shift"),altKey:i["a"]({key:"altKey.long",comment:["This is the long form for the Alt key on the keyboard"]},"Alt"),metaKey:i["a"]({key:"superKey.long",comment:["This is the long form for the Super key on the keyboard"]},"Super"),separator:"+"});function a(t,e,n){if(null===e)return"";var i=[];return t.ctrlKey&&i.push(n.ctrlKey),t.shiftKey&&i.push(n.shiftKey),t.altKey&&i.push(n.altKey),t.metaKey&&i.push(n.metaKey),i.push(e),i.join(n.separator)}},"9ee1":function(t,e,n){"use strict";var i;n.d(e,"a",(function(){return i})),function(t){function e(t,e){if(t.start>=e.end||e.start>=t.end)return{start:0,end:0};var n=Math.max(t.start,e.start),i=Math.min(t.end,e.end);return i-n<=0?{start:0,end:0}:{start:n,end:i}}function n(t){return t.end-t.start<=0}function i(t,i){return!n(e(t,i))}function r(t,e){var i=[],r={start:t.start,end:Math.min(e.start,t.end)},o={start:Math.max(e.end,t.start),end:t.end};return n(r)||i.push(r),n(o)||i.push(o),i}t.intersect=e,t.isEmpty=n,t.intersects=i,t.relativeComplement=r}(i||(i={}))},a666:function(t,e,n){"use strict";n.d(e,"g",(function(){return a})),n.d(e,"f",(function(){return h})),n.d(e,"e",(function(){return u})),n.d(e,"h",(function(){return l})),n.d(e,"b",(function(){return c})),n.d(e,"a",(function(){return p})),n.d(e,"d",(function(){return d})),n.d(e,"c",(function(){return f}));var i=!1,r="__is_disposable_tracked__";function o(t){if(i&&t&&t!==p.None)try{t[r]=!0}catch(e){}}function s(t){if(!i)return t;var e=new Error("Potentially leaked disposable").stack;return setTimeout((function(){t[r]||console.log(e)}),3e3),t}function a(t){return"function"===typeof t.dispose&&0===t.dispose.length}function h(t){return Array.isArray(t)?(t.forEach((function(t){t&&(o(t),t.dispose())})),[]):t?(o(t),t.dispose(),t):void 0}function u(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return t.forEach(o),s({dispose:function(){return h(t)}})}function l(t){var e=s({dispose:function(){o(e),t()}});return e}var c=function(){function t(){this._toDispose=new Set,this._isDisposed=!1}return t.prototype.dispose=function(){this._isDisposed||(o(this),this._isDisposed=!0,this.clear())},t.prototype.clear=function(){this._toDispose.forEach((function(t){return t.dispose()})),this._toDispose.clear()},t.prototype.add=function(t){if(!t)return t;if(t===this)throw new Error("Cannot register a disposable on itself!");return o(t),this._isDisposed?console.warn(new Error("Trying to add a disposable to a DisposableStore that has already been disposed of. The added object will be leaked!").stack):this._toDispose.add(t),t},t}(),p=function(){function t(){this._store=new c,s(this)}return t.prototype.dispose=function(){o(this),this._store.dispose()},t.prototype._register=function(t){if(t===this)throw new Error("Cannot register a disposable on itself!");return this._store.add(t)},t.None=Object.freeze({dispose:function(){}}),t}(),d=function(){function t(){this._isDisposed=!1,s(this)}return Object.defineProperty(t.prototype,"value",{get:function(){return this._isDisposed?void 0:this._value},set:function(t){this._isDisposed||t===this._value||(this._value&&this._value.dispose(),t&&o(t),this._value=t)},enumerable:!0,configurable:!0}),t.prototype.clear=function(){this.value=void 0},t.prototype.dispose=function(){this._isDisposed=!0,o(this),this._value&&this._value.dispose(),this._value=void 0},t}(),f=function(){function t(t){this.object=t}return t.prototype.dispose=function(){},t}()},aa3d:function(t,e,n){"use strict";n.d(e,"c",(function(){return r})),n.d(e,"d",(function(){return o})),n.d(e,"b",(function(){return a})),n.d(e,"g",(function(){return u})),n.d(e,"a",(function(){return l})),n.d(e,"e",(function(){return c})),n.d(e,"f",(function(){return p}));var i=n("ef8e");function r(t){if(!t||"object"!==typeof t)return t;if(t instanceof RegExp)return t;var e=Array.isArray(t)?[]:{};return Object.keys(t).forEach((function(n){t[n]&&"object"===typeof t[n]?e[n]=r(t[n]):e[n]=t[n]})),e}function o(t){if(!t||"object"!==typeof t)return t;var e=[t];while(e.length>0){var n=e.shift();for(var i in Object.freeze(n),n)if(s.call(n,i)){var r=n[i];"object"!==typeof r||Object.isFrozen(r)||e.push(r)}}return t}var s=Object.prototype.hasOwnProperty;function a(t,e){return h(t,e,new Set)}function h(t,e,n){if(Object(i["l"])(t))return t;var r=e(t);if("undefined"!==typeof r)return r;if(Object(i["d"])(t)){for(var o=[],a=0,u=t;a<u.length;a++){var l=u[a];o.push(h(l,e,n))}return o}if(Object(i["i"])(t)){if(n.has(t))throw new Error("Cannot clone recursive data-structure");n.add(t);var c={};for(var p in t)s.call(t,p)&&(c[p]=h(t[p],e,n));return n.delete(t),c}return t}function u(t,e,n){return void 0===n&&(n=!0),Object(i["i"])(t)?(Object(i["i"])(e)&&Object.keys(e).forEach((function(r){r in t?n&&(Object(i["i"])(t[r])&&Object(i["i"])(e[r])?u(t[r],e[r],n):t[r]=e[r]):t[r]=e[r]})),t):e}function l(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return e.forEach((function(e){return Object.keys(e).forEach((function(n){return t[n]=e[n]}))})),t}function c(t,e){if(t===e)return!0;if(null===t||void 0===t||null===e||void 0===e)return!1;if(typeof t!==typeof e)return!1;if("object"!==typeof t)return!1;if(Array.isArray(t)!==Array.isArray(e))return!1;var n,i;if(Array.isArray(t)){if(t.length!==e.length)return!1;for(n=0;n<t.length;n++)if(!c(t[n],e[n]))return!1}else{var r=[];for(i in t)r.push(i);r.sort();var o=[];for(i in e)o.push(i);if(o.sort(),!c(r,o))return!1;for(n=0;n<r.length;n++)if(!c(t[r[n]],e[r[n]]))return!1}return!0}function p(t,e,n){var i=e(t);return"undefined"===typeof i?n:i}},b589:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"a",(function(){return a}));var i,r=n("6d8e"),o=n("30db");(function(t){t.inMemory="inmemory",t.vscode="vscode",t.internal="private",t.walkThrough="walkThrough",t.walkThroughSnippet="walkThroughSnippet",t.http="http",t.https="https",t.file="file",t.mailto="mailto",t.untitled="untitled",t.data="data",t.command="command",t.vscodeRemote="vscode-remote",t.vscodeRemoteResource="vscode-remote-resource",t.userData="vscode-userdata"})(i||(i={}));var s=function(){function t(){this._hosts=Object.create(null),this._ports=Object.create(null),this._connectionTokens=Object.create(null),this._preferredWebSchema="http",this._delegate=null}return t.prototype.setPreferredWebSchema=function(t){this._preferredWebSchema=t},t.prototype.rewrite=function(t){if(this._delegate)return this._delegate(t);var e=t.authority,n=this._hosts[e];n&&-1!==n.indexOf(":")&&(n="["+n+"]");var s=this._ports[e],a=this._connectionTokens[e],h="path="+encodeURIComponent(t.path);return"string"===typeof a&&(h+="&tkn="+encodeURIComponent(a)),r["a"].from({scheme:o["g"]?this._preferredWebSchema:i.vscodeRemoteResource,authority:n+":"+s,path:"/vscode-remote-resource",query:h})},t}(),a=new s},b9b4:function(t,e,n){"use strict";n.d(e,"b",(function(){return d})),n.d(e,"a",(function(){return g}));var i=n("32b8"),r=n("3742"),o=n("9768"),s=n("b589"),a=n("82c9"),h="text/plain",u="application/unknown",l=[],c=[],p=[];function d(t,e){void 0===e&&(e=!1);var n=f(t);l.push(n),n.userConfigured?p.push(n):c.push(n),e&&!n.userConfigured&&l.forEach((function(t){t.mime===n.mime||t.userConfigured||(n.extension&&t.extension===n.extension&&console.warn("Overwriting extension <<"+n.extension+">> to now point to mime <<"+n.mime+">>"),n.filename&&t.filename===n.filename&&console.warn("Overwriting filename <<"+n.filename+">> to now point to mime <<"+n.mime+">>"),n.filepattern&&t.filepattern===n.filepattern&&console.warn("Overwriting filepattern <<"+n.filepattern+">> to now point to mime <<"+n.mime+">>"),n.firstline&&t.firstline===n.firstline&&console.warn("Overwriting firstline <<"+n.firstline+">> to now point to mime <<"+n.mime+">>"))}))}function f(t){return{id:t.id,mime:t.mime,filename:t.filename,extension:t.extension,filepattern:t.filepattern,firstline:t.firstline,userConfigured:t.userConfigured,filenameLowercase:t.filename?t.filename.toLowerCase():void 0,extensionLowercase:t.extension?t.extension.toLowerCase():void 0,filepatternLowercase:t.filepattern?t.filepattern.toLowerCase():void 0,filepatternOnPath:!!t.filepattern&&t.filepattern.indexOf(i["posix"].sep)>=0}}function g(t,e){var n;if(t)switch(t.scheme){case s["b"].file:n=t.fsPath;break;case s["b"].data:var r=a["a"].parseMetaData(t);n=r.get(a["a"].META_DATA_LABEL);break;default:n=t.path}if(!n)return[u];n=n.toLowerCase();var o=Object(i["basename"])(n),l=m(n,o,p);if(l)return[l,h];var d=m(n,o,c);if(d)return[d,h];if(e){var f=y(e);if(f)return[f,h]}return[u]}function m(t,e,n){for(var i=null,s=null,a=null,h=n.length-1;h>=0;h--){var u=n[h];if(e===u.filenameLowercase){i=u;break}if(u.filepattern&&(!s||u.filepattern.length>s.filepattern.length)){var l=u.filepatternOnPath?t:e;Object(o["a"])(u.filepatternLowercase,l)&&(s=u)}u.extension&&(!a||u.extension.length>a.extension.length)&&Object(r["m"])(e,u.extensionLowercase)&&(a=u)}return i?i.mime:s?s.mime:a?a.mime:null}function y(t){if(Object(r["P"])(t)&&(t=t.substr(1)),t.length>0)for(var e=l.length-1;e>=0;e--){var n=l[e];if(n.firstline){var i=t.match(n.firstline);if(i&&i.length>0)return n.mime}}return null}},bcc1:function(t,e,n){},c317:function(t,e,n){"use strict";(function(t){n.d(e,"a",(function(){return o})),n.d(e,"b",(function(){return s})),n.d(e,"c",(function(){return a}));var i=n("30db"),r="undefined"===typeof t?{cwd:function(){return"/"},env:Object.create(null),get platform(){return i["h"]?"win32":i["e"]?"darwin":"linux"},nextTick:function(t){return Object(i["i"])(t)}}:t,o=r.cwd,s=r.env,a=r.platform}).call(this,n("4362"))},c531:function(t,e,n){"use strict";n.d(e,"a",(function(){return h}));var i=n("30db"),r=n("1b1f");function o(t,e){if(i["b"].MonacoEnvironment){if("function"===typeof i["b"].MonacoEnvironment.getWorker)return i["b"].MonacoEnvironment.getWorker(t,e);if("function"===typeof i["b"].MonacoEnvironment.getWorkerUrl)return new Worker(i["b"].MonacoEnvironment.getWorkerUrl(t,e))}throw new Error("You must define a function MonacoEnvironment.getWorkerUrl or MonacoEnvironment.getWorker")}function s(t){return"function"===typeof t.then}var a=function(){function t(t,e,n,i,r){this.id=e;var a=o("workerMain.js",n);s(a)?this.worker=a:this.worker=Promise.resolve(a),this.postMessage(t,[]),this.worker.then((function(t){t.onmessage=function(t){i(t.data)},t.onmessageerror=r,"function"===typeof t.addEventListener&&t.addEventListener("error",r)}))}return t.prototype.getId=function(){return this.id},t.prototype.postMessage=function(t,e){this.worker&&this.worker.then((function(n){return n.postMessage(t,e)}))},t.prototype.dispose=function(){this.worker&&this.worker.then((function(t){return t.terminate()})),this.worker=null},t}(),h=function(){function t(t){this._label=t,this._webWorkerFailedBeforeError=!1}return t.prototype.create=function(e,n,i){var o=this,s=++t.LAST_WORKER_ID;if(this._webWorkerFailedBeforeError)throw this._webWorkerFailedBeforeError;return new a(e,s,this._label||"anonymous"+s,n,(function(t){Object(r["b"])(t),o._webWorkerFailedBeforeError=t,i(t)}))},t.LAST_WORKER_ID=0,t}()},db88:function(t,e,n){"use strict";n.d(e,"a",(function(){return o}));var i=n("258a"),r=function(){function t(e){this.element=e,this.next=t.Undefined,this.prev=t.Undefined}return t.Undefined=new t(void 0),t}(),o=function(){function t(){this._first=r.Undefined,this._last=r.Undefined,this._size=0}return Object.defineProperty(t.prototype,"size",{get:function(){return this._size},enumerable:!0,configurable:!0}),t.prototype.isEmpty=function(){return this._first===r.Undefined},t.prototype.clear=function(){this._first=r.Undefined,this._last=r.Undefined,this._size=0},t.prototype.unshift=function(t){return this._insert(t,!1)},t.prototype.push=function(t){return this._insert(t,!0)},t.prototype._insert=function(t,e){var n=this,i=new r(t);if(this._first===r.Undefined)this._first=i,this._last=i;else if(e){var o=this._last;this._last=i,i.prev=o,o.next=i}else{var s=this._first;this._first=i,i.next=s,s.prev=i}this._size+=1;var a=!1;return function(){a||(a=!0,n._remove(i))}},t.prototype.shift=function(){if(this._first!==r.Undefined){var t=this._first.element;return this._remove(this._first),t}},t.prototype.pop=function(){if(this._last!==r.Undefined){var t=this._last.element;return this._remove(this._last),t}},t.prototype._remove=function(t){if(t.prev!==r.Undefined&&t.next!==r.Undefined){var e=t.prev;e.next=t.next,t.next.prev=e}else t.prev===r.Undefined&&t.next===r.Undefined?(this._first=r.Undefined,this._last=r.Undefined):t.next===r.Undefined?(this._last=this._last.prev,this._last.next=r.Undefined):t.prev===r.Undefined&&(this._first=this._first.next,this._first.prev=r.Undefined);this._size-=1},t.prototype.iterator=function(){var t,e=this._first;return{next:function(){return e===r.Undefined?i["c"]:(t?t.value=e.element:t={done:!1,value:e.element},e=e.next,t)}}},t.prototype.toArray=function(){for(var t=[],e=this._first;e!==r.Undefined;e=e.next)t.push(e.element);return t},t}()},debc:function(t,e,n){"use strict";n.d(e,"b",(function(){return u})),n.d(e,"a",(function(){return l})),n.d(e,"c",(function(){return p}));var i=n("6d8e"),r=n("32b8"),o=n("3742"),s=n("b589"),a=n("30db"),h=n("82c9");function u(t,e,n){if("string"===typeof t&&(t=i["a"].file(t)),n){var o=n.getWorkspaceFolder(t);if(o){var u=n.getWorkspace().folders.length>1,l=void 0;if(l=Object(h["e"])(o.uri,t)?"":Object(h["h"])(o.uri,t),u){var d=o.name?o.name:Object(h["b"])(o.uri);l=l?d+" • "+l:d}return l}}if(t.scheme!==s["b"].file&&t.scheme!==s["b"].untitled)return t.with({query:null,fragment:null}).toString(!0);if(c(t.fsPath))return Object(r["normalize"])(p(t.fsPath));var g=Object(r["normalize"])(t.fsPath);return!a["h"]&&e&&(g=f(g,e.userHome)),g}function l(t){if(t){"string"===typeof t&&(t=i["a"].file(t));var e=Object(h["b"])(t)||(t.scheme===s["b"].file?t.fsPath:t.path);return c(e)?p(e):e}}function c(t){return!(!a["h"]||!t||":"!==t[1])}function p(t){return c(t)?t.charAt(0).toUpperCase()+t.slice(1):t}var d=Object.create(null);function f(t,e){if(a["h"]||!t||!e)return t;var n=d.original===e?d.normalized:void 0;return n||(n=""+Object(o["K"])(e,r["posix"].sep)+r["posix"].sep,d={original:e,normalized:n}),(a["d"]?Object(o["N"])(t,n):Object(o["O"])(t,n))&&(t="~/"+t.substr(n.length)),t}},e58e:function(t,e,n){"use strict";n.d(e,"a",(function(){return o}));var i=n("30db"),r=i["b"].performance&&"function"===typeof i["b"].performance.now,o=function(){function t(t){this._highResolution=r&&t,this._startTime=this._now(),this._stopTime=-1}return t.create=function(e){return void 0===e&&(e=!0),new t(e)},t.prototype.stop=function(){this._stopTime=this._now()},t.prototype.elapsed=function(){return-1!==this._stopTime?this._stopTime-this._startTime:this._now()-this._startTime},t.prototype._now=function(){return this._highResolution?i["b"].performance.now():(new Date).getTime()},t}()},ef8e:function(t,e,n){"use strict";n.d(e,"d",(function(){return r})),n.d(e,"j",(function(){return o})),n.d(e,"i",(function(){return s})),n.d(e,"h",(function(){return a})),n.d(e,"e",(function(){return h})),n.d(e,"k",(function(){return u})),n.d(e,"l",(function(){return l})),n.d(e,"a",(function(){return c})),n.d(e,"f",(function(){return d})),n.d(e,"g",(function(){return f})),n.d(e,"m",(function(){return g})),n.d(e,"c",(function(){return v})),n.d(e,"b",(function(){return b})),n.d(e,"n",(function(){return _})),n.d(e,"o",(function(){return w}));var i={number:"number",string:"string",undefined:"undefined",object:"object",function:"function"};function r(t){return Array.isArray?Array.isArray(t):!(!t||typeof t.length!==i.number||t.constructor!==Array)}function o(t){return typeof t===i.string||t instanceof String}function s(t){return typeof t===i.object&&null!==t&&!Array.isArray(t)&&!(t instanceof RegExp)&&!(t instanceof Date)}function a(t){return(typeof t===i.number||t instanceof Number)&&!isNaN(t)}function h(t){return!0===t||!1===t}function u(t){return typeof t===i.undefined}function l(t){return u(t)||null===t}function c(t,e){if(!t)throw new Error(e?"Unexpected type, expected '"+e+"'":"Unexpected type")}var p=Object.prototype.hasOwnProperty;function d(t){if(!s(t))return!1;for(var e in t)if(p.call(t,e))return!1;return!0}function f(t){return typeof t===i.function}function g(t,e){for(var n=Math.min(t.length,e.length),i=0;i<n;i++)m(t[i],e[i])}function m(t,e){if(o(e)){if(typeof t!==e)throw new Error("argument does not match constraint: typeof "+e)}else if(f(e)){try{if(t instanceof e)return}catch(n){}if(!l(t)&&t.constructor===e)return;if(1===e.length&&!0===e.call(void 0,t))return;throw new Error("argument does not match one of these constraints: arg instanceof constraint, arg.constructor === constraint, nor constraint(arg) === true")}}function y(t){var e=[],n=Object.getPrototypeOf(t);while(Object.prototype!==n)e=e.concat(Object.getOwnPropertyNames(n)),n=Object.getPrototypeOf(n);return e}function v(t){for(var e=[],n=0,i=y(t);n<i.length;n++){var r=i[n];"function"===typeof t[r]&&e.push(r)}return e}function b(t,e){for(var n=function(t){return function(){var n=Array.prototype.slice.call(arguments,0);return e(t,n)}},i={},r=0,o=t;r<o.length;r++){var s=o[r];i[s]=n(s)}return i}function _(t){return null===t?void 0:t}function w(t){return"undefined"===typeof t?null:t}},f41d:function(t,e,n){"use strict";n.r(e);var i=n("fa12");Object(i["a"])({id:"css",extensions:[".css"],aliases:["CSS","css"],mimetypes:["text/css"],loader:function(){return n.e("chunk-2d21b84a").then(n.bind(null,"bfb2"))}})},fa12:function(t,e,n){"use strict";n.d(e,"a",(function(){return a}));var i="undefined"===typeof monaco?self.monaco:monaco,r={},o={},s=function(){function t(t){var e=this;this._languageId=t,this._loadingTriggered=!1,this._lazyLoadPromise=new Promise((function(t,n){e._lazyLoadPromiseResolve=t,e._lazyLoadPromiseReject=n}))}return t.getOrCreate=function(e){return o[e]||(o[e]=new t(e)),o[e]},t.prototype.whenLoaded=function(){return this._lazyLoadPromise},t.prototype.load=function(){var t=this;return this._loadingTriggered||(this._loadingTriggered=!0,r[this._languageId].loader().then((function(e){return t._lazyLoadPromiseResolve(e)}),(function(e){return t._lazyLoadPromiseReject(e)}))),this._lazyLoadPromise},t}();function a(t){var e=t.id;r[e]=t,i.languages.register(t);var n=s.getOrCreate(e);i.languages.setMonarchTokensProvider(e,n.whenLoaded().then((function(t){return t.language}))),i.languages.onLanguage(e,(function(){n.load().then((function(t){i.languages.setLanguageConfiguration(e,t.conf)}))}))}},fe45:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"a",(function(){return u})),n.d(e,"f",(function(){return l})),n.d(e,"e",(function(){return p})),n.d(e,"d",(function(){return f})),n.d(e,"c",(function(){return g}));var i,r=n("fdcc"),o=function(){function t(){this._keyCodeToStr=[],this._strToKeyCode=Object.create(null)}return t.prototype.define=function(t,e){this._keyCodeToStr[t]=e,this._strToKeyCode[e.toLowerCase()]=t},t.prototype.keyCodeToStr=function(t){return this._keyCodeToStr[t]},t.prototype.strToKeyCode=function(t){return this._strToKeyCode[t.toLowerCase()]||0},t}(),s=new o,a=new o,h=new o;function u(t,e){var n=(65535&e)<<16>>>0;return(t|n)>>>0}function l(t,e){if(0===t)return null;var n=(65535&t)>>>0,i=(**********&t)>>>16;return new d(0!==i?[c(n,e),c(i,e)]:[c(n,e)])}function c(t,e){var n=!!(2048&t),i=!!(256&t),r=2===e?i:n,o=!!(1024&t),s=!!(512&t),a=2===e?n:i,h=255&t;return new p(r,o,s,a,h)}(function(){function t(t,e,n,i){void 0===n&&(n=e),void 0===i&&(i=n),s.define(t,e),a.define(t,n),h.define(t,i)}t(0,"unknown"),t(1,"Backspace"),t(2,"Tab"),t(3,"Enter"),t(4,"Shift"),t(5,"Ctrl"),t(6,"Alt"),t(7,"PauseBreak"),t(8,"CapsLock"),t(9,"Escape"),t(10,"Space"),t(11,"PageUp"),t(12,"PageDown"),t(13,"End"),t(14,"Home"),t(15,"LeftArrow","Left"),t(16,"UpArrow","Up"),t(17,"RightArrow","Right"),t(18,"DownArrow","Down"),t(19,"Insert"),t(20,"Delete"),t(21,"0"),t(22,"1"),t(23,"2"),t(24,"3"),t(25,"4"),t(26,"5"),t(27,"6"),t(28,"7"),t(29,"8"),t(30,"9"),t(31,"A"),t(32,"B"),t(33,"C"),t(34,"D"),t(35,"E"),t(36,"F"),t(37,"G"),t(38,"H"),t(39,"I"),t(40,"J"),t(41,"K"),t(42,"L"),t(43,"M"),t(44,"N"),t(45,"O"),t(46,"P"),t(47,"Q"),t(48,"R"),t(49,"S"),t(50,"T"),t(51,"U"),t(52,"V"),t(53,"W"),t(54,"X"),t(55,"Y"),t(56,"Z"),t(57,"Meta"),t(58,"ContextMenu"),t(59,"F1"),t(60,"F2"),t(61,"F3"),t(62,"F4"),t(63,"F5"),t(64,"F6"),t(65,"F7"),t(66,"F8"),t(67,"F9"),t(68,"F10"),t(69,"F11"),t(70,"F12"),t(71,"F13"),t(72,"F14"),t(73,"F15"),t(74,"F16"),t(75,"F17"),t(76,"F18"),t(77,"F19"),t(78,"NumLock"),t(79,"ScrollLock"),t(80,";",";","OEM_1"),t(81,"=","=","OEM_PLUS"),t(82,",",",","OEM_COMMA"),t(83,"-","-","OEM_MINUS"),t(84,".",".","OEM_PERIOD"),t(85,"/","/","OEM_2"),t(86,"`","`","OEM_3"),t(110,"ABNT_C1"),t(111,"ABNT_C2"),t(87,"[","[","OEM_4"),t(88,"\\","\\","OEM_5"),t(89,"]","]","OEM_6"),t(90,"'","'","OEM_7"),t(91,"OEM_8"),t(92,"OEM_102"),t(93,"NumPad0"),t(94,"NumPad1"),t(95,"NumPad2"),t(96,"NumPad3"),t(97,"NumPad4"),t(98,"NumPad5"),t(99,"NumPad6"),t(100,"NumPad7"),t(101,"NumPad8"),t(102,"NumPad9"),t(103,"NumPad_Multiply"),t(104,"NumPad_Add"),t(105,"NumPad_Separator"),t(106,"NumPad_Subtract"),t(107,"NumPad_Decimal"),t(108,"NumPad_Divide")})(),function(t){function e(t){return s.keyCodeToStr(t)}function n(t){return s.strToKeyCode(t)}function i(t){return a.keyCodeToStr(t)}function r(t){return h.keyCodeToStr(t)}function o(t){return a.strToKeyCode(t)||h.strToKeyCode(t)}t.toString=e,t.fromString=n,t.toUserSettingsUS=i,t.toUserSettingsGeneral=r,t.fromUserSettings=o}(i||(i={}));var p=function(){function t(t,e,n,i,r){this.ctrlKey=t,this.shiftKey=e,this.altKey=n,this.metaKey=i,this.keyCode=r}return t.prototype.equals=function(t){return this.ctrlKey===t.ctrlKey&&this.shiftKey===t.shiftKey&&this.altKey===t.altKey&&this.metaKey===t.metaKey&&this.keyCode===t.keyCode},t.prototype.isModifierKey=function(){return 0===this.keyCode||5===this.keyCode||57===this.keyCode||6===this.keyCode||4===this.keyCode},t.prototype.toChord=function(){return new d([this])},t.prototype.isDuplicateModifierCase=function(){return this.ctrlKey&&5===this.keyCode||this.shiftKey&&4===this.keyCode||this.altKey&&6===this.keyCode||this.metaKey&&57===this.keyCode},t}(),d=function(){function t(t){if(0===t.length)throw Object(r["b"])("parts");this.parts=t}return t.prototype.equals=function(t){if(null===t)return!1;if(this.parts.length!==t.parts.length)return!1;for(var e=0;e<this.parts.length;e++)if(!this.parts[e].equals(t.parts[e]))return!1;return!0},t}(),f=function(){function t(t,e,n,i,r,o){this.ctrlKey=t,this.shiftKey=e,this.altKey=n,this.metaKey=i,this.keyLabel=r,this.keyAriaLabel=o}return t}(),g=function(){function t(){}return t}()}}]);