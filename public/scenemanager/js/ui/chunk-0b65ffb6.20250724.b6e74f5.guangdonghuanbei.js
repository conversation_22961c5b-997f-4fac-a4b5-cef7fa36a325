(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0b65ffb6"],{db4f:function(e,t,r){"use strict";r.r(t),r.d(t,"setupMode",(function(){return ds}));var n,i=12e4,o=function(){function e(e){var t=this;this._defaults=e,this._worker=null,this._idleCheckInterval=window.setInterval((function(){return t._checkIfIdle()}),3e4),this._lastUsedTime=0,this._configChangeListener=this._defaults.onDidChange((function(){return t._stopWorker()}))}return e.prototype._stopWorker=function(){this._worker&&(this._worker.dispose(),this._worker=null),this._client=null},e.prototype.dispose=function(){clearInterval(this._idleCheckInterval),this._configChangeListener.dispose(),this._stopWorker()},e.prototype._checkIfIdle=function(){if(this._worker){var e=Date.now()-this._lastUsedTime;e>i&&this._stopWorker()}},e.prototype._getClient=function(){return this._lastUsedTime=Date.now(),this._client||(this._worker=monaco.editor.createWebWorker({moduleId:"vs/language/css/cssWorker",label:this._defaults.languageId,createData:{languageSettings:this._defaults.diagnosticsOptions,languageId:this._defaults.languageId}}),this._client=this._worker.getProxy()),this._client},e.prototype.getLanguageServiceWorker=function(){for(var e,t=this,r=[],n=0;n<arguments.length;n++)r[n]=arguments[n];return this._getClient().then((function(t){e=t})).then((function(e){return t._worker.withSyncedResources(r)})).then((function(t){return e}))},e}();(function(e){e[e["Ident"]=0]="Ident",e[e["AtKeyword"]=1]="AtKeyword",e[e["String"]=2]="String",e[e["BadString"]=3]="BadString",e[e["UnquotedString"]=4]="UnquotedString",e[e["Hash"]=5]="Hash",e[e["Num"]=6]="Num",e[e["Percentage"]=7]="Percentage",e[e["Dimension"]=8]="Dimension",e[e["UnicodeRange"]=9]="UnicodeRange",e[e["CDO"]=10]="CDO",e[e["CDC"]=11]="CDC",e[e["Colon"]=12]="Colon",e[e["SemiColon"]=13]="SemiColon",e[e["CurlyL"]=14]="CurlyL",e[e["CurlyR"]=15]="CurlyR",e[e["ParenthesisL"]=16]="ParenthesisL",e[e["ParenthesisR"]=17]="ParenthesisR",e[e["BracketL"]=18]="BracketL",e[e["BracketR"]=19]="BracketR",e[e["Whitespace"]=20]="Whitespace",e[e["Includes"]=21]="Includes",e[e["Dashmatch"]=22]="Dashmatch",e[e["SubstringOperator"]=23]="SubstringOperator",e[e["PrefixOperator"]=24]="PrefixOperator",e[e["SuffixOperator"]=25]="SuffixOperator",e[e["Delim"]=26]="Delim",e[e["EMS"]=27]="EMS",e[e["EXS"]=28]="EXS",e[e["Length"]=29]="Length",e[e["Angle"]=30]="Angle",e[e["Time"]=31]="Time",e[e["Freq"]=32]="Freq",e[e["Exclamation"]=33]="Exclamation",e[e["Resolution"]=34]="Resolution",e[e["Comma"]=35]="Comma",e[e["Charset"]=36]="Charset",e[e["EscapedJavaScript"]=37]="EscapedJavaScript",e[e["BadEscapedJavaScript"]=38]="BadEscapedJavaScript",e[e["Comment"]=39]="Comment",e[e["SingleLineComment"]=40]="SingleLineComment",e[e["EOF"]=41]="EOF",e[e["CustomToken"]=42]="CustomToken"})(n||(n={}));var s=function(){function e(e){this.source=e,this.len=e.length,this.position=0}return e.prototype.substring=function(e,t){return void 0===t&&(t=this.position),this.source.substring(e,t)},e.prototype.eos=function(){return this.len<=this.position},e.prototype.pos=function(){return this.position},e.prototype.goBackTo=function(e){this.position=e},e.prototype.goBack=function(e){this.position-=e},e.prototype.advance=function(e){this.position+=e},e.prototype.nextChar=function(){return this.source.charCodeAt(this.position++)||0},e.prototype.peekChar=function(e){return void 0===e&&(e=0),this.source.charCodeAt(this.position+e)||0},e.prototype.lookbackChar=function(e){return void 0===e&&(e=0),this.source.charCodeAt(this.position-e)||0},e.prototype.advanceIfChar=function(e){return e===this.source.charCodeAt(this.position)&&(this.position++,!0)},e.prototype.advanceIfChars=function(e){if(this.position+e.length>this.source.length)return!1;for(var t=0;t<e.length;t++)if(this.source.charCodeAt(this.position+t)!==e[t])return!1;return this.advance(t),!0},e.prototype.advanceWhileChar=function(e){var t=this.position;while(this.position<this.len&&e(this.source.charCodeAt(this.position)))this.position++;return this.position-t},e}(),a="a".charCodeAt(0),c="f".charCodeAt(0),u="z".charCodeAt(0),h="A".charCodeAt(0),l="F".charCodeAt(0),p="Z".charCodeAt(0),f="0".charCodeAt(0),d="9".charCodeAt(0),m="~".charCodeAt(0),g="^".charCodeAt(0),y="=".charCodeAt(0),v="|".charCodeAt(0),b="-".charCodeAt(0),k="_".charCodeAt(0),x="%".charCodeAt(0),C="*".charCodeAt(0),_="(".charCodeAt(0),w=")".charCodeAt(0),P="<".charCodeAt(0),S=">".charCodeAt(0),E="@".charCodeAt(0),I="#".charCodeAt(0),R="$".charCodeAt(0),A="\\".charCodeAt(0),D="/".charCodeAt(0),T="\n".charCodeAt(0),F="\r".charCodeAt(0),M="\f".charCodeAt(0),O='"'.charCodeAt(0),V="'".charCodeAt(0),L=" ".charCodeAt(0),N="\t".charCodeAt(0),$=";".charCodeAt(0),B=":".charCodeAt(0),U="{".charCodeAt(0),W="}".charCodeAt(0),j="[".charCodeAt(0),K="]".charCodeAt(0),q=",".charCodeAt(0),z=".".charCodeAt(0),H="!".charCodeAt(0),G={};G[$]=n.SemiColon,G[B]=n.Colon,G[U]=n.CurlyL,G[W]=n.CurlyR,G[K]=n.BracketR,G[j]=n.BracketL,G[_]=n.ParenthesisL,G[w]=n.ParenthesisR,G[q]=n.Comma;var Q={};Q["em"]=n.EMS,Q["ex"]=n.EXS,Q["px"]=n.Length,Q["cm"]=n.Length,Q["mm"]=n.Length,Q["in"]=n.Length,Q["pt"]=n.Length,Q["pc"]=n.Length,Q["deg"]=n.Angle,Q["rad"]=n.Angle,Q["grad"]=n.Angle,Q["ms"]=n.Time,Q["s"]=n.Time,Q["hz"]=n.Freq,Q["khz"]=n.Freq,Q["%"]=n.Percentage,Q["fr"]=n.Percentage,Q["dpi"]=n.Resolution,Q["dpcm"]=n.Resolution;var J,X,Z=function(){function e(){this.stream=new s(""),this.ignoreComment=!0,this.ignoreWhitespace=!0,this.inURL=!1}return e.prototype.setSource=function(e){this.stream=new s(e)},e.prototype.finishToken=function(e,t,r){return{offset:e,len:this.stream.pos()-e,type:t,text:r||this.stream.substring(e)}},e.prototype.substring=function(e,t){return this.stream.substring(e,e+t)},e.prototype.pos=function(){return this.stream.pos()},e.prototype.goBackTo=function(e){this.stream.goBackTo(e)},e.prototype.scanUnquotedString=function(){var e=this.stream.pos(),t=[];return this._unquotedString(t)?this.finishToken(e,n.UnquotedString,t.join("")):null},e.prototype.scan=function(){var e=this.trivia();if(null!==e)return e;var t=this.stream.pos();return this.stream.eos()?this.finishToken(t,n.EOF):this.scanNext(t)},e.prototype.scanNext=function(e){if(this.stream.advanceIfChars([P,H,b,b]))return this.finishToken(e,n.CDO);if(this.stream.advanceIfChars([b,b,S]))return this.finishToken(e,n.CDC);var t=[];if(this.ident(t))return this.finishToken(e,n.Ident,t.join(""));if(this.stream.advanceIfChar(E)){if(t=["@"],this._name(t)){var r=t.join("");return"@charset"===r?this.finishToken(e,n.Charset,r):this.finishToken(e,n.AtKeyword,r)}return this.finishToken(e,n.Delim)}if(this.stream.advanceIfChar(I))return t=["#"],this._name(t)?this.finishToken(e,n.Hash,t.join("")):this.finishToken(e,n.Delim);if(this.stream.advanceIfChar(H))return this.finishToken(e,n.Exclamation);if(this._number()){var i=this.stream.pos();if(t=[this.stream.substring(e,i)],this.stream.advanceIfChar(x))return this.finishToken(e,n.Percentage);if(this.ident(t)){var o=this.stream.substring(i).toLowerCase(),s=Q[o];return"undefined"!==typeof s?this.finishToken(e,s,t.join("")):this.finishToken(e,n.Dimension,t.join(""))}return this.finishToken(e,n.Num)}t=[];var a=this._string(t);return null!==a?this.finishToken(e,a,t.join("")):(a=G[this.stream.peekChar()],"undefined"!==typeof a?(this.stream.advance(1),this.finishToken(e,a)):this.stream.peekChar(0)===m&&this.stream.peekChar(1)===y?(this.stream.advance(2),this.finishToken(e,n.Includes)):this.stream.peekChar(0)===v&&this.stream.peekChar(1)===y?(this.stream.advance(2),this.finishToken(e,n.Dashmatch)):this.stream.peekChar(0)===C&&this.stream.peekChar(1)===y?(this.stream.advance(2),this.finishToken(e,n.SubstringOperator)):this.stream.peekChar(0)===g&&this.stream.peekChar(1)===y?(this.stream.advance(2),this.finishToken(e,n.PrefixOperator)):this.stream.peekChar(0)===R&&this.stream.peekChar(1)===y?(this.stream.advance(2),this.finishToken(e,n.SuffixOperator)):(this.stream.nextChar(),this.finishToken(e,n.Delim)))},e.prototype._matchWordAnyCase=function(e){var t=0;return this.stream.advanceWhileChar((function(r){var n=e[t]===r||e[t+1]===r;return n&&(t+=2),n})),t===e.length||(this.stream.goBack(t/2),!1)},e.prototype.trivia=function(){while(1){var e=this.stream.pos();if(this._whitespace()){if(!this.ignoreWhitespace)return this.finishToken(e,n.Whitespace)}else{if(!this.comment())return null;if(!this.ignoreComment)return this.finishToken(e,n.Comment)}}},e.prototype.comment=function(){if(this.stream.advanceIfChars([D,C])){var e=!1,t=!1;return this.stream.advanceWhileChar((function(r){return t&&r===D?(e=!0,!1):(t=r===C,!0)})),e&&this.stream.advance(1),!0}return!1},e.prototype._number=function(){var e,t=0;return this.stream.peekChar()===z&&(t=1),e=this.stream.peekChar(t),e>=f&&e<=d&&(this.stream.advance(t+1),this.stream.advanceWhileChar((function(e){return e>=f&&e<=d||0===t&&e===z})),!0)},e.prototype._newline=function(e){var t=this.stream.peekChar();switch(t){case F:case M:case T:return this.stream.advance(1),e.push(String.fromCharCode(t)),t===F&&this.stream.advanceIfChar(T)&&e.push("\n"),!0}return!1},e.prototype._escape=function(e,t){var r=this.stream.peekChar();if(r===A){this.stream.advance(1),r=this.stream.peekChar();var n=0;while(n<6&&(r>=f&&r<=d||r>=a&&r<=c||r>=h&&r<=l))this.stream.advance(1),r=this.stream.peekChar(),n++;if(n>0){try{var i=parseInt(this.stream.substring(this.stream.pos()-n),16);i&&e.push(String.fromCharCode(i))}catch(o){}return r===L||r===N?this.stream.advance(1):this._newline([]),!0}if(r!==F&&r!==M&&r!==T)return this.stream.advance(1),e.push(String.fromCharCode(r)),!0;if(t)return this._newline(e)}return!1},e.prototype._stringChar=function(e,t){var r=this.stream.peekChar();return 0!==r&&r!==e&&r!==A&&r!==F&&r!==M&&r!==T&&(this.stream.advance(1),t.push(String.fromCharCode(r)),!0)},e.prototype._string=function(e){if(this.stream.peekChar()===V||this.stream.peekChar()===O){var t=this.stream.nextChar();e.push(String.fromCharCode(t));while(this._stringChar(t,e)||this._escape(e,!0));return this.stream.peekChar()===t?(this.stream.nextChar(),e.push(String.fromCharCode(t)),n.String):n.BadString}return null},e.prototype._unquotedChar=function(e){var t=this.stream.peekChar();return 0!==t&&t!==A&&t!==V&&t!==O&&t!==_&&t!==w&&t!==L&&t!==N&&t!==T&&t!==M&&t!==F&&(this.stream.advance(1),e.push(String.fromCharCode(t)),!0)},e.prototype._unquotedString=function(e){var t=!1;while(this._unquotedChar(e)||this._escape(e))t=!0;return t},e.prototype._whitespace=function(){var e=this.stream.advanceWhileChar((function(e){return e===L||e===N||e===T||e===M||e===F}));return e>0},e.prototype._name=function(e){var t=!1;while(this._identChar(e)||this._escape(e))t=!0;return t},e.prototype.ident=function(e){var t=this.stream.pos(),r=this._minus(e);if(r&&this._minus(e)){if(this._identFirstChar(e)||this._escape(e)){while(this._identChar(e)||this._escape(e));return!0}}else if(this._identFirstChar(e)||this._escape(e)){while(this._identChar(e)||this._escape(e));return!0}return this.stream.goBackTo(t),!1},e.prototype._identFirstChar=function(e){var t=this.stream.peekChar();return(t===k||t>=a&&t<=u||t>=h&&t<=p||t>=128&&t<=65535)&&(this.stream.advance(1),e.push(String.fromCharCode(t)),!0)},e.prototype._minus=function(e){var t=this.stream.peekChar();return t===b&&(this.stream.advance(1),e.push(String.fromCharCode(t)),!0)},e.prototype._identChar=function(e){var t=this.stream.peekChar();return(t===k||t===b||t>=a&&t<=u||t>=h&&t<=p||t>=f&&t<=d||t>=128&&t<=65535)&&(this.stream.advance(1),e.push(String.fromCharCode(t)),!0)},e}(),Y=function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},e(t,r)};return function(t,r){function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();function ee(e,t){var r=null;return!e||t<e.offset||t>e.end?null:(e.accept((function(e){return-1===e.offset&&-1===e.length||e.offset<=t&&e.end>=t&&(r?e.length<=r.length&&(r=e):r=e,!0)})),r)}function te(e,t){var r=ee(e,t),n=[];while(r)n.unshift(r),r=r.parent;return n}function re(e){var t=e.findParent(J.Declaration),r=t&&t.getValue();return r&&r.encloses(e)?t:null}(function(e){e[e["Undefined"]=0]="Undefined",e[e["Identifier"]=1]="Identifier",e[e["Stylesheet"]=2]="Stylesheet",e[e["Ruleset"]=3]="Ruleset",e[e["Selector"]=4]="Selector",e[e["SimpleSelector"]=5]="SimpleSelector",e[e["SelectorInterpolation"]=6]="SelectorInterpolation",e[e["SelectorCombinator"]=7]="SelectorCombinator",e[e["SelectorCombinatorParent"]=8]="SelectorCombinatorParent",e[e["SelectorCombinatorSibling"]=9]="SelectorCombinatorSibling",e[e["SelectorCombinatorAllSiblings"]=10]="SelectorCombinatorAllSiblings",e[e["SelectorCombinatorShadowPiercingDescendant"]=11]="SelectorCombinatorShadowPiercingDescendant",e[e["Page"]=12]="Page",e[e["PageBoxMarginBox"]=13]="PageBoxMarginBox",e[e["ClassSelector"]=14]="ClassSelector",e[e["IdentifierSelector"]=15]="IdentifierSelector",e[e["ElementNameSelector"]=16]="ElementNameSelector",e[e["PseudoSelector"]=17]="PseudoSelector",e[e["AttributeSelector"]=18]="AttributeSelector",e[e["Declaration"]=19]="Declaration",e[e["Declarations"]=20]="Declarations",e[e["Property"]=21]="Property",e[e["Expression"]=22]="Expression",e[e["BinaryExpression"]=23]="BinaryExpression",e[e["Term"]=24]="Term",e[e["Operator"]=25]="Operator",e[e["Value"]=26]="Value",e[e["StringLiteral"]=27]="StringLiteral",e[e["URILiteral"]=28]="URILiteral",e[e["EscapedValue"]=29]="EscapedValue",e[e["Function"]=30]="Function",e[e["NumericValue"]=31]="NumericValue",e[e["HexColorValue"]=32]="HexColorValue",e[e["MixinDeclaration"]=33]="MixinDeclaration",e[e["MixinReference"]=34]="MixinReference",e[e["VariableName"]=35]="VariableName",e[e["VariableDeclaration"]=36]="VariableDeclaration",e[e["Prio"]=37]="Prio",e[e["Interpolation"]=38]="Interpolation",e[e["NestedProperties"]=39]="NestedProperties",e[e["ExtendsReference"]=40]="ExtendsReference",e[e["SelectorPlaceholder"]=41]="SelectorPlaceholder",e[e["Debug"]=42]="Debug",e[e["If"]=43]="If",e[e["Else"]=44]="Else",e[e["For"]=45]="For",e[e["Each"]=46]="Each",e[e["While"]=47]="While",e[e["MixinContent"]=48]="MixinContent",e[e["Media"]=49]="Media",e[e["Keyframe"]=50]="Keyframe",e[e["FontFace"]=51]="FontFace",e[e["Import"]=52]="Import",e[e["Namespace"]=53]="Namespace",e[e["Invocation"]=54]="Invocation",e[e["FunctionDeclaration"]=55]="FunctionDeclaration",e[e["ReturnStatement"]=56]="ReturnStatement",e[e["MediaQuery"]=57]="MediaQuery",e[e["FunctionParameter"]=58]="FunctionParameter",e[e["FunctionArgument"]=59]="FunctionArgument",e[e["KeyframeSelector"]=60]="KeyframeSelector",e[e["ViewPort"]=61]="ViewPort",e[e["Document"]=62]="Document",e[e["AtApplyRule"]=63]="AtApplyRule",e[e["CustomPropertyDeclaration"]=64]="CustomPropertyDeclaration",e[e["CustomPropertySet"]=65]="CustomPropertySet",e[e["ListEntry"]=66]="ListEntry",e[e["Supports"]=67]="Supports",e[e["SupportsCondition"]=68]="SupportsCondition",e[e["NamespacePrefix"]=69]="NamespacePrefix",e[e["GridLine"]=70]="GridLine",e[e["Plugin"]=71]="Plugin",e[e["UnknownAtRule"]=72]="UnknownAtRule",e[e["Use"]=73]="Use",e[e["ModuleConfiguration"]=74]="ModuleConfiguration",e[e["Forward"]=75]="Forward",e[e["ForwardVisibility"]=76]="ForwardVisibility",e[e["Module"]=77]="Module"})(J||(J={})),function(e){e[e["Mixin"]=0]="Mixin",e[e["Rule"]=1]="Rule",e[e["Variable"]=2]="Variable",e[e["Function"]=3]="Function",e[e["Keyframe"]=4]="Keyframe",e[e["Unknown"]=5]="Unknown",e[e["Module"]=6]="Module",e[e["Forward"]=7]="Forward",e[e["ForwardVisibility"]=8]="ForwardVisibility"}(X||(X={}));var ne,ie=function(){function e(e,t,r){void 0===e&&(e=-1),void 0===t&&(t=-1),this.parent=null,this.offset=e,this.length=t,r&&(this.nodeType=r)}return Object.defineProperty(e.prototype,"end",{get:function(){return this.offset+this.length},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"type",{get:function(){return this.nodeType||J.Undefined},set:function(e){this.nodeType=e},enumerable:!0,configurable:!0}),e.prototype.getTextProvider=function(){var e=this;while(e&&!e.textProvider)e=e.parent;return e?e.textProvider:function(){return"unknown"}},e.prototype.getText=function(){return this.getTextProvider()(this.offset,this.length)},e.prototype.matches=function(e){return this.length===e.length&&this.getTextProvider()(this.offset,this.length)===e},e.prototype.startsWith=function(e){return this.length>=e.length&&this.getTextProvider()(this.offset,e.length)===e},e.prototype.endsWith=function(e){return this.length>=e.length&&this.getTextProvider()(this.end-e.length,e.length)===e},e.prototype.accept=function(e){if(e(this)&&this.children)for(var t=0,r=this.children;t<r.length;t++){var n=r[t];n.accept(e)}},e.prototype.acceptVisitor=function(e){this.accept(e.visitNode.bind(e))},e.prototype.adoptChild=function(e,t){if(void 0===t&&(t=-1),e.parent&&e.parent.children){var r=e.parent.children.indexOf(e);r>=0&&e.parent.children.splice(r,1)}e.parent=this;var n=this.children;return n||(n=this.children=[]),-1!==t?n.splice(t,0,e):n.push(e),e},e.prototype.attachTo=function(e,t){return void 0===t&&(t=-1),e&&e.adoptChild(this,t),this},e.prototype.collectIssues=function(e){this.issues&&e.push.apply(e,this.issues)},e.prototype.addIssue=function(e){this.issues||(this.issues=[]),this.issues.push(e)},e.prototype.hasIssue=function(e){return Array.isArray(this.issues)&&this.issues.some((function(t){return t.getRule()===e}))},e.prototype.isErroneous=function(e){return void 0===e&&(e=!1),!!(this.issues&&this.issues.length>0)||e&&Array.isArray(this.children)&&this.children.some((function(e){return e.isErroneous(!0)}))},e.prototype.setNode=function(e,t,r){return void 0===r&&(r=-1),!!t&&(t.attachTo(this,r),this[e]=t,!0)},e.prototype.addChild=function(e){return!!e&&(this.children||(this.children=[]),e.attachTo(this),this.updateOffsetAndLength(e),!0)},e.prototype.updateOffsetAndLength=function(e){(e.offset<this.offset||-1===this.offset)&&(this.offset=e.offset);var t=e.end;(t>this.end||-1===this.length)&&(this.length=t-this.offset)},e.prototype.hasChildren=function(){return!!this.children&&this.children.length>0},e.prototype.getChildren=function(){return this.children?this.children.slice(0):[]},e.prototype.getChild=function(e){return this.children&&e<this.children.length?this.children[e]:null},e.prototype.addChildren=function(e){for(var t=0,r=e;t<r.length;t++){var n=r[t];this.addChild(n)}},e.prototype.findFirstChildBeforeOffset=function(e){if(this.children)for(var t=null,r=this.children.length-1;r>=0;r--)if(t=this.children[r],t.offset<=e)return t;return null},e.prototype.findChildAtOffset=function(e,t){var r=this.findFirstChildBeforeOffset(e);return r&&r.end>=e?t&&r.findChildAtOffset(e,!0)||r:null},e.prototype.encloses=function(e){return this.offset<=e.offset&&this.offset+this.length>=e.offset+e.length},e.prototype.getParent=function(){var e=this.parent;while(e instanceof oe)e=e.parent;return e},e.prototype.findParent=function(e){var t=this;while(t&&t.type!==e)t=t.parent;return t},e.prototype.findAParent=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=this;while(r&&!e.some((function(e){return r.type===e})))r=r.parent;return r},e.prototype.setData=function(e,t){this.options||(this.options={}),this.options[e]=t},e.prototype.getData=function(e){return this.options&&this.options.hasOwnProperty(e)?this.options[e]:null},e}(),oe=function(e){function t(t,r){void 0===r&&(r=-1);var n=e.call(this,-1,-1)||this;return n.attachTo(t,r),n.offset=-1,n.length=-1,n}return Y(t,e),t}(ie),se=function(e){function t(t,r){var n=e.call(this,t,r)||this;return n.isCustomProperty=!1,n}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.Identifier},enumerable:!0,configurable:!0}),t.prototype.containsInterpolation=function(){return this.hasChildren()},t}(ie),ae=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.Stylesheet},enumerable:!0,configurable:!0}),t}(ie),ce=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.Declarations},enumerable:!0,configurable:!0}),t}(ie),ue=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),t.prototype.getDeclarations=function(){return this.declarations},t.prototype.setDeclarations=function(e){return this.setNode("declarations",e)},t}(ie),he=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.Ruleset},enumerable:!0,configurable:!0}),t.prototype.getSelectors=function(){return this.selectors||(this.selectors=new oe(this)),this.selectors},t.prototype.isNested=function(){return!!this.parent&&null!==this.parent.findParent(J.Declarations)},t}(ue),le=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.Selector},enumerable:!0,configurable:!0}),t}(ie),pe=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.SimpleSelector},enumerable:!0,configurable:!0}),t}(ie),fe=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.AtApplyRule},enumerable:!0,configurable:!0}),t.prototype.setIdentifier=function(e){return this.setNode("identifier",e,0)},t.prototype.getIdentifier=function(){return this.identifier},t.prototype.getName=function(){return this.identifier?this.identifier.getText():""},t}(ie),de=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),t}(ie),me=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.CustomPropertyDeclaration},enumerable:!0,configurable:!0}),t.prototype.setProperty=function(e){return this.setNode("property",e)},t.prototype.getProperty=function(){return this.property},t.prototype.setValue=function(e){return this.setNode("value",e)},t.prototype.getValue=function(){return this.value},t.prototype.setPropertySet=function(e){return this.setNode("propertySet",e)},t.prototype.getPropertySet=function(){return this.propertySet},t}(de),ge=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.CustomPropertySet},enumerable:!0,configurable:!0}),t}(ue),ye=function(e){function t(t,r){var n=e.call(this,t,r)||this;return n.property=null,n}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.Declaration},enumerable:!0,configurable:!0}),t.prototype.setProperty=function(e){return this.setNode("property",e)},t.prototype.getProperty=function(){return this.property},t.prototype.getFullPropertyName=function(){var e=this.property?this.property.getName():"unknown";if(this.parent instanceof ce&&this.parent.getParent()instanceof De){var r=this.parent.getParent().getParent();if(r instanceof t)return r.getFullPropertyName()+e}return e},t.prototype.getNonPrefixedPropertyName=function(){var e=this.getFullPropertyName();if(e&&"-"===e.charAt(0)){var t=e.indexOf("-",1);if(-1!==t)return e.substring(t+1)}return e},t.prototype.setValue=function(e){return this.setNode("value",e)},t.prototype.getValue=function(){return this.value},t.prototype.setNestedProperties=function(e){return this.setNode("nestedProperties",e)},t.prototype.getNestedProperties=function(){return this.nestedProperties},t}(de),ve=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.Property},enumerable:!0,configurable:!0}),t.prototype.setIdentifier=function(e){return this.setNode("identifier",e)},t.prototype.getIdentifier=function(){return this.identifier},t.prototype.getName=function(){return this.getText()},t.prototype.isCustomProperty=function(){return!!this.identifier&&this.identifier.isCustomProperty},t}(ie),be=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.Invocation},enumerable:!0,configurable:!0}),t.prototype.getArguments=function(){return this.arguments||(this.arguments=new oe(this)),this.arguments},t}(ie),ke=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.Function},enumerable:!0,configurable:!0}),t.prototype.setIdentifier=function(e){return this.setNode("identifier",e,0)},t.prototype.getIdentifier=function(){return this.identifier},t.prototype.getName=function(){return this.identifier?this.identifier.getText():""},t}(be),xe=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.FunctionParameter},enumerable:!0,configurable:!0}),t.prototype.setIdentifier=function(e){return this.setNode("identifier",e,0)},t.prototype.getIdentifier=function(){return this.identifier},t.prototype.getName=function(){return this.identifier?this.identifier.getText():""},t.prototype.setDefaultValue=function(e){return this.setNode("defaultValue",e,0)},t.prototype.getDefaultValue=function(){return this.defaultValue},t}(ie),Ce=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.FunctionArgument},enumerable:!0,configurable:!0}),t.prototype.setIdentifier=function(e){return this.setNode("identifier",e,0)},t.prototype.getIdentifier=function(){return this.identifier},t.prototype.getName=function(){return this.identifier?this.identifier.getText():""},t.prototype.setValue=function(e){return this.setNode("value",e,0)},t.prototype.getValue=function(){return this.value},t}(ie),_e=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.If},enumerable:!0,configurable:!0}),t.prototype.setExpression=function(e){return this.setNode("expression",e,0)},t.prototype.setElseClause=function(e){return this.setNode("elseClause",e)},t}(ue),we=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.For},enumerable:!0,configurable:!0}),t.prototype.setVariable=function(e){return this.setNode("variable",e,0)},t}(ue),Pe=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.Each},enumerable:!0,configurable:!0}),t.prototype.getVariables=function(){return this.variables||(this.variables=new oe(this)),this.variables},t}(ue),Se=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.While},enumerable:!0,configurable:!0}),t}(ue),Ee=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.Else},enumerable:!0,configurable:!0}),t}(ue),Ie=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.FunctionDeclaration},enumerable:!0,configurable:!0}),t.prototype.setIdentifier=function(e){return this.setNode("identifier",e,0)},t.prototype.getIdentifier=function(){return this.identifier},t.prototype.getName=function(){return this.identifier?this.identifier.getText():""},t.prototype.getParameters=function(){return this.parameters||(this.parameters=new oe(this)),this.parameters},t}(ue),Re=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.ViewPort},enumerable:!0,configurable:!0}),t}(ue),Ae=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.FontFace},enumerable:!0,configurable:!0}),t}(ue),De=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.NestedProperties},enumerable:!0,configurable:!0}),t}(ue),Te=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.Keyframe},enumerable:!0,configurable:!0}),t.prototype.setKeyword=function(e){return this.setNode("keyword",e,0)},t.prototype.getKeyword=function(){return this.keyword},t.prototype.setIdentifier=function(e){return this.setNode("identifier",e,0)},t.prototype.getIdentifier=function(){return this.identifier},t.prototype.getName=function(){return this.identifier?this.identifier.getText():""},t}(ue),Fe=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.KeyframeSelector},enumerable:!0,configurable:!0}),t}(ue),Me=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.Import},enumerable:!0,configurable:!0}),t.prototype.setMedialist=function(e){return!!e&&(e.attachTo(this),!0)},t}(ie),Oe=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.Use},enumerable:!0,configurable:!0}),t.prototype.getParameters=function(){return this.parameters||(this.parameters=new oe(this)),this.parameters},t.prototype.setIdentifier=function(e){return this.setNode("identifier",e,0)},t.prototype.getIdentifier=function(){return this.identifier},t}(ie),Ve=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.ModuleConfiguration},enumerable:!0,configurable:!0}),t.prototype.setIdentifier=function(e){return this.setNode("identifier",e,0)},t.prototype.getIdentifier=function(){return this.identifier},t.prototype.getName=function(){return this.identifier?this.identifier.getText():""},t.prototype.setValue=function(e){return this.setNode("value",e,0)},t.prototype.getValue=function(){return this.value},t}(ie),Le=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.Forward},enumerable:!0,configurable:!0}),t.prototype.setIdentifier=function(e){return this.setNode("identifier",e,0)},t.prototype.getIdentifier=function(){return this.identifier},t}(ie),Ne=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.ForwardVisibility},enumerable:!0,configurable:!0}),t.prototype.setIdentifier=function(e){return this.setNode("identifier",e,0)},t.prototype.getIdentifier=function(){return this.identifier},t}(ie),$e=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.Namespace},enumerable:!0,configurable:!0}),t}(ie),Be=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.Media},enumerable:!0,configurable:!0}),t}(ue),Ue=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.Supports},enumerable:!0,configurable:!0}),t}(ue),We=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.Document},enumerable:!0,configurable:!0}),t}(ue),je=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),t.prototype.getMediums=function(){return this.mediums||(this.mediums=new oe(this)),this.mediums},t}(ie),Ke=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.MediaQuery},enumerable:!0,configurable:!0}),t}(ie),qe=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.SupportsCondition},enumerable:!0,configurable:!0}),t}(ie),ze=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.Page},enumerable:!0,configurable:!0}),t}(ue),He=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.PageBoxMarginBox},enumerable:!0,configurable:!0}),t}(ue),Ge=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.Expression},enumerable:!0,configurable:!0}),t}(ie),Qe=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.BinaryExpression},enumerable:!0,configurable:!0}),t.prototype.setLeft=function(e){return this.setNode("left",e)},t.prototype.getLeft=function(){return this.left},t.prototype.setRight=function(e){return this.setNode("right",e)},t.prototype.getRight=function(){return this.right},t.prototype.setOperator=function(e){return this.setNode("operator",e)},t.prototype.getOperator=function(){return this.operator},t}(ie),Je=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.Term},enumerable:!0,configurable:!0}),t.prototype.setOperator=function(e){return this.setNode("operator",e)},t.prototype.getOperator=function(){return this.operator},t.prototype.setExpression=function(e){return this.setNode("expression",e)},t.prototype.getExpression=function(){return this.expression},t}(ie),Xe=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.AttributeSelector},enumerable:!0,configurable:!0}),t.prototype.setNamespacePrefix=function(e){return this.setNode("namespacePrefix",e)},t.prototype.getNamespacePrefix=function(){return this.namespacePrefix},t.prototype.setIdentifier=function(e){return this.setNode("identifier",e)},t.prototype.getIdentifier=function(){return this.identifier},t.prototype.setOperator=function(e){return this.setNode("operator",e)},t.prototype.getOperator=function(){return this.operator},t.prototype.setValue=function(e){return this.setNode("value",e)},t.prototype.getValue=function(){return this.value},t}(ie),Ze=(function(e){function t(t,r){return e.call(this,t,r)||this}Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.Operator},enumerable:!0,configurable:!0})}(ie),function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.HexColorValue},enumerable:!0,configurable:!0}),t}(ie)),Ye=".".charCodeAt(0),et="0".charCodeAt(0),tt="9".charCodeAt(0),rt=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.NumericValue},enumerable:!0,configurable:!0}),t.prototype.getValue=function(){for(var e,t=this.getText(),r=0,n=0,i=t.length;n<i;n++){if(e=t.charCodeAt(n),!(et<=e&&e<=tt||e===Ye))break;r+=1}return{value:t.substring(0,r),unit:r<t.length?t.substring(r):void 0}},t}(ie),nt=function(e){function t(t,r){var n=e.call(this,t,r)||this;return n.variable=null,n.value=null,n.needsSemicolon=!0,n}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.VariableDeclaration},enumerable:!0,configurable:!0}),t.prototype.setVariable=function(e){return!!e&&(e.attachTo(this),this.variable=e,!0)},t.prototype.getVariable=function(){return this.variable},t.prototype.getName=function(){return this.variable?this.variable.getName():""},t.prototype.setValue=function(e){return!!e&&(e.attachTo(this),this.value=e,!0)},t.prototype.getValue=function(){return this.value},t}(de),it=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.Interpolation},enumerable:!0,configurable:!0}),t}(ie),ot=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.VariableName},enumerable:!0,configurable:!0}),t.prototype.getName=function(){return this.getText()},t}(ie),st=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.ExtendsReference},enumerable:!0,configurable:!0}),t.prototype.getSelectors=function(){return this.selectors||(this.selectors=new oe(this)),this.selectors},t}(ie),at=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.MixinReference},enumerable:!0,configurable:!0}),t.prototype.getNamespaces=function(){return this.namespaces||(this.namespaces=new oe(this)),this.namespaces},t.prototype.setIdentifier=function(e){return this.setNode("identifier",e,0)},t.prototype.getIdentifier=function(){return this.identifier},t.prototype.getName=function(){return this.identifier?this.identifier.getText():""},t.prototype.getArguments=function(){return this.arguments||(this.arguments=new oe(this)),this.arguments},t.prototype.setContent=function(e){return this.setNode("content",e)},t.prototype.getContent=function(){return this.content},t}(ie),ct=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.MixinDeclaration},enumerable:!0,configurable:!0}),t.prototype.setIdentifier=function(e){return this.setNode("identifier",e,0)},t.prototype.getIdentifier=function(){return this.identifier},t.prototype.getName=function(){return this.identifier?this.identifier.getText():""},t.prototype.getParameters=function(){return this.parameters||(this.parameters=new oe(this)),this.parameters},t.prototype.setGuard=function(e){return e&&(e.attachTo(this),this.guard=e),!1},t}(ue),ut=function(e){function t(t,r){return e.call(this,t,r)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.UnknownAtRule},enumerable:!0,configurable:!0}),t.prototype.setAtRuleName=function(e){this.atRuleName=e},t.prototype.getAtRuleName=function(){return this.atRuleName},t}(ue),ht=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.ListEntry},enumerable:!0,configurable:!0}),t.prototype.setKey=function(e){return this.setNode("key",e,0)},t.prototype.setValue=function(e){return this.setNode("value",e,1)},t}(ie),lt=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return Y(t,e),t.prototype.getConditions=function(){return this.conditions||(this.conditions=new oe(this)),this.conditions},t}(ie),pt=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return Y(t,e),t.prototype.setVariable=function(e){return this.setNode("variable",e)},t}(ie),ft=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return Y(t,e),Object.defineProperty(t.prototype,"type",{get:function(){return J.Module},enumerable:!0,configurable:!0}),t.prototype.setIdentifier=function(e){return this.setNode("identifier",e,0)},t.prototype.getIdentifier=function(){return this.identifier},t}(ie);(function(e){e[e["Ignore"]=1]="Ignore",e[e["Warning"]=2]="Warning",e[e["Error"]=4]="Error"})(ne||(ne={}));var dt=function(){function e(e,t,r,n,i,o){void 0===i&&(i=e.offset),void 0===o&&(o=e.length),this.node=e,this.rule=t,this.level=r,this.message=n||t.message,this.offset=i,this.length=o}return e.prototype.getRule=function(){return this.rule},e.prototype.getLevel=function(){return this.level},e.prototype.getOffset=function(){return this.offset},e.prototype.getLength=function(){return this.length},e.prototype.getNode=function(){return this.node},e.prototype.getMessage=function(){return this.message},e}(),mt=function(){function e(){this.entries=[]}return e.entries=function(t){var r=new e;return t.acceptVisitor(r),r.entries},e.prototype.visitNode=function(e){return e.isErroneous()&&e.collectIssues(this.entries),!0},e}();function gt(e,t){var r;return r=0===t.length?e:e.replace(/\{(\d+)\}/g,(function(e,r){var n=r[0];return"undefined"!==typeof t[n]?t[n]:e})),r}function yt(e,t){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];return gt(t,r)}function vt(e){return yt}var bt=vt(),kt=function(){function e(e,t){this.id=e,this.message=t}return e}(),xt={NumberExpected:new kt("css-numberexpected",bt("expected.number","number expected")),ConditionExpected:new kt("css-conditionexpected",bt("expected.condt","condition expected")),RuleOrSelectorExpected:new kt("css-ruleorselectorexpected",bt("expected.ruleorselector","at-rule or selector expected")),DotExpected:new kt("css-dotexpected",bt("expected.dot","dot expected")),ColonExpected:new kt("css-colonexpected",bt("expected.colon","colon expected")),SemiColonExpected:new kt("css-semicolonexpected",bt("expected.semicolon","semi-colon expected")),TermExpected:new kt("css-termexpected",bt("expected.term","term expected")),ExpressionExpected:new kt("css-expressionexpected",bt("expected.expression","expression expected")),OperatorExpected:new kt("css-operatorexpected",bt("expected.operator","operator expected")),IdentifierExpected:new kt("css-identifierexpected",bt("expected.ident","identifier expected")),PercentageExpected:new kt("css-percentageexpected",bt("expected.percentage","percentage expected")),URIOrStringExpected:new kt("css-uriorstringexpected",bt("expected.uriorstring","uri or string expected")),URIExpected:new kt("css-uriexpected",bt("expected.uri","URI expected")),VariableNameExpected:new kt("css-varnameexpected",bt("expected.varname","variable name expected")),VariableValueExpected:new kt("css-varvalueexpected",bt("expected.varvalue","variable value expected")),PropertyValueExpected:new kt("css-propertyvalueexpected",bt("expected.propvalue","property value expected")),LeftCurlyExpected:new kt("css-lcurlyexpected",bt("expected.lcurly","{ expected")),RightCurlyExpected:new kt("css-rcurlyexpected",bt("expected.rcurly","} expected")),LeftSquareBracketExpected:new kt("css-rbracketexpected",bt("expected.lsquare","[ expected")),RightSquareBracketExpected:new kt("css-lbracketexpected",bt("expected.rsquare","] expected")),LeftParenthesisExpected:new kt("css-lparentexpected",bt("expected.lparen","( expected")),RightParenthesisExpected:new kt("css-rparentexpected",bt("expected.rparent",") expected")),CommaExpected:new kt("css-commaexpected",bt("expected.comma","comma expected")),PageDirectiveOrDeclarationExpected:new kt("css-pagedirordeclexpected",bt("expected.pagedirordecl","page directive or declaraton expected")),UnknownAtRule:new kt("css-unknownatrule",bt("unknown.atrule","at-rule unknown")),UnknownKeyword:new kt("css-unknownkeyword",bt("unknown.keyword","unknown keyword")),SelectorExpected:new kt("css-selectorexpected",bt("expected.selector","selector expected")),StringLiteralExpected:new kt("css-stringliteralexpected",bt("expected.stringliteral","string literal expected")),WhitespaceExpected:new kt("css-whitespaceexpected",bt("expected.whitespace","whitespace expected")),MediaQueryExpected:new kt("css-mediaqueryexpected",bt("expected.mediaquery","media query expected")),IdentifierOrWildcardExpected:new kt("css-idorwildcardexpected",bt("expected.idorwildcard","identifier or wildcard expected")),WildcardExpected:new kt("css-wildcardexpected",bt("expected.wildcard","wildcard expected")),IdentifierOrVariableExpected:new kt("css-idorvarexpected",bt("expected.idorvar","identifier or variable expected"))},Ct=r("418a");function _t(e){return Object.keys(e).map((function(t){return e[t]}))}function wt(e){return"undefined"!==typeof e}var Pt=function(){function e(e){this.dataProviders=e,this._propertySet={},this._atDirectiveSet={},this._pseudoClassSet={},this._pseudoElementSet={},this._properties=[],this._atDirectives=[],this._pseudoClasses=[],this._pseudoElements=[],this.collectData()}return e.prototype.addDataProviders=function(e){this.dataProviders=this.dataProviders.concat(e),this.collectData()},e.prototype.collectData=function(){var e=this;this.dataProviders.forEach((function(t){t.provideProperties().forEach((function(t){e._propertySet[t.name]||(e._propertySet[t.name]=t)})),t.provideAtDirectives().forEach((function(t){e._atDirectiveSet[t.name]||(e._atDirectiveSet[t.name]=t)})),t.providePseudoClasses().forEach((function(t){e._pseudoClassSet[t.name]||(e._pseudoClassSet[t.name]=t)})),t.providePseudoElements().forEach((function(t){e._pseudoElementSet[t.name]||(e._pseudoElementSet[t.name]=t)}))})),this._properties=_t(this._propertySet),this._atDirectives=_t(this._atDirectiveSet),this._pseudoClasses=_t(this._pseudoClassSet),this._pseudoElements=_t(this._pseudoElementSet)},e.prototype.getProperty=function(e){return this._propertySet[e]},e.prototype.getAtDirective=function(e){return this._atDirectiveSet[e]},e.prototype.getPseudoClass=function(e){return this._pseudoClassSet[e]},e.prototype.getPseudoElement=function(e){return this._pseudoElementSet[e]},e.prototype.getProperties=function(){return this._properties},e.prototype.getAtDirectives=function(){return this._atDirectives},e.prototype.getPseudoClasses=function(){return this._pseudoClasses},e.prototype.getPseudoElements=function(){return this._pseudoElements},e.prototype.isKnownProperty=function(e){return e.toLowerCase()in this._propertySet},e.prototype.isStandardProperty=function(e){return this.isKnownProperty(e)&&(!this._propertySet[e.toLowerCase()].status||"standard"===this._propertySet[e.toLowerCase()].status)},e}(),St=function(){function e(e){this._properties=[],this._atDirectives=[],this._pseudoClasses=[],this._pseudoElements=[],this.addData(e)}return e.prototype.provideProperties=function(){return this._properties},e.prototype.provideAtDirectives=function(){return this._atDirectives},e.prototype.providePseudoClasses=function(){return this._pseudoClasses},e.prototype.providePseudoElements=function(){return this._pseudoElements},e.prototype.addData=function(e){e.properties&&(this._properties=this._properties.concat(e.properties)),e.atDirectives&&(this._atDirectives=this._atDirectives.concat(e.atDirectives)),e.pseudoClasses&&(this._pseudoClasses=this._pseudoClasses.concat(e.pseudoClasses)),e.pseudoElements&&(this._pseudoElements=this._pseudoElements.concat(e.pseudoElements))},e}(),Et={E:"Edge",FF:"Firefox",S:"Safari",C:"Chrome",IE:"IE",O:"Opera"};function It(e){switch(e){case"experimental":return"⚠️ Property is experimental. Be cautious when using it.️\n\n";case"nonstandard":return"🚨️ Property is nonstandard. Avoid using it.\n\n";case"obsolete":return"🚨️️️ Property is obsolete. Avoid using it.\n\n";default:return""}}function Rt(e,t){return t?{kind:"markdown",value:Dt(e)}:{kind:"plaintext",value:At(e)}}function At(e){if(!e.description||""===e.description)return"";if("string"!==typeof e.description)return e.description.value;var t="";e.status&&(t+=It(e.status)),t+=e.description;var r=Tt(e.browsers);return r&&(t+="\n("+r+")"),"syntax"in e&&(t+="\n\nSyntax: "+e.syntax),e.references&&e.references.length>0&&(t+="\n\n",t+=e.references.map((function(e){return e.name+": "+e.url})).join(" | ")),t}function Dt(e){if(!e.description||""===e.description)return"";var t="";e.status&&(t+=It(e.status)),"string"===typeof e.description?t+=e.description:t=e.description.value;var r=Tt(e.browsers);return r&&(t+="\n\n("+r+")"),"syntax"in e&&(t+="\n\nSyntax: "+e.syntax),e.references&&e.references.length>0&&(t+="\n\n",t+=e.references.map((function(e){return"["+e.name+"]("+e.url+")"})).join(" | ")),t}function Tt(e){return void 0===e&&(e=[]),0===e.length?null:e.map((function(e){var t="",r=e.match(/([A-Z]+)(\d+)?/),n=r[1],i=r[2];return n in Et&&(t+=Et[n]),i&&(t+=" "+i),t})).join(", ")}var Ft=vt(),Mt=[{func:"rgb($red, $green, $blue)",desc:Ft("css.builtin.rgb","Creates a Color from red, green, and blue values.")},{func:"rgba($red, $green, $blue, $alpha)",desc:Ft("css.builtin.rgba","Creates a Color from red, green, blue, and alpha values.")},{func:"hsl($hue, $saturation, $lightness)",desc:Ft("css.builtin.hsl","Creates a Color from hue, saturation, and lightness values.")},{func:"hsla($hue, $saturation, $lightness, $alpha)",desc:Ft("css.builtin.hsla","Creates a Color from hue, saturation, lightness, and alpha values.")}],Ot={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgrey:"#a9a9a9",darkgreen:"#006400",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",grey:"#808080",green:"#008000",greenyellow:"#adff2f",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgrey:"#d3d3d3",lightgreen:"#90ee90",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370d8",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#d87093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",red:"#ff0000",rebeccapurple:"#663399",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"},Vt={currentColor:"The value of the 'color' property. The computed value of the 'currentColor' keyword is the computed value of the 'color' property. If the 'currentColor' keyword is set on the 'color' property itself, it is treated as 'color:inherit' at parse time.",transparent:"Fully transparent. This keyword can be considered a shorthand for rgba(0,0,0,0) which is its computed value."};function Lt(e,t){var r=e.getText(),n=r.match(/^([-+]?[0-9]*\.?[0-9]+)(%?)$/);if(n){n[2]&&(t=100);var i=parseFloat(n[1])/t;if(i>=0&&i<=1)return i}throw new Error}function Nt(e){var t=e.getText(),r=t.match(/^([-+]?[0-9]*\.?[0-9]+)(deg)?$/);if(r)return parseFloat(t)%360;throw new Error}function $t(e){var t=e.getName();return!!t&&/^(rgb|rgba|hsl|hsla)$/gi.test(t)}var Bt=48,Ut=57,Wt=65,jt=97,Kt=102;function qt(e){return e<Bt?0:e<=Ut?e-Bt:(e<jt&&(e+=jt-Wt),e>=jt&&e<=Kt?e-jt+10:0)}function zt(e){if("#"!==e[0])return null;switch(e.length){case 4:return{red:17*qt(e.charCodeAt(1))/255,green:17*qt(e.charCodeAt(2))/255,blue:17*qt(e.charCodeAt(3))/255,alpha:1};case 5:return{red:17*qt(e.charCodeAt(1))/255,green:17*qt(e.charCodeAt(2))/255,blue:17*qt(e.charCodeAt(3))/255,alpha:17*qt(e.charCodeAt(4))/255};case 7:return{red:(16*qt(e.charCodeAt(1))+qt(e.charCodeAt(2)))/255,green:(16*qt(e.charCodeAt(3))+qt(e.charCodeAt(4)))/255,blue:(16*qt(e.charCodeAt(5))+qt(e.charCodeAt(6)))/255,alpha:1};case 9:return{red:(16*qt(e.charCodeAt(1))+qt(e.charCodeAt(2)))/255,green:(16*qt(e.charCodeAt(3))+qt(e.charCodeAt(4)))/255,blue:(16*qt(e.charCodeAt(5))+qt(e.charCodeAt(6)))/255,alpha:(16*qt(e.charCodeAt(7))+qt(e.charCodeAt(8)))/255}}return null}function Ht(e,t,r,n){if(void 0===n&&(n=1),e/=60,0===t)return{red:r,green:r,blue:r,alpha:n};var i=function(e,t,r){while(r<0)r+=6;while(r>=6)r-=6;return r<1?(t-e)*r+e:r<3?t:r<4?(t-e)*(4-r)+e:e},o=r<=.5?r*(t+1):r+t-r*t,s=2*r-o;return{red:i(s,o,e+2),green:i(s,o,e),blue:i(s,o,e-2),alpha:n}}function Gt(e){var t=e.red,r=e.green,n=e.blue,i=e.alpha,o=Math.max(t,r,n),s=Math.min(t,r,n),a=0,c=0,u=(s+o)/2,h=o-s;if(h>0){switch(c=Math.min(u<=.5?h/(2*u):h/(2-2*u),1),o){case t:a=(r-n)/h+(r<n?6:0);break;case r:a=(n-t)/h+2;break;case n:a=(t-r)/h+4;break}a*=60,a=Math.round(a)}return{h:a,s:c,l:u,a:i}}function Qt(e){if(e.type===J.HexColorValue){var t=e.getText();return zt(t)}if(e.type===J.Function){var r=e,n=r.getName(),i=r.getArguments().getChildren();if(!n||i.length<3||i.length>4)return null;try{var o=4===i.length?Lt(i[3],1):1;if("rgb"===n||"rgba"===n)return{red:Lt(i[0],255),green:Lt(i[1],255),blue:Lt(i[2],255),alpha:o};if("hsl"===n||"hsla"===n){var s=Nt(i[0]),a=Lt(i[1],100),c=Lt(i[2],100);return Ht(s,a,c,o)}}catch(f){return null}}else if(e.type===J.Identifier){if(e.parent&&e.parent.type!==J.Term)return null;var u=e.parent;if(u&&u.parent&&u.parent.type===J.BinaryExpression){var h=u.parent;if(h.parent&&h.parent.type===J.ListEntry&&h.parent.key===h)return null}var l=e.getText().toLowerCase();if("none"===l)return null;var p=Ot[l];if(p)return zt(p)}return null}var Jt={bottom:"Computes to ‘100%’ for the vertical position if one or two values are given, otherwise specifies the bottom edge as the origin for the next offset.",center:"Computes to ‘50%’ (‘left 50%’) for the horizontal position if the horizontal position is not otherwise specified, or ‘50%’ (‘top 50%’) for the vertical position if it is.",left:"Computes to ‘0%’ for the horizontal position if one or two values are given, otherwise specifies the left edge as the origin for the next offset.",right:"Computes to ‘100%’ for the horizontal position if one or two values are given, otherwise specifies the right edge as the origin for the next offset.",top:"Computes to ‘0%’ for the vertical position if one or two values are given, otherwise specifies the top edge as the origin for the next offset."},Xt={"no-repeat":"Placed once and not repeated in this direction.",repeat:"Repeated in this direction as often as needed to cover the background painting area.","repeat-x":"Computes to ‘repeat no-repeat’.","repeat-y":"Computes to ‘no-repeat repeat’.",round:"Repeated as often as will fit within the background positioning area. If it doesn’t fit a whole number of times, it is rescaled so that it does.",space:"Repeated as often as will fit within the background positioning area without being clipped and then the images are spaced out to fill the area."},Zt={dashed:"A series of square-ended dashes.",dotted:"A series of round dots.",double:"Two parallel solid lines with some space between them.",groove:"Looks as if it were carved in the canvas.",hidden:"Same as ‘none’, but has different behavior in the border conflict resolution rules for border-collapsed tables.",inset:"Looks as if the content on the inside of the border is sunken into the canvas.",none:"No border. Color and width are ignored.",outset:"Looks as if the content on the inside of the border is coming out of the canvas.",ridge:"Looks as if it were coming out of the canvas.",solid:"A single line segment."},Yt=["medium","thick","thin"],er={"border-box":"The background is painted within (clipped to) the border box.","content-box":"The background is painted within (clipped to) the content box.","padding-box":"The background is painted within (clipped to) the padding box."},tr={"margin-box":"Uses the margin box as reference box.","fill-box":"Uses the object bounding box as reference box.","stroke-box":"Uses the stroke bounding box as reference box.","view-box":"Uses the nearest SVG viewport as reference box."},rr={initial:"Represents the value specified as the property’s initial value.",inherit:"Represents the computed value of the property on the element’s parent.",unset:"Acts as either `inherit` or `initial`, depending on whether the property is inherited or not."},nr={"url()":"Reference an image file by URL","image()":"Provide image fallbacks and annotations.","-webkit-image-set()":"Provide multiple resolutions. Remember to use unprefixed image-set() in addition.","image-set()":"Provide multiple resolutions of an image and const the UA decide which is most appropriate in a given situation.","-moz-element()":"Use an element in the document as an image. Remember to use unprefixed element() in addition.","element()":"Use an element in the document as an image.","cross-fade()":"Indicates the two images to be combined and how far along in the transition the combination is.","-webkit-gradient()":"Deprecated. Use modern linear-gradient() or radial-gradient() instead.","-webkit-linear-gradient()":"Linear gradient. Remember to use unprefixed version in addition.","-moz-linear-gradient()":"Linear gradient. Remember to use unprefixed version in addition.","-o-linear-gradient()":"Linear gradient. Remember to use unprefixed version in addition.","linear-gradient()":"A linear gradient is created by specifying a straight gradient line, and then several colors placed along that line.","-webkit-repeating-linear-gradient()":"Repeating Linear gradient. Remember to use unprefixed version in addition.","-moz-repeating-linear-gradient()":"Repeating Linear gradient. Remember to use unprefixed version in addition.","-o-repeating-linear-gradient()":"Repeating Linear gradient. Remember to use unprefixed version in addition.","repeating-linear-gradient()":"Same as linear-gradient, except the color-stops are repeated infinitely in both directions, with their positions shifted by multiples of the difference between the last specified color-stop’s position and the first specified color-stop’s position.","-webkit-radial-gradient()":"Radial gradient. Remember to use unprefixed version in addition.","-moz-radial-gradient()":"Radial gradient. Remember to use unprefixed version in addition.","radial-gradient()":"Colors emerge from a single point and smoothly spread outward in a circular or elliptical shape.","-webkit-repeating-radial-gradient()":"Repeating radial gradient. Remember to use unprefixed version in addition.","-moz-repeating-radial-gradient()":"Repeating radial gradient. Remember to use unprefixed version in addition.","repeating-radial-gradient()":"Same as radial-gradient, except the color-stops are repeated infinitely in both directions, with their positions shifted by multiples of the difference between the last specified color-stop’s position and the first specified color-stop’s position."},ir={ease:"Equivalent to cubic-bezier(0.25, 0.1, 0.25, 1.0).","ease-in":"Equivalent to cubic-bezier(0.42, 0, 1.0, 1.0).","ease-in-out":"Equivalent to cubic-bezier(0.42, 0, 0.58, 1.0).","ease-out":"Equivalent to cubic-bezier(0, 0, 0.58, 1.0).",linear:"Equivalent to cubic-bezier(0.0, 0.0, 1.0, 1.0).","step-end":"Equivalent to steps(1, end).","step-start":"Equivalent to steps(1, start).","steps()":"The first parameter specifies the number of intervals in the function. The second parameter, which is optional, is either the value “start” or “end”.","cubic-bezier()":"Specifies a cubic-bezier curve. The four values specify points P1 and P2  of the curve as (x1, y1, x2, y2).","cubic-bezier(0.6, -0.28, 0.735, 0.045)":"Ease-in Back. Overshoots.","cubic-bezier(0.68, -0.55, 0.265, 1.55)":"Ease-in-out Back. Overshoots.","cubic-bezier(0.175, 0.885, 0.32, 1.275)":"Ease-out Back. Overshoots.","cubic-bezier(0.6, 0.04, 0.98, 0.335)":"Ease-in Circular. Based on half circle.","cubic-bezier(0.785, 0.135, 0.15, 0.86)":"Ease-in-out Circular. Based on half circle.","cubic-bezier(0.075, 0.82, 0.165, 1)":"Ease-out Circular. Based on half circle.","cubic-bezier(0.55, 0.055, 0.675, 0.19)":"Ease-in Cubic. Based on power of three.","cubic-bezier(0.645, 0.045, 0.355, 1)":"Ease-in-out Cubic. Based on power of three.","cubic-bezier(0.215, 0.610, 0.355, 1)":"Ease-out Cubic. Based on power of three.","cubic-bezier(0.95, 0.05, 0.795, 0.035)":"Ease-in Exponential. Based on two to the power ten.","cubic-bezier(1, 0, 0, 1)":"Ease-in-out Exponential. Based on two to the power ten.","cubic-bezier(0.19, 1, 0.22, 1)":"Ease-out Exponential. Based on two to the power ten.","cubic-bezier(0.47, 0, 0.745, 0.715)":"Ease-in Sine.","cubic-bezier(0.445, 0.05, 0.55, 0.95)":"Ease-in-out Sine.","cubic-bezier(0.39, 0.575, 0.565, 1)":"Ease-out Sine.","cubic-bezier(0.55, 0.085, 0.68, 0.53)":"Ease-in Quadratic. Based on power of two.","cubic-bezier(0.455, 0.03, 0.515, 0.955)":"Ease-in-out Quadratic. Based on power of two.","cubic-bezier(0.25, 0.46, 0.45, 0.94)":"Ease-out Quadratic. Based on power of two.","cubic-bezier(0.895, 0.03, 0.685, 0.22)":"Ease-in Quartic. Based on power of four.","cubic-bezier(0.77, 0, 0.175, 1)":"Ease-in-out Quartic. Based on power of four.","cubic-bezier(0.165, 0.84, 0.44, 1)":"Ease-out Quartic. Based on power of four.","cubic-bezier(0.755, 0.05, 0.855, 0.06)":"Ease-in Quintic. Based on power of five.","cubic-bezier(0.86, 0, 0.07, 1)":"Ease-in-out Quintic. Based on power of five.","cubic-bezier(0.23, 1, 0.320, 1)":"Ease-out Quintic. Based on power of five."},or={"circle()":"Defines a circle.","ellipse()":"Defines an ellipse.","inset()":"Defines an inset rectangle.","polygon()":"Defines a polygon."},sr={length:["em","rem","ex","px","cm","mm","in","pt","pc","ch","vw","vh","vmin","vmax"],angle:["deg","rad","grad","turn"],time:["ms","s"],frequency:["Hz","kHz"],resolution:["dpi","dpcm","dppx"],percentage:["%","fr"]},ar=["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rb","rp","rt","rtc","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","title","tr","track","u","ul","const","video","wbr"],cr=["circle","clipPath","cursor","defs","desc","ellipse","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","foreignObject","g","hatch","hatchpath","image","line","linearGradient","marker","mask","mesh","meshpatch","meshrow","metadata","mpath","path","pattern","polygon","polyline","radialGradient","rect","set","solidcolor","stop","svg","switch","symbol","text","textPath","tspan","use","view"],ur=["@bottom-center","@bottom-left","@bottom-left-corner","@bottom-right","@bottom-right-corner","@left-bottom","@left-middle","@left-top","@right-bottom","@right-middle","@right-top","@top-center","@top-left","@top-left-corner","@top-right","@top-right-corner"],hr=new Pt([new St(Ct["a"])]),lr=function(){for(var e=0,t=0,r=arguments.length;t<r;t++)e+=arguments[t].length;var n=Array(e),i=0;for(t=0;t<r;t++)for(var o=arguments[t],s=0,a=o.length;s<a;s++,i++)n[i]=o[s];return n},pr=function(){function e(e){void 0===e&&(e=new Z),this.keyframeRegex=/^@(\-(webkit|ms|moz|o)\-)?keyframes$/i,this.scanner=e,this.token={type:n.EOF,offset:-1,len:0,text:""},this.prevToken=void 0}return e.prototype.peekIdent=function(e){return n.Ident===this.token.type&&e.length===this.token.text.length&&e===this.token.text.toLowerCase()},e.prototype.peekKeyword=function(e){return n.AtKeyword===this.token.type&&e.length===this.token.text.length&&e===this.token.text.toLowerCase()},e.prototype.peekDelim=function(e){return n.Delim===this.token.type&&e===this.token.text},e.prototype.peek=function(e){return e===this.token.type},e.prototype.peekRegExp=function(e,t){return e===this.token.type&&t.test(this.token.text)},e.prototype.hasWhitespace=function(){return!!this.prevToken&&this.prevToken.offset+this.prevToken.len!==this.token.offset},e.prototype.consumeToken=function(){this.prevToken=this.token,this.token=this.scanner.scan()},e.prototype.mark=function(){return{prev:this.prevToken,curr:this.token,pos:this.scanner.pos()}},e.prototype.restoreAtMark=function(e){this.prevToken=e.prev,this.token=e.curr,this.scanner.goBackTo(e.pos)},e.prototype.try=function(e){var t=this.mark(),r=e();return r||(this.restoreAtMark(t),null)},e.prototype.acceptOneKeyword=function(e){if(n.AtKeyword===this.token.type)for(var t=0,r=e;t<r.length;t++){var i=r[t];if(i.length===this.token.text.length&&i===this.token.text.toLowerCase())return this.consumeToken(),!0}return!1},e.prototype.accept=function(e){return e===this.token.type&&(this.consumeToken(),!0)},e.prototype.acceptIdent=function(e){return!!this.peekIdent(e)&&(this.consumeToken(),!0)},e.prototype.acceptKeyword=function(e){return!!this.peekKeyword(e)&&(this.consumeToken(),!0)},e.prototype.acceptDelim=function(e){return!!this.peekDelim(e)&&(this.consumeToken(),!0)},e.prototype.acceptRegexp=function(e){return!!e.test(this.token.text)&&(this.consumeToken(),!0)},e.prototype._parseRegexp=function(e){var t=this.createNode(J.Identifier);do{}while(this.acceptRegexp(e));return this.finish(t)},e.prototype.acceptUnquotedString=function(){var e=this.scanner.pos();this.scanner.goBackTo(this.token.offset);var t=this.scanner.scanUnquotedString();return t?(this.token=t,this.consumeToken(),!0):(this.scanner.goBackTo(e),!1)},e.prototype.resync=function(e,t){while(1){if(e&&-1!==e.indexOf(this.token.type))return this.consumeToken(),!0;if(t&&-1!==t.indexOf(this.token.type))return!0;if(this.token.type===n.EOF)return!1;this.token=this.scanner.scan()}},e.prototype.createNode=function(e){return new ie(this.token.offset,this.token.len,e)},e.prototype.create=function(e){return new e(this.token.offset,this.token.len)},e.prototype.finish=function(e,t,r,n){if(!(e instanceof oe)&&(t&&this.markError(e,t,r,n),this.prevToken)){var i=this.prevToken.offset+this.prevToken.len;e.length=i>e.offset?i-e.offset:0}return e},e.prototype.markError=function(e,t,r,n){this.token!==this.lastErrorToken&&(e.addIssue(new dt(e,t,ne.Error,void 0,this.token.offset,this.token.len)),this.lastErrorToken=this.token),(r||n)&&this.resync(r,n)},e.prototype.parseStylesheet=function(e){var t=e.version,r=e.getText(),n=function(n,i){if(e.version!==t)throw new Error("Underlying model has changed, AST is no longer valid");return r.substr(n,i)};return this.internalParse(r,this._parseStylesheet,n)},e.prototype.internalParse=function(e,t,r){this.scanner.setSource(e),this.token=this.scanner.scan();var n=t.bind(this)();return n&&(n.textProvider=r||function(t,r){return e.substr(t,r)}),n},e.prototype._parseStylesheet=function(){var e=this.create(ae);while(e.addChild(this._parseStylesheetStart()));var t=!1;do{var r=!1;do{r=!1;var i=this._parseStylesheetStatement();i&&(e.addChild(i),r=!0,t=!1,this.peek(n.EOF)||!this._needsSemicolonAfter(i)||this.accept(n.SemiColon)||this.markError(e,xt.SemiColonExpected));while(this.accept(n.SemiColon)||this.accept(n.CDO)||this.accept(n.CDC))r=!0,t=!1}while(r);if(this.peek(n.EOF))break;t||(this.peek(n.AtKeyword)?this.markError(e,xt.UnknownAtRule):this.markError(e,xt.RuleOrSelectorExpected),t=!0),this.consumeToken()}while(!this.peek(n.EOF));return this.finish(e)},e.prototype._parseStylesheetStart=function(){return this._parseCharset()},e.prototype._parseStylesheetStatement=function(e){return void 0===e&&(e=!1),this.peek(n.AtKeyword)?this._parseStylesheetAtStatement(e):this._parseRuleset(e)},e.prototype._parseStylesheetAtStatement=function(e){return void 0===e&&(e=!1),this._parseImport()||this._parseMedia(e)||this._parsePage()||this._parseFontFace()||this._parseKeyframe()||this._parseSupports(e)||this._parseViewPort()||this._parseNamespace()||this._parseDocument()||this._parseUnknownAtRule()},e.prototype._tryParseRuleset=function(e){var t=this.mark();if(this._parseSelector(e)){while(this.accept(n.Comma)&&this._parseSelector(e));if(this.accept(n.CurlyL))return this.restoreAtMark(t),this._parseRuleset(e)}return this.restoreAtMark(t),null},e.prototype._parseRuleset=function(e){void 0===e&&(e=!1);var t=this.create(he),r=t.getSelectors();if(!r.addChild(this._parseSelector(e)))return null;while(this.accept(n.Comma))if(!r.addChild(this._parseSelector(e)))return this.finish(t,xt.SelectorExpected);return this._parseBody(t,this._parseRuleSetDeclaration.bind(this))},e.prototype._parseRuleSetDeclaration=function(){return this._parseAtApply()||this._tryParseCustomPropertyDeclaration()||this._parseDeclaration()||this._parseUnknownAtRule()},e.prototype._parseAtApply=function(){if(!this.peekKeyword("@apply"))return null;var e=this.create(fe);return this.consumeToken(),e.setIdentifier(this._parseIdent([X.Variable]))?this.finish(e):this.finish(e,xt.IdentifierExpected)},e.prototype._needsSemicolonAfter=function(e){switch(e.type){case J.Keyframe:case J.ViewPort:case J.Media:case J.Ruleset:case J.Namespace:case J.If:case J.For:case J.Each:case J.While:case J.MixinDeclaration:case J.FunctionDeclaration:return!1;case J.ExtendsReference:case J.MixinContent:case J.ReturnStatement:case J.MediaQuery:case J.Debug:case J.Import:case J.AtApplyRule:case J.CustomPropertyDeclaration:return!0;case J.VariableDeclaration:return e.needsSemicolon;case J.MixinReference:return!e.getContent();case J.Declaration:return!e.getNestedProperties()}return!1},e.prototype._parseDeclarations=function(e){var t=this.create(ce);if(!this.accept(n.CurlyL))return null;var r=e();while(t.addChild(r)){if(this.peek(n.CurlyR))break;if(this._needsSemicolonAfter(r)&&!this.accept(n.SemiColon))return this.finish(t,xt.SemiColonExpected,[n.SemiColon,n.CurlyR]);r&&this.prevToken&&this.prevToken.type===n.SemiColon&&(r.semicolonPosition=this.prevToken.offset);while(this.accept(n.SemiColon));r=e()}return this.accept(n.CurlyR)?this.finish(t):this.finish(t,xt.RightCurlyExpected,[n.CurlyR,n.SemiColon])},e.prototype._parseBody=function(e,t){return e.setDeclarations(this._parseDeclarations(t))?this.finish(e):this.finish(e,xt.LeftCurlyExpected,[n.CurlyR,n.SemiColon])},e.prototype._parseSelector=function(e){var t=this.create(le),r=!1;e&&(r=t.addChild(this._parseCombinator()));while(t.addChild(this._parseSimpleSelector()))r=!0,t.addChild(this._parseCombinator());return r?this.finish(t):null},e.prototype._parseDeclaration=function(e){var t=this.create(ye);if(!t.setProperty(this._parseProperty()))return null;if(!this.accept(n.Colon)){var r=e?lr(e,[n.SemiColon]):[n.SemiColon];return this.finish(t,xt.ColonExpected,[n.Colon],r)}return this.prevToken&&(t.colonPosition=this.prevToken.offset),t.setValue(this._parseExpr())?(t.addChild(this._parsePrio()),this.peek(n.SemiColon)&&(t.semicolonPosition=this.token.offset),this.finish(t)):this.finish(t,xt.PropertyValueExpected)},e.prototype._tryParseCustomPropertyDeclaration=function(){if(!this.peekRegExp(n.Ident,/^--/))return null;var e=this.create(me);if(!e.setProperty(this._parseProperty()))return null;if(!this.accept(n.Colon))return this.finish(e,xt.ColonExpected,[n.Colon]);this.prevToken&&(e.colonPosition=this.prevToken.offset);var t=this.mark();if(this.peek(n.CurlyL)){var r=this.create(ge),i=this._parseDeclarations(this._parseRuleSetDeclaration.bind(this));if(r.setDeclarations(i)&&!i.isErroneous(!0)&&(r.addChild(this._parsePrio()),this.peek(n.SemiColon)))return this.finish(r),e.setPropertySet(r),e.semicolonPosition=this.token.offset,this.finish(e);this.restoreAtMark(t)}var o=this._parseExpr();return o&&!o.isErroneous(!0)&&(this._parsePrio(),this.peek(n.SemiColon))?(e.setValue(o),e.semicolonPosition=this.token.offset,this.finish(e)):(this.restoreAtMark(t),e.addChild(this._parseCustomPropertyValue()),e.addChild(this._parsePrio()),wt(e.colonPosition)&&this.token.offset===e.colonPosition+1?this.finish(e,xt.PropertyValueExpected):this.finish(e))},e.prototype._parseCustomPropertyValue=function(){var e=this.create(ie),t=function(){return 0===r&&0===i&&0===o},r=0,i=0,o=0;e:while(1){switch(this.token.type){case n.SemiColon:if(t())break e;break;case n.Exclamation:if(t())break e;break;case n.CurlyL:r++;break;case n.CurlyR:if(r--,r<0){if(0===i&&0===o)break e;return this.finish(e,xt.LeftCurlyExpected)}break;case n.ParenthesisL:i++;break;case n.ParenthesisR:if(i--,i<0)return this.finish(e,xt.LeftParenthesisExpected);break;case n.BracketL:o++;break;case n.BracketR:if(o--,o<0)return this.finish(e,xt.LeftSquareBracketExpected);break;case n.BadString:break e;case n.EOF:var s=xt.RightCurlyExpected;return o>0?s=xt.RightSquareBracketExpected:i>0&&(s=xt.RightParenthesisExpected),this.finish(e,s)}this.consumeToken()}return this.finish(e)},e.prototype._tryToParseDeclaration=function(){var e=this.mark();return this._parseProperty()&&this.accept(n.Colon)?(this.restoreAtMark(e),this._parseDeclaration()):(this.restoreAtMark(e),null)},e.prototype._parseProperty=function(){var e=this.create(ve),t=this.mark();return(this.acceptDelim("*")||this.acceptDelim("_"))&&this.hasWhitespace()?(this.restoreAtMark(t),null):e.setIdentifier(this._parsePropertyIdentifier())?this.finish(e):null},e.prototype._parsePropertyIdentifier=function(){return this._parseIdent()},e.prototype._parseCharset=function(){if(!this.peek(n.Charset))return null;var e=this.create(ie);return this.consumeToken(),this.accept(n.String)?this.accept(n.SemiColon)?this.finish(e):this.finish(e,xt.SemiColonExpected):this.finish(e,xt.IdentifierExpected)},e.prototype._parseImport=function(){if(!this.peekKeyword("@import"))return null;var e=this.create(Me);return this.consumeToken(),e.addChild(this._parseURILiteral())||e.addChild(this._parseStringLiteral())?(this.peek(n.SemiColon)||this.peek(n.EOF)||e.setMedialist(this._parseMediaQueryList()),this.finish(e)):this.finish(e,xt.URIOrStringExpected)},e.prototype._parseNamespace=function(){if(!this.peekKeyword("@namespace"))return null;var e=this.create($e);return this.consumeToken(),e.addChild(this._parseURILiteral())||(e.addChild(this._parseIdent()),e.addChild(this._parseURILiteral())||e.addChild(this._parseStringLiteral()))?this.accept(n.SemiColon)?this.finish(e):this.finish(e,xt.SemiColonExpected):this.finish(e,xt.URIExpected,[n.SemiColon])},e.prototype._parseFontFace=function(){if(!this.peekKeyword("@font-face"))return null;var e=this.create(Ae);return this.consumeToken(),this._parseBody(e,this._parseRuleSetDeclaration.bind(this))},e.prototype._parseViewPort=function(){if(!this.peekKeyword("@-ms-viewport")&&!this.peekKeyword("@-o-viewport")&&!this.peekKeyword("@viewport"))return null;var e=this.create(Re);return this.consumeToken(),this._parseBody(e,this._parseRuleSetDeclaration.bind(this))},e.prototype._parseKeyframe=function(){if(!this.peekRegExp(n.AtKeyword,this.keyframeRegex))return null;var e=this.create(Te),t=this.create(ie);return this.consumeToken(),e.setKeyword(this.finish(t)),t.matches("@-ms-keyframes")&&this.markError(t,xt.UnknownKeyword),e.setIdentifier(this._parseKeyframeIdent())?this._parseBody(e,this._parseKeyframeSelector.bind(this)):this.finish(e,xt.IdentifierExpected,[n.CurlyR])},e.prototype._parseKeyframeIdent=function(){return this._parseIdent([X.Keyframe])},e.prototype._parseKeyframeSelector=function(){var e=this.create(Fe);if(!e.addChild(this._parseIdent())&&!this.accept(n.Percentage))return null;while(this.accept(n.Comma))if(!e.addChild(this._parseIdent())&&!this.accept(n.Percentage))return this.finish(e,xt.PercentageExpected);return this._parseBody(e,this._parseRuleSetDeclaration.bind(this))},e.prototype._tryParseKeyframeSelector=function(){var e=this.create(Fe),t=this.mark();if(!e.addChild(this._parseIdent())&&!this.accept(n.Percentage))return null;while(this.accept(n.Comma))if(!e.addChild(this._parseIdent())&&!this.accept(n.Percentage))return this.restoreAtMark(t),null;return this.peek(n.CurlyL)?this._parseBody(e,this._parseRuleSetDeclaration.bind(this)):(this.restoreAtMark(t),null)},e.prototype._parseSupports=function(e){if(void 0===e&&(e=!1),!this.peekKeyword("@supports"))return null;var t=this.create(Ue);return this.consumeToken(),t.addChild(this._parseSupportsCondition()),this._parseBody(t,this._parseSupportsDeclaration.bind(this,e))},e.prototype._parseSupportsDeclaration=function(e){return void 0===e&&(e=!1),e?this._tryParseRuleset(!0)||this._tryToParseDeclaration()||this._parseStylesheetStatement(!0):this._parseStylesheetStatement(!1)},e.prototype._parseSupportsCondition=function(){var e=this.create(qe);if(this.acceptIdent("not"))e.addChild(this._parseSupportsConditionInParens());else if(e.addChild(this._parseSupportsConditionInParens()),this.peekRegExp(n.Ident,/^(and|or)$/i)){var t=this.token.text.toLowerCase();while(this.acceptIdent(t))e.addChild(this._parseSupportsConditionInParens())}return this.finish(e)},e.prototype._parseSupportsConditionInParens=function(){var e=this.create(qe);if(this.accept(n.ParenthesisL))return this.prevToken&&(e.lParent=this.prevToken.offset),e.addChild(this._tryToParseDeclaration())||this._parseSupportsCondition()?this.accept(n.ParenthesisR)?(this.prevToken&&(e.rParent=this.prevToken.offset),this.finish(e)):this.finish(e,xt.RightParenthesisExpected,[n.ParenthesisR],[]):this.finish(e,xt.ConditionExpected);if(this.peek(n.Ident)){var t=this.mark();if(this.consumeToken(),!this.hasWhitespace()&&this.accept(n.ParenthesisL)){var r=1;while(this.token.type!==n.EOF&&0!==r)this.token.type===n.ParenthesisL?r++:this.token.type===n.ParenthesisR&&r--,this.consumeToken();return this.finish(e)}this.restoreAtMark(t)}return this.finish(e,xt.LeftParenthesisExpected,[],[n.ParenthesisL])},e.prototype._parseMediaDeclaration=function(e){return void 0===e&&(e=!1),e?this._tryParseRuleset(!0)||this._tryToParseDeclaration()||this._parseStylesheetStatement(!0):this._parseStylesheetStatement(!1)},e.prototype._parseMedia=function(e){if(void 0===e&&(e=!1),!this.peekKeyword("@media"))return null;var t=this.create(Be);return this.consumeToken(),t.addChild(this._parseMediaQueryList())?this._parseBody(t,this._parseMediaDeclaration.bind(this,e)):this.finish(t,xt.MediaQueryExpected)},e.prototype._parseMediaQueryList=function(){var e=this.create(je);if(!e.addChild(this._parseMediaQuery([n.CurlyL])))return this.finish(e,xt.MediaQueryExpected);while(this.accept(n.Comma))if(!e.addChild(this._parseMediaQuery([n.CurlyL])))return this.finish(e,xt.MediaQueryExpected);return this.finish(e)},e.prototype._parseMediaQuery=function(e){var t=this.create(Ke),r=!0,i=!1;if(!this.peek(n.ParenthesisL)){if(this.acceptIdent("only")||this.acceptIdent("not"),!t.addChild(this._parseIdent()))return null;i=!0,r=this.acceptIdent("and")}while(r)if(t.addChild(this._parseMediaContentStart()))r=this.acceptIdent("and");else{if(!this.accept(n.ParenthesisL))return i?this.finish(t,xt.LeftParenthesisExpected,[],e):null;if(!t.addChild(this._parseMediaFeatureName()))return this.finish(t,xt.IdentifierExpected,[],e);if(this.accept(n.Colon)&&!t.addChild(this._parseExpr()))return this.finish(t,xt.TermExpected,[],e);if(!this.accept(n.ParenthesisR))return this.finish(t,xt.RightParenthesisExpected,[],e);r=this.acceptIdent("and")}return this.finish(t)},e.prototype._parseMediaContentStart=function(){return null},e.prototype._parseMediaFeatureName=function(){return this._parseIdent()},e.prototype._parseMedium=function(){var e=this.create(ie);return e.addChild(this._parseIdent())?this.finish(e):null},e.prototype._parsePageDeclaration=function(){return this._parsePageMarginBox()||this._parseRuleSetDeclaration()},e.prototype._parsePage=function(){if(!this.peekKeyword("@page"))return null;var e=this.create(ze);if(this.consumeToken(),e.addChild(this._parsePageSelector()))while(this.accept(n.Comma))if(!e.addChild(this._parsePageSelector()))return this.finish(e,xt.IdentifierExpected);return this._parseBody(e,this._parsePageDeclaration.bind(this))},e.prototype._parsePageMarginBox=function(){if(!this.peek(n.AtKeyword))return null;var e=this.create(He);return this.acceptOneKeyword(ur)||this.markError(e,xt.UnknownAtRule,[],[n.CurlyL]),this._parseBody(e,this._parseRuleSetDeclaration.bind(this))},e.prototype._parsePageSelector=function(){if(!this.peek(n.Ident)&&!this.peek(n.Colon))return null;var e=this.create(ie);return e.addChild(this._parseIdent()),this.accept(n.Colon)&&!e.addChild(this._parseIdent())?this.finish(e,xt.IdentifierExpected):this.finish(e)},e.prototype._parseDocument=function(){if(!this.peekKeyword("@-moz-document"))return null;var e=this.create(We);return this.consumeToken(),this.resync([],[n.CurlyL]),this._parseBody(e,this._parseStylesheetStatement.bind(this))},e.prototype._parseUnknownAtRule=function(){if(!this.peek(n.AtKeyword))return null;var e=this.create(ut);e.addChild(this._parseUnknownAtRuleName());var t=function(){return 0===i&&0===o&&0===s},r=0,i=0,o=0,s=0;e:while(1){switch(this.token.type){case n.SemiColon:if(t())break e;break;case n.EOF:return i>0?this.finish(e,xt.RightCurlyExpected):s>0?this.finish(e,xt.RightSquareBracketExpected):o>0?this.finish(e,xt.RightParenthesisExpected):this.finish(e);case n.CurlyL:r++,i++;break;case n.CurlyR:if(i--,r>0&&0===i){if(this.consumeToken(),s>0)return this.finish(e,xt.RightSquareBracketExpected);if(o>0)return this.finish(e,xt.RightParenthesisExpected);break e}if(i<0){if(0===o&&0===s)break e;return this.finish(e,xt.LeftCurlyExpected)}break;case n.ParenthesisL:o++;break;case n.ParenthesisR:if(o--,o<0)return this.finish(e,xt.LeftParenthesisExpected);break;case n.BracketL:s++;break;case n.BracketR:if(s--,s<0)return this.finish(e,xt.LeftSquareBracketExpected);break}this.consumeToken()}return e},e.prototype._parseUnknownAtRuleName=function(){var e=this.create(ie);return this.accept(n.AtKeyword)?this.finish(e):e},e.prototype._parseOperator=function(){if(this.peekDelim("/")||this.peekDelim("*")||this.peekDelim("+")||this.peekDelim("-")||this.peek(n.Dashmatch)||this.peek(n.Includes)||this.peek(n.SubstringOperator)||this.peek(n.PrefixOperator)||this.peek(n.SuffixOperator)||this.peekDelim("=")){var e=this.createNode(J.Operator);return this.consumeToken(),this.finish(e)}return null},e.prototype._parseUnaryOperator=function(){if(!this.peekDelim("+")&&!this.peekDelim("-"))return null;var e=this.create(ie);return this.consumeToken(),this.finish(e)},e.prototype._parseCombinator=function(){if(this.peekDelim(">")){var e=this.create(ie);this.consumeToken();var t=this.mark();if(!this.hasWhitespace()&&this.acceptDelim(">")){if(!this.hasWhitespace()&&this.acceptDelim(">"))return e.type=J.SelectorCombinatorShadowPiercingDescendant,this.finish(e);this.restoreAtMark(t)}return e.type=J.SelectorCombinatorParent,this.finish(e)}if(this.peekDelim("+")){e=this.create(ie);return this.consumeToken(),e.type=J.SelectorCombinatorSibling,this.finish(e)}if(this.peekDelim("~")){e=this.create(ie);return this.consumeToken(),e.type=J.SelectorCombinatorAllSiblings,this.finish(e)}if(this.peekDelim("/")){e=this.create(ie);this.consumeToken();t=this.mark();if(!this.hasWhitespace()&&this.acceptIdent("deep")&&!this.hasWhitespace()&&this.acceptDelim("/"))return e.type=J.SelectorCombinatorShadowPiercingDescendant,this.finish(e);this.restoreAtMark(t)}return null},e.prototype._parseSimpleSelector=function(){var e=this.create(pe),t=0;e.addChild(this._parseElementName())&&t++;while((0===t||!this.hasWhitespace())&&e.addChild(this._parseSimpleSelectorBody()))t++;return t>0?this.finish(e):null},e.prototype._parseSimpleSelectorBody=function(){return this._parsePseudo()||this._parseHash()||this._parseClass()||this._parseAttrib()},e.prototype._parseSelectorIdent=function(){return this._parseIdent()},e.prototype._parseHash=function(){if(!this.peek(n.Hash)&&!this.peekDelim("#"))return null;var e=this.createNode(J.IdentifierSelector);if(this.acceptDelim("#")){if(this.hasWhitespace()||!e.addChild(this._parseSelectorIdent()))return this.finish(e,xt.IdentifierExpected)}else this.consumeToken();return this.finish(e)},e.prototype._parseClass=function(){if(!this.peekDelim("."))return null;var e=this.createNode(J.ClassSelector);return this.consumeToken(),this.hasWhitespace()||!e.addChild(this._parseSelectorIdent())?this.finish(e,xt.IdentifierExpected):this.finish(e)},e.prototype._parseElementName=function(){var e=this.mark(),t=this.createNode(J.ElementNameSelector);return t.addChild(this._parseNamespacePrefix()),t.addChild(this._parseSelectorIdent())||this.acceptDelim("*")?this.finish(t):(this.restoreAtMark(e),null)},e.prototype._parseNamespacePrefix=function(){var e=this.mark(),t=this.createNode(J.NamespacePrefix);return!t.addChild(this._parseIdent())&&this.acceptDelim("*"),this.acceptDelim("|")?this.finish(t):(this.restoreAtMark(e),null)},e.prototype._parseAttrib=function(){if(!this.peek(n.BracketL))return null;var e=this.create(Xe);return this.consumeToken(),e.setNamespacePrefix(this._parseNamespacePrefix()),e.setIdentifier(this._parseIdent())?(e.setOperator(this._parseOperator())&&(e.setValue(this._parseBinaryExpr()),this.acceptIdent("i")),this.accept(n.BracketR)?this.finish(e):this.finish(e,xt.RightSquareBracketExpected)):this.finish(e,xt.IdentifierExpected)},e.prototype._parsePseudo=function(){var e=this,t=this._tryParsePseudoIdentifier();if(t){if(!this.hasWhitespace()&&this.accept(n.ParenthesisL)){var r=function(){var t=e.create(ie);if(!t.addChild(e._parseSelector(!1)))return null;while(e.accept(n.Comma)&&t.addChild(e._parseSelector(!1)));return e.peek(n.ParenthesisR)?e.finish(t):null};if(t.addChild(this.try(r)||this._parseBinaryExpr()),!this.accept(n.ParenthesisR))return this.finish(t,xt.RightParenthesisExpected)}return this.finish(t)}return null},e.prototype._tryParsePseudoIdentifier=function(){if(!this.peek(n.Colon))return null;var e=this.mark(),t=this.createNode(J.PseudoSelector);return this.consumeToken(),this.hasWhitespace()?(this.restoreAtMark(e),null):(this.accept(n.Colon)&&this.hasWhitespace()&&this.markError(t,xt.IdentifierExpected),t.addChild(this._parseIdent())||this.markError(t,xt.IdentifierExpected),t)},e.prototype._tryParsePrio=function(){var e=this.mark(),t=this._parsePrio();return t||(this.restoreAtMark(e),null)},e.prototype._parsePrio=function(){if(!this.peek(n.Exclamation))return null;var e=this.createNode(J.Prio);return this.accept(n.Exclamation)&&this.acceptIdent("important")?this.finish(e):null},e.prototype._parseExpr=function(e){void 0===e&&(e=!1);var t=this.create(Ge);if(!t.addChild(this._parseBinaryExpr()))return null;while(1){if(this.peek(n.Comma)){if(e)return this.finish(t);this.consumeToken()}if(!t.addChild(this._parseBinaryExpr()))break}return this.finish(t)},e.prototype._parseNamedLine=function(){if(!this.peek(n.BracketL))return null;var e=this.createNode(J.GridLine);this.consumeToken();while(e.addChild(this._parseIdent()));return this.accept(n.BracketR)?this.finish(e):this.finish(e,xt.RightSquareBracketExpected)},e.prototype._parseBinaryExpr=function(e,t){var r=this.create(Qe);if(!r.setLeft(e||this._parseTerm()))return null;if(!r.setOperator(t||this._parseOperator()))return this.finish(r);if(!r.setRight(this._parseTerm()))return this.finish(r,xt.TermExpected);r=this.finish(r);var n=this._parseOperator();return n&&(r=this._parseBinaryExpr(r,n)),this.finish(r)},e.prototype._parseTerm=function(){var e=this.create(Je);return e.setOperator(this._parseUnaryOperator()),e.setExpression(this._parseURILiteral())||e.setExpression(this._parseFunction())||e.setExpression(this._parseIdent())||e.setExpression(this._parseStringLiteral())||e.setExpression(this._parseNumeric())||e.setExpression(this._parseHexColor())||e.setExpression(this._parseOperation())||e.setExpression(this._parseNamedLine())?this.finish(e):null},e.prototype._parseOperation=function(){if(!this.peek(n.ParenthesisL))return null;var e=this.create(ie);return this.consumeToken(),e.addChild(this._parseExpr()),this.accept(n.ParenthesisR)?this.finish(e):this.finish(e,xt.RightParenthesisExpected)},e.prototype._parseNumeric=function(){if(this.peek(n.Num)||this.peek(n.Percentage)||this.peek(n.Resolution)||this.peek(n.Length)||this.peek(n.EMS)||this.peek(n.EXS)||this.peek(n.Angle)||this.peek(n.Time)||this.peek(n.Dimension)||this.peek(n.Freq)){var e=this.create(rt);return this.consumeToken(),this.finish(e)}return null},e.prototype._parseStringLiteral=function(){if(!this.peek(n.String)&&!this.peek(n.BadString))return null;var e=this.createNode(J.StringLiteral);return this.consumeToken(),this.finish(e)},e.prototype._parseURILiteral=function(){if(!this.peekRegExp(n.Ident,/^url(-prefix)?$/i))return null;var e=this.mark(),t=this.createNode(J.URILiteral);return this.accept(n.Ident),this.hasWhitespace()||!this.peek(n.ParenthesisL)?(this.restoreAtMark(e),null):(this.scanner.inURL=!0,this.consumeToken(),t.addChild(this._parseURLArgument()),this.scanner.inURL=!1,this.accept(n.ParenthesisR)?this.finish(t):this.finish(t,xt.RightParenthesisExpected))},e.prototype._parseURLArgument=function(){var e=this.create(ie);return this.accept(n.String)||this.accept(n.BadString)||this.acceptUnquotedString()?this.finish(e):null},e.prototype._parseIdent=function(e){if(!this.peek(n.Ident))return null;var t=this.create(se);return e&&(t.referenceTypes=e),t.isCustomProperty=this.peekRegExp(n.Ident,/^--/),this.consumeToken(),this.finish(t)},e.prototype._parseFunction=function(){var e=this.mark(),t=this.create(ke);if(!t.setIdentifier(this._parseFunctionIdentifier()))return null;if(this.hasWhitespace()||!this.accept(n.ParenthesisL))return this.restoreAtMark(e),null;if(t.getArguments().addChild(this._parseFunctionArgument()))while(this.accept(n.Comma)){if(this.peek(n.ParenthesisR))break;t.getArguments().addChild(this._parseFunctionArgument())||this.markError(t,xt.ExpressionExpected)}return this.accept(n.ParenthesisR)?this.finish(t):this.finish(t,xt.RightParenthesisExpected)},e.prototype._parseFunctionIdentifier=function(){if(!this.peek(n.Ident))return null;var e=this.create(se);if(e.referenceTypes=[X.Function],this.acceptIdent("progid")){if(this.accept(n.Colon))while(this.accept(n.Ident)&&this.acceptDelim("."));return this.finish(e)}return this.consumeToken(),this.finish(e)},e.prototype._parseFunctionArgument=function(){var e=this.create(Ce);return e.setValue(this._parseExpr(!0))?this.finish(e):null},e.prototype._parseHexColor=function(){if(this.peekRegExp(n.Hash,/^#([A-Fa-f0-9]{3}|[A-Fa-f0-9]{4}|[A-Fa-f0-9]{6}|[A-Fa-f0-9]{8})$/g)){var e=this.create(Ze);return this.consumeToken(),this.finish(e)}return null},e}();function fr(e,t){var r=0,n=e.length;if(0===n)return 0;while(r<n){var i=Math.floor((r+n)/2);t(e[i])?n=i:r=i+1}return r}function dr(e,t){return-1!==e.indexOf(t)}function mr(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var r=[],n=0,i=e;n<i.length;n++)for(var o=i[n],s=0,a=o;s<a.length;s++){var c=a[s];dr(r,c)||r.push(c)}return r}var gr,yr,vr,br,kr,xr,Cr,_r,wr,Pr,Sr,Er,Ir,Rr,Ar,Dr,Tr,Fr,Mr,Or,Vr=function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},e(t,r)};return function(t,r){function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Lr=function(){function e(e,t){this.offset=e,this.length=t,this.symbols=[],this.parent=null,this.children=[]}return e.prototype.addChild=function(e){this.children.push(e),e.setParent(this)},e.prototype.setParent=function(e){this.parent=e},e.prototype.findScope=function(e,t){return void 0===t&&(t=0),this.offset<=e&&this.offset+this.length>e+t||this.offset===e&&this.length===t?this.findInScope(e,t):null},e.prototype.findInScope=function(e,t){void 0===t&&(t=0);var r=e+t,n=fr(this.children,(function(e){return e.offset>r}));if(0===n)return this;var i=this.children[n-1];return i.offset<=e&&i.offset+i.length>=e+t?i.findInScope(e,t):this},e.prototype.addSymbol=function(e){this.symbols.push(e)},e.prototype.getSymbol=function(e,t){for(var r=0;r<this.symbols.length;r++){var n=this.symbols[r];if(n.name===e&&n.type===t)return n}return null},e.prototype.getSymbols=function(){return this.symbols},e}(),Nr=function(e){function t(){return e.call(this,0,Number.MAX_VALUE)||this}return Vr(t,e),t}(Lr),$r=function(){function e(e,t,r,n){this.name=e,this.value=t,this.node=r,this.type=n}return e}(),Br=function(){function e(e){this.scope=e}return e.prototype.addSymbol=function(e,t,r,n){if(-1!==e.offset){var i=this.scope.findScope(e.offset,e.length);i&&i.addSymbol(new $r(t,r,e,n))}},e.prototype.addScope=function(e){if(-1!==e.offset){var t=this.scope.findScope(e.offset,e.length);if(t&&(t.offset!==e.offset||t.length!==e.length)){var r=new Lr(e.offset,e.length);return t.addChild(r),r}return t}return null},e.prototype.addSymbolToChildScope=function(e,t,r,n,i){if(e&&-1!==e.offset){var o=this.addScope(e);o&&o.addSymbol(new $r(r,n,t,i))}},e.prototype.visitNode=function(e){switch(e.type){case J.Keyframe:return this.addSymbol(e,e.getName(),void 0,X.Keyframe),!0;case J.CustomPropertyDeclaration:return this.visitCustomPropertyDeclarationNode(e);case J.VariableDeclaration:return this.visitVariableDeclarationNode(e);case J.Ruleset:return this.visitRuleSet(e);case J.MixinDeclaration:return this.addSymbol(e,e.getName(),void 0,X.Mixin),!0;case J.FunctionDeclaration:return this.addSymbol(e,e.getName(),void 0,X.Function),!0;case J.FunctionParameter:return this.visitFunctionParameterNode(e);case J.Declarations:return this.addScope(e),!0;case J.For:var t=e,r=t.getDeclarations();return r&&t.variable&&this.addSymbolToChildScope(r,t.variable,t.variable.getName(),void 0,X.Variable),!0;case J.Each:var n=e,i=n.getDeclarations();if(i)for(var o=n.getVariables().getChildren(),s=0,a=o;s<a.length;s++){var c=a[s];this.addSymbolToChildScope(i,c,c.getName(),void 0,X.Variable)}return!0}return!0},e.prototype.visitRuleSet=function(e){var t=this.scope.findScope(e.offset,e.length);if(t)for(var r=0,n=e.getSelectors().getChildren();r<n.length;r++){var i=n[r];i instanceof le&&1===i.getChildren().length&&t.addSymbol(new $r(i.getChild(0).getText(),void 0,i,X.Rule))}return!0},e.prototype.visitVariableDeclarationNode=function(e){var t=e.getValue()?e.getValue().getText():void 0;return this.addSymbol(e,e.getName(),t,X.Variable),!0},e.prototype.visitFunctionParameterNode=function(e){var t=e.getParent().getDeclarations();if(t){var r=e.getDefaultValue(),n=r?r.getText():void 0;this.addSymbolToChildScope(t,e,e.getName(),n,X.Variable)}return!0},e.prototype.visitCustomPropertyDeclarationNode=function(e){var t=e.getValue()?e.getValue().getText():"";return this.addCSSVariable(e.getProperty(),e.getProperty().getName(),t,X.Variable),!0},e.prototype.addCSSVariable=function(e,t,r,n){-1!==e.offset&&this.scope.addSymbol(new $r(t,r,e,n))},e}(),Ur=function(){function e(e){this.global=new Nr,e.acceptVisitor(new Br(this.global))}return e.prototype.findSymbolsAtOffset=function(e,t){var r=this.global.findScope(e,0),n=[],i={};while(r){for(var o=r.getSymbols(),s=0;s<o.length;s++){var a=o[s];a.type!==t||i[a.name]||(n.push(a),i[a.name]=!0)}r=r.parent}return n},e.prototype.internalFindSymbol=function(e,t){var r=e;if(e.parent instanceof xe&&e.parent.getParent()instanceof ue&&(r=e.parent.getParent().getDeclarations()),e.parent instanceof Ce&&e.parent.getParent()instanceof ke){var n=e.parent.getParent().getIdentifier();if(n){var i=this.internalFindSymbol(n,[X.Function]);i&&(r=i.node.getDeclarations())}}if(!r)return null;var o=e.getText(),s=this.global.findScope(r.offset,r.length);while(s){for(var a=0;a<t.length;a++){var c=t[a],u=s.getSymbol(o,c);if(u)return u}s=s.parent}return null},e.prototype.evaluateReferenceTypes=function(e){if(e instanceof se){var t=e.referenceTypes;if(t)return t;if(e.isCustomProperty)return[X.Variable];var r=re(e);if(r){var n=r.getNonPrefixedPropertyName();if(("animation"===n||"animation-name"===n)&&r.getValue()&&r.getValue().offset===e.offset)return[X.Keyframe]}}else if(e instanceof ot)return[X.Variable];var i=e.findAParent(J.Selector,J.ExtendsReference);return i?[X.Rule]:null},e.prototype.findSymbolFromNode=function(e){if(!e)return null;while(e.type===J.Interpolation)e=e.getParent();var t=this.evaluateReferenceTypes(e);return t?this.internalFindSymbol(e,t):null},e.prototype.matchesSymbol=function(e,t){if(!e)return!1;while(e.type===J.Interpolation)e=e.getParent();if(!e.matches(t.name))return!1;var r=this.evaluateReferenceTypes(e);if(!r||-1===r.indexOf(t.type))return!1;var n=this.internalFindSymbol(e,r);return n===t},e.prototype.findSymbol=function(e,t,r){var n=this.global.findScope(r);while(n){var i=n.getSymbol(e,t);if(i)return i;n=n.parent}return null},e}();function Wr(e,t){if(e.length<t.length)return!1;for(var r=0;r<t.length;r++)if(e[r]!==t[r])return!1;return!0}function jr(e,t){var r=e.length-t.length;return r>0?e.lastIndexOf(t)===r:0===r&&e===t}function Kr(e,t,r){void 0===r&&(r=4);var n=Math.abs(e.length-t.length);if(n>r)return 0;var i,o,s=[],a=[];for(i=0;i<t.length+1;++i)a.push(0);for(i=0;i<e.length+1;++i)s.push(a);for(i=1;i<e.length+1;++i)for(o=1;o<t.length+1;++o)e[i-1]===t[o-1]?s[i][o]=s[i-1][o-1]+1:s[i][o]=Math.max(s[i-1][o],s[i][o-1]);return s[e.length][t.length]-Math.sqrt(n)}function qr(e,t){return void 0===t&&(t=!0),e?e.length<140?e:e.slice(0,140)+(t?"…":""):""}(function(e){function t(e,t){return{line:e,character:t}}function r(e){var t=e;return Cn.objectLiteral(t)&&Cn.number(t.line)&&Cn.number(t.character)}e.create=t,e.is=r})(gr||(gr={})),function(e){function t(e,t,r,n){if(Cn.number(e)&&Cn.number(t)&&Cn.number(r)&&Cn.number(n))return{start:gr.create(e,t),end:gr.create(r,n)};if(gr.is(e)&&gr.is(t))return{start:e,end:t};throw new Error("Range#create called with invalid arguments["+e+", "+t+", "+r+", "+n+"]")}function r(e){var t=e;return Cn.objectLiteral(t)&&gr.is(t.start)&&gr.is(t.end)}e.create=t,e.is=r}(yr||(yr={})),function(e){function t(e,t){return{uri:e,range:t}}function r(e){var t=e;return Cn.defined(t)&&yr.is(t.range)&&(Cn.string(t.uri)||Cn.undefined(t.uri))}e.create=t,e.is=r}(vr||(vr={})),function(e){function t(e,t,r,n){return{targetUri:e,targetRange:t,targetSelectionRange:r,originSelectionRange:n}}function r(e){var t=e;return Cn.defined(t)&&yr.is(t.targetRange)&&Cn.string(t.targetUri)&&(yr.is(t.targetSelectionRange)||Cn.undefined(t.targetSelectionRange))&&(yr.is(t.originSelectionRange)||Cn.undefined(t.originSelectionRange))}e.create=t,e.is=r}(br||(br={})),function(e){function t(e,t,r,n){return{red:e,green:t,blue:r,alpha:n}}function r(e){var t=e;return Cn.number(t.red)&&Cn.number(t.green)&&Cn.number(t.blue)&&Cn.number(t.alpha)}e.create=t,e.is=r}(kr||(kr={})),function(e){function t(e,t){return{range:e,color:t}}function r(e){var t=e;return yr.is(t.range)&&kr.is(t.color)}e.create=t,e.is=r}(xr||(xr={})),function(e){function t(e,t,r){return{label:e,textEdit:t,additionalTextEdits:r}}function r(e){var t=e;return Cn.string(t.label)&&(Cn.undefined(t.textEdit)||Ar.is(t))&&(Cn.undefined(t.additionalTextEdits)||Cn.typedArray(t.additionalTextEdits,Ar.is))}e.create=t,e.is=r}(Cr||(Cr={})),function(e){e["Comment"]="comment",e["Imports"]="imports",e["Region"]="region"}(_r||(_r={})),function(e){function t(e,t,r,n,i){var o={startLine:e,endLine:t};return Cn.defined(r)&&(o.startCharacter=r),Cn.defined(n)&&(o.endCharacter=n),Cn.defined(i)&&(o.kind=i),o}function r(e){var t=e;return Cn.number(t.startLine)&&Cn.number(t.startLine)&&(Cn.undefined(t.startCharacter)||Cn.number(t.startCharacter))&&(Cn.undefined(t.endCharacter)||Cn.number(t.endCharacter))&&(Cn.undefined(t.kind)||Cn.string(t.kind))}e.create=t,e.is=r}(wr||(wr={})),function(e){function t(e,t){return{location:e,message:t}}function r(e){var t=e;return Cn.defined(t)&&vr.is(t.location)&&Cn.string(t.message)}e.create=t,e.is=r}(Pr||(Pr={})),function(e){e.Error=1,e.Warning=2,e.Information=3,e.Hint=4}(Sr||(Sr={})),function(e){e.Unnecessary=1,e.Deprecated=2}(Er||(Er={})),function(e){function t(e,t,r,n,i,o){var s={range:e,message:t};return Cn.defined(r)&&(s.severity=r),Cn.defined(n)&&(s.code=n),Cn.defined(i)&&(s.source=i),Cn.defined(o)&&(s.relatedInformation=o),s}function r(e){var t=e;return Cn.defined(t)&&yr.is(t.range)&&Cn.string(t.message)&&(Cn.number(t.severity)||Cn.undefined(t.severity))&&(Cn.number(t.code)||Cn.string(t.code)||Cn.undefined(t.code))&&(Cn.string(t.source)||Cn.undefined(t.source))&&(Cn.undefined(t.relatedInformation)||Cn.typedArray(t.relatedInformation,Pr.is))}e.create=t,e.is=r}(Ir||(Ir={})),function(e){function t(e,t){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];var i={title:e,command:t};return Cn.defined(r)&&r.length>0&&(i.arguments=r),i}function r(e){var t=e;return Cn.defined(t)&&Cn.string(t.title)&&Cn.string(t.command)}e.create=t,e.is=r}(Rr||(Rr={})),function(e){function t(e,t){return{range:e,newText:t}}function r(e,t){return{range:{start:e,end:e},newText:t}}function n(e){return{range:e,newText:""}}function i(e){var t=e;return Cn.objectLiteral(t)&&Cn.string(t.newText)&&yr.is(t.range)}e.replace=t,e.insert=r,e.del=n,e.is=i}(Ar||(Ar={})),function(e){function t(e,t){return{textDocument:e,edits:t}}function r(e){var t=e;return Cn.defined(t)&&Hr.is(t.textDocument)&&Array.isArray(t.edits)}e.create=t,e.is=r}(Dr||(Dr={})),function(e){function t(e,t){var r={kind:"create",uri:e};return void 0===t||void 0===t.overwrite&&void 0===t.ignoreIfExists||(r.options=t),r}function r(e){var t=e;return t&&"create"===t.kind&&Cn.string(t.uri)&&(void 0===t.options||(void 0===t.options.overwrite||Cn.boolean(t.options.overwrite))&&(void 0===t.options.ignoreIfExists||Cn.boolean(t.options.ignoreIfExists)))}e.create=t,e.is=r}(Tr||(Tr={})),function(e){function t(e,t,r){var n={kind:"rename",oldUri:e,newUri:t};return void 0===r||void 0===r.overwrite&&void 0===r.ignoreIfExists||(n.options=r),n}function r(e){var t=e;return t&&"rename"===t.kind&&Cn.string(t.oldUri)&&Cn.string(t.newUri)&&(void 0===t.options||(void 0===t.options.overwrite||Cn.boolean(t.options.overwrite))&&(void 0===t.options.ignoreIfExists||Cn.boolean(t.options.ignoreIfExists)))}e.create=t,e.is=r}(Fr||(Fr={})),function(e){function t(e,t){var r={kind:"delete",uri:e};return void 0===t||void 0===t.recursive&&void 0===t.ignoreIfNotExists||(r.options=t),r}function r(e){var t=e;return t&&"delete"===t.kind&&Cn.string(t.uri)&&(void 0===t.options||(void 0===t.options.recursive||Cn.boolean(t.options.recursive))&&(void 0===t.options.ignoreIfNotExists||Cn.boolean(t.options.ignoreIfNotExists)))}e.create=t,e.is=r}(Mr||(Mr={})),function(e){function t(e){var t=e;return t&&(void 0!==t.changes||void 0!==t.documentChanges)&&(void 0===t.documentChanges||t.documentChanges.every((function(e){return Cn.string(e.kind)?Tr.is(e)||Fr.is(e)||Mr.is(e):Dr.is(e)})))}e.is=t}(Or||(Or={}));var zr,Hr,Gr,Qr,Jr,Xr,Zr,Yr,en,tn,rn,nn,on,sn,an,cn,un,hn,ln,pn,fn,dn,mn,gn,yn,vn,bn,kn=function(){function e(e){this.edits=e}return e.prototype.insert=function(e,t){this.edits.push(Ar.insert(e,t))},e.prototype.replace=function(e,t){this.edits.push(Ar.replace(e,t))},e.prototype.delete=function(e){this.edits.push(Ar.del(e))},e.prototype.add=function(e){this.edits.push(e)},e.prototype.all=function(){return this.edits},e.prototype.clear=function(){this.edits.splice(0,this.edits.length)},e}();(function(){function e(e){var t=this;this._textEditChanges=Object.create(null),e&&(this._workspaceEdit=e,e.documentChanges?e.documentChanges.forEach((function(e){if(Dr.is(e)){var r=new kn(e.edits);t._textEditChanges[e.textDocument.uri]=r}})):e.changes&&Object.keys(e.changes).forEach((function(r){var n=new kn(e.changes[r]);t._textEditChanges[r]=n})))}Object.defineProperty(e.prototype,"edit",{get:function(){return this._workspaceEdit},enumerable:!0,configurable:!0}),e.prototype.getTextEditChange=function(e){if(Hr.is(e)){if(this._workspaceEdit||(this._workspaceEdit={documentChanges:[]}),!this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.");var t=e,r=this._textEditChanges[t.uri];if(!r){var n=[],i={textDocument:t,edits:n};this._workspaceEdit.documentChanges.push(i),r=new kn(n),this._textEditChanges[t.uri]=r}return r}if(this._workspaceEdit||(this._workspaceEdit={changes:Object.create(null)}),!this._workspaceEdit.changes)throw new Error("Workspace edit is not configured for normal text edit changes.");r=this._textEditChanges[e];if(!r){n=[];this._workspaceEdit.changes[e]=n,r=new kn(n),this._textEditChanges[e]=r}return r},e.prototype.createFile=function(e,t){this.checkDocumentChanges(),this._workspaceEdit.documentChanges.push(Tr.create(e,t))},e.prototype.renameFile=function(e,t,r){this.checkDocumentChanges(),this._workspaceEdit.documentChanges.push(Fr.create(e,t,r))},e.prototype.deleteFile=function(e,t){this.checkDocumentChanges(),this._workspaceEdit.documentChanges.push(Mr.create(e,t))},e.prototype.checkDocumentChanges=function(){if(!this._workspaceEdit||!this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.")}})();(function(e){function t(e){return{uri:e}}function r(e){var t=e;return Cn.defined(t)&&Cn.string(t.uri)}e.create=t,e.is=r})(zr||(zr={})),function(e){function t(e,t){return{uri:e,version:t}}function r(e){var t=e;return Cn.defined(t)&&Cn.string(t.uri)&&(null===t.version||Cn.number(t.version))}e.create=t,e.is=r}(Hr||(Hr={})),function(e){function t(e,t,r,n){return{uri:e,languageId:t,version:r,text:n}}function r(e){var t=e;return Cn.defined(t)&&Cn.string(t.uri)&&Cn.string(t.languageId)&&Cn.number(t.version)&&Cn.string(t.text)}e.create=t,e.is=r}(Gr||(Gr={})),function(e){e.PlainText="plaintext",e.Markdown="markdown"}(Qr||(Qr={})),function(e){function t(t){var r=t;return r===e.PlainText||r===e.Markdown}e.is=t}(Qr||(Qr={})),function(e){function t(e){var t=e;return Cn.objectLiteral(e)&&Qr.is(t.kind)&&Cn.string(t.value)}e.is=t}(Jr||(Jr={})),function(e){e.Text=1,e.Method=2,e.Function=3,e.Constructor=4,e.Field=5,e.Variable=6,e.Class=7,e.Interface=8,e.Module=9,e.Property=10,e.Unit=11,e.Value=12,e.Enum=13,e.Keyword=14,e.Snippet=15,e.Color=16,e.File=17,e.Reference=18,e.Folder=19,e.EnumMember=20,e.Constant=21,e.Struct=22,e.Event=23,e.Operator=24,e.TypeParameter=25}(Xr||(Xr={})),function(e){e.PlainText=1,e.Snippet=2}(Zr||(Zr={})),function(e){e.Deprecated=1}(Yr||(Yr={})),function(e){function t(e){return{label:e}}e.create=t}(en||(en={})),function(e){function t(e,t){return{items:e||[],isIncomplete:!!t}}e.create=t}(tn||(tn={})),function(e){function t(e){return e.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}function r(e){var t=e;return Cn.string(t)||Cn.objectLiteral(t)&&Cn.string(t.language)&&Cn.string(t.value)}e.fromPlainText=t,e.is=r}(rn||(rn={})),function(e){function t(e){var t=e;return!!t&&Cn.objectLiteral(t)&&(Jr.is(t.contents)||rn.is(t.contents)||Cn.typedArray(t.contents,rn.is))&&(void 0===e.range||yr.is(e.range))}e.is=t}(nn||(nn={})),function(e){function t(e,t){return t?{label:e,documentation:t}:{label:e}}e.create=t}(on||(on={})),function(e){function t(e,t){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];var i={label:e};return Cn.defined(t)&&(i.documentation=t),Cn.defined(r)?i.parameters=r:i.parameters=[],i}e.create=t}(sn||(sn={})),function(e){e.Text=1,e.Read=2,e.Write=3}(an||(an={})),function(e){function t(e,t){var r={range:e};return Cn.number(t)&&(r.kind=t),r}e.create=t}(cn||(cn={})),function(e){e.File=1,e.Module=2,e.Namespace=3,e.Package=4,e.Class=5,e.Method=6,e.Property=7,e.Field=8,e.Constructor=9,e.Enum=10,e.Interface=11,e.Function=12,e.Variable=13,e.Constant=14,e.String=15,e.Number=16,e.Boolean=17,e.Array=18,e.Object=19,e.Key=20,e.Null=21,e.EnumMember=22,e.Struct=23,e.Event=24,e.Operator=25,e.TypeParameter=26}(un||(un={})),function(e){e.Deprecated=1}(hn||(hn={})),function(e){function t(e,t,r,n,i){var o={name:e,kind:t,location:{uri:n,range:r}};return i&&(o.containerName=i),o}e.create=t}(ln||(ln={})),function(e){function t(e,t,r,n,i,o){var s={name:e,detail:t,kind:r,range:n,selectionRange:i};return void 0!==o&&(s.children=o),s}function r(e){var t=e;return t&&Cn.string(t.name)&&Cn.number(t.kind)&&yr.is(t.range)&&yr.is(t.selectionRange)&&(void 0===t.detail||Cn.string(t.detail))&&(void 0===t.deprecated||Cn.boolean(t.deprecated))&&(void 0===t.children||Array.isArray(t.children))}e.create=t,e.is=r}(pn||(pn={})),function(e){e.Empty="",e.QuickFix="quickfix",e.Refactor="refactor",e.RefactorExtract="refactor.extract",e.RefactorInline="refactor.inline",e.RefactorRewrite="refactor.rewrite",e.Source="source",e.SourceOrganizeImports="source.organizeImports",e.SourceFixAll="source.fixAll"}(fn||(fn={})),function(e){function t(e,t){var r={diagnostics:e};return void 0!==t&&null!==t&&(r.only=t),r}function r(e){var t=e;return Cn.defined(t)&&Cn.typedArray(t.diagnostics,Ir.is)&&(void 0===t.only||Cn.typedArray(t.only,Cn.string))}e.create=t,e.is=r}(dn||(dn={})),function(e){function t(e,t,r){var n={title:e};return Rr.is(t)?n.command=t:n.edit=t,void 0!==r&&(n.kind=r),n}function r(e){var t=e;return t&&Cn.string(t.title)&&(void 0===t.diagnostics||Cn.typedArray(t.diagnostics,Ir.is))&&(void 0===t.kind||Cn.string(t.kind))&&(void 0!==t.edit||void 0!==t.command)&&(void 0===t.command||Rr.is(t.command))&&(void 0===t.isPreferred||Cn.boolean(t.isPreferred))&&(void 0===t.edit||Or.is(t.edit))}e.create=t,e.is=r}(mn||(mn={})),function(e){function t(e,t){var r={range:e};return Cn.defined(t)&&(r.data=t),r}function r(e){var t=e;return Cn.defined(t)&&yr.is(t.range)&&(Cn.undefined(t.command)||Rr.is(t.command))}e.create=t,e.is=r}(gn||(gn={})),function(e){function t(e,t){return{tabSize:e,insertSpaces:t}}function r(e){var t=e;return Cn.defined(t)&&Cn.number(t.tabSize)&&Cn.boolean(t.insertSpaces)}e.create=t,e.is=r}(yn||(yn={})),function(e){function t(e,t,r){return{range:e,target:t,data:r}}function r(e){var t=e;return Cn.defined(t)&&yr.is(t.range)&&(Cn.undefined(t.target)||Cn.string(t.target))}e.create=t,e.is=r}(vn||(vn={})),function(e){function t(e,t){return{range:e,parent:t}}function r(t){var r=t;return void 0!==r&&yr.is(r.range)&&(void 0===r.parent||e.is(r.parent))}e.create=t,e.is=r}(bn||(bn={}));var xn;(function(e){function t(e,t,r,n){return new _n(e,t,r,n)}function r(e){var t=e;return!!(Cn.defined(t)&&Cn.string(t.uri)&&(Cn.undefined(t.languageId)||Cn.string(t.languageId))&&Cn.number(t.lineCount)&&Cn.func(t.getText)&&Cn.func(t.positionAt)&&Cn.func(t.offsetAt))}function n(e,t){for(var r=e.getText(),n=i(t,(function(e,t){var r=e.range.start.line-t.range.start.line;return 0===r?e.range.start.character-t.range.start.character:r})),o=r.length,s=n.length-1;s>=0;s--){var a=n[s],c=e.offsetAt(a.range.start),u=e.offsetAt(a.range.end);if(!(u<=o))throw new Error("Overlapping edit");r=r.substring(0,c)+a.newText+r.substring(u,r.length),o=c}return r}function i(e,t){if(e.length<=1)return e;var r=e.length/2|0,n=e.slice(0,r),o=e.slice(r);i(n,t),i(o,t);var s=0,a=0,c=0;while(s<n.length&&a<o.length){var u=t(n[s],o[a]);e[c++]=u<=0?n[s++]:o[a++]}while(s<n.length)e[c++]=n[s++];while(a<o.length)e[c++]=o[a++];return e}e.create=t,e.is=r,e.applyEdits=n})(xn||(xn={}));var Cn,_n=function(){function e(e,t,r,n){this._uri=e,this._languageId=t,this._version=r,this._content=n,this._lineOffsets=void 0}return Object.defineProperty(e.prototype,"uri",{get:function(){return this._uri},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"languageId",{get:function(){return this._languageId},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"version",{get:function(){return this._version},enumerable:!0,configurable:!0}),e.prototype.getText=function(e){if(e){var t=this.offsetAt(e.start),r=this.offsetAt(e.end);return this._content.substring(t,r)}return this._content},e.prototype.update=function(e,t){this._content=e.text,this._version=t,this._lineOffsets=void 0},e.prototype.getLineOffsets=function(){if(void 0===this._lineOffsets){for(var e=[],t=this._content,r=!0,n=0;n<t.length;n++){r&&(e.push(n),r=!1);var i=t.charAt(n);r="\r"===i||"\n"===i,"\r"===i&&n+1<t.length&&"\n"===t.charAt(n+1)&&n++}r&&t.length>0&&e.push(t.length),this._lineOffsets=e}return this._lineOffsets},e.prototype.positionAt=function(e){e=Math.max(Math.min(e,this._content.length),0);var t=this.getLineOffsets(),r=0,n=t.length;if(0===n)return gr.create(0,e);while(r<n){var i=Math.floor((r+n)/2);t[i]>e?n=i:r=i+1}var o=r-1;return gr.create(o,e-t[o])},e.prototype.offsetAt=function(e){var t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;var r=t[e.line],n=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(r+e.character,n),r)},Object.defineProperty(e.prototype,"lineCount",{get:function(){return this.getLineOffsets().length},enumerable:!0,configurable:!0}),e}();(function(e){var t=Object.prototype.toString;function r(e){return"undefined"!==typeof e}function n(e){return"undefined"===typeof e}function i(e){return!0===e||!1===e}function o(e){return"[object String]"===t.call(e)}function s(e){return"[object Number]"===t.call(e)}function a(e){return"[object Function]"===t.call(e)}function c(e){return null!==e&&"object"===typeof e}function u(e,t){return Array.isArray(e)&&e.every(t)}e.defined=r,e.undefined=n,e.boolean=i,e.string=o,e.number=s,e.func=a,e.objectLiteral=c,e.typedArray=u})(Cn||(Cn={}));var wn,Pn,Sn,En=function(){function e(e,t,r,n){this._uri=e,this._languageId=t,this._version=r,this._content=n,this._lineOffsets=void 0}return Object.defineProperty(e.prototype,"uri",{get:function(){return this._uri},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"languageId",{get:function(){return this._languageId},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"version",{get:function(){return this._version},enumerable:!0,configurable:!0}),e.prototype.getText=function(e){if(e){var t=this.offsetAt(e.start),r=this.offsetAt(e.end);return this._content.substring(t,r)}return this._content},e.prototype.update=function(t,r){for(var n=0,i=t;n<i.length;n++){var o=i[n];if(e.isIncremental(o)){var s=An(o.range),a=this.offsetAt(s.start),c=this.offsetAt(s.end);this._content=this._content.substring(0,a)+o.text+this._content.substring(c,this._content.length);var u=Math.max(s.start.line,0),h=Math.max(s.end.line,0),l=this._lineOffsets,p=Rn(o.text,!1,a);if(h-u===p.length)for(var f=0,d=p.length;f<d;f++)l[f+u+1]=p[f];else p.length<1e4?l.splice.apply(l,[u+1,h-u].concat(p)):this._lineOffsets=l=l.slice(0,u+1).concat(p,l.slice(h+1));var m=o.text.length-(c-a);if(0!==m)for(f=u+1+p.length,d=l.length;f<d;f++)l[f]=l[f]+m}else{if(!e.isFull(o))throw new Error("Unknown change event received");this._content=o.text,this._lineOffsets=void 0}}this._version=r},e.prototype.getLineOffsets=function(){return void 0===this._lineOffsets&&(this._lineOffsets=Rn(this._content,!0)),this._lineOffsets},e.prototype.positionAt=function(e){e=Math.max(Math.min(e,this._content.length),0);var t=this.getLineOffsets(),r=0,n=t.length;if(0===n)return{line:0,character:e};while(r<n){var i=Math.floor((r+n)/2);t[i]>e?n=i:r=i+1}var o=r-1;return{line:o,character:e-t[o]}},e.prototype.offsetAt=function(e){var t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;var r=t[e.line],n=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(r+e.character,n),r)},Object.defineProperty(e.prototype,"lineCount",{get:function(){return this.getLineOffsets().length},enumerable:!0,configurable:!0}),e.isIncremental=function(e){var t=e;return void 0!==t&&null!==t&&"string"===typeof t.text&&void 0!==t.range&&(void 0===t.rangeLength||"number"===typeof t.rangeLength)},e.isFull=function(e){var t=e;return void 0!==t&&null!==t&&"string"===typeof t.text&&void 0===t.range&&void 0===t.rangeLength},e}();function In(e,t){if(e.length<=1)return e;var r=e.length/2|0,n=e.slice(0,r),i=e.slice(r);In(n,t),In(i,t);var o=0,s=0,a=0;while(o<n.length&&s<i.length){var c=t(n[o],i[s]);e[a++]=c<=0?n[o++]:i[s++]}while(o<n.length)e[a++]=n[o++];while(s<i.length)e[a++]=i[s++];return e}function Rn(e,t,r){void 0===r&&(r=0);for(var n=t?[r]:[],i=0;i<e.length;i++){var o=e.charCodeAt(i);13!==o&&10!==o||(13===o&&i+1<e.length&&10===e.charCodeAt(i+1)&&i++,n.push(r+i+1))}return n}function An(e){var t=e.start,r=e.end;return t.line>r.line||t.line===r.line&&t.character>r.character?{start:r,end:t}:e}function Dn(e){var t=An(e.range);return t!==e.range?{newText:e.newText,range:t}:e}(function(e){function t(e,t,r,n){return new En(e,t,r,n)}function r(e,t,r){if(e instanceof En)return e.update(t,r),e;throw new Error("TextDocument.update: document must be created by TextDocument.create")}function n(e,t){for(var r=e.getText(),n=In(t.map(Dn),(function(e,t){var r=e.range.start.line-t.range.start.line;return 0===r?e.range.start.character-t.range.start.character:r})),i=r.length,o=n.length-1;o>=0;o--){var s=n[o],a=e.offsetAt(s.range.start),c=e.offsetAt(s.range.end);if(!(c<=i))throw new Error("Overlapping edit");r=r.substring(0,a)+s.newText+r.substring(c,r.length),i=a}return r}e.create=t,e.update=r,e.applyEdits=n})(wn||(wn={})),function(e){e.LATEST={textDocument:{completion:{completionItem:{documentationFormat:[Qr.Markdown,Qr.PlainText]}},hover:{contentFormat:[Qr.Markdown,Qr.PlainText]}}}}(Pn||(Pn={})),function(e){e[e["Unknown"]=0]="Unknown",e[e["File"]=1]="File",e[e["Directory"]=2]="Directory",e[e["SymbolicLink"]=64]="SymbolicLink"}(Sn||(Sn={}));var Tn,Fn=vt(),Mn=Zr.Snippet;(function(e){e["Enums"]=" ",e["Normal"]="d",e["VendorPrefixed"]="x",e["Term"]="y",e["Variable"]="z"})(Tn||(Tn={}));var On=function(){function e(e,t){void 0===e&&(e=null),this.variablePrefix=e,this.clientCapabilities=t,this.completionParticipants=[],this.valueTypes=[J.Identifier,J.Value,J.StringLiteral,J.URILiteral,J.NumericValue,J.HexColorValue,J.VariableName,J.Prio]}return e.prototype.configure=function(e){this.settings=e},e.prototype.getSymbolContext=function(){return this.symbolContext||(this.symbolContext=new Ur(this.styleSheet)),this.symbolContext},e.prototype.setCompletionParticipants=function(e){this.completionParticipants=e||[]},e.prototype.doComplete=function(e,t,r){this.offset=e.offsetAt(t),this.position=t,this.currentWord=Wn(e,this.offset),this.defaultReplaceRange=yr.create(gr.create(this.position.line,this.position.character-this.currentWord.length),this.position),this.textDocument=e,this.styleSheet=r;try{var n={isIncomplete:!1,items:[]};this.nodePath=te(this.styleSheet,this.offset);for(var i=this.nodePath.length-1;i>=0;i--){var o=this.nodePath[i];if(o instanceof ve)this.getCompletionsForDeclarationProperty(o.getParent(),n);else if(o instanceof Ge)o.parent instanceof it?this.getVariableProposals(null,n):this.getCompletionsForExpression(o,n);else if(o instanceof pe){var s=o.findAParent(J.ExtendsReference,J.Ruleset);if(s)if(s.type===J.ExtendsReference)this.getCompletionsForExtendsReference(s,o,n);else{var a=s;this.getCompletionsForSelector(a,a&&a.isNested(),n)}}else if(o instanceof Ce)this.getCompletionsForFunctionArgument(o,o.getParent(),n);else if(o instanceof ce)this.getCompletionsForDeclarations(o,n);else if(o instanceof nt)this.getCompletionsForVariableDeclaration(o,n);else if(o instanceof he)this.getCompletionsForRuleSet(o,n);else if(o instanceof it)this.getCompletionsForInterpolation(o,n);else if(o instanceof Ie)this.getCompletionsForFunctionDeclaration(o,n);else if(o instanceof at)this.getCompletionsForMixinReference(o,n);else if(o instanceof ke)this.getCompletionsForFunctionArgument(null,o,n);else if(o instanceof Ue)this.getCompletionsForSupports(o,n);else if(o instanceof qe)this.getCompletionsForSupportsCondition(o,n);else if(o instanceof st)this.getCompletionsForExtendsReference(o,null,n);else if(o.type===J.URILiteral)this.getCompletionForUriLiteralValue(o,n);else if(null===o.parent)this.getCompletionForTopLevel(n);else{if(o.type!==J.StringLiteral||!this.isImportPathParent(o.parent.type))continue;this.getCompletionForImportPath(o,n)}if(n.items.length>0||this.offset>o.offset)return this.finalize(n)}return this.getCompletionsForStylesheet(n),0===n.items.length&&this.variablePrefix&&0===this.currentWord.indexOf(this.variablePrefix)&&this.getVariableProposals(null,n),this.finalize(n)}finally{this.position=null,this.currentWord=null,this.textDocument=null,this.styleSheet=null,this.symbolContext=null,this.defaultReplaceRange=null,this.nodePath=null}},e.prototype.isImportPathParent=function(e){return e===J.Import},e.prototype.finalize=function(e){var t=e.items.some((function(e){return!!e.sortText||"-"===e.label[0]}));return t&&e.items.forEach((function(e,t){e.sortText?"-"===e.label[0]?e.sortText+=Tn.VendorPrefixed+"_"+Ln(t):e.sortText+=Tn.Normal+"_"+Ln(t):"-"===e.label[0]?e.sortText=Tn.VendorPrefixed+"_"+Ln(t):e.sortText=Tn.Normal+"_"+Ln(t)})),e},e.prototype.findInNodePath=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];for(var r=this.nodePath.length-1;r>=0;r--){var n=this.nodePath[r];if(-1!==e.indexOf(n.type))return n}return null},e.prototype.getCompletionsForDeclarationProperty=function(e,t){return this.getPropertyProposals(e,t)},e.prototype.getPropertyProposals=function(e,t){var r=this,n=this.isTriggerPropertyValueCompletionEnabled,i=this.isCompletePropertyWithSemicolonEnabled,o=hr.getProperties();return o.forEach((function(o){var s,a,c=!1;e?(s=r.getCompletionRange(e.getProperty()),a=o.name,wt(e.colonPosition)||(a+=": ",c=!0)):(s=r.getCompletionRange(null),a=o.name+": ",c=!0),!e&&i&&(a+="$0;"),e&&!e.semicolonPosition&&i&&r.offset>=r.textDocument.offsetAt(s.end)&&(a+="$0;");var u={label:o.name,documentation:Rt(o,r.doesSupportMarkdown()),tags:Vn(o)?[Yr.Deprecated]:[],textEdit:Ar.replace(s,a),insertTextFormat:Zr.Snippet,kind:Xr.Property};o.restrictions||(c=!1),n&&c&&(u.command={title:"Suggest",command:"editor.action.triggerSuggest"}),Wr(o.name,"-")&&(u.sortText=Tn.VendorPrefixed),t.items.push(u)})),this.completionParticipants.forEach((function(e){e.onCssProperty&&e.onCssProperty({propertyName:r.currentWord,range:r.defaultReplaceRange})})),t},Object.defineProperty(e.prototype,"isTriggerPropertyValueCompletionEnabled",{get:function(){return!this.settings||!this.settings.completion||void 0===this.settings.completion.triggerPropertyValueCompletion||this.settings.completion.triggerPropertyValueCompletion},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"isCompletePropertyWithSemicolonEnabled",{get:function(){return!this.settings||!this.settings.completion||void 0===this.settings.completion.completePropertyWithSemicolon||this.settings.completion.completePropertyWithSemicolon},enumerable:!0,configurable:!0}),e.prototype.getCompletionsForDeclarationValue=function(e,t){var r=this,n=e.getFullPropertyName(),i=hr.getProperty(n),o=e.getValue()||null;while(o&&o.hasChildren())o=o.findChildAtOffset(this.offset,!1);if(this.completionParticipants.forEach((function(e){e.onCssPropertyValue&&e.onCssPropertyValue({propertyName:n,propertyValue:r.currentWord,range:r.getCompletionRange(o)})})),i){if(i.restrictions)for(var s=0,a=i.restrictions;s<a.length;s++){var c=a[s];switch(c){case"color":this.getColorProposals(i,o,t);break;case"position":this.getPositionProposals(i,o,t);break;case"repeat":this.getRepeatStyleProposals(i,o,t);break;case"line-style":this.getLineStyleProposals(i,o,t);break;case"line-width":this.getLineWidthProposals(i,o,t);break;case"geometry-box":this.getGeometryBoxProposals(i,o,t);break;case"box":this.getBoxProposals(i,o,t);break;case"image":this.getImageProposals(i,o,t);break;case"timing-function":this.getTimingFunctionProposals(i,o,t);break;case"shape":this.getBasicShapeProposals(i,o,t);break}}this.getValueEnumProposals(i,o,t),this.getCSSWideKeywordProposals(i,o,t),this.getUnitProposals(i,o,t)}else for(var u=Bn(this.styleSheet,e),h=0,l=u.getEntries();h<l.length;h++){var p=l[h];t.items.push({label:p,textEdit:Ar.replace(this.getCompletionRange(o),p),kind:Xr.Value})}return this.getVariableProposals(o,t),this.getTermProposals(i,o,t),t},e.prototype.getValueEnumProposals=function(e,t,r){if(e.values)for(var n=0,i=e.values;n<i.length;n++){var o=i[n],s=o.name,a=void 0;if(jr(s,")")){var c=s.lastIndexOf("(");-1!==c&&(s=s.substr(0,c)+"($1)",a=Mn)}var u={label:o.name,documentation:Rt(o,this.doesSupportMarkdown()),tags:Vn(e)?[Yr.Deprecated]:[],textEdit:Ar.replace(this.getCompletionRange(t),s),sortText:Tn.Enums,kind:Xr.Value,insertTextFormat:a};r.items.push(u)}return r},e.prototype.getCSSWideKeywordProposals=function(e,t,r){for(var n in rr)r.items.push({label:n,documentation:rr[n],textEdit:Ar.replace(this.getCompletionRange(t),n),kind:Xr.Value});return r},e.prototype.getCompletionsForInterpolation=function(e,t){return this.offset>=e.offset+2&&this.getVariableProposals(null,t),t},e.prototype.getVariableProposals=function(e,t){for(var r=this.getSymbolContext().findSymbolsAtOffset(this.offset,X.Variable),n=0,i=r;n<i.length;n++){var o=i[n],s=Wr(o.name,"--")?"var("+o.name+")":o.name,a={label:o.name,documentation:o.value?qr(o.value):o.value,textEdit:Ar.replace(this.getCompletionRange(e),s),kind:Xr.Variable,sortText:Tn.Variable};if("string"===typeof a.documentation&&jn(a.documentation)&&(a.kind=Xr.Color),o.node.type===J.FunctionParameter){var c=o.node.getParent();c.type===J.MixinDeclaration&&(a.detail=Fn("completion.argument","argument from '{0}'",c.getName()))}t.items.push(a)}return t},e.prototype.getVariableProposalsForCSSVarFunction=function(e){var t=this.getSymbolContext().findSymbolsAtOffset(this.offset,X.Variable);t=t.filter((function(e){return Wr(e.name,"--")}));for(var r=0,n=t;r<n.length;r++){var i=n[r],o={label:i.name,documentation:i.value?qr(i.value):i.value,textEdit:Ar.replace(this.getCompletionRange(null),i.name),kind:Xr.Variable};"string"===typeof o.documentation&&jn(o.documentation)&&(o.kind=Xr.Color),e.items.push(o)}return e},e.prototype.getUnitProposals=function(e,t,r){var n="0";if(this.currentWord.length>0){var i=this.currentWord.match(/^-?\d[\.\d+]*/);i&&(n=i[0],r.isIncomplete=n.length===this.currentWord.length)}else 0===this.currentWord.length&&(r.isIncomplete=!0);if(t&&t.parent&&t.parent.type===J.Term&&(t=t.getParent()),e.restrictions)for(var o=0,s=e.restrictions;o<s.length;o++){var a=s[o],c=sr[a];if(c)for(var u=0,h=c;u<h.length;u++){var l=h[u],p=n+l;r.items.push({label:p,textEdit:Ar.replace(this.getCompletionRange(t),p),kind:Xr.Unit})}}return r},e.prototype.getCompletionRange=function(e){if(e&&e.offset<=this.offset&&this.offset<=e.end){var t=-1!==e.end?this.textDocument.positionAt(e.end):this.position,r=this.textDocument.positionAt(e.offset);if(r.line===t.line)return yr.create(r,t)}return this.defaultReplaceRange},e.prototype.getColorProposals=function(e,t,r){for(var n in Ot)r.items.push({label:n,documentation:Ot[n],textEdit:Ar.replace(this.getCompletionRange(t),n),kind:Xr.Color});for(var n in Vt)r.items.push({label:n,documentation:Vt[n],textEdit:Ar.replace(this.getCompletionRange(t),n),kind:Xr.Value});var i=new Nn;this.styleSheet.acceptVisitor(new Un(i,this.offset));for(var o=0,s=i.getEntries();o<s.length;o++){n=s[o];r.items.push({label:n,textEdit:Ar.replace(this.getCompletionRange(t),n),kind:Xr.Color})}for(var a=function(e){var n=1,i=function(e,t){return"${"+n+++":"+t+"}"},o=e.func.replace(/\[?\$(\w+)\]?/g,i);r.items.push({label:e.func.substr(0,e.func.indexOf("(")),detail:e.func,documentation:e.desc,textEdit:Ar.replace(c.getCompletionRange(t),o),insertTextFormat:Mn,kind:Xr.Function})},c=this,u=0,h=Mt;u<h.length;u++){var l=h[u];a(l)}return r},e.prototype.getPositionProposals=function(e,t,r){for(var n in Jt)r.items.push({label:n,documentation:Jt[n],textEdit:Ar.replace(this.getCompletionRange(t),n),kind:Xr.Value});return r},e.prototype.getRepeatStyleProposals=function(e,t,r){for(var n in Xt)r.items.push({label:n,documentation:Xt[n],textEdit:Ar.replace(this.getCompletionRange(t),n),kind:Xr.Value});return r},e.prototype.getLineStyleProposals=function(e,t,r){for(var n in Zt)r.items.push({label:n,documentation:Zt[n],textEdit:Ar.replace(this.getCompletionRange(t),n),kind:Xr.Value});return r},e.prototype.getLineWidthProposals=function(e,t,r){for(var n=0,i=Yt;n<i.length;n++){var o=i[n];r.items.push({label:o,textEdit:Ar.replace(this.getCompletionRange(t),o),kind:Xr.Value})}return r},e.prototype.getGeometryBoxProposals=function(e,t,r){for(var n in tr)r.items.push({label:n,documentation:tr[n],textEdit:Ar.replace(this.getCompletionRange(t),n),kind:Xr.Value});return r},e.prototype.getBoxProposals=function(e,t,r){for(var n in er)r.items.push({label:n,documentation:er[n],textEdit:Ar.replace(this.getCompletionRange(t),n),kind:Xr.Value});return r},e.prototype.getImageProposals=function(e,t,r){for(var n in nr){var i=$n(n);r.items.push({label:n,documentation:nr[n],textEdit:Ar.replace(this.getCompletionRange(t),i),kind:Xr.Function,insertTextFormat:n!==i?Mn:void 0})}return r},e.prototype.getTimingFunctionProposals=function(e,t,r){for(var n in ir){var i=$n(n);r.items.push({label:n,documentation:ir[n],textEdit:Ar.replace(this.getCompletionRange(t),i),kind:Xr.Function,insertTextFormat:n!==i?Mn:void 0})}return r},e.prototype.getBasicShapeProposals=function(e,t,r){for(var n in or){var i=$n(n);r.items.push({label:n,documentation:or[n],textEdit:Ar.replace(this.getCompletionRange(t),i),kind:Xr.Function,insertTextFormat:n!==i?Mn:void 0})}return r},e.prototype.getCompletionsForStylesheet=function(e){var t=this.styleSheet.findFirstChildBeforeOffset(this.offset);return t?t instanceof he?this.getCompletionsForRuleSet(t,e):t instanceof Ue?this.getCompletionsForSupports(t,e):e:this.getCompletionForTopLevel(e)},e.prototype.getCompletionForTopLevel=function(e){var t=this;return hr.getAtDirectives().forEach((function(r){e.items.push({label:r.name,textEdit:Ar.replace(t.getCompletionRange(null),r.name),documentation:Rt(r,t.doesSupportMarkdown()),tags:Vn(r)?[Yr.Deprecated]:[],kind:Xr.Keyword})})),this.getCompletionsForSelector(null,!1,e),e},e.prototype.getCompletionsForRuleSet=function(e,t){var r=e.getDeclarations(),n=r&&r.endsWith("}")&&this.offset>=r.end;if(n)return this.getCompletionForTopLevel(t);var i=!r||this.offset<=r.offset;return i?this.getCompletionsForSelector(e,e.isNested(),t):this.getCompletionsForDeclarations(e.getDeclarations(),t)},e.prototype.getCompletionsForSelector=function(e,t,r){var n=this,i=this.findInNodePath(J.PseudoSelector,J.IdentifierSelector,J.ClassSelector,J.ElementNameSelector);!i&&this.offset-this.currentWord.length>0&&":"===this.textDocument.getText()[this.offset-this.currentWord.length-1]&&(this.currentWord=":"+this.currentWord,this.defaultReplaceRange=yr.create(gr.create(this.position.line,this.position.character-this.currentWord.length),this.position));var o=hr.getPseudoClasses();o.forEach((function(e){var t=$n(e.name),o={label:e.name,textEdit:Ar.replace(n.getCompletionRange(i),t),documentation:Rt(e,n.doesSupportMarkdown()),tags:Vn(e)?[Yr.Deprecated]:[],kind:Xr.Function,insertTextFormat:e.name!==t?Mn:void 0};Wr(e.name,":-")&&(o.sortText=Tn.VendorPrefixed),r.items.push(o)}));var s=hr.getPseudoElements();if(s.forEach((function(e){var t=$n(e.name),o={label:e.name,textEdit:Ar.replace(n.getCompletionRange(i),t),documentation:Rt(e,n.doesSupportMarkdown()),tags:Vn(e)?[Yr.Deprecated]:[],kind:Xr.Function,insertTextFormat:e.name!==t?Mn:void 0};Wr(e.name,"::-")&&(o.sortText=Tn.VendorPrefixed),r.items.push(o)})),!t){for(var a=0,c=ar;a<c.length;a++){var u=c[a];r.items.push({label:u,textEdit:Ar.replace(this.getCompletionRange(i),u),kind:Xr.Keyword})}for(var h=0,l=cr;h<l.length;h++){u=l[h];r.items.push({label:u,textEdit:Ar.replace(this.getCompletionRange(i),u),kind:Xr.Keyword})}}var p={};p[this.currentWord]=!0;var f=this.textDocument.getText();if(this.styleSheet.accept((function(e){if(e.type===J.SimpleSelector&&e.length>0){var t=f.substr(e.offset,e.length);return"."!==t.charAt(0)||p[t]||(p[t]=!0,r.items.push({label:t,textEdit:Ar.replace(n.getCompletionRange(i),t),kind:Xr.Keyword})),!1}return!0})),e&&e.isNested()){var d=e.getSelectors().findFirstChildBeforeOffset(this.offset);d&&0===e.getSelectors().getChildren().indexOf(d)&&this.getPropertyProposals(null,r)}return r},e.prototype.getCompletionsForDeclarations=function(e,t){if(!e||this.offset===e.offset)return t;var r=e.findFirstChildBeforeOffset(this.offset);if(!r)return this.getCompletionsForDeclarationProperty(null,t);if(r instanceof de){var n=r;if(!wt(n.colonPosition)||this.offset<=n.colonPosition)return this.getCompletionsForDeclarationProperty(n,t);if(wt(n.semicolonPosition)&&n.semicolonPosition<this.offset)return this.offset===n.semicolonPosition+1?t:this.getCompletionsForDeclarationProperty(null,t);if(n instanceof ye)return this.getCompletionsForDeclarationValue(n,t)}else r instanceof st?this.getCompletionsForExtendsReference(r,null,t):this.currentWord&&"@"===this.currentWord[0]&&this.getCompletionsForDeclarationProperty(null,t);return t},e.prototype.getCompletionsForVariableDeclaration=function(e,t){return this.offset&&wt(e.colonPosition)&&this.offset>e.colonPosition&&this.getVariableProposals(e.getValue(),t),t},e.prototype.getCompletionsForExpression=function(e,t){var r=e.getParent();if(r instanceof Ce)return this.getCompletionsForFunctionArgument(r,r.getParent(),t),t;var n=e.findParent(J.Declaration);if(!n)return this.getTermProposals(null,null,t),t;var i=e.findChildAtOffset(this.offset,!0);return i?i instanceof rt||i instanceof se?this.getCompletionsForDeclarationValue(n,t):t:this.getCompletionsForDeclarationValue(n,t)},e.prototype.getCompletionsForFunctionArgument=function(e,t,r){var n=t.getIdentifier();return n&&n.matches("var")&&(t.getArguments().hasChildren()&&t.getArguments().getChild(0)!==e||this.getVariableProposalsForCSSVarFunction(r)),r},e.prototype.getCompletionsForFunctionDeclaration=function(e,t){var r=e.getDeclarations();return r&&this.offset>r.offset&&this.offset<r.end&&this.getTermProposals(null,null,t),t},e.prototype.getCompletionsForMixinReference=function(e,t){for(var r=this.getSymbolContext().findSymbolsAtOffset(this.offset,X.Mixin),n=0,i=r;n<i.length;n++){var o=i[n];o.node instanceof ct&&t.items.push(this.makeTermProposal(o,o.node.getParameters(),null))}return t},e.prototype.getTermProposals=function(e,t,r){for(var n=this.getSymbolContext().findSymbolsAtOffset(this.offset,X.Function),i=0,o=n;i<o.length;i++){var s=o[i];s.node instanceof Ie&&r.items.push(this.makeTermProposal(s,s.node.getParameters(),t))}return r},e.prototype.makeTermProposal=function(e,t,r){e.node;var n=t.getChildren().map((function(e){return e instanceof xe?e.getName():e.getText()})),i=e.name+"("+n.map((function(e,t){return"${"+(t+1)+":"+e+"}"})).join(", ")+")";return{label:e.name,detail:e.name+"("+n.join(", ")+")",textEdit:Ar.replace(this.getCompletionRange(r),i),insertTextFormat:Mn,kind:Xr.Function,sortText:Tn.Term}},e.prototype.getCompletionsForSupportsCondition=function(e,t){var r=e.findFirstChildBeforeOffset(this.offset);if(r){if(r instanceof ye)return!wt(r.colonPosition)||this.offset<=r.colonPosition?this.getCompletionsForDeclarationProperty(r,t):this.getCompletionsForDeclarationValue(r,t);if(r instanceof qe)return this.getCompletionsForSupportsCondition(r,t)}return wt(e.lParent)&&this.offset>e.lParent&&(!wt(e.rParent)||this.offset<=e.rParent)?this.getCompletionsForDeclarationProperty(null,t):t},e.prototype.getCompletionsForSupports=function(e,t){var r=e.getDeclarations(),n=!r||this.offset<=r.offset;if(n){var i=e.findFirstChildBeforeOffset(this.offset);return i instanceof qe?this.getCompletionsForSupportsCondition(i,t):t}return this.getCompletionForTopLevel(t)},e.prototype.getCompletionsForExtendsReference=function(e,t,r){return r},e.prototype.getCompletionForUriLiteralValue=function(e,t){var r,n,i;if(e.hasChildren()){var o=e.getChild(0);r=o.getText(),n=this.position,i=this.getCompletionRange(o)}else{r="",n=this.position;var s=this.textDocument.positionAt(e.offset+"url(".length);i=yr.create(s,s)}return this.completionParticipants.forEach((function(e){e.onCssURILiteralValue&&e.onCssURILiteralValue({uriValue:r,position:n,range:i})})),t},e.prototype.getCompletionForImportPath=function(e,t){var r=this;return this.completionParticipants.forEach((function(t){t.onCssImportPath&&t.onCssImportPath({pathValue:e.getText(),position:r.position,range:r.getCompletionRange(e)})})),t},e.prototype.doesSupportMarkdown=function(){if(!wt(this.supportsMarkdown)){if(!wt(this.clientCapabilities))return this.supportsMarkdown=!0,this.supportsMarkdown;var e=this.clientCapabilities.textDocument&&this.clientCapabilities.textDocument.completion;this.supportsMarkdown=e&&e.completionItem&&Array.isArray(e.completionItem.documentationFormat)&&-1!==e.completionItem.documentationFormat.indexOf(Qr.Markdown)}return this.supportsMarkdown},e}();function Vn(e){return!(!e.status||"nonstandard"!==e.status&&"obsolete"!==e.status)}function Ln(e){var t=e.toString();switch(t.length){case 4:return t;case 3:return"0"+t;case 2:return"00"+t;case 1:return"000"+t;default:return"0000"}}var Nn=function(){function e(){this.entries={}}return e.prototype.add=function(e){this.entries[e]=!0},e.prototype.getEntries=function(){return Object.keys(this.entries)},e}();function $n(e){return e.replace(/\(\)$/,"($1)")}function Bn(e,t){var r=t.getFullPropertyName(),n=new Nn;function i(e){return(e instanceof se||e instanceof rt||e instanceof Ze)&&n.add(e.getText()),!0}function o(e){var t=e.getFullPropertyName();return r===t}function s(e){if(e instanceof ye&&e!==t&&o(e)){var r=e.getValue();r&&r.accept(i)}return!0}return e.accept(s),n}var Un=function(){function e(e,t){this.entries=e,this.currentOffset=t}return e.prototype.visitNode=function(e){return(e instanceof Ze||e instanceof ke&&$t(e))&&(this.currentOffset<e.offset||e.end<this.currentOffset)&&this.entries.add(e.getText()),!0},e}();function Wn(e,t){var r=t-1,n=e.getText();while(r>=0&&-1===' \t\n\r":{[()]},*>+'.indexOf(n.charAt(r)))r--;return n.substring(r+1,t)}function jn(e){return e.toLowerCase()in Ot||/(^#[0-9A-F]{6}$)|(^#[0-9A-F]{3}$)/i.test(e)}var Kn,qn=function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},e(t,r)};return function(t,r){function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),zn=vt(),Hn=function(){function e(){this.parent=null,this.children=null,this.attributes=null}return e.prototype.findAttribute=function(e){if(this.attributes)for(var t=0,r=this.attributes;t<r.length;t++){var n=r[t];if(n.name===e)return n.value}return null},e.prototype.addChild=function(t){t instanceof e&&(t.parent=this),this.children||(this.children=[]),this.children.push(t)},e.prototype.append=function(e){if(this.attributes){var t=this.attributes[this.attributes.length-1];t.value=t.value+e}},e.prototype.prepend=function(e){if(this.attributes){var t=this.attributes[0];t.value=e+t.value}},e.prototype.findRoot=function(){var e=this;while(e.parent&&!(e.parent instanceof Gn))e=e.parent;return e},e.prototype.removeChild=function(e){if(this.children){var t=this.children.indexOf(e);if(-1!==t)return this.children.splice(t,1),!0}return!1},e.prototype.addAttr=function(e,t){this.attributes||(this.attributes=[]);for(var r=0,n=this.attributes;r<n.length;r++){var i=n[r];if(i.name===e)return void(i.value+=" "+t)}this.attributes.push({name:e,value:t})},e.prototype.clone=function(t){void 0===t&&(t=!0);var r=new e;if(this.attributes){r.attributes=[];for(var n=0,i=this.attributes;n<i.length;n++){var o=i[n];r.addAttr(o.name,o.value)}}if(t&&this.children){r.children=[];for(var s=0;s<this.children.length;s++)r.addChild(this.children[s].clone())}return r},e.prototype.cloneWithParent=function(){var e=this.clone(!1);if(this.parent&&!(this.parent instanceof Gn)){var t=this.parent.cloneWithParent();t.addChild(e)}return e},e}(),Gn=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return qn(t,e),t}(Hn),Qn=function(e){function t(t){var r=e.call(this)||this;return r.addAttr("name",t),r}return qn(t,e),t}(Hn),Jn=function(){function e(e){this.quote=e,this.result=[]}return e.prototype.print=function(e){this.result=[],e instanceof Gn?e.children&&this.doPrint(e.children,0):this.doPrint([e],0);var t=this.result.join("\n");return[{language:"html",value:t}]},e.prototype.doPrint=function(e,t){for(var r=0,n=e;r<n.length;r++){var i=n[r];this.doPrintElement(i,t),i.children&&this.doPrint(i.children,t+1)}},e.prototype.writeLine=function(e,t){var r=new Array(e+1).join("  ");this.result.push(r+t)},e.prototype.doPrintElement=function(e,t){var r=e.findAttribute("name");if(e instanceof Qn||"…"===r)this.writeLine(t,r);else{var n=["<"];if(r?n.push(r):n.push("element"),e.attributes)for(var i=0,o=e.attributes;i<o.length;i++){var s=o[i];if("name"!==s.name){n.push(" "),n.push(s.name);var a=s.value;a&&(n.push("="),n.push(Kn.ensure(a,this.quote)))}}n.push(">"),this.writeLine(t,n.join(""))}},e}();(function(e){function t(e,t){return t+r(e)+t}function r(e){var t=e.match(/^['"](.*)["']$/);return t?t[1]:e}e.ensure=t,e.remove=r})(Kn||(Kn={}));var Xn=function(){function e(){this.id=0,this.attr=0,this.tag=0}return e}();function Zn(e,t){for(var r=new Hn,n=0,i=e.getChildren();n<i.length;n++){var o=i[n];switch(o.type){case J.SelectorCombinator:if(t){var s=o.getText().split("&");if(1===s.length){r.addAttr("name",s[0]);break}if(r=t.cloneWithParent(),s[0]){var a=r.findRoot();a.prepend(s[0])}for(var c=1;c<s.length;c++){if(c>1){var u=t.cloneWithParent();r.addChild(u.findRoot()),r=u}r.append(s[c])}}break;case J.SelectorPlaceholder:if(o.matches("@at-root"))return r;case J.ElementNameSelector:var h=o.getText();r.addAttr("name","*"===h?"element":Yn(h));break;case J.ClassSelector:r.addAttr("class",Yn(o.getText().substring(1)));break;case J.IdentifierSelector:r.addAttr("id",Yn(o.getText().substring(1)));break;case J.MixinDeclaration:r.addAttr("class",o.getName());break;case J.PseudoSelector:r.addAttr(Yn(o.getText()),"");break;case J.AttributeSelector:var l=o,p=l.getIdentifier();if(p){var f=l.getValue(),d=l.getOperator(),m=void 0;if(f&&d)switch(Yn(d.getText())){case"|=":m=Kn.remove(Yn(f.getText()))+"-…";break;case"^=":m=Kn.remove(Yn(f.getText()))+"…";break;case"$=":m="…"+Kn.remove(Yn(f.getText()));break;case"~=":m=" … "+Kn.remove(Yn(f.getText()))+" … ";break;case"*=":m="…"+Kn.remove(Yn(f.getText()))+"…";break;default:m=Kn.remove(Yn(f.getText()));break}r.addAttr(Yn(p.getText()),m)}break}}return r}function Yn(e){var t=new Z;t.setSource(e);var r=t.scanUnquotedString();return r?r.text:e}function ei(e){var t=e.match(/^::?([\w-]+)/);return!!t&&!!hr.getPseudoElement("::"+t[1])}function ti(e){function t(e){e.getChildren().forEach((function(e){switch(e.type){case J.IdentifierSelector:r.id++;break;case J.ClassSelector:case J.AttributeSelector:r.attr++;break;case J.ElementNameSelector:if(e.matches("*"))break;r.tag++;break;case J.PseudoSelector:var n=e.getText();if(ei(n))r.tag++;else{if(n.match(/^:not/i))break;r.attr++}break}e.getChildren().length>0&&t(e)}))}var r=new Xn;return t(e),zn("specificity","[Selector Specificity](https://developer.mozilla.org/en-US/docs/Web/CSS/Specificity): ({0}, {1}, {2})",r.id,r.attr,r.tag)}function ri(e){var t=si(e);if(t){var r=new Jn('"').print(t);return r.push(ti(e)),r}return[]}function ni(e){var t=Zn(e),r=new Jn('"').print(t);return r.push(ti(e)),r}var ii=function(){function e(e){this.prev=null,this.element=e}return e.prototype.processSelector=function(e){var t=null;if(!(this.element instanceof Gn)&&e.getChildren().some((function(e){return e.hasChildren()&&e.getChild(0).type===J.SelectorCombinator}))){var r=this.element.findRoot();r.parent instanceof Gn&&(t=this.element,this.element=r.parent,this.element.removeChild(r),this.prev=null)}for(var n=0,i=e.getChildren();n<i.length;n++){var o=i[n];if(o instanceof pe){if(this.prev instanceof pe){var s=new Qn("…");this.element.addChild(s),this.element=s}else this.prev&&(this.prev.matches("+")||this.prev.matches("~"))&&this.element.parent&&(this.element=this.element.parent);this.prev&&this.prev.matches("~")&&(this.element.addChild(Zn(o)),this.element.addChild(new Qn("⋮")));var a=Zn(o,t),c=a.findRoot();this.element.addChild(c),this.element=a}(o instanceof pe||o.type===J.SelectorCombinatorParent||o.type===J.SelectorCombinatorShadowPiercingDescendant||o.type===J.SelectorCombinatorSibling||o.type===J.SelectorCombinatorAllSiblings)&&(this.prev=o)}},e}();function oi(e){switch(e.type){case J.MixinDeclaration:case J.Stylesheet:return!0}return!1}function si(e){if(e.matches("@at-root"))return null;var t=new Gn,r=[],n=e.getParent();if(n instanceof he){var i=n.getParent();while(i&&!oi(i)){if(i instanceof he){if(i.getSelectors().matches("@at-root"))break;r.push(i)}i=i.getParent()}}for(var o=new ii(t),s=r.length-1;s>=0;s--){var a=r[s].getSelectors().getChild(0);a&&o.processSelector(a)}return o.processSelector(e),t}(function(){function e(e){this.clientCapabilities=e}e.prototype.doHover=function(e,t,r){function n(t){return yr.create(e.positionAt(t.offset),e.positionAt(t.end))}for(var i=e.offsetAt(t),o=te(r,i),s=null,a=0;a<o.length;a++){var c=o[a];if(c instanceof le){s={contents:ri(c),range:n(c)};break}if(c instanceof pe){Wr(c.getText(),"@")||(s={contents:ni(c),range:n(c)});break}if(c instanceof ye){var u=c.getFullPropertyName(),h=hr.getProperty(u);h&&(s={contents:Rt(h,this.doesSupportMarkdown()),range:n(c)})}else if(c instanceof ut){var l=c.getText();h=hr.getAtDirective(l);h&&(s={contents:Rt(h,this.doesSupportMarkdown()),range:n(c)})}else if(c instanceof ie&&c.type===J.PseudoSelector){var p=c.getText();h="::"===p.slice(0,2)?hr.getPseudoElement(p):hr.getPseudoClass(p);h&&(s={contents:Rt(h,this.doesSupportMarkdown()),range:n(c)})}else;}return s&&(s.contents=this.convertContents(s.contents)),s},e.prototype.convertContents=function(e){return this.doesSupportMarkdown()||"string"===typeof e?e:"kind"in e?{kind:"plaintext",value:e.value}:Array.isArray(e)?e.map((function(e){return"string"===typeof e?e:e.value})):e.value},e.prototype.doesSupportMarkdown=function(){if(!wt(this.supportsMarkdown)){if(!wt(this.clientCapabilities))return this.supportsMarkdown=!0,this.supportsMarkdown;var e=this.clientCapabilities.textDocument&&this.clientCapabilities.textDocument.hover;this.supportsMarkdown=e&&e.contentFormat&&Array.isArray(e.contentFormat)&&-1!==e.contentFormat.indexOf(Qr.Markdown)}return this.supportsMarkdown}})();var ai=function(e,t,r,n){function i(e){return e instanceof r?e:new r((function(t){t(e)}))}return new(r||(r=Promise))((function(r,o){function s(e){try{c(n.next(e))}catch(t){o(t)}}function a(e){try{c(n["throw"](e))}catch(t){o(t)}}function c(e){e.done?r(e.value):i(e.value).then(s,a)}c((n=n.apply(e,t||[])).next())}))},ci=function(e,t){var r,n,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(e){return function(t){return c([e,t])}}function c(o){if(r)throw new TypeError("Generator is already executing.");while(s)try{if(r=1,n&&(i=2&o[0]?n["return"]:o[0]?n["throw"]||((i=n["return"])&&i.call(n),0):n.next)&&!(i=i.call(n,o[1])).done)return i;switch(n=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,n=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(i=s.trys,!(i=i.length>0&&i[i.length-1])&&(6===o[0]||2===o[0])){s=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){s.label=o[1];break}if(6===o[0]&&s.label<i[1]){s.label=i[1],i=o;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(o);break}i[2]&&s.ops.pop(),s.trys.pop();continue}o=t.call(e,s)}catch(a){o=[6,a],n=0}finally{r=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}},ui=vt(),hi=function(){function e(){}return e.prototype.findDefinition=function(e,t,r){var n=new Ur(r),i=e.offsetAt(t),o=ee(r,i);if(!o)return null;var s=n.findSymbolFromNode(o);return s?{uri:e.uri,range:di(s.node,e)}:null},e.prototype.findReferences=function(e,t,r){var n=this.findDocumentHighlights(e,t,r);return n.map((function(t){return{uri:e.uri,range:t.range}}))},e.prototype.findDocumentHighlights=function(e,t,r){var n=[],i=e.offsetAt(t),o=ee(r,i);if(!o||o.type===J.Stylesheet||o.type===J.Declarations)return n;o.type===J.Identifier&&o.parent&&o.parent.type===J.ClassSelector&&(o=o.parent);var s=new Ur(r),a=s.findSymbolFromNode(o),c=o.getText();return r.accept((function(t){if(a){if(s.matchesSymbol(t,a))return n.push({kind:mi(t),range:di(t,e)}),!1}else o&&o.type===t.type&&t.matches(c)&&n.push({kind:mi(t),range:di(t,e)});return!0})),n},e.prototype.isRawStringDocumentLinkNode=function(e){return e.type===J.Import},e.prototype.findDocumentLinks=function(e,t,r){var n=this,i=[];return t.accept((function(t){if(t.type===J.URILiteral){var o=pi(e,t,r);return o&&i.push(o),!1}if(t.parent&&n.isRawStringDocumentLinkNode(t.parent)){var s=t.getText();if(Wr(s,"'")||Wr(s,'"')){o=fi(e,t,r);o&&i.push(o)}return!1}return!0})),i},e.prototype.findDocumentLinks2=function(e,t,r){return ai(this,void 0,void 0,(function(){return ci(this,(function(n){return[2,this.findDocumentLinks(e,t,r)]}))}))},e.prototype.findDocumentSymbols=function(e,t){var r=[];return t.accept((function(t){var n={name:null,kind:un.Class,location:null},i=t;if(t instanceof le)return n.name=t.getText(),i=t.findAParent(J.Ruleset,J.ExtendsReference),i&&(n.location=vr.create(e.uri,di(i,e)),r.push(n)),!1;if(t instanceof nt)n.name=t.getName(),n.kind=un.Variable;else if(t instanceof ct)n.name=t.getName(),n.kind=un.Method;else if(t instanceof Ie)n.name=t.getName(),n.kind=un.Function;else if(t instanceof Te)n.name=ui("literal.keyframes","@keyframes {0}",t.getName());else if(t instanceof Ae)n.name=ui("literal.fontface","@font-face");else if(t instanceof Be){var o=t.getChild(0);o instanceof je&&(n.name="@media "+o.getText(),n.kind=un.Module)}return n.name&&(n.location=vr.create(e.uri,di(i,e)),r.push(n)),!0})),r},e.prototype.findDocumentColors=function(e,t){var r=[];return t.accept((function(t){var n=li(t,e);return n&&r.push(n),!0})),r},e.prototype.getColorPresentations=function(e,t,r,n){var i,o=[],s=Math.round(255*r.red),a=Math.round(255*r.green),c=Math.round(255*r.blue);i=1===r.alpha?"rgb("+s+", "+a+", "+c+")":"rgba("+s+", "+a+", "+c+", "+r.alpha+")",o.push({label:i,textEdit:Ar.replace(n,i)}),i=1===r.alpha?"#"+gi(s)+gi(a)+gi(c):"#"+gi(s)+gi(a)+gi(c)+gi(Math.round(255*r.alpha)),o.push({label:i,textEdit:Ar.replace(n,i)});var u=Gt(r);return i=1===u.a?"hsl("+u.h+", "+Math.round(100*u.s)+"%, "+Math.round(100*u.l)+"%)":"hsla("+u.h+", "+Math.round(100*u.s)+"%, "+Math.round(100*u.l)+"%, "+u.a+")",o.push({label:i,textEdit:Ar.replace(n,i)}),o},e.prototype.doRename=function(e,t,r,n){var i,o=this.findDocumentHighlights(e,t,n),s=o.map((function(e){return Ar.replace(e.range,r)}));return{changes:(i={},i[e.uri]=s,i)}},e}();function li(e,t){var r=Qt(e);if(r){var n=di(e,t);return{color:r,range:n}}return null}function pi(e,t,r){if(0===t.getChildren().length)return null;var n=t.getChild(0);return fi(e,n,r)}function fi(e,t,r){if(!t)return null;var n,i=t.getText(),o=di(t,e);return o.start.line===o.end.line&&o.start.character===o.end.character?null:((Wr(i,"'")||Wr(i,'"'))&&(i=i.slice(1,-1)),n=Wr(i,"http://")||Wr(i,"https://")||/^\w+:\/\//g.test(i)?i:r.resolveReference(i,e.uri),{range:o,target:n})}function di(e,t){return yr.create(t.positionAt(e.offset),t.positionAt(e.end))}function mi(e){if(e.type===J.Selector)return an.Write;if(e instanceof se&&e.parent&&e.parent instanceof ve&&e.isCustomProperty)return an.Write;if(e.parent)switch(e.parent.type){case J.FunctionDeclaration:case J.MixinDeclaration:case J.Keyframe:case J.VariableDeclaration:case J.FunctionParameter:return an.Write}return an.Read}function gi(e){var t=e.toString(16);return 2!==t.length?"0"+t:t}var yi=vt(),vi=ne.Warning,bi=ne.Error,ki=ne.Ignore,xi=function(){function e(e,t,r){this.id=e,this.message=t,this.defaultValue=r}return e}(),Ci=function(){function e(e,t,r){this.id=e,this.message=t,this.defaultValue=r}return e}(),_i={AllVendorPrefixes:new xi("compatibleVendorPrefixes",yi("rule.vendorprefixes.all","When using a vendor-specific prefix make sure to also include all other vendor-specific properties"),ki),IncludeStandardPropertyWhenUsingVendorPrefix:new xi("vendorPrefix",yi("rule.standardvendorprefix.all","When using a vendor-specific prefix also include the standard property"),vi),DuplicateDeclarations:new xi("duplicateProperties",yi("rule.duplicateDeclarations","Do not use duplicate style definitions"),ki),EmptyRuleSet:new xi("emptyRules",yi("rule.emptyRuleSets","Do not use empty rulesets"),vi),ImportStatemement:new xi("importStatement",yi("rule.importDirective","Import statements do not load in parallel"),ki),BewareOfBoxModelSize:new xi("boxModel",yi("rule.bewareOfBoxModelSize","Do not use width or height when using padding or border"),ki),UniversalSelector:new xi("universalSelector",yi("rule.universalSelector","The universal selector (*) is known to be slow"),ki),ZeroWithUnit:new xi("zeroUnits",yi("rule.zeroWidthUnit","No unit for zero needed"),ki),RequiredPropertiesForFontFace:new xi("fontFaceProperties",yi("rule.fontFaceProperties","@font-face rule must define 'src' and 'font-family' properties"),vi),HexColorLength:new xi("hexColorLength",yi("rule.hexColor","Hex colors must consist of three, four, six or eight hex numbers"),bi),ArgsInColorFunction:new xi("argumentsInColorFunction",yi("rule.colorFunction","Invalid number of parameters"),bi),UnknownProperty:new xi("unknownProperties",yi("rule.unknownProperty","Unknown property."),vi),UnknownAtRules:new xi("unknownAtRules",yi("rule.unknownAtRules","Unknown at-rule."),vi),IEStarHack:new xi("ieHack",yi("rule.ieHack","IE hacks are only necessary when supporting IE7 and older"),ki),UnknownVendorSpecificProperty:new xi("unknownVendorSpecificProperties",yi("rule.unknownVendorSpecificProperty","Unknown vendor specific property."),ki),PropertyIgnoredDueToDisplay:new xi("propertyIgnoredDueToDisplay",yi("rule.propertyIgnoredDueToDisplay","Property is ignored due to the display."),vi),AvoidImportant:new xi("important",yi("rule.avoidImportant","Avoid using !important. It is an indication that the specificity of the entire CSS has gotten out of control and needs to be refactored."),ki),AvoidFloat:new xi("float",yi("rule.avoidFloat","Avoid using 'float'. Floats lead to fragile CSS that is easy to break if one aspect of the layout changes."),ki),AvoidIdSelector:new xi("idSelector",yi("rule.avoidIdSelector","Selectors should not contain IDs because these rules are too tightly coupled with the HTML."),ki)},wi={ValidProperties:new Ci("validProperties",yi("rule.validProperties","A list of properties that are not validated against the `unknownProperties` rule."),[])},Pi=function(){function e(e){void 0===e&&(e={}),this.conf=e}return e.prototype.getRule=function(e){if(this.conf.hasOwnProperty(e.id)){var t=Si(this.conf[e.id]);if(t)return t}return e.defaultValue},e.prototype.getSetting=function(e){return this.conf[e.id]},e}();function Si(e){switch(e){case"ignore":return ne.Ignore;case"warning":return ne.Warning;case"error":return ne.Error}return null}var Ei=vt(),Ii=(function(){function e(){}e.prototype.doCodeActions=function(e,t,r,n){return this.doCodeActions2(e,t,r,n).map((function(t){var r=t.edit&&t.edit.documentChanges&&t.edit.documentChanges[0];return Rr.create(t.title,"_css.applyCodeAction",e.uri,e.version,r&&r.edits)}))},e.prototype.doCodeActions2=function(e,t,r,n){var i=[];if(r.diagnostics)for(var o=0,s=r.diagnostics;o<s.length;o++){var a=s[o];this.appendFixesForMarker(e,n,a,i)}return i},e.prototype.getFixesForUnknownProperty=function(e,t,r,n){var i=t.getName(),o=[];hr.getProperties().forEach((function(e){var t=Kr(i,e.name);t>=i.length/2&&o.push({property:e.name,score:t})})),o.sort((function(e,t){return t.score-e.score}));for(var s=3,a=0,c=o;a<c.length;a++){var u=c[a],h=u.property,l=Ei("css.codeaction.rename","Rename to '{0}'",h),p=Ar.replace(r.range,h),f=Hr.create(e.uri,e.version),d={documentChanges:[Dr.create(f,[p])]},m=mn.create(l,d,fn.QuickFix);if(m.diagnostics=[r],n.push(m),--s<=0)return}},e.prototype.appendFixesForMarker=function(e,t,r,n){if(r.code===_i.UnknownProperty.id)for(var i=e.offsetAt(r.range.start),o=e.offsetAt(r.range.end),s=te(t,i),a=s.length-1;a>=0;a--){var c=s[a];if(c instanceof ye){var u=c.getProperty();if(u&&u.offset===i&&u.end===o)return void this.getFixesForUnknownProperty(e,u,r,n)}}}}(),function(){function e(e){this.fullPropertyName=e.getFullPropertyName().toLowerCase(),this.node=e}return e}());function Ri(e,t,r,n){var i=e[t];i.value=r,r&&(dr(i.properties,n)||i.properties.push(n))}function Ai(e,t,r){Ri(e,"top",t,r),Ri(e,"right",t,r),Ri(e,"bottom",t,r),Ri(e,"left",t,r)}function Di(e,t,r,n){"top"===t||"right"===t||"bottom"===t||"left"===t?Ri(e,t,r,n):Ai(e,r,n)}function Ti(e,t,r){switch(t.length){case 1:Di(e,void 0,t[0],r);break;case 2:Di(e,"top",t[0],r),Di(e,"bottom",t[0],r),Di(e,"right",t[1],r),Di(e,"left",t[1],r);break;case 3:Di(e,"top",t[0],r),Di(e,"right",t[1],r),Di(e,"left",t[1],r),Di(e,"bottom",t[2],r);break;case 4:Di(e,"top",t[0],r),Di(e,"right",t[1],r),Di(e,"bottom",t[2],r),Di(e,"left",t[3],r);break}}function Fi(e,t){for(var r=0,n=t;r<n.length;r++){var i=n[r];if(e.matches(i))return!0}return!1}function Mi(e,t){return void 0===t&&(t=!0),(!t||!Fi(e,["initial","unset"]))&&0!==parseFloat(e.getText())}function Oi(e,t){return void 0===t&&(t=!0),e.map((function(e){return Mi(e,t)}))}function Vi(e,t){return void 0===t&&(t=!0),!Fi(e,["none","hidden"])&&(!t||!Fi(e,["initial","unset"]))}function Li(e,t){return void 0===t&&(t=!0),e.map((function(e){return Vi(e,t)}))}function Ni(e){var t=e.getChildren();if(1===t.length){var r=t[0];return Mi(r)&&Vi(r)}for(var n=0,i=t;n<i.length;n++){var o=i[n];r=o;if(!Mi(r,!1)||!Vi(r,!1))return!1}return!0}function $i(e){for(var t={top:{value:!1,properties:[]},right:{value:!1,properties:[]},bottom:{value:!1,properties:[]},left:{value:!1,properties:[]}},r=0,n=e;r<n.length;r++){var i=n[r],o=i.node.value;if("undefined"!==typeof o)switch(i.fullPropertyName){case"box-sizing":return{top:{value:!1,properties:[]},right:{value:!1,properties:[]},bottom:{value:!1,properties:[]},left:{value:!1,properties:[]}};case"width":t.width=i;break;case"height":t.height=i;break;default:var s=i.fullPropertyName.split("-");switch(s[0]){case"border":switch(s[1]){case void 0:case"top":case"right":case"bottom":case"left":switch(s[2]){case void 0:Di(t,s[1],Ni(o),i);break;case"width":Di(t,s[1],Mi(o,!1),i);break;case"style":Di(t,s[1],Vi(o,!0),i);break}break;case"width":Ti(t,Oi(o.getChildren(),!1),i);break;case"style":Ti(t,Li(o.getChildren(),!0),i);break}break;case"padding":1===s.length?Ti(t,Oi(o.getChildren(),!0),i):Di(t,s[1],Mi(o,!0),i);break}break}}return t}var Bi=vt(),Ui=function(){function e(){this.data={}}return e.prototype.add=function(e,t,r){var n=this.data[e];n||(n={nodes:[],names:[]},this.data[e]=n),n.names.push(t),r&&n.nodes.push(r)},e}(),Wi=function(){function e(e,t){var r=this;this.warnings=[],this.settings=t,this.documentText=e.getText(),this.keyframes=new Ui,this.validProperties={};var n=t.getSetting(wi.ValidProperties);Array.isArray(n)&&n.forEach((function(e){if("string"===typeof e){var t=e.trim().toLowerCase();t.length&&(r.validProperties[t]=!0)}}))}return e.entries=function(t,r,n,i){var o=new e(r,n);return t.acceptVisitor(o),o.completeValidations(),o.getEntries(i)},e.prototype.isValidPropertyDeclaration=function(e){var t=e.fullPropertyName;return this.validProperties[t]},e.prototype.fetch=function(e,t){for(var r=[],n=0,i=e;n<i.length;n++){var o=i[n];o.fullPropertyName===t&&r.push(o)}return r},e.prototype.fetchWithValue=function(e,t,r){for(var n=[],i=0,o=e;i<o.length;i++){var s=o[i];if(s.fullPropertyName===t){var a=s.node.getValue();a&&this.findValueInExpression(a,r)&&n.push(s)}}return n},e.prototype.findValueInExpression=function(e,t){var r=!1;return e.accept((function(e){return e.type===J.Identifier&&e.matches(t)&&(r=!0),!r})),r},e.prototype.getEntries=function(e){return void 0===e&&(e=ne.Warning|ne.Error),this.warnings.filter((function(t){return 0!==(t.getLevel()&e)}))},e.prototype.addEntry=function(e,t,r){var n=new dt(e,t,this.settings.getRule(t),r);this.warnings.push(n)},e.prototype.getMissingNames=function(e,t){for(var r=e.slice(0),n=0;n<t.length;n++){var i=r.indexOf(t[n]);-1!==i&&(r[i]=null)}var o=null;for(n=0;n<r.length;n++){var s=r[n];s&&(o=null===o?Bi("namelist.single","'{0}'",s):Bi("namelist.concatenated","{0}, '{1}'",o,s))}return o},e.prototype.visitNode=function(e){switch(e.type){case J.UnknownAtRule:return this.visitUnknownAtRule(e);case J.Keyframe:return this.visitKeyframe(e);case J.FontFace:return this.visitFontFace(e);case J.Ruleset:return this.visitRuleSet(e);case J.SimpleSelector:return this.visitSimpleSelector(e);case J.Function:return this.visitFunction(e);case J.NumericValue:return this.visitNumericValue(e);case J.Import:return this.visitImport(e);case J.HexColorValue:return this.visitHexColorValue(e);case J.Prio:return this.visitPrio(e)}return!0},e.prototype.completeValidations=function(){this.validateKeyframes()},e.prototype.visitUnknownAtRule=function(e){var t=e.getChild(0);if(!t)return!1;var r=hr.getAtDirective(t.getText());return!r&&(this.addEntry(t,_i.UnknownAtRules,"Unknown at rule "+t.getText()),!0)},e.prototype.visitKeyframe=function(e){var t=e.getKeyword();if(!t)return!1;var r=t.getText();return this.keyframes.add(e.getName(),r,"@keyframes"!==r?t:null),!0},e.prototype.validateKeyframes=function(){var e=["@-webkit-keyframes","@-moz-keyframes","@-o-keyframes"];for(var t in this.keyframes.data){var r=this.keyframes.data[t].names,n=-1===r.indexOf("@keyframes");if(n||1!==r.length){var i=this.getMissingNames(e,r);if(i||n)for(var o=0,s=this.keyframes.data[t].nodes;o<s.length;o++){var a=s[o];if(n){var c=Bi("keyframes.standardrule.missing","Always define standard rule '@keyframes' when defining keyframes.");this.addEntry(a,_i.IncludeStandardPropertyWhenUsingVendorPrefix,c)}if(i){c=Bi("keyframes.vendorspecific.missing","Always include all vendor specific rules: Missing: {0}",i);this.addEntry(a,_i.AllVendorPrefixes,c)}}}}return!0},e.prototype.visitSimpleSelector=function(e){var t=this.documentText.charAt(e.offset);return 1===e.length&&"*"===t&&this.addEntry(e,_i.UniversalSelector),"#"===t&&this.addEntry(e,_i.AvoidIdSelector),!0},e.prototype.visitImport=function(e){return this.addEntry(e,_i.ImportStatemement),!0},e.prototype.visitRuleSet=function(t){var r=t.getDeclarations();if(!r)return!1;r.hasChildren()||this.addEntry(t.getSelectors(),_i.EmptyRuleSet);for(var n=[],i=0,o=r.getChildren();i<o.length;i++){var s=o[i];s instanceof ye&&n.push(new Ii(s))}var a=$i(n);if(a.width){var c=[];if(a.right.value&&(c=mr(c,a.right.properties)),a.left.value&&(c=mr(c,a.left.properties)),0!==c.length){for(var u=0,h=c;u<h.length;u++){var l=h[u];this.addEntry(l.node,_i.BewareOfBoxModelSize)}this.addEntry(a.width.node,_i.BewareOfBoxModelSize)}}if(a.height){c=[];if(a.top.value&&(c=mr(c,a.top.properties)),a.bottom.value&&(c=mr(c,a.bottom.properties)),0!==c.length){for(var p=0,f=c;p<f.length;p++){l=f[p];this.addEntry(l.node,_i.BewareOfBoxModelSize)}this.addEntry(a.height.node,_i.BewareOfBoxModelSize)}}var d=this.fetchWithValue(n,"display","inline");if(d.length>0)for(var m=0,g=["width","height","margin-top","margin-bottom","float"];m<g.length;m++)for(var y=g[m],v=this.fetch(n,y),b=0;b<v.length;b++){var k=v[b].node,x=k.getValue();("float"!==y||x&&!x.matches("none"))&&this.addEntry(k,_i.PropertyIgnoredDueToDisplay,Bi("rule.propertyIgnoredDueToDisplayInline","Property is ignored due to the display. With 'display: inline', the width, height, margin-top, margin-bottom, and float properties have no effect."))}if(d=this.fetchWithValue(n,"display","inline-block"),d.length>0)for(v=this.fetch(n,"float"),b=0;b<v.length;b++){var C=v[b].node;x=C.getValue();x&&!x.matches("none")&&this.addEntry(C,_i.PropertyIgnoredDueToDisplay,Bi("rule.propertyIgnoredDueToDisplayInlineBlock","inline-block is ignored due to the float. If 'float' has a value other than 'none', the box is floated and 'display' is treated as 'block'"))}if(d=this.fetchWithValue(n,"display","block"),d.length>0)for(v=this.fetch(n,"vertical-align"),b=0;b<v.length;b++)this.addEntry(v[b].node,_i.PropertyIgnoredDueToDisplay,Bi("rule.propertyIgnoredDueToDisplayBlock","Property is ignored due to the display. With 'display: block', vertical-align should not be used."));var _=this.fetch(n,"float");for(b=0;b<_.length;b++){s=_[b];this.isValidPropertyDeclaration(s)||this.addEntry(s.node,_i.AvoidFloat)}for(var w=0;w<n.length;w++){s=n[w];if("background"!==s.fullPropertyName&&!this.validProperties[s.fullPropertyName]){x=s.node.getValue();if(x&&"-"!==this.documentText.charAt(x.offset)){var P=this.fetch(n,s.fullPropertyName);if(P.length>1)for(var S=0;S<P.length;S++){var E=P[S].node.getValue();E&&"-"!==this.documentText.charAt(E.offset)&&P[S]!==s&&this.addEntry(s.node,_i.DuplicateDeclarations)}}}}var I=t.getSelectors().matches(":export");if(!I){for(var R=new Ui,A=!1,D=0,T=n;D<T.length;D++){s=T[D];var F=s.node;if(this.isCSSDeclaration(F)){var M=s.fullPropertyName,O=M.charAt(0);if("-"===O){if("-"!==M.charAt(1)){hr.isKnownProperty(M)||this.validProperties[M]||this.addEntry(F.getProperty(),_i.UnknownVendorSpecificProperty);var V=F.getNonPrefixedPropertyName();R.add(V,M,F.getProperty())}}else{var L=M;"*"!==O&&"_"!==O||(this.addEntry(F.getProperty(),_i.IEStarHack),M=M.substr(1)),hr.isKnownProperty(L)||hr.isKnownProperty(M)||this.validProperties[M]||this.addEntry(F.getProperty(),_i.UnknownProperty,Bi("property.unknownproperty.detailed","Unknown property: '{0}'",M)),R.add(M,M,null)}}else A=!0}if(!A)for(var N in R.data){var $=R.data[N],B=$.names,U=hr.isStandardProperty(N)&&-1===B.indexOf(N);if(U||1!==B.length){for(var W=[],j=(w=0,e.prefixes.length);w<j;w++){var K=e.prefixes[w];hr.isStandardProperty(K+N)&&W.push(K+N)}var q=this.getMissingNames(W,B);if(q||U)for(var z=0,H=$.nodes;z<H.length;z++){var G=H[z];if(U){var Q=Bi("property.standard.missing","Also define the standard property '{0}' for compatibility",N);this.addEntry(G,_i.IncludeStandardPropertyWhenUsingVendorPrefix,Q)}if(q){Q=Bi("property.vendorspecific.missing","Always include all vendor specific properties: Missing: {0}",q);this.addEntry(G,_i.AllVendorPrefixes,Q)}}}}}return!0},e.prototype.visitPrio=function(e){return this.addEntry(e,_i.AvoidImportant),!0},e.prototype.visitNumericValue=function(e){var t=e.findParent(J.Function);if(t&&"calc"===t.getName())return!0;var r=e.findParent(J.Declaration);if(r){var n=r.getValue();if(n){var i=e.getValue();if(!i.unit||-1===sr.length.indexOf(i.unit.toLowerCase()))return!0;0===parseFloat(i.value)&&i.unit&&!this.validProperties[r.getFullPropertyName()]&&this.addEntry(e,_i.ZeroWithUnit)}}return!0},e.prototype.visitFontFace=function(e){var t=e.getDeclarations();if(!t)return!1;for(var r=!1,n=!1,i=!1,o=0,s=t.getChildren();o<s.length;o++){var a=s[o];if(this.isCSSDeclaration(a)){var c=a.getProperty().getName().toLowerCase();"src"===c&&(r=!0),"font-family"===c&&(n=!0)}else i=!0}return i||r&&n||this.addEntry(e,_i.RequiredPropertiesForFontFace),!0},e.prototype.isCSSDeclaration=function(e){if(e instanceof ye){if(!e.getValue())return!1;var t=e.getProperty();if(!t)return!1;var r=t.getIdentifier();return!(!r||r.containsInterpolation())}return!1},e.prototype.visitHexColorValue=function(e){var t=e.length;return 9!==t&&7!==t&&5!==t&&4!==t&&this.addEntry(e,_i.HexColorLength),!1},e.prototype.visitFunction=function(e){var t=e.getName().toLowerCase(),r=-1,n=0;switch(t){case"rgb(":case"hsl(":r=3;break;case"rgba(":case"hsla(":r=4;break}return-1!==r&&(e.getArguments().accept((function(e){return!(e instanceof Qe)||(n+=1,!1)})),n!==r&&this.addEntry(e,_i.ArgsInColorFunction)),!0},e.prefixes=["-ms-","-moz-","-o-","-webkit-"],e}(),ji=(function(){function e(){}e.prototype.configure=function(e){this.settings=e},e.prototype.doValidation=function(e,t,r){if(void 0===r&&(r=this.settings),r&&!1===r.validate)return[];var n=[];n.push.apply(n,mt.entries(t)),n.push.apply(n,Wi.entries(t,e,new Pi(r&&r.lint)));var i=[];for(var o in _i)i.push(_i[o].id);function s(t){var r=yr.create(e.positionAt(t.getOffset()),e.positionAt(t.getOffset()+t.getLength())),n=e.languageId;return{code:t.getRule().id,source:n,message:t.getMessage(),severity:t.getLevel()===ne.Warning?Sr.Warning:Sr.Error,range:r}}return n.filter((function(e){return e.getLevel()!==ne.Ignore})).map(s)}}(),function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},e(t,r)};return function(t,r){function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}()),Ki="/".charCodeAt(0),qi="\n".charCodeAt(0),zi="\r".charCodeAt(0),Hi="\f".charCodeAt(0),Gi="$".charCodeAt(0),Qi="#".charCodeAt(0),Ji="{".charCodeAt(0),Xi="=".charCodeAt(0),Zi="!".charCodeAt(0),Yi="<".charCodeAt(0),eo=">".charCodeAt(0),to=".".charCodeAt(0),ro="@".charCodeAt(0),no=n.CustomToken,io=no++,oo=no++,so=(no++,no++),ao=no++,co=no++,uo=no++,ho=no++,lo=(no++,no++),po=no++,fo=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return ji(t,e),t.prototype.scanNext=function(t){if(this.stream.advanceIfChar(Gi)){var r=["$"];if(this.ident(r))return this.finishToken(t,io,r.join(""));this.stream.goBackTo(t)}if(this.stream.advanceIfChars([Qi,Ji]))return this.finishToken(t,oo);if(this.stream.advanceIfChars([Xi,Xi]))return this.finishToken(t,so);if(this.stream.advanceIfChars([Zi,Xi]))return this.finishToken(t,ao);if(this.stream.advanceIfChar(Yi))return this.stream.advanceIfChar(Xi)?this.finishToken(t,uo):this.finishToken(t,n.Delim);if(this.stream.advanceIfChar(eo))return this.stream.advanceIfChar(Xi)?this.finishToken(t,co):this.finishToken(t,n.Delim);if(this.stream.advanceIfChars([to,to,to]))return this.finishToken(t,ho);if(this.stream.advanceIfChar(ro)){r=["@"];if(this.ident(r)){var i=r.join("");if("@forward"===i)return this.finishToken(t,lo,i);if("@use"===i)return this.finishToken(t,po,i)}this.stream.goBackTo(t)}return e.prototype.scanNext.call(this,t)},t.prototype.comment=function(){return!!e.prototype.comment.call(this)||!(this.inURL||!this.stream.advanceIfChars([Ki,Ki]))&&(this.stream.advanceWhileChar((function(e){switch(e){case qi:case zi:case Hi:return!1;default:return!0}})),!0)},t}(Z),mo=vt(),go=function(){function e(e,t){this.id=e,this.message=t}return e}(),yo={FromExpected:new go("scss-fromexpected",mo("expected.from","'from' expected")),ThroughOrToExpected:new go("scss-throughexpected",mo("expected.through","'through' or 'to' expected")),InExpected:new go("scss-fromexpected",mo("expected.in","'in' expected"))},vo=function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},e(t,r)};return function(t,r){function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),bo=(function(e){function t(){return e.call(this,new fo)||this}vo(t,e),t.prototype._parseStylesheetStart=function(){return this._parseForward()||this._parseUse()||e.prototype._parseStylesheetStart.call(this)},t.prototype._parseStylesheetStatement=function(){return this.peek(n.AtKeyword)?this._parseWarnAndDebug()||this._parseControlStatement()||this._parseMixinDeclaration()||this._parseMixinContent()||this._parseMixinReference()||this._parseFunctionDeclaration()||e.prototype._parseStylesheetAtStatement.call(this):this._parseRuleset(!0)||this._parseVariableDeclaration()},t.prototype._parseImport=function(){if(!this.peekKeyword("@import"))return null;var e=this.create(Me);if(this.consumeToken(),!e.addChild(this._parseURILiteral())&&!e.addChild(this._parseStringLiteral()))return this.finish(e,xt.URIOrStringExpected);while(this.accept(n.Comma))if(!e.addChild(this._parseURILiteral())&&!e.addChild(this._parseStringLiteral()))return this.finish(e,xt.URIOrStringExpected);return this.peek(n.SemiColon)||this.peek(n.EOF)||e.setMedialist(this._parseMediaQueryList()),this.finish(e)},t.prototype._parseVariableDeclaration=function(e){if(void 0===e&&(e=[]),!this.peek(io))return null;var t=this.create(nt);if(!t.setVariable(this._parseVariable()))return null;if(!this.accept(n.Colon))return this.finish(t,xt.ColonExpected);if(this.prevToken&&(t.colonPosition=this.prevToken.offset),!t.setValue(this._parseExpr()))return this.finish(t,xt.VariableValueExpected,[],e);while(this.accept(n.Exclamation)){if(!this.peekRegExp(n.Ident,/^(default|global)$/))return this.finish(t,xt.UnknownKeyword);this.consumeToken()}return this.peek(n.SemiColon)&&(t.semicolonPosition=this.token.offset),this.finish(t)},t.prototype._parseMediaContentStart=function(){return this._parseInterpolation()},t.prototype._parseMediaFeatureName=function(){return this._parseModuleMember()||this._parseFunction()||this._parseIdent()||this._parseVariable()},t.prototype._parseKeyframeSelector=function(){return this._tryParseKeyframeSelector()||this._parseControlStatement(this._parseKeyframeSelector.bind(this))||this._parseVariableDeclaration()||this._parseMixinContent()},t.prototype._parseVariable=function(){if(!this.peek(io))return null;var e=this.create(ot);return this.consumeToken(),e},t.prototype._parseModuleMember=function(){var e=this.mark(),t=this.create(ft);return t.setIdentifier(this._parseIdent([X.Module]))?this.hasWhitespace()||!this.acceptDelim(".")||this.hasWhitespace()?(this.restoreAtMark(e),null):t.addChild(this._parseVariable()||this._parseFunction())?t:this.finish(t,xt.IdentifierOrVariableExpected):null},t.prototype._parseIdent=function(e){var t=this;if(!this.peek(n.Ident)&&!this.peek(oo)&&!this.peekDelim("-"))return null;var r=this.create(se);r.referenceTypes=e,r.isCustomProperty=this.peekRegExp(n.Ident,/^--/);var i=!1,o=function(){var e=t.mark();return t.acceptDelim("-")&&(t.hasWhitespace()||t.acceptDelim("-"),t.hasWhitespace())?(t.restoreAtMark(e),null):t._parseInterpolation()};while(this.accept(n.Ident)||r.addChild(o())||i&&(this.acceptDelim("-")||this.accept(n.Num)))if(i=!0,this.hasWhitespace())break;return i?this.finish(r):null},t.prototype._parseTerm=function(){var t=this.create(Je);if(t.setExpression(this._parseModuleMember()))return this.finish(t);var r=e.prototype._parseTerm.call(this);return r||(t.setExpression(this._parseVariable())||t.setExpression(this._parseSelectorCombinator())||t.setExpression(this._tryParsePrio())?this.finish(t):null)},t.prototype._parseInterpolation=function(){if(this.peek(oo)){var e=this.create(it);return this.consumeToken(),e.addChild(this._parseExpr())||this._parseSelectorCombinator()?this.accept(n.CurlyR)?this.finish(e):this.finish(e,xt.RightCurlyExpected):this.accept(n.CurlyR)?this.finish(e):this.finish(e,xt.ExpressionExpected)}return null},t.prototype._parseOperator=function(){if(this.peek(so)||this.peek(ao)||this.peek(co)||this.peek(uo)||this.peekDelim(">")||this.peekDelim("<")||this.peekIdent("and")||this.peekIdent("or")||this.peekDelim("%")){var t=this.createNode(J.Operator);return this.consumeToken(),this.finish(t)}return e.prototype._parseOperator.call(this)},t.prototype._parseUnaryOperator=function(){if(this.peekIdent("not")){var t=this.create(ie);return this.consumeToken(),this.finish(t)}return e.prototype._parseUnaryOperator.call(this)},t.prototype._parseRuleSetDeclaration=function(){return this.peek(n.AtKeyword)?this._parseKeyframe()||this._parseImport()||this._parseMedia(!0)||this._parseFontFace()||this._parseWarnAndDebug()||this._parseControlStatement()||this._parseFunctionDeclaration()||this._parseExtends()||this._parseMixinReference()||this._parseMixinContent()||this._parseMixinDeclaration()||this._parseRuleset(!0)||this._parseSupports(!0):this._parseVariableDeclaration()||this._tryParseRuleset(!0)||e.prototype._parseRuleSetDeclaration.call(this)},t.prototype._parseDeclaration=function(e){var t=this.create(ye);if(!t.setProperty(this._parseProperty()))return null;if(!this.accept(n.Colon))return this.finish(t,xt.ColonExpected,[n.Colon],e);this.prevToken&&(t.colonPosition=this.prevToken.offset);var r=!1;if(t.setValue(this._parseExpr())&&(r=!0,t.addChild(this._parsePrio())),this.peek(n.CurlyL))t.setNestedProperties(this._parseNestedProperties());else if(!r)return this.finish(t,xt.PropertyValueExpected);return this.peek(n.SemiColon)&&(t.semicolonPosition=this.token.offset),this.finish(t)},t.prototype._parseNestedProperties=function(){var e=this.create(De);return this._parseBody(e,this._parseDeclaration.bind(this))},t.prototype._parseExtends=function(){if(this.peekKeyword("@extend")){var e=this.create(st);if(this.consumeToken(),!e.getSelectors().addChild(this._parseSimpleSelector()))return this.finish(e,xt.SelectorExpected);while(this.accept(n.Comma))e.getSelectors().addChild(this._parseSimpleSelector());return this.accept(n.Exclamation)&&!this.acceptIdent("optional")?this.finish(e,xt.UnknownKeyword):this.finish(e)}return null},t.prototype._parseSimpleSelectorBody=function(){return this._parseSelectorCombinator()||this._parseSelectorPlaceholder()||e.prototype._parseSimpleSelectorBody.call(this)},t.prototype._parseSelectorCombinator=function(){if(this.peekDelim("&")){var e=this.createNode(J.SelectorCombinator);this.consumeToken();while(!this.hasWhitespace()&&(this.acceptDelim("-")||this.accept(n.Num)||this.accept(n.Dimension)||e.addChild(this._parseIdent())||this.acceptDelim("&")));return this.finish(e)}return null},t.prototype._parseSelectorPlaceholder=function(){if(this.peekDelim("%")){var e=this.createNode(J.SelectorPlaceholder);return this.consumeToken(),this._parseIdent(),this.finish(e)}if(this.peekKeyword("@at-root")){e=this.createNode(J.SelectorPlaceholder);return this.consumeToken(),this.finish(e)}return null},t.prototype._parseElementName=function(){var t=this.mark(),r=e.prototype._parseElementName.call(this);return r&&!this.hasWhitespace()&&this.peek(n.ParenthesisL)?(this.restoreAtMark(t),null):r},t.prototype._tryParsePseudoIdentifier=function(){return this._parseInterpolation()||e.prototype._tryParsePseudoIdentifier.call(this)},t.prototype._parseWarnAndDebug=function(){if(!this.peekKeyword("@debug")&&!this.peekKeyword("@warn")&&!this.peekKeyword("@error"))return null;var e=this.createNode(J.Debug);return this.consumeToken(),e.addChild(this._parseExpr()),this.finish(e)},t.prototype._parseControlStatement=function(e){return void 0===e&&(e=this._parseRuleSetDeclaration.bind(this)),this.peek(n.AtKeyword)?this._parseIfStatement(e)||this._parseForStatement(e)||this._parseEachStatement(e)||this._parseWhileStatement(e):null},t.prototype._parseIfStatement=function(e){return this.peekKeyword("@if")?this._internalParseIfStatement(e):null},t.prototype._internalParseIfStatement=function(e){var t=this.create(_e);if(this.consumeToken(),!t.setExpression(this._parseExpr(!0)))return this.finish(t,xt.ExpressionExpected);if(this._parseBody(t,e),this.acceptKeyword("@else"))if(this.peekIdent("if"))t.setElseClause(this._internalParseIfStatement(e));else if(this.peek(n.CurlyL)){var r=this.create(Ee);this._parseBody(r,e),t.setElseClause(r)}return this.finish(t)},t.prototype._parseForStatement=function(e){if(!this.peekKeyword("@for"))return null;var t=this.create(we);return this.consumeToken(),t.setVariable(this._parseVariable())?this.acceptIdent("from")?t.addChild(this._parseBinaryExpr())?this.acceptIdent("to")||this.acceptIdent("through")?t.addChild(this._parseBinaryExpr())?this._parseBody(t,e):this.finish(t,xt.ExpressionExpected,[n.CurlyR]):this.finish(t,yo.ThroughOrToExpected,[n.CurlyR]):this.finish(t,xt.ExpressionExpected,[n.CurlyR]):this.finish(t,yo.FromExpected,[n.CurlyR]):this.finish(t,xt.VariableNameExpected,[n.CurlyR])},t.prototype._parseEachStatement=function(e){if(!this.peekKeyword("@each"))return null;var t=this.create(Pe);this.consumeToken();var r=t.getVariables();if(!r.addChild(this._parseVariable()))return this.finish(t,xt.VariableNameExpected,[n.CurlyR]);while(this.accept(n.Comma))if(!r.addChild(this._parseVariable()))return this.finish(t,xt.VariableNameExpected,[n.CurlyR]);return this.finish(r),this.acceptIdent("in")?t.addChild(this._parseExpr())?this._parseBody(t,e):this.finish(t,xt.ExpressionExpected,[n.CurlyR]):this.finish(t,yo.InExpected,[n.CurlyR])},t.prototype._parseWhileStatement=function(e){if(!this.peekKeyword("@while"))return null;var t=this.create(Se);return this.consumeToken(),t.addChild(this._parseBinaryExpr())?this._parseBody(t,e):this.finish(t,xt.ExpressionExpected,[n.CurlyR])},t.prototype._parseFunctionBodyDeclaration=function(){return this._parseVariableDeclaration()||this._parseReturnStatement()||this._parseWarnAndDebug()||this._parseControlStatement(this._parseFunctionBodyDeclaration.bind(this))},t.prototype._parseFunctionDeclaration=function(){if(!this.peekKeyword("@function"))return null;var e=this.create(Ie);if(this.consumeToken(),!e.setIdentifier(this._parseIdent([X.Function])))return this.finish(e,xt.IdentifierExpected,[n.CurlyR]);if(!this.accept(n.ParenthesisL))return this.finish(e,xt.LeftParenthesisExpected,[n.CurlyR]);if(e.getParameters().addChild(this._parseParameterDeclaration()))while(this.accept(n.Comma)){if(this.peek(n.ParenthesisR))break;if(!e.getParameters().addChild(this._parseParameterDeclaration()))return this.finish(e,xt.VariableNameExpected)}return this.accept(n.ParenthesisR)?this._parseBody(e,this._parseFunctionBodyDeclaration.bind(this)):this.finish(e,xt.RightParenthesisExpected,[n.CurlyR])},t.prototype._parseReturnStatement=function(){if(!this.peekKeyword("@return"))return null;var e=this.createNode(J.ReturnStatement);return this.consumeToken(),e.addChild(this._parseExpr())?this.finish(e):this.finish(e,xt.ExpressionExpected)},t.prototype._parseMixinDeclaration=function(){if(!this.peekKeyword("@mixin"))return null;var e=this.create(ct);if(this.consumeToken(),!e.setIdentifier(this._parseIdent([X.Mixin])))return this.finish(e,xt.IdentifierExpected,[n.CurlyR]);if(this.accept(n.ParenthesisL)){if(e.getParameters().addChild(this._parseParameterDeclaration()))while(this.accept(n.Comma)){if(this.peek(n.ParenthesisR))break;if(!e.getParameters().addChild(this._parseParameterDeclaration()))return this.finish(e,xt.VariableNameExpected)}if(!this.accept(n.ParenthesisR))return this.finish(e,xt.RightParenthesisExpected,[n.CurlyR])}return this._parseBody(e,this._parseRuleSetDeclaration.bind(this))},t.prototype._parseParameterDeclaration=function(){var e=this.create(xe);return e.setIdentifier(this._parseVariable())?(this.accept(ho),this.accept(n.Colon)&&!e.setDefaultValue(this._parseExpr(!0))?this.finish(e,xt.VariableValueExpected,[],[n.Comma,n.ParenthesisR]):this.finish(e)):null},t.prototype._parseMixinContent=function(){if(!this.peekKeyword("@content"))return null;var e=this.createNode(J.MixinContent);return this.consumeToken(),this.finish(e)},t.prototype._parseMixinReference=function(){if(!this.peekKeyword("@include"))return null;var e=this.create(at);this.consumeToken();var t=this._parseIdent([X.Mixin]);if(!e.setIdentifier(t))return this.finish(e,xt.IdentifierExpected,[n.CurlyR]);if(!this.hasWhitespace()&&this.acceptDelim(".")&&!this.hasWhitespace()){var r=this._parseIdent([X.Mixin]);if(!r)return this.finish(e,xt.IdentifierExpected,[n.CurlyR]);var i=this.create(ft);t.referenceTypes=[X.Module],i.setIdentifier(t),e.setIdentifier(r),e.addChild(i)}if(this.accept(n.ParenthesisL)){if(e.getArguments().addChild(this._parseFunctionArgument()))while(this.accept(n.Comma)){if(this.peek(n.ParenthesisR))break;if(!e.getArguments().addChild(this._parseFunctionArgument()))return this.finish(e,xt.ExpressionExpected)}if(!this.accept(n.ParenthesisR))return this.finish(e,xt.RightParenthesisExpected)}if(this.peek(n.CurlyL)){var o=this.create(ue);this._parseBody(o,this._parseMixinReferenceBodyStatement.bind(this)),e.setContent(o)}return this.finish(e)},t.prototype._parseMixinReferenceBodyStatement=function(){return this._tryParseKeyframeSelector()||this._parseRuleSetDeclaration()},t.prototype._parseFunctionArgument=function(){var e=this.create(Ce),t=this.mark(),r=this._parseVariable();if(r)if(this.accept(n.Colon))e.setIdentifier(r);else{if(this.accept(ho))return e.setValue(r),this.finish(e);this.restoreAtMark(t)}return e.setValue(this._parseExpr(!0))?(this.accept(ho),e.addChild(this._parsePrio()),this.finish(e)):null},t.prototype._parseURLArgument=function(){var t=this.mark(),r=e.prototype._parseURLArgument.call(this);if(!r||!this.peek(n.ParenthesisR)){this.restoreAtMark(t);var i=this.create(ie);return i.addChild(this._parseBinaryExpr()),this.finish(i)}return r},t.prototype._parseOperation=function(){if(!this.peek(n.ParenthesisL))return null;var e=this.create(ie);this.consumeToken();while(e.addChild(this._parseListElement()))this.accept(n.Comma);return this.accept(n.ParenthesisR)?this.finish(e):this.finish(e,xt.RightParenthesisExpected)},t.prototype._parseListElement=function(){var e=this.create(ht),t=this._parseBinaryExpr();if(!t)return null;if(this.accept(n.Colon)){if(e.setKey(t),!e.setValue(this._parseBinaryExpr()))return this.finish(e,xt.ExpressionExpected)}else e.setValue(t);return this.finish(e)},t.prototype._parseUse=function(){if(!this.peek(po))return null;var e=this.create(Oe);if(this.consumeToken(),!e.addChild(this._parseStringLiteral()))return this.finish(e,xt.StringLiteralExpected);if(!this.peek(n.SemiColon)&&!this.peek(n.EOF)){if(!this.peekRegExp(n.Ident,/as|with/))return this.finish(e,xt.UnknownKeyword);if(this.acceptIdent("as")&&!e.setIdentifier(this._parseIdent([X.Module]))&&!this.acceptDelim("*"))return this.finish(e,xt.IdentifierOrWildcardExpected);if(this.acceptIdent("with")){if(!this.accept(n.ParenthesisL))return this.finish(e,xt.LeftParenthesisExpected,[n.ParenthesisR]);if(!e.getParameters().addChild(this._parseModuleConfigDeclaration()))return this.finish(e,xt.VariableNameExpected);while(this.accept(n.Comma)){if(this.peek(n.ParenthesisR))break;if(!e.getParameters().addChild(this._parseModuleConfigDeclaration()))return this.finish(e,xt.VariableNameExpected)}if(!this.accept(n.ParenthesisR))return this.finish(e,xt.RightParenthesisExpected)}}return this.accept(n.SemiColon)||this.accept(n.EOF)?this.finish(e):this.finish(e,xt.SemiColonExpected)},t.prototype._parseModuleConfigDeclaration=function(){var e=this.create(Ve);return e.setIdentifier(this._parseVariable())?this.accept(n.Colon)&&e.setValue(this._parseExpr(!0))?this.finish(e):this.finish(e,xt.VariableValueExpected,[],[n.Comma,n.ParenthesisR]):null},t.prototype._parseForward=function(){if(!this.peek(lo))return null;var e=this.create(Le);if(this.consumeToken(),!e.addChild(this._parseStringLiteral()))return this.finish(e,xt.StringLiteralExpected);if(!this.peek(n.SemiColon)&&!this.peek(n.EOF)){if(!this.peekRegExp(n.Ident,/as|hide|show/))return this.finish(e,xt.UnknownKeyword);if(this.acceptIdent("as")){var t=this._parseIdent([X.Forward]);if(!e.setIdentifier(t))return this.finish(e,xt.IdentifierExpected);if(this.hasWhitespace()||!this.acceptDelim("*"))return this.finish(e,xt.WildcardExpected)}if((this.peekIdent("hide")||this.peekIdent("show"))&&!e.addChild(this._parseForwardVisibility()))return this.finish(e,xt.IdentifierOrVariableExpected)}return this.accept(n.SemiColon)||this.accept(n.EOF)?this.finish(e):this.finish(e,xt.SemiColonExpected)},t.prototype._parseForwardVisibility=function(){var e=this.create(Ne);e.setIdentifier(this._parseIdent());while(e.addChild(this._parseVariable()||this._parseIdent()));return e.getChildren().length>1?e:null}}(pr),function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},e(t,r)};return function(t,r){function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}()),ko=vt();(function(e){function t(r){var n=e.call(this,"$",r)||this;return xo(t.scssModuleLoaders),xo(t.scssModuleBuiltIns),n}bo(t,e),t.prototype.isImportPathParent=function(t){return t===J.Forward||t===J.Use||e.prototype.isImportPathParent.call(this,t)},t.prototype.getCompletionForImportPath=function(r,n){var i,o=r.getParent().type;return o!==J.Forward&&o!==J.Use||(i=n.items).push.apply(i,t.scssModuleBuiltIns),e.prototype.getCompletionForImportPath.call(this,r,n)},t.prototype.createReplaceFunction=function(){var e=1;return function(r,n){return"\\"+n+": ${"+e+++":"+(t.variableDefaults[n]||"")+"}"}},t.prototype.createFunctionProposals=function(e,t,r,n){for(var i=0,o=e;i<o.length;i++){var s=o[i],a=s.func.replace(/\[?(\$\w+)\]?/g,this.createReplaceFunction()),c=s.func.substr(0,s.func.indexOf("(")),u={label:c,detail:s.func,documentation:s.desc,textEdit:Ar.replace(this.getCompletionRange(t),a),insertTextFormat:Zr.Snippet,kind:Xr.Function};r&&(u.sortText="z"),n.items.push(u)}return n},t.prototype.getCompletionsForSelector=function(r,n,i){return this.createFunctionProposals(t.selectorFuncs,null,!0,i),e.prototype.getCompletionsForSelector.call(this,r,n,i)},t.prototype.getTermProposals=function(r,n,i){var o=t.builtInFuncs;return r&&(o=o.filter((function(e){return!e.type||!r.restrictions||-1!==r.restrictions.indexOf(e.type)}))),this.createFunctionProposals(o,n,!0,i),e.prototype.getTermProposals.call(this,r,n,i)},t.prototype.getColorProposals=function(r,n,i){return this.createFunctionProposals(t.colorProposals,n,!1,i),e.prototype.getColorProposals.call(this,r,n,i)},t.prototype.getCompletionsForDeclarationProperty=function(t,r){return this.getCompletionForAtDirectives(r),this.getCompletionsForSelector(null,!0,r),e.prototype.getCompletionsForDeclarationProperty.call(this,t,r)},t.prototype.getCompletionsForExtendsReference=function(e,t,r){for(var n=this.getSymbolContext().findSymbolsAtOffset(this.offset,X.Rule),i=0,o=n;i<o.length;i++){var s=o[i],a={label:s.name,textEdit:Ar.replace(this.getCompletionRange(t),s.name),kind:Xr.Function};r.items.push(a)}return r},t.prototype.getCompletionForAtDirectives=function(e){var r;return(r=e.items).push.apply(r,t.scssAtDirectives),e},t.prototype.getCompletionForTopLevel=function(t){return this.getCompletionForAtDirectives(t),this.getCompletionForModuleLoaders(t),e.prototype.getCompletionForTopLevel.call(this,t),t},t.prototype.getCompletionForModuleLoaders=function(e){var r;return(r=e.items).push.apply(r,t.scssModuleLoaders),e},t.variableDefaults={$red:"1",$green:"2",$blue:"3",$alpha:"1.0",$color:"#000000",$weight:"0.5",$hue:"0",$saturation:"0%",$lightness:"0%",$degrees:"0",$amount:"0",$string:'""',$substring:'"s"',$number:"0",$limit:"1"},t.colorProposals=[{func:"red($color)",desc:ko("scss.builtin.red","Gets the red component of a color.")},{func:"green($color)",desc:ko("scss.builtin.green","Gets the green component of a color.")},{func:"blue($color)",desc:ko("scss.builtin.blue","Gets the blue component of a color.")},{func:"mix($color, $color, [$weight])",desc:ko("scss.builtin.mix","Mixes two colors together.")},{func:"hue($color)",desc:ko("scss.builtin.hue","Gets the hue component of a color.")},{func:"saturation($color)",desc:ko("scss.builtin.saturation","Gets the saturation component of a color.")},{func:"lightness($color)",desc:ko("scss.builtin.lightness","Gets the lightness component of a color.")},{func:"adjust-hue($color, $degrees)",desc:ko("scss.builtin.adjust-hue","Changes the hue of a color.")},{func:"lighten($color, $amount)",desc:ko("scss.builtin.lighten","Makes a color lighter.")},{func:"darken($color, $amount)",desc:ko("scss.builtin.darken","Makes a color darker.")},{func:"saturate($color, $amount)",desc:ko("scss.builtin.saturate","Makes a color more saturated.")},{func:"desaturate($color, $amount)",desc:ko("scss.builtin.desaturate","Makes a color less saturated.")},{func:"grayscale($color)",desc:ko("scss.builtin.grayscale","Converts a color to grayscale.")},{func:"complement($color)",desc:ko("scss.builtin.complement","Returns the complement of a color.")},{func:"invert($color)",desc:ko("scss.builtin.invert","Returns the inverse of a color.")},{func:"alpha($color)",desc:ko("scss.builtin.alpha","Gets the opacity component of a color.")},{func:"opacity($color)",desc:"Gets the alpha component (opacity) of a color."},{func:"rgba($color, $alpha)",desc:ko("scss.builtin.rgba","Changes the alpha component for a color.")},{func:"opacify($color, $amount)",desc:ko("scss.builtin.opacify","Makes a color more opaque.")},{func:"fade-in($color, $amount)",desc:ko("scss.builtin.fade-in","Makes a color more opaque.")},{func:"transparentize($color, $amount)",desc:ko("scss.builtin.transparentize","Makes a color more transparent.")},{func:"fade-out($color, $amount)",desc:ko("scss.builtin.fade-out","Makes a color more transparent.")},{func:"adjust-color($color, [$red], [$green], [$blue], [$hue], [$saturation], [$lightness], [$alpha])",desc:ko("scss.builtin.adjust-color","Increases or decreases one or more components of a color.")},{func:"scale-color($color, [$red], [$green], [$blue], [$saturation], [$lightness], [$alpha])",desc:ko("scss.builtin.scale-color","Fluidly scales one or more properties of a color.")},{func:"change-color($color, [$red], [$green], [$blue], [$hue], [$saturation], [$lightness], [$alpha])",desc:ko("scss.builtin.change-color","Changes one or more properties of a color.")},{func:"ie-hex-str($color)",desc:ko("scss.builtin.ie-hex-str","Converts a color into the format understood by IE filters.")}],t.selectorFuncs=[{func:"selector-nest($selectors…)",desc:ko("scss.builtin.selector-nest","Nests selector beneath one another like they would be nested in the stylesheet.")},{func:"selector-append($selectors…)",desc:ko("scss.builtin.selector-append","Appends selectors to one another without spaces in between.")},{func:"selector-extend($selector, $extendee, $extender)",desc:ko("scss.builtin.selector-extend","Extends $extendee with $extender within $selector.")},{func:"selector-replace($selector, $original, $replacement)",desc:ko("scss.builtin.selector-replace","Replaces $original with $replacement within $selector.")},{func:"selector-unify($selector1, $selector2)",desc:ko("scss.builtin.selector-unify","Unifies two selectors to produce a selector that matches elements matched by both.")},{func:"is-superselector($super, $sub)",desc:ko("scss.builtin.is-superselector","Returns whether $super matches all the elements $sub does, and possibly more.")},{func:"simple-selectors($selector)",desc:ko("scss.builtin.simple-selectors","Returns the simple selectors that comprise a compound selector.")},{func:"selector-parse($selector)",desc:ko("scss.builtin.selector-parse","Parses a selector into the format returned by &.")}],t.builtInFuncs=[{func:"unquote($string)",desc:ko("scss.builtin.unquote","Removes quotes from a string.")},{func:"quote($string)",desc:ko("scss.builtin.quote","Adds quotes to a string.")},{func:"str-length($string)",desc:ko("scss.builtin.str-length","Returns the number of characters in a string.")},{func:"str-insert($string, $insert, $index)",desc:ko("scss.builtin.str-insert","Inserts $insert into $string at $index.")},{func:"str-index($string, $substring)",desc:ko("scss.builtin.str-index","Returns the index of the first occurance of $substring in $string.")},{func:"str-slice($string, $start-at, [$end-at])",desc:ko("scss.builtin.str-slice","Extracts a substring from $string.")},{func:"to-upper-case($string)",desc:ko("scss.builtin.to-upper-case","Converts a string to upper case.")},{func:"to-lower-case($string)",desc:ko("scss.builtin.to-lower-case","Converts a string to lower case.")},{func:"percentage($number)",desc:ko("scss.builtin.percentage","Converts a unitless number to a percentage."),type:"percentage"},{func:"round($number)",desc:ko("scss.builtin.round","Rounds a number to the nearest whole number.")},{func:"ceil($number)",desc:ko("scss.builtin.ceil","Rounds a number up to the next whole number.")},{func:"floor($number)",desc:ko("scss.builtin.floor","Rounds a number down to the previous whole number.")},{func:"abs($number)",desc:ko("scss.builtin.abs","Returns the absolute value of a number.")},{func:"min($numbers)",desc:ko("scss.builtin.min","Finds the minimum of several numbers.")},{func:"max($numbers)",desc:ko("scss.builtin.max","Finds the maximum of several numbers.")},{func:"random([$limit])",desc:ko("scss.builtin.random","Returns a random number.")},{func:"length($list)",desc:ko("scss.builtin.length","Returns the length of a list.")},{func:"nth($list, $n)",desc:ko("scss.builtin.nth","Returns a specific item in a list.")},{func:"set-nth($list, $n, $value)",desc:ko("scss.builtin.set-nth","Replaces the nth item in a list.")},{func:"join($list1, $list2, [$separator])",desc:ko("scss.builtin.join","Joins together two lists into one.")},{func:"append($list1, $val, [$separator])",desc:ko("scss.builtin.append","Appends a single value onto the end of a list.")},{func:"zip($lists)",desc:ko("scss.builtin.zip","Combines several lists into a single multidimensional list.")},{func:"index($list, $value)",desc:ko("scss.builtin.index","Returns the position of a value within a list.")},{func:"list-separator(#list)",desc:ko("scss.builtin.list-separator","Returns the separator of a list.")},{func:"map-get($map, $key)",desc:ko("scss.builtin.map-get","Returns the value in a map associated with a given key.")},{func:"map-merge($map1, $map2)",desc:ko("scss.builtin.map-merge","Merges two maps together into a new map.")},{func:"map-remove($map, $keys)",desc:ko("scss.builtin.map-remove","Returns a new map with keys removed.")},{func:"map-keys($map)",desc:ko("scss.builtin.map-keys","Returns a list of all keys in a map.")},{func:"map-values($map)",desc:ko("scss.builtin.map-values","Returns a list of all values in a map.")},{func:"map-has-key($map, $key)",desc:ko("scss.builtin.map-has-key","Returns whether a map has a value associated with a given key.")},{func:"keywords($args)",desc:ko("scss.builtin.keywords","Returns the keywords passed to a function that takes variable arguments.")},{func:"feature-exists($feature)",desc:ko("scss.builtin.feature-exists","Returns whether a feature exists in the current Sass runtime.")},{func:"variable-exists($name)",desc:ko("scss.builtin.variable-exists","Returns whether a variable with the given name exists in the current scope.")},{func:"global-variable-exists($name)",desc:ko("scss.builtin.global-variable-exists","Returns whether a variable with the given name exists in the global scope.")},{func:"function-exists($name)",desc:ko("scss.builtin.function-exists","Returns whether a function with the given name exists.")},{func:"mixin-exists($name)",desc:ko("scss.builtin.mixin-exists","Returns whether a mixin with the given name exists.")},{func:"inspect($value)",desc:ko("scss.builtin.inspect","Returns the string representation of a value as it would be represented in Sass.")},{func:"type-of($value)",desc:ko("scss.builtin.type-of","Returns the type of a value.")},{func:"unit($number)",desc:ko("scss.builtin.unit","Returns the unit(s) associated with a number.")},{func:"unitless($number)",desc:ko("scss.builtin.unitless","Returns whether a number has units.")},{func:"comparable($number1, $number2)",desc:ko("scss.builtin.comparable","Returns whether two numbers can be added, subtracted, or compared.")},{func:"call($name, $args…)",desc:ko("scss.builtin.call","Dynamically calls a Sass function.")}],t.scssAtDirectives=[{label:"@extend",documentation:ko("scss.builtin.@extend","Inherits the styles of another selector."),kind:Xr.Keyword},{label:"@at-root",documentation:ko("scss.builtin.@at-root","Causes one or more rules to be emitted at the root of the document."),kind:Xr.Keyword},{label:"@debug",documentation:ko("scss.builtin.@debug","Prints the value of an expression to the standard error output stream. Useful for debugging complicated Sass files."),kind:Xr.Keyword},{label:"@warn",documentation:ko("scss.builtin.@warn","Prints the value of an expression to the standard error output stream. Useful for libraries that need to warn users of deprecations or recovering from minor mixin usage mistakes. Warnings can be turned off with the `--quiet` command-line option or the `:quiet` Sass option."),kind:Xr.Keyword},{label:"@error",documentation:ko("scss.builtin.@error","Throws the value of an expression as a fatal error with stack trace. Useful for validating arguments to mixins and functions."),kind:Xr.Keyword},{label:"@if",documentation:ko("scss.builtin.@if","Includes the body if the expression does not evaluate to `false` or `null`."),insertText:"@if ${1:expr} {\n\t$0\n}",insertTextFormat:Zr.Snippet,kind:Xr.Keyword},{label:"@for",documentation:ko("scss.builtin.@for","For loop that repeatedly outputs a set of styles for each `$var` in the `from/through` or `from/to` clause."),insertText:"@for \\$${1:var} from ${2:start} ${3|to,through|} ${4:end} {\n\t$0\n}",insertTextFormat:Zr.Snippet,kind:Xr.Keyword},{label:"@each",documentation:ko("scss.builtin.@each","Each loop that sets `$var` to each item in the list or map, then outputs the styles it contains using that value of `$var`."),insertText:"@each \\$${1:var} in ${2:list} {\n\t$0\n}",insertTextFormat:Zr.Snippet,kind:Xr.Keyword},{label:"@while",documentation:ko("scss.builtin.@while","While loop that takes an expression and repeatedly outputs the nested styles until the statement evaluates to `false`."),insertText:"@while ${1:condition} {\n\t$0\n}",insertTextFormat:Zr.Snippet,kind:Xr.Keyword},{label:"@mixin",documentation:ko("scss.builtin.@mixin","Defines styles that can be re-used throughout the stylesheet with `@include`."),insertText:"@mixin ${1:name} {\n\t$0\n}",insertTextFormat:Zr.Snippet,kind:Xr.Keyword},{label:"@include",documentation:ko("scss.builtin.@include","Includes the styles defined by another mixin into the current rule."),kind:Xr.Keyword},{label:"@function",documentation:ko("scss.builtin.@function","Defines complex operations that can be re-used throughout stylesheets."),kind:Xr.Keyword}],t.scssModuleLoaders=[{label:"@use",documentation:ko("scss.builtin.@use","Loads mixins, functions, and variables from other Sass stylesheets as 'modules', and combines CSS from multiple stylesheets together."),references:[{name:"Sass documentation",url:"https://sass-lang.com/documentation/at-rules/use"}],insertText:"@use '$0';",insertTextFormat:Zr.Snippet,kind:Xr.Keyword},{label:"@forward",documentation:ko("scss.builtin.@forward","Loads a Sass stylesheet and makes its mixins, functions, and variables available when this stylesheet is loaded with the @use rule."),references:[{name:"Sass documentation",url:"https://sass-lang.com/documentation/at-rules/forward"}],insertText:"@forward '$0';",insertTextFormat:Zr.Snippet,kind:Xr.Keyword}],t.scssModuleBuiltIns=[{label:"sass:math",documentation:ko("scss.builtin.sass:math","Provides functions that operate on numbers."),references:[{name:"Sass documentation",url:"https://sass-lang.com/documentation/modules/math"}],kind:Xr.Module},{label:"sass:string",documentation:ko("scss.builtin.sass:string","Makes it easy to combine, search, or split apart strings."),references:[{name:"Sass documentation",url:"https://sass-lang.com/documentation/modules/string"}],kind:Xr.Module},{label:"sass:color",documentation:ko("scss.builtin.sass:color","Generates new colors based on existing ones, making it easy to build color themes."),references:[{name:"Sass documentation",url:"https://sass-lang.com/documentation/modules/color"}],kind:Xr.Module},{label:"sass:list",documentation:ko("scss.builtin.sass:list","Lets you access and modify values in lists."),references:[{name:"Sass documentation",url:"https://sass-lang.com/documentation/modules/list"}],kind:Xr.Module},{label:"sass:map",documentation:ko("scss.builtin.sass:map","Makes it possible to look up the value associated with a key in a map, and much more."),references:[{name:"Sass documentation",url:"https://sass-lang.com/documentation/modules/map"}],kind:Xr.Module},{label:"sass:selector",documentation:ko("scss.builtin.sass:selector","Provides access to Sass’s powerful selector engine."),references:[{name:"Sass documentation",url:"https://sass-lang.com/documentation/modules/selector"}],kind:Xr.Module},{label:"sass:meta",documentation:ko("scss.builtin.sass:meta","Exposes the details of Sass’s inner workings."),references:[{name:"Sass documentation",url:"https://sass-lang.com/documentation/modules/meta"}],kind:Xr.Module}]})(On);function xo(e){e.forEach((function(e){if(e.documentation&&e.references&&e.references.length>0){var t="string"===typeof e.documentation?{kind:"markdown",value:e.documentation}:{kind:"markdown",value:e.documentation.value};t.value+="\n\n",t.value+=e.references.map((function(e){return"["+e.name+"]("+e.url+")"})).join(" | "),e.documentation=t}}))}var Co=function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},e(t,r)};return function(t,r){function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),_o="/".charCodeAt(0),wo="\n".charCodeAt(0),Po="\r".charCodeAt(0),So="\f".charCodeAt(0),Eo="`".charCodeAt(0),Io=".".charCodeAt(0),Ro=n.CustomToken,Ao=Ro++,Do=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return Co(t,e),t.prototype.scanNext=function(t){var r=this.escapedJavaScript();return null!==r?this.finishToken(t,r):this.stream.advanceIfChars([Io,Io,Io])?this.finishToken(t,Ao):e.prototype.scanNext.call(this,t)},t.prototype.comment=function(){return!!e.prototype.comment.call(this)||!(this.inURL||!this.stream.advanceIfChars([_o,_o]))&&(this.stream.advanceWhileChar((function(e){switch(e){case wo:case Po:case So:return!1;default:return!0}})),!0)},t.prototype.escapedJavaScript=function(){var e=this.stream.peekChar();return e===Eo?(this.stream.advance(1),this.stream.advanceWhileChar((function(e){return e!==Eo})),this.stream.advanceIfChar(Eo)?n.EscapedJavaScript:n.BadEscapedJavaScript):null},t}(Z),To=function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},e(t,r)};return function(t,r){function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Fo=(function(e){function t(){return e.call(this,new Do)||this}To(t,e),t.prototype._parseStylesheetStatement=function(t){return void 0===t&&(t=!1),this.peek(n.AtKeyword)?this._parseVariableDeclaration()||this._parsePlugin()||e.prototype._parseStylesheetAtStatement.call(this,t):this._tryParseMixinDeclaration()||this._tryParseMixinReference()||this._parseFunction()||this._parseRuleset(!0)},t.prototype._parseImport=function(){if(!this.peekKeyword("@import")&&!this.peekKeyword("@import-once"))return null;var e=this.create(Me);if(this.consumeToken(),this.accept(n.ParenthesisL)){if(!this.accept(n.Ident))return this.finish(e,xt.IdentifierExpected,[n.SemiColon]);do{if(!this.accept(n.Comma))break}while(this.accept(n.Ident));if(!this.accept(n.ParenthesisR))return this.finish(e,xt.RightParenthesisExpected,[n.SemiColon])}return e.addChild(this._parseURILiteral())||e.addChild(this._parseStringLiteral())?(this.peek(n.SemiColon)||this.peek(n.EOF)||e.setMedialist(this._parseMediaQueryList()),this.finish(e)):this.finish(e,xt.URIOrStringExpected,[n.SemiColon])},t.prototype._parsePlugin=function(){if(!this.peekKeyword("@plugin"))return null;var e=this.createNode(J.Plugin);return this.consumeToken(),e.addChild(this._parseStringLiteral())?this.accept(n.SemiColon)?this.finish(e):this.finish(e,xt.SemiColonExpected):this.finish(e,xt.StringLiteralExpected)},t.prototype._parseMediaQuery=function(t){var r=e.prototype._parseMediaQuery.call(this,t);if(!r){var n=this.create(Ke);return n.addChild(this._parseVariable())?this.finish(n):null}return r},t.prototype._parseMediaDeclaration=function(e){return void 0===e&&(e=!1),this._tryParseRuleset(e)||this._tryToParseDeclaration()||this._tryParseMixinDeclaration()||this._tryParseMixinReference()||this._parseDetachedRuleSetMixin()||this._parseStylesheetStatement(e)},t.prototype._parseMediaFeatureName=function(){return this._parseIdent()||this._parseVariable()},t.prototype._parseVariableDeclaration=function(e){void 0===e&&(e=[]);var t=this.create(nt),r=this.mark();if(!t.setVariable(this._parseVariable(!0)))return null;if(!this.accept(n.Colon))return this.restoreAtMark(r),null;if(this.prevToken&&(t.colonPosition=this.prevToken.offset),t.setValue(this._parseDetachedRuleSet()))t.needsSemicolon=!1;else if(!t.setValue(this._parseExpr()))return this.finish(t,xt.VariableValueExpected,[],e);return t.addChild(this._parsePrio()),this.peek(n.SemiColon)&&(t.semicolonPosition=this.token.offset),this.finish(t)},t.prototype._parseDetachedRuleSet=function(){var e=this.mark();if(this.peekDelim("#")||this.peekDelim(".")){if(this.consumeToken(),this.hasWhitespace()||!this.accept(n.ParenthesisL))return this.restoreAtMark(e),null;var t=this.create(ct);if(t.getParameters().addChild(this._parseMixinParameter()))while(this.accept(n.Comma)||this.accept(n.SemiColon)){if(this.peek(n.ParenthesisR))break;t.getParameters().addChild(this._parseMixinParameter())||this.markError(t,xt.IdentifierExpected,[],[n.ParenthesisR])}if(!this.accept(n.ParenthesisR))return this.restoreAtMark(e),null}if(!this.peek(n.CurlyL))return null;var r=this.create(ue);return this._parseBody(r,this._parseDetachedRuleSetBody.bind(this)),this.finish(r)},t.prototype._parseDetachedRuleSetBody=function(){return this._tryParseKeyframeSelector()||this._parseRuleSetDeclaration()},t.prototype._addLookupChildren=function(e){if(!e.addChild(this._parseLookupValue()))return!1;var t=!1;while(1){if(this.peek(n.BracketL)&&(t=!0),!e.addChild(this._parseLookupValue()))break;t=!1}return!t},t.prototype._parseLookupValue=function(){var e=this.create(ie),t=this.mark();return this.accept(n.BracketL)&&((e.addChild(this._parseVariable(!1,!0))||e.addChild(this._parsePropertyIdentifier()))&&this.accept(n.BracketR)||this.accept(n.BracketR))?e:(this.restoreAtMark(t),null)},t.prototype._parseVariable=function(e,t){void 0===e&&(e=!1),void 0===t&&(t=!1);var r=!e&&this.peekDelim("$");if(!this.peekDelim("@")&&!r&&!this.peek(n.AtKeyword))return null;var i=this.create(ot),o=this.mark();while(this.acceptDelim("@")||!e&&this.acceptDelim("$"))if(this.hasWhitespace())return this.restoreAtMark(o),null;return(this.accept(n.AtKeyword)||this.accept(n.Ident))&&(t||!this.peek(n.BracketL)||this._addLookupChildren(i))?i:(this.restoreAtMark(o),null)},t.prototype._parseTerm=function(){var t=e.prototype._parseTerm.call(this);return t||(t=this.create(Je),t.setExpression(this._parseVariable())||t.setExpression(this._parseEscaped())||t.setExpression(this._tryParseMixinReference(!1))?this.finish(t):null)},t.prototype._parseEscaped=function(){if(this.peek(n.EscapedJavaScript)||this.peek(n.BadEscapedJavaScript)){var e=this.createNode(J.EscapedValue);return this.consumeToken(),this.finish(e)}if(this.peekDelim("~")){e=this.createNode(J.EscapedValue);return this.consumeToken(),this.accept(n.String)||this.accept(n.EscapedJavaScript)?this.finish(e):this.finish(e,xt.TermExpected)}return null},t.prototype._parseOperator=function(){var t=this._parseGuardOperator();return t||e.prototype._parseOperator.call(this)},t.prototype._parseGuardOperator=function(){if(this.peekDelim(">")){var e=this.createNode(J.Operator);return this.consumeToken(),this.acceptDelim("="),e}if(this.peekDelim("=")){e=this.createNode(J.Operator);return this.consumeToken(),this.acceptDelim("<"),e}if(this.peekDelim("<")){e=this.createNode(J.Operator);return this.consumeToken(),this.acceptDelim("="),e}return null},t.prototype._parseRuleSetDeclaration=function(){return this.peek(n.AtKeyword)?this._parseKeyframe()||this._parseMedia(!0)||this._parseImport()||this._parseSupports(!0)||this._parseDetachedRuleSetMixin()||this._parseVariableDeclaration()||this._parseUnknownAtRule():this._tryParseMixinDeclaration()||this._tryParseRuleset(!0)||this._tryParseMixinReference()||this._parseFunction()||this._parseExtend()||e.prototype._parseRuleSetDeclaration.call(this)},t.prototype._parseKeyframeIdent=function(){return this._parseIdent([X.Keyframe])||this._parseVariable()},t.prototype._parseKeyframeSelector=function(){return this._parseDetachedRuleSetMixin()||e.prototype._parseKeyframeSelector.call(this)},t.prototype._parseSimpleSelectorBody=function(){return this._parseSelectorCombinator()||e.prototype._parseSimpleSelectorBody.call(this)},t.prototype._parseSelector=function(e){var t=this.create(le),r=!1;e&&(r=t.addChild(this._parseCombinator()));while(t.addChild(this._parseSimpleSelector())){r=!0;var i=this.mark();if(t.addChild(this._parseGuard())&&this.peek(n.CurlyL))break;this.restoreAtMark(i),t.addChild(this._parseCombinator())}return r?this.finish(t):null},t.prototype._parseSelectorCombinator=function(){if(this.peekDelim("&")){var e=this.createNode(J.SelectorCombinator);this.consumeToken();while(!this.hasWhitespace()&&(this.acceptDelim("-")||this.accept(n.Num)||this.accept(n.Dimension)||e.addChild(this._parseIdent())||this.acceptDelim("&")));return this.finish(e)}return null},t.prototype._parseSelectorIdent=function(){if(!this.peekInterpolatedIdent())return null;var e=this.createNode(J.SelectorInterpolation),t=this._acceptInterpolatedIdent(e);return t?this.finish(e):null},t.prototype._parsePropertyIdentifier=function(e){void 0===e&&(e=!1);var t=/^[\w-]+/;if(!this.peekInterpolatedIdent()&&!this.peekRegExp(this.token.type,t))return null;var r=this.mark(),n=this.create(se);n.isCustomProperty=this.acceptDelim("-")&&this.acceptDelim("-");var i=!1;return i=e?n.isCustomProperty?n.addChild(this._parseIdent()):n.addChild(this._parseRegexp(t)):n.isCustomProperty?this._acceptInterpolatedIdent(n):this._acceptInterpolatedIdent(n,t),i?(e||this.hasWhitespace()||(this.acceptDelim("+"),this.hasWhitespace()||this.acceptIdent("_")),this.finish(n)):(this.restoreAtMark(r),null)},t.prototype.peekInterpolatedIdent=function(){return this.peek(n.Ident)||this.peekDelim("@")||this.peekDelim("$")||this.peekDelim("-")},t.prototype._acceptInterpolatedIdent=function(e,t){var r=this,i=!1,o=function(){var e=r.mark();return r.acceptDelim("-")&&(r.hasWhitespace()||r.acceptDelim("-"),r.hasWhitespace())?(r.restoreAtMark(e),null):r._parseInterpolation()},s=t?function(){return r.acceptRegexp(t)}:function(){return r.accept(n.Ident)};while(s()||e.addChild(this._parseInterpolation()||this.try(o)))if(i=!0,this.hasWhitespace())break;return i},t.prototype._parseInterpolation=function(){var e=this.mark();if(this.peekDelim("@")||this.peekDelim("$")){var t=this.createNode(J.Interpolation);return this.consumeToken(),this.hasWhitespace()||!this.accept(n.CurlyL)?(this.restoreAtMark(e),null):t.addChild(this._parseIdent())?this.accept(n.CurlyR)?this.finish(t):this.finish(t,xt.RightCurlyExpected):this.finish(t,xt.IdentifierExpected)}return null},t.prototype._tryParseMixinDeclaration=function(){var e=this.mark(),t=this.create(ct);if(!t.setIdentifier(this._parseMixinDeclarationIdentifier())||!this.accept(n.ParenthesisL))return this.restoreAtMark(e),null;if(t.getParameters().addChild(this._parseMixinParameter()))while(this.accept(n.Comma)||this.accept(n.SemiColon)){if(this.peek(n.ParenthesisR))break;t.getParameters().addChild(this._parseMixinParameter())||this.markError(t,xt.IdentifierExpected,[],[n.ParenthesisR])}return this.accept(n.ParenthesisR)?(t.setGuard(this._parseGuard()),this.peek(n.CurlyL)?this._parseBody(t,this._parseMixInBodyDeclaration.bind(this)):(this.restoreAtMark(e),null)):(this.restoreAtMark(e),null)},t.prototype._parseMixInBodyDeclaration=function(){return this._parseFontFace()||this._parseRuleSetDeclaration()},t.prototype._parseMixinDeclarationIdentifier=function(){var e;if(this.peekDelim("#")||this.peekDelim(".")){if(e=this.create(se),this.consumeToken(),this.hasWhitespace()||!e.addChild(this._parseIdent()))return null}else{if(!this.peek(n.Hash))return null;e=this.create(se),this.consumeToken()}return e.referenceTypes=[X.Mixin],this.finish(e)},t.prototype._parsePseudo=function(){if(!this.peek(n.Colon))return null;var t=this.mark(),r=this.create(st);return this.consumeToken(),this.acceptIdent("extend")?this._completeExtends(r):(this.restoreAtMark(t),e.prototype._parsePseudo.call(this))},t.prototype._parseExtend=function(){if(!this.peekDelim("&"))return null;var e=this.mark(),t=this.create(st);return this.consumeToken(),!this.hasWhitespace()&&this.accept(n.Colon)&&this.acceptIdent("extend")?this._completeExtends(t):(this.restoreAtMark(e),null)},t.prototype._completeExtends=function(e){if(!this.accept(n.ParenthesisL))return this.finish(e,xt.LeftParenthesisExpected);var t=e.getSelectors();if(!t.addChild(this._parseSelector(!0)))return this.finish(e,xt.SelectorExpected);while(this.accept(n.Comma))if(!t.addChild(this._parseSelector(!0)))return this.finish(e,xt.SelectorExpected);return this.accept(n.ParenthesisR)?this.finish(e):this.finish(e,xt.RightParenthesisExpected)},t.prototype._parseDetachedRuleSetMixin=function(){if(!this.peek(n.AtKeyword))return null;var e=this.mark(),t=this.create(at);return!t.addChild(this._parseVariable(!0))||!this.hasWhitespace()&&this.accept(n.ParenthesisL)?this.accept(n.ParenthesisR)?this.finish(t):this.finish(t,xt.RightParenthesisExpected):(this.restoreAtMark(e),null)},t.prototype._tryParseMixinReference=function(e){void 0===e&&(e=!0);var t=this.mark(),r=this.create(at),i=this._parseMixinDeclarationIdentifier();while(i){this.acceptDelim(">");var o=this._parseMixinDeclarationIdentifier();if(!o)break;r.getNamespaces().addChild(i),i=o}if(!r.setIdentifier(i))return this.restoreAtMark(t),null;var s=!1;if(!this.hasWhitespace()&&this.accept(n.ParenthesisL)){if(s=!0,r.getArguments().addChild(this._parseMixinArgument()))while(this.accept(n.Comma)||this.accept(n.SemiColon)){if(this.peek(n.ParenthesisR))break;if(!r.getArguments().addChild(this._parseMixinArgument()))return this.finish(r,xt.ExpressionExpected)}if(!this.accept(n.ParenthesisR))return this.finish(r,xt.RightParenthesisExpected);i.referenceTypes=[X.Mixin]}else i.referenceTypes=[X.Mixin,X.Rule];return this.peek(n.BracketL)?e||this._addLookupChildren(r):r.addChild(this._parsePrio()),s||this.peek(n.SemiColon)||this.peek(n.CurlyR)||this.peek(n.EOF)?this.finish(r):(this.restoreAtMark(t),null)},t.prototype._parseMixinArgument=function(){var e=this.create(Ce),t=this.mark(),r=this._parseVariable();return r&&(this.accept(n.Colon)?e.setIdentifier(r):this.restoreAtMark(t)),e.setValue(this._parseDetachedRuleSet()||this._parseExpr(!0))?this.finish(e):(this.restoreAtMark(t),null)},t.prototype._parseMixinParameter=function(){var e=this.create(xe);if(this.peekKeyword("@rest")){var t=this.create(ie);return this.consumeToken(),this.accept(Ao)?(e.setIdentifier(this.finish(t)),this.finish(e)):this.finish(e,xt.DotExpected,[],[n.Comma,n.ParenthesisR])}if(this.peek(Ao)){var r=this.create(ie);return this.consumeToken(),e.setIdentifier(this.finish(r)),this.finish(e)}var i=!1;return e.setIdentifier(this._parseVariable())&&(this.accept(n.Colon),i=!0),e.setDefaultValue(this._parseDetachedRuleSet()||this._parseExpr(!0))||i?this.finish(e):null},t.prototype._parseGuard=function(){if(!this.peekIdent("when"))return null;var e=this.create(lt);if(this.consumeToken(),e.isNegated=this.acceptIdent("not"),!e.getConditions().addChild(this._parseGuardCondition()))return this.finish(e,xt.ConditionExpected);while(this.acceptIdent("and")||this.accept(n.Comma))if(!e.getConditions().addChild(this._parseGuardCondition()))return this.finish(e,xt.ConditionExpected);return this.finish(e)},t.prototype._parseGuardCondition=function(){if(!this.peek(n.ParenthesisL))return null;var e=this.create(pt);return this.consumeToken(),e.addChild(this._parseExpr()),this.accept(n.ParenthesisR)?this.finish(e):this.finish(e,xt.RightParenthesisExpected)},t.prototype._parseFunction=function(){var e=this.mark(),t=this.create(ke);if(!t.setIdentifier(this._parseFunctionIdentifier()))return null;if(this.hasWhitespace()||!this.accept(n.ParenthesisL))return this.restoreAtMark(e),null;if(t.getArguments().addChild(this._parseMixinArgument()))while(this.accept(n.Comma)||this.accept(n.SemiColon)){if(this.peek(n.ParenthesisR))break;if(!t.getArguments().addChild(this._parseMixinArgument()))return this.finish(t,xt.ExpressionExpected)}return this.accept(n.ParenthesisR)?this.finish(t):this.finish(t,xt.RightParenthesisExpected)},t.prototype._parseFunctionIdentifier=function(){if(this.peekDelim("%")){var t=this.create(se);return t.referenceTypes=[X.Function],this.consumeToken(),this.finish(t)}return e.prototype._parseFunctionIdentifier.call(this)},t.prototype._parseURLArgument=function(){var t=this.mark(),r=e.prototype._parseURLArgument.call(this);if(!r||!this.peek(n.ParenthesisR)){this.restoreAtMark(t);var i=this.create(ie);return i.addChild(this._parseBinaryExpr()),this.finish(i)}return r}}(pr),function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},e(t,r)};return function(t,r){function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}()),Mo=vt();(function(e){function t(t){return e.call(this,"@",t)||this}Fo(t,e),t.prototype.createFunctionProposals=function(e,t,r,n){for(var i=0,o=e;i<o.length;i++){var s=o[i],a={label:s.name,detail:s.example,documentation:s.description,textEdit:Ar.replace(this.getCompletionRange(t),s.name+"($0)"),insertTextFormat:Zr.Snippet,kind:Xr.Function};r&&(a.sortText="z"),n.items.push(a)}return n},t.prototype.getTermProposals=function(r,n,i){var o=t.builtInProposals;return r&&(o=o.filter((function(e){return!e.type||!r.restrictions||-1!==r.restrictions.indexOf(e.type)}))),this.createFunctionProposals(o,n,!0,i),e.prototype.getTermProposals.call(this,r,n,i)},t.prototype.getColorProposals=function(r,n,i){return this.createFunctionProposals(t.colorProposals,n,!1,i),e.prototype.getColorProposals.call(this,r,n,i)},t.prototype.getCompletionsForDeclarationProperty=function(t,r){return this.getCompletionsForSelector(null,!0,r),e.prototype.getCompletionsForDeclarationProperty.call(this,t,r)},t.builtInProposals=[{name:"if",example:"if(condition, trueValue [, falseValue]);",description:Mo("less.builtin.if","returns one of two values depending on a condition.")},{name:"boolean",example:"boolean(condition);",description:Mo("less.builtin.boolean",'"store" a boolean test for later evaluation in a guard or if().')},{name:"length",example:"length(@list);",description:Mo("less.builtin.length","returns the number of elements in a value list")},{name:"extract",example:"extract(@list, index);",description:Mo("less.builtin.extract","returns a value at the specified position in the list")},{name:"range",example:"range([start, ] end [, step]);",description:Mo("less.builtin.range","generate a list spanning a range of values")},{name:"each",example:"each(@list, ruleset);",description:Mo("less.builtin.each","bind the evaluation of a ruleset to each member of a list.")},{name:"escape",example:"escape(@string);",description:Mo("less.builtin.escape","URL encodes a string")},{name:"e",example:"e(@string);",description:Mo("less.builtin.e","escape string content")},{name:"replace",example:"replace(@string, @pattern, @replacement[, @flags]);",description:Mo("less.builtin.replace","string replace")},{name:"unit",example:"unit(@dimension, [@unit: '']);",description:Mo("less.builtin.unit","remove or change the unit of a dimension")},{name:"color",example:"color(@string);",description:Mo("less.builtin.color","parses a string to a color"),type:"color"},{name:"convert",example:"convert(@value, unit);",description:Mo("less.builtin.convert","converts numbers from one type into another")},{name:"data-uri",example:"data-uri([mimetype,] url);",description:Mo("less.builtin.data-uri","inlines a resource and falls back to `url()`"),type:"url"},{name:"abs",description:Mo("less.builtin.abs","absolute value of a number"),example:"abs(number);"},{name:"acos",description:Mo("less.builtin.acos","arccosine - inverse of cosine function"),example:"acos(number);"},{name:"asin",description:Mo("less.builtin.asin","arcsine - inverse of sine function"),example:"asin(number);"},{name:"ceil",example:"ceil(@number);",description:Mo("less.builtin.ceil","rounds up to an integer")},{name:"cos",description:Mo("less.builtin.cos","cosine function"),example:"cos(number);"},{name:"floor",description:Mo("less.builtin.floor","rounds down to an integer"),example:"floor(@number);"},{name:"percentage",description:Mo("less.builtin.percentage","converts to a %, e.g. 0.5 > 50%"),example:"percentage(@number);",type:"percentage"},{name:"round",description:Mo("less.builtin.round","rounds a number to a number of places"),example:"round(number, [places: 0]);"},{name:"sqrt",description:Mo("less.builtin.sqrt","calculates square root of a number"),example:"sqrt(number);"},{name:"sin",description:Mo("less.builtin.sin","sine function"),example:"sin(number);"},{name:"tan",description:Mo("less.builtin.tan","tangent function"),example:"tan(number);"},{name:"atan",description:Mo("less.builtin.atan","arctangent - inverse of tangent function"),example:"atan(number);"},{name:"pi",description:Mo("less.builtin.pi","returns pi"),example:"pi();"},{name:"pow",description:Mo("less.builtin.pow","first argument raised to the power of the second argument"),example:"pow(@base, @exponent);"},{name:"mod",description:Mo("less.builtin.mod","first argument modulus second argument"),example:"mod(number, number);"},{name:"min",description:Mo("less.builtin.min","returns the lowest of one or more values"),example:"min(@x, @y);"},{name:"max",description:Mo("less.builtin.max","returns the lowest of one or more values"),example:"max(@x, @y);"}],t.colorProposals=[{name:"argb",example:"argb(@color);",description:Mo("less.builtin.argb","creates a #AARRGGBB")},{name:"hsl",example:"hsl(@hue, @saturation, @lightness);",description:Mo("less.builtin.hsl","creates a color")},{name:"hsla",example:"hsla(@hue, @saturation, @lightness, @alpha);",description:Mo("less.builtin.hsla","creates a color")},{name:"hsv",example:"hsv(@hue, @saturation, @value);",description:Mo("less.builtin.hsv","creates a color")},{name:"hsva",example:"hsva(@hue, @saturation, @value, @alpha);",description:Mo("less.builtin.hsva","creates a color")},{name:"hue",example:"hue(@color);",description:Mo("less.builtin.hue","returns the `hue` channel of `@color` in the HSL space")},{name:"saturation",example:"saturation(@color);",description:Mo("less.builtin.saturation","returns the `saturation` channel of `@color` in the HSL space")},{name:"lightness",example:"lightness(@color);",description:Mo("less.builtin.lightness","returns the `lightness` channel of `@color` in the HSL space")},{name:"hsvhue",example:"hsvhue(@color);",description:Mo("less.builtin.hsvhue","returns the `hue` channel of `@color` in the HSV space")},{name:"hsvsaturation",example:"hsvsaturation(@color);",description:Mo("less.builtin.hsvsaturation","returns the `saturation` channel of `@color` in the HSV space")},{name:"hsvvalue",example:"hsvvalue(@color);",description:Mo("less.builtin.hsvvalue","returns the `value` channel of `@color` in the HSV space")},{name:"red",example:"red(@color);",description:Mo("less.builtin.red","returns the `red` channel of `@color`")},{name:"green",example:"green(@color);",description:Mo("less.builtin.green","returns the `green` channel of `@color`")},{name:"blue",example:"blue(@color);",description:Mo("less.builtin.blue","returns the `blue` channel of `@color`")},{name:"alpha",example:"alpha(@color);",description:Mo("less.builtin.alpha","returns the `alpha` channel of `@color`")},{name:"luma",example:"luma(@color);",description:Mo("less.builtin.luma","returns the `luma` value (perceptual brightness) of `@color`")},{name:"saturate",example:"saturate(@color, 10%);",description:Mo("less.builtin.saturate","return `@color` 10% points more saturated")},{name:"desaturate",example:"desaturate(@color, 10%);",description:Mo("less.builtin.desaturate","return `@color` 10% points less saturated")},{name:"lighten",example:"lighten(@color, 10%);",description:Mo("less.builtin.lighten","return `@color` 10% points lighter")},{name:"darken",example:"darken(@color, 10%);",description:Mo("less.builtin.darken","return `@color` 10% points darker")},{name:"fadein",example:"fadein(@color, 10%);",description:Mo("less.builtin.fadein","return `@color` 10% points less transparent")},{name:"fadeout",example:"fadeout(@color, 10%);",description:Mo("less.builtin.fadeout","return `@color` 10% points more transparent")},{name:"fade",example:"fade(@color, 50%);",description:Mo("less.builtin.fade","return `@color` with 50% transparency")},{name:"spin",example:"spin(@color, 10);",description:Mo("less.builtin.spin","return `@color` with a 10 degree larger in hue")},{name:"mix",example:"mix(@color1, @color2, [@weight: 50%]);",description:Mo("less.builtin.mix","return a mix of `@color1` and `@color2`")},{name:"greyscale",example:"greyscale(@color);",description:Mo("less.builtin.greyscale","returns a grey, 100% desaturated color")},{name:"contrast",example:"contrast(@color1, [@darkcolor: black], [@lightcolor: white], [@threshold: 43%]);",description:Mo("less.builtin.contrast","return `@darkcolor` if `@color1 is> 43% luma` otherwise return `@lightcolor`, see notes")},{name:"multiply",example:"multiply(@color1, @color2);"},{name:"screen",example:"screen(@color1, @color2);"},{name:"overlay",example:"overlay(@color1, @color2);"},{name:"softlight",example:"softlight(@color1, @color2);"},{name:"hardlight",example:"hardlight(@color1, @color2);"},{name:"difference",example:"difference(@color1, @color2);"},{name:"exclusion",example:"exclusion(@color1, @color2);"},{name:"average",example:"average(@color1, @color2);"},{name:"negation",example:"negation(@color1, @color2);"}]})(On);var Oo=r("ed59"),Vo=function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},e(t,r)};return function(t,r){function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Lo=function(){return Lo=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r],t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},Lo.apply(this,arguments)},No=function(e,t,r,n){function i(e){return e instanceof r?e:new r((function(t){t(e)}))}return new(r||(r=Promise))((function(r,o){function s(e){try{c(n.next(e))}catch(t){o(t)}}function a(e){try{c(n["throw"](e))}catch(t){o(t)}}function c(e){e.done?r(e.value):i(e.value).then(s,a)}c((n=n.apply(e,t||[])).next())}))},$o=function(e,t){var r,n,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(e){return function(t){return c([e,t])}}function c(o){if(r)throw new TypeError("Generator is already executing.");while(s)try{if(r=1,n&&(i=2&o[0]?n["return"]:o[0]?n["throw"]||((i=n["return"])&&i.call(n),0):n.next)&&!(i=i.call(n,o[1])).done)return i;switch(n=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,n=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(i=s.trys,!(i=i.length>0&&i[i.length-1])&&(6===o[0]||2===o[0])){s=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){s.label=o[1];break}if(6===o[0]&&s.label<i[1]){s.label=i[1],i=o;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(o);break}i[2]&&s.ops.pop(),s.trys.pop();continue}o=t.call(e,s)}catch(a){o=[6,a],n=0}finally{r=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}};(function(e){function t(t){var r=e.call(this)||this;return r.fileSystemProvider=t,r}Vo(t,e),t.prototype.isRawStringDocumentLinkNode=function(t){return e.prototype.isRawStringDocumentLinkNode.call(this,t)||t.type===J.Use||t.type===J.Forward},t.prototype.findDocumentLinks2=function(e,t,r){return No(this,void 0,void 0,(function(){function n(e){if(""!==e.path&&!e.path.endsWith(".scss")&&!e.path.endsWith(".css")){if(e.path.endsWith("/"))return[e.with({path:e.path+"index.scss"}).toString(),e.with({path:e.path+"_index.scss"}).toString()];var t=e.path.split("/"),r=t[t.length-1],n=e.path.slice(0,-r.length);if(r.startsWith("_"))return e.path.endsWith(".scss")?void 0:[e.with({path:e.path+".scss"}).toString()];var i=r+".scss",o=function(t){return e.with({path:n+t}).toString()},s=o(i),a=o("_"+i),c=o(i.slice(0,-5)+"/index.scss"),u=o(i.slice(0,-5)+"/_index.scss"),h=o(i.slice(0,-5)+".css");return[s,a,c,u,h]}}function i(e){return No(this,void 0,void 0,(function(){var t;return $o(this,(function(r){switch(r.label){case 0:if(!s)return[2,!1];r.label=1;case 1:return r.trys.push([1,3,,4]),[4,s.stat(e)];case 2:return t=r.sent(),t.type===Sn.Unknown&&-1===t.size?[2,!1]:[2,!0];case 3:return r.sent(),[2,!1];case 4:return[2]}}))}))}var o,s,a,c,u,h,l,p;return $o(this,(function(f){switch(f.label){case 0:if(o=this.findDocumentLinks(e,t,r),s=this.fileSystemProvider,a=[],!s)return[3,9];c=0,f.label=1;case 1:if(!(c<o.length))return[3,8];if(u=o[c].target,!u)return[3,7];h=null;try{h=Oo["a"].parse(u)}catch(d){if(d instanceof URIError)return[3,7];throw d}return l=n(h),l?[3,3]:[4,i(u)];case 2:return f.sent()&&a.push(o[c]),[3,7];case 3:p=0,f.label=4;case 4:return p<l.length?[4,i(l[p])]:[3,7];case 5:if(f.sent())return a.push(Lo(Lo({},o[c]),{target:l[p]})),[3,7];f.label=6;case 6:return p++,[3,4];case 7:return c++,[3,1];case 8:return[3,10];case 9:a.push.apply(a,o),f.label=10;case 10:return[2,a]}}))}))}})(hi);var Bo=monaco.Uri,Uo=monaco.Range,Wo=function(){function e(e,t,r){var n=this;this._languageId=e,this._worker=t,this._disposables=[],this._listener=Object.create(null);var i=function(e){var t,r=e.getModeId();r===n._languageId&&(n._listener[e.uri.toString()]=e.onDidChangeContent((function(){window.clearTimeout(t),t=window.setTimeout((function(){return n._doValidate(e.uri,r)}),500)})),n._doValidate(e.uri,r))},o=function(e){monaco.editor.setModelMarkers(e,n._languageId,[]);var t=e.uri.toString(),r=n._listener[t];r&&(r.dispose(),delete n._listener[t])};this._disposables.push(monaco.editor.onDidCreateModel(i)),this._disposables.push(monaco.editor.onWillDisposeModel(o)),this._disposables.push(monaco.editor.onDidChangeModelLanguage((function(e){o(e.model),i(e.model)}))),r.onDidChange((function(e){monaco.editor.getModels().forEach((function(e){e.getModeId()===n._languageId&&(o(e),i(e))}))})),this._disposables.push({dispose:function(){for(var e in n._listener)n._listener[e].dispose()}}),monaco.editor.getModels().forEach(i)}return e.prototype.dispose=function(){this._disposables.forEach((function(e){return e&&e.dispose()})),this._disposables=[]},e.prototype._doValidate=function(e,t){this._worker(e).then((function(t){return t.doValidation(e.toString())})).then((function(r){var n=r.map((function(t){return Ko(e,t)})),i=monaco.editor.getModel(e);i.getModeId()===t&&monaco.editor.setModelMarkers(i,t,n)})).then(void 0,(function(e){console.error(e)}))},e}();function jo(e){switch(e){case Sr.Error:return monaco.MarkerSeverity.Error;case Sr.Warning:return monaco.MarkerSeverity.Warning;case Sr.Information:return monaco.MarkerSeverity.Info;case Sr.Hint:return monaco.MarkerSeverity.Hint;default:return monaco.MarkerSeverity.Info}}function Ko(e,t){var r="number"===typeof t.code?String(t.code):t.code;return{severity:jo(t.severity),startLineNumber:t.range.start.line+1,startColumn:t.range.start.character+1,endLineNumber:t.range.end.line+1,endColumn:t.range.end.character+1,message:t.message,code:r,source:t.source}}function qo(e){if(e)return{character:e.column-1,line:e.lineNumber-1}}function zo(e){if(e)return{start:{line:e.startLineNumber-1,character:e.startColumn-1},end:{line:e.endLineNumber-1,character:e.endColumn-1}}}function Ho(e){if(e)return new monaco.Range(e.start.line+1,e.start.character+1,e.end.line+1,e.end.character+1)}function Go(e){var t=monaco.languages.CompletionItemKind;switch(e){case Xr.Text:return t.Text;case Xr.Method:return t.Method;case Xr.Function:return t.Function;case Xr.Constructor:return t.Constructor;case Xr.Field:return t.Field;case Xr.Variable:return t.Variable;case Xr.Class:return t.Class;case Xr.Interface:return t.Interface;case Xr.Module:return t.Module;case Xr.Property:return t.Property;case Xr.Unit:return t.Unit;case Xr.Value:return t.Value;case Xr.Enum:return t.Enum;case Xr.Keyword:return t.Keyword;case Xr.Snippet:return t.Snippet;case Xr.Color:return t.Color;case Xr.File:return t.File;case Xr.Reference:return t.Reference}return t.Property}function Qo(e){if(e)return{range:Ho(e.range),text:e.newText}}var Jo=function(){function e(e){this._worker=e}return Object.defineProperty(e.prototype,"triggerCharacters",{get:function(){return[" ",":"]},enumerable:!0,configurable:!0}),e.prototype.provideCompletionItems=function(e,t,r,n){var i=e.uri;return this._worker(i).then((function(e){return e.doComplete(i.toString(),qo(t))})).then((function(r){if(r){var n=e.getWordUntilPosition(t),i=new Uo(t.lineNumber,n.startColumn,t.lineNumber,n.endColumn),o=r.items.map((function(e){var t={label:e.label,insertText:e.insertText||e.label,sortText:e.sortText,filterText:e.filterText,documentation:e.documentation,detail:e.detail,range:i,kind:Go(e.kind)};return e.textEdit&&(t.range=Ho(e.textEdit.range),t.insertText=e.textEdit.newText),e.additionalTextEdits&&(t.additionalTextEdits=e.additionalTextEdits.map(Qo)),e.insertTextFormat===Zr.Snippet&&(t.insertTextRules=monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet),t}));return{isIncomplete:r.isIncomplete,suggestions:o}}}))},e}();function Xo(e){return e&&"object"===typeof e&&"string"===typeof e.kind}function Zo(e){return"string"===typeof e?{value:e}:Xo(e)?"plaintext"===e.kind?{value:e.value.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}:{value:e.value}:{value:"```"+e.language+"\n"+e.value+"\n```\n"}}function Yo(e){if(e)return Array.isArray(e)?e.map(Zo):[Zo(e)]}var es=function(){function e(e){this._worker=e}return e.prototype.provideHover=function(e,t,r){var n=e.uri;return this._worker(n).then((function(e){return e.doHover(n.toString(),qo(t))})).then((function(e){if(e)return{range:Ho(e.range),contents:Yo(e.contents)}}))},e}();function ts(e){switch(e){case an.Read:return monaco.languages.DocumentHighlightKind.Read;case an.Write:return monaco.languages.DocumentHighlightKind.Write;case an.Text:return monaco.languages.DocumentHighlightKind.Text}return monaco.languages.DocumentHighlightKind.Text}var rs=function(){function e(e){this._worker=e}return e.prototype.provideDocumentHighlights=function(e,t,r){var n=e.uri;return this._worker(n).then((function(e){return e.findDocumentHighlights(n.toString(),qo(t))})).then((function(e){if(e)return e.map((function(e){return{range:Ho(e.range),kind:ts(e.kind)}}))}))},e}();function ns(e){return{uri:Bo.parse(e.uri),range:Ho(e.range)}}var is=function(){function e(e){this._worker=e}return e.prototype.provideDefinition=function(e,t,r){var n=e.uri;return this._worker(n).then((function(e){return e.findDefinition(n.toString(),qo(t))})).then((function(e){if(e)return[ns(e)]}))},e}(),os=function(){function e(e){this._worker=e}return e.prototype.provideReferences=function(e,t,r,n){var i=e.uri;return this._worker(i).then((function(e){return e.findReferences(i.toString(),qo(t))})).then((function(e){if(e)return e.map(ns)}))},e}();function ss(e){if(e&&e.changes){var t=[];for(var r in e.changes)for(var n=Bo.parse(r),i=0,o=e.changes[r];i<o.length;i++){var s=o[i];t.push({resource:n,edit:{range:Ho(s.range),text:s.newText}})}return{edits:t}}}var as=function(){function e(e){this._worker=e}return e.prototype.provideRenameEdits=function(e,t,r,n){var i=e.uri;return this._worker(i).then((function(e){return e.doRename(i.toString(),qo(t),r)})).then((function(e){return ss(e)}))},e}();function cs(e){var t=monaco.languages.SymbolKind;switch(e){case un.File:return t.Array;case un.Module:return t.Module;case un.Namespace:return t.Namespace;case un.Package:return t.Package;case un.Class:return t.Class;case un.Method:return t.Method;case un.Property:return t.Property;case un.Field:return t.Field;case un.Constructor:return t.Constructor;case un.Enum:return t.Enum;case un.Interface:return t.Interface;case un.Function:return t.Function;case un.Variable:return t.Variable;case un.Constant:return t.Constant;case un.String:return t.String;case un.Number:return t.Number;case un.Boolean:return t.Boolean;case un.Array:return t.Array}return t.Function}var us=function(){function e(e){this._worker=e}return e.prototype.provideDocumentSymbols=function(e,t){var r=e.uri;return this._worker(r).then((function(e){return e.findDocumentSymbols(r.toString())})).then((function(e){if(e)return e.map((function(e){return{name:e.name,detail:"",containerName:e.containerName,kind:cs(e.kind),tags:[],range:Ho(e.location.range),selectionRange:Ho(e.location.range)}}))}))},e}(),hs=function(){function e(e){this._worker=e}return e.prototype.provideDocumentColors=function(e,t){var r=e.uri;return this._worker(r).then((function(e){return e.findDocumentColors(r.toString())})).then((function(e){if(e)return e.map((function(e){return{color:e.color,range:Ho(e.range)}}))}))},e.prototype.provideColorPresentations=function(e,t,r){var n=e.uri;return this._worker(n).then((function(e){return e.getColorPresentations(n.toString(),t.color,zo(t.range))})).then((function(e){if(e)return e.map((function(e){var t={label:e.label};return e.textEdit&&(t.textEdit=Qo(e.textEdit)),e.additionalTextEdits&&(t.additionalTextEdits=e.additionalTextEdits.map(Qo)),t}))}))},e}(),ls=function(){function e(e){this._worker=e}return e.prototype.provideFoldingRanges=function(e,t,r){var n=e.uri;return this._worker(n).then((function(e){return e.getFoldingRanges(n.toString(),t)})).then((function(e){if(e)return e.map((function(e){var t={start:e.startLine+1,end:e.endLine+1};return"undefined"!==typeof e.kind&&(t.kind=ps(e.kind)),t}))}))},e}();function ps(e){switch(e){case _r.Comment:return monaco.languages.FoldingRangeKind.Comment;case _r.Imports:return monaco.languages.FoldingRangeKind.Imports;case _r.Region:return monaco.languages.FoldingRangeKind.Region}}var fs=function(){function e(e){this._worker=e}return e.prototype.provideSelectionRanges=function(e,t,r){var n=e.uri;return this._worker(n).then((function(e){return e.getSelectionRanges(n.toString(),t.map(qo))})).then((function(e){if(e)return e.map((function(e){var t=[];while(e)t.push({range:Ho(e.range)}),e=e.parent;return t}))}))},e}();function ds(e){var t=[],r=[],n=new o(e);t.push(n);var i=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return n.getLanguageServiceWorker.apply(n,e)};function s(){var t=e.languageId,n=e.modeConfiguration;gs(r),n.completionItems&&r.push(monaco.languages.registerCompletionItemProvider(t,new Jo(i))),n.hovers&&r.push(monaco.languages.registerHoverProvider(t,new es(i))),n.documentHighlights&&r.push(monaco.languages.registerDocumentHighlightProvider(t,new rs(i))),n.definitions&&r.push(monaco.languages.registerDefinitionProvider(t,new is(i))),n.references&&r.push(monaco.languages.registerReferenceProvider(t,new os(i))),n.documentSymbols&&r.push(monaco.languages.registerDocumentSymbolProvider(t,new us(i))),n.rename&&r.push(monaco.languages.registerRenameProvider(t,new as(i))),n.colors&&r.push(monaco.languages.registerColorProvider(t,new hs(i))),n.foldingRanges&&r.push(monaco.languages.registerFoldingRangeProvider(t,new ls(i))),n.diagnostics&&r.push(new Wo(t,i,e)),n.selectionRanges&&r.push(monaco.languages.registerSelectionRangeProvider(t,new fs(i)))}return s(),t.push(ms(r)),ms(t)}function ms(e){return{dispose:function(){return gs(e)}}}function gs(e){while(e.length)e.pop().dispose()}},ed59:function(e,t,r){"use strict";(function(e){r.d(t,"a",(function(){return g}));var n,i,o=function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},e(t,r)};return function(t,r){function n(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();if("object"===typeof e)i="win32"===e.platform;else if("object"===typeof navigator){var s=navigator.userAgent;i=s.indexOf("Windows")>=0}var a=/^\w[\w\d+.-]*$/,c=/^\//,u=/^\/\//;function h(e,t){if(!e.scheme&&t)throw new Error('[UriError]: Scheme is missing: {scheme: "", authority: "'+e.authority+'", path: "'+e.path+'", query: "'+e.query+'", fragment: "'+e.fragment+'"}');if(e.scheme&&!a.test(e.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(e.path)if(e.authority){if(!c.test(e.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(u.test(e.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}function l(e,t){return e||t?e:"file"}function p(e,t){switch(e){case"https":case"http":case"file":t?t[0]!==d&&(t=d+t):t=d;break}return t}var f="",d="/",m=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/,g=function(){function e(e,t,r,n,i,o){void 0===o&&(o=!1),"object"===typeof e?(this.scheme=e.scheme||f,this.authority=e.authority||f,this.path=e.path||f,this.query=e.query||f,this.fragment=e.fragment||f):(this.scheme=l(e,o),this.authority=t||f,this.path=p(this.scheme,r||f),this.query=n||f,this.fragment=i||f,h(this,o))}return e.isUri=function(t){return t instanceof e||!!t&&("string"===typeof t.authority&&"string"===typeof t.fragment&&"string"===typeof t.path&&"string"===typeof t.query&&"string"===typeof t.scheme&&"function"===typeof t.fsPath&&"function"===typeof t.with&&"function"===typeof t.toString)},Object.defineProperty(e.prototype,"fsPath",{get:function(){return C(this)},enumerable:!0,configurable:!0}),e.prototype.with=function(e){if(!e)return this;var t=e.scheme,r=e.authority,n=e.path,i=e.query,o=e.fragment;return void 0===t?t=this.scheme:null===t&&(t=f),void 0===r?r=this.authority:null===r&&(r=f),void 0===n?n=this.path:null===n&&(n=f),void 0===i?i=this.query:null===i&&(i=f),void 0===o?o=this.fragment:null===o&&(o=f),t===this.scheme&&r===this.authority&&n===this.path&&i===this.query&&o===this.fragment?this:new v(t,r,n,i,o)},e.parse=function(e,t){void 0===t&&(t=!1);var r=m.exec(e);return r?new v(r[2]||f,decodeURIComponent(r[4]||f),decodeURIComponent(r[5]||f),decodeURIComponent(r[7]||f),decodeURIComponent(r[9]||f),t):new v(f,f,f,f,f)},e.file=function(e){var t=f;if(i&&(e=e.replace(/\\/g,d)),e[0]===d&&e[1]===d){var r=e.indexOf(d,2);-1===r?(t=e.substring(2),e=d):(t=e.substring(2,r),e=e.substring(r)||d)}return new v("file",t,e,f,f)},e.from=function(e){return new v(e.scheme,e.authority,e.path,e.query,e.fragment)},e.prototype.toString=function(e){return void 0===e&&(e=!1),_(this,e)},e.prototype.toJSON=function(){return this},e.revive=function(t){if(t){if(t instanceof e)return t;var r=new v(t);return r._formatted=t.external,r._fsPath=t._sep===y?t.fsPath:null,r}return t},e}(),y=i?1:void 0,v=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._formatted=null,t._fsPath=null,t}return o(t,e),Object.defineProperty(t.prototype,"fsPath",{get:function(){return this._fsPath||(this._fsPath=C(this)),this._fsPath},enumerable:!0,configurable:!0}),t.prototype.toString=function(e){return void 0===e&&(e=!1),e?_(this,!0):(this._formatted||(this._formatted=_(this,!1)),this._formatted)},t.prototype.toJSON=function(){var e={$mid:1};return this._fsPath&&(e.fsPath=this._fsPath,e._sep=y),this._formatted&&(e.external=this._formatted),this.path&&(e.path=this.path),this.scheme&&(e.scheme=this.scheme),this.authority&&(e.authority=this.authority),this.query&&(e.query=this.query),this.fragment&&(e.fragment=this.fragment),e},t}(g),b=(n={},n[58]="%3A",n[47]="%2F",n[63]="%3F",n[35]="%23",n[91]="%5B",n[93]="%5D",n[64]="%40",n[33]="%21",n[36]="%24",n[38]="%26",n[39]="%27",n[40]="%28",n[41]="%29",n[42]="%2A",n[43]="%2B",n[44]="%2C",n[59]="%3B",n[61]="%3D",n[32]="%20",n);function k(e,t){for(var r=void 0,n=-1,i=0;i<e.length;i++){var o=e.charCodeAt(i);if(o>=97&&o<=122||o>=65&&o<=90||o>=48&&o<=57||45===o||46===o||95===o||126===o||t&&47===o)-1!==n&&(r+=encodeURIComponent(e.substring(n,i)),n=-1),void 0!==r&&(r+=e.charAt(i));else{void 0===r&&(r=e.substr(0,i));var s=b[o];void 0!==s?(-1!==n&&(r+=encodeURIComponent(e.substring(n,i)),n=-1),r+=s):-1===n&&(n=i)}}return-1!==n&&(r+=encodeURIComponent(e.substring(n))),void 0!==r?r:e}function x(e){for(var t=void 0,r=0;r<e.length;r++){var n=e.charCodeAt(r);35===n||63===n?(void 0===t&&(t=e.substr(0,r)),t+=b[n]):void 0!==t&&(t+=e[r])}return void 0!==t?t:e}function C(e){var t;return t=e.authority&&e.path.length>1&&"file"===e.scheme?"//"+e.authority+e.path:47===e.path.charCodeAt(0)&&(e.path.charCodeAt(1)>=65&&e.path.charCodeAt(1)<=90||e.path.charCodeAt(1)>=97&&e.path.charCodeAt(1)<=122)&&58===e.path.charCodeAt(2)?e.path[1].toLowerCase()+e.path.substr(2):e.path,i&&(t=t.replace(/\//g,"\\")),t}function _(e,t){var r=t?x:k,n="",i=e.scheme,o=e.authority,s=e.path,a=e.query,c=e.fragment;if(i&&(n+=i,n+=":"),(o||"file"===i)&&(n+=d,n+=d),o){var u=o.indexOf("@");if(-1!==u){var h=o.substr(0,u);o=o.substr(u+1),u=h.indexOf(":"),-1===u?n+=r(h,!1):(n+=r(h.substr(0,u),!1),n+=":",n+=r(h.substr(u+1),!1)),n+="@"}o=o.toLowerCase(),u=o.indexOf(":"),-1===u?n+=r(o,!1):(n+=r(o.substr(0,u),!1),n+=o.substr(u))}if(s){if(s.length>=3&&47===s.charCodeAt(0)&&58===s.charCodeAt(2)){var l=s.charCodeAt(1);l>=65&&l<=90&&(s="/"+String.fromCharCode(l+32)+":"+s.substr(3))}else if(s.length>=2&&58===s.charCodeAt(1)){l=s.charCodeAt(0);l>=65&&l<=90&&(s=String.fromCharCode(l+32)+":"+s.substr(2))}n+=r(s,!0)}return a&&(n+="?",n+=r(a,!1)),c&&(n+="#",n+=t?c:k(c,!1)),n}}).call(this,r("4362"))}}]);