(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0d831d6a"],{"0676":function(t,r,n){function e(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}n("d9e2"),t.exports=e,t.exports.__esModule=!0,t.exports["default"]=t.exports},"11b0":function(t,r,n){function e(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}n("a4d3"),n("e01a"),n("d3b7"),n("d28b"),n("3ca3"),n("ddb0"),n("a630"),t.exports=e,t.exports.__esModule=!0,t.exports["default"]=t.exports},"1ab9":function(t,r,n){var e=n("4f4d"),o=n("2514"),i=Object.hasOwnProperty,a=Object.create(null);for(var u in e)i.call(e,u)&&(a[e[u]]=u);var c=t.exports={to:{},get:{}};function s(t,r,n){return Math.min(Math.max(r,t),n)}function l(t){var r=Math.round(t).toString(16).toUpperCase();return r.length<2?"0"+r:r}c.get=function(t){var r,n,e=t.substring(0,3).toLowerCase();switch(e){case"hsl":r=c.get.hsl(t),n="hsl";break;case"hwb":r=c.get.hwb(t),n="hwb";break;default:r=c.get.rgb(t),n="rgb";break}return r?{model:n,value:r}:null},c.get.rgb=function(t){if(!t)return null;var r,n,o,a=/^#([a-f0-9]{3,4})$/i,u=/^#([a-f0-9]{6})([a-f0-9]{2})?$/i,c=/^rgba?\(\s*([+-]?\d+)(?=[\s,])\s*(?:,\s*)?([+-]?\d+)(?=[\s,])\s*(?:,\s*)?([+-]?\d+)\s*(?:[,|\/]\s*([+-]?[\d\.]+)(%?)\s*)?\)$/,l=/^rgba?\(\s*([+-]?[\d\.]+)\%\s*,?\s*([+-]?[\d\.]+)\%\s*,?\s*([+-]?[\d\.]+)\%\s*(?:[,|\/]\s*([+-]?[\d\.]+)(%?)\s*)?\)$/,f=/^(\w+)$/,h=[0,0,0,1];if(r=t.match(u)){for(o=r[2],r=r[1],n=0;n<3;n++){var p=2*n;h[n]=parseInt(r.slice(p,p+2),16)}o&&(h[3]=parseInt(o,16)/255)}else if(r=t.match(a)){for(r=r[1],o=r[3],n=0;n<3;n++)h[n]=parseInt(r[n]+r[n],16);o&&(h[3]=parseInt(o+o,16)/255)}else if(r=t.match(c)){for(n=0;n<3;n++)h[n]=parseInt(r[n+1],0);r[4]&&(r[5]?h[3]=.01*parseFloat(r[4]):h[3]=parseFloat(r[4]))}else{if(!(r=t.match(l)))return(r=t.match(f))?"transparent"===r[1]?[0,0,0,0]:i.call(e,r[1])?(h=e[r[1]],h[3]=1,h):null:null;for(n=0;n<3;n++)h[n]=Math.round(2.55*parseFloat(r[n+1]));r[4]&&(r[5]?h[3]=.01*parseFloat(r[4]):h[3]=parseFloat(r[4]))}for(n=0;n<3;n++)h[n]=s(h[n],0,255);return h[3]=s(h[3],0,1),h},c.get.hsl=function(t){if(!t)return null;var r=/^hsla?\(\s*([+-]?(?:\d{0,3}\.)?\d+)(?:deg)?\s*,?\s*([+-]?[\d\.]+)%\s*,?\s*([+-]?[\d\.]+)%\s*(?:[,|\/]\s*([+-]?(?=\.\d|\d)(?:0|[1-9]\d*)?(?:\.\d*)?(?:[eE][+-]?\d+)?)\s*)?\)$/,n=t.match(r);if(n){var e=parseFloat(n[4]),o=(parseFloat(n[1])%360+360)%360,i=s(parseFloat(n[2]),0,100),a=s(parseFloat(n[3]),0,100),u=s(isNaN(e)?1:e,0,1);return[o,i,a,u]}return null},c.get.hwb=function(t){if(!t)return null;var r=/^hwb\(\s*([+-]?\d{0,3}(?:\.\d+)?)(?:deg)?\s*,\s*([+-]?[\d\.]+)%\s*,\s*([+-]?[\d\.]+)%\s*(?:,\s*([+-]?(?=\.\d|\d)(?:0|[1-9]\d*)?(?:\.\d*)?(?:[eE][+-]?\d+)?)\s*)?\)$/,n=t.match(r);if(n){var e=parseFloat(n[4]),o=(parseFloat(n[1])%360+360)%360,i=s(parseFloat(n[2]),0,100),a=s(parseFloat(n[3]),0,100),u=s(isNaN(e)?1:e,0,1);return[o,i,a,u]}return null},c.to.hex=function(){var t=o(arguments);return"#"+l(t[0])+l(t[1])+l(t[2])+(t[3]<1?l(Math.round(255*t[3])):"")},c.to.rgb=function(){var t=o(arguments);return t.length<4||1===t[3]?"rgb("+Math.round(t[0])+", "+Math.round(t[1])+", "+Math.round(t[2])+")":"rgba("+Math.round(t[0])+", "+Math.round(t[1])+", "+Math.round(t[2])+", "+t[3]+")"},c.to.rgb.percent=function(){var t=o(arguments),r=Math.round(t[0]/255*100),n=Math.round(t[1]/255*100),e=Math.round(t[2]/255*100);return t.length<4||1===t[3]?"rgb("+r+"%, "+n+"%, "+e+"%)":"rgba("+r+"%, "+n+"%, "+e+"%, "+t[3]+")"},c.to.hsl=function(){var t=o(arguments);return t.length<4||1===t[3]?"hsl("+t[0]+", "+t[1]+"%, "+t[2]+"%)":"hsla("+t[0]+", "+t[1]+"%, "+t[2]+"%, "+t[3]+")"},c.to.hwb=function(){var t=o(arguments),r="";return t.length>=4&&1!==t[3]&&(r=", "+t[3]),"hwb("+t[0]+", "+t[1]+"%, "+t[2]+"%"+r+")"},c.to.keyword=function(t){return a[t.slice(0,3)]}},2236:function(t,r,n){var e=n("5a43");function o(t){if(Array.isArray(t))return e(t)}t.exports=o,t.exports.__esModule=!0,t.exports["default"]=t.exports},"278c":function(t,r,n){var e=n("c135"),o=n("9b42"),i=n("6613"),a=n("c240");function u(t,r){return e(t)||o(t,r)||i(t,r)||a()}t.exports=u,t.exports.__esModule=!0,t.exports["default"]=t.exports},"36c6":function(t,r,n){function e(r){return t.exports=e=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},t.exports.__esModule=!0,t.exports["default"]=t.exports,e(r)}n("3410"),t.exports=e,t.exports.__esModule=!0,t.exports["default"]=t.exports},"3c96":function(t,r,n){function e(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}n("d9e2"),t.exports=e,t.exports.__esModule=!0,t.exports["default"]=t.exports},"448a":function(t,r,n){var e=n("2236"),o=n("11b0"),i=n("6613"),a=n("0676");function u(t){return e(t)||o(t)||i(t)||a()}t.exports=u,t.exports.__esModule=!0,t.exports["default"]=t.exports},"4a4b":function(t,r){function n(r,e){return t.exports=n=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,r){return t.__proto__=r,t},t.exports.__esModule=!0,t.exports["default"]=t.exports,n(r,e)}t.exports=n,t.exports.__esModule=!0,t.exports["default"]=t.exports},"4f4d":function(t,r,n){"use strict";t.exports={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]}},"53ec":function(t,r,n){function e(t,r,n,e,o){var i={};return Object.keys(e).forEach((function(t){i[t]=e[t]})),i.enumerable=!!i.enumerable,i.configurable=!!i.configurable,("value"in i||i.initializer)&&(i.writable=!0),i=n.slice().reverse().reduce((function(n,e){return e(t,r,n)||n}),i),o&&void 0!==i.initializer&&(i.value=i.initializer?i.initializer.call(o):void 0,i.initializer=void 0),void 0===i.initializer&&(Object.defineProperty(t,r,i),i=null),i}n("d3b7"),n("159b"),n("b64b"),n("fb6a"),t.exports=e,t.exports.__esModule=!0,t.exports["default"]=t.exports},"5a43":function(t,r){function n(t,r){(null==r||r>t.length)&&(r=t.length);for(var n=0,e=new Array(r);n<r;n++)e[n]=t[n];return e}t.exports=n,t.exports.__esModule=!0,t.exports["default"]=t.exports},"5bc3":function(t,r){function n(t,r){for(var n=0;n<r.length;n++){var e=r[n];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(t,e.key,e)}}function e(t,r,e){return r&&n(t.prototype,r),e&&n(t,e),Object.defineProperty(t,"prototype",{writable:!1}),t}t.exports=e,t.exports.__esModule=!0,t.exports["default"]=t.exports},"5e45":function(t,r,n){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),function(t){t["TextureCompressionBC"]="texture-compression-bc"}(r.ExtensionName||(r.ExtensionName={})),function(t){t["ClampToEdge"]="clamp-to-edge",t["Repeat"]="repeat",t["MirrorRepeat"]="mirror-repeat"}(r.AddressMode||(r.AddressMode={})),function(t){t["UniformBuffer"]="uniform-buffer",t["StorageBuffer"]="storage-buffer",t["ReadonlyStorageBuffer"]="readonly-storage-buffer",t["Sampler"]="sampler",t["ComparisonSampler"]="comparison-sampler",t["SampledTexture"]="sampled-texture",t["ReadonlyStorageTexture"]="readonly-storage-texture",t["WriteonlyStorageTexture"]="writeonly-storage-texture"}(r.BindingType||(r.BindingType={})),function(t){t["Zero"]="zero",t["One"]="one",t["SrcColor"]="src-color",t["OneMinusSrcColor"]="one-minus-src-color",t["SrcAlpha"]="src-alpha",t["OneMinusSrcAlpha"]="one-minus-src-alpha",t["DstColor"]="dst-color",t["OneMinusDstColor"]="one-minus-dst-color",t["DstAlpha"]="dst-alpha",t["OneMinusDstAlpha"]="one-minus-dst-alpha",t["SrcAlphaSaturated"]="src-alpha-saturated",t["BlendColor"]="blend-color",t["OneMinusBlendColor"]="one-minus-blend-color"}(r.BlendFactor||(r.BlendFactor={})),function(t){t["Add"]="add",t["Subtract"]="subtract",t["ReverseSubtract"]="reverse-subtract",t["Min"]="min",t["Max"]="max"}(r.BlendOperation||(r.BlendOperation={})),function(t){t["Never"]="never",t["Less"]="less",t["Equal"]="equal",t["LessEqual"]="less-equal",t["Greater"]="greater",t["NotEqual"]="not-equal",t["GreaterEqual"]="greater-equal",t["Always"]="always"}(r.CompareFunction||(r.CompareFunction={})),function(t){t["None"]="none",t["Front"]="front",t["Back"]="back"}(r.CullMode||(r.CullMode={})),function(t){t["Nearest"]="nearest",t["Linear"]="linear"}(r.FilterMode||(r.FilterMode={})),function(t){t["CCW"]="ccw",t["CW"]="cw"}(r.FrontFace||(r.FrontFace={})),function(t){t["Uint16"]="uint16",t["Uint32"]="uint32"}(r.IndexFormat||(r.IndexFormat={})),function(t){t["Vertex"]="vertex",t["Instance"]="instance"}(r.InputStepMode||(r.InputStepMode={})),function(t){t["Load"]="load"}(r.LoadOp||(r.LoadOp={})),function(t){t["PointList"]="point-list",t["LineList"]="line-list",t["LineStrip"]="line-strip",t["TriangleList"]="triangle-list",t["TriangleStrip"]="triangle-strip"}(r.PrimitiveTopology||(r.PrimitiveTopology={})),function(t){t["Keep"]="keep",t["Zero"]="zero",t["Replace"]="replace",t["Invert"]="invert",t["IncrementClamp"]="increment-clamp",t["DecrementClamp"]="decrement-clamp",t["IncrementWrap"]="increment-wrap",t["DecrementWrap"]="decrement-wrap"}(r.StencilOperation||(r.StencilOperation={})),function(t){t["Store"]="store",t["Clear"]="clear"}(r.StoreOp||(r.StoreOp={})),function(t){t["E1d"]="1d",t["E2d"]="2d",t["E3d"]="3d"}(r.TextureDimension||(r.TextureDimension={})),function(t){t["R8Unorm"]="r8unorm",t["R8Snorm"]="r8snorm",t["R8Uint"]="r8uint",t["R8Sint"]="r8sint",t["R16Uint"]="r16uint",t["R16Sint"]="r16sint",t["R16Float"]="r16float",t["RG8Unorm"]="rg8unorm",t["RG8Snorm"]="rg8snorm",t["RG8Uint"]="rg8uint",t["RG8Sint"]="rg8sint",t["R32Uint"]="r32uint",t["R32Sint"]="r32sint",t["R32Float"]="r32float",t["RG16Uint"]="rg16uint",t["RG16Sint"]="rg16sint",t["RG16Float"]="rg16float",t["RGBA8Unorm"]="rgba8unorm",t["RGBA8UnormSRGB"]="rgba8unorm-srgb",t["RGBA8Snorm"]="rgba8snorm",t["RGBA8Uint"]="rgba8uint",t["RGBA8Sint"]="rgba8sint",t["BGRA8Unorm"]="bgra8unorm",t["BGRA8UnormSRGB"]="bgra8unorm-srgb",t["RGB10A2Unorm"]="rgb10a2unorm",t["RG11B10Float"]="rg11b10float",t["RG32Uint"]="rg32uint",t["RG32Sint"]="rg32sint",t["RG32Float"]="rg32float",t["RGBA16Uint"]="rgba16uint",t["RGBA16Sint"]="rgba16sint",t["RGBA16Float"]="rgba16float",t["RGBA32Uint"]="rgba32uint",t["RGBA32Sint"]="rgba32sint",t["RGBA32Float"]="rgba32float",t["Depth32Float"]="depth32float",t["Depth24Plus"]="depth24plus",t["Depth24PlusStencil8"]="depth24plus-stencil8"}(r.TextureFormat||(r.TextureFormat={})),function(t){t["Float"]="float",t["Sint"]="sint",t["Uint"]="uint"}(r.TextureComponentType||(r.TextureComponentType={})),function(t){t["E1d"]="1d",t["E2d"]="2d",t["E2dArray"]="2d-array",t["Cube"]="cube",t["CubeArray"]="cube-array",t["E3d"]="3d"}(r.TextureViewDimension||(r.TextureViewDimension={})),function(t){t["Uchar2"]="uchar2",t["Uchar4"]="uchar4",t["Char2"]="char2",t["Char4"]="char4",t["Uchar2Norm"]="uchar2norm",t["Uchar4Norm"]="uchar4norm",t["Char2Norm"]="char2norm",t["Char4Norm"]="char4norm",t["Ushort2"]="ushort2",t["Ushort4"]="ushort4",t["Short2"]="short2",t["Short4"]="short4",t["Ushort2Norm"]="ushort2norm",t["Ushort4Norm"]="ushort4norm",t["Short2Norm"]="short2norm",t["Short4Norm"]="short4norm",t["Half2"]="half2",t["Half4"]="half4",t["Float"]="float",t["Float2"]="float2",t["Float3"]="float3",t["Float4"]="float4",t["Uint"]="uint",t["Uint2"]="uint2",t["Uint3"]="uint3",t["Uint4"]="uint4",t["Int"]="int",t["Int2"]="int2",t["Int3"]="int3",t["Int4"]="int4"}(r.VertexFormat||(r.VertexFormat={})),function(t){t["All"]="all",t["StencilOnly"]="stencil-only",t["DepthOnly"]="depth-only"}(r.TextureAspect||(r.TextureAspect={})),function(t){t["Error"]="error",t["Warning"]="warning",t["Info"]="info"}(r.CompilationMessageType||(r.CompilationMessageType={})),function(t){t["Occlusion"]="occlusion"}(r.QueryType||(r.QueryType={})),function(t){t[t["MapRead"]=1]="MapRead",t[t["MapWrite"]=2]="MapWrite",t[t["CopySrc"]=4]="CopySrc",t[t["CopyDst"]=8]="CopyDst",t[t["Index"]=16]="Index",t[t["Vertex"]=32]="Vertex",t[t["Uniform"]=64]="Uniform",t[t["Storage"]=128]="Storage",t[t["Indirect"]=256]="Indirect",t[t["QueryResolve"]=512]="QueryResolve"}(r.BufferUsage||(r.BufferUsage={})),function(t){t[t["Red"]=1]="Red",t[t["Green"]=2]="Green",t[t["Blue"]=4]="Blue",t[t["Alpha"]=8]="Alpha",t[t["All"]=15]="All"}(r.ColorWrite||(r.ColorWrite={})),function(t){t[t["Vertex"]=1]="Vertex",t[t["Fragment"]=2]="Fragment",t[t["Compute"]=4]="Compute"}(r.ShaderStage||(r.ShaderStage={})),function(t){t[t["CopySrc"]=1]="CopySrc",t[t["CopyDst"]=2]="CopyDst",t[t["Sampled"]=4]="Sampled",t[t["Storage"]=8]="Storage",t[t["OutputAttachment"]=16]="OutputAttachment"}(r.TextureUsage||(r.TextureUsage={})),function(t){t[t["Read"]=1]="Read",t[t["Write"]=2]="Write"}(r.MapMode||(r.MapMode={}))},6613:function(t,r,n){n("fb6a"),n("d3b7"),n("b0c0"),n("a630"),n("3ca3"),n("ac1f"),n("00b4");var e=n("5a43");function o(t,r){if(t){if("string"===typeof t)return e(t,r);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?e(t,r):void 0}}t.exports=o,t.exports.__esModule=!0,t.exports["default"]=t.exports},6929:function(t,r,n){"use strict";var e=n("1ab9"),o=n("bb15"),i=[].slice,a=["keyword","gray","hex"],u={};Object.keys(o).forEach((function(t){u[i.call(o[t].labels).sort().join("")]=t}));var c={};function s(t,r){if(!(this instanceof s))return new s(t,r);if(r&&r in a&&(r=null),r&&!(r in o))throw new Error("Unknown model: "+r);var n,l;if(null==t)this.model="rgb",this.color=[0,0,0],this.valpha=1;else if(t instanceof s)this.model=t.model,this.color=t.color.slice(),this.valpha=t.valpha;else if("string"===typeof t){var f=e.get(t);if(null===f)throw new Error("Unable to parse color from string: "+t);this.model=f.model,l=o[this.model].channels,this.color=f.value.slice(0,l),this.valpha="number"===typeof f.value[l]?f.value[l]:1}else if(t.length){this.model=r||"rgb",l=o[this.model].channels;var h=i.call(t,0,l);this.color=v(h,l),this.valpha="number"===typeof t[l]?t[l]:1}else if("number"===typeof t)t&=16777215,this.model="rgb",this.color=[t>>16&255,t>>8&255,255&t],this.valpha=1;else{this.valpha=1;var p=Object.keys(t);"alpha"in t&&(p.splice(p.indexOf("alpha"),1),this.valpha="number"===typeof t.alpha?t.alpha:0);var d=p.sort().join("");if(!(d in u))throw new Error("Unable to parse color from object: "+JSON.stringify(t));this.model=u[d];var m=o[this.model].labels,g=[];for(n=0;n<m.length;n++)g.push(t[m[n]]);this.color=v(g)}if(c[this.model])for(l=o[this.model].channels,n=0;n<l;n++){var b=c[this.model][n];b&&(this.color[n]=b(this.color[n]))}this.valpha=Math.max(0,Math.min(1,this.valpha)),Object.freeze&&Object.freeze(this)}function l(t,r){return Number(t.toFixed(r))}function f(t){return function(r){return l(r,t)}}function h(t,r,n){return t=Array.isArray(t)?t:[t],t.forEach((function(t){(c[t]||(c[t]=[]))[r]=n})),t=t[0],function(e){var o;return arguments.length?(n&&(e=n(e)),o=this[t](),o.color[r]=e,o):(o=this[t]().color[r],n&&(o=n(o)),o)}}function p(t){return function(r){return Math.max(0,Math.min(t,r))}}function d(t){return Array.isArray(t)?t:[t]}function v(t,r){for(var n=0;n<r;n++)"number"!==typeof t[n]&&(t[n]=0);return t}s.prototype={toString:function(){return this.string()},toJSON:function(){return this[this.model]()},string:function(t){var r=this.model in e.to?this:this.rgb();r=r.round("number"===typeof t?t:1);var n=1===r.valpha?r.color:r.color.concat(this.valpha);return e.to[r.model](n)},percentString:function(t){var r=this.rgb().round("number"===typeof t?t:1),n=1===r.valpha?r.color:r.color.concat(this.valpha);return e.to.rgb.percent(n)},array:function(){return 1===this.valpha?this.color.slice():this.color.concat(this.valpha)},object:function(){for(var t={},r=o[this.model].channels,n=o[this.model].labels,e=0;e<r;e++)t[n[e]]=this.color[e];return 1!==this.valpha&&(t.alpha=this.valpha),t},unitArray:function(){var t=this.rgb().color;return t[0]/=255,t[1]/=255,t[2]/=255,1!==this.valpha&&t.push(this.valpha),t},unitObject:function(){var t=this.rgb().object();return t.r/=255,t.g/=255,t.b/=255,1!==this.valpha&&(t.alpha=this.valpha),t},round:function(t){return t=Math.max(t||0,0),new s(this.color.map(f(t)).concat(this.valpha),this.model)},alpha:function(t){return arguments.length?new s(this.color.concat(Math.max(0,Math.min(1,t))),this.model):this.valpha},red:h("rgb",0,p(255)),green:h("rgb",1,p(255)),blue:h("rgb",2,p(255)),hue:h(["hsl","hsv","hsl","hwb","hcg"],0,(function(t){return(t%360+360)%360})),saturationl:h("hsl",1,p(100)),lightness:h("hsl",2,p(100)),saturationv:h("hsv",1,p(100)),value:h("hsv",2,p(100)),chroma:h("hcg",1,p(100)),gray:h("hcg",2,p(100)),white:h("hwb",1,p(100)),wblack:h("hwb",2,p(100)),cyan:h("cmyk",0,p(100)),magenta:h("cmyk",1,p(100)),yellow:h("cmyk",2,p(100)),black:h("cmyk",3,p(100)),x:h("xyz",0,p(100)),y:h("xyz",1,p(100)),z:h("xyz",2,p(100)),l:h("lab",0,p(100)),a:h("lab",1),b:h("lab",2),keyword:function(t){return arguments.length?new s(t):o[this.model].keyword(this.color)},hex:function(t){return arguments.length?new s(t):e.to.hex(this.rgb().round().color)},rgbNumber:function(){var t=this.rgb().color;return(255&t[0])<<16|(255&t[1])<<8|255&t[2]},luminosity:function(){for(var t=this.rgb().color,r=[],n=0;n<t.length;n++){var e=t[n]/255;r[n]=e<=.03928?e/12.92:Math.pow((e+.055)/1.055,2.4)}return.2126*r[0]+.7152*r[1]+.0722*r[2]},contrast:function(t){var r=this.luminosity(),n=t.luminosity();return r>n?(r+.05)/(n+.05):(n+.05)/(r+.05)},level:function(t){var r=this.contrast(t);return r>=7.1?"AAA":r>=4.5?"AA":""},isDark:function(){var t=this.rgb().color,r=(299*t[0]+587*t[1]+114*t[2])/1e3;return r<128},isLight:function(){return!this.isDark()},negate:function(){for(var t=this.rgb(),r=0;r<3;r++)t.color[r]=255-t.color[r];return t},lighten:function(t){var r=this.hsl();return r.color[2]+=r.color[2]*t,r},darken:function(t){var r=this.hsl();return r.color[2]-=r.color[2]*t,r},saturate:function(t){var r=this.hsl();return r.color[1]+=r.color[1]*t,r},desaturate:function(t){var r=this.hsl();return r.color[1]-=r.color[1]*t,r},whiten:function(t){var r=this.hwb();return r.color[1]+=r.color[1]*t,r},blacken:function(t){var r=this.hwb();return r.color[2]+=r.color[2]*t,r},grayscale:function(){var t=this.rgb().color,r=.3*t[0]+.59*t[1]+.11*t[2];return s.rgb(r,r,r)},fade:function(t){return this.alpha(this.valpha-this.valpha*t)},opaquer:function(t){return this.alpha(this.valpha+this.valpha*t)},rotate:function(t){var r=this.hsl(),n=r.color[0];return n=(n+t)%360,n=n<0?360+n:n,r.color[0]=n,r},mix:function(t,r){if(!t||!t.rgb)throw new Error('Argument to "mix" was not a Color instance, but rather an instance of '+typeof t);var n=t.rgb(),e=this.rgb(),o=void 0===r?.5:r,i=2*o-1,a=n.alpha()-e.alpha(),u=((i*a===-1?i:(i+a)/(1+i*a))+1)/2,c=1-u;return s.rgb(u*n.red()+c*e.red(),u*n.green()+c*e.green(),u*n.blue()+c*e.blue(),n.alpha()*o+e.alpha()*(1-o))}},Object.keys(o).forEach((function(t){if(-1===a.indexOf(t)){var r=o[t].channels;s.prototype[t]=function(){if(this.model===t)return new s(this);if(arguments.length)return new s(arguments,t);var n="number"===typeof arguments[r]?r:this.valpha;return new s(d(o[this.model][t].raw(this.color)).concat(n),t)},s[t]=function(n){return"number"===typeof n&&(n=v(i.call(arguments),r)),new s(n,t)}}})),t.exports=s},"6b58":function(t,r,n){n("d9e2");var e=n("7037")["default"],o=n("3c96");function i(t,r){if(r&&("object"===e(r)||"function"===typeof r))return r;if(void 0!==r)throw new TypeError("Derived constructors may only return object or undefined");return o(t)}t.exports=i,t.exports.__esModule=!0,t.exports["default"]=t.exports},7037:function(t,r,n){function e(r){return t.exports=e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t.exports.__esModule=!0,t.exports["default"]=t.exports,e(r)}n("a4d3"),n("e01a"),n("d3b7"),n("d28b"),n("3ca3"),n("ddb0"),t.exports=e,t.exports.__esModule=!0,t.exports["default"]=t.exports},"7ec2":function(t,r,n){n("a4d3"),n("e01a"),n("d3b7"),n("d28b"),n("3ca3"),n("ddb0"),n("b636"),n("944a"),n("0c47"),n("23dc"),n("d9e2"),n("3410"),n("159b"),n("b0c0"),n("fb6a");var e=n("7037")["default"];function o(){"use strict";
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */t.exports=o=function(){return r},t.exports.__esModule=!0,t.exports["default"]=t.exports;var r={},n=Object.prototype,i=n.hasOwnProperty,a="function"==typeof Symbol?Symbol:{},u=a.iterator||"@@iterator",c=a.asyncIterator||"@@asyncIterator",s=a.toStringTag||"@@toStringTag";function l(t,r,n){return Object.defineProperty(t,r,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[r]}try{l({},"")}catch(C){l=function(t,r,n){return t[r]=n}}function f(t,r,n,e){var o=r&&r.prototype instanceof d?r:d,i=Object.create(o.prototype),a=new _(e||[]);return i._invoke=function(t,r,n){var e="suspendedStart";return function(o,i){if("executing"===e)throw new Error("Generator is already running");if("completed"===e){if("throw"===o)throw i;return N()}for(n.method=o,n.arg=i;;){var a=n.delegate;if(a){var u=O(a,n);if(u){if(u===p)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===e)throw e="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);e="executing";var c=h(t,r,n);if("normal"===c.type){if(e=n.done?"completed":"suspendedYield",c.arg===p)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(e="completed",n.method="throw",n.arg=c.arg)}}}(t,n,a),i}function h(t,r,n){try{return{type:"normal",arg:t.call(r,n)}}catch(C){return{type:"throw",arg:C}}}r.wrap=f;var p={};function d(){}function v(){}function m(){}var g={};l(g,u,(function(){return this}));var b=Object.getPrototypeOf,y=b&&b(b(S([])));y&&y!==n&&i.call(y,u)&&(g=y);var x=m.prototype=d.prototype=Object.create(g);function w(t){["next","throw","return"].forEach((function(r){l(t,r,(function(t){return this._invoke(r,t)}))}))}function M(t,r){function n(o,a,u,c){var s=h(t[o],t,a);if("throw"!==s.type){var l=s.arg,f=l.value;return f&&"object"==e(f)&&i.call(f,"__await")?r.resolve(f.__await).then((function(t){n("next",t,u,c)}),(function(t){n("throw",t,u,c)})):r.resolve(f).then((function(t){l.value=t,u(l)}),(function(t){return n("throw",t,u,c)}))}c(s.arg)}var o;this._invoke=function(t,e){function i(){return new r((function(r,o){n(t,e,r,o)}))}return o=o?o.then(i,i):i()}}function O(t,r){var n=t.iterator[r.method];if(void 0===n){if(r.delegate=null,"throw"===r.method){if(t.iterator["return"]&&(r.method="return",r.arg=void 0,O(t,r),"throw"===r.method))return p;r.method="throw",r.arg=new TypeError("The iterator does not provide a 'throw' method")}return p}var e=h(n,t.iterator,r.arg);if("throw"===e.type)return r.method="throw",r.arg=e.arg,r.delegate=null,p;var o=e.arg;return o?o.done?(r[t.resultName]=o.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=void 0),r.delegate=null,p):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,p)}function k(t){var r={tryLoc:t[0]};1 in t&&(r.catchLoc=t[1]),2 in t&&(r.finallyLoc=t[2],r.afterLoc=t[3]),this.tryEntries.push(r)}function j(t){var r=t.completion||{};r.type="normal",delete r.arg,t.completion=r}function _(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(k,this),this.reset(!0)}function S(t){if(t){var r=t[u];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,e=function r(){for(;++n<t.length;)if(i.call(t,n))return r.value=t[n],r.done=!1,r;return r.value=void 0,r.done=!0,r};return e.next=e}}return{next:N}}function N(){return{value:void 0,done:!0}}return v.prototype=m,l(x,"constructor",m),l(m,"constructor",v),v.displayName=l(m,s,"GeneratorFunction"),r.isGeneratorFunction=function(t){var r="function"==typeof t&&t.constructor;return!!r&&(r===v||"GeneratorFunction"===(r.displayName||r.name))},r.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,l(t,s,"GeneratorFunction")),t.prototype=Object.create(x),t},r.awrap=function(t){return{__await:t}},w(M.prototype),l(M.prototype,c,(function(){return this})),r.AsyncIterator=M,r.async=function(t,n,e,o,i){void 0===i&&(i=Promise);var a=new M(f(t,n,e,o),i);return r.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},w(x),l(x,s,"Generator"),l(x,u,(function(){return this})),l(x,"toString",(function(){return"[object Generator]"})),r.keys=function(t){var r=[];for(var n in t)r.push(n);return r.reverse(),function n(){for(;r.length;){var e=r.pop();if(e in t)return n.value=e,n.done=!1,n}return n.done=!0,n}},r.values=S,_.prototype={constructor:_,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(j),!t)for(var r in this)"t"===r.charAt(0)&&i.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function n(n,e){return a.type="throw",a.arg=t,r.next=n,e&&(r.method="next",r.arg=void 0),!!e}for(var e=this.tryEntries.length-1;e>=0;--e){var o=this.tryEntries[e],a=o.completion;if("root"===o.tryLoc)return n("end");if(o.tryLoc<=this.prev){var u=i.call(o,"catchLoc"),c=i.call(o,"finallyLoc");if(u&&c){if(this.prev<o.catchLoc)return n(o.catchLoc,!0);if(this.prev<o.finallyLoc)return n(o.finallyLoc)}else if(u){if(this.prev<o.catchLoc)return n(o.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return n(o.finallyLoc)}}}},abrupt:function(t,r){for(var n=this.tryEntries.length-1;n>=0;--n){var e=this.tryEntries[n];if(e.tryLoc<=this.prev&&i.call(e,"finallyLoc")&&this.prev<e.finallyLoc){var o=e;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=r&&r<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=r,o?(this.method="next",this.next=o.finallyLoc,p):this.complete(a)},complete:function(t,r){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&r&&(this.next=r),p},finish:function(t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),j(n),p}},catch:function(t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===t){var e=n.completion;if("throw"===e.type){var o=e.arg;j(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:S(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=void 0),p}},r}t.exports=o,t.exports.__esModule=!0,t.exports["default"]=t.exports},8937:function(t,r,n){"use strict";n.r(r),n.d(r,"contains",(function(){return a})),n.d(r,"includes",(function(){return a})),n.d(r,"difference",(function(){return l})),n.d(r,"find",(function(){return A})),n.d(r,"findIndex",(function(){return E})),n.d(r,"firstValue",(function(){return I})),n.d(r,"flatten",(function(){return R})),n.d(r,"flattenDeep",(function(){return D})),n.d(r,"getRange",(function(){return G})),n.d(r,"pull",(function(){return V})),n.d(r,"pullAt",(function(){return Q})),n.d(r,"reduce",(function(){return Z})),n.d(r,"remove",(function(){return tt})),n.d(r,"sortBy",(function(){return et})),n.d(r,"union",(function(){return at})),n.d(r,"uniq",(function(){return ot})),n.d(r,"valuesOfKey",(function(){return ut})),n.d(r,"head",(function(){return ct})),n.d(r,"last",(function(){return st})),n.d(r,"startsWith",(function(){return ft})),n.d(r,"endsWith",(function(){return pt})),n.d(r,"filter",(function(){return c})),n.d(r,"every",(function(){return vt})),n.d(r,"some",(function(){return gt})),n.d(r,"group",(function(){return Mt})),n.d(r,"groupBy",(function(){return xt})),n.d(r,"groupToMap",(function(){return wt})),n.d(r,"getWrapBehavior",(function(){return kt})),n.d(r,"wrapBehavior",(function(){return _t})),n.d(r,"number2color",(function(){return Ct})),n.d(r,"parseRadius",(function(){return Tt})),n.d(r,"clamp",(function(){return Ft})),n.d(r,"fixedBase",(function(){return Pt})),n.d(r,"isDecimal",(function(){return Ut})),n.d(r,"isEven",(function(){return Yt})),n.d(r,"isInteger",(function(){return qt})),n.d(r,"isNegative",(function(){return Ht})),n.d(r,"isNumberEqual",(function(){return Vt})),n.d(r,"isOdd",(function(){return Jt})),n.d(r,"isPositive",(function(){return Kt})),n.d(r,"max",(function(){return U})),n.d(r,"maxBy",(function(){return Zt})),n.d(r,"min",(function(){return B})),n.d(r,"minBy",(function(){return Xt})),n.d(r,"mod",(function(){return rr})),n.d(r,"toDegree",(function(){return or})),n.d(r,"toInteger",(function(){return ir})),n.d(r,"toRadian",(function(){return cr})),n.d(r,"forIn",(function(){return sr})),n.d(r,"has",(function(){return lr})),n.d(r,"hasKey",(function(){return fr})),n.d(r,"hasValue",(function(){return dr})),n.d(r,"keys",(function(){return M})),n.d(r,"isMatch",(function(){return k})),n.d(r,"values",(function(){return pr})),n.d(r,"lowerCase",(function(){return gr})),n.d(r,"lowerFirst",(function(){return yr})),n.d(r,"substitute",(function(){return wr})),n.d(r,"upperCase",(function(){return Or})),n.d(r,"upperFirst",(function(){return jr})),n.d(r,"getType",(function(){return Nr})),n.d(r,"isArguments",(function(){return Ar})),n.d(r,"isArray",(function(){return g})),n.d(r,"isArrayLike",(function(){return o})),n.d(r,"isBoolean",(function(){return Er})),n.d(r,"isDate",(function(){return Ir})),n.d(r,"isError",(function(){return Rr})),n.d(r,"isFunction",(function(){return d})),n.d(r,"isFinite",(function(){return Lr})),n.d(r,"isNil",(function(){return m})),n.d(r,"isNull",(function(){return Ur})),n.d(r,"isNumber",(function(){return Lt})),n.d(r,"isObject",(function(){return b})),n.d(r,"isObjectLike",(function(){return _})),n.d(r,"isPlainObject",(function(){return N})),n.d(r,"isPrototype",(function(){return Gr})),n.d(r,"isRegExp",(function(){return zr})),n.d(r,"isString",(function(){return rt})),n.d(r,"isType",(function(){return p})),n.d(r,"isUndefined",(function(){return Wr})),n.d(r,"isElement",(function(){return $r})),n.d(r,"requestAnimationFrame",(function(){return Jr})),n.d(r,"clearAnimationFrame",(function(){return Qr})),n.d(r,"augment",(function(){return tn})),n.d(r,"clone",(function(){return nn})),n.d(r,"debounce",(function(){return on})),n.d(r,"memoize",(function(){return an})),n.d(r,"deepMix",(function(){return ln})),n.d(r,"each",(function(){return x})),n.d(r,"extend",(function(){return hn})),n.d(r,"indexOf",(function(){return dn})),n.d(r,"isEmpty",(function(){return gn})),n.d(r,"isEqual",(function(){return yn})),n.d(r,"isEqualWith",(function(){return xn})),n.d(r,"map",(function(){return Mn})),n.d(r,"mapValues",(function(){return kn})),n.d(r,"mix",(function(){return Zr})),n.d(r,"assign",(function(){return Zr})),n.d(r,"get",(function(){return jn})),n.d(r,"set",(function(){return _n})),n.d(r,"pick",(function(){return Nn})),n.d(r,"omit",(function(){return Cn})),n.d(r,"throttle",(function(){return An})),n.d(r,"toArray",(function(){return Tn})),n.d(r,"toString",(function(){return vr})),n.d(r,"uniqueId",(function(){return Fn})),n.d(r,"noop",(function(){return In})),n.d(r,"identity",(function(){return Pn})),n.d(r,"size",(function(){return Rn})),n.d(r,"measureTextWidth",(function(){return Un})),n.d(r,"getEllipsisText",(function(){return Bn})),n.d(r,"Cache",(function(){return Gn}));var e=function(t){return null!==t&&"function"!==typeof t&&isFinite(t.length)},o=e,i=function(t,r){return!!o(t)&&t.indexOf(r)>-1},a=i,u=function(t,r){if(!o(t))return t;for(var n=[],e=0;e<t.length;e++){var i=t[e];r(i,e)&&n.push(i)}return n},c=u,s=function(t,r){return void 0===r&&(r=[]),c(t,(function(t){return!a(r,t)}))},l=s,f={}.toString,h=function(t,r){return f.call(t)==="[object "+r+"]"},p=h,d=function(t){return p(t,"Function")},v=function(t){return null===t||void 0===t},m=v,g=function(t){return Array.isArray?Array.isArray(t):p(t,"Array")},b=function(t){var r=typeof t;return null!==t&&"object"===r||"function"===r};function y(t,r){var n;if(t)if(g(t)){for(var e=0,o=t.length;e<o;e++)if(n=r(t[e],e),!1===n)break}else if(b(t))for(var i in t)if(t.hasOwnProperty(i)&&(n=r(t[i],i),!1===n))break}var x=y,w=Object.keys?function(t){return Object.keys(t)}:function(t){var r=[];return x(t,(function(n,e){d(t)&&"prototype"===e||r.push(e)})),r},M=w;function O(t,r){var n=M(r),e=n.length;if(m(t))return!e;for(var o=0;o<e;o+=1){var i=n[o];if(r[i]!==t[i]||!(i in t))return!1}return!0}var k=O,j=function(t){return"object"===typeof t&&null!==t},_=j,S=function(t){if(!_(t)||!p(t,"Object"))return!1;if(null===Object.getPrototypeOf(t))return!0;var r=t;while(null!==Object.getPrototypeOf(r))r=Object.getPrototypeOf(r);return Object.getPrototypeOf(t)===r},N=S;function C(t,r){if(!g(t))return null;var n;if(d(r)&&(n=r),N(r)&&(n=function(t){return k(t,r)}),n)for(var e=0;e<t.length;e+=1)if(n(t[e]))return t[e];return null}var A=C;function T(t,r,n){void 0===n&&(n=0);for(var e=n;e<t.length;e++)if(r(t[e],e))return e;return-1}var E=T,F=function(t,r){for(var n=null,e=0;e<t.length;e++){var o=t[e],i=o[r];if(!m(i)){n=g(i)?i[0]:i;break}}return n},I=F,P=function(t){if(!g(t))return[];for(var r=[],n=0;n<t.length;n++)r=r.concat(t[n]);return r},R=P,L=function(t,r){if(void 0===r&&(r=[]),g(t))for(var n=0;n<t.length;n+=1)L(t[n],r);else r.push(t);return r},D=L,U=function(t){if(g(t))return t.reduce((function(t,r){return Math.max(t,r)}),t[0])},B=function(t){if(g(t))return t.reduce((function(t,r){return Math.min(t,r)}),t[0])},Y=function(t){var r=t.filter((function(t){return!isNaN(t)}));if(!r.length)return{min:0,max:0};if(g(t[0])){for(var n=[],e=0;e<t.length;e++)n=n.concat(t[e]);r=n}var o=U(r),i=B(r);return{min:i,max:o}},G=Y,q=Array.prototype,z=q.splice,H=q.indexOf,W=function(t){for(var r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];for(var e=0;e<r.length;e++){var o=r[e],i=-1;while((i=H.call(t,o))>-1)z.call(t,i,1)}return t},V=W,$=Array.prototype.splice,J=function(t,r){if(!o(t))return[];var n=t?r.length:0,e=n-1;while(n--){var i=void 0,a=r[n];n!==e&&a===i||(i=a,$.call(t,a,1))}return t},Q=J,K=function(t,r,n){if(!g(t)&&!N(t))return t;var e=n;return x(t,(function(t,n){e=r(e,t,n)})),e},Z=K,X=function(t,r){var n=[];if(!o(t))return n;var e=-1,i=[],a=t.length;while(++e<a){var u=t[e];r(u,e,t)&&(n.push(u),i.push(e))}return Q(t,i),n},tt=X,rt=function(t){return p(t,"String")};function nt(t,r){var n;if(d(r))n=function(t,n){return r(t)-r(n)};else{var e=[];rt(r)?e.push(r):g(r)&&(e=r),n=function(t,r){for(var n=0;n<e.length;n+=1){var o=e[n];if(t[o]>r[o])return 1;if(t[o]<r[o])return-1}return 0}}return t.sort(n),t}var et=nt;function ot(t,r){void 0===r&&(r=new Map);var n=[];if(Array.isArray(t))for(var e=0,o=t.length;e<o;e++){var i=t[e];r.has(i)||(n.push(i),r.set(i,!0))}return n}var it=function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return ot([].concat.apply([],t))},at=it,ut=function(t,r){for(var n=[],e={},o=0;o<t.length;o++){var i=t[o],a=i[r];if(!m(a)){g(a)||(a=[a]);for(var u=0;u<a.length;u++){var c=a[u];e[c]||(n.push(c),e[c]=!0)}}}return n};function ct(t){if(o(t))return t[0]}function st(t){if(o(t)){var r=t;return r[r.length-1]}}function lt(t,r){return!(!g(t)&&!rt(t))&&t[0]===r}var ft=lt;function ht(t,r){return!(!g(t)&&!rt(t))&&t[t.length-1]===r}var pt=ht,dt=function(t,r){for(var n=0;n<t.length;n++)if(!r(t[n],n))return!1;return!0},vt=dt,mt=function(t,r){for(var n=0;n<t.length;n++)if(r(t[n],n))return!0;return!1},gt=mt,bt=Object.prototype.hasOwnProperty;function yt(t,r){if(!r||!g(t))return{};for(var n,e={},o=d(r)?r:function(t){return t[r]},i=0;i<t.length;i++){var a=t[i];n=o(a),bt.call(e,n)?e[n].push(a):e[n]=[a]}return e}var xt=yt;function wt(t,r){if(!r)return{0:t};if(!d(r)){var n=g(r)?r:r.replace(/\s+/g,"").split("*");r=function(t){for(var r="_",e=0,o=n.length;e<o;e++)r+=t[n[e]]&&t[n[e]].toString();return r}}return xt(t,r)}var Mt=function(t,r){if(!r)return[t];var n=wt(t,r),e=[];for(var o in n)e.push(n[o]);return e};function Ot(t,r){return t["_wrap_"+r]}var kt=Ot;function jt(t,r){if(t["_wrap_"+r])return t["_wrap_"+r];var n=function(n){t[r](n)};return t["_wrap_"+r]=n,n}var _t=jt,St={};function Nt(t){var r=St[t];if(!r){for(var n=t.toString(16),e=n.length;e<6;e++)n="0"+n;r="#"+n,St[t]=r}return r}var Ct=Nt;function At(t){var r=0,n=0,e=0,o=0;return g(t)?1===t.length?r=n=e=o=t[0]:2===t.length?(r=e=t[0],n=o=t[1]):3===t.length?(r=t[0],n=o=t[1],e=t[2]):(r=t[0],n=t[1],e=t[2],o=t[3]):r=n=e=o=t,{r1:r,r2:n,r3:e,r4:o}}var Tt=At,Et=function(t,r,n){return t<r?r:t>n?n:t},Ft=Et,It=function(t,r){var n=r.toString(),e=n.indexOf(".");if(-1===e)return Math.round(t);var o=n.substr(e+1).length;return o>20&&(o=20),parseFloat(t.toFixed(o))},Pt=It,Rt=function(t){return p(t,"Number")},Lt=Rt,Dt=function(t){return Lt(t)&&t%1!==0},Ut=Dt,Bt=function(t){return Lt(t)&&t%2===0},Yt=Bt,Gt=Number.isInteger?Number.isInteger:function(t){return Lt(t)&&t%1===0},qt=Gt,zt=function(t){return Lt(t)&&t<0},Ht=zt,Wt=1e-5;function Vt(t,r,n){return void 0===n&&(n=Wt),Math.abs(t-r)<n}var $t=function(t){return Lt(t)&&t%2!==0},Jt=$t,Qt=function(t){return Lt(t)&&t>0},Kt=Qt,Zt=function(t,r){if(g(t)){for(var n,e=-1/0,o=0;o<t.length;o++){var i=t[o],a=d(r)?r(i):i[r];a>e&&(n=i,e=a)}return n}},Xt=function(t,r){if(g(t)){for(var n,e=1/0,o=0;o<t.length;o++){var i=t[o],a=d(r)?r(i):i[r];a<e&&(n=i,e=a)}return n}},tr=function(t,r){return(t%r+r)%r},rr=tr,nr=180/Math.PI,er=function(t){return nr*t},or=er,ir=parseInt,ar=Math.PI/180,ur=function(t){return ar*t},cr=ur,sr=x,lr=function(t,r){return t.hasOwnProperty(r)},fr=lr,hr=Object.values?function(t){return Object.values(t)}:function(t){var r=[];return x(t,(function(n,e){d(t)&&"prototype"===e||r.push(n)})),r},pr=hr,dr=function(t,r){return a(pr(t),r)},vr=function(t){return m(t)?"":t.toString()},mr=function(t){return vr(t).toLowerCase()},gr=mr,br=function(t){var r=vr(t);return r.charAt(0).toLowerCase()+r.substring(1)},yr=br;function xr(t,r){return t&&r?t.replace(/\\?\{([^{}]+)\}/g,(function(t,n){return"\\"===t.charAt(0)?t.slice(1):void 0===r[n]?"":r[n]})):t}var wr=xr,Mr=function(t){return vr(t).toUpperCase()},Or=Mr,kr=function(t){var r=vr(t);return r.charAt(0).toUpperCase()+r.substring(1)},jr=kr,_r={}.toString,Sr=function(t){return _r.call(t).replace(/^\[object /,"").replace(/]$/,"")},Nr=Sr,Cr=function(t){return p(t,"Arguments")},Ar=Cr,Tr=function(t){return p(t,"Boolean")},Er=Tr,Fr=function(t){return p(t,"Date")},Ir=Fr,Pr=function(t){return p(t,"Error")},Rr=Pr,Lr=function(t){return Lt(t)&&isFinite(t)},Dr=function(t){return null===t},Ur=Dr,Br=Object.prototype,Yr=function(t){var r=t&&t.constructor,n="function"===typeof r&&r.prototype||Br;return t===n},Gr=Yr,qr=function(t){return p(t,"RegExp")},zr=qr,Hr=function(t){return void 0===t},Wr=Hr,Vr=function(t){return t instanceof Element||t instanceof HTMLDocument},$r=Vr;function Jr(t){var r=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.msRequestAnimationFrame||function(t){return setTimeout(t,16)};return r(t)}function Qr(t){var r=window.cancelAnimationFrame||window.webkitCancelAnimationFrame||window.mozCancelAnimationFrame||window.msCancelAnimationFrame||clearTimeout;r(t)}function Kr(t,r){for(var n in r)r.hasOwnProperty(n)&&"constructor"!==n&&void 0!==r[n]&&(t[n]=r[n])}function Zr(t,r,n,e){return r&&Kr(t,r),n&&Kr(t,n),e&&Kr(t,e),t}var Xr=function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];for(var n=t[0],e=1;e<t.length;e++){var o=t[e];d(o)&&(o=o.prototype),Zr(n.prototype,o)}},tn=Xr,rn=function(t){if("object"!==typeof t||null===t)return t;var r;if(g(t)){r=[];for(var n=0,e=t.length;n<e;n++)"object"===typeof t[n]&&null!=t[n]?r[n]=rn(t[n]):r[n]=t[n]}else for(var o in r={},t)"object"===typeof t[o]&&null!=t[o]?r[o]=rn(t[o]):r[o]=t[o];return r},nn=rn;function en(t,r,n){var e;return function(){var o=this,i=arguments,a=function(){e=null,n||t.apply(o,i)},u=n&&!e;clearTimeout(e),e=setTimeout(a,r),u&&t.apply(o,i)}}var on=en,an=function(t,r){if(!d(t))throw new TypeError("Expected a function");var n=function(){for(var e=[],o=0;o<arguments.length;o++)e[o]=arguments[o];var i=r?r.apply(this,e):e[0],a=n.cache;if(a.has(i))return a.get(i);var u=t.apply(this,e);return a.set(i,u),u};return n.cache=new Map,n},un=5;function cn(t,r,n,e){for(var o in n=n||0,e=e||un,r)if(r.hasOwnProperty(o)){var i=r[o];null!==i&&N(i)?(N(t[o])||(t[o]={}),n<e?cn(t[o],i,n+1,e):t[o]=r[o]):g(i)?(t[o]=[],t[o]=t[o].concat(i)):void 0!==i&&(t[o]=i)}}var sn=function(t){for(var r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];for(var e=0;e<r.length;e+=1)cn(t,r[e]);return t},ln=sn,fn=function(t,r,n,e){d(r)||(n=r,r=t,t=function(){});var o=Object.create?function(t,r){return Object.create(t,{constructor:{value:r}})}:function(t,r){function n(){}n.prototype=t;var e=new n;return e.constructor=r,e},i=o(r.prototype,t);return t.prototype=Zr(i,t.prototype),t.superclass=o(r.prototype,r),Zr(i,n),Zr(t,e),t},hn=fn,pn=function(t,r){if(!o(t))return-1;var n=Array.prototype.indexOf;if(n)return n.call(t,r);for(var e=-1,i=0;i<t.length;i++)if(t[i]===r){e=i;break}return e},dn=pn,vn=Object.prototype.hasOwnProperty;function mn(t){if(m(t))return!0;if(o(t))return!t.length;var r=Nr(t);if("Map"===r||"Set"===r)return!t.size;if(Gr(t))return!Object.keys(t).length;for(var n in t)if(vn.call(t,n))return!1;return!0}var gn=mn,bn=function(t,r){if(t===r)return!0;if(!t||!r)return!1;if(rt(t)||rt(r))return!1;if(o(t)||o(r)){if(t.length!==r.length)return!1;for(var n=!0,e=0;e<t.length;e++)if(n=bn(t[e],r[e]),!n)break;return n}if(_(t)||_(r)){var i=Object.keys(t),a=Object.keys(r);if(i.length!==a.length)return!1;for(n=!0,e=0;e<i.length;e++)if(n=bn(t[i[e]],r[i[e]]),!n)break;return n}return!1},yn=bn,xn=function(t,r,n){return d(n)?!!n(t,r):yn(t,r)},wn=function(t,r){if(!o(t))return t;for(var n=[],e=0;e<t.length;e++){var i=t[e];n.push(r(i,e))}return n},Mn=wn,On=function(t){return t},kn=function(t,r){void 0===r&&(r=On);var n={};return b(t)&&!m(t)&&Object.keys(t).forEach((function(e){n[e]=r(t[e],e)})),n},jn=function(t,r,n){var e=0,o=rt(r)?r.split("."):r;while(t&&e<o.length)t=t[o[e++]];return void 0===t||e<o.length?n:t},_n=function(t,r,n){var e=t,o=rt(r)?r.split("."):r;return o.forEach((function(t,r){r<o.length-1?(b(e[t])||(e[t]=Lt(o[r+1])?[]:{}),e=e[t]):e[t]=n})),t},Sn=Object.prototype.hasOwnProperty,Nn=function(t,r){if(null===t||!N(t))return{};var n={};return x(r,(function(r){Sn.call(t,r)&&(n[r]=t[r])})),n},Cn=function(t,r){return Z(t,(function(t,n,e){return r.includes(e)||(t[e]=n),t}),{})},An=function(t,r,n){var e,o,i,a,u=0;n||(n={});var c=function(){u=!1===n.leading?0:Date.now(),e=null,a=t.apply(o,i),e||(o=i=null)},s=function(){var s=Date.now();u||!1!==n.leading||(u=s);var l=r-(s-u);return o=this,i=arguments,l<=0||l>r?(e&&(clearTimeout(e),e=null),u=s,a=t.apply(o,i),e||(o=i=null)):e||!1===n.trailing||(e=setTimeout(c,l)),a};return s.cancel=function(){clearTimeout(e),u=0,e=o=i=null},s},Tn=function(t){return o(t)?Array.prototype.slice.call(t):[]},En={},Fn=function(t){return t=t||"g",En[t]?En[t]+=1:En[t]=1,t+En[t]},In=function(){},Pn=function(t){return t};function Rn(t){return m(t)?0:o(t)?t.length:Object.keys(t).length}Object.create;function Ln(){for(var t=0,r=0,n=arguments.length;r<n;r++)t+=arguments[r].length;var e=Array(t),o=0;for(r=0;r<n;r++)for(var i=arguments[r],a=0,u=i.length;a<u;a++,o++)e[o]=i[a];return e}var Dn;Object.create;var Un=an((function(t,r){void 0===r&&(r={});var n=r.fontSize,e=r.fontFamily,o=r.fontWeight,i=r.fontStyle,a=r.fontVariant;return Dn||(Dn=document.createElement("canvas").getContext("2d")),Dn.font=[i,a,o,n+"px",e].join(" "),Dn.measureText(rt(t)?t:"").width}),(function(t,r){return void 0===r&&(r={}),Ln([t],pr(r)).join("")})),Bn=function(t,r,n,e){void 0===e&&(e="...");var o,i,a=16,u=Un(e,n),c=rt(t)?t:vr(t),s=r,l=[];if(Un(t,n)<=r)return t;while(1){if(o=c.substr(0,a),i=Un(o,n),i+u>s&&i>s)break;if(l.push(o),s-=i,c=c.substr(a),!c)return l.join("")}while(1){if(o=c.substr(0,1),i=Un(o,n),i+u>s)break;if(l.push(o),s-=i,c=c.substr(1),!c)return l.join("")}return""+l.join("")+e},Yn=function(){function t(){this.map={}}return t.prototype.has=function(t){return void 0!==this.map[t]},t.prototype.get=function(t,r){var n=this.map[t];return void 0===n?r:n},t.prototype.set=function(t,r){this.map[t]=r},t.prototype.clear=function(){this.map={}},t.prototype.delete=function(t){delete this.map[t]},t.prototype.size=function(){return Object.keys(this.map).length},t}(),Gn=Yn},9523:function(t,r){function n(t,r,n){return r in t?Object.defineProperty(t,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[r]=n,t}t.exports=n,t.exports.__esModule=!0,t.exports["default"]=t.exports},"970b":function(t,r,n){function e(t,r){if(!(t instanceof r))throw new TypeError("Cannot call a class as a function")}n("d9e2"),t.exports=e,t.exports.__esModule=!0,t.exports["default"]=t.exports},"9b42":function(t,r,n){function e(t,r){var n=null==t?null:"undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var e,o,i=[],a=!0,u=!1;try{for(n=n.call(t);!(a=(e=n.next()).done);a=!0)if(i.push(e.value),r&&i.length===r)break}catch(c){u=!0,o=c}finally{try{a||null==n["return"]||n["return"]()}finally{if(u)throw o}}return i}}n("a4d3"),n("e01a"),n("d3b7"),n("d28b"),n("3ca3"),n("ddb0"),t.exports=e,t.exports.__esModule=!0,t.exports["default"]=t.exports},a279:function(t,r,n){var e=n("b753");function o(){for(var t={},r=Object.keys(e),n=r.length,o=0;o<n;o++)t[r[o]]={distance:-1,parent:null};return t}function i(t){var r=o(),n=[t];r[t].distance=0;while(n.length)for(var i=n.pop(),a=Object.keys(e[i]),u=a.length,c=0;c<u;c++){var s=a[c],l=r[s];-1===l.distance&&(l.distance=r[i].distance+1,l.parent=i,n.unshift(s))}return r}function a(t,r){return function(n){return r(t(n))}}function u(t,r){var n=[r[t].parent,t],o=e[r[t].parent][t],i=r[t].parent;while(r[i].parent)n.unshift(r[i].parent),o=a(e[r[i].parent][i],o),i=r[i].parent;return o.conversion=n,o}t.exports=function(t){for(var r=i(t),n={},e=Object.keys(r),o=e.length,a=0;a<o;a++){var c=e[a],s=r[c];null!==s.parent&&(n[c]=u(c,r))}return n}},a34a:function(t,r,n){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},e(t)}n("6c57"),n("a4d3"),n("e01a"),n("d3b7"),n("d28b"),n("3ca3"),n("ddb0");var o=n("7ec2")();t.exports=o;try{regeneratorRuntime=o}catch(i){"object"===("undefined"===typeof globalThis?"undefined":e(globalThis))?globalThis.regeneratorRuntime=o:Function("r","regeneratorRuntime = r")(o)}},b753:function(t,r,n){var e=n("4f4d"),o={};for(var i in e)e.hasOwnProperty(i)&&(o[e[i]]=i);var a=t.exports={rgb:{channels:3,labels:"rgb"},hsl:{channels:3,labels:"hsl"},hsv:{channels:3,labels:"hsv"},hwb:{channels:3,labels:"hwb"},cmyk:{channels:4,labels:"cmyk"},xyz:{channels:3,labels:"xyz"},lab:{channels:3,labels:"lab"},lch:{channels:3,labels:"lch"},hex:{channels:1,labels:["hex"]},keyword:{channels:1,labels:["keyword"]},ansi16:{channels:1,labels:["ansi16"]},ansi256:{channels:1,labels:["ansi256"]},hcg:{channels:3,labels:["h","c","g"]},apple:{channels:3,labels:["r16","g16","b16"]},gray:{channels:1,labels:["gray"]}};for(var u in a)if(a.hasOwnProperty(u)){if(!("channels"in a[u]))throw new Error("missing channels property: "+u);if(!("labels"in a[u]))throw new Error("missing channel labels property: "+u);if(a[u].labels.length!==a[u].channels)throw new Error("channel and label counts mismatch: "+u);var c=a[u].channels,s=a[u].labels;delete a[u].channels,delete a[u].labels,Object.defineProperty(a[u],"channels",{value:c}),Object.defineProperty(a[u],"labels",{value:s})}function l(t,r){return Math.pow(t[0]-r[0],2)+Math.pow(t[1]-r[1],2)+Math.pow(t[2]-r[2],2)}a.rgb.hsl=function(t){var r,n,e,o=t[0]/255,i=t[1]/255,a=t[2]/255,u=Math.min(o,i,a),c=Math.max(o,i,a),s=c-u;return c===u?r=0:o===c?r=(i-a)/s:i===c?r=2+(a-o)/s:a===c&&(r=4+(o-i)/s),r=Math.min(60*r,360),r<0&&(r+=360),e=(u+c)/2,n=c===u?0:e<=.5?s/(c+u):s/(2-c-u),[r,100*n,100*e]},a.rgb.hsv=function(t){var r,n,e,o,i,a=t[0]/255,u=t[1]/255,c=t[2]/255,s=Math.max(a,u,c),l=s-Math.min(a,u,c),f=function(t){return(s-t)/6/l+.5};return 0===l?o=i=0:(i=l/s,r=f(a),n=f(u),e=f(c),a===s?o=e-n:u===s?o=1/3+r-e:c===s&&(o=2/3+n-r),o<0?o+=1:o>1&&(o-=1)),[360*o,100*i,100*s]},a.rgb.hwb=function(t){var r=t[0],n=t[1],e=t[2],o=a.rgb.hsl(t)[0],i=1/255*Math.min(r,Math.min(n,e));return e=1-1/255*Math.max(r,Math.max(n,e)),[o,100*i,100*e]},a.rgb.cmyk=function(t){var r,n,e,o,i=t[0]/255,a=t[1]/255,u=t[2]/255;return o=Math.min(1-i,1-a,1-u),r=(1-i-o)/(1-o)||0,n=(1-a-o)/(1-o)||0,e=(1-u-o)/(1-o)||0,[100*r,100*n,100*e,100*o]},a.rgb.keyword=function(t){var r=o[t];if(r)return r;var n,i=1/0;for(var a in e)if(e.hasOwnProperty(a)){var u=e[a],c=l(t,u);c<i&&(i=c,n=a)}return n},a.keyword.rgb=function(t){return e[t]},a.rgb.xyz=function(t){var r=t[0]/255,n=t[1]/255,e=t[2]/255;r=r>.04045?Math.pow((r+.055)/1.055,2.4):r/12.92,n=n>.04045?Math.pow((n+.055)/1.055,2.4):n/12.92,e=e>.04045?Math.pow((e+.055)/1.055,2.4):e/12.92;var o=.4124*r+.3576*n+.1805*e,i=.2126*r+.7152*n+.0722*e,a=.0193*r+.1192*n+.9505*e;return[100*o,100*i,100*a]},a.rgb.lab=function(t){var r,n,e,o=a.rgb.xyz(t),i=o[0],u=o[1],c=o[2];return i/=95.047,u/=100,c/=108.883,i=i>.008856?Math.pow(i,1/3):7.787*i+16/116,u=u>.008856?Math.pow(u,1/3):7.787*u+16/116,c=c>.008856?Math.pow(c,1/3):7.787*c+16/116,r=116*u-16,n=500*(i-u),e=200*(u-c),[r,n,e]},a.hsl.rgb=function(t){var r,n,e,o,i,a=t[0]/360,u=t[1]/100,c=t[2]/100;if(0===u)return i=255*c,[i,i,i];n=c<.5?c*(1+u):c+u-c*u,r=2*c-n,o=[0,0,0];for(var s=0;s<3;s++)e=a+1/3*-(s-1),e<0&&e++,e>1&&e--,i=6*e<1?r+6*(n-r)*e:2*e<1?n:3*e<2?r+(n-r)*(2/3-e)*6:r,o[s]=255*i;return o},a.hsl.hsv=function(t){var r,n,e=t[0],o=t[1]/100,i=t[2]/100,a=o,u=Math.max(i,.01);return i*=2,o*=i<=1?i:2-i,a*=u<=1?u:2-u,n=(i+o)/2,r=0===i?2*a/(u+a):2*o/(i+o),[e,100*r,100*n]},a.hsv.rgb=function(t){var r=t[0]/60,n=t[1]/100,e=t[2]/100,o=Math.floor(r)%6,i=r-Math.floor(r),a=255*e*(1-n),u=255*e*(1-n*i),c=255*e*(1-n*(1-i));switch(e*=255,o){case 0:return[e,c,a];case 1:return[u,e,a];case 2:return[a,e,c];case 3:return[a,u,e];case 4:return[c,a,e];case 5:return[e,a,u]}},a.hsv.hsl=function(t){var r,n,e,o=t[0],i=t[1]/100,a=t[2]/100,u=Math.max(a,.01);return e=(2-i)*a,r=(2-i)*u,n=i*u,n/=r<=1?r:2-r,n=n||0,e/=2,[o,100*n,100*e]},a.hwb.rgb=function(t){var r,n,e,o,i,a,u,c=t[0]/360,s=t[1]/100,l=t[2]/100,f=s+l;switch(f>1&&(s/=f,l/=f),r=Math.floor(6*c),n=1-l,e=6*c-r,0!==(1&r)&&(e=1-e),o=s+e*(n-s),r){default:case 6:case 0:i=n,a=o,u=s;break;case 1:i=o,a=n,u=s;break;case 2:i=s,a=n,u=o;break;case 3:i=s,a=o,u=n;break;case 4:i=o,a=s,u=n;break;case 5:i=n,a=s,u=o;break}return[255*i,255*a,255*u]},a.cmyk.rgb=function(t){var r,n,e,o=t[0]/100,i=t[1]/100,a=t[2]/100,u=t[3]/100;return r=1-Math.min(1,o*(1-u)+u),n=1-Math.min(1,i*(1-u)+u),e=1-Math.min(1,a*(1-u)+u),[255*r,255*n,255*e]},a.xyz.rgb=function(t){var r,n,e,o=t[0]/100,i=t[1]/100,a=t[2]/100;return r=3.2406*o+-1.5372*i+-.4986*a,n=-.9689*o+1.8758*i+.0415*a,e=.0557*o+-.204*i+1.057*a,r=r>.0031308?1.055*Math.pow(r,1/2.4)-.055:12.92*r,n=n>.0031308?1.055*Math.pow(n,1/2.4)-.055:12.92*n,e=e>.0031308?1.055*Math.pow(e,1/2.4)-.055:12.92*e,r=Math.min(Math.max(0,r),1),n=Math.min(Math.max(0,n),1),e=Math.min(Math.max(0,e),1),[255*r,255*n,255*e]},a.xyz.lab=function(t){var r,n,e,o=t[0],i=t[1],a=t[2];return o/=95.047,i/=100,a/=108.883,o=o>.008856?Math.pow(o,1/3):7.787*o+16/116,i=i>.008856?Math.pow(i,1/3):7.787*i+16/116,a=a>.008856?Math.pow(a,1/3):7.787*a+16/116,r=116*i-16,n=500*(o-i),e=200*(i-a),[r,n,e]},a.lab.xyz=function(t){var r,n,e,o=t[0],i=t[1],a=t[2];n=(o+16)/116,r=i/500+n,e=n-a/200;var u=Math.pow(n,3),c=Math.pow(r,3),s=Math.pow(e,3);return n=u>.008856?u:(n-16/116)/7.787,r=c>.008856?c:(r-16/116)/7.787,e=s>.008856?s:(e-16/116)/7.787,r*=95.047,n*=100,e*=108.883,[r,n,e]},a.lab.lch=function(t){var r,n,e,o=t[0],i=t[1],a=t[2];return r=Math.atan2(a,i),n=360*r/2/Math.PI,n<0&&(n+=360),e=Math.sqrt(i*i+a*a),[o,e,n]},a.lch.lab=function(t){var r,n,e,o=t[0],i=t[1],a=t[2];return e=a/360*2*Math.PI,r=i*Math.cos(e),n=i*Math.sin(e),[o,r,n]},a.rgb.ansi16=function(t){var r=t[0],n=t[1],e=t[2],o=1 in arguments?arguments[1]:a.rgb.hsv(t)[2];if(o=Math.round(o/50),0===o)return 30;var i=30+(Math.round(e/255)<<2|Math.round(n/255)<<1|Math.round(r/255));return 2===o&&(i+=60),i},a.hsv.ansi16=function(t){return a.rgb.ansi16(a.hsv.rgb(t),t[2])},a.rgb.ansi256=function(t){var r=t[0],n=t[1],e=t[2];if(r===n&&n===e)return r<8?16:r>248?231:Math.round((r-8)/247*24)+232;var o=16+36*Math.round(r/255*5)+6*Math.round(n/255*5)+Math.round(e/255*5);return o},a.ansi16.rgb=function(t){var r=t%10;if(0===r||7===r)return t>50&&(r+=3.5),r=r/10.5*255,[r,r,r];var n=.5*(1+~~(t>50)),e=(1&r)*n*255,o=(r>>1&1)*n*255,i=(r>>2&1)*n*255;return[e,o,i]},a.ansi256.rgb=function(t){if(t>=232){var r=10*(t-232)+8;return[r,r,r]}var n;t-=16;var e=Math.floor(t/36)/5*255,o=Math.floor((n=t%36)/6)/5*255,i=n%6/5*255;return[e,o,i]},a.rgb.hex=function(t){var r=((255&Math.round(t[0]))<<16)+((255&Math.round(t[1]))<<8)+(255&Math.round(t[2])),n=r.toString(16).toUpperCase();return"000000".substring(n.length)+n},a.hex.rgb=function(t){var r=t.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);if(!r)return[0,0,0];var n=r[0];3===r[0].length&&(n=n.split("").map((function(t){return t+t})).join(""));var e=parseInt(n,16),o=e>>16&255,i=e>>8&255,a=255&e;return[o,i,a]},a.rgb.hcg=function(t){var r,n,e=t[0]/255,o=t[1]/255,i=t[2]/255,a=Math.max(Math.max(e,o),i),u=Math.min(Math.min(e,o),i),c=a-u;return r=c<1?u/(1-c):0,n=c<=0?0:a===e?(o-i)/c%6:a===o?2+(i-e)/c:4+(e-o)/c+4,n/=6,n%=1,[360*n,100*c,100*r]},a.hsl.hcg=function(t){var r=t[1]/100,n=t[2]/100,e=1,o=0;return e=n<.5?2*r*n:2*r*(1-n),e<1&&(o=(n-.5*e)/(1-e)),[t[0],100*e,100*o]},a.hsv.hcg=function(t){var r=t[1]/100,n=t[2]/100,e=r*n,o=0;return e<1&&(o=(n-e)/(1-e)),[t[0],100*e,100*o]},a.hcg.rgb=function(t){var r=t[0]/360,n=t[1]/100,e=t[2]/100;if(0===n)return[255*e,255*e,255*e];var o=[0,0,0],i=r%1*6,a=i%1,u=1-a,c=0;switch(Math.floor(i)){case 0:o[0]=1,o[1]=a,o[2]=0;break;case 1:o[0]=u,o[1]=1,o[2]=0;break;case 2:o[0]=0,o[1]=1,o[2]=a;break;case 3:o[0]=0,o[1]=u,o[2]=1;break;case 4:o[0]=a,o[1]=0,o[2]=1;break;default:o[0]=1,o[1]=0,o[2]=u}return c=(1-n)*e,[255*(n*o[0]+c),255*(n*o[1]+c),255*(n*o[2]+c)]},a.hcg.hsv=function(t){var r=t[1]/100,n=t[2]/100,e=r+n*(1-r),o=0;return e>0&&(o=r/e),[t[0],100*o,100*e]},a.hcg.hsl=function(t){var r=t[1]/100,n=t[2]/100,e=n*(1-r)+.5*r,o=0;return e>0&&e<.5?o=r/(2*e):e>=.5&&e<1&&(o=r/(2*(1-e))),[t[0],100*o,100*e]},a.hcg.hwb=function(t){var r=t[1]/100,n=t[2]/100,e=r+n*(1-r);return[t[0],100*(e-r),100*(1-e)]},a.hwb.hcg=function(t){var r=t[1]/100,n=t[2]/100,e=1-n,o=e-r,i=0;return o<1&&(i=(e-o)/(1-o)),[t[0],100*o,100*i]},a.apple.rgb=function(t){return[t[0]/65535*255,t[1]/65535*255,t[2]/65535*255]},a.rgb.apple=function(t){return[t[0]/255*65535,t[1]/255*65535,t[2]/255*65535]},a.gray.rgb=function(t){return[t[0]/100*255,t[0]/100*255,t[0]/100*255]},a.gray.hsl=a.gray.hsv=function(t){return[0,0,t[0]]},a.gray.hwb=function(t){return[0,100,t[0]]},a.gray.cmyk=function(t){return[0,0,0,t[0]]},a.gray.lab=function(t){return[t[0],0,0]},a.gray.hex=function(t){var r=255&Math.round(t[0]/100*255),n=(r<<16)+(r<<8)+r,e=n.toString(16).toUpperCase();return"000000".substring(e.length)+e},a.rgb.gray=function(t){var r=(t[0]+t[1]+t[2])/3;return[r/255*100]}},bb15:function(t,r,n){var e=n("b753"),o=n("a279"),i={},a=Object.keys(e);function u(t){var r=function(r){return void 0===r||null===r?r:(arguments.length>1&&(r=Array.prototype.slice.call(arguments)),t(r))};return"conversion"in t&&(r.conversion=t.conversion),r}function c(t){var r=function(r){if(void 0===r||null===r)return r;arguments.length>1&&(r=Array.prototype.slice.call(arguments));var n=t(r);if("object"===typeof n)for(var e=n.length,o=0;o<e;o++)n[o]=Math.round(n[o]);return n};return"conversion"in t&&(r.conversion=t.conversion),r}a.forEach((function(t){i[t]={},Object.defineProperty(i[t],"channels",{value:e[t].channels}),Object.defineProperty(i[t],"labels",{value:e[t].labels});var r=o(t),n=Object.keys(r);n.forEach((function(n){var e=r[n];i[t][n]=c(e),i[t][n].raw=u(e)}))})),t.exports=i},c135:function(t,r){function n(t){if(Array.isArray(t))return t}t.exports=n,t.exports.__esModule=!0,t.exports["default"]=t.exports},c240:function(t,r,n){function e(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}n("d9e2"),t.exports=e,t.exports.__esModule=!0,t.exports["default"]=t.exports},c86f:function(t,r){function n(t,r,n,e){n&&Object.defineProperty(t,r,{enumerable:n.enumerable,configurable:n.configurable,writable:n.writable,value:n.initializer?n.initializer.call(e):void 0})}t.exports=n,t.exports.__esModule=!0,t.exports["default"]=t.exports},c973:function(t,r,n){function e(t,r,n,e,o,i,a){try{var u=t[i](a),c=u.value}catch(s){return void n(s)}u.done?r(c):Promise.resolve(c).then(e,o)}function o(t){return function(){var r=this,n=arguments;return new Promise((function(o,i){var a=t.apply(r,n);function u(t){e(a,o,i,u,c,"next",t)}function c(t){e(a,o,i,u,c,"throw",t)}u(void 0)}))}}n("d3b7"),t.exports=o,t.exports.__esModule=!0,t.exports["default"]=t.exports},d400:function(t,r,n){function e(t,r){throw new Error("Decorating class property failed. Please ensure that proposal-class-properties is enabled and runs after the decorators transform.")}n("d9e2"),t.exports=e,t.exports.__esModule=!0,t.exports["default"]=t.exports},ed6d:function(t,r,n){n("d9e2");var e=n("4a4b");function o(t,r){if("function"!==typeof r&&null!==r)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(r&&r.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),r&&e(t,r)}t.exports=o,t.exports.__esModule=!0,t.exports["default"]=t.exports},f2fb:function(t,r,n){"use strict";n.d(r,"a",(function(){return d})),n.d(r,"b",(function(){return F}));var e=n("8937"),o={};function i(t){return o[t]}function a(t,r){o[t]=r}var u=function(){function t(t){this.type="base",this.isCategory=!1,this.isLinear=!1,this.isContinuous=!1,this.isIdentity=!1,this.values=[],this.range=[0,1],this.ticks=[],this.__cfg__=t,this.initCfg(),this.init()}return t.prototype.translate=function(t){return t},t.prototype.change=function(t){Object(e["assign"])(this.__cfg__,t),this.init()},t.prototype.clone=function(){return this.constructor(this.__cfg__)},t.prototype.getTicks=function(){var t=this;return Object(e["map"])(this.ticks,(function(r,n){return Object(e["isObject"])(r)?r:{text:t.getText(r,n),tickValue:r,value:t.scale(r)}}))},t.prototype.getText=function(t,r){var n=this.formatter,o=n?n(t,r):t;return Object(e["isNil"])(o)||!Object(e["isFunction"])(o.toString)?"":o.toString()},t.prototype.getConfig=function(t){return this.__cfg__[t]},t.prototype.init=function(){Object(e["assign"])(this,this.__cfg__),this.setDomain(),Object(e["isEmpty"])(this.getConfig("ticks"))&&(this.ticks=this.calculateTicks())},t.prototype.initCfg=function(){},t.prototype.setDomain=function(){},t.prototype.calculateTicks=function(){var t=this.tickMethod,r=[];if(Object(e["isString"])(t)){var n=i(t);if(!n)throw new Error("There is no method to to calculate ticks!");r=n(this)}else Object(e["isFunction"])(t)&&(r=t(this));return r},t.prototype.rangeMin=function(){return this.range[0]},t.prototype.rangeMax=function(){return this.range[1]},t.prototype.calcPercent=function(t,r,n){return Object(e["isNumber"])(t)?(t-r)/(n-r):NaN},t.prototype.calcValue=function(t,r,n){return r+t*(n-r)},t}(),c=u,s=function(t,r){return s=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])},s(t,r)};function l(t,r){if("function"!==typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=t}s(t,r),t.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}var f=function(){return f=Object.assign||function(t){for(var r,n=1,e=arguments.length;n<e;n++)for(var o in r=arguments[n],r)Object.prototype.hasOwnProperty.call(r,o)&&(t[o]=r[o]);return t},f.apply(this,arguments)};Object.create;function h(){for(var t=0,r=0,n=arguments.length;r<n;r++)t+=arguments[r].length;var e=Array(t),o=0;for(r=0;r<n;r++)for(var i=arguments[r],a=0,u=i.length;a<u;a++,o++)e[o]=i[a];return e}Object.create;var p=function(t){function r(){var r=null!==t&&t.apply(this,arguments)||this;return r.type="cat",r.isCategory=!0,r}return l(r,t),r.prototype.buildIndexMap=function(){if(!this.translateIndexMap){this.translateIndexMap=new Map;for(var t=0;t<this.values.length;t++)this.translateIndexMap.set(this.values[t],t)}},r.prototype.translate=function(t){this.buildIndexMap();var r=this.translateIndexMap.get(t);return void 0===r&&(r=Object(e["isNumber"])(t)?t:NaN),r},r.prototype.scale=function(t){var r=this.translate(t),n=this.calcPercent(r,this.min,this.max);return this.calcValue(n,this.rangeMin(),this.rangeMax())},r.prototype.invert=function(t){var r=this.max-this.min,n=this.calcPercent(t,this.rangeMin(),this.rangeMax()),e=Math.round(r*n)+this.min;return e<this.min||e>this.max?NaN:this.values[e]},r.prototype.getText=function(r){for(var n=[],o=1;o<arguments.length;o++)n[o-1]=arguments[o];var i=r;return Object(e["isNumber"])(r)&&!this.values.includes(r)&&(i=this.values[i]),t.prototype.getText.apply(this,h([i],n))},r.prototype.initCfg=function(){this.tickMethod="cat"},r.prototype.setDomain=function(){if(Object(e["isNil"])(this.getConfig("min"))&&(this.min=0),Object(e["isNil"])(this.getConfig("max"))){var t=this.values.length;this.max=t>1?t-1:t}this.translateIndexMap&&(this.translateIndexMap=void 0)},r}(c),d=p,v=n("1789"),m=function(t){return function(r,n,o,i){var a=Object(e["isNil"])(o)?0:o,u=Object(e["isNil"])(i)?r.length:i;while(a<u){var c=a+u>>>1;t(r[c])>n?u=c:a=c+1}return a}},g="format";function b(t,r){var n=v[g]||v["default"][g];return n(t,r)}function y(t){return Object(e["isString"])(t)&&(t=t.indexOf("T")>0?new Date(t).getTime():new Date(t.replace(/-/gi,"/")).getTime()),Object(e["isDate"])(t)&&(t=t.getTime()),t}var x=1e3,w=60*x,M=60*w,O=24*M,k=31*O,j=365*O,_=[["HH:mm:ss",x],["HH:mm:ss",10*x],["HH:mm:ss",30*x],["HH:mm",w],["HH:mm",10*w],["HH:mm",30*w],["HH",M],["HH",6*M],["HH",12*M],["YYYY-MM-DD",O],["YYYY-MM-DD",4*O],["YYYY-WW",7*O],["YYYY-MM",k],["YYYY-MM",4*k],["YYYY-MM",6*k],["YYYY",380*O]];function S(t,r,n){var o=(r-t)/n,i=m((function(t){return t[1]}))(_,o)-1,a=_[i];return i<0?a=_[0]:i>=_.length&&(a=Object(e["last"])(_)),a}var N=function(t){function r(){var r=null!==t&&t.apply(this,arguments)||this;return r.type="timeCat",r}return l(r,t),r.prototype.translate=function(t){t=y(t);var r=this.values.indexOf(t);return-1===r&&(r=Object(e["isNumber"])(t)&&t<this.values.length?t:NaN),r},r.prototype.getText=function(t,r){var n=this.translate(t);if(n>-1){var e=this.values[n],o=this.formatter;return e=o?o(e,r):b(e,this.mask),e}return t},r.prototype.initCfg=function(){this.tickMethod="time-cat",this.mask="YYYY-MM-DD",this.tickCount=7},r.prototype.setDomain=function(){var r=this.values;Object(e["each"])(r,(function(t,n){r[n]=y(t)})),r.sort((function(t,r){return t-r})),t.prototype.setDomain.call(this)},r}(d),C=N,A=function(t){function r(){var r=null!==t&&t.apply(this,arguments)||this;return r.isContinuous=!0,r}return l(r,t),r.prototype.scale=function(t){if(Object(e["isNil"])(t))return NaN;var r=this.rangeMin(),n=this.rangeMax(),o=this.max,i=this.min;if(o===i)return r;var a=this.getScalePercent(t);return r+a*(n-r)},r.prototype.init=function(){t.prototype.init.call(this);var r=this.ticks,n=Object(e["head"])(r),o=Object(e["last"])(r);n<this.min&&(this.min=n),o>this.max&&(this.max=o),Object(e["isNil"])(this.minLimit)||(this.min=n),Object(e["isNil"])(this.maxLimit)||(this.max=o)},r.prototype.setDomain=function(){var t=Object(e["getRange"])(this.values),r=t.min,n=t.max;Object(e["isNil"])(this.min)&&(this.min=r),Object(e["isNil"])(this.max)&&(this.max=n),this.min>this.max&&(this.min=r,this.max=n)},r.prototype.calculateTicks=function(){var r=this,n=t.prototype.calculateTicks.call(this);return this.nice||(n=Object(e["filter"])(n,(function(t){return t>=r.min&&t<=r.max}))),n},r.prototype.getScalePercent=function(t){var r=this.max,n=this.min;return(t-n)/(r-n)},r.prototype.getInvertPercent=function(t){return(t-this.rangeMin())/(this.rangeMax()-this.rangeMin())},r}(c),T=A,E=function(t){function r(){var r=null!==t&&t.apply(this,arguments)||this;return r.type="linear",r.isLinear=!0,r}return l(r,t),r.prototype.invert=function(t){var r=this.getInvertPercent(t);return this.min+r*(this.max-this.min)},r.prototype.initCfg=function(){this.tickMethod="wilkinson-extended",this.nice=!1},r}(T),F=E;function I(t,r){var n,e=Math.E;return n=r>=0?Math.pow(e,Math.log(r)/t):-1*Math.pow(e,Math.log(-r)/t),n}function P(t,r){return 1===t?1:Math.log(r)/Math.log(t)}function R(t,r,n){Object(e["isNil"])(n)&&(n=Math.max.apply(null,t));var o=n;return Object(e["each"])(t,(function(t){t>0&&t<o&&(o=t)})),o===n&&(o=n/r),o>1&&(o=1),o}var L=function(t){function r(){var r=null!==t&&t.apply(this,arguments)||this;return r.type="log",r}return l(r,t),r.prototype.invert=function(t){var r,n=this.base,e=P(n,this.max),o=this.rangeMin(),i=this.rangeMax()-o,a=this.positiveMin;if(a){if(0===t)return 0;r=P(n,a/n);var u=1/(e-r)*i;if(t<u)return t/u*a}else r=P(n,this.min);var c=(t-o)/i,s=c*(e-r)+r;return Math.pow(n,s)},r.prototype.initCfg=function(){this.tickMethod="log",this.base=10,this.tickCount=6,this.nice=!0},r.prototype.setDomain=function(){t.prototype.setDomain.call(this);var r=this.min;if(r<0)throw new Error("When you use log scale, the minimum value must be greater than zero!");0===r&&(this.positiveMin=R(this.values,this.base,this.max))},r.prototype.getScalePercent=function(t){var r=this.max,n=this.min;if(r===n)return 0;if(t<=0)return 0;var e,o=this.base,i=this.positiveMin;return i&&(n=1*i/o),e=t<i?t/i/(P(o,r)-P(o,n)):(P(o,t)-P(o,n))/(P(o,r)-P(o,n)),e},r}(T),D=L,U=function(t){function r(){var r=null!==t&&t.apply(this,arguments)||this;return r.type="pow",r}return l(r,t),r.prototype.invert=function(t){var r=this.getInvertPercent(t),n=this.exponent,e=I(n,this.max),o=I(n,this.min),i=r*(e-o)+o,a=i>=0?1:-1;return Math.pow(i,n)*a},r.prototype.initCfg=function(){this.tickMethod="pow",this.exponent=2,this.tickCount=5,this.nice=!0},r.prototype.getScalePercent=function(t){var r=this.max,n=this.min;if(r===n)return 0;var e=this.exponent,o=(I(e,t)-I(e,n))/(I(e,r)-I(e,n));return o},r}(T),B=U,Y=function(t){function r(){var r=null!==t&&t.apply(this,arguments)||this;return r.type="time",r}return l(r,t),r.prototype.getText=function(t,r){var n=this.translate(t),e=this.formatter;return e?e(n,r):b(n,this.mask)},r.prototype.scale=function(r){var n=r;return(Object(e["isString"])(n)||Object(e["isDate"])(n))&&(n=this.translate(n)),t.prototype.scale.call(this,n)},r.prototype.translate=function(t){return y(t)},r.prototype.initCfg=function(){this.tickMethod="time-pretty",this.mask="YYYY-MM-DD",this.tickCount=7,this.nice=!1},r.prototype.setDomain=function(){var t=this.values,r=this.getConfig("min"),n=this.getConfig("max");if(Object(e["isNil"])(r)&&Object(e["isNumber"])(r)||(this.min=this.translate(this.min)),Object(e["isNil"])(n)&&Object(e["isNumber"])(n)||(this.max=this.translate(this.max)),t&&t.length){var o=[],i=1/0,a=i,u=0;Object(e["each"])(t,(function(t){var r=y(t);if(isNaN(r))throw new TypeError("Invalid Time: "+t+" in time scale!");i>r?(a=i,i=r):a>r&&(a=r),u<r&&(u=r),o.push(r)})),t.length>1&&(this.minTickInterval=a-i),Object(e["isNil"])(r)&&(this.min=i),Object(e["isNil"])(n)&&(this.max=u)}},r}(F),G=Y,q=function(t){function r(){var r=null!==t&&t.apply(this,arguments)||this;return r.type="quantize",r}return l(r,t),r.prototype.invert=function(t){var r=this.ticks,n=r.length,o=this.getInvertPercent(t),i=Math.floor(o*(n-1));if(i>=n-1)return Object(e["last"])(r);if(i<0)return Object(e["head"])(r);var a=r[i],u=r[i+1],c=i/(n-1),s=(i+1)/(n-1);return a+(o-c)/(s-c)*(u-a)},r.prototype.initCfg=function(){this.tickMethod="r-pretty",this.tickCount=5,this.nice=!0},r.prototype.calculateTicks=function(){var r=t.prototype.calculateTicks.call(this);return this.nice||(Object(e["last"])(r)!==this.max&&r.push(this.max),Object(e["head"])(r)!==this.min&&r.unshift(this.min)),r},r.prototype.getScalePercent=function(t){var r=this.ticks;if(t<Object(e["head"])(r))return 0;if(t>Object(e["last"])(r))return 1;var n=0;return Object(e["each"])(r,(function(r,e){if(!(t>=r))return!1;n=e})),n/(r.length-1)},r}(T),z=q,H=function(t){function r(){var r=null!==t&&t.apply(this,arguments)||this;return r.type="quantile",r}return l(r,t),r.prototype.initCfg=function(){this.tickMethod="quantile",this.tickCount=5,this.nice=!0},r}(z),W=H,V={};function $(t){return V[t]}function J(t,r){if($(t))throw new Error("type '"+t+"' existed.");V[t]=r}var Q=function(t){function r(){var r=null!==t&&t.apply(this,arguments)||this;return r.type="identity",r.isIdentity=!0,r}return l(r,t),r.prototype.calculateTicks=function(){return this.values},r.prototype.scale=function(t){return this.values[0]!==t&&Object(e["isNumber"])(t)?t:this.range[0]},r.prototype.invert=function(t){var r=this.range;return t<r[0]||t>r[1]?NaN:this.values[0]},r}(c),K=Q;function Z(t){var r=t.values,n=t.tickInterval,o=t.tickCount,i=t.showLast;if(Object(e["isNumber"])(n)){var a=Object(e["filter"])(r,(function(t,r){return r%n===0})),u=Object(e["last"])(r);return i&&Object(e["last"])(a)!==u&&a.push(u),a}var c=r.length,s=t.min,l=t.max;if(Object(e["isNil"])(s)&&(s=0),Object(e["isNil"])(l)&&(l=r.length-1),!Object(e["isNumber"])(o)||o>=c)return r.slice(s,l+1);if(o<=0||l<=0)return[];for(var f=1===o?c:Math.floor(c/(o-1)),h=[],p=s,d=0;d<o;d++){if(p>=l)break;p=Math.min(s+d*f,l),d===o-1&&i?h.push(r[l]):h.push(r[p])}return h}function X(t){var r=t.min,n=t.max,e=t.nice,o=t.tickCount,i=new ot;return i.domain([r,n]),e&&i.nice(o),i.ticks(o)}var tt=5,rt=Math.sqrt(50),nt=Math.sqrt(10),et=Math.sqrt(2),ot=function(){function t(){this._domain=[0,1]}return t.prototype.domain=function(t){return t?(this._domain=Array.from(t,Number),this):this._domain.slice()},t.prototype.nice=function(t){var r,n;void 0===t&&(t=tt);var e,o=this._domain.slice(),i=0,a=this._domain.length-1,u=this._domain[i],c=this._domain[a];return c<u&&(r=[c,u],u=r[0],c=r[1],n=[a,i],i=n[0],a=n[1]),e=at(u,c,t),e>0?(u=Math.floor(u/e)*e,c=Math.ceil(c/e)*e,e=at(u,c,t)):e<0&&(u=Math.ceil(u*e)/e,c=Math.floor(c*e)/e,e=at(u,c,t)),e>0?(o[i]=Math.floor(u/e)*e,o[a]=Math.ceil(c/e)*e,this.domain(o)):e<0&&(o[i]=Math.ceil(u*e)/e,o[a]=Math.floor(c*e)/e,this.domain(o)),this},t.prototype.ticks=function(t){return void 0===t&&(t=tt),it(this._domain[0],this._domain[this._domain.length-1],t||tt)},t}();function it(t,r,n){var e,o,i,a,u=-1;if(r=+r,t=+t,n=+n,t===r&&n>0)return[t];if((e=r<t)&&(o=t,t=r,r=o),0===(a=at(t,r,n))||!isFinite(a))return[];if(a>0){t=Math.ceil(t/a),r=Math.floor(r/a),i=new Array(o=Math.ceil(r-t+1));while(++u<o)i[u]=(t+u)*a}else{t=Math.floor(t*a),r=Math.ceil(r*a),i=new Array(o=Math.ceil(t-r+1));while(++u<o)i[u]=(t-u)/a}return e&&i.reverse(),i}function at(t,r,n){var e=(r-t)/Math.max(0,n),o=Math.floor(Math.log(e)/Math.LN10),i=e/Math.pow(10,o);return o>=0?(i>=rt?10:i>=nt?5:i>=et?2:1)*Math.pow(10,o):-Math.pow(10,-o)/(i>=rt?10:i>=nt?5:i>=et?2:1)}function ut(t,r,n){var e;return e="ceil"===n?Math.ceil(t/r):"floor"===n?Math.floor(t/r):Math.round(t/r),e*r}function ct(t,r,n){var o=ut(t,n,"floor"),i=ut(r,n,"ceil");o=Object(e["fixedBase"])(o,n),i=Object(e["fixedBase"])(i,n);for(var a=[],u=Math.max((i-o)/(Math.pow(2,12)-1),n),c=o;c<=i;c+=u){var s=Object(e["fixedBase"])(c,u);a.push(s)}return{min:o,max:i,ticks:a}}function st(t,r,n){var o,i=t.minLimit,a=t.maxLimit,u=t.min,c=t.max,s=t.tickCount,l=void 0===s?5:s,f=Object(e["isNil"])(i)?Object(e["isNil"])(r)?u:r:i,h=Object(e["isNil"])(a)?Object(e["isNil"])(n)?c:n:a;if(f>h&&(o=[f,h],h=o[0],f=o[1]),l<=2)return[f,h];for(var p=(h-f)/(l-1),d=[],v=0;v<l;v++)d.push(f+p*v);return d}function lt(t){var r=t.min,n=t.max,o=t.tickInterval,i=t.minLimit,a=t.maxLimit,u=X(t);return Object(e["isNil"])(i)&&Object(e["isNil"])(a)?o?ct(r,n,o).ticks:u:st(t,Object(e["head"])(u),Object(e["last"])(u))}function ft(t){return Math.abs(t)<1e-15?t:parseFloat(t.toFixed(15))}var ht=[1,5,2,2.5,4,3],pt=100*Number.EPSILON;function dt(t,r){return(t%r+r)%r}function vt(t){return Math.round(1e12*t)/1e12}function mt(t,r,n,o,i,a){var u=Object(e["size"])(r),c=Object(e["indexOf"])(r,t),s=0,l=dt(o,a);return(l<pt||a-l<pt)&&o<=0&&i>=0&&(s=1),1-c/(u-1)-n+s}function gt(t,r,n){var o=Object(e["size"])(r),i=Object(e["indexOf"])(r,t),a=1;return 1-i/(o-1)-n+a}function bt(t,r,n,e,o,i){var a=(t-1)/(i-o),u=(r-1)/(Math.max(i,e)-Math.min(n,o));return 2-Math.max(a/u,u/a)}function yt(t,r){return t>=r?2-(t-1)/(r-1):1}function xt(t,r,n,e){var o=r-t;return 1-.5*(Math.pow(r-e,2)+Math.pow(t-n,2))/Math.pow(.1*o,2)}function wt(t,r,n){var e=r-t;if(n>e){var o=(n-e)/2;return 1-Math.pow(o,2)/Math.pow(.1*e,2)}return 1}function Mt(){return 1}function Ot(t,r,n,o,i,a){void 0===n&&(n=5),void 0===o&&(o=!0),void 0===i&&(i=ht),void 0===a&&(a=[.25,.2,.5,.05]);var u=n<0?0:Math.round(n);if(Number.isNaN(t)||Number.isNaN(r)||"number"!==typeof t||"number"!==typeof r||!u)return{min:0,max:0,ticks:[]};if(r-t<1e-15||1===u)return{min:t,max:r,ticks:[t]};if(r-t>1e148){var c=n||5,s=(r-t)/c;return{min:t,max:r,ticks:Array(c).fill(null).map((function(r,n){return ft(t+s*n)}))}}var l={score:-2,lmin:0,lmax:0,lstep:0},f=1;while(f<1/0){for(var h=0;h<i.length;h+=1){var p=i[h],d=gt(p,i,f);if(a[0]*d+a[1]+a[2]+a[3]<l.score){f=1/0;break}var v=2;while(v<1/0){var m=yt(v,u);if(a[0]*d+a[1]+a[2]*m+a[3]<l.score)break;var g=(r-t)/(v+1)/f/p,b=Math.ceil(Math.log10(g));while(b<1/0){var y=f*p*Math.pow(10,b),x=wt(t,r,y*(v-1));if(a[0]*d+a[1]*x+a[2]*m+a[3]<l.score)break;var w=Math.floor(r/y)*f-(v-1)*f,M=Math.ceil(t/y)*f;if(w<=M){c=M-w;for(var O=0;O<=c;O+=1){var k=w+O,j=k*(y/f),_=j+y*(v-1),S=y,N=mt(p,i,f,j,_,S),C=xt(t,r,j,_),A=bt(v,u,t,r,j,_),T=Mt(),E=a[0]*N+a[1]*C+a[2]*A+a[3]*T;E>l.score&&(!o||j<=t&&_>=r)&&(l.lmin=j,l.lmax=_,l.lstep=S,l.score=E)}}b+=1}v+=1}}f+=1}var F=ft(l.lmax),I=ft(l.lmin),P=ft(l.lstep),R=Math.floor(vt((F-I)/P))+1,L=new Array(R);L[0]=ft(I);for(h=1;h<R;h++)L[h]=ft(L[h-1]+P);return{min:Math.min(t,Object(e["head"])(L)),max:Math.max(r,Object(e["last"])(L)),ticks:L}}function kt(t){var r=t.min,n=t.max,o=t.tickCount,i=t.nice,a=t.tickInterval,u=t.minLimit,c=t.maxLimit,s=Ot(r,n,o,i).ticks;return Object(e["isNil"])(u)&&Object(e["isNil"])(c)?a?ct(r,n,a).ticks:s:st(t,Object(e["head"])(s),Object(e["last"])(s))}function jt(t){var r,n=t.base,e=t.tickCount,o=t.min,i=t.max,a=t.values,u=P(n,i);if(o>0)r=Math.floor(P(n,o));else{var c=R(a,n,i);r=Math.floor(P(n,c))}for(var s=u-r,l=Math.ceil(s/e),f=[],h=r;h<u+l;h+=l)f.push(Math.pow(n,h));return o<=0&&f.unshift(0),f}function _t(t,r,n){if(void 0===n&&(n=5),t===r)return{max:r,min:t,ticks:[t]};var e=n<0?0:Math.round(n);if(0===e)return{max:r,min:t,ticks:[]};var o=1.5,i=.5+1.5*o,a=r-t,u=a/e,c=Math.pow(10,Math.floor(Math.log10(u))),s=c;2*c-u<o*(u-s)&&(s=2*c,5*c-u<i*(u-s)&&(s=5*c,10*c-u<o*(u-s)&&(s=10*c)));for(var l=Math.ceil(r/s),f=Math.floor(t/s),h=Math.max(l*s,r),p=Math.min(f*s,t),d=Math.floor((h-p)/s)+1,v=new Array(d),m=0;m<d;m++)v[m]=ft(p+m*s);return{min:p,max:h,ticks:v}}function St(t){var r=t.exponent,n=t.tickCount,e=Math.ceil(I(r,t.max)),o=Math.floor(I(r,t.min)),i=_t(o,e,n).ticks;return i.map((function(t){var n=t>=0?1:-1;return Math.pow(t,r)*n}))}function Nt(t,r){var n=t.length*r;return 1===r?t[t.length-1]:0===r?t[0]:n%1!==0?t[Math.ceil(n)-1]:t.length%2===0?(t[n-1]+t[n])/2:t[n]}function Ct(t){var r=t.tickCount,n=t.values;if(!n||!n.length)return[];for(var e=n.slice().sort((function(t,r){return t-r})),o=[],i=0;i<r;i++){var a=i/(r-1);o.push(Nt(e,a))}return o}function At(t){var r=t.min,n=t.max,o=t.tickCount,i=t.tickInterval,a=t.minLimit,u=t.maxLimit,c=_t(r,n,o).ticks;return Object(e["isNil"])(a)&&Object(e["isNil"])(u)?i?ct(r,n,i).ticks:c:st(t,Object(e["head"])(c),Object(e["last"])(c))}function Tt(t){var r=t.min,n=t.max,e=t.minTickInterval,o=t.tickInterval,i=t.tickCount;if(o)i=Math.ceil((n-r)/o);else{o=S(r,n,i)[1];var a=(n-r)/o,u=a/i;u>1&&(o*=Math.ceil(u)),e&&o<e&&(o=e)}o=Math.max(Math.floor((n-r)/(Math.pow(2,12)-1)),o);for(var c=[],s=r;s<n+o;s+=o)c.push(s);return c}function Et(t){var r=Z(f({showLast:!0},t));return r}function Ft(t){return new Date(t).getFullYear()}function It(t){return new Date(t,0,1).getTime()}function Pt(t){return new Date(t).getMonth()}function Rt(t,r){var n=Ft(t),e=Ft(r),o=Pt(t),i=Pt(r);return 12*(e-n)+(i-o)%12}function Lt(t,r){return new Date(t,r,1).getTime()}function Dt(t,r){return Math.ceil((r-t)/O)}function Ut(t,r){return Math.ceil((r-t)/M)}function Bt(t,r){return Math.ceil((r-t)/6e4)}function Yt(t){var r=t.min,n=t.max,e=t.minTickInterval,o=t.tickCount,i=t.tickInterval,a=[];i||(i=(n-r)/o,e&&i<e&&(i=e)),i=Math.max(Math.floor((n-r)/(Math.pow(2,12)-1)),i);var u=Ft(r);if(i>j)for(var c=Ft(n),s=Math.ceil(i/j),l=u;l<=c+s;l+=s)a.push(It(l));else if(i>k){var f=Math.ceil(i/k),h=Pt(r),p=Rt(r,n);for(l=0;l<=p+f;l+=f)a.push(Lt(u,l+h))}else if(i>O){var d=new Date(r),v=d.getFullYear(),m=d.getMonth(),g=d.getDate(),b=Math.ceil(i/O),y=Dt(r,n);for(l=0;l<y+b;l+=b)a.push(new Date(v,m,g+l).getTime())}else if(i>M){d=new Date(r),v=d.getFullYear(),m=d.getMonth(),b=d.getDate();var _=d.getHours(),S=Math.ceil(i/M),N=Ut(r,n);for(l=0;l<=N+S;l+=S)a.push(new Date(v,m,b,_+l).getTime())}else if(i>w){var C=Bt(r,n),A=Math.ceil(i/w);for(l=0;l<=C+A;l+=A)a.push(r+l*w)}else{var T=i;T<x&&(T=x);var E=Math.floor(r/x)*x,F=Math.ceil((n-r)/x),I=Math.ceil(T/x);for(l=0;l<F+I;l+=I)a.push(E+l*x)}return a.length>=512&&console.warn("Notice: current ticks length("+a.length+') >= 512, may cause performance issues, even out of memory. Because of the configure "tickInterval"(in milliseconds, current is '+i+") is too small, increase the value to solve the problem!"),a}a("cat",Z),a("time-cat",Et),a("wilkinson-extended",kt),a("r-pretty",At),a("time",Tt),a("time-pretty",Yt),a("log",jt),a("pow",St),a("quantile",Ct),a("d3-linear",lt),J("cat",d),J("category",d),J("identity",K),J("linear",F),J("log",D),J("pow",B),J("time",G),J("timeCat",C),J("quantize",z),J("quantile",W)}}]);