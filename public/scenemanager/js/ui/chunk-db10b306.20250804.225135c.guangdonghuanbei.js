(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-db10b306"],{2506:function(t,e,i){"use strict";i("a0e2")},4149:function(t,e,i){"use strict";i.r(e);var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("dialogComp",{attrs:{hideHeader:!1,needClose:"true",zIndex:"5",draw:!0,right:0,bottom:70,drag:!0,title:t.$t("menuIconName.interactive2D3D"),icon:"icon-details",width:360,height:360,type:"detailInfo",dragTopOffset:t.dragTopOffset,position:"fixed"},on:{close:t.closeDialog},scopedSlots:t._u([{key:"center",fn:function(){return[i("div",{staticClass:"model-tree-container"},[i("iframe",{attrs:{src:(t.isProduct?".":"")+"/iframe/interactive.html?projectID="+t.projectID+"&isProduct="+t.isProduct}})])]},proxy:!0}])})},o=[],c={name:"interactive2D3D",props:["interactiveData"],data:function(){return{parentDom:null,isProduct:!1,projectID:"",RotateParams:{pointA:{X:0,Y:0},pointB:{},pointC:{},count:0,anchorAngle:0,resultAngle:0,endMoveOffset:{x:0,y:0}}}},computed:{dragTopOffset:function(){return this.$store.state.scene.topToolBarHeight}},created:function(){this.projectID=this.$store.state.scene.currentSceneProjectID,window.interactiveData=this.interactiveData,this.isProduct=1,window.interactiveNotice=this.interactiveNotice},mounted:function(){},methods:{interactiveNotice:function(){this.$notify({title:this.$t("messageTips.tipsTitle"),message:this.$t("others._2dAnnotation")})},closeDialog:function(){delete window.interactiveNotice,delete window.interactiveData,this.$store.commit("toggleActiveDialog","interactive")}},beforeDestroy:function(){}},r=c,a=(i("2506"),i("90ae"),i("2877")),s=Object(a["a"])(r,n,o,!1,null,"75876de6",null);e["default"]=s.exports},"90ae":function(t,e,i){"use strict";i("d399")},a0e2:function(t,e,i){},d399:function(t,e,i){}}]);