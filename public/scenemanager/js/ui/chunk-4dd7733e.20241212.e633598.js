(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4dd7733e"],{"0208":function(t,e,i){},"325b":function(t,e,i){"use strict";i("f3ee")},"56b3":function(t,e,i){"use strict";i("0208")},e0be:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"filter-component-wrap special",style:{maxHeight:t.maxDialogHeight,top:t.dialogTop}},[i("div",{directives:[{name:"vDrag",rawName:"v-vDrag",value:{dragTopOffset:t.dragTopOffset},expression:"{dragTopOffset}"}],staticClass:"title"},[i("span",[t._v(t._s(t.$t("dialog.filterElement.label")))]),i("div",[i("i",{staticClass:"el-icon-close cursor-btn",on:{click:function(e){return t.closeDialog("batchExtrude")}}})])]),i("div",{directives:[{name:"loading",rawName:"v-loading",value:t.isLoading,expression:"isLoading"}],staticClass:"content",style:{maxHeight:t.maxContentHeight},attrs:{"element-loading-text":t.$t("dialog.filterElement.message"),"element-loading-spinner":"el-icon-loading","element-loading-background":"rgba(0, 0, 0, 0.8)"}},[t._l(t.datas,(function(e,a){return i("div",{key:"batch_extrude"+a,staticClass:"tab-div"},[i("div",{staticClass:"datas-top-bar"},[t.datas.length>1?i("div",{staticClass:"left-title"},[i("span",[t._v(t._s(t.$t("dialog.filterElement.label"))+t._s(a+1))])]):t._e(),t.datas.length>1?i("div",{staticClass:"right-menu"},[i("el-tooltip",{attrs:{enterable:!1,effect:"dark",content:t.$t("menuIconName.delete"),placement:"right"}},[i("span",{staticClass:"cursor-btn"},[i("CommonSVG",{attrs:{size:16,"icon-class":"delete"},nativeOn:{click:function(e){return e.stopPropagation(),t.removeBatchExtrudeOption(a)}}})],1)])],1):t._e()]),i("div",{staticClass:"data-item"},[i("el-select",{attrs:{filterable:"",size:"small",placeholder:t.$t("dialog.filterElement.placeholder")},on:{change:function(e){return t.changeCategory(e,a)}},model:{value:e.category,callback:function(i){t.$set(e,"category",i)},expression:"item.category"}},[i("div",{staticClass:"prefix-text",attrs:{slot:"prefix"},slot:"prefix"},[t._v(" "+t._s(t.$t("dialog.filterElement.label2"))+" ")]),t._l(t.selectData,(function(t,e){return i("el-option",{key:t.GroupName,attrs:{label:t.GroupName,value:e}})}))],2)],1),i("div",{staticClass:"data-item"},[i("el-select",{attrs:{filterable:"",size:"small",placeholder:t.$t("dialog.filterElement.placeholder1")},model:{value:e.peculiarity,callback:function(i){t.$set(e,"peculiarity",i)},expression:"item.peculiarity"}},[i("div",{staticClass:"prefix-text",attrs:{slot:"prefix"},slot:"prefix"},[t._v(" "+t._s(t.$t("dialog.filterElement.label3"))+" ")]),t._l(e.categoryDataOption,(function(t){return i("el-option",{key:t.Name,attrs:{label:t.Name,value:t.Name}})}))],2)],1),i("div",{staticClass:"data-item"},[i("el-select",{attrs:{size:"small",placeholder:t.$t("dialog.filterElement.placeholder2")},model:{value:e.conditions,callback:function(i){t.$set(e,"conditions",i)},expression:"item.conditions"}},[i("div",{staticClass:"prefix-text",attrs:{slot:"prefix"},slot:"prefix"},[t._v(" "+t._s(t.$t("dialog.filterElement.label4"))+" ")]),t._l(t.conditionsOptions,(function(t){return i("el-option",{key:t.label,attrs:{label:t.label,value:t.value}})}))],2)],1),i("div",{staticClass:"data-item"},[i("el-input",{attrs:{size:"small",type:"text",title:"",placeholder:t.$t("dialog.filterElement.placeholder3")},model:{value:e.conditionsValue,callback:function(i){t.$set(e,"conditionsValue",i)},expression:"item.conditionsValue"}},[i("div",{staticClass:"prefix-text",attrs:{slot:"prefix"},slot:"prefix"},[t._v(" "+t._s(t.$t("dialog.filterElement.label5"))+" ")])])],1)])})),i("div",{staticClass:"handle-wrap"},[i("span",{staticClass:"add-btn",on:{click:function(e){return t.addCurrentOption()}}},[i("i",{staticClass:"el-icon-plus"}),t._v(" "+t._s(t.$t("dialog.filterElement.label6"))+" ")]),i("span",{staticClass:"add-btn",on:{click:function(e){return t.resetData()}}},[i("CommonSVG",{staticClass:"margin-right-5",attrs:{"icon-class":"tb_remove_active",color:"var(--theme)",size:16}}),t._v(" "+t._s(this.$t("menuIconName.reset"))+" ")],1)]),i("div",{staticClass:"filter-element",on:{click:function(e){return t.openList()}}},[t._v(" "+t._s(t.$t("dialog.filterElement.label7"))+" ")])],2)])},n=[],o=i("c7eb"),l=i("1da1"),s=(i("d3b7"),i("3ca3"),i("ddb0"),i("159b"),i("a434"),i("caad"),i("2532"),{name:"filterCondition",components:{CommonSVG:function(){return i.e("chunk-51f90655").then(i.bind(null,"17d0"))}},data:function(){return{datas:[{category:"",peculiarity:"",conditions:"",categoryDataOption:[],conditionsValue:""}],maxDialogHeight:0,maxContentHeight:0,dialogTop:"50px",selectData:[],categoryData:[],isLoading:!0,conditionsOptions:[{label:"等于",value:"1"},{label:"不等于",value:"2"},{label:"大于",value:"3"},{label:"小于",value:"4"},{label:"包含",value:"5"},{label:"不包含",value:"6"}]}},created:function(){this.setConditionsOptions(),this.getPropertyNames(),this.setDialogSize(),window.addEventListener("resize",this.setDialogSize)},mounted:function(){},beforeDestroy:function(){window.removeEventListener("resize",this.setDialogSize)},computed:{activeDialog:function(){return this.$store.state.dialog.activeDialog},dragTopOffset:function(){return this.$store.state.scene.topToolBarHeight},currentFilterId:function(){return this.$store.state.menuList.currentFilterId},filterConditionData:function(){return this.$store.state.menuList.filterConditionData}},methods:{setConditionsOptions:function(){var t=this.$i18n.locale,e=this.$i18n.getLocaleMessage(t).dialog.filterElement.labels;this.conditionsOptions.forEach((function(t,i){t.label=e[i]}))},getPropertyNames:function(){var t=this;return Object(l["a"])(Object(o["a"])().mark((function e(){var i,a;return Object(o["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.isLoading=!0,i=window.scene.features.get(t.currentFilterId),e.next=4,t.$api.GetPropertyNames({VaultID:i.vaultID,ModelID:i.modelID});case 4:a=e.sent,t.selectData=a.data,t.filterConditionData.length&&(t.datas=t.filterConditionData),t.isLoading=!1;case 8:case"end":return e.stop()}}),e)})))()},changeCategory:function(t,e){this.datas[e].categoryDataOption=this.selectData[t].PropertyNames},setDialogSize:function(){var t=document.body.clientHeight,e=0,i=document.querySelector(".topToolBarContainer");i&&(this.dialogTop="130px",e=i.clientHeight);var a=t-e-40;this.maxDialogHeight=a+"px",this.maxContentHeight=a-57+"px"},closeDialog:function(){this.$store.commit("toggleActiveDialog","filterCondition")},addCurrentOption:function(t){this.datas.push({category:"",peculiarity:"",conditions:"",categoryDataOption:[],conditionsValue:""})},resetData:function(){this.datas=[{category:"",peculiarity:"",conditions:"",categoryDataOption:[],conditionsValue:""}]},removeBatchExtrudeOption:function(t){this.datas.splice(t,1)},openList:function(){var t=0,e=0;this.datas.forEach((function(i){""===i.category&&e++,""!==i.category&&""!==i.peculiarity&&""!==i.conditions&&""!==i.conditionsValue||t++})),e>0?this.$message.warning(this.$t("dialog.filterElement.placeholder")):t>0?this.$message.warning(this.$t("dialog.filterElement.message1")):(this.activeDialog.includes("filterConditionList")?this.$emit("searchElement"):(this.$store.commit("toggleActiveDialog","filterConditionList"),this.$store.commit("setFilterConditionData",this.datas)),this.$store.commit("toggleActiveDialog","filterCondition"))}}}),r=s,c=(i("56b3"),i("325b"),i("2877")),d=Object(c["a"])(r,a,n,!1,null,"6930daa5",null);e["default"]=d.exports},f3ee:function(t,e,i){}}]);