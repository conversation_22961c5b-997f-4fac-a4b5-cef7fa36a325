(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-362c7540"],{"08ec":function(t,i,n){"use strict";n("d970")},"26a6":function(t,i,n){"use strict";n("6257")},6257:function(t,i,n){},ca7f:function(t,i,n){"use strict";n.r(i);var o=function(){var t=this,i=t.$createElement,n=t._self._c||i;return n("dialogComp",{attrs:{hideHeader:!1,needClose:!1,zIndex:"5",draw:!0,right:100,top:180,drag:!0,title:t.editPathAnimation?t.$t("formRelational.basePoint.label1"):t.$t("topToolBarMenu.advanced.children.pathAnimation.label"),icon:"icon-details",width:174,height:210,type:"detailInfo",backgroundColor:"var(--primary-bg-color)",position:"fixed"},on:{close:t.closeDialog},scopedSlots:t._u([{key:"center",fn:function(){return[n("div",{staticClass:"position-list path-animation-wrap"},[n("div",{staticClass:"item"},[n("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.pathAnimationPosition.x,expression:"pathAnimationPosition.x"}],attrs:{type:"number",size:"mini"},on:{input:function(i){return t.changePosition(i,"x")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[n("span",[t._v("X")])]},proxy:!0}]),model:{value:t.pathAnimationPosition.x,callback:function(i){t.$set(t.pathAnimationPosition,"x",i)},expression:"pathAnimationPosition.x"}})],1),n("div",{staticClass:"item"},[n("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.pathAnimationPosition.y,expression:"pathAnimationPosition.y"}],attrs:{type:"number",size:"mini"},on:{input:function(i){return t.changePosition(i,"y")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v("Y")]},proxy:!0}]),model:{value:t.pathAnimationPosition.y,callback:function(i){t.$set(t.pathAnimationPosition,"y",i)},expression:"pathAnimationPosition.y"}})],1),n("div",{staticClass:"item"},[n("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.pathAnimationPosition.z,expression:"pathAnimationPosition.z"}],attrs:{type:"number",size:"mini"},on:{input:function(i){return t.changePosition(i,"z")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v("Z")]},proxy:!0}]),model:{value:t.pathAnimationPosition.z,callback:function(i){t.$set(t.pathAnimationPosition,"z",i)},expression:"pathAnimationPosition.z"}})],1),n("div",{staticClass:"dialog-footer"},[t.editPathAnimation?n("div",{staticClass:"btn del-btn",on:{click:t.deleteCoor}},[t._v(" "+t._s(t.$t("messageTips.delete"))+" ")]):t._e(),n("div",{staticClass:"btn confirm-btn",on:{click:t.confirmCoor}},[t._v(" "+t._s(t.$t("menuIconName.confirm"))+" ")])])])]},proxy:!0}])})},e=[],a=n("ade3"),s=(n("a9e3"),n("a434"),n("f7fe")),r=n.n(s),l={name:"",data:function(){return{pos:[]}},computed:{pathAnimationPosition:{get:function(){return this.$store.state.pathAnimation.pathAnimationPosition}},editPathAnimation:function(){return this.$store.state.pathAnimation.editPathAnimation},transFormStatus:function(){return this.$store.state.pathAnimation.transFormStatus}},created:function(){this.changePosition=r()(this.changePosition,1e3)},beforeDestroy:function(){this.$store.commit("setPathAnimationPosition",{x:0,y:0,z:0})},methods:{changePosition:function(t,i){var n=this.pathAnimationPosition;this.$store.commit("setPathAnimationPosition",Object.assign({},n,Object(a["a"])({},i,Number(t))))},deleteCoor:function(){if(this.editPathAnimation){var t=window.scene.mv.tools.draw.getAllData(),i=window.scene.mv.tools.draw.currentPointIndex;if(t.polyline.length){for(var n=t.polyline[0].points.length,o=[],e=0;e<n;e++)o[e]=[t.polyline[0].points[e].x,t.polyline[0].points[e].y,t.polyline[0].points[e].z];o.splice(i,1),window.scene.mv.tools.draw.startDrawLine(o)}scene.mv.tools.draw.editPoint(),window.scene.render(),this.transFormStatus&&(window.scene.mv.tools.transform.deactive(),this.$store.commit("toogleTransFormStatus",!1));var a=this.$store.state.dialog.activeDialog;-1!==a.indexOf("pathAnimation")&&this.$store.commit("toggleActiveDialog","pathAnimation"),this.$store.commit("toogleTransFormStatus",!1),this.$store.commit("toogleEditPathAnimation",!1),this.$store.commit("setPathAnimationPosition",[0,0,0])}},confirmCoor:function(){if(this.transFormStatus&&(window.scene.mv.tools.transform.deactive(),this.$store.commit("toogleTransFormStatus",!1)),this.editPathAnimation){for(var t=window.scene.mv.tools.draw.getAllData(),i=window.scene.mv.tools.draw.currentPointIndex,n=t.polyline[0].points.length,o=[],e=0;e<n;e++)o[e]=[t.polyline[0].points[e].x,t.polyline[0].points[e].y,t.polyline[0].points[e].z];var a=this.pathAnimationPosition;o[i]=[a.x,a.y,a.z],window.scene.mv.tools.draw.startDrawLine(o),scene.mv.tools.draw.editPoint(),window.scene.render(),this.$store.commit("toogleEditPathAnimation",!1);var s=this.$store.state.dialog.activeDialog;-1!==s.indexOf("pathAnimation")&&this.$store.commit("toggleActiveDialog","pathAnimation")}else{var r=this.pathAnimationPosition;window.scene.mv.tools.draw.addPoint([r.x,r.y,r.z])}}}},c=l,m=(n("08ec"),n("26a6"),n("2877")),d=Object(m["a"])(c,o,e,!1,null,"614e6d2b",null);i["default"]=d.exports},d970:function(t,i,n){}}]);