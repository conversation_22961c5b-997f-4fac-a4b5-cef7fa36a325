(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-05a08cbe"],{"0376":function(e,t,n){"use strict";var r=this&&this.__extends||function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!==typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),o=this&&this.__awaiter||function(e,t,n,r){function o(e){return e instanceof n?e:new n((function(t){t(e)}))}return new(n||(n=Promise))((function(n,i){function a(e){try{c(r.next(e))}catch(t){i(t)}}function u(e){try{c(r["throw"](e))}catch(t){i(t)}}function c(e){e.done?n(e.value):o(e.value).then(a,u)}c((r=r.apply(e,t||[])).next())}))},i=this&&this.__generator||function(e,t){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:u(0),throw:u(1),return:u(2)},"function"===typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function u(e){return function(t){return c([e,t])}}function c(i){if(n)throw new TypeError("Generator is already executing.");while(a)try{if(n=1,r&&(o=2&i[0]?r["return"]:i[0]?r["throw"]||((o=r["return"])&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,r=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(o=a.trys,!(o=o.length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(u){i=[6,u],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}};Object.defineProperty(t,"__esModule",{value:!0}),t.GForceGPULayout=void 0;var a=n("5bc9"),u=n("f271"),c=n("a196"),s=n("6299"),l=n("19d2"),d=n("b3dc"),f=n("3888"),v=function(e){function t(t){var n=e.call(this)||this;return n.maxIteration=1e3,n.edgeStrength=200,n.nodeStrength=1e3,n.coulombDisScale=.005,n.damping=.9,n.maxSpeed=1e3,n.minMovement=.5,n.interval=.02,n.factor=1,n.linkDistance=1,n.gravity=10,n.workerEnabled=!1,n.nodes=[],n.edges=[],n.width=300,n.height=300,n.nodeMap={},n.nodeIdxMap={},n.updateCfg(t),n}return r(t,e),t.prototype.getDefaultCfg=function(){return{maxIteration:2e3,gravity:10,clustering:!1,clusterGravity:10}},t.prototype.execute=function(){return o(this,void 0,void 0,(function(){var e,t,n,r,o;return i(this,(function(i){switch(i.label){case 0:return e=this,t=e.nodes,t&&0!==t.length?(e.width||"undefined"===typeof window||(e.width=window.innerWidth),e.height||"undefined"===typeof window||(e.height=window.innerHeight),e.center||(e.center=[e.width/2,e.height/2]),n=e.center,1===t.length?(t[0].x=n[0],t[0].y=n[1],e.onLayoutEnd&&e.onLayoutEnd(),[2]):(r={},o={},t.forEach((function(t,n){(0,u.isNumber)(t.x)||(t.x=Math.random()*e.width),(0,u.isNumber)(t.y)||(t.y=Math.random()*e.height),r[t.id]=t,o[t.id]=n})),e.nodeMap=r,e.nodeIdxMap=o,e.nodeStrength=(0,s.proccessToFunc)(e.nodeStrength,1),e.edgeStrength=(0,s.proccessToFunc)(e.edgeStrength,1),[4,e.run()])):(e.onLayoutEnd&&e.onLayoutEnd(),[2]);case 1:return i.sent(),[2]}}))}))},t.prototype.executeWithWorker=function(e,t){var n=this,r=n.nodes,o=n.center;if(r&&0!==r.length){if(1===r.length)return r[0].x=o[0],void(r[0].y=o[1]);var i={},a={};r.forEach((function(e,t){(0,u.isNumber)(e.x)||(e.x=Math.random()*n.width),(0,u.isNumber)(e.y)||(e.y=Math.random()*n.height),i[e.id]=e,a[e.id]=t})),n.nodeMap=i,n.nodeIdxMap=a,n.nodeStrength=(0,s.proccessToFunc)(n.nodeStrength,1),n.edgeStrength=(0,s.proccessToFunc)(n.edgeStrength,1),n.run(e,t)}},t.prototype.run=function(e,t){return o(this,void 0,void 0,(function(){var n,r,a,v,p,y,g,h,_,x,m,b,D,I,w,G,S,k,N,z,E,P,M,C,T,U,O,A=this;return i(this,(function(W){switch(W.label){case 0:for(n=this,r=n.nodes,a=n.edges,v=n.maxIteration,n.width||"undefined"===typeof window||(n.width=window.innerWidth),n.height||"undefined"===typeof window||(n.height=window.innerHeight),p=r.length,n.linkDistance=(0,s.proccessToFunc)(n.linkDistance),n.edgeStrength=(0,s.proccessToFunc)(n.edgeStrength),y=(0,s.buildTextureDataWithTwoEdgeAttr)(r,a,n.linkDistance,n.edgeStrength),g=y.maxEdgePerVetex,h=y.array,n.degrees=(0,l.getDegree)(r.length,n.nodeIdxMap,a),_=[],x=[],m=[],b=[],D=[],I=[],w=[],n.getMass||(n.getMass=function(e){return n.degrees[n.nodeIdxMap[e.id]]||1}),G=n.gravity,S=n.center,r.forEach((function(e,t){_.push(n.getMass(e)),x.push(n.nodeStrength(e)),n.degrees[t]||(n.degrees[t]=0);var r=[S[0],S[1],G];if(n.getCenter){var o=n.getCenter(e,n.degrees[t]);o&&(0,u.isNumber)(o[0])&&(0,u.isNumber)(o[1])&&(0,u.isNumber)(o[2])&&(r=o)}m.push(r[0]),b.push(r[1]),D.push(r[2]),(0,u.isNumber)(e.fx)&&(0,u.isNumber)(e.fy)?(I.push(e.fx||.001),w.push(e.fy||.001)):(I.push(0),w.push(0))})),k=(0,s.arrayToTextureData)([_,n.degrees,x,I]),N=(0,s.arrayToTextureData)([m,b,D,w]),z=n.workerEnabled,E=z?c.World.create({canvas:e,engineOptions:{supportCompute:!0}}):c.World.create({engineOptions:{supportCompute:!0}}),P=n.onLayoutEnd,M=[],h.forEach((function(e){M.push(e)})),C=0;C<4;C++)M.push(0);return T=E.createKernel(d.gForceBundle).setDispatch([p,1,1]).setBinding({u_Data:h,u_damping:n.damping,u_maxSpeed:n.maxSpeed,u_minMovement:n.minMovement,u_coulombDisScale:n.coulombDisScale,u_factor:n.factor,u_NodeAttributeArray1:k,u_NodeAttributeArray2:N,MAX_EDGE_PER_VERTEX:g,VERTEX_COUNT:p,u_AveMovement:M,u_interval:n.interval}),U=E.createKernel(d.aveMovementBundle).setDispatch([1,1,1]).setBinding({u_Data:h,VERTEX_COUNT:p,u_AveMovement:[0,0,0,0]}),O=function(){return o(A,void 0,void 0,(function(){var o,a,u;return i(this,(function(i){switch(i.label){case 0:o=0,i.label=1;case 1:return o<v?[4,T.execute()]:[3,5];case 2:return i.sent(),U.setBinding({u_Data:T}),[4,U.execute()];case 3:i.sent(),a=Math.max(.02,n.interval-.002*o),T.setBinding({u_interval:a,u_AveMovement:U}),i.label=4;case 4:return o++,[3,1];case 5:return[4,T.getOutput()];case 6:return u=i.sent(),e?t.postMessage({type:f.LAYOUT_MESSAGE.GPUEND,vertexEdgeData:u}):r.forEach((function(e,t){var n=u[4*t],r=u[4*t+1];e.x=n,e.y=r})),P&&P(),[2]}}))}))},[4,O()];case 1:return W.sent(),[2]}}))}))},t.prototype.getType=function(){return"gForce-gpu"},t}(a.Base);t.GForceGPULayout=v},"0717":function(e,t,n){"use strict";var r=this&&this.__extends||function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!==typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),o=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.ForceAtlas2Layout=void 0;var i=n("5bc9"),a=n("f271"),u=o(n("faf9")),c=o(n("431f")),s=o(n("871d")),l=function(e){function t(t){var n=e.call(this)||this;return n.center=[0,0],n.width=300,n.height=300,n.nodes=[],n.edges=[],n.kr=5,n.kg=1,n.mode="normal",n.preventOverlap=!1,n.dissuadeHubs=!1,n.barnesHut=void 0,n.maxIteration=0,n.ks=.1,n.ksmax=10,n.tao=.1,n.onLayoutEnd=function(){},n.prune=void 0,n.updateCfg(t),n}return r(t,e),t.prototype.getDefaultCfg=function(){return{}},t.prototype.execute=function(){var e=this,t=e.nodes,n=e.onLayoutEnd,r=e.prune,o=e.maxIteration;e.width||"undefined"===typeof window||(e.width=window.innerWidth),e.height||"undefined"===typeof window||(e.height=window.innerHeight);for(var i=[],u=t.length,c=0;c<u;c+=1){var s=t[c],l=10,d=10;(0,a.isNumber)(s.size)&&(l=s.size,d=s.size),(0,a.isArray)(s.size)?(isNaN(s.size[0])||(l=s.size[0]),isNaN(s.size[1])||(d=s.size[1])):(0,a.isObject)(s.size)&&(l=s.size.width,d=s.size.height),e.getWidth&&!isNaN(e.getWidth(s))&&(d=e.getWidth(s)),e.getHeight&&!isNaN(e.getHeight(s))&&(l=e.getHeight(s));var f=Math.max(l,d);i.push(f)}void 0===e.barnesHut&&u>250&&(e.barnesHut=!0),void 0===e.prune&&u>100&&(e.prune=!0),0!==this.maxIteration||e.prune?0===this.maxIteration&&r&&(o=100,u<=200&&u>100?o=500:u>200&&(o=950),this.maxIteration=o):(o=250,u<=200&&u>100?o=1e3:u>200&&(o=1200),this.maxIteration=o),e.kr||(e.kr=50,u>100&&u<=500?e.kr=20:u>500&&(e.kr=1)),e.kg||(e.kg=20,u>100&&u<=500?e.kg=10:u>500&&(e.kg=1)),this.nodes=e.updateNodesByForces(i),n()},t.prototype.updateNodesByForces=function(e){for(var t=this,n=t.edges,r=t.maxIteration,o=t.nodes,i=n.filter((function(e){var t=(0,a.getEdgeTerminal)(e,"source"),n=(0,a.getEdgeTerminal)(e,"target");return t!==n})),u=o.length,c=i.length,s=[],l={},d={},f=[],v=0;v<u;v+=1)l[o[v].id]=v,s[v]=0,(void 0===o[v].x||isNaN(o[v].x))&&(o[v].x=1e3*Math.random()),(void 0===o[v].y||isNaN(o[v].y))&&(o[v].y=1e3*Math.random()),f.push({x:o[v].x,y:o[v].y});for(v=0;v<c;v+=1){for(var p=void 0,y=void 0,g=0,h=0,_=0;_<u;_+=1){var x=(0,a.getEdgeTerminal)(i[v],"source"),m=(0,a.getEdgeTerminal)(i[v],"target");o[_].id===x?(p=o[_],g=_):o[_].id===m&&(y=o[_],h=_),d[v]={sourceIdx:g,targetIdx:h}}p&&(s[l[p.id]]+=1),y&&(s[l[y.id]]+=1)}var b=r;if(o=this.iterate(b,l,d,c,s,e),t.prune){for(_=0;_<c;_+=1)s[d[_].sourceIdx]<=1?(o[d[_].sourceIdx].x=o[d[_].targetIdx].x,o[d[_].sourceIdx].y=o[d[_].targetIdx].y):s[d[_].targetIdx]<=1&&(o[d[_].targetIdx].x=o[d[_].sourceIdx].x,o[d[_].targetIdx].y=o[d[_].sourceIdx].y);t.prune=!1,t.barnesHut=!1,b=100,o=this.iterate(b,l,d,c,s,e)}return o},t.prototype.iterate=function(e,t,n,r,o,i){for(var a=this,c=a.nodes,s=a.kr,l=a.preventOverlap,d=a.barnesHut,f=c.length,v=0,p=100,y=e,g=50,h=[],_=[],x=[],m=0;m<f;m+=1)if(h[2*m]=0,h[2*m+1]=0,d){var b={id:m,rx:c[m].x,ry:c[m].y,mass:1,g:s,degree:o[m]};x[m]=new u.default(b)}while(y>0){for(m=0;m<f;m+=1)_[2*m]=h[2*m],_[2*m+1]=h[2*m+1],h[2*m]=0,h[2*m+1]=0;h=this.getAttrForces(y,g,r,t,n,o,i,h),h=d&&(l&&y>g||!l)?this.getOptRepGraForces(h,x,o):this.getRepGraForces(y,g,h,p,i,o);var D=this.updatePos(h,_,v,o);c=D.nodes,v=D.sg,y--,a.tick&&a.tick()}return c},t.prototype.getAttrForces=function(e,t,n,r,o,i,a,u){for(var c=this,s=c.nodes,l=c.preventOverlap,d=c.dissuadeHubs,f=c.mode,v=c.prune,p=0;p<n;p+=1){var y=s[o[p].sourceIdx],g=o[p].sourceIdx,h=s[o[p].targetIdx],_=o[p].targetIdx;if(!v||!(i[g]<=1||i[_]<=1)){var x=[h.x-y.x,h.y-y.y],m=Math.hypot(x[0],x[1]);m=m<1e-4?1e-4:m,x[0]=x[0]/m,x[1]=x[1]/m,l&&e<t&&(m=m-a[g]-a[_]);var b=m,D=b;"linlog"===f&&(b=Math.log(1+m),D=b),d&&(b=m/i[g],D=m/i[_]),l&&e<t&&m<=0?(b=0,D=0):l&&e<t&&m>0&&(b=m,D=m),u[2*r[y.id]]+=b*x[0],u[2*r[h.id]]-=D*x[0],u[2*r[y.id]+1]+=b*x[1],u[2*r[h.id]+1]-=D*x[1]}}return u},t.prototype.getRepGraForces=function(e,t,n,r,o,i){for(var a=this,u=a.nodes,c=a.preventOverlap,s=a.kr,l=a.kg,d=a.center,f=a.prune,v=u.length,p=0;p<v;p+=1){for(var y=p+1;y<v;y+=1)if(!f||!(i[p]<=1||i[y]<=1)){var g=[u[y].x-u[p].x,u[y].y-u[p].y],h=Math.hypot(g[0],g[1]);h=h<1e-4?1e-4:h,g[0]=g[0]/h,g[1]=g[1]/h,c&&e<t&&(h=h-o[p]-o[y]);var _=s*(i[p]+1)*(i[y]+1)/h;c&&e<t&&h<0?_=r*(i[p]+1)*(i[y]+1):c&&e<t&&0===h?_=0:c&&e<t&&h>0&&(_=s*(i[p]+1)*(i[y]+1)/h),n[2*p]-=_*g[0],n[2*y]+=_*g[0],n[2*p+1]-=_*g[1],n[2*y+1]+=_*g[1]}var x=[u[p].x-d[0],u[p].y-d[1]],m=Math.hypot(x[0],x[1]);x[0]=x[0]/m,x[1]=x[1]/m;var b=l*(i[p]+1);n[2*p]-=b*x[0],n[2*p+1]-=b*x[1]}return n},t.prototype.getOptRepGraForces=function(e,t,n){for(var r=this,o=r.nodes,i=r.kg,a=r.center,u=r.prune,l=o.length,d=9e10,f=-9e10,v=9e10,p=-9e10,y=0;y<l;y+=1)u&&n[y]<=1||(t[y].setPos(o[y].x,o[y].y),o[y].x>=f&&(f=o[y].x),o[y].x<=d&&(d=o[y].x),o[y].y>=p&&(p=o[y].y),o[y].y<=v&&(v=o[y].y));var g=Math.max(f-d,p-v),h={xmid:(f+d)/2,ymid:(p+v)/2,length:g,massCenter:a,mass:l},_=new c.default(h),x=new s.default(_);for(y=0;y<l;y+=1)u&&n[y]<=1||t[y].in(_)&&x.insert(t[y]);for(y=0;y<l;y+=1)if(!(u&&n[y]<=1)){t[y].resetForce(),x.updateForce(t[y]),e[2*y]-=t[y].fx,e[2*y+1]-=t[y].fy;var m=[o[y].x-a[0],o[y].y-a[1]],b=Math.hypot(m[0],m[1]);b=b<1e-4?1e-4:b,m[0]=m[0]/b,m[1]=m[1]/b;var D=i*(n[y]+1);e[2*y]-=D*m[0],e[2*y+1]-=D*m[1]}return e},t.prototype.updatePos=function(e,t,n,r){for(var o=this,i=o.nodes,a=o.ks,u=o.tao,c=o.prune,s=o.ksmax,l=i.length,d=[],f=[],v=0,p=0,y=0;y<l;y+=1)if(!(c&&r[y]<=1)){var g=[e[2*y]-t[2*y],e[2*y+1]-t[2*y+1]],h=Math.hypot(g[0],g[1]),_=[e[2*y]+t[2*y],e[2*y+1]+t[2*y+1]],x=Math.hypot(_[0],_[1]);d[y]=h,f[y]=x/2,v+=(r[y]+1)*d[y],p+=(r[y]+1)*f[y]}var m=n;n=u*p/v,0!==m&&(n=n>1.5*m?1.5*m:n);for(y=0;y<l;y+=1)if(!(c&&r[y]<=1)){var b=a*n/(1+n*Math.sqrt(d[y])),D=Math.hypot(e[2*y],e[2*y+1]);D=D<1e-4?1e-4:D;var I=s/D;b=b>I?I:b;var w=b*e[2*y],G=b*e[2*y+1];i[y].x+=w,i[y].y+=G}return{nodes:i,sg:n}},t}(i.Base);t.ForceAtlas2Layout=l},"077d":function(e,t,n){"use strict";var r=this&&this.__extends||function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!==typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),o=this&&this.__awaiter||function(e,t,n,r){function o(e){return e instanceof n?e:new n((function(t){t(e)}))}return new(n||(n=Promise))((function(n,i){function a(e){try{c(r.next(e))}catch(t){i(t)}}function u(e){try{c(r["throw"](e))}catch(t){i(t)}}function c(e){e.done?n(e.value):o(e.value).then(a,u)}c((r=r.apply(e,t||[])).next())}))},i=this&&this.__generator||function(e,t){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:u(0),throw:u(1),return:u(2)},"function"===typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function u(e){return function(t){return c([e,t])}}function c(i){if(n)throw new TypeError("Generator is already executing.");while(a)try{if(n=1,r&&(o=2&i[0]?r["return"]:i[0]?r["throw"]||((o=r["return"])&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,r=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(o=a.trys,!(o=o.length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(u){i=[6,u],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}};Object.defineProperty(t,"__esModule",{value:!0}),t.FruchtermanGPULayout=void 0;var a=n("5bc9"),u=n("f271"),c=n("a196"),s=n("6299"),l=n("18fc"),d=n("3888"),f=function(e){function t(t){var n=e.call(this)||this;return n.maxIteration=1e3,n.gravity=10,n.speed=1,n.clustering=!1,n.clusterField="cluster",n.clusterGravity=10,n.workerEnabled=!1,n.nodes=[],n.edges=[],n.width=300,n.height=300,n.nodeMap={},n.nodeIdxMap={},n.updateCfg(t),n}return r(t,e),t.prototype.getDefaultCfg=function(){return{maxIteration:1e3,gravity:10,speed:1,clustering:!1,clusterGravity:10}},t.prototype.execute=function(){return o(this,void 0,void 0,(function(){var e,t,n,r,o,a=this;return i(this,(function(i){switch(i.label){case 0:return e=this,t=e.nodes,t&&0!==t.length?(e.width||"undefined"===typeof window||(e.width=window.innerWidth),e.height||"undefined"===typeof window||(e.height=window.innerHeight),e.center||(e.center=[e.width/2,e.height/2]),n=e.center,1===t.length?(t[0].x=n[0],t[0].y=n[1],e.onLayoutEnd&&e.onLayoutEnd(),[2]):(r={},o={},t.forEach((function(e,t){(0,u.isNumber)(e.x)||(e.x=Math.random()*a.width),(0,u.isNumber)(e.y)||(e.y=Math.random()*a.height),r[e.id]=e,o[e.id]=t})),e.nodeMap=r,e.nodeIdxMap=o,[4,e.run()])):(e.onLayoutEnd&&e.onLayoutEnd(),[2]);case 1:return i.sent(),[2]}}))}))},t.prototype.executeWithWorker=function(e,t){return o(this,void 0,void 0,(function(){var n,r,o,a,c,s=this;return i(this,(function(i){switch(i.label){case 0:return n=this,r=n.nodes,o=n.center,r&&0!==r.length?1===r.length?(r[0].x=o[0],r[0].y=o[1],[2]):(a={},c={},r.forEach((function(e,t){(0,u.isNumber)(e.x)||(e.x=Math.random()*s.width),(0,u.isNumber)(e.y)||(e.y=Math.random()*s.height),a[e.id]=e,c[e.id]=t})),n.nodeMap=a,n.nodeIdxMap=c,[4,n.run(e,t)]):[2];case 1:return i.sent(),[2]}}))}))},t.prototype.run=function(e,t){return o(this,void 0,void 0,(function(){var n,r,a,f,v,p,y,g,h,_,x,m,b,D,I,w,G,S,k,N,z,E,P,M,C,T,U=this;return i(this,(function(O){switch(O.label){case 0:for(n=this,r=n.nodes,a=n.edges,f=n.maxIteration,v=n.center,p=n.height*n.width,y=Math.sqrt(p)/10,g=p/(r.length+1),h=Math.sqrt(g),_=n.speed,x=n.clustering,m=(0,s.attributesToTextureData)([n.clusterField],r),b=m.array,D=m.count,r.forEach((function(e,t){var n=0,r=0;(0,u.isNumber)(e.fx)&&(0,u.isNumber)(e.fy)&&(n=e.fx||.001,r=e.fy||.001),b[4*t+1]=n,b[4*t+2]=r})),I=r.length,w=(0,s.buildTextureData)(r,a),G=w.maxEdgePerVetex,S=w.array,k=n.workerEnabled,N=k?c.World.create({canvas:e,engineOptions:{supportCompute:!0}}):c.World.create({engineOptions:{supportCompute:!0}}),z=n.onLayoutEnd,E=[],P=0;P<D;P++)E.push(0,0,0,0);return M=N.createKernel(l.fruchtermanBundle).setDispatch([I,1,1]).setBinding({u_Data:S,u_K:h,u_K2:g,u_Gravity:n.gravity,u_ClusterGravity:n.clusterGravity||n.gravity||1,u_Speed:_,u_MaxDisplace:y,u_Clustering:x?1:0,u_Center:v,u_AttributeArray:b,u_ClusterCenters:E,MAX_EDGE_PER_VERTEX:G,VERTEX_COUNT:I}),x&&(C=N.createKernel(l.clusterBundle).setDispatch([D,1,1]).setBinding({u_Data:S,u_NodeAttributes:b,u_ClusterCenters:E,VERTEX_COUNT:I,CLUSTER_COUNT:D})),T=function(){return o(U,void 0,void 0,(function(){var n,o;return i(this,(function(i){switch(i.label){case 0:n=0,i.label=1;case 1:return n<f?[4,M.execute()]:[3,6];case 2:return i.sent(),x?(C.setBinding({u_Data:M}),[4,C.execute()]):[3,4];case 3:i.sent(),M.setBinding({u_ClusterCenters:C}),i.label=4;case 4:M.setBinding({u_MaxDisplace:y*=.99}),i.label=5;case 5:return n++,[3,1];case 6:return[4,M.getOutput()];case 7:return o=i.sent(),e?t.postMessage({type:d.LAYOUT_MESSAGE.GPUEND,vertexEdgeData:o}):r.forEach((function(e,t){var n=o[4*t],r=o[4*t+1];e.x=n,e.y=r})),z&&z(),[2]}}))}))},[4,T()];case 1:return O.sent(),[2]}}))}))},t.prototype.getType=function(){return"fruchterman-gpu"},t}(a.Base);t.FruchtermanGPULayout=f},1231:function(e,t,n){"use strict";var r=this&&this.__assign||function(){return r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n],t)Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},r.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(){this.cells=[],this.columnNum=0,this.rowNum=0,this.additionColumn=[],this.additionRow=[]}return e.prototype.init=function(t,n,r){this.cells=[],this.CELL_W=r.CELL_W||e.DEFAULT_CELL_W,this.CELL_H=r.CELL_H||e.DEFAULT_CELL_H,this.columnNum=Math.ceil(t/this.CELL_W),this.rowNum=Math.ceil(n/this.CELL_H),e.MIN_DIST=Math.pow(t,2)+Math.pow(n,2);for(var o=0;o<this.columnNum;o++){for(var i=[],a=0;a<this.rowNum;a++){var u={dx:o,dy:a,x:o*this.CELL_W,y:a*this.CELL_H,occupied:!1};i.push(u)}this.cells.push(i)}},e.prototype.findGridByNodeId=function(e){for(var t,n,r=0;r<this.columnNum;r++)for(var o=0;o<this.rowNum;o++)if(this.cells[r][o].node&&(null===(n=null===(t=this.cells[r][o])||void 0===t?void 0:t.node)||void 0===n?void 0:n.id)===e)return{column:r,row:o};return null},e.prototype.sqdist=function(e,t){return Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2)},e.prototype.occupyNearest=function(t){for(var n,r=e.MIN_DIST,o=null,i=0;i<this.columnNum;i++)for(var a=0;a<this.rowNum;a++)!this.cells[i][a].occupied&&(n=this.sqdist(t,this.cells[i][a]))<r&&(r=n,o=this.cells[i][a]);return o&&(o.occupied=!0),o},e.prototype.insertColumn=function(e,t){if(!(t<=0)){for(var n=0;n<t;n++){this.cells[n+this.columnNum]=[];for(var o=0;o<this.rowNum;o++)this.cells[n+this.columnNum][o]={dx:n,dy:o,x:n*this.CELL_W,y:o*this.CELL_H,occupied:!1,node:null}}for(n=this.columnNum-1;n>e;n--)for(o=0;o<this.rowNum;o++)this.cells[n+t][o]=r(r({},this.cells[n][o]),{x:(n+t)*this.CELL_W,y:o*this.CELL_H}),this.cells[n][o]={x:n*this.CELL_W,y:o*this.CELL_H,occupied:!0,node:null};for(o=0;o<this.additionColumn.length;o++)this.additionColumn[o]>=e&&(this.additionColumn[o]+=t);for(n=0;n<t;n++)this.additionColumn.push(e+n+1);this.columnNum+=t}},e.prototype.insertRow=function(e,t){if(!(t<=0)){for(var n=0;n<t;n++)for(var o=0;o<this.columnNum;o++)this.cells[o][n+this.rowNum]={dx:o,dy:n,x:o*this.CELL_W,y:n*this.CELL_H,occupied:!1,node:null};for(o=0;o<this.columnNum;o++)for(n=this.rowNum-1;n>e;n--)this.cells[o][n+t]=r(r({},this.cells[o][n]),{dx:o,dy:n+t,x:o*this.CELL_W,y:(n+t)*this.CELL_H}),this.cells[o][n]={dx:o,dy:n,x:o*this.CELL_W,y:n*this.CELL_H,occupied:!1,node:null};for(n=0;n<this.additionRow.length;n++)this.additionRow[n]>=e&&(this.additionRow[n]+=t);for(o=0;o<t;o++)this.additionRow.push(e+o+1);this.rowNum+=t}},e.prototype.getNodes=function(){for(var e=[],t=0;t<this.columnNum;t++)for(var n=0;n<this.rowNum;n++)this.cells[t][n].node&&e.push(this.cells[t][n]);return e},e.MIN_DIST=50,e.DEFAULT_CELL_W=80,e.DEFAULT_CELL_H=80,e}();t.default=o},"18fc":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.clusterBundle=t.clusterCode=t.fruchtermanBundle=t.fruchtermanCode=void 0,t.fruchtermanCode="\nimport { globalInvocationID } from 'g-webgpu';\nconst MAX_EDGE_PER_VERTEX;\nconst VERTEX_COUNT;\n@numthreads(1, 1, 1)\nclass Fruchterman {\n  @in @out\n  u_Data: vec4[];\n  @in\n  u_K: float;\n  @in\n  u_K2: float;\n  \n  @in\n  u_Center: vec2;\n  @in\n  u_Gravity: float;\n  @in\n  u_ClusterGravity: float;\n  @in\n  u_Speed: float;\n  @in\n  u_MaxDisplace: float;\n  @in\n  u_Clustering: float;\n  @in\n  u_AttributeArray: vec4[];\n  @in\n  u_ClusterCenters: vec4[];\n  calcRepulsive(i: int, currentNode: vec4): vec2 {\n    let dx = 0, dy = 0;\n    for (let j = 0; j < VERTEX_COUNT; j++) {\n      if (i != j) {\n        const nextNode = this.u_Data[j];\n        const xDist = currentNode[0] - nextNode[0];\n        const yDist = currentNode[1] - nextNode[1];\n        const dist = (xDist * xDist + yDist * yDist) + 0.01;\n        let param = this.u_K2 / dist;\n        \n        if (dist > 0.0) {\n          dx += param * xDist;\n          dy += param * yDist;\n          if (xDist == 0 && yDist == 0) {\n            const sign = i < j ? 1 : -1;\n            dx += param * sign;\n            dy += param * sign;\n          }\n        }\n      }\n    }\n    return [dx, dy];\n  }\n  calcGravity(currentNode: vec4, nodeAttributes: vec4): vec2 { // \n    let dx = 0, dy = 0;\n    const vx = currentNode[0] - this.u_Center[0];\n    const vy = currentNode[1] - this.u_Center[1];\n    const gf = 0.01 * this.u_K * this.u_Gravity;\n    dx = gf * vx;\n    dy = gf * vy;\n    if (this.u_Clustering == 1) {\n      const clusterIdx = int(nodeAttributes[0]);\n      const center = this.u_ClusterCenters[clusterIdx];\n      const cvx = currentNode[0] - center[0];\n      const cvy = currentNode[1] - center[1];\n      const dist = sqrt(cvx * cvx + cvy * cvy) + 0.01;\n      const parma = this.u_K * this.u_ClusterGravity / dist;\n      dx += parma * cvx;\n      dy += parma * cvy;\n    }\n    return [dx, dy];\n  }\n  calcAttractive(i: int, currentNode: vec4): vec2 {\n    let dx = 0, dy = 0;\n    const arr_offset = int(floor(currentNode[2] + 0.5));\n    const length = int(floor(currentNode[3] + 0.5));\n    const node_buffer: vec4;\n    for (let p = 0; p < MAX_EDGE_PER_VERTEX; p++) {\n      if (p >= length) break;\n      const arr_idx = arr_offset + p;\n      // when arr_idx % 4 == 0 update currentNodedx_buffer\n      const buf_offset = arr_idx - arr_idx / 4 * 4;\n      if (p == 0 || buf_offset == 0) {\n        node_buffer = this.u_Data[int(arr_idx / 4)];\n      }\n      const float_j = buf_offset == 0 ? node_buffer[0] :\n                      buf_offset == 1 ? node_buffer[1] :\n                      buf_offset == 2 ? node_buffer[2] :\n                                        node_buffer[3];\n      const nextNode = this.u_Data[int(float_j)];\n      const xDist = currentNode[0] - nextNode[0];\n      const yDist = currentNode[1] - nextNode[1];\n      const dist = sqrt(xDist * xDist + yDist * yDist) + 0.01;\n      let attractiveF = dist / this.u_K;\n    \n      if (dist > 0.0) {\n        dx -= xDist * attractiveF;\n        dy -= yDist * attractiveF;\n        if (xDist == 0 && yDist == 0) {\n          const sign = i < int(float_j) ? 1 : -1;\n          dx -= sign * attractiveF;\n          dy -= sign * attractiveF;\n        }\n      }\n    }\n    return [dx, dy];\n  }\n  @main\n  compute() {\n    const i = globalInvocationID.x;\n    const currentNode = this.u_Data[i];\n    let dx = 0, dy = 0;\n    if (i >= VERTEX_COUNT) {\n      this.u_Data[i] = currentNode;\n      return;\n    }\n\n    // [gravity, fx, fy, 0]\n    const nodeAttributes = this.u_AttributeArray[i];\n\n    if (nodeAttributes[1] != 0 && nodeAttributes[2] != 0) {\n      // the node is fixed\n      this.u_Data[i] = [\n        nodeAttributes[1],\n        nodeAttributes[2],\n        currentNode[2],\n        currentNode[3]\n      ];\n      return;\n    }\n\n    // repulsive\n    const repulsive = this.calcRepulsive(i, currentNode);\n    dx += repulsive[0];\n    dy += repulsive[1];\n    // attractive\n    const attractive = this.calcAttractive(i, currentNode);\n    dx += attractive[0];\n    dy += attractive[1];\n    // gravity\n    const gravity = this.calcGravity(currentNode, nodeAttributes);\n    dx -= gravity[0];\n    dy -= gravity[1];\n    // speed\n    dx *= this.u_Speed;\n    dy *= this.u_Speed;\n\n    // move\n    const distLength = sqrt(dx * dx + dy * dy);\n    if (distLength > 0.0) {\n      const limitedDist = min(this.u_MaxDisplace * this.u_Speed, distLength);\n      this.u_Data[i] = [\n        currentNode[0] + dx / distLength * limitedDist,\n        currentNode[1] + dy / distLength * limitedDist,\n        currentNode[2],\n        currentNode[3]\n      ];\n    }\n  }\n}\n",t.fruchtermanBundle='{"shaders":{"WGSL":"import \\"GLSL.std.450\\" as std;\\n\\n\\n# var gWebGPUDebug : bool = false;\\n# var gWebGPUDebugOutput : vec4<f32> = vec4<f32>(0.0);\\n\\n[[builtin global_invocation_id]] var<in> globalInvocationID : vec3<u32>;\\n# [[builtin work_group_size]] var<in> workGroupSize : vec3<u32>;\\n# [[builtin work_group_id]] var<in> workGroupID : vec3<u32>;\\n[[builtin local_invocation_id]] var<in> localInvocationID : vec3<u32>;\\n# [[builtin num_work_groups]] var<in> numWorkGroups : vec3<u32>;\\n[[builtin local_invocation_idx]] var<in> localInvocationIndex : u32;\\n\\ntype GWebGPUParams = [[block]] struct {\\n  [[offset 0]] u_K : f32;\\n  [[offset 4]] u_K2 : f32;\\n  [[offset 8]] u_Center : vec2<f32>;\\n  [[offset 16]] u_Gravity : f32;\\n  [[offset 20]] u_ClusterGravity : f32;\\n  [[offset 24]] u_Speed : f32;\\n  [[offset 28]] u_MaxDisplace : f32;\\n  [[offset 32]] u_Clustering : f32;\\n};\\n[[binding 0, set 0]] var<uniform> gWebGPUUniformParams : GWebGPUParams;\\ntype GWebGPUBuffer0 = [[block]] struct {\\n  [[offset 0]] u_Data : [[stride 16]] array<vec4<f32>>;\\n};\\n[[binding 1, set 0]] var<storage_buffer> gWebGPUBuffer0 : GWebGPUBuffer0;\\ntype GWebGPUBuffer1 = [[block]] struct {\\n  [[offset 0]] u_AttributeArray : [[stride 16]] array<vec4<f32>>;\\n};\\n[[binding 2, set 0]] var<storage_buffer> gWebGPUBuffer1 : GWebGPUBuffer1;\\ntype GWebGPUBuffer2 = [[block]] struct {\\n  [[offset 0]] u_ClusterCenters : [[stride 16]] array<vec4<f32>>;\\n};\\n[[binding 3, set 0]] var<storage_buffer> gWebGPUBuffer2 : GWebGPUBuffer2;\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\nfn calcRepulsive(i : i32, currentNode : vec4<f32>) -> vec2<f32> {var dx : f32 = 0.0;\\nvar dy : f32 = 0.0;\\nfor (var j : i32 = 0; j < __DefineValuePlaceholder__VERTEX_COUNT; j = j + 1) {if (i != j) {var nextNode : vec4<f32> = gWebGPUBuffer0.u_Data[j];\\nvar xDist : f32 = currentNode.x - nextNode.x;\\nvar yDist : f32 = currentNode.y - nextNode.y;\\nvar dist : f32 = ((xDist * xDist) + (yDist * yDist)) + 0.01;\\nvar param : f32 = gWebGPUUniformParams.u_K2 / dist;\\nif (dist > 0.0) {dx = dx + param * xDist;\\ndy = dy + param * yDist;\\nif ((xDist == 0.0) && (yDist == 0.0)) {var sign : f32 = select(1.0, -1.0, i < j);\\ndx = dx + param * std::sign;\\ndy = dy + param * std::sign;}}}}\\nreturn vec2<f32>(dx, dy);}\\nfn calcGravity(currentNode : vec4<f32>, nodeAttributes : vec4<f32>) -> vec2<f32> {var dx : f32 = 0.0;\\nvar dy : f32 = 0.0;\\nvar vx : f32 = currentNode.x - gWebGPUUniformParams.u_Center.x;\\nvar vy : f32 = currentNode.y - gWebGPUUniformParams.u_Center.y;\\nvar gf : f32 = (0.01 * gWebGPUUniformParams.u_K) * gWebGPUUniformParams.u_Gravity;\\ndx = gf * vx;\\ndy = gf * vy;\\nif (gWebGPUUniformParams.u_Clustering == 1.0) {var clusterIdx : i32 = i32(nodeAttributes.x);\\nvar center : vec4<f32> = gWebGPUBuffer2.u_ClusterCenters[clusterIdx];\\nvar cvx : f32 = currentNode.x - center.x;\\nvar cvy : f32 = currentNode.y - center.y;\\nvar dist : f32 = std::sqrt((cvx * cvx) + (cvy * cvy)) + 0.01;\\nvar parma : f32 = (gWebGPUUniformParams.u_K * gWebGPUUniformParams.u_ClusterGravity) / dist;\\ndx = dx + parma * cvx;\\ndy = dy + parma * cvy;}\\nreturn vec2<f32>(dx, dy);}\\nfn calcAttractive(i : i32, currentNode : vec4<f32>) -> vec2<f32> {var dx : f32 = 0.0;\\nvar dy : f32 = 0.0;\\nvar arr_offset : i32 = i32(std::floor(currentNode.z + 0.5));\\nvar length : i32 = i32(std::floor(currentNode.w + 0.5));\\nvar node_buffer : vec4<f32>;\\nfor (var p : i32 = 0; p < __DefineValuePlaceholder__MAX_EDGE_PER_VERTEX; p = p + 1) {if (p >= length) {break;}\\nvar arr_idx : i32 = arr_offset + i32(p);\\nvar buf_offset : i32 = arr_idx - ((arr_idx / 4) * 4);\\nif ((p == 0) || (buf_offset == 0)) {node_buffer = gWebGPUBuffer0.u_Data[i32(arr_idx / 4)];}\\nvar float_j : f32 = select(node_buffer.x, select(node_buffer.y, select(node_buffer.z, node_buffer.w, buf_offset == 2), buf_offset == 1), buf_offset == 0);\\nvar nextNode : vec4<f32> = gWebGPUBuffer0.u_Data[i32(float_j)];\\nvar xDist : f32 = currentNode.x - nextNode.x;\\nvar yDist : f32 = currentNode.y - nextNode.y;\\nvar dist : f32 = std::sqrt((xDist * xDist) + (yDist * yDist)) + 0.01;\\nvar attractiveF : f32 = dist / gWebGPUUniformParams.u_K;\\nif (dist > 0.0) {dx = dx - xDist * attractiveF;\\ndy = dy - yDist * attractiveF;\\nif ((xDist == 0.0) && (yDist == 0.0)) {var sign : f32 = select(1.0, -1.0, i < i32(float_j));\\ndx = dx - std::sign * attractiveF;\\ndy = dy - std::sign * attractiveF;}}}\\nreturn vec2<f32>(dx, dy);}\\nfn main() -> void {var i : i32 = globalInvocationID.x;\\nvar currentNode : vec4<f32> = gWebGPUBuffer0.u_Data[i];\\nvar dx : f32 = 0.0;\\nvar dy : f32 = 0.0;\\nif (i >= __DefineValuePlaceholder__VERTEX_COUNT) {gWebGPUBuffer0.u_Data[i] = currentNode;\\nreturn ;}\\nvar nodeAttributes : vec4<f32> = gWebGPUBuffer1.u_AttributeArray[i];\\nif ((nodeAttributes.y != 0.0) && (nodeAttributes.z != 0.0)) {gWebGPUBuffer0.u_Data[i] = vec4<f32>(nodeAttributes.y, nodeAttributes.z, currentNode.z, currentNode.w);\\nreturn ;}\\nvar repulsive : vec2<f32> = calcRepulsive(i, currentNode);\\ndx = dx + repulsive.x;\\ndy = dy + repulsive.y;\\nvar attractive : vec2<f32> = calcAttractive(i, currentNode);\\ndx = dx + attractive.x;\\ndy = dy + attractive.y;\\nvar gravity : vec2<f32> = calcGravity(currentNode, nodeAttributes);\\ndx = dx - gravity.x;\\ndy = dy - gravity.y;\\ndx = dx * gWebGPUUniformParams.u_Speed;\\ndy = dy * gWebGPUUniformParams.u_Speed;\\nvar distLength : f32 = std::sqrt((dx * dx) + (dy * dy));\\nif (distLength > 0.0) {var limitedDist : f32 = std::min(gWebGPUUniformParams.u_MaxDisplace * gWebGPUUniformParams.u_Speed, distLength);\\ngWebGPUBuffer0.u_Data[i] = vec4<f32>(currentNode.x + ((dx / distLength) * limitedDist), currentNode.y + ((dy / distLength) * limitedDist), currentNode.z, currentNode.w);}\\nreturn;}\\n\\nentry_point compute as \\"main\\" = main;\\n","GLSL450":"\\n\\n\\nbool gWebGPUDebug = false;\\nvec4 gWebGPUDebugOutput = vec4(0.0);\\n\\nivec3 globalInvocationID = ivec3(gl_GlobalInvocationID);\\nivec3 workGroupSize = ivec3(1,1,1);\\nivec3 workGroupID = ivec3(gl_WorkGroupID);\\nivec3 localInvocationID = ivec3(gl_LocalInvocationID);\\nivec3 numWorkGroups = ivec3(gl_NumWorkGroups);\\nint localInvocationIndex = int(gl_LocalInvocationIndex);\\n\\nlayout(std140, set = 0, binding = 0) uniform GWebGPUParams {\\n  float u_K;\\n  float u_K2;\\n  vec2 u_Center;\\n  float u_Gravity;\\n  float u_ClusterGravity;\\n  float u_Speed;\\n  float u_MaxDisplace;\\n  float u_Clustering;\\n} gWebGPUUniformParams;\\nlayout(std430, set = 0, binding = 1) buffer   GWebGPUBuffer0 {\\n  vec4 u_Data[];\\n} gWebGPUBuffer0;\\n\\nlayout(std430, set = 0, binding = 2) buffer readonly  GWebGPUBuffer1 {\\n  vec4 u_AttributeArray[];\\n} gWebGPUBuffer1;\\n\\nlayout(std430, set = 0, binding = 3) buffer readonly  GWebGPUBuffer2 {\\n  vec4 u_ClusterCenters[];\\n} gWebGPUBuffer2;\\n\\n\\n\\n#define MAX_EDGE_PER_VERTEX __DefineValuePlaceholder__MAX_EDGE_PER_VERTEX\\n#define VERTEX_COUNT __DefineValuePlaceholder__VERTEX_COUNT\\nlayout (\\n  local_size_x = 1,\\n  local_size_y = 1,\\n  local_size_z = 1\\n) in;\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\nvec2 calcRepulsive(int i, vec4 currentNode) {float dx = 0.0;\\nfloat dy = 0.0;\\nfor (int j = 0; j < VERTEX_COUNT; j++) {if (i != j) {vec4 nextNode = gWebGPUBuffer0.u_Data[j];\\nfloat xDist = currentNode.x - nextNode.x;\\nfloat yDist = currentNode.y - nextNode.y;\\nfloat dist = ((xDist * xDist) + (yDist * yDist)) + 0.01;\\nfloat param = gWebGPUUniformParams.u_K2 / dist;\\nif (dist > 0.0) {dx += param * xDist;\\ndy += param * yDist;\\nif ((xDist == 0.0) && (yDist == 0.0)) {float sign = (i < j) ? (1.0) : (-1.0);\\ndx += param * sign;\\ndy += param * sign;}}}}\\nreturn vec2(dx, dy);}\\nvec2 calcGravity(vec4 currentNode, vec4 nodeAttributes) {float dx = 0.0;\\nfloat dy = 0.0;\\nfloat vx = currentNode.x - gWebGPUUniformParams.u_Center.x;\\nfloat vy = currentNode.y - gWebGPUUniformParams.u_Center.y;\\nfloat gf = (0.01 * gWebGPUUniformParams.u_K) * gWebGPUUniformParams.u_Gravity;\\ndx = gf * vx;\\ndy = gf * vy;\\nif (gWebGPUUniformParams.u_Clustering == 1.0) {int clusterIdx = int(nodeAttributes.x);\\nvec4 center = gWebGPUBuffer2.u_ClusterCenters[clusterIdx];\\nfloat cvx = currentNode.x - center.x;\\nfloat cvy = currentNode.y - center.y;\\nfloat dist = sqrt((cvx * cvx) + (cvy * cvy)) + 0.01;\\nfloat parma = (gWebGPUUniformParams.u_K * gWebGPUUniformParams.u_ClusterGravity) / dist;\\ndx += parma * cvx;\\ndy += parma * cvy;}\\nreturn vec2(dx, dy);}\\nvec2 calcAttractive(int i, vec4 currentNode) {float dx = 0.0;\\nfloat dy = 0.0;\\nint arr_offset = int(floor(currentNode.z + 0.5));\\nint length = int(floor(currentNode.w + 0.5));\\nvec4 node_buffer;\\nfor (int p = 0; p < MAX_EDGE_PER_VERTEX; p++) {if (p >= length) {break;}\\nint arr_idx = arr_offset + int(p);\\nint buf_offset = arr_idx - ((arr_idx / 4) * 4);\\nif ((p == 0) || (buf_offset == 0)) {node_buffer = gWebGPUBuffer0.u_Data[int(arr_idx / 4)];}\\nfloat float_j = (buf_offset == 0) ? (node_buffer.x) : ((buf_offset == 1) ? (node_buffer.y) : ((buf_offset == 2) ? (node_buffer.z) : (node_buffer.w)));\\nvec4 nextNode = gWebGPUBuffer0.u_Data[int(float_j)];\\nfloat xDist = currentNode.x - nextNode.x;\\nfloat yDist = currentNode.y - nextNode.y;\\nfloat dist = sqrt((xDist * xDist) + (yDist * yDist)) + 0.01;\\nfloat attractiveF = dist / gWebGPUUniformParams.u_K;\\nif (dist > 0.0) {dx -= xDist * attractiveF;\\ndy -= yDist * attractiveF;\\nif ((xDist == 0.0) && (yDist == 0.0)) {float sign = (i < int(float_j)) ? (1.0) : (-1.0);\\ndx -= sign * attractiveF;\\ndy -= sign * attractiveF;}}}\\nreturn vec2(dx, dy);}\\nvoid main() {int i = globalInvocationID.x;\\nvec4 currentNode = gWebGPUBuffer0.u_Data[i];\\nfloat dx = 0.0;\\nfloat dy = 0.0;\\nif (i >= VERTEX_COUNT) {gWebGPUBuffer0.u_Data[i] = currentNode;\\nreturn ;}\\nvec4 nodeAttributes = gWebGPUBuffer1.u_AttributeArray[i];\\nif ((nodeAttributes.y != 0.0) && (nodeAttributes.z != 0.0)) {gWebGPUBuffer0.u_Data[i] = vec4(nodeAttributes.y, nodeAttributes.z, currentNode.z, currentNode.w);\\nreturn ;}\\nvec2 repulsive = calcRepulsive(i, currentNode);\\ndx += repulsive.x;\\ndy += repulsive.y;\\nvec2 attractive = calcAttractive(i, currentNode);\\ndx += attractive.x;\\ndy += attractive.y;\\nvec2 gravity = calcGravity(currentNode, nodeAttributes);\\ndx -= gravity.x;\\ndy -= gravity.y;\\ndx *= gWebGPUUniformParams.u_Speed;\\ndy *= gWebGPUUniformParams.u_Speed;\\nfloat distLength = sqrt((dx * dx) + (dy * dy));\\nif (distLength > 0.0) {float limitedDist = min(gWebGPUUniformParams.u_MaxDisplace * gWebGPUUniformParams.u_Speed, distLength);\\ngWebGPUBuffer0.u_Data[i] = vec4(currentNode.x + ((dx / distLength) * limitedDist), currentNode.y + ((dy / distLength) * limitedDist), currentNode.z, currentNode.w);}}\\n","GLSL100":"\\n\\nfloat epsilon = 0.00001;\\nvec2 addrTranslation_1Dto2D(float address1D, vec2 texSize) {\\n  vec2 conv_const = vec2(1.0 / texSize.x, 1.0 / (texSize.x * texSize.y));\\n  vec2 normAddr2D = float(address1D) * conv_const;\\n  return vec2(fract(normAddr2D.x + epsilon), normAddr2D.y);\\n}\\n\\nvoid barrier() {}\\n  \\n\\nuniform vec2 u_OutputTextureSize;\\nuniform int u_OutputTexelCount;\\nvarying vec2 v_TexCoord;\\n\\nbool gWebGPUDebug = false;\\nvec4 gWebGPUDebugOutput = vec4(0.0);\\n\\n#define MAX_EDGE_PER_VERTEX __DefineValuePlaceholder__MAX_EDGE_PER_VERTEX\\n#define VERTEX_COUNT __DefineValuePlaceholder__VERTEX_COUNT\\n\\nuniform sampler2D u_Data;\\nuniform vec2 u_DataSize;\\nvec4 getDatau_Data(vec2 address2D) {\\n  return vec4(texture2D(u_Data, address2D).rgba);\\n}\\nvec4 getDatau_Data(float address1D) {\\n  return getDatau_Data(addrTranslation_1Dto2D(address1D, u_DataSize));\\n}\\nvec4 getDatau_Data(int address1D) {\\n  return getDatau_Data(float(address1D));\\n}\\nuniform float u_K;\\nuniform float u_K2;\\nuniform vec2 u_Center;\\nuniform float u_Gravity;\\nuniform float u_ClusterGravity;\\nuniform float u_Speed;\\nuniform float u_MaxDisplace;\\nuniform float u_Clustering;\\nuniform sampler2D u_AttributeArray;\\nuniform vec2 u_AttributeArraySize;\\nvec4 getDatau_AttributeArray(vec2 address2D) {\\n  return vec4(texture2D(u_AttributeArray, address2D).rgba);\\n}\\nvec4 getDatau_AttributeArray(float address1D) {\\n  return getDatau_AttributeArray(addrTranslation_1Dto2D(address1D, u_AttributeArraySize));\\n}\\nvec4 getDatau_AttributeArray(int address1D) {\\n  return getDatau_AttributeArray(float(address1D));\\n}\\nuniform sampler2D u_ClusterCenters;\\nuniform vec2 u_ClusterCentersSize;\\nvec4 getDatau_ClusterCenters(vec2 address2D) {\\n  return vec4(texture2D(u_ClusterCenters, address2D).rgba);\\n}\\nvec4 getDatau_ClusterCenters(float address1D) {\\n  return getDatau_ClusterCenters(addrTranslation_1Dto2D(address1D, u_ClusterCentersSize));\\n}\\nvec4 getDatau_ClusterCenters(int address1D) {\\n  return getDatau_ClusterCenters(float(address1D));\\n}\\nvec2 calcRepulsive(int i, vec4 currentNode) {\\nivec3 workGroupSize = ivec3(1, 1, 1);\\nivec3 numWorkGroups = ivec3(1, 1, 1);     \\nint globalInvocationIndex = int(floor(v_TexCoord.x * u_OutputTextureSize.x))\\n  + int(floor(v_TexCoord.y * u_OutputTextureSize.y)) * int(u_OutputTextureSize.x);\\nint workGroupIDLength = globalInvocationIndex / (workGroupSize.x * workGroupSize.y * workGroupSize.z);\\nivec3 workGroupID = ivec3(workGroupIDLength / numWorkGroups.y / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.y);\\nint localInvocationIDZ = globalInvocationIndex / (workGroupSize.x * workGroupSize.y);\\nint localInvocationIDY = (globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y) / workGroupSize.x;\\nint localInvocationIDX = globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y - localInvocationIDY * workGroupSize.x;\\nivec3 localInvocationID = ivec3(localInvocationIDX, localInvocationIDY, localInvocationIDZ);\\nivec3 globalInvocationID = workGroupID * workGroupSize + localInvocationID;\\nint localInvocationIndex = localInvocationID.z * workGroupSize.x * workGroupSize.y\\n                + localInvocationID.y * workGroupSize.x + localInvocationID.x;\\nfloat dx = 0.0;\\nfloat dy = 0.0;\\nfor (int j = 0; j < VERTEX_COUNT; j++) {if (i != j) {vec4 nextNode = getDatau_Data(j);\\nfloat xDist = currentNode.x - nextNode.x;\\nfloat yDist = currentNode.y - nextNode.y;\\nfloat dist = ((xDist * xDist) + (yDist * yDist)) + 0.01;\\nfloat param = u_K2 / dist;\\nif (dist > 0.0) {dx += param * xDist;\\ndy += param * yDist;\\nif ((xDist == 0.0) && (yDist == 0.0)) {float sign = (i < j) ? (1.0) : (-1.0);\\ndx += param * sign;\\ndy += param * sign;}}}}\\nreturn vec2(dx, dy);}\\nvec2 calcGravity(vec4 currentNode, vec4 nodeAttributes) {\\nivec3 workGroupSize = ivec3(1, 1, 1);\\nivec3 numWorkGroups = ivec3(1, 1, 1);     \\nint globalInvocationIndex = int(floor(v_TexCoord.x * u_OutputTextureSize.x))\\n  + int(floor(v_TexCoord.y * u_OutputTextureSize.y)) * int(u_OutputTextureSize.x);\\nint workGroupIDLength = globalInvocationIndex / (workGroupSize.x * workGroupSize.y * workGroupSize.z);\\nivec3 workGroupID = ivec3(workGroupIDLength / numWorkGroups.y / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.y);\\nint localInvocationIDZ = globalInvocationIndex / (workGroupSize.x * workGroupSize.y);\\nint localInvocationIDY = (globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y) / workGroupSize.x;\\nint localInvocationIDX = globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y - localInvocationIDY * workGroupSize.x;\\nivec3 localInvocationID = ivec3(localInvocationIDX, localInvocationIDY, localInvocationIDZ);\\nivec3 globalInvocationID = workGroupID * workGroupSize + localInvocationID;\\nint localInvocationIndex = localInvocationID.z * workGroupSize.x * workGroupSize.y\\n                + localInvocationID.y * workGroupSize.x + localInvocationID.x;\\nfloat dx = 0.0;\\nfloat dy = 0.0;\\nfloat vx = currentNode.x - u_Center.x;\\nfloat vy = currentNode.y - u_Center.y;\\nfloat gf = (0.01 * u_K) * u_Gravity;\\ndx = gf * vx;\\ndy = gf * vy;\\nif (u_Clustering == 1.0) {int clusterIdx = int(nodeAttributes.x);\\nvec4 center = getDatau_ClusterCenters(clusterIdx);\\nfloat cvx = currentNode.x - center.x;\\nfloat cvy = currentNode.y - center.y;\\nfloat dist = sqrt((cvx * cvx) + (cvy * cvy)) + 0.01;\\nfloat parma = (u_K * u_ClusterGravity) / dist;\\ndx += parma * cvx;\\ndy += parma * cvy;}\\nreturn vec2(dx, dy);}\\nvec2 calcAttractive(int i, vec4 currentNode) {\\nivec3 workGroupSize = ivec3(1, 1, 1);\\nivec3 numWorkGroups = ivec3(1, 1, 1);     \\nint globalInvocationIndex = int(floor(v_TexCoord.x * u_OutputTextureSize.x))\\n  + int(floor(v_TexCoord.y * u_OutputTextureSize.y)) * int(u_OutputTextureSize.x);\\nint workGroupIDLength = globalInvocationIndex / (workGroupSize.x * workGroupSize.y * workGroupSize.z);\\nivec3 workGroupID = ivec3(workGroupIDLength / numWorkGroups.y / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.y);\\nint localInvocationIDZ = globalInvocationIndex / (workGroupSize.x * workGroupSize.y);\\nint localInvocationIDY = (globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y) / workGroupSize.x;\\nint localInvocationIDX = globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y - localInvocationIDY * workGroupSize.x;\\nivec3 localInvocationID = ivec3(localInvocationIDX, localInvocationIDY, localInvocationIDZ);\\nivec3 globalInvocationID = workGroupID * workGroupSize + localInvocationID;\\nint localInvocationIndex = localInvocationID.z * workGroupSize.x * workGroupSize.y\\n                + localInvocationID.y * workGroupSize.x + localInvocationID.x;\\nfloat dx = 0.0;\\nfloat dy = 0.0;\\nint arr_offset = int(floor(currentNode.z + 0.5));\\nint length = int(floor(currentNode.w + 0.5));\\nvec4 node_buffer;\\nfor (int p = 0; p < MAX_EDGE_PER_VERTEX; p++) {if (p >= length) {break;}\\nint arr_idx = arr_offset + int(p);\\nint buf_offset = arr_idx - ((arr_idx / 4) * 4);\\nif ((p == 0) || (buf_offset == 0)) {node_buffer = getDatau_Data(int(arr_idx / 4));}\\nfloat float_j = (buf_offset == 0) ? (node_buffer.x) : ((buf_offset == 1) ? (node_buffer.y) : ((buf_offset == 2) ? (node_buffer.z) : (node_buffer.w)));\\nvec4 nextNode = getDatau_Data(int(float_j));\\nfloat xDist = currentNode.x - nextNode.x;\\nfloat yDist = currentNode.y - nextNode.y;\\nfloat dist = sqrt((xDist * xDist) + (yDist * yDist)) + 0.01;\\nfloat attractiveF = dist / u_K;\\nif (dist > 0.0) {dx -= xDist * attractiveF;\\ndy -= yDist * attractiveF;\\nif ((xDist == 0.0) && (yDist == 0.0)) {float sign = (i < int(float_j)) ? (1.0) : (-1.0);\\ndx -= sign * attractiveF;\\ndy -= sign * attractiveF;}}}\\nreturn vec2(dx, dy);}\\nvoid main() {\\nivec3 workGroupSize = ivec3(1, 1, 1);\\nivec3 numWorkGroups = ivec3(1, 1, 1);     \\nint globalInvocationIndex = int(floor(v_TexCoord.x * u_OutputTextureSize.x))\\n  + int(floor(v_TexCoord.y * u_OutputTextureSize.y)) * int(u_OutputTextureSize.x);\\nint workGroupIDLength = globalInvocationIndex / (workGroupSize.x * workGroupSize.y * workGroupSize.z);\\nivec3 workGroupID = ivec3(workGroupIDLength / numWorkGroups.y / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.y);\\nint localInvocationIDZ = globalInvocationIndex / (workGroupSize.x * workGroupSize.y);\\nint localInvocationIDY = (globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y) / workGroupSize.x;\\nint localInvocationIDX = globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y - localInvocationIDY * workGroupSize.x;\\nivec3 localInvocationID = ivec3(localInvocationIDX, localInvocationIDY, localInvocationIDZ);\\nivec3 globalInvocationID = workGroupID * workGroupSize + localInvocationID;\\nint localInvocationIndex = localInvocationID.z * workGroupSize.x * workGroupSize.y\\n                + localInvocationID.y * workGroupSize.x + localInvocationID.x;\\nint i = globalInvocationID.x;\\nvec4 currentNode = getDatau_Data(i);\\nfloat dx = 0.0;\\nfloat dy = 0.0;\\nif (i >= VERTEX_COUNT) {gl_FragColor = vec4(currentNode);\\nreturn ;}\\nvec4 nodeAttributes = getDatau_AttributeArray(i);\\nif ((nodeAttributes.y != 0.0) && (nodeAttributes.z != 0.0)) {gl_FragColor = vec4(vec4(nodeAttributes.y, nodeAttributes.z, currentNode.z, currentNode.w));\\nreturn ;}\\nvec2 repulsive = calcRepulsive(i, currentNode);\\ndx += repulsive.x;\\ndy += repulsive.y;\\nvec2 attractive = calcAttractive(i, currentNode);\\ndx += attractive.x;\\ndy += attractive.y;\\nvec2 gravity = calcGravity(currentNode, nodeAttributes);\\ndx -= gravity.x;\\ndy -= gravity.y;\\ndx *= u_Speed;\\ndy *= u_Speed;\\nfloat distLength = sqrt((dx * dx) + (dy * dy));\\nif (distLength > 0.0) {float limitedDist = min(u_MaxDisplace * u_Speed, distLength);\\ngl_FragColor = vec4(vec4(currentNode.x + ((dx / distLength) * limitedDist), currentNode.y + ((dy / distLength) * limitedDist), currentNode.z, currentNode.w));}if (gWebGPUDebug) {\\n  gl_FragColor = gWebGPUDebugOutput;\\n}}\\n"},"context":{"name":"","dispatch":[1,1,1],"threadGroupSize":[1,1,1],"maxIteration":1,"defines":[{"name":"MAX_EDGE_PER_VERTEX","type":"Float","runtime":true},{"name":"VERTEX_COUNT","type":"Float","runtime":true}],"uniforms":[{"name":"u_Data","type":"vec4<f32>[]","storageClass":"StorageBuffer","readonly":false,"writeonly":false,"size":[1,1]},{"name":"u_K","type":"Float","storageClass":"Uniform","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_K2","type":"Float","storageClass":"Uniform","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_Center","type":"vec2<f32>","storageClass":"Uniform","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_Gravity","type":"Float","storageClass":"Uniform","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_ClusterGravity","type":"Float","storageClass":"Uniform","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_Speed","type":"Float","storageClass":"Uniform","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_MaxDisplace","type":"Float","storageClass":"Uniform","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_Clustering","type":"Float","storageClass":"Uniform","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_AttributeArray","type":"vec4<f32>[]","storageClass":"StorageBuffer","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_ClusterCenters","type":"vec4<f32>[]","storageClass":"StorageBuffer","readonly":true,"writeonly":false,"size":[1,1]}],"globalDeclarations":[],"output":{"name":"u_Data","size":[1,1],"length":1},"needPingpong":true}}',t.clusterCode="\nimport { globalInvocationID } from 'g-webgpu';\nconst VERTEX_COUNT;\nconst CLUSTER_COUNT;\n@numthreads(1, 1, 1)\nclass CalcCenter {\n  @in\n  u_Data: vec4[];\n  @in\n  u_NodeAttributes: vec4[]; // [[clusterIdx, 0, 0, 0], ...]\n  @in @out\n  u_ClusterCenters: vec4[]; // [[cx, cy, nodeCount, clusterIdx], ...]\n  @main\n  compute() {\n    const i = globalInvocationID.x;\n    const center = this.u_ClusterCenters[i];\n    let sumx = 0;\n    let sumy = 0;\n    let count = 0;\n    for (let j = 0; j < VERTEX_COUNT; j++) {\n      const attributes = this.u_NodeAttributes[j];\n      const clusterIdx = int(attributes[0]);\n      const vertex = this.u_Data[j];\n      if (clusterIdx == i) {\n        sumx += vertex.x;\n        sumy += vertex.y;\n        count += 1;\n      }\n    }\n    this.u_ClusterCenters[i] = [\n      sumx / count,\n      sumy / count,\n      count,\n      i\n    ];\n  }\n}\n",t.clusterBundle='{"shaders":{"WGSL":"import \\"GLSL.std.450\\" as std;\\n\\n\\n# var gWebGPUDebug : bool = false;\\n# var gWebGPUDebugOutput : vec4<f32> = vec4<f32>(0.0);\\n\\n[[builtin global_invocation_id]] var<in> globalInvocationID : vec3<u32>;\\n# [[builtin work_group_size]] var<in> workGroupSize : vec3<u32>;\\n# [[builtin work_group_id]] var<in> workGroupID : vec3<u32>;\\n[[builtin local_invocation_id]] var<in> localInvocationID : vec3<u32>;\\n# [[builtin num_work_groups]] var<in> numWorkGroups : vec3<u32>;\\n[[builtin local_invocation_idx]] var<in> localInvocationIndex : u32;\\n\\n\\ntype GWebGPUBuffer0 = [[block]] struct {\\n  [[offset 0]] u_Data : [[stride 16]] array<vec4<f32>>;\\n};\\n[[binding 0, set 0]] var<storage_buffer> gWebGPUBuffer0 : GWebGPUBuffer0;\\ntype GWebGPUBuffer1 = [[block]] struct {\\n  [[offset 0]] u_NodeAttributes : [[stride 16]] array<vec4<f32>>;\\n};\\n[[binding 1, set 0]] var<storage_buffer> gWebGPUBuffer1 : GWebGPUBuffer1;\\ntype GWebGPUBuffer2 = [[block]] struct {\\n  [[offset 0]] u_ClusterCenters : [[stride 16]] array<vec4<f32>>;\\n};\\n[[binding 2, set 0]] var<storage_buffer> gWebGPUBuffer2 : GWebGPUBuffer2;\\n\\n\\n\\n\\n\\n\\n\\n\\n\\nfn main() -> void {var i : i32 = globalInvocationID.x;\\nvar center : vec4<f32> = gWebGPUBuffer2.u_ClusterCenters[i];\\nvar sumx : f32 = 0.0;\\nvar sumy : f32 = 0.0;\\nvar count : f32 = 0.0;\\nfor (var j : i32 = 0; j < __DefineValuePlaceholder__VERTEX_COUNT; j = j + 1) {var attributes : vec4<f32> = gWebGPUBuffer1.u_NodeAttributes[j];\\nvar clusterIdx : i32 = i32(attributes.x);\\nvar vertex : vec4<f32> = gWebGPUBuffer0.u_Data[j];\\nif (clusterIdx == i) {sumx = sumx + vertex.x;\\nsumy = sumy + vertex.y;\\ncount = count + 1.0;}}\\ngWebGPUBuffer2.u_ClusterCenters[i] = vec4<f32>(sumx / count, sumy / count, count, i);\\nreturn;}\\n\\nentry_point compute as \\"main\\" = main;\\n","GLSL450":"\\n\\n\\nbool gWebGPUDebug = false;\\nvec4 gWebGPUDebugOutput = vec4(0.0);\\n\\nivec3 globalInvocationID = ivec3(gl_GlobalInvocationID);\\nivec3 workGroupSize = ivec3(1,1,1);\\nivec3 workGroupID = ivec3(gl_WorkGroupID);\\nivec3 localInvocationID = ivec3(gl_LocalInvocationID);\\nivec3 numWorkGroups = ivec3(gl_NumWorkGroups);\\nint localInvocationIndex = int(gl_LocalInvocationIndex);\\n\\n\\nlayout(std430, set = 0, binding = 0) buffer readonly  GWebGPUBuffer0 {\\n  vec4 u_Data[];\\n} gWebGPUBuffer0;\\n\\nlayout(std430, set = 0, binding = 1) buffer readonly  GWebGPUBuffer1 {\\n  vec4 u_NodeAttributes[];\\n} gWebGPUBuffer1;\\n\\nlayout(std430, set = 0, binding = 2) buffer   GWebGPUBuffer2 {\\n  vec4 u_ClusterCenters[];\\n} gWebGPUBuffer2;\\n\\n\\n\\n#define VERTEX_COUNT __DefineValuePlaceholder__VERTEX_COUNT\\n#define CLUSTER_COUNT __DefineValuePlaceholder__CLUSTER_COUNT\\nlayout (\\n  local_size_x = 1,\\n  local_size_y = 1,\\n  local_size_z = 1\\n) in;\\n\\n\\n\\nvoid main() {int i = globalInvocationID.x;\\nvec4 center = gWebGPUBuffer2.u_ClusterCenters[i];\\nfloat sumx = 0.0;\\nfloat sumy = 0.0;\\nfloat count = 0.0;\\nfor (int j = 0; j < VERTEX_COUNT; j++) {vec4 attributes = gWebGPUBuffer1.u_NodeAttributes[j];\\nint clusterIdx = int(attributes.x);\\nvec4 vertex = gWebGPUBuffer0.u_Data[j];\\nif (clusterIdx == i) {sumx += vertex.x;\\nsumy += vertex.y;\\ncount += 1.0;}}\\ngWebGPUBuffer2.u_ClusterCenters[i] = vec4(sumx / count, sumy / count, count, i);}\\n","GLSL100":"\\n\\nfloat epsilon = 0.00001;\\nvec2 addrTranslation_1Dto2D(float address1D, vec2 texSize) {\\n  vec2 conv_const = vec2(1.0 / texSize.x, 1.0 / (texSize.x * texSize.y));\\n  vec2 normAddr2D = float(address1D) * conv_const;\\n  return vec2(fract(normAddr2D.x + epsilon), normAddr2D.y);\\n}\\n\\nvoid barrier() {}\\n  \\n\\nuniform vec2 u_OutputTextureSize;\\nuniform int u_OutputTexelCount;\\nvarying vec2 v_TexCoord;\\n\\nbool gWebGPUDebug = false;\\nvec4 gWebGPUDebugOutput = vec4(0.0);\\n\\n#define VERTEX_COUNT __DefineValuePlaceholder__VERTEX_COUNT\\n#define CLUSTER_COUNT __DefineValuePlaceholder__CLUSTER_COUNT\\n\\nuniform sampler2D u_Data;\\nuniform vec2 u_DataSize;\\nvec4 getDatau_Data(vec2 address2D) {\\n  return vec4(texture2D(u_Data, address2D).rgba);\\n}\\nvec4 getDatau_Data(float address1D) {\\n  return getDatau_Data(addrTranslation_1Dto2D(address1D, u_DataSize));\\n}\\nvec4 getDatau_Data(int address1D) {\\n  return getDatau_Data(float(address1D));\\n}\\nuniform sampler2D u_NodeAttributes;\\nuniform vec2 u_NodeAttributesSize;\\nvec4 getDatau_NodeAttributes(vec2 address2D) {\\n  return vec4(texture2D(u_NodeAttributes, address2D).rgba);\\n}\\nvec4 getDatau_NodeAttributes(float address1D) {\\n  return getDatau_NodeAttributes(addrTranslation_1Dto2D(address1D, u_NodeAttributesSize));\\n}\\nvec4 getDatau_NodeAttributes(int address1D) {\\n  return getDatau_NodeAttributes(float(address1D));\\n}\\nuniform sampler2D u_ClusterCenters;\\nuniform vec2 u_ClusterCentersSize;\\nvec4 getDatau_ClusterCenters(vec2 address2D) {\\n  return vec4(texture2D(u_ClusterCenters, address2D).rgba);\\n}\\nvec4 getDatau_ClusterCenters(float address1D) {\\n  return getDatau_ClusterCenters(addrTranslation_1Dto2D(address1D, u_ClusterCentersSize));\\n}\\nvec4 getDatau_ClusterCenters(int address1D) {\\n  return getDatau_ClusterCenters(float(address1D));\\n}\\nvoid main() {\\nivec3 workGroupSize = ivec3(1, 1, 1);\\nivec3 numWorkGroups = ivec3(1, 1, 1);     \\nint globalInvocationIndex = int(floor(v_TexCoord.x * u_OutputTextureSize.x))\\n  + int(floor(v_TexCoord.y * u_OutputTextureSize.y)) * int(u_OutputTextureSize.x);\\nint workGroupIDLength = globalInvocationIndex / (workGroupSize.x * workGroupSize.y * workGroupSize.z);\\nivec3 workGroupID = ivec3(workGroupIDLength / numWorkGroups.y / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.y);\\nint localInvocationIDZ = globalInvocationIndex / (workGroupSize.x * workGroupSize.y);\\nint localInvocationIDY = (globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y) / workGroupSize.x;\\nint localInvocationIDX = globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y - localInvocationIDY * workGroupSize.x;\\nivec3 localInvocationID = ivec3(localInvocationIDX, localInvocationIDY, localInvocationIDZ);\\nivec3 globalInvocationID = workGroupID * workGroupSize + localInvocationID;\\nint localInvocationIndex = localInvocationID.z * workGroupSize.x * workGroupSize.y\\n                + localInvocationID.y * workGroupSize.x + localInvocationID.x;\\nint i = globalInvocationID.x;\\nvec4 center = getDatau_ClusterCenters(i);\\nfloat sumx = 0.0;\\nfloat sumy = 0.0;\\nfloat count = 0.0;\\nfor (int j = 0; j < VERTEX_COUNT; j++) {vec4 attributes = getDatau_NodeAttributes(j);\\nint clusterIdx = int(attributes.x);\\nvec4 vertex = getDatau_Data(j);\\nif (clusterIdx == i) {sumx += vertex.x;\\nsumy += vertex.y;\\ncount += 1.0;}}\\ngl_FragColor = vec4(vec4(sumx / count, sumy / count, count, i));if (gWebGPUDebug) {\\n  gl_FragColor = gWebGPUDebugOutput;\\n}}\\n"},"context":{"name":"","dispatch":[1,1,1],"threadGroupSize":[1,1,1],"maxIteration":1,"defines":[{"name":"VERTEX_COUNT","type":"Float","runtime":true},{"name":"CLUSTER_COUNT","type":"Float","runtime":true}],"uniforms":[{"name":"u_Data","type":"vec4<f32>[]","storageClass":"StorageBuffer","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_NodeAttributes","type":"vec4<f32>[]","storageClass":"StorageBuffer","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_ClusterCenters","type":"vec4<f32>[]","storageClass":"StorageBuffer","readonly":false,"writeonly":false,"size":[1,1]}],"globalDeclarations":[],"output":{"name":"u_ClusterCenters","size":[1,1],"length":1},"needPingpong":true}}'},"19d2":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.findMinMaxNodeXY=t.traverseTreeUp=t.scaleMatrix=t.getAdjMatrix=t.floydWarshall=t.getDegree=t.getEdgeTerminal=void 0;var r=n("291d"),o=function(e,t){var n=e[t];return(0,r.isObject)(n)?n.cell:n};t.getEdgeTerminal=o;var i=function(e,n,r){for(var o=[],i=0;i<e;i++)o[i]=0;return r?(r.forEach((function(e){var r=(0,t.getEdgeTerminal)(e,"source"),i=(0,t.getEdgeTerminal)(e,"target");r&&(o[n[r]]+=1),i&&(o[n[i]]+=1)})),o):o};t.getDegree=i;var a=function(e){for(var t=[],n=e.length,r=0;r<n;r+=1){t[r]=[];for(var o=0;o<n;o+=1)r===o?t[r][o]=0:0!==e[r][o]&&e[r][o]?t[r][o]=e[r][o]:t[r][o]=1/0}for(var i=0;i<n;i+=1)for(r=0;r<n;r+=1)for(o=0;o<n;o+=1)t[r][o]>t[r][i]+t[i][o]&&(t[r][o]=t[r][i]+t[i][o]);return t};t.floydWarshall=a;var u=function(e,n){var r=e.nodes,o=e.edges,i=[],a={};if(!r)throw new Error("invalid nodes data!");return r&&r.forEach((function(e,t){a[e.id]=t;var n=[];i.push(n)})),o&&o.forEach((function(e){var r=(0,t.getEdgeTerminal)(e,"source"),o=(0,t.getEdgeTerminal)(e,"target"),u=a[r],c=a[o];i[u][c]=1,n||(i[c][u]=1)})),i};t.getAdjMatrix=u;var c=function(e,t){var n=[];return e.forEach((function(e){var r=[];e.forEach((function(e){r.push(e*t)})),n.push(r)})),n};t.scaleMatrix=c;var s=function(e,t){if(e&&e.children)for(var n=e.children.length-1;n>=0;n--)if(!s(e.children[n],t))return;return!!t(e)},l=function(e,t){"function"===typeof t&&s(e,t)};t.traverseTreeUp=l;var d=function(e){var t=1/0,n=1/0,r=-1/0,o=-1/0;return e.forEach((function(e){t>e.x&&(t=e.x),n>e.y&&(n=e.y),r<e.x&&(r=e.x),o<e.y&&(o=e.y)})),{minX:t,minY:n,maxX:r,maxY:o}};t.findMinMaxNodeXY=d},"291d":function(e,t,n){"use strict";var r=this&&this.__assign||function(){return r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n],t)Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},r.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.clone=t.isObject=void 0;var o=function(e){return null!==e&&"object"===typeof e};t.isObject=o;var i=function(e){if(null===e)return e;if(e instanceof Date)return new Date(e.getTime());if(e instanceof Array){var n=[];return e.forEach((function(e){n.push(e)})),n.map((function(e){return(0,t.clone)(e)}))}if("object"===typeof e&&e!=={}){var o=r({},e);return Object.keys(o).forEach((function(e){o[e]=(0,t.clone)(o[e])})),o}return e};t.clone=i},"2ef1":function(e,t,n){"use strict";n.d(t,"a",(function(){return d})),n.d(t,"b",(function(){return y})),n.d(t,"c",(function(){return _})),n.d(t,"d",(function(){return G}));var r=n("8937"),o=/[MLHVQTCSAZ]([^MLHVQTCSAZ]*)/gi,i=/[^\s\,]+/gi;function a(e){var t=e||[];return Object(r["isArray"])(t)?t:Object(r["isString"])(t)?(t=t.match(o),Object(r["each"])(t,(function(e,n){if(e=e.match(i),e[0].length>1){var o=e[0].charAt(0);e.splice(1,0,e[0].substr(1)),e[0]=o}Object(r["each"])(e,(function(t,n){isNaN(t)||(e[n]=+t)})),t[n]=e})),t):void 0}var u=a,c=n("20e7");function s(e,t,n,r){var o,i,a,u,s,l,d,f=[],v=!!r;if(v){a=r[0],u=r[1];for(var p=0,y=e.length;p<y;p+=1){var g=e[p];a=c["d"].min([0,0],a,g),u=c["d"].max([0,0],u,g)}}p=0;for(var h=e.length;p<h;p+=1){g=e[p];if(0!==p||n)if(p!==h-1||n){var _=[p?p-1:h-1,p-1][n?0:1];o=e[_],i=e[n?(p+1)%h:p+1];var x=[0,0];x=c["d"].sub(x,i,o),x=c["d"].scale(x,x,t);var m=c["d"].distance(g,o),b=c["d"].distance(g,i),D=m+b;0!==D&&(m/=D,b/=D);var I=c["d"].scale([0,0],x,-m),w=c["d"].scale([0,0],x,b);l=c["d"].add([0,0],g,I),s=c["d"].add([0,0],g,w),s=c["d"].min([0,0],s,c["d"].max([0,0],i,g)),s=c["d"].max([0,0],s,c["d"].min([0,0],i,g)),I=c["d"].sub([0,0],s,g),I=c["d"].scale([0,0],I,-m/b),l=c["d"].add([0,0],g,I),l=c["d"].min([0,0],l,c["d"].max([0,0],o,g)),l=c["d"].max([0,0],l,c["d"].min([0,0],o,g)),w=c["d"].sub([0,0],g,l),w=c["d"].scale([0,0],w,b/m),s=c["d"].add([0,0],g,w),v&&(l=c["d"].max([0,0],l,a),l=c["d"].min([0,0],l,u),s=c["d"].max([0,0],s,a),s=c["d"].min([0,0],s,u)),f.push(d),f.push(l),d=s}else l=g,f.push(d),f.push(l);else d=g}return n&&f.push(f.shift()),f}function l(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=[[0,0],[1,1]]);for(var r=!!t,o=[],i=0,a=e.length;i<a;i+=2)o.push([e[i],e[i+1]]);var u,c,l,d=s(o,.4,r,n),f=o.length,v=[];for(i=0;i<f-1;i+=1)u=d[2*i],c=d[2*i+1],l=o[i+1],v.push(["C",u[0],u[1],c[0],c[1],l[0],l[1]]);return r&&(u=d[f],c=d[f+1],l=o[0],v.push(["C",u[0],u[1],c[0],c[1],l[0],l[1]])),v}var d=l;var f="\t\n\v\f\r   ᠎             　\u2028\u2029",v=new RegExp("([a-z])["+f+",]*((-?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?["+f+"]*,?["+f+"]*)+)","ig"),p=new RegExp("(-?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?)["+f+"]*,?["+f+"]*","ig");function y(e){if(!e)return null;if(Object(r["isArray"])(e))return e;var t={a:7,c:6,o:2,h:1,l:2,m:2,r:4,q:4,s:4,t:2,v:1,u:3,z:0},n=[];return String(e).replace(v,(function(e,r,o){var i=[],a=r.toLowerCase();if(o.replace(p,(function(e,t){t&&i.push(+t)})),"m"===a&&i.length>2&&(n.push([r].concat(i.splice(0,2))),a="l",r="m"===r?"l":"L"),"o"===a&&1===i.length&&n.push([r,i[0]]),"r"===a)n.push([r].concat(i));else while(i.length>=t[a])if(n.push([r].concat(i.splice(0,t[a]))),!t[a])break;return""})),n}var g=/[a-z]/;function h(e,t){return[t[0]+(t[0]-e[0]),t[1]+(t[1]-e[1])]}function _(e){var t=y(e);if(!t||!t.length)return[["M",0,0]];for(var n=!1,r=0;r<t.length;r++){var o=t[r][0];if(g.test(o)||["V","H","T","S"].indexOf(o)>=0){n=!0;break}}if(!n)return t;var i=[],a=0,u=0,c=0,s=0,l=0,d=t[0];"M"!==d[0]&&"m"!==d[0]||(a=+d[1],u=+d[2],c=a,s=u,l++,i[0]=["M",a,u]);r=l;for(var f=t.length;r<f;r++){var v=t[r],p=i[r-1],_=[],x=(o=v[0],o.toUpperCase());if(o!==x)switch(_[0]=x,x){case"A":_[1]=v[1],_[2]=v[2],_[3]=v[3],_[4]=v[4],_[5]=v[5],_[6]=+v[6]+a,_[7]=+v[7]+u;break;case"V":_[1]=+v[1]+u;break;case"H":_[1]=+v[1]+a;break;case"M":c=+v[1]+a,s=+v[2]+u,_[1]=c,_[2]=s;break;default:for(var m=1,b=v.length;m<b;m++)_[m]=+v[m]+(m%2?a:u)}else _=t[r];switch(x){case"Z":a=+c,u=+s;break;case"H":a=_[1],_=["L",a,u];break;case"V":u=_[1],_=["L",a,u];break;case"T":a=_[1],u=_[2];var D=h([p[1],p[2]],[p[3],p[4]]);_=["Q",D[0],D[1],a,u];break;case"S":a=_[_.length-2],u=_[_.length-1];var I=p.length,w=h([p[I-4],p[I-3]],[p[I-2],p[I-1]]);_=["C",w[0],w[1],_[1],_[2],a,u];break;case"M":c=_[_.length-2],s=_[_.length-1];break;default:a=_[_.length-2],u=_[_.length-1]}i.push(_)}return i}Math.PI;function x(e){return Math.sqrt(e[0]*e[0]+e[1]*e[1])}function m(e,t){return x(e)*x(t)?(e[0]*t[0]+e[1]*t[1])/(x(e)*x(t)):1}function b(e,t){return(e[0]*t[1]<e[1]*t[0]?-1:1)*Math.acos(m(e,t))}function D(e,t){return e[0]===t[0]&&e[1]===t[1]}function I(e,t){var n=t[1],o=t[2],i=Object(r["mod"])(Object(r["toRadian"])(t[3]),2*Math.PI),a=t[4],u=t[5],c=e[0],s=e[1],l=t[6],d=t[7],f=Math.cos(i)*(c-l)/2+Math.sin(i)*(s-d)/2,v=-1*Math.sin(i)*(c-l)/2+Math.cos(i)*(s-d)/2,p=f*f/(n*n)+v*v/(o*o);p>1&&(n*=Math.sqrt(p),o*=Math.sqrt(p));var y=n*n*(v*v)+o*o*(f*f),g=y?Math.sqrt((n*n*(o*o)-y)/y):1;a===u&&(g*=-1),isNaN(g)&&(g=0);var h=o?g*n*v/o:0,_=n?g*-o*f/n:0,x=(c+l)/2+Math.cos(i)*h-Math.sin(i)*_,I=(s+d)/2+Math.sin(i)*h+Math.cos(i)*_,w=[(f-h)/n,(v-_)/o],G=[(-1*f-h)/n,(-1*v-_)/o],S=b([1,0],w),k=b(w,G);return m(w,G)<=-1&&(k=Math.PI),m(w,G)>=1&&(k=0),0===u&&k>0&&(k-=2*Math.PI),1===u&&k<0&&(k+=2*Math.PI),{cx:x,cy:I,rx:D(e,[l,d])?0:n,ry:D(e,[l,d])?0:o,startAngle:S,endAngle:S+k,xRotation:i,arcFlag:a,sweepFlag:u}}function w(e,t){return[t[0]+(t[0]-e[0]),t[1]+(t[1]-e[1])]}function G(e){e=u(e);for(var t=[],n=null,r=null,o=null,i=0,a=e.length,c=0;c<a;c++){var s=e[c];r=e[c+1];var l=s[0],d={command:l,prePoint:n,params:s,startTangent:null,endTangent:null};switch(l){case"M":o=[s[1],s[2]],i=c;break;case"A":var f=I(n,s);d["arcParams"]=f;break;default:break}if("Z"===l)n=o,r=e[i+1];else{var v=s.length;n=[s[v-2],s[v-1]]}r&&"Z"===r[0]&&(r=e[i],t[i]&&(t[i].prePoint=n)),d["currentPoint"]=n,t[i]&&D(n,t[i].currentPoint)&&(t[i].prePoint=d.prePoint);var p=r?[r[r.length-2],r[r.length-1]]:null;d["nextPoint"]=p;var y=d.prePoint;if(["L","H","V"].includes(l))d.startTangent=[y[0]-n[0],y[1]-n[1]],d.endTangent=[n[0]-y[0],n[1]-y[1]];else if("Q"===l){var g=[s[1],s[2]];d.startTangent=[y[0]-g[0],y[1]-g[1]],d.endTangent=[n[0]-g[0],n[1]-g[1]]}else if("T"===l){var h=t[c-1];g=w(h.currentPoint,y);"Q"===h.command?(d.command="Q",d.startTangent=[y[0]-g[0],y[1]-g[1]],d.endTangent=[n[0]-g[0],n[1]-g[1]]):(d.command="TL",d.startTangent=[y[0]-n[0],y[1]-n[1]],d.endTangent=[n[0]-y[0],n[1]-y[1]])}else if("C"===l){var _=[s[1],s[2]],x=[s[3],s[4]];d.startTangent=[y[0]-_[0],y[1]-_[1]],d.endTangent=[n[0]-x[0],n[1]-x[1]],0===d.startTangent[0]&&0===d.startTangent[1]&&(d.startTangent=[_[0]-x[0],_[1]-x[1]]),0===d.endTangent[0]&&0===d.endTangent[1]&&(d.endTangent=[x[0]-_[0],x[1]-_[1]])}else if("S"===l){h=t[c-1],_=w(h.currentPoint,y),x=[s[1],s[2]];"C"===h.command?(d.command="C",d.startTangent=[y[0]-_[0],y[1]-_[1]],d.endTangent=[n[0]-x[0],n[1]-x[1]]):(d.command="SQ",d.startTangent=[y[0]-x[0],y[1]-x[1]],d.endTangent=[n[0]-x[0],n[1]-x[1]])}else if("A"===l){var m=.001,b=d["arcParams"]||{},G=b.cx,S=void 0===G?0:G,k=b.cy,N=void 0===k?0:k,z=b.rx,E=void 0===z?0:z,P=b.ry,M=void 0===P?0:P,C=b.sweepFlag,T=void 0===C?0:C,U=b.startAngle,O=void 0===U?0:U,A=b.endAngle,W=void 0===A?0:A;0===T&&(m*=-1);var L=E*Math.cos(O-m)+S,j=M*Math.sin(O-m)+N;d.startTangent=[L-o[0],j-o[1]];var F=E*Math.cos(O+W+m)+S,B=M*Math.sin(O+W-m)+N;d.endTangent=[y[0]-F,y[1]-B]}t.push(d)}return t}},"3ddc":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n("b4ae"),o=function(){function e(e){this.distances=e.distances,this.dimension=e.dimension||2,this.linkDistance=e.linkDistance}return e.prototype.layout=function(){var e=this,t=e.dimension,n=e.distances,o=e.linkDistance;try{var i=r.Matrix.mul(r.Matrix.pow(n,2),-.5),a=i.mean("row"),u=i.mean("column"),c=i.mean();i.add(c).subRowVector(a).subColumnVector(u);var s=new r.SingularValueDecomposition(i),l=r.Matrix.sqrt(s.diagonalMatrix).diagonal();return s.leftSingularVectors.toJSON().map((function(e){return r.Matrix.mul([e],[l]).toJSON()[0].splice(0,t)}))}catch(y){for(var d=[],f=0;f<n.length;f++){var v=Math.random()*o,p=Math.random()*o;d.push([v,p])}return d}},e}();t.default=o},4237:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.camelize=t.isString=void 0;var r=function(e){return"string"===typeof e};t.isString=r;var o=function(e){var t=Object.create(null);return function(n){var r=t[n];return r||(t[n]=e(n))}},i=/-(\w)/g;t.camelize=o((function(e){return e.replace(i,(function(e,t){return t?t.toUpperCase():""}))}))},"431f":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e){this.xmid=e.xmid,this.ymid=e.ymid,this.length=e.length,this.massCenter=e.massCenter||[0,0],this.mass=e.mass||1}return e.prototype.getLength=function(){return this.length},e.prototype.contains=function(e,t){var n=this.length/2;return e<=this.xmid+n&&e>=this.xmid-n&&t<=this.ymid+n&&t>=this.ymid-n},e.prototype.NW=function(){var t=this.xmid-this.length/4,n=this.ymid+this.length/4,r=this.length/2,o={xmid:t,ymid:n,length:r},i=new e(o);return i},e.prototype.NE=function(){var t=this.xmid+this.length/4,n=this.ymid+this.length/4,r=this.length/2,o={xmid:t,ymid:n,length:r},i=new e(o);return i},e.prototype.SW=function(){var t=this.xmid-this.length/4,n=this.ymid-this.length/4,r=this.length/2,o={xmid:t,ymid:n,length:r},i=new e(o);return i},e.prototype.SE=function(){var t=this.xmid+this.length/4,n=this.ymid-this.length/4,r=this.length/2,o={xmid:t,ymid:n,length:r},i=new e(o);return i},e}();t.default=r},"4b03":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.toNumber=t.isNaN=t.isNumber=void 0;var r=function(e){return"number"===typeof e};t.isNumber=r;var o=function(e){return Number.isNaN(Number(e))};t.isNaN=o;var i=function(e){var n=parseFloat(e);return(0,t.isNaN)(n)?e:n};t.toNumber=i},"4d1a":function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),o(n("ccb2"),t)},"4da8":function(e,t,n){"use strict";var r=this&&this.__extends||function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!==typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(t,"__esModule",{value:!0}),t.RandomLayout=void 0;var o=n("5bc9"),i=function(e){function t(t){var n=e.call(this)||this;return n.center=[0,0],n.width=300,n.height=300,n.nodes=[],n.edges=[],n.onLayoutEnd=function(){},n.updateCfg(t),n}return r(t,e),t.prototype.getDefaultCfg=function(){return{center:[0,0],width:300,height:300}},t.prototype.execute=function(){var e=this,t=e.nodes,n=.9,r=e.center;return e.width||"undefined"===typeof window||(e.width=window.innerWidth),e.height||"undefined"===typeof window||(e.height=window.innerHeight),t&&t.forEach((function(t){t.x=(Math.random()-.5)*n*e.width+r[0],t.y=(Math.random()-.5)*n*e.height+r[1]})),e.onLayoutEnd&&e.onLayoutEnd(),{nodes:t,edges:this.edges}},t.prototype.getType=function(){return"random"},t}(o.Base);t.RandomLayout=i},"4e2a":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Layouts=t.Layout=void 0;var r=n("7049"),o=n("9a24"),i=n("4da8"),a=n("aac1"),u=n("4d1a"),c=n("7c66"),s=n("13d2e"),l=n("634b"),d=n("455a"),f=n("82cc"),v=n("fa97"),p=n("077d"),y=n("0376"),g=n("c7ea"),h=n("7d0a"),_=n("0717"),x=n("927c"),m=n("3c0e"),b=function(){function e(e){var t=(0,r.getLayoutByName)(e.type);this.layoutInstance=new t(e)}return e.prototype.layout=function(e){return this.layoutInstance.layout(e)},e.prototype.updateCfg=function(e){this.layoutInstance.updateCfg(e)},e.prototype.init=function(e){this.layoutInstance.init(e)},e.prototype.execute=function(){this.layoutInstance.execute()},e.prototype.getDefaultCfg=function(){return this.layoutInstance.getDefaultCfg()},e.prototype.destroy=function(){return this.layoutInstance.destroy()},e}();t.Layout=b,t.Layouts={force:u.ForceLayout,fruchterman:v.FruchtermanLayout,forceAtlas2:_.ForceAtlas2Layout,gForce:a.GForceLayout,dagre:s.DagreLayout,dagreCompound:m.DagreCompoundLayout,circular:c.CircularLayout,radial:l.RadialLayout,concentric:d.ConcentricLayout,grid:o.GridLayout,mds:f.MDSLayout,comboForce:g.ComboForceLayout,comboCombined:h.ComboCombinedLayout,random:i.RandomLayout,"gForce-gpu":y.GForceGPULayout,"fruchterman-gpu":p.FruchtermanGPULayout,er:x.ERLayout}},5401:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=800,o=function(){function e(e){this.disp=[],this.positions=e.positions,this.adjMatrix=e.adjMatrix,this.focusID=e.focusID,this.radii=e.radii,this.iterations=e.iterations||10,this.height=e.height||10,this.width=e.width||10,this.speed=e.speed||100,this.gravity=e.gravity||10,this.nodeSizeFunc=e.nodeSizeFunc,this.k=e.k||5,this.strictRadial=e.strictRadial,this.nodes=e.nodes}return e.prototype.layout=function(){var e=this,t=e.positions,n=[],r=e.iterations,o=e.width/10;e.maxDisplace=o,e.disp=n;for(var i=0;i<r;i++)t.forEach((function(e,t){n[t]={x:0,y:0}})),e.getRepulsion(),e.updatePositions();return t},e.prototype.getRepulsion=function(){var e=this,t=e.positions,n=e.nodes,r=e.disp,o=e.k,i=e.radii||[];t.forEach((function(a,u){r[u]={x:0,y:0},t.forEach((function(t,c){if(u!==c&&i[u]===i[c]){var s=a[0]-t[0],l=a[1]-t[1],d=Math.sqrt(s*s+l*l);if(0===d){d=1;var f=u>c?1:-1;s=.01*f,l=.01*f}if(d<e.nodeSizeFunc(n[u])/2+e.nodeSizeFunc(n[c])/2){var v=o*o/d;r[u].x+=s/d*v,r[u].y+=l/d*v}}}))}))},e.prototype.updatePositions=function(){var e=this,t=e.positions,n=e.disp,o=e.speed,i=e.strictRadial,a=e.focusID,u=e.maxDisplace||e.width/10;i&&n.forEach((function(e,n){var r=t[n][0]-t[a][0],o=t[n][1]-t[a][1],i=Math.sqrt(r*r+o*o),u=o/i,c=-r/i,s=Math.sqrt(e.x*e.x+e.y*e.y),l=Math.acos((u*e.x+c*e.y)/s);l>Math.PI/2&&(l-=Math.PI/2,u*=-1,c*=-1);var d=Math.cos(l)*s;e.x=u*d,e.y=c*d}));var c=e.radii;t.forEach((function(e,s){if(s!==a){var l=Math.sqrt(n[s].x*n[s].x+n[s].y*n[s].y);if(l>0&&s!==a){var d=Math.min(u*(o/r),l);if(e[0]+=n[s].x/l*d,e[1]+=n[s].y/l*d,i){var f=e[0]-t[a][0],v=e[1]-t[a][1],p=Math.sqrt(f*f+v*v);f=f/p*c[s],v=v/p*c[s],e[0]=t[a][0]+f,e[1]=t[a][1]+v}}}}))},e}();t.default=o},6098:function(e,t,n){"use strict";var r=this&&this.__assign||function(){return r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n],t)Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},r.apply(this,arguments)},o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e["default"]=t}),a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&o(t,e,n);return i(t,e),t},u=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var c=a(n("0a4a")),s=u(n("7ef3")),l=u(n("cb77")),d=n("13d2e");function f(e,t){var n=e.nodes,o=e.edges,i=t.width,a=t.height;if(!(null===n||void 0===n?void 0:n.length))return Promise.resolve();var u=[];n.forEach((function(e){var t=o.filter((function(t){return t.source===e.id||t.target===e.id}));if(t.length>1){var n=r({},e);delete n.size,u.push(n)}}));var f=[];o.forEach((function(e){var t=u.find((function(t){return t.id===e.source})),n=u.find((function(t){return t.id===e.target}));t&&n&&f.push(e)}));var v=new d.DagreLayout({type:"dagre",ranksep:t.nodeMinGap,nodesep:t.nodeMinGap}),p=v.layout({nodes:u,edges:f}).nodes;n.forEach((function(e){var t=(p||[]).find((function(t){return t.id===e.id}));e.x=(null===t||void 0===t?void 0:t.x)||i/2,e.y=(null===t||void 0===t?void 0:t.y)||a/2}));var y=JSON.parse(JSON.stringify(n)),g=JSON.parse(JSON.stringify(o)),h=c.forceSimulation().nodes(y).force("link",c.forceLink(g).id((function(e){return e.id})).distance((function(e){var t=f.find((function(t){return t.source===e.source&&t.target===e.target}));return t?30:20}))).force("charge",c.forceManyBody()).force("center",c.forceCenter(i/2,a/2)).force("x",c.forceX(i/2)).force("y",c.forceY(a/2)).alpha(.3).alphaDecay(.08).alphaMin(.001),_=new Promise((function(e){h.on("end",(function(){n.forEach((function(e){var t=y.find((function(t){return t.id===e.id}));t&&(e.x=t.x,e.y=t.y)}));var r=Math.min.apply(Math,n.map((function(e){return e.x}))),u=Math.max.apply(Math,n.map((function(e){return e.x}))),c=Math.min.apply(Math,n.map((function(e){return e.y}))),d=Math.max.apply(Math,n.map((function(e){return e.y}))),f=i/(u-r),v=a/(d-c);n.forEach((function(e){void 0!==e.x&&f<1&&(e.x=(e.x-r)*f),void 0!==e.y&&v<1&&(e.y=(e.y-c)*v)})),n.forEach((function(e){e.sizeTemp=e.size,e.size=[10,10]})),(0,l.default)(n,o),n.forEach((function(e){e.size=e.sizeTemp||[],delete e.sizeTemp})),(0,s.default)({nodes:n,edges:o},t),e()}))}));return _}t.default=f},6299:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.arrayToTextureData=t.attributesToTextureData=t.buildTextureDataWithTwoEdgeAttr=t.buildTextureData=t.proccessToFunc=void 0;var r=n("f271"),o=n("19d2"),i=function(e,t){var n;return n=e?(0,r.isNumber)(e)?function(){return e}:e:function(){return t||1},n};t.proccessToFunc=i;var a=function(e,t){var n=[],r=[],i={},a=0;for(a=0;a<e.length;a++){var u=e[a];i[u.id]=a,n.push(u.x),n.push(u.y),n.push(0),n.push(0),r.push([])}for(a=0;a<t.length;a++){var c=t[a],s=(0,o.getEdgeTerminal)(c,"source"),l=(0,o.getEdgeTerminal)(c,"target");r[i[s]].push(i[l]),r[i[l]].push(i[s])}var d=0;for(a=0;a<e.length;a++){var f=n.length,v=r[a],p=v.length;n[4*a+2]=f,n[4*a+3]=v.length,d=Math.max(d,v.length);for(var y=0;y<p;++y){var g=v[y];n.push(+g)}}while(n.length%4!==0)n.push(0);return{maxEdgePerVetex:d,array:new Float32Array(n)}};t.buildTextureData=a;var u=function(e,t,n,r){var i=[],a=[],u={},c=0;for(c=0;c<e.length;c++){var s=e[c];u[s.id]=c,i.push(s.x),i.push(s.y),i.push(0),i.push(0),a.push([])}for(c=0;c<t.length;c++){var l=t[c],d=(0,o.getEdgeTerminal)(l,"source"),f=(0,o.getEdgeTerminal)(l,"target");a[u[d]].push(u[f]),a[u[d]].push(n(l)),a[u[d]].push(r(l)),a[u[d]].push(0),a[u[f]].push(u[d]),a[u[f]].push(n(l)),a[u[f]].push(r(l)),a[u[f]].push(0)}var v=0;for(c=0;c<e.length;c++){var p=i.length,y=a[c],g=y.length;i[4*c+2]=p+1048576*g/4,i[4*c+3]=0,v=Math.max(v,g/4);for(var h=0;h<g;++h){var _=y[h];i.push(+_)}}while(i.length%4!==0)i.push(0);return{maxEdgePerVetex:v,array:new Float32Array(i)}};t.buildTextureDataWithTwoEdgeAttr=u;var c=function(e,t){var n=[],r=e.length,o={};return t.forEach((function(t){e.forEach((function(e,i){if(void 0===o[t[e]]&&(o[t[e]]=Object.keys(o).length),n.push(o[t[e]]),i===r-1)while(n.length%4!==0)n.push(0)}))})),{array:new Float32Array(n),count:Object.keys(o).length}};t.attributesToTextureData=c;var s=function(e){for(var t=[],n=e.length,r=e[0].length,o=function(r){e.forEach((function(e,o){if(t.push(e[r]),o===n-1)while(t.length%4!==0)t.push(0)}))},i=0;i<r;i++)o(i);return new Float32Array(t)};t.arrayToTextureData=s},"634b":function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),o(n("711c"),t)},7049:function(e,t,n){"use strict";var r=this&&this.__extends||function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!==typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(t,"__esModule",{value:!0}),t.getLayoutByName=t.unRegisterLayout=t.registerLayout=void 0;var o=n("5bc9"),i=n("f271"),a=new Map,u=function(e,t){if(a.get(e)&&console.warn("The layout with the name ".concat(e," exists already, it will be overridden")),(0,i.isObject)(t)){var n=function(e){function n(n){var r=e.call(this)||this,o=r,i={},a=o.getDefaultCfg();return Object.assign(i,a,t,n),Object.keys(i).forEach((function(e){var t=i[e];o[e]=t})),r}return r(n,e),n}(o.Base);a.set(e,n)}else a.set(e,t);return a.get(e)};t.registerLayout=u;var c=function(e){a.has(e)&&a.delete(e)};t.unRegisterLayout=c;var s=function(e){return a.has(e)?a.get(e):null};t.getLayoutByName=s},"711c":function(e,t,n){"use strict";var r=this&&this.__extends||function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!==typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),o=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.RadialLayout=void 0;var i=n("f271"),a=n("5bc9"),u=o(n("3ddc")),c=o(n("5401"));function s(e){for(var t=e.length,n=e[0].length,r=[],o=0;o<t;o++){for(var i=[],a=0;a<n;a++)0!==e[o][a]?i.push(1/(e[o][a]*e[o][a])):i.push(0);r.push(i)}return r}function l(e,t){var n=-1;return e.forEach((function(e,r){e.id===t&&(n=r)})),n}function d(e,t){return Math.sqrt((e[0]-t[0])*(e[0]-t[0])+(e[1]-t[1])*(e[1]-t[1]))}var f=function(e){function t(t){var n=e.call(this)||this;return n.maxIteration=1e3,n.focusNode=null,n.unitRadius=null,n.linkDistance=50,n.preventOverlap=!1,n.strictRadial=!0,n.maxPreventOverlapIteration=200,n.sortStrength=10,n.nodes=[],n.edges=[],n.updateCfg(t),n}return r(t,e),t.prototype.getDefaultCfg=function(){return{maxIteration:1e3,focusNode:null,unitRadius:null,linkDistance:50,preventOverlap:!1,nodeSize:void 0,nodeSpacing:void 0,strictRadial:!0,maxPreventOverlapIteration:200,sortBy:void 0,sortStrength:10}},t.prototype.execute=function(){var e=this,t=e.nodes,n=e.edges||[];if(t&&0!==t.length){e.width||"undefined"===typeof window||(e.width=window.innerWidth),e.height||"undefined"===typeof window||(e.height=window.innerHeight),e.center||(e.center=[e.width/2,e.height/2]);var r=e.center;if(1===t.length)return t[0].x=r[0],t[0].y=r[1],void(e.onLayoutEnd&&e.onLayoutEnd());var o=e.linkDistance,a=null;if((0,i.isString)(e.focusNode)){for(var d=!1,f=0;f<t.length;f++)t[f].id===e.focusNode&&(a=t[f],e.focusNode=a,d=!0,f=t.length);d||(a=null)}else a=e.focusNode;a||(a=t[0],e.focusNode=a);var v=l(t,a.id);v<0&&(v=0),e.focusIndex=v;var p=(0,i.getAdjMatrix)({nodes:t,edges:n},!1),y=(0,i.floydWarshall)(p),g=e.maxToFocus(y,v);e.handleInfinity(y,v,g+1),e.distances=y;var h=y[v],_=e.width||500,x=e.height||500,m=_-r[0]>r[0]?r[0]:_-r[0],b=x-r[1]>r[1]?r[1]:x-r[1];0===m&&(m=_/2),0===b&&(b=x/2);var D=b>m?m:b,I=Math.max.apply(Math,h),w=[];h.forEach((function(t,n){e.unitRadius||(e.unitRadius=D/I),w[n]=t*e.unitRadius})),e.radii=w;var G=e.eIdealDisMatrix();e.eIdealDistances=G;var S=s(G);e.weights=S;var k=new u.default({linkDistance:o,distances:G}),N=k.layout();N.forEach((function(e){(0,i.isNaN)(e[0])&&(e[0]=Math.random()*o),(0,i.isNaN)(e[1])&&(e[1]=Math.random()*o)})),e.positions=N,N.forEach((function(e,n){t[n].x=e[0]+r[0],t[n].y=e[1]+r[1]})),N.forEach((function(e){e[0]-=N[v][0],e[1]-=N[v][1]})),e.run();var z,E=e.preventOverlap,P=e.nodeSize,M=e.strictRadial;if(E){var C,T=e.nodeSpacing;C=(0,i.isNumber)(T)?function(){return T}:(0,i.isFunction)(T)?T:function(){return 0},z=P?(0,i.isArray)(P)?function(e){var t=P[0]>P[1]?P[0]:P[1];return t+C(e)}:function(e){return P+C(e)}:function(e){if(e.size){if((0,i.isArray)(e.size)){var t=e.size[0]>e.size[1]?e.size[0]:e.size[1];return t+C(e)}if((0,i.isObject)(e.size)){t=e.size.width>e.size.height?e.size.width:e.size.height;return t+C(e)}return e.size+C(e)}return 10+C(e)};var U={nodes:t,nodeSizeFunc:z,adjMatrix:p,positions:N,radii:w,height:x,width:_,strictRadial:M,focusID:v,iterations:e.maxPreventOverlapIteration||200,k:N.length/4.5},O=new c.default(U);N=O.layout()}return N.forEach((function(e,n){t[n].x=e[0]+r[0],t[n].y=e[1]+r[1]})),e.onLayoutEnd&&e.onLayoutEnd(),{nodes:t,edges:n}}e.onLayoutEnd&&e.onLayoutEnd()},t.prototype.run=function(){for(var e=this,t=e.maxIteration,n=e.positions||[],r=e.weights||[],o=e.eIdealDistances||[],i=e.radii||[],a=0;a<=t;a++){var u=a/t;e.oneIteration(u,n,i,o,r)}},t.prototype.oneIteration=function(e,t,n,r,o){var i=this,a=1-e,u=i.focusIndex;t.forEach((function(i,c){var s=d(i,[0,0]),l=0===s?0:1/s;if(c!==u){var f=0,v=0,p=0;t.forEach((function(e,t){if(c!==t){var n=d(i,e),a=0===n?0:1/n,u=r[t][c];p+=o[c][t],f+=o[c][t]*(e[0]+u*(i[0]-e[0])*a),v+=o[c][t]*(e[1]+u*(i[1]-e[1])*a)}}));var y=0===n[c]?0:1/n[c];p*=a,p+=e*y*y,f*=a,f+=e*y*i[0]*l,i[0]=f/p,v*=a,v+=e*y*i[1]*l,i[1]=v/p}}))},t.prototype.eIdealDisMatrix=function(){var e=this,t=e.nodes;if(!t)return[];var n=e.distances,r=e.linkDistance,o=e.radii||[],a=e.unitRadius||50,u=[];return n&&n.forEach((function(n,c){var s=[];n.forEach((function(n,u){if(c===u)s.push(0);else if(o[c]===o[u])if("data"===e.sortBy)s.push(n*(Math.abs(c-u)*e.sortStrength)/(o[c]/a));else if(e.sortBy){var l=t[c][e.sortBy]||0,d=t[u][e.sortBy]||0;(0,i.isString)(l)&&(l=l.charCodeAt(0)),(0,i.isString)(d)&&(d=d.charCodeAt(0)),s.push(n*(Math.abs(l-d)*e.sortStrength)/(o[c]/a))}else s.push(n*r/(o[c]/a));else{var f=(r+a)/2;s.push(n*f)}})),u.push(s)})),u},t.prototype.handleInfinity=function(e,t,n){for(var r=e.length,o=0;o<r;o++)if(e[t][o]===1/0){e[t][o]=n,e[o][t]=n;for(var i=0;i<r;i++)e[o][i]!==1/0&&e[t][i]===1/0&&(e[t][i]=n+e[o][i],e[i][t]=n+e[o][i])}for(o=0;o<r;o++)if(o!==t)for(i=0;i<r;i++)if(e[o][i]===1/0){var a=Math.abs(e[t][o]-e[t][i]);a=0===a?1:a,e[o][i]=a}},t.prototype.maxToFocus=function(e,t){for(var n=0,r=0;r<e[t].length;r++)e[t][r]!==1/0&&(n=e[t][r]>n?e[t][r]:n);return n},t.prototype.getType=function(){return"radial"},t}(a.Base);t.RadialLayout=f},"7ef3":function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=r(n("1231"));function i(e,t){if(!e.nodes||0===e.nodes.length)return e;var n=t.width,r=t.height,i=t.nodeMinGap,a=1e4,u=1e4;e.nodes.forEach((function(e){var t=e.size[0]||50,n=e.size[1]||50;a=Math.min(t,a),u=Math.min(n,u)}));var c=new o.default;c.init(n,r,{CELL_H:u,CELL_W:a}),e.nodes.forEach((function(e){var t=c.occupyNearest(e);t&&(t.node={id:e.id,size:e.size},e.x=t.x,e.y=t.y,e.dx=t.dx,e.dy=t.dy)}));for(var s=0;s<e.nodes.length;s++){var l=e.nodes[s],d=c.findGridByNodeId(l.id);if(!d)throw new Error("can not find node cell");var f=d.column,v=d.row;if(l.size[0]+i>a){for(var p=Math.ceil((l.size[0]+i)/a)-1,y=p,g=0;g<p;g++){var h=c.additionColumn.indexOf(f+g+1)>-1;if(!h||c.cells[f+g+1][v].node)break;y--}c.insertColumn(f,y)}if(l.size[1]+i>u){for(p=Math.ceil((l.size[1]+i)/u)-1,y=p,g=0;g<p;g++){h=c.additionRow.indexOf(v+g+1)>-1;if(!h||c.cells[f][v+g+1].node)break;y--}c.insertRow(v,y)}}for(s=0;s<c.columnNum;s++){var _=function(t){var n=c.cells[s][t];if(n.node){var r=e.nodes.find((function(e){var t;return e.id===(null===(t=null===n||void 0===n?void 0:n.node)||void 0===t?void 0:t.id)}));r&&(r.x=n.x+r.size[0]/2,r.y=n.y+r.size[1]/2)}};for(g=0;g<c.rowNum;g++)_(g)}}t.default=i},"82cc":function(e,t,n){"use strict";var r=this&&this.__extends||function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!==typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(t,"__esModule",{value:!0}),t.MDSLayout=void 0;var o=n("b4ae"),i=n("f271"),a=n("5bc9"),u=function(e){function t(t){var n=e.call(this)||this;return n.center=[0,0],n.linkDistance=50,n.nodes=[],n.edges=[],n.onLayoutEnd=function(){},n.updateCfg(t),n}return r(t,e),t.prototype.getDefaultCfg=function(){return{center:[0,0],linkDistance:50}},t.prototype.execute=function(){var e=this,t=e.nodes,n=e.edges,r=void 0===n?[]:n,o=e.center;if(t&&0!==t.length){if(1===t.length)return t[0].x=o[0],t[0].y=o[1],void(e.onLayoutEnd&&e.onLayoutEnd());var a=e.linkDistance,u=(0,i.getAdjMatrix)({nodes:t,edges:r},!1),c=(0,i.floydWarshall)(u);e.handleInfinity(c);var s=(0,i.scaleMatrix)(c,a);e.scaledDistances=s;var l=e.runMDS();return e.positions=l,l.forEach((function(e,n){t[n].x=e[0]+o[0],t[n].y=e[1]+o[1]})),e.onLayoutEnd&&e.onLayoutEnd(),{nodes:t,edges:r}}e.onLayoutEnd&&e.onLayoutEnd()},t.prototype.runMDS=function(){var e=this,t=2,n=e.scaledDistances,r=o.Matrix.mul(o.Matrix.pow(n,2),-.5),i=r.mean("row"),a=r.mean("column"),u=r.mean();r.add(u).subRowVector(i).subColumnVector(a);var c=new o.SingularValueDecomposition(r),s=o.Matrix.sqrt(c.diagonalMatrix).diagonal();return c.leftSingularVectors.toJSON().map((function(e){return o.Matrix.mul([e],[s]).toJSON()[0].splice(0,t)}))},t.prototype.handleInfinity=function(e){var t=-999999;e.forEach((function(e){e.forEach((function(e){e!==1/0&&t<e&&(t=e)}))})),e.forEach((function(n,r){n.forEach((function(n,o){n===1/0&&(e[r][o]=t)}))}))},t.prototype.getType=function(){return"mds"},t}(a.Base);t.MDSLayout=u},"871d":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e){this.body=null,this.quad=null,this.NW=null,this.NE=null,this.SW=null,this.SE=null,this.theta=.5,null!=e&&(this.quad=e)}return e.prototype.insert=function(t){null!=this.body?this._isExternal()?(this.quad&&(this.NW=new e(this.quad.NW()),this.NE=new e(this.quad.NE()),this.SW=new e(this.quad.SW()),this.SE=new e(this.quad.SE())),this._putBody(this.body),this._putBody(t),this.body=this.body.add(t)):(this.body=this.body.add(t),this._putBody(t)):this.body=t},e.prototype._putBody=function(e){this.quad&&(e.in(this.quad.NW())&&this.NW?this.NW.insert(e):e.in(this.quad.NE())&&this.NE?this.NE.insert(e):e.in(this.quad.SW())&&this.SW?this.SW.insert(e):e.in(this.quad.SE())&&this.SE&&this.SE.insert(e))},e.prototype._isExternal=function(){return null==this.NW&&null==this.NE&&null==this.SW&&null==this.SE},e.prototype.updateForce=function(e){if(null!=this.body&&e!==this.body)if(this._isExternal())e.addForce(this.body);else{var t=this.quad?this.quad.getLength():0,n=this.body.distanceTo(e);t/n<this.theta?e.addForce(this.body):(this.NW&&this.NW.updateForce(e),this.NE&&this.NE.updateForce(e),this.SW&&this.SW.updateForce(e),this.SE&&this.SE.updateForce(e))}},e}();t.default=r},"910e":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isArray=void 0,t.isArray=Array.isArray},"927c":function(e,t,n){"use strict";var r=this&&this.__extends||function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!==typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),o=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.ERLayout=void 0;var i=n("5bc9"),a=o(n("6098")),u=function(e){function t(t){var n=e.call(this)||this;return n.width=300,n.height=300,n.nodeMinGap=50,n.onLayoutEnd=function(){},t&&n.updateCfg(t),n}return r(t,e),t.prototype.getDefaultCfg=function(){return{width:300,height:300,nodeMinGap:50}},t.prototype.execute=function(){var e=this,t=e.nodes,n=e.edges;return null===t||void 0===t||t.forEach((function(e){e.size||(e.size=[50,50])})),(0,a.default)({nodes:t,edges:n},{width:this.width,height:this.height,nodeMinGap:this.nodeMinGap}).then((function(){e.onLayoutEnd&&e.onLayoutEnd()}))},t.prototype.getType=function(){return"er"},t}(i.Base);t.ERLayout=u},"9a24":function(e,t,n){"use strict";var r=this&&this.__extends||function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!==typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(t,"__esModule",{value:!0}),t.GridLayout=void 0;var o=n("f271"),i=n("5bc9"),a=function(e){function t(t){var n=e.call(this)||this;return n.begin=[0,0],n.preventOverlap=!0,n.preventOverlapPadding=10,n.condense=!1,n.sortBy="degree",n.nodes=[],n.edges=[],n.width=300,n.height=300,n.row=0,n.col=0,n.cellWidth=0,n.cellHeight=0,n.cellUsed={},n.id2manPos={},n.onLayoutEnd=function(){},n.updateCfg(t),n}return r(t,e),t.prototype.getDefaultCfg=function(){return{begin:[0,0],preventOverlap:!0,preventOverlapPadding:10,condense:!1,rows:void 0,cols:void 0,position:void 0,sortBy:"degree",nodeSize:30}},t.prototype.execute=function(){var e=this,t=e.nodes,n=e.edges,r=e.begin,i=t.length;if(0===i)return e.onLayoutEnd&&e.onLayoutEnd(),{nodes:t,edges:n};if(1===i)return t[0].x=r[0],t[0].y=r[1],e.onLayoutEnd&&e.onLayoutEnd(),{nodes:t,edges:n};var a=e.sortBy,u=e.width,c=e.height,s=e.condense,l=e.preventOverlapPadding,d=e.preventOverlap,f=e.nodeSpacing,v=e.nodeSize,p=[];t.forEach((function(e){p.push(e)}));var y={};if(p.forEach((function(e,t){y[e.id]=t})),("degree"===a||!(0,o.isString)(a)||void 0===p[0][a])&&(a="degree",(0,o.isNaN)(t[0].degree))){var g=(0,o.getDegree)(p.length,y,n);p.forEach((function(e,t){e.degree=g[t]}))}p.sort((function(e,t){return t[a]-e[a]})),u||"undefined"===typeof window||(u=window.innerWidth),c||"undefined"===typeof window||(c=window.innerHeight);var h=e.rows,_=null!=e.cols?e.cols:e.columns;if(e.cells=i,null!=h&&null!=_?(e.rows=h,e.cols=_):null!=h&&null==_?(e.rows=h,e.cols=Math.ceil(e.cells/e.rows)):null==h&&null!=_?(e.cols=_,e.rows=Math.ceil(e.cells/e.cols)):(e.splits=Math.sqrt(e.cells*e.height/e.width),e.rows=Math.round(e.splits),e.cols=Math.round(e.width/e.height*e.splits)),e.rows=Math.max(e.rows,1),e.cols=Math.max(e.cols,1),e.cols*e.rows>e.cells){var x=e.small(),m=e.large();(x-1)*m>=e.cells?e.small(x-1):(m-1)*x>=e.cells&&e.large(m-1)}else while(e.cols*e.rows<e.cells){x=e.small(),m=e.large();(m+1)*x>=e.cells?e.large(m+1):e.small(x+1)}if(e.cellWidth=u/e.cols,e.cellHeight=c/e.rows,s&&(e.cellWidth=0,e.cellHeight=0),d||f){var b=(0,o.getFuncByUnknownType)(10,f),D=(0,o.getFuncByUnknownType)(30,v,!1);p.forEach((function(t){t.x&&t.y||(t.x=0,t.y=0);var n=D(t),r=n[0],o=void 0===r?30:r,i=n[1],a=void 0===i?30:i,u=void 0!==b?b(t):l,c=o+u,s=a+u;e.cellWidth=Math.max(e.cellWidth,c),e.cellHeight=Math.max(e.cellHeight,s)}))}e.cellUsed={},e.row=0,e.col=0,e.id2manPos={};for(var I=0;I<p.length;I++){var w=p[I],G=void 0;if(e.position&&(G=e.position(w)),G&&(void 0!==G.row||void 0!==G.col)){var S={row:G.row,col:G.col};if(void 0===S.col){S.col=0;while(e.used(S.row,S.col))S.col++}else if(void 0===S.row){S.row=0;while(e.used(S.row,S.col))S.row++}e.id2manPos[w.id]=S,e.use(S.row,S.col)}e.getPos(w)}return e.onLayoutEnd&&e.onLayoutEnd(),{edges:n,nodes:p}},t.prototype.small=function(e){var t,n=this,r=n.rows||5,o=n.cols||5;if(null==e)t=Math.min(r,o);else{var i=Math.min(r,o);i===n.rows?n.rows=e:n.cols=e}return t},t.prototype.large=function(e){var t,n=this,r=n.rows||5,o=n.cols||5;if(null==e)t=Math.max(r,o);else{var i=Math.max(r,o);i===n.rows?n.rows=e:n.cols=e}return t},t.prototype.used=function(e,t){var n=this;return n.cellUsed["c-".concat(e,"-").concat(t)]||!1},t.prototype.use=function(e,t){var n=this;n.cellUsed["c-".concat(e,"-").concat(t)]=!0},t.prototype.moveToNextCell=function(){var e=this,t=e.cols||5;e.col++,e.col>=t&&(e.col=0,e.row++)},t.prototype.getPos=function(e){var t,n,r=this,o=r.begin,i=r.cellWidth,a=r.cellHeight,u=r.id2manPos[e.id];if(u)t=u.col*i+i/2+o[0],n=u.row*a+a/2+o[1];else{while(r.used(r.row,r.col))r.moveToNextCell();t=r.col*i+i/2+o[0],n=r.row*a+a/2+o[1],r.use(r.row,r.col),r.moveToNextCell()}e.x=t,e.y=n},t.prototype.getType=function(){return"grid"},t}(i.Base);t.GridLayout=a},aac1:function(e,t,n){"use strict";var r=this&&this.__extends||function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!==typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(t,"__esModule",{value:!0}),t.GForceLayout=void 0;var o=n("5bc9"),i=n("f271"),a=function(e,t){var n;return n=e?(0,i.isNumber)(e)?function(t){return e}:e:function(e){return t||1},n},u=function(e){function t(t){var n=e.call(this)||this;return n.maxIteration=500,n.workerEnabled=!1,n.edgeStrength=200,n.nodeStrength=1e3,n.coulombDisScale=.005,n.damping=.9,n.maxSpeed=1e3,n.minMovement=.5,n.interval=.02,n.factor=1,n.linkDistance=1,n.gravity=10,n.preventOverlap=!0,n.collideStrength=1,n.tick=function(){},n.nodes=[],n.edges=[],n.width=300,n.height=300,n.nodeMap={},n.nodeIdxMap={},n.animate=!0,n.updateCfg(t),n}return r(t,e),t.prototype.getDefaultCfg=function(){return{maxIteration:500,gravity:10,enableTick:!0,animate:!0}},t.prototype.execute=function(){var e,t,n=this,r=n.nodes;if(void 0!==n.timeInterval&&"undefined"!==typeof window&&window.clearInterval(n.timeInterval),r&&0!==r.length){n.width||"undefined"===typeof window||(n.width=window.innerWidth),n.height||"undefined"===typeof window||(n.height=window.innerHeight),n.center||(n.center=[n.width/2,n.height/2]);var o=n.center;if(1===r.length)return r[0].x=o[0],r[0].y=o[1],void(null===(t=n.onLayoutEnd)||void 0===t||t.call(n));var u={},c={};r.forEach((function(e,t){(0,i.isNumber)(e.x)||(e.x=Math.random()*n.width),(0,i.isNumber)(e.y)||(e.y=Math.random()*n.height),u[e.id]=e,c[e.id]=t})),n.nodeMap=u,n.nodeIdxMap=c,n.linkDistance=a(n.linkDistance,1),n.nodeStrength=a(n.nodeStrength,1),n.edgeStrength=a(n.edgeStrength,1);var s,l=n.nodeSize;if(n.preventOverlap){var d,f=n.nodeSpacing;d=(0,i.isNumber)(f)?function(){return f}:(0,i.isFunction)(f)?f:function(){return 0},s=l?(0,i.isArray)(l)?function(e){return Math.max(l[0],l[1])+d(e)}:function(e){return l+d(e)}:function(e){return e.size?(0,i.isArray)(e.size)?Math.max(e.size[0],e.size[1])+d(e):(0,i.isObject)(e.size)?Math.max(e.size.width,e.size.height)+d(e):e.size+d(e):10+d(e)}}n.nodeSize=s;var v=n.edges;n.degrees=(0,i.getDegree)(r.length,n.nodeIdxMap,v),n.getMass||(n.getMass=function(e){var t=e.mass||n.degrees[n.nodeIdxMap[e.id]]||1;return t}),n.run()}else null===(e=n.onLayoutEnd)||void 0===e||e.call(n)},t.prototype.run=function(){var e,t=this,n=t.maxIteration,r=t.nodes,o=t.workerEnabled,i=t.minMovement,a=t.animate;if(r)if(o||!a){for(var u=0;u<n;u++){var c=t.runOneStep(u);if(t.reachMoveThreshold(r,c,i))break}null===(e=t.onLayoutEnd)||void 0===e||e.call(t)}else{if("undefined"===typeof window)return;var s=0;this.timeInterval=window.setInterval((function(){var e,o;if(r){var a=t.runOneStep(s)||[];t.reachMoveThreshold(r,a,i)&&(null===(e=t.onLayoutEnd)||void 0===e||e.call(t),window.clearInterval(t.timeInterval)),s++,s>=n&&(null===(o=t.onLayoutEnd)||void 0===o||o.call(t),window.clearInterval(t.timeInterval))}}),0)}},t.prototype.reachMoveThreshold=function(e,t,n){var r=0;return e.forEach((function(e,n){var o=e.x-t[n].x,i=e.y-t[n].y;r+=Math.sqrt(o*o+i*i)})),r/=e.length,r<n},t.prototype.runOneStep=function(e){var t,n=this,r=n.nodes,o=n.edges,i=[],a=[];if(r){r.forEach((function(e,t){i[2*t]=0,i[2*t+1]=0,a[2*t]=0,a[2*t+1]=0})),n.calRepulsive(i,r),o&&n.calAttractive(i,o),n.calGravity(i,r);var u=Math.max(.02,n.interval-.002*e);n.updateVelocity(i,a,u,r);var c=[];return r.forEach((function(e){c.push({x:e.x,y:e.y})})),n.updatePosition(a,u,r),null===(t=n.tick)||void 0===t||t.call(n),c}},t.prototype.calRepulsive=function(e,t){var n=this,r=n.getMass,o=n.factor,i=n.coulombDisScale,a=n.preventOverlap,u=n.collideStrength,c=void 0===u?1:u,s=n.nodeStrength,l=n.nodeSize;t.forEach((function(n,u){var d=r?r(n):1;t.forEach((function(t,f){if(!(u>=f)){var v=n.x-t.x,p=n.y-t.y;0===v&&0===p&&(v=.01*Math.random(),p=.01*Math.random());var y=v*v+p*p,g=Math.sqrt(y),h=(g+.1)*i,_=v/g,x=p/g,m=.5*(s(n)+s(t))*o/(h*h),b=r?r(t):1;if(e[2*u]+=_*m,e[2*u+1]+=x*m,e[2*f]-=_*m,e[2*f+1]-=x*m,a&&(l(n)+l(t))/2>g){var D=c*(s(n)+s(t))*.5/y;e[2*u]+=_*D/d,e[2*u+1]+=x*D/d,e[2*f]-=_*D/b,e[2*f+1]-=x*D/b}}}))}))},t.prototype.calAttractive=function(e,t){var n=this,r=n.nodeMap,o=n.nodeIdxMap,a=n.linkDistance,u=n.edgeStrength,c=n.nodeSize,s=n.getMass;t.forEach((function(t,n){var l=(0,i.getEdgeTerminal)(t,"source"),d=(0,i.getEdgeTerminal)(t,"target"),f=r[l],v=r[d],p=v.x-f.x,y=v.y-f.y;0===p&&0===y&&(p=.01*Math.random(),y=.01*Math.random());var g=Math.sqrt(p*p+y*y),h=p/g,_=y/g,x=a(t,f,v)||1+(c(f)+c(f)||0)/2,m=x-g,b=m*u(t),D=o[l],I=o[d],w=s?s(f):1,G=s?s(v):1;e[2*D]-=h*b/w,e[2*D+1]-=_*b/w,e[2*I]+=h*b/G,e[2*I+1]+=_*b/G}))},t.prototype.calGravity=function(e,t){for(var n=this,r=n.center,o=n.gravity,a=n.degrees,u=t.length,c=0;c<u;c++){var s=t[c],l=s.x-r[0],d=s.y-r[1],f=o;if(n.getCenter){var v=n.getCenter(s,a[c]);v&&(0,i.isNumber)(v[0])&&(0,i.isNumber)(v[1])&&(0,i.isNumber)(v[2])&&(l=s.x-v[0],d=s.y-v[1],f=v[2])}f&&(e[2*c]-=f*l,e[2*c+1]-=f*d)}},t.prototype.updateVelocity=function(e,t,n,r){var o=this,i=n*o.damping;r.forEach((function(n,r){var a=e[2*r]*i||.01,u=e[2*r+1]*i||.01,c=Math.sqrt(a*a+u*u);if(c>o.maxSpeed){var s=o.maxSpeed/c;a*=s,u*=s}t[2*r]=a,t[2*r+1]=u}))},t.prototype.updatePosition=function(e,t,n){n.forEach((function(n,r){if((0,i.isNumber)(n.fx)&&(0,i.isNumber)(n.fy))return n.x=n.fx,void(n.y=n.fy);var o=e[2*r]*t,a=e[2*r+1]*t;n.x+=o,n.y+=a}))},t.prototype.stop=function(){this.timeInterval&&"undefined"!==typeof window&&window.clearInterval(this.timeInterval)},t.prototype.destroy=function(){var e=this;e.stop(),e.tick=null,e.nodes=null,e.edges=null,e.destroyed=!0},t.prototype.getType=function(){return"gForce"},t}(o.Base);t.GForceLayout=u},aaf2:function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e["default"]=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});var a=i(n("0a4a")),u=n("f271");function c(){function e(e){return function(){return e}}var t,n=function(e){return e.cluster},r=e(1),o=e(-1),i=e(100),c=e(.1),s=[0,0],l=[],d={},f=[],v=100,p=100,y={none:{x:0,y:0}},g=[],h="force",_=!0,x=.1;function m(e){if(!_)return m;t.tick(),S();for(var r=0,o=l.length,i=void 0,a=e*x;r<o;++r)i=l[r],i.vx+=(y[n(i)].x-i.x)*a,i.vy+=(y[n(i)].y-i.y)*a}function b(){l&&D()}function D(){if(l&&l.length){if(void 0===n(l[0]))throw Error("Couldnt find the grouping attribute for the nodes. Make sure to set it up with forceInABox.groupBy('clusterAttr') before calling .links()");var e=I();t=a.forceSimulation(e.nodes).force("x",a.forceX(v).strength(.1)).force("y",a.forceY(p).strength(.1)).force("collide",a.forceCollide((function(e){return e.r})).iterations(4)).force("charge",a.forceManyBody().strength(o)).force("links",a.forceLink(e.nodes.length?e.links:[]).distance(i).strength(c)),g=t.nodes(),S()}}function I(){var e=[],t=[],n={},r=[],o={},i=[];return o=w(l),i=G(f),r=Object.keys(o),r.forEach((function(t,r){var i=o[t];e.push({id:t,size:i.count,r:Math.sqrt(i.sumforceNodeSize/Math.PI)}),n[t]=r})),i.forEach((function(e){var r=(0,u.getEdgeTerminal)(e,"source"),o=(0,u.getEdgeTerminal)(e,"target"),i=n[r],a=n[o];void 0!==i&&void 0!==a&&t.push({source:i,target:a,count:e.count})})),{nodes:e,links:t}}function w(e){var t={};return e.forEach((function(e){var r=n(e);t[r]||(t[r]={count:0,sumforceNodeSize:0})})),e.forEach((function(e){var o=n(e),i=r(e),a=t[o];a.count=a.count+1,a.sumforceNodeSize=a.sumforceNodeSize+Math.PI*(i*i)*1.3,t[o]=a})),t}function G(e){var t={},n=[];e.forEach((function(e){var n=k(e),r=0;void 0!==t[n]&&(r=t[n]),r+=1,t[n]=r}));var r=Object.entries(t);return r.forEach((function(e){var t=e[0],r=e[1],o=t.split("~")[0],i=t.split("~")[1];void 0!==o&&void 0!==i&&n.push({source:o,target:i,count:r})})),n}function S(){return y={none:{x:0,y:0}},g.forEach((function(e){y[e.id]={x:e.x-s[0],y:e.y-s[1]}})),y}function k(e){var t=(0,u.getEdgeTerminal)(e,"source"),r=(0,u.getEdgeTerminal)(e,"target"),o=n(d[t]),i=n(d[r]);return o<=i?"".concat(o,"~").concat(i):"".concat(i,"~").concat(o)}function N(e){d={},e.forEach((function(e){d[e.id]=e}))}function z(e){return arguments.length?(h=e,b(),m):h}function E(e){return arguments.length?"string"===typeof e?(n=function(t){return t[e]},m):(n=e,m):n}function P(e){return arguments.length?(_=e,m):_}function M(e){return arguments.length?(x=e,m):x}function C(e){return arguments.length?(v=e,m):v}function T(e){return arguments.length?(p=e,m):p}function U(e){return arguments.length?(N(e||[]),l=e||[],m):l}function O(e){return arguments.length?(f=e||[],b(),m):f}function A(t){return arguments.length?(r="function"===typeof t?t:e(+t),b(),m):r}function W(t){return arguments.length?(o="function"===typeof t?t:e(+t),b(),m):o}function L(t){return arguments.length?(i="function"===typeof t?t:e(+t),b(),m):i}function j(t){return arguments.length?(c="function"===typeof t?t:e(+t),b(),m):c}function F(e){return arguments.length?(s=e,m):s}return m.initialize=function(e){l=e,b()},m.template=z,m.groupBy=E,m.enableGrouping=P,m.strength=M,m.centerX=C,m.centerY=T,m.nodes=U,m.links=O,m.forceNodeSize=A,m.nodeSize=m.forceNodeSize,m.forceCharge=W,m.forceLinkDistance=L,m.forceLinkStrength=j,m.offset=F,m.getFocis=S,m}t.default=c},b3dc:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.aveMovementBundle=t.aveMovementCode=t.gForceBundle=t.gForceCode=void 0,t.gForceCode="\nimport { globalInvocationID } from 'g-webgpu';\n\nconst MAX_EDGE_PER_VERTEX;\nconst VERTEX_COUNT;\nconst SHIFT_20 = 1048576;\n\n@numthreads(1, 1, 1)\nclass GGForce {\n  @in @out\n  u_Data: vec4[];\n\n  @in\n  u_damping: float;\n  \n  @in\n  u_maxSpeed: float;\n\n  @in\n  u_minMovement: float;\n\n  @in\n  u_AveMovement: vec4[];\n\n  @in\n  u_coulombDisScale: float;\n\n  @in\n  u_factor: float;\n\n  @in\n  u_NodeAttributeArray1: vec4[];\n\n  @in\n  u_NodeAttributeArray2: vec4[];\n\n  @in\n  u_interval: float;\n\n  unpack_float(packedValue: float): ivec2 {\n    const packedIntValue = int(packedValue);\n    const v0 = packedIntValue / SHIFT_20;\n    return [v0, packedIntValue - v0 * SHIFT_20];\n  }\n\n  calcRepulsive(i: int, currentNode: vec4): vec2 {\n    let ax = 0, ay = 0;\n    for (let j: int = 0; j < VERTEX_COUNT; j++) {\n      if (i != j) {\n        const nextNode = this.u_Data[j];\n        const vx = currentNode[0] - nextNode[0];\n        const vy = currentNode[1] - nextNode[1];\n        const dist = sqrt(vx * vx + vy * vy) + 0.01;\n        const n_dist = (dist + 0.1) * this.u_coulombDisScale;\n        const direx = vx / dist;\n        const direy = vy / dist;\n        const attributesi = this.u_NodeAttributeArray1[i];\n        const attributesj = this.u_NodeAttributeArray1[j];\n        const massi = attributesi[0];\n        const nodeStrengthi = attributesi[2];\n        const nodeStrengthj = attributesj[2];\n        const nodeStrength = (nodeStrengthi + nodeStrengthj) / 2;\n        // const param = nodeStrength * this.u_factor / (n_dist * n_dist * massi);\n        const param = nodeStrength * this.u_factor / (n_dist * n_dist);\n        ax += direx * param;\n        ay += direy * param;\n      }\n    }\n    return [ax, ay];\n  }\n\n  calcGravity(i: int, currentNode: vec4, attributes2: vec4): vec2 {\n    // note: attributes2 = [centerX, centerY, gravity, 0]\n\n    const vx = currentNode[0] - attributes2[0];\n    const vy = currentNode[1] - attributes2[1];\n    \n    const ax = vx * attributes2[2];\n    const ay = vy * attributes2[2];\n    \n    return [ax, ay];\n  }\n\n  calcAttractive(i: int, currentNode: vec4, attributes1: vec4): vec2 {\n    // note: attributes1 = [mass, degree, nodeSterngth, 0]\n\n    const mass = attributes1[0];\n    let ax = 0, ay = 0;\n    // const arr_offset = int(floor(currentNode[2] + 0.5));\n    // const length = int(floor(currentNode[3] + 0.5));\n\n    const compressed = this.unpack_float(currentNode[2]);\n    const length = compressed[0];\n    const arr_offset = compressed[1];\n\n    const node_buffer: vec4;\n    for (let p: int = 0; p < MAX_EDGE_PER_VERTEX; p++) {\n      if (p >= length) break;\n      const arr_idx = arr_offset + 4 * p; // i 节点的第 p 条边开始的小格子位置\n      const buf_offset = arr_idx - arr_idx / 4 * 4;\n      if (p == 0 || buf_offset == 0) {\n        node_buffer = this.u_Data[int(arr_idx / 4)]; // 大格子，大格子位置=小个子位置 / 4，\n      }\n\n      let float_j: float = node_buffer[0];\n\n      const nextNode = this.u_Data[int(float_j)];\n      const vx = nextNode[0] - currentNode[0];\n      const vy = nextNode[1] - currentNode[1];\n      const dist = sqrt(vx * vx + vy * vy) + 0.01;\n      const direx = vx / dist;\n      const direy = vy / dist;\n      const edgeLength = node_buffer[1];\n      const edgeStrength = node_buffer[2];\n      const diff: float = edgeLength - dist;//edgeLength\n      // const param = diff * this.u_stiffness / mass; //\n      const param = diff * edgeStrength / mass; // \n      ax -= direx * param;\n      ay -= direy * param;\n    }\n    return [ax, ay];\n  }\n\n  @main\n  compute() {\n    const i = globalInvocationID.x;\n    const currentNode = this.u_Data[i];\n    const movement = u_AveMovement[0];\n    let ax = 0, ay = 0;\n\n    if (i >= VERTEX_COUNT || movement.x < u_minMovement) {\n      this.u_Data[i] = currentNode;\n      return;\n    }\n\n    // 每个节点属性占两个数组中各一格\n    // [mass, degree, nodeStrength, fx]\n    const nodeAttributes1 = this.u_NodeAttributeArray1[i];\n    // [centerX, centerY, centerGravity, fy]\n    const nodeAttributes2 = this.u_NodeAttributeArray2[i];\n\n    // repulsive\n    const repulsive = this.calcRepulsive(i, currentNode);\n    ax += repulsive[0];\n    ay += repulsive[1];\n\n    // attractive\n    const attractive = this.calcAttractive(i, currentNode, nodeAttributes1);\n    ax += attractive[0];\n    ay += attractive[1];\n\n    // gravity\n    const gravity = this.calcGravity(i, currentNode, nodeAttributes2);\n    ax -= gravity[0];\n    ay -= gravity[1];\n\n    // speed\n    const param = this.u_interval * this.u_damping;\n    let vx = ax * param;\n    let vy = ay * param;\n    const vlength = sqrt(vx * vx + vy * vy) + 0.0001;\n    if (vlength > this.u_maxSpeed) {\n      const param2 = this.u_maxSpeed / vlength;\n      vx = param2 * vx;\n      vy = param2 * vy;\n    }\n\n    // move\n    if (nodeAttributes1[3] != 0 && nodeAttributes2[3] != 0) {\n      this.u_Data[i] = [\n        nodeAttributes1[3],\n        nodeAttributes2[3],\n        currentNode[2],\n        0\n      ];\n    } else {\n      const distx = vx * this.u_interval;\n      const disty = vy * this.u_interval;\n      const distLength = sqrt(distx * distx + disty * disty);\n      this.u_Data[i] = [\n        currentNode[0] + distx,\n        currentNode[1] + disty,\n        currentNode[2],\n        distLength\n      ];\n    }\n    \n    // the avarage move distance\n    // need to share memory\n    \n  }\n}\n",t.gForceBundle='{"shaders":{"WGSL":"import \\"GLSL.std.450\\" as std;\\n\\n\\n# var gWebGPUDebug : bool = false;\\n# var gWebGPUDebugOutput : vec4<f32> = vec4<f32>(0.0);\\n\\n[[builtin global_invocation_id]] var<in> globalInvocationID : vec3<u32>;\\n# [[builtin work_group_size]] var<in> workGroupSize : vec3<u32>;\\n# [[builtin work_group_id]] var<in> workGroupID : vec3<u32>;\\n[[builtin local_invocation_id]] var<in> localInvocationID : vec3<u32>;\\n# [[builtin num_work_groups]] var<in> numWorkGroups : vec3<u32>;\\n[[builtin local_invocation_idx]] var<in> localInvocationIndex : u32;\\n\\ntype GWebGPUParams = [[block]] struct {\\n  [[offset 0]] u_damping : f32;\\n  [[offset 4]] u_maxSpeed : f32;\\n  [[offset 8]] u_minMovement : f32;\\n  \\n  [[offset 12]] u_coulombDisScale : f32;\\n  [[offset 16]] u_factor : f32;\\n  \\n  \\n  [[offset 20]] u_interval : f32;\\n};\\n[[binding 0, set 0]] var<uniform> gWebGPUUniformParams : GWebGPUParams;\\ntype GWebGPUBuffer0 = [[block]] struct {\\n  [[offset 0]] u_Data : [[stride 16]] array<vec4<f32>>;\\n};\\n[[binding 1, set 0]] var<storage_buffer> gWebGPUBuffer0 : GWebGPUBuffer0;\\ntype GWebGPUBuffer1 = [[block]] struct {\\n  [[offset 0]] u_AveMovement : [[stride 16]] array<vec4<f32>>;\\n};\\n[[binding 2, set 0]] var<storage_buffer> gWebGPUBuffer1 : GWebGPUBuffer1;\\ntype GWebGPUBuffer2 = [[block]] struct {\\n  [[offset 0]] u_NodeAttributeArray1 : [[stride 16]] array<vec4<f32>>;\\n};\\n[[binding 3, set 0]] var<storage_buffer> gWebGPUBuffer2 : GWebGPUBuffer2;\\ntype GWebGPUBuffer3 = [[block]] struct {\\n  [[offset 0]] u_NodeAttributeArray2 : [[stride 16]] array<vec4<f32>>;\\n};\\n[[binding 4, set 0]] var<storage_buffer> gWebGPUBuffer3 : GWebGPUBuffer3;\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\nfn unpack_float(packedValue : f32) -> vec2<i32> {var packedIntValue : i32 = i32(packedValue);\\nvar v0 : i32 = packedIntValue / 1048576;\\nreturn vec2<i32>(v0, packedIntValue - (v0 * 1048576));}\\nfn calcRepulsive(i : i32, currentNode : vec4<f32>) -> vec2<f32> {var ax : f32 = 0.0;\\nvar ay : f32 = 0.0;\\nfor (var j : i32 = 0; j < __DefineValuePlaceholder__VERTEX_COUNT; j = j + 1) {if (i != j) {var nextNode : vec4<f32> = gWebGPUBuffer0.u_Data[j];\\nvar vx : f32 = currentNode.x - nextNode.x;\\nvar vy : f32 = currentNode.y - nextNode.y;\\nvar dist : f32 = std::sqrt((vx * vx) + (vy * vy)) + 0.01;\\nvar n_dist : f32 = (dist + 0.1) * gWebGPUUniformParams.u_coulombDisScale;\\nvar direx : f32 = vx / dist;\\nvar direy : f32 = vy / dist;\\nvar attributesi : vec4<f32> = gWebGPUBuffer2.u_NodeAttributeArray1[i];\\nvar attributesj : vec4<f32> = gWebGPUBuffer2.u_NodeAttributeArray1[j];\\nvar massi : f32 = attributesi.x;\\nvar nodeStrengthi : f32 = attributesi.z;\\nvar nodeStrengthj : f32 = attributesj.z;\\nvar nodeStrength : f32 = (nodeStrengthi + nodeStrengthj) / 2.0;\\nvar param : f32 = (nodeStrength * gWebGPUUniformParams.u_factor) / (n_dist * n_dist);\\nax = ax + direx * param;\\nay = ay + direy * param;}}\\nreturn vec2<f32>(ax, ay);}\\nfn calcGravity(i : i32, currentNode : vec4<f32>, attributes2 : vec4<f32>) -> vec2<f32> {var vx : f32 = currentNode.x - attributes2.x;\\nvar vy : f32 = currentNode.y - attributes2.y;\\nvar ax : f32 = vx * attributes2.z;\\nvar ay : f32 = vy * attributes2.z;\\nreturn vec2<f32>(ax, ay);}\\nfn calcAttractive(i : i32, currentNode : vec4<f32>, attributes1 : vec4<f32>) -> vec2<f32> {var mass : f32 = attributes1.x;\\nvar ax : f32 = 0.0;\\nvar ay : f32 = 0.0;\\nvar compressed : vec2<i32> = unpack_float(currentNode.z);\\nvar length : i32 = compressed.x;\\nvar arr_offset : i32 = compressed.y;\\nvar node_buffer : vec4<f32>;\\nfor (var p : i32 = 0; p < __DefineValuePlaceholder__MAX_EDGE_PER_VERTEX; p = p + 1) {if (p >= length) {break;}\\nvar arr_idx : i32 = arr_offset + (4 * p);\\nvar buf_offset : i32 = arr_idx - ((arr_idx / 4) * 4);\\nif ((p == 0) || (buf_offset == 0)) {node_buffer = gWebGPUBuffer0.u_Data[i32(arr_idx / 4)];}\\nvar float_j : f32 = node_buffer.x;\\nvar nextNode : vec4<f32> = gWebGPUBuffer0.u_Data[i32(float_j)];\\nvar vx : f32 = nextNode.x - currentNode.x;\\nvar vy : f32 = nextNode.y - currentNode.y;\\nvar dist : f32 = std::sqrt((vx * vx) + (vy * vy)) + 0.01;\\nvar direx : f32 = vx / dist;\\nvar direy : f32 = vy / dist;\\nvar edgeLength : f32 = node_buffer.y;\\nvar edgeStrength : f32 = node_buffer.z;\\nvar diff : f32 = edgeLength - dist;\\nvar param : f32 = (diff * edgeStrength) / mass;\\nax = ax - direx * param;\\nay = ay - direy * param;}\\nreturn vec2<f32>(ax, ay);}\\nfn main() -> void {var i : i32 = globalInvocationID.x;\\nvar currentNode : vec4<f32> = gWebGPUBuffer0.u_Data[i];\\nvar movement : vec4<f32> = gWebGPUBuffer1.u_AveMovement[0];\\nvar ax : f32 = 0.0;\\nvar ay : f32 = 0.0;\\nif ((i >= __DefineValuePlaceholder__VERTEX_COUNT) || (movement.x < gWebGPUUniformParams.u_minMovement)) {gWebGPUBuffer0.u_Data[i] = currentNode;\\nreturn ;}\\nvar nodeAttributes1 : vec4<f32> = gWebGPUBuffer2.u_NodeAttributeArray1[i];\\nvar nodeAttributes2 : vec4<f32> = gWebGPUBuffer3.u_NodeAttributeArray2[i];\\nvar repulsive : vec2<f32> = calcRepulsive(i, currentNode);\\nax = ax + repulsive.x;\\nay = ay + repulsive.y;\\nvar attractive : vec2<f32> = calcAttractive(i, currentNode, nodeAttributes1);\\nax = ax + attractive.x;\\nay = ay + attractive.y;\\nvar gravity : vec2<f32> = calcGravity(i, currentNode, nodeAttributes2);\\nax = ax - gravity.x;\\nay = ay - gravity.y;\\nvar param : f32 = gWebGPUUniformParams.u_interval * gWebGPUUniformParams.u_damping;\\nvar vx : f32 = ax * param;\\nvar vy : f32 = ay * param;\\nvar vlength : f32 = std::sqrt((vx * vx) + (vy * vy)) + 0.0001;\\nif (vlength > gWebGPUUniformParams.u_maxSpeed) {var param2 : f32 = gWebGPUUniformParams.u_maxSpeed / vlength;\\nvx = param2 * vx;\\nvy = param2 * vy;}\\nvar distx : f32 = vx * gWebGPUUniformParams.u_interval;\\nvar disty : f32 = vy * gWebGPUUniformParams.u_interval;\\nvar distLength : f32 = std::sqrt((distx * distx) + (disty * disty));\\nif ((nodeAttributes1.w != 0.0) && (nodeAttributes2.w != 0.0)) {gWebGPUBuffer0.u_Data[i] = vec4<f32>(nodeAttributes1.w, nodeAttributes2.w, currentNode.z, 0.0);}else {gWebGPUBuffer0.u_Data[i] = vec4<f32>(currentNode.x + distx, currentNode.y + disty, currentNode.z, distLength);}\\nreturn;}\\n\\nentry_point compute as \\"main\\" = main;\\n","GLSL450":"\\n\\n\\nbool gWebGPUDebug = false;\\nvec4 gWebGPUDebugOutput = vec4(0.0);\\n\\nivec3 globalInvocationID = ivec3(gl_GlobalInvocationID);\\nivec3 workGroupSize = ivec3(1,1,1);\\nivec3 workGroupID = ivec3(gl_WorkGroupID);\\nivec3 localInvocationID = ivec3(gl_LocalInvocationID);\\nivec3 numWorkGroups = ivec3(gl_NumWorkGroups);\\nint localInvocationIndex = int(gl_LocalInvocationIndex);\\n\\nlayout(std140, set = 0, binding = 0) uniform GWebGPUParams {\\n  float u_damping;\\n  float u_maxSpeed;\\n  float u_minMovement;\\n  \\n  float u_coulombDisScale;\\n  float u_factor;\\n  \\n  \\n  float u_interval;\\n} gWebGPUUniformParams;\\nlayout(std430, set = 0, binding = 1) buffer   GWebGPUBuffer0 {\\n  vec4 u_Data[];\\n} gWebGPUBuffer0;\\n\\nlayout(std430, set = 0, binding = 2) buffer readonly  GWebGPUBuffer1 {\\n  vec4 u_AveMovement[];\\n} gWebGPUBuffer1;\\n\\nlayout(std430, set = 0, binding = 3) buffer readonly  GWebGPUBuffer2 {\\n  vec4 u_NodeAttributeArray1[];\\n} gWebGPUBuffer2;\\n\\nlayout(std430, set = 0, binding = 4) buffer readonly  GWebGPUBuffer3 {\\n  vec4 u_NodeAttributeArray2[];\\n} gWebGPUBuffer3;\\n\\n\\n\\n#define MAX_EDGE_PER_VERTEX __DefineValuePlaceholder__MAX_EDGE_PER_VERTEX\\n#define VERTEX_COUNT __DefineValuePlaceholder__VERTEX_COUNT\\n#define SHIFT_20 1048576.0\\nlayout (\\n  local_size_x = 1,\\n  local_size_y = 1,\\n  local_size_z = 1\\n) in;\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\nivec2 unpack_float(float packedValue) {int packedIntValue = int(packedValue);\\nint v0 = packedIntValue / int(SHIFT_20);\\nreturn ivec2(v0, packedIntValue - (v0 * int(SHIFT_20)));}\\nvec2 calcRepulsive(int i, vec4 currentNode) {float ax = 0.0;\\nfloat ay = 0.0;\\nfor (int j = 0; j < VERTEX_COUNT; j++) {if (i != j) {vec4 nextNode = gWebGPUBuffer0.u_Data[j];\\nfloat vx = currentNode.x - nextNode.x;\\nfloat vy = currentNode.y - nextNode.y;\\nfloat dist = sqrt((vx * vx) + (vy * vy)) + 0.01;\\nfloat n_dist = (dist + 0.1) * gWebGPUUniformParams.u_coulombDisScale;\\nfloat direx = vx / dist;\\nfloat direy = vy / dist;\\nvec4 attributesi = gWebGPUBuffer2.u_NodeAttributeArray1[i];\\nvec4 attributesj = gWebGPUBuffer2.u_NodeAttributeArray1[j];\\nfloat massi = attributesi.x;\\nfloat nodeStrengthi = attributesi.z;\\nfloat nodeStrengthj = attributesj.z;\\nfloat nodeStrength = (nodeStrengthi + nodeStrengthj) / 2.0;\\nfloat param = (nodeStrength * gWebGPUUniformParams.u_factor) / (n_dist * n_dist);\\nax += direx * param;\\nay += direy * param;}}\\nreturn vec2(ax, ay);}\\nvec2 calcGravity(int i, vec4 currentNode, vec4 attributes2) {float vx = currentNode.x - attributes2.x;\\nfloat vy = currentNode.y - attributes2.y;\\nfloat ax = vx * attributes2.z;\\nfloat ay = vy * attributes2.z;\\nreturn vec2(ax, ay);}\\nvec2 calcAttractive(int i, vec4 currentNode, vec4 attributes1) {float mass = attributes1.x;\\nfloat ax = 0.0;\\nfloat ay = 0.0;\\nivec2 compressed = unpack_float(currentNode.z);\\nint length = compressed.x;\\nint arr_offset = compressed.y;\\nvec4 node_buffer;\\nfor (int p = 0; p < MAX_EDGE_PER_VERTEX; p++) {if (p >= length) {break;}\\nint arr_idx = arr_offset + (4 * p);\\nint buf_offset = arr_idx - ((arr_idx / 4) * 4);\\nif ((p == 0) || (buf_offset == 0)) {node_buffer = gWebGPUBuffer0.u_Data[int(arr_idx / 4)];}\\nfloat float_j = node_buffer.x;\\nvec4 nextNode = gWebGPUBuffer0.u_Data[int(float_j)];\\nfloat vx = nextNode.x - currentNode.x;\\nfloat vy = nextNode.y - currentNode.y;\\nfloat dist = sqrt((vx * vx) + (vy * vy)) + 0.01;\\nfloat direx = vx / dist;\\nfloat direy = vy / dist;\\nfloat edgeLength = node_buffer.y;\\nfloat edgeStrength = node_buffer.z;\\nfloat diff = edgeLength - dist;\\nfloat param = (diff * edgeStrength) / mass;\\nax -= direx * param;\\nay -= direy * param;}\\nreturn vec2(ax, ay);}\\nvoid main() {int i = globalInvocationID.x;\\nvec4 currentNode = gWebGPUBuffer0.u_Data[i];\\nvec4 movement = gWebGPUBuffer1.u_AveMovement[0];\\nfloat ax = 0.0;\\nfloat ay = 0.0;\\nif ((i >= VERTEX_COUNT) || (movement.x < gWebGPUUniformParams.u_minMovement)) {gWebGPUBuffer0.u_Data[i] = currentNode;\\nreturn ;}\\nvec4 nodeAttributes1 = gWebGPUBuffer2.u_NodeAttributeArray1[i];\\nvec4 nodeAttributes2 = gWebGPUBuffer3.u_NodeAttributeArray2[i];\\nvec2 repulsive = calcRepulsive(i, currentNode);\\nax += repulsive.x;\\nay += repulsive.y;\\nvec2 attractive = calcAttractive(i, currentNode, nodeAttributes1);\\nax += attractive.x;\\nay += attractive.y;\\nvec2 gravity = calcGravity(i, currentNode, nodeAttributes2);\\nax -= gravity.x;\\nay -= gravity.y;\\nfloat param = gWebGPUUniformParams.u_interval * gWebGPUUniformParams.u_damping;\\nfloat vx = ax * param;\\nfloat vy = ay * param;\\nfloat vlength = sqrt((vx * vx) + (vy * vy)) + 0.0001;\\nif (vlength > gWebGPUUniformParams.u_maxSpeed) {float param2 = gWebGPUUniformParams.u_maxSpeed / vlength;\\nvx = param2 * vx;\\nvy = param2 * vy;}\\nfloat distx = vx * gWebGPUUniformParams.u_interval;\\nfloat disty = vy * gWebGPUUniformParams.u_interval;\\nfloat distLength = sqrt((distx * distx) + (disty * disty));\\nif ((nodeAttributes1.w != 0.0) && (nodeAttributes2.w != 0.0)) {gWebGPUBuffer0.u_Data[i] = vec4(nodeAttributes1.w, nodeAttributes2.w, currentNode.z, 0.0);}else {gWebGPUBuffer0.u_Data[i] = vec4(currentNode.x + distx, currentNode.y + disty, currentNode.z, distLength);}}\\n","GLSL100":"\\n\\nfloat epsilon = 0.00001;\\nvec2 addrTranslation_1Dto2D(float address1D, vec2 texSize) {\\n  vec2 conv_const = vec2(1.0 / texSize.x, 1.0 / (texSize.x * texSize.y));\\n  vec2 normAddr2D = float(address1D) * conv_const;\\n  return vec2(fract(normAddr2D.x + epsilon), normAddr2D.y);\\n}\\n\\nvoid barrier() {}\\n  \\n\\nuniform vec2 u_OutputTextureSize;\\nuniform int u_OutputTexelCount;\\nvarying vec2 v_TexCoord;\\n\\nbool gWebGPUDebug = false;\\nvec4 gWebGPUDebugOutput = vec4(0.0);\\n\\n#define MAX_EDGE_PER_VERTEX __DefineValuePlaceholder__MAX_EDGE_PER_VERTEX\\n#define VERTEX_COUNT __DefineValuePlaceholder__VERTEX_COUNT\\n#define SHIFT_20 1048576.0\\n\\nuniform sampler2D u_Data;\\nuniform vec2 u_DataSize;\\nvec4 getDatau_Data(vec2 address2D) {\\n  return vec4(texture2D(u_Data, address2D).rgba);\\n}\\nvec4 getDatau_Data(float address1D) {\\n  return getDatau_Data(addrTranslation_1Dto2D(address1D, u_DataSize));\\n}\\nvec4 getDatau_Data(int address1D) {\\n  return getDatau_Data(float(address1D));\\n}\\nuniform float u_damping;\\nuniform float u_maxSpeed;\\nuniform float u_minMovement;\\nuniform sampler2D u_AveMovement;\\nuniform vec2 u_AveMovementSize;\\nvec4 getDatau_AveMovement(vec2 address2D) {\\n  return vec4(texture2D(u_AveMovement, address2D).rgba);\\n}\\nvec4 getDatau_AveMovement(float address1D) {\\n  return getDatau_AveMovement(addrTranslation_1Dto2D(address1D, u_AveMovementSize));\\n}\\nvec4 getDatau_AveMovement(int address1D) {\\n  return getDatau_AveMovement(float(address1D));\\n}\\nuniform float u_coulombDisScale;\\nuniform float u_factor;\\nuniform sampler2D u_NodeAttributeArray1;\\nuniform vec2 u_NodeAttributeArray1Size;\\nvec4 getDatau_NodeAttributeArray1(vec2 address2D) {\\n  return vec4(texture2D(u_NodeAttributeArray1, address2D).rgba);\\n}\\nvec4 getDatau_NodeAttributeArray1(float address1D) {\\n  return getDatau_NodeAttributeArray1(addrTranslation_1Dto2D(address1D, u_NodeAttributeArray1Size));\\n}\\nvec4 getDatau_NodeAttributeArray1(int address1D) {\\n  return getDatau_NodeAttributeArray1(float(address1D));\\n}\\nuniform sampler2D u_NodeAttributeArray2;\\nuniform vec2 u_NodeAttributeArray2Size;\\nvec4 getDatau_NodeAttributeArray2(vec2 address2D) {\\n  return vec4(texture2D(u_NodeAttributeArray2, address2D).rgba);\\n}\\nvec4 getDatau_NodeAttributeArray2(float address1D) {\\n  return getDatau_NodeAttributeArray2(addrTranslation_1Dto2D(address1D, u_NodeAttributeArray2Size));\\n}\\nvec4 getDatau_NodeAttributeArray2(int address1D) {\\n  return getDatau_NodeAttributeArray2(float(address1D));\\n}\\nuniform float u_interval;\\nivec2 unpack_float(float packedValue) {\\nivec3 workGroupSize = ivec3(1, 1, 1);\\nivec3 numWorkGroups = ivec3(1, 1, 1);     \\nint globalInvocationIndex = int(floor(v_TexCoord.x * u_OutputTextureSize.x))\\n  + int(floor(v_TexCoord.y * u_OutputTextureSize.y)) * int(u_OutputTextureSize.x);\\nint workGroupIDLength = globalInvocationIndex / (workGroupSize.x * workGroupSize.y * workGroupSize.z);\\nivec3 workGroupID = ivec3(workGroupIDLength / numWorkGroups.y / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.y);\\nint localInvocationIDZ = globalInvocationIndex / (workGroupSize.x * workGroupSize.y);\\nint localInvocationIDY = (globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y) / workGroupSize.x;\\nint localInvocationIDX = globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y - localInvocationIDY * workGroupSize.x;\\nivec3 localInvocationID = ivec3(localInvocationIDX, localInvocationIDY, localInvocationIDZ);\\nivec3 globalInvocationID = workGroupID * workGroupSize + localInvocationID;\\nint localInvocationIndex = localInvocationID.z * workGroupSize.x * workGroupSize.y\\n                + localInvocationID.y * workGroupSize.x + localInvocationID.x;\\nint packedIntValue = int(packedValue);\\nint v0 = packedIntValue / int(SHIFT_20);\\nreturn ivec2(v0, packedIntValue - (v0 * int(SHIFT_20)));}\\nvec2 calcRepulsive(int i, vec4 currentNode) {\\nivec3 workGroupSize = ivec3(1, 1, 1);\\nivec3 numWorkGroups = ivec3(1, 1, 1);     \\nint globalInvocationIndex = int(floor(v_TexCoord.x * u_OutputTextureSize.x))\\n  + int(floor(v_TexCoord.y * u_OutputTextureSize.y)) * int(u_OutputTextureSize.x);\\nint workGroupIDLength = globalInvocationIndex / (workGroupSize.x * workGroupSize.y * workGroupSize.z);\\nivec3 workGroupID = ivec3(workGroupIDLength / numWorkGroups.y / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.y);\\nint localInvocationIDZ = globalInvocationIndex / (workGroupSize.x * workGroupSize.y);\\nint localInvocationIDY = (globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y) / workGroupSize.x;\\nint localInvocationIDX = globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y - localInvocationIDY * workGroupSize.x;\\nivec3 localInvocationID = ivec3(localInvocationIDX, localInvocationIDY, localInvocationIDZ);\\nivec3 globalInvocationID = workGroupID * workGroupSize + localInvocationID;\\nint localInvocationIndex = localInvocationID.z * workGroupSize.x * workGroupSize.y\\n                + localInvocationID.y * workGroupSize.x + localInvocationID.x;\\nfloat ax = 0.0;\\nfloat ay = 0.0;\\nfor (int j = 0; j < VERTEX_COUNT; j++) {if (i != j) {vec4 nextNode = getDatau_Data(j);\\nfloat vx = currentNode.x - nextNode.x;\\nfloat vy = currentNode.y - nextNode.y;\\nfloat dist = sqrt((vx * vx) + (vy * vy)) + 0.01;\\nfloat n_dist = (dist + 0.1) * u_coulombDisScale;\\nfloat direx = vx / dist;\\nfloat direy = vy / dist;\\nvec4 attributesi = getDatau_NodeAttributeArray1(i);\\nvec4 attributesj = getDatau_NodeAttributeArray1(j);\\nfloat massi = attributesi.x;\\nfloat nodeStrengthi = attributesi.z;\\nfloat nodeStrengthj = attributesj.z;\\nfloat nodeStrength = (nodeStrengthi + nodeStrengthj) / 2.0;\\nfloat param = (nodeStrength * u_factor) / (n_dist * n_dist);\\nax += direx * param;\\nay += direy * param;}}\\nreturn vec2(ax, ay);}\\nvec2 calcGravity(int i, vec4 currentNode, vec4 attributes2) {\\nivec3 workGroupSize = ivec3(1, 1, 1);\\nivec3 numWorkGroups = ivec3(1, 1, 1);     \\nint globalInvocationIndex = int(floor(v_TexCoord.x * u_OutputTextureSize.x))\\n  + int(floor(v_TexCoord.y * u_OutputTextureSize.y)) * int(u_OutputTextureSize.x);\\nint workGroupIDLength = globalInvocationIndex / (workGroupSize.x * workGroupSize.y * workGroupSize.z);\\nivec3 workGroupID = ivec3(workGroupIDLength / numWorkGroups.y / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.y);\\nint localInvocationIDZ = globalInvocationIndex / (workGroupSize.x * workGroupSize.y);\\nint localInvocationIDY = (globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y) / workGroupSize.x;\\nint localInvocationIDX = globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y - localInvocationIDY * workGroupSize.x;\\nivec3 localInvocationID = ivec3(localInvocationIDX, localInvocationIDY, localInvocationIDZ);\\nivec3 globalInvocationID = workGroupID * workGroupSize + localInvocationID;\\nint localInvocationIndex = localInvocationID.z * workGroupSize.x * workGroupSize.y\\n                + localInvocationID.y * workGroupSize.x + localInvocationID.x;\\nfloat vx = currentNode.x - attributes2.x;\\nfloat vy = currentNode.y - attributes2.y;\\nfloat ax = vx * attributes2.z;\\nfloat ay = vy * attributes2.z;\\nreturn vec2(ax, ay);}\\nvec2 calcAttractive(int i, vec4 currentNode, vec4 attributes1) {\\nivec3 workGroupSize = ivec3(1, 1, 1);\\nivec3 numWorkGroups = ivec3(1, 1, 1);     \\nint globalInvocationIndex = int(floor(v_TexCoord.x * u_OutputTextureSize.x))\\n  + int(floor(v_TexCoord.y * u_OutputTextureSize.y)) * int(u_OutputTextureSize.x);\\nint workGroupIDLength = globalInvocationIndex / (workGroupSize.x * workGroupSize.y * workGroupSize.z);\\nivec3 workGroupID = ivec3(workGroupIDLength / numWorkGroups.y / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.y);\\nint localInvocationIDZ = globalInvocationIndex / (workGroupSize.x * workGroupSize.y);\\nint localInvocationIDY = (globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y) / workGroupSize.x;\\nint localInvocationIDX = globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y - localInvocationIDY * workGroupSize.x;\\nivec3 localInvocationID = ivec3(localInvocationIDX, localInvocationIDY, localInvocationIDZ);\\nivec3 globalInvocationID = workGroupID * workGroupSize + localInvocationID;\\nint localInvocationIndex = localInvocationID.z * workGroupSize.x * workGroupSize.y\\n                + localInvocationID.y * workGroupSize.x + localInvocationID.x;\\nfloat mass = attributes1.x;\\nfloat ax = 0.0;\\nfloat ay = 0.0;\\nivec2 compressed = unpack_float(currentNode.z);\\nint length = compressed.x;\\nint arr_offset = compressed.y;\\nvec4 node_buffer;\\nfor (int p = 0; p < MAX_EDGE_PER_VERTEX; p++) {if (p >= length) {break;}\\nint arr_idx = arr_offset + (4 * p);\\nint buf_offset = arr_idx - ((arr_idx / 4) * 4);\\nif ((p == 0) || (buf_offset == 0)) {node_buffer = getDatau_Data(int(arr_idx / 4));}\\nfloat float_j = node_buffer.x;\\nvec4 nextNode = getDatau_Data(int(float_j));\\nfloat vx = nextNode.x - currentNode.x;\\nfloat vy = nextNode.y - currentNode.y;\\nfloat dist = sqrt((vx * vx) + (vy * vy)) + 0.01;\\nfloat direx = vx / dist;\\nfloat direy = vy / dist;\\nfloat edgeLength = node_buffer.y;\\nfloat edgeStrength = node_buffer.z;\\nfloat diff = edgeLength - dist;\\nfloat param = (diff * edgeStrength) / mass;\\nax -= direx * param;\\nay -= direy * param;}\\nreturn vec2(ax, ay);}\\nvoid main() {\\nivec3 workGroupSize = ivec3(1, 1, 1);\\nivec3 numWorkGroups = ivec3(1, 1, 1);     \\nint globalInvocationIndex = int(floor(v_TexCoord.x * u_OutputTextureSize.x))\\n  + int(floor(v_TexCoord.y * u_OutputTextureSize.y)) * int(u_OutputTextureSize.x);\\nint workGroupIDLength = globalInvocationIndex / (workGroupSize.x * workGroupSize.y * workGroupSize.z);\\nivec3 workGroupID = ivec3(workGroupIDLength / numWorkGroups.y / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.y);\\nint localInvocationIDZ = globalInvocationIndex / (workGroupSize.x * workGroupSize.y);\\nint localInvocationIDY = (globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y) / workGroupSize.x;\\nint localInvocationIDX = globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y - localInvocationIDY * workGroupSize.x;\\nivec3 localInvocationID = ivec3(localInvocationIDX, localInvocationIDY, localInvocationIDZ);\\nivec3 globalInvocationID = workGroupID * workGroupSize + localInvocationID;\\nint localInvocationIndex = localInvocationID.z * workGroupSize.x * workGroupSize.y\\n                + localInvocationID.y * workGroupSize.x + localInvocationID.x;\\nint i = globalInvocationID.x;\\nvec4 currentNode = getDatau_Data(i);\\nvec4 movement = getDatau_AveMovement(0.0);\\nfloat ax = 0.0;\\nfloat ay = 0.0;\\nif ((i >= VERTEX_COUNT) || (movement.x < u_minMovement)) {gl_FragColor = vec4(currentNode);\\nreturn ;}\\nvec4 nodeAttributes1 = getDatau_NodeAttributeArray1(i);\\nvec4 nodeAttributes2 = getDatau_NodeAttributeArray2(i);\\nvec2 repulsive = calcRepulsive(i, currentNode);\\nax += repulsive.x;\\nay += repulsive.y;\\nvec2 attractive = calcAttractive(i, currentNode, nodeAttributes1);\\nax += attractive.x;\\nay += attractive.y;\\nvec2 gravity = calcGravity(i, currentNode, nodeAttributes2);\\nax -= gravity.x;\\nay -= gravity.y;\\nfloat param = u_interval * u_damping;\\nfloat vx = ax * param;\\nfloat vy = ay * param;\\nfloat vlength = sqrt((vx * vx) + (vy * vy)) + 0.0001;\\nif (vlength > u_maxSpeed) {float param2 = u_maxSpeed / vlength;\\nvx = param2 * vx;\\nvy = param2 * vy;}\\nfloat distx = vx * u_interval;\\nfloat disty = vy * u_interval;\\nfloat distLength = sqrt((distx * distx) + (disty * disty));\\nif ((nodeAttributes1.w != 0.0) && (nodeAttributes2.w != 0.0)) {gl_FragColor = vec4(vec4(nodeAttributes1.w, nodeAttributes2.w, currentNode.z, 0.0));}else {gl_FragColor = vec4(vec4(currentNode.x + distx, currentNode.y + disty, currentNode.z, distLength));}if (gWebGPUDebug) {\\n  gl_FragColor = gWebGPUDebugOutput;\\n}}\\n"},"context":{"name":"","dispatch":[1,1,1],"threadGroupSize":[1,1,1],"maxIteration":1,"defines":[{"name":"MAX_EDGE_PER_VERTEX","type":"Float","runtime":true},{"name":"VERTEX_COUNT","type":"Float","runtime":true},{"name":"SHIFT_20","type":"Float","value":1048576,"runtime":false}],"uniforms":[{"name":"u_Data","type":"vec4<f32>[]","storageClass":"StorageBuffer","readonly":false,"writeonly":false,"size":[1,1]},{"name":"u_damping","type":"Float","storageClass":"Uniform","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_maxSpeed","type":"Float","storageClass":"Uniform","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_minMovement","type":"Float","storageClass":"Uniform","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_AveMovement","type":"vec4<f32>[]","storageClass":"StorageBuffer","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_coulombDisScale","type":"Float","storageClass":"Uniform","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_factor","type":"Float","storageClass":"Uniform","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_NodeAttributeArray1","type":"vec4<f32>[]","storageClass":"StorageBuffer","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_NodeAttributeArray2","type":"vec4<f32>[]","storageClass":"StorageBuffer","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_interval","type":"Float","storageClass":"Uniform","readonly":true,"writeonly":false,"size":[1,1]}],"globalDeclarations":[],"output":{"name":"u_Data","size":[1,1],"length":1},"needPingpong":true}}',t.aveMovementCode="\nconst VERTEX_COUNT;\n@numthreads(1, 1, 1)\nclass CalcAveMovement {\n  @in\n  u_Data: vec4[];\n  @in\n  u_iter: float;\n  @in @out\n  u_AveMovement: vec4[];\n  @main\n  compute() {\n    let movement = 0;\n    for (let j: int = 0; j < VERTEX_COUNT; j++) {\n      const vertex = this.u_Data[j];\n      movement += vertex[3];\n    }\n    movement = movement / float(VERTEX_COUNT);\n    this.u_AveMovement[0] = [movement, 0, 0, 0];\n  }\n}\n",t.aveMovementBundle='{"shaders":{"WGSL":"import \\"GLSL.std.450\\" as std;\\n\\n\\n# var gWebGPUDebug : bool = false;\\n# var gWebGPUDebugOutput : vec4<f32> = vec4<f32>(0.0);\\n\\n[[builtin global_invocation_id]] var<in> globalInvocationID : vec3<u32>;\\n# [[builtin work_group_size]] var<in> workGroupSize : vec3<u32>;\\n# [[builtin work_group_id]] var<in> workGroupID : vec3<u32>;\\n[[builtin local_invocation_id]] var<in> localInvocationID : vec3<u32>;\\n# [[builtin num_work_groups]] var<in> numWorkGroups : vec3<u32>;\\n[[builtin local_invocation_idx]] var<in> localInvocationIndex : u32;\\n\\ntype GWebGPUParams = [[block]] struct {\\n  [[offset 0]] u_iter : f32;\\n};\\n[[binding 0, set 0]] var<uniform> gWebGPUUniformParams : GWebGPUParams;\\ntype GWebGPUBuffer0 = [[block]] struct {\\n  [[offset 0]] u_Data : [[stride 16]] array<vec4<f32>>;\\n};\\n[[binding 1, set 0]] var<storage_buffer> gWebGPUBuffer0 : GWebGPUBuffer0;\\ntype GWebGPUBuffer1 = [[block]] struct {\\n  [[offset 0]] u_AveMovement : [[stride 16]] array<vec4<f32>>;\\n};\\n[[binding 2, set 0]] var<storage_buffer> gWebGPUBuffer1 : GWebGPUBuffer1;\\n\\n\\n\\n\\n\\n\\n\\n\\nfn main() -> void {var movement : f32 = 0.0;\\nfor (var j : i32 = 0; j < __DefineValuePlaceholder__VERTEX_COUNT; j = j + 1) {var vertex : vec4<f32> = gWebGPUBuffer0.u_Data[j];\\nmovement = movement + vertex.w;}\\nmovement = movement / f32(__DefineValuePlaceholder__VERTEX_COUNT);\\ngWebGPUBuffer1.u_AveMovement[0] = vec4<f32>(movement, 0.0, 0.0, 0.0);\\nreturn;}\\n\\nentry_point compute as \\"main\\" = main;\\n","GLSL450":"\\n\\n\\nbool gWebGPUDebug = false;\\nvec4 gWebGPUDebugOutput = vec4(0.0);\\n\\nivec3 globalInvocationID = ivec3(gl_GlobalInvocationID);\\nivec3 workGroupSize = ivec3(1,1,1);\\nivec3 workGroupID = ivec3(gl_WorkGroupID);\\nivec3 localInvocationID = ivec3(gl_LocalInvocationID);\\nivec3 numWorkGroups = ivec3(gl_NumWorkGroups);\\nint localInvocationIndex = int(gl_LocalInvocationIndex);\\n\\nlayout(std140, set = 0, binding = 0) uniform GWebGPUParams {\\n  float u_iter;\\n} gWebGPUUniformParams;\\nlayout(std430, set = 0, binding = 1) buffer readonly  GWebGPUBuffer0 {\\n  vec4 u_Data[];\\n} gWebGPUBuffer0;\\n\\nlayout(std430, set = 0, binding = 2) buffer   GWebGPUBuffer1 {\\n  vec4 u_AveMovement[];\\n} gWebGPUBuffer1;\\n\\n\\n\\n#define VERTEX_COUNT __DefineValuePlaceholder__VERTEX_COUNT\\nlayout (\\n  local_size_x = 1,\\n  local_size_y = 1,\\n  local_size_z = 1\\n) in;\\n\\n\\n\\nvoid main() {float movement = 0.0;\\nfor (int j = 0; j < VERTEX_COUNT; j++) {vec4 vertex = gWebGPUBuffer0.u_Data[j];\\nmovement += vertex.w;}\\nmovement = movement / float(VERTEX_COUNT);\\ngWebGPUBuffer1.u_AveMovement[0] = vec4(movement, 0.0, 0.0, 0.0);}\\n","GLSL100":"\\n\\nfloat epsilon = 0.00001;\\nvec2 addrTranslation_1Dto2D(float address1D, vec2 texSize) {\\n  vec2 conv_const = vec2(1.0 / texSize.x, 1.0 / (texSize.x * texSize.y));\\n  vec2 normAddr2D = float(address1D) * conv_const;\\n  return vec2(fract(normAddr2D.x + epsilon), normAddr2D.y);\\n}\\n\\nvoid barrier() {}\\n  \\n\\nuniform vec2 u_OutputTextureSize;\\nuniform int u_OutputTexelCount;\\nvarying vec2 v_TexCoord;\\n\\nbool gWebGPUDebug = false;\\nvec4 gWebGPUDebugOutput = vec4(0.0);\\n\\n#define VERTEX_COUNT __DefineValuePlaceholder__VERTEX_COUNT\\n\\nuniform sampler2D u_Data;\\nuniform vec2 u_DataSize;\\nvec4 getDatau_Data(vec2 address2D) {\\n  return vec4(texture2D(u_Data, address2D).rgba);\\n}\\nvec4 getDatau_Data(float address1D) {\\n  return getDatau_Data(addrTranslation_1Dto2D(address1D, u_DataSize));\\n}\\nvec4 getDatau_Data(int address1D) {\\n  return getDatau_Data(float(address1D));\\n}\\nuniform float u_iter;\\nuniform sampler2D u_AveMovement;\\nuniform vec2 u_AveMovementSize;\\nvec4 getDatau_AveMovement(vec2 address2D) {\\n  return vec4(texture2D(u_AveMovement, address2D).rgba);\\n}\\nvec4 getDatau_AveMovement(float address1D) {\\n  return getDatau_AveMovement(addrTranslation_1Dto2D(address1D, u_AveMovementSize));\\n}\\nvec4 getDatau_AveMovement(int address1D) {\\n  return getDatau_AveMovement(float(address1D));\\n}\\nvoid main() {\\nivec3 workGroupSize = ivec3(1, 1, 1);\\nivec3 numWorkGroups = ivec3(1, 1, 1);     \\nint globalInvocationIndex = int(floor(v_TexCoord.x * u_OutputTextureSize.x))\\n  + int(floor(v_TexCoord.y * u_OutputTextureSize.y)) * int(u_OutputTextureSize.x);\\nint workGroupIDLength = globalInvocationIndex / (workGroupSize.x * workGroupSize.y * workGroupSize.z);\\nivec3 workGroupID = ivec3(workGroupIDLength / numWorkGroups.y / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.z, workGroupIDLength / numWorkGroups.x / numWorkGroups.y);\\nint localInvocationIDZ = globalInvocationIndex / (workGroupSize.x * workGroupSize.y);\\nint localInvocationIDY = (globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y) / workGroupSize.x;\\nint localInvocationIDX = globalInvocationIndex - localInvocationIDZ * workGroupSize.x * workGroupSize.y - localInvocationIDY * workGroupSize.x;\\nivec3 localInvocationID = ivec3(localInvocationIDX, localInvocationIDY, localInvocationIDZ);\\nivec3 globalInvocationID = workGroupID * workGroupSize + localInvocationID;\\nint localInvocationIndex = localInvocationID.z * workGroupSize.x * workGroupSize.y\\n                + localInvocationID.y * workGroupSize.x + localInvocationID.x;\\nfloat movement = 0.0;\\nfor (int j = 0; j < VERTEX_COUNT; j++) {vec4 vertex = getDatau_Data(j);\\nmovement += vertex.w;}\\nmovement = movement / float(VERTEX_COUNT);\\ngl_FragColor = vec4(vec4(movement, 0.0, 0.0, 0.0));if (gWebGPUDebug) {\\n  gl_FragColor = gWebGPUDebugOutput;\\n}}\\n"},"context":{"name":"","dispatch":[1,1,1],"threadGroupSize":[1,1,1],"maxIteration":1,"defines":[{"name":"VERTEX_COUNT","type":"Float","runtime":true}],"uniforms":[{"name":"u_Data","type":"vec4<f32>[]","storageClass":"StorageBuffer","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_iter","type":"Float","storageClass":"Uniform","readonly":true,"writeonly":false,"size":[1,1]},{"name":"u_AveMovement","type":"vec4<f32>[]","storageClass":"StorageBuffer","readonly":false,"writeonly":false,"size":[1,1]}],"globalDeclarations":[],"output":{"name":"u_AveMovement","size":[1,1],"length":1},"needPingpong":true}}'},b3ea:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0})},cb77:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=1200,o=800,i=1e7,a=10,u=3.141592653589793,c=1.5707963267948966,s=.375*u,l=.625*u,d=new Map,f=10,v=10,p=.8,y=.1,g=.5;function h(e,t,n){var r=e.x-e.size[0]/2,o=e.y-e.size[1]/2,i=e.x+e.size[0]/2,a=e.y+e.size[1]/2,u=t.x-t.size[0]/2,d=t.y-t.size[1]/2,f=t.x+t.size[0]/2,v=t.y+t.size[1]/2,p=e.x,y=e.y,g=t.x,h=t.y,_=g-p,x=Math.atan2(_,h-y),m=0,b=0,D=0,I=0;x>c?(b=o-v,m=u-i,D=parseFloat(b?(b/Math.cos(x)).toFixed(2):m.toFixed(2)),I=parseFloat(m?(m/Math.sin(x)).toFixed(2):b.toFixed(2))):0<x&&x<=c?(b=d-a,m=u-i,D=I=b>m?parseFloat(b?(b/Math.cos(x)).toFixed(2):m.toFixed(2)):parseFloat(m?(m/Math.sin(x)).toFixed(2):b.toFixed(2))):x<-c?(b=o-v,m=-(f-r),D=I=b>m?parseFloat(b?(b/Math.cos(x)).toFixed(2):m.toFixed(2)):parseFloat(m?(m/Math.sin(x)).toFixed(2):b.toFixed(2))):(b=d-a,m=Math.abs(_)>(i-r)/2?r-f:_,D=I=b>m?parseFloat(b?(b/Math.cos(x)).toFixed(2):m.toFixed(2)):parseFloat(m&&0!==x?(m/Math.sin(x)).toFixed(2):b.toFixed(2)));var w=parseFloat(x.toFixed(2)),G=n;return n&&(G=s<w&&w<l),{distance:Math.abs(D<I?D:I),isHoriz:G}}function _(e,t){var n=d.get(e.id)||[],r=n.find((function(e){return e.source===t.id||e.target===t.id})),o=e.size[0]*e.size[1],u=t.size[0]*t.size[1],c=o>u?t:e,s=o>u?e:t,l=c.x-c.size[0]/2,f=c.y-c.size[1]/2,v=c.x+c.size[0]/2,p=c.y+c.size[1]/2,y=s.x-s.size[0]/2,g=s.y-s.size[1]/2,_=s.x+s.size[0]/2,x=s.y+s.size[1]/2,m=c.x,b=c.y,D=s.x,I=s.y,w=v>=y&&_>=l&&p>=g&&x>=f,G=0,S=0;if(w){S=Math.sqrt(Math.pow(D-m,2)+Math.pow(I-b,2));var k=l>y?l:y,N=f>g?f:g,z=v<_?v:_,E=p<x?p:x,P=z-k,M=E-N,C=P*M;0===S&&(S=1e-7),G=1*a/S*100+C,G*=i}else{var T=!1,U=h(c,s,T);S=U.distance,T=U.isHoriz,S<=a?G+=0!==S?r?a+1*i/S:a+i*a/S:i:(G+=S,r&&(G+=S*S))}return G}function x(e){for(var t=0,n=0;n<e.length;n++){var i=e[n];(i.x<0||i.y<0||i.x>r||i.y>o)&&(t+=1e12);for(var a=n+1;a<e.length;a++)t+=_(i,e[a])}return t}function m(e,t,n,r){var o=new Map;n.forEach((function(e,t){o.set(e.id,e)}));var i=r.filter((function(t){return t.source===e.id||t.target===e.id}))||[],a=[];i.forEach((function(t){var n=t.source===e.id?t.target:t.source,r=o.get(n);r&&a.push(r)}));for(var u=!0,c=0;c<a.length;c++){var s=a[c],l=180*Math.atan((e.y-s.y)/(s.x-e.y)),d=180*Math.atan((t.y-s.y)/(s.x-t.y)),f=l<30||l>150,v=d<30||d>150,p=l>70&&l<110,y=d>70&&d<110;if(f&&!v||l*d<0){u=!1;break}if(p&&!y||l*d<0){u=!1;break}if((s.x-e.x)*(s.x-t.x)<0){u=!1;break}if((s.y-e.y)*(s.y-t.y)<0){u=!1;break}}return u}function b(e,t){for(var n=!1,r=1,o=f*r,i=v*r,a=[o,-o,0,0],u=[0,0,i,-i],c=0;c<e.length;++c)for(var s=e[c],l=D(s,e),d=0;d<a.length;d++){var h=m(s,{x:s.x+a[d],y:s.y+u[d]},e,t);if(h){s.x+=a[d],s.y+=u[d];var _=D(s,e),b=Math.random();_<l||b<p&&b>y?(l=_,n=!0):(s.x-=a[d],s.y-=u[d])}}return p>y&&(p*=g),n?x(e):0}function D(e,t){var n=0;(e.x<0||e.y<0||e.x+e.size[0]+20>r||e.y+e.size[1]+20>o)&&(n+=1e12);for(var i=0;i<t.length;++i)e.id!==t[i].id&&(n+=_(e,t[i]));return n}function I(e,t){if(0===e.length)return{nodes:e,edges:t};e.forEach((function(e){var n=t.filter((function(t){return t.source===e.id||t.target===e.id}));d.set(e,n)})),e.sort((function(e,t){var n,r;return(null===(n=d.get(e.id))||void 0===n?void 0:n.length)-(null===(r=d.get(t.id))||void 0===r?void 0:r.length)}));var n=x(e),r=20,o=1,i=0,a=50,u=0;while(r>0){if(u++,u>=a)break;var c=b(e,t);0!==c&&(i=c),o=i-n,n=i,0===o?--r:r=20}return e.forEach((function(e){e.x=e.x-e.size[0]/2,e.y=e.y-e.size[1]/2})),{nodes:e,edges:t}}t.default=I},ccb2:function(e,t,n){"use strict";var r=this&&this.__extends||function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!==typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e["default"]=t}),a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&o(t,e,n);return i(t,e),t},u=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.ForceLayout=void 0;var c=a(n("0a4a")),s=u(n("aaf2")),l=n("f271"),d=n("5bc9"),f=n("3888"),v=function(e){function t(t){var n=e.call(this)||this;return n.center=[0,0],n.nodeStrength=null,n.edgeStrength=null,n.preventOverlap=!1,n.clusterNodeStrength=null,n.clusterEdgeStrength=null,n.clusterEdgeDistance=null,n.clusterNodeSize=null,n.clusterFociStrength=null,n.linkDistance=50,n.alphaDecay=.028,n.alphaMin=.001,n.alpha=.3,n.collideStrength=1,n.workerEnabled=!1,n.tick=function(){},n.onLayoutEnd=function(){},n.ticking=void 0,t&&n.updateCfg(t),n}return r(t,e),t.prototype.getDefaultCfg=function(){return{center:[0,0],nodeStrength:null,edgeStrength:null,preventOverlap:!1,nodeSize:void 0,nodeSpacing:void 0,linkDistance:50,forceSimulation:null,alphaDecay:.028,alphaMin:.001,alpha:.3,collideStrength:1,clustering:!1,clusterNodeStrength:-1,clusterEdgeStrength:.1,clusterEdgeDistance:100,clusterFociStrength:.8,clusterNodeSize:10,tick:function(){},onLayoutEnd:function(){},workerEnabled:!1}},t.prototype.init=function(e){var t=this;t.nodes=e.nodes||[];var n=e.edges||[];t.edges=n.map((function(e){var t={},n=["targetNode","sourceNode","startPoint","endPoint"];return Object.keys(e).forEach((function(r){n.indexOf(r)>-1||(t[r]=e[r])})),t})),t.ticking=!1},t.prototype.execute=function(e){var t=this,n=t.nodes,r=t.edges;if(!t.ticking){var o=t.forceSimulation,i=t.alphaMin,a=t.alphaDecay,u=t.alpha;if(o){if(e)if(t.clustering&&t.clusterForce&&(t.clusterForce.nodes(n),t.clusterForce.links(r)),o.nodes(n),r&&t.edgeForce)t.edgeForce.links(r);else if(r&&!t.edgeForce){v=c.forceLink().id((function(e){return e.id})).links(r);t.edgeStrength&&v.strength(t.edgeStrength),t.linkDistance&&v.distance(t.linkDistance),t.edgeForce=v,o.force("link",v)}t.preventOverlap&&t.overlapProcess(o),o.alpha(u).restart(),this.ticking=!0}else try{var l=c.forceManyBody();if(t.nodeStrength&&l.strength(t.nodeStrength),o=c.forceSimulation().nodes(n),t.clustering){var d=(0,s.default)();d.centerX(t.center[0]).centerY(t.center[1]).template("force").strength(t.clusterFociStrength),r&&d.links(r),n&&d.nodes(n),d.forceLinkDistance(t.clusterEdgeDistance).forceLinkStrength(t.clusterEdgeStrength).forceCharge(t.clusterNodeStrength).forceNodeSize(t.clusterNodeSize),t.clusterForce=d,o.force("group",d)}if(o.force("center",c.forceCenter(t.center[0],t.center[1])).force("charge",l).alpha(u).alphaDecay(a).alphaMin(i),t.preventOverlap&&t.overlapProcess(o),r){var v=c.forceLink().id((function(e){return e.id})).links(r);t.edgeStrength&&v.strength(t.edgeStrength),t.linkDistance&&v.distance(t.linkDistance),t.edgeForce=v,o.force("link",v)}if(t.workerEnabled&&!y()&&(t.workerEnabled=!1,console.warn("workerEnabled option is only supported when running in web worker.")),t.workerEnabled){o.stop();for(var g=p(o),h=1;h<=g;h++)o.tick(),postMessage({nodes:n,currentTick:h,totalTicks:g,type:f.LAYOUT_MESSAGE.TICK},void 0);t.ticking=!1}else o.on("tick",(function(){t.tick()})).on("end",(function(){t.ticking=!1,t.onLayoutEnd&&t.onLayoutEnd()})),t.ticking=!0;t.forceSimulation=o,t.ticking=!0}catch(_){t.ticking=!1,console.warn(_)}}},t.prototype.overlapProcess=function(e){var t,n,r=this,o=r.nodeSize,i=r.nodeSpacing,a=r.collideStrength;if(n=(0,l.isNumber)(i)?function(){return i}:(0,l.isFunction)(i)?i:function(){return 0},o)if((0,l.isFunction)(o))t=function(e){var t=o(e);return t+n(e)};else if((0,l.isArray)(o)){var u=o[0]>o[1]?o[0]:o[1],s=u/2;t=function(e){return s+n(e)}}else if((0,l.isNumber)(o)){var d=o/2;t=function(e){return d+n(e)}}else t=function(){return 10};else t=function(e){if(e.size){if((0,l.isArray)(e.size)){var t=e.size[0]>e.size[1]?e.size[0]:e.size[1];return t/2+n(e)}if((0,l.isObject)(e.size)){t=e.size.width>e.size.height?e.size.width:e.size.height;return t/2+n(e)}return e.size/2+n(e)}return 10+n(e)};e.force("collisionForce",c.forceCollide(t).strength(a))},t.prototype.updateCfg=function(e){var t=this;t.ticking&&(t.forceSimulation.stop(),t.ticking=!1),t.forceSimulation=null,Object.assign(t,e)},t.prototype.destroy=function(){var e=this;e.ticking&&(e.forceSimulation.stop(),e.ticking=!1),e.nodes=null,e.edges=null,e.destroyed=!0},t}(d.Base);function p(e){var t=e.alphaMin(),n=e.alphaTarget(),r=e.alpha(),o=Math.log((t-n)/(r-n))/Math.log(1-e.alphaDecay()),i=Math.ceil(o);return i}function y(){return"undefined"!==typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope}t.ForceLayout=v},ccec:function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),t.ERLayout=t.ForceAtlas2Layout=t.ComboCombinedLayout=t.ComboForceLayout=t.GForceGPULayout=t.FruchtermanGPULayout=t.FruchtermanLayout=t.MDSLayout=t.ConcentricLayout=t.RadialLayout=t.DagreCompoundLayout=t.DagreLayout=t.CircularLayout=t.ForceLayout=t.GForceLayout=t.RandomLayout=t.GridLayout=t.Layouts=t.Layout=void 0;var i=n("9a24");Object.defineProperty(t,"GridLayout",{enumerable:!0,get:function(){return i.GridLayout}});var a=n("4da8");Object.defineProperty(t,"RandomLayout",{enumerable:!0,get:function(){return a.RandomLayout}});var u=n("aac1");Object.defineProperty(t,"GForceLayout",{enumerable:!0,get:function(){return u.GForceLayout}});var c=n("4d1a");Object.defineProperty(t,"ForceLayout",{enumerable:!0,get:function(){return c.ForceLayout}});var s=n("7c66");Object.defineProperty(t,"CircularLayout",{enumerable:!0,get:function(){return s.CircularLayout}});var l=n("13d2e");Object.defineProperty(t,"DagreLayout",{enumerable:!0,get:function(){return l.DagreLayout}});var d=n("3c0e");Object.defineProperty(t,"DagreCompoundLayout",{enumerable:!0,get:function(){return d.DagreCompoundLayout}});var f=n("634b");Object.defineProperty(t,"RadialLayout",{enumerable:!0,get:function(){return f.RadialLayout}});var v=n("455a");Object.defineProperty(t,"ConcentricLayout",{enumerable:!0,get:function(){return v.ConcentricLayout}});var p=n("82cc");Object.defineProperty(t,"MDSLayout",{enumerable:!0,get:function(){return p.MDSLayout}});var y=n("fa97");Object.defineProperty(t,"FruchtermanLayout",{enumerable:!0,get:function(){return y.FruchtermanLayout}});var g=n("077d");Object.defineProperty(t,"FruchtermanGPULayout",{enumerable:!0,get:function(){return g.FruchtermanGPULayout}});var h=n("0376");Object.defineProperty(t,"GForceGPULayout",{enumerable:!0,get:function(){return h.GForceGPULayout}});var _=n("c7ea");Object.defineProperty(t,"ComboForceLayout",{enumerable:!0,get:function(){return _.ComboForceLayout}});var x=n("7d0a");Object.defineProperty(t,"ComboCombinedLayout",{enumerable:!0,get:function(){return x.ComboCombinedLayout}});var m=n("0717");Object.defineProperty(t,"ForceAtlas2Layout",{enumerable:!0,get:function(){return m.ForceAtlas2Layout}});var b=n("927c");Object.defineProperty(t,"ERLayout",{enumerable:!0,get:function(){return b.ERLayout}});var D=n("4e2a");Object.defineProperty(t,"Layout",{enumerable:!0,get:function(){return D.Layout}}),Object.defineProperty(t,"Layouts",{enumerable:!0,get:function(){return D.Layouts}}),o(n("b3ea"),t)},dccd:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getFuncByUnknownType=t.getFunc=t.isFunction=void 0;var r=n("f271"),o=n("4b03"),i=function(e){return"function"===typeof e};t.isFunction=i;var a=function(e,t,n){var r;return r=n||((0,o.isNumber)(e)?function(){return e}:function(){return t}),r};t.getFunc=a;var u=function(e,n,i){return void 0===i&&(i=!0),n||0===n?(0,t.isFunction)(n)?n:(0,o.isNumber)(n)?function(){return n}:(0,r.isArray)(n)?function(){if(i){var t=Math.max.apply(Math,n);return isNaN(t)?e:t}return n}:(0,r.isObject)(n)?function(){if(i){var t=Math.max(n.width,n.height);return isNaN(t)?e:t}return[n.width,n.height]}:function(){return e}:function(t){return t.size?(0,r.isArray)(t.size)?t.size[0]>t.size[1]?t.size[0]:t.size[1]:(0,r.isObject)(t.size)?t.size.width>t.size.height?t.size.width:t.size.height:t.size:e}};t.getFuncByUnknownType=u},e897:function(e,t,n){"use strict";n.d(t,"b",(function(){return o["a"]})),n.d(t,"c",(function(){return o["d"]})),n.d(t,"d",(function(){return o["e"]})),n.d(t,"a",(function(){return r}));var r={};n.r(r),n.d(r,"leftTranslate",(function(){return i})),n.d(r,"leftRotate",(function(){return a})),n.d(r,"leftScale",(function(){return u})),n.d(r,"transform",(function(){return s})),n.d(r,"direction",(function(){return l})),n.d(r,"angleTo",(function(){return d})),n.d(r,"vertical",(function(){return f}));var o=n("20e7");function i(e,t,n){var r=[0,0,0,0,0,0,0,0,0];return o["a"].fromTranslation(r,n),o["a"].multiply(e,r,t)}function a(e,t,n){var r=[0,0,0,0,0,0,0,0,0];return o["a"].fromRotation(r,n),o["a"].multiply(e,r,t)}function u(e,t,n){var r=[0,0,0,0,0,0,0,0,0];return o["a"].fromScaling(r,n),o["a"].multiply(e,r,t)}function c(e,t,n){return o["a"].multiply(e,n,t)}function s(e,t){for(var n=e?[].concat(e):[1,0,0,0,1,0,0,0,1],r=0,o=t.length;r<o;r++){var s=t[r];switch(s[0]){case"t":i(n,n,[s[1],s[2]]);break;case"s":u(n,n,[s[1],s[2]]);break;case"r":a(n,n,s[1]);break;case"m":c(n,n,s[1]);break;default:break}}return n}function l(e,t){return e[0]*t[1]-t[0]*e[1]}function d(e,t,n){var r=o["d"].angle(e,t),i=l(e,t)>=0;return n?i?2*Math.PI-r:r:i?r:2*Math.PI-r}function f(e,t,n){return n?(e[0]=t[1],e[1]=-1*t[0]):(e[0]=-1*t[1],e[1]=t[0]),e}},f271:function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),o(n("4237"),t),o(n("910e"),t),o(n("4b03"),t),o(n("19d2"),t),o(n("291d"),t),o(n("dccd"),t)},fa97:function(e,t,n){"use strict";var r=this&&this.__extends||function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!==typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(t,"__esModule",{value:!0}),t.FruchtermanLayout=void 0;var o=n("5bc9"),i=n("f271"),a=800,u=function(e){function t(t){var n=e.call(this)||this;return n.maxIteration=1e3,n.workerEnabled=!1,n.gravity=10,n.speed=5,n.clustering=!1,n.clusterGravity=10,n.nodes=[],n.edges=[],n.width=300,n.height=300,n.nodeMap={},n.nodeIdxMap={},n.onLayoutEnd=function(){},n.tick=function(){},n.animate=!0,n.updateCfg(t),n}return r(t,e),t.prototype.getDefaultCfg=function(){return{maxIteration:1e3,gravity:10,speed:1,clustering:!1,clusterGravity:10,animate:!0}},t.prototype.execute=function(){var e,t,n=this,r=this,o=r.nodes;if(void 0!==r.timeInterval&&"undefined"!==typeof window&&window.clearInterval(r.timeInterval),o&&0!==o.length){r.width||"undefined"===typeof window||(r.width=window.innerWidth),r.height||"undefined"===typeof window||(r.height=window.innerHeight),r.center||(r.center=[r.width/2,r.height/2]);var a=r.center;if(1===o.length)return o[0].x=a[0],o[0].y=a[1],void(null===(t=r.onLayoutEnd)||void 0===t||t.call(r));var u={},c={};return o.forEach((function(e,t){(0,i.isNumber)(e.x)||(e.x=Math.random()*n.width),(0,i.isNumber)(e.y)||(e.y=Math.random()*n.height),u[e.id]=e,c[e.id]=t})),r.nodeMap=u,r.nodeIdxMap=c,r.run()}null===(e=r.onLayoutEnd)||void 0===e||e.call(r)},t.prototype.run=function(){var e,t=this,n=t.nodes;if(n){var r=t.edges,o=t.maxIteration,i=t.workerEnabled,a=t.clustering,u=t.animate,c={};if(a&&n.forEach((function(e){void 0===c[e.cluster]&&(c[e.cluster]={name:e.cluster,cx:0,cy:0,count:0})})),i||!u){for(var s=0;s<o;s++)t.runOneStep(c);null===(e=t.onLayoutEnd)||void 0===e||e.call(t)}else{if("undefined"===typeof window)return;var l=0;this.timeInterval=window.setInterval((function(){var e;t.runOneStep(c),l++,l>=o&&(null===(e=t.onLayoutEnd)||void 0===e||e.call(t),window.clearInterval(t.timeInterval))}),0)}return{nodes:n,edges:r}}},t.prototype.runOneStep=function(e){var t,n=this,r=n.nodes;if(r){var o=n.edges,u=n.center,c=n.gravity,s=n.speed,l=n.clustering,d=n.height*n.width,f=Math.sqrt(d)/10,v=d/(r.length+1),p=Math.sqrt(v),y=[];if(r.forEach((function(e,t){y[t]={x:0,y:0}})),n.applyCalculate(r,o,y,p,v),l){for(var g in e)e[g].cx=0,e[g].cy=0,e[g].count=0;for(var g in r.forEach((function(t){var n=e[t.cluster];(0,i.isNumber)(t.x)&&(n.cx+=t.x),(0,i.isNumber)(t.y)&&(n.cy+=t.y),n.count++})),e)e[g].cx/=e[g].count,e[g].cy/=e[g].count;var h=n.clusterGravity||c;r.forEach((function(t,n){if((0,i.isNumber)(t.x)&&(0,i.isNumber)(t.y)){var r=e[t.cluster],o=Math.sqrt((t.x-r.cx)*(t.x-r.cx)+(t.y-r.cy)*(t.y-r.cy)),a=p*h;y[n].x-=a*(t.x-r.cx)/o,y[n].y-=a*(t.y-r.cy)/o}}))}r.forEach((function(e,t){if((0,i.isNumber)(e.x)&&(0,i.isNumber)(e.y)){var n=.01*p*c;y[t].x-=n*(e.x-u[0]),y[t].y-=n*(e.y-u[1])}})),r.forEach((function(e,t){if((0,i.isNumber)(e.fx)&&(0,i.isNumber)(e.fy))return e.x=e.fx,void(e.y=e.fy);if((0,i.isNumber)(e.x)&&(0,i.isNumber)(e.y)){var n=Math.sqrt(y[t].x*y[t].x+y[t].y*y[t].y);if(n>0){var r=Math.min(f*(s/a),n);e.x+=y[t].x/n*r,e.y+=y[t].y/n*r}}})),null===(t=n.tick)||void 0===t||t.call(n)}},t.prototype.applyCalculate=function(e,t,n,r,o){var i=this;i.calRepulsive(e,n,o),t&&i.calAttractive(t,n,r)},t.prototype.calRepulsive=function(e,t,n){e.forEach((function(r,o){t[o]={x:0,y:0},e.forEach((function(e,a){if(o!==a&&(0,i.isNumber)(r.x)&&(0,i.isNumber)(e.x)&&(0,i.isNumber)(r.y)&&(0,i.isNumber)(e.y)){var u=r.x-e.x,c=r.y-e.y,s=u*u+c*c;if(0===s){s=1;var l=o>a?1:-1;u=.01*l,c=.01*l}var d=n/s;t[o].x+=u*d,t[o].y+=c*d}}))}))},t.prototype.calAttractive=function(e,t,n){var r=this;e.forEach((function(e){var o=(0,i.getEdgeTerminal)(e,"source"),a=(0,i.getEdgeTerminal)(e,"target");if(o&&a){var u=r.nodeIdxMap[o],c=r.nodeIdxMap[a];if(u!==c){var s=r.nodeMap[o],l=r.nodeMap[a];if((0,i.isNumber)(l.x)&&(0,i.isNumber)(s.x)&&(0,i.isNumber)(l.y)&&(0,i.isNumber)(s.y)){var d=l.x-s.x,f=l.y-s.y,v=Math.sqrt(d*d+f*f),p=v*v/n;t[c].x-=d/v*p,t[c].y-=f/v*p,t[u].x+=d/v*p,t[u].y+=f/v*p}}}}))},t.prototype.stop=function(){this.timeInterval&&"undefined"!==typeof window&&window.clearInterval(this.timeInterval)},t.prototype.destroy=function(){var e=this;e.stop(),e.tick=null,e.nodes=null,e.edges=null,e.destroyed=!0},t.prototype.getType=function(){return"fruchterman"},t}(o.Base);t.FruchtermanLayout=u},faf9:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e){this.id=e.id||0,this.rx=e.rx,this.ry=e.ry,this.fx=0,this.fy=0,this.mass=e.mass,this.degree=e.degree,this.g=e.g||0}return e.prototype.distanceTo=function(e){var t=this.rx-e.rx,n=this.ry-e.ry;return Math.hypot(t,n)},e.prototype.setPos=function(e,t){this.rx=e,this.ry=t},e.prototype.resetForce=function(){this.fx=0,this.fy=0},e.prototype.addForce=function(e){var t=e.rx-this.rx,n=e.ry-this.ry,r=Math.hypot(t,n);r=r<1e-4?1e-4:r;var o=this.g*(this.degree+1)*(e.degree+1)/r;this.fx+=o*t/r,this.fy+=o*n/r},e.prototype.in=function(e){return e.contains(this.rx,this.ry)},e.prototype.add=function(t){var n=this.mass+t.mass,r=(this.rx*this.mass+t.rx*t.mass)/n,o=(this.ry*this.mass+t.ry*t.mass)/n,i=this.degree+t.degree,a={rx:r,ry:o,mass:n,degree:i};return new e(a)},e}();t.default=r}}]);