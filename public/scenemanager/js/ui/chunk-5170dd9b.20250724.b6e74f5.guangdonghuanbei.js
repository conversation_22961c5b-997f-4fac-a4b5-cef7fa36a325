(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5170dd9b"],{"1af3":function(e,t,n){"use strict";n.r(t);var i=n("dff7"),r=n("6a89"),o=n("8025"),a=n("c101"),s=n("b2cc"),u=n("a40b"),c=function(){function e(e,t,n){this._editRange=e,this._originalSelection=t,this._text=n}return e.prototype.getEditOperations=function(e,t){t.addTrackedEditOperation(this._editRange,this._text)},e.prototype.computeCursorState=function(e,t){var n=t.getInverseEditOperations(),i=n[0].range;return this._originalSelection.isEmpty()?new o["a"](i.endLine<PERSON><PERSON><PERSON>,Math.min(this._originalSelection.positionColumn,i.endColumn),i.<PERSON><PERSON><PERSON><PERSON>,Math.min(this._originalSelection.positionColumn,i.endColumn)):new o["a"](i.endLineNumber,i.endColumn-this._text.length,i.endLineNumber,i.endColumn)},e}(),l=n("bc04"),d=n("b7d0"),h=n("918c"),p=n("b57f"),f=n("5fe7"),g=n("fdcc"),m=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),b=function(e,t,n,i){var r,o=arguments.length,a=o<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,n):i;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)a=Reflect.decorate(e,t,n,i);else for(var s=e.length-1;s>=0;s--)(r=e[s])&&(a=(o<3?r(a):o>3?r(t,n,a):r(t,n))||a);return o>3&&a&&Object.defineProperty(t,n,a),a},v=function(e,t){return function(n,i){t(n,i,e)}},y=function(){function e(e,t){this.decorationIds=[],this.editor=e,this.editorWorkerService=t}return e.get=function(t){return t.getContribution(e.ID)},e.prototype.dispose=function(){},e.prototype.run=function(t,n){var i=this;this.currentRequest&&this.currentRequest.cancel();var a=this.editor.getSelection(),s=this.editor.getModel();if(s&&a){var u=a;if(u.startLineNumber===u.endLineNumber){var d=new l["a"](this.editor,5),h=s.uri;return this.editorWorkerService.canNavigateValueSet(h)?(this.currentRequest=Object(f["f"])((function(e){return i.editorWorkerService.navigateValueSet(h,u,n)})),this.currentRequest.then((function(n){if(n&&n.range&&n.value&&d.validate(i.editor)){var a=r["a"].lift(n.range),s=n.range,l=n.value.length-(u.endColumn-u.startColumn);s={startLineNumber:s.startLineNumber,startColumn:s.startColumn,endLineNumber:s.endLineNumber,endColumn:s.startColumn+n.value.length},l>1&&(u=new o["a"](u.startLineNumber,u.startColumn,u.endLineNumber,u.endColumn+l-1));var h=new c(a,u,n.value);i.editor.pushUndoStop(),i.editor.executeCommand(t,h),i.editor.pushUndoStop(),i.decorationIds=i.editor.deltaDecorations(i.decorationIds,[{range:s,options:e.DECORATION}]),i.decorationRemover&&i.decorationRemover.cancel(),i.decorationRemover=Object(f["l"])(350),i.decorationRemover.then((function(){return i.decorationIds=i.editor.deltaDecorations(i.decorationIds,[])})).catch(g["e"])}})).catch(g["e"])):Promise.resolve(void 0)}}},e.ID="editor.contrib.inPlaceReplaceController",e.DECORATION=p["a"].register({className:"valueSetReplacement"}),e=b([v(1,u["a"])],e),e}(),_=function(e){function t(){return e.call(this,{id:"editor.action.inPlaceReplace.up",label:i["a"]("InPlaceReplaceAction.previous.label","Replace with Previous Value"),alias:"Replace with Previous Value",precondition:a["a"].writable,kbOpts:{kbExpr:a["a"].editorTextFocus,primary:3154,weight:100}})||this}return m(t,e),t.prototype.run=function(e,t){var n=y.get(t);return n?n.run(this.id,!0):Promise.resolve(void 0)},t}(s["b"]),C=function(e){function t(){return e.call(this,{id:"editor.action.inPlaceReplace.down",label:i["a"]("InPlaceReplaceAction.next.label","Replace with Next Value"),alias:"Replace with Next Value",precondition:a["a"].writable,kbOpts:{kbExpr:a["a"].editorTextFocus,primary:3156,weight:100}})||this}return m(t,e),t.prototype.run=function(e,t){var n=y.get(t);return n?n.run(this.id,!1):Promise.resolve(void 0)},t}(s["b"]);Object(s["h"])(y.ID,y),Object(s["f"])(_),Object(s["f"])(C),Object(d["e"])((function(e,t){var n=e.getColor(h["d"]);n&&t.addRule(".monaco-editor.vs .valueSetReplacement { outline: solid 2px "+n+"; }")}))},"351f":function(e,t,n){"use strict";n.d(t,"a",(function(){return g}));n("b329");var i=n("dff7"),r=n("5fe7"),o=n("a666"),a=n("3813"),s=n("6a89"),u=n("b2cc"),c=n("4fc3"),l=n("b7d0"),d=n("303e"),h=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),p=function(e,t,n,i){var r,o=arguments.length,a=o<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,n):i;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)a=Reflect.decorate(e,t,n,i);else for(var s=e.length-1;s>=0;s--)(r=e[s])&&(a=(o<3?r(a):o>3?r(t,n,a):r(t,n))||a);return o>3&&a&&Object.defineProperty(t,n,a),a},f=function(e,t){return function(n,i){t(n,i,e)}},g=function(e){function t(n,i){var r=e.call(this)||this;return r.closeTimeout=3e3,r._messageWidget=r._register(new o["d"]),r._messageListeners=r._register(new o["b"]),r._editor=n,r._visible=t.MESSAGE_VISIBLE.bindTo(i),r._register(r._editor.onDidAttemptReadOnlyEdit((function(){return r._onDidAttemptReadOnlyEdit()}))),r}return h(t,e),t.get=function(e){return e.getContribution(t.ID)},t.prototype.dispose=function(){e.prototype.dispose.call(this),this._visible.reset()},t.prototype.showMessage=function(e,t){var n,i=this;Object(a["a"])(e),this._visible.set(!0),this._messageWidget.clear(),this._messageListeners.clear(),this._messageWidget.value=new b(this._editor,t,e),this._messageListeners.add(this._editor.onDidBlurEditorText((function(){return i.closeMessage()}))),this._messageListeners.add(this._editor.onDidChangeCursorPosition((function(){return i.closeMessage()}))),this._messageListeners.add(this._editor.onDidDispose((function(){return i.closeMessage()}))),this._messageListeners.add(this._editor.onDidChangeModel((function(){return i.closeMessage()}))),this._messageListeners.add(new r["e"]((function(){return i.closeMessage()}),this.closeTimeout)),this._messageListeners.add(this._editor.onMouseMove((function(e){e.target.position&&(n?n.containsPosition(e.target.position)||i.closeMessage():n=new s["a"](t.lineNumber-3,1,e.target.position.lineNumber+3,1))})))},t.prototype.closeMessage=function(){this._visible.reset(),this._messageListeners.clear(),this._messageWidget.value&&this._messageListeners.add(b.fadeOut(this._messageWidget.value))},t.prototype._onDidAttemptReadOnlyEdit=function(){this._editor.hasModel()&&this.showMessage(i["a"]("editor.readonly","Cannot edit in read-only editor"),this._editor.getPosition())},t.ID="editor.contrib.messageController",t.MESSAGE_VISIBLE=new c["d"]("messageVisible",!1),t=p([f(1,c["c"])],t),t}(o["a"]),m=u["c"].bindToContribution(g.get);Object(u["g"])(new m({id:"leaveEditorMessage",precondition:g.MESSAGE_VISIBLE,handler:function(e){return e.closeMessage()},kbOpts:{weight:130,primary:9}}));var b=function(){function e(e,t,n){var i=t.lineNumber,r=t.column;this.allowEditorOverflow=!0,this.suppressMouseDown=!1,this._editor=e,this._editor.revealLinesInCenterIfOutsideViewport(i,i,0),this._position={lineNumber:i,column:r-1},this._domNode=document.createElement("div"),this._domNode.classList.add("monaco-editor-overlaymessage");var o=document.createElement("div");o.classList.add("message"),o.textContent=n,this._domNode.appendChild(o);var a=document.createElement("div");a.classList.add("anchor"),this._domNode.appendChild(a),this._editor.addContentWidget(this),this._domNode.classList.add("fadeIn")}return e.fadeOut=function(e){var t,n=function(){e.dispose(),clearTimeout(t),e.getDomNode().removeEventListener("animationend",n)};return t=setTimeout(n,110),e.getDomNode().addEventListener("animationend",n),e.getDomNode().classList.add("fadeOut"),{dispose:n}},e.prototype.dispose=function(){this._editor.removeContentWidget(this)},e.prototype.getId=function(){return"messageoverlay"},e.prototype.getDomNode=function(){return this._domNode},e.prototype.getPosition=function(){return{position:this._position,preference:[1,2]}},e}();Object(u["h"])(g.ID,g),Object(l["e"])((function(e,t){var n=e.getColor(d["gb"]);if(n){var i=e.type===l["b"]?2:1;t.addRule(".monaco-editor .monaco-editor-overlaymessage .anchor { border-top-color: "+n+"; }"),t.addRule(".monaco-editor .monaco-editor-overlaymessage .message { border: "+i+"px solid "+n+"; }")}var r=e.getColor(d["fb"]);r&&t.addRule(".monaco-editor .monaco-editor-overlaymessage .message { background-color: "+r+"; }");var o=e.getColor(d["hb"]);o&&t.addRule(".monaco-editor .monaco-editor-overlaymessage .message { color: "+o+"; }")}))},"5b02":function(e,t,n){"use strict";n.r(t),n.d(t,"TriggerParameterHintsAction",(function(){return G}));var i=n("dff7"),r=n("a666"),o=n("0a0f"),a=n("c101"),s=n("4fc3"),u=n("b2cc"),c=n("11f7"),l=n("e32d"),d=n("3813"),h=n("1898"),p=n("308f"),f=(n("cab5"),n("5818")),g=n("dea0"),m=n("5fe7"),b=n("fdcc"),v=n("b707"),y=n("2504"),_=function(e,t,n,i){function r(e){return e instanceof n?e:new n((function(t){t(e)}))}return new(n||(n=Promise))((function(n,o){function a(e){try{u(i.next(e))}catch(t){o(t)}}function s(e){try{u(i["throw"](e))}catch(t){o(t)}}function u(e){e.done?n(e.value):r(e.value).then(a,s)}u((i=i.apply(e,t||[])).next())}))},C=function(e,t){var n,i,r,o,a={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"===typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(e){return function(t){return u([e,t])}}function u(o){if(n)throw new TypeError("Generator is already executing.");while(a)try{if(n=1,i&&(r=2&o[0]?i["return"]:o[0]?i["throw"]||((r=i["return"])&&r.call(i),0):i.next)&&!(r=r.call(i,o[1])).done)return r;switch(i=0,r&&(o=[2&o[0],r.value]),o[0]){case 0:case 1:r=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,i=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(r=a.trys,!(r=r.length>0&&r[r.length-1])&&(6===o[0]||2===o[0])){a=0;continue}if(3===o[0]&&(!r||o[1]>r[0]&&o[1]<r[3])){a.label=o[1];break}if(6===o[0]&&a.label<r[1]){a.label=r[1],r=o;break}if(r&&a.label<r[2]){a.label=r[2],a.ops.push(o);break}r[2]&&a.ops.pop(),a.trys.pop();continue}o=t.call(e,a)}catch(s){o=[6,s],i=0}finally{n=r=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}},L={Visible:new s["d"]("parameterHintsVisible",!1),MultipleSignatures:new s["d"]("parameterHintsMultipleSignatures",!1)};function w(e,t,n,i){var r=v["x"].ordered(e);return Object(m["h"])(r.map((function(r){return function(){return Promise.resolve(r.provideSignatureHelp(e,t,i,n)).catch((function(e){return Object(b["f"])(e)}))}})))}Object(u["e"])("_executeSignatureHelpProvider",(function(e,t,n){return _(void 0,void 0,void 0,(function(){var i;return C(this,(function(r){switch(r.label){case 0:return[4,w(e,t,{triggerKind:v["y"].Invoke,isRetrigger:!1,triggerCharacter:n["triggerCharacter"]},y["a"].None)];case 1:return i=r.sent(),i?(setTimeout((function(){return i.dispose()}),0),[2,i.value]):[2,void 0]}}))}))}));var O,S=n("5bd7"),k=n("303e"),N=n("b7d0"),M=n("3170"),T=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),x=function(){return x=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n],t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},x.apply(this,arguments)},E=function(e,t,n,i){function r(e){return e instanceof n?e:new n((function(t){t(e)}))}return new(n||(n=Promise))((function(n,o){function a(e){try{u(i.next(e))}catch(t){o(t)}}function s(e){try{u(i["throw"](e))}catch(t){o(t)}}function u(e){e.done?n(e.value):r(e.value).then(a,s)}u((i=i.apply(e,t||[])).next())}))},D=function(e,t){var n,i,r,o,a={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"===typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(e){return function(t){return u([e,t])}}function u(o){if(n)throw new TypeError("Generator is already executing.");while(a)try{if(n=1,i&&(r=2&o[0]?i["return"]:o[0]?i["throw"]||((r=i["return"])&&r.call(i),0):i.next)&&!(r=r.call(i,o[1])).done)return r;switch(i=0,r&&(o=[2&o[0],r.value]),o[0]){case 0:case 1:r=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,i=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(r=a.trys,!(r=r.length>0&&r[r.length-1])&&(6===o[0]||2===o[0])){a=0;continue}if(3===o[0]&&(!r||o[1]>r[0]&&o[1]<r[3])){a.label=o[1];break}if(6===o[0]&&a.label<r[1]){a.label=r[1],r=o;break}if(r&&a.label<r[2]){a.label=r[2],a.ops.push(o);break}r[2]&&a.ops.pop(),a.trys.pop();continue}o=t.call(e,a)}catch(s){o=[6,s],i=0}finally{n=r=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}};(function(e){e.Default={type:0};var t=function(){function e(e){this.request=e,this.type=2}return e}();e.Pending=t;var n=function(){function e(e){this.hints=e,this.type=1}return e}();e.Active=n})(O||(O={}));var I=function(e){function t(n,i){void 0===i&&(i=t.DEFAULT_DELAY);var o=e.call(this)||this;return o._onChangedHints=o._register(new p["a"]),o.onChangedHints=o._onChangedHints.event,o.triggerOnType=!1,o._state=O.Default,o._pendingTriggers=[],o._lastSignatureHelpResult=o._register(new r["d"]),o.triggerChars=new M["b"],o.retriggerChars=new M["b"],o.triggerId=0,o.editor=n,o.throttledDelayer=new m["a"](i),o._register(o.editor.onDidChangeConfiguration((function(){return o.onEditorConfigurationChange()}))),o._register(o.editor.onDidChangeModel((function(e){return o.onModelChanged()}))),o._register(o.editor.onDidChangeModelLanguage((function(e){return o.onModelChanged()}))),o._register(o.editor.onDidChangeCursorSelection((function(e){return o.onCursorChange(e)}))),o._register(o.editor.onDidChangeModelContent((function(e){return o.onModelContentChange()}))),o._register(v["x"].onDidChange(o.onModelChanged,o)),o._register(o.editor.onDidType((function(e){return o.onDidType(e)}))),o.onEditorConfigurationChange(),o.onModelChanged(),o}return T(t,e),Object.defineProperty(t.prototype,"state",{get:function(){return this._state},set:function(e){2===this._state.type&&this._state.request.cancel(),this._state=e},enumerable:!0,configurable:!0}),t.prototype.cancel=function(e){void 0===e&&(e=!1),this.state=O.Default,this.throttledDelayer.cancel(),e||this._onChangedHints.fire(void 0)},t.prototype.trigger=function(e,t){var n=this,i=this.editor.getModel();if(i&&v["x"].has(i)){var r=++this.triggerId;this._pendingTriggers.push(e),this.throttledDelayer.trigger((function(){return n.doTrigger(r)}),t).catch(b["e"])}},t.prototype.next=function(){if(1===this.state.type){var e=this.state.hints.signatures.length,t=this.state.hints.activeSignature,n=t%e===e-1,i=this.editor.getOption(64).cycle;!(e<2||n)||i?this.updateActiveSignature(n&&i?0:t+1):this.cancel()}},t.prototype.previous=function(){if(1===this.state.type){var e=this.state.hints.signatures.length,t=this.state.hints.activeSignature,n=0===t,i=this.editor.getOption(64).cycle;!(e<2||n)||i?this.updateActiveSignature(n&&i?e-1:t-1):this.cancel()}},t.prototype.updateActiveSignature=function(e){1===this.state.type&&(this.state=new O.Active(x(x({},this.state.hints),{activeSignature:e})),this._onChangedHints.fire(this.state.hints))},t.prototype.doTrigger=function(e){return E(this,void 0,void 0,(function(){var t,n,i,r,o,a,s,u;return D(this,(function(c){switch(c.label){case 0:if(t=1===this.state.type||2===this.state.type,n=1===this.state.type?this.state.hints:void 0,this.cancel(!0),0===this._pendingTriggers.length)return[2,!1];if(i=this._pendingTriggers.reduce(A),this._pendingTriggers=[],r={triggerKind:i.triggerKind,triggerCharacter:i.triggerCharacter,isRetrigger:t,activeSignatureHelp:n},!this.editor.hasModel())return[2,!1];o=this.editor.getModel(),a=this.editor.getPosition(),this.state=new O.Pending(Object(m["f"])((function(e){return w(o,a,r,e)}))),c.label=1;case 1:return c.trys.push([1,3,,4]),[4,this.state.request];case 2:return s=c.sent(),e!==this.triggerId?(null===s||void 0===s||s.dispose(),[2,!1]):s&&s.value.signatures&&0!==s.value.signatures.length?(this.state=new O.Active(s.value),this._lastSignatureHelpResult.value=s,this._onChangedHints.fire(this.state.hints),[2,!0]):(null===s||void 0===s||s.dispose(),this._lastSignatureHelpResult.clear(),this.cancel(),[2,!1]);case 3:return u=c.sent(),e===this.triggerId&&(this.state=O.Default),Object(b["e"])(u),[2,!1];case 4:return[2]}}))}))},Object.defineProperty(t.prototype,"isTriggered",{get:function(){return 1===this.state.type||2===this.state.type||this.throttledDelayer.isTriggered()},enumerable:!0,configurable:!0}),t.prototype.onModelChanged=function(){this.cancel(),this.triggerChars=new M["b"],this.retriggerChars=new M["b"];var e=this.editor.getModel();if(e)for(var t=0,n=v["x"].ordered(e);t<n.length;t++){for(var i=n[t],r=0,o=i.signatureHelpTriggerCharacters||[];r<o.length;r++){var a=o[r];this.triggerChars.add(a.charCodeAt(0)),this.retriggerChars.add(a.charCodeAt(0))}for(var s=0,u=i.signatureHelpRetriggerCharacters||[];s<u.length;s++){a=u[s];this.retriggerChars.add(a.charCodeAt(0))}}},t.prototype.onDidType=function(e){if(this.triggerOnType){var t=e.length-1,n=e.charCodeAt(t);(this.triggerChars.has(n)||this.isTriggered&&this.retriggerChars.has(n))&&this.trigger({triggerKind:v["y"].TriggerCharacter,triggerCharacter:e.charAt(t)})}},t.prototype.onCursorChange=function(e){"mouse"===e.source?this.cancel():this.isTriggered&&this.trigger({triggerKind:v["y"].ContentChange})},t.prototype.onModelContentChange=function(){this.isTriggered&&this.trigger({triggerKind:v["y"].ContentChange})},t.prototype.onEditorConfigurationChange=function(){this.triggerOnType=this.editor.getOption(64).enabled,this.triggerOnType||this.cancel()},t.prototype.dispose=function(){this.cancel(!0),e.prototype.dispose.call(this)},t.DEFAULT_DELAY=120,t}(r["a"]);function A(e,t){switch(t.triggerKind){case v["y"].Invoke:return t;case v["y"].ContentChange:return e;case v["y"].TriggerCharacter:default:return t}}var R=n("3742"),P=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),j=function(e,t,n,i){var r,o=arguments.length,a=o<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,n):i;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)a=Reflect.decorate(e,t,n,i);else for(var s=e.length-1;s>=0;s--)(r=e[s])&&(a=(o<3?r(a):o>3?r(t,n,a):r(t,n))||a);return o>3&&a&&Object.defineProperty(t,n,a),a},F=function(e,t){return function(n,i){t(n,i,e)}},H=c["a"],U=function(e){function t(t,n,i,o){var a=e.call(this)||this;return a.editor=t,a.renderDisposeables=a._register(new r["b"]),a.visible=!1,a.announcedLabel=null,a.allowEditorOverflow=!0,a.markdownRenderer=a._register(new g["a"](t,o,i)),a.model=a._register(new I(t)),a.keyVisible=L.Visible.bindTo(n),a.keyMultipleSignatures=L.MultipleSignatures.bindTo(n),a._register(a.model.onChangedHints((function(e){e?(a.show(),a.render(e)):a.hide()}))),a}return P(t,e),t.prototype.createParamaterHintDOMNodes=function(){var e=this,t=H(".editor-widget.parameter-hints-widget"),n=c["q"](t,H(".wrapper"));n.tabIndex=-1;var i=c["q"](n,H(".controls")),r=c["q"](i,H(".button.codicon.codicon-chevron-up")),o=c["q"](i,H(".overloads")),a=c["q"](i,H(".button.codicon.codicon-chevron-down")),s=Object(l["b"])(Object(l["a"])(r,"click"));this._register(s(this.previous,this));var u=Object(l["b"])(Object(l["a"])(a,"click"));this._register(u(this.next,this));var d=H(".body"),f=new h["a"](d,{});this._register(f),n.appendChild(f.getDomNode());var g=c["q"](d,H(".signature")),m=c["q"](d,H(".docs"));t.style.userSelect="text",this.domNodes={element:t,signature:g,overloads:o,docs:m,scrollbar:f},this.editor.addContentWidget(this),this.hide(),this._register(this.editor.onDidChangeCursorSelection((function(t){e.visible&&e.editor.layoutContentWidget(e)})));var b=function(){if(e.domNodes){var t=e.editor.getOption(34);e.domNodes.element.style.fontSize=t.fontSize+"px"}};b(),this._register(p["b"].chain(this.editor.onDidChangeConfiguration.bind(this.editor)).filter((function(e){return e.hasChanged(34)})).on(b,null)),this._register(this.editor.onDidLayoutChange((function(t){return e.updateMaxHeight()}))),this.updateMaxHeight()},t.prototype.show=function(){var e=this;this.visible||(this.domNodes||this.createParamaterHintDOMNodes(),this.keyVisible.set(!0),this.visible=!0,setTimeout((function(){e.domNodes&&c["f"](e.domNodes.element,"visible")}),100),this.editor.layoutContentWidget(this))},t.prototype.hide=function(){this.visible&&(this.keyVisible.reset(),this.visible=!1,this.announcedLabel=null,this.domNodes&&c["P"](this.domNodes.element,"visible"),this.editor.layoutContentWidget(this))},t.prototype.getPosition=function(){return this.visible?{position:this.editor.getPosition(),preference:[1,2]}:null},t.prototype.render=function(e){if(this.domNodes){var t=e.signatures.length>1;c["Y"](this.domNodes.element,"multiple",t),this.keyMultipleSignatures.set(t),this.domNodes.signature.innerHTML="",this.domNodes.docs.innerHTML="";var n=e.signatures[e.activeSignature];if(n){var r=c["q"](this.domNodes.signature,H(".code")),o=n.parameters.length>0,a=this.editor.getOption(34);if(r.style.fontSize=a.fontSize+"px",r.style.fontFamily=a.fontFamily,o)this.renderParameters(r,n,e.activeParameter);else{var s=c["q"](r,H("span"));s.textContent=n.label}this.renderDisposeables.clear();var u=n.parameters[e.activeParameter];if(u&&u.documentation){var l=H("span.documentation");if("string"===typeof u.documentation)l.textContent=u.documentation;else{var h=this.markdownRenderer.render(u.documentation);c["f"](h.element,"markdown-docs"),this.renderDisposeables.add(h),l.appendChild(h.element)}c["q"](this.domNodes.docs,H("p",{},l))}if(void 0===n.documentation);else if("string"===typeof n.documentation)c["q"](this.domNodes.docs,H("p",{},n.documentation));else{h=this.markdownRenderer.render(n.documentation);c["f"](h.element,"markdown-docs"),this.renderDisposeables.add(h),c["q"](this.domNodes.docs,h.element)}var p=this.hasDocs(n,u);if(c["Y"](this.domNodes.signature,"has-docs",p),c["Y"](this.domNodes.docs,"empty",!p),this.domNodes.overloads.textContent=Object(R["F"])(e.activeSignature+1,e.signatures.length.toString().length)+"/"+e.signatures.length,u){var f=this.getParameterLabel(n,e.activeParameter);this.announcedLabel!==f&&(d["a"](i["a"]("hint","{0}, hint",f)),this.announcedLabel=f)}this.editor.layoutContentWidget(this),this.domNodes.scrollbar.scanDomNode()}}},t.prototype.hasDocs=function(e,t){return!!(t&&"string"===typeof t.documentation&&t.documentation.length>0)||(!!(t&&"object"===typeof t.documentation&&t.documentation.value.length>0)||("string"===typeof e.documentation&&e.documentation.length>0||"object"===typeof e.documentation&&e.documentation.value.length>0))},t.prototype.renderParameters=function(e,t,n){var i=this.getParameterLabelOffsets(t,n),r=i[0],o=i[1],a=document.createElement("span");a.textContent=t.label.substring(0,r);var s=document.createElement("span");s.textContent=t.label.substring(r,o),s.className="parameter active";var u=document.createElement("span");u.textContent=t.label.substring(o),c["q"](e,a,s,u)},t.prototype.getParameterLabel=function(e,t){var n=e.parameters[t];return"string"===typeof n.label?n.label:e.label.substring(n.label[0],n.label[1])},t.prototype.getParameterLabelOffsets=function(e,t){var n=e.parameters[t];if(n){if(Array.isArray(n.label))return n.label;var i=e.label.lastIndexOf(n.label);return i>=0?[i,i+n.label.length]:[0,0]}return[0,0]},t.prototype.next=function(){this.editor.focus(),this.model.next()},t.prototype.previous=function(){this.editor.focus(),this.model.previous()},t.prototype.cancel=function(){this.model.cancel()},t.prototype.getDomNode=function(){return this.domNodes||this.createParamaterHintDOMNodes(),this.domNodes.element},t.prototype.getId=function(){return t.ID},t.prototype.trigger=function(e){this.model.trigger(e,0)},t.prototype.updateMaxHeight=function(){if(this.domNodes){var e=Math.max(this.editor.getLayoutInfo().height/4,250),t=e+"px";this.domNodes.element.style.maxHeight=t;var n=this.domNodes.element.getElementsByClassName("wrapper");n.length&&(n[0].style.maxHeight=t)}},t.ID="editor.widget.parameterHintsWidget",t=j([F(1,s["c"]),F(2,S["a"]),F(3,f["a"])],t),t}(r["a"]);Object(N["e"])((function(e,t){var n=e.getColor(k["B"]);if(n){var i=e.type===N["b"]?2:1;t.addRule(".monaco-editor .parameter-hints-widget { border: "+i+"px solid "+n+"; }"),t.addRule(".monaco-editor .parameter-hints-widget.multiple .body { border-left: 1px solid "+n.transparent(.5)+"; }"),t.addRule(".monaco-editor .parameter-hints-widget .signature.has-docs { border-bottom: 1px solid "+n.transparent(.5)+"; }")}var r=e.getColor(k["A"]);r&&t.addRule(".monaco-editor .parameter-hints-widget { background-color: "+r+"; }");var o=e.getColor(k["ec"]);o&&t.addRule(".monaco-editor .parameter-hints-widget a { color: "+o+"; }");var a=e.getColor(k["C"]);a&&t.addRule(".monaco-editor .parameter-hints-widget { color: "+a+"; }");var s=e.getColor(k["dc"]);s&&t.addRule(".monaco-editor .parameter-hints-widget code { background-color: "+s+"; }")}));var B=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),V=function(e,t,n,i){var r,o=arguments.length,a=o<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,n):i;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)a=Reflect.decorate(e,t,n,i);else for(var s=e.length-1;s>=0;s--)(r=e[s])&&(a=(o<3?r(a):o>3?r(t,n,a):r(t,n))||a);return o>3&&a&&Object.defineProperty(t,n,a),a},W=function(e,t){return function(n,i){t(n,i,e)}},q=function(e){function t(t,n){var i=e.call(this)||this;return i.editor=t,i.widget=i._register(n.createInstance(U,i.editor)),i}return B(t,e),t.get=function(e){return e.getContribution(t.ID)},t.prototype.cancel=function(){this.widget.cancel()},t.prototype.previous=function(){this.widget.previous()},t.prototype.next=function(){this.widget.next()},t.prototype.trigger=function(e){this.widget.trigger(e)},t.ID="editor.controller.parameterHints",t=V([W(1,o["a"])],t),t}(r["a"]),G=function(e){function t(){return e.call(this,{id:"editor.action.triggerParameterHints",label:i["a"]("parameterHints.trigger.label","Trigger Parameter Hints"),alias:"Trigger Parameter Hints",precondition:a["a"].hasSignatureHelpProvider,kbOpts:{kbExpr:a["a"].editorTextFocus,primary:3082,weight:100}})||this}return B(t,e),t.prototype.run=function(e,t){var n=q.get(t);n&&n.trigger({triggerKind:v["y"].Invoke})},t}(u["b"]);Object(u["h"])(q.ID,q),Object(u["f"])(G);var K=175,z=u["c"].bindToContribution(q.get);Object(u["g"])(new z({id:"closeParameterHints",precondition:L.Visible,handler:function(e){return e.cancel()},kbOpts:{weight:K,kbExpr:a["a"].focus,primary:9,secondary:[1033]}})),Object(u["g"])(new z({id:"showPrevParameterHint",precondition:s["a"].and(L.Visible,L.MultipleSignatures),handler:function(e){return e.previous()},kbOpts:{weight:K,kbExpr:a["a"].focus,primary:16,secondary:[528],mac:{primary:16,secondary:[528,302]}}})),Object(u["g"])(new z({id:"showNextParameterHint",precondition:s["a"].and(L.Visible,L.MultipleSignatures),handler:function(e){return e.next()},kbOpts:{weight:K,kbExpr:a["a"].focus,primary:18,secondary:[530],mac:{primary:18,secondary:[530,300]}}}))},"607c":function(e,t,n){},"747f":function(e,t,n){"use strict";n.r(t),n.d(t,"DuplicateSelectionAction",(function(){return x})),n.d(t,"AbstractSortLinesAction",(function(){return A})),n.d(t,"SortLinesAscendingAction",(function(){return R})),n.d(t,"SortLinesDescendingAction",(function(){return P})),n.d(t,"TrimTrailingWhitespaceAction",(function(){return j})),n.d(t,"DeleteLinesAction",(function(){return F})),n.d(t,"IndentLinesAction",(function(){return H})),n.d(t,"InsertLineBeforeAction",(function(){return B})),n.d(t,"InsertLineAfterAction",(function(){return V})),n.d(t,"AbstractDeleteAllToBoundaryAction",(function(){return W})),n.d(t,"DeleteAllLeftAction",(function(){return q})),n.d(t,"DeleteAllRightAction",(function(){return G})),n.d(t,"JoinLinesAction",(function(){return K})),n.d(t,"TransposeAction",(function(){return z})),n.d(t,"AbstractCaseAction",(function(){return J})),n.d(t,"UpperCaseAction",(function(){return Y})),n.d(t,"LowerCaseAction",(function(){return $})),n.d(t,"TitleCaseAction",(function(){return X}));var i=n("dff7"),r=n("fe45"),o=n("d585"),a=n("b2cc"),s=n("2c29"),u=n("7faa"),c=n("191f"),l=n("d3f4"),d=n("7061"),h=n("6a89"),p=n("8025"),f=n("c101"),g=function(){function e(e,t){this._selection=e,this._isCopyingDown=t,this._selectionDirection=0,this._selectionId=null,this._startLineNumberDelta=0,this._endLineNumberDelta=0}return e.prototype.getEditOperations=function(e,t){var n=this._selection;this._startLineNumberDelta=0,this._endLineNumberDelta=0,n.startLineNumber<n.endLineNumber&&1===n.endColumn&&(this._endLineNumberDelta=1,n=n.setEndPosition(n.endLineNumber-1,e.getLineMaxColumn(n.endLineNumber-1)));for(var i=[],r=n.startLineNumber;r<=n.endLineNumber;r++)i.push(e.getLineContent(r));var o=i.join("\n");""===o&&this._isCopyingDown&&(this._startLineNumberDelta++,this._endLineNumberDelta++),this._isCopyingDown?t.addEditOperation(new h["a"](n.startLineNumber,1,n.startLineNumber,1),o+"\n"):t.addEditOperation(new h["a"](n.endLineNumber,e.getLineMaxColumn(n.endLineNumber),n.endLineNumber,e.getLineMaxColumn(n.endLineNumber)),"\n"+o),this._selectionId=t.trackSelection(n),this._selectionDirection=this._selection.getDirection()},e.prototype.computeCursorState=function(e,t){var n=t.getTrackedSelection(this._selectionId);if(0!==this._startLineNumberDelta||0!==this._endLineNumberDelta){var i=n.startLineNumber,r=n.startColumn,o=n.endLineNumber,a=n.endColumn;0!==this._startLineNumberDelta&&(i+=this._startLineNumberDelta,r=1),0!==this._endLineNumberDelta&&(o+=this._endLineNumberDelta,a=1),n=p["a"].createWithDirection(i,r,o,a,this._selectionDirection)}return n},e}(),m=n("3742"),b=n("ccde"),v=n("2837"),y=n("70cb");function _(e,t){for(var n=0,i=0;i<e.length;i++)"\t"===e.charAt(i)?n+=t:n++;return n}function C(e,t,n){e=e<0?0:e;var i="";if(!n){var r=Math.floor(e/t);e%=t;for(var o=0;o<r;o++)i+="\t"}for(o=0;o<e;o++)i+=" ";return i}var L=function(){function e(e,t,n){this._selection=e,this._isMovingDown=t,this._autoIndent=n,this._selectionId=null,this._moveEndLineSelectionShrink=!1}return e.prototype.getEditOperations=function(e,t){var n=e.getLineCount();if(this._isMovingDown&&this._selection.endLineNumber===n)this._selectionId=t.trackSelection(this._selection);else if(this._isMovingDown||1!==this._selection.startLineNumber){this._moveEndPositionDown=!1;var i=this._selection;i.startLineNumber<i.endLineNumber&&1===i.endColumn&&(this._moveEndPositionDown=!0,i=i.setEndPosition(i.endLineNumber-1,e.getLineMaxColumn(i.endLineNumber-1)));var r=e.getOptions(),o=r.tabSize,a=r.indentSize,s=r.insertSpaces,u=this.buildIndentConverter(o,a,s),c={getLineTokens:function(t){return e.getLineTokens(t)},getLanguageIdentifier:function(){return e.getLanguageIdentifier()},getLanguageIdAtPosition:function(t,n){return e.getLanguageIdAtPosition(t,n)},getLineContent:null};if(i.startLineNumber===i.endLineNumber&&1===e.getLineMaxColumn(i.startLineNumber)){var l=i.startLineNumber,d=this._isMovingDown?l+1:l-1;1===e.getLineMaxColumn(d)?t.addEditOperation(new h["a"](1,1,1,1),null):(t.addEditOperation(new h["a"](l,1,l,1),e.getLineContent(d)),t.addEditOperation(new h["a"](d,1,d,e.getLineMaxColumn(d)),null)),i=new p["a"](d,1,d,1)}else{var f,g=void 0;if(this._isMovingDown){f=i.endLineNumber+1,g=e.getLineContent(f),t.addEditOperation(new h["a"](f-1,e.getLineMaxColumn(f-1),f,e.getLineMaxColumn(f)),null);var b=g;if(this.shouldAutoIndent(e,i)){var v=this.matchEnterRule(e,u,o,f,i.startLineNumber-1);if(null!==v){var L=m["t"](e.getLineContent(f)),w=v+_(L,o),O=C(w,o,s);b=O+this.trimLeft(g)}else{c.getLineContent=function(t){return t===i.startLineNumber?e.getLineContent(f):e.getLineContent(t)};var S=y["a"].getGoodIndentForLine(this._autoIndent,c,e.getLanguageIdAtPosition(f,1),i.startLineNumber,u);if(null!==S){L=m["t"](e.getLineContent(f)),w=_(S,o);var k=_(L,o);if(w!==k){O=C(w,o,s);b=O+this.trimLeft(g)}}}t.addEditOperation(new h["a"](i.startLineNumber,1,i.startLineNumber,1),b+"\n");var N=this.matchEnterRule(e,u,o,i.startLineNumber,i.startLineNumber,b);if(null!==N)0!==N&&this.getIndentEditsOfMovingBlock(e,t,i,o,s,N);else{c.getLineContent=function(t){return t===i.startLineNumber?b:t>=i.startLineNumber+1&&t<=i.endLineNumber+1?e.getLineContent(t-1):e.getLineContent(t)};var M=y["a"].getGoodIndentForLine(this._autoIndent,c,e.getLanguageIdAtPosition(f,1),i.startLineNumber+1,u);if(null!==M){L=m["t"](e.getLineContent(i.startLineNumber)),w=_(M,o),k=_(L,o);if(w!==k){var T=w-k;this.getIndentEditsOfMovingBlock(e,t,i,o,s,T)}}}}else t.addEditOperation(new h["a"](i.startLineNumber,1,i.startLineNumber,1),b+"\n")}else if(f=i.startLineNumber-1,g=e.getLineContent(f),t.addEditOperation(new h["a"](f,1,f+1,1),null),t.addEditOperation(new h["a"](i.endLineNumber,e.getLineMaxColumn(i.endLineNumber),i.endLineNumber,e.getLineMaxColumn(i.endLineNumber)),"\n"+g),this.shouldAutoIndent(e,i)){c.getLineContent=function(t){return t===f?e.getLineContent(i.startLineNumber):e.getLineContent(t)};N=this.matchEnterRule(e,u,o,i.startLineNumber,i.startLineNumber-2);if(null!==N)0!==N&&this.getIndentEditsOfMovingBlock(e,t,i,o,s,N);else{var x=y["a"].getGoodIndentForLine(this._autoIndent,c,e.getLanguageIdAtPosition(i.startLineNumber,1),f,u);if(null!==x){var E=m["t"](e.getLineContent(i.startLineNumber));w=_(x,o),k=_(E,o);if(w!==k){T=w-k;this.getIndentEditsOfMovingBlock(e,t,i,o,s,T)}}}}}this._selectionId=t.trackSelection(i)}else this._selectionId=t.trackSelection(this._selection)},e.prototype.buildIndentConverter=function(e,t,n){return{shiftIndent:function(i){return b["a"].shiftIndent(i,i.length+1,e,t,n)},unshiftIndent:function(i){return b["a"].unshiftIndent(i,i.length+1,e,t,n)}}},e.prototype.matchEnterRule=function(e,t,n,i,r,o){var a=r;while(a>=1){var s=void 0;s=a===r&&void 0!==o?o:e.getLineContent(a);var u=m["D"](s);if(u>=0)break;a--}if(a<1||i>e.getLineCount())return null;var c=e.getLineMaxColumn(a),l=y["a"].getEnterAction(this._autoIndent,e,new h["a"](a,c,a,c));if(l){var d=l.indentation;l.indentAction===v["a"].None||l.indentAction===v["a"].Indent?d=l.indentation+l.appendText:l.indentAction===v["a"].IndentOutdent?d=l.indentation:l.indentAction===v["a"].Outdent&&(d=t.unshiftIndent(l.indentation)+l.appendText);var p=e.getLineContent(i);if(this.trimLeft(p).indexOf(this.trimLeft(d))>=0){var f=m["t"](e.getLineContent(i)),g=m["t"](d),b=y["a"].getIndentMetadata(e,i);null!==b&&2&b&&(g=t.unshiftIndent(g));var C=_(g,n),L=_(f,n);return C-L}}return null},e.prototype.trimLeft=function(e){return e.replace(/^\s+/,"")},e.prototype.shouldAutoIndent=function(e,t){if(this._autoIndent<4)return!1;if(!e.isCheapToTokenize(t.startLineNumber))return!1;var n=e.getLanguageIdAtPosition(t.startLineNumber,1),i=e.getLanguageIdAtPosition(t.endLineNumber,1);return n===i&&null!==y["a"].getIndentRulesSupport(n)},e.prototype.getIndentEditsOfMovingBlock=function(e,t,n,i,r,o){for(var a=n.startLineNumber;a<=n.endLineNumber;a++){var s=e.getLineContent(a),u=m["t"](s),c=_(u,i),l=c+o,d=C(l,i,r);d!==u&&(t.addEditOperation(new h["a"](a,1,a,u.length+1),d),a===n.endLineNumber&&n.endColumn<=u.length+1&&""===d&&(this._moveEndLineSelectionShrink=!0))}},e.prototype.computeCursorState=function(e,t){var n=t.getTrackedSelection(this._selectionId);return this._moveEndPositionDown&&(n=n.setEndPosition(n.endLineNumber+1,1)),this._moveEndLineSelectionShrink&&n.startLineNumber<n.endLineNumber&&(n=n.setEndPosition(n.endLineNumber,2)),n},e}(),w=function(){function e(e,t){this.selection=e,this.descending=t,this.selectionId=null}return e.getCollator=function(){return e._COLLATOR||(e._COLLATOR=new Intl.Collator),e._COLLATOR},e.prototype.getEditOperations=function(e,t){var n=S(e,this.selection,this.descending);n&&t.addEditOperation(n.range,n.text),this.selectionId=t.trackSelection(this.selection)},e.prototype.computeCursorState=function(e,t){return t.getTrackedSelection(this.selectionId)},e.canRun=function(e,t,n){if(null===e)return!1;var i=O(e,t,n);if(!i)return!1;for(var r=0,o=i.before.length;r<o;r++)if(i.before[r]!==i.after[r])return!0;return!1},e._COLLATOR=null,e}();function O(e,t,n){var i=t.startLineNumber,r=t.endLineNumber;if(1===t.endColumn&&r--,i>=r)return null;for(var o=[],a=i;a<=r;a++)o.push(e.getLineContent(a));var s=o.slice(0);return s.sort(w.getCollator().compare),!0===n&&(s=s.reverse()),{startLineNumber:i,endLineNumber:r,before:o,after:s}}function S(e,t,n){var i=O(e,t,n);return i?l["a"].replace(new h["a"](i.startLineNumber,1,i.endLineNumber,e.getLineMaxColumn(i.endLineNumber)),i.after.join("\n")):null}var k=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),N=function(e){function t(t,n){var i=e.call(this,n)||this;return i.down=t,i}return k(t,e),t.prototype.run=function(e,t){for(var n=[],i=t.getSelections()||[],r=0,o=i;r<o.length;r++){var a=o[r];n.push(new g(a,this.down))}t.pushUndoStop(),t.executeCommands(this.id,n),t.pushUndoStop()},t}(a["b"]),M=function(e){function t(){return e.call(this,!1,{id:"editor.action.copyLinesUpAction",label:i["a"]("lines.copyUp","Copy Line Up"),alias:"Copy Line Up",precondition:f["a"].writable,kbOpts:{kbExpr:f["a"].editorTextFocus,primary:1552,linux:{primary:3600},weight:100},menuOpts:{menuId:25,group:"2_line",title:i["a"]({key:"miCopyLinesUp",comment:["&& denotes a mnemonic"]},"&&Copy Line Up"),order:1}})||this}return k(t,e),t}(N),T=function(e){function t(){return e.call(this,!0,{id:"editor.action.copyLinesDownAction",label:i["a"]("lines.copyDown","Copy Line Down"),alias:"Copy Line Down",precondition:f["a"].writable,kbOpts:{kbExpr:f["a"].editorTextFocus,primary:1554,linux:{primary:3602},weight:100},menuOpts:{menuId:25,group:"2_line",title:i["a"]({key:"miCopyLinesDown",comment:["&& denotes a mnemonic"]},"Co&&py Line Down"),order:2}})||this}return k(t,e),t}(N),x=function(e){function t(){return e.call(this,{id:"editor.action.duplicateSelection",label:i["a"]("duplicateSelection","Duplicate Selection"),alias:"Duplicate Selection",precondition:f["a"].writable,menuOpts:{menuId:25,group:"2_line",title:i["a"]({key:"miDuplicateSelection",comment:["&& denotes a mnemonic"]},"&&Duplicate Selection"),order:5}})||this}return k(t,e),t.prototype.run=function(e,t,n){if(t.hasModel()){for(var i=[],r=t.getSelections(),o=t.getModel(),a=0,u=r;a<u.length;a++){var c=u[a];if(c.isEmpty())i.push(new g(c,!0));else{var l=new p["a"](c.endLineNumber,c.endColumn,c.endLineNumber,c.endColumn);i.push(new s["c"](l,o.getValueInRange(c)))}}t.pushUndoStop(),t.executeCommands(this.id,i),t.pushUndoStop()}},t}(a["b"]),E=function(e){function t(t,n){var i=e.call(this,n)||this;return i.down=t,i}return k(t,e),t.prototype.run=function(e,t){for(var n=[],i=t.getSelections()||[],r=t.getOption(8),o=0,a=i;o<a.length;o++){var s=a[o];n.push(new L(s,this.down,r))}t.pushUndoStop(),t.executeCommands(this.id,n),t.pushUndoStop()},t}(a["b"]),D=function(e){function t(){return e.call(this,!1,{id:"editor.action.moveLinesUpAction",label:i["a"]("lines.moveUp","Move Line Up"),alias:"Move Line Up",precondition:f["a"].writable,kbOpts:{kbExpr:f["a"].editorTextFocus,primary:528,linux:{primary:528},weight:100},menuOpts:{menuId:25,group:"2_line",title:i["a"]({key:"miMoveLinesUp",comment:["&& denotes a mnemonic"]},"Mo&&ve Line Up"),order:3}})||this}return k(t,e),t}(E),I=function(e){function t(){return e.call(this,!0,{id:"editor.action.moveLinesDownAction",label:i["a"]("lines.moveDown","Move Line Down"),alias:"Move Line Down",precondition:f["a"].writable,kbOpts:{kbExpr:f["a"].editorTextFocus,primary:530,linux:{primary:530},weight:100},menuOpts:{menuId:25,group:"2_line",title:i["a"]({key:"miMoveLinesDown",comment:["&& denotes a mnemonic"]},"Move &&Line Down"),order:4}})||this}return k(t,e),t}(E),A=function(e){function t(t,n){var i=e.call(this,n)||this;return i.descending=t,i}return k(t,e),t.prototype.run=function(e,t){for(var n=t.getSelections()||[],i=0,r=n;i<r.length;i++){var o=r[i];if(!w.canRun(t.getModel(),o,this.descending))return}for(var a=[],s=0,u=n.length;s<u;s++)a[s]=new w(n[s],this.descending);t.pushUndoStop(),t.executeCommands(this.id,a),t.pushUndoStop()},t}(a["b"]),R=function(e){function t(){return e.call(this,!1,{id:"editor.action.sortLinesAscending",label:i["a"]("lines.sortAscending","Sort Lines Ascending"),alias:"Sort Lines Ascending",precondition:f["a"].writable})||this}return k(t,e),t}(A),P=function(e){function t(){return e.call(this,!0,{id:"editor.action.sortLinesDescending",label:i["a"]("lines.sortDescending","Sort Lines Descending"),alias:"Sort Lines Descending",precondition:f["a"].writable})||this}return k(t,e),t}(A),j=function(e){function t(){return e.call(this,{id:t.ID,label:i["a"]("lines.trimTrailingWhitespace","Trim Trailing Whitespace"),alias:"Trim Trailing Whitespace",precondition:f["a"].writable,kbOpts:{kbExpr:f["a"].editorTextFocus,primary:Object(r["a"])(2089,2102),weight:100}})||this}return k(t,e),t.prototype.run=function(e,t,n){var i=[];"auto-save"===n.reason&&(i=(t.getSelections()||[]).map((function(e){return new d["a"](e.positionLineNumber,e.positionColumn)})));var r=t.getSelection();if(null!==r){var o=new u["a"](r,i);t.pushUndoStop(),t.executeCommands(this.id,[o]),t.pushUndoStop()}},t.ID="editor.action.trimTrailingWhitespace",t}(a["b"]),F=function(e){function t(){return e.call(this,{id:"editor.action.deleteLines",label:i["a"]("lines.delete","Delete Line"),alias:"Delete Line",precondition:f["a"].writable,kbOpts:{kbExpr:f["a"].textInputFocus,primary:3113,weight:100}})||this}return k(t,e),t.prototype.run=function(e,t){if(t.hasModel()){var n=this._getLinesToRemove(t),i=t.getModel();if(1!==i.getLineCount()||1!==i.getLineMaxColumn(1)){for(var r=0,o=[],a=[],s=0,u=n.length;s<u;s++){var c=n[s],d=c.startLineNumber,h=c.endLineNumber,f=1,g=i.getLineMaxColumn(h);h<i.getLineCount()?(h+=1,g=1):d>1&&(d-=1,f=i.getLineMaxColumn(d)),o.push(l["a"].replace(new p["a"](d,f,h,g),"")),a.push(new p["a"](d-r,c.positionColumn,d-r,c.positionColumn)),r+=c.endLineNumber-c.startLineNumber+1}t.pushUndoStop(),t.executeEdits(this.id,o,a),t.pushUndoStop()}}},t.prototype._getLinesToRemove=function(e){var t=e.getSelections().map((function(e){var t=e.endLineNumber;return e.startLineNumber<e.endLineNumber&&1===e.endColumn&&(t-=1),{startLineNumber:e.startLineNumber,selectionStartColumn:e.selectionStartColumn,endLineNumber:t,positionColumn:e.positionColumn}}));t.sort((function(e,t){return e.startLineNumber===t.startLineNumber?e.endLineNumber-t.endLineNumber:e.startLineNumber-t.startLineNumber}));for(var n=[],i=t[0],r=1;r<t.length;r++)i.endLineNumber+1>=t[r].startLineNumber?i.endLineNumber=t[r].endLineNumber:(n.push(i),i=t[r]);return n.push(i),n},t}(a["b"]),H=function(e){function t(){return e.call(this,{id:"editor.action.indentLines",label:i["a"]("lines.indent","Indent Line"),alias:"Indent Line",precondition:f["a"].writable,kbOpts:{kbExpr:f["a"].editorTextFocus,primary:2137,weight:100}})||this}return k(t,e),t.prototype.run=function(e,t){var n=t._getCursors();n&&(t.pushUndoStop(),t.executeCommands(this.id,c["a"].indent(n.context.config,t.getModel(),t.getSelections())),t.pushUndoStop())},t}(a["b"]),U=function(e){function t(){return e.call(this,{id:"editor.action.outdentLines",label:i["a"]("lines.outdent","Outdent Line"),alias:"Outdent Line",precondition:f["a"].writable,kbOpts:{kbExpr:f["a"].editorTextFocus,primary:2135,weight:100}})||this}return k(t,e),t.prototype.run=function(e,t){o["CoreEditingCommands"].Outdent.runEditorCommand(e,t,null)},t}(a["b"]),B=function(e){function t(){return e.call(this,{id:"editor.action.insertLineBefore",label:i["a"]("lines.insertBefore","Insert Line Above"),alias:"Insert Line Above",precondition:f["a"].writable,kbOpts:{kbExpr:f["a"].editorTextFocus,primary:3075,weight:100}})||this}return k(t,e),t.prototype.run=function(e,t){var n=t._getCursors();n&&(t.pushUndoStop(),t.executeCommands(this.id,c["a"].lineInsertBefore(n.context.config,t.getModel(),t.getSelections())))},t}(a["b"]),V=function(e){function t(){return e.call(this,{id:"editor.action.insertLineAfter",label:i["a"]("lines.insertAfter","Insert Line Below"),alias:"Insert Line Below",precondition:f["a"].writable,kbOpts:{kbExpr:f["a"].editorTextFocus,primary:2051,weight:100}})||this}return k(t,e),t.prototype.run=function(e,t){var n=t._getCursors();n&&(t.pushUndoStop(),t.executeCommands(this.id,c["a"].lineInsertAfter(n.context.config,t.getModel(),t.getSelections())))},t}(a["b"]),W=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return k(t,e),t.prototype.run=function(e,t){if(t.hasModel()){for(var n=t.getSelection(),i=this._getRangesToDelete(t),r=[],o=0,a=i.length-1;o<a;o++){var s=i[o],u=i[o+1];null===h["a"].intersectRanges(s,u)?r.push(s):i[o+1]=h["a"].plusRange(s,u)}r.push(i[i.length-1]);var c=this._getEndCursorState(n,r),d=r.map((function(e){return l["a"].replace(e,"")}));t.pushUndoStop(),t.executeEdits(this.id,d,c),t.pushUndoStop()}},t}(a["b"]),q=function(e){function t(){return e.call(this,{id:"deleteAllLeft",label:i["a"]("lines.deleteAllLeft","Delete All Left"),alias:"Delete All Left",precondition:f["a"].writable,kbOpts:{kbExpr:f["a"].textInputFocus,primary:0,mac:{primary:2049},weight:100}})||this}return k(t,e),t.prototype._getEndCursorState=function(e,t){var n=null,i=[],r=0;return t.forEach((function(t){var o;if(1===t.endColumn&&r>0){var a=t.startLineNumber-r;o=new p["a"](a,t.startColumn,a,t.startColumn)}else o=new p["a"](t.startLineNumber,t.startColumn,t.startLineNumber,t.startColumn);r+=t.endLineNumber-t.startLineNumber,t.intersectRanges(e)?n=o:i.push(o)})),n&&i.unshift(n),i},t.prototype._getRangesToDelete=function(e){var t=e.getSelections();if(null===t)return[];var n=t,i=e.getModel();return null===i?[]:(n.sort(h["a"].compareRangesUsingStarts),n=n.map((function(e){if(e.isEmpty()){if(1===e.startColumn){var t=Math.max(1,e.startLineNumber-1),n=1===e.startLineNumber?1:i.getLineContent(t).length+1;return new h["a"](t,n,e.startLineNumber,1)}return new h["a"](e.startLineNumber,1,e.startLineNumber,e.startColumn)}return new h["a"](e.startLineNumber,1,e.endLineNumber,e.endColumn)})),n)},t}(W),G=function(e){function t(){return e.call(this,{id:"deleteAllRight",label:i["a"]("lines.deleteAllRight","Delete All Right"),alias:"Delete All Right",precondition:f["a"].writable,kbOpts:{kbExpr:f["a"].textInputFocus,primary:0,mac:{primary:297,secondary:[2068]},weight:100}})||this}return k(t,e),t.prototype._getEndCursorState=function(e,t){for(var n=null,i=[],r=0,o=t.length,a=0;r<o;r++){var s=t[r],u=new p["a"](s.startLineNumber-a,s.startColumn,s.startLineNumber-a,s.startColumn);s.intersectRanges(e)?n=u:i.push(u)}return n&&i.unshift(n),i},t.prototype._getRangesToDelete=function(e){var t=e.getModel();if(null===t)return[];var n=e.getSelections();if(null===n)return[];var i=n.map((function(e){if(e.isEmpty()){var n=t.getLineMaxColumn(e.startLineNumber);return e.startColumn===n?new h["a"](e.startLineNumber,e.startColumn,e.startLineNumber+1,1):new h["a"](e.startLineNumber,e.startColumn,e.startLineNumber,n)}return e}));return i.sort(h["a"].compareRangesUsingStarts),i},t}(W),K=function(e){function t(){return e.call(this,{id:"editor.action.joinLines",label:i["a"]("lines.joinLines","Join Lines"),alias:"Join Lines",precondition:f["a"].writable,kbOpts:{kbExpr:f["a"].editorTextFocus,primary:0,mac:{primary:296},weight:100}})||this}return k(t,e),t.prototype.run=function(e,t){var n=t.getSelections();if(null!==n){var i=t.getSelection();if(null!==i){n.sort(h["a"].compareRangesUsingStarts);var r=[],o=n.reduce((function(e,t){return e.isEmpty()?e.endLineNumber===t.startLineNumber?(i.equalsSelection(e)&&(i=t),t):t.startLineNumber>e.endLineNumber+1?(r.push(e),t):new p["a"](e.startLineNumber,e.startColumn,t.endLineNumber,t.endColumn):t.startLineNumber>e.endLineNumber?(r.push(e),t):new p["a"](e.startLineNumber,e.startColumn,t.endLineNumber,t.endColumn)}));r.push(o);var a=t.getModel();if(null!==a){for(var s=[],u=[],c=i,d=0,f=0,g=r.length;f<g;f++){var m=r[f],b=m.startLineNumber,v=1,y=0,_=void 0,C=void 0,L=a.getLineContent(m.endLineNumber).length-m.endColumn;if(m.isEmpty()||m.startLineNumber===m.endLineNumber){var w=m.getStartPosition();w.lineNumber<a.getLineCount()?(_=b+1,C=a.getLineMaxColumn(_)):(_=w.lineNumber,C=a.getLineMaxColumn(w.lineNumber))}else _=m.endLineNumber,C=a.getLineMaxColumn(_);for(var O=a.getLineContent(b),S=b+1;S<=_;S++){var k=a.getLineContent(S),N=a.getLineFirstNonWhitespaceColumn(S);if(N>=1){var M=!0;""===O&&(M=!1),!M||" "!==O.charAt(O.length-1)&&"\t"!==O.charAt(O.length-1)||(M=!1,O=O.replace(/[\s\uFEFF\xA0]+$/g," "));var T=k.substr(N-1);O+=(M?" ":"")+T,y=M?T.length+1:T.length}else y=0}var x=new h["a"](b,v,_,C);if(!x.isEmpty()){var E=void 0;m.isEmpty()?(s.push(l["a"].replace(x,O)),E=new p["a"](x.startLineNumber-d,O.length-y+1,b-d,O.length-y+1)):m.startLineNumber===m.endLineNumber?(s.push(l["a"].replace(x,O)),E=new p["a"](m.startLineNumber-d,m.startColumn,m.endLineNumber-d,m.endColumn)):(s.push(l["a"].replace(x,O)),E=new p["a"](m.startLineNumber-d,m.startColumn,m.startLineNumber-d,O.length-L)),null!==h["a"].intersectRanges(x,i)?c=E:u.push(E)}d+=x.endLineNumber-x.startLineNumber}u.unshift(c),t.pushUndoStop(),t.executeEdits(this.id,s,u),t.pushUndoStop()}}}},t}(a["b"]),z=function(e){function t(){return e.call(this,{id:"editor.action.transpose",label:i["a"]("editor.transpose","Transpose characters around the cursor"),alias:"Transpose characters around the cursor",precondition:f["a"].writable})||this}return k(t,e),t.prototype.run=function(e,t){var n=t.getSelections();if(null!==n){var i=t.getModel();if(null!==i){for(var r=[],o=0,a=n.length;o<a;o++){var u=n[o];if(u.isEmpty()){var c=u.getStartPosition(),l=i.getLineMaxColumn(c.lineNumber);if(c.column>=l){if(c.lineNumber===i.getLineCount())continue;var d=new h["a"](c.lineNumber,Math.max(1,c.column-1),c.lineNumber+1,1),f=i.getValueInRange(d).split("").reverse().join("");r.push(new s["a"](new p["a"](c.lineNumber,Math.max(1,c.column-1),c.lineNumber+1,1),f))}else{d=new h["a"](c.lineNumber,Math.max(1,c.column-1),c.lineNumber,c.column+1),f=i.getValueInRange(d).split("").reverse().join("");r.push(new s["b"](d,f,new p["a"](c.lineNumber,c.column+1,c.lineNumber,c.column+1)))}}}t.pushUndoStop(),t.executeCommands(this.id,r),t.pushUndoStop()}}},t}(a["b"]),J=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return k(t,e),t.prototype.run=function(e,t){var n=t.getSelections();if(null!==n){var i=t.getModel();if(null!==i){for(var r=t.getOption(96),o=[],a=0,u=n.length;a<u;a++){var c=n[a];if(c.isEmpty()){var l=c.getStartPosition(),d=i.getWordAtPosition(l);if(!d)continue;var f=new h["a"](l.lineNumber,d.startColumn,l.lineNumber,d.endColumn),g=i.getValueInRange(f);o.push(new s["b"](f,this._modifyText(g,r),new p["a"](l.lineNumber,l.column,l.lineNumber,l.column)))}else{g=i.getValueInRange(c);o.push(new s["b"](c,this._modifyText(g,r),c))}}t.pushUndoStop(),t.executeCommands(this.id,o),t.pushUndoStop()}}},t}(a["b"]),Y=function(e){function t(){return e.call(this,{id:"editor.action.transformToUppercase",label:i["a"]("editor.transformToUppercase","Transform to Uppercase"),alias:"Transform to Uppercase",precondition:f["a"].writable})||this}return k(t,e),t.prototype._modifyText=function(e,t){return e.toLocaleUpperCase()},t}(J),$=function(e){function t(){return e.call(this,{id:"editor.action.transformToLowercase",label:i["a"]("editor.transformToLowercase","Transform to Lowercase"),alias:"Transform to Lowercase",precondition:f["a"].writable})||this}return k(t,e),t.prototype._modifyText=function(e,t){return e.toLocaleLowerCase()},t}(J),X=function(e){function t(){return e.call(this,{id:"editor.action.transformToTitlecase",label:i["a"]("editor.transformToTitlecase","Transform to Title Case"),alias:"Transform to Title Case",precondition:f["a"].writable})||this}return k(t,e),t.prototype._modifyText=function(e,t){for(var n="\r\n\t "+t,i=n.split(""),r="",o=!0,a=0;a<e.length;a++){var s=e[a];i.indexOf(s)>=0?(o=!0,r+=s):o?(o=!1,r+=s.toLocaleUpperCase()):r+=s.toLocaleLowerCase()}return r},t}(J);Object(a["f"])(M),Object(a["f"])(T),Object(a["f"])(x),Object(a["f"])(D),Object(a["f"])(I),Object(a["f"])(R),Object(a["f"])(P),Object(a["f"])(j),Object(a["f"])(F),Object(a["f"])(H),Object(a["f"])(U),Object(a["f"])(B),Object(a["f"])(V),Object(a["f"])(q),Object(a["f"])(G),Object(a["f"])(K),Object(a["f"])(z),Object(a["f"])(Y),Object(a["f"])($),Object(a["f"])(X)},"7b59":function(e,t,n){},"88d4":function(e,t,n){"use strict";n.d(t,"a",(function(){return w})),n.d(t,"b",(function(){return i})),n.d(t,"d",(function(){return S})),n.d(t,"c",(function(){return N})),n.d(t,"p",(function(){return M})),n.d(t,"q",(function(){return T})),n.d(t,"r",(function(){return x})),n.d(t,"e",(function(){return E})),n.d(t,"j",(function(){return D})),n.d(t,"l",(function(){return I})),n.d(t,"k",(function(){return A})),n.d(t,"n",(function(){return R})),n.d(t,"o",(function(){return P})),n.d(t,"f",(function(){return j})),n.d(t,"g",(function(){return F})),n.d(t,"m",(function(){return H})),n.d(t,"h",(function(){return U})),n.d(t,"i",(function(){return B}));n("7b59");var i,r=n("11f7"),o=n("5aa5"),a=n("f070"),s=n("ceb8"),u=n("308f"),c=n("aa3d"),l=n("3742"),d=n("5717"),h=n("d379"),p=n("7275"),f=n("dff7"),g=n("4fc3"),m=n("0a0f"),b=n("f5f3"),v=n("b2cc"),y=n("303e"),_=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),C=function(e,t,n,i){var r,o=arguments.length,a=o<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,n):i;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)a=Reflect.decorate(e,t,n,i);else for(var s=e.length-1;s>=0;s--)(r=e[s])&&(a=(o<3?r(a):o>3?r(t,n,a):r(t,n))||a);return o>3&&a&&Object.defineProperty(t,n,a),a},L=function(e,t){return function(n,i){t(n,i,e)}},w=Object(m["c"])("IPeekViewService");Object(b["b"])(w,function(){function e(){this._widgets=new Map}return e.prototype.addExclusiveWidget=function(e,t){var n=this,i=this._widgets.get(e);i&&(i.listener.dispose(),i.widget.dispose());var r=function(){var i=n._widgets.get(e);i&&i.widget===t&&(i.listener.dispose(),n._widgets.delete(e))};this._widgets.set(e,{widget:t,listener:t.onDidClose(r)})},e}()),function(e){e.inPeekEditor=new g["d"]("inReferenceSearchEditor",!0),e.notInPeekEditor=e.inPeekEditor.toNegated()}(i||(i={}));var O=function(){function e(e,t){e instanceof h["a"]&&i.inPeekEditor.bindTo(t)}return e.prototype.dispose=function(){},e.ID="editor.contrib.referenceController",e=C([L(1,g["c"])],e),e}();function S(e){var t=e.get(d["a"]).getFocusedCodeEditor();return t instanceof h["a"]?t.getParentEditor():t}Object(v["h"])(O.ID,O);var k={headerBackgroundColor:s["a"].white,primaryHeadingColor:s["a"].fromHex("#333333"),secondaryHeadingColor:s["a"].fromHex("#6c6c6cb3")},N=function(e){function t(t,n){void 0===n&&(n={});var i=e.call(this,t,n)||this;return i._onDidClose=new u["a"],i.onDidClose=i._onDidClose.event,c["g"](i.options,k,!1),i}return _(t,e),t.prototype.dispose=function(){e.prototype.dispose.call(this),this._onDidClose.fire(this)},t.prototype.style=function(t){var n=this.options;t.headerBackgroundColor&&(n.headerBackgroundColor=t.headerBackgroundColor),t.primaryHeadingColor&&(n.primaryHeadingColor=t.primaryHeadingColor),t.secondaryHeadingColor&&(n.secondaryHeadingColor=t.secondaryHeadingColor),e.prototype.style.call(this,t)},t.prototype._applyStyles=function(){e.prototype._applyStyles.call(this);var t=this.options;this._headElement&&t.headerBackgroundColor&&(this._headElement.style.backgroundColor=t.headerBackgroundColor.toString()),this._primaryHeading&&t.primaryHeadingColor&&(this._primaryHeading.style.color=t.primaryHeadingColor.toString()),this._secondaryHeading&&t.secondaryHeadingColor&&(this._secondaryHeading.style.color=t.secondaryHeadingColor.toString()),this._bodyElement&&t.frameColor&&(this._bodyElement.style.borderColor=t.frameColor.toString())},t.prototype._fillContainer=function(e){this.setCssClass("peekview-widget"),this._headElement=r["a"](".head"),this._bodyElement=r["a"](".body"),this._fillHead(this._headElement),this._fillBody(this._bodyElement),e.appendChild(this._headElement),e.appendChild(this._bodyElement)},t.prototype._fillHead=function(e){var t=this,n=r["a"](".peekview-title");r["q"](this._headElement,n),r["o"](n,"click",(function(e){return t._onTitleClick(e)})),this._fillTitleIcon(n),this._primaryHeading=r["a"]("span.filename"),this._secondaryHeading=r["a"]("span.dirname"),this._metaHeading=r["a"]("span.meta"),r["q"](n,this._primaryHeading,this._secondaryHeading,this._metaHeading);var i=r["a"](".peekview-actions");r["q"](this._headElement,i);var s=this._getActionBarOptions();this._actionbarWidget=new o["a"](i,s),this._disposables.add(this._actionbarWidget),this._actionbarWidget.push(new a["a"]("peekview.close",f["a"]("label.close","Close"),"codicon-close",!0,(function(){return t.dispose(),Promise.resolve()})),{label:!1,icon:!0})},t.prototype._fillTitleIcon=function(e){},t.prototype._getActionBarOptions=function(){return{}},t.prototype._onTitleClick=function(e){},t.prototype.setTitle=function(e,t){this._primaryHeading&&this._secondaryHeading&&(this._primaryHeading.innerHTML=l["o"](e),this._primaryHeading.setAttribute("aria-label",e),t?this._secondaryHeading.innerHTML=l["o"](t):r["t"](this._secondaryHeading))},t.prototype.setMetaTitle=function(e){this._metaHeading&&(e?(this._metaHeading.innerHTML=l["o"](e),r["X"](this._metaHeading)):r["J"](this._metaHeading))},t.prototype._doLayout=function(e,t){if(!this._isShowing&&e<0)this.dispose();else{var n=Math.ceil(1.2*this.editor.getOption(49)),i=Math.round(e-(n+2));this._doLayoutHead(n,t),this._doLayoutBody(i,t)}},t.prototype._doLayoutHead=function(e,t){this._headElement&&(this._headElement.style.height=e+"px",this._headElement.style.lineHeight=this._headElement.style.height)},t.prototype._doLayoutBody=function(e,t){this._bodyElement&&(this._bodyElement.style.height=e+"px")},t}(p["a"]),M=Object(y["Tb"])("peekViewTitle.background",{dark:"#1E1E1E",light:"#FFFFFF",hc:"#0C141F"},f["a"]("peekViewTitleBackground","Background color of the peek view title area.")),T=Object(y["Tb"])("peekViewTitleLabel.foreground",{dark:"#FFFFFF",light:"#333333",hc:"#FFFFFF"},f["a"]("peekViewTitleForeground","Color of the peek view title.")),x=Object(y["Tb"])("peekViewTitleDescription.foreground",{dark:"#ccccccb3",light:"#616161e6",hc:"#FFFFFF99"},f["a"]("peekViewTitleInfoForeground","Color of the peek view title info.")),E=Object(y["Tb"])("peekView.border",{dark:"#007acc",light:"#007acc",hc:y["e"]},f["a"]("peekViewBorder","Color of the peek view borders and arrow.")),D=Object(y["Tb"])("peekViewResult.background",{dark:"#252526",light:"#F3F3F3",hc:s["a"].black},f["a"]("peekViewResultsBackground","Background color of the peek view result list.")),I=Object(y["Tb"])("peekViewResult.lineForeground",{dark:"#bbbbbb",light:"#646465",hc:s["a"].white},f["a"]("peekViewResultsMatchForeground","Foreground color for line nodes in the peek view result list.")),A=Object(y["Tb"])("peekViewResult.fileForeground",{dark:s["a"].white,light:"#1E1E1E",hc:s["a"].white},f["a"]("peekViewResultsFileForeground","Foreground color for file nodes in the peek view result list.")),R=Object(y["Tb"])("peekViewResult.selectionBackground",{dark:"#3399ff33",light:"#3399ff33",hc:null},f["a"]("peekViewResultsSelectionBackground","Background color of the selected entry in the peek view result list.")),P=Object(y["Tb"])("peekViewResult.selectionForeground",{dark:s["a"].white,light:"#6C6C6C",hc:s["a"].white},f["a"]("peekViewResultsSelectionForeground","Foreground color of the selected entry in the peek view result list.")),j=Object(y["Tb"])("peekViewEditor.background",{dark:"#001F33",light:"#F2F8FC",hc:s["a"].black},f["a"]("peekViewEditorBackground","Background color of the peek view editor.")),F=Object(y["Tb"])("peekViewEditorGutter.background",{dark:j,light:j,hc:j},f["a"]("peekViewEditorGutterBackground","Background color of the gutter in the peek view editor.")),H=Object(y["Tb"])("peekViewResult.matchHighlightBackground",{dark:"#ea5c004d",light:"#ea5c004d",hc:null},f["a"]("peekViewResultsMatchHighlight","Match highlight color in the peek view result list.")),U=Object(y["Tb"])("peekViewEditor.matchHighlightBackground",{dark:"#ff8f0099",light:"#f5d802de",hc:null},f["a"]("peekViewEditorMatchHighlight","Match highlight color in the peek view editor.")),B=Object(y["Tb"])("peekViewEditor.matchHighlightBorder",{dark:null,light:null,hc:y["b"]},f["a"]("peekViewEditorMatchHighlightBorder","Match highlight border in the peek view editor."))},b329:function(e,t,n){},cab5:function(e,t,n){},d844:function(e,t,n){"use strict";n.r(t);n("607c");var i=n("dff7"),r=n("5fe7"),o=n("2504"),a=n("fdcc"),s=n("78bc"),u=n("a666"),c=n("30db"),l=n("b2cc"),d=n("b57f"),h=n("b707"),p=n("6816"),f=n("6d8e"),g=n("6a89"),m=n("1b69"),b=n("9e74"),v=n("e8e3"),y=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),_=function(e,t,n,i){function r(e){return e instanceof n?e:new n((function(t){t(e)}))}return new(n||(n=Promise))((function(n,o){function a(e){try{u(i.next(e))}catch(t){o(t)}}function s(e){try{u(i["throw"](e))}catch(t){o(t)}}function u(e){e.done?n(e.value):r(e.value).then(a,s)}u((i=i.apply(e,t||[])).next())}))},C=function(e,t){var n,i,r,o,a={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"===typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(e){return function(t){return u([e,t])}}function u(o){if(n)throw new TypeError("Generator is already executing.");while(a)try{if(n=1,i&&(r=2&o[0]?i["return"]:o[0]?i["throw"]||((r=i["return"])&&r.call(i),0):i.next)&&!(r=r.call(i,o[1])).done)return r;switch(i=0,r&&(o=[2&o[0],r.value]),o[0]){case 0:case 1:r=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,i=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(r=a.trys,!(r=r.length>0&&r[r.length-1])&&(6===o[0]||2===o[0])){a=0;continue}if(3===o[0]&&(!r||o[1]>r[0]&&o[1]<r[3])){a.label=o[1];break}if(6===o[0]&&a.label<r[1]){a.label=r[1],r=o;break}if(r&&a.label<r[2]){a.label=r[2],a.ops.push(o);break}r[2]&&a.ops.pop(),a.trys.pop();continue}o=t.call(e,a)}catch(s){o=[6,s],i=0}finally{n=r=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}},L=function(){function e(e,t){this._link=e,this._provider=t}return e.prototype.toJSON=function(){return{range:this.range,url:this.url,tooltip:this.tooltip}},Object.defineProperty(e.prototype,"range",{get:function(){return this._link.range},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"url",{get:function(){return this._link.url},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"tooltip",{get:function(){return this._link.tooltip},enumerable:!0,configurable:!0}),e.prototype.resolve=function(e){return _(this,void 0,void 0,(function(){var t=this;return C(this,(function(n){return this._link.url?[2,this._link.url]:"function"===typeof this._provider.resolveLink?[2,Promise.resolve(this._provider.resolveLink(this._link,e)).then((function(n){return t._link=n||t._link,t._link.url?t.resolve(e):Promise.reject(new Error("missing"))}))]:[2,Promise.reject(new Error("missing"))]}))}))},e}(),w=function(e){function t(n){for(var i=e.call(this)||this,r=[],o=function(e,n){var i=e.links.map((function(e){return new L(e,n)}));r=t._union(r,i),Object(u["g"])(n)&&a._register(n)},a=this,s=0,c=n;s<c.length;s++){var l=c[s],d=l[0],h=l[1];o(d,h)}return i.links=r,i}return y(t,e),t._union=function(e,t){var n,i,r,o,a=[];for(n=0,r=0,i=e.length,o=t.length;n<i&&r<o;){var s=e[n],u=t[r];if(g["a"].areIntersectingOrTouching(s.range,u.range))n++;else{var c=g["a"].compareRangesUsingStarts(s.range,u.range);c<0?(a.push(s),n++):(a.push(u),r++)}}for(;n<i;n++)a.push(e[n]);for(;r<o;r++)a.push(t[r]);return a},t}(u["a"]);function O(e,t){var n=[],i=h["s"].ordered(e).reverse().map((function(i,r){return Promise.resolve(i.provideLinks(e,t)).then((function(e){e&&(n[r]=[e,i])}),a["f"])}));return Promise.all(i).then((function(){var e=new w(Object(v["d"])(n));return t.isCancellationRequested?(e.dispose(),new w([])):e}))}b["a"].registerCommand("_executeLinkProvider",(function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return _(void 0,void 0,void 0,(function(){var n,i,r,a;return C(this,(function(s){switch(s.label){case 0:return n=t[0],n instanceof f["a"]?(i=e.get(m["a"]).getModel(n),i?[4,O(i,o["a"].None)]:[2,[]]):[2,[]];case 1:return r=s.sent(),r?(a=r.links.slice(0),r.dispose(),[2,a]):[2,[]]}}))}))}));var S=n("b0cd"),k=n("5bd7"),N=n("303e"),M=n("b7d0"),T=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),x=function(){return x=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n],t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},x.apply(this,arguments)},E=function(e,t,n,i){var r,o=arguments.length,a=o<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,n):i;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)a=Reflect.decorate(e,t,n,i);else for(var s=e.length-1;s>=0;s--)(r=e[s])&&(a=(o<3?r(a):o>3?r(t,n,a):r(t,n))||a);return o>3&&a&&Object.defineProperty(t,n,a),a},D=function(e,t){return function(n,i){t(n,i,e)}},I=function(e,t,n,i){function r(e){return e instanceof n?e:new n((function(t){t(e)}))}return new(n||(n=Promise))((function(n,o){function a(e){try{u(i.next(e))}catch(t){o(t)}}function s(e){try{u(i["throw"](e))}catch(t){o(t)}}function u(e){e.done?n(e.value):r(e.value).then(a,s)}u((i=i.apply(e,t||[])).next())}))},A=function(e,t){var n,i,r,o,a={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"===typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(e){return function(t){return u([e,t])}}function u(o){if(n)throw new TypeError("Generator is already executing.");while(a)try{if(n=1,i&&(r=2&o[0]?i["return"]:o[0]?i["throw"]||((r=i["return"])&&r.call(i),0):i.next)&&!(r=r.call(i,o[1])).done)return r;switch(i=0,r&&(o=[2&o[0],r.value]),o[0]){case 0:case 1:r=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,i=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(r=a.trys,!(r=r.length>0&&r[r.length-1])&&(6===o[0]||2===o[0])){a=0;continue}if(3===o[0]&&(!r||o[1]>r[0]&&o[1]<r[3])){a.label=o[1];break}if(6===o[0]&&a.label<r[1]){a.label=r[1],r=o;break}if(r&&a.label<r[2]){a.label=r[2],a.ops.push(o);break}r[2]&&a.ops.pop(),a.trys.pop();continue}o=t.call(e,a)}catch(s){o=[6,s],i=0}finally{n=r=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}};function R(e,t){var n=e.url&&/^command:/i.test(e.url.toString()),r=e.tooltip?e.tooltip:n?i["a"]("links.navigate.executeCmd","Execute command"):i["a"]("links.navigate.follow","Follow link"),o=t?c["e"]?i["a"]("links.navigate.kb.meta.mac","cmd + click"):i["a"]("links.navigate.kb.meta","ctrl + click"):c["e"]?i["a"]("links.navigate.kb.alt.mac","option + click"):i["a"]("links.navigate.kb.alt","alt + click");if(e.url){var a=new s["a"]("",!0).appendMarkdown("["+r+"]("+e.url.toString()+") ("+o+")");return a}return(new s["a"]).appendText(r+" ("+o+")")}var P={general:d["a"].register({stickiness:1,collapseOnReplaceEdit:!0,inlineClassName:"detected-link"}),active:d["a"].register({stickiness:1,collapseOnReplaceEdit:!0,inlineClassName:"detected-link-active"})},j=function(){function e(e,t){this.link=e,this.decorationId=t}return e.decoration=function(t,n){return{range:t.range,options:e._getOptions(t,n,!1)}},e._getOptions=function(e,t,n){var i=x({},n?P.active:P.general);return i.hoverMessage=R(e,t),i},e.prototype.activate=function(t,n){t.changeDecorationOptions(this.decorationId,e._getOptions(this.link,n,!0))},e.prototype.deactivate=function(t,n){t.changeDecorationOptions(this.decorationId,e._getOptions(this.link,n,!1))},e}(),F=function(){function e(e,t,n){var i=this;this.listenersToRemove=new u["b"],this.editor=e,this.openerService=t,this.notificationService=n;var o=new p["a"](e);this.listenersToRemove.add(o),this.listenersToRemove.add(o.onMouseMoveOrRelevantKeyDown((function(e){var t=e[0],n=e[1];i._onEditorMouseMove(t,n)}))),this.listenersToRemove.add(o.onExecute((function(e){i.onEditorMouseUp(e)}))),this.listenersToRemove.add(o.onCancel((function(e){i.cleanUpActiveLinkDecoration()}))),this.enabled=e.getOption(52),this.listenersToRemove.add(e.onDidChangeConfiguration((function(t){var n=e.getOption(52);i.enabled!==n&&(i.enabled=n,i.updateDecorations([]),i.stop(),i.beginCompute())}))),this.listenersToRemove.add(e.onDidChangeModelContent((function(e){return i.onChange()}))),this.listenersToRemove.add(e.onDidChangeModel((function(e){return i.onModelChanged()}))),this.listenersToRemove.add(e.onDidChangeModelLanguage((function(e){return i.onModelModeChanged()}))),this.listenersToRemove.add(h["s"].onDidChange((function(e){return i.onModelModeChanged()}))),this.timeout=new r["e"],this.computePromise=null,this.activeLinksList=null,this.currentOccurrences={},this.activeLinkDecorationId=null,this.beginCompute()}return e.get=function(t){return t.getContribution(e.ID)},e.prototype.onModelChanged=function(){this.currentOccurrences={},this.activeLinkDecorationId=null,this.stop(),this.beginCompute()},e.prototype.onModelModeChanged=function(){this.stop(),this.beginCompute()},e.prototype.onChange=function(){var t=this;this.timeout.setIfNotSet((function(){return t.beginCompute()}),e.RECOMPUTE_TIME)},e.prototype.beginCompute=function(){return I(this,void 0,void 0,(function(){var e,t,n;return A(this,(function(i){switch(i.label){case 0:if(!this.editor.hasModel()||!this.enabled)return[2];if(e=this.editor.getModel(),!h["s"].has(e))return[2];this.activeLinksList&&(this.activeLinksList.dispose(),this.activeLinksList=null),this.computePromise=r["f"]((function(t){return O(e,t)})),i.label=1;case 1:return i.trys.push([1,3,4,5]),t=this,[4,this.computePromise];case 2:return t.activeLinksList=i.sent(),this.updateDecorations(this.activeLinksList.links),[3,5];case 3:return n=i.sent(),Object(a["e"])(n),[3,5];case 4:return this.computePromise=null,[7];case 5:return[2]}}))}))},e.prototype.updateDecorations=function(e){for(var t="altKey"===this.editor.getOption(59),n=[],i=Object.keys(this.currentOccurrences),r=0,o=i.length;r<o;r++){var a=i[r],s=this.currentOccurrences[a];n.push(s.decorationId)}var u=[];if(e)for(var c=0,l=e;c<l.length;c++){var d=l[c];u.push(j.decoration(d,t))}var h=this.editor.deltaDecorations(n,u);this.currentOccurrences={},this.activeLinkDecorationId=null;for(r=0,o=h.length;r<o;r++){s=new j(e[r],h[r]);this.currentOccurrences[s.decorationId]=s}},e.prototype._onEditorMouseMove=function(e,t){var n=this,i="altKey"===this.editor.getOption(59);if(this.isEnabled(e,t)){this.cleanUpActiveLinkDecoration();var r=this.getLinkOccurrence(e.target.position);r&&this.editor.changeDecorations((function(e){r.activate(e,i),n.activeLinkDecorationId=r.decorationId}))}else this.cleanUpActiveLinkDecoration()},e.prototype.cleanUpActiveLinkDecoration=function(){var e="altKey"===this.editor.getOption(59);if(this.activeLinkDecorationId){var t=this.currentOccurrences[this.activeLinkDecorationId];t&&this.editor.changeDecorations((function(n){t.deactivate(n,e)})),this.activeLinkDecorationId=null}},e.prototype.onEditorMouseUp=function(e){if(this.isEnabled(e)){var t=this.getLinkOccurrence(e.target.position);t&&this.openLinkOccurrence(t,e.hasSideBySideModifier,!0)}},e.prototype.openLinkOccurrence=function(e,t,n){var r=this;if(void 0===n&&(n=!1),this.openerService){var s=e.link;s.resolve(o["a"].None).then((function(e){return r.openerService.open(e,{openToSide:t,fromUserGesture:n})}),(function(e){var t=e instanceof Error?e.message:e;"invalid"===t?r.notificationService.warn(i["a"]("invalid.url","Failed to open this link because it is not well-formed: {0}",s.url.toString())):"missing"===t?r.notificationService.warn(i["a"]("missing.url","Failed to open this link because its target is missing.")):Object(a["e"])(e)}))}},e.prototype.getLinkOccurrence=function(e){if(!this.editor.hasModel()||!e)return null;for(var t=this.editor.getModel().getDecorationsInRange({startLineNumber:e.lineNumber,startColumn:e.column,endLineNumber:e.lineNumber,endColumn:e.column},0,!0),n=0,i=t;n<i.length;n++){var r=i[n],o=this.currentOccurrences[r.id];if(o)return o}return null},e.prototype.isEnabled=function(e,t){return Boolean(6===e.target.type&&(e.hasTriggerModifier||t&&t.keyCodeIsTriggerKey))},e.prototype.stop=function(){this.timeout.cancel(),this.activeLinksList&&this.activeLinksList.dispose(),this.computePromise&&(this.computePromise.cancel(),this.computePromise=null)},e.prototype.dispose=function(){this.listenersToRemove.dispose(),this.stop(),this.timeout.dispose()},e.ID="editor.linkDetector",e.RECOMPUTE_TIME=1e3,e=E([D(1,k["a"]),D(2,S["a"])],e),e}(),H=function(e){function t(){return e.call(this,{id:"editor.action.openLink",label:i["a"]("label","Open Link"),alias:"Open Link",precondition:void 0})||this}return T(t,e),t.prototype.run=function(e,t){var n=F.get(t);if(n&&t.hasModel())for(var i=t.getSelections(),r=0,o=i;r<o.length;r++){var a=o[r],s=n.getLinkOccurrence(a.getEndPosition());s&&n.openLinkOccurrence(s,!1)}},t}(l["b"]);Object(l["h"])(F.ID,F),Object(l["f"])(H),Object(M["e"])((function(e,t){var n=e.getColor(N["n"]);n&&t.addRule(".monaco-editor .detected-link-active { color: "+n+" !important; }")}))},dea0:function(e,t,n){"use strict";n.d(t,"a",(function(){return g}));var i=n("c39c"),r=n("5bd7"),o=n("5818"),a=n("fdcc"),s=n("4d05"),u=n("0a0f"),c=n("308f"),l=n("a666"),d=n("b707"),h=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),p=function(e,t,n,i){var r,o=arguments.length,a=o<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,n):i;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)a=Reflect.decorate(e,t,n,i);else for(var s=e.length-1;s>=0;s--)(r=e[s])&&(a=(o<3?r(a):o>3?r(t,n,a):r(t,n))||a);return o>3&&a&&Object.defineProperty(t,n,a),a},f=function(e,t){return function(n,i){t(n,i,e)}},g=function(e){function t(t,n,i){void 0===i&&(i=r["b"]);var o=e.call(this)||this;return o._editor=t,o._modeService=n,o._openerService=i,o._onDidRenderCodeBlock=o._register(new c["a"]),o.onDidRenderCodeBlock=o._onDidRenderCodeBlock.event,o}return h(t,e),t.prototype.getOptions=function(e){var t=this;return{codeBlockRenderer:function(e,n){var i=null;if(e)i=t._modeService.getModeIdForLanguageName(e);else{var r=t._editor.getModel();r&&(i=r.getLanguageIdentifier().language)}return t._modeService.triggerMode(i||""),Promise.resolve(!0).then((function(e){var t=d["B"].getPromise(i||"");return t?t.then((function(e){return Object(s["b"])(n,e)})):Object(s["b"])(n,void 0)})).then((function(e){return'<span style="font-family: '+t._editor.getOption(34).fontFamily+'">'+e+"</span>"}))},codeBlockRenderCallback:function(){return t._onDidRenderCodeBlock.fire()},actionHandler:{callback:function(e){t._openerService.open(e,{fromUserGesture:!0}).catch(a["e"])},disposeables:e}}},t.prototype.render=function(e){var t,n=new l["b"];return t=e?Object(i["a"])(e,this.getOptions(n)):document.createElement("span"),{element:t,dispose:function(){return n.dispose()}}},t=p([f(1,o["a"]),f(2,Object(u["d"])(r["a"]))],t),t}(l["a"])},f17c:function(e,t,n){"use strict";n.r(t),n.d(t,"InsertCursorAbove",(function(){return _})),n.d(t,"InsertCursorBelow",(function(){return C})),n.d(t,"MultiCursorSessionResult",(function(){return S})),n.d(t,"MultiCursorSession",(function(){return k})),n.d(t,"MultiCursorSelectionController",(function(){return N})),n.d(t,"MultiCursorSelectionControllerAction",(function(){return M})),n.d(t,"AddSelectionToNextFindMatchAction",(function(){return T})),n.d(t,"AddSelectionToPreviousFindMatchAction",(function(){return x})),n.d(t,"MoveSelectionToNextFindMatchAction",(function(){return E})),n.d(t,"MoveSelectionToPreviousFindMatchAction",(function(){return D})),n.d(t,"SelectHighlightsAction",(function(){return I})),n.d(t,"CompatChangeAll",(function(){return A})),n.d(t,"SelectionHighlighter",(function(){return P}));var i=n("dff7"),r=n("5fe7"),o=n("fe45"),a=n("a666"),s=n("b2cc"),u=n("a007"),c=n("6a89"),l=n("8025"),d=n("c101"),h=n("3352"),p=n("b57f"),f=n("b707"),g=n("a106"),m=n("303e"),b=n("b7d0"),v=n("4fc3"),y=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),_=function(e){function t(){return e.call(this,{id:"editor.action.insertCursorAbove",label:i["a"]("mutlicursor.insertAbove","Add Cursor Above"),alias:"Add Cursor Above",precondition:void 0,kbOpts:{kbExpr:d["a"].editorTextFocus,primary:2576,linux:{primary:1552,secondary:[3088]},weight:100},menuOpts:{menuId:25,group:"3_multi",title:i["a"]({key:"miInsertCursorAbove",comment:["&& denotes a mnemonic"]},"&&Add Cursor Above"),order:2}})||this}return y(t,e),t.prototype.run=function(e,t,n){if(t.hasModel()){var i=n&&!0===n.logicalLine,r=t._getCursors(),o=r.context;o.config.readOnly||(o.model.pushStackElement(),r.setStates(n.source,3,u["b"].addCursorUp(o,r.getAll(),i)),r.reveal(n.source,!0,1,0))}},t}(s["b"]),C=function(e){function t(){return e.call(this,{id:"editor.action.insertCursorBelow",label:i["a"]("mutlicursor.insertBelow","Add Cursor Below"),alias:"Add Cursor Below",precondition:void 0,kbOpts:{kbExpr:d["a"].editorTextFocus,primary:2578,linux:{primary:1554,secondary:[3090]},weight:100},menuOpts:{menuId:25,group:"3_multi",title:i["a"]({key:"miInsertCursorBelow",comment:["&& denotes a mnemonic"]},"A&&dd Cursor Below"),order:3}})||this}return y(t,e),t.prototype.run=function(e,t,n){if(t.hasModel()){var i=n&&!0===n.logicalLine,r=t._getCursors(),o=r.context;o.config.readOnly||(o.model.pushStackElement(),r.setStates(n.source,3,u["b"].addCursorDown(o,r.getAll(),i)),r.reveal(n.source,!0,2,0))}},t}(s["b"]),L=function(e){function t(){return e.call(this,{id:"editor.action.insertCursorAtEndOfEachLineSelected",label:i["a"]("mutlicursor.insertAtEndOfEachLineSelected","Add Cursors to Line Ends"),alias:"Add Cursors to Line Ends",precondition:void 0,kbOpts:{kbExpr:d["a"].editorTextFocus,primary:1575,weight:100},menuOpts:{menuId:25,group:"3_multi",title:i["a"]({key:"miInsertCursorAtEndOfEachLineSelected",comment:["&& denotes a mnemonic"]},"Add C&&ursors to Line Ends"),order:4}})||this}return y(t,e),t.prototype.getCursorsForSelection=function(e,t,n){if(!e.isEmpty()){for(var i=e.startLineNumber;i<e.endLineNumber;i++){var r=t.getLineMaxColumn(i);n.push(new l["a"](i,r,i,r))}e.endColumn>1&&n.push(new l["a"](e.endLineNumber,e.endColumn,e.endLineNumber,e.endColumn))}},t.prototype.run=function(e,t){var n=this;if(t.hasModel()){var i=t.getModel(),r=t.getSelections(),o=[];r.forEach((function(e){return n.getCursorsForSelection(e,i,o)})),o.length>0&&t.setSelections(o)}},t}(s["b"]),w=function(e){function t(){return e.call(this,{id:"editor.action.addCursorsToBottom",label:i["a"]("mutlicursor.addCursorsToBottom","Add Cursors To Bottom"),alias:"Add Cursors To Bottom",precondition:void 0})||this}return y(t,e),t.prototype.run=function(e,t){if(t.hasModel()){for(var n=t.getSelections(),i=t.getModel().getLineCount(),r=[],o=n[0].startLineNumber;o<=i;o++)r.push(new l["a"](o,n[0].startColumn,o,n[0].endColumn));r.length>0&&t.setSelections(r)}},t}(s["b"]),O=function(e){function t(){return e.call(this,{id:"editor.action.addCursorsToTop",label:i["a"]("mutlicursor.addCursorsToTop","Add Cursors To Top"),alias:"Add Cursors To Top",precondition:void 0})||this}return y(t,e),t.prototype.run=function(e,t){if(t.hasModel()){for(var n=t.getSelections(),i=[],r=n[0].startLineNumber;r>=1;r--)i.push(new l["a"](r,n[0].startColumn,r,n[0].endColumn));i.length>0&&t.setSelections(i)}},t}(s["b"]),S=function(){function e(e,t,n){this.selections=e,this.revealRange=t,this.revealScrollType=n}return e}(),k=function(){function e(e,t,n,i,r,o,a){this._editor=e,this.findController=t,this.isDisconnectedFromFindController=n,this.searchText=i,this.wholeWord=r,this.matchCase=o,this.currentMatch=a}return e.create=function(t,n){if(!t.hasModel())return null;var i=n.getState();if(!t.hasTextFocus()&&i.isRevealed&&i.searchString.length>0)return new e(t,n,!1,i.searchString,i.wholeWord,i.matchCase,null);var r,o,a=!1,s=t.getSelections();1===s.length&&s[0].isEmpty()?(a=!0,r=!0,o=!0):(r=i.wholeWord,o=i.matchCase);var u,c=t.getSelection(),d=null;if(c.isEmpty()){var h=t.getModel().getWordAtPosition(c.getStartPosition());if(!h)return null;u=h.word,d=new l["a"](c.startLineNumber,h.startColumn,c.startLineNumber,h.endColumn)}else u=t.getModel().getValueInRange(c).replace(/\r\n/g,"\n");return new e(t,n,a,u,r,o,d)},e.prototype.addSelectionToNextFindMatch=function(){if(!this._editor.hasModel())return null;var e=this._getNextMatch();if(!e)return null;var t=this._editor.getSelections();return new S(t.concat(e),e,0)},e.prototype.moveSelectionToNextFindMatch=function(){if(!this._editor.hasModel())return null;var e=this._getNextMatch();if(!e)return null;var t=this._editor.getSelections();return new S(t.slice(0,t.length-1).concat(e),e,0)},e.prototype._getNextMatch=function(){if(!this._editor.hasModel())return null;if(this.currentMatch){var e=this.currentMatch;return this.currentMatch=null,e}this.findController.highlightFindOptions();var t=this._editor.getSelections(),n=t[t.length-1],i=this._editor.getModel().findNextMatch(this.searchText,n.getEndPosition(),!1,this.matchCase,this.wholeWord?this._editor.getOption(96):null,!1);return i?new l["a"](i.range.startLineNumber,i.range.startColumn,i.range.endLineNumber,i.range.endColumn):null},e.prototype.addSelectionToPreviousFindMatch=function(){if(!this._editor.hasModel())return null;var e=this._getPreviousMatch();if(!e)return null;var t=this._editor.getSelections();return new S(t.concat(e),e,0)},e.prototype.moveSelectionToPreviousFindMatch=function(){if(!this._editor.hasModel())return null;var e=this._getPreviousMatch();if(!e)return null;var t=this._editor.getSelections();return new S(t.slice(0,t.length-1).concat(e),e,0)},e.prototype._getPreviousMatch=function(){if(!this._editor.hasModel())return null;if(this.currentMatch){var e=this.currentMatch;return this.currentMatch=null,e}this.findController.highlightFindOptions();var t=this._editor.getSelections(),n=t[t.length-1],i=this._editor.getModel().findPreviousMatch(this.searchText,n.getStartPosition(),!1,this.matchCase,this.wholeWord?this._editor.getOption(96):null,!1);return i?new l["a"](i.range.startLineNumber,i.range.startColumn,i.range.endLineNumber,i.range.endColumn):null},e.prototype.selectAll=function(){return this._editor.hasModel()?(this.findController.highlightFindOptions(),this._editor.getModel().findMatches(this.searchText,!0,!1,this.matchCase,this.wholeWord?this._editor.getOption(96):null,!1,1073741824)):[]},e}(),N=function(e){function t(t){var n=e.call(this)||this;return n._sessionDispose=n._register(new a["b"]),n._editor=t,n._ignoreSelectionChange=!1,n._session=null,n}return y(t,e),t.get=function(e){return e.getContribution(t.ID)},t.prototype.dispose=function(){this._endSession(),e.prototype.dispose.call(this)},t.prototype._beginSessionIfNeeded=function(e){var t=this;if(!this._session){var n=k.create(this._editor,e);if(!n)return;this._session=n;var i={searchString:this._session.searchText};this._session.isDisconnectedFromFindController&&(i.wholeWordOverride=1,i.matchCaseOverride=1,i.isRegexOverride=2),e.getState().change(i,!1),this._sessionDispose.add(this._editor.onDidChangeCursorSelection((function(e){t._ignoreSelectionChange||t._endSession()}))),this._sessionDispose.add(this._editor.onDidBlurEditorText((function(){t._endSession()}))),this._sessionDispose.add(e.getState().onFindReplaceStateChange((function(e){(e.matchCase||e.wholeWord)&&t._endSession()})))}},t.prototype._endSession=function(){if(this._sessionDispose.clear(),this._session&&this._session.isDisconnectedFromFindController){var e={wholeWordOverride:0,matchCaseOverride:0,isRegexOverride:0};this._session.findController.getState().change(e,!1)}this._session=null},t.prototype._setSelections=function(e){this._ignoreSelectionChange=!0,this._editor.setSelections(e),this._ignoreSelectionChange=!1},t.prototype._expandEmptyToWord=function(e,t){if(!t.isEmpty())return t;var n=e.getWordAtPosition(t.getStartPosition());return n?new l["a"](t.startLineNumber,n.startColumn,t.startLineNumber,n.endColumn):t},t.prototype._applySessionResult=function(e){e&&(this._setSelections(e.selections),e.revealRange&&this._editor.revealRangeInCenterIfOutsideViewport(e.revealRange,e.revealScrollType))},t.prototype.getSession=function(e){return this._session},t.prototype.addSelectionToNextFindMatch=function(e){if(this._editor.hasModel()){if(!this._session){var t=this._editor.getSelections();if(t.length>1){var n=e.getState(),i=n.matchCase,r=j(this._editor.getModel(),t,i);if(!r){for(var o=this._editor.getModel(),a=[],s=0,u=t.length;s<u;s++)a[s]=this._expandEmptyToWord(o,t[s]);return void this._editor.setSelections(a)}}}this._beginSessionIfNeeded(e),this._session&&this._applySessionResult(this._session.addSelectionToNextFindMatch())}},t.prototype.addSelectionToPreviousFindMatch=function(e){this._beginSessionIfNeeded(e),this._session&&this._applySessionResult(this._session.addSelectionToPreviousFindMatch())},t.prototype.moveSelectionToNextFindMatch=function(e){this._beginSessionIfNeeded(e),this._session&&this._applySessionResult(this._session.moveSelectionToNextFindMatch())},t.prototype.moveSelectionToPreviousFindMatch=function(e){this._beginSessionIfNeeded(e),this._session&&this._applySessionResult(this._session.moveSelectionToPreviousFindMatch())},t.prototype.selectAll=function(e){if(this._editor.hasModel()){var t=null,n=e.getState();if(n.isRevealed&&n.searchString.length>0&&n.isRegex)t=this._editor.getModel().findMatches(n.searchString,!0,n.isRegex,n.matchCase,n.wholeWord?this._editor.getOption(96):null,!1,1073741824);else{if(this._beginSessionIfNeeded(e),!this._session)return;t=this._session.selectAll()}if(n.searchScope){for(var i=n.searchScope,r=[],o=0;o<t.length;o++)t[o].range.endLineNumber<=i.endLineNumber&&t[o].range.startLineNumber>=i.startLineNumber&&r.push(t[o]);t=r}if(t.length>0){for(var a=this._editor.getSelection(),s=(o=0,t.length);o<s;o++){var u=t[o],c=u.range.intersectRanges(a);if(c){t[o]=t[0],t[0]=u;break}}this._setSelections(t.map((function(e){return new l["a"](e.range.startLineNumber,e.range.startColumn,e.range.endLineNumber,e.range.endColumn)})))}}},t.ID="editor.contrib.multiCursorController",t}(a["a"]),M=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return y(t,e),t.prototype.run=function(e,t){var n=N.get(t);if(n){var i=g["CommonFindController"].get(t);i&&this._run(n,i)}},t}(s["b"]),T=function(e){function t(){return e.call(this,{id:"editor.action.addSelectionToNextFindMatch",label:i["a"]("addSelectionToNextFindMatch","Add Selection To Next Find Match"),alias:"Add Selection To Next Find Match",precondition:void 0,kbOpts:{kbExpr:d["a"].focus,primary:2082,weight:100},menuOpts:{menuId:25,group:"3_multi",title:i["a"]({key:"miAddSelectionToNextFindMatch",comment:["&& denotes a mnemonic"]},"Add &&Next Occurrence"),order:5}})||this}return y(t,e),t.prototype._run=function(e,t){e.addSelectionToNextFindMatch(t)},t}(M),x=function(e){function t(){return e.call(this,{id:"editor.action.addSelectionToPreviousFindMatch",label:i["a"]("addSelectionToPreviousFindMatch","Add Selection To Previous Find Match"),alias:"Add Selection To Previous Find Match",precondition:void 0,menuOpts:{menuId:25,group:"3_multi",title:i["a"]({key:"miAddSelectionToPreviousFindMatch",comment:["&& denotes a mnemonic"]},"Add P&&revious Occurrence"),order:6}})||this}return y(t,e),t.prototype._run=function(e,t){e.addSelectionToPreviousFindMatch(t)},t}(M),E=function(e){function t(){return e.call(this,{id:"editor.action.moveSelectionToNextFindMatch",label:i["a"]("moveSelectionToNextFindMatch","Move Last Selection To Next Find Match"),alias:"Move Last Selection To Next Find Match",precondition:void 0,kbOpts:{kbExpr:d["a"].focus,primary:Object(o["a"])(2089,2082),weight:100}})||this}return y(t,e),t.prototype._run=function(e,t){e.moveSelectionToNextFindMatch(t)},t}(M),D=function(e){function t(){return e.call(this,{id:"editor.action.moveSelectionToPreviousFindMatch",label:i["a"]("moveSelectionToPreviousFindMatch","Move Last Selection To Previous Find Match"),alias:"Move Last Selection To Previous Find Match",precondition:void 0})||this}return y(t,e),t.prototype._run=function(e,t){e.moveSelectionToPreviousFindMatch(t)},t}(M),I=function(e){function t(){return e.call(this,{id:"editor.action.selectHighlights",label:i["a"]("selectAllOccurrencesOfFindMatch","Select All Occurrences of Find Match"),alias:"Select All Occurrences of Find Match",precondition:void 0,kbOpts:{kbExpr:d["a"].focus,primary:3114,weight:100},menuOpts:{menuId:25,group:"3_multi",title:i["a"]({key:"miSelectHighlights",comment:["&& denotes a mnemonic"]},"Select All &&Occurrences"),order:7}})||this}return y(t,e),t.prototype._run=function(e,t){e.selectAll(t)},t}(M),A=function(e){function t(){return e.call(this,{id:"editor.action.changeAll",label:i["a"]("changeAll.label","Change All Occurrences"),alias:"Change All Occurrences",precondition:v["a"].and(d["a"].writable,d["a"].editorTextFocus),kbOpts:{kbExpr:d["a"].editorTextFocus,primary:2108,weight:100},contextMenuOpts:{group:"1_modification",order:1.2}})||this}return y(t,e),t.prototype._run=function(e,t){e.selectAll(t)},t}(M),R=function(){function e(e,t,n){this.searchText=e,this.matchCase=t,this.wordSeparators=n}return e.softEquals=function(e,t){return!e&&!t||!(!e||!t)&&(e.searchText===t.searchText&&e.matchCase===t.matchCase&&e.wordSeparators===t.wordSeparators)},e}(),P=function(e){function t(t){var n=e.call(this)||this;return n.editor=t,n._isEnabled=t.getOption(82),n.decorations=[],n.updateSoon=n._register(new r["d"]((function(){return n._update()}),300)),n.state=null,n._register(t.onDidChangeConfiguration((function(e){n._isEnabled=t.getOption(82)}))),n._register(t.onDidChangeCursorSelection((function(e){n._isEnabled&&(e.selection.isEmpty()?3===e.reason?(n.state&&n._setState(null),n.updateSoon.schedule()):n._setState(null):n._update())}))),n._register(t.onDidChangeModel((function(e){n._setState(null)}))),n._register(g["CommonFindController"].get(t).getState().onFindReplaceStateChange((function(e){n._update()}))),n}return y(t,e),t.prototype._update=function(){this._setState(t._createState(this._isEnabled,this.editor))},t._createState=function(e,t){if(!e)return null;if(!t.hasModel())return null;var n=t.getSelection();if(n.startLineNumber!==n.endLineNumber)return null;var i=N.get(t);if(!i)return null;var r=g["CommonFindController"].get(t);if(!r)return null;var o=i.getSession(r);if(!o){var a=t.getSelections();if(a.length>1){var s=r.getState(),u=s.matchCase,c=j(t.getModel(),a,u);if(!c)return null}o=k.create(t,r)}if(!o)return null;if(o.currentMatch)return null;if(/^[ \t]+$/.test(o.searchText))return null;if(o.searchText.length>200)return null;var l=r.getState(),d=l.matchCase;if(l.isRevealed){var h=l.searchString;d||(h=h.toLowerCase());var p=o.searchText;if(d||(p=p.toLowerCase()),h===p&&o.matchCase===l.matchCase&&o.wholeWord===l.wholeWord&&!l.isRegex)return null}return new R(o.searchText,o.matchCase,o.wholeWord?t.getOption(96):null)},t.prototype._setState=function(e){if(R.softEquals(this.state,e))this.state=e;else if(this.state=e,this.state){if(this.editor.hasModel()){var n=this.editor.getModel();if(!n.isTooLargeForTokenization()){var i=f["i"].has(n),r=n.findMatches(this.state.searchText,!0,!1,this.state.matchCase,this.state.wordSeparators,!1).map((function(e){return e.range}));r.sort(c["a"].compareRangesUsingStarts);var o=this.editor.getSelections();o.sort(c["a"].compareRangesUsingStarts);for(var a=[],s=0,u=0,l=r.length,d=o.length;s<l;){var h=r[s];if(u>=d)a.push(h),s++;else{var p=c["a"].compareRangesUsingStarts(h,o[u]);p<0?(!o[u].isEmpty()&&c["a"].areIntersecting(h,o[u])||a.push(h),s++):(p>0||s++,u++)}}var g=a.map((function(e){return{range:e,options:i?t._SELECTION_HIGHLIGHT:t._SELECTION_HIGHLIGHT_OVERVIEW}}));this.decorations=this.editor.deltaDecorations(this.decorations,g)}}}else this.decorations=this.editor.deltaDecorations(this.decorations,[])},t.prototype.dispose=function(){this._setState(null),e.prototype.dispose.call(this)},t.ID="editor.contrib.selectionHighlighter",t._SELECTION_HIGHLIGHT_OVERVIEW=p["a"].register({stickiness:1,className:"selectionHighlight",overviewRuler:{color:Object(b["f"])(m["Mb"]),position:h["d"].Center}}),t._SELECTION_HIGHLIGHT=p["a"].register({stickiness:1,className:"selectionHighlight"}),t}(a["a"]);function j(e,t,n){for(var i=F(e,t[0],!n),r=1,o=t.length;r<o;r++){var a=t[r];if(a.isEmpty())return!1;var s=F(e,a,!n);if(i!==s)return!1}return!0}function F(e,t,n){var i=e.getValueInRange(t);return n?i.toLowerCase():i}Object(s["h"])(N.ID,N),Object(s["h"])(P.ID,P),Object(s["f"])(_),Object(s["f"])(C),Object(s["f"])(L),Object(s["f"])(T),Object(s["f"])(x),Object(s["f"])(E),Object(s["f"])(D),Object(s["f"])(I),Object(s["f"])(A),Object(s["f"])(w),Object(s["f"])(O)},f86e:function(e,t,n){"use strict";n.d(t,"a",(function(){return f}));var i=n("6d8e"),r=n("6a89"),o=n("1b69"),a=n("2504"),s=n("b78f"),u=n("70f5"),c=n("be5f"),l=n("9e74"),d=n("ef8e"),h=function(e,t,n,i){function r(e){return e instanceof n?e:new n((function(t){t(e)}))}return new(n||(n=Promise))((function(n,o){function a(e){try{u(i.next(e))}catch(t){o(t)}}function s(e){try{u(i["throw"](e))}catch(t){o(t)}}function u(e){e.done?n(e.value):r(e.value).then(a,s)}u((i=i.apply(e,t||[])).next())}))},p=function(e,t){var n,i,r,o,a={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"===typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(e){return function(t){return u([e,t])}}function u(o){if(n)throw new TypeError("Generator is already executing.");while(a)try{if(n=1,i&&(r=2&o[0]?i["return"]:o[0]?i["throw"]||((r=i["return"])&&r.call(i),0):i.next)&&!(r=r.call(i,o[1])).done)return r;switch(i=0,r&&(o=[2&o[0],r.value]),o[0]){case 0:case 1:r=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,i=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(r=a.trys,!(r=r.length>0&&r[r.length-1])&&(6===o[0]||2===o[0])){a=0;continue}if(3===o[0]&&(!r||o[1]>r[0]&&o[1]<r[3])){a.label=o[1];break}if(6===o[0]&&a.label<r[1]){a.label=r[1],r=o;break}if(r&&a.label<r[2]){a.label=r[2],a.ops.push(o);break}r[2]&&a.ops.pop(),a.trys.pop();continue}o=t.call(e,a)}catch(s){o=[6,s],i=0}finally{n=r=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}};function f(e,t,n){return h(this,void 0,void 0,(function(){var i,r,o,a,s,l;return p(this,(function(d){switch(d.label){case 0:return[4,u["b"].create(e,n)];case 1:for(i=d.sent(),r=[],o=0,a=Object(c["d"])(i.children);o<a.length;o++)s=a[o],s instanceof u["a"]?r.push(s.symbol):r.push.apply(r,Object(c["d"])(s.children).map((function(e){return e.symbol})));return l=[],n.isCancellationRequested?[2,l]:(t?m(l,r,""):l=r,[2,l.sort(g)])}}))}))}function g(e,t){return r["a"].compareRangesUsingStarts(e.range,t.range)}function m(e,t,n){for(var i=0,r=t;i<r.length;i++){var o=r[i];e.push({kind:o.kind,tags:o.tags,name:o.name,detail:o.detail,containerName:o.containerName||n,range:o.range,selectionRange:o.selectionRange,children:void 0}),o.children&&m(e,o.children,o.name)}}l["a"].registerCommand("_executeDocumentSymbolProvider",(function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];return h(this,void 0,void 0,(function(){var n,r,u;return p(this,(function(c){switch(c.label){case 0:return n=t[0],Object(d["a"])(i["a"].isUri(n)),r=e.get(o["a"]).getModel(n),r?[2,f(r,!1,a["a"].None)]:[4,e.get(s["a"]).createModelReference(n)];case 1:u=c.sent(),c.label=2;case 2:return c.trys.push([2,,4,5]),[4,f(u.object.textEditorModel,!1,a["a"].None)];case 3:return[2,c.sent()];case 4:return u.dispose(),[7];case 5:return[2]}}))}))}))}}]);