(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-239d1576"],{"0d30":function(e,t,i){},"4a64":function(e,t,i){"use strict";i.d(t,"a",(function(){return re}));i("94a7");var o=i("dff7"),n=i("11f7"),r=i("6653"),a=i("70c3"),s=i("5fe7"),d=i("308f"),l=i("a666"),u=i("aa3d"),h=i("1ddc"),c=i("bc04"),f=i("5717"),g=i("9c1d"),m=(i("0d30"),i("5aa5")),p=i("1898"),_=i("f070"),v=i("b2cc"),D=i("fd49"),y=i("e1b5"),w=i("7061"),b=i("918c"),C=i("6da2"),E=i("a8d0"),S=i("4fc3"),L=i("303e"),N=i("b7d0"),I=function(){var e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])},e(t,i)};return function(t,i){function o(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(o.prototype=i.prototype,new o)}}(),R=3,O=function(){function e(e,t,i,o){this.originalLineStart=e,this.originalLineEnd=t,this.modifiedLineStart=i,this.modifiedLineEnd=o}return e.prototype.getType=function(){return 0===this.originalLineStart?1:0===this.modifiedLineStart?2:0},e}(),M=function(){function e(e){this.entries=e}return e}(),T=function(e){function t(t){var i=e.call(this)||this;return i._width=0,i._diffEditor=t,i._isVisible=!1,i.shadow=Object(r["b"])(document.createElement("div")),i.shadow.setClassName("diff-review-shadow"),i.actionBarContainer=Object(r["b"])(document.createElement("div")),i.actionBarContainer.setClassName("diff-review-actions"),i._actionBar=i._register(new m["a"](i.actionBarContainer.domNode)),i._actionBar.push(new _["a"]("diffreview.close",o["a"]("label.close","Close"),"close-diff-review",!0,(function(){return i.hide(),Promise.resolve(null)})),{label:!1,icon:!0}),i.domNode=Object(r["b"])(document.createElement("div")),i.domNode.setClassName("diff-review monaco-editor-background"),i._content=Object(r["b"])(document.createElement("div")),i._content.setClassName("diff-review-content"),i.scrollbar=i._register(new p["a"](i._content.domNode,{})),i.domNode.domNode.appendChild(i.scrollbar.getDomNode()),i._register(t.onDidUpdateDiff((function(){i._isVisible&&(i._diffs=i._compute(),i._render())}))),i._register(t.getModifiedEditor().onDidChangeCursorPosition((function(){i._isVisible&&i._render()}))),i._register(t.getOriginalEditor().onDidFocusEditorWidget((function(){i._isVisible&&i.hide()}))),i._register(t.getModifiedEditor().onDidFocusEditorWidget((function(){i._isVisible&&i.hide()}))),i._register(n["o"](i.domNode.domNode,"click",(function(e){e.preventDefault();var t=n["x"](e.target,"diff-review-row");t&&i._goToRow(t)}))),i._register(n["o"](i.domNode.domNode,"keydown",(function(e){(e.equals(18)||e.equals(2066)||e.equals(530))&&(e.preventDefault(),i._goToRow(i._getNextRow())),(e.equals(16)||e.equals(2064)||e.equals(528))&&(e.preventDefault(),i._goToRow(i._getPrevRow())),(e.equals(9)||e.equals(2057)||e.equals(521)||e.equals(1033))&&(e.preventDefault(),i.hide()),(e.equals(10)||e.equals(3))&&(e.preventDefault(),i.accept())}))),i._diffs=[],i._currentDiff=null,i}return I(t,e),t.prototype.prev=function(){var e=0;if(this._isVisible||(this._diffs=this._compute()),this._isVisible){for(var t=-1,i=0,o=this._diffs.length;i<o;i++)if(this._diffs[i]===this._currentDiff){t=i;break}e=this._diffs.length+t-1}else e=this._findDiffIndex(this._diffEditor.getPosition());0!==this._diffs.length&&(e%=this._diffs.length,this._diffEditor.setPosition(new w["a"](this._diffs[e].entries[0].modifiedLineStart,1)),this._isVisible=!0,this._diffEditor.doLayout(),this._render(),this._goToRow(this._getNextRow()))},t.prototype.next=function(){var e=0;if(this._isVisible||(this._diffs=this._compute()),this._isVisible){for(var t=-1,i=0,o=this._diffs.length;i<o;i++)if(this._diffs[i]===this._currentDiff){t=i;break}e=t+1}else e=this._findDiffIndex(this._diffEditor.getPosition());0!==this._diffs.length&&(e%=this._diffs.length,this._diffEditor.setPosition(new w["a"](this._diffs[e].entries[0].modifiedLineStart,1)),this._isVisible=!0,this._diffEditor.doLayout(),this._render(),this._goToRow(this._getNextRow()))},t.prototype.accept=function(){var e=-1,t=this._getCurrentFocusedRow();if(t){var i=parseInt(t.getAttribute("data-line"),10);isNaN(i)||(e=i)}this.hide(),-1!==e&&(this._diffEditor.setPosition(new w["a"](e,1)),this._diffEditor.revealPosition(new w["a"](e,1),1))},t.prototype.hide=function(){this._isVisible=!1,this._diffEditor.focus(),this._diffEditor.doLayout(),this._render()},t.prototype._getPrevRow=function(){var e=this._getCurrentFocusedRow();return e?e.previousElementSibling?e.previousElementSibling:e:this._getFirstRow()},t.prototype._getNextRow=function(){var e=this._getCurrentFocusedRow();return e?e.nextElementSibling?e.nextElementSibling:e:this._getFirstRow()},t.prototype._getFirstRow=function(){return this.domNode.domNode.querySelector(".diff-review-row")},t.prototype._getCurrentFocusedRow=function(){var e=document.activeElement;return e&&/diff-review-row/.test(e.className)?e:null},t.prototype._goToRow=function(e){var t=this._getCurrentFocusedRow();e.tabIndex=0,e.focus(),t&&t!==e&&(t.tabIndex=-1),this.scrollbar.scanDomNode()},t.prototype.isVisible=function(){return this._isVisible},t.prototype.layout=function(e,t,i){this._width=t,this.shadow.setTop(e-6),this.shadow.setWidth(t),this.shadow.setHeight(this._isVisible?6:0),this.domNode.setTop(e),this.domNode.setWidth(t),this.domNode.setHeight(i),this._content.setHeight(i),this._content.setWidth(t),this._isVisible?(this.actionBarContainer.setAttribute("aria-hidden","false"),this.actionBarContainer.setDisplay("block")):(this.actionBarContainer.setAttribute("aria-hidden","true"),this.actionBarContainer.setDisplay("none"))},t.prototype._compute=function(){var e=this._diffEditor.getLineChanges();if(!e||0===e.length)return[];var i=this._diffEditor.getOriginalEditor().getModel(),o=this._diffEditor.getModifiedEditor().getModel();return i&&o?t._mergeAdjacent(e,i.getLineCount(),o.getLineCount()):[]},t._mergeAdjacent=function(e,t,i){if(!e||0===e.length)return[];for(var o=[],n=0,r=0,a=e.length;r<a;r++){var s=e[r],d=s.originalStartLineNumber,l=s.originalEndLineNumber,u=s.modifiedStartLineNumber,h=s.modifiedEndLineNumber,c=[],f=0,g=0===l?d:d-1,m=0===h?u:u-1,p=1,_=1;if(r>0){var v=e[r-1];p=0===v.originalEndLineNumber?v.originalStartLineNumber+1:v.originalEndLineNumber+1,_=0===v.modifiedEndLineNumber?v.modifiedStartLineNumber+1:v.modifiedEndLineNumber+1}var D=g-R+1,y=m-R+1;if(D<p){var w=p-D;D+=w,y+=w}if(y<_){w=_-y;D+=w,y+=w}c[f++]=new O(D,g,y,m),0!==l&&(c[f++]=new O(d,l,0,0)),0!==h&&(c[f++]=new O(0,0,u,h));var b=0===l?d+1:l+1,C=0===h?u+1:h+1,E=t,S=i;if(r+1<a){var L=e[r+1];E=0===L.originalEndLineNumber?L.originalStartLineNumber:L.originalStartLineNumber-1,S=0===L.modifiedEndLineNumber?L.modifiedStartLineNumber:L.modifiedStartLineNumber-1}var N=b+R-1,I=C+R-1;if(N>E){w=E-N;N+=w,I+=w}if(I>S){w=S-I;N+=w,I+=w}c[f++]=new O(b,N,C,I),o[n++]=new M(c)}var T=o[0].entries,x=[],P=0;for(r=1,a=o.length;r<a;r++){var V=o[r].entries,F=T[T.length-1],W=V[0];0===F.getType()&&0===W.getType()&&W.originalLineStart<=F.originalLineEnd?(T[T.length-1]=new O(F.originalLineStart,W.originalLineEnd,F.modifiedLineStart,W.modifiedLineEnd),T=T.concat(V.slice(1))):(x[P++]=new M(T),T=V)}return x[P++]=new M(T),x},t.prototype._findDiffIndex=function(e){for(var t=e.lineNumber,i=0,o=this._diffs.length;i<o;i++){var n=this._diffs[i].entries,r=n[n.length-1].modifiedLineEnd;if(t<=r)return i}return 0},t.prototype._render=function(){var e=this._diffEditor.getOriginalEditor().getOptions(),i=this._diffEditor.getModifiedEditor().getOptions(),r=this._diffEditor.getOriginalEditor().getModel(),a=this._diffEditor.getModifiedEditor().getModel(),s=r.getOptions(),d=a.getOptions();if(!this._isVisible||!r||!a)return n["t"](this._content.domNode),this._currentDiff=null,void this.scrollbar.scanDomNode();var l=this._findDiffIndex(this._diffEditor.getPosition());if(this._diffs[l]!==this._currentDiff){this._currentDiff=this._diffs[l];var u=this._diffs[l].entries,c=document.createElement("div");c.className="diff-review-table",c.setAttribute("role","list"),h["a"].applyFontInfoSlow(c,i.get(34));for(var f=0,g=0,m=0,p=0,_=0,v=u.length;_<v;_++){var D=u[_],y=D.originalLineStart,w=D.originalLineEnd,b=D.modifiedLineStart,C=D.modifiedLineEnd;0!==y&&(0===f||y<f)&&(f=y),0!==w&&(0===g||w>g)&&(g=w),0!==b&&(0===m||b<m)&&(m=b),0!==C&&(0===p||C>p)&&(p=C)}var E=document.createElement("div");E.className="diff-review-row";var S=document.createElement("div");S.className="diff-review-cell diff-review-summary";var L=g-f+1,N=p-m+1;S.appendChild(document.createTextNode(l+1+"/"+this._diffs.length+": @@ -"+f+","+L+" +"+m+","+N+" @@")),E.setAttribute("data-line",String(m));var I=function(e){return 0===e?o["a"]("no_lines","no lines"):1===e?o["a"]("one_line","1 line"):o["a"]("more_lines","{0} lines",e)},R=I(L),O=I(N);E.setAttribute("aria-label",o["a"]({key:"header",comment:["This is the ARIA label for a git diff header.","A git diff header looks like this: @@ -154,12 +159,39 @@.","That encodes that at original line 154 (which is now line 159), 12 lines were removed/changed with 39 lines.","Variables 0 and 1 refer to the diff index out of total number of diffs.","Variables 2 and 4 will be numbers (a line number).",'Variables 3 and 5 will be "no lines", "1 line" or "X lines", localized separately.']},"Difference {0} of {1}: original {2}, {3}, modified {4}, {5}",l+1,this._diffs.length,f,R,m,O)),E.appendChild(S),E.setAttribute("role","listitem"),c.appendChild(E);var M=m;for(_=0,v=u.length;_<v;_++){D=u[_];t._renderSection(c,D,M,this._width,e,r,s,i,a,d),0!==D.modifiedLineStart&&(M=D.modifiedLineEnd)}n["t"](this._content.domNode),this._content.domNode.appendChild(c),this.scrollbar.scanDomNode()}},t._renderSection=function(e,t,i,n,r,a,s,d,l,u){var h=t.getType(),c="diff-review-row",f="",g="diff-review-spacer";switch(h){case 1:c="diff-review-row line-insert",f=" char-insert",g="diff-review-spacer insert-sign";break;case 2:c="diff-review-row line-delete",f=" char-delete",g="diff-review-spacer delete-sign";break}for(var m=t.originalLineStart,p=t.originalLineEnd,_=t.modifiedLineStart,v=t.modifiedLineEnd,D=Math.max(v-_,p-m),y=r.get(107),w=y.glyphMarginWidth+y.lineNumbersWidth,b=d.get(107),C=10+b.glyphMarginWidth+b.lineNumbersWidth,E=0;E<=D;E++){var S=0===m?0:m+E,L=0===_?0:_+E,N=document.createElement("div");N.style.minWidth=n+"px",N.className=c,N.setAttribute("role","listitem"),0!==L&&(i=L),N.setAttribute("data-line",String(i));var I=document.createElement("div");I.className="diff-review-cell",N.appendChild(I);var R=document.createElement("span");R.style.width=w+"px",R.style.minWidth=w+"px",R.className="diff-review-line-number"+f,0!==S?R.appendChild(document.createTextNode(String(S))):R.innerHTML="&#160;",I.appendChild(R);var O=document.createElement("span");O.style.width=C+"px",O.style.minWidth=C+"px",O.style.paddingRight="10px",O.className="diff-review-line-number"+f,0!==L?O.appendChild(document.createTextNode(String(L))):O.innerHTML="&#160;",I.appendChild(O);var M=document.createElement("span");M.className=g,M.innerHTML="&#160;&#160;",I.appendChild(M);var T=void 0;0!==L?(I.insertAdjacentHTML("beforeend",this._renderLine(l,d,u.tabSize,L)),T=l.getLineContent(L)):(I.insertAdjacentHTML("beforeend",this._renderLine(a,r,s.tabSize,S)),T=a.getLineContent(S)),0===T.length&&(T=o["a"]("blankLine","blank"));var x="";switch(h){case 0:x=o["a"]("equalLine","original {0}, modified {1}: {2}",S,L,T);break;case 1:x=o["a"]("insertLine","+ modified {0}: {1}",L,T);break;case 2:x=o["a"]("deleteLine","- original {0}: {1}",S,T);break}N.setAttribute("aria-label",x),e.appendChild(N)}},t._renderLine=function(e,t,i,o){var n=e.getLineContent(o),r=t.get(34),a=16793600,s=new Uint32Array(2);s[0]=n.length,s[1]=a;var d=new y["a"](s,n),l=E["d"].isBasicASCII(n,e.mightContainNonBasicASCII()),u=E["d"].containsRTL(n,l,e.mightContainRTL()),h=Object(C["e"])(new C["c"](r.isMonospace&&!t.get(23),r.canUseHalfwidthRightwardsArrow,n,!1,l,u,0,d,[],i,0,r.spaceWidth,r.middotWidth,t.get(88),t.get(74),t.get(69),t.get(35)!==D["d"].OFF,null));return h.html},t}(l["a"]);Object(N["e"])((function(e,t){var i=e.getColor(b["k"]);i&&t.addRule(".monaco-diff-editor .diff-review-line-number { color: "+i+"; }");var o=e.getColor(L["Vb"]);o&&t.addRule(".monaco-diff-editor .diff-review-shadow { box-shadow: "+o+" 0 -6px 6px -6px inset; }")}));var x=function(e){function t(){return e.call(this,{id:"editor.action.diffReview.next",label:o["a"]("editor.action.diffReview.next","Go to Next Difference"),alias:"Go to Next Difference",precondition:S["a"].has("isInDiffEditor"),kbOpts:{kbExpr:null,primary:65,weight:100}})||this}return I(t,e),t.prototype.run=function(e,t){var i=V(e);i&&i.diffReviewNext()},t}(v["b"]),P=function(e){function t(){return e.call(this,{id:"editor.action.diffReview.prev",label:o["a"]("editor.action.diffReview.prev","Go to Previous Difference"),alias:"Go to Previous Difference",precondition:S["a"].has("isInDiffEditor"),kbOpts:{kbExpr:null,primary:1089,weight:100}})||this}return I(t,e),t.prototype.run=function(e,t){var i=V(e);i&&i.diffReviewPrev()},t}(v["b"]);function V(e){for(var t=e.get(f["a"]),i=t.listDiffEditors(),o=0,n=i.length;o<n;o++){var r=i[o];if(r.hasWidgetFocus())return r}return null}Object(v["f"])(x),Object(v["f"])(P);var F=i("6a89"),W=i("7ab3"),A=i("8ae8"),H=i("b57f"),k=i("a40b"),j=i("32f2"),z=i("7416"),U=i("0a0f"),Z=i("f07b"),B=i("b0cd"),q=i("533b"),K=function(){var e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])},e(t,i)};return function(t,i){function o(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(o.prototype=i.prototype,new o)}}(),G=function(e,t,i,o){function n(e){return e instanceof i?e:new i((function(t){t(e)}))}return new(i||(i=Promise))((function(i,r){function a(e){try{d(o.next(e))}catch(t){r(t)}}function s(e){try{d(o["throw"](e))}catch(t){r(t)}}function d(e){e.done?i(e.value):n(e.value).then(a,s)}d((o=o.apply(e,t||[])).next())}))},X=function(e,t){var i,o,n,r,a={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},"function"===typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(e){return function(t){return d([e,t])}}function d(r){if(i)throw new TypeError("Generator is already executing.");while(a)try{if(i=1,o&&(n=2&r[0]?o["return"]:r[0]?o["throw"]||((n=o["return"])&&n.call(o),0):o.next)&&!(n=n.call(o,r[1])).done)return n;switch(o=0,n&&(r=[2&r[0],n.value]),r[0]){case 0:case 1:n=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,o=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(n=a.trys,!(n=n.length>0&&n[n.length-1])&&(6===r[0]||2===r[0])){a=0;continue}if(3===r[0]&&(!n||r[1]>n[0]&&r[1]<n[3])){a.label=r[1];break}if(6===r[0]&&a.label<n[1]){a.label=n[1],n=r;break}if(n&&a.label<n[2]){a.label=n[2],a.ops.push(r);break}n[2]&&a.ops.pop(),a.trys.pop();continue}r=t.call(e,a)}catch(s){r=[6,s],o=0}finally{i=n=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}},J=function(e){function t(t,i,r,a,s,d){var l=e.call(this)||this;l._viewZoneId=t,l._marginDomNode=i,l.editor=r,l.diff=a,l._contextMenuService=s,l._clipboardService=d,l._visibility=!1,l._marginDomNode.style.zIndex="10",l._diffActions=document.createElement("div"),l._diffActions.className="codicon codicon-lightbulb lightbulb-glyph",l._diffActions.style.position="absolute";var u=r.getOption(49),h=r.getModel().getEOL();l._diffActions.style.right="0px",l._diffActions.style.visibility="hidden",l._diffActions.style.height=u+"px",l._diffActions.style.lineHeight=u+"px",l._marginDomNode.appendChild(l._diffActions);var c=[];c.push(new _["a"]("diff.clipboard.copyDeletedContent",a.originalEndLineNumber>a.modifiedStartLineNumber?o["a"]("diff.clipboard.copyDeletedLinesContent.label","Copy deleted lines"):o["a"]("diff.clipboard.copyDeletedLinesContent.single.label","Copy deleted line"),void 0,!0,(function(){return G(l,void 0,void 0,(function(){return X(this,(function(e){switch(e.label){case 0:return[4,this._clipboardService.writeText(a.originalContent.join(h)+h)];case 1:return e.sent(),[2]}}))}))})));var f=0,g=void 0;a.originalEndLineNumber>a.modifiedStartLineNumber&&(g=new _["a"]("diff.clipboard.copyDeletedLineContent",o["a"]("diff.clipboard.copyDeletedLineContent.label","Copy deleted line ({0})",a.originalStartLineNumber),void 0,!0,(function(){return G(l,void 0,void 0,(function(){return X(this,(function(e){switch(e.label){case 0:return[4,this._clipboardService.writeText(a.originalContent[f])];case 1:return e.sent(),[2]}}))}))})),c.push(g));var m=r.getOption(68);m||c.push(new _["a"]("diff.inline.revertChange",o["a"]("diff.inline.revertChange.label","Revert this change"),void 0,!0,(function(){return G(l,void 0,void 0,(function(){var e;return X(this,(function(t){return 0===a.modifiedEndLineNumber?(e=r.getModel().getLineMaxColumn(a.modifiedStartLineNumber),r.executeEdits("diffEditor",[{range:new F["a"](a.modifiedStartLineNumber,e,a.modifiedStartLineNumber,e),text:h+a.originalContent.join(h)}])):(e=r.getModel().getLineMaxColumn(a.modifiedEndLineNumber),r.executeEdits("diffEditor",[{range:new F["a"](a.modifiedStartLineNumber,1,a.modifiedEndLineNumber,e),text:a.originalContent.join(h)}])),[2]}))}))})));var p=function(e,t){l._contextMenuService.showContextMenu({getAnchor:function(){return{x:e,y:t}},getActions:function(){return g&&(g.label=o["a"]("diff.clipboard.copyDeletedLineContent.label","Copy deleted line ({0})",a.originalStartLineNumber+f)),c},autoSelectFirstItem:!0})};return l._register(n["o"](l._diffActions,"mousedown",(function(e){var t=n["C"](l._diffActions),i=t.top,o=t.height,r=Math.floor(u/3);e.preventDefault(),p(e.posx,i+o+r)}))),l._register(r.onMouseMove((function(e){if(8===e.target.type||5===e.target.type){var t=e.target.detail.viewZoneId;t===l._viewZoneId?(l.visibility=!0,f=l._updateLightBulbPosition(l._marginDomNode,e.event.browserEvent.y,u)):l.visibility=!1}else l.visibility=!1}))),l._register(r.onMouseDown((function(e){if(e.event.rightButton&&(8===e.target.type||5===e.target.type)){var t=e.target.detail.viewZoneId;t===l._viewZoneId&&(e.event.preventDefault(),f=l._updateLightBulbPosition(l._marginDomNode,e.event.browserEvent.y,u),p(e.event.posx,e.event.posy+u))}}))),l}return K(t,e),Object.defineProperty(t.prototype,"visibility",{get:function(){return this._visibility},set:function(e){this._visibility!==e&&(this._visibility=e,this._diffActions.style.visibility=e?"visible":"hidden")},enumerable:!0,configurable:!0}),t.prototype._updateLightBulbPosition=function(e,t,i){var o=n["C"](e).top,r=t-o,a=Math.floor(r/i),s=a*i;return this._diffActions.style.top=s+"px",a},t}(l["a"]),Y=i("fdcc"),Q=i("b539"),$=i("a37f"),ee=function(){var e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])},e(t,i)};return function(t,i){function o(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(o.prototype=i.prototype,new o)}}(),te=function(e,t,i,o){var n,r=arguments.length,a=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,i):o;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)a=Reflect.decorate(e,t,i,o);else for(var s=e.length-1;s>=0;s--)(n=e[s])&&(a=(r<3?n(a):r>3?n(t,i,a):n(t,i))||a);return r>3&&a&&Object.defineProperty(t,i,a),a},ie=function(e,t){return function(i,o){t(i,o,e)}},oe=function(){function e(e,t){this._contextMenuService=e,this._clipboardService=t,this._zones=[],this.inlineDiffMargins=[],this._zonesMap={},this._decorations=[]}return e.prototype.getForeignViewZones=function(e){var t=this;return e.filter((function(e){return!t._zonesMap[String(e.id)]}))},e.prototype.clean=function(e){var t=this;this._zones.length>0&&e.changeViewZones((function(e){for(var i=0,o=t._zones.length;i<o;i++)e.removeZone(t._zones[i])})),this._zones=[],this._zonesMap={},this._decorations=e.deltaDecorations(this._decorations,[])},e.prototype.apply=function(e,t,i,o){var n=this,r=o?c["c"].capture(e):null;e.changeViewZones((function(t){for(var o=0,r=n._zones.length;o<r;o++)t.removeZone(n._zones[o]);o=0;for(var a=n.inlineDiffMargins.length;o<a;o++)n.inlineDiffMargins[o].dispose();n._zones=[],n._zonesMap={},n.inlineDiffMargins=[];o=0;for(var s=i.zones.length;o<s;o++){var d=i.zones[o];d.suppressMouseDown=!0;var l=t.addZone(d);n._zones.push(l),n._zonesMap[String(l)]=!0,i.zones[o].diff&&d.marginDomNode&&n._clipboardService&&(d.suppressMouseDown=!1,n.inlineDiffMargins.push(new J(l,d.marginDomNode,e,i.zones[o].diff,n._contextMenuService,n._clipboardService)))}})),r&&r.restore(e),this._decorations=e.deltaDecorations(this._decorations,i.decorations),t&&t.setZones(i.overviewZones)},e}(),ne=0,re=function(e){function t(i,o,a,l,u,h,c,f,g,m,p){var _=e.call(this)||this;_._editorProgressService=p,_._onDidDispose=_._register(new d["a"]),_.onDidDispose=_._onDidDispose.event,_._onDidUpdateDiff=_._register(new d["a"]),_.onDidUpdateDiff=_._onDidUpdateDiff.event,_._lastOriginalWarning=null,_._lastModifiedWarning=null,_._editorWorkerService=l,_._codeEditorService=c,_._contextKeyService=_._register(u.createScoped(i)),_._contextKeyService.createKey("isInDiffEditor",!0),_._themeService=f,_._notificationService=g,_.id=++ne,_._state=0,_._updatingDiffProgress=null,_._domElement=i,o=o||{},_._renderSideBySide=!0,"undefined"!==typeof o.renderSideBySide&&(_._renderSideBySide=o.renderSideBySide),_._maxComputationTime=5e3,"undefined"!==typeof o.maxComputationTime&&(_._maxComputationTime=o.maxComputationTime),_._ignoreTrimWhitespace=!0,"undefined"!==typeof o.ignoreTrimWhitespace&&(_._ignoreTrimWhitespace=o.ignoreTrimWhitespace),_._renderIndicators=!0,"undefined"!==typeof o.renderIndicators&&(_._renderIndicators=o.renderIndicators),_._originalIsEditable=!1,"undefined"!==typeof o.originalEditable&&(_._originalIsEditable=Boolean(o.originalEditable)),_._updateDecorationsRunner=_._register(new s["d"]((function(){return _._updateDecorations()}),0)),_._containerDomElement=document.createElement("div"),_._containerDomElement.className=t._getClassName(_._themeService.getTheme(),_._renderSideBySide),_._containerDomElement.style.position="relative",_._containerDomElement.style.height="100%",_._domElement.appendChild(_._containerDomElement),_._overviewViewportDomElement=Object(r["b"])(document.createElement("div")),_._overviewViewportDomElement.setClassName("diffViewport"),_._overviewViewportDomElement.setPosition("absolute"),_._overviewDomElement=document.createElement("div"),_._overviewDomElement.className="diffOverview",_._overviewDomElement.style.position="absolute",_._overviewDomElement.appendChild(_._overviewViewportDomElement.domNode),_._register(n["o"](_._overviewDomElement,"mousedown",(function(e){_.modifiedEditor.delegateVerticalScrollbarMouseDown(e)}))),_._containerDomElement.appendChild(_._overviewDomElement),_._originalDomNode=document.createElement("div"),_._originalDomNode.className="editor original",_._originalDomNode.style.position="absolute",_._originalDomNode.style.height="100%",_._containerDomElement.appendChild(_._originalDomNode),_._modifiedDomNode=document.createElement("div"),_._modifiedDomNode.className="editor modified",_._modifiedDomNode.style.position="absolute",_._modifiedDomNode.style.height="100%",_._containerDomElement.appendChild(_._modifiedDomNode),_._beginUpdateDecorationsTimeout=-1,_._currentlyChangingViewZones=!1,_._diffComputationToken=0,_._originalEditorState=new oe(m,a),_._modifiedEditorState=new oe(m,a),_._isVisible=!0,_._isHandlingScrollEvent=!1,_._elementSizeObserver=_._register(new $["a"](_._containerDomElement,void 0,(function(){return _._onDidContainerSizeChanged()}))),o.automaticLayout&&_._elementSizeObserver.startObserving(),_._diffComputationResult=null;var D=_._contextKeyService.createScoped();D.createKey("isInDiffLeftEditor",!0);var y=new Z["a"];y.set(S["c"],D);var w=h.createChild(y),b=_._contextKeyService.createScoped();b.createKey("isInDiffRightEditor",!0);var C=new Z["a"];C.set(S["c"],b);var E=h.createChild(C);_.originalEditor=_._createLeftHandSideEditor(o,w),_.modifiedEditor=_._createRightHandSideEditor(o,E),_._originalOverviewRuler=null,_._modifiedOverviewRuler=null,_._reviewPane=new T(_),_._containerDomElement.appendChild(_._reviewPane.domNode.domNode),_._containerDomElement.appendChild(_._reviewPane.shadow.domNode),_._containerDomElement.appendChild(_._reviewPane.actionBarContainer.domNode),_._enableSplitViewResizing=!0,"undefined"!==typeof o.enableSplitViewResizing&&(_._enableSplitViewResizing=o.enableSplitViewResizing),_._renderSideBySide?_._setStrategy(new he(_._createDataSource(),_._enableSplitViewResizing)):_._setStrategy(new fe(_._createDataSource(),_._enableSplitViewResizing)),_._register(f.onThemeChange((function(e){_._strategy&&_._strategy.applyColors(e)&&_._updateDecorationsRunner.schedule(),_._containerDomElement.className=t._getClassName(_._themeService.getTheme(),_._renderSideBySide)})));for(var L=v["d"].getDiffEditorContributions(),N=0,I=L;N<I.length;N++){var R=I[N];try{_._register(h.createInstance(R.ctor,_))}catch(O){Object(Y["e"])(O)}}return _._codeEditorService.addDiffEditor(_),_}return ee(t,e),t.prototype._setState=function(e){this._state!==e&&(this._state=e,this._updatingDiffProgress&&(this._updatingDiffProgress.done(),this._updatingDiffProgress=null),1===this._state&&(this._updatingDiffProgress=this._editorProgressService.show(!0,1e3)))},t.prototype.hasWidgetFocus=function(){return n["K"](document.activeElement,this._domElement)},t.prototype.diffReviewNext=function(){this._reviewPane.next()},t.prototype.diffReviewPrev=function(){this._reviewPane.prev()},t._getClassName=function(e,t){var i="monaco-diff-editor monaco-editor-background ";return t&&(i+="side-by-side "),i+=Object(N["d"])(e.type),i},t.prototype._recreateOverviewRulers=function(){this._originalOverviewRuler&&(this._overviewDomElement.removeChild(this._originalOverviewRuler.getDomNode()),this._originalOverviewRuler.dispose()),this.originalEditor.hasModel()&&(this._originalOverviewRuler=this.originalEditor.createOverviewRuler("original diffOverviewRuler"),this._overviewDomElement.appendChild(this._originalOverviewRuler.getDomNode())),this._modifiedOverviewRuler&&(this._overviewDomElement.removeChild(this._modifiedOverviewRuler.getDomNode()),this._modifiedOverviewRuler.dispose()),this.modifiedEditor.hasModel()&&(this._modifiedOverviewRuler=this.modifiedEditor.createOverviewRuler("modified diffOverviewRuler"),this._overviewDomElement.appendChild(this._modifiedOverviewRuler.getDomNode())),this._layoutOverviewRulers()},t.prototype._createLeftHandSideEditor=function(e,t){var i=this,o=this._createInnerEditor(t,this._originalDomNode,this._adjustOptionsForLeftHandSide(e,this._originalIsEditable));return this._register(o.onDidScrollChange((function(e){i._isHandlingScrollEvent||(e.scrollTopChanged||e.scrollLeftChanged||e.scrollHeightChanged)&&(i._isHandlingScrollEvent=!0,i.modifiedEditor.setScrollPosition({scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}),i._isHandlingScrollEvent=!1,i._layoutOverviewViewport())}))),this._register(o.onDidChangeViewZones((function(){i._onViewZonesChanged()}))),this._register(o.onDidChangeModelContent((function(){i._isVisible&&i._beginUpdateDecorationsSoon()}))),o},t.prototype._createRightHandSideEditor=function(e,t){var i=this,o=this._createInnerEditor(t,this._modifiedDomNode,this._adjustOptionsForRightHandSide(e));return this._register(o.onDidScrollChange((function(e){i._isHandlingScrollEvent||(e.scrollTopChanged||e.scrollLeftChanged||e.scrollHeightChanged)&&(i._isHandlingScrollEvent=!0,i.originalEditor.setScrollPosition({scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}),i._isHandlingScrollEvent=!1,i._layoutOverviewViewport())}))),this._register(o.onDidChangeViewZones((function(){i._onViewZonesChanged()}))),this._register(o.onDidChangeConfiguration((function(e){e.hasChanged(34)&&o.getModel()&&i._onViewZonesChanged()}))),this._register(o.onDidChangeModelContent((function(){i._isVisible&&i._beginUpdateDecorationsSoon()}))),this._register(o.onDidChangeModelOptions((function(e){e.tabSize&&i._updateDecorationsRunner.schedule()}))),o},t.prototype._createInnerEditor=function(e,t,i){return e.createInstance(g["a"],t,i,{})},t.prototype.dispose=function(){this._codeEditorService.removeDiffEditor(this),-1!==this._beginUpdateDecorationsTimeout&&(window.clearTimeout(this._beginUpdateDecorationsTimeout),this._beginUpdateDecorationsTimeout=-1),this._cleanViewZonesAndDecorations(),this._originalOverviewRuler&&(this._overviewDomElement.removeChild(this._originalOverviewRuler.getDomNode()),this._originalOverviewRuler.dispose()),this._modifiedOverviewRuler&&(this._overviewDomElement.removeChild(this._modifiedOverviewRuler.getDomNode()),this._modifiedOverviewRuler.dispose()),this._overviewDomElement.removeChild(this._overviewViewportDomElement.domNode),this._containerDomElement.removeChild(this._overviewDomElement),this._containerDomElement.removeChild(this._originalDomNode),this.originalEditor.dispose(),this._containerDomElement.removeChild(this._modifiedDomNode),this.modifiedEditor.dispose(),this._strategy.dispose(),this._containerDomElement.removeChild(this._reviewPane.domNode.domNode),this._containerDomElement.removeChild(this._reviewPane.shadow.domNode),this._containerDomElement.removeChild(this._reviewPane.actionBarContainer.domNode),this._reviewPane.dispose(),this._domElement.removeChild(this._containerDomElement),this._onDidDispose.fire(),e.prototype.dispose.call(this)},t.prototype.getId=function(){return this.getEditorType()+":"+this.id},t.prototype.getEditorType=function(){return A["a"].IDiffEditor},t.prototype.getLineChanges=function(){return this._diffComputationResult?this._diffComputationResult.changes:null},t.prototype.getOriginalEditor=function(){return this.originalEditor},t.prototype.getModifiedEditor=function(){return this.modifiedEditor},t.prototype.updateOptions=function(e){var i=!1;"undefined"!==typeof e.renderSideBySide&&this._renderSideBySide!==e.renderSideBySide&&(this._renderSideBySide=e.renderSideBySide,i=!0),"undefined"!==typeof e.maxComputationTime&&(this._maxComputationTime=e.maxComputationTime,this._isVisible&&this._beginUpdateDecorationsSoon());var o=!1;"undefined"!==typeof e.ignoreTrimWhitespace&&this._ignoreTrimWhitespace!==e.ignoreTrimWhitespace&&(this._ignoreTrimWhitespace=e.ignoreTrimWhitespace,o=!0),"undefined"!==typeof e.renderIndicators&&this._renderIndicators!==e.renderIndicators&&(this._renderIndicators=e.renderIndicators,o=!0),o&&this._beginUpdateDecorations(),"undefined"!==typeof e.originalEditable&&(this._originalIsEditable=Boolean(e.originalEditable)),this.modifiedEditor.updateOptions(this._adjustOptionsForRightHandSide(e)),this.originalEditor.updateOptions(this._adjustOptionsForLeftHandSide(e,this._originalIsEditable)),"undefined"!==typeof e.enableSplitViewResizing&&(this._enableSplitViewResizing=e.enableSplitViewResizing),this._strategy.setEnableSplitViewResizing(this._enableSplitViewResizing),i&&(this._renderSideBySide?this._setStrategy(new he(this._createDataSource(),this._enableSplitViewResizing)):this._setStrategy(new fe(this._createDataSource(),this._enableSplitViewResizing)),this._containerDomElement.className=t._getClassName(this._themeService.getTheme(),this._renderSideBySide))},t.prototype.getModel=function(){return{original:this.originalEditor.getModel(),modified:this.modifiedEditor.getModel()}},t.prototype.setModel=function(e){if(e&&(!e.original||!e.modified))throw new Error(e.original?"DiffEditorWidget.setModel: Modified model is null":"DiffEditorWidget.setModel: Original model is null");this._cleanViewZonesAndDecorations(),this.originalEditor.setModel(e?e.original:null),this.modifiedEditor.setModel(e?e.modified:null),this._updateDecorationsRunner.cancel(),e&&(this.originalEditor.setScrollTop(0),this.modifiedEditor.setScrollTop(0)),this._diffComputationResult=null,this._diffComputationToken++,this._setState(0),e&&(this._recreateOverviewRulers(),this._beginUpdateDecorations()),this._layoutOverviewViewport()},t.prototype.getDomNode=function(){return this._domElement},t.prototype.getVisibleColumnFromPosition=function(e){return this.modifiedEditor.getVisibleColumnFromPosition(e)},t.prototype.getPosition=function(){return this.modifiedEditor.getPosition()},t.prototype.setPosition=function(e){this.modifiedEditor.setPosition(e)},t.prototype.revealLine=function(e,t){void 0===t&&(t=0),this.modifiedEditor.revealLine(e,t)},t.prototype.revealLineInCenter=function(e,t){void 0===t&&(t=0),this.modifiedEditor.revealLineInCenter(e,t)},t.prototype.revealLineInCenterIfOutsideViewport=function(e,t){void 0===t&&(t=0),this.modifiedEditor.revealLineInCenterIfOutsideViewport(e,t)},t.prototype.revealPosition=function(e,t){void 0===t&&(t=0),this.modifiedEditor.revealPosition(e,t)},t.prototype.revealPositionInCenter=function(e,t){void 0===t&&(t=0),this.modifiedEditor.revealPositionInCenter(e,t)},t.prototype.revealPositionInCenterIfOutsideViewport=function(e,t){void 0===t&&(t=0),this.modifiedEditor.revealPositionInCenterIfOutsideViewport(e,t)},t.prototype.getSelection=function(){return this.modifiedEditor.getSelection()},t.prototype.getSelections=function(){return this.modifiedEditor.getSelections()},t.prototype.setSelection=function(e){this.modifiedEditor.setSelection(e)},t.prototype.setSelections=function(e){this.modifiedEditor.setSelections(e)},t.prototype.revealLines=function(e,t,i){void 0===i&&(i=0),this.modifiedEditor.revealLines(e,t,i)},t.prototype.revealLinesInCenter=function(e,t,i){void 0===i&&(i=0),this.modifiedEditor.revealLinesInCenter(e,t,i)},t.prototype.revealLinesInCenterIfOutsideViewport=function(e,t,i){void 0===i&&(i=0),this.modifiedEditor.revealLinesInCenterIfOutsideViewport(e,t,i)},t.prototype.revealRange=function(e,t,i,o){void 0===t&&(t=0),void 0===i&&(i=!1),void 0===o&&(o=!0),this.modifiedEditor.revealRange(e,t,i,o)},t.prototype.revealRangeInCenter=function(e,t){void 0===t&&(t=0),this.modifiedEditor.revealRangeInCenter(e,t)},t.prototype.revealRangeInCenterIfOutsideViewport=function(e,t){void 0===t&&(t=0),this.modifiedEditor.revealRangeInCenterIfOutsideViewport(e,t)},t.prototype.revealRangeAtTop=function(e,t){void 0===t&&(t=0),this.modifiedEditor.revealRangeAtTop(e,t)},t.prototype.getSupportedActions=function(){return this.modifiedEditor.getSupportedActions()},t.prototype.saveViewState=function(){var e=this.originalEditor.saveViewState(),t=this.modifiedEditor.saveViewState();return{original:e,modified:t}},t.prototype.restoreViewState=function(e){if(e.original&&e.modified){var t=e;this.originalEditor.restoreViewState(t.original),this.modifiedEditor.restoreViewState(t.modified)}},t.prototype.layout=function(e){this._elementSizeObserver.observe(e)},t.prototype.focus=function(){this.modifiedEditor.focus()},t.prototype.hasTextFocus=function(){return this.originalEditor.hasTextFocus()||this.modifiedEditor.hasTextFocus()},t.prototype.trigger=function(e,t,i){this.modifiedEditor.trigger(e,t,i)},t.prototype.changeDecorations=function(e){return this.modifiedEditor.changeDecorations(e)},t.prototype._onDidContainerSizeChanged=function(){this._doLayout()},t.prototype._getReviewHeight=function(){return this._reviewPane.isVisible()?this._elementSizeObserver.getHeight():0},t.prototype._layoutOverviewRulers=function(){if(this._originalOverviewRuler&&this._modifiedOverviewRuler){var e=this._elementSizeObserver.getHeight(),i=this._getReviewHeight(),o=t.ENTIRE_DIFF_OVERVIEW_WIDTH-2*t.ONE_OVERVIEW_WIDTH,n=this.modifiedEditor.getLayoutInfo();n&&(this._originalOverviewRuler.setLayout({top:0,width:t.ONE_OVERVIEW_WIDTH,right:o+t.ONE_OVERVIEW_WIDTH,height:e-i}),this._modifiedOverviewRuler.setLayout({top:0,right:0,width:t.ONE_OVERVIEW_WIDTH,height:e-i}))}},t.prototype._onViewZonesChanged=function(){this._currentlyChangingViewZones||this._updateDecorationsRunner.schedule()},t.prototype._beginUpdateDecorationsSoon=function(){var e=this;-1!==this._beginUpdateDecorationsTimeout&&(window.clearTimeout(this._beginUpdateDecorationsTimeout),this._beginUpdateDecorationsTimeout=-1),this._beginUpdateDecorationsTimeout=window.setTimeout((function(){return e._beginUpdateDecorations()}),t.UPDATE_DIFF_DECORATIONS_DELAY)},t._equals=function(e,t){return!e&&!t||!(!e||!t)&&e.toString()===t.toString()},t.prototype._beginUpdateDecorations=function(){var e=this;this._beginUpdateDecorationsTimeout=-1;var i=this.originalEditor.getModel(),n=this.modifiedEditor.getModel();if(i&&n){this._diffComputationToken++;var r=this._diffComputationToken;this._setState(1),this._editorWorkerService.canComputeDiff(i.uri,n.uri)?this._editorWorkerService.computeDiff(i.uri,n.uri,this._ignoreTrimWhitespace,this._maxComputationTime).then((function(t){r===e._diffComputationToken&&i===e.originalEditor.getModel()&&n===e.modifiedEditor.getModel()&&(e._setState(2),e._diffComputationResult=t,e._updateDecorationsRunner.schedule(),e._onDidUpdateDiff.fire())}),(function(t){r===e._diffComputationToken&&i===e.originalEditor.getModel()&&n===e.modifiedEditor.getModel()&&(e._setState(2),e._diffComputationResult=null,e._updateDecorationsRunner.schedule())})):t._equals(i.uri,this._lastOriginalWarning)&&t._equals(n.uri,this._lastModifiedWarning)||(this._lastOriginalWarning=i.uri,this._lastModifiedWarning=n.uri,this._notificationService.warn(o["a"]("diff.tooLarge","Cannot compare files because one file is too large.")))}},t.prototype._cleanViewZonesAndDecorations=function(){this._originalEditorState.clean(this.originalEditor),this._modifiedEditorState.clean(this.modifiedEditor)},t.prototype._updateDecorations=function(){if(this.originalEditor.getModel()&&this.modifiedEditor.getModel()&&this._originalOverviewRuler&&this._modifiedOverviewRuler){var e=this._diffComputationResult?this._diffComputationResult.changes:[],t=this._originalEditorState.getForeignViewZones(this.originalEditor.getWhitespaces()),i=this._modifiedEditorState.getForeignViewZones(this.modifiedEditor.getWhitespaces()),o=this._strategy.getEditorsDiffDecorations(e,this._ignoreTrimWhitespace,this._renderIndicators,t,i,this.originalEditor,this.modifiedEditor);try{this._currentlyChangingViewZones=!0,this._originalEditorState.apply(this.originalEditor,this._originalOverviewRuler,o.original,!1),this._modifiedEditorState.apply(this.modifiedEditor,this._modifiedOverviewRuler,o.modified,!0)}finally{this._currentlyChangingViewZones=!1}}},t.prototype._adjustOptionsForSubEditor=function(e){var t=u["c"](e||{});return t.inDiffEditor=!0,t.wordWrap="off",t.wordWrapMinified=!1,t.automaticLayout=!1,t.scrollbar=t.scrollbar||{},t.scrollbar.vertical="visible",t.folding=!1,t.codeLens=!1,t.fixedOverflowWidgets=!0,t.minimap||(t.minimap={}),t.minimap.enabled=!1,t},t.prototype._adjustOptionsForLeftHandSide=function(e,t){var i=this._adjustOptionsForSubEditor(e);return i.readOnly=!t,i.extraEditorClassName="original-in-monaco-diff-editor",i},t.prototype._adjustOptionsForRightHandSide=function(e){var i=this._adjustOptionsForSubEditor(e);return i.revealHorizontalRightPadding=D["e"].revealHorizontalRightPadding.defaultValue+t.ENTIRE_DIFF_OVERVIEW_WIDTH,i.scrollbar.verticalHasArrows=!1,i.extraEditorClassName="modified-in-monaco-diff-editor",i},t.prototype.doLayout=function(){this._elementSizeObserver.observe(),this._doLayout()},t.prototype._doLayout=function(){var e=this._elementSizeObserver.getWidth(),i=this._elementSizeObserver.getHeight(),o=this._getReviewHeight(),n=this._strategy.layout();this._originalDomNode.style.width=n+"px",this._originalDomNode.style.left="0px",this._modifiedDomNode.style.width=e-n+"px",this._modifiedDomNode.style.left=n+"px",this._overviewDomElement.style.top="0px",this._overviewDomElement.style.height=i-o+"px",this._overviewDomElement.style.width=t.ENTIRE_DIFF_OVERVIEW_WIDTH+"px",this._overviewDomElement.style.left=e-t.ENTIRE_DIFF_OVERVIEW_WIDTH+"px",this._overviewViewportDomElement.setWidth(t.ENTIRE_DIFF_OVERVIEW_WIDTH),this._overviewViewportDomElement.setHeight(30),this.originalEditor.layout({width:n,height:i-o}),this.modifiedEditor.layout({width:e-n-t.ENTIRE_DIFF_OVERVIEW_WIDTH,height:i-o}),(this._originalOverviewRuler||this._modifiedOverviewRuler)&&this._layoutOverviewRulers(),this._reviewPane.layout(i-o,e,o),this._layoutOverviewViewport()},t.prototype._layoutOverviewViewport=function(){var e=this._computeOverviewViewport();e?(this._overviewViewportDomElement.setTop(e.top),this._overviewViewportDomElement.setHeight(e.height)):(this._overviewViewportDomElement.setTop(0),this._overviewViewportDomElement.setHeight(0))},t.prototype._computeOverviewViewport=function(){var e=this.modifiedEditor.getLayoutInfo();if(!e)return null;var t=this.modifiedEditor.getScrollTop(),i=this.modifiedEditor.getScrollHeight(),o=Math.max(0,e.height),n=Math.max(0,o-0),r=i>0?n/i:0,a=Math.max(0,Math.floor(e.height*r)),s=Math.floor(t*r);return{height:a,top:s}},t.prototype._createDataSource=function(){var e=this;return{getWidth:function(){return e._elementSizeObserver.getWidth()},getHeight:function(){return e._elementSizeObserver.getHeight()-e._getReviewHeight()},getContainerDomNode:function(){return e._containerDomElement},relayoutEditors:function(){e._doLayout()},getOriginalEditor:function(){return e.originalEditor},getModifiedEditor:function(){return e.modifiedEditor}}},t.prototype._setStrategy=function(e){this._strategy&&this._strategy.dispose(),this._strategy=e,e.applyColors(this._themeService.getTheme()),this._diffComputationResult&&this._updateDecorations(),this._doLayout()},t.prototype._getLineChangeAtOrBeforeLineNumber=function(e,t){var i=this._diffComputationResult?this._diffComputationResult.changes:[];if(0===i.length||e<t(i[0]))return null;var o=0,n=i.length-1;while(o<n){var r=Math.floor((o+n)/2),a=t(i[r]),s=r+1<=n?t(i[r+1]):1073741824;e<a?n=r-1:e>=s?o=r+1:(o=r,n=r)}return i[o]},t.prototype._getEquivalentLineForOriginalLineNumber=function(e){var t=this._getLineChangeAtOrBeforeLineNumber(e,(function(e){return e.originalStartLineNumber}));if(!t)return e;var i=t.originalStartLineNumber+(t.originalEndLineNumber>0?-1:0),o=t.modifiedStartLineNumber+(t.modifiedEndLineNumber>0?-1:0),n=t.originalEndLineNumber>0?t.originalEndLineNumber-t.originalStartLineNumber+1:0,r=t.modifiedEndLineNumber>0?t.modifiedEndLineNumber-t.modifiedStartLineNumber+1:0,a=e-i;return a<=n?o+Math.min(a,r):o+r-n+a},t.prototype._getEquivalentLineForModifiedLineNumber=function(e){var t=this._getLineChangeAtOrBeforeLineNumber(e,(function(e){return e.modifiedStartLineNumber}));if(!t)return e;var i=t.originalStartLineNumber+(t.originalEndLineNumber>0?-1:0),o=t.modifiedStartLineNumber+(t.modifiedEndLineNumber>0?-1:0),n=t.originalEndLineNumber>0?t.originalEndLineNumber-t.originalStartLineNumber+1:0,r=t.modifiedEndLineNumber>0?t.modifiedEndLineNumber-t.modifiedStartLineNumber+1:0,a=e-o;return a<=r?i+Math.min(a,n):i+n-r+a},t.prototype.getDiffLineInformationForOriginal=function(e){return this._diffComputationResult?{equivalentLineNumber:this._getEquivalentLineForOriginalLineNumber(e)}:null},t.prototype.getDiffLineInformationForModified=function(e){return this._diffComputationResult?{equivalentLineNumber:this._getEquivalentLineForModifiedLineNumber(e)}:null},t.ONE_OVERVIEW_WIDTH=15,t.ENTIRE_DIFF_OVERVIEW_WIDTH=30,t.UPDATE_DIFF_DECORATIONS_DELAY=200,t=te([ie(3,k["a"]),ie(4,S["c"]),ie(5,U["a"]),ie(6,f["a"]),ie(7,N["c"]),ie(8,B["a"]),ie(9,q["a"]),ie(10,Q["a"])],t),t}(l["a"]),ae=function(e){function t(t){var i=e.call(this)||this;return i._dataSource=t,i._insertColor=null,i._removeColor=null,i}return ee(t,e),t.prototype.applyColors=function(e){var t=(e.getColor(L["j"])||L["g"]).transparent(2),i=(e.getColor(L["l"])||L["h"]).transparent(2),o=!t.equals(this._insertColor)||!i.equals(this._removeColor);return this._insertColor=t,this._removeColor=i,o},t.prototype.getEditorsDiffDecorations=function(e,t,i,o,n,r,a){n=n.sort((function(e,t){return e.afterLineNumber-t.afterLineNumber})),o=o.sort((function(e,t){return e.afterLineNumber-t.afterLineNumber}));var s=this._getViewZones(e,o,n,r,a,i),d=this._getOriginalEditorDecorations(e,t,i,r,a),l=this._getModifiedEditorDecorations(e,t,i,r,a);return{original:{decorations:d.decorations,overviewZones:d.overviewZones,zones:s.original},modified:{decorations:l.decorations,overviewZones:l.overviewZones,zones:s.modified}}},t}(l["a"]),se=function(){function e(e){this._source=e,this._index=-1,this.current=null,this.advance()}return e.prototype.advance=function(){this._index++,this._index<this._source.length?this.current=this._source[this._index]:this.current=null},e}(),de=function(){function e(e,t,i,o,n){this.lineChanges=e,this.originalForeignVZ=t,this.originalLineHeight=i,this.modifiedForeignVZ=o,this.modifiedLineHeight=n}return e.prototype.getViewZones=function(){for(var t={original:[],modified:[]},i=0,o=0,n=0,r=0,a=0,s=0,d=function(e,t){return e.afterLineNumber-t.afterLineNumber},l=function(e,t){if(null===t.domNode&&e.length>0){var i=e[e.length-1];if(i.afterLineNumber===t.afterLineNumber&&null===i.domNode)return void(i.heightInLines+=t.heightInLines)}e.push(t)},u=new se(this.modifiedForeignVZ),h=new se(this.originalForeignVZ),c=0,f=this.lineChanges.length;c<=f;c++){var g=c<f?this.lineChanges[c]:null;null!==g?(n=g.originalStartLineNumber+(g.originalEndLineNumber>0?-1:0),r=g.modifiedStartLineNumber+(g.modifiedEndLineNumber>0?-1:0),o=g.originalEndLineNumber>0?g.originalEndLineNumber-g.originalStartLineNumber+1:0,i=g.modifiedEndLineNumber>0?g.modifiedEndLineNumber-g.modifiedStartLineNumber+1:0,a=Math.max(g.originalStartLineNumber,g.originalEndLineNumber),s=Math.max(g.modifiedStartLineNumber,g.modifiedEndLineNumber)):(n+=1e7+o,r+=1e7+i,a=n,s=r);var m=[],p=[];while(u.current&&u.current.afterLineNumber<=s){var _=void 0;_=u.current.afterLineNumber<=r?n-r+u.current.afterLineNumber:a;var v=null;g&&g.modifiedStartLineNumber<=u.current.afterLineNumber&&u.current.afterLineNumber<=g.modifiedEndLineNumber&&(v=this._createOriginalMarginDomNodeForModifiedForeignViewZoneInAddedRegion()),m.push({afterLineNumber:_,heightInLines:u.current.height/this.modifiedLineHeight,domNode:null,marginDomNode:v}),u.advance()}while(h.current&&h.current.afterLineNumber<=a){_=void 0;_=h.current.afterLineNumber<=n?r-n+h.current.afterLineNumber:s,p.push({afterLineNumber:_,heightInLines:h.current.height/this.originalLineHeight,domNode:null}),h.advance()}if(null!==g&&me(g)){var D=this._produceOriginalFromDiff(g,o,i);D&&m.push(D)}if(null!==g&&pe(g)){D=this._produceModifiedFromDiff(g,o,i);D&&p.push(D)}var y=0,w=0;m=m.sort(d),p=p.sort(d);while(y<m.length&&w<p.length){var b=m[y],C=p[w],E=b.afterLineNumber-n,S=C.afterLineNumber-r;E<S?(l(t.original,b),y++):S<E?(l(t.modified,C),w++):b.shouldNotShrink?(l(t.original,b),y++):C.shouldNotShrink?(l(t.modified,C),w++):b.heightInLines>=C.heightInLines?(b.heightInLines-=C.heightInLines,w++):(C.heightInLines-=b.heightInLines,y++)}while(y<m.length)l(t.original,m[y]),y++;while(w<p.length)l(t.modified,p[w]),w++}return{original:e._ensureDomNodes(t.original),modified:e._ensureDomNodes(t.modified)}},e._ensureDomNodes=function(e){return e.map((function(e){return e.domNode||(e.domNode=_e()),e}))},e}();function le(e,t,i,o,n){return{range:new F["a"](e,t,i,o),options:n}}var ue={charDelete:H["a"].register({className:"char-delete"}),charDeleteWholeLine:H["a"].register({className:"char-delete",isWholeLine:!0}),charInsert:H["a"].register({className:"char-insert"}),charInsertWholeLine:H["a"].register({className:"char-insert",isWholeLine:!0}),lineInsert:H["a"].register({className:"line-insert",marginClassName:"line-insert",isWholeLine:!0}),lineInsertWithSign:H["a"].register({className:"line-insert",linesDecorationsClassName:"insert-sign codicon codicon-add",marginClassName:"line-insert",isWholeLine:!0}),lineDelete:H["a"].register({className:"line-delete",marginClassName:"line-delete",isWholeLine:!0}),lineDeleteWithSign:H["a"].register({className:"line-delete",linesDecorationsClassName:"delete-sign codicon codicon-remove",marginClassName:"line-delete",isWholeLine:!0}),lineDeleteMargin:H["a"].register({marginClassName:"line-delete"})},he=function(e){function t(t,i){var o=e.call(this,t)||this;return o._disableSash=!1===i,o._sashRatio=null,o._sashPosition=null,o._startSashPosition=null,o._sash=o._register(new a["a"](o._dataSource.getContainerDomNode(),o)),o._disableSash&&(o._sash.state=0),o._sash.onDidStart((function(){return o.onSashDragStart()})),o._sash.onDidChange((function(e){return o.onSashDrag(e)})),o._sash.onDidEnd((function(){return o.onSashDragEnd()})),o._sash.onDidReset((function(){return o.onSashReset()})),o}return ee(t,e),t.prototype.setEnableSplitViewResizing=function(e){var t=!1===e;this._disableSash!==t&&(this._disableSash=t,this._sash.state=this._disableSash?0:3)},t.prototype.layout=function(e){void 0===e&&(e=this._sashRatio);var i=this._dataSource.getWidth(),o=i-re.ENTIRE_DIFF_OVERVIEW_WIDTH,n=Math.floor((e||.5)*o),r=Math.floor(.5*o);return n=this._disableSash?r:n||r,o>2*t.MINIMUM_EDITOR_WIDTH?(n<t.MINIMUM_EDITOR_WIDTH&&(n=t.MINIMUM_EDITOR_WIDTH),n>o-t.MINIMUM_EDITOR_WIDTH&&(n=o-t.MINIMUM_EDITOR_WIDTH)):n=r,this._sashPosition!==n&&(this._sashPosition=n,this._sash.layout()),this._sashPosition},t.prototype.onSashDragStart=function(){this._startSashPosition=this._sashPosition},t.prototype.onSashDrag=function(e){var t=this._dataSource.getWidth(),i=t-re.ENTIRE_DIFF_OVERVIEW_WIDTH,o=this.layout((this._startSashPosition+(e.currentX-e.startX))/i);this._sashRatio=o/i,this._dataSource.relayoutEditors()},t.prototype.onSashDragEnd=function(){this._sash.layout()},t.prototype.onSashReset=function(){this._sashRatio=.5,this._dataSource.relayoutEditors(),this._sash.layout()},t.prototype.getVerticalSashTop=function(e){return 0},t.prototype.getVerticalSashLeft=function(e){return this._sashPosition},t.prototype.getVerticalSashHeight=function(e){return this._dataSource.getHeight()},t.prototype._getViewZones=function(e,t,i,o,n){var r=new ce(e,t,o.getOption(49),i,n.getOption(49));return r.getViewZones()},t.prototype._getOriginalEditorDecorations=function(e,t,i,o,n){for(var r=String(this._removeColor),a={decorations:[],overviewZones:[]},s=o.getModel(),d=0,l=e.length;d<l;d++){var u=e[d];if(pe(u)&&(a.decorations.push({range:new F["a"](u.originalStartLineNumber,1,u.originalEndLineNumber,1073741824),options:i?ue.lineDeleteWithSign:ue.lineDelete}),me(u)&&u.charChanges||a.decorations.push(le(u.originalStartLineNumber,1,u.originalEndLineNumber,1073741824,ue.charDeleteWholeLine)),a.overviewZones.push(new j["a"](u.originalStartLineNumber,u.originalEndLineNumber,r)),u.charChanges))for(var h=0,c=u.charChanges.length;h<c;h++){var f=u.charChanges[h];if(pe(f))if(t)for(var g=f.originalStartLineNumber;g<=f.originalEndLineNumber;g++){var m=void 0,p=void 0;m=g===f.originalStartLineNumber?f.originalStartColumn:s.getLineFirstNonWhitespaceColumn(g),p=g===f.originalEndLineNumber?f.originalEndColumn:s.getLineLastNonWhitespaceColumn(g),a.decorations.push(le(g,m,g,p,ue.charDelete))}else a.decorations.push(le(f.originalStartLineNumber,f.originalStartColumn,f.originalEndLineNumber,f.originalEndColumn,ue.charDelete))}}return a},t.prototype._getModifiedEditorDecorations=function(e,t,i,o,n){for(var r=String(this._insertColor),a={decorations:[],overviewZones:[]},s=n.getModel(),d=0,l=e.length;d<l;d++){var u=e[d];if(me(u)&&(a.decorations.push({range:new F["a"](u.modifiedStartLineNumber,1,u.modifiedEndLineNumber,1073741824),options:i?ue.lineInsertWithSign:ue.lineInsert}),pe(u)&&u.charChanges||a.decorations.push(le(u.modifiedStartLineNumber,1,u.modifiedEndLineNumber,1073741824,ue.charInsertWholeLine)),a.overviewZones.push(new j["a"](u.modifiedStartLineNumber,u.modifiedEndLineNumber,r)),u.charChanges))for(var h=0,c=u.charChanges.length;h<c;h++){var f=u.charChanges[h];if(me(f))if(t)for(var g=f.modifiedStartLineNumber;g<=f.modifiedEndLineNumber;g++){var m=void 0,p=void 0;m=g===f.modifiedStartLineNumber?f.modifiedStartColumn:s.getLineFirstNonWhitespaceColumn(g),p=g===f.modifiedEndLineNumber?f.modifiedEndColumn:s.getLineLastNonWhitespaceColumn(g),a.decorations.push(le(g,m,g,p,ue.charInsert))}else a.decorations.push(le(f.modifiedStartLineNumber,f.modifiedStartColumn,f.modifiedEndLineNumber,f.modifiedEndColumn,ue.charInsert))}}return a},t.MINIMUM_EDITOR_WIDTH=100,t}(ae),ce=function(e){function t(t,i,o,n,r){return e.call(this,t,i,o,n,r)||this}return ee(t,e),t.prototype._createOriginalMarginDomNodeForModifiedForeignViewZoneInAddedRegion=function(){return null},t.prototype._produceOriginalFromDiff=function(e,t,i){return i>t?{afterLineNumber:Math.max(e.originalStartLineNumber,e.originalEndLineNumber),heightInLines:i-t,domNode:null}:null},t.prototype._produceModifiedFromDiff=function(e,t,i){return t>i?{afterLineNumber:Math.max(e.modifiedStartLineNumber,e.modifiedEndLineNumber),heightInLines:t-i,domNode:null}:null},t}(de),fe=function(e){function t(t,i){var o=e.call(this,t)||this;return o.decorationsLeft=t.getOriginalEditor().getLayoutInfo().decorationsLeft,o._register(t.getOriginalEditor().onDidLayoutChange((function(e){o.decorationsLeft!==e.decorationsLeft&&(o.decorationsLeft=e.decorationsLeft,t.relayoutEditors())}))),o}return ee(t,e),t.prototype.setEnableSplitViewResizing=function(e){},t.prototype._getViewZones=function(e,t,i,o,n,r){var a=new ge(e,t,i,o,n,r);return a.getViewZones()},t.prototype._getOriginalEditorDecorations=function(e,t,i,o,n){for(var r=String(this._removeColor),a={decorations:[],overviewZones:[]},s=0,d=e.length;s<d;s++){var l=e[s];pe(l)&&(a.decorations.push({range:new F["a"](l.originalStartLineNumber,1,l.originalEndLineNumber,1073741824),options:ue.lineDeleteMargin}),a.overviewZones.push(new j["a"](l.originalStartLineNumber,l.originalEndLineNumber,r)))}return a},t.prototype._getModifiedEditorDecorations=function(e,t,i,o,n){for(var r=String(this._insertColor),a={decorations:[],overviewZones:[]},s=n.getModel(),d=0,l=e.length;d<l;d++){var u=e[d];if(me(u))if(a.decorations.push({range:new F["a"](u.modifiedStartLineNumber,1,u.modifiedEndLineNumber,1073741824),options:i?ue.lineInsertWithSign:ue.lineInsert}),a.overviewZones.push(new j["a"](u.modifiedStartLineNumber,u.modifiedEndLineNumber,r)),u.charChanges)for(var h=0,c=u.charChanges.length;h<c;h++){var f=u.charChanges[h];if(me(f))if(t)for(var g=f.modifiedStartLineNumber;g<=f.modifiedEndLineNumber;g++){var m=void 0,p=void 0;m=g===f.modifiedStartLineNumber?f.modifiedStartColumn:s.getLineFirstNonWhitespaceColumn(g),p=g===f.modifiedEndLineNumber?f.modifiedEndColumn:s.getLineLastNonWhitespaceColumn(g),a.decorations.push(le(g,m,g,p,ue.charInsert))}else a.decorations.push(le(f.modifiedStartLineNumber,f.modifiedStartColumn,f.modifiedEndLineNumber,f.modifiedEndColumn,ue.charInsert))}else a.decorations.push(le(u.modifiedStartLineNumber,1,u.modifiedEndLineNumber,1073741824,ue.charInsertWholeLine))}return a},t.prototype.layout=function(){return Math.max(5,this.decorationsLeft)},t}(ae),ge=function(e){function t(t,i,o,n,r,a){var s=e.call(this,t,i,n.getOption(49),o,r.getOption(49))||this;return s.originalModel=n.getModel(),s.modifiedEditorOptions=r.getOptions(),s.modifiedEditorTabSize=r.getModel().getOptions().tabSize,s.renderIndicators=a,s}return ee(t,e),t.prototype._createOriginalMarginDomNodeForModifiedForeignViewZoneInAddedRegion=function(){var e=document.createElement("div");return e.className="inline-added-margin-view-zone",e},t.prototype._produceOriginalFromDiff=function(e,t,i){var o=document.createElement("div");return o.className="inline-added-margin-view-zone",{afterLineNumber:Math.max(e.originalStartLineNumber,e.originalEndLineNumber),heightInLines:i,domNode:document.createElement("div"),marginDomNode:o}},t.prototype._produceModifiedFromDiff=function(e,t,i){var o=[];if(e.charChanges)for(var n=0,r=e.charChanges.length;n<r;n++){var a=e.charChanges[n];pe(a)&&o.push(new E["a"](new F["a"](a.originalStartLineNumber,a.originalStartColumn,a.originalEndLineNumber,a.originalEndColumn),"char-delete",0))}for(var s=Object(W["a"])(1e4),d=[],l=this.modifiedEditorOptions.get(107),u=this.modifiedEditorOptions.get(34),c=l.decorationsWidth,f=this.modifiedEditorOptions.get(49),g=u.typicalHalfwidthCharacterWidth,m=0,p=[],_=e.originalStartLineNumber;_<=e.originalEndLineNumber;_++)if(m=Math.max(m,this._renderOriginalLine(_-e.originalStartLineNumber,this.originalModel,this.modifiedEditorOptions,this.modifiedEditorTabSize,_,o,s)),p.push(this.originalModel.getLineContent(_)),this.renderIndicators){var v=_-e.originalStartLineNumber;d=d.concat(['<div class="delete-sign codicon codicon-remove" style="position:absolute;top:'+v*f+"px;width:"+c+"px;height:"+f+'px;right:0;"></div>'])}m+=this.modifiedEditorOptions.get(79);var D=document.createElement("div");D.className="view-lines line-delete",D.innerHTML=s.build(),h["a"].applyFontInfoSlow(D,u);var y=document.createElement("div");return y.className="inline-deleted-margin-view-zone",y.innerHTML=d.join(""),h["a"].applyFontInfoSlow(y,u),{shouldNotShrink:!0,afterLineNumber:0===e.modifiedEndLineNumber?e.modifiedStartLineNumber:e.modifiedStartLineNumber-1,heightInLines:t,minWidthInPx:m*g,domNode:D,marginDomNode:y,diff:{originalStartLineNumber:e.originalStartLineNumber,originalEndLineNumber:e.originalEndLineNumber,modifiedStartLineNumber:e.modifiedStartLineNumber,modifiedEndLineNumber:e.modifiedEndLineNumber,originalContent:p}}},t.prototype._renderOriginalLine=function(e,t,i,o,n,r,a){var s=t.getLineTokens(n),d=s.getLineContent(),l=i.get(34),u=z["a"].filter(r,n,1,d.length+1);a.appendASCIIString('<div class="view-line'),0===r.length&&a.appendASCIIString(" char-delete"),a.appendASCIIString('" style="top:'),a.appendASCIIString(String(e*i.get(49))),a.appendASCIIString('px;width:1000000px;">');var h=E["d"].isBasicASCII(d,t.mightContainNonBasicASCII()),c=E["d"].containsRTL(d,h,t.mightContainRTL()),f=Object(C["d"])(new C["c"](l.isMonospace&&!i.get(23),l.canUseHalfwidthRightwardsArrow,d,!1,h,c,0,s,u,o,0,l.spaceWidth,l.middotWidth,i.get(88),i.get(74),i.get(69),i.get(35)!==D["d"].OFF,null),a);a.appendASCIIString("</div>");var g=f.characterMapping.getAbsoluteOffsets();return g.length>0?g[g.length-1]:0},t}(de);function me(e){return e.modifiedEndLineNumber>0}function pe(e){return e.originalEndLineNumber>0}function _e(){var e=document.createElement("div");return e.className="diagonal-fill",e}Object(N["e"])((function(e,t){var i=e.getColor(L["j"]);i&&(t.addRule(".monaco-editor .line-insert, .monaco-editor .char-insert { background-color: "+i+"; }"),t.addRule(".monaco-diff-editor .line-insert, .monaco-diff-editor .char-insert { background-color: "+i+"; }"),t.addRule(".monaco-editor .inline-added-margin-view-zone { background-color: "+i+"; }"));var o=e.getColor(L["l"]);o&&(t.addRule(".monaco-editor .line-delete, .monaco-editor .char-delete { background-color: "+o+"; }"),t.addRule(".monaco-diff-editor .line-delete, .monaco-diff-editor .char-delete { background-color: "+o+"; }"),t.addRule(".monaco-editor .inline-deleted-margin-view-zone { background-color: "+o+"; }"));var n=e.getColor(L["k"]);n&&t.addRule(".monaco-editor .line-insert, .monaco-editor .char-insert { border: 1px "+("hc"===e.type?"dashed":"solid")+" "+n+"; }");var r=e.getColor(L["m"]);r&&t.addRule(".monaco-editor .line-delete, .monaco-editor .char-delete { border: 1px "+("hc"===e.type?"dashed":"solid")+" "+r+"; }");var a=e.getColor(L["Vb"]);a&&t.addRule(".monaco-diff-editor.side-by-side .editor.modified { box-shadow: -6px 0 5px -5px "+a+"; }");var s=e.getColor(L["i"]);s&&t.addRule(".monaco-diff-editor.side-by-side .editor.modified { border-left: 1px solid "+s+"; }")}))},"94a7":function(e,t,i){},"96b9":function(e,t,i){},"9c1d":function(e,t,i){"use strict";i.d(t,"a",(function(){return z}));i("96b9");var o=i("dff7"),n=i("11f7"),r=i("fdcc"),a=i("308f"),s=i("a666"),d=i("b589"),l=i("1ddc"),u=i("b2cc"),h=i("5717"),c=i("7e97"),f=i("7608"),g=i("fd49"),m=i("04a5"),p=i("2e5d"),_=i("7061"),v=i("6a89"),D=i("8025"),y=i("f58f"),w=i("8ae8"),b=i("c101"),C=i("b707"),E=i("918c"),S=i("303e"),L=i("91df"),N=i("9e74"),I=i("4fc3"),R=i("0a0f"),O=i("f07b"),M=i("b0cd"),T=i("b7d0"),x=i("4779"),P=i("ef8e"),V=i("7e0b"),F=i("e8f2"),W=function(){var e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])},e(t,i)};return function(t,i){function o(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(o.prototype=i.prototype,new o)}}(),A=function(e,t,i,o){var n,r=arguments.length,a=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,i):o;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)a=Reflect.decorate(e,t,i,o);else for(var s=e.length-1;s>=0;s--)(n=e[s])&&(a=(r<3?n(a):r>3?n(t,i,a):n(t,i))||a);return r>3&&a&&Object.defineProperty(t,i,a),a},H=function(e,t){return function(i,o){t(i,o,e)}},k=0,j=function(){function e(e,t,i,o,n,r){this.model=e,this.viewModel=t,this.cursor=i,this.view=o,this.hasRealView=n,this.listenersToRemove=r}return e.prototype.dispose=function(){Object(s["f"])(this.listenersToRemove),this.model.onBeforeDetached(),this.hasRealView&&this.view.dispose(),this.cursor.dispose(),this.viewModel.dispose()},e}(),z=function(e){function t(t,i,o,n,s,d,l,h,c,f){var g,m=e.call(this)||this;m._onDidDispose=m._register(new a["a"]),m.onDidDispose=m._onDidDispose.event,m._onDidChangeModelContent=m._register(new a["a"]),m.onDidChangeModelContent=m._onDidChangeModelContent.event,m._onDidChangeModelLanguage=m._register(new a["a"]),m.onDidChangeModelLanguage=m._onDidChangeModelLanguage.event,m._onDidChangeModelLanguageConfiguration=m._register(new a["a"]),m.onDidChangeModelLanguageConfiguration=m._onDidChangeModelLanguageConfiguration.event,m._onDidChangeModelOptions=m._register(new a["a"]),m.onDidChangeModelOptions=m._onDidChangeModelOptions.event,m._onDidChangeModelDecorations=m._register(new a["a"]),m.onDidChangeModelDecorations=m._onDidChangeModelDecorations.event,m._onDidChangeConfiguration=m._register(new a["a"]),m.onDidChangeConfiguration=m._onDidChangeConfiguration.event,m._onDidChangeModel=m._register(new a["a"]),m.onDidChangeModel=m._onDidChangeModel.event,m._onDidChangeCursorPosition=m._register(new a["a"]),m.onDidChangeCursorPosition=m._onDidChangeCursorPosition.event,m._onDidChangeCursorSelection=m._register(new a["a"]),m.onDidChangeCursorSelection=m._onDidChangeCursorSelection.event,m._onDidAttemptReadOnlyEdit=m._register(new a["a"]),m.onDidAttemptReadOnlyEdit=m._onDidAttemptReadOnlyEdit.event,m._onDidLayoutChange=m._register(new a["a"]),m.onDidLayoutChange=m._onDidLayoutChange.event,m._editorTextFocus=m._register(new U),m.onDidFocusEditorText=m._editorTextFocus.onDidChangeToTrue,m.onDidBlurEditorText=m._editorTextFocus.onDidChangeToFalse,m._editorWidgetFocus=m._register(new U),m.onDidFocusEditorWidget=m._editorWidgetFocus.onDidChangeToTrue,m.onDidBlurEditorWidget=m._editorWidgetFocus.onDidChangeToFalse,m._onWillType=m._register(new a["a"]),m.onWillType=m._onWillType.event,m._onDidType=m._register(new a["a"]),m.onDidType=m._onDidType.event,m._onDidCompositionStart=m._register(new a["a"]),m.onDidCompositionStart=m._onDidCompositionStart.event,m._onDidCompositionEnd=m._register(new a["a"]),m.onDidCompositionEnd=m._onDidCompositionEnd.event,m._onDidPaste=m._register(new a["a"]),m.onDidPaste=m._onDidPaste.event,m._onMouseUp=m._register(new a["a"]),m.onMouseUp=m._onMouseUp.event,m._onMouseDown=m._register(new a["a"]),m.onMouseDown=m._onMouseDown.event,m._onMouseDrag=m._register(new a["a"]),m.onMouseDrag=m._onMouseDrag.event,m._onMouseDrop=m._register(new a["a"]),m.onMouseDrop=m._onMouseDrop.event,m._onContextMenu=m._register(new a["a"]),m.onContextMenu=m._onContextMenu.event,m._onMouseMove=m._register(new a["a"]),m.onMouseMove=m._onMouseMove.event,m._onMouseLeave=m._register(new a["a"]),m.onMouseLeave=m._onMouseLeave.event,m._onMouseWheel=m._register(new a["a"]),m.onMouseWheel=m._onMouseWheel.event,m._onKeyUp=m._register(new a["a"]),m.onKeyUp=m._onKeyUp.event,m._onKeyDown=m._register(new a["a"]),m.onKeyDown=m._onKeyDown.event,m._onDidContentSizeChange=m._register(new a["a"]),m.onDidContentSizeChange=m._onDidContentSizeChange.event,m._onDidScrollChange=m._register(new a["a"]),m.onDidScrollChange=m._onDidScrollChange.event,m._onDidChangeViewZones=m._register(new a["a"]),m.onDidChangeViewZones=m._onDidChangeViewZones.event,m._domElement=t,m._id=++k,m._decorationTypeKeysToIds={},m._decorationTypeSubtypes={},m.isSimpleWidget=o.isSimpleWidget||!1,m._telemetryData=o.telemetryData,i=i||{},m._configuration=m._register(m._createConfiguration(i,f)),m._register(m._configuration.onDidChange((function(e){m._onDidChangeConfiguration.fire(e);var t=m._configuration.options;if(e.hasChanged(107)){var i=t.get(107);m._onDidLayoutChange.fire(i)}}))),m._contextKeyService=m._register(l.createScoped(m._domElement)),m._notificationService=c,m._codeEditorService=s,m._commandService=d,m._themeService=h,m._register(new Z(m,m._contextKeyService)),m._register(new B(m,m._contextKeyService)),m._instantiationService=n.createChild(new O["a"]([I["c"],m._contextKeyService])),m._modelData=null,m._contributions={},m._actions={},m._focusTracker=new q(t),m._focusTracker.onChange((function(){m._editorWidgetFocus.setValue(m._focusTracker.hasFocus())})),m._contentWidgets={},m._overlayWidgets={},g=Array.isArray(o.contributions)?o.contributions:u["d"].getEditorContributions();for(var p=0,_=g;p<_.length;p++){var v=_[p];try{var D=m._instantiationService.createInstance(v.ctor,m);m._contributions[v.id]=D}catch(w){Object(r["e"])(w)}}return u["d"].getEditorActions().forEach((function(e){var t=new y["a"](e.id,e.label,e.alias,Object(P["n"])(e.precondition),(function(){return m._instantiationService.invokeFunction((function(t){return Promise.resolve(e.runEditorCommand(t,m,null))}))}),m._contextKeyService);m._actions[t.id]=t})),m._codeEditorService.addCodeEditor(m),m}return W(t,e),t.prototype._createConfiguration=function(e,t){return new l["a"](this.isSimpleWidget,e,this._domElement,t)},t.prototype.getId=function(){return this.getEditorType()+":"+this._id},t.prototype.getEditorType=function(){return w["a"].ICodeEditor},t.prototype.dispose=function(){this._codeEditorService.removeCodeEditor(this),this._focusTracker.dispose();for(var t=Object.keys(this._contributions),i=0,o=t.length;i<o;i++){var n=t[i];this._contributions[n].dispose()}this._removeDecorationTypes(),this._postDetachModelCleanup(this._detachModel()),this._onDidDispose.fire(),e.prototype.dispose.call(this)},t.prototype.invokeWithinContext=function(e){return this._instantiationService.invokeFunction(e)},t.prototype.updateOptions=function(e){this._configuration.updateOptions(e)},t.prototype.getOptions=function(){return this._configuration.options},t.prototype.getOption=function(e){return this._configuration.options.get(e)},t.prototype.getRawOptions=function(){return this._configuration.getRawOptions()},t.prototype.getValue=function(e){if(void 0===e&&(e=null),!this._modelData)return"";var t=!(!e||!e.preserveBOM),i=0;return e&&e.lineEnding&&"\n"===e.lineEnding?i=1:e&&e.lineEnding&&"\r\n"===e.lineEnding&&(i=2),this._modelData.model.getValue(i,t)},t.prototype.setValue=function(e){this._modelData&&this._modelData.model.setValue(e)},t.prototype.getModel=function(){return this._modelData?this._modelData.model:null},t.prototype.setModel=function(e){void 0===e&&(e=null);var t=e;if((null!==this._modelData||null!==t)&&(!this._modelData||this._modelData.model!==t)){var i=this.hasTextFocus(),o=this._detachModel();this._attachModel(t),i&&this.hasModel()&&this.focus();var n={oldModelUrl:o?o.uri:null,newModelUrl:t?t.uri:null};this._removeDecorationTypes(),this._onDidChangeModel.fire(n),this._postDetachModelCleanup(o)}},t.prototype._removeDecorationTypes=function(){if(this._decorationTypeKeysToIds={},this._decorationTypeSubtypes){for(var e in this._decorationTypeSubtypes){var t=this._decorationTypeSubtypes[e];for(var i in t)this._removeDecorationType(e+"-"+i)}this._decorationTypeSubtypes={}}},t.prototype.getVisibleRanges=function(){return this._modelData?this._modelData.viewModel.getVisibleRanges():[]},t.prototype.getWhitespaces=function(){return this._modelData?this._modelData.viewModel.viewLayout.getWhitespaces():[]},t._getVerticalOffsetForPosition=function(e,t,i){var o=e.model.validatePosition({lineNumber:t,column:i}),n=e.viewModel.coordinatesConverter.convertModelPositionToViewPosition(o);return e.viewModel.viewLayout.getVerticalOffsetForLineNumber(n.lineNumber)},t.prototype.getTopForLineNumber=function(e){return this._modelData?t._getVerticalOffsetForPosition(this._modelData,e,1):-1},t.prototype.getTopForPosition=function(e,i){return this._modelData?t._getVerticalOffsetForPosition(this._modelData,e,i):-1},t.prototype.setHiddenAreas=function(e){this._modelData&&this._modelData.viewModel.setHiddenAreas(e.map((function(e){return v["a"].lift(e)})))},t.prototype.getVisibleColumnFromPosition=function(e){if(!this._modelData)return e.column;var t=this._modelData.model.validatePosition(e),i=this._modelData.model.getOptions().tabSize;return p["a"].visibleColumnFromColumn(this._modelData.model.getLineContent(t.lineNumber),t.column,i)+1},t.prototype.getPosition=function(){return this._modelData?this._modelData.cursor.getPosition():null},t.prototype.setPosition=function(e){if(this._modelData){if(!_["a"].isIPosition(e))throw new Error("Invalid arguments");this._modelData.cursor.setSelections("api",[{selectionStartLineNumber:e.lineNumber,selectionStartColumn:e.column,positionLineNumber:e.lineNumber,positionColumn:e.column}])}},t.prototype._sendRevealRange=function(e,t,i,o){if(this._modelData){if(!v["a"].isIRange(e))throw new Error("Invalid arguments");var n=this._modelData.model.validateRange(e),r=this._modelData.viewModel.coordinatesConverter.convertModelRangeToViewRange(n);this._modelData.cursor.emitCursorRevealRange("api",r,t,i,o)}},t.prototype.revealLine=function(e,t){void 0===t&&(t=0),this._revealLine(e,0,t)},t.prototype.revealLineInCenter=function(e,t){void 0===t&&(t=0),this._revealLine(e,1,t)},t.prototype.revealLineInCenterIfOutsideViewport=function(e,t){void 0===t&&(t=0),this._revealLine(e,2,t)},t.prototype._revealLine=function(e,t,i){if("number"!==typeof e)throw new Error("Invalid arguments");this._sendRevealRange(new v["a"](e,1,e,1),t,!1,i)},t.prototype.revealPosition=function(e,t){void 0===t&&(t=0),this._revealPosition(e,0,!0,t)},t.prototype.revealPositionInCenter=function(e,t){void 0===t&&(t=0),this._revealPosition(e,1,!0,t)},t.prototype.revealPositionInCenterIfOutsideViewport=function(e,t){void 0===t&&(t=0),this._revealPosition(e,2,!0,t)},t.prototype._revealPosition=function(e,t,i,o){if(!_["a"].isIPosition(e))throw new Error("Invalid arguments");this._sendRevealRange(new v["a"](e.lineNumber,e.column,e.lineNumber,e.column),t,i,o)},t.prototype.getSelection=function(){return this._modelData?this._modelData.cursor.getSelection():null},t.prototype.getSelections=function(){return this._modelData?this._modelData.cursor.getSelections():null},t.prototype.setSelection=function(e){var t=D["a"].isISelection(e),i=v["a"].isIRange(e);if(!t&&!i)throw new Error("Invalid arguments");if(t)this._setSelectionImpl(e);else if(i){var o={selectionStartLineNumber:e.startLineNumber,selectionStartColumn:e.startColumn,positionLineNumber:e.endLineNumber,positionColumn:e.endColumn};this._setSelectionImpl(o)}},t.prototype._setSelectionImpl=function(e){if(this._modelData){var t=new D["a"](e.selectionStartLineNumber,e.selectionStartColumn,e.positionLineNumber,e.positionColumn);this._modelData.cursor.setSelections("api",[t])}},t.prototype.revealLines=function(e,t,i){void 0===i&&(i=0),this._revealLines(e,t,0,i)},t.prototype.revealLinesInCenter=function(e,t,i){void 0===i&&(i=0),this._revealLines(e,t,1,i)},t.prototype.revealLinesInCenterIfOutsideViewport=function(e,t,i){void 0===i&&(i=0),this._revealLines(e,t,2,i)},t.prototype._revealLines=function(e,t,i,o){if("number"!==typeof e||"number"!==typeof t)throw new Error("Invalid arguments");this._sendRevealRange(new v["a"](e,1,t,1),i,!1,o)},t.prototype.revealRange=function(e,t,i,o){void 0===t&&(t=0),void 0===i&&(i=!1),void 0===o&&(o=!0),this._revealRange(e,i?1:0,o,t)},t.prototype.revealRangeInCenter=function(e,t){void 0===t&&(t=0),this._revealRange(e,1,!0,t)},t.prototype.revealRangeInCenterIfOutsideViewport=function(e,t){void 0===t&&(t=0),this._revealRange(e,2,!0,t)},t.prototype.revealRangeAtTop=function(e,t){void 0===t&&(t=0),this._revealRange(e,3,!0,t)},t.prototype._revealRange=function(e,t,i,o){if(!v["a"].isIRange(e))throw new Error("Invalid arguments");this._sendRevealRange(v["a"].lift(e),t,i,o)},t.prototype.setSelections=function(e,t){if(void 0===t&&(t="api"),this._modelData){if(!e||0===e.length)throw new Error("Invalid arguments");for(var i=0,o=e.length;i<o;i++)if(!D["a"].isISelection(e[i]))throw new Error("Invalid arguments");this._modelData.cursor.setSelections(t,e)}},t.prototype.getContentWidth=function(){return this._modelData?this._modelData.viewModel.viewLayout.getContentWidth():-1},t.prototype.getScrollWidth=function(){return this._modelData?this._modelData.viewModel.viewLayout.getScrollWidth():-1},t.prototype.getScrollLeft=function(){return this._modelData?this._modelData.viewModel.viewLayout.getCurrentScrollLeft():-1},t.prototype.getContentHeight=function(){return this._modelData?this._modelData.viewModel.viewLayout.getContentHeight():-1},t.prototype.getScrollHeight=function(){return this._modelData?this._modelData.viewModel.viewLayout.getScrollHeight():-1},t.prototype.getScrollTop=function(){return this._modelData?this._modelData.viewModel.viewLayout.getCurrentScrollTop():-1},t.prototype.setScrollLeft=function(e){if(this._modelData){if("number"!==typeof e)throw new Error("Invalid arguments");this._modelData.viewModel.viewLayout.setScrollPositionNow({scrollLeft:e})}},t.prototype.setScrollTop=function(e){if(this._modelData){if("number"!==typeof e)throw new Error("Invalid arguments");this._modelData.viewModel.viewLayout.setScrollPositionNow({scrollTop:e})}},t.prototype.setScrollPosition=function(e){this._modelData&&this._modelData.viewModel.viewLayout.setScrollPositionNow(e)},t.prototype.saveViewState=function(){if(!this._modelData)return null;for(var e={},t=Object.keys(this._contributions),i=0,o=t;i<o.length;i++){var n=o[i],r=this._contributions[n];"function"===typeof r.saveViewState&&(e[n]=r.saveViewState())}var a=this._modelData.cursor.saveState(),s=this._modelData.viewModel.saveState();return{cursorState:a,viewState:s,contributionsState:e}},t.prototype.restoreViewState=function(e){if(this._modelData&&this._modelData.hasRealView){var t=e;if(t&&t.cursorState&&t.viewState){var i=t.cursorState;Array.isArray(i)?this._modelData.cursor.restoreState(i):this._modelData.cursor.restoreState([i]);for(var o=t.contributionsState||{},n=Object.keys(this._contributions),r=0,a=n.length;r<a;r++){var s=n[r],d=this._contributions[s];"function"===typeof d.restoreViewState&&d.restoreViewState(o[s])}var l=this._modelData.viewModel.reduceRestoreState(t.viewState);this._modelData.view.restoreState(l)}}},t.prototype.getContribution=function(e){return this._contributions[e]||null},t.prototype.getActions=function(){for(var e=[],t=Object.keys(this._actions),i=0,o=t.length;i<o;i++){var n=t[i];e.push(this._actions[n])}return e},t.prototype.getSupportedActions=function(){var e=this.getActions();return e=e.filter((function(e){return e.isSupported()})),e},t.prototype.getAction=function(e){return this._actions[e]||null},t.prototype.trigger=function(e,t,i){if(i=i||{},t===w["b"].Type){if(!this._modelData||"string"!==typeof i.text||0===i.text.length)return;return"keyboard"===e&&this._onWillType.fire(i.text),this._modelData.cursor.trigger(e,t,i),void("keyboard"===e&&this._onDidType.fire(i.text))}if(t!==w["b"].Paste){var o=this.getAction(t);o?Promise.resolve(o.run()).then(void 0,r["e"]):this._modelData&&(this._triggerEditorCommand(e,t,i)||(this._modelData.cursor.trigger(e,t,i),t===w["b"].CompositionStart&&this._onDidCompositionStart.fire(),t===w["b"].CompositionEnd&&this._onDidCompositionEnd.fire()))}else{if(!this._modelData||"string"!==typeof i.text||0===i.text.length)return;var n=this._modelData.cursor.getSelection().getStartPosition();this._modelData.cursor.trigger(e,t,i);var a=this._modelData.cursor.getSelection().getStartPosition();"keyboard"===e&&this._onDidPaste.fire({range:new v["a"](n.lineNumber,n.column,a.lineNumber,a.column),mode:i.mode})}},t.prototype._triggerEditorCommand=function(e,t,i){var o=this,n=u["d"].getEditorCommand(t);return!!n&&(i=i||{},i.source=e,this._instantiationService.invokeFunction((function(e){Promise.resolve(n.runEditorCommand(e,o,i)).then(void 0,r["e"])})),!0)},t.prototype._getCursors=function(){return this._modelData?this._modelData.cursor:null},t.prototype.pushUndoStop=function(){return!!this._modelData&&(!this._configuration.options.get(68)&&(this._modelData.model.pushStackElement(),!0))},t.prototype.executeEdits=function(e,t,i){return!!this._modelData&&(!this._configuration.options.get(68)&&(o=i?Array.isArray(i)?function(){return i}:i:function(){return null},this._modelData.cursor.executeEdits(e,t,o),!0));var o},t.prototype.executeCommand=function(e,t){this._modelData&&this._modelData.cursor.trigger(e,w["b"].ExecuteCommand,t)},t.prototype.executeCommands=function(e,t){this._modelData&&this._modelData.cursor.trigger(e,w["b"].ExecuteCommands,t)},t.prototype.changeDecorations=function(e){return this._modelData?this._modelData.model.changeDecorations(e,this._id):null},t.prototype.getLineDecorations=function(e){return this._modelData?this._modelData.model.getLineDecorations(e,this._id,Object(g["j"])(this._configuration.options)):null},t.prototype.deltaDecorations=function(e,t){return this._modelData?0===e.length&&0===t.length?e:this._modelData.model.deltaDecorations(e,t,this._id):[]},t.prototype.removeDecorations=function(e){var t=this._decorationTypeKeysToIds[e];t&&this.deltaDecorations(t,[]),this._decorationTypeKeysToIds.hasOwnProperty(e)&&delete this._decorationTypeKeysToIds[e],this._decorationTypeSubtypes.hasOwnProperty(e)&&delete this._decorationTypeSubtypes[e]},t.prototype.getLayoutInfo=function(){var e=this._configuration.options,t=e.get(107);return t},t.prototype.createOverviewRuler=function(e){return this._modelData&&this._modelData.hasRealView?this._modelData.view.createOverviewRuler(e):null},t.prototype.getContainerDomNode=function(){return this._domElement},t.prototype.getDomNode=function(){return this._modelData&&this._modelData.hasRealView?this._modelData.view.domNode.domNode:null},t.prototype.delegateVerticalScrollbarMouseDown=function(e){this._modelData&&this._modelData.hasRealView&&this._modelData.view.delegateVerticalScrollbarMouseDown(e)},t.prototype.layout=function(e){this._configuration.observeReferenceElement(e),this.render()},t.prototype.focus=function(){this._modelData&&this._modelData.hasRealView&&this._modelData.view.focus()},t.prototype.hasTextFocus=function(){return!(!this._modelData||!this._modelData.hasRealView)&&this._modelData.view.isFocused()},t.prototype.hasWidgetFocus=function(){return this._focusTracker&&this._focusTracker.hasFocus()},t.prototype.addContentWidget=function(e){var t={widget:e,position:e.getPosition()};this._contentWidgets.hasOwnProperty(e.getId())&&console.warn("Overwriting a content widget with the same id."),this._contentWidgets[e.getId()]=t,this._modelData&&this._modelData.hasRealView&&this._modelData.view.addContentWidget(t)},t.prototype.layoutContentWidget=function(e){var t=e.getId();if(this._contentWidgets.hasOwnProperty(t)){var i=this._contentWidgets[t];i.position=e.getPosition(),this._modelData&&this._modelData.hasRealView&&this._modelData.view.layoutContentWidget(i)}},t.prototype.removeContentWidget=function(e){var t=e.getId();if(this._contentWidgets.hasOwnProperty(t)){var i=this._contentWidgets[t];delete this._contentWidgets[t],this._modelData&&this._modelData.hasRealView&&this._modelData.view.removeContentWidget(i)}},t.prototype.addOverlayWidget=function(e){var t={widget:e,position:e.getPosition()};this._overlayWidgets.hasOwnProperty(e.getId())&&console.warn("Overwriting an overlay widget with the same id."),this._overlayWidgets[e.getId()]=t,this._modelData&&this._modelData.hasRealView&&this._modelData.view.addOverlayWidget(t)},t.prototype.layoutOverlayWidget=function(e){var t=e.getId();if(this._overlayWidgets.hasOwnProperty(t)){var i=this._overlayWidgets[t];i.position=e.getPosition(),this._modelData&&this._modelData.hasRealView&&this._modelData.view.layoutOverlayWidget(i)}},t.prototype.removeOverlayWidget=function(e){var t=e.getId();if(this._overlayWidgets.hasOwnProperty(t)){var i=this._overlayWidgets[t];delete this._overlayWidgets[t],this._modelData&&this._modelData.hasRealView&&this._modelData.view.removeOverlayWidget(i)}},t.prototype.changeViewZones=function(e){if(this._modelData&&this._modelData.hasRealView){var t=this._modelData.view.change(e);t&&this._onDidChangeViewZones.fire()}},t.prototype.getTargetAtClientPoint=function(e,t){return this._modelData&&this._modelData.hasRealView?this._modelData.view.getTargetAtClientPoint(e,t):null},t.prototype.getScrolledVisiblePosition=function(e){if(!this._modelData||!this._modelData.hasRealView)return null;var i=this._modelData.model.validatePosition(e),o=this._configuration.options,n=o.get(107),r=t._getVerticalOffsetForPosition(this._modelData,i.lineNumber,i.column)-this.getScrollTop(),a=this._modelData.view.getOffsetForColumn(i.lineNumber,i.column)+n.glyphMarginWidth+n.lineNumbersWidth+n.decorationsWidth-this.getScrollLeft();return{top:r,left:a,height:o.get(49)}},t.prototype.getOffsetForColumn=function(e,t){return this._modelData&&this._modelData.hasRealView?this._modelData.view.getOffsetForColumn(e,t):-1},t.prototype.render=function(e){void 0===e&&(e=!1),this._modelData&&this._modelData.hasRealView&&this._modelData.view.render(!0,e)},t.prototype.setAriaOptions=function(e){this._modelData&&this._modelData.hasRealView&&this._modelData.view.setAriaOptions(e)},t.prototype.applyFontInfo=function(e){l["a"].applyFontInfoSlow(e,this._configuration.options.get(34))},t.prototype._attachModel=function(e){var t=this;if(e){var i=[];this._domElement.setAttribute("data-mode-id",e.getLanguageIdentifier().language),this._configuration.setIsDominatedByLongLines(e.isDominatedByLongLines()),this._configuration.setMaxLineNumber(e.getLineCount()),e.onBeforeAttached();var r=new L["a"](this._id,this._configuration,e,F["a"].create(),V["a"].create(this._configuration.options),(function(e){return n["W"](e)}));i.push(e.onDidChangeDecorations((function(e){return t._onDidChangeModelDecorations.fire(e)}))),i.push(e.onDidChangeLanguage((function(i){t._domElement.setAttribute("data-mode-id",e.getLanguageIdentifier().language),t._onDidChangeModelLanguage.fire(i)}))),i.push(e.onDidChangeLanguageConfiguration((function(e){return t._onDidChangeModelLanguageConfiguration.fire(e)}))),i.push(e.onDidChangeContent((function(e){return t._onDidChangeModelContent.fire(e)}))),i.push(e.onDidChangeOptions((function(e){return t._onDidChangeModelOptions.fire(e)}))),i.push(e.onWillDispose((function(){return t.setModel(null)})));var a=new m["a"](this._configuration,e,r);i.push(a.onDidReachMaxCursorCount((function(){t._notificationService.warn(o["a"]("cursors.maximum","The number of cursors has been limited to {0}.",m["a"].MAX_CURSOR_COUNT))}))),i.push(a.onDidAttemptReadOnlyEdit((function(){t._onDidAttemptReadOnlyEdit.fire(void 0)}))),i.push(a.onDidChange((function(e){for(var i=[],o=0,n=e.selections.length;o<n;o++)i[o]=e.selections[o].getPosition();var r={position:i[0],secondaryPositions:i.slice(1),reason:e.reason,source:e.source};t._onDidChangeCursorPosition.fire(r);var a={selection:e.selections[0],secondarySelections:e.selections.slice(1),modelVersionId:e.modelVersionId,oldSelections:e.oldSelections,oldModelVersionId:e.oldModelVersionId,source:e.source,reason:e.reason};t._onDidChangeCursorSelection.fire(a)})));var s=this._createView(r,a),d=s[0],l=s[1];if(l){this._domElement.appendChild(d.domNode.domNode);for(var u=Object.keys(this._contentWidgets),h=0,c=u.length;h<c;h++){var f=u[h];d.addContentWidget(this._contentWidgets[f])}u=Object.keys(this._overlayWidgets);for(h=0,c=u.length;h<c;h++){f=u[h];d.addOverlayWidget(this._overlayWidgets[f])}d.render(!1,!0),d.domNode.domNode.setAttribute("data-uri",e.uri.toString())}this._modelData=new j(e,r,a,d,l,i)}else this._modelData=null},t.prototype._createView=function(e,t){var i,o=this;i=this.isSimpleWidget?{executeEditorCommand:function(e,i){e.runCoreEditorCommand(t,i)},paste:function(e,t,i,n,r){o.trigger(e,w["b"].Paste,{text:t,pasteOnNewLine:i,multicursorText:n,mode:r})},type:function(e,t){o.trigger(e,w["b"].Type,{text:t})},replacePreviousChar:function(e,t,i){o.trigger(e,w["b"].ReplacePreviousChar,{text:t,replaceCharCnt:i})},compositionStart:function(e){o.trigger(e,w["b"].CompositionStart,void 0)},compositionEnd:function(e){o.trigger(e,w["b"].CompositionEnd,void 0)},cut:function(e){o.trigger(e,w["b"].Cut,void 0)}}:{executeEditorCommand:function(e,i){e.runCoreEditorCommand(t,i)},paste:function(e,t,i,n,r){o._commandService.executeCommand(w["b"].Paste,{text:t,pasteOnNewLine:i,multicursorText:n,mode:r})},type:function(e,t){o._commandService.executeCommand(w["b"].Type,{text:t})},replacePreviousChar:function(e,t,i){o._commandService.executeCommand(w["b"].ReplacePreviousChar,{text:t,replaceCharCnt:i})},compositionStart:function(e){o._commandService.executeCommand(w["b"].CompositionStart,{})},compositionEnd:function(e){o._commandService.executeCommand(w["b"].CompositionEnd,{})},cut:function(e){o._commandService.executeCommand(w["b"].Cut,{})}};var n=new f["a"](e);n.onDidContentSizeChange=function(e){return o._onDidContentSizeChange.fire(e)},n.onDidScroll=function(e){return o._onDidScrollChange.fire(e)},n.onDidGainFocus=function(){return o._editorTextFocus.setValue(!0)},n.onDidLoseFocus=function(){return o._editorTextFocus.setValue(!1)},n.onContextMenu=function(e){return o._onContextMenu.fire(e)},n.onMouseDown=function(e){return o._onMouseDown.fire(e)},n.onMouseUp=function(e){return o._onMouseUp.fire(e)},n.onMouseDrag=function(e){return o._onMouseDrag.fire(e)},n.onMouseDrop=function(e){return o._onMouseDrop.fire(e)},n.onKeyUp=function(e){return o._onKeyUp.fire(e)},n.onMouseMove=function(e){return o._onMouseMove.fire(e)},n.onMouseLeave=function(e){return o._onMouseLeave.fire(e)},n.onMouseWheel=function(e){return o._onMouseWheel.fire(e)},n.onKeyDown=function(e){return o._onKeyDown.fire(e)};var r=new c["a"](i,this._configuration,this._themeService,e,t,n);return[r,!0]},t.prototype._postDetachModelCleanup=function(e){e&&e.removeAllDecorationsWithOwnerId(this._id)},t.prototype._detachModel=function(){if(!this._modelData)return null;var e=this._modelData.model,t=this._modelData.hasRealView?this._modelData.view.domNode.domNode:null;return this._modelData.dispose(),this._modelData=null,this._domElement.removeAttribute("data-mode-id"),t&&this._domElement.removeChild(t),e},t.prototype._removeDecorationType=function(e){this._codeEditorService.removeDecorationType(e)},t.prototype.hasModel=function(){return null!==this._modelData},t=A([H(3,R["a"]),H(4,h["a"]),H(5,N["b"]),H(6,I["c"]),H(7,T["c"]),H(8,M["a"]),H(9,x["b"])],t),t}(s["a"]),U=function(e){function t(){var t=e.call(this)||this;return t._onDidChangeToTrue=t._register(new a["a"]),t.onDidChangeToTrue=t._onDidChangeToTrue.event,t._onDidChangeToFalse=t._register(new a["a"]),t.onDidChangeToFalse=t._onDidChangeToFalse.event,t._value=0,t}return W(t,e),t.prototype.setValue=function(e){var t=e?2:1;this._value!==t&&(this._value=t,2===this._value?this._onDidChangeToTrue.fire():1===this._value&&this._onDidChangeToFalse.fire())},t}(s["a"]),Z=function(e){function t(t,i){var o=e.call(this)||this;return o._editor=t,i.createKey("editorId",t.getId()),o._editorSimpleInput=b["a"].editorSimpleInput.bindTo(i),o._editorFocus=b["a"].focus.bindTo(i),o._textInputFocus=b["a"].textInputFocus.bindTo(i),o._editorTextFocus=b["a"].editorTextFocus.bindTo(i),o._editorTabMovesFocus=b["a"].tabMovesFocus.bindTo(i),o._editorReadonly=b["a"].readOnly.bindTo(i),o._hasMultipleSelections=b["a"].hasMultipleSelections.bindTo(i),o._hasNonEmptySelection=b["a"].hasNonEmptySelection.bindTo(i),o._canUndo=b["a"].canUndo.bindTo(i),o._canRedo=b["a"].canRedo.bindTo(i),o._register(o._editor.onDidChangeConfiguration((function(){return o._updateFromConfig()}))),o._register(o._editor.onDidChangeCursorSelection((function(){return o._updateFromSelection()}))),o._register(o._editor.onDidFocusEditorWidget((function(){return o._updateFromFocus()}))),o._register(o._editor.onDidBlurEditorWidget((function(){return o._updateFromFocus()}))),o._register(o._editor.onDidFocusEditorText((function(){return o._updateFromFocus()}))),o._register(o._editor.onDidBlurEditorText((function(){return o._updateFromFocus()}))),o._register(o._editor.onDidChangeModel((function(){return o._updateFromModel()}))),o._register(o._editor.onDidChangeConfiguration((function(){return o._updateFromModel()}))),o._updateFromConfig(),o._updateFromSelection(),o._updateFromFocus(),o._updateFromModel(),o._editorSimpleInput.set(o._editor.isSimpleWidget),o}return W(t,e),t.prototype._updateFromConfig=function(){var e=this._editor.getOptions();this._editorTabMovesFocus.set(e.get(106)),this._editorReadonly.set(e.get(68))},t.prototype._updateFromSelection=function(){var e=this._editor.getSelections();e?(this._hasMultipleSelections.set(e.length>1),this._hasNonEmptySelection.set(e.some((function(e){return!e.isEmpty()})))):(this._hasMultipleSelections.reset(),this._hasNonEmptySelection.reset())},t.prototype._updateFromFocus=function(){this._editorFocus.set(this._editor.hasWidgetFocus()&&!this._editor.isSimpleWidget),this._editorTextFocus.set(this._editor.hasTextFocus()&&!this._editor.isSimpleWidget),this._textInputFocus.set(this._editor.hasTextFocus())},t.prototype._updateFromModel=function(){var e=this._editor.getModel();this._canUndo.set(Boolean(e&&e.canUndo())),this._canRedo.set(Boolean(e&&e.canRedo()))},t}(s["a"]),B=function(e){function t(t,i){var o=e.call(this)||this;o._editor=t,o._contextKeyService=i,o._langId=b["a"].languageId.bindTo(i),o._hasCompletionItemProvider=b["a"].hasCompletionItemProvider.bindTo(i),o._hasCodeActionsProvider=b["a"].hasCodeActionsProvider.bindTo(i),o._hasCodeLensProvider=b["a"].hasCodeLensProvider.bindTo(i),o._hasDefinitionProvider=b["a"].hasDefinitionProvider.bindTo(i),o._hasDeclarationProvider=b["a"].hasDeclarationProvider.bindTo(i),o._hasImplementationProvider=b["a"].hasImplementationProvider.bindTo(i),o._hasTypeDefinitionProvider=b["a"].hasTypeDefinitionProvider.bindTo(i),o._hasHoverProvider=b["a"].hasHoverProvider.bindTo(i),o._hasDocumentHighlightProvider=b["a"].hasDocumentHighlightProvider.bindTo(i),o._hasDocumentSymbolProvider=b["a"].hasDocumentSymbolProvider.bindTo(i),o._hasReferenceProvider=b["a"].hasReferenceProvider.bindTo(i),o._hasRenameProvider=b["a"].hasRenameProvider.bindTo(i),o._hasSignatureHelpProvider=b["a"].hasSignatureHelpProvider.bindTo(i),o._hasDocumentFormattingProvider=b["a"].hasDocumentFormattingProvider.bindTo(i),o._hasDocumentSelectionFormattingProvider=b["a"].hasDocumentSelectionFormattingProvider.bindTo(i),o._hasMultipleDocumentFormattingProvider=b["a"].hasMultipleDocumentFormattingProvider.bindTo(i),o._hasMultipleDocumentSelectionFormattingProvider=b["a"].hasMultipleDocumentSelectionFormattingProvider.bindTo(i),o._isInWalkThrough=b["a"].isInEmbeddedEditor.bindTo(i);var n=function(){return o._update()};return o._register(t.onDidChangeModel(n)),o._register(t.onDidChangeModelLanguage(n)),o._register(C["d"].onDidChange(n)),o._register(C["a"].onDidChange(n)),o._register(C["b"].onDidChange(n)),o._register(C["f"].onDidChange(n)),o._register(C["e"].onDidChange(n)),o._register(C["q"].onDidChange(n)),o._register(C["C"].onDidChange(n)),o._register(C["p"].onDidChange(n)),o._register(C["i"].onDidChange(n)),o._register(C["m"].onDidChange(n)),o._register(C["u"].onDidChange(n)),o._register(C["v"].onDidChange(n)),o._register(C["g"].onDidChange(n)),o._register(C["j"].onDidChange(n)),o._register(C["x"].onDidChange(n)),n(),o}return W(t,e),t.prototype.dispose=function(){e.prototype.dispose.call(this)},t.prototype.reset=function(){var e=this;this._contextKeyService.bufferChangeEvents((function(){e._langId.reset(),e._hasCompletionItemProvider.reset(),e._hasCodeActionsProvider.reset(),e._hasCodeLensProvider.reset(),e._hasDefinitionProvider.reset(),e._hasDeclarationProvider.reset(),e._hasImplementationProvider.reset(),e._hasTypeDefinitionProvider.reset(),e._hasHoverProvider.reset(),e._hasDocumentHighlightProvider.reset(),e._hasDocumentSymbolProvider.reset(),e._hasReferenceProvider.reset(),e._hasRenameProvider.reset(),e._hasDocumentFormattingProvider.reset(),e._hasDocumentSelectionFormattingProvider.reset(),e._hasSignatureHelpProvider.reset(),e._isInWalkThrough.reset()}))},t.prototype._update=function(){var e=this,t=this._editor.getModel();t?this._contextKeyService.bufferChangeEvents((function(){e._langId.set(t.getLanguageIdentifier().language),e._hasCompletionItemProvider.set(C["d"].has(t)),e._hasCodeActionsProvider.set(C["a"].has(t)),e._hasCodeLensProvider.set(C["b"].has(t)),e._hasDefinitionProvider.set(C["f"].has(t)),e._hasDeclarationProvider.set(C["e"].has(t)),e._hasImplementationProvider.set(C["q"].has(t)),e._hasTypeDefinitionProvider.set(C["C"].has(t)),e._hasHoverProvider.set(C["p"].has(t)),e._hasDocumentHighlightProvider.set(C["i"].has(t)),e._hasDocumentSymbolProvider.set(C["m"].has(t)),e._hasReferenceProvider.set(C["u"].has(t)),e._hasRenameProvider.set(C["v"].has(t)),e._hasSignatureHelpProvider.set(C["x"].has(t)),e._hasDocumentFormattingProvider.set(C["g"].has(t)||C["j"].has(t)),e._hasDocumentSelectionFormattingProvider.set(C["j"].has(t)),e._hasMultipleDocumentFormattingProvider.set(C["g"].all(t).length+C["j"].all(t).length>1),e._hasMultipleDocumentSelectionFormattingProvider.set(C["j"].all(t).length>1),e._isInWalkThrough.set(t.uri.scheme===d["b"].walkThroughSnippet)})):this.reset()},t}(s["a"]),q=function(e){function t(t){var i=e.call(this)||this;return i._onChange=i._register(new a["a"]),i.onChange=i._onChange.event,i._hasFocus=!1,i._domFocusTracker=i._register(n["Z"](t)),i._register(i._domFocusTracker.onDidFocus((function(){i._hasFocus=!0,i._onChange.fire(void 0)}))),i._register(i._domFocusTracker.onDidBlur((function(){i._hasFocus=!1,i._onChange.fire(void 0)}))),i}return W(t,e),t.prototype.hasFocus=function(){return this._hasFocus},t}(s["a"]),K=encodeURIComponent("<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 3' enable-background='new 0 0 6 3' height='3' width='6'><g fill='"),G=encodeURIComponent("'><polygon points='5.5,0 2.5,3 1.1,3 4.1,0'/><polygon points='4,0 6,2 6,0.6 5.4,0'/><polygon points='0,2 1,3 2.4,3 0,0.6'/></g></svg>");function X(e){return K+encodeURIComponent(e.toString())+G}var J=encodeURIComponent('<svg xmlns="http://www.w3.org/2000/svg" height="3" width="12"><g fill="'),Y=encodeURIComponent('"><circle cx="1" cy="1" r="1"/><circle cx="5" cy="1" r="1"/><circle cx="9" cy="1" r="1"/></g></svg>');function Q(e){return J+encodeURIComponent(e.toString())+Y}Object(T["e"])((function(e,t){var i=e.getColor(S["p"]);i&&t.addRule(".monaco-editor .squiggly-error { border-bottom: 4px double "+i+"; }");var o=e.getColor(S["q"]);o&&t.addRule('.monaco-editor .squiggly-error { background: url("data:image/svg+xml,'+X(o)+'") repeat-x bottom left; }');var n=e.getColor(S["O"]);n&&t.addRule(".monaco-editor .squiggly-warning { border-bottom: 4px double "+n+"; }");var r=e.getColor(S["P"]);r&&t.addRule('.monaco-editor .squiggly-warning { background: url("data:image/svg+xml,'+X(r)+'") repeat-x bottom left; }');var a=e.getColor(S["G"]);a&&t.addRule(".monaco-editor .squiggly-info { border-bottom: 4px double "+a+"; }");var s=e.getColor(S["H"]);s&&t.addRule('.monaco-editor .squiggly-info { background: url("data:image/svg+xml,'+X(s)+'") repeat-x bottom left; }');var d=e.getColor(S["y"]);d&&t.addRule(".monaco-editor .squiggly-hint { border-bottom: 2px dotted "+d+"; }");var l=e.getColor(S["z"]);l&&t.addRule('.monaco-editor .squiggly-hint { background: url("data:image/svg+xml,'+Q(l)+'") no-repeat bottom left; }');var u=e.getColor(E["o"]);u&&t.addRule(".monaco-editor.showUnused .squiggly-inline-unnecessary { opacity: "+u.rgba.a+"; }");var h=e.getColor(E["n"]);h&&t.addRule(".monaco-editor.showUnused .squiggly-unnecessary { border-bottom: 2px dashed "+h+"; }");var c=e.getColor(S["x"])||"inherit";t.addRule(".monaco-editor .squiggly-inline-deprecated { text-decoration: line-through; text-decoration-color: "+c+"}")}))},d0b6:function(e,t,i){"use strict";i.d(t,"a",(function(){return u}));var o=i("1569"),n=i("308f"),r=i("a666"),a=i("aa3d"),s=i("6a89"),d=function(){var e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])},e(t,i)};return function(t,i){function o(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(o.prototype=i.prototype,new o)}}(),l={followsCaret:!0,ignoreCharChanges:!0,alwaysRevealFirst:!0},u=function(e){function t(t,i){void 0===i&&(i={});var o=e.call(this)||this;return o._onDidUpdate=o._register(new n["a"]),o._editor=t,o._options=a["g"](i,l,!1),o.disposed=!1,o.nextIdx=-1,o.ranges=[],o.ignoreSelectionChange=!1,o.revealFirst=Boolean(o._options.alwaysRevealFirst),o._register(o._editor.onDidDispose((function(){return o.dispose()}))),o._register(o._editor.onDidUpdateDiff((function(){return o._onDiffUpdated()}))),o._options.followsCaret&&o._register(o._editor.getModifiedEditor().onDidChangeCursorPosition((function(e){o.ignoreSelectionChange||(o.nextIdx=-1)}))),o._options.alwaysRevealFirst&&o._register(o._editor.getModifiedEditor().onDidChangeModel((function(e){o.revealFirst=!0}))),o._init(),o}return d(t,e),t.prototype._init=function(){this._editor.getLineChanges()},t.prototype._onDiffUpdated=function(){this._init(),this._compute(this._editor.getLineChanges()),this.revealFirst&&null!==this._editor.getLineChanges()&&(this.revealFirst=!1,this.nextIdx=-1,this.next(1))},t.prototype._compute=function(e){var t=this;this.ranges=[],e&&e.forEach((function(e){!t._options.ignoreCharChanges&&e.charChanges?e.charChanges.forEach((function(e){t.ranges.push({rhs:!0,range:new s["a"](e.modifiedStartLineNumber,e.modifiedStartColumn,e.modifiedEndLineNumber,e.modifiedEndColumn)})})):t.ranges.push({rhs:!0,range:new s["a"](e.modifiedStartLineNumber,1,e.modifiedStartLineNumber,1)})})),this.ranges.sort((function(e,t){return e.range.getStartPosition().isBeforeOrEqual(t.range.getStartPosition())?-1:t.range.getStartPosition().isBeforeOrEqual(e.range.getStartPosition())?1:0})),this._onDidUpdate.fire(this)},t.prototype._initIdx=function(e){var t=!1,i=this._editor.getPosition();if(i){for(var o=0,n=this.ranges.length;o<n&&!t;o++){var r=this.ranges[o].range;i.isBeforeOrEqual(r.getStartPosition())&&(this.nextIdx=o+(e?0:-1),t=!0)}t||(this.nextIdx=e?0:this.ranges.length-1),this.nextIdx<0&&(this.nextIdx=this.ranges.length-1)}else this.nextIdx=0},t.prototype._move=function(e,t){if(o["a"](!this.disposed,"Illegal State - diff navigator has been disposed"),this.canNavigate()){-1===this.nextIdx?this._initIdx(e):e?(this.nextIdx+=1,this.nextIdx>=this.ranges.length&&(this.nextIdx=0)):(this.nextIdx-=1,this.nextIdx<0&&(this.nextIdx=this.ranges.length-1));var i=this.ranges[this.nextIdx];this.ignoreSelectionChange=!0;try{var n=i.range.getStartPosition();this._editor.setPosition(n),this._editor.revealPositionInCenter(n,t)}finally{this.ignoreSelectionChange=!1}}},t.prototype.canNavigate=function(){return this.ranges&&this.ranges.length>0},t.prototype.next=function(e){void 0===e&&(e=0),this._move(!0,e)},t.prototype.previous=function(e){void 0===e&&(e=0),this._move(!1,e)},t.prototype.dispose=function(){e.prototype.dispose.call(this),this.ranges=[],this.disposed=!0},t}(r["a"])},d379:function(e,t,i){"use strict";i.d(t,"a",(function(){return m}));var o=i("aa3d"),n=i("5717"),r=i("9c1d"),a=i("9e74"),s=i("4fc3"),d=i("0a0f"),l=i("b0cd"),u=i("b7d0"),h=i("4779"),c=function(){var e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])},e(t,i)};return function(t,i){function o(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(o.prototype=i.prototype,new o)}}(),f=function(e,t,i,o){var n,r=arguments.length,a=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,i):o;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)a=Reflect.decorate(e,t,i,o);else for(var s=e.length-1;s>=0;s--)(n=e[s])&&(a=(r<3?n(a):r>3?n(t,i,a):n(t,i))||a);return r>3&&a&&Object.defineProperty(t,i,a),a},g=function(e,t){return function(i,o){t(i,o,e)}},m=function(e){function t(t,i,o,n,r,a,s,d,l,u){var h=e.call(this,t,o.getRawOptions(),{},n,r,a,s,d,l,u)||this;return h._parentEditor=o,h._overwriteOptions=i,e.prototype.updateOptions.call(h,h._overwriteOptions),h._register(o.onDidChangeConfiguration((function(e){return h._onParentConfigurationChanged(e)}))),h}return c(t,e),t.prototype.getParentEditor=function(){return this._parentEditor},t.prototype._onParentConfigurationChanged=function(t){e.prototype.updateOptions.call(this,this._parentEditor.getRawOptions()),e.prototype.updateOptions.call(this,this._overwriteOptions)},t.prototype.updateOptions=function(t){o["g"](this._overwriteOptions,t,!0),e.prototype.updateOptions.call(this,this._overwriteOptions)},t=f([g(3,d["a"]),g(4,n["a"]),g(5,a["b"]),g(6,s["c"]),g(7,u["c"]),g(8,l["a"]),g(9,h["b"])],t),t}(r["a"])}}]);