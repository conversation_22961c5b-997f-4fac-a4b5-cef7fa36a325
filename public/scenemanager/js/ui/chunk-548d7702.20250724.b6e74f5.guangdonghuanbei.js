(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-548d7702"],{"059a":function(e,t,i){},"0616":function(e,t,i){"use strict";i("059a")},"07ac":function(e,t,i){var s=i("23e7"),o=i("6f53").values;s({target:"Object",stat:!0},{values:function(e){return o(e)}})},3738:function(e,t,i){},"5ae6":function(e,t,i){},8859:function(e,t,i){},"9a9c":function(e,t,i){"use strict";var s=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"dialog-wrap"},[i("div",{staticClass:"header"},[i("span",[e._v(" "+e._s(e.$t("dialog.coordinateImport.label"))+" "),i("el-tooltip",{attrs:{enterable:!1,effect:"dark",placement:"top"}},[i("div",{attrs:{slot:"content"},domProps:{innerHTML:e._s(e.$t("dialog.coordinateImport.tooltip"))},slot:"content"}),i("i",{staticClass:"outline-none el-input__icon el-icon-question"})])],1),i("span",{staticClass:"handle-box"},[i("i",{staticClass:"el-icon-close",on:{click:e.close}})])]),i("div",{staticClass:"content"},[i("div",{staticClass:"radio-tab"},[i("div",{staticClass:"margin-right-15"},[e._v(" "+e._s(e.$t("dialog.coordinateImport.label1"))+" "),i("el-tooltip",{attrs:{enterable:!1,effect:"dark",placement:"top"}},[i("div",{attrs:{slot:"content"},domProps:{innerHTML:e._s(e.$t("dialog.coordinateImport.tooltip1"))},slot:"content"}),i("i",{staticClass:"outline-none el-icon-question"})])],1),i("el-radio",{staticStyle:{color:"#FFF","font-size":"12px"},attrs:{label:"1"},model:{value:e.checkType,callback:function(t){e.checkType=t},expression:"checkType"}},[e._v(" "+e._s(e.$t("dialog.coordinateImport.label2"))+" ")]),i("el-radio",{staticStyle:{color:"#FFF","font-size":"12px"},attrs:{label:"2"},model:{value:e.checkType,callback:function(t){e.checkType=t},expression:"checkType"}},[e._v(" "+e._s(e.$t("dialog.coordinateImport.label3"))+" ")])],1),i("textarea",{directives:[{name:"model",rawName:"v-model",value:e.content,expression:"content"}],staticClass:"textarea",staticStyle:{resize:"none"},domProps:{value:e.content},on:{input:function(t){t.target.composing||(e.content=t.target.value)}}}),i("div",{staticClass:"btns"},[i("button",{staticClass:"btn cancel",on:{click:e.cancel}},[e._v(" "+e._s(e.$t("messageTips.cancel"))+" ")]),i("button",{staticClass:"btn confirm",on:{click:e.confirm}},[e._v(" "+e._s(e.$t("menuIconName.confirm"))+" ")])])])])},o=[],n=i("2909"),a=(i("a15b"),i("ac1f"),i("1276"),i("4de4"),i("d3b7"),i("d81d"),{name:"",data:function(){return{content:"",checkType:"1"}},methods:{cancel:function(){this.close()},close:function(){this.$emit("close")},confirm:function(){if(this.content){var e=this.content.split(/[\t\r\f\n\s]*/g).join(""),t=e.split(";");t=t.filter((function(e){return""!=e}));var i=t.map((function(e){return e=e.split(","),e}));2==this.checkType&&(i=i.map((function(e){var t,i=(t=window.scene.mv.tools.coordinate).mercator2vector.apply(t,Object(n["a"])(e));return[i.x,i.y,i.z]}))),this.$emit("confirm",i)}else this.$message({type:"info",message:this.$t("dialog.coordinateImport.message")})}},beforeDestroy:function(){this.content=""}}),r=a,c=(i("f6b8"),i("2877")),l=Object(c["a"])(r,s,o,!1,null,"03d405a4",null);t["a"]=l.exports},a8f3:function(e,t,i){"use strict";i("3738")},d0aa:function(e,t,i){"use strict";i.r(t);var s=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"topToolBarContainer"},[i("div",{staticClass:"tabWrap"},[e._l(e.navData,(function(t,s){return[i("div",{class:["tabItem",{active:e.active==s}],on:{click:function(t){return e.changeActiveType(s)}}},[i("CommonSVG",{attrs:{"icon-class":t.icon,size:16}}),i("span",{staticClass:"text"},[e._v(e._s(t.label))])],1)]}))],2),i("div",{class:["elements-menu-list",{"media-style":1!=e.active}]},e._l(e.childrenData,(function(t,s){return i("div",{key:t+s,staticClass:"menu-list-item"},[i("div",{class:t.layout},e._l(t.list,(function(s,o){return i("div",{key:o,staticClass:"list-item cursor-btn relative",class:[{extend:s.extend||s.importPosition},{disable:s.disable},{actived:(s.subType||s.type)===e.activedType}],on:{click:function(t){return e.handleElementsClick(s)}}},[s.thumb&&""!=s.thumb?i("img",{attrs:{src:s.thumb}}):i("CommonSVG",{attrs:{"icon-class":s.icon,size:t.size?t.size:20}}),i("span",[e._v(e._s(s.title))]),s.extend?i("i",{staticClass:"el-icon-caret-bottom el-icon cursor-btn",class:{disable:s.extend.disable},on:{click:function(t){return t.stopPropagation(),e.handleElementsClick(s.extend)}}}):e._e(),s.importPosition?i("i",{staticClass:"el-icon-caret-bottom el-icon cursor-btn"}):e._e(),s.children?i("div",{staticClass:"list-item-child"},[i("ul",e._l(s.children,(function(t,o){return i("li",{key:t+o,on:{click:function(i){return i.stopPropagation(),e.handleElementsClick(t,s)}}},[i("CommonSVG",{attrs:{"icon-class":t.icon,size:t.size?t.size:20}}),i("p",{staticClass:"margin-left-5"},[e._v(e._s(t.title))])],1)})),0)]):e._e()],1)})),0),t.afterLineX?i("div",{staticClass:"afterLineX"}):e._e()])})),0),e.showImportDialog?i("CoordinateImport",{on:{close:function(t){e.showImportDialog=!1},confirm:e.getPositionList}}):e._e(),e.showProjectorDialog?i("ProjectorDialog",{on:{confirm:e.addProjectorFeature,close:e.beforeAddProjectorFeature}}):e._e()],1)},o=[],n=(i("d3b7"),i("3ca3"),i("ddb0"),i("caad"),i("2532"),i("e9c4"),i("159b"),i("0e1b")),a=i("9a9c"),r=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("dialogComp",{ref:"linkView",staticClass:"sourceDom",attrs:{hideHeader:!1,needClose:"true",zIndex:"4",height:"180",position:"fixed",left:e.dialogLeft,top:0,right:0,bottom:0,title:e.$t("featureDatas.projector.name"),icon:"icon-details",width:390,type:"detailInfo"},on:{close:function(t){return e.closeDialog("cancel")}},scopedSlots:e._u([{key:"center",fn:function(){return[i("div",{staticClass:"projectorDialog-container"},[i("div",{staticClass:"content"},[0===e.step?i("el-input",{class:{"is-error":e.inputError},attrs:{placeholder:e.$t("dialog.projector.placeholder"),size:"small"},model:{value:e.url,callback:function(t){e.url="string"===typeof t?t.trim():t},expression:"url"}},[e.inputLoad?i("i",{staticClass:"el-input__icon el-icon-loading",attrs:{slot:"suffix"},slot:"suffix"}):e._e()]):e._e(),1===e.step?[i("el-radio",{attrs:{label:"camera"},on:{input:e.radioChange},model:{value:e.radio,callback:function(t){e.radio=t},expression:"radio"}},[e._v(" "+e._s(e.$t("dialog.projector.label"))+" ")]),i("el-radio",{attrs:{label:"click"},on:{input:e.radioChange},model:{value:e.radio,callback:function(t){e.radio=t},expression:"radio"}},[e._v(" "+e._s(e.$t("dialog.projector.label1"))+" ")])]:e._e()],2),i("div",{staticClass:"bottom-btn"},[0===e.step?i("span",{staticClass:"cursor-btn",on:{click:function(t){return e.handleStep("next")}}},[e._v(" "+e._s(e.$t("sceneMainMenu.redo.name"))+" ")]):e._e(),1===e.step?[i("span",{staticClass:"cursor-btn margin-right-8",on:{click:function(t){return e.handleStep("prev")}}},[e._v(" "+e._s(e.$t("sceneMainMenu.undo.name"))+" ")]),i("span",{staticClass:"cursor-btn confirm",on:{click:function(t){return e.confirm()}}},[e._v(" "+e._s(e.$t("menuIconName.confirm"))+" ")])]:e._e()],2)])]},proxy:!0}])})},c=[],l=i("c7eb"),d=i("1da1"),h=(i("07ac"),i("d81d"),i("a630"),i("b893")),u={name:"ProjectorDialog",data:function(){return{dialogLeft:0,inputError:!1,inputLoad:!1,step:0,radio:"camera",position:[0,0,0],rotation:[0,0,0],currentNotify:null,anchorID:"",url:""}},created:function(){this.getCurrentCameraPosition(),window.scene.mv.status.selectable=!1},methods:{getCurrentCameraPosition:function(){var e=window.scene.mv.tools.coordinate.realPosition(window.scene.mv.THREE_Camera.position.clone()),t=window.scene.mv.THREE_Camera.rotation.clone();e=JSON.parse(JSON.stringify(e)),this.position=Object.values(e);var i=[t.x,t.y,t.z];this.rotation=i.map((function(e){return e*(180/Math.PI)}))},handleStep:function(e){if("next"==e){this.inputLoad=!0;var t=this.validateUrl();this.inputLoad=!1,t&&(this.step+=1)}else this.step-=1},validateUrl:function(){return""!=this.url||(this.$message.error(this.$t("dialog.projector.message")),!1)},confirm:function(){var e=this;return Object(d["a"])(Object(l["a"])().mark((function t(){var i,s,o,n;return Object(l["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return i={elementIDs:[],position:e.position,rotation:e.rotation,fov:30,aspect:16/9,scale:1,helper:!0,video:!0},s=e.$loading({lock:!0,text:e.$t("others.prepare"),spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),t.next=4,Object(h["a"])(e.url);case 4:o=t.sent,i.video=!o,s.close(),i.elementIDs=Array.from(window.scene.selectedObjects.keys()),e.$emit("confirm",i,e.url),""!=e.anchorID&&(n=window.scene.features.get(e.anchorID),n.visible=!1,n.offset=[0,0,0]),e.closeDialog();case 11:case"end":return t.stop()}}),t)})))()},radioChange:function(e){"click"==e?(this.dialogLeft=9999,""==this.anchorID?this.checkedCoordinateAddFeature():(window.scene.features.get(this.anchorID).visible=!0,this.handleProjectorTransformChange()),document.addEventListener("keyup",this.onEscKeyUp)):(""!=this.anchorID&&(window.scene.features.get(this.anchorID).visible=!1),this.getCurrentCameraPosition())},checkedCoordinateAddFeature:function(){this.currentNotify=this.$notify({title:this.$t("messageTips.tipsTitle"),dangerouslyUseHTMLString:!0,message:this.$t("dialog.projector.message1"),duration:0}),window.scene.mv.events.singleSelection.on("default",this.onCheckedCoordinate)},onCheckedCoordinate:function(e){var t=this,i=[0,0,0],s=window.scene.mv._THREE;try{var o=window.scene.queryPosition(new s.Vector2(e.clientX,e.clientY));""!=o&&void 0!=o&&(i=window.scene.mv.tools.coordinate.vector2mercator(o),this.addTemporaryAnnotationFeature(i),window.scene.mv.events.singleSelection.off("default",this.onCheckedCoordinate),setTimeout((function(){t.handleProjectorTransformChange()}),300))}catch(n){console.error(n)}},handleProjectorTransformChange:function(){var e=window.scene.features.get(this.anchorID);this.onTransformChangeSetPosition(),window.scene.mv.tools.transform.currentMode="translate",window.scene.mv.tools.transform.active(e),window.scene.mv.events.transformChange.on("default",this.onProjectorTransformChange),window.scene.forceRepaint=!0},onTransformChangeSetPosition:function(){var e=window.scene.features.get(this.anchorID),t=window.scene.mv.tools.coordinate.mercator2vector(e.origin[0],e.origin[1],e.altitude);this.onProjectorTransformChange(t)},onProjectorTransformChange:function(e){e&&(this.position=Object.values(e))},addTemporaryAnnotationFeature:function(e){var t={position:{x:0,y:0,z:0},content:"",htmlCode:'<div class="triggerScatterPlot"></div>',cssCode:"",jsCode:"",minDistance:0,maxDistance:1/0},i=window.scene.addFeature("annotation","projector-annotation");this.anchorID=i.id,i.origin=[e[0],e[1]],i.altitude=e[2],i.offset=[0,0,0],i.dataKey="annotation-"+i.id,window.scene.postData(t,i.dataKey),i.load(),window.scene.render()},onEscKeyUp:function(e){13==e.keyCode&&"Enter"===e.key&&this.dialogLeft>0&&(window.scene.forceRepaint=!0,this.currentNotify&&(this.currentNotify.close(),this.currentNotify=null),this.dialogLeft=0,""==this.anchorID?(window.scene.mv.events.singleSelection.off("default",this.onCheckedCoordinate),this.radio="camera",this.radioChange("camera"),this.$message.error(this.$t("dialog.projector.message2"))):(window.scene.mv.tools.transform.deactive(),window.scene.mv.events.transformChange.off("default",this.onProjectorTransformChange)),document.removeEventListener("keyup",this.onEscKeyUp))},closeDialog:function(e){this.$emit("close",e)}},beforeDestroy:function(){window.scene.mv.status.selectable=!0}},m=u,g=(i("a8f3"),i("2877")),p=Object(g["a"])(m,r,c,!1,null,"27e226ed",null),v=p.exports,f=i("5a94"),w=i("f7fe"),y=i.n(w),b={name:"SceneTopElementsToolBar",mixins:[n["a"]],components:{CommonSVG:function(){return i.e("chunk-51f90655").then(i.bind(null,"17d0"))},CoordinateImport:a["a"],ProjectorDialog:v},data:function(){return{sceneLabelDatas:[],currentFreeSketch:"",active:0,showImportDialog:!1,showProjectorDialog:!1,messageObj:""}},computed:{bottomMenuActive:function(){return this.$store.state.menuList.bottomMenuActive},dragData:function(){return this.$store.state.scene.dragOverData},validActivedTypes:function(){var e;return null!==(e=this.$store.state.sceneSetting.validActivedTypes)&&void 0!==e?e:[]},navData:function(){return this.$store.state.sceneSetting.topElementNavData},childrenData:function(){return this.$store.state.sceneSetting.topElementNavData[this.active].children},activedType:function(){return this.$store.state.sceneSetting.activedType},menuActive:function(){return this.$store.state.menuList.menuActive}},watch:{},created:function(){this.setToolBarLayout=y()(this.setToolBarLayout,500),this.$bus.on("eventoff",this.eventLogoffBeforeSetting),this.setToolBarLayout(),window.addEventListener("resize",this.setToolBarLayout)},methods:{setToolBarLayout:function(){var e=window.document.documentElement.getBoundingClientRect().width,t={tabIndex:0,eq:3,layout:""};t.layout=e<=1340?"portrait":"normal",this.$store.commit("setToolBarLayout",t),Object(f["d"])()},getIsActived:function(e){var t,i=this.$store.state.sceneSetting.activedType;return!!e&&(null!==(t=e.subType)&&void 0!==t?t:e.type)===i},changeActiveType:function(e){this.getIfCanChangeActiveType()&&(this.active=e,1===e?this.$store.commit("setActivedType",""):(this.$store.commit("setValidActivedType","element"),this.$store.commit("setIsTimeLineVisible",!1)))},getIfCanChangeActiveType:function(){return!this.$store.state.animation.isTimeLineVisible||(this.$message(this.$t("messageTips.exitEditingAnimate")),!1)},getPositionList:function(e){window.scene.mv.tools.draw.active(),window.scene.mv.tools.draw.startDrawLine(e),window.scene.mv.tools.draw.drawMode=2,this.handleFreeSketchPolygon(),"pathAnimation"!==this.dragData.type&&window.scene.mv.tools.draw.deactive(),this.dragData.staticType=!0,this.$store.commit("saveDragOverData",this.dragData),this.$store.commit("toggleSettingActive",this.dragData.type),this.showImportDialog=!1},drawPoline:function(e,t){var i=e.type;this.$store.commit("saveDragOverData",t),this.$emit("topElementSelected",t),"draw"===i?this.freeSketchPolygon(t.freeSketch,t):this.showImportDialog=!0},handleElementsClick:function(e,t){if(window.offCheckedCoordinate&&window.offCheckedCoordinate(),window.offCheckedCoordinate&&(window.offCheckedCoordinate=null,delete window.offCheckedCoordinate),e.disable)return!1;if(""!=this.currentFreeSketch){var i=!1;if(this.activedType!=e.subType&&this.activedType!=e.type||this.currentFreeSketch!=e.freeSketch&&"checkedCoordinate"!=this.currentFreeSketch||(i=!0),this.eventLogoffBeforeSetting(),i)return}var s=this.bottomMenuActiveMessageTips();if(!s)return!1;if(this.menuActive.includes("renderSetting")&&(this.$store.commit("toggleActiveDialog","renderSetting"),this.$store.commit("toggleMenuActive","renderSetting")),this.menuActive.includes("boxSelection"))return this.$message({message:this.$t("messageTips.exitBoxSelection"),type:"info"}),!1;if(("viewpoint"==e.type||"markup"==e.type)&&!window.scene.inited)return this.$message.error(this.$t("messageTips.canvasNotInitialized")+e.title),!1;var o=["screening","viewpoint","markup","advanced_material","projector"];if(o.includes(e.type)||!this.$store.state.menuList.elementMenu)if(""==this.$store.state.widget.settingActive)if(this.$store.state.animation.isTimeLineVisible){var n=this.elMessageShow("isTimeLineVisible");n||this.$message(this.$t("messageTips.exitEditingAnimate"))}else if("import"!==e.type&&"draw"!==e.type){if(e.subType&&""!=e.subType&&"extend"==e.subType)return this.emitTopMenuExtendDatas(JSON.parse(JSON.stringify(e))),!1;var a=this.warnBeforeSetting(e);if(!a)return!1;if("pathAnimation"===e.type){var r=this.$store.state.pathAnimation.roamActive;if(r)return void this.$message(this.$t("messageTips.exitRoam"))}if(this.$store.commit("saveDragOverData",e),this.$emit("topElementSelected",e),e.firstHand)if(e.freeSketch&&""!=e.freeSketch)e.importPosition||this.freeSketchPolygon(e.freeSketch,e);else if(e.clickCoordinate)this.checkedCoordinateAddFeature();else{if("_3dBuilding"===e.type&&window.scene.findFeature("3DBuilding"))return void this.$notify({title:this.$t("featureDatas._3dBuilding.name"),message:this.$t("others.alreadyExists"),type:"info"});this.submitSetting()}if("sceneManage"==e.interlock&&("animation"===e.type&&this.$store.commit("setAnimationSubtype",e.subType),this.$emit("addElement",e.type)),e){var c=e.subType||e.type;this.validActivedTypes.includes(c)&&this.$store.commit("setValidActivedType",c),this.$store.commit("setActivedType",c)}"screening"!==e.type&&this.$emit("clearSelection")}else this.drawPoline(e,t);else{var l=this.elMessageShow(this.$store.state.widget.settingActive);l||this.$message(this.$t("messageTips.exitEditingStatus"))}else this.$message({message:this.$t("messageTips.exitElementMenu"),type:"info"})},emitTopMenuExtendDatas:function(e){window.dispatchEvent(new CustomEvent("onExtendTopMenuSelected",{detail:e}))},submitSetting:function(){var e=this,t=this.dragData.type,i=this.styleFormDatas.datas[t]&&this.styleFormDatas.datas[t].list;switch(t){case"_3dBuilding":this.add3DbuildingFeature(i);break;case"polygon":var s={color:"rgb(255, 50, 0)",opacity:1,top:"extrude"==this.dragData.subType?10:0,base:0,interval:600,direction:"x",showline:!1};"extrude"==this.dragData.subType&&(s.top=10,s.base=i[4].value),"excavation"!=this.dragData.subType&&"tileExcavation"!=this.dragData.subType||(s.top=5,s.base=i[4].value),"flatten"!=this.dragData.subType&&"tileFlatten"!=this.dragData.subType||(s.top=5,s.base=i[4].value),"water"!=this.dragData.subType&&"surface"!=this.dragData.subType&&"environment"!=this.dragData.subType||(s.base=i[4].value),"water"==this.dragData.subType&&(s.color="rgb(0,75,64)"),this.addPolygonFeature(i,s,{priority:0});break;case"ripplewall":var o=[];i[1].position.forEach((function(e,t){o.push({id:t+1,position:{x:e[0],y:e[1]}})})),i[1].units=o;var n={color:"rgb(255, 50, 0)",speed:Math.ceil(.015*33.33),opacity:1,height:10},a=window.scene.mv.tools.coordinate.vector2mercator(i[1].position[0]);this.styleFormDatas.altitude=a[2];var r=null;this.addRipplewallFeature(i,n,r,{priority:0});break;case"radar":var c={color:"rgb(255, 50, 0)",opacity:1,radius:5,speed:1,width:90};this.addRadarFeature(i,c,null,{priority:0});break;case"shield":var l={color:"rgb(255, 50, 0)",opacity:1,radius:5,speed:Math.ceil(6.666)};this.addShieldFeature(i,l,null,{priority:0});break;case"ring":var d={color:"rgb(255, 50, 0)",radius:5,speed:5};this.addRingFeature(i,d,null,{priority:0});break;case"flame":case"smoke":var h={width:10,height:30};this.addFlameFeature(h,{priority:0});break;case"polyline":var u={color:"rgb(255, 50, 0)",opacity:1,width:2,flow:!1,interval:1};this.addPolylineFeature(i,u,{priority:0});break;case"heatmap":var m={color:"rgb(255, 255, 255)",opacity:1,scaleZ:5,wireframe:!1,size:13,gradient:{.25:"rgb(0,0,255)",.55:"rgb(0,255,0)",.85:"rgb(255,255,0)",1:"rgb(255,0,0)"},pointsArray:[{x:0,y:0,value:0}]};this.addHeatmapFeature(i,m,null,{priority:0});break;case"annotation":return void this.addTemporaryAnnotationFeature(this.dragData);case"batch-extrude":this.addBatchExtrudeFeature2();break;case"trigger":var g=JSON.parse(JSON.stringify(this.dragData));if("sensor"==this.dragData.subType)g.sensorPosition=this.styleFormDatas.datas.trigger.list[1].position,g.icon="trigger_sensor_element";else{var p=window.scene.mv.tools.coordinate.mercator2vector(this.styleFormDatas.longitude,this.styleFormDatas.latitude,this.styleFormDatas.altitude);g.rangePosition=p,g.icon="trigger_range_advanced"}return this.$store.commit("saveDragOverData",g),void this.handleTopAdvancedEnvironmentSet(t);case"advanced_material":this.handleBeforeMaterialSetting();case"advanced_setting":case"advanced_envmap":return void this.handleTopAdvancedEnvironmentSet(t);case"pathAnimation":return void this.addPathAnimation(this.dragData,i);case"waterfall":var v={width:1,height:1,depth:1};this.addWaterfallFeature(v,{priority:0});break;case"projector":this.beforeAddProjectorFeature("cancel");break;case"snow":this.addSnowFeature({priority:0});break;case"tailline":var f={positions:[],color:"rgba(255, 50, 0)",tailMap:"",speed:1,reverse:!1,lineWidth:3};this.addTailLineFeature(i,f,{priority:0});break}setTimeout((function(){e.$deepUpdateScene(t),e.$emit("close"),window.scene.render()}),500)},onCheckedCoordinate:function(e){var t=[0,0,0],i=window.scene.mv._THREE;try{var s=window.scene.queryPosition(new i.Vector2(e.clientX,e.clientY));""!=s&&void 0!=s&&(t=window.scene.mv.tools.coordinate.vector2mercator(s))}catch(o){}this.styleFormDatas.longitude=t[0],this.styleFormDatas.latitude=t[1],this.styleFormDatas.altitude=t[2],this.eventLogoffBeforeSetting(),this.submitSetting()},checkedCoordinateAddFeature:function(){this.currentFreeSketch="checkedCoordinate",this.$store.commit("setElementsBarActivatedTool","checkedCoordinate"),window.scene.mv.status.selectable=!1,this.$message(this.$t("messageTips.publishSomething",{name:this.dragData.title})),window.scene.mv.events.singleSelection.on("default",this.onCheckedCoordinate),document.addEventListener("keyup",this.onEscKeyUp)},freeSketchPolygon:function(e,t){var i;if(window.scene.mv.tools.draw.active(),this.$store.commit("toogleDrawState",!0),"polygon"==e)window.scene.mv.tools.draw.allowConCavePolygon=!0,window.scene.mv.tools.draw.startDrawPolygon();else if("polyline"==e){window.scene.mv.tools.draw.startDrawLine();var s=this.$store.state.dialog.activeDialog;window.scene.mv.events.mousemove.on("default",this.getPosition),-1===s.indexOf("pathAnimation")&&this.$store.commit("toggleActiveDialog","pathAnimation")}else"rectangle"==e&&window.scene.mv.tools.draw.startDrawPolygon();var o=this.$store.state.sceneSetting.activedType;(null!==(i=t.subType)&&void 0!==i?i:t.type)!==o&&this.$message({showClose:!0,message:this.$t("messageTips.freeSketch.defaultMsg"),duration:7e3}),this.currentFreeSketch=e,this.$store.commit("setElementsBarActivatedTool",e),"rectangle"==e?window.scene.mv.tools.draw.finishDrawEvent.on("default",this.handleFreeSketchRectangle):window.scene.mv.tools.draw.finishDrawEvent.on("default",this.handleFreeSketchPolygon),document.addEventListener("keyup",this.onEscKeyUp)},getPosition:function(e){var t=window.scene.mv._THREE,i=window.scene.queryPosition(new t.Vector2(e.clientX,e.clientY));void 0==i&&(i={x:0,y:0,z:0}),this.$store.commit("setPathAnimationPosition",i)},handleFreeSketchPolygon:function(){var e=window.scene.mv.tools.draw.getAllData(),t=this.dragData.type,i=this.styleFormDatas.datas[t].list;if(this.$store.commit("toogleDrawState",!1),e.polygon.length>0){var s=e.polygon[0].points.length;if(s-1<=2)this.$message.error(this.$t("messageTips.freeSketch.errorMsg1"));else{window.scene.mv.tools.draw.allowConCavePolygon=!1;for(var o=[],n=0;n<s-1;n++)o[n]=[e.polygon[0].points[n].x,e.polygon[0].points[n].y,e.polygon[0].points[n].z];i[1].position=o;var a=["flatten","extrude","surface"];if("polygon"==this.dragData.type&&a.includes(this.dragData.subType)){var r=window.scene.mv.tools.coordinate.vector2mercator(e.polygon[0].points[0]);this.styleFormDatas.datas["polygon"].list[4].value=r[2]}this.submitSetting()}}else{var c=e.polyline[0].points.length;if(c<=1)this.$message.error(this.$t("messageTips.freeSketch.errorMsg2"));else{for(var l=[],d=0;d<c;d++)l[d]=[e.polyline[0].points[d].x,e.polyline[0].points[d].y,e.polyline[0].points[d].z];l=this.getFilteredPositions(l),i[1].position=l,this.submitSetting()}}this.eventLogoffBeforeSetting()},getFilteredPositions:function(e){for(var t,i,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{x:5e-4,y:5e-4,z:5e-4},o=[e[0]],n=e.length,a=o.length,r=s.x,c=s.y,l=s.z,d=1;d<n;d++)t=e[d],i=o[a-1],Math.abs(i[0]-t[0])<=r&&Math.abs(i[1]-t[1])<=c&&Math.abs(i[2]-t[2])<=l||(a=o.push(t));return o},onEscKeyUp:function(e){if(""==this.currentFreeSketch)return!1;27==e.keyCode&&"Escape"===e.key&&(this.$store.commit("toogleDrawState",!1),this.$store.commit("setActivedType",""),"pathAnimation"==this.dragData.type&&window.scene.mv.tools.draw.deactive(),this.eventLogoffBeforeSetting())},eventLogoffBeforeSetting:function(){if(this.currentFreeSketch){var e=this.dragData.type;switch(this.currentFreeSketch){case"rectangle":window.scene.mv.tools.draw.deactive(),window.scene.mv.tools.draw.finishDrawEvent.off("default",this.handleFreeSketchRectangle);break;case"polyline":case"polygon":if("pathAnimation"!==e&&window.scene.mv.tools.draw.deactive(),"pathAnimation"===e||"polyline"===e||"tailline"===e){window.scene.mv.events.mousemove.off("default",this.getPosition);var t=this.$store.state.dialog.activeDialog;-1!==t.indexOf("pathAnimation")&&this.$store.commit("toggleActiveDialog","pathAnimation")}window.scene.mv.tools.draw.finishDrawEvent.off("default",this.handleFreeSketchPolygon);break;case"checkedCoordinate":window.scene.mv.status.selectable=!0,window.scene.mv.events.singleSelection.off("default",this.onCheckedCoordinate);break}this.$store.commit("toogleDrawState",!1),this.$store.commit("setActivedType",""),document.removeEventListener("keyup",this.onEscKeyUp),this.currentFreeSketch="",this.$store.commit("setElementsBarActivatedTool","")}},warnBeforeSetting:function(e){var t=!0,i=e.type,s=["_3dBuilding","dem","wmts","video","wms","tms","shp","geoJSON","kml"],o=["excavation","flatten","demOpacity"];if(s.includes(i)){var n=window.scene.features.has("underlay"),a=this.elMessageShow(i);n||a||this.$message({message:this.$t("messageTips.necessaryUnderlay",{title:e.title}),type:"warning",showClose:!0,duration:8e3})}else if("polygon"==i&&o.includes(e.subType)){var r=window.scene.features.has("dem"),c=this.elMessageShow(e.subType);r||(c||this.$message({message:this.$t("messageTips.necessaryDem"),type:"warning",showClose:!0}),t=!1)}else if("projector"==i){var l=this.elMessageShow("projector");0===window.scene.selectedObjects.size&&(l||this.$message({message:this.$t("dialog.projector.message3"),type:"error"}),t=!1)}else if("screening"===i){var d=this.elMessageShow("screening"),h=window.scene.selectedObjects;0===h.size&&(d||this.$message({message:this.$t("messageTips.selectLeastOneElement"),type:"error",customClass:i}),t=!1)}return t},handleBeforeMaterialSetting:function(){this.$message({showClose:!0,message:this.$t("featureSetting.materialSetting.message"),type:"success",duration:5e3}),this.$store.commit("toggleActiveDialog","empty"),this.$store.commit("changeSceneManageMenuListParam",{name:"renderSetting",attr:"disable",value:!0,reverse:!0})},beforeAddProjectorFeature:function(e){this.showProjectorDialog=!this.showProjectorDialog,this.showProjectorDialog||(this.eventLogoffBeforeSetting(),window.scene.features.has("projector-annotation")&&"cancel"===e&&window.scene.features.get("projector-annotation").dispose())},bottomMenuActiveMessageTips:function(){if(document.querySelectorAll(".el-message").length&&""!=this.bottomMenuActive&&this.messageObj==this.bottomMenuActive)return!1;var e=!0,t={message:"",type:"info"};return"measure"===this.bottomMenuActive&&(t.message=this.$t("messageTips.exitMeasure"),e=!1),"analysis"===this.bottomMenuActive&&(t.message=this.$t("messageTips.exitAnalysis"),e=!1),"roam"===this.bottomMenuActive&&(t.message=this.$t("messageTips.exitRoam"),e=!1),"sectioning"!==this.bottomMenuActive&&"sectionPlaneing"!==this.bottomMenuActive||(t.message=this.$t("messageTips.exitSection"),e=!1),"markup"===this.bottomMenuActive&&(t.message=this.$t("messageTips.exitElementMenu"),e=!1),e||(this.$message(t),this.messageObj=this.bottomMenuActive),e},elMessageShow:function(e){var t=document.querySelectorAll(".el-message").length;return!(!t||this.messageObj!=e)||(this.messageObj=e,!1)}}},$=b,k=(i("d2d7"),i("0616"),Object(g["a"])($,s,o,!1,null,"0e45e07d",null));t["default"]=k.exports},d2d7:function(e,t,i){"use strict";i("5ae6")},f6b8:function(e,t,i){"use strict";i("8859")}}]);