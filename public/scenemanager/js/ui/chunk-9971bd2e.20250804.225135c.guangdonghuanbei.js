(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-9971bd2e"],{"4f62":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("dialogComp",{attrs:{hideHeader:!1,needClose:"true",zIndex:"4",draw:!1,right:60,drag:!0,title:e.$t("menuIconName.setUp"),icon:"icon-details",width:320,height:"80%",type:"detailInfo",top:160},on:{close:e.closeDialog},scopedSlots:e._u([e.isVothingScenemanager?null:{key:"center",fn:function(){return[a("div",{staticClass:"setup-container"},[a("div",{staticClass:"setup-top-bar"},e._l(e.tabList,(function(t,n){return a("div",{key:n+t.label,class:[{active:e.activeTab==t.label},{disable:t.disable},"setup-tab"],on:{click:function(a){return e.switchTab(t.label)}}},[e._v(" "+e._s(t.title)+" ")])})),0),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.isLoad,expression:"isLoad"}],staticStyle:{height:"100%"},attrs:{"element-loading-text":e.$t("others.dataLoading"),"element-loading-spinner":"el-icon-loading","element-loading-background":"rgba(0, 0, 0, 0.8)"}},[a("div",{directives:[{name:"show",rawName:"v-show",value:"markup"==e.activeTab,expression:"activeTab=='markup'"}],staticClass:"setup-content"},[e.viewpointBtnState?a("div",{staticClass:"add-btn cursor-btn",on:{click:function(t){return e.handleAddObject("markup")}}},[e._v(" "+e._s(e.$t("topToolBarMenu.advanced.children.markup.label"))+" "),a("CommonSVG",{attrs:{color:"#FFFFFF","icon-class":"add_feature",size:16}})],1):e._e(),e._l(e.markupDatas,(function(t,n){return a("div",{key:t.id+n,staticClass:"viewpoints-list",on:{click:function(a){return e.restoreData(t)}}},[t.default?a("div",{staticClass:"list-mark"},[e._v(" "+e._s(e.$t("others.default"))+" ")]):e._e(),a("img",{attrs:{src:t.thumbnail,alt:""}}),a("div",{staticClass:"list-bottom"},[a("span",{staticClass:"left-title"},[e._v(" "+e._s(t.name||e.$t("topToolBarMenu.advanced.children.markup.name")+n)+" ")]),e.viewpointBtnState?a("div",{staticClass:"right-menu"},[a("el-tooltip",{attrs:{effect:"dark",content:e.$t("menuIconName.delete"),placement:"top"}},[a("i",{staticClass:"el-icon-delete",on:{click:function(a){return a.stopPropagation(),e.handleListMenu(t,n,"delete")}}})])],1):e._e()])])}))],2),a("div",{directives:[{name:"show",rawName:"v-show",value:"viewpoint"==e.activeTab,expression:"activeTab=='viewpoint'"}],staticClass:"setup-content"},[e.viewpointBtnState?a("div",{staticClass:"add-btn cursor-btn",on:{click:function(t){return e.handleAddObject("viewpoint")}}},[e._v(" "+e._s(e.$t("topToolBarMenu.advanced.children.viewpoint.label"))+" "),a("CommonSVG",{attrs:{color:"#FFFFFF","icon-class":"add_feature",size:16}})],1):e._e(),e._l(e.viewpointDatas,(function(t,n){return a("div",{key:t.id+n,staticClass:"viewpoints-list",on:{click:function(a){return e.restoreData(t)}}},[t.default?a("div",{staticClass:"list-mark"},[e._v(" "+e._s(e.$t("others.default"))+" ")]):e._e(),a("img",{attrs:{src:t.thumbnail,alt:""}}),a("div",{staticClass:"list-bottom"},[a("span",{staticClass:"left-title"},[e._v(e._s(t.name||e.$t("topToolBarMenu.advanced.children.viewpoint.name")+n))]),e.viewpointBtnState?a("div",{staticClass:"right-menu"},[a("el-tooltip",{attrs:{effect:"dark",content:t.default?e.$t("topToolBarMenu.advanced.children.viewpoint.label1"):e.$t("topToolBarMenu.advanced.children.viewpoint.label2"),placement:"top"}},[a("i",{class:[{"color-409eff":t.default},"el-icon-s-home"],on:{click:function(a){return e.handleListMenu(t,n,"default")}}})]),a("el-tooltip",{attrs:{effect:"dark",content:e.$t("menuIconName.delete"),placement:"top"}},[a("i",{staticClass:"el-icon-delete",on:{click:function(a){return a.stopPropagation(),e.handleListMenu(t,n,"delete")}}})])],1):e._e()])])}))],2)])])]},proxy:!0}],null,!0)}),e.addFormDialog.dialogState?a("dialogComp",{attrs:{hideHeader:!1,needClose:"true",zIndex:"4",position:"fixed",draw:!1,top:0,right:0,bottom:0,left:0,drag:!1,title:"markup"==e.activeTab?e.$t("topToolBarMenu.advanced.children.markup.label"):e.$t("topToolBarMenu.advanced.children.viewpoint.label"),width:300,height:150,type:"detailInfo"},on:{close:e.closeAddDialog},scopedSlots:e._u([{key:"center",fn:function(){return[a("div",{staticClass:"add-container"},[a("el-input",{class:{"is-error":e.addFormDialog.inputError},attrs:{size:"small",placeholder:"markup"==e.activeTab?e.$t("topToolBarMenu.advanced.children.markup.placeholder"):e.$t("topToolBarMenu.advanced.children.viewpoint.placeholder")},scopedSlots:e._u([{key:"prepend",fn:function(){return[a("span",[e._v(" "+e._s("markup"==e.activeTab?e.$t("topToolBarMenu.advanced.children.markup.label1"):e.$t("topToolBarMenu.advanced.children.viewpoint.label3"))+" ")])]},proxy:!0}],null,!1,2716068270),model:{value:e.addFormDialog.name,callback:function(t){e.$set(e.addFormDialog,"name",t)},expression:"addFormDialog.name"}}),a("div",{staticClass:"bottom-btn"},[a("span",{staticClass:"cursor-btn cancel margin-right-8",on:{click:e.closeAddDialog}},[e._v(" "+e._s(e.$t("messageTips.cancel"))+" ")]),a("span",{staticClass:"cursor-btn confirm",on:{click:e.saveData}},[e._v(" "+e._s(e.$t("menuIconName.confirm"))+" ")])])],1)]},proxy:!0}],null,!1,3527205651)}):e._e(),a("transition",{attrs:{"enter-active-class":"animate__animated animate__fadeInUp"}},["setup"===e.bottomMenuActive?a("MarkupMenu",{on:{saveData:function(t){e.addFormDialog.dialogState=!0}}}):e._e()],1)],1)},i=[],o=a("c7eb"),s=a("1da1"),c=(a("d3b7"),a("3ca3"),a("ddb0"),a("c740"),a("b0c0"),a("a434"),a("159b"),{name:"MainSetUp",components:{CommonSVG:function(){return a.e("chunk-51f90655").then(a.bind(null,"17d0"))},MarkupMenu:function(){return a.e("chunk-43c5d891").then(a.bind(null,"f45e"))}},props:["isVothingScenemanager"],data:function(){return{tabList:[{title:this.$t("topToolBarMenu.advanced.children.markup.name"),label:"markup",icon:""},{title:this.$t("topToolBarMenu.advanced.children.viewpoint.name"),label:"viewpoint",icon:""},{title:this.$t("topToolBarMenu.advanced.children.pathAnimation.name"),label:"animate",icon:"",disable:!0},{title:this.$t("menuIconName.setUp"),label:"set",icon:"",disable:!0}],isLoad:!1,activeTab:"markup",markupDatas:[],viewpointDatas:[],addFormDialog:{menuState:!1,dialogState:!1,name:"",inputError:!1}}},mounted:function(){this.getSceneViewpoints()},methods:{handleListMenu:function(e,t,a){var n=this;switch(a){case"default":if("viewpoint"==this.activeTab){var i=this.viewpointDatas.findIndex((function(e){return!0===e.default}));i>=0&&(this.viewpointDatas[i].default=!1),e.default=!e.default}break;case"delete":this.$confirm(this.$t("messageTips.deleteSomething",{name:e.name}),this.$t("messageTips.tipsTitle"),{confirmButtonText:this.$t("menuIconName.confirm"),cancelButtonText:this.$t("messageTips.cancel"),type:"warning"}).then((function(){"viewpoint"==n.activeTab&&n.viewpointDatas.splice(t,1),"markup"==n.activeTab&&n.markupDatas.splice(t,1);var a=window.scene.viewpoints.findIndex((function(t){return t.id==e.id}));window.scene.viewpoints.splice(a,1),n.$message({type:"success",message:n.$t("messageTips.deleteSuccess")})})).catch((function(){}));break}},restoreData:function(e){window.scene.mv.tools.markup.clear(),"markup"==e.type?window.scene.restoreMarkup(e):window.scene.restoreViewpoint(e),window.scene.render()},getSceneViewpoints:function(){var e=this;this.markupDatas=[],this.viewpointDatas=[],window.scene.viewpoints.length>0&&window.scene.viewpoints.forEach((function(t){"markup"==t.type?e.markupDatas.unshift(t):e.viewpointDatas.unshift(t)}))},saveData:function(){var e=this;return Object(s["a"])(Object(o["a"])().mark((function t(){var a,n,i,s;return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(a=e.beforeValidate(),a){t.next=3;break}return t.abrupt("return",!1);case 3:if(n=e.$loading({lock:!0,text:e.$t("others.prepare"),spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),i=null,s="","markup"!==e.activeTab){t.next=13;break}return s=e.$t("topToolBarMenu.advanced.children.markup.name"),t.next=10,window.scene.snapMarkup().catch((function(t){n.close(),e.addFormDialog.dialogState=!1}));case 10:i=t.sent,t.next=17;break;case 13:return s=e.$t("topToolBarMenu.advanced.children.viewpoint.name"),t.next=16,window.scene.snapViewpoint().catch((function(t){n.close(),e.addFormDialog.dialogState=!1}));case 16:i=t.sent;case 17:i.name=e.addFormDialog.name,i.save(window.scene),e.$notify({title:s+" "+i.name,message:e.$t("others.added"),type:"success"}),n.close(),e.addFormDialog.dialogState=!1,e.addFormDialog.name="","markup"===e.activeTab&&e.$store.commit("toggleBottomMenuActive","setup"),e.getSceneViewpoints();case 25:case"end":return t.stop()}}),t)})))()},beforeValidate:function(){var e=this;if(0==window.scene.viewpoints.length)return!0;if(""==this.addFormDialog.name)return this.$message.error(this.$t("messageTips.nameNotEmpty")),this.addFormDialog.inputError=!0,!1;var t=window.scene.viewpoints.findIndex((function(t){return t.name==e.addFormDialog.name}));return!(t>=0)||(this.$message.error(this.addFormDialog.name+" "+this.$t("messageTips.nameAlreadyExists")),!1)},handleAddObject:function(e){switch(e){case"markup":this.$store.commit("toggleBottomMenuActive","setup");break;case"viewpoint":this.addFormDialog.dialogState=!0;break}},switchTab:function(e){if("animate"==e||"set"==e)return!1;this.activeTab=e,"setup"==this.bottomMenuActive&&this.$store.commit("toggleBottomMenuActive","setup")},closeDialog:function(){this.$store.commit("toggleActiveDialog","MainSetUp"),this.$store.commit("toggleMenuActive","setup"),"setup"==this.bottomMenuActive&&this.$store.commit("toggleBottomMenuActive","setup")},closeAddDialog:function(){this.addFormDialog.dialogState=!1,this.addFormDialog.name=""}},computed:{bottomMenuActive:function(){return this.$store.state.menuList.bottomMenuActive},viewpointBtnState:function(){return this.$store.state.menuList.sceneOtherMenuState.viewpoint}},beforeDestroy:function(){this.$store.commit("changeSceneManageMenuListParam",{name:"",attr:"disable",value:!1})}}),r=c,l=(a("764f"),a("2877")),d=Object(l["a"])(r,n,i,!1,null,"6ea81138",null);t["default"]=d.exports},"764f":function(e,t,a){"use strict";a("f56f")},f56f:function(e,t,a){}}]);