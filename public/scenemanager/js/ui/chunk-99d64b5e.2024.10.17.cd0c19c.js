(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-99d64b5e"],{"0089":function(t,e,a){t.exports=a.p+"static/img/鹅卵石-07.942692d4.png"},"0119":function(t,e,a){},"0188":function(t,e,a){t.exports=a.p+"static/img/地砖-03.a179e8d7.png"},"01de":function(t,e,a){t.exports=a.p+"static/img/沙地-01.e96b7274.png"},"04de":function(t,e,a){"use strict";a("0d69")},"05c2":function(t,e,a){t.exports=a.p+"static/img/meta-video.6f2f6a7d.png"},"0659":function(t,e,a){t.exports=a.p+"static/img/地砖-04.574bed93.png"},"066e":function(t,e,a){"use strict";a("12cd")},"0d5b":function(t,e,a){var i={"./压路机.png":"ee5f","./灌木.png":"d675","./白色轿车.png":"6ac8","./空调面板.png":"855e"};function n(t){var e=s(t);return a(e)}function s(t){if(!a.o(i,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return i[t]}n.keys=function(){return Object.keys(i)},n.resolve=s,t.exports=n,n.id="0d5b"},"0d69":function(t,e,a){},"0e1b":function(t,e,a){"use strict";var i=a("c7eb"),n=a("1da1"),s=(a("b0c0"),a("d81d"),a("d3b7"),a("159b"),a("99af"),a("e9c4"),a("b64b"),a("ac1f"),a("00b4"),a("caad"),a("cb29"),a("25f0"),a("a434"),a("1276"),a("a15b"),a("fb6a"),a("5319"),{data:function(){return{anchorExampleCss:"\n.anchor-point-panel {\n    position: relative;\n}\n.anchor-point-panel .container {\n  min-width: 200px;\n  border: 1px solid var(--theme);\n  padding: 4px 10px;\n  background-color: rgba(0, 0, 0,0.7);\n  overflow: hidden;\n  color: #FFFFFF;\n  position: absolute;\n  pointer-events: none;\n}\n.container .corner-marker {\n    position: absolute;\n    width: 15px;\n    height: 15px;\n    border: 1px solid #19B2FF;\n}\n.corner-marker.one {\n    top: 0;\n    left: 0;\n    border-right: none;\n    border-bottom: none;\n}\n.corner-marker.two {\n    top: 0;\n    right: 0;\n    border-left: none;\n    border-bottom: none;\n}\n.corner-marker.three {\n    bottom: 0;\n    left: 0;\n    border-right: none;\n    border-top: none;\n}\n.corner-marker.four {\n    bottom: 0;\n    right: 0;\n    border-left: none;\n    border-top: none;\n}\n.anchor-point-panel .panel-title {\n    position: relative;\n    height: 30px;\n    margin-bottom: 10px;\n    display: flex;\n    align-items: center;\n    overflow: hidden;\n    white-space: nowrap;\n    text-align: center;\n    background: linear-gradient(90deg, rgba(0,147,255,0) 0%, rgba(0,147,255,0.2) 49%, rgba(0,147,255,0) 100%);\n}\n.anchor-point-panel .panel-title:before {\n    content: '';\n    width: 50px;\n    height: 2px;\n    background: rgba(25,178,255,0.9);\n    border-radius: 0 0 2px 2px;\n    top: 0;\n    left: 50%;\n    position: absolute;\n    margin-left: -25px;\n}\n.anchor-point-panel .panel-title>span{\n    width: 100%;\n    font-size: 14px;\n}\n.anchor-point-panel .panel-close {\n  font-style: normal;\n  font-size: 24px;\n  cursor: pointer;\n  margin-top: -5px;\n  margin-left: 15px;\n  pointer-events: auto;\n}\n.anchor-point-panel .panel-close:before {\n  content: \"\\00D7\";\n}\n.anchor-point-panel .panel-close:hover {\nopacity: 0.8\n}\n.anchor-point-panel .container .item-list {\n  height: 24px;\n  display: flex;\n  justify-content: space-between;\n  flex-wrap: nowrap;\n  align-items: center;\n  white-space: nowrap;\n  margin-bottom: 2px;\n}\n.anchor-point-panel .container .item-list .desc {\n  margin-left: 15px;\n  color: #99FFFF;\n}\n.anchor-point-panel .panel-point-img img {\n    width: 100%;\n    height: 100%;\n}\n.anchor-point-panel.anchor-leadWire .panel-point-img:before,\n.anchor-point-panel.anchor-leadWire .panel-point-img:after {\n    content: '';\n}\n\n.anchor-point-panel.anchor-leadWire-default .container{\n    transform: translateX(-50%);\n    margin-left: 16px;\n    bottom:100%;\n}\n\n.anchor-point-panel.anchor-leadWire-left .container{\n    bottom:calc(100% + 8px);\n    right: 100%;\n    margin-right: 20px;\n}\n.anchor-point-panel.anchor-leadWire-left .panel-point-img:before {\n    width: 1px;\n    height: 35px;\n    background-color: var(--theme);\n    position: absolute;\n    top: -34px;\n    left: 50%;\n    border-top-right-radius: 10px;\n    transform: rotateZ(-20deg);\n    margin-left: -6px;\n}\n\n.anchor-point-panel.anchor-leadWire-left .panel-point-img:after {\n    width: 24px;\n    height: 1px;\n    background-color: var(--theme);\n    position: absolute;\n    top: -34px;\n    right: 50%;\n    margin-right: 12px;\n}\n\n.anchor-point-panel.anchor-leadWire-middle .container{\n    bottom:calc(100% + 20px);\n    transform: translateX(-50%);\n    margin-left: 16px;\n}\n.anchor-point-panel.anchor-leadWire-middle .panel-point-img:before {\n    width: 1px;\n    height: 20px;\n    background-color: var(--theme);\n    position: absolute;\n    top: -20px;\n    left: 50%;\n}\n\n.anchor-point-panel.anchor-leadWire-right .container{\n    bottom:calc(100% + 8px);\n    left: 100%;\n    margin-left: 20px;\n}\n.anchor-point-panel.anchor-leadWire-right .panel-point-img:before {\n    width: 1px;\n    height: 35px;\n    background-color: var(--theme);\n    position: absolute;\n    top: -34px;\n    left: 50%;\n    border-top-left-radius: 10px;\n    transform: rotateZ(20deg);\n    margin-left: 6px;\n}\n.anchor-point-panel.anchor-leadWire-right .panel-point-img:after {\n    width: 24px;\n    height: 1px;\n    background-color: var(--theme);\n    position: absolute;\n    top: -34px;\n    left: 50%;\n    margin-left: 12px;\n}",billboardExampleCss:"\n                .anchor-point-panel .container {\n                  min-width: 200px;\n                  border: 1px solid var(--theme);\n                  padding: 4px 10px;\n                  background-color: rgba(0, 0, 0,0.7);\n                  overflow: hidden;\n                  color: #FFFFFF;\n                  position: relative;\n                }\n                .container .corner-marker {\n                    position: absolute;\n                    width: 15px;\n                    height: 15px;\n                    border: 1px solid #19B2FF;\n                }\n                .corner-marker.one {\n                    top: 0;\n                    left: 0;\n                    border-right: none;\n                    border-bottom: none;\n                }\n                .corner-marker.two {\n                    top: 0;\n                    right: 0;\n                    border-left: none;\n                    border-bottom: none;\n                }\n                .corner-marker.three {\n                    bottom: 0;\n                    left: 0;\n                    border-right: none;\n                    border-top: none;\n                }\n                .corner-marker.four {\n                    bottom: 0;\n                    right: 0;\n                    border-left: none;\n                    border-top: none;\n                }\n                .anchor-point-panel .panel-title {\n                    position: relative;\n                    height: 30px;\n                    margin-bottom: 10px;\n                    display: flex;\n                    align-items: center;\n                    overflow: hidden;\n                    white-space: nowrap;\n                    text-align: center;\n                    background: linear-gradient(90deg, rgba(0,147,255,0) 0%, rgba(0,147,255,0.2) 49%, rgba(0,147,255,0) 100%);\n                }\n                .anchor-point-panel .panel-title:before {\n                    content: '';\n                    width: 50px;\n                    height: 2px;\n                    background: rgba(25,178,255,0.9);\n                    border-radius: 0 0 2px 2px;\n                    top: 0;\n                    left: 50%;\n                    position: absolute;\n                    margin-left: -25px;\n                }\n                .anchor-point-panel .panel-title>span{\n                    width: 100%;\n                    font-size: 14px;\n                }\n                .anchor-point-panel .container .item-list {\n                  height: 24px;\n                  display: flex;\n                  justify-content: space-between;\n                  flex-wrap: nowrap;\n                  align-items: center;\n                  white-space: nowrap;\n                }\n                .anchor-point-panel .container .item-list .title {\n                  color: #DDDDDD;\n                }\n                .anchor-point-panel .container .item-list .desc {\n                  margin-left: 15px;\n                  color: #99FFFF;\n                }\n                .anchor-point-panel.anchor-leadWire .panel-point-img:before,\n                .anchor-point-panel.anchor-leadWire .panel-point-img:after {\n                    content: '';\n                }\n                \n                .anchor-point-panel.anchor-leadWire-default .panel-point-img{\n                    width: 100%;\n                    text-align: center;\n                }\n                .anchor-point-panel.anchor-leadWire-left .panel-point-img{\n                    width: 100%;\n                    text-align: right;\n                    position: relative;\n                }\n                .anchor-point-panel.anchor-leadWire-default .container {\n                    transform: unset;\n                    margin-left: unset;\n                    bottom: unset;\n                }\n                .anchor-point-panel.anchor-leadWire-left .container{\n                    bottom: unset;\n                    right: unset;\n                    margin-right: 50px;\n                }\n                .anchor-point-panel.anchor-leadWire-left .panel-point-img:before {\n                    width: 1px;\n                    height: 35px;\n                    background-color: var(--theme);\n                    position: absolute;\n                    bottom: 100%;\n                    right: 20px;\n                    top: unset;\n                    left: unset;\n                    border-top-right-radius: 10px;\n                    transform: rotateZ(-20deg);\n                }\n                \n                .anchor-point-panel.anchor-leadWire-left .panel-point-img:after {\n                    width: 24px;\n                    height: 1px;\n                    background-color: var(--theme);\n                    position: absolute;\n                    top: -34px;\n                    right: 27px;\n                    margin-right: unset;\n                }\n                \n                .anchor-point-panel.anchor-leadWire-middle .container{\n                    margin-bottom: 20px;\n                    bottom: unset;\n                    transform: unset;\n                    margin-left: unset;\n                }\n                .anchor-point-panel.anchor-leadWire-middle .panel-point-img{\n                    width: 100%;\n                    text-align: center;\n                    position: relative;\n                }\n                .anchor-point-panel.anchor-leadWire-middle .panel-point-img:before {\n                    width: 1px;\n                    height: 20px;\n                    background-color: var(--theme);\n                    position: absolute;\n                    bottom: 100%;\n                    left: 50%;\n                }\n                \n                .anchor-point-panel.anchor-leadWire-right .container{\n                    bottom: unset;\n                    left: unset;\n                    margin-left: 50px;\n                }\n                .anchor-point-panel.anchor-leadWire-right .panel-point-img{\n                    width: 100%;\n                    text-align: left;\n                    position: relative;\n                }\n                .anchor-point-panel.anchor-leadWire-right .panel-point-img:before {\n                    width: 1px;\n                    height: 35px;\n                    background-color: var(--theme);\n                    position: absolute;\n                    bottom: 100%;\n                    left: 20px;\n                    border-top-left-radius: 10px;\n                    transform: rotateZ(20deg);\n                    top: unset;\n                    margin-left: unset;\n                }\n                .anchor-point-panel.anchor-leadWire-right .panel-point-img:after {\n                    width: 24px;\n                    height: 1px;\n                    background-color: var(--theme);\n                    position: absolute;\n                    top: -34px;\n                    left: 26px;\n                    margin-left: unset;\n                }"}},created:function(){this.setPanelTemplateScene()},methods:{setBasicAnchorImage:function(t){var e="",a=0==t[6].annotationRadio?"annotation":"billboard";return this.styleFormDatas.datas[a].setType.checkedImg=t[5].checkedType,0==parseInt(t[5].checkedType)&&(this.styleFormDatas.datas[a].setType.customSrc=t[5].fileSrc),-1!=parseInt(t[5].checkedType)&&(e=t[5].fileSrc),e},setCustomAnchorType:function(t){var e=this.dragData.type;0==t?this.$set(this.styleFormDatas.datas[e].list[4],"optionState",!1):this.$set(this.styleFormDatas.datas[e].list[4],"optionState",!0)},setAnchorPanelLeadWireType:function(t,e){this.$set(this.styleFormDatas.datas[e].list[5],"optionState",t)},setDefaultVideoAnchorHTML:function(t,e,a){var i=this.setLeadWireDataAnchorCls(t),n=this.setBasicAnchorImage(t),s="width:".concat(t[1].width+"px",";height:").concat(t[1].height+"px",";"),o="",r="",l="";""!=n?(o='<img class="point-img" src="'.concat(n,'">'),r='<i class="panel-close"></i>'):s="width:".concat(t[1].width+"px",";height:0};"),""!=n&&"alwaysShow"!=e.annotation.panelToggleMode&&"mouseover"!=e.annotation.panelToggleMode||(r='<i class="panel-close" style="display: none"></i>'),window.myVideo&&window.myVideo[this.dragData.id]&&window.myVideoId===a&&(window.myVideo[this.dragData.id]&&window.myVideo[this.dragData.id].pause(),window.myVideo[this.dragData.id]&&window.myVideo[this.dragData.id].dispose());var c=/\.(m3u8|hls|mp4)$/;l=c.test(e.annotation.videoLink)?e.annotation.videoLink.indexOf(".m3u8")>-1?'\n                    <video id="myVideo-'.concat(a,'" class="video-js vjs-default-skin vjs-big-play-centered" loop="loop" controls preload="auto" muted="\'muted" width="240" style="width: 240px" data-setup=\'{"autoplay": true}\'> \n                        <source id="source" src="').concat(e.annotation.videoLink,"\" type='application/x-mpegURL'></source>\n                    </video>\n                    "):'\n                    <video id="myVideo-'.concat(a,'" class="video-js vjs-default-skin vjs-big-play-centered" loop="loop" controls preload="auto" muted="\'muted" width="240" style="width: 240px" data-setup=\'{"autoplay": true}\'> \n                        <source id="source" src="').concat(e.annotation.videoLink,'"></source>\n                    </video>\n                    '):'<iframe width="240" class="video-iframe" style="border:none;" src="'.concat(e.annotation.videoLink,'">\n                </iframe>');var d="margin-left:".concat(t[1].width/2,"px;");if("alwaysShow"!==e.annotation.panelToggleMode){d+="display:none";var p=i;i=p.split(" ").splice(1).toString()}var u='<div class="anchor-point-panel '.concat(i,'" style="').concat(s,'">\n                                    <div class="container" style="pointer-events:auto;').concat(d,'">\n                                        <div class="panel-title" style="justify-content:center;margin-bottom:0;">\n                                            <span style="margin-top:5px;">').concat(e.annotation.title,"</span>\n                                            ").concat(r,"\n                                        </div>\n                                        <div>\n                                            ").concat(l,'\n                                        </div>\n                                        <div>\n                                            <span class="corner-marker one"></span>\n                                            <span class="corner-marker two"></span>\n                                            <span class="corner-marker three"></span>\n                                            <span class="corner-marker four"></span>\n                                        </div>\n                                    </div>\n                                    <div class="panel-point-img" style="').concat(s,'">\n                                        ').concat(o,"\n                                    </div>\n                                </div>");return u},setDefaultDataAnchorHTML:function(t,e){var a=this.setLeadWireDataAnchorCls(t),i=this.setBasicAnchorImage(t),n=null,s="",o="";""!=i&&(s='<img class="point-img" src="'.concat(i,'">'),o='<i class="panel-close"></i>'),""!=i&&"alwaysShow"!=e.annotation.panelToggleMode&&"mouseover"!=e.annotation.panelToggleMode||(o='<i class="panel-close" style="display: none"></i>');var r="margin-left:".concat(t[1].width/2,"px;");if("alwaysShow"!==e.annotation.panelToggleMode){r+="display:none;";var l=a;a=l.split(" ").splice(1).toString()}return r+="width: 260px;",n='<div class="anchor-point-panel '.concat(a,'" style="${anchorStyle}">\n                                    <div class="container" style="').concat(r,'">\n                                        <div class="panel-title">\n                                            <span>${panelTitle}</span>\n                                            ').concat(o,'\n                                        </div>\n                                        <loop></loop>\n                                        <div>\n                                            <span class="corner-marker one"></span>\n                                            <span class="corner-marker two"></span>\n                                            <span class="corner-marker three"></span>\n                                            <span class="corner-marker four"></span>\n                                        </div>\n                                    </div>\n                                    <div class="panel-point-img" style="${anchorStyle}">\n                                        ').concat(s,"\n                                    </div>\n                                </div>"),n},setDefaultDataBillboardHTML:function(t){var e=this.setLeadWireDataAnchorCls(t),a=this.setBasicAnchorImage(t),i="";""!=a&&(i='<img class="point-img" src="'.concat(a,'" style="${anchorStyle}">'));var n='<div class="anchor-point-panel '.concat(e,'">\n                                <div class="container">\n                                    <div class="panel-title">\n                                        <span>${panelTitle}</span>\n                                    </div>\n                                    <loop></loop>\n                                    <div>\n                                        <span class="corner-marker one"></span>\n                                        <span class="corner-marker two"></span>\n                                        <span class="corner-marker three"></span>\n                                        <span class="corner-marker four"></span>\n                                    </div>\n                                </div>\n                                <div class="panel-point-img">\n                                    ').concat(i,"\n                                </div>\n                            </div>");return n},setLeadWireDataAnchorCls:function(t){var e="anchor-leadWire ";switch(parseInt(t[4].checkedType)){case 0:e+="anchor-leadWire-default";break;case 1:e+="anchor-leadWire-left";break;case 2:e+="anchor-leadWire-middle";break;case 3:e+="anchor-leadWire-right";break}return e},setDefaultDataAnchorCss:function(t,e,a){var i=document.querySelector("."+e[0]);if(null!=i)return!1;if(""!=a){var n=document.createTextNode(a),s=document.createElement("style"),o=document.querySelector("head");s.id="annotation-css-"+t,s.classList=e.join(" "),s.type="text/css",s.appendChild(n),o.appendChild(s)}},setCustomDataAnchorHTML:function(t,e,a){var i=a,n=i.indexOf("<loop>"),s=i.indexOf("</loop>"),o=i.slice(n,s+7),r="";e.dataList.forEach((function(t){var e="",a="";"img"===t.valType?(e='<img width="100%" height="100%" src="'.concat(t.value,'" alt="">'),a="height:auto;"):e="link"===t.valType?'<a href="'.concat(t.value,'" target="_blank">点击查看</a>'):t.value,r+='<div class="item-list" style="'.concat(a,'">\n                                <span class="title">').concat(t.key,'</span>\n                                <span class="desc ').concat(t.valType,'">\n                                    ').concat(e,"\n                                </span>\n                            </div>")}));var l="width:30px;height:30px;",c=i.replace(o,r),d=new Function("panelTitle","anchorStyle","return `".concat(c,"`;"))(e.title,l);return d},setPanelTemplateScene:function(){var t=document.querySelector("#default-panel-template-scene");if(null!=t)return!1;var e=document.createElement("template");e.id="default-panel-template-scene",e.innerHTML='<div class="anchor-point-panel" style="&dollar;{anchorStyle}">\n            <div class="container">\n                <div class="panel-title">\n                    <span>&dollar;{panelTitle}</span>\n                    <i class="panel-close"></i>\n                </div>\n                <loop>\n                    <div class="item-list">\n                        <span class="title">&dollar;{loop.key}</span>\n                        <span class="desc">&dollar;{loop.value}</span>\n                    </div>\n                </loop>\n                    <div>\n                        <span class="corner-marker one"></span>\n                        <span class="corner-marker two"></span>\n                        <span class="corner-marker three"></span>\n                        <span class="corner-marker four"></span>\n                    </div>\n            </div>\n            <div style="&dollar;{anchorStyle}">\n                <img class="point-img" src="http://www.probim.cn:8088/bimexample/img/point.png">\n            </div>\n        </div>',document.body.appendChild(e)},setDataAnnotationDefaultEvent:function(t){var e="",a="";return t.annotation.panelToggleMode&&"alwaysShow"!=t.annotation.panelToggleMode&&("mouseover"===t.annotation.panelToggleMode?e='annotation.addEventListener("mouseover",e=>{if(e.target.className=="point-img"){annotation.querySelector(".container").style.display="block";annotation.firstChild.classList.add("anchor-leadWire");}});annotation.addEventListener("mouseleave",e=>{annotation.querySelector(".container").style.display="none";annotation.firstChild.classList.remove("anchor-leadWire");});':(e='annotation.addEventListener("'+t.annotation.panelToggleMode+'",e=>{if(e.target.className=="point-img"){annotation.querySelector(".container").style.display="block";annotation.firstChild.classList.add("anchor-leadWire");}});',a='annotation.querySelector(".panel-close").addEventListener("click",e=>{e.stopPropagation();annotation.querySelector(".container").style.display="none";annotation.firstChild.classList.remove("anchor-leadWire");});')),e+='annotation.addEventListener("mouseover",()=>{annotation.style.zIndex=99});annotation.addEventListener("mouseleave",()=>{annotation.style.zIndex=9});',e+a},setHTML:function(t,e){var a=t;if(null!=e&&void 0!=e&&""!=e){var i=t,n=i.indexOf("<loop>"),s=i.indexOf("</loop>"),o=i.slice(n,s+7),r=o.substring(6,o.length-7),l=new Function("data","return data.map((loop,index)=>{return `".concat(r,'`}).join("")'))(e.dataList),c="width:".concat(e.width+"px",";height:").concat(e.height+"px",";"),d=i.replace(o,l);a=new Function("panelTitle","anchorStyle","return `".concat(d,"`;"))(e.title,c)}var p=(new DOMParser).parseFromString(a,"text/html"),u=p.body.firstChild;return u}}}),o=a("ed08"),r=a("b893"),l=a("5a94");e["a"]={mixins:[s],data:function(){return{styleFormDatas:{id:"",thumn:"",dataType:"",typeName:"",name:"",longitude:0,latitude:0,altitude:0,rotation:[0,0,0],offset:[0,0,0],validate:{longitude:!0,latitude:!0,altitude:!0,rotation:!0},datas:{model:{type:"model",name:this.$t("featureDatas.model.name"),list:[{optionState:!0,phaseValue:-1,phaseOption:[{options:[]},{options:[]}]},{title:this.$t("featureSetting.advanced.model.label"),setOptions:[{title:this.$t("featureSetting.advanced.model.label1"),state:!1,attr:"space"},{title:this.$t("featureSetting.advanced.model.label2"),state:!1,attr:"area"},{title:this.$t("featureSetting.advanced.model.label3"),state:!1,attr:"part"},{title:this.$t("featureSetting.advanced.model.label4"),state:!0,attr:"texture"}],optionState:!0}]},dem:{type:"dem",name:this.$t("featureDatas.dem.name"),list:[{title:this.$t("formRelational.address.label"),name:"",value:"mapbox://mapbox.mapbox-terrain-dem-v1",defaultAddress:"mapbox://mapbox.mapbox-terrain-dem-v1",optionState:!0},{title:this.$t("formRelational.opacity.label"),value:1,optionState:!1}]},_3dTiles:{type:"_3dTiles",name:this.$t("featureDatas._3dTiles.subName"),list:[{title:this.$t("formRelational.address.label"),value:"",optionState:!0,validate:!0,validateState:null}]},wmts:{type:"wmts",name:this.$t("featureDatas.wmts.name"),list:[{title:this.$t("formRelational.address.label"),value:"",optionState:!0,validate:!0,optionalList:[{itemImg:"./image/wmts/wmts1.png",label:this.$t("featureDatas.wmts.extend.mapWorld"),pathname:"/img_w/wmts?tk=1e1e425c920f09544d68abc6d3817d04&SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&FORMAT=tiles",url:""},{itemImg:"./image/wmts/wmts2.png",label:"ArcGIS Online",url:"https://services.arcgisonline.com/arcgis/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"}]}]},wms:{type:"wms",name:this.$t("featureDatas.wms.name"),list:[{title:this.$t("formRelational.address.label"),value:"https://img.nj.gov/imagerywms/Natural2015?bbox={bbox-epsg-3857}&format=image/png&service=WMS&version=1.1.1&request=GetMap&srs=EPSG:3857&transparent=true&width=256&height=256&layers=Natural2015",optionState:!0,validate:!0}]},tms:{type:"tms",name:this.$t("featureDatas.tms.name"),list:[{title:this.$t("formRelational.address.label"),value:"",optionState:!0,validate:!0}]},_3dBuilding:{type:"_3dBuilding",name:this.$t("featureDatas._3dBuilding.name"),list:[{title:this.$t("formRelational.color.label"),value:"rgb(215, 245, 255)"},{title:this.$t("formRelational.opacity.label"),value:1},{title:this.$t("featureSetting.style._3dBuilding.label"),value:12}]},annotation:{type:"annotation",name:this.$t("featureDatas.annotation.name"),list:[{clickCoordinate:!0,title:this.$t("formRelational.basePoint.label1"),tooltip:this.$t("formRelational.basePoint.placeholder"),optionState:!0},{title:this.$t("formRelational.translate.label"),optionState:!0},{title:this.$t("featureSetting.advanced.model.label"),width:30,height:30,optionState:!0,distanceType:!0,distance:500,tipContent:this.$t("featureSetting.style.anchorPoint.tooltip1")},{title:this.$t("others.type"),radio:"0",needPanel:!1,optionState:!0},{title:this.$t("featureSetting.style.anchorPoint.label1"),radio:"0",defaultImg:["".concat("","image/point_1.png"),"".concat("","image/point_2.png"),"".concat("","image/point_3.png"),"".concat("","image/point_4.png")],checkedImg:0,imgLink:"",htmlCode:"",cssCode:"",optionState:!1},{title:this.$t("featureSetting.style.anchorPoint.label10"),defaultImg:[a("f6a2"),a("113c"),a("fc5e"),a("2007")],checkedType:0,optionState:!1}],setType:{}},billboard:{type:"billboard",name:this.$t("featureDatas.billboard.name"),list:[{clickCoordinate:!0,title:this.$t("formRelational.basePoint.label1"),tooltip:this.$t("formRelational.basePoint.placeholder"),optionState:!0},{title:this.$t("formRelational.translate.label"),optionState:!0},{title:this.$t("featureSetting.style.anchorPoint.label4"),width:4,height:2,optionState:!0,lockScale:!0,locked:!1,tips:!0,tipContent:this.$t("featureSetting.style.anchorPoint.tooltip2")},{title:this.$t("others.type"),radio:"0",needPanel:!1,optionState:!0},{title:this.$t("featureSetting.style.anchorPoint.label1"),radio:"0",defaultImg:["".concat("","image/point_1.png"),"".concat("","image/point_2.png"),"".concat("","image/point_3.png"),"".concat("","image/point_4.png")],checkedImg:0,imgLink:"",htmlCode:"",cssCode:"",optionState:!1},{title:this.$t("featureSetting.style.anchorPoint.label10"),defaultImg:[a("f6a2"),a("113c"),a("fc5e"),a("2007")],checkedType:0,optionState:!1}],setType:{}},gltf:{type:"glTF",name:this.$t("featureDatas.gltf.subName"),list:[{title:this.$t("formRelational.address.label"),value:"",optionState:!0,needImageSource:!1,validate:!0,validateState:null,addType:0,addModeList:[{label:this.$t("dialog.materialLibrary.tooltip"),value:0},{label:this.$t("dialog.materialLibrary.tooltip1"),value:1}]}]},fbx:{type:"fbx",name:this.$t("featureDatas.fbx.subName"),list:[{title:this.$t("formRelational.address.label"),value:"",validate:!0,validateState:null,addType:0,addModeList:[{label:this.$t("dialog.materialLibrary.tooltip"),value:0},{label:this.$t("dialog.materialLibrary.tooltip1"),value:1}]}]},panorama:{type:"panorama",name:this.$t("featureDatas.panorama.name"),list:[{title:this.$t("formRelational.address.label"),value:"",optionState:!0,validate:!0,validateState:null},{title:this.$t("formRelational.radius.label"),value:20,optionState:!0},{title:this.$t("featureSetting.style.panorama.deepPath"),value:"",optionState:!0}]},shield:{type:"shield",name:this.$t("featureDatas.shield.name"),list:[{title:this.$t("formRelational.address.label"),value:"",optionState:!1},{clickCoordinate:!0,title:this.$t("formRelational.basePoint.label1"),tooltip:this.$t("formRelational.basePoint.placeholder"),optionState:!0}]},heatmap:{type:"heatmap",name:this.$t("featureDatas.heatmap.name"),list:[{title:this.$t("menuIconName.setUp"),optionState:!0}]},video:{type:"video",name:this.$t("featureDatas.video.name"),list:[{title:this.$t("formRelational.address.label"),value:"",optionState:!0,validate:!0,validateState:null},{title:this.$t("featureSetting.style.video.coordinate"),cor:[{id:1,position:{x:0,y:0}},{id:2,position:{x:0,y:0}},{id:3,position:{x:0,y:0}},{id:4,position:{x:0,y:0}}]}]},ring:{type:"ring",name:this.$t("featureDatas.ring.name"),list:[{title:this.$t("formRelational.address.label"),value:"",optionState:!1},{clickCoordinate:!0,title:this.$t("formRelational.basePoint.label1"),tooltip:this.$t("formRelational.basePoint.placeholder"),optionState:!0}]},ripplewall:{type:"ripplewall",name:this.$t("featureDatas.ripplewall.name"),list:[{title:this.$t("menuIconName.setUp"),optionState:!0},{title:this.$t("dialog.coordinate.name"),position:[],units:[]}]},radar:{type:"radar",name:this.$t("featureDatas.radar.name"),list:[{title:this.$t("formRelational.address.label"),value:"",optionState:!1},{clickCoordinate:!0,title:this.$t("formRelational.basePoint.label1"),tooltip:this.$t("formRelational.basePoint.placeholder"),optionState:!0}]},shp:{type:"shp",name:this.$t("featureDatas.shp.name"),list:[{title:this.$t("formRelational.address.label"),value:"",optionState:!0,validate:!0,validateState:null}]},geoJSON:{type:"geoJSON",name:this.$t("featureDatas.geoJSON.name"),list:[{title:this.$t("formRelational.address.label"),value:"",optionState:!0,validate:!0,validateState:null}]},vectorextrude:{type:"vectorextrude",name:this.$t("featureDatas.vectorextrude.name"),list:[{title:this.$t("formRelational.address.label"),value:"",optionState:!0,validate:!0,validateState:null}]},polyline:{type:"polyline",name:this.$t("featureDatas.polyline.name"),list:[{title:this.$t("formRelational.image.label1"),value:"",optionState:!0,validateImg:!0,validateState:null},{title:this.$t("dialog.coordinate.name"),position:[[0,0,0],[0,0,0]],optionState:!0,addOption:!0,freeSketchGeometry:!0,tooltip:this.$t("formRelational.basePoint.tooltip"),optionItem:{name:"position",value:[0,0,0]}}]},polygon:{type:"polygon",name:"",list:[{title:this.$t("formRelational.image.label1"),value:"",optionState:!1,needImageSource:!0,validate:!0,validateState:null},{title:this.$t("dialog.coordinate.name"),position:[[0,0],[0,0],[0,0]],optionState:!0,freeSketchGeometry:!0,addOption:!0,tooltip:this.$t("formRelational.basePoint.tooltip"),optionItem:{name:"position",value:[0,0]}},{title:this.$t("formRelational.color.label"),value:"rgb(255, 50, 0)"},{title:this.$t("formRelational.opacity.label"),value:1},{title:this.$t("formRelational.baseTop.label"),value:10},{title:this.$t("formRelational.top.label"),value:10},{title:this.$t("formRelational.speed.label"),value:1},{title:this.$t("formRelational.showLine.label"),value:!1}]},flame:{type:"flame",name:this.$t("featureDatas.flame.name"),list:[{clickCoordinate:!0,title:this.$t("formRelational.basePoint.label"),tooltip:this.$t("formRelational.basePoint.placeholder"),optionState:!0}]},smoke:{type:"smoke",name:this.$t("featureDatas.smoke.name"),list:[{clickCoordinate:!0,title:this.$t("formRelational.basePoint.label"),tooltip:this.$t("formRelational.basePoint.placeholder"),optionState:!0}]},"batch-extrude":{type:"batch_extrude",name:this.$t("featureDatas['batch-extrude'].name"),list:[{title:this.$t("featureSetting.style.batchExtrude.label8"),position:[[-1,-1],[1,-1],[1,1],[-1,1]],radius:2,optionState:!0,addOption:!0,tooltip:this.$t("formRelational.basePoint.tooltip"),type:"polygon",optionItem:{name:"position",value:[0,0]}}]},kml:{type:"kml",name:this.$t("featureDatas.kml.name"),list:[{title:this.$t("formRelational.address.label"),value:"",optionState:!0,validate:!0,validateState:null}]},advanced_envmap:{list:[{title:this.$t("formRelational.image.label"),value:3},{title:this.$t("topToolBarMenu.advanced.children.advanced_envmap.settings.label1"),value:30},{title:this.$t("topToolBarMenu.advanced.children.advanced_envmap.settings.label2"),value:.7}]},advanced_setting:{list:[{title:this.$t("topToolBarMenu.advanced.children.advanced_setting.settings.label"),value:0},{title:this.$t("topToolBarMenu.advanced.children.advanced_setting.settings.label1"),value:8192},{title:this.$t("topToolBarMenu.advanced.children.advanced_setting.settings.label2"),value:!1},{title:this.$t("topToolBarMenu.advanced.children.advanced_setting.settings.label3"),value:!1},{title:this.$t("topToolBarMenu.advanced.children.advanced_setting.settings.label4"),value:"rgb(0, 0.6274509803921569, 0.9764705882352941)"}]},pathAnimation:{type:"pathAnimation",name:this.$t("topToolBarMenu.advanced.children.pathAnimation.name"),list:[{},{title:this.$t("topToolBarMenu.advanced.children.pathAnimation.settings.label6"),position:[[0,0,0],[0,0,0]],optionState:!0}]},waterfall:{type:"waterfall",name:this.$t("featureDatas.waterfall.name"),list:[{clickCoordinate:!0,title:this.$t("formRelational.basePoint.label"),tooltip:this.$t("formRelational.basePoint.placeholder"),optionState:!0}]},trigger:{list:[{title:this.$t("topToolBarMenu.advanced.children.trigger.name")},{title:this.$t("featureSetting.triggerConditions.label6"),position:[]}]},projector:{elementIDs:[],position:[0,0,0],rotation:[0,0,0],fov:30,aspect:16/9,scale:1,helper:!0,list:[{title:"资源地址",value:"",validate:!0}]},snow:{width:200,height:200,speed:5,density:5},tailline:{type:"tailline",name:this.$t("featureDatas.tailline.name"),list:[{title:this.$t("formRelational.image.label1"),value:"",optionState:!0,validateImg:!0,validateState:null},{title:this.$t("dialog.coordinate.name"),position:[[0,0,0],[0,0,0]],optionState:!0,addOption:!0,freeSketchGeometry:!0,tooltip:this.$t("formRelational.basePoint.tooltip"),optionItem:{name:"position",value:[0,0,0]}}]}}}}},computed:{isVothing:function(){return this.$store.state.menuList.isVothing}},methods:{addModelFeature:function(t,e){var a=this;if(this.dragData.isDragData){var i={id:this.dragData.featureID,name:this.dragData.featureName};this.hanleAddModelFeature(t,i,"add",e)}else{var n=window.scene.features.get(this.dragData.id),s=""===n.version?-1:n.version;if(s!==t[0].phaseValue)this.$confirm(this.$t("messageTips.reloadModel"),this.$t("messageTips.tipsTitle"),{confirmButtonText:this.$t("menuIconName.confirm"),cancelButtonText:this.$t("messageTips.cancel"),type:"warning"}).then((function(){n.dispose(),window.scene.render();var i={id:a.dragData.modelID,name:a.dragData.name};a.hanleAddModelFeature(t,i,"edit",e)})).catch((function(){a.$parent.loading&&a.$parent.loading.close()}));else if(this.dragData.setFeatureID&&this.dragData.id!=this.styleFormDatas.id){n.dispose(),window.scene.render();var o={id:this.dragData.modelID,name:this.dragData.name};this.hanleAddModelFeature(t,o,"edit",e)}else{n.origin=[parseFloat(this.styleFormDatas.longitude+""),parseFloat(this.styleFormDatas.latitude+"")],n.altitude=parseFloat(this.styleFormDatas.altitude+""),n.rotation=this.styleFormDatas.rotation.map((function(t){return parseFloat(t+"")})),n.offset=this.styleFormDatas.offset.map((function(t){return parseFloat(t+"")})),n.id=this.styleFormDatas.id,n.priority=e.priority,n.always=e.always||!1,t[1].setOptions.forEach((function(t){n[t.attr]=t.state})),this.$emit("closeSet"),this.$nextTick((function(){a.$deepUpdateScene("model")}));var r="【".concat(this.$t("featureDatas.model.name"),"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:r,type:"success"})}}},hanleAddModelFeature:function(t,e,a,i){var n=this,s=window.location.origin+"/MODEL_URL";s=window.IP_CONFIG.MODEL_URL;var o=window.scene.addFeature("model",this.styleFormDatas.id);o.server=s,o.modelID=e.id,o.vaultID=this.$store.state.scene.currentSceneProjectID,o.version=t[0].phaseValue>=0?t[0].phaseValue:"",o.origin=[parseFloat(this.styleFormDatas.longitude+""),parseFloat(this.styleFormDatas.latitude+"")],o.altitude=parseFloat(this.styleFormDatas.altitude+""),o.rotation=this.styleFormDatas.rotation.map((function(t){return parseFloat(t+"")})),o.name=e.name,o.priority=i.priority,o.always=i.always||!1;for(var r=[],l=0;l<this.styleFormDatas.offset.length;l++)r[l]=parseFloat(this.styleFormDatas.offset[l]+"");o.offset=r,t[1].setOptions.forEach((function(t){o[t.attr]=t.state})),o.load().then((function(){o.activeView().then((function(){window.scene.fit2Feature(o),n.$nextTick((function(){n.$deepUpdateScene("model")}));var t="".concat("add"==a?n.$t("others.added"):n.$t("others.updated"));n.$message({showClose:!0,message:"【".concat(n.$t("featureDatas.model.name"),"】").concat(t),type:"success"}),n.$emit("closeSet");var e=JSON.parse(JSON.parse(JSON.stringify(o)));"add"==a?(e.maxVersion=o.maxVersion,e.staticType=!0,e.setFeatureID=!0,n.$store.commit("saveDragOverData",e),n.$store.commit("toggleSettingActive","model")):(e.setFeatureID&&delete e.setFeatureID,e.staticType&&delete e.staticType,n.$store.commit("saveDragOverData",e))})).catch((function(t){console.error(t)})).finally((function(){n.$emit("closeSet")}))})).catch((function(t){console.error(t)})).finally((function(){n.$emit("closeSet")}))},addDemFeature:function(t,e){var a=window.scene.features.get("dem");if(a){""!=t[0].value?a.url=t[0].value:a.url=t[0].defaultAddress,a.name=""==this.styleFormDatas.name?a.name:this.styleFormDatas.name,a.priority=e.priority,a.always=e.always||!1,a.dataKey=window.scene.postData({opacity:t[1].value},a.id);var i="【".concat(this.$t("featureDatas.dem.name"),"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:i,type:"success"})}else{var n=window.scene.addFeature("dem","d1");""!=t[0].value?n.url=t[0].value:n.url=t[0].defaultAddress,n.dataKey=n.id,n.name=this.$t("featureDatas.dem.name"),n.priority=e.priority,n.always=e.always||!1,n.load();var s="【".concat(this.$t("featureDatas.dem.name"),"】").concat(this.$t("others.added"));this.$message({showClose:!0,message:s,type:"success"});var o=JSON.parse(JSON.parse(JSON.stringify(n)));o.staticType=!0,this.$store.commit("saveDragOverData",o),this.$store.commit("toggleSettingActive","dem")}},add3DTilesFeature:function(t,e,a){var s=this;return Object(n["a"])(Object(i["a"])().mark((function n(){var o,r,c,d,p;return Object(i["a"])().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(!s.dragData.isDragData){i.next=22;break}return o=window.scene.addFeature("tile",s.styleFormDatas.id),o.url=t[0].value,o.dataKey="tile-"+o.id,o.offset=s.styleFormDatas.offset.map((function(t){return parseFloat(t+"")})),o.rotation=s.styleFormDatas.rotation.map((function(t){return parseFloat(t+"")})),o.priority=a.priority,o.always=a.always||!1,window.scene.postData(e,o.dataKey),o.name=l["a"]["_3dTiles"].getName()||s.styleFormDatas.name,o.load(),r="【".concat(s.$t("featureDatas._3dTiles.name"),"】").concat(s.$t("others.added")),s.$message({showClose:!0,message:r,type:"success"}),i.next=15,s.asynchronousListenerTiles();case 15:window.scene.fit2Feature(o.id),c=JSON.parse(JSON.parse(JSON.stringify(o))),c.staticType=!0,s.$store.commit("saveDragOverData",c),s.$store.commit("toggleSettingActive","_3dTiles"),i.next=35;break;case 22:d=window.scene.features.get(s.dragData.id),d.url=t[0].value,d.id=s.styleFormDatas.id,d.name=s.styleFormDatas.name||d.name,d.offset=s.styleFormDatas.offset.map((function(t){return parseFloat(t+"")})),d.origin=[parseFloat(s.styleFormDatas.longitude+""),parseFloat(s.styleFormDatas.latitude+"")],d.altitude=parseFloat(s.styleFormDatas.altitude+""),d.rotation=s.styleFormDatas.rotation.map((function(t){return parseFloat(t+"")})),d.priority=a.priority,d.always=a.always||!1,window.scene.postData(e,d.dataKey),p="【".concat(s.$t("featureDatas._3dTiles.name"),"】").concat(s.$t("others.updated")),s.$message({showClose:!0,message:p,type:"success"});case 35:case"end":return i.stop()}}),n)})))()},addwmtsFeature:function(t,e){var a=this.dragData.type;if(this.dragData.isDragData){var i=window.scene.addFeature(a,this.styleFormDatas.id);i.name=l["a"][a].getName()||this.styleFormDatas.name,i.url=t[0].value,i.priority=e.priority,i.always=e.always||!1,i.load();var n="【".concat(a,"】").concat(this.$t("others.added"));this.$message({showClose:!0,message:n,type:"success"});var s=JSON.parse(JSON.parse(JSON.stringify(i)));s.staticType=!0,this.$store.commit("saveDragOverData",s),this.$store.commit("toggleSettingActive",a)}else{var o=window.scene.features.get(this.dragData.id);o.name=this.styleFormDatas.name||o.name,o.url=t[0].value,o.id=this.styleFormDatas.id,o.priority=e.priority,o.always=e.always||!1;var r="【".concat(a,"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:r,type:"success"})}},add3DbuildingFeature:function(t){var e=window.scene.features.get("3DBuilding"),a=!0;if(e||(a=!1,e=window.scene.addFeature("building"),e.dataKey="building"+e.id,window.scene.postData({color:t[0].value,opacity:t[1].value,minzoom:t[2].value},e.dataKey)),e.name=this.styleFormDatas.name||this.$t("featureDatas._3dBuilding.name"),a){var i=JSON.parse(JSON.parse(JSON.stringify(e)));i.staticType=!0,this.$store.commit("saveDragOverData",i),this.$store.commit("toggleSettingActive","_3dBuilding");var n="【".concat(this.$t("featureDatas._3dBuilding.name"),"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:n,type:"success"})}else{e.load();var s="【".concat(this.$t("featureDatas._3dBuilding.name"),"】").concat(this.$t("others.added"));this.$message({showClose:!0,message:s,type:"success"});var o=JSON.parse(JSON.parse(JSON.stringify(e)));o.staticType=!0,this.$store.commit("saveDragOverData",o),this.$store.commit("toggleSettingActive","_3dBuilding")}},addAnnotationFeature:function(t,e,a,i,n){var s=this,c="",d="",p='var clickTimeSign = null;annotation.addEventListener("click",()=>{window.scene.fit2Feature(annotation.id)});';this.styleFormDatas.datas.annotation.setType.type=t[2].radio,this.styleFormDatas.datas.annotation.setType.annotationRadio=t[6].annotationRadio,this.styleFormDatas.datas.annotation.setType.visibleDistance=t[0].value,a&&a.length>0&&(a.forEach((function(t){t.jsCodes&&""!=t.jsCodes&&(p+=t.jsCodes+" ")})),this.styleFormDatas.datas.annotation.setType.eventsList=a),e.annotation.width=t[1].width,e.annotation.height=parseInt(t[5].checkedType)>=0?t[1].height:0,""==e.annotation.title&&""==e.annotation.videoLink&&(e.annotation.title=this.$t("featureSetting.data.anchorPoint.label7"));var u=null;if(this.dragData.isDragData?(u=window.scene.addFeature("annotation"),u.name=l["a"]["annotation"].getName()||this.styleFormDatas.name):(u=window.scene.features.get(this.dragData.id),u.name=this.styleFormDatas.name||u.name,"billboard"==u.type&&(u=window.scene.addFeature("annotation"),u.dataKey="annotion-"+u.id,window.scene.removeFeature(this.dragData.id,!0),this.$deepUpdateScene("billboard"))),u.origin=[parseFloat(this.styleFormDatas.longitude+""),parseFloat(this.styleFormDatas.latitude+"")],u.altitude=parseFloat(this.styleFormDatas.altitude+""),u.priority=n.priority,u.always=n.always||!1,void 0!=i&&e.annotation.dataList&&0==e.annotation.dataList.length&&(Object.keys(i.data.widgets.entityProperty).forEach((function(t){i.data.widgets.entityProperty[t]&&t.length>0&&i.data.widgets.entityProperty[t].forEach((function(t){e.annotation.dataList.push({key:t.name,value:0})}))})),e.annotation.dataList=Object(o["a"])(e.annotation.dataList,"key")),"custom"==t[2].radio)this.styleFormDatas.datas.annotation.setType.anchorType="custom",c=t[3].htmlCode,d=t[3].cssCode;else if(this.styleFormDatas.datas.annotation.setType.anchorType="default",t[4].checkedType>=0&&e.annotation.dataList&&e.annotation.dataList.length>0||"videoAnchor"==t[2].radio){if(this.styleFormDatas.datas.annotation.setType.dataPanel=!0,this.styleFormDatas.datas.annotation.setType.panelToggleMode=e.annotation.panelToggleMode,"videoAnchor"==t[2].radio){""==e.annotation.title&&(e.annotation.title=this.$t("featureSetting.data.anchorPoint.label8")),this.styleFormDatas.datas.annotation.setType.video={},this.styleFormDatas.datas.annotation.setType.video.videoLink=e.annotation.videoLink,this.styleFormDatas.datas.annotation.setType.video.title=e.annotation.title,c=this.setDefaultVideoAnchorHTML(t,e,this.dragData.id);var h=/\.(m3u8|hls|mp4)$/;h.test(e.annotation.videoLink)?p+="\n                            let scriptElement = document.getElementById('script1');\n                            if (!scriptElement) {\n                                let script1 = document.createElement('script');\n                                script1.id = 'script1';\n                                script1.src = '".concat(window.location.origin+Object(r["g"])(),"sources/videoJs/video.min.js';\n                                let script2 = document.createElement('script');\n                                script2.id = 'script2';\n                                script2.src = '").concat(window.location.origin+Object(r["g"])(),'sources/videoJs/videojs-contrib-hls.min.js\';\n                                document.body.appendChild(script1);\n                                document.body.appendChild(script2);\n                                var link=document.createElement("link");\n                                link.setAttribute("rel", "stylesheet");\n                                link.setAttribute("type", "text/css");\n                                link.setAttribute("href", \'').concat(window.location.origin+Object(r["g"])(),"sources/videoJs/video-js-cdn.min.css');\n                                var heads = document.getElementsByTagName(\"head\");\n                                if(heads.length) {\n                                    heads[0].appendChild(link);\n                                } else {\n                                    document.documentElement.appendChild(link);\n                                }\n                            };\n                            setTimeout(() => {\n                                window.myVideoId = '").concat(this.dragData.id,"';\n                                if (!window.myVideo) {\n                                    window.myVideo = {}\n                                }\n                                window.myVideo['").concat(this.dragData.id,"'] = videojs('myVideo-").concat(this.dragData.id,"',{\n\n                                    bigPlayButton : true,\n\n                                    textTrackDisplay : false,\n\n                                    posterImage: false,\n                                    autoplay: true,\n\n                                    errorDisplay : false\n\n                                    })\n\n                                    window.myVideo['").concat(this.dragData.id,"'].play() // 视频播放\n\n                            }, 1000);\n                            "):this.$nextTick((function(){s.$store.commit("listenAnnotation")}))}else c=this.setDefaultDataAnchorHTML(t,e);p+=this.setDataAnnotationDefaultEvent(e),this.styleFormDatas.datas.annotation.setType.lineType=t[4].checkedType}else{var m=this.setBasicAnchorImage(t);""==m&&(m="".concat("","image/anchor/1_point.png"),this.styleFormDatas.datas.annotation.setType.checkedImg=1),c='<img style="width:'.concat(t[1].width,"px;\n                                                height:").concat(t[1].height,"px;\n                                                margin-top:").concat(-1*t[1].height/2+10,'px"\n                                                src="').concat(m,'">')}this.styleFormDatas.datas.annotation.setType.checkedImgSize={width:t[1].width,height:t[1].height},u.offset=this.styleFormDatas.offset.map((function(t){return parseFloat(t+"")})),u.name=this.styleFormDatas.name||u.name;var g={content:e.annotation.dataList&&e.annotation.dataList.length>0?e.annotation:"",htmlCode:c,cssCode:d,jsCode:p,minDistance:""!=t[0].value[0]?parseFloat(t[0].value[0]):0,maxDistance:""!=t[0].value[1]?parseFloat(t[0].value[1]):1/0,setType:this.styleFormDatas.datas.annotation.setType};if(u.dataKey="annotation-"+u.id,window.scene.postData(g,"annotation-"+u.id),this.isVothing&&this.setIotElementData(u.dataKey,e,i),this.dragData.isDragData||"billboard"==this.dragData.type){u.load();var f="【".concat(this.$t("featureSetting.style.anchorPoint.label20"),"】").concat(this.$t("others.added"));this.$message({showClose:!0,message:f,type:"success"}),this.$deepUpdateScene("annotation")}else{var y=JSON.parse(JSON.stringify(this.dragData));y.staticType&&(delete y.staticType,this.$store.commit("saveDragOverData",y));var v="【".concat(this.$t("featureSetting.style.anchorPoint.label20"),"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:v,type:"success"})}},addPanoramaFeature:function(t,e){var a=null;if(this.dragData.isDragData?(a=window.scene.addFeature("panorama"),a.name=l["a"]["panorama"].getName()||this.styleFormDatas.name):(a=window.scene.features.get(this.dragData.id),a.name=this.styleFormDatas.name||a.name),a.offset=this.styleFormDatas.offset.map((function(t){return parseFloat(t+"")})),a.radius=parseFloat(t[1].value),a.url=t[0].value,a.origin=[parseFloat(this.styleFormDatas.longitude+""),parseFloat(this.styleFormDatas.latitude+"")],a.altitude=parseFloat(this.styleFormDatas.altitude+""),a.priority=e.priority,a.always=e.always||!1,a.dataKey=window.scene.postData({radius:a.radius,depthPath:t[2].value}),this.dragData.isDragData){a.load(),window.scene.fit2Feature(a);var i="【".concat(this.$t("featureDatas.panorama.name"),"】").concat(this.$t("others.added"));this.$message({showClose:!0,message:i,type:"success"});var n=JSON.parse(JSON.parse(JSON.stringify(a)));n.staticType=!0,this.$store.commit("saveDragOverData",n),this.$store.commit("toggleSettingActive","panorama")}else{var s="【".concat(this.$t("featureDatas.panorama.name"),"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:s,type:"success"})}},addShieldFeature:function(t,e,a,i){var n=null;this.dragData.isDragData?(n=window.scene.addFeature("shield"),n.name=l["a"]["shield"].getName()||this.styleFormDatas.name):(n=window.scene.features.get(this.dragData.id),n.name=this.styleFormDatas.name||n.name),n.radius=parseFloat(e.radius),n.origin=[parseFloat(this.styleFormDatas.longitude+""),parseFloat(this.styleFormDatas.latitude+"")],n.altitude=parseFloat(this.styleFormDatas.altitude+""),n.offset=this.styleFormDatas.offset.map((function(t){return parseFloat(t+"")})),n.priority=i.priority,n.always=i.always||!1;var s={color:e.color,opacity:e.opacity,radius:parseFloat(e.radius+""),speed:Math.floor(e.speed/33.33*100)/100};if(n.dataKey="shield-"+n.id,window.scene.postData(s,n.dataKey),this.isVothing&&this.setIotElementData(n.dataKey,e,a),this.dragData.isDragData){n.load(),window.scene.fit2Feature(n);var o="【".concat(this.$t("featureDatas.shield.name"),"】").concat(this.$t("others.added"));this.$message({showClose:!0,message:o,type:"success"});var r=JSON.parse(JSON.parse(JSON.stringify(n)));r.staticType=!0,this.$store.commit("saveDragOverData",r),this.$store.commit("toggleSettingActive","shield")}else{var c="【".concat(this.$t("featureDatas.shield.name"),"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:c,type:"success"})}},addHeatmapFeature:function(t,e,a,i){var n=null;this.dragData.isDragData?(n=window.scene.addFeature("heatmap"),n.name=l["a"]["heatmap"].getName()||this.styleFormDatas.name):(n=window.scene.features.get(this.dragData.id),n.name=this.styleFormDatas.name||n.name),n.origin=[parseFloat(this.styleFormDatas.longitude+""),parseFloat(this.styleFormDatas.latitude+"")],n.altitude=parseFloat(this.styleFormDatas.altitude+""),n.priority=i.priority,n.always=i.always||!1;var s=e.pointsArray||[],o=[];if(s&&s.length>0?s.map((function(t){var e={};e.position=[parseFloat(t.x+""),parseFloat(t.y+"")],e.value=parseFloat(t.value+""),o.push(e)})):o=[],n.dataKey="heatmap-"+n.id,window.scene.postData({points:o,gradient:e.gradient,color:e.color,size:parseFloat(e.size+""),scaleZ:parseFloat(e.scaleZ+""),opacity:parseFloat(e.opacity+""),wireframe:e.wireframe},n.dataKey),this.isVothing&&this.setIotElementData(n.dataKey,e,a),this.dragData.isDragData){n.load(),this.$message({showClose:!0,message:this.$t("featureSetting.style.heatMap.message"),type:"success"});var r=JSON.parse(JSON.parse(JSON.stringify(n)));r.staticType=!0,this.$store.commit("saveDragOverData",r),this.$store.commit("toggleSettingActive","heatmap")}else{var c="【".concat(this.$t("featureDatas.heatmap.name"),"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:c,type:"success"})}},addRingFeature:function(t,e,a,i){var n=null;if(this.dragData.isDragData?(n=window.scene.addFeature("ring"),n.name=l["a"]["ring"].getName()||this.styleFormDatas.name):(n=window.scene.features.get(this.dragData.id),n.name=this.styleFormDatas.name||n.name),n.origin=[parseFloat(this.styleFormDatas.longitude+""),parseFloat(this.styleFormDatas.latitude+"")],n.altitude=parseFloat(this.styleFormDatas.altitude+""),n.offset=this.styleFormDatas.offset.map((function(t){return parseFloat(t+"")})),n.dataKey="ring-"+n.id,n.priority=i.priority,n.always=i.always||!1,window.scene.postData({radius:parseFloat(e.radius+""),color:e.color,speed:e.speed},n.dataKey),this.isVothing&&this.setIotElementData(n.dataKey,e,a),this.dragData.isDragData){n.load(),window.scene.fit2Feature(n);var s="【".concat(this.$t("featureDatas.ring.name"),"】").concat(this.$t("others.added"));this.$message({showClose:!0,message:s,type:"success"});var o=JSON.parse(JSON.parse(JSON.stringify(n)));o.staticType=!0,this.$store.commit("saveDragOverData",o),this.$store.commit("toggleSettingActive","ring")}else{var r="【".concat(this.$t("featureDatas.ring.name"),"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:r,type:"success"})}},addRipplewallFeature:function(t,e,a,i){var n=null;this.dragData.isDragData?(n=window.scene.addFeature("ripplewall"),n.dataKey="ripplewall-"+n.id,n.name=l["a"]["ripplewall"].getName()||this.styleFormDatas.name):(n=window.scene.features.get(this.dragData.id),n.name=this.styleFormDatas.name||n.name),n.origin=[parseFloat(this.styleFormDatas.longitude+""),parseFloat(this.styleFormDatas.latitude+"")],n.altitude=parseFloat(this.styleFormDatas.altitude+""),n.priority=i.priority,n.always=i.always||!1;var s=t[1].units,o=s[0].position,r=s[s.length-1].position;if(o.x==r.x&&o.y==r.y||(s[s.length]={id:s.length+1,position:s[0].position}),window.scene.postData({units:JSON.parse(JSON.stringify(s)),color:e.color,speed:Math.floor(e.speed/33.33*100)/100,opacity:e.opacity,height:parseFloat(e.height+"")},n.dataKey),this.isVothing&&this.setIotElementData(n.dataKey,e,a),this.dragData.isDragData){n.load(),window.scene.fit2Feature(n);var c="【".concat(this.$t("featureDatas.ripplewall.name"),"】").concat(this.$t("others.added"));this.$message({showClose:!0,message:c,type:"success"});var d=JSON.parse(JSON.parse(JSON.stringify(n)));d.staticType=!0,this.$store.commit("saveDragOverData",d),this.$store.commit("toggleSettingActive","ripplewall")}else{var p="【".concat(this.$t("featureDatas.ripplewall.name"),"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:p,type:"success"})}},addRadarFeature:function(t,e,a,i){var n=null;this.dragData.isDragData?(n=window.scene.addFeature("radar"),n.dataKey="radar-"+n.id,n.name=l["a"]["radar"].getName()||this.styleFormDatas.name):(n=window.scene.features.get(this.dragData.id),n.name=this.styleFormDatas.name||n.name),n.origin=[parseFloat(this.styleFormDatas.longitude+""),parseFloat(this.styleFormDatas.latitude+"")],n.altitude=parseFloat(this.styleFormDatas.altitude+""),n.offset=this.styleFormDatas.offset.map((function(t){return parseFloat(t+"")})),n.priority=i.priority,n.always=i.always||!1;var s=parseFloat(e.width+"")/180*Math.PI,o={color:e.color,speed:e.speed,radius:parseFloat(e.radius+""),opacity:parseFloat(e.opacity+""),width:s};if(this.isVothing&&this.setIotElementData(n.dataKey,e,a),window.scene.postData(o,n.dataKey),this.dragData.isDragData){n.load(),window.scene.fit2Feature(n);var r="【".concat(this.$t("featureDatas.radar.name"),"】").concat(this.$t("others.added"));this.$message({showClose:!0,message:r,type:"success"});var c=JSON.parse(JSON.parse(JSON.stringify(n)));c.staticType=!0,this.$store.commit("saveDragOverData",c),this.$store.commit("toggleSettingActive","radar")}else{var d="【".concat(this.$t("featureDatas.radar.name"),"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:d,type:"success"})}},addVideoFeature:function(t,e){var a=null;this.dragData.isDragData?(a=window.scene.addFeature("video"),a.dataKey="video-"+a.id,a.name=l["a"]["video"].getName()||this.styleFormDatas.name):(a=window.scene.features.get(this.dragData.id),a.name=this.styleFormDatas.name||a.name),a.url=t[0].value,a.priority=e.priority,a.always=e.always||!1;var i=[];if(t[1].cor.forEach((function(t){i.push([parseFloat(t.position.x+""),parseFloat(t.position.y+"")])})),window.scene.postData(i,a.dataKey),this.dragData.isDragData){a.load(),window.scene.fit2Feature(a);var n="【".concat(this.$t("featureDatas.video.name"),"】").concat(this.$t("others.added"));this.$message({showClose:!0,message:n,type:"success"});var s=JSON.parse(JSON.parse(JSON.stringify(a)));s.staticType=!0,this.$store.commit("saveDragOverData",s),this.$store.commit("toggleSettingActive","video")}else{var o="【".concat(this.$t("featureDatas.video.name"),"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:o,type:"success"})}},addSHPFeature:function(t,e,a){var i=null;if(this.dragData.isDragData?(i=window.scene.addFeature("shp"),i.name=l["a"]["shp"].getName()||this.styleFormDatas.name):(i=window.scene.features.get(this.dragData.id),i.name=this.styleFormDatas.name||i.name),i.url=t[0].value,i.priority=a.priority,i.always=a.always||!1,i.dataKey=window.scene.postData({color:e.color,opacity:parseFloat(e.opacity+""),lineWidth:parseFloat(e.lineWidth+""),height:parseFloat(e.height+""),useUniquePaint:e.useUniquePaint}),this.dragData.isDragData){i.load();var n="【".concat(this.$t("featureDatas.shp.name"),"】").concat(this.$t("others.added"));this.$message({showClose:!0,message:n,type:"success"});var s=JSON.parse(JSON.parse(JSON.stringify(i)));s.staticType=!0,this.$store.commit("saveDragOverData",s),this.$store.commit("toggleSettingActive","shp")}else{var o="【".concat(this.$t("featureDatas.shp.name"),"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:o,type:"success"})}},addGeoJSONFeature:function(t,e,a){var i=null;this.dragData.isDragData?(i=window.scene.addFeature("geoJSON"),i.name=l["a"]["geoJSON"].getName()||this.styleFormDatas.name):(i=window.scene.features.get(this.dragData.id),i.name=this.styleFormDatas.name||i.name);var n=["lineWidth","opacity","size","textSize","outlineWidth"];for(var s in e)n.includes(s)&&(e[s]=parseFloat(e[s]+""));var o=JSON.stringify(e);if(e.originalData=o,i.url=t[0].value,i.dataKey=window.scene.postData(e),i.priority=a.priority,i.always=a.always||!1,this.dragData.isDragData){i.load();var r="【".concat(this.$t("featureDatas.geoJSON.name"),"】").concat(this.$t("others.added"));this.$message({showClose:!0,message:r,type:"success"}),window.scene.render();var c=JSON.parse(JSON.parse(JSON.stringify(i)));c.staticType=!0,this.$store.commit("saveDragOverData",c),this.$store.commit("toggleSettingActive","geoJSON")}else{var d="【".concat(this.$t("featureDatas.geoJSON.name"),"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:d,type:"success"})}},addGltfOrFbxFeature:function(t,e,a){var i=this,n=null,s=null,o=1,r=!0;if(this.dragData.isDragData)if(n=window.scene.addFeature(this.dragData.type),s=this.dragData.defaultSource,s){var c=this.dragData.fileSrc.substring(0,4);this.isVothing?n.url="http"===c?this.dragData.fileSrc:window.IP_CONFIG.BASE_URL+this.dragData.fileSrc:n.url=window.IP_CONFIG.BASE_URL+this.dragData.fileSrc,n.name=l["a"][this.dragData.type].getName()||this.dragData.title}else n.url=t[0].value,n.name=l["a"][this.dragData.type].getName()||("fbx"===this.dragData.type?this.$t("featureDatas.fbx.subName"):this.$t("featureDatas.gltf.subName"));else n=window.scene.features.get(this.dragData.id),s=n.data.defaultSource,n.url=t[2].value,o=t[0].value,r=t[1].value,n.name=this.styleFormDatas.name||n.name;n.origin=[parseFloat(this.styleFormDatas.longitude+""),parseFloat(this.styleFormDatas.latitude+"")],n.altitude=parseFloat(this.styleFormDatas.altitude+""),n.offset=this.styleFormDatas.offset.map((function(t){return parseFloat(t+"")})),n.rotation=this.styleFormDatas.rotation.map((function(t){return parseFloat(t+"")})),n.priority=e.priority,n.always=e.always||!1,n.dataKey=window.scene.postData({defaultSource:s,scale:o,animation:r});var d="".concat("fbx"==this.dragData.type?this.$t("featureDatas.fbx.subName"):this.$t("featureDatas.gltf.subName"));this.dragData.isDragData?(n.load(),setTimeout((function(){window.scene.render(),i.$message({showClose:!0,message:"【".concat(d,"】").concat(i.$t("others.added")),type:"success"});var t=JSON.parse(JSON.parse(JSON.stringify(n)));t.staticType=!0,a||(window.scene.fit2Feature(n),i.$store.commit("saveDragOverData",t),i.$store.commit("toggleSettingActive","gltf"))}),500)):this.$message({showClose:!0,message:"【".concat(d,"】").concat(this.$t("others.updated")),type:"success"})},addPolylineFeature:function(t,e,a){var i=null;this.dragData.isDragData?(i=window.scene.addFeature("polyline"),i.name=l["a"]["polyline"].getName()||this.styleFormDatas.name):(i=window.scene.features.get(this.dragData.id),i.name=this.styleFormDatas.name||i.name),i.priority=a.priority,i.always=a.always||!1;var n=[];t[1].position.forEach((function(t,e){n[e]=t.map((function(t){return parseFloat(t+"")}))}));var s="";""!=t[0].value&&(s=t[0].value);var o={positions:n,flow:e.flow,width:e.width,opacity:e.opacity,image:s,color:e.color,interval:10-(e.interval-1)};if(i.dataKey=window.scene.postData(o,i.dataKey),this.dragData.isDragData){i.load();var r="【".concat(this.$t("featureDatas.polyline.name"),"】").concat(this.$t("others.added"));this.$message({showClose:!0,message:r,type:"success"});var c=JSON.parse(JSON.parse(JSON.stringify(i)));c.staticType=!0,this.$store.commit("saveDragOverData",c),this.$store.commit("toggleSettingActive","polyline")}else{var d="【".concat(this.$t("featureDatas.polyline.name"),"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:d,type:"success"})}},addPolygonFeature:function(t,e,a){var i=null,n=!1;if(this.dragData.isDragData){if(i=window.scene.addFeature("polygon"),n=this.dragData.defaultSource,n){var s=this.dragData.thumb.substring(0,4);this.isVothing?t[0].value="http"===s?this.dragData.thumb:window.location.origin+Object(r["g"])()+this.dragData.thumb:t[0].value=window.location.origin+Object(r["g"])()+this.dragData.thumb}}else i=window.scene.features.get(this.dragData.id),i.name=this.styleFormDatas.name||i.name;var o={positions:[],top:0,base:0,flatten:!1,excavation:!1,extrude:!1,water:!1,fill:!1,showline:!1,color:"rgb(255, 50, 0)",opacity:1,image:"",flow:!1,interval:1,direction:"x",environment:!1,subType:this.dragData.subType,title:"",imageRepeat:{x:1,y:1},terrainOpacity:.1};switch(t[1].position.forEach((function(t,e){o.positions[e]=t.map((function(t){return parseFloat(t+"")}))})),"{}"!=JSON.stringify(e)&&void 0!=e&&(o.color=e.color,o.opacity=e.opacity,o.top=parseFloat(e.top+""),o.base=parseFloat(e.base+""),o.interval=e.interval,o.showline=e.showline,o.imageRepeat=e.imageRepeat,o.terrainOpacity=e.terrainOpacity),this.dragData.subType){case"surface":o.fill=!0,this.handleSubtypeFeatureChanged("surface",i.id,o);break;case"extrude":o.extrude=!0,o.top=Math.abs(o.top)+o.base,this.handleSubtypeFeatureChanged("extrude",i.id,o);break;case"flatten":o.flatten=!0,o.terrainFlatten=!0,o.top=-1*Math.abs(o.top)+o.base,this.handleSubtypeFeatureChanged("flatten",i.id,o);break;case"tileFlatten":o.flatten=!0,o.tileFlatten=!0,o.top=-1*Math.abs(o.top)+o.base,this.handleSubtypeFeatureChanged("tileFlatten",i.id,o);break;case"environment":o.fill=!0,o.environment=!0,o.image=t[0].value,this.handleSubtypeFeatureChanged("environment",i.id,o);break;case"tileExcavation":o.excavation=!0,o.tileExcavation=!0,o.top=-1*Math.abs(o.top)+o.base,this.handleSubtypeFeatureChanged("tileExcavation",i.id,o);break;case"excavation":o.excavation=!0,o.terrainExcavation=!0,o.top=-1*Math.abs(o.top)+o.base,this.handleSubtypeFeatureChanged("excavation",i.id,o);break;case"demOpacity":this.handleSubtypeFeatureChanged("demOpacity",i.id,o);break;case"water":o.water=!0,o.flow=!0,this.handleSubtypeFeatureChanged("water",i.id,o);break}if(i.priority=a.priority,i.always=a.always||!1,this.dragData.isDragData){i.name=o.title,i.dataKey=window.scene.postData(o),i.load();var c="【".concat(this.$t("featureDatas.polygon.name"),"-").concat(o.title,"】").concat(this.$t("others.added"));this.$message({showClose:!0,message:c,type:"success"});var d=JSON.parse(JSON.parse(JSON.stringify(i)));d.subType=this.dragData.subType,d.title=l["a"][this.dragData.subType].getBaseName(),d.staticType=!0,this.$store.commit("saveDragOverData",d),this.$store.commit("toggleSettingActive","polygon")}else{window.scene.postData(o,i.dataKey);var p="【".concat(this.$t("featureDatas.polygon.name"),"-").concat(i.name,"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:p,type:"success"}),window.scene.fit2Feature(i.id)}},addBillboardFeature:function(t,e,a){var i=this,n="",s="";this.styleFormDatas.datas.billboard.setType.type=t[2].radio,this.styleFormDatas.datas.billboard.setType.annotationRadio=t[6].annotationRadio,this.styleFormDatas.datas.billboard.setType.visibleDistance=t[0].value,""==e.annotation.title&&(e.annotation.title=this.$t("featureSetting.data.anchorPoint.label7")),e.annotation.width=t[1].width,e.annotation.height=t[1].height;var o=null;if(this.dragData.staticType?(o=window.scene.addFeature("billboard"),o.dataKey="billboard-"+o.id,window.scene.removeFeature(this.dragData.id,!0)):(o=window.scene.features.get(this.dragData.id),"annotation"==o.type&&(o=window.scene.addFeature("billboard"),o.dataKey="billboard-"+o.id,window.scene.removeFeature(this.dragData.id,!0),this.$deepUpdateScene("annotation"))),o.origin=[parseFloat(this.styleFormDatas.longitude+""),parseFloat(this.styleFormDatas.latitude+"")],o.altitude=parseFloat(this.styleFormDatas.altitude+""),o.name=l["a"]["billboard"].getName()||this.styleFormDatas.name,o.priority=a.priority,o.always=a.always||!1,"custom"==t[2].radio)this.styleFormDatas.datas.billboard.setType.anchorType="custom",n=t[3].htmlCode,s=t[3].cssCode;else if(this.styleFormDatas.datas.billboard.setType.anchorType="default",t[4].checkedType>=0&&e.annotation.dataList&&e.annotation.dataList.length>0)if(this.styleFormDatas.datas.billboard.setType.dataPanel=!0,"custom"==t[2].radio)n=t[3].htmlCode,s=t[3].cssCode;else{var r=this.setDefaultDataBillboardHTML(t);n=this.setCustomDataAnchorHTML(t,e.annotation,r),s=this.billboardExampleCss,this.styleFormDatas.datas.billboard.setType.lineType=t[4].checkedType}else{var c=this.setBasicAnchorImage(t);""==c&&(c="".concat("","image/anchor/1_point.png"),this.styleFormDatas.datas.annotation.setType.checkedImg=1),n='<img src="'.concat(c,'" style="vertical-align:bottom">')}this.styleFormDatas.datas.billboard.setType.checkedImgSize={width:t[1].width,height:t[1].height},o.offset=this.styleFormDatas.offset.map((function(t){return parseFloat(t+"")}));var d=document.createElement("div");d.id="billboard-cyb",d.style.position="fixed",document.body.prepend(d);var p=document.createTextNode(s),u=document.createElement("style");u.type="text/css",u.appendChild(p),d.appendChild(u);var h=(new DOMParser).parseFromString(n,"text/html");d.appendChild(h.body.firstChild),setTimeout((function(){window.domtoimage.toPng(document.querySelector("#billboard-cyb")).then((function(a){var n={image:a,rect:{width:0,height:0},line:!1,lineWidth:2,lineColor:"green",setType:i.styleFormDatas.datas.billboard.setType,content:e.annotation.dataList&&e.annotation.dataList.length>0?e.annotation:""};if(t[1].locked){var s=new Image;s.onload=function(){n.rect.width=t[1].lockWidth,n.rect.height=s.clientHeight/s.clientWidth*t[1].lockWidth,n.setType.locked=t[1].locked,i.handleBillboardImage(o,n,d,s)},s.src=a,s.id="cyb-img",s.style.position="fixed",s.style.visibility="hidden",document.body.appendChild(s)}else n.rect.width=t[1].width,n.rect.height=t[1].height,i.handleBillboardImage(o,n,d)})).catch((function(t){console.error(t),document.body.removeChild(d)}))}),500)},handleBillboardImage:function(t,e,a,i){window.scene.postData(e,t.dataKey);var n="";"annotation"==this.dragData.type?(t.load(),n=this.$t("others.added")):n=this.$t("others.updated"),this.$message({showClose:!0,message:"【".concat(this.$t("featureDatas.billboard.name"),"-").concat(this.styleFormDatas.name,"】").concat(n),type:"success"}),window.scene.render(),document.body.removeChild(a),i&&document.body.removeChild(i),this.$deepUpdateScene("billboard"),this.$emit("closeSet"),setTimeout((function(){window.scene.render()}),1e3)},addTemporaryAnnotationFeature:function(t){var e={position:{x:0,y:0,z:0},content:"",htmlCode:'<img style="width:30px;height:30px;" src="'.concat("",'image/point.gif">'),cssCode:"",jsCode:"",minDistance:0,maxDistance:1/0},a=window.scene.addFeature("annotation");a.origin=[parseFloat(this.styleFormDatas.longitude+""),parseFloat(this.styleFormDatas.latitude+"")],a.altitude=parseFloat(this.styleFormDatas.altitude+""),a.name=l["a"]["annotation"].getName()||t.title||this.$t("featureSetting.style.anchorPoint.label19"),a.offset=[0,0,0],a.dataKey="annotation-"+a.id,window.scene.postData(e,a.dataKey),a.load(),window.scene.render();var i=JSON.parse(JSON.parse(JSON.stringify(a)));i.staticType=!0,i.checkedType=t.subType,this.$store.commit("saveDragOverData",i),this.$store.commit("toggleSettingActive","annotation")},addFlameFeature:function(t,e){var a=null;this.dragData.isDragData?(a=window.scene.addFeature(this.dragData.type),a.dataKey="".concat(this.dragData.type,"-").concat(a.id),a.name=l["a"][this.dragData.type].getName()||this.styleFormDatas.name):(a=window.scene.features.get(this.dragData.id),a.name=this.styleFormDatas.name||a.name),a.origin=[parseFloat(this.styleFormDatas.longitude+""),parseFloat(this.styleFormDatas.latitude+"")],a.altitude=parseFloat(this.styleFormDatas.altitude+""),a.offset=this.styleFormDatas.offset.map((function(t){return parseFloat(t+"")})),a.priority=e.priority,a.always=e.always||!1,window.scene.postData({width:parseFloat(t.width+""),height:parseFloat(t.height+"")},a.dataKey);var i="flame"===this.dragData.type?this.$t("featureDatas.flame.name"):this.$t("featureDatas.smoke.name");if(this.dragData.isDragData){a.load(),this.$message({showClose:!0,message:"【".concat(i,"】").concat(this.$t("others.added")),type:"success"}),window.scene.fit2Feature(a);var n=JSON.parse(JSON.parse(JSON.stringify(a)));n.staticType=!0,this.$store.commit("saveDragOverData",n),this.$store.commit("toggleSettingActive",this.dragData.type)}else this.$message({showClose:!0,message:"【".concat(i,"】").concat(this.$t("others.updated")),type:"success"})},addBatchExtrudeFeature:function(t,e,a){var i=null;this.dragData.isDragData?(i=window.scene.addFeature("batch-extrude"),i.dataKey="".concat(this.dragData.type,"-").concat(i.id)):(i=window.scene.features.get(this.dragData.id),i.name=this.styleFormDatas.name||i.name);var n=[],s=2;"polygon"==t[0].type?n=t[0].position.map((function(t){return{x:parseFloat(t[0]+"")||0,y:parseFloat(t[1]+"")||0}})):s=parseFloat(t[0].radius+"")||2;var o=[];e.datas.forEach((function(t){o.push({color:t.color,bottomPosition:{x:parseFloat(t.bottomPosition[0]+"")||0,y:parseFloat(t.bottomPosition[1]+"")||0,z:parseFloat(t.bottomPosition[2]+"")||0},topPosition:{x:parseFloat(t.topPosition[0]+"")||0,y:parseFloat(t.topPosition[1]+"")||0,z:parseFloat(t.topPosition[2]+"")||0}})}));var r={type:t[0].type,coords:n,radius:s,datas:o,circleSet:{segments:32}};i.origin=[parseFloat(this.styleFormDatas.longitude+""),parseFloat(this.styleFormDatas.latitude+"")],i.altitude=parseFloat(this.styleFormDatas.altitude+""),i.name=this.styleFormDatas.name,i.priority=a.priority,i.always=a.always||!1,window.scene.postData(r,i.dataKey);var l=this.$t("featureDatas['batch-extrude'].name")+"-"+this.styleFormDatas.name;this.dragData.isDragData?(i.load(),this.$message({showClose:!0,message:"【".concat(l,"】").concat(this.$t("others.added")),type:"success"}),window.scene.fit2Feature(i)):this.$message({showClose:!0,message:"【".concat(l,"】").concat(this.$t("others.updated")),type:"success"})},addBatchExtrudeFeature2:function(){var t=window.scene.addFeature("batch-extrude");t.dataKey="batch-extrude-".concat(t.id),t.name=l["a"]["batch-extrude"].getName()||this.styleFormDatas.name,t.priority=0;var e={type:"polygon",coords:[{x:-1,y:-1},{x:1,y:-1},{x:1,y:1},{x:-1,y:1}],radius:2,datas:[],circleSet:{segments:24}};window.scene.postData(e,t.dataKey),t.load();var a=JSON.parse(JSON.parse(JSON.stringify(t)));a.staticType=!0,this.$store.commit("saveDragOverData",a),this.$store.commit("toggleSettingActive","batch-extrude")},addKMLFeature:function(t,e){var a=null;if(this.dragData.isDragData?(a=window.scene.addFeature("kml"),a.name=l["a"]["kml"].getName()||this.styleFormDatas.name):(a=window.scene.features.get(this.dragData.id),a.name=this.styleFormDatas.name||a.name),a.url=t[0].value,a.priority=e.priority,a.always=e.always||!1,this.dragData.isDragData){a.load();var i="【".concat(this.$t("featureDatas.kml.name"),"】").concat(this.$t("others.added"));this.$message({showClose:!0,message:i,type:"success"});var n=JSON.parse(JSON.parse(JSON.stringify(a)));n.staticType=!0,this.$store.commit("saveDragOverData",n),this.$store.commit("toggleSettingActive","kml")}else{var s="【".concat(this.$t("featureDatas.kml.name"),"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:s,type:"success"})}},addVectorExtrudeFeature:function(t,e,a){var i=this,n=null;this.dragData.isDragData?(n=window.scene.addFeature("vectorextrude"),n.name=l["a"]["vectorextrude"].getName()||this.styleFormDatas.name):(n=window.scene.features.get(this.dragData.id),n.name=this.styleFormDatas.name||n.name);var s=JSON.stringify(e);if(e.originalData=s,n.url=t[0].value,n.dataKey=window.scene.postData(e),n.priority=a.priority,n.always=a.always||!1,this.dragData.isDragData)n.load().then((function(){window.scene.fit2Feature(n);var t="【".concat(i.$t("featureDatas.vectorextrude.name"),"】").concat(i.$t("others.added"));i.$message({showClose:!0,message:t,type:"success"}),window.scene.render();var e=JSON.parse(JSON.parse(JSON.stringify(n)));e.staticType=!0,i.$store.commit("saveDragOverData",e),i.$store.commit("toggleSettingActive","vectorextrude")}));else{var o="【".concat(this.$t("featureDatas.vectorextrude.name"),"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:o,type:"success"})}},handleTopAdvancedEnvironmentSet:function(t){this.$store.commit("toggleSettingActive",t)},addPathAnimation:function(t,e){e[1].position;var a=t.type;t.staticType=!0,this.$store.commit("saveDragOverData",t),this.$store.commit("toggleSettingActive",a);var i=this.$store.state.dialog.activeDialog;-1!==i.indexOf("pathAnimation")&&this.$store.commit("toggleActiveDialog","pathAnimation")},addWaterfallFeature:function(t,e){var a=null;this.dragData.isDragData?(a=window.scene.addFeature(this.dragData.type),a.dataKey="".concat(this.dragData.type,"-").concat(a.id),a.name=l["a"][this.dragData.type].getName()||this.styleFormDatas.name):(a=window.scene.features.get(this.dragData.id),a.name=this.styleFormDatas.name||a.name),a.origin=[parseFloat(this.styleFormDatas.longitude+""),parseFloat(this.styleFormDatas.latitude+"")],a.altitude=parseFloat(this.styleFormDatas.altitude+""),a.offset=this.styleFormDatas.offset.map((function(t){return parseFloat(t+"")})),window.scene.postData({width:parseFloat(t.width+""),height:parseFloat(t.height+""),depth:parseFloat(t.depth+"")},a.dataKey);var i="";if(this.dragData.isDragData){a.load(),i="【".concat(this.$t("featureDatas.waterfall.subName"),"】").concat(this.$t("others.added")),window.scene.fit2Feature(a);var n=JSON.parse(JSON.parse(JSON.stringify(a)));n.staticType=!0,this.$store.commit("saveDragOverData",n),this.$store.commit("toggleSettingActive",this.dragData.type)}else i="【".concat(this.$t("featureDatas.waterfall.subName"),"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:i,type:"success"})},setIotElementData:function(t,e,a){void 0!=a&&null!=a&&(a.elementid=t,a.data.title=e.title||"",a.data.elementType=this.dragData.type,this.$store.commit("setCurrentWidgetInfo",a))},handleSubtypeFeatureChanged:function(t,e,a){this.dragData.isDragData&&(this.$bus.emit("subtypeFeatureChanged",{eventType:"add",featureType:t,featureId:e}),a.title=l["a"][t].getName())},addProjectorFeature:function(t,e){var a=null;this.dragData.isDragData?(a=window.scene.addFeature("projector"),a.dataKey="".concat(this.dragData.type,"-").concat(a.id),a.name=l["a"][this.dragData.type].getName()||this.styleFormDatas.name,a.url=e,window.scene.postData(t,a.dataKey)):(a=window.scene.features.get(this.dragData.id),a.name=this.styleFormDatas.name||a.name);var i="";if(this.dragData.isDragData){a.load(),i="【".concat(this.$t("featureDatas.projector.name"),"】").concat(this.$t("others.added"));var n=JSON.parse(JSON.parse(JSON.stringify(a)));n.staticType=!0,this.$store.commit("saveDragOverData",n),this.$store.commit("toggleSettingActive",this.dragData.type)}else i="【".concat(this.$t("featureDatas.projector.name"),"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:i,type:"success"})},addSnowFeature:function(t){var e=null;this.dragData.isDragData?(e=window.scene.addFeature(this.dragData.type),e.dataKey="".concat(this.dragData.type,"-").concat(e.id),e.name=l["a"][this.dragData.type].getName()||this.styleFormDatas.name,window.scene.postData({width:50,height:50,speed:1,density:1},e.dataKey)):(e=window.scene.features.get(this.dragData.id),e.name=this.styleFormDatas.name||e.name),e.origin=[parseFloat(this.styleFormDatas.longitude+""),parseFloat(this.styleFormDatas.latitude+"")],e.altitude=parseFloat(this.styleFormDatas.altitude+""),e.offset=this.styleFormDatas.offset.map((function(t){return parseFloat(t+"")})),e.priority=t.priority,e.always=t.always||!1;var a="";if(this.dragData.isDragData){e.load(),a="【".concat(this.$t("featureDatas.snow.name"),"】").concat(this.$t("others.added")),setTimeout((function(){window.scene.fit2Feature(e)}),800);var i=JSON.parse(JSON.parse(JSON.stringify(e)));i.staticType=!0,this.$store.commit("saveDragOverData",i),this.$store.commit("toggleSettingActive",this.dragData.type)}else a="【".concat(this.$t("featureDatas.snow.name"),"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:a,type:"success"})},addTailLineFeature:function(t,e,a){var i=null;this.dragData.isDragData?(i=window.scene.addFeature("tailline"),i.name=l["a"]["tailline"].getName()||this.styleFormDatas.name):(i=window.scene.features.get(this.dragData.id),i.name=this.styleFormDatas.name||i.name),i.priority=a.priority,i.always=a.always||!1;var n=[];t[1].position.forEach((function(t,e){n[e]=t.map((function(t){return parseFloat(t+"")}))}));var s="";""!=t[0].value&&(s=t[0].value);var o={positions:n,color:e.color,tailMap:s,speed:e.speed,reverse:e.reverse,lineWidth:e.lineWidth};i.dataKey=window.scene.postData(o,i.dataKey);var r="";if(this.dragData.isDragData){i.load(),r="【".concat(this.$t("featureDatas.tailline.name"),"】").concat(this.$t("others.added"));var c=JSON.parse(JSON.parse(JSON.stringify(i)));c.staticType=!0,this.$store.commit("saveDragOverData",c),this.$store.commit("toggleSettingActive","tailline")}else r="【".concat(this.$t("featureDatas.tailline.name"),"】").concat(this.$t("others.updated"));this.$message({showClose:!0,message:r,type:"success"})}}}},"0ee7":function(t,e,a){t.exports=a.p+"static/img/沙地-04.b25d9580.png"},"113c":function(t,e){t.exports="data:image/png;base64,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"},"129f":function(t,e){t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!=t&&e!=e}},"12cd":function(t,e,a){},1367:function(t,e,a){t.exports=a.p+"static/img/柏油路-03.2c5ab5d6.png"},1492:function(t,e,a){var i={"./公交车.png":"ef87","./工人(动态).png":"372f","./空调.png":"d7e9","./草石头.png":"5e5c","./野草_1.png":"da4e","./野草_2.png":"dffd"};function n(t){var e=s(t);return a(e)}function s(t){if(!a.o(i,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return i[t]}n.keys=function(){return Object.keys(i)},n.resolve=s,t.exports=n,n.id="1492"},2007:function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJAAAACQCAMAAADQmBKKAAACmlBMVEUAAAAIGjMSPHciceIlfPklfPYIHTcngv8TJT0mgv/AxsyTnKaFjppaZnjf4uVncoFLWWsaK0OXn6ocLkXHy9C9wsm4vcSutLwvP1QlNUsYKUAzQVbd3+KiqbKVnaeIkZ1OW27l5+rM0dXCyM25v8amrbYfMEYMHzgKHTbz9PXr7e+Bi5drdoU5SFwnN03b3eHQ09jJzdORmaSKk55GVGZDUWQqOU8VKD8PIjn8/PzU19vFyc+1usKqsbp+iJR6hJJve4lodIRXY3Q7Sl02RVne4eS/xMuxt76pr7hyfoxdaXmYoKuOlqFgbHxSX3FQXW9BT2I+TmEOIDnW2t6kq7Umgf+dpa91gI5teYhZZnZVYnM9TWAngP/5+frw8fPp6+7S1dqcpK6MlKA8S18sPFEsgPzO0tdjbn05mf/39/j19vfu7/Hs7e/h5OfY29/X297V2N3Kz9SgqLGboqxlcIAebeEmgf8mgP4if/srgfo1jPUmgP4mgP/T1tqzucCLlJ8ngf8ngv8ngv8ngv8tiv0zd+8mgf8hdOkngP9JV2kmgf8ngf8mgf8ngP8ngP8ngv8igPk2kvYsif8mhf/+/v4RlfoOoPw/qfdXu/omgf8jcuUmgf8ngf8ld+wjZdsmgP8ngv8rd/AphP/39/h+0vwlfvoFfPktaOQrYdEMhvZrwvhIn/IsZNgngP8SjvYSPnqFy/gngP8vaOAWlvZ0wfdMrPcPee82iO5csfUUgvAtZ9t4vPcuaOMaivItYdJvtPUsZNNFovYmgP7///8tZ+ErZNqE4P8In/8Hmf8Fk/8Djv+Y6P+P5f9z2P8DiP+j7/993f952/9k0f8Gl/9v0f05fucrY9UKpf8BhP9WxP1kxPspofdMofEqYNJUHjyIAAAAwnRSTlMAzNrI/fvMfM548Obj2vfd2M/nz/Hv7evT0c/T9unm49j58vDu6tDNzPz64t7U0fbz8uXk19bSzs3+9PHt6+Hh393a1dT37+zq39vn5dzZ2NbVzfXp6Ojg3trZ1Yb9+/n06OTV0iPz3Ab8/Pr6+PX19PLo59zbtqVgHRL79vTs5OBuWFAuD+Tb2tfNva2Wj0hBKhcL/v79/fnb1tTFvLmeZzs1/fr59/fz7ufm5uXd2tnX1cfEu7q5oJuahn94cWNXVdEPMB8AAAe/SURBVHja7NZJbxJhHMdxf2riOEyHAcpWQPZ9a6GUxbIVaqEtslSxlNparGIXW6uNS9zjEo1RL55MPHn24FvovDFnRqKJFxNakMT5JjN5DnP4ZJ7J/J9jYmJiYmJiYmJiR9XzNw8PHp48bEfn+cB5Do7jsB2Z59rFg4OBAt1/NGCg5YMBA90bNNCXQQOdEEEi6MhBYykopvjKwHk5FyPnk4JvdqMCoVq+0TqrjW6N9x6UoGBy890EQmaJhM2o1e4x9SVwLW3Gaa5hoM2yqwZdmzVFIpFoL0F5iWeYQicOZDCwDDW3MTOXA6JrcXs2m/V5zgExh7oAKFk+Uy9Bb61fp2eIn8UQ0l+6xDK0rV53SxFxb3tDDmBtZR1opkct6aV4xenMs4pegqCNy7HJCiURWiFJDjQVbNBS4FJ0gtxqK1ciADI7l1d2jAYseFM0egrysvFZfy5H38nlAvBLuRz0lHmWAwncx6xlogO31BCFniL8PQURZo9q0z+mCVKGIpyX+VT0lJvhQbmUNc7uPg2HwxpAEndbCX1WTz1O9RQkeZKgikMbsSA1pwTaRQPZXA+mWW2wDZLdigdJPnYIuVVb0pJh1/VUy9JT0JlcgkJSiSDFbNYQUobNKqZG7g17AV0Vngz4OJDNqC4Q89PQU5pP2p5uGRJUIF7iQM6N81B5CgvGknk7b3IbwdUBfRrCLKEuxLa9HAi0t5cg4BZVnAZoCsPykY1Jdq+qNTqA8rgAGrPzsUMA1AVna4R/zpjt+Z9a5vOwJuRjJT2kty6zQjcF0JVJvg6IY04Ly96CSjlAVp4AnwZcCk0+d/WqsGwLd/i0AMYjQFQa+Q+nvQgSQSJIBP1jkGvE73LVquCrOPGriRig1fQfRNgqBm+jOgm+YcXV4CiXEoC1zDAhO8OM9BuUKqnDskxibW3IpbX4GTu4FoB1S0Quv26Ty3V9Bp1Xu1fV6dup87fWZcotq9SzZ7myZwGSZyANjxbCJvQZ5KqHH6t0Zp92RtiycTs0KgBLKxKQ0qmpp2f6DUpn/K2JrK3ASHjQ/qKZHPOQpGOmyIGUKpWl76CASSk15QMz8iUoWluGVIvI2QgCsSEOVG42030HjSdH54ez2F91wrF4XdHav5Ok75QAHiSfn1f3HYTJtG6vBp9HIXxDyuZotZq98BNkr9dt/QdpduNrMf/orNLJgXQ70TGFwtQBNQMBX99BRf28LEUt6JC2o0DvG6PbKtVZAWSjr1ssiaDxMKCXz0/9rc9/gKIucBHCFYiBcOUBQgPIJg4/y27c+758+m997uNwfX93sKb9tWevBgu0fGrAzkOvHwwW6MXdATsx3n3RJcg5Arii4JPhdzEABNE96MG3G12BiHrFsN/I2zonRp2Kbw7AkwhBZEIE4eoSdGq5y9FhZAwhl9S6uFgVzkMzAa4FQLHbsFpv7litlS5Bz651Byqd9ZjPhiUGL/3WkXDv+qbVXLtAaA6VhtLY0KE70Md33Q7Xon16TqafdUxCeENPNGVSE4B/UwJSYjQmux2uP5i3l540ogAMw/MtWrFIYWpJZQYiFAQSoIoiGhEvjUBFxCtC0ShGa1zUxmDihdpa8ZIW77oymmhNu+mmXXTRpIvuuumf6kBsAuzmnDQ574L1E0LC5Dtn9rOkf67TgwajM5Ue7MuDXP2LylG9UilYaiVQyO83E4JOY/OEIEFQugRxpr1KjWHVgq+jTtPt1miGdSoJVCMI/YSgykOOEBR+aLK8iiNuUmB2ur66ttvpSDmbARXdM/X7bVIQxtTCYwFJ03DhNxTy6Ht7G28f0Dq6uvrIQMeXHDFo4M1UE6pTG2N50JOAYAbGb0HOcDhCBlpZJQapGixGf2QxilElHAvdfS2TbneqAPK/NGu1Dfc7CUCZ2BkxSNT9m4NFPHe16mxhwGoEvB6BL6QgAG0eMDbHXNywBbrJMTZYXV2zBdqJ7bAFur5ibGPMfWQLtH3B0YF0zwCxTi2iPFFLBDrYpAI9NXqDgKu9p0GBoow+n29kSvq4Jxd0FstQgWa0edBQCFErirLxvE4ff8vzvFzQ6grlHDPXHHS0BSbb2tQoSbA0waC0Qy7o8pgSZPQEAecUSvM26Q1uHRypLoU80Mk59dFCHrQU0KCkBx1WeBoBayQpD3RYSQuK6iXQnMaA0mpsH+xVQLMAWaD52CklyOkeDyKZhtuK4hSmlhGEBuxyV9jsPkcJSrZ6g7Y5EWoziks8UoygJrqhqrfKAu0d0YIAb3CpE0A6gqLSQ3ZTraMqMKuakQN68Y6jB/HxKKRsGpTWk0gk8ho5oIkKxg7wdtfYAm0tM3bEubzF1m3htV3G7lNXTDB241zaqOSXXf9voKM9xt5akDYqkibOpe/o613aKDaq8lZz668/36GNK+vX90+/v/w44Zgo8/Pbn7/t0UkKwkAQQNG6rOAFGjRrJVtFMqwENw4oLhQVRUUhU3fmmMT5LnZOkULqneDD94PAScPc3Fyhdh0jj2M/cJI0zLKnsYPare/623ScJHx9CmvSgNopuuu6njYvLOkICJxkj3dTLWnVBAyWsoertjQCFPoa56IKOgASey4Es+1FF7CYiYiV5QXQ6EUR+w4Bke2DTduAyGDMzoBKC9WwigKEEEIIIYQQ8r9++/cS0GFMxBYAAAAASUVORK5CYII="},"230a":function(t,e,a){t.exports=a.p+"static/img/地砖-01.d1e0f02d.png"},2797:function(t,e,a){t.exports=a.p+"static/img/地砖-08.b9c401d1.png"},2843:function(t,e,a){},"2f13":function(t,e,a){t.exports=a.p+"static/img/meta-3DTiles.19631555.png"},"372f":function(t,e,a){t.exports=a.p+"static/img/工人(动态).c69be70a.png"},3914:function(t,e,a){t.exports=a.p+"static/img/thumb_gis_annotation.ddff8325.png"},3992:function(t,e,a){t.exports=a.p+"static/img/鹅卵石-04.74e52098.png"},"39f2":function(t,e,a){t.exports=a.p+"static/img/塑胶-03.f0ea4431.png"},"42a9":function(t,e,a){t.exports=a.p+"static/img/鹅卵石-03.53200844.png"},"459b":function(t,e,a){t.exports=a.p+"static/img/地砖-05.80b32b35.png"},"45cf":function(t,e,a){t.exports=a.p+"static/img/鹅卵石-02.9c2c1284.png"},"45da":function(t,e,a){t.exports=a.p+"static/img/地砖-02.bbeb8f0a.png"},"46c0":function(t,e,a){t.exports=a.p+"static/img/柏油路-02.c35f9945.png"},"48a0":function(t,e,a){t.exports=a.p+"static/img/草坪-04.a04aad5b.png"},"4c1b":function(t,e,a){},"4f98":function(t,e,a){t.exports=a.p+"static/img/柏油路-01.6e93823a.png"},"4fa5":function(t,e,a){t.exports=a.p+"static/img/鹅卵石-06.50a3de57.png"},5084:function(t,e,a){t.exports=a.p+"static/img/thumb_noon.313f5036.png"},"521b":function(t,e,a){},"554c":function(t,e,a){t.exports=a.p+"static/img/地砖-10.b179c79f.png"},"58d9":function(t,e,a){t.exports=a.p+"static/img/草坪-02.d41e6018.png"},"5a44":function(t,e){t.exports="data:image/png;base64,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"},"5cf9":function(t,e,a){t.exports=a.p+"static/img/草坪-01.729e425c.png"},"5d3f":function(t,e,a){},"5e5c":function(t,e,a){t.exports=a.p+"static/img/草石头.04f147e9.png"},"608c":function(t,e,a){},"631b":function(t,e,a){t.exports=a.p+"static/img/thumb_gis_satellite_v9.8f4013c4.png"},6546:function(t,e,a){t.exports=a.p+"static/img/thumb_night.ca503ad7.png"},6674:function(t,e,a){t.exports=a.p+"static/img/thumb_gis_arcGIS.a994309f.png"},"690d":function(t,e,a){t.exports=a.p+"static/img/地砖-07.d322540b.png"},"6ac8":function(t,e){t.exports="data:image/png;base64,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"},"6b9a":function(t,e,a){"use strict";a("521b")},"71d2":function(t,e,a){t.exports=a.p+"static/img/thumb_gis_tianditu.127ca95f.png"},7477:function(t,e,a){t.exports=a.p+"static/img/thumb_evening.3b62bacf.png"},"7bd2":function(t,e,a){t.exports=a.p+"static/img/thumb_gis_light_blue.631fec54.png"},"7e32f":function(t,e,a){t.exports=a.p+"static/img/草坪-06.f798633d.png"},"7ecc":function(t,e,a){t.exports=a.p+"static/img/meta-dem.417f07c2.png"},"7f83":function(t,e){t.exports="data:image/png;base64,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"},"841c":function(t,e,a){"use strict";var i=a("c65b"),n=a("d784"),s=a("825a"),o=a("1d80"),r=a("129f"),l=a("577e"),c=a("dc4a"),d=a("14c3");n("search",(function(t,e,a){return[function(e){var a=o(this),n=void 0==e?void 0:c(e,t);return n?i(n,e,a):new RegExp(e)[t](l(a))},function(t){var i=s(this),n=l(t),o=a(e,i,n);if(o.done)return o.value;var c=i.lastIndex;r(c,0)||(i.lastIndex=0);var p=d(i,n);return r(i.lastIndex,c)||(i.lastIndex=c),null===p?-1:p.index}]}))},"855c":function(t,e,a){t.exports=a.p+"static/img/thumb_gis_blackish_green.7ef892ac.png"},"855e":function(t,e){t.exports="data:image/png;base64,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"},"896e":function(t,e,a){"use strict";a("5d3f")},"92df":function(t,e,a){"use strict";a("0119")},9507:function(t,e,a){t.exports=a.p+"static/img/草坪-03.291686d4.png"},9629:function(t,e,a){t.exports=a.p+"static/img/thumb_gis_space_grey.90044b88.png"},"9a49":function(t,e,a){t.exports=a.p+"static/img/thumb_gis_blank.346545a3.png"},a414:function(t,e,a){"use strict";a("4c1b")},a7c9:function(t,e,a){t.exports=a.p+"static/img/地砖-06.01abadea.png"},ab0f:function(t,e,a){t.exports=a.p+"static/img/沙地-02.be56ac58.png"},abfc:function(t,e,a){t.exports=a.p+"static/img/thumb_gis_satellite_streets.afd76146.png"},b39c:function(t,e,a){t.exports=a.p+"static/img/thumb_gis_tianditu_streets.70a12dcb.png"},b634:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("dialogComp",{ref:"linkView",staticClass:"sourceDom",attrs:{hideHeader:!1,needClose:"true",zIndex:"4",draw:!0,left:t.dialogLeft,drag:!0,title:t.setSourceTitle,icon:"icon-details",width:t.dialogWidth,height:t.dialogHeight,isSource:!0,type:"detailInfo",top:t.TopToolbarHeight},on:{close:t.closeDialog},scopedSlots:t._u([{key:"center",fn:function(){return[a("div",{staticClass:"source-container"},[a("div",{staticClass:"right-content",class:{modelContent:"model"==t.dragData.type}},["model"==t.dragData.type?a("ModelObj"):t._e(),"underlay"==t.dragData.type?a("SourceUnderlay",{attrs:{currentData:t.dragData},on:{close:t.closeDialog}}):t._e(),"skybox"==t.dragData.type?a("SourceSkyBox",{attrs:{currentData:t.dragData},on:{close:t.closeDialog}}):t._e(),"gltf"==t.dragData.type||"fbx"==t.dragData.type||"panorama"==t.dragData.type||"_3dTiles"==t.dragData.type?a("SceneMaterialLibrary",{key:t.dataKey,attrs:{currentData:t.dragData,vothingElementArr:t.vothingElementArr},on:{checkedCoordinateAddFeature:t.checkedCoordinateAddFeature}}):t._e(),"polygon"==t.dragData.type?a("SourceElementImage",{attrs:{currentData:t.dragData},on:{freeSketchPolygon:t.freeSketchPolygon}}):t._e()],1)])]},proxy:!0}])})},n=[],s=(a("caad"),a("2532"),a("99af"),function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"resources-label-container"},[a("div",{directives:[{name:"show",rawName:"v-show",value:t.labelDatas.length>0,expression:"labelDatas.length > 0"}],staticClass:"top-filter"},[a("div",{staticClass:"search"},[a("el-input",{attrs:{placeholder:t.$t("dialog.materialLibrary.model.placeholder"),size:"mini"},model:{value:t.searchValue,callback:function(e){t.searchValue=e},expression:"searchValue"}},[a("i",{staticClass:"el-input__icon el-icon-search",attrs:{slot:"prefix"},slot:"prefix"})])],1),a("div",{staticClass:"filter"},[t.toggleMode?a("el-tooltip",{attrs:{effect:"dark",enterable:!1,placement:"top",content:t.$t("dialog.materialLibrary.model.label")}},[a("span",{staticClass:"outline-none cursor-btn",on:{click:function(e){return t.handleToggleMode(!1)}}},[a("CommonSVG",{attrs:{size:"17","icon-class":"list_mode"}})],1)]):a("el-tooltip",{attrs:{effect:"dark",enterable:!1,placement:"top",content:t.$t("dialog.materialLibrary.model.label1")}},[a("span",{staticClass:"outline-none cursor-btn",on:{click:function(e){return t.handleToggleMode(!0)}}},[a("CommonSVG",{attrs:{size:"17","icon-class":"view_mode"}})],1)])],1)]),t.toggleMode?a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"label-content",class:{h100:t.loading},attrs:{"element-loading-spinner":"el-icon-loading","element-loading-background":"rgba(0, 0, 0, 0)"}},t._l(t.filterCurrentDatas,(function(e,i){return a("div",{key:e.featureID,staticClass:"label-item"},[a("div",{staticClass:"thumb"},[a("div",{staticClass:"order-number"},[t._v(t._s(i+1))]),""==e.thumbnail||"Project"==e.thumbnail?a("img",{attrs:{src:t.defaultImg,alt:""}}):a("img",{attrs:{src:"data:image/png;base64,"+e.thumbnail,alt:""}}),a("div",{staticClass:"comSvg"},[a("el-tooltip",{staticClass:"box-item margin-top-5",attrs:{enterable:!1,effect:"dark",content:t.$t("dialog.materialLibrary.model.tooltip"),placement:"top"},nativeOn:{click:function(a){return t.handleAddFeature(e,"default")}}},[a("CommonSVG",{attrs:{color:"#DCDCDC",size:"20","icon-class":"mode_icon"}})],1),a("el-tooltip",{staticClass:"box-item",attrs:{enterable:!1,effect:"dark",content:t.$t("dialog.materialLibrary.model.tooltip1"),placement:"top"},nativeOn:{click:function(a){return t.handleAddFeature(e,"custom")}}},[a("CommonSVG",{attrs:{color:"#DCDCDC",size:"20","icon-class":"model_click_feature"}})],1)],1)]),a("el-tooltip",{staticClass:"box-item",attrs:{"open-delay":"1000",enterable:!1,effect:"dark",content:e.featureName,placement:"bottom"}},[a("p",{staticClass:"title"},[e.loaded&&e.loaded.length>0?a("CommonSVG",{attrs:{color:"rgb(103, 194, 58)",size:"17","icon-class":"point"}}):t._e(),t._v(" "+t._s(e.featureName)+" ")],1)])],1)})),0):t._e(),t.toggleMode?t._e():a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"label-content label-list-content",class:{h100:t.loading},attrs:{"element-loading-spinner":"el-icon-loading","element-loading-background":"rgba(0, 0, 0, 0)"}},t._l(t.filterCurrentDatas,(function(e,i){return a("div",{key:e.featureID,staticClass:"label-item overflow-hidden"},[a("div",{staticClass:"thumb"},[a("div",{staticClass:"order-number"},[t._v(t._s(i+1))]),""==e.thumbnail||"Project"==e.thumbnail?a("img",{attrs:{src:t.defaultImg,alt:""}}):a("img",{attrs:{src:"data:image/png;base64,"+e.thumbnail,alt:""}})]),a("div",{staticClass:"desc"},[a("el-tooltip",{staticClass:"box-item",attrs:{"open-delay":"1000",enterable:!1,effect:"dark",content:e.featureName,placement:"right"}},[a("p",{staticClass:"title"},[e.loaded&&e.loaded.length>0?a("CommonSVG",{attrs:{color:"rgb(103, 194, 58)",size:"17","icon-class":"point"}}):t._e(),t._v(" "+t._s(e.featureName)+" ")],1)]),a("div",{staticClass:"color-98A3B3"},[t._v(t._s(e.createTime.replace(/(.+)T(.+)\.\d+\+?.+/g,"$1 $2")))])],1),a("div",{staticClass:"comSvg-right"},[a("el-tooltip",{staticClass:"box-item margin-top-5",attrs:{enterable:!1,effect:"dark",content:t.$t("dialog.materialLibrary.model.tooltip"),placement:"top"},nativeOn:{click:function(a){return t.handleAddFeature(e,"default")}}},[a("CommonSVG",{attrs:{color:"#DCDCDC",size:"20","icon-class":"mode_icon"}})],1),a("el-tooltip",{staticClass:"box-item margin-left-10 margin-right-10",attrs:{enterable:!1,effect:"dark",content:t.$t("dialog.materialLibrary.model.tooltip1"),placement:"top"},nativeOn:{click:function(a){return t.handleAddFeature(e,"custom")}}},[a("CommonSVG",{attrs:{color:"#DCDCDC",size:"20","icon-class":"model_click_feature"}})],1)],1)])})),0),this.labelDatas.length>0?a("div",{staticClass:"total"},[t._v(" "+t._s(t.$t("dialog.materialLibrary.model.totalText",{num:t.labelDatas.length}))+" ")]):t._e()])}),o=[],r=a("c7eb"),l=a("1da1"),c=(a("d3b7"),a("3ca3"),a("ddb0"),a("4ec9"),a("159b"),a("ac1f"),a("841c"),a("c740"),a("a434"),a("cb29"),!0),d={name:"ModelObj",components:{CommonSVG:function(){return a.e("chunk-51f90655").then(a.bind(null,"17d0"))}},data:function(){return{loading:!1,labelDatas:[],defaultImg:a("5a44"),projectID:"",searchValue:"",toggleMode:!0,modelFeatureID:new Map}},computed:{filterCurrentDatas:function(){var t=[],e=this.searchValue.toLowerCase();return this.labelDatas.forEach((function(a){var i=a.featureName.toLowerCase();-1!=i.search(e)&&t.push(a)})),t}},mounted:function(){this.projectID=this.getProjectId(),this.getAllFeatures(),this.toggleMode=c,this.featuresEventListener("on")},methods:{featuresEventListener:function(t){window.scene.mv.events.featureListChanged[t]("remove",this.handleRemoveModel)},handleAddModel:function(t){var e=window.scene.features.get(t);if("model"==e.type){this.modelFeatureID.set(e.id,e.modelID);var a=this.labelDatas.findIndex((function(t){return t.featureID==e.modelID}));void 0==a.loaded&&(a.loaded=[]),a.loaded.push(!0)}},handleRemoveModel:function(t){if(this.modelFeatureID.has(t)){var e=this.modelFeatureID.get(t),a=this.labelDatas.findIndex((function(t){return t.featureID==e}));this.labelDatas[a].loaded.splice(0,1)}},handleToggleMode:function(t){this.toggleMode=t,c=t},handleAddFeature:function(t,e){"custom"==e?this.$parent.$parent.checkedCoordinateAddFeature(t):this.$parent.$parent.modelDefaultCoordinateAddFeature(t)},getProjectId:function(){return this.$store.state.scene.currentSceneProjectID},getAllFeatures:function(){var t=this;return Object(l["a"])(Object(r["a"])().mark((function e(){var a,i,n;return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a=t.getLoadedModels(),i=t.projectID,t.loading=!0,e.next=5,t.$api.getAllFeatures({VaultID:i});case 5:n=e.sent,n.data.forEach((function(t){if(a.has(t.featureID)){var e=a.get(t.featureID);t.loaded=Array(e).fill(!0)}t.type="model",t.isDragData=!0,t.maxVersion=t.currentVersion})),t.loading=!1,t.labelDatas=n.data;case 9:case"end":return e.stop()}}),e)})))()},getLoadedModels:function(){var t=this,e=new Map;return window.scene.features.forEach((function(a){if("model"==a.type){if(e.has(a.modelID)){var i=e.get(a.modelID);i+=1,e.set(a.modelID,i)}else e.set(a.modelID,1);t.modelFeatureID.set(a.id,a.modelID)}})),e}},beforeDestroy:function(){this.featuresEventListener("off")}},p=d,u=(a("896e"),a("2877")),h=Object(u["a"])(p,s,o,!1,null,"023b6980",null),m=h.exports,g=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"resources-label-container"},[a("div",{staticClass:"label-container"},[a("div",{staticClass:"label-content"},t._l(t.underlayWmtsDatas,(function(e){return a("div",{key:e.id,staticClass:"label-item cursor-btn",on:{click:function(a){return t.handleAddwmtsFeature(e,e.id)}}},[a("div",{class:[{active:e.id==t.underlayRadio},"thumb"]},[a("img",{attrs:{src:e.thumb,alt:""}}),a("i",{staticClass:"icon el-icon-plus"})]),a("p",{staticClass:"title"},[t._v(t._s(e.title))])])})),0),a("div",{staticClass:"padding-left-20 padding-right-20 margin-bottom-10 fieldset-x color-dcdcdc"},[t._v("Mapbox")]),a("div",{staticClass:"label-content"},t._l(t.underlayLabelDatas,(function(e,i){return a("div",{key:i,staticClass:"label-item cursor-btn",on:{click:function(a){return t.handleAddFeature(e,i)}}},[a("div",{class:[{active:i==t.underlayRadio},"thumb"]},[a("img",{attrs:{src:e.thumb,alt:""}}),a("i",{staticClass:"icon el-icon-plus"})]),a("p",{staticClass:"title"},[t._v(t._s(e.title))])])})),0)]),a("div",{staticClass:"projection"},[a("span",[t._v(t._s(t.$t("dialog.materialLibrary.underlay.label")))]),a("el-radio",{staticClass:"margin-right-10",staticStyle:{color:"var(--primary-text-color)"},attrs:{label:"globe"},on:{input:t.setProjection},model:{value:t.projectionValue,callback:function(e){t.projectionValue=e},expression:"projectionValue"}},[t._v(" "+t._s(t.$t("dialog.materialLibrary.underlay.label1"))+" ")]),a("el-radio",{staticStyle:{color:"var(--primary-text-color)"},attrs:{label:"mercator"},on:{input:t.setProjection},model:{value:t.projectionValue,callback:function(e){t.projectionValue=e},expression:"projectionValue"}},[t._v(" "+t._s(t.$t("dialog.materialLibrary.underlay.label2"))+" ")])],1)])},f=[],y=(a("b0c0"),{name:"SourceUnderlay",props:["currentData"],data:function(){return{underlayWmtsDatas:[{title:"天地图",thumb:a("71d2"),url:"",type:"wmts",label:"tianditu",pathname:"/img_w/wmts?tk=1e1e425c920f09544d68abc6d3817d04&SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&FORMAT=tiles"},{title:"天地图(街道)",thumb:a("b39c"),url:"",url1:"",type:"wmts",label:"tianditu_streets",pathname:"/vec_w/wmts?tk=1e1e425c920f09544d68abc6d3817d04&SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&FORMAT=tiles",pathname1:"/cva_w/wmts?tk=1e1e425c920f09544d68abc6d3817d04&SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cva&STYLE=default&TILEMATRIXSET=w&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&FORMAT=tiles"},{title:"ArcGIS Online",thumb:a("6674"),url:"https://services.arcgisonline.com/arcgis/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}",type:"wmts",label:"arcGIS"}],underlayLabelDatas:[{title:"影像注记",thumb:a("abfc"),url:"/ref/style/satellite.json",type:"underlay",label:"satelliteStreets"},{title:"影像",thumb:a("631b"),url:"/ref/style/lightsatellite.json",type:"underlay",label:"satelliteV9"},{title:"黑色",thumb:a("bde1"),url:"/ref/style/dark.json",type:"underlay",label:"black"},{title:"注记",thumb:a("3914"),url:"/ref/style/annotation.json",type:"underlay",label:"annotation"},{title:"白色",thumb:a("dec6"),url:"/ref/style/light.json",type:"underlay",label:"white"},{title:"太空灰",thumb:a("9629"),url:"/ref/style/grey.json",type:"underlay",label:"spaceGrey"},{title:"街道",thumb:a("bef7"),url:"/ref/style/streets.json",type:"underlay",label:"vector"},{title:"墨绿",thumb:a("855c"),url:"/ref/style/darkgreen.json",type:"underlay",label:"blackishGreen"},{title:"荧光",thumb:a("cf80"),url:"/ref/style/globe.json",type:"underlay",label:"fluorescence"},{title:"深蓝科技",thumb:a("bbc9"),url:"/ref/style/darkblue.json",type:"underlay",label:"darkBlue"},{title:"蓝色极简",thumb:a("7bd2"),url:"/ref/style/lightblue.json",type:"underlay",label:"lightBlue"},{title:"空白球体",thumb:a("9a49"),url:"/ref/style/blank.json",type:"underlay",noToken:!0,label:"blank"}],underlayRadio:999,projectionValue:"globe"}},created:function(){this.setUnderlay();var t=window.scene.findFeature("underlay");t&&(this.underlayRadio=this.underlayLabelDatas.findIndex((function(e){return e.title==t.name})),this.projectionValue=t.data.projectionValue);var e=window.scene.findFeature("underlay_wmts");if(e){var a=this.underlayWmtsDatas.findIndex((function(t){return t.title==e.name}));this.underlayRadio=-1*(a+1)}this.setRandomWmtsLink()},methods:{handleAddwmtsFeature:function(t,e){var a=this;if(this.underlayRadio==e)return!1;window.scene.features.has("underlay")||this.addUnderlayFeature(11),window.scene.removeFeature("underlay_wmts_streets",!0),window.scene.removeFeature("underlay_wmts",!0),setTimeout((function(){var i=window.scene.findFeature("underlay_wmts");if(i||(i=window.scene.addFeature("wmts","underlay_wmts")),i.name=t.title,i.url=t.url,i.priority=0,i.always=!1,i.loaded||i.load(),"tianditu_streets"==t.label){var n=window.scene.addFeature("wmts","underlay_wmts_streets");n.name=t.title,n.url=t.url1,n.priority=0,n.always=!1,n.loaded||n.load()}a.$deepUpdateScene("wmts"),a.underlayRadio=e,a.$emit("close"),window.scene.render(),a.$store.commit("setActivedType","")}),500)},setRandomWmtsLink:function(){var t=Math.floor(8*Math.random()),e=window.location.protocol,a="";a="http:"==e?"http://t".concat(t,".tianditu.com"):"https://t".concat(t,".tianditu.gov.cn"),this.underlayWmtsDatas[0].url=a+this.underlayWmtsDatas[0].pathname,this.underlayWmtsDatas[1].url=a+this.underlayWmtsDatas[1].pathname,this.underlayWmtsDatas[1].url1=a+this.underlayWmtsDatas[1].pathname1},setUnderlay:function(){var t=this.$i18n.locale,e=this.$i18n.getLocaleMessage(t).dialog.materialLibrary.underlay.labels;this.underlayLabelDatas.forEach((function(t){t.title=e[t.label]})),this.underlayWmtsDatas.forEach((function(t,a){t.title=e[t.label],t.id=-1*(a+1)}))},setProjection:function(t){var e=this;if(window.scene.features.has("underlay")){var a=window.scene.findFeature("underlay");a.setProjection(t),window.scene.postData({projectionValue:t},a.dataKey),this.$nextTick((function(){e.$deepUpdateScene("underlay")}))}},handleAddFeature:function(t,e){if(!t.noToken){var a=this.beforeValidate();if(!a)return!1}if(this.underlayRadio==e)return!1;"blank"!=t.label&&(window.scene.removeFeature("underlay_wmts",!0),window.scene.removeFeature("underlay_wmts_streets",!0),this.$deepUpdateScene("wmts")),this.underlayRadio=e,this.addUnderlayFeature(e)},beforeValidate:function(){this.$notify.closeAll();var t=window.scene.config.mapboxToken;return""!=t&&void 0!=t&&"YOUR_MAPBOX_ACCESS_TOKEN"!=t||(this.$message.error(this.$t("dialog.materialLibrary.underlay.message")),this.$notify.info({title:this.$t("dialog.materialLibrary.underlay.message2"),dangerouslyUseHTMLString:!0,message:this.$t("dialog.materialLibrary.underlay.message1"),duration:15e3}),!1)},addUnderlayFeature:function(t){var e=this;return Object(l["a"])(Object(r["a"])().mark((function a(){var i,n,s;return Object(r["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(i=window.scene.features.get("underlay"),n=!0,i||(i=window.scene.addFeature("underlay"),n=!1),!e.underlayLabelDatas[t].isMapUrl){a.next=7;break}i.url=e.underlayLabelDatas[t].url,a.next=13;break;case 7:return a.next=9,e.asyncRequestMapBoxUrl(t).catch((function(t){return console.error(t),e.$message.error(e.$t("others.requestError")),"error"}));case 9:if(s=a.sent,"error"!=s){a.next=12;break}return a.abrupt("return",!1);case 12:i.url=s;case 13:i.name=e.underlayLabelDatas[t].title,n?e.$notify({title:e.$t("featureDatas.underlay.name"),message:e.$t("dialog.materialLibrary.updated",{name:i.name}),type:"success"}):(i.load(),e.$store.commit("setSceneUnderlayStatus",{underlayState:!0}),e.$notify({title:e.$t("featureDatas.underlay.name"),message:e.$t("messageTips.AddedToScene"),type:"success"})),window.scene.postData({projectionValue:e.projectionValue},i.dataKey),i.setProjection(e.projectionValue),setTimeout((function(){e.$deepUpdateScene("underlay"),e.$emit("close"),window.scene.render()}),500),e.$store.commit("setActivedType","");case 19:case"end":return a.stop()}}),a)})))()},asyncRequestMapBoxUrl:function(t){var e=this;return new Promise((function(a,i){var n=window.IP_CONFIG.SOURCES_URL+e.underlayLabelDatas[t].url;fetch(n).then((function(t){return t.json()})).then((function(t){t.sprite=window.IP_CONFIG.SOURCES_URL+"/ref/style/sprite",a(t)})).catch((function(t){i(t)}))}))}}}),v=y,b=(a("ea5f"),Object(u["a"])(v,g,f,!1,null,"33bcb034",null)),D=b.exports,w=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"resources-label-container"},[a("div",{staticClass:"label-content"},t._l(t.skyBoxOptions,(function(e,i){return a("div",{key:i,staticClass:"label-item cursor-btn",on:{click:function(e){return t.handleAddFeature(i)}}},[a("div",{class:[{active:i==t.skyBoxValueRadio},"thumb"]},[a("img",{attrs:{src:e.thumb,alt:""}}),a("i",{staticClass:"icon el-icon-plus"})]),a("p",{staticClass:"title"},[t._v(t._s(e.title))])])})),0)])},A=[],S={name:"SourceSkyBox",props:["currentData"],data:function(){return{skyBoxOptions:[{title:"早晨",thumb:a("7f83"),value:"morning",type:"underlay"},{title:"中午",thumb:a("5084"),value:"noon",type:"underlay"},{title:"傍晚",thumb:a("7477"),value:"evening",type:"underlay"},{title:"夜间",thumb:a("6546"),value:"night",type:"underlay"}],skyBoxValueRadio:-1}},created:function(){this.setSkybox();var t=window.scene.findFeature("skybox");t&&(this.skyBoxValueRadio=this.skyBoxOptions.findIndex((function(e){return e.title==t.name})))},methods:{setSkybox:function(){var t=this.$i18n.locale,e=this.$i18n.getLocaleMessage(t).dialog.materialLibrary.skybox.labels;this.skyBoxOptions.forEach((function(t,a){t.title=e[a]}))},handleAddFeature:function(t){this.skyBoxValueRadio=t,this.addSkyboxFeature()},addSkyboxFeature:function(){var t=this,e=window.scene.findFeature("skybox"),a=!0;e?e=window.scene.features.get("skybox"):(e=window.scene.addFeature("skybox"),e.load(),a=!1),e.name=this.skyBoxOptions[this.skyBoxValueRadio].title,e.setTime(this.skyBoxOptions[this.skyBoxValueRadio].value),a?this.$notify({title:this.$t("featureDatas.skybox.name"),message:this.$t("dialog.materialLibrary.updated",{name:e.name}),type:"success"}):this.$notify({title:"".concat(this.$t("featureDatas.skybox.name"),"-").concat(e.name),message:this.$t("messageTips.AddedToScene"),type:"success"}),setTimeout((function(){t.$deepUpdateScene("skybox"),t.$emit("close"),window.scene.render()}),1e3),this.$store.commit("setActivedType","")}}},F=S,x=(a("04de"),Object(u["a"])(F,w,A,!1,null,"61ecf956",null)),C=x.exports,k=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"resources-label-container"},[i("div",{staticClass:"top-filter"},[i("div",{staticClass:"search"},[i("el-input",{attrs:{placeholder:t.$t("dialog.materialLibrary.placeholder"),size:"mini"},model:{value:t.searchValue,callback:function(e){t.searchValue=e},expression:"searchValue"}},[i("i",{staticClass:"el-input__icon el-icon-search",attrs:{slot:"prefix"},slot:"prefix"})])],1),!t.isVothing&&t.isFilterVisible?i("div",{staticClass:"filter"},[i("el-popover",{attrs:{placement:"bottom",trigger:"click",width:"200"},model:{value:t.popoverVisible,callback:function(e){t.popoverVisible=e},expression:"popoverVisible"}},[i("ul",{staticClass:"material-filter-item"},t._l(t.filterKey,(function(e){return i("li",{key:e,class:{active:e==t.currentKey},on:{click:function(a){return t.materialFilter(e)}}},[t._v(" "+t._s(e)+" ")])})),0),i("div",{staticClass:"cursor-btn",attrs:{slot:"reference"},slot:"reference"},[i("CommonSVG",{attrs:{size:"18","icon-class":"screening"}}),i("span",{staticClass:"margin-left-5"},[t._v(t._s(t.currentKey))])],1)])],1):t._e()]),""!=t.searchValue||t.loading?t._e():i("div",{staticClass:"custom-btn cursor-btn",on:{click:function(e){return t.addCustom()}}},[i("img",{attrs:{src:a("e11d")}})]),t.isVothing&&t.isTab?i("div",{staticClass:"vo-custom-tab",style:""===t.searchValue||t.loading?"":"margin-top: 35px;"},[i("span",{class:"sys"===t.isType?"tabs active":"tabs",on:{click:function(e){return t.getSourceType("sys")}}},[t._v(" "+t._s(t.$t("dialog.materialLibrary.label"))+" ")]),i("span",{class:"my"===t.isType?"tabs active":"tabs",on:{click:function(e){return t.getSourceType("my")}}},[t._v(" "+t._s(t.$t("dialog.materialLibrary.label1"))+" ")])]):t._e(),i("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"label-content",class:{h100:t.loading,"pd-t40":t.isVothing&&""!==t.searchValue&&"panorama"===t.currentData.type||!t.isVothing&&""!==t.searchValue},attrs:{"element-loading-spinner":"el-icon-loading","element-loading-background":"rgba(0, 0, 0, 0)"}},t._l(t.filterCurrentDatas,(function(e,a){return i("div",{key:e.title,staticClass:"label-item",on:{click:function(e){return t.setCurIndex(a)}}},[i("div",{staticClass:"thumb"},[i("img",{attrs:{src:t.getItemImg(e.thumb),alt:e.title}}),i("div",{staticClass:"comSvg"},[i("el-tooltip",{staticClass:"box-item",attrs:{enterable:!1,effect:"dark",content:t.$t("dialog.materialLibrary.tooltip"),placement:"top"},nativeOn:{click:function(a){return t.addFgltfFbxFeature(e,0)}}},[i("CommonSVG",{attrs:{color:"#DCDCDC",size:"20","icon-class":"once"}})],1),!t.isVothing||t.isVothing&&t.isTab?i("el-tooltip",{staticClass:"box-item",attrs:{enterable:!1,effect:"dark",content:t.$t("dialog.materialLibrary.tooltip1"),placement:"top"},nativeOn:{click:function(a){return t.addFgltfFbxFeature(e,1)}}},[i("CommonSVG",{attrs:{color:"#DCDCDC",size:"20","icon-class":"continuous_feature"}})],1):t._e()],1)]),i("el-tooltip",{staticClass:"box-item",attrs:{"open-delay":"1000",enterable:!1,effect:"dark",content:e.title,placement:"bottom"}},[i("p",{staticClass:"title"},[t._v(t._s(e.title))])])],1)})),0)])},E=[],T=a("2909"),I=(a("a630"),a("5319"),a("1276"),{name:"SceneMaterialLibrary",props:["currentData","vothingElementArr"],components:{CommonSVG:function(){return a.e("chunk-51f90655").then(a.bind(null,"17d0"))}},data:function(){return{materialList:[],loading:!0,allDatas:new Map,currentDatas:[],filterKey:[this.$t("dialog.materialLibrary.all")],currentKey:this.$t("dialog.materialLibrary.all"),popoverVisible:!1,searchValue:"",category:"gltf",curIndex:"",isType:"sys",isTab:!1,betaData:[],isFilterVisible:!0}},created:function(){this.isVothing?this.getVothingData():(this.loading=!1,this.isFilterVisible=!1)},computed:{filterCurrentDatas:function(){var t=this,e=[];return this.currentDatas.forEach((function(a){-1!=a.title.search(t.searchValue)&&e.push(a)})),e},activeDialog:function(){return this.$store.state.dialog.activeDialog},projectId:function(){return window.localStorage.getItem("cj-projectId")},isVothing:function(){return this.$store.state.menuList.isVothing}},methods:{getItemImg:function(t){if(!this.isVothing)return t;if(t)return"panorama"===this.currentData.type?"".concat(window.IP_CONFIG.PANO_URL,"/Panorama").concat(t,"/cover.png"):"http"===t.substring(0,4)?t:window.IP_CONFIG.BASE_URL+t;switch(this.currentData.type){case"dem":return a("7ecc");case"gltf":case"fbx":return a("dc7c");case"_3dTiles":return a("2f13");case"video":return a("05c2");case"wmts":case"wms":case"tms":return a("dec5");case"geoJSON":case"shp":case"kml":return a("c14d")}},setCurIndex:function(t){this.curIndex=t===this.curIndex?"":t},addCustom:function(){this.activeDialog.includes("ElementLink")||(this.curIndex="",this.$store.commit("toggleActiveDialog","ElementLink"),this.$store.commit("toggleActiveDialog","Source"))},addFgltfFbxFeature:function(t,e){this.$emit("checkedCoordinateAddFeature",{item:t,addType:e})},handleAddFeature:function(t){this.$emit("checkedCoordinateAddFeature",t)},materialFilter:function(t){var e=this;if(this.currentKey=t,this.searchValue="",this.currentDatas=[],t==this.$t("dialog.materialLibrary.all")){var a=Array.from(this.allDatas.values());a.forEach((function(t){var a;(a=e.currentDatas).push.apply(a,Object(T["a"])(t))}))}else this.isVothing&&t===this.$t("dialog.materialLibrary.vothing.label")?this.currentDatas=this.betaData:this.currentDatas=this.allDatas.get(t);this.popoverVisible=!1},getAllList:function(){var t=this;return Object(l["a"])(Object(r["a"])().mark((function e(){var a,i;return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a=t.category,e.next=3,t.$api.getAllMaterialList();case 3:i=e.sent,i.data.length>0&&(i.data.forEach((function(e){var i;0!=e.Materials.length&&(e.Materials.forEach((function(t){t.thumb=window.IP_CONFIG.BASE_URL+t.thumb,t.type=a,t.isDragData=!0,t.defaultSource=!0})),t.allDatas.set(e.CategoryName,e.Materials),(i=t.currentDatas).push.apply(i,Object(T["a"])(e.Materials)))})),t.filterKey=t.filterKey.concat(Array.from(t.allDatas.keys()))),t.loading=!1;case 6:case"end":return e.stop()}}),e)})))()},getVothingData:function(){var t=this;return Object(l["a"])(Object(r["a"])().mark((function e(){return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.loading=!1,"gltf"!==t.currentData.type&&"fbx"!==t.currentData.type||(t.isTab=!0,t.betaData=[],t.vothingElementArr.forEach((function(e,a){t.betaData.push({title:e.ElementName,thumb:e.ElementLogo,fileSrc:e.ElementValue,isDragData:!0,type:t.currentData.type,defaultSource:!0,IsSystem:e.IsSystem})})),t.betaData.length>0?t.getSourceType("sys"):t.addCustom()),"panorama"===t.currentData.type&&(t.isTab=!1,t.betaData=[],t.vothingElementArr.forEach((function(e,a){t.betaData.push({title:e.PbName,thumb:e.PbUrl,fileSrc:e.PbUrl,isDragData:!0,type:t.currentData.type,defaultSource:!0})})),t.currentDatas=t.betaData);case 3:case"end":return e.stop()}}),e)})))()},getSourceType:function(t){this.isType=t;var e=[];this.betaData.forEach((function(a){"sys"===t&&a.IsSystem&&e.push(a),"my"!==t||a.IsSystem||e.push(a)})),this.currentDatas=e},getPublicImg:function(){var t=this,e=this.category,i=null;switch(e){case"gltf":i=a("1492");break;case"fbx":i=a("0d5b");break;case"environment":i=a("ee57");break}i.keys().forEach((function(a,i){var n=a.replace(/(.*\/)*([\s\S]*?)/gi,"$2"),s=n.split(".")[0],o="".concat("","/image/").concat(e,"/").concat(n);if("environment"==e)t.materialList.push({id:i,title:s,thumb:o,isDragData:!0,type:e,defaultSource:!0});else{var r="/".concat(e,"/").concat(s,".").concat(e);t.materialList.push({id:i,title:s,thumb:o,fileSrc:r,isDragData:!0,type:e,defaultSource:!0})}}))}}}),O=I,M=(a("a414"),Object(u["a"])(O,k,E,!1,null,"ee296a2a",null)),P=M.exports,N=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"resources-label-container"},[i("div",{staticClass:"custom-btn",on:{click:function(e){return t.addCustom()}}},[i("img",{attrs:{src:a("e11d")}})]),t.isVothing?i("div",{staticClass:"vo-custom-tab"},[i("span",{class:"sys"===t.isType?"tabs active":"tabs",on:{click:function(e){return t.getSourceType("sys")}}},[t._v(" "+t._s(t.$t("dialog.materialLibrary.label"))+" ")]),i("span",{class:"my"===t.isType?"tabs active":"tabs",on:{click:function(e){return t.getSourceType("my")}}},[t._v(" "+t._s(t.$t("dialog.materialLibrary.label1"))+" ")])]):t._e(),i("div",{staticClass:"label-content"},t._l(t.imgList,(function(e){return i("div",{key:e.id,staticClass:"label-item cursor-btn",on:{click:function(a){return t.handleAddFeature(e)}}},[i("div",{staticClass:"thumb"},[i("img",{attrs:{src:e.thumb,alt:""}}),i("i",{staticClass:"icon el-icon-plus"})]),i("p",{staticClass:"title"},[t._v(t._s(e.fileName))])])})),0)])},B=[],Q=a("b893"),L={name:"SourceElementImage",props:["currentData"],data:function(){return{imgList:[],isType:"sys",allList:[]}},computed:{activeDialog:function(){return this.$store.state.dialog.activeDialog},projectId:function(){return window.localStorage.getItem("cj-projectId")},isVothing:function(){return this.$store.state.menuList.isVothing}},created:function(){this.isVothing&&this.getVothingImgs()},methods:{getVothingImgs:function(){var t=this;return Object(l["a"])(Object(r["a"])().mark((function e(){var a,i;return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a={ProjectId:t.projectId,ElementTypeId:"Texture",PageIndex:1,PageSize:9999,Key:""},e.next=3,t.$api.GetElementList(a);case 3:i=e.sent,200===i.data.ErrorCode&&(t.allList=i.data.Result.List,t.getSourceType("sys"));case 5:case"end":return e.stop()}}),e)})))()},getItemImg:function(t){if(!this.isVothing)return t;if(t)return"http"===t.substring(0,4)?t:window.IP_CONFIG.BASE_URL+t;switch(this.currentData.type){case"dem":return a("7ecc");case"gltf":case"fbx":return a("dc7c");case"_3dTiles":return a("2f13");case"video":return a("05c2");case"wmts":case"wms":case"tms":return a("dec5");case"geoJSON":case"shp":case"kml":return a("c14d")}},getSourceType:function(t){var e=this;this.isType=t;var a=[];this.allList.forEach((function(i){"sys"===t&&i.IsSystem&&a.push({id:i.ElementId,title:e.$t("featureDatas.polygon.extend.environment.name"),fileName:i.ElementName,thumb:e.getItemImg(i.ElementValue),isDragData:!0,type:e.currentData.type,subType:e.currentData.subType,defaultSource:!0}),"my"!==t||i.IsSystem||a.push({id:i.ElementId,title:e.$t("featureDatas.polygon.extend.environment.name"),fileName:i.ElementName,thumb:e.getItemImg(i.ElementValue),isDragData:!0,type:e.currentData.type,subType:e.currentData.subType,defaultSource:!0})})),this.imgList=a},getPublicImg:function(){var t=this,e=this.currentData.subType,i=null;switch(e){case"environment":i=a("ee57");break}i.keys().forEach((function(a,i){var n=a.replace(/(.*\/)*([\s\S]*?)/gi,"$2"),s=n.split(".")[0],o="".concat("","/image/").concat(e,"/").concat(n);if("environment"==e)t.imgList.push({id:i,title:t.$t("featureDatas.polygon.extend.environment.name"),fileName:s,thumb:o,isDragData:!0,type:t.currentData.type,subType:t.currentData.subType,defaultSource:!0});else{var r="/".concat(e,"/").concat(s,".").concat(e);t.imgList.push({id:i,title:s,thumb:o,fileSrc:r,isDragData:!0})}}))},addCustom:function(){this.activeDialog.includes("ElementLink")||(this.$store.commit("toggleActiveDialog","ElementLink"),this.$store.commit("toggleActiveDialog","Source"))},closeDialog:function(){this.$emit("close")},checkedElementSource:function(t){var e=this.dragData.type,a=window.location.origin+Object(Q["g"])()+t.thumb;this.styleFormDatas.datas[e].list[0].value=a,this.freeSketchPolygon("polygon")},handleAddFeature:function(t){this.$emit("freeSketchPolygon","polygon",t),this.closeDialog()}}},V=L,R=(a("92df"),Object(u["a"])(V,N,B,!1,null,"7ffa8b9d",null)),J=R.exports,U=a("0e1b"),j={name:"Source",props:["vothingElementArr"],mixins:[U["a"]],components:{ModelObj:m,SourceUnderlay:D,SourceSkyBox:C,SceneMaterialLibrary:P,SourceElementImage:J},data:function(){return{currentData:{title:"",type:"",name:""},tablabel:"one",currentMenu:{},dialogLeft:0,sceneLabelDatas:[],dialogHeight:200,dialogWidth:292,TopToolbarHeight:122,currentSelectSource:null,currentListenerEvent:"",addType:0,dataKey:Date.now()}},computed:{sourceCurrentTypeEq:function(){return this.$store.state.dialog.sourceCurrentTypeEq},dragData:function(){var t=["gltf","fbx","panorama","_3dTiles"];return t.includes(this.$store.state.scene.dragOverData.type)&&(this.dataKey=Date.now()),this.$store.state.scene.dragOverData},setSourceTitle:function(){var t={model:this.$t("featureDatas.model.name"),underlay:this.$t("featureDatas.underlay.name"),skybox:this.$t("featureDatas.skybox.name"),panorama:this.$t("featureDatas.panorama.name"),_3dTiles:this.$t("featureDatas._3dTiles.name"),gltf:this.$t("dialog.materialLibrary.gltf"),fbx:this.$t("dialog.materialLibrary.fbx"),polygon:this.$t("dialog.materialLibrary.polygon")};return t[this.dragData.type]},isVothing:function(){return this.$store.state.menuList.isVothing}},created:function(){var t=document.querySelector(".sceneManageDom").style.left;parseInt(t)<0?this.dialogLeft=30:this.dialogLeft=335;var e=["skybox","underlay"];e.includes(this.dragData.type)||this.changeManageSize(),"skybox"==this.dragData.type?(this.dialogWidth=385,this.dialogHeight=185):"underlay"==this.dragData.type?this.dialogHeight=430:(this.dialogWidth=292,this.dialogHeight=100,this.changeManageSize()),window.addEventListener("resize",this.changeManageSize)},watch:{"dragData.type":function(t){"skybox"==t?(this.dialogWidth=385,this.dialogHeight=185):"underlay"==t?(this.dialogWidth=292,this.dialogHeight=430):(this.dialogWidth=292,this.dialogHeight=100,this.changeManageSize()),this.offEvents()}},mounted:function(){},methods:{initData:function(){this.currentData.type=this.dragData.type,this.currentData.title=this.dragData.title},onCheckedCoordinate:function(t){var e=[0,0,0],a=window.scene.mv._THREE;try{var i=window.scene.queryPosition(new a.Vector2(t.clientX,t.clientY));""!=i&&void 0!=i&&(e=window.scene.mv.tools.coordinate.vector2mercator(i))}catch(n){console.error(n)}this.styleFormDatas.longitude=e[0],this.styleFormDatas.latitude=e[1],this.styleFormDatas.altitude=e[2],this.addType&&window.scene.mv.status.selectable&&(window.scene.mv.status.selectable=!1),this.addType||this.offEvents(),this.submitSetting()},checkedCoordinateAddFeature:function(t){this.currentSelectSource=t.item?t.item:t,this.addType=t.item?t.addType:0,this.$refs.linkView.$el.style.visibility="hidden",window.scene.mv.status.selectable=!1;var e="",a=this.currentSelectSource.type;switch(a){case"model":e=this.currentSelectSource.featureName;break;case"fbx":e=this.currentSelectSource.title;break;case"gltf":e=this.currentSelectSource.title;break;case"panorama":e=this.currentSelectSource.title;break;case"_3dTiles":e=this.currentSelectSource.title;break}this.currentListenerEvent="onCheckedCoordinate",window.scene.mv.events.singleSelection.on("default",this.onCheckedCoordinate),document.addEventListener("keyup",this.onEscKeyUp),this.addType?(this.$message(this.$t("messageTips.continuousAddition")),window.scene.mv.events.contextmenu.on("default",this.offCheckedCoordinate),window["offCheckedCoordinate"]=this.offCheckedCoordinate):this.$message(this.$t("messageTips.publishSomething",{name:e}))},offCheckedCoordinate:function(){window.scene.mv.events.singleSelection.off("default",this.onCheckedCoordinate),window.scene.mv.events.contextmenu.off("default",this.offCheckedCoordinate),this.$message(this.$t("messageTips.exitContinuousAddition")),this.$store.commit("setActivedType",""),this.closeDialog(),window.scene.mv.status.selectable=!0,this.addType=0,window.offCheckedCoordinate&&(window.offCheckedCoordinate=null,delete window.offCheckedCoordinate)},freeSketchPolygon:function(t,e){this.currentSelectSource=e,window.scene.mv.tools.draw.active(),this.$refs.linkView.$el.style.visibility="hidden",this.$store.commit("toogleDrawState",!0),"polygon"==t?window.scene.mv.tools.draw.startDrawPolygon():"polyline"==t?window.scene.mv.tools.draw.startDrawLine():"rectangle"==t&&window.scene.mv.tools.draw.startDrawPolygon(),this.$message({showClose:!0,message:this.$t("messageTips.freeSketch.defaultMsg"),duration:7e3}),"rectangle"==t?(this.currentListenerEvent="handleFreeSketchRectangle",window.scene.mv.tools.draw.finishDrawEvent.on("default",this.handleFreeSketchRectangle)):(this.currentListenerEvent="handleFreeSketchPolygon",window.scene.mv.tools.draw.finishDrawEvent.on("default",this.handleFreeSketchPolygon)),document.addEventListener("keyup",this.onEscKeyUp)},handleFreeSketchPolygon:function(){var t=window.scene.mv.tools.draw.getAllData(),e=this.currentSelectSource.type,a=this.styleFormDatas.datas[e].list;if(this.$store.commit("toogleDrawState",!1),t.polygon.length>0){var i=t.polygon[0].points.length;if(i-1<=2)this.$message.error(this.$t("messageTips.freeSketch.errorMsg1"));else{for(var n=[],s=0;s<i-1;s++)n[s]=[t.polygon[0].points[s].x,t.polygon[0].points[s].y,t.polygon[0].points[s].z];a[1].position=n;var o=["environment"];if("polygon"==this.currentSelectSource.type&&o.includes(this.currentSelectSource.subType)){var r=window.scene.mv.tools.coordinate.vector2mercator(t.polygon[0].points[0]);this.styleFormDatas.datas["polygon"].list[4].value=r[2]}this.submitSetting()}}else{var l=t.polyline[0].points.length;if(l<=1)this.$message.error(this.$t("messageTips.freeSketch.errorMsg2"));else{for(var c=[],d=0;d<l;d++)c[d]=[t.polyline[0].points[d].x,t.polyline[0].points[d].y,t.polyline[0].points[d].z];a[1].position=c,this.submitSetting()}}window.scene.mv.tools.draw.deactive(),this.offEvents()},onEscKeyUp:function(t){27==t.keyCode&&"Escape"===t.key&&this.offEvents()},offEvents:function(){var t=this;switch(window.scene.mv.tools.draw.deactive(),this.$nextTick((function(){t.$refs.linkView&&(t.$refs.linkView.$el.style.visibility="visible")})),window.scene.mv.status.selectable=!0,""!=this.currentListenerEvent&&document.removeEventListener("keyup",this.onEscKeyUp),this.currentListenerEvent){case"handleFreeSketchRectangle":window.scene.mv.tools.draw.finishDrawEvent.off("default",this.handleFreeSketchRectangle);break;case"handleFreeSketchPolygon":window.scene.mv.tools.draw.finishDrawEvent.off("default",this.handleFreeSketchPolygon);break;case"onCheckedCoordinate":this.addType||window.scene.mv.events.singleSelection.off("default",this.onCheckedCoordinate);break}this.currentListenerEvent=""},changeManageSize:function(){if("skybox"!=this.dragData.type){var t=document.body.clientHeight;this.dialogHeight=100-(2*this.TopToolbarHeight+100)/(t/100)+"%"}},closeDialog:function(){var t=this.$store.state.dialog.activeDialog;t.includes("Source")&&this.$store.commit("toggleActiveDialog","Source"),this.$store.commit("toggleMenuActive","widgetSource")},handleWidgetClick:function(t,e){},submitSetting:function(){var t=this,e=this.currentSelectSource.type,a=this.styleFormDatas.datas[e].list;switch(this.$store.commit("saveDragOverData",this.currentSelectSource),e){case"model":this.addModelFeature(a,{priority:0});break;case"underlay":this.addUnderlayFeature(a);break;case"fbx":this.isVothing&&(a[0].value=this.currentSelectSource.fileSrc,this.addGltfOrFbxFeature(a,{priority:0},this.addType));break;case"gltf":this.addGltfOrFbxFeature(a,{priority:0},this.addType);break;case"panorama":this.isVothing&&(a[0].value="".concat(window.IP_CONFIG.PANO_URL,"/Panorama").concat(this.currentSelectSource.thumb,"/cover.png"),this.addPanoramaFeature(a,{priority:0}));break;case"polygon":var i={color:"rgb(255, 50, 0)",opacity:1,top:0,base:0,interval:100,direction:"x",showline:!1};"environment"===this.dragData.subType&&(i.base=a[4].value),this.addPolygonFeature(a,i,{priority:0});break;case"_3dTiles":this.isVothing&&(this.addLinkViewLoading(),this.add3DTilesFeature(a,{},{priority:0}));break}this.addType||(this.currentSelectSource=null),"model"!=e&&setTimeout((function(){t.$deepUpdateScene(e),window.scene.render()}),1e3),this.addType||this.closeDialog()},modelDefaultCoordinateAddFeature:function(t){if(this.currentSelectSource=t.item?t.item:t,!t.extension||""==t.extension)return this.submitSetting(),!1;var e=JSON.parse(t.extension);e.origin&&(this.styleFormDatas.longitude=e.origin[1],this.styleFormDatas.latitude=e.origin[0]),e.altitude&&(this.styleFormDatas.altitude=e.altitude),e.rotation&&(this.styleFormDatas.rotation=e.rotation),e.offset&&(this.styleFormDatas.offset=e.offset),this.submitSetting()}},beforeDestroy:function(){this.offEvents(),window.removeEventListener("resize",this.changeManageSize)}},H=j,K=(a("6b9a"),a("d920"),a("066e"),Object(u["a"])(H,i,n,!1,null,"42744122",null));e["default"]=K.exports},b8fd:function(t,e,a){t.exports=a.p+"static/img/塑胶-05.7c8555dd.png"},bbc9:function(t,e,a){t.exports=a.p+"static/img/thumb_gis_dark_blue.f5b58b32.png"},bde1:function(t,e,a){t.exports=a.p+"static/img/thumb_gis_black.41edc3d4.png"},bef7:function(t,e,a){t.exports=a.p+"static/img/thumb_gis_vector.234492a5.png"},c14d:function(t,e,a){t.exports=a.p+"static/img/meta-shp.0e65e0a0.png"},c74d:function(t,e,a){t.exports=a.p+"static/img/沙地-03.79e37c85.png"},cb29:function(t,e,a){var i=a("23e7"),n=a("81d5"),s=a("44d2");i({target:"Array",proto:!0},{fill:n}),s("fill")},cf80:function(t,e,a){t.exports=a.p+"static/img/thumb_gis_fluorescence.336aac7a.png"},d44b:function(t,e,a){t.exports=a.p+"static/img/鹅卵石-05.e42e95e3.png"},d675:function(t,e,a){t.exports=a.p+"static/img/灌木.96bb3e4b.png"},d7e9:function(t,e){t.exports="data:image/png;base64,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"},d82a:function(t,e,a){t.exports=a.p+"static/img/鹅卵石-01.c5215fcf.png"},d920:function(t,e,a){"use strict";a("608c")},da4e:function(t,e,a){t.exports=a.p+"static/img/野草_1.c35f12ca.png"},dc7c:function(t,e,a){t.exports=a.p+"static/img/meta-gltf.c8b3e119.png"},dec5:function(t,e,a){t.exports=a.p+"static/img/meta-wms.7a364751.png"},dec6:function(t,e,a){t.exports=a.p+"static/img/thumb_gis_white.4d2a11f2.png"},dffd:function(t,e,a){t.exports=a.p+"static/img/野草_2.21e2f50d.png"},e11d:function(t,e){t.exports="data:image/png;base64,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"},e948:function(t,e,a){t.exports=a.p+"static/img/塑胶-01.aba00ddd.png"},ea5f:function(t,e,a){"use strict";a("2843")},eba3:function(t,e,a){t.exports=a.p+"static/img/塑胶-04.dfb349a7.png"},ed11:function(t,e,a){t.exports=a.p+"static/img/地砖-09.4531fcff.png"},ee57:function(t,e,a){var i={"./地砖-01.png":"230a","./地砖-02.png":"45da","./地砖-03.png":"0188","./地砖-04.png":"0659","./地砖-05.png":"459b","./地砖-06.png":"a7c9","./地砖-07.png":"690d","./地砖-08.png":"2797","./地砖-09.png":"ed11","./地砖-10.png":"554c","./塑胶-01.png":"e948","./塑胶-02.png":"f50d","./塑胶-03.png":"39f2","./塑胶-04.png":"eba3","./塑胶-05.png":"b8fd","./柏油路-01.png":"4f98","./柏油路-02.png":"46c0","./柏油路-03.png":"1367","./沙地-01.png":"01de","./沙地-02.png":"ab0f","./沙地-03.png":"c74d","./沙地-04.png":"0ee7","./草坪-01.png":"5cf9","./草坪-02.png":"58d9","./草坪-03.png":"9507","./草坪-04.png":"48a0","./草坪-05.png":"ff0e","./草坪-06.png":"7e32f","./鹅卵石-01.png":"d82a","./鹅卵石-02.png":"45cf","./鹅卵石-03.png":"42a9","./鹅卵石-04.png":"3992","./鹅卵石-05.png":"d44b","./鹅卵石-06.png":"4fa5","./鹅卵石-07.png":"0089"};function n(t){var e=s(t);return a(e)}function s(t){if(!a.o(i,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return i[t]}n.keys=function(){return Object.keys(i)},n.resolve=s,t.exports=n,n.id="ee57"},ee5f:function(t,e,a){t.exports=a.p+"static/img/压路机.823d2da2.png"},ef87:function(t,e,a){t.exports=a.p+"static/img/公交车.b6f5860f.png"},f50d:function(t,e,a){t.exports=a.p+"static/img/塑胶-02.f6f6da07.png"},f6a2:function(t,e){t.exports="data:image/png;base64,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"},fc5e:function(t,e){t.exports="data:image/png;base64,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"},ff0e:function(t,e,a){t.exports=a.p+"static/img/草坪-05.3b3f362b.png"}}]);