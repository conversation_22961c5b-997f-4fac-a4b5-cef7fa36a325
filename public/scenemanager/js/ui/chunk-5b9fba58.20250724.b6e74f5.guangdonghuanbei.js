(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5b9fba58"],{"07ac":function(e,t,i){var s=i("23e7"),n=i("6f53").values;s({target:"Object",stat:!0},{values:function(e){return n(e)}})},"16b0":function(e,t,i){},"5c88":function(e,t,i){"use strict";i("16b0")},ad58:function(e,t,i){"use strict";i.r(t);var s=function(){var e=this,t=e.$createElement,i=e._self._c||t;return e.viewshedMounted?i("div",{staticClass:"bottom-menu-container",class:{"first-story":"model"===e.elementMenu}},[e._l(e.menuList,(function(t,s){return[t.show?i("div",{key:t.label,staticClass:"menu-item",class:{active:s===e.activeMenu},on:{click:function(i){return e.toggleMenu(s,t)}}},[i("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:t.name,placement:"top"}},[i("span",[i("CommonSVG",{attrs:{color:s===e.activeMenu?"#fff":"#DDDDDD","icon-class":t.icon,size:16}})],1)])],1):e._e(),t.slider?["coord"===t.sliderType?[i("transition",{attrs:{name:"sliderFade",mode:"out-in"}},[i("div",{directives:[{name:"show",rawName:"v-show",value:e.activeMenuType===t.label,expression:"activeMenuType === item.label"}],staticClass:"item-slider"},[i("div",{staticClass:"slider-wrap h100 list-div-rotation"},[i("div",{staticClass:"items-center"},[i("div",{staticClass:"input-item"},[i("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:e.visibleData[t.key].x,expression:"visibleData[item.key].x"}],attrs:{type:"number",title:"",size:"mini",placeholder:"X"},on:{input:function(i){return e.inputChangeViewshedDebounce(i,t.key)}},model:{value:e.visibleData[t.key].x,callback:function(i){e.$set(e.visibleData[t.key],"x",i)},expression:"visibleData[item.key].x"}},[i("template",{slot:"append"},[e._v("X")])],2)],1),i("div",{staticClass:"input-item"},[i("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:e.visibleData[t.key].y,expression:"visibleData[item.key].y"}],attrs:{type:"number",title:"",size:"mini",placeholder:"Y"},on:{input:function(i){return e.inputChangeViewshedDebounce(i,t.key)}},model:{value:e.visibleData[t.key].y,callback:function(i){e.$set(e.visibleData[t.key],"y",i)},expression:"visibleData[item.key].y"}},[i("template",{slot:"append"},[e._v("Y")])],2)],1),i("div",{staticClass:"input-item"},[i("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:e.visibleData[t.key].z,expression:"visibleData[item.key].z"}],attrs:{type:"number",title:"",size:"mini",placeholder:"Z"},on:{input:function(i){return e.inputChangeViewshedDebounce(i,t.key)}},model:{value:e.visibleData[t.key].z,callback:function(i){e.$set(e.visibleData[t.key],"z",i)},expression:"visibleData[item.key].z"}},[i("template",{slot:"append"},[e._v("Z")])],2)],1),i("span",{staticClass:"cursor-btn"},[i("CommonSVG",{attrs:{"icon-class":"select_coord",color:e.isQueryPositionEnabled&&e.initParamsType==t.key?"var(--theme)":"",size:16},nativeOn:{click:function(i){return e.toggleQueryPosition(t.key)}}})],1)])])])])]:"input"===t.sliderType?[i("transition",{attrs:{name:"sliderFade1",mode:"out-in"}},[i("div",{directives:[{name:"show",rawName:"v-show",value:e.activeMenuType===t.label,expression:"activeMenuType === item.label"}],staticClass:"item-slider input-slider"},[i("div",{staticClass:"slider-wrap h100 list-div-rotation"},["farthest_distance"!==t.label?i("div",{staticClass:"items-center"},[i("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:e.visibleData[t.key],expression:"visibleData[item.key]"}],attrs:{type:"number",title:"",size:"mini",placeholder:"1~180"},on:{input:function(i){return e.inputChangeViewshedDebounce(i,t.key)}},model:{value:e.visibleData[t.key],callback:function(i){e.$set(e.visibleData,t.key,i)},expression:"visibleData[item.key]"}},[i("template",{slot:"append"},[e._v(" "+e._s(e.$t("sceneMainMenu.analysis.extend.viewable.label"))+" ")])],2)],1):e._e()])])])]:e._e()]:e._e()]})),e.dialogVisible?i("dialogComp",{attrs:{hideHeader:!1,needClose:!0,backgroundColor:"var(--primary-bg-color)",zIndex:"5",draw:!0,right:125,drag:!0,title:e.$t("sceneMainMenu.analysis.extend.viewable.label1"),icon:"icon-details",width:460,dragTopOffset:e.dragTopOffset,height:e.attrHeight,type:"detailInfo",position:"fixed","min-width":300,top:e.attrTop},on:{close:e.closeDialog},scopedSlots:e._u([{key:"center",fn:function(){return[i("div",{staticClass:"content"},[e.dialogList.length>0?[i("div",{staticClass:"list-content virtually-list-content",on:{scroll:e.updateVisibleList}},[i("div",{staticClass:"item t-header"},[i("span",{staticClass:"item-checkbox"},[i("el-checkbox",{attrs:{indeterminate:e.isIndeterminate},on:{change:e.handleCheckAllChange},model:{value:e.checkAll,callback:function(t){e.checkAll=t},expression:"checkAll"}})],1),i("span",{staticClass:"item-text"},[e._v(" "+e._s(e.$t("featureSetting.style.model.label"))+" ")]),i("span",{staticClass:"item-text"},[e._v(" "+e._s(e.$t("dialog.attribute.table.label2"))+" ")]),i("span",{staticClass:"item-text"},[e._v(" "+e._s(e.$t("dialog.attribute.table.label3"))+" ")]),i("span",{staticClass:"item-text"},[e._v(" "+e._s(e.$t("dialog.attribute.table.label1"))+" ")])]),i("div",{staticClass:"virtually-item-content"},e._l(e.visibleList,(function(t){return i("div",{key:t.id,staticClass:"item"},[i("span",{staticClass:"item-checkbox"},[i("el-checkbox",{on:{change:function(i){return e.handleCheckedElementChange(t)}},model:{value:t.checked,callback:function(i){e.$set(t,"checked",i)},expression:"item.checked"}})],1),i("span",{staticClass:"item-text",attrs:{title:t.modelName}},[e._v(e._s(t.modelName))]),i("span",{staticClass:"item-text",attrs:{title:t.name}},[e._v(e._s(t.name))]),i("span",{staticClass:"item-text"},[e._v(e._s(t.elementId))]),i("span",{staticClass:"item-checkbox"},[i("span",{on:{click:function(i){return i.stopPropagation(),e.openAttr(t.id)}}},[i("CommonSVG",{attrs:{color:"#DDDDDD","icon-class":"attribute_feature",size:16}})],1)])])})),0)]),i("div",{staticClass:"bottom-menu"},[i("div",[e._v(e._s(e.$t("formRelational.element.message",{num:e.dialogList.length})))]),i("div",[i("span",{staticClass:"cursor-btn",on:{click:function(t){return e.handlerCheckedElementsEvents("selected")}}},[e._v(" "+e._s(e.$t("menuIconName.selected"))+" ")]),i("span",{staticClass:"cursor-btn margin-left-15",on:{click:function(t){return e.handlerCheckedElementsEvents("zoom")}}},[e._v(" "+e._s(e.$t("menuIconName.zoom"))+" ")])])])]:e._e(),0===e.dialogList.length?i("div",{staticClass:"no-data"},[e._v(" "+e._s(e.$t("sceneMainMenu.analysis.extend.viewable.message"))+" ")]):e._e()],2)]},proxy:!0}],null,!1,2679130874)}):e._e()],2):e._e()},n=[],a=(i("d3b7"),i("3ca3"),i("ddb0"),i("07ac"),i("159b"),i("a434"),i("b0c0"),i("ac1f"),i("1276"),i("fb6a"),i("f7fe")),o=i.n(a),l={name:"visibleMenu",components:{CommonSVG:function(){return i.e("chunk-51f90655").then(i.bind(null,"17d0"))}},props:["compKey"],data:function(){return{menuList:[{name:this.$t("bottomMenu.visibleMenu.label"),label:"camera_position",icon:"camera_position",show:!0,slider:!0,sliderType:"coord",key:"cameraPosition"},{name:this.$t("bottomMenu.visibleMenu.label1"),label:"target",icon:"target",show:!0,slider:!0,sliderType:"coord",key:"targetPosition"},{name:this.$t("bottomMenu.visibleMenu.label2"),label:"level_angle_feature",icon:"level_angle_feature",show:!0,slider:!0,sliderType:"input",key:"horizontalFov"},{name:this.$t("bottomMenu.visibleMenu.label3"),label:"perpendicularity_feature",icon:"perpendicularity_feature",show:!0,slider:!0,sliderType:"input",key:"verticalFov"},{name:this.$t("bottomMenu.visibleMenu.label4"),label:"element",icon:"element",show:!0},{name:this.$t("menuIconName.exit"),label:"quit",icon:"quit",show:!0}],isQueryPositionEnabled:!1,activeMenu:"",activeMenuType:"",dialogList:[],visibleList:[],visibleData:{horizontalFov:45,verticalFov:45,cameraPosition:{x:0,y:0,z:0},targetPosition:{x:0,y:0,z:0},maxDistance:300},dialogVisible:!1,initParamsType:"cameraPosition",attrHeight:500,attrTop:170,viewshedMounted:!1,checkAll:!1,isIndeterminate:!1,checkedElements:[]}},computed:{dragTopOffset:function(){return this.$store.state.scene.topToolBarHeight},elementMenu:function(){return this.$store.state.menuList.elementMenu}},created:function(){window.scene.clearSelection(),this.$message.info(this.$t("bottomMenu.visibleMenu.message")),this.$notify({title:this.$t("bottomMenu.visibleMenu.label5"),dangerouslyUseHTMLString:!0,message:this.$t("bottomMenu.visibleMenu.message1"),duration:17e3}),window.scene.mv.status.selectable=!1,window.scene.mv.events.singleSelection.on("default",this.onCheckedCoordinate),document.addEventListener("keyup",this.onEscKeyUp),this.inputChangeViewshedDebounce=o()(this.inputChangeViewshedDebounce,1e3)},methods:{inputChangeViewshedDebounce:function(e,t){switch(t){case"cameraPosition":case"targetPosition":var i=Object.values(this.visibleData[t]);this.computeViewshedCoordinate(i,t),this.initParamsType="";break;case"horizontalFov":var s=parseFloat(e+"");window.scene.mv.tools.viewshed.horizontalFov=s*(Math.PI/180);break;case"verticalFov":var n=parseFloat(e+"");window.scene.mv.tools.viewshed.verticalFov=n*(Math.PI/180);break}setTimeout((function(){window.scene.render()}))},handlerCheckedElementsEvents:function(e){if(0!=this.checkedElements.length){window.scene.clearSelection();var t=window.scene.createArrayObject(this.checkedElements);t.select(),"zoom"==e&&window.scene.fit(this.checkedElements),setTimeout((function(){window.scene.render()}))}else this.$message.error(this.$t("messageTips.checkOne"))},handleCheckAllChange:function(e){var t=this;e?this.dialogList.forEach((function(e){t.checkedElements.push(e.id),e.checked=!0})):(this.checkedElements=[],this.dialogList.forEach((function(e){e.checked=!1}))),this.isIndeterminate=!1},handleCheckedElementChange:function(e){var t=this,i=this.checkedElements.indexOf(e.id);i<0?this.checkedElements.push(e.id):this.checkedElements.splice(i,1),this.$nextTick((function(){t.checkedElements.length>0&&t.checkedElements.length<t.dialogList.length&&(t.isIndeterminate=!0),0===t.checkedElements.length&&(t.isIndeterminate=!1),t.checkedElements.length==t.dialogList.length&&(t.isIndeterminate=!1)}))},onCheckedCoordinate:function(e){var t=window.scene.mv._THREE,i=window.scene.queryPosition(new t.Vector2(e.clientX,e.clientY));""!=i&&void 0!=i||(i={x:0,y:0,z:0}),window.scene.mv.events.singleSelection.off("default",this.onCheckedCoordinate),"cameraPosition"==this.initParamsType?this.addTemporaryAnnotationFeature(i):this.initTargetPosition(i)},addTemporaryAnnotationFeature:function(e){this.visibleData.cameraPosition={x:e.x,y:e.y,z:e.z};var t=window.scene.mv.tools.coordinate.vector2mercator(e),i={position:{x:0,y:0,z:0},content:"",htmlCode:'<div class="triggerScatterPlot"></div>',cssCode:"",jsCode:"",minDistance:0,maxDistance:1/0},s="trigger-center-annotation",n=window.scene.addFeature("annotation",s);n.origin=[t[0],t[1]],n.altitude=t[2],n.name=this.$t("bottomMenu.visibleMenu.label"),n.offset=[0,0,0],n.dataKey="annotation-"+n.id,window.scene.postData(i,n.dataKey),n.load(),window.scene.render(),this.$message.info(this.$t("bottomMenu.visibleMenu.message2")),this.initParamsType="targetPosition",window.scene.mv.events.singleSelection.on("default",this.onCheckedCoordinate)},initTargetPosition:function(e){this.visibleData.targetPosition={x:e.x,y:e.y,z:e.z},document.removeEventListener("keyup",this.onEscKeyUp),window.scene.removeFeature("trigger-center-annotation",!0),this.setViewshed()},setViewshed:function(){var e=window.scene.mv.tools.coordinate.scenePosition(Object.values(this.visibleData.cameraPosition)),t=window.scene.mv.tools.coordinate.scenePosition(Object.values(this.visibleData.targetPosition)),i=e.distanceTo(t);window.scene.mv.tools.viewshed.active({horizontalFov:this.visibleData.horizontalFov*(Math.PI/180),verticalFov:this.visibleData.verticalFov*(Math.PI/180),cameraPosition:e,targetPosition:t,maxDistance:i}),window.scene.render(),this.initParamsType="",this.viewshedMounted=!0},toggleMenu:function(e,t){var i=this,s=t.label;switch(s){case"quit":this.exit(!0);break;case"element":if(!this.dialogVisible){this.setDialogSize(),this.dialogVisible=!0;var n=window.scene.mv.tools.viewshed.query();n.forEach((function(e){i.dialogList.push({modelName:e.model.name,id:e.id,name:e.name,elementId:e.id.split("^")[1],checked:!1})})),this.$nextTick((function(){i.updateVisibleList()}))}break;case"camera_position":case"target":case"level_angle_feature":case"perpendicularity_feature":this.closeDialog();break}e<this.menuList.length&&(this.activeMenu=e),this.activeMenuType=t.label},toggleQueryPosition:function(e){this.isQueryPositionEnabled&&this.quitSetViewshedBasePoint(),this.initParamsType!=e?(this.isQueryPositionEnabled=!0,this.initParamsType=e,window.scene.mv.events.mousedown.on("default",this.setViewshedBasePoint),this.$message.info(this.$t("messageTips.checkCoordinate"))):this.initParamsType=""},setViewshedBasePoint:function(e){var t=this,i=window.scene,s=i.queryPosition(new i.mv._THREE.Vector2(e.clientX,e.clientY));s&&(this.visibleData[this.initParamsType]={x:s.x,y:s.y,z:s.z},this.computeViewshedCoordinate(s,this.initParamsType),setTimeout((function(){window.scene.render(),t.quitSetViewshedBasePoint(),t.initParamsType=""}),200))},computeViewshedCoordinate:function(e,t){var i=window.scene.mv.tools.coordinate.scenePosition(e);"cameraPosition"==t?window.scene.mv.tools.viewshed.cameraPosition=i:window.scene.mv.tools.viewshed.targetPosition=i;var s=window.scene.mv.tools.viewshed.cameraPosition.distanceTo(window.scene.mv.tools.viewshed.targetPosition);window.scene.mv.tools.viewshed.maxDistance=s},quitSetViewshedBasePoint:function(){window.scene.mv.events.mousedown.off("default",this.setViewshedBasePoint),this.isQueryPositionEnabled=!1},closeDialog:function(){this.dialogList=[],this.activeMenuType="",this.activeMenu="",this.checkAll=!1,this.checkedElements=[],this.isIndeterminate=!1,this.dialogVisible=!1,window.scene.clearSelection(),window.scene.render();var e=this.$store.state.dialog.activeDialog;e.indexOf("attribute")>0&&this.$store.commit("toggleActiveDialog","attribute")},openAttr:function(e){var t=this,i=this.$store.state.dialog.activeDialog;i.indexOf("attribute")<0&&this.$store.commit("toggleActiveDialog","attribute"),window.scene.findObject(e).selected=!0,this.$nextTick((function(){t.$bus.emit("viewshedShowAttribute",e),window.scene.render()}))},exit:function(e){this.$store.commit("toogleBottomChildMenuActive",""),e&&this.$store.commit("toggleBottomMenuActive","")},onEscKeyUp:function(e){if(""==this.currentFreeSketch)return!1;27==e.keyCode&&"Escape"===e.key&&(window.scene.removeFeature("trigger-center-annotation",!0),this.viewshedMounted||this.exit(!0))},setDialogSize:function(){var e=document.body.clientHeight,t=document.querySelector(".desktop-view").clientHeight;0==t?(this.attrHeight=e-40-30,this.attrTop=50):(this.attrHeight=e-t-30,this.attrTop=t+10)},updateVisibleList:function(){var e=document.querySelector(".virtually-list-content"),t=document.querySelector(".virtually-item-content"),i=document.querySelector(".virtually-list-content").querySelector(".t-header"),s=Math.floor(e.scrollTop/40),n=s+Math.ceil(e.clientHeight/40)+4;this.visibleList=this.dialogList.slice(s,n),t.style.top="".concat(40*s+40,"px"),i.style.top="".concat(e.scrollTop,"px")}},beforeDestroy:function(){this.isQueryPositionEnabled&&this.quitSetViewshedBasePoint(),this.viewshedMounted?(window.scene.mv.tools.viewshed.deactive(),this.exit()):(document.removeEventListener("keyup",this.onEscKeyUp),window.scene.mv.events.singleSelection.off("default",this.onCheckedCoordinate));var e=this.$store.state.dialog.activeDialog;e.indexOf("attribute")>=0&&this.$store.commit("toggleActiveDialog","attribute"),this.$store.commit("toogleElementMenu",""),window.scene.mv.status.selectable=!0,window.scene.clearSelection()}},c=l,r=(i("c052"),i("5c88"),i("2877")),d=Object(r["a"])(c,s,n,!1,null,"1b802961",null);t["default"]=d.exports},c052:function(e,t,i){"use strict";i("f62e")},f62e:function(e,t,i){}}]);