(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-e0e72d4a"],{"0538":function(e,t,n){"use strict";var o=n("e330"),i=n("59ed"),s=n("861d"),a=n("1a2d"),r=n("f36a"),c=n("40d5"),l=Function,u=o([].concat),d=o([].join),m={},h=function(e,t,n){if(!a(m,t)){for(var o=[],i=0;i<t;i++)o[i]="a["+i+"]";m[t]=l("C,a","return new C("+d(o,",")+")")}return m[t](e,n)};e.exports=c?l.bind:function(e){var t=i(this),n=t.prototype,o=r(arguments,1),a=function(){var n=u(o,r(arguments));return this instanceof a?h(t,n.length,n):t.apply(e,n)};return s(n)&&(a.prototype=n),a}},"150f":function(e,t,n){"use strict";n("52d5")},4478:function(e,t,n){"use strict";n.d(t,"a",(function(){return s}));n("4ae1"),n("d3b7"),n("f8c9");function o(e,t){return o=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},o(e,t)}function i(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}function s(e,t,n){return s=i()?Reflect.construct.bind():function(e,t,n){var i=[null];i.push.apply(i,t);var s=Function.bind.apply(e,i),a=new s;return n&&o(a,n.prototype),a},s.apply(null,arguments)}},"4ae1":function(e,t,n){var o=n("23e7"),i=n("d066"),s=n("2ba4"),a=n("0538"),r=n("5087"),c=n("825a"),l=n("861d"),u=n("7c73"),d=n("d039"),m=i("Reflect","construct"),h=Object.prototype,f=[].push,p=d((function(){function e(){}return!(m((function(){}),[],e)instanceof e)})),g=!d((function(){m((function(){}))})),v=p||g;o({target:"Reflect",stat:!0,forced:v,sham:v},{construct:function(e,t){r(e),c(t);var n=arguments.length<3?e:r(arguments[2]);if(g&&!p)return m(e,t,n);if(e==n){switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3])}var o=[null];return s(f,o,t),new(s(a,e,o))}var i=n.prototype,d=u(l(i)?i:h),v=s(e,d,t);return l(v)?v:d}})},"52d5":function(e,t,n){},ddde:function(e,t,n){"use strict";n.r(t);var o=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"sceneManage-menu"},[n("div",{ref:"menusWrapper",staticClass:"menus-wrapper",class:{isCollapsed:e.isMenusCollapsed}},[n("span",{staticClass:"wrapper-icon"},[n("CommonSVG",{staticClass:"wrapper-before",attrs:{color:"var(--primary-bg-color)","icon-class":"menus_wrapper_left",size:31}})],1),e.isMenusCollapsed?[n("el-tooltip",{attrs:{enterable:!1,effect:"dark",content:e.$t("others.toolbox"),"popper-class":"flag",placement:"bottom"}},[n("div",{staticClass:"tools-wrapper",on:{click:e.toggleMenusCollapsed}},[n("CommonSVG",{staticClass:"tools-icon",attrs:{color:"#242630","icon-class":"tb_tools",size:24}})],1)])]:[n("div",{staticClass:"menu-content"},[e._l(e.menuList,(function(t,o){return n("div",{key:t.name+o,staticClass:"menu-item",class:{"cursor-btn":!t.children,active:e.menuActive.includes(t.name)||e.bottomMenuActive==t.name,disabled:t.disable,"menu-has-children":t.children&&t.children.length},on:{mouseenter:function(n){return e.mainMenuMouseenter(t,n)},mouseleave:function(n){return e.mainMenuMouseleave(t,n)},click:function(n){return n.stopPropagation(),e.handleMenuClick(t)}}},[n("el-tooltip",{staticClass:"box-item",attrs:{enterable:!1,effect:"dark",content:t.label,disabled:!!t.children,placement:"bottom"}},[n("span",{style:{"margin-top":"roller"===t.name?"2px;":""}},["extend"===t.type&&"custom"===t.iconType?n("img",{staticClass:"menu-icon-extend",style:{height:t.size+"px"},attrs:{src:t.icon,alt:t.name}}):n("CommonSVG",{staticClass:"menu-icon-builtin",attrs:{color:"#FFFFFF","icon-class":t.icon+(e.menuActive.includes(t.name)||e.bottomMenuActive==t.name?"_active":""),size:t.size}}),t.children&&t.children.length>0?n("i",{staticClass:"el-icon-caret-bottom el-icon cursor-btn"}):e._e()],1)]),t.children&&t.children.length>0?n("div",{directives:[{name:"show",rawName:"v-show",value:t.childrenState,expression:"menu.childrenState"}],staticClass:"item-children"},e._l(t.children,(function(o,i){return n("div",{key:o.label,staticClass:"child-list",class:{active:e.bottomMenuChildActive===o.value},on:{click:function(n){return n.stopPropagation(),e.handleMenuClick(t,i,o)}}},[n("CommonSVG",{attrs:{color:"#FFFFFF","icon-class":o.icon,size:o.size}}),n("span",{staticClass:"text"},[e._v(e._s(o.label))])],1)})),0):e._e()],1)})),n("div",{staticClass:"menus-togglebar cursor-btn",on:{click:e.toggleMenusCollapsed}},[n("el-tooltip",{attrs:{enterable:!1,effect:"dark",content:e.toggleBarTooltipContent,"popper-class":"flag",placement:"bottom"}},[n("span",[n("CommonSVG",{class:{"rotate-z-90":!e.isMenusCollapsed},attrs:{color:"#FFFFFF","icon-class":"double_up_feature",size:20}})],1)])],1)],2)],n("span",{staticClass:"wrapper-icon"},[n("CommonSVG",{staticClass:"wrapper-after",attrs:{color:"var(--primary-bg-color)","icon-class":"menus_wrapper_right",size:31}})],1)],2),n("div",{ref:"mouseCoordinateDom",staticClass:"mouse-coordinate-dom"},[n("div",[n("span",[e._v("X：")]),e._v(e._s(e.coordinateQuery.x))]),n("div",[n("span",[e._v("Y：")]),e._v(e._s(e.coordinateQuery.y))]),n("div",{staticClass:"margin-bottom-8"},[n("span",[e._v("Z：")]),e._v(e._s(e.coordinateQuery.z))]),n("div",[n("span",[e._v(e._s(e.$t("formRelational.longitude.label"))+"：")]),e._v(e._s(e.geographicalQuery[0]))]),n("div",[n("span",[e._v(e._s(e.$t("formRelational.latitude.label"))+"：")]),e._v(e._s(e.geographicalQuery[1]))]),n("div",[n("span",[e._v(e._s(e.$t("formRelational.altitude.label"))+"：")]),e._v(e._s(e.geographicalQuery[2]))])])])},i=[],s=n("c7eb"),a=n("4478"),r=n("2909"),c=n("1da1"),l=n("5530"),u=(n("d3b7"),n("3ca3"),n("ddb0"),n("4de4"),n("b0c0"),n("a434"),n("c740"),n("99af"),n("caad"),n("2532"),n("159b"),n("e9c4"),n("d81d"),n("a630"),n("b893")),d=n("2f62"),m={name:"SceneManageMenu",components:{CommonSVG:function(){return n.e("chunk-51f90655").then(n.bind(null,"17d0"))}},data:function(){return{geographicalQuery:[0,0,0],geographicalQueryCopy:[0,0,0],coordinateQuery:{x:0,y:0,z:0},coordinateQueryCopy:{x:0,y:0,z:0},undo:0,redo:0,isMouseMove:!0}},computed:Object(l["a"])({menuList:function(){for(var e=this.$store.state.menuList.sceneManageMenuList.filter((function(e){return e.isShow})),t=-1,n=0,o=e.length;n<o;n++){var i=e[n];if(i.handler&&""!=i.handler&&"string"==typeof i.handler&&(i.handler=this[i.handler]),"locator"==i.name&&(t=n),i.children&&i.children.length>0)for(var s=0;s<i.children.length;s++)i.children[s].handler&&""!=i.children[s].handler&&"string"==typeof i.children[s].handler&&(i.children[s].handler=this[i.children[s].handler])}-1!==t&&(window.LBS_ENABLED||e.splice(t,1));var a=e.findIndex((function(e){return"operation"===e.name}));if(-1===a)return e.concat(this.$store.state.menuList.sceneManageMenuListExtends.filter((function(e){return e.isShow})));var r=e.splice(a,1);return e.concat(this.$store.state.menuList.sceneManageMenuListExtends.filter((function(e){return e.isShow})).concat(r))},svgClass:function(){return this.className?"svg-icon ".concat(this.className):"svg-icon"},toggleBarTooltipContent:function(){var e=this.$t("others.collapse");return this.isMenusCollapsed&&(e=this.$t("others.expand")),e}},Object(d["b"])({isEdit:function(e){return e.scene.sceneEditMode},isMenusCollapsed:function(e){return e.menuList.isMenusCollapsed},bottomMenuChildActive:function(e){return e.menuList.bottomMenuChildActive},bottomMenuActive:function(e){return e.menuList.bottomMenuActive},menuActive:function(e){return e.menuList.menuActive},sectionLoaded:function(e){return e.menuList.sectionLoaded},elementsBarActivatedTool:function(e){return e.scene.elementsBarActivatedTool},activeDialog:function(e){return e.dialog.activeDialog}})),watch:{isMenusCollapsed:function(e){if(e)this.$refs.menusWrapper.style.width="80px";else{var t=40,n=40,o=this.$store.state.menuList.sceneManageMenuList.filter((function(e){return e.isShow})).length*n;this.$refs.menusWrapper.style.width="".concat(t+o,"px")}},"$store.state.menuList.bottomMenuExecuteState":{deep:"true",handler:function(e){this.getRedoUndo()}}},created:function(){this.isEdit||this.$store.commit("changeSceneManageMenuListParam",{name:"locator",attr:"isShow",value:!1})},methods:{handleBoxSelection:function(){var e=document.querySelector("#renderDom"),t=null;if(this.menuActive.includes("boxSelection"))return this.cancelBoxSelectionEvents(),!0;var n=this,o=[[0,0],[0,0]],i=document.querySelector(".mapboxgl-interactive");null!=i&&(i.style.pointerEvents="none"),t=document.createElement("div"),t.style.borderWidth="2px",t.style.borderStyle="solid",t.style.borderColor="#2680FE",t.style.position="absolute",t.style.left="-100%",t.style.zIndex=10,t.className="boxSelectionDom",e.appendChild(t),window.scene.mv.controller.enabled=!1,e.onmousedown=function(i){i=window.event||i;var s=i.clientX-e.offsetLeft,a=i.clientY-e.offsetTop;return o[0]=[s,a],e.onmousemove=function(i){i=window.event||i;var r=i.clientX-e.offsetLeft,c=i.clientY-e.offsetTop;o[1]=[r,c],t.style.left="".concat(r>s?s:r,"px"),t.style.top="".concat(c>a?a:c,"px"),t.style.width="".concat(Math.abs(r-s),"px"),t.style.height="".concat(Math.abs(c-a),"px"),n.isMouseMove=!1},!1},document.onmouseup=function(){e.onmousemove=null,t.style.left="-100%",n.isMouseMove||n.getElementByBoxSelection(o)}},getElementByBoxSelection:function(e){var t=this;return Object(c["a"])(Object(s["a"])().mark((function n(){var o,i,c;return Object(s["a"])().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return o=Object(a["a"])(window.scene.mv._THREE.Vector2,Object(r["a"])(e[0])),i=Object(a["a"])(window.scene.mv._THREE.Vector2,Object(r["a"])(e[1])),n.next=4,window.scene.mv.tools.boxSelector.select(o,i,!1);case 4:c=n.sent,c.length>0&&(t.cancelBoxSelectionEvents(),t.$store.commit("toggleMenuActive","boxSelection"),t.$emit("selectElement",c[0])),t.isMouseMove=!0,window.scene.render();case 8:case"end":return n.stop()}}),n)})))()},cancelBoxSelectionEvents:function(){window.scene.mv.controller.enabled=!0;var e=document.querySelector(".mapboxgl-interactive");null!=e&&(e.style.pointerEvents="auto"),document.onmouseup=null,document.querySelector("#renderDom").onmousedown=null,document.querySelector(".boxSelectionDom").remove(),this.$store.commit("changeSceneManageMenuListParam",{name:"boxSelection",attr:"disable",value:!1,reverse:!0}),window.scene.render()},getRedoUndo:function(){var e=this;this.$nextTick((function(){e.redo=window.scene.redo.length,e.undo=window.scene.undo.length,e.undo>0?e.$store.commit("changeSceneManageMenuListParam",{name:"undo",attr:"disable",value:!1}):e.$store.commit("changeSceneManageMenuListParam",{name:"undo",attr:"disable",value:!0}),e.redo>0?e.$store.commit("changeSceneManageMenuListParam",{name:"redo",attr:"disable",value:!1}):e.$store.commit("changeSceneManageMenuListParam",{name:"redo",attr:"disable",value:!0})}))},resetAll:function(){window.scene.execute("reset"),this.getRedoUndo(),this.activeDialog.includes("statefulElements")&&this.$bus.emit("onSetStatefulElementsDatas"),this.$bus.emit("onSetFilterComponentState")},handleStep:function(e){window.scene.execute(e),this.getRedoUndo(),this.activeDialog.includes("statefulElements")&&this.$bus.emit("onSetStatefulElementsDatas")},mainMenuMouseenter:function(e,t){e.children&&!this.menuActive.includes(e.name)&&(t.target.querySelector(".item-children").style.display="block",e.childrenState=!0)},mainMenuMouseleave:function(e,t){e.children&&(t.target.querySelector(".item-children").style.display="none",e.childrenState=!1)},classObject:function(e){return{active:-1!=this.menuActive.indexOf(e.name)||this.bottomMenuActive==e.name,disabled:e.disable}},elementsBarActivatedToolMsg:function(){var e="",t=!1;switch(this.elementsBarActivatedTool){case"polygon":case"polyline":case"rectangle":e=this.$t("messageTips.exitDraw");break;case"checkedCoordinate":e=this.$t("messageTips.exitCheckedPoint");break}return this.activeDialog.includes("ElementLink")&&(e=this.$t("messageTips.exitLinkDialog")),e?this.$message.error(e):t=!0,t},handleMenuClick:function(e,t,n){var o=this,i=this.elementsBarActivatedToolMsg();if(!i)return!1;if(e.type&&"extend"===e.type||this.$emit("closeBottomMenu"),"roam"===e.name){var s=this.$store.state.pathAnimation.pathanimationActive;if(s)return void this.$message.error(this.$t("messageTips.exitPathRoam"))}if(window.offCheckedCoordinate&&window.offCheckedCoordinate(),window.offCheckedCoordinate&&(window.offCheckedCoordinate=null,delete window.offCheckedCoordinate),"sectioning"!==e.name&&"sectionPlaneing"!==e.name||!this.sectionLoaded){if(e.disable)return!1;if(""==this.$store.state.widget.settingActive){if(e.handler&&e.handler.constructor==Function){var a=e.handler(e.arg);if(0==a)return!1}if(n&&n.handler&&n.handler.constructor==Function){var r=n.handler(n.arg);if(0==r)return!1}if(e.mutex&&!this.menuActive.includes(e.name)){var c=this.menuList.filter((function(e){return o.menuActive.includes(e.name)}));null!==c&&void 0!==c&&c.length&&c.forEach((function(e){e.handler&&e.handler()})),this.$store.commit("toggleMenuActive","empty"),this.$store.commit("toggleBottomMenuActive",""),this.$store.commit("toggleActiveDialog","empty"),this.$store.commit("changeSceneManageMenuListParam",{name:e.name,attr:"disable",value:!0,reverse:!0})}this.$nextTick((function(){e.justEvent||(e.multiple?o.$store.commit("toggleMenuActive",e.name):(""!=o.$store.state.widget.settingActive&&o.$store.commit("toggleSettingActive","closeSetting"),e.children&&n&&(e.childrenState=!1,o.bottomMenuActive!==e.name&&o.$store.commit("toggleBottomMenuActive",e.name),o.$store.commit("toogleBottomChildMenuActive",e.children[t].value)),"sectioning"===e.name&&(o.$store.commit("setSectionValue","box"),o.$store.commit("toogleSectionLoaded",!0)),e.children||o.$store.commit("toggleBottomMenuActive",e.name)),o.$store.commit("toggleActiveDialog",e.dialogName))})),this.$nextTick((function(){var t=JSON.parse(JSON.stringify(e));t.active=o.menuActive.includes(e.name)||o.bottomMenuActive==e.name,o.emitElementMenuExtendDatas(t)}))}else this.$message(this.$t("messageTips.exitEditingStatus"))}else this.$message(this.$t("messageTips.exitSection"))},emitElementMenuExtendDatas:function(e){window.dispatchEvent(new CustomEvent("onExtendScenemanageMenuClick",{detail:e}))},goFrontView:function(){window.scene.resetCamera()},renderSettingClick:function(){return""==this.$store.state.widget.settingActive||(this.$message(this.$t("messageTips.exitEditingStatus")),!1)},roamClick:function(){return window.scene.features.size>0||(this.$message.error(this.$t("messageTips.beforeRoam")),!1)},beforeActiveExplosion:function(){return!(window.scene.getSelection().length<=1)||(this.$message.error(this.$t("messageTips.beforeExplosion")),!1)},locationClick:function(){return this.menuActive.includes("location")?(this.$refs.mouseCoordinateDom.style.left="-999px",window.scene.mv.events.mousemove.off("default",this.handleCoordinateQuery),window.scene.mv.events.mouseup.off("default",this.copyMouseCoordinate),this.$store.commit("changeSceneManageMenuListParam",{name:"location",attr:"disable",value:!1})):(this.$notify({title:this.$t("messageTips.location.title"),dangerouslyUseHTMLString:!0,message:this.$t("messageTips.location.tips"),duration:17e3}),window.scene.mv.events.mousemove.on("default",this.handleCoordinateQuery),window.scene.mv.events.mouseup.on("default",this.copyMouseCoordinate)),!0},copyMouseCoordinate:function(){var e=document.createElement("textarea");e.value="X:".concat(this.coordinateQueryCopy.x,";Y:").concat(this.coordinateQueryCopy.y,";Z:").concat(this.coordinateQueryCopy.z,";\n").concat(this.$t("formRelational.longitude.label"),":").concat(this.geographicalQueryCopy[0],";").concat(this.$t("formRelational.latitude.label"),":").concat(this.geographicalQueryCopy[1],";").concat(this.$t("formRelational.altitude.label"),":").concat(this.geographicalQueryCopy[2],";"),e.setAttribute("readonly",""),e.style.position="absolute",e.style.left="-9999px",document.body.appendChild(e);var t=document.getSelection().rangeCount>0&&document.getSelection().getRangeAt(0);e.select(),document.execCommand("copy"),document.body.removeChild(e),this.$message.success(this.$t("messageTips.copySuccess")),t&&(document.getSelection().removeAllRanges(),document.getSelection().addRange(t))},handleCoordinateQuery:function(e){var t=window.scene.mv._THREE;this.$refs.mouseCoordinateDom.style.left=e.clientX+15+"px",this.$refs.mouseCoordinateDom.style.top=e.clientY+15+"px";var n=window.scene.queryPosition(new t.Vector2(e.clientX,e.clientY));""!=n&&void 0!=n?(this.coordinateQueryCopy.x=n.x||0,this.coordinateQueryCopy.y=n.y||0,this.coordinateQueryCopy.z=n.z||0,this.geographicalQueryCopy=window.scene.mv.tools.coordinate.vector2mercator(n)):(this.coordinateQueryCopy={x:0,y:0,z:0},this.geographicalQueryCopy=[0,0,0]),this.coordinateQuery.x=Object(u["j"])(this.coordinateQueryCopy.x),this.coordinateQuery.y=Object(u["j"])(this.coordinateQueryCopy.y),this.coordinateQuery.z=Object(u["j"])(this.coordinateQueryCopy.z),this.geographicalQuery=this.geographicalQueryCopy.map((function(e){return Object(u["j"])(e,6)}))},beautifyClick:function(){var e=Array.from(window.scene.features.values()),t=e.findIndex((function(e){return"model"==e.type}));if(-1===t)return this.$message.error(this.$t("messageTips.beforeBeautify")),!1;window.scene.rayTracerRedering?window.scene.rayTracerRedering=!1:window.scene.rayTracerRedering=!0,window.scene.render()},toggleMenusCollapsed:function(){this.$store.commit("setIsMenusCollapsed",!this.isMenusCollapsed)}}},h=m,f=(n("150f"),n("2877")),p=Object(f["a"])(h,o,i,!1,null,"0a169852",null);t["default"]=p.exports}}]);