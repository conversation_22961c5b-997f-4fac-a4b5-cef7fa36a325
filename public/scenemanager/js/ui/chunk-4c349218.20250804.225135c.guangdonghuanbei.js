(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4c349218"],{2243:function(t,e,a){},b5c0:function(t,e,a){"use strict";a("2243")},cce9:function(t,e,a){"use strict";a.r(e);var c=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"attribute",on:{click:function(e){return t.closeDialog()}}},[a("div",{staticClass:"attribute-container"},t._l(t.attrData,(function(e){return a("ul",[a("li",{staticClass:"group-title"},[t._v(t._s(e.key))]),t._l(e.children,(function(e){return a("li",{staticClass:"group-content"},[a("span",{staticClass:"title"},[t._v(t._s(e.key))]),a("span",{staticClass:"desc"},[t._v(t._s(e.val))])])}))],2)})),0)])},n=[],i=(a("a630"),a("3ca3"),a("d3b7"),a("ddb0"),a("6062"),a("159b"),a("b0c0"),{name:"Attribute",data:function(){return{attrData:[]}},created:function(){this.getSelectedObjectsProperty()},methods:{closeDialog:function(){this.$bus.emit("closeAttributeDialog",{title:this.$t("dialog.attribute.table.label1"),icon:"attribute_feature",label:"attribute",needActive:!0})},getSelectedObjectsProperty:function(){var t=this;if(window.scene.selectedObjects.size>0){var e=Array.from(window.scene.selectedObjects.values());e[e.length-1].getProperty().then((function(e){t.setSelectedObjectsProperty(e)}))}},setSelectedObjectsProperty:function(t){var e=this,a=["自定义属性","限制条件","结构","阶段化","构造","图形","标识数据","材质和装饰","分析属性","尺寸标注"],c=new Set(a),n=new Set;t.forEach((function(t){n.add(t.group),c.add(t.group)})),c.forEach((function(t){n.has(t)||c.delete(t)})),c.forEach((function(t){e.attrData.push({key:t,val:"",children:[]})})),t.forEach((function(t){e.attrData.forEach((function(e){t.group===e.key&&e.children.push({id:t.id,key:t.name,val:t.value,group:t.group})}))}))}}}),r=i,s=(a("b5c0"),a("2877")),o=Object(s["a"])(r,c,n,!1,null,"9d5a00d6",null);e["default"]=o.exports}}]);