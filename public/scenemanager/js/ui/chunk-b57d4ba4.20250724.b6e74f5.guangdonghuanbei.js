(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-b57d4ba4"],{"009a":function(e,t,n){"use strict";n.d(t,"a",(function(){return g}));var r=n("66cb"),o=n.n(r),i=2,a=.16,d=.05,u=.05,c=.15,s=5,l=4,f=[{index:7,opacity:.15},{index:6,opacity:.25},{index:5,opacity:.3},{index:5,opacity:.45},{index:5,opacity:.65},{index:5,opacity:.85},{index:4,opacity:.9},{index:3,opacity:.95},{index:2,opacity:.97},{index:1,opacity:.98}];function h(e,t,n){var r;return r=Math.round(e.h)>=60&&Math.round(e.h)<=240?n?Math.round(e.h)-i*t:Math.round(e.h)+i*t:n?Math.round(e.h)+i*t:Math.round(e.h)-i*t,r<0?r+=360:r>=360&&(r-=360),r}function v(e,t,n){return 0===e.h&&0===e.s?e.s:(r=n?e.s-a*t:t===l?e.s+a:e.s+d*t,r>1&&(r=1),n&&t===s&&r>.1&&(r=.1),r<.06&&(r=.06),Number(r.toFixed(2)));var r}function p(e,t,n){var r;return r=n?e.v+u*t:e.v-c*t,r>1&&(r=1),Number(r.toFixed(2))}function g(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=[],r=o()(e),i=s;i>0;i-=1){var a=r.toHsv(),d=o()({h:h(a,i,!0),s:v(a,i,!0),v:p(a,i,!0)}).toHexString();n.push(d)}n.push(r.toHexString());for(var u=1;u<=l;u+=1){var c=r.toHsv(),g=o()({h:h(c,u),s:v(c,u),v:p(c,u)}).toHexString();n.push(g)}return"dark"===t.theme?f.map((function(e){var r=e.index,i=e.opacity,a=o.a.mix(t.backgroundColor||"#141414",n[r],100*i).toHexString();return a})):n}var b={red:"#F5222D",volcano:"#FA541C",orange:"#FA8C16",gold:"#FAAD14",yellow:"#FADB14",lime:"#A0D911",green:"#52C41A",cyan:"#13C2C2",blue:"#1890FF",geekblue:"#2F54EB",purple:"#722ED1",magenta:"#EB2F96",grey:"#666666"},y={},m={};Object.keys(b).forEach((function(e){y[e]=g(b[e]),y[e].primary=y[e][5],m[e]=g(b[e],{theme:"dark",backgroundColor:"#141414"}),m[e].primary=m[e][5]}));y.red,y.volcano,y.gold,y.orange,y.yellow,y.lime,y.green,y.cyan,y.blue,y.geekblue,y.purple,y.magenta,y.grey},"0253":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=o(n("16c7"));function o(e){return e&&e.__esModule?e:{default:e}}var i=function(e,t){for(var n=(0,r.default)(e,t),o=[],i=n.length,a=0;a<i;a+=1){o[a]=[];for(var d=0;d<i;d+=1)a===d?o[a][d]=0:0!==n[a][d]&&n[a][d]?o[a][d]=n[a][d]:o[a][d]=1/0}for(var u=0;u<i;u+=1)for(a=0;a<i;a+=1)for(d=0;d<i;d+=1)o[a][d]>o[a][u]+o[u][d]&&(o[a][d]=o[a][u]+o[u][d]);return o},a=i;t.default=a},"0765":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=i(n("d9c5")),o=n("dcfe");function i(e){return e&&e.__esModule?e:{default:e}}var a=function(e,t,n){"number"!==typeof t&&(t=1e-6),"number"!==typeof n&&(n=.85);for(var i,a=1,d=0,u=1e3,c=e.nodes,s=void 0===c?[]:c,l=e.edges,f=void 0===l?[]:l,h=s.length,v={},p={},g=0;g<h;++g){var b=s[g],y=b.id;v[y]=1/h,p[y]=1/h}var m=(0,r.default)(e);while(u>0&&a>t){d=0;for(g=0;g<h;++g){b=s[g],y=b.id;if(i=0,0===m[b.id].inDegree)v[y]=0;else{for(var E=(0,o.getNeighbors)(y,f,"source"),L=0;L<E.length;++L){var w=E[L],A=m[w].outDegree;A>0&&(i+=p[w]/A)}v[y]=n*i,d+=v[y]}}d=(1-d)/h,a=0;for(g=0;g<h;++g){b=s[g],y=b.id;i=v[y]+d,a+=Math.abs(i-p[y]),p[y]=i}u-=1}return p},d=a;t.default=d},"0c62":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n("8937"),o=function(){function e(e){this.arr=e}return e.prototype.getArr=function(){return this.arr||[]},e.prototype.add=function(t){var n,r=t.arr;if(!(null===(n=this.arr)||void 0===n?void 0:n.length))return new e(r);if(!(null===r||void 0===r?void 0:r.length))return new e(this.arr);if(this.arr.length===r.length){var o=[];for(var i in this.arr)o[i]=this.arr[i]+r[i];return new e(o)}},e.prototype.subtract=function(t){var n,r=t.arr;if(!(null===(n=this.arr)||void 0===n?void 0:n.length))return new e(r);if(!(null===r||void 0===r?void 0:r.length))return new e(this.arr);if(this.arr.length===r.length){var o=[];for(var i in this.arr)o[i]=this.arr[i]-r[i];return new e(o)}},e.prototype.avg=function(t){var n=[];if(0!==t)for(var r in this.arr)n[r]=this.arr[r]/t;return new e(n)},e.prototype.negate=function(){var t=[];for(var n in this.arr)t[n]=-this.arr[n];return new e(t)},e.prototype.squareEuclideanDistance=function(e){var t,n=e.arr;if(!(null===(t=this.arr)||void 0===t?void 0:t.length)||!(null===n||void 0===n?void 0:n.length))return 0;if(this.arr.length===n.length){var r=0;for(var o in this.arr)r+=Math.pow(this.arr[o]-e.arr[o],2);return r}},e.prototype.euclideanDistance=function(e){var t,n=e.arr;if(!(null===(t=this.arr)||void 0===t?void 0:t.length)||!(null===n||void 0===n?void 0:n.length))return 0;if(this.arr.length===n.length){var r=0;for(var o in this.arr)r+=Math.pow(this.arr[o]-e.arr[o],2);return Math.sqrt(r)}console.error("The two vectors are unequal in length.")},e.prototype.normalize=function(){var t=[],n=(0,r.clone)(this.arr);n.sort((function(e,t){return e-t}));var o=n[n.length-1],i=n[0];for(var a in this.arr)t[a]=(this.arr[a]-i)/(o-i);return new e(t)},e.prototype.norm2=function(){var e;if(!(null===(e=this.arr)||void 0===e?void 0:e.length))return 0;var t=0;for(var n in this.arr)t+=Math.pow(this.arr[n],2);return Math.sqrt(t)},e.prototype.dot=function(e){var t,n=e.arr;if(!(null===(t=this.arr)||void 0===t?void 0:t.length)||!(null===n||void 0===n?void 0:n.length))return 0;if(this.arr.length===n.length){var r=0;for(var o in this.arr)r+=this.arr[o]*e.arr[o];return r}console.error("The two vectors are unequal in length.")},e.prototype.equal=function(e){var t,n=e.arr;if((null===(t=this.arr)||void 0===t?void 0:t.length)!==(null===n||void 0===n?void 0:n.length))return!1;for(var r in this.arr)if(this.arr[r]!==n[r])return!1;return!0},e}(),i=o;t.default=i},1683:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"GADDIAsync",{enumerable:!0,get:function(){return r.GADDIAsync}}),Object.defineProperty(t,"connectedComponentAsync",{enumerable:!0,get:function(){return r.connectedComponentAsync}}),t.default=void 0,Object.defineProperty(t,"detectAllCyclesAsync",{enumerable:!0,get:function(){return r.detectAllCyclesAsync}}),Object.defineProperty(t,"detectAllDirectedCycleAsync",{enumerable:!0,get:function(){return r.detectAllDirectedCycleAsync}}),Object.defineProperty(t,"detectAllUndirectedCycleAsync",{enumerable:!0,get:function(){return r.detectAllUndirectedCycleAsync}}),Object.defineProperty(t,"detectCycleAsync",{enumerable:!0,get:function(){return r.detectCycleAsync}}),t.detectDirectedCycleAsync=void 0,Object.defineProperty(t,"dijkstraAsync",{enumerable:!0,get:function(){return r.dijkstraAsync}}),Object.defineProperty(t,"findAllPathAsync",{enumerable:!0,get:function(){return r.findAllPathAsync}}),Object.defineProperty(t,"findShortestPathAsync",{enumerable:!0,get:function(){return r.findShortestPathAsync}}),Object.defineProperty(t,"floydWarshallAsync",{enumerable:!0,get:function(){return r.floydWarshallAsync}}),Object.defineProperty(t,"getAdjMatrixAsync",{enumerable:!0,get:function(){return r.getAdjMatrixAsync}}),Object.defineProperty(t,"getDegreeAsync",{enumerable:!0,get:function(){return r.getDegreeAsync}}),Object.defineProperty(t,"getInDegreeAsync",{enumerable:!0,get:function(){return r.getInDegreeAsync}}),Object.defineProperty(t,"getNeighborsAsync",{enumerable:!0,get:function(){return r.getNeighborsAsync}}),Object.defineProperty(t,"getOutDegreeAsync",{enumerable:!0,get:function(){return r.getOutDegreeAsync}}),Object.defineProperty(t,"labelPropagationAsync",{enumerable:!0,get:function(){return r.labelPropagationAsync}}),Object.defineProperty(t,"louvainAsync",{enumerable:!0,get:function(){return r.louvainAsync}}),Object.defineProperty(t,"minimumSpanningTreeAsync",{enumerable:!0,get:function(){return r.minimumSpanningTreeAsync}}),Object.defineProperty(t,"pageRankAsync",{enumerable:!0,get:function(){return r.pageRankAsync}});var r=n("e98d"),o=r.detectCycleAsync;t.detectDirectedCycleAsync=o;var i={getAdjMatrixAsync:r.getAdjMatrixAsync,connectedComponentAsync:r.connectedComponentAsync,getDegreeAsync:r.getDegreeAsync,getInDegreeAsync:r.getInDegreeAsync,getOutDegreeAsync:r.getOutDegreeAsync,detectCycleAsync:r.detectCycleAsync,detectDirectedCycleAsync:o,detectAllCyclesAsync:r.detectAllCyclesAsync,detectAllDirectedCycleAsync:r.detectAllDirectedCycleAsync,detectAllUndirectedCycleAsync:r.detectAllUndirectedCycleAsync,dijkstraAsync:r.dijkstraAsync,findAllPathAsync:r.findAllPathAsync,findShortestPathAsync:r.findShortestPathAsync,floydWarshallAsync:r.floydWarshallAsync,labelPropagationAsync:r.labelPropagationAsync,louvainAsync:r.louvainAsync,minimumSpanningTreeAsync:r.minimumSpanningTreeAsync,pageRankAsync:r.pageRankAsync,getNeighborsAsync:r.getNeighborsAsync,GADDIAsync:r.GADDIAsync};t.default=i},"16c7":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=function(e,t){var n=e.nodes,r=e.edges,o=[],i={};if(!n)throw new Error("invalid nodes data!");return n&&n.forEach((function(e,t){i[e.id]=t;var n=[];o.push(n)})),r&&r.forEach((function(e){var n=e.source,r=e.target,a=i[n],d=i[r];!a&&0!==a||!d&&0!==d||(o[a][d]=1,t||(o[d][a]=1))})),o},o=r;t.default=o},"264e":function(e,t,n){"use strict";function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"GADDI",{enumerable:!0,get:function(){return b.default}}),Object.defineProperty(t,"breadthFirstSearch",{enumerable:!0,get:function(){return i.default}}),Object.defineProperty(t,"connectedComponent",{enumerable:!0,get:function(){return a.default}}),Object.defineProperty(t,"depthFirstSearch",{enumerable:!0,get:function(){return c.default}}),Object.defineProperty(t,"detectCycle",{enumerable:!0,get:function(){return u.default}}),Object.defineProperty(t,"dijkstra",{enumerable:!0,get:function(){return s.default}}),Object.defineProperty(t,"findAllPath",{enumerable:!0,get:function(){return l.findAllPath}}),Object.defineProperty(t,"findShortestPath",{enumerable:!0,get:function(){return l.findShortestPath}}),Object.defineProperty(t,"floydWarshall",{enumerable:!0,get:function(){return f.default}}),Object.defineProperty(t,"getAdjMatrix",{enumerable:!0,get:function(){return o.default}}),Object.defineProperty(t,"getDegree",{enumerable:!0,get:function(){return d.default}}),Object.defineProperty(t,"getInDegree",{enumerable:!0,get:function(){return d.getInDegree}}),Object.defineProperty(t,"getNeighbors",{enumerable:!0,get:function(){return y.getNeighbors}}),Object.defineProperty(t,"getOutDegree",{enumerable:!0,get:function(){return d.getOutDegree}}),Object.defineProperty(t,"labelPropagation",{enumerable:!0,get:function(){return h.default}}),Object.defineProperty(t,"louvain",{enumerable:!0,get:function(){return v.default}}),Object.defineProperty(t,"minimumSpanningTree",{enumerable:!0,get:function(){return p.default}}),Object.defineProperty(t,"pageRank",{enumerable:!0,get:function(){return g.default}});var o=L(n("16c7")),i=L(n("3872")),a=L(n("862e")),d=E(n("d9c5")),u=L(n("5140")),c=L(n("fe51")),s=L(n("9b96")),l=n("98e2"),f=L(n("0253")),h=L(n("61bc")),v=L(n("b14b")),p=L(n("3ef9")),g=L(n("0765")),b=L(n("649f")),y=n("dcfe");function m(e){if("function"!==typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(m=function(e){return e?n:t})(e)}function E(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==r(e)&&"function"!==typeof e)return{default:e};var n=m(t);if(n&&n.has(e))return n.get(e);var o={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var d=i?Object.getOwnPropertyDescriptor(e,a):null;d&&(d.get||d.set)?Object.defineProperty(o,a,d):o[a]=e[a]}return o.default=e,n&&n.set(e,o),o}function L(e){return e&&e.__esModule?e:{default:e}}},"27f4":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getPropertyWeight=t.getAllSortProperties=t.getAllProperties=t.default=void 0;var r=n("869c"),o=function(e,t){void 0===e&&(e=[]),void 0===t&&(t=100);var n={};e.forEach((function(e){e.properties&&Object.keys(e.properties).forEach((function(t){"id"===t||!"".concat(e.properties[t]).match(r.secondReg)&&!"".concat(e.properties[t]).match(r.dateReg)&&isNaN(Number(e.properties[t]))?n.hasOwnProperty(t)&&delete n[t]:n.hasOwnProperty(t)?n[t]+=1:n[t]=1}))}));var o=Object.keys(n).sort((function(e,t){return n[t]-n[e]}));return o.length<t?o:o.slice(0,t)};t.getAllSortProperties=o;var i=function(e,t){return t.map((function(t){if(e.hasOwnProperty(t)){if(!isNaN(Number(e[t])))return Number(e[t]);if(e[t].match(r.secondReg)||e[t].match(r.dateReg))return Number(Date.parse(new Date(e[t])))/1e3}return 0}))},a=function(e){for(var t=o(e),n=[],r=0;r<e.length;r++)n[r]=i(e[r].properties,t);return n};t.getPropertyWeight=a;var d=function(e,t){void 0===t&&(t=void 0);var n=[];return e.forEach((function(e){void 0===t&&n.push(e),void 0!==e[t]&&n.push(e[t])})),n};t.getAllProperties=d;var u={getAllSortProperties:o,getPropertyWeight:a,getAllProperties:d};t.default=u},3872:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=i(n("52b3")),o=n("dcfe");function i(e){return e&&e.__esModule?e:{default:e}}function a(e){void 0===e&&(e={});var t=e,n=function(){},r=function(){var e={};return function(t){var n=t.next,r=n;return!e[r]&&(e[r]=!0,!0)}}();return t.allowTraversal=e.allowTraversal||r,t.enter=e.enter||n,t.leave=e.leave||n,t}var d=function(e,t,n,i){void 0===i&&(i=!0);var d=a(n),u=new r.default,c=e.edges,s=void 0===c?[]:c;u.enqueue(t);var l="",f=function(){var e=u.dequeue();d.enter({current:e,previous:l}),(0,o.getNeighbors)(e,s,i?"target":void 0).forEach((function(t){d.allowTraversal({previous:l,current:e,next:t})&&u.enqueue(t)})),d.leave({current:e,previous:l}),l=e};while(!u.isEmpty())f()},u=d;t.default=u},"3ef9":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=a(n("83ad")),o=a(n("9c2f")),i=n("dcfe");function a(e){return e&&e.__esModule?e:{default:e}}var d=function(e,t){var n=[],r=e.nodes,a=void 0===r?[]:r,d=e.edges,u=void 0===d?[]:d;if(0===a.length)return n;var c=a[0],s=new Set;s.add(c);var l=function(e,n){return t?e.weight-n.weight:0},f=new o.default(l);(0,i.getEdgesByNodeId)(c.id,u).forEach((function(e){f.insert(e)}));while(!f.isEmpty()){var h=f.delMin(),v=h.source,p=h.target;s.has(v)&&s.has(p)||(n.push(h),s.has(v)||(s.add(v),(0,i.getEdgesByNodeId)(v,u).forEach((function(e){f.insert(e)}))),s.has(p)||(s.add(p),(0,i.getEdgesByNodeId)(p,u).forEach((function(e){f.insert(e)}))))}return n},u=function(e,t){var n=[],o=e.nodes,i=void 0===o?[]:o,a=e.edges,d=void 0===a?[]:a;if(0===i.length)return n;var u=d.map((function(e){return e}));t&&u.sort((function(e,t){return e.weight-t.weight}));var c=new r.default(i.map((function(e){return e.id})));while(u.length>0){var s=u.shift(),l=s.source,f=s.target;c.connected(l,f)||(n.push(s),c.union(l,f))}return n},c=function(e,t,n){var r={prim:d,kruskal:u};return n?r[n](e,t):u(e,t)},s=c;t.default=s},5140:function(e,t,n){"use strict";function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.detectAllUndirectedCycle=t.detectAllDirectedCycle=t.detectAllCycles=t.default=void 0;var o=c(n("fe51")),i=u(n("862e")),a=n("dcfe");function d(e){if("function"!==typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(d=function(e){return e?n:t})(e)}function u(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==r(e)&&"function"!==typeof e)return{default:e};var n=d(t);if(n&&n.has(e))return n.get(e);var o={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var u=i?Object.getOwnPropertyDescriptor(e,a):null;u&&(u.get||u.set)?Object.defineProperty(o,a,u):o[a]=e[a]}return o.default=e,n&&n.set(e,o),o}function c(e){return e&&e.__esModule?e:{default:e}}var s=function(e){var t=null,n=e.nodes,r=void 0===n?[]:n,i={},a={},d={},u={};r.forEach((function(e){a[e.id]=e}));var c={enter:function(e){var n=e.current,r=e.previous;if(d[n]){t={};var o=n,u=r;while(u!==n)t[o]=u,o=u,u=i[u];t[o]=u}else d[n]=n,delete a[n],i[n]=r},leave:function(e){var t=e.current;u[t]=t,delete d[t]},allowTraversal:function(e){var n=e.next;return!t&&!u[n]}};while(Object.keys(a).length){var s=Object.keys(a)[0];(0,o.default)(e,s,c)}return t},l=function(e,t,n){var r,o;void 0===n&&(n=!0);for(var d=[],u=(0,i.default)(e,!1),c=0,s=u;c<s.length;c++){var l=s[c];if(l.length){var f=l[0],h=f.id,v=[f],p=(r={},r[h]=f,r),g=(o={},o[h]=new Set,o);while(v.length>0)for(var b=v.pop(),y=b.id,m=(0,a.getNeighbors)(y,e.edges),E=function(r){var o,i=m[r],a=e.nodes.find((function(e){return e.id===i}));if(i===y)d.push((o={},o[i]=b,o));else if(i in g){if(!g[y].has(a)){var u=!0,c=[a,b],s=p[y];while(g[i].size&&!g[i].has(s)){if(c.push(s),s===p[s.id])break;s=p[s.id]}if(c.push(s),t&&n?(u=!1,c.findIndex((function(e){return t.indexOf(e.id)>-1}))>-1&&(u=!0)):t&&!n&&c.findIndex((function(e){return t.indexOf(e.id)>-1}))>-1&&(u=!1),u){for(var l={},f=1;f<c.length;f+=1)l[c[f-1].id]=c[f];c.length&&(l[c[c.length-1].id]=c[0]),d.push(l)}g[i].add(b)}}else p[i]=b,v.push(a),g[i]=new Set([b])},L=0;L<m.length;L+=1)E(L)}}return d};t.detectAllUndirectedCycle=l;var f=function(e,t,n){void 0===n&&(n=!0);for(var r=[],o=new Set,d=[],u=[],c={},s={},l=function(e){var t=[e];while(t.length>0){var n=t.pop();o.has(n)&&(o.delete(n),d[n.id].forEach((function(e){t.push(e)})),d[n.id].clear())}},f=function e(i,a,s){var f=!1;if(t&&!1===n&&t.indexOf(i.id)>-1)return f;r.push(i),o.add(i);for(var h=s[i.id],v=0;v<h.length;v+=1){var p=c[h[v]];if(p===a){for(var g={},b=1;b<r.length;b+=1)g[r[b-1].id]=r[b];r.length&&(g[r[r.length-1].id]=r[0]),u.push(g),f=!0}else o.has(p)||e(p,a,s)&&(f=!0)}if(f)l(i);else for(v=0;v<h.length;v+=1){p=c[h[v]];d[p.id].has(i)||d[p.id].add(i)}return r.pop(),f},h=e.nodes,v=void 0===h?[]:h,p=0;p<v.length;p+=1){var g=v[p],b=g.id;s[b]=p,c[p]=g}if(t&&n){var y=function(e){var n=t[e];s[v[e].id]=s[n],s[n]=0,c[0]=v.find((function(e){return e.id===n})),c[s[v[e].id]]=v[e]};for(p=0;p<t.length;p++)y(p)}var m=function(r){for(var o,i,d=1/0,c=0;c<r.length;c+=1)for(var l=r[c],f=0;f<l.length;f++){var h=s[l[f].id];h<d&&(d=h,i=c)}var v=r[i],p=[];for(c=0;c<v.length;c+=1){var g=v[c];p[g.id]=[];for(var b=0,y=(0,a.getNeighbors)(g.id,e.edges,"target").filter((function(e){return v.map((function(e){return e.id})).indexOf(e)>-1}));b<y.length;b++){var m=y[b];m!==g.id||!1===n&&t.indexOf(g.id)>-1?p[g.id].push(s[m]):u.push((o={},o[g.id]=g,o))}}return{component:v,adjList:p,minIdx:d}},E=0;while(E<v.length){var L=v.filter((function(e){return s[e.id]>=E})),w=(0,i.detectStrongConnectComponents)({nodes:L,edges:e.edges}).filter((function(e){return e.length>1}));if(0===w.length)break;var A=m(w),O=A.minIdx,N=A.adjList,j=A.component;if(!(j.length>1))break;j.forEach((function(e){d[e.id]=new Set}));var M=c[O];if(t&&n&&-1===t.indexOf(M.id))return u;f(M,M,N),E=O+1}return u};t.detectAllDirectedCycle=f;var h=function(e,t,n,r){return void 0===r&&(r=!0),t?f(e,n,r):l(e,n,r)};t.detectAllCycles=h;var v=s;t.default=v},"52b3":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=o(n("5cb0"));function o(e){return e&&e.__esModule?e:{default:e}}var i=function(){function e(){this.linkedList=new r.default}return e.prototype.isEmpty=function(){return!this.linkedList.head},e.prototype.peek=function(){return this.linkedList.head?this.linkedList.head.value:null},e.prototype.enqueue=function(e){this.linkedList.append(e)},e.prototype.dequeue=function(){var e=this.linkedList.deleteHead();return e?e.value:null},e.prototype.toString=function(e){return this.linkedList.toString(e)},e}(),a=i;t.default=a},"5cb0":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.LinkedListNode=void 0;var r=function(e,t){return e===t},o=function(){function e(e,t){void 0===t&&(t=null),this.value=e,this.next=t}return e.prototype.toString=function(e){return e?e(this.value):"".concat(this.value)},e}();t.LinkedListNode=o;var i=function(){function e(e){void 0===e&&(e=r),this.head=null,this.tail=null,this.compare=e}return e.prototype.prepend=function(e){var t=new o(e,this.head);return this.head=t,this.tail||(this.tail=t),this},e.prototype.append=function(e){var t=new o(e);return this.head?(this.tail.next=t,this.tail=t,this):(this.head=t,this.tail=t,this)},e.prototype.delete=function(e){if(!this.head)return null;var t=null;while(this.head&&this.compare(this.head.value,e))t=this.head,this.head=this.head.next;var n=this.head;if(null!==n)while(n.next)this.compare(n.next.value,e)?(t=n.next,n.next=n.next.next):n=n.next;return this.compare(this.tail.value,e)&&(this.tail=n),t},e.prototype.find=function(e){var t=e.value,n=void 0===t?void 0:t,r=e.callback,o=void 0===r?void 0:r;if(!this.head)return null;var i=this.head;while(i){if(o&&o(i.value))return i;if(void 0!==n&&this.compare(i.value,n))return i;i=i.next}return null},e.prototype.deleteTail=function(){var e=this.tail;if(this.head===this.tail)return this.head=null,this.tail=null,e;var t=this.head;while(t.next)t.next.next?t=t.next:t.next=null;return this.tail=t,e},e.prototype.deleteHead=function(){if(!this.head)return null;var e=this.head;return this.head.next?this.head=this.head.next:(this.head=null,this.tail=null),e},e.prototype.fromArray=function(e){var t=this;return e.forEach((function(e){return t.append(e)})),this},e.prototype.toArray=function(){var e=[],t=this.head;while(t)e.push(t),t=t.next;return e},e.prototype.reverse=function(){var e=this.head,t=null,n=null;while(e)n=e.next,e.next=t,t=e,e=n;this.tail=this.head,this.head=t},e.prototype.toString=function(e){return void 0===e&&(e=void 0),this.toArray().map((function(t){return t.toString(e)})).toString()},e}(),a=i;t.default=a},"5d6f":function(e,t,n){"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.DistanceType=void 0,t.DistanceType=r,function(e){e["EuclideanDistance"]="euclideanDistance"}(r||(t.DistanceType=r={}))},"61bc":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=i(n("16c7")),o=n("dcfe");function i(e){return e&&e.__esModule?e:{default:e}}var a=function(e,t,n,i){void 0===t&&(t=!1),void 0===n&&(n="weight"),void 0===i&&(i=1e3);var a=e.nodes,d=void 0===a?[]:a,u=e.edges,c=void 0===u?[]:u,s={},l={};d.forEach((function(e,t){var n=(0,o.uniqueId)();e.clusterId=n,s[n]={id:n,nodes:[e]},l[e.id]={node:e,idx:t}}));var f=(0,r.default)(e,t),h=[],v={};f.forEach((function(e,t){var n=0,r=d[t].id;v[r]={},e.forEach((function(e,t){if(e){n+=e;var o=d[t].id;v[r][o]=e}})),h.push(n)}));var p=0,g=function(){var e=!1;if(d.forEach((function(t){var n={};Object.keys(v[t.id]).forEach((function(e){var r=v[t.id][e],o=l[e].node,i=o.clusterId;n[i]||(n[i]=0),n[i]+=r}));var r=-1/0,o=[];if(Object.keys(n).forEach((function(e){r<n[e]?(r=n[e],o=[e]):r===n[e]&&o.push(e)})),1!==o.length||o[0]!==t.clusterId){var i=o.indexOf(t.clusterId);if(i>=0&&o.splice(i,1),o&&o.length){e=!0;var a=s[t.clusterId],d=a.nodes.indexOf(t);a.nodes.splice(d,1);var u=Math.floor(Math.random()*o.length),c=s[o[u]];c.nodes.push(t),t.clusterId=c.id}}})),!e)return"break";p++};while(p<i){var b=g();if("break"===b)break}Object.keys(s).forEach((function(e){var t=s[e];t.nodes&&t.nodes.length||delete s[e]}));var y=[],m={};c.forEach((function(e){var t=e.source,r=e.target,o=e[n]||1,i=l[t].node.clusterId,a=l[r].node.clusterId,d="".concat(i,"---").concat(a);if(m[d])m[d].weight+=o,m[d].count++;else{var u={source:i,target:a,weight:o,count:1};m[d]=u,y.push(u)}}));var E=[];return Object.keys(s).forEach((function(e){E.push(s[e])})),{clusters:E,clusterEdges:y}},d=a;t.default=d},"649f":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n("cb87"),o=u(n("0253")),i=u(n("ada5")),a=u(n("9b96")),d=n("dcfe");function u(e){return e&&e.__esModule?e:{default:e}}var c=function(e,t,n,r){void 0===n&&(n="cluster"),void 0===r&&(r=2);var o=[],i=e.nodes;return t.forEach((function(e,t){o.push(s(i,e,t,n,r))})),o},s=function(e,t,n,r,o){var i=[n],a=[],d={};return t.forEach((function(t,u){if(t<=o&&n!==u){i.push(u),a.push(e[u]);var c=e[u][r];d[c]?(d[c].count++,d[c].dists.push(t)):d[c]={count:1,dists:[t]}}})),Object.keys(d).forEach((function(e){d[e].dists=d[e].dists.sort((function(e,t){return e-t}))})),{nodeIdx:n,nodeId:e[n].id,nodeIdxs:i,neighbors:a,neighborNum:i.length-1,nodeLabelCountMap:d}},l=function(e,t,n,r,o){var i=Math.ceil(n/t),a={},d=0;return r.forEach((function(e,r){var u=0,c=0,s=e.nodeIdxs,l=e.neighborNum-1;while(u<i){var f=s[1+Math.floor(Math.random()*l)],h=0;while(a["".concat(r,"-").concat(f)]||a["".concat(f,"-").concat(r)])if(f=Math.floor(Math.random()*t),h++,h>2*t)break;if(h<2*t&&(a["".concat(r,"-").concat(f)]={start:r,end:f,distance:o[r][f]},u++,d++,d>=n))return a;if(c++,c>2*t)break}if(u<i){var v=i-u;i=(i+v)/(t-r-1)}})),a},f=function(e,t,n,r){var o=n.nodes;return r||(r={}),Object.keys(e).forEach((function(i){var a,d;if(!r||!r[i]){r[i]={nodes:[],edges:[]};var u=e[i],c=null===(a=t[u.start])||void 0===a?void 0:a.nodeIdxs,s=null===(d=t[u.end])||void 0===d?void 0:d.nodeIdxs;if(c&&s){var l=new Set(s),f=c.filter((function(e){return l.has(e)}));if(f&&f.length){for(var h={},v=f.length,p=0;p<v;p++){var g=o[f[p]];r[i].nodes.push(g),h[g.id]=!0}n.edges.forEach((function(e){h[e.source]&&h[e.target]&&r[i].edges.push(e)}))}}}})),r},h=function(e,t,n,r){var o,i,a={};e.nodes.forEach((function(e){a[e.id]=e}));var d=0;return!(null===(o=null===t||void 0===t?void 0:t.edges)||void 0===o?void 0:o.length)||(null===(i=null===t||void 0===t?void 0:t.nodes)||void 0===i?void 0:i.length)<2?0:(e.edges.forEach((function(e){var o=a[e.source][n],i=a[e.target][n],u=null===t||void 0===t?void 0:t.nodes[0][n],c=null===t||void 0===t?void 0:t.nodes[1][n],s=null===t||void 0===t?void 0:t.edges[0][r];e[r]===s&&(o===u&&i===c||o===c&&i===u)&&d++})),d)},v=function(e,t,n){for(var r=1/0,o=0,i=function(t){var n=e[t],i=Object.keys(n).sort((function(e,t){return n[e]-n[t]})),a=10,d=[];i.forEach((function(e,t){d[t%a]||(d[t%a]={graphs:[],totalCount:0,aveCount:0}),d[t%a].graphs.push(e),d[t%a].totalCount+=n[e]}));var u=0,c=[];d.forEach((function(e){var t=e.totalCount/e.graphs.length;e.aveCount=t,c.push(t);var r=0,o=e.length;e.graphs.forEach((function(t,o){var i=n[t];e.graphs.forEach((function(e,t){o!==t&&(r+=Math.abs(i-n[e]))}))})),r/=o*(o-1)/2,u+=r})),u/=d.length;var s=0;c.forEach((function(e,t){c.forEach((function(n,r){t!==r&&(s+=Math.abs(e-n))})),s/=c.length*(c.length-1)/2}));var l=s-u;r<l&&(r=l,o=t)},a=0;a<t;a++)i(a);return{structure:n[o],structureCountMap:e[o]}},p=function(e,t){var n={},r={};return e.forEach((function(e,o){n[e.id]={idx:o,node:e,degree:0,inDegree:0,outDegree:0};var i=e[t];r[i]||(r[i]=[]),r[i].push(e)})),{nodeMap:n,nodeLabelMap:r}},g=function(e,t,n){var r={},o={};return e.forEach((function(e,i){r["".concat(d.uniqueId)]={idx:i,edge:e};var a=e[t];o[a]||(o[a]=[]),o[a].push(e);var u=n[e.source];u&&(u.degree++,u.outDegree++);var c=n[e.target];c&&(c.degree++,c.inDegree++)})),{edgeMap:r,edgeLabelMap:o}},b=function(e,t,n){var r=t.length,o={};return t.forEach((function(t,i){for(var a=n?0:i+1,d=e[i].id,u=a;u<r;u++)if(i!==u){var c=e[u].id,s=t[u];o["".concat(d,"-").concat(c)]=s,n||(o["".concat(c,"-").concat(d)]=s)}})),o},y=function(e,t,n,r,o,i,a,d,u,c,s){var l,v="".concat(t.id,"-").concat(n.id);if(c&&c[v])return c[v];var p=s?s[v]:void 0;if(!p){var g=(l={},l[v]={start:r[t.id].idx,end:r[n.id].idx,distance:o},l);s=f(g,i,e,s),p=s[v]}return h(p,a,d,u)},m=function(e,t,n,r){var o,i,a,d=null===(o=e[t])||void 0===o?void 0:o.degree,u=null===(i=e[t])||void 0===i?void 0:i.inDegree,c=null===(a=e[t])||void 0===a?void 0:a.outDegree;return void 0===e[t]&&(d=1/0,u=1/0,c=1/0,r[t].forEach((function(e){var t=n[e.id].degree;d>t&&(d=t);var r=n[e.id].inDegree;u>r&&(u=r);var o=n[e.id].outDegree;c>o&&(c=o)})),e[t]={degree:d,inDegree:u,outDegree:c}),{minPatternNodeLabelDegree:d,minPatternNodeLabelInDegree:u,minPatternNodeLabelOutDegree:c}},E=function(e,t,n,d,u,E,L){var w;if(void 0===n&&(n=!1),void 0===E&&(E="cluster"),void 0===L&&(L="cluster"),e&&e.nodes){var A=e.nodes.length;if(A){var O=(0,o.default)(e,n),N=(0,o.default)(t,n),j=b(e.nodes,O,n),M=b(t.nodes,N,n),k=p(e.nodes,E),D=k.nodeMap,I=k.nodeLabelMap,_=p(t.nodes,E),P=_.nodeMap,x=_.nodeLabelMap;g(e.edges,L,D);var S=g(t.edges,L,P).edgeLabelMap,C=[];null===N||void 0===N||N.forEach((function(e){C=C.concat(e)})),u||(u=Math.max.apply(Math,(0,r.__spreadArray)((0,r.__spreadArray)([],C,!1),[2],!1))),d||(d=u);var T=c(e,O,E,d),R=c(t,N,E,d),G=Math.min(100,A*(A-1)/2),q=l(d,A,G,T,O),F=f(q,T,e),U=10,H=1,B=1,V=4,W={graphs:F,nodeLabelProp:E,edgeLabelProp:L,minSupport:H,minNodeNum:B,maxNodeNum:V,directed:n},z=(0,i.default)(W).slice(0,U),K=z.length,$=[];z.forEach((function(e,t){$[t]={},Object.keys(F).forEach((function(n){var r=F[n],o=h(r,e,E,L);$[t][n]=o}))}));var J=v($,K,z),Q=J.structure,X=J.structureCountMap,Y=t.nodes[0],Z=[],ee=null===(w=t.nodes[0])||void 0===w?void 0:w[E],te=-1/0;t.nodes.forEach((function(e){var t=e[E],n=I[t];(null===n||void 0===n?void 0:n.length)>te&&(te=n.length,Z=n,ee=t,Y=e)}));var ne={},re={},oe={},ie={},ae={},de={};Object.keys(x).forEach((function(r,o){ae[r]=[],n&&(de[r]=[]);var i=-1/0,a=x[r],d={};a.forEach((function(e){var t=M["".concat(Y.id,"-").concat(e.id)];if(t&&ae[r].push(t),i<t&&(i=t),d["".concat(Y.id,"-").concat(e.id)]={start:0,end:P[e.id].idx,distance:t},n){var o=M["".concat(e.id,"-").concat(Y.id)];o&&de[r].push(o)}})),ae[r]=ae[r].sort((function(e,t){return e-t})),n&&(de[r]=de[r].sort((function(e,t){return e-t}))),re=f(d,R,t,re);var u=[];if(Object.keys(d).forEach((function(e){if(oe[e])u.push(oe[e]);else{var t=re[e];oe[e]=h(t,Q,E,L),u.push(oe[e])}})),u=u.sort((function(e,t){return t-e})),ie["".concat(Y.id,"-").concat(r)]=u,r!==ee)for(var c=(null===Z||void 0===Z?void 0:Z.length)||0,s=function(t){var n=Z[t],o=T[D[n.id].idx],i=o.nodeLabelCountMap[r],a=x[r].length;if(!i||i.count<a)return Z.splice(t,1),"continue";for(var d=!1,c=0;c<a;c++)if(i.dists[c]>ae[r][c]){d=!0;break}if(d)return Z.splice(t,1),"continue";var s={};o.neighbors.forEach((function(e){var t=j["".concat(n.id,"-").concat(e.id)];s["".concat(n.id,"-").concat(e.id)]={start:D[n.id].idx,end:D[e.id].idx,distance:t}})),F=f(s,T,e,F);var l=[];Object.keys(s).forEach((function(e){if(X[e])l.push(X[e]);else{var t=F[e];X[e]=h(t,Q,E,L),l.push(X[e])}})),l=l.sort((function(e,t){return t-e}));var v=!1;for(c=0;c<a;c++)if(l[c]<u[c]){v=!0;break}return v?(Z.splice(t,1),"continue"):void 0},l=c-1;l>=0;l--)s(l)}));var ue=[];null===Z||void 0===Z||Z.forEach((function(r){for(var o=D[r.id].idx,i=s(e.nodes,O[o],o,E,u),a=i.neighbors,d=a.length,c=!1,l=d-1;l>=0;l--){if(a.length+1<t.nodes.length)return void(c=!0);var f=a[l],h=f[E];if(x[h]&&x[h].length)if(ae[h]&&ae[h].length){var v="".concat(r.id,"-").concat(f.id),p=j[v],g=ae[h].length-1,b=ae[h][g];if(p>b)a.splice(l,1);else{if(n){var w="".concat(f.id,"-").concat(r.id),A=j[w];g=de[h].length-1;var N=de[h][g];if(A>N){a.splice(l,1);continue}}var M=X[v]?X[v]:y(e,r,f,D,p,T,Q,E,L,X,F),k="".concat(Y.id,"-").concat(h),I=ie[k][ie[k].length-1];if(M<I)a.splice(l,1);else{var _=m(ne,h,P,x),S=_.minPatternNodeLabelDegree;_.minPatternNodeLabelInDegree,_.minPatternNodeLabelOutDegree;D[f.id].degree<S&&a.splice(l,1)}}}else a.splice(l,1);else a.splice(l,1)}c||ue.push({nodes:[r].concat(a)})}));var ce=(0,a.default)(t,Y.id,!1).length,se={};n?(Object.keys(ce).forEach((function(e){var t=P[e].node[E];se[t]?se[t].push(ce[e]):se[t]=[ce[e]]})),Object.keys(se).forEach((function(e){se[e].sort((function(e,t){return e-t}))}))):se=ae;for(var le=ue.length,fe=function(r){var o=ue[r],i=o.nodes[0],d={},u={};o.nodes.forEach((function(e,t){u[e.id]={idx:t,node:e,degree:0,inDegree:0,outDegree:0};var n=e[E];d[n]?d[n]++:d[n]=1}));var c=[],s={};e.edges.forEach((function(e){u[e.source]&&u[e.target]&&(c.push(e),s[e[L]]?s[e[L]]++:s[e[L]]=1,u[e.source].degree++,u[e.target].degree++,u[e.source].outDegree++,u[e.target].inDegree++)}));for(var l=Object.keys(S).length,f=!1,h=0;h<l;h++){var v=Object.keys(S)[h];if(!s[v]||s[v]<S[v].length){f=!0;break}}if(f)return ue.splice(r,1),"continue";var p=c.length;if(p<t.edges.length)return ue.splice(r,1),"break";var g=!1,b=function(e){var t=c[e],r=t[L],o=S[r];if(!o||!o.length)return s[r]--,o&&s[r]<o.length?(g=!0,"break"):(c.splice(e,1),u[t.source].degree--,u[t.target].degree--,u[t.source].outDegree--,u[t.target].inDegree--,"continue");var i=u[t.source].node[E],a=u[t.target].node[E],d=!1;return o.forEach((function(e){var t=P[e.source].node,r=P[e.target].node;t[E]===i&&r[E]===a&&(d=!0),n||t[E]!==a||r[E]!==i||(d=!0)})),d?void 0:(s[r]--,o&&s[r]<o.length?(g=!0,"break"):(c.splice(e,1),u[t.source].degree--,u[t.target].degree--,u[t.source].outDegree--,u[t.target].inDegree--,"continue"))};for(h=p-1;h>=0;h--){var y=b(h);if("break"===y)break}if(g)return ue.splice(r,1),"continue";o.edges=c;var w=(0,a.default)(o,o.nodes[0].id,!1).length;if(Object.keys(w).reverse().forEach((function(e){if(e!==o.nodes[0].id&&!g){if(w[e]===1/0){var t=u[e].node[E];if(d[t]--,d[t]<x[t].length)return void(g=!0);var n=o.nodes.indexOf(u[e].node);return o.nodes.splice(n,1),void(u[e]=void 0)}var r=D[e].node[E];if(!se[r]||!se[r].length||w[e]>se[r][se[r].length-1]){t=u[e].node[E];if(d[t]--,d[t]<x[t].length)return void(g=!0);n=o.nodes.indexOf(u[e].node);o.nodes.splice(n,1),u[e]=void 0}}})),g)return ue.splice(r,1),"continue";var A=!0,O=0;while(A&&!g){A=!1;var N=n?u[i.id].degree<P[Y.id].degree||u[i.id].inDegree<P[Y.id].inDegree||u[i.id].outDegree<P[Y.id].outDegree:u[i.id].degree<P[Y.id].degree;if(N){g=!0;break}if(d[i[E]]<x[i[E]].length){g=!0;break}for(var j=o.nodes.length,M=j-1;M>=0;M--){var k=o.nodes[M],I=u[k.id].degree,_=u[k.id].inDegree,C=u[k.id].outDegree,T=k[E],R=m(ne,T,P,x),G=R.minPatternNodeLabelDegree,q=R.minPatternNodeLabelInDegree,F=R.minPatternNodeLabelOutDegree,U=n?I<G||_<q||C<F:I<G;if(U){if(d[k[E]]--,d[k[E]]<x[k[E]].length){g=!0;break}o.nodes.splice(M,1),u[k.id]=void 0,A=!0}}if(g||!A&&0!==O)break;p=c.length;for(var H=p-1;H>=0;H--){var B=c[H];if(!u[B.source]||!u[B.target]){c.splice(H,1);var V=B[L];if(s[V]--,u[B.source]&&(u[B.source].degree--,u[B.source].outDegree--),u[B.target]&&(u[B.target].degree--,u[B.target].inDegree--),S[V]&&s[V]<S[V].length){g=!0;break}A=!0}}O++}return g||g||o.nodes.length<t.nodes.length||c.length<t.edges.length?(ue.splice(r,1),"continue"):void 0},he=le-1;he>=0;he--){var ve=fe(he);if("break"===ve)break}var pe=ue.length,ge=function(e){var t=ue[e],n={};t.edges.forEach((function(e){var t="".concat(e.source,"-").concat(e.target,"-").concat(e.label);n[t]?n[t]++:n[t]=1}));for(var r=function(e){var t=ue[e],r={};t.edges.forEach((function(e){var t="".concat(e.source,"-").concat(e.target,"-").concat(e.label);r[t]?r[t]++:r[t]=1}));var o=!0;Object.keys(r).length!==Object.keys(n).length?o=!1:Object.keys(n).forEach((function(e){r[e]!==n[e]&&(o=!1)})),o&&ue.splice(e,1)},o=pe-1;o>e;o--)r(o);pe=ue.length};for(he=0;he<=pe-1;he++)ge(he);return ue}}},L=E;t.default=L},7162:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.oneHot=t.getDistance=t.getAllKeyValueMap=t.default=void 0;var r=n("8937"),o=n("5d6f"),i=a(n("0c62"));function a(e){return e&&e.__esModule?e:{default:e}}var d=function(e,t,n){var o=[];(null===t||void 0===t?void 0:t.length)?o=t:(e.forEach((function(e){o=o.concat(Object.keys(e))})),o=(0,r.uniq)(o));var i={};return o.forEach((function(t){var o=[];e.forEach((function(e){void 0!==e[t]&&""!==e[t]&&o.push(e[t])})),o.length&&!(null===n||void 0===n?void 0:n.includes(t))&&(i[t]=(0,r.uniq)(o))})),i};t.getAllKeyValueMap=d;var u=function(e,t,n){var r=d(e,t,n),o=[];return Object.keys(r).length?(e.forEach((function(e,t){var n=[];if(1===Object.keys(r).length){var i=Object.keys(r)[0],a=r[i];a.every((function(e){return!isNaN(Number(e))}))&&(n=[e[i]])}else Object.keys(r).forEach((function(t){for(var o=e[t],i=r[t],a=i.findIndex((function(e){return o===e})),d=[],u=0;u<i.length;u++)u===a?d.push(1):d.push(0);n=n.concat(d)}));o[t]=n})),o):o};t.oneHot=u;var c=function(e,t,n,r){void 0===n&&(n=o.DistanceType.EuclideanDistance);var a=0;switch(n){case o.DistanceType.EuclideanDistance:a=new i.default(e).euclideanDistance(new i.default(t));break;default:break}return a};t.getDistance=c;var s={getAllKeyValueMap:d,oneHot:u,getDistance:c};t.default=s},"7c8a":function(e,t,n){"use strict";function r(e,t,n){if(e){if("function"===typeof e.addEventListener)return e.addEventListener(t,n,!1),{remove:function(){e.removeEventListener(t,n,!1)}};if("function"===typeof e.attachEvent)return e.attachEvent("on"+t,n),{remove:function(){e.detachEvent("on"+t,n)}}}}var o,i,a,d;function u(){o=document.createElement("table"),i=document.createElement("tr"),a=/^\s*<(\w+|!)[^>]*>/,d={tr:document.createElement("tbody"),tbody:o,thead:o,tfoot:o,td:i,th:i,"*":document.createElement("div")}}function c(e){o||u();var t=a.test(e)&&RegExp.$1;t&&t in d||(t="*");var n=d[t];e="string"===typeof e?e.replace(/(^\s*)|(\s*$)/g,""):e,n.innerHTML=""+e;var r=n.childNodes[0];return r&&n.contains(r)&&n.removeChild(r),r}function s(e,t){if(e)for(var n in t)t.hasOwnProperty(n)&&(e.style[n]=t[n]);return e}n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return c})),n.d(t,"c",(function(){return s}))},"7fa2":function(e,t,n){"use strict";var r="*",o=function(){function e(){this._events={}}return e.prototype.on=function(e,t,n){return this._events[e]||(this._events[e]=[]),this._events[e].push({callback:t,once:!!n}),this},e.prototype.once=function(e,t){return this.on(e,t,!0)},e.prototype.emit=function(e){for(var t=this,n=[],o=1;o<arguments.length;o++)n[o-1]=arguments[o];var i=this._events[e]||[],a=this._events[r]||[],d=function(r){for(var o=r.length,i=0;i<o;i++)if(r[i]){var a=r[i],d=a.callback,u=a.once;u&&(r.splice(i,1),0===r.length&&delete t._events[e],o--,i--),d.apply(t,n)}};d(i),d(a)},e.prototype.off=function(e,t){if(e)if(t){for(var n=this._events[e]||[],r=n.length,o=0;o<r;o++)n[o].callback===t&&(n.splice(o,1),r--,o--);0===n.length&&delete this._events[e]}else delete this._events[e];else this._events={};return this},e.prototype.getEvents=function(){return this._events},e}();t["a"]=o},"83ad":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=function(){function e(e){this.count=e.length,this.parent={};for(var t=0,n=e;t<n.length;t++){var r=n[t];this.parent[r]=r}}return e.prototype.find=function(e){while(this.parent[e]!==e)e=this.parent[e];return e},e.prototype.union=function(e,t){var n=this.find(e),r=this.find(t);n!==r&&(n<r?(this.parent[t]!==t&&this.union(this.parent[t],e),this.parent[t]=this.parent[e]):(this.parent[e]!==e&&this.union(this.parent[e],t),this.parent[e]=this.parent[t]))},e.prototype.connected=function(e,t){return this.find(e)===this.find(t)},e}(),o=r;t.default=o},"862e":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=a,t.detectStrongConnectComponents=t.detectConnectedComponents=void 0;var r=n("dcfe"),o=function(e){for(var t=e.nodes,n=void 0===t?[]:t,o=e.edges,i=void 0===o?[]:o,a=[],d={},u=[],c=function e(t){u.push(t),d[t.id]=!0;for(var o=(0,r.getNeighbors)(t.id,i),a=function(t){var r=o[t];if(!d[r]){var i=n.filter((function(e){return e.id===r}));i.length>0&&e(i[0])}},c=0;c<o.length;++c)a(c)},s=0;s<n.length;s++){var l=n[s];if(!d[l.id]){c(l);var f=[];while(u.length>0)f.push(u.pop());a.push(f)}}return a};t.detectConnectedComponents=o;var i=function(e){for(var t=e.nodes,n=void 0===t?[]:t,o=e.edges,i=void 0===o?[]:o,a=[],d={},u={},c={},s=[],l=0,f=function e(t){u[t.id]=l,c[t.id]=l,l+=1,a.push(t),d[t.id]=!0;for(var o=(0,r.getNeighbors)(t.id,i,"target").filter((function(e){return n.map((function(e){return e.id})).indexOf(e)>-1})),f=function(r){var i=o[r];if(u[i]||0===u[i])d[i]&&(c[t.id]=Math.min(c[t.id],u[i]));else{var a=n.filter((function(e){return e.id===i}));a.length>0&&e(a[0]),c[t.id]=Math.min(c[t.id],c[i])}},h=0;h<o.length;h++)f(h);if(c[t.id]===u[t.id]){var v=[];while(a.length>0){var p=a.pop();if(d[p.id]=!1,v.push(p),p===t)break}v.length>0&&s.push(v)}},h=0,v=n;h<v.length;h++){var p=v[h];u[p.id]||0===u[p.id]||f(p)}return s};function a(e,t){return t?i(e):o(e)}t.detectStrongConnectComponents=i},"869c":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.secondReg=t.dateReg=void 0;var r=/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/;t.secondReg=r;var o=/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2}) (\d{1,2}):(\d{1,2}):(\d{1,2})$/;t.dateReg=o},9023:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MESSAGE=t.ALGORITHM=void 0;var r={pageRank:"pageRank",breadthFirstSearch:"breadthFirstSearch",connectedComponent:"connectedComponent",depthFirstSearch:"depthFirstSearch",detectCycle:"detectCycle",detectDirectedCycle:"detectDirectedCycle",detectAllCycles:"detectAllCycles",detectAllDirectedCycle:"detectAllDirectedCycle",detectAllUndirectedCycle:"detectAllUndirectedCycle",dijkstra:"dijkstra",findAllPath:"findAllPath",findShortestPath:"findShortestPath",floydWarshall:"floydWarshall",getAdjMatrix:"getAdjMatrix",getDegree:"getDegree",getInDegree:"getInDegree",getNeighbors:"getNeighbors",getOutDegree:"getOutDegree",labelPropagation:"labelPropagation",louvain:"louvain",GADDI:"GADDI",minimumSpanningTree:"minimumSpanningTree",SUCCESS:"SUCCESS",FAILURE:"FAILURE"};t.ALGORITHM=r;var o={SUCCESS:"SUCCESS",FAILURE:"FAILURE"};t.MESSAGE=o},9154:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.VACANT_NODE_LABEL=t.VACANT_NODE_ID=t.VACANT_GRAPH_ID=t.VACANT_EDGE_LABEL=t.VACANT_EDGE_ID=t.Node=t.Graph=t.Edge=t.AUTO_EDGE_ID=void 0;var r=-1;t.VACANT_EDGE_ID=r;var o=-1;t.VACANT_NODE_ID=o;var i="-1";t.VACANT_EDGE_LABEL=i;var a="-1";t.VACANT_NODE_LABEL=a;var d=-1;t.VACANT_GRAPH_ID=d;var u="-1";t.AUTO_EDGE_ID=u;var c=function(){function e(e,t,n,a){void 0===e&&(e=r),void 0===t&&(t=o),void 0===n&&(n=o),void 0===a&&(a=i),this.id=e,this.from=t,this.to=n,this.label=a}return e}();t.Edge=c;var s=function(){function e(e,t){void 0===e&&(e=o),void 0===t&&(t=a),this.id=e,this.label=t,this.edges=[],this.edgeMap={}}return e.prototype.addEdge=function(e){this.edges.push(e),this.edgeMap[e.id]=e},e}();t.Node=s;var l=function(){function e(e,t,n){void 0===e&&(e=o),void 0===t&&(t=!0),void 0===n&&(n=!1),this.id=e,this.edgeIdAutoIncrease=t,this.edges=[],this.nodes=[],this.nodeMap={},this.edgeMap={},this.nodeLabelMap={},this.edgeLabelMap={},this.counter=0,this.directed=n}return e.prototype.getNodeNum=function(){return this.nodes.length},e.prototype.addNode=function(e,t){if(!this.nodeMap[e]){var n=new s(e,t);this.nodes.push(n),this.nodeMap[e]=n,this.nodeLabelMap[t]||(this.nodeLabelMap[t]=[]),this.nodeLabelMap[t].push(e)}},e.prototype.addEdge=function(e,t,n,r){if((this.edgeIdAutoIncrease||void 0===e)&&(e=this.counter++),!(this.nodeMap[t]&&this.nodeMap[n]&&this.nodeMap[n].edgeMap[e])){var o=new c(e,t,n,r);if(this.edges.push(o),this.edgeMap[e]=o,this.nodeMap[t].addEdge(o),this.edgeLabelMap[r]||(this.edgeLabelMap[r]=[]),this.edgeLabelMap[r].push(o),!this.directed){var i=new c(e,n,t,r);this.nodeMap[n].addEdge(i),this.edgeLabelMap[r].push(i)}}},e}();t.Graph=l},"97b1":function(e,t,n){"use strict";n.r(t),n.d(t,"getAdjMatrix",(function(){return o})),n.d(t,"breadthFirstSearch",(function(){return b})),n.d(t,"connectedComponent",(function(){return E})),n.d(t,"getDegree",(function(){return w})),n.d(t,"getInDegree",(function(){return A})),n.d(t,"getOutDegree",(function(){return O})),n.d(t,"detectCycle",(function(){return P})),n.d(t,"detectDirectedCycle",(function(){return nt})),n.d(t,"detectAllCycles",(function(){return _})),n.d(t,"detectAllDirectedCycle",(function(){return I})),n.d(t,"detectAllUndirectedCycle",(function(){return D})),n.d(t,"depthFirstSearch",(function(){return M})),n.d(t,"dijkstra",(function(){return R})),n.d(t,"findAllPath",(function(){return U})),n.d(t,"findShortestPath",(function(){return F})),n.d(t,"floydWarshall",(function(){return B})),n.d(t,"labelPropagation",(function(){return W})),n.d(t,"louvain",(function(){return te})),n.d(t,"iLouvain",(function(){return re})),n.d(t,"kCore",(function(){return ie})),n.d(t,"kMeans",(function(){return ue})),n.d(t,"cosineSimilarity",(function(){return se})),n.d(t,"nodesCosineSimilarity",(function(){return fe})),n.d(t,"minimumSpanningTree",(function(){return Le})),n.d(t,"pageRank",(function(){return Ae})),n.d(t,"getNeighbors",(function(){return l})),n.d(t,"Stack",(function(){return tt})),n.d(t,"GADDI",(function(){return Ze}));var r=function(e,t){var n=e.nodes,r=e.edges,o=[],i={};if(!n)throw new Error("invalid nodes data!");return n&&n.forEach((function(e,t){i[e.id]=t;var n=[];o.push(n)})),r&&r.forEach((function(e){var n=e.source,r=e.target,a=i[n],d=i[r];!a&&0!==a||!d&&0!==d||(o[a][d]=1,t||(o[d][a]=1))})),o},o=r,i=function(e,t){return e===t},a=function(){function e(e,t){void 0===t&&(t=null),this.value=e,this.next=t}return e.prototype.toString=function(e){return e?e(this.value):"".concat(this.value)},e}(),d=function(){function e(e){void 0===e&&(e=i),this.head=null,this.tail=null,this.compare=e}return e.prototype.prepend=function(e){var t=new a(e,this.head);return this.head=t,this.tail||(this.tail=t),this},e.prototype.append=function(e){var t=new a(e);return this.head?(this.tail.next=t,this.tail=t,this):(this.head=t,this.tail=t,this)},e.prototype.delete=function(e){if(!this.head)return null;var t=null;while(this.head&&this.compare(this.head.value,e))t=this.head,this.head=this.head.next;var n=this.head;if(null!==n)while(n.next)this.compare(n.next.value,e)?(t=n.next,n.next=n.next.next):n=n.next;return this.compare(this.tail.value,e)&&(this.tail=n),t},e.prototype.find=function(e){var t=e.value,n=void 0===t?void 0:t,r=e.callback,o=void 0===r?void 0:r;if(!this.head)return null;var i=this.head;while(i){if(o&&o(i.value))return i;if(void 0!==n&&this.compare(i.value,n))return i;i=i.next}return null},e.prototype.deleteTail=function(){var e=this.tail;if(this.head===this.tail)return this.head=null,this.tail=null,e;var t=this.head;while(t.next)t.next.next?t=t.next:t.next=null;return this.tail=t,e},e.prototype.deleteHead=function(){if(!this.head)return null;var e=this.head;return this.head.next?this.head=this.head.next:(this.head=null,this.tail=null),e},e.prototype.fromArray=function(e){var t=this;return e.forEach((function(e){return t.append(e)})),this},e.prototype.toArray=function(){var e=[],t=this.head;while(t)e.push(t),t=t.next;return e},e.prototype.reverse=function(){var e=this.head,t=null,n=null;while(e)n=e.next,e.next=t,t=e,e=n;this.tail=this.head,this.head=t},e.prototype.toString=function(e){return void 0===e&&(e=void 0),this.toArray().map((function(t){return t.toString(e)})).toString()},e}(),u=d,c=function(){function e(){this.linkedList=new u}return e.prototype.isEmpty=function(){return!this.linkedList.head},e.prototype.peek=function(){return this.linkedList.head?this.linkedList.head.value:null},e.prototype.enqueue=function(e){this.linkedList.append(e)},e.prototype.dequeue=function(){var e=this.linkedList.deleteHead();return e?e.value:null},e.prototype.toString=function(e){return this.linkedList.toString(e)},e}(),s=c,l=function(e,t,n){void 0===t&&(t=[]);var r=t.filter((function(t){return t.source===e||t.target===e}));if("target"===n){var o=function(t){return t.source===e};return r.filter(o).map((function(e){return e.target}))}if("source"===n){var i=function(t){return t.target===e};return r.filter(i).map((function(e){return e.source}))}var a=function(t){return t.source===e?t.target:t.source};return r.map(a)},f=function(e,t){return t.filter((function(t){return t.source===e}))},h=function(e,t){return t.filter((function(t){return t.source===e||t.target===e}))},v=function(e){void 0===e&&(e=0);var t="".concat(Math.random()).split(".")[1].substr(0,5),n="".concat(Math.random()).split(".")[1].substr(0,5);return"".concat(e,"-").concat(t).concat(n)};function p(e){void 0===e&&(e={});var t=e,n=function(){},r=function(){var e={};return function(t){var n=t.next,r=n;return!e[r]&&(e[r]=!0,!0)}}();return t.allowTraversal=e.allowTraversal||r,t.enter=e.enter||n,t.leave=e.leave||n,t}var g=function(e,t,n,r){void 0===r&&(r=!0);var o=p(n),i=new s,a=e.edges,d=void 0===a?[]:a;i.enqueue(t);var u="",c=function(){var e=i.dequeue();o.enter({current:e,previous:u}),l(e,d,r?"target":void 0).forEach((function(t){o.allowTraversal({previous:u,current:e,next:t})&&i.enqueue(t)})),o.leave({current:e,previous:u}),u=e};while(!i.isEmpty())c()},b=g,y=function(e){for(var t=e.nodes,n=void 0===t?[]:t,r=e.edges,o=void 0===r?[]:r,i=[],a={},d=[],u=function e(t){d.push(t),a[t.id]=!0;for(var r=l(t.id,o),i=function(t){var o=r[t];if(!a[o]){var i=n.filter((function(e){return e.id===o}));i.length>0&&e(i[0])}},u=0;u<r.length;++u)i(u)},c=0;c<n.length;c++){var s=n[c];if(!a[s.id]){u(s);var f=[];while(d.length>0)f.push(d.pop());i.push(f)}}return i},m=function(e){for(var t=e.nodes,n=void 0===t?[]:t,r=e.edges,o=void 0===r?[]:r,i=[],a={},d={},u={},c=[],s=0,f=function e(t){d[t.id]=s,u[t.id]=s,s+=1,i.push(t),a[t.id]=!0;for(var r=l(t.id,o,"target").filter((function(e){return n.map((function(e){return e.id})).indexOf(e)>-1})),f=function(o){var i=r[o];if(d[i]||0===d[i])a[i]&&(u[t.id]=Math.min(u[t.id],d[i]));else{var c=n.filter((function(e){return e.id===i}));c.length>0&&e(c[0]),u[t.id]=Math.min(u[t.id],u[i])}},h=0;h<r.length;h++)f(h);if(u[t.id]===d[t.id]){var v=[];while(i.length>0){var p=i.pop();if(a[p.id]=!1,v.push(p),p===t)break}v.length>0&&c.push(v)}},h=0,v=n;h<v.length;h++){var p=v[h];d[p.id]||0===d[p.id]||f(p)}return c};function E(e,t){return t?m(e):y(e)}var L=function(e){var t={},n=e.nodes,r=void 0===n?[]:n,o=e.edges,i=void 0===o?[]:o;return r.forEach((function(e){t[e.id]={degree:0,inDegree:0,outDegree:0}})),i.forEach((function(e){t[e.source].degree++,t[e.source].outDegree++,t[e.target].degree++,t[e.target].inDegree++})),t},w=L,A=function(e,t){var n=L(e);return n[t]?L(e)[t].inDegree:0},O=function(e,t){var n=L(e);return n[t]?L(e)[t].outDegree:0};function N(e){void 0===e&&(e={});var t=e,n=function(){},r=function(){var e={};return function(t){var n=t.next;return!e[n]&&(e[n]=!0,!0)}}();return t.allowTraversal=e.allowTraversal||r,t.enter=e.enter||n,t.leave=e.leave||n,t}function j(e,t,n,r){r.enter({current:t,previous:n});var o=e.edges,i=void 0===o?[]:o;l(t,i,"target").forEach((function(o){r.allowTraversal({previous:n,current:t,next:o})&&j(e,o,t,r)})),r.leave({current:t,previous:n})}function M(e,t,n){j(e,t,"",N(n))}var k=function(e){var t=null,n=e.nodes,r=void 0===n?[]:n,o={},i={},a={},d={};r.forEach((function(e){i[e.id]=e}));var u={enter:function(e){var n=e.current,r=e.previous;if(a[n]){t={};var d=n,u=r;while(u!==n)t[d]=u,d=u,u=o[u];t[d]=u}else a[n]=n,delete i[n],o[n]=r},leave:function(e){var t=e.current;d[t]=t,delete a[t]},allowTraversal:function(e){var n=e.next;return!t&&!d[n]}};while(Object.keys(i).length){var c=Object.keys(i)[0];M(e,c,u)}return t},D=function(e,t,n){var r,o;void 0===n&&(n=!0);for(var i=[],a=E(e,!1),d=0,u=a;d<u.length;d++){var c=u[d];if(c.length){var s=c[0],f=s.id,h=[s],v=(r={},r[f]=s,r),p=(o={},o[f]=new Set,o);while(h.length>0)for(var g=h.pop(),b=g.id,y=l(b,e.edges),m=function(r){var o,a=y[r],d=e.nodes.find((function(e){return e.id===a}));if(a===b)i.push((o={},o[a]=g,o));else if(a in p){if(!p[b].has(d)){var u=!0,c=[d,g],s=v[b];while(p[a].size&&!p[a].has(s)){if(c.push(s),s===v[s.id])break;s=v[s.id]}if(c.push(s),t&&n?(u=!1,c.findIndex((function(e){return t.indexOf(e.id)>-1}))>-1&&(u=!0)):t&&!n&&c.findIndex((function(e){return t.indexOf(e.id)>-1}))>-1&&(u=!1),u){for(var l={},f=1;f<c.length;f+=1)l[c[f-1].id]=c[f];c.length&&(l[c[c.length-1].id]=c[0]),i.push(l)}p[a].add(g)}}else v[a]=g,h.push(d),p[a]=new Set([g])},L=0;L<y.length;L+=1)m(L)}}return i},I=function(e,t,n){void 0===n&&(n=!0);for(var r=[],o=new Set,i=[],a=[],d={},u={},c=function(e){var t=[e];while(t.length>0){var n=t.pop();o.has(n)&&(o.delete(n),i[n.id].forEach((function(e){t.push(e)})),i[n.id].clear())}},s=function e(u,s,l){var f=!1;if(t&&!1===n&&t.indexOf(u.id)>-1)return f;r.push(u),o.add(u);for(var h=l[u.id],v=0;v<h.length;v+=1){var p=d[h[v]];if(p===s){for(var g={},b=1;b<r.length;b+=1)g[r[b-1].id]=r[b];r.length&&(g[r[r.length-1].id]=r[0]),a.push(g),f=!0}else o.has(p)||e(p,s,l)&&(f=!0)}if(f)c(u);else for(v=0;v<h.length;v+=1){p=d[h[v]];i[p.id].has(u)||i[p.id].add(u)}return r.pop(),f},f=e.nodes,h=void 0===f?[]:f,v=0;v<h.length;v+=1){var p=h[v],g=p.id;u[g]=v,d[v]=p}if(t&&n){var b=function(e){var n=t[e];u[h[e].id]=u[n],u[n]=0,d[0]=h.find((function(e){return e.id===n})),d[u[h[e].id]]=h[e]};for(v=0;v<t.length;v++)b(v)}var y=function(r){for(var o,i,d=1/0,c=0;c<r.length;c+=1)for(var s=r[c],f=0;f<s.length;f++){var h=u[s[f].id];h<d&&(d=h,i=c)}var v=r[i],p=[];for(c=0;c<v.length;c+=1){var g=v[c];p[g.id]=[];for(var b=0,y=l(g.id,e.edges,"target").filter((function(e){return v.map((function(e){return e.id})).indexOf(e)>-1}));b<y.length;b++){var m=y[b];m!==g.id||!1===n&&t.indexOf(g.id)>-1?p[g.id].push(u[m]):a.push((o={},o[g.id]=g,o))}}return{component:v,adjList:p,minIdx:d}},E=0;while(E<h.length){var L=h.filter((function(e){return u[e.id]>=E})),w=m({nodes:L,edges:e.edges}).filter((function(e){return e.length>1}));if(0===w.length)break;var A=y(w),O=A.minIdx,N=A.adjList,j=A.component;if(!(j.length>1))break;j.forEach((function(e){i[e.id]=new Set}));var M=d[O];if(t&&n&&-1===t.indexOf(M.id))return a;s(M,M,N),E=O+1}return a},_=function(e,t,n,r){return void 0===r&&(r=!0),t?I(e,n,r):D(e,n,r)},P=k,x=n("cb87"),S=n("8937"),C=function(e,t,n){for(var r,o=1/0,i=0;i<t.length;i++){var a=t[i].id;!n[a]&&e[a]<=o&&(o=e[a],r=t[i])}return r},T=function(e,t,n,r){var o=e.nodes,i=void 0===o?[]:o,a=e.edges,d=void 0===a?[]:a,u=[],c={},s={},l={};i.forEach((function(e,n){var r=e.id;u.push(r),s[r]=1/0,r===t&&(s[r]=0)}));for(var v=i.length,p=function(e){var t=C(s,i,c),o=t.id;if(c[o]=!0,s[o]===1/0)return"continue";var a=[];a=n?f(o,d):h(o,d),a.forEach((function(e){var n=e.target,i=e.source,a=n===o?i:n,d=r&&e[r]?e[r]:1;s[a]>s[t.id]+d?(s[a]=s[t.id]+d,l[a]=[t.id]):s[a]===s[t.id]+d&&l[a].push(t.id)}))},g=0;g<v;g++)p(g);l[t]=[t];var b={};for(var y in s)s[y]!==1/0&&G(t,y,l,b);var m={};for(var y in b)m[y]=b[y][0];return{length:s,path:m,allPath:b}},R=T;function G(e,t,n,r){if(e===t)return[e];if(r[t])return r[t];for(var o=[],i=0,a=n[t];i<a.length;i++){var d=a[i],u=G(e,d,n,r);if(!u)return;for(var c=0,s=u;c<s.length;c++){var l=s[c];Object(S["isArray"])(l)?o.push(Object(x["__spreadArray"])(Object(x["__spreadArray"])([],l,!0),[t],!1)):o.push([l,t])}}return r[t]=o,r[t]}var q,F=function(e,t,n,r,o){var i=R(e,t,r,o),a=i.length,d=i.path,u=i.allPath;return{length:a[n],path:d[n],allPath:u[n]}},U=function(e,t,n,r){var o;if(t===n)return[[t]];var i=e.edges,a=void 0===i?[]:i,d=[t],u=(o={},o[t]=!0,o),c=[],s=[],f=r?l(t,a,"target"):l(t,a);c.push(f);while(d.length>0&&c.length>0){var h=c[c.length-1];if(h.length){var v=h.shift();if(v&&(d.push(v),u[v]=!0,f=r?l(v,a,"target"):l(v,a),c.push(f.filter((function(e){return!u[e]})))),d[d.length-1]===n){var p=d.map((function(e){return e}));s.push(p);g=d.pop();u[g]=!1,c.pop()}}else{var g=d.pop();u[g]=!1,c.pop()}}return s},H=function(e,t){for(var n=o(e,t),r=[],i=n.length,a=0;a<i;a+=1){r[a]=[];for(var d=0;d<i;d+=1)a===d?r[a][d]=0:0!==n[a][d]&&n[a][d]?r[a][d]=n[a][d]:r[a][d]=1/0}for(var u=0;u<i;u+=1)for(a=0;a<i;a+=1)for(d=0;d<i;d+=1)r[a][d]>r[a][u]+r[u][d]&&(r[a][d]=r[a][u]+r[u][d]);return r},B=H,V=function(e,t,n,r){void 0===t&&(t=!1),void 0===n&&(n="weight"),void 0===r&&(r=1e3);var i=e.nodes,a=void 0===i?[]:i,d=e.edges,u=void 0===d?[]:d,c={},s={};a.forEach((function(e,t){var n=v();e.clusterId=n,c[n]={id:n,nodes:[e]},s[e.id]={node:e,idx:t}}));var l=o(e,t),f=[],h={};l.forEach((function(e,t){var n=0,r=a[t].id;h[r]={},e.forEach((function(e,t){if(e){n+=e;var o=a[t].id;h[r][o]=e}})),f.push(n)}));var p=0,g=function(){var e=!1;if(a.forEach((function(t){var n={};Object.keys(h[t.id]).forEach((function(e){var r=h[t.id][e],o=s[e].node,i=o.clusterId;n[i]||(n[i]=0),n[i]+=r}));var r=-1/0,o=[];if(Object.keys(n).forEach((function(e){r<n[e]?(r=n[e],o=[e]):r===n[e]&&o.push(e)})),1!==o.length||o[0]!==t.clusterId){var i=o.indexOf(t.clusterId);if(i>=0&&o.splice(i,1),o&&o.length){e=!0;var a=c[t.clusterId],d=a.nodes.indexOf(t);a.nodes.splice(d,1);var u=Math.floor(Math.random()*o.length),l=c[o[u]];l.nodes.push(t),t.clusterId=l.id}}})),!e)return"break";p++};while(p<r){var b=g();if("break"===b)break}Object.keys(c).forEach((function(e){var t=c[e];t.nodes&&t.nodes.length||delete c[e]}));var y=[],m={};u.forEach((function(e){var t=e.source,r=e.target,o=e[n]||1,i=s[t].node.clusterId,a=s[r].node.clusterId,d="".concat(i,"---").concat(a);if(m[d])m[d].weight+=o,m[d].count++;else{var u={source:i,target:a,weight:o,count:1};m[d]=u,y.push(u)}}));var E=[];return Object.keys(c).forEach((function(e){E.push(c[e])})),{clusters:E,clusterEdges:y}},W=V,z=function(){function e(e){this.arr=e}return e.prototype.getArr=function(){return this.arr||[]},e.prototype.add=function(t){var n,r=t.arr;if(!(null===(n=this.arr)||void 0===n?void 0:n.length))return new e(r);if(!(null===r||void 0===r?void 0:r.length))return new e(this.arr);if(this.arr.length===r.length){var o=[];for(var i in this.arr)o[i]=this.arr[i]+r[i];return new e(o)}},e.prototype.subtract=function(t){var n,r=t.arr;if(!(null===(n=this.arr)||void 0===n?void 0:n.length))return new e(r);if(!(null===r||void 0===r?void 0:r.length))return new e(this.arr);if(this.arr.length===r.length){var o=[];for(var i in this.arr)o[i]=this.arr[i]-r[i];return new e(o)}},e.prototype.avg=function(t){var n=[];if(0!==t)for(var r in this.arr)n[r]=this.arr[r]/t;return new e(n)},e.prototype.negate=function(){var t=[];for(var n in this.arr)t[n]=-this.arr[n];return new e(t)},e.prototype.squareEuclideanDistance=function(e){var t,n=e.arr;if(!(null===(t=this.arr)||void 0===t?void 0:t.length)||!(null===n||void 0===n?void 0:n.length))return 0;if(this.arr.length===n.length){var r=0;for(var o in this.arr)r+=Math.pow(this.arr[o]-e.arr[o],2);return r}},e.prototype.euclideanDistance=function(e){var t,n=e.arr;if(!(null===(t=this.arr)||void 0===t?void 0:t.length)||!(null===n||void 0===n?void 0:n.length))return 0;if(this.arr.length===n.length){var r=0;for(var o in this.arr)r+=Math.pow(this.arr[o]-e.arr[o],2);return Math.sqrt(r)}console.error("The two vectors are unequal in length.")},e.prototype.normalize=function(){var t=[],n=Object(S["clone"])(this.arr);n.sort((function(e,t){return e-t}));var r=n[n.length-1],o=n[0];for(var i in this.arr)t[i]=(this.arr[i]-o)/(r-o);return new e(t)},e.prototype.norm2=function(){var e;if(!(null===(e=this.arr)||void 0===e?void 0:e.length))return 0;var t=0;for(var n in this.arr)t+=Math.pow(this.arr[n],2);return Math.sqrt(t)},e.prototype.dot=function(e){var t,n=e.arr;if(!(null===(t=this.arr)||void 0===t?void 0:t.length)||!(null===n||void 0===n?void 0:n.length))return 0;if(this.arr.length===n.length){var r=0;for(var o in this.arr)r+=this.arr[o]*e.arr[o];return r}console.error("The two vectors are unequal in length.")},e.prototype.equal=function(e){var t,n=e.arr;if((null===(t=this.arr)||void 0===t?void 0:t.length)!==(null===n||void 0===n?void 0:n.length))return!1;for(var r in this.arr)if(this.arr[r]!==n[r])return!1;return!0},e}(),K=z,$=function(e,t){void 0===t&&(t=void 0);var n=[];return e.forEach((function(e){void 0===t&&n.push(e),void 0!==e[t]&&n.push(e[t])})),n};(function(e){e["EuclideanDistance"]="euclideanDistance"})(q||(q={}));var J=function(e,t,n){var r=[];(null===t||void 0===t?void 0:t.length)?r=t:(e.forEach((function(e){r=r.concat(Object.keys(e))})),r=Object(S["uniq"])(r));var o={};return r.forEach((function(t){var r=[];e.forEach((function(e){void 0!==e[t]&&""!==e[t]&&r.push(e[t])})),r.length&&!(null===n||void 0===n?void 0:n.includes(t))&&(o[t]=Object(S["uniq"])(r))})),o},Q=function(e,t,n){var r=J(e,t,n),o=[];return Object.keys(r).length?(e.forEach((function(e,t){var n=[];if(1===Object.keys(r).length){var i=Object.keys(r)[0],a=r[i];a.every((function(e){return!isNaN(Number(e))}))&&(n=[e[i]])}else Object.keys(r).forEach((function(t){for(var o=e[t],i=r[t],a=i.findIndex((function(e){return o===e})),d=[],u=0;u<i.length;u++)u===a?d.push(1):d.push(0);n=n.concat(d)}));o[t]=n})),o):o},X=function(e,t,n,r){void 0===n&&(n=q.EuclideanDistance);var o=0;switch(n){case q.EuclideanDistance:o=new K(e).euclideanDistance(new K(t));break;default:break}return o},Y=function(e,t,n,r){for(var o=t.length,i=2*r,a=0,d=0;d<o;d++)for(var u=e[d].clusterId,c=0;c<o;c++){var s=e[c].clusterId;if(u===s){var l=t[d][c]||0,f=n[d]||0,h=n[c]||0;a+=l-f*h/i}}return a*=1/i,a},Z=function(e,t){void 0===e&&(e=[]);for(var n=e.length,r=new K([]),o=0;o<n;o++)r=r.add(new K(t[o]));var i=r.avg(n);i.normalize();var a=0;for(o=0;o<n;o++){var d=new K(t[o]),u=d.squareEuclideanDistance(i);a+=u}var c=[];e.forEach((function(){c.push([])}));for(o=0;o<n;o++){d=new K(t[o]);e[o]["clusterInertial"]=0;for(var s=0;s<n;s++)if(o!==s){var l=new K(t[s]);c[o][s]=d.squareEuclideanDistance(l),e[o]["clusterInertial"]+=c[o][s]}else c[o][s]=0}var f=0,h=2*n*a;for(o=0;o<n;o++){var v=e[o].clusterId;for(s=0;s<n;s++){var p=e[s].clusterId;if(o!==s&&v===p){var g=e[o].clusterInertial*e[s].clusterInertial/Math.pow(h,2)-c[o][s]/h;f+=g}}}return Number(f.toFixed(4))},ee=function(e,t,n,r,i,a,d,u,c){void 0===t&&(t=!1),void 0===n&&(n="weight"),void 0===r&&(r=1e-4),void 0===i&&(i=!1),void 0===a&&(a=void 0),void 0===d&&(d=[]),void 0===u&&(u=["id"]),void 0===c&&(c=1);var s=e.nodes,l=void 0===s?[]:s,f=e.edges,h=void 0===f?[]:f,v=[];if(i){l.forEach((function(e,t){e.properties=e.properties||{},e.originIndex=t}));var p=[];l.every((function(e){return e.hasOwnProperty("nodeType")}))&&(p=Array.from(new Set(l.map((function(e){return e.nodeType})))),l.forEach((function(e){e.properties.nodeType=p.findIndex((function(t){return t===e.nodeType}))})));var g=$(l,a);v=Q(g,d,u)}var b=1,y={},m={};l.forEach((function(e,t){var n=String(b++);e.clusterId=n,y[n]={id:n,nodes:[e]},m[e.id]={node:e,idx:t}}));var E=o(e,t),L=[],w={},A=0;E.forEach((function(e,t){var n=0,r=l[t].id;w[r]={},e.forEach((function(e,t){if(e){n+=e;var o=l[t].id;w[r][o]=e,A+=e}})),L.push(n)})),A/=2;var O=1/0,N=1/0,j=0,M=[],k={};while(1){O=i&&l.every((function(e){return e.hasOwnProperty("properties")}))?Y(l,E,L,A)+Z(l,v)*c:Y(l,E,L,A),0===j&&(N=O,M=l,k=y);var D=O>0&&O>N&&O-N<r;if(O>N&&(M=l.map((function(e){return{node:e,clusterId:e.clusterId}})),k=Object(S["clone"])(y),N=O),D||j>100)break;j++,Object.keys(y).forEach((function(e){var t=0;h.forEach((function(r){var o=r.source,i=r.target,a=m[o].node.clusterId,d=m[i].node.clusterId;(a===e&&d!==e||d===e&&a!==e)&&(t+=r[n]||1)})),y[e].sumTot=t})),l.forEach((function(e,t){var r,o=y[e.clusterId],a=0,d=L[t]/(2*A),u=0,s=o.nodes;s.forEach((function(e){var n=m[e.id].idx;u+=E[t][n]||0}));var l=u-o.sumTot*d,f=s.filter((function(t){return t.id!==e.id})),p=[];f.forEach((function(e,t){p[t]=v[e.originIndex]}));var g=Z(f,v)*c,b=w[e.id];if(Object.keys(b).forEach((function(n){var o=m[n].node,u=o.clusterId;if(u!==e.clusterId){var s=y[u],f=s.nodes;if(f&&f.length){var h=0;f.forEach((function(e){var n=m[e.id].idx;h+=E[t][n]||0}));var p=h-s.sumTot*d,b=f.concat([e]),L=[];b.forEach((function(e,t){L[t]=v[e.originIndex]}));var w=Z(b,v)*c,A=p-l;i&&(A=p+w-(l+g)),A>a&&(a=A,r=s)}}})),a>0){r.nodes.push(e);var O=e.clusterId;e.clusterId=r.id;var N=o.nodes.indexOf(e);o.nodes.splice(N,1);var j=0,M=0;h.forEach((function(e){var t=e.source,o=e.target,i=m[t].node.clusterId,a=m[o].node.clusterId;(i===r.id&&a!==r.id||a===r.id&&i!==r.id)&&(j+=e[n]||1),(i===O&&a!==O||a===O&&i!==O)&&(M+=e[n]||1)})),r.sumTot=j,o.sumTot=M}}))}var I={},_=0;Object.keys(k).forEach((function(e){var t=k[e];if(t.nodes&&t.nodes.length){var n=String(_+1);n!==e&&(t.id=n,t.nodes=t.nodes.map((function(e){return{id:e.id,clusterId:n}})),k[n]=t,I[e]=n,delete k[e],_++)}else delete k[e]})),M.forEach((function(e){var t=e.node,n=e.clusterId;t&&(t.clusterId=n,t.clusterId&&I[t.clusterId]&&(t.clusterId=I[t.clusterId]))}));var P=[],x={};h.forEach((function(e){var t=e.source,r=e.target,o=e[n]||1,i=m[t].node.clusterId,a=m[r].node.clusterId;if(i&&a){var d="".concat(i,"---").concat(a);if(x[d])x[d].weight+=o,x[d].count++;else{var u={source:i,target:a,weight:o,count:1};x[d]=u,P.push(u)}}}));var C=[];return Object.keys(k).forEach((function(e){C.push(k[e])})),{clusters:C,clusterEdges:P}},te=ee,ne=function(e,t,n,r,o,i,a,d){return void 0===t&&(t=!1),void 0===n&&(n="weight"),void 0===r&&(r=1e-4),void 0===o&&(o=void 0),void 0===i&&(i=[]),void 0===a&&(a=["id"]),void 0===d&&(d=1),te(e,t,n,r,!0,o,i,a,d)},re=ne,oe=function(e,t){var n;void 0===t&&(t=1);var r=Object(S["clone"])(e),o=r.nodes,i=void 0===o?[]:o,a=r.edges,d=void 0===a?[]:a,u=function(){var e=w({nodes:i,edges:d}),r=Object.keys(e);r.sort((function(t,n){var r,o;return(null===(r=e[t])||void 0===r?void 0:r.degree)-(null===(o=e[n])||void 0===o?void 0:o.degree)}));var o=r[0];if(!i.length||(null===(n=e[o])||void 0===n?void 0:n.degree)>=t)return"break";var a=i.findIndex((function(e){return e.id===o}));i.splice(a,1),d=d.filter((function(e){return!(e.source===o||e.target===o)}))};while(1){var c=u();if("break"===c)break}return{nodes:i,edges:d}},ie=oe,ae=function(e,t,n){var r=[];switch(e){case q.EuclideanDistance:r=t[n];break;default:r=[];break}return r},de=function(e,t,n,r,o,i){void 0===t&&(t=3),void 0===n&&(n=void 0),void 0===r&&(r=[]),void 0===o&&(o=["id"]),void 0===i&&(i=q.EuclideanDistance);var a=e.nodes,d=void 0===a?[]:a,u=e.edges,c=void 0===u?[]:u,s={clusters:[{id:"0",nodes:d}],clusterEdges:[]};if(i===q.EuclideanDistance&&!d.every((function(e){return e.hasOwnProperty(n)})))return s;var l=[],f=[];if(i===q.EuclideanDistance&&(l=$(d,n),f=Q(l,r,o)),!f.length)return s;for(var h=Object(S["uniq"])(f.map((function(e){return e.join("")}))),v=Math.min(t,d.length,h.length),p=0;p<d.length;p++)d[p].originIndex=p;var g=[],b=[],y=[];for(p=0;p<v;p++)if(0===p){var m=Math.floor(Math.random()*d.length);switch(i){case q.EuclideanDistance:g[p]=f[m];break;default:g[p]=[];break}b.push(m),y[p]=[d[m]],d[m].clusterId=String(p)}else{for(var E=-1/0,L=0,w=function(e){if(!b.includes(e)){for(var t=0,n=0;n<g.length;n++){var r=0;switch(i){case q.EuclideanDistance:r=X(f[d[e].originIndex],g[n],i);break;default:break}t+=r}var o=t/g.length;o>E&&!g.find((function(t){return Object(S["isEqual"])(t,ae(i,f,d[e].originIndex))}))&&(E=o,L=e)}},A=0;A<d.length;A++)w(A);g[p]=ae(i,f,L),b.push(L),y[p]=[d[L]],d[L].clusterId=String(p)}var O=0;while(1){for(p=0;p<d.length;p++){var N=0,j=1/0;if(0!==O||!b.includes(p)){for(var M=0;M<g.length;M++){var k=0;switch(i){case q.EuclideanDistance:k=X(f[p],g[M],i);break;default:break}k<j&&(j=k,N=M)}if(void 0!==d[p].clusterId)for(var D=y[Number(d[p].clusterId)].length-1;D>=0;D--)y[Number(d[p].clusterId)][D].id===d[p].id&&y[Number(d[p].clusterId)].splice(D,1);d[p].clusterId=String(N),y[N].push(d[p])}}var I=!1;for(p=0;p<y.length;p++){var _=y[p],P=new K([]);for(M=0;M<_.length;M++)P=P.add(new K(f[_[M].originIndex]));var x=P.avg(_.length);x.equal(new K(g[p]))||(I=!0,g[p]=x.getArr())}if(O++,d.every((function(e){return void 0!==e.clusterId}))&&I||O>=1e3)break}var C=[],T={};return c.forEach((function(e){var t,n,r=e.source,o=e.target,i=null===(t=d.find((function(e){return e.id===r})))||void 0===t?void 0:t.clusterId,a=null===(n=d.find((function(e){return e.id===o})))||void 0===n?void 0:n.clusterId,u="".concat(i,"---").concat(a);if(T[u])T[u].count++;else{var c={source:i,target:a,count:1};T[u]=c,C.push(c)}})),{clusters:y,clusterEdges:C}},ue=de,ce=function(e,t){var n=new K(t),r=n.norm2(),o=new K(e),i=o.norm2(),a=n.dot(o),d=r*i,u=d?a/d:0;return u},se=ce,le=function(e,t,n,r,o){void 0===e&&(e=[]),void 0===n&&(n=void 0),void 0===r&&(r=[]),void 0===o&&(o=[]);var i=Object(S["clone"])(e.filter((function(e){return e.id!==t.id}))),a=e.findIndex((function(e){return e.id===t.id})),d=$(e,n),u=Q(d,r,o),c=u[a],s=[];return i.forEach((function(e,n){if(e.id!==t.id){var r=u[n],o=se(r,c);s.push(o),e.cosineSimilarity=o}})),i.sort((function(e,t){return t.cosineSimilarity-e.cosineSimilarity})),{allCosineSimilarity:s,similarNodes:i}},fe=le,he=function(){function e(e){this.count=e.length,this.parent={};for(var t=0,n=e;t<n.length;t++){var r=n[t];this.parent[r]=r}}return e.prototype.find=function(e){while(this.parent[e]!==e)e=this.parent[e];return e},e.prototype.union=function(e,t){var n=this.find(e),r=this.find(t);n!==r&&(n<r?(this.parent[t]!==t&&this.union(this.parent[t],e),this.parent[t]=this.parent[e]):(this.parent[e]!==e&&this.union(this.parent[e],t),this.parent[e]=this.parent[t]))},e.prototype.connected=function(e,t){return this.find(e)===this.find(t)},e}(),ve=he,pe=function(e,t){return e-t},ge=function(){function e(e){void 0===e&&(e=pe),this.compareFn=e,this.list=[]}return e.prototype.getLeft=function(e){return 2*e+1},e.prototype.getRight=function(e){return 2*e+2},e.prototype.getParent=function(e){return 0===e?null:Math.floor((e-1)/2)},e.prototype.isEmpty=function(){return this.list.length<=0},e.prototype.top=function(){return this.isEmpty()?void 0:this.list[0]},e.prototype.delMin=function(){var e=this.top(),t=this.list.pop();return this.list.length>0&&(this.list[0]=t,this.moveDown(0)),e},e.prototype.insert=function(e){if(null!==e){this.list.push(e);var t=this.list.length-1;return this.moveUp(t),!0}return!1},e.prototype.moveUp=function(e){var t=this.getParent(e);while(e&&e>0&&this.compareFn(this.list[t],this.list[e])>0){var n=this.list[t];this.list[t]=this.list[e],this.list[e]=n,e=t,t=this.getParent(e)}},e.prototype.moveDown=function(e){var t,n=e,r=this.getLeft(e),o=this.getRight(e),i=this.list.length;null!==r&&r<i&&this.compareFn(this.list[n],this.list[r])>0?n=r:null!==o&&o<i&&this.compareFn(this.list[n],this.list[o])>0&&(n=o),e!==n&&(t=[this.list[n],this.list[e]],this.list[e]=t[0],this.list[n]=t[1],this.moveDown(n))},e}(),be=ge,ye=function(e,t){var n=[],r=e.nodes,o=void 0===r?[]:r,i=e.edges,a=void 0===i?[]:i;if(0===o.length)return n;var d=o[0],u=new Set;u.add(d);var c=function(e,n){return t?e.weight-n.weight:0},s=new be(c);h(d.id,a).forEach((function(e){s.insert(e)}));while(!s.isEmpty()){var l=s.delMin(),f=l.source,v=l.target;u.has(f)&&u.has(v)||(n.push(l),u.has(f)||(u.add(f),h(f,a).forEach((function(e){s.insert(e)}))),u.has(v)||(u.add(v),h(v,a).forEach((function(e){s.insert(e)}))))}return n},me=function(e,t){var n=[],r=e.nodes,o=void 0===r?[]:r,i=e.edges,a=void 0===i?[]:i;if(0===o.length)return n;var d=a.map((function(e){return e}));t&&d.sort((function(e,t){return e.weight-t.weight}));var u=new ve(o.map((function(e){return e.id})));while(d.length>0){var c=d.shift(),s=c.source,l=c.target;u.connected(s,l)||(n.push(c),u.union(s,l))}return n},Ee=function(e,t,n){var r={prim:ye,kruskal:me};return n?r[n](e,t):me(e,t)},Le=Ee,we=function(e,t,n){"number"!==typeof t&&(t=1e-6),"number"!==typeof n&&(n=.85);for(var r,o=1,i=0,a=1e3,d=e.nodes,u=void 0===d?[]:d,c=e.edges,s=void 0===c?[]:c,f=u.length,h={},v={},p=0;p<f;++p){var g=u[p],b=g.id;h[b]=1/f,v[b]=1/f}var y=w(e);while(a>0&&o>t){i=0;for(p=0;p<f;++p){g=u[p],b=g.id;if(r=0,0===y[g.id].inDegree)h[b]=0;else{for(var m=l(b,s,"source"),E=0;E<m.length;++E){var L=m[E],A=y[L].outDegree;A>0&&(r+=v[L]/A)}h[b]=n*r,i+=h[b]}}i=(1-i)/f,o=0;for(p=0;p<f;++p){g=u[p],b=g.id;r=h[b]+i,o+=Math.abs(r-v[b]),v[b]=r}a-=1}return v},Ae=we,Oe=-1,Ne=-1,je="-1",Me="-1",ke=-1,De=function(){function e(e,t,n,r){void 0===e&&(e=Oe),void 0===t&&(t=Ne),void 0===n&&(n=Ne),void 0===r&&(r=je),this.id=e,this.from=t,this.to=n,this.label=r}return e}(),Ie=function(){function e(e,t){void 0===e&&(e=Ne),void 0===t&&(t=Me),this.id=e,this.label=t,this.edges=[],this.edgeMap={}}return e.prototype.addEdge=function(e){this.edges.push(e),this.edgeMap[e.id]=e},e}(),_e=function(){function e(e,t,n){void 0===e&&(e=Ne),void 0===t&&(t=!0),void 0===n&&(n=!1),this.id=e,this.edgeIdAutoIncrease=t,this.edges=[],this.nodes=[],this.nodeMap={},this.edgeMap={},this.nodeLabelMap={},this.edgeLabelMap={},this.counter=0,this.directed=n}return e.prototype.getNodeNum=function(){return this.nodes.length},e.prototype.addNode=function(e,t){if(!this.nodeMap[e]){var n=new Ie(e,t);this.nodes.push(n),this.nodeMap[e]=n,this.nodeLabelMap[t]||(this.nodeLabelMap[t]=[]),this.nodeLabelMap[t].push(e)}},e.prototype.addEdge=function(e,t,n,r){if((this.edgeIdAutoIncrease||void 0===e)&&(e=this.counter++),!(this.nodeMap[t]&&this.nodeMap[n]&&this.nodeMap[n].edgeMap[e])){var o=new De(e,t,n,r);if(this.edges.push(o),this.edgeMap[e]=o,this.nodeMap[t].addEdge(o),this.edgeLabelMap[r]||(this.edgeLabelMap[r]=[]),this.edgeLabelMap[r].push(o),!this.directed){var i=new De(e,n,t,r);this.nodeMap[n].addEdge(i),this.edgeLabelMap[r].push(i)}}},e}(),Pe=function(){function e(e,t,n,r,o){this.fromNode=e,this.toNode=t,this.nodeEdgeNodeLabel={nodeLabel1:n||Me,edgeLabel:r||je,nodeLabel2:o||Me}}return e.prototype.equalTo=function(e){return this.fromNode===e.formNode&&this.toNode===e.toNode&&this.nodeEdgeNodeLabel===e.nodeEdgeNodeLabel},e.prototype.notEqualTo=function(e){return!this.equalTo(e)},e}(),xe=function(){function e(){this.rmpath=[],this.dfsEdgeList=[]}return e.prototype.equalTo=function(e){var t=this.dfsEdgeList.length,n=e.length;if(t!==n)return!1;for(var r=0;r<t;r++)if(this.dfsEdgeList[r]!==e[r])return!1;return!0},e.prototype.notEqualTo=function(e){return!this.equalTo(e)},e.prototype.pushBack=function(e,t,n,r,o){return this.dfsEdgeList.push(new Pe(e,t,n,r,o)),this.dfsEdgeList},e.prototype.toGraph=function(e,t){void 0===e&&(e=ke),void 0===t&&(t=!1);var n=new _e(e,!0,t);return this.dfsEdgeList.forEach((function(e){var t=e.fromNode,r=e.toNode,o=e.nodeEdgeNodeLabel,i=o.nodeLabel1,a=o.edgeLabel,d=o.nodeLabel2;i!==Me&&n.addNode(t,i),d!==Me&&n.addNode(r,d),i!==Me&&d!==i&&n.addEdge(void 0,t,r,a)})),n},e.prototype.buildRmpath=function(){this.rmpath=[];for(var e=void 0,t=this.dfsEdgeList.length,n=t-1;n>=0;n--){var r=this.dfsEdgeList[n],o=r.fromNode,i=r.toNode;o<i&&(void 0===e||i===e)&&(this.rmpath.push(n),e=o)}return this.rmpath},e.prototype.getNodeNum=function(){var e={};return this.dfsEdgeList.forEach((function(t){e[t.fromNode]||(e[t.fromNode]=!0),e[t.toNode]||(e[t.toNode]=!0)})),Object.keys(e).length},e}(),Se=function(){function e(e){if(this.his={},this.nodesUsed={},this.edgesUsed={},this.edges=[],e){while(e){var t=e.edge;this.edges.push(t),this.nodesUsed[t.from]=1,this.nodesUsed[t.to]=1,this.edgesUsed[t.id]=1,e=e.preNode}this.edges=this.edges.reverse()}}return e.prototype.hasNode=function(e){return 1===this.nodesUsed[e.id]},e.prototype.hasEdge=function(e){return 1===this.edgesUsed[e.id]},e}(),Ce=function(){function e(e){var t=e.graphs,n=e.minSupport,r=void 0===n?2:n,o=e.minNodeNum,i=void 0===o?1:o,a=e.maxNodeNum,d=void 0===a?4:a,u=e.top,c=void 0===u?10:u,s=e.directed,l=void 0!==s&&s,f=e.verbose,h=void 0!==f&&f;this.graphs=t,this.dfsCode=new xe,this.support=0,this.frequentSize1Subgraphs=[],this.frequentSubgraphs=[],this.minSupport=r,this.top=c,this.directed=l,this.counter=0,this.maxNodeNum=d,this.minNodeNum=i,this.verbose=h,this.maxNodeNum<this.minNodeNum&&(this.maxNodeNum=this.minNodeNum),this.reportDF=[]}return e.prototype.findForwardRootEdges=function(e,t){var n=this,r=[],o=e.nodeMap;return t.edges.forEach((function(e){(n.directed||t.label<=o[e.to].label)&&r.push(e)})),r},e.prototype.findBackwardEdge=function(e,t,n,r){if(!this.directed&&t===n)return null;for(var o=e.nodeMap,i=o[n.to],a=i.edges,d=a.length,u=0;u<d;u++){var c=a[u];if(!r.hasEdge(c)&&c.to===t.from)if(this.directed){if(o[t.from].label<o[n.to].label||o[t.from].label===o[n.to].label&&t.label<=c.label)return c}else if(t.label<c.label||t.label===c.label&&o[t.to].label<=o[n.to].label)return c}return null},e.prototype.findForwardPureEdges=function(e,t,n,r){for(var o=[],i=t.to,a=e.nodeMap[i].edges,d=a.length,u=0;u<d;u++){var c=a[u],s=e.nodeMap[c.to];n<=s.label&&!r.hasNode(s)&&o.push(c)}return o},e.prototype.findForwardRmpathEdges=function(e,t,n,r){for(var o=[],i=e.nodeMap,a=i[t.to].label,d=i[t.from],u=d.edges,c=u.length,s=0;s<c;s++){var l=u[s],f=i[l.to].label;t.to===l.to||n>f||r.hasNode(i[l.to])||(t.label<l.label||t.label===l.label&&a<=f)&&o.push(l)}return o},e.prototype.getSupport=function(e){var t={};return e.forEach((function(e){t[e.graphId]||(t[e.graphId]=!0)})),Object.keys(t).length},e.prototype.findMinLabel=function(e){var t=void 0;return Object.keys(e).forEach((function(n){var r=e[n],o=r.nodeLabel1,i=r.edgeLabel,a=r.nodeLabel2;t?(o<t.nodeLabel1||o===t.nodeLabel1&&i<t.edgeLabel||o===t.nodeLabel1&&i===t.edgeLabel&&a<t.nodeLabel2)&&(t={nodeLabel1:o,edgeLabel:i,nodeLabel2:a}):t={nodeLabel1:o,edgeLabel:i,nodeLabel2:a}})),t},e.prototype.isMin=function(){var e=this,t=this.dfsCode;if(this.verbose&&console.log("isMin checking",t),1===t.dfsEdgeList.length)return!0;var n=this.directed,r=t.toGraph(ke,n),o=r.nodeMap,i=new xe,a={};r.nodes.forEach((function(t){var n=e.findForwardRootEdges(r,t);n.forEach((function(e){var n=o[e.to],i="".concat(t.label,"-").concat(e.label,"-").concat(n.label);a[i]||(a[i]={projected:[],nodeLabel1:t.label,edgeLabel:e.label,nodeLabel2:n.label});var d={graphId:r.id,edge:e,preNode:null};a[i].projected.push(d)}))}));var d=this.findMinLabel(a);if(d){i.dfsEdgeList.push(new Pe(0,1,d.nodeLabel1,d.edgeLabel,d.nodeLabel2));var u=function a(d){for(var u=i.buildRmpath(),c=i.dfsEdgeList[0].nodeEdgeNodeLabel.nodeLabel1,s=i.dfsEdgeList[u[0]].toNode,l={},f=!1,h=0,v=n?-1:0,p=function(t){if(f)return"break";d.forEach((function(n){var o=new Se(n),a=e.findBackwardEdge(r,o.edges[u[t]],o.edges[u[0]],o);a&&(l[a.label]||(l[a.label]={projected:[],edgeLabel:a.label}),l[a.label].projected.push({graphId:r.id,edge:l,preNode:n}),h=i.dfsEdgeList[u[t]].fromNode,f=!0)}))},g=u.length-1;g>v;g--){var b=p(g);if("break"===b)break}if(f){var y=e.findMinLabel(l);i.dfsEdgeList.push(new Pe(s,h,Me,y.edgeLabel,Me));var m=i.dfsEdgeList.length-1;return e.dfsCode.dfsEdgeList[m]===i.dfsEdgeList[m]&&a(l[y.edgeLabel].projected)}var E={};f=!1;var L=0;d.forEach((function(t){var n=new Se(t),i=e.findForwardPureEdges(r,n.edges[u[0]],c,n);i.length>0&&(f=!0,L=s,i.forEach((function(e){var n="".concat(e.label,"-").concat(o[e.to].label);E[n]||(E[n]={projected:[],edgeLabel:e.label,nodeLabel2:o[e.to].label}),E[n].projected.push({graphId:r.id,edge:e,preNode:t})})))}));var w=u.length,A=function(t){if(f)return"break";var n=u[t];d.forEach((function(t){var a=new Se(t),d=e.findForwardRmpathEdges(r,a.edges[n],c,a);d.length>0&&(f=!0,L=i.dfsEdgeList[n].fromNode,d.forEach((function(e){var n="".concat(e.label,"-").concat(o[e.to].label);E[n]||(E[n]={projected:[],edgeLabel:e.label,nodeLabel2:o[e.to].label}),E[n].projected.push({graphId:r.id,edge:e,preNode:t})})))}))};for(g=0;g<w;g++){var O=A(g);if("break"===O)break}if(!f)return!0;var N=e.findMinLabel(E);i.dfsEdgeList.push(new Pe(L,s+1,Me,N.edgeLabel,N.nodeLabel2));var j=i.dfsEdgeList.length-1;return t.dfsEdgeList[j]===i.dfsEdgeList[j]&&a(E["".concat(N.edgeLabel,"-").concat(N.nodeLabel2)].projected)},c="".concat(d.nodeLabel1,"-").concat(d.edgeLabel,"-").concat(d.nodeLabel2);return u(a[c].projected)}},e.prototype.report=function(){if(!(this.dfsCode.getNodeNum()<this.minNodeNum)){this.counter++;var e=this.dfsCode.toGraph(this.counter,this.directed);this.frequentSubgraphs.push(Object(S["clone"])(e))}},e.prototype.subGraphMining=function(e){var t=this,n=this.getSupport(e);if(!(n<this.minSupport)&&this.isMin()){this.report();var r=this.dfsCode.getNodeNum(),o=this.dfsCode.buildRmpath(),i=this.dfsCode.dfsEdgeList[o[0]].toNode,a=this.dfsCode.dfsEdgeList[0].nodeEdgeNodeLabel.nodeLabel1,d={},u={};e.forEach((function(e){for(var n=t.graphs[e.graphId],c=n.nodeMap,s=new Se(e),l=o.length-1;l>=0;l--){var f=t.findBackwardEdge(n,s.edges[o[l]],s.edges[o[0]],s);if(f){var h="".concat(t.dfsCode.dfsEdgeList[o[l]].fromNode,"-").concat(f.label);u[h]||(u[h]={projected:[],toNodeId:t.dfsCode.dfsEdgeList[o[l]].fromNode,edgeLabel:f.label}),u[h].projected.push({graphId:e.graphId,edge:f,preNode:e})}}if(!(r>=t.maxNodeNum)){var v=t.findForwardPureEdges(n,s.edges[o[0]],a,s);v.forEach((function(t){var n="".concat(i,"-").concat(t.label,"-").concat(c[t.to].label);d[n]||(d[n]={projected:[],fromNodeId:i,edgeLabel:t.label,nodeLabel2:c[t.to].label}),d[n].projected.push({graphId:e.graphId,edge:t,preNode:e})}));var p=function(r){var i=t.findForwardRmpathEdges(n,s.edges[o[r]],a,s);i.forEach((function(n){var i="".concat(t.dfsCode.dfsEdgeList[o[r]].fromNode,"-").concat(n.label,"-").concat(c[n.to].label);d[i]||(d[i]={projected:[],fromNodeId:t.dfsCode.dfsEdgeList[o[r]].fromNode,edgeLabel:n.label,nodeLabel2:c[n.to].label}),d[i].projected.push({graphId:e.graphId,edge:n,preNode:e})}))};for(l=0;l<o.length;l++)p(l)}})),Object.keys(u).forEach((function(e){var n=u[e],r=n.toNodeId,o=n.edgeLabel;t.dfsCode.dfsEdgeList.push(new Pe(i,r,"-1",o,"-1")),t.subGraphMining(u[e].projected),t.dfsCode.dfsEdgeList.pop()})),Object.keys(d).forEach((function(e){var n=d[e],r=n.fromNodeId,o=n.edgeLabel,a=n.nodeLabel2;t.dfsCode.dfsEdgeList.push(new Pe(r,i+1,Me,o,a)),t.subGraphMining(d[e].projected),t.dfsCode.dfsEdgeList.pop()}))}},e.prototype.generate1EdgeFrequentSubGraphs=function(){var e=this.graphs,t=this.directed,n=this.minSupport,r=this.frequentSize1Subgraphs,o={},i={},a={},d={};return Object.keys(e).forEach((function(n){var r=e[n],u=r.nodeMap;r.nodes.forEach((function(e,r){var c=e.label,s="".concat(n,"-").concat(c);if(!a[s]){var l=o[c]||0;l++,o[c]=l}a[s]={graphKey:n,label:c},e.edges.forEach((function(e){var r=c,o=u[e.to].label;if(!t&&r>o){var a=o;o=r,r=a}var s=e.label,l="".concat(n,"-").concat(r,"-").concat(s,"-").concat(o),f="".concat(r,"-").concat(s,"-").concat(o);if(!i[f]){var h=i[f]||0;h++,i[f]=h}d[l]={graphId:n,nodeLabel1:r,edgeLabel:s,nodeLabel2:o}}))}))})),Object.keys(o).forEach((function(e){var t=o[e];if(!(t<n)){var i={nodes:[],edges:[]};i.nodes.push({id:"0",label:e}),r.push(i)}})),r},e.prototype.run=function(){var e=this;if(this.frequentSize1Subgraphs=this.generate1EdgeFrequentSubGraphs(),!(this.maxNodeNum<2)){var t=this.graphs,n=(this.directed,{});Object.keys(t).forEach((function(r){var o=t[r],i=o.nodeMap;o.nodes.forEach((function(t){var a=e.findForwardRootEdges(o,t);a.forEach((function(e){var o=i[e.to],a="".concat(t.label,"-").concat(e.label,"-").concat(o.label);n[a]||(n[a]={projected:[],nodeLabel1:t.label,edgeLabel:e.label,nodeLabel2:o.label});var d={graphId:r,edge:e,preNode:null};n[a].projected.push(d)}))}))})),Object.keys(n).forEach((function(t){var r=n[t],o=r.projected,i=r.nodeLabel1,a=r.edgeLabel,d=r.nodeLabel2;e.dfsCode.dfsEdgeList.push(new Pe(0,1,i,a,d)),e.subGraphMining(o),e.dfsCode.dfsEdgeList.pop()}))}},e}(),Te=function(e,t,n,r){var o={};return Object.keys(e).forEach((function(i,a){var d=e[i],u=new _e(a,!0,t),c={};d.nodes.forEach((function(e,t){u.addNode(t,e[n]),c[e.id]=t})),d.edges.forEach((function(e,t){var n=c[e.source],o=c[e.target];u.addEdge(-1,n,o,e[r])})),u&&u.getNodeNum()&&(o[u.id]=u)})),o},Re=function(e,t,n){var r=[];return e.forEach((function(e){var o={nodes:[],edges:[]};e.nodes.forEach((function(e){var n;o.nodes.push((n={id:"".concat(e.id)},n[t]=e.label,n))})),e.edges.forEach((function(e){var t;o.edges.push((t={source:"".concat(e.from),target:"".concat(e.to)},t[n]=e.label,t))})),r.push(o)})),r},Ge="cluster",qe=function(e){var t=e.graphs,n=e.directed,r=void 0!==n&&n,o=e.nodeLabelProp,i=void 0===o?Ge:o,a=e.edgeLabelProp,d=void 0===a?Ge:a,u=Te(t,r,i,d),c=e.minSupport,s=e.maxNodeNum,l=e.minNodeNum,f=e.verbose,h=e.top,v={graphs:u,minSupport:c,maxNodeNum:s,minNodeNum:l,top:h,verbose:f,directed:r},p=new Ce(v);p.run();var g=Re(p.frequentSubgraphs,i,d);return g},Fe=qe,Ue=function(e,t,n,r){void 0===n&&(n="cluster"),void 0===r&&(r=2);var o=[],i=e.nodes;return t.forEach((function(e,t){o.push(He(i,e,t,n,r))})),o},He=function(e,t,n,r,o){var i=[n],a=[],d={};return t.forEach((function(t,u){if(t<=o&&n!==u){i.push(u),a.push(e[u]);var c=e[u][r];d[c]?(d[c].count++,d[c].dists.push(t)):d[c]={count:1,dists:[t]}}})),Object.keys(d).forEach((function(e){d[e].dists=d[e].dists.sort((function(e,t){return e-t}))})),{nodeIdx:n,nodeId:e[n].id,nodeIdxs:i,neighbors:a,neighborNum:i.length-1,nodeLabelCountMap:d}},Be=function(e,t,n,r,o){var i=Math.ceil(n/t),a={},d=0;return r.forEach((function(e,r){var u=0,c=0,s=e.nodeIdxs,l=e.neighborNum-1;while(u<i){var f=s[1+Math.floor(Math.random()*l)],h=0;while(a["".concat(r,"-").concat(f)]||a["".concat(f,"-").concat(r)])if(f=Math.floor(Math.random()*t),h++,h>2*t)break;if(h<2*t&&(a["".concat(r,"-").concat(f)]={start:r,end:f,distance:o[r][f]},u++,d++,d>=n))return a;if(c++,c>2*t)break}if(u<i){var v=i-u;i=(i+v)/(t-r-1)}})),a},Ve=function(e,t,n,r){var o=n.nodes;return r||(r={}),Object.keys(e).forEach((function(i){var a,d;if(!r||!r[i]){r[i]={nodes:[],edges:[]};var u=e[i],c=null===(a=t[u.start])||void 0===a?void 0:a.nodeIdxs,s=null===(d=t[u.end])||void 0===d?void 0:d.nodeIdxs;if(c&&s){var l=new Set(s),f=c.filter((function(e){return l.has(e)}));if(f&&f.length){for(var h={},v=f.length,p=0;p<v;p++){var g=o[f[p]];r[i].nodes.push(g),h[g.id]=!0}n.edges.forEach((function(e){h[e.source]&&h[e.target]&&r[i].edges.push(e)}))}}}})),r},We=function(e,t,n,r){var o,i,a={};e.nodes.forEach((function(e){a[e.id]=e}));var d=0;return!(null===(o=null===t||void 0===t?void 0:t.edges)||void 0===o?void 0:o.length)||(null===(i=null===t||void 0===t?void 0:t.nodes)||void 0===i?void 0:i.length)<2?0:(e.edges.forEach((function(e){var o=a[e.source][n],i=a[e.target][n],u=null===t||void 0===t?void 0:t.nodes[0][n],c=null===t||void 0===t?void 0:t.nodes[1][n],s=null===t||void 0===t?void 0:t.edges[0][r];e[r]===s&&(o===u&&i===c||o===c&&i===u)&&d++})),d)},ze=function(e,t,n){for(var r=1/0,o=0,i=function(t){var n=e[t],i=Object.keys(n).sort((function(e,t){return n[e]-n[t]})),a=10,d=[];i.forEach((function(e,t){d[t%a]||(d[t%a]={graphs:[],totalCount:0,aveCount:0}),d[t%a].graphs.push(e),d[t%a].totalCount+=n[e]}));var u=0,c=[];d.forEach((function(e){var t=e.totalCount/e.graphs.length;e.aveCount=t,c.push(t);var r=0,o=e.length;e.graphs.forEach((function(t,o){var i=n[t];e.graphs.forEach((function(e,t){o!==t&&(r+=Math.abs(i-n[e]))}))})),r/=o*(o-1)/2,u+=r})),u/=d.length;var s=0;c.forEach((function(e,t){c.forEach((function(n,r){t!==r&&(s+=Math.abs(e-n))})),s/=c.length*(c.length-1)/2}));var l=s-u;r<l&&(r=l,o=t)},a=0;a<t;a++)i(a);return{structure:n[o],structureCountMap:e[o]}},Ke=function(e,t){var n={},r={};return e.forEach((function(e,o){n[e.id]={idx:o,node:e,degree:0,inDegree:0,outDegree:0};var i=e[t];r[i]||(r[i]=[]),r[i].push(e)})),{nodeMap:n,nodeLabelMap:r}},$e=function(e,t,n){var r={},o={};return e.forEach((function(e,i){r["".concat(v)]={idx:i,edge:e};var a=e[t];o[a]||(o[a]=[]),o[a].push(e);var d=n[e.source];d&&(d.degree++,d.outDegree++);var u=n[e.target];u&&(u.degree++,u.inDegree++)})),{edgeMap:r,edgeLabelMap:o}},Je=function(e,t,n){var r=t.length,o={};return t.forEach((function(t,i){for(var a=n?0:i+1,d=e[i].id,u=a;u<r;u++)if(i!==u){var c=e[u].id,s=t[u];o["".concat(d,"-").concat(c)]=s,n||(o["".concat(c,"-").concat(d)]=s)}})),o},Qe=function(e,t,n,r,o,i,a,d,u,c,s){var l,f="".concat(t.id,"-").concat(n.id);if(c&&c[f])return c[f];var h=s?s[f]:void 0;if(!h){var v=(l={},l[f]={start:r[t.id].idx,end:r[n.id].idx,distance:o},l);s=Ve(v,i,e,s),h=s[f]}return We(h,a,d,u)},Xe=function(e,t,n,r){var o,i,a,d=null===(o=e[t])||void 0===o?void 0:o.degree,u=null===(i=e[t])||void 0===i?void 0:i.inDegree,c=null===(a=e[t])||void 0===a?void 0:a.outDegree;return void 0===e[t]&&(d=1/0,u=1/0,c=1/0,r[t].forEach((function(e){var t=n[e.id].degree;d>t&&(d=t);var r=n[e.id].inDegree;u>r&&(u=r);var o=n[e.id].outDegree;c>o&&(c=o)})),e[t]={degree:d,inDegree:u,outDegree:c}),{minPatternNodeLabelDegree:d,minPatternNodeLabelInDegree:u,minPatternNodeLabelOutDegree:c}},Ye=function(e,t,n,r,o,i,a){var d;if(void 0===n&&(n=!1),void 0===i&&(i="cluster"),void 0===a&&(a="cluster"),e&&e.nodes){var u=e.nodes.length;if(u){var c=B(e,n),s=B(t,n),l=Je(e.nodes,c,n),f=Je(t.nodes,s,n),h=Ke(e.nodes,i),v=h.nodeMap,p=h.nodeLabelMap,g=Ke(t.nodes,i),b=g.nodeMap,y=g.nodeLabelMap;$e(e.edges,a,v);var m=$e(t.edges,a,b).edgeLabelMap,E=[];null===s||void 0===s||s.forEach((function(e){E=E.concat(e)})),o||(o=Math.max.apply(Math,Object(x["__spreadArray"])(Object(x["__spreadArray"])([],E,!1),[2],!1))),r||(r=o);var L=Ue(e,c,i,r),w=Ue(t,s,i,r),A=Math.min(100,u*(u-1)/2),O=Be(r,u,A,L,c),N=Ve(O,L,e),j=10,M=1,k=1,D=4,I={graphs:N,nodeLabelProp:i,edgeLabelProp:a,minSupport:M,minNodeNum:k,maxNodeNum:D,directed:n},_=Fe(I).slice(0,j),P=_.length,S=[];_.forEach((function(e,t){S[t]={},Object.keys(N).forEach((function(n){var r=N[n],o=We(r,e,i,a);S[t][n]=o}))}));var C=ze(S,P,_),T=C.structure,G=C.structureCountMap,q=t.nodes[0],F=[],U=null===(d=t.nodes[0])||void 0===d?void 0:d[i],H=-1/0;t.nodes.forEach((function(e){var t=e[i],n=p[t];(null===n||void 0===n?void 0:n.length)>H&&(H=n.length,F=n,U=t,q=e)}));var V={},W={},z={},K={},$={},J={};Object.keys(y).forEach((function(r,o){$[r]=[],n&&(J[r]=[]);var d=-1/0,u=y[r],c={};u.forEach((function(e){var t=f["".concat(q.id,"-").concat(e.id)];if(t&&$[r].push(t),d<t&&(d=t),c["".concat(q.id,"-").concat(e.id)]={start:0,end:b[e.id].idx,distance:t},n){var o=f["".concat(e.id,"-").concat(q.id)];o&&J[r].push(o)}})),$[r]=$[r].sort((function(e,t){return e-t})),n&&(J[r]=J[r].sort((function(e,t){return e-t}))),W=Ve(c,w,t,W);var s=[];if(Object.keys(c).forEach((function(e){if(z[e])s.push(z[e]);else{var t=W[e];z[e]=We(t,T,i,a),s.push(z[e])}})),s=s.sort((function(e,t){return t-e})),K["".concat(q.id,"-").concat(r)]=s,r!==U)for(var h=(null===F||void 0===F?void 0:F.length)||0,p=function(t){var n=F[t],o=L[v[n.id].idx],d=o.nodeLabelCountMap[r],u=y[r].length;if(!d||d.count<u)return F.splice(t,1),"continue";for(var c=!1,f=0;f<u;f++)if(d.dists[f]>$[r][f]){c=!0;break}if(c)return F.splice(t,1),"continue";var h={};o.neighbors.forEach((function(e){var t=l["".concat(n.id,"-").concat(e.id)];h["".concat(n.id,"-").concat(e.id)]={start:v[n.id].idx,end:v[e.id].idx,distance:t}})),N=Ve(h,L,e,N);var p=[];Object.keys(h).forEach((function(e){if(G[e])p.push(G[e]);else{var t=N[e];G[e]=We(t,T,i,a),p.push(G[e])}})),p=p.sort((function(e,t){return t-e}));var g=!1;for(f=0;f<u;f++)if(p[f]<s[f]){g=!0;break}return g?(F.splice(t,1),"continue"):void 0},g=h-1;g>=0;g--)p(g)}));var Q=[];null===F||void 0===F||F.forEach((function(r){for(var d=v[r.id].idx,u=He(e.nodes,c[d],d,i,o),s=u.neighbors,f=s.length,h=!1,p=f-1;p>=0;p--){if(s.length+1<t.nodes.length)return void(h=!0);var g=s[p],m=g[i];if(y[m]&&y[m].length)if($[m]&&$[m].length){var E="".concat(r.id,"-").concat(g.id),w=l[E],A=$[m].length-1,O=$[m][A];if(w>O)s.splice(p,1);else{if(n){var j="".concat(g.id,"-").concat(r.id),M=l[j];A=J[m].length-1;var k=J[m][A];if(M>k){s.splice(p,1);continue}}var D=G[E]?G[E]:Qe(e,r,g,v,w,L,T,i,a,G,N),I="".concat(q.id,"-").concat(m),_=K[I][K[I].length-1];if(D<_)s.splice(p,1);else{var P=Xe(V,m,b,y),x=P.minPatternNodeLabelDegree;P.minPatternNodeLabelInDegree,P.minPatternNodeLabelOutDegree;v[g.id].degree<x&&s.splice(p,1)}}}else s.splice(p,1);else s.splice(p,1)}h||Q.push({nodes:[r].concat(s)})}));var X=R(t,q.id,!1).length,Y={};n?(Object.keys(X).forEach((function(e){var t=b[e].node[i];Y[t]?Y[t].push(X[e]):Y[t]=[X[e]]})),Object.keys(Y).forEach((function(e){Y[e].sort((function(e,t){return e-t}))}))):Y=$;for(var Z=Q.length,ee=function(r){var o=Q[r],d=o.nodes[0],u={},c={};o.nodes.forEach((function(e,t){c[e.id]={idx:t,node:e,degree:0,inDegree:0,outDegree:0};var n=e[i];u[n]?u[n]++:u[n]=1}));var s=[],l={};e.edges.forEach((function(e){c[e.source]&&c[e.target]&&(s.push(e),l[e[a]]?l[e[a]]++:l[e[a]]=1,c[e.source].degree++,c[e.target].degree++,c[e.source].outDegree++,c[e.target].inDegree++)}));for(var f=Object.keys(m).length,h=!1,p=0;p<f;p++){var g=Object.keys(m)[p];if(!l[g]||l[g]<m[g].length){h=!0;break}}if(h)return Q.splice(r,1),"continue";var E=s.length;if(E<t.edges.length)return Q.splice(r,1),"break";var L=!1,w=function(e){var t=s[e],r=t[a],o=m[r];if(!o||!o.length)return l[r]--,o&&l[r]<o.length?(L=!0,"break"):(s.splice(e,1),c[t.source].degree--,c[t.target].degree--,c[t.source].outDegree--,c[t.target].inDegree--,"continue");var d=c[t.source].node[i],u=c[t.target].node[i],f=!1;return o.forEach((function(e){var t=b[e.source].node,r=b[e.target].node;t[i]===d&&r[i]===u&&(f=!0),n||t[i]!==u||r[i]!==d||(f=!0)})),f?void 0:(l[r]--,o&&l[r]<o.length?(L=!0,"break"):(s.splice(e,1),c[t.source].degree--,c[t.target].degree--,c[t.source].outDegree--,c[t.target].inDegree--,"continue"))};for(p=E-1;p>=0;p--){var A=w(p);if("break"===A)break}if(L)return Q.splice(r,1),"continue";o.edges=s;var O=R(o,o.nodes[0].id,!1).length;if(Object.keys(O).reverse().forEach((function(e){if(e!==o.nodes[0].id&&!L){if(O[e]===1/0){var t=c[e].node[i];if(u[t]--,u[t]<y[t].length)return void(L=!0);var n=o.nodes.indexOf(c[e].node);return o.nodes.splice(n,1),void(c[e]=void 0)}var r=v[e].node[i];if(!Y[r]||!Y[r].length||O[e]>Y[r][Y[r].length-1]){t=c[e].node[i];if(u[t]--,u[t]<y[t].length)return void(L=!0);n=o.nodes.indexOf(c[e].node);o.nodes.splice(n,1),c[e]=void 0}}})),L)return Q.splice(r,1),"continue";var N=!0,j=0;while(N&&!L){N=!1;var M=n?c[d.id].degree<b[q.id].degree||c[d.id].inDegree<b[q.id].inDegree||c[d.id].outDegree<b[q.id].outDegree:c[d.id].degree<b[q.id].degree;if(M){L=!0;break}if(u[d[i]]<y[d[i]].length){L=!0;break}for(var k=o.nodes.length,D=k-1;D>=0;D--){var I=o.nodes[D],_=c[I.id].degree,P=c[I.id].inDegree,x=c[I.id].outDegree,S=I[i],C=Xe(V,S,b,y),T=C.minPatternNodeLabelDegree,G=C.minPatternNodeLabelInDegree,F=C.minPatternNodeLabelOutDegree,U=n?_<T||P<G||x<F:_<T;if(U){if(u[I[i]]--,u[I[i]]<y[I[i]].length){L=!0;break}o.nodes.splice(D,1),c[I.id]=void 0,N=!0}}if(L||!N&&0!==j)break;E=s.length;for(var H=E-1;H>=0;H--){var B=s[H];if(!c[B.source]||!c[B.target]){s.splice(H,1);var W=B[a];if(l[W]--,c[B.source]&&(c[B.source].degree--,c[B.source].outDegree--),c[B.target]&&(c[B.target].degree--,c[B.target].inDegree--),m[W]&&l[W]<m[W].length){L=!0;break}N=!0}}j++}return L||L||o.nodes.length<t.nodes.length||s.length<t.edges.length?(Q.splice(r,1),"continue"):void 0},te=Z-1;te>=0;te--){var ne=ee(te);if("break"===ne)break}var re=Q.length,oe=function(e){var t=Q[e],n={};t.edges.forEach((function(e){var t="".concat(e.source,"-").concat(e.target,"-").concat(e.label);n[t]?n[t]++:n[t]=1}));for(var r=function(e){var t=Q[e],r={};t.edges.forEach((function(e){var t="".concat(e.source,"-").concat(e.target,"-").concat(e.label);r[t]?r[t]++:r[t]=1}));var o=!0;Object.keys(r).length!==Object.keys(n).length?o=!1:Object.keys(n).forEach((function(e){r[e]!==n[e]&&(o=!1)})),o&&Q.splice(e,1)},o=re-1;o>e;o--)r(o);re=Q.length};for(te=0;te<=re-1;te++)oe(te);return Q}}},Ze=Ye,et=function(){function e(e){void 0===e&&(e=10),this.linkedList=new u,this.maxStep=e}return Object.defineProperty(e.prototype,"length",{get:function(){return this.linkedList.toArray().length},enumerable:!1,configurable:!0}),e.prototype.isEmpty=function(){return!this.linkedList.head},e.prototype.isMaxStack=function(){return this.toArray().length>=this.maxStep},e.prototype.peek=function(){return this.isEmpty()?null:this.linkedList.head.value},e.prototype.push=function(e){this.linkedList.prepend(e),this.length>this.maxStep&&this.linkedList.deleteTail()},e.prototype.pop=function(){var e=this.linkedList.deleteHead();return e?e.value:null},e.prototype.toArray=function(){return this.linkedList.toArray().map((function(e){return e.value}))},e.prototype.clear=function(){while(!this.isEmpty())this.pop()},e}(),tt=et,nt=P;t["default"]={getAdjMatrix:o,breadthFirstSearch:b,connectedComponent:E,getDegree:w,getInDegree:A,getOutDegree:O,detectCycle:P,detectDirectedCycle:nt,detectAllCycles:_,detectAllDirectedCycle:I,detectAllUndirectedCycle:D,depthFirstSearch:M,dijkstra:R,findAllPath:U,findShortestPath:F,floydWarshall:B,labelPropagation:W,louvain:te,iLouvain:re,kCore:ie,kMeans:ue,cosineSimilarity:se,nodesCosineSimilarity:fe,minimumSpanningTree:Le,pageRank:Ae,getNeighbors:l,Stack:tt,GADDI:Ze}},"98e2":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.findShortestPath=t.findAllPath=void 0;var r=i(n("9b96")),o=n("dcfe");function i(e){return e&&e.__esModule?e:{default:e}}var a=function(e,t,n,o,i){var a=(0,r.default)(e,t,o,i),d=a.length,u=a.path,c=a.allPath;return{length:d[n],path:u[n],allPath:c[n]}};t.findShortestPath=a;var d=function(e,t,n,r){var i;if(t===n)return[[t]];var a=e.edges,d=void 0===a?[]:a,u=[t],c=(i={},i[t]=!0,i),s=[],l=[],f=r?(0,o.getNeighbors)(t,d,"target"):(0,o.getNeighbors)(t,d);s.push(f);while(u.length>0&&s.length>0){var h=s[s.length-1];if(h.length){var v=h.shift();if(v&&(u.push(v),c[v]=!0,f=r?(0,o.getNeighbors)(v,d,"target"):(0,o.getNeighbors)(v,d),s.push(f.filter((function(e){return!c[e]})))),u[u.length-1]===n){var p=u.map((function(e){return e}));l.push(p);g=u.pop();c[g]=!1,s.pop()}}else{var g=u.pop();c[g]=!1,s.pop()}}return l};t.findAllPath=d},"9b96":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n("cb87"),o=n("8937"),i=n("dcfe"),a=function(e,t,n){for(var r,o=1/0,i=0;i<t.length;i++){var a=t[i].id;!n[a]&&e[a]<=o&&(o=e[a],r=t[i])}return r},d=function(e,t,n,r){var o=e.nodes,d=void 0===o?[]:o,u=e.edges,s=void 0===u?[]:u,l=[],f={},h={},v={};d.forEach((function(e,n){var r=e.id;l.push(r),h[r]=1/0,r===t&&(h[r]=0)}));for(var p=d.length,g=function(e){var t=a(h,d,f),o=t.id;if(f[o]=!0,h[o]===1/0)return"continue";var u=[];u=n?(0,i.getOutEdgesNodeId)(o,s):(0,i.getEdgesByNodeId)(o,s),u.forEach((function(e){var n=e.target,i=e.source,a=n===o?i:n,d=r&&e[r]?e[r]:1;h[a]>h[t.id]+d?(h[a]=h[t.id]+d,v[a]=[t.id]):h[a]===h[t.id]+d&&v[a].push(t.id)}))},b=0;b<p;b++)g(b);v[t]=[t];var y={};for(var m in h)h[m]!==1/0&&c(t,m,v,y);var E={};for(var m in y)E[m]=y[m][0];return{length:h,path:E,allPath:y}},u=d;function c(e,t,n,i){if(e===t)return[e];if(i[t])return i[t];for(var a=[],d=0,u=n[t];d<u.length;d++){var s=u[d],l=c(e,s,n,i);if(!l)return;for(var f=0,h=l;f<h.length;f++){var v=h[f];(0,o.isArray)(v)?a.push((0,r.__spreadArray)((0,r.__spreadArray)([],v,!0),[t],!1)):a.push([v,t])}}return i[t]=a,i[t]}t.default=u},"9c2f":function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=function(e,t){return e-t},o=function(){function e(e){void 0===e&&(e=r),this.compareFn=e,this.list=[]}return e.prototype.getLeft=function(e){return 2*e+1},e.prototype.getRight=function(e){return 2*e+2},e.prototype.getParent=function(e){return 0===e?null:Math.floor((e-1)/2)},e.prototype.isEmpty=function(){return this.list.length<=0},e.prototype.top=function(){return this.isEmpty()?void 0:this.list[0]},e.prototype.delMin=function(){var e=this.top(),t=this.list.pop();return this.list.length>0&&(this.list[0]=t,this.moveDown(0)),e},e.prototype.insert=function(e){if(null!==e){this.list.push(e);var t=this.list.length-1;return this.moveUp(t),!0}return!1},e.prototype.moveUp=function(e){var t=this.getParent(e);while(e&&e>0&&this.compareFn(this.list[t],this.list[e])>0){var n=this.list[t];this.list[t]=this.list[e],this.list[e]=n,e=t,t=this.getParent(e)}},e.prototype.moveDown=function(e){var t,n=e,r=this.getLeft(e),o=this.getRight(e),i=this.list.length;null!==r&&r<i&&this.compareFn(this.list[n],this.list[r])>0?n=r:null!==o&&o<i&&this.compareFn(this.list[n],this.list[o])>0&&(n=o),e!==n&&(t=[this.list[n],this.list[e]],this.list[e]=t[0],this.list[n]=t[1],this.moveDown(n))},e}(),i=o;t.default=i},"9fba":function(e,t,n){"use strict";function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=d(n("264e")),i=n("9023");function a(e){if("function"!==typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(a=function(e){return e?n:t})(e)}function d(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==r(e)&&"function"!==typeof e)return{default:e};var n=a(t);if(n&&n.has(e))return n.get(e);var o={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var d in e)if("default"!==d&&Object.prototype.hasOwnProperty.call(e,d)){var u=i?Object.getOwnPropertyDescriptor(e,d):null;u&&(u.get||u.set)?Object.defineProperty(o,d,u):o[d]=e[d]}return o.default=e,n&&n.set(e,o),o}var u="undefined"!==typeof self?self:{};u.onmessage=function(e){var t=e.data,n=t._algorithmType,r=t.data;if(n)if("function"!==typeof o[n])u.postMessage({_algorithmType:i.MESSAGE.FAILURE});else{var a=o[n].apply(o,r);u.postMessage({_algorithmType:i.MESSAGE.SUCCESS,data:a})}};var c=null;t.default=c},ada5:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n("8937"),o=n("9154"),i=function(){function e(e,t,n,r,i){this.fromNode=e,this.toNode=t,this.nodeEdgeNodeLabel={nodeLabel1:n||o.VACANT_NODE_LABEL,edgeLabel:r||o.VACANT_EDGE_LABEL,nodeLabel2:i||o.VACANT_NODE_LABEL}}return e.prototype.equalTo=function(e){return this.fromNode===e.formNode&&this.toNode===e.toNode&&this.nodeEdgeNodeLabel===e.nodeEdgeNodeLabel},e.prototype.notEqualTo=function(e){return!this.equalTo(e)},e}(),a=function(){function e(){this.rmpath=[],this.dfsEdgeList=[]}return e.prototype.equalTo=function(e){var t=this.dfsEdgeList.length,n=e.length;if(t!==n)return!1;for(var r=0;r<t;r++)if(this.dfsEdgeList[r]!==e[r])return!1;return!0},e.prototype.notEqualTo=function(e){return!this.equalTo(e)},e.prototype.pushBack=function(e,t,n,r,o){return this.dfsEdgeList.push(new i(e,t,n,r,o)),this.dfsEdgeList},e.prototype.toGraph=function(e,t){void 0===e&&(e=o.VACANT_GRAPH_ID),void 0===t&&(t=!1);var n=new o.Graph(e,!0,t);return this.dfsEdgeList.forEach((function(e){var t=e.fromNode,r=e.toNode,i=e.nodeEdgeNodeLabel,a=i.nodeLabel1,d=i.edgeLabel,u=i.nodeLabel2;a!==o.VACANT_NODE_LABEL&&n.addNode(t,a),u!==o.VACANT_NODE_LABEL&&n.addNode(r,u),a!==o.VACANT_NODE_LABEL&&u!==a&&n.addEdge(void 0,t,r,d)})),n},e.prototype.buildRmpath=function(){this.rmpath=[];for(var e=void 0,t=this.dfsEdgeList.length,n=t-1;n>=0;n--){var r=this.dfsEdgeList[n],o=r.fromNode,i=r.toNode;o<i&&(void 0===e||i===e)&&(this.rmpath.push(n),e=o)}return this.rmpath},e.prototype.getNodeNum=function(){var e={};return this.dfsEdgeList.forEach((function(t){e[t.fromNode]||(e[t.fromNode]=!0),e[t.toNode]||(e[t.toNode]=!0)})),Object.keys(e).length},e}(),d=function(){function e(e){if(this.his={},this.nodesUsed={},this.edgesUsed={},this.edges=[],e){while(e){var t=e.edge;this.edges.push(t),this.nodesUsed[t.from]=1,this.nodesUsed[t.to]=1,this.edgesUsed[t.id]=1,e=e.preNode}this.edges=this.edges.reverse()}}return e.prototype.hasNode=function(e){return 1===this.nodesUsed[e.id]},e.prototype.hasEdge=function(e){return 1===this.edgesUsed[e.id]},e}(),u=function(){function e(e){var t=e.graphs,n=e.minSupport,r=void 0===n?2:n,o=e.minNodeNum,i=void 0===o?1:o,d=e.maxNodeNum,u=void 0===d?4:d,c=e.top,s=void 0===c?10:c,l=e.directed,f=void 0!==l&&l,h=e.verbose,v=void 0!==h&&h;this.graphs=t,this.dfsCode=new a,this.support=0,this.frequentSize1Subgraphs=[],this.frequentSubgraphs=[],this.minSupport=r,this.top=s,this.directed=f,this.counter=0,this.maxNodeNum=u,this.minNodeNum=i,this.verbose=v,this.maxNodeNum<this.minNodeNum&&(this.maxNodeNum=this.minNodeNum),this.reportDF=[]}return e.prototype.findForwardRootEdges=function(e,t){var n=this,r=[],o=e.nodeMap;return t.edges.forEach((function(e){(n.directed||t.label<=o[e.to].label)&&r.push(e)})),r},e.prototype.findBackwardEdge=function(e,t,n,r){if(!this.directed&&t===n)return null;for(var o=e.nodeMap,i=o[n.to],a=i.edges,d=a.length,u=0;u<d;u++){var c=a[u];if(!r.hasEdge(c)&&c.to===t.from)if(this.directed){if(o[t.from].label<o[n.to].label||o[t.from].label===o[n.to].label&&t.label<=c.label)return c}else if(t.label<c.label||t.label===c.label&&o[t.to].label<=o[n.to].label)return c}return null},e.prototype.findForwardPureEdges=function(e,t,n,r){for(var o=[],i=t.to,a=e.nodeMap[i].edges,d=a.length,u=0;u<d;u++){var c=a[u],s=e.nodeMap[c.to];n<=s.label&&!r.hasNode(s)&&o.push(c)}return o},e.prototype.findForwardRmpathEdges=function(e,t,n,r){for(var o=[],i=e.nodeMap,a=i[t.to].label,d=i[t.from],u=d.edges,c=u.length,s=0;s<c;s++){var l=u[s],f=i[l.to].label;t.to===l.to||n>f||r.hasNode(i[l.to])||(t.label<l.label||t.label===l.label&&a<=f)&&o.push(l)}return o},e.prototype.getSupport=function(e){var t={};return e.forEach((function(e){t[e.graphId]||(t[e.graphId]=!0)})),Object.keys(t).length},e.prototype.findMinLabel=function(e){var t=void 0;return Object.keys(e).forEach((function(n){var r=e[n],o=r.nodeLabel1,i=r.edgeLabel,a=r.nodeLabel2;t?(o<t.nodeLabel1||o===t.nodeLabel1&&i<t.edgeLabel||o===t.nodeLabel1&&i===t.edgeLabel&&a<t.nodeLabel2)&&(t={nodeLabel1:o,edgeLabel:i,nodeLabel2:a}):t={nodeLabel1:o,edgeLabel:i,nodeLabel2:a}})),t},e.prototype.isMin=function(){var e=this,t=this.dfsCode;if(this.verbose&&console.log("isMin checking",t),1===t.dfsEdgeList.length)return!0;var n=this.directed,r=t.toGraph(o.VACANT_GRAPH_ID,n),u=r.nodeMap,c=new a,s={};r.nodes.forEach((function(t){var n=e.findForwardRootEdges(r,t);n.forEach((function(e){var n=u[e.to],o="".concat(t.label,"-").concat(e.label,"-").concat(n.label);s[o]||(s[o]={projected:[],nodeLabel1:t.label,edgeLabel:e.label,nodeLabel2:n.label});var i={graphId:r.id,edge:e,preNode:null};s[o].projected.push(i)}))}));var l=this.findMinLabel(s);if(l){c.dfsEdgeList.push(new i(0,1,l.nodeLabel1,l.edgeLabel,l.nodeLabel2));var f=function a(s){for(var l=c.buildRmpath(),f=c.dfsEdgeList[0].nodeEdgeNodeLabel.nodeLabel1,h=c.dfsEdgeList[l[0]].toNode,v={},p=!1,g=0,b=n?-1:0,y=function(t){if(p)return"break";s.forEach((function(n){var o=new d(n),i=e.findBackwardEdge(r,o.edges[l[t]],o.edges[l[0]],o);i&&(v[i.label]||(v[i.label]={projected:[],edgeLabel:i.label}),v[i.label].projected.push({graphId:r.id,edge:v,preNode:n}),g=c.dfsEdgeList[l[t]].fromNode,p=!0)}))},m=l.length-1;m>b;m--){var E=y(m);if("break"===E)break}if(p){var L=e.findMinLabel(v);c.dfsEdgeList.push(new i(h,g,o.VACANT_NODE_LABEL,L.edgeLabel,o.VACANT_NODE_LABEL));var w=c.dfsEdgeList.length-1;return e.dfsCode.dfsEdgeList[w]===c.dfsEdgeList[w]&&a(v[L.edgeLabel].projected)}var A={};p=!1;var O=0;s.forEach((function(t){var n=new d(t),o=e.findForwardPureEdges(r,n.edges[l[0]],f,n);o.length>0&&(p=!0,O=h,o.forEach((function(e){var n="".concat(e.label,"-").concat(u[e.to].label);A[n]||(A[n]={projected:[],edgeLabel:e.label,nodeLabel2:u[e.to].label}),A[n].projected.push({graphId:r.id,edge:e,preNode:t})})))}));var N=l.length,j=function(t){if(p)return"break";var n=l[t];s.forEach((function(t){var o=new d(t),i=e.findForwardRmpathEdges(r,o.edges[n],f,o);i.length>0&&(p=!0,O=c.dfsEdgeList[n].fromNode,i.forEach((function(e){var n="".concat(e.label,"-").concat(u[e.to].label);A[n]||(A[n]={projected:[],edgeLabel:e.label,nodeLabel2:u[e.to].label}),A[n].projected.push({graphId:r.id,edge:e,preNode:t})})))}))};for(m=0;m<N;m++){var M=j(m);if("break"===M)break}if(!p)return!0;var k=e.findMinLabel(A);c.dfsEdgeList.push(new i(O,h+1,o.VACANT_NODE_LABEL,k.edgeLabel,k.nodeLabel2));var D=c.dfsEdgeList.length-1;return t.dfsEdgeList[D]===c.dfsEdgeList[D]&&a(A["".concat(k.edgeLabel,"-").concat(k.nodeLabel2)].projected)},h="".concat(l.nodeLabel1,"-").concat(l.edgeLabel,"-").concat(l.nodeLabel2);return f(s[h].projected)}},e.prototype.report=function(){if(!(this.dfsCode.getNodeNum()<this.minNodeNum)){this.counter++;var e=this.dfsCode.toGraph(this.counter,this.directed);this.frequentSubgraphs.push((0,r.clone)(e))}},e.prototype.subGraphMining=function(e){var t=this,n=this.getSupport(e);if(!(n<this.minSupport)&&this.isMin()){this.report();var r=this.dfsCode.getNodeNum(),a=this.dfsCode.buildRmpath(),u=this.dfsCode.dfsEdgeList[a[0]].toNode,c=this.dfsCode.dfsEdgeList[0].nodeEdgeNodeLabel.nodeLabel1,s={},l={};e.forEach((function(e){for(var n=t.graphs[e.graphId],o=n.nodeMap,i=new d(e),f=a.length-1;f>=0;f--){var h=t.findBackwardEdge(n,i.edges[a[f]],i.edges[a[0]],i);if(h){var v="".concat(t.dfsCode.dfsEdgeList[a[f]].fromNode,"-").concat(h.label);l[v]||(l[v]={projected:[],toNodeId:t.dfsCode.dfsEdgeList[a[f]].fromNode,edgeLabel:h.label}),l[v].projected.push({graphId:e.graphId,edge:h,preNode:e})}}if(!(r>=t.maxNodeNum)){var p=t.findForwardPureEdges(n,i.edges[a[0]],c,i);p.forEach((function(t){var n="".concat(u,"-").concat(t.label,"-").concat(o[t.to].label);s[n]||(s[n]={projected:[],fromNodeId:u,edgeLabel:t.label,nodeLabel2:o[t.to].label}),s[n].projected.push({graphId:e.graphId,edge:t,preNode:e})}));var g=function(r){var d=t.findForwardRmpathEdges(n,i.edges[a[r]],c,i);d.forEach((function(n){var i="".concat(t.dfsCode.dfsEdgeList[a[r]].fromNode,"-").concat(n.label,"-").concat(o[n.to].label);s[i]||(s[i]={projected:[],fromNodeId:t.dfsCode.dfsEdgeList[a[r]].fromNode,edgeLabel:n.label,nodeLabel2:o[n.to].label}),s[i].projected.push({graphId:e.graphId,edge:n,preNode:e})}))};for(f=0;f<a.length;f++)g(f)}})),Object.keys(l).forEach((function(e){var n=l[e],r=n.toNodeId,o=n.edgeLabel;t.dfsCode.dfsEdgeList.push(new i(u,r,"-1",o,"-1")),t.subGraphMining(l[e].projected),t.dfsCode.dfsEdgeList.pop()})),Object.keys(s).forEach((function(e){var n=s[e],r=n.fromNodeId,a=n.edgeLabel,d=n.nodeLabel2;t.dfsCode.dfsEdgeList.push(new i(r,u+1,o.VACANT_NODE_LABEL,a,d)),t.subGraphMining(s[e].projected),t.dfsCode.dfsEdgeList.pop()}))}},e.prototype.generate1EdgeFrequentSubGraphs=function(){var e=this.graphs,t=this.directed,n=this.minSupport,r=this.frequentSize1Subgraphs,o={},i={},a={},d={};return Object.keys(e).forEach((function(n){var r=e[n],u=r.nodeMap;r.nodes.forEach((function(e,r){var c=e.label,s="".concat(n,"-").concat(c);if(!a[s]){var l=o[c]||0;l++,o[c]=l}a[s]={graphKey:n,label:c},e.edges.forEach((function(e){var r=c,o=u[e.to].label;if(!t&&r>o){var a=o;o=r,r=a}var s=e.label,l="".concat(n,"-").concat(r,"-").concat(s,"-").concat(o),f="".concat(r,"-").concat(s,"-").concat(o);if(!i[f]){var h=i[f]||0;h++,i[f]=h}d[l]={graphId:n,nodeLabel1:r,edgeLabel:s,nodeLabel2:o}}))}))})),Object.keys(o).forEach((function(e){var t=o[e];if(!(t<n)){var i={nodes:[],edges:[]};i.nodes.push({id:"0",label:e}),r.push(i)}})),r},e.prototype.run=function(){var e=this;if(this.frequentSize1Subgraphs=this.generate1EdgeFrequentSubGraphs(),!(this.maxNodeNum<2)){var t=this.graphs,n=(this.directed,{});Object.keys(t).forEach((function(r){var o=t[r],i=o.nodeMap;o.nodes.forEach((function(t){var a=e.findForwardRootEdges(o,t);a.forEach((function(e){var o=i[e.to],a="".concat(t.label,"-").concat(e.label,"-").concat(o.label);n[a]||(n[a]={projected:[],nodeLabel1:t.label,edgeLabel:e.label,nodeLabel2:o.label});var d={graphId:r,edge:e,preNode:null};n[a].projected.push(d)}))}))})),Object.keys(n).forEach((function(t){var r=n[t],o=r.projected,a=r.nodeLabel1,d=r.edgeLabel,u=r.nodeLabel2;e.dfsCode.dfsEdgeList.push(new i(0,1,a,d,u)),e.subGraphMining(o),e.dfsCode.dfsEdgeList.pop()}))}},e}(),c=function(e,t,n,r){var i={};return Object.keys(e).forEach((function(a,d){var u=e[a],c=new o.Graph(d,!0,t),s={};u.nodes.forEach((function(e,t){c.addNode(t,e[n]),s[e.id]=t})),u.edges.forEach((function(e,t){var n=s[e.source],o=s[e.target];c.addEdge(-1,n,o,e[r])})),c&&c.getNodeNum()&&(i[c.id]=c)})),i},s=function(e,t,n){var r=[];return e.forEach((function(e){var o={nodes:[],edges:[]};e.nodes.forEach((function(e){var n;o.nodes.push((n={id:"".concat(e.id)},n[t]=e.label,n))})),e.edges.forEach((function(e){var t;o.edges.push((t={source:"".concat(e.from),target:"".concat(e.to)},t[n]=e.label,t))})),r.push(o)})),r},l="cluster",f=function(e){var t=e.graphs,n=e.directed,r=void 0!==n&&n,o=e.nodeLabelProp,i=void 0===o?l:o,a=e.edgeLabelProp,d=void 0===a?l:a,f=c(t,r,i,d),h=e.minSupport,v=e.maxNodeNum,p=e.minNodeNum,g=e.verbose,b=e.top,y={graphs:f,minSupport:h,maxNodeNum:v,minNodeNum:p,top:b,verbose:g,directed:r},m=new u(y);m.run();var E=s(m.frequentSubgraphs,i,d);return E},h=f;t.default=h},b14b:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n("8937"),o=u(n("16c7")),i=u(n("0c62")),a=n("27f4"),d=n("7162");function u(e){return e&&e.__esModule?e:{default:e}}var c=function(e,t,n,r){for(var o=t.length,i=2*r,a=0,d=0;d<o;d++)for(var u=e[d].clusterId,c=0;c<o;c++){var s=e[c].clusterId;if(u===s){var l=t[d][c]||0,f=n[d]||0,h=n[c]||0;a+=l-f*h/i}}return a*=1/i,a},s=function(e,t){void 0===e&&(e=[]);for(var n=e.length,r=new i.default([]),o=0;o<n;o++)r=r.add(new i.default(t[o]));var a=r.avg(n);a.normalize();var d=0;for(o=0;o<n;o++){var u=new i.default(t[o]),c=u.squareEuclideanDistance(a);d+=c}var s=[];e.forEach((function(){s.push([])}));for(o=0;o<n;o++){u=new i.default(t[o]);e[o]["clusterInertial"]=0;for(var l=0;l<n;l++)if(o!==l){var f=new i.default(t[l]);s[o][l]=u.squareEuclideanDistance(f),e[o]["clusterInertial"]+=s[o][l]}else s[o][l]=0}var h=0,v=2*n*d;for(o=0;o<n;o++){var p=e[o].clusterId;for(l=0;l<n;l++){var g=e[l].clusterId;if(o!==l&&p===g){var b=e[o].clusterInertial*e[l].clusterInertial/Math.pow(v,2)-s[o][l]/v;h+=b}}}return Number(h.toFixed(4))},l=function(e,t,n,i,u,l,f,h,v){void 0===t&&(t=!1),void 0===n&&(n="weight"),void 0===i&&(i=1e-4),void 0===u&&(u=!1),void 0===l&&(l=void 0),void 0===f&&(f=[]),void 0===h&&(h=["id"]),void 0===v&&(v=1);var p=e.nodes,g=void 0===p?[]:p,b=e.edges,y=void 0===b?[]:b,m=[];if(u){g.forEach((function(e,t){e.properties=e.properties||{},e.originIndex=t}));var E=[];g.every((function(e){return e.hasOwnProperty("nodeType")}))&&(E=Array.from(new Set(g.map((function(e){return e.nodeType})))),g.forEach((function(e){e.properties.nodeType=E.findIndex((function(t){return t===e.nodeType}))})));var L=(0,a.getAllProperties)(g,l);m=(0,d.oneHot)(L,f,h)}var w=1,A={},O={};g.forEach((function(e,t){var n=String(w++);e.clusterId=n,A[n]={id:n,nodes:[e]},O[e.id]={node:e,idx:t}}));var N=(0,o.default)(e,t),j=[],M={},k=0;N.forEach((function(e,t){var n=0,r=g[t].id;M[r]={},e.forEach((function(e,t){if(e){n+=e;var o=g[t].id;M[r][o]=e,k+=e}})),j.push(n)})),k/=2;var D=1/0,I=1/0,_=0,P=[],x={};while(1){D=u&&g.every((function(e){return e.hasOwnProperty("properties")}))?c(g,N,j,k)+s(g,m)*v:c(g,N,j,k),0===_&&(I=D,P=g,x=A);var S=D>0&&D>I&&D-I<i;if(D>I&&(P=g.map((function(e){return{node:e,clusterId:e.clusterId}})),x=(0,r.clone)(A),I=D),S||_>100)break;_++,Object.keys(A).forEach((function(e){var t=0;y.forEach((function(r){var o=r.source,i=r.target,a=O[o].node.clusterId,d=O[i].node.clusterId;(a===e&&d!==e||d===e&&a!==e)&&(t+=r[n]||1)})),A[e].sumTot=t})),g.forEach((function(e,t){var r,o=A[e.clusterId],i=0,a=j[t]/(2*k),d=0,c=o.nodes;c.forEach((function(e){var n=O[e.id].idx;d+=N[t][n]||0}));var l=d-o.sumTot*a,f=c.filter((function(t){return t.id!==e.id})),h=[];f.forEach((function(e,t){h[t]=m[e.originIndex]}));var p=s(f,m)*v,g=M[e.id];if(Object.keys(g).forEach((function(n){var o=O[n].node,d=o.clusterId;if(d!==e.clusterId){var c=A[d],f=c.nodes;if(f&&f.length){var h=0;f.forEach((function(e){var n=O[e.id].idx;h+=N[t][n]||0}));var g=h-c.sumTot*a,b=f.concat([e]),y=[];b.forEach((function(e,t){y[t]=m[e.originIndex]}));var E=s(b,m)*v,L=g-l;u&&(L=g+E-(l+p)),L>i&&(i=L,r=c)}}})),i>0){r.nodes.push(e);var b=e.clusterId;e.clusterId=r.id;var E=o.nodes.indexOf(e);o.nodes.splice(E,1);var L=0,w=0;y.forEach((function(e){var t=e.source,o=e.target,i=O[t].node.clusterId,a=O[o].node.clusterId;(i===r.id&&a!==r.id||a===r.id&&i!==r.id)&&(L+=e[n]||1),(i===b&&a!==b||a===b&&i!==b)&&(w+=e[n]||1)})),r.sumTot=L,o.sumTot=w}}))}var C={},T=0;Object.keys(x).forEach((function(e){var t=x[e];if(t.nodes&&t.nodes.length){var n=String(T+1);n!==e&&(t.id=n,t.nodes=t.nodes.map((function(e){return{id:e.id,clusterId:n}})),x[n]=t,C[e]=n,delete x[e],T++)}else delete x[e]})),P.forEach((function(e){var t=e.node,n=e.clusterId;t&&(t.clusterId=n,t.clusterId&&C[t.clusterId]&&(t.clusterId=C[t.clusterId]))}));var R=[],G={};y.forEach((function(e){var t=e.source,r=e.target,o=e[n]||1,i=O[t].node.clusterId,a=O[r].node.clusterId;if(i&&a){var d="".concat(i,"---").concat(a);if(G[d])G[d].weight+=o,G[d].count++;else{var u={source:i,target:a,weight:o,count:1};G[d]=u,R.push(u)}}}));var q=[];return Object.keys(x).forEach((function(e){q.push(x[e])})),{clusters:q,clusterEdges:R}},f=l;t.default=f},c0d2:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n("9023"),o=i(n("9fba"));function i(e){return e&&e.__esModule?e:{default:e}}var a=function(e){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return new Promise((function(n,i){var a=new o.default;a.postMessage({_algorithmType:e,data:t}),a.onmessage=function(e){var t=e.data,o=t.data,d=t._algorithmType;r.MESSAGE.SUCCESS===d?n(o):i(),a.terminate()}}))}},d=a;t.default=d},cb87:function(e,t,n){"use strict";n.r(t),n.d(t,"__extends",(function(){return o})),n.d(t,"__assign",(function(){return i})),n.d(t,"__rest",(function(){return a})),n.d(t,"__decorate",(function(){return d})),n.d(t,"__param",(function(){return u})),n.d(t,"__metadata",(function(){return c})),n.d(t,"__awaiter",(function(){return s})),n.d(t,"__generator",(function(){return l})),n.d(t,"__createBinding",(function(){return f})),n.d(t,"__exportStar",(function(){return h})),n.d(t,"__values",(function(){return v})),n.d(t,"__read",(function(){return p})),n.d(t,"__spread",(function(){return g})),n.d(t,"__spreadArrays",(function(){return b})),n.d(t,"__spreadArray",(function(){return y})),n.d(t,"__await",(function(){return m})),n.d(t,"__asyncGenerator",(function(){return E})),n.d(t,"__asyncDelegator",(function(){return L})),n.d(t,"__asyncValues",(function(){return w})),n.d(t,"__makeTemplateObject",(function(){return A})),n.d(t,"__importStar",(function(){return N})),n.d(t,"__importDefault",(function(){return j})),n.d(t,"__classPrivateFieldGet",(function(){return M})),n.d(t,"__classPrivateFieldSet",(function(){return k})),n.d(t,"__classPrivateFieldIn",(function(){return D}));var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},r(e,t)};function o(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}var i=function(){return i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n],t)Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},i.apply(this,arguments)};function a(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}function d(e,t,n,r){var o,i=arguments.length,a=i<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)a=Reflect.decorate(e,t,n,r);else for(var d=e.length-1;d>=0;d--)(o=e[d])&&(a=(i<3?o(a):i>3?o(t,n,a):o(t,n))||a);return i>3&&a&&Object.defineProperty(t,n,a),a}function u(e,t){return function(n,r){t(n,r,e)}}function c(e,t){if("object"===typeof Reflect&&"function"===typeof Reflect.metadata)return Reflect.metadata(e,t)}function s(e,t,n,r){function o(e){return e instanceof n?e:new n((function(t){t(e)}))}return new(n||(n=Promise))((function(n,i){function a(e){try{u(r.next(e))}catch(t){i(t)}}function d(e){try{u(r["throw"](e))}catch(t){i(t)}}function u(e){e.done?n(e.value):o(e.value).then(a,d)}u((r=r.apply(e,t||[])).next())}))}function l(e,t){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:d(0),throw:d(1),return:d(2)},"function"===typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function d(e){return function(t){return u([e,t])}}function u(i){if(n)throw new TypeError("Generator is already executing.");while(a)try{if(n=1,r&&(o=2&i[0]?r["return"]:i[0]?r["throw"]||((o=r["return"])&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,r=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(o=a.trys,!(o=o.length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(d){i=[6,d],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}}var f=Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]};function h(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||f(t,e,n)}function v(e){var t="function"===typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"===typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function p(e,t){var n="function"===typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{while((void 0===t||t-- >0)&&!(r=i.next()).done)a.push(r.value)}catch(d){o={error:d}}finally{try{r&&!r.done&&(n=i["return"])&&n.call(i)}finally{if(o)throw o.error}}return a}function g(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(p(arguments[t]));return e}function b(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;var r=Array(e),o=0;for(t=0;t<n;t++)for(var i=arguments[t],a=0,d=i.length;a<d;a++,o++)r[o]=i[a];return r}function y(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}function m(e){return this instanceof m?(this.v=e,this):new m(e)}function E(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r,o=n.apply(e,t||[]),i=[];return r={},a("next"),a("throw"),a("return"),r[Symbol.asyncIterator]=function(){return this},r;function a(e){o[e]&&(r[e]=function(t){return new Promise((function(n,r){i.push([e,t,n,r])>1||d(e,t)}))})}function d(e,t){try{u(o[e](t))}catch(n){l(i[0][3],n)}}function u(e){e.value instanceof m?Promise.resolve(e.value.v).then(c,s):l(i[0][2],e)}function c(e){d("next",e)}function s(e){d("throw",e)}function l(e,t){e(t),i.shift(),i.length&&d(i[0][0],i[0][1])}}function L(e){var t,n;return t={},r("next"),r("throw",(function(e){throw e})),r("return"),t[Symbol.iterator]=function(){return this},t;function r(r,o){t[r]=e[r]?function(t){return(n=!n)?{value:m(e[r](t)),done:"return"===r}:o?o(t):t}:o}}function w(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,n=e[Symbol.asyncIterator];return n?n.call(e):(e="function"===typeof v?v(e):e[Symbol.iterator](),t={},r("next"),r("throw"),r("return"),t[Symbol.asyncIterator]=function(){return this},t);function r(n){t[n]=e[n]&&function(t){return new Promise((function(r,i){t=e[n](t),o(r,i,t.done,t.value)}))}}function o(e,t,n,r){Promise.resolve(r).then((function(t){e({value:t,done:n})}),t)}}function A(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e}var O=Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e["default"]=t};function N(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&f(t,e,n);return O(t,e),t}function j(e){return e&&e.__esModule?e:{default:e}}function M(e,t,n,r){if("a"===n&&!r)throw new TypeError("Private accessor was defined without a getter");if("function"===typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===n?r:"a"===n?r.call(e):r?r.value:t.get(e)}function k(e,t,n,r,o){if("m"===r)throw new TypeError("Private method is not writable");if("a"===r&&!o)throw new TypeError("Private accessor was defined without a setter");if("function"===typeof t?e!==t||!o:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===r?o.call(e,n):o?o.value=n:t.set(e,n),n}function D(e,t){if(null===t||"object"!==typeof t&&"function"!==typeof t)throw new TypeError("Cannot use 'in' operator on non-object");return"function"===typeof e?t===e:e.has(t)}},d9c5:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getOutDegree=t.getInDegree=t.default=void 0;var r=function(e){var t={},n=e.nodes,r=void 0===n?[]:n,o=e.edges,i=void 0===o?[]:o;return r.forEach((function(e){t[e.id]={degree:0,inDegree:0,outDegree:0}})),i.forEach((function(e){t[e.source].degree++,t[e.source].outDegree++,t[e.target].degree++,t[e.target].inDegree++})),t},o=r;t.default=o;var i=function(e,t){var n=r(e);return n[t]?r(e)[t].inDegree:0};t.getInDegree=i;var a=function(e,t){var n=r(e);return n[t]?r(e)[t].outDegree:0};t.getOutDegree=a},dcfe:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.uniqueId=t.getOutEdgesNodeId=t.getNeighbors=t.getEdgesByNodeId=void 0;var r=function(e,t,n){void 0===t&&(t=[]);var r=t.filter((function(t){return t.source===e||t.target===e}));if("target"===n){var o=function(t){return t.source===e};return r.filter(o).map((function(e){return e.target}))}if("source"===n){var i=function(t){return t.target===e};return r.filter(i).map((function(e){return e.source}))}var a=function(t){return t.source===e?t.target:t.source};return r.map(a)};t.getNeighbors=r;var o=function(e,t){return t.filter((function(t){return t.source===e}))};t.getOutEdgesNodeId=o;var i=function(e,t){return t.filter((function(t){return t.source===e||t.target===e}))};t.getEdgesByNodeId=i;var a=function(e){void 0===e&&(e=0);var t="".concat(Math.random()).split(".")[1].substr(0,5),n="".concat(Math.random()).split(".")[1].substr(0,5);return"".concat(e,"-").concat(t).concat(n)};t.uniqueId=a},e98d:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.pageRankAsync=t.minimumSpanningTreeAsync=t.louvainAsync=t.labelPropagationAsync=t.getOutDegreeAsync=t.getNeighborsAsync=t.getInDegreeAsync=t.getDegreeAsync=t.getAdjMatrixAsync=t.floydWarshallAsync=t.findShortestPathAsync=t.findAllPathAsync=t.dijkstraAsync=t.detectCycleAsync=t.detectAllUndirectedCycleAsync=t.detectAllDirectedCycleAsync=t.detectAllCyclesAsync=t.connectedComponentAsync=t.GADDIAsync=void 0;var r=i(n("c0d2")),o=n("9023");function i(e){return e&&e.__esModule?e:{default:e}}var a=function(e,t){return(0,r.default)(o.ALGORITHM.getAdjMatrix).apply(void 0,[e,t])};t.getAdjMatrixAsync=a;var d=function(e,t){return(0,r.default)(o.ALGORITHM.connectedComponent).apply(void 0,[e,t])};t.connectedComponentAsync=d;var u=function(e){return(0,r.default)(o.ALGORITHM.getDegree)(e)};t.getDegreeAsync=u;var c=function(e,t){return(0,r.default)(o.ALGORITHM.getInDegree)(e,t)};t.getInDegreeAsync=c;var s=function(e,t){return(0,r.default)(o.ALGORITHM.getOutDegree)(e,t)};t.getOutDegreeAsync=s;var l=function(e){return(0,r.default)(o.ALGORITHM.detectCycle)(e)};t.detectCycleAsync=l;var f=function(e){return(0,r.default)(o.ALGORITHM.detectAllCycles)(e)};t.detectAllCyclesAsync=f;var h=function(e){return(0,r.default)(o.ALGORITHM.detectAllDirectedCycle)(e)};t.detectAllDirectedCycleAsync=h;var v=function(e){return(0,r.default)(o.ALGORITHM.detectAllUndirectedCycle)(e)};t.detectAllUndirectedCycleAsync=v;var p=function(e,t,n,i){return(0,r.default)(o.ALGORITHM.dijkstra).apply(void 0,[e,t,n,i])};t.dijkstraAsync=p;var g=function(e,t,n,i){return(0,r.default)(o.ALGORITHM.findAllPath).apply(void 0,[e,t,n,i])};t.findAllPathAsync=g;var b=function(e,t,n,i,a){return(0,r.default)(o.ALGORITHM.findShortestPath).apply(void 0,[e,t,n,i,a])};t.findShortestPathAsync=b;var y=function(e,t){return(0,r.default)(o.ALGORITHM.floydWarshall).apply(void 0,[e,t])};t.floydWarshallAsync=y;var m=function(e,t,n,i){return void 0===i&&(i=1e3),(0,r.default)(o.ALGORITHM.labelPropagation)(e,t,n,i)};t.labelPropagationAsync=m;var E=function(e,t,n,i){return(0,r.default)(o.ALGORITHM.louvain)(e,t,n,i)};t.louvainAsync=E;var L=function(e,t,n){return(0,r.default)(o.ALGORITHM.minimumSpanningTree).apply(void 0,[e,t,n])};t.minimumSpanningTreeAsync=L;var w=function(e,t,n){return(0,r.default)(o.ALGORITHM.pageRank).apply(void 0,[e,t,n])};t.pageRankAsync=w;var A=function(e,t,n){return(0,r.default)(o.ALGORITHM.getNeighbors).apply(void 0,[e,t,n])};t.getNeighborsAsync=A;var O=function(e,t,n,i,a,d,u){return void 0===n&&(n=!1),void 0===d&&(d="cluster"),void 0===u&&(u="cluster"),(0,r.default)(o.ALGORITHM.GADDI).apply(void 0,[e,t,n,i,a,d,u])};t.GADDIAsync=O},fe51:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=a;var r=n("dcfe");function o(e){void 0===e&&(e={});var t=e,n=function(){},r=function(){var e={};return function(t){var n=t.next;return!e[n]&&(e[n]=!0,!0)}}();return t.allowTraversal=e.allowTraversal||r,t.enter=e.enter||n,t.leave=e.leave||n,t}function i(e,t,n,o){o.enter({current:t,previous:n});var a=e.edges,d=void 0===a?[]:a;(0,r.getNeighbors)(t,d,"target").forEach((function(r){o.allowTraversal({previous:n,current:t,next:r})&&i(e,r,t,o)})),o.leave({current:t,previous:n})}function a(e,t,n){i(e,t,"",o(n))}}}]);