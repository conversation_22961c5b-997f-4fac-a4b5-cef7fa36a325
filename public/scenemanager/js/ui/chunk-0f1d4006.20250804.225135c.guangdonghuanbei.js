(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0f1d4006"],{"036c":function(t,e,a){},"095c":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{ref:"editor",staticClass:"editor-container"})},s=[],n=a("33f9"),r={name:"Editor",props:{language:{type:String,default:"html"},codes:{type:String,default:""},readOnly:{type:Boolean,default:!1}},data:function(){return{monacoEditor:null}},methods:{initEditor:function(){var t=this;this.monacoEditor=n["editor"].create(this.$refs.editor,{language:this.language,theme:"vs-dark",value:this.codes,readOnly:this.readOnly,wordWrap:"on",wrappingStrategy:"simple",wordWrapBreakAfterCharacters:"\t})]?|/&.,;¢°′″‰℃、。｡､￠，．：；？！％・･ゝゞヽヾーァィゥェォッャュョヮヵヶぁぃぅぇぉっゃゅょゎゕゖㇰㇱㇲㇳㇴㇵㇶㇷㇸㇹㇺㇻㇼㇽㇾㇿ々〻ｧｨｩｪｫｬｭｮｯｰ”〉》」』】〕）］｝｣",tabSize:2,roundedSelection:!1,minimap:{enabled:!1},automaticLayout:!0}),this.$emit("onMounted",this.monacoEditor),this.monacoEditor.onDidChangeModelContent((function(e){t.$emit("onCodeChange",t.monacoEditor.getValue(),t.monacoEditor)}))},setValue:function(t){this.monacoEditor.setValue(t)},setLayout:function(){this.monacoEditor.layout()}},mounted:function(){var t=this;this.$nextTick((function(){t.initEditor()}))},beforeDestroy:function(){this.monacoEditor=null}},l=r,o=(a("61ab"),a("2877")),c=Object(o["a"])(l,i,s,!1,null,"083464d8",null);e["a"]=c.exports},"0ea8":function(t,e,a){},1001:function(t,e,a){},1261:function(t,e,a){"use strict";a("6992")},"1a14":function(t,e,a){},"1c90":function(t,e,a){},"1f2e":function(t,e,a){},2060:function(t,e,a){"use strict";a("6b5b")},2349:function(t,e,a){"use strict";a("6839")},2759:function(t,e,a){"use strict";a("7405")},"2e53":function(t,e,a){},"31b1":function(t,e,a){"use strict";a("f137")},"329f":function(t,e,a){},"365a":function(t,e,a){},"3aaa":function(t,e,a){"use strict";a("439f")},"40a8":function(t,e,a){"use strict";a("036c")},"42eb":function(t,e,a){},"439f":function(t,e,a){},"49d9f":function(t,e,a){"use strict";a("1a14")},5058:function(t,e,a){"use strict";a("2e53")},"560f":function(t,e,a){},5889:function(t,e,a){"use strict";a("ed9b")},"5a44":function(t,e){t.exports="data:image/png;base64,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"},"5bb5":function(t,e,a){},"61ab":function(t,e,a){"use strict";a("42eb")},6839:function(t,e,a){},6992:function(t,e,a){},"6aa9":function(t,e,a){},"6b5b":function(t,e,a){},"6bcd":function(t,e,a){"use strict";a("de67")},"6d21":function(t,e,a){"use strict";a("a778")},7405:function(t,e,a){},"7ab6":function(t,e,a){"use strict";a("5bb5")},"7b45":function(t,e,a){"use strict";a("e034")},8859:function(t,e,a){},"902e":function(t,e,a){},"922e":function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJAAAACQCAMAAADQmBKKAAAASFBMVEUAAABIUVptc3pHUFlra4Brc3tHUVlsdHpIUFpxd31HU19sdHtIUVlHUFlrc3pKU1xeZm5ocHdTW2Rja3NhaXBqcXhWXmZPV2BqR8IiAAAADXRSTlMA/pKSBvPz1dUrK72998/8KwAAAZ1JREFUeNrt2clug0AQhOEBvCfULCx+/zdNFFlpycQh0RRyW6rvag6/uwc4EERERERERERERERE5NVdutOhr3Q4dZfAseuanqLpdpSe957mnVHU9UQd4fw0PVFz8TUgxohOPdUp1Dr0VIdQqydTkIL+YR6maZjdBMURX8boJGjEzegjaMa32UXQYEGDi6DJgiYFvcTK3B1qd7e9vweju1eH2TIopjEVR0HXCZ+Sm6CY8SV5CbI7uXERVAArchAUM8zQPD9oBAhFvKACEIpYQbYwM8SnBo1YyI+KmpLSdeOgArNWFAd8SnHLIFvYelEz3H4sGwYl/CwvNmPX2pD4QQWPTIuiGSaXbYJsYetFVxgbEjkowawULdpz4QcV/K7cH+j7IVGD7E+vFT2cZS7coIRV5e5AL4dEDCr4g/nuQC+HRAtaW5jNIM4rF3CCEkhy4QSBJ0VnQcjFWRCQorMgZG9BCLX2oNqHWkdQHUOtFlRtqHUG1Tn4GlEb6u3eQPPG+XDf0uazCxzn9rhHpf2xPQcREREREREREREREXlxH94v+dWLMqibAAAAAElFTkSuQmCC"},"939a":function(t,e,a){"use strict";a("329f")},9576:function(t,e,a){},"9a9c":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"dialog-wrap"},[a("div",{staticClass:"header"},[a("span",[t._v(" "+t._s(t.$t("dialog.coordinateImport.label"))+" "),a("el-tooltip",{attrs:{enterable:!1,effect:"dark",placement:"top"}},[a("div",{attrs:{slot:"content"},domProps:{innerHTML:t._s(t.$t("dialog.coordinateImport.tooltip"))},slot:"content"}),a("i",{staticClass:"outline-none el-input__icon el-icon-question"})])],1),a("span",{staticClass:"handle-box"},[a("i",{staticClass:"el-icon-close",on:{click:t.close}})])]),a("div",{staticClass:"content"},[a("div",{staticClass:"radio-tab"},[a("div",{staticClass:"margin-right-15"},[t._v(" "+t._s(t.$t("dialog.coordinateImport.label1"))+" "),a("el-tooltip",{attrs:{enterable:!1,effect:"dark",placement:"top"}},[a("div",{attrs:{slot:"content"},domProps:{innerHTML:t._s(t.$t("dialog.coordinateImport.tooltip1"))},slot:"content"}),a("i",{staticClass:"outline-none el-icon-question"})])],1),a("el-radio",{staticStyle:{color:"#FFF","font-size":"12px"},attrs:{label:"1"},model:{value:t.checkType,callback:function(e){t.checkType=e},expression:"checkType"}},[t._v(" "+t._s(t.$t("dialog.coordinateImport.label2"))+" ")]),a("el-radio",{staticStyle:{color:"#FFF","font-size":"12px"},attrs:{label:"2"},model:{value:t.checkType,callback:function(e){t.checkType=e},expression:"checkType"}},[t._v(" "+t._s(t.$t("dialog.coordinateImport.label3"))+" ")])],1),a("textarea",{directives:[{name:"model",rawName:"v-model",value:t.content,expression:"content"}],staticClass:"textarea",staticStyle:{resize:"none"},domProps:{value:t.content},on:{input:function(e){e.target.composing||(t.content=e.target.value)}}}),a("div",{staticClass:"btns"},[a("button",{staticClass:"btn cancel",on:{click:t.cancel}},[t._v(" "+t._s(t.$t("messageTips.cancel"))+" ")]),a("button",{staticClass:"btn confirm",on:{click:t.confirm}},[t._v(" "+t._s(t.$t("menuIconName.confirm"))+" ")])])])])},s=[],n=a("2909"),r=(a("a15b"),a("ac1f"),a("1276"),a("4de4"),a("d3b7"),a("d81d"),{name:"",data:function(){return{content:"",checkType:"1"}},methods:{cancel:function(){this.close()},close:function(){this.$emit("close")},confirm:function(){if(this.content){var t=this.content.split(/[\t\r\f\n\s]*/g).join(""),e=t.split(";");e=e.filter((function(t){return""!=t}));var a=e.map((function(t){return t=t.split(","),t}));2==this.checkType&&(a=a.map((function(t){var e,a=(e=window.scene.mv.tools.coordinate).mercator2vector.apply(e,Object(n["a"])(t));return[a.x,a.y,a.z]}))),this.$emit("confirm",a)}else this.$message({type:"info",message:this.$t("dialog.coordinateImport.message")})}},beforeDestroy:function(){this.content=""}}),l=r,o=(a("f6b8"),a("2877")),c=Object(o["a"])(l,i,s,!1,null,"03d405a4",null);e["a"]=c.exports},"9e0b":function(t,e,a){"use strict";a("6aa9")},a778:function(t,e,a){},abf2:function(t,e,a){},b703:function(t,e,a){},c7fea:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"settings-container"},[t.dragData.hideSettingTab?t._e():a("div",{staticClass:"top-tabs"},t._l(t.tabDatas,(function(e,i){return a("span",{key:i,class:[{active:i==t.tabActive},"tabs-item"],on:{click:function(a){return t.switchTopTabs(e,i)}}},[t._v(" "+t._s(e.title)+" ")])})),0),t.renderSetOption.includes("sceneStyleSet")?[a("StyleSet",{directives:[{name:"show",rawName:"v-show",value:"sceneStyleSet"==t.contentActive,expression:"contentActive=='sceneStyleSet'"}],key:t.settingKey[0],ref:"sceneStyleSet",on:{closeSet:t.cancel,eventsContentChange:t.eventsContentChange,tabDatasSetting:t.tabDatasSetting,styleSetDataReady:t.styleSetDataReady,setload:function(e){return t.loading.close()}}})]:t._e(),t.renderSetOption.includes("sceneDataSet")?[a("DatasSet",{directives:[{name:"show",rawName:"v-show",value:"sceneDataSet"==t.contentActive,expression:"contentActive=='sceneDataSet'"}],key:t.settingKey[1],ref:"sceneDataSet",attrs:{dragData:t.dragData},on:{closeSet:t.cancel,setRelationData:t.setRelationData}})]:t._e(),t.renderSetOption.includes("sceneEventSet")?[a("EventsSet",{directives:[{name:"show",rawName:"v-show",value:"sceneEventSet"==t.contentActive,expression:"contentActive=='sceneEventSet'"}],key:t.settingKey[2],ref:"sceneEventSet",attrs:{contentActive:t.contentActive},on:{closeSet:t.cancel}})]:t._e(),t.renderSetOption.includes("sceneAdvancedSet")?[a("AdvancedSet",{directives:[{name:"show",rawName:"v-show",value:"sceneAdvancedSet"==t.contentActive,expression:"contentActive=='sceneAdvancedSet'"}],key:t.settingKey[2],ref:"sceneAdvancedSet",attrs:{dragData:t.dragData},on:{closeSet:t.cancel}})]:t._e(),t.renderSetOption.includes("triggerConditions")?[a("TriggerConditions",{directives:[{name:"show",rawName:"v-show",value:"triggerConditions"==t.contentActive,expression:"contentActive=='triggerConditions'"}],key:t.settingKey[2],ref:"triggerConditions",attrs:{dragData:t.dragData},on:{closeSet:t.cancel}})]:t._e(),t.renderSetOption.includes("triggerBehavior")?[a("TriggerBehavior",{directives:[{name:"show",rawName:"v-show",value:"triggerBehavior"==t.contentActive,expression:"contentActive=='triggerBehavior'"}],key:t.settingKey[2],ref:"triggerBehavior",attrs:{dragData:t.dragData},on:{closeSet:t.cancel}})]:t._e(),t.renderSetOption.includes("materialParams")?[a("MaterialParams",{directives:[{name:"show",rawName:"v-show",value:"materialParams"==t.contentActive,expression:"contentActive=='materialParams'"}],ref:"materialParamsRef",on:{resetOrigin:function(e){return t.cancel("cancel")}}})]:t._e(),t.renderSetOption.includes("materialTexture")?[a("MaterialTexture",{directives:[{name:"show",rawName:"v-show",value:"materialTexture"==t.contentActive,expression:"contentActive=='materialTexture'"}],ref:"materialTextureRef",on:{inputUpdateMaterial:t.inputUpdateMaterial}})]:t._e(),t.renderSetOption.includes("timeSeries")?[a("TimeSeries",{directives:[{name:"show",rawName:"v-show",value:"timeSeries"==t.contentActive,expression:"contentActive=='timeSeries'"}],ref:"timeSeriesRef",attrs:{dragData:t.dragData}})]:t._e(),t.dragData.hideFormButton?t._e():a("div",{staticClass:"objectSetting-form-button"},[a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:t.$t("messageTips.cancel"),placement:"top"}},[a("span",{staticClass:"btn btn-cancel cursor-btn",on:{click:function(e){return t.cancel("cancel")}}},[a("i",{staticClass:"el-icon-close"})])]),a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:t.$t("messageTips.save"),placement:"top"}},[a("span",{staticClass:"btn btn-default cursor-btn",on:{click:t.settingSubmit}},[a("i",{staticClass:"el-icon-check"})])])],1)],2),t.isRelation?a("RelationSet",{ref:"relation",on:{submitRelation:t.submitRelation}}):t._e()],1)},s=[],n=(a("caad"),a("2532"),a("d81d"),a("d3b7"),a("159b"),a("7db0"),a("e9c4"),a("4de4"),a("b0c0"),a("b64b"),function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"bottom-content"},["annotation"==t.dragData.type||"billboard"==t.dragData.type?a("AnchorPointSetting",{ref:t.dragData.type,attrs:{currentElement:t.dragData}}):t._e()],1)}),r=[],l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex relative"},["videoAnchor"==t.checkedAnchorType?[a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("featureSetting.data.anchorPoint.label"))+" ")]),a("div",{staticClass:"items-center"},[a("el-input",{attrs:{size:"mini",placeholder:"请输入标题(可选)"},model:{value:t.params.title,callback:function(e){t.$set(t.params,"title",e)},expression:"params.title"}})],1)])]),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("featureSetting.data.anchorPoint.label1"))+" ")]),a("div",{staticClass:"items-center"},[a("el-input",{attrs:{size:"mini",placeholder:"请输入视频链接地址"},scopedSlots:t._u([t.videoListSHJS.renderState?{key:"append",fn:function(){return[a("span",{staticClass:"cursor-btn",on:{click:function(e){return t.handleAcnhorVideoListClick_SHJG()}}},[a("CommonSVG",{attrs:{color:t.videoListSHJS.btnState?"var(--theme)":"var(--primary-text-color)","icon-class":"list_mode",size:12}})],1)]},proxy:!0}:null],null,!0),model:{value:t.params.videoLink,callback:function(e){t.$set(t.params,"videoLink",e)},expression:"params.videoLink"}})],1)])])]:a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("featureSetting.data.anchorPoint.label2"))+" ")]),a("div",{staticClass:"popup-item-center items-center",on:{click:t.togglePanel}},[a("span",{staticClass:"margin-left-5"},[t._v(" "+t._s(t.$t("featureSetting.style.heatMap.label5"))+" ")]),a("CommonSVG",{attrs:{"icon-class":"add_feature",size:12}})],1)]),t.pointDataState?a("div",{staticClass:"regional-coordinates-container"},[a("div",{staticClass:"title"},[a("span",[t._v(" "+t._s(t.$t("featureSetting.data.anchorPoint.label3"))+" ")]),a("div",[a("i",{staticClass:"el-icon-close cursor-btn",on:{click:t.togglePanel}})])]),a("el-row",{staticClass:"items-center margin-bottom-10",attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-input",{attrs:{size:"small",placeholder:t.$t("featureSetting.data.anchorPoint.placeholder")},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v(t._s(t.$t("featureSetting.data.anchorPoint.label4")))])]},proxy:!0}],null,!1,2971043449),model:{value:t.params.title,callback:function(e){t.$set(t.params,"title",e)},expression:"params.title"}})],1)],1),t.isVothing?a("div",{staticClass:"items-center margin-bottom-10"},[a("div",{staticClass:"el-input el-input--small el-input-group el-input-group--prepend"},[a("div",{staticClass:"el-input-group__prepend"},[a("span",[t._v(t._s(t.$t("featureSetting.data.anchorPoint.label5")))])]),t.isVothing?a("el-cascader",{staticStyle:{width:"100%",height:"32px","line-height":"32px"},attrs:{options:t.iotDataSelect.dataOptions,props:{value:"id",label:"name",children:"children",checkStrictly:!0},clearable:"","popper-class":"data-area-cascader","change-on-select":""},on:{change:t.iotDataChange},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.node,s=e.data;return[a("span",[t._v(t._s(s.name))]),i.isLeaf?t._e():a("span",[t._v(" ("+t._s(s.children.length)+") ")])]}}],null,!1,2759470695),model:{value:t.iotDataSelect.selectedData,callback:function(e){t.$set(t.iotDataSelect,"selectedData",e)},expression:"iotDataSelect.selectedData"}}):t._e()],1)]):t._e(),a("div",{staticClass:"title"},[a("span",{staticClass:"color-98A3B3"},[t._v(" "+t._s(t.isShowAttr?t.$t("featureSetting.data.anchorPoint.label6"):t.$t("featureSetting.data.anchorPoint.label2"))+" ")]),t.isShowAttr?a("div",[a("i",{staticClass:"el-icon-plus cursor-btn",on:{click:function(e){return t.handleAttrAdd()}}})]):a("div",[a("i",{staticClass:"el-icon-plus cursor-btn",on:{click:function(e){return t.handleAddDataObj("custom")}}})])]),a("div",{staticClass:"datas-table"},[t.isShowAttr?a("el-table",{ref:"attrTbl",staticStyle:{width:"100%","margin-top":"12px"},attrs:{"row-style":{height:"0"},"cell-style":t.rowClass,"show-header":!1,height:"120px",data:t.attrModifiedObj,"row-key":t.getRowKeys,"row-class-name":"attr-table-column"}},[a("el-table-column",{attrs:{label:"",width:"290"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:t.$t("featureSetting.data.anchorPoint.placeholder1")},on:{change:function(a){return t.handleAttrModify(e.row)}},model:{value:e.row.name,callback:function(a){t.$set(e.row,"name",a)},expression:"scope.row.name"}},t._l(t.attrObj,(function(t,e){return a("el-option",{key:e,attrs:{label:t.name,value:t.name}})})),1)]}}],null,!1,4290486792)}),a("el-table-column",{attrs:{label:"","min-width":"30"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",{staticClass:"custom-column color-FF6640 cursor-btn",on:{click:function(a){return t.handleAttrDelete(e.$index,e.row)}}},[t._v(" "+t._s(t.$t("menuIconName.delete"))+" ")])]}}],null,!1,3768258885)})],1):a("el-table",{staticStyle:{width:"100%"},attrs:{size:"mini",data:t.params.dataList,height:"240px","header-row-class-name":"datas-table-title","row-class-name":"datas-table-column"}},[a("el-table-column",{attrs:{label:t.$t("others.name"),"min-width":"70"},scopedSlots:t._u([{key:"default",fn:function(e){return[t.params.dataList[e.$index].isEdit?a("el-input",{attrs:{size:"mini",placeholder:t.$t("others.name")},model:{value:t.params.dataList[e.$index].key,callback:function(a){t.$set(t.params.dataList[e.$index],"key",a)},expression:"params.dataList[scope.$index].key"}}):a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:e.row.key,placement:"top"}},[a("span",{staticClass:"custom-column"},[t._v(t._s(e.row.key))])])]}}],null,!1,2634980637)}),a("el-table-column",{attrs:{label:t.$t("others.value"),"min-width":"90"},scopedSlots:t._u([{key:"default",fn:function(e){return[t.params.dataList[e.$index].isEdit?a("el-input",{staticClass:"annotation-value-type-append",attrs:{size:"mini",placeholder:t.$t("others.value")},model:{value:t.params.dataList[e.$index].value,callback:function(a){t.$set(t.params.dataList[e.$index],"value",a)},expression:"params.dataList[scope.$index].value"}},[a("template",{slot:"append"},[a("el-dropdown",{attrs:{trigger:"click"},on:{command:function(a){return t.handleCommand(a,e)}}},[a("span",{staticClass:"el-dropdown-link cursor-btn"},[a("CommonSVG",{attrs:{"icon-class":t.valueTypeIcon[t.params.dataList[e.$index].valType],color:"var(--theme)",size:16}})],1),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{command:"txt"}},[t._v(t._s(t.$t("others.txt")))]),a("el-dropdown-item",{attrs:{command:"img"}},[t._v(t._s(t.$t("others.img")))]),a("el-dropdown-item",{attrs:{command:"link"}},[t._v(t._s(t.$t("others.link")))])],1)],1)],1)],2):a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:e.row.value,placement:"top"}},[a("span",{staticClass:"custom-column"},[t._v(t._s(e.row.value))])])]}}],null,!1,3373132757)}),a("el-table-column",{attrs:{label:"操作","min-width":"60","header-align":"center",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[t.params.dataList[e.$index].isEdit?[a("span",{staticClass:"custom-column color-FF6640 cursor-btn",on:{click:function(a){return t.handleCancelCustom(e)}}},[t._v(" "+t._s(t.$t("messageTips.cancel"))+"  ")]),a("span",{staticClass:"custom-column color-FF6640 cursor-btn",on:{click:function(a){return t.handleAddCustom(e)}}},[t._v("  "+t._s(t.$t("menuIconName.confirm"))+" ")])]:[a("span",{staticClass:"custom-column color-FF6640 cursor-btn",on:{click:function(a){return t.handleEditDataObj(e)}}},[t._v(" "+t._s(t.$t("menuIconName.edit"))+"  ")]),a("span",{staticClass:"custom-column color-FF6640 cursor-btn",on:{click:function(a){return t.handleDelete(e.$index)}}},[t._v("  "+t._s(t.$t("menuIconName.delete"))+" ")])]]}}],null,!1,2489787414)})],1)],1)],1):t._e()])],2)},o=[],c=(a("3ca3"),a("ddb0"),a("a434"),a("ed08")),u=a("b893"),d={name:"AnchorPointSetting",props:["currentElement"],components:{CommonSVG:function(){return a.e("chunk-51f90655").then(a.bind(null,"17d0"))}},data:function(){return{pointDataState:!1,checkedAnchorType:"",params:{title:"",dataList:[],dataTypeSelect:"",videoLink:""},iotDataSelect:{dataOptions:[],selectedData:["0"],selectedContent:{}},relationArr:[],isShowAttr:!1,attrModifiedObj:[],attrObj:[],valueTypeIcon:{txt:"text_feature",img:"image",link:"relation_lock"},videoListSHJS:{renderState:!1,btnState:!1}}},computed:{isVothing:function(){return this.$store.state.menuList.isVothing}},created:function(){this.handleRenderVideoList_SHJG();var t=window.scene.features.get(this.currentElement.id);t.data.setType&&void 0!=t.data.content?(this.checkedAnchorType=t.data.setType.type,this.initEditDatas(t.data)):this.checkedAnchorType=this.currentElement.checkedType,this.getiotData()},methods:{handleRenderVideoList_SHJG:function(){var t=Object(u["c"])("anchorVideoList");""!=t&&null!=t&&(this.videoListSHJS.renderState=!0,this.$bus.on("handleAcnhorVideoListSelected",this.handleAcnhorVideoListSelected_SHJG))},handleAcnhorVideoListClick_SHJG:function(){this.videoListSHJS.btnState=!this.videoListSHJS.btnState,this.$bus.emit("handleAcnhorVideoListState",this.videoListSHJS.btnState)},handleAcnhorVideoListSelected_SHJG:function(t){t&&(t.title&&""!=t.title&&(this.params.title=t.title),t.url&&""!=t.url&&(this.params.videoLink=t.url),this.videoListSHJS.btnState=t.state)},handleCommand:function(t,e){this.params.dataList[e.$index].valType=t,"link"==t&&(this.params.dataList[e.$index].clickToView=this.$t("others.clickToView"))},initEditDatas:function(t){if("videoAnchor"==t.setType.type)window.myVideoId=this.currentElement.id,this.params.videoLink=t.setType.video.videoLink,this.params.title=t.setType.video.title;else if(this.params.title=t.content.title,this.params.dataList=t.content.dataList||[],this.pointDataState=!0,this.isVothing&&"annotation"===t.setType.type){var e,a=this.$store.state.scene.sceneList||[],i=this.currentElement.dataKey;for(var s in a)"element"===a[s].type&&a[s].elementid===i&&(e=a[s].data);e&&e.widgets&&(this.iotDataSelect.selectedData=e.widgets.entityData?[e.widgets.entityData.id]:"",this.iotDataSelect.selectedContent=e.widgets.entityData?e.widgets.entityData:{},this.relationArr=e.widgets.relationArr,this.$emit("setRelationData",this.relationArr),this.showPropertyData());window.scene.features.get(this.dragData.id)}},getiotData:function(){var t=this.$store.state.scene.iotentityList||[];t.length>0?(this.iotDataSelect.dataOptions=Object(c["b"])(t),this.iotDataSelect.dataOptions.length>0&&"0"!==this.iotDataSelect.dataOptions[0].id&&this.iotDataSelect.dataOptions.unshift({name:this.$t("others.nothing"),id:"0"})):this.iotDataSelect.dataOptions=[{name:this.$t("others.nothing"),id:"0"}]},iotDataChange:function(t){if("0"!==t){var e=Object(c["c"])(this.iotDataSelect.dataOptions,t[t.length-1]);this.iotDataSelect.selectedContent=e,this.isShowAttr=!0,this.showPropertyData()}else this.iotDataSelect.selectedContent={},this.attrModifiedObj=[],this.isShowAttr=!1;this.relationArr=[],this.$store.commit("getRelationData",{isShow:!1})},showPropertyData:function(){if("{}"===JSON.stringify(this.iotDataSelect.selectedContent))return this.iotDataSelect.selectedData=["0"],void(this.isShowAttr=!1);this.isShowAttr=!0;var t=this.iotDataSelect.selectedContent.data.entity,e=this.iotDataSelect.selectedContent.data.entityProperty;if(t&&t.length>0){var a=t[0].id,i=e[a];this.attrModifiedObj=Object(c["b"])(i)}var s=this.$store.state.scene.iotentityList,n=Array.isArray(this.iotDataSelect.selectedData)?Object(c["c"])(s,this.iotDataSelect.selectedData[this.iotDataSelect.selectedData.length-1]):Object(c["c"])(s,this.iotDataSelect.selectedData);if(n.data&&n.data.entity&&n.data.entity.length>0){var r=n.data.entity[0].id,l=n.data.entityProperty[r];this.attrObj=Object(c["b"])(l)}else this.attrObj=[]},rowClass:function(){return"text-align: left;padding:0;"},getRowKeys:function(t){return t.id},handleAttrModify:function(t){for(var e=0;e<this.attrModifiedObj.length-1;e++)for(var a=e+1;a<this.attrModifiedObj.length;a++)this.attrModifiedObj[e].name===this.attrModifiedObj[a].name&&(this.attrModifiedObj.splice(a,1),a--)},handleAttrDelete:function(t){this.attrModifiedObj.splice(t,1)},togglePanel:function(){this.pointDataState=!this.pointDataState},handleCancelCustom:function(t){this.params.dataList[t.$index].isEdit&&"add"==this.params.dataList[t.$index].type?this.params.dataList.splice(t.$index,1):(this.params.dataList[t.$index].isEdit=!1,delete this.params.dataList[t.$index].type)},handleAddCustom:function(t){this.params.dataList[t.$index].isEdit=!1,delete this.params.dataList[t.$index].type},handleEditDataObj:function(t){this.params.dataList[t.$index].isEdit=!0},handleDelete:function(t){this.params.dataList.splice(t,1)},handleAddDataObj:function(t){switch(t){case"custom":var e={key:"",value:"",isEdit:!0,type:"add",valType:"txt"};this.params.dataList.unshift(e);break}}},beforeDestroy:function(){this.videoListSHJS.renderState&&this.$bus.off("handleAcnhorVideoListSelected",this.handleAcnhorVideoListSelected_SHJG)}},m=d,p=(a("3aaa"),a("2877")),h=Object(p["a"])(m,l,o,!1,null,"590027e2",null),v=h.exports,f={name:"SceneDatasSet",props:["dragData"],components:{AnchorPointSetting:v},methods:{getDatas:function(){var t=this.$refs[this.dragData.type].params;return"annotation"==this.dragData.type&&t.dataList.length&&(t.dataList=t.dataList.filter((function(t){return!t.isEdit}))),t},beforeValidate:function(){var t=!0;if("annotation"==this.dragData.type&&"videoAnchor"==this.dragData.checkedType){var e=this.getDatas(),a=e.videoLink;""==a&&(t=!1,this.$message.error(this.$t("featureSetting.data.anchorPoint.message")))}return t},postRelationDatas:function(t){var e=null;switch(this.dragData.type){case"annotation":e=this.$refs[this.dragData.type],e.relationArr=t;break}},getRelationDatas:function(){var t={};switch(this.dragData.type){case"annotation":this.$refs[this.dragData.type],t=this.$refs[this.dragData.type].iotDataSelect;break}return t},getAttr:function(){var t=[];switch(this.dragData.type){case"annotation":this.$refs[this.dragData.type],t=this.$refs[this.dragData.type].attrModifiedObj;break}return t}}},g=f,b=(a("5058"),a("dd60"),Object(p["a"])(g,n,r,!1,null,"a7de0704",null)),S=b.exports,C=a("22e2"),y=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"bottom-content"},["annotation"==t.dragData.type||"billboard"==t.dragData.type?a("AnchorPointSetting",{ref:t.dragData.type,attrs:{tabActive:t.contentActive,currentElement:t.dragData}}):t._e()],1)},w=[],E=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex relative"},["custom"!=t.contentType?[a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("div",{staticClass:"popup-item-center items-center",class:{disable:t.eventForm.length==t.eventsOptions.length},on:{click:function(e){return t.addObjectEvent()}}},[a("span",{staticClass:"margin-left-5"},[t._v(" "+t._s(t.$t("featureSetting.event.anchorPoint.label"))+" ")]),a("CommonSVG",{attrs:{"icon-class":"add_feature",size:12}})],1)])]),a("div",{staticClass:"after-lineX"}),t.eventForm.length>0?a("div",{staticClass:"anchor-list-div h100"},t._l(t.eventForm,(function(e,i){return a("div",{key:"events-"+i},[a("div",{staticClass:"items-center"},[a("div",{ref:"eventForm"+i,refInFor:!0,staticClass:"popup-item-center items-center",on:{click:function(e){return t.togglePanel(i)}}},[a("span",{staticClass:"margin-left-5 margin-right-5"},[t._v(" "+t._s(e.name+t.$t("featureSetting.event.anchorPoint.label1"))+" ")]),a("CommonSVG",{attrs:{"icon-class":"notation_feature",size:12}})],1),a("el-tooltip",{attrs:{effect:"dark",content:t.$t("menuIconName.delete"),placement:"top"}},[a("i",{staticClass:"el-icon el-icon-delete cursor-btn",on:{click:function(e){return t.deleteObjectEvent(i)}}})])],1)])})),0):t._e(),t.eventForm.length>0&&t.popupState?a("div",{staticClass:"regional-coordinates-container",style:{left:t.popupLeft+"px",maxHeight:t.popupMaxHeight+"px"}},[a("div",{staticClass:"title"},[a("span",[t._v(t._s(t.$t("featureSetting.event.anchorPoint.label1")))]),a("i",{staticClass:"el-icon-close cursor-btn",on:{click:function(e){return t.togglePanel(t.currentEventTab)}}})]),"custom"!=t.eventForm[t.currentEventTab].behaviorList[0]?a("div",{staticClass:"annotation-data-type-select margin-bottom-8"},[a("div",{staticClass:"el-input el-input--small el-input-group el-input-group--prepend"},[a("div",{staticClass:"el-input-group__prepend"},[a("span",[t._v(t._s(t.$t("featureSetting.event.anchorPoint.label2")))])]),a("el-select",{attrs:{size:"mini",placeholder:t.$t("featureSetting.event.anchorPoint.placeholder")},on:{change:t.selectObjectEvent},model:{value:t.eventForm[t.currentEventTab].eventsValue,callback:function(e){t.$set(t.eventForm[t.currentEventTab],"eventsValue",e)},expression:"eventForm[currentEventTab].eventsValue"}},t._l(t.eventsOptions,(function(t){return a("el-option",{key:t.value,attrs:{disabled:t.disabled,label:t.label,value:t.value}})})),1)],1)]):t._e(),t._l(t.eventForm[t.currentEventTab].behaviorListState,(function(e,i){return a("div",{key:"behavior-"+i,staticClass:"tab-div tab-events"},[a("div",{staticClass:"tab-top-bar datas-top-bar"},[a("div",{staticClass:"left-title",on:{click:function(e){return t.switchListState(i)}}},[a("span",[t._v(t._s(t.$t("featureSetting.event.anchorPoint.label3")+(i+1)))]),a("i",e?{staticClass:"el-icon-caret-bottom"}:{staticClass:"el-icon-caret-top"})]),a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:t.$t("featureSetting.event.anchorPoint.tooltip"),placement:"top"}},[a("div",{staticClass:"right-menu cursor-btn",on:{click:function(e){return t.removeCurrentBehaviorType(i)}}},[a("i",{staticClass:"el-icon el-icon-delete"})])])],1),a("el-collapse-transition",[a("div",{directives:[{name:"show",rawName:"v-show",value:e,expression:"listState"}],staticClass:"events-input"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{staticClass:"margin-bottom-5",attrs:{span:24}},[a("div",{staticClass:"el-input el-input--mini el-input-group el-input-group--prepend"},[a("div",{staticClass:"el-input-group__prepend"},[a("span",[t._v(t._s(t.$t("featureSetting.event.anchorPoint.label3")))])]),a("el-select",{attrs:{size:"mini",placeholder:t.$t("featureSetting.event.anchorPoint.placeholder1")},model:{value:t.eventForm[t.currentEventTab].behaviorList[i],callback:function(e){t.$set(t.eventForm[t.currentEventTab].behaviorList,i,e)},expression:"eventForm[currentEventTab].behaviorList[stateEq]"}},t._l(t.behaviorOption,(function(e,s){return a("el-option-group",{key:"behavior"+s,attrs:{label:e.label}},t._l(e.options,(function(e){return a("el-option",{key:e.type,attrs:{disabled:t.eventForm[t.currentEventTab].behaviorList.includes(e.type),label:e.title,value:e.type},nativeOn:{click:function(a){return t.getBehaviorValue(e.type,s,i)}}})})),1)})),1)],1)])],1),void 0!=t.eventForm[t.currentEventTab].objectsList[i]&&t.eventForm[t.currentEventTab].objectsList[i]>0?a("el-row",{staticClass:"margin-bottom-5",attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("div",{staticClass:"el-input el-input--mini el-input-group el-input-group--prepend"},[a("div",{staticClass:"el-input-group__prepend"},[a("span",[t._v(t._s(t.$t("formRelational.trigger.label")))])]),"1"==t.eventForm[t.currentEventTab].objectsList[i]?[a("div",{staticClass:"space-between el-input__inner cursor-btn",on:{click:function(e){return t.getElementsTags(i)}}},[t._v(" "+t._s(t.eventForm[t.currentEventTab].selectedElementIndex==i?t.$t("formRelational.trigger.label1"):t.$t("formRelational.trigger.label2"))+" "),a("CommonSVG",{attrs:{"icon-class":"mouse_left",size:14}})],1)]:t._e(),"2"==t.eventForm[t.currentEventTab].objectsList[i]?[a("div",{staticClass:"space-between el-input__inner cursor-btn",on:{click:function(e){return t.getFeaturesTags(i)}}},[t._v(" "+t._s(t.eventForm[t.currentEventTab].selectedFeatureIndex==i?t.$t("formRelational.trigger.label3"):t.$t("formRelational.trigger.label4"))+" "),a("CommonSVG",{attrs:{"icon-class":"mouse_left",size:14}})],1)]:t._e(),"3"==t.eventForm[t.currentEventTab].objectsList[i]?[a("el-select",{attrs:{size:"mini",placeholder:t.$t("formRelational.trigger.placeholder")},on:{change:function(e){return t.selectAnimateOption(e,i)}}},t._l(t.animationOption,(function(e){return a("el-option",{key:e.value,attrs:{disabled:t.eventForm[t.currentEventTab].selectedAnimateDisable.includes(e.value),label:e.label,value:e.value}})})),1)]:t._e()],2)])],1):t._e(),"1"==t.eventForm[t.currentEventTab].objectsList[i]&&t.eventForm[t.currentEventTab].selectedElements[i].length>0?a("el-row",{staticClass:"margin-bottom-5",staticStyle:{"flex-wrap":"wrap"},attrs:{gutter:20}},[t.eventForm[t.currentEventTab].selectedElements[i].length<6?a("el-col",{attrs:{span:24}},t._l(t.eventForm[t.currentEventTab].selectedElements[i],(function(e){return a("el-tag",{key:e.id,staticClass:"margin-right-5 margin-bottom-5 cursor-btn",attrs:{color:"rgba(255,255,255,0.1)",size:"small",effect:"dark",type:"info","disable-transitions":!0,closable:""},on:{click:function(a){return t.handleTagClick(e,i,"element")},close:function(a){return t.handleTagClose(e,i,"element")}}},[t._v(" "+t._s(e.name)+" ")])})),1):a("el-col",{attrs:{span:24}},[a("div",{staticClass:"el-input el-input--mini el-input-group"},[a("el-popover",{attrs:{placement:"top",width:"400",trigger:"manual"},model:{value:t.eventForm[t.currentEventTab].selectedElementsState,callback:function(e){t.$set(t.eventForm[t.currentEventTab],"selectedElementsState",e)},expression:"eventForm[currentEventTab].selectedElementsState"}},[a("div",[a("div",{staticClass:"space-between el-icon"},[a("span",[t._v(" ")]),a("i",{staticClass:"el-icon-close cursor-btn",on:{click:function(e){e.stopPropagation(),t.eventForm[t.currentEventTab].selectedElementsState=!t.eventForm[t.currentEventTab].selectedElementsState}}})])]),t._l(t.eventForm[t.currentEventTab].selectedElements[i],(function(e){return a("el-tag",{key:e.id,staticClass:"margin-right-5 margin-bottom-5 cursor-btn",attrs:{color:"rgba(255,255,255,0.1)",size:"small",effect:"dark",type:"info","disable-transitions":!0,closable:""},on:{click:function(a){return t.handleTagClick(e,i,"element")},close:function(a){return t.handleTagClose(e,i,"element")}}},[t._v(" "+t._s(e.name)+" ")])})),a("div",{staticClass:"el-input__inner cursor-btn text-center",attrs:{slot:"reference"},on:{click:function(e){t.eventForm[t.currentEventTab].selectedElementsState=!t.eventForm[t.currentEventTab].selectedElementsState}},slot:"reference"},[t._v(" "+t._s(t.$t("formRelational.trigger.message",{num:t.eventForm[t.currentEventTab].selectedElements[i].length}))+" ")])],2)],1)])],1):t._e(),"2"==t.eventForm[t.currentEventTab].objectsList[i]&&t.eventForm[t.currentEventTab].selectedFeatures[i].length>0?a("el-row",{staticClass:"margin-bottom-5",staticStyle:{"flex-wrap":"wrap"},attrs:{gutter:20}},[t.eventForm[t.currentEventTab].selectedFeatures[i].length<6?a("el-col",{attrs:{span:24}},t._l(t.eventForm[t.currentEventTab].selectedFeatures[i],(function(e){return a("el-tag",{key:e.id,staticClass:"margin-right-5 margin-bottom-5 cursor-btn",attrs:{color:"rgba(255,255,255,0.1)",size:"small",effect:"dark",type:"info","disable-transitions":!0,closable:""},on:{click:function(a){return t.handleTagClick(e,i,"feature")},close:function(a){return t.handleTagClose(e,i,"feature")}}},[t._v(" "+t._s(e.name)+" ")])})),1):a("el-col",{attrs:{span:24}},[a("div",{staticClass:"el-input el-input--mini el-input-group"},[a("el-popover",{attrs:{placement:"top",width:"400",trigger:"manual"},model:{value:t.eventForm[t.currentEventTab].selectedFeaturesState,callback:function(e){t.$set(t.eventForm[t.currentEventTab],"selectedFeaturesState",e)},expression:"eventForm[currentEventTab].selectedFeaturesState"}},[a("div",[a("div",{staticClass:"space-between el-icon"},[a("span",[t._v(" ")]),a("i",{staticClass:"el-icon-close cursor-btn",on:{click:function(e){e.stopPropagation(),t.eventForm[t.currentEventTab].selectedFeaturesState=!t.eventForm[t.currentEventTab].selectedFeaturesState}}})])]),t._l(t.eventForm[t.currentEventTab].selectedFeatures[i],(function(e){return a("el-tag",{key:e.id,staticClass:"margin-right-5 margin-bottom-5 cursor-btn",attrs:{color:"rgba(255,255,255,0.1)",size:"small",effect:"dark",type:"info","disable-transitions":!0,closable:""},on:{click:function(a){return t.handleTagClick(e,i,"feature")},close:function(a){return t.handleTagClose(e,i,"feature")}}},[t._v(" "+t._s(e.name)+" ")])})),a("div",{staticClass:"el-input__inner cursor-btn text-center",attrs:{slot:"reference"},on:{click:function(e){t.eventForm[t.currentEventTab].selectedFeaturesState=!t.eventForm[t.currentEventTab].selectedFeaturesState}},slot:"reference"},[t._v(" "+t._s(t.$t("formRelational.trigger.message1",{num:t.eventForm[t.currentEventTab].selectedFeatures[i].length}))+" ")])],2)],1)])],1):t._e(),"3"==t.eventForm[t.currentEventTab].objectsList[i]&&t.eventForm[t.currentEventTab].selectedAnimates[i].length>0?a("el-row",{staticClass:"margin-bottom-5",staticStyle:{"flex-wrap":"wrap"},attrs:{gutter:20}},[t.eventForm[t.currentEventTab].selectedAnimates[i].length<6?a("el-col",{attrs:{span:24}},t._l(t.eventForm[t.currentEventTab].selectedAnimates[i],(function(e){return a("el-tag",{key:e.id,staticClass:"margin-right-5 margin-bottom-5 cursor-btn",attrs:{color:"rgba(255,255,255,0.1)",size:"small",effect:"dark",type:"info","disable-transitions":!0,closable:""},on:{click:function(a){return t.handleTagClick(e,i,"animate")},close:function(a){return t.handleTagClose(e,i,"animate")}}},[t._v(" "+t._s(e.name)+" ")])})),1):a("el-col",{attrs:{span:24}},[a("div",{staticClass:"el-input el-input--mini el-input-group"},[a("el-popover",{attrs:{placement:"top",width:"400",trigger:"manual"},model:{value:t.eventForm[t.currentEventTab].selectedAnimatesState,callback:function(e){t.$set(t.eventForm[t.currentEventTab],"selectedAnimatesState",e)},expression:"eventForm[currentEventTab].selectedAnimatesState"}},[a("div",[a("div",{staticClass:"space-between el-icon"},[a("span",[t._v(" ")]),a("i",{staticClass:"el-icon-close cursor-btn",on:{click:function(e){e.stopPropagation(),t.eventForm[t.currentEventTab].selectedAnimatesState=!t.eventForm[t.currentEventTab].selectedAnimatesState}}})])]),t._l(t.eventForm[t.currentEventTab].selectedAnimates[i],(function(e){return a("el-tag",{key:e.id,staticClass:"margin-right-5 margin-bottom-5 cursor-btn",attrs:{color:"rgba(255,255,255,0.1)",size:"small",effect:"dark",type:"info","disable-transitions":!0,closable:""},on:{click:function(a){return t.handleTagClick(e,i,"animate")},close:function(a){return t.handleTagClose(e,i,"animate")}}},[t._v(" "+t._s(e.name)+" ")])})),a("div",{staticClass:"el-input__inner cursor-btn text-center",attrs:{slot:"reference"},on:{click:function(e){t.eventForm[t.currentEventTab].selectedAnimatesState=!t.eventForm[t.currentEventTab].selectedAnimatesState}},slot:"reference"},[t._v(" "+t._s(t.$t("formRelational.trigger.message2",{num:t.eventForm[t.currentEventTab].selectedAnimates[i].length}))+" ")])],2)],1)])],1):t._e(),"custom"==t.eventForm[t.currentEventTab].behaviorList[0]?a("div",{staticClass:"model-api"},[a("div",{staticClass:"tab-top-bar datas-top-bar"},[a("div",{staticClass:"left-title"},[a("span",[t._v(" "+t._s(t.$t("featureSetting.advanced.name"))+" ")])])]),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("Editor",{attrs:{theme:"hc-black",language:"javascript",codes:t.eventForm[t.currentEventTab].jsCodes},on:{onCodeChange:t.htmlOnCodeChange}})],1)],1)],1):t._e()],1)])],1)})),"custom"!=t.eventForm[t.currentEventTab].behaviorList[0]&&t.eventForm[t.currentEventTab].behaviorList.length<t.behaviorOptionLen-1?a("div",{staticClass:"add-behavior-btn cursor-btn",on:{click:t.addCurrentBehaviorType}},[a("i",{staticClass:"el-icon-plus"}),t._v(" "+t._s(t.$t("formRelational.trigger.label5"))+" ")]):t._e()],2):t._e()]:t._e(),"custom"==t.contentType?[a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("div",{staticClass:"popup-item-center items-center",class:{disable:t.eventForm.length>0},on:{click:function(e){return t.addObjectCustomEvent()}}},[a("span",{staticClass:"margin-left-5"},[t._v(" "+t._s(t.$t("featureSetting.event.anchorPoint.label"))+" ")]),a("CommonSVG",{attrs:{"icon-class":"add_feature",size:12}})],1)])]),a("div",{staticClass:"after-lineX"}),t.eventForm.length>0?a("div",{staticClass:"anchor-list-div h100"},[a("div",[a("div",{staticClass:"items-center"},[a("div",{ref:"eventForm0",staticClass:"popup-item-center items-center",on:{click:function(e){return t.togglePanel(t.currentEventTab)}}},[a("span",{staticClass:"margin-left-5 margin-right-5"},[t._v(" "+t._s(t.$t("featureSetting.event.anchorPoint.label4"))+" ")]),a("CommonSVG",{attrs:{"icon-class":"notation_feature",size:12}})],1),a("el-tooltip",{attrs:{effect:"dark",content:t.$t("menuIconName.delete"),placement:"top"}},[a("i",{staticClass:"el-icon el-icon-delete cursor-btn",on:{click:function(e){return t.deleteObjectEvent(t.currentEventTab)}}})])],1)])]):t._e(),t.eventForm.length>0&&t.popupState?a("div",{staticClass:"regional-coordinates-container",style:{left:t.popupLeft+"px"}},[a("div",{staticClass:"title"},[a("span",[t._v(" "+t._s(t.$t("featureSetting.event.anchorPoint.label1"))+" ")]),a("i",{staticClass:"el-icon-close cursor-btn",on:{click:function(e){return t.togglePanel(t.currentEventTab)}}})]),a("div",{staticClass:"margin-bottom-5 custom-tabs"},[a("span",[t._v("锚点JavaScript")]),a("span",{staticClass:"cursor-btn anchor-example-tips",on:{click:function(e){return t.copyAnchorExampleTips()}}},[t._v(" "+t._s(t.$t("featureSetting.style.anchorPoint.label2"))+" ")])]),a("Editor",{attrs:{theme:"hc-black",language:"javascript",codes:t.eventForm[t.currentEventTab].jsCodes},on:{onCodeChange:t.htmlOnCodeChange}})],1):t._e()]:t._e()],2)},x=[],_=a("b85c"),T=a("2909"),k=(a("99af"),a("c740"),a("095c")),D={name:"AnchorPointSetting",components:{Editor:k["a"],CommonSVG:function(){return a.e("chunk-51f90655").then(a.bind(null,"17d0"))}},props:["currentElement","tabActive"],data:function(){return{contentType:"",currentEventTab:0,currentSelectedEvent:{index:[],value:""},eventsOptions:[{value:"click",label:this.$t("featureSetting.style.anchorPoint.label13"),disabled:!1},{value:"dblclick",label:this.$t("featureSetting.event.anchorPoint.label5"),disabled:!1},{value:"mouseover",label:this.$t("featureSetting.style.anchorPoint.label12"),disabled:!1},{value:"contextmenu",label:this.$t("featureSetting.event.anchorPoint.label6"),disabled:!1}],behaviorOption:[{label:"",options:[{title:this.$t("featureSetting.style.anchorPoint.label18"),type:"custom"}]}],behaviorOptionLen:0,objectsOption:[{value:"element",label:this.$t("formRelational.element.label"),disabled:!1},{value:"feature",label:this.$t("formRelational.feature.label"),disabled:!1},{value:"animation",label:this.$t("formRelational.animate.label"),disabled:!0}],animationOption:[],modelApiTips:[],eventForm:[],popupLeft:0,popupMaxHeight:100,popupState:!1}},created:function(){var t=this,e=window.scene.features.get(this.currentElement.id);e.data.setType?(this.initEditEvents(),this.contentType=e.data.setType.type):this.contentType=this.currentElement.checkedType,this.behaviorOption=[].concat(Object(T["a"])(this.behaviorOption),Object(T["a"])(this.$store.state.anchorEvents.eventSetUniversalApi)),this.behaviorOption.forEach((function(e){t.behaviorOptionLen+=e.options.length}));var a=this.$store.getters.allAnimations;a.length>0&&a.forEach((function(e){var a=[],i="";for(var s in e)a.push(e[s].animationName),i=e[s].groupName;t.animationOption.push({value:a[0],label:i,resultIds:a,disabled:!1})}))},mounted:function(){this.setPopupMaxHeight(),window.addEventListener("resize",this.setPopupMaxHeight)},watch:{currentEventTab:function(t,e){this.eventForm[e]&&(this.eventForm[e].getElementsTagsState&&this.elementPickFinishedOff(e),this.eventForm[e].getFeaturesTagsState&&this.featurePickFinishedOff(e))},tabActive:function(t){"sceneEventSet"!=t&&this.eventForm.length>0&&(this.eventForm[this.currentEventTab].getElementsTagsState&&this.elementPickFinishedOff(this.currentEventTab),this.eventForm[this.currentEventTab].getFeaturesTagsState&&this.featurePickFinishedOff(this.currentEventTab))}},methods:{copyAnchorExampleTips:function(){var t='annotation.addEventListener("click",function(e) {alert("Success!")});',e=document.createElement("textarea");e.value=t,e.setAttribute("readonly",""),e.style.position="absolute",e.style.left="-9999px",document.body.appendChild(e);var a=document.getSelection().rangeCount>0&&document.getSelection().getRangeAt(0);e.select(),document.execCommand("copy"),document.body.removeChild(e),this.$notify({title:"",message:this.$t("messageTips.copySuccess1"),type:"success"}),a&&(document.getSelection().removeAllRanges(),document.getSelection().addRange(a))},selectAnimateOption:function(t,e){var a=this.animationOption.find((function(e){return e.value==t}));this.eventForm[this.currentEventTab].selectedAnimates[e].push({id:a.value,name:a.label,resultIds:a.resultIds}),this.eventForm[this.currentEventTab].selectedAnimateDisable.push(t)},handleTagClick:function(t,e,a){switch(a){case"element":var i=window.scene.findObject(t.id);i.selected=!0;break;case"feature":window.scene.fit2Feature(t.id);break}window.scene.render()},handleTagClose:function(t,e,a){var i="";switch(a){case"element":i="selectedElements";var s=window.scene.findObject(t.id);s.selected=!1;break;case"feature":i="selectedFeatures",window.scene.features.get(t.id).clearAABB();break;case"animate":i="selectedAnimates";var n=this.eventForm[this.currentEventTab].selectedAnimateDisable.findIndex((function(e){return e==t.id}));this.eventForm[this.currentEventTab].selectedAnimateDisable.splice(n,1);break}window.scene.render();var r=this.eventForm[this.currentEventTab][i][e].findIndex((function(e){return e.id===t.id}));this.eventForm[this.currentEventTab][i][e].splice(r,1)},getFeaturesTags:function(t){this.eventForm[this.currentEventTab].getFeaturesTagsState&&(window.scene.mv.events.featurePicked.off("default",this.getSelectedFeatures),this.eventForm[this.currentEventTab].getFeaturesTagsState=!1,window.scene.config.highlight=!0),t!=this.eventForm[this.currentEventTab].selectedFeatureIndex?(this.eventForm[this.currentEventTab].selectedFeatureIndex=t,window.scene.mv.events.featurePicked.on("default",this.getSelectedFeatures),this.eventForm[this.currentEventTab].getFeaturesTagsState=!0,window.scene.config.highlight=!1,this.eventForm[this.currentEventTab].getElementsTagsState&&this.elementPickFinishedOff(this.currentEventTab)):this.eventForm[this.currentEventTab].selectedFeatureIndex=-1},getSelectedFeatures:function(t){var e=this.eventForm[this.currentEventTab].selectedFeatureIndex,a=this.eventForm[this.currentEventTab].selectedFeatures[e].findIndex((function(e){return e.id===t.id}));a<0&&this.eventForm[this.currentEventTab].selectedFeatures[e].push({name:t.name,id:t.id})},getElementsTags:function(t){var e,a=!1,i=Object(_["a"])(window.scene.features);try{for(i.s();!(e=i.n()).done;){var s=e.value;if("model"==s[1].type){a=!0;break}}}catch(n){i.e(n)}finally{i.f()}if(!a)return this.$message.error(this.$t("messageTips.addOneModel")),!1;this.eventForm[this.currentEventTab].getElementsTagsState&&(window.scene.mv.events.pickFinished.off("default",this.getSelectedObjects),this.eventForm[this.currentEventTab].getElementsTagsState=!1),t!=this.eventForm[this.currentEventTab].selectedElementIndex?(this.eventForm[this.currentEventTab].selectedElementIndex=t,window.scene.mv.events.pickFinished.on("default",this.getSelectedObjects),this.eventForm[this.currentEventTab].getElementsTagsState=!0,this.eventForm[this.currentEventTab].getFeaturesTagsState&&this.featurePickFinishedOff(this.currentEventTab)):this.eventForm[this.currentEventTab].selectedElementIndex=-1},getSelectedObjects:function(t){var e=window.scene.findObject(t),a=this.eventForm[this.currentEventTab].selectedElementIndex,i=this.eventForm[this.currentEventTab].selectedElements[a].findIndex((function(e){return e.id===t}));i<0&&this.eventForm[this.currentEventTab].selectedElements[a].push({name:e.name,id:e.id})},initEditEvents:function(){var t=this,e=window.scene.features.get(this.currentElement.id);if(e.data.setType&&e.data.setType.eventsList){var a=e.data.setType.eventsList;a.length>0&&a.forEach((function(e){t.addObjectEvent(e)}))}},setInteractiveOptions:function(){this.eventsOptions=[],this.behaviorOption=[];var t=[{value:"click",label:this.$t("featureSetting.style.anchorPoint.label13"),disabled:!1},{value:"dblclick",label:"左键双击",disabled:!1},{value:"mouseover",label:this.$t("featureSetting.style.anchorPoint.label12"),disabled:!1},{value:"contextmenu",label:"右键单击",disabled:!1}],e=[{value:"default",label:this.$t("others.default")},{value:"model",label:"与模型交互"}];this.eventsOptions=t,this.behaviorOption=e},switchListState:function(t){this.$set(this.eventForm[this.currentEventTab].behaviorListState,t,!this.eventForm[this.currentEventTab].behaviorListState[t])},addObjectEvent:function(t){var e=this;if(t)this.eventForm.push({name:t.name,eventsValue:t.eventsValue,behaviorList:t.behaviorList,objectsList:t.objectsList,animateList:t.animateList,selectedAnimates:t.selectedAnimates,selectedAnimateDisable:t.selectedAnimateDisable,selectedAnimatesState:!1,behaviorListState:t.behaviorListState,getElementsTagsState:!1,selectedElementIndex:-1,selectedElements:t.selectedElements,selectedElementsState:!1,getFeaturesTagsState:!1,selectedFeatureIndex:-1,selectedFeatures:t.selectedFeatures,selectedFeaturesState:!1,jsCodes:t.jsCodes});else{if(window.scene.clearSelection(),window.scene.render(),this.popupState=!0,this.eventForm.length>=this.eventsOptions.length)return;this.eventForm.length>0?this.currentEventTab=this.eventForm.length:this.currentEventTab=0,this.eventForm.push({name:"",eventsValue:"",behaviorList:[""],objectsList:[],animateList:[],selectedAnimates:[[]],selectedAnimateDisable:[],selectedAnimatesState:!1,behaviorListState:[!0],getElementsTagsState:!1,selectedElementIndex:-1,selectedElements:[[]],selectedElementsState:!1,getFeaturesTagsState:!1,selectedFeatureIndex:-1,selectedFeatures:[[]],selectedFeaturesState:!1,jsCodes:""}),this.setEventsOptionsDisabled(),this.$nextTick((function(){e.popupLeft=e.$refs["eventForm"+e.currentEventTab][0].offsetLeft}))}},addObjectCustomEvent:function(){var t=this;this.eventForm.length>0||(this.eventForm.push({name:"",eventsValue:"",behaviorList:[""],objectsList:[],animateList:[],selectedAnimates:[[]],selectedAnimateDisable:[],selectedAnimatesState:!1,behaviorListState:[!0],getElementsTagsState:!1,selectedElementIndex:-1,selectedElements:[[]],selectedElementsState:!1,getFeaturesTagsState:!1,selectedFeatureIndex:-1,selectedFeatures:[[]],selectedFeaturesState:!1,jsCodes:""}),this.$nextTick((function(){t.popupLeft=t.$refs["eventForm0"].offsetLeft})))},deleteObjectEvent:function(t){var e=this;this.popupState&&this.togglePanel(this.currentEventTab),this.$nextTick((function(){e.eventForm.splice(t,1),delete e.currentSelectedEvent.index[t],e.$set(e.eventsOptions[t],"disabled",!1),e.currentEventTab=0}))},selectObjectEvent:function(t,e){var a=this.eventsOptions.findIndex((function(e){return e.value==t}));this.currentSelectedEvent.index[this.currentEventTab]=a,this.eventForm[this.currentEventTab].name=this.eventsOptions[a].label,this.setEventsOptionsDisabled()},getEventsValue:function(t){},getBehaviorValue:function(t,e,a){var i=this;if("custom"==t&&(this.eventForm[this.currentEventTab].behaviorList=["custom"],this.eventForm[this.currentEventTab].behaviorListState=[!0],this.eventForm[this.currentEventTab].name="",this.eventForm[this.currentEventTab].eventsValue=""),this.$set(this.eventForm[this.currentEventTab].behaviorList,a,t),void 0!=this.eventForm[this.currentEventTab].objectsList[a]&&this.eventForm[this.currentEventTab].objectsList[a]!=e)switch(parseInt(this.eventForm[this.currentEventTab].objectsList[a])){case 1:this.eventForm[this.currentEventTab].selectedElements[a]=[],this.eventForm[this.currentEventTab].getElementsTagsState&&this.elementPickFinishedOff(this.currentEventTab);break;case 2:this.eventForm[this.currentEventTab].selectedFeatures[a]=[],this.eventForm[this.currentEventTab].getFeaturesTagsState&&this.featurePickFinishedOff(this.currentEventTab);break;case 3:this.eventForm[this.currentEventTab].selectedAnimates[a].length>0&&this.eventForm[this.currentEventTab].selectedAnimates[a].forEach((function(t){var e=i.eventForm[i.currentEventTab].selectedAnimateDisable.findIndex((function(e){return e==t.id}));i.eventForm[i.currentEventTab].selectedAnimateDisable.splice(e,1)})),this.eventForm[this.currentEventTab].selectedAnimates[a]=[];break}this.$set(this.eventForm[this.currentEventTab].objectsList,a,e)},removeCurrentBehaviorType:function(t){var e=this;this.eventForm[this.currentEventTab].behaviorList.splice(t,1),this.eventForm[this.currentEventTab].objectsList.splice(t,1),this.eventForm[this.currentEventTab].behaviorListState.splice(t,1),this.eventForm[this.currentEventTab].selectedElements.splice(t,1),this.eventForm[this.currentEventTab].selectedFeatures.splice(t,1),this.eventForm[this.currentEventTab].animateList.splice(t,1),this.eventForm[this.currentEventTab].selectedAnimates[t].forEach((function(t){var a=e.eventForm[e.currentEventTab].selectedAnimateDisable.findIndex((function(e){return e==t.id}));-1!=a&&e.eventForm[e.currentEventTab].selectedAnimateDisable.splice(a,1)})),this.eventForm[this.currentEventTab].selectedAnimates.splice(t,1)},addCurrentBehaviorType:function(){this.eventForm[this.currentEventTab].behaviorList.push(""),this.eventForm[this.currentEventTab].behaviorListState.push(!0),this.eventForm[this.currentEventTab].selectedElements.push([]),this.eventForm[this.currentEventTab].selectedFeatures.push([]),this.eventForm[this.currentEventTab].selectedAnimates.push([])},htmlOnCodeChange:function(t,e){this.eventForm[this.currentEventTab].jsCodes=t},copyDefaultInputValue:function(t,e){if(""!=this.eventForm[this.currentEventTab].eventsValue)switch(t.type){case"zoomElement":this.setZoomElementCode();break;case"isolateElement":this.setIsolateElementCode();break;case"hideElement":this.setHideElementCode();break}else this.$message.error("请选择触发条件")},copyInputValue:function(t){t.target.select(),document.execCommand("copy",!0),this.$notify({title:"",message:this.$t("messageTips.copySuccess1"),type:"success"})},setEventListener:function(t,e){var a="";switch(e){case"click":a="annotation.addEventListener('".concat(e,"',function() {\n                                clearTimeout(clickTimeSign);\n                                clickTimeSign = setTimeout(function() {\n                                    ").concat(t,"\n                                }, 300)});");break;case"dblclick":a="annotation.addEventListener('".concat(e,"',function() {clearTimeout(clickTimeSign);").concat(t,"});");break;case"mouseover":a="annotation.addEventListener('".concat(e,"',function() {").concat(t,"});");break;case"contextmenu":a="annotation.addEventListener('mouseup',function(e) {if (e.button==2){".concat(t,"}});");break}return a},setZoomElementCode:function(t,e,a){var i="";if("1"==t&&e.selectedElements[a].length>0){var s=e.selectedElements[a].map((function(t){return t.id}));i="window.scene.fit(".concat(JSON.stringify(s),");")}return"2"==t&&e.selectedFeatures[a].length>0&&(i="window.scene.fit2Feature(".concat(JSON.stringify(e.selectedFeatures[a][0].id),");")),i},setIsolateElementCode:function(t,e,a){var i="";if("1"==t){var s=e.selectedElements[a].map((function(t){return t.id}));i="window.scene.execute('isolate',{\n       objectIDs: ".concat(JSON.stringify(s),"\n    });\n    window.scene.clearSelection();")}return i},setHideElementCode:function(t,e,a){var i="";if("1"==t&&e.selectedElements[a].length>0){var s=e.selectedElements[a].map((function(t){return t.id}));i="window.scene.execute('hide',{\n       objectIDs: ".concat(JSON.stringify(s),"\n    });\n    window.scene.clearSelection();")}if("2"==t&&e.selectedFeatures[a].length>0){var n=e.selectedFeatures[a].map((function(t){return t.id}));i="window.scene.execute('hide',{\n       featureIDs: ".concat(JSON.stringify(n),"\n    });")}return i},setShowElementCode:function(t,e,a){var i="";if("1"==t&&e.selectedElements[a].length>0){var s=e.selectedElements[a].map((function(t){return t.id}));i="window.scene.execute('show',{\n       objectIDs: ".concat(JSON.stringify(s),"\n    });\n    window.scene.clearSelection();")}if("2"==t&&e.selectedFeatures[a].length>0){var n=e.selectedFeatures[a].map((function(t){return t.id}));i="window.scene.execute('show',{\n       featureIDs: ".concat(JSON.stringify(n),"\n    });")}return i},setAnimationCode:function(t,e,a){var i="";if(e.selectedAnimates[a].length>0){var s=e.selectedAnimates[a].reduce((function(t,e){return t.resultIds.concat(e.resultIds)})),n="";switch(t){case"animate_play":n="play";break;case"animate_pause":n="pause";break;case"animate_stop":n="stop";break}i="\nwindow.scene.execute('".concat(n,"',{\n      animationIDs: ").concat(JSON.stringify(s),"\n      })")}return i},detailDbclickHandle:function(t){var e=document.createElement("textarea");e.value=t,e.setAttribute("readonly",""),e.style.position="absolute",e.style.left="-9999px",document.body.appendChild(e);var a=document.getSelection().rangeCount>0&&document.getSelection().getRangeAt(0);e.select(),document.execCommand("copy"),document.body.removeChild(e),this.$notify({title:"",message:this.$t("messageTips.copySuccess1"),type:"success"}),a&&(document.getSelection().removeAllRanges(),document.getSelection().addRange(a))},submitSetting:function(t,e){var a=t.type;switch(a){case"annotation":this.addAnnotationFeatureEvent(t,e);break}},addAnnotationFeatureEvent:function(t,e){if(t.load(),this.eventForm.length>0){var a=t.data[0].div;this.eventForm.forEach((function(e){if(""!=e.jsCodes){t.markerArray[0].properties.customEvents=e.jsCodes;var i=e.jsCodes,s=new Function("annotation","".concat(i));s(a)}}))}e.dataList&&e.dataList.length>0&&this.setDataAnnotationDefaultEvent(t.data[0].id),this.$notify({title:this.$t("featureSetting.style.anchorPoint.label19"),message:this.$t("messageTips.AddedToScene"),type:"success"}),this.$emit("closeSet")},toggleContentType:function(t){"custom"==t?this.eventForm[0]={name:"",eventsValue:"",behaviorList:["custom"],jsCodes:""}:this.eventForm=[],this.contentType=t},beforeSubmitSetCode:function(){var t=this;return this.eventForm.length>0&&"custom"!=this.contentType&&this.eventForm.forEach((function(e){if("custom"!=e.behaviorList[0]){var a="";e.behaviorList.forEach((function(i,s){if(""!=i){var n=e.objectsList[s];switch(i){case"element_zoom":case"feature_zoom":a+=t.setZoomElementCode(n,e,s);break;case"element_isolate":a+=t.setIsolateElementCode(n,e,s);break;case"element_hide":case"feature_hide":a+=t.setHideElementCode(n,e,s);break;case"element_show":case"feature_show":a+=t.setShowElementCode(n,e,s);break;case"animate_play":case"animate_pause":case"animate_stop":a+=t.setAnimationCode(i,e,s);break}}})),e.jsCodes=t.setEventListener(a,e.eventsValue)}})),this.eventForm},beforeValidate:function(){if("custom"==this.contentType)return!0;var t=window.scene.getSelection().filter((function(t){return"element"==t.type}));return!(0==t.length&&this.eventForm.length>0)||(this.$message.error(this.$t("messageTips.selectLeastOneElement")),!1)},elementPickFinishedOff:function(t){window.scene.mv.events.pickFinished.off("default",this.getSelectedObjects),this.eventForm[t].selectedElementIndex=-1,this.eventForm[t].getElementsTagsState=!1},featurePickFinishedOff:function(t){window.scene.mv.events.featurePicked.off("default",this.getSelectedFeatures),this.eventForm[t].selectedFeatureIndex=-1,this.eventForm[t].getFeaturesTagsState=!1},togglePanel:function(t){this.currentEventTab==t&&this.popupState?(this.popupState=!1,window.scene.clearSelection(),window.scene.render()):(this.popupState=!0,this.setEventsOptionsDisabled()),this.eventForm[this.currentEventTab].getElementsTagsState&&this.elementPickFinishedOff(this.currentEventTab),this.eventForm[this.currentEventTab].getFeaturesTagsState&&this.featurePickFinishedOff(this.currentEventTab),this.eventForm[t].selectedElementsState&&(this.eventForm[t].selectedElementsState=!1),"custom"==this.contentType?this.popupLeft=this.$refs["eventForm"+t].offsetLeft:this.popupLeft=this.$refs["eventForm"+t][0].offsetLeft,this.currentEventTab=t},setPopupMaxHeight:function(){var t=document.body.clientHeight,e=document.querySelector(".topToolBarContainer").clientHeight,a=document.querySelector(".settings-container").clientHeight;this.popupMaxHeight=t-e-a-20},setEventsOptionsDisabled:function(){var t=this;this.eventsOptions.forEach((function(e,a){var i=t.currentSelectedEvent.index.includes(a);e.disabled=i}))}},beforeDestroy:function(){window.removeEventListener("resize",this.setPopupMaxHeight),void 0!=this.eventForm[this.currentEventTab]&&this.eventForm[this.currentEventTab].getElementsTagsState&&this.elementPickFinishedOff(this.currentEventTab),void 0!=this.eventForm[this.currentEventTab]&&this.eventForm[this.currentEventTab].getFeaturesTagsState&&this.featurePickFinishedOff(this.currentEventTab)}},$=D,A=(a("2060"),Object(p["a"])($,E,x,!1,null,"9a8f868e",null)),F=A.exports,O={name:"SceneEventsSet",props:["contentActive"],components:{AnchorPointSetting:F},data:function(){return{}},created:function(){},mounted:function(){},computed:{dragData:function(){return this.$store.state.scene.dragOverData}},methods:{getEventDatas:function(){var t=this.$refs[this.dragData.type].beforeSubmitSetCode();return t}}},P=O,L=Object(p["a"])(P,y,w,!1,null,"31c0ae30",null),I=L.exports,M=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"bottom-content"},["model"==t.dragData.type?a("ModelSetting",{ref:"model",attrs:{currentElement:t.dragData}}):t._e(),"gltf"==t.dragData.type||"fbx"==t.dragData.type?a("GltfFbxSetting",{ref:t.dragData.type,attrs:{currentElement:t.dragData}}):t._e(),"geoJSON"==t.dragData.type?a("GeoJSONSetting",{ref:"geoJSON",attrs:{currentElement:t.dragData}}):t._e(),"_3dTiles"==t.dragData.type?a("TilesSetting",{ref:"_3dTiles",attrs:{currentElement:t.dragData}}):t._e(),"annotation"==t.dragData.type||"billboard"==t.dragData.type?a("labelSetting",{ref:"annotation",attrs:{currentElement:t.dragData}}):t._e(),"radar"==t.dragData.type?a("radarSetting",{ref:"radar",attrs:{currentElement:t.dragData}}):t._e(),"shield"==t.dragData.type?a("shieldSetting",{ref:"shield",attrs:{currentElement:t.dragData}}):t._e(),"ring"==t.dragData.type?a("ringSetting",{ref:"ring",attrs:{currentElement:t.dragData}}):t._e(),"flame"==t.dragData.type?a("flameSetting",{ref:"flame",attrs:{currentElement:t.dragData}}):t._e(),"smoke"==t.dragData.type?a("smokeSetting",{ref:"smoke",attrs:{currentElement:t.dragData}}):t._e(),"ripplewall"==t.dragData.type?a("rippleSetting",{ref:"ripplewall",attrs:{currentElement:t.dragData}}):t._e(),"heatmap"==t.dragData.type?a("heatmapSetting",{ref:"heatmap",attrs:{currentElement:t.dragData}}):t._e(),"vectorextrude"==t.dragData.type?a("VectorExtrudeSetting",{ref:"vectorextrude",attrs:{currentElement:t.dragData}}):t._e(),"shp"==t.dragData.type?a("ShpSetting",{ref:"shp",attrs:{currentElement:t.dragData}}):t._e(),a("div",{staticClass:"items-center"},[a("div",{staticClass:"styleSet-list-div list-div-150 h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title min85"},[t._v(" "+t._s(t.$t("featureSetting.advanced.priority"))+" ")]),a("el-input",{attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("featureSetting.advanced.priority")},model:{value:t.priority,callback:function(e){t.priority=e},expression:"priority"}},[a("template",{slot:"suffix"},[a("el-tooltip",{staticClass:"box-item color-98A3B3",attrs:{effect:"dark",placement:"top",enterable:!1}},[a("div",{attrs:{slot:"content"},domProps:{innerHTML:t._s(t.tipPriority)},slot:"content"}),a("i",{staticClass:"outline-none el-input__icon el-icon-question"})])],1)],2)],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title min85"},[t._v(" "+t._s(t.$t("featureSetting.advanced.always"))+" ")]),a("el-checkbox",{model:{value:t.always,callback:function(e){t.always=e},expression:"always"}},[a("el-tooltip",{staticClass:"box-item color-98A3B3 text-size-12",attrs:{effect:"dark",placement:"top",enterable:!1}},[a("div",{attrs:{slot:"content"},domProps:{innerHTML:t._s(t.$t("featureSetting.advanced.tooltip1"))},slot:"content"}),a("i",{staticClass:"outline-none el-input__icon el-icon-question"})])],1)],1)]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title min85"},[t._v(" "+t._s(t.$t("featureSetting.advanced.predefineCameraInfo.label"))+" "),a("el-tooltip",{staticClass:"box-item",attrs:{enterable:!1,effect:"dark",placement:"top"}},[a("div",{attrs:{slot:"content"},domProps:{innerHTML:t._s(t.$t("featureSetting.advanced.predefineCameraInfo.tooltip"))},slot:"content"}),a("i",{staticClass:"outline-none el-input__icon el-icon-question"})])],1),a("div",{staticClass:"cursor-btn camera-btn update margin-right-10",on:{click:function(e){return t.setFeatureCameraInfo()}}},[t._v(" "+t._s(t.$t("featureSetting.advanced.predefineCameraInfo.label1"))+" ")]),t.predefineCameraInfo?a("div",{staticClass:"cursor-btn camera-btn default",on:{click:function(e){return t.resetFeatureCameraInfo()}}},[t._v(" "+t._s(t.$t("featureSetting.advanced.predefineCameraInfo.label2"))+" ")]):t._e()])])])],1)},R=[],j=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex"},[a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("el-checkbox",{on:{change:function(e){return t.advancedSetting(e,0)}},model:{value:t.elementDatas.list[0].setOptions[0].state,callback:function(e){t.$set(t.elementDatas.list[0].setOptions[0],"state",e)},expression:"elementDatas.list[0].setOptions[0].state"}},[a("span",{staticClass:"color-98A3B3 text-size-12"},[t._v(" "+t._s(t.elementDatas.list[0].setOptions[0].title)+" ")])])],1),a("div",{staticClass:"items-center"},[a("el-checkbox",{on:{change:function(e){return t.advancedSetting(e,1)}},model:{value:t.elementDatas.list[0].setOptions[1].state,callback:function(e){t.$set(t.elementDatas.list[0].setOptions[1],"state",e)},expression:"elementDatas.list[0].setOptions[1].state"}},[a("span",{staticClass:"color-98A3B3 text-size-12"},[t._v(" "+t._s(t.elementDatas.list[0].setOptions[1].title)+" ")])])],1)]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title text-center"},[t._v(" 投影修正 "),a("el-tooltip",{staticClass:"box-item",attrs:{enterable:!1,effect:"dark",placement:"top"}},[a("div",{attrs:{slot:"content"},domProps:{innerHTML:t._s(t.$t("featureSetting.advanced.model.tooltip"))},slot:"content"}),a("i",{staticClass:"outline-none el-input__icon el-icon-question"})])],1)])]),a("div",{staticClass:"styleSet-list-div h100"},[a("el-select",{attrs:{clearable:"",size:"mini",placeholder:"请选择坐标系"},on:{change:function(e){return t.setCorrection(e,"coordinateSystem")}},model:{value:t.elementDatas.list[1].value,callback:function(e){t.$set(t.elementDatas.list[1],"value",e)},expression:"elementDatas.list[1].value"}},t._l(t.elementDatas.list[1].setOptions,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1),a("el-input",{attrs:{size:"mini",type:"number",title:"",placeholder:"请输入中央经线坐标"},on:{input:function(e){return t.setCorrection(e,"centralMeridian")}},model:{value:t.elementDatas.list[2].value,callback:function(e){t.$set(t.elementDatas.list[2],"value",e)},expression:"elementDatas.list[2].value"}})],1),a("div",{staticClass:"after-lineX"})])},V=[],z={name:"ModelSetting",props:["currentElement"],data:function(){return{itemGutter:10,elementDatas:{list:[{title:this.$t("featureSetting.advanced.model.label"),setOptions:[{title:this.$t("featureSetting.advanced.model.label3"),state:!1,attr:"part"},{title:this.$t("featureSetting.advanced.model.label4"),state:!0,attr:"texture"}],optionState:!0},{title:this.$t("featureSetting.advanced.model.label5"),setOptions:[{label:"cgcs2000",value:"cgcs2000"},{label:"xian1980",value:"xian1980"}],value:""},{title:this.$t("featureSetting.advanced.model.label6"),value:""}]},timeout:null}},created:function(){this.init()},methods:{setCorrection:function(t,e){var a=this;null!=this.timeout&&clearTimeout(this.timeout),this.timeout=setTimeout((function(){var i={coordinateSystem:"",centralMeridian:0,effectBpt:!0},s=window.scene.features.get(a.currentElement.id);"coordinateSystem"==e&&""!=t&&""==a.elementDatas.list[2].value&&(a.elementDatas.list[2].value=s.origin[0]),""!=a.elementDatas.list[1].value?(i.coordinateSystem=a.elementDatas.list[1].value,""!=a.elementDatas.list[2].value&&(i.centralMeridian=parseFloat(a.elementDatas.list[2].value+"")),s.correction=i):s.correction=void 0}),800)},init:function(){var t=this;this.elementDatas.list[0].setOptions.forEach((function(e,a){t.$set(t.elementDatas.list[0].setOptions[a],"state",t.currentElement[e.attr])}));var e=window.scene.features.get(this.currentElement.id);e.correction&&(e.correction.coordinateSystem&&""!=e.correction.coordinateSystem&&(this.elementDatas.list[1].value=e.correction.coordinateSystem),e.correction.centralMeridian&&""!=e.correction.centralMeridian&&(this.elementDatas.list[2].value=e.correction.centralMeridian))},advancedSetting:function(t,e){var a=window.scene.features.get(this.currentElement.id);a[this.elementDatas.list[0].setOptions[e].attr]=t,window.scene.render()}}},N=z,B=(a("f4af"),Object(p["a"])(N,j,V,!1,null,"6278c8f1",null)),J=B.exports,G=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex"},[a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("formRelational.animate.play"))+" ")]),a("el-checkbox",{attrs:{size:"small"},on:{change:t.inputLinkage},model:{value:t.elementDatas.list[1].value,callback:function(e){t.$set(t.elementDatas.list[1],"value",e)},expression:"elementDatas.list[1].value"}})],1)]),t.elementDatas.list[2].optionState?[a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("formRelational.address.label"))+" ")]),a("el-input",{attrs:{size:"mini",placeholder:t.$t("formRelational.address.placeholder")},model:{value:t.elementDatas.list[2].value,callback:function(e){t.$set(t.elementDatas.list[2],"value",e)},expression:"elementDatas.list[2].value"}})],1)])]:t._e(),a("div",{staticClass:"after-lineX"}),a("FollowSettingItem",{attrs:{currentElement:t.currentElement}}),a("div",{staticClass:"after-lineX"})],2)},H=[],U=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"popup-box h100 relative"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.follow.label")))]),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("el-radio",{staticClass:"color-98A3B3",attrs:{label:"feature"},on:{input:t.followRadioChange},model:{value:t.followType,callback:function(e){t.followType=e},expression:"followType"}},[t._v("元素")]),a("el-radio",{staticClass:"color-98A3B3",attrs:{label:"object"},on:{input:t.followRadioChange},model:{value:t.followType,callback:function(e){t.followType=e},expression:"followType"}},[t._v("构件")])],1),"feature"==t.followType?a("div",{staticClass:"popup-item-center items-center",on:{click:t.togglePanel}},[a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",enterable:!1,content:t.elementName||t.$t("formRelational.follow.tooltip"),placement:"top"}},[a("span",{staticClass:"margin-left-5 follow-name"},[t._v(" "+t._s(t.elementName||t.$t("formRelational.follow.tooltip"))+" ")])]),a("CommonSVG",{attrs:{"icon-class":"object",size:16}})],1):[""!=t.selectedElement.id?a("div",{staticClass:"popup-item-center items-center"},[a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",enterable:!1,content:t.selectedElement.name,placement:"top"}},[a("span",{staticClass:"margin-left-5 follow-name"},[t._v(" "+t._s(t.selectedElement.name)+" ")])]),a("i",{staticClass:"el-icon-close",on:{click:function(e){return e.stopPropagation(),t.clearSelectedElement.apply(null,arguments)}}})],1):a("div",{staticClass:"popup-item-center items-center",on:{click:function(e){return t.clickGetElement()}}},[a("span",{staticClass:"margin-left-5"},[t._v(" "+t._s(t.pickFinished?t.$t("formRelational.trigger.label1"):t.$t("formRelational.trigger.label2"))+" ")]),a("CommonSVG",{attrs:{"icon-class":"select",size:16}})],1)]],2),t.visibleFollow&&"feature"==t.followType?a("ElementFollow",{attrs:{currentElement:t.currentElement},on:{close:function(e){t.visibleFollow=!1},submitData:t.submitData}}):t._e()],1)},X=[],q=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"element-follow-container"},[a("div",{staticClass:"title"},[a("span",[t._v(" "+t._s(t.$t("formRelational.follow.label"))+" ")]),a("div",[a("i",{staticClass:"el-icon-close cursor-btn",on:{click:t.togglePanel}})])]),a("div",{staticClass:"content"},t._l(t.listData,(function(e,i){return a("div",{key:i},[a("div",{staticClass:"type-title"},[t._v(t._s(e.name))]),a("div",{staticClass:"item-box"},t._l(e.list,(function(e,i){return a("div",{key:i,staticClass:"list-item cursor-btn",class:[{active:(t.currentElement.follow||(t.selectItem||{}).id||"")===e.id}],on:{click:function(a){return t.selectData(e)}}},[t._v(" "+t._s(e.name)+" "),a("span",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}]},[t._v(t._s((t.selectItem||{}).id))])])})),0)])})),0),a("div",{staticClass:"btn-box"},[a("span",{staticClass:"btn",on:{click:function(e){return t.submitFollow()}}},[t._v(" "+t._s(t.$t("menuIconName.confirm"))+" ")])])])},Q=[],K=(a("bf19"),a("009f")),W={name:"elementFollow",props:["currentElement"],data:function(){return{listData:[],sceneManageTreeDatas:K["a"],selectItem:{}}},mounted:function(){var t=this,e=["underlay","skybox","dem","_3dBuilding"],a={},i=JSON.parse(window.scene.toJSON());for(var s in i.features.forEach((function(i){var s=JSON.parse(i);s.id!==t.currentElement.id&&(e.includes(s.type)||(a[s.type]||(a[s.type]=[]),a[s.type].push(s)))})),a){var n=a[s];this.listData.push({name:K["a"][s].name,list:n})}this.selectItem.id=window.scene.features.get(this.currentElement.id).follow},methods:{togglePanel:function(){this.$emit("close")},selectData:function(t){this.selectItem.id===t.id?this.$set(this,"selectItem",{}):this.$set(this,"selectItem",t),this.currentElement.follow=(this.selectItem||{}).id||"",this.currentElement.followType="feature"},submitFollow:function(){var t=window.scene.features.get(this.currentElement.id);(K["a"][this.selectItem.type]||{}).name;t.follow=this.selectItem.id,t.followType="feature",this.$emit("close"),this.$emit("submitData",this.selectItem),this.$nextTick((function(){window.scene.render()}))}}},Y=W,Z=(a("ef3e"),Object(p["a"])(Y,q,Q,!1,null,"1831677d",null)),tt=Z.exports,et={name:"FollowSettingItem",props:["currentElement"],components:{ElementFollow:tt,CommonSVG:function(){return a.e("chunk-51f90655").then(a.bind(null,"17d0"))}},data:function(){return{visibleFollow:!1,sceneManageTreeDatas:K["a"],selectData:{name:"",id:""},elementName:"",followType:"feature",pickFinished:!1,selectedElement:{name:"",id:""}}},created:function(){var t=window.scene.features.get(this.currentElement.id);if(t.follow&&""!=t.follow){if("feature"==t.followType){var e=window.scene.features.get(t.follow);e&&(this.elementName=e.name,this.selectData.name=e.name,this.selectData.id=t.follow,this.followType="feature")}if("object"==t.followType){var a=window.scene.findObject(t.follow);a&&(this.selectedElement.name=a.name,this.selectedElement.id=t.follow,this.followType="object")}}},methods:{submitData:function(t){this.selectData=t;var e=window.scene.features,a=e.get(this.currentElement.follow)||{};this.elementName=this.selectData.name||(this.sceneManageTreeDatas[a.type]||{}).name||""},togglePanel:function(){this.visibleFollow=!this.visibleFollow},followRadioChange:function(t){var e=window.scene.features.get(this.currentElement.id);"feature"==t&&this.selectData.id&&(e.follow=this.selectData.id,e.followType="feature",this.pickFinished&&(window.scene.mv.events.pickFinished.off("default",this.getSelectedElement),this.pickFinished=!1)),"object"==t&&this.selectedElement.id&&(e.follow=this.selectedElement.id,e.followType="object")},clickGetElement:function(){this.pickFinished?window.scene.mv.events.pickFinished.off("default",this.getSelectedElement):window.scene.mv.events.pickFinished.on("default",this.getSelectedElement),this.pickFinished=!this.pickFinished},getSelectedElement:function(t){if(""!=t){var e=window.scene.findObject(t);this.selectedElement.name=e.name,this.selectedElement.id=e.id;var a=window.scene.features.get(this.currentElement.id);a.follow=e.id,a.followType="object",window.scene.mv.events.pickFinished.off("default",this.getSelectedElement),this.pickFinished=!1,window.scene.render()}},clearSelectedElement:function(){this.selectedElement.name="",this.selectedElement.id="";var t=window.scene.features.get(this.currentElement.id);t.follow="",t.followType="feature",window.scene.render()}},beforeDestroy:function(){this.pickFinished&&window.scene.mv.events.pickFinished.off("default",this.getSelectedElement)}},at=et,it=(a("6d21"),Object(p["a"])(at,U,X,!1,null,"2b3cfab2",null)),st=it.exports,nt={name:"GltfFbxSetting",props:["currentElement"],data:function(){return{itemGutter:10,elementDatas:{id:"",longitude:0,latitude:0,altitude:0,rotation:[0,0,0],offset:[0,0,0],list:[{title:this.$t("featureSetting.advanced.gltfFbx.label"),value:1},{title:this.$t("formRelational.animate.play"),value:!0},{title:this.$t("formRelational.address.label"),value:"",optionState:!0}]}}},components:{FollowSettingItem:st},created:function(){this.init()},methods:{init:function(){var t=window.scene.features.get(this.currentElement.id);this.elementDatas.id=this.currentElement.id,this.elementDatas.list[0].value=t.data.scale,this.elementDatas.list[1].value=t.data.animation,this.elementDatas.list[2].value=t.url,t.data.defaultSource&&(this.elementDatas.list[2].optionState=!1)},inputLinkage:function(){var t=window.scene.features.get(this.currentElement.id);window.scene.postData({scale:this.elementDatas.list[0].value,animation:this.elementDatas.list[1].value},t.dataKey)}}},rt=nt,lt=(a("49d9f"),Object(p["a"])(rt,G,H,!1,null,"5cc00b45",null)),ot=lt.exports,ct=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex"},[a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("featureSetting.advanced.geoJSON.label"))+" "),a("el-tooltip",{staticClass:"box-item",attrs:{enterable:!1,effect:"dark",placement:"top"}},[a("div",{attrs:{slot:"content"},domProps:{innerHTML:t._s(t.$t("featureSetting.advanced.geoJSON.tooltip"))},slot:"content"}),a("i",{staticClass:"el-icon-question"})])],1),a("el-checkbox",{staticStyle:{"margin-left":"12px"},attrs:{size:"small"},on:{change:function(e){return t.inputGeoJSONPostData("useUniquePaint")}},model:{value:t.params.useUniquePaint,callback:function(e){t.$set(t.params,"useUniquePaint",e)},expression:"params.useUniquePaint"}})],1)]),a("div",{staticClass:"after-lineX"}),t.params.useUniquePaint?t._e():[a("div",{staticClass:"styleSet-list-div h100",staticStyle:{width:"200px"}},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.opacity.label")))]),a("div",{staticClass:"items-center w100"},[a("el-slider",{staticStyle:{width:"100%"},attrs:{min:.1,max:1,step:.1},on:{change:function(e){return t.inputGeoJSONPostData("opacity")}},model:{value:t.params.opacity,callback:function(e){t.$set(t.params,"opacity",e)},expression:"params.opacity"}}),a("span",{staticClass:"slider-num"},[t._v(t._s(t.params.opacity))])],1)]),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.color.label")))]),a("el-color-picker",{staticStyle:{"margin-right":"12px"},attrs:{"popper-class":"colorPicker","show-alpha":!1,"color-format":"rgb",size:"mini"},on:{change:function(e){return t.inputGeoJSONPostData("color")}},model:{value:t.params.color,callback:function(e){t.$set(t.params,"color",e)},expression:"params.color"}})],1)]),a("div",{staticClass:"after-lineX"}),void 0!=t.params.outlineWidth?[a("div",{staticClass:"styleSet-list-div h100 list-div-160"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.showLine.label2")))]),a("el-input",{attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("formRelational.showLine.placeholder")},on:{input:function(e){return t.inputGeoJSONPostData("outlineWidth")}},model:{value:t.params.outlineWidth,callback:function(e){t.$set(t.params,"outlineWidth",e)},expression:"params.outlineWidth"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.showLine.label1")))]),a("el-color-picker",{staticStyle:{"margin-right":"12px"},attrs:{"popper-class":"colorPicker","show-alpha":!1,"color-format":"rgb",size:"mini"},on:{change:function(e){return t.inputGeoJSONPostData("outlineColor")}},model:{value:t.params.outlineColor,callback:function(e){t.$set(t.params,"outlineColor",e)},expression:"params.outlineColor"}})],1)]),a("div",{staticClass:"after-lineX"})]:t._e(),a("div",{staticClass:"h100 geoJson-style-list"},[void 0!=t.params.textSize?a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("featureSetting.advanced.geoJSON.label1"))+" ")]),a("el-input",{attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("featureSetting.advanced.geoJSON.label1")},on:{input:function(e){return t.inputGeoJSONPostData("textSize")}},model:{value:t.params.textSize,callback:function(e){t.$set(t.params,"textSize",e)},expression:"params.textSize"}})],1):t._e(),void 0!=t.params.size?a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("featureSetting.advanced.geoJSON.label2"))+" ")]),a("el-input",{attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("featureSetting.advanced.geoJSON.label2")},on:{input:function(e){return t.inputGeoJSONPostData("size")}},model:{value:t.params.size,callback:function(e){t.$set(t.params,"size",e)},expression:"params.size"}})],1):t._e()]),void 0!=t.params.textSize||void 0!=t.params.size?a("div",{staticClass:"after-lineX"}):t._e(),void 0!=t.params.lineWidth?a("div",{staticClass:"h100 geoJson-style-list"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.lineWidth.label")))]),a("el-input",{attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("formRelational.lineWidth.placeholder")},on:{input:function(e){return t.inputGeoJSONPostData("lineWidth")}},model:{value:t.params.lineWidth,callback:function(e){t.$set(t.params,"lineWidth",e)},expression:"params.lineWidth"}})],1)]):t._e(),void 0!=t.params.lineWidth?a("div",{staticClass:"after-lineX"}):t._e()]],2)},ut=[],dt={name:"GeoJSONSetting",props:["currentElement"],data:function(){return{params:{useUniquePaint:!0,color:"rgb(255, 50, 0)",opacity:1}}},created:function(){this.init()},methods:{init:function(){var t=window.scene.features.get(this.currentElement.id),e=JSON.parse(t.data.originalData);this.params=e},inputGeoJSONPostData:function(t){var e=window.scene.features.get(this.currentElement.id),a={useUniquePaint:this.params.useUniquePaint},i=this.params[t];"color"!=t&&"outlineColor"!=t&&"useUniquePaint"!=t&&(i=parseFloat(i+"")),this.params.useUniquePaint?(a.color="",a.opacity=1):a[t]=i,window.scene.postData(a,e.dataKey)}}},mt=dt,pt=(a("f4d3"),Object(p["a"])(mt,ct,ut,!1,null,"55773c24",null)),ht=pt.exports,vt=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex"},[a("div",{staticClass:"styleSet-list-div h100 sort-width"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title margin-right-5"},[t._v(" "+t._s(t.$t("featureSetting.advanced._3dTiles.label"))+" ")]),a("el-input",{attrs:{min:"0",size:"mini",type:"number",title:"",placeholder:t.$t("featureSetting.advanced._3dTiles.label")},on:{input:function(e){return t.inputChangeTilesPostData("errorThreshold")}},model:{value:t.params.errorThreshold,callback:function(e){t.$set(t.params,"errorThreshold",e)},expression:"params.errorThreshold"}},[a("template",{slot:"suffix"},[a("el-tooltip",{staticClass:"box-item",attrs:{enterable:!1,effect:"dark",placement:"top"}},[a("div",{attrs:{slot:"content"},domProps:{innerHTML:t._s(t.$t("featureSetting.advanced._3dTiles.tooltip"))},slot:"content"}),a("i",{staticClass:"outline-none el-input__icon el-icon-question"})])],1)],2)],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title margin-right-5"},[t._v(" "+t._s(t.$t("featureSetting.advanced._3dTiles.label1"))+" ")]),a("el-input",{attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("featureSetting.advanced._3dTiles.label1")},on:{input:function(e){return t.inputChangeTilesPostData("errorTarget")}},model:{value:t.params.errorTarget,callback:function(e){t.$set(t.params,"errorTarget",e)},expression:"params.errorTarget"}},[a("template",{slot:"suffix"},[a("el-tooltip",{staticClass:"box-item",attrs:{enterable:!1,effect:"dark",placement:"top"}},[a("div",{attrs:{slot:"content"},domProps:{innerHTML:t._s(t.$t("featureSetting.advanced._3dTiles.tooltip1"))},slot:"content"}),a("i",{staticClass:"outline-none el-input__icon el-icon-question"})])],1)],2)],1)]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title item-title-w80"},[t._v(" "+t._s(t.$t("featureSetting.advanced._3dTiles.label2"))+" ")]),a("el-checkbox",{attrs:{size:"small"},on:{change:function(e){return t.inputChangeTilesPostData("includePoints")}},model:{value:t.params.includePoints,callback:function(e){t.$set(t.params,"includePoints",e)},expression:"params.includePoints"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title item-title-w80"},[t._v(" "+t._s(t.$t("featureSetting.advanced._3dTiles.label5"))+" "),a("el-tooltip",{staticClass:"box-item",attrs:{enterable:!1,effect:"dark",placement:"top"}},[a("div",{attrs:{slot:"content"},domProps:{innerHTML:t._s(t.$t("featureSetting.advanced._3dTiles.tooltip3"))},slot:"content"}),a("i",{staticClass:"outline-none el-input__icon el-icon-question"})])],1),a("el-checkbox",{attrs:{size:"small"},on:{change:t.setCorrection},model:{value:t.params.effectBpt,callback:function(e){t.$set(t.params,"effectBpt",e)},expression:"params.effectBpt"}})],1)]),a("div",{staticClass:"after-lineX"}),t.params.includePoints?[a("div",{staticClass:"styleSet-list-div h100 sort-width"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("featureSetting.advanced._3dTiles.label4"))+" ")]),a("el-color-picker",{staticStyle:{"margin-right":"12px"},attrs:{"popper-class":"colorPicker","show-alpha":!1,"color-format":"rgb",size:"mini"},on:{change:function(e){return t.inputChangeTilesPostData("pntColor")}},model:{value:t.params.pntColor,callback:function(e){t.$set(t.params,"pntColor",e)},expression:"params.pntColor"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("featureSetting.advanced._3dTiles.label3"))+" ")]),a("el-input",{attrs:{size:"mini",readonly:!t.params.includePoints,type:"number",placeholder:t.$t("featureSetting.advanced._3dTiles.label3")},on:{input:function(e){return t.inputChangeTilesPostData("pntsSize")}},model:{value:t.params.pntsSize,callback:function(e){t.$set(t.params,"pntsSize",e)},expression:"params.pntsSize"}},[a("template",{slot:"suffix"},[a("el-tooltip",{staticClass:"box-item",attrs:{enterable:!1,effect:"dark",placement:"top"}},[a("div",{attrs:{slot:"content"},slot:"content"},[t._v(" "+t._s(t.$t("featureSetting.advanced._3dTiles.tooltip2"))+" ")]),a("i",{staticClass:"outline-none el-input__icon el-icon-question"})])],1)],2)],1)]),a("div",{staticClass:"after-lineX"})]:t._e()],2)},ft=[],gt=a("f7fe"),bt=a.n(gt),St={props:["currentElement"],name:"TilesSetting",data:function(){return{params:{errorTarget:15,errorThreshold:60,includePoints:!1,pntsSize:2,pntColor:"",effectBpt:!1},storage:{pntsSize:2,pntColor:""}}},created:function(){this.init(),this.inputChangeTilesPostData=bt()(this.inputChangeTilesPostData,800)},methods:{init:function(){var t=window.scene.features.get(this.currentElement.id),e=JSON.parse(JSON.stringify(t.data));this.params.errorTarget=e.errorTarget,this.params.errorThreshold=e.errorThreshold,this.params.includePoints=e.includePoints,this.params.pntsSize=e.pntsSize,this.params.pntColor=e.pntColor||"",t.correction&&(this.params.effectBpt=!0),this.storage.pntsSize=e.pntsSize,this.storage.pntColor=e.pntColor||""},inputChangeTilesPostData:function(t){var e=window.scene.features.get(this.currentElement.id),a=["errorTarget","errorThreshold","pntsSize"];if(a.includes(t)){this.params[t]<0&&(this.params[t]=0);var i=parseFloat(this.params[t]+"");this.params[t]=i}"pntsSize"!=t&&"pntColor"!=t||(this.storage[t]=this.params[t]),"includePoints"==t&&(this.params.includePoints?(this.params.pntsSize=this.storage.pntsSize,this.params.pntColor=this.storage.pntColor):(this.params.pntsSize=2,this.params.pntColor="")),window.scene.postData(this.params,e.dataKey)},setCorrection:function(t){var e=window.scene.features.get(this.currentElement.id);t?(e.correction={},e.correction.effectBpt=!1):e.correction=void 0}}},Ct=St,yt=(a("7b45"),Object(p["a"])(Ct,vt,ft,!1,null,"ee35a2ee",null)),wt=yt.exports,Et=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex"},[a("FollowSettingItem",{attrs:{currentElement:t.currentElement}}),a("div",{staticClass:"after-lineX"})],1)},xt=[],_t={name:"labelSetting",props:["currentElement"],components:{FollowSettingItem:st}},Tt=_t,kt=(a("cc86"),Object(p["a"])(Tt,Et,xt,!1,null,"23c210e7",null)),Dt=kt.exports,$t=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex"},[a("FollowSettingItem",{attrs:{currentElement:t.currentElement}}),a("div",{staticClass:"after-lineX"}),t.isVothing?[a("VothingData",{ref:"vothingData",attrs:{currentElement:t.currentElement}}),a("div",{staticClass:"after-lineX"})]:t._e()],2)},At=[],Ft=a("964f"),Ot={name:"flameSetting",props:["currentElement"],data:function(){return{}},components:{VothingData:Ft["a"],FollowSettingItem:st},computed:{isVothing:function(){return this.$store.state.menuList.isVothing}}},Pt=Ot,Lt=(a("2349"),Object(p["a"])(Pt,$t,At,!1,null,"5fec0f88",null)),It=Lt.exports,Mt=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex"},[a("FollowSettingItem",{attrs:{currentElement:t.currentElement}}),a("div",{staticClass:"after-lineX"}),t.isVothing?[a("VothingData",{ref:"vothingData",attrs:{currentElement:t.currentElement}}),a("div",{staticClass:"after-lineX"})]:t._e()],2)},Rt=[],jt={name:"flameSetting",props:["currentElement"],components:{FollowSettingItem:st,VothingData:Ft["a"]},computed:{isVothing:function(){return this.$store.state.menuList.isVothing}}},Vt=jt,zt=(a("ee33"),Object(p["a"])(Vt,Mt,Rt,!1,null,"d3ffd8f4",null)),Nt=zt.exports,Bt=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex"},[a("FollowSettingItem",{attrs:{currentElement:t.currentElement}}),a("div",{staticClass:"after-lineX"}),t.isVothing?[a("VothingData",{ref:"vothingData",attrs:{currentElement:t.currentElement}}),a("div",{staticClass:"after-lineX"})]:t._e()],2)},Jt=[],Gt={name:"flameSetting",props:["currentElement"],components:{FollowSettingItem:st,VothingData:Ft["a"]},computed:{isVothing:function(){return this.$store.state.menuList.isVothing}}},Ht=Gt,Ut=(a("939a"),Object(p["a"])(Ht,Bt,Jt,!1,null,"4ae42157",null)),Xt=Ut.exports,qt=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex"},[a("FollowSettingItem",{attrs:{currentElement:t.currentElement}}),a("div",{staticClass:"after-lineX"})],1)},Qt=[],Kt={name:"flameSetting",props:["currentElement"],components:{FollowSettingItem:st}},Wt=Kt,Yt=(a("1261"),Object(p["a"])(Wt,qt,Qt,!1,null,"b5df348a",null)),Zt=Yt.exports,te=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex"},[a("FollowSettingItem",{attrs:{currentElement:t.currentElement}}),a("div",{staticClass:"after-lineX"})],1)},ee=[],ae={name:"flameSetting",props:["currentElement"],components:{FollowSettingItem:st}},ie=ae,se=(a("6bcd"),Object(p["a"])(ie,te,ee,!1,null,"0b6f9e3a",null)),ne=se.exports,re=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex"},[t.isVothing?a("VothingData",{ref:"vothingData",attrs:{currentElement:t.currentElement}}):t._e(),a("div",{staticClass:"after-lineX"})],1)},le=[],oe={name:"rippleSetting",props:["currentElement"],data:function(){return{sceneManageTreeDatas:K["a"]}},components:{VothingData:Ft["a"]},computed:{isVothing:function(){return this.$store.state.menuList.isVothing}},methods:{}},ce=oe,ue=(a("ccd4"),Object(p["a"])(ce,re,le,!1,null,"5d4e4bf6",null)),de=ue.exports,me=function(){var t=this,e=t.$createElement,a=t._self._c||e;return t.isVothing?a("div",{staticClass:"flex"},[a("VothingData",{ref:"vothingData",attrs:{currentElement:t.currentElement}}),a("div",{staticClass:"after-lineX"})],1):t._e()},pe=[],he={name:"heatmapSetting",props:["currentElement"],data:function(){return{sceneManageTreeDatas:K["a"]}},components:{VothingData:Ft["a"]},computed:{isVothing:function(){return this.$store.state.menuList.isVothing}},methods:{}},ve=he,fe=(a("d5cf"),Object(p["a"])(ve,me,pe,!1,null,"03d58b39",null)),ge=fe.exports,be=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex"},[a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("featureSetting.style.vectorExtrude.label"))+" "),a("el-tooltip",{staticClass:"box-item",attrs:{enterable:!1,effect:"dark",placement:"top"}},[a("div",{attrs:{slot:"content"},domProps:{innerHTML:t._s(t.$t("featureSetting.style.vectorExtrude.tooltip"))},slot:"content"}),a("i",{staticClass:"el-icon-question"})])],1),a("el-checkbox",{staticStyle:{"margin-left":"12px"},attrs:{size:"small"},on:{change:t.inputVectorExtrudePostData},model:{value:t.params.useUniquePaint,callback:function(e){t.$set(t.params,"useUniquePaint",e)},expression:"params.useUniquePaint"}})],1)]),a("div",{staticClass:"after-lineX"}),t.params.useUniquePaint?t._e():[a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.color.label")))]),a("el-color-picker",{staticStyle:{"margin-right":"12px"},attrs:{"popper-class":"colorPicker","show-alpha":!1,"color-format":"rgb",size:"mini"},on:{change:t.inputVectorExtrudePostData},model:{value:t.params.color,callback:function(e){t.$set(t.params,"color",e)},expression:"params.color"}})],1)]),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("featureSetting.style.vectorExtrude.label1")))]),a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.params.base,expression:"params.base"}],attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("featureSetting.style.vectorExtrude.placeholder1")},on:{input:function(e){return t.inputVectorExtrudePostData("base")}},scopedSlots:t._u([{key:"append",fn:function(){return[a("span",[t._v("m")])]},proxy:!0}],null,!1,1549734309),model:{value:t.params.base,callback:function(e){t.$set(t.params,"base",e)},expression:"params.base"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("featureSetting.style.vectorExtrude.label2")))]),a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.params.top,expression:"params.top"}],attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("featureSetting.style.vectorExtrude.placeholder2")},on:{input:function(e){return t.inputVectorExtrudePostData("top")}},scopedSlots:t._u([{key:"append",fn:function(){return[a("span",[t._v("m")])]},proxy:!0}],null,!1,1549734309),model:{value:t.params.top,callback:function(e){t.$set(t.params,"top",e)},expression:"params.top"}})],1)]),a("div",{staticClass:"after-lineX"})]],2)},Se=[],Ce={name:"VectorExtrudeSetting",props:["currentElement"],data:function(){return{params:{top:10,base:0,color:"rgb(255, 50, 0)",useUniquePaint:!0}}},created:function(){this.init(),this.inputVectorExtrudePostData=bt()(this.inputVectorExtrudePostData,800)},methods:{init:function(){var t=window.scene.features.get(this.currentElement.id),e=JSON.parse(t.data.originalData);for(var a in this.params)this.params[a]=e[a]},inputVectorExtrudePostData:function(t){var e=window.scene.features.get(this.currentElement.id);"base"!==t&&"top"!==t||(this.params[t]=parseFloat(this.params[t]+""));var a={top:this.params.top,base:this.params.base,color:this.params.color,useUniquePaint:this.params.useUniquePaint};window.scene.postData(a,e.dataKey),window.scene.render()}}},ye=Ce,we=(a("2759"),Object(p["a"])(ye,be,Se,!1,null,"324f03de",null)),Ee=we.exports,xe=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex"},[a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("featureSetting.style.vectorExtrude.label"))+" "),a("el-tooltip",{staticClass:"box-item",attrs:{enterable:!1,effect:"dark",placement:"top"}},[a("div",{attrs:{slot:"content"},domProps:{innerHTML:t._s(t.$t("featureSetting.advanced.shp.tooltip"))},slot:"content"}),a("i",{staticClass:"el-icon-question"})])],1),a("el-checkbox",{staticStyle:{"margin-left":"12px"},attrs:{size:"small"},on:{change:t.inputShpPostData},model:{value:t.params.useUniquePaint,callback:function(e){t.$set(t.params,"useUniquePaint",e)},expression:"params.useUniquePaint"}})],1)]),a("div",{staticClass:"after-lineX"}),t.params.useUniquePaint?t._e():[a("div",{staticClass:"styleSet-list-div h100 list-div-160"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.color.label")))]),a("el-color-picker",{staticStyle:{"margin-right":"12px"},attrs:{"popper-class":"colorPicker","show-alpha":!1,"color-format":"rgb",size:"mini"},on:{change:t.inputShpPostData},model:{value:t.params.color,callback:function(e){t.$set(t.params,"color",e)},expression:"params.color"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.lineWidth.label")))]),a("el-input",{attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("formRelational.lineWidth.placeholder")},on:{input:function(e){return t.inputShpPostData("lineWidth")}},model:{value:t.params.lineWidth,callback:function(e){t.$set(t.params,"lineWidth",e)},expression:"params.lineWidth"}})],1)]),a("div",{staticClass:"after-lineX"})]],2)},_e=[],Te={name:"ShpSetting",props:["currentElement"],data:function(){return{params:{lineWidth:2,color:"rgb(255, 50, 0)",useUniquePaint:!0}}},created:function(){this.init(),this.inputShpPostData=bt()(this.inputShpPostData,800)},methods:{init:function(){var t=window.scene.features.get(this.currentElement.id);for(var e in this.params)this.params[e]=t.data[e]},inputShpPostData:function(t){var e=window.scene.features.get(this.currentElement.id);"lineWidth"===t&&(this.params[t]=parseFloat(this.params[t]+""));var a={lineWidth:this.params.lineWidth,color:this.params.color,useUniquePaint:this.params.useUniquePaint};window.scene.postData(a,e.dataKey),window.scene.render()}}},ke=Te,De=(a("40a8"),Object(p["a"])(ke,xe,_e,!1,null,"b790fc7e",null)),$e=De.exports,Ae={name:"AdvancedSet",props:["dragData"],components:{ModelSetting:J,GltfFbxSetting:ot,GeoJSONSetting:ht,TilesSetting:wt,labelSetting:Dt,radarSetting:It,shieldSetting:Nt,ringSetting:Xt,flameSetting:Zt,smokeSetting:ne,rippleSetting:de,heatmapSetting:ge,VectorExtrudeSetting:Ee,ShpSetting:$e},data:function(){return{tipPriority:this.$t("featureSetting.advanced.tooltip"),priority:0,always:!1,predefineCameraInfo:null,messageObj:null}},computed:{},created:function(){this.setFeatureParams()},methods:{beforeValidate:function(){var t=!0;if("model"==this.dragData.type){var e=this.$refs.model.elementDatas.list;if(""!=e[1].value){if(""==e[2].value)return this.$message.error(this.$t("featureSetting.advanced.model.message")),!1;if(parseFloat(e[2].value)<=-180||parseFloat(e[2].value)>=180)return this.$message.error(this.$t("featureSetting.advanced.model.message")),!1}}return t},setFeatureParams:function(){var t=window.scene.features.get(this.dragData.id);t.priority&&(this.priority=t.priority),t.always&&(this.always=t.always),t.predefineCameraInfo&&(this.predefineCameraInfo=t.predefineCameraInfo)},getAdvancedDatas:function(){var t={extra:{}};switch(this.dragData.type){case"model":case"gltf":case"fbx":t.list=this.$refs[this.dragData.type].elementDatas.list;break;case"geoJSON":case"_3dTiles":case"vectorextrude":case"shp":t.params=this.$refs[this.dragData.type].params;break}return t.extra.priority=parseFloat(this.priority+""),t.extra.always=this.always,t.extra.predefineCameraInfo=this.predefineCameraInfo,t},postRelationDatas:function(t){var e=null;switch(this.dragData.type){case"shield":case"ring":case"radar":case"ripplewall":case"heatmap":e=this.$refs[this.dragData.type],e.$refs.vothingData.relationArr=t;break}},getRelationDatas:function(){var t=null,e={};switch(this.dragData.type){case"shield":case"ring":case"radar":case"ripplewall":case"heatmap":t=this.$refs[this.dragData.type],e=t.$refs.vothingData.iotDataSelect;break}return e},setFeatureCameraInfo:function(){this.messageObj&&this.messageObj.close();var t=window.scene.getCameraInfo();this.predefineCameraInfo=JSON.parse(JSON.stringify(t)),this.messageObj=this.$message.success(this.$t("featureSetting.advanced.predefineCameraInfo.message"))},resetFeatureCameraInfo:function(){this.messageObj&&this.messageObj.close(),this.predefineCameraInfo=null,this.messageObj=this.$message(this.$t("featureSetting.advanced.predefineCameraInfo.message1"))}}},Fe=Ae,Oe=(a("9e0b"),a("f6bd"),Object(p["a"])(Fe,M,R,!1,null,"2a0bf079",null)),Pe=Oe.exports,Le=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("dialogComp",{ref:"ObjectSettingsRef",attrs:{hideHeader:!1,needClose:"true",zIndex:"4",draw:!0,right:700,drag:!0,title:t.relationData.type+t.$t("featureSetting.vothing.extend.relation.label"),icon:"icon-details",width:440,height:t.dialogHeight,type:"detailInfo",top:115},on:{close:t.closeDialog},scopedSlots:t._u([{key:"center",fn:function(){return[a("div",{staticClass:"settings-container"},[a("div",{staticClass:"bottom-content"},[[a("div",{staticClass:"tab-div tab-events tab-style"},[a("div",{staticClass:"tab-top-bar datas-top-bar"},[a("div",{staticClass:"left-title",on:{click:function(e){t.listState.data=!t.listState.data}}},[a("span",[t._v(t._s(t.$t("featureSetting.vothing.extend.relation.label1")))]),t.listState.data?a("i",{staticClass:"el-icon-caret-bottom"}):a("i",{staticClass:"el-icon-caret-top"})])]),a("el-collapse-transition",[a("div",{directives:[{name:"show",rawName:"v-show",value:t.listState.data,expression:"listState.data"}],staticClass:"datas-table"},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:t.entity,"header-row-class-name":"datas-table-title","row-class-name":"datas-table-column"}},[a("el-table-column",{attrs:{width:"42"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-radio",{attrs:{label:e.$index},on:{change:t.getData},model:{value:t.checkedRadio1,callback:function(e){t.checkedRadio1=e},expression:"checkedRadio1"}})]}}])}),a("el-table-column",{attrs:{label:t.$t("others.type"),"min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:e.row.name,placement:"top"}},[a("span",{staticClass:"custom-column"},[t._v(" "+t._s(t.$t("featureSetting.vothing.extend.relation.label2"))+" ")])])]}}])}),a("el-table-column",{attrs:{label:t.$t("others.name"),"min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:e.row.name,placement:"top"}},[a("span",{staticClass:"custom-column"},[t._v(t._s(e.row.name))])])]}}])})],1)],1)])],1),a("div",{staticClass:"tab-div tab-events tab-style"},[a("div",{staticClass:"tab-top-bar datas-top-bar"},[a("div",{staticClass:"left-title",on:{click:function(e){t.listState.attr=!t.listState.attr}}},[a("span",[t._v(" "+t._s(t.$t("dialog.attribute.table.label1"))+" ")]),t.listState.attr?a("i",{staticClass:"el-icon-caret-bottom"}):a("i",{staticClass:"el-icon-caret-top"})])]),a("el-collapse-transition",[a("div",{directives:[{name:"show",rawName:"v-show",value:t.listState.attr,expression:"listState.attr"}],staticClass:"datas-table"},[a("el-table",{staticStyle:{width:"100%"},attrs:{data:t.attrList,"header-row-class-name":"datas-table-title","row-class-name":"datas-table-column"}},[a("el-table-column",{attrs:{width:"42"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-radio",{attrs:{label:e.$index},on:{change:t.getAttr},model:{value:t.checkedRadio2,callback:function(e){t.checkedRadio2=e},expression:"checkedRadio2"}})]}}])}),a("el-table-column",{attrs:{label:t.$t("others.type"),"min-width":"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:e.row.name,placement:"top"}},[a("span",{staticClass:"custom-column"},[t._v(t._s(e.row.name))])])]}}])})],1)],1)])],1),a("div",{staticClass:"tab-div tab-events tab-style"},[a("div",{staticClass:"tab-top-bar datas-top-bar"},[a("div",{staticClass:"left-title",on:{click:function(e){t.listState.code=!t.listState.code}}},[a("span",[t._v(" "+t._s(t.$t("featureSetting.advanced.name"))+" ")]),t.listState.code?a("i",{staticClass:"el-icon-caret-bottom"}):a("i",{staticClass:"el-icon-caret-top"})])]),a("el-collapse-transition",[a("div",{directives:[{name:"show",rawName:"v-show",value:t.listState.code,expression:"listState.code"}],staticClass:"annotationCodeInput margin-bottom-8"},[a("p",{staticClass:"margin-bottom-8"},[t._v(" JS "),a("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:t.$t("featureSetting.vothing.extend.relation.tooltip",{type:t.relationData.type}),placement:"top"}},[a("span",{staticClass:"cursor-btn anchor-example-tips",on:{click:t.copyAnchorExampleTips}},[t._v(" "+t._s(t.$t("featureSetting.style.anchorPoint.label2"))+" ")])])],1),a("Editor",{attrs:{theme:"hc-black",language:"javascript",codes:t.javascriptCodes},on:{onMounted:t.javascriptOnMounted,onCodeChange:t.javascriptOnCodeChange}})],1)])],1)]],2),a("div",{staticClass:"form-button"},[a("span",{staticClass:"btn btn-cancel cursor-btn",on:{click:t.cancel}},[t._v(" "+t._s(t.$t("messageTips.cancel"))+" ")]),a("span",{staticClass:"btn btn-default cursor-btn",on:{click:t.settingSubmit}},[t._v(" "+t._s(t.$t("menuIconName.confirm"))+" ")])])])]},proxy:!0}])})},Ie=[],Me={name:"RelationSet",components:{Editor:k["a"]},data:function(){return{radio:"0",dialogHeight:600,listState:{type:!0,data:!0,attr:!1,code:!0},entity:[],entityProperty:{},attrList:[],checkedRadio1:-1,checkedRadio2:-1,jsEditor:null,javascriptCodes:""}},computed:{projectId:function(){return window.localStorage.getItem("cj-projectId")},isVothing:function(){return this.$store.state.menuList.isVothing},relationData:function(){return this.$store.state.widget.relationData}},watch:{"$store.state.widget.relationData":{deep:!0,handler:function(t){this.initData()}}},mounted:function(){this.initData()},methods:{copyAnchorExampleTips:function(){var t=document.createElement("textarea");t.value=this.$t("featureSetting.vothing.extend.relation.tooltip1"),t.setAttribute("readonly",""),t.style.position="absolute",t.style.left="-9999px",document.body.appendChild(t),t.select(),document.execCommand("copy"),document.body.removeChild(t),this.$message.success(this.$t("messageTips.copySuccess1"))},getData:function(){this.listState.attr=!0,this.attrList=this.entityProperty[this.entity[this.checkedRadio1].id],this.checkedRadio2=-1},getAttr:function(){},initData:function(){var t=this;this.entity=this.relationData.selectedContent.data.entity,this.entityProperty=this.relationData.selectedContent.data.entityProperty,this.relationData.formerData&&null!==this.relationData.formerData&&(this.entity.map((function(e,a){e.id!==t.relationData.formerData.chosedEntity.id||(t.checkedRadio1=a)})),this.attrList=this.entityProperty[this.entity[this.checkedRadio1].id],this.attrList.map((function(e,a){e.name!==t.relationData.formerData.chosedProperty.name||(t.checkedRadio2=a)})),this.listState.attr=!0,this.javascriptCodes=this.relationData.formerData.javascriptCodes)},setCustomAnchorType:function(t){},closeDialog:function(){this.$store.commit("getRelationData",{isShow:!1})},cancel:function(){this.$store.commit("getRelationData",{isShow:!1})},settingSubmit:function(){if(-1===this.checkedRadio1||-1!==this.checkedRadio2)if(""===this.javascriptCodes||-1!==this.checkedRadio1&&-1!==this.checkedRadio2){var t={isRelation:""!==JSON.stringify(this.attrList[this.checkedRadio2])||""!==this.javascriptCodes,chosedEntity:this.entity[this.checkedRadio1],chosedProperty:this.attrList[this.checkedRadio2],javascriptCodes:this.javascriptCodes,relationData:this.$store.state.widget.relationData};this.$emit("submitRelation",t),this.closeDialog()}else this.$message.warning(this.$t("featureSetting.vothing.extend.relation.message"));else this.$message.warning(this.$t("featureSetting.vothing.extend.relation.message"))},javascriptOnCodeChange:function(t){this.javascriptCodes=t},javascriptOnMounted:function(t){this.jsEditor=t}}},Re=Me,je=(a("dcd5"),a("f013"),Object(p["a"])(Re,Le,Ie,!1,null,"2da504c4",null)),Ve=je.exports,ze=a("e780"),Ne=a("af0b"),Be=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"bottom-content flex relative material-params"},[a("div",{staticClass:"styleSet-list-div h100 list-div-170"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("featureSetting.materialSetting.texture.name"))+" ")]),a("div",{staticClass:"popup-item-center items-center",on:{click:function(e){return t.toggleEditTexture()}}},[a("span",{staticClass:"margin-left-5 margin-right-10"},[t._v(" "+t._s(t.$t("featureSetting.materialSetting.texture.edit"))+" ")]),a("CommonSVG",{attrs:{size:"16",color:t.textureEdit.dialogState?"var(--theme)":"","icon-class":"notation_feature"}})],1)]),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("featureSetting.materialSetting.params.label"))+" ")]),a("div",{staticClass:"items-center w100"},[a("el-input",{attrs:{size:"mini",type:"number",title:""},on:{input:function(e){return t.inputChangeMaterialParams("polygonOffsetFactor")}},model:{value:t.params.polygonOffsetFactor,callback:function(e){t.$set(t.params,"polygonOffsetFactor",e)},expression:"params.polygonOffsetFactor"}},[a("template",{slot:"suffix"},[a("el-tooltip",{staticClass:"box-item",attrs:{enterable:!1,effect:"dark",placement:"top"}},[a("div",{attrs:{slot:"content"},domProps:{innerHTML:t._s(t.$t("featureSetting.materialSetting.params.tooltip"))},slot:"content"}),a("i",{staticClass:"outline-none el-input__icon el-icon-question"})])],1)],2)],1)])]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("featureSetting.materialSetting.params.label1"))+" "),a("el-checkbox",{staticClass:"margin-left-5",on:{change:function(e){return t.inputChangeMaterialParams("metal")}},model:{value:t.params.metal,callback:function(e){t.$set(t.params,"metal",e)},expression:"params.metal"}})],1),a("div",{staticClass:"items-center w100 relative"},[a("el-slider",{staticStyle:{width:"100%"},attrs:{min:0,max:1,step:.01,disabled:!t.params.metal},on:{change:function(e){return t.inputChangeMaterialParams("metalness")}},model:{value:t.params.metalness,callback:function(e){t.$set(t.params,"metalness",e)},expression:"params.metalness"}}),a("el-input",{staticClass:"margin-left-5 slideInput",attrs:{size:"mini",type:"number",title:"",disabled:!t.params.metal},on:{input:function(e){return t.inputChangeMaterialParams("metalness")}},model:{value:t.params.metalness,callback:function(e){t.$set(t.params,"metalness",e)},expression:"params.metalness"}})],1)]),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("featureSetting.materialSetting.params.label2"))+" ")]),a("div",{staticClass:"items-center w100"},[a("el-slider",{staticStyle:{width:"100%"},attrs:{min:0,max:1,step:.01},on:{change:function(e){return t.inputChangeMaterialParams("roughness")}},model:{value:t.params.roughness,callback:function(e){t.$set(t.params,"roughness",e)},expression:"params.roughness"}}),a("el-input",{staticClass:"margin-left-5 slideInput",attrs:{size:"mini",type:"number",title:""},on:{input:function(e){return t.inputChangeMaterialParams("roughness")}},model:{value:t.params.roughness,callback:function(e){t.$set(t.params,"roughness",e)},expression:"params.roughness"}})],1)])]),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title short-title"},[t._v(" "+t._s(t.$t("featureSetting.materialSetting.params.label3"))+" ")]),a("div",{staticClass:"items-center w100"},[a("el-slider",{staticStyle:{width:"100%"},attrs:{min:0,max:1,step:.01},on:{change:function(e){return t.inputChangeMaterialParams("reflectivity")}},model:{value:t.params.reflectivity,callback:function(e){t.$set(t.params,"reflectivity",e)},expression:"params.reflectivity"}}),a("el-input",{staticClass:"margin-left-5 slideInput",attrs:{size:"mini",type:"number",title:""},on:{input:function(e){return t.inputChangeMaterialParams("reflectivity")}},model:{value:t.params.reflectivity,callback:function(e){t.$set(t.params,"reflectivity",e)},expression:"params.reflectivity"}})],1)]),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title short-title"},[t._v(" "+t._s(t.$t("featureSetting.materialSetting.params.label4"))+" ")]),a("div",{staticClass:"items-center w100"},[a("el-slider",{staticStyle:{width:"100%"},attrs:{min:0,max:1,step:.01},on:{change:function(e){return t.inputChangeMaterialParams("emissive")}},model:{value:t.params.emissive,callback:function(e){t.$set(t.params,"emissive",e)},expression:"params.emissive"}}),a("el-input",{staticClass:"margin-left-5 slideInput",attrs:{size:"mini",type:"number",title:""},on:{input:function(e){return t.inputChangeMaterialParams("emissive")}},model:{value:t.params.emissive,callback:function(e){t.$set(t.params,"emissive",e)},expression:"params.emissive"}})],1)])]),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("formRelational.color.label"))+" ")]),a("el-color-picker",{staticStyle:{"margin-right":"12px"},attrs:{"popper-class":"colorPicker","color-format":"rgb",size:"mini"},on:{change:function(e){return t.inputChangeMaterialParams("color")}},model:{value:t.params.color,callback:function(e){t.$set(t.params,"color",e)},expression:"params.color"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("featureSetting.materialSetting.params.label5"))+" ")]),a("el-color-picker",{staticStyle:{"margin-right":"12px"},attrs:{"popper-class":"colorPicker","color-format":"rgb",size:"mini"},on:{change:function(e){return t.inputChangeMaterialParams("emissiveColor")}},model:{value:t.params.emissiveColor,callback:function(e){t.$set(t.params,"emissiveColor",e)},expression:"params.emissiveColor"}})],1)]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-130"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("featureSetting.materialSetting.params.label6"))+" ")]),a("el-input",{attrs:{size:"mini",type:"number",title:""},on:{input:function(e){return t.inputChangeMaterialParams("clearCoat")}},model:{value:t.params.clearCoat,callback:function(e){t.$set(t.params,"clearCoat",e)},expression:"params.clearCoat"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("featureSetting.materialSetting.params.label7"))+" ")]),a("div",{staticClass:"items-center w100"},[a("el-input",{attrs:{size:"mini",type:"number",title:""},on:{input:function(e){return t.inputChangeMaterialParams("clearCoatRoughness")}},model:{value:t.params.clearCoatRoughness,callback:function(e){t.$set(t.params,"clearCoatRoughness",e)},expression:"params.clearCoatRoughness"}})],1)])]),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("featureSetting.materialSetting.params.label8"))+" ")]),a("el-color-picker",{staticStyle:{"margin-right":"12px"},attrs:{"popper-class":"colorPicker","color-format":"rgb",size:"mini"},on:{change:function(e){return t.inputChangeMaterialParams("specularColor")}},model:{value:t.params.specularColor,callback:function(e){t.$set(t.params,"specularColor",e)},expression:"params.specularColor"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("featureSetting.materialSetting.params.label9"))+" ")]),a("el-color-picker",{staticStyle:{"margin-right":"12px"},attrs:{"popper-class":"colorPicker","color-format":"rgb",size:"mini"},on:{change:function(e){return t.inputChangeMaterialParams("sheenColor")}},model:{value:t.params.sheenColor,callback:function(e){t.$set(t.params,"sheenColor",e)},expression:"params.sheenColor"}})],1)]),a("div",{staticClass:"styleSet-list-div h100 list-div-130"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title",staticStyle:{"min-width":"80px"}},[t._v(" "+t._s(t.$t("featureSetting.materialSetting.params.label10"))+" ")]),a("el-input",{attrs:{size:"mini",type:"number",title:""},on:{input:function(e){return t.inputChangeMaterialParams("specularIntensity")}},model:{value:t.params.specularIntensity,callback:function(e){t.$set(t.params,"specularIntensity",e)},expression:"params.specularIntensity"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title",staticStyle:{"min-width":"80px"}},[t._v(" "+t._s(t.$t("featureSetting.materialSetting.params.label11"))+" ")]),a("div",{staticClass:"items-center w100"},[a("el-input",{attrs:{size:"mini",type:"number",title:""},on:{input:function(e){return t.inputChangeMaterialParams("sheenRoughness")}},model:{value:t.params.sheenRoughness,callback:function(e){t.$set(t.params,"sheenRoughness",e)},expression:"params.sheenRoughness"}})],1)])]),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("formRelational.opacity.label"))+" ")]),a("div",{staticClass:"items-center w100"},[a("el-slider",{staticStyle:{width:"100%"},attrs:{min:0,max:1,step:.1},on:{change:function(e){return t.inputChangeMaterialParams("opacity")}},model:{value:t.params.opacity,callback:function(e){t.$set(t.params,"opacity",e)},expression:"params.opacity"}}),a("span",{staticClass:"slider-num"},[t._v(t._s(t._f("roundOpacity")(t.params.opacity)))])],1)]),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("featureSetting.materialSetting.params.label12"))+" ")]),a("div",{staticClass:"items-center w100"},[a("el-slider",{staticStyle:{width:"100%"},attrs:{min:0,max:1,step:.01},on:{change:function(e){return t.inputChangeMaterialParams("sheen")}},model:{value:t.params.sheen,callback:function(e){t.$set(t.params,"sheen",e)},expression:"params.sheen"}}),a("el-input",{staticClass:"margin-left-5 slideInput",attrs:{size:"mini",type:"number",title:""},on:{input:function(e){return t.inputChangeMaterialParams("sheen")}},model:{value:t.params.sheen,callback:function(e){t.$set(t.params,"sheen",e)},expression:"params.sheen"}})],1)])]),a("div",{staticClass:"after-lineX"}),a("dialogComp",{directives:[{name:"show",rawName:"v-show",value:t.textureEdit.dialogState,expression:"textureEdit.dialogState"}],attrs:{hideHeader:!1,needClose:"true",zIndex:"5",draw:!0,right:100,drag:!0,title:t.$t("featureSetting.materialSetting.texture.edit"),icon:"icon-details",width:270,height:390,type:"detailInfo",dragTopOffset:t.dragTopOffset,top:125,needAdd:!0,backgroundColor:"var(--primary-bg-color)",position:"fixed"},on:{close:function(e){return t.toggleEditTexture()},add:function(e){return t.showTextrueList()}},scopedSlots:t._u([{key:"center",fn:function(){return[a("div",{staticClass:"textureEdit-container"},[a("div",{staticClass:"addList-dialog"},[a("el-dropdown",{attrs:{size:"small",trigger:"click"},on:{command:t.handleTextureAdd}},[a("span",{staticClass:"texture-dropdown-link"},[a("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},t._l(t.textureEdit.typeList,(function(e,i){return a("el-dropdown-item",{key:i,attrs:{disabled:t.disabledTextureItem(i),command:i}},[a("CommonSVG",{attrs:{color:"var(--theme)",size:"14","icon-class":"material_texture_type"}}),t._v(" "+t._s(e)+" ")],1)})),1)],1)],1),t._l(t.params.maps,(function(e,i){return a("div",{staticClass:"map-list"},[a("div",{staticClass:"space-between-center list-tab"},[a("div",{staticClass:"align-center",on:{click:function(t){e.listState=!e.listState}}},[a("span",[t._v(t._s(t.textureEdit.typeList[e.name]))]),e.listState?a("i",{staticClass:"el-icon-caret-bottom"}):a("i",{staticClass:"el-icon-caret-top"})]),a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:t.$t("menuIconName.delete"),placement:"bottom"}},[a("div",{staticClass:"cursor-btn",on:{click:function(a){return t.handleTextureDelete(e.name,i)}}},[a("i",{staticClass:"el-icon el-icon-delete"})])])],1),a("el-collapse-transition",[a("div",{directives:[{name:"show",rawName:"v-show",value:e.listState,expression:"item.listState"}],staticClass:"styleSet-list-div"},[a("div",{staticClass:"items-center list-item",staticStyle:{height:"auto"}},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("featureSetting.materialSetting.texture.label"))+" ")]),a("div",{staticClass:"material-thumb"},[e.path&&""!=e.path?a("div",{staticClass:"w100 h100 padding-5"},[a("el-image",{staticClass:"preview-comp w100 h100",class:"preview-comp"+i,attrs:{src:e.path,"preview-src-list":[e.path]}})],1):t._e()])]),a("div",{staticClass:"items-center list-item"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("featureSetting.materialSetting.texture.label1"))+" ")]),a("el-input",{attrs:{size:"mini",placeholder:t.$t("featureSetting.materialSetting.texture.label1")},on:{input:function(e){return t.inputChangeMaterialTexture("path",i)}},model:{value:e.path,callback:function(a){t.$set(e,"path",a)},expression:"item.path"}}),e.thumbState?a("div",{staticClass:"thumb-dialog"},[a("div",{staticClass:"title"},[a("span",[t._v(t._s(t.$t("formRelational.image.label")))]),a("div",[a("i",{staticClass:"el-icon-close cursor-btn",on:{click:function(e){return t.openThumb(i)}}})])]),a("div",{staticClass:"thumb-content"},t._l(t.thumbList,(function(e){return a("div",{key:e.url,staticClass:"item",class:{active:e.img===e.path},on:{click:function(a){return t.checkPolygonImg(e)}}},[a("div",{staticClass:"img-box"},[a("img",{attrs:{src:e.img}})]),a("span",[t._v(t._s(e.name))])])})),0)]):t._e()],1),a("div",{staticClass:"items-center list-item"},[a("div",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("featureSetting.materialSetting.texture.label2"))+" ")]),a("el-input",{attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("featureSetting.materialSetting.texture.label2")},on:{input:function(e){return t.inputChangeMaterialTexture("rotation",i)}},model:{value:e.rotation,callback:function(a){t.$set(e,"rotation",a)},expression:"item.rotation"}})],1),a("div",{staticClass:"items-center list-item"},[a("div",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("featureSetting.materialSetting.texture.label3"))+" ")]),a("el-input",{directives:[{name:"halfAdjust",rawName:"v-halfAdjust",value:e.uoffset,expression:"item.uoffset"}],attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("featureSetting.materialSetting.texture.label3")},on:{input:function(e){return t.inputChangeMaterialTexture("uoffset",i)}},model:{value:e.uoffset,callback:function(a){t.$set(e,"uoffset",a)},expression:"item.uoffset"}})],1),a("div",{staticClass:"items-center list-item"},[a("div",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("featureSetting.materialSetting.texture.label4"))+" ")]),a("el-input",{directives:[{name:"halfAdjust",rawName:"v-halfAdjust",value:e.voffset,expression:"item.voffset"}],attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("featureSetting.materialSetting.texture.label4")},on:{input:function(e){return t.inputChangeMaterialTexture("voffset",i)}},model:{value:e.voffset,callback:function(a){t.$set(e,"voffset",a)},expression:"item.voffset"}})],1),a("div",{staticClass:"items-center list-item"},[a("div",{staticClass:"item-title"},[a("span",[t._v(t._s(t.$t("featureSetting.materialSetting.texture.label5")))])]),e.uRepeat?a("el-input",{directives:[{name:"halfAdjust",rawName:"v-halfAdjust",value:e.usc,expression:"item.usc"}],attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("featureSetting.materialSetting.texture.label5")},on:{input:function(e){return t.inputChangeMaterialTexture("usc",i)}},model:{value:e.usc,callback:function(a){t.$set(e,"usc",a)},expression:"item.usc"}},[a("template",{slot:"suffix"},[a("el-tooltip",{staticClass:"box-item",attrs:{enterable:!1,effect:"dark",content:t.$t("featureSetting.materialSetting.texture.tooltip"),placement:"top"}},[a("i",{staticClass:"outline-none el-input__icon el-icon-question"})])],1)],2):t._e(),a("el-checkbox",{staticClass:"margin-left-5",on:{change:function(e){return t.inputChangeMaterialTexture("uRepeat",i)}},model:{value:e.uRepeat,callback:function(a){t.$set(e,"uRepeat",a)},expression:"item.uRepeat"}})],1),a("div",{staticClass:"items-center list-item"},[a("div",{staticClass:"item-title"},[a("span",[t._v(t._s(t.$t("featureSetting.materialSetting.texture.label6")))])]),e.vRepeat?a("el-input",{directives:[{name:"halfAdjust",rawName:"v-halfAdjust",value:e.vsc,expression:"item.vsc"}],attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("featureSetting.materialSetting.texture.label6")},on:{input:function(e){return t.inputChangeMaterialTexture("vsc",i)}},model:{value:e.vsc,callback:function(a){t.$set(e,"vsc",a)},expression:"item.vsc"}},[a("template",{slot:"suffix"},[a("el-tooltip",{staticClass:"box-item",attrs:{enterable:!1,effect:"dark",content:t.$t("featureSetting.materialSetting.texture.tooltip"),placement:"top"}},[a("i",{staticClass:"outline-none el-input__icon el-icon-question"})])],1)],2):t._e(),a("el-checkbox",{staticClass:"margin-left-5",on:{change:function(e){return t.inputChangeMaterialTexture("vRepeat",i)}},model:{value:e.vRepeat,callback:function(a){t.$set(e,"vRepeat",a)},expression:"item.vRepeat"}})],1)])])],1)})),0==t.params.maps.length?a("div",{staticClass:"margin-top-10 text-center"},[t._v(" "+t._s(t.$t("featureSetting.materialSetting.texture.noData"))+" ")]):t._e()],2)]},proxy:!0}])}),t.needResetOriginal?a("div",{staticClass:"reset-material h100"},[a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:t.$t("featureSetting.materialSetting.params.tooltip1"),placement:"top"}},[a("span",{staticClass:"btn btn-cancel cursor-btn",on:{click:function(e){return t.resetOrigin()}}},[a("CommonSVG",{attrs:{color:"",size:"14","icon-class":"clear_feature"}})],1)])],1):t._e()],1)},Je=[],Ge=(a("4ec9"),a("25f0"),{name:"MaterialParams",components:{CommonSVG:function(){return a.e("chunk-51f90655").then(a.bind(null,"17d0"))}},data:function(){return{params:{type:"",metal:!1,metalness:1,roughness:1,reflectivity:1,opacity:1,emissive:1,polygonOffsetFactor:1,sheenColor:null,sheenRoughness:1,sheen:0,clearCoat:.05,clearCoatRoughness:.05,specularColor:null,specularIntensity:1,color:null,emissiveColor:null,lights:1,dashed:1,linewidth:1,dashScale:1,dashSize:1,gapSize:1,maps:[]},sliderKeys:{metalness:0,roughness:0},storageParams:{},textureEdit:{dialogState:!1,typeList:{map:"",bump:""},rawData:new Map,templateData:{file:"",name:"",path:"",rotation:0,scale:0,uRepeat:!1,vRepeat:!1,usc:1,vsc:1,uoffset:0,voffset:0,listState:!0}},needResetOriginal:!1}},computed:{dragTopOffset:function(){return this.$store.state.scene.topToolBarHeight}},filters:{roundOpacity:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;return Math.round(+t+"e"+e)/Math.pow(10,e)}},created:function(){window.scene.clearSelection(),this.inputChangeMaterialParams=bt()(this.inputChangeMaterialParams,1e3),this.inputChangeMaterialTexture=bt()(this.inputChangeMaterialTexture,1e3),this.textureEdit.typeList.map=this.$t("featureSetting.materialSetting.texture.map"),this.textureEdit.typeList.bump=this.$t("featureSetting.materialSetting.texture.bump")},methods:{test:function(){this.sliderKeys.metalness=(new Date).getTime()},resetTextureImgPath:function(t,e){var a=this;this.textureEdit.rawData.has(t)&&(this.params.maps[e].path=this.textureEdit.rawData.get(t),this.$nextTick((function(){a.inputUpdateMaterial()})))},disabledTextureItem:function(t){var e=this.params.maps.findIndex((function(e){return e.name==t}));return e>=0},handleTextureDelete:function(t,e){var a=this;this.$confirm(this.$t("messageTips.deleteSomething",{name:this.textureEdit.typeList[t]}),this.$t("messageTips.tipsTitle"),{confirmButtonText:this.$t("menuIconName.confirm"),cancelButtonText:this.$t("messageTips.cancel"),type:"warning"}).then((function(){a.params.maps.splice(e,1),a.$nextTick((function(){a.inputUpdateMaterial()}))})).catch((function(){}))},handleTextureAdd:function(t){var e=JSON.parse(JSON.stringify(this.textureEdit.templateData));e.name=t,e.file=Object(u["d"])(),this.params.maps.unshift(e)},showTextrueList:function(){document.querySelector(".texture-dropdown-link").click()},toggleEditTexture:function(){this.textureEdit.dialogState=!this.textureEdit.dialogState},setParamsData:function(t){var e=this,a=JSON.parse(t.params),i=t.originalSign;Object.assign(this.params,this.$options.data().params,a),this.params.color&&(this.params.color=this.colorToString16(this.params.color),this.params.color=Object(u["e"])(this.params.color)),this.params.emissiveColor&&(this.params.emissiveColor=this.colorToString16(this.params.emissiveColor),this.params.emissiveColor=Object(u["e"])(this.params.emissiveColor)),this.params.sheenColor&&(this.params.sheenColor=this.colorToString16(this.params.sheenColor),this.params.sheenColor=Object(u["e"])(this.params.sheenColor)),this.params.specularColor&&(this.params.specularColor=this.colorToString16(this.params.specularColor),this.params.specularColor=Object(u["e"])(this.params.specularColor)),this.params.maps.length>0&&this.params.maps.forEach((function(t){e.$set(t,"listState",!0),e.textureEdit.rawData.set(t.name,t.path)})),this.needResetOriginal=i},colorToString16:function(t){var e=t.toString(16);return 4===e.length&&(e="00"+e),"0x"+e},inputChangeMaterialParams:function(t){if(!this.params.matID)return!1;var e=["metalness","roughness","reflectivity","emissive","sheenRoughness","specularIntensity","sheen","clearCoat","clearCoatRoughness","polygonOffsetFactor"];e.includes(t)&&(this.params[t]=parseFloat(this.params[t])),window.MaterialParams[t]=this.params[t],this.params.color&&(window.MaterialParams.color=parseInt(Object(u["i"])(this.params.color))),this.params.emissiveColor&&(window.MaterialParams.emissiveColor=parseInt(Object(u["i"])(this.params.emissiveColor))),this.params.sheenColor&&(window.MaterialParams.sheenColor=parseInt(Object(u["i"])(this.params.sheenColor))),this.params.specularColor&&(window.MaterialParams.specularColor=parseInt(Object(u["i"])(this.params.specularColor))),t&&this.inputUpdateMaterial()},inputUpdateMaterial:function(){window.MaterialParams.maps=this.params.maps.filter((function(t){return""!=t.path})),console.warn(window.MaterialParams,"材质数据"),window.MaterialParams.matID&&(window.scene.mv.materialService.updateMaterial(window.MaterialParams),this.$nextTick((function(){window.scene.render()})))},handleSliderInput:function(t){this.params[t]<=0&&(this.params[t]=.01),this.params[t]>1&&(this.params[t]=1)},inputChangeMaterialTexture:function(t,e){("usc"==t||"vsc"==t)&&this.params.maps[e][t]<1&&(this.params.maps[e][t]=1);var a=["rotation","scale","usc","vsc","uoffset","voffset"];if(a.includes(t)&&(this.params.maps[e][t]=parseFloat(this.params.maps[e][t])),"path"==t){if(""==this.params.maps[e].path)return!1;this.params.maps[e].file=Object(u["d"])()}"path"!=t&&"uRepeat"!=t&&"vRepeat"!=t&&(this.params.maps[e][t]=parseFloat(this.params.maps[e][t])),this.inputUpdateMaterial()},resetOrigin:function(){var t=this;this.$confirm(this.$t("featureSetting.materialSetting.params.tooltip2"),this.$t("messageTips.tipsTitle"),{confirmButtonText:this.$t("menuIconName.confirm"),cancelButtonText:this.$t("messageTips.cancel"),type:"warning",dangerouslyUseHTMLString:!0}).then((function(){var e={params:window.MaterialParams.original,originalSign:!1};t.$store.commit("saveMaterialRawData",e),t.$emit("resetOrigin")})).catch((function(){}))}},beforeDestroy:function(){Object.assign(this.params,this.$options.data().params)}}),He=Ge,Ue=(a("31b1"),Object(p["a"])(He,Be,Je,!1,null,"7e1761dc",null)),Xe=Ue.exports,qe=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"bottom-content relative material-texture"},[t._l(t.maps,(function(e,i){return a("div",{directives:[{name:"show",rawName:"v-show",value:t.tabActive==i,expression:"tabActive==index"}],staticClass:"flex"},[a("div",{staticClass:"styleSet-list-div"},[a("div",{staticClass:"material-thumb"},[e.path&&""!=e.path?a("div",{staticClass:"w100 h100 padding-5"},[a("el-image",{staticClass:"preview-comp",class:"preview-comp"+i,attrs:{src:e.path,"preview-src-list":[e.path]}})],1):t._e()])]),a("div",{staticClass:"styleSet-list-div"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v("纹理地址")]),a("el-input",{attrs:{size:"mini",placeholder:"纹理地址"},on:{input:function(e){return t.inputChangeMaterialTexture("path",i)}},model:{value:e.path,callback:function(a){t.$set(e,"path",a)},expression:"item.path"}},[a("template",{slot:"append"},[a("span",{staticClass:"cursor-btn",on:{click:function(e){return t.resetTextureImgPath(i)}}},[a("CommonSVG",{attrs:{size:16,color:"var(--theme)","icon-class":"tb_remove_active"}})],1)])],2),a("el-tooltip",{attrs:{enterable:!1,effect:"dark",content:t.$t("formRelational.image.tooltip"),placement:"top"}},[a("span",{staticClass:"thumb-box margin-left-5 cursor-btn",on:{click:function(e){return t.openThumb(i)}}},[a("CommonSVG",{attrs:{size:16,color:"var(--theme)","icon-class":"thumbnail"}})],1)])],1),e.thumbState?a("div",{staticClass:"thumb-dialog"},[a("div",{staticClass:"title"},[a("span",[t._v(t._s(t.$t("formRelational.image.label")))]),a("div",[a("i",{staticClass:"el-icon-close cursor-btn",on:{click:function(e){return t.openThumb(i)}}})])]),a("div",{staticClass:"thumb-content"},t._l(t.thumbList,(function(e){return a("div",{key:e.url,staticClass:"item",class:{active:e.img===e.path},on:{click:function(a){return t.checkPolygonImg(e)}}},[a("div",{staticClass:"img-box"},[a("img",{attrs:{src:e.img}})]),a("span",[t._v(t._s(e.name))])])})),0)]):t._e()]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("div",{staticClass:"item-title"},[t._v("纹理旋转")]),a("el-input",{attrs:{size:"mini",type:"number",placeholder:"纹理旋转"},on:{input:function(e){return t.inputChangeMaterialTexture("rotation",i)}},model:{value:e.rotation,callback:function(a){t.$set(e,"rotation",a)},expression:"item.rotation"}})],1),a("div",{staticClass:"items-center"},[a("div",{staticClass:"item-title"},[t._v("纹理缩放")]),a("el-input",{attrs:{size:"mini",type:"number",placeholder:"纹理缩放"},on:{input:function(e){return t.inputChangeMaterialTexture("scale",i)}},model:{value:e.scale,callback:function(a){t.$set(e,"scale",a)},expression:"item.scale"}})],1)]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("div",{staticClass:"item-title"},[t._v("水平偏移")]),a("el-input",{attrs:{size:"mini",type:"number",placeholder:"水平偏移"},on:{input:function(e){return t.inputChangeMaterialTexture("uoffset",i)}},scopedSlots:t._u([{key:"append",fn:function(){return[a("span",[t._v("mm")])]},proxy:!0}],null,!0),model:{value:e.uoffset,callback:function(a){t.$set(e,"uoffset",a)},expression:"item.uoffset"}})],1),a("div",{staticClass:"items-center"},[a("div",{staticClass:"item-title"},[t._v("垂直偏移")]),a("el-input",{attrs:{size:"mini",type:"number",placeholder:"垂直偏移"},on:{input:function(e){return t.inputChangeMaterialTexture("voffset",i)}},scopedSlots:t._u([{key:"append",fn:function(){return[a("span",[t._v("mm")])]},proxy:!0}],null,!0),model:{value:e.voffset,callback:function(a){t.$set(e,"voffset",a)},expression:"item.voffset"}})],1)]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("div",{staticClass:"item-title",staticStyle:{"min-width":"75px"}},[a("span",[t._v("水平重复")]),a("el-checkbox",{staticClass:"margin-left-5",model:{value:e.uRepeat,callback:function(a){t.$set(e,"uRepeat",a)},expression:"item.uRepeat"}})],1),e.uRepeat?a("el-input",{attrs:{size:"mini",type:"number",placeholder:"水平重复"},on:{input:function(e){return t.inputChangeMaterialTexture("usc",i)}},model:{value:e.usc,callback:function(a){t.$set(e,"usc",a)},expression:"item.usc"}}):t._e()],1),a("div",{staticClass:"items-center"},[a("div",{staticClass:"item-title",staticStyle:{"min-width":"75px"}},[a("span",[t._v("垂直重复")]),a("el-checkbox",{staticClass:"margin-left-5",model:{value:e.vRepeat,callback:function(a){t.$set(e,"vRepeat",a)},expression:"item.vRepeat"}})],1),e.vRepeat?a("el-input",{attrs:{size:"mini",type:"number",placeholder:"垂直重复"},on:{input:function(e){return t.inputChangeMaterialTexture("vsc",i)}},model:{value:e.vsc,callback:function(a){t.$set(e,"vsc",a)},expression:"item.vsc"}}):t._e()],1)]),a("div",{staticClass:"after-lineX"})])})),0==t.maps.length?a("div",{staticClass:"items-center"},[t._v("未查询到纹理数据")]):t._e(),t.textureTabs.length?a("div",{staticClass:"texture-tabs"},t._l(t.textureTabs,(function(e,i){return a("span",{key:e.type,class:[{active:t.tabActive==i},"tabs-item"],on:{click:function(e){return t.toggleTabActive(i)}}},[t._v(" "+t._s(e.title)+" ")])})),0):t._e()],2)},Qe=[],Ke=(a("ac1f"),a("5319"),a("1276"),{name:"MaterialTexture",components:{CommonSVG:function(){return a.e("chunk-51f90655").then(a.bind(null,"17d0"))}},data:function(){return{bb:0,thumbList:[],maps:[],rawPathdata:[],textureTabs:[],tabActive:0}},created:function(){this.getPublicImg(),this.inputChangeMaterialTexture=bt()(this.inputChangeMaterialTexture,800)},methods:{toggleTabActive:function(t){this.tabActive=t},resetTextureImgPath:function(t){this.maps[t].path=this.rawPathdata[t]},previewTextureImg:function(t){document.querySelector(".preview-comp".concat(t," .el-image__preview")).click()},openThumb:function(t){this.maps[t].thumbState=!this.maps[t].thumbState},inputChangeMaterialTexture:function(t,e){"path"!=t&&(this.maps[e][t]=parseFloat(this.maps[e][t])),this.$emit("inputUpdateMaterial")},setTextureTitle:function(t){var e="未知";switch(t){case"map":e="默认纹理";break;case"bump":e="凹凸纹理";break;case"alpha":e="剪切";break}this.textureTabs.push({title:e,type:t})},setTextureData:function(t){var e=this;this.maps=[],t.maps.length>0&&t.maps.forEach((function(t){t.thumbState=!1,e.rawPathdata.push(t.path),e.maps.push(t),e.setTextureTitle(t.name)}))},getPublicImg:function(){var t=this,e=a("ee57");e.keys().length>0&&e.keys().forEach((function(e,a){var i=e.replace(/(.*\/)*([\s\S]*?)/gi,"$2"),s=(i.split(".")[0],"".concat("","/image/environment/").concat(i));t.thumbList.push({name:i,img:s})}))}}}),We=Ke,Ye=(a("eb98"),Object(p["a"])(We,qe,Qe,!1,null,"75358167",null)),Ze=Ye.exports,ta=a("8c81"),ea=a("5de3"),aa=a("2a61"),ia={name:"ObjectSettings",components:{DatasSet:S,StyleSet:C["a"],EventsSet:I,AdvancedSet:Pe,RelationSet:Ve,TriggerConditions:ze["a"],TriggerBehavior:Ne["a"],MaterialParams:Xe,MaterialTexture:Ze,TimeSeries:ta["a"]},mixins:[ea["a"]],props:["settingActive"],data:function(){return{settingKey:[0,1,2,3],tabActive:0,contentActive:"data",scene:null,loading:null,topTabMenu:[{title:this.$t("featureSetting.style.name"),type:"sceneStyleSet"},{title:this.$t("featureSetting.data.name"),type:"sceneDataSet"},{title:this.$t("featureSetting.event.name"),type:"sceneEventSet"},{title:this.$t("featureSetting.advanced.name"),type:"sceneAdvancedSet"},{title:this.$t("featureSetting.triggerConditions.name"),type:"triggerConditions"},{title:this.$t("featureSetting.triggerBehavior.name"),type:"triggerBehavior"},{title:this.$t("featureSetting.materialSetting.params.name"),type:"materialParams"},{title:this.$t("featureSetting.timeSeries.name"),type:"timeSeries"}],annotationNeedData:null,entity:[],entityarr:[],propertyArr:{},entityProperty:{},entityData:{},relationArr:[],tabDatas:[],renderSetOption:[],relationSet:["shield","ring","radar","ripplewall","heatmap","annotation"]}},computed:{dragData:function(){return this.$store.state.scene.dragOverData},projectId:function(){return window.localStorage.getItem("cj-projectId")},currentRef:function(){return this.$store.state.widget.currentWidget},isVothing:function(){return this.$store.state.menuList.isVothing},isRelation:function(){return this.$store.state.widget.isRelation},isIOTType:function(){return this.relationSet.includes(this.dragData.type)}},watch:{"$store.state.scene.dragOverData.type":{handler:function(t,e){for(var a in this.init(),this.settingKey)this.$set(this.settingKey,a,Date.parse(new Date))},deep:!0}},created:function(){this.init()},mounted:function(){},methods:{containerWidthChange:function(){var t=document.querySelector(".sceneManageDom").style.left,e=document.querySelector(".settings-container");parseInt(t)<0?(e.style.left=0,e.style.width="100%"):(e.style.left="300px",e.style.width="calc(100% - 300px)")},init:function(){this.tabDatas=[];var t=this.dragData.type;if("model"!=t||"timeSeries"!=this.settingActive){if(null===this.annotationNeedData){var e=["model","dem","_3dBuilding","fbx","gltf","panorama","video","_3dTiles","wmts","wms","tms","kml","polygon","ripplewall","geoJSON","radar","shp","smoke","flame","polygon","polyline","vectorextrude","shield","ring","heatmap","batch-extrude","advanced_envmap","advanced_setting","pathAnimation","waterfall","projector","snow","tailline"],a=["model","dem","gltf","fbx","annotation","billboard","_3dTiles","panorama","video","polygon","heatmap","ripplewall","radar","shield","ring","flame","smoke","polyline","batch-extrude","vectorextrude","wmts","wms","tms","geoJSON","shp","kml","tailline"];if(e.includes(t))this.tabDatas.push(this.topTabMenu[0]);else if("annotation"==t||"billboard"==t){var i="";if(this.dragData.checkedType)i=this.dragData.checkedType;else{var s=window.scene.features.get(this.dragData.id);i=s.data.setType.type}this.tabDatas="default"==i||"custom"==i?[this.topTabMenu[0],this.topTabMenu[2]]:[this.topTabMenu[0],this.topTabMenu[1],this.topTabMenu[2]]}else"trigger"==t?this.tabDatas=[this.topTabMenu[4],this.topTabMenu[5]]:"advanced_material"==t?(this.tabDatas=[this.topTabMenu[6]],this.getMaterialDatas()):this.tabDatas=this.topTabMenu;a.includes(t)&&this.tabDatas.push(this.topTabMenu[3]),this.contentActive=this.tabDatas[0].type}else"annotation"==this.dragData.type?this.tabDatas=this.topTabMenu:"billboard"==this.dragData.type&&(this.tabDatas=[this.topTabMenu[0],this.topTabMenu[1]]);this.renderSetOption=this.tabDatas.map((function(t){return t.type}))}else this.timeSequenceInit()},closeDialog:function(){this.$store.commit("toggleSettingActive","closeSetting"),this.$store.commit("setActivedType","")},switchTopTabs:function(t,e){this.tabActive=e,this.contentActive=t.type},cancel:function(t){var e=this;this.$store.commit("toggleSettingActive","closeSetting"),this.$store.commit("setActivedType","");var a=this.$store.state.menuList.elementData;if("cancel"==t)if(this.dragData.staticType)try{if(this.$store.commit("toogleElementMenu",""),"pathAnimation"===this.dragData.type){var i=this.$store.state.pathAnimation.editPathAnimation,s=this.$store.state.dialog.activeDialog;-1!==s.indexOf("pathAnimation")&&this.$store.commit("toggleActiveDialog","pathAnimation"),i&&(window.scene.mv.tools.transform.deactive(),this.$store.commit("toogleEditPathAnimation",!1)),window.scene.mv.tools.draw.deactive()}else{var n=window.scene.features.get(this.dragData.id);n.dispose()}}catch(r){console.warn(r)}else this.handelSceneStyleReset(),a&&a.id&&window.scene.clearSelection();this.$store.commit("saveFeatureRawData",null),null!=this.loading&&this.loading.close(),this.$nextTick((function(){window.scene.render(),"objectSetting"!=e.dragData.interlock&&e.$deepUpdateScene(e.dragData.type)}))},setRelationData:function(t){this.relationArr=t},submitRelation:function(t){var e=this;if(this.relationArr.length>0){var a=-1;this.relationArr.forEach((function(i,s){i.relationData.type===t.relationData.type&&("heatmap"===i.relationData.type?i.relationData.relationKey.index===t.relationData.relationKey.index&&(e.relationArr[s]=t,a=1):(e.relationArr[s]=t,a=1))})),-1===a&&this.relationArr.push(t)}else this.relationArr.push(t);"annotation"===this.dragData.type?this.$refs.sceneDataSet.postRelationDatas(this.relationArr):"heatmap"===this.dragData.type?this.$refs.sceneStyleSet.postRelationDatas(this.relationArr):this.$refs.sceneAdvancedSet.postRelationDatas(this.relationArr)},settingSubmit:function(){if("trigger"!=this.dragData.type)if("advanced_material"!=this.dragData.type){var t=this.tabDatas.find((function(t){return"sceneStyleSet"==t.type}));if(void 0!=t){var e=this.$refs.sceneStyleSet.beforeValidate();if(!e)return!1}var a=this.$store.state.menuList.elementData,i=[],s=this.tabDatas.find((function(t){return"sceneEventSet"==t.type}));void 0!=s&&(i=this.$refs.sceneEventSet.getEventDatas());var n={},r=this.tabDatas.find((function(t){return"sceneDataSet"==t.type}));if(void 0!=r){var l=this.$refs.sceneDataSet.beforeValidate();if(!l)return!1;n=this.$refs.sceneDataSet.getDatas()}var o=[],c=this.tabDatas.find((function(t){return"sceneAdvancedSet"==t.type}));if(void 0!=c){var u=this.$refs.sceneAdvancedSet.beforeValidate();if(!u)return!1;o=this.$refs.sceneAdvancedSet.getAdvancedDatas()}var d=null,m=!1;if(this.isVothing&&this.isIOTType){var p={};if(p="annotation"===this.dragData.type&&"custom"!=this.dragData.checkedType?this.$refs.sceneDataSet.getRelationDatas():"heatmap"===this.dragData.type?this.$refs.sceneStyleSet.getRelationDatas():this.$refs.sceneAdvancedSet.getRelationDatas(),p.selectedData&&p.selectedData!==[]&&"0"!==p.selectedData[0]){var h=p.selectedContent||{};if("{}"===JSON.stringify(h))this.entityData={},this.propertyArr=[],this.entityarr=[],this.entity=[],this.entityProperty={};else if("annotation"===this.dragData.type){var v=this.$refs.sceneDataSet.getAttr()||[];if(v.length>0){var f=v.filter((function(t){return""!==t.name}));Object.keys(h.data.entityProperty).forEach((function(t){h.data.entityProperty[t]=f})),this.entityProperty=h.data.entityProperty,Object.keys(h.data.propertyArr).forEach((function(t){h.data.propertyArr[t]=f})),this.propertyArr=h.data.propertyArr}else this.propertyArr=h.data.propertyArr,this.entityProperty=h.data.entityProperty;this.entityData=h,this.entityarr=h.data.entityarr,this.entity=h.data.entity}else this.propertyArr=h.data.propertyArr,this.entityProperty=h.data.entityProperty,this.entityData=h,this.entityarr=h.data.entityarr,this.entity=h.data.entity;d={elementid:"",type:"element",data:{title:"",elementType:"",widgets:{relationArr:this.relationArr,entityData:this.entityData,propertyArr:this.propertyArr,entityarr:this.entityarr,entityProperty:this.entityProperty,entity:this.entity}}},m=!0}else m=!1}this.loading=this.$loading({lock:!0,text:this.$t("others.featurePrepare"),spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.5)"}),this.$store.commit("setActivedType",""),this.isVothing&&this.isIOTType&&m?this.$refs["sceneStyleSet"].submitSetting(n,i,o,d):this.$refs["sceneStyleSet"].submitSetting(n,i,o),a&&a.id&&window.scene.clearSelection()}else this.submitMaterial();else this.submitTrigger()},submitTrigger:function(){var t=this.$refs.triggerConditions.beforeValidate();if(!t)return!1;var e=this.$refs.triggerBehavior.beforeValidate();if(!e)return!1;this.loading=this.$loading({lock:!0,text:this.$t("others.prepare"),spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.5)"}),this.$store.commit("setActivedType","");var a=this.$refs.triggerConditions.params,i=this.$refs.triggerBehavior.getBehaviorForm();"range"==a.triggerType&&aa["a"](a,i,this.dragData),"sensor"==a.triggerType&&aa["b"](a,i,this.dragData),this.$store.commit("getTriggerList"),this.cancel()},styleSetDataReady:function(t,e){this.$refs.sceneEventSet.submitSetting(t,e)},tabDatasSetting:function(t){this.annotationNeedData=t||null},eventsContentChange:function(t){this.$refs.sceneEventSet.toggleContentType(t)},getBatchExtrudeCoordinate:function(){var t=this.$refs.sceneStyleSet.styleFormDatas,e=[t.longitude,t.latitude,t.altitude];return e},getStyleFormDatas:function(){return this.$refs.sceneStyleSet.styleFormDatas},getMaterialDatas:function(){window.scene.mv.events.mouseup.on("default",this.getPrimitive),window.scene.config.highlight=!1},getPrimitive:function(t){var e=this,a=window.scene.pickPrimitive(t);a&&(window.scene.mv.events.mouseup.off("default",this.getPrimitive),this.$nextTick((function(){var t=null;window.MaterialParams=t=window.scene.getMaterialParamByPrim(a),t=JSON.parse(JSON.stringify(t));var i={params:JSON.stringify(t),originalSign:!!window.MaterialParams.original};window.MaterialParams.original||(window.MaterialParams.original=i.params),e.$store.commit("saveMaterialRawData",i),e.$refs["materialParamsRef"].setParamsData(i)})))},submitMaterial:function(){var t=this.$loading({lock:!0,text:this.$t("others.prepare"),spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.5)"});this.$store.commit("setActivedType",""),this.$store.commit("saveMaterialRawData",""),setTimeout((function(){t.close()}),1e3),this.cancel()},inputUpdateMaterial:function(){var t=null;t=this.$refs["materialParamsRef"].storageParams,t.matID&&(window.scene.mv.materialService.updateMaterial(t),this.$nextTick((function(){window.scene.render()})))},timeSequenceInit:function(){this.tabDatas=[this.topTabMenu[7]],this.contentActive=this.tabDatas[0].type,this.renderSetOption=this.tabDatas.map((function(t){return t.type}))}},beforeDestroy:function(){"advanced_material"==this.dragData.type&&(delete window.MaterialParams,this.$message.closeAll(),window.scene.mv.events.mouseup.off("default",this.getPrimitive),window.scene.config.highlight=!0,window.scene.clearSelection(),this.$store.commit("changeSceneManageMenuListParam",{name:"renderSetting",attr:"disable",value:!1,reverse:!0}))}},sa=ia,na=(a("5889"),a("7ab6"),Object(p["a"])(sa,i,s,!1,null,"1c1e1f1e",null));e["default"]=na.exports},c862:function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAZ5JREFUOE+1lD1LA0EQhmcuByJEa78bwUJBEC3ELp3KXQIWAcnFRshG0cofYH6EmJxgoTmJWgjJhWiXxkoUQcRCO7/AUg1GSe5G9jAxBpK7g7jNsl/PvjvvzCK0uGGLeVAFbmmZGRNQBYJ+V5cgPAhALKL4j/m5KjCh6fcIkDIQD90APURBApiPKvLAX2BSJ1PAieWQdOEGuLmXHRdMOo+GZUvcr0IbYHxH7xU+iwXGgq+1FzoGxvJ5kR+M+Xxl3ie07CkRxV/6vAe1846A2+l0R/nNc0JAajQs76r7uUEqGdfFcrm7XRT9CMjETmN6MRB4twW2eY1bDgOkS6bIK1yNmtTXAXGEKVLQGmv6BhCOcehXwTPUNIYCgR+I5pgijSIi/QDvEHAtEpYyfExEqGrZK0A8MhEyTYH1CreS2SkTKI0fzz2MsZJrhTxtamOIgJMAYLCwtGqZk9QXXMWwkocVl7seC08kgLwUks8sd+vctzWlPrETWm44qszeNEp210C7qmkM5LVsQsrwtKiWW/7b2D3N6fr/fbBOFdjt+wbxCigkXqUuRQAAAABJRU5ErkJggg=="},ca37:function(t,e,a){},cc86:function(t,e,a){"use strict";a("1001")},ccd4:function(t,e,a){"use strict";a("0ea8")},d161:function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAKAAAACgCAMAAAC8EZcfAAAAXVBMVEX///+apbUAAAC2vsmlsL2eqLieqLimr7+zvsiiq7qapbTt7/W7w83///+apLWcp7egvsWapbbb3uP8/PybprairLubprSZoLabprWjrbv///+dqbertMOfqbmYo7M3RL3wAAAAHnRSTlMa0QBGd6Whc0qU6B9ACcuyBXcmE76HeyOeaw+uVVCdOHLVAAABkUlEQVR42u3d22rCQBSF4TVJtU7OnmsPef/HbKLxplDoIOiK/f+73H2MSDaMuJWN1dtdmNLUo5+bdn0caRp5kj6CGTAMHboz8F1DpczaVPsmvHUDsJZr1S6cMnXyrXopM63bSsbVaoM1cKsmbGRdCPIO4HMD81zLpYzzPj6AAA0CeGuLhVYreWd/iAABJgbQK+ZBgIkBtAsg86BBAAEmBtAr5kGAiQG0CyDzoEEArYBlLIpYGgNjPxSNgcUILIyB/TnjefACdD0+gAABAvxlHnQG2r9JAAIEOHfgM8+DZSz6P1fE8u7A2CcV7w4s0oAFwNl9xPZfEubBebxJAAIEOHfgM8+DAAECBMg8OMerMPvLRPvr2CHvC+0hgF7x+0GAiQG0C6DbPHjuvx0iQICJAfSKeRBgYgDtAsg8aBBAgIkB9Ip5EGBiAO0CyDxoEMAb8//D7DbkMm6rddjLto1qHUNrtzTgWtXETtmXr28XDpfVC46V+bR6YVpeoZdw6VWSw/NpWl5xXf/xcNDP589p/cc3HUFFpwToygYAAAAASUVORK5CYII="},d5cf:function(t,e,a){"use strict";a("abf2")},dcd5:function(t,e,a){"use strict";a("ff4b")},dd60:function(t,e,a){"use strict";a("b703")},de67:function(t,e,a){},e034:function(t,e,a){},eb98:function(t,e,a){"use strict";a("560f")},ed9b:function(t,e,a){},ee33:function(t,e,a){"use strict";a("1f2e")},ef3e:function(t,e,a){"use strict";a("902e")},f013:function(t,e,a){"use strict";a("9576")},f137:function(t,e,a){},f4af:function(t,e,a){"use strict";a("1c90")},f4d3:function(t,e,a){"use strict";a("365a")},f6b8:function(t,e,a){"use strict";a("8859")},f6bd:function(t,e,a){"use strict";a("ca37")},ff4b:function(t,e,a){}}]);