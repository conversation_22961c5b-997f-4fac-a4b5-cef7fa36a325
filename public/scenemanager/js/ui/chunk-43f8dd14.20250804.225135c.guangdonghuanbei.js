(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-43f8dd14"],{1898:function(e,t,i){"use strict";i.d(t,"b",(function(){return L})),i.d(t,"c",(function(){return R})),i.d(t,"a",(function(){return A}));i("7aad");var o=i("0f70"),n=i("11f7"),r=i("6653"),s=i("5d28"),a=i("00a3"),l=i("1b7d"),d=i("5fe7"),h=function(){var e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])},e(t,i)};return function(t,i){function o(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(o.prototype=i.prototype,new o)}}(),u=11,c=function(e){function t(t){var i=e.call(this)||this;return i._onActivate=t.onActivate,i.bgDomNode=document.createElement("div"),i.bgDomNode.className="arrow-background",i.bgDomNode.style.position="absolute",i.bgDomNode.style.width=t.bgWidth+"px",i.bgDomNode.style.height=t.bgHeight+"px","undefined"!==typeof t.top&&(i.bgDomNode.style.top="0px"),"undefined"!==typeof t.left&&(i.bgDomNode.style.left="0px"),"undefined"!==typeof t.bottom&&(i.bgDomNode.style.bottom="0px"),"undefined"!==typeof t.right&&(i.bgDomNode.style.right="0px"),i.domNode=document.createElement("div"),i.domNode.className=t.className,i.domNode.style.position="absolute",i.domNode.style.width=u+"px",i.domNode.style.height=u+"px","undefined"!==typeof t.top&&(i.domNode.style.top=t.top+"px"),"undefined"!==typeof t.left&&(i.domNode.style.left=t.left+"px"),"undefined"!==typeof t.bottom&&(i.domNode.style.bottom=t.bottom+"px"),"undefined"!==typeof t.right&&(i.domNode.style.right=t.right+"px"),i._mouseMoveMonitor=i._register(new a["a"]),i.onmousedown(i.bgDomNode,(function(e){return i._arrowMouseDown(e)})),i.onmousedown(i.domNode,(function(e){return i._arrowMouseDown(e)})),i._mousedownRepeatTimer=i._register(new d["c"]),i._mousedownScheduleRepeatTimer=i._register(new d["e"]),i}return h(t,e),t.prototype._arrowMouseDown=function(e){var t=this,i=function(){t._mousedownRepeatTimer.cancelAndSet((function(){return t._onActivate()}),1e3/24)};this._onActivate(),this._mousedownRepeatTimer.cancel(),this._mousedownScheduleRepeatTimer.cancelAndSet(i,200),this._mouseMoveMonitor.startMonitoring(e.target,e.buttons,a["b"],(function(e){}),(function(){t._mousedownRepeatTimer.cancel(),t._mousedownScheduleRepeatTimer.cancel()})),e.preventDefault()},t}(l["a"]),p=i("a666"),f=function(){var e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])},e(t,i)};return function(t,i){function o(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(o.prototype=i.prototype,new o)}}(),m=function(e){function t(t,i,o){var n=e.call(this)||this;return n._visibility=t,n._visibleClassName=i,n._invisibleClassName=o,n._domNode=null,n._isVisible=!1,n._isNeeded=!1,n._shouldBeVisible=!1,n._revealTimer=n._register(new d["e"]),n}return f(t,e),t.prototype.applyVisibilitySetting=function(e){return 2!==this._visibility&&(3===this._visibility||e)},t.prototype.setShouldBeVisible=function(e){var t=this.applyVisibilitySetting(e);this._shouldBeVisible!==t&&(this._shouldBeVisible=t,this.ensureVisibility())},t.prototype.setIsNeeded=function(e){this._isNeeded!==e&&(this._isNeeded=e,this.ensureVisibility())},t.prototype.setDomNode=function(e){this._domNode=e,this._domNode.setClassName(this._invisibleClassName),this.setShouldBeVisible(!1)},t.prototype.ensureVisibility=function(){this._isNeeded?this._shouldBeVisible?this._reveal():this._hide(!0):this._hide(!1)},t.prototype._reveal=function(){var e=this;this._isVisible||(this._isVisible=!0,this._revealTimer.setIfNotSet((function(){e._domNode&&e._domNode.setClassName(e._visibleClassName)}),0))},t.prototype._hide=function(e){this._revealTimer.cancel(),this._isVisible&&(this._isVisible=!1,this._domNode&&this._domNode.setClassName(this._invisibleClassName+(e?" fade":"")))},t}(p["a"]),v=i("30db"),b=function(){var e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])},e(t,i)};return function(t,i){function o(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(o.prototype=i.prototype,new o)}}(),y=140,g=function(e){function t(t){var i=e.call(this)||this;return i._lazyRender=t.lazyRender,i._host=t.host,i._scrollable=t.scrollable,i._scrollbarState=t.scrollbarState,i._visibilityController=i._register(new m(t.visibility,"visible scrollbar "+t.extraScrollbarClassName,"invisible scrollbar "+t.extraScrollbarClassName)),i._visibilityController.setIsNeeded(i._scrollbarState.isNeeded()),i._mouseMoveMonitor=i._register(new a["a"]),i._shouldRender=!0,i.domNode=Object(r["b"])(document.createElement("div")),i.domNode.setAttribute("role","presentation"),i.domNode.setAttribute("aria-hidden","true"),i._visibilityController.setDomNode(i.domNode),i.domNode.setPosition("absolute"),i.onmousedown(i.domNode.domNode,(function(e){return i._domNodeMouseDown(e)})),i}return b(t,e),t.prototype._createArrow=function(e){var t=this._register(new c(e));this.domNode.domNode.appendChild(t.bgDomNode),this.domNode.domNode.appendChild(t.domNode)},t.prototype._createSlider=function(e,t,i,o){var n=this;this.slider=Object(r["b"])(document.createElement("div")),this.slider.setClassName("slider"),this.slider.setPosition("absolute"),this.slider.setTop(e),this.slider.setLeft(t),"number"===typeof i&&this.slider.setWidth(i),"number"===typeof o&&this.slider.setHeight(o),this.slider.setLayerHinting(!0),this.slider.setContain("strict"),this.domNode.domNode.appendChild(this.slider.domNode),this.onmousedown(this.slider.domNode,(function(e){e.leftButton&&(e.preventDefault(),n._sliderMouseDown(e,(function(){})))})),this.onclick(this.slider.domNode,(function(e){e.leftButton&&e.stopPropagation()}))},t.prototype._onElementSize=function(e){return this._scrollbarState.setVisibleSize(e)&&(this._visibilityController.setIsNeeded(this._scrollbarState.isNeeded()),this._shouldRender=!0,this._lazyRender||this.render()),this._shouldRender},t.prototype._onElementScrollSize=function(e){return this._scrollbarState.setScrollSize(e)&&(this._visibilityController.setIsNeeded(this._scrollbarState.isNeeded()),this._shouldRender=!0,this._lazyRender||this.render()),this._shouldRender},t.prototype._onElementScrollPosition=function(e){return this._scrollbarState.setScrollPosition(e)&&(this._visibilityController.setIsNeeded(this._scrollbarState.isNeeded()),this._shouldRender=!0,this._lazyRender||this.render()),this._shouldRender},t.prototype.beginReveal=function(){this._visibilityController.setShouldBeVisible(!0)},t.prototype.beginHide=function(){this._visibilityController.setShouldBeVisible(!1)},t.prototype.render=function(){this._shouldRender&&(this._shouldRender=!1,this._renderDomNode(this._scrollbarState.getRectangleLargeSize(),this._scrollbarState.getRectangleSmallSize()),this._updateSlider(this._scrollbarState.getSliderSize(),this._scrollbarState.getArrowSize()+this._scrollbarState.getSliderPosition()))},t.prototype._domNodeMouseDown=function(e){e.target===this.domNode.domNode&&this._onMouseDown(e)},t.prototype.delegateMouseDown=function(e){var t=this.domNode.domNode.getClientRects()[0].top,i=t+this._scrollbarState.getSliderPosition(),o=t+this._scrollbarState.getSliderPosition()+this._scrollbarState.getSliderSize(),n=this._sliderMousePosition(e);i<=n&&n<=o?e.leftButton&&(e.preventDefault(),this._sliderMouseDown(e,(function(){}))):this._onMouseDown(e)},t.prototype._onMouseDown=function(e){var t,i;if(e.target===this.domNode.domNode&&"number"===typeof e.browserEvent.offsetX&&"number"===typeof e.browserEvent.offsetY)t=e.browserEvent.offsetX,i=e.browserEvent.offsetY;else{var o=n["C"](this.domNode.domNode);t=e.posx-o.left,i=e.posy-o.top}this._setDesiredScrollPositionNow(this._scrollbarState.getDesiredScrollPositionFromOffset(this._mouseDownRelativePosition(t,i))),e.leftButton&&(e.preventDefault(),this._sliderMouseDown(e,(function(){})))},t.prototype._sliderMouseDown=function(e,t){var i=this,o=this._sliderMousePosition(e),n=this._sliderOrthogonalMousePosition(e),r=this._scrollbarState.clone();this.slider.toggleClassName("active",!0),this._mouseMoveMonitor.startMonitoring(e.target,e.buttons,a["b"],(function(e){var t=i._sliderOrthogonalMousePosition(e),s=Math.abs(t-n);if(v["h"]&&s>y)i._setDesiredScrollPositionNow(r.getScrollPosition());else{var a=i._sliderMousePosition(e),l=a-o;i._setDesiredScrollPositionNow(r.getDesiredScrollPositionFromDelta(l))}}),(function(){i.slider.toggleClassName("active",!1),i._host.onDragEnd(),t()})),this._host.onDragStart()},t.prototype._setDesiredScrollPositionNow=function(e){var t={};this.writeScrollPosition(t,e),this._scrollable.setScrollPositionNow(t)},t}(l["a"]),_=20,S=function(){function e(e,t,i,o,n,r){this._scrollbarSize=Math.round(t),this._oppositeScrollbarSize=Math.round(i),this._arrowSize=Math.round(e),this._visibleSize=o,this._scrollSize=n,this._scrollPosition=r,this._computedAvailableSize=0,this._computedIsNeeded=!1,this._computedSliderSize=0,this._computedSliderRatio=0,this._computedSliderPosition=0,this._refreshComputedValues()}return e.prototype.clone=function(){return new e(this._arrowSize,this._scrollbarSize,this._oppositeScrollbarSize,this._visibleSize,this._scrollSize,this._scrollPosition)},e.prototype.setVisibleSize=function(e){var t=Math.round(e);return this._visibleSize!==t&&(this._visibleSize=t,this._refreshComputedValues(),!0)},e.prototype.setScrollSize=function(e){var t=Math.round(e);return this._scrollSize!==t&&(this._scrollSize=t,this._refreshComputedValues(),!0)},e.prototype.setScrollPosition=function(e){var t=Math.round(e);return this._scrollPosition!==t&&(this._scrollPosition=t,this._refreshComputedValues(),!0)},e._computeValues=function(e,t,i,o,n){var r=Math.max(0,i-e),s=Math.max(0,r-2*t),a=o>0&&o>i;if(!a)return{computedAvailableSize:Math.round(r),computedIsNeeded:a,computedSliderSize:Math.round(s),computedSliderRatio:0,computedSliderPosition:0};var l=Math.round(Math.max(_,Math.floor(i*s/o))),d=(s-l)/(o-i),h=n*d;return{computedAvailableSize:Math.round(r),computedIsNeeded:a,computedSliderSize:Math.round(l),computedSliderRatio:d,computedSliderPosition:Math.round(h)}},e.prototype._refreshComputedValues=function(){var t=e._computeValues(this._oppositeScrollbarSize,this._arrowSize,this._visibleSize,this._scrollSize,this._scrollPosition);this._computedAvailableSize=t.computedAvailableSize,this._computedIsNeeded=t.computedIsNeeded,this._computedSliderSize=t.computedSliderSize,this._computedSliderRatio=t.computedSliderRatio,this._computedSliderPosition=t.computedSliderPosition},e.prototype.getArrowSize=function(){return this._arrowSize},e.prototype.getScrollPosition=function(){return this._scrollPosition},e.prototype.getRectangleLargeSize=function(){return this._computedAvailableSize},e.prototype.getRectangleSmallSize=function(){return this._scrollbarSize},e.prototype.isNeeded=function(){return this._computedIsNeeded},e.prototype.getSliderSize=function(){return this._computedSliderSize},e.prototype.getSliderPosition=function(){return this._computedSliderPosition},e.prototype.getDesiredScrollPositionFromOffset=function(e){if(!this._computedIsNeeded)return 0;var t=e-this._arrowSize-this._computedSliderSize/2;return Math.round(t/this._computedSliderRatio)},e.prototype.getDesiredScrollPositionFromDelta=function(e){if(!this._computedIsNeeded)return 0;var t=this._computedSliderPosition+e;return Math.round(t/this._computedSliderRatio)},e}(),w=function(){var e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])},e(t,i)};return function(t,i){function o(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(o.prototype=i.prototype,new o)}}(),N=function(e){function t(t,i,o){var n=this,r=t.getScrollDimensions(),a=t.getCurrentScrollPosition();if(n=e.call(this,{lazyRender:i.lazyRender,host:o,scrollbarState:new S(i.horizontalHasArrows?i.arrowSize:0,2===i.horizontal?0:i.horizontalScrollbarSize,2===i.vertical?0:i.verticalScrollbarSize,r.width,r.scrollWidth,a.scrollLeft),visibility:i.horizontal,extraScrollbarClassName:"horizontal",scrollable:t})||this,i.horizontalHasArrows){var l=(i.arrowSize-u)/2,d=(i.horizontalScrollbarSize-u)/2;n._createArrow({className:"left-arrow",top:d,left:l,bottom:void 0,right:void 0,bgWidth:i.arrowSize,bgHeight:i.horizontalScrollbarSize,onActivate:function(){return n._host.onMouseWheel(new s["c"](null,1,0))}}),n._createArrow({className:"right-arrow",top:d,left:void 0,bottom:void 0,right:l,bgWidth:i.arrowSize,bgHeight:i.horizontalScrollbarSize,onActivate:function(){return n._host.onMouseWheel(new s["c"](null,-1,0))}})}return n._createSlider(Math.floor((i.horizontalScrollbarSize-i.horizontalSliderSize)/2),0,void 0,i.horizontalSliderSize),n}return w(t,e),t.prototype._updateSlider=function(e,t){this.slider.setWidth(e),this.slider.setLeft(t)},t.prototype._renderDomNode=function(e,t){this.domNode.setWidth(e),this.domNode.setHeight(t),this.domNode.setLeft(0),this.domNode.setBottom(0)},t.prototype.onDidScroll=function(e){return this._shouldRender=this._onElementScrollSize(e.scrollWidth)||this._shouldRender,this._shouldRender=this._onElementScrollPosition(e.scrollLeft)||this._shouldRender,this._shouldRender=this._onElementSize(e.width)||this._shouldRender,this._shouldRender},t.prototype._mouseDownRelativePosition=function(e,t){return e},t.prototype._sliderMousePosition=function(e){return e.posx},t.prototype._sliderOrthogonalMousePosition=function(e){return e.posy},t.prototype.writeScrollPosition=function(e,t){e.scrollLeft=t},t}(g),C=function(){var e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])},e(t,i)};return function(t,i){function o(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(o.prototype=i.prototype,new o)}}(),D=function(e){function t(t,i,o){var n=this,r=t.getScrollDimensions(),a=t.getCurrentScrollPosition();if(n=e.call(this,{lazyRender:i.lazyRender,host:o,scrollbarState:new S(i.verticalHasArrows?i.arrowSize:0,2===i.vertical?0:i.verticalScrollbarSize,0,r.height,r.scrollHeight,a.scrollTop),visibility:i.vertical,extraScrollbarClassName:"vertical",scrollable:t})||this,i.verticalHasArrows){var l=(i.arrowSize-u)/2,d=(i.verticalScrollbarSize-u)/2;n._createArrow({className:"up-arrow",top:l,left:d,bottom:void 0,right:void 0,bgWidth:i.verticalScrollbarSize,bgHeight:i.arrowSize,onActivate:function(){return n._host.onMouseWheel(new s["c"](null,0,1))}}),n._createArrow({className:"down-arrow",top:void 0,left:d,bottom:l,right:void 0,bgWidth:i.verticalScrollbarSize,bgHeight:i.arrowSize,onActivate:function(){return n._host.onMouseWheel(new s["c"](null,0,-1))}})}return n._createSlider(0,Math.floor((i.verticalScrollbarSize-i.verticalSliderSize)/2),i.verticalSliderSize,void 0),n}return C(t,e),t.prototype._updateSlider=function(e,t){this.slider.setHeight(e),this.slider.setTop(t)},t.prototype._renderDomNode=function(e,t){this.domNode.setWidth(t),this.domNode.setHeight(e),this.domNode.setRight(0),this.domNode.setTop(0)},t.prototype.onDidScroll=function(e){return this._shouldRender=this._onElementScrollSize(e.scrollHeight)||this._shouldRender,this._shouldRender=this._onElementScrollPosition(e.scrollTop)||this._shouldRender,this._shouldRender=this._onElementSize(e.height)||this._shouldRender,this._shouldRender},t.prototype._mouseDownRelativePosition=function(e,t){return t},t.prototype._sliderMousePosition=function(e){return e.posy},t.prototype._sliderOrthogonalMousePosition=function(e){return e.posx},t.prototype.writeScrollPosition=function(e,t){e.scrollTop=t},t}(g),O=i("308f"),P=i("42e3"),z=function(){var e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])},e(t,i)};return function(t,i){function o(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(o.prototype=i.prototype,new o)}}(),E=500,I=50,j=!0,T=function(){function e(e,t,i){this.timestamp=e,this.deltaX=t,this.deltaY=i,this.score=0}return e}(),x=function(){function e(){this._capacity=5,this._memory=[],this._front=-1,this._rear=-1}return e.prototype.isPhysicalMouseWheel=function(){if(-1===this._front&&-1===this._rear)return!1;var e=1,t=0,i=1,o=this._rear;do{var n=o===this._front?e:Math.pow(2,-i);if(e-=n,t+=this._memory[o].score*n,o===this._front)break;o=(this._capacity+o-1)%this._capacity,i++}while(1);return t<=.5},e.prototype.accept=function(e,t,i){var o=new T(e,t,i);o.score=this._computeScore(o),-1===this._front&&-1===this._rear?(this._memory[0]=o,this._front=0,this._rear=0):(this._rear=(this._rear+1)%this._capacity,this._rear===this._front&&(this._front=(this._front+1)%this._capacity),this._memory[this._rear]=o)},e.prototype._computeScore=function(e){if(Math.abs(e.deltaX)>0&&Math.abs(e.deltaY)>0)return 1;var t=.5;-1===this._front&&-1===this._rear||this._memory[this._rear];return(Math.abs(e.deltaX-Math.round(e.deltaX))>0||Math.abs(e.deltaY-Math.round(e.deltaY))>0)&&(t+=.25),Math.min(Math.max(t,0),1)},e.INSTANCE=new e,e}(),M=function(e){function t(t,i,o){var n=e.call(this)||this;n._onScroll=n._register(new O["a"]),n.onScroll=n._onScroll.event,t.style.overflow="hidden",n._options=k(i),n._scrollable=o,n._register(n._scrollable.onScroll((function(e){n._onDidScroll(e),n._onScroll.fire(e)})));var s={onMouseWheel:function(e){return n._onMouseWheel(e)},onDragStart:function(){return n._onDragStart()},onDragEnd:function(){return n._onDragEnd()}};return n._verticalScrollbar=n._register(new D(n._scrollable,n._options,s)),n._horizontalScrollbar=n._register(new N(n._scrollable,n._options,s)),n._domNode=document.createElement("div"),n._domNode.className="monaco-scrollable-element "+n._options.className,n._domNode.setAttribute("role","presentation"),n._domNode.style.position="relative",n._domNode.style.overflow="hidden",n._domNode.appendChild(t),n._domNode.appendChild(n._horizontalScrollbar.domNode.domNode),n._domNode.appendChild(n._verticalScrollbar.domNode.domNode),n._options.useShadows?(n._leftShadowDomNode=Object(r["b"])(document.createElement("div")),n._leftShadowDomNode.setClassName("shadow"),n._domNode.appendChild(n._leftShadowDomNode.domNode),n._topShadowDomNode=Object(r["b"])(document.createElement("div")),n._topShadowDomNode.setClassName("shadow"),n._domNode.appendChild(n._topShadowDomNode.domNode),n._topLeftShadowDomNode=Object(r["b"])(document.createElement("div")),n._topLeftShadowDomNode.setClassName("shadow top-left-corner"),n._domNode.appendChild(n._topLeftShadowDomNode.domNode)):(n._leftShadowDomNode=null,n._topShadowDomNode=null,n._topLeftShadowDomNode=null),n._listenOnDomNode=n._options.listenOnDomNode||n._domNode,n._mouseWheelToDispose=[],n._setListeningToMouseWheel(n._options.handleMouseWheel),n.onmouseover(n._listenOnDomNode,(function(e){return n._onMouseOver(e)})),n.onnonbubblingmouseout(n._listenOnDomNode,(function(e){return n._onMouseOut(e)})),n._hideTimeout=n._register(new d["e"]),n._isDragging=!1,n._mouseIsOver=!1,n._shouldRender=!0,n._revealOnScroll=!0,n}return z(t,e),t.prototype.dispose=function(){this._mouseWheelToDispose=Object(p["f"])(this._mouseWheelToDispose),e.prototype.dispose.call(this)},t.prototype.getDomNode=function(){return this._domNode},t.prototype.getOverviewRulerLayoutInfo=function(){return{parent:this._domNode,insertBefore:this._verticalScrollbar.domNode.domNode}},t.prototype.delegateVerticalScrollbarMouseDown=function(e){this._verticalScrollbar.delegateMouseDown(e)},t.prototype.getScrollDimensions=function(){return this._scrollable.getScrollDimensions()},t.prototype.setScrollDimensions=function(e){this._scrollable.setScrollDimensions(e)},t.prototype.updateClassName=function(e){this._options.className=e,v["e"]&&(this._options.className+=" mac"),this._domNode.className="monaco-scrollable-element "+this._options.className},t.prototype.updateOptions=function(e){var t=k(e);this._options.handleMouseWheel=t.handleMouseWheel,this._options.mouseWheelScrollSensitivity=t.mouseWheelScrollSensitivity,this._options.fastScrollSensitivity=t.fastScrollSensitivity,this._setListeningToMouseWheel(this._options.handleMouseWheel),this._options.lazyRender||this._render()},t.prototype._setListeningToMouseWheel=function(e){var t=this,i=this._mouseWheelToDispose.length>0;if(i!==e&&(this._mouseWheelToDispose=Object(p["f"])(this._mouseWheelToDispose),e)){var r=function(e){t._onMouseWheel(new s["c"](e))};this._mouseWheelToDispose.push(n["j"](this._listenOnDomNode,o["f"]?"mousewheel":"wheel",r,{passive:!1}))}},t.prototype._onMouseWheel=function(e){var t,i=x.INSTANCE;if(j&&i.accept(Date.now(),e.deltaX,e.deltaY),e.deltaY||e.deltaX){var o=e.deltaY*this._options.mouseWheelScrollSensitivity,n=e.deltaX*this._options.mouseWheelScrollSensitivity;this._options.flipAxes&&(t=[n,o],o=t[0],n=t[1]);var r=!v["e"]&&e.browserEvent&&e.browserEvent.shiftKey;!this._options.scrollYToX&&!r||n||(n=o,o=0),e.browserEvent&&e.browserEvent.altKey&&(n*=this._options.fastScrollSensitivity,o*=this._options.fastScrollSensitivity);var s=this._scrollable.getFutureScrollPosition(),a={};if(o){var l=s.scrollTop-I*o;this._verticalScrollbar.writeScrollPosition(a,l)}if(n){var d=s.scrollLeft-I*n;this._horizontalScrollbar.writeScrollPosition(a,d)}if(a=this._scrollable.validateScrollPosition(a),s.scrollLeft!==a.scrollLeft||s.scrollTop!==a.scrollTop){var h=j&&this._options.mouseWheelSmoothScroll&&i.isPhysicalMouseWheel();h?this._scrollable.setScrollPositionSmooth(a):this._scrollable.setScrollPositionNow(a),this._shouldRender=!0}}(this._options.alwaysConsumeMouseWheel||this._shouldRender)&&(e.preventDefault(),e.stopPropagation())},t.prototype._onDidScroll=function(e){this._shouldRender=this._horizontalScrollbar.onDidScroll(e)||this._shouldRender,this._shouldRender=this._verticalScrollbar.onDidScroll(e)||this._shouldRender,this._options.useShadows&&(this._shouldRender=!0),this._revealOnScroll&&this._reveal(),this._options.lazyRender||this._render()},t.prototype.renderNow=function(){if(!this._options.lazyRender)throw new Error("Please use `lazyRender` together with `renderNow`!");this._render()},t.prototype._render=function(){if(this._shouldRender&&(this._shouldRender=!1,this._horizontalScrollbar.render(),this._verticalScrollbar.render(),this._options.useShadows)){var e=this._scrollable.getCurrentScrollPosition(),t=e.scrollTop>0,i=e.scrollLeft>0;this._leftShadowDomNode.setClassName("shadow"+(i?" left":"")),this._topShadowDomNode.setClassName("shadow"+(t?" top":"")),this._topLeftShadowDomNode.setClassName("shadow top-left-corner"+(t?" top":"")+(i?" left":""))}},t.prototype._onDragStart=function(){this._isDragging=!0,this._reveal()},t.prototype._onDragEnd=function(){this._isDragging=!1,this._hide()},t.prototype._onMouseOut=function(e){this._mouseIsOver=!1,this._hide()},t.prototype._onMouseOver=function(e){this._mouseIsOver=!0,this._reveal()},t.prototype._reveal=function(){this._verticalScrollbar.beginReveal(),this._horizontalScrollbar.beginReveal(),this._scheduleHide()},t.prototype._hide=function(){this._mouseIsOver||this._isDragging||(this._verticalScrollbar.beginHide(),this._horizontalScrollbar.beginHide())},t.prototype._scheduleHide=function(){var e=this;this._mouseIsOver||this._isDragging||this._hideTimeout.cancelAndSet((function(){return e._hide()}),E)},t}(l["a"]),L=function(e){function t(t,i){var o=this;i=i||{},i.mouseWheelSmoothScroll=!1;var r=new P["a"](0,(function(e){return n["W"](e)}));return o=e.call(this,t,i,r)||this,o._register(r),o}return z(t,e),t.prototype.setScrollPosition=function(e){this._scrollable.setScrollPositionNow(e)},t.prototype.getScrollPosition=function(){return this._scrollable.getCurrentScrollPosition()},t}(M),R=function(e){function t(t,i,o){return e.call(this,t,i,o)||this}return z(t,e),t}(M),A=function(e){function t(t,i){var o=e.call(this,t,i)||this;return o._element=t,o.onScroll((function(e){e.scrollTopChanged&&(o._element.scrollTop=e.scrollTop),e.scrollLeftChanged&&(o._element.scrollLeft=e.scrollLeft)})),o.scanDomNode(),o}return z(t,e),t.prototype.scanDomNode=function(){this.setScrollDimensions({width:this._element.clientWidth,scrollWidth:this._element.scrollWidth,height:this._element.clientHeight,scrollHeight:this._element.scrollHeight}),this.setScrollPosition({scrollLeft:this._element.scrollLeft,scrollTop:this._element.scrollTop})},t}(L);function k(e){var t={lazyRender:"undefined"!==typeof e.lazyRender&&e.lazyRender,className:"undefined"!==typeof e.className?e.className:"",useShadows:"undefined"===typeof e.useShadows||e.useShadows,handleMouseWheel:"undefined"===typeof e.handleMouseWheel||e.handleMouseWheel,flipAxes:"undefined"!==typeof e.flipAxes&&e.flipAxes,alwaysConsumeMouseWheel:"undefined"!==typeof e.alwaysConsumeMouseWheel&&e.alwaysConsumeMouseWheel,scrollYToX:"undefined"!==typeof e.scrollYToX&&e.scrollYToX,mouseWheelScrollSensitivity:"undefined"!==typeof e.mouseWheelScrollSensitivity?e.mouseWheelScrollSensitivity:1,fastScrollSensitivity:"undefined"!==typeof e.fastScrollSensitivity?e.fastScrollSensitivity:5,mouseWheelSmoothScroll:"undefined"===typeof e.mouseWheelSmoothScroll||e.mouseWheelSmoothScroll,arrowSize:"undefined"!==typeof e.arrowSize?e.arrowSize:11,listenOnDomNode:"undefined"!==typeof e.listenOnDomNode?e.listenOnDomNode:null,horizontal:"undefined"!==typeof e.horizontal?e.horizontal:1,horizontalScrollbarSize:"undefined"!==typeof e.horizontalScrollbarSize?e.horizontalScrollbarSize:10,horizontalSliderSize:"undefined"!==typeof e.horizontalSliderSize?e.horizontalSliderSize:0,horizontalHasArrows:"undefined"!==typeof e.horizontalHasArrows&&e.horizontalHasArrows,vertical:"undefined"!==typeof e.vertical?e.vertical:1,verticalScrollbarSize:"undefined"!==typeof e.verticalScrollbarSize?e.verticalScrollbarSize:10,verticalHasArrows:"undefined"!==typeof e.verticalHasArrows&&e.verticalHasArrows,verticalSliderSize:"undefined"!==typeof e.verticalSliderSize?e.verticalSliderSize:0};return t.horizontalSliderSize="undefined"!==typeof e.horizontalSliderSize?e.horizontalSliderSize:t.horizontalScrollbarSize,t.verticalSliderSize="undefined"!==typeof e.verticalSliderSize?e.verticalSliderSize:t.verticalScrollbarSize,v["e"]&&(t.className+=" mac"),t}},"1b7d":function(e,t,i){"use strict";i.d(t,"a",(function(){return d}));var o=i("11f7"),n=i("b835"),r=i("5d28"),s=i("a666"),a=i("a60f"),l=function(){var e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])},e(t,i)};return function(t,i){function o(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(o.prototype=i.prototype,new o)}}(),d=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return l(t,e),t.prototype.onclick=function(e,t){this._register(o["j"](e,o["d"].CLICK,(function(e){return t(new r["b"](e))})))},t.prototype.onmousedown=function(e,t){this._register(o["j"](e,o["d"].MOUSE_DOWN,(function(e){return t(new r["b"](e))})))},t.prototype.onmouseover=function(e,t){this._register(o["j"](e,o["d"].MOUSE_OVER,(function(e){return t(new r["b"](e))})))},t.prototype.onnonbubblingmouseout=function(e,t){this._register(o["k"](e,(function(e){return t(new r["b"](e))})))},t.prototype.onkeydown=function(e,t){this._register(o["j"](e,o["d"].KEY_DOWN,(function(e){return t(new n["a"](e))})))},t.prototype.onkeyup=function(e,t){this._register(o["j"](e,o["d"].KEY_UP,(function(e){return t(new n["a"](e))})))},t.prototype.oninput=function(e,t){this._register(o["j"](e,o["d"].INPUT,t))},t.prototype.onblur=function(e,t){this._register(o["j"](e,o["d"].BLUR,t))},t.prototype.onfocus=function(e,t){this._register(o["j"](e,o["d"].FOCUS,t))},t.prototype.ignoreGesture=function(e){a["b"].ignoreTarget(e)},t}(s["a"])},"1f26":function(e,t,i){},"3be3":function(e,t,i){"use strict";i.d(t,"a",(function(){return v}));i("1f26");var o=i("a666"),n=i("ceb8"),r=i("aa3d"),s=i("11f7"),a=i("5fe7"),l=function(){var e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])},e(t,i)};return function(t,i){function o(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(o.prototype=i.prototype,new o)}}(),d="done",h="active",u="infinite",c="discrete",p="monaco-progress-container",f="progress-bit",m={progressBarBackground:n["a"].fromHex("#0E70C0")},v=function(e){function t(t,i){var o=e.call(this)||this;return o.options=i||Object.create(null),Object(r["g"])(o.options,m,!1),o.workedVal=0,o.progressBarBackground=o.options.progressBarBackground,o._register(o.showDelayedScheduler=new a["d"]((function(){return Object(s["X"])(o.element)}),0)),o.create(t),o}return l(t,e),t.prototype.create=function(e){this.element=document.createElement("div"),Object(s["f"])(this.element,p),e.appendChild(this.element),this.bit=document.createElement("div"),Object(s["f"])(this.bit,f),this.element.appendChild(this.bit),this.applyStyles()},t.prototype.off=function(){this.bit.style.width="inherit",this.bit.style.opacity="1",Object(s["Q"])(this.element,h,u,c),this.workedVal=0,this.totalWork=void 0},t.prototype.stop=function(){return this.doDone(!1)},t.prototype.doDone=function(e){var t=this;return Object(s["f"])(this.element,d),Object(s["I"])(this.element,u)?(this.bit.style.opacity="0",e?setTimeout((function(){return t.off()}),200):this.off()):(this.bit.style.width="inherit",e?setTimeout((function(){return t.off()}),200):this.off()),this},t.prototype.hide=function(){Object(s["J"])(this.element),this.showDelayedScheduler.cancel()},t.prototype.style=function(e){this.progressBarBackground=e.progressBarBackground,this.applyStyles()},t.prototype.applyStyles=function(){if(this.bit){var e=this.progressBarBackground?this.progressBarBackground.toString():"";this.bit.style.backgroundColor=e}},t}(o["a"])},"5b3a":function(e,t,i){"use strict";i.d(t,"a",(function(){return j})),i.d(t,"b",(function(){return L}));var o=i("c482"),n=i("63d5"),r=i("c8ac"),s=i("a666"),a=i("308f"),l=i("5fe7"),d=i("258a"),h=i("7de1"),u=i("fdcc"),c=i("11f7"),p=i("4035"),f=i("c34a"),m=function(){var e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])},e(t,i)};return function(t,i){function o(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(o.prototype=i.prototype,new o)}}(),v=function(){return v=Object.assign||function(e){for(var t,i=1,o=arguments.length;i<o;i++)for(var n in t=arguments[i],t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},v.apply(this,arguments)},b=function(e,t,i,o){function n(e){return e instanceof i?e:new i((function(t){t(e)}))}return new(i||(i=Promise))((function(i,r){function s(e){try{l(o.next(e))}catch(t){r(t)}}function a(e){try{l(o["throw"](e))}catch(t){r(t)}}function l(e){e.done?i(e.value):n(e.value).then(s,a)}l((o=o.apply(e,t||[])).next())}))},y=function(e,t){var i,o,n,r,s={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return r={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function a(e){return function(t){return l([e,t])}}function l(r){if(i)throw new TypeError("Generator is already executing.");while(s)try{if(i=1,o&&(n=2&r[0]?o["return"]:r[0]?o["throw"]||((n=o["return"])&&n.call(o),0):o.next)&&!(n=n.call(o,r[1])).done)return n;switch(o=0,n&&(r=[2&r[0],n.value]),r[0]){case 0:case 1:n=r;break;case 4:return s.label++,{value:r[1],done:!1};case 5:s.label++,o=r[1],r=[0];continue;case 7:r=s.ops.pop(),s.trys.pop();continue;default:if(n=s.trys,!(n=n.length>0&&n[n.length-1])&&(6===r[0]||2===r[0])){s=0;continue}if(3===r[0]&&(!n||r[1]>n[0]&&r[1]<n[3])){s.label=r[1];break}if(6===r[0]&&s.label<n[1]){s.label=n[1],n=r;break}if(n&&s.label<n[2]){s.label=n[2],s.ops.push(r);break}n[2]&&s.ops.pop(),s.trys.pop();continue}r=t.call(e,s)}catch(a){r=[6,a],o=0}finally{i=n=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}},g=function(){for(var e=0,t=0,i=arguments.length;t<i;t++)e+=arguments[t].length;var o=Array(e),n=0;for(t=0;t<i;t++)for(var r=arguments[t],s=0,a=r.length;s<a;s++,n++)o[n]=r[s];return o};function _(e){return v(v({},e),{children:[],refreshPromise:void 0,stale:!0,slow:!1,collapsedByDefault:void 0})}function S(e,t){return!!t.parent&&(t.parent===e||S(e,t.parent))}function w(e,t){return e===t||S(e,t)||S(t,e)}var N=function(){function e(e){this.node=e}return Object.defineProperty(e.prototype,"element",{get:function(){return this.node.element.element},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"children",{get:function(){return this.node.children.map((function(t){return new e(t)}))},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"depth",{get:function(){return this.node.depth},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"visibleChildrenCount",{get:function(){return this.node.visibleChildrenCount},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"visibleChildIndex",{get:function(){return this.node.visibleChildIndex},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"collapsible",{get:function(){return this.node.collapsible},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"collapsed",{get:function(){return this.node.collapsed},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"visible",{get:function(){return this.node.visible},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"filterData",{get:function(){return this.node.filterData},enumerable:!0,configurable:!0}),e}(),C=function(){function e(e,t,i){this.renderer=e,this.nodeMapper=t,this.onDidChangeTwistieState=i,this.renderedNodes=new Map,this.templateId=e.templateId}return e.prototype.renderTemplate=function(e){var t=this.renderer.renderTemplate(e);return{templateData:t}},e.prototype.renderElement=function(e,t,i,o){this.renderer.renderElement(this.nodeMapper.map(e),t,i.templateData,o)},e.prototype.renderTwistie=function(e,t){return Object(c["Y"])(t,"codicon-loading",e.slow),!1},e.prototype.disposeElement=function(e,t,i,o){this.renderer.disposeElement&&this.renderer.disposeElement(this.nodeMapper.map(e),t,i.templateData,o)},e.prototype.disposeTemplate=function(e){this.renderer.disposeTemplate(e.templateData)},e.prototype.dispose=function(){this.renderedNodes.clear()},e}();function D(e){return{browserEvent:e.browserEvent,elements:e.elements.map((function(e){return e.element}))}}var O=function(e){function t(t){var i=e.call(this,t.elements.map((function(e){return e.element})))||this;return i.data=t,i}return m(t,e),t}(h["a"]);function P(e){return e instanceof h["a"]?new O(e):e}var z=function(){function e(e){this.dnd=e}return e.prototype.getDragURI=function(e){return this.dnd.getDragURI(e.element)},e.prototype.getDragLabel=function(e,t){if(this.dnd.getDragLabel)return this.dnd.getDragLabel(e.map((function(e){return e.element})),t)},e.prototype.onDragStart=function(e,t){this.dnd.onDragStart&&this.dnd.onDragStart(P(e),t)},e.prototype.onDragOver=function(e,t,i,o,n){return void 0===n&&(n=!0),this.dnd.onDragOver(P(e),t&&t.element,i,o)},e.prototype.drop=function(e,t,i,o){this.dnd.drop(P(e),t&&t.element,i,o)},e.prototype.onDragEnd=function(e){this.dnd.onDragEnd&&this.dnd.onDragEnd(e)},e}();function E(e){return e&&v(v({},e),{collapseByDefault:!0,identityProvider:e.identityProvider&&{getId:function(t){return e.identityProvider.getId(t.element)}},dnd:e.dnd&&new z(e.dnd),multipleSelectionController:e.multipleSelectionController&&{isSelectionSingleChangeEvent:function(t){return e.multipleSelectionController.isSelectionSingleChangeEvent(v(v({},t),{element:t.element}))},isSelectionRangeChangeEvent:function(t){return e.multipleSelectionController.isSelectionRangeChangeEvent(v(v({},t),{element:t.element}))}},accessibilityProvider:e.accessibilityProvider&&v(v({},e.accessibilityProvider),{getAriaLabel:function(t){return e.accessibilityProvider.getAriaLabel(t.element)},getAriaLevel:e.accessibilityProvider.getAriaLevel&&function(t){return e.accessibilityProvider.getAriaLevel(t.element)},getActiveDescendantId:e.accessibilityProvider.getActiveDescendantId&&function(t){return e.accessibilityProvider.getActiveDescendantId(t.element)}}),filter:e.filter&&{filter:function(t,i){return e.filter.filter(t.element,i)}},keyboardNavigationLabelProvider:e.keyboardNavigationLabelProvider&&v(v({},e.keyboardNavigationLabelProvider),{getKeyboardNavigationLabel:function(t){return e.keyboardNavigationLabelProvider.getKeyboardNavigationLabel(t.element)}}),sorter:void 0,expandOnlyOnTwistieClick:"undefined"===typeof e.expandOnlyOnTwistieClick?void 0:"function"!==typeof e.expandOnlyOnTwistieClick?e.expandOnlyOnTwistieClick:function(t){return e.expandOnlyOnTwistieClick(t.element)},ariaProvider:e.ariaProvider&&{getPosInSet:function(t,i){return e.ariaProvider.getPosInSet(t.element,i)},getSetSize:function(t,i,o){return e.ariaProvider.getSetSize(t.element,i,o)},getRole:e.ariaProvider.getRole?function(t){return e.ariaProvider.getRole(t.element)}:void 0,isChecked:e.ariaProvider.isChecked?function(t){var i;return(null===(i=e.ariaProvider)||void 0===i?void 0:i.isChecked)(t.element)}:void 0},additionalScrollHeight:e.additionalScrollHeight})}function I(e,t){t(e),e.children.forEach((function(e){return I(e,t)}))}var j=function(){function e(e,t,i,o,n,l){void 0===l&&(l={}),this.user=e,this.dataSource=n,this.nodes=new Map,this.subTreeRefreshPromises=new Map,this.refreshPromises=new Map,this._onDidRender=new a["a"],this._onDidChangeNodeSlowState=new a["a"],this.nodeMapper=new r["b"]((function(e){return new N(e)})),this.disposables=new s["b"],this.identityProvider=l.identityProvider,this.autoExpandSingleChildren="undefined"!==typeof l.autoExpandSingleChildren&&l.autoExpandSingleChildren,this.sorter=l.sorter,this.collapseByDefault=l.collapseByDefault,this.tree=this.createTree(e,t,i,o,l),this.root=_({element:void 0,parent:null,hasChildren:!0}),this.identityProvider&&(this.root=v(v({},this.root),{id:null})),this.nodes.set(null,this.root),this.tree.onDidChangeCollapseState(this._onDidChangeCollapseState,this,this.disposables)}return Object.defineProperty(e.prototype,"onDidChangeFocus",{get:function(){return a["b"].map(this.tree.onDidChangeFocus,D)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"onDidChangeSelection",{get:function(){return a["b"].map(this.tree.onDidChangeSelection,D)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"onDidOpen",{get:function(){return a["b"].map(this.tree.onDidOpen,D)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"onDidFocus",{get:function(){return this.tree.onDidFocus},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"onDidDispose",{get:function(){return this.tree.onDidDispose},enumerable:!0,configurable:!0}),e.prototype.createTree=function(e,t,i,r,s){var a=this,l=new o["b"](i),d=r.map((function(e){return new C(e,a.nodeMapper,a._onDidChangeNodeSlowState.event)})),h=E(s)||{};return new n["b"](e,t,l,d,h)},e.prototype.updateOptions=function(e){void 0===e&&(e={}),this.tree.updateOptions(e)},e.prototype.getHTMLElement=function(){return this.tree.getHTMLElement()},Object.defineProperty(e.prototype,"scrollTop",{get:function(){return this.tree.scrollTop},set:function(e){this.tree.scrollTop=e},enumerable:!0,configurable:!0}),e.prototype.domFocus=function(){this.tree.domFocus()},e.prototype.layout=function(e,t){this.tree.layout(e,t)},e.prototype.style=function(e){this.tree.style(e)},e.prototype.getInput=function(){return this.root.element},e.prototype.setInput=function(e,t){return b(this,void 0,void 0,(function(){var i;return y(this,(function(o){switch(o.label){case 0:return this.refreshPromises.forEach((function(e){return e.cancel()})),this.refreshPromises.clear(),this.root.element=e,i=t&&{viewState:t,focus:[],selection:[]},[4,this._updateChildren(e,!0,!1,i)];case 1:return o.sent(),i&&(this.tree.setFocus(i.focus),this.tree.setSelection(i.selection)),t&&"number"===typeof t.scrollTop&&(this.scrollTop=t.scrollTop),[2]}}))}))},e.prototype._updateChildren=function(e,t,i,o){return void 0===e&&(e=this.root.element),void 0===t&&(t=!0),void 0===i&&(i=!1),b(this,void 0,void 0,(function(){var n;return y(this,(function(s){switch(s.label){case 0:if("undefined"===typeof this.root.element)throw new r["a"](this.user,"Tree input not set");return this.root.refreshPromise?[4,this.root.refreshPromise]:[3,3];case 1:return s.sent(),[4,a["b"].toPromise(this._onDidRender.event)];case 2:s.sent(),s.label=3;case 3:return n=this.getDataNode(e),[4,this.refreshAndRenderNode(n,t,o)];case 4:if(s.sent(),i)try{this.tree.rerender(n)}catch(l){}return[2]}}))}))},e.prototype.rerender=function(e){if(void 0!==e&&e!==this.root.element){var t=this.getDataNode(e);this.tree.rerender(t)}else this.tree.rerender()},e.prototype.collapse=function(e,t){void 0===t&&(t=!1);var i=this.getDataNode(e);return this.tree.collapse(i===this.root?null:i,t)},e.prototype.expand=function(e,t){return void 0===t&&(t=!1),b(this,void 0,void 0,(function(){var i,o;return y(this,(function(n){switch(n.label){case 0:if("undefined"===typeof this.root.element)throw new r["a"](this.user,"Tree input not set");return this.root.refreshPromise?[4,this.root.refreshPromise]:[3,3];case 1:return n.sent(),[4,a["b"].toPromise(this._onDidRender.event)];case 2:n.sent(),n.label=3;case 3:return i=this.getDataNode(e),this.tree.hasElement(i)&&!this.tree.isCollapsible(i)?[2,!1]:i.refreshPromise?[4,this.root.refreshPromise]:[3,6];case 4:return n.sent(),[4,a["b"].toPromise(this._onDidRender.event)];case 5:n.sent(),n.label=6;case 6:return i===this.root||i.refreshPromise||this.tree.isCollapsed(i)?(o=this.tree.expand(i===this.root?null:i,t),i.refreshPromise?[4,this.root.refreshPromise]:[3,9]):[2,!1];case 7:return n.sent(),[4,a["b"].toPromise(this._onDidRender.event)];case 8:n.sent(),n.label=9;case 9:return[2,o]}}))}))},e.prototype.setSelection=function(e,t){var i=this,o=e.map((function(e){return i.getDataNode(e)}));this.tree.setSelection(o,t)},e.prototype.getSelection=function(){var e=this.tree.getSelection();return e.map((function(e){return e.element}))},e.prototype.setFocus=function(e,t){var i=this,o=e.map((function(e){return i.getDataNode(e)}));this.tree.setFocus(o,t)},e.prototype.getFocus=function(){var e=this.tree.getFocus();return e.map((function(e){return e.element}))},e.prototype.reveal=function(e,t){this.tree.reveal(this.getDataNode(e),t)},e.prototype.getDataNode=function(e){var t=this.nodes.get(e===this.root.element?null:e);if(!t)throw new r["a"](this.user,"Data tree node not found: "+e);return t},e.prototype.refreshAndRenderNode=function(e,t,i){return b(this,void 0,void 0,(function(){return y(this,(function(o){switch(o.label){case 0:return[4,this.refreshNode(e,t,i)];case 1:return o.sent(),this.render(e,i),[2]}}))}))},e.prototype.refreshNode=function(e,t,i){return b(this,void 0,void 0,(function(){var o,n=this;return y(this,(function(r){return this.subTreeRefreshPromises.forEach((function(r,s){!o&&w(s,e)&&(o=r.then((function(){return n.refreshNode(e,t,i)})))})),o?[2,o]:[2,this.doRefreshSubTree(e,t,i)]}))}))},e.prototype.doRefreshSubTree=function(e,t,i){return b(this,void 0,void 0,(function(){var o,n,r=this;return y(this,(function(s){switch(s.label){case 0:e.refreshPromise=new Promise((function(e){return o=e})),this.subTreeRefreshPromises.set(e,e.refreshPromise),e.refreshPromise.finally((function(){e.refreshPromise=void 0,r.subTreeRefreshPromises.delete(e)})),s.label=1;case 1:return s.trys.push([1,,4,5]),[4,this.doRefreshNode(e,t,i)];case 2:return n=s.sent(),e.stale=!1,[4,Promise.all(n.map((function(e){return r.doRefreshSubTree(e,t,i)})))];case 3:return s.sent(),[3,5];case 4:return o(),[7];case 5:return[2]}}))}))},e.prototype.doRefreshNode=function(e,t,i){return b(this,void 0,void 0,(function(){var o,n,r,s,a=this;return y(this,(function(d){switch(d.label){case 0:e.hasChildren=!!this.dataSource.hasChildren(e.element),e.hasChildren?(n=Object(l["l"])(800),n.then((function(){e.slow=!0,a._onDidChangeNodeSlowState.fire(e)}),(function(e){return null})),o=this.doGetChildren(e).finally((function(){return n.cancel()}))):o=Promise.resolve([]),d.label=1;case 1:return d.trys.push([1,3,4,5]),[4,o];case 2:return r=d.sent(),[2,this.setChildren(e,r,t,i)];case 3:if(s=d.sent(),e!==this.root&&this.tree.collapse(e===this.root?null:e),Object(u["d"])(s))return[2,[]];throw s;case 4:return e.slow&&(e.slow=!1,this._onDidChangeNodeSlowState.fire(e)),[7];case 5:return[2]}}))}))},e.prototype.doGetChildren=function(e){var t=this,i=this.refreshPromises.get(e);return i||(i=Object(l["f"])((function(){return b(t,void 0,void 0,(function(){var t;return y(this,(function(i){switch(i.label){case 0:return[4,this.dataSource.getChildren(e.element)];case 1:return t=i.sent(),[2,this.processChildren(t)]}}))}))})),this.refreshPromises.set(e,i),i.finally((function(){return t.refreshPromises.delete(e)})))},e.prototype._onDidChangeCollapseState=function(e){var t=e.node,i=e.deep;!t.collapsed&&t.element.stale&&(i?this.collapse(t.element.element):this.refreshAndRenderNode(t.element,!1).catch(u["e"]))},e.prototype.setChildren=function(e,t,i,o){var n,r=this;if(0===e.children.length&&0===t.length)return[];for(var s=new Map,a=new Map,l=0,d=e.children;l<d.length;l++){var h=d[l];if(s.set(h.element,h),this.identityProvider){var u=this.tree.isCollapsed(h);a.set(h.id,{node:h,collapsed:u})}}for(var c=[],f=t.map((function(t){var n=!!r.dataSource.hasChildren(t);if(!r.identityProvider){var l=_({element:t,parent:e,hasChildren:n});return n&&r.collapseByDefault&&!r.collapseByDefault(t)&&(l.collapsedByDefault=!1,c.push(l)),l}var d=r.identityProvider.getId(t).toString(),h=a.get(d);if(h){l=h.node;return s.delete(l.element),r.nodes.delete(l.element),r.nodes.set(t,l),l.element=t,l.hasChildren=n,i?h.collapsed?(l.children.forEach((function(e){return I(e,(function(e){return r.nodes.delete(e.element)}))})),l.children.splice(0,l.children.length),l.stale=!0):c.push(l):n&&r.collapseByDefault&&!r.collapseByDefault(t)&&(l.collapsedByDefault=!1,c.push(l)),l}var u=_({element:t,parent:e,id:d,hasChildren:n});return o&&o.viewState.focus&&o.viewState.focus.indexOf(d)>-1&&o.focus.push(u),o&&o.viewState.selection&&o.viewState.selection.indexOf(d)>-1&&o.selection.push(u),o&&o.viewState.expanded&&o.viewState.expanded.indexOf(d)>-1?c.push(u):n&&r.collapseByDefault&&!r.collapseByDefault(t)&&(u.collapsedByDefault=!1,c.push(u)),u})),m=0,v=Object(p["e"])(s);m<v.length;m++){var b=v[m];I(b,(function(e){return r.nodes.delete(e.element)}))}for(var y=0,S=f;y<S.length;y++){h=S[y];this.nodes.set(h.element,h)}return(n=e.children).splice.apply(n,g([0,e.children.length],f)),e!==this.root&&this.autoExpandSingleChildren&&1===f.length&&0===c.length&&(f[0].collapsedByDefault=!1,c.push(f[0])),c},e.prototype.render=function(e,t){var i=this,o=e.children.map((function(e){return i.asTreeElement(e,t)}));this.tree.setChildren(e===this.root?null:e,o),e!==this.root&&this.tree.setCollapsible(e,e.hasChildren),this._onDidRender.fire()},e.prototype.asTreeElement=function(e,t){var i,o=this;return e.stale?{element:e,collapsible:e.hasChildren,collapsed:!0}:(i=!(t&&t.viewState.expanded&&e.id&&t.viewState.expanded.indexOf(e.id)>-1)&&e.collapsedByDefault,e.collapsedByDefault=void 0,{element:e,children:e.hasChildren?d["d"].map(d["d"].fromArray(e.children),(function(e){return o.asTreeElement(e,t)})):[],collapsible:e.hasChildren,collapsed:i})},e.prototype.processChildren=function(e){return this.sorter&&e.sort(this.sorter.compare.bind(this.sorter)),e},e.prototype.dispose=function(){this.disposables.dispose()},e}(),T=function(){function e(e){this.node=e}return Object.defineProperty(e.prototype,"element",{get:function(){return{elements:this.node.element.elements.map((function(e){return e.element})),incompressible:this.node.element.incompressible}},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"children",{get:function(){return this.node.children.map((function(t){return new e(t)}))},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"depth",{get:function(){return this.node.depth},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"visibleChildrenCount",{get:function(){return this.node.visibleChildrenCount},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"visibleChildIndex",{get:function(){return this.node.visibleChildIndex},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"collapsible",{get:function(){return this.node.collapsible},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"collapsed",{get:function(){return this.node.collapsed},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"visible",{get:function(){return this.node.visible},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"filterData",{get:function(){return this.node.filterData},enumerable:!0,configurable:!0}),e}(),x=function(){function e(e,t,i,o){this.renderer=e,this.nodeMapper=t,this.compressibleNodeMapperProvider=i,this.onDidChangeTwistieState=o,this.renderedNodes=new Map,this.disposables=[],this.templateId=e.templateId}return e.prototype.renderTemplate=function(e){var t=this.renderer.renderTemplate(e);return{templateData:t}},e.prototype.renderElement=function(e,t,i,o){this.renderer.renderElement(this.nodeMapper.map(e),t,i.templateData,o)},e.prototype.renderCompressedElements=function(e,t,i,o){this.renderer.renderCompressedElements(this.compressibleNodeMapperProvider().map(e),t,i.templateData,o)},e.prototype.renderTwistie=function(e,t){return Object(c["Y"])(t,"codicon-loading",e.slow),!1},e.prototype.disposeElement=function(e,t,i,o){this.renderer.disposeElement&&this.renderer.disposeElement(this.nodeMapper.map(e),t,i.templateData,o)},e.prototype.disposeCompressedElements=function(e,t,i,o){this.renderer.disposeCompressedElements&&this.renderer.disposeCompressedElements(this.compressibleNodeMapperProvider().map(e),t,i.templateData,o)},e.prototype.disposeTemplate=function(e){this.renderer.disposeTemplate(e.templateData)},e.prototype.dispose=function(){this.renderedNodes.clear(),this.disposables=Object(s["f"])(this.disposables)},e}();function M(e){var t=e&&E(e);return t&&v(v({},t),{keyboardNavigationLabelProvider:t.keyboardNavigationLabelProvider&&v(v({},t.keyboardNavigationLabelProvider),{getCompressedNodeKeyboardNavigationLabel:function(t){return e.keyboardNavigationLabelProvider.getCompressedNodeKeyboardNavigationLabel(t.map((function(e){return e.element})))}})})}var L=function(e){function t(t,i,o,n,s,a,l){void 0===l&&(l={});var d=e.call(this,t,i,o,s,a,l)||this;return d.compressionDelegate=n,d.compressibleNodeMapper=new r["b"]((function(e){return new T(e)})),d.filter=l.filter,d}return m(t,e),t.prototype.createTree=function(e,t,i,r,s){var a=this,l=new o["b"](i),d=r.map((function(e){return new x(e,a.nodeMapper,(function(){return a.compressibleNodeMapper}),a._onDidChangeNodeSlowState.event)})),h=M(s)||{};return new n["a"](e,t,l,d,h)},t.prototype.asTreeElement=function(t,i){return v({incompressible:this.compressionDelegate.isIncompressible(t.element)},e.prototype.asTreeElement.call(this,t,i))},t.prototype.updateOptions=function(e){void 0===e&&(e={}),this.tree.updateOptions(e)},t.prototype.render=function(t,i){var o=this;if(!this.identityProvider)return e.prototype.render.call(this,t,i);var n=function(e){return o.identityProvider.getId(e).toString()},r=function(e){for(var t=new Set,i=0,r=e;i<r.length;i++){var s=r[i],a=o.tree.getCompressedTreeNode(s===o.root?null:s);if(a.element)for(var l=0,d=a.element.elements;l<d.length;l++){var h=d[l];t.add(n(h.element))}}return t},s=r(this.tree.getSelection()),a=r(this.tree.getFocus());e.prototype.render.call(this,t,i);var l=this.getSelection(),d=!1,h=this.getFocus(),u=!1,c=function(e){var t=e.element;if(t)for(var i=0;i<t.elements.length;i++){var o=n(t.elements[i].element),r=t.elements[t.elements.length-1].element;s.has(o)&&-1===l.indexOf(r)&&(l.push(r),d=!0),a.has(o)&&-1===h.indexOf(r)&&(h.push(r),u=!0)}e.children.forEach(c)};c(this.tree.getCompressedTreeNode(t===this.root?null:t)),d&&this.setSelection(l),u&&this.setFocus(h)},t.prototype.processChildren=function(t){var i=this;return this.filter&&(t=t.filter((function(e){var t=i.filter.filter(e,1),o=R(t);if(2===o)throw new Error("Recursive tree visibility not supported in async data compressed trees");return 1===o}))),e.prototype.processChildren.call(this,t)},t}(j);function R(e){return"boolean"===typeof e?e?1:0:Object(f["c"])(e)?Object(f["b"])(e.visibility):Object(f["b"])(e)}},"63d5":function(e,t,i){"use strict";i.d(t,"b",(function(){return C})),i.d(t,"a",(function(){return P}));var o=i("c482"),n=i("9d1c"),r=i("258a"),s=i("308f"),a=i("c8ac"),l=function(){return l=Object.assign||function(e){for(var t,i=1,o=arguments.length;i<o;i++)for(var n in t=arguments[i],t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},l.apply(this,arguments)};function d(e){var t=[e.element],i=e.incompressible||!1;return{element:{elements:t,incompressible:i},children:r["d"].map(r["d"].from(e.children),d),collapsible:e.collapsible,collapsed:e.collapsed}}function h(e){var t,i,o=[e.element],n=e.incompressible||!1;while(1){if(t=r["d"].from(e.children),i=r["d"].collect(t,2),1!==i.length)break;if(e=i[0],e.incompressible)break;o.push(e.element)}return{element:{elements:o,incompressible:n},children:r["d"].map(r["d"].concat(r["d"].fromArray(i),t),h),collapsible:e.collapsible,collapsed:e.collapsed}}function u(e,t){var i;return void 0===t&&(t=0),i=t<e.element.elements.length-1?r["d"].single(u(e,t+1)):r["d"].map(r["d"].from(e.children),(function(e){return u(e,0)})),0===t&&e.element.incompressible?{element:e.element.elements[t],children:i,incompressible:!0,collapsible:e.collapsible,collapsed:e.collapsed}:{element:e.element.elements[t],children:i,collapsible:e.collapsible,collapsed:e.collapsed}}function c(e){return u(e,0)}function p(e,t,i){return e.element===t?l(l({},e),{children:i}):l(l({},e),{children:r["d"].map(r["d"].from(e.children),(function(e){return p(e,t,i)}))})}var f=function(){function e(e,t,i){void 0===i&&(i={}),this.user=e,this.nodes=new Map,this.model=new n["a"](e,t,i),this.enabled="undefined"===typeof i.compressionEnabled||i.compressionEnabled}return Object.defineProperty(e.prototype,"onDidSplice",{get:function(){return this.model.onDidSplice},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"onDidChangeCollapseState",{get:function(){return this.model.onDidChangeCollapseState},enumerable:!0,configurable:!0}),e.prototype.setChildren=function(e,t){if(null!==e){var i=this.nodes.get(e);if(!i)throw new Error("Unknown compressed tree node");var o=this.model.getNode(i),n=this.model.getParentNodeLocation(i),s=this.model.getNode(n),a=c(o),l=p(a,e,r["d"].from(t)),u=(this.enabled?h:d)(l),f=s.children.map((function(e){return e===o?u:e}));this._setChildren(s.element,f)}else{var m=r["d"].map(r["d"].from(t),this.enabled?h:d);this._setChildren(null,m)}},e.prototype.setCompressionEnabled=function(e){if(e!==this.enabled){this.enabled=e;var t=this.model.getNode(),i=r["d"].from(t.children),o=r["d"].map(i,c),n=r["d"].map(o,e?h:d);this._setChildren(null,n)}},e.prototype._setChildren=function(e,t){var i=this,o=new Set,n=function(e){for(var t=0,n=e.element.elements;t<n.length;t++){var r=n[t];o.add(r),i.nodes.set(r,e.element)}},r=function(e){for(var t=0,n=e.element.elements;t<n.length;t++){var r=n[t];o.has(r)||i.nodes.delete(r)}};this.model.setChildren(e,t,n,r)},e.prototype.has=function(e){return this.nodes.has(e)},e.prototype.getListIndex=function(e){var t=this.getCompressedNode(e);return this.model.getListIndex(t)},e.prototype.getListRenderCount=function(e){var t=this.getCompressedNode(e);return this.model.getListRenderCount(t)},e.prototype.getNode=function(e){if("undefined"===typeof e)return this.model.getNode();var t=this.getCompressedNode(e);return this.model.getNode(t)},e.prototype.getNodeLocation=function(e){var t=this.model.getNodeLocation(e);return null===t?null:t.elements[t.elements.length-1]},e.prototype.getParentNodeLocation=function(e){var t=this.getCompressedNode(e),i=this.model.getParentNodeLocation(t);return null===i?null:i.elements[i.elements.length-1]},e.prototype.isCollapsible=function(e){var t=this.getCompressedNode(e);return this.model.isCollapsible(t)},e.prototype.setCollapsible=function(e,t){var i=this.getCompressedNode(e);return this.model.setCollapsible(i,t)},e.prototype.isCollapsed=function(e){var t=this.getCompressedNode(e);return this.model.isCollapsed(t)},e.prototype.setCollapsed=function(e,t,i){var o=this.getCompressedNode(e);return this.model.setCollapsed(o,t,i)},e.prototype.expandTo=function(e){var t=this.getCompressedNode(e);this.model.expandTo(t)},e.prototype.rerender=function(e){var t=this.getCompressedNode(e);this.model.rerender(t)},e.prototype.refilter=function(){this.model.refilter()},e.prototype.getCompressedNode=function(e){if(null===e)return null;var t=this.nodes.get(e);if(!t)throw new a["a"](this.user,"Tree element not found: "+e);return t},e}(),m=function(e){return e[e.length-1]},v=function(){function e(e,t){this.unwrapper=e,this.node=t}return Object.defineProperty(e.prototype,"element",{get:function(){return null===this.node.element?null:this.unwrapper(this.node.element)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"children",{get:function(){var t=this;return this.node.children.map((function(i){return new e(t.unwrapper,i)}))},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"depth",{get:function(){return this.node.depth},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"visibleChildrenCount",{get:function(){return this.node.visibleChildrenCount},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"visibleChildIndex",{get:function(){return this.node.visibleChildIndex},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"collapsible",{get:function(){return this.node.collapsible},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"collapsed",{get:function(){return this.node.collapsed},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"visible",{get:function(){return this.node.visible},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"filterData",{get:function(){return this.node.filterData},enumerable:!0,configurable:!0}),e}();function b(e,t){return{splice:function(i,o,n){t.splice(i,o,n.map((function(t){return e.map(t)})))}}}function y(e,t){return l(l({},t),{sorter:t.sorter&&{compare:function(e,i){return t.sorter.compare(e.elements[0],i.elements[0])}},identityProvider:t.identityProvider&&{getId:function(i){return t.identityProvider.getId(e(i))}},filter:t.filter&&{filter:function(i,o){return t.filter.filter(e(i),o)}}})}var g=function(){function e(e,t,i){var o=this;void 0===i&&(i={}),this.elementMapper=i.elementMapper||m;var n=function(e){return o.elementMapper(e.elements)};this.nodeMapper=new a["b"]((function(e){return new v(n,e)})),this.model=new f(e,b(this.nodeMapper,t),y(n,i))}return Object.defineProperty(e.prototype,"onDidSplice",{get:function(){var e=this;return s["b"].map(this.model.onDidSplice,(function(t){var i=t.insertedNodes,o=t.deletedNodes;return{insertedNodes:i.map((function(t){return e.nodeMapper.map(t)})),deletedNodes:o.map((function(t){return e.nodeMapper.map(t)}))}}))},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"onDidChangeCollapseState",{get:function(){var e=this;return s["b"].map(this.model.onDidChangeCollapseState,(function(t){var i=t.node,o=t.deep;return{node:e.nodeMapper.map(i),deep:o}}))},enumerable:!0,configurable:!0}),e.prototype.setChildren=function(e,t){this.model.setChildren(e,t)},e.prototype.setCompressionEnabled=function(e){this.model.setCompressionEnabled(e)},e.prototype.has=function(e){return this.model.has(e)},e.prototype.getListIndex=function(e){return this.model.getListIndex(e)},e.prototype.getListRenderCount=function(e){return this.model.getListRenderCount(e)},e.prototype.getNode=function(e){return this.nodeMapper.map(this.model.getNode(e))},e.prototype.getNodeLocation=function(e){return e.element},e.prototype.getParentNodeLocation=function(e){return this.model.getParentNodeLocation(e)},e.prototype.isCollapsible=function(e){return this.model.isCollapsible(e)},e.prototype.setCollapsible=function(e,t){return this.model.setCollapsible(e,t)},e.prototype.isCollapsed=function(e){return this.model.isCollapsed(e)},e.prototype.setCollapsed=function(e,t,i){return this.model.setCollapsed(e,t,i)},e.prototype.expandTo=function(e){return this.model.expandTo(e)},e.prototype.rerender=function(e){return this.model.rerender(e)},e.prototype.refilter=function(){return this.model.refilter()},e.prototype.getCompressedTreeNode=function(e){return void 0===e&&(e=null),this.model.getNode(e)},e}(),_=i("6424"),S=function(){var e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])},e(t,i)};return function(t,i){function o(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(o.prototype=i.prototype,new o)}}(),w=function(){return w=Object.assign||function(e){for(var t,i=1,o=arguments.length;i<o;i++)for(var n in t=arguments[i],t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},w.apply(this,arguments)},N=function(e,t,i,o){var n,r=arguments.length,s=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,i):o;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)s=Reflect.decorate(e,t,i,o);else for(var a=e.length-1;a>=0;a--)(n=e[a])&&(s=(r<3?n(s):r>3?n(t,i,s):n(t,i))||s);return r>3&&s&&Object.defineProperty(t,i,s),s},C=function(e){function t(t,i,o,n,r){return void 0===r&&(r={}),e.call(this,t,i,o,n,r)||this}return S(t,e),Object.defineProperty(t.prototype,"onDidChangeCollapseState",{get:function(){return this.model.onDidChangeCollapseState},enumerable:!0,configurable:!0}),t.prototype.setChildren=function(e,t){this.model.setChildren(e,t)},t.prototype.rerender=function(e){void 0!==e?this.model.rerender(e):this.view.rerender()},t.prototype.hasElement=function(e){return this.model.has(e)},t.prototype.createModel=function(e,t,i){return new n["a"](e,t,i)},t}(o["a"]),D=function(){function e(e,t){this._compressedTreeNodeProvider=e,this.renderer=t,this.templateId=t.templateId,t.onDidChangeTwistieState&&(this.onDidChangeTwistieState=t.onDidChangeTwistieState)}return Object.defineProperty(e.prototype,"compressedTreeNodeProvider",{get:function(){return this._compressedTreeNodeProvider()},enumerable:!0,configurable:!0}),e.prototype.renderTemplate=function(e){var t=this.renderer.renderTemplate(e);return{compressedTreeNode:void 0,data:t}},e.prototype.renderElement=function(e,t,i,o){var n=this.compressedTreeNodeProvider.getCompressedTreeNode(e.element);1===n.element.elements.length?(i.compressedTreeNode=void 0,this.renderer.renderElement(e,t,i.data,o)):(i.compressedTreeNode=n,this.renderer.renderCompressedElements(n,t,i.data,o))},e.prototype.disposeElement=function(e,t,i,o){i.compressedTreeNode?this.renderer.disposeCompressedElements&&this.renderer.disposeCompressedElements(i.compressedTreeNode,t,i.data,o):this.renderer.disposeElement&&this.renderer.disposeElement(e,t,i.data,o)},e.prototype.disposeTemplate=function(e){this.renderer.disposeTemplate(e.data)},e.prototype.renderTwistie=function(e,t){this.renderer.renderTwistie&&this.renderer.renderTwistie(e,t)},N([_["a"]],e.prototype,"compressedTreeNodeProvider",null),e}();function O(e,t){return t&&w(w({},t),{keyboardNavigationLabelProvider:t.keyboardNavigationLabelProvider&&{getKeyboardNavigationLabel:function(i){var o;try{o=e().getCompressedTreeNode(i)}catch(n){return t.keyboardNavigationLabelProvider.getKeyboardNavigationLabel(i)}return 1===o.element.elements.length?t.keyboardNavigationLabelProvider.getKeyboardNavigationLabel(i):t.keyboardNavigationLabelProvider.getCompressedNodeKeyboardNavigationLabel(o.element.elements)}}})}var P=function(e){function t(t,i,o,n,r){void 0===r&&(r={});var s=this,a=function(){return s},l=n.map((function(e){return new D(a,e)}));return s=e.call(this,t,i,o,l,O(a,r))||this,s}return S(t,e),t.prototype.setChildren=function(e,t){this.model.setChildren(e,t)},t.prototype.createModel=function(e,t,i){return new g(e,t,i)},t.prototype.updateOptions=function(t){void 0===t&&(t={}),e.prototype.updateOptions.call(this,t),"undefined"!==typeof t.compressionEnabled&&this.model.setCompressionEnabled(t.compressionEnabled)},t.prototype.getCompressedTreeNode=function(e){return void 0===e&&(e=null),this.model.getCompressedTreeNode(e)},t}(C)},"70c3":function(e,t,i){"use strict";i.d(t,"a",(function(){return m}));i("ba77");var o=i("a666"),n=i("0f70"),r=i("30db"),s=i("ef8e"),a=i("a60f"),l=i("5d28"),d=i("308f"),h=i("11f7"),u=i("e32d"),c=function(){var e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])},e(t,i)};return function(t,i){function o(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(o.prototype=i.prototype,new o)}}(),p=function(){for(var e=0,t=0,i=arguments.length;t<i;t++)e+=arguments[t].length;var o=Array(e),n=0;for(t=0;t<i;t++)for(var r=arguments[t],s=0,a=r.length;s<a;s++,n++)o[n]=r[s];return o},f=!1,m=function(e){function t(t,i,s){void 0===s&&(s={});var l=e.call(this)||this;return l._state=3,l._onDidEnablementChange=l._register(new d["a"]),l.onDidEnablementChange=l._onDidEnablementChange.event,l._onDidStart=l._register(new d["a"]),l.onDidStart=l._onDidStart.event,l._onDidChange=l._register(new d["a"]),l.onDidChange=l._onDidChange.event,l._onDidReset=l._register(new d["a"]),l.onDidReset=l._onDidReset.event,l._onDidEnd=l._register(new d["a"]),l.onDidEnd=l._onDidEnd.event,l.linkedSash=void 0,l.orthogonalStartSashDisposables=l._register(new o["b"]),l.orthogonalEndSashDisposables=l._register(new o["b"]),l.el=Object(h["q"])(t,Object(h["a"])(".monaco-sash")),r["e"]&&Object(h["f"])(l.el,"mac"),l._register(Object(u["a"])(l.el,"mousedown")(l.onMouseDown,l)),l._register(Object(u["a"])(l.el,"dblclick")(l.onMouseDoubleClick,l)),l._register(a["b"].addTarget(l.el)),l._register(Object(u["a"])(l.el,a["a"].Start)(l.onTouchStart,l)),n["j"]&&Object(h["f"])(l.el,"touch"),l.setOrientation(s.orientation||0),l.hidden=!1,l.layoutProvider=i,l.orthogonalStartSash=s.orthogonalStartSash,l.orthogonalEndSash=s.orthogonalEndSash,Object(h["Y"])(l.el,"debug",f),l}return c(t,e),Object.defineProperty(t.prototype,"state",{get:function(){return this._state},set:function(e){this._state!==e&&(Object(h["Y"])(this.el,"disabled",0===e),Object(h["Y"])(this.el,"minimum",1===e),Object(h["Y"])(this.el,"maximum",2===e),this._state=e,this._onDidEnablementChange.fire(e))},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"orthogonalStartSash",{get:function(){return this._orthogonalStartSash},set:function(e){this.orthogonalStartSashDisposables.clear(),e?(this.orthogonalStartSashDisposables.add(e.onDidEnablementChange(this.onOrthogonalStartSashEnablementChange,this)),this.onOrthogonalStartSashEnablementChange(e.state)):this.onOrthogonalStartSashEnablementChange(0),this._orthogonalStartSash=e},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"orthogonalEndSash",{get:function(){return this._orthogonalEndSash},set:function(e){this.orthogonalEndSashDisposables.clear(),e?(this.orthogonalEndSashDisposables.add(e.onDidEnablementChange(this.onOrthogonalEndSashEnablementChange,this)),this.onOrthogonalEndSashEnablementChange(e.state)):this.onOrthogonalEndSashEnablementChange(0),this._orthogonalEndSash=e},enumerable:!0,configurable:!0}),t.prototype.setOrientation=function(e){this.orientation=e,1===this.orientation?(Object(h["f"])(this.el,"horizontal"),Object(h["P"])(this.el,"vertical")):(Object(h["P"])(this.el,"horizontal"),Object(h["f"])(this.el,"vertical")),this.layoutProvider&&this.layout()},t.prototype.onMouseDown=function(e){var t=this;h["c"].stop(e,!1);var i=!1;if(!e.__orthogonalSashEvent){var n=this.getOrthogonalSash(e);n&&(i=!0,e.__orthogonalSashEvent=!0,n.onMouseDown(e))}if(this.linkedSash&&!e.__linkedSashEvent&&(e.__linkedSashEvent=!0,this.linkedSash.onMouseDown(e)),this.state){for(var s=p(Object(h["D"])("iframe"),Object(h["D"])("webview")),a=0,d=s;a<d.length;a++){var c=d[a];c.style.pointerEvents="none"}var f=new l["b"](e),m=f.posx,v=f.posy,b=f.altKey,y={startX:m,currentX:m,startY:v,currentY:v,altKey:b};Object(h["f"])(this.el,"active"),this._onDidStart.fire(y);var g=Object(h["w"])(this.el),_=function(){var e="";e=i?"all-scroll":1===t.orientation?1===t.state?"s-resize":2===t.state?"n-resize":r["e"]?"row-resize":"ns-resize":1===t.state?"e-resize":2===t.state?"w-resize":r["e"]?"col-resize":"ew-resize",g.innerHTML="* { cursor: "+e+" !important; }"},S=new o["b"];_(),i||this.onDidEnablementChange(_,null,S);var w=function(e){h["c"].stop(e,!1);var i=new l["b"](e),o={startX:m,currentX:i.posx,startY:v,currentY:i.posy,altKey:b};t._onDidChange.fire(o)},N=function(e){h["c"].stop(e,!1),t.el.removeChild(g),Object(h["P"])(t.el,"active"),t._onDidEnd.fire(),S.dispose();for(var i=0,o=s;i<o.length;i++){var n=o[i];n.style.pointerEvents="auto"}};Object(u["a"])(window,"mousemove")(w,null,S),Object(u["a"])(window,"mouseup")(N,null,S)}},t.prototype.onMouseDoubleClick=function(e){var t=this.getOrthogonalSash(e);t&&t._onDidReset.fire(),this.linkedSash&&this.linkedSash._onDidReset.fire(),this._onDidReset.fire()},t.prototype.onTouchStart=function(e){var t=this;h["c"].stop(e);var i=[],n=e.pageX,r=e.pageY,l=e.altKey;this._onDidStart.fire({startX:n,currentX:n,startY:r,currentY:r,altKey:l}),i.push(Object(h["j"])(this.el,a["a"].Change,(function(e){s["h"](e.pageX)&&s["h"](e.pageY)&&t._onDidChange.fire({startX:n,currentX:e.pageX,startY:r,currentY:e.pageY,altKey:l})}))),i.push(Object(h["j"])(this.el,a["a"].End,(function(e){t._onDidEnd.fire(),Object(o["f"])(i)})))},t.prototype.layout=function(){var e=n["j"]?20:4;if(0===this.orientation){var t=this.layoutProvider;this.el.style.left=t.getVerticalSashLeft(this)-e/2+"px",t.getVerticalSashTop&&(this.el.style.top=t.getVerticalSashTop(this)+"px"),t.getVerticalSashHeight&&(this.el.style.height=t.getVerticalSashHeight(this)+"px")}else{var i=this.layoutProvider;this.el.style.top=i.getHorizontalSashTop(this)-e/2+"px",i.getHorizontalSashLeft&&(this.el.style.left=i.getHorizontalSashLeft(this)+"px"),i.getHorizontalSashWidth&&(this.el.style.width=i.getHorizontalSashWidth(this)+"px")}},t.prototype.hide=function(){this.hidden=!0,this.el.style.display="none",this.el.setAttribute("aria-hidden","true")},t.prototype.onOrthogonalStartSashEnablementChange=function(e){Object(h["Y"])(this.el,"orthogonal-start",0!==e)},t.prototype.onOrthogonalEndSashEnablementChange=function(e){Object(h["Y"])(this.el,"orthogonal-end",0!==e)},t.prototype.getOrthogonalSash=function(e){if(0===this.orientation){if(e.offsetY<=4)return this.orthogonalStartSash;if(e.offsetY>=this.el.clientHeight-4)return this.orthogonalEndSash}else{if(e.offsetX<=4)return this.orthogonalStartSash;if(e.offsetX>=this.el.clientWidth-4)return this.orthogonalEndSash}},t.prototype.dispose=function(){e.prototype.dispose.call(this),this.el.remove()},t}(o["a"])},"7aad":function(e,t,i){},"9d1c":function(e,t,i){"use strict";i.d(t,"a",(function(){return d}));var o=i("258a"),n=i("c34a"),r=i("c8ac"),s=i("e8e3"),a=function(){return a=Object.assign||function(e){for(var t,i=1,o=arguments.length;i<o;i++)for(var n in t=arguments[i],t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},a.apply(this,arguments)},l=function(){for(var e=0,t=0,i=arguments.length;t<i;t++)e+=arguments[t].length;var o=Array(e),n=0;for(t=0;t<i;t++)for(var r=arguments[t],s=0,a=r.length;s<a;s++,n++)o[n]=r[s];return o},d=function(){function e(e,t,i){void 0===i&&(i={}),this.user=e,this.nodes=new Map,this.nodesByIdentity=new Map,this.model=new n["a"](e,t,null,i),this.onDidSplice=this.model.onDidSplice,this.onDidChangeCollapseState=this.model.onDidChangeCollapseState,this.onDidChangeRenderNodeCount=this.model.onDidChangeRenderNodeCount,i.sorter&&(this.sorter={compare:function(e,t){return i.sorter.compare(e.element,t.element)}}),this.identityProvider=i.identityProvider}return e.prototype.setChildren=function(e,t,i,o){var n=this.getElementLocation(e);this._setChildren(n,this.preserveCollapseState(t),i,o)},e.prototype._setChildren=function(e,t,i,o){var n=this,r=new Set,s=new Set,a=function(e){if(r.add(e.element),n.nodes.set(e.element,e),n.identityProvider){var t=n.identityProvider.getId(e.element).toString();s.add(t),n.nodesByIdentity.set(t,e)}i&&i(e)},d=function(e){if(r.has(e.element)||n.nodes.delete(e.element),n.identityProvider){var t=n.identityProvider.getId(e.element).toString();s.has(t)||n.nodesByIdentity.delete(t)}o&&o(e)};this.model.splice(l(e,[0]),Number.MAX_VALUE,t,a,d)},e.prototype.preserveCollapseState=function(e){var t=this,i=e?Object(o["f"])(e):o["d"].empty();return this.sorter&&(i=o["d"].fromArray(Object(s["r"])(o["d"].collect(i),this.sorter.compare.bind(this.sorter)))),o["d"].map(i,(function(e){var i=t.nodes.get(e.element);if(!i&&t.identityProvider){var o=t.identityProvider.getId(e.element).toString();i=t.nodesByIdentity.get(o)}if(!i)return a(a({},e),{children:t.preserveCollapseState(e.children)});var n="boolean"===typeof e.collapsible?e.collapsible:i.collapsible,r="undefined"!==typeof e.collapsed?e.collapsed:i.collapsed;return a(a({},e),{collapsible:n,collapsed:r,children:t.preserveCollapseState(e.children)})}))},e.prototype.rerender=function(e){var t=this.getElementLocation(e);this.model.rerender(t)},e.prototype.has=function(e){return this.nodes.has(e)},e.prototype.getListIndex=function(e){var t=this.getElementLocation(e);return this.model.getListIndex(t)},e.prototype.getListRenderCount=function(e){var t=this.getElementLocation(e);return this.model.getListRenderCount(t)},e.prototype.isCollapsible=function(e){var t=this.getElementLocation(e);return this.model.isCollapsible(t)},e.prototype.setCollapsible=function(e,t){var i=this.getElementLocation(e);return this.model.setCollapsible(i,t)},e.prototype.isCollapsed=function(e){var t=this.getElementLocation(e);return this.model.isCollapsed(t)},e.prototype.setCollapsed=function(e,t,i){var o=this.getElementLocation(e);return this.model.setCollapsed(o,t,i)},e.prototype.expandTo=function(e){var t=this.getElementLocation(e);this.model.expandTo(t)},e.prototype.refilter=function(){this.model.refilter()},e.prototype.getNode=function(e){if(void 0===e&&(e=null),null===e)return this.model.getNode(this.model.rootRef);var t=this.nodes.get(e);if(!t)throw new r["a"](this.user,"Tree element not found: "+e);return t},e.prototype.getNodeLocation=function(e){return e.element},e.prototype.getParentNodeLocation=function(e){if(null===e)throw new r["a"](this.user,"Invalid getParentNodeLocation call");var t=this.nodes.get(e);if(!t)throw new r["a"](this.user,"Tree element not found: "+e);var i=this.model.getNodeLocation(t),o=this.model.getParentNodeLocation(i),n=this.model.getNode(o);return n.element},e.prototype.getElementLocation=function(e){if(null===e)return[];var t=this.nodes.get(e);if(!t)throw new r["a"](this.user,"Tree element not found: "+e);return this.model.getNodeLocation(t)},e}()},ba77:function(e,t,i){},c34a:function(e,t,i){"use strict";i.d(t,"c",(function(){return l})),i.d(t,"b",(function(){return d})),i.d(t,"a",(function(){return u}));var o=i("c8ac"),n=i("e8e3"),r=i("308f"),s=i("258a"),a=function(){for(var e=0,t=0,i=arguments.length;t<i;t++)e+=arguments[t].length;var o=Array(e),n=0;for(t=0;t<i;t++)for(var r=arguments[t],s=0,a=r.length;s<a;s++,n++)o[n]=r[s];return o};function l(e){return"object"===typeof e&&"visibility"in e&&"data"in e}function d(e){switch(e){case!0:return 1;case!1:return 0;default:return e}}function h(e){return"boolean"===typeof e.collapsible}var u=function(){function e(e,t,i,o){void 0===o&&(o={}),this.user=e,this.list=t,this.rootRef=[],this.eventBufferer=new r["c"],this._onDidChangeCollapseState=new r["a"],this.onDidChangeCollapseState=this.eventBufferer.wrapEvent(this._onDidChangeCollapseState.event),this._onDidChangeRenderNodeCount=new r["a"],this.onDidChangeRenderNodeCount=this.eventBufferer.wrapEvent(this._onDidChangeRenderNodeCount.event),this._onDidSplice=new r["a"],this.onDidSplice=this._onDidSplice.event,this.collapseByDefault="undefined"!==typeof o.collapseByDefault&&o.collapseByDefault,this.filter=o.filter,this.autoExpandSingleChildren="undefined"!==typeof o.autoExpandSingleChildren&&o.autoExpandSingleChildren,this.root={parent:void 0,element:i,children:[],depth:0,visibleChildrenCount:0,visibleChildIndex:-1,collapsible:!1,collapsed:!1,renderNodeCount:0,visible:!0,filterData:void 0}}return e.prototype.splice=function(e,t,i,n,r){var l,d=this;if(0===e.length)throw new o["a"](this.user,"Invalid tree location");for(var h=this.getParentNodeWithListIndex(e),u=h.parentNode,c=h.listIndex,p=h.revealed,f=h.visible,m=[],v=s["d"].map(s["d"].from(i),(function(e){return d.createTreeNode(e,u,u.visible?1:0,p,m,n)})),b=e[e.length-1],y=0,g=b;g>=0&&g<u.children.length;g--){var _=u.children[g];if(_.visible){y=_.visibleChildIndex;break}}var S=[],w=0,N=0;s["d"].forEach(v,(function(e){S.push(e),N+=e.renderNodeCount,e.visible&&(e.visibleChildIndex=y+w++)}));for(var C=(l=u.children).splice.apply(l,a([b,t],S)),D=0,O=0,P=C;O<P.length;O++){_=P[O];_.visible&&D++}if(0!==D)for(g=b+S.length;g<u.children.length;g++){_=u.children[g];_.visible&&(_.visibleChildIndex-=D)}if(u.visibleChildrenCount+=w-D,p&&f){var z=C.reduce((function(e,t){return e+(t.visible?t.renderNodeCount:0)}),0);this._updateAncestorsRenderNodeCount(u,N-z),this.list.splice(c,z,m)}if(C.length>0&&r){var E=function(e){r(e),e.children.forEach(E)};C.forEach(E)}this._onDidSplice.fire({insertedNodes:S,deletedNodes:C})},e.prototype.rerender=function(e){if(0===e.length)throw new o["a"](this.user,"Invalid tree location");var t=this.getTreeNodeWithListIndex(e),i=t.node,n=t.listIndex,r=t.revealed;r&&this.list.splice(n,1,[i])},e.prototype.has=function(e){return this.hasTreeNode(e)},e.prototype.getListIndex=function(e){var t=this.getTreeNodeWithListIndex(e),i=t.listIndex,o=t.visible,n=t.revealed;return o&&n?i:-1},e.prototype.getListRenderCount=function(e){return this.getTreeNode(e).renderNodeCount},e.prototype.isCollapsible=function(e){return this.getTreeNode(e).collapsible},e.prototype.setCollapsible=function(e,t){var i=this,o=this.getTreeNode(e);"undefined"===typeof t&&(t=!o.collapsible);var n={collapsible:t};return this.eventBufferer.bufferEvents((function(){return i._setCollapseState(e,n)}))},e.prototype.isCollapsed=function(e){return this.getTreeNode(e).collapsed},e.prototype.setCollapsed=function(e,t,i){var o=this,n=this.getTreeNode(e);"undefined"===typeof t&&(t=!n.collapsed);var r={collapsed:t,recursive:i||!1};return this.eventBufferer.bufferEvents((function(){return o._setCollapseState(e,r)}))},e.prototype._setCollapseState=function(e,t){var i=this.getTreeNodeWithListIndex(e),o=i.node,n=i.listIndex,r=i.revealed,s=this._setListNodeCollapseState(o,n,r,t);if(o!==this.root&&this.autoExpandSingleChildren&&s&&!h(t)&&o.collapsible&&!o.collapsed&&!t.recursive){for(var l=-1,d=0;d<o.children.length;d++){var u=o.children[d];if(u.visible){if(l>-1){l=-1;break}l=d}}l>-1&&this._setCollapseState(a(e,[l]),t)}return s},e.prototype._setListNodeCollapseState=function(e,t,i,o){var n=this._setNodeCollapseState(e,o,!1);if(!i||!e.visible||!n)return n;var r=e.renderNodeCount,s=this.updateNodeAfterCollapseChange(e),a=r-(-1===t?0:1);return this.list.splice(t+1,a,s.slice(1)),n},e.prototype._setNodeCollapseState=function(e,t,i){var o;if(e===this.root?o=!1:(h(t)?(o=e.collapsible!==t.collapsible,e.collapsible=t.collapsible):e.collapsible?(o=e.collapsed!==t.collapsed,e.collapsed=t.collapsed):o=!1,o&&this._onDidChangeCollapseState.fire({node:e,deep:i})),!h(t)&&t.recursive)for(var n=0,r=e.children;n<r.length;n++){var s=r[n];o=this._setNodeCollapseState(s,t,!0)||o}return o},e.prototype.expandTo=function(e){var t=this;this.eventBufferer.bufferEvents((function(){var i=t.getTreeNode(e);while(i.parent)i=i.parent,e=e.slice(0,e.length-1),i.collapsed&&t._setCollapseState(e,{collapsed:!1,recursive:!1})}))},e.prototype.refilter=function(){var e=this.root.renderNodeCount,t=this.updateNodeAfterFilterChange(this.root);this.list.splice(0,e,t)},e.prototype.createTreeNode=function(e,t,i,o,n,r){var a=this,l={parent:t,element:e.element,children:[],depth:t.depth+1,visibleChildrenCount:0,visibleChildIndex:-1,collapsible:"boolean"===typeof e.collapsible?e.collapsible:"undefined"!==typeof e.collapsed,collapsed:"undefined"===typeof e.collapsed?this.collapseByDefault:e.collapsed,renderNodeCount:1,visible:!0,filterData:void 0},d=this._filterNode(l,i);o&&n.push(l);var h=s["d"].from(e.children),u=o&&0!==d&&!l.collapsed,c=s["d"].map(h,(function(e){return a.createTreeNode(e,l,d,u,n,r)})),p=0,f=1;return s["d"].forEach(c,(function(e){l.children.push(e),f+=e.renderNodeCount,e.visible&&(e.visibleChildIndex=p++)})),l.collapsible=l.collapsible||l.children.length>0,l.visibleChildrenCount=p,l.visible=2===d?p>0:1===d,l.visible?l.collapsed||(l.renderNodeCount=f):(l.renderNodeCount=0,o&&n.pop()),r&&r(l),l},e.prototype.updateNodeAfterCollapseChange=function(e){var t=e.renderNodeCount,i=[];return this._updateNodeAfterCollapseChange(e,i),this._updateAncestorsRenderNodeCount(e.parent,i.length-t),i},e.prototype._updateNodeAfterCollapseChange=function(e,t){if(!1===e.visible)return 0;if(t.push(e),e.renderNodeCount=1,!e.collapsed)for(var i=0,o=e.children;i<o.length;i++){var n=o[i];e.renderNodeCount+=this._updateNodeAfterCollapseChange(n,t)}return this._onDidChangeRenderNodeCount.fire(e),e.renderNodeCount},e.prototype.updateNodeAfterFilterChange=function(e){var t=e.renderNodeCount,i=[];return this._updateNodeAfterFilterChange(e,e.visible?1:0,i),this._updateAncestorsRenderNodeCount(e.parent,i.length-t),i},e.prototype._updateNodeAfterFilterChange=function(e,t,i,o){var n;if(void 0===o&&(o=!0),e!==this.root){if(n=this._filterNode(e,t),0===n)return e.visible=!1,e.renderNodeCount=0,!1;o&&i.push(e)}var r=i.length;e.renderNodeCount=e===this.root?0:1;var s=!1;if(e.collapsed&&0===n)e.visibleChildrenCount=0;else{for(var a=0,l=0,d=e.children;l<d.length;l++){var h=d[l];s=this._updateNodeAfterFilterChange(h,n,i,o&&!e.collapsed)||s,h.visible&&(h.visibleChildIndex=a++)}e.visibleChildrenCount=a}return e!==this.root&&(e.visible=2===n?s:1===n),e.visible?e.collapsed||(e.renderNodeCount+=i.length-r):(e.renderNodeCount=0,o&&i.pop()),this._onDidChangeRenderNodeCount.fire(e),e.visible},e.prototype._updateAncestorsRenderNodeCount=function(e,t){if(0!==t)while(e)e.renderNodeCount+=t,this._onDidChangeRenderNodeCount.fire(e),e=e.parent},e.prototype._filterNode=function(e,t){var i=this.filter?this.filter.filter(e.element,t):1;return"boolean"===typeof i?(e.filterData=void 0,i?1:0):l(i)?(e.filterData=i.data,d(i.visibility)):(e.filterData=void 0,d(i))},e.prototype.hasTreeNode=function(e,t){if(void 0===t&&(t=this.root),!e||0===e.length)return!0;var i=e[0],o=e.slice(1);return!(i<0||i>t.children.length)&&this.hasTreeNode(o,t.children[i])},e.prototype.getTreeNode=function(e,t){if(void 0===t&&(t=this.root),!e||0===e.length)return t;var i=e[0],n=e.slice(1);if(i<0||i>t.children.length)throw new o["a"](this.user,"Invalid tree location");return this.getTreeNode(n,t.children[i])},e.prototype.getTreeNodeWithListIndex=function(e){if(0===e.length)return{node:this.root,listIndex:-1,revealed:!0,visible:!1};var t=this.getParentNodeWithListIndex(e),i=t.parentNode,n=t.listIndex,r=t.revealed,s=t.visible,a=e[e.length-1];if(a<0||a>i.children.length)throw new o["a"](this.user,"Invalid tree location");var l=i.children[a];return{node:l,listIndex:n,revealed:r,visible:s&&l.visible}},e.prototype.getParentNodeWithListIndex=function(e,t,i,n,r){void 0===t&&(t=this.root),void 0===i&&(i=0),void 0===n&&(n=!0),void 0===r&&(r=!0);var s=e[0],a=e.slice(1);if(s<0||s>t.children.length)throw new o["a"](this.user,"Invalid tree location");for(var l=0;l<s;l++)i+=t.children[l].renderNodeCount;return n=n&&!t.collapsed,r=r&&t.visible,0===a.length?{parentNode:t,listIndex:i,revealed:n,visible:r}:this.getParentNodeWithListIndex(a,t.children[s],i+1,n,r)},e.prototype.getNode=function(e){return void 0===e&&(e=[]),this.getTreeNode(e)},e.prototype.getNodeLocation=function(e){var t=[],i=e;while(i.parent)t.push(i.parent.children.indexOf(i)),i=i.parent;return t.reverse()},e.prototype.getParentNodeLocation=function(e){return 0===e.length?void 0:1===e.length?[]:Object(n["w"])(e)[0]},e}()},c482:function(e,t,i){"use strict";i.d(t,"b",(function(){return z})),i.d(t,"a",(function(){return F}));i("d95f");var o=i("a666"),n=i("72a7"),r=i("11f7"),s=i("308f"),a=i("b835"),l=i("650e"),d=i("e8e3"),h=i("7de1"),u=i("e32d"),c=i("7e93"),p=i("c34a"),f=i("dff7"),m=i("5fe7"),v=i("30db"),b=i("4035"),y=i("49d9"),g=i("be5f"),_=function(){var e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])},e(t,i)};return function(t,i){function o(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(o.prototype=i.prototype,new o)}}(),S=function(){return S=Object.assign||function(e){for(var t,i=1,o=arguments.length;i<o;i++)for(var n in t=arguments[i],t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},S.apply(this,arguments)},w=function(){for(var e=0,t=0,i=arguments.length;t<i;t++)e+=arguments[t].length;var o=Array(e),n=0;for(t=0;t<i;t++)for(var r=arguments[t],s=0,a=r.length;s<a;s++,n++)o[n]=r[s];return o},N=function(e){function t(t){var i=e.call(this,t.elements.map((function(e){return e.element})))||this;return i.data=t,i}return _(t,e),t}(h["a"]);function C(e){return e instanceof h["a"]?new N(e):e}var D=function(){function e(e,t){this.modelProvider=e,this.dnd=t,this.autoExpandDisposable=o["a"].None}return e.prototype.getDragURI=function(e){return this.dnd.getDragURI(e.element)},e.prototype.getDragLabel=function(e,t){if(this.dnd.getDragLabel)return this.dnd.getDragLabel(e.map((function(e){return e.element})),t)},e.prototype.onDragStart=function(e,t){this.dnd.onDragStart&&this.dnd.onDragStart(C(e),t)},e.prototype.onDragOver=function(e,t,i,o,n){var r=this;void 0===n&&(n=!0);var s=this.dnd.onDragOver(C(e),t&&t.element,i,o),a=this.autoExpandNode!==t;if(a&&(this.autoExpandDisposable.dispose(),this.autoExpandNode=t),"undefined"===typeof t)return s;if(a&&"boolean"!==typeof s&&s.autoExpand&&(this.autoExpandDisposable=Object(m["g"])((function(){var e=r.modelProvider(),i=e.getNodeLocation(t);e.isCollapsed(i)&&e.setCollapsed(i,!1),r.autoExpandNode=void 0}),500)),"boolean"===typeof s||!s.accept||"undefined"===typeof s.bubble||s.feedback){if(!n){var l="boolean"===typeof s?s:s.accept,h="boolean"===typeof s?void 0:s.effect;return{accept:l,effect:h,feedback:[i]}}return s}if(1===s.bubble){var u=this.modelProvider(),c=u.getNodeLocation(t),p=u.getParentNodeLocation(c),f=u.getNode(p),v=p&&u.getListIndex(p);return this.onDragOver(e,f,v,o,!1)}var b=this.modelProvider(),y=b.getNodeLocation(t),g=b.getListIndex(y),_=b.getListRenderCount(y);return S(S({},s),{feedback:Object(d["u"])(g,g+_)})},e.prototype.drop=function(e,t,i,o){this.autoExpandDisposable.dispose(),this.autoExpandNode=void 0,this.dnd.drop(C(e),t&&t.element,i,o)},e.prototype.onDragEnd=function(e){this.dnd.onDragEnd&&this.dnd.onDragEnd(e)},e}();function O(e,t){return t&&S(S({},t),{identityProvider:t.identityProvider&&{getId:function(e){return t.identityProvider.getId(e.element)}},dnd:t.dnd&&new D(e,t.dnd),multipleSelectionController:t.multipleSelectionController&&{isSelectionSingleChangeEvent:function(e){return t.multipleSelectionController.isSelectionSingleChangeEvent(S(S({},e),{element:e.element}))},isSelectionRangeChangeEvent:function(e){return t.multipleSelectionController.isSelectionRangeChangeEvent(S(S({},e),{element:e.element}))}},accessibilityProvider:t.accessibilityProvider&&S(S({},t.accessibilityProvider),{getAriaLabel:function(e){return t.accessibilityProvider.getAriaLabel(e.element)},getAriaLevel:function(e){return e.depth},getActiveDescendantId:t.accessibilityProvider.getActiveDescendantId&&function(e){return t.accessibilityProvider.getActiveDescendantId(e.element)}}),keyboardNavigationLabelProvider:t.keyboardNavigationLabelProvider&&S(S({},t.keyboardNavigationLabelProvider),{getKeyboardNavigationLabel:function(e){return t.keyboardNavigationLabelProvider.getKeyboardNavigationLabel(e.element)}}),enableKeyboardNavigation:t.simpleKeyboardNavigation,ariaProvider:{getSetSize:function(t){var i=e(),o=i.getNodeLocation(t),n=i.getParentNodeLocation(o),r=i.getNode(n);return r.visibleChildrenCount},getPosInSet:function(e){return e.visibleChildIndex+1},isChecked:t.ariaProvider&&t.ariaProvider.isChecked?function(e){return t.ariaProvider.isChecked(e.element)}:void 0,getRole:t.ariaProvider&&t.ariaProvider.getRole?function(e){return t.ariaProvider.getRole(e.element)}:void 0}})}var P,z=function(){function e(e){this.delegate=e}return e.prototype.getHeight=function(e){return this.delegate.getHeight(e.element)},e.prototype.getTemplateId=function(e){return this.delegate.getTemplateId(e.element)},e.prototype.hasDynamicHeight=function(e){return!!this.delegate.hasDynamicHeight&&this.delegate.hasDynamicHeight(e.element)},e.prototype.setDynamicHeight=function(e,t){this.delegate.setDynamicHeight&&this.delegate.setDynamicHeight(e.element,t)},e}();(function(e){e["None"]="none",e["OnHover"]="onHover",e["Always"]="always"})(P||(P={}));var E=function(){function e(e,t){var i=this;void 0===t&&(t=[]),this._elements=t,this.onDidChange=s["b"].forEach(e,(function(e){return i._elements=e}))}return Object.defineProperty(e.prototype,"elements",{get:function(){return this._elements},enumerable:!0,configurable:!0}),e}(),I=function(){function e(t,i,n,r,a){void 0===a&&(a={}),this.renderer=t,this.modelProvider=i,this.activeNodes=r,this.renderedElements=new Map,this.renderedNodes=new Map,this.indent=e.DefaultIndent,this.hideTwistiesOfChildlessElements=!1,this.shouldRenderIndentGuides=!1,this.renderedIndentGuides=new g["a"],this.activeIndentNodes=new Set,this.indentGuidesDisposable=o["a"].None,this.disposables=new o["b"],this.templateId=t.templateId,this.updateOptions(a),s["b"].map(n,(function(e){return e.node}))(this.onDidChangeNodeTwistieState,this,this.disposables),t.onDidChangeTwistieState&&t.onDidChangeTwistieState(this.onDidChangeTwistieState,this,this.disposables)}return e.prototype.updateOptions=function(e){if(void 0===e&&(e={}),"undefined"!==typeof e.indent&&(this.indent=Object(y["a"])(e.indent,0,40)),"undefined"!==typeof e.renderIndentGuides){var t=e.renderIndentGuides!==P.None;if(t!==this.shouldRenderIndentGuides&&(this.shouldRenderIndentGuides=t,this.indentGuidesDisposable.dispose(),t)){var i=new o["b"];this.activeNodes.onDidChange(this._onDidChangeActiveNodes,this,i),this.indentGuidesDisposable=i,this._onDidChangeActiveNodes(this.activeNodes.elements)}}"undefined"!==typeof e.hideTwistiesOfChildlessElements&&(this.hideTwistiesOfChildlessElements=e.hideTwistiesOfChildlessElements)},e.prototype.renderTemplate=function(e){var t=Object(r["q"])(e,Object(r["a"])(".monaco-tl-row")),i=Object(r["q"])(t,Object(r["a"])(".monaco-tl-indent")),n=Object(r["q"])(t,Object(r["a"])(".monaco-tl-twistie")),s=Object(r["q"])(t,Object(r["a"])(".monaco-tl-contents")),a=this.renderer.renderTemplate(s);return{container:e,indent:i,twistie:n,indentGuidesDisposable:o["a"].None,templateData:a}},e.prototype.renderElement=function(t,i,o,n){"number"===typeof n&&(this.renderedNodes.set(t,{templateData:o,height:n}),this.renderedElements.set(t.element,t));var r=e.DefaultIndent+(t.depth-1)*this.indent;o.twistie.style.paddingLeft=r+"px",o.indent.style.width=r+this.indent-16+"px",this.renderTwistie(t,o),"number"===typeof n&&this.renderIndentGuides(t,o),this.renderer.renderElement(t,i,o.templateData,n)},e.prototype.disposeElement=function(e,t,i,o){i.indentGuidesDisposable.dispose(),this.renderer.disposeElement&&this.renderer.disposeElement(e,t,i.templateData,o),"number"===typeof o&&(this.renderedNodes.delete(e),this.renderedElements.delete(e.element))},e.prototype.disposeTemplate=function(e){this.renderer.disposeTemplate(e.templateData)},e.prototype.onDidChangeTwistieState=function(e){var t=this.renderedElements.get(e);t&&this.onDidChangeNodeTwistieState(t)},e.prototype.onDidChangeNodeTwistieState=function(e){var t=this.renderedNodes.get(e);t&&(this.renderTwistie(e,t.templateData),this._onDidChangeActiveNodes(this.activeNodes.elements),this.renderIndentGuides(e,t.templateData))},e.prototype.renderTwistie=function(e,t){this.renderer.renderTwistie&&this.renderer.renderTwistie(e.element,t.twistie),e.collapsible&&(!this.hideTwistiesOfChildlessElements||e.visibleChildrenCount>0)?(Object(r["g"])(t.twistie,"codicon","codicon-chevron-down","collapsible"),Object(r["Y"])(t.twistie,"collapsed",e.collapsed)):Object(r["Q"])(t.twistie,"codicon","codicon-chevron-down","collapsible","collapsed"),e.collapsible?t.container.setAttribute("aria-expanded",String(!e.collapsed)):t.container.removeAttribute("aria-expanded")},e.prototype.renderIndentGuides=function(e,t){var i=this;if(Object(r["t"])(t.indent),t.indentGuidesDisposable.dispose(),this.shouldRenderIndentGuides){var n=new o["b"],s=this.modelProvider(),a=e,l=function(){var e=s.getNodeLocation(a),l=s.getParentNodeLocation(e);if(!l)return"break";var h=s.getNode(l),u=Object(r["a"])(".indent-guide",{style:"width: "+d.indent+"px"});d.activeIndentNodes.has(h)&&Object(r["f"])(u,"active"),0===t.indent.childElementCount?t.indent.appendChild(u):t.indent.insertBefore(u,t.indent.firstElementChild),d.renderedIndentGuides.add(h,u),n.add(Object(o["h"])((function(){return i.renderedIndentGuides.delete(h,u)}))),a=h},d=this;while(1){var h=l();if("break"===h)break}t.indentGuidesDisposable=n}},e.prototype._onDidChangeActiveNodes=function(e){var t=this;if(this.shouldRenderIndentGuides){var i=new Set,o=this.modelProvider();e.forEach((function(e){var t=o.getNodeLocation(e);try{var n=o.getParentNodeLocation(t);e.collapsible&&e.children.length>0&&!e.collapsed?i.add(e):n&&i.add(o.getNode(n))}catch(r){}})),this.activeIndentNodes.forEach((function(e){i.has(e)||t.renderedIndentGuides.forEach(e,(function(e){return Object(r["P"])(e,"active")}))})),i.forEach((function(e){t.activeIndentNodes.has(e)||t.renderedIndentGuides.forEach(e,(function(e){return Object(r["f"])(e,"active")}))})),this.activeIndentNodes=i}},e.prototype.dispose=function(){this.renderedNodes.clear(),this.renderedElements.clear(),this.indentGuidesDisposable.dispose(),Object(o["f"])(this.disposables)},e.DefaultIndent=8,e}(),j=function(){function e(e,t,i){this.tree=e,this.keyboardNavigationLabelProvider=t,this._filter=i,this._totalCount=0,this._matchCount=0,this._pattern="",this._lowercasePattern="",this.disposables=new o["b"],e.onWillRefilter(this.reset,this,this.disposables)}return Object.defineProperty(e.prototype,"totalCount",{get:function(){return this._totalCount},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"matchCount",{get:function(){return this._matchCount},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"pattern",{set:function(e){this._pattern=e,this._lowercasePattern=e.toLowerCase()},enumerable:!0,configurable:!0}),e.prototype.filter=function(e,t){if(this._filter){var i=this._filter.filter(e,t);if(this.tree.options.simpleKeyboardNavigation)return i;var o=void 0;if(o="boolean"===typeof i?i?1:0:Object(p["c"])(i)?Object(p["b"])(i.visibility):i,0===o)return!1}if(this._totalCount++,this.tree.options.simpleKeyboardNavigation||!this._pattern)return this._matchCount++,{data:c["a"].Default,visibility:!0};var n=this.keyboardNavigationLabelProvider.getKeyboardNavigationLabel(e),r=n&&n.toString();if("undefined"===typeof r)return{data:c["a"].Default,visibility:!0};var s=Object(c["d"])(this._pattern,this._lowercasePattern,0,r,r.toLowerCase(),0,!0);return s?(this._matchCount++,{data:s,visibility:!0}):this.tree.options.filterOnType?2:{data:c["a"].Default,visibility:!0}},e.prototype.reset=function(){this._totalCount=0,this._matchCount=0},e.prototype.dispose=function(){Object(o["f"])(this.disposables)},e}(),T=function(){function e(e,t,i,n,a){this.tree=e,this.view=i,this.filter=n,this.keyboardNavigationDelegate=a,this._enabled=!1,this._pattern="",this._empty=!1,this._onDidChangeEmptyState=new s["a"],this.positionClassName="ne",this.automaticKeyboardNavigation=!0,this.triggered=!1,this._onDidChangePattern=new s["a"],this.enabledDisposables=new o["b"],this.disposables=new o["b"],this.domNode=Object(r["a"])(".monaco-list-type-filter."+this.positionClassName),this.domNode.draggable=!0,Object(u["a"])(this.domNode,"dragstart")(this.onDragStart,this,this.disposables),this.messageDomNode=Object(r["q"])(i.getHTMLElement(),Object(r["a"])(".monaco-list-type-filter-message")),this.labelDomNode=Object(r["q"])(this.domNode,Object(r["a"])("span.label"));var l=Object(r["q"])(this.domNode,Object(r["a"])(".controls"));this._filterOnType=!!e.options.filterOnType,this.filterOnTypeDomNode=Object(r["q"])(l,Object(r["a"])("input.filter.codicon.codicon-list-selection")),this.filterOnTypeDomNode.type="checkbox",this.filterOnTypeDomNode.checked=this._filterOnType,this.filterOnTypeDomNode.tabIndex=-1,this.updateFilterOnTypeTitle(),Object(u["a"])(this.filterOnTypeDomNode,"input")(this.onDidChangeFilterOnType,this,this.disposables),this.clearDomNode=Object(r["q"])(l,Object(r["a"])("button.clear.codicon.codicon-close")),this.clearDomNode.tabIndex=-1,this.clearDomNode.title=Object(f["a"])("clear","Clear"),this.keyboardNavigationEventFilter=e.options.keyboardNavigationEventFilter,t.onDidSplice(this.onDidSpliceModel,this,this.disposables),this.updateOptions(e.options)}return Object.defineProperty(e.prototype,"enabled",{get:function(){return this._enabled},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"pattern",{get:function(){return this._pattern},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"filterOnType",{get:function(){return this._filterOnType},enumerable:!0,configurable:!0}),e.prototype.updateOptions=function(e){e.simpleKeyboardNavigation?this.disable():this.enable(),"undefined"!==typeof e.filterOnType&&(this._filterOnType=!!e.filterOnType,this.filterOnTypeDomNode.checked=this._filterOnType),"undefined"!==typeof e.automaticKeyboardNavigation&&(this.automaticKeyboardNavigation=e.automaticKeyboardNavigation),this.tree.refilter(),this.render(),this.automaticKeyboardNavigation||this.onEventOrInput("")},e.prototype.enable=function(){var e=this;if(!this._enabled){var t=s["b"].chain(Object(u["a"])(this.view.getHTMLElement(),"keydown")).filter((function(t){return!x(t.target)||t.target===e.filterOnTypeDomNode})).filter((function(e){return"Dead"!==e.key&&!/^Media/.test(e.key)})).map((function(e){return new a["a"](e)})).filter(this.keyboardNavigationEventFilter||function(){return!0}).filter((function(){return e.automaticKeyboardNavigation||e.triggered})).filter((function(t){return e.keyboardNavigationDelegate.mightProducePrintableCharacter(t)||(e.pattern.length>0||e.triggered)&&(9===t.keyCode||1===t.keyCode)&&!t.altKey&&!t.ctrlKey&&!t.metaKey||1===t.keyCode&&(v["e"]?t.altKey&&!t.metaKey:t.ctrlKey)&&!t.shiftKey})).forEach((function(e){e.stopPropagation(),e.preventDefault()})).event,i=Object(u["a"])(this.clearDomNode,"click");s["b"].chain(s["b"].any(t,i)).event(this.onEventOrInput,this,this.enabledDisposables),this.filter.pattern="",this.tree.refilter(),this.render(),this._enabled=!0,this.triggered=!1}},e.prototype.disable=function(){this._enabled&&(this.domNode.remove(),this.enabledDisposables.clear(),this.tree.refilter(),this.render(),this._enabled=!1,this.triggered=!1)},e.prototype.onEventOrInput=function(e){"string"===typeof e?this.onInput(e):e instanceof MouseEvent||9===e.keyCode||1===e.keyCode&&(v["e"]?e.altKey:e.ctrlKey)?this.onInput(""):1===e.keyCode?this.onInput(0===this.pattern.length?"":this.pattern.substr(0,this.pattern.length-1)):this.onInput(this.pattern+e.browserEvent.key)},e.prototype.onInput=function(e){var t=this.view.getHTMLElement();e&&!this.domNode.parentElement?t.append(this.domNode):!e&&this.domNode.parentElement&&(this.domNode.remove(),this.tree.domFocus()),this._pattern=e,this._onDidChangePattern.fire(e),this.filter.pattern=e,this.tree.refilter(),e&&this.tree.focusNext(0,!0,void 0,(function(e){return!c["a"].isDefault(e.filterData)}));var i=this.tree.getFocus();if(i.length>0){var o=i[0];null===this.tree.getRelativeTop(o)&&this.tree.reveal(o,.5)}this.render(),e||(this.triggered=!1)},e.prototype.onDragStart=function(){var e=this,t=this.view.getHTMLElement(),i=Object(r["C"])(t).left,n=t.clientWidth,s=n/2,a=this.domNode.clientWidth,d=new o["b"],h=this.positionClassName,c=function(){switch(h){case"nw":e.domNode.style.top="4px",e.domNode.style.left="4px";break;case"ne":e.domNode.style.top="4px",e.domNode.style.left=n-a-6+"px";break}},p=function(e){e.preventDefault();var t=e.screenX-i;e.dataTransfer&&(e.dataTransfer.dropEffect="none"),h=t<s?"nw":"ne",c()},f=function(){e.positionClassName=h,e.domNode.className="monaco-list-type-filter "+e.positionClassName,e.domNode.style.top="",e.domNode.style.left="",Object(o["f"])(d)};c(),Object(r["P"])(this.domNode,h),Object(r["f"])(this.domNode,"dragging"),d.add(Object(o["h"])((function(){return Object(r["P"])(e.domNode,"dragging")}))),Object(u["a"])(document,"dragover")(p,null,d),Object(u["a"])(this.domNode,"dragend")(f,null,d),l["c"].CurrentDragAndDropData=new l["b"]("vscode-ui"),d.add(Object(o["h"])((function(){return l["c"].CurrentDragAndDropData=void 0})))},e.prototype.onDidSpliceModel=function(){this._enabled&&0!==this.pattern.length&&(this.tree.refilter(),this.render())},e.prototype.onDidChangeFilterOnType=function(){this.tree.updateOptions({filterOnType:this.filterOnTypeDomNode.checked}),this.tree.refilter(),this.tree.domFocus(),this.render(),this.updateFilterOnTypeTitle()},e.prototype.updateFilterOnTypeTitle=function(){this.filterOnType?this.filterOnTypeDomNode.title=Object(f["a"])("disable filter on type","Disable Filter on Type"):this.filterOnTypeDomNode.title=Object(f["a"])("enable filter on type","Enable Filter on Type")},e.prototype.render=function(){var e=this.filter.totalCount>0&&0===this.filter.matchCount;this.pattern&&this.tree.options.filterOnType&&e?(this.messageDomNode.textContent=Object(f["a"])("empty","No elements found"),this._empty=!0):(this.messageDomNode.innerHTML="",this._empty=!1),Object(r["Y"])(this.domNode,"no-matches",e),this.domNode.title=Object(f["a"])("found","Matched {0} out of {1} elements",this.filter.matchCount,this.filter.totalCount),this.labelDomNode.textContent=this.pattern.length>16?"…"+this.pattern.substr(this.pattern.length-16):this.pattern,this._onDidChangeEmptyState.fire(this._empty)},e.prototype.shouldAllowFocus=function(e){return!(this.enabled&&this.pattern&&!this.filterOnType)||(this.filter.totalCount>0&&this.filter.matchCount<=1||!c["a"].isDefault(e.filterData))},e.prototype.dispose=function(){this._enabled&&(this.domNode.remove(),this.enabledDisposables.dispose(),this._enabled=!1,this.triggered=!1),this._onDidChangePattern.dispose(),Object(o["f"])(this.disposables)},e}();function x(e){return"INPUT"===e.tagName||"TEXTAREA"===e.tagName}function M(e){return{elements:e.elements.map((function(e){return e.element})),browserEvent:e.browserEvent}}function L(e,t){t(e),e.children.forEach((function(e){return L(e,t)}))}var R=function(){function e(e){this.identityProvider=e,this.nodes=[],this._onDidChange=new s["a"],this.onDidChange=this._onDidChange.event}return Object.defineProperty(e.prototype,"nodeSet",{get:function(){return this._nodeSet||(this._nodeSet=this.createNodeSet()),this._nodeSet},enumerable:!0,configurable:!0}),e.prototype.set=function(e,t){Object(d["g"])(this.nodes,e)||this._set(e,!1,t)},e.prototype._set=function(e,t,i){if(this.nodes=w(e),this.elements=void 0,this._nodeSet=void 0,!t){var o=this;this._onDidChange.fire({get elements(){return o.get()},browserEvent:i})}},e.prototype.get=function(){return this.elements||(this.elements=this.nodes.map((function(e){return e.element}))),w(this.elements)},e.prototype.getNodes=function(){return this.nodes},e.prototype.has=function(e){return this.nodeSet.has(e)},e.prototype.onDidModelSplice=function(e){var t=this,i=e.insertedNodes,o=e.deletedNodes;if(!this.identityProvider){var n=this.createNodeSet(),r=function(e){return n.delete(e)};return o.forEach((function(e){return L(e,r)})),void this.set(Object(b["e"])(n))}var s=new Set,a=function(e){return s.add(t.identityProvider.getId(e.element).toString())};o.forEach((function(e){return L(e,a)}));var l=new Map,d=function(e){return l.set(t.identityProvider.getId(e.element).toString(),e)};i.forEach((function(e){return L(e,d)}));for(var h=[],u=0,c=this.nodes;u<c.length;u++){var p=c[u],f=this.identityProvider.getId(p.element).toString(),m=s.has(f);if(m){var v=l.get(f);v&&h.push(v)}else h.push(p)}this._set(h,!0)},e.prototype.createNodeSet=function(){for(var e=new Set,t=0,i=this.nodes;t<i.length;t++){var o=i[t];e.add(o)}return e},e}(),A=function(e){function t(t,i){var o=e.call(this,t)||this;return o.tree=i,o}return _(t,e),t.prototype.onPointer=function(t){if(!x(t.browserEvent.target)){var i=t.element;if(!i)return e.prototype.onPointer.call(this,t);if(this.isSelectionRangeChangeEvent(t)||this.isSelectionSingleChangeEvent(t))return e.prototype.onPointer.call(this,t);var o=Object(r["I"])(t.browserEvent.target,"monaco-tl-twistie");if(!this.tree.openOnSingleClick&&2!==t.browserEvent.detail&&!o)return e.prototype.onPointer.call(this,t);var n=!1;if(n="function"===typeof this.tree.expandOnlyOnTwistieClick?this.tree.expandOnlyOnTwistieClick(i.element):!!this.tree.expandOnlyOnTwistieClick,n&&!o)return e.prototype.onPointer.call(this,t);if(i.collapsible){var s=this.tree.model,a=s.getNodeLocation(i),l=t.browserEvent.altKey;if(s.setCollapsed(a,void 0,l),n&&o)return}e.prototype.onPointer.call(this,t)}},t.prototype.onDoubleClick=function(t){var i=Object(r["I"])(t.browserEvent.target,"monaco-tl-twistie");i||e.prototype.onDoubleClick.call(this,t)},t}(n["d"]),k=function(e){function t(t,i,o,n,r,s,a){var l=e.call(this,t,i,o,n,a)||this;return l.focusTrait=r,l.selectionTrait=s,l}return _(t,e),t.prototype.createMouseController=function(e){return new A(this,e.tree)},t.prototype.splice=function(t,i,o){var n=this;if(void 0===o&&(o=[]),e.prototype.splice.call(this,t,i,o),0!==o.length){var r=[],s=[];o.forEach((function(e,i){n.focusTrait.has(e)&&r.push(t+i),n.selectionTrait.has(e)&&s.push(t+i)})),r.length>0&&e.prototype.setFocus.call(this,Object(d["f"])(w(e.prototype.getFocus.call(this),r))),s.length>0&&e.prototype.setSelection.call(this,Object(d["f"])(w(e.prototype.getSelection.call(this),s)))}},t.prototype.setFocus=function(t,i,o){var n=this;void 0===o&&(o=!1),e.prototype.setFocus.call(this,t,i),o||this.focusTrait.set(t.map((function(e){return n.element(e)})),i)},t.prototype.setSelection=function(t,i,o){var n=this;void 0===o&&(o=!1),e.prototype.setSelection.call(this,t,i),o||this.selectionTrait.set(t.map((function(e){return n.element(e)})),i)},t}(n["c"]),F=function(){function e(e,t,i,l,h){var u=this;void 0===h&&(h={}),this._options=h,this.eventBufferer=new s["c"],this.disposables=new o["b"],this._onWillRefilter=new s["a"],this.onWillRefilter=this._onWillRefilter.event,this._onDidUpdateOptions=new s["a"];var c,p=new z(i),f=new s["f"],m=new s["f"],v=new E(m.event);this.renderers=l.map((function(e){return new I(e,(function(){return u.model}),f.event,v,h)}));for(var b=0,y=this.renderers;b<y.length;b++){var g=y[b];this.disposables.add(g)}h.keyboardNavigationLabelProvider&&(c=new j(this,h.keyboardNavigationLabelProvider,h.filter),h=S(S({},h),{filter:c}),this.disposables.add(c)),this.focus=new R(h.identityProvider),this.selection=new R(h.identityProvider),this.view=new k(e,t,p,this.renderers,this.focus,this.selection,S(S({},O((function(){return u.model}),h)),{tree:this})),this.model=this.createModel(e,this.view,h),f.input=this.model.onDidChangeCollapseState;var _=s["b"].forEach(this.model.onDidSplice,(function(e){u.eventBufferer.bufferEvents((function(){u.focus.onDidModelSplice(e),u.selection.onDidModelSplice(e)}))}));if(_((function(){return null}),null,this.disposables),m.input=s["b"].chain(s["b"].any(_,this.focus.onDidChange,this.selection.onDidChange)).debounce((function(){return null}),0).map((function(){for(var e=new Set,t=0,i=u.focus.getNodes();t<i.length;t++){var o=i[t];e.add(o)}for(var n=0,r=u.selection.getNodes();n<r.length;n++){o=r[n];e.add(o)}return Object(d["n"])(e)})).event,!1!==h.keyboardSupport){var w=s["b"].chain(this.view.onKeyDown).filter((function(e){return!x(e.target)})).map((function(e){return new a["a"](e)}));w.filter((function(e){return 15===e.keyCode})).on(this.onLeftArrow,this,this.disposables),w.filter((function(e){return 17===e.keyCode})).on(this.onRightArrow,this,this.disposables),w.filter((function(e){return 10===e.keyCode})).on(this.onSpace,this,this.disposables)}if(h.keyboardNavigationLabelProvider){var N=h.keyboardNavigationDelegate||n["a"];this.typeFilterController=new T(this,this.model,this.view,c,N),this.focusNavigationFilter=function(e){return u.typeFilterController.shouldAllowFocus(e)},this.disposables.add(this.typeFilterController)}this.styleElement=Object(r["w"])(this.view.getHTMLElement()),Object(r["Y"])(this.getHTMLElement(),"always",this._options.renderIndentGuides===P.Always)}return Object.defineProperty(e.prototype,"onDidChangeFocus",{get:function(){return this.eventBufferer.wrapEvent(this.focus.onDidChange)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"onDidChangeSelection",{get:function(){return this.eventBufferer.wrapEvent(this.selection.onDidChange)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"onDidOpen",{get:function(){return s["b"].map(this.view.onDidOpen,M)},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"onDidFocus",{get:function(){return this.view.onDidFocus},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"onDidChangeCollapseState",{get:function(){return this.model.onDidChangeCollapseState},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"openOnSingleClick",{get:function(){return"undefined"===typeof this._options.openOnSingleClick||this._options.openOnSingleClick},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"expandOnlyOnTwistieClick",{get:function(){return"undefined"!==typeof this._options.expandOnlyOnTwistieClick&&this._options.expandOnlyOnTwistieClick},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"onDidDispose",{get:function(){return this.view.onDidDispose},enumerable:!0,configurable:!0}),e.prototype.updateOptions=function(e){void 0===e&&(e={}),this._options=S(S({},this._options),e);for(var t=0,i=this.renderers;t<i.length;t++){var o=i[t];o.updateOptions(e)}this.view.updateOptions({enableKeyboardNavigation:this._options.simpleKeyboardNavigation,automaticKeyboardNavigation:this._options.automaticKeyboardNavigation}),this.typeFilterController&&this.typeFilterController.updateOptions(this._options),this._onDidUpdateOptions.fire(this._options),Object(r["Y"])(this.getHTMLElement(),"always",this._options.renderIndentGuides===P.Always)},Object.defineProperty(e.prototype,"options",{get:function(){return this._options},enumerable:!0,configurable:!0}),e.prototype.getHTMLElement=function(){return this.view.getHTMLElement()},Object.defineProperty(e.prototype,"scrollTop",{get:function(){return this.view.scrollTop},set:function(e){this.view.scrollTop=e},enumerable:!0,configurable:!0}),e.prototype.domFocus=function(){this.view.domFocus()},e.prototype.layout=function(e,t){this.view.layout(e,t)},e.prototype.style=function(e){var t="."+this.view.domId,i=[];e.treeIndentGuidesStroke&&(i.push(".monaco-list"+t+":hover .monaco-tl-indent > .indent-guide, .monaco-list"+t+".always .monaco-tl-indent > .indent-guide  { border-color: "+e.treeIndentGuidesStroke.transparent(.4)+"; }"),i.push(".monaco-list"+t+" .monaco-tl-indent > .indent-guide.active { border-color: "+e.treeIndentGuidesStroke+"; }"));var o=i.join("\n");o!==this.styleElement.innerHTML&&(this.styleElement.innerHTML=o),this.view.style(e)},e.prototype.collapse=function(e,t){return void 0===t&&(t=!1),this.model.setCollapsed(e,!0,t)},e.prototype.expand=function(e,t){return void 0===t&&(t=!1),this.model.setCollapsed(e,!1,t)},e.prototype.isCollapsible=function(e){return this.model.isCollapsible(e)},e.prototype.setCollapsible=function(e,t){return this.model.setCollapsible(e,t)},e.prototype.isCollapsed=function(e){return this.model.isCollapsed(e)},e.prototype.refilter=function(){this._onWillRefilter.fire(void 0),this.model.refilter()},e.prototype.setSelection=function(e,t){var i=this,o=e.map((function(e){return i.model.getNode(e)}));this.selection.set(o,t);var n=e.map((function(e){return i.model.getListIndex(e)})).filter((function(e){return e>-1}));this.view.setSelection(n,t,!0)},e.prototype.getSelection=function(){return this.selection.get()},e.prototype.setFocus=function(e,t){var i=this,o=e.map((function(e){return i.model.getNode(e)}));this.focus.set(o,t);var n=e.map((function(e){return i.model.getListIndex(e)})).filter((function(e){return e>-1}));this.view.setFocus(n,t,!0)},e.prototype.focusNext=function(e,t,i,o){void 0===e&&(e=1),void 0===t&&(t=!1),void 0===o&&(o=this.focusNavigationFilter),this.view.focusNext(e,t,i,o)},e.prototype.getFocus=function(){return this.focus.get()},e.prototype.reveal=function(e,t){this.model.expandTo(e);var i=this.model.getListIndex(e);-1!==i&&this.view.reveal(i,t)},e.prototype.getRelativeTop=function(e){var t=this.model.getListIndex(e);return-1===t?null:this.view.getRelativeTop(t)},e.prototype.onLeftArrow=function(e){e.preventDefault(),e.stopPropagation();var t=this.view.getFocusedElements();if(0!==t.length){var i=t[0],o=this.model.getNodeLocation(i),n=this.model.setCollapsed(o,!0);if(!n){var r=this.model.getParentNodeLocation(o);if(!r)return;var s=this.model.getListIndex(r);this.view.reveal(s),this.view.setFocus([s])}}},e.prototype.onRightArrow=function(e){e.preventDefault(),e.stopPropagation();var t=this.view.getFocusedElements();if(0!==t.length){var i=t[0],o=this.model.getNodeLocation(i),n=this.model.setCollapsed(o,!1);if(!n){if(!i.children.some((function(e){return e.visible})))return;var r=this.view.getFocus()[0],s=r+1;this.view.reveal(s),this.view.setFocus([s])}}},e.prototype.onSpace=function(e){e.preventDefault(),e.stopPropagation();var t=this.view.getFocusedElements();if(0!==t.length){var i=t[0],o=this.model.getNodeLocation(i),n=e.browserEvent.altKey;this.model.setCollapsed(o,void 0,n)}},e.prototype.dispose=function(){Object(o["f"])(this.disposables),this.view.dispose()},e}()},c8ac:function(e,t,i){"use strict";i.d(t,"a",(function(){return n})),i.d(t,"b",(function(){return r}));var o=function(){var e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])},e(t,i)};return function(t,i){function o(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(o.prototype=i.prototype,new o)}}(),n=function(e){function t(t,i){return e.call(this,"TreeError ["+t+"] "+i)||this}return o(t,e),t}(Error),r=function(){function e(e){this.fn=e,this._map=new WeakMap}return e.prototype.map=function(e){var t=this._map.get(e);return t||(t=this.fn(e),this._map.set(e,t)),t},e}()},d95f:function(e,t,i){},e385:function(e,t,i){"use strict";i.d(t,"a",(function(){return n})),i.d(t,"b",(function(){return _}));i("e750");var o,n,r=i("a666"),s=i("308f"),a=i("ef8e"),l=i("11f7"),d=i("49d9"),h=i("e8e3"),u=i("70c3"),c=i("ceb8"),p=i("e32d"),f=function(){var e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])},e(t,i)};return function(t,i){function o(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(o.prototype=i.prototype,new o)}}(),m=function(){for(var e=0,t=0,i=arguments.length;t<i;t++)e+=arguments[t].length;var o=Array(e),n=0;for(t=0;t<i;t++)for(var r=arguments[t],s=0,a=r.length;s<a;s++,n++)o[n]=r[s];return o},v={separatorBorder:c["a"].transparent},b=function(){function e(e,t,i,o){this.container=e,this.view=t,this.disposable=o,this._cachedVisibleSize=void 0,"number"===typeof i?(this._size=i,this._cachedVisibleSize=void 0,l["f"](e,"visible")):(this._size=0,this._cachedVisibleSize=i.cachedVisibleSize)}return Object.defineProperty(e.prototype,"size",{get:function(){return this._size},set:function(e){this._size=e},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"visible",{get:function(){return"undefined"===typeof this._cachedVisibleSize},enumerable:!0,configurable:!0}),e.prototype.setVisible=function(e,t){e!==this.visible&&(e?(this.size=Object(d["a"])(this._cachedVisibleSize,this.viewMinimumSize,this.viewMaximumSize),this._cachedVisibleSize=void 0):(this._cachedVisibleSize="number"===typeof t?t:this.size,this.size=0),l["Y"](this.container,"visible",e),this.view.setVisible&&this.view.setVisible(e))},Object.defineProperty(e.prototype,"minimumSize",{get:function(){return this.visible?this.view.minimumSize:0},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"viewMinimumSize",{get:function(){return this.view.minimumSize},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"maximumSize",{get:function(){return this.visible?this.view.maximumSize:0},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"viewMaximumSize",{get:function(){return this.view.maximumSize},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"priority",{get:function(){return this.view.priority},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"snap",{get:function(){return!!this.view.snap},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"enabled",{set:function(e){this.container.style.pointerEvents=e?null:"none"},enumerable:!0,configurable:!0}),e.prototype.layout=function(e,t){this.layoutContainer(e),this.view.layout(this.size,e,t)},e.prototype.dispose=function(){return this.disposable.dispose(),this.view},e}(),y=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return f(t,e),t.prototype.layoutContainer=function(e){this.container.style.top=e+"px",this.container.style.height=this.size+"px"},t}(b),g=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return f(t,e),t.prototype.layoutContainer=function(e){this.container.style.left=e+"px",this.container.style.width=this.size+"px"},t}(b);(function(e){e[e["Idle"]=0]="Idle",e[e["Busy"]=1]="Busy"})(o||(o={})),function(e){function t(e){return{type:"split",index:e}}function i(e){return{type:"invisible",cachedVisibleSize:e}}e.Distribute={type:"distribute"},e.Split=t,e.Invisible=i}(n||(n={}));var _=function(e){function t(t,i){void 0===i&&(i={});var n=e.call(this)||this;return n.size=0,n.contentSize=0,n.proportions=void 0,n.viewItems=[],n.sashItems=[],n.state=o.Idle,n._onDidSashChange=n._register(new s["a"]),n.onDidSashChange=n._onDidSashChange.event,n._onDidSashReset=n._register(new s["a"]),n._startSnappingEnabled=!0,n._endSnappingEnabled=!0,n.orientation=a["k"](i.orientation)?0:i.orientation,n.inverseAltBehavior=!!i.inverseAltBehavior,n.proportionalLayout=!!a["k"](i.proportionalLayout)||!!i.proportionalLayout,n.el=document.createElement("div"),l["f"](n.el,"monaco-split-view2"),l["f"](n.el,0===n.orientation?"vertical":"horizontal"),t.appendChild(n.el),n.sashContainer=l["q"](n.el,l["a"](".sash-container")),n.viewContainer=l["q"](n.el,l["a"](".split-view-container")),n.style(i.styles||v),i.descriptor&&(n.size=i.descriptor.size,i.descriptor.views.forEach((function(e,t){var i=a["k"](e.visible)||e.visible?e.size:{type:"invisible",cachedVisibleSize:e.size},o=e.view;n.doAddView(o,i,t,!0)})),n.contentSize=n.viewItems.reduce((function(e,t){return e+t.size}),0),n.saveProportions()),n}return f(t,e),Object.defineProperty(t.prototype,"orthogonalStartSash",{get:function(){return this._orthogonalStartSash},set:function(e){for(var t=0,i=this.sashItems;t<i.length;t++){var o=i[t];o.sash.orthogonalStartSash=e}this._orthogonalStartSash=e},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"orthogonalEndSash",{get:function(){return this._orthogonalEndSash},set:function(e){for(var t=0,i=this.sashItems;t<i.length;t++){var o=i[t];o.sash.orthogonalEndSash=e}this._orthogonalEndSash=e},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"startSnappingEnabled",{get:function(){return this._startSnappingEnabled},set:function(e){this._startSnappingEnabled!==e&&(this._startSnappingEnabled=e,this.updateSashEnablement())},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"endSnappingEnabled",{get:function(){return this._endSnappingEnabled},set:function(e){this._endSnappingEnabled!==e&&(this._endSnappingEnabled=e,this.updateSashEnablement())},enumerable:!0,configurable:!0}),t.prototype.style=function(e){e.separatorBorder.isTransparent()?(l["P"](this.el,"separator-border"),this.el.style.removeProperty("--separator-border")):(l["f"](this.el,"separator-border"),this.el.style.setProperty("--separator-border",e.separatorBorder.toString()))},t.prototype.addView=function(e,t,i){void 0===i&&(i=this.viewItems.length),this.doAddView(e,t,i,!1)},t.prototype.layout=function(e,t){var i=this,o=Math.max(this.size,this.contentSize);if(this.size=e,this.layoutContext=t,this.proportions)for(var n=0;n<this.viewItems.length;n++){var r=this.viewItems[n];r.size=Object(d["a"])(Math.round(this.proportions[n]*e),r.minimumSize,r.maximumSize)}else{var s=Object(h["u"])(this.viewItems.length),a=s.filter((function(e){return 1===i.viewItems[e].priority})),l=s.filter((function(e){return 2===i.viewItems[e].priority}));this.resize(this.viewItems.length-1,e-o,void 0,a,l)}this.distributeEmptySpace(),this.layoutViews()},t.prototype.saveProportions=function(){var e=this;this.proportionalLayout&&this.contentSize>0&&(this.proportions=this.viewItems.map((function(t){return t.size/e.contentSize})))},t.prototype.onSashStart=function(e){for(var t=this,i=e.sash,o=e.start,n=e.alt,s=0,a=this.viewItems;s<a.length;s++){var l=a[s];l.enabled=!1}var d=Object(h["k"])(this.sashItems,(function(e){return e.sash===i})),u=Object(r["e"])(Object(p["a"])(document.body,"keydown")((function(e){return c(t.sashDragState.current,e.altKey)})),Object(p["a"])(document.body,"keyup")((function(){return c(t.sashDragState.current,!1)}))),c=function(e,i){var o,n,r=t.viewItems.map((function(e){return e.size})),s=Number.NEGATIVE_INFINITY,a=Number.POSITIVE_INFINITY;if(t.inverseAltBehavior&&(i=!i),i){var l=d===t.sashItems.length-1;if(l){var c=t.viewItems[d];s=(c.minimumSize-c.size)/2,a=(c.maximumSize-c.size)/2}else{c=t.viewItems[d+1];s=(c.size-c.maximumSize)/2,a=(c.size-c.minimumSize)/2}}if(!i){var p=Object(h["u"])(d,-1),f=Object(h["u"])(d+1,t.viewItems.length),m=p.reduce((function(e,i){return e+(t.viewItems[i].minimumSize-r[i])}),0),v=p.reduce((function(e,i){return e+(t.viewItems[i].viewMaximumSize-r[i])}),0),b=0===f.length?Number.POSITIVE_INFINITY:f.reduce((function(e,i){return e+(r[i]-t.viewItems[i].minimumSize)}),0),y=0===f.length?Number.NEGATIVE_INFINITY:f.reduce((function(e,i){return e+(r[i]-t.viewItems[i].viewMaximumSize)}),0),g=Math.max(m,y),_=Math.min(b,v),S=t.findFirstSnapIndex(p),w=t.findFirstSnapIndex(f);if("number"===typeof S){c=t.viewItems[S];var N=Math.floor(c.viewMinimumSize/2);o={index:S,limitDelta:c.visible?g-N:g+N,size:c.size}}if("number"===typeof w){c=t.viewItems[w],N=Math.floor(c.viewMinimumSize/2);n={index:w,limitDelta:c.visible?_+N:_-N,size:c.size}}}t.sashDragState={start:e,current:e,index:d,sizes:r,minDelta:s,maxDelta:a,alt:i,snapBefore:o,snapAfter:n,disposable:u}};c(o,n)},t.prototype.onSashChange=function(e){var t=e.current,i=this.sashDragState,o=i.index,n=i.start,r=i.sizes,s=i.alt,a=i.minDelta,l=i.maxDelta,d=i.snapBefore,h=i.snapAfter;this.sashDragState.current=t;var u=t-n,c=this.resize(o,u,r,void 0,void 0,a,l,d,h);if(s){var p=o===this.sashItems.length-1,f=this.viewItems.map((function(e){return e.size})),m=p?o:o+1,v=this.viewItems[m],b=v.size-v.maximumSize,y=v.size-v.minimumSize,g=p?o-1:o+1;this.resize(g,-c,f,void 0,void 0,b,y)}this.distributeEmptySpace(),this.layoutViews()},t.prototype.onSashEnd=function(e){this._onDidSashChange.fire(e),this.sashDragState.disposable.dispose(),this.saveProportions();for(var t=0,i=this.viewItems;t<i.length;t++){var o=i[t];o.enabled=!0}},t.prototype.onViewChange=function(e,t){var i=this.viewItems.indexOf(e);i<0||i>=this.viewItems.length||(t="number"===typeof t?t:e.size,t=Object(d["a"])(t,e.minimumSize,e.maximumSize),this.inverseAltBehavior&&i>0?(this.resize(i-1,Math.floor((e.size-t)/2)),this.distributeEmptySpace(),this.layoutViews()):(e.size=t,this.relayout([i],void 0)))},t.prototype.resizeView=function(e,t){var i=this;if(this.state!==o.Idle)throw new Error("Cant modify splitview");if(this.state=o.Busy,!(e<0||e>=this.viewItems.length)){var n=Object(h["u"])(this.viewItems.length).filter((function(t){return t!==e})),r=m(n.filter((function(e){return 1===i.viewItems[e].priority})),[e]),s=n.filter((function(e){return 2===i.viewItems[e].priority})),a=this.viewItems[e];t=Math.round(t),t=Object(d["a"])(t,a.minimumSize,Math.min(a.maximumSize,this.size)),a.size=t,this.relayout(r,s),this.state=o.Idle}},t.prototype.distributeViewSizes=function(){for(var e=this,t=[],i=0,o=0,n=this.viewItems;o<n.length;o++){var r=n[o];r.maximumSize-r.minimumSize>0&&(t.push(r),i+=r.size)}for(var s=Math.floor(i/t.length),a=0,l=t;a<l.length;a++){r=l[a];r.size=Object(d["a"])(s,r.minimumSize,r.maximumSize)}var u=Object(h["u"])(this.viewItems.length),c=u.filter((function(t){return 1===e.viewItems[t].priority})),p=u.filter((function(t){return 2===e.viewItems[t].priority}));this.relayout(c,p)},t.prototype.getViewSize=function(e){return e<0||e>=this.viewItems.length?-1:this.viewItems[e].size},t.prototype.doAddView=function(e,t,i,n){var a=this;if(void 0===i&&(i=this.viewItems.length),this.state!==o.Idle)throw new Error("Cant modify splitview");this.state=o.Busy;var d=l["a"](".split-view-view");i===this.viewItems.length?this.viewContainer.appendChild(d):this.viewContainer.insertBefore(d,this.viewContainer.children.item(i));var c,p=e.onDidChange((function(e){return a.onViewChange(b,e)})),f=Object(r["h"])((function(){return a.viewContainer.removeChild(d)})),m=Object(r["e"])(p,f);c="number"===typeof t?t:"split"===t.type?this.getViewSize(t.index)/2:"invisible"===t.type?{cachedVisibleSize:t.cachedVisibleSize}:e.minimumSize;var v,b=0===this.orientation?new y(d,e,c,m):new g(d,e,c,m);if(this.viewItems.splice(i,0,b),this.viewItems.length>1){var _=0===this.orientation?1:0,S=0===this.orientation?{getHorizontalSashTop:function(e){return a.getSashPosition(e)}}:{getVerticalSashLeft:function(e){return a.getSashPosition(e)}},w=new u["a"](this.sashContainer,S,{orientation:_,orthogonalStartSash:this.orthogonalStartSash,orthogonalEndSash:this.orthogonalEndSash}),N=0===this.orientation?function(e){return{sash:w,start:e.startY,current:e.currentY,alt:e.altKey}}:function(e){return{sash:w,start:e.startX,current:e.currentX,alt:e.altKey}},C=s["b"].map(w.onDidStart,N),D=C(this.onSashStart,this),O=s["b"].map(w.onDidChange,N),P=O(this.onSashChange,this),z=s["b"].map(w.onDidEnd,(function(){return Object(h["k"])(a.sashItems,(function(e){return e.sash===w}))})),E=z(this.onSashEnd,this),I=w.onDidReset((function(){var e=Object(h["k"])(a.sashItems,(function(e){return e.sash===w})),t=Object(h["u"])(e,-1),i=Object(h["u"])(e+1,a.viewItems.length),o=a.findFirstSnapIndex(t),n=a.findFirstSnapIndex(i);("number"!==typeof o||a.viewItems[o].visible)&&("number"!==typeof n||a.viewItems[n].visible)&&a._onDidSashReset.fire(e)})),j=Object(r["e"])(D,P,E,I,w),T={sash:w,disposable:j};this.sashItems.splice(i-1,0,T)}d.appendChild(e.element),"number"!==typeof t&&"split"===t.type&&(v=[t.index]),n||this.relayout([i],v),this.state=o.Idle,n||"number"===typeof t||"distribute"!==t.type||this.distributeViewSizes()},t.prototype.relayout=function(e,t){var i=this.viewItems.reduce((function(e,t){return e+t.size}),0);this.resize(this.viewItems.length-1,this.size-i,void 0,e,t),this.distributeEmptySpace(),this.layoutViews(),this.saveProportions()},t.prototype.resize=function(e,t,i,o,n,r,s,a,l){var u=this;if(void 0===i&&(i=this.viewItems.map((function(e){return e.size}))),void 0===r&&(r=Number.NEGATIVE_INFINITY),void 0===s&&(s=Number.POSITIVE_INFINITY),e<0||e>=this.viewItems.length)return 0;var c=Object(h["u"])(e,-1),p=Object(h["u"])(e+1,this.viewItems.length);if(n)for(var f=0,m=n;f<m.length;f++){var v=m[f];Object(h["t"])(c,v),Object(h["t"])(p,v)}if(o)for(var b=0,y=o;b<y.length;b++){var g=y[b];Object(h["s"])(c,g),Object(h["s"])(p,g)}var _=c.map((function(e){return u.viewItems[e]})),S=c.map((function(e){return i[e]})),w=p.map((function(e){return u.viewItems[e]})),N=p.map((function(e){return i[e]})),C=c.reduce((function(e,t){return e+(u.viewItems[t].minimumSize-i[t])}),0),D=c.reduce((function(e,t){return e+(u.viewItems[t].maximumSize-i[t])}),0),O=0===p.length?Number.POSITIVE_INFINITY:p.reduce((function(e,t){return e+(i[t]-u.viewItems[t].minimumSize)}),0),P=0===p.length?Number.NEGATIVE_INFINITY:p.reduce((function(e,t){return e+(i[t]-u.viewItems[t].maximumSize)}),0),z=Math.max(C,P,r),E=Math.min(O,D,s),I=!1;if(a){var j=this.viewItems[a.index],T=t>=a.limitDelta;I=T!==j.visible,j.setVisible(T,a.size)}if(!I&&l){j=this.viewItems[l.index],T=t<l.limitDelta;I=T!==j.visible,j.setVisible(T,l.size)}if(I)return this.resize(e,t,i,o,n,r,s);t=Object(d["a"])(t,z,E);for(var x=0,M=t;x<_.length;x++){var L=_[x],R=Object(d["a"])(S[x]+M,L.minimumSize,L.maximumSize),A=R-S[x];M-=A,L.size=R}x=0;for(var k=t;x<w.length;x++){L=w[x],R=Object(d["a"])(N[x]-k,L.minimumSize,L.maximumSize),A=R-N[x];k+=A,L.size=R}return t},t.prototype.distributeEmptySpace=function(e){for(var t=this,i=this.viewItems.reduce((function(e,t){return e+t.size}),0),o=this.size-i,n=Object(h["u"])(this.viewItems.length-1,-1),r=n.filter((function(e){return 1===t.viewItems[e].priority})),s=n.filter((function(e){return 2===t.viewItems[e].priority})),a=0,l=s;a<l.length;a++){var u=l[a];Object(h["t"])(n,u)}for(var c=0,p=r;c<p.length;c++){u=p[c];Object(h["s"])(n,u)}"number"===typeof e&&Object(h["s"])(n,e);for(var f=0;0!==o&&f<n.length;f++){var m=this.viewItems[n[f]],v=Object(d["a"])(m.size+o,m.minimumSize,m.maximumSize),b=v-m.size;o-=b,m.size=v}},t.prototype.layoutViews=function(){this.contentSize=this.viewItems.reduce((function(e,t){return e+t.size}),0);for(var e=0,t=0,i=this.viewItems;t<i.length;t++){var o=i[t];o.layout(e,this.layoutContext),e+=o.size}this.sashItems.forEach((function(e){return e.sash.layout()})),this.updateSashEnablement()},t.prototype.updateSashEnablement=function(){var e=!1,t=this.viewItems.map((function(t){return e=t.size-t.minimumSize>0||e}));e=!1;var i=this.viewItems.map((function(t){return e=t.maximumSize-t.size>0||e})),o=m(this.viewItems).reverse();e=!1;var n=o.map((function(t){return e=t.size-t.minimumSize>0||e})).reverse();e=!1;for(var r=o.map((function(t){return e=t.maximumSize-t.size>0||e})).reverse(),s=0,a=0;a<this.sashItems.length;a++){var l=this.sashItems[a].sash,d=this.viewItems[a];s+=d.size;var u=!(t[a]&&r[a+1]),c=!(i[a]&&n[a+1]);if(u&&c){var p=Object(h["u"])(a,-1),f=Object(h["u"])(a+1,this.viewItems.length),v=this.findFirstSnapIndex(p),b=this.findFirstSnapIndex(f),y="number"===typeof v&&!this.viewItems[v].visible,g="number"===typeof b&&!this.viewItems[b].visible;y&&n[a]&&(s>0||this.startSnappingEnabled)?l.state=1:g&&t[a]&&(s<this.contentSize||this.endSnappingEnabled)?l.state=2:l.state=0}else l.state=u&&!c?1:!u&&c?2:3}},t.prototype.getSashPosition=function(e){for(var t=0,i=0;i<this.sashItems.length;i++)if(t+=this.viewItems[i].size,this.sashItems[i].sash===e)return Math.min(t,this.contentSize-2);return 0},t.prototype.findFirstSnapIndex=function(e){for(var t=0,i=e;t<i.length;t++){var o=i[t],n=this.viewItems[o];if(n.visible&&n.snap)return o}for(var r=0,s=e;r<s.length;r++){o=s[r],n=this.viewItems[o];if(n.visible&&n.maximumSize-n.minimumSize>0)return;if(!n.visible&&n.snap)return o}},t.prototype.dispose=function(){e.prototype.dispose.call(this),this.viewItems.forEach((function(e){return e.dispose()})),this.viewItems=[],this.sashItems.forEach((function(e){return e.disposable.dispose()})),this.sashItems=[]},t}(r["a"])},e750:function(e,t,i){},e818e:function(e,t,i){"use strict";i.d(t,"a",(function(){return s}));var o=i("c482"),n=i("9d1c"),r=function(){var e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i])},e(t,i)};return function(t,i){function o(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(o.prototype=i.prototype,new o)}}(),s=function(e){function t(t,i,o,n,r,s){void 0===s&&(s={});var a=e.call(this,t,i,o,n,s)||this;return a.user=t,a.dataSource=r,a.identityProvider=s.identityProvider,a}return r(t,e),t.prototype.createModel=function(e,t,i){return new n["a"](e,t,i)},t}(o["a"])}}]);