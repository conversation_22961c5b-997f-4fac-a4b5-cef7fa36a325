(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-ea5555c2"],{"01e6":function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("dialogComp",{staticClass:"sourceDom",attrs:{hideHeader:!1,needClose:"true",backgroundColor:"rgba(31, 41, 51, 0.45)",zIndex:"5",draw:!0,left:e.dialogLeft,drag:!0,title:e.$t("dialog.topology.table"),icon:"icon-details",width:e.dialogWidth,height:e.dialogHeight,type:"detailInfo",top:e.TopToolbarHeight+40,dragTopOffset:e.dragTopOffset},on:{close:e.closeDialog,moving:e.dialogMoving},scopedSlots:e._u([{key:"center",fn:function(){return[r("div",{staticClass:"topology-container"},[r("div",{staticClass:"tree-list"},[r("el-tree",{ref:"viewTree",attrs:{indent:20,data:e.treeList,"node-key":"id",props:e.defaultTreeProps},on:{"node-click":e.handleNodeClick},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.node,a=t.data;return[a.parentId?r("span",[r("el-tooltip",{attrs:{content:n.label,enterable:"false",placement:"left"}},[r("span",[e._v(e._s(n.label))])])],1):r("span",[e._v(e._s(n.label))])]}}])})],1),r("div",{staticClass:"graph-wh",attrs:{id:"container"}})])]},proxy:!0}])})},a=[],i=r("c7eb"),o=r("1da1"),f=r("b85c"),u=r("5530"),s=(r("ac1f"),r("1276"),r("d81d"),r("d3b7"),r("159b"),r("99af"),r("7db0"),r("b0c0"),r("e9c4"),r("e439"),r("4de4"),r("4d63"),r("c607"),r("2c3e"),r("25f0"),r("00b4"),r("cb29"),r("7c3e0")),c={},l={name:"Topology",data:function(){return{selectedSystemChildren:[],currentNodeKey:null,treeData:[],dialogLeft:0,dialogHeight:500,dialogWidth:800,TopToolbarHeight:122,treeList:[],defaultTreeProps:{children:"children",label:"label",parentId:"parentId",isLeaf:"iselem",id:"id"}}},watch:{treeData:{deep:!0,handler:function(e,t){var r=this;setTimeout((function(){r.init()}),300)}}},computed:{modelData:function(){return this.$store.state.topology.topoCurrentModel},viewpoint:function(){return this.$store.state.topology.viewpoint},dragTopOffset:function(){return this.$store.state.scene.topToolBarHeight}},created:function(){var e=document.querySelector(".sceneManageDom").style.left;parseInt(e)<0?this.dialogLeft=30:this.dialogLeft=335,this.changeManageSize(),window.addEventListener("resize",this.changeManageSize)},mounted:function(){var e=this;this.lastSelectedNodeArr=[],this.currentHoverNodeData=null,this.noteParent=[],this.noteChildren=[],this.$bus.on("selectTopoloNodeId",this.selectTopoloNodeId),this.$nextTick((function(){e.getList()})),window.checkCurrentNode=function(t){if(null!=e.currentHoverNodeData){var r=c.findById(e.currentHoverNodeData.id);"upstream"==t?(e.noteParent=[],"111"!==r._cfg.id&&e.noteParent.push(r),e.getNoteParent(r._cfg.parent),e.findNodesByModels(e.noteParent)):(e.noteChildren=[],r.getModel().collapsed&&(r.getModel().collapsed=!r.getModel().collapsed,c.setItemState(r,"collapsed",r.getModel().collapsed),c.layout()),e.noteChildren.push(r),r._cfg.children&&e.getNoteChildren(r._cfg.children),e.findNodesByModels(e.noteChildren))}}},beforeDestroy:function(){window.removeEventListener("resize",this.changeManageSize),this.$bus.off("selectTopoloNodeId",this.selectTopoloNodeId)},methods:{selectTopoloNodeId:function(e){if(e){var t=e.split("^")[1],r=c.findById(t);r&&(c.focusItem(t),this.selectNode({item:r,target:null}))}},selectElement:function(e){var t=window.scene.features.get(this.modelData.id),r=t.systems&&t.systems.map((function(e){return Object(u["a"])({},e.topo)})),n=this.findValueInTree(r,e.id);this.handleSystemNodeClick(n)},findValueInTree:function(e,t){var r,n=Object(f["a"])(e);try{for(n.s();!(r=n.n()).done;){var a=r.value;if(a.id===t)return a;if(a.Children&&a.Children.length>0){var i=this.findValueInTree(a.Children,t);if(i)return i}}}catch(o){n.e(o)}finally{n.f()}return null},setSystemChildrenSelected:function(e){var t=this;e.forEach((function(e){var r="".concat(t.modelData.id,"^").concat(e.id);t.selectedSystemChildren.push(r),e.Attach&&e.Attach.length&&e.Attach.forEach((function(e){t.selectedSystemChildren.push("".concat(t.modelData.id,"^").concat(e.id))})),e.Children&&t.setSystemChildrenSelected(e.Children)}))},handleSystemNodeClick:function(e){window.scene.clearSelection(),this.selectedSystemChildren=[],e.Children.length?this.setSystemChildrenSelected(e.Children):this.selectedSystemChildren=["".concat(this.modelData.id,"^").concat(e.id)];var t=window.scene.createArrayObject(this.selectedSystemChildren);t.select(),window.scene.fit(this.selectedSystemChildren),window.scene.render()},handleNodeClick:function(e){var t;e.parentId&&(t=c.findById(e.parentId)),t&&t.getModel().collapsed&&(t.getModel().collapsed=!t.getModel().collapsed,c.setItemState(t,"collapsed",t.getModel().collapsed),c.layout()),c.focusItem(e.id);var r=c.findById(e.id);this.selectNode({item:r,target:null})},selectNode:function(e){if(e.target&&"collapse-icon"===e.target.get("name"))return e.item.getModel().collapsed=!e.item.getModel().collapsed,c.setItemState(e.item,"collapsed",e.item.getModel().collapsed),void c.layout();var t=e.item;this.findNodesByModels([t]),this.currentHoverNodeData=e.item.getModel()},changeManageSize:function(){var e=document.body.clientHeight;this.dialogHeight=100-(this.TopToolbarHeight+50)/(e/100)+"%",this.dialogWidth=(document.body.clientWidth-this.dialogLeft)/2},dialogMoving:function(){var e=document.querySelector("#container");c&&!c.get("destroyed")&&e&&e.scrollWidth&&e.scrollHeight&&c.changeSize(e.scrollWidth,e.scrollHeight)},closeDialog:function(){this.$store.commit("toggleTopologyActive","closeTopology"),window.scene.restoreViewpoint(JSON.parse(this.viewpoint)),this.$store.commit("setViewpoint",{}),this.$store.commit("toogleElementMenu",""),this.$store.commit("selectElementData",{})},getNoteParent:function(e){"111"!==e._cfg.id&&(this.noteParent.push(e),this.getNoteParent(e._cfg.parent))},getNoteChildren:function(e){var t=this;e.forEach((function(e){t.noteChildren.push(e),e._cfg.children&&t.getNoteChildren(e._cfg.children)}))},recursionSetTopoType:function(e){var t=this;return e.forEach((function(e){e.children&&e.children.length>0?(e.class="c1",e.label=t.fittingString(e.label,136,12),t.recursionSetTopoType(e.children)):(e.class="c2",e.label=t.fittingString(e.label,136,12))})),e},getList:function(){var e=this;return Object(o["a"])(Object(i["a"])().mark((function t(){var r,n,a,o,f,s,c,l;return Object(i["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:r=window.scene.features.get(e.modelData.id),n=r.systems,n=n.map((function(e){return Object(u["a"])({},e.topo)})),n=n.reduce((function(e,t){var r=e.find((function(e){return e.name===t.name}));return r?r.Children=r.Children.concat(t.Children):e.push(t),e}),[]),a=[],n.forEach((function(e,t){a.push({id:e.id,label:e.name,parentId:"",children:[]}),e.Children.forEach((function(r){a[t].children.push({id:r.id,label:r.name,parentId:e.id,children:[]})}))})),e.treeList=a,o=JSON.parse(JSON.stringify(n)),f=[["id","id"],["label","name"],["children","Children"]],s=e.changeKeys(o,f),c=e.recursionSetTopoType(s),l=[{id:"111",label:"根节点",children:c,class:"c0"}],e.treeData=l;case 13:case"end":return t.stop()}}),t)})))()},changeKeys:function(e,t){var r=this;return e.forEach((function(e){t.forEach((function(n){var a=n[1],i=n[0];a!==i&&e[a]&&(Object.defineProperty(e,i,Object.getOwnPropertyDescriptor(e,a)),delete e[a],e.children&&e.children.length>0&&r.changeKeys(e.children,t))}))})),e},findNodesByModels:function(e){var t=this,r=c.get("autoPaint");c.setAutoPaint(!1),this.lastSelectedNodeArr.length>0&&(this.lastSelectedNodeArr.forEach((function(e){var r=e.getModel();c.setItemState(e,"isSelected",!1);var n=window.scene.findObject("".concat(t.modelData.id,"^").concat(r.id));n&&(n.selected=!1)})),this.$store.commit("toogleElementMenu",""),window.scene.render(),this.lastSelectedNodeArr=[]),e&&(window.scene.execute("reset"),setTimeout((function(){t.lastSelectedNodeArr=e;var r=[];e.forEach((function(e){var n=e.getModel();c.setItemState(e,"isSelected",!0);var a=window.scene.findObject("".concat(t.modelData.id,"^").concat(n.id));a&&(a.selected=!0),r.push("".concat(t.modelData.id,"^").concat(n.id))})),window.scene.fit(r);var n=window.scene.getSelection().filter((function(e){return"element"==e.type}));if(0!==n.length){var a={objectIDs:n.map((function(e){return e.id}))};window.scene.execute("isolate",a),t.$store.commit("updateMenuExecuteState",{random:(new Date).getTime()}),window.scene.render(),t.$store.commit("toogleElementMenu","model")}else t.$store.commit("toogleElementMenu","")}),100)),c.paint(),c.setAutoPaint(r)},fittingString:function(e,t,r){var n=0,a=e,i=new RegExp("[一-龥]+");return e.split("").forEach((function(o,f){n>t||(i.test(o)?n+=r:n+=s["a"].Util.getLetterWidth(o,r),n>t&&(a="".concat(e.substr(0,f),"\n").concat(e.substr(f))))})),a},setCollapsed:function(e){var t=this;return e.depth<=2&&(e.children?e.children.forEach((function(e){e.collapsed=!0,t.setCollapsed(e)})):e.collapsed=!0),e},init:function(){var e=this,t=this,r=this.treeData[0],n=new s["a"].ToolBar({className:"g6-component-toolbar",getContent:function(){return t.setTooltipHtml()},handleClick:function(e,t){n.handleDefaultOperator(e,t)}});new s["a"].Grid;s["a"].registerNode("icon-node",{options:{size:[60,20],stroke:"",fill:""},labelPosition:"center",draw:function(e,t){var r=e.size[0],n=e.size[1],a=e.labelCfg,i=void 0===a?{}:a,o=t.addShape("rect",{attrs:{x:-r/2,y:-n/2,width:r,height:n,fill:e.children.length?"c0"===e.class?"#6A6A6A":"#FFF":"#4CC2DA",radius:[20,20],lineWidth:0},name:"main-box",draggable:!0});return e.children.length&&t.addShape("marker",{attrs:{x:r/2,y:0,r:6,cursor:"pointer",symbol:e.collapsed?s["a"].Marker.expand:s["a"].Marker.collapse,stroke:"#666",lineWidth:1,fill:"#fff"},name:"collapse-icon"}),t.addShape("text",{attrs:Object(u["a"])(Object(u["a"])({},i.style),{},{x:0,y:0,textAlign:"center",textBaseline:"middle",text:e.label,fill:"c0"===e.class||"c2"===e.class?"#fff":"#000"}),name:"description"}),o},setState:function(e,t,r){if("collapsed"===e){var n=r.get("group").find((function(e){return"collapse-icon"===e.get("name")})),a=t?s["a"].Marker.expand:s["a"].Marker.collapse;n.attr("symbol",a)}var i=r.getContainer(),o=r.get("model").class,f=i.find((function(e){return"main-box"===e.get("name")})),u=i.find((function(e){return"description"===e.get("name")}));"isSelected"===e?t?(f.attr("fill","#FF6640"),u.attr("fill","#FFF")):(f.attr("fill","c0"===o?"#6A6A6A":"c1"===o?"#FFF":"#4CC2DA"),u.attr("fill","c0"===o||"c2"===o?"#fff":"#000")):"isHovered"===e&&(t?(f.attr("fill","#FF6640"),u.attr("fill","#FFF")):r.hasState("isSelected")||(f.attr("fill","c0"===o?"#6A6A6A":"c1"===o?"#FFF":"#4CC2DA"),u.attr("fill","c0"===o||"c2"===o?"#fff":"#000")))}}),s["a"].registerEdge("flow-line",{draw:function(e,t){var r=e.startPoint,n=e.endPoint,a=e.style,i=t.addShape("path",{attrs:{stroke:a.stroke,endArrow:a.endArrow,path:[["M",r.x,r.y],["L",n.x/2+r.x/2,r.y],["L",n.x/2+r.x/2,n.y],["L",n.x,n.y]]}});return i}}),s["a"].registerBehavior("right-drag-canvas",{getEvents:function(){return{"canvas:mousedown":"onMousedown","canvas:mousemove":"onMousemove"}},onMousedown:function(e){if(2==e.originalEvent.button){var t=o.querySelector("#container canvas");t.style.cursor="pointer",t.onmouseup=function(e){if(2==e.button){var t=o.querySelector("#container canvas");t.style.cursor="default"}}}},onMousemove:function(e){2==e.originalEvent.buttons&&c.translate(e.originalEvent.movementX,e.originalEvent.movementY)}});var a={stroke:"#FFFFFF"},i={type:"compactBox",direction:"LR",getId:function(e){return e.id},getHeight:function(){return 20},getWidth:function(){return 70},getVGap:function(){return 40},getHGap:function(){return 70}},o=document.getElementById("container");new s["a"].Minimap({size:[o.clientWidth/4,o.clientHeight/4]});c=new s["a"].TreeGraph({container:"container",linkCenter:!0,plugins:[n],modes:{default:["right-drag-canvas","zoom-canvas"]},anchorPoints:[[.5,0]],defaultNode:{type:"icon-node",size:[140,40]},defaultEdge:{type:"flow-line",style:a},layout:i});var f=[r];f=this.customConfigNodeStyle(f),c.data(f[0]),c.render();var l=c.get("width"),d=c.get("height"),h={x:l+20,y:d};c.zoom(.6,h),c.on("node:mouseenter",(function(e){e.item;c.setItemState(e.item,"isHovered",!0)})),c.on("node:mouseleave",(function(e){e.item;c.setItemState(e.item,"isHovered",!1)})),c.on("node:click",(function(t){var r=e.$refs.viewTree;if(1===t.item.getModel().depth)e.currentNodeKey=t.item.getModel().id,e.$nextTick((function(){r.setCurrentKey([t.item.getModel().id]),r.setCheckedKeys([t.item.getModel().id])}));else if(2===t.item.getModel().depth){var n=t.item._cfg.parent._cfg.id;e.$nextTick((function(){r.store.setDefaultExpandedKeys([n]),r.setCurrentKey([t.item.getModel().id]),r.setCheckedKeys([t.item.getModel().id])}))}else e.$nextTick((function(){r.setCurrentKey([]),r.setCheckedKeys([])}));e.selectNode(t)})),c.on("canvas:click",(function(t){var r=c.get("autoPaint");c.setAutoPaint(!1);var n=c.findAllByState("node","isSelected");n.forEach((function(e){c.setItemState(e,"isSelected",!1)})),e.currentHoverNodeData=null;var a=e.$refs.viewTree;e.$nextTick((function(){a.setCurrentKey([]),a.setCheckedKeys([])})),c.paint(),c.setAutoPaint(r)})),"undefined"!==typeof window&&(window.onresize=function(){c&&!c.get("destroyed")&&o&&o.scrollWidth&&o.scrollHeight&&c.changeSize(o.scrollWidth,o.scrollHeight)})},setTooltipHtml:function(e){return'<div class="title">\n                        <div class="item" onclick="window.checkCurrentNode(\'upstream\')">\n                            '.concat(this.$t("dialog.topology.table1"),'\n                        </div>\n                        <div class="item interval" style="margin-left: 4px" onclick="window.checkCurrentNode(\'downstream\')">\n                            ').concat(this.$t("dialog.topology.table2"),"\n                        </div>\n                   </div>")},customConfigNodeStyle:function(e){var t=this;return e.forEach((function(e){switch(e.style||(e.style={}),e.linkPoints||(e.linkPoints={}),e.labelCfg||(e.labelCfg={style:{}}),e.class){case"c0":e.style.fill="#6A6A6A",e.labelCfg.style.fill="#FFFFFF",e.linkPoints.top=!1;break;case"c1":e.style.fill="#FFFFFF",e.labelCfg.style.fill="#000000";break;case"c2":e.style.fill="#4CC2DA",e.labelCfg.style.fill="#FFFFFF";break}e.children&&e.children.length>0&&t.customConfigNodeStyle(e.children)})),e}}},d=l,h=(r("a23a"),r("ad06"),r("2877")),p=Object(h["a"])(d,n,a,!1,null,"587990d2",null);t["default"]=p.exports},2514:function(e,t,r){"use strict";var n=r("b8fa"),a=Array.prototype.concat,i=Array.prototype.slice,o=e.exports=function(e){for(var t=[],r=0,o=e.length;r<o;r++){var f=e[r];n(f)?t=a.call(t,i.call(f)):t.push(f)}return t};o.wrap=function(e){return function(){return e(o(arguments))}}},"66cb":function(e,t,r){var n;(function(a){var i=/^\s+/,o=/\s+$/,f=0,u=a.round,s=a.min,c=a.max,l=a.random;function d(e,t){if(e=e||"",t=t||{},e instanceof d)return e;if(!(this instanceof d))return new d(e,t);var r=h(e);this._originalInput=e,this._r=r.r,this._g=r.g,this._b=r.b,this._a=r.a,this._roundA=u(100*this._a)/100,this._format=t.format||r.format,this._gradientType=t.gradientType,this._r<1&&(this._r=u(this._r)),this._g<1&&(this._g=u(this._g)),this._b<1&&(this._b=u(this._b)),this._ok=r.ok,this._tc_id=f++}function h(e){var t={r:0,g:0,b:0},r=1,n=null,a=null,i=null,o=!1,f=!1;return"string"==typeof e&&(e=Y(e)),"object"==typeof e&&(Q(e.r)&&Q(e.g)&&Q(e.b)?(t=p(e.r,e.g,e.b),o=!0,f="%"===String(e.r).substr(-1)?"prgb":"rgb"):Q(e.h)&&Q(e.s)&&Q(e.v)?(n=q(e.s),a=q(e.v),t=v(e.h,n,a),o=!0,f="hsv"):Q(e.h)&&Q(e.s)&&Q(e.l)&&(n=q(e.s),i=q(e.l),t=b(e.h,n,i),o=!0,f="hsl"),e.hasOwnProperty("a")&&(r=e.a)),r=R(r),{ok:o,format:e.format||f,r:s(255,c(t.r,0)),g:s(255,c(t.g,0)),b:s(255,c(t.b,0)),a:r}}function p(e,t,r){return{r:255*L(e,255),g:255*L(t,255),b:255*L(r,255)}}function m(e,t,r){e=L(e,255),t=L(t,255),r=L(r,255);var n,a,i=c(e,t,r),o=s(e,t,r),f=(i+o)/2;if(i==o)n=a=0;else{var u=i-o;switch(a=f>.5?u/(2-i-o):u/(i+o),i){case e:n=(t-r)/u+(t<r?6:0);break;case t:n=(r-e)/u+2;break;case r:n=(e-t)/u+4;break}n/=6}return{h:n,s:a,l:f}}function b(e,t,r){var n,a,i;function o(e,t,r){return r<0&&(r+=1),r>1&&(r-=1),r<1/6?e+6*(t-e)*r:r<.5?t:r<2/3?e+(t-e)*(2/3-r)*6:e}if(e=L(e,360),t=L(t,100),r=L(r,100),0===t)n=a=i=r;else{var f=r<.5?r*(1+t):r+t-r*t,u=2*r-f;n=o(u,f,e+1/3),a=o(u,f,e),i=o(u,f,e-1/3)}return{r:255*n,g:255*a,b:255*i}}function g(e,t,r){e=L(e,255),t=L(t,255),r=L(r,255);var n,a,i=c(e,t,r),o=s(e,t,r),f=i,u=i-o;if(a=0===i?0:u/i,i==o)n=0;else{switch(i){case e:n=(t-r)/u+(t<r?6:0);break;case t:n=(r-e)/u+2;break;case r:n=(e-t)/u+4;break}n/=6}return{h:n,s:a,v:f}}function v(e,t,r){e=6*L(e,360),t=L(t,100),r=L(r,100);var n=a.floor(e),i=e-n,o=r*(1-t),f=r*(1-i*t),u=r*(1-(1-i)*t),s=n%6,c=[r,f,o,o,u,r][s],l=[u,r,r,f,o,o][s],d=[o,o,u,r,r,f][s];return{r:255*c,g:255*l,b:255*d}}function y(e,t,r,n){var a=[W(u(e).toString(16)),W(u(t).toString(16)),W(u(r).toString(16))];return n&&a[0].charAt(0)==a[0].charAt(1)&&a[1].charAt(0)==a[1].charAt(1)&&a[2].charAt(0)==a[2].charAt(1)?a[0].charAt(0)+a[1].charAt(0)+a[2].charAt(0):a.join("")}function x(e,t,r,n,a){var i=[W(u(e).toString(16)),W(u(t).toString(16)),W(u(r).toString(16)),W(G(n))];return a&&i[0].charAt(0)==i[0].charAt(1)&&i[1].charAt(0)==i[1].charAt(1)&&i[2].charAt(0)==i[2].charAt(1)&&i[3].charAt(0)==i[3].charAt(1)?i[0].charAt(0)+i[1].charAt(0)+i[2].charAt(0)+i[3].charAt(0):i.join("")}function w(e,t,r,n){var a=[W(G(n)),W(u(e).toString(16)),W(u(t).toString(16)),W(u(r).toString(16))];return a.join("")}function _(e,t){t=0===t?0:t||10;var r=d(e).toHsl();return r.s-=t/100,r.s=V(r.s),d(r)}function A(e,t){t=0===t?0:t||10;var r=d(e).toHsl();return r.s+=t/100,r.s=V(r.s),d(r)}function k(e){return d(e).desaturate(100)}function S(e,t){t=0===t?0:t||10;var r=d(e).toHsl();return r.l+=t/100,r.l=V(r.l),d(r)}function E(e,t){t=0===t?0:t||10;var r=d(e).toRgb();return r.r=c(0,s(255,r.r-u(-t/100*255))),r.g=c(0,s(255,r.g-u(-t/100*255))),r.b=c(0,s(255,r.b-u(-t/100*255))),d(r)}function O(e,t){t=0===t?0:t||10;var r=d(e).toHsl();return r.l-=t/100,r.l=V(r.l),d(r)}function T(e,t){var r=d(e).toHsl(),n=(r.h+t)%360;return r.h=n<0?360+n:n,d(r)}function C(e){var t=d(e).toHsl();return t.h=(t.h+180)%360,d(t)}function j(e){var t=d(e).toHsl(),r=t.h;return[d(e),d({h:(r+120)%360,s:t.s,l:t.l}),d({h:(r+240)%360,s:t.s,l:t.l})]}function D(e){var t=d(e).toHsl(),r=t.h;return[d(e),d({h:(r+90)%360,s:t.s,l:t.l}),d({h:(r+180)%360,s:t.s,l:t.l}),d({h:(r+270)%360,s:t.s,l:t.l})]}function F(e){var t=d(e).toHsl(),r=t.h;return[d(e),d({h:(r+72)%360,s:t.s,l:t.l}),d({h:(r+216)%360,s:t.s,l:t.l})]}function M(e,t,r){t=t||6,r=r||30;var n=d(e).toHsl(),a=360/r,i=[d(e)];for(n.h=(n.h-(a*t>>1)+720)%360;--t;)n.h=(n.h+a)%360,i.push(d(n));return i}function P(e,t){t=t||6;var r=d(e).toHsv(),n=r.h,a=r.s,i=r.v,o=[],f=1/t;while(t--)o.push(d({h:n,s:a,v:i})),i=(i+f)%1;return o}d.prototype={isDark:function(){return this.getBrightness()<128},isLight:function(){return!this.isDark()},isValid:function(){return this._ok},getOriginalInput:function(){return this._originalInput},getFormat:function(){return this._format},getAlpha:function(){return this._a},getBrightness:function(){var e=this.toRgb();return(299*e.r+587*e.g+114*e.b)/1e3},getLuminance:function(){var e,t,r,n,i,o,f=this.toRgb();return e=f.r/255,t=f.g/255,r=f.b/255,n=e<=.03928?e/12.92:a.pow((e+.055)/1.055,2.4),i=t<=.03928?t/12.92:a.pow((t+.055)/1.055,2.4),o=r<=.03928?r/12.92:a.pow((r+.055)/1.055,2.4),.2126*n+.7152*i+.0722*o},setAlpha:function(e){return this._a=R(e),this._roundA=u(100*this._a)/100,this},toHsv:function(){var e=g(this._r,this._g,this._b);return{h:360*e.h,s:e.s,v:e.v,a:this._a}},toHsvString:function(){var e=g(this._r,this._g,this._b),t=u(360*e.h),r=u(100*e.s),n=u(100*e.v);return 1==this._a?"hsv("+t+", "+r+"%, "+n+"%)":"hsva("+t+", "+r+"%, "+n+"%, "+this._roundA+")"},toHsl:function(){var e=m(this._r,this._g,this._b);return{h:360*e.h,s:e.s,l:e.l,a:this._a}},toHslString:function(){var e=m(this._r,this._g,this._b),t=u(360*e.h),r=u(100*e.s),n=u(100*e.l);return 1==this._a?"hsl("+t+", "+r+"%, "+n+"%)":"hsla("+t+", "+r+"%, "+n+"%, "+this._roundA+")"},toHex:function(e){return y(this._r,this._g,this._b,e)},toHexString:function(e){return"#"+this.toHex(e)},toHex8:function(e){return x(this._r,this._g,this._b,this._a,e)},toHex8String:function(e){return"#"+this.toHex8(e)},toRgb:function(){return{r:u(this._r),g:u(this._g),b:u(this._b),a:this._a}},toRgbString:function(){return 1==this._a?"rgb("+u(this._r)+", "+u(this._g)+", "+u(this._b)+")":"rgba("+u(this._r)+", "+u(this._g)+", "+u(this._b)+", "+this._roundA+")"},toPercentageRgb:function(){return{r:u(100*L(this._r,255))+"%",g:u(100*L(this._g,255))+"%",b:u(100*L(this._b,255))+"%",a:this._a}},toPercentageRgbString:function(){return 1==this._a?"rgb("+u(100*L(this._r,255))+"%, "+u(100*L(this._g,255))+"%, "+u(100*L(this._b,255))+"%)":"rgba("+u(100*L(this._r,255))+"%, "+u(100*L(this._g,255))+"%, "+u(100*L(this._b,255))+"%, "+this._roundA+")"},toName:function(){return 0===this._a?"transparent":!(this._a<1)&&(I[y(this._r,this._g,this._b,!0)]||!1)},toFilter:function(e){var t="#"+w(this._r,this._g,this._b,this._a),r=t,n=this._gradientType?"GradientType = 1, ":"";if(e){var a=d(e);r="#"+w(a._r,a._g,a._b,a._a)}return"progid:DXImageTransform.Microsoft.gradient("+n+"startColorstr="+t+",endColorstr="+r+")"},toString:function(e){var t=!!e;e=e||this._format;var r=!1,n=this._a<1&&this._a>=0,a=!t&&n&&("hex"===e||"hex6"===e||"hex3"===e||"hex4"===e||"hex8"===e||"name"===e);return a?"name"===e&&0===this._a?this.toName():this.toRgbString():("rgb"===e&&(r=this.toRgbString()),"prgb"===e&&(r=this.toPercentageRgbString()),"hex"!==e&&"hex6"!==e||(r=this.toHexString()),"hex3"===e&&(r=this.toHexString(!0)),"hex4"===e&&(r=this.toHex8String(!0)),"hex8"===e&&(r=this.toHex8String()),"name"===e&&(r=this.toName()),"hsl"===e&&(r=this.toHslString()),"hsv"===e&&(r=this.toHsvString()),r||this.toHexString())},clone:function(){return d(this.toString())},_applyModification:function(e,t){var r=e.apply(null,[this].concat([].slice.call(t)));return this._r=r._r,this._g=r._g,this._b=r._b,this.setAlpha(r._a),this},lighten:function(){return this._applyModification(S,arguments)},brighten:function(){return this._applyModification(E,arguments)},darken:function(){return this._applyModification(O,arguments)},desaturate:function(){return this._applyModification(_,arguments)},saturate:function(){return this._applyModification(A,arguments)},greyscale:function(){return this._applyModification(k,arguments)},spin:function(){return this._applyModification(T,arguments)},_applyCombination:function(e,t){return e.apply(null,[this].concat([].slice.call(t)))},analogous:function(){return this._applyCombination(M,arguments)},complement:function(){return this._applyCombination(C,arguments)},monochromatic:function(){return this._applyCombination(P,arguments)},splitcomplement:function(){return this._applyCombination(F,arguments)},triad:function(){return this._applyCombination(j,arguments)},tetrad:function(){return this._applyCombination(D,arguments)}},d.fromRatio=function(e,t){if("object"==typeof e){var r={};for(var n in e)e.hasOwnProperty(n)&&(r[n]="a"===n?e[n]:q(e[n]));e=r}return d(e,t)},d.equals=function(e,t){return!(!e||!t)&&d(e).toRgbString()==d(t).toRgbString()},d.random=function(){return d.fromRatio({r:l(),g:l(),b:l()})},d.mix=function(e,t,r){r=0===r?0:r||50;var n=d(e).toRgb(),a=d(t).toRgb(),i=r/100,o={r:(a.r-n.r)*i+n.r,g:(a.g-n.g)*i+n.g,b:(a.b-n.b)*i+n.b,a:(a.a-n.a)*i+n.a};return d(o)},d.readability=function(e,t){var r=d(e),n=d(t);return(a.max(r.getLuminance(),n.getLuminance())+.05)/(a.min(r.getLuminance(),n.getLuminance())+.05)},d.isReadable=function(e,t,r){var n,a,i=d.readability(e,t);switch(a=!1,n=X(r),n.level+n.size){case"AAsmall":case"AAAlarge":a=i>=4.5;break;case"AAlarge":a=i>=3;break;case"AAAsmall":a=i>=7;break}return a},d.mostReadable=function(e,t,r){var n,a,i,o,f=null,u=0;r=r||{},a=r.includeFallbackColors,i=r.level,o=r.size;for(var s=0;s<t.length;s++)n=d.readability(e,t[s]),n>u&&(u=n,f=d(t[s]));return d.isReadable(e,f,{level:i,size:o})||!a?f:(r.includeFallbackColors=!1,d.mostReadable(e,["#fff","#000"],r))};var z=d.names={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"0ff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000",blanchedalmond:"ffebcd",blue:"00f",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",burntsienna:"ea7e5d",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"0ff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkgrey:"a9a9a9",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"f0f",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",grey:"808080",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgreen:"90ee90",lightgrey:"d3d3d3",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"789",lightslategrey:"789",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"0f0",limegreen:"32cd32",linen:"faf0e6",magenta:"f0f",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370db",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"db7093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"f00",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"fff",whitesmoke:"f5f5f5",yellow:"ff0",yellowgreen:"9acd32"},I=d.hexNames=B(z);function B(e){var t={};for(var r in e)e.hasOwnProperty(r)&&(t[e[r]]=r);return t}function R(e){return e=parseFloat(e),(isNaN(e)||e<0||e>1)&&(e=1),e}function L(e,t){H(e)&&(e="100%");var r=U(e);return e=s(t,c(0,parseFloat(e))),r&&(e=parseInt(e*t,10)/100),a.abs(e-t)<1e-6?1:e%t/parseFloat(t)}function V(e){return s(1,c(0,e))}function N(e){return parseInt(e,16)}function H(e){return"string"==typeof e&&-1!=e.indexOf(".")&&1===parseFloat(e)}function U(e){return"string"===typeof e&&-1!=e.indexOf("%")}function W(e){return 1==e.length?"0"+e:""+e}function q(e){return e<=1&&(e=100*e+"%"),e}function G(e){return a.round(255*parseFloat(e)).toString(16)}function $(e){return N(e)/255}var K=function(){var e="[-\\+]?\\d+%?",t="[-\\+]?\\d*\\.\\d+%?",r="(?:"+t+")|(?:"+e+")",n="[\\s|\\(]+("+r+")[,|\\s]+("+r+")[,|\\s]+("+r+")\\s*\\)?",a="[\\s|\\(]+("+r+")[,|\\s]+("+r+")[,|\\s]+("+r+")[,|\\s]+("+r+")\\s*\\)?";return{CSS_UNIT:new RegExp(r),rgb:new RegExp("rgb"+n),rgba:new RegExp("rgba"+a),hsl:new RegExp("hsl"+n),hsla:new RegExp("hsla"+a),hsv:new RegExp("hsv"+n),hsva:new RegExp("hsva"+a),hex3:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex6:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,hex4:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex8:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/}}();function Q(e){return!!K.CSS_UNIT.exec(e)}function Y(e){e=e.replace(i,"").replace(o,"").toLowerCase();var t,r=!1;if(z[e])e=z[e],r=!0;else if("transparent"==e)return{r:0,g:0,b:0,a:0,format:"name"};return(t=K.rgb.exec(e))?{r:t[1],g:t[2],b:t[3]}:(t=K.rgba.exec(e))?{r:t[1],g:t[2],b:t[3],a:t[4]}:(t=K.hsl.exec(e))?{h:t[1],s:t[2],l:t[3]}:(t=K.hsla.exec(e))?{h:t[1],s:t[2],l:t[3],a:t[4]}:(t=K.hsv.exec(e))?{h:t[1],s:t[2],v:t[3]}:(t=K.hsva.exec(e))?{h:t[1],s:t[2],v:t[3],a:t[4]}:(t=K.hex8.exec(e))?{r:N(t[1]),g:N(t[2]),b:N(t[3]),a:$(t[4]),format:r?"name":"hex8"}:(t=K.hex6.exec(e))?{r:N(t[1]),g:N(t[2]),b:N(t[3]),format:r?"name":"hex"}:(t=K.hex4.exec(e))?{r:N(t[1]+""+t[1]),g:N(t[2]+""+t[2]),b:N(t[3]+""+t[3]),a:$(t[4]+""+t[4]),format:r?"name":"hex8"}:!!(t=K.hex3.exec(e))&&{r:N(t[1]+""+t[1]),g:N(t[2]+""+t[2]),b:N(t[3]+""+t[3]),format:r?"name":"hex"}}function X(e){var t,r;return e=e||{level:"AA",size:"small"},t=(e.level||"AA").toUpperCase(),r=(e.size||"small").toLowerCase(),"AA"!==t&&"AAA"!==t&&(t="AA"),"small"!==r&&"large"!==r&&(r="small"),{level:t,size:r}}e.exports?e.exports=d:(n=function(){return d}.call(t,r,t,e),void 0===n||(e.exports=n))})(Math)},"7dbb":function(e,t,r){(function(t,r){e.exports=r()})(0,(function(){"use strict";var e=function(e){return e instanceof Uint8Array||e instanceof Uint16Array||e instanceof Uint32Array||e instanceof Int8Array||e instanceof Int16Array||e instanceof Int32Array||e instanceof Float32Array||e instanceof Float64Array||e instanceof Uint8ClampedArray},t=function(e,t){for(var r=Object.keys(t),n=0;n<r.length;++n)e[r[n]]=t[r[n]];return e},r="\n";function n(e){return"undefined"!==typeof atob?atob(e):"base64:"+e}function a(e){var t=new Error("(regl) "+e);throw console.error(t),t}function i(e,t){e||a(t)}function o(e){return e?": "+e:""}function f(e,t,r){e in t||a("unknown parameter ("+e+")"+o(r)+". possible values: "+Object.keys(t).join())}function u(t,r){e(t)||a("invalid parameter type"+o(r)+". must be a typed array")}function s(e,t){switch(t){case"number":return"number"===typeof e;case"object":return"object"===typeof e;case"string":return"string"===typeof e;case"boolean":return"boolean"===typeof e;case"function":return"function"===typeof e;case"undefined":return"undefined"===typeof e;case"symbol":return"symbol"===typeof e}}function c(e,t,r){s(e,t)||a("invalid parameter type"+o(r)+". expected "+t+", got "+typeof e)}function l(e,t){e>=0&&(0|e)===e||a("invalid parameter type, ("+e+")"+o(t)+". must be a nonnegative integer")}function d(e,t,r){t.indexOf(e)<0&&a("invalid value"+o(r)+". must be one of: "+t)}var h=["gl","canvas","container","attributes","pixelRatio","extensions","optionalExtensions","profile","onDone"];function p(e){Object.keys(e).forEach((function(e){h.indexOf(e)<0&&a('invalid regl constructor argument "'+e+'". must be one of '+h)}))}function m(e,t){e+="";while(e.length<t)e=" "+e;return e}function b(){this.name="unknown",this.lines=[],this.index={},this.hasErrors=!1}function g(e,t){this.number=e,this.line=t,this.errors=[]}function v(e,t,r){this.file=e,this.line=t,this.message=r}function y(){var e=new Error,t=(e.stack||e).toString(),r=/compileProcedure.*\n\s*at.*\((.*)\)/.exec(t);if(r)return r[1];var n=/compileProcedure.*\n\s*at\s+(.*)(\n|$)/.exec(t);return n?n[1]:"unknown"}function x(){var e=new Error,t=(e.stack||e).toString(),r=/at REGLCommand.*\n\s+at.*\((.*)\)/.exec(t);if(r)return r[1];var n=/at REGLCommand.*\n\s+at\s+(.*)\n/.exec(t);return n?n[1]:"unknown"}function w(e,t){var r=e.split("\n"),a=1,i=0,o={unknown:new b,0:new b};o.unknown.name=o[0].name=t||y(),o.unknown.lines.push(new g(0,""));for(var f=0;f<r.length;++f){var u=r[f],s=/^\s*#\s*(\w+)\s+(.+)\s*$/.exec(u);if(s)switch(s[1]){case"line":var c=/(\d+)(\s+\d+)?/.exec(s[2]);c&&(a=0|c[1],c[2]&&(i=0|c[2],i in o||(o[i]=new b)));break;case"define":var l=/SHADER_NAME(_B64)?\s+(.*)$/.exec(s[2]);l&&(o[i].name=l[1]?n(l[2]):l[2]);break}o[i].lines.push(new g(a++,u))}return Object.keys(o).forEach((function(e){var t=o[e];t.lines.forEach((function(e){t.index[e.number]=e}))})),o}function _(e){var t=[];return e.split("\n").forEach((function(e){if(!(e.length<5)){var r=/^ERROR:\s+(\d+):(\d+):\s*(.*)$/.exec(e);r?t.push(new v(0|r[1],0|r[2],r[3].trim())):e.length>0&&t.push(new v("unknown",0,e))}})),t}function A(e,t){t.forEach((function(t){var r=e[t.file];if(r){var n=r.index[t.line];if(n)return n.errors.push(t),void(r.hasErrors=!0)}e.unknown.hasErrors=!0,e.unknown.lines[0].errors.push(t)}))}function k(e,t,n,a,o){if(!e.getShaderParameter(t,e.COMPILE_STATUS)){var f=e.getShaderInfoLog(t),u=a===e.FRAGMENT_SHADER?"fragment":"vertex";D(n,"string",u+" shader source must be a string",o);var s=w(n,o),c=_(f);A(s,c),Object.keys(s).forEach((function(e){var t=s[e];if(t.hasErrors){var n=[""],a=[""];i("file number "+e+": "+t.name+"\n","color:red;text-decoration:underline;font-weight:bold"),t.lines.forEach((function(e){if(e.errors.length>0){i(m(e.number,4)+"|  ","background-color:yellow; font-weight:bold"),i(e.line+r,"color:red; background-color:yellow; font-weight:bold");var t=0;e.errors.forEach((function(n){var a=n.message,o=/^\s*'(.*)'\s*:\s*(.*)$/.exec(a);if(o){var f=o[1];switch(a=o[2],f){case"assign":f="=";break}t=Math.max(e.line.indexOf(f,t),0)}else t=0;i(m("| ",6)),i(m("^^^",t+3)+r,"font-weight:bold"),i(m("| ",6)),i(a+r,"font-weight:bold")})),i(m("| ",6)+r)}else i(m(e.number,4)+"|  "),i(e.line+r,"color:red")})),"undefined"===typeof document||window.chrome?console.log(n.join("")):(a[0]=n.join("%c"),console.log.apply(console,a))}function i(e,t){n.push(e),a.push(t||"")}})),i.raise("Error compiling "+u+" shader, "+s[0].name)}}function S(e,t,n,a,o){if(!e.getProgramParameter(t,e.LINK_STATUS)){var f=e.getProgramInfoLog(t),u=w(n,o),s=w(a,o),c='Error linking program with vertex shader, "'+s[0].name+'", and fragment shader "'+u[0].name+'"';"undefined"!==typeof document?console.log("%c"+c+r+"%c"+f,"color:red;text-decoration:underline;font-weight:bold","color:red"):console.log(c+r+f),i.raise(c)}}function E(e){e._commandRef=y()}function O(e,t,r,n){function a(e){return e?n.id(e):0}function i(e,t){Object.keys(t).forEach((function(t){e[n.id(t)]=!0}))}E(e),e._fragId=a(e.static.frag),e._vertId=a(e.static.vert);var o=e._uniformSet={};i(o,t.static),i(o,t.dynamic);var f=e._attributeSet={};i(f,r.static),i(f,r.dynamic),e._hasCount="count"in e.static||"count"in e.dynamic||"elements"in e.static||"elements"in e.dynamic}function T(e,t){var r=x();a(e+" in command "+(t||y())+("unknown"===r?"":" called from "+r))}function C(e,t,r){e||T(t,r||y())}function j(e,t,r,n){e in t||T("unknown parameter ("+e+")"+o(r)+". possible values: "+Object.keys(t).join(),n||y())}function D(e,t,r,n){s(e,t)||T("invalid parameter type"+o(r)+". expected "+t+", got "+typeof e,n||y())}function F(e){e()}function M(e,t,r){e.texture?d(e.texture._texture.internalformat,t,"unsupported texture format for attachment"):d(e.renderbuffer._renderbuffer.format,r,"unsupported renderbuffer format for attachment")}var P=33071,z=9728,I=9984,B=9985,R=9986,L=9987,V=5120,N=5121,H=5122,U=5123,W=5124,q=5125,G=5126,$=32819,K=32820,Q=33635,Y=34042,X=36193,J={};function Z(e,t){return e===K||e===$||e===Q?2:e===Y?4:J[e]*t}function ee(e){return!(e&e-1)&&!!e}function te(e,t,r){var n,a=t.width,o=t.height,f=t.channels;i(a>0&&a<=r.maxTextureSize&&o>0&&o<=r.maxTextureSize,"invalid texture shape"),e.wrapS===P&&e.wrapT===P||i(ee(a)&&ee(o),"incompatible wrap mode for texture, both width and height must be power of 2"),1===t.mipmask?1!==a&&1!==o&&i(e.minFilter!==I&&e.minFilter!==R&&e.minFilter!==B&&e.minFilter!==L,"min filter requires mipmap"):(i(ee(a)&&ee(o),"texture must be a square power of 2 to support mipmapping"),i(t.mipmask===(a<<1)-1,"missing or incomplete mipmap data")),t.type===G&&(r.extensions.indexOf("oes_texture_float_linear")<0&&i(e.minFilter===z&&e.magFilter===z,"filter not supported, must enable oes_texture_float_linear"),i(!e.genMipmaps,"mipmap generation not supported with float textures"));var u=t.images;for(n=0;n<16;++n)if(u[n]){var s=a>>n,c=o>>n;i(t.mipmask&1<<n,"missing mipmap data");var l=u[n];if(i(l.width===s&&l.height===c,"invalid shape for mip images"),i(l.format===t.format&&l.internalformat===t.internalformat&&l.type===t.type,"incompatible type for mip image"),l.compressed);else if(l.data){var d=Math.ceil(Z(l.type,f)*s/l.unpackAlignment)*l.unpackAlignment;i(l.data.byteLength===d*c,"invalid data for image, buffer size is inconsistent with image format")}else l.element||l.copy}else e.genMipmaps||i(0===(t.mipmask&1<<n),"extra mipmap data");t.compressed&&i(!e.genMipmaps,"mipmap generation for compressed images not supported")}function re(e,t,r,n){var a=e.width,o=e.height,f=e.channels;i(a>0&&a<=n.maxTextureSize&&o>0&&o<=n.maxTextureSize,"invalid texture shape"),i(a===o,"cube map must be square"),i(t.wrapS===P&&t.wrapT===P,"wrap mode not supported by cube map");for(var u=0;u<r.length;++u){var s=r[u];i(s.width===a&&s.height===o,"inconsistent cube map face shape"),t.genMipmaps&&(i(!s.compressed,"can not generate mipmap for compressed textures"),i(1===s.mipmask,"can not specify mipmaps and generate mipmaps"));for(var c=s.images,l=0;l<16;++l){var d=c[l];if(d){var h=a>>l,p=o>>l;i(s.mipmask&1<<l,"missing mipmap data"),i(d.width===h&&d.height===p,"invalid shape for mip images"),i(d.format===e.format&&d.internalformat===e.internalformat&&d.type===e.type,"incompatible type for mip image"),d.compressed||(d.data?i(d.data.byteLength===h*p*Math.max(Z(d.type,f),d.unpackAlignment),"invalid data for image, buffer size is inconsistent with image format"):d.element||d.copy)}}}}J[V]=J[N]=1,J[H]=J[U]=J[X]=J[Q]=J[$]=J[K]=2,J[W]=J[q]=J[G]=J[Y]=4;var ne=t(i,{optional:F,raise:a,commandRaise:T,command:C,parameter:f,commandParameter:j,constructor:p,type:c,commandType:D,isTypedArray:u,nni:l,oneOf:d,shaderError:k,linkError:S,callSite:x,saveCommandRef:E,saveDrawInfo:O,framebufferFormat:M,guessCommand:y,texture2D:te,textureCube:re}),ae=0,ie=0,oe=5,fe=6;function ue(e,t){this.id=ae++,this.type=e,this.data=t}function se(e){return e.replace(/\\/g,"\\\\").replace(/"/g,'\\"')}function ce(e){if(0===e.length)return[];var t=e.charAt(0),r=e.charAt(e.length-1);if(e.length>1&&t===r&&('"'===t||"'"===t))return['"'+se(e.substr(1,e.length-2))+'"'];var n=/\[(false|true|null|\d+|'[^']*'|"[^"]*")\]/.exec(e);if(n)return ce(e.substr(0,n.index)).concat(ce(n[1])).concat(ce(e.substr(n.index+n[0].length)));var a=e.split(".");if(1===a.length)return['"'+se(e)+'"'];for(var i=[],o=0;o<a.length;++o)i=i.concat(ce(a[o]));return i}function le(e){return"["+ce(e).join("][")+"]"}function de(e,t){return new ue(e,le(t+""))}function he(e){return"function"===typeof e&&!e._reglType||e instanceof ue}function pe(e,t){return"function"===typeof e?new ue(ie,e):"number"===typeof e||"boolean"===typeof e?new ue(oe,e):Array.isArray(e)?new ue(fe,e.map((e,r)=>pe(e,t+"["+r+"]"))):e instanceof ue?e:void ne(!1,"invalid option type in uniform "+t)}var me={DynamicVariable:ue,define:de,isDynamic:he,unbox:pe,accessor:le},be={next:"function"===typeof requestAnimationFrame?function(e){return requestAnimationFrame(e)}:function(e){return setTimeout(e,16)},cancel:"function"===typeof cancelAnimationFrame?function(e){return cancelAnimationFrame(e)}:clearTimeout},ge="undefined"!==typeof performance&&performance.now?function(){return performance.now()}:function(){return+new Date};function ve(){var e={"":0},t=[""];return{id:function(r){var n=e[r];return n||(n=e[r]=t.length,t.push(r),n)},str:function(e){return t[e]}}}function ye(e,r,n){var a,i=document.createElement("canvas");function o(){var r=window.innerWidth,a=window.innerHeight;if(e!==document.body){var o=e.getBoundingClientRect();r=o.right-o.left,a=o.bottom-o.top}i.width=n*r,i.height=n*a,t(i.style,{width:r+"px",height:a+"px"})}function f(){a?a.disconnect():window.removeEventListener("resize",o),e.removeChild(i)}return t(i.style,{border:0,margin:0,padding:0,top:0,left:0}),e.appendChild(i),e===document.body&&(i.style.position="absolute",t(e.style,{margin:0,padding:0})),e!==document.body&&"function"===typeof ResizeObserver?(a=new ResizeObserver((function(){setTimeout(o)})),a.observe(e)):window.addEventListener("resize",o,!1),o(),{canvas:i,onDestroy:f}}function xe(e,t){function r(r){try{return e.getContext(r,t)}catch(n){return null}}return r("webgl")||r("experimental-webgl")||r("webgl-experimental")}function we(e){return"string"===typeof e.nodeName&&"function"===typeof e.appendChild&&"function"===typeof e.getBoundingClientRect}function _e(e){return"function"===typeof e.drawArrays||"function"===typeof e.drawElements}function Ae(e){return"string"===typeof e?e.split():(ne(Array.isArray(e),"invalid extension array"),e)}function ke(e){return"string"===typeof e?(ne("undefined"!==typeof document,"not supported outside of DOM"),document.querySelector(e)):e}function Se(e){var t,r,n,a,i=e||{},o={},f=[],u=[],s="undefined"===typeof window?1:window.devicePixelRatio,c=!1,l=function(e){e&&ne.raise(e)},d=function(){};if("string"===typeof i?(ne("undefined"!==typeof document,"selector queries only supported in DOM enviroments"),t=document.querySelector(i),ne(t,"invalid query string for element")):"object"===typeof i?we(i)?t=i:_e(i)?(a=i,n=a.canvas):(ne.constructor(i),"gl"in i?a=i.gl:"canvas"in i?n=ke(i.canvas):"container"in i&&(r=ke(i.container)),"attributes"in i&&(o=i.attributes,ne.type(o,"object","invalid context attributes")),"extensions"in i&&(f=Ae(i.extensions)),"optionalExtensions"in i&&(u=Ae(i.optionalExtensions)),"onDone"in i&&(ne.type(i.onDone,"function","invalid or missing onDone callback"),l=i.onDone),"profile"in i&&(c=!!i.profile),"pixelRatio"in i&&(s=+i.pixelRatio,ne(s>0,"invalid pixel ratio"))):ne.raise("invalid arguments to regl"),t&&("canvas"===t.nodeName.toLowerCase()?n=t:r=t),!a){if(!n){ne("undefined"!==typeof document,"must manually specify webgl context outside of DOM environments");var h=ye(r||document.body,l,s);if(!h)return null;n=h.canvas,d=h.onDestroy}void 0===o.premultipliedAlpha&&(o.premultipliedAlpha=!0),a=xe(n,o)}return a?{gl:a,canvas:n,container:r,extensions:f,optionalExtensions:u,pixelRatio:s,profile:c,onDone:l,onDestroy:d}:(d(),l("webgl not supported, try upgrading your browser or graphics drivers http://get.webgl.org"),null)}function Ee(e,t){var r={};function n(t){ne.type(t,"string","extension name must be string");var n,a=t.toLowerCase();try{n=r[a]=e.getExtension(a)}catch(i){}return!!n}for(var a=0;a<t.extensions.length;++a){var i=t.extensions[a];if(!n(i))return t.onDestroy(),t.onDone('"'+i+'" extension is not supported by the current WebGL context, try upgrading your system or a different browser'),null}return t.optionalExtensions.forEach(n),{extensions:r,restore:function(){Object.keys(r).forEach((function(e){if(r[e]&&!n(e))throw new Error("(regl): error restoring extension "+e)}))}}}function Oe(e,t){for(var r=Array(e),n=0;n<e;++n)r[n]=t(n);return r}var Te=5120,Ce=5121,je=5122,De=5123,Fe=5124,Me=5125,Pe=5126;function ze(e){for(var t=16;t<=1<<28;t*=16)if(e<=t)return t;return 0}function Ie(e){var t,r;return t=(e>65535)<<4,e>>>=t,r=(e>255)<<3,e>>>=r,t|=r,r=(e>15)<<2,e>>>=r,t|=r,r=(e>3)<<1,e>>>=r,t|=r,t|e>>1}function Be(){var e=Oe(8,(function(){return[]}));function t(t){var r=ze(t),n=e[Ie(r)>>2];return n.length>0?n.pop():new ArrayBuffer(r)}function r(t){e[Ie(t.byteLength)>>2].push(t)}function n(e,r){var n=null;switch(e){case Te:n=new Int8Array(t(r),0,r);break;case Ce:n=new Uint8Array(t(r),0,r);break;case je:n=new Int16Array(t(2*r),0,r);break;case De:n=new Uint16Array(t(2*r),0,r);break;case Fe:n=new Int32Array(t(4*r),0,r);break;case Me:n=new Uint32Array(t(4*r),0,r);break;case Pe:n=new Float32Array(t(4*r),0,r);break;default:return null}return n.length!==r?n.subarray(0,r):n}function a(e){r(e.buffer)}return{alloc:t,free:r,allocType:n,freeType:a}}var Re=Be();Re.zero=Be();var Le=3408,Ve=3410,Ne=3411,He=3412,Ue=3413,We=3414,qe=3415,Ge=33901,$e=33902,Ke=3379,Qe=3386,Ye=34921,Xe=36347,Je=36348,Ze=35661,et=35660,tt=34930,rt=36349,nt=34076,at=34024,it=7936,ot=7937,ft=7938,ut=35724,st=34047,ct=36063,lt=34852,dt=3553,ht=34067,pt=34069,mt=33984,bt=6408,gt=5126,vt=5121,yt=36160,xt=36053,wt=36064,_t=16384,At=function(e,t){var r=1;t.ext_texture_filter_anisotropic&&(r=e.getParameter(st));var n=1,a=1;t.webgl_draw_buffers&&(n=e.getParameter(lt),a=e.getParameter(ct));var i=!!t.oes_texture_float;if(i){var o=e.createTexture();e.bindTexture(dt,o),e.texImage2D(dt,0,bt,1,1,0,bt,gt,null);var f=e.createFramebuffer();if(e.bindFramebuffer(yt,f),e.framebufferTexture2D(yt,wt,dt,o,0),e.bindTexture(dt,null),e.checkFramebufferStatus(yt)!==xt)i=!1;else{e.viewport(0,0,1,1),e.clearColor(1,0,0,1),e.clear(_t);var u=Re.allocType(gt,4);e.readPixels(0,0,1,1,bt,gt,u),e.getError()?i=!1:(e.deleteFramebuffer(f),e.deleteTexture(o),i=1===u[0]),Re.freeType(u)}}var s="undefined"!==typeof navigator&&(/MSIE/.test(navigator.userAgent)||/Trident\//.test(navigator.appVersion)||/Edge/.test(navigator.userAgent)),c=!0;if(!s){var l=e.createTexture(),d=Re.allocType(vt,36);e.activeTexture(mt),e.bindTexture(ht,l),e.texImage2D(pt,0,bt,3,3,0,bt,vt,d),Re.freeType(d),e.bindTexture(ht,null),e.deleteTexture(l),c=!e.getError()}return{colorBits:[e.getParameter(Ve),e.getParameter(Ne),e.getParameter(He),e.getParameter(Ue)],depthBits:e.getParameter(We),stencilBits:e.getParameter(qe),subpixelBits:e.getParameter(Le),extensions:Object.keys(t).filter((function(e){return!!t[e]})),maxAnisotropic:r,maxDrawbuffers:n,maxColorAttachments:a,pointSizeDims:e.getParameter(Ge),lineWidthDims:e.getParameter($e),maxViewportDims:e.getParameter(Qe),maxCombinedTextureUnits:e.getParameter(Ze),maxCubeMapSize:e.getParameter(nt),maxRenderbufferSize:e.getParameter(at),maxTextureUnits:e.getParameter(tt),maxTextureSize:e.getParameter(Ke),maxAttributes:e.getParameter(Ye),maxVertexUniforms:e.getParameter(Xe),maxVertexTextureUnits:e.getParameter(et),maxVaryingVectors:e.getParameter(Je),maxFragmentUniforms:e.getParameter(rt),glsl:e.getParameter(ut),renderer:e.getParameter(ot),vendor:e.getParameter(it),version:e.getParameter(ft),readFloat:i,npotTextureCube:c}};function kt(t){return!!t&&"object"===typeof t&&Array.isArray(t.shape)&&Array.isArray(t.stride)&&"number"===typeof t.offset&&t.shape.length===t.stride.length&&(Array.isArray(t.data)||e(t.data))}var St=function(e){return Object.keys(e).map((function(t){return e[t]}))},Et={shape:Ft,flatten:Dt};function Ot(e,t,r){for(var n=0;n<t;++n)r[n]=e[n]}function Tt(e,t,r,n){for(var a=0,i=0;i<t;++i)for(var o=e[i],f=0;f<r;++f)n[a++]=o[f]}function Ct(e,t,r,n,a,i){for(var o=i,f=0;f<t;++f)for(var u=e[f],s=0;s<r;++s)for(var c=u[s],l=0;l<n;++l)a[o++]=c[l]}function jt(e,t,r,n,a){for(var i=1,o=r+1;o<t.length;++o)i*=t[o];var f=t[r];if(t.length-r===4){var u=t[r+1],s=t[r+2],c=t[r+3];for(o=0;o<f;++o)Ct(e[o],u,s,c,n,a),a+=i}else for(o=0;o<f;++o)jt(e[o],t,r+1,n,a),a+=i}function Dt(e,t,r,n){var a=1;if(t.length)for(var i=0;i<t.length;++i)a*=t[i];else a=0;var o=n||Re.allocType(r,a);switch(t.length){case 0:break;case 1:Ot(e,t[0],o);break;case 2:Tt(e,t[0],t[1],o);break;case 3:Ct(e,t[0],t[1],t[2],o,0);break;default:jt(e,t,0,o,0)}return o}function Ft(e){for(var t=[],r=e;r.length;r=r[0])t.push(r.length);return t}var Mt={"[object Int8Array]":5120,"[object Int16Array]":5122,"[object Int32Array]":5124,"[object Uint8Array]":5121,"[object Uint8ClampedArray]":5121,"[object Uint16Array]":5123,"[object Uint32Array]":5125,"[object Float32Array]":5126,"[object Float64Array]":5121,"[object ArrayBuffer]":5121},Pt=5120,zt=5122,It=5124,Bt=5121,Rt=5123,Lt=5125,Vt=5126,Nt=5126,Ht={int8:Pt,int16:zt,int32:It,uint8:Bt,uint16:Rt,uint32:Lt,float:Vt,float32:Nt},Ut=35048,Wt=35040,qt={dynamic:Ut,stream:Wt,static:35044},Gt=Et.flatten,$t=Et.shape,Kt=35044,Qt=35040,Yt=5121,Xt=5126,Jt=[];function Zt(e){return 0|Mt[Object.prototype.toString.call(e)]}function er(e,t){for(var r=0;r<t.length;++r)e[r]=t[r]}function tr(e,t,r,n,a,i,o){for(var f=0,u=0;u<r;++u)for(var s=0;s<n;++s)e[f++]=t[a*u+i*s+o]}function rr(t,r,n,a){var i=0,o={};function f(e){this.id=i++,this.buffer=t.createBuffer(),this.type=e,this.usage=Kt,this.byteLength=0,this.dimension=1,this.dtype=Yt,this.persistentData=null,n.profile&&(this.stats={size:0})}f.prototype.bind=function(){t.bindBuffer(this.type,this.buffer)},f.prototype.destroy=function(){h(this)};var u=[];function s(e,t){var r=u.pop();return r||(r=new f(e)),r.bind(),d(r,t,Qt,0,1,!1),r}function c(e){u.push(e)}function l(e,r,n){e.byteLength=r.byteLength,t.bufferData(e.type,r,n)}function d(t,r,n,a,i,o){var f,u;if(t.usage=n,Array.isArray(r)){if(t.dtype=a||Xt,r.length>0)if(Array.isArray(r[0])){f=$t(r);for(var s=1,c=1;c<f.length;++c)s*=f[c];t.dimension=s,u=Gt(r,f,t.dtype),l(t,u,n),o?t.persistentData=u:Re.freeType(u)}else if("number"===typeof r[0]){t.dimension=i;var d=Re.allocType(t.dtype,r.length);er(d,r),l(t,d,n),o?t.persistentData=d:Re.freeType(d)}else e(r[0])?(t.dimension=r[0].length,t.dtype=a||Zt(r[0])||Xt,u=Gt(r,[r.length,r[0].length],t.dtype),l(t,u,n),o?t.persistentData=u:Re.freeType(u)):ne.raise("invalid buffer data")}else if(e(r))t.dtype=a||Zt(r),t.dimension=i,l(t,r,n),o&&(t.persistentData=new Uint8Array(new Uint8Array(r.buffer)));else if(kt(r)){f=r.shape;var h=r.stride,p=r.offset,m=0,b=0,g=0,v=0;1===f.length?(m=f[0],b=1,g=h[0],v=0):2===f.length?(m=f[0],b=f[1],g=h[0],v=h[1]):ne.raise("invalid shape"),t.dtype=a||Zt(r.data)||Xt,t.dimension=b;var y=Re.allocType(t.dtype,m*b);tr(y,r.data,m,b,g,v,p),l(t,y,n),o?t.persistentData=y:Re.freeType(y)}else r instanceof ArrayBuffer?(t.dtype=Yt,t.dimension=i,l(t,r,n),o&&(t.persistentData=new Uint8Array(new Uint8Array(r)))):ne.raise("invalid buffer data")}function h(e){r.bufferCount--,a(e);var n=e.buffer;ne(n,"buffer must not be deleted already"),t.deleteBuffer(n),e.buffer=null,delete o[e.id]}function p(a,i,u,s){r.bufferCount++;var c=new f(i);function l(r){var a=Kt,i=null,o=0,f=0,u=1;return Array.isArray(r)||e(r)||kt(r)||r instanceof ArrayBuffer?i=r:"number"===typeof r?o=0|r:r&&(ne.type(r,"object","buffer arguments must be an object, a number or an array"),"data"in r&&(ne(null===i||Array.isArray(i)||e(i)||kt(i),"invalid data for buffer"),i=r.data),"usage"in r&&(ne.parameter(r.usage,qt,"invalid buffer usage"),a=qt[r.usage]),"type"in r&&(ne.parameter(r.type,Ht,"invalid buffer type"),f=Ht[r.type]),"dimension"in r&&(ne.type(r.dimension,"number","invalid dimension"),u=0|r.dimension),"length"in r&&(ne.nni(o,"buffer length must be a nonnegative integer"),o=0|r.length)),c.bind(),i?d(c,i,a,f,u,s):(o&&t.bufferData(c.type,o,a),c.dtype=f||Yt,c.usage=a,c.dimension=u,c.byteLength=o),n.profile&&(c.stats.size=c.byteLength*Jt[c.dtype]),l}function p(e,r){ne(r+e.byteLength<=c.byteLength,"invalid buffer subdata call, buffer is too small.  Can't write data of size "+e.byteLength+" starting from offset "+r+" to a buffer of size "+c.byteLength),t.bufferSubData(c.type,r,e)}function m(t,r){var n,a=0|(r||0);if(c.bind(),e(t)||t instanceof ArrayBuffer)p(t,a);else if(Array.isArray(t)){if(t.length>0)if("number"===typeof t[0]){var i=Re.allocType(c.dtype,t.length);er(i,t),p(i,a),Re.freeType(i)}else if(Array.isArray(t[0])||e(t[0])){n=$t(t);var o=Gt(t,n,c.dtype);p(o,a),Re.freeType(o)}else ne.raise("invalid buffer data")}else if(kt(t)){n=t.shape;var f=t.stride,u=0,s=0,d=0,h=0;1===n.length?(u=n[0],s=1,d=f[0],h=0):2===n.length?(u=n[0],s=n[1],d=f[0],h=f[1]):ne.raise("invalid shape");var m=Array.isArray(t.data)?c.dtype:Zt(t.data),b=Re.allocType(m,u*s);tr(b,t.data,u,s,d,h,t.offset),p(b,a),Re.freeType(b)}else ne.raise("invalid data for buffer subdata");return l}return o[c.id]=c,u||l(a),l._reglType="buffer",l._buffer=c,l.subdata=m,n.profile&&(l.stats=c.stats),l.destroy=function(){h(c)},l}function m(){St(o).forEach((function(e){e.buffer=t.createBuffer(),t.bindBuffer(e.type,e.buffer),t.bufferData(e.type,e.persistentData||e.byteLength,e.usage)}))}return n.profile&&(r.getTotalBufferSize=function(){var e=0;return Object.keys(o).forEach((function(t){e+=o[t].stats.size})),e}),{create:p,createStream:s,destroyStream:c,clear:function(){St(o).forEach(h),u.forEach(h)},getBuffer:function(e){return e&&e._buffer instanceof f?e._buffer:null},restore:m,_initBuffer:d}}Jt[5120]=1,Jt[5122]=2,Jt[5124]=4,Jt[5121]=1,Jt[5123]=2,Jt[5125]=4,Jt[5126]=4;var nr=0,ar=0,ir=1,or=1,fr=4,ur=4,sr={points:nr,point:ar,lines:ir,line:or,triangles:fr,triangle:ur,"line loop":2,"line strip":3,"triangle strip":5,"triangle fan":6},cr=0,lr=1,dr=4,hr=5120,pr=5121,mr=5122,br=5123,gr=5124,vr=5125,yr=34963,xr=35040,wr=35044;function _r(t,r,n,a){var i={},o=0,f={uint8:pr,uint16:br};function u(e){this.id=o++,i[this.id]=this,this.buffer=e,this.primType=dr,this.vertCount=0,this.type=0}r.oes_element_index_uint&&(f.uint32=vr),u.prototype.bind=function(){this.buffer.bind()};var s=[];function c(e){var t=s.pop();return t||(t=new u(n.create(null,yr,!0,!1)._buffer)),d(t,e,xr,-1,-1,0,0),t}function l(e){s.push(e)}function d(a,i,o,f,u,s,c){var l;if(a.buffer.bind(),i){var d=c;c||e(i)&&(!kt(i)||e(i.data))||(d=r.oes_element_index_uint?vr:br),n._initBuffer(a.buffer,i,o,d,3)}else t.bufferData(yr,s,o),a.buffer.dtype=l||pr,a.buffer.usage=o,a.buffer.dimension=3,a.buffer.byteLength=s;if(l=c,!c){switch(a.buffer.dtype){case pr:case hr:l=pr;break;case br:case mr:l=br;break;case vr:case gr:l=vr;break;default:ne.raise("unsupported type for element array")}a.buffer.dtype=l}a.type=l,ne(l!==vr||!!r.oes_element_index_uint,"32 bit element buffers not supported, enable oes_element_index_uint first");var h=u;h<0&&(h=a.buffer.byteLength,l===br?h>>=1:l===vr&&(h>>=2)),a.vertCount=h;var p=f;if(f<0){p=dr;var m=a.buffer.dimension;1===m&&(p=cr),2===m&&(p=lr),3===m&&(p=dr)}a.primType=p}function h(e){a.elementsCount--,ne(null!==e.buffer,"must not double destroy elements"),delete i[e.id],e.buffer.destroy(),e.buffer=null}function p(t,r){var i=n.create(null,yr,!0),o=new u(i._buffer);function s(t){if(t)if("number"===typeof t)i(t),o.primType=dr,o.vertCount=0|t,o.type=pr;else{var r=null,n=wr,a=-1,u=-1,c=0,l=0;Array.isArray(t)||e(t)||kt(t)?r=t:(ne.type(t,"object","invalid arguments for elements"),"data"in t&&(r=t.data,ne(Array.isArray(r)||e(r)||kt(r),"invalid data for element buffer")),"usage"in t&&(ne.parameter(t.usage,qt,"invalid element buffer usage"),n=qt[t.usage]),"primitive"in t&&(ne.parameter(t.primitive,sr,"invalid element buffer primitive"),a=sr[t.primitive]),"count"in t&&(ne("number"===typeof t.count&&t.count>=0,"invalid vertex count for elements"),u=0|t.count),"type"in t&&(ne.parameter(t.type,f,"invalid buffer type"),l=f[t.type]),"length"in t?c=0|t.length:(c=u,l===br||l===mr?c*=2:l!==vr&&l!==gr||(c*=4))),d(o,r,n,a,u,c,l)}else i(),o.primType=dr,o.vertCount=0,o.type=pr;return s}return a.elementsCount++,s(t),s._reglType="elements",s._elements=o,s.subdata=function(e,t){return i.subdata(e,t),s},s.destroy=function(){h(o)},s}return{create:p,createStream:c,destroyStream:l,getElements:function(e){return"function"===typeof e&&e._elements instanceof u?e._elements:null},clear:function(){St(i).forEach(h)}}}var Ar=new Float32Array(1),kr=new Uint32Array(Ar.buffer),Sr=5123;function Er(e){for(var t=Re.allocType(Sr,e.length),r=0;r<e.length;++r)if(isNaN(e[r]))t[r]=65535;else if(e[r]===1/0)t[r]=31744;else if(e[r]===-1/0)t[r]=64512;else{Ar[0]=e[r];var n=kr[0],a=n>>>31<<15,i=(n<<1>>>24)-127,o=n>>13&1023;if(i<-24)t[r]=a;else if(i<-14){var f=-14-i;t[r]=a+(o+1024>>f)}else t[r]=i>15?a+31744:a+(i+15<<10)+o}return t}function Or(t){return Array.isArray(t)||e(t)}var Tr=function(e){return!(e&e-1)&&!!e},Cr=34467,jr=3553,Dr=34067,Fr=34069,Mr=6408,Pr=6406,zr=6407,Ir=6409,Br=6410,Rr=32854,Lr=32855,Vr=36194,Nr=32819,Hr=32820,Ur=33635,Wr=34042,qr=6402,Gr=34041,$r=35904,Kr=35906,Qr=36193,Yr=33776,Xr=33777,Jr=33778,Zr=33779,en=35986,tn=35987,rn=34798,nn=35840,an=35841,on=35842,fn=35843,un=36196,sn=5121,cn=5123,ln=5125,dn=5126,hn=10242,pn=10243,mn=10497,bn=33071,gn=33648,vn=10240,yn=10241,xn=9728,wn=9729,_n=9984,An=9985,kn=9986,Sn=9987,En=33170,On=4352,Tn=4353,Cn=4354,jn=34046,Dn=3317,Fn=37440,Mn=37441,Pn=37443,zn=37444,In=33984,Bn=[_n,kn,An,Sn],Rn=[0,Ir,Br,zr,Mr],Ln={};function Vn(e){return"[object "+e+"]"}Ln[Ir]=Ln[Pr]=Ln[qr]=1,Ln[Gr]=Ln[Br]=2,Ln[zr]=Ln[$r]=3,Ln[Mr]=Ln[Kr]=4;var Nn=Vn("HTMLCanvasElement"),Hn=Vn("OffscreenCanvas"),Un=Vn("CanvasRenderingContext2D"),Wn=Vn("ImageBitmap"),qn=Vn("HTMLImageElement"),Gn=Vn("HTMLVideoElement"),$n=Object.keys(Mt).concat([Nn,Hn,Un,Wn,qn,Gn]),Kn=[];Kn[sn]=1,Kn[dn]=4,Kn[Qr]=2,Kn[cn]=2,Kn[ln]=4;var Qn=[];function Yn(e){return Array.isArray(e)&&(0===e.length||"number"===typeof e[0])}function Xn(e){if(!Array.isArray(e))return!1;var t=e.length;return!(0===t||!Or(e[0]))}function Jn(e){return Object.prototype.toString.call(e)}function Zn(e){return Jn(e)===Nn}function ea(e){return Jn(e)===Hn}function ta(e){return Jn(e)===Un}function ra(e){return Jn(e)===Wn}function na(e){return Jn(e)===qn}function aa(e){return Jn(e)===Gn}function ia(e){if(!e)return!1;var t=Jn(e);return $n.indexOf(t)>=0||(Yn(e)||Xn(e)||kt(e))}function oa(e){return 0|Mt[Object.prototype.toString.call(e)]}function fa(e,t){var r=t.length;switch(e.type){case sn:case cn:case ln:case dn:var n=Re.allocType(e.type,r);n.set(t),e.data=n;break;case Qr:e.data=Er(t);break;default:ne.raise("unsupported texture type, must specify a typed array")}}function ua(e,t){return Re.allocType(e.type===Qr?dn:e.type,t)}function sa(e,t){e.type===Qr?(e.data=Er(t),Re.freeType(t)):e.data=t}function ca(e,t,r,n,a,i){for(var o=e.width,f=e.height,u=e.channels,s=o*f*u,c=ua(e,s),l=0,d=0;d<f;++d)for(var h=0;h<o;++h)for(var p=0;p<u;++p)c[l++]=t[r*h+n*d+a*p+i];sa(e,c)}function la(e,t,r,n,a,i){var o;if(o="undefined"!==typeof Qn[e]?Qn[e]:Ln[e]*Kn[t],i&&(o*=6),a){var f=0,u=r;while(u>=1)f+=o*u*u,u/=2;return f}return o*r*n}function da(r,n,a,i,o,f,u){var s={"don't care":On,"dont care":On,nice:Cn,fast:Tn},c={repeat:mn,clamp:bn,mirror:gn},l={nearest:xn,linear:wn},d=t({mipmap:Sn,"nearest mipmap nearest":_n,"linear mipmap nearest":An,"nearest mipmap linear":kn,"linear mipmap linear":Sn},l),h={none:0,browser:zn},p={uint8:sn,rgba4:Nr,rgb565:Ur,"rgb5 a1":Hr},m={alpha:Pr,luminance:Ir,"luminance alpha":Br,rgb:zr,rgba:Mr,rgba4:Rr,"rgb5 a1":Lr,rgb565:Vr},b={};n.ext_srgb&&(m.srgb=$r,m.srgba=Kr),n.oes_texture_float&&(p.float32=p.float=dn),n.oes_texture_half_float&&(p["float16"]=p["half float"]=Qr),n.webgl_depth_texture&&(t(m,{depth:qr,"depth stencil":Gr}),t(p,{uint16:cn,uint32:ln,"depth stencil":Wr})),n.webgl_compressed_texture_s3tc&&t(b,{"rgb s3tc dxt1":Yr,"rgba s3tc dxt1":Xr,"rgba s3tc dxt3":Jr,"rgba s3tc dxt5":Zr}),n.webgl_compressed_texture_atc&&t(b,{"rgb atc":en,"rgba atc explicit alpha":tn,"rgba atc interpolated alpha":rn}),n.webgl_compressed_texture_pvrtc&&t(b,{"rgb pvrtc 4bppv1":nn,"rgb pvrtc 2bppv1":an,"rgba pvrtc 4bppv1":on,"rgba pvrtc 2bppv1":fn}),n.webgl_compressed_texture_etc1&&(b["rgb etc1"]=un);var g=Array.prototype.slice.call(r.getParameter(Cr));Object.keys(b).forEach((function(e){var t=b[e];g.indexOf(t)>=0&&(m[e]=t)}));var v=Object.keys(m);a.textureFormats=v;var y=[];Object.keys(m).forEach((function(e){var t=m[e];y[t]=e}));var x=[];Object.keys(p).forEach((function(e){var t=p[e];x[t]=e}));var w=[];Object.keys(l).forEach((function(e){var t=l[e];w[t]=e}));var _=[];Object.keys(d).forEach((function(e){var t=d[e];_[t]=e}));var A=[];Object.keys(c).forEach((function(e){var t=c[e];A[t]=e}));var k=v.reduce((function(e,t){var r=m[t];return r===Ir||r===Pr||r===Ir||r===Br||r===qr||r===Gr||n.ext_srgb&&(r===$r||r===Kr)?e[r]=r:r===Lr||t.indexOf("rgba")>=0?e[r]=Mr:e[r]=zr,e}),{});function S(){this.internalformat=Mr,this.format=Mr,this.type=sn,this.compressed=!1,this.premultiplyAlpha=!1,this.flipY=!1,this.unpackAlignment=1,this.colorSpace=zn,this.width=0,this.height=0,this.channels=0}function E(e,t){e.internalformat=t.internalformat,e.format=t.format,e.type=t.type,e.compressed=t.compressed,e.premultiplyAlpha=t.premultiplyAlpha,e.flipY=t.flipY,e.unpackAlignment=t.unpackAlignment,e.colorSpace=t.colorSpace,e.width=t.width,e.height=t.height,e.channels=t.channels}function O(e,t){if("object"===typeof t&&t){if("premultiplyAlpha"in t&&(ne.type(t.premultiplyAlpha,"boolean","invalid premultiplyAlpha"),e.premultiplyAlpha=t.premultiplyAlpha),"flipY"in t&&(ne.type(t.flipY,"boolean","invalid texture flip"),e.flipY=t.flipY),"alignment"in t&&(ne.oneOf(t.alignment,[1,2,4,8],"invalid texture unpack alignment"),e.unpackAlignment=t.alignment),"colorSpace"in t&&(ne.parameter(t.colorSpace,h,"invalid colorSpace"),e.colorSpace=h[t.colorSpace]),"type"in t){var r=t.type;ne(n.oes_texture_float||!("float"===r||"float32"===r),"you must enable the OES_texture_float extension in order to use floating point textures."),ne(n.oes_texture_half_float||!("half float"===r||"float16"===r),"you must enable the OES_texture_half_float extension in order to use 16-bit floating point textures."),ne(n.webgl_depth_texture||!("uint16"===r||"uint32"===r||"depth stencil"===r),"you must enable the WEBGL_depth_texture extension in order to use depth/stencil textures."),ne.parameter(r,p,"invalid texture type"),e.type=p[r]}var i=e.width,o=e.height,f=e.channels,u=!1;"shape"in t?(ne(Array.isArray(t.shape)&&t.shape.length>=2,"shape must be an array"),i=t.shape[0],o=t.shape[1],3===t.shape.length&&(f=t.shape[2],ne(f>0&&f<=4,"invalid number of channels"),u=!0),ne(i>=0&&i<=a.maxTextureSize,"invalid width"),ne(o>=0&&o<=a.maxTextureSize,"invalid height")):("radius"in t&&(i=o=t.radius,ne(i>=0&&i<=a.maxTextureSize,"invalid radius")),"width"in t&&(i=t.width,ne(i>=0&&i<=a.maxTextureSize,"invalid width")),"height"in t&&(o=t.height,ne(o>=0&&o<=a.maxTextureSize,"invalid height")),"channels"in t&&(f=t.channels,ne(f>0&&f<=4,"invalid number of channels"),u=!0)),e.width=0|i,e.height=0|o,e.channels=0|f;var s=!1;if("format"in t){var c=t.format;ne(n.webgl_depth_texture||!("depth"===c||"depth stencil"===c),"you must enable the WEBGL_depth_texture extension in order to use depth/stencil textures."),ne.parameter(c,m,"invalid texture format");var l=e.internalformat=m[c];e.format=k[l],c in p&&("type"in t||(e.type=p[c])),c in b&&(e.compressed=!0),s=!0}!u&&s?e.channels=Ln[e.format]:u&&!s?e.channels!==Rn[e.format]&&(e.format=e.internalformat=Rn[e.channels]):s&&u&&ne(e.channels===Ln[e.format],"number of channels inconsistent with specified format")}}function T(e){r.pixelStorei(Fn,e.flipY),r.pixelStorei(Mn,e.premultiplyAlpha),r.pixelStorei(Pn,e.colorSpace),r.pixelStorei(Dn,e.unpackAlignment)}function C(){S.call(this),this.xOffset=0,this.yOffset=0,this.data=null,this.needsFree=!1,this.element=null,this.needsCopy=!1}function j(t,r){var n=null;if(ia(r)?n=r:r&&(ne.type(r,"object","invalid pixel data type"),O(t,r),"x"in r&&(t.xOffset=0|r.x),"y"in r&&(t.yOffset=0|r.y),ia(r.data)&&(n=r.data)),ne(!t.compressed||n instanceof Uint8Array,"compressed texture data must be stored in a uint8array"),r.copy){ne(!n,"can not specify copy and data field for the same texture");var i=o.viewportWidth,f=o.viewportHeight;t.width=t.width||i-t.xOffset,t.height=t.height||f-t.yOffset,t.needsCopy=!0,ne(t.xOffset>=0&&t.xOffset<i&&t.yOffset>=0&&t.yOffset<f&&t.width>0&&t.width<=i&&t.height>0&&t.height<=f,"copy texture read out of bounds")}else if(n){if(e(n))t.channels=t.channels||4,t.data=n,"type"in r||t.type!==sn||(t.type=oa(n));else if(Yn(n))t.channels=t.channels||4,fa(t,n),t.alignment=1,t.needsFree=!0;else if(kt(n)){var u=n.data;Array.isArray(u)||t.type!==sn||(t.type=oa(u));var s,c,l,d,h,p,m=n.shape,b=n.stride;3===m.length?(l=m[2],p=b[2]):(ne(2===m.length,"invalid ndarray pixel data, must be 2 or 3D"),l=1,p=1),s=m[0],c=m[1],d=b[0],h=b[1],t.alignment=1,t.width=s,t.height=c,t.channels=l,t.format=t.internalformat=Rn[l],t.needsFree=!0,ca(t,u,d,h,p,n.offset)}else if(Zn(n)||ea(n)||ta(n))Zn(n)||ea(n)?t.element=n:t.element=n.canvas,t.width=t.element.width,t.height=t.element.height,t.channels=4;else if(ra(n))t.element=n,t.width=n.width,t.height=n.height,t.channels=4;else if(na(n))t.element=n,t.width=n.naturalWidth,t.height=n.naturalHeight,t.channels=4;else if(aa(n))t.element=n,t.width=n.videoWidth,t.height=n.videoHeight,t.channels=4;else if(Xn(n)){var g=t.width||n[0].length,v=t.height||n.length,y=t.channels;y=Or(n[0][0])?y||n[0][0].length:y||1;for(var x=Et.shape(n),w=1,_=0;_<x.length;++_)w*=x[_];var A=ua(t,w);Et.flatten(n,x,"",A),sa(t,A),t.alignment=1,t.width=g,t.height=v,t.channels=y,t.format=t.internalformat=Rn[y],t.needsFree=!0}}else t.width=t.width||1,t.height=t.height||1,t.channels=t.channels||4;t.type===dn?ne(a.extensions.indexOf("oes_texture_float")>=0,"oes_texture_float extension not enabled"):t.type===Qr&&ne(a.extensions.indexOf("oes_texture_half_float")>=0,"oes_texture_half_float extension not enabled")}function D(e,t,n){var a=e.element,o=e.data,f=e.internalformat,u=e.format,s=e.type,c=e.width,l=e.height;T(e),a?r.texImage2D(t,n,u,u,s,a):e.compressed?r.compressedTexImage2D(t,n,f,c,l,0,o):e.needsCopy?(i(),r.copyTexImage2D(t,n,u,e.xOffset,e.yOffset,c,l,0)):r.texImage2D(t,n,u,c,l,0,u,s,o||null)}function F(e,t,n,a,o){var f=e.element,u=e.data,s=e.internalformat,c=e.format,l=e.type,d=e.width,h=e.height;T(e),f?r.texSubImage2D(t,o,n,a,c,l,f):e.compressed?r.compressedTexSubImage2D(t,o,n,a,s,d,h,u):e.needsCopy?(i(),r.copyTexSubImage2D(t,o,n,a,e.xOffset,e.yOffset,d,h)):r.texSubImage2D(t,o,n,a,d,h,c,l,u)}var M=[];function P(){return M.pop()||new C}function z(e){e.needsFree&&Re.freeType(e.data),C.call(e),M.push(e)}function I(){S.call(this),this.genMipmaps=!1,this.mipmapHint=On,this.mipmask=0,this.images=Array(16)}function B(e,t,r){var n=e.images[0]=P();e.mipmask=1,n.width=e.width=t,n.height=e.height=r,n.channels=e.channels=4}function R(e,t){var r=null;if(ia(t))r=e.images[0]=P(),E(r,e),j(r,t),e.mipmask=1;else if(O(e,t),Array.isArray(t.mipmap))for(var n=t.mipmap,a=0;a<n.length;++a)r=e.images[a]=P(),E(r,e),r.width>>=a,r.height>>=a,j(r,n[a]),e.mipmask|=1<<a;else r=e.images[0]=P(),E(r,e),j(r,t),e.mipmask=1;E(e,e.images[0]),!e.compressed||e.internalformat!==Yr&&e.internalformat!==Xr&&e.internalformat!==Jr&&e.internalformat!==Zr||ne(e.width%4===0&&e.height%4===0,"for compressed texture formats, mipmap level 0 must have width and height that are a multiple of 4")}function L(e,t){for(var r=e.images,n=0;n<r.length;++n){if(!r[n])return;D(r[n],t,n)}}var V=[];function N(){var e=V.pop()||new I;S.call(e),e.mipmask=0;for(var t=0;t<16;++t)e.images[t]=null;return e}function H(e){for(var t=e.images,r=0;r<t.length;++r)t[r]&&z(t[r]),t[r]=null;V.push(e)}function U(){this.minFilter=xn,this.magFilter=xn,this.wrapS=bn,this.wrapT=bn,this.anisotropic=1,this.genMipmaps=!1,this.mipmapHint=On}function W(e,t){if("min"in t){var r=t.min;ne.parameter(r,d),e.minFilter=d[r],Bn.indexOf(e.minFilter)>=0&&!("faces"in t)&&(e.genMipmaps=!0)}if("mag"in t){var n=t.mag;ne.parameter(n,l),e.magFilter=l[n]}var i=e.wrapS,o=e.wrapT;if("wrap"in t){var f=t.wrap;"string"===typeof f?(ne.parameter(f,c),i=o=c[f]):Array.isArray(f)&&(ne.parameter(f[0],c),ne.parameter(f[1],c),i=c[f[0]],o=c[f[1]])}else{if("wrapS"in t){var u=t.wrapS;ne.parameter(u,c),i=c[u]}if("wrapT"in t){var h=t.wrapT;ne.parameter(h,c),o=c[h]}}if(e.wrapS=i,e.wrapT=o,"anisotropic"in t){var p=t.anisotropic;ne("number"===typeof p&&p>=1&&p<=a.maxAnisotropic,"aniso samples must be between 1 and "),e.anisotropic=t.anisotropic}if("mipmap"in t){var m=!1;switch(typeof t.mipmap){case"string":ne.parameter(t.mipmap,s,"invalid mipmap hint"),e.mipmapHint=s[t.mipmap],e.genMipmaps=!0,m=!0;break;case"boolean":m=e.genMipmaps=t.mipmap;break;case"object":ne(Array.isArray(t.mipmap),"invalid mipmap type"),e.genMipmaps=!1,m=!0;break;default:ne.raise("invalid mipmap type")}m&&!("min"in t)&&(e.minFilter=_n)}}function q(e,t){r.texParameteri(t,yn,e.minFilter),r.texParameteri(t,vn,e.magFilter),r.texParameteri(t,hn,e.wrapS),r.texParameteri(t,pn,e.wrapT),n.ext_texture_filter_anisotropic&&r.texParameteri(t,jn,e.anisotropic),e.genMipmaps&&(r.hint(En,e.mipmapHint),r.generateMipmap(t))}var G=0,$={},K=a.maxTextureUnits,Q=Array(K).map((function(){return null}));function Y(e){S.call(this),this.mipmask=0,this.internalformat=Mr,this.id=G++,this.refCount=1,this.target=e,this.texture=r.createTexture(),this.unit=-1,this.bindCount=0,this.texInfo=new U,u.profile&&(this.stats={size:0})}function X(e){r.activeTexture(In),r.bindTexture(e.target,e.texture)}function J(){var e=Q[0];e?r.bindTexture(e.target,e.texture):r.bindTexture(jr,null)}function Z(e){var t=e.texture;ne(t,"must not double destroy texture");var n=e.unit,a=e.target;n>=0&&(r.activeTexture(In+n),r.bindTexture(a,null),Q[n]=null),r.deleteTexture(t),e.texture=null,e.params=null,e.pixels=null,e.refCount=0,delete $[e.id],f.textureCount--}function ee(e,t){var n=new Y(jr);function i(e,t){var r=n.texInfo;U.call(r);var o=N();return"number"===typeof e?B(o,0|e,"number"===typeof t?0|t:0|e):e?(ne.type(e,"object","invalid arguments to regl.texture"),W(r,e),R(o,e)):B(o,1,1),r.genMipmaps&&(o.mipmask=(o.width<<1)-1),n.mipmask=o.mipmask,E(n,o),ne.texture2D(r,o,a),n.internalformat=o.internalformat,i.width=o.width,i.height=o.height,X(n),L(o,jr),q(r,jr),J(),H(o),u.profile&&(n.stats.size=la(n.internalformat,n.type,o.width,o.height,r.genMipmaps,!1)),i.format=y[n.internalformat],i.type=x[n.type],i.mag=w[r.magFilter],i.min=_[r.minFilter],i.wrapS=A[r.wrapS],i.wrapT=A[r.wrapT],i}function o(e,t,r,a){ne(!!e,"must specify image data");var o=0|t,f=0|r,u=0|a,s=P();return E(s,n),s.width=0,s.height=0,j(s,e),s.width=s.width||(n.width>>u)-o,s.height=s.height||(n.height>>u)-f,ne(n.type===s.type&&n.format===s.format&&n.internalformat===s.internalformat,"incompatible format for texture.subimage"),ne(o>=0&&f>=0&&o+s.width<=n.width&&f+s.height<=n.height,"texture.subimage write out of bounds"),ne(n.mipmask&1<<u,"missing mipmap data"),ne(s.data||s.element||s.needsCopy,"missing image data"),X(n),F(s,jr,o,f,u),J(),z(s),i}function s(e,t){var a=0|e,o=0|t||a;if(a===n.width&&o===n.height)return i;i.width=n.width=a,i.height=n.height=o,X(n);for(var f=0;n.mipmask>>f;++f){var s=a>>f,c=o>>f;if(!s||!c)break;r.texImage2D(jr,f,n.format,s,c,0,n.format,n.type,null)}return J(),u.profile&&(n.stats.size=la(n.internalformat,n.type,a,o,!1,!1)),i}return $[n.id]=n,f.textureCount++,i(e,t),i.subimage=o,i.resize=s,i._reglType="texture2d",i._texture=n,u.profile&&(i.stats=n.stats),i.destroy=function(){n.decRef()},i}function te(e,t,n,i,o,s){var c=new Y(Dr);$[c.id]=c,f.cubeCount++;var l=new Array(6);function d(e,t,r,n,i,o){var f,s=c.texInfo;for(U.call(s),f=0;f<6;++f)l[f]=N();if("number"!==typeof e&&e)if("object"===typeof e)if(t)R(l[0],e),R(l[1],t),R(l[2],r),R(l[3],n),R(l[4],i),R(l[5],o);else if(W(s,e),O(c,e),"faces"in e){var h=e.faces;for(ne(Array.isArray(h)&&6===h.length,"cube faces must be a length 6 array"),f=0;f<6;++f)ne("object"===typeof h[f]&&!!h[f],"invalid input for cube map face"),E(l[f],c),R(l[f],h[f])}else for(f=0;f<6;++f)R(l[f],e);else ne.raise("invalid arguments to cube map");else{var p=0|e||1;for(f=0;f<6;++f)B(l[f],p,p)}for(E(c,l[0]),a.npotTextureCube||ne(Tr(c.width)&&Tr(c.height),"your browser does not support non power or two texture dimensions"),s.genMipmaps?c.mipmask=(l[0].width<<1)-1:c.mipmask=l[0].mipmask,ne.textureCube(c,s,l,a),c.internalformat=l[0].internalformat,d.width=l[0].width,d.height=l[0].height,X(c),f=0;f<6;++f)L(l[f],Fr+f);for(q(s,Dr),J(),u.profile&&(c.stats.size=la(c.internalformat,c.type,d.width,d.height,s.genMipmaps,!0)),d.format=y[c.internalformat],d.type=x[c.type],d.mag=w[s.magFilter],d.min=_[s.minFilter],d.wrapS=A[s.wrapS],d.wrapT=A[s.wrapT],f=0;f<6;++f)H(l[f]);return d}function h(e,t,r,n,a){ne(!!t,"must specify image data"),ne("number"===typeof e&&e===(0|e)&&e>=0&&e<6,"invalid face");var i=0|r,o=0|n,f=0|a,u=P();return E(u,c),u.width=0,u.height=0,j(u,t),u.width=u.width||(c.width>>f)-i,u.height=u.height||(c.height>>f)-o,ne(c.type===u.type&&c.format===u.format&&c.internalformat===u.internalformat,"incompatible format for texture.subimage"),ne(i>=0&&o>=0&&i+u.width<=c.width&&o+u.height<=c.height,"texture.subimage write out of bounds"),ne(c.mipmask&1<<f,"missing mipmap data"),ne(u.data||u.element||u.needsCopy,"missing image data"),X(c),F(u,Fr+e,i,o,f),J(),z(u),d}function p(e){var t=0|e;if(t!==c.width){d.width=c.width=t,d.height=c.height=t,X(c);for(var n=0;n<6;++n)for(var a=0;c.mipmask>>a;++a)r.texImage2D(Fr+n,a,c.format,t>>a,t>>a,0,c.format,c.type,null);return J(),u.profile&&(c.stats.size=la(c.internalformat,c.type,d.width,d.height,!1,!0)),d}}return d(e,t,n,i,o,s),d.subimage=h,d.resize=p,d._reglType="textureCube",d._texture=c,u.profile&&(d.stats=c.stats),d.destroy=function(){c.decRef()},d}function re(){for(var e=0;e<K;++e)r.activeTexture(In+e),r.bindTexture(jr,null),Q[e]=null;St($).forEach(Z),f.cubeCount=0,f.textureCount=0}function ae(){for(var e=0;e<K;++e){var t=Q[e];t&&(t.bindCount=0,t.unit=-1,Q[e]=null)}St($).forEach((function(e){e.texture=r.createTexture(),r.bindTexture(e.target,e.texture);for(var t=0;t<32;++t)if(0!==(e.mipmask&1<<t))if(e.target===jr)r.texImage2D(jr,t,e.internalformat,e.width>>t,e.height>>t,0,e.internalformat,e.type,null);else for(var n=0;n<6;++n)r.texImage2D(Fr+n,t,e.internalformat,e.width>>t,e.height>>t,0,e.internalformat,e.type,null);q(e.texInfo,e.target)}))}function ie(){for(var e=0;e<K;++e){var t=Q[e];t&&(t.bindCount=0,t.unit=-1,Q[e]=null),r.activeTexture(In+e),r.bindTexture(jr,null),r.bindTexture(Dr,null)}}return t(Y.prototype,{bind:function(){var e=this;e.bindCount+=1;var t=e.unit;if(t<0){for(var n=0;n<K;++n){var a=Q[n];if(a){if(a.bindCount>0)continue;a.unit=-1}Q[n]=e,t=n;break}t>=K&&ne.raise("insufficient number of texture units"),u.profile&&f.maxTextureUnits<t+1&&(f.maxTextureUnits=t+1),e.unit=t,r.activeTexture(In+t),r.bindTexture(e.target,e.texture)}return t},unbind:function(){this.bindCount-=1},decRef:function(){--this.refCount<=0&&Z(this)}}),u.profile&&(f.getTotalTextureSize=function(){var e=0;return Object.keys($).forEach((function(t){e+=$[t].stats.size})),e}),{create2D:ee,createCube:te,clear:re,getTexture:function(e){return null},restore:ae,refresh:ie}}Qn[Rr]=2,Qn[Lr]=2,Qn[Vr]=2,Qn[Gr]=4,Qn[Yr]=.5,Qn[Xr]=.5,Qn[Jr]=1,Qn[Zr]=1,Qn[en]=.5,Qn[tn]=1,Qn[rn]=1,Qn[nn]=.5,Qn[an]=.25,Qn[on]=.5,Qn[fn]=.25,Qn[un]=.5;var ha=36161,pa=32854,ma=32855,ba=36194,ga=33189,va=36168,ya=34041,xa=35907,wa=34836,_a=34842,Aa=34843,ka=[];function Sa(e,t,r){return ka[e]*t*r}ka[pa]=2,ka[ma]=2,ka[ba]=2,ka[ga]=2,ka[va]=1,ka[ya]=4,ka[xa]=4,ka[wa]=16,ka[_a]=8,ka[Aa]=6;var Ea=function(e,t,r,n,a){var i={rgba4:pa,rgb565:ba,"rgb5 a1":ma,depth:ga,stencil:va,"depth stencil":ya};t.ext_srgb&&(i["srgba"]=xa),t.ext_color_buffer_half_float&&(i["rgba16f"]=_a,i["rgb16f"]=Aa),t.webgl_color_buffer_float&&(i["rgba32f"]=wa);var o=[];Object.keys(i).forEach((function(e){var t=i[e];o[t]=e}));var f=0,u={};function s(e){this.id=f++,this.refCount=1,this.renderbuffer=e,this.format=pa,this.width=0,this.height=0,a.profile&&(this.stats={size:0})}function c(t){var r=t.renderbuffer;ne(r,"must not double destroy renderbuffer"),e.bindRenderbuffer(ha,null),e.deleteRenderbuffer(r),t.renderbuffer=null,t.refCount=0,delete u[t.id],n.renderbufferCount--}function l(t,f){var c=new s(e.createRenderbuffer());function l(t,n){var f=0,u=0,s=pa;if("object"===typeof t&&t){var d=t;if("shape"in d){var h=d.shape;ne(Array.isArray(h)&&h.length>=2,"invalid renderbuffer shape"),f=0|h[0],u=0|h[1]}else"radius"in d&&(f=u=0|d.radius),"width"in d&&(f=0|d.width),"height"in d&&(u=0|d.height);"format"in d&&(ne.parameter(d.format,i,"invalid renderbuffer format"),s=i[d.format])}else"number"===typeof t?(f=0|t,u="number"===typeof n?0|n:f):t?ne.raise("invalid arguments to renderbuffer constructor"):f=u=1;if(ne(f>0&&u>0&&f<=r.maxRenderbufferSize&&u<=r.maxRenderbufferSize,"invalid renderbuffer size"),f!==c.width||u!==c.height||s!==c.format)return l.width=c.width=f,l.height=c.height=u,c.format=s,e.bindRenderbuffer(ha,c.renderbuffer),e.renderbufferStorage(ha,s,f,u),ne(0===e.getError(),"invalid render buffer format"),a.profile&&(c.stats.size=Sa(c.format,c.width,c.height)),l.format=o[c.format],l}function d(t,n){var i=0|t,o=0|n||i;return i===c.width&&o===c.height||(ne(i>0&&o>0&&i<=r.maxRenderbufferSize&&o<=r.maxRenderbufferSize,"invalid renderbuffer size"),l.width=c.width=i,l.height=c.height=o,e.bindRenderbuffer(ha,c.renderbuffer),e.renderbufferStorage(ha,c.format,i,o),ne(0===e.getError(),"invalid render buffer format"),a.profile&&(c.stats.size=Sa(c.format,c.width,c.height))),l}return u[c.id]=c,n.renderbufferCount++,l(t,f),l.resize=d,l._reglType="renderbuffer",l._renderbuffer=c,a.profile&&(l.stats=c.stats),l.destroy=function(){c.decRef()},l}function d(){St(u).forEach((function(t){t.renderbuffer=e.createRenderbuffer(),e.bindRenderbuffer(ha,t.renderbuffer),e.renderbufferStorage(ha,t.format,t.width,t.height)})),e.bindRenderbuffer(ha,null)}return s.prototype.decRef=function(){--this.refCount<=0&&c(this)},a.profile&&(n.getTotalRenderbufferSize=function(){var e=0;return Object.keys(u).forEach((function(t){e+=u[t].stats.size})),e}),{create:l,clear:function(){St(u).forEach(c)},restore:d}},Oa=36160,Ta=36161,Ca=3553,ja=34069,Da=36064,Fa=36096,Ma=36128,Pa=33306,za=36053,Ia=36054,Ba=36055,Ra=36057,La=36061,Va=36193,Na=5121,Ha=5126,Ua=6407,Wa=6408,qa=6402,Ga=[Ua,Wa],$a=[];$a[Wa]=4,$a[Ua]=3;var Ka=[];Ka[Na]=1,Ka[Ha]=4,Ka[Va]=2;var Qa=32854,Ya=32855,Xa=36194,Ja=33189,Za=36168,ei=34041,ti=35907,ri=34836,ni=34842,ai=34843,ii=[Qa,Ya,Xa,ti,ni,ai,ri],oi={};function fi(e,r,n,a,i,o){var f={cur:null,next:null,dirty:!1,setFBO:null},u=["rgba"],s=["rgba4","rgb565","rgb5 a1"];r.ext_srgb&&s.push("srgba"),r.ext_color_buffer_half_float&&s.push("rgba16f","rgb16f"),r.webgl_color_buffer_float&&s.push("rgba32f");var c=["uint8"];function l(e,t,r){this.target=e,this.texture=t,this.renderbuffer=r;var n=0,a=0;t?(n=t.width,a=t.height):r&&(n=r.width,a=r.height),this.width=n,this.height=a}function d(e){e&&(e.texture&&e.texture._texture.decRef(),e.renderbuffer&&e.renderbuffer._renderbuffer.decRef())}function h(e,t,r){if(e)if(e.texture){var n=e.texture._texture,a=Math.max(1,n.width),i=Math.max(1,n.height);ne(a===t&&i===r,"inconsistent width/height for supplied texture"),n.refCount+=1}else{var o=e.renderbuffer._renderbuffer;ne(o.width===t&&o.height===r,"inconsistent width/height for renderbuffer"),o.refCount+=1}}function p(t,r){r&&(r.texture?e.framebufferTexture2D(Oa,t,r.target,r.texture._texture.texture,0):e.framebufferRenderbuffer(Oa,t,Ta,r.renderbuffer._renderbuffer.renderbuffer))}function m(e){var t=Ca,r=null,n=null,a=e;"object"===typeof e&&(a=e.data,"target"in e&&(t=0|e.target)),ne.type(a,"function","invalid attachment data");var i=a._reglType;return"texture2d"===i?(r=a,ne(t===Ca)):"textureCube"===i?(r=a,ne(t>=ja&&t<ja+6,"invalid cube map target")):"renderbuffer"===i?(n=a,t=Ta):ne.raise("invalid regl object for attachment"),new l(t,r,n)}function b(e,t,r,n,o){if(r){var f=a.create2D({width:e,height:t,format:n,type:o});return f._texture.refCount=0,new l(Ca,f,null)}var u=i.create({width:e,height:t,format:n});return u._renderbuffer.refCount=0,new l(Ta,null,u)}function g(e){return e&&(e.texture||e.renderbuffer)}function v(e,t,r){e&&(e.texture?e.texture.resize(t,r):e.renderbuffer&&e.renderbuffer.resize(t,r),e.width=t,e.height=r)}r.oes_texture_half_float&&c.push("half float","float16"),r.oes_texture_float&&c.push("float","float32");var y=0,x={};function w(){this.id=y++,x[this.id]=this,this.framebuffer=e.createFramebuffer(),this.width=0,this.height=0,this.colorAttachments=[],this.depthAttachment=null,this.stencilAttachment=null,this.depthStencilAttachment=null}function _(e){e.colorAttachments.forEach(d),d(e.depthAttachment),d(e.stencilAttachment),d(e.depthStencilAttachment)}function A(t){var r=t.framebuffer;ne(r,"must not double destroy framebuffer"),e.deleteFramebuffer(r),t.framebuffer=null,o.framebufferCount--,delete x[t.id]}function k(t){var r;e.bindFramebuffer(Oa,t.framebuffer);var a=t.colorAttachments;for(r=0;r<a.length;++r)p(Da+r,a[r]);for(r=a.length;r<n.maxColorAttachments;++r)e.framebufferTexture2D(Oa,Da+r,Ca,null,0);e.framebufferTexture2D(Oa,Pa,Ca,null,0),e.framebufferTexture2D(Oa,Fa,Ca,null,0),e.framebufferTexture2D(Oa,Ma,Ca,null,0),p(Fa,t.depthAttachment),p(Ma,t.stencilAttachment),p(Pa,t.depthStencilAttachment);var i=e.checkFramebufferStatus(Oa);e.isContextLost()||i===za||ne.raise("framebuffer configuration not supported, status = "+oi[i]),e.bindFramebuffer(Oa,f.next?f.next.framebuffer:null),f.cur=f.next,e.getError()}function S(e,a){var i=new w;function l(e,t){var a;ne(f.next!==i,"can not update framebuffer which is currently in use");var o=0,d=0,p=!0,v=!0,y=null,x=!0,w="rgba",A="uint8",S=1,E=null,O=null,T=null,C=!1;if("number"===typeof e)o=0|e,d=0|t||o;else if(e){ne.type(e,"object","invalid arguments for framebuffer");var j=e;if("shape"in j){var D=j.shape;ne(Array.isArray(D)&&D.length>=2,"invalid shape for framebuffer"),o=D[0],d=D[1]}else"radius"in j&&(o=d=j.radius),"width"in j&&(o=j.width),"height"in j&&(d=j.height);("color"in j||"colors"in j)&&(y=j.color||j.colors,Array.isArray(y)&&ne(1===y.length||r.webgl_draw_buffers,"multiple render targets not supported")),y||("colorCount"in j&&(S=0|j.colorCount,ne(S>0,"invalid color buffer count")),"colorTexture"in j&&(x=!!j.colorTexture,w="rgba4"),"colorType"in j&&(A=j.colorType,x?(ne(r.oes_texture_float||!("float"===A||"float32"===A),"you must enable OES_texture_float in order to use floating point framebuffer objects"),ne(r.oes_texture_half_float||!("half float"===A||"float16"===A),"you must enable OES_texture_half_float in order to use 16-bit floating point framebuffer objects")):"half float"===A||"float16"===A?(ne(r.ext_color_buffer_half_float,"you must enable EXT_color_buffer_half_float to use 16-bit render buffers"),w="rgba16f"):"float"!==A&&"float32"!==A||(ne(r.webgl_color_buffer_float,"you must enable WEBGL_color_buffer_float in order to use 32-bit floating point renderbuffers"),w="rgba32f"),ne.oneOf(A,c,"invalid color type")),"colorFormat"in j&&(w=j.colorFormat,u.indexOf(w)>=0?x=!0:s.indexOf(w)>=0?x=!1:x?ne.oneOf(j.colorFormat,u,"invalid color format for texture"):ne.oneOf(j.colorFormat,s,"invalid color format for renderbuffer"))),("depthTexture"in j||"depthStencilTexture"in j)&&(C=!(!j.depthTexture&&!j.depthStencilTexture),ne(!C||r.webgl_depth_texture,"webgl_depth_texture extension not supported")),"depth"in j&&("boolean"===typeof j.depth?p=j.depth:(E=j.depth,v=!1)),"stencil"in j&&("boolean"===typeof j.stencil?v=j.stencil:(O=j.stencil,p=!1)),"depthStencil"in j&&("boolean"===typeof j.depthStencil?p=v=j.depthStencil:(T=j.depthStencil,p=!1,v=!1))}else o=d=1;var F=null,M=null,P=null,z=null;if(Array.isArray(y))F=y.map(m);else if(y)F=[m(y)];else for(F=new Array(S),a=0;a<S;++a)F[a]=b(o,d,x,w,A);ne(r.webgl_draw_buffers||F.length<=1,"you must enable the WEBGL_draw_buffers extension in order to use multiple color buffers."),ne(F.length<=n.maxColorAttachments,"too many color attachments, not supported"),o=o||F[0].width,d=d||F[0].height,E?M=m(E):p&&!v&&(M=b(o,d,C,"depth","uint32")),O?P=m(O):v&&!p&&(P=b(o,d,!1,"stencil","uint8")),T?z=m(T):!E&&!O&&v&&p&&(z=b(o,d,C,"depth stencil","depth stencil")),ne(!!E+!!O+!!T<=1,"invalid framebuffer configuration, can specify exactly one depth/stencil attachment");var I=null;for(a=0;a<F.length;++a)if(h(F[a],o,d),ne(!F[a]||F[a].texture&&Ga.indexOf(F[a].texture._texture.format)>=0||F[a].renderbuffer&&ii.indexOf(F[a].renderbuffer._renderbuffer.format)>=0,"framebuffer color attachment "+a+" is invalid"),F[a]&&F[a].texture){var B=$a[F[a].texture._texture.format]*Ka[F[a].texture._texture.type];null===I?I=B:ne(I===B,"all color attachments much have the same number of bits per pixel.")}return h(M,o,d),ne(!M||M.texture&&M.texture._texture.format===qa||M.renderbuffer&&M.renderbuffer._renderbuffer.format===Ja,"invalid depth attachment for framebuffer object"),h(P,o,d),ne(!P||P.renderbuffer&&P.renderbuffer._renderbuffer.format===Za,"invalid stencil attachment for framebuffer object"),h(z,o,d),ne(!z||z.texture&&z.texture._texture.format===ei||z.renderbuffer&&z.renderbuffer._renderbuffer.format===ei,"invalid depth-stencil attachment for framebuffer object"),_(i),i.width=o,i.height=d,i.colorAttachments=F,i.depthAttachment=M,i.stencilAttachment=P,i.depthStencilAttachment=z,l.color=F.map(g),l.depth=g(M),l.stencil=g(P),l.depthStencil=g(z),l.width=i.width,l.height=i.height,k(i),l}function d(e,t){ne(f.next!==i,"can not resize a framebuffer which is currently in use");var r=Math.max(0|e,1),n=Math.max(0|t||r,1);if(r===i.width&&n===i.height)return l;for(var a=i.colorAttachments,o=0;o<a.length;++o)v(a[o],r,n);return v(i.depthAttachment,r,n),v(i.stencilAttachment,r,n),v(i.depthStencilAttachment,r,n),i.width=l.width=r,i.height=l.height=n,k(i),l}return o.framebufferCount++,l(e,a),t(l,{resize:d,_reglType:"framebuffer",_framebuffer:i,destroy:function(){A(i),_(i)},use:function(e){f.setFBO({framebuffer:l},e)}})}function E(e){var i=Array(6);function o(e){var n;ne(i.indexOf(f.next)<0,"can not update framebuffer which is currently in use");var s,l={color:null},d=0,h=null,p="rgba",m="uint8",b=1;if("number"===typeof e)d=0|e;else if(e){ne.type(e,"object","invalid arguments for framebuffer");var g=e;if("shape"in g){var v=g.shape;ne(Array.isArray(v)&&v.length>=2,"invalid shape for framebuffer"),ne(v[0]===v[1],"cube framebuffer must be square"),d=v[0]}else"radius"in g&&(d=0|g.radius),"width"in g?(d=0|g.width,"height"in g&&ne(g.height===d,"must be square")):"height"in g&&(d=0|g.height);("color"in g||"colors"in g)&&(h=g.color||g.colors,Array.isArray(h)&&ne(1===h.length||r.webgl_draw_buffers,"multiple render targets not supported")),h||("colorCount"in g&&(b=0|g.colorCount,ne(b>0,"invalid color buffer count")),"colorType"in g&&(ne.oneOf(g.colorType,c,"invalid color type"),m=g.colorType),"colorFormat"in g&&(p=g.colorFormat,ne.oneOf(g.colorFormat,u,"invalid color format for texture"))),"depth"in g&&(l.depth=g.depth),"stencil"in g&&(l.stencil=g.stencil),"depthStencil"in g&&(l.depthStencil=g.depthStencil)}else d=1;if(h)if(Array.isArray(h))for(s=[],n=0;n<h.length;++n)s[n]=h[n];else s=[h];else{s=Array(b);var y={radius:d,format:p,type:m};for(n=0;n<b;++n)s[n]=a.createCube(y)}for(l.color=Array(s.length),n=0;n<s.length;++n){var x=s[n];ne("function"===typeof x&&"textureCube"===x._reglType,"invalid cube map"),d=d||x.width,ne(x.width===d&&x.height===d,"invalid cube map shape"),l.color[n]={target:ja,data:s[n]}}for(n=0;n<6;++n){for(var w=0;w<s.length;++w)l.color[w].target=ja+n;n>0&&(l.depth=i[0].depth,l.stencil=i[0].stencil,l.depthStencil=i[0].depthStencil),i[n]?i[n](l):i[n]=S(l)}return t(o,{width:d,height:d,color:s})}function s(e){var t,r=0|e;if(ne(r>0&&r<=n.maxCubeMapSize,"invalid radius for cube fbo"),r===o.width)return o;var a=o.color;for(t=0;t<a.length;++t)a[t].resize(r);for(t=0;t<6;++t)i[t].resize(r);return o.width=o.height=r,o}return o(e),t(o,{faces:i,resize:s,_reglType:"framebufferCube",destroy:function(){i.forEach((function(e){e.destroy()}))}})}function O(){f.cur=null,f.next=null,f.dirty=!0,St(x).forEach((function(t){t.framebuffer=e.createFramebuffer(),k(t)}))}return t(f,{getFramebuffer:function(e){if("function"===typeof e&&"framebuffer"===e._reglType){var t=e._framebuffer;if(t instanceof w)return t}return null},create:S,createCube:E,clear:function(){St(x).forEach(A)},restore:O})}oi[za]="complete",oi[Ia]="incomplete attachment",oi[Ra]="incomplete dimensions",oi[Ba]="incomplete, missing attachment",oi[La]="unsupported";var ui=5126,si=34962;function ci(){this.state=0,this.x=0,this.y=0,this.z=0,this.w=0,this.buffer=null,this.size=0,this.normalized=!1,this.type=ui,this.offset=0,this.stride=0,this.divisor=0}function li(t,r,n,a,i){for(var o=n.maxAttributes,f=new Array(o),u=0;u<o;++u)f[u]=new ci;var s=0,c={},l={Record:ci,scope:{},state:f,currentVAO:null,targetVAO:null,restore:h()?x:function(){},createVAO:w,getVAO:m,destroyBuffer:d,setVAO:h()?b:g,clear:h()?v:function(){}};function d(e){for(var r=0;r<f.length;++r){var n=f[r];n.buffer===e&&(t.disableVertexAttribArray(r),n.buffer=null)}}function h(){return r.oes_vertex_array_object}function p(){return r.angle_instanced_arrays}function m(e){return"function"===typeof e&&e._vao?e._vao:null}function b(e){if(e!==l.currentVAO){var t=h();e?t.bindVertexArrayOES(e.vao):t.bindVertexArrayOES(null),l.currentVAO=e}}function g(e){if(e!==l.currentVAO){if(e)e.bindAttrs();else for(var r=p(),n=0;n<f.length;++n){var a=f[n];a.buffer?(t.enableVertexAttribArray(n),t.vertexAttribPointer(n,a.size,a.type,a.normalized,a.stride,a.offfset),r&&a.divisor&&r.vertexAttribDivisorANGLE(n,a.divisor)):(t.disableVertexAttribArray(n),t.vertexAttrib4f(n,a.x,a.y,a.z,a.w))}l.currentVAO=e}}function v(){St(c).forEach((function(e){e.destroy()}))}function y(){this.id=++s,this.attributes=[];var e=h();this.vao=e?e.createVertexArrayOES():null,c[this.id]=this,this.buffers=[]}function x(){var e=h();e&&St(c).forEach((function(e){e.refresh()}))}function w(t){var n=new y;function f(t){ne(Array.isArray(t),"arguments to vertex array constructor must be an array"),ne(t.length<o,"too many attributes"),ne(t.length>0,"must specify at least one attribute");var a={},u=n.attributes;u.length=t.length;for(var s=0;s<t.length;++s){var c,l=t[s],d=u[s]=new ci,h=l.data||l;if(Array.isArray(h)||e(h)||kt(h))n.buffers[s]&&(c=n.buffers[s],e(h)&&c._buffer.byteLength>=h.byteLength?c.subdata(h):(c.destroy(),n.buffers[s]=null)),n.buffers[s]||(c=n.buffers[s]=i.create(l,si,!1,!0)),d.buffer=i.getBuffer(c),d.size=0|d.buffer.dimension,d.normalized=!1,d.type=d.buffer.dtype,d.offset=0,d.stride=0,d.divisor=0,d.state=1,a[s]=1;else i.getBuffer(l)?(d.buffer=i.getBuffer(l),d.size=0|d.buffer.dimension,d.normalized=!1,d.type=d.buffer.dtype,d.offset=0,d.stride=0,d.divisor=0,d.state=1):i.getBuffer(l.buffer)?(d.buffer=i.getBuffer(l.buffer),d.size=0|(+l.size||d.buffer.dimension),d.normalized=!!l.normalized||!1,"type"in l?(ne.parameter(l.type,Ht,"invalid buffer type"),d.type=Ht[l.type]):d.type=d.buffer.dtype,d.offset=0|(l.offset||0),d.stride=0|(l.stride||0),d.divisor=0|(l.divisor||0),d.state=1,ne(d.size>=1&&d.size<=4,"size must be between 1 and 4"),ne(d.offset>=0,"invalid offset"),ne(d.stride>=0&&d.stride<=255,"stride must be between 0 and 255"),ne(d.divisor>=0,"divisor must be positive"),ne(!d.divisor||!!r.angle_instanced_arrays,"ANGLE_instanced_arrays must be enabled to use divisor")):"x"in l?(ne(s>0,"first attribute must not be a constant"),d.x=+l.x||0,d.y=+l.y||0,d.z=+l.z||0,d.w=+l.w||0,d.state=2):ne(!1,"invalid attribute spec for location "+s)}for(var p=0;p<n.buffers.length;++p)!a[p]&&n.buffers[p]&&(n.buffers[p].destroy(),n.buffers[p]=null);return n.refresh(),f}return a.vaoCount+=1,f.destroy=function(){for(var e=0;e<n.buffers.length;++e)n.buffers[e]&&n.buffers[e].destroy();n.buffers.length=0,n.destroy()},f._vao=n,f._reglType="vao",f(t)}return y.prototype.bindAttrs=function(){for(var e=p(),r=this.attributes,n=0;n<r.length;++n){var a=r[n];a.buffer?(t.enableVertexAttribArray(n),t.bindBuffer(si,a.buffer.buffer),t.vertexAttribPointer(n,a.size,a.type,a.normalized,a.stride,a.offset),e&&a.divisor&&e.vertexAttribDivisorANGLE(n,a.divisor)):(t.disableVertexAttribArray(n),t.vertexAttrib4f(n,a.x,a.y,a.z,a.w))}for(var i=r.length;i<o;++i)t.disableVertexAttribArray(i)},y.prototype.refresh=function(){var e=h();e&&(e.bindVertexArrayOES(this.vao),this.bindAttrs(),l.currentVAO=this)},y.prototype.destroy=function(){if(this.vao){var e=h();this===l.currentVAO&&(l.currentVAO=null,e.bindVertexArrayOES(null)),e.deleteVertexArrayOES(this.vao),this.vao=null}c[this.id]&&(delete c[this.id],a.vaoCount-=1)},l}var di=35632,hi=35633,pi=35718,mi=35721;function bi(e,r,n,a){var i={},o={};function f(e,t,r,n){this.name=e,this.id=t,this.location=r,this.info=n}function u(e,t){for(var r=0;r<e.length;++r)if(e[r].id===t.id)return void(e[r].location=t.location);e.push(t)}function s(t,n,a){var f=t===di?i:o,u=f[n];if(!u){var s=r.str(n);u=e.createShader(t),e.shaderSource(u,s),e.compileShader(u),ne.shaderError(e,u,s,t,a),f[n]=u}return u}var c={},l=[],d=0;function h(e,t){this.id=d++,this.fragId=e,this.vertId=t,this.program=null,this.uniforms=[],this.attributes=[],this.refCount=1,a.profile&&(this.stats={uniformsCount:0,attributesCount:0})}function p(t,n,i){var o,c,l=s(di,t.fragId),d=s(hi,t.vertId),h=t.program=e.createProgram();if(e.attachShader(h,l),e.attachShader(h,d),i)for(o=0;o<i.length;++o){var p=i[o];e.bindAttribLocation(h,p[0],p[1])}e.linkProgram(h),ne.linkError(e,h,r.str(t.fragId),r.str(t.vertId),n);var m=e.getProgramParameter(h,pi);a.profile&&(t.stats.uniformsCount=m);var b=t.uniforms;for(o=0;o<m;++o)if(c=e.getActiveUniform(h,o),c)if(c.size>1)for(var g=0;g<c.size;++g){var v=c.name.replace("[0]","["+g+"]");u(b,new f(v,r.id(v),e.getUniformLocation(h,v),c))}else u(b,new f(c.name,r.id(c.name),e.getUniformLocation(h,c.name),c));var y=e.getProgramParameter(h,mi);a.profile&&(t.stats.attributesCount=y);var x=t.attributes;for(o=0;o<y;++o)c=e.getActiveAttrib(h,o),c&&u(x,new f(c.name,r.id(c.name),e.getAttribLocation(h,c.name),c))}function m(){i={},o={};for(var e=0;e<l.length;++e)p(l[e],null,l[e].attributes.map((function(e){return[e.location,e.name]})))}return a.profile&&(n.getMaxUniformsCount=function(){var e=0;return l.forEach((function(t){t.stats.uniformsCount>e&&(e=t.stats.uniformsCount)})),e},n.getMaxAttributesCount=function(){var e=0;return l.forEach((function(t){t.stats.attributesCount>e&&(e=t.stats.attributesCount)})),e}),{clear:function(){var t=e.deleteShader.bind(e);St(i).forEach(t),i={},St(o).forEach(t),o={},l.forEach((function(t){e.deleteProgram(t.program)})),l.length=0,c={},n.shaderCount=0},program:function(r,a,f,u){ne.command(r>=0,"missing vertex shader",f),ne.command(a>=0,"missing fragment shader",f);var s=c[a];s||(s=c[a]={});var d=s[r];if(d&&(d.refCount++,!u))return d;var m=new h(a,r);return n.shaderCount++,p(m,f,u),d||(s[r]=m),l.push(m),t(m,{destroy:function(){if(m.refCount--,m.refCount<=0){e.deleteProgram(m.program);var t=l.indexOf(m);l.splice(t,1),n.shaderCount--}s[m.vertId].refCount<=0&&(e.deleteShader(o[m.vertId]),delete o[m.vertId],delete c[m.fragId][m.vertId]),Object.keys(c[m.fragId]).length||(e.deleteShader(i[m.fragId]),delete i[m.fragId],delete c[m.fragId])}})},restore:m,shader:s,frag:-1,vert:-1}}var gi=6408,vi=5121,yi=3333,xi=5126;function wi(t,r,n,a,i,o,f){function u(u){var s;null===r.next?(ne(i.preserveDrawingBuffer,'you must create a webgl context with "preserveDrawingBuffer":true in order to read pixels from the drawing buffer'),s=vi):(ne(null!==r.next.colorAttachments[0].texture,"You cannot read from a renderbuffer"),s=r.next.colorAttachments[0].texture._texture.type,o.oes_texture_float?(ne(s===vi||s===xi,"Reading from a framebuffer is only allowed for the types 'uint8' and 'float'"),s===xi&&ne(f.readFloat,"Reading 'float' values is not permitted in your browser. For a fallback, please see: https://www.npmjs.com/package/glsl-read-float")):ne(s===vi,"Reading from a framebuffer is only allowed for the type 'uint8'"));var c=0,l=0,d=a.framebufferWidth,h=a.framebufferHeight,p=null;e(u)?p=u:u&&(ne.type(u,"object","invalid arguments to regl.read()"),c=0|u.x,l=0|u.y,ne(c>=0&&c<a.framebufferWidth,"invalid x offset for regl.read"),ne(l>=0&&l<a.framebufferHeight,"invalid y offset for regl.read"),d=0|(u.width||a.framebufferWidth-c),h=0|(u.height||a.framebufferHeight-l),p=u.data||null),p&&(s===vi?ne(p instanceof Uint8Array,"buffer must be 'Uint8Array' when reading from a framebuffer of type 'uint8'"):s===xi&&ne(p instanceof Float32Array,"buffer must be 'Float32Array' when reading from a framebuffer of type 'float'")),ne(d>0&&d+c<=a.framebufferWidth,"invalid width for read pixels"),ne(h>0&&h+l<=a.framebufferHeight,"invalid height for read pixels"),n();var m=d*h*4;return p||(s===vi?p=new Uint8Array(m):s===xi&&(p=p||new Float32Array(m))),ne.isTypedArray(p,"data buffer for regl.read() must be a typedarray"),ne(p.byteLength>=m,"data buffer for regl.read() too small"),t.pixelStorei(yi,4),t.readPixels(c,l,d,h,gi,s,p),p}function s(e){var t;return r.setFBO({framebuffer:e.framebuffer},(function(){t=u(e)})),t}function c(e){return e&&"framebuffer"in e?s(e):u(e)}return c}function _i(e){return Array.prototype.slice.call(e)}function Ai(e){return _i(e).join("")}function ki(){var e=0,r=[],n=[];function a(t){for(var a=0;a<n.length;++a)if(n[a]===t)return r[a];var i="g"+e++;return r.push(i),n.push(t),i}function i(){var r=[];function n(){r.push.apply(r,_i(arguments))}var a=[];function i(){var t="v"+e++;return a.push(t),arguments.length>0&&(r.push(t,"="),r.push.apply(r,_i(arguments)),r.push(";")),t}return t(n,{def:i,toString:function(){return Ai([a.length>0?"var "+a.join(",")+";":"",Ai(r)])}})}function o(){var e=i(),r=i(),n=e.toString,a=r.toString;function o(t,n){r(t,n,"=",e.def(t,n),";")}return t((function(){e.apply(e,_i(arguments))}),{def:e.def,entry:e,exit:r,save:o,set:function(t,r,n){o(t,r),e(t,r,"=",n,";")},toString:function(){return n()+a()}})}function f(){var e=Ai(arguments),r=o(),n=o(),a=r.toString,i=n.toString;return t(r,{then:function(){return r.apply(r,_i(arguments)),this},else:function(){return n.apply(n,_i(arguments)),this},toString:function(){var t=i();return t&&(t="else{"+t+"}"),Ai(["if(",e,"){",a(),"}",t])}})}var u=i(),s={};function c(e,r){var n=[];function a(){var e="a"+n.length;return n.push(e),e}r=r||0;for(var i=0;i<r;++i)a();var f=o(),u=f.toString,c=s[e]=t(f,{arg:a,toString:function(){return Ai(["function(",n.join(),"){",u(),"}"])}});return c}function l(){var e=['"use strict";',u,"return {"];Object.keys(s).forEach((function(t){e.push('"',t,'":',s[t].toString(),",")})),e.push("}");var t=Ai(e).replace(/;/g,";\n").replace(/}/g,"}\n").replace(/{/g,"{\n"),a=Function.apply(null,r.concat(t));return a.apply(null,n)}return{global:u,link:a,block:i,proc:c,scope:o,cond:f,compile:l}}var Si="xyzw".split(""),Ei=5121,Oi=1,Ti=2,Ci=0,ji=1,Di=2,Fi=3,Mi=4,Pi=5,zi=6,Ii="dither",Bi="blend.enable",Ri="blend.color",Li="blend.equation",Vi="blend.func",Ni="depth.enable",Hi="depth.func",Ui="depth.range",Wi="depth.mask",qi="colorMask",Gi="cull.enable",$i="cull.face",Ki="frontFace",Qi="lineWidth",Yi="polygonOffset.enable",Xi="polygonOffset.offset",Ji="sample.alpha",Zi="sample.enable",eo="sample.coverage",to="stencil.enable",ro="stencil.mask",no="stencil.func",ao="stencil.opFront",io="stencil.opBack",oo="scissor.enable",fo="scissor.box",uo="viewport",so="profile",co="framebuffer",lo="vert",ho="frag",po="elements",mo="primitive",bo="count",go="offset",vo="instances",yo="vao",xo="Width",wo="Height",_o=co+xo,Ao=co+wo,ko=uo+xo,So=uo+wo,Eo="drawingBuffer",Oo=Eo+xo,To=Eo+wo,Co=[Vi,Li,no,ao,io,eo,uo,fo,Xi],jo=34962,Do=34963,Fo=35632,Mo=35633,Po=3553,zo=34067,Io=2884,Bo=3042,Ro=3024,Lo=2960,Vo=2929,No=3089,Ho=32823,Uo=32926,Wo=32928,qo=5126,Go=35664,$o=35665,Ko=35666,Qo=5124,Yo=35667,Xo=35668,Jo=35669,Zo=35670,ef=35671,tf=35672,rf=35673,nf=35674,af=35675,of=35676,ff=35678,uf=35680,sf=4,cf=1028,lf=1029,df=2304,hf=2305,pf=32775,mf=32776,bf=519,gf=7680,vf=0,yf=1,xf=32774,wf=513,_f=36160,Af=36064,kf={0:0,1:1,zero:0,one:1,"src color":768,"one minus src color":769,"src alpha":770,"one minus src alpha":771,"dst color":774,"one minus dst color":775,"dst alpha":772,"one minus dst alpha":773,"constant color":32769,"one minus constant color":32770,"constant alpha":32771,"one minus constant alpha":32772,"src alpha saturate":776},Sf=["constant color, constant alpha","one minus constant color, constant alpha","constant color, one minus constant alpha","one minus constant color, one minus constant alpha","constant alpha, constant color","constant alpha, one minus constant color","one minus constant alpha, constant color","one minus constant alpha, one minus constant color"],Ef={never:512,less:513,"<":513,equal:514,"=":514,"==":514,"===":514,lequal:515,"<=":515,greater:516,">":516,notequal:517,"!=":517,"!==":517,gequal:518,">=":518,always:519},Of={0:0,zero:0,keep:7680,replace:7681,increment:7682,decrement:7683,"increment wrap":34055,"decrement wrap":34056,invert:5386},Tf={frag:Fo,vert:Mo},Cf={cw:df,ccw:hf};function jf(t){return Array.isArray(t)||e(t)||kt(t)}function Df(e){return e.sort((function(e,t){return e===uo?-1:t===uo?1:e<t?-1:1}))}function Ff(e,t,r,n){this.thisDep=e,this.contextDep=t,this.propDep=r,this.append=n}function Mf(e){return e&&!(e.thisDep||e.contextDep||e.propDep)}function Pf(e){return new Ff(!1,!1,!1,e)}function zf(e,t){var r=e.type;if(r===Ci){var n=e.data.length;return new Ff(!0,n>=1,n>=2,t)}if(r===Mi){var a=e.data;return new Ff(a.thisDep,a.contextDep,a.propDep,t)}if(r===Pi)return new Ff(!1,!1,!1,t);if(r===zi){for(var i=!1,o=!1,f=!1,u=0;u<e.data.length;++u){var s=e.data[u];if(s.type===ji)f=!0;else if(s.type===Di)o=!0;else if(s.type===Fi)i=!0;else if(s.type===Ci){i=!0;var c=s.data;c>=1&&(o=!0),c>=2&&(f=!0)}else s.type===Mi&&(i=i||s.data.thisDep,o=o||s.data.contextDep,f=f||s.data.propDep)}return new Ff(i,o,f,t)}return new Ff(r===Fi,r===Di,r===ji,t)}var If=new Ff(!1,!1,!1,(function(){}));function Bf(e,r,n,a,i,o,f,u,s,c,l,d,h,p,m){var b=c.Record,g={add:32774,subtract:32778,"reverse subtract":32779};n.ext_blend_minmax&&(g.min=pf,g.max=mf);var v=n.angle_instanced_arrays,y=n.webgl_draw_buffers,x={dirty:!0,profile:m.profile},w={},_=[],A={},k={};function S(e){return e.replace(".","_")}function E(e,t,r){var n=S(e);_.push(e),w[n]=x[n]=!!r,A[n]=t}function O(e,t,r){var n=S(e);_.push(e),Array.isArray(r)?(x[n]=r.slice(),w[n]=r.slice()):x[n]=w[n]=r,k[n]=t}E(Ii,Ro),E(Bi,Bo),O(Ri,"blendColor",[0,0,0,0]),O(Li,"blendEquationSeparate",[xf,xf]),O(Vi,"blendFuncSeparate",[yf,vf,yf,vf]),E(Ni,Vo,!0),O(Hi,"depthFunc",wf),O(Ui,"depthRange",[0,1]),O(Wi,"depthMask",!0),O(qi,qi,[!0,!0,!0,!0]),E(Gi,Io),O($i,"cullFace",lf),O(Ki,Ki,hf),O(Qi,Qi,1),E(Yi,Ho),O(Xi,"polygonOffset",[0,0]),E(Ji,Uo),E(Zi,Wo),O(eo,"sampleCoverage",[1,!1]),E(to,Lo),O(ro,"stencilMask",-1),O(no,"stencilFunc",[bf,0,-1]),O(ao,"stencilOpSeparate",[cf,gf,gf,gf]),O(io,"stencilOpSeparate",[lf,gf,gf,gf]),E(oo,No),O(fo,"scissor",[0,0,e.drawingBufferWidth,e.drawingBufferHeight]),O(uo,uo,[0,0,e.drawingBufferWidth,e.drawingBufferHeight]);var T={gl:e,context:h,strings:r,next:w,current:x,draw:d,elements:o,buffer:i,shader:l,attributes:c.state,vao:c,uniforms:s,framebuffer:u,extensions:n,timer:p,isBufferArgs:jf},C={primTypes:sr,compareFuncs:Ef,blendFuncs:kf,blendEquations:g,stencilOps:Of,glTypes:Ht,orientationType:Cf};ne.optional((function(){T.isArrayLike=Or})),y&&(C.backBuffer=[lf],C.drawBuffer=Oe(a.maxDrawbuffers,(function(e){return 0===e?[0]:Oe(e,(function(e){return Af+e}))})));var j=0;function D(){var e=ki(),t=e.link,n=e.global;e.id=j++,e.batchId="0";var a=t(T),i=e.shared={props:"a0"};Object.keys(T).forEach((function(e){i[e]=n.def(a,".",e)})),ne.optional((function(){e.CHECK=t(ne),e.commandStr=ne.guessCommand(),e.command=t(e.commandStr),e.assert=function(e,r,n){e("if(!(",r,"))",this.CHECK,".commandRaise(",t(n),",",this.command,");")},C.invalidBlendCombinations=Sf}));var o=e.next={},f=e.current={};Object.keys(k).forEach((function(e){Array.isArray(x[e])&&(o[e]=n.def(i.next,".",e),f[e]=n.def(i.current,".",e))}));var u=e.constants={};Object.keys(C).forEach((function(e){u[e]=n.def(JSON.stringify(C[e]))})),e.invoke=function(r,n){switch(n.type){case Ci:var a=["this",i.context,i.props,e.batchId];return r.def(t(n.data),".call(",a.slice(0,Math.max(n.data.length+1,4)),")");case ji:return r.def(i.props,n.data);case Di:return r.def(i.context,n.data);case Fi:return r.def("this",n.data);case Mi:return n.data.append(e,r),n.data.ref;case Pi:return n.data.toString();case zi:return n.data.map((function(t){return e.invoke(r,t)}))}},e.attribCache={};var s={};return e.scopeAttrib=function(e){var n=r.id(e);if(n in s)return s[n];var a=c.scope[n];a||(a=c.scope[n]=new b);var i=s[n]=t(a);return i},e}function F(e){var t,r=e.static,n=e.dynamic;if(so in r){var a=!!r[so];t=Pf((function(e,t){return a})),t.enable=a}else if(so in n){var i=n[so];t=zf(i,(function(e,t){return e.invoke(t,i)}))}return t}function M(e,t){var r=e.static,n=e.dynamic;if(co in r){var a=r[co];return a?(a=u.getFramebuffer(a),ne.command(a,"invalid framebuffer object"),Pf((function(e,t){var r=e.link(a),n=e.shared;t.set(n.framebuffer,".next",r);var i=n.context;return t.set(i,"."+_o,r+".width"),t.set(i,"."+Ao,r+".height"),r}))):Pf((function(e,t){var r=e.shared;t.set(r.framebuffer,".next","null");var n=r.context;return t.set(n,"."+_o,n+"."+Oo),t.set(n,"."+Ao,n+"."+To),"null"}))}if(co in n){var i=n[co];return zf(i,(function(e,t){var r=e.invoke(t,i),n=e.shared,a=n.framebuffer,o=t.def(a,".getFramebuffer(",r,")");ne.optional((function(){e.assert(t,"!"+r+"||"+o,"invalid framebuffer object")})),t.set(a,".next",o);var f=n.context;return t.set(f,"."+_o,o+"?"+o+".width:"+f+"."+Oo),t.set(f,"."+Ao,o+"?"+o+".height:"+f+"."+To),o}))}return null}function P(e,t,r){var n=e.static,a=e.dynamic;function i(e){if(e in n){var i=n[e];ne.commandType(i,"object","invalid "+e,r.commandStr);var o,f,u=!0,s=0|i.x,c=0|i.y;return"width"in i?(o=0|i.width,ne.command(o>=0,"invalid "+e,r.commandStr)):u=!1,"height"in i?(f=0|i.height,ne.command(f>=0,"invalid "+e,r.commandStr)):u=!1,new Ff(!u&&t&&t.thisDep,!u&&t&&t.contextDep,!u&&t&&t.propDep,(function(e,t){var r=e.shared.context,n=o;"width"in i||(n=t.def(r,".",_o,"-",s));var a=f;return"height"in i||(a=t.def(r,".",Ao,"-",c)),[s,c,n,a]}))}if(e in a){var l=a[e],d=zf(l,(function(t,r){var n=t.invoke(r,l);ne.optional((function(){t.assert(r,n+"&&typeof "+n+'==="object"',"invalid "+e)}));var a=t.shared.context,i=r.def(n,".x|0"),o=r.def(n,".y|0"),f=r.def('"width" in ',n,"?",n,".width|0:","(",a,".",_o,"-",i,")"),u=r.def('"height" in ',n,"?",n,".height|0:","(",a,".",Ao,"-",o,")");return ne.optional((function(){t.assert(r,f+">=0&&"+u+">=0","invalid "+e)})),[i,o,f,u]}));return t&&(d.thisDep=d.thisDep||t.thisDep,d.contextDep=d.contextDep||t.contextDep,d.propDep=d.propDep||t.propDep),d}return t?new Ff(t.thisDep,t.contextDep,t.propDep,(function(e,t){var r=e.shared.context;return[0,0,t.def(r,".",_o),t.def(r,".",Ao)]})):null}var o=i(uo);if(o){var f=o;o=new Ff(o.thisDep,o.contextDep,o.propDep,(function(e,t){var r=f.append(e,t),n=e.shared.context;return t.set(n,"."+ko,r[2]),t.set(n,"."+So,r[3]),r}))}return{viewport:o,scissor_box:i(fo)}}function z(e,t){var r=e.static,n="string"===typeof r[ho]&&"string"===typeof r[lo];if(n){if(Object.keys(t.dynamic).length>0)return null;var a=t.static,i=Object.keys(a);if(i.length>0&&"number"===typeof a[i[0]]){for(var o=[],f=0;f<i.length;++f)ne("number"===typeof a[i[f]],"must specify all vertex attribute locations when using vaos"),o.push([0|a[i[f]],i[f]]);return o}}return null}function I(e,t,n){var a=e.static,i=e.dynamic;function o(e){if(e in a){var t=r.id(a[e]);ne.optional((function(){l.shader(Tf[e],t,ne.guessCommand())}));var n=Pf((function(){return t}));return n.id=t,n}if(e in i){var o=i[e];return zf(o,(function(t,r){var n=t.invoke(r,o),a=r.def(t.shared.strings,".id(",n,")");return ne.optional((function(){r(t.shared.shader,".shader(",Tf[e],",",a,",",t.command,");")})),a}))}return null}var f,u=o(ho),s=o(lo),c=null;return Mf(u)&&Mf(s)?(c=l.program(s.id,u.id,null,n),f=Pf((function(e,t){return e.link(c)}))):f=new Ff(u&&u.thisDep||s&&s.thisDep,u&&u.contextDep||s&&s.contextDep,u&&u.propDep||s&&s.propDep,(function(e,t){var r,n,a=e.shared.shader;r=u?u.append(e,t):t.def(a,".",ho),n=s?s.append(e,t):t.def(a,".",lo);var i=a+".program("+n+","+r;return ne.optional((function(){i+=","+e.command})),t.def(i+")")})),{frag:u,vert:s,progVar:f,program:c}}function B(e,t){var r=e.static,n=e.dynamic;function a(){if(po in r){var e=r[po];jf(e)?e=o.getElements(o.create(e,!0)):e&&(e=o.getElements(e),ne.command(e,"invalid elements",t.commandStr));var a=Pf((function(t,r){if(e){var n=t.link(e);return t.ELEMENTS=n,n}return t.ELEMENTS=null,null}));return a.value=e,a}if(po in n){var i=n[po];return zf(i,(function(e,t){var r=e.shared,n=r.isBufferArgs,a=r.elements,o=e.invoke(t,i),f=t.def("null"),u=t.def(n,"(",o,")"),s=e.cond(u).then(f,"=",a,".createStream(",o,");").else(f,"=",a,".getElements(",o,");");return ne.optional((function(){e.assert(s.else,"!"+o+"||"+f,"invalid elements")})),t.entry(s),t.exit(e.cond(u).then(a,".destroyStream(",f,");")),e.ELEMENTS=f,f}))}return null}var i=a();function f(){if(mo in r){var e=r[mo];return ne.commandParameter(e,sr,"invalid primitve",t.commandStr),Pf((function(t,r){return sr[e]}))}if(mo in n){var a=n[mo];return zf(a,(function(e,t){var r=e.constants.primTypes,n=e.invoke(t,a);return ne.optional((function(){e.assert(t,n+" in "+r,"invalid primitive, must be one of "+Object.keys(sr))})),t.def(r,"[",n,"]")}))}return i?Mf(i)?i.value?Pf((function(e,t){return t.def(e.ELEMENTS,".primType")})):Pf((function(){return sf})):new Ff(i.thisDep,i.contextDep,i.propDep,(function(e,t){var r=e.ELEMENTS;return t.def(r,"?",r,".primType:",sf)})):null}function u(e,a){if(e in r){var o=0|r[e];return ne.command(!a||o>=0,"invalid "+e,t.commandStr),Pf((function(e,t){return a&&(e.OFFSET=o),o}))}if(e in n){var f=n[e];return zf(f,(function(t,r){var n=t.invoke(r,f);return a&&(t.OFFSET=n,ne.optional((function(){t.assert(r,n+">=0","invalid "+e)}))),n}))}return a&&i?Pf((function(e,t){return e.OFFSET="0",0})):null}var s=u(go,!0);function c(){if(bo in r){var e=0|r[bo];return ne.command("number"===typeof e&&e>=0,"invalid vertex count",t.commandStr),Pf((function(){return e}))}if(bo in n){var a=n[bo];return zf(a,(function(e,t){var r=e.invoke(t,a);return ne.optional((function(){e.assert(t,"typeof "+r+'==="number"&&'+r+">=0&&"+r+"===("+r+"|0)","invalid vertex count")})),r}))}if(i){if(Mf(i)){if(i)return s?new Ff(s.thisDep,s.contextDep,s.propDep,(function(e,t){var r=t.def(e.ELEMENTS,".vertCount-",e.OFFSET);return ne.optional((function(){e.assert(t,r+">=0","invalid vertex offset/element buffer too small")})),r})):Pf((function(e,t){return t.def(e.ELEMENTS,".vertCount")}));var o=Pf((function(){return-1}));return ne.optional((function(){o.MISSING=!0})),o}var f=new Ff(i.thisDep||s.thisDep,i.contextDep||s.contextDep,i.propDep||s.propDep,(function(e,t){var r=e.ELEMENTS;return e.OFFSET?t.def(r,"?",r,".vertCount-",e.OFFSET,":-1"):t.def(r,"?",r,".vertCount:-1")}));return ne.optional((function(){f.DYNAMIC=!0})),f}return null}return{elements:i,primitive:f(),count:c(),instances:u(vo,!1),offset:s}}function R(e,t){var r=e.static,n=e.dynamic,i={};return _.forEach((function(e){var o=S(e);function f(t,a){if(e in r){var f=t(r[e]);i[o]=Pf((function(){return f}))}else if(e in n){var u=n[e];i[o]=zf(u,(function(e,t){return a(e,t,e.invoke(t,u))}))}}switch(e){case Gi:case Bi:case Ii:case to:case Ni:case oo:case Yi:case Ji:case Zi:case Wi:return f((function(r){return ne.commandType(r,"boolean",e,t.commandStr),r}),(function(t,r,n){return ne.optional((function(){t.assert(r,"typeof "+n+'==="boolean"',"invalid flag "+e,t.commandStr)})),n}));case Hi:return f((function(r){return ne.commandParameter(r,Ef,"invalid "+e,t.commandStr),Ef[r]}),(function(t,r,n){var a=t.constants.compareFuncs;return ne.optional((function(){t.assert(r,n+" in "+a,"invalid "+e+", must be one of "+Object.keys(Ef))})),r.def(a,"[",n,"]")}));case Ui:return f((function(e){return ne.command(Or(e)&&2===e.length&&"number"===typeof e[0]&&"number"===typeof e[1]&&e[0]<=e[1],"depth range is 2d array",t.commandStr),e}),(function(e,t,r){ne.optional((function(){e.assert(t,e.shared.isArrayLike+"("+r+")&&"+r+".length===2&&typeof "+r+'[0]==="number"&&typeof '+r+'[1]==="number"&&'+r+"[0]<="+r+"[1]","depth range must be a 2d array")}));var n=t.def("+",r,"[0]"),a=t.def("+",r,"[1]");return[n,a]}));case Vi:return f((function(e){ne.commandType(e,"object","blend.func",t.commandStr);var r="srcRGB"in e?e.srcRGB:e.src,n="srcAlpha"in e?e.srcAlpha:e.src,a="dstRGB"in e?e.dstRGB:e.dst,i="dstAlpha"in e?e.dstAlpha:e.dst;return ne.commandParameter(r,kf,o+".srcRGB",t.commandStr),ne.commandParameter(n,kf,o+".srcAlpha",t.commandStr),ne.commandParameter(a,kf,o+".dstRGB",t.commandStr),ne.commandParameter(i,kf,o+".dstAlpha",t.commandStr),ne.command(-1===Sf.indexOf(r+", "+a),"unallowed blending combination (srcRGB, dstRGB) = ("+r+", "+a+")",t.commandStr),[kf[r],kf[a],kf[n],kf[i]]}),(function(t,r,n){var a=t.constants.blendFuncs;function i(i,o){var f=r.def('"',i,o,'" in ',n,"?",n,".",i,o,":",n,".",i);return ne.optional((function(){t.assert(r,f+" in "+a,"invalid "+e+"."+i+o+", must be one of "+Object.keys(kf))})),f}ne.optional((function(){t.assert(r,n+"&&typeof "+n+'==="object"',"invalid blend func, must be an object")}));var o=i("src","RGB"),f=i("dst","RGB");ne.optional((function(){var e=t.constants.invalidBlendCombinations;t.assert(r,e+".indexOf("+o+'+", "+'+f+") === -1 ","unallowed blending combination for (srcRGB, dstRGB)")}));var u=r.def(a,"[",o,"]"),s=r.def(a,"[",i("src","Alpha"),"]"),c=r.def(a,"[",f,"]"),l=r.def(a,"[",i("dst","Alpha"),"]");return[u,c,s,l]}));case Li:return f((function(r){return"string"===typeof r?(ne.commandParameter(r,g,"invalid "+e,t.commandStr),[g[r],g[r]]):"object"===typeof r?(ne.commandParameter(r.rgb,g,e+".rgb",t.commandStr),ne.commandParameter(r.alpha,g,e+".alpha",t.commandStr),[g[r.rgb],g[r.alpha]]):void ne.commandRaise("invalid blend.equation",t.commandStr)}),(function(t,r,n){var a=t.constants.blendEquations,i=r.def(),o=r.def(),f=t.cond("typeof ",n,'==="string"');return ne.optional((function(){function r(e,r,n){t.assert(e,n+" in "+a,"invalid "+r+", must be one of "+Object.keys(g))}r(f.then,e,n),t.assert(f.else,n+"&&typeof "+n+'==="object"',"invalid "+e),r(f.else,e+".rgb",n+".rgb"),r(f.else,e+".alpha",n+".alpha")})),f.then(i,"=",o,"=",a,"[",n,"];"),f.else(i,"=",a,"[",n,".rgb];",o,"=",a,"[",n,".alpha];"),r(f),[i,o]}));case Ri:return f((function(e){return ne.command(Or(e)&&4===e.length,"blend.color must be a 4d array",t.commandStr),Oe(4,(function(t){return+e[t]}))}),(function(e,t,r){return ne.optional((function(){e.assert(t,e.shared.isArrayLike+"("+r+")&&"+r+".length===4","blend.color must be a 4d array")})),Oe(4,(function(e){return t.def("+",r,"[",e,"]")}))}));case ro:return f((function(e){return ne.commandType(e,"number",o,t.commandStr),0|e}),(function(e,t,r){return ne.optional((function(){e.assert(t,"typeof "+r+'==="number"',"invalid stencil.mask")})),t.def(r,"|0")}));case no:return f((function(r){ne.commandType(r,"object",o,t.commandStr);var n=r.cmp||"keep",a=r.ref||0,i="mask"in r?r.mask:-1;return ne.commandParameter(n,Ef,e+".cmp",t.commandStr),ne.commandType(a,"number",e+".ref",t.commandStr),ne.commandType(i,"number",e+".mask",t.commandStr),[Ef[n],a,i]}),(function(e,t,r){var n=e.constants.compareFuncs;ne.optional((function(){function a(){e.assert(t,Array.prototype.join.call(arguments,""),"invalid stencil.func")}a(r+"&&typeof ",r,'==="object"'),a('!("cmp" in ',r,")||(",r,".cmp in ",n,")")}));var a=t.def('"cmp" in ',r,"?",n,"[",r,".cmp]",":",gf),i=t.def(r,".ref|0"),o=t.def('"mask" in ',r,"?",r,".mask|0:-1");return[a,i,o]}));case ao:case io:return f((function(r){ne.commandType(r,"object",o,t.commandStr);var n=r.fail||"keep",a=r.zfail||"keep",i=r.zpass||"keep";return ne.commandParameter(n,Of,e+".fail",t.commandStr),ne.commandParameter(a,Of,e+".zfail",t.commandStr),ne.commandParameter(i,Of,e+".zpass",t.commandStr),[e===io?lf:cf,Of[n],Of[a],Of[i]]}),(function(t,r,n){var a=t.constants.stencilOps;function i(i){return ne.optional((function(){t.assert(r,'!("'+i+'" in '+n+")||("+n+"."+i+" in "+a+")","invalid "+e+"."+i+", must be one of "+Object.keys(Of))})),r.def('"',i,'" in ',n,"?",a,"[",n,".",i,"]:",gf)}return ne.optional((function(){t.assert(r,n+"&&typeof "+n+'==="object"',"invalid "+e)})),[e===io?lf:cf,i("fail"),i("zfail"),i("zpass")]}));case Xi:return f((function(e){ne.commandType(e,"object",o,t.commandStr);var r=0|e.factor,n=0|e.units;return ne.commandType(r,"number",o+".factor",t.commandStr),ne.commandType(n,"number",o+".units",t.commandStr),[r,n]}),(function(t,r,n){ne.optional((function(){t.assert(r,n+"&&typeof "+n+'==="object"',"invalid "+e)}));var a=r.def(n,".factor|0"),i=r.def(n,".units|0");return[a,i]}));case $i:return f((function(e){var r=0;return"front"===e?r=cf:"back"===e&&(r=lf),ne.command(!!r,o,t.commandStr),r}),(function(e,t,r){return ne.optional((function(){e.assert(t,r+'==="front"||'+r+'==="back"',"invalid cull.face")})),t.def(r,'==="front"?',cf,":",lf)}));case Qi:return f((function(e){return ne.command("number"===typeof e&&e>=a.lineWidthDims[0]&&e<=a.lineWidthDims[1],"invalid line width, must be a positive number between "+a.lineWidthDims[0]+" and "+a.lineWidthDims[1],t.commandStr),e}),(function(e,t,r){return ne.optional((function(){e.assert(t,"typeof "+r+'==="number"&&'+r+">="+a.lineWidthDims[0]+"&&"+r+"<="+a.lineWidthDims[1],"invalid line width")})),r}));case Ki:return f((function(e){return ne.commandParameter(e,Cf,o,t.commandStr),Cf[e]}),(function(e,t,r){return ne.optional((function(){e.assert(t,r+'==="cw"||'+r+'==="ccw"',"invalid frontFace, must be one of cw,ccw")})),t.def(r+'==="cw"?'+df+":"+hf)}));case qi:return f((function(e){return ne.command(Or(e)&&4===e.length,"color.mask must be length 4 array",t.commandStr),e.map((function(e){return!!e}))}),(function(e,t,r){return ne.optional((function(){e.assert(t,e.shared.isArrayLike+"("+r+")&&"+r+".length===4","invalid color.mask")})),Oe(4,(function(e){return"!!"+r+"["+e+"]"}))}));case eo:return f((function(e){ne.command("object"===typeof e&&e,o,t.commandStr);var r="value"in e?e.value:1,n=!!e.invert;return ne.command("number"===typeof r&&r>=0&&r<=1,"sample.coverage.value must be a number between 0 and 1",t.commandStr),[r,n]}),(function(e,t,r){ne.optional((function(){e.assert(t,r+"&&typeof "+r+'==="object"',"invalid sample.coverage")}));var n=t.def('"value" in ',r,"?+",r,".value:1"),a=t.def("!!",r,".invert");return[n,a]}))}})),i}function L(e,t){var r=e.static,n=e.dynamic,a={};return Object.keys(r).forEach((function(e){var n,i=r[e];if("number"===typeof i||"boolean"===typeof i)n=Pf((function(){return i}));else if("function"===typeof i){var o=i._reglType;"texture2d"===o||"textureCube"===o?n=Pf((function(e){return e.link(i)})):"framebuffer"===o||"framebufferCube"===o?(ne.command(i.color.length>0,'missing color attachment for framebuffer sent to uniform "'+e+'"',t.commandStr),n=Pf((function(e){return e.link(i.color[0])}))):ne.commandRaise('invalid data for uniform "'+e+'"',t.commandStr)}else Or(i)?n=Pf((function(t){var r=t.global.def("[",Oe(i.length,(function(r){return ne.command("number"===typeof i[r]||"boolean"===typeof i[r],"invalid uniform "+e,t.commandStr),i[r]})),"]");return r})):ne.commandRaise('invalid or missing data for uniform "'+e+'"',t.commandStr);n.value=i,a[e]=n})),Object.keys(n).forEach((function(e){var t=n[e];a[e]=zf(t,(function(e,r){return e.invoke(r,t)}))})),a}function V(e,t){var n=e.static,a=e.dynamic,o={};return Object.keys(n).forEach((function(e){var a=n[e],f=r.id(e),u=new b;if(jf(a))u.state=Oi,u.buffer=i.getBuffer(i.create(a,jo,!1,!0)),u.type=0;else{var s=i.getBuffer(a);if(s)u.state=Oi,u.buffer=s,u.type=0;else if(ne.command("object"===typeof a&&a,"invalid data for attribute "+e,t.commandStr),"constant"in a){var c=a.constant;u.buffer="null",u.state=Ti,"number"===typeof c?u.x=c:(ne.command(Or(c)&&c.length>0&&c.length<=4,"invalid constant for attribute "+e,t.commandStr),Si.forEach((function(e,t){t<c.length&&(u[e]=c[t])})))}else{s=jf(a.buffer)?i.getBuffer(i.create(a.buffer,jo,!1,!0)):i.getBuffer(a.buffer),ne.command(!!s,'missing buffer for attribute "'+e+'"',t.commandStr);var l=0|a.offset;ne.command(l>=0,'invalid offset for attribute "'+e+'"',t.commandStr);var d=0|a.stride;ne.command(d>=0&&d<256,'invalid stride for attribute "'+e+'", must be integer betweeen [0, 255]',t.commandStr);var h=0|a.size;ne.command(!("size"in a)||h>0&&h<=4,'invalid size for attribute "'+e+'", must be 1,2,3,4',t.commandStr);var p=!!a.normalized,m=0;"type"in a&&(ne.commandParameter(a.type,Ht,"invalid type for attribute "+e,t.commandStr),m=Ht[a.type]);var g=0|a.divisor;"divisor"in a&&(ne.command(0===g||v,'cannot specify divisor for attribute "'+e+'", instancing not supported',t.commandStr),ne.command(g>=0,'invalid divisor for attribute "'+e+'"',t.commandStr)),ne.optional((function(){var r=t.commandStr,n=["buffer","offset","divisor","normalized","type","size","stride"];Object.keys(a).forEach((function(t){ne.command(n.indexOf(t)>=0,'unknown parameter "'+t+'" for attribute pointer "'+e+'" (valid parameters are '+n+")",r)}))})),u.buffer=s,u.state=Oi,u.size=h,u.normalized=p,u.type=m||s.dtype,u.offset=l,u.stride=d,u.divisor=g}}o[e]=Pf((function(e,t){var r=e.attribCache;if(f in r)return r[f];var n={isStream:!1};return Object.keys(u).forEach((function(e){n[e]=u[e]})),u.buffer&&(n.buffer=e.link(u.buffer),n.type=n.type||n.buffer+".dtype"),r[f]=n,n}))})),Object.keys(a).forEach((function(e){var t=a[e];function r(r,n){var a=r.invoke(n,t),i=r.shared,o=r.constants,f=i.isBufferArgs,u=i.buffer;ne.optional((function(){r.assert(n,a+"&&(typeof "+a+'==="object"||typeof '+a+'==="function")&&('+f+"("+a+")||"+u+".getBuffer("+a+")||"+u+".getBuffer("+a+".buffer)||"+f+"("+a+'.buffer)||("constant" in '+a+"&&(typeof "+a+'.constant==="number"||'+i.isArrayLike+"("+a+".constant))))",'invalid dynamic attribute "'+e+'"')}));var s={isStream:n.def(!1)},c=new b;c.state=Oi,Object.keys(c).forEach((function(e){s[e]=n.def(""+c[e])}));var l=s.buffer,d=s.type;function h(e){n(s[e],"=",a,".",e,"|0;")}return n("if(",f,"(",a,")){",s.isStream,"=true;",l,"=",u,".createStream(",jo,",",a,");",d,"=",l,".dtype;","}else{",l,"=",u,".getBuffer(",a,");","if(",l,"){",d,"=",l,".dtype;",'}else if("constant" in ',a,"){",s.state,"=",Ti,";","if(typeof "+a+'.constant === "number"){',s[Si[0]],"=",a,".constant;",Si.slice(1).map((function(e){return s[e]})).join("="),"=0;","}else{",Si.map((function(e,t){return s[e]+"="+a+".constant.length>"+t+"?"+a+".constant["+t+"]:0;"})).join(""),"}}else{","if(",f,"(",a,".buffer)){",l,"=",u,".createStream(",jo,",",a,".buffer);","}else{",l,"=",u,".getBuffer(",a,".buffer);","}",d,'="type" in ',a,"?",o.glTypes,"[",a,".type]:",l,".dtype;",s.normalized,"=!!",a,".normalized;"),h("size"),h("offset"),h("stride"),h("divisor"),n("}}"),n.exit("if(",s.isStream,"){",u,".destroyStream(",l,");","}"),s}o[e]=zf(t,r)})),o}function N(e,t){var r=e.static,n=e.dynamic;if(yo in r){var a=r[yo];return null!==a&&null===c.getVAO(a)&&(a=c.createVAO(a)),Pf((function(e){return e.link(c.getVAO(a))}))}if(yo in n){var i=n[yo];return zf(i,(function(e,t){var r=e.invoke(t,i);return t.def(e.shared.vao+".getVAO("+r+")")}))}return null}function H(e){var t=e.static,r=e.dynamic,n={};return Object.keys(t).forEach((function(e){var r=t[e];n[e]=Pf((function(e,t){return"number"===typeof r||"boolean"===typeof r?""+r:e.link(r)}))})),Object.keys(r).forEach((function(e){var t=r[e];n[e]=zf(t,(function(e,r){return e.invoke(r,t)}))})),n}function U(e,t,r,a,i){var o=e.static,f=e.dynamic;ne.optional((function(){var e=[co,lo,ho,po,mo,go,bo,vo,so,yo].concat(_);function t(t){Object.keys(t).forEach((function(t){ne.command(e.indexOf(t)>=0,'unknown parameter "'+t+'"',i.commandStr)}))}t(o),t(f)}));var u=z(e,t),s=M(e,i),l=P(e,s,i),d=B(e,i),h=R(e,i),p=I(e,i,u);function m(e){var t=l[e];t&&(h[e]=t)}m(uo),m(S(fo));var b=Object.keys(h).length>0,g={framebuffer:s,draw:d,shader:p,state:h,dirty:b,scopeVAO:null,drawVAO:null,useVAO:!1,attributes:{}};if(g.profile=F(e,i),g.uniforms=L(r,i),g.drawVAO=g.scopeVAO=N(e,i),!g.drawVAO&&p.program&&!u&&n.angle_instanced_arrays){var v=!0,y=p.program.attributes.map((function(e){var r=t.static[e];return v=v&&!!r,r}));if(v&&y.length>0){var x=c.getVAO(c.createVAO(y));g.drawVAO=new Ff(null,null,null,(function(e,t){return e.link(x)})),g.useVAO=!0}}return u?g.useVAO=!0:g.attributes=V(t,i),g.context=H(a,i),g}function W(e,t,r){var n=e.shared,a=n.context,i=e.scope();Object.keys(r).forEach((function(n){t.save(a,"."+n);var o=r[n],f=o.append(e,t);Array.isArray(f)?i(a,".",n,"=[",f.join(),"];"):i(a,".",n,"=",f,";")})),t(i)}function q(e,t,r,n){var a,i=e.shared,o=i.gl,f=i.framebuffer;y&&(a=t.def(i.extensions,".webgl_draw_buffers"));var u,s=e.constants,c=s.drawBuffer,l=s.backBuffer;u=r?r.append(e,t):t.def(f,".next"),n||t("if(",u,"!==",f,".cur){"),t("if(",u,"){",o,".bindFramebuffer(",_f,",",u,".framebuffer);"),y&&t(a,".drawBuffersWEBGL(",c,"[",u,".colorAttachments.length]);"),t("}else{",o,".bindFramebuffer(",_f,",null);"),y&&t(a,".drawBuffersWEBGL(",l,");"),t("}",f,".cur=",u,";"),n||t("}")}function G(e,t,r){var n=e.shared,a=n.gl,i=e.current,o=e.next,f=n.current,u=n.next,s=e.cond(f,".dirty");_.forEach((function(t){var n,c,l=S(t);if(!(l in r.state))if(l in o){n=o[l],c=i[l];var d=Oe(x[l].length,(function(e){return s.def(n,"[",e,"]")}));s(e.cond(d.map((function(e,t){return e+"!=="+c+"["+t+"]"})).join("||")).then(a,".",k[l],"(",d,");",d.map((function(e,t){return c+"["+t+"]="+e})).join(";"),";"))}else{n=s.def(u,".",l);var h=e.cond(n,"!==",f,".",l);s(h),l in A?h(e.cond(n).then(a,".enable(",A[l],");").else(a,".disable(",A[l],");"),f,".",l,"=",n,";"):h(a,".",k[l],"(",n,");",f,".",l,"=",n,";")}})),0===Object.keys(r.state).length&&s(f,".dirty=false;"),t(s)}function $(e,t,r,n){var a=e.shared,i=e.current,o=a.current,f=a.gl;Df(Object.keys(r)).forEach((function(a){var u=r[a];if(!n||n(u)){var s=u.append(e,t);if(A[a]){var c=A[a];Mf(u)?t(f,s?".enable(":".disable(",c,");"):t(e.cond(s).then(f,".enable(",c,");").else(f,".disable(",c,");")),t(o,".",a,"=",s,";")}else if(Or(s)){var l=i[a];t(f,".",k[a],"(",s,");",s.map((function(e,t){return l+"["+t+"]="+e})).join(";"),";")}else t(f,".",k[a],"(",s,");",o,".",a,"=",s,";")}}))}function K(e,t){v&&(e.instancing=t.def(e.shared.extensions,".angle_instanced_arrays"))}function Q(e,t,r,n,a){var i,o,f,u=e.shared,s=e.stats,c=u.current,l=u.timer,d=r.profile;function h(){return"undefined"===typeof performance?"Date.now()":"performance.now()"}function m(e){i=t.def(),e(i,"=",h(),";"),"string"===typeof a?e(s,".count+=",a,";"):e(s,".count++;"),p&&(n?(o=t.def(),e(o,"=",l,".getNumPendingQueries();")):e(l,".beginQuery(",s,");"))}function b(e){e(s,".cpuTime+=",h(),"-",i,";"),p&&(n?e(l,".pushScopeStats(",o,",",l,".getNumPendingQueries(),",s,");"):e(l,".endQuery();"))}function g(e){var r=t.def(c,".profile");t(c,".profile=",e,";"),t.exit(c,".profile=",r,";")}if(d){if(Mf(d))return void(d.enable?(m(t),b(t.exit),g("true")):g("false"));f=d.append(e,t),g(f)}else f=t.def(c,".profile");var v=e.block();m(v),t("if(",f,"){",v,"}");var y=e.block();b(y),t.exit("if(",f,"){",y,"}")}function Y(e,t,r,n,a){var i=e.shared;function o(e){switch(e){case Go:case Yo:case ef:return 2;case $o:case Xo:case tf:return 3;case Ko:case Jo:case rf:return 4;default:return 1}}function f(r,n,a){var o=i.gl,f=t.def(r,".location"),u=t.def(i.attributes,"[",f,"]"),s=a.state,c=a.buffer,l=[a.x,a.y,a.z,a.w],d=["buffer","normalized","offset","stride"];function h(){t("if(!",u,".buffer){",o,".enableVertexAttribArray(",f,");}");var r,i=a.type;if(r=a.size?t.def(a.size,"||",n):n,t("if(",u,".type!==",i,"||",u,".size!==",r,"||",d.map((function(e){return u+"."+e+"!=="+a[e]})).join("||"),"){",o,".bindBuffer(",jo,",",c,".buffer);",o,".vertexAttribPointer(",[f,r,i,a.normalized,a.stride,a.offset],");",u,".type=",i,";",u,".size=",r,";",d.map((function(e){return u+"."+e+"="+a[e]+";"})).join(""),"}"),v){var s=a.divisor;t("if(",u,".divisor!==",s,"){",e.instancing,".vertexAttribDivisorANGLE(",[f,s],");",u,".divisor=",s,";}")}}function p(){t("if(",u,".buffer){",o,".disableVertexAttribArray(",f,");",u,".buffer=null;","}if(",Si.map((function(e,t){return u+"."+e+"!=="+l[t]})).join("||"),"){",o,".vertexAttrib4f(",f,",",l,");",Si.map((function(e,t){return u+"."+e+"="+l[t]+";"})).join(""),"}")}s===Oi?h():s===Ti?p():(t("if(",s,"===",Oi,"){"),h(),t("}else{"),p(),t("}"))}n.forEach((function(n){var i,u=n.name,s=r.attributes[u];if(s){if(!a(s))return;i=s.append(e,t)}else{if(!a(If))return;var c=e.scopeAttrib(u);ne.optional((function(){e.assert(t,c+".state","missing attribute "+u)})),i={},Object.keys(new b).forEach((function(e){i[e]=t.def(c,".",e)}))}f(e.link(n),o(n.info.type),i)}))}function X(e,t,n,a,i){for(var o,f=e.shared,u=f.gl,s=0;s<a.length;++s){var c,l=a[s],d=l.name,h=l.info.type,p=n.uniforms[d],m=e.link(l),b=m+".location";if(p){if(!i(p))continue;if(Mf(p)){var g=p.value;if(ne.command(null!==g&&"undefined"!==typeof g,'missing uniform "'+d+'"',e.commandStr),h===ff||h===uf){ne.command("function"===typeof g&&(h===ff&&("texture2d"===g._reglType||"framebuffer"===g._reglType)||h===uf&&("textureCube"===g._reglType||"framebufferCube"===g._reglType)),"invalid texture for uniform "+d,e.commandStr);var v=e.link(g._texture||g.color[0]._texture);t(u,".uniform1i(",b,",",v+".bind());"),t.exit(v,".unbind();")}else if(h===nf||h===af||h===of){ne.optional((function(){ne.command(Or(g),"invalid matrix for uniform "+d,e.commandStr),ne.command(h===nf&&4===g.length||h===af&&9===g.length||h===of&&16===g.length,"invalid length for matrix uniform "+d,e.commandStr)}));var y=e.global.def("new Float32Array(["+Array.prototype.slice.call(g)+"])"),x=2;h===af?x=3:h===of&&(x=4),t(u,".uniformMatrix",x,"fv(",b,",false,",y,");")}else{switch(h){case qo:ne.commandType(g,"number","uniform "+d,e.commandStr),o="1f";break;case Go:ne.command(Or(g)&&2===g.length,"uniform "+d,e.commandStr),o="2f";break;case $o:ne.command(Or(g)&&3===g.length,"uniform "+d,e.commandStr),o="3f";break;case Ko:ne.command(Or(g)&&4===g.length,"uniform "+d,e.commandStr),o="4f";break;case Zo:ne.commandType(g,"boolean","uniform "+d,e.commandStr),o="1i";break;case Qo:ne.commandType(g,"number","uniform "+d,e.commandStr),o="1i";break;case ef:ne.command(Or(g)&&2===g.length,"uniform "+d,e.commandStr),o="2i";break;case Yo:ne.command(Or(g)&&2===g.length,"uniform "+d,e.commandStr),o="2i";break;case tf:ne.command(Or(g)&&3===g.length,"uniform "+d,e.commandStr),o="3i";break;case Xo:ne.command(Or(g)&&3===g.length,"uniform "+d,e.commandStr),o="3i";break;case rf:ne.command(Or(g)&&4===g.length,"uniform "+d,e.commandStr),o="4i";break;case Jo:ne.command(Or(g)&&4===g.length,"uniform "+d,e.commandStr),o="4i";break}t(u,".uniform",o,"(",b,",",Or(g)?Array.prototype.slice.call(g):g,");")}continue}c=p.append(e,t)}else{if(!i(If))continue;c=t.def(f.uniforms,"[",r.id(d),"]")}h===ff?(ne(!Array.isArray(c),"must specify a scalar prop for textures"),t("if(",c,"&&",c,'._reglType==="framebuffer"){',c,"=",c,".color[0];","}")):h===uf&&(ne(!Array.isArray(c),"must specify a scalar prop for cube maps"),t("if(",c,"&&",c,'._reglType==="framebufferCube"){',c,"=",c,".color[0];","}")),ne.optional((function(){function r(r,n){e.assert(t,r,'bad data or missing for uniform "'+d+'".  '+n)}function n(e){ne(!Array.isArray(c),"must not specify an array type for uniform"),r("typeof "+c+'==="'+e+'"',"invalid type, expected "+e)}function a(t,n){Array.isArray(c)?ne(c.length===t,"must have length "+t):r(f.isArrayLike+"("+c+")&&"+c+".length==="+t,"invalid vector, should have length "+t,e.commandStr)}function i(t){ne(!Array.isArray(c),"must not specify a value type"),r("typeof "+c+'==="function"&&'+c+'._reglType==="texture'+(t===Po?"2d":"Cube")+'"',"invalid texture type",e.commandStr)}switch(h){case Qo:n("number");break;case Yo:a(2,"number");break;case Xo:a(3,"number");break;case Jo:a(4,"number");break;case qo:n("number");break;case Go:a(2,"number");break;case $o:a(3,"number");break;case Ko:a(4,"number");break;case Zo:n("boolean");break;case ef:a(2,"boolean");break;case tf:a(3,"boolean");break;case rf:a(4,"boolean");break;case nf:a(4,"number");break;case af:a(9,"number");break;case of:a(16,"number");break;case ff:i(Po);break;case uf:i(zo);break}}));var w=1;switch(h){case ff:case uf:var _=t.def(c,"._texture");t(u,".uniform1i(",b,",",_,".bind());"),t.exit(_,".unbind();");continue;case Qo:case Zo:o="1i";break;case Yo:case ef:o="2i",w=2;break;case Xo:case tf:o="3i",w=3;break;case Jo:case rf:o="4i",w=4;break;case qo:o="1f";break;case Go:o="2f",w=2;break;case $o:o="3f",w=3;break;case Ko:o="4f",w=4;break;case nf:o="Matrix2fv";break;case af:o="Matrix3fv";break;case of:o="Matrix4fv";break}if(t(u,".uniform",o,"(",b,","),"M"===o.charAt(0)){var A=Math.pow(h-nf+2,2),k=e.global.def("new Float32Array(",A,")");Array.isArray(c)?t("false,(",Oe(A,(function(e){return k+"["+e+"]="+c[e]})),",",k,")"):t("false,(Array.isArray(",c,")||",c," instanceof Float32Array)?",c,":(",Oe(A,(function(e){return k+"["+e+"]="+c+"["+e+"]"})),",",k,")")}else w>1?t(Oe(w,(function(e){return Array.isArray(c)?c[e]:c+"["+e+"]"}))):(ne(!Array.isArray(c),"uniform value must not be an array"),t(c));t(");")}}function J(e,t,r,n){var a=e.shared,i=a.gl,o=a.draw,f=n.draw;function u(){var a,u=f.elements,s=t;return u?((u.contextDep&&n.contextDynamic||u.propDep)&&(s=r),a=u.append(e,s)):a=s.def(o,".",po),a&&s("if("+a+")"+i+".bindBuffer("+Do+","+a+".buffer.buffer);"),a}function s(){var a,i=f.count,u=t;return i?((i.contextDep&&n.contextDynamic||i.propDep)&&(u=r),a=i.append(e,u),ne.optional((function(){i.MISSING&&e.assert(t,"false","missing vertex count"),i.DYNAMIC&&e.assert(u,a+">=0","missing vertex count")}))):(a=u.def(o,".",bo),ne.optional((function(){e.assert(u,a+">=0","missing vertex count")}))),a}var c=u();function l(a){var i=f[a];return i?i.contextDep&&n.contextDynamic||i.propDep?i.append(e,r):i.append(e,t):t.def(o,".",a)}var d,h,p=l(mo),m=l(go),b=s();if("number"===typeof b){if(0===b)return}else r("if(",b,"){"),r.exit("}");v&&(d=l(vo),h=e.instancing);var g=c+".type",y=f.elements&&Mf(f.elements);function x(){function e(){r(h,".drawElementsInstancedANGLE(",[p,b,g,m+"<<(("+g+"-"+Ei+")>>1)",d],");")}function t(){r(h,".drawArraysInstancedANGLE(",[p,m,b,d],");")}c?y?e():(r("if(",c,"){"),e(),r("}else{"),t(),r("}")):t()}function w(){function e(){r(i+".drawElements("+[p,b,g,m+"<<(("+g+"-"+Ei+")>>1)"]+");")}function t(){r(i+".drawArrays("+[p,m,b]+");")}c?y?e():(r("if(",c,"){"),e(),r("}else{"),t(),r("}")):t()}v&&("number"!==typeof d||d>=0)?"string"===typeof d?(r("if(",d,">0){"),x(),r("}else if(",d,"<0){"),w(),r("}")):x():w()}function Z(e,t,r,n,a){var i=D(),o=i.proc("body",a);return ne.optional((function(){i.commandStr=t.commandStr,i.command=i.link(t.commandStr)})),v&&(i.instancing=o.def(i.shared.extensions,".angle_instanced_arrays")),e(i,o,r,n),i.compile().body}function ee(e,t,r,n){K(e,t),r.useVAO?r.drawVAO?t(e.shared.vao,".setVAO(",r.drawVAO.append(e,t),");"):t(e.shared.vao,".setVAO(",e.shared.vao,".targetVAO);"):(t(e.shared.vao,".setVAO(null);"),Y(e,t,r,n.attributes,(function(){return!0}))),X(e,t,r,n.uniforms,(function(){return!0})),J(e,t,t,r)}function te(e,t){var r=e.proc("draw",1);K(e,r),W(e,r,t.context),q(e,r,t.framebuffer),G(e,r,t),$(e,r,t.state),Q(e,r,t,!1,!0);var n=t.shader.progVar.append(e,r);if(r(e.shared.gl,".useProgram(",n,".program);"),t.shader.program)ee(e,r,t,t.shader.program);else{r(e.shared.vao,".setVAO(null);");var a=e.global.def("{}"),i=r.def(n,".id"),o=r.def(a,"[",i,"]");r(e.cond(o).then(o,".call(this,a0);").else(o,"=",a,"[",i,"]=",e.link((function(r){return Z(ee,e,t,r,1)})),"(",n,");",o,".call(this,a0);"))}Object.keys(t.state).length>0&&r(e.shared.current,".dirty=true;")}function re(e,t,r,n){function a(){return!0}e.batchId="a1",K(e,t),Y(e,t,r,n.attributes,a),X(e,t,r,n.uniforms,a),J(e,t,t,r)}function ae(e,t,r,n){K(e,t);var a=r.contextDep,i=t.def(),o="a0",f="a1",u=t.def();e.shared.props=u,e.batchId=i;var s=e.scope(),c=e.scope();function l(e){return e.contextDep&&a||e.propDep}function d(e){return!l(e)}if(t(s.entry,"for(",i,"=0;",i,"<",f,";++",i,"){",u,"=",o,"[",i,"];",c,"}",s.exit),r.needsContext&&W(e,c,r.context),r.needsFramebuffer&&q(e,c,r.framebuffer),$(e,c,r.state,l),r.profile&&l(r.profile)&&Q(e,c,r,!1,!0),n)r.useVAO?r.drawVAO?l(r.drawVAO)?c(e.shared.vao,".setVAO(",r.drawVAO.append(e,c),");"):s(e.shared.vao,".setVAO(",r.drawVAO.append(e,s),");"):s(e.shared.vao,".setVAO(",e.shared.vao,".targetVAO);"):(s(e.shared.vao,".setVAO(null);"),Y(e,s,r,n.attributes,d),Y(e,c,r,n.attributes,l)),X(e,s,r,n.uniforms,d),X(e,c,r,n.uniforms,l),J(e,s,c,r);else{var h=e.global.def("{}"),p=r.shader.progVar.append(e,c),m=c.def(p,".id"),b=c.def(h,"[",m,"]");c(e.shared.gl,".useProgram(",p,".program);","if(!",b,"){",b,"=",h,"[",m,"]=",e.link((function(t){return Z(re,e,r,t,2)})),"(",p,");}",b,".call(this,a0[",i,"],",i,");")}}function ie(e,t){var r=e.proc("batch",2);e.batchId="0",K(e,r);var n=!1,a=!0;Object.keys(t.context).forEach((function(e){n=n||t.context[e].propDep})),n||(W(e,r,t.context),a=!1);var i=t.framebuffer,o=!1;function f(e){return e.contextDep&&n||e.propDep}i?(i.propDep?n=o=!0:i.contextDep&&n&&(o=!0),o||q(e,r,i)):q(e,r,null),t.state.viewport&&t.state.viewport.propDep&&(n=!0),G(e,r,t),$(e,r,t.state,(function(e){return!f(e)})),t.profile&&f(t.profile)||Q(e,r,t,!1,"a1"),t.contextDep=n,t.needsContext=a,t.needsFramebuffer=o;var u=t.shader.progVar;if(u.contextDep&&n||u.propDep)ae(e,r,t,null);else{var s=u.append(e,r);if(r(e.shared.gl,".useProgram(",s,".program);"),t.shader.program)ae(e,r,t,t.shader.program);else{r(e.shared.vao,".setVAO(null);");var c=e.global.def("{}"),l=r.def(s,".id"),d=r.def(c,"[",l,"]");r(e.cond(d).then(d,".call(this,a0,a1);").else(d,"=",c,"[",l,"]=",e.link((function(r){return Z(ae,e,t,r,2)})),"(",s,");",d,".call(this,a0,a1);"))}}Object.keys(t.state).length>0&&r(e.shared.current,".dirty=true;")}function oe(e,t){var n=e.proc("scope",3);e.batchId="a2";var a=e.shared,i=a.current;function o(r){var i=t.shader[r];i&&n.set(a.shader,"."+r,i.append(e,n))}W(e,n,t.context),t.framebuffer&&t.framebuffer.append(e,n),Df(Object.keys(t.state)).forEach((function(r){var i=t.state[r],o=i.append(e,n);Or(o)?o.forEach((function(t,a){n.set(e.next[r],"["+a+"]",t)})):n.set(a.next,"."+r,o)})),Q(e,n,t,!0,!0),[po,go,bo,vo,mo].forEach((function(r){var i=t.draw[r];i&&n.set(a.draw,"."+r,""+i.append(e,n))})),Object.keys(t.uniforms).forEach((function(i){var o=t.uniforms[i].append(e,n);Array.isArray(o)&&(o="["+o.join()+"]"),n.set(a.uniforms,"["+r.id(i)+"]",o)})),Object.keys(t.attributes).forEach((function(r){var a=t.attributes[r].append(e,n),i=e.scopeAttrib(r);Object.keys(new b).forEach((function(e){n.set(i,"."+e,a[e])}))})),t.scopeVAO&&n.set(a.vao,".targetVAO",t.scopeVAO.append(e,n)),o(lo),o(ho),Object.keys(t.state).length>0&&(n(i,".dirty=true;"),n.exit(i,".dirty=true;")),n("a1(",e.shared.context,",a0,",e.batchId,");")}function fe(e){if("object"===typeof e&&!Or(e)){for(var t=Object.keys(e),r=0;r<t.length;++r)if(me.isDynamic(e[t[r]]))return!0;return!1}}function ue(e,t,r){var n=t.static[r];if(n&&fe(n)){var a=e.global,i=Object.keys(n),o=!1,f=!1,u=!1,s=e.global.def("{}");i.forEach((function(t){var r=n[t];if(me.isDynamic(r)){"function"===typeof r&&(r=n[t]=me.unbox(r));var i=zf(r,null);o=o||i.thisDep,u=u||i.propDep,f=f||i.contextDep}else{switch(a(s,".",t,"="),typeof r){case"number":a(r);break;case"string":a('"',r,'"');break;case"object":Array.isArray(r)&&a("[",r.join(),"]");break;default:a(e.link(r));break}a(";")}})),t.dynamic[r]=new me.DynamicVariable(Mi,{thisDep:o,contextDep:f,propDep:u,ref:s,append:c}),delete t.static[r]}function c(e,t){i.forEach((function(r){var a=n[r];if(me.isDynamic(a)){var i=e.invoke(t,a);t(s,".",r,"=",i,";")}}))}}function se(e,r,n,a,i){var o=D();o.stats=o.link(i),Object.keys(r.static).forEach((function(e){ue(o,r,e)})),Co.forEach((function(t){ue(o,e,t)}));var f=U(e,r,n,a,o);return te(o,f),oe(o,f),ie(o,f),t(o.compile(),{destroy:function(){f.shader.program.destroy()}})}return{next:w,current:x,procs:function(){var e=D(),t=e.proc("poll"),r=e.proc("refresh"),i=e.block();t(i),r(i);var o,f=e.shared,u=f.gl,s=f.next,c=f.current;i(c,".dirty=false;"),q(e,t),q(e,r,null,!0),v&&(o=e.link(v)),n.oes_vertex_array_object&&r(e.link(n.oes_vertex_array_object),".bindVertexArrayOES(null);");for(var l=0;l<a.maxAttributes;++l){var d=r.def(f.attributes,"[",l,"]"),h=e.cond(d,".buffer");h.then(u,".enableVertexAttribArray(",l,");",u,".bindBuffer(",jo,",",d,".buffer.buffer);",u,".vertexAttribPointer(",l,",",d,".size,",d,".type,",d,".normalized,",d,".stride,",d,".offset);").else(u,".disableVertexAttribArray(",l,");",u,".vertexAttrib4f(",l,",",d,".x,",d,".y,",d,".z,",d,".w);",d,".buffer=null;"),r(h),v&&r(o,".vertexAttribDivisorANGLE(",l,",",d,".divisor);")}return r(e.shared.vao,".currentVAO=null;",e.shared.vao,".setVAO(",e.shared.vao,".targetVAO);"),Object.keys(A).forEach((function(n){var a=A[n],o=i.def(s,".",n),f=e.block();f("if(",o,"){",u,".enable(",a,")}else{",u,".disable(",a,")}",c,".",n,"=",o,";"),r(f),t("if(",o,"!==",c,".",n,"){",f,"}")})),Object.keys(k).forEach((function(n){var a,o,f=k[n],l=x[n],d=e.block();if(d(u,".",f,"("),Or(l)){var h=l.length;a=e.global.def(s,".",n),o=e.global.def(c,".",n),d(Oe(h,(function(e){return a+"["+e+"]"})),");",Oe(h,(function(e){return o+"["+e+"]="+a+"["+e+"];"})).join("")),t("if(",Oe(h,(function(e){return a+"["+e+"]!=="+o+"["+e+"]"})).join("||"),"){",d,"}")}else a=i.def(s,".",n),o=i.def(c,".",n),d(a,");",c,".",n,"=",a,";"),t("if(",a,"!==",o,"){",d,"}");r(d)})),e.compile()}(),compile:se}}function Rf(){return{vaoCount:0,bufferCount:0,elementsCount:0,framebufferCount:0,shaderCount:0,textureCount:0,cubeCount:0,renderbufferCount:0,maxTextureUnits:0}}var Lf=34918,Vf=34919,Nf=35007,Hf=function(e,t){if(!t.ext_disjoint_timer_query)return null;var r=[];function n(){return r.pop()||t.ext_disjoint_timer_query.createQueryEXT()}function a(e){r.push(e)}var i=[];function o(e){var r=n();t.ext_disjoint_timer_query.beginQueryEXT(Nf,r),i.push(r),h(i.length-1,i.length,e)}function f(){t.ext_disjoint_timer_query.endQueryEXT(Nf)}function u(){this.startQueryIndex=-1,this.endQueryIndex=-1,this.sum=0,this.stats=null}var s=[];function c(){return s.pop()||new u}function l(e){s.push(e)}var d=[];function h(e,t,r){var n=c();n.startQueryIndex=e,n.endQueryIndex=t,n.sum=0,n.stats=r,d.push(n)}var p=[],m=[];function b(){var e,r,n=i.length;if(0!==n){m.length=Math.max(m.length,n+1),p.length=Math.max(p.length,n+1),p[0]=0,m[0]=0;var o=0;for(e=0,r=0;r<i.length;++r){var f=i[r];t.ext_disjoint_timer_query.getQueryObjectEXT(f,Vf)?(o+=t.ext_disjoint_timer_query.getQueryObjectEXT(f,Lf),a(f)):i[e++]=f,p[r+1]=o,m[r+1]=e}for(i.length=e,e=0,r=0;r<d.length;++r){var u=d[r],s=u.startQueryIndex,c=u.endQueryIndex;u.sum+=p[c]-p[s];var h=m[s],b=m[c];b===h?(u.stats.gpuTime+=u.sum/1e6,l(u)):(u.startQueryIndex=h,u.endQueryIndex=b,d[e++]=u)}d.length=e}}return{beginQuery:o,endQuery:f,pushScopeStats:h,update:b,getNumPendingQueries:function(){return i.length},clear:function(){r.push.apply(r,i);for(var e=0;e<r.length;e++)t.ext_disjoint_timer_query.deleteQueryEXT(r[e]);i.length=0,r.length=0},restore:function(){i.length=0,r.length=0}}},Uf=16384,Wf=256,qf=1024,Gf=34962,$f="webglcontextlost",Kf="webglcontextrestored",Qf=1,Yf=2,Xf=3;function Jf(e,t){for(var r=0;r<e.length;++r)if(e[r]===t)return r;return-1}function Zf(e){var r=Se(e);if(!r)return null;var n=r.gl,a=n.getContextAttributes(),i=n.isContextLost(),o=Ee(n,r);if(!o)return null;var f=ve(),u=Rf(),s=o.extensions,c=Hf(n,s),l=ge(),d=n.drawingBufferWidth,h=n.drawingBufferHeight,p={tick:0,time:0,viewportWidth:d,viewportHeight:h,framebufferWidth:d,framebufferHeight:h,drawingBufferWidth:d,drawingBufferHeight:h,pixelRatio:r.pixelRatio},m={},b={elements:null,primitive:4,count:-1,offset:0,instances:-1},g=At(n,s),v=rr(n,u,r,x),y=li(n,s,g,u,v);function x(e){return y.destroyBuffer(e)}var w=_r(n,s,v,u),_=bi(n,f,u,r),A=da(n,s,g,(function(){E.procs.poll()}),p,u,r),k=Ea(n,s,g,u,r),S=fi(n,s,g,A,k,u),E=Bf(n,f,s,g,v,w,A,S,m,y,_,b,p,c,r),O=wi(n,S,E.procs.poll,p,a,s,g),T=E.next,C=n.canvas,j=[],D=[],F=[],M=[r.onDestroy],P=null;function z(){if(0===j.length)return c&&c.update(),void(P=null);P=be.next(z),$();for(var e=j.length-1;e>=0;--e){var t=j[e];t&&t(p,null,0)}n.flush(),c&&c.update()}function I(){!P&&j.length>0&&(P=be.next(z))}function B(){P&&(be.cancel(z),P=null)}function R(e){e.preventDefault(),i=!0,B(),D.forEach((function(e){e()}))}function L(e){n.getError(),i=!1,o.restore(),_.restore(),v.restore(),A.restore(),k.restore(),S.restore(),y.restore(),c&&c.restore(),E.procs.refresh(),I(),F.forEach((function(e){e()}))}function V(){j.length=0,B(),C&&(C.removeEventListener($f,R),C.removeEventListener(Kf,L)),_.clear(),S.clear(),k.clear(),A.clear(),w.clear(),v.clear(),y.clear(),c&&c.clear(),M.forEach((function(e){e()}))}function N(e){function r(e){var r=t({},e);function n(e){if(e in r){var t=r[e];delete r[e],Object.keys(t).forEach((function(n){r[e+"."+n]=t[n]}))}}return delete r.uniforms,delete r.attributes,delete r.context,delete r.vao,"stencil"in r&&r.stencil.op&&(r.stencil.opBack=r.stencil.opFront=r.stencil.op,delete r.stencil.op),n("blend"),n("depth"),n("cull"),n("stencil"),n("polygonOffset"),n("scissor"),n("sample"),"vao"in e&&(r.vao=e.vao),r}function n(e,t){var r={},n={};return Object.keys(e).forEach((function(a){var i=e[a];if(me.isDynamic(i))n[a]=me.unbox(i,a);else{if(t&&Array.isArray(i))for(var o=0;o<i.length;++o)if(me.isDynamic(i[o]))return void(n[a]=me.unbox(i,a));r[a]=i}})),{dynamic:n,static:r}}ne(!!e,"invalid args to regl({...})"),ne.type(e,"object","invalid args to regl({...})");var a=n(e.context||{},!0),o=n(e.uniforms||{},!0),f=n(e.attributes||{},!1),u=n(r(e),!1),s={gpuTime:0,cpuTime:0,count:0},c=E.compile(u,f,o,a,s),l=c.draw,d=c.batch,h=c.scope,p=[];function m(e){while(p.length<e)p.push(null);return p}function b(e,t){var r;if(i&&ne.raise("context lost"),"function"===typeof e)return h.call(this,null,e,0);if("function"===typeof t)if("number"===typeof e)for(r=0;r<e;++r)h.call(this,null,t,r);else{if(!Array.isArray(e))return h.call(this,e,t,0);for(r=0;r<e.length;++r)h.call(this,e[r],t,r)}else if("number"===typeof e){if(e>0)return d.call(this,m(0|e),0|e)}else{if(!Array.isArray(e))return l.call(this,e);if(e.length)return d.call(this,e,e.length)}}return t(b,{stats:s,destroy:function(){c.destroy()}})}C&&(C.addEventListener($f,R,!1),C.addEventListener(Kf,L,!1));var H=S.setFBO=N({framebuffer:me.define.call(null,Qf,"framebuffer")});function U(e,t){var r=0;E.procs.poll();var a=t.color;a&&(n.clearColor(+a[0]||0,+a[1]||0,+a[2]||0,+a[3]||0),r|=Uf),"depth"in t&&(n.clearDepth(+t.depth),r|=Wf),"stencil"in t&&(n.clearStencil(0|t.stencil),r|=qf),ne(!!r,"called regl.clear with no buffer specified"),n.clear(r)}function W(e){if(ne("object"===typeof e&&e,"regl.clear() takes an object as input"),"framebuffer"in e)if(e.framebuffer&&"framebufferCube"===e.framebuffer_reglType)for(var r=0;r<6;++r)H(t({framebuffer:e.framebuffer.faces[r]},e),U);else H(e,U);else U(null,e)}function q(e){function t(){var t=Jf(j,e);function r(){var e=Jf(j,r);j[e]=j[j.length-1],j.length-=1,j.length<=0&&B()}ne(t>=0,"cannot cancel a frame twice"),j[t]=r}return ne.type(e,"function","regl.frame() callback must be a function"),j.push(e),I(),{cancel:t}}function G(){var e=T.viewport,t=T.scissor_box;e[0]=e[1]=t[0]=t[1]=0,p.viewportWidth=p.framebufferWidth=p.drawingBufferWidth=e[2]=t[2]=n.drawingBufferWidth,p.viewportHeight=p.framebufferHeight=p.drawingBufferHeight=e[3]=t[3]=n.drawingBufferHeight}function $(){p.tick+=1,p.time=Q(),G(),E.procs.poll()}function K(){A.refresh(),G(),E.procs.refresh(),c&&c.update()}function Q(){return(ge()-l)/1e3}function Y(e,t){var r;switch(ne.type(t,"function","listener callback must be a function"),e){case"frame":return q(t);case"lost":r=D;break;case"restore":r=F;break;case"destroy":r=M;break;default:ne.raise("invalid event, must be one of frame,lost,restore,destroy")}return r.push(t),{cancel:function(){for(var e=0;e<r.length;++e)if(r[e]===t)return r[e]=r[r.length-1],void r.pop()}}}K();var X=t(N,{clear:W,prop:me.define.bind(null,Qf),context:me.define.bind(null,Yf),this:me.define.bind(null,Xf),draw:N({}),buffer:function(e){return v.create(e,Gf,!1,!1)},elements:function(e){return w.create(e,!1)},texture:A.create2D,cube:A.createCube,renderbuffer:k.create,framebuffer:S.create,framebufferCube:S.createCube,vao:y.createVAO,attributes:a,frame:q,on:Y,limits:g,hasExtension:function(e){return g.extensions.indexOf(e.toLowerCase())>=0},read:O,destroy:V,_gl:n,_refresh:K,poll:function(){$(),c&&c.update()},now:Q,stats:u});return r.onDone(null,X),X}return Zf}))},"91a7":function(e,t,r){var n=r("fac2"),a=r("e079"),i=r("b7c3"),o=r("706c"),f=r("6bc6"),u=[0,0];e.exports.computeMiter=function(e,t,r,o,s){return n(e,r,o),i(e,e),a(t,-e[1],e[0]),a(u,-r[1],r[0]),s/f(t,u)},e.exports.normal=function(e,t){return a(e,-t[1],t[0]),e},e.exports.direction=function(e,t,r){return o(e,t,r),i(e,e),e}},"98db":function(e,t,r){(function(e,t){
/*! *****************************************************************************
Copyright (C) Microsoft. All rights reserved.
Licensed under the Apache License, Version 2.0 (the "License"); you may not use
this file except in compliance with the License. You may obtain a copy of the
License at http://www.apache.org/licenses/LICENSE-2.0

THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
MERCHANTABLITY OR NON-INFRINGEMENT.

See the Apache Version 2.0 License for specific language governing permissions
and limitations under the License.
***************************************************************************** */
var r;(function(r){(function(e){var n="object"===typeof t?t:"object"===typeof self?self:"object"===typeof this?this:Function("return this;")(),a=i(r);function i(e,t){return function(r,n){"function"!==typeof e[r]&&Object.defineProperty(e,r,{configurable:!0,writable:!0,value:n}),t&&t(r,n)}}"undefined"===typeof n.Reflect?n.Reflect=r:a=i(n.Reflect,a),e(a)})((function(t){var r=Object.prototype.hasOwnProperty,n="function"===typeof Symbol,a=n&&"undefined"!==typeof Symbol.toPrimitive?Symbol.toPrimitive:"@@toPrimitive",i=n&&"undefined"!==typeof Symbol.iterator?Symbol.iterator:"@@iterator",o="function"===typeof Object.create,f={__proto__:[]}instanceof Array,u=!o&&!f,s={create:o?function(){return ie(Object.create(null))}:f?function(){return ie({__proto__:null})}:function(){return ie({})},has:u?function(e,t){return r.call(e,t)}:function(e,t){return t in e},get:u?function(e,t){return r.call(e,t)?e[t]:void 0}:function(e,t){return e[t]}},c=Object.getPrototypeOf(Function),l="object"===typeof e&&Object({NODE_ENV:"production",VUE_APP_UIVERSION:"20250804.225135c.guangdonghuanbei",VUE_APP_MULTIVERSE_PATH:"./",BASE_URL:""})&&"true"===Object({NODE_ENV:"production",VUE_APP_UIVERSION:"20250804.225135c.guangdonghuanbei",VUE_APP_MULTIVERSE_PATH:"./",BASE_URL:""})["REFLECT_METADATA_USE_MAP_POLYFILL"],d=l||"function"!==typeof Map||"function"!==typeof Map.prototype.entries?re():Map,h=l||"function"!==typeof Set||"function"!==typeof Set.prototype.entries?ne():Set,p=l||"function"!==typeof WeakMap?ae():WeakMap,m=new p;function b(e,t,r,n){if(B(r)){if(!G(e))throw new TypeError;if(!K(t))throw new TypeError;return E(e,t)}if(!G(e))throw new TypeError;if(!V(t))throw new TypeError;if(!V(n)&&!B(n)&&!R(n))throw new TypeError;return R(n)&&(n=void 0),r=q(r),O(e,t,r,n)}function g(e,t){function r(r,n){if(!V(r))throw new TypeError;if(!B(n)&&!Q(n))throw new TypeError;M(e,t,r,n)}return r}function v(e,t,r,n){if(!V(r))throw new TypeError;return B(n)||(n=q(n)),M(e,t,r,n)}function y(e,t,r){if(!V(t))throw new TypeError;return B(r)||(r=q(r)),C(e,t,r)}function x(e,t,r){if(!V(t))throw new TypeError;return B(r)||(r=q(r)),j(e,t,r)}function w(e,t,r){if(!V(t))throw new TypeError;return B(r)||(r=q(r)),D(e,t,r)}function _(e,t,r){if(!V(t))throw new TypeError;return B(r)||(r=q(r)),F(e,t,r)}function A(e,t){if(!V(e))throw new TypeError;return B(t)||(t=q(t)),P(e,t)}function k(e,t){if(!V(e))throw new TypeError;return B(t)||(t=q(t)),z(e,t)}function S(e,t,r){if(!V(t))throw new TypeError;B(r)||(r=q(r));var n=T(t,r,!1);if(B(n))return!1;if(!n.delete(e))return!1;if(n.size>0)return!0;var a=m.get(t);return a.delete(r),a.size>0||m.delete(t),!0}function E(e,t){for(var r=e.length-1;r>=0;--r){var n=e[r],a=n(t);if(!B(a)&&!R(a)){if(!K(a))throw new TypeError;t=a}}return t}function O(e,t,r,n){for(var a=e.length-1;a>=0;--a){var i=e[a],o=i(t,r,n);if(!B(o)&&!R(o)){if(!V(o))throw new TypeError;n=o}}return n}function T(e,t,r){var n=m.get(e);if(B(n)){if(!r)return;n=new d,m.set(e,n)}var a=n.get(t);if(B(a)){if(!r)return;a=new d,n.set(t,a)}return a}function C(e,t,r){var n=j(e,t,r);if(n)return!0;var a=te(t);return!R(a)&&C(e,a,r)}function j(e,t,r){var n=T(t,r,!1);return!B(n)&&U(n.has(e))}function D(e,t,r){var n=j(e,t,r);if(n)return F(e,t,r);var a=te(t);return R(a)?void 0:D(e,a,r)}function F(e,t,r){var n=T(t,r,!1);if(!B(n))return n.get(e)}function M(e,t,r,n){var a=T(r,n,!0);a.set(e,t)}function P(e,t){var r=z(e,t),n=te(e);if(null===n)return r;var a=P(n,t);if(a.length<=0)return r;if(r.length<=0)return a;for(var i=new h,o=[],f=0,u=r;f<u.length;f++){var s=u[f],c=i.has(s);c||(i.add(s),o.push(s))}for(var l=0,d=a;l<d.length;l++){s=d[l],c=i.has(s);c||(i.add(s),o.push(s))}return o}function z(e,t){var r=[],n=T(e,t,!1);if(B(n))return r;var a=n.keys(),i=X(a),o=0;while(1){var f=Z(i);if(!f)return r.length=o,r;var u=J(f);try{r[o]=u}catch(s){try{ee(i)}finally{throw s}}o++}}function I(e){if(null===e)return 1;switch(typeof e){case"undefined":return 0;case"boolean":return 2;case"string":return 3;case"symbol":return 4;case"number":return 5;case"object":return null===e?1:6;default:return 6}}function B(e){return void 0===e}function R(e){return null===e}function L(e){return"symbol"===typeof e}function V(e){return"object"===typeof e?null!==e:"function"===typeof e}function N(e,t){switch(I(e)){case 0:return e;case 1:return e;case 2:return e;case 3:return e;case 4:return e;case 5:return e}var r=3===t?"string":5===t?"number":"default",n=Y(e,a);if(void 0!==n){var i=n.call(e,r);if(V(i))throw new TypeError;return i}return H(e,"default"===r?"number":r)}function H(e,t){if("string"===t){var r=e.toString;if($(r)){var n=r.call(e);if(!V(n))return n}var a=e.valueOf;if($(a)){n=a.call(e);if(!V(n))return n}}else{a=e.valueOf;if($(a)){n=a.call(e);if(!V(n))return n}var i=e.toString;if($(i)){n=i.call(e);if(!V(n))return n}}throw new TypeError}function U(e){return!!e}function W(e){return""+e}function q(e){var t=N(e,3);return L(t)?t:W(t)}function G(e){return Array.isArray?Array.isArray(e):e instanceof Object?e instanceof Array:"[object Array]"===Object.prototype.toString.call(e)}function $(e){return"function"===typeof e}function K(e){return"function"===typeof e}function Q(e){switch(I(e)){case 3:return!0;case 4:return!0;default:return!1}}function Y(e,t){var r=e[t];if(void 0!==r&&null!==r){if(!$(r))throw new TypeError;return r}}function X(e){var t=Y(e,i);if(!$(t))throw new TypeError;var r=t.call(e);if(!V(r))throw new TypeError;return r}function J(e){return e.value}function Z(e){var t=e.next();return!t.done&&t}function ee(e){var t=e["return"];t&&t.call(e)}function te(e){var t=Object.getPrototypeOf(e);if("function"!==typeof e||e===c)return t;if(t!==c)return t;var r=e.prototype,n=r&&Object.getPrototypeOf(r);if(null==n||n===Object.prototype)return t;var a=n.constructor;return"function"!==typeof a||a===e?t:a}function re(){var e={},t=[],r=function(){function e(e,t,r){this._index=0,this._keys=e,this._values=t,this._selector=r}return e.prototype["@@iterator"]=function(){return this},e.prototype[i]=function(){return this},e.prototype.next=function(){var e=this._index;if(e>=0&&e<this._keys.length){var r=this._selector(this._keys[e],this._values[e]);return e+1>=this._keys.length?(this._index=-1,this._keys=t,this._values=t):this._index++,{value:r,done:!1}}return{value:void 0,done:!0}},e.prototype.throw=function(e){throw this._index>=0&&(this._index=-1,this._keys=t,this._values=t),e},e.prototype.return=function(e){return this._index>=0&&(this._index=-1,this._keys=t,this._values=t),{value:e,done:!0}},e}();return function(){function t(){this._keys=[],this._values=[],this._cacheKey=e,this._cacheIndex=-2}return Object.defineProperty(t.prototype,"size",{get:function(){return this._keys.length},enumerable:!0,configurable:!0}),t.prototype.has=function(e){return this._find(e,!1)>=0},t.prototype.get=function(e){var t=this._find(e,!1);return t>=0?this._values[t]:void 0},t.prototype.set=function(e,t){var r=this._find(e,!0);return this._values[r]=t,this},t.prototype.delete=function(t){var r=this._find(t,!1);if(r>=0){for(var n=this._keys.length,a=r+1;a<n;a++)this._keys[a-1]=this._keys[a],this._values[a-1]=this._values[a];return this._keys.length--,this._values.length--,t===this._cacheKey&&(this._cacheKey=e,this._cacheIndex=-2),!0}return!1},t.prototype.clear=function(){this._keys.length=0,this._values.length=0,this._cacheKey=e,this._cacheIndex=-2},t.prototype.keys=function(){return new r(this._keys,this._values,n)},t.prototype.values=function(){return new r(this._keys,this._values,a)},t.prototype.entries=function(){return new r(this._keys,this._values,o)},t.prototype["@@iterator"]=function(){return this.entries()},t.prototype[i]=function(){return this.entries()},t.prototype._find=function(e,t){return this._cacheKey!==e&&(this._cacheIndex=this._keys.indexOf(this._cacheKey=e)),this._cacheIndex<0&&t&&(this._cacheIndex=this._keys.length,this._keys.push(e),this._values.push(void 0)),this._cacheIndex},t}();function n(e,t){return e}function a(e,t){return t}function o(e,t){return[e,t]}}function ne(){return function(){function e(){this._map=new d}return Object.defineProperty(e.prototype,"size",{get:function(){return this._map.size},enumerable:!0,configurable:!0}),e.prototype.has=function(e){return this._map.has(e)},e.prototype.add=function(e){return this._map.set(e,e),this},e.prototype.delete=function(e){return this._map.delete(e)},e.prototype.clear=function(){this._map.clear()},e.prototype.keys=function(){return this._map.keys()},e.prototype.values=function(){return this._map.values()},e.prototype.entries=function(){return this._map.entries()},e.prototype["@@iterator"]=function(){return this.keys()},e.prototype[i]=function(){return this.keys()},e}()}function ae(){var e=16,t=s.create(),n=a();return function(){function e(){this._key=a()}return e.prototype.has=function(e){var t=i(e,!1);return void 0!==t&&s.has(t,this._key)},e.prototype.get=function(e){var t=i(e,!1);return void 0!==t?s.get(t,this._key):void 0},e.prototype.set=function(e,t){var r=i(e,!0);return r[this._key]=t,this},e.prototype.delete=function(e){var t=i(e,!1);return void 0!==t&&delete t[this._key]},e.prototype.clear=function(){this._key=a()},e}();function a(){var e;do{e="@@WeakMap@@"+u()}while(s.has(t,e));return t[e]=!0,e}function i(e,t){if(!r.call(e,n)){if(!t)return;Object.defineProperty(e,n,{value:s.create()})}return e[n]}function o(e,t){for(var r=0;r<t;++r)e[r]=255*Math.random()|0;return e}function f(e){return"function"===typeof Uint8Array?"undefined"!==typeof crypto?crypto.getRandomValues(new Uint8Array(e)):"undefined"!==typeof msCrypto?msCrypto.getRandomValues(new Uint8Array(e)):o(new Uint8Array(e),e):o(new Array(e),e)}function u(){var t=f(e);t[6]=79&t[6]|64,t[8]=191&t[8]|128;for(var r="",n=0;n<e;++n){var a=t[n];4!==n&&6!==n&&8!==n||(r+="-"),a<16&&(r+="0"),r+=a.toString(16).toLowerCase()}return r}}function ie(e){return e.__=void 0,delete e.__,e}t("decorate",b),t("metadata",g),t("defineMetadata",v),t("hasMetadata",y),t("hasOwnMetadata",x),t("getMetadata",w),t("getOwnMetadata",_),t("getMetadataKeys",A),t("getOwnMetadataKeys",k),t("deleteMetadata",S)}))})(r||(r={}))}).call(this,r("4362"),r("c8ba"))},9980:function(e,t,r){},"9ab4":function(e,t,r){"use strict";r.d(t,"c",(function(){return a})),r.d(t,"a",(function(){return i})),r.d(t,"e",(function(){return o})),r.d(t,"b",(function(){return f})),r.d(t,"d",(function(){return u}));
/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */
var n=function(e,t){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])},n(e,t)};function a(e,t){function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var i=function(){return i=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var a in t=arguments[r],t)Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},i.apply(this,arguments)};function o(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var a=0;for(n=Object.getOwnPropertySymbols(e);a<n.length;a++)t.indexOf(n[a])<0&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(r[n[a]]=e[n[a]])}return r}function f(e,t,r,n){function a(e){return e instanceof r?e:new r((function(t){t(e)}))}return new(r||(r=Promise))((function(r,i){function o(e){try{u(n.next(e))}catch(t){i(t)}}function f(e){try{u(n["throw"](e))}catch(t){i(t)}}function u(e){e.done?r(e.value):a(e.value).then(o,f)}u((n=n.apply(e,t||[])).next())}))}function u(e,t){var r,n,a,i,o={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return i={next:f(0),throw:f(1),return:f(2)},"function"===typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function f(e){return function(t){return u([e,t])}}function u(i){if(r)throw new TypeError("Generator is already executing.");while(o)try{if(r=1,n&&(a=2&i[0]?n["return"]:i[0]?n["throw"]||((a=n["return"])&&a.call(n),0):n.next)&&!(a=a.call(n,i[1])).done)return a;switch(n=0,a&&(i=[2&i[0],a.value]),i[0]){case 0:case 1:a=i;break;case 4:return o.label++,{value:i[1],done:!1};case 5:o.label++,n=i[1],i=[0];continue;case 7:i=o.ops.pop(),o.trys.pop();continue;default:if(a=o.trys,!(a=a.length>0&&a[a.length-1])&&(6===i[0]||2===i[0])){o=0;continue}if(3===i[0]&&(!a||i[1]>a[0]&&i[1]<a[3])){o.label=i[1];break}if(6===i[0]&&o.label<a[1]){o.label=a[1],a=i;break}if(a&&o.label<a[2]){o.label=a[2],o.ops.push(i);break}a[2]&&o.ops.pop(),o.trys.pop();continue}i=t.call(e,o)}catch(f){i=[6,f],n=0}finally{r=a=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}}},a23a:function(e,t,r){"use strict";r("f6df")},ad06:function(e,t,r){"use strict";r("9980")},b8fa:function(e,t){e.exports=function(e){return!(!e||"string"===typeof e)&&(e instanceof Array||Array.isArray(e)||e.length>=0&&(e.splice instanceof Function||Object.getOwnPropertyDescriptor(e,e.length-1)&&"String"!==e.constructor.name))}},f6df:function(e,t,r){}}]);