(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-35b8aa3e"],{"0210":function(e,t,n){"use strict";n.r(t),n.d(t,"GotoLineEntry",(function(){return g})),n.d(t,"GotoLineAction",(function(){return h}));n("0bda");var o=n("3742"),r=n("469c"),i=n("b055"),a=n("b2cc"),s=n("7061"),u=n("6a89"),c=n("c101"),d=n("af33"),l=n("03d9"),f=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),g=function(e){function t(t,n,o){var r=e.call(this)||this;return r.editor=n,r.decorator=o,r.parseResult=r.parseInput(t),r}return f(t,e),t.prototype.parseInput=function(e){var t,n,r=e.split(",").map((function(e){return parseInt(e,10)})).filter((function(e){return!isNaN(e)}));if(t=0===r.length?new s["a"](-1,-1):1===r.length?new s["a"](r[0],1):new s["a"](r[0],r[1]),Object(i["a"])(this.editor))n=this.editor.getModel();else{var a=this.editor.getModel();n=a?a.modified:null}var u,c=!!n&&n.validatePosition(t).equals(t);return u=c?t.column&&t.column>1?o["r"](l["b"].gotoLineLabelValidLineAndColumn,t.lineNumber,t.column):o["r"](l["b"].gotoLineLabelValidLine,t.lineNumber):t.lineNumber<1||t.lineNumber>(n?n.getLineCount():0)?o["r"](l["b"].gotoLineLabelEmptyWithLineLimit,n?n.getLineCount():0):o["r"](l["b"].gotoLineLabelEmptyWithLineAndColumnLimit,n?n.getLineMaxColumn(t.lineNumber):0),{position:t,isValid:c,label:u}},t.prototype.getLabel=function(){return this.parseResult.label},t.prototype.getAriaLabel=function(){var e=this.editor.getPosition(),t=e?e.lineNumber:0;return o["r"](l["b"].gotoLineAriaLabel,t,this.parseResult.label)},t.prototype.run=function(e,t){return 1===e?this.runOpen():this.runPreview()},t.prototype.runOpen=function(){if(!this.parseResult.isValid)return!1;var e=this.toSelection();return this.editor.setSelection(e),this.editor.revealRangeInCenter(e,0),this.editor.focus(),!0},t.prototype.runPreview=function(){if(!this.parseResult.isValid)return this.decorator.clearDecorations(),!1;var e=this.toSelection();return this.editor.revealRangeInCenter(e,0),this.decorator.decorateLine(e,this.editor),!1},t.prototype.toSelection=function(){return new u["a"](this.parseResult.position.lineNumber,this.parseResult.position.column,this.parseResult.position.lineNumber,this.parseResult.position.column)},t}(r["a"]),h=function(e){function t(){return e.call(this,l["b"].gotoLineActionInput,{id:"editor.action.gotoLine",label:l["b"].gotoLineActionLabel,alias:"Go to Line...",precondition:void 0,kbOpts:{kbExpr:c["a"].focus,primary:2085,mac:{primary:293},weight:100}})||this}return f(t,e),t.prototype.run=function(e,t){var n=this;this._show(this.getController(t),{getModel:function(e){return new r["c"]([new g(e,t,n.getController(t))])},getAutoFocus:function(e){return{autoFocusFirstEntry:e.length>0}}})},t}(d["a"]);Object(a["f"])(h)},"0bda":function(e,t,n){},"133b":function(e,t,n){},"3c6b":function(e,t,n){"use strict";n.d(t,"d",(function(){return P})),n.d(t,"e",(function(){return F})),n.d(t,"c",(function(){return N})),n.d(t,"g",(function(){return R})),n.d(t,"l",(function(){return z})),n.d(t,"m",(function(){return H})),n.d(t,"b",(function(){return B})),n.d(t,"h",(function(){return W})),n.d(t,"i",(function(){return q})),n.d(t,"n",(function(){return V})),n.d(t,"k",(function(){return G})),n.d(t,"o",(function(){return U})),n.d(t,"a",(function(){return $})),n.d(t,"j",(function(){return Q})),n.d(t,"f",(function(){return J}));var o=n("3742"),r=n("11f7"),i=n("b835"),a=n("308f"),s=n("fe45"),u=n("a666"),c=n("30db"),d=n("4b76"),l=n("6d8e"),f=n("b055"),g=n("8830"),h=n("d3f4"),p=n("7061"),m=n("6a89"),b=n("b707"),v=n("9e74"),y=n("fbba"),_=n("bd05"),k=n("6206"),w=n("61c2"),C=n("9eb8"),S=n("c6a9"),O=n("5fc1"),D=n("b0cd"),E=n("1165"),L=n("03d9"),T=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),I=function(e,t,n,o){var r,i=arguments.length,a=i<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)a=Reflect.decorate(e,t,n,o);else for(var s=e.length-1;s>=0;s--)(r=e[s])&&(a=(i<3?r(a):i>3?r(t,n,a):r(t,n))||a);return i>3&&a&&Object.defineProperty(t,n,a),a},M=function(e,t){return function(n,o){t(n,o,e)}},x=function(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;var o=Array(e),r=0;for(t=0;t<n;t++)for(var i=arguments[t],a=0,s=i.length;a<s;a++,r++)o[r]=i[a];return o},j=function(){function e(e){this.model=e,this._onDispose=new a["a"]}return Object.defineProperty(e.prototype,"textEditorModel",{get:function(){return this.model},enumerable:!0,configurable:!0}),e.prototype.dispose=function(){this._onDispose.fire()},e}();function A(e,t,n){return Object(f["a"])(e)?t(e):n(e)}var P=function(){function e(e){this.modelService=e}return e.prototype.setEditor=function(e){this.editor=e},e.prototype.createModelReference=function(e){var t=this,n=null;return this.editor&&(n=A(this.editor,(function(n){return t.findModel(n,e)}),(function(n){return t.findModel(n.getOriginalEditor(),e)||t.findModel(n.getModifiedEditor(),e)}))),n?Promise.resolve(new u["c"](new j(n))):Promise.reject(new Error("Model not found"))},e.prototype.findModel=function(e,t){var n=this.modelService?this.modelService.getModel(t):e.getModel();return n&&n.uri.toString()!==t.toString()?null:n},e}(),F=function(){function e(){}return e.prototype.show=function(){return e.NULL_PROGRESS_RUNNER},e.prototype.showWhile=function(e,t){return Promise.resolve(void 0)},e.NULL_PROGRESS_RUNNER={done:function(){},total:function(){},worked:function(){}},e}(),N=function(){function e(){}return e}(),R=function(){function e(){}return e.prototype.info=function(e){return this.notify({severity:d["a"].Info,message:e})},e.prototype.warn=function(e){return this.notify({severity:d["a"].Warning,message:e})},e.prototype.error=function(e){return this.notify({severity:d["a"].Error,message:e})},e.prototype.notify=function(t){switch(t.severity){case d["a"].Error:console.error(t.message);break;case d["a"].Warning:console.warn(t.message);break;default:console.log(t.message);break}return e.NO_OP},e.prototype.status=function(e,t){return u["a"].None},e.NO_OP=new D["b"],e}(),z=function(){function e(e){this._onWillExecuteCommand=new a["a"],this._onDidExecuteCommand=new a["a"],this._instantiationService=e,this._dynamicCommands=Object.create(null)}return e.prototype.addCommand=function(e){var t=this,n=e.id;return this._dynamicCommands[n]=e,Object(u["h"])((function(){delete t._dynamicCommands[n]}))},e.prototype.executeCommand=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var o=v["a"].getCommand(e)||this._dynamicCommands[e];if(!o)return Promise.reject(new Error("command '"+e+"' not found"));try{this._onWillExecuteCommand.fire({commandId:e,args:t});var r=this._instantiationService.invokeFunction.apply(this._instantiationService,x([o.handler],t));return this._onDidExecuteCommand.fire({commandId:e,args:t}),Promise.resolve(r)}catch(i){return Promise.reject(i)}},e}(),H=function(e){function t(t,n,o,a,s){var u=e.call(this,t,n,o,a)||this;return u._cachedResolver=null,u._dynamicKeybindings=[],u._register(r["j"](s,r["d"].KEY_DOWN,(function(e){var t=new i["a"](e),n=u._dispatch(t,t.target);n&&(t.preventDefault(),t.stopPropagation())}))),u}return T(t,e),t.prototype.addDynamicKeybinding=function(e,t,n,o){var r=this,i=Object(s["f"])(t,c["a"]),a=new u["b"];i&&(this._dynamicKeybindings.push({keybinding:i,command:e,when:o,weight1:1e3,weight2:0}),a.add(Object(u["h"])((function(){for(var t=0;t<r._dynamicKeybindings.length;t++){var n=r._dynamicKeybindings[t];if(n.command===e)return r._dynamicKeybindings.splice(t,1),void r.updateResolver({source:1})}}))));var d=this._commandService;if(!(d instanceof z))throw new Error("Unknown command service!");return a.add(d.addCommand({id:e,handler:n})),this.updateResolver({source:1}),a},t.prototype.updateResolver=function(e){this._cachedResolver=null,this._onDidUpdateKeybindings.fire(e)},t.prototype._getResolver=function(){if(!this._cachedResolver){var e=this._toNormalizedKeybindingItems(C["a"].getDefaultKeybindings(),!0),t=this._toNormalizedKeybindingItems(this._dynamicKeybindings,!1);this._cachedResolver=new w["a"](e,t)}return this._cachedResolver},t.prototype._documentHasFocus=function(){return document.hasFocus()},t.prototype._toNormalizedKeybindingItems=function(e,t){for(var n=[],o=0,r=0,i=e;r<i.length;r++){var a=i[r],s=a.when||void 0,u=a.keybinding;if(u)for(var c=this.resolveKeybinding(u),d=0,l=c;d<l.length;d++){var f=l[d];n[o++]=new S["a"](f,a.command,a.commandArgs,s,t)}else n[o++]=new S["a"](void 0,a.command,a.commandArgs,s,t)}return n},t.prototype.resolveKeybinding=function(e){return[new O["a"](e,c["a"])]},t.prototype.resolveKeyboardEvent=function(e){var t=new s["e"](e.ctrlKey,e.shiftKey,e.altKey,e.metaKey,e.keyCode).toChord();return new O["a"](t,c["a"])},t}(k["a"]);function K(e){return e&&"object"===typeof e&&(!e.overrideIdentifier||"string"===typeof e.overrideIdentifier)&&(!e.resource||e.resource instanceof l["a"])}var B=function(){function e(){this._onDidChangeConfiguration=new a["a"],this.onDidChangeConfiguration=this._onDidChangeConfiguration.event,this._configuration=new _["a"](new _["c"],new _["b"])}return e.prototype.configuration=function(){return this._configuration},e.prototype.getValue=function(e,t){var n="string"===typeof e?e:void 0,o=K(e)?e:K(t)?t:{};return this.configuration().getValue(n,o,void 0)},e.prototype.updateValue=function(e,t,n,o){return this.configuration().updateValue(e,t),Promise.resolve()},e.prototype.inspect=function(e,t){return void 0===t&&(t={}),this.configuration().inspect(e,t,void 0)},e}(),W=function(){function e(e){var t=this;this.configurationService=e,this._onDidChangeConfiguration=new a["a"],this.configurationService.onDidChangeConfiguration((function(e){t._onDidChangeConfiguration.fire({affectedKeys:e.affectedKeys,affectsConfiguration:function(t,n){return e.affectsConfiguration(n)}})}))}return e.prototype.getValue=function(e,t,n){var o=p["a"].isIPosition(t)?t:null,r=o?"string"===typeof n?n:void 0:"string"===typeof t?t:void 0;return"undefined"===typeof r?this.configurationService.getValue():this.configurationService.getValue(r)},e}(),q=function(){function e(e){this.configurationService=e}return e.prototype.getEOL=function(e,t){var n=this.configurationService.getValue("files.eol",{overrideIdentifier:t,resource:e});return n&&"auto"!==n?n:c["d"]||c["e"]?"\n":"\r\n"},e=I([M(0,y["a"])],e),e}(),V=function(){function e(){}return e.prototype.publicLog=function(e,t){return Promise.resolve(void 0)},e.prototype.publicLog2=function(e,t){return this.publicLog(e,t)},e}(),G=function(){function e(){var t=l["a"].from({scheme:e.SCHEME,authority:"model",path:"/"});this.workspace={id:"4064f6ec-cb38-4ad0-af64-ee6467e63c82",folders:[new E["b"]({uri:t,name:"",index:0})]}}return e.prototype.getWorkspace=function(){return this.workspace},e.prototype.getWorkspaceFolder=function(t){return t&&t.scheme===e.SCHEME?this.workspace.folders[0]:null},e.SCHEME="inmemory",e}();function U(e,t,n){t&&e instanceof B&&Object.keys(t).forEach((function(o){Object(g["d"])(o)&&e.updateValue("editor."+o,t[o]),n&&Object(g["c"])(o)&&e.updateValue("diffEditor."+o,t[o])}))}var $=function(){function e(e){this._modelService=e}return e.prototype.hasPreviewHandler=function(){return!1},e.prototype.apply=function(e,t){var n=new Map;if(e.edits)for(var r=0,i=e.edits;r<i.length;r++){var a=i[r];if(!b["D"].is(a))return Promise.reject(new Error("bad edit - only text edits are supported"));var s=this._modelService.getModel(a.resource);if(!s)return Promise.reject(new Error("bad edit - model not found"));var u=n.get(s);u||(u=[],n.set(s,u)),u.push(a.edit)}var c=0,d=0;return n.forEach((function(e,t){t.pushStackElement(),t.pushEditOperations([],e.map((function(e){return h["a"].replaceMove(m["a"].lift(e.range),e.text)})),(function(){return[]})),t.pushStackElement(),d+=1,c+=e.length})),Promise.resolve({selection:void 0,ariaSummary:o["r"](L["f"].bulkEditServiceSummary,c,d)})},e}(),Q=function(){function e(){}return e.prototype.getUriLabel=function(e,t){return"file"===e.scheme?e.fsPath:e.path},e}(),J=function(){function e(e){this._container=e,this.onLayout=a["b"].None}return Object.defineProperty(e.prototype,"container",{get:function(){return this._container},enumerable:!0,configurable:!0}),e}()},"41a0":function(e,t,n){},"42f0":function(e,t,n){},4390:function(e,t,n){"use strict";n.d(t,"a",(function(){return Ie}));n("b223");var o=n("5717"),r=n("3695"),i=n("d0b6"),a=n("fd49"),s=n("fb71"),u=n("8ae8"),c=n("3352"),d=n("b707"),l=n("8bf1"),f=n("a40b"),g=n("b78f"),h=n("af50"),p=n("6881"),m=n("5fe7"),b=n("3742"),v=n("e1b5"),y=n("6da2"),_=n("a8d0"),k=n("d0c6"),w=function(){function e(){}return e.colorizeElement=function(e,t,n,o){o=o||{};var r=o.theme||"vs",i=o.mimeType||n.getAttribute("lang")||n.getAttribute("data-lang");if(!i)return console.error("Mode not detected"),Promise.resolve();e.setTheme(r);var a=n.firstChild?n.firstChild.nodeValue:"";n.className+=" "+r;var s=function(e){n.innerHTML=e};return this.colorize(t,a||"",i,o).then(s,(function(e){return console.error(e)}))},e.colorize=function(e,t,n,o){var r=4;o&&"number"===typeof o.tabSize&&(r=o.tabSize),b["P"](t)&&(t=t.substr(1));var i=t.split(/\r\n|\r|\n/),a=e.getModeId(n);if(!a)return Promise.resolve(S(i,r));e.triggerMode(a);var s=d["B"].get(a);if(s)return C(i,r,s);var u=d["B"].getPromise(a);return new Promise(u?function(e,t){u.then((function(n){C(i,r,n).then(e,t)}),t)}:function(e,t){var n=null,o=null,s=function(){n&&(n.dispose(),n=null),o&&(o.dispose(),o=null);var s=d["B"].get(a);s?C(i,r,s).then(e,t):e(S(i,r))};o=new m["e"],o.cancelAndSet(s,500),n=d["B"].onDidChange((function(e){e.changedLanguages.indexOf(a)>=0&&s()}))})},e.colorizeLine=function(e,t,n,o,r){void 0===r&&(r=4);var i=_["d"].isBasicASCII(e,t),a=_["d"].containsRTL(e,i,n),s=Object(y["e"])(new y["c"](!1,!0,e,!1,i,a,0,o,[],r,0,0,0,-1,"none",!1,!1,null));return s.html},e.colorizeModelLine=function(e,t,n){void 0===n&&(n=4);var o=e.getLineContent(t);e.forceTokenization(t);var r=e.getLineTokens(t),i=r.inflate();return this.colorizeLine(o,e.mightContainNonBasicASCII(),e.mightContainRTL(),i,n)},e}();function C(e,t,n){return new Promise((function(o,r){var i=function(){var a=O(e,t,n);if(n instanceof k["a"]){var s=n.getLoadStatus();if(!1===s.loaded)return void s.promise.then(i,r)}o(a)};i()}))}function S(e,t){var n=[],o=16793600,r=new Uint32Array(2);r[0]=0,r[1]=o;for(var i=0,a=e.length;i<a;i++){var s=e[i];r[0]=s.length;var u=new v["a"](r,s),c=_["d"].isBasicASCII(s,!0),d=_["d"].containsRTL(s,c,!0),l=Object(y["e"])(new y["c"](!1,!0,s,!1,c,d,0,u,[],t,0,0,0,-1,"none",!1,!1,null));n=n.concat(l.html),n.push("<br/>")}return n.join("")}function O(e,t,n){for(var o=[],r=n.getInitialState(),i=0,a=e.length;i<a;i++){var s=e[i],u=n.tokenize2(s,r,0);v["a"].convertToEndOffset(u.tokens,s.length);var c=new v["a"](u.tokens,s),d=_["d"].isBasicASCII(s,!0),l=_["d"].containsRTL(s,d,!0),f=Object(y["e"])(new y["c"](!1,!0,s,!1,d,l,0,c.inflate(),[],t,0,0,0,-1,"none",!1,!1,null));o=o.concat(f.html),o.push("<br/>"),r=u.endState}return o.join("")}var D=n("3c6b"),E=n("0f70"),L=n("3813"),T=n("a666"),I=n("9c1d"),M=n("4a64"),x=n("f58f"),j=n("b1ca"),A=n("7e32"),P=n("9e74"),F=n("fbba"),N=n("4fc3"),R=n("533b"),z=n("0a0f"),H=n("6dec"),K=n("b0cd"),B=n("b7d0"),W=n("4779"),q=n("03d9"),V=n("f577"),G=n("b539"),U=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),$=function(e,t,n,o){var r,i=arguments.length,a=i<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)a=Reflect.decorate(e,t,n,o);else for(var s=e.length-1;s>=0;s--)(r=e[s])&&(a=(i<3?r(a):i>3?r(t,n,a):r(t,n))||a);return i>3&&a&&Object.defineProperty(t,n,a),a},Q=function(e,t){return function(n,o){t(n,o,e)}},J=function(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;var o=Array(e),r=0;for(t=0;t<n;t++)for(var i=arguments[t],a=0,s=i.length;a<s;a++,r++)o[r]=i[a];return o},Y=0,X=!1;function Z(){X||(X=!0,L["b"](document.body))}var ee=function(e){function t(t,n,o,r,i,a,s,u,c,d){var l=this;return n=n||{},n.ariaLabel=n.ariaLabel||q["g"].editorViewAccessibleLabel,n.ariaLabel=n.ariaLabel+";"+(E["i"]?q["g"].accessibilityHelpMessageIE:q["g"].accessibilityHelpMessage),l=e.call(this,t,n,{},o,r,i,a,u,c,d)||this,s instanceof D["m"]?l._standaloneKeybindingService=s:l._standaloneKeybindingService=null,Z(),l}return U(t,e),t.prototype.addCommand=function(e,t,n){if(!this._standaloneKeybindingService)return console.warn("Cannot add command because the editor is configured with an unrecognized KeybindingService"),null;var o="DYNAMIC_"+ ++Y,r=N["a"].deserialize(n);return this._standaloneKeybindingService.addDynamicKeybinding(o,e,t,r),o},t.prototype.createContextKey=function(e,t){return this._contextKeyService.createKey(e,t)},t.prototype.addAction=function(e){var t=this;if("string"!==typeof e.id||"string"!==typeof e.label||"function"!==typeof e.run)throw new Error("Invalid action descriptor, `id`, `label` and `run` are required properties!");if(!this._standaloneKeybindingService)return console.warn("Cannot add keybinding because the editor is configured with an unrecognized KeybindingService"),T["a"].None;var n=e.id,o=e.label,r=N["a"].and(N["a"].equals("editorId",this.getId()),N["a"].deserialize(e.precondition)),i=e.keybindings,a=N["a"].and(r,N["a"].deserialize(e.keybindingContext)),s=e.contextMenuGroupId||null,u=e.contextMenuOrder||0,c=function(n){for(var o=[],r=1;r<arguments.length;r++)o[r-1]=arguments[r];return Promise.resolve(e.run.apply(e,J([t],o)))},d=new T["b"],l=this.getId()+":"+n;if(d.add(P["a"].registerCommand(l,c)),s){var f={command:{id:l,title:o},when:r,group:s,order:u};d.add(A["c"].appendMenuItem(7,f))}if(Array.isArray(i))for(var g=0,h=i;g<h.length;g++){var p=h[g];d.add(this._standaloneKeybindingService.addDynamicKeybinding(l,p,c,a))}var m=new x["a"](l,o,o,r,c,this._contextKeyService);return this._actions[n]=m,d.add(Object(T["h"])((function(){delete t._actions[n]}))),d},t=$([Q(2,z["a"]),Q(3,o["a"]),Q(4,P["b"]),Q(5,N["c"]),Q(6,H["a"]),Q(7,B["c"]),Q(8,K["a"]),Q(9,W["b"])],t),t}(I["a"]),te=function(e){function t(t,n,o,r,i,a,s,u,c,d,l,f,g){var h=this;Object(D["o"])(f,n,!1);var p=d.registerEditorContainer(t);n=n||{},"string"===typeof n.theme&&d.setTheme(n.theme);var m,b=n.model;if(delete n.model,h=e.call(this,t,n,r,i,a,s,u,d,l,g)||this,h._contextViewService=c,h._configurationService=f,h._register(o),h._register(p),"undefined"===typeof b?(m=self.monaco.editor.createModel(n.value||"",n.language||"text/plain"),h._ownsModel=!0):(m=b,h._ownsModel=!1),h._attachModel(m),m){var v={oldModelUrl:null,newModelUrl:m.uri};h._onDidChangeModel.fire(v)}return h}return U(t,e),t.prototype.dispose=function(){e.prototype.dispose.call(this)},t.prototype.updateOptions=function(t){Object(D["o"])(this._configurationService,t,!1),e.prototype.updateOptions.call(this,t)},t.prototype._attachModel=function(t){e.prototype._attachModel.call(this,t),this._modelData&&this._contextViewService.setContainer(this._modelData.view.domNode.domNode)},t.prototype._postDetachModelCleanup=function(t){e.prototype._postDetachModelCleanup.call(this,t),t&&this._ownsModel&&(t.dispose(),this._ownsModel=!1)},t=$([Q(3,z["a"]),Q(4,o["a"]),Q(5,P["b"]),Q(6,N["c"]),Q(7,H["a"]),Q(8,R["b"]),Q(9,j["a"]),Q(10,K["a"]),Q(11,F["a"]),Q(12,W["b"])],t),t}(ee),ne=function(e){function t(t,n,o,r,i,a,s,u,c,d,l,f,g,h,p){var m=this;Object(D["o"])(f,n,!0);var b=d.registerEditorContainer(t);return n=n||{},"string"===typeof n.theme&&(n.theme=d.setTheme(n.theme)),m=e.call(this,t,n,p,u,i,r,c,d,l,g,h)||this,m._contextViewService=s,m._configurationService=f,m._register(o),m._register(b),m._contextViewService.setContainer(m._containerDomElement),m}return U(t,e),t.prototype.dispose=function(){e.prototype.dispose.call(this)},t.prototype.updateOptions=function(t){Object(D["o"])(this._configurationService,t,!0),e.prototype.updateOptions.call(this,t)},t.prototype._createInnerEditor=function(e,t,n){return e.createInstance(ee,t,n)},t.prototype.getOriginalEditor=function(){return e.prototype.getOriginalEditor.call(this)},t.prototype.getModifiedEditor=function(){return e.prototype.getModifiedEditor.call(this)},t.prototype.addCommand=function(e,t,n){return this.getModifiedEditor().addCommand(e,t,n)},t.prototype.createContextKey=function(e,t){return this.getModifiedEditor().createContextKey(e,t)},t.prototype.addAction=function(e){return this.getModifiedEditor().addAction(e)},t=$([Q(3,z["a"]),Q(4,N["c"]),Q(5,H["a"]),Q(6,R["b"]),Q(7,f["a"]),Q(8,o["a"]),Q(9,j["a"]),Q(10,K["a"]),Q(11,F["a"]),Q(12,R["a"]),Q(13,G["a"]),Q(14,Object(z["d"])(V["a"]))],t),t}(M["a"]),oe=n("caf5"),re=n("5bd7"),ie=n("1ddc");function ae(e,t,n){var i=new oe["a"](e,t),a=null;i.has(g["a"])||(a=new D["d"](oe["b"].modelService.get()),i.set(g["a"],a)),i.has(re["a"])||i.set(re["a"],new r["a"](i.get(o["a"]),i.get(P["b"])));var s=n(i);return a&&a.setEditor(s),s}function se(e,t,n){return ae(e,n||{},(function(n){return new te(e,t,n,n.get(z["a"]),n.get(o["a"]),n.get(P["b"]),n.get(N["c"]),n.get(H["a"]),n.get(R["b"]),n.get(j["a"]),n.get(K["a"]),n.get(F["a"]),n.get(W["b"]))}))}function ue(e){return oe["b"].codeEditorService.get().onCodeEditorAdd((function(t){e(t)}))}function ce(e,t,n){return ae(e,n||{},(function(n){return new ne(e,t,n,n.get(z["a"]),n.get(N["c"]),n.get(H["a"]),n.get(R["b"]),n.get(f["a"]),n.get(o["a"]),n.get(j["a"]),n.get(K["a"]),n.get(F["a"]),n.get(R["a"]),n.get(G["a"]),null)}))}function de(e,t){return new i["a"](e,t)}function le(e,t,n){return oe["b"].modelService.get().createModel(e,t,n)}function fe(e,t,n){if(e=e||"",!t){var o=e.indexOf("\n"),r=e;return-1!==o&&(r=e.substring(0,o)),le(e,oe["b"].modeService.get().createByFilepathOrFirstLine(n||null,r),n)}return le(e,oe["b"].modeService.get().create(t),n)}function ge(e,t){oe["b"].modelService.get().setMode(e,oe["b"].modeService.get().create(t))}function he(e,t,n){e&&oe["b"].markerService.get().changeOne(t,e.uri,n)}function pe(e){return oe["b"].markerService.get().read(e)}function me(e){return oe["b"].modelService.get().getModel(e)}function be(){return oe["b"].modelService.get().getModels()}function ve(e){return oe["b"].modelService.get().onModelAdded(e)}function ye(e){return oe["b"].modelService.get().onModelRemoved(e)}function _e(e){return oe["b"].modelService.get().onModelModeChanged((function(t){e({model:t.model,oldLanguage:t.oldModeId})}))}function ke(e){return Object(h["a"])(oe["b"].modelService.get(),e)}function we(e,t){return w.colorizeElement(oe["b"].standaloneThemeService.get(),oe["b"].modeService.get(),e,t)}function Ce(e,t,n){return w.colorize(oe["b"].modeService.get(),e,t,n)}function Se(e,t,n){return void 0===n&&(n=4),w.colorizeModelLine(e,t,n)}function Oe(e){var t=d["B"].get(e);return t||{getInitialState:function(){return l["c"]},tokenize:function(t,n,o){return Object(l["d"])(e,t,n,o)}}}function De(e,t){var n=oe["b"].modeService.get();n.triggerMode(t);for(var o=Oe(t),r=e.split(/\r\n|\r|\n/),i=[],a=o.getInitialState(),s=0,u=r.length;s<u;s++){var c=r[s],d=o.tokenize(c,a,0);i[s]=d.tokens,a=d.endState}return i}function Ee(e,t){oe["b"].standaloneThemeService.get().defineTheme(e,t)}function Le(e){oe["b"].standaloneThemeService.get().setTheme(e)}function Te(){Object(ie["b"])()}function Ie(){return{create:se,onDidCreateEditor:ue,createDiffEditor:ce,createDiffNavigator:de,createModel:fe,setModelLanguage:ge,setModelMarkers:he,getModelMarkers:pe,getModels:be,getModel:me,onDidCreateModel:ve,onWillDisposeModel:ye,onDidChangeModelLanguage:_e,createWebWorker:ke,colorizeElement:we,colorize:Ce,colorizeModelLine:Se,tokenize:De,defineTheme:Ee,setTheme:Le,remeasureFonts:Te,AccessibilitySupport:p["a"],ContentWidgetPositionPreference:p["f"],CursorChangeReason:p["g"],DefaultEndOfLine:p["h"],EditorAutoIndentStrategy:p["j"],EditorOption:p["k"],EndOfLinePreference:p["l"],EndOfLineSequence:p["m"],MinimapPosition:p["r"],MouseTargetType:p["s"],OverlayWidgetPositionPreference:p["t"],OverviewRulerLane:p["u"],RenderLineNumbersType:p["v"],RenderMinimap:p["w"],ScrollbarVisibility:p["y"],ScrollType:p["x"],TextEditorCursorBlinkingStyle:p["D"],TextEditorCursorStyle:p["E"],TrackedRangeStickiness:p["F"],WrappingIndent:p["G"],ConfigurationChangedEvent:a["a"],BareFontInfo:s["a"],FontInfo:s["b"],TextModelResolvedOptions:c["e"],FindMatch:c["b"],EditorType:u["a"],EditorOptions:a["e"]}}},4816:function(e,t,n){"use strict";n.r(t);n("41a0");var o=n("0f70"),r=n("11f7"),i=n("6653"),a=n("31df"),s=n("3813"),u=n("1b7d"),c=n("a666"),d=n("30db"),l=n("3742"),f=n("6d8e"),g=n("b2cc"),h=n("c101"),p=n("93ba"),m=n("4fc3"),b=n("0a0f"),v=n("6dec"),y=n("5bd7"),_=n("303e"),k=n("b7d0"),w=n("03d9"),C=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),S=function(e,t,n,o){var r,i=arguments.length,a=i<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)a=Reflect.decorate(e,t,n,o);else for(var s=e.length-1;s>=0;s--)(r=e[s])&&(a=(i<3?r(a):i>3?r(t,n,a):r(t,n))||a);return i>3&&a&&Object.defineProperty(t,n,a),a},O=function(e,t){return function(n,o){t(n,o,e)}},D=new m["d"]("accessibilityHelpWidgetVisible",!1),E=function(e){function t(t,n){var o=e.call(this)||this;return o._editor=t,o._widget=o._register(n.createInstance(T,o._editor)),o}return C(t,e),t.get=function(e){return e.getContribution(t.ID)},t.prototype.show=function(){this._widget.show()},t.prototype.hide=function(){this._widget.hide()},t.ID="editor.contrib.accessibilityHelpController",t=S([O(1,b["a"])],t),t}(c["a"]);function L(e,t){return e&&0!==e.length?1===e.length?t?l["r"](w["a"].singleSelectionRange,e[0].positionLineNumber,e[0].positionColumn,t):l["r"](w["a"].singleSelection,e[0].positionLineNumber,e[0].positionColumn):t?l["r"](w["a"].multiSelectionRange,e.length,t):e.length>0?l["r"](w["a"].multiSelection,e.length):"":w["a"].noSelection}var T=function(e){function t(t,n,o,a){var u=e.call(this)||this;return u._contextKeyService=n,u._keybindingService=o,u._openerService=a,u._editor=t,u._isVisibleKey=D.bindTo(u._contextKeyService),u._domNode=Object(i["b"])(document.createElement("div")),u._domNode.setClassName("accessibilityHelpWidget"),u._domNode.setDisplay("none"),u._domNode.setAttribute("role","dialog"),u._domNode.setAttribute("aria-hidden","true"),u._contentDomNode=Object(i["b"])(document.createElement("div")),u._contentDomNode.setAttribute("role","document"),u._domNode.appendChild(u._contentDomNode),u._isVisible=!1,u._register(u._editor.onDidLayoutChange((function(){u._isVisible&&u._layout()}))),u._register(r["o"](u._contentDomNode.domNode,"keydown",(function(e){if(u._isVisible&&(e.equals(2083)&&(Object(s["a"])(w["a"].emergencyConfOn),u._editor.updateOptions({accessibilitySupport:"on"}),r["t"](u._contentDomNode.domNode),u._buildContent(),u._contentDomNode.domNode.focus(),e.preventDefault(),e.stopPropagation()),e.equals(2086))){Object(s["a"])(w["a"].openingDocs);var t=u._editor.getRawOptions().accessibilityHelpUrl;"undefined"===typeof t&&(t="https://go.microsoft.com/fwlink/?linkid=852450"),u._openerService.open(f["a"].parse(t)),e.preventDefault(),e.stopPropagation()}}))),u.onblur(u._contentDomNode.domNode,(function(){u.hide()})),u._editor.addOverlayWidget(u),u}return C(t,e),t.prototype.dispose=function(){this._editor.removeOverlayWidget(this),e.prototype.dispose.call(this)},t.prototype.getId=function(){return t.ID},t.prototype.getDomNode=function(){return this._domNode.domNode},t.prototype.getPosition=function(){return{preference:null}},t.prototype.show=function(){this._isVisible||(this._isVisible=!0,this._isVisibleKey.set(!0),this._layout(),this._domNode.setDisplay("block"),this._domNode.setAttribute("aria-hidden","false"),this._contentDomNode.domNode.tabIndex=0,this._buildContent(),this._contentDomNode.domNode.focus())},t.prototype._descriptionForCommand=function(e,t,n){var o=this._keybindingService.lookupKeybinding(e);return o?l["r"](t,o.getAriaLabel()):l["r"](n,e)},t.prototype._buildContent=function(){var e=this._editor.getOptions(),t=this._editor.getSelections(),n=0;if(t){var o=this._editor.getModel();o&&t.forEach((function(e){n+=o.getValueLengthInRange(e)}))}var r=L(t,n);e.get(45)?e.get(68)?r+=w["a"].readonlyDiffEditor:r+=w["a"].editableDiffEditor:e.get(68)?r+=w["a"].readonlyEditor:r+=w["a"].editableEditor;var i=d["e"]?w["a"].changeConfigToOnMac:w["a"].changeConfigToOnWinLinux;switch(e.get(2)){case 0:r+="\n\n - "+i;break;case 2:r+="\n\n - "+w["a"].auto_on;break;case 1:r+="\n\n - "+w["a"].auto_off,r+=" "+i;break}e.get(106)?r+="\n\n - "+this._descriptionForCommand(p["ToggleTabFocusModeAction"].ID,w["a"].tabFocusModeOnMsg,w["a"].tabFocusModeOnMsgNoKb):r+="\n\n - "+this._descriptionForCommand(p["ToggleTabFocusModeAction"].ID,w["a"].tabFocusModeOffMsg,w["a"].tabFocusModeOffMsgNoKb);var s=d["e"]?w["a"].openDocMac:w["a"].openDocWinLinux;r+="\n\n - "+s,r+="\n\n"+w["a"].outroMsg,this._contentDomNode.domNode.appendChild(Object(a["b"])(r)),this._contentDomNode.domNode.setAttribute("aria-label",r)},t.prototype.hide=function(){this._isVisible&&(this._isVisible=!1,this._isVisibleKey.reset(),this._domNode.setDisplay("none"),this._domNode.setAttribute("aria-hidden","true"),this._contentDomNode.domNode.tabIndex=-1,r["t"](this._contentDomNode.domNode),this._editor.focus())},t.prototype._layout=function(){var e=this._editor.getLayoutInfo(),n=Math.max(5,Math.min(t.WIDTH,e.width-40)),o=Math.max(5,Math.min(t.HEIGHT,e.height-40));this._domNode.setWidth(n),this._domNode.setHeight(o);var r=Math.round((e.height-o)/2);this._domNode.setTop(r);var i=Math.round((e.width-n)/2);this._domNode.setLeft(i)},t.ID="editor.contrib.accessibilityHelpWidget",t.WIDTH=500,t.HEIGHT=300,t=S([O(1,m["c"]),O(2,v["a"]),O(3,y["a"])],t),t}(u["a"]),I=function(e){function t(){return e.call(this,{id:"editor.action.showAccessibilityHelp",label:w["a"].showAccessibilityHelpAction,alias:"Show Accessibility Help",precondition:void 0,kbOpts:{kbExpr:h["a"].focus,primary:o["i"]?2107:571,weight:100}})||this}return C(t,e),t.prototype.run=function(e,t){var n=E.get(t);n&&n.show()},t}(g["b"]);Object(g["h"])(E.ID,E),Object(g["f"])(I);var M=g["c"].bindToContribution(E.get);Object(g["g"])(new M({id:"closeAccessibilityHelp",precondition:D,handler:function(e){return e.hide()},kbOpts:{weight:200,kbExpr:h["a"].focus,primary:9,secondary:[1033]}})),Object(k["e"])((function(e,t){var n=e.getColor(_["Q"]);n&&t.addRule(".monaco-editor .accessibilityHelpWidget { background-color: "+n+"; }");var o=e.getColor(_["S"]);o&&t.addRule(".monaco-editor .accessibilityHelpWidget { color: "+o+"; }");var r=e.getColor(_["hc"]);r&&t.addRule(".monaco-editor .accessibilityHelpWidget { box-shadow: 0 2px 8px "+r+"; }");var i=e.getColor(_["e"]);i&&t.addRule(".monaco-editor .accessibilityHelpWidget { border: 2px solid "+i+"; }")}))},5249:function(e,t,n){"use strict";n.d(t,"a",(function(){return ne}));var o=n("6a89"),r=n("4dc7"),i=n("b707"),a=n("70cb"),s=n("32a4"),u=n("6881"),c=n("caf5"),d=n("8f4b");function l(e,t){if(!t)return!1;if(!Array.isArray(t))return!1;for(var n=0,o=t;n<o.length;n++){var r=o[n];if(!e(r))return!1}return!0}function f(e,t){return"boolean"===typeof e?e:t}function g(e,t){return"string"===typeof e?e:t}function h(e){for(var t={},n=0,o=e;n<o.length;n++){var r=o[n];t[r]=!0}return t}function p(e,t){void 0===t&&(t=!1),t&&(e=e.map((function(e){return e.toLowerCase()})));var n=h(e);return t?function(e){return void 0!==n[e.toLowerCase()]&&n.hasOwnProperty(e.toLowerCase())}:function(e){return void 0!==n[e]&&n.hasOwnProperty(e)}}function m(e,t){var n=0;while(t.indexOf("@")>=0&&n<5)n++,t=t.replace(/@(\w+)/g,(function(n,o){var r="";if("string"===typeof e[o])r=e[o];else{if(!(e[o]&&e[o]instanceof RegExp))throw void 0===e[o]?d["a"](e,"language definition does not contain attribute '"+o+"', used at: "+t):d["a"](e,"attribute reference '"+o+"' must be a string, used at: "+t);r=e[o].source}return d["b"](r)?"":"(?:"+r+")"}));return new RegExp(t,e.ignoreCase?"i":"")}function b(e,t,n,o){if(o<0)return e;if(o<t.length)return t[o];if(o>=100){o-=100;var r=n.split(".");if(r.unshift(n),o<r.length)return r[o]}return null}function v(e,t,n,o){var r=-1,i=n,a=n.match(/^\$(([sS]?)(\d\d?)|#)(.*)$/);a&&(a[3]&&(r=parseInt(a[3]),a[2]&&(r+=100)),i=a[4]);var s,u="~",c=i;if(i&&0!==i.length?/^\w*$/.test(c)?u="==":(a=i.match(/^(@|!@|~|!~|==|!=)(.*)$/),a&&(u=a[1],c=a[2])):(u="!=",c=""),"~"!==u&&"!~"!==u||!/^(\w|\|)*$/.test(c))if("@"===u||"!@"===u){var f=e[c];if(!f)throw d["a"](e,"the @ match target '"+c+"' is not defined, in rule: "+t);if(!l((function(e){return"string"===typeof e}),f))throw d["a"](e,"the @ match target '"+c+"' must be an array of strings, in rule: "+t);var g=p(f,e.ignoreCase);s=function(e){return"@"===u?g(e):!g(e)}}else if("~"===u||"!~"===u)if(c.indexOf("$")<0){var h=m(e,"^"+c+"$");s=function(e){return"~"===u?h.test(e):!h.test(e)}}else s=function(t,n,o,r){var i=m(e,"^"+d["k"](e,c,n,o,r)+"$");return i.test(t)};else if(c.indexOf("$")<0){var v=d["d"](e,c);s=function(e){return"=="===u?e===v:e!==v}}else{var y=d["d"](e,c);s=function(t,n,o,r,i){var a=d["k"](e,y,n,o,r);return"=="===u?t===a:t!==a}}else{var _=p(c.split("|"),e.ignoreCase);s=function(e){return"~"===u?_(e):!_(e)}}return-1===r?{name:n,value:o,test:function(e,t,n,o){return s(e,e,t,n,o)}}:{name:n,value:o,test:function(e,t,n,o){var i=b(e,t,n,r);return s(i||"",e,t,n,o)}}}function y(e,t,n){if(n){if("string"===typeof n)return n;if(n.token||""===n.token){if("string"!==typeof n.token)throw d["a"](e,"a 'token' attribute must be of type string, in rule: "+t);var o={token:n.token};if(n.token.indexOf("$")>=0&&(o.tokenSubst=!0),"string"===typeof n.bracket)if("@open"===n.bracket)o.bracket=1;else{if("@close"!==n.bracket)throw d["a"](e,"a 'bracket' attribute must be either '@open' or '@close', in rule: "+t);o.bracket=-1}if(n.next){if("string"!==typeof n.next)throw d["a"](e,"the next state must be a string value in rule: "+t);var r=n.next;if(!/^(@pop|@push|@popall)$/.test(r)&&("@"===r[0]&&(r=r.substr(1)),r.indexOf("$")<0&&!d["j"](e,d["k"](e,r,"",[],""))))throw d["a"](e,"the next state '"+n.next+"' is not defined in rule: "+t);o.next=r}return"number"===typeof n.goBack&&(o.goBack=n.goBack),"string"===typeof n.switchTo&&(o.switchTo=n.switchTo),"string"===typeof n.log&&(o.log=n.log),"string"===typeof n.nextEmbedded&&(o.nextEmbedded=n.nextEmbedded,e.usesEmbedded=!0),o}if(Array.isArray(n)){for(var i=[],a=0,s=n.length;a<s;a++)i[a]=y(e,t,n[a]);return{group:i}}if(n.cases){var u=[];for(var c in n.cases)if(n.cases.hasOwnProperty(c)){var l=y(e,t,n.cases[c]);"@default"===c||"@"===c||""===c?u.push({test:void 0,value:l,name:c}):"@eos"===c?u.push({test:function(e,t,n,o){return o},value:l,name:c}):u.push(v(e,t,c,l))}var f=e.defaultToken;return{test:function(e,t,n,o){for(var r=0,i=u;r<i.length;r++){var a=i[r],s=!a.test||a.test(e,t,n,o);if(s)return a.value}return f}}}throw d["a"](e,"an action must be a string, an object with a 'token' or 'cases' attribute, or an array of actions; in rule: "+t)}return{token:""}}var _=function(){function e(e){this.regex=new RegExp(""),this.action={token:""},this.matchOnlyAtLineStart=!1,this.name="",this.name=e}return e.prototype.setRegex=function(e,t){var n;if("string"===typeof t)n=t;else{if(!(t instanceof RegExp))throw d["a"](e,"rules must start with a match string or regular expression: "+this.name);n=t.source}this.matchOnlyAtLineStart=n.length>0&&"^"===n[0],this.name=this.name+": "+n,this.regex=m(e,"^(?:"+(this.matchOnlyAtLineStart?n.substr(1):n)+")")},e.prototype.setAction=function(e,t){this.action=y(e,this.name,t)},e}();function k(e,t){if(!t||"object"!==typeof t)throw new Error("Monarch: expecting a language definition object");var n={};n.languageId=e,n.noThrow=!1,n.maxStack=100,n.start="string"===typeof t.start?t.start:null,n.ignoreCase=f(t.ignoreCase,!1),n.tokenPostfix=g(t.tokenPostfix,"."+n.languageId),n.defaultToken=g(t.defaultToken,"source"),n.usesEmbedded=!1;var o=t;function r(e,i,a){for(var s=0,u=a;s<u.length;s++){var c=u[s],l=c.include;if(l){if("string"!==typeof l)throw d["a"](n,"an 'include' attribute must be a string at: "+e);if("@"===l[0]&&(l=l.substr(1)),!t.tokenizer[l])throw d["a"](n,"include target '"+l+"' is not defined at: "+e);r(e+"."+l,i,t.tokenizer[l])}else{var g=new _(e);if(Array.isArray(c)&&c.length>=1&&c.length<=3)if(g.setRegex(o,c[0]),c.length>=3)if("string"===typeof c[1])g.setAction(o,{token:c[1],next:c[2]});else{if("object"!==typeof c[1])throw d["a"](n,"a next state as the last element of a rule can only be given if the action is either an object or a string, at: "+e);var h=c[1];h.next=c[2],g.setAction(o,h)}else g.setAction(o,c[1]);else{if(!c.regex)throw d["a"](n,"a rule must either be an array, or an object with a 'regex' or 'include' field at: "+e);c.name&&"string"===typeof c.name&&(g.name=c.name),c.matchOnlyAtStart&&(g.matchOnlyAtLineStart=f(c.matchOnlyAtLineStart,!1)),g.setRegex(o,c.regex),g.setAction(o,c.action)}i.push(g)}}}if(o.languageId=e,o.ignoreCase=n.ignoreCase,o.noThrow=n.noThrow,o.usesEmbedded=n.usesEmbedded,o.stateNames=t.tokenizer,o.defaultToken=n.defaultToken,!t.tokenizer||"object"!==typeof t.tokenizer)throw d["a"](n,"a language definition must define the 'tokenizer' attribute as an object");for(var i in n.tokenizer=[],t.tokenizer)if(t.tokenizer.hasOwnProperty(i)){n.start||(n.start=i);var a=t.tokenizer[i];n.tokenizer[i]=new Array,r("tokenizer."+i,n.tokenizer[i],a)}if(n.usesEmbedded=o.usesEmbedded,t.brackets){if(!Array.isArray(t.brackets))throw d["a"](n,"the 'brackets' attribute must be defined as an array")}else t.brackets=[{open:"{",close:"}",token:"delimiter.curly"},{open:"[",close:"]",token:"delimiter.square"},{open:"(",close:")",token:"delimiter.parenthesis"},{open:"<",close:">",token:"delimiter.angle"}];for(var s=[],u=0,c=t.brackets;u<c.length;u++){var l=c[u],h=l;if(h&&Array.isArray(h)&&3===h.length&&(h={token:h[2],open:h[0],close:h[1]}),h.open===h.close)throw d["a"](n,"open and close brackets in a 'brackets' attribute must be different: "+h.open+"\n hint: use the 'bracket' attribute if matching on equal brackets is required.");if("string"!==typeof h.open||"string"!==typeof h.token||"string"!==typeof h.close)throw d["a"](n,"every element in the 'brackets' array must be a '{open,close,token}' object or array");s.push({token:h.token+n.tokenPostfix,open:d["d"](n,h.open),close:d["d"](n,h.close)})}return n.brackets=s,n.noThrow=!0,n}var w=n("d0c6");function C(e){s["a"].registerLanguage(e)}function S(){var e=[];return e=e.concat(s["a"].getLanguages()),e}function O(e){var t=c["b"].modeService.get().getLanguageIdentifier(e);return t?t.id:0}function D(e,t){var n=c["b"].modeService.get().onDidCreateMode((function(o){o.getId()===e&&(n.dispose(),t())}));return n}function E(e,t){var n=c["b"].modeService.get().getLanguageIdentifier(e);if(!n)throw new Error("Cannot set configuration for unknown language "+e);return a["a"].register(n,t)}var L=function(){function e(e){this._actual=e}return e.prototype.getInitialState=function(){return this._actual.getInitialState()},e.prototype.tokenize=function(e,t,n){throw new Error("Not supported!")},e.prototype.tokenize2=function(e,t){var n=this._actual.tokenizeEncoded(e,t);return new r["c"](n.tokens,n.endState)},e}(),T=function(){function e(e,t,n){this._standaloneThemeService=e,this._languageIdentifier=t,this._actual=n}return e.prototype.getInitialState=function(){return this._actual.getInitialState()},e.prototype._toClassicTokens=function(e,t,n){for(var o=[],i=0,a=0,s=e.length;a<s;a++){var u=e[a],c=u.startIndex;0===a?c=0:c<i&&(c=i),o[a]=new r["a"](c+n,u.scopes,t),i=c}return o},e.prototype.tokenize=function(e,t,n){var o,i=this._actual.tokenize(e,t),a=this._toClassicTokens(i.tokens,this._languageIdentifier.language,n);return o=i.endState.equals(t)?t:i.endState,new r["b"](a,o)},e.prototype._toBinaryTokens=function(e,t){for(var n=this._languageIdentifier.id,o=this._standaloneThemeService.getTheme().tokenTheme,r=[],i=0,a=0,s=0,u=e.length;s<u;s++){var c=e[s],d=o.match(n,c.scopes);if(!(i>0&&r[i-1]===d)){var l=c.startIndex;0===s?l=0:l<a&&(l=a),r[i++]=l+t,r[i++]=d,a=l}}var f=new Uint32Array(i);for(s=0;s<i;s++)f[s]=r[s];return f},e.prototype.tokenize2=function(e,t,n){var o,i=this._actual.tokenize(e,t),a=this._toBinaryTokens(i.tokens,n);return o=i.endState.equals(t)?t:i.endState,new r["c"](a,o)},e}();function I(e){return"tokenizeEncoded"in e}function M(e){return e&&"function"===typeof e.then}function x(e,t){var n=c["b"].modeService.get().getLanguageIdentifier(e);if(!n)throw new Error("Cannot set tokens provider for unknown language "+e);var o=function(e){return I(e)?new L(e):new T(c["b"].standaloneThemeService.get(),n,e)};return M(t)?i["B"].registerPromise(e,t.then((function(e){return o(e)}))):i["B"].register(e,o(t))}function j(e,t){var n=function(t){return Object(w["b"])(c["b"].modeService.get(),c["b"].standaloneThemeService.get(),e,k(e,t))};return M(t)?i["B"].registerPromise(e,t.then((function(e){return n(e)}))):i["B"].register(e,n(t))}function A(e,t){return i["u"].register(e,t)}function P(e,t){return i["v"].register(e,t)}function F(e,t){return i["x"].register(e,t)}function N(e,t){return i["p"].register(e,{provideHover:function(e,n,r){var i=e.getWordAtPosition(n);return Promise.resolve(t.provideHover(e,n,r)).then((function(e){if(e)return!e.range&&i&&(e.range=new o["a"](n.lineNumber,i.startColumn,n.lineNumber,i.endColumn)),e.range||(e.range=new o["a"](n.lineNumber,n.column,n.lineNumber,n.column)),e}))}})}function R(e,t){return i["m"].register(e,t)}function z(e,t){return i["i"].register(e,t)}function H(e,t){return i["f"].register(e,t)}function K(e,t){return i["q"].register(e,t)}function B(e,t){return i["C"].register(e,t)}function W(e,t){return i["b"].register(e,t)}function q(e,t){return i["a"].register(e,{provideCodeActions:function(e,n,r,i){var a=c["b"].markerService.get().read({resource:e.uri}).filter((function(e){return o["a"].areIntersectingOrTouching(e,n)}));return t.provideCodeActions(e,n,{markers:a,only:r.only},i)}})}function V(e,t){return i["g"].register(e,t)}function G(e,t){return i["j"].register(e,t)}function U(e,t){return i["t"].register(e,t)}function $(e,t){return i["s"].register(e,t)}function Q(e,t){return i["d"].register(e,t)}function J(e,t){return i["c"].register(e,t)}function Y(e,t){return i["o"].register(e,t)}function X(e,t){return i["e"].register(e,t)}function Z(e,t){return i["w"].register(e,t)}function ee(e,t){return i["l"].register(e,t)}function te(e,t){return i["k"].register(e,t)}function ne(){return{register:C,getLanguages:S,onLanguage:D,getEncodedLanguageId:O,setLanguageConfiguration:E,setTokensProvider:x,setMonarchTokensProvider:j,registerReferenceProvider:A,registerRenameProvider:P,registerCompletionItemProvider:Q,registerSignatureHelpProvider:F,registerHoverProvider:N,registerDocumentSymbolProvider:R,registerDocumentHighlightProvider:z,registerDefinitionProvider:H,registerImplementationProvider:K,registerTypeDefinitionProvider:B,registerCodeLensProvider:W,registerCodeActionProvider:q,registerDocumentFormattingEditProvider:V,registerDocumentRangeFormattingEditProvider:G,registerOnTypeFormattingEditProvider:U,registerLinkProvider:$,registerColorProvider:J,registerFoldingRangeProvider:Y,registerDeclarationProvider:X,registerSelectionRangeProvider:Z,registerDocumentSemanticTokensProvider:ee,registerDocumentRangeSemanticTokensProvider:te,DocumentHighlightKind:u["i"],CompletionItemKind:u["c"],CompletionItemTag:u["d"],CompletionItemInsertTextRule:u["b"],SymbolKind:u["B"],SymbolTag:u["C"],IndentAction:u["n"],CompletionTriggerKind:u["e"],SignatureHelpTriggerKind:u["A"],FoldingRangeKind:i["n"]}}},5900:function(e,t,n){"use strict";n.r(t),n.d(t,"SymbolEntry",(function(){return m})),n.d(t,"QuickOutlineAction",(function(){return b}));n("42f0"),n("93be"),n("8ea8");var o=n("2504"),r=n("7e93"),i=n("3742"),a=n("469c"),s=n("b2cc"),u=n("6a89"),c=n("c101"),d=n("b707"),l=n("f86e"),f=n("af33"),g=n("03d9"),h=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),p=":",m=function(e){function t(t,n,o,r,i,a,s){var u=e.call(this)||this;return u.name=t,u.type=n,u.description=o,u.range=r,u.setHighlights(i),u.editor=a,u.decorator=s,u}return h(t,e),t.prototype.getLabel=function(){return this.name},t.prototype.getAriaLabel=function(){return i["r"](g["e"].entryAriaLabel,this.name)},t.prototype.getIcon=function(){return this.type},t.prototype.getDescription=function(){return this.description},t.prototype.getType=function(){return this.type},t.prototype.getRange=function(){return this.range},t.prototype.run=function(e,t){return 1===e?this.runOpen(t):this.runPreview()},t.prototype.runOpen=function(e){var t=this.toSelection();return this.editor.setSelection(t),this.editor.revealRangeInCenter(t,0),this.editor.focus(),!0},t.prototype.runPreview=function(){var e=this.toSelection();return this.editor.revealRangeInCenter(e,0),this.decorator.decorateLine(this.range,this.editor),!1},t.prototype.toSelection=function(){return new u["a"](this.range.startLineNumber,this.range.startColumn||1,this.range.startLineNumber,this.range.startColumn||1)},t}(a["b"]),b=function(e){function t(){return e.call(this,g["e"].quickOutlineActionInput,{id:"editor.action.quickOutline",label:g["e"].quickOutlineActionLabel,alias:"Go to Symbol...",precondition:c["a"].hasDocumentSymbolProvider,kbOpts:{kbExpr:c["a"].focus,primary:3117,weight:100},contextMenuOpts:{group:"navigation",order:3}})||this}return h(t,e),t.prototype.run=function(e,t){var n=this;if(t.hasModel()){var r=t.getModel();if(d["m"].has(r))return Object(l["a"])(r,!0,o["a"].None).then((function(e){0!==e.length&&n._run(t,e)}))}},t.prototype._run=function(e,t){var n=this;this._show(this.getController(e),{getModel:function(o){return new a["c"](n.toQuickOpenEntries(e,t,o))},getAutoFocus:function(e){return 0===e.indexOf(p)&&(e=e.substr(p.length)),{autoFocusPrefixMatch:e,autoFocusFirstEntry:!!e}}})},t.prototype.symbolEntry=function(e,t,n,o,r,i,a){return new m(e,t,n,u["a"].lift(o),r,i,a)},t.prototype.toQuickOpenEntries=function(e,t,n){var o=this.getController(e),a=[],s=n;0===n.indexOf(p)&&(s=s.substr(p.length));for(var u=0,c=t;u<c.length;u++){var l=c[u],f=i["Q"](l.name),h=Object(r["f"])(s,f);if(h){var m=void 0;l.containerName&&(m=l.containerName),a.push(this.symbolEntry(f,d["z"].toCssClassName(l.kind),m,l.range,h,e,o))}}if(n&&(a=0===n.indexOf(p)?a.sort(this.sortScoped.bind(this,n.toLowerCase())):a.sort(this.sortNormal.bind(this,n.toLowerCase()))),a.length>0&&0===n.indexOf(p)){for(var b=null,v=null,y=0,_=0;_<a.length;_++){var k=a[_];b!==k.getType()?(v&&v.setGroupLabel(this.typeToLabel(b||"",y)),b=k.getType(),v=k,y=1,k.setShowBorder(_>0)):y++}v&&v.setGroupLabel(this.typeToLabel(b||"",y))}else a.length>0&&a[0].setGroupLabel(i["r"](g["e"]._symbols_,a.length));return a},t.prototype.typeToLabel=function(e,t){switch(e){case"module":return i["r"](g["e"]._modules_,t);case"class":return i["r"](g["e"]._class_,t);case"interface":return i["r"](g["e"]._interface_,t);case"method":return i["r"](g["e"]._method_,t);case"function":return i["r"](g["e"]._function_,t);case"property":return i["r"](g["e"]._property_,t);case"variable":return i["r"](g["e"]._variable_,t);case"var":return i["r"](g["e"]._variable2_,t);case"constructor":return i["r"](g["e"]._constructor_,t);case"call":return i["r"](g["e"]._call_,t)}return e},t.prototype.sortNormal=function(e,t,n){var o=t.getLabel().toLowerCase(),r=n.getLabel().toLowerCase(),i=o.localeCompare(r);if(0!==i)return i;var a=t.getRange(),s=n.getRange();return a.startLineNumber-s.startLineNumber},t.prototype.sortScoped=function(e,t,n){e=e.substr(p.length);var o=t.getType(),r=n.getType(),i=o.localeCompare(r);if(0!==i)return i;if(e){var a=t.getLabel().toLowerCase(),s=n.getLabel().toLowerCase(),u=a.localeCompare(s);if(0!==u)return u}var c=t.getRange(),d=n.getRange();return c.startLineNumber-d.startLineNumber},t}(f["a"]);Object(s["f"])(b)},"722f":function(e,t,n){},7367:function(e,t,n){"use strict";n.r(t),n.d(t,"LanguageServiceDefaultsImpl",(function(){return r}));n("33f9");var o=monaco.Emitter,r=function(){function e(e,t,n){this._onDidChange=new o,this._languageId=e,this.setOptions(t),this.setModeConfiguration(n)}return Object.defineProperty(e.prototype,"onDidChange",{get:function(){return this._onDidChange.event},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"languageId",{get:function(){return this._languageId},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"options",{get:function(){return this._options},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"modeConfiguration",{get:function(){return this._modeConfiguration},enumerable:!0,configurable:!0}),e.prototype.setOptions=function(e){this._options=e||Object.create(null),this._onDidChange.fire(this)},e.prototype.setModeConfiguration=function(e){this._modeConfiguration=e||Object.create(null),this._onDidChange.fire(this)},e}(),i={tabSize:4,insertSpaces:!1,wrapLineLength:120,unformatted:'default": "a, abbr, acronym, b, bdo, big, br, button, cite, code, dfn, em, i, img, input, kbd, label, map, object, q, samp, select, small, span, strong, sub, sup, textarea, tt, var',contentUnformatted:"pre",indentInnerHtml:!1,preserveNewLines:!0,maxPreserveNewLines:null,indentHandlebars:!1,endWithNewline:!1,extraLiners:"head, body, /html",wrapAttributes:"auto"},a={format:i,suggest:{html5:!0,angular1:!0,ionic:!0}},s={format:i,suggest:{html5:!0}},u={format:i,suggest:{html5:!0,razor:!0}};function c(e){return{completionItems:!0,hovers:!0,documentSymbols:!0,links:!0,documentHighlights:!0,rename:!0,colors:!0,foldingRanges:!0,selectionRanges:!0,diagnostics:e===d,documentFormattingEdits:e===d,documentRangeFormattingEdits:e===d}}var d="html",l="handlebars",f="razor",g=new r(d,a,c(d)),h=new r(l,s,c(l)),p=new r(f,u,c(f));function m(){return{htmlDefaults:g,razorDefaults:p,handlebarDefaults:h}}function b(){return Promise.all([n.e("chunk-1e2736cd"),n.e("chunk-cc039926")]).then(n.bind(null,"fa5b"))}monaco.languages.html=m(),monaco.languages.onLanguage(d,(function(){b().then((function(e){return e.setupMode(g)}))})),monaco.languages.onLanguage(l,(function(){b().then((function(e){return e.setupMode(h)}))})),monaco.languages.onLanguage(f,(function(){b().then((function(e){return e.setupMode(p)}))}))},8090:function(e,t,n){"use strict";n.r(t);n("133b");var o=n("ceb8"),r=n("a666"),i=n("3742"),a=n("b2cc"),s=n("b707"),u=n("8bf1"),c=n("5818"),d=n("b1ca"),l=n("303e"),f=n("b7d0"),g=n("03d9"),h=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),p=function(e,t,n,o){var r,i=arguments.length,a=i<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)a=Reflect.decorate(e,t,n,o);else for(var s=e.length-1;s>=0;s--)(r=e[s])&&(a=(i<3?r(a):i>3?r(t,n,a):r(t,n))||a);return i>3&&a&&Object.defineProperty(t,n,a),a},m=function(e,t){return function(n,o){t(n,o,e)}},b=function(e){function t(t,n,o){var r=e.call(this)||this;return r._editor=t,r._modeService=o,r._widget=null,r._register(r._editor.onDidChangeModel((function(e){return r.stop()}))),r._register(r._editor.onDidChangeModelLanguage((function(e){return r.stop()}))),r._register(s["B"].onDidChange((function(e){return r.stop()}))),r}return h(t,e),t.get=function(e){return e.getContribution(t.ID)},t.prototype.dispose=function(){this.stop(),e.prototype.dispose.call(this)},t.prototype.launch=function(){this._widget||this._editor.hasModel()&&(this._widget=new k(this._editor,this._modeService))},t.prototype.stop=function(){this._widget&&(this._widget.dispose(),this._widget=null)},t.ID="editor.contrib.inspectTokens",t=p([m(1,d["a"]),m(2,c["a"])],t),t}(r["a"]),v=function(e){function t(){return e.call(this,{id:"editor.action.inspectTokens",label:g["c"].inspectTokensAction,alias:"Developer: Inspect Tokens",precondition:void 0})||this}return h(t,e),t.prototype.run=function(e,t){var n=b.get(t);n&&n.launch()},t}(a["b"]);function y(e){for(var t="",n=0,o=e.length;n<o;n++){var r=e.charCodeAt(n);switch(r){case 9:t+="&rarr;";break;case 32:t+="&middot;";break;case 60:t+="&lt;";break;case 62:t+="&gt;";break;case 38:t+="&amp;";break;default:t+=String.fromCharCode(r)}}return t}function _(e){var t=s["B"].get(e.language);return t||{getInitialState:function(){return u["c"]},tokenize:function(t,n,o){return Object(u["d"])(e.language,t,n,o)},tokenize2:function(t,n,o){return Object(u["e"])(e.id,t,n,o)}}}var k=function(e){function t(t,n){var o=e.call(this)||this;return o.allowEditorOverflow=!0,o._editor=t,o._modeService=n,o._model=o._editor.getModel(),o._domNode=document.createElement("div"),o._domNode.className="tokens-inspect-widget",o._tokenizationSupport=_(o._model.getLanguageIdentifier()),o._compute(o._editor.getPosition()),o._register(o._editor.onDidChangeCursorPosition((function(e){return o._compute(o._editor.getPosition())}))),o._editor.addContentWidget(o),o}return h(t,e),t.prototype.dispose=function(){this._editor.removeContentWidget(this),e.prototype.dispose.call(this)},t.prototype.getId=function(){return t._ID},t.prototype._compute=function(e){for(var t=this._getTokensAtLine(e.lineNumber),n=0,r=t.tokens1.length-1;r>=0;r--){var a=t.tokens1[r];if(e.column-1>=a.offset){n=r;break}}var s=0;for(r=t.tokens2.length>>>1;r>=0;r--)if(e.column-1>=t.tokens2[r<<1]){s=r;break}var u="",c=this._model.getLineContent(e.lineNumber),d="";if(n<t.tokens1.length){var l=t.tokens1[n].offset,f=n+1<t.tokens1.length?t.tokens1[n+1].offset:c.length;d=c.substring(l,f)}u+='<h2 class="tm-token">'+y(d)+'<span class="tm-token-length">('+d.length+" "+(1===d.length?"char":"chars")+")</span></h2>",u+='<hr class="tokens-inspect-separator" style="clear:both"/>';var g=this._decodeMetadata(t.tokens2[1+(s<<1)]);u+='<table class="tm-metadata-table"><tbody>',u+='<tr><td class="tm-metadata-key">language</td><td class="tm-metadata-value">'+Object(i["o"])(g.languageIdentifier.language)+"</td>",u+='<tr><td class="tm-metadata-key">token type</td><td class="tm-metadata-value">'+this._tokenTypeToString(g.tokenType)+"</td>",u+='<tr><td class="tm-metadata-key">font style</td><td class="tm-metadata-value">'+this._fontStyleToString(g.fontStyle)+"</td>",u+='<tr><td class="tm-metadata-key">foreground</td><td class="tm-metadata-value">'+o["a"].Format.CSS.formatHex(g.foreground)+"</td>",u+='<tr><td class="tm-metadata-key">background</td><td class="tm-metadata-value">'+o["a"].Format.CSS.formatHex(g.background)+"</td>",u+="</tbody></table>",u+='<hr class="tokens-inspect-separator"/>',n<t.tokens1.length&&(u+='<span class="tm-token-type">'+Object(i["o"])(t.tokens1[n].type)+"</span>"),this._domNode.innerHTML=u,this._editor.layoutContentWidget(this)},t.prototype._decodeMetadata=function(e){var t=s["B"].getColorMap(),n=s["A"].getLanguageId(e),o=s["A"].getTokenType(e),r=s["A"].getFontStyle(e),i=s["A"].getForeground(e),a=s["A"].getBackground(e);return{languageIdentifier:this._modeService.getLanguageIdentifier(n),tokenType:o,fontStyle:r,foreground:t[i],background:t[a]}},t.prototype._tokenTypeToString=function(e){switch(e){case 0:return"Other";case 1:return"Comment";case 2:return"String";case 4:return"RegEx"}return"??"},t.prototype._fontStyleToString=function(e){var t="";return 1&e&&(t+="italic "),2&e&&(t+="bold "),4&e&&(t+="underline "),0===t.length&&(t="---"),t},t.prototype._getTokensAtLine=function(e){var t=this._getStateBeforeLine(e),n=this._tokenizationSupport.tokenize(this._model.getLineContent(e),t,0),o=this._tokenizationSupport.tokenize2(this._model.getLineContent(e),t,0);return{startState:t,tokens1:n.tokens,tokens2:o.tokens,endState:n.endState}},t.prototype._getStateBeforeLine=function(e){for(var t=this._tokenizationSupport.getInitialState(),n=1;n<e;n++){var o=this._tokenizationSupport.tokenize(this._model.getLineContent(n),t,0);t=o.endState}return t},t.prototype.getDomNode=function(){return this._domNode},t.prototype.getPosition=function(){return{position:this._editor.getPosition(),preference:[2,1]}},t._ID="editor.contrib.inspectTokensWidget",t}(r["a"]);Object(a["h"])(b.ID,b),Object(a["f"])(v),Object(f["e"])((function(e,t){var n=e.getColor(l["B"]);if(n){var o=e.type===f["b"]?2:1;t.addRule(".monaco-editor .tokens-inspect-widget { border: "+o+"px solid "+n+"; }"),t.addRule(".monaco-editor .tokens-inspect-widget .tokens-inspect-separator { background-color: "+n+"; }")}var r=e.getColor(l["A"]);r&&t.addRule(".monaco-editor .tokens-inspect-widget { background-color: "+r+"; }");var i=e.getColor(l["C"]);i&&t.addRule(".monaco-editor .tokens-inspect-widget { color: "+i+"; }")}))},"8f4b":function(e,t,n){"use strict";function o(e){return Array.isArray(e)}function r(e){return!o(e)}function i(e){return"string"===typeof e}function a(e){return!i(e)}function s(e){return!e}function u(e,t){return e.ignoreCase&&t?t.toLowerCase():t}function c(e){return e.replace(/[&<>'"_]/g,"-")}function d(e,t){console.log(e.languageId+": "+t)}function l(e,t){return new Error(e.languageId+": "+t)}function f(e,t,n,o,r){var i=/\$((\$)|(#)|(\d\d?)|[sS](\d\d?)|@(\w+))/g,a=null;return t.replace(i,(function(t,i,c,d,l,f,g,h,p){return s(c)?s(d)?!s(l)&&l<o.length?u(e,o[l]):!s(g)&&e&&"string"===typeof e[g]?e[g]:(null===a&&(a=r.split("."),a.unshift(r)),!s(f)&&f<a.length?u(e,a[f]):""):u(e,n):"$"}))}function g(e,t){var n=t;while(n&&n.length>0){var o=e.tokenizer[n];if(o)return o;var r=n.lastIndexOf(".");n=r<0?null:n.substr(0,r)}return null}function h(e,t){var n=t;while(n&&n.length>0){var o=e.stateNames[n];if(o)return!0;var r=n.lastIndexOf(".");n=r<0?null:n.substr(0,r)}return!1}n.d(t,"e",(function(){return r})),n.d(t,"g",(function(){return i})),n.d(t,"f",(function(){return a})),n.d(t,"b",(function(){return s})),n.d(t,"d",(function(){return u})),n.d(t,"i",(function(){return c})),n.d(t,"h",(function(){return d})),n.d(t,"a",(function(){return l})),n.d(t,"k",(function(){return f})),n.d(t,"c",(function(){return g})),n.d(t,"j",(function(){return h}))},a222:function(e,t,n){"use strict";n.r(t),n.d(t,"IPadShowKeyboard",(function(){return u}));n("722f");var o=n("0f70"),r=n("11f7"),i=n("a666"),a=n("b2cc"),s=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),u=function(e){function t(t){var n=e.call(this)||this;return n.editor=t,n.widget=null,o["j"]&&(n._register(t.onDidChangeConfiguration((function(){return n.update()}))),n.update()),n}return s(t,e),t.prototype.update=function(){var e=!this.editor.getOption(68);!this.widget&&e?this.widget=new c(this.editor):this.widget&&!e&&(this.widget.dispose(),this.widget=null)},t.prototype.dispose=function(){e.prototype.dispose.call(this),this.widget&&(this.widget.dispose(),this.widget=null)},t.ID="editor.contrib.iPadShowKeyboard",t}(i["a"]),c=function(e){function t(t){var n=e.call(this)||this;return n.editor=t,n._domNode=document.createElement("textarea"),n._domNode.className="iPadShowKeyboard",n._register(r["j"](n._domNode,"touchstart",(function(e){n.editor.focus()}))),n._register(r["j"](n._domNode,"focus",(function(e){n.editor.focus()}))),n.editor.addOverlayWidget(n),n}return s(t,e),t.prototype.dispose=function(){this.editor.removeOverlayWidget(this),e.prototype.dispose.call(this)},t.prototype.getId=function(){return t.ID},t.prototype.getDomNode=function(){return this._domNode},t.prototype.getPosition=function(){return{preference:1}},t.ID="editor.contrib.ShowKeyboardWidget",t}(i["a"]);Object(a["h"])(u.ID,u)},a79b:function(e,t,n){"use strict";n.r(t),n.d(t,"LanguageServiceDefaultsImpl",(function(){return r}));n("33f9");var o=monaco.Emitter,r=function(){function e(e,t,n){this._onDidChange=new o,this._languageId=e,this.setDiagnosticsOptions(t),this.setModeConfiguration(n)}return Object.defineProperty(e.prototype,"onDidChange",{get:function(){return this._onDidChange.event},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"languageId",{get:function(){return this._languageId},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"modeConfiguration",{get:function(){return this._modeConfiguration},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"diagnosticsOptions",{get:function(){return this._diagnosticsOptions},enumerable:!0,configurable:!0}),e.prototype.setDiagnosticsOptions=function(e){this._diagnosticsOptions=e||Object.create(null),this._onDidChange.fire(this)},e.prototype.setModeConfiguration=function(e){this._modeConfiguration=e||Object.create(null),this._onDidChange.fire(this)},e}(),i={validate:!0,allowComments:!0,schemas:[],enableSchemaRequest:!1},a={documentFormattingEdits:!0,documentRangeFormattingEdits:!0,completionItems:!0,hovers:!0,documentSymbols:!0,tokens:!0,colors:!0,foldingRanges:!0,diagnostics:!0,selectionRanges:!0},s=new r("json",i,a);function u(){return{jsonDefaults:s}}function c(){return n.e("chunk-e13e4362").then(n.bind(null,"47b9"))}monaco.languages.json=u(),monaco.languages.register({id:"json",extensions:[".json",".bowerrc",".jshintrc",".jscsrc",".eslintrc",".babelrc",".har"],aliases:["JSON","json"],mimetypes:["application/json"]}),monaco.languages.onLanguage("json",(function(){c().then((function(e){return e.setupMode(s)}))}))},a87d:function(e,t,n){},af33:function(e,t,n){"use strict";n.d(t,"a",(function(){return p}));n("a87d");var o=n("b2cc"),r=n("b57f"),i=n("11f7"),a=n("86d7"),s=n("303e"),u=n("a6d7"),c=function(){function e(e,t,n,o,r,i){this.codeEditor=e,this.themeService=i,this.visible=!1,this.domNode=document.createElement("div"),this.quickOpenWidget=new a["a"](this.domNode,{onOk:t,onCancel:n,onType:o},{inputPlaceHolder:void 0,inputAriaLabel:r.inputAriaLabel,keyboardSupport:!0}),this.styler=Object(u["d"])(this.quickOpenWidget,this.themeService,{pickerGroupForeground:s["W"]}),this.quickOpenWidget.create(),this.codeEditor.addOverlayWidget(this)}return e.prototype.setInput=function(e,t){this.quickOpenWidget.setInput(e,t)},e.prototype.getId=function(){return e.ID},e.prototype.getDomNode=function(){return this.domNode},e.prototype.destroy=function(){this.codeEditor.removeOverlayWidget(this),this.quickOpenWidget.dispose(),this.styler.dispose()},e.prototype.show=function(e){this.visible=!0;var t=this.codeEditor.getLayoutInfo();t&&this.quickOpenWidget.layout(new i["b"](t.width,t.height)),this.quickOpenWidget.show(e),this.codeEditor.layoutOverlayWidget(this)},e.prototype.getPosition=function(){return this.visible?{preference:2}:null},e.ID="editor.contrib.quickOpenEditorWidget",e}(),d=n("b7d0"),l=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),f=function(e,t,n,o){var r,i=arguments.length,a=i<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)a=Reflect.decorate(e,t,n,o);else for(var s=e.length-1;s>=0;s--)(r=e[s])&&(a=(i<3?r(a):i>3?r(t,n,a):r(t,n))||a);return i>3&&a&&Object.defineProperty(t,n,a),a},g=function(e,t){return function(n,o){t(n,o,e)}},h=function(){function e(e,t){this.themeService=t,this.widget=null,this.rangeHighlightDecorationId=null,this.lastKnownEditorSelection=null,this.editor=e}return e.get=function(t){return t.getContribution(e.ID)},e.prototype.dispose=function(){this.widget&&(this.widget.destroy(),this.widget=null)},e.prototype.run=function(e){var t=this;this.widget&&(this.widget.destroy(),this.widget=null);var n=function(e){t.clearDecorations(),e&&t.lastKnownEditorSelection&&(t.editor.setSelection(t.lastKnownEditorSelection),t.editor.revealRangeInCenterIfOutsideViewport(t.lastKnownEditorSelection,0)),t.lastKnownEditorSelection=null,document.activeElement!==document.body&&e||t.editor.focus()};this.widget=new c(this.editor,(function(){return n(!1)}),(function(){return n(!0)}),(function(n){t.widget.setInput(e.getModel(n),e.getAutoFocus(n))}),{inputAriaLabel:e.inputAriaLabel},this.themeService),this.lastKnownEditorSelection||(this.lastKnownEditorSelection=this.editor.getSelection()),this.widget.show("")},e.prototype.decorateLine=function(t,n){var o=[];this.rangeHighlightDecorationId&&(o.push(this.rangeHighlightDecorationId),this.rangeHighlightDecorationId=null);var r=[{range:t,options:e._RANGE_HIGHLIGHT_DECORATION}],i=n.deltaDecorations(o,r);this.rangeHighlightDecorationId=i[0]},e.prototype.clearDecorations=function(){this.rangeHighlightDecorationId&&(this.editor.deltaDecorations([this.rangeHighlightDecorationId],[]),this.rangeHighlightDecorationId=null)},e.ID="editor.controller.quickOpenController",e._RANGE_HIGHLIGHT_DECORATION=r["a"].register({className:"rangeHighlight",isWholeLine:!0}),e=f([g(1,d["c"])],e),e}(),p=function(e){function t(t,n){var o=e.call(this,n)||this;return o._inputAriaLabel=t,o}return l(t,e),t.prototype.getController=function(e){return h.get(e)},t.prototype._show=function(e,t){e.run({inputAriaLabel:this._inputAriaLabel,getModel:function(e){return t.getModel(e)},getAutoFocus:function(e){return t.getAutoFocus(e)}})},t}(o["b"]);Object(o["h"])(h.ID,h)},b1ca:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var o=n("0a0f"),r=Object(o["c"])("themeService")},b223:function(e,t,n){},bd50:function(e,t,n){"use strict";n.r(t);var o=n("b2cc"),r=n("b1ca"),i=n("03d9"),a=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),s=function(e){function t(){var t=e.call(this,{id:"editor.action.toggleHighContrast",label:i["h"].toggleHighContrast,alias:"Toggle High Contrast Theme",precondition:void 0})||this;return t._originalThemeName=null,t}return a(t,e),t.prototype.run=function(e,t){var n=e.get(r["a"]);this._originalThemeName?(n.setTheme(this._originalThemeName),this._originalThemeName=null):(this._originalThemeName=n.getTheme().themeName,n.setTheme("hc-black"))},t}(o["b"]);Object(o["f"])(s)},bfe0:function(e,t,n){"use strict";n.r(t),n.d(t,"EditorActionCommandEntry",(function(){return h})),n.d(t,"QuickCommandAction",(function(){return p}));var o=n("3742"),r=n("0f70"),i=n("fdcc"),a=n("7e93"),s=n("469c"),u=n("b2cc"),c=n("c101"),d=n("af33"),l=n("6dec"),f=n("03d9"),g=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),h=function(e){function t(t,n,o,r,i){var a=e.call(this)||this;return a.key=t,a.keyAriaLabel=n,a.setHighlights(o),a.action=r,a.editor=i,a}return g(t,e),t.prototype.getLabel=function(){return this.action.label},t.prototype.getAriaLabel=function(){return this.keyAriaLabel?o["r"](f["d"].ariaLabelEntryWithKey,this.getLabel(),this.keyAriaLabel):o["r"](f["d"].ariaLabelEntry,this.getLabel())},t.prototype.getGroupLabel=function(){return this.key},t.prototype.run=function(e,t){var n=this;return 1===e&&(setTimeout((function(){n.editor.focus();try{var e=n.action.run()||Promise.resolve();e.then(void 0,i["e"])}catch(t){Object(i["e"])(t)}}),50),!0)},t}(s["b"]),p=function(e){function t(){return e.call(this,f["d"].quickCommandActionInput,{id:"editor.action.quickCommand",label:f["d"].quickCommandActionLabel,alias:"Command Palette",precondition:void 0,kbOpts:{kbExpr:c["a"].focus,primary:r["i"]?571:59,weight:100},contextMenuOpts:{group:"z_commands",order:1}})||this}return g(t,e),t.prototype.run=function(e,t){var n=this,o=e.get(l["a"]);this._show(this.getController(t),{getModel:function(e){return new s["c"](n._editorActionsToEntries(o,t,e))},getAutoFocus:function(e){return{autoFocusFirstEntry:!0,autoFocusPrefixMatch:e}}})},t.prototype._sort=function(e,t){var n=(e.getLabel()||"").toLowerCase(),o=(t.getLabel()||"").toLowerCase();return n.localeCompare(o)},t.prototype._editorActionsToEntries=function(e,t,n){for(var o=t.getSupportedActions(),r=[],i=0,s=o;i<s.length;i++){var u=s[i],c=e.lookupKeybinding(u.id);if(u.label){var d=Object(a["f"])(n,u.label);d&&r.push(new h(c&&c.getLabel()||"",c&&c.getAriaLabel()||"",d,u,t))}}return r=r.sort(this._sort),r},t}(d["a"]);Object(u["f"])(p)},caf5:function(e,t,n){"use strict";n.d(t,"b",(function(){return W})),n.d(t,"a",(function(){return Ce}));var o,r,i,a=n("a666"),s=n("c7f5"),u=n("5717"),c=n("a40b"),d=n("567d"),l=n("5818"),f=n("d19c"),g=n("1b69"),h=n("d1a7"),p=n("7b4a"),m=n("3c6b"),b=n("11f7"),v=n("b589"),y=n("9e6f"),_=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),k=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return _(t,e),t.prototype.getActiveCodeEditor=function(){return null},t.prototype.openCodeEditor=function(e,t,n){return t?Promise.resolve(this.doOpenEditor(t,e)):Promise.resolve(null)},t.prototype.doOpenEditor=function(e,t){var n=this.findModel(e,t.resource);if(!n){if(t.resource){var o=t.resource.scheme;if(o===v["b"].http||o===v["b"].https)return Object(b["ab"])(t.resource.toString()),e}return null}var r=t.options?t.options.selection:null;if(r)if("number"===typeof r.endLineNumber&&"number"===typeof r.endColumn)e.setSelection(r),e.revealRangeInCenter(r,1);else{var i={lineNumber:r.startLineNumber,column:r.startColumn};e.setPosition(i),e.revealPositionInCenter(i,1)}return e},t.prototype.findModel=function(e,t){var n=e.getModel();return n&&n.uri.toString()!==t.toString()?null:n},t}(y["a"]),w=n("ceb8"),C=n("308f"),S=n("b707"),O=n("5adc"),D=n("918c"),E=n("303e"),L={base:"vs",inherit:!1,rules:[{token:"",foreground:"000000",background:"fffffe"},{token:"invalid",foreground:"cd3131"},{token:"emphasis",fontStyle:"italic"},{token:"strong",fontStyle:"bold"},{token:"variable",foreground:"001188"},{token:"variable.predefined",foreground:"4864AA"},{token:"constant",foreground:"dd0000"},{token:"comment",foreground:"008000"},{token:"number",foreground:"098658"},{token:"number.hex",foreground:"3030c0"},{token:"regexp",foreground:"800000"},{token:"annotation",foreground:"808080"},{token:"type",foreground:"008080"},{token:"delimiter",foreground:"000000"},{token:"delimiter.html",foreground:"383838"},{token:"delimiter.xml",foreground:"0000FF"},{token:"tag",foreground:"800000"},{token:"tag.id.pug",foreground:"4F76AC"},{token:"tag.class.pug",foreground:"4F76AC"},{token:"meta.scss",foreground:"800000"},{token:"metatag",foreground:"e00000"},{token:"metatag.content.html",foreground:"FF0000"},{token:"metatag.html",foreground:"808080"},{token:"metatag.xml",foreground:"808080"},{token:"metatag.php",fontStyle:"bold"},{token:"key",foreground:"863B00"},{token:"string.key.json",foreground:"A31515"},{token:"string.value.json",foreground:"0451A5"},{token:"attribute.name",foreground:"FF0000"},{token:"attribute.value",foreground:"0451A5"},{token:"attribute.value.number",foreground:"098658"},{token:"attribute.value.unit",foreground:"098658"},{token:"attribute.value.html",foreground:"0000FF"},{token:"attribute.value.xml",foreground:"0000FF"},{token:"string",foreground:"A31515"},{token:"string.html",foreground:"0000FF"},{token:"string.sql",foreground:"FF0000"},{token:"string.yaml",foreground:"0451A5"},{token:"keyword",foreground:"0000FF"},{token:"keyword.json",foreground:"0451A5"},{token:"keyword.flow",foreground:"AF00DB"},{token:"keyword.flow.scss",foreground:"0000FF"},{token:"operator.scss",foreground:"666666"},{token:"operator.sql",foreground:"778899"},{token:"operator.swift",foreground:"666666"},{token:"predefined.sql",foreground:"FF00FF"}],colors:(o={},o[E["o"]]="#FFFFFE",o[E["x"]]="#000000",o[E["F"]]="#E5EBF1",o[D["h"]]="#D3D3D3",o[D["a"]]="#939393",o[E["M"]]="#ADD6FF4D",o)},T={base:"vs-dark",inherit:!1,rules:[{token:"",foreground:"D4D4D4",background:"1E1E1E"},{token:"invalid",foreground:"f44747"},{token:"emphasis",fontStyle:"italic"},{token:"strong",fontStyle:"bold"},{token:"variable",foreground:"74B0DF"},{token:"variable.predefined",foreground:"4864AA"},{token:"variable.parameter",foreground:"9CDCFE"},{token:"constant",foreground:"569CD6"},{token:"comment",foreground:"608B4E"},{token:"number",foreground:"B5CEA8"},{token:"number.hex",foreground:"5BB498"},{token:"regexp",foreground:"B46695"},{token:"annotation",foreground:"cc6666"},{token:"type",foreground:"3DC9B0"},{token:"delimiter",foreground:"DCDCDC"},{token:"delimiter.html",foreground:"808080"},{token:"delimiter.xml",foreground:"808080"},{token:"tag",foreground:"569CD6"},{token:"tag.id.pug",foreground:"4F76AC"},{token:"tag.class.pug",foreground:"4F76AC"},{token:"meta.scss",foreground:"A79873"},{token:"meta.tag",foreground:"CE9178"},{token:"metatag",foreground:"DD6A6F"},{token:"metatag.content.html",foreground:"9CDCFE"},{token:"metatag.html",foreground:"569CD6"},{token:"metatag.xml",foreground:"569CD6"},{token:"metatag.php",fontStyle:"bold"},{token:"key",foreground:"9CDCFE"},{token:"string.key.json",foreground:"9CDCFE"},{token:"string.value.json",foreground:"CE9178"},{token:"attribute.name",foreground:"9CDCFE"},{token:"attribute.value",foreground:"CE9178"},{token:"attribute.value.number.css",foreground:"B5CEA8"},{token:"attribute.value.unit.css",foreground:"B5CEA8"},{token:"attribute.value.hex.css",foreground:"D4D4D4"},{token:"string",foreground:"CE9178"},{token:"string.sql",foreground:"FF0000"},{token:"keyword",foreground:"569CD6"},{token:"keyword.flow",foreground:"C586C0"},{token:"keyword.json",foreground:"CE9178"},{token:"keyword.flow.scss",foreground:"569CD6"},{token:"operator.scss",foreground:"909090"},{token:"operator.sql",foreground:"778899"},{token:"operator.swift",foreground:"909090"},{token:"predefined.sql",foreground:"FF00FF"}],colors:(r={},r[E["o"]]="#1E1E1E",r[E["x"]]="#D4D4D4",r[E["F"]]="#3A3D41",r[D["h"]]="#404040",r[D["a"]]="#707070",r[E["M"]]="#ADD6FF26",r)},I={base:"hc-black",inherit:!1,rules:[{token:"",foreground:"FFFFFF",background:"000000"},{token:"invalid",foreground:"f44747"},{token:"emphasis",fontStyle:"italic"},{token:"strong",fontStyle:"bold"},{token:"variable",foreground:"1AEBFF"},{token:"variable.parameter",foreground:"9CDCFE"},{token:"constant",foreground:"569CD6"},{token:"comment",foreground:"608B4E"},{token:"number",foreground:"FFFFFF"},{token:"regexp",foreground:"C0C0C0"},{token:"annotation",foreground:"569CD6"},{token:"type",foreground:"3DC9B0"},{token:"delimiter",foreground:"FFFF00"},{token:"delimiter.html",foreground:"FFFF00"},{token:"tag",foreground:"569CD6"},{token:"tag.id.pug",foreground:"4F76AC"},{token:"tag.class.pug",foreground:"4F76AC"},{token:"meta",foreground:"D4D4D4"},{token:"meta.tag",foreground:"CE9178"},{token:"metatag",foreground:"569CD6"},{token:"metatag.content.html",foreground:"1AEBFF"},{token:"metatag.html",foreground:"569CD6"},{token:"metatag.xml",foreground:"569CD6"},{token:"metatag.php",fontStyle:"bold"},{token:"key",foreground:"9CDCFE"},{token:"string.key",foreground:"9CDCFE"},{token:"string.value",foreground:"CE9178"},{token:"attribute.name",foreground:"569CD6"},{token:"attribute.value",foreground:"3FF23F"},{token:"string",foreground:"CE9178"},{token:"string.sql",foreground:"FF0000"},{token:"keyword",foreground:"569CD6"},{token:"keyword.flow",foreground:"C586C0"},{token:"operator.sql",foreground:"778899"},{token:"operator.swift",foreground:"909090"},{token:"predefined.sql",foreground:"FF00FF"}],colors:(i={},i[E["o"]]="#000000",i[E["x"]]="#FFFFFF",i[D["h"]]="#FFFFFF",i[D["a"]]="#FFFFFF",i)},M=n("89cd"),x=n("b7d0"),j=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),A="vs",P="vs-dark",F="hc-black",N=M["a"].as(E["a"].ColorContribution),R=M["a"].as(x["a"].ThemingContribution),z=function(){function e(e,t){this.themeData=t;var n=t.base;e.length>0?(this.id=n+" "+e,this.themeName=e):(this.id=n,this.themeName=n),this.colors=null,this.defaultColors=Object.create(null),this._tokenTheme=null}return Object.defineProperty(e.prototype,"base",{get:function(){return this.themeData.base},enumerable:!0,configurable:!0}),e.prototype.notifyBaseUpdated=function(){this.themeData.inherit&&(this.colors=null,this._tokenTheme=null)},e.prototype.getColors=function(){if(!this.colors){var e=new Map;for(var t in this.themeData.colors)e.set(t,w["a"].fromHex(this.themeData.colors[t]));if(this.themeData.inherit){var n=K(this.themeData.base);for(var t in n.colors)e.has(t)||e.set(t,w["a"].fromHex(n.colors[t]))}this.colors=e}return this.colors},e.prototype.getColor=function(e,t){var n=this.getColors().get(e);return n||(!1!==t?this.getDefault(e):void 0)},e.prototype.getDefault=function(e){var t=this.defaultColors[e];return t||(t=N.resolveDefaultColor(e,this),this.defaultColors[e]=t,t)},e.prototype.defines=function(e){return Object.prototype.hasOwnProperty.call(this.getColors(),e)},Object.defineProperty(e.prototype,"type",{get:function(){switch(this.base){case A:return"light";case F:return"hc";default:return"dark"}},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"tokenTheme",{get:function(){if(!this._tokenTheme){var e=[],t=[];if(this.themeData.inherit){var n=K(this.themeData.base);e=n.rules,n.encodedTokensColors&&(t=n.encodedTokensColors)}e=e.concat(this.themeData.rules),this.themeData.encodedTokensColors&&(t=this.themeData.encodedTokensColors),this._tokenTheme=O["a"].createFromRawTokenTheme(e,t)}return this._tokenTheme},enumerable:!0,configurable:!0}),e.prototype.getTokenStyleMetadata=function(e,t){},e}();function H(e){return e===A||e===P||e===F}function K(e){switch(e){case A:return L;case P:return T;case F:return I}}function B(e){var t=K(e);return new z(e,t)}var W,q=function(e){function t(){var t=e.call(this)||this;return t._onThemeChange=t._register(new C["a"]),t.onThemeChange=t._onThemeChange.event,t._environment=Object.create(null),t._knownThemes=new Map,t._knownThemes.set(A,B(A)),t._knownThemes.set(P,B(P)),t._knownThemes.set(F,B(F)),t._css="",t._globalStyleElement=null,t._styleElements=[],t.setTheme(A),t}return j(t,e),t.prototype.registerEditorContainer=function(e){return b["N"](e)?this._registerShadowDomContainer(e):this._registerRegularEditorContainer()},t.prototype._registerRegularEditorContainer=function(){return this._globalStyleElement||(this._globalStyleElement=b["w"](),this._globalStyleElement.className="monaco-colors",this._globalStyleElement.innerHTML=this._css,this._styleElements.push(this._globalStyleElement)),a["a"].None},t.prototype._registerShadowDomContainer=function(e){var t=this,n=b["w"](e);return n.className="monaco-colors",n.innerHTML=this._css,this._styleElements.push(n),{dispose:function(){for(var e=0;e<t._styleElements.length;e++)if(t._styleElements[e]===n)return void t._styleElements.splice(e,1)}}},t.prototype.defineTheme=function(e,t){if(!/^[a-z0-9\-]+$/i.test(e))throw new Error("Illegal theme name!");if(!H(t.base)&&!H(e))throw new Error("Illegal theme base!");this._knownThemes.set(e,new z(e,t)),H(e)&&this._knownThemes.forEach((function(t){t.base===e&&t.notifyBaseUpdated()})),this._theme&&this._theme.themeName===e&&this.setTheme(e)},t.prototype.getTheme=function(){return this._theme},t.prototype.setTheme=function(e){var t,n=this;if(t=this._knownThemes.has(e)?this._knownThemes.get(e):this._knownThemes.get(A),this._theme===t)return t.id;this._theme=t;var o=[],r={},i={addRule:function(e){r[e]||(o.push(e),r[e]=!0)}};R.getThemingParticipants().forEach((function(e){return e(t,i,n._environment)}));var a=t.tokenTheme,s=a.getColorMap();return i.addRule(Object(O["b"])(s)),this._css=o.join("\n"),this._styleElements.forEach((function(e){return e.innerHTML=n._css})),S["B"].setColorMap(s),this._onThemeChange.fire(t),t.id},t.prototype.getIconTheme=function(){return{hasFileIcons:!1,hasFolderIcons:!1,hidesExplorerArrows:!1}},t}(a["a"]),V=n("b1ca"),G=n("7e32"),U=n("9e74"),$=n("fbba"),Q=n("f620"),J=n("4fc3"),Y=n("762e"),X=n("533b"),Z=n("107e"),ee=n("eafb"),te=n("0a0f"),ne=n("485e"),oe=n("f07b"),re=n("6dec"),ie=n("47cb"),ae=n("93d9"),se=n("d3d7"),ue=n("37f2"),ce=n("b400"),de=n("b0cd"),le=n("b539"),fe=n("03e8"),ge=n("5d75"),he=n("1165"),pe=n("4e95"),me=n("efdb"),be=n("e6e5"),ve=n("4779"),ye=n("ad8e"),_e=n("f5f3"),ke=n("cb336"),we=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}();(function(e){var t=new oe["a"],n=function(){function e(e,t){this._serviceId=e,this._factory=t,this._value=null}return Object.defineProperty(e.prototype,"id",{get:function(){return this._serviceId},enumerable:!0,configurable:!0}),e.prototype.get=function(e){if(!this._value){if(e&&(this._value=e[this._serviceId.toString()]),this._value||(this._value=this._factory(e)),!this._value)throw new Error("Service "+this._serviceId+" is missing!");t.set(this._serviceId,this._value)}return this._value},e}();e.LazyStaticService=n;var o=[];function r(e,t){var r=new n(e,t);return o.push(r),r}function i(e){for(var t=new oe["a"],n=0,r=Object(_e["a"])();n<r.length;n++){var i=r[n],a=i[0],s=i[1];t.set(a,s)}for(var u in e)e.hasOwnProperty(u)&&t.set(Object(te["c"])(u),e[u]);o.forEach((function(n){return t.set(n.id,n.get(e))}));var c=new ne["a"](t,!0);return t.set(te["a"],c),[t,c]}e.init=i,e.instantiationService=r(te["a"],(function(){return new ne["a"](t,!0)}));var a=new m["b"];e.configurationService=r($["a"],(function(){return a})),e.resourceConfigurationService=r(p["a"],(function(){return new m["h"](a)})),e.resourcePropertiesService=r(p["b"],(function(){return new m["i"](a)})),e.contextService=r(he["a"],(function(){return new m["k"]})),e.labelService=r(ie["a"],(function(){return new m["j"]})),e.telemetryService=r(ge["a"],(function(){return new m["n"]})),e.dialogService=r(ee["a"],(function(){return new m["c"]})),e.notificationService=r(de["a"],(function(){return new m["g"]})),e.markerService=r(ce["b"],(function(){return new ue["a"]})),e.modeService=r(l["a"],(function(e){return new f["a"]})),e.standaloneThemeService=r(V["a"],(function(){return new q})),e.logService=r(se["a"],(function(){return new se["c"]})),e.modelService=r(g["a"],(function(t){return new h["a"](e.configurationService.get(t),e.resourcePropertiesService.get(t),e.standaloneThemeService.get(t),e.logService.get(t))})),e.markerDecorationsService=r(me["a"],(function(t){return new be["a"](e.modelService.get(t),e.markerService.get(t))})),e.codeEditorService=r(u["a"],(function(t){return new k(e.standaloneThemeService.get(t))})),e.editorProgressService=r(le["a"],(function(){return new m["e"]})),e.storageService=r(fe["a"],(function(){return new fe["b"]})),e.editorWorkerService=r(c["a"],(function(t){return new d["b"](e.modelService.get(t),e.resourceConfigurationService.get(t),e.logService.get(t))}))})(W||(W={}));var Ce=function(e){function t(t,n){var o=e.call(this)||this,r=W.init(n),i=r[0],a=r[1];o._serviceCollection=i,o._instantiationService=a;var u=o.get($["a"]),c=o.get(de["a"]),d=o.get(ge["a"]),l=o.get(x["c"]),f=function(e,t){var r=null;return n&&(r=n[e.toString()]),r||(r=t()),o._serviceCollection.set(e,r),r},h=f(J["c"],(function(){return o._register(new Q["a"](u))}));f(ve["b"],(function(){return new ke["a"](h,u)})),f(ae["a"],(function(){return new ae["b"](l)}));var p=f(U["b"],(function(){return new m["l"](o._instantiationService)})),b=f(re["a"],(function(){return o._register(new m["m"](h,p,d,c,t))})),v=f(ye["a"],(function(){return new m["f"](t)})),y=f(X["b"],(function(){return o._register(new Z["a"](v))}));return f(X["a"],(function(){var e=new Y["a"](d,c,y,b,l);return e.configure({blockMouse:!1}),o._register(e)})),f(G["a"],(function(){return new pe["a"](p)})),f(s["a"],(function(){return new m["a"](W.modelService.get(g["a"]))})),o}return we(t,e),t.prototype.get=function(e){var t=this._serviceCollection.get(e);if(!t)throw new Error("Missing service "+e);return t},t.prototype.set=function(e,t){this._serviceCollection.set(e,t)},t.prototype.has=function(e){return this._serviceCollection.has(e)},t}(a["a"])},d0c6:function(e,t,n){"use strict";n.d(t,"a",(function(){return p})),n.d(t,"b",(function(){return b}));var o=n("4dc7"),r=n("b707"),i=n("8bf1"),a=n("8f4b"),s=5,u=function(){function e(e){this._maxCacheDepth=e,this._entries=Object.create(null)}return e.create=function(e,t){return this._INSTANCE.create(e,t)},e.prototype.create=function(e,t){if(null!==e&&e.depth>=this._maxCacheDepth)return new c(e,t);var n=c.getStackElementId(e);n.length>0&&(n+="|"),n+=t;var o=this._entries[n];return o||(o=new c(e,t),this._entries[n]=o,o)},e._INSTANCE=new e(s),e}(),c=function(){function e(e,t){this.parent=e,this.state=t,this.depth=(this.parent?this.parent.depth:0)+1}return e.getStackElementId=function(e){var t="";while(null!==e)t.length>0&&(t+="|"),t+=e.state,e=e.parent;return t},e._equals=function(e,t){while(null!==e&&null!==t){if(e===t)return!0;if(e.state!==t.state)return!1;e=e.parent,t=t.parent}return null===e&&null===t},e.prototype.equals=function(t){return e._equals(this,t)},e.prototype.push=function(e){return u.create(this,e)},e.prototype.pop=function(){return this.parent},e.prototype.popall=function(){var e=this;while(e.parent)e=e.parent;return e},e.prototype.switchTo=function(e){return u.create(this.parent,e)},e}(),d=function(){function e(e,t){this.modeId=e,this.state=t}return e.prototype.equals=function(e){return this.modeId===e.modeId&&this.state.equals(e.state)},e.prototype.clone=function(){var t=this.state.clone();return t===this.state?this:new e(this.modeId,this.state)},e}(),l=function(){function e(e){this._maxCacheDepth=e,this._entries=Object.create(null)}return e.create=function(e,t){return this._INSTANCE.create(e,t)},e.prototype.create=function(e,t){if(null!==t)return new f(e,t);if(null!==e&&e.depth>=this._maxCacheDepth)return new f(e,t);var n=c.getStackElementId(e),o=this._entries[n];return o||(o=new f(e,null),this._entries[n]=o,o)},e._INSTANCE=new e(s),e}(),f=function(){function e(e,t){this.stack=e,this.embeddedModeData=t}return e.prototype.clone=function(){var e=this.embeddedModeData?this.embeddedModeData.clone():null;return e===this.embeddedModeData?this:l.create(this.stack,this.embeddedModeData)},e.prototype.equals=function(t){return t instanceof e&&(!!this.stack.equals(t.stack)&&(null===this.embeddedModeData&&null===t.embeddedModeData||null!==this.embeddedModeData&&null!==t.embeddedModeData&&this.embeddedModeData.equals(t.embeddedModeData)))},e}(),g=function(){function e(){this._tokens=[],this._language=null,this._lastTokenType=null,this._lastTokenLanguage=null}return e.prototype.enterMode=function(e,t){this._language=t},e.prototype.emit=function(e,t){this._lastTokenType===t&&this._lastTokenLanguage===this._language||(this._lastTokenType=t,this._lastTokenLanguage=this._language,this._tokens.push(new o["a"](e,t,this._language)))},e.prototype.nestedModeTokenize=function(e,t,n){var o=t.modeId,i=t.state,a=r["B"].get(o);if(!a)return this.enterMode(n,o),this.emit(n,""),i;var s=a.tokenize(e,i,n);return this._tokens=this._tokens.concat(s.tokens),this._lastTokenType=null,this._lastTokenLanguage=null,this._language=null,s.endState},e.prototype.finalize=function(e){return new o["b"](this._tokens,e)},e}(),h=function(){function e(e,t){this._modeService=e,this._theme=t,this._prependTokens=null,this._tokens=[],this._currentLanguageId=0,this._lastTokenMetadata=0}return e.prototype.enterMode=function(e,t){this._currentLanguageId=this._modeService.getLanguageIdentifier(t).id},e.prototype.emit=function(e,t){var n=this._theme.match(this._currentLanguageId,t);this._lastTokenMetadata!==n&&(this._lastTokenMetadata=n,this._tokens.push(e),this._tokens.push(n))},e._merge=function(e,t,n){var o=null!==e?e.length:0,r=t.length,i=null!==n?n.length:0;if(0===o&&0===r&&0===i)return new Uint32Array(0);if(0===o&&0===r)return n;if(0===r&&0===i)return e;var a=new Uint32Array(o+r+i);null!==e&&a.set(e);for(var s=0;s<r;s++)a[o+s]=t[s];return null!==n&&a.set(n,o+r),a},e.prototype.nestedModeTokenize=function(t,n,o){var i=n.modeId,a=n.state,s=r["B"].get(i);if(!s)return this.enterMode(o,i),this.emit(o,""),a;var u=s.tokenize2(t,a,o);return this._prependTokens=e._merge(this._prependTokens,this._tokens,u.tokens),this._tokens=[],this._currentLanguageId=0,this._lastTokenMetadata=0,u.endState},e.prototype.finalize=function(t){return new o["c"](e._merge(this._prependTokens,this._tokens,null),t)},e}(),p=function(){function e(e,t,n,o){var i=this;this._modeService=e,this._standaloneThemeService=t,this._modeId=n,this._lexer=o,this._embeddedModes=Object.create(null),this.embeddedLoaded=Promise.resolve(void 0);var a=!1;this._tokenizationRegistryListener=r["B"].onDidChange((function(e){if(!a){for(var t=!1,n=0,o=e.changedLanguages.length;n<o;n++){var s=e.changedLanguages[n];if(i._embeddedModes[s]){t=!0;break}}t&&(a=!0,r["B"].fire([i._modeId]),a=!1)}}))}return e.prototype.dispose=function(){this._tokenizationRegistryListener.dispose()},e.prototype.getLoadStatus=function(){var t=[];for(var n in this._embeddedModes){var o=r["B"].get(n);if(o){if(o instanceof e){var i=o.getLoadStatus();!1===i.loaded&&t.push(i.promise)}}else{var a=r["B"].getPromise(n);a&&t.push(a)}}return 0===t.length?{loaded:!0}:{loaded:!1,promise:Promise.all(t).then((function(e){}))}},e.prototype.getInitialState=function(){var e=u.create(null,this._lexer.start);return l.create(e,null)},e.prototype.tokenize=function(e,t,n){var o=new g,r=this._tokenize(e,t,n,o);return o.finalize(r)},e.prototype.tokenize2=function(e,t,n){var o=new h(this._modeService,this._standaloneThemeService.getTheme().tokenTheme),r=this._tokenize(e,t,n,o);return o.finalize(r)},e.prototype._tokenize=function(e,t,n,o){return t.embeddedModeData?this._nestedTokenize(e,t,n,o):this._myTokenize(e,t,n,o)},e.prototype._findLeavingNestedModeOffset=function(e,t){var n=this._lexer.tokenizer[t.stack.state];if(!n&&(n=a["c"](this._lexer,t.stack.state),!n))throw a["a"](this._lexer,"tokenizer state is not defined: "+t.stack.state);for(var o=-1,r=!1,i=0,s=n;i<s.length;i++){var u=s[i];if(a["f"](u.action)&&"@pop"===u.action.nextEmbedded){r=!0;var c=u.regex,d=u.regex.source;"^(?:"===d.substr(0,4)&&")"===d.substr(d.length-1,1)&&(c=new RegExp(d.substr(4,d.length-5),c.ignoreCase?"i":""));var l=e.search(c);-1===l||0!==l&&u.matchOnlyAtLineStart||(-1===o||l<o)&&(o=l)}}if(!r)throw a["a"](this._lexer,'no rule containing nextEmbedded: "@pop" in tokenizer embedded state: '+t.stack.state);return o},e.prototype._nestedTokenize=function(e,t,n,o){var r=this._findLeavingNestedModeOffset(e,t);if(-1===r){var i=o.nestedModeTokenize(e,t.embeddedModeData,n);return l.create(t.stack,new d(t.embeddedModeData.modeId,i))}var a=e.substring(0,r);a.length>0&&o.nestedModeTokenize(a,t.embeddedModeData,n);var s=e.substring(r);return this._myTokenize(s,t,n+r,o)},e.prototype._safeRuleName=function(e){return e?e.name:"(unknown)"},e.prototype._myTokenize=function(e,t,n,o){o.enterMode(n,this._modeId);var r=e.length,i=t.embeddedModeData,s=t.stack,u=0,c=null,d=!0;while(d||u<r){var f=u,g=s.depth,h=c?c.groups.length:0,p=s.state,b=null,v=null,y=null,_=null,k=null;if(c){b=c.matches;var w=c.groups.shift();v=w.matched,y=w.action,_=c.rule,0===c.groups.length&&(c=null)}else{if(!d&&u>=r)break;d=!1;var C=this._lexer.tokenizer[p];if(!C&&(C=a["c"](this._lexer,p),!C))throw a["a"](this._lexer,"tokenizer state is not defined: "+p);for(var S=e.substr(u),O=0,D=C;O<D.length;O++){var E=D[O];if((0===u||!E.matchOnlyAtLineStart)&&(b=S.match(E.regex),b)){v=b[0],y=E.action;break}}}if(b||(b=[""],v=""),y||(u<r&&(b=[e.charAt(u)],v=b[0]),y=this._lexer.defaultToken),null===v)break;u+=v.length;while(a["e"](y)&&a["f"](y)&&y.test)y=y.test(v,b,p,u===r);var L=null;if("string"===typeof y||Array.isArray(y))L=y;else if(y.group)L=y.group;else if(null!==y.token&&void 0!==y.token){if(L=y.tokenSubst?a["k"](this._lexer,y.token,v,b,p):y.token,y.nextEmbedded)if("@pop"===y.nextEmbedded){if(!i)throw a["a"](this._lexer,"cannot pop embedded mode if not inside one");i=null}else{if(i)throw a["a"](this._lexer,"cannot enter embedded mode from within an embedded mode");k=a["k"](this._lexer,y.nextEmbedded,v,b,p)}if(y.goBack&&(u=Math.max(0,u-y.goBack)),y.switchTo&&"string"===typeof y.switchTo){var T=a["k"](this._lexer,y.switchTo,v,b,p);if("@"===T[0]&&(T=T.substr(1)),!a["c"](this._lexer,T))throw a["a"](this._lexer,"trying to switch to a state '"+T+"' that is undefined in rule: "+this._safeRuleName(_));s=s.switchTo(T)}else{if(y.transform&&"function"===typeof y.transform)throw a["a"](this._lexer,"action.transform not supported");if(y.next)if("@push"===y.next){if(s.depth>=this._lexer.maxStack)throw a["a"](this._lexer,"maximum tokenizer stack size reached: ["+s.state+","+s.parent.state+",...]");s=s.push(p)}else if("@pop"===y.next){if(s.depth<=1)throw a["a"](this._lexer,"trying to pop an empty stack in rule: "+this._safeRuleName(_));s=s.pop()}else if("@popall"===y.next)s=s.popall();else{T=a["k"](this._lexer,y.next,v,b,p);if("@"===T[0]&&(T=T.substr(1)),!a["c"](this._lexer,T))throw a["a"](this._lexer,"trying to set a next state '"+T+"' that is undefined in rule: "+this._safeRuleName(_));s=s.push(T)}}y.log&&"string"===typeof y.log&&a["h"](this._lexer,this._lexer.languageId+": "+a["k"](this._lexer,y.log,v,b,p))}if(null===L)throw a["a"](this._lexer,"lexer rule has no well-defined action in rule: "+this._safeRuleName(_));if(Array.isArray(L)){if(c&&c.groups.length>0)throw a["a"](this._lexer,"groups cannot be nested: "+this._safeRuleName(_));if(b.length!==L.length+1)throw a["a"](this._lexer,"matched number of groups does not match the number of actions in rule: "+this._safeRuleName(_));for(var I=0,M=1;M<b.length;M++)I+=b[M].length;if(I!==v.length)throw a["a"](this._lexer,"with groups, all characters should be matched in consecutive groups in rule: "+this._safeRuleName(_));c={rule:_,matches:b,groups:[]};for(M=0;M<L.length;M++)c.groups[M]={action:L[M],matched:b[M+1]};u-=v.length}else{if("@rematch"===L&&(u-=v.length,v="",b=null,L=""),0===v.length){if(0===r||g!==s.depth||p!==s.state||(c?c.groups.length:0)!==h)continue;throw a["a"](this._lexer,"no progress in tokenizer in rule: "+this._safeRuleName(_))}var x=null;if(a["g"](L)&&0===L.indexOf("@brackets")){var j=L.substr("@brackets".length),A=m(this._lexer,v);if(!A)throw a["a"](this._lexer,"@brackets token returned but no bracket defined as: "+v);x=a["i"](A.token+j)}else{var P=""===L?"":L+this._lexer.tokenPostfix;x=a["i"](P)}if(o.emit(f+n,x),null!==k){var F=this._modeService.getModeIdForLanguageName(k);F&&(k=F);var N=this._getNestedEmbeddedModeData(k);if(u<r){S=e.substr(u);return this._nestedTokenize(S,l.create(s,N),n+u,o)}return l.create(s,N)}}}return l.create(s,i)},e.prototype._getNestedEmbeddedModeData=function(e){var t=this._locateMode(e);if(t){var n=r["B"].get(t);if(n)return new d(t,n.getInitialState())}return new d(t||i["b"],i["c"])},e.prototype._locateMode=function(e){if(!e||!this._modeService.isRegisteredMode(e))return null;if(e===this._modeId)return e;var t=this._modeService.getModeId(e);return t&&(this._modeService.triggerMode(t),this._embeddedModes[t]=!0),t},e}();function m(e,t){if(!t)return null;t=a["d"](e,t);for(var n=e.brackets,o=0,r=n;o<r.length;o++){var i=r[o];if(i.open===t)return{token:i.token,bracketType:1};if(i.close===t)return{token:i.token,bracketType:-1}}return null}function b(e,t,n,o){return new p(e,t,n,o)}},e2c2:function(e,t,n){"use strict";n.r(t),n.d(t,"StandaloneReferencesController",(function(){return h}));var o=n("b2cc"),r=n("5717"),i=n("418f"),a=n("fbba"),s=n("4fc3"),u=n("0a0f"),c=n("b0cd"),d=n("03e8"),l=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function o(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(o.prototype=n.prototype,new o)}}(),f=function(e,t,n,o){var r,i=arguments.length,a=i<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"===typeof Reflect&&"function"===typeof Reflect.decorate)a=Reflect.decorate(e,t,n,o);else for(var s=e.length-1;s>=0;s--)(r=e[s])&&(a=(i<3?r(a):i>3?r(t,n,a):r(t,n))||a);return i>3&&a&&Object.defineProperty(t,n,a),a},g=function(e,t){return function(n,o){t(n,o,e)}},h=function(e){function t(t,n,o,r,i,a,s){return e.call(this,!0,t,n,o,r,i,a,s)||this}return l(t,e),t=f([g(1,s["c"]),g(2,r["a"]),g(3,c["a"]),g(4,u["a"]),g(5,d["a"]),g(6,a["a"])],t),t}(i["a"]);Object(o["h"])(i["a"].ID,h)},f570:function(e,t,n){"use strict";n.r(t),n.d(t,"LanguageServiceDefaultsImpl",(function(){return r}));n("33f9");var o=monaco.Emitter,r=function(){function e(e,t,n){this._onDidChange=new o,this._languageId=e,this.setDiagnosticsOptions(t),this.setModeConfiguration(n)}return Object.defineProperty(e.prototype,"onDidChange",{get:function(){return this._onDidChange.event},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"languageId",{get:function(){return this._languageId},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"modeConfiguration",{get:function(){return this._modeConfiguration},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"diagnosticsOptions",{get:function(){return this._diagnosticsOptions},enumerable:!0,configurable:!0}),e.prototype.setDiagnosticsOptions=function(e){this._diagnosticsOptions=e||Object.create(null),this._onDidChange.fire(this)},e.prototype.setModeConfiguration=function(e){this._modeConfiguration=e||Object.create(null),this._onDidChange.fire(this)},e}(),i={validate:!0,lint:{compatibleVendorPrefixes:"ignore",vendorPrefix:"warning",duplicateProperties:"warning",emptyRules:"warning",importStatement:"ignore",boxModel:"ignore",universalSelector:"ignore",zeroUnits:"ignore",fontFaceProperties:"warning",hexColorLength:"error",argumentsInColorFunction:"error",unknownProperties:"warning",ieHack:"ignore",unknownVendorSpecificProperties:"ignore",propertyIgnoredDueToDisplay:"warning",important:"ignore",float:"ignore",idSelector:"ignore"}},a={completionItems:!0,hovers:!0,documentSymbols:!0,definitions:!0,references:!0,documentHighlights:!0,rename:!0,colors:!0,foldingRanges:!0,diagnostics:!0,selectionRanges:!0},s=new r("css",i,a),u=new r("scss",i,a),c=new r("less",i,a);function d(){return{cssDefaults:s,lessDefaults:c,scssDefaults:u}}function l(){return Promise.all([n.e("chunk-0b65ffb6"),n.e("chunk-2d0c035c")]).then(n.bind(null,"db4f"))}monaco.languages.css=d(),monaco.languages.onLanguage("less",(function(){l().then((function(e){return e.setupMode(c)}))})),monaco.languages.onLanguage("scss",(function(){l().then((function(e){return e.setupMode(u)}))})),monaco.languages.onLanguage("css",(function(){l().then((function(e){return e.setupMode(s)}))}))}}]);