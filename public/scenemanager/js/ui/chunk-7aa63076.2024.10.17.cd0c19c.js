(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7aa63076"],{3352:function(e,t,n){"use strict";var i,r;n.d(t,"d",(function(){return i})),n.d(t,"c",(function(){return r})),n.d(t,"e",(function(){return o})),n.d(t,"b",(function(){return s})),n.d(t,"a",(function(){return a})),function(e){e[e["Left"]=1]="Left",e[e["Center"]=2]="Center",e[e["Right"]=4]="Right",e[e["Full"]=7]="Full"}(i||(i={})),function(e){e[e["Inline"]=1]="Inline",e[e["Gutter"]=2]="Gutter"}(r||(r={}));var o=function(){function e(e){this.tabSize=Math.max(1,0|e.tabSize),this.indentSize=0|e.tabSize,this.insertSpaces=Boolean(e.insertSpaces),this.defaultEOL=0|e.defaultEOL,this.trimAutoWhitespace=Boolean(e.trimAutoWhitespace)}return e.prototype.equals=function(e){return this.tabSize===e.tabSize&&this.indentSize===e.indentSize&&this.insertSpaces===e.insertSpaces&&this.defaultEOL===e.defaultEOL&&this.trimAutoWhitespace===e.trimAutoWhitespace},e.prototype.createChangeEvent=function(e){return{tabSize:this.tabSize!==e.tabSize,indentSize:this.indentSize!==e.indentSize,insertSpaces:this.insertSpaces!==e.insertSpaces,trimAutoWhitespace:this.trimAutoWhitespace!==e.trimAutoWhitespace}},e}(),s=function(){function e(e,t){this.range=e,this.matches=t}return e}(),a=function(){function e(e,t,n){this.reverseEdits=e,this.changes=t,this.trimAutoWhitespaceLineNumbers=n}return e}()},4111:function(e,t,n){"use strict";n.d(t,"f",(function(){return a})),n.d(t,"b",(function(){return f})),n.d(t,"c",(function(){return l})),n.d(t,"a",(function(){return d})),n.d(t,"e",(function(){return _})),n.d(t,"d",(function(){return v}));var i=n("e8e3"),r=n("e1b5"),o=n("7061"),s=n("b707");function a(e){for(var t=0,n=0,i=0,r=0,o=e.length;r<o;r++){var s=e.charCodeAt(r);13===s?(0===t&&(n=r),t++,r+1<o&&10===e.charCodeAt(r+1)&&r++,i=r+1):10===s&&(0===t&&(n=r),t++,i=r+1)}return 0===t&&(n=e.length),[t,n,e.length-i]}function u(e){return(16384|e<<0|2<<23)>>>0}var h=new Uint32Array(0).buffer,f=function(){function e(){this.tokens=[]}return e.prototype.add=function(e,t){if(this.tokens.length>0){var n=this.tokens[this.tokens.length-1],i=n.startLineNumber+n.tokens.length-1;if(i+1===e)return void n.tokens.push(t)}this.tokens.push(new p(e,[t]))},e}(),l=function(){function e(e){this._tokens=e,this._tokenCount=e.length/4}return e.prototype.getMaxDeltaLine=function(){var e=this.getTokenCount();return 0===e?-1:this.getDeltaLine(e-1)},e.prototype.getTokenCount=function(){return this._tokenCount},e.prototype.getDeltaLine=function(e){return this._tokens[4*e]},e.prototype.getStartCharacter=function(e){return this._tokens[4*e+1]},e.prototype.getEndCharacter=function(e){return this._tokens[4*e+2]},e.prototype.getMetadata=function(e){return this._tokens[4*e+3]},e.prototype.clear=function(){this._tokenCount=0},e.prototype.acceptDeleteRange=function(e,t,n,i,r){for(var o=this._tokens,s=this._tokenCount,a=i-t,u=0,h=!1,f=0;f<s;f++){var l=4*f,c=o[l],d=o[l+1],p=o[l+2],g=o[l+3];if(c<t||c===t&&p<=n)u++;else{if(c===t&&d<n)c===i&&p>r?p-=r-n:p=n;else if(c===t&&d===n){if(!(c===i&&p>r)){h=!0;continue}p-=r-n}else if(c<i||c===i&&d<r){if(!(c===i&&p>r)){h=!0;continue}c===t?(d=n,p=d+(p-r)):(d=0,p=d+(p-r))}else if(c>i){if(0===a&&!h){u=s;break}c-=a}else{if(!(c===i&&d>=r))throw new Error("Not possible!");e&&0===c&&(d+=e,p+=e),c-=a,d-=r-n,p-=r-n}var _=4*u;o[_]=c,o[_+1]=d,o[_+2]=p,o[_+3]=g,u++}}this._tokenCount=u},e.prototype.acceptInsertText=function(e,t,n,i,r,o){for(var s=0===n&&1===i&&(o>=48&&o<=57||o>=65&&o<=90||o>=97&&o<=122),a=this._tokens,u=this._tokenCount,h=0;h<u;h++){var f=4*h,l=a[f],c=a[f+1],d=a[f+2];if(!(l<e||l===e&&d<t)){if(l===e&&d===t){if(!s)continue;d+=1}else if(l===e&&c<t&&t<d)0===n?d+=i:d=t;else{if(l===e&&c===t&&s)continue;if(l===e)if(l+=n,0===n)c+=i,d+=i;else{var p=d-c;c=r+(c-t),d=c+p}else l+=n}a[f]=l,a[f+1]=c,a[f+2]=d}}},e}(),c=function(){function e(e,t,n){this._actual=e,this._startTokenIndex=t,this._endTokenIndex=n}return e.prototype.getCount=function(){return this._endTokenIndex-this._startTokenIndex+1},e.prototype.getStartCharacter=function(e){return this._actual.getStartCharacter(this._startTokenIndex+e)},e.prototype.getEndCharacter=function(e){return this._actual.getEndCharacter(this._startTokenIndex+e)},e.prototype.getMetadata=function(e){return this._actual.getMetadata(this._startTokenIndex+e)},e}(),d=function(){function e(e,t){this.startLineNumber=e,this.tokens=t,this.endLineNumber=this.startLineNumber+this.tokens.getMaxDeltaLine()}return e.prototype._updateEndLineNumber=function(){this.endLineNumber=this.startLineNumber+this.tokens.getMaxDeltaLine()},e.prototype.getLineTokens=function(t){if(this.startLineNumber<=t&&t<=this.endLineNumber){var n=e._findTokensWithLine(this.tokens,t-this.startLineNumber);if(n){var i=n[0],r=n[1];return new c(this.tokens,i,r)}}return null},e._findTokensWithLine=function(e,t){var n=0,i=e.getTokenCount()-1;while(n<i){var r=n+Math.floor((i-n)/2),o=e.getDeltaLine(r);if(o<t)n=r+1;else{if(!(o>t)){var s=r;while(s>n&&e.getDeltaLine(s-1)===t)s--;var a=r;while(a<i&&e.getDeltaLine(a+1)===t)a++;return[s,a]}i=r-1}}return e.getDeltaLine(n)===t?[n,n]:null},e.prototype.applyEdit=function(e,t){var n=a(t),i=n[0],r=n[1],o=n[2];this.acceptEdit(e,i,r,o,t.length>0?t.charCodeAt(0):0)},e.prototype.acceptEdit=function(e,t,n,i,r){this._acceptDeleteRange(e),this._acceptInsertText(new o["a"](e.startLineNumber,e.startColumn),t,n,i,r),this._updateEndLineNumber()},e.prototype._acceptDeleteRange=function(e){if(e.startLineNumber!==e.endLineNumber||e.startColumn!==e.endColumn){var t=e.startLineNumber-this.startLineNumber,n=e.endLineNumber-this.startLineNumber;if(n<0){var i=n-t;this.startLineNumber-=i}else{var r=this.tokens.getMaxDeltaLine();if(!(t>=r+1)){if(t<0&&n>=r+1)return this.startLineNumber=0,void this.tokens.clear();if(t<0){var o=-t;this.startLineNumber-=o,this.tokens.acceptDeleteRange(e.startColumn-1,0,0,n,e.endColumn-1)}else this.tokens.acceptDeleteRange(0,t,e.startColumn-1,n,e.endColumn-1)}}}},e.prototype._acceptInsertText=function(e,t,n,i,r){if(0!==t||0!==n){var o=e.lineNumber-this.startLineNumber;if(o<0)this.startLineNumber+=t;else{var s=this.tokens.getMaxDeltaLine();o>=s+1||this.tokens.acceptInsertText(o,e.column-1,t,n,i,r)}}},e}(),p=function(){function e(e,t){this.startLineNumber=e,this.tokens=t}return e}();function g(e){return e instanceof Uint32Array?e:new Uint32Array(e)}var _=function(){function e(){this._pieces=[]}return e.prototype.flush=function(){this._pieces=[]},e.prototype.set=function(e){this._pieces=e||[]},e.prototype.addSemanticTokens=function(t,n){var i=this._pieces;if(0===i.length)return n;var o=e._findFirstPieceWithLine(i,t),s=this._pieces[o].getLineTokens(t);if(!s)return n;for(var a=n.getCount(),u=s.getCount(),h=0,f=[],l=0,c=0;c<u;c++){var d=s.getStartCharacter(c),p=s.getEndCharacter(c),g=s.getMetadata(c),_=((1&g?2048:0)|(2&g?4096:0)|(4&g?8192:0)|(8&g?8372224:0)|(16&g?4286578688:0))>>>0,v=~_>>>0;while(h<a&&n.getEndOffset(h)<=d)f[l++]=n.getEndOffset(h),f[l++]=n.getMetadata(h),h++;h<a&&n.getStartOffset(h)<d&&(f[l++]=d,f[l++]=n.getMetadata(h));while(h<a&&n.getEndOffset(h)<p)f[l++]=n.getEndOffset(h),f[l++]=n.getMetadata(h)&v|g&_,h++;if(h<a&&n.getEndOffset(h)===p)f[l++]=n.getEndOffset(h),f[l++]=n.getMetadata(h)&v|g&_,h++;else{var m=Math.min(Math.max(0,h-1),a-1);f[l++]=p,f[l++]=n.getMetadata(m)&v|g&_}}while(h<a)f[l++]=n.getEndOffset(h),f[l++]=n.getMetadata(h),h++;return new r["a"](new Uint32Array(f),n.getLineContent())},e._findFirstPieceWithLine=function(e,t){var n=0,i=e.length-1;while(n<i){var r=n+Math.floor((i-n)/2);if(e[r].endLineNumber<t)n=r+1;else{if(!(e[r].startLineNumber>t)){while(r>n&&e[r-1].startLineNumber<=t&&t<=e[r-1].endLineNumber)r--;return r}i=r-1}}return n},e.prototype.acceptEdit=function(e,t,n,i,r){for(var o=0,s=this._pieces;o<s.length;o++){var a=s[o];a.acceptEdit(e,t,n,i,r)}},e}(),v=function(){function e(){this._lineTokens=[],this._len=0}return e.prototype.flush=function(){this._lineTokens=[],this._len=0},e.prototype.getTokens=function(e,t,n){var i=null;if(t<this._len&&(i=this._lineTokens[t]),null!==i&&i!==h)return new r["a"](g(i),n);var o=new Uint32Array(2);return o[0]=n.length,o[1]=u(e),new r["a"](o,n)},e._massageTokens=function(e,t,n){var i=n?g(n):null;if(0===t){var r=!1;if(i&&i.length>1&&(r=s["A"].getLanguageId(i[1])!==e),!r)return h}if(!i||0===i.length){var o=new Uint32Array(2);return o[0]=t,o[1]=u(e),o.buffer}return i[i.length-2]=t,0===i.byteOffset&&i.byteLength===i.buffer.byteLength?i.buffer:i},e.prototype._ensureLine=function(e){while(e>=this._len)this._lineTokens[this._len]=null,this._len++},e.prototype._deleteLines=function(e,t){0!==t&&(e+t>this._len&&(t=this._len-e),this._lineTokens.splice(e,t),this._len-=t)},e.prototype._insertLines=function(e,t){if(0!==t){for(var n=[],r=0;r<t;r++)n[r]=null;this._lineTokens=i["a"](this._lineTokens,e,n),this._len+=t}},e.prototype.setTokens=function(t,n,i,r){var o=e._massageTokens(t,i,r);this._ensureLine(n),this._lineTokens[n]=o},e.prototype.acceptEdit=function(e,t,n){this._acceptDeleteRange(e),this._acceptInsertText(new o["a"](e.startLineNumber,e.startColumn),t,n)},e.prototype._acceptDeleteRange=function(t){var n=t.startLineNumber-1;if(!(n>=this._len))if(t.startLineNumber!==t.endLineNumber){this._lineTokens[n]=e._deleteEnding(this._lineTokens[n],t.startColumn-1);var i=t.endLineNumber-1,r=null;i<this._len&&(r=e._deleteBeginning(this._lineTokens[i],t.endColumn-1)),this._lineTokens[n]=e._append(this._lineTokens[n],r),this._deleteLines(t.startLineNumber,t.endLineNumber-t.startLineNumber)}else{if(t.startColumn===t.endColumn)return;this._lineTokens[n]=e._delete(this._lineTokens[n],t.startColumn-1,t.endColumn-1)}},e.prototype._acceptInsertText=function(t,n,i){if(0!==n||0!==i){var r=t.lineNumber-1;r>=this._len||(0!==n?(this._lineTokens[r]=e._deleteEnding(this._lineTokens[r],t.column-1),this._lineTokens[r]=e._insert(this._lineTokens[r],t.column-1,i),this._insertLines(t.lineNumber,n)):this._lineTokens[r]=e._insert(this._lineTokens[r],t.column-1,i))}},e._deleteBeginning=function(t,n){return null===t||t===h?t:e._delete(t,0,n)},e._deleteEnding=function(t,n){if(null===t||t===h)return t;var i=g(t),r=i[i.length-2];return e._delete(t,n,r)},e._delete=function(e,t,n){if(null===e||e===h||t===n)return e;var i=g(e),o=i.length>>>1;if(0===t&&i[i.length-2]===n)return h;var s,a,u=r["a"].findIndexInTokensArray(i,t),f=u>0?i[u-1<<1]:0,l=i[u<<1];if(n<l){for(var c=n-t,d=u;d<o;d++)i[d<<1]-=c;return e}f!==t?(i[u<<1]=t,s=u+1<<1,a=t):(s=u<<1,a=f);for(var p=n-t,_=u+1;_<o;_++){var v=i[_<<1]-p;v>a&&(i[s++]=v,i[s++]=i[1+(_<<1)],a=v)}if(s===i.length)return e;var m=new Uint32Array(s);return m.set(i.subarray(0,s),0),m.buffer},e._append=function(e,t){if(t===h)return e;if(e===h)return t;if(null===e)return e;if(null===t)return null;var n=g(e),i=g(t),r=i.length>>>1,o=new Uint32Array(n.length+i.length);o.set(n,0);for(var s=n.length,a=n[n.length-2],u=0;u<r;u++)o[s++]=i[u<<1]+a,o[s++]=i[1+(u<<1)];return o.buffer},e._insert=function(e,t,n){if(null===e||e===h)return e;var i=g(e),o=i.length>>>1,s=r["a"].findIndexInTokensArray(i,t);if(s>0){var a=i[s-1<<1];a===t&&s--}for(var u=s;u<o;u++)i[u<<1]+=n;return e},e}()},"8c027":function(e,t,n){"use strict";n.d(t,"a",(function(){return h})),n.d(t,"d",(function(){return c})),n.d(t,"c",(function(){return p})),n.d(t,"e",(function(){return v})),n.d(t,"b",(function(){return m}));var i=n("3742"),r=n("e6ff"),o=n("7061"),s=n("6a89"),a=n("3352"),u=999,h=function(){function e(e,t,n,i){this.searchString=e,this.isRegex=t,this.matchCase=n,this.wordSeparators=i}return e.prototype.parseSearchRequest=function(){if(""===this.searchString)return null;var e;e=this.isRegex?f(this.searchString):this.searchString.indexOf("\n")>=0;var t=null;try{t=i["l"](this.searchString,this.isRegex,{matchCase:this.matchCase,wholeWord:!1,multiline:e,global:!0,unicode:!0})}catch(o){return null}if(!t)return null;var n=!this.isRegex&&!e;return n&&this.searchString.toLowerCase()!==this.searchString.toUpperCase()&&(n=this.matchCase),new l(t,this.wordSeparators?Object(r["a"])(this.wordSeparators):null,n?this.searchString:null)},e}();function f(e){if(!e||0===e.length)return!1;for(var t=0,n=e.length;t<n;t++){var i=e.charCodeAt(t);if(92===i){if(t++,t>=n)break;var r=e.charCodeAt(t);if(110===r||114===r||87===r||119===r)return!0}}return!1}var l=function(){function e(e,t,n){this.regex=e,this.wordSeparators=t,this.simpleSearch=n}return e}();function c(e,t,n){if(!n)return new a["b"](e,null);for(var i=[],r=0,o=t.length;r<o;r++)i[r]=t[r];return new a["b"](e,i)}var d=function(){function e(e){for(var t=[],n=0,i=0,r=e.length;i<r;i++)10===e.charCodeAt(i)&&(t[n++]=i);this._lineFeedsOffsets=t}return e.prototype.findLineFeedCountBeforeOffset=function(e){var t=this._lineFeedsOffsets,n=0,i=t.length-1;if(-1===i)return 0;if(e<=t[0])return 0;while(n<i){var r=n+((i-n)/2>>0);t[r]>=e?i=r-1:t[r+1]>=e?(n=r,i=r):n=r+1}return n+1},e}(),p=function(){function e(){}return e.findMatches=function(e,t,n,i,r){var o=t.parseSearchRequest();return o?o.regex.multiline?this._doFindMatchesMultiline(e,n,new m(o.wordSeparators,o.regex),i,r):this._doFindMatchesLineByLine(e,n,o,i,r):[]},e._getMultilineMatchRange=function(e,t,n,i,r,o){var a,u,h=0;if(i?(h=i.findLineFeedCountBeforeOffset(r),a=t+r+h):a=t+r,i){var f=i.findLineFeedCountBeforeOffset(r+o.length),l=f-h;u=a+o.length+l}else u=a+o.length;var c=e.getPositionAt(a),d=e.getPositionAt(u);return new s["a"](c.lineNumber,c.column,d.lineNumber,d.column)},e._doFindMatchesMultiline=function(e,t,n,i,r){var o,s=e.getOffsetAt(t.getStartPosition()),a=e.getValueInRange(t,1),u="\r\n"===e.getEOL()?new d(a):null,h=[],f=0;n.reset(0);while(o=n.next(a))if(h[f++]=c(this._getMultilineMatchRange(e,s,a,u,o.index,o[0]),o,i),f>=r)return h;return h},e._doFindMatchesLineByLine=function(e,t,n,i,r){var o=[],s=0;if(t.startLineNumber===t.endLineNumber){var a=e.getLineContent(t.startLineNumber).substring(t.startColumn-1,t.endColumn-1);return s=this._findMatchesInLine(n,a,t.startLineNumber,t.startColumn-1,s,o,i,r),o}var u=e.getLineContent(t.startLineNumber).substring(t.startColumn-1);s=this._findMatchesInLine(n,u,t.startLineNumber,t.startColumn-1,s,o,i,r);for(var h=t.startLineNumber+1;h<t.endLineNumber&&s<r;h++)s=this._findMatchesInLine(n,e.getLineContent(h),h,0,s,o,i,r);if(s<r){var f=e.getLineContent(t.endLineNumber).substring(0,t.endColumn-1);s=this._findMatchesInLine(n,f,t.endLineNumber,0,s,o,i,r)}return o},e._findMatchesInLine=function(e,t,n,i,r,o,u,h){var f=e.wordSeparators;if(!u&&e.simpleSearch){var l=e.simpleSearch,d=l.length,p=t.length,g=-d;while(-1!==(g=t.indexOf(l,g+d)))if((!f||v(f,t,p,g,d))&&(o[r++]=new a["b"](new s["a"](n,g+1+i,n,g+1+d+i),null),r>=h))return r;return r}var _,L=new m(e.wordSeparators,e.regex);L.reset(0);do{if(_=L.next(t),_&&(o[r++]=c(new s["a"](n,_.index+1+i,n,_.index+1+_[0].length+i),_,u),r>=h))return r}while(_);return r},e.findNextMatch=function(e,t,n,i){var r=t.parseSearchRequest();if(!r)return null;var o=new m(r.wordSeparators,r.regex);return r.regex.multiline?this._doFindNextMatchMultiline(e,n,o,i):this._doFindNextMatchLineByLine(e,n,o,i)},e._doFindNextMatchMultiline=function(e,t,n,i){var r=new o["a"](t.lineNumber,1),a=e.getOffsetAt(r),u=e.getLineCount(),h=e.getValueInRange(new s["a"](r.lineNumber,r.column,u,e.getLineMaxColumn(u)),1),f="\r\n"===e.getEOL()?new d(h):null;n.reset(t.column-1);var l=n.next(h);return l?c(this._getMultilineMatchRange(e,a,h,f,l.index,l[0]),l,i):1!==t.lineNumber||1!==t.column?this._doFindNextMatchMultiline(e,new o["a"](1,1),n,i):null},e._doFindNextMatchLineByLine=function(e,t,n,i){var r=e.getLineCount(),o=t.lineNumber,s=e.getLineContent(o),a=this._findFirstMatchInLine(n,s,o,t.column,i);if(a)return a;for(var u=1;u<=r;u++){var h=(o+u-1)%r,f=e.getLineContent(h+1),l=this._findFirstMatchInLine(n,f,h+1,1,i);if(l)return l}return null},e._findFirstMatchInLine=function(e,t,n,i,r){e.reset(i-1);var o=e.next(t);return o?c(new s["a"](n,o.index+1,n,o.index+1+o[0].length),o,r):null},e.findPreviousMatch=function(e,t,n,i){var r=t.parseSearchRequest();if(!r)return null;var o=new m(r.wordSeparators,r.regex);return r.regex.multiline?this._doFindPreviousMatchMultiline(e,n,o,i):this._doFindPreviousMatchLineByLine(e,n,o,i)},e._doFindPreviousMatchMultiline=function(e,t,n,i){var r=this._doFindMatchesMultiline(e,new s["a"](1,1,t.lineNumber,t.column),n,i,10*u);if(r.length>0)return r[r.length-1];var a=e.getLineCount();return t.lineNumber!==a||t.column!==e.getLineMaxColumn(a)?this._doFindPreviousMatchMultiline(e,new o["a"](a,e.getLineMaxColumn(a)),n,i):null},e._doFindPreviousMatchLineByLine=function(e,t,n,i){var r=e.getLineCount(),o=t.lineNumber,s=e.getLineContent(o).substring(0,t.column-1),a=this._findLastMatchInLine(n,s,o,i);if(a)return a;for(var u=1;u<=r;u++){var h=(r+o-u-1)%r,f=e.getLineContent(h+1),l=this._findLastMatchInLine(n,f,h+1,i);if(l)return l}return null},e._findLastMatchInLine=function(e,t,n,i){var r,o=null;e.reset(0);while(r=e.next(t))o=c(new s["a"](n,r.index+1,n,r.index+1+r[0].length),r,i);return o},e}();function g(e,t,n,i,r){if(0===i)return!0;var o=t.charCodeAt(i-1);if(0!==e.get(o))return!0;if(13===o||10===o)return!0;if(r>0){var s=t.charCodeAt(i);if(0!==e.get(s))return!0}return!1}function _(e,t,n,i,r){if(i+r===n)return!0;var o=t.charCodeAt(i+r);if(0!==e.get(o))return!0;if(13===o||10===o)return!0;if(r>0){var s=t.charCodeAt(i+r-1);if(0!==e.get(s))return!0}return!1}function v(e,t,n,i,r){return g(e,t,n,i,r)&&_(e,t,n,i,r)}var m=function(){function e(e,t){this._wordSeparators=e,this._searchRegex=t,this._prevMatchStartIndex=-1,this._prevMatchLength=0}return e.prototype.reset=function(e){this._searchRegex.lastIndex=e,this._prevMatchStartIndex=-1,this._prevMatchLength=0},e.prototype.next=function(e){var t,n=e.length;do{if(this._prevMatchStartIndex+this._prevMatchLength===n)return null;if(t=this._searchRegex.exec(e),!t)return null;var i=t.index,r=t[0].length;if(i===this._prevMatchStartIndex&&r===this._prevMatchLength){if(0===r){this._searchRegex.lastIndex+=1;continue}return null}if(this._prevMatchStartIndex=i,this._prevMatchLength=r,!this._wordSeparators||v(this._wordSeparators,e,n,i,r))return t}while(t);return null},e}()},a411:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var i=n("7061"),r=n("2de5"),o=function(){function e(e,t,n,i){this._uri=e,this._lines=t,this._eol=n,this._versionId=i,this._lineStarts=null}return e.prototype.dispose=function(){this._lines.length=0},e.prototype.getText=function(){return this._lines.join(this._eol)},e.prototype.onEvents=function(e){e.eol&&e.eol!==this._eol&&(this._eol=e.eol,this._lineStarts=null);for(var t=e.changes,n=0,r=t;n<r.length;n++){var o=r[n];this._acceptDeleteRange(o.range),this._acceptInsertText(new i["a"](o.range.startLineNumber,o.range.startColumn),o.text)}this._versionId=e.versionId},e.prototype._ensureLineStarts=function(){if(!this._lineStarts){for(var e=this._eol.length,t=this._lines.length,n=new Uint32Array(t),i=0;i<t;i++)n[i]=this._lines[i].length+e;this._lineStarts=new r["a"](n)}},e.prototype._setLineText=function(e,t){this._lines[e]=t,this._lineStarts&&this._lineStarts.changeValue(e,this._lines[e].length+this._eol.length)},e.prototype._acceptDeleteRange=function(e){if(e.startLineNumber!==e.endLineNumber)this._setLineText(e.startLineNumber-1,this._lines[e.startLineNumber-1].substring(0,e.startColumn-1)+this._lines[e.endLineNumber-1].substring(e.endColumn-1)),this._lines.splice(e.startLineNumber,e.endLineNumber-e.startLineNumber),this._lineStarts&&this._lineStarts.removeValues(e.startLineNumber,e.endLineNumber-e.startLineNumber);else{if(e.startColumn===e.endColumn)return;this._setLineText(e.startLineNumber-1,this._lines[e.startLineNumber-1].substring(0,e.startColumn-1)+this._lines[e.startLineNumber-1].substring(e.endColumn-1))}},e.prototype._acceptInsertText=function(e,t){if(0!==t.length){var n=t.split(/\r\n|\r|\n/);if(1!==n.length){n[n.length-1]+=this._lines[e.lineNumber-1].substring(e.column-1),this._setLineText(e.lineNumber-1,this._lines[e.lineNumber-1].substring(0,e.column-1)+n[0]);for(var i=new Uint32Array(n.length-1),r=1;r<n.length;r++)this._lines.splice(e.lineNumber+r-1,0,n[r]),i[r-1]=n[r].length+this._eol.length;this._lineStarts&&this._lineStarts.insertValues(e.lineNumber,i)}else this._setLineText(e.lineNumber-1,this._lines[e.lineNumber-1].substring(0,e.column-1)+n[0]+this._lines[e.lineNumber-1].substring(e.column-1))}},e}()},b57f:function(e,t,n){"use strict";n.d(t,"b",(function(){return st})),n.d(t,"a",(function(){return ct}));var i=n("fdcc"),r=n("308f"),o=n("a666"),s=n("3742"),a=n("6d8e"),u=n("fd49"),h=n("7061"),f=n("6a89"),l=n("8025"),c=n("3352"),d=function(){function e(e,t){this.beforeVersionId=e,this.beforeCursorState=t,this.afterCursorState=null,this.afterVersionId=-1,this.editOperations=[]}return e.prototype.undo=function(e){for(var t=this.editOperations.length-1;t>=0;t--)this.editOperations[t]={operations:e.applyEdits(this.editOperations[t].operations)}},e.prototype.redo=function(e){for(var t=0;t<this.editOperations.length;t++)this.editOperations[t]={operations:e.applyEdits(this.editOperations[t].operations)}},e}();function p(e){var t=e.getEOL();return"\n"===t?0:1}var g=function(){function e(e,t){this.beforeVersionId=e,this.beforeCursorState=null,this.afterCursorState=null,this.afterVersionId=-1,this.eol=t}return e.prototype.undo=function(e){var t=p(e);e.setEOL(this.eol),this.eol=t},e.prototype.redo=function(e){var t=p(e);e.setEOL(this.eol),this.eol=t},e}(),_=function(){function e(e){this.model=e,this.currentOpenStackElement=null,this.past=[],this.future=[]}return e.prototype.pushStackElement=function(){null!==this.currentOpenStackElement&&(this.past.push(this.currentOpenStackElement),this.currentOpenStackElement=null)},e.prototype.clear=function(){this.currentOpenStackElement=null,this.past=[],this.future=[]},e.prototype.pushEOL=function(e){this.future=[],this.currentOpenStackElement&&this.pushStackElement();var t=p(this.model),n=new g(this.model.getAlternativeVersionId(),t);this.model.setEOL(e),n.afterVersionId=this.model.getVersionId(),this.currentOpenStackElement=n,this.pushStackElement()},e.prototype.pushEditOperation=function(t,n,i){this.future=[];var r=null;this.currentOpenStackElement&&(this.currentOpenStackElement instanceof d?r=this.currentOpenStackElement:this.pushStackElement()),this.currentOpenStackElement||(r=new d(this.model.getAlternativeVersionId(),t),this.currentOpenStackElement=r);var o={operations:this.model.applyEdits(n)};return r.editOperations.push(o),r.afterCursorState=e._computeCursorState(i,o.operations),r.afterVersionId=this.model.getVersionId(),r.afterCursorState},e._computeCursorState=function(e,t){try{return e?e(t):null}catch(n){return Object(i["e"])(n),null}},e.prototype.undo=function(){if(this.pushStackElement(),this.past.length>0){var e=this.past.pop();try{e.undo(this.model)}catch(t){return Object(i["e"])(t),this.clear(),null}return this.future.push(e),{selections:e.beforeCursorState,recordedVersionId:e.beforeVersionId}}return null},e.prototype.canUndo=function(){return this.past.length>0||null!==this.currentOpenStackElement},e.prototype.redo=function(){if(this.future.length>0){var e=this.future.pop();try{e.redo(this.model)}catch(t){return Object(i["e"])(t),this.clear(),null}return this.past.push(e),{selections:e.afterCursorState,recordedVersionId:e.afterVersionId}}return null},e.prototype.canRedo=function(){return this.future.length>0},e}(),v=function(){function e(){this.spacesDiff=0,this.looksLikeAlignment=!1}return e}();function m(e,t,n,i,r){var o;for(r.spacesDiff=0,r.looksLikeAlignment=!1,o=0;o<t&&o<i;o++){var s=e.charCodeAt(o),a=n.charCodeAt(o);if(s!==a)break}for(var u=0,h=0,f=o;f<t;f++){s=e.charCodeAt(f);32===s?u++:h++}var l=0,c=0;for(f=o;f<i;f++){a=n.charCodeAt(f);32===a?l++:c++}if(!(u>0&&h>0)&&!(l>0&&c>0)){var d=Math.abs(h-c),p=Math.abs(u-l);if(0===d)return r.spacesDiff=p,void(p>0&&0<=l-1&&l-1<e.length&&l<n.length&&32!==n.charCodeAt(l)&&32===e.charCodeAt(l-1)&&44===e.charCodeAt(e.length-1)&&(r.looksLikeAlignment=!0));p%d!==0||(r.spacesDiff=p/d)}}function L(e,t,n){for(var i=Math.min(e.getLineCount(),1e4),r=0,o=0,s="",a=0,u=[2,4,6,8,3,5,7],h=8,f=[0,0,0,0,0,0,0,0,0],l=new v,c=1;c<=i;c++){for(var d=e.getLineLength(c),p=e.getLineContent(c),g=d<=65536,_=!1,L=0,b=0,C=0,I=0,y=d;I<y;I++){var N=g?p.charCodeAt(I):e.getLineCharCode(c,I);if(9===N)C++;else{if(32!==N){_=!0,L=I;break}b++}}if(_&&(C>0?r++:b>1&&o++,m(s,a,p,L,l),!l.looksLikeAlignment||n&&t===l.spacesDiff)){var k=l.spacesDiff;k<=h&&f[k]++,s=p,a=L}}var w=n;r!==o&&(w=r<o);var S=t;if(w){var x=w?0:.1*i;u.forEach((function(e){var t=f[e];t>x&&(x=t,S=e)})),4===S&&f[4]>0&&f[2]>0&&f[2]>=f[4]/2&&(S=2)}return{insertSpaces:w,tabSize:S}}function b(e){return(1&e.metadata)>>>0}function C(e,t){e.metadata=254&e.metadata|t<<0}function I(e){return(2&e.metadata)>>>1===1}function y(e,t){e.metadata=253&e.metadata|(t?1:0)<<1}function N(e){return(4&e.metadata)>>>2===1}function k(e,t){e.metadata=251&e.metadata|(t?1:0)<<2}function w(e){return(8&e.metadata)>>>3===1}function S(e,t){e.metadata=247&e.metadata|(t?1:0)<<3}function x(e){return(48&e.metadata)>>>4}function E(e,t){e.metadata=207&e.metadata|t<<4}function O(e){return(64&e.metadata)>>>6===1}function T(e,t){e.metadata=191&e.metadata|(t?1:0)<<6}var D=function(){function e(e,t,n){this.metadata=0,this.parent=this,this.left=this,this.right=this,C(this,1),this.start=t,this.end=n,this.delta=0,this.maxEnd=n,this.id=e,this.ownerId=0,this.options=null,k(this,!1),E(this,1),S(this,!1),T(this,!1),this.cachedVersionId=0,this.cachedAbsoluteStart=t,this.cachedAbsoluteEnd=n,this.range=null,y(this,!1)}return e.prototype.reset=function(e,t,n,i){this.start=t,this.end=n,this.maxEnd=n,this.cachedVersionId=e,this.cachedAbsoluteStart=t,this.cachedAbsoluteEnd=n,this.range=i},e.prototype.setOptions=function(e){this.options=e;var t=this.options.className;k(this,"squiggly-error"===t||"squiggly-warning"===t||"squiggly-info"===t),E(this,this.options.stickiness),S(this,!(!this.options.overviewRuler||!this.options.overviewRuler.color)),T(this,this.options.collapseOnReplaceEdit)},e.prototype.setCachedOffsets=function(e,t,n){this.cachedVersionId!==n&&(this.range=null),this.cachedVersionId=n,this.cachedAbsoluteStart=e,this.cachedAbsoluteEnd=t},e.prototype.detach=function(){this.parent=null,this.left=null,this.right=null},e}(),M=new D(null,0,0);M.parent=M,M.left=M,M.right=M,C(M,0);var A=function(){function e(){this.root=M,this.requestNormalizeDelta=!1}return e.prototype.intervalSearch=function(e,t,n,i,r){return this.root===M?[]:j(this,e,t,n,i,r)},e.prototype.search=function(e,t,n){return this.root===M?[]:U(this,e,t,n)},e.prototype.collectNodesFromOwner=function(e){return P(this,e)},e.prototype.collectNodesPostOrder=function(){return W(this)},e.prototype.insert=function(e){q(this,e),this._normalizeDeltaIfNecessary()},e.prototype.delete=function(e){G(this,e),this._normalizeDeltaIfNecessary()},e.prototype.resolveNode=function(e,t){var n=e,i=0;while(e!==this.root)e===e.parent.right&&(i+=e.parent.delta),e=e.parent;var r=n.start+i,o=n.end+i;n.setCachedOffsets(r,o,t)},e.prototype.acceptReplace=function(e,t,n,i){for(var r=F(this,e,e+t),o=0,s=r.length;o<s;o++){var a=r[o];G(this,a)}this._normalizeDeltaIfNecessary(),V(this,e,e+t,n),this._normalizeDeltaIfNecessary();for(o=0,s=r.length;o<s;o++){a=r[o];a.start=a.cachedAbsoluteStart,a.end=a.cachedAbsoluteEnd,B(a,e,e+t,n,i),a.maxEnd=a.end,q(this,a)}this._normalizeDeltaIfNecessary()},e.prototype._normalizeDeltaIfNecessary=function(){this.requestNormalizeDelta&&(this.requestNormalizeDelta=!1,R(this))},e}();function R(e){var t=e.root,n=0;while(t!==M)t.left===M||I(t.left)?t.right===M||I(t.right)?(t.start=n+t.start,t.end=n+t.end,t.delta=0,Q(t),y(t,!0),y(t.left,!1),y(t.right,!1),t===t.parent.right&&(n-=t.parent.delta),t=t.parent):(n+=t.delta,t=t.right):t=t.left;y(e.root,!1)}function z(e,t,n,i){return e<n||!(e>n)&&(1!==i&&(2===i||t))}function B(e,t,n,i,r){var o=x(e),s=0===o||2===o,a=1===o||2===o,u=n-t,h=i,f=Math.min(u,h),l=e.start,c=!1,d=e.end,p=!1;t<=l&&d<=n&&O(e)&&(e.start=t,c=!0,e.end=t,p=!0);var g=r?1:u>0?2:0;if(!c&&z(l,s,t,g)&&(c=!0),!p&&z(d,a,t,g)&&(p=!0),f>0&&!r){g=u>h?2:0;!c&&z(l,s,t+f,g)&&(c=!0),!p&&z(d,a,t+f,g)&&(p=!0)}g=r?1:0;!c&&z(l,s,n,g)&&(e.start=t+h,c=!0),!p&&z(d,a,n,g)&&(e.end=t+h,p=!0);var _=h-u;c||(e.start=Math.max(0,l+_)),p||(e.end=Math.max(0,d+_)),e.start>e.end&&(e.end=e.start)}function F(e,t,n){var i=e.root,r=0,o=0,s=0,a=0,u=[],h=0;while(i!==M)if(I(i))y(i.left,!1),y(i.right,!1),i===i.parent.right&&(r-=i.parent.delta),i=i.parent;else{if(!I(i.left)){if(o=r+i.maxEnd,o<t){y(i,!0);continue}if(i.left!==M){i=i.left;continue}}s=r+i.start,s>n?y(i,!0):(a=r+i.end,a>=t&&(i.setCachedOffsets(s,a,0),u[h++]=i),y(i,!0),i.right===M||I(i.right)||(r+=i.delta,i=i.right))}return y(e.root,!1),u}function V(e,t,n,i){var r=e.root,o=0,s=0,a=0,u=i-(n-t);while(r!==M)if(I(r))y(r.left,!1),y(r.right,!1),r===r.parent.right&&(o-=r.parent.delta),Q(r),r=r.parent;else{if(!I(r.left)){if(s=o+r.maxEnd,s<t){y(r,!0);continue}if(r.left!==M){r=r.left;continue}}a=o+r.start,a>n?(r.start+=u,r.end+=u,r.delta+=u,(r.delta<-1073741824||r.delta>1073741824)&&(e.requestNormalizeDelta=!0),y(r,!0)):(y(r,!0),r.right===M||I(r.right)||(o+=r.delta,r=r.right))}y(e.root,!1)}function P(e,t){var n=e.root,i=[],r=0;while(n!==M)I(n)?(y(n.left,!1),y(n.right,!1),n=n.parent):n.left===M||I(n.left)?(n.ownerId===t&&(i[r++]=n),y(n,!0),n.right===M||I(n.right)||(n=n.right)):n=n.left;return y(e.root,!1),i}function W(e){var t=e.root,n=[],i=0;while(t!==M)I(t)?(y(t.left,!1),y(t.right,!1),t=t.parent):t.left===M||I(t.left)?t.right===M||I(t.right)?(n[i++]=t,y(t,!0)):t=t.right:t=t.left;return y(e.root,!1),n}function U(e,t,n,i){var r=e.root,o=0,s=0,a=0,u=[],h=0;while(r!==M)if(I(r))y(r.left,!1),y(r.right,!1),r===r.parent.right&&(o-=r.parent.delta),r=r.parent;else if(r.left===M||I(r.left)){s=o+r.start,a=o+r.end,r.setCachedOffsets(s,a,i);var f=!0;t&&r.ownerId&&r.ownerId!==t&&(f=!1),n&&N(r)&&(f=!1),f&&(u[h++]=r),y(r,!0),r.right===M||I(r.right)||(o+=r.delta,r=r.right)}else r=r.left;return y(e.root,!1),u}function j(e,t,n,i,r,o){var s=e.root,a=0,u=0,h=0,f=0,l=[],c=0;while(s!==M)if(I(s))y(s.left,!1),y(s.right,!1),s===s.parent.right&&(a-=s.parent.delta),s=s.parent;else{if(!I(s.left)){if(u=a+s.maxEnd,u<t){y(s,!0);continue}if(s.left!==M){s=s.left;continue}}if(h=a+s.start,h>n)y(s,!0);else{if(f=a+s.end,f>=t){s.setCachedOffsets(h,f,o);var d=!0;i&&s.ownerId&&s.ownerId!==i&&(d=!1),r&&N(s)&&(d=!1),d&&(l[c++]=s)}y(s,!0),s.right===M||I(s.right)||(a+=s.delta,s=s.right)}}return y(e.root,!1),l}function q(e,t){if(e.root===M)return t.parent=M,t.left=M,t.right=M,C(t,0),e.root=t,e.root;H(e,t),X(t.parent);var n=t;while(n!==e.root&&1===b(n.parent))if(n.parent===n.parent.parent.left){var i=n.parent.parent.right;1===b(i)?(C(n.parent,0),C(i,0),C(n.parent.parent,1),n=n.parent.parent):(n===n.parent.right&&(n=n.parent,J(e,n)),C(n.parent,0),C(n.parent.parent,1),Z(e,n.parent.parent))}else{i=n.parent.parent.left;1===b(i)?(C(n.parent,0),C(i,0),C(n.parent.parent,1),n=n.parent.parent):(n===n.parent.left&&(n=n.parent,Z(e,n)),C(n.parent,0),C(n.parent.parent,1),J(e,n.parent.parent))}return C(e.root,0),t}function H(e,t){var n=0,i=e.root,r=t.start,o=t.end;while(1){var s=ee(r,o,i.start+n,i.end+n);if(s<0){if(i.left===M){t.start-=n,t.end-=n,t.maxEnd-=n,i.left=t;break}i=i.left}else{if(i.right===M){t.start-=n+i.delta,t.end-=n+i.delta,t.maxEnd-=n+i.delta,i.right=t;break}n+=i.delta,i=i.right}}t.parent=i,t.left=M,t.right=M,C(t,1)}function G(e,t){var n,i;if(t.left===M?(n=t.right,i=t,n.delta+=t.delta,(n.delta<-1073741824||n.delta>1073741824)&&(e.requestNormalizeDelta=!0),n.start+=t.delta,n.end+=t.delta):t.right===M?(n=t.left,i=t):(i=$(t.right),n=i.right,n.start+=i.delta,n.end+=i.delta,n.delta+=i.delta,(n.delta<-1073741824||n.delta>1073741824)&&(e.requestNormalizeDelta=!0),i.start+=t.delta,i.end+=t.delta,i.delta=t.delta,(i.delta<-1073741824||i.delta>1073741824)&&(e.requestNormalizeDelta=!0)),i===e.root)return e.root=n,C(n,0),t.detach(),Y(),Q(n),void(e.root.parent=M);var r,o=1===b(i);if(i===i.parent.left?i.parent.left=n:i.parent.right=n,i===t?n.parent=i.parent:(i.parent===t?n.parent=i:n.parent=i.parent,i.left=t.left,i.right=t.right,i.parent=t.parent,C(i,b(t)),t===e.root?e.root=i:t===t.parent.left?t.parent.left=i:t.parent.right=i,i.left!==M&&(i.left.parent=i),i.right!==M&&(i.right.parent=i)),t.detach(),o)return X(n.parent),i!==t&&(X(i),X(i.parent)),void Y();X(n),X(n.parent),i!==t&&(X(i),X(i.parent));while(n!==e.root&&0===b(n))n===n.parent.left?(r=n.parent.right,1===b(r)&&(C(r,0),C(n.parent,1),J(e,n.parent),r=n.parent.right),0===b(r.left)&&0===b(r.right)?(C(r,1),n=n.parent):(0===b(r.right)&&(C(r.left,0),C(r,1),Z(e,r),r=n.parent.right),C(r,b(n.parent)),C(n.parent,0),C(r.right,0),J(e,n.parent),n=e.root)):(r=n.parent.left,1===b(r)&&(C(r,0),C(n.parent,1),Z(e,n.parent),r=n.parent.left),0===b(r.left)&&0===b(r.right)?(C(r,1),n=n.parent):(0===b(r.left)&&(C(r.right,0),C(r,1),J(e,r),r=n.parent.left),C(r,b(n.parent)),C(n.parent,0),C(r.left,0),Z(e,n.parent),n=e.root));C(n,0),Y()}function $(e){while(e.left!==M)e=e.left;return e}function Y(){M.parent=M,M.delta=0,M.start=0,M.end=0}function J(e,t){var n=t.right;n.delta+=t.delta,(n.delta<-1073741824||n.delta>1073741824)&&(e.requestNormalizeDelta=!0),n.start+=t.delta,n.end+=t.delta,t.right=n.left,n.left!==M&&(n.left.parent=t),n.parent=t.parent,t.parent===M?e.root=n:t===t.parent.left?t.parent.left=n:t.parent.right=n,n.left=t,t.parent=n,Q(t),Q(n)}function Z(e,t){var n=t.left;t.delta-=n.delta,(t.delta<-1073741824||t.delta>1073741824)&&(e.requestNormalizeDelta=!0),t.start-=n.delta,t.end-=n.delta,t.left=n.right,n.right!==M&&(n.right.parent=t),n.parent=t.parent,t.parent===M?e.root=n:t===t.parent.right?t.parent.right=n:t.parent.left=n,n.right=t,t.parent=n,Q(t),Q(n)}function K(e){var t=e.end;if(e.left!==M){var n=e.left.maxEnd;n>t&&(t=n)}if(e.right!==M){var i=e.right.maxEnd+e.delta;i>t&&(t=i)}return t}function Q(e){e.maxEnd=K(e)}function X(e){while(e!==M){var t=K(e);if(e.maxEnd===t)return;e.maxEnd=t,e=e.parent}}function ee(e,t,n,i){return e===n?t-i:e-n}var te=function(){function e(e,t){this.piece=e,this.color=t,this.size_left=0,this.lf_left=0,this.parent=this,this.left=this,this.right=this}return e.prototype.next=function(){if(this.right!==ne)return ie(this.right);var e=this;while(e.parent!==ne){if(e.parent.left===e)break;e=e.parent}return e.parent===ne?ne:e.parent},e.prototype.prev=function(){if(this.left!==ne)return re(this.left);var e=this;while(e.parent!==ne){if(e.parent.right===e)break;e=e.parent}return e.parent===ne?ne:e.parent},e.prototype.detach=function(){this.parent=null,this.left=null,this.right=null},e}(),ne=new te(null,0);function ie(e){while(e.left!==ne)e=e.left;return e}function re(e){while(e.right!==ne)e=e.right;return e}function oe(e){return e===ne?0:e.size_left+e.piece.length+oe(e.right)}function se(e){return e===ne?0:e.lf_left+e.piece.lineFeedCnt+se(e.right)}function ae(){ne.parent=ne}function ue(e,t){var n=t.right;n.size_left+=t.size_left+(t.piece?t.piece.length:0),n.lf_left+=t.lf_left+(t.piece?t.piece.lineFeedCnt:0),t.right=n.left,n.left!==ne&&(n.left.parent=t),n.parent=t.parent,t.parent===ne?e.root=n:t.parent.left===t?t.parent.left=n:t.parent.right=n,n.left=t,t.parent=n}function he(e,t){var n=t.left;t.left=n.right,n.right!==ne&&(n.right.parent=t),n.parent=t.parent,t.size_left-=n.size_left+(n.piece?n.piece.length:0),t.lf_left-=n.lf_left+(n.piece?n.piece.lineFeedCnt:0),t.parent===ne?e.root=n:t===t.parent.right?t.parent.right=n:t.parent.left=n,n.right=t,t.parent=n}function fe(e,t){var n,i;if(t.left===ne?(i=t,n=i.right):t.right===ne?(i=t,n=i.left):(i=ie(t.right),n=i.right),i===e.root)return e.root=n,n.color=0,t.detach(),ae(),void(e.root.parent=ne);var r=1===i.color;if(i===i.parent.left?i.parent.left=n:i.parent.right=n,i===t?(n.parent=i.parent,de(e,n)):(i.parent===t?n.parent=i:n.parent=i.parent,de(e,n),i.left=t.left,i.right=t.right,i.parent=t.parent,i.color=t.color,t===e.root?e.root=i:t===t.parent.left?t.parent.left=i:t.parent.right=i,i.left!==ne&&(i.left.parent=i),i.right!==ne&&(i.right.parent=i),i.size_left=t.size_left,i.lf_left=t.lf_left,de(e,i)),t.detach(),n.parent.left===n){var o=oe(n),s=se(n);if(o!==n.parent.size_left||s!==n.parent.lf_left){var a=o-n.parent.size_left,u=s-n.parent.lf_left;n.parent.size_left=o,n.parent.lf_left=s,ce(e,n.parent,a,u)}}if(de(e,n.parent),r)ae();else{var h;while(n!==e.root&&0===n.color)n===n.parent.left?(h=n.parent.right,1===h.color&&(h.color=0,n.parent.color=1,ue(e,n.parent),h=n.parent.right),0===h.left.color&&0===h.right.color?(h.color=1,n=n.parent):(0===h.right.color&&(h.left.color=0,h.color=1,he(e,h),h=n.parent.right),h.color=n.parent.color,n.parent.color=0,h.right.color=0,ue(e,n.parent),n=e.root)):(h=n.parent.left,1===h.color&&(h.color=0,n.parent.color=1,he(e,n.parent),h=n.parent.left),0===h.left.color&&0===h.right.color?(h.color=1,n=n.parent):(0===h.left.color&&(h.right.color=0,h.color=1,ue(e,h),h=n.parent.left),h.color=n.parent.color,n.parent.color=0,h.left.color=0,he(e,n.parent),n=e.root));n.color=0,ae()}}function le(e,t){de(e,t);while(t!==e.root&&1===t.parent.color)if(t.parent===t.parent.parent.left){var n=t.parent.parent.right;1===n.color?(t.parent.color=0,n.color=0,t.parent.parent.color=1,t=t.parent.parent):(t===t.parent.right&&(t=t.parent,ue(e,t)),t.parent.color=0,t.parent.parent.color=1,he(e,t.parent.parent))}else{n=t.parent.parent.left;1===n.color?(t.parent.color=0,n.color=0,t.parent.parent.color=1,t=t.parent.parent):(t===t.parent.left&&(t=t.parent,he(e,t)),t.parent.color=0,t.parent.parent.color=1,ue(e,t.parent.parent))}e.root.color=0}function ce(e,t,n,i){while(t!==e.root&&t!==ne)t.parent.left===t&&(t.parent.size_left+=n,t.parent.lf_left+=i),t=t.parent}function de(e,t){var n=0,i=0;if(t!==e.root){if(0===n){while(t!==e.root&&t===t.parent.right)t=t.parent;if(t===e.root)return;t=t.parent,n=oe(t.left)-t.size_left,i=se(t.left)-t.lf_left,t.size_left+=n,t.lf_left+=i}while(t!==e.root&&(0!==n||0!==i))t.parent.left===t&&(t.parent.size_left+=n,t.parent.lf_left+=i),t=t.parent}}ne.parent=ne,ne.left=ne,ne.right=ne,ne.color=0;var pe=n("8c027"),ge=65535;function _e(e){var t;return t=e[e.length-1]<65536?new Uint16Array(e.length):new Uint32Array(e.length),t.set(e,0),t}var ve=function(){function e(e,t,n,i,r){this.lineStarts=e,this.cr=t,this.lf=n,this.crlf=i,this.isBasicASCII=r}return e}();function me(e,t){void 0===t&&(t=!0);for(var n=[0],i=1,r=0,o=e.length;r<o;r++){var s=e.charCodeAt(r);13===s?r+1<o&&10===e.charCodeAt(r+1)?(n[i++]=r+2,r++):n[i++]=r+1:10===s&&(n[i++]=r+1)}return t?_e(n):n}function Le(e,t){e.length=0,e[0]=0;for(var n=1,i=0,r=0,o=0,s=!0,a=0,u=t.length;a<u;a++){var h=t.charCodeAt(a);13===h?a+1<u&&10===t.charCodeAt(a+1)?(o++,e[n++]=a+2,a++):(i++,e[n++]=a+1):10===h?(r++,e[n++]=a+1):s&&9!==h&&(h<32||h>126)&&(s=!1)}var f=new ve(_e(e),i,r,o,s);return e.length=0,f}var be=function(){function e(e,t,n,i,r){this.bufferIndex=e,this.start=t,this.end=n,this.lineFeedCnt=i,this.length=r}return e}(),Ce=function(){function e(e,t){this.buffer=e,this.lineStarts=t}return e}(),Ie=function(){function e(e){this._limit=e,this._cache=[]}return e.prototype.get=function(e){for(var t=this._cache.length-1;t>=0;t--){var n=this._cache[t];if(n.nodeStartOffset<=e&&n.nodeStartOffset+n.node.piece.length>=e)return n}return null},e.prototype.get2=function(e){for(var t=this._cache.length-1;t>=0;t--){var n=this._cache[t];if(n.nodeStartLineNumber&&n.nodeStartLineNumber<e&&n.nodeStartLineNumber+n.node.piece.lineFeedCnt>=e)return n}return null},e.prototype.set=function(e){this._cache.length>=this._limit&&this._cache.shift(),this._cache.push(e)},e.prototype.valdiate=function(e){for(var t=!1,n=this._cache,i=0;i<n.length;i++){var r=n[i];(null===r.node.parent||r.nodeStartOffset>=e)&&(n[i]=null,t=!0)}if(t){for(var o=[],s=0,a=n;s<a.length;s++){var u=a[s];null!==u&&o.push(u)}this._cache=o}},e}(),ye=function(){function e(e,t,n){this.create(e,t,n)}return e.prototype.create=function(e,t,n){this._buffers=[new Ce("",[0])],this._lastChangeBufferPos={line:0,column:0},this.root=ne,this._lineCnt=1,this._length=0,this._EOL=t,this._EOLLength=t.length,this._EOLNormalized=n;for(var i=null,r=0,o=e.length;r<o;r++)if(e[r].buffer.length>0){e[r].lineStarts||(e[r].lineStarts=me(e[r].buffer));var s=new be(r+1,{line:0,column:0},{line:e[r].lineStarts.length-1,column:e[r].buffer.length-e[r].lineStarts[e[r].lineStarts.length-1]},e[r].lineStarts.length-1,e[r].buffer.length);this._buffers.push(e[r]),i=this.rbInsertRight(i,s)}this._searchCache=new Ie(1),this._lastVisitedLine={lineNumber:0,value:""},this.computeBufferMetadata()},e.prototype.normalizeEOL=function(e){var t=this,n=ge,i=n-Math.floor(n/3),r=2*i,o="",s=0,a=[];if(this.iterate(this.root,(function(n){var u=t.getNodeContent(n),h=u.length;if(s<=i||s+h<r)return o+=u,s+=h,!0;var f=o.replace(/\r\n|\r|\n/g,e);return a.push(new Ce(f,me(f))),o=u,s=h,!0})),s>0){var u=o.replace(/\r\n|\r|\n/g,e);a.push(new Ce(u,me(u)))}this.create(a,e,!0)},e.prototype.getEOL=function(){return this._EOL},e.prototype.setEOL=function(e){this._EOL=e,this._EOLLength=this._EOL.length,this.normalizeEOL(e)},e.prototype.getOffsetAt=function(e,t){var n=0,i=this.root;while(i!==ne)if(i.left!==ne&&i.lf_left+1>=e)i=i.left;else{if(i.lf_left+i.piece.lineFeedCnt+1>=e){n+=i.size_left;var r=this.getAccumulatedValue(i,e-i.lf_left-2);return n+(r+t-1)}e-=i.lf_left+i.piece.lineFeedCnt,n+=i.size_left+i.piece.length,i=i.right}return n},e.prototype.getPositionAt=function(e){e=Math.floor(e),e=Math.max(0,e);var t=this.root,n=0,i=e;while(t!==ne)if(0!==t.size_left&&t.size_left>=e)t=t.left;else{if(t.size_left+t.piece.length>=e){var r=this.getIndexOf(t,e-t.size_left);if(n+=t.lf_left+r.index,0===r.index){var o=this.getOffsetAt(n+1,1),s=i-o;return new h["a"](n+1,s+1)}return new h["a"](n+1,r.remainder+1)}if(e-=t.size_left+t.piece.length,n+=t.lf_left+t.piece.lineFeedCnt,t.right===ne){o=this.getOffsetAt(n+1,1),s=i-e-o;return new h["a"](n+1,s+1)}t=t.right}return new h["a"](1,1)},e.prototype.getValueInRange=function(e,t){if(e.startLineNumber===e.endLineNumber&&e.startColumn===e.endColumn)return"";var n=this.nodeAt2(e.startLineNumber,e.startColumn),i=this.nodeAt2(e.endLineNumber,e.endColumn),r=this.getValueInRange2(n,i);return t?t===this._EOL&&this._EOLNormalized&&t===this.getEOL()&&this._EOLNormalized?r:r.replace(/\r\n|\r|\n/g,t):r},e.prototype.getValueInRange2=function(e,t){if(e.node===t.node){var n=e.node,i=this._buffers[n.piece.bufferIndex].buffer,r=this.offsetInBuffer(n.piece.bufferIndex,n.piece.start);return i.substring(r+e.remainder,r+t.remainder)}var o=e.node,s=this._buffers[o.piece.bufferIndex].buffer,a=this.offsetInBuffer(o.piece.bufferIndex,o.piece.start),u=s.substring(a+e.remainder,a+o.piece.length);o=o.next();while(o!==ne){var h=this._buffers[o.piece.bufferIndex].buffer,f=this.offsetInBuffer(o.piece.bufferIndex,o.piece.start);if(o===t.node){u+=h.substring(f,f+t.remainder);break}u+=h.substr(f,o.piece.length),o=o.next()}return u},e.prototype.getLinesContent=function(){var e=this,t=[],n=0,i="",r=!1;return this.iterate(this.root,(function(o){if(o===ne)return!0;var s=o.piece,a=s.length;if(0===a)return!0;var u=e._buffers[s.bufferIndex].buffer,h=e._buffers[s.bufferIndex].lineStarts,f=s.start.line,l=s.end.line,c=h[f]+s.start.column;if(r&&(10===u.charCodeAt(c)&&(c++,a--),t[n++]=i,i="",r=!1,0===a))return!0;if(f===l)return e._EOLNormalized||13!==u.charCodeAt(c+a-1)?i+=u.substr(c,a):(r=!0,i+=u.substr(c,a-1)),!0;i+=e._EOLNormalized?u.substring(c,Math.max(c,h[f+1]-e._EOLLength)):u.substring(c,h[f+1]).replace(/(\r\n|\r|\n)$/,""),t[n++]=i;for(var d=f+1;d<l;d++)i=e._EOLNormalized?u.substring(h[d],h[d+1]-e._EOLLength):u.substring(h[d],h[d+1]).replace(/(\r\n|\r|\n)$/,""),t[n++]=i;return e._EOLNormalized||13!==u.charCodeAt(h[l]+s.end.column-1)?i=u.substr(h[l],s.end.column):(r=!0,0===s.end.column?n--:i=u.substr(h[l],s.end.column-1)),!0})),r&&(t[n++]=i,i=""),t[n++]=i,t},e.prototype.getLength=function(){return this._length},e.prototype.getLineCount=function(){return this._lineCnt},e.prototype.getLineContent=function(e){return this._lastVisitedLine.lineNumber===e||(this._lastVisitedLine.lineNumber=e,e===this._lineCnt?this._lastVisitedLine.value=this.getLineRawContent(e):this._EOLNormalized?this._lastVisitedLine.value=this.getLineRawContent(e,this._EOLLength):this._lastVisitedLine.value=this.getLineRawContent(e).replace(/(\r\n|\r|\n)$/,"")),this._lastVisitedLine.value},e.prototype.getLineCharCode=function(e,t){var n=this.nodeAt2(e,t+1);if(n.remainder===n.node.piece.length){var i=n.node.next();if(!i)return 0;var r=this._buffers[i.piece.bufferIndex],o=this.offsetInBuffer(i.piece.bufferIndex,i.piece.start);return r.buffer.charCodeAt(o)}r=this._buffers[n.node.piece.bufferIndex],o=this.offsetInBuffer(n.node.piece.bufferIndex,n.node.piece.start);var s=o+n.remainder;return r.buffer.charCodeAt(s)},e.prototype.getLineLength=function(e){if(e===this.getLineCount()){var t=this.getOffsetAt(e,1);return this.getLength()-t}return this.getOffsetAt(e+1,1)-this.getOffsetAt(e,1)-this._EOLLength},e.prototype.findMatchesInNode=function(e,t,n,i,r,o,s,a,u,h,l){var c,d,p,g=this._buffers[e.piece.bufferIndex],_=this.offsetInBuffer(e.piece.bufferIndex,e.piece.start),v=this.offsetInBuffer(e.piece.bufferIndex,r),m=this.offsetInBuffer(e.piece.bufferIndex,o),L={line:0,column:0};t._wordSeparators?(d=g.buffer.substring(v,m),p=function(e){return e+v},t.reset(-1)):(d=g.buffer,p=function(e){return e},t.reset(v));do{if(c=t.next(d),c){if(p(c.index)>=m)return h;this.positionInBuffer(e,p(c.index)-_,L);var b=this.getLineFeedCnt(e.piece.bufferIndex,r,L),C=L.line===r.line?L.column-r.column+i:L.column+1,I=C+c[0].length;if(l[h++]=Object(pe["d"])(new f["a"](n+b,C,n+b,I),c,a),p(c.index)+c[0].length>=m)return h;if(h>=u)return h}}while(c);return h},e.prototype.findMatchesLineByLine=function(e,t,n,i){var r=[],o=0,s=new pe["b"](t.wordSeparators,t.regex),a=this.nodeAt2(e.startLineNumber,e.startColumn);if(null===a)return[];var u=this.nodeAt2(e.endLineNumber,e.endColumn);if(null===u)return[];var h=this.positionInBuffer(a.node,a.remainder),f=this.positionInBuffer(u.node,u.remainder);if(a.node===u.node)return this.findMatchesInNode(a.node,s,e.startLineNumber,e.startColumn,h,f,t,n,i,o,r),r;var l=e.startLineNumber,c=a.node;while(c!==u.node){var d=this.getLineFeedCnt(c.piece.bufferIndex,h,c.piece.end);if(d>=1){var p=this._buffers[c.piece.bufferIndex].lineStarts,g=this.offsetInBuffer(c.piece.bufferIndex,c.piece.start),_=p[h.line+d],v=l===e.startLineNumber?e.startColumn:1;if(o=this.findMatchesInNode(c,s,l,v,h,this.positionInBuffer(c,_-g),t,n,i,o,r),o>=i)return r;l+=d}var m=l===e.startLineNumber?e.startColumn-1:0;if(l===e.endLineNumber){var L=this.getLineContent(l).substring(m,e.endColumn-1);return o=this._findMatchesInLine(t,s,L,e.endLineNumber,m,o,r,n,i),r}if(o=this._findMatchesInLine(t,s,this.getLineContent(l).substr(m),l,m,o,r,n,i),o>=i)return r;l++,a=this.nodeAt2(l,1),c=a.node,h=this.positionInBuffer(a.node,a.remainder)}if(l===e.endLineNumber){var b=l===e.startLineNumber?e.startColumn-1:0;L=this.getLineContent(l).substring(b,e.endColumn-1);return o=this._findMatchesInLine(t,s,L,e.endLineNumber,b,o,r,n,i),r}var C=l===e.startLineNumber?e.startColumn:1;return o=this.findMatchesInNode(u.node,s,l,C,h,f,t,n,i,o,r),r},e.prototype._findMatchesInLine=function(e,t,n,i,r,o,s,a,u){var h,l=e.wordSeparators;if(!a&&e.simpleSearch){var d=e.simpleSearch,p=d.length,g=n.length,_=-p;while(-1!==(_=n.indexOf(d,_+p)))if((!l||Object(pe["e"])(l,n,g,_,p))&&(s[o++]=new c["b"](new f["a"](i,_+1+r,i,_+1+p+r),null),o>=u))return o;return o}t.reset(0);do{if(h=t.next(n),h&&(s[o++]=Object(pe["d"])(new f["a"](i,h.index+1+r,i,h.index+1+h[0].length+r),h,a),o>=u))return o}while(h);return o},e.prototype.insert=function(e,t,n){if(void 0===n&&(n=!1),this._EOLNormalized=this._EOLNormalized&&n,this._lastVisitedLine.lineNumber=0,this._lastVisitedLine.value="",this.root!==ne){var i=this.nodeAt(e),r=i.node,o=i.remainder,s=i.nodeStartOffset,a=r.piece,u=a.bufferIndex,h=this.positionInBuffer(r,o);if(0===r.piece.bufferIndex&&a.end.line===this._lastChangeBufferPos.line&&a.end.column===this._lastChangeBufferPos.column&&s+a.length===e&&t.length<ge)return this.appendToNode(r,t),void this.computeBufferMetadata();if(s===e)this.insertContentToNodeLeft(t,r),this._searchCache.valdiate(e);else if(s+r.piece.length>e){var f=[],l=new be(a.bufferIndex,h,a.end,this.getLineFeedCnt(a.bufferIndex,h,a.end),this.offsetInBuffer(u,a.end)-this.offsetInBuffer(u,h));if(this.shouldCheckCRLF()&&this.endWithCR(t)){var c=this.nodeCharCodeAt(r,o);if(10===c){var d={line:l.start.line+1,column:0};l=new be(l.bufferIndex,d,l.end,this.getLineFeedCnt(l.bufferIndex,d,l.end),l.length-1),t+="\n"}}if(this.shouldCheckCRLF()&&this.startWithLF(t)){var p=this.nodeCharCodeAt(r,o-1);if(13===p){var g=this.positionInBuffer(r,o-1);this.deleteNodeTail(r,g),t="\r"+t,0===r.piece.length&&f.push(r)}else this.deleteNodeTail(r,h)}else this.deleteNodeTail(r,h);var _=this.createNewPieces(t);l.length>0&&this.rbInsertRight(r,l);for(var v=r,m=0;m<_.length;m++)v=this.rbInsertRight(v,_[m]);this.deleteNodes(f)}else this.insertContentToNodeRight(t,r)}else{var L=this.createNewPieces(t);for(r=this.rbInsertLeft(null,L[0]),m=1;m<L.length;m++)r=this.rbInsertRight(r,L[m])}this.computeBufferMetadata()},e.prototype.delete=function(e,t){if(this._lastVisitedLine.lineNumber=0,this._lastVisitedLine.value="",!(t<=0||this.root===ne)){var n=this.nodeAt(e),i=this.nodeAt(e+t),r=n.node,o=i.node;if(r===o){var s=this.positionInBuffer(r,n.remainder),a=this.positionInBuffer(r,i.remainder);if(n.nodeStartOffset===e){if(t===r.piece.length){var u=r.next();return fe(this,r),this.validateCRLFWithPrevNode(u),void this.computeBufferMetadata()}return this.deleteNodeHead(r,a),this._searchCache.valdiate(e),this.validateCRLFWithPrevNode(r),void this.computeBufferMetadata()}return n.nodeStartOffset+r.piece.length===e+t?(this.deleteNodeTail(r,s),this.validateCRLFWithNextNode(r),void this.computeBufferMetadata()):(this.shrinkNode(r,s,a),void this.computeBufferMetadata())}var h=[],f=this.positionInBuffer(r,n.remainder);this.deleteNodeTail(r,f),this._searchCache.valdiate(e),0===r.piece.length&&h.push(r);var l=this.positionInBuffer(o,i.remainder);this.deleteNodeHead(o,l),0===o.piece.length&&h.push(o);for(var c=r.next(),d=c;d!==ne&&d!==o;d=d.next())h.push(d);var p=0===r.piece.length?r.prev():r;this.deleteNodes(h),this.validateCRLFWithNextNode(p),this.computeBufferMetadata()}},e.prototype.insertContentToNodeLeft=function(e,t){var n=[];if(this.shouldCheckCRLF()&&this.endWithCR(e)&&this.startWithLF(t)){var i=t.piece,r={line:i.start.line+1,column:0},o=new be(i.bufferIndex,r,i.end,this.getLineFeedCnt(i.bufferIndex,r,i.end),i.length-1);t.piece=o,e+="\n",ce(this,t,-1,-1),0===t.piece.length&&n.push(t)}for(var s=this.createNewPieces(e),a=this.rbInsertLeft(t,s[s.length-1]),u=s.length-2;u>=0;u--)a=this.rbInsertLeft(a,s[u]);this.validateCRLFWithPrevNode(a),this.deleteNodes(n)},e.prototype.insertContentToNodeRight=function(e,t){this.adjustCarriageReturnFromNext(e,t)&&(e+="\n");for(var n=this.createNewPieces(e),i=this.rbInsertRight(t,n[0]),r=i,o=1;o<n.length;o++)r=this.rbInsertRight(r,n[o]);this.validateCRLFWithPrevNode(i)},e.prototype.positionInBuffer=function(e,t,n){var i=e.piece,r=e.piece.bufferIndex,o=this._buffers[r].lineStarts,s=o[i.start.line]+i.start.column,a=s+t,u=i.start.line,h=i.end.line,f=0,l=0,c=0;while(u<=h){if(f=u+(h-u)/2|0,c=o[f],f===h)break;if(l=o[f+1],a<c)h=f-1;else{if(!(a>=l))break;u=f+1}}return n?(n.line=f,n.column=a-c,null):{line:f,column:a-c}},e.prototype.getLineFeedCnt=function(e,t,n){if(0===n.column)return n.line-t.line;var i=this._buffers[e].lineStarts;if(n.line===i.length-1)return n.line-t.line;var r=i[n.line+1],o=i[n.line]+n.column;if(r>o+1)return n.line-t.line;var s=o-1,a=this._buffers[e].buffer;return 13===a.charCodeAt(s)?n.line-t.line+1:n.line-t.line},e.prototype.offsetInBuffer=function(e,t){var n=this._buffers[e].lineStarts;return n[t.line]+t.column},e.prototype.deleteNodes=function(e){for(var t=0;t<e.length;t++)fe(this,e[t])},e.prototype.createNewPieces=function(e){if(e.length>ge){var t=[];while(e.length>ge){var n=e.charCodeAt(ge-1),i=void 0;13===n||n>=55296&&n<=56319?(i=e.substring(0,ge-1),e=e.substring(ge-1)):(i=e.substring(0,ge),e=e.substring(ge));var r=me(i);t.push(new be(this._buffers.length,{line:0,column:0},{line:r.length-1,column:i.length-r[r.length-1]},r.length-1,i.length)),this._buffers.push(new Ce(i,r))}var o=me(e);return t.push(new be(this._buffers.length,{line:0,column:0},{line:o.length-1,column:e.length-o[o.length-1]},o.length-1,e.length)),this._buffers.push(new Ce(e,o)),t}var s=this._buffers[0].buffer.length,a=me(e,!1),u=this._lastChangeBufferPos;if(this._buffers[0].lineStarts[this._buffers[0].lineStarts.length-1]===s&&0!==s&&this.startWithLF(e)&&this.endWithCR(this._buffers[0].buffer)){this._lastChangeBufferPos={line:this._lastChangeBufferPos.line,column:this._lastChangeBufferPos.column+1},u=this._lastChangeBufferPos;for(var h=0;h<a.length;h++)a[h]+=s+1;this._buffers[0].lineStarts=this._buffers[0].lineStarts.concat(a.slice(1)),this._buffers[0].buffer+="_"+e,s+=1}else{if(0!==s)for(h=0;h<a.length;h++)a[h]+=s;this._buffers[0].lineStarts=this._buffers[0].lineStarts.concat(a.slice(1)),this._buffers[0].buffer+=e}var f=this._buffers[0].buffer.length,l=this._buffers[0].lineStarts.length-1,c=f-this._buffers[0].lineStarts[l],d={line:l,column:c},p=new be(0,u,d,this.getLineFeedCnt(0,u,d),f-s);return this._lastChangeBufferPos=d,[p]},e.prototype.getLineRawContent=function(e,t){void 0===t&&(t=0);var n=this.root,i="",r=this._searchCache.get2(e);if(r){n=r.node;var o=this.getAccumulatedValue(n,e-r.nodeStartLineNumber-1),s=this._buffers[n.piece.bufferIndex].buffer,a=this.offsetInBuffer(n.piece.bufferIndex,n.piece.start);if(r.nodeStartLineNumber+n.piece.lineFeedCnt!==e){var u=this.getAccumulatedValue(n,e-r.nodeStartLineNumber);return s.substring(a+o,a+u-t)}i=s.substring(a+o,a+n.piece.length)}else{var h=0,f=e;while(n!==ne)if(n.left!==ne&&n.lf_left>=e-1)n=n.left;else{if(n.lf_left+n.piece.lineFeedCnt>e-1){o=this.getAccumulatedValue(n,e-n.lf_left-2),u=this.getAccumulatedValue(n,e-n.lf_left-1),s=this._buffers[n.piece.bufferIndex].buffer,a=this.offsetInBuffer(n.piece.bufferIndex,n.piece.start);return h+=n.size_left,this._searchCache.set({node:n,nodeStartOffset:h,nodeStartLineNumber:f-(e-1-n.lf_left)}),s.substring(a+o,a+u-t)}if(n.lf_left+n.piece.lineFeedCnt===e-1){o=this.getAccumulatedValue(n,e-n.lf_left-2),s=this._buffers[n.piece.bufferIndex].buffer,a=this.offsetInBuffer(n.piece.bufferIndex,n.piece.start);i=s.substring(a+o,a+n.piece.length);break}e-=n.lf_left+n.piece.lineFeedCnt,h+=n.size_left+n.piece.length,n=n.right}}n=n.next();while(n!==ne){s=this._buffers[n.piece.bufferIndex].buffer;if(n.piece.lineFeedCnt>0){u=this.getAccumulatedValue(n,0),a=this.offsetInBuffer(n.piece.bufferIndex,n.piece.start);return i+=s.substring(a,a+u-t),i}a=this.offsetInBuffer(n.piece.bufferIndex,n.piece.start);i+=s.substr(a,n.piece.length),n=n.next()}return i},e.prototype.computeBufferMetadata=function(){var e=this.root,t=1,n=0;while(e!==ne)t+=e.lf_left+e.piece.lineFeedCnt,n+=e.size_left+e.piece.length,e=e.right;this._lineCnt=t,this._length=n,this._searchCache.valdiate(this._length)},e.prototype.getIndexOf=function(e,t){var n=e.piece,i=this.positionInBuffer(e,t),r=i.line-n.start.line;if(this.offsetInBuffer(n.bufferIndex,n.end)-this.offsetInBuffer(n.bufferIndex,n.start)===t){var o=this.getLineFeedCnt(e.piece.bufferIndex,n.start,i);if(o!==r)return{index:o,remainder:0}}return{index:r,remainder:i.column}},e.prototype.getAccumulatedValue=function(e,t){if(t<0)return 0;var n=e.piece,i=this._buffers[n.bufferIndex].lineStarts,r=n.start.line+t+1;return r>n.end.line?i[n.end.line]+n.end.column-i[n.start.line]-n.start.column:i[r]-i[n.start.line]-n.start.column},e.prototype.deleteNodeTail=function(e,t){var n=e.piece,i=n.lineFeedCnt,r=this.offsetInBuffer(n.bufferIndex,n.end),o=t,s=this.offsetInBuffer(n.bufferIndex,o),a=this.getLineFeedCnt(n.bufferIndex,n.start,o),u=a-i,h=s-r,f=n.length+h;e.piece=new be(n.bufferIndex,n.start,o,a,f),ce(this,e,h,u)},e.prototype.deleteNodeHead=function(e,t){var n=e.piece,i=n.lineFeedCnt,r=this.offsetInBuffer(n.bufferIndex,n.start),o=t,s=this.getLineFeedCnt(n.bufferIndex,o,n.end),a=this.offsetInBuffer(n.bufferIndex,o),u=s-i,h=r-a,f=n.length+h;e.piece=new be(n.bufferIndex,o,n.end,s,f),ce(this,e,h,u)},e.prototype.shrinkNode=function(e,t,n){var i=e.piece,r=i.start,o=i.end,s=i.length,a=i.lineFeedCnt,u=t,h=this.getLineFeedCnt(i.bufferIndex,i.start,u),f=this.offsetInBuffer(i.bufferIndex,t)-this.offsetInBuffer(i.bufferIndex,r);e.piece=new be(i.bufferIndex,i.start,u,h,f),ce(this,e,f-s,h-a);var l=new be(i.bufferIndex,n,o,this.getLineFeedCnt(i.bufferIndex,n,o),this.offsetInBuffer(i.bufferIndex,o)-this.offsetInBuffer(i.bufferIndex,n)),c=this.rbInsertRight(e,l);this.validateCRLFWithPrevNode(c)},e.prototype.appendToNode=function(e,t){this.adjustCarriageReturnFromNext(t,e)&&(t+="\n");var n=this.shouldCheckCRLF()&&this.startWithLF(t)&&this.endWithCR(e),i=this._buffers[0].buffer.length;this._buffers[0].buffer+=t;for(var r=me(t,!1),o=0;o<r.length;o++)r[o]+=i;if(n){var s=this._buffers[0].lineStarts[this._buffers[0].lineStarts.length-2];this._buffers[0].lineStarts.pop(),this._lastChangeBufferPos={line:this._lastChangeBufferPos.line-1,column:i-s}}this._buffers[0].lineStarts=this._buffers[0].lineStarts.concat(r.slice(1));var a=this._buffers[0].lineStarts.length-1,u=this._buffers[0].buffer.length-this._buffers[0].lineStarts[a],h={line:a,column:u},f=e.piece.length+t.length,l=e.piece.lineFeedCnt,c=this.getLineFeedCnt(0,e.piece.start,h),d=c-l;e.piece=new be(e.piece.bufferIndex,e.piece.start,h,c,f),this._lastChangeBufferPos=h,ce(this,e,t.length,d)},e.prototype.nodeAt=function(e){var t=this.root,n=this._searchCache.get(e);if(n)return{node:n.node,nodeStartOffset:n.nodeStartOffset,remainder:e-n.nodeStartOffset};var i=0;while(t!==ne)if(t.size_left>e)t=t.left;else{if(t.size_left+t.piece.length>=e){i+=t.size_left;var r={node:t,remainder:e-t.size_left,nodeStartOffset:i};return this._searchCache.set(r),r}e-=t.size_left+t.piece.length,i+=t.size_left+t.piece.length,t=t.right}return null},e.prototype.nodeAt2=function(e,t){var n=this.root,i=0;while(n!==ne)if(n.left!==ne&&n.lf_left>=e-1)n=n.left;else{if(n.lf_left+n.piece.lineFeedCnt>e-1){var r=this.getAccumulatedValue(n,e-n.lf_left-2),o=this.getAccumulatedValue(n,e-n.lf_left-1);return i+=n.size_left,{node:n,remainder:Math.min(r+t-1,o),nodeStartOffset:i}}if(n.lf_left+n.piece.lineFeedCnt===e-1){r=this.getAccumulatedValue(n,e-n.lf_left-2);if(r+t-1<=n.piece.length)return{node:n,remainder:r+t-1,nodeStartOffset:i};t-=n.piece.length-r;break}e-=n.lf_left+n.piece.lineFeedCnt,i+=n.size_left+n.piece.length,n=n.right}n=n.next();while(n!==ne){if(n.piece.lineFeedCnt>0){o=this.getAccumulatedValue(n,0);var s=this.offsetOfNode(n);return{node:n,remainder:Math.min(t-1,o),nodeStartOffset:s}}if(n.piece.length>=t-1){var a=this.offsetOfNode(n);return{node:n,remainder:t-1,nodeStartOffset:a}}t-=n.piece.length,n=n.next()}return null},e.prototype.nodeCharCodeAt=function(e,t){if(e.piece.lineFeedCnt<1)return-1;var n=this._buffers[e.piece.bufferIndex],i=this.offsetInBuffer(e.piece.bufferIndex,e.piece.start)+t;return n.buffer.charCodeAt(i)},e.prototype.offsetOfNode=function(e){if(!e)return 0;var t=e.size_left;while(e!==this.root)e.parent.right===e&&(t+=e.parent.size_left+e.parent.piece.length),e=e.parent;return t},e.prototype.shouldCheckCRLF=function(){return!(this._EOLNormalized&&"\n"===this._EOL)},e.prototype.startWithLF=function(e){if("string"===typeof e)return 10===e.charCodeAt(0);if(e===ne||0===e.piece.lineFeedCnt)return!1;var t=e.piece,n=this._buffers[t.bufferIndex].lineStarts,i=t.start.line,r=n[i]+t.start.column;if(i===n.length-1)return!1;var o=n[i+1];return!(o>r+1)&&10===this._buffers[t.bufferIndex].buffer.charCodeAt(r)},e.prototype.endWithCR=function(e){return"string"===typeof e?13===e.charCodeAt(e.length-1):e!==ne&&0!==e.piece.lineFeedCnt&&13===this.nodeCharCodeAt(e,e.piece.length-1)},e.prototype.validateCRLFWithPrevNode=function(e){if(this.shouldCheckCRLF()&&this.startWithLF(e)){var t=e.prev();this.endWithCR(t)&&this.fixCRLF(t,e)}},e.prototype.validateCRLFWithNextNode=function(e){if(this.shouldCheckCRLF()&&this.endWithCR(e)){var t=e.next();this.startWithLF(t)&&this.fixCRLF(e,t)}},e.prototype.fixCRLF=function(e,t){var n,i=[],r=this._buffers[e.piece.bufferIndex].lineStarts;n=0===e.piece.end.column?{line:e.piece.end.line-1,column:r[e.piece.end.line]-r[e.piece.end.line-1]-1}:{line:e.piece.end.line,column:e.piece.end.column-1};var o=e.piece.length-1,s=e.piece.lineFeedCnt-1;e.piece=new be(e.piece.bufferIndex,e.piece.start,n,s,o),ce(this,e,-1,-1),0===e.piece.length&&i.push(e);var a={line:t.piece.start.line+1,column:0},u=t.piece.length-1,h=this.getLineFeedCnt(t.piece.bufferIndex,a,t.piece.end);t.piece=new be(t.piece.bufferIndex,a,t.piece.end,h,u),ce(this,t,-1,-1),0===t.piece.length&&i.push(t);var f=this.createNewPieces("\r\n");this.rbInsertRight(e,f[0]);for(var l=0;l<i.length;l++)fe(this,i[l])},e.prototype.adjustCarriageReturnFromNext=function(e,t){if(this.shouldCheckCRLF()&&this.endWithCR(e)){var n=t.next();if(this.startWithLF(n)){if(e+="\n",1===n.piece.length)fe(this,n);else{var i=n.piece,r={line:i.start.line+1,column:0},o=i.length-1,s=this.getLineFeedCnt(i.bufferIndex,r,i.end);n.piece=new be(i.bufferIndex,r,i.end,s,o),ce(this,n,-1,-1)}return!0}}return!1},e.prototype.iterate=function(e,t){if(e===ne)return t(ne);var n=this.iterate(e.left,t);return n?t(e)&&this.iterate(e.right,t):n},e.prototype.getNodeContent=function(e){if(e===ne)return"";var t,n=this._buffers[e.piece.bufferIndex],i=e.piece,r=this.offsetInBuffer(i.bufferIndex,i.start),o=this.offsetInBuffer(i.bufferIndex,i.end);return t=n.buffer.substring(r,o),t},e.prototype.rbInsertRight=function(e,t){var n=new te(t,1);n.left=ne,n.right=ne,n.parent=ne,n.size_left=0,n.lf_left=0;var i=this.root;if(i===ne)this.root=n,n.color=0;else if(e.right===ne)e.right=n,n.parent=e;else{var r=ie(e.right);r.left=n,n.parent=r}return le(this,n),n},e.prototype.rbInsertLeft=function(e,t){var n=new te(t,1);if(n.left=ne,n.right=ne,n.parent=ne,n.size_left=0,n.lf_left=0,this.root===ne)this.root=n,n.color=0;else if(e.left===ne)e.left=n,n.parent=e;else{var i=re(e.left);i.right=n,n.parent=i}return le(this,n),n},e}(),Ne=function(){function e(e,t,n,i,r,o){this._BOM=t,this._mightContainNonBasicASCII=!r,this._mightContainRTL=i,this._pieceTree=new ye(e,n,o)}return e.prototype.mightContainRTL=function(){return this._mightContainRTL},e.prototype.mightContainNonBasicASCII=function(){return this._mightContainNonBasicASCII},e.prototype.getBOM=function(){return this._BOM},e.prototype.getEOL=function(){return this._pieceTree.getEOL()},e.prototype.getOffsetAt=function(e,t){return this._pieceTree.getOffsetAt(e,t)},e.prototype.getPositionAt=function(e){return this._pieceTree.getPositionAt(e)},e.prototype.getRangeAt=function(e,t){var n=e+t,i=this.getPositionAt(e),r=this.getPositionAt(n);return new f["a"](i.lineNumber,i.column,r.lineNumber,r.column)},e.prototype.getValueInRange=function(e,t){if(void 0===t&&(t=0),e.isEmpty())return"";var n=this._getEndOfLine(t);return this._pieceTree.getValueInRange(e,n)},e.prototype.getValueLengthInRange=function(e,t){if(void 0===t&&(t=0),e.isEmpty())return 0;if(e.startLineNumber===e.endLineNumber)return e.endColumn-e.startColumn;var n=this.getOffsetAt(e.startLineNumber,e.startColumn),i=this.getOffsetAt(e.endLineNumber,e.endColumn);return i-n},e.prototype.getCharacterCountInRange=function(e,t){if(void 0===t&&(t=0),this._mightContainNonBasicASCII){for(var n=0,i=e.startLineNumber,r=e.endLineNumber,o=i;o<=r;o++)for(var a=this.getLineContent(o),u=o===i?e.startColumn-1:0,h=o===r?e.endColumn-1:a.length,f=u;f<h;f++)s["z"](a.charCodeAt(f))?(n+=1,f+=1):n+=1;return n+=this._getEndOfLine(t).length*(r-i),n}return this.getValueLengthInRange(e,t)},e.prototype.getLength=function(){return this._pieceTree.getLength()},e.prototype.getLineCount=function(){return this._pieceTree.getLineCount()},e.prototype.getLinesContent=function(){return this._pieceTree.getLinesContent()},e.prototype.getLineContent=function(e){return this._pieceTree.getLineContent(e)},e.prototype.getLineCharCode=function(e,t){return this._pieceTree.getLineCharCode(e,t)},e.prototype.getLineLength=function(e){return this._pieceTree.getLineLength(e)},e.prototype.getLineFirstNonWhitespaceColumn=function(e){var t=s["q"](this.getLineContent(e));return-1===t?0:t+1},e.prototype.getLineLastNonWhitespaceColumn=function(e){var t=s["D"](this.getLineContent(e));return-1===t?0:t+2},e.prototype._getEndOfLine=function(e){switch(e){case 1:return"\n";case 2:return"\r\n";case 0:return this.getEOL()}throw new Error("Unknown EOL preference")},e.prototype.setEOL=function(e){this._pieceTree.setEOL(e)},e.prototype.applyEdits=function(t,n){for(var i=this._mightContainRTL,r=this._mightContainNonBasicASCII,o=!0,a=[],u=0;u<t.length;u++){var h=t[u];o&&h._isTracked&&(o=!1);var f=h.range;!i&&h.text&&(i=s["i"](h.text)),!r&&h.text&&(r=!s["v"](h.text)),a[u]={sortIndex:u,identifier:h.identifier||null,range:f,rangeOffset:this.getOffsetAt(f.startLineNumber,f.startColumn),rangeLength:this.getValueLengthInRange(f),lines:h.text?h.text.split(/\r\n|\r|\n/):null,forceMoveMarkers:Boolean(h.forceMoveMarkers),isAutoWhitespaceEdit:h.isAutoWhitespaceEdit||!1}}a.sort(e._sortOpsAscending);for(var l=!1,d=(u=0,a.length-1);u<d;u++){var p=a[u].range.getEndPosition(),g=a[u+1].range.getStartPosition();if(g.isBeforeOrEqual(p)){if(g.isBefore(p))throw new Error("Overlapping ranges are not allowed!");l=!0}}o&&(a=this._reduceOperations(a));var _=e._getInverseEditRanges(a),v=[];for(u=0;u<a.length;u++){h=a[u];var m=_[u];if(n&&h.isAutoWhitespaceEdit&&h.range.isEmpty())for(var L=m.startLineNumber;L<=m.endLineNumber;L++){var b="";L===m.startLineNumber&&(b=this.getLineContent(h.range.startLineNumber),-1!==s["q"](b))||v.push({lineNumber:L,oldContent:b})}}var C=[];for(u=0;u<a.length;u++){h=a[u],m=_[u];C[u]={sortIndex:h.sortIndex,identifier:h.identifier,range:m,text:this.getValueInRange(h.range),forceMoveMarkers:h.forceMoveMarkers}}l||C.sort((function(e,t){return e.sortIndex-t.sortIndex})),this._mightContainRTL=i,this._mightContainNonBasicASCII=r;var I=this._doApplyEdits(a),y=null;if(n&&v.length>0){v.sort((function(e,t){return t.lineNumber-e.lineNumber})),y=[];u=0;for(var N=v.length;u<N;u++){L=v[u].lineNumber;if(!(u>0&&v[u-1].lineNumber===L)){var k=v[u].oldContent,w=this.getLineContent(L);0!==w.length&&w!==k&&-1===s["q"](w)&&y.push(L)}}}return new c["a"](C,I,y)},e.prototype._reduceOperations=function(e){return e.length<1e3?e:[this._toSingleEditOperation(e)]},e.prototype._toSingleEditOperation=function(e){for(var t=!1,n=e[0].range,i=e[e.length-1].range,r=new f["a"](n.startLineNumber,n.startColumn,i.endLineNumber,i.endColumn),o=n.startLineNumber,s=n.startColumn,a=[],u=0,h=e.length;u<h;u++){var l=e[u],c=l.range;t=t||l.forceMoveMarkers;for(var d=o;d<c.startLineNumber;d++)d===o?a.push(this.getLineContent(d).substring(s-1)):(a.push("\n"),a.push(this.getLineContent(d)));if(c.startLineNumber===o?a.push(this.getLineContent(c.startLineNumber).substring(s-1,c.startColumn-1)):(a.push("\n"),a.push(this.getLineContent(c.startLineNumber).substring(0,c.startColumn-1))),l.lines)for(var p=0,g=l.lines.length;p<g;p++)0!==p&&a.push("\n"),a.push(l.lines[p]);o=l.range.endLineNumber,s=l.range.endColumn}return{sortIndex:0,identifier:e[0].identifier,range:r,rangeOffset:this.getOffsetAt(r.startLineNumber,r.startColumn),rangeLength:this.getValueLengthInRange(r,0),lines:a.join("").split("\n"),forceMoveMarkers:t,isAutoWhitespaceEdit:!1}},e.prototype._doApplyEdits=function(t){t.sort(e._sortOpsDescending);for(var n=[],i=0;i<t.length;i++){var r=t[i],o=r.range.startLineNumber,s=r.range.startColumn,a=r.range.endLineNumber,u=r.range.endColumn;if(o!==a||s!==u||r.lines&&0!==r.lines.length){var h=a-o,l=r.lines?r.lines.length-1:0,c=Math.min(h,l),d=r.lines?r.lines.join(this.getEOL()):"";if(d?(this._pieceTree.delete(r.rangeOffset,r.rangeLength),this._pieceTree.insert(r.rangeOffset,d,!0)):this._pieceTree.delete(r.rangeOffset,r.rangeLength),c<l){for(var p=[],g=c+1;g<=l;g++)p.push(r.lines[g]);p[p.length-1]=this.getLineContent(o+l-1)}var _=new f["a"](o,s,a,u);n.push({range:_,rangeLength:r.rangeLength,text:d,rangeOffset:r.rangeOffset,forceMoveMarkers:r.forceMoveMarkers})}}return n},e.prototype.findMatchesLineByLine=function(e,t,n,i){return this._pieceTree.findMatchesLineByLine(e,t,n,i)},e._getInverseEditRanges=function(e){for(var t=[],n=0,i=0,r=null,o=0,s=e.length;o<s;o++){var a=e[o],u=void 0,h=void 0;r?r.range.endLineNumber===a.range.startLineNumber?(u=n,h=i+(a.range.startColumn-r.range.endColumn)):(u=n+(a.range.startLineNumber-r.range.endLineNumber),h=a.range.startColumn):(u=a.range.startLineNumber,h=a.range.startColumn);var l=void 0;if(a.lines&&a.lines.length>0){var c=a.lines.length,d=a.lines[0],p=a.lines[c-1];l=1===c?new f["a"](u,h,u,h+d.length):new f["a"](u,h,u+c-1,p.length+1)}else l=new f["a"](u,h,u,h);n=l.endLineNumber,i=l.endColumn,t.push(l),r=a}return t},e._sortOpsAscending=function(e,t){var n=f["a"].compareRangesUsingEnds(e.range,t.range);return 0===n?e.sortIndex-t.sortIndex:n},e._sortOpsDescending=function(e,t){var n=f["a"].compareRangesUsingEnds(e.range,t.range);return 0===n?t.sortIndex-e.sortIndex:-n},e}(),ke=function(){function e(e,t,n,i,r,o,s,a){this._chunks=e,this._bom=t,this._cr=n,this._lf=i,this._crlf=r,this._containsRTL=o,this._isBasicASCII=s,this._normalizeEOL=a}return e.prototype._getEOL=function(e){var t=this._cr+this._lf+this._crlf,n=this._cr+this._crlf;return 0===t?1===e?"\n":"\r\n":n>t/2?"\r\n":"\n"},e.prototype.create=function(e){var t=this._getEOL(e),n=this._chunks;if(this._normalizeEOL&&("\r\n"===t&&(this._cr>0||this._lf>0)||"\n"===t&&(this._cr>0||this._crlf>0)))for(var i=0,r=n.length;i<r;i++){var o=n[i].buffer.replace(/\r\n|\r|\n/g,t),s=me(o);n[i]=new Ce(o,s)}return new Ne(n,this._bom,t,this._containsRTL,this._isBasicASCII,this._normalizeEOL)},e}(),we=function(){function e(){this.chunks=[],this.BOM="",this._hasPreviousChar=!1,this._previousChar=0,this._tmpLineStarts=[],this.cr=0,this.lf=0,this.crlf=0,this.containsRTL=!1,this.isBasicASCII=!0}return e.prototype.acceptChunk=function(e){if(0!==e.length){0===this.chunks.length&&s["P"](e)&&(this.BOM=s["a"],e=e.substr(1));var t=e.charCodeAt(e.length-1);13===t||t>=55296&&t<=56319?(this._acceptChunk1(e.substr(0,e.length-1),!1),this._hasPreviousChar=!0,this._previousChar=t):(this._acceptChunk1(e,!1),this._hasPreviousChar=!1,this._previousChar=t)}},e.prototype._acceptChunk1=function(e,t){(t||0!==e.length)&&(this._hasPreviousChar?this._acceptChunk2(String.fromCharCode(this._previousChar)+e):this._acceptChunk2(e))},e.prototype._acceptChunk2=function(e){var t=Le(this._tmpLineStarts,e);this.chunks.push(new Ce(e,t.lineStarts)),this.cr+=t.cr,this.lf+=t.lf,this.crlf+=t.crlf,this.isBasicASCII&&(this.isBasicASCII=t.isBasicASCII),this.isBasicASCII||this.containsRTL||(this.containsRTL=s["i"](e))},e.prototype.finish=function(e){return void 0===e&&(e=!0),this._finish(),new ke(this.chunks,this.BOM,this.cr,this.lf,this.crlf,this.containsRTL,this.isBasicASCII,e)},e.prototype._finish=function(){if(0===this.chunks.length&&this._acceptChunk1("",!0),this._hasPreviousChar){this._hasPreviousChar=!1;var e=this.chunks[this.chunks.length-1];e.buffer+=String.fromCharCode(this._previousChar);var t=me(e.buffer);e.lineStarts=t,13===this._previousChar&&this.cr++}},e}(),Se=function(){function e(){this.changeType=1}return e}(),xe=function(){function e(e,t){this.changeType=2,this.lineNumber=e,this.detail=t}return e}(),Ee=function(){function e(e,t){this.changeType=3,this.fromLineNumber=e,this.toLineNumber=t}return e}(),Oe=function(){function e(e,t,n){this.changeType=4,this.fromLineNumber=e,this.toLineNumber=t,this.detail=n}return e}(),Te=function(){function e(){this.changeType=5}return e}(),De=function(){function e(e,t,n,i){this.changes=e,this.versionId=t,this.isUndoing=n,this.isRedoing=i}return e.prototype.containsEvent=function(e){for(var t=0,n=this.changes.length;t<n;t++){var i=this.changes[t];if(i.changeType===e)return!0}return!1},e.merge=function(t,n){var i=[].concat(t.changes).concat(n.changes),r=n.versionId,o=t.isUndoing||n.isUndoing,s=t.isRedoing||n.isRedoing;return new e(i,r,o,s)},e}(),Me=function(){function e(e,t){this.rawContentChangedEvent=e,this.contentChangedEvent=t}return e.prototype.merge=function(t){var n=De.merge(this.rawContentChangedEvent,t.rawContentChangedEvent),i=e._mergeChangeEvents(this.contentChangedEvent,t.contentChangedEvent);return new e(n,i)},e._mergeChangeEvents=function(e,t){var n=[].concat(e.changes).concat(t.changes),i=t.eol,r=t.versionId,o=e.isUndoing||t.isUndoing,s=e.isRedoing||t.isRedoing,a=e.isFlush||t.isFlush;return{changes:n,eol:i,versionId:r,isUndoing:o,isRedoing:s,isFlush:a}},e}(),Ae=n("e8e3"),Re=n("e1b5"),ze=n("b707"),Be=n("8bf1"),Fe=n("e58e"),Ve=n("4111"),Pe=n("30db"),We=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}(),Ue=function(){function e(){this._beginState=[],this._valid=[],this._len=0,this._invalidLineStartIndex=0}return e.prototype._reset=function(e){this._beginState=[],this._valid=[],this._len=0,this._invalidLineStartIndex=0,e&&this._setBeginState(0,e)},e.prototype.flush=function(e){this._reset(e)},Object.defineProperty(e.prototype,"invalidLineStartIndex",{get:function(){return this._invalidLineStartIndex},enumerable:!0,configurable:!0}),e.prototype._invalidateLine=function(e){e<this._len&&(this._valid[e]=!1),e<this._invalidLineStartIndex&&(this._invalidLineStartIndex=e)},e.prototype._isValid=function(e){return e<this._len&&this._valid[e]},e.prototype.getBeginState=function(e){return e<this._len?this._beginState[e]:null},e.prototype._ensureLine=function(e){while(e>=this._len)this._beginState[this._len]=null,this._valid[this._len]=!1,this._len++},e.prototype._deleteLines=function(e,t){0!==t&&(e+t>this._len&&(t=this._len-e),this._beginState.splice(e,t),this._valid.splice(e,t),this._len-=t)},e.prototype._insertLines=function(e,t){if(0!==t){for(var n=[],i=[],r=0;r<t;r++)n[r]=null,i[r]=!1;this._beginState=Ae["a"](this._beginState,e,n),this._valid=Ae["a"](this._valid,e,i),this._len+=t}},e.prototype._setValid=function(e,t){this._ensureLine(e),this._valid[e]=t},e.prototype._setBeginState=function(e,t){this._ensureLine(e),this._beginState[e]=t},e.prototype.setEndState=function(e,t,n){if(this._setValid(t,!0),this._invalidLineStartIndex=t+1,t!==e-1){var i=this.getBeginState(t+1);if(null===i||!n.equals(i))return this._setBeginState(t+1,n),void this._invalidateLine(t+1);var r=t+1;while(r<e){if(!this._isValid(r))break;r++}this._invalidLineStartIndex=r}},e.prototype.setFakeTokens=function(e){this._setValid(e,!1)},e.prototype.applyEdits=function(e,t){for(var n=e.endLineNumber-e.startLineNumber,i=t,r=Math.min(n,i),o=r;o>=0;o--)this._invalidateLine(e.startLineNumber+o-1);this._acceptDeleteRange(e),this._acceptInsertText(new h["a"](e.startLineNumber,e.startColumn),t)},e.prototype._acceptDeleteRange=function(e){var t=e.startLineNumber-1;t>=this._len||this._deleteLines(e.startLineNumber,e.endLineNumber-e.startLineNumber)},e.prototype._acceptInsertText=function(e,t){var n=e.lineNumber-1;n>=this._len||this._insertLines(e.lineNumber,t)},e}(),je=function(e){function t(t){var n=e.call(this)||this;return n._isDisposed=!1,n._textModel=t,n._tokenizationStateStore=new Ue,n._tokenizationSupport=null,n._register(ze["B"].onDidChange((function(e){var t=n._textModel.getLanguageIdentifier();-1!==e.changedLanguages.indexOf(t.language)&&(n._resetTokenizationState(),n._textModel.clearTokens())}))),n._register(n._textModel.onDidChangeRawContentFast((function(e){e.containsEvent(1)&&n._resetTokenizationState()}))),n._register(n._textModel.onDidChangeContentFast((function(e){for(var t=0,i=e.changes.length;t<i;t++){var r=e.changes[t],o=Object(Ve["f"])(r.text)[0];n._tokenizationStateStore.applyEdits(r.range,o)}n._beginBackgroundTokenization()}))),n._register(n._textModel.onDidChangeAttached((function(){n._beginBackgroundTokenization()}))),n._register(n._textModel.onDidChangeLanguage((function(){n._resetTokenizationState(),n._textModel.clearTokens()}))),n._resetTokenizationState(),n}return We(t,e),t.prototype.dispose=function(){this._isDisposed=!0,e.prototype.dispose.call(this)},t.prototype._resetTokenizationState=function(){var e=qe(this._textModel),t=e[0],n=e[1];this._tokenizationSupport=t,this._tokenizationStateStore.flush(n),this._beginBackgroundTokenization()},t.prototype._beginBackgroundTokenization=function(){var e=this;this._textModel.isAttachedToEditor()&&this._hasLinesToTokenize()&&Pe["i"]((function(){e._isDisposed||e._revalidateTokensNow()}))},t.prototype._revalidateTokensNow=function(e){void 0===e&&(e=this._textModel.getLineCount());var t=1,n=new Ve["b"],i=Fe["a"].create(!1);while(this._hasLinesToTokenize()){if(i.elapsed()>t)break;var r=this._tokenizeOneInvalidLine(n);if(r>=e)break}this._beginBackgroundTokenization(),this._textModel.setTokens(n.tokens)},t.prototype.tokenizeViewport=function(e,t){var n=new Ve["b"];this._tokenizeViewport(n,e,t),this._textModel.setTokens(n.tokens)},t.prototype.reset=function(){this._resetTokenizationState(),this._textModel.clearTokens()},t.prototype.forceTokenization=function(e){var t=new Ve["b"];this._updateTokensUntilLine(t,e),this._textModel.setTokens(t.tokens)},t.prototype.isCheapToTokenize=function(e){if(!this._tokenizationSupport)return!0;var t=this._tokenizationStateStore.invalidLineStartIndex+1;return!(e>t)&&(e<t||this._textModel.getLineLength(e)<2048)},t.prototype._hasLinesToTokenize=function(){return!!this._tokenizationSupport&&this._tokenizationStateStore.invalidLineStartIndex<this._textModel.getLineCount()},t.prototype._tokenizeOneInvalidLine=function(e){if(!this._hasLinesToTokenize())return this._textModel.getLineCount()+1;var t=this._tokenizationStateStore.invalidLineStartIndex+1;return this._updateTokensUntilLine(e,t),t},t.prototype._updateTokensUntilLine=function(e,t){if(this._tokenizationSupport)for(var n=this._textModel.getLanguageIdentifier(),i=this._textModel.getLineCount(),r=t-1,o=this._tokenizationStateStore.invalidLineStartIndex;o<=r;o++){var s=this._textModel.getLineContent(o+1),a=this._tokenizationStateStore.getBeginState(o),u=He(n,this._tokenizationSupport,s,a);e.add(o+1,u.tokens),this._tokenizationStateStore.setEndState(i,o,u.endState),o=this._tokenizationStateStore.invalidLineStartIndex-1}},t.prototype._tokenizeViewport=function(e,t,n){if(this._tokenizationSupport&&!(n<=this._tokenizationStateStore.invalidLineStartIndex))if(t<=this._tokenizationStateStore.invalidLineStartIndex)this._updateTokensUntilLine(e,n);else{for(var i=this._textModel.getLineFirstNonWhitespaceColumn(t),r=[],o=null,s=t-1;i>0&&s>=1;s--){var a=this._textModel.getLineFirstNonWhitespaceColumn(s);if(0!==a&&a<i){if(o=this._tokenizationStateStore.getBeginState(s-1),o)break;r.push(this._textModel.getLineContent(s)),i=a}}o||(o=this._tokenizationSupport.getInitialState());var u=this._textModel.getLanguageIdentifier(),h=o;for(s=r.length-1;s>=0;s--){var f=He(u,this._tokenizationSupport,r[s],h);h=f.endState}for(var l=t;l<=n;l++){var c=this._textModel.getLineContent(l);f=He(u,this._tokenizationSupport,c,h);e.add(l,f.tokens),this._tokenizationStateStore.setFakeTokens(l-1),h=f.endState}}},t}(o["a"]);function qe(e){var t=e.getLanguageIdentifier(),n=e.isTooLargeForTokenization()?null:ze["B"].get(t.language),r=null;if(n)try{r=n.getInitialState()}catch(o){Object(i["e"])(o),n=null}return[n,r]}function He(e,t,n,r){var o=null;if(t)try{o=t.tokenize2(n,r.clone(),0)}catch(s){Object(i["e"])(s)}return o||(o=Object(Be["e"])(e.id,n,r,0)),Re["a"].convertToEndOffset(o.tokens,n.length),o}var Ge=n("d093"),$e=n("70cb"),Ye=n("045b"),Je=n("1080"),Ze=n("ef8e"),Ke=n("ceb8"),Qe=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},e(t,n)};return function(t,n){function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}}();function Xe(){return new we}function et(e){var t=Xe();return t.acceptChunk(e),t.finish()}function tt(e,t){var n="string"===typeof e?et(e):e;return n.create(t)}var nt=0,it=999,rt=1e4,ot=function(){throw new Error("Invalid change accessor")},st=function(e){function t(n,i,o,u){void 0===u&&(u=null);var h=e.call(this)||this;h._onWillDispose=h._register(new r["a"]),h.onWillDispose=h._onWillDispose.event,h._onDidChangeDecorations=h._register(new gt),h.onDidChangeDecorations=h._onDidChangeDecorations.event,h._onDidChangeLanguage=h._register(new r["a"]),h.onDidChangeLanguage=h._onDidChangeLanguage.event,h._onDidChangeLanguageConfiguration=h._register(new r["a"]),h.onDidChangeLanguageConfiguration=h._onDidChangeLanguageConfiguration.event,h._onDidChangeTokens=h._register(new r["a"]),h.onDidChangeTokens=h._onDidChangeTokens.event,h._onDidChangeOptions=h._register(new r["a"]),h.onDidChangeOptions=h._onDidChangeOptions.event,h._onDidChangeAttached=h._register(new r["a"]),h.onDidChangeAttached=h._onDidChangeAttached.event,h._eventEmitter=h._register(new _t),nt++,h.id="$model"+nt,h.isForSimpleWidget=i.isForSimpleWidget,h._associatedResource="undefined"===typeof u||null===u?a["a"].parse("inmemory://model/"+nt):u,h._attachedEditorCount=0,h._buffer=tt(n,i.defaultEOL),h._options=t.resolveOptions(h._buffer,i);var l=h._buffer.getLineCount(),c=h._buffer.getValueLengthInRange(new f["a"](1,1,l,h._buffer.getLineLength(l)+1),0);return i.largeFileOptimizations?h._isTooLargeForTokenization=c>t.LARGE_FILE_SIZE_THRESHOLD||l>t.LARGE_FILE_LINE_COUNT_THRESHOLD:h._isTooLargeForTokenization=!1,h._isTooLargeForSyncing=c>t.MODEL_SYNC_LIMIT,h._versionId=1,h._alternativeVersionId=1,h._isDisposed=!1,h._isDisposing=!1,h._languageIdentifier=o||Be["a"],h._languageRegistryListener=$e["a"].onDidChange((function(e){e.languageIdentifier.id===h._languageIdentifier.id&&h._onDidChangeLanguageConfiguration.fire({})})),h._instanceId=s["M"](nt),h._lastDecorationId=0,h._decorations=Object.create(null),h._decorationsTree=new at,h._commandManager=new _(h),h._isUndoing=!1,h._isRedoing=!1,h._trimAutoWhitespaceLines=null,h._tokens=new Ve["d"],h._tokens2=new Ve["e"],h._tokenization=new je(h),h}return Qe(t,e),t.createFromString=function(e,n,i,r){return void 0===n&&(n=t.DEFAULT_CREATION_OPTIONS),void 0===i&&(i=null),void 0===r&&(r=null),new t(e,n,i,r)},t.resolveOptions=function(e,t){if(t.detectIndentation){var n=L(e,t.tabSize,t.insertSpaces);return new c["e"]({tabSize:n.tabSize,indentSize:n.tabSize,insertSpaces:n.insertSpaces,trimAutoWhitespace:t.trimAutoWhitespace,defaultEOL:t.defaultEOL})}return new c["e"]({tabSize:t.tabSize,indentSize:t.indentSize,insertSpaces:t.insertSpaces,trimAutoWhitespace:t.trimAutoWhitespace,defaultEOL:t.defaultEOL})},t.prototype.onDidChangeRawContentFast=function(e){return this._eventEmitter.fastEvent((function(t){return e(t.rawContentChangedEvent)}))},t.prototype.onDidChangeRawContent=function(e){return this._eventEmitter.slowEvent((function(t){return e(t.rawContentChangedEvent)}))},t.prototype.onDidChangeContentFast=function(e){return this._eventEmitter.fastEvent((function(t){return e(t.contentChangedEvent)}))},t.prototype.onDidChangeContent=function(e){return this._eventEmitter.slowEvent((function(t){return e(t.contentChangedEvent)}))},t.prototype.dispose=function(){this._isDisposing=!0,this._onWillDispose.fire(),this._languageRegistryListener.dispose(),this._tokenization.dispose(),this._isDisposed=!0,e.prototype.dispose.call(this),this._isDisposing=!1},t.prototype._assertNotDisposed=function(){if(this._isDisposed)throw new Error("Model is disposed!")},t.prototype._emitContentChangedEvent=function(e,t){this._isDisposing||this._eventEmitter.fire(new Me(e,t))},t.prototype.setValue=function(e){if(this._assertNotDisposed(),null!==e){var t=tt(e,this._options.defaultEOL);this.setValueFromTextBuffer(t)}},t.prototype._createContentChanged2=function(e,t,n,i,r,o,s){return{changes:[{range:e,rangeOffset:t,rangeLength:n,text:i}],eol:this._buffer.getEOL(),versionId:this.getVersionId(),isUndoing:r,isRedoing:o,isFlush:s}},t.prototype.setValueFromTextBuffer=function(e){if(this._assertNotDisposed(),null!==e){var t=this.getFullModelRange(),n=this.getValueLengthInRange(t),i=this.getLineCount(),r=this.getLineMaxColumn(i);this._buffer=e,this._increaseVersionId(),this._tokens.flush(),this._tokens2.flush(),this._decorations=Object.create(null),this._decorationsTree=new at,this._commandManager=new _(this),this._trimAutoWhitespaceLines=null,this._emitContentChangedEvent(new De([new Se],this._versionId,!1,!1),this._createContentChanged2(new f["a"](1,1,i,r),0,n,this.getValue(),!1,!1,!0))}},t.prototype.setEOL=function(e){this._assertNotDisposed();var t=1===e?"\r\n":"\n";if(this._buffer.getEOL()!==t){var n=this.getFullModelRange(),i=this.getValueLengthInRange(n),r=this.getLineCount(),o=this.getLineMaxColumn(r);this._onBeforeEOLChange(),this._buffer.setEOL(t),this._increaseVersionId(),this._onAfterEOLChange(),this._emitContentChangedEvent(new De([new Te],this._versionId,!1,!1),this._createContentChanged2(new f["a"](1,1,r,o),0,i,this.getValue(),!1,!1,!1))}},t.prototype._onBeforeEOLChange=function(){var e=this.getVersionId(),t=this._decorationsTree.search(0,!1,!1,e);this._ensureNodesHaveRanges(t)},t.prototype._onAfterEOLChange=function(){for(var e=this.getVersionId(),t=this._decorationsTree.collectNodesPostOrder(),n=0,i=t.length;n<i;n++){var r=t[n],o=r.cachedAbsoluteStart-r.start,s=this._buffer.getOffsetAt(r.range.startLineNumber,r.range.startColumn),a=this._buffer.getOffsetAt(r.range.endLineNumber,r.range.endColumn);r.cachedAbsoluteStart=s,r.cachedAbsoluteEnd=a,r.cachedVersionId=e,r.start=s-o,r.end=a-o,Q(r)}},t.prototype.onBeforeAttached=function(){this._attachedEditorCount++,1===this._attachedEditorCount&&this._onDidChangeAttached.fire(void 0)},t.prototype.onBeforeDetached=function(){this._attachedEditorCount--,0===this._attachedEditorCount&&this._onDidChangeAttached.fire(void 0)},t.prototype.isAttachedToEditor=function(){return this._attachedEditorCount>0},t.prototype.getAttachedEditorCount=function(){return this._attachedEditorCount},t.prototype.isTooLargeForSyncing=function(){return this._isTooLargeForSyncing},t.prototype.isTooLargeForTokenization=function(){return this._isTooLargeForTokenization},t.prototype.isDisposed=function(){return this._isDisposed},t.prototype.isDominatedByLongLines=function(){if(this._assertNotDisposed(),this.isTooLargeForTokenization())return!1;for(var e=0,t=0,n=this._buffer.getLineCount(),i=1;i<=n;i++){var r=this._buffer.getLineLength(i);r>=rt?t+=r:e+=r}return t>e},Object.defineProperty(t.prototype,"uri",{get:function(){return this._associatedResource},enumerable:!0,configurable:!0}),t.prototype.getOptions=function(){return this._assertNotDisposed(),this._options},t.prototype.getFormattingOptions=function(){return{tabSize:this._options.indentSize,insertSpaces:this._options.insertSpaces}},t.prototype.updateOptions=function(e){this._assertNotDisposed();var t="undefined"!==typeof e.tabSize?e.tabSize:this._options.tabSize,n="undefined"!==typeof e.indentSize?e.indentSize:this._options.indentSize,i="undefined"!==typeof e.insertSpaces?e.insertSpaces:this._options.insertSpaces,r="undefined"!==typeof e.trimAutoWhitespace?e.trimAutoWhitespace:this._options.trimAutoWhitespace,o=new c["e"]({tabSize:t,indentSize:n,insertSpaces:i,defaultEOL:this._options.defaultEOL,trimAutoWhitespace:r});if(!this._options.equals(o)){var s=this._options.createChangeEvent(o);this._options=o,this._onDidChangeOptions.fire(s)}},t.prototype.detectIndentation=function(e,t){this._assertNotDisposed();var n=L(this._buffer,t,e);this.updateOptions({insertSpaces:n.insertSpaces,tabSize:n.tabSize,indentSize:n.tabSize})},t._normalizeIndentationFromWhitespace=function(e,t,n){for(var i=0,r=0;r<e.length;r++)"\t"===e.charAt(r)?i+=t:i++;var o="";if(!n){var s=Math.floor(i/t);i%=t;for(r=0;r<s;r++)o+="\t"}for(r=0;r<i;r++)o+=" ";return o},t.normalizeIndentation=function(e,n,i){var r=s["q"](e);return-1===r&&(r=e.length),t._normalizeIndentationFromWhitespace(e.substring(0,r),n,i)+e.substring(r)},t.prototype.normalizeIndentation=function(e){return this._assertNotDisposed(),t.normalizeIndentation(e,this._options.indentSize,this._options.insertSpaces)},t.prototype.getVersionId=function(){return this._assertNotDisposed(),this._versionId},t.prototype.mightContainRTL=function(){return this._buffer.mightContainRTL()},t.prototype.mightContainNonBasicASCII=function(){return this._buffer.mightContainNonBasicASCII()},t.prototype.getAlternativeVersionId=function(){return this._assertNotDisposed(),this._alternativeVersionId},t.prototype.getOffsetAt=function(e){this._assertNotDisposed();var t=this._validatePosition(e.lineNumber,e.column,0);return this._buffer.getOffsetAt(t.lineNumber,t.column)},t.prototype.getPositionAt=function(e){this._assertNotDisposed();var t=Math.min(this._buffer.getLength(),Math.max(0,e));return this._buffer.getPositionAt(t)},t.prototype._increaseVersionId=function(){this._versionId=this._versionId+1,this._alternativeVersionId=this._versionId},t.prototype._overwriteAlternativeVersionId=function(e){this._alternativeVersionId=e},t.prototype.getValue=function(e,t){void 0===t&&(t=!1),this._assertNotDisposed();var n=this.getFullModelRange(),i=this.getValueInRange(n,e);return t?this._buffer.getBOM()+i:i},t.prototype.getValueLength=function(e,t){void 0===t&&(t=!1),this._assertNotDisposed();var n=this.getFullModelRange(),i=this.getValueLengthInRange(n,e);return t?this._buffer.getBOM().length+i:i},t.prototype.getValueInRange=function(e,t){return void 0===t&&(t=0),this._assertNotDisposed(),this._buffer.getValueInRange(this.validateRange(e),t)},t.prototype.getValueLengthInRange=function(e,t){return void 0===t&&(t=0),this._assertNotDisposed(),this._buffer.getValueLengthInRange(this.validateRange(e),t)},t.prototype.getCharacterCountInRange=function(e,t){return void 0===t&&(t=0),this._assertNotDisposed(),this._buffer.getCharacterCountInRange(this.validateRange(e),t)},t.prototype.getLineCount=function(){return this._assertNotDisposed(),this._buffer.getLineCount()},t.prototype.getLineContent=function(e){if(this._assertNotDisposed(),e<1||e>this.getLineCount())throw new Error("Illegal value for lineNumber");return this._buffer.getLineContent(e)},t.prototype.getLineLength=function(e){if(this._assertNotDisposed(),e<1||e>this.getLineCount())throw new Error("Illegal value for lineNumber");return this._buffer.getLineLength(e)},t.prototype.getLinesContent=function(){return this._assertNotDisposed(),this._buffer.getLinesContent()},t.prototype.getEOL=function(){return this._assertNotDisposed(),this._buffer.getEOL()},t.prototype.getLineMinColumn=function(e){return this._assertNotDisposed(),1},t.prototype.getLineMaxColumn=function(e){if(this._assertNotDisposed(),e<1||e>this.getLineCount())throw new Error("Illegal value for lineNumber");return this._buffer.getLineLength(e)+1},t.prototype.getLineFirstNonWhitespaceColumn=function(e){if(this._assertNotDisposed(),e<1||e>this.getLineCount())throw new Error("Illegal value for lineNumber");return this._buffer.getLineFirstNonWhitespaceColumn(e)},t.prototype.getLineLastNonWhitespaceColumn=function(e){if(this._assertNotDisposed(),e<1||e>this.getLineCount())throw new Error("Illegal value for lineNumber");return this._buffer.getLineLastNonWhitespaceColumn(e)},t.prototype._validateRangeRelaxedNoAllocations=function(e){var t,n,i=this._buffer.getLineCount(),r=e.startLineNumber,o=e.startColumn;if(r<1)t=1,n=1;else if(r>i)t=i,n=this.getLineMaxColumn(t);else if(t=0|r,o<=1)n=1;else{var s=this.getLineMaxColumn(t);n=o>=s?s:0|o}var a,u,h=e.endLineNumber,c=e.endColumn;if(h<1)a=1,u=1;else if(h>i)a=i,u=this.getLineMaxColumn(a);else if(a=0|h,c<=1)u=1;else{s=this.getLineMaxColumn(a);u=c>=s?s:0|c}return r===t&&o===n&&h===a&&c===u&&e instanceof f["a"]&&!(e instanceof l["a"])?e:new f["a"](t,n,a,u)},t.prototype._isValidPosition=function(e,t,n){if("number"!==typeof e||"number"!==typeof t)return!1;if(isNaN(e)||isNaN(t))return!1;if(e<1||t<1)return!1;if((0|e)!==e||(0|t)!==t)return!1;var i=this._buffer.getLineCount();if(e>i)return!1;if(1===t)return!0;var r=this.getLineMaxColumn(e);if(t>r)return!1;if(1===n){var o=this._buffer.getLineCharCode(e,t-2);if(s["z"](o))return!1}return!0},t.prototype._validatePosition=function(e,t,n){var i=Math.floor("number"!==typeof e||isNaN(e)?1:e),r=Math.floor("number"!==typeof t||isNaN(t)?1:t),o=this._buffer.getLineCount();if(i<1)return new h["a"](1,1);if(i>o)return new h["a"](o,this.getLineMaxColumn(o));if(r<=1)return new h["a"](i,1);var a=this.getLineMaxColumn(i);if(r>=a)return new h["a"](i,a);if(1===n){var u=this._buffer.getLineCharCode(i,r-2);if(s["z"](u))return new h["a"](i,r-1)}return new h["a"](i,r)},t.prototype.validatePosition=function(e){var t=1;return this._assertNotDisposed(),e instanceof h["a"]&&this._isValidPosition(e.lineNumber,e.column,t)?e:this._validatePosition(e.lineNumber,e.column,t)},t.prototype._isValidRange=function(e,t){var n=e.startLineNumber,i=e.startColumn,r=e.endLineNumber,o=e.endColumn;if(!this._isValidPosition(n,i,0))return!1;if(!this._isValidPosition(r,o,0))return!1;if(1===t){var a=i>1?this._buffer.getLineCharCode(n,i-2):0,u=o>1&&o<=this._buffer.getLineLength(r)?this._buffer.getLineCharCode(r,o-2):0,h=s["z"](a),f=s["z"](u);return!h&&!f}return!0},t.prototype.validateRange=function(e){var t=1;if(this._assertNotDisposed(),e instanceof f["a"]&&!(e instanceof l["a"])&&this._isValidRange(e,t))return e;var n=this._validatePosition(e.startLineNumber,e.startColumn,0),i=this._validatePosition(e.endLineNumber,e.endColumn,0),r=n.lineNumber,o=n.column,a=i.lineNumber,u=i.column;if(1===t){var h=o>1?this._buffer.getLineCharCode(r,o-2):0,c=u>1&&u<=this._buffer.getLineLength(a)?this._buffer.getLineCharCode(a,u-2):0,d=s["z"](h),p=s["z"](c);return d||p?r===a&&o===u?new f["a"](r,o-1,a,u-1):d&&p?new f["a"](r,o-1,a,u+1):d?new f["a"](r,o-1,a,u):new f["a"](r,o,a,u+1):new f["a"](r,o,a,u)}return new f["a"](r,o,a,u)},t.prototype.modifyPosition=function(e,t){this._assertNotDisposed();var n=this.getOffsetAt(e)+t;return this.getPositionAt(Math.min(this._buffer.getLength(),Math.max(0,n)))},t.prototype.getFullModelRange=function(){this._assertNotDisposed();var e=this.getLineCount();return new f["a"](1,1,e,this.getLineMaxColumn(e))},t.prototype.findMatchesLineByLine=function(e,t,n,i){return this._buffer.findMatchesLineByLine(e,t,n,i)},t.prototype.findMatches=function(e,t,n,i,r,o,s){var a;if(void 0===s&&(s=it),this._assertNotDisposed(),a=f["a"].isIRange(t)?this.validateRange(t):this.getFullModelRange(),!n&&e.indexOf("\n")<0){var u=new pe["a"](e,n,i,r),h=u.parseSearchRequest();return h?this.findMatchesLineByLine(a,h,o,s):[]}return pe["c"].findMatches(this,new pe["a"](e,n,i,r),a,o,s)},t.prototype.findNextMatch=function(e,t,n,i,r,o){this._assertNotDisposed();var s=this.validatePosition(t);if(!n&&e.indexOf("\n")<0){var a=new pe["a"](e,n,i,r),u=a.parseSearchRequest();if(!u)return null;var h=this.getLineCount(),l=new f["a"](s.lineNumber,s.column,h,this.getLineMaxColumn(h)),c=this.findMatchesLineByLine(l,u,o,1);return pe["c"].findNextMatch(this,new pe["a"](e,n,i,r),s,o),c.length>0?c[0]:(l=new f["a"](1,1,s.lineNumber,this.getLineMaxColumn(s.lineNumber)),c=this.findMatchesLineByLine(l,u,o,1),c.length>0?c[0]:null)}return pe["c"].findNextMatch(this,new pe["a"](e,n,i,r),s,o)},t.prototype.findPreviousMatch=function(e,t,n,i,r,o){this._assertNotDisposed();var s=this.validatePosition(t);return pe["c"].findPreviousMatch(this,new pe["a"](e,n,i,r),s,o)},t.prototype.pushStackElement=function(){this._commandManager.pushStackElement()},t.prototype.pushEOL=function(e){var t="\n"===this.getEOL()?0:1;if(t!==e)try{this._onDidChangeDecorations.beginDeferredEmit(),this._eventEmitter.beginDeferredEmit(),this._commandManager.pushEOL(e)}finally{this._eventEmitter.endDeferredEmit(),this._onDidChangeDecorations.endDeferredEmit()}},t.prototype.pushEditOperations=function(e,t,n){try{return this._onDidChangeDecorations.beginDeferredEmit(),this._eventEmitter.beginDeferredEmit(),this._pushEditOperations(e,t,n)}finally{this._eventEmitter.endDeferredEmit(),this._onDidChangeDecorations.endDeferredEmit()}},t.prototype._pushEditOperations=function(e,t,n){var i=this;if(this._options.trimAutoWhitespace&&this._trimAutoWhitespaceLines){for(var r=t.map((function(e){return{range:i.validateRange(e.range),text:e.text}})),o=!0,s=0,a=e.length;s<a;s++){for(var u=e[s],h=!1,l=0,c=r.length;l<c;l++){var d=r[l].range,p=d.startLineNumber>u.endLineNumber,g=u.startLineNumber>d.endLineNumber;if(!p&&!g){h=!0;break}}if(!h){o=!1;break}}if(o)for(s=0,a=this._trimAutoWhitespaceLines.length;s<a;s++){var _=this._trimAutoWhitespaceLines[s],v=this.getLineMaxColumn(_),m=!0;for(l=0,c=r.length;l<c;l++){d=r[l].range;var L=r[l].text;if(!(_<d.startLineNumber||_>d.endLineNumber)&&(!(_===d.startLineNumber&&d.startColumn===v&&d.isEmpty()&&L&&L.length>0&&"\n"===L.charAt(0))&&!(_===d.startLineNumber&&1===d.startColumn&&d.isEmpty()&&L&&L.length>0&&"\n"===L.charAt(L.length-1)))){m=!1;break}}m&&t.push({range:new f["a"](_,1,_,v),text:null})}this._trimAutoWhitespaceLines=null}return this._commandManager.pushEditOperation(e,t,n)},t.prototype.applyEdits=function(e){try{return this._onDidChangeDecorations.beginDeferredEmit(),this._eventEmitter.beginDeferredEmit(),this._applyEdits(e)}finally{this._eventEmitter.endDeferredEmit(),this._onDidChangeDecorations.endDeferredEmit()}},t.prototype._applyEdits=function(e){for(var t=0,n=e.length;t<n;t++)e[t].range=this.validateRange(e[t].range);var i=this._buffer.getLineCount(),r=this._buffer.applyEdits(e,this._options.trimAutoWhitespace),o=this._buffer.getLineCount(),s=r.changes;if(this._trimAutoWhitespaceLines=r.trimAutoWhitespaceLineNumbers,0!==s.length){var a=[],u=i;for(t=0,n=s.length;t<n;t++){var h=s[t],f=Object(Ve["f"])(h.text),l=f[0],c=f[1],d=f[2];this._tokens.acceptEdit(h.range,l,c),this._tokens2.acceptEdit(h.range,l,c,d,h.text.length>0?h.text.charCodeAt(0):0),this._onDidChangeDecorations.fire(),this._decorationsTree.acceptReplace(h.rangeOffset,h.rangeLength,h.text.length,h.forceMoveMarkers);for(var p=h.range.startLineNumber,g=h.range.endLineNumber,_=g-p,v=l,m=Math.min(_,v),L=v-_,b=m;b>=0;b--){var C=p+b,I=o-u-L+C;a.push(new xe(C,this.getLineContent(I)))}if(m<_){var y=p+m;a.push(new Ee(y+1,g))}if(m<v){for(var N=p+m,k=v-m,w=o-u-k+N+1,S=[],x=0;x<k;x++){var E=w+x;S[E-w]=this.getLineContent(E)}a.push(new Oe(N+1,p+v,S))}u+=L}this._increaseVersionId(),this._emitContentChangedEvent(new De(a,this.getVersionId(),this._isUndoing,this._isRedoing),{changes:s,eol:this._buffer.getEOL(),versionId:this.getVersionId(),isUndoing:this._isUndoing,isRedoing:this._isRedoing,isFlush:!1})}return r.reverseEdits},t.prototype._undo=function(){this._isUndoing=!0;var e=this._commandManager.undo();return this._isUndoing=!1,e?(this._overwriteAlternativeVersionId(e.recordedVersionId),e.selections):null},t.prototype.undo=function(){try{return this._onDidChangeDecorations.beginDeferredEmit(),this._eventEmitter.beginDeferredEmit(),this._undo()}finally{this._eventEmitter.endDeferredEmit(),this._onDidChangeDecorations.endDeferredEmit()}},t.prototype.canUndo=function(){return this._commandManager.canUndo()},t.prototype._redo=function(){this._isRedoing=!0;var e=this._commandManager.redo();return this._isRedoing=!1,e?(this._overwriteAlternativeVersionId(e.recordedVersionId),e.selections):null},t.prototype.redo=function(){try{return this._onDidChangeDecorations.beginDeferredEmit(),this._eventEmitter.beginDeferredEmit(),this._redo()}finally{this._eventEmitter.endDeferredEmit(),this._onDidChangeDecorations.endDeferredEmit()}},t.prototype.canRedo=function(){return this._commandManager.canRedo()},t.prototype.changeDecorations=function(e,t){void 0===t&&(t=0),this._assertNotDisposed();try{return this._onDidChangeDecorations.beginDeferredEmit(),this._changeDecorations(t,e)}finally{this._onDidChangeDecorations.endDeferredEmit()}},t.prototype._changeDecorations=function(e,t){var n=this,r={addDecoration:function(t,i){return n._onDidChangeDecorations.fire(),n._deltaDecorationsImpl(e,[],[{range:t,options:i}])[0]},changeDecoration:function(e,t){n._onDidChangeDecorations.fire(),n._changeDecorationImpl(e,t)},changeDecorationOptions:function(e,t){n._onDidChangeDecorations.fire(),n._changeDecorationOptionsImpl(e,pt(t))},removeDecoration:function(t){n._onDidChangeDecorations.fire(),n._deltaDecorationsImpl(e,[t],[])},deltaDecorations:function(t,i){return 0===t.length&&0===i.length?[]:(n._onDidChangeDecorations.fire(),n._deltaDecorationsImpl(e,t,i))}},o=null;try{o=t(r)}catch(s){Object(i["e"])(s)}return r.addDecoration=ot,r.changeDecoration=ot,r.changeDecorationOptions=ot,r.removeDecoration=ot,r.deltaDecorations=ot,o},t.prototype.deltaDecorations=function(e,t,n){if(void 0===n&&(n=0),this._assertNotDisposed(),e||(e=[]),0===e.length&&0===t.length)return[];try{return this._onDidChangeDecorations.beginDeferredEmit(),this._onDidChangeDecorations.fire(),this._deltaDecorationsImpl(n,e,t)}finally{this._onDidChangeDecorations.endDeferredEmit()}},t.prototype._getTrackedRange=function(e){return this.getDecorationRange(e)},t.prototype._setTrackedRange=function(e,t,n){var i=e?this._decorations[e]:null;if(!i)return t?this._deltaDecorationsImpl(0,[],[{range:t,options:dt[n]}])[0]:null;if(!t)return this._decorationsTree.delete(i),delete this._decorations[i.id],null;var r=this._validateRangeRelaxedNoAllocations(t),o=this._buffer.getOffsetAt(r.startLineNumber,r.startColumn),s=this._buffer.getOffsetAt(r.endLineNumber,r.endColumn);return this._decorationsTree.delete(i),i.reset(this.getVersionId(),o,s,r),i.setOptions(dt[n]),this._decorationsTree.insert(i),i.id},t.prototype.removeAllDecorationsWithOwnerId=function(e){if(!this._isDisposed)for(var t=this._decorationsTree.collectNodesFromOwner(e),n=0,i=t.length;n<i;n++){var r=t[n];this._decorationsTree.delete(r),delete this._decorations[r.id]}},t.prototype.getDecorationOptions=function(e){var t=this._decorations[e];return t?t.options:null},t.prototype.getDecorationRange=function(e){var t=this._decorations[e];if(!t)return null;var n=this.getVersionId();return t.cachedVersionId!==n&&this._decorationsTree.resolveNode(t,n),null===t.range&&(t.range=this._getRangeAt(t.cachedAbsoluteStart,t.cachedAbsoluteEnd)),t.range},t.prototype.getLineDecorations=function(e,t,n){return void 0===t&&(t=0),void 0===n&&(n=!1),e<1||e>this.getLineCount()?[]:this.getLinesDecorations(e,e,t,n)},t.prototype.getLinesDecorations=function(e,t,n,i){void 0===n&&(n=0),void 0===i&&(i=!1);var r=this.getLineCount(),o=Math.min(r,Math.max(1,e)),s=Math.min(r,Math.max(1,t)),a=this.getLineMaxColumn(s);return this._getDecorationsInRange(new f["a"](o,1,s,a),n,i)},t.prototype.getDecorationsInRange=function(e,t,n){void 0===t&&(t=0),void 0===n&&(n=!1);var i=this.validateRange(e);return this._getDecorationsInRange(i,t,n)},t.prototype.getOverviewRulerDecorations=function(e,t){void 0===e&&(e=0),void 0===t&&(t=!1);var n=this.getVersionId(),i=this._decorationsTree.search(e,t,!0,n);return this._ensureNodesHaveRanges(i)},t.prototype.getAllDecorations=function(e,t){void 0===e&&(e=0),void 0===t&&(t=!1);var n=this.getVersionId(),i=this._decorationsTree.search(e,t,!1,n);return this._ensureNodesHaveRanges(i)},t.prototype._getDecorationsInRange=function(e,t,n){var i=this._buffer.getOffsetAt(e.startLineNumber,e.startColumn),r=this._buffer.getOffsetAt(e.endLineNumber,e.endColumn),o=this.getVersionId(),s=this._decorationsTree.intervalSearch(i,r,t,n,o);return this._ensureNodesHaveRanges(s)},t.prototype._ensureNodesHaveRanges=function(e){for(var t=0,n=e.length;t<n;t++){var i=e[t];null===i.range&&(i.range=this._getRangeAt(i.cachedAbsoluteStart,i.cachedAbsoluteEnd))}return e},t.prototype._getRangeAt=function(e,t){return this._buffer.getRangeAt(e,t-e)},t.prototype._changeDecorationImpl=function(e,t){var n=this._decorations[e];if(n){var i=this._validateRangeRelaxedNoAllocations(t),r=this._buffer.getOffsetAt(i.startLineNumber,i.startColumn),o=this._buffer.getOffsetAt(i.endLineNumber,i.endColumn);this._decorationsTree.delete(n),n.reset(this.getVersionId(),r,o,i),this._decorationsTree.insert(n)}},t.prototype._changeDecorationOptionsImpl=function(e,t){var n=this._decorations[e];if(n){var i=!(!n.options.overviewRuler||!n.options.overviewRuler.color),r=!(!t.overviewRuler||!t.overviewRuler.color);i!==r?(this._decorationsTree.delete(n),n.setOptions(t),this._decorationsTree.insert(n)):n.setOptions(t)}},t.prototype._deltaDecorationsImpl=function(e,t,n){var i=this.getVersionId(),r=t.length,o=0,s=n.length,a=0,u=new Array(s);while(o<r||a<s){var h=null;if(o<r){do{h=this._decorations[t[o++]]}while(!h&&o<r);h&&this._decorationsTree.delete(h)}if(a<s){if(!h){var f=++this._lastDecorationId,l=this._instanceId+";"+f;h=new D(l,0,0),this._decorations[l]=h}var c=n[a],d=this._validateRangeRelaxedNoAllocations(c.range),p=pt(c.options),g=this._buffer.getOffsetAt(d.startLineNumber,d.startColumn),_=this._buffer.getOffsetAt(d.endLineNumber,d.endColumn);h.ownerId=e,h.reset(i,g,_,d),h.setOptions(p),this._decorationsTree.insert(h),u[a]=h.id,a++}else h&&delete this._decorations[h.id]}return u},t.prototype.setLineTokens=function(e,t){if(e<1||e>this.getLineCount())throw new Error("Illegal value for lineNumber");this._tokens.setTokens(this._languageIdentifier.id,e-1,this._buffer.getLineLength(e),t)},t.prototype.setTokens=function(e){if(0!==e.length){for(var t=[],n=0,i=e.length;n<i;n++){var r=e[n];t.push({fromLineNumber:r.startLineNumber,toLineNumber:r.startLineNumber+r.tokens.length-1});for(var o=0,s=r.tokens.length;o<s;o++)this.setLineTokens(r.startLineNumber+o,r.tokens[o])}this._emitModelTokensChangedEvent({tokenizationSupportChanged:!1,ranges:t})}},t.prototype.setSemanticTokens=function(e){this._tokens2.set(e),this._emitModelTokensChangedEvent({tokenizationSupportChanged:!1,ranges:[{fromLineNumber:1,toLineNumber:this.getLineCount()}]})},t.prototype.tokenizeViewport=function(e,t){e=Math.max(1,e),t=Math.min(this._buffer.getLineCount(),t),this._tokenization.tokenizeViewport(e,t)},t.prototype.clearTokens=function(){this._tokens.flush(),this._emitModelTokensChangedEvent({tokenizationSupportChanged:!0,ranges:[{fromLineNumber:1,toLineNumber:this._buffer.getLineCount()}]})},t.prototype._emitModelTokensChangedEvent=function(e){this._isDisposing||this._onDidChangeTokens.fire(e)},t.prototype.resetTokenization=function(){this._tokenization.reset()},t.prototype.forceTokenization=function(e){if(e<1||e>this.getLineCount())throw new Error("Illegal value for lineNumber");this._tokenization.forceTokenization(e)},t.prototype.isCheapToTokenize=function(e){return this._tokenization.isCheapToTokenize(e)},t.prototype.tokenizeIfCheap=function(e){this.isCheapToTokenize(e)&&this.forceTokenization(e)},t.prototype.getLineTokens=function(e){if(e<1||e>this.getLineCount())throw new Error("Illegal value for lineNumber");return this._getLineTokens(e)},t.prototype._getLineTokens=function(e){var t=this.getLineContent(e),n=this._tokens.getTokens(this._languageIdentifier.id,e-1,t);return this._tokens2.addSemanticTokens(e,n)},t.prototype.getLanguageIdentifier=function(){return this._languageIdentifier},t.prototype.getModeId=function(){return this._languageIdentifier.language},t.prototype.setMode=function(e){if(this._languageIdentifier.id!==e.id){var t={oldLanguage:this._languageIdentifier.language,newLanguage:e.language};this._languageIdentifier=e,this._onDidChangeLanguage.fire(t),this._onDidChangeLanguageConfiguration.fire({})}},t.prototype.getLanguageIdAtPosition=function(e,t){var n=this.validatePosition(new h["a"](e,t)),i=this.getLineTokens(n.lineNumber);return i.getLanguageId(i.findTokenIndexAtOffset(n.column-1))},t.prototype.getWordAtPosition=function(e){this._assertNotDisposed();var n=this.validatePosition(e),i=this.getLineContent(n.lineNumber),r=this._getLineTokens(n.lineNumber),o=r.findTokenIndexAtOffset(n.column-1),s=t._findLanguageBoundaries(r,o),a=s[0],u=s[1],h=Object(Ge["d"])(n.column,$e["a"].getWordDefinition(r.getLanguageId(o)),i.substring(a,u),a);if(h&&h.startColumn<=e.column&&e.column<=h.endColumn)return h;if(o>0&&a===n.column-1){var f=t._findLanguageBoundaries(r,o-1),l=f[0],c=f[1],d=Object(Ge["d"])(n.column,$e["a"].getWordDefinition(r.getLanguageId(o-1)),i.substring(l,c),l);if(d&&d.startColumn<=e.column&&e.column<=d.endColumn)return d}return null},t._findLanguageBoundaries=function(e,t){for(var n=e.getLanguageId(t),i=0,r=t;r>=0&&e.getLanguageId(r)===n;r--)i=e.getStartOffset(r);for(var o=e.getLineContent().length,s=(r=t,e.getCount());r<s&&e.getLanguageId(r)===n;r++)o=e.getEndOffset(r);return[i,o]},t.prototype.getWordUntilPosition=function(e){var t=this.getWordAtPosition(e);return t?{word:t.word.substr(0,e.column-t.startColumn),startColumn:t.startColumn,endColumn:e.column}:{word:"",startColumn:e.column,endColumn:e.column}},t.prototype.findMatchingBracketUp=function(e,t){var n=e.toLowerCase(),i=this.validatePosition(t),r=this._getLineTokens(i.lineNumber),o=r.getLanguageId(r.findTokenIndexAtOffset(i.column-1)),s=$e["a"].getBracketsSupport(o);if(!s)return null;var a=s.textIsBracket[n];return a?this._findMatchingBracketUp(a,i):null},t.prototype.matchBracket=function(e){return this._matchBracket(this.validatePosition(e))},t.prototype._matchBracket=function(e){var t=e.lineNumber,n=this._getLineTokens(t),i=n.getCount(),r=this._buffer.getLineContent(t),o=n.findTokenIndexAtOffset(e.column-1);if(o<0)return null;var s=$e["a"].getBracketsSupport(n.getLanguageId(o));if(s&&!Object(Ye["b"])(n.getStandardTokenType(o))){for(var a=Math.max(0,e.column-1-s.maxBracketLength),u=o-1;u>=0;u--){var h=n.getEndOffset(u);if(h<=a)break;Object(Ye["b"])(n.getStandardTokenType(u))&&(a=h)}var f=Math.min(r.length,e.column-1+s.maxBracketLength),l=null;while(1){var c=Je["a"].findNextBracketInRange(s.forwardRegex,t,r,a,f);if(!c)break;if(c.startColumn<=e.column&&e.column<=c.endColumn){var d=r.substring(c.startColumn-1,c.endColumn-1).toLowerCase(),p=this._matchFoundBracket(c,s.textIsBracket[d],s.textIsOpenBracket[d]);p&&(l=p)}a=c.endColumn-1}if(l)return l}if(o>0&&n.getStartOffset(o)===e.column-1){var g=o-1,_=$e["a"].getBracketsSupport(n.getLanguageId(g));if(_&&!Object(Ye["b"])(n.getStandardTokenType(g))){for(a=Math.max(0,e.column-1-_.maxBracketLength),f=Math.min(r.length,e.column-1+_.maxBracketLength),u=g+1;u<i;u++){var v=n.getStartOffset(u);if(v>=f)break;Object(Ye["b"])(n.getStandardTokenType(u))&&(f=v)}c=Je["a"].findPrevBracketInRange(_.reversedRegex,t,r,a,f);if(c&&c.startColumn<=e.column&&e.column<=c.endColumn){d=r.substring(c.startColumn-1,c.endColumn-1).toLowerCase(),p=this._matchFoundBracket(c,_.textIsBracket[d],_.textIsOpenBracket[d]);if(p)return p}}}return null},t.prototype._matchFoundBracket=function(e,t,n){if(!t)return null;if(n){var i=this._findMatchingBracketDown(t,e.getEndPosition());if(i)return[e,i]}else{i=this._findMatchingBracketUp(t,e.getStartPosition());if(i)return[e,i]}return null},t.prototype._findMatchingBracketUp=function(e,t){for(var n=e.languageIdentifier.id,i=e.reversedRegex,r=-1,o=function(t,n,o,s){while(1){var a=Je["a"].findPrevBracketInRange(i,t,n,o,s);if(!a)break;var u=n.substring(a.startColumn-1,a.endColumn-1).toLowerCase();if(e.isOpen(u)?r++:e.isClose(u)&&r--,0===r)return a;s=a.startColumn-1}return null},s=t.lineNumber;s>=1;s--){var a=this._getLineTokens(s),u=a.getCount(),h=this._buffer.getLineContent(s),f=u-1,l=h.length,c=h.length;s===t.lineNumber&&(f=a.findTokenIndexAtOffset(t.column-1),l=t.column-1,c=t.column-1);for(var d=!0;f>=0;f--){var p=a.getLanguageId(f)===n&&!Object(Ye["b"])(a.getStandardTokenType(f));if(p)d?l=a.getStartOffset(f):(l=a.getStartOffset(f),c=a.getEndOffset(f));else if(d&&l!==c){var g=o(s,h,l,c);if(g)return g}d=p}if(d&&l!==c){g=o(s,h,l,c);if(g)return g}}return null},t.prototype._findMatchingBracketDown=function(e,t){for(var n=e.languageIdentifier.id,i=e.forwardRegex,r=1,o=function(t,n,o,s){while(1){var a=Je["a"].findNextBracketInRange(i,t,n,o,s);if(!a)break;var u=n.substring(a.startColumn-1,a.endColumn-1).toLowerCase();if(e.isOpen(u)?r++:e.isClose(u)&&r--,0===r)return a;o=a.endColumn-1}return null},s=this.getLineCount(),a=t.lineNumber;a<=s;a++){var u=this._getLineTokens(a),h=u.getCount(),f=this._buffer.getLineContent(a),l=0,c=0,d=0;a===t.lineNumber&&(l=u.findTokenIndexAtOffset(t.column-1),c=t.column-1,d=t.column-1);for(var p=!0;l<h;l++){var g=u.getLanguageId(l)===n&&!Object(Ye["b"])(u.getStandardTokenType(l));if(g)p||(c=u.getStartOffset(l)),d=u.getEndOffset(l);else if(p&&c!==d){var _=o(a,f,c,d);if(_)return _}p=g}if(p&&c!==d){_=o(a,f,c,d);if(_)return _}}return null},t.prototype.findPrevBracket=function(e){for(var t=this.validatePosition(e),n=-1,i=null,r=t.lineNumber;r>=1;r--){var o=this._getLineTokens(r),s=o.getCount(),a=this._buffer.getLineContent(r),u=s-1,h=a.length,f=a.length;if(r===t.lineNumber){u=o.findTokenIndexAtOffset(t.column-1),h=t.column-1,f=t.column-1;var l=o.getLanguageId(u);n!==l&&(n=l,i=$e["a"].getBracketsSupport(n))}for(var c=!0;u>=0;u--){l=o.getLanguageId(u);if(n!==l){if(i&&c&&h!==f){var d=Je["a"].findPrevBracketInRange(i.reversedRegex,r,a,h,f);if(d)return this._toFoundBracket(i,d);c=!1}n=l,i=$e["a"].getBracketsSupport(n)}var p=!!i&&!Object(Ye["b"])(o.getStandardTokenType(u));if(p)c?h=o.getStartOffset(u):(h=o.getStartOffset(u),f=o.getEndOffset(u));else if(i&&c&&h!==f){d=Je["a"].findPrevBracketInRange(i.reversedRegex,r,a,h,f);if(d)return this._toFoundBracket(i,d)}c=p}if(i&&c&&h!==f){d=Je["a"].findPrevBracketInRange(i.reversedRegex,r,a,h,f);if(d)return this._toFoundBracket(i,d)}}return null},t.prototype.findNextBracket=function(e){for(var t=this.validatePosition(e),n=this.getLineCount(),i=-1,r=null,o=t.lineNumber;o<=n;o++){var s=this._getLineTokens(o),a=s.getCount(),u=this._buffer.getLineContent(o),h=0,f=0,l=0;if(o===t.lineNumber){h=s.findTokenIndexAtOffset(t.column-1),f=t.column-1,l=t.column-1;var c=s.getLanguageId(h);i!==c&&(i=c,r=$e["a"].getBracketsSupport(i))}for(var d=!0;h<a;h++){c=s.getLanguageId(h);if(i!==c){if(r&&d&&f!==l){var p=Je["a"].findNextBracketInRange(r.forwardRegex,o,u,f,l);if(p)return this._toFoundBracket(r,p);d=!1}i=c,r=$e["a"].getBracketsSupport(i)}var g=!!r&&!Object(Ye["b"])(s.getStandardTokenType(h));if(g)d||(f=s.getStartOffset(h)),l=s.getEndOffset(h);else if(r&&d&&f!==l){p=Je["a"].findNextBracketInRange(r.forwardRegex,o,u,f,l);if(p)return this._toFoundBracket(r,p)}d=g}if(r&&d&&f!==l){p=Je["a"].findNextBracketInRange(r.forwardRegex,o,u,f,l);if(p)return this._toFoundBracket(r,p)}}return null},t.prototype.findEnclosingBrackets=function(e,t){var n=this;void 0===t&&(t=1073741824);for(var i=this.validatePosition(e),r=this.getLineCount(),o=new Map,s=[],a=function(e,t){if(!o.has(e)){for(var n=[],i=0,r=t?t.brackets.length:0;i<r;i++)n[i]=0;o.set(e,n)}s=o.get(e)},u=function(e,t,i,r,o){while(1){var a=Je["a"].findNextBracketInRange(e.forwardRegex,t,i,r,o);if(!a)break;var u=i.substring(a.startColumn-1,a.endColumn-1).toLowerCase(),h=e.textIsBracket[u];if(h&&(h.isOpen(u)?s[h.index]++:h.isClose(u)&&s[h.index]--,-1===s[h.index]))return n._matchFoundBracket(a,h,!1);r=a.endColumn-1}return null},h=-1,f=null,l=Date.now(),c=i.lineNumber;c<=r;c++){var d=Date.now()-l;if(d>t)return null;var p=this._getLineTokens(c),g=p.getCount(),_=this._buffer.getLineContent(c),v=0,m=0,L=0;if(c===i.lineNumber){v=p.findTokenIndexAtOffset(i.column-1),m=i.column-1,L=i.column-1;var b=p.getLanguageId(v);h!==b&&(h=b,f=$e["a"].getBracketsSupport(h),a(h,f))}for(var C=!0;v<g;v++){b=p.getLanguageId(v);if(h!==b){if(f&&C&&m!==L){var I=u(f,c,_,m,L);if(I)return I;C=!1}h=b,f=$e["a"].getBracketsSupport(h),a(h,f)}var y=!!f&&!Object(Ye["b"])(p.getStandardTokenType(v));if(y)C||(m=p.getStartOffset(v)),L=p.getEndOffset(v);else if(f&&C&&m!==L){I=u(f,c,_,m,L);if(I)return I}C=y}if(f&&C&&m!==L){I=u(f,c,_,m,L);if(I)return I}}return null},t.prototype._toFoundBracket=function(e,t){if(!t)return null;var n=this.getValueInRange(t);n=n.toLowerCase();var i=e.textIsBracket[n];return i?{range:t,open:i.open,close:i.close,isOpen:e.textIsOpenBracket[n]}:null},t.computeIndentLevel=function(e,t){var n=0,i=0,r=e.length;while(i<r){var o=e.charCodeAt(i);if(32===o)n++;else{if(9!==o)break;n=n-n%t+t}i++}return i===r?-1:n},t.prototype._computeIndentLevel=function(e){return t.computeIndentLevel(this._buffer.getLineContent(e+1),this._options.tabSize)},t.prototype.getActiveIndentGuide=function(e,t,n){var i=this;this._assertNotDisposed();var r=this.getLineCount();if(e<1||e>r)throw new Error("Illegal value for lineNumber");for(var o=$e["a"].getFoldingRules(this._languageIdentifier.id),s=Boolean(o&&o.offSide),a=-2,u=-1,h=-2,f=-1,l=function(e){if(-1!==a&&(-2===a||a>e-1)){a=-1,u=-1;for(var t=e-2;t>=0;t--){var n=i._computeIndentLevel(t);if(n>=0){a=t,u=n;break}}}if(-2===h){h=-1,f=-1;for(t=e;t<r;t++){var o=i._computeIndentLevel(t);if(o>=0){h=t,f=o;break}}}},c=-2,d=-1,p=-2,g=-1,_=function(e){if(-2===c){c=-1,d=-1;for(var t=e-2;t>=0;t--){var n=i._computeIndentLevel(t);if(n>=0){c=t,d=n;break}}}if(-1!==p&&(-2===p||p<e-1)){p=-1,g=-1;for(t=e;t<r;t++){var o=i._computeIndentLevel(t);if(o>=0){p=t,g=o;break}}}},v=0,m=!0,L=0,b=!0,C=0,I=0;m||b;I++){var y=e-I,N=e+I;if(0!==I&&(y<1||y<t)&&(m=!1),0!==I&&(N>r||N>n)&&(b=!1),I>5e4&&(m=!1,b=!1),m){var k=void 0,w=this._computeIndentLevel(y-1);if(w>=0?(h=y-1,f=w,k=Math.ceil(w/this._options.indentSize)):(l(y),k=this._getIndentLevelForWhitespaceLine(s,u,f)),0===I){if(v=y,L=N,C=k,0===C)return{startLineNumber:v,endLineNumber:L,indent:C};continue}k>=C?v=y:m=!1}if(b){var S=void 0;w=this._computeIndentLevel(N-1);w>=0?(c=N-1,d=w,S=Math.ceil(w/this._options.indentSize)):(_(N),S=this._getIndentLevelForWhitespaceLine(s,d,g)),S>=C?L=N:b=!1}}return{startLineNumber:v,endLineNumber:L,indent:C}},t.prototype.getLinesIndentGuides=function(e,t){this._assertNotDisposed();var n=this.getLineCount();if(e<1||e>n)throw new Error("Illegal value for startLineNumber");if(t<1||t>n)throw new Error("Illegal value for endLineNumber");for(var i=$e["a"].getFoldingRules(this._languageIdentifier.id),r=Boolean(i&&i.offSide),o=new Array(t-e+1),s=-2,a=-1,u=-2,h=-1,f=e;f<=t;f++){var l=f-e,c=this._computeIndentLevel(f-1);if(c>=0)s=f-1,a=c,o[l]=Math.ceil(c/this._options.indentSize);else{if(-2===s){s=-1,a=-1;for(var d=f-2;d>=0;d--){var p=this._computeIndentLevel(d);if(p>=0){s=d,a=p;break}}}if(-1!==u&&(-2===u||u<f-1)){u=-1,h=-1;for(d=f;d<n;d++){p=this._computeIndentLevel(d);if(p>=0){u=d,h=p;break}}}o[l]=this._getIndentLevelForWhitespaceLine(r,a,h)}}return o},t.prototype._getIndentLevelForWhitespaceLine=function(e,t,n){return-1===t||-1===n?0:t<n?1+Math.floor(t/this._options.indentSize):t===n||e?Math.ceil(n/this._options.indentSize):1+Math.floor(n/this._options.indentSize)},t.MODEL_SYNC_LIMIT=52428800,t.LARGE_FILE_SIZE_THRESHOLD=20971520,t.LARGE_FILE_LINE_COUNT_THRESHOLD=3e5,t.DEFAULT_CREATION_OPTIONS={isForSimpleWidget:!1,tabSize:u["c"].tabSize,indentSize:u["c"].indentSize,insertSpaces:u["c"].insertSpaces,detectIndentation:!1,defaultEOL:1,trimAutoWhitespace:u["c"].trimAutoWhitespace,largeFileOptimizations:u["c"].largeFileOptimizations},t}(o["a"]),at=function(){function e(){this._decorationsTree0=new A,this._decorationsTree1=new A}return e.prototype.intervalSearch=function(e,t,n,i,r){var o=this._decorationsTree0.intervalSearch(e,t,n,i,r),s=this._decorationsTree1.intervalSearch(e,t,n,i,r);return o.concat(s)},e.prototype.search=function(e,t,n,i){if(n)return this._decorationsTree1.search(e,t,i);var r=this._decorationsTree0.search(e,t,i),o=this._decorationsTree1.search(e,t,i);return r.concat(o)},e.prototype.collectNodesFromOwner=function(e){var t=this._decorationsTree0.collectNodesFromOwner(e),n=this._decorationsTree1.collectNodesFromOwner(e);return t.concat(n)},e.prototype.collectNodesPostOrder=function(){var e=this._decorationsTree0.collectNodesPostOrder(),t=this._decorationsTree1.collectNodesPostOrder();return e.concat(t)},e.prototype.insert=function(e){w(e)?this._decorationsTree1.insert(e):this._decorationsTree0.insert(e)},e.prototype.delete=function(e){w(e)?this._decorationsTree1.delete(e):this._decorationsTree0.delete(e)},e.prototype.resolveNode=function(e,t){w(e)?this._decorationsTree1.resolveNode(e,t):this._decorationsTree0.resolveNode(e,t)},e.prototype.acceptReplace=function(e,t,n,i){this._decorationsTree0.acceptReplace(e,t,n,i),this._decorationsTree1.acceptReplace(e,t,n,i)},e}();function ut(e){return e.replace(/[^a-z0-9\-_]/gi," ")}var ht=function(){function e(e){this.color=e.color||"",this.darkColor=e.darkColor||""}return e}(),ft=function(e){function t(t){var n=e.call(this,t)||this;return n._resolvedColor=null,n.position="number"===typeof t.position?t.position:c["d"].Center,n}return Qe(t,e),t.prototype.getColor=function(e){return this._resolvedColor||("light"!==e.type&&this.darkColor?this._resolvedColor=this._resolveColor(this.darkColor,e):this._resolvedColor=this._resolveColor(this.color,e)),this._resolvedColor},t.prototype.invalidateCachedColor=function(){this._resolvedColor=null},t.prototype._resolveColor=function(e,t){if("string"===typeof e)return e;var n=e?t.getColor(e.id):null;return n?n.toString():""},t}(ht),lt=function(e){function t(t){var n=e.call(this,t)||this;return n.position=t.position,n}return Qe(t,e),t.prototype.getColor=function(e){return this._resolvedColor||("light"!==e.type&&this.darkColor?this._resolvedColor=this._resolveColor(this.darkColor,e):this._resolvedColor=this._resolveColor(this.color,e)),this._resolvedColor},t.prototype.invalidateCachedColor=function(){this._resolvedColor=void 0},t.prototype._resolveColor=function(e,t){return"string"===typeof e?Ke["a"].fromHex(e):t.getColor(e.id)},t}(ht),ct=function(){function e(e){this.stickiness=e.stickiness||0,this.zIndex=e.zIndex||0,this.className=e.className?ut(e.className):null,this.hoverMessage=Object(Ze["o"])(e.hoverMessage),this.glyphMarginHoverMessage=Object(Ze["o"])(e.glyphMarginHoverMessage),this.isWholeLine=e.isWholeLine||!1,this.showIfCollapsed=e.showIfCollapsed||!1,this.collapseOnReplaceEdit=e.collapseOnReplaceEdit||!1,this.overviewRuler=e.overviewRuler?new ft(e.overviewRuler):null,this.minimap=e.minimap?new lt(e.minimap):null,this.glyphMarginClassName=e.glyphMarginClassName?ut(e.glyphMarginClassName):null,this.linesDecorationsClassName=e.linesDecorationsClassName?ut(e.linesDecorationsClassName):null,this.marginClassName=e.marginClassName?ut(e.marginClassName):null,this.inlineClassName=e.inlineClassName?ut(e.inlineClassName):null,this.inlineClassNameAffectsLetterSpacing=e.inlineClassNameAffectsLetterSpacing||!1,this.beforeContentClassName=e.beforeContentClassName?ut(e.beforeContentClassName):null,this.afterContentClassName=e.afterContentClassName?ut(e.afterContentClassName):null}return e.register=function(t){return new e(t)},e.createDynamic=function(t){return new e(t)},e}();ct.EMPTY=ct.register({});var dt=[ct.register({stickiness:0}),ct.register({stickiness:1}),ct.register({stickiness:2}),ct.register({stickiness:3})];function pt(e){return e instanceof ct?e:ct.createDynamic(e)}var gt=function(e){function t(){var t=e.call(this)||this;return t._actual=t._register(new r["a"]),t.event=t._actual.event,t._deferredCnt=0,t._shouldFire=!1,t}return Qe(t,e),t.prototype.beginDeferredEmit=function(){this._deferredCnt++},t.prototype.endDeferredEmit=function(){this._deferredCnt--,0===this._deferredCnt&&this._shouldFire&&(this._shouldFire=!1,this._actual.fire({}))},t.prototype.fire=function(){this._shouldFire=!0},t}(o["a"]),_t=function(e){function t(){var t=e.call(this)||this;return t._fastEmitter=t._register(new r["a"]),t.fastEvent=t._fastEmitter.event,t._slowEmitter=t._register(new r["a"]),t.slowEvent=t._slowEmitter.event,t._deferredCnt=0,t._deferredEvent=null,t}return Qe(t,e),t.prototype.beginDeferredEmit=function(){this._deferredCnt++},t.prototype.endDeferredEmit=function(){if(this._deferredCnt--,0===this._deferredCnt&&null!==this._deferredEvent){var e=this._deferredEvent;this._deferredEvent=null,this._fastEmitter.fire(e),this._slowEmitter.fire(e)}},t.prototype.fire=function(e){this._deferredCnt>0?this._deferredEvent?this._deferredEvent=this._deferredEvent.merge(e):this._deferredEvent=e:(this._fastEmitter.fire(e),this._slowEmitter.fire(e))},t}(o["a"])},b707:function(e,t,n){"use strict";n.d(t,"r",(function(){return l})),n.d(t,"A",(function(){return c})),n.d(t,"F",(function(){return d})),n.d(t,"E",(function(){return p})),n.d(t,"y",(function(){return i})),n.d(t,"h",(function(){return r})),n.d(t,"G",(function(){return g})),n.d(t,"z",(function(){return o})),n.d(t,"n",(function(){return m})),n.d(t,"D",(function(){return v})),n.d(t,"u",(function(){return L})),n.d(t,"v",(function(){return b})),n.d(t,"d",(function(){return C})),n.d(t,"x",(function(){return I})),n.d(t,"p",(function(){return y})),n.d(t,"m",(function(){return N})),n.d(t,"i",(function(){return k})),n.d(t,"f",(function(){return w})),n.d(t,"e",(function(){return S})),n.d(t,"q",(function(){return x})),n.d(t,"C",(function(){return E})),n.d(t,"b",(function(){return O})),n.d(t,"a",(function(){return T})),n.d(t,"g",(function(){return D})),n.d(t,"j",(function(){return M})),n.d(t,"t",(function(){return A})),n.d(t,"s",(function(){return R})),n.d(t,"c",(function(){return z})),n.d(t,"w",(function(){return B})),n.d(t,"o",(function(){return F})),n.d(t,"l",(function(){return V})),n.d(t,"k",(function(){return P})),n.d(t,"B",(function(){return W}));var i,r,o,s=n("ef8e"),a=n("6d8e"),u=n("6a89"),h=n("6a5d"),f=n("72b4"),l=function(){function e(e,t){this.language=e,this.id=t}return e}(),c=function(){function e(){}return e.getLanguageId=function(e){return(255&e)>>>0},e.getTokenType=function(e){return(1792&e)>>>8},e.getFontStyle=function(e){return(14336&e)>>>11},e.getForeground=function(e){return(8372224&e)>>>14},e.getBackground=function(e){return(4286578688&e)>>>23},e.getClassNameFromMetadata=function(e){var t=this.getForeground(e),n="mtk"+t,i=this.getFontStyle(e);return 1&i&&(n+=" mtki"),2&i&&(n+=" mtkb"),4&i&&(n+=" mtku"),n},e.getInlineStyleFromMetadata=function(e,t){var n=this.getForeground(e),i=this.getFontStyle(e),r="color: "+t[n]+";";return 1&i&&(r+="font-style: italic;"),2&i&&(r+="font-weight: bold;"),4&i&&(r+="text-decoration: underline;"),r},e}(),d=function(){var e=Object.create(null);return e[0]="method",e[1]="function",e[2]="constructor",e[3]="field",e[4]="variable",e[5]="class",e[6]="struct",e[7]="interface",e[8]="module",e[9]="property",e[10]="event",e[11]="operator",e[12]="unit",e[13]="value",e[14]="constant",e[15]="enum",e[16]="enum-member",e[17]="keyword",e[25]="snippet",e[18]="text",e[19]="color",e[20]="file",e[21]="reference",e[22]="customcolor",e[23]="folder",e[24]="type-parameter",function(t){return e[t]||"property"}}(),p=function(){var e=Object.create(null);return e["method"]=0,e["function"]=1,e["constructor"]=2,e["field"]=3,e["variable"]=4,e["class"]=5,e["struct"]=6,e["interface"]=7,e["module"]=8,e["property"]=9,e["event"]=10,e["operator"]=11,e["unit"]=12,e["value"]=13,e["constant"]=14,e["enum"]=15,e["enum-member"]=16,e["enumMember"]=16,e["keyword"]=17,e["snippet"]=25,e["text"]=18,e["color"]=19,e["file"]=20,e["reference"]=21,e["customcolor"]=22,e["folder"]=23,e["type-parameter"]=24,e["typeParameter"]=24,function(t,n){var i=e[t];return"undefined"!==typeof i||n||(i=9),i}}();function g(e){return e&&a["a"].isUri(e.uri)&&u["a"].isIRange(e.range)&&(u["a"].isIRange(e.originSelectionRange)||u["a"].isIRange(e.targetSelectionRange))}(function(e){e[e["Invoke"]=1]="Invoke",e[e["TriggerCharacter"]=2]="TriggerCharacter",e[e["ContentChange"]=3]="ContentChange"})(i||(i={})),function(e){e[e["Text"]=0]="Text",e[e["Read"]=1]="Read",e[e["Write"]=2]="Write"}(r||(r={})),function(e){var t=new Map;t.set("file",0),t.set("module",1),t.set("namespace",2),t.set("package",3),t.set("class",4),t.set("method",5),t.set("property",6),t.set("field",7),t.set("constructor",8),t.set("enum",9),t.set("interface",10),t.set("function",11),t.set("variable",12),t.set("constant",13),t.set("string",14),t.set("number",15),t.set("boolean",16),t.set("array",17),t.set("object",18),t.set("key",19),t.set("null",20),t.set("enum-member",21),t.set("struct",22),t.set("event",23),t.set("operator",24),t.set("type-parameter",25);var n=new Map;function i(e){return t.get(e)}function r(e){return n.get(e)}function o(e,t){return"codicon "+(t?"inline":"block")+" codicon-symbol-"+(n.get(e)||"property")}n.set(0,"file"),n.set(1,"module"),n.set(2,"namespace"),n.set(3,"package"),n.set(4,"class"),n.set(5,"method"),n.set(6,"property"),n.set(7,"field"),n.set(8,"constructor"),n.set(9,"enum"),n.set(10,"interface"),n.set(11,"function"),n.set(12,"variable"),n.set(13,"constant"),n.set(14,"string"),n.set(15,"number"),n.set(16,"boolean"),n.set(17,"array"),n.set(18,"object"),n.set(19,"key"),n.set(20,"null"),n.set(21,"enum-member"),n.set(22,"struct"),n.set(23,"event"),n.set(24,"operator"),n.set(25,"type-parameter"),e.fromString=i,e.toString=r,e.toCssClassName=o}(o||(o={}));var _,v,m=function(){function e(e){this.value=e}return e.Comment=new e("comment"),e.Imports=new e("imports"),e.Region=new e("region"),e}();(function(e){function t(e){return Object(s["i"])(e)&&(Boolean(e.newUri)||Boolean(e.oldUri))}e.is=t})(_||(_={})),function(e){function t(e){return Object(s["i"])(e)&&a["a"].isUri(e.resource)&&Object(s["i"])(e.edit)}e.is=t}(v||(v={}));var L=new h["a"],b=new h["a"],C=new h["a"],I=new h["a"],y=new h["a"],N=new h["a"],k=new h["a"],w=new h["a"],S=new h["a"],x=new h["a"],E=new h["a"],O=new h["a"],T=new h["a"],D=new h["a"],M=new h["a"],A=new h["a"],R=new h["a"],z=new h["a"],B=new h["a"],F=new h["a"],V=new h["a"],P=new h["a"],W=new f["a"]},d093:function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"a",(function(){return o})),n.d(t,"c",(function(){return s})),n.d(t,"d",(function(){return h}));var i="`~!@#$%^&*()-=+[{]}\\|;:'\",.<>/?";function r(e){void 0===e&&(e="");for(var t="(-?\\d*\\.\\d\\w*)|([^",n=0,r=i;n<r.length;n++){var o=r[n];e.indexOf(o)>=0||(t+="\\"+o)}return t+="\\s]+)",new RegExp(t,"g")}var o=r();function s(e){var t=o;if(e&&e instanceof RegExp)if(e.global)t=e;else{var n="g";e.ignoreCase&&(n+="i"),e.multiline&&(n+="m"),e.unicode&&(n+="u"),t=new RegExp(e.source,n)}return t.lastIndex=0,t}function a(e,t,n,i){var r,o=e-1-i,s=n.lastIndexOf(" ",o-1)+1;t.lastIndex=s;while(r=t.exec(n)){var a=r.index||0;if(a<=o&&t.lastIndex>=o)return{word:r[0],startColumn:i+1+a,endColumn:i+1+t.lastIndex}}return null}function u(e,t,n,i){var r,o=e-1-i;t.lastIndex=0;while(r=t.exec(n)){var s=r.index||0;if(s>o)return null;if(t.lastIndex>=o)return{word:r[0],startColumn:i+1+s,endColumn:i+1+t.lastIndex}}return null}function h(e,t,n,i){t.lastIndex=0;var r=t.exec(n);if(!r)return null;var o=r[0].indexOf(" ")>=0?u(e,t,n,i):a(e,t,n,i);return t.lastIndex=0,o}}}]);