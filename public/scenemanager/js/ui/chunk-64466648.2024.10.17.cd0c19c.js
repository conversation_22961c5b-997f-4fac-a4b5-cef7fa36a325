(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-64466648"],{"3cfa":function(e,t,n){"use strict";n.r(t);var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"main-menu"},e._l(e.mainMenuList,(function(t){return n("div",{key:t.label,staticClass:"menu-item",class:{active:e.selectedMenu==t.label&&t.needActive},on:{click:function(n){return e.menuClick(t)}}},[n("CommonSVG",{attrs:{"icon-class":t.icon}}),n("span",[e._v(e._s(t.title))])],1)})),0)},a=[],c=(n("d3b7"),n("3ca3"),n("ddb0"),{name:"SceneManageMenu",components:{CommonSVG:function(){return n.e("chunk-51f90655").then(n.bind(null,"17d0"))}},data:function(){return{mainMenuList:[{title:this.$t("sceneMainMenu.frontView.name"),icon:"tb_view",label:"home"},{title:this.$t("topToolBarMenu.advanced.children.viewpoint.name"),icon:"view_advanced",label:"viewpoint"},{title:this.$t("topToolBarMenu.advanced.children.markup.name"),icon:"comment_advanced",label:"markup"},{title:this.$t("sceneMainMenu.roam.name"),icon:"rocker",label:"rocker",needActive:!0},{title:this.$t("featureDatas.skybox.name"),icon:"skybox_element",label:"skybox"},{title:this.$t("menuIconName.reset"),icon:"tb_remove",label:"reset"}],selectedMenu:""}},methods:{menuClick:function(e){switch(this.selectedMenu==e.label?this.selectedMenu="":e.needActive&&(this.selectedMenu=e.label),e.label){case"home":window.scene.resetCamera();break;case"viewpoint":this.$emit("toggleViewpoint",0);break;case"markup":this.$emit("toggleMarkup",0);break;case"views":this.$emit("toggleViews",0);break;case"rocker":"rocker"==this.selectedMenu?this.mainMenuList[3].icon="rocker_default":this.mainMenuList[3].icon="rocker",this.$emit("toggleRocker");break;case"reset":window.scene.execute("reset");break;case"skybox":this.$emit("toggleSkybox",0);break}}}}),s=c,o=(n("4f36"),n("2877")),l=Object(o["a"])(s,i,a,!1,null,"565ec910",null);t["default"]=l.exports},"4f36":function(e,t,n){"use strict";n("7864")},7864:function(e,t,n){}}]);