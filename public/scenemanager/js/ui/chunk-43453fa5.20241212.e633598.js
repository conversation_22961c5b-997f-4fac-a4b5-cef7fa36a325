(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-43453fa5"],{"12f2":function(e,t,n){},"2bed":function(e,t,n){"use strict";n("12f2")},"3f7f":function(e,t,n){"use strict";n.r(t);var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"bottom-menu-container",class:{"first-story":"model"===e.elementMenu}},[e._l(e.menuList,(function(t,i){return[t.show?n("div",{key:t.label,staticClass:"menu-item cursor-btn",class:{active:0===i&&e.sectionDialog||3===i&&e.boxTransForm||"V"==e.rollerDirection&&2==i||"H"==e.rollerDirection&&1==i},on:{click:function(t){return e.toggleMenu(i)}}},[n("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:t.name,placement:"top"}},[n("span",[n("CommonSVG",{attrs:{color:"#FFFFFF","icon-class":t.icon,size:16}})],1)])],1):e._e(),"xuanzhuan"===t.label?[n("transition",{attrs:{name:"sliderFade"}},[n("div",{directives:[{name:"show",rawName:"v-show",value:e.boxTransForm,expression:"boxTransForm"}],staticClass:"item-slider"},[n("el-slider",{staticStyle:{width:"100%"},attrs:{min:t.min,max:t.max,step:t.step,"format-tooltip":e.sliderTooltip},on:{input:function(t){e.setRatio(t)}},model:{value:e.ratio,callback:function(t){e.ratio=t},expression:"ratio"}})],1)])]:e._e()]})),e.sectionDialog?n("dialogComp",{attrs:{hideHeader:!1,needClose:!0,backgroundColor:"var(--primary-bg-color)",zIndex:"5",draw:!0,left:400,drag:!0,title:e.$t("sceneMainMenu.analysis.extend.roller.label"),icon:"icon-details",width:273,height:280,type:"detailInfo",position:"absolute",bottom:45},on:{close:e.closeDialog},scopedSlots:e._u([{key:"center",fn:function(){return[n("div",{staticClass:"content"},[e.dialogList.length?n("div",{staticClass:"list-content"},e._l(e.dialogList,(function(t){return n("div",{key:t.id,staticClass:"item cursor-btn",class:{active:e.selectedList.includes(t.id)},on:{click:function(n){return e.selectItem(t)}}},[e._v(" "+e._s(t.name)+" ")])})),0):e._e(),0===e.dialogList.length?n("div",{staticClass:"no-data"},[e._v(" "+e._s(e.$t("others.emptyData"))+" ")]):e._e(),n("div",{staticClass:"btn-box"},[n("span",{staticClass:"btn",on:{click:function(t){return e.submit()}}},[e._v(" "+e._s(e.$t("menuIconName.confirm"))+" ")])])])]},proxy:!0}],null,!1,3877478344)}):e._e(),e.isScrollBar?n("div",{staticClass:"scroll-bar",class:{horizontal:"V"===e.rollerDirection},style:e.styleStr,on:{mousedown:function(t){return e.startRoller()}}},[n("svg",{staticClass:"scroll-bar-svg",attrs:{width:"100",height:"100",viewBox:"0 0 100 100"}},[n("polygon",{staticStyle:{fill:"#DADFE6"},attrs:{points:"0 50 37 68 37 32 0 50"}}),n("polygon",{staticStyle:{fill:"#DADFE6"},attrs:{points:"100 50 64 32 64 68 100 50"}})])]):e._e()],2)},o=[],s=(n("d3b7"),n("3ca3"),n("ddb0"),n("159b"),n("caad"),n("b0c0"),n("a434"),{name:"RollerMenu",components:{CommonSVG:function(){return n.e("chunk-51f90655").then(n.bind(null,"17d0"))}},props:["compKey"],data:function(){return{menuList:[{name:this.$t("sceneMainMenu.analysis.extend.roller.label"),label:"object",icon:"list",show:!0},{name:this.$t("sceneMainMenu.analysis.extend.roller.label1"),label:"portrait",icon:"portrait",show:!0},{name:this.$t("sceneMainMenu.analysis.extend.roller.label2"),label:"horizontal",icon:"horizontal_feature",show:!0},{name:this.$t("sceneMainMenu.analysis.extend.roller.label3"),label:"xuanzhuan",icon:"proportion_feature",show:!0,min:0,max:100,step:.5},{name:this.$t("menuIconName.exit"),label:"quit",icon:"quit",show:!0}],activeMenu:"",dialogList:[],selectedList:[],boxTransForm:!1,sectionDialog:!1,ratio:50,rollerDirection:"H",styleStr:"left:50%",active:!1,isScrollBar:!1}},computed:{sectionValue:function(){return this.$store.state.menuList.sectionValue},sectionSelectData:function(){return this.$store.state.menuList.sectionSelectData},sectionRandom:function(){return this.$store.state.menuList.sectionRandom},selectedSectionData:function(){return this.$store.state.menuList.selectedSectionData},elementMenu:function(){return this.$store.state.menuList.elementMenu}},created:function(){this.getDialogData(),this.sectionDialog=!0,this.activeMenu=0,window.scene.clearSelection(),window.scene.render()},watch:{sectionRandom:{deep:!0,handler:function(){this.getDialogData()}},compKey:{deep:!0,handler:function(){"model"===this.compKey.type&&this.getDialogData()}}},methods:{sliderTooltip:function(e){return parseInt(e)},startRoller:function(){var e=this,t=this;this.active=!0,document.querySelector("#renderDom").style.pointerEvents="none",document.onmousemove=function(n){if(t.active){var i=null,o=null,s="";"H"===t.rollerDirection?(o=document.documentElement.clientWidth,i=n.clientX,s=parseFloat((i+15)/o*100),e.styleStr="left:".concat(i+15,"px")):(o=document.documentElement.clientHeight,i=n.clientY,s=parseFloat((i+15)/o*100),e.styleStr="top:".concat(i+15,"px")),t.ratio=parseFloat(s),window.scene.config.rollerRatio=s/100}},document.onmouseup=function(){t.endRoller()}},endRoller:function(){document.onmousemove=null,this.active=!1,document.querySelector("#renderDom").style.pointerEvents=""},getDialogData:function(){var e=this;this.dialogList=[];var t=window.scene.features,n=["model","_3dTiles","dem"];t.forEach((function(t){n.includes(t.type)&&e.dialogList.push({id:t.id,name:t.name})}))},setRatio:function(e){var t=e/100;window.scene.config.rollerRatio=t;var n=0;"H"===this.rollerDirection?(n=document.documentElement.clientWidth,this.styleStr="left:".concat(t*n,"px")):(n=document.documentElement.clientHeight,this.styleStr="top:".concat(t*n,"px")),window.scene.render()},toggleMenu:function(e){switch(e){case 0:this.sectionDialog=!this.sectionDialog;break;case 1:this.rollerDirection="H",this.styleStr="left:50%",window.scene.config.rollerDirection=this.rollerDirection,this.ratio=50,window.scene.config.rollerRatio=.5,window.scene.render();break;case 2:this.rollerDirection="V",this.styleStr="top:50%",window.scene.config.rollerDirection=this.rollerDirection,this.ratio=50,window.scene.config.rollerRatio=.5,window.scene.render();break;case 3:this.ratio=100*window.scene.config.rollerRatio,this.boxTransForm=!this.boxTransForm;break;case 4:this.exit(),this.$store.commit("toggleBottomMenuActive","analysis"),this.$store.commit("toogleBottomChildMenuActive","roller");break}e<this.menuList.length&&(this.activeMenu=e)},selectItem:function(e){var t=e.id;if(-1===this.selectedList.indexOf(t))this.selectedList.push(t);else{var n=this.selectedList.indexOf(t);this.selectedList.splice(n,1)}},submit:function(){var e=this;0!==this.selectedList.length?(window.scene.features.forEach((function(e){e.roller=!1})),this.selectedList.forEach((function(t){var n=window.scene.features.get(t);window.scene.config.rollerRatio=e.ratio/100,window.scene.config.rollerDirection=e.rollerDirection,n.roller=!0})),this.isScrollBar=!0,window.scene.render(),this.closeDialog()):this.$message(this.$t("sceneMainMenu.analysis.extend.roller.message"))},closeDialog:function(){this.sectionDialog=!1},exit:function(){window.scene.config.rollerRatio=1,window.scene.config.rollerDirection="H",window.scene.features.forEach((function(e){e.roller=!1})),window.scene.mv.status.selectable=!0,window.scene.render()}},beforeDestroy:function(){this.exit(),document.onmouseup=null}}),l=s,r=(n("2bed"),n("2877")),a=Object(r["a"])(l,i,o,!1,null,"fd3450b8",null);t["default"]=a.exports}}]);