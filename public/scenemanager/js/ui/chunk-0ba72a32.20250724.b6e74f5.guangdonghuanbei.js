(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0ba72a32"],{"05c2":function(e,t,i){e.exports=i.p+"static/img/meta-video.6f2f6a7d.png"},"129f":function(e,t){e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!=e&&t!=t}},"2f13":function(e,t,i){e.exports=i.p+"static/img/meta-3DTiles.19631555.png"},"31f5":function(e,t,i){"use strict";i("45ea")},"45ea":function(e,t,i){},"699c":function(e,t,i){},"7ecc":function(e,t,i){e.exports=i.p+"static/img/meta-dem.417f07c2.png"},"841c":function(e,t,i){"use strict";var s=i("c65b"),a=i("d784"),n=i("825a"),o=i("1d80"),r=i("129f"),l=i("577e"),d=i("dc4a"),c=i("14c3");a("search",(function(e,t,i){return[function(t){var i=o(this),a=void 0==t?void 0:d(t,e);return a?s(a,t,i):new RegExp(t)[e](l(i))},function(e){var s=n(this),a=l(e),o=i(t,s,a);if(o.done)return o.value;var d=s.lastIndex;r(d,0)||(s.lastIndex=0);var h=c(s,a);return r(s.lastIndex,d)||(s.lastIndex=d),null===h?-1:h.index}]}))},aade:function(e,t,i){"use strict";i("b0c0"),i("7db0"),i("d3b7"),i("d81d"),i("b893");t["a"]={methods:{beforeValidate:function(){var e=this.dragData.type;if("objectSetting"==this.dragData.interlock)return!0;if(window.scene.features.has(this.$refs[e].elementDatas.id)&&this.dragData.isDragData)return this.inputError=!0,this.$message.error(this.$t("formRelational.featureID.message")),!1;if("annotation"===e&&""===this.$refs[e].elementDatas.list[3].htmlCode)return this.inputError=!0,this.$message.error(this.$t("featureSetting.data.anchorPoint.message1")),!1;if(this.styleFormDatas.name!==this.$t("featureDatas._3dBuilding.name")&&""==this.styleFormDatas.name)return this.inputError=!0,this.$message.error(this.$t("featureSetting.style.placeholder")),!1;if(this.$refs[e].elementDatas.longitude<=-180||this.$refs[e].elementDatas.longitude>=180)return this.inputError=!0,this.$refs[e].elementDatas.validate.longitude=!1,this.$message.error(this.$t("formRelational.longitude.message")),!1;if(this.$refs[e].elementDatas.latitude<=-90||this.$refs[e].elementDatas.latitude>=90)return this.inputError=!0,this.$refs[e].elementDatas.validate.latitude=!1,this.$message.error(this.$t("formRelational.latitude.message")),!1;var t=this.$refs[e].elementDatas.list.find((function(e){return e.validate}));if(void 0!=t&&""==t.value&&t.optionState)return this.inputError=!0,this.$message.error(this.$t("others.inputName",{name:t.title})),!1;for(var i in this.$refs[e].elementDatas.longitude=this.$refs[e].elementDatas.longitude||0,this.$refs[e].elementDatas.latitude=this.$refs[e].elementDatas.latitude||0,this.$refs[e].elementDatas.altitude=this.$refs[e].elementDatas.altitude||0,this.$refs[e].elementDatas.rotation=this.$refs[e].elementDatas.rotation||0,void 0!=this.$refs[e].offset&&(this.$refs[e].offset=this.$refs[e].offset.map((function(e){return e||0}))),this.inputError=!1,this.$refs[e].validate)!0;return!0},checkStyleLinkValidate:function(e,t){var i=this,s=this.dragData.type;if(""!=t){this.styleFormDatas.datas[s].list[e].validateState=0;var a=new XMLHttpRequest;a.open("GET",t,!0),a.send();a.onload=function(){var t=null;200==a.status?("success",t=1):("error",t=2),i.styleFormDatas.datas[s].list[e].validateState=t},a.onerror=function(){i.styleFormDatas.datas[s].list[e].validateState=2}}else this.styleFormDatas.datas[s].list[e].validateState=null}}}},c14d:function(e,t,i){e.exports=i.p+"static/img/meta-shp.0e65e0a0.png"},cb93:function(e,t,i){"use strict";i.r(t);var s=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("dialogComp",{ref:"linkView",staticClass:"sourceDom",attrs:{hideHeader:!1,needClose:"true",zIndex:"4",width:e.dialogWidth,height:e.dialogHeight,drag:!0,draw:!0,left:e.dialogLeft,top:e.TopToolbarHeight,title:e.styleFormDatas.typeName,icon:"icon-details",isSource:!0,type:"detailInfo"},on:{close:e.closeDialog},scopedSlots:e._u([{key:"center",fn:function(){return[s("div",{staticClass:"source-container"},[s("div",{staticClass:"right-content modelContent"},[s("div",{staticClass:"vothing-beta-container"},[s("div",{staticClass:"top-filter"},[s("div",{staticClass:"search"},[s("el-input",{attrs:{placeholder:e.$t("others.inputName1"),size:"mini"},model:{value:e.searchValue,callback:function(t){e.searchValue=t},expression:"searchValue"}},[s("i",{staticClass:"el-input__icon el-icon-search",attrs:{slot:"prefix"},slot:"prefix"})])],1)]),""!=e.searchValue||e.loading?e._e():s("div",{staticClass:"custom-btn",on:{click:function(t){return e.addCustom()}}},[s("img",{attrs:{src:i("e11d")}})]),s("div",{staticClass:"vo-custom-tab",style:""===e.searchValue||e.loading?"":"margin-top: 45px;"},[s("span",{class:"sys"===e.isType?"tabs active":"tabs",on:{click:function(t){return e.getSourceType("sys")}}},[e._v(" "+e._s(e.$t("dialog.materialLibrary.label"))+" ")]),s("span",{class:"my"===e.isType?"tabs active":"tabs",on:{click:function(t){return e.getSourceType("my")}}},[e._v(" "+e._s(e.$t("dialog.materialLibrary.label1"))+" ")])]),s("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"label-content",class:{h100:e.loading},attrs:{"element-loading-spinner":"el-icon-loading","element-loading-background":"rgba(0, 0, 0, 0)"}},e._l(e.filterCurrentDatas,(function(t,i){return s("div",{key:t.title,staticClass:"label-item",on:{click:function(t){return e.setCurIndex(i)}}},[s("div",{class:[{active:e.eq==e.underlayRadio},"thumb"],attrs:{title:t.ElementName},on:{click:function(i){return e.saveLinkInput(t)}}},[s("img",{attrs:{src:e.getImg(t.ElementLogo),alt:t.title}}),s("i",{staticClass:"icon el-icon-plus"})]),s("p",{staticClass:"title"},[e._v(e._s(t.ElementName))])])})),0)])])])]},proxy:!0}])})},a=[],n=i("c7eb"),o=i("1da1"),r=(i("d3b7"),i("159b"),i("ac1f"),i("841c"),i("caad"),i("2532"),i("b0c0"),i("e9c4"),i("a630"),i("3ca3"),i("ddb0"),i("c740"),i("0e1b")),l=i("aade"),d={name:"VothingBetaSource",props:["currentData","vothingElementArr"],mixins:[r["a"],l["a"]],components:{},data:function(){return{linkViewLoading:null,TopToolbarHeight:122,dialogLeft:0,dialogHeight:400,dialogWidth:300,searchValue:"",total:0,loading:!1,choosedItem:{},labelDatas:[],isType:"sys",curIndex:"",currentFreeSketch:""}},computed:{dragData:function(){return this.$store.state.scene.dragOverData},projectId:function(){return window.localStorage.getItem("cj-projectId")},filterCurrentDatas:function(){var e=[],t=this.searchValue.toLowerCase();return this.labelDatas.forEach((function(i){var s=i.ElementName.toLowerCase();-1!=s.search(t)&&e.push(i)})),e}},created:function(){var e=document.querySelector(".sceneManageDom").style.left;parseInt(e)<0?this.dialogLeft=30:this.dialogLeft=335,this.initEditDatas(this.dragData.type),this.styleFormDatas.typeName=this.dragData.title},beforeDestroy:function(){},watch:{"dragData.title":function(){this.styleFormDatas.typeName=this.dragData.title,this.initEditDatas(this.dragData.type)}},methods:{addCustom:function(){var e=this.$store.state.dialog.activeDialog;e.includes("ElementLink")||(this.curIndex="",this.$store.commit("toggleActiveDialog","ElementLink"),this.$store.commit("toggleActiveDialog","VothingBeta"))},getTypeName:function(e){var t={dem:"DEM",gltf:"glTF",fbx:"FBX",_3dTiles:"3D Tiles",video:"Video",polygon:"Texture",wmts:"WMTS",wms:"WMS",tms:"TMS",geoJSON:"geoJSON",shp:"SHP",kml:"KML"};return t[e]},setCurIndex:function(e){this.curIndex=e===this.curIndex?"":e},getItems:function(e){this.choosedItem.ElementId===e.ElementId?(this.choosedItem={},this.styleFormDatas.datas[this.dragData.type].list[0].value=""):(this.choosedItem=e,this.styleFormDatas.datas[this.dragData.type].list[0].value=e.ElementValue)},initEditDatas:function(e){var t=this;return Object(o["a"])(Object(n["a"])().mark((function e(){return Object(n["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.loading=!0,t.getSourceType("sys"),t.loading=!1;case 3:case"end":return e.stop()}}),e)})))()},getImg:function(e){if(e)return"http"===e.substring(0,4)?e:window.IP_CONFIG.BASE_URL+e;switch(this.dragData.type){case"dem":return i("7ecc");case"gltf":case"fbx":return i("dc7c");case"_3dTiles":return i("2f13");case"video":return i("05c2");case"wmts":case"wms":case"tms":return i("dec5");case"geoJSON":case"shp":case"kml":return i("c14d")}},getSourceType:function(e){this.isType=e;var t=[];this.vothingElementArr.forEach((function(i){"sys"===e&&i.IsSystem&&t.push(i),"my"!==e||i.IsSystem||t.push(i)})),this.labelDatas=t},initGeoJSONSetting:function(e){var t=this;if(""==e[0].value)return!1;this.linkViewLoading=this.$loading({lock:!0,text:this.$t("messageTips.loadConfiguration"),spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.5)"}),fetch(e[0].value).then((function(e){return e.json()})).then((function(i){if(i.features&&i.features.length>0){var s={useUniquePaint:!0,color:"rgb(255, 50, 0)",opacity:1};i.features.forEach((function(e){e.geometry&&"Polygon"==e.geometry.type&&(s.showFill=!0,s.showOutline=!0,s.outlineColor="rgb(255, 50, 0)",s.outlineWidth=2),e.geometry&&"Point"==e.geometry.type&&(s.showPoint=!0,e.properties&&e.properties.name&&(s.showText=!0,s.size=4,s.textSize=12)),e.geometry&&"LineString"==e.geometry.type&&(s.showLine=!0,s.lineWidth=2)})),t.addGeoJSONFeature(e,s,{priority:0}),setTimeout((function(){t.$deepUpdateScene("geoJSON"),t.linkViewLoading.close(),t.closeDialog(),window.scene.render()}),1e3)}else t.$message.error(t.$t("featureSetting.style.geoJSON.message")),t.linkViewLoading.close()})).catch((function(e){t.$message.error(t.$t("featureSetting.style.geoJSON.message1")),t.linkViewLoading.close()}))},closeDialog:function(){var e=this.$store.state.dialog.activeDialog;e.includes("VothingBeta")&&this.$store.commit("toggleActiveDialog","VothingBeta")},cancelLinkInput:function(){this.closeDialog(),this.$store.commit("setActivedType","")},addLinkViewLoading:function(){this.linkViewLoading=this.$loading({lock:!0,text:this.$t("others.featurePrepare"),spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.5)"})},saveLinkInput:function(e){var t=this;if(this.choosedItem=e,"http"===e.ElementValue.substring(0,4)||"mapbox://"===e.ElementValue.substring(0,9)?this.styleFormDatas.datas[this.dragData.type].list[0].value=e.ElementValue:this.styleFormDatas.datas[this.dragData.type].list[0].value=window.IP_CONFIG.BASE_URL+e.ElementValue,this.labelDatas.length>0&&"{}"===JSON.stringify(this.choosedItem))this.$message.error(this.$t("featureSetting.vothing.message"));else{if(0===this.labelDatas.length&&""==this.styleFormDatas.datas[this.dragData.type].list[0].value)return this.inputError=!0,void this.$message.error(this.$t("messageTips.errorAddress"));var i=this.dragData.type,s=this.styleFormDatas.datas[i].list;switch(i){case"dem":this.addLinkViewLoading(),this.addDemFeature(s,{priority:0});break;case"_3dTiles":this.addLinkViewLoading(),this.add3DTilesFeature(s,{},{priority:0});break;case"fbx":case"gltf":return void this.addGltfFbxFeature();case"panorama":return void this.checkedCoordinateAddFeature();case"video":return void this.freeSketchPolygon("rectangle");case"polygon":return void this.freeSketchPolygon("polygon");case"vectorextrude":var a={top:10,base:0,opacity:1,color:"rgb(255, 50, 0)",useUniquePaint:!0};this.addLinkViewLoading(),this.addVectorExtrudeFeature(s,a,{priority:0});break;case"wmts":case"wms":case"tms":this.addLinkViewLoading(),this.addwmtsFeature(s,{priority:0});break;case"shp":var n={color:"rgb(255, 50, 0)",opacity:1,height:1,lineWidth:2,useUniquePaint:!0};this.addLinkViewLoading(),this.addSHPFeature(s,n,{priority:0});break;case"kml":this.addLinkViewLoading(),this.addKMLFeature(s,{priority:0});break;case"geoJSON":return void this.initGeoJSONSetting(s)}setTimeout((function(){t.$deepUpdateScene(i),t.linkViewLoading.close(),t.closeDialog(),window.scene.render()}),1e3),this.$store.commit("setActivedType","")}},onCheckedCoordinate:function(e){var t=[0,0,0],i=window.scene.mv._THREE,s=this.dragData.type,a=this.styleFormDatas.datas[s].list[0].addType;try{var n=window.scene.queryPosition(new i.Vector2(e.clientX,e.clientY));""!=n&&void 0!=n&&(t=window.scene.mv.tools.coordinate.vector2mercator(n))}catch(o){}this.styleFormDatas.longitude=t[0],this.styleFormDatas.latitude=t[1],this.styleFormDatas.altitude=t[2],a||(window.scene.mv.status.selectable=!0,window.scene.mv.events.singleSelection.off("default",this.onCheckedCoordinate)),this.submitSetting()},checkedCoordinateAddFeature:function(){this.$refs.linkView.$el.style.visibility="hidden",window.scene.mv.status.selectable=!1,this.$message(this.$t("messageTips.publishSomething",{name:this.dragData.title})),window.scene.mv.events.singleSelection.on("default",this.onCheckedCoordinate)},addGltfFbxFeature:function(){this.$refs.linkView.$el.style.visibility="hidden",window.scene.mv.status.selectable=!1;var e=this.dragData.type,t=this.styleFormDatas.datas[e].list[0].addType;t?(this.$message(this.$t("messageTips.continuousAddition")),window.scene.mv.events.contextmenu.on("default",this.offCheckedCoordinate),window["offCheckedCoordinate"]=this.offCheckedCoordinate):this.$message(this.$t("messageTips.publishSomething",{name:this.dragData.title})),window.scene.mv.events.singleSelection.on("default",this.onCheckedCoordinate)},offCheckedCoordinate:function(){window.scene.mv.events.singleSelection.off("default",this.onCheckedCoordinate),window.scene.mv.events.contextmenu.off("default",this.offCheckedCoordinate),this.$message(this.$t("messageTips.exitContinuousAddition")),this.$store.commit("setActivedType",""),window.scene.mv.status.selectable=!0,window.offCheckedCoordinate&&(window.offCheckedCoordinate=null,delete window.offCheckedCoordinate)},submitSetting:function(){var e=this;this.linkViewLoading=this.$loading({lock:!0,text:this.$t("others.featurePrepare"),spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.5)"});var t=this.dragData.type,i=this.styleFormDatas.datas[t].list,s=this.styleFormDatas.datas[t].list[0].addType;switch(t){case"fbx":case"gltf":this.addGltfOrFbxFeature(i,{priority:0},s);break;case"panorama":this.addPanoramaFeature(i,{priority:0});break;case"video":this.addVideoFeature(i,{priority:0});break;case"polygon":var a={color:"rgb(255, 50, 0)",opacity:1,top:0,base:0,interval:100,direction:"x",showline:!1};this.addPolygonFeature(i,a,{priority:0});break}this.currentFreeSketch="",setTimeout((function(){e.$deepUpdateScene(t),e.linkViewLoading.close(),e.closeDialog(),window.scene.render()}),1e3)},freeSketchPolygon:function(e){var t=Array.from(window.scene.features.values()),i=t.findIndex((function(e){return"model"==e.type||"underlay"==e.type}));if(-1==i)return this.$message({message:this.$t("messageTips.freeSketch.errorMsg"),type:"warning",showClose:!0,duration:8e3}),this.$store.commit("setActivedType",""),!1;window.scene.mv.tools.draw.active(),this.$refs.linkView.$el.style.visibility="hidden",this.$store.commit("toogleDrawState",!0),"polygon"==e?window.scene.mv.tools.draw.startDrawPolygon():"polyline"==e?window.scene.mv.tools.draw.startDrawLine():"rectangle"==e&&window.scene.mv.tools.draw.startDrawPolygon(),this.$message({showClose:!0,message:this.$t("messageTips.freeSketch.defaultMsg"),duration:7e3}),this.currentFreeSketch=e,"rectangle"==e?window.scene.mv.tools.draw.finishDrawEvent.on("default",this.handleFreeSketchRectangle):window.scene.mv.tools.draw.finishDrawEvent.on("default",this.handleFreeSketchPolygon),document.addEventListener("keyup",this.onEscKeyUp)},checkUrl:function(e){this.styleFormDatas.datas[this.dragData.type].list[0].value=e.url},handleFreeSketchRectangle:function(){var e=window.scene.mv.tools.draw.getAllData();if(e.polygon.length>0){var t=e.polygon[0].points.length;if(t-1<=3)return this.$message.error(this.$t("messageTips.freeSketch.errorMsg3")),!1;for(var i=0;i<t-1;i++){var s=window.scene.mv.tools.coordinate.vector2mercator(e.polygon[0].points[i]);this.styleFormDatas.datas[this.dragData.type].list[1].cor[i]&&(this.styleFormDatas.datas[this.dragData.type].list[1].cor[i].position.x=s[0],this.styleFormDatas.datas[this.dragData.type].list[1].cor[i].position.y=s[1])}}window.scene.mv.tools.draw.deactive(),window.scene.mv.tools.draw.finishDrawEvent.off("default",this.handleFreeSketchRectangle),document.removeEventListener("keyup",this.onEscKeyUp),this.submitSetting()},handleFreeSketchPolygon:function(){var e=window.scene.mv.tools.draw.getAllData(),t=this.dragData.type,i=this.styleFormDatas.datas[t].list;if(this.$store.commit("toogleDrawState",!1),e.polygon.length>0){var s=e.polygon[0].points.length;if(s-1<=2)this.$message.error(this.$t("messageTips.freeSketch.errorMsg1"));else{for(var a=[],n=0;n<s-1;n++)a[n]=[e.polygon[0].points[n].x,e.polygon[0].points[n].y];i[1].position=a,this.submitSetting()}}else{var o=e.polyline[0].points.length;if(o<=1)this.$message.error(this.$t("messageTips.freeSketch.errorMsg2"));else{for(var r=[],l=0;l<o;l++)r[l]=[e.polyline[0].points[l].x,e.polyline[0].points[l].y,e.polyline[0].points[l].z];i[1].position=r,this.submitSetting()}}window.scene.mv.tools.draw.deactive(),window.scene.mv.tools.draw.finishDrawEvent.off("default",this.handleFreeSketchPolygon),document.removeEventListener("keyup",this.onEscKeyUp)},onEscKeyUp:function(e){27==e.keyCode&&"Escape"===e.key&&(window.scene.mv.tools.draw.deactive(),this.$refs.linkView.$el.style.visibility="visible",this.$store.commit("toogleDrawState",!1),"rectangle"==this.currentFreeSketch?window.scene.mv.tools.draw.finishDrawEvent.off("default",this.handleFreeSketchRectangle):window.scene.mv.tools.draw.finishDrawEvent.off("default",this.handleFreeSketchPolygon),document.removeEventListener("keyup",this.onEscKeyUp),this.currentFreeSketch="")},asynchronousListenerTiles:function(){return new Promise((function(e){window.scene.mv.events.featureInited.on("default",(function(){e("featureInited")}))}))}}},c=d,h=(i("31f5"),i("d573"),i("2877")),u=Object(h["a"])(c,s,a,!1,null,"68087a98",null);t["default"]=u.exports},d573:function(e,t,i){"use strict";i("699c")},dc7c:function(e,t,i){e.exports=i.p+"static/img/meta-gltf.c8b3e119.png"},dec5:function(e,t,i){e.exports=i.p+"static/img/meta-wms.7a364751.png"},e11d:function(e,t){e.exports="data:image/png;base64,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"}}]);