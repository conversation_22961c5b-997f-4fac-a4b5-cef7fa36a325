(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d21b334"],{bf82:function(t,e,i){"use strict";var o={};i.r(o),i.d(o,"mixColor",(function(){return m})),i.d(o,"getColorsWithSubjectColor",(function(){return S})),i.d(o,"getColorSetsBySubjectColors",(function(){return w}));var a={};i.r(a),i.d(a,"proccessToFunc",(function(){return U})),i.d(a,"buildTextureData",(function(){return F})),i.d(a,"buildTextureDataWithOneEdgeAttr",(function(){return R})),i.d(a,"buildTextureDataWithTwoEdgeAttr",(function(){return W})),i.d(a,"attributesToTextureData",(function(){return N})),i.d(a,"arrayToTextureData",(function(){return Y})),i.d(a,"radialLayout",(function(){return z}));var n={};i.r(n),i.d(n,"gpuDetector",(function(){return G}));var r=i("9ab4"),s=i("3822"),l=i("97b1"),c=i("1683"),d=i("53c8"),h=i("bfb1"),g=i("e897"),u=i("8937"),f=i("6929"),v=i.n(f),p=i("009a"),m=function(t,e,i){var o=v()(t),a=v()(e);return v()([(1-i)*o.red()+i*a.red(),(1-i)*o.green()+i*a.green(),(1-i)*o.blue()+i*a.blue()]).rgb()},y=function(t,e,i){void 0===e&&(e="#fff"),void 0===i&&(i="rgb(150, 150, 150)");var o=m(e,t,.05).rgb().toString(),a=m(e,t,.1).rgb().toString(),n=m(e,t,.2).rgb().toString(),r=m(e,t,.4).rgb().toString(),s=m(e,i,.02).rgb().toString(),l=m(e,i,.05).rgb().toString(),c=m(e,i,.1).rgb().toString(),d=m(e,i,.2).rgb().toString(),h=m(e,i,.3).rgb().toString(),g=Object(p["a"])(t,{theme:"default",backgroundColor:e}),u=v()(t).hex().toLowerCase(),f=g.indexOf(u),y=t;return-1!==f&&(y=g[f+1]),{mainStroke:t,mainFill:a,activeStroke:t,activeFill:o,inactiveStroke:r,inactiveFill:o,selectedStroke:t,selectedFill:e,highlightStroke:y,highlightFill:n,disableStroke:h,disableFill:l,edgeMainStroke:h,edgeActiveStroke:t,edgeInactiveStroke:d,edgeSelectedStroke:t,edgeHighlightStroke:t,edgeDisableStroke:c,comboMainStroke:h,comboMainFill:s,comboActiveStroke:t,comboActiveFill:o,comboInactiveStroke:h,comboInactiveFill:s,comboSelectedStroke:t,comboSelectedFill:s,comboHighlightStroke:y,comboHighlightFill:s,comboDisableStroke:d,comboDisableFill:l}},b=function(t,e,i){void 0===e&&(e="#fff"),void 0===i&&(i="#777");var o=m(e,t,.2).rgb().toString(),a=m(e,t,.3).rgb().toString(),n=m(e,t,.6).rgb().toString(),r=m(e,t,.8).rgb().toString(),s=m(e,i,.2).rgb().toString(),l=m(e,i,.25).rgb().toString(),c=m(e,i,.3).rgb().toString(),d=m(e,i,.4).rgb().toString(),h=m(e,i,.5).rgb().toString(),g=Object(p["a"])(t,{theme:"dark",backgroundColor:e}),u=v()(t).hex().toLowerCase(),f=g.indexOf(u),y=t;return-1!==f&&(y=g[f+1]),{mainStroke:r,mainFill:o,activeStroke:t,activeFill:a,inactiveStroke:r,inactiveFill:o,selectedStroke:t,selectedFill:o,highlightStroke:t,highlightFill:n,disableStroke:h,disableFill:l,edgeMainStroke:i,edgeActiveStroke:t,edgeInactiveStroke:i,edgeSelectedStroke:t,edgeHighlightStroke:t,edgeDisableStroke:c,comboMainStroke:d,comboMainFill:l,comboActiveStroke:t,comboActiveFill:s,comboInactiveStroke:d,comboInactiveFill:l,comboSelectedStroke:t,comboSelectedFill:s,comboHighlightStroke:y,comboHighlightFill:l,comboDisableStroke:d,comboDisableFill:s}},S=function(t,e,i,o){return void 0===e&&(e="#fff"),void 0===i&&(i="default"),void 0===o&&(o="rgb(150, 150, 150)"),"default"===i?y(t,e,"rgb(150, 150, 150)"):b(t,e,"#777")},w=function(t,e,i,o){void 0===e&&(e="#fff"),void 0===i&&(i="default"),void 0===o&&(o="rgb(150, 150, 150)");var a=[];return t.forEach((function(t){a.push(S(t,e,i,o))})),a},x="rgb(95, 149, 255)",k="rgb(255, 255, 255)",C="rgb(0, 0, 0)",E=S(x,k),L={version:"0.6.15",rootContainerClassName:"root-container",nodeContainerClassName:"node-container",edgeContainerClassName:"edge-container",comboContainerClassName:"combo-container",delegateContainerClassName:"delegate-container",defaultLoopPosition:"top",nodeLabel:{style:{fill:"#000",fontSize:12,textAlign:"center",textBaseline:"middle"},offset:4},defaultNode:{type:"circle",style:{lineWidth:1,stroke:E.mainStroke,fill:E.mainFill},size:20,color:E.mainStroke,linkPoints:{size:8,lineWidth:1,fill:E.activeFill,stroke:E.activeStroke}},nodeStateStyles:{active:{fill:E.activeFill,stroke:E.activeStroke,lineWidth:2,shadowColor:E.mainStroke,shadowBlur:10},selected:{fill:E.selectedFill,stroke:E.selectedStroke,lineWidth:4,shadowColor:E.selectedStroke,shadowBlur:10,"text-shape":{fontWeight:500}},highlight:{fill:E.highlightFill,stroke:E.highlightStroke,lineWidth:2,"text-shape":{fontWeight:500}},inactive:{fill:E.inactiveFill,stroke:E.inactiveStroke,lineWidth:1},disable:{fill:E.disableFill,stroke:E.disableStroke,lineWidth:1}},edgeLabel:{style:{fill:C,textAlign:"center",textBaseline:"middle",fontSize:12}},defaultEdge:{type:"line",size:1,style:{stroke:E.edgeMainStroke,lineAppendWidth:2},color:E.edgeMainStroke},edgeStateStyles:{active:{stroke:E.edgeActiveStroke,lineWidth:1},selected:{stroke:E.edgeSelectedStroke,lineWidth:2,shadowColor:E.edgeSelectedStroke,shadowBlur:10,"text-shape":{fontWeight:500}},highlight:{stroke:E.edgeHighlightStroke,lineWidth:2,"text-shape":{fontWeight:500}},inactive:{stroke:E.edgeInactiveStroke,lineWidth:1},disable:{stroke:E.edgeDisableStroke,lineWidth:1}},comboLabel:{style:{fill:C,textBaseline:"middle",fontSize:12},refY:10,refX:10},defaultCombo:{type:"circle",style:{fill:E.comboMainFill,lineWidth:1,stroke:E.comboMainStroke,r:5,width:20,height:10},size:[20,5],color:E.comboMainStroke,padding:[25,20,15,20]},comboStateStyles:{active:{stroke:E.comboActiveStroke,lineWidth:1,fill:E.comboActiveFill},selected:{stroke:E.comboSelectedStroke,lineWidth:2,fill:E.comboSelectedFill,shadowColor:E.comboSelectedStroke,shadowBlur:10,"text-shape":{fontWeight:500}},highlight:{stroke:E.comboHighlightStroke,lineWidth:2,fill:E.comboHighlightFill,"text-shape":{fontWeight:500}},inactive:{stroke:E.comboInactiveStroke,fill:E.comboInactiveFill,lineWidth:1},disable:{stroke:E.comboDisableStroke,fill:E.comboDisableFill,lineWidth:1}},delegateStyle:{fill:"#F3F9FF",fillOpacity:.5,stroke:"#1890FF",strokeOpacity:.9,lineDash:[5,5]},textWaterMarkerConfig:{width:150,height:100,compatible:!1,text:{x:0,y:60,lineHeight:20,rotate:20,fontSize:14,fontFamily:"Microsoft YaHei",fill:"rgba(0, 0, 0, 0.1)",baseline:"Middle"}},imageWaterMarkerConfig:{width:150,height:130,compatible:!1,image:{x:0,y:0,width:30,height:20,rotate:0}},waterMarkerImage:"https://gw.alipayobjects.com/os/s/prod/antv/assets/image/logo-with-text-73b8a.svg"},D=i("7c8a"),I=s["h"].cloneEvent,M=s["h"].isViewportChanged,O=function(t){function e(e){var i=t.call(this,e)||this;return i.extendEvents=[],i.dragging=!1,i.preItem=null,i.graph=e,i.destroyed=!1,i.initEvents(),i}return Object(r["c"])(e,t),e.prototype.initEvents=function(){var t=this,e=t.graph,i=t.extendEvents,o=void 0===i?[]:i,a=e.get("canvas"),n=a.get("el"),r=Object(u["wrapBehavior"])(this,"onCanvasEvents"),s=Object(u["wrapBehavior"])(this,"onExtendEvents"),l=Object(u["wrapBehavior"])(this,"onWheelEvent");a.off("*").on("*",r),this.canvasHandler=r,o.push(Object(D["a"])(n,"DOMMouseScroll",l)),o.push(Object(D["a"])(n,"mousewheel",l)),"undefined"!==typeof window&&(o.push(Object(D["a"])(window,"keydown",s)),o.push(Object(D["a"])(window,"keyup",s)),o.push(Object(D["a"])(window,"focus",s)))},e.getItemRoot=function(t){while(t&&!t.get("item"))t=t.get("parent");return t},e.prototype.onCanvasEvents=function(t){var i=this.graph,o=i.get("canvas"),a=t.target,n=t.type;t.canvasX=t.x,t.canvasY=t.y;var r={x:t.canvasX,y:t.canvasY},s=i.get("group"),l=s.getMatrix();if(l||(l=[1,0,0,0,1,0,0,0,1]),M(l)&&(r=i.getPointByClient(t.clientX,t.clientY)),t.x=r.x,t.y=r.y,t.currentTarget=i,a===o)return"mousemove"!==n&&"mouseleave"!==n||this.handleMouseMove(t,"canvas"),t.target=o,t.item=null,i.emit(n,t),void i.emit("canvas:".concat(n),t);var c=e.getItemRoot(a);if(c){var d=c.get("item");if(!d.destroyed){var h=d.getType();if(t.target=a,t.item=d,t.canvasX===t.x&&t.canvasY===t.y){var g=i.getCanvasByPoint(t.x,t.y);t.canvasX=g.x,t.canvasY=g.y}i.emit(n,t),t.name&&!t.name.includes(":")?i.emit("".concat(h,":").concat(n),t):i.emit(t.name,t),"dragstart"===n&&(this.dragging=!0),"dragend"===n&&(this.dragging=!1),"mousemove"===n&&this.handleMouseMove(t,h)}}else i.emit(n,t)},e.prototype.onExtendEvents=function(t){this.graph.emit(t.type,t)},e.prototype.onWheelEvent=function(t){Object(u["isNil"])(t.wheelDelta)&&(t.wheelDelta=-t.detail),this.graph.emit("wheel",t)},e.prototype.handleMouseMove=function(t,e){var i=this,o=i.graph,a=i.preItem,n=o.get("canvas"),r=t.target===n?null:t.item;t=I(t),a&&a!==r&&!a.destroyed&&(t.item=a,this.emitCustomEvent(a.getType(),"mouseleave",t),this.dragging&&this.emitCustomEvent(a.getType(),"dragleave",t)),r&&a!==r&&(t.item=r,this.emitCustomEvent(e,"mouseenter",t),this.dragging&&this.emitCustomEvent(e,"dragenter",t)),this.preItem=r},e.prototype.emitCustomEvent=function(t,e,i){i.type=e,this.graph.emit("".concat(t,":").concat(e),i)},e.prototype.destroy=function(){var t=this,e=t.graph,i=t.canvasHandler,o=t.extendEvents,a=e.get("canvas");a.off("*",i),Object(u["each"])(o,(function(t){t.remove()})),this.dragging=!1,this.preItem=null,this.extendEvents.length=0,this.canvasHandler=null,this.destroyed=!0},e}(s["a"]),T=O,P=i("2e4c"),B=i("0519"),A=i.n(B),j=s["h"].traverseTree,U=function(t,e){var i;return i=t?Object(u["isNumber"])(t)?function(e){return t}:t:function(t){return e||1},i},F=function(t,e){var i=[],o=[],a={},n=0;for(n=0;n<t.length;n++){var r=t[n];a[r.id]=n,i.push(r.x),i.push(r.y),i.push(0),i.push(0),o.push([])}for(n=0;n<e.length;n++){var s=e[n];o[a[s.source]].push(a[s.target]),o[a[s.target]].push(a[s.source])}var l=0;for(n=0;n<t.length;n++){var c=i.length,d=o[n],h=d.length;i[4*n+2]=c,i[4*n+3]=d.length,l=Math.max(l,d.length);for(var g=0;g<h;++g){var u=d[g];i.push(+u)}}while(i.length%4!==0)i.push(0);return{array:new Float32Array(i),maxEdgePerVetex:l}},R=function(t,e,i){var o=[],a=[],n={},r=0;for(r=0;r<t.length;r++){var s=t[r];n[s.id]=r,o.push(s.x),o.push(s.y),o.push(0),o.push(0),a.push([])}for(r=0;r<e.length;r++){var l=e[r];a[n[l.source]].push(n[l.target]),a[n[l.source]].push(i(l)),a[n[l.target]].push(n[l.source]),a[n[l.target]].push(i(l))}var c=0;for(r=0;r<t.length;r++){var d=o.length,h=a[r],g=h.length;o[4*r+2]=d,o[4*r+3]=g/2,c=Math.max(c,g/2);for(var u=0;u<g;++u){var f=h[u];o.push(+f)}}while(o.length%4!==0)o.push(0);return{array:new Float32Array(o),maxEdgePerVetex:c}},W=function(t,e,i,o){var a=[],n=[],r={},s=0;for(s=0;s<t.length;s++){var l=t[s];r[l.id]=s,a.push(l.x),a.push(l.y),a.push(0),a.push(0),n.push([])}for(s=0;s<e.length;s++){var c=e[s];n[r[c.source]].push(r[c.target]),n[r[c.source]].push(i(c)),n[r[c.source]].push(o(c)),n[r[c.source]].push(0),n[r[c.target]].push(r[c.source]),n[r[c.target]].push(i(c)),n[r[c.target]].push(o(c)),n[r[c.target]].push(0)}var d=0;for(s=0;s<t.length;s++){var h=a.length,g=n[s],u=g.length;a[4*s+2]=h+1048576*u/4,a[4*s+3]=0,d=Math.max(d,u/4);for(var f=0;f<u;++f){var v=g[f];a.push(+v)}}while(a.length%4!==0)a.push(0);return{array:new Float32Array(a),maxEdgePerVetex:d}},N=function(t,e){var i=[],o=t.length,a={};return e.forEach((function(e){t.forEach((function(t,n){if(void 0===a[e[t]]&&(a[e[t]]=Object.keys(a).length),i.push(a[e[t]]),n===o-1)while(i.length%4!==0)i.push(0)}))})),{array:new Float32Array(i),count:Object.keys(a).length}},Y=function(t){for(var e=[],i=t.length,o=t[0].length,a=function(o){t.forEach((function(t,a){if(e.push(t[o]),a===i-1)while(e.length%4!==0)e.push(0)}))},n=0;n<o;n++)a(n);return new Float32Array(e)},z=function(t,e){var i=["V","TB","BT"],o={x:1/0,y:1/0},a={x:-1/0,y:-1/0},n="x",r="y";e&&i.indexOf(e)>=0&&(r="x",n="y");var s=0;j(t,(function(t){return s++,t.x>a.x&&(a.x=t.x),t.x<o.x&&(o.x=t.x),t.y>a.y&&(a.y=t.y),t.y<o.y&&(o.y=t.y),!0}));var l=2*Math.PI/s,c=a[r]-o[r];return 0===c||j(t,(function(e){var i=(e[r]-o[r])/c*(2*Math.PI-l)+l,a=Math.abs("x"===n?e.x-t.x:e.y-t.y);return e.x=a*Math.cos(i),e.y=a*Math.sin(i),!0})),t},G=function t(){return"undefined"===typeof window||"undefined"===typeof document?{}:{canvas:!!window.CanvasRenderingContext2D,webgl:function(){try{var t=document.createElement("canvas");return!(!window.WebGLRenderingContext||!t.getContext("webgl")&&!t.getContext("experimental-webgl"))}catch(e){return!1}}(),workers:!!window.Worker,fileapi:window.File&&window.FileReader&&window.FileList&&window.Blob,getWebGLErrorMessage:function(){var t=document.createElement("div");return t.id="webgl-error-message",t.style.fontFamily="monospace",t.style.fontSize="13px",t.style.fontWeight="normal",t.style.textAlign="center",t.style.background="#fff",t.style.color="#000",t.style.padding="1.5em",t.style.width="400px",t.style.margin="5em auto 0",this.webgl||(t.innerHTML=window.WebGLRenderingContext?['Your graphics card does not seem to support <a href="http://khronos.org/webgl/wiki/Getting_a_WebGL_Implementation" rel="external nofollow" rel="external nofollow" style="color:#000">WebGL</a>.<br />','Find out how to get it <a href="http://get.webgl.org/" rel="external nofollow" rel="external nofollow" style="color:#000">here</a>.'].join("\n"):['Your browser does not seem to support <a href="http://khronos.org/webgl/wiki/Getting_a_WebGL_Implementation" rel="external nofollow" rel="external nofollow" style="color:#000">WebGL</a>.<br/>','Find out how to get it <a href="http://get.webgl.org/" rel="external nofollow" rel="external nofollow" style="color:#000">here</a>.'].join("\n")),t},addGetWebGLMessage:function(e){e=e||{};var i=void 0!==e.parent?e.parent:document.body,o=void 0!==e.id?e.id:"oldie",a=t().getWebGLErrorMessage();a.id=o,i.appendChild(a)}}},K=Object(r["a"])(Object(r["a"])(Object(r["a"])(Object(r["a"])({},s["h"]),o),a),n),X=K,q=X.radialLayout,V=function(){function t(t){this.type=t.type,this.radial=t.radial,this.config=t}return t.prototype.init=function(t){var e=this;this.data=t,this.radial?this.layoutMethod=function(t){var i=A.a[e.type](t,e.config);return q(i),i}:this.layoutMethod=function(t){return A.a[e.type](t,e.config)}},t.prototype.execute=function(){return this.layoutMethod(this.data,this.config)},t.prototype.layout=function(t){return this.init(t),this.execute()},t}(),_=V;Object(P["registerLayout"])("grid",P["GridLayout"]),Object(P["registerLayout"])("random",P["RandomLayout"]),Object(P["registerLayout"])("force",P["ForceLayout"]),Object(P["registerLayout"])("circular",P["CircularLayout"]),Object(P["registerLayout"])("dagre",P["DagreLayout"]),Object(P["registerLayout"])("dagreCompound",P["DagreCompoundLayout"]),Object(P["registerLayout"])("radial",P["RadialLayout"]),Object(P["registerLayout"])("concentric",P["ConcentricLayout"]),Object(P["registerLayout"])("mds",P["MDSLayout"]),Object(P["registerLayout"])("fruchterman",P["FruchtermanLayout"]),Object(P["registerLayout"])("fruchterman-gpu",P["FruchtermanGPULayout"]),Object(P["registerLayout"])("gForce",P["GForceLayout"]),Object(P["registerLayout"])("gForce-gpu",P["GForceGPULayout"]),Object(P["registerLayout"])("comboForce",P["ComboForceLayout"]),Object(P["registerLayout"])("comboCombined",P["ComboCombinedLayout"]),Object(P["registerLayout"])("forceAtlas2",P["ForceAtlas2Layout"]);var Z=function(t,e){e.isCustomLayout=!0,P["Layouts"][t]=Object(P["registerLayout"])(t,e)},H=function(){function t(t,e){var i=t.toString(),o=new Blob(["importScripts('".concat(e,"');(").concat(i,")()")],{type:"text/javascript"});return new Worker(URL.createObjectURL(o))}return t}(),J=H,Q=function(t){function e(){var t={RUN:"LAYOUT_RUN",END:"LAYOUT_END",ERROR:"LAYOUT_ERROR",TICK:"LAYOUT_TICK",GPURUN:"GPU_LAYOUT_RUN",GPUEND:"GPU_LAYOUT_END"};function e(e){var i=e.data.type;return i===t.RUN||i===t.GPURUN}function i(e){var i=this,o=e.data.type;switch(o){case t.RUN:var a,n=e.data,r=n.nodes,s=n.edges,l=n.layoutCfg,c=void 0===l?{}:l,d=c.type,h=layout.getLayoutByName(d);if(!h){this.postMessage({type:t.ERROR,message:"layout ".concat(d," not found")});break}c.onLayoutEnd=function(){i.postMessage({type:t.END,nodes:r}),null===a||void 0===a||a.destroy()},a=new h(c),a.init({nodes:r,edges:s}),a.execute();break;case t.GPURUN:var g=e.data,u=g.nodes,f=(s=g.edges,g.layoutCfg),v=(c=void 0===f?{}:f,g.canvas);d=c.type,h=layout.getLayoutByName(d);if(!h){this.postMessage({type:t.ERROR,message:"layout ".concat(d," not found")});break}if("gpu"!==d.split("-")[1]){this.postMessage({type:t.ERROR,message:"layout ".concat(d," does not support GPU")});break}var p=new h(c);p.init({nodes:u,edges:s}),p.executeWithWorker(v,this);break;default:break}}layout.registerLayout("grid",layout.GridLayout),layout.registerLayout("random",layout.RandomLayout),layout.registerLayout("force",layout.ForceLayout),layout.registerLayout("circular",layout.CircularLayout),layout.registerLayout("dagre",layout.DagreLayout),layout.registerLayout("dagreCompound",layout.DagreCompoundLayout),layout.registerLayout("radial",layout.RadialLayout),layout.registerLayout("concentric",layout.ConcentricLayout),layout.registerLayout("mds",layout.MDSLayout),layout.registerLayout("fruchterman",layout.FruchtermanLayout),layout.registerLayout("fruchterman-gpu",layout.FruchtermanGPULayout),layout.registerLayout("gForce",layout.GForceLayout),layout.registerLayout("gForce-gpu",layout.GForceGPULayout),layout.registerLayout("comboForce",layout.ComboForceLayout),layout.registerLayout("comboCombined",layout.ComboCombinedLayout),layout.registerLayout("forceAtlas2",layout.ForceAtlas2Layout),onmessage=function(t){e(t)&&i(t)}}void 0===t&&(t="https://unpkg.com/@antv/layout@latest/dist/layout.min.js");var i=new J(e,t);return i},$={RUN:"LAYOUT_RUN",END:"LAYOUT_END",ERROR:"LAYOUT_ERROR",TICK:"LAYOUT_TICK",GPURUN:"GPU_LAYOUT_RUN",GPUEND:"GPU_LAYOUT_END"};function tt(t){return tt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},tt(t)}var et=function(t){return setTimeout(t,16)},it=function(t){return clearTimeout(t)},ot={requestAnimationFrame:function(t){var e="undefined"!==typeof window&&(window.requestAnimationFrame||window.webkitRequestAnimationFrame)||et;return e(t)},cancelAnimationFrame:function(t){var e="undefined"!==typeof window&&(window.cancelAnimationFrame||window.webkitCancelAnimationFrame)||it;return e(t)}},at=["fruchterman","gForce"],nt=["force","grid","circular"],rt=function(t){function e(e){var i=t.call(this,e)||this;return i.graph=e,i.layoutCfg=e.get("layout")||{},i.layoutType=i.getLayoutType(),i.worker=null,i.workerData={},i.initLayout(),i}return Object(r["c"])(e,t),e.prototype.initLayout=function(){},e.prototype.getWorker=function(){return this.worker||("undefined"===typeof Worker?(console.warn("Web worker is not supported in current browser."),this.worker=null):this.worker=Q(this.layoutCfg.workerScriptURL)),this.worker},e.prototype.stopWorker=function(){var t=this.workerData;this.worker&&(this.worker.terminate(),this.worker=null,t.requestId&&(ot.cancelAnimationFrame(t.requestId),t.requestId=null),t.requestId2&&(ot.cancelAnimationFrame(t.requestId2),t.requestId2=null))},e.prototype.execLayoutMethod=function(t,e){var i=this;return new Promise((function(o,a){return Object(r["b"])(i,void 0,void 0,(function(){var i,n,s,l,c,d,h,g,u;return Object(r["d"])(this,(function(r){switch(r.label){case 0:if(i=this.graph,!i||i.get("destroyed"))return[2];n=t.type,t.onLayoutEnd=function(){i.emit("aftersublayout",{type:n}),o()},n&&this.isGPU&&(this.hasGPUVersion(n)?n="".concat(n,"-gpu"):console.warn("The '".concat(n,"' layout does not support GPU calculation for now, it will run in CPU."))),s="force"===n||"g6force"===n||"gForce"===n,s?(l=t.onTick,g=function(){l&&l(),i.refreshPositions()},t.tick=g):"comboForce"!==n&&"comboCombined"!==n||(t.comboTrees=i.get("comboTrees")),c=!1;try{d=new P["Layouts"][n](t)}catch(f){console.warn("The layout method: '".concat(n,"' does not exist! Please specify it first.")),a()}return c=d.enableTick,c&&(h=t.onTick,g=function(){h&&h(),i.refreshPositions()},d.tick=g),u=this.filterLayoutData(this.data,t),ht(u,e),d.init(u),i.emit("beforesublayout",{type:n}),[4,d.execute()];case 1:return r.sent(),d.isCustomLayout&&t.onLayoutEnd&&t.onLayoutEnd(),this.layoutMethods[e]=d,[2]}}))}))}))},e.prototype.updateLayoutMethod=function(t,e){var i=this;return new Promise((function(o,a){return Object(r["b"])(i,void 0,void 0,(function(){var i,a,n;return Object(r["d"])(this,(function(r){switch(r.label){case 0:return i=this.graph,a=null===e||void 0===e?void 0:e.type,e.onLayoutEnd=function(){i.emit("aftersublayout",{type:a}),o()},n=this.filterLayoutData(this.data,e),t.init(n),t.updateCfg(e),i.emit("beforesublayout",{type:a}),[4,t.execute()];case 1:return r.sent(),t.isCustomLayout&&e.onLayoutEnd&&e.onLayoutEnd(),[2]}}))}))}))},e.prototype.layout=function(t){var e=this,i=this.graph;this.data=this.setDataFromGraph();var o=this.data,a=o.nodes,n=o.hiddenNodes;if(!a)return!1;var s=i.get("width"),l=i.get("height"),c={};Object.assign(c,{width:s,height:l,center:[s/2,l/2]},this.layoutCfg),this.layoutCfg=c,this.destoryLayoutMethods(),i.emit("beforelayout"),this.initPositions(c.center,a),this.initPositions(c.center,n);var d=c.type;d&&"gpu"===d.split("-")[1]&&(d=d.split("-")[0],c.gpuEnabled=!0);var h=!1;c.gpuEnabled&&(h=!0,G().webgl||(console.warn("Your browser does not support webGL or GPGPU. The layout will run in CPU."),h=!1)),this.isGPU=h;var g=c.onLayoutEnd,u=c.layoutEndFormatted,f=c.adjust;if(u||(c.layoutEndFormatted=!0,c.onAllLayoutEnd=function(){return Object(r["b"])(e,void 0,void 0,(function(){return Object(r["d"])(this,(function(t){switch(t.label){case 0:return g&&g(),this.refreshLayout(),f&&c.pipes?[4,this.adjustPipesBox(this.data,f)]:[3,2];case 1:t.sent(),this.refreshLayout(),t.label=2;case 2:return i.emit("afterlayout"),[2]}}))}))}),this.stopWorker(),c.workerEnabled&&this.layoutWithWorker(this.data))return!0;var v=Promise.resolve(),p=!1;return c.type?(p=!0,v=v.then((function(){return Object(r["b"])(e,void 0,void 0,(function(){return Object(r["d"])(this,(function(t){switch(t.label){case 0:return[4,this.execLayoutMethod(c,0)];case 1:return[2,t.sent()]}}))}))}))):c.pipes&&(p=!0,c.pipes.forEach((function(t,i){v=v.then((function(){return Object(r["b"])(e,void 0,void 0,(function(){return Object(r["d"])(this,(function(e){switch(e.label){case 0:return[4,this.execLayoutMethod(t,i)];case 1:return[2,e.sent()]}}))}))}))}))),p?v.then((function(){c.onAllLayoutEnd&&c.onAllLayoutEnd(),t&&t()})).catch((function(t){console.warn("graph layout failed,",t)})):(this.refreshLayout(),null===t||void 0===t||t()),!1},e.prototype.layoutWithWorker=function(t){var e=this,i=this,o=i.layoutCfg,a=i.graph,n=this.getWorker(),r=this.workerData;if(!n)return!1;r.requestId=null,r.requestId2=null,r.currentTick=null,r.currentTickData=null,a.emit("beforelayout");var s=Promise.resolve(),l=!1;if(o.type)l=!0,s=s.then((function(){return e.runWebworker(n,t,o)}));else if(o.pipes){l=!0;for(var c=function(i){s=s.then((function(){return e.runWebworker(n,t,i)}))},d=0,h=o.pipes;d<h.length;d++){var g=h[d];c(g)}}return l&&s.then((function(){o.onAllLayoutEnd&&o.onAllLayoutEnd()})).catch((function(t){console.error("layout failed",t)})),!0},e.prototype.runWebworker=function(t,e,i){var o=this,a=this.isGPU,n=this.filterLayoutData(e,i),r=n.nodes,s=n.edges,l=document.createElement("canvas"),c=a&&"undefined"!==typeof window&&window.navigator&&!navigator["gpu"]&&"OffscreenCanvas"in window&&"transferControlToOffscreen"in l,d=ct(i,(function(t){return"function"!==typeof t}));if(c){var h=l.transferControlToOffscreen();d.type="".concat(d.type,"-gpu"),t.postMessage({type:$.GPURUN,nodes:r,edges:s,layoutCfg:d,canvas:h},[h])}else t.postMessage({type:$.RUN,nodes:r,edges:s,layoutCfg:d});return new Promise((function(e,a){t.onmessage=function(t){o.handleWorkerMessage(e,a,t,n,i)}}))},e.prototype.handleWorkerMessage=function(t,e,i,o,a){var n=this,r=n.graph,s=n.workerData,l=i.data,c=l.type,d=function(){a.onTick&&a.onTick()};switch(c){case $.TICK:s.currentTick=l.currentTick,s.currentTickData=l,s.requestId||(s.requestId=ot.requestAnimationFrame((function(){lt(o,l),r.refreshPositions(),d(),l.currentTick===l.totalTicks?t():s.currentTick===l.totalTicks&&(s.requestId2=ot.requestAnimationFrame((function(){lt(o,s.currentTickData),r.refreshPositions(),s.requestId2=null,d(),t()}))),s.requestId=null})));break;case $.END:null==s.currentTick&&(lt(o,l),t());break;case $.GPUEND:null==s.currentTick&&(dt(o,l),t());break;case $.ERROR:console.warn("Web-Worker layout error!",l.message),e();break;default:e();break}},e.prototype.updateLayoutCfg=function(t){var e=this,i=this,o=i.graph,a=i.layoutMethods,n=Object(u["mix"])({},this.layoutCfg,t);if(this.layoutCfg=n,null===a||void 0===a?void 0:a.length){if(this.data=this.setDataFromGraph(),this.stopWorker(),!t.workerEnabled||!this.layoutWithWorker(this.data)){o.emit("beforelayout");var s=Promise.resolve(),l=!1;1===(null===a||void 0===a?void 0:a.length)?(l=!0,s=s.then((function(){return Object(r["b"])(e,void 0,void 0,(function(){return Object(r["d"])(this,(function(t){switch(t.label){case 0:return[4,this.updateLayoutMethod(a[0],n)];case 1:return[2,t.sent()]}}))}))}))):(l=!0,null===a||void 0===a||a.forEach((function(t,i){var o=n.pipes[i];s=s.then((function(){return Object(r["b"])(e,void 0,void 0,(function(){return Object(r["d"])(this,(function(e){switch(e.label){case 0:return[4,this.updateLayoutMethod(t,o)];case 1:return[2,e.sent()]}}))}))}))}))),l&&s.then((function(){n.onAllLayoutEnd&&n.onAllLayoutEnd()})).catch((function(t){console.warn("layout failed",t)}))}}else this.layout()},e.prototype.adjustPipesBox=function(t,e){var i=this;return new Promise((function(o){var a=t.nodes;(null===a||void 0===a?void 0:a.length)||o(),nt.includes(e)||(console.warn("The adjust type ".concat(e," is not supported yet, please assign it with 'force', 'grid', or 'circular'.")),o());var n={center:i.layoutCfg.center,nodeSize:function(t){return Math.max(t.height,t.width)},preventOverlap:!0,onLayoutEnd:function(){}},r=i.getLayoutBBox(a),s=r.groupNodes,l=r.layoutNodes,c=Object(u["clone"])(l);n.onLayoutEnd=function(){null===l||void 0===l||l.forEach((function(t,e){var i,o,a,n=t.x-(null===(i=c[e])||void 0===i?void 0:i.x),r=t.y-(null===(o=c[e])||void 0===o?void 0:o.y);null===(a=s[e])||void 0===a||a.forEach((function(t){t.x+=n,t.y+=r}))})),o()};var d=new P["Layouts"][e](n);d.layout({nodes:l})}))},e.prototype.hasGPUVersion=function(t){return at.includes(t)},e.prototype.destroy=function(){this.destoryLayoutMethods();var t=this.worker;t&&(t.terminate(),this.worker=null),this.destroyed=!0,this.graph.set("layout",void 0),this.layoutCfg=void 0,this.layoutType=void 0,this.layoutMethods=void 0,this.graph=null},e}(s["c"]),st=rt;function lt(t,e){for(var i=t.nodes,o=e.nodes,a=i.length,n=0;n<a;n++){var r=i[n];r.x=o[n].x,r.y=o[n].y}}function ct(t,e){var i={};return t&&"object"===tt(t)?(Object.keys(t).forEach((function(o){t.hasOwnProperty(o)&&e(t[o])&&(i[o]=t[o])})),i):t}function dt(t,e){for(var i=t.nodes,o=e.vertexEdgeData,a=i.length,n=0;n<a;n++){var r=i[n],s=o[4*n],l=o[4*n+1];r.x=s,r.y=l}}function ht(t,e){var i;if(null===(i=null===t||void 0===t?void 0:t.nodes)||void 0===i?void 0:i.length){var o=t.nodes;o.forEach((function(t){t.layoutOrder=e}))}}var gt=g["a"].transform,ut="svg",ft=function(t){function e(e){var i=t.call(this,e)||this,o=i.get("defaultNode");return o||i.set("defaultNode",{type:"circle"}),o.type||(o.type="circle",i.set("defaultNode",o)),i.destroyed=!1,i}return Object(r["c"])(e,t),e.prototype.initLayoutController=function(){var t=new st(this);this.set({layoutController:t})},e.prototype.initEventController=function(){var t=new T(this);this.set({eventController:t})},e.prototype.initCanvas=function(){var t=this.get("container");if("string"===typeof t&&(t=document.getElementById(t),this.set("container",t)),!t)throw new Error("invalid container");var e=t.clientWidth,i=t.clientHeight,o=this.get("width")||e,a=this.get("height")||i;this.get("width")||this.get("height")||(this.set("width",e),this.set("height",i));var n,r=this.get("renderer");if(r===ut)n=new h["a"]({container:t,width:o,height:a});else{var s={container:t,width:o,height:a},l=this.get("pixelRatio");l&&(s.pixelRatio=l),n=new d["a"](s)}this.set("canvas",n)},e.prototype.initPlugins=function(){var t=this;Object(u["each"])(t.get("plugins"),(function(e){!e.destroyed&&e.initPlugin&&e.initPlugin(t)}))},e.prototype.downloadImageWatermark=function(t,e,i,o){return Object(r["b"])(this,void 0,void 0,(function(){var a,n,s;return Object(r["d"])(this,(function(r){switch(r.label){case 0:return a=t.style.backgroundImage,n=a.slice(5,a.length-2),s=new Image,s.src=n,[4,new Promise((function(t){s.onload=function(){var a=e.createPattern(s,"repeat");e.rect(0,0,i,o),e.fillStyle=a,e.fill(),t("")}}))];case 1:return r.sent(),[2]}}))}))},e.prototype.asyncToDataUrl=function(t,e,i,o,a,n){var s=this,l=document.querySelector(".g6-graph-watermarker"),c=this.get("canvas"),d=c.getRenderer(),h=n||c.get("el"),g="";t||(t="image/png"),setTimeout((function(){return Object(r["b"])(s,void 0,void 0,(function(){var n,s,c,u,f,v,p,m,y,b;return Object(r["d"])(this,(function(r){switch(r.label){case 0:return"svg"!==d?[3,1]:(n=h.cloneNode(!0),s=document.implementation.createDocumentType("svg","-//W3C//DTD SVG 1.1//EN","http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd"),c=document.implementation.createDocument("http://www.w3.org/2000/svg","svg",s),c.replaceChild(n,c.documentElement),u=(new XMLSerializer).serializeToString(c),g="data:image/svg+xml;charset=utf8,".concat(encodeURIComponent(u)),[3,4]);case 1:return f=void 0,v=h.getContext("2d"),p=o||this.get("width"),m=a||this.get("height"),y=void 0,l?[4,this.downloadImageWatermark(l,v,p,m)]:[3,3];case 2:r.sent(),r.label=3;case 3:if(e){b="undefined"!==typeof window?window.devicePixelRatio:1;try{f=v.getImageData(0,0,p*b,m*b),y=v.globalCompositeOperation,v.globalCompositeOperation="destination-over",v.fillStyle=e,v.fillRect(0,0,p,m)}catch(S){console.error("Download image failed. Out of memory at ImageData creation")}}g=h.toDataURL(t),e&&(v.clearRect(0,0,p,m),v.putImageData(f,0,0),v.globalCompositeOperation=y),r.label=4;case 4:return i&&i(g),[2]}}))}))}),16)},e.prototype.toDataURL=function(t,e){var i=this.get("canvas"),o=i.getRenderer(),a=i.get("el");t||(t="image/png");var n="";if("svg"===o){var r=a.cloneNode(!0),s=document.implementation.createDocumentType("svg","-//W3C//DTD SVG 1.1//EN","http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd"),l=document.implementation.createDocument("http://www.w3.org/2000/svg","svg",s);l.replaceChild(r,l.documentElement);var c=(new XMLSerializer).serializeToString(l);n="data:image/svg+xml;charset=utf8,".concat(encodeURIComponent(c))}else{var d=void 0,h=a.getContext("2d"),g=Math.max(this.get("width"),500),u=Math.max(this.get("height"),500),f=void 0;if(e){var v="undefined"!==typeof window&&window.devicePixelRatio||1;try{d=h.getImageData(0,0,g*v,u*v),f=h.globalCompositeOperation,h.globalCompositeOperation="destination-over",h.fillStyle=e,h.fillRect(0,0,g,u)}catch(p){console.error("Download image failed. Out of memory at ImageData creation")}}n=a.toDataURL(t),e&&(h.clearRect(0,0,g,u),h.putImageData(d,0,0),h.globalCompositeOperation=f)}return n},e.prototype.toFullDataURL=function(t,e,i){var o=this.get("group").getCanvasBBox(),a=o.height,n=o.width,r=this.get("renderer"),s=Object(D["b"])('<div id="virtual-image"></div>'),l=i?i.backgroundColor:void 0,c=i?i.padding:void 0;c?Object(u["isNumber"])(c)&&(c=[c,c,c,c]):c=[0,0,0,0];var g=a+c[0]+c[2],f=n+c[1]+c[3],v={container:s,height:g,width:f,quickHit:!0},p="svg"===r?new h["a"](v):new d["a"](v),m=this.get("group"),y=m.clone(),b=Object(u["clone"])(y.getMatrix());b||(b=[1,0,0,0,1,0,0,0,1]);var S=(o.maxX+o.minX)/2,w=(o.maxY+o.minY)/2;b=gt(b,[["t",-S,-w],["t",n/2+c[3],a/2+c[0]]]),y.resetMatrix(),y.setMatrix(b),p.add(y);var x=p.get("el"),k="";e||(e="image/png"),setTimeout((function(){if("svg"===r){var i=x.cloneNode(!0),o=document.implementation.createDocumentType("svg","-//W3C//DTD SVG 1.1//EN","http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd"),a=document.implementation.createDocument("http://www.w3.org/2000/svg","svg",o);a.replaceChild(i,a.documentElement);var n=(new XMLSerializer).serializeToString(a);k="data:image/svg+xml;charset=utf8,".concat(encodeURIComponent(n))}else{var s=void 0,c=x.getContext("2d"),d=void 0;if(l){var h="undefined"!==typeof window?window.devicePixelRatio:1;try{s=c.getImageData(0,0,f*h,g*h),d=c.globalCompositeOperation,c.globalCompositeOperation="destination-over",c.fillStyle=l,c.fillRect(0,0,f,g)}catch(u){console.error("Download image failed. Out of memory at ImageData creation")}}k=x.toDataURL(e),l&&(c.clearRect(0,0,f,g),c.putImageData(s,0,0),c.globalCompositeOperation=d)}t&&t(k)}),16)},e.prototype.downloadFullImage=function(t,e,i){var o=this,a=this.get("group").getCanvasBBox(),n=a.height,r=a.width,s=this.get("renderer"),l=Object(D["b"])('<div id="virtual-image"></div>'),c=document.querySelector(".g6-graph-watermarker"),g=i?i.backgroundColor:void 0,f=i?i.padding:void 0;f?Object(u["isNumber"])(f)&&(f=[f,f,f,f]):f=[0,0,0,0];var v=n+f[0]+f[2],p=r+f[1]+f[3];if(c){var m=this.get("graphWaterMarker").cfg||{},y=m.width,b=m.height;v=Math.ceil(v/b)*b,p=Math.ceil(p/y)*y}var S={container:l,height:v,width:p},w="svg"===s?new h["a"](S):new d["a"](S),x=this.get("group"),k=x.clone(),C=Object(u["clone"])(k.getMatrix());C||(C=[1,0,0,0,1,0,0,0,1]);var E=(a.maxX+a.minX)/2,L=(a.maxY+a.minY)/2;C=gt(C,[["t",-E,-L],["t",r/2+f[3],n/2+f[0]]]),k.resetMatrix(),k.setMatrix(C),w.add(k);var I=w.get("el");e||(e="image/png"),this.asyncToDataUrl(e,g,(function(i){var a=document.createElement("a"),n=(t||"graph")+("svg"===s?".svg":".".concat(e.split("/")[1]));o.dataURLToImage(i,s,a,n);var r=document.createEvent("MouseEvents");r.initEvent("click",!1,!1),a.dispatchEvent(r)}),p,v,I)},e.prototype.downloadImage=function(t,e,i){var o=this,a=this;a.isAnimating()&&a.stopAnimate();var n=a.get("canvas"),r=n.getRenderer();e||(e="image/png");var s=(t||"graph")+("svg"===r?".svg":e.split("/")[1]),l=document.createElement("a");a.asyncToDataUrl(e,i,(function(t){o.dataURLToImage(t,r,l,s);var e=document.createEvent("MouseEvents");e.initEvent("click",!1,!1),l.dispatchEvent(e)}))},e.prototype.dataURLToImage=function(t,e,i,o){if(t&&"data:"!==t){if("undefined"!==typeof window)if(window.Blob&&window.URL&&"svg"!==e){var a=t.split(","),n="";if(a&&a.length>0){var r=a[0].match(/:(.*?);/);r&&r.length>=2&&(n=r[1])}var s=atob(a[1]),l=s.length,c=new Uint8Array(l);while(l--)c[l]=s.charCodeAt(l);var d=new Blob([c],{type:n});window.navigator.msSaveBlob?window.navigator.msSaveBlob(d,o):i.addEventListener("click",(function(){i.download=o,i.href=window.URL.createObjectURL(d)}))}else i.addEventListener("click",(function(){i.download=o,i.href=t}))}else console.error("Download image failed. The graph is too large or there is invalid attribute values in graph items")},e.prototype.addPlugin=function(t){var e=this;t.destroyed||(e.get("plugins").push(t),t.initPlugin(e))},e.prototype.removePlugin=function(t){var e=this.get("plugins"),i=e.indexOf(t);i>=0&&(t.destroyPlugin(),e.splice(i,1))},e.prototype.setImageWaterMarker=function(t,e){void 0===t&&(t=L.waterMarkerImage);var i=this.get("container");Object(u["isString"])(i)&&(i=document.getElementById(i)),i.style.position||(i.style.position="relative");var o=this.get("graphWaterMarker"),a=Object(u["deepMix"])({},L.imageWaterMarkerConfig,e),n=a.width,r=a.height,s=a.compatible,l=a.image;if(!o){var c={container:i,width:n,height:r,capture:!1},h=this.get("pixelRatio");h&&(c.pixelRatio=h),o=new d["a"](c),this.set("graphWaterMarker",o)}o.get("el").style.display="none";var g=o.get("context"),f=l.rotate,v=l.x,p=l.y;g.rotate(-f*Math.PI/180);var m=new Image;m.crossOrigin="anonymous",m.src=t,m.onload=function(){if(g.drawImage(m,v,p,l.width,l.height),g.rotate(f*Math.PI/180),s)i.style.cssText="background-image: url(".concat(o.get("el").toDataURL("image/png"),");background-repeat:repeat;");else{var t=document.querySelector(".g6-graph-watermarker");t||(t=document.createElement("div"),t.className="g6-graph-watermarker"),t.className="g6-graph-watermarker",o.destroyed||(t.style.cssText="background-image: url(".concat(o.get("el").toDataURL("image/png"),");background-repeat:repeat;position:absolute;top:0;bottom:0;left:0;right:0;pointer-events:none;z-index:-1;"),i.appendChild(t))}}},e.prototype.setTextWaterMarker=function(t,e){var i=this.get("container");Object(u["isString"])(i)&&(i=document.getElementById(i)),i.style.position||(i.style.position="relative");var o=this.get("graphWaterMarker"),a=Object(u["deepMix"])({},L.textWaterMarkerConfig,e),n=a.width,r=a.height,s=a.compatible,l=a.text;if(!o){var c={container:i,width:n,height:r,capture:!1},h=this.get("pixelRatio");h&&(c.pixelRatio=h),o=new d["a"](c),this.set("graphWaterMarker",o)}o.get("el").style.display="none";var g=o.get("context"),f=l.rotate,v=l.fill,p=l.fontFamily,m=l.fontSize,y=l.baseline,b=l.x,S=l.y,w=l.lineHeight;g.rotate(-f*Math.PI/180),g.font="".concat(m,"px ").concat(p),g.fillStyle=v,g.textBaseline=y;for(var x=t.length-1;x>=0;x--)g.fillText(t[x],b,S+x*w);if(g.rotate(f*Math.PI/180),s)i.style.cssText="background-image: url(".concat(o.get("el").toDataURL("image/png"),");background-repeat:repeat;");else{var k=document.querySelector(".g6-graph-watermarker");k||(k=document.createElement("div"),k.className="g6-graph-watermarker"),k.style.cssText="background-image: url(".concat(o.get("el").toDataURL("image/png"),");background-repeat:repeat;position:absolute;top:0;bottom:0;left:0;right:0;pointer-events:none;z-index:99;"),i.appendChild(k)}},e.prototype.destroy=function(){var e,i,o,a;Object(u["each"])(this.get("plugins"),(function(t){t.destroyPlugin()}));var n=this.get("tooltips");if(n)for(var r=0;r<n.length;r++){var s=n[r];if(s){var l=s.parentElement;l&&l.removeChild(s)}}null===(e=this.get("eventController"))||void 0===e||e.destroy(),null===(i=this.get("layoutController"))||void 0===i||i.destroy(),null===(o=this.get("graphWaterMarker"))||void 0===o||o.destroy(),null===(a=document.querySelector(".g6-graph-watermarker"))||void 0===a||a.remove(),t.prototype.destroy.call(this)},e}(s["b"]),vt=ft,pt=X.radialLayout,mt=X.traverseTree,yt=function(t){function e(e){var i=t.call(this,e)||this;return i.layoutAnimating=!1,i.set("removeList",[]),i.set("layoutMethod",i.getLayout()),i}return Object(r["c"])(e,t),e.prototype.getLayout=function(){var t=this.get("layout");return t?"function"===typeof t?t:(t.type||(t.type="dendrogram"),t.direction||(t.direction="TB"),t.radial?function(e){var i=A.a[t.type](e,t);return pt(i),i}:function(e){return A.a[t.type](e,t)}):null},e.indexOfChild=function(t,e){var i=-1;return Object(u["each"])(t,(function(t,o){if(e===t.id)return i=o,!1})),i},e.prototype.getDefaultCfg=function(){var e=t.prototype.getDefaultCfg.call(this);return e.animate=!0,e},e.prototype.innerAddChild=function(t,e,i){var o=this,a=t.data;a&&(a.x=t.x,a.y=t.y,a.depth=t.depth);var n=o.addItem("node",a,!1);if(e){if(n.set("parent",e),i){var r=e.get("originAttrs");if(r)n.set("originAttrs",r);else{var s=e.getModel();n.set("originAttrs",{x:s.x,y:s.y})}}var l=e.get("children");l?l.push(n):e.set("children",[n]),o.addItem("edge",{source:e.get("id"),target:n.get("id"),id:"".concat(e.get("id"),":").concat(n.get("id"))},!1)}return Object(u["each"])(t.children||[],(function(t){o.innerAddChild(t,n,i)})),o.emit("afteraddchild",{item:n,parent:e}),n},e.prototype.innerUpdateChild=function(t,i,o){var a=this,n=a.findById(t.id);if(n){Object(u["each"])(t.children||[],(function(t){a.innerUpdateChild(t,n,o)}));var r,s,l=n.get("children");if(l){var c=l.length;if(c>0)for(var d=l.length-1;d>=0;d--){var h=l[d].getModel();-1===e.indexOfChild(t.children||[],h.id)&&(a.innerRemoveChild(h.id,{x:t.x,y:t.y},o),l.splice(d,1))}}n.get("originAttrs")&&(r=n.get("originAttrs").x,s=n.get("originAttrs").y);var g=n.getModel();o&&n.set("originAttrs",{x:g.x,y:g.y}),n.set("model",t.data),r===t.x&&s===t.y||n.updatePosition({x:t.x,y:t.y})}else a.innerAddChild(t,i,o)},e.prototype.innerRemoveChild=function(t,e,i){var o=this,a=o.findById(t);if(a)if(Object(u["each"])(a.get("children"),(function(t){o.innerRemoveChild(t.getModel().id,e,i)})),i){var n=a.getModel();a.set("to",e),a.set("originAttrs",{x:n.x,y:n.y}),o.get("removeList").push(a)}else o.removeItem(a,!1)},e.prototype.changeData=function(t){var e=this;this.getNodes().map((function(t){return e.clearItemStates(t)})),this.getEdges().map((function(t){return e.clearItemStates(t)})),t?(e.data(t),e.render()):e.layout(this.get("fitView"))},e.prototype.changeLayout=function(t){console.warn("Please call updateLayout instead of changeLayout. changeLayout will be discarded soon");var e=this;e.updateLayout(t)},e.prototype.updateLayout=function(t){var e=this;t?(e.set("layout",t),e.set("layoutMethod",e.getLayout()),e.layout()):console.warn("layout cannot be null")},e.prototype.refreshLayout=function(t){console.warn("Please call layout instead of refreshLayout. refreshLayout will be discarded soon");var e=this;e.layout(t)},e.prototype.layout=function(t){var e=this,i=e.get("data"),o=e.get("layoutMethod"),a=o?o(i,e.get("layout")):i,n=e.get("animate");if(e.emit("beforerefreshlayout",{data:i,layoutData:a}),e.emit("beforelayout"),e.innerUpdateChild(a,void 0,n),t){var r=e.get("viewController");r.fitView()}n?e.layoutAnimate(a):(e.refresh(),e.paint()),e.emit("afterrefreshlayout",{data:i,layoutData:a}),e.emit("afterlayout")},e.prototype.addChild=function(t,e){var i=this;i.emit("beforeaddchild",{model:t,parent:e}),Object(u["isString"])(e)||(e=e.get("id"));var o=i.findDataById(e);o&&(o.children||(o.children=[]),o.children.push(t),i.changeData())},e.prototype.updateChildren=function(t,e){var i=this;if(e&&i.findById(e)){var o=i.findDataById(e);o.children=t,i.changeData()}else console.warn("Update children failed! There is no node with id '".concat(e,"'"))},e.prototype.updateChild=function(t,i){var o=this;if(i&&o.findById(i)){var a=o.findDataById(i),n=o.findById(t.id);if(a.children||(a.children=[]),n){var r=e.indexOfChild(a.children,t.id);a.children[r]=t}else a.children.push(t);o.changeData()}else o.changeData(t)},e.prototype.removeChild=function(t){var i=this,o=i.findById(t);if(o){var a=o.get("parent");if(a&&!a.destroyed){var n=i.findDataById(a.get("id")),r=n&&n.children||[],s=o.getModel(),l=e.indexOfChild(r,s.id);r.splice(l,1)}i.changeData()}},e.prototype.findDataById=function(t,e){var i=this;if(e||(e=i.get("data")),t===e.id)return e;var o=null;return Object(u["each"])(e.children||[],(function(e){return e.id===t?(o=e,!1):(o=i.findDataById(t,e),!o&&void 0)})),o},e.prototype.layoutAnimate=function(t,e){var i=this,o=this.get("animateCfg");i.emit("beforeanimate",{data:t}),i.getEdges().forEach((function(t){var e=t.get("model");e.sourceAnchor||(e.sourceAnchor=t.get("sourceAnchorIndex"))})),this.get("canvas").animate((function(o){mt(t,(function(a){var n=i.findById(a.id);if(n){var r=n.get("originAttrs"),s=n.get("model");if(r||(r={x:s.x,y:s.y},n.set("originAttrs",r)),e){var l=e(n,o,r,t);n.set("model",Object.assign(s,l))}else s.x=r.x+(a.x-r.x)*o,s.y=r.y+(a.y-r.y)*o}return!0})),Object(u["each"])(i.get("removeList"),(function(t){var e=t.getModel(),i=t.get("originAttrs"),a=t.get("to");e.x=i.x+(a.x-i.x)*o,e.y=i.y+(a.y-i.y)*o})),i.refreshPositions()}),{duration:o.duration,easing:o.ease,callback:function(){Object(u["each"])(i.getNodes(),(function(t){t.set("originAttrs",null)})),Object(u["each"])(i.get("removeList"),(function(t){i.removeItem(t)})),i.set("removeList",[]),o.callback&&o.callback(),i.emit("afteranimate",{data:t})},delay:o.delay})},e.prototype.stopLayoutAnimate=function(){this.get("canvas").stopAnimate(),this.emit("layoutanimateend",{data:this.get("data")}),this.layoutAnimating=!1},e.prototype.isLayoutAnimating=function(){return this.layoutAnimating},e.prototype.render=function(){var t=this,e=t.get("data");if(!e||!Object(u["isObject"])(e)||!Object.keys(e).length)throw new Error("data must be defined first");t.clear(),t.emit("beforerender"),t.layout(this.get("fitView")),t.emit("afterrender")},e.prototype.save=function(){return this.get("data")},e}(vt),bt=yt,St=i("0d3c"),wt=St["a"],xt=(i("1649"),X.cloneEvent),kt=X.isNaN,Ct=Math.abs,Et=10,Lt=["shift","ctrl","alt","control"],Dt={getDefaultCfg:function(){return{direction:"both",enableOptimize:!1,scalableRange:0,allowDragOnItem:!1}},getEvents:function(){return{dragstart:"onMouseDown",drag:"onMouseMove",dragend:"onMouseUp","canvas:click":"onMouseUp",keyup:"onKeyUp",focus:"onKeyUp",keydown:"onKeyDown",touchstart:"onTouchStart",touchmove:"onTouchMove",touchend:"onMouseUp"}},updateViewport:function(t){var e=this.origin,i=+t.clientX,o=+t.clientY;if(!kt(i)&&!kt(o)){var a=i-e.x,n=o-e.y;"x"===this.get("direction")?n=0:"y"===this.get("direction")&&(a=0),this.origin={x:i,y:o};var r=this.graph.get("width"),s=this.graph.get("height"),l=this.graph.get("canvas").getCanvasBBox(),c=this.scalableRange,d=this.scalableRange;c<1&&c>-1&&(c*=r,d*=s),(l.minX<=r+c&&l.minX+a>r+c||l.maxX+c>=0&&l.maxX+c+a<0)&&(a=0),(l.minY<=s+d&&l.minY+n>s+d||l.maxY+d>=0&&l.maxY+d+n<0)&&(n=0),this.graph.translate(a,n)}},onTouchStart:function(t){var e=this,i=t.originalEvent.touches,o=i[0],a=i[1];o&&a||(t.preventDefault(),e.onMouseDown(t))},onMouseDown:function(t){var e=this,i=t.originalEvent;if((!i||"touchstart"===t.name||0===i.button)&&("touchstart"===t.name||"undefined"===typeof window||!window.event||window.event.buttons||window.event.button)&&this.shouldBegin.call(this,t)&&!e.keydown){var o=t.target,a=o&&o.isCanvas&&o.isCanvas();if((this.allowDragOnItem||a)&&(e.origin={x:t.clientX,y:t.clientY},e.dragging=!1,this.enableOptimize)){for(var n=this.graph,r=n.getEdges(),s=0,l=r.length;s<l;s++){var c=r[s].get("group").get("children");c&&c.forEach((function(t){t.set("ori-visibility",t.get("ori-visibility")||t.get("visible")),t.hide()}))}for(var d=n.getNodes(),h=0,g=d.length;h<g;h++)for(var u=d[h].getContainer(),f=u.get("children"),v=0,p=f;v<p.length;v++){var m=p[v],y=m.get("isKeyShape");y||(m.set("ori-visibility",m.get("ori-visibility")||m.get("visible")),m.hide())}}}},onTouchMove:function(t){var e=this,i=t.originalEvent.touches,o=i[0],a=i[1];o&&a?this.onMouseUp(t):(t.preventDefault(),e.onMouseMove(t))},onMouseMove:function(t){var e=this.graph;if(!this.keydown){var i=t.target,o=i&&i.isCanvas&&i.isCanvas();if((this.allowDragOnItem||o)&&(t=xt(t),this.origin)){if(this.dragging)t.type="drag",e.emit("canvas:drag",t);else{if(Ct(this.origin.x-t.clientX)+Ct(this.origin.y-t.clientY)<Et)return;this.shouldBegin.call(this,t)&&(t.type="dragstart",e.emit("canvas:dragstart",t),this.originPosition={x:t.clientX,y:t.clientY},this.dragging=!0)}this.shouldUpdate.call(this,t)&&this.updateViewport(t)}}},onMouseUp:function(t){var e,i,o=this.graph;if(!this.keydown){var a=o.getZoom(),n=o.get("modeController"),r=null===(i=null===(e=null===n||void 0===n?void 0:n.modes[n.mode])||void 0===e?void 0:e.filter((function(t){return"zoom-canvas"===t.type})))||void 0===i?void 0:i[0],s=r?r.optimizeZoom||.1:0;if(this.enableOptimize){for(var l=o.getEdges(),c=0,d=l.length;c<d;c++){var h=l[c].get("group").get("children");h&&h.forEach((function(t){var e=t.get("ori-visibility");e&&t.show()}))}if(a>s)for(var g=o.getNodes(),u=0,f=g.length;u<f;u++)for(var v=g[u].getContainer(),p=v.get("children"),m=0,y=p;m<y.length;m++){var b=y[m],S=b.get("isKeyShape");if(!S){var w=b.get("ori-visibility");w&&b.show()}}}this.dragging?(t=xt(t),this.shouldEnd.call(this,t)&&this.updateViewport(t),t.type="dragend",t.dx=t.clientX-this.originPosition.x,t.dy=t.clientY-this.originPosition.y,o.emit("canvas:dragend",t),this.endDrag()):this.origin=null}},endDrag:function(){this.origin=null,this.dragging=!1,this.dragbegin=!1},onKeyDown:function(t){var e=this,i=t.key;i&&(Lt.indexOf(i.toLowerCase())>-1?e.keydown=!0:e.keydown=!1)},onKeyUp:function(){this.keydown=!1,this.origin=null,this.dragging=!1,this.dragbegin=!1}},It={getDefaultCfg:function(){return{updateEdge:!0,delegateStyle:{},enableDelegate:!1,onlyChangeComboSize:!1,comboActiveState:"",selectedState:"selected",enableOptimize:!1,enableDebounce:!1,enableStack:!0}},getEvents:function(){return{"node:dragstart":"onDragStart","node:drag":"onDrag","node:dragend":"onDragEnd","combo:dragenter":"onDragEnter","combo:dragleave":"onDragLeave","combo:drop":"onDropCombo","node:drop":"onDropNode","canvas:drop":"onDropCanvas",touchstart:"onTouchStart",touchmove:"onTouchMove",touchend:"onDragEnd"}},validationCombo:function(t){if(!this.origin||!t||t.destroyed)return!1;var e=t.getType();return"combo"===e},onTouchStart:function(t){if(t.item){var e=this;try{var i=t.originalEvent.touches,o=i[0],a=i[1];if(o&&a)return;t.preventDefault()}catch(t){console.warn("Touch original event not exist!")}e.onDragStart(t)}},onTouchMove:function(t){var e=this;try{var i=t.originalEvent.touches,o=i[0],a=i[1];if(o&&a)return void e.onDragEnd(t);t.preventDefault()}catch(t){console.warn("Touch original event not exist!")}e.onDrag(t)},onDragStart:function(t){var e=this;if(this.currentShouldEnd=!0,this.shouldBegin.call(this,t)){var i=t.item;if(i&&!i.destroyed&&!i.hasLocked()){var o=i.getContainer();o.set("capture",!1),this.cachedCaptureItems||(this.cachedCaptureItems=[]),this.cachedCaptureItems.push(i);var a=t.target;if(a){var n=a.get("isAnchorPoint");if(n)return}var r=this.graph;this.targets=[],this.targetCombo=null;var s=r.findAllByState("node",this.selectedState),l=i.get("id"),c=s.filter((function(t){var e=t.get("id");return l===e}));0===c.length?this.targets.push(i):s.length>1?s.forEach((function(t){var i=t.hasLocked();i||e.targets.push(t)})):this.targets.push(i);var d=[];this.targets.forEach((function(t){d.push(Object(u["clone"])(t.getModel()))})),this.set("beforeDragNodes",d),this.hidenEdge={},this.get("updateEdge")&&this.enableOptimize&&!this.enableDelegate&&this.targets.forEach((function(t){var i=t.getEdges();i.forEach((function(t){t.isVisible()&&(e.hidenEdge[t.getID()]=!0,t.hide())}))})),this.origin={x:t.x,y:t.y},this.point={},this.originPoint={}}}},onDrag:function(t){var e=this;this.origin&&this.shouldUpdate.call(this,t)&&(this.get("enableDelegate")?this.updateDelegate(t):this.enableDebounce?this.debounceUpdate({targets:this.targets,graph:this.graph,point:this.point,origin:this.origin,evt:t,updateEdge:this.get("updateEdge")}):this.targets.map((function(i){e.update(i,t)})))},onDragEnd:function(t){var e,i=this;if(this.origin){null===(e=this.cachedCaptureItems)||void 0===e||e.forEach((function(t){var e=t.getContainer();e.set("capture",!0)})),this.cachedCaptureItems=[],this.delegateRect&&(this.delegateRect.remove(),this.delegateRect=null),this.get("updateEdge")&&this.enableOptimize&&!this.enableDelegate&&this.targets.forEach((function(t){var e=t.getEdges();e.forEach((function(t){i.hidenEdge[t.getID()]&&t.show(),t.refresh()}))})),this.hidenEdge={};var o=this.graph;if(o.get("enabledStack")&&this.enableStack){var a={before:{nodes:[],edges:[],combos:[]},after:{nodes:[],edges:[],combos:[]}};this.get("beforeDragNodes").forEach((function(t){a.before.nodes.push({id:t.id,x:t.x,y:t.y})})),this.targets.forEach((function(t){var e=t.getModel();a.after.nodes.push({id:e.id,x:e.x,y:e.y})})),o.pushStack("update",Object(u["clone"])(a))}o.emit("dragnodeend",{items:this.targets,targetItem:null}),this.point={},this.origin=null,this.originPoint={},this.targets.length=0,this.targetCombo=null}},onDropCombo:function(t){var e=t.item;if(this.currentShouldEnd=this.shouldEnd.call(this,t,e),this.updatePositions(t,!this.currentShouldEnd),this.currentShouldEnd&&this.validationCombo(e)){var i=this.graph;if(this.comboActiveState&&i.setItemState(e,this.comboActiveState,!1),this.targetCombo=e,this.onlyChangeComboSize)i.updateCombos();else{var o=e.getModel();this.targets.map((function(t){var e=t.getModel();e.comboId!==o.id&&i.updateComboTree(t,o.id)})),i.updateCombo(e)}i.emit("dragnodeend",{items:this.targets,targetItem:this.targetCombo})}},onDropCanvas:function(t){var e=this.graph;this.currentShouldEnd=this.shouldEnd.call(this,t,void 0),this.updatePositions(t,!this.currentShouldEnd),this.targets&&0!==this.targets.length&&this.currentShouldEnd&&(this.onlyChangeComboSize?e.updateCombos():this.targets.map((function(t){var i=t.getModel();i.comboId&&e.updateComboTree(t)})))},onDropNode:function(t){if(this.targets&&0!==this.targets.length){var e=this,i=t.item,o=e.graph,a=i.getModel().comboId,n=a?o.findById(a):void 0;if(this.currentShouldEnd=this.shouldEnd.call(this,t,n),this.updatePositions(t,!this.currentShouldEnd),this.currentShouldEnd){if(this.onlyChangeComboSize)o.updateCombos();else if(a){var r=o.findById(a);e.comboActiveState&&o.setItemState(r,e.comboActiveState,!1),this.targets.map((function(t){var e=t.getModel();a!==e.comboId&&o.updateComboTree(t,a)})),o.updateCombo(r)}else this.targets.map((function(t){var e=t.getModel();e.comboId&&o.updateComboTree(t)}));o.emit("dragnodeend",{items:this.targets,targetItem:i})}}},onDragEnter:function(t){var e=t.item;if(this.validationCombo(e)){var i=this.graph;this.comboActiveState&&i.setItemState(e,this.comboActiveState,!0)}},onDragLeave:function(t){var e=t.item;if(this.validationCombo(e)){var i=this.graph;this.comboActiveState&&i.setItemState(e,this.comboActiveState,!1)}},updatePositions:function(t,e){var i=this;this.targets&&0!==this.targets.length&&(this.get("enableDelegate")?this.enableDebounce?this.debounceUpdate({targets:this.targets,graph:this.graph,point:this.point,origin:this.origin,evt:t,updateEdge:this.get("updateEdge"),updateFunc:this.update}):e||this.targets.map((function(e){return i.update(e,t)})):this.targets.map((function(o){return i.update(o,t,e)})))},update:function(t,e,i){var o=this.origin,a=t.get("model"),n=t.get("id");this.point[n]||(this.point[n]={x:a.x||0,y:a.y||0});var r=e.x-o.x+this.point[n].x,s=e.y-o.y+this.point[n].y;i&&(r+=o.x-e.x,s+=o.y-e.y);var l={x:r,y:s};this.get("updateEdge")?this.graph.updateItem(t,l,!1):t.updatePosition(l)},debounceUpdate:Object(u["debounce"])((function(t){var e=t.targets,i=t.graph,o=t.point,a=t.origin,n=t.evt,r=t.updateEdge;t.updateFunc;e.map((function(t){var e=t.get("model"),s=t.get("id");o[s]||(o[s]={x:e.x||0,y:e.y||0});var l=n.x-a.x+o[s].x,c=n.y-a.y+o[s].y,d={x:l,y:c};r?i.updateItem(t,d,!1):t.updatePosition(d)}))}),50,!0),updateDelegate:function(t){var e=this.graph;if(this.delegateRect){var i=t.x-this.origin.x+this.originPoint.minX,o=t.y-this.origin.y+this.originPoint.minY;this.delegateRect.attr({x:i,y:o})}else{var a=e.get("group"),n=Object(u["deepMix"])({},L.delegateStyle,this.delegateStyle),s=this.calculationGroupPosition(t),l=s.x,c=s.y,d=s.width,h=s.height,g=s.minX,f=s.minY;this.originPoint={x:l,y:c,width:d,height:h,minX:g,minY:f},this.delegateRect=a.addShape("rect",{attrs:Object(r["a"])({width:d,height:h,x:l,y:c},n),name:"rect-delegate-shape"}),this.delegate=this.delegateRect,this.delegateRect.set("capture",!1)}},calculationGroupPosition:function(t){var e=this.targets;0===e.length&&e.push(t.item);for(var i=1/0,o=-1/0,a=1/0,n=-1/0,r=0;r<e.length;r++){var s=e[r],l=s.getBBox(),c=l.minX,d=l.minY,h=l.maxX,g=l.maxY;c<i&&(i=c),d<a&&(a=d),h>o&&(o=h),g>n&&(n=g)}var u=Math.floor(i),f=Math.floor(a),v=Math.ceil(o)-Math.floor(i),p=Math.ceil(n)-Math.floor(a);return{x:u,y:f,width:v,height:p,minX:i,minY:a}}},Mt={getDefaultCfg:function(){return{trigger:"mouseenter",activeState:"active",inactiveState:"inactive",resetSelected:!1,shouldUpdate:function(){return!0}}},getEvents:function(){return"mouseenter"===this.get("trigger")?{"node:mouseenter":"setAllItemStates","combo:mouseenter":"setAllItemStates","node:mouseleave":"clearActiveState","combo:mouseleave":"clearActiveState"}:{"node:click":"setAllItemStates","combo:click":"setAllItemStates","canvas:click":"clearActiveState","node:touchstart":"setOnTouchStart","combo:touchstart":"setOnTouchStart","canvas:touchstart":"clearOnTouchStart"}},setOnTouchStart:function(t){var e=this;try{var i=t.originalEvent.touches,o=i[0],a=i[1];if(o&&a)return;t.preventDefault()}catch(t){console.warn("Touch original event not exist!")}e.setAllItemStates(t)},clearOnTouchStart:function(t){var e=this;try{var i=t.originalEvent.touches,o=i[0],a=i[1];if(o&&a)return;t.preventDefault()}catch(t){console.warn("Touch original event not exist!")}e.clearActiveState(t)},setAllItemStates:function(t){var e=t.item,i=this.graph;if(this.item=e,this.shouldUpdate(t.item,{event:t,action:"activate"})){for(var o=this,a=this.activeState,n=this.inactiveState,r=i.getNodes(),s=i.getCombos(),l=i.getEdges(),c=i.get("vedges"),d=r.length,h=s.length,g=l.length,u=c.length,f=0;f<d;f++){var v=r[f],p=v.hasState("selected");o.resetSelected&&p&&i.setItemState(v,"selected",!1),i.setItemState(v,a,!1),n&&i.setItemState(v,n,!0)}for(f=0;f<h;f++){var m=s[f];p=m.hasState("selected");o.resetSelected&&p&&i.setItemState(m,"selected",!1),i.setItemState(m,a,!1),n&&i.setItemState(m,n,!0)}for(f=0;f<g;f++){var y=l[f];i.setItemState(y,a,!1),n&&i.setItemState(y,n,!0)}for(f=0;f<u;f++){var b=c[f];i.setItemState(b,a,!1),n&&i.setItemState(b,n,!0)}n&&i.setItemState(e,n,!1),i.setItemState(e,a,!0);var S=e.getEdges(),w=S.length;for(f=0;f<w;f++){y=S[f];var x=void 0;x=y.getSource()===e?y.getTarget():y.getSource(),n&&i.setItemState(x,n,!1),i.setItemState(x,a,!0),i.setItemState(y,n,!1),i.setItemState(y,a,!0),y.toFront()}i.emit("afteractivaterelations",{item:t.item,action:"activate"})}},clearActiveState:function(t){var e=this,i=e.get("graph");if(e.shouldUpdate(t.item,{event:t,action:"deactivate"})){var o=this.activeState,a=this.inactiveState,n=i.get("autoPaint");i.setAutoPaint(!1);for(var r=i.getNodes()||[],s=i.getCombos()||[],l=i.getEdges()||[],c=i.get("vedges")||[],d=r.length,h=s.length,g=l.length,u=c.length,f=0;f<d;f++){var v=r[f];i.clearItemStates(v,[o,a])}for(f=0;f<h;f++){var p=s[f];i.clearItemStates(p,[o,a])}for(f=0;f<g;f++){var m=l[f];i.clearItemStates(m,[o,a,"deactivate"])}for(f=0;f<u;f++){var y=c[f];i.clearItemStates(y,[o,a,"deactivate"])}i.paint(),i.setAutoPaint(n),i.emit("afteractivaterelations",{item:t.item||e.get("item"),action:"deactivate"})}}},Ot=Math.min,Tt=Math.max,Pt=Math.abs,Bt="shift",At=["drag","shift","ctrl","alt","control"],jt={getDefaultCfg:function(){return{brushStyle:{fill:"#EEF6FF",fillOpacity:.4,stroke:"#DDEEFE",lineWidth:1},onSelect:function(){},onDeselect:function(){},selectedState:"selected",trigger:Bt,includeEdges:!0,selectedEdges:[],selectedNodes:[]}},getEvents:function(){return At.indexOf(this.trigger.toLowerCase())>-1||(this.trigger=Bt,console.warn("Behavior brush-select 的 trigger 参数不合法，请输入 'drag'、'shift'、'ctrl' 或 'alt'")),"drag"===this.trigger?{dragstart:"onMouseDown",drag:"onMouseMove",dragend:"onMouseUp","canvas:click":"clearStates"}:{dragstart:"onMouseDown",drag:"onMouseMove",dragend:"onMouseUp","canvas:click":"clearStates",keyup:"onKeyUp",keydown:"onKeyDown"}},onMouseDown:function(t){var e=t.item,i=this.brush;e||("drag"===this.trigger||this.keydown)&&(this.selectedNodes&&0!==this.selectedNodes.length&&this.clearStates(),i||(i=this.createBrush()),this.originPoint={x:t.canvasX,y:t.canvasY},i.attr({width:0,height:0}),i.show(),this.dragging=!0)},onMouseMove:function(t){this.dragging&&("drag"===this.trigger||this.keydown)&&this.updateBrush(t)},onMouseUp:function(t){this.graph;(this.brush||this.dragging)&&("drag"===this.trigger||this.keydown)&&(this.brush.remove(!0),this.brush=null,this.getSelectedNodes(t),this.dragging=!1)},clearStates:function(){var t=this,e=t.graph,i=t.selectedState,o=e.findAllByState("node",i),a=e.findAllByState("edge",i);o.forEach((function(t){return e.setItemState(t,i,!1)})),a.forEach((function(t){return e.setItemState(t,i,!1)})),this.selectedNodes=[],this.selectedEdges=[],this.onDeselect&&this.onDeselect(this.selectedNodes,this.selectedEdges),e.emit("nodeselectchange",{selectedItems:{nodes:[],edges:[]},select:!1})},getSelectedNodes:function(t){var e=this,i=this,o=i.graph,a=i.originPoint,n=i.shouldUpdate,r=this.selectedState,s={x:t.x,y:t.y},l=o.getPointByCanvas(a.x,a.y),c=Ot(s.x,l.x),d=Tt(s.x,l.x),h=Ot(s.y,l.y),g=Tt(s.y,l.y),u=[],f=[];o.getNodes().forEach((function(t){if(t.isVisible()){var e=t.getBBox();if(e.centerX>=c&&e.centerX<=d&&e.centerY>=h&&e.centerY<=g&&n(t,"select")){u.push(t);var i=t.getModel();f.push(i.id),o.setItemState(t,r,!0)}}}));var v=[];this.includeEdges&&u.forEach((function(t){var i=t.getOutEdges();i.forEach((function(t){if(t.isVisible()){var i=t.getModel(),a=i.source,r=i.target;f.includes(a)&&f.includes(r)&&n(t,"select")&&(v.push(t),o.setItemState(t,e.selectedState,!0))}}))})),this.selectedEdges=v,this.selectedNodes=u,this.onSelect&&this.onSelect(u,v),o.emit("nodeselectchange",{selectedItems:{nodes:u,edges:v},select:!0})},createBrush:function(){var t=this,e=t.graph.get("canvas").addShape("rect",{attrs:t.brushStyle,capture:!1,name:"brush-shape"});return this.brush=e,this.delegate=e,e},updateBrush:function(t){var e=this.originPoint;this.brush.attr({width:Pt(t.canvasX-e.x),height:Pt(t.canvasY-e.y),x:Ot(t.canvasX,e.x),y:Ot(t.canvasY,e.y)})},onKeyDown:function(t){var e=t.key;if(e){var i=this.trigger.toLowerCase(),o=e.toLowerCase();this.keydown=o===i||"control"===o&&"ctrl"===i||"ctrl"===o&&"control"===i}},onKeyUp:function(){this.brush&&(this.brush.remove(!0),this.brush=null,this.dragging=!1),this.keydown=!1}},Ut="shift",Ft=["shift","ctrl","alt","control"],Rt={getDefaultCfg:function(){return{multiple:!0,trigger:Ut,selectedState:"selected",selectNode:!0,selectEdge:!1,selectCombo:!0}},getEvents:function(){var t=this;return Ft.indexOf(t.trigger.toLowerCase())>-1||(t.trigger=Ut,console.warn("Behavior click-select 的 trigger 参数不合法，请输入 'drag'、'shift'、'ctrl' 或 'alt'")),t.multiple?{"node:click":"onClick","combo:click":"onClick","edge:click":"onClick","canvas:click":"onCanvasClick",keyup:"onKeyUp",keydown:"onKeyDown"}:{"node:click":"onClick","combo:click":"onClick","edge:click":"onClick","canvas:click":"onCanvasClick"}},onClick:function(t){var e=this,i=t.item;if(i&&!i.destroyed){var o=i.getType(),a=e.graph,n=e.keydown,r=e.multiple,s=e.shouldUpdate,l=e.shouldBegin;if(l.call(e,t)){if(!n||!r){var c=a.findAllByState("node",e.selectedState).concat(a.findAllByState("edge",e.selectedState)).concat(a.findAllByState("combo",e.selectedState));Object(u["each"])(c,(function(t){t!==i&&a.setItemState(t,e.selectedState,!1)}))}var d=function(){switch(o){case"node":return e.selectNode;case"edge":return e.selectEdge;case"combo":return e.selectCombo;default:return!1}}();if(d)if(i.hasState(e.selectedState)){s.call(e,t)&&a.setItemState(i,e.selectedState,!1);h=a.findAllByState("node",e.selectedState),g=a.findAllByState("edge",e.selectedState),f=a.findAllByState("combo",e.selectedState);a.emit("nodeselectchange",{target:i,selectedItems:{nodes:h,edges:g,combos:f},select:!1})}else{s.call(e,t)&&a.setItemState(i,e.selectedState,!0);h=a.findAllByState("node",e.selectedState),g=a.findAllByState("edge",e.selectedState),f=a.findAllByState("combo",e.selectedState);a.emit("nodeselectchange",{target:i,selectedItems:{nodes:h,edges:g,combos:f},select:!0})}else{var h=a.findAllByState("node",e.selectedState),g=a.findAllByState("edge",e.selectedState),f=a.findAllByState("combo",e.selectedState);a.emit("nodeselectchange",{selectedItems:{nodes:h,edges:g,combos:f},select:!1})}}}},onCanvasClick:function(t){var e=this,i=this,o=i.graph,a=i.shouldBegin;if(a.call(this,t)){var n=o.findAllByState("node",this.selectedState);Object(u["each"])(n,(function(t){o.setItemState(t,e.selectedState,!1)}));var r=o.findAllByState("edge",this.selectedState);Object(u["each"])(r,(function(t){o.setItemState(t,e.selectedState,!1)}));var s=o.findAllByState("combo",this.selectedState);Object(u["each"])(s,(function(t){o.setItemState(t,e.selectedState,!1)})),o.emit("nodeselectchange",{selectedItems:{nodes:[],edges:[],combos:[]},select:!1})}},onKeyDown:function(t){var e=this,i=t.key;i&&(i.toLowerCase()===this.trigger.toLowerCase()||"control"===i.toLowerCase()?e.keydown=!0:e.keydown=!1)},onKeyUp:function(){var t=this;t.keydown=!1}},Wt=g["a"].transform,Nt=.05,Yt={getDefaultCfg:function(){return{sensitivity:2,minZoom:void 0,maxZoom:void 0,enableOptimize:!1,optimizeZoom:.1,fixSelectedItems:{fixAll:!1,fixLineWidth:!1,fixLabel:!1,fixState:"selected"},animate:!1,animateCfg:{duration:500}}},getEvents:function(){var t=this.fixSelectedItems;return t.fixState||(t.fixState="selected"),t.fixAll&&(t.fixLineWidth=!0,t.fixLabel=!0),{wheel:"onWheel",touchstart:"onTouchStart",touchmove:"onTouchMove",touchend:"onTouchEnd"}},onTouchStart:function(t){var e=t.originalEvent.touches,i=e[0],o=e[1];t.preventDefault(),o&&(this.shouldBegin&&!this.shouldBegin.call(this,t)||(this.startPoint={pageX:i.pageX,pageY:i.pageY},this.moveable=!0,o&&(this.endPoint={pageX:o.pageX,pageY:o.pageY}),this.originScale=this.graph.getZoom()||this.currentScale||1))},onTouchMove:function(t){if(this.moveable){t.preventDefault();var e=t.originalEvent.touches,i=e[0],o=e[1];if(o){this.endPoint||(this.endPoint={pageX:o.pageX,pageY:o.pageY});var a=function(t,e){return Math.hypot(e.x-t.x,e.y-t.y)},n=a({x:i.pageX,y:i.pageY},{x:o.pageX,y:o.pageY})/a({x:this.startPoint.pageX,y:this.startPoint.pageY},{x:this.endPoint.pageX,y:this.endPoint.pageY}),r=this.originScale*n;this.currentScale=r;var s=this.get("minZoom")||this.graph.get("minZoom"),l=this.get("maxZoom")||this.graph.get("maxZoom");if(!(r>l||r<s)){var c=this.get("animate"),d=this.get("animateCfg"),h=this.graph.get("canvas"),g=h.getPointByClient(t.clientX,t.clientY);this.graph.zoomTo(r,{x:g.x,y:g.y},c,d),this.graph.emit("wheelzoom",t)}}}},onTouchEnd:function(){this.moveable=!1,this.endPoint=null},onWheel:function(t){var e=this,i=this,o=i.graph,a=i.fixSelectedItems;if((!this.shouldBegin||this.shouldBegin.call(this,t))&&this.shouldUpdate.call(this,t)){t.preventDefault();var n=o.get("canvas"),r=n.getPointByClient(t.clientX,t.clientY),s=this.get("sensitivity"),l=o.getZoom(),c=l,d=l;c=t.wheelDelta<0?1-Nt*s:1/(1-Nt*s),d=l*c;var h=this.get("minZoom")||o.get("minZoom"),g=this.get("maxZoom")||o.get("maxZoom");d>g?d=g:d<h&&(d=h);var f=this.get("enableOptimize");if(f){var v=this.get("optimizeZoom"),p=this.get("optimized"),m=o.getNodes(),y=o.getEdges(),b=m.length,S=y.length;if(!p){for(var w=0;w<b;w++){var x=m[w];if(!x.destroyed)for(var k=x.get("group").get("children"),C=k.length,E=0;E<C;E++){var L=k[E];L.destoryed||L.get("isKeyShape")||(L.set("ori-visibility",L.get("ori-visibility")||L.get("visible")),L.hide())}}for(var D=0;D<S;D++){var I=y[D];for(k=I.get("group").get("children"),C=k.length,E=0;E<C;E++){L=k[E];L.set("ori-visibility",L.get("ori-visibility")||L.get("visible")),L.hide()}}this.set("optimized",!0)}clearTimeout(this.get("timeout"));var M=setTimeout((function(){var t=o.getZoom(),i=e.get("optimized");if(i){e.set("optimized",!1);for(var a=0;a<b;a++){var n=m[a],r=n.get("group").get("children"),s=r.length;if(t<v){var l=n.getKeyShape(),c=l.get("ori-visibility");c&&l.show()}else for(var d=0;d<s;d++){var h=r[d];c=h.get("ori-visibility");!h.get("visible")&&c&&c&&h.show()}}for(var g=0;g<S;g++){var u=y[g];r=u.get("group").get("children"),s=r.length;if(t<v){l=u.getKeyShape(),c=l.get("ori-visibility");c&&l.show()}else for(d=0;d<s;d++){h=r[d];if(!h.get("visible")){c=h.get("ori-visibility");c&&h.show()}}}}}),100);this.set("timeout",M)}if(l<=1){var O=void 0,T=void 0;if(a.fixAll||a.fixLineWidth||a.fixLabel){O=o.findAllByState("node",a.fixState),T=o.findAllByState("edge",a.fixState);for(var P=l/d,B=O.length,A=0;A<B;A++){x=O[A];var j=x.getContainer(),U=x.getModel(),F=x.getOriginStyle(),R=x.getStateStyle(a.fixState),W=x.get("shapeFactory").getShape(U.type).getStateStyle(a.fixState,x)[a.fixState];if(a.fixAll){if(d<=1){var N=Object(u["clone"])(j.getMatrix());N||(N=[1,0,0,0,1,0,0,0,1]);var Y=x.getModel(),z=Y.x,G=Y.y;N=Wt(N,[["t",-z,-G],["s",P,P],["t",z,G]]),j.setMatrix(N)}}else for(k=j.get("children"),C=k.length,E=0;E<C;E++){L=k[E];var K=void 0,X=void 0;if(a.fixLabel){var q=L.get("type");if("text"===q){K=L.attr("fontSize")||12;var V=R[L.get("name")],_=W[L.get("name")],Z=V?V.fontSize:12,H=_?_.fontSize:12,J=Z||H||12;if(d<=1&&L.attr("fontSize",J/d),X)break}}if(a.fixLineWidth&&L.get("isKeyShape")){X=L.attr("lineWidth")||0;var Q=R.lineWidth||W.lineWidth||F.lineWidth||0;if(d<=1&&L.attr("lineWidth",Q/d),K)break}}}for(var $=T.length,tt=0;tt<$;tt++)for(I=T[tt],j=I.getContainer(),k=j.get("children"),U=I.getModel(),R=I.getStateStyle(a.fixState),W=I.get("shapeFactory").getShape(U.type).getStateStyle(a.fixState,I)[a.fixState],C=k.length,E=0;E<C;E++){L=k[E],K=void 0,X=void 0;if(a.fixLabel||a.fixAll){q=L.get("type");if("text"===q){K=L.attr("fontSize")||12;V=R[L.get("name")],_=W[L.get("name")],Z=V?V.fontSize:12,H=_?_.fontSize:12,J=Z||H||12;if(d<=1&&L.attr("fontSize",J/d),X)break}}if((a.fixLineWidth||a.fixAll)&&L.get("isKeyShape")){X=L.attr("lineWidth")||0;Q=R.lineWidth||W.lineWidth||1;if(d<=1&&L.attr("lineWidth",Q/d),K)break}}}}var et=this.get("animate"),it=this.get("animateCfg");o.zoomTo(d,{x:r.x,y:r.y},et,it),o.emit("wheelzoom",t)}}},zt={onMouseEnter:function(t){var e=t.item;this.currentTarget=e,this.showTooltip(t),this.graph.emit("tooltipchange",{item:t.item,action:"show"})},onMouseMove:function(t){this.shouldUpdate(t)?this.currentTarget&&t.item===this.currentTarget&&this.updatePosition(t):this.hideTooltip()},onMouseLeave:function(t){this.shouldEnd(t)&&(this.hideTooltip(),this.graph.emit("tooltipchange",{item:this.currentTarget,action:"hide"}),this.currentTarget=null)},showTooltip:function(t){var e=this.container;if(t.item&&!t.item.destroyed){e||(e=this.createTooltip(this.graph.get("canvas")),this.container=e);var i=this.formatText(t.item.get("model"),t);e.innerHTML=i,Object(D["c"])(this.container,{visibility:"visible"}),this.updatePosition(t)}},hideTooltip:function(){Object(D["c"])(this.container,{visibility:"hidden"})},updatePosition:function(t){var e=this.get("shouldBegin"),i=this,o=i.width,a=i.height,n=i.container,r=i.graph;if(e(t)){var s=r.getPointByClient(t.clientX,t.clientY),l=r.getCanvasByPoint(s.x,s.y),c=l.x,d=l.y,h=n.getBoundingClientRect();c>o/2?c-=h.width:c+=this.offset,d>a/2?d-=h.height:d+=this.offset;var g="".concat(c,"px"),u="".concat(d,"px");Object(D["c"])(this.container,{left:g,top:u,visibility:"visible"})}else Object(D["c"])(n,{visibility:"hidden"})},createTooltip:function(t){var e=t.get("el");e.style.position="relative";var i=Object(D["b"])('<div class="g6-tooltip g6-'.concat(this.item,'-tooltip"></div>'));return e.parentNode.appendChild(i),Object(D["c"])(i,{position:"absolute",visibility:"visible"}),this.width=t.get("width"),this.height=t.get("height"),this.container=i,this.graph.get("tooltips").push(i),i}},Gt=Object(r["a"])({getDefaultCfg:function(){return{item:"node",offset:12,formatText:function(t){return t.label}}},getEvents:function(){return{"node:mouseenter":"onMouseEnter","node:mouseleave":"onMouseLeave","node:mousemove":"onMouseMove",afterremoveitem:"onMouseLeave"}}},zt),Kt=Object(r["a"])({getDefaultCfg:function(){return{item:"edge",offset:12,formatText:function(t){return"source: ".concat(t.source," target: ").concat(t.target)}}},getEvents:function(){return{"edge:mouseenter":"onMouseEnter","edge:mouseleave":"onMouseLeave","edge:mousemove":"onMouseMove",afterremoveitem:"onMouseLeave"}}},zt),Xt="click",qt=["click","dblclick"],Vt={getDefaultCfg:function(){return{trigger:Xt,onChange:function(){}}},getEvents:function(){var t,e;return qt.includes(this.trigger)?e=this.trigger:(e=Xt,console.warn("Behavior collapse-expand 的 trigger 参数不合法，请输入 'click' 或 'dblclick'")),t={},t["node:".concat(e)]="onNodeClick",t.touchstart="onNodeClick",t},onNodeClick:function(t){var e=t.item;if(e){var i=this.graph.findDataById(e.get("id"));if(i){var o=i.children;if(o&&0!==o.length){var a=!i.collapsed;this.shouldBegin(t,a)&&(i.collapsed=a,e.getModel().collapsed=a,this.graph.emit("itemcollapsed",{item:t.item,collapsed:a}),this.shouldUpdate(t,a)&&(this.onChange(e,a),this.graph.layout()))}}}}},_t=X.calculationItemsBBox,Zt=function t(e,i){if(!1!==i(e)&&e){var o=e.get("combos");if(0===o.length)return!1;Object(u["each"])(o,(function(e){t(e,i)}))}},Ht={getDefaultCfg:function(){return{enableDelegate:!1,delegateStyle:{},onlyChangeComboSize:!1,activeState:"",selectedState:"selected",enableStack:!0}},getEvents:function(){return{"combo:dragstart":"onDragStart","combo:drag":"onDrag","combo:dragend":"onDragEnd","combo:drop":"onDrop","node:drop":"onNodeDrop","combo:dragenter":"onDragEnter","combo:dragleave":"onDragLeave"}},validationCombo:function(t){var e=t.item;if(!e||e.destroyed)return!1;if(!this.shouldUpdate.call(this,t))return!1;var i=e.getType();return"combo"===i},onDragStart:function(t){var e=this,i=this.graph,o=t.item;if(this.currentShouldEnd=!0,this.validationCombo(t)){this.targets=[];var a=i.findAllByState("combo",this.selectedState),n=o.get("id"),r=a.filter((function(t){var e=t.get("id");return n===e}));0===r.length?this.targets.push(o):this.targets=a,this.activeState&&this.targets.map((function(t){var o=t.getModel();if(o.parentId){var a=i.findById(o.parentId);a&&i.setItemState(a,e.activeState,!0)}})),this.point={},this.originPoint={},this.origin={x:t.x,y:t.y},this.currentItemChildCombos=[],Zt(o,(function(t){if(t.destroyed)return!1;var i=t.getModel();return e.currentItemChildCombos.push(i.id),!0}))}},onDrag:function(t){var e=this;if(this.origin&&this.validationCombo(t))if(this.enableDelegate)this.updateDelegate(t);else{if(this.activeState){var i=this.graph,o=t.item,a=o.getModel(),n=i.getCombos(),r=o.getBBox(),s=r.centerX,l=r.centerY,c=r.width,d=n.filter((function(t){var i=t.getModel();return a.parentId,i.id!==a.id&&!e.currentItemChildCombos.includes(i.id)}));d.map((function(t){var o=t.getBBox(),a=o.centerX,n=o.centerY,r=o.width,d=s-a,h=l-n,g=2*Math.sqrt(d*d+h*h);c+r-g>.8*c?i.setItemState(t,e.activeState,!0):i.setItemState(t,e.activeState,!1)}))}Object(u["each"])(this.targets,(function(i){e.updateCombo(i,t)}))}},updatePositions:function(t,e){var i=this;(this.enableDelegate||e)&&Object(u["each"])(this.targets,(function(o){i.updateCombo(o,t,e)}))},onDrop:function(t){var e=this,i=t.item;if(this.currentShouldEnd=this.shouldEnd.call(this,t,i),this.updatePositions(t,!this.currentShouldEnd),this.currentShouldEnd&&i&&this.targets&&!i.destroyed){var o=this.graph,a=i.getModel();this.targets.map((function(t){var n=t.getModel();n.parentId!==a.id?(e.activeState&&o.setItemState(i,e.activeState,!1),e.onlyChangeComboSize?o.updateCombo(t):o.updateComboTree(t,a.id,!1)):o.updateCombo(i)})),this.end(i,t),this.endComparison=!0}},onNodeDrop:function(t){var e=this;if(this.targets&&0!==this.targets.length){var i=this.graph,o=t.item,a=o.getModel().comboId,n=a?i.findById(a):void 0;if(this.currentShouldEnd=this.shouldEnd.call(this,t,n),this.updatePositions(t,!this.currentShouldEnd),this.currentShouldEnd){var r;if(a){if(this.activeState){var s=i.findById(a);i.setItemState(s,this.activeState,!1)}this.targets.map((function(t){e.onlyChangeComboSize?i.updateCombo(t):a!==t.getID()&&(r=i.findById(a),a!==t.getModel().parentId&&i.updateComboTree(t,a,!1))}))}else this.targets.map((function(t){if(e.onlyChangeComboSize)i.updateCombo(t);else{var o=t.getModel();o.comboId&&i.updateComboTree(t,void 0,!1)}}));this.endComparison=!0,this.end(r,t)}}},onDragEnter:function(t){if(this.origin&&this.validationCombo(t)){var e=t.item,i=this.graph;this.activeState&&i.setItemState(e,this.activeState,!0)}},onDragLeave:function(t){if(this.origin&&this.validationCombo(t)){var e=t.item,i=this.graph;this.activeState&&i.setItemState(e,this.activeState,!1)}},onDragEnd:function(t){if(this.targets&&0!==this.targets.length){var e=t.item;this.currentShouldEnd&&this.updatePositions(t);var i=this.getParentCombo(e.getModel().parentId),o=this.graph;i&&this.activeState&&o.setItemState(i,this.activeState,!1),this.end(void 0,t)}},end:function(t,e){var i=this;if(this.origin){var o=this.graph;if(this.delegateShape){var a=o.get("delegateGroup");a.clear(),this.delegateShape=null}if(t&&this.activeState&&o.setItemState(t,this.activeState,!1),!t){var n=o.get("enabledStack")&&this.enableStack;this.targets.map((function(t){i.onlyChangeComboSize?o.updateCombo(t):o.updateComboTree(t,void 0,n)}))}this.point=[],this.origin=null,this.originPoint=null,this.targets.length=0}},traverse:function(t,e){var i=this;if(!1!==e(t)&&t){var o=t.get("combos");Object(u["each"])(o,(function(t){i.traverse(t,e)}));var a=t.get("nodes");Object(u["each"])(a,(function(t){i.traverse(t,e)}))}},updateCombo:function(t,e,i){var o=this;this.traverse(t,(function(t){return!t.destroyed&&(o.updateSingleItem(t,e,i),!0)}))},updateSingleItem:function(t,e,i){var o,a=this.origin,n=this.graph,r=t.getModel(),s=t.get("id");this.point[s]||(this.point[s]={x:r.x,y:r.y});var l=e.x-a.x+this.point[s].x,c=e.y-a.y+this.point[s].y;i&&(l+=a.x-e.x,c+=a.y-e.y),n.updateItem(t,{x:l,y:c},!1),null===(o=t.getEdges())||void 0===o||o.forEach((function(t){return t.refresh()}))},getParentCombo:function(t){var e=this.graph;if(t){var i=e.findById(t);if(i)return i}},updateDelegate:function(t){var e=this.graph;if(this.delegateShape){var i=t.x-this.origin.x+this.originPoint.minX,o=t.y-this.origin.y+this.originPoint.minY;this.delegateShape.attr({x:i,y:o})}else{var a=e.get("delegateGroup"),n=null;n=this.targets.length>1?_t(this.targets):this.targets[0].getBBox();var s=n.x,l=n.y,c=n.width,d=n.height,h=n.minX,g=n.minY;this.originPoint={x:s,y:l,width:c,height:d,minX:h,minY:g};var u=Object(r["a"])(Object(r["a"])({},L.delegateStyle),this.delegateStyle);this.delegateShape=a.addShape("rect",{attrs:Object(r["a"])({width:n.width,height:n.height,x:n.x,y:n.y},u),name:"combo-delegate-shape"}),this.delegateShape.set("capture",!1),this.delegate=this.delegateShape}}},Jt="dblclick",Qt=["click","dblclick"],$t={getDefaultCfg:function(){return{trigger:Jt,relayout:!0}},getEvents:function(){var t,e;return Qt.includes(this.trigger)?e=this.trigger:(e=Jt,console.warn("Behavior collapse-expand-group 的 trigger 参数不合法，请输入 'click' 或 'dblclick'")),t={},t["combo:".concat(e)]="onComboClick",t},onComboClick:function(t){var e=t.item,i=this,o=i.graph,a=i.relayout;if(e&&!e.destroyed&&"combo"===e.getType()){var n=e.getModel(),r=n.id;r&&(o.collapseExpandCombo(r),a&&o.get("layout")?o.layout():o.refreshPositions())}}},te=X.isPolygonsIntersect,ee=X.pathToPoints,ie="shift",oe=["drag","shift","ctrl","alt","control"],ae=function(t,e){var i,o=t.getKeyShape();if("path"===t.get("type"))i=ee(o.attr("path"));else{var a=o.getCanvasBBox();i=[[a.minX,a.minY],[a.maxX,a.minY],[a.maxX,a.maxY],[a.minX,a.maxY]]}return te(e,i)},ne={getDefaultCfg:function(){return{delegateStyle:{fill:"#EEF6FF",fillOpacity:.4,stroke:"#DDEEFE",lineWidth:1},onSelect:function(){},onDeselect:function(){},shouldDeselect:void 0,selectedState:"selected",trigger:ie,includeEdges:!0,selectedEdges:[],selectedNodes:[]}},getEvents:function(){return oe.indexOf(this.trigger.toLowerCase())>-1||(this.trigger=ie,console.warn("Behavior lasso-select 的 trigger 参数不合法，请输入 'drag'、'shift'、'ctrl' 或 'alt'")),"drag"===this.trigger?{dragstart:"onDragStart",drag:"onDragMove",dragend:"onDragEnd","canvas:click":"clearStates"}:{dragstart:"onDragStart",drag:"onDragMove",dragend:"onDragEnd",keyup:"onKeyUp",keydown:"onKeyDown","canvas:click":"clearStates"}},onDragStart:function(t){var e=this.lasso,i=t.item;i||("drag"===this.trigger||this.keydown)&&(this.selectedNodes&&0!==this.selectedNodes.length&&this.clearStates("dragstart"),e||(e=this.createLasso()),this.dragging=!0,this.originPoint={x:t.x,y:t.y},this.points.push(this.originPoint),e.show())},onDragMove:function(t){this.dragging&&("drag"===this.trigger||this.keydown)&&(this.points.push({x:t.x,y:t.y}),this.updateLasso(t))},onDragEnd:function(t){(this.lasso||this.dragging)&&("drag"===this.trigger||this.keydown)&&(this.points.push(this.originPoint),this.getSelectedItems(),this.lasso.remove(!0),this.lasso=null,this.points=[],this.dragging=!1)},getLassoPath:function(){var t=this.points,e=[];return t.length&&(t.forEach((function(t,i){0===i?e.push(["M",t.x,t.y]):e.push(["L",t.x,t.y])})),e.push(["L",t[0].x,t[0].y])),e},clearStates:function(t){void 0===t&&(t="canvas:click");var e=this,i=e.graph,o=e.selectedState,a=e.shouldDeselect,n=i.findAllByState("node",o),r=i.findAllByState("edge",o);a&&!a({action:t,nodes:n,edges:r})||(n.forEach((function(t){return i.setItemState(t,o,!1)})),r.forEach((function(t){return i.setItemState(t,o,!1)}))),this.onDeselect&&this.onDeselect(this.selectedNodes,this.selectedEdges),this.selectedNodes=[],this.selectedEdges=[],i.emit("nodeselectchange",{selectedItems:{nodes:[],edges:[]},select:!1})},getSelectedItems:function(){var t=this,e=this,i=e.graph,o=e.shouldUpdate,a=this.points.map((function(t){return[i.getCanvasByPoint(t.x,t.y).x,i.getCanvasByPoint(t.x,t.y).y]})),n=this.selectedState,r=[],s=[];i.getNodes().forEach((function(t){if(t.isVisible()&&ae(t,a)&&o(t,"select")){r.push(t);var e=t.getModel();s.push(e.id),i.setItemState(t,n,!0)}}));var l=[];this.includeEdges&&r.forEach((function(e){var a=e.getOutEdges();a.forEach((function(e){if(e.isVisible()){var a=e.getModel(),n=a.source,r=a.target;s.includes(n)&&s.includes(r)&&o(e,"select")&&(l.push(e),i.setItemState(e,t.selectedState,!0))}}))})),this.selectedEdges=l,this.selectedNodes=r,this.onSelect&&this.onSelect(r,l),i.emit("nodeselectchange",{selectedItems:{nodes:r,edges:l},select:!0})},createLasso:function(){var t=this,e=t.graph.get("delegateGroup").addShape("path",{attrs:Object(r["a"])({path:[]},t.delegateStyle),capture:!1,name:"lasso-shape"});return this.lasso=e,this.delegate=e,this.points=[],e},updateLasso:function(t){var e=this;this.lasso.attr({path:e.getLassoPath()})},onKeyDown:function(t){var e=t.key;e&&(e.toLowerCase()===this.trigger.toLowerCase()?this.keydown=!0:this.keydown=!1)},onKeyUp:function(){this.lasso&&(this.lasso.remove(!0),this.lasso=null,this.points=[],this.dragging=!1),this.keydown=!1}},re="click",se=["click","drag"],le=void 0,ce=["shift","ctrl","control","alt","meta",void 0],de={getDefaultCfg:function(){return{trigger:re,key:le,edgeConfig:{},getEdgeConfig:void 0}},getEvents:function(){var t,e=this;return se.indexOf(e.trigger.toLowerCase())>-1||(e.trigger=re,console.warn("Behavior create-edge 的 trigger 参数不合法，请输入 'click'，'drag'")),e.key&&-1===ce.indexOf(e.key.toLowerCase())&&(e.trigger=le,console.warn("Behavior create-edge 的 key 参数不合法，请输入 'shift'，'ctrl'，'alt'，'control'，或 undefined")),"drag"===e.trigger?t={"node:dragstart":"onClick","combo:dragstart":"onClick",drag:"updateEndPoint","node:drop":"onClick","combo:drop":"onClick",dragend:"onDragEnd"}:"click"===e.trigger&&(t={"node:click":"onClick",mousemove:"updateEndPoint","edge:click":"cancelCreating","canvas:click":"cancelCreating","combo:click":"onClick"}),e.key&&(t.keydown="onKeyDown",t.keyup="onKeyUp"),t},onDragEnd:function(t){var e=this;if(!e.key||e.keydown){var i=t.item;i&&i.getID()!==e.source&&"node"===i.getType()||e.cancelCreating({item:e.edge,x:t.x,y:t.y})}},onClick:function(t){var e=this;if(!e.key||e.keydown){var i=t.item,o=e.graph,a=i.getModel(),n=e.getEdgeConfig;if(e.addingEdge&&e.edge){if(!e.shouldEnd.call(e,t))return;var s=void 0;s=n&&Object(u["isFunction"])(n)?n({source:e.source,target:a.id}):e.edgeConfig;var l=Object(r["a"])({target:a.id},s);if(e.source===a.id&&(l.type="loop"),o.emit("beforecreateedge",{}),o.updateItem(e.edge,l,!1),o.get("enabledStack")){var c=Object(r["a"])(Object(r["a"])({},e.edge.getModel()),{itemType:"edge"}),d={};d.edges=[c],o.pushStack("add",{before:{},after:d})}o.emit("aftercreateedge",{edge:e.edge}),e.edge.getKeyShape().set("capture",!0),e.edge=null,e.addingEdge=!1}else{if(!e.shouldBegin.call(e,t))return;s=void 0;s=n&&Object(u["isFunction"])(n)?n({source:a.id,target:a.id}):e.edgeConfig,e.edge=o.addItem("edge",Object(r["a"])({source:a.id,target:a.id},s),!1),e.source=a.id,e.addingEdge=!0,e.edge.getKeyShape().set("capture",!1)}}},updateEndPoint:function(t){var e=this;if(!e.key||e.keydown){e.edge&&e.edge.destroyed&&e.cancelCreating({item:e.edge});var i={x:t.x,y:t.y};e.graph.findById(e.source)?e.addingEdge&&e.edge&&e.graph.updateItem(e.edge,{target:i},!1):e.addingEdge=!1}},cancelCreating:function(t){var e,i,o=this;if(!o.key||o.keydown){var a=o.graph,n=t.item;return o.addingEdge&&(o.edge===n||(null===(i=null===(e=t.target)||void 0===e?void 0:e.isCanvas)||void 0===i?void 0:i.call(e)))?(o.edge&&!o.edge.destroyed&&a.removeItem(o.edge,!1),o.edge=null,void(o.addingEdge=!1)):void 0}},onKeyDown:function(t){var e=this,i=t.key;i&&(i.toLowerCase()===e.key.toLowerCase()?e.keydown=!0:e.keydown=!1)},onKeyUp:function(){var t=this;t.addingEdge&&t.edge&&(t.graph.removeItem(t.edge,!1),t.addingEdge=!1,t.edge=null),this.keydown=!1}},he="ctrl",ge=["shift","ctrl","alt","control"],ue="1",fe={getDefaultCfg:function(){return{trigger:he,combinedKey:ue,functionName:"fitView",functionParams:[]}},getEvents:function(){return ge.indexOf(this.trigger.toLowerCase())>-1||(this.trigger=he,console.warn("Behavior shortcuts-fit-view 的 trigger 参数 '".concat(this.trigger,"' 不合法，请输入 'drag'、'shift'、'ctrl' 或 'alt'"))),this.combinedKey===this.trigger&&(this.combinedKey=void 0),{keyup:"onKeyUp",keydown:"onKeyDown"}},onKeyDown:function(t){var e=t.key;if(e){var i=this.trigger.toLowerCase(),o=e.toLowerCase();this.triggerKeydown||(this.triggerKeydown=o===i||"control"===o&&"ctrl"===i||"ctrl"===o&&"control"===i);var a=this.graph;if(!a[this.functionName])return console.warn("Behavior shortcuts-fit-view 的 functionName 参数 '".concat(this.functionName,"' 不合法，它不是 Graph 的一个函数名")),{};if(!this.triggerKeydown||this.combinedKey){var n=this.combinedKey.toLowerCase();this.triggerKeydown&&(o===n||"control"===o&&"ctrl"===n||"ctrl"===o&&"control"===n)&&(this.functionParams&&this.functionParams.length?a[this.functionName].apply(a,this.functionParams):a[this.functionName]())}else this.functionParams&&this.functionParams.length?a[this.functionName].apply(a,this.functionParams):a[this.functionName]()}},onKeyUp:function(){this.brush&&(this.brush.remove(!0),this.brush=null,this.dragging=!1),this.triggerKeydown=!1}},ve=["shift","ctrl","alt","control","meta"],pe={getDefaultCfg:function(){return{direction:"both",enableOptimize:!1,zoomKey:"ctrl",scalableRange:0}},getEvents:function(){return this.zoomKey&&-1!==ve.indexOf(this.zoomKey)||(this.zoomKey="ctrl"),{wheel:"onWheel"}},onWheel:function(t){var e=this,i=this.graph,o=Array.isArray(this.zoomKey)?[].concat(this.zoomKey):[this.zoomKey];o.includes("control")&&o.push("ctrl");var a=o.some((function(e){return t["".concat(e,"Key")]}));if(a){var n=i.get("canvas"),r=n.getPointByClient(t.clientX,t.clientY),s=i.getZoom();t.wheelDelta>0?s+=.05*s:s-=.05*s,i.zoomTo(s,{x:r.x,y:r.y})}else{var l=t.deltaX||t.movementX,c=t.deltaY||t.movementY;!c&&navigator.userAgent.indexOf("Firefox")>-1&&(c=125*-t.wheelDelta/3);var d=this.graph.get("width"),h=this.graph.get("height"),g=this.graph.get("canvas").getCanvasBBox(),u=this.scalableRange,f=this.scalableRange;u<1&&u>-1&&(u*=d,f*=h);var v=g.minX,p=g.maxX,m=g.minY,y=g.maxY;l>0?p<-u?l=0:p-l<-u&&(l=p+u):l<0&&(v>d+u?l=0:v-l>d+u&&(l=v-(d+u))),c>0?y<-f?c=0:y-c<-f&&(c=y+f):c<0&&(m>h+f?c=0:m-c>h+f&&(c=m-(h+f))),"x"===this.get("direction")?c=0:"y"===this.get("direction")&&(l=0),i.translate(-l,-c)}t.preventDefault();var b=this.get("enableOptimize");if(b){var S=this.get("optimizeZoom"),w=this.get("optimized"),x=i.getNodes(),k=i.getEdges(),C=x.length,E=k.length;if(!w){for(var L=0;L<C;L++){var D=x[L];if(!D.destroyed)for(var I=D.get("group").get("children"),M=I.length,O=0;O<M;O++){var T=I[O];T.destoryed||T.get("isKeyShape")||(T.set("ori-visibility",T.get("ori-visibility")||T.get("visible")),T.hide())}}for(var P=0;P<E;P++){var B=k[P];for(I=B.get("group").get("children"),M=I.length,O=0;O<M;O++){T=I[O];T.set("ori-visibility",T.get("ori-visibility")||T.get("visible")),T.hide()}}this.set("optimized",!0)}clearTimeout(this.get("timeout"));var A=setTimeout((function(){var t=i.getZoom(),o=e.get("optimized");if(o){e.set("optimized",!1);for(var a=0;a<C;a++){var n=x[a],r=n.get("group").get("children"),s=r.length;if(t<S){var l=n.getKeyShape(),c=l.get("ori-visibility");c&&l.show()}else for(var d=0;d<s;d++){var h=r[d];c=h.get("ori-visibility");!h.get("visible")&&c&&c&&h.show()}}for(var g=0;g<E;g++){var u=k[g];r=u.get("group").get("children"),s=r.length;if(t<S){l=u.getKeyShape(),c=l.get("ori-visibility");c&&l.show()}else for(d=0;d<s;d++){h=r[d];if(!h.get("visible")){c=h.get("ori-visibility");c&&h.show()}}}}}),100);this.set("timeout",A)}}},me={"drag-canvas":Dt,"zoom-canvas":Yt,"drag-node":It,"activate-relations":Mt,"brush-select":jt,"click-select":Rt,"lasso-select":ne,tooltip:Gt,"edge-tooltip":Kt,"collapse-expand":Vt,"drag-combo":Ht,"collapse-expand-combo":$t,"create-edge":de,"shortcuts-call":fe,"scroll-canvas":pe};Object(u["each"])(me,(function(t,e){Object(s["i"])(e,t)}));var ye=Object(r["a"])(Object(r["a"])({},l),c),be=(wt.Grid,wt.Minimap,wt.Bundling,wt.Menu,wt.Fisheye),Se=(wt.ToolBar,wt.Tooltip,wt.TimeBar),we=wt.ImageMinimap,xe=wt.EdgeFilterLens,ke=wt.SnapLine;wt.Legend,e["a"]={version:L.version,Graph:vt,TreeGraph:bt,Util:X,Layout:P["Layouts"],TreeLayout:_,registerLayout:Z,Global:L,registerBehavior:s["i"],registerCombo:s["j"],registerEdge:s["k"],registerNode:s["l"],Minimap:wt.Minimap,Grid:wt.Grid,Bundling:wt.Bundling,Menu:wt.Menu,ToolBar:wt.ToolBar,Tooltip:wt.Tooltip,Legend:wt.Legend,TimeBar:Se,SnapLine:ke,Fisheye:be,ImageMinimap:we,EdgeFilterLens:xe,Algorithm:ye,Arrow:s["d"],Marker:s["f"],Shape:s["g"]}}}]);