(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5eaee012"],{"036d":function(t,e,a){"use strict";a("f02e")},"063d":function(t,e,a){"use strict";a("c0f7")},"0a51":function(t,e,a){},"0abe":function(t,e,a){},"148b":function(t,e,a){},1689:function(t,e,a){"use strict";a("c52d")},1821:function(t,e,a){"use strict";a("802b")},"18af":function(t,e,a){},"1cf4":function(t,e,a){"use strict";a("6f6e")},"1fd8":function(t,e,a){},"20be":function(t,e,a){"use strict";a("9ecb")},"22e2":function(t,e,a){"use strict";var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"bottom-content"},[t.dragData.hideElementName?t._e():a("div",{staticClass:"styleSet-list-div"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("featureSetting.style.elementType")))]),a("div",{staticClass:"items-center"},[a("CommonSVG",{attrs:{"icon-class":t.styleFormDatas.dataType,size:18}}),a("span",{staticClass:"margin-left-5 nowrap"},[t._v(t._s(t.styleFormDatas.typeName))])],1)]),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("featureSetting.style.elementName")))]),a("el-input",{class:{"is-error":t.inputError&&""==t.styleFormDatas.name},attrs:{size:"mini",readonly:t.needDisbleNameInputType.includes(t.dragData.type),placeholder:t.$t("featureSetting.style.placeholder")},model:{value:t.styleFormDatas.name,callback:function(e){t.$set(t.styleFormDatas,"name","string"===typeof e?e.trim():e)},expression:"styleFormDatas.name"}})],1)]),"model"==t.dragData.type?a("ModelSetting",{ref:"model",attrs:{elementTransformObj:t.elementTransformObj,currentElement:t.dragData},on:{handleModelTransform:t.handleModelTransform,inputChangeRotation:t.inputChangeRotation,inputChangeOrigin:t.inputChangeOrigin,inputChangeOffset:t.inputChangeOffset,clickCheckedCoordinate:t.clickCheckedCoordinate}}):t._e(),"dem"==t.dragData.type?a("DemSetting",{ref:"dem",attrs:{currentElement:t.dragData}}):t._e(),"_3dBuilding"==t.dragData.type?a("BuildingSetting",{ref:"_3dBuilding",attrs:{currentElement:t.dragData}}):t._e(),"gltf"==t.dragData.type||"fbx"==t.dragData.type?a("GltfFbxSetting",{ref:t.dragData.type,attrs:{elementTransformObj:t.elementTransformObj,currentElement:t.dragData},on:{handleModelTransform:t.handleModelTransform,inputChangeRotation:t.inputChangeRotation,inputChangeOrigin:t.inputChangeOrigin,inputChangeOffset:t.inputChangeOffset,inputChangeScale:t.inputChangeScale,clickCheckedCoordinate:t.clickCheckedCoordinate}}):t._e(),"_3dTiles"==t.dragData.type?a("TilesSetting",{ref:"_3dTiles",attrs:{elementTransformObj:t.elementTransformObj,currentElement:t.dragData},on:{clickCheckedCoordinate:t.clickCheckedCoordinate,handleModelTransform:t.handleModelTransform,inputChangeRotation:t.inputChangeRotation,inputChangeOrigin:t.inputChangeOrigin,inputChangeOffset:t.inputChangeOffset}}):t._e(),"panorama"==t.dragData.type?a("PanoramaSetting",{ref:"panorama",attrs:{elementTransformObj:t.elementTransformObj,currentElement:t.dragData},on:{clickCheckedCoordinate:t.clickCheckedCoordinate,handleModelTransform:t.handleModelTransform,inputChangeOrigin:t.inputChangeOrigin,inputChangeOffset:t.inputChangeOffset}}):t._e(),"video"==t.dragData.type?a("VideoSetting",{ref:"video",attrs:{currentElement:t.dragData}}):t._e(),"polygon"==t.dragData.type?a("PolygonSetting",{ref:"polygon",attrs:{currentElement:t.dragData}}):t._e(),"ripplewall"==t.dragData.type?a("RipplewallSetting",{ref:"ripplewall",attrs:{currentElement:t.dragData},on:{inputChangeOrigin:t.inputChangeOrigin}}):t._e(),"radar"==t.dragData.type?a("RadarSetting",{ref:"radar",attrs:{elementTransformObj:t.elementTransformObj,currentElement:t.dragData},on:{clickCheckedCoordinate:t.clickCheckedCoordinate,handleModelTransform:t.handleModelTransform,inputChangeOrigin:t.inputChangeOrigin,inputChangeOffset:t.inputChangeOffset}}):t._e(),"shield"==t.dragData.type?a("ShieldSetting",{ref:"shield",attrs:{elementTransformObj:t.elementTransformObj,currentElement:t.dragData},on:{clickCheckedCoordinate:t.clickCheckedCoordinate,handleModelTransform:t.handleModelTransform,inputChangeOrigin:t.inputChangeOrigin,inputChangeOffset:t.inputChangeOffset}}):t._e(),"ring"==t.dragData.type?a("RingSetting",{ref:"ring",attrs:{elementTransformObj:t.elementTransformObj,currentElement:t.dragData},on:{clickCheckedCoordinate:t.clickCheckedCoordinate,handleModelTransform:t.handleModelTransform,inputChangeOrigin:t.inputChangeOrigin,inputChangeOffset:t.inputChangeOffset}}):t._e(),"flame"==t.dragData.type||"smoke"==t.dragData.type?a("SmokeFlameSetting",{ref:t.dragData.type,attrs:{elementTransformObj:t.elementTransformObj,currentElement:t.dragData},on:{clickCheckedCoordinate:t.clickCheckedCoordinate,handleModelTransform:t.handleModelTransform,inputChangeOrigin:t.inputChangeOrigin,inputChangeOffset:t.inputChangeOffset}}):t._e(),"polyline"==t.dragData.type?a("PolylineSetting",{ref:"polyline",attrs:{currentElement:t.dragData}}):t._e(),"vectorextrude"==t.dragData.type?a("VectorExtrudeSetting",{ref:"vectorextrude",attrs:{currentElement:t.dragData}}):t._e(),"wmts"==t.dragData.type||"wms"==t.dragData.type||"tms"==t.dragData.type?a("WmtsWmsTmsSetting",{ref:t.dragData.type,attrs:{currentElement:t.dragData}}):t._e(),"shp"==t.dragData.type?a("ShpSetting",{ref:"shp",attrs:{currentElement:t.dragData}}):t._e(),"kml"==t.dragData.type?a("KmlSetting",{ref:"kml",attrs:{currentElement:t.dragData}}):t._e(),"geoJSON"==t.dragData.type?a("GeoJSONSetting",{ref:"geoJSON",attrs:{currentElement:t.dragData}}):t._e(),"heatmap"==t.dragData.type?a("HeatmapSetting",{ref:"heatmap",attrs:{currentElement:t.dragData},on:{inputChangeOrigin:t.inputChangeOrigin}}):t._e(),"batch-extrude"==t.dragData.type?a("BatchExtrudeSetting",{ref:"batch-extrude",attrs:{currentElement:t.dragData}}):t._e(),"annotation"==t.dragData.type||"billboard"==t.dragData.type?a("AnchorPointSetting",{ref:t.dragData.type,attrs:{elementTransformObj:t.elementTransformObj,dragData:t.dragData},on:{clickCheckedCoordinate:t.clickCheckedCoordinate,inputChangeOrigin:t.inputChangeOrigin,inputChangeOffset:t.inputChangeOffset}}):t._e(),"advanced_envmap"==t.dragData.type?a("advancedEnvmap",{ref:"advanced_envmap"}):t._e(),"advanced_setting"==t.dragData.type?a("advancedSetting",{ref:"advanced_setting"}):t._e(),"pathAnimation"==t.dragData.type?a("PathAnimation",{ref:"pathAnimation"}):t._e(),"waterfall"==t.dragData.type?a("waterFall",{ref:t.dragData.type,attrs:{elementTransformObj:t.elementTransformObj,currentElement:t.dragData},on:{clickCheckedCoordinate:t.clickCheckedCoordinate,handleModelTransform:t.handleModelTransform,inputChangeOrigin:t.inputChangeOrigin,inputChangeOffset:t.inputChangeOffset}}):t._e(),"projector"==t.dragData.type?a("Projector",{ref:"projector",attrs:{currentElement:t.dragData}}):t._e(),"snow"==t.dragData.type?a("SnowSetting",{ref:"snow",attrs:{currentElement:t.dragData},on:{clickCheckedCoordinate:t.clickCheckedCoordinate,handleModelTransform:t.handleModelTransform,inputChangeOrigin:t.inputChangeOrigin,inputChangeOffset:t.inputChangeOffset}}):t._e(),"tailline"==t.dragData.type?a("TailLineSetting",{ref:"tailline",attrs:{currentElement:t.dragData}}):t._e(),""!=t.elementSourceDialogState?a("ElementImageSourceDialog",{attrs:{datas:t.elementSourceDialogState},on:{checkedElementSource:t.checkedElementSource,close:function(e){t.elementSourceDialogState=""}}}):t._e()],1)},i=[],n=(a("d3b7"),a("3ca3"),a("ddb0"),a("4de4"),a("7db0"),a("b0c0"),a("e9c4"),a("d81d"),a("99af"),a("18a5"),a("a630"),a("159b"),a("a434"),a("b893")),l=a("0e1b"),o=a("aade"),r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("dialogComp",{attrs:{hideHeader:!1,needClose:"true",zIndex:"4",position:"fixed",draw:!0,right:650,drag:!0,title:t.$t("dialog.materialLibrary.name"),icon:"icon-details",width:410,height:325,type:"detailInfo",top:10},on:{close:t.closeDialog},scopedSlots:t._u([{key:"center",fn:function(){return[a("div",{staticClass:"resources-label-container"},[a("div",{staticClass:"label-content"},t._l(t.imgList,(function(e){return a("div",{key:e.id,staticClass:"label-item cursor-btn"},[a("div",{staticClass:"thumb",on:{click:function(a){return t.checkCurrent(e)}}},[a("img",{attrs:{src:e.thumb,alt:""}})]),a("p",{staticClass:"title"},[t._v(t._s(e.title))])])})),0)])]},proxy:!0}])})},c=[],u=(a("ac1f"),a("5319"),a("1276"),{name:"ElementImageSourceDialog",props:["datas"],data:function(){return{imgList:[]}},computed:{},created:function(){this.getPublicImg()},methods:{getPublicImg:function(){var t=this,e=this.datas,s=null;switch(e){case"gltf":s=a("1492");break;case"fbx":s=a("0d5b");break;case"environment":s=a("ee57");break}s.keys().forEach((function(a,s){var i=a.replace(/(.*\/)*([\s\S]*?)/gi,"$2"),n=i.split(".")[0],l="".concat("","/image/").concat(e,"/").concat(i);if("environment"==e)t.imgList.push({id:s,title:n,thumb:l});else{var o="/".concat(e,"/").concat(n,".").concat(e);t.imgList.push({id:s,title:n,thumb:l,fileSrc:o})}}))},closeDialog:function(){this.$emit("close")},checkCurrent:function(t){this.$emit("checkedElementSource",t),this.closeDialog()}}}),m=u,d=(a("43ef"),a("2877")),p=Object(d["a"])(m,r,c,!1,null,"1e0da5ca",null),h=p.exports,f=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex"},[a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("featureSetting.style.model.version.title")))]),a("el-select",{attrs:{size:"mini",placeholder:t.$t("featureSetting.style.model.version.placeholder")},model:{value:t.elementDatas.list[0].phaseValue,callback:function(e){t.$set(t.elementDatas.list[0],"phaseValue",e)},expression:"elementDatas.list[0].phaseValue"}},t._l(t.elementDatas.list[0].phaseOption,(function(e){return a("el-option-group",{key:e},t._l(e.options,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)})),1)],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.featureID.label")))]),a("el-input",{attrs:{size:"mini",readonly:!t.currentElement.setFeatureID,placeholder:t.$t("formRelational.featureID.placeholder")},model:{value:t.elementDatas.id,callback:function(e){t.$set(t.elementDatas,"id",e)},expression:"elementDatas.id"}})],1)]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{staticClass:"space-between-center",attrs:{span:12}},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.basePoint.label")))]),a("el-tooltip",{attrs:{effect:"dark",content:t.$t("formRelational.basePoint.placeholder"),placement:"top"}},[a("span",{staticClass:"outline-none cursor-btn",on:{click:function(e){return t.clickCheckedCoordinate()}}},[a("CommonSVG",{attrs:{color:"var(--theme)",size:"18","icon-class":"select_coord"}})],1)])],1),a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.longitude,expression:"elementDatas.longitude"}],class:{"is-error":t.inputError&&!t.styleFormDatas.validate.longitude},attrs:{type:"number",size:"mini",placeholder:t.$t("formRelational.longitude.placeholder")},on:{input:function(e){return t.inputChangeOrigin(e,"longitude")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v(t._s(t.$t("formRelational.longitude.label")))])]},proxy:!0}]),model:{value:t.elementDatas.longitude,callback:function(e){t.$set(t.elementDatas,"longitude",e)},expression:"elementDatas.longitude"}})],1)],1),a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.latitude,expression:"elementDatas.latitude"}],class:{"is-error":t.inputError&&!t.styleFormDatas.validate.latitude},attrs:{type:"number",size:"mini",placeholder:t.$t("formRelational.latitude.placeholder")},on:{input:function(e){return t.inputChangeOrigin(e,"latitude")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v(t._s(t.$t("formRelational.latitude.label")))]},proxy:!0}]),model:{value:t.elementDatas.latitude,callback:function(e){t.$set(t.elementDatas,"latitude",e)},expression:"elementDatas.latitude"}})],1),a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.altitude,expression:"elementDatas.altitude"}],class:{"is-error":t.inputError&&!t.styleFormDatas.validate.altitude},attrs:{type:"number",size:"mini",placeholder:t.$t("formRelational.altitude.placeholder")},on:{input:function(e){return t.inputChangeOrigin(e,"altitude")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v(t._s(t.$t("formRelational.altitude.label")))])]},proxy:!0}]),model:{value:t.elementDatas.altitude,callback:function(e){t.$set(t.elementDatas,"altitude",e)},expression:"elementDatas.altitude"}})],1)],1)],1),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-rotation"},[a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{staticClass:"space-between-center",attrs:{span:12}},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.rotation.label")))]),a("el-tooltip",{attrs:{effect:"dark",content:t.$t("formRelational.rotation.tooltip"),placement:"top"}},[a("span",{staticClass:"outline-none cursor-btn",on:{click:function(e){return t.handleModelTransform("rotate")}}},[a("CommonSVG",{attrs:{color:"var(--theme)",size:"16","icon-class":"rotating_feature"}})],1)])],1),a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.rotation[0],expression:"elementDatas.rotation[0]"}],attrs:{type:"number",size:"mini",placeholder:t.$t("formRelational.rotation.placeholderX")},on:{input:function(e){return t.inputChangeRotation(e,"x")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v(t._s(t.$t("formRelational.rotation.x")))]},proxy:!0}]),model:{value:t.elementDatas.rotation[0],callback:function(e){t.$set(t.elementDatas.rotation,0,e)},expression:"elementDatas.rotation[0]"}})],1)],1),a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.rotation[1],expression:"elementDatas.rotation[1]"}],attrs:{type:"number",size:"mini",placeholder:t.$t("formRelational.rotation.placeholderY")},on:{input:function(e){return t.inputChangeRotation(e,"y")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v(t._s(t.$t("formRelational.rotation.y")))]},proxy:!0}]),model:{value:t.elementDatas.rotation[1],callback:function(e){t.$set(t.elementDatas.rotation,1,e)},expression:"elementDatas.rotation[1]"}})],1),a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.rotation[2],expression:"elementDatas.rotation[2]"}],attrs:{type:"number",size:"mini",placeholder:t.$t("formRelational.rotation.placeholderZ")},on:{input:function(e){return t.inputChangeRotation(e,"z")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v(t._s(t.$t("formRelational.rotation.z")))]},proxy:!0}]),model:{value:t.elementDatas.rotation[2],callback:function(e){t.$set(t.elementDatas.rotation,2,e)},expression:"elementDatas.rotation[2]"}})],1)],1)],1),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-offset"},[a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{staticClass:"space-between-center",attrs:{span:12}},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.translate.label")))]),a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:t.$t("formRelational.translate.tooltip"),placement:"top"}},[a("span",{staticClass:"outline-none cursor-btn",on:{click:function(e){return t.handleModelTransform("translate")}}},[a("CommonSVG",{attrs:{color:"var(--theme)",size:"17","icon-class":"crosshair_move_feature"}})],1)])],1),a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.offset[0],expression:"elementDatas.offset[0]"}],attrs:{type:"number",size:"mini",placeholder:t.$t("formRelational.translate.placeholderX")},on:{input:function(e){return t.inputChangeOffset(e,"x")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v(t._s(t.$t("formRelational.translate.x")))])]},proxy:!0}]),model:{value:t.elementDatas.offset[0],callback:function(e){t.$set(t.elementDatas.offset,0,e)},expression:"elementDatas.offset[0]"}})],1)],1),a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.offset[1],expression:"elementDatas.offset[1]"}],attrs:{type:"number",size:"mini",placeholder:t.$t("formRelational.translate.placeholderY")},on:{input:function(e){return t.inputChangeOffset(e,"y")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v(t._s(t.$t("formRelational.translate.y")))]},proxy:!0}]),model:{value:t.elementDatas.offset[1],callback:function(e){t.$set(t.elementDatas.offset,1,e)},expression:"elementDatas.offset[1]"}})],1),a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.offset[2],expression:"elementDatas.offset[2]"}],attrs:{type:"number",size:"mini",placeholder:t.$t("formRelational.translate.placeholderZ")},on:{input:function(e){return t.inputChangeOffset(e,"z")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v(t._s(t.$t("formRelational.translate.z")))]},proxy:!0}]),model:{value:t.elementDatas.offset[2],callback:function(e){t.$set(t.elementDatas.offset,2,e)},expression:"elementDatas.offset[2]"}})],1)],1)],1),a("div",{staticClass:"after-lineX"})])},v=[],g=a("3285"),y={name:"ModelSetting",props:["currentElement","elementTransformObj"],mixins:[g["a"]],data:function(){return{itemGutter:10,elementDatas:{type:"model",name:this.$t("featureDatas.model.name"),id:"",longitude:0,latitude:0,altitude:0,rotation:[0,0,0],offset:[0,0,0],list:[{optionState:!0,phaseValue:-1,phaseOption:[{options:[]},{options:[]}],noToggleRequired:!0}],validate:{longitude:!0,latitude:!0,altitude:!0,rotation:!0}}}},created:function(){this.init()},methods:{init:function(){for(var t=0;t<=this.currentElement.maxVersion;t++)this.elementDatas.list[0].phaseOption[0].options.push({label:"V "+t,value:t});var e=""===this.currentElement.version?-1:this.currentElement.version;this.elementDatas.list[0].phaseValue=e,this.elementDatas.list[0].phaseOption[1].options.push({label:this.$t("featureSetting.style.model.version.latest"),value:-1}),this.elementDatas.id=this.currentElement.id,this.elementDatas.longitude=this.currentElement.origin[0],this.elementDatas.latitude=this.currentElement.origin[1],this.elementDatas.altitude=this.currentElement.altitude,Array.isArray(this.currentElement.rotation)?this.elementDatas.rotation=this.currentElement.rotation:this.elementDatas.rotation=[0,0,this.currentElement.rotation],this.elementDatas.offset=this.currentElement.offset}}},C=y,D=(a("996b"),Object(d["a"])(C,f,v,!1,null,"356b8f9e",null)),b=D.exports,w=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex"},[a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.elementDatas.list[0].title))]),a("el-input",{class:{"is-error":t.inputError&&""==t.elementDatas.list[0].value},attrs:{size:"mini",placeholder:t.$t("formRelational.address.placeholder")},model:{value:t.elementDatas.list[0].value,callback:function(e){t.$set(t.elementDatas.list[0],"value",e)},expression:"elementDatas.list[0].value"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.elementDatas.list[1].title))]),a("div",{staticClass:"items-center w100"},[a("el-slider",{staticStyle:{width:"100%"},attrs:{min:.1,max:1,step:.1},on:{change:t.changeDemOpacity},model:{value:t.elementDatas.list[1].value,callback:function(e){t.$set(t.elementDatas.list[1],"value",e)},expression:"elementDatas.list[1].value"}}),a("span",{staticClass:"slider-num"},[t._v(t._s(t.elementDatas.list[1].value))])],1)])])])},_=[],x={name:"DemSetting",props:["currentElement"],data:function(){return{itemGutter:10,elementDatas:{id:"",longitude:0,latitude:0,altitude:0,rotation:[0,0,0],offset:[0,0,0],list:[{title:this.$t("formRelational.address.label"),name:"",value:"mapbox://mapbox.mapbox-terrain-dem-v1",defaultAddress:"mapbox://mapbox.mapbox-terrain-dem-v1",validate:!0,optionState:!0},{title:this.$t("formRelational.opacity.label"),value:1,optionState:!0}]}}},created:function(){this.init()},methods:{init:function(){var t=window.scene.features.get(this.currentElement.id);this.elementDatas.list[0].value=this.currentElement.url,this.elementDatas.list[0].defaultAddress=this.currentElement.url,this.elementDatas.list[1].value=t.data.opacity},changeDemOpacity:function(t){var e=window.scene.features.get(this.currentElement.id);window.scene.postData({opacity:t},e.dataKey)}}},k=x,$=(a("a173"),Object(d["a"])(k,w,_,!1,null,"43f18d2b",null)),S=$.exports,P=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex"},[a("div",{staticClass:"styleSet-list-div h100 list-div-200"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.elementDatas.list[0].title))]),a("el-color-picker",{staticStyle:{"margin-right":"12px"},attrs:{"popper-class":"colorPicker","show-alpha":!1,"color-format":"rgb",size:"mini"},on:{change:t.changeBuildingColor},model:{value:t.elementDatas.list[0].value,callback:function(e){t.$set(t.elementDatas.list[0],"value",e)},expression:"elementDatas.list[0].value"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.elementDatas.list[1].title))]),a("div",{staticClass:"items-center w100"},[a("el-slider",{staticStyle:{width:"100%"},attrs:{min:.1,max:1,step:.1},on:{change:t.changeBuildingOpacity},model:{value:t.elementDatas.list[1].value,callback:function(e){t.$set(t.elementDatas.list[1],"value",e)},expression:"elementDatas.list[1].value"}}),a("span",{staticClass:"slider-num"},[t._v(t._s(t.elementDatas.list[1].value))])],1)])]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.elementDatas.list[2].title))]),a("el-input",{attrs:{size:"mini",type:"number",placeholder:t.$t("formRelational.address.placeholder")},on:{input:t.changeBuildingminzoom},model:{value:t.elementDatas.list[2].value,callback:function(e){t.$set(t.elementDatas.list[2],"value",e)},expression:"elementDatas.list[2].value"}},[a("template",{slot:"suffix"},[a("el-tooltip",{staticClass:"box-item",attrs:{enterable:!1,effect:"dark",placement:"top"}},[a("div",{attrs:{slot:"content"},domProps:{innerHTML:t._s(t.$t("featureSetting.style._3dBuilding.tooltip"))},slot:"content"}),a("i",{staticClass:"outline-none el-input__icon el-icon-question"})])],1)],2)],1)])])},E=[],R=a("f7fe"),T=a.n(R),O={name:"BuildingSetting",props:["currentElement"],data:function(){return{itemGutter:10,elementDatas:{id:"",longitude:0,latitude:0,altitude:0,rotation:[0,0,0],offset:[0,0,0],list:[{title:this.$t("formRelational.color.label"),value:"rgb(215, 245, 255)"},{title:this.$t("formRelational.opacity.label"),value:1},{title:this.$t("featureSetting.style._3dBuilding.label"),value:12}]}}},created:function(){this.init(),this.changeBuildingminzoom=T()(this.changeBuildingminzoom,800)},methods:{init:function(){var t=window.scene.features.get(this.currentElement.id);this.elementDatas.list[0].value=t.data.color,this.elementDatas.list[1].value=t.data.opacity},changeBuildingOpacity:function(t){var e=window.scene.features.get(this.currentElement.id);window.scene.postData({opacity:t},e.dataKey)},changeBuildingColor:function(t){var e=window.scene.features.get(this.currentElement.id);window.scene.postData({color:t},e.dataKey)},changeBuildingminzoom:function(t){t=parseFloat(t),t<1&&(t=1),t>30&&(t=15);var e=window.scene.features.get(this.currentElement.id);window.scene.postData({minzoom:t},e.dataKey)}}},j=O,z=(a("ce07"),Object(d["a"])(j,P,E,!1,null,"9184c2f8",null)),F=z.exports,I=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex"},[a("div",{staticClass:"styleSet-list-div h100"},[a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{staticClass:"space-between-center",attrs:{span:12}},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.basePoint.label")))]),a("el-tooltip",{attrs:{effect:"dark",content:t.$t("formRelational.basePoint.placeholder"),placement:"top"}},[a("span",{staticClass:"outline-none cursor-btn",on:{click:function(e){return t.clickCheckedCoordinate()}}},[a("CommonSVG",{attrs:{color:"var(--theme)",size:"18","icon-class":"select_coord"}})],1)])],1),a("el-col",{attrs:{span:12}},[a("el-input",{class:{"is-error":t.inputError&&!t.styleFormDatas.validate.longitude},attrs:{type:"number",size:"mini",placeholder:t.$t("formRelational.longitude.placeholder")},on:{input:function(e){return t.inputChangeOrigin(e,"longitude")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v(t._s(t.$t("formRelational.longitude.label")))])]},proxy:!0}]),model:{value:t.elementDatas.longitude,callback:function(e){t.$set(t.elementDatas,"longitude",e)},expression:"elementDatas.longitude"}})],1)],1),a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{attrs:{span:12}},[a("el-input",{class:{"is-error":t.inputError&&!t.styleFormDatas.validate.latitude},attrs:{type:"number",size:"mini",placeholder:t.$t("formRelational.latitude.placeholder")},on:{input:function(e){return t.inputChangeOrigin(e,"latitude")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v(t._s(t.$t("formRelational.latitude.label")))]},proxy:!0}]),model:{value:t.elementDatas.latitude,callback:function(e){t.$set(t.elementDatas,"latitude",e)},expression:"elementDatas.latitude"}})],1),a("el-col",{attrs:{span:12}},[a("el-input",{class:{"is-error":t.inputError&&!t.styleFormDatas.validate.altitude},attrs:{type:"number",size:"mini",placeholder:t.$t("formRelational.altitude.placeholder")},on:{input:function(e){return t.inputChangeOrigin(e,"altitude")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v(t._s(t.$t("formRelational.altitude.label")))])]},proxy:!0}]),model:{value:t.elementDatas.altitude,callback:function(e){t.$set(t.elementDatas,"altitude",e)},expression:"elementDatas.altitude"}})],1)],1)],1),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-rotation"},[a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{staticClass:"space-between-center",attrs:{span:12}},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.rotation.label")))]),a("el-tooltip",{attrs:{effect:"dark",content:t.$t("formRelational.rotation.tooltip"),placement:"top"}},[a("span",{staticClass:"outline-none cursor-btn",on:{click:function(e){return t.handleModelTransform("rotate")}}},[a("CommonSVG",{attrs:{color:"var(--theme)",size:"16","icon-class":"rotating_feature"}})],1)])],1),a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.rotation[0],expression:"elementDatas.rotation[0]"}],attrs:{type:"number",size:"mini",placeholder:t.$t("formRelational.rotation.placeholderX")},on:{input:function(e){return t.inputChangeRotation(e,"x")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v(t._s(t.$t("formRelational.rotation.x")))]},proxy:!0}]),model:{value:t.elementDatas.rotation[0],callback:function(e){t.$set(t.elementDatas.rotation,0,e)},expression:"elementDatas.rotation[0]"}})],1)],1),a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.rotation[1],expression:"elementDatas.rotation[1]"}],attrs:{type:"number",size:"mini",placeholder:t.$t("formRelational.rotation.placeholderY")},on:{input:function(e){return t.inputChangeRotation(e,"y")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v(t._s(t.$t("formRelational.rotation.y")))]},proxy:!0}]),model:{value:t.elementDatas.rotation[1],callback:function(e){t.$set(t.elementDatas.rotation,1,e)},expression:"elementDatas.rotation[1]"}})],1),a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.rotation[2],expression:"elementDatas.rotation[2]"}],attrs:{type:"number",size:"mini",placeholder:t.$t("formRelational.rotation.placeholderZ")},on:{input:function(e){return t.inputChangeRotation(e,"z")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v(t._s(t.$t("formRelational.rotation.z")))]},proxy:!0}]),model:{value:t.elementDatas.rotation[2],callback:function(e){t.$set(t.elementDatas.rotation,2,e)},expression:"elementDatas.rotation[2]"}})],1)],1)],1),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-offset"},[a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{staticClass:"space-between-center",attrs:{span:12}},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.translate.label")))]),a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:t.$t("formRelational.translate.tooltip"),placement:"top"}},[a("span",{staticClass:"outline-none cursor-btn",on:{click:function(e){return t.handleModelTransform("translate")}}},[a("CommonSVG",{attrs:{color:"var(--theme)",size:"17","icon-class":"crosshair_move_feature"}})],1)])],1),a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.offset[0],expression:"elementDatas.offset[0]"}],attrs:{type:"number",size:"mini",placeholder:t.$t("formRelational.translate.placeholderX")},on:{input:function(e){return t.inputChangeOffset(e,"x")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v(t._s(t.$t("formRelational.translate.x")))])]},proxy:!0}]),model:{value:t.elementDatas.offset[0],callback:function(e){t.$set(t.elementDatas.offset,0,e)},expression:"elementDatas.offset[0]"}})],1)],1),a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.offset[1],expression:"elementDatas.offset[1]"}],attrs:{type:"number",size:"mini",placeholder:t.$t("formRelational.translate.placeholderY")},on:{input:function(e){return t.inputChangeOffset(e,"y")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v(t._s(t.$t("formRelational.translate.y")))]},proxy:!0}]),model:{value:t.elementDatas.offset[1],callback:function(e){t.$set(t.elementDatas.offset,1,e)},expression:"elementDatas.offset[1]"}})],1),a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.offset[2],expression:"elementDatas.offset[2]"}],attrs:{type:"number",size:"mini",placeholder:t.$t("formRelational.translate.placeholderZ")},on:{input:function(e){return t.inputChangeOffset(e,"z")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v(t._s(t.$t("formRelational.translate.z")))]},proxy:!0}]),model:{value:t.elementDatas.offset[2],callback:function(e){t.$set(t.elementDatas.offset,2,e)},expression:"elementDatas.offset[2]"}})],1)],1)],1),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{staticClass:"space-between-center",attrs:{span:12}},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.scale.label")))]),a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:t.$t("formRelational.scale.tooltip"),placement:"top"}},[a("span",{staticClass:"outline-none cursor-btn",on:{click:function(e){return t.handleModelTransform("scale")}}},[a("CommonSVG",{attrs:{color:"var(--theme)",size:"17","icon-class":"xyz"}})],1)])],1),a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.scaleObj.x,expression:"elementDatas.scaleObj.x"}],attrs:{type:"number",size:"mini",placeholder:t.$t("formRelational.scale.placeholderX")},on:{input:function(e){return t.inputChangeScale(e,"x")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v(t._s(t.$t("formRelational.scale.x")))])]},proxy:!0}]),model:{value:t.elementDatas.scaleObj.x,callback:function(e){t.$set(t.elementDatas.scaleObj,"x",e)},expression:"elementDatas.scaleObj.x"}})],1)],1),a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.scaleObj.y,expression:"elementDatas.scaleObj.y"}],attrs:{type:"number",size:"mini",placeholder:t.$t("formRelational.scale.placeholderY")},on:{input:function(e){return t.inputChangeScale(e,"y")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v(t._s(t.$t("formRelational.scale.y")))]},proxy:!0}]),model:{value:t.elementDatas.scaleObj.y,callback:function(e){t.$set(t.elementDatas.scaleObj,"y",e)},expression:"elementDatas.scaleObj.y"}})],1),a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.scaleObj.z,expression:"elementDatas.scaleObj.z"}],attrs:{type:"number",size:"mini",placeholder:t.$t("formRelational.scale.placeholderZ")},on:{input:function(e){return t.inputChangeScale(e,"z")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v(t._s(t.$t("formRelational.scale.z")))]},proxy:!0}]),model:{value:t.elementDatas.scaleObj.z,callback:function(e){t.$set(t.elementDatas.scaleObj,"z",e)},expression:"elementDatas.scaleObj.z"}})],1)],1)],1),a("div",{staticClass:"after-lineX"})])},A=[],N={name:"GltfFbxSetting",props:["currentElement","elementTransformObj"],mixins:[g["a"]],data:function(){return{itemGutter:10,elementDatas:{id:"",longitude:0,latitude:0,altitude:0,rotation:[0,0,0],offset:[0,0,0],scale:[1,1,1],list:[],scaleObj:{x:1,y:1,z:1}}}},created:function(){this.init()},methods:{init:function(){var t=window.scene.features.get(this.currentElement.id);this.elementDatas.id=this.currentElement.id,this.elementDatas.longitude=t.origin[0],this.elementDatas.latitude=t.origin[1],this.elementDatas.altitude=t.altitude,t.rotation instanceof Array&&(this.elementDatas.rotation=t.rotation),this.elementDatas.offset=t.offset,void 0!=t.scale?(this.elementDatas.scale=t.scale,this.elementDatas.scaleObj.x=t.scale[0],this.elementDatas.scaleObj.y=t.scale[1],this.elementDatas.scaleObj.z=t.scale[2]):t.scale=this.elementDatas.scale}}},M=N,G=(a("d1ca"),Object(d["a"])(M,I,A,!1,null,"27c58b80",null)),X=G.exports,V=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex"},[a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.featureID.label")))]),a("el-input",{attrs:{size:"mini",readonly:"true",placeholder:t.$t("formRelational.featureID.placeholder")},model:{value:t.elementDatas.id,callback:function(e){t.$set(t.elementDatas,"id",e)},expression:"elementDatas.id"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.elementDatas.list[0].title))]),a("el-input",{attrs:{size:"mini",placeholder:t.$t("formRelational.address.placeholder")},model:{value:t.elementDatas.list[0].value,callback:function(e){t.$set(t.elementDatas.list[0],"value",e)},expression:"elementDatas.list[0].value"}})],1)]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{staticClass:"space-between-center",attrs:{span:12}},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.basePoint.label")))]),a("el-tooltip",{attrs:{effect:"dark",content:t.$t("formRelational.basePoint.placeholder"),placement:"top"}},[a("span",{staticClass:"outline-none cursor-btn",on:{click:function(e){return t.clickCheckedCoordinate()}}},[a("CommonSVG",{attrs:{color:"var(--theme)",size:"18","icon-class":"select_coord"}})],1)])],1),a("el-col",{attrs:{span:12}},[a("el-input",{class:{"is-error":t.inputError&&!t.styleFormDatas.validate.longitude},attrs:{type:"number",size:"mini",placeholder:t.$t("formRelational.longitude.placeholder")},on:{input:function(e){return t.inputChangeOrigin(e,"longitude")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v(t._s(t.$t("formRelational.longitude.label")))])]},proxy:!0}]),model:{value:t.elementDatas.longitude,callback:function(e){t.$set(t.elementDatas,"longitude",e)},expression:"elementDatas.longitude"}})],1)],1),a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{attrs:{span:12}},[a("el-input",{class:{"is-error":t.inputError&&!t.styleFormDatas.validate.latitude},attrs:{type:"number",title:"",size:"mini",placeholder:t.$t("formRelational.latitude.placeholder")},on:{input:function(e){return t.inputChangeOrigin(e,"latitude")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v(t._s(t.$t("formRelational.latitude.label")))]},proxy:!0}]),model:{value:t.elementDatas.latitude,callback:function(e){t.$set(t.elementDatas,"latitude",e)},expression:"elementDatas.latitude"}})],1),a("el-col",{attrs:{span:12}},[a("el-input",{class:{"is-error":t.inputError&&!t.styleFormDatas.validate.altitude},attrs:{type:"number",title:"",size:"mini",placeholder:t.$t("formRelational.altitude.placeholder")},on:{input:function(e){return t.inputChangeOrigin(e,"altitude")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v(t._s(t.$t("formRelational.altitude.label")))])]},proxy:!0}]),model:{value:t.elementDatas.altitude,callback:function(e){t.$set(t.elementDatas,"altitude",e)},expression:"elementDatas.altitude"}})],1)],1)],1),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-rotation"},[a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{staticClass:"space-between-center",attrs:{span:12}},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.rotation.label")))]),a("el-tooltip",{attrs:{effect:"dark",content:t.$t("formRelational.rotation.tooltip"),placement:"top"}},[a("span",{staticClass:"outline-none cursor-btn",on:{click:function(e){return t.handleModelTransform("rotate")}}},[a("CommonSVG",{attrs:{color:"var(--theme)",size:"16","icon-class":"rotating_feature"}})],1)])],1),a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.rotation[0],expression:"elementDatas.rotation[0]"}],attrs:{type:"number",size:"mini",placeholder:t.$t("formRelational.rotation.placeholderX")},on:{input:function(e){return t.inputChangeRotation(e,"x")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v(t._s(t.$t("formRelational.rotation.x")))]},proxy:!0}]),model:{value:t.elementDatas.rotation[0],callback:function(e){t.$set(t.elementDatas.rotation,0,e)},expression:"elementDatas.rotation[0]"}})],1)],1),a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.rotation[1],expression:"elementDatas.rotation[1]"}],attrs:{type:"number",size:"mini",placeholder:t.$t("formRelational.rotation.placeholderY")},on:{input:function(e){return t.inputChangeRotation(e,"y")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v(t._s(t.$t("formRelational.rotation.y")))]},proxy:!0}]),model:{value:t.elementDatas.rotation[1],callback:function(e){t.$set(t.elementDatas.rotation,1,e)},expression:"elementDatas.rotation[1]"}})],1),a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.rotation[2],expression:"elementDatas.rotation[2]"}],attrs:{type:"number",size:"mini",placeholder:t.$t("formRelational.rotation.placeholderZ")},on:{input:function(e){return t.inputChangeRotation(e,"z")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v(t._s(t.$t("formRelational.rotation.z")))]},proxy:!0}]),model:{value:t.elementDatas.rotation[2],callback:function(e){t.$set(t.elementDatas.rotation,2,e)},expression:"elementDatas.rotation[2]"}})],1)],1)],1),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-offset"},[a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{staticClass:"space-between-center",attrs:{span:12}},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.translate.label")))]),a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:t.$t("formRelational.translate.tooltip"),placement:"top"}},[a("span",{staticClass:"outline-none cursor-btn",on:{click:function(e){return t.handleModelTransform("translate")}}},[a("CommonSVG",{attrs:{color:"var(--theme)",size:"17","icon-class":"crosshair_move_feature"}})],1)])],1),a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.offset[0],expression:"elementDatas.offset[0]"}],attrs:{type:"number",size:"mini",placeholder:t.$t("formRelational.translate.placeholderX")},on:{input:function(e){return t.inputChangeOffset(e,"x")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v(t._s(t.$t("formRelational.translate.x")))])]},proxy:!0}]),model:{value:t.elementDatas.offset[0],callback:function(e){t.$set(t.elementDatas.offset,0,e)},expression:"elementDatas.offset[0]"}})],1)],1),a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.offset[1],expression:"elementDatas.offset[1]"}],attrs:{type:"number",size:"mini",placeholder:t.$t("formRelational.translate.placeholderY")},on:{input:function(e){return t.inputChangeOffset(e,"y")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v(t._s(t.$t("formRelational.translate.y")))]},proxy:!0}]),model:{value:t.elementDatas.offset[1],callback:function(e){t.$set(t.elementDatas.offset,1,e)},expression:"elementDatas.offset[1]"}})],1),a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.offset[2],expression:"elementDatas.offset[2]"}],attrs:{type:"number",size:"mini",placeholder:t.$t("formRelational.translate.placeholderZ")},on:{input:function(e){return t.inputChangeOffset(e,"z")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v(t._s(t.$t("formRelational.translate.z")))]},proxy:!0}]),model:{value:t.elementDatas.offset[2],callback:function(e){t.$set(t.elementDatas.offset,2,e)},expression:"elementDatas.offset[2]"}})],1)],1)],1),a("div",{staticClass:"after-lineX"})])},L=[],B={name:"TilesSetting",props:["currentElement","elementTransformObj"],mixins:[g["a"]],data:function(){return{itemGutter:10,elementDatas:{id:"",longitude:0,latitude:0,altitude:0,rotation:[0,0,0],offset:[0,0,0],list:[{title:this.$t("formRelational.address.label"),value:"",optionState:!0,validate:!0,validateState:null}]}}},created:function(){this.init()},methods:{init:function(){var t=window.scene.features.get(this.currentElement.id);this.elementDatas.id=this.currentElement.id,this.elementDatas.list[0].value=t.url,this.elementDatas.longitude=t.origin[0],this.elementDatas.latitude=t.origin[1],this.elementDatas.altitude=t.altitude,this.elementDatas.offset=t.offset,this.elementDatas.rotation=Array.isArray(t.rotation)?t.rotation:[0,0,0]}}},H=B,K=(a("29c1"),Object(d["a"])(H,V,L,!1,null,"0c8a2d68",null)),Y=K.exports,W=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex"},[a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.featureID.label")))]),a("el-input",{attrs:{size:"mini",placeholder:t.$t("formRelational.featureID.placeholder")},model:{value:t.elementDatas.id,callback:function(e){t.$set(t.elementDatas,"id",e)},expression:"elementDatas.id"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.elementDatas.list[0].title))]),a("el-input",{attrs:{size:"mini",placeholder:t.$t("formRelational.address.label")},model:{value:t.elementDatas.list[0].value,callback:function(e){t.$set(t.elementDatas.list[0],"value",e)},expression:"elementDatas.list[0].value"}})],1)]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{staticClass:"space-between-center",attrs:{span:12}},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.basePoint.label")))]),a("el-tooltip",{attrs:{effect:"dark",content:t.$t("formRelational.basePoint.placeholder"),placement:"top"}},[a("span",{staticClass:"outline-none cursor-btn",on:{click:function(e){return t.clickCheckedCoordinate()}}},[a("CommonSVG",{attrs:{color:"var(--theme)",size:"18","icon-class":"select_coord"}})],1)])],1),a("el-col",{attrs:{span:12}},[a("el-input",{class:{"is-error":t.inputError&&!t.styleFormDatas.validate.longitude},attrs:{type:"number",size:"mini",placeholder:t.$t("formRelational.longitude.placeholder")},on:{input:function(e){return t.inputChangeOrigin(e,"longitude")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v(t._s(t.$t("formRelational.longitude.label")))])]},proxy:!0}]),model:{value:t.elementDatas.longitude,callback:function(e){t.$set(t.elementDatas,"longitude",e)},expression:"elementDatas.longitude"}})],1)],1),a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{attrs:{span:12}},[a("el-input",{class:{"is-error":t.inputError&&!t.styleFormDatas.validate.latitude},attrs:{type:"number",size:"mini",placeholder:t.$t("formRelational.latitude.placeholder")},on:{input:function(e){return t.inputChangeOrigin(e,"latitude")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v(t._s(t.$t("formRelational.latitude.label")))]},proxy:!0}]),model:{value:t.elementDatas.latitude,callback:function(e){t.$set(t.elementDatas,"latitude",e)},expression:"elementDatas.latitude"}})],1),a("el-col",{attrs:{span:12}},[a("el-input",{class:{"is-error":t.inputError&&!t.styleFormDatas.validate.altitude},attrs:{type:"number",size:"mini",placeholder:t.$t("formRelational.altitude.placeholder")},on:{input:function(e){return t.inputChangeOrigin(e,"altitude")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v(t._s(t.$t("formRelational.altitude.label")))])]},proxy:!0}]),model:{value:t.elementDatas.altitude,callback:function(e){t.$set(t.elementDatas,"altitude",e)},expression:"elementDatas.altitude"}})],1)],1)],1),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-offset"},[a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{staticClass:"space-between-center",attrs:{span:12}},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.translate.label")))]),a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:t.$t("formRelational.translate.tooltip"),placement:"top"}},[a("span",{staticClass:"outline-none cursor-btn",on:{click:function(e){return t.handleModelTransform("translate")}}},[a("CommonSVG",{attrs:{color:"var(--theme)",size:"17","icon-class":"crosshair_move_feature"}})],1)])],1),a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.offset[0],expression:"elementDatas.offset[0]"}],attrs:{type:"number",title:"",size:"mini",placeholder:t.$t("formRelational.translate.placeholderX")},on:{input:function(e){return t.inputChangeOffset(e,"x")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v(t._s(t.$t("formRelational.translate.x")))])]},proxy:!0}]),model:{value:t.elementDatas.offset[0],callback:function(e){t.$set(t.elementDatas.offset,0,e)},expression:"elementDatas.offset[0]"}})],1)],1),a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.offset[1],expression:"elementDatas.offset[1]"}],attrs:{type:"number",title:"",size:"mini",placeholder:t.$t("formRelational.translate.placeholderY")},on:{input:function(e){return t.inputChangeOffset(e,"y")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v(t._s(t.$t("formRelational.translate.y")))]},proxy:!0}]),model:{value:t.elementDatas.offset[1],callback:function(e){t.$set(t.elementDatas.offset,1,e)},expression:"elementDatas.offset[1]"}})],1),a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.offset[2],expression:"elementDatas.offset[2]"}],attrs:{type:"number",title:"",size:"mini",placeholder:t.$t("formRelational.translate.placeholderZ")},on:{input:function(e){return t.inputChangeOffset(e,"z")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v(t._s(t.$t("formRelational.translate.z")))]},proxy:!0}]),model:{value:t.elementDatas.offset[2],callback:function(e){t.$set(t.elementDatas.offset,2,e)},expression:"elementDatas.offset[2]"}})],1)],1)],1),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.elementDatas.list[1].title))]),a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.list[1].value,expression:"elementDatas.list[1].value"}],attrs:{size:"mini",type:"number",placeholder:t.$t("formRelational.radius.placeholder")},on:{input:t.inputPanoramaRadius},scopedSlots:t._u([{key:"append",fn:function(){return[a("span",[t._v("m")])]},proxy:!0}]),model:{value:t.elementDatas.list[1].value,callback:function(e){t.$set(t.elementDatas.list[1],"value",e)},expression:"elementDatas.list[1].value"}})],1),a("div",{staticClass:"items-center"})]),a("div",{staticClass:"after-lineX"})])},Z=[],J={name:"PanoramaSetting",props:["currentElement","elementTransformObj"],mixins:[g["a"]],data:function(){return{itemGutter:10,elementDatas:{id:"",longitude:0,latitude:0,altitude:0,rotation:[0,0,0],offset:[0,0,0],list:[{title:this.$t("formRelational.address.label"),value:"",optionState:!0,validate:!0,validateState:null},{title:this.$t("formRelational.radius.label"),value:20,optionState:!0},{title:this.$t("featureSetting.style.panorama.deepPath"),value:"",optionState:!0}]}}},created:function(){this.init(),this.inputPanoramaRadius=T()(this.inputPanoramaRadius,800)},methods:{init:function(){var t=window.scene.features.get(this.currentElement.id);this.elementDatas.id=this.currentElement.id,this.elementDatas.list[0].value=t.url,this.elementDatas.list[1].value=t.data.radius,this.elementDatas.list[2].value=t.data.depthPath,this.elementDatas.longitude=t.origin[0],this.elementDatas.latitude=t.origin[1],this.elementDatas.altitude=t.altitude,this.elementDatas.offset=t.offset},inputPanoramaRadius:function(t){var e=window.scene.features.get(this.currentElement.id);window.scene.postData({radius:t},e.dataKey),window.scene.render()},inputPanoramaDepthTexture:function(t){if(""!=t){var e=window.scene.features.get(this.currentElement.id);window.scene.postData({depthPath:t},e.dataKey)}}}},q=J,U=(a("fb4f"),Object(d["a"])(q,W,Z,!1,null,"5c7755ea",null)),Q=U.exports,tt=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex relative"},[a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.elementDatas.list[0].title))]),a("el-input",{attrs:{size:"mini",placeholder:t.$t("formRelational.address.placeholder")},model:{value:t.elementDatas.list[0].value,callback:function(e){t.$set(t.elementDatas.list[0],"value",e)},expression:"elementDatas.list[0].value"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.elementDatas.list[1].title))]),a("div",{staticClass:"popup-item-center items-center",on:{click:t.togglePanel}},[a("CommonSVG",{attrs:{"icon-class":"xyz",size:16}}),a("span",{staticClass:"margin-left-5"},[t._v(t._s(t.elementDatas.list[1].title))])],1)])]),t.elementDatas.list[1].panelState?a("div",{staticClass:"regional-coordinates-container"},[a("div",{staticClass:"title"},[a("span",[t._v(t._s(t.elementDatas.list[1].title))]),a("i",{staticClass:"el-icon-close cursor-btn",on:{click:t.togglePanel}})]),t._l(t.elementDatas.list[1].cor,(function(e,s){return a("el-row",{key:s,attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-input",{attrs:{type:"number",title:"",size:"mini",placeholder:t.$t("formRelational.longitude.placeholder")},on:{input:t.inputVideoCor},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v(t._s(t.$t("formRelational.longitude.label")))])]},proxy:!0}],null,!0),model:{value:e.position.x,callback:function(a){t.$set(e.position,"x",a)},expression:"pos.position.x"}})],1),a("el-col",{attrs:{span:12}},[a("el-input",{attrs:{type:"number",title:"",size:"mini",placeholder:t.$t("formRelational.latitude.placeholder")},on:{input:t.inputVideoCor},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v(t._s(t.$t("formRelational.latitude.label")))]},proxy:!0}],null,!0),model:{value:e.position.y,callback:function(a){t.$set(e.position,"y",a)},expression:"pos.position.y"}})],1)],1)}))],2):t._e()])},et=[],at={name:"VideoSetting",props:["currentElement"],components:{CommonSVG:function(){return a.e("chunk-51f90655").then(a.bind(null,"17d0"))}},data:function(){return{itemGutter:10,elementDatas:{id:"",longitude:0,latitude:0,altitude:0,rotation:[0,0,0],offset:[0,0,0],list:[{title:this.$t("formRelational.address.label"),value:"",optionState:!0,validate:!0,validateState:null},{title:this.$t("featureSetting.style.video.coordinate"),cor:[{id:1,position:{x:0,y:0}},{id:2,position:{x:0,y:0}},{id:3,position:{x:0,y:0}},{id:4,position:{x:0,y:0}}],panelState:!1}]},inputIcon:a("59ab")}},created:function(){this.init(),this.inputVideoCor=T()(this.inputVideoCor,800)},methods:{init:function(){var t=this,e=window.scene.features.get(this.currentElement.id);this.elementDatas.id=this.currentElement.id,this.elementDatas.list[0].value=e.url,e.data.forEach((function(e,a){t.elementDatas.list[1].cor[a].position.x=e[0],t.elementDatas.list[1].cor[a].position.y=e[1]}))},togglePanel:function(){this.elementDatas.list[1].panelState=!this.elementDatas.list[1].panelState},inputVideoCor:function(){var t=window.scene.features.get(this.currentElement.id),e=[];this.elementDatas.list[1].cor.forEach((function(t){e.push([parseFloat(t.position.x+""),parseFloat(t.position.y+"")])})),window.scene.postData(e,t.dataKey)}}},st=at,it=(a("a84b"),Object(d["a"])(st,tt,et,!1,null,"53e90f12",null)),nt=it.exports,lt=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex relative w100"},["environment"==t.subType?[a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.elementDatas.list[0].title))]),a("el-input",{attrs:{size:"mini",placeholder:t.$t("formRelational.image.placeholder")},on:{change:t.inputChangeImageAddress},model:{value:t.elementDatas.list[0].value,callback:function(e){t.$set(t.elementDatas.list[0],"value","string"===typeof e?e.trim():e)},expression:"elementDatas.list[0].value"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.elementDatas.list[1].title))]),a("div",{staticClass:"popup-item-center items-center",on:{click:t.togglePanel}},[a("CommonSVG",{attrs:{"icon-class":"xyz",size:16}}),a("span",{staticClass:"margin-left-5"},[t._v(t._s(t.elementDatas.list[1].title))])],1)]),t.elementDatas.list[0].thumbState?a("div",{staticClass:"thumb-dialog"},[a("div",{staticClass:"title"},[a("span",[t._v(t._s(t.$t("formRelational.image.label")))]),a("div",[a("i",{staticClass:"el-icon-close cursor-btn",on:{click:t.openThumb}})])]),a("div",{staticClass:"thumb-content"},t._l(t.thumbList,(function(e){return a("div",{key:e.url,staticClass:"item",class:{active:e.img===t.elementDatas.list[0].value},on:{click:function(a){return t.checkPolygonImg(e)}}},[a("div",{staticClass:"img-box"},[a("img",{attrs:{src:e.img}})]),a("span",[t._v(t._s(e.name))])])})),0)]):t._e()]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.opacity.label")))]),a("div",{staticClass:"items-center w100"},[a("el-slider",{staticStyle:{width:"100%"},attrs:{min:.1,max:1,step:.1},on:{change:function(e){return t.inputChangePolygonPostData(e,"opacity")}},model:{value:t.params.opacity,callback:function(e){t.$set(t.params,"opacity",e)},expression:"params.opacity"}}),a("span",{staticClass:"slider-num"},[t._v(t._s(t.params.opacity))])],1)]),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.baseTop.label")))]),a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.params.base,expression:"params.base"}],attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("formRelational.baseTop.placeholder")},on:{input:function(e){return t.inputChangePolygonPostData(e,"base")}},scopedSlots:t._u([{key:"append",fn:function(){return[a("span",[t._v("m")])]},proxy:!0}],null,!1,1549734309),model:{value:t.params.base,callback:function(e){t.$set(t.params,"base",e)},expression:"params.base"}})],1)]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("featureSetting.style.polygon.environment.fillX")))]),a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.params.imageRepeat.x,expression:"params.imageRepeat.x"}],attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("featureSetting.style.polygon.environment.placeholder")},on:{input:function(e){return t.inputChangePolygonPostData(e,"imageRepeat","x")}},scopedSlots:t._u([{key:"append",fn:function(){return[a("span",[t._v(t._s(t.$t("featureSetting.style.polygon.environment.piece")))])]},proxy:!0}],null,!1,2341311394),model:{value:t.params.imageRepeat.x,callback:function(e){t.$set(t.params.imageRepeat,"x",e)},expression:"params.imageRepeat.x"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("featureSetting.style.polygon.environment.fillY")))]),a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.params.imageRepeat.y,expression:"params.imageRepeat.y"}],attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("featureSetting.style.polygon.environment.placeholder")},on:{input:function(e){return t.inputChangePolygonPostData(e,"imageRepeat","y")}},scopedSlots:t._u([{key:"append",fn:function(){return[a("span",[t._v(t._s(t.$t("featureSetting.style.polygon.environment.piece")))])]},proxy:!0}],null,!1,2341311394),model:{value:t.params.imageRepeat.y,callback:function(e){t.$set(t.params.imageRepeat,"y",e)},expression:"params.imageRepeat.y"}})],1)]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.showLine.label")))]),a("el-switch",{staticStyle:{"margin-left":"12px"},on:{change:function(e){return t.inputChangePolygonPostData(e,"showline")}},model:{value:t.params.showline,callback:function(e){t.$set(t.params,"showline",e)},expression:"params.showline"}})],1),t.params.showline?a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("featureSetting.style.polygon.environment.lineDashed")))]),a("el-switch",{staticStyle:{"margin-left":"12px"},on:{change:function(e){return t.inputChangePolygonPostData(e,"lineDashed")}},model:{value:t.params.lineDashed,callback:function(e){t.$set(t.params,"lineDashed",e)},expression:"params.lineDashed"}})],1):t._e()]),t.params.showline?[a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.showLine.label1")))]),a("el-color-picker",{staticStyle:{"margin-right":"12px"},attrs:{"popper-class":"colorPicker","show-alpha":!1,"color-format":"rgb",size:"mini"},on:{change:function(e){return t.inputChangePolygonPostData(e,"lineColor")}},model:{value:t.params.lineColor,callback:function(e){t.$set(t.params,"lineColor",e)},expression:"params.lineColor"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.showLine.label2")))]),a("el-input",{attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("formRelational.showLine.placeholder")},on:{input:function(e){return t.inputChangePolygonPostData(e,"lineWidth")}},model:{value:t.params.lineWidth,callback:function(e){t.$set(t.params,"lineWidth",e)},expression:"params.lineWidth"}})],1)])]:t._e()]:t._e(),"water"==t.subType?[a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-200"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.baseTop.label")))]),a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.params.base,expression:"params.base"}],attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("formRelational.baseTop.placeholder")},on:{input:function(e){return t.inputChangePolygonPostData(e,"base")}},scopedSlots:t._u([{key:"append",fn:function(){return[a("span",[t._v("m")])]},proxy:!0}],null,!1,1549734309),model:{value:t.params.base,callback:function(e){t.$set(t.params,"base",e)},expression:"params.base"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.elementDatas.list[1].title))]),a("div",{staticClass:"popup-item-center items-center",on:{click:t.togglePanel}},[a("CommonSVG",{attrs:{"icon-class":"xyz",size:16}}),a("span",{staticClass:"margin-left-5"},[t._v(t._s(t.elementDatas.list[1].title))])],1)])]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100",staticStyle:{width:"200px"}},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.speed.label")))]),a("div",{staticClass:"items-center w100"},[a("el-slider",{staticStyle:{width:"100%"},attrs:{min:1,max:10,step:1},on:{change:function(e){return t.inputChangePolygonPostData(e,"interval")}},model:{value:t.params.interval,callback:function(e){t.$set(t.params,"interval",e)},expression:"params.interval"}}),a("span",{staticClass:"slider-num"},[t._v(t._s(t.params.interval))])],1)]),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.opacity.label")))]),a("div",{staticClass:"items-center w100"},[a("el-slider",{staticStyle:{width:"100%"},attrs:{min:.1,max:1,step:.1},on:{change:function(e){return t.inputChangePolygonPostData(e,"opacity")}},model:{value:t.params.opacity,callback:function(e){t.$set(t.params,"opacity",e)},expression:"params.opacity"}}),a("span",{staticClass:"slider-num"},[t._v(t._s(t.params.opacity))])],1)])]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.color.label")))]),a("el-color-picker",{staticStyle:{"margin-right":"12px"},attrs:{"popper-class":"colorPicker","show-alpha":!1,"color-format":"rgb",size:"mini"},on:{change:function(e){return t.inputChangePolygonPostData(e,"color")}},model:{value:t.params.color,callback:function(e){t.$set(t.params,"color",e)},expression:"params.color"}})],1)]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.showLine.label")))]),a("el-switch",{staticStyle:{"margin-left":"12px"},on:{change:function(e){return t.inputChangePolygonPostData(e,"showline")}},model:{value:t.params.showline,callback:function(e){t.$set(t.params,"showline",e)},expression:"params.showline"}})],1),t.params.showline?a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("featureSetting.style.polygon.environment.lineDashed")))]),a("el-switch",{staticStyle:{"margin-left":"12px"},on:{change:function(e){return t.inputChangePolygonPostData(e,"lineDashed")}},model:{value:t.params.lineDashed,callback:function(e){t.$set(t.params,"lineDashed",e)},expression:"params.lineDashed"}})],1):t._e()]),t.params.showline?[a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-150"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.showLine.label1")))]),a("el-color-picker",{staticStyle:{"margin-right":"12px"},attrs:{"popper-class":"colorPicker","show-alpha":!1,"color-format":"rgb",size:"mini"},on:{change:function(e){return t.inputChangePolygonPostData(e,"lineColor")}},model:{value:t.params.lineColor,callback:function(e){t.$set(t.params,"lineColor",e)},expression:"params.lineColor"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.showLine.label2")))]),a("el-input",{attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("formRelational.showLine.placeholder")},on:{input:function(e){return t.inputChangePolygonPostData(e,"lineWidth")}},model:{value:t.params.lineWidth,callback:function(e){t.$set(t.params,"lineWidth",e)},expression:"params.lineWidth"}})],1)])]:t._e()]:t._e(),"flatten"==t.subType||"tileExcavation"==t.subType||"excavation"===t.subType||"tileFlatten"===t.subType?[a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.elementDatas.list[1].title))]),a("div",{staticClass:"popup-item-center items-center",on:{click:t.togglePanel}},[a("CommonSVG",{attrs:{"icon-class":"xyz",size:16}}),a("span",{staticClass:"margin-left-5"},[t._v(t._s(t.elementDatas.list[1].title))])],1)]),a("div",{staticClass:"items-center"},[t._v("  ")])]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s("flatten"==t.subType||"tileFlatten"==t.subType?t.$t("featureSetting.style.polygon.flattenBase"):t.$t("featureSetting.style.polygon.excavationBase"))+" ")]),a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.params.base,expression:"params.base"}],attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("formRelational.baseTop.placeholder")},on:{input:function(e){return t.inputChangePolygonPostData(e,"base")}},scopedSlots:t._u([{key:"append",fn:function(){return[a("span",[t._v("m")])]},proxy:!0}],null,!1,1549734309),model:{value:t.params.base,callback:function(e){t.$set(t.params,"base",e)},expression:"params.base"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s("flatten"==t.subType||"tileFlatten"==t.subType?t.$t("featureSetting.style.polygon.flattenTop"):t.$t("featureSetting.style.polygon.excavationTop"))+" ")]),a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.params.top,expression:"params.top"}],attrs:{size:"mini",type:"number",title:"",placeholder:"flatten"==t.subType?t.$t("featureSetting.style.polygon.placeholder"):t.$t("featureSetting.style.polygon.placeholder1")},on:{input:function(e){return t.inputChangePolygonPostData(e,"top")}},scopedSlots:t._u([{key:"append",fn:function(){return[a("span",[t._v("m")])]},proxy:!0}],null,!1,1549734309),model:{value:t.params.top,callback:function(e){t.$set(t.params,"top",e)},expression:"params.top"}})],1)])]:t._e(),"demOpacity"==t.subType?[a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.elementDatas.list[1].title))]),a("div",{staticClass:"popup-item-center items-center",on:{click:t.togglePanel}},[a("CommonSVG",{attrs:{"icon-class":"xyz",size:16}}),a("span",{staticClass:"margin-left-5"},[t._v(t._s(t.elementDatas.list[1].title))])],1)]),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("featureSetting.style.polygon.demOpacity")))]),a("div",{staticClass:"items-center w100"},[a("el-slider",{staticStyle:{width:"100%"},attrs:{min:.1,max:1,step:.1},on:{change:function(e){return t.inputChangePolygonPostData(e,"terrainOpacity")}},model:{value:t.params.terrainOpacity,callback:function(e){t.$set(t.params,"terrainOpacity",e)},expression:"params.terrainOpacity"}}),a("span",{staticClass:"slider-num"},[t._v(t._s(t.params.terrainOpacity))])],1)]),a("div",{staticClass:"items-center"})])]:t._e(),"surface"==t.subType?[a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.baseTop.label")))]),a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.params.base,expression:"params.base"}],attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("formRelational.baseTop.placeholder")},on:{input:function(e){return t.inputChangePolygonPostData(e,"base")}},scopedSlots:t._u([{key:"append",fn:function(){return[a("span",[t._v("m")])]},proxy:!0}],null,!1,1549734309),model:{value:t.params.base,callback:function(e){t.$set(t.params,"base",e)},expression:"params.base"}})],1)]),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.elementDatas.list[1].title))]),a("div",{staticClass:"popup-item-center items-center",on:{click:t.togglePanel}},[a("CommonSVG",{attrs:{"icon-class":"xyz",size:16}}),a("span",{staticClass:"margin-left-5"},[t._v(t._s(t.elementDatas.list[1].title))])],1)])]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-200"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.color.label")))]),a("el-color-picker",{staticStyle:{"margin-right":"12px"},attrs:{"popper-class":"colorPicker","show-alpha":!1,"color-format":"rgb",size:"mini"},on:{change:function(e){return t.inputChangePolygonPostData(e,"color")}},model:{value:t.params.color,callback:function(e){t.$set(t.params,"color",e)},expression:"params.color"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.opacity.label")))]),a("div",{staticClass:"items-center w100"},[a("el-slider",{staticStyle:{width:"100%"},attrs:{min:.1,max:1,step:.1},on:{change:function(e){return t.inputChangePolygonPostData(e,"opacity")}},model:{value:t.params.opacity,callback:function(e){t.$set(t.params,"opacity",e)},expression:"params.opacity"}}),a("span",{staticClass:"slider-num"},[t._v(t._s(t.params.opacity))])],1)])]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-200"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title",staticStyle:{"min-width":"80px"}},[t._v(" "+t._s(t.$t("featureSetting.style.polygon.demOpacity"))+" "),a("el-tooltip",{staticClass:"box-item",attrs:{enterable:!1,effect:"dark",placement:"top"}},[a("div",{attrs:{slot:"content"},domProps:{innerHTML:t._s(t.$t("featureSetting.style.polygon.tooltip"))},slot:"content"}),a("i",{staticClass:"el-icon-question"})])],1),a("div",{staticClass:"items-center w100"},[a("el-slider",{staticStyle:{width:"100%"},attrs:{min:.1,max:1,step:.1},on:{change:function(e){return t.inputChangePolygonPostData(e,"terrainOpacity")}},model:{value:t.params.terrainOpacity,callback:function(e){t.$set(t.params,"terrainOpacity",e)},expression:"params.terrainOpacity"}}),a("span",{staticClass:"slider-num"},[t._v(t._s(t.params.terrainOpacity))])],1)]),a("div",{staticClass:"items-center"})])]:t._e(),"extrude"==t.subType?[a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.elementDatas.list[1].title))]),a("div",{staticClass:"popup-item-center items-center list-div-117",on:{click:t.togglePanel}},[a("CommonSVG",{attrs:{"icon-class":"xyz",size:16}}),a("span",{staticClass:"margin-left-5"},[t._v(t._s(t.elementDatas.list[1].title))])],1)])]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-160"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.opacity.label")))]),a("div",{staticClass:"items-center w100"},[a("el-slider",{staticStyle:{width:"100%"},attrs:{min:.1,max:1,step:.1},on:{change:function(e){return t.inputChangePolygonPostData(e,"opacity")}},model:{value:t.params.opacity,callback:function(e){t.$set(t.params,"opacity",e)},expression:"params.opacity"}}),a("span",{staticClass:"slider-num"},[t._v(t._s(t.params.opacity))])],1)]),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.color.label")))]),a("el-color-picker",{staticStyle:{"margin-right":"12px"},attrs:{"popper-class":"colorPicker","show-alpha":!1,"color-format":"rgb",size:"mini"},on:{change:function(e){return t.inputChangePolygonPostData(e,"color")}},model:{value:t.params.color,callback:function(e){t.$set(t.params,"color",e)},expression:"params.color"}})],1)]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-160"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("formRelational.border.label1"))+" "),a("el-tooltip",{staticClass:"box-item",attrs:{enterable:!1,effect:"dark",placement:"top"}},[a("div",{attrs:{slot:"content"},domProps:{innerHTML:t._s(t.$t("featureSetting.style.polygon.tooltip1"))},slot:"content"}),a("i",{staticClass:"outline-none el-input__icon el-icon-question"})])],1),a("el-checkbox",{on:{change:function(e){return t.inputChangePolygonPostData(e,"showExtrudeLine")}},model:{value:t.params.showExtrudeLine,callback:function(e){t.$set(t.params,"showExtrudeLine",e)},expression:"params.showExtrudeLine"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.border.label")))]),a("el-color-picker",{staticStyle:{"margin-right":"12px"},attrs:{"popper-class":"colorPicker","show-alpha":!1,"color-format":"rgb",size:"mini"},on:{change:function(e){return t.inputChangePolygonPostData(e,"extrudeLineColor")}},model:{value:t.params.extrudeLineColor,callback:function(e){t.$set(t.params,"extrudeLineColor",e)},expression:"params.extrudeLineColor"}})],1)]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.baseTop.label")))]),a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.params.base,expression:"params.base"}],attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("formRelational.baseTop.placeholder")},on:{input:function(e){return t.inputChangePolygonPostData(e,"base")}},scopedSlots:t._u([{key:"append",fn:function(){return[a("span",[t._v("m")])]},proxy:!0}],null,!1,1549734309),model:{value:t.params.base,callback:function(e){t.$set(t.params,"base",e)},expression:"params.base"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.top.label")))]),a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.params.top,expression:"params.top"}],attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("formRelational.top.placeholder")},on:{input:function(e){return t.inputChangePolygonPostData(e,"top")}},scopedSlots:t._u([{key:"append",fn:function(){return[a("span",[t._v("m")])]},proxy:!0}],null,!1,1549734309),model:{value:t.params.top,callback:function(e){t.$set(t.params,"top",e)},expression:"params.top"}})],1)])]:t._e(),t.elementDatas.list[1].panelState?a("div",{staticClass:"regional-coordinates-container reginoal-dialog"},[a("div",{staticClass:"title"},[a("span",[t._v(t._s(t.elementDatas.list[1].title))]),a("div",[a("span",{staticClass:"downloadExcel",on:{click:t.downloadExcel}},[t._v(t._s(t.$t("others.export")))]),a("i",{staticClass:"el-icon-plus cursor-btn margin-right-8",on:{click:function(e){return t.addPoint()}}}),a("i",{staticClass:"el-icon-close cursor-btn",on:{click:t.togglePanel}})])]),a("div",{staticClass:"position-list-wrap"},t._l(t.elementDatas.list[1].position,(function(e,s){return a("div",{key:s,staticClass:"position-list"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:e[0],expression:"pos[0]"}],attrs:{type:"number",title:"",size:"mini",placeholder:"X"},on:{input:function(e){return t.inputChangePolygonPostData(e,"positions")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v("X")])]},proxy:!0}],null,!0),model:{value:e[0],callback:function(a){t.$set(e,0,a)},expression:"pos[0]"}})],1),a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:e[1],expression:"pos[1]"}],attrs:{type:"number",title:"",size:"mini",placeholder:"Y"},on:{input:function(e){return t.inputChangePolygonPostData(e,"positions")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v("Y")]},proxy:!0}],null,!0),model:{value:e[1],callback:function(a){t.$set(e,1,a)},expression:"pos[1]"}})],1)],1),a("div",{staticClass:"delete-btn"},[a("span",{staticClass:"coord-box",on:{click:function(e){return t.clickCheckedOffsetCoordinate(s)}}},[a("CommonSVG",{attrs:{color:t.curIndex===s?"var(--theme)":"#FFFFFF",size:"16","icon-class":"select_coord"}})],1),t.elementDatas.list[1].position.length>3?a("i",{staticClass:"el-icon el-icon-remove-outline icons",on:{click:function(e){return t.delPoints(s)}}}):t._e()])],1)})),0)]):t._e()],2)},ot=[],rt=(a("25f0"),a("caad"),a("a15b"),{name:"PolygonSetting",props:["currentElement"],components:{CommonSVG:function(){return a.e("chunk-51f90655").then(a.bind(null,"17d0"))}},data:function(){return{itemGutter:10,elementDatas:{id:"",longitude:0,latitude:0,altitude:0,rotation:[0,0,0],offset:[0,0,0],list:[{title:this.$t("formRelational.image.label1"),value:"",optionState:!0,validate:!0,validateState:null,thumbState:!1},{title:this.$t("dialog.coordinate.name"),position:[[0,0],[0,0],[0,0]],optionItem:{name:"position",value:[0,0]},panelState:!1}]},params:{color:"rgb(255, 50, 0)",opacity:1,top:0,base:0,interval:1,showline:!1,imageRepeat:{x:1,y:1},terrainOpacity:.1,terrainExcavation:!1,terrainFlatten:!1,tileExcavation:!1,tileFlatten:!1,lineColor:65493,lineDashed:!1,lineWidth:4,extrudeLineColor:"rgb(0,255,255)",showExtrudeLine:!0},subType:"",thumbList:[],curIndex:"",speedEnum:[1e3,900,800,700,600,500,400,300,200,100]}},created:function(){this.init(),this.getPublicImg(),this.inputChangePolygonPostData=T()(this.inputChangePolygonPostData,800)},methods:{init:function(){var t=window.scene.features.get(this.currentElement.id);this.subType=t.data.subType||this.currentElement.subType,this.elementDatas.id=this.currentElement.id,this.elementDatas.list[0].value=t.data.image,this.elementDatas.list[1].position=t.data.positions,this.params.color=t.data.color,this.params.opacity=t.data.opacity,this.params.top=t.data.top,this.params.base=t.data.base,this.params.interval=this.speedEnum.indexOf(t.data.interval)+1,this.params.showline=t.data.showline,this.params.imageRepeat=t.data.imageRepeat,this.params.terrainOpacity=t.data.terrainOpacity,this.params.extrudeLineColor=t.data.extrudeLineColor,this.params.showExtrudeLine=t.data.showExtrudeLine;var e=t.data.lineColor;isNaN(e)||(e="#00"+e.toString(16)),this.params.lineColor=e,this.params.lineDashed=t.data.lineDashed,this.params.lineWidth=t.data.lineWidth,"environment"!==this.subType&&(this.elementDatas.list[0].validate=!1);var a=["flatten","excavation","tileExcavation","tileFlatten"];"extrude"==this.subType?this.params.top=t.data.top-t.data.base:a.includes(this.subType)&&(this.params.top=t.data.base-t.data.top)},openThumb:function(){this.elementDatas.list[0].thumbState=!this.elementDatas.list[0].thumbState},checkPolygonImg:function(t){this.elementDatas.list[0].value=t.img,this.inputChangePolygonPostData()},getPublicImg:function(){var t=this,e=a("ee57");e.keys().length>0&&e.keys().forEach((function(e,a){var s=e.replace(/(.*\/)*([\s\S]*?)/gi,"$2"),i=(s.split(".")[0],"".concat("","/image/environment/").concat(s));t.thumbList.push({name:s,img:i})}))},togglePanel:function(){this.elementDatas.list[1].panelState=!this.elementDatas.list[1].panelState,""!==this.curIndex&&this.offEvent()},delPoints:function(t){this.elementDatas.list[1].position.splice(t,1),this.inputChangePolygonPostData("","positions"),""!==this.curIndex&&this.offEvent()},addPoint:function(){this.elementDatas.list[1].position.push([0,0]),""!==this.curIndex&&this.offEvent()},clickCheckedOffsetCoordinate:function(t){""===this.curIndex?(this.curIndex=t,window.scene.mv.status.selectable=!1,this.$message.success(this.$t("messageTips.checkCoordinate")),window.scene.mv.events.singleSelection.on("default",this.onCheckedOffsetCoordinate)):this.curIndex===t&&this.offEvent()},offEvent:function(){this.curIndex="",window.scene.mv.status.selectable=!0,this.$message.success(this.$t("messageTips.closeCheckCoordinate")),window.scene.mv.events.singleSelection.off("default",this.onCheckedOffsetCoordinate)},onCheckedOffsetCoordinate:function(t){var e=window.scene.mv._THREE,a=window.scene.queryPosition(new e.Vector2(t.clientX,t.clientY));if(""!=a&&void 0!=a){var s=this.curIndex;this.$set(this.elementDatas.list[1].position[s],0,a.x),this.$set(this.elementDatas.list[1].position[s],1,a.y),this.$set(this.elementDatas.list[1].position[s],2,a.z),this.curIndex="",this.inputChangePolygonPostData("","positions")}else this.$message.error(this.$t("messageTips.errorCheckCoordinate"));window.scene.mv.status.selectable=!0,window.scene.mv.events.singleSelection.off("default",this.onCheckedOffsetCoordinate)},inputChangeImageAddress:function(t){var e=window.scene.features.get(this.currentElement.id);window.scene.postData({image:t},e.dataKey)},inputChangePolygonPostData:function(t,e,a){var s=window.scene.features.get(this.currentElement.id),i={},n=null;switch("showline"==e&&"color"==e||(n=parseFloat(t+"")),this.subType){case"surface":i.fill=!0;break;case"extrude":i.extrude=!0;break;case"flatten":i.terrainFlatten=!0,i.flatten=!0;break;case"tileFlatten":i.tileFlatten=!0,i.flatten=!0;break;case"environment":i.fill=!0,i.environment=!0,i.image=this.elementDatas.list[0].value;break;case"excavation":i.terrainExcavation=!0,i.excavation=!0;break;case"tileExcavation":i.tileExcavation=!0,i.excavation=!0;break;case"demOpacity":i.excavation=!0;break;case"water":i.water=!0;break}var l=["flatten","excavation","tileExcavation","tileFlatten"];switch(e){case"base":l.includes(this.subType)&&(i.top=-1*Math.abs(this.params.top)+n),"extrude"==this.subType&&(i.top=Math.abs(this.params.top)+n);break;case"top":if(l.includes(this.subType)&&(n=-1*Math.abs(n)+parseFloat(this.params.base+""),isNaN(n)))return;"extrude"==this.subType&&(n=Math.abs(n)+parseFloat(this.params.base+""));break;case"interval":n=this.speedEnum[t-1];break;case"color":case"lineColor":case"showline":case"lineDashed":case"terrainOpacity":case"extrudeLineColor":case"showExtrudeLine":n=t;break;case"imageRepeat":n=this.params.imageRepeat,n[a]=""==t||t<1?1:t;break}i[e]=n;var o=[];this.elementDatas.list[1].position.forEach((function(t,e){o[e]=t.map((function(t){return parseFloat(t+"")}))})),i.positions=o,window.scene.postData(Object.assign(i,{terrainOpacity:this.params.terrainOpacity}),s.dataKey),setTimeout((function(){window.scene.render()}),300)},downloadExcel:function(){var t=document.createElement("textarea");t.setAttribute("readonly","");var e=[],a=this.elementDatas.list[1].position;e=a.map((function(t){return t=t.join(","),t})),t.value=e.join(";"),t.style.position="absolute",t.style.left="-9999px",document.body.appendChild(t);var s=document.getSelection().rangeCount>0&&document.getSelection().getRangeAt(0);t.select(),document.execCommand("copy"),document.body.removeChild(t),this.$message.success(this.$t("messageTips.copySuccess1")),s&&(document.getSelection().removeAllRanges(),document.getSelection().addRange(s))}},beforeDestroy:function(){""!==this.curIndex&&this.offEvent()}}),ct=rt,ut=(a("eb3b"),Object(d["a"])(ct,lt,ot,!1,null,"b2c8a468",null)),mt=ut.exports,dt=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex relative w100"},[a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-200"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.altitude.label")))]),a("el-input",{attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("formRelational.altitude.placeholder")},on:{input:function(e){return t.inputChangeOrigin(e,"altitude")}},model:{value:t.elementDatas.altitude,callback:function(e){t.$set(t.elementDatas,"altitude",e)},expression:"elementDatas.altitude"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.elementDatas.list[1].title))]),a("div",{staticClass:"popup-item-center items-center",on:{click:t.togglePanel}},[a("CommonSVG",{attrs:{"icon-class":"xyz",size:16}}),a("span",{staticClass:"margin-left-5"},[t._v(t._s(t.elementDatas.list[1].title))])],1)])]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-200"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.color.label")))]),a("el-color-picker",{staticStyle:{"margin-right":"12px"},attrs:{"popper-class":"colorPicker","show-alpha":!1,"color-format":"rgb",size:"mini"},on:{change:function(e){return t.inputChangeRipplewallPostData(e,"color")}},model:{value:t.params.color,callback:function(e){t.$set(t.params,"color",e)},expression:"params.color"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.opacity.label")))]),a("div",{staticClass:"items-center w100"},[a("el-slider",{staticStyle:{width:"100%"},attrs:{min:.1,max:1,step:.1},on:{change:function(e){return t.inputChangeRipplewallPostData(e,"opacity")}},model:{value:t.params.opacity,callback:function(e){t.$set(t.params,"opacity",e)},expression:"params.opacity"}}),a("span",{staticClass:"slider-num"},[t._v(t._s(t.params.opacity))])],1)])]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-200"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.top.label")))]),a("el-input",{attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("formRelational.top.placeholder")},on:{input:function(e){return t.inputChangeRipplewallPostData(e,"height")}},scopedSlots:t._u([{key:"append",fn:function(){return[a("span",[t._v("m")])]},proxy:!0}]),model:{value:t.params.height,callback:function(e){t.$set(t.params,"height",e)},expression:"params.height"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.speed.label")))]),a("div",{staticClass:"items-center w100"},[a("el-slider",{staticStyle:{width:"100%"},attrs:{min:1,max:10,step:1},on:{change:function(e){return t.inputChangeRipplewallPostData(e,"speed")}},model:{value:t.params.speed,callback:function(e){t.$set(t.params,"speed",e)},expression:"params.speed"}}),a("span",{staticClass:"slider-num"},[t._v(t._s(t.params.speed))])],1)])]),t.elementDatas.list[1].panelState?a("div",{staticClass:"regional-coordinates-container reginoal-dialog"},[a("div",{staticClass:"title"},[a("span",[t._v(t._s(t.elementDatas.list[1].title))]),a("div",[a("i",{staticClass:"el-icon-plus cursor-btn margin-right-8",on:{click:function(e){return t.addPoint()}}}),a("i",{staticClass:"el-icon-close cursor-btn",on:{click:t.togglePanel}})])]),a("div",{staticClass:"position-list-wrap"},t._l(t.elementDatas.list[1].units,(function(e,s){return a("div",{key:s,staticClass:"position-list"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:e.position.x,expression:"pos.position.x"}],attrs:{type:"number",title:"",size:"mini",placeholder:"X"},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v("X")])]},proxy:!0}],null,!0),model:{value:e.position.x,callback:function(a){t.$set(e.position,"x",a)},expression:"pos.position.x"}})],1),a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:e.position.y,expression:"pos.position.y"}],attrs:{type:"number",title:"",size:"mini",placeholder:"Y"},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v("Y")]},proxy:!0}],null,!0),model:{value:e.position.y,callback:function(a){t.$set(e.position,"y",a)},expression:"pos.position.y"}})],1)],1),a("div",{staticClass:"delete-btn"},[a("span",{staticClass:"coord-box",on:{click:function(e){return t.clickCheckedOffsetCoordinate(s)}}},[a("CommonSVG",{attrs:{color:t.curIndex===s?"var(--theme)":"#FFFFFF",size:"16","icon-class":"select_coord"}})],1),t.elementDatas.list[1].units.length>3?a("i",{staticClass:"el-icon el-icon-remove-outline icons",on:{click:function(e){return t.delPoints(s)}}}):t._e()])],1)})),0)]):t._e()])},pt=[],ht={name:"RipplewallSetting",props:["currentElement"],mixins:[g["a"]],components:{CommonSVG:function(){return a.e("chunk-51f90655").then(a.bind(null,"17d0"))}},data:function(){return{itemGutter:10,elementDatas:{id:"",longitude:0,latitude:0,altitude:0,rotation:[0,0,0],offset:[0,0,0],list:[{title:"",value:""},{title:this.$t("dialog.coordinate.name"),units:[],optionItem:{name:"units",value:{x:0,y:0}},panelState:!1}]},params:{color:"rgb(255, 50, 0)",opacity:1,height:0,speed:1},curIndex:""}},created:function(){this.init(),this.inputChangeRipplewallPostData=T()(this.inputChangeRipplewallPostData,800)},methods:{init:function(){var t=window.scene.features.get(this.currentElement.id);this.elementDatas.id=this.currentElement.id,this.elementDatas.list[1].units=JSON.parse(JSON.stringify(t.data.units)),this.params.color=t.data.color,this.params.opacity=t.data.opacity,this.params.height=t.data.height,this.params.speed=Math.ceil(33.33*t.data.speed),this.elementDatas.altitude=t.altitude,this.params.altitude=t.altitude},inputChangeRipplewallPostData:function(t,e){var a=window.scene.features.get(this.currentElement.id),s={height:parseFloat(this.params.height+"")};switch(e){case"speed":t=Math.floor(t/33.33*100)/100;break;case"height":t=parseFloat(t+"");break;case"units":var i=JSON.parse(JSON.stringify(this.elementDatas.list[1].units));i.forEach((function(t){t.position.x=parseFloat(t.position.x+""),t.position.y=parseFloat(t.position.y+"")})),t=i;break}s[e]=t,window.scene.postData(s,a.dataKey)},togglePanel:function(){this.elementDatas.list[1].panelState=!this.elementDatas.list[1].panelState,""!==this.curIndex&&this.offEvent()},delPoints:function(t){this.elementDatas.list[1].units.splice(t,1),this.inputChangeRipplewallPostData("","units"),""!==this.curIndex&&this.offEvent()},addPoint:function(){this.elementDatas.list[1].units.push({id:this.elementDatas.list[1].units.length+1,position:{x:0,y:0}}),""!==this.curIndex&&this.offEvent()},clickCheckedOffsetCoordinate:function(t){""===this.curIndex?(this.curIndex=t,window.scene.mv.status.selectable=!1,this.$message.success(this.$t("messageTips.checkCoordinate")),window.scene.mv.events.singleSelection.on("default",this.onCheckedOffsetCoordinate)):this.curIndex===t&&this.offEvent()},offEvent:function(){this.curIndex="",window.scene.mv.status.selectable=!0,this.$message.success(this.$t("messageTips.closeCheckCoordinate")),window.scene.mv.events.singleSelection.off("default",this.onCheckedOffsetCoordinate)},onCheckedOffsetCoordinate:function(t){var e=window.scene.mv._THREE,a=window.scene.queryPosition(new e.Vector2(t.clientX,t.clientY));if(""!=a&&void 0!=a){var s=this.curIndex;this.elementDatas.list[1].units[s].position.x=a.x,this.elementDatas.list[1].units[s].position.y=a.y,this.curIndex="",this.inputChangeRipplewallPostData("","units")}else this.$message.error(this.$t("messageTips.errorCheckCoordinate"));window.scene.mv.status.selectable=!0,window.scene.mv.events.singleSelection.off("default",this.onCheckedOffsetCoordinate)},downloadExcel:function(){var t=document.createElement("textarea");t.setAttribute("readonly","");var e=[],a=this.elementDatas.list[1].position;e=a.map((function(t){return t=t.join(","),t})),t.value=e.join(";"),t.style.position="absolute",t.style.left="-9999px",document.body.appendChild(t);var s=document.getSelection().rangeCount>0&&document.getSelection().getRangeAt(0);t.select(),document.execCommand("copy"),document.body.removeChild(t),this.$message.success(this.$t("messageTips.copySuccess1")),s&&(document.getSelection().removeAllRanges(),document.getSelection().addRange(s))}},beforeDestroy:function(){""!==this.curIndex&&this.offEvent()}},ft=ht,vt=(a("a858"),Object(d["a"])(ft,dt,pt,!1,null,"306dc3d4",null)),gt=vt.exports,yt=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex relative"},[a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{staticClass:"space-between-center",attrs:{span:12}},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.basePoint.label")))]),a("el-tooltip",{attrs:{effect:"dark",content:t.$t("formRelational.basePoint.placeholder"),placement:"top"}},[a("span",{staticClass:"outline-none cursor-btn",on:{click:function(e){return t.clickCheckedCoordinate()}}},[a("CommonSVG",{attrs:{color:"var(--theme)",size:"18","icon-class":"select_coord"}})],1)])],1),a("el-col",{attrs:{span:12}},[a("el-input",{class:{"is-error":t.inputError&&!t.styleFormDatas.validate.longitude},attrs:{type:"number",title:"",size:"mini",placeholder:t.$t("formRelational.longitude.placeholder")},on:{input:function(e){return t.inputChangeOrigin(e,"longitude")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v(t._s(t.$t("formRelational.longitude.label")))])]},proxy:!0}]),model:{value:t.elementDatas.longitude,callback:function(e){t.$set(t.elementDatas,"longitude",e)},expression:"elementDatas.longitude"}})],1)],1),a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{attrs:{span:12}},[a("el-input",{class:{"is-error":t.inputError&&!t.styleFormDatas.validate.latitude},attrs:{type:"number",title:"",size:"mini",placeholder:t.$t("formRelational.latitude.placeholder")},on:{input:function(e){return t.inputChangeOrigin(e,"latitude")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v(t._s(t.$t("formRelational.latitude.label")))]},proxy:!0}]),model:{value:t.elementDatas.latitude,callback:function(e){t.$set(t.elementDatas,"latitude",e)},expression:"elementDatas.latitude"}})],1),a("el-col",{attrs:{span:12}},[a("el-input",{class:{"is-error":t.inputError&&!t.styleFormDatas.validate.altitude},attrs:{type:"number",title:"",size:"mini",placeholder:t.$t("formRelational.altitude.placeholder")},on:{input:function(e){return t.inputChangeOrigin(e,"altitude")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v(t._s(t.$t("formRelational.altitude.label")))])]},proxy:!0}]),model:{value:t.elementDatas.altitude,callback:function(e){t.$set(t.elementDatas,"altitude",e)},expression:"elementDatas.altitude"}})],1)],1)],1),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-offset"},[a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{staticClass:"space-between-center",attrs:{span:12}},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.translate.label")))]),a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:t.$t("formRelational.translate.tooltip"),placement:"top"}},[a("span",{staticClass:"outline-none cursor-btn",on:{click:function(e){return t.handleModelTransform("translate")}}},[a("CommonSVG",{attrs:{color:"var(--theme)",size:"17","icon-class":"crosshair_move_feature"}})],1)])],1),a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.offset[0],expression:"elementDatas.offset[0]"}],attrs:{type:"number",title:"",size:"mini",placeholder:"X"},on:{input:function(e){return t.inputChangeOffset(e,"x")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v("X")])]},proxy:!0}]),model:{value:t.elementDatas.offset[0],callback:function(e){t.$set(t.elementDatas.offset,0,e)},expression:"elementDatas.offset[0]"}})],1)],1),a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.offset[1],expression:"elementDatas.offset[1]"}],attrs:{type:"number",title:"",size:"mini",placeholder:"Y"},on:{input:function(e){return t.inputChangeOffset(e,"y")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v("Y")]},proxy:!0}]),model:{value:t.elementDatas.offset[1],callback:function(e){t.$set(t.elementDatas.offset,1,e)},expression:"elementDatas.offset[1]"}})],1),a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.offset[2],expression:"elementDatas.offset[2]"}],attrs:{type:"number",title:"",size:"mini",placeholder:"Z"},on:{input:function(e){return t.inputChangeOffset(e,"z")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v("Z")]},proxy:!0}]),model:{value:t.elementDatas.offset[2],callback:function(e){t.$set(t.elementDatas.offset,2,e)},expression:"elementDatas.offset[2]"}})],1)],1)],1),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-200"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.color.label")))]),a("el-color-picker",{staticStyle:{"margin-right":"12px"},attrs:{"popper-class":"colorPicker","show-alpha":!1,"color-format":"rgb",size:"mini"},on:{change:function(e){return t.inputChangeRadarPostData(e,"color")}},model:{value:t.params.color,callback:function(e){t.$set(t.params,"color",e)},expression:"params.color"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.opacity.label")))]),a("div",{staticClass:"items-center w100"},[a("el-slider",{staticStyle:{width:"100%"},attrs:{min:.1,max:1,step:.1},on:{change:function(e){return t.inputChangeRadarPostData(e,"opacity")}},model:{value:t.params.opacity,callback:function(e){t.$set(t.params,"opacity",e)},expression:"params.opacity"}}),a("span",{staticClass:"slider-num"},[t._v(t._s(t.params.opacity))])],1)])]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100",staticStyle:{width:"200px"}},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.radius.label")))]),a("el-input",{attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("formRelational.radius.placeholder")},on:{input:function(e){return t.inputChangeRadarPostData(e,"radius")}},scopedSlots:t._u([{key:"append",fn:function(){return[a("span",[t._v("m")])]},proxy:!0}]),model:{value:t.params.radius,callback:function(e){t.$set(t.params,"radius",e)},expression:"params.radius"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.speed.label")))]),a("div",{staticClass:"items-center w100"},[a("el-slider",{staticStyle:{width:"100%"},attrs:{min:1,max:10,step:1},on:{change:function(e){return t.inputChangeRadarPostData(e,"speed")}},model:{value:t.params.speed,callback:function(e){t.$set(t.params,"speed",e)},expression:"params.speed"}}),a("span",{staticClass:"slider-num"},[t._v(t._s(t.params.speed))])],1)])]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100",staticStyle:{width:"200px"}},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("featureSetting.style.radar.label"))+" ")]),a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.params.width,expression:"params.width"}],attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("featureSetting.style.radar.placeholder")},on:{input:function(e){return t.inputChangeRadarPostData(e,"width")}},model:{value:t.params.width,callback:function(e){t.$set(t.params,"width",e)},expression:"params.width"}})],1),a("div",{staticClass:"items-center"},[t._v("  ")])]),a("div",{staticClass:"after-lineX"})])},Ct=[],Dt={name:"RadarSetting",props:["currentElement","elementTransformObj"],mixins:[g["a"]],data:function(){return{itemGutter:10,elementDatas:{id:"",longitude:0,latitude:0,altitude:0,rotation:[0,0,0],offset:[0,0,0],list:[]},params:{color:"rgb(255, 50, 0)",opacity:1,height:0,radius:5,speed:1,width:90}}},created:function(){this.init()},methods:{init:function(){var t=window.scene.features.get(this.currentElement.id);this.elementDatas.id=this.currentElement.id,this.elementDatas.longitude=t.origin[0],this.elementDatas.latitude=t.origin[1],this.elementDatas.altitude=t.altitude,this.elementDatas.offset=t.offset,this.params.color=t.data.color,this.params.opacity=t.data.opacity,this.params.radius=t.data.radius,this.params.speed=t.data.speed,this.params.width=180*parseFloat(t.data.width+"")/Math.PI},togglePanel:function(){this.elementDatas.list[1].panelState=!this.elementDatas.list[1].panelState},inputChangeRadarPostData:function(t,e){var a=window.scene.features.get(this.currentElement.id),s={radius:parseFloat(this.params.radius+"")};switch(e){case"opacity":case"radius":case"speed":t=parseFloat(t+"");break;case"width":t=parseFloat(t+"")/180*Math.PI;break}s[e]=t,window.scene.postData(s,a.dataKey)}}},bt=Dt,wt=(a("48d4"),Object(d["a"])(bt,yt,Ct,!1,null,"209d4580",null)),_t=wt.exports,xt=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex relative"},[a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{staticClass:"space-between-center",attrs:{span:12}},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.basePoint.label")))]),a("el-tooltip",{attrs:{effect:"dark",content:t.$t("formRelational.basePoint.placeholder"),placement:"top"}},[a("span",{staticClass:"outline-none cursor-btn",on:{click:function(e){return t.clickCheckedCoordinate()}}},[a("CommonSVG",{attrs:{color:"var(--theme)",size:"18","icon-class":"select_coord"}})],1)])],1),a("el-col",{attrs:{span:12}},[a("el-input",{class:{"is-error":t.inputError&&!t.styleFormDatas.validate.longitude},attrs:{type:"number",title:"",size:"mini",placeholder:t.$t("formRelational.longitude.placeholder")},on:{input:function(e){return t.inputChangeOrigin(e,"longitude")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v(t._s(t.$t("formRelational.longitude.label")))])]},proxy:!0}]),model:{value:t.elementDatas.longitude,callback:function(e){t.$set(t.elementDatas,"longitude",e)},expression:"elementDatas.longitude"}})],1)],1),a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{attrs:{span:12}},[a("el-input",{class:{"is-error":t.inputError&&!t.styleFormDatas.validate.latitude},attrs:{type:"number",title:"",size:"mini",placeholder:t.$t("formRelational.latitude.placeholder")},on:{input:function(e){return t.inputChangeOrigin(e,"latitude")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v(t._s(t.$t("formRelational.latitude.label")))]},proxy:!0}]),model:{value:t.elementDatas.latitude,callback:function(e){t.$set(t.elementDatas,"latitude",e)},expression:"elementDatas.latitude"}})],1),a("el-col",{attrs:{span:12}},[a("el-input",{class:{"is-error":t.inputError&&!t.styleFormDatas.validate.altitude},attrs:{type:"number",title:"",size:"mini",placeholder:t.$t("formRelational.altitude.placeholder")},on:{input:function(e){return t.inputChangeOrigin(e,"altitude")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v(t._s(t.$t("formRelational.altitude.label")))])]},proxy:!0}]),model:{value:t.elementDatas.altitude,callback:function(e){t.$set(t.elementDatas,"altitude",e)},expression:"elementDatas.altitude"}})],1)],1)],1),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-offset"},[a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{staticClass:"space-between-center",attrs:{span:12}},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.translate.label")))]),a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:t.$t("formRelational.translate.tooltip"),placement:"top"}},[a("span",{staticClass:"outline-none cursor-btn",on:{click:function(e){return t.handleModelTransform("translate")}}},[a("CommonSVG",{attrs:{color:"var(--theme)",size:"17","icon-class":"crosshair_move_feature"}})],1)])],1),a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.offset[0],expression:"elementDatas.offset[0]"}],attrs:{type:"number",title:"",size:"mini",placeholder:"X"},on:{input:function(e){return t.inputChangeOffset(e,"x")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v("X")])]},proxy:!0}]),model:{value:t.elementDatas.offset[0],callback:function(e){t.$set(t.elementDatas.offset,0,e)},expression:"elementDatas.offset[0]"}})],1)],1),a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.offset[1],expression:"elementDatas.offset[1]"}],attrs:{type:"number",title:"",size:"mini",placeholder:"Y"},on:{input:function(e){return t.inputChangeOffset(e,"y")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v("Y")]},proxy:!0}]),model:{value:t.elementDatas.offset[1],callback:function(e){t.$set(t.elementDatas.offset,1,e)},expression:"elementDatas.offset[1]"}})],1),a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.offset[2],expression:"elementDatas.offset[2]"}],attrs:{type:"number",title:"",size:"mini",placeholder:"Z"},on:{input:function(e){return t.inputChangeOffset(e,"z")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v("Z")]},proxy:!0}]),model:{value:t.elementDatas.offset[2],callback:function(e){t.$set(t.elementDatas.offset,2,e)},expression:"elementDatas.offset[2]"}})],1)],1)],1),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-200"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.color.label")))]),a("el-color-picker",{staticStyle:{"margin-right":"12px"},attrs:{"popper-class":"colorPicker","show-alpha":!1,"color-format":"rgb",size:"mini"},on:{change:function(e){return t.inputChangeShieldPostData(e,"color")}},model:{value:t.params.color,callback:function(e){t.$set(t.params,"color",e)},expression:"params.color"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.opacity.label")))]),a("div",{staticClass:"items-center w100"},[a("el-slider",{staticStyle:{width:"100%"},attrs:{min:.1,max:1,step:.1},on:{change:function(e){return t.inputChangeShieldPostData(e,"opacity")}},model:{value:t.params.opacity,callback:function(e){t.$set(t.params,"opacity",e)},expression:"params.opacity"}}),a("span",{staticClass:"slider-num"},[t._v(t._s(t.params.opacity))])],1)])]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100",staticStyle:{width:"200px"}},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.radius.label")))]),a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.params.radius,expression:"params.radius"}],attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("formRelational.radius.placeholder")},on:{input:function(e){return t.inputChangeShieldPostData(e,"radius")}},scopedSlots:t._u([{key:"append",fn:function(){return[a("span",[t._v("m")])]},proxy:!0}]),model:{value:t.params.radius,callback:function(e){t.$set(t.params,"radius",e)},expression:"params.radius"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.speed.label")))]),a("div",{staticClass:"items-center w100"},[a("el-slider",{staticStyle:{width:"100%"},attrs:{min:1,max:10,step:1},on:{change:function(e){return t.inputChangeShieldPostData(e,"speed")}},model:{value:t.params.speed,callback:function(e){t.$set(t.params,"speed",e)},expression:"params.speed"}}),a("span",{staticClass:"slider-num"},[t._v(t._s(t.params.speed))])],1)])]),a("div",{staticClass:"after-lineX"})])},kt=[],$t={name:"ShieldSetting",props:["currentElement","elementTransformObj"],mixins:[g["a"]],data:function(){return{itemGutter:10,elementDatas:{id:"",longitude:0,latitude:0,altitude:0,rotation:[0,0,0],offset:[0,0,0],list:[]},params:{color:"rgb(255, 50, 0)",opacity:1,height:0,radius:5,speed:1}}},created:function(){this.init()},methods:{init:function(){var t=window.scene.features.get(this.currentElement.id);this.elementDatas.id=this.currentElement.id,this.elementDatas.longitude=t.origin[0],this.elementDatas.latitude=t.origin[1],this.elementDatas.altitude=t.altitude,this.elementDatas.offset=t.offset,this.params.color=t.data.color,this.params.opacity=t.data.opacity,this.params.radius=t.data.radius,this.params.speed=Math.ceil(33.33*t.data.speed)},togglePanel:function(){this.elementDatas.list[1].panelState=!this.elementDatas.list[1].panelState},inputChangeShieldPostData:function(t,e){var a=window.scene.features.get(this.currentElement.id),s={};"color"!=e&&(t=parseFloat(t+"")),"speed"==e&&(t=Math.floor(t/33.33*100)/100),s[e]=t,window.scene.postData(s,a.dataKey)}}},St=$t,Pt=(a("784a"),Object(d["a"])(St,xt,kt,!1,null,"2e49cd12",null)),Et=Pt.exports,Rt=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex relative"},[a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{staticClass:"space-between-center",attrs:{span:12}},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.basePoint.label")))]),a("el-tooltip",{attrs:{effect:"dark",content:t.$t("formRelational.basePoint.placeholder"),placement:"top"}},[a("span",{staticClass:"outline-none cursor-btn",on:{click:function(e){return t.clickCheckedCoordinate()}}},[a("CommonSVG",{attrs:{color:"var(--theme)",size:"18","icon-class":"select_coord"}})],1)])],1),a("el-col",{attrs:{span:12}},[a("el-input",{class:{"is-error":t.inputError&&!t.styleFormDatas.validate.longitude},attrs:{type:"number",title:"",size:"mini",placeholder:t.$t("formRelational.longitude.placeholder")},on:{input:function(e){return t.inputChangeOrigin(e,"longitude")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v(t._s(t.$t("formRelational.longitude.label")))])]},proxy:!0}]),model:{value:t.elementDatas.longitude,callback:function(e){t.$set(t.elementDatas,"longitude",e)},expression:"elementDatas.longitude"}})],1)],1),a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{attrs:{span:12}},[a("el-input",{class:{"is-error":t.inputError&&!t.styleFormDatas.validate.latitude},attrs:{type:"number",title:"",size:"mini",placeholder:t.$t("formRelational.latitude.placeholder")},on:{input:function(e){return t.inputChangeOrigin(e,"latitude")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v(t._s(t.$t("formRelational.latitude.label")))]},proxy:!0}]),model:{value:t.elementDatas.latitude,callback:function(e){t.$set(t.elementDatas,"latitude",e)},expression:"elementDatas.latitude"}})],1),a("el-col",{attrs:{span:12}},[a("el-input",{class:{"is-error":t.inputError&&!t.styleFormDatas.validate.altitude},attrs:{type:"number",title:"",size:"mini",placeholder:t.$t("formRelational.altitude.placeholder")},on:{input:function(e){return t.inputChangeOrigin(e,"altitude")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v(t._s(t.$t("formRelational.altitude.label")))])]},proxy:!0}]),model:{value:t.elementDatas.altitude,callback:function(e){t.$set(t.elementDatas,"altitude",e)},expression:"elementDatas.altitude"}})],1)],1)],1),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-offset"},[a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{staticClass:"space-between-center",attrs:{span:12}},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.translate.label")))]),a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:t.$t("formRelational.translate.tooltip"),placement:"top"}},[a("span",{staticClass:"outline-none cursor-btn",on:{click:function(e){return t.handleModelTransform("translate")}}},[a("CommonSVG",{attrs:{color:"var(--theme)",size:"17","icon-class":"crosshair_move_feature"}})],1)])],1),a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.offset[0],expression:"elementDatas.offset[0]"}],attrs:{type:"number",title:"",size:"mini",placeholder:"X"},on:{input:function(e){return t.inputChangeOffset(e,"x")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v("X")])]},proxy:!0}]),model:{value:t.elementDatas.offset[0],callback:function(e){t.$set(t.elementDatas.offset,0,e)},expression:"elementDatas.offset[0]"}})],1)],1),a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.offset[1],expression:"elementDatas.offset[1]"}],attrs:{type:"number",title:"",size:"mini",placeholder:"Y"},on:{input:function(e){return t.inputChangeOffset(e,"y")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v("Y")]},proxy:!0}]),model:{value:t.elementDatas.offset[1],callback:function(e){t.$set(t.elementDatas.offset,1,e)},expression:"elementDatas.offset[1]"}})],1),a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.offset[2],expression:"elementDatas.offset[2]"}],attrs:{type:"number",title:"",size:"mini",placeholder:"Z"},on:{input:function(e){return t.inputChangeOffset(e,"z")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v("Z")]},proxy:!0}]),model:{value:t.elementDatas.offset[2],callback:function(e){t.$set(t.elementDatas.offset,2,e)},expression:"elementDatas.offset[2]"}})],1)],1)],1),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-200"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.color.label")))]),a("el-color-picker",{staticStyle:{"margin-right":"12px"},attrs:{"popper-class":"colorPicker","show-alpha":!1,"color-format":"rgb",size:"mini"},on:{change:function(e){return t.inputChangeRingPostData(e,"color")}},model:{value:t.params.color,callback:function(e){t.$set(t.params,"color",e)},expression:"params.color"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.speed.label")))]),a("div",{staticClass:"items-center w100"},[a("el-slider",{staticStyle:{width:"100%"},attrs:{min:1,max:10,step:1},on:{change:function(e){return t.inputChangeRingPostData(e,"speed")}},model:{value:t.params.speed,callback:function(e){t.$set(t.params,"speed",e)},expression:"params.speed"}}),a("span",{staticClass:"slider-num"},[t._v(t._s(t.params.speed))])],1)])]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-radius"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.radius.label")))]),a("el-input",{attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("formRelational.radius.placeholder")},on:{input:function(e){return t.inputChangeRingPostData(e,"radius")}},scopedSlots:t._u([{key:"append",fn:function(){return[a("span",[t._v("m")])]},proxy:!0}]),model:{value:t.params.radius,callback:function(e){t.$set(t.params,"radius",e)},expression:"params.radius"}})],1),a("div",{staticClass:"items-center"},[t._v("  ")])]),a("div",{staticClass:"after-lineX"})])},Tt=[],Ot={name:"RingSetting",props:["currentElement","elementTransformObj"],mixins:[g["a"]],data:function(){return{itemGutter:10,elementDatas:{id:"",longitude:0,latitude:0,altitude:0,rotation:[0,0,0],offset:[0,0,0],list:[]},params:{color:"rgb(255, 50, 0)",radius:5,speed:5}}},created:function(){this.init()},methods:{init:function(){var t=window.scene.features.get(this.currentElement.id);this.elementDatas.id=this.currentElement.id,this.elementDatas.longitude=t.origin[0],this.elementDatas.latitude=t.origin[1],this.elementDatas.altitude=t.altitude,this.elementDatas.offset=t.offset,this.params.color=t.data.color,this.params.radius=t.data.radius,this.params.speed=t.data.speed},togglePanel:function(){this.elementDatas.list[1].panelState=!this.elementDatas.list[1].panelState},inputChangeRingPostData:function(t,e){var a=window.scene.features.get(this.currentElement.id),s={};"color"!=e&&(t=parseFloat(t+"")),s[e]=t,window.scene.postData(s,a.dataKey)}}},jt=Ot,zt=(a("cbe6"),Object(d["a"])(jt,Rt,Tt,!1,null,"3220e518",null)),Ft=zt.exports,It=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex relative"},[a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{staticClass:"space-between-center",attrs:{span:12}},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.basePoint.label")))]),a("el-tooltip",{attrs:{effect:"dark",content:t.$t("formRelational.basePoint.placeholder"),placement:"top"}},[a("span",{staticClass:"outline-none cursor-btn",on:{click:function(e){return t.clickCheckedCoordinate()}}},[a("CommonSVG",{attrs:{color:"var(--theme)",size:"18","icon-class":"select_coord"}})],1)])],1),a("el-col",{attrs:{span:12}},[a("el-input",{class:{"is-error":t.inputError&&!t.styleFormDatas.validate.longitude},attrs:{type:"number",title:"",size:"mini",placeholder:t.$t("formRelational.longitude.placeholder")},on:{input:function(e){return t.inputChangeOrigin(e,"longitude")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v(t._s(t.$t("formRelational.longitude.label")))])]},proxy:!0}]),model:{value:t.elementDatas.longitude,callback:function(e){t.$set(t.elementDatas,"longitude",e)},expression:"elementDatas.longitude"}})],1)],1),a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{attrs:{span:12}},[a("el-input",{class:{"is-error":t.inputError&&!t.styleFormDatas.validate.latitude},attrs:{type:"number",title:"",size:"mini",placeholder:t.$t("formRelational.latitude.placeholder")},on:{input:function(e){return t.inputChangeOrigin(e,"latitude")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v(t._s(t.$t("formRelational.latitude.label")))]},proxy:!0}]),model:{value:t.elementDatas.latitude,callback:function(e){t.$set(t.elementDatas,"latitude",e)},expression:"elementDatas.latitude"}})],1),a("el-col",{attrs:{span:12}},[a("el-input",{class:{"is-error":t.inputError&&!t.styleFormDatas.validate.altitude},attrs:{type:"number",title:"",size:"mini",placeholder:t.$t("formRelational.altitude.placeholder")},on:{input:function(e){return t.inputChangeOrigin(e,"altitude")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v(t._s(t.$t("formRelational.altitude.label")))])]},proxy:!0}]),model:{value:t.elementDatas.altitude,callback:function(e){t.$set(t.elementDatas,"altitude",e)},expression:"elementDatas.altitude"}})],1)],1)],1),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-offset"},[a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{staticClass:"space-between-center",attrs:{span:12}},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.translate.label")))]),a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:t.$t("formRelational.translate.tooltip"),placement:"top"}},[a("span",{staticClass:"outline-none cursor-btn",on:{click:function(e){return t.handleModelTransform("translate")}}},[a("CommonSVG",{attrs:{color:"var(--theme)",size:"17","icon-class":"crosshair_move_feature"}})],1)])],1),a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.offset[0],expression:"elementDatas.offset[0]"}],attrs:{type:"number",title:"",size:"mini",placeholder:"X"},on:{input:function(e){return t.inputChangeOffset(e,"x")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v("X")])]},proxy:!0}]),model:{value:t.elementDatas.offset[0],callback:function(e){t.$set(t.elementDatas.offset,0,e)},expression:"elementDatas.offset[0]"}})],1)],1),a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.offset[1],expression:"elementDatas.offset[1]"}],attrs:{type:"number",title:"",size:"mini",placeholder:"Y"},on:{input:function(e){return t.inputChangeOffset(e,"y")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v("Y")]},proxy:!0}]),model:{value:t.elementDatas.offset[1],callback:function(e){t.$set(t.elementDatas.offset,1,e)},expression:"elementDatas.offset[1]"}})],1),a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.offset[2],expression:"elementDatas.offset[2]"}],attrs:{type:"number",title:"",size:"mini",placeholder:"Z"},on:{input:function(e){return t.inputChangeOffset(e,"z")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v("Z")]},proxy:!0}]),model:{value:t.elementDatas.offset[2],callback:function(e){t.$set(t.elementDatas.offset,2,e)},expression:"elementDatas.offset[2]"}})],1)],1)],1),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100",staticStyle:{width:"200px"}},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.width.label")))]),a("el-input",{attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("formRelational.width.placeholder")},on:{input:function(e){return t.inputChangePostData(e,"width")}},scopedSlots:t._u([{key:"append",fn:function(){return[a("span",[t._v("m")])]},proxy:!0}]),model:{value:t.params.width,callback:function(e){t.$set(t.params,"width",e)},expression:"params.width"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.top.label")))]),a("el-input",{attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("formRelational.top.placeholder")},on:{input:function(e){return t.inputChangePostData(e,"height")}},scopedSlots:t._u([{key:"append",fn:function(){return[a("span",[t._v("m")])]},proxy:!0}]),model:{value:t.params.height,callback:function(e){t.$set(t.params,"height",e)},expression:"params.height"}})],1)]),"flame"==t.currentElement.type?[a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100",staticStyle:{width:"200px"}},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.speed.label")))]),a("div",{staticClass:"items-center w100"},[a("el-slider",{staticStyle:{width:"100%"},attrs:{min:1,max:10,step:1},on:{change:function(e){return t.inputChangePostData(e,"speed")}},model:{value:t.params.speed,callback:function(e){t.$set(t.params,"speed",e)},expression:"params.speed"}}),a("span",{staticClass:"slider-num"},[t._v(t._s(t.params.speed))])],1)]),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.gain.label")))]),a("div",{staticClass:"items-center w100 relative"},[a("span",{staticClass:"tip-class left"},[t._v("强")]),a("el-slider",{staticStyle:{width:"100%"},attrs:{min:.1,max:5,step:.1,"show-tooltip":!1},on:{change:function(e){return t.inputChangePostData(e,"gain")}},model:{value:t.params.gain,callback:function(e){t.$set(t.params,"gain",e)},expression:"params.gain"}}),a("span",{staticClass:"tip-class right"},[t._v("弱")]),a("span",{staticClass:"slider-num"})],1)])]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100",staticStyle:{width:"200px"}},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.lacunarity.label")))]),a("div",{staticClass:"items-center w100"},[a("el-slider",{staticStyle:{width:"100%"},attrs:{min:1,max:10,step:1},on:{change:function(e){return t.inputChangePostData(e,"lacunarity")}},model:{value:t.params.lacunarity,callback:function(e){t.$set(t.params,"lacunarity",e)},expression:"params.lacunarity"}}),a("span",{staticClass:"slider-num"},[t._v(t._s(t.params.lacunarity))])],1)]),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.magnitude.label")))]),a("div",{staticClass:"items-center w100"},[a("el-slider",{staticStyle:{width:"100%"},attrs:{min:1,max:10,step:.1},on:{change:function(e){return t.inputChangePostData(e,"magnitude")}},model:{value:t.params.magnitude,callback:function(e){t.$set(t.params,"magnitude",e)},expression:"params.magnitude"}}),a("span",{staticClass:"slider-num"},[t._v(t._s(t.params.magnitude))])],1)])])]:t._e(),"smoke"==t.currentElement.type?[a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-160"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.color.label")))]),a("el-color-picker",{staticStyle:{"margin-right":"12px"},attrs:{"popper-class":"colorPicker","show-alpha":!1,"color-format":"rgb",size:"mini"},on:{change:function(e){return t.inputChangePostData(e,"color")}},model:{value:t.params.color,callback:function(e){t.$set(t.params,"color",e)},expression:"params.color"}})],1)])]:t._e()],2)},At=[],Nt={name:"SmokeFlameSetting",props:["currentElement","elementTransformObj"],mixins:[g["a"]],data:function(){return{itemGutter:10,elementDatas:{id:"",longitude:0,latitude:0,altitude:0,rotation:[0,0,0],offset:[0,0,0],list:[]},params:{width:10,height:30,color:"",speed:1,gain:.1,lacunarity:1,magnitude:1}}},created:function(){this.init()},methods:{init:function(){var t=window.scene.features.get(this.currentElement.id);this.elementDatas.id=this.currentElement.id,this.elementDatas.longitude=t.origin[0],this.elementDatas.latitude=t.origin[1],this.elementDatas.altitude=t.altitude,this.elementDatas.offset=t.offset,this.params.width=t.data.width,this.params.height=t.data.height,"flame"==this.currentElement.type?(this.params.speed=t.data.speed,this.params.gain=t.data.gain,this.params.lacunarity=t.data.lacunarity,this.params.magnitude=t.data.magnitude):(this.params.color=t.data.color,"gray"==t.data.color&&(this.params.color="rgb(0, 0, 0)"))},togglePanel:function(){this.elementDatas.list[1].panelState=!this.elementDatas.list[1].panelState},inputChangePostData:function(t,e){var a=window.scene.features.get(this.currentElement.id),s={};s[e]="color"==e?t:parseFloat(t+""),window.scene.postData(s,a.dataKey)}}},Mt=Nt,Gt=(a("818f"),Object(d["a"])(Mt,It,At,!1,null,"7eaae180",null)),Xt=Gt.exports,Vt=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex relative w100"},[a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-200"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.elementDatas.list[1].title))]),a("div",{staticClass:"popup-item-center items-center",on:{click:t.togglePanel}},[a("CommonSVG",{attrs:{"icon-class":"xyz",size:16}}),a("span",{staticClass:"margin-left-5"},[t._v(t._s(t.elementDatas.list[1].title))])],1)]),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.lineWidth.label")))]),a("el-input",{attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("formRelational.lineWidth.placeholder")},on:{input:function(e){return t.inputChangePolylinePostData(e,"width")}},scopedSlots:t._u([{key:"append",fn:function(){return[a("span",[t._v("m")])]},proxy:!0}]),model:{value:t.params.width,callback:function(e){t.$set(t.params,"width",e)},expression:"params.width"}})],1)]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-200"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.color.label")))]),a("el-color-picker",{staticStyle:{"margin-right":"12px"},attrs:{"popper-class":"colorPicker","show-alpha":!1,"color-format":"rgb",size:"mini"},on:{change:function(e){return t.inputChangePolylinePostData(e,"color")}},model:{value:t.params.color,callback:function(e){t.$set(t.params,"color",e)},expression:"params.color"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.opacity.label")))]),a("div",{staticClass:"items-center w100"},[a("el-slider",{staticStyle:{width:"100%"},attrs:{min:.1,max:1,step:.1},on:{change:function(e){return t.inputChangePolylinePostData(e,"opacity")}},model:{value:t.params.opacity,callback:function(e){t.$set(t.params,"opacity",e)},expression:"params.opacity"}}),a("span",{staticClass:"slider-num"},[t._v(t._s(t.params.opacity))])],1)])]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("featureSetting.style.polyline.label")))]),a("el-switch",{on:{change:function(e){return t.inputChangePolylinePostData(e,"flow")}},model:{value:t.params.flow,callback:function(e){t.$set(t.params,"flow",e)},expression:"params.flow"}})],1)]),a("div",{staticClass:"after-lineX"}),t.params.flow?[a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.speed.label")))]),a("div",{staticClass:"items-center w100"},[a("el-slider",{staticStyle:{width:"100%"},attrs:{min:1,max:10,step:1,disabled:!t.params.flow},on:{change:function(e){return t.inputChangePolylinePostData(e,"interval")}},model:{value:t.params.interval,callback:function(e){t.$set(t.params,"interval",e)},expression:"params.interval"}}),a("span",{staticClass:"slider-num"},[t._v(t._s(t.params.interval))])],1)]),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("featureSetting.style.polyline.label1")))]),a("div",[a("el-radio",{staticClass:"color-98A3B3",attrs:{label:"x"},on:{input:function(e){return t.inputChangePolylinePostData(e,"direction")}},model:{value:t.params.direction,callback:function(e){t.$set(t.params,"direction",e)},expression:"params.direction"}},[t._v(" "+t._s(t.$t("featureSetting.style.polyline.label2"))+" ")]),a("el-radio",{staticClass:"color-98A3B3",attrs:{label:"-x"},on:{input:function(e){return t.inputChangePolylinePostData(e,"direction")}},model:{value:t.params.direction,callback:function(e){t.$set(t.params,"direction",e)},expression:"params.direction"}},[t._v(" "+t._s(t.$t("featureSetting.style.polyline.label3"))+" ")])],1)])]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.elementDatas.list[0].title))]),a("el-input",{attrs:{size:"mini",placeholder:t.$t("formRelational.image.placeholder")},on:{change:function(e){return t.inputChangePolylinePostData(e,"image")}},model:{value:t.elementDatas.list[0].value,callback:function(e){t.$set(t.elementDatas.list[0],"value",e)},expression:"elementDatas.list[0].value"}}),a("el-tooltip",{attrs:{enterable:!1,effect:"dark",content:t.$t("formRelational.image.label"),placement:"top"}},[a("span",{staticClass:"thumb-box",on:{click:t.openThumb}},[a("CommonSVG",{attrs:{size:16,"icon-class":"thumbnail"}})],1)])],1),t.elementDatas.list[0].thumbState?a("div",{staticClass:"thumb-dialog"},[a("div",{staticClass:"title"},[a("span",[t._v(t._s(t.$t("formRelational.image.label")))]),a("div",[a("i",{staticClass:"el-icon-close cursor-btn",on:{click:t.openThumb}})])]),a("div",{staticClass:"thumb-content"},t._l(t.elementDatas.list[0].thumbList,(function(e){return a("div",{key:e.url,staticClass:"item",class:{active:e.img===t.elementDatas.list[0].value},on:{click:function(a){return t.checkPolylineImg(e)}}},[a("div",{staticClass:"img-box"},[a("img",{attrs:{src:e.img}})])])})),0)]):t._e()]),a("div",{staticClass:"after-lineX"})]:t._e(),t.elementDatas.list[1].panelState?a("div",{staticClass:"regional-coordinates-container reginoal-dialog"},[a("div",{staticClass:"title"},[a("span",[t._v(t._s(t.elementDatas.list[1].title))]),a("div",[a("span",{staticClass:"downloadExcel",on:{click:t.downloadExcel}},[t._v(t._s(t.$t("others.export")))]),a("span",{staticClass:"downloadExcel",on:{click:t.importData}},[t._v(t._s(t.$t("others.import")))]),a("i",{staticClass:"el-icon-plus cursor-btn margin-right-8",on:{click:function(e){return t.addPoint()}}}),a("i",{staticClass:"el-icon-close cursor-btn",on:{click:t.togglePanel}})])]),a("div",{staticClass:"position-list-wrap"},t._l(t.elementDatas.list[1].position,(function(e,s){return a("div",{key:s,staticClass:"position-list"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:e[0],expression:"pos[0]"}],attrs:{type:"number",title:"",size:"mini",placeholder:"X"},on:{input:function(e){return t.inputChangePolylinePostData(e,"positions")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v("X")])]},proxy:!0}],null,!0),model:{value:e[0],callback:function(a){t.$set(e,0,a)},expression:"pos[0]"}})],1),a("el-col",{attrs:{span:8}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:e[1],expression:"pos[1]"}],attrs:{type:"number",title:"",size:"mini",placeholder:"Y"},on:{input:function(e){return t.inputChangePolylinePostData(e,"positions")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v("Y")]},proxy:!0}],null,!0),model:{value:e[1],callback:function(a){t.$set(e,1,a)},expression:"pos[1]"}})],1),a("el-col",{attrs:{span:8}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:e[2],expression:"pos[2]"}],attrs:{type:"number",title:"",size:"mini",placeholder:"Z"},on:{input:function(e){return t.inputChangePolylinePostData(e,"positions")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v("Z")]},proxy:!0}],null,!0),model:{value:e[2],callback:function(a){t.$set(e,2,a)},expression:"pos[2]"}})],1)],1),a("div",{staticClass:"delete-btn"},[a("span",{staticClass:"coord-box",on:{click:function(a){return t.clickCheckedOffsetCoordinate(s,e)}}},[a("CommonSVG",{attrs:{color:t.curIndex===s?"var(--theme)":"#FFFFFF",size:"16","icon-class":"amend_feature"}})],1),t.elementDatas.list[1].position.length>2?a("i",{staticClass:"el-icon el-icon-remove-outline icons",on:{click:function(e){return t.delPoints(s)}}}):t._e()])],1)})),0)]):t._e(),t.showImportDialog?a("CoordinateImport",{on:{close:function(e){t.showImportDialog=!1},confirm:t.getPosition}}):t._e()],2)},Lt=[],Bt=a("9a9c"),Ht={name:"PolylineSetting",props:["currentElement"],components:{CommonSVG:function(){return a.e("chunk-51f90655").then(a.bind(null,"17d0"))},CoordinateImport:Bt["a"]},data:function(){return{itemGutter:10,showImportDialog:!1,elementDatas:{id:"",longitude:0,latitude:0,altitude:0,rotation:[0,0,0],offset:[0,0,0],list:[{title:this.$t("formRelational.image.label1"),value:"",optionState:!0,validateImg:!0,validateState:null,thumbState:!1,thumbList:[]},{title:this.$t("dialog.coordinate.name"),position:[[0,0,0],[0,0,0]],panelState:!1,addOption:!0,optionItem:{name:"position",value:[0,0,0]}}]},curIndex:"",params:{color:"rgb(255, 50, 0)",opacity:1,width:2,flow:!0,interval:1,image:"",direction:"x"},timeout:null,timeout1:null,timer:null,inputTimeout:null}},created:function(){this.init(),this.getPublicImg()},computed:{transFormStatus:function(){return this.$store.state.pathAnimation.transFormStatus}},methods:{init:function(){var t=window.scene.features.get(this.currentElement.id);this.elementDatas.id=this.currentElement.id,this.elementDatas.longitude=t.origin[0],this.elementDatas.latitude=t.origin[1],this.elementDatas.altitude=t.altitude,this.elementDatas.offset=t.offset,t.data.image&&""!==t.data.image&&(this.elementDatas.list[0].value=t.data.image),this.elementDatas.list[1].position=t.data.positions,this.params.color=t.data.color,this.params.opacity=t.data.opacity,this.params.width=t.data.width,this.params.flow=t.data.flow,this.params.direction=t.data.direction,this.params.interval=10-(t.data.interval-1)},getPublicImg:function(){var t=this,e=a("a18a");e.keys().length>0&&e.keys().forEach((function(e,a){var s=e.replace(/(.*\/)*([\s\S]*?)/gi,"$2"),i=(s.split(".")[0],"./image/polyline/".concat(s));t.elementDatas.list[0].thumbList.push({name:s,img:i})}))},checkPolylineImg:function(t){this.elementDatas.list[0].value=t.img,this.inputChangePolylinePostData(t.img,"image")},togglePanel:function(){var t=this,e=window.scene.mv.tools.draw.getAllData();if(e.polyline.length){for(var a=e.polyline[0].points.length,s=[],i=0;i<a;i++)s[i]=[e.polyline[0].points[i].x,e.polyline[0].points[i].y,e.polyline[0].points[i].z];this.elementDatas.list[1].position=s}this.elementDatas.list[1].panelState=!this.elementDatas.list[1].panelState;var n=window.scene.features.get(this.currentElement.id);if(this.elementDatas.list[1].panelState)n.visible=!1,window.scene.mv.tools.draw.active(),window.scene.mv.tools.draw.startDrawLine(this.elementDatas.list[1].position),window.scene.mv.tools.draw.editPoint(),this.timeout=setTimeout((function(){window.scene.render(),window.scene.mv.events.keyPointChange.on("change",t.keyPointChangeHandle),window.scene.mv.events.transformChange.on("default",t.transformChangeHandle)}),500);else{n.visible=!0,window.scene.mv.tools.transform.deactive(),window.scene.mv.tools.draw.deactive();var l=this.$store.state.dialog.activeDialog;-1!==l.indexOf("pathAnimation")&&this.$store.commit("toggleActiveDialog","pathAnimation"),window.scene.mv.events.keyPointChange.off("change",this.keyPointChangeHandle),window.scene.mv.events.transformChange.off("default",this.transformChangeHandle),window.scene.render()}if(this.transFormStatus){window.scene.mv.tools.transform.deactive();var o=this.$store.state.dialog.activeDialog;-1!==o.indexOf("pathAnimation")&&this.$store.commit("toggleActiveDialog","pathAnimation")}else this.$store.commit("toogleTransFormStatus",!0);""!==this.curIndex&&this.offEvent()},delPoints:function(t){if(""!==this.curIndex&&this.offEvent(),this.transFormStatus){window.scene.mv.tools.transform.deactive();var e=this.$store.state.dialog.activeDialog;-1!==e.indexOf("pathAnimation")&&this.$store.commit("toggleActiveDialog","pathAnimation")}else this.$store.commit("toogleTransFormStatus",!0);this.elementDatas.list[1].position.splice(t,1),this.inputChangePolylinePostData("","positions")},addPoint:function(){""!==this.curIndex&&this.offEvent(),this.elementDatas.list[1].position.push([0,0,0]),this.inputChangePolylinePostData("","positions")},openThumb:function(){this.elementDatas.list[0].thumbState=!this.elementDatas.list[0].thumbState},clickCheckedOffsetCoordinate:function(t,e){var a=this;this.curIndex!==t?(""!==this.curIndex&&this.offEvent(),this.$nextTick((function(){""===a.curIndex&&(a.curIndex=t,a.transFormStatus?window.scene.mv.tools.transform.deactive():a.$store.commit("toogleTransFormStatus",!0),window.scene.mv.status.selectable=!1,window.scene.mv.tools.draw.currentPointIndex=t,window.scene.mv.tools.transform.active(e,1),document.addEventListener("keyup",a.onEscKeyUp),a.timer=setTimeout((function(){window.scene.render()}),500))}))):this.offEvent()},onEscKeyUp:function(t){27==t.keyCode&&"Escape"===t.key&&this.offEvent()},offEvent:function(){this.curIndex="",window.scene.mv.status.selectable=!0,window.scene.mv.tools.transform.deactive()},onCheckedOffsetCoordinate:function(t){var e=window.scene.mv._THREE,a=window.scene.queryPosition(new e.Vector2(t.clientX,t.clientY));if(""!=a&&void 0!=a){var s=this.curIndex;this.$set(this.elementDatas.list[1].position[s],0,a.x),this.$set(this.elementDatas.list[1].position[s],1,a.y),this.$set(this.elementDatas.list[1].position[s],2,a.z),this.curIndex="",this.inputChangePolylinePostData("","positions"),window.scene.mv.tools.transform.deactive()}else this.$message.error(this.$t("messageTips.errorCheckCoordinate"));window.scene.mv.status.selectable=!0},inputChangePolylinePostData:function(t,e){var a=this;null!=this.inputTimeout&&clearTimeout(this.inputTimeout),this.inputTimeout=setTimeout((function(){var s=window.scene.features.get(a.currentElement.id),i={},n=[];switch(e){case"interval":case"opacity":case"width":t=parseFloat(t+""),"interval"==e&&(t=10-(t-1));break;case"positions":n=[],a.elementDatas.list[1].position.forEach((function(t,e){n[e]=t.map((function(t){var e=0;return""!=t&&(e=parseFloat(t+"")),e}))})),a.elementDatas.list[1].position=n,a.elementDatas.list[1].panelState&&(window.scene.mv.tools.draw.startDrawLine(a.elementDatas.list[1].position),window.scene.mv.tools.draw.editPoint());break;case"flow":t?(a.elementDatas.list[0].value=location.protocol+"//resources.vothing.com/img/flow.png",i.image=a.elementDatas.list[0].value,i.interval=parseFloat(10-(a.params.interval-1)+"")):(i.image="",a.elementDatas.list[0].value="");break}"positions"==e?i.positions=n:i[e]=t,window.scene.postData(i,s.dataKey),window.scene.render(),a.inputTimeout=null}),1e3)},downloadExcel:function(){""!==this.curIndex&&this.offEvent();var t=document.createElement("textarea");t.setAttribute("readonly","");var e=[],a=this.elementDatas.list[1].position;e=a.map((function(t){return t=t.join(","),t})),t.value=e.join(";"),t.style.position="absolute",t.style.left="-9999px",document.body.appendChild(t);var s=document.getSelection().rangeCount>0&&document.getSelection().getRangeAt(0);t.select(),document.execCommand("copy"),document.body.removeChild(t),this.$message.success(this.$t("messageTips.copySuccess1")),s&&(document.getSelection().removeAllRanges(),document.getSelection().addRange(s))},importData:function(){""!==this.curIndex&&this.offEvent(),this.showImportDialog=!0},getPosition:function(t){this.elementDatas.list[1].position=t,this.inputChangePolylinePostData("","positions"),this.showImportDialog=!1},keyPointChangeHandle:function(t){this.$store.commit("toogleEditPathAnimation",!0),this.transFormStatus?window.scene.mv.tools.transform.deactive():this.$store.commit("toogleTransFormStatus",!0),this.curIndex=window.scene.mv.tools.draw.currentPointIndex,window.scene.mv.tools.transform.active([t.x,t.y,t.z],1),this.$store.commit("setPathAnimationPosition",{x:t.x,y:t.y,z:t.z}),window.scene.mv.tools.draw.currentPointMesh.visible=!0},transformChangeHandle:function(t){if(t){var e=window.scene.mv.tools.draw.currentPointIndex;this.$set(this.elementDatas.list[1].position,e,[t.x,t.y,t.z]),window.scene.mv.tools.draw.updateEditPointPosition([t.x,t.y,t.z]),this.$store.commit("setPathAnimationPosition",{x:t.x,y:t.y,z:t.z}),this.inputChangePolylinePostData("","positions")}}},beforeDestroy:function(){var t=window.scene.features.get(this.currentElement.id),e=this.$store.state.pathAnimation.editPathAnimation;e&&this.$store.commit("toogleEditPathAnimation",!1),window.scene.mv.tools.draw.deactive(),window.scene.mv.tools.transform.deactive();var a=this.$store.state.dialog.activeDialog;-1!==a.indexOf("pathAnimation")&&this.$store.commit("toggleActiveDialog","pathAnimation"),this.$store.commit("toogleTransFormStatus",!1),t&&(t.visible=!0);var s=this;this.timeout1=setTimeout((function(){window.scene.render(),clearTimeout(s.timeout),clearTimeout(s.timeout1),clearTimeout(s.timer)}),500),window.scene.mv.events.keyPointChange.off("change",this.keyPointChangeHandle),window.scene.mv.events.transformChange.off("default",this.transformChangeHandle),""!==this.curIndex&&this.offEvent()}},Kt=Ht,Yt=(a("9978"),Object(d["a"])(Kt,Vt,Lt,!1,null,"e752eeae",null)),Wt=Yt.exports,Zt=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex"},[a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.elementDatas.list[0].title)+" "),a("el-tooltip",{staticClass:"box-item",attrs:{enterable:!1,effect:"dark",placement:"top"}},[a("div",{attrs:{slot:"content"},domProps:{innerHTML:t._s(t.$t("featureSetting.style.vectorExtrude.tooltip1"))},slot:"content"}),a("i",{staticClass:"el-icon-question"})])],1),a("el-input",{attrs:{size:"mini",readonly:"",placeholder:t.$t("formRelational.address.placeholder")},model:{value:t.elementDatas.list[0].value,callback:function(e){t.$set(t.elementDatas.list[0],"value",e)},expression:"elementDatas.list[0].value"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.opacity.label")))]),a("div",{staticClass:"items-center w100"},[a("el-slider",{staticStyle:{width:"100%"},attrs:{min:.1,max:1,step:.1},on:{change:t.inputVectorExtrudePostData},model:{value:t.params.opacity,callback:function(e){t.$set(t.params,"opacity",e)},expression:"params.opacity"}}),a("span",{staticClass:"slider-num"},[t._v(t._s(t.params.opacity))])],1)])]),a("div",{staticClass:"after-lineX"})])},Jt=[],qt={name:"VectorExtrudeSetting",props:["currentElement"],data:function(){return{itemGutter:10,elementDatas:{id:"",longitude:0,latitude:0,altitude:0,rotation:[0,0,0],offset:[0,0,0],list:[{title:this.$t("formRelational.address.label"),value:"",optionState:!0,validateImg:!0,validateState:null}]},params:{opacity:1}}},created:function(){this.init()},methods:{init:function(){var t=window.scene.features.get(this.currentElement.id);this.elementDatas.id=this.currentElement.id,this.elementDatas.longitude=t.origin[0],this.elementDatas.latitude=t.origin[1],this.elementDatas.altitude=t.altitude,this.elementDatas.offset=t.offset,this.elementDatas.list[0].value=t.url;var e=JSON.parse(t.data.originalData);this.params.opacity=e.opacity},inputVectorExtrudePostData:function(){var t=window.scene.features.get(this.currentElement.id),e={opacity:parseFloat(this.params.opacity+"")};window.scene.postData(e,t.dataKey),window.scene.render()}}},Ut=qt,Qt=(a("cfb7"),Object(d["a"])(Ut,Zt,Jt,!1,null,"2c79b566",null)),te=Qt.exports,ee=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex"},[a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.featureID.label")))]),a("el-input",{attrs:{size:"mini",readonly:"",placeholder:t.$t("formRelational.featureID.placeholder")},model:{value:t.elementDatas.id,callback:function(e){t.$set(t.elementDatas,"id",e)},expression:"elementDatas.id"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.elementDatas.list[0].title))]),a("el-input",{attrs:{size:"mini",placeholder:t.$t("formRelational.address.placeholder")},model:{value:t.elementDatas.list[0].value,callback:function(e){t.$set(t.elementDatas.list[0],"value",e)},expression:"elementDatas.list[0].value"}})],1)])])},ae=[],se={name:"WmtsWmsTmsSetting",props:["currentElement"],data:function(){return{itemGutter:10,elementDatas:{id:"",longitude:0,latitude:0,altitude:0,rotation:[0,0,0],offset:[0,0,0],list:[{title:this.$t("formRelational.address.label"),value:"",optionState:!0,validateImg:!0,validateState:null}]}}},created:function(){this.init()},methods:{init:function(){var t=window.scene.features.get(this.currentElement.id);this.elementDatas.id=this.currentElement.id,this.elementDatas.list[0].value=t.url}}},ie=se,ne=(a("1689"),Object(d["a"])(ie,ee,ae,!1,null,"19bce133",null)),le=ne.exports,oe=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex w100"},[a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.elementDatas.list[0].title))]),a("el-input",{attrs:{size:"mini",readonly:"",placeholder:t.$t("formRelational.address.placeholder")},model:{value:t.elementDatas.list[0].value,callback:function(e){t.$set(t.elementDatas.list[0],"value",e)},expression:"elementDatas.list[0].value"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.opacity.label")))]),a("div",{staticClass:"items-center w100"},[a("el-slider",{staticStyle:{width:"100%"},attrs:{min:.1,max:1,step:.1},on:{change:function(e){return t.inputChangeSHPPostData("opacity")}},model:{value:t.params.opacity,callback:function(e){t.$set(t.params,"opacity",e)},expression:"params.opacity"}}),a("span",{staticClass:"slider-num"},[t._v(t._s(t.params.opacity))])],1)])]),a("div",{staticClass:"after-lineX"})])},re=[],ce={name:"ShpSetting",props:["currentElement"],data:function(){return{itemGutter:10,elementDatas:{id:"",longitude:0,latitude:0,altitude:0,rotation:[0,0,0],offset:[0,0,0],list:[{title:this.$t("formRelational.address.label"),value:"",optionState:!0,validateImg:!0,validateState:null}]},params:{opacity:1,height:1}}},created:function(){this.init()},methods:{init:function(){var t=window.scene.features.get(this.currentElement.id);this.elementDatas.id=this.currentElement.id,this.elementDatas.list[0].value=t.url,this.params.opacity=t.data.opacity,this.params.height=parseFloat(t.data.height+"")},inputChangeSHPPostData:function(t){var e=window.scene.features.get(this.currentElement.id),a={};a[t]=this.params[t],window.scene.postData(a,e.dataKey)}}},ue=ce,me=(a("43fd"),Object(d["a"])(ue,oe,re,!1,null,"67d4b967",null)),de=me.exports,pe=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex"},[a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.elementDatas.list[0].title))]),a("el-input",{attrs:{size:"mini",placeholder:t.$t("formRelational.address.placeholder")},model:{value:t.elementDatas.list[0].value,callback:function(e){t.$set(t.elementDatas.list[0],"value",e)},expression:"elementDatas.list[0].value"}})],1)])])},he=[],fe={name:"KmlSetting",props:["currentElement"],data:function(){return{itemGutter:10,elementDatas:{id:"",longitude:0,latitude:0,altitude:0,rotation:[0,0,0],offset:[0,0,0],list:[{title:this.$t("formRelational.address.label"),value:"",optionState:!0,validateImg:!0,validateState:null}]}}},created:function(){this.init()},methods:{init:function(){var t=window.scene.features.get(this.currentElement.id);this.elementDatas.id=this.currentElement.id,this.elementDatas.list[0].value=t.url}}},ve=fe,ge=(a("1cf4"),Object(d["a"])(ve,pe,he,!1,null,"2fdb2530",null)),ye=ge.exports,Ce=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex"},[a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.elementDatas.list[0].title))]),a("el-input",{attrs:{size:"mini",placeholder:t.$t("formRelational.address.placeholder")},model:{value:t.elementDatas.list[0].value,callback:function(e){t.$set(t.elementDatas.list[0],"value",e)},expression:"elementDatas.list[0].value"}})],1)]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[void 0!=t.params.showFill?a("el-checkbox",{on:{change:function(e){return t.inputGeoJSONPostData("showFill")}},model:{value:t.params.showFill,callback:function(e){t.$set(t.params,"showFill",e)},expression:"params.showFill"}},[t._v(" "+t._s(t.$t("featureSetting.style.geoJSON.label"))+" ")]):t._e(),void 0!=t.params.showLine?a("el-checkbox",{on:{change:function(e){return t.inputGeoJSONPostData("showLine")}},model:{value:t.params.showLine,callback:function(e){t.$set(t.params,"showLine",e)},expression:"params.showLine"}},[t._v(" "+t._s(t.$t("featureSetting.style.geoJSON.label1"))+" ")]):t._e(),void 0!=t.params.showText?a("el-checkbox",{on:{change:function(e){return t.inputGeoJSONPostData("showText")}},model:{value:t.params.showText,callback:function(e){t.$set(t.params,"showText",e)},expression:"params.showText"}},[t._v(" "+t._s(t.$t("featureSetting.style.geoJSON.label2"))+" ")]):t._e()],1),a("div",{staticClass:"items-center"},[void 0!=t.params.showPoint?a("el-checkbox",{on:{change:function(e){return t.inputGeoJSONPostData("showPoint")}},model:{value:t.params.showPoint,callback:function(e){t.$set(t.params,"showPoint",e)},expression:"params.showPoint"}},[t._v(" "+t._s(t.$t("featureSetting.style.geoJSON.label3"))+" ")]):t._e(),void 0!=t.params.showOutline?a("el-checkbox",{on:{change:function(e){return t.inputGeoJSONPostData("showOutline")}},model:{value:t.params.showOutline,callback:function(e){t.$set(t.params,"showOutline",e)},expression:"params.showOutline"}},[t._v(" "+t._s(t.$t("formRelational.showLine.label"))+" ")]):t._e()],1)]),a("div",{staticClass:"after-lineX"})])},De=[],be={name:"GeoJSONSetting",props:["currentElement"],data:function(){return{itemGutter:10,elementDatas:{id:"",longitude:0,latitude:0,altitude:0,rotation:[0,0,0],offset:[0,0,0],list:[{title:this.$t("formRelational.address.label"),value:"",optionState:!0,validateImg:!0,validateState:null}]},params:{}}},created:function(){this.init()},methods:{init:function(){var t=window.scene.features.get(this.currentElement.id);this.elementDatas.id=this.currentElement.id,this.elementDatas.list[0].value=t.url;var e={showFill:null,showLine:null,showText:null,showPoint:null,showOutline:null},a=JSON.parse(t.data.originalData);e.showFill=a.showFill,e.showLine=a.showLine,e.showText=a.showText,e.showPoint=a.showPoint,e.showOutline=a.showOutline,this.params=e},inputGeoJSONPostData:function(t){var e={};e[t]=this.params[t];var a=window.scene.features.get(this.currentElement.id);window.scene.postData(e,a.dataKey)}}},we=be,_e=(a("d825"),Object(d["a"])(we,Ce,De,!1,null,"7cffa82d",null)),xe=_e.exports,ke=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex relative"},[a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-160"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title",staticStyle:{"min-width":"80px"}},[t._v(" "+t._s(t.$t("formRelational.altitude.label"))+" ")]),a("el-input",{attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("formRelational.altitude.placeholder")},on:{input:function(e){return t.changeData("altitude")}},model:{value:t.elementDatas.altitude,callback:function(e){t.$set(t.elementDatas,"altitude",e)},expression:"elementDatas.altitude"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title",staticStyle:{"min-width":"80px"}},[t._v(" "+t._s(t.$t("featureSetting.style.heatMap.label6"))+" "),a("el-tooltip",{staticClass:"box-item",attrs:{enterable:!1,effect:"dark",placement:"top"}},[a("div",{attrs:{slot:"content"},domProps:{innerHTML:t._s(t.$t("featureSetting.style.heatMap.tooltip1"))},slot:"content"}),a("i",{staticClass:"outline-none el-input__icon el-icon-question"})])],1),a("el-input",{attrs:{size:"mini",type:"number",placeholder:t.$t("featureSetting.style.heatMap.label6")},on:{input:function(e){return t.changeData("gridScale")}},model:{value:t.params.gridScale,callback:function(e){t.$set(t.params,"gridScale",t._n(e))},expression:"params.gridScale"}})],1)]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-160"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.radius.label")))]),a("el-input",{attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("formRelational.radius.placeholder")},on:{input:function(e){return t.changeData("size")}},scopedSlots:t._u([{key:"append",fn:function(){return[a("span",[t._v("m")])]},proxy:!0}]),model:{value:t.params.size,callback:function(e){t.$set(t.params,"size",e)},expression:"params.size"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("featureSetting.style.heatMap.label3"))+" "),a("el-tooltip",{staticClass:"box-item",attrs:{enterable:!1,effect:"dark",placement:"top"}},[a("div",{attrs:{slot:"content"},domProps:{innerHTML:t._s(t.$t("featureSetting.style.heatMap.tooltip2"))},slot:"content"}),a("i",{staticClass:"outline-none el-input__icon el-icon-question"})])],1),a("el-input",{attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("formRelational.radius.placeholder")},on:{input:function(e){return t.changeData("scaleZ")}},scopedSlots:t._u([{key:"append",fn:function(){return[a("span",[t._v("m")])]},proxy:!0}]),model:{value:t.params.scaleZ,callback:function(e){t.$set(t.params,"scaleZ",e)},expression:"params.scaleZ"}})],1)]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("featureSetting.style.heatMap.label")))]),a("div",{staticClass:"popup-item-center items-center",on:{click:function(e){return t.togglePanel("color")}}},[a("span",{staticClass:"margin-left-5"},[t._v(t._s(t.$t("featureSetting.style.heatMap.label1")))]),a("CommonSVG",{attrs:{"icon-class":"notation_feature",size:16}})],1)]),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.opacity.label")))]),a("div",{staticClass:"items-center w100"},[a("el-slider",{staticStyle:{width:"100%"},attrs:{min:.1,max:1,step:.1},on:{change:function(e){return t.changeData("opacity")}},model:{value:t.params.opacity,callback:function(e){t.$set(t.params,"opacity",e)},expression:"params.opacity"}}),a("span",{staticClass:"slider-num"},[t._v(t._s(t.params.opacity))])],1)]),t.colorPanelState?a("div",{staticClass:"regional-coordinates-container"},[a("div",{staticClass:"title"},[a("span",[t._v(" "+t._s(t.$t("featureSetting.style.heatMap.label1"))+" "),a("el-tooltip",{staticClass:"box-item",attrs:{enterable:!1,effect:"dark",placement:"top"}},[a("div",{attrs:{slot:"content"},slot:"content"},[t._v(" "+t._s(t.$t("featureSetting.style.heatMap.tooltip"))+" ")]),a("i",{staticClass:"outline-none el-icon-question"})])],1),a("div",[a("i",{staticClass:"el-icon-plus cursor-btn margin-right-8",on:{click:t.addGradient}}),a("i",{staticClass:"el-icon-close cursor-btn",on:{click:function(e){return t.togglePanel("color")}}})])]),t._l(t.gradientKeys,(function(e,s){return a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("div",{staticClass:"style-color-picker"},[a("div",{staticClass:"label"},[a("span",{staticClass:"gradient-title"},[t._v(t._s(t.$t("featureSetting.style.heatMap.label")))]),a("el-input",{staticClass:"margin-right-5",attrs:{type:"number",size:"mini",placeholder:"X"},on:{input:function(a){return t.changeData("gradientKey",e)}},model:{value:e.show,callback:function(a){t.$set(e,"show",a)},expression:"gk.show"}}),t._v(" % ")],1),a("el-color-picker",{attrs:{"popper-class":"colorPicker","show-alpha":!1,"color-format":"rgb",size:"mini"},on:{change:function(a){return t.changeData("gradient",e)}},model:{value:t.params.gradient[e.key],callback:function(a){t.$set(t.params.gradient,e.key,a)},expression:"params.gradient[gk.key]"}}),a("span",{staticClass:"margin-left-5"},[t._v(t._s(t.params.gradient[e.key]))])],1),t.gradientKeys.length>1?a("div",[a("el-tooltip",{staticClass:"box-item",attrs:{enterable:!1,effect:"dark",content:t.$t("menuIconName.delete"),placement:"top"}},[a("i",{staticClass:"cursor-btn el-icon el-icon-remove-outline",on:{click:function(e){return t.delGradient(s)}}})]),a("el-tooltip",{staticClass:"box-item",attrs:{enterable:!1,effect:"dark",content:t.$t("menuIconName.save"),placement:"top"}},[e.save?a("i",{staticClass:"cursor-btn el-icon el-icon-circle-check margin-left-5",on:{click:function(e){return t.saveGradient(s)}}}):t._e()])],1):t._e()])],1)}))],2):t._e()]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("featureSetting.style.heatMap.label4")))]),a("div",{staticClass:"popup-item-center items-center",on:{click:function(e){return t.togglePanel("point")}}},[a("span",{staticClass:"margin-left-5"},[t._v(t._s(t.$t("featureSetting.style.heatMap.label5")))]),a("CommonSVG",{attrs:{"icon-class":"notation_feature",size:16}})],1)]),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("featureSetting.style.heatMap.label2")))]),a("el-switch",{staticStyle:{"margin-left":"12px"},on:{change:function(e){return t.changeData("wireframe")}},model:{value:t.params.wireframe,callback:function(e){t.$set(t.params,"wireframe",e)},expression:"params.wireframe"}})],1),t.pointPanelState?a("div",{staticClass:"regional-coordinates-container",staticStyle:{left:"0"}},[a("div",{staticClass:"title"},[a("span",[t._v(t._s(t.$t("featureSetting.style.heatMap.label5")))]),a("div",[a("i",{staticClass:"el-icon-plus cursor-btn margin-right-8",on:{click:t.addPoint}}),a("i",{staticClass:"el-icon-close cursor-btn",on:{click:function(e){return t.togglePanel("point")}}})])]),t._l(t.params.pointsArray,(function(e,s){return a("div",{key:s,staticClass:"items-center margin-top-10"},[a("el-row",{attrs:{gutter:t.itemGutter}},[a("el-col",{attrs:{span:8}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:e.x,expression:"pos.x"}],attrs:{type:"number",title:"",size:"mini",placeholder:"X"},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v("X")])]},proxy:!0}],null,!0),model:{value:e.x,callback:function(a){t.$set(e,"x",a)},expression:"pos.x"}})],1),a("el-col",{attrs:{span:8}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:e.y,expression:"pos.y"}],attrs:{type:"number",title:"",size:"mini",placeholder:"Y"},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v("Y")]},proxy:!0}],null,!0),model:{value:e.y,callback:function(a){t.$set(e,"y",a)},expression:"pos.y"}})],1),a("el-col",{attrs:{span:8}},[a("el-input",{attrs:{type:"number",size:"mini",placeholder:t.$t("others.value")},on:{input:function(e){return t.changeData("points")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v(t._s(t.$t("others.value")))]},proxy:!0}],null,!0),model:{value:e.value,callback:function(a){t.$set(e,"value",a)},expression:"pos.value"}})],1)],1),a("div",{staticClass:"text-center items-center margin-left-10"},[a("el-tooltip",{staticClass:"box-item",attrs:{enterable:!1,effect:"dark",content:t.$t("formRelational.basePoint.placeholder"),placement:"left"}},[a("span",{staticClass:"cursor-btn",on:{click:function(e){return t.clickCheckedCoordinate(s)}}},[a("CommonSVG",{attrs:{size:"20","icon-class":"select_coord"}})],1)]),a("el-tooltip",{staticClass:"box-item",attrs:{enterable:!1,effect:"dark",content:t.$t("menuIconName.delete"),placement:"right"}},[t.params.pointsArray.length>1?a("i",{staticClass:"cursor-btn el-icon el-icon-remove-outline icons ml2",on:{click:function(e){return t.delPoints(s)}}}):t._e()])],1)],1)}))],2):t._e()]),a("div",{staticClass:"after-lineX"})])},$e=[],Se=(a("4e82"),a("35b3"),a("a9e3"),a("b64b"),a("2532"),a("ed08")),Pe={name:"HeatmapSetting",props:["currentElement"],mixins:[g["a"]],data:function(){return{itemGutter:10,elementDatas:{id:"",longitude:0,latitude:0,altitude:0,rotation:[0,0,0],offset:[0,0,0],list:[]},params:{color:"rgb(255, 255, 255)",opacity:1,scaleZ:5,wireframe:!1,size:13,gradient:{.25:"rgb(0,0,255)",.55:"rgb(0,255,0)",.85:"rgb(255,255,0)",1:"rgb(255,0,0)"},pointsArray:[],gridScale:.5},colorPanelState:!1,pointPanelState:!0,checkedIndex:-1,iotDataSelect:{dataOptions:[],selectedData:["0"],selectedContent:{}},notShowRelation:"",iotDataContent:{},relationArr:[],dataObj:{title:"",sceneDataSign:"",heatmap:{value:"rgb(255, 50, 0)",opacity:1,scaleZ:5,wireframe:!1,radius:13,gradient:{a:"rgb(0,0,255)",b:"rgb(0,255,0)",c:"rgb(255,255,0)",d:"rgb(255,0,0)"},checkedIndex:-1}},gradientKeys:[]}},computed:{isVothing:function(){return this.$store.state.menuList.isVothing}},created:function(){this.init(),this.isVothing&&this.getiotData(),this.changeData=T()(this.changeData,800)},methods:{init:function(){var t=this;if(this.isVothing){var e,a=this.$store.state.scene.sceneList||[],s=this.currentElement.dataKey;for(var i in a)"element"===a[i].type&&a[i].elementid===s&&(e=a[i].data);e&&e.widgets&&(this.iotDataSelect.selectedData=e.widgets.entityData?[e.widgets.entityData.id]:["0"],this.iotDataSelect.selectedContent=e.widgets.entityData?e.widgets.entityData:{},this.relationArr=e.widgets.relationArr,this.$emit("setRelationData",this.relationArr))}var n=window.scene.features.get(this.currentElement.id);this.elementDatas.id=this.currentElement.id,this.elementDatas.altitude=this.currentElement.altitude,this.params.color=n.data.color,this.params.opacity=n.data.opacity,this.params.gradient=n.data.gradient,this.params.size=n.data.size,this.params.scaleZ=n.data.scaleZ,this.params.wireframe=n.data.wireframe,this.params.gridScale=n.data.gridScale,this.sortGradient(n.data.gradient),n.data.points&&n.data.points.length>0?n.data.points.forEach((function(e){t.params.pointsArray.push({value:e.value,x:e.position[0],y:e.position[1]})})):this.params.pointsArray=[]},togglePanel:function(t){"color"==t?(this.colorPanelState=!this.colorPanelState,this.gradientKeys.sort((function(t,e){return parseFloat(t.key)-parseFloat(e.key)}))):this.pointPanelState=!this.pointPanelState},addPoint:function(){this.params.pointsArray.push({value:0,x:0,y:0})},delPoints:function(t){this.params.pointsArray.splice(t,1)},onCheckedCoordinate:function(t){var e=window.scene.mv._THREE,a=window.scene.queryPosition(new e.Vector2(t.clientX,t.clientY));""!=a&&void 0!=a?(this.params.pointsArray[this.checkedIndex].x=a.x,this.params.pointsArray[this.checkedIndex].y=a.y):this.$message.error(this.$t("messageTips.errorCheckCoordinate")),this.checkedIndex=-1,this.pointPanelState=!0,this.inputChangePostData(),this.coordinateEventsOff()},clickCheckedCoordinate:function(t){this.checkedIndex=t,this.pointPanelState=!1,window.scene.mv.status.selectable=!1,window.scene.mv.events.singleSelection.on("default",this.onCheckedCoordinate)},coordinateEventsOff:function(){window.scene.mv.status.selectable=!0,window.scene.mv.events.singleSelection.off("default",this.onCheckedCoordinate)},iotDataChange:function(t){if("0"!==t){var e=Object(Se["c"])(this.iotDataSelect.dataOptions,t[t.length-1]);if(this.iotDataSelect.selectedContent=e,"heatmap"===this.currentElement.type){this.params.pointsArray=[];for(var a=0;a<e.data.entity.length;a++)this.params.pointsArray.push({value:0,x:0,y:0})}}else this.iotDataSelect.selectedContent={};this.relationArr=[],this.$store.commit("getRelationData",{isShow:!1})},getiotData:function(){var t=this.$store.state.scene.iotentityList||[];t.length>0?(this.iotDataSelect.dataOptions=Object(Se["b"])(t),this.iotDataSelect.dataOptions.length>0&&"0"!==this.iotDataSelect.dataOptions[0].id&&this.iotDataSelect.dataOptions.unshift({name:this.$t("others.nothing"),id:"0"})):this.iotDataSelect.dataOptions=[{name:this.$t("others.nothing"),id:"0"}]},getRelations:function(t,e){if(""!==this.iotDataSelect.selectedData&&"0"!==this.iotDataSelect.selectedData[0]){this.$store.commit("getRelationData",{isShow:!1});var a={};a="heatmap"===t?{type:t,value:this.params.pointsArray[e],index:e}:{type:t,value:"color"===t?this.dataObj[this.currentElement.type]["value"]:this.dataObj[this.currentElement.type][t]};var s=null;this.relationArr.length>0&&this.relationArr.forEach((function(a){"heatmap"===t?e===a.relationData.relationKey.index&&(s=a):t===a.relationData.type&&(s=a)})),this.$store.commit("getRelationData",{isShow:!0,setType:this.setType,data:{selectedData:this.iotDataSelect.selectedData,selectedContent:this.iotDataSelect.selectedContent,dragid:this.currentElement.id,type:t,relationKey:a,formerData:s}})}},mouseover:function(t,e){var a=null;this.relationArr.length>0?(this.relationArr.forEach((function(s){"heatmap"===t?e===s.relationData.relationKey.index&&(a=s):t===s.relationData.type&&(a=s)})),a&&a.chosedEntity&&a.chosedProperty?(this.iotDataContent={entity:a.chosedEntity.name,property:a.chosedProperty.name,type:t},this.notShowRelation=""):this.notShowRelation="heatmap"===t?e:t):this.notShowRelation="heatmap"===t?e:t},mouseout:function(t){this.notShowRelation=""},changeData:function(t,e){if(e&&e.save)return!1;if("gradientKey"==t){if(e.show<0||e.show>100)return this.$message.error(this.$t("featureSetting.style.heatMap.tooltip")),e.show=Math.floor(100*(parseFloat(e.key)+Number.EPSILON)),!1;var a=e.show/100,s=this.params.gradient[e.key];delete this.params.gradient[e.key],this.$set(this.params.gradient,a,s),e.key=a}this.inputChangePostData(t)},inputChangePostData:function(t){var e=this,a=window.scene.features.get(this.currentElement.id);"altitude"===t&&(a.altitude=parseFloat(this.elementDatas.altitude+""));var s=this.params.pointsArray||[],i=[];s&&s.length>0?s.map((function(t){var e={};e.position=[parseFloat(t.x+""),parseFloat(t.y+"")],e.value=parseFloat(t.value+""),i.push(e)})):i=[];var n=[];this.gradientKeys.forEach((function(t){t.save||n.push(t.key)}));var l={};n.forEach((function(t){l[t]=e.params.gradient[t]})),a.dataKey="heatmap-"+a.id,window.scene.postData({points:i,gradient:l,color:this.params.color,size:parseFloat(this.params.size+""),scaleZ:parseFloat(this.params.scaleZ+""),opacity:parseFloat(this.params.opacity+""),wireframe:this.params.wireframe,gridScale:parseFloat(this.params.gridScale+"")},a.dataKey),setTimeout((function(){window.scene.render()}),200)},sortGradient:function(t){var e=this,a=Object.keys(t);a.sort((function(t,e){return parseFloat(t)-parseFloat(e)})),a.forEach((function(t){e.gradientKeys.push({key:t,show:Object(n["j"])(100*t)})}))},addGradient:function(){var t=Object.keys(this.params.gradient),e=this.getRandomUniqueInt(t);this.gradientKeys.push({key:e,show:Math.floor(100*(e+Number.EPSILON)),save:!0}),this.params.gradient[e]="rgb(255,255,255)"},delGradient:function(t){var e=this.gradientKeys[t].save,a=this.gradientKeys[t].key;delete this.params.gradient[a],this.gradientKeys.splice(t,1),e||this.changeData("gradient")},saveGradient:function(t){var e=this.gradientKeys[t].show/100;return this.params.gradient[e]?(this.$message.error(this.$t("featureSetting.style.heatMap.message1")),!1):this.gradientKeys[t].show<0||this.gradientKeys[t].show>100?(this.$message.error(this.$t("featureSetting.style.heatMap.tooltip")),!1):(delete this.gradientKeys[t].save,void this.changeData("gradientKey",this.gradientKeys[t]))},getRandomUniqueInt:function(t){var e="";do{e=Math.floor(101*Math.random())/100+""}while(t.includes(e));return parseFloat(e)},getParams:function(){var t=this,e=[];return this.gradientKeys.forEach((function(t){t.save&&e.push(t.key)})),e.length&&e.forEach((function(e){delete t.params.gradient[e]})),this.params}},beforeDestroy:function(){this.coordinateEventsOff()}},Ee=Pe,Re=(a("ac5e"),a("036d"),Object(d["a"])(Ee,ke,$e,!1,null,"2cc901fc",null)),Te=Re.exports,Oe=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex relative"},[a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("featureSetting.style.batchExtrude.label")))]),a("el-select",{attrs:{size:"mini",placeholder:t.$t("featureSetting.style.batchExtrude.placeholder")},on:{change:function(e){return t.inputBatchExtrudePostData(e,"type")}},model:{value:t.elementDatas.list[0].type,callback:function(e){t.$set(t.elementDatas.list[0],"type",e)},expression:"elementDatas.list[0].type"}},t._l(t.crossSectionData,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),"polygon"==t.elementDatas.list[0].type?a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("featureSetting.style.batchExtrude.label1")))]),a("div",{staticClass:"popup-item-center items-center",on:{click:function(e){return t.togglePanel("crossSection")}}},[a("span",{staticClass:"margin-left-5"},[t._v(t._s(t.$t("menuIconName.edit")))]),a("CommonSVG",{attrs:{"icon-class":"notation_feature",size:16}})],1)]):"circle"==t.elementDatas.list[0].type?a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("featureSetting.style.batchExtrude.label2")))]),a("el-input",{attrs:{type:"number",title:"",size:"mini",placeholder:t.$t("featureSetting.style.batchExtrude.placeholder2")},on:{input:function(e){return t.inputBatchExtrudePostData(e,"radius")}},model:{value:t.elementDatas.list[0].radius,callback:function(e){t.$set(t.elementDatas.list[0],"radius",e)},expression:"elementDatas.list[0].radius"}})],1):t._e(),a("div",{directives:[{name:"show",rawName:"v-show",value:t.crossSectionState&&"polygon"==t.elementDatas.list[0].type,expression:"crossSectionState && elementDatas.list[0].type=='polygon'"}],staticClass:"regional-coordinates-container",staticStyle:{"min-width":"auto"},style:{maxHeight:t.maxDialogHeight}},[a("div",{staticClass:"title"},[a("span",[t._v(t._s(t.$t("featureSetting.style.batchExtrude.label1"))+"("+t._s(t.$t("featureSetting.style.batchExtrude.placeholder3"))+")")]),a("div",[a("i",{staticClass:"el-icon-plus cursor-btn margin-right-8",on:{click:function(e){return t.addCurrentOption(t.elementDatas.list[0])}}}),a("i",{staticClass:"el-icon-close cursor-btn",on:{click:function(e){return t.togglePanel("crossSection")}}})])]),a("div",{staticClass:"content",style:{maxHeight:t.maxContentHeight}},t._l(t.elementDatas.list[0].position,(function(e,s){return a("div",{key:"crossSection"+s,staticClass:"items-center margin-top-10"},[a("el-row",{attrs:{gutter:t.itemGutter}},[a("el-col",{attrs:{span:12}},[a("el-input",{attrs:{type:"number",title:"",size:"mini",placeholder:"X"},on:{input:function(e){return t.inputBatchExtrudePostData(e,"coords")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v("X")])]},proxy:!0}],null,!0),model:{value:e[0],callback:function(a){t.$set(e,0,a)},expression:"pos[0]"}})],1),a("el-col",{attrs:{span:12}},[a("el-input",{attrs:{type:"number",title:"",size:"mini",placeholder:"Y"},on:{input:function(e){return t.inputBatchExtrudePostData(e,"coords")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v("Y")]},proxy:!0}],null,!0),model:{value:e[1],callback:function(a){t.$set(e,1,a)},expression:"pos[1]"}})],1)],1),t.elementDatas.list[0].position.length>3?a("div",{staticClass:"text-center items-center margin-left-10"},[a("el-tooltip",{staticClass:"box-item",attrs:{enterable:!1,effect:"dark",content:t.$t("menuIconName.delete"),placement:"right"}},[a("i",{staticClass:"cursor-btn el-icon el-icon-remove-outline icons",on:{click:function(e){return t.removeCurrentOption(t.elementDatas.list[0],s)}}})])],1):t._e()],1)})),0)])]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("featureSetting.style.batchExtrude.label3")))]),a("div",{staticClass:"popup-item-center items-center",on:{click:function(e){return t.togglePanel("batchExtrude")}}},[a("span",{staticClass:"margin-left-5"},[t._v(t._s(t.$t("menuIconName.edit")))]),a("CommonSVG",{attrs:{"icon-class":"notation_feature",size:16}})],1)]),a("div",{directives:[{name:"show",rawName:"v-show",value:t.batchExtrudeState,expression:"batchExtrudeState"}],staticClass:"regional-coordinates-container special",style:{maxHeight:t.maxDialogHeight}},[a("div",{directives:[{name:"vDrag",rawName:"v-vDrag",value:{dragTopOffset:t.dragTopOffset},expression:"{dragTopOffset}"}],staticClass:"title"},[a("span",[t._v(t._s(t.$t("featureSetting.style.batchExtrude.label4")))]),a("div",[a("i",{staticClass:"el-icon-plus cursor-btn margin-right-8",on:{click:function(e){return t.addCurrentOption(t.params)}}}),a("i",{staticClass:"el-icon-close cursor-btn",on:{click:function(e){return t.togglePanel("batchExtrude")}}})])]),a("div",{staticClass:"content",style:{maxHeight:t.maxContentHeight}},t._l(t.params.datas,(function(e,s){return a("div",{key:"batch_extrude"+s,staticClass:"tab-div"},[a("div",{staticClass:"datas-top-bar"},[a("div",{staticClass:"left-title",on:{click:function(t){e.toggleState=!e.toggleState}}},[a("span",[t._v(t._s(t.$t("featureSetting.style.batchExtrude.label5"))+t._s(s+1))]),e.toggleState?a("i",{staticClass:"el-icon-caret-bottom"}):a("i",{staticClass:"el-icon-caret-top"})]),a("div",{staticClass:"right-menu"},[a("el-tooltip",{attrs:{enterable:!1,effect:"dark",content:t.$t("menuIconName.zoom"),placement:"right"}},[a("span",{staticClass:"cursor-btn margin-right-10"},[a("CommonSVG",{attrs:{size:16,"icon-class":"focusing_feature"},nativeOn:{click:function(e){return e.stopPropagation(),t.zoomBatchExtrudeOption(s)}}})],1)]),a("el-tooltip",{attrs:{enterable:!1,effect:"dark",content:t.$t("menuIconName.delete"),placement:"right"}},[a("span",{staticClass:"cursor-btn"},[a("CommonSVG",{attrs:{size:16,"icon-class":"delete"},nativeOn:{click:function(e){return e.stopPropagation(),t.removeBatchExtrudeOption(s)}}})],1)])],1)]),a("el-collapse-transition",[a("div",{directives:[{name:"show",rawName:"v-show",value:e.toggleState,expression:"item.toggleState"}],staticClass:"datas-input events-input"},[a("div",{staticClass:"items-center margin-bottom-5"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("featureSetting.style.batchExtrude.label6")))]),a("el-row",{staticClass:"items-center",attrs:{gutter:10}},[a("el-col",{attrs:{span:7}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:e.bottomPosition[0],expression:"item.bottomPosition[0]"}],attrs:{size:"mini",type:"number",title:"",placeholder:"X"},on:{input:function(e){return t.inputBatchExtrudePostData(e,"datas")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v("X")])]},proxy:!0}],null,!0),model:{value:e.bottomPosition[0],callback:function(a){t.$set(e.bottomPosition,0,a)},expression:"item.bottomPosition[0]"}})],1),a("el-col",{attrs:{span:7}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:e.bottomPosition[1],expression:"item.bottomPosition[1]"}],attrs:{size:"mini",type:"number",title:"",placeholder:"Y"},on:{input:function(e){return t.inputBatchExtrudePostData(e,"datas")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v("Y")])]},proxy:!0}],null,!0),model:{value:e.bottomPosition[1],callback:function(a){t.$set(e.bottomPosition,1,a)},expression:"item.bottomPosition[1]"}})],1),a("el-col",{attrs:{span:7}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:e.bottomPosition[2],expression:"item.bottomPosition[2]"}],attrs:{size:"mini",type:"number",title:"",placeholder:"value"},on:{input:function(e){return t.inputBatchExtrudePostData(e,"datas")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v("Z")])]},proxy:!0}],null,!0),model:{value:e.bottomPosition[2],callback:function(a){t.$set(e.bottomPosition,2,a)},expression:"item.bottomPosition[2]"}})],1),a("el-col",{staticClass:"items-center",staticStyle:{padding:"0"},attrs:{span:3}},[a("el-tooltip",{attrs:{enterable:!1,effect:"dark",placement:"right",content:t.$t("featureSetting.style.batchExtrude.tooltip")}},[a("i",{staticClass:"el-icon-sort cursor-btn",staticStyle:{"font-size":"14px"},on:{click:function(a){return t.copyCoordinates(e,"toBottom")}}})]),a("el-tooltip",{attrs:{enterable:!1,effect:"dark",content:t.$t("formRelational.basePoint.placeholder"),placement:"right"}},[a("span",{staticClass:"cursor-btn",on:{click:function(e){return t.clickCheckedOffsetCoordinate(s,"bottomPosition")}}},[a("CommonSVG",{attrs:{size:"18","icon-class":"select_coord"}})],1)])],1)],1)],1),a("div",{staticClass:"items-center margin-bottom-5"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("featureSetting.style.batchExtrude.label7")))]),a("el-row",{staticClass:"items-center",attrs:{gutter:10}},[a("el-col",{attrs:{span:7}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:e.topPosition[0],expression:"item.topPosition[0]"}],attrs:{size:"mini",type:"number",title:"",placeholder:"X"},on:{input:function(e){return t.inputBatchExtrudePostData(e,"datas")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v("X")])]},proxy:!0}],null,!0),model:{value:e.topPosition[0],callback:function(a){t.$set(e.topPosition,0,a)},expression:"item.topPosition[0]"}})],1),a("el-col",{attrs:{span:7}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:e.topPosition[1],expression:"item.topPosition[1]"}],attrs:{size:"mini",type:"number",title:"",placeholder:"Y"},on:{input:function(e){return t.inputBatchExtrudePostData(e,"datas")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v("Y")])]},proxy:!0}],null,!0),model:{value:e.topPosition[1],callback:function(a){t.$set(e.topPosition,1,a)},expression:"item.topPosition[1]"}})],1),a("el-col",{attrs:{span:7}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:e.topPosition[2],expression:"item.topPosition[2]"}],attrs:{size:"mini",type:"number",title:"",placeholder:"value"},on:{input:function(e){return t.inputBatchExtrudePostData(e,"datas")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v("Z")])]},proxy:!0}],null,!0),model:{value:e.topPosition[2],callback:function(a){t.$set(e.topPosition,2,a)},expression:"item.topPosition[2]"}})],1),a("el-col",{staticClass:"items-center",staticStyle:{padding:"0"},attrs:{span:3}},[a("el-tooltip",{attrs:{enterable:!1,effect:"dark",placement:"right",content:t.$t("featureSetting.style.batchExtrude.tooltip")}},[a("i",{staticClass:"el-icon-sort cursor-btn",staticStyle:{"font-size":"14px"},on:{click:function(a){return t.copyCoordinates(e,"toTop")}}})]),a("el-tooltip",{attrs:{enterable:!1,effect:"dark",content:t.$t("formRelational.basePoint.placeholder"),placement:"right"}},[a("span",{staticClass:"cursor-btn",on:{click:function(e){return t.clickCheckedOffsetCoordinate(s,"topPosition")}}},[a("CommonSVG",{attrs:{size:"18","icon-class":"select_coord"}})],1)])],1)],1)],1),a("div",{staticClass:"items-center margin-bottom-5"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.color.label")))]),a("el-color-picker",{attrs:{"popper-class":"colorPicker","show-alpha":!1,"color-format":"rgb",size:"mini"},on:{change:function(e){return t.inputBatchExtrudePostData(e,"datas")}},model:{value:e.color,callback:function(a){t.$set(e,"color",a)},expression:"item.color"}})],1)])])],1)})),0)])])])},je=[],ze=(a("07ac"),{name:"BatchExtrudeSetting",props:["currentElement"],components:{CommonSVG:function(){return a.e("chunk-51f90655").then(a.bind(null,"17d0"))}},data:function(){return{itemGutter:10,elementDatas:{id:"",longitude:0,latitude:0,altitude:0,rotation:[0,0,0],offset:[0,0,0],list:[{title:this.$t("featureSetting.style.batchExtrude.label8"),position:[[-1,-1],[1,-1],[1,1],[-1,1]],radius:2,type:"polygon",optionItem:{name:"position",value:[0,0]}}]},params:{datas:[{color:"rgb(255, 50, 0)",bottomPosition:[0,0,0],topPosition:[0,0,0],toggleState:!0}],currentOption:0,which:"bottomPosition",other:"topPosition",optionItem:{name:"datas",value:{color:"rgb(255, 50, 0)",bottomPosition:[0,0,0],topPosition:[0,0,0],toggleState:!0}}},crossSectionData:[{value:"polygon",label:this.$t("featureSetting.style.batchExtrude.label9")},{value:"circle",label:this.$t("featureSetting.style.batchExtrude.label10")}],crossSectionState:!1,batchExtrudeState:!0,maxDialogHeight:0,maxContentHeight:0}},computed:{dragTopOffset:function(){return this.$store.state.scene.topToolBarHeight}},created:function(){this.currentElement.staticType||this.init()},mounted:function(){this.calculateHeight(),window.addEventListener("resize",this.calculateHeight)},methods:{init:function(){var t=window.scene.features.get(this.currentElement.id);if(this.elementDatas.id=this.currentElement.id,this.elementDatas.longitude=t.origin[0],this.elementDatas.latitude=t.origin[1],this.elementDatas.altitude=t.altitude,this.elementDatas.list[0].type=t.data.type,"polygon"==t.data.type){var e=[];t.data.coords.forEach((function(t){e.push(Object.values(t))})),this.elementDatas.list[0].position=e}else this.elementDatas.list[0].radius=t.data.radius;var a=[];t.data.datas.forEach((function(t){a.push({color:t.color,bottomPosition:[t.bottomPosition.x,t.bottomPosition.y,t.bottomPosition.z],topPosition:[t.topPosition.x,t.topPosition.y,t.topPosition.z],toggleState:!0})})),this.params.datas=a},togglePanel:function(t){"crossSection"==t?this.crossSectionState=!this.crossSectionState:this.batchExtrudeState=!this.batchExtrudeState},addCurrentOption:function(t){t[t.optionItem.name].push(JSON.parse(JSON.stringify(t.optionItem.value)))},removeCurrentOption:function(t,e){t[t.optionItem.name].splice(e,1),this.inputBatchExtrudePostData("","coords")},removeBatchExtrudeOption:function(t){this.params.datas.splice(t,1),this.inputBatchExtrudePostData("","datas")},zoomBatchExtrudeOption:function(t){var e=window.scene.features.get(this.currentElement.id),a=e.data.datas[t];if(!a)return!1;window.scene.fit([a.id])},clickCheckedOffsetCoordinate:function(t,e){this.params.currentOption=t,this.params.which=e,this.params.other="bottomPosition"===e?"topPosition":"bottomPosition",window.scene.mv.status.selectable=!1,this.$message.success({message:this.$t("messageTips.checkCoordinate"),duration:2e3}),window.scene.mv.events.singleSelection.on("default",this.onCheckedOffsetCoordinate)},onCheckedOffsetCoordinate:function(t){var e=window.scene.mv._THREE,a=window.scene.queryPosition(new e.Vector2(t.clientX,t.clientY));if(""!=a&&void 0!=a){var s=this.params.currentOption,i=this.params.which,n=this.params.other;if(this.$set(this.params.datas[s][i],0,a.x),this.$set(this.params.datas[s][i],1,a.y),this.$set(this.params.datas[s][i],2,a.z),"bottomPosition"==i){var l=parseFloat(this.params.datas[s]["topPosition"][2]);(0==l||l<a.z)&&this.$set(this.params.datas[s]["topPosition"],2,a.z+10)}0==this.params.datas[s][n][0]&&0==this.params.datas[s][n][1]&&(this.$set(this.params.datas[s][n],0,a.x),this.$set(this.params.datas[s][n],1,a.y)),this.inputBatchExtrudePostData("","datas")}else this.$message.error(this.$t("messageTips.errorCheckCoordinate"));window.scene.mv.status.selectable=!0,window.scene.mv.events.singleSelection.off("default",this.onCheckedOffsetCoordinate)},calculateHeight:function(){var t=document.body.clientHeight,e=document.querySelector(".settings-container").clientHeight,a=document.querySelector(".topToolBarContainer").clientHeight,s=t-e-a-40;this.maxDialogHeight=s+"px",this.maxContentHeight=s-57+"px"},copyCoordinates:function(t,e){"toTop"==e?(t.topPosition.splice(0,1,t.bottomPosition[0]),t.topPosition.splice(1,1,t.bottomPosition[1])):(t.bottomPosition.splice(0,1,t.topPosition[0]),t.bottomPosition.splice(1,1,t.topPosition[1])),this.inputBatchExtrudePostData("","datas")},inputBatchExtrudePostData:function(t,e){var a=window.scene.features.get(this.currentElement.id),s={},i=this.elementDatas.list[0].position.map((function(t){return{x:parseFloat(t[0]+"")||0,y:parseFloat(t[1]+"")||0}}));s.coords=i;var n=Object.values(this.params.datas[0].bottomPosition),l=window.scene.mv.tools.coordinate.vector2mercator(n);switch(l.length-=1,a.origin=l,e){case"type":s.type=t;break;case"coords":var o=this.elementDatas.list[0].position.map((function(t){return{x:parseFloat(t[0]+"")||0,y:parseFloat(t[1]+"")||0}}));s.coords=o;break;case"radius":s.radius=parseFloat(t+"")||2;break;case"datas":var r=[];this.params.datas.forEach((function(t){r.push({color:t.color,bottomPosition:{x:parseFloat(t.bottomPosition[0]+"")||0,y:parseFloat(t.bottomPosition[1]+"")||0,z:parseFloat(t.bottomPosition[2]+"")||0},topPosition:{x:parseFloat(t.topPosition[0]+"")||0,y:parseFloat(t.topPosition[1]+"")||0,z:parseFloat(t.topPosition[2]+"")||0}})})),s.datas=r;break}window.scene.postData(s,a.dataKey)}},beforeDestroy:function(){window.removeEventListener("resize",this.calculateHeight)}}),Fe=ze,Ie=(a("ce7f"),Object(d["a"])(Fe,Oe,je,!1,null,"bdfc87aa",null)),Ae=Ie.exports,Ne=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex relative"},[a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("featureSetting.style.anchorPoint.label")))]),a("div",{staticClass:"popup-item-center items-center",on:{click:function(e){return t.togglePanel("pointSyleState")}}},[a("span",{staticClass:"margin-left-5"},[t._v(t._s(t._f("anchorStyleText")(t.checkedAnchorType,t.filtersI18n)))]),a("CommonSVG",{attrs:{"icon-class":"notation_feature",size:16}})],1)]),a("div",{directives:[{name:"show",rawName:"v-show",value:"videoAnchor"!=t.checkedAnchorType,expression:"checkedAnchorType!='videoAnchor'"}],staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("featureSetting.style.anchorPoint.label1"))+" ")]),a("div",{staticClass:"items-center"},[a("el-radio",{staticStyle:{"margin-right":"15px"},attrs:{label:"0"},on:{input:t.annotationRadioChange},model:{value:t.elementDatas.list[6].annotationRadio,callback:function(e){t.$set(t.elementDatas.list[6],"annotationRadio",e)},expression:"elementDatas.list[6].annotationRadio"}},[t._v(" "+t._s(t.$t("featureDatas.annotation.name"))+" ")]),a("el-radio",{attrs:{label:"1"},on:{input:t.annotationRadioChange},model:{value:t.elementDatas.list[6].annotationRadio,callback:function(e){t.$set(t.elementDatas.list[6],"annotationRadio",e)},expression:"elementDatas.list[6].annotationRadio"}},[t._v(" "+t._s(t.$t("featureDatas.billboard.name"))+" ")])],1)]),t.pointSyleState?a("div",{staticClass:"regional-coordinates-container"},[a("div",{staticClass:"title"},[a("span",[t._v(" "+t._s("custom"===t.checkedAnchorType?t.$t("featureDatas.annotation.extend.custom.name"):t.$t("featureSetting.style.anchorPoint.label"))+" "),"panel"==t.checkedAnchorType?[a("el-tooltip",{attrs:{effect:"dark",enterable:!1,placement:"top"}},[a("div",{attrs:{slot:"content"},domProps:{innerHTML:t._s(t.panelStyleTip)},slot:"content"}),a("i",{staticClass:"el-icon-question"})])]:t._e()],2),a("div",[a("i",{staticClass:"el-icon-close cursor-btn",on:{click:function(e){return t.togglePanel("pointSyleState")}}})])]),"custom"===t.checkedAnchorType?a("div",{ref:"customAchormonaco"},[a("div",{staticClass:"custom-tabs"},[a("div",[a("span",{class:[{active:t.customTabState},"cursor-btn"],on:{click:function(e){return t.toggleCustomTabState(!0)}}},[t._v("HTML")]),a("span",{class:[{active:!t.customTabState},"cursor-btn margin-left-15"],on:{click:function(e){return t.toggleCustomTabState(!1)}}},[t._v("CSS")])]),a("span",{staticClass:"cursor-btn anchor-example-tips",on:{click:function(e){return t.copyAnchorExampleTips(t.customTabState)}}},[t._v(" "+t._s(t.$t("featureSetting.style.anchorPoint.label2"))+" ")])]),a("div",{directives:[{name:"show",rawName:"v-show",value:t.customTabState,expression:"customTabState"}]},[a("Editor",{ref:"htmlEditor",attrs:{theme:"hc-black",language:"html",codes:t.elementDatas.list[3].htmlCode},on:{onCodeChange:t.htmlOnCodeChange}})],1),a("div",{directives:[{name:"show",rawName:"v-show",value:!t.customTabState,expression:"!customTabState"}]},[a("Editor",{ref:"cssEditor",attrs:{theme:"hc-black",language:"css",codes:t.elementDatas.list[3].cssCode},on:{onCodeChange:t.cssOnCodeChange}})],1),a("div",{staticClass:"draw-corner-mark"},[a("CommonSVG",{directives:[{name:"vDraw",rawName:"v-vDraw",value:t.setMonacoEditorSize,expression:"setMonacoEditorSize"}],attrs:{"icon-class":"drag_window",size:12}})],1)]):a("div",{staticClass:"items-center"},[t.pointStyleImg.anchor.length>0?a("div",{staticClass:"resources-label-container"},[a("div",{staticClass:"label-content anchor-label h100"},t._l(t.pointStyleImg.anchor,(function(e,s){return a("div",{key:"anchor"+s,staticClass:"label-item cursor-btn",class:{active:s==t.pointStyleImg.anchorCheck},on:{click:function(e){return t.checkCurrentAnchor(s,"anchorCheck")}}},[a("div",{staticClass:"thumb",class:{addLink:e.defaultSource}},[a("img",{attrs:{src:e.thumb,alt:""}}),a("i",{staticClass:"active-icon el-icon-circle-check"})])])})),0)]):t._e(),t.pointStyleImg.panel.length>0?[a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"resources-label-container panel-resources-label"},[a("div",{staticClass:"label-content"},t._l(t.pointStyleImg.panel,(function(e,s){return a("div",{key:"panel"+s,staticClass:"label-item cursor-btn",class:{active:s==t.pointStyleImg.panelCheck},on:{click:function(e){return t.checkCurrentAnchor(s,"panelCheck")}}},[a("div",{staticClass:"thumb"},[a("img",{attrs:{src:e.thumb,alt:""}}),a("i",{staticClass:"active-icon el-icon-circle-check"})]),a("p",{staticClass:"text-center"},[t._v(t._s(e.title))])])})),0),a("div",{staticClass:"items-center panel-toggle"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("featureSetting.style.anchorPoint.label3")))]),a("el-select",{staticStyle:{width:"100px"},attrs:{size:"mini",placeholder:t.$t("featureSetting.style.anchorPoint.placeholder")},model:{value:t.panelToggleMode.value,callback:function(e){t.$set(t.panelToggleMode,"value",e)},expression:"panelToggleMode.value"}},t._l(t.panelToggleMode.options,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1)])]:t._e()],2)]):t._e()]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{staticClass:"space-between-center",attrs:{span:12}},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.basePoint.label")))]),a("el-tooltip",{attrs:{effect:"dark",content:t.$t("formRelational.basePoint.placeholder"),placement:"top"}},[a("span",{staticClass:"outline-none cursor-btn",on:{click:function(e){return t.clickCheckedCoordinate()}}},[a("CommonSVG",{attrs:{color:"var(--theme)",size:"18","icon-class":"select_coord"}})],1)])],1),a("el-col",{attrs:{span:12}},[a("el-input",{class:{"is-error":t.inputError&&!t.styleFormDatas.validate.longitude},attrs:{type:"number",title:"",size:"mini",placeholder:t.$t("formRelational.longitude.placeholder")},on:{input:function(e){return t.inputChangeOrigin(e,"longitude")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v(t._s(t.$t("formRelational.longitude.label")))])]},proxy:!0}]),model:{value:t.elementDatas.longitude,callback:function(e){t.$set(t.elementDatas,"longitude",e)},expression:"elementDatas.longitude"}})],1)],1),a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{attrs:{span:12}},[a("el-input",{class:{"is-error":t.inputError&&!t.styleFormDatas.validate.latitude},attrs:{type:"number",title:"",size:"mini",placeholder:t.$t("formRelational.latitude.placeholder")},on:{input:function(e){return t.inputChangeOrigin(e,"latitude")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v(t._s(t.$t("formRelational.latitude.label")))]},proxy:!0}]),model:{value:t.elementDatas.latitude,callback:function(e){t.$set(t.elementDatas,"latitude",e)},expression:"elementDatas.latitude"}})],1),a("el-col",{attrs:{span:12}},[a("el-input",{class:{"is-error":t.inputError&&!t.styleFormDatas.validate.altitude},attrs:{type:"number",title:"",size:"mini",placeholder:t.$t("formRelational.altitude.placeholder")},on:{input:function(e){return t.inputChangeOrigin(e,"altitude")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v(t._s(t.$t("formRelational.altitude.label")))])]},proxy:!0}]),model:{value:t.elementDatas.altitude,callback:function(e){t.$set(t.elementDatas,"altitude",e)},expression:"elementDatas.altitude"}})],1)],1)],1),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-offset"},[a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{attrs:{span:12}},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.translate.label")))])]),a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.offset[0],expression:"elementDatas.offset[0]"}],attrs:{type:"number",title:"",size:"mini",placeholder:"X"},on:{input:function(e){return t.inputChangeOffset(e,"x")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v("X")])]},proxy:!0}]),model:{value:t.elementDatas.offset[0],callback:function(e){t.$set(t.elementDatas.offset,0,e)},expression:"elementDatas.offset[0]"}})],1)],1),a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.offset[1],expression:"elementDatas.offset[1]"}],attrs:{type:"number",title:"",size:"mini",placeholder:"Y"},on:{input:function(e){return t.inputChangeOffset(e,"y")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v("Y")]},proxy:!0}]),model:{value:t.elementDatas.offset[1],callback:function(e){t.$set(t.elementDatas.offset,1,e)},expression:"elementDatas.offset[1]"}})],1),a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.offset[2],expression:"elementDatas.offset[2]"}],attrs:{type:"number",title:"",size:"mini",placeholder:"Z"},on:{input:function(e){return t.inputChangeOffset(e,"z")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v("Z")]},proxy:!0}]),model:{value:t.elementDatas.offset[2],callback:function(e){t.$set(t.elementDatas.offset,2,e)},expression:"elementDatas.offset[2]"}})],1)],1)],1),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100",staticStyle:{"flex-basis":"230px","flex-shrink":"0"}},["custom"===t.checkedAnchorType&&1==t.elementDatas.list[6].annotationRadio?a("div",{staticClass:"items-center"},[1==t.elementDatas.list[6].annotationRadio?a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("featureSetting.style.anchorPoint.label4"))+" "),1==t.elementDatas.list[6].annotationRadio?a("el-tooltip",{attrs:{effect:"dark",content:t.$t("featureSetting.style.anchorPoint.tooltip"),placement:"top"}},[a("i",{staticClass:"el-icon-connection cursor-btn",staticStyle:{"font-size":"15px"},style:t.styleLocked,on:{click:function(e){t.elementDatas.list[1].locked=!t.elementDatas.list[1].locked}}})]):t._e()],1):t._e(),t.elementDatas.list[1].locked&&1==t.elementDatas.list[6].annotationRadio?a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.list[1].lockWidth,expression:"elementDatas.list[1].lockWidth"}],attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("featureSetting.style.anchorPoint.placeholder1")},model:{value:t.elementDatas.list[1].lockWidth,callback:function(e){t.$set(t.elementDatas.list[1],"lockWidth",e)},expression:"elementDatas.list[1].lockWidth"}},[a("template",{slot:"suffix"},[a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",placement:"top",enterable:!1}},[a("div",{attrs:{slot:"content"},domProps:{innerHTML:t._s(t.elementDatas.list[1].tipContent)},slot:"content"}),a("i",{staticClass:"outline-none el-input__icon el-icon-question"})])],1)],2):t._e()],1):t._e(),"custom"!==t.checkedAnchorType?a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title min85"},[t._v(" "+t._s(t.$t("featureSetting.style.anchorPoint.label4"))+" "),1==t.elementDatas.list[6].annotationRadio?a("el-tooltip",{attrs:{effect:"dark",content:t.$t("featureSetting.style.anchorPoint.tooltip"),placement:"top"}},[a("i",{staticClass:"el-icon-connection cursor-btn",staticStyle:{"font-size":"15px"},style:t.styleLocked,on:{click:function(e){t.elementDatas.list[1].locked=!t.elementDatas.list[1].locked}}})]):t._e()],1),t.elementDatas.list[1].locked&&1==t.elementDatas.list[6].annotationRadio?a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.list[1].lockWidth,expression:"elementDatas.list[1].lockWidth"}],attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("featureSetting.style.anchorPoint.placeholder1")},on:{input:function(e){return t.anchorPointInputValidate(e,"lockWidth")}},model:{value:t.elementDatas.list[1].lockWidth,callback:function(e){t.$set(t.elementDatas.list[1],"lockWidth",e)},expression:"elementDatas.list[1].lockWidth"}},[a("template",{slot:"suffix"},[a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",placement:"top",enterable:!1}},[a("div",{attrs:{slot:"content"},domProps:{innerHTML:t._s(t.elementDatas.list[1].tipContent)},slot:"content"}),a("i",{staticClass:"outline-none el-input__icon el-icon-question"})])],1)],2):a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{attrs:{span:11}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.list[1].width,expression:"elementDatas.list[1].width"}],attrs:{type:"number",title:"",size:"mini",placeholder:t.$t("featureSetting.style.anchorPoint.label5")},on:{input:function(e){return t.anchorPointInputValidate(e,"width")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v(t._s(t.$t("featureSetting.style.anchorPoint.label5")))]},proxy:!0}],null,!1,2132209892),model:{value:t.elementDatas.list[1].width,callback:function(e){t.$set(t.elementDatas.list[1],"width",e)},expression:"elementDatas.list[1].width"}})],1),a("el-col",{attrs:{span:2}}),a("el-col",{attrs:{span:11}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.list[1].height,expression:"elementDatas.list[1].height"}],attrs:{type:"number",title:"",size:"mini",placeholder:t.$t("featureSetting.style.anchorPoint.label6")},on:{input:function(e){return t.anchorPointInputValidate(e,"height")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v(t._s(t.$t("featureSetting.style.anchorPoint.label6")))]},proxy:!0}],null,!1,2818826375),model:{value:t.elementDatas.list[1].height,callback:function(e){t.$set(t.elementDatas.list[1],"height",e)},expression:"elementDatas.list[1].height"}})],1)],1)],1):t._e(),1==t.elementDatas.list[6].annotationRadio?a("div",{staticClass:"items-center"},[t._v(" ")]):a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title min85"},[t._v(" "+t._s(t.$t("featureSetting.style.anchorPoint.label7"))+" "),a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",placement:"top",enterable:!1}},[a("div",{attrs:{slot:"content"},domProps:{innerHTML:t._s(t.elementDatas.list[0].tipContent)},slot:"content"}),a("i",{staticClass:"outline-none el-input__icon el-icon-question"})])],1),a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{attrs:{span:11}},[a("el-input",{attrs:{size:"mini",type:"number"},on:{input:function(e){return t.anchorPointInputValidate(e,"visibleMin")}},model:{value:t.elementDatas.list[0].value[0],callback:function(e){t.$set(t.elementDatas.list[0].value,0,e)},expression:"elementDatas.list[0].value[0]"}})],1),a("el-col",{attrs:{span:2}},[t._v("-")]),a("el-col",{attrs:{span:11}},[a("el-input",{attrs:{size:"mini",type:"number"},on:{input:function(e){return t.anchorPointInputValidate(e,"visibleMax")}},model:{value:t.elementDatas.list[0].value[1],callback:function(e){t.$set(t.elementDatas.list[0].value,1,e)},expression:"elementDatas.list[0].value[1]"}})],1)],1)],1)]),t.linkPointDialog?a("dialogComp",{ref:"linkView",staticClass:"sourceDom",attrs:{hideHeader:!1,needClose:"true",zIndex:"4",height:"150",position:"fixed",left:0,top:0,right:0,bottom:0,title:t.$t("featureSetting.style.anchorPoint.label8"),icon:"icon-details",width:390,isSource:!0,type:"detailInfo"},on:{close:t.cancelLinkInput},scopedSlots:t._u([{key:"center",fn:function(){return[a("div",{staticClass:"add-container element-add-link-view"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:24}},[a("el-input",{class:{"is-error":t.pointStyleImg.anchor[0].inputError},attrs:{size:"small",placeholder:t.$t("featureSetting.style.anchorPoint.placeholder2")},model:{value:t.linkPointSrc,callback:function(e){t.linkPointSrc=e},expression:"linkPointSrc"}})],1)],1),a("div",{staticClass:"bottom-btn"},[a("span",{staticClass:"cursor-btn cancel margin-right-8",on:{click:t.cancelLinkInput}},[t._v(" "+t._s(t.$t("messageTips.cancel"))+" ")]),a("span",{staticClass:"cursor-btn confirm",on:{click:t.saveLinkInput}},[t._v(" "+t._s(t.$t("messageTips.confirm"))+" ")])])],1)]},proxy:!0}],null,!1,2120223161)}):t._e()],1)},Me=[],Ge=a("095c"),Xe={name:"AnchorPointSetting",props:["dragData","elementTransformObj"],mixins:[l["a"],g["a"]],components:{Editor:Ge["a"]},data:function(){return{itemGutter:10,elementDatas:{id:"",longitude:0,latitude:0,altitude:0,rotation:[0,0,0],offset:[0,0,0],list:[{title:this.$t("featureSetting.style.anchorPoint.label7"),value:[0,500],tipContent:this.$t("featureSetting.style.anchorPoint.tooltip1")},{title:this.$t("featureSetting.style.anchorPoint.label4"),width:30,height:30,locked:!1,lockWidth:1,tipContent:this.$t("featureSetting.style.anchorPoint.tooltip2")},{title:this.$t("featureSetting.style.anchorPoint.label1"),radio:"",needPanel:!1},{title:this.$t("featureSetting.style.anchorPoint.label9"),htmlCode:'<img style="width:30px;height:30px;" src="'.concat("",'image/anchor/1_point.png">'),cssCode:""},{title:this.$t("featureSetting.style.anchorPoint.label10"),defaultImg:[],checkedType:0},{fileSrc:"",checkedType:0},{annotationRadio:"0"}]},pointSyleState:!1,linkPointDialog:!1,linkPointSrc:"",customTabState:!0,pointStyleImg:{anchor:[{id:0,title:this.$t("featureSetting.style.anchorPoint.label11"),thumb:a("d161"),isDragData:!0,type:"anchor",fileSrc:"",defaultSource:!0,inputError:!1}],panel:[],anchorCheck:1,panelCheck:-1},panelToggleMode:{options:[{value:"mouseover",label:this.$t("featureSetting.style.anchorPoint.label12")},{value:"click",label:this.$t("featureSetting.style.anchorPoint.label13")},{value:"alwaysShow",label:this.$t("featureSetting.style.anchorPoint.label14")}],value:"alwaysShow",originValue:""},checkedAnchorType:"",panelStyleTip:this.$t("featureSetting.style.anchorPoint.tooltip3")}},created:function(){var t=window.scene.features.get(this.dragData.id);t.data.setType?(this.checkedAnchorType=t.data.setType.type,this.initEditDatas(t)):(this.checkedAnchorType=this.dragData.checkedType,this.init()),"custom"!=this.checkedAnchorType&&this.getPublicImg("anchor"),"panel"==this.checkedAnchorType&&this.getPublicImg("panel"),"videoAnchor"==this.checkedAnchorType&&this.getPublicImg("panel"),this.anchorPointInputValidate=T()(this.anchorPointInputValidate,1e3)},mounted:function(){var t=document.createElement("script");t.type="text/javascript",t.src="./sources/dom-to-image.min.js",document.head.appendChild(t)},filters:{anchorStyleText:function(t,e){var a="";switch(t){case"default":a=e("featureSetting.style.anchorPoint.label15");break;case"custom":a=e("featureSetting.style.anchorPoint.label16");break;case"panel":case"videoAnchor":a=e("featureSetting.style.anchorPoint.label17");break}return a}},computed:{styleLocked:function(){var t={color:"#409eff"};return this.elementDatas.list[1].locked?t:""}},methods:{anchorPointInputValidate:function(t,e){switch(e){case"lockWidth":(t<=0||""==t)&&this.$set(this.elementDatas.list[1],"lockWidth",1);break;case"width":(t<=0||""==t)&&this.$set(this.elementDatas.list[1],"width",1);break;case"height":(t<=0||""==t)&&this.$set(this.elementDatas.list[1],"height",1);break;case"visibleMin":(t<0||""==t)&&this.$set(this.elementDatas.list[0].value,0,0);break;case"visibleMax":""!=t&&t<=0&&this.$set(this.elementDatas.list[0].value,1,1);break}},filtersI18n:function(t){return this.$t(t)},initEditDatas:function(t){var e=t.data.setType,a=t.data.content;this.elementDatas.longitude=t.origin[0],this.elementDatas.latitude=t.origin[1],this.elementDatas.altitude=t.altitude,this.elementDatas.offset=t.offset;var s=[0,500];e.visibleDistance&&(s[0]=e.visibleDistance[0],e.visibleDistance[1]!==1/0&&(s[1]=e.visibleDistance[1])),this.elementDatas.list[0].value=s,this.elementDatas.list[1].width=a.width?a.width:e.checkedImgSize.width,this.elementDatas.list[1].height=a.height?a.height:e.checkedImgSize.height,this.elementDatas.list[2].radio=e.type,this.elementDatas.list[6].annotationRadio=e.annotationRadio,e.locked&&(this.elementDatas.list[1].locked=e.locked,this.elementDatas.list[1].lockWidth=t.data.rect.width),"custom"==e.type&&(this.elementDatas.list[3].htmlCode=t.data.htmlCode,this.elementDatas.list[3].cssCode=t.data.cssCode),"panel"!=e.type&&"videoAnchor"!=e.type||(this.elementDatas.list[4].checkedType=e.lineType,this.pointStyleImg.panelCheck=e.lineType),this.elementDatas.list[5].fileSrc=e.customSrc,this.elementDatas.list[5].checkedType=e.checkedImg,this.pointStyleImg.anchorCheck=e.checkedImg,0==e.checkedImg&&(this.pointStyleImg.anchor[0].fileSrc=e.customSrc,this.pointStyleImg.anchor[0].defaultSource=!1),e.panelToggleMode&&""!=e.panelToggleMode&&(this.panelToggleMode.value=e.panelToggleMode,this.panelToggleMode.originValue=e.panelToggleMode)},toggleCustomTabState:function(t){var e=this;this.customTabState=t,this.$nextTick((function(){e.setMonacoEditorSize()}))},setMonacoEditorSize:function(){var t=this.$refs.customAchormonaco.offsetHeight;this.customTabState?this.$refs.htmlEditor.$el.style.height="".concat(t-30,"px"):this.$refs.cssEditor.$el.style.height="".concat(t-30,"px")},init:function(){var t=window.scene.features.get(this.dragData.id);this.elementDatas.id=this.dragData.id,this.elementDatas.longitude=t.origin[0],this.elementDatas.latitude=t.origin[1],this.elementDatas.altitude=t.altitude,this.elementDatas.list[2].radio=this.dragData.checkedType,"videoAnchor"==this.checkedAnchorType&&(this.pointStyleImg.panelCheck=0)},getPublicImg:function(t){var e=this,s=null;switch(t){case"anchor":s=a("14e4");break;case"panel":s=a("e33b");break}var i={left:"左引线",middle:"中引线",right:"右引线",original:"默认"};s.keys().length>0&&s.keys().forEach((function(a,s){var l=a.replace(/(.*\/)*([\s\S]*?)/gi,"$2"),o=l.split(".")[0],r=o.split("_")[1],c=o.split("_")[0],u="".concat(window.location.origin+Object(n["g"])(),"image/").concat(t,"/").concat(l);"panel"==t&&(r=i[r]),e.pointStyleImg[t][c]={id:s+1,title:r,thumb:u,isDragData:!0,type:t,fileSrc:u}}))},togglePanel:function(t){this[t]=!this[t]},checkCurrentAnchor:function(t,e){"anchorCheck"==e&&0==t?(this.linkPointDialog=!this.linkPointDialog,""!=this.pointStyleImg.anchor[0].fileSrc&&(this.linkPointSrc=this.pointStyleImg.anchor[0].fileSrc)):(this.pointStyleImg[e]==t?("anchorCheck"==e&&parseInt(this.pointStyleImg.panelCheck+"")>=0&&(this.pointStyleImg.anchorCheck=-1),"videoAnchor"!=this.checkedAnchorType&&("panelCheck"==e&&parseInt(this.pointStyleImg.anchorCheck+"")<0&&(this.pointStyleImg.anchorCheck=1,this.pointStyleImg.panelCheck=-1),"panelCheck"==e&&parseInt(this.pointStyleImg.anchorCheck+"")>=0&&(this.pointStyleImg.panelCheck=-1))):this.pointStyleImg[e]=t,this.cancelLinkInput())},saveLinkInput:function(){var t=this,e=this.linkPointSrc;""!=e?Object(n["a"])(e).then((function(a){a?(t.linkPointDialog=!1,t.pointStyleImg.anchor[0].thumb=e,t.pointStyleImg.anchor[0].fileSrc=e,t.pointStyleImg.anchor[0].defaultSource=!1,t.pointStyleImg.anchorCheck=0,t.pointStyleImg.anchor[0].inputError=!1):(t.pointStyleImg.anchor[0].inputError=!0,t.$message.error(t.$t("messageTips.errorLoadAddress")))})):this.$message.error(this.$t("messageTips.errorAddress"))},cancelLinkInput:function(){this.linkPointDialog=!1,this.pointStyleImg.anchor[0].inputError&&(this.linkPointSrc="",this.pointStyleImg.anchor[0].inputError=!1)},copyAnchorExampleTips:function(t){var e="";e=t?document.querySelector("#default-panel-template-scene").innerHTML:this.$parent.anchorExampleCss;var a=document.createElement("textarea");a.value=e,a.setAttribute("readonly",""),a.style.position="absolute",a.style.left="-9999px",document.body.appendChild(a);var s=document.getSelection().rangeCount>0&&document.getSelection().getRangeAt(0);a.select(),document.execCommand("copy"),document.body.removeChild(a),this.$notify({title:"",message:this.$t("messageTips.copySuccess1"),type:"success"}),s&&(document.getSelection().removeAllRanges(),document.getSelection().addRange(s))},htmlOnCodeChange:function(t,e){this.elementDatas.list[3].htmlCode=t},cssOnCodeChange:function(t,e){this.elementDatas.list[3].cssCode=t},annotationRadioChange:function(t){0==t?(this.elementDatas.list[1].locked=!1,this.elementDatas.list[1].width=30,this.elementDatas.list[1].height=30,this.panelToggleMode.options=[{value:"mouseover",label:this.$t("featureSetting.style.anchorPoint.label12")},{value:"click",label:this.$t("featureSetting.style.anchorPoint.label13")},{value:"alwaysShow",label:this.$t("featureSetting.style.anchorPoint.label14")}],this.panelToggleMode.value=this.panelToggleMode.originValue||this.panelToggleMode.value):(this.elementDatas.list[1].locked=!0,this.elementDatas.list[1].width=4,this.elementDatas.list[1].height=2,this.panelToggleMode.options=[{value:"alwaysShow",label:this.$t("featureSetting.style.anchorPoint.label14")}],this.panelToggleMode.value="alwaysShow")}}},Ve=Xe,Le=(a("d246"),Object(d["a"])(Ve,Ne,Me,!1,null,"8ffee520",null)),Be=Le.exports,He=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex relative"},[a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("topToolBarMenu.advanced.children.advanced_envmap.name"))+" ")]),a("div",{staticClass:"popup-item-center items-center",on:{click:t.togglePanel}},[a("span",{staticClass:"margin-left-5"},[t._v(t._s(t.envMaps[t.elementDatas.list[0].value].name))]),a("CommonSVG",{attrs:{"icon-class":"notation_feature",size:16}})],1)]),a("div",{directives:[{name:"show",rawName:"v-show",value:t.panelState,expression:"panelState"}],staticClass:"regional-coordinates-container"},[a("div",{staticClass:"title"},[a("span",[t._v(" "+t._s(t.$t("topToolBarMenu.advanced.children.advanced_envmap.settings.label"))+" ")]),a("div",[a("i",{staticClass:"el-icon-close cursor-btn",on:{click:t.togglePanel}})])]),a("div",{staticClass:"resources-label-container"},[a("div",{staticClass:"label-content anchor-label h100"},t._l(t.envMaps,(function(e,s){return a("div",{key:e.name,staticClass:"label-item cursor-btn",class:{active:s==t.elementDatas.list[0].value},on:{click:function(e){return t.checkCurrentAnchor(s)}}},[a("div",{staticClass:"thumb"},[a("img",{attrs:{src:e.src,alt:""}})]),a("p",{staticClass:"text-center"},[t._v(t._s(e.name))])])})),0)])])]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-200"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title margin-right-8"},[t._v(" "+t._s(t.$t("topToolBarMenu.advanced.children.advanced_envmap.settings.label1"))+" ")]),a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.list[1].value,expression:"elementDatas.list[1].value"}],attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("topToolBarMenu.advanced.children.advanced_envmap.settings.placeholder")},on:{input:t.setEnvAngle},model:{value:t.elementDatas.list[1].value,callback:function(e){t.$set(t.elementDatas.list[1],"value",e)},expression:"elementDatas.list[1].value"}},[a("template",{slot:"suffix"},[a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",placement:"top",enterable:!1}},[a("div",{attrs:{slot:"content"},slot:"content"},[t._v(" "+t._s(t.$t("topToolBarMenu.advanced.children.advanced_envmap.settings.tooltips"))+" ")]),a("i",{staticClass:"outline-none el-input__icon el-icon-question"})])],1)],2)],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title margin-right-8"},[t._v(" "+t._s(t.$t("topToolBarMenu.advanced.children.advanced_envmap.settings.label2"))+" ")]),a("div",{staticClass:"items-center w100"},[a("el-slider",{staticStyle:{width:"100%"},attrs:{min:.1,max:10,step:.1},on:{change:t.setEnvmapIntensity},model:{value:t.elementDatas.list[2].value,callback:function(e){t.$set(t.elementDatas.list[2],"value",e)},expression:"elementDatas.list[2].value"}}),a("span",{staticClass:"slider-num"},[t._v(t._s(t.elementDatas.list[2].value))])],1)])]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-200"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title margin-right-15 min85"},[t._v(" "+t._s(t.$t("topToolBarMenu.advanced.children.advanced_envmap.settings.label3"))+" ")]),a("div",{staticClass:"items-center w100"},[a("el-slider",{staticStyle:{width:"100%"},attrs:{min:0,max:100,step:1},on:{change:t.setFogIntensity},model:{value:t.elementDatas.list[4].value,callback:function(e){t.$set(t.elementDatas.list[4],"value",e)},expression:"elementDatas.list[4].value"}}),a("span",{staticClass:"slider-num"},[t._v(t._s(t.elementDatas.list[4].value))])],1)]),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title min85"},[t._v(" "+t._s(t.$t("topToolBarMenu.advanced.children.advanced_envmap.settings.label4"))+" ")]),a("div",{staticClass:"items-center w100"},[a("span",{staticClass:"slider-num"},[t._v(t._s(t.elementDatas.list[3].value[0]))]),a("el-slider",{staticStyle:{width:"100px"},attrs:{range:"",step:1,min:-20,max:20},on:{change:t.setFogRange},model:{value:t.elementDatas.list[3].value,callback:function(e){t.$set(t.elementDatas.list[3],"value",e)},expression:"elementDatas.list[3].value"}}),a("span",{staticClass:"slider-num"},[t._v(t._s(t.elementDatas.list[3].value[1]))])],1)])])])},Ke=[],Ye=a("43fe"),We={name:"advancedEnvmap",components:{CommonSVG:function(){return a.e("chunk-51f90655").then(a.bind(null,"17d0"))}},data:function(){return{envMaps:[],elementDatas:{list:[{title:this.$t("formRelational.image.label"),value:3},{title:this.$t("topToolBarMenu.advanced.children.advanced_envmap.settings.label1"),value:30},{title:this.$t("topToolBarMenu.advanced.children.advanced_envmap.settings.label2"),value:.7},{title:this.$t("topToolBarMenu.advanced.children.advanced_envmap.settings.label4"),value:[-20,20],checked:!1},{title:this.$t("topToolBarMenu.advanced.children.advanced_envmap.settings.label3"),value:0}]},panelState:!1}},created:function(){this.setEnvMaps();var t=window.scene.config.envmapIndex,e=window.scene.config.envAngle,a=window.scene.config.envmapIntensity,s=window.scene.config.fogRange,i=window.scene.config.fogIntensity,n={img:t,angle:e,intensity:a,fogRange:s,fogIntensity:i};this.elementDatas.list[0].value=t,this.elementDatas.list[1].value=e,this.elementDatas.list[2].value=a,void 0!=s&&(this.elementDatas.list[3].checked=!0,this.elementDatas.list[3].value=s),void 0!=i&&(this.elementDatas.list[4].value=i),this.$store.commit("saveFeatureRawData",JSON.stringify(n))},methods:{setEnvMaps:function(){var t=this,e=this.$i18n.locale,a=this.$i18n.getLocaleMessage(e).topToolBarMenu.advanced.children.advanced_envmap.settings.LightingType;Ye.envMapsName.forEach((function(e,s){t.envMaps.push({name:a[s],src:e["src"]})}))},setEnvAngle:function(t){var e=parseFloat(t+"");e<0&&(e=0),e>360&&(e=360),this.elementDatas.list[1].value=e,window.scene.config.envAngle=e,window.scene.render()},setEnvmapIntensity:function(t){var e=parseFloat(t+"");this.elementDatas.list[2].value=e,window.scene.config.envmapIntensity=e,window.scene.render()},togglePanel:function(){this.panelState=!this.panelState},checkCurrentAnchor:function(t){this.elementDatas.list[0].value=t,window.scene.config.envmapIndex=t,setTimeout((function(){window.scene.render()}),300),window.scene.render()},setFogRange:function(){window.scene.config.fogRange=this.elementDatas.list[3].value},setFogIntensity:function(){window.scene.config.fogIntensity=this.elementDatas.list[4].value,window.scene.render()}}},Ze=We,Je=(a("3fd7"),Object(d["a"])(Ze,He,Ke,!1,null,"6510539c",null)),qe=Je.exports,Ue=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex w100"},[a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title",staticStyle:{"flex-basis":"120px"}},[t._v(" "+t._s(t.elementDatas.list[0].title)+" "),a("el-tooltip",{staticClass:"box-item",attrs:{enterable:!1,effect:"dark",placement:"top"}},[a("div",{attrs:{slot:"content"},domProps:{innerHTML:t._s(t.$t("topToolBarMenu.advanced.children.advanced_setting.settings.tooltips"))},slot:"content"}),a("i",{staticClass:"outline-none el-input__icon el-icon-question"})])],1),a("div",{staticClass:"items-center w100"},[a("el-slider",{staticStyle:{width:"100%"},attrs:{min:0,max:10,step:1},on:{change:function(e){return t.inputChangeSetting("cullingRatio")}},model:{value:t.elementDatas.list[0].value,callback:function(e){t.$set(t.elementDatas.list[0],"value",e)},expression:"elementDatas.list[0].value"}}),a("span",{staticClass:"slider-num"},[t._v(t._s(t.elementDatas.list[0].value))])],1)]),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title",staticStyle:{"flex-basis":"120px"}},[t._v(" "+t._s(t.elementDatas.list[1].title)+" ")]),a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.list[1].value,expression:"elementDatas.list[1].value"}],attrs:{size:"mini",type:"number",title:"",placeholder:"请输入最大场景内存"},on:{input:function(e){return t.inputChangeSetting("maxMemory")}},scopedSlots:t._u([{key:"append",fn:function(){return[a("span",[t._v("MB")])]},proxy:!0}]),model:{value:t.elementDatas.list[1].value,callback:function(e){t.$set(t.elementDatas.list[1],"value",e)},expression:"elementDatas.list[1].value"}})],1)]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.elementDatas.list[2].title)+" ")]),a("div",{staticClass:"items-center"},[a("el-switch",{staticStyle:{"margin-left":"12px"},on:{change:function(e){return t.inputChangeSetting("dynamicReleasing")}},model:{value:t.elementDatas.list[2].value,callback:function(e){t.$set(t.elementDatas.list[2],"value",e)},expression:"elementDatas.list[2].value"}}),a("el-tooltip",{staticClass:"box-item",attrs:{enterable:!1,effect:"dark",placement:"top"}},[a("div",{attrs:{slot:"content"},domProps:{innerHTML:t._s(t.$t("topToolBarMenu.advanced.children.advanced_setting.settings.tooltips1"))},slot:"content"}),a("i",{staticClass:"outline-none el-input__icon el-icon-question"})])],1)]),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.elementDatas.list[3].title)+" ")]),a("el-switch",{staticStyle:{"margin-left":"12px"},on:{change:function(e){return t.inputChangeSetting("highQuality")}},model:{value:t.elementDatas.list[3].value,callback:function(e){t.$set(t.elementDatas.list[3],"value",e)},expression:"elementDatas.list[3].value"}})],1)]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.elementDatas.list[6].title)+" "),a("el-tooltip",{staticClass:"box-item",attrs:{enterable:!1,effect:"dark",placement:"top"}},[a("div",{attrs:{slot:"content"},domProps:{innerHTML:t._s(t.$t("topToolBarMenu.advanced.children.advanced_setting.settings.tooltips2"))},slot:"content"}),a("i",{staticClass:"outline-none el-input__icon el-icon-question"})])],1),a("el-checkbox",{on:{change:function(e){return t.inputChangeSetting("highlight")}},model:{value:t.elementDatas.list[6].value,callback:function(e){t.$set(t.elementDatas.list[6],"value",e)},expression:"elementDatas.list[6].value"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.elementDatas.list[4].title)+" ")]),a("el-color-picker",{attrs:{"popper-class":"colorPicker","show-alpha":!1,"color-format":"rgb",size:"mini"},on:{change:function(e){return t.inputChangeSetting("highlightColor")}},model:{value:t.elementDatas.list[4].value,callback:function(e){t.$set(t.elementDatas.list[4],"value",e)},expression:"elementDatas.list[4].value"}})],1)]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title",staticStyle:{width:"90px"}},[t._v(" "+t._s(t.elementDatas.list[7].title)+" ")]),a("el-checkbox",{on:{change:function(e){return t.inputChangeSetting("ssao")}},model:{value:t.elementDatas.list[7].value,callback:function(e){t.$set(t.elementDatas.list[7],"value",e)},expression:"elementDatas.list[7].value"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title",staticStyle:{width:"90px"}},[t._v(" "+t._s(t.$t("topToolBarMenu.advanced.children.advanced_setting.settings.label8"))+" ")]),a("el-checkbox",{on:{change:function(e){return t.inputChangeSetting("shadow")}},model:{value:t.elementDatas.list[8].value,callback:function(e){t.$set(t.elementDatas.list[8],"value",e)},expression:"elementDatas.list[8].value"}})],1)]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100",staticStyle:{"flex-basis":"400px"}},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title",staticStyle:{"flex-basis":"120px","flex-shrink":"0"}},[t._v(" "+t._s(t.elementDatas.list[5].title)+" "),a("el-tooltip",{staticClass:"box-item",attrs:{enterable:!1,effect:"dark",placement:"top"}},[a("div",{attrs:{slot:"content"},slot:"content"},[t._v(" "+t._s(t.$t("topToolBarMenu.advanced.children.advanced_setting.settings.tooltips3"))+" ")]),a("i",{staticClass:"outline-none el-input__icon el-icon-question"})])],1),a("el-input",{attrs:{size:"mini",placeholder:"Mapbox Token"},on:{input:function(e){return t.inputChangeSetting("mapbox")}},model:{value:t.elementDatas.list[5].value,callback:function(e){t.$set(t.elementDatas.list[5],"value",e)},expression:"elementDatas.list[5].value"}})],1)])])},Qe=[],ta={name:"advancedSetting",data:function(){return{elementDatas:{list:[{title:this.$t("topToolBarMenu.advanced.children.advanced_setting.settings.label"),value:0},{title:this.$t("topToolBarMenu.advanced.children.advanced_setting.settings.label1"),value:8192},{title:this.$t("topToolBarMenu.advanced.children.advanced_setting.settings.label2"),value:!1},{title:this.$t("topToolBarMenu.advanced.children.advanced_setting.settings.label3"),value:!1},{title:this.$t("topToolBarMenu.advanced.children.advanced_setting.settings.label4"),value:"rgb(0, 0.6274509803921569, 0.9764705882352941)"},{title:this.$t("topToolBarMenu.advanced.children.advanced_setting.settings.label5"),value:""},{title:this.$t("topToolBarMenu.advanced.children.advanced_setting.settings.label6"),value:!0},{title:this.$t("topToolBarMenu.advanced.children.advanced_setting.settings.label7"),value:!0},{title:this.$t("topToolBarMenu.advanced.children.advanced_setting.settings.label8"),value:!1}]},renderMode:[{title:this.$t("dialog.renderSetting.label1"),value:1},{title:this.$t("dialog.renderSetting.label2"),value:2},{title:this.$t("dialog.renderSetting.label3"),value:3}],modeRadio:1}},created:function(){var t,e={cullingRatio:window.scene.config.cullingRatio,maxMemory:window.scene.config.maxMemory,dynamicReleasing:window.scene.config.dynamicReleasing,highQuality:window.scene.config.highQuality,highlightColor:null!==(t=window.scene.config.highlightColor)&&void 0!==t?t:"rgb(0, 160, 249)",mapboxToken:window.scene.config.mapboxToken,highlight:window.scene.config.highlight,ssao:window.scene.config.ssao,shadow:window.scene.config.shadow,modeRadio:window.scene.mv.renderMode};if(!isNaN(e.highlightColor)){var a=Object(n["b"])(e.highlightColor);e.highlightColor=Object(n["e"])(a)}this.elementDatas.list[0].value=e.cullingRatio,this.elementDatas.list[1].value=e.maxMemory,this.elementDatas.list[2].value=e.dynamicReleasing,this.elementDatas.list[3].value=e.highQuality,this.elementDatas.list[4].value=e.highlightColor,this.elementDatas.list[5].value=e.mapboxToken,this.elementDatas.list[6].value=e.highlight,this.elementDatas.list[7].value=e.ssao,this.elementDatas.list[8].value=e.shadow,this.modeRadio=e.modeRadio,this.$store.commit("saveFeatureRawData",JSON.stringify(e))},methods:{inputChangeSetting:function(t){switch(t){case"cullingRatio":window.scene.config.cullingRatio=parseFloat(this.elementDatas.list[0].value+"");break;case"maxMemory":window.scene.config.maxMemory=parseFloat(this.elementDatas.list[1].value+"");break;case"dynamicReleasing":window.scene.config.dynamicReleasing=this.elementDatas.list[2].value;break;case"highQuality":window.scene.config.highQuality=this.elementDatas.list[3].value;break;case"highlightColor":window.scene.config.highlightColor=this.elementDatas.list[4].value;break;case"mapbox":window.scene.config.mapboxToken=this.elementDatas.list[5].value;break;case"highlight":window.scene.config.highlight=this.elementDatas.list[6].value;break;case"ssao":window.scene.config.ssao=this.elementDatas.list[7].value;break;case"shadow":window.scene.config.shadow=this.elementDatas.list[8].value;break;case"modeRadio":window.scene.mv.renderMode=this.modeRadio;break}window.scene.render()}}},ea=ta,aa=(a("ab47"),Object(d["a"])(ea,Ue,Qe,!1,null,"323a2206",null)),sa=aa.exports,ia=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex relative path-animation-setting w100"},[a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.elementDatas.list[1].title))]),a("div",{staticClass:"popup-item-center items-center",on:{click:t.togglePanel}},[a("CommonSVG",{attrs:{"icon-class":"xyz",size:16}}),a("span",{staticClass:"margin-left-5"},[t._v(t._s(t.elementDatas.list[1].title))])],1)]),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("topToolBarMenu.advanced.children.pathAnimation.settings.label"))+" ")]),a("el-input",{attrs:{size:"mini",type:"text",placeholder:t.$t("topToolBarMenu.advanced.children.pathAnimation.settings.placeholder")},on:{input:function(e){return t.changeParams(e,"avatarUrl")}},model:{value:t.params.avatarUrl,callback:function(e){t.$set(t.params,"avatarUrl",e)},expression:"params.avatarUrl"}})],1)]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("topToolBarMenu.advanced.children.pathAnimation.settings.label1"))+" ")]),a("el-switch",{staticStyle:{"margin-left":"12px"},on:{change:function(e){return t.changeParams(e,"avatarVisible")}},model:{value:t.params.avatarVisible,callback:function(e){t.$set(t.params,"avatarVisible",e)},expression:"params.avatarVisible"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("topToolBarMenu.advanced.children.pathAnimation.settings.label2"))+" ")]),a("el-switch",{staticStyle:{"margin-left":"12px"},on:{change:function(e){return t.changeParams(e,"showLine")}},model:{value:t.params.showLine,callback:function(e){t.$set(t.params,"showLine",e)},expression:"params.showLine"}})],1)]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-160"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("topToolBarMenu.advanced.children.pathAnimation.settings.label3"))+" ")]),a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.params.step,expression:"params.step"}],attrs:{size:"mini",type:"number",placeholder:t.$t("topToolBarMenu.advanced.children.pathAnimation.settings.placeholder")},on:{input:function(e){return t.changeParams(e,"step")}},scopedSlots:t._u([{key:"append",fn:function(){return[a("span",[t._v("m")])]},proxy:!0}]),model:{value:t.params.step,callback:function(e){t.$set(t.params,"step",e)},expression:"params.step"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.speed.label")))]),a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.params.speed,expression:"params.speed"}],attrs:{size:"mini",type:"number",placeholder:t.$t("formRelational.speed.placeholder")},on:{input:function(e){return t.changeParams(e,"speed")}},scopedSlots:t._u([{key:"append",fn:function(){return[a("span",[t._v("m/s")])]},proxy:!0}]),model:{value:t.params.speed,callback:function(e){t.$set(t.params,"speed",e)},expression:"params.speed"}})],1)]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-200"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("topToolBarMenu.advanced.children.pathAnimation.settings.label4"))+" ")]),a("div",{staticClass:"items-center w100"},[a("el-slider",{staticStyle:{width:"100%"},attrs:{min:3,max:10,step:1,"format-tooltip":t.sliderTooltip},on:{change:function(e){return t.changeParams(e,"cameraDistance")}},model:{value:t.params.cameraDistance,callback:function(e){t.$set(t.params,"cameraDistance",e)},expression:"params.cameraDistance"}}),a("span",{staticClass:"slider-num"},[t._v(t._s(t.params.cameraDistance))])],1)]),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("topToolBarMenu.advanced.children.pathAnimation.settings.label7"))+" "),a("el-tooltip",{staticClass:"box-item",attrs:{enterable:!1,effect:"dark",placement:"top"}},[a("div",{attrs:{slot:"content"},domProps:{innerHTML:t._s(t.$t("topToolBarMenu.advanced.children.pathAnimation.settings.tooltips"))},slot:"content"}),a("i",{staticClass:"el-icon-question"})])],1),a("div",{staticClass:"items-center w100"},[a("el-slider",{staticStyle:{width:"100%"},attrs:{min:0,max:85,step:1},on:{change:function(e){return t.changeParams(e,"pitch")}},model:{value:t.params.pitch,callback:function(e){t.$set(t.params,"pitch",e)},expression:"params.pitch"}}),a("span",{staticClass:"slider-num"},[t._v(t._s(t.params.pitch))])],1)])]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-200"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("topToolBarMenu.advanced.children.pathAnimation.settings.label5"))+" ")]),a("div",{staticClass:"items-center w100"},[a("el-slider",{staticStyle:{width:"100%"},attrs:{min:-20,max:20,step:1,"format-tooltip":t.verticleOffsetTooltip},on:{change:function(e){return t.changeParams(e,"verticleOffset")}},model:{value:t.params.verticleOffset,callback:function(e){t.$set(t.params,"verticleOffset",e)},expression:"params.verticleOffset"}}),a("span",{staticClass:"slider-num"},[t._v(t._s(t.params.verticleOffset))])],1)])]),a("div",{staticClass:"after-lineX"}),t.panelState?a("div",{staticClass:"regional-coordinates-container reginoal-dialog"},[a("div",{staticClass:"title"},[a("span",[t._v(t._s(t.elementDatas.list[1].title))]),a("div",[a("span",{staticClass:"downloadExcel",on:{click:t.downloadExcel}},[t._v(t._s(t.$t("others.export")))]),a("span",{staticClass:"downloadExcel",on:{click:t.importData}},[t._v(t._s(t.$t("others.import")))]),a("i",{staticClass:"el-icon-plus cursor-btn margin-right-8",on:{click:function(e){return t.addPoint()}}}),a("i",{staticClass:"el-icon-close cursor-btn",on:{click:t.togglePanel}})])]),a("div",{staticClass:"content"},[a("div",{staticClass:"list-wrap"},t._l(t.elementDatas.list[1].position,(function(e,s){return a("div",{key:s,staticClass:"position-list"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:e[0],expression:"pos[0]"}],attrs:{type:"number",size:"mini",placeholder:"X"},on:{input:function(e){return t.changePosition(e,"positions")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v("X")])]},proxy:!0}],null,!0),model:{value:e[0],callback:function(a){t.$set(e,0,a)},expression:"pos[0]"}})],1),a("el-col",{attrs:{span:8}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:e[1],expression:"pos[1]"}],attrs:{type:"number",size:"mini",placeholder:"Y"},on:{input:function(e){return t.changePosition(e,"positions")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v("Y")]},proxy:!0}],null,!0),model:{value:e[1],callback:function(a){t.$set(e,1,a)},expression:"pos[1]"}})],1),a("el-col",{attrs:{span:8}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:e[2],expression:"pos[2]"}],attrs:{type:"number",size:"mini",placeholder:"Z"},on:{input:function(e){return t.changePosition(e,"positions")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v("Z")]},proxy:!0}],null,!0),model:{value:e[2],callback:function(a){t.$set(e,2,a)},expression:"pos[2]"}})],1)],1),a("div",{staticClass:"delete-btn"},[a("span",{staticClass:"coord-box",on:{click:function(a){return t.clickCheckedOffsetCoordinate(s,e)}}},[a("CommonSVG",{attrs:{color:t.curIndex===s?"var(--theme)":"#FFFFFF",size:"18","icon-class":"amend_feature"}})],1),t.elementDatas.list[1].position.length>2?a("i",{staticClass:"el-icon el-icon-remove-outline icons",staticStyle:{"margin-top":"2px"},on:{click:function(e){return t.delPoints(s)}}}):t._e()])],1)})),0)])]):t._e(),t.showImportDialog?a("CoordinateImport",{on:{close:function(e){t.showImportDialog=!1},confirm:t.getPosition}}):t._e()],1)},na=[],la={name:"pathAnimation",components:{CommonSVG:function(){return a.e("chunk-51f90655").then(a.bind(null,"17d0"))},CoordinateImport:Bt["a"]},data:function(){return{showImportDialog:!1,elementDatas:{list:[{},{title:this.$t("topToolBarMenu.advanced.children.pathAnimation.settings.label6"),position:[[0,0,0],[0,0,0]],optionState:!0}]},panelState:!1,curIndex:"",params:{avatarUrl:"".concat(window.IP_CONFIG.SOURCES_URL,"ref/walk/bimworker.gltf"),step:.75,speed:5,cameraDistance:5,showLine:!0,avatarVisible:!0,verticleOffset:0,pitch:75},timer:null,inputTimeout:null}},created:function(){var t=window.scene.mv.tools.draw.getAllData(),e=window.scene.findPathRoaming(this.curPathanimationId);if(t.polyline.length){for(var a=t.polyline[0].points.length,s=[],i=0;i<a;i++)s[i]=[t.polyline[0].points[i].x,t.polyline[0].points[i].y,t.polyline[0].points[i].z];this.elementDatas.list[1].position=s}this.params=Object.assign({},this.params,e);window.scene;window.scene.mv.tools.draw.editPoint(),window.scene.render(),window.scene.mv.events.keyPointChange.on("change",this.keyPointChangeHandle),window.scene.mv.events.transformChange.on("default",this.transformChangeHandle),this.changeParams=T()(this.changeParams,800)},beforeDestroy:function(){var t=this.$store.state.pathAnimation.editPathAnimation;window.scene.mv.events.keyPointChange.off("change",this.keyPointChangeHandle),window.scene.mv.events.transformChange.off("default",this.transformChangeHandle),t&&this.$store.commit("toogleEditPathAnimation",!1);var e=this.$store.state.dialog.activeDialog;-1!==e.indexOf("pathAnimation")&&this.$store.commit("toggleActiveDialog","pathAnimation"),window.scene.mv.tools.draw.deactive(),window.scene.mv.tools.transform.deactive(),this.$store.commit("toogleTransFormStatus",!1),clearTimeout(this.timer),""!==this.curIndex&&this.offEvent()},computed:{editPathAnimation:function(){return this.$store.state.pathAnimation.editPathAnimation},transFormStatus:function(){return this.$store.state.pathAnimation.transFormStatus},curPathanimationId:function(){return this.$store.state.pathAnimation.curPathanimationId}},methods:{sliderTooltip:function(t){return"".concat(t," m")},verticleOffsetTooltip:function(t){return"".concat(t," m")},keyPointChangeHandle:function(t){this.$store.commit("toogleEditPathAnimation",!0),this.transFormStatus?window.scene.mv.tools.transform.deactive():this.$store.commit("toogleTransFormStatus",!0);var e=this.$store.state.dialog.activeDialog;-1===e.indexOf("pathAnimation")&&this.$store.commit("toggleActiveDialog","pathAnimation"),window.scene.mv.tools.transform.active([t.x,t.y,t.z],1),window.scene.mv.tools.draw.currentPointMesh.visible=!0,this.$store.commit("setPathAnimationPosition",{x:t.x,y:t.y,z:t.z})},transformChangeHandle:function(t){var e=window.scene.mv.tools.draw.currentPointIndex;this.$set(this.elementDatas.list[1].position,e,[t.x,t.y,t.z]),window.scene.render(),this.$store.commit("setPathAnimationPosition",{x:t.x,y:t.y,z:t.z}),window.scene.mv.tools.draw.updateEditPointPosition([t.x,t.y,t.z])},changeParams:function(t,e){("step"==e||"speed"==e)&&t<=0&&(t=.1),this.params[e]=t},togglePanel:function(){var t=window.scene.mv.tools.draw.getAllData();if(t.polyline.length){for(var e=t.polyline[0].points.length,a=[],s=0;s<e;s++)a[s]=[t.polyline[0].points[s].x,t.polyline[0].points[s].y,t.polyline[0].points[s].z];this.elementDatas.list[1].position=a}if(this.panelState=!this.panelState,this.transFormStatus){window.scene.mv.tools.transform.deactive();var i=this.$store.state.dialog.activeDialog;-1!==i.indexOf("pathAnimation")&&this.$store.commit("toggleActiveDialog","pathAnimation")}else this.$store.commit("toogleTransFormStatus",!0);""!==this.curIndex&&this.offEvent()},delPoints:function(t){if(""!==this.curIndex&&this.offEvent(),this.transFormStatus){window.scene.mv.tools.transform.deactive();var e=this.$store.state.dialog.activeDialog;-1!==e.indexOf("pathAnimation")&&this.$store.commit("toggleActiveDialog","pathAnimation")}else this.$store.commit("toogleTransFormStatus",!0);this.elementDatas.list[1].position.splice(t,1),this.changePosition()},addPoint:function(){""!==this.curIndex&&this.offEvent(),this.elementDatas.list[1].position.push([0,0,0]),this.changePosition()},offEvent:function(){this.curIndex="",window.scene.mv.status.selectable=!0,window.scene.mv.tools.transform.deactive(),clearTimeout(this.timer)},clickCheckedOffsetCoordinate:function(t,e){var a=this;this.curIndex!==t?(""!==this.curIndex&&this.offEvent(),this.$nextTick((function(){""===a.curIndex&&(a.curIndex=t,window.scene.mv.tools.draw.currentPointIndex=t,a.transFormStatus?window.scene.mv.tools.transform.deactive():a.$store.commit("toogleTransFormStatus",!0),window.scene.mv.status.selectable=!1,window.scene.mv.tools.transform.active(e,1),document.addEventListener("keyup",a.onEscKeyUp),a.timer=setTimeout((function(){window.scene.render()}),500))}))):this.offEvent()},onEscKeyUp:function(t){27==t.keyCode&&"Escape"===t.key&&this.offEvent()},onCheckedOffsetCoordinate:function(t){var e=window.scene.mv._THREE,a=window.scene.queryPosition(new e.Vector2(t.clientX,t.clientY));if(""!=a&&void 0!=a){var s=this.curIndex;this.$set(this.elementDatas.list[1].position[s],0,a.x),this.$set(this.elementDatas.list[1].position[s],1,a.y),this.$set(this.elementDatas.list[1].position[s],2,a.z),this.curIndex="",this.changePosition(),window.scene.mv.tools.transform.deactive()}else this.$message.error(this.$t("topToolBarMenu.advanced.children.pathAnimation.settings.message"));window.scene.mv.status.selectable=!0},changePosition:function(){var t=this;null!=this.inputTimeout&&clearTimeout(this.inputTimeout),this.inputTimeout=setTimeout((function(){var e=[];t.elementDatas.list[1].position.forEach((function(t,a){e[a]=t.map((function(t){var e=0;return""!=t&&(e=parseFloat(t+"")),e}))})),t.elementDatas.list[1].position=e,window.scene.mv.tools.draw.startDrawLine(t.elementDatas.list[1].position),window.scene.mv.tools.draw.editPoint(),window.scene.render(),t.inputTimeout=null}),1500)},downloadExcel:function(){var t=document.createElement("textarea");t.setAttribute("readonly","");var e=[],a=this.elementDatas.list[1].position;e=a.map((function(t){return t=t.join(","),t})),t.value=e.join(";"),t.style.position="absolute",t.style.left="-9999px",document.body.appendChild(t);var s=document.getSelection().rangeCount>0&&document.getSelection().getRangeAt(0);t.select(),document.execCommand("copy"),document.body.removeChild(t),this.$message.success(this.$t("messageTips.copySuccess1")),s&&(document.getSelection().removeAllRanges(),document.getSelection().addRange(s))},importData:function(){""!==this.curIndex&&this.offEvent(),this.showImportDialog=!0},getPosition:function(t){this.elementDatas.list[1].position=t,this.changePosition(),this.showImportDialog=!1}}},oa=la,ra=(a("a4b3"),a("638c"),Object(d["a"])(oa,ia,na,!1,null,"e0ca0650",null)),ca=ra.exports,ua=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex relative"},[a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{staticClass:"space-between-center",attrs:{span:12}},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.basePoint.label")))]),a("el-tooltip",{attrs:{effect:"dark",content:t.$t("formRelational.basePoint.placeholder"),placement:"top"}},[a("span",{staticClass:"outline-none cursor-btn",on:{click:function(e){return t.clickCheckedCoordinate()}}},[a("CommonSVG",{attrs:{color:"var(--theme)",size:"18","icon-class":"select_coord"}})],1)])],1),a("el-col",{attrs:{span:12}},[a("el-input",{class:{"is-error":t.inputError&&!t.styleFormDatas.validate.longitude},attrs:{type:"number",title:"",size:"mini",placeholder:t.$t("formRelational.longitude.placeholder")},on:{input:function(e){return t.inputChangeOrigin(e,"longitude")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v(t._s(t.$t("formRelational.longitude.label")))])]},proxy:!0}]),model:{value:t.elementDatas.longitude,callback:function(e){t.$set(t.elementDatas,"longitude",e)},expression:"elementDatas.longitude"}})],1)],1),a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{attrs:{span:12}},[a("el-input",{class:{"is-error":t.inputError&&!t.styleFormDatas.validate.latitude},attrs:{type:"number",title:"",size:"mini",placeholder:t.$t("formRelational.latitude.placeholder")},on:{input:function(e){return t.inputChangeOrigin(e,"latitude")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v(t._s(t.$t("formRelational.latitude.label")))]},proxy:!0}]),model:{value:t.elementDatas.latitude,callback:function(e){t.$set(t.elementDatas,"latitude",e)},expression:"elementDatas.latitude"}})],1),a("el-col",{attrs:{span:12}},[a("el-input",{class:{"is-error":t.inputError&&!t.styleFormDatas.validate.altitude},attrs:{type:"number",title:"",size:"mini",placeholder:t.$t("formRelational.altitude.placeholder")},on:{input:function(e){return t.inputChangeOrigin(e,"altitude")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v(t._s(t.$t("formRelational.altitude.label")))])]},proxy:!0}]),model:{value:t.elementDatas.altitude,callback:function(e){t.$set(t.elementDatas,"altitude",e)},expression:"elementDatas.altitude"}})],1)],1)],1),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-offset"},[a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{staticClass:"space-between-center",attrs:{span:12}},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.translate.label")))]),a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:t.$t("formRelational.translate.tooltip"),placement:"top"}},[a("span",{staticClass:"outline-none cursor-btn",on:{click:function(e){return t.handleModelTransform("translate")}}},[a("CommonSVG",{attrs:{color:"var(--theme)",size:"17","icon-class":"crosshair_move_feature"}})],1)])],1),a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.offset[0],expression:"elementDatas.offset[0]"}],attrs:{type:"number",title:"",size:"mini",placeholder:"X"},on:{input:function(e){return t.inputChangeOffset(e,"x")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v("X")])]},proxy:!0}]),model:{value:t.elementDatas.offset[0],callback:function(e){t.$set(t.elementDatas.offset,0,e)},expression:"elementDatas.offset[0]"}})],1)],1),a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.offset[1],expression:"elementDatas.offset[1]"}],attrs:{type:"number",title:"",size:"mini",placeholder:"Y"},on:{input:function(e){return t.inputChangeOffset(e,"y")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v("Y")]},proxy:!0}]),model:{value:t.elementDatas.offset[1],callback:function(e){t.$set(t.elementDatas.offset,1,e)},expression:"elementDatas.offset[1]"}})],1),a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.offset[2],expression:"elementDatas.offset[2]"}],attrs:{type:"number",title:"",size:"mini",placeholder:"Z"},on:{input:function(e){return t.inputChangeOffset(e,"z")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v("Z")]},proxy:!0}]),model:{value:t.elementDatas.offset[2],callback:function(e){t.$set(t.elementDatas.offset,2,e)},expression:"elementDatas.offset[2]"}})],1)],1)],1),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100",staticStyle:{width:"200px"}},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.width.label")))]),a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.params.width,expression:"params.width"}],attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("formRelational.width.placeholder")},on:{input:function(e){return t.inputChangePostData(e,"width")}},scopedSlots:t._u([{key:"append",fn:function(){return[a("span",[t._v("m")])]},proxy:!0}]),model:{value:t.params.width,callback:function(e){t.$set(t.params,"width",e)},expression:"params.width"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.top.label")))]),a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.params.height,expression:"params.height"}],attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("formRelational.top.placeholder")},on:{input:function(e){return t.inputChangePostData(e,"height")}},scopedSlots:t._u([{key:"append",fn:function(){return[a("span",[t._v("m")])]},proxy:!0}]),model:{value:t.params.height,callback:function(e){t.$set(t.params,"height",e)},expression:"params.height"}})],1)]),a("div",{staticClass:"styleSet-list-div h100",staticStyle:{width:"200px"}},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("featureSetting.style.waterFall.label"))+" ")]),a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.params.depth,expression:"params.depth"}],attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("featureSetting.style.waterFall.placeholder")},on:{input:function(e){return t.inputChangePostData(e,"depth")}},scopedSlots:t._u([{key:"append",fn:function(){return[a("span",[t._v("m")])]},proxy:!0}]),model:{value:t.params.depth,callback:function(e){t.$set(t.params,"depth",e)},expression:"params.depth"}})],1),a("div",{staticClass:"items-center",staticStyle:{height:"24px"}})])])},ma=[],da={name:"waterfall",props:["currentElement","elementTransformObj"],mixins:[g["a"]],data:function(){return{itemGutter:10,elementDatas:{id:"",longitude:0,latitude:0,altitude:0,rotation:[0,0,0],offset:[0,0,0],list:[]},params:{width:1,height:1,depth:1}}},created:function(){this.init()},methods:{init:function(){var t=window.scene.features.get(this.currentElement.id);this.elementDatas.id=this.currentElement.id,this.elementDatas.longitude=t.origin[0],this.elementDatas.latitude=t.origin[1],this.elementDatas.altitude=t.altitude,this.elementDatas.offset=t.offset,this.params.width=t.data.width,this.params.height=t.data.height,this.params.depth=t.data.depth},togglePanel:function(){this.elementDatas.list[1].panelState=!this.elementDatas.list[1].panelState},inputChangePostData:function(t,e){var a=window.scene.features.get(this.currentElement.id),s={};s[e]=parseFloat(t+""),window.scene.postData(s,a.dataKey)}}},pa=da,ha=(a("c75f"),Object(d["a"])(pa,ua,ma,!1,null,"50f966d7",null)),fa=ha.exports,va=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex"},[a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("featureSetting.style.projector.label"))+" ")]),a("el-input",{class:{"is-error":t.inputError&&""==t.elementDatas.list[0].value},attrs:{size:"mini",placeholder:t.$t("featureSetting.style.projector.label"),readonly:""},on:{input:function(e){return t.inputProjectorPostData("url")}},model:{value:t.elementDatas.list[0].value,callback:function(e){t.$set(t.elementDatas.list[0],"value",e)},expression:"elementDatas.list[0].value"}})],1)]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-offset"},[a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{staticClass:"space-between-center",attrs:{span:12}},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("featureSetting.style.projector.label1"))+" ")]),a("el-tooltip",{attrs:{effect:"dark",content:t.$t("featureSetting.style.projector.tooltip"),placement:"top"}},[a("span",{staticClass:"outline-none cursor-btn",on:{click:function(e){return t.handleDragTransform("translate")}}},[a("CommonSVG",{attrs:{color:"var(--theme)",size:"18","icon-class":"crosshair_move_feature"}})],1)])],1),a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"halfAdjust",rawName:"v-halfAdjust",value:t.params.position[0],expression:"params.position[0]"}],attrs:{type:"number",title:"",size:"mini",placeholder:"X",readonly:""!==t.transformChange},on:{input:function(e){return t.inputProjectorPostData("position","input")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v("X")])]},proxy:!0}]),model:{value:t.params.position[0],callback:function(e){t.$set(t.params.position,0,e)},expression:"params.position[0]"}})],1)],1),a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"halfAdjust",rawName:"v-halfAdjust",value:t.params.position[1],expression:"params.position[1]"}],attrs:{type:"number",title:"",size:"mini",placeholder:"Y",readonly:""!==t.transformChange},on:{input:function(e){return t.inputProjectorPostData("position","input")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v("Y")]},proxy:!0}]),model:{value:t.params.position[1],callback:function(e){t.$set(t.params.position,1,e)},expression:"params.position[1]"}})],1),a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"halfAdjust",rawName:"v-halfAdjust",value:t.params.position[2],expression:"params.position[2]"}],attrs:{type:"number",title:"",size:"mini",placeholder:"Z",readonly:""!==t.transformChange},on:{input:function(e){return t.inputProjectorPostData("position","input")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v("Z")]},proxy:!0}]),model:{value:t.params.position[2],callback:function(e){t.$set(t.params.position,2,e)},expression:"params.position[2]"}})],1)],1)],1),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-rotation"},[a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{staticClass:"space-between-center",attrs:{span:12}},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("featureSetting.style.projector.label2"))+" ")]),a("el-tooltip",{attrs:{effect:"dark",content:t.$t("featureSetting.style.projector.tooltip1"),placement:"top"}},[a("span",{staticClass:"outline-none cursor-btn",on:{click:function(e){return t.handleDragTransform("rotate")}}},[a("CommonSVG",{attrs:{color:"var(--theme)",size:"18","icon-class":"rotating_feature"}})],1)])],1),a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"halfAdjust",rawName:"v-halfAdjust",value:t.params.rotation[0],expression:"params.rotation[0]"}],attrs:{title:"",type:"number",size:"mini",readonly:""!==t.transformChange,placeholder:t.$t("formRelational.rotation.placeholderX")},on:{input:function(e){return t.inputProjectorPostData("rotation")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v(t._s(t.$t("formRelational.rotation.x")))]},proxy:!0}]),model:{value:t.params.rotation[0],callback:function(e){t.$set(t.params.rotation,0,e)},expression:"params.rotation[0]"}})],1)],1),a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"halfAdjust",rawName:"v-halfAdjust",value:t.params.rotation[1],expression:"params.rotation[1]"}],attrs:{title:"",type:"number",size:"mini",readonly:""!==t.transformChange,placeholder:t.$t("formRelational.rotation.placeholderY")},on:{input:function(e){return t.inputProjectorPostData("rotation")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v(t._s(t.$t("formRelational.rotation.y")))]},proxy:!0}]),model:{value:t.params.rotation[1],callback:function(e){t.$set(t.params.rotation,1,e)},expression:"params.rotation[1]"}})],1),a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"halfAdjust",rawName:"v-halfAdjust",value:t.params.rotation[2],expression:"params.rotation[2]"}],attrs:{title:"",type:"number",size:"mini",readonly:""!==t.transformChange,placeholder:t.$t("formRelational.rotation.placeholderZ")},on:{input:function(e){return t.inputProjectorPostData("rotation")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v(t._s(t.$t("formRelational.rotation.z")))]},proxy:!0}]),model:{value:t.params.rotation[2],callback:function(e){t.$set(t.params.rotation,2,e)},expression:"params.rotation[2]"}})],1)],1)],1),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-160"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title",staticStyle:{"min-width":"95px"}},[t._v(" "+t._s(t.$t("featureSetting.style.projector.label3"))+" ")]),a("el-input",{attrs:{size:"mini",type:"number",placeholder:t.$t("featureSetting.style.projector.label3"),title:""},on:{input:function(e){return t.inputProjectorPostData("fov")}},model:{value:t.params.fov,callback:function(e){t.$set(t.params,"fov",e)},expression:"params.fov"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title",staticStyle:{"min-width":"95px"}},[t._v(" "+t._s(t.$t("featureSetting.style.projector.label4"))+" "),a("el-tooltip",{staticClass:"box-item",attrs:{enterable:!1,effect:"dark",content:t.$t("featureSetting.style.projector.tooltip2"),placement:"top"}},[a("i",{staticClass:"el-icon-question"})])],1),a("el-input",{directives:[{name:"halfAdjust",rawName:"v-halfAdjust",value:t.params.aspect,expression:"params.aspect"}],attrs:{size:"mini",type:"number",placeholder:t.$t("featureSetting.style.projector.label4"),title:""},on:{input:function(e){return t.inputProjectorPostData("aspect")}},model:{value:t.params.aspect,callback:function(e){t.$set(t.params,"aspect",e)},expression:"params.aspect"}})],1)]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-160"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("featureSetting.style.projector.label5"))+" ")]),a("el-input",{attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("featureSetting.style.projector.label5")},on:{input:function(e){return t.inputProjectorPostData("scale")}},model:{value:t.params.scale,callback:function(e){t.$set(t.params,"scale",e)},expression:"params.scale"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(" "+t._s(t.$t("featureSetting.style.projector.label6"))+" ")]),a("el-checkbox",{staticStyle:{"margin-left":"12px"},attrs:{size:"small"},on:{change:function(e){return t.inputProjectorPostData("helper")}},model:{value:t.params.helper,callback:function(e){t.$set(t.params,"helper",e)},expression:"params.helper"}})],1)]),a("div",{staticClass:"after-lineX"})])},ga=[],ya={name:"Projector",props:["currentElement"],components:{CommonSVG:function(){return a.e("chunk-51f90655").then(a.bind(null,"17d0"))}},data:function(){return{itemGutter:10,params:{elementIDs:[],position:[0,0,0],rotation:[0,0,0],fov:30,aspect:16/9,scale:1,helper:!0,video:!0},elementDatas:{list:[{title:this.$t("featureSetting.style.projector.label"),value:"",validate:!0,optionState:!0}]},dataKey:"",anchorID:"projector-annotation",transformChange:""}},created:function(){this.init(),this.inputProjectorPostData=T()(this.inputProjectorPostData,1e3)},methods:{init:function(){var t=window.scene.features.get(this.currentElement.id);for(var e in t.data)this.params[e]=t.data[e];this.elementDatas.list[0].value=t.url,this.dataKey=t.dataKey,window.scene.features.has("projector-annotation")||this.addTemporaryAnnotationFeature(),window.scene.clearSelection(),window.scene.render()},inputProjectorPostData:function(t,e){if("url"==t)window.scene.features.get(this.currentElement.id).url=this.elementDatas.list[0].value;else{"position"==t&&(this.params[t]=this.params[t].map((function(t){return parseFloat(t)})),"input"==e&&this.handleSynchronizeAnchorPosition()),"rotation"==t&&(this.params[t]=this.params[t].map((function(t){return parseFloat(t)})));var a=["fov","aspect","scale"];a.includes(t)&&(this.params[t]=parseFloat(this.params[t]),this.params[t]<=0&&(this.params[t]=.1));var s={};s[t]=this.params[t],window.scene.postData(s,this.dataKey)}},handleSynchronizeAnchorPosition:function(){var t=window.scene.mv.tools.coordinate.vector2mercator(this.params.position),e=window.scene.features.get(this.anchorID);e.origin=[t[0],t[1]],e.altitude=t[2],e.offset=[0,0,0]},handleDragTransform:function(t){var e=this;""!=this.transformChange&&this.transformChange==t?this.offHandleDragTransform():""!=this.transformChange&&this.transformChange!=t?(this.transformChange=t,window.scene.mv.tools.transform.currentMode=t):setTimeout((function(){var a=window.scene.features.get(e.anchorID);e.handleSynchronizeAnchorPosition(),a.visible=!0,e.transformChange=t,window.scene.mv.tools.transform.currentMode=t,window.scene.mv.tools.transform.active(a),window.scene.mv.events.transformChange.on("default",e.onProjectorTransformChange)}),300)},addTemporaryAnnotationFeature:function(){var t=window.scene.mv.tools.coordinate.vector2mercator(this.params.position),e={position:{x:0,y:0,z:0},content:"",htmlCode:'<div class="triggerScatterPlot"></div>',cssCode:"",jsCode:"",minDistance:0,maxDistance:1/0},a=window.scene.addFeature("annotation",this.anchorID);a.origin=[t[0],t[1]],a.altitude=t[2],a.offset=[0,0,0],a.rotation=this.params.rotation,a.dataKey="annotation-"+a.id,a.visible=!1,window.scene.postData(e,a.dataKey),a.load()},onProjectorTransformChange:function(t,e){if(t&&(this.params.position=Object.values(t),this.inputProjectorPostData("position")),e){var a=window.scene.features.get(this.anchorID).rotation;this.$set(this.params.rotation,0,a[0]),this.$set(this.params.rotation,1,a[1]),this.$set(this.params.rotation,2,a[2]),this.inputProjectorPostData("rotation")}},offHandleDragTransform:function(){window.scene.mv.tools.transform.deactive(),window.scene.mv.events.transformChange.off("default",this.onProjectorTransformChange),this.transformChange="",window.scene.features.get(this.anchorID).visible=!1,window.scene.render(),setTimeout((function(){window.scene.render()}),1e3)}},beforeDestroy:function(){""!=this.transformChange&&this.offHandleDragTransform(),""!=this.anchorID&&window.scene.features.has(this.anchorID)&&window.scene.features.get(this.anchorID).dispose()}},Ca=ya,Da=(a("92b1"),Object(d["a"])(Ca,va,ga,!1,null,"4e74ab72",null)),ba=Da.exports,wa=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex"},[a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{staticClass:"space-between-center",attrs:{span:12}},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.basePoint.label")))]),a("el-tooltip",{attrs:{effect:"dark",content:t.$t("formRelational.basePoint.placeholder"),placement:"top"}},[a("span",{staticClass:"outline-none cursor-btn",on:{click:function(e){return t.clickCheckedCoordinate()}}},[a("CommonSVG",{attrs:{color:"var(--theme)",size:"18","icon-class":"select_coord"}})],1)])],1),a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.longitude,expression:"elementDatas.longitude"}],class:{"is-error":t.inputError&&!t.styleFormDatas.validate.longitude},attrs:{type:"number",title:"",size:"mini",placeholder:t.$t("formRelational.longitude.placeholder")},on:{input:function(e){return t.inputChangeOrigin(e,"longitude")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v(t._s(t.$t("formRelational.longitude.label")))])]},proxy:!0}]),model:{value:t.elementDatas.longitude,callback:function(e){t.$set(t.elementDatas,"longitude",e)},expression:"elementDatas.longitude"}})],1)],1),a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.latitude,expression:"elementDatas.latitude"}],class:{"is-error":t.inputError&&!t.styleFormDatas.validate.latitude},attrs:{type:"number",title:"",size:"mini",placeholder:t.$t("formRelational.latitude.placeholder")},on:{input:function(e){return t.inputChangeOrigin(e,"latitude")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v(t._s(t.$t("formRelational.latitude.label")))]},proxy:!0}]),model:{value:t.elementDatas.latitude,callback:function(e){t.$set(t.elementDatas,"latitude",e)},expression:"elementDatas.latitude"}})],1),a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.altitude,expression:"elementDatas.altitude"}],class:{"is-error":t.inputError&&!t.styleFormDatas.validate.altitude},attrs:{type:"number",title:"",size:"mini",placeholder:t.$t("formRelational.altitude.placeholder")},on:{input:function(e){return t.inputChangeOrigin(e,"altitude")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v(t._s(t.$t("formRelational.altitude.label")))])]},proxy:!0}]),model:{value:t.elementDatas.altitude,callback:function(e){t.$set(t.elementDatas,"altitude",e)},expression:"elementDatas.altitude"}})],1)],1)],1),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-offset"},[a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{staticClass:"space-between-center",attrs:{span:12}},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.translate.label")))]),a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",content:t.$t("formRelational.translate.tooltip"),placement:"top"}},[a("span",{staticClass:"outline-none cursor-btn",on:{click:function(e){return t.handleModelTransform("translate")}}},[a("CommonSVG",{attrs:{color:"var(--theme)",size:"17","icon-class":"crosshair_move_feature"}})],1)])],1),a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.offset[0],expression:"elementDatas.offset[0]"}],attrs:{type:"number",title:"",size:"mini",placeholder:"X"},on:{input:function(e){return t.inputChangeOffset(e,"x")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v("X")])]},proxy:!0}]),model:{value:t.elementDatas.offset[0],callback:function(e){t.$set(t.elementDatas.offset,0,e)},expression:"elementDatas.offset[0]"}})],1)],1),a("el-row",{staticClass:"items-center",attrs:{gutter:t.itemGutter}},[a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.offset[1],expression:"elementDatas.offset[1]"}],attrs:{type:"number",title:"",size:"mini",placeholder:"Y"},on:{input:function(e){return t.inputChangeOffset(e,"y")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v("Y")]},proxy:!0}]),model:{value:t.elementDatas.offset[1],callback:function(e){t.$set(t.elementDatas.offset,1,e)},expression:"elementDatas.offset[1]"}})],1),a("el-col",{attrs:{span:12}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.elementDatas.offset[2],expression:"elementDatas.offset[2]"}],attrs:{type:"number",title:"",size:"mini",placeholder:"Z"},on:{input:function(e){return t.inputChangeOffset(e,"z")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v("Z")]},proxy:!0}]),model:{value:t.elementDatas.offset[2],callback:function(e){t.$set(t.elementDatas.offset,2,e)},expression:"elementDatas.offset[2]"}})],1)],1)],1),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.width.label")))]),a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.params.width,expression:"params.width"}],attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("formRelational.width.placeholder")},on:{input:function(e){return t.inputChangePostData(e,"width")}},scopedSlots:t._u([{key:"append",fn:function(){return[a("span",[t._v("m")])]},proxy:!0}]),model:{value:t.params.width,callback:function(e){t.$set(t.params,"width",e)},expression:"params.width"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.top.label")))]),a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:t.params.height,expression:"params.height"}],attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("formRelational.top.placeholder")},on:{input:function(e){return t.inputChangePostData(e,"height")}},scopedSlots:t._u([{key:"append",fn:function(){return[a("span",[t._v("m")])]},proxy:!0}]),model:{value:t.params.height,callback:function(e){t.$set(t.params,"height",e)},expression:"params.height"}})],1)]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-200"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.speed.label")))]),a("div",{staticClass:"items-center w100"},[a("el-slider",{staticStyle:{width:"100%"},attrs:{min:.5,max:5,step:.5},on:{change:function(e){return t.inputChangePostData(e,"speed")}},model:{value:t.params.speed,callback:function(e){t.$set(t.params,"speed",e)},expression:"params.speed"}}),a("span",{staticClass:"slider-num"},[t._v(t._s(t.params.speed))])],1)]),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.density.label")))]),a("div",{staticClass:"items-center w100"},[a("el-slider",{staticStyle:{width:"100%"},attrs:{min:.5,max:5,step:.5},on:{change:function(e){return t.inputChangePostData(e,"density")}},model:{value:t.params.density,callback:function(e){t.$set(t.params,"density",e)},expression:"params.density"}}),a("span",{staticClass:"slider-num"},[t._v(t._s(t.params.density))])],1)])]),a("div",{staticClass:"after-lineX"})])},_a=[],xa={name:"SnowSetting",props:["currentElement"],mixins:[g["a"]],data:function(){return{itemGutter:10,elementDatas:{id:"",longitude:0,latitude:0,altitude:0,offset:[0,0,0],list:[]},params:{width:200,height:200,speed:5,density:5}}},created:function(){this.inputChangePostData=T()(this.inputChangePostData,800),this.init()},methods:{init:function(){var t=window.scene.features.get(this.currentElement.id);this.elementDatas.id=this.currentElement.id,this.elementDatas.longitude=t.origin[0],this.elementDatas.latitude=t.origin[1],this.elementDatas.altitude=t.altitude,this.elementDatas.offset=t.offset,this.params.width=t.data.width,this.params.height=t.data.height,this.params.speed=t.data.speed,this.params.density=t.data.density},inputChangePostData:function(t,e){var a=window.scene.features.get(this.currentElement.id),s={};s[e]=parseFloat(t+""),("width"==e||"height"==e)&&s[e]<=0&&(s[e]=1,this.params[e]=1),("width"==e||"height"==e)&&s[e]>1e3&&(s[e]=1e3,this.params[e]=1e3),window.scene.postData(s,a.dataKey)}}},ka=xa,$a=(a("20be"),Object(d["a"])(ka,wa,_a,!1,null,"961233f0",null)),Sa=$a.exports,Pa=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex relative w100"},[a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.elementDatas.list[1].title))]),a("div",{staticClass:"popup-item-center items-center",on:{click:t.togglePanel}},[a("CommonSVG",{attrs:{"icon-class":"xyz",size:16}}),a("span",{staticClass:"margin-left-5"},[t._v(t._s(t.elementDatas.list[1].title))])],1)])]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100 list-div-200"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.color.label")))]),a("el-color-picker",{staticStyle:{"margin-right":"12px"},attrs:{"popper-class":"colorPicker","show-alpha":!1,"color-format":"rgb",size:"mini"},on:{change:function(e){return t.inputChangeTaillinePostData(e,"color")}},model:{value:t.params.color,callback:function(e){t.$set(t.params,"color",e)},expression:"params.color"}})],1),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.speed.label")))]),a("div",{staticClass:"items-center w100"},[a("el-slider",{staticStyle:{width:"100%"},attrs:{min:1,max:10,step:1},on:{change:function(e){return t.inputChangeTaillinePostData(e,"speed")}},model:{value:t.params.speed,callback:function(e){t.$set(t.params,"speed",e)},expression:"params.speed"}}),a("span",{staticClass:"slider-num"},[t._v(t._s(t.params.speed))])],1)])]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100",staticStyle:{width:"200px"}},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("featureSetting.style.tailline.label")))]),a("div",{staticClass:"items-center w100"},[a("el-switch",{staticStyle:{"margin-left":"12px"},on:{change:function(e){return t.inputChangeTaillinePostData(e,"reverse")}},model:{value:t.params.reverse,callback:function(e){t.$set(t.params,"reverse",e)},expression:"params.reverse"}}),a("div",{staticClass:"slider-num"},[t._v(t._s(t.params.reverse?"反向":"正向"))])],1)]),a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.$t("formRelational.lineWidth.label")))]),a("el-input",{attrs:{size:"mini",type:"number",title:"",placeholder:t.$t("formRelational.lineWidth.placeholder")},on:{input:function(e){return t.inputChangeTaillinePostData(e,"lineWidth")}},scopedSlots:t._u([{key:"append",fn:function(){return[a("span",[t._v("m")])]},proxy:!0}]),model:{value:t.params.lineWidth,callback:function(e){t.$set(t.params,"lineWidth",e)},expression:"params.lineWidth"}},[a("template",{slot:"suffix"},[a("el-tooltip",{staticClass:"box-item",attrs:{effect:"dark",placement:"top",enterable:!1}},[a("div",{attrs:{slot:"content"},domProps:{innerHTML:t._s(t.$t("featureSetting.style.tailline.tooltip"))},slot:"content"}),a("i",{staticClass:"outline-none el-input__icon el-icon-question"})])],1)],2)],1)]),a("div",{staticClass:"after-lineX"}),a("div",{staticClass:"styleSet-list-div h100"},[a("div",{staticClass:"items-center"},[a("span",{staticClass:"item-title"},[t._v(t._s(t.elementDatas.list[0].title))]),a("el-input",{attrs:{size:"mini",placeholder:t.$t("formRelational.image.placeholder")},on:{input:function(e){return t.inputChangeTaillinePostData(e,"tailMap")}},model:{value:t.elementDatas.list[0].value,callback:function(e){t.$set(t.elementDatas.list[0],"value",e)},expression:"elementDatas.list[0].value"}}),a("el-tooltip",{attrs:{enterable:!1,effect:"dark",content:t.$t("formRelational.image.label"),placement:"top"}},[a("span",{staticClass:"thumb-box",on:{click:t.openThumb}},[a("CommonSVG",{attrs:{size:16,"icon-class":"thumbnail"}})],1)])],1),t.elementDatas.list[0].thumbState?a("div",{staticClass:"thumb-dialog"},[a("div",{staticClass:"title"},[a("span",[t._v(t._s(t.$t("formRelational.image.label")))]),a("div",[a("i",{staticClass:"el-icon-close cursor-btn",on:{click:t.openThumb}})])]),a("div",{staticClass:"thumb-content"},t._l(t.elementDatas.list[0].thumbList,(function(e){return a("div",{key:e.url,staticClass:"item",class:{active:e.img===t.elementDatas.list[0].value},on:{click:function(a){return t.checkPolylineImg(e)}}},[a("div",{staticClass:"img-box"},[a("img",{attrs:{src:e.img}})])])})),0)]):t._e()]),a("div",{staticClass:"after-lineX"}),t.elementDatas.list[1].panelState?a("div",{staticClass:"regional-coordinates-container reginoal-dialog"},[a("div",{staticClass:"title"},[a("span",[t._v(t._s(t.elementDatas.list[1].title))]),a("div",[a("span",{staticClass:"downloadExcel",on:{click:t.downloadExcel}},[t._v(t._s(t.$t("others.export")))]),a("span",{staticClass:"downloadExcel",on:{click:t.importData}},[t._v(t._s(t.$t("others.import")))]),a("i",{staticClass:"el-icon-plus cursor-btn margin-right-8",on:{click:function(e){return t.addPoint()}}}),a("i",{staticClass:"el-icon-close cursor-btn",on:{click:t.togglePanel}})])]),a("div",{staticClass:"position-list-wrap"},t._l(t.elementDatas.list[1].position,(function(e,s){return a("div",{key:s,staticClass:"position-list"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:e[0],expression:"pos[0]"}],attrs:{type:"number",title:"",size:"mini",placeholder:"X"},on:{input:function(e){return t.inputChangeTaillinePostData(e,"positions")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[a("span",[t._v("X")])]},proxy:!0}],null,!0),model:{value:e[0],callback:function(a){t.$set(e,0,a)},expression:"pos[0]"}})],1),a("el-col",{attrs:{span:8}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:e[1],expression:"pos[1]"}],attrs:{type:"number",title:"",size:"mini",placeholder:"Y"},on:{input:function(e){return t.inputChangeTaillinePostData(e,"positions")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v("Y")]},proxy:!0}],null,!0),model:{value:e[1],callback:function(a){t.$set(e,1,a)},expression:"pos[1]"}})],1),a("el-col",{attrs:{span:8}},[a("el-input",{directives:[{name:"half-adjust",rawName:"v-half-adjust",value:e[2],expression:"pos[2]"}],attrs:{type:"number",title:"",size:"mini",placeholder:"Z"},on:{input:function(e){return t.inputChangeTaillinePostData(e,"positions")}},scopedSlots:t._u([{key:"prepend",fn:function(){return[t._v("Z")]},proxy:!0}],null,!0),model:{value:e[2],callback:function(a){t.$set(e,2,a)},expression:"pos[2]"}})],1)],1),a("div",{staticClass:"delete-btn"},[a("span",{staticClass:"coord-box",on:{click:function(a){return t.clickCheckedOffsetCoordinate(s,e)}}},[a("CommonSVG",{attrs:{color:t.curIndex===s?"var(--theme)":"#FFFFFF",size:"16","icon-class":"amend_feature"}})],1),t.elementDatas.list[1].position.length>2?a("i",{staticClass:"el-icon el-icon-remove-outline icons",on:{click:function(e){return t.delPoints(s)}}}):t._e()])],1)})),0)]):t._e(),t.showImportDialog?a("CoordinateImport",{on:{close:function(e){t.showImportDialog=!1},confirm:t.getPosition}}):t._e()],1)},Ea=[],Ra={name:"TailLineSetting",props:["currentElement"],components:{CommonSVG:function(){return a.e("chunk-51f90655").then(a.bind(null,"17d0"))},CoordinateImport:Bt["a"]},data:function(){return{showImportDialog:!1,curIndex:"",elementDatas:{id:"",longitude:0,latitude:0,altitude:0,rotation:[0,0,0],offset:[0,0,0],list:[{title:this.$t("formRelational.image.label1"),value:"",optionState:!0,validateImg:!0,validateState:null,thumbState:!1,thumbList:[]},{title:this.$t("dialog.coordinate.name"),position:[[0,0,0],[0,0,0]],panelState:!1,addOption:!0,optionItem:{name:"position",value:[0,0,0]}}]},params:{positions:[],color:"rgba(255, 50, 0)",tailMap:"http://example.vothing.com/img/trail.png",speed:1,reverse:!1,lineWidth:3}}},created:function(){this.init(),this.getPublicImg(),this.inputChangeTaillinePostData=T()(this.inputChangeTaillinePostData,800)},methods:{init:function(){var t=window.scene.features.get(this.currentElement.id);this.elementDatas.id=this.currentElement.id,t.data.tailMap&&""!==t.data.tailMap&&(this.elementDatas.list[0].value=t.data.tailMap),this.elementDatas.list[1].position=t.data.positions,this.params.positions=t.data.positions,this.params.color=t.data.color,this.params.tailMap=t.data.tailMap,this.params.speed=t.data.speed,this.params.reverse=t.data.reverse,this.params.lineWidth=t.data.lineWidth},getPublicImg:function(){var t=this,e=a("b60a");e.keys().length>0&&e.keys().forEach((function(e,a){var s=e.replace(/(.*\/)*([\s\S]*?)/gi,"$2"),i=(s.split(".")[0],"./image/tailline/".concat(s));t.elementDatas.list[0].thumbList.push({name:s,img:i})}))},inputChangeTaillinePostData:function(t,e){var a=window.scene.features.get(this.currentElement.id),s={},i=[];switch(e){case"speed":t=parseFloat(t+"");break;case"lineWidth":var n=parseFloat(t+"");n<0&&(n=1),n>5&&(n=5),t=n,this.params.lineWidth=t;break;case"positions":this.elementDatas.list[1].position.forEach((function(t,e){i[e]=t.map((function(t){var e=0;return""!=t&&(e=parseFloat(t+"")),e}))})),this.elementDatas.list[1].position=i,this.elementDatas.list[1].panelState&&(window.scene.mv.tools.draw.startDrawLine(this.elementDatas.list[1].position),window.scene.mv.tools.draw.editPoint());break}"positions"==e?s.positions=i:s[e]=t,window.scene.postData(s,a.dataKey),window.scene.render()},checkPolylineImg:function(t){var e="";this.elementDatas.list[0].value!=t.img&&(e=t.img),this.elementDatas.list[0].value=e,this.inputChangeTaillinePostData(e,"tailMap")},togglePanel:function(){var t=this,e=window.scene.mv.tools.draw.getAllData();if(e.polyline.length){for(var a=e.polyline[0].points.length,s=[],i=0;i<a;i++)s[i]=[e.polyline[0].points[i].x,e.polyline[0].points[i].y,e.polyline[0].points[i].z];this.elementDatas.list[1].position=s}this.elementDatas.list[1].panelState=!this.elementDatas.list[1].panelState;var n=window.scene.features.get(this.currentElement.id);if(this.elementDatas.list[1].panelState)n.visible=!1,window.scene.mv.tools.draw.active(),window.scene.mv.tools.draw.startDrawLine(this.elementDatas.list[1].position),window.scene.mv.tools.draw.editPoint(),this.timeout=setTimeout((function(){window.scene.render(),window.scene.mv.events.keyPointChange.on("change",t.keyPointChangeHandle),window.scene.mv.events.transformChange.on("default",t.transformChangeHandle)}),500);else{n.visible=!0,window.scene.mv.tools.transform.deactive(),window.scene.mv.tools.draw.deactive();var l=this.$store.state.dialog.activeDialog;-1!==l.indexOf("pathAnimation")&&this.$store.commit("toggleActiveDialog","pathAnimation"),window.scene.mv.events.keyPointChange.off("change",this.keyPointChangeHandle),window.scene.mv.events.transformChange.off("default",this.transformChangeHandle),window.scene.render()}if(this.transFormStatus){window.scene.mv.tools.transform.deactive();var o=this.$store.state.dialog.activeDialog;-1!==o.indexOf("pathAnimation")&&this.$store.commit("toggleActiveDialog","pathAnimation")}else this.$store.commit("toogleTransFormStatus",!0);""!==this.curIndex&&this.offEvent()},delPoints:function(t){if(""!==this.curIndex&&this.offEvent(),this.transFormStatus){window.scene.mv.tools.transform.deactive();var e=this.$store.state.dialog.activeDialog;-1!==e.indexOf("pathAnimation")&&this.$store.commit("toggleActiveDialog","pathAnimation")}else this.$store.commit("toogleTransFormStatus",!0);this.elementDatas.list[1].position.splice(t,1),this.inputChangeTaillinePostData("","positions")},addPoint:function(){""!==this.curIndex&&this.offEvent(),this.elementDatas.list[1].position.push([0,0,0]),this.inputChangeTaillinePostData("","positions")},openThumb:function(){this.elementDatas.list[0].thumbState=!this.elementDatas.list[0].thumbState},clickCheckedOffsetCoordinate:function(t,e){var a=this;this.curIndex!==t?(""!==this.curIndex&&this.offEvent(),this.$nextTick((function(){""===a.curIndex&&(a.curIndex=t,a.transFormStatus?window.scene.mv.tools.transform.deactive():a.$store.commit("toogleTransFormStatus",!0),window.scene.mv.status.selectable=!1,window.scene.mv.tools.draw.currentPointIndex=t,window.scene.mv.tools.transform.active(e,1),document.addEventListener("keyup",a.onEscKeyUp),a.timer=setTimeout((function(){window.scene.render()}),500))}))):this.offEvent()},onEscKeyUp:function(t){27==t.keyCode&&"Escape"===t.key&&this.offEvent()},offEvent:function(){this.curIndex="",window.scene.mv.status.selectable=!0,window.scene.mv.tools.transform.deactive()},onCheckedOffsetCoordinate:function(t){var e=window.scene.mv._THREE,a=window.scene.queryPosition(new e.Vector2(t.clientX,t.clientY));if(""!=a&&void 0!=a){var s=this.curIndex;this.$set(this.elementDatas.list[1].position[s],0,a.x),this.$set(this.elementDatas.list[1].position[s],1,a.y),this.$set(this.elementDatas.list[1].position[s],2,a.z),this.curIndex="",this.inputChangeTaillinePostData("","positions"),window.scene.mv.tools.transform.deactive()}else this.$message.error(this.$t("messageTips.errorCheckCoordinate"));window.scene.mv.status.selectable=!0},downloadExcel:function(){""!==this.curIndex&&this.offEvent();var t=document.createElement("textarea");t.setAttribute("readonly","");var e=[],a=this.elementDatas.list[1].position;e=a.map((function(t){return t=t.join(","),t})),t.value=e.join(";"),t.style.position="absolute",t.style.left="-9999px",document.body.appendChild(t);var s=document.getSelection().rangeCount>0&&document.getSelection().getRangeAt(0);t.select(),document.execCommand("copy"),document.body.removeChild(t),this.$message.success(this.$t("messageTips.copySuccess1")),s&&(document.getSelection().removeAllRanges(),document.getSelection().addRange(s))},importData:function(){""!==this.curIndex&&this.offEvent(),this.showImportDialog=!0},getPosition:function(t){this.elementDatas.list[1].position=t,this.inputChangeTaillinePostData("","positions"),this.showImportDialog=!1},keyPointChangeHandle:function(t){this.$store.commit("toogleEditPathAnimation",!0),this.transFormStatus?window.scene.mv.tools.transform.deactive():this.$store.commit("toogleTransFormStatus",!0),this.curIndex=window.scene.mv.tools.draw.currentPointIndex,window.scene.mv.tools.transform.active([t.x,t.y,t.z],1),this.$store.commit("setPathAnimationPosition",{x:t.x,y:t.y,z:t.z}),window.scene.mv.tools.draw.currentPointMesh.visible=!0},transformChangeHandle:function(t){if(t){var e=window.scene.mv.tools.draw.currentPointIndex;this.$set(this.elementDatas.list[1].position,e,[t.x,t.y,t.z]),window.scene.mv.tools.draw.updateEditPointPosition([t.x,t.y,t.z]),this.$store.commit("setPathAnimationPosition",{x:t.x,y:t.y,z:t.z}),this.inputChangeTaillinePostData("","positions")}}},beforeDestroy:function(){var t=window.scene.features.get(this.currentElement.id),e=this.$store.state.pathAnimation.editPathAnimation;e&&this.$store.commit("toogleEditPathAnimation",!1),window.scene.mv.tools.draw.deactive(),window.scene.mv.tools.transform.deactive();var a=this.$store.state.dialog.activeDialog;-1!==a.indexOf("pathAnimation")&&this.$store.commit("toggleActiveDialog","pathAnimation"),this.$store.commit("toogleTransFormStatus",!1),t&&(t.visible=!0),window.scene.mv.events.keyPointChange.off("change",this.keyPointChangeHandle),window.scene.mv.events.transformChange.off("default",this.transformChangeHandle),""!==this.curIndex&&this.offEvent()}},Ta=Ra,Oa=(a("fa71"),Object(d["a"])(Ta,Pa,Ea,!1,null,"5acdc120",null)),ja=Oa.exports,za=null,Fa={name:"SceneStyleSet",mixins:[l["a"],o["a"]],components:{ElementImageSourceDialog:h,ModelSetting:b,DemSetting:S,BuildingSetting:F,GltfFbxSetting:X,TilesSetting:Y,PanoramaSetting:Q,VideoSetting:nt,PolygonSetting:mt,RipplewallSetting:gt,RadarSetting:_t,ShieldSetting:Et,RingSetting:Ft,SmokeFlameSetting:Xt,PolylineSetting:Wt,VectorExtrudeSetting:te,WmtsWmsTmsSetting:le,ShpSetting:de,KmlSetting:ye,GeoJSONSetting:xe,HeatmapSetting:Te,BatchExtrudeSetting:Ae,AnchorPointSetting:Be,advancedEnvmap:qe,advancedSetting:sa,PathAnimation:ca,waterFall:fa,Projector:ba,SnowSetting:Sa,TailLineSetting:ja,CommonSVG:function(){return a.e("chunk-51f90655").then(a.bind(null,"17d0"))}},computed:{isVothing:function(){return this.$store.state.menuList.isVothing},list:function(){return this.$store.state.widget.widgetItem},dragData:function(){return this.$store.state.scene.dragOverData},styleSetList:function(){var t=this.dragData.type,e=this.styleFormDatas.datas[t].list.filter((function(t){return t.optionState}));return e}},data:function(){return{listState:[],setPublicTranslation:!1,inputError:!1,defaultImg:a("922e"),color:"rgba(19, 206, 102, 0.8)",customIDType:["_3dTiles","wmts","wms","tms"],needDisbleNameInputType:["model","underlay","skybox"],elementTransformObj:"",elementSourceDialogState:""}},mounted:function(){this.initData()},methods:{checkedElementSource:function(t){var e=this.dragData;switch(e.type){case"polygon":var a=window.location.origin+Object(n["g"])()+t.thumb;this.styleFormDatas.datas.polygon.list[1].value=a;break;case"gltf":case"fbx":var s=window.IP_CONFIG.SOURCES_URL+"sceneSources"+t.fileSrc;this.styleFormDatas.datas[e.type].list[0].value=s;break}},toggleSourceDialog:function(){var t=this.dragData.type;"polygon"==t&&(t=this.dragData.subType),this.elementSourceDialogState=t},setSkyBoxName:function(t){var e=this.styleFormDatas.datas["skybox"].list[0].skyboxOptions.find((function(e){return e.value==t}));this.styleFormDatas.name=e.label},evil:function(t){var e=Function;return new e("return "+t)()},initData:function(){var t=this.dragData.type;switch(za=window.scene.features.get(this.dragData.id),t){case"model":this.styleFormDatas.dataType="model_element",this.styleFormDatas.typeName=this.$t("featureDatas.model.name"),this.styleFormDatas.name=this.dragData.name||this.$t("featureDatas.model.name");break;case"dem":this.styleFormDatas.dataType="dem_element",this.styleFormDatas.typeName=this.$t("featureDatas.dem.name"),this.styleFormDatas.name=this.dragData.name||this.$t("featureDatas.dem.subName");break;case"_3dBuilding":this.styleFormDatas.dataType="_3dBuilding",this.styleFormDatas.typeName=this.$t("featureDatas._3dBuilding.name"),this.styleFormDatas.name=this.dragData.name||this.$t("featureDatas._3dBuilding.name");break;case"gltf":this.styleFormDatas.dataType="gltf_element",this.styleFormDatas.typeName=this.$t("featureDatas.gltf.subName"),this.styleFormDatas.name=this.dragData.name||this.$t("featureDatas.gltf.subName"),this.dragData.defaultSource&&(this.styleFormDatas.datas[t].list[0].optionState=!1);break;case"fbx":this.styleFormDatas.dataType="fbx",this.styleFormDatas.typeName=this.$t("featureDatas.fbx.subName"),this.styleFormDatas.name=this.dragData.name||this.$t("featureDatas.fbx.subName"),this.dragData.defaultSource&&(this.styleFormDatas.datas[t].list[0].optionState=!1);break;case"_3dTiles":this.styleFormDatas.dataType="_3dTiles",this.styleFormDatas.typeName=this.$t("featureDatas._3dTiles.name"),this.styleFormDatas.name=this.dragData.name||this.$t("featureDatas._3dTiles.subName");break;case"panorama":this.styleFormDatas.dataType="panorama_element",this.styleFormDatas.typeName=this.$t("featureDatas.panorama.name"),this.styleFormDatas.name=this.dragData.name||this.$t("featureDatas.panorama.name");break;case"video":this.styleFormDatas.dataType="video_element",this.styleFormDatas.typeName=this.$t("featureDatas.video.name"),this.styleFormDatas.name=this.dragData.name||this.$t("featureDatas.video.name");break;case"polygon":this.styleFormDatas.dataType="polygon_surface_element",this.styleFormDatas.typeName=this.dragData.title,this.styleFormDatas.name=this.dragData.name||this.$t("featureDatas.polygon.name");break;case"ripplewall":this.styleFormDatas.dataType="ripplewall_element",this.styleFormDatas.typeName=this.$t("featureDatas.ripplewall.name"),this.styleFormDatas.name=this.dragData.name||this.$t("featureDatas.ripplewall.name");break;case"radar":this.styleFormDatas.dataType="radar_element",this.styleFormDatas.typeName=this.$t("featureDatas.radar.name"),this.styleFormDatas.name=this.dragData.name||this.$t("featureDatas.radar.name");break;case"shield":this.styleFormDatas.dataType="shield_element",this.styleFormDatas.typeName=this.$t("featureDatas.shield.name"),this.styleFormDatas.name=this.dragData.name||this.$t("featureDatas.shield.name");break;case"ring":this.styleFormDatas.dataType="ring_element",this.styleFormDatas.typeName=this.$t("featureDatas.ring.name"),this.styleFormDatas.name=this.dragData.name||this.$t("featureDatas.ring.name");break;case"flame":this.styleFormDatas.dataType="fire_element",this.styleFormDatas.typeName=this.$t("featureDatas.flame.name"),this.styleFormDatas.name=this.dragData.name||this.$t("featureDatas.flame.name");break;case"smoke":this.styleFormDatas.dataType="smoke_element",this.styleFormDatas.typeName=this.$t("featureDatas.smoke.name"),this.styleFormDatas.name=this.dragData.name||this.$t("featureDatas.smoke.name");break;case"polyline":this.styleFormDatas.dataType="polygon_line_element",this.styleFormDatas.typeName=this.$t("featureDatas.polyline.name"),this.styleFormDatas.name=this.dragData.name||this.$t("featureDatas.polyline.name");break;case"vectorextrude":this.styleFormDatas.dataType="vectorextrude_element",this.styleFormDatas.typeName=this.$t("featureDatas.vectorextrude.name"),this.styleFormDatas.name=this.dragData.name||this.$t("featureDatas.vectorextrude.name");break;case"wmts":this.styleFormDatas.dataType="wmts_element",this.styleFormDatas.typeName=this.$t("featureDatas.wmts.name"),this.styleFormDatas.name=this.dragData.name||this.$t("featureDatas.wmts.name");break;case"wms":this.styleFormDatas.dataType="wms_element",this.styleFormDatas.typeName=this.$t("featureDatas.wms.name"),this.styleFormDatas.name=this.dragData.name||this.$t("featureDatas.wms.name");break;case"tms":this.styleFormDatas.dataType="tms_element",this.styleFormDatas.typeName=this.$t("featureDatas.tms.name"),this.styleFormDatas.name=this.dragData.name||this.$t("featureDatas.tms.name");break;case"shp":this.styleFormDatas.dataType="shp_element",this.styleFormDatas.typeName=this.$t("featureDatas.shp.name"),this.styleFormDatas.name=this.dragData.name||this.$t("featureDatas.shp.name");break;case"kml":this.styleFormDatas.dataType="kml_element",this.styleFormDatas.typeName=this.$t("featureDatas.kml.name"),this.styleFormDatas.name=this.dragData.name||this.$t("featureDatas.kml.name");break;case"geoJSON":this.styleFormDatas.dataType="geojson_element",this.styleFormDatas.typeName=this.$t("featureDatas.geoJSON.name"),this.styleFormDatas.name=this.dragData.name||this.$t("featureDatas.geoJSON.name");break;case"heatmap":this.styleFormDatas.dataType="heatmap_element",this.styleFormDatas.typeName=this.$t("featureDatas.heatmap.name"),this.styleFormDatas.name=this.dragData.name||this.$t("featureDatas.heatmap.name");break;case"batch-extrude":this.styleFormDatas.dataType="batch_extrude_element",this.styleFormDatas.typeName=this.$t("featureDatas['batch-extrude'].name"),this.styleFormDatas.name=this.dragData.name||this.$t("featureDatas['batch-extrude'].name");break;case"annotation":this.styleFormDatas.dataType="annotation_element",this.styleFormDatas.typeName=this.$t("featureDatas.annotation.name"),this.styleFormDatas.name=this.dragData.name||this.$t("featureDatas.annotation.name");break;case"billboard":this.styleFormDatas.dataType="billboard",this.styleFormDatas.typeName=this.$t("featureDatas.billboard.name"),this.styleFormDatas.name=this.dragData.name||this.$t("featureDatas.billboard.name");break;case"pathAnimation":this.styleFormDatas.dataType="pathanimation_advanced",this.styleFormDatas.typeName=this.$t("topToolBarMenu.advanced.children.pathAnimation.name"),this.styleFormDatas.name=this.dragData.name||this.$t("topToolBarMenu.advanced.children.pathAnimation.name");break;case"waterfall":this.styleFormDatas.dataType="waterfall_element",this.styleFormDatas.typeName=this.$t("featureDatas.waterfall.name"),this.styleFormDatas.name=this.dragData.name||this.$t("featureDatas.waterfall.name");break;case"projector":this.styleFormDatas.dataType="video_projection_element",this.styleFormDatas.typeName=this.$t("featureDatas.projector.name"),this.styleFormDatas.name=this.dragData.name||this.$t("featureDatas.projector.name");break;case"snow":this.styleFormDatas.dataType="snow_element",this.styleFormDatas.typeName=this.$t("featureDatas.snow.name"),this.styleFormDatas.name=this.dragData.name||this.$t("featureDatas.snow.name");break;case"tailline":this.styleFormDatas.dataType="tail_line",this.styleFormDatas.typeName=this.$t("featureDatas.tailline.name"),this.styleFormDatas.name=this.dragData.name||this.$t("featureDatas.tailline.name");break}this.dragData.defaultSource&&(this.styleFormDatas.name=this.dragData.title),"model"==t&&(void 0==this.dragData.thumbnail||""==this.dragData.thumbnail?this.styleFormDatas.thumn=a("5a44"):this.styleFormDatas.thumn="data:image/png;base64,"+this.dragData.thumbnail)},switchListState:function(t){this.$set(this.listState,t,!this.listState[t])},submitSetting:function(t,e,a,s){var i=this.dragData.type;if("objectSetting"!=this.dragData.interlock){this.styleFormDatas.id=this.$refs[i].elementDatas.id,this.styleFormDatas.longitude=this.$refs[i].elementDatas.longitude||0,this.styleFormDatas.latitude=this.$refs[i].elementDatas.latitude||0,this.styleFormDatas.altitude=this.$refs[i].elementDatas.altitude||0,this.styleFormDatas.rotation=this.$refs[i].elementDatas.rotation&&this.$refs[i].elementDatas.rotation.map((function(t){return t||0})),this.styleFormDatas.offset=this.$refs[i].elementDatas.offset&&this.$refs[i].elementDatas.offset.map((function(t){return t||0})),this.$refs[i].elementDatas.scale&&(this.styleFormDatas.scale=this.$refs[i].elementDatas.scale.map((function(t){return t||1})));var n=this.$refs[i].elementDatas.list;switch(i){case"model":var l=n.concat(a.list);return void this.addModelFeature(l,a.extra);case"dem":this.addDemFeature(n,a.extra);break;case"_3dBuilding":this.add3DbuildingFeature(n);break;case"fbx":var o=n.concat(a.list);this.addGltfOrFbxFeature(o,a.extra);break;case"gltf":var r=n.concat(a.list);this.addGltfOrFbxFeature(r,a.extra);break;case"_3dTiles":this.add3DTilesFeature(n,a.params,a.extra);break;case"panorama":this.addPanoramaFeature(n,a.extra);break;case"video":this.addVideoFeature(n,a.extra);break;case"polygon":var c=this.$refs[i].params,u=[1e3,900,800,700,600,500,400,300,200,100];c.interval=u[c.interval-1],this.addPolygonFeature(n,c,a.extra);break;case"ripplewall":var m=this.$refs[i].params;this.addRipplewallFeature(n,m,s,a.extra);break;case"radar":var d=this.$refs[i].params;this.addRadarFeature(n,d,s,a.extra);break;case"shield":var p=this.$refs[i].params;this.addShieldFeature(n,p,s,a.extra);break;case"ring":var h=this.$refs[i].params;this.addRingFeature(n,h,s,a.extra);break;case"flame":case"smoke":var f=this.$refs[i].params;this.addFlameFeature(f,a.extra);break;case"polyline":var v=this.$refs[i].params;this.addPolylineFeature(n,v,a.extra);break;case"vectorextrude":var g=this.$refs[i].params,y=Object.assign(a.params,g);this.addVectorExtrudeFeature(n,y,a.extra);break;case"wmts":case"wms":case"tms":this.addwmtsFeature(n,a.extra);break;case"shp":var C=this.$refs[i].params,D=Object.assign(a.params,C);this.addSHPFeature(n,D,a.extra);break;case"kml":this.addKMLFeature(n,a.extra);break;case"geoJSON":var b={};b=this.$refs[i].params;var w=Object.assign(a.params,b);this.addGeoJSONFeature(n,w,a.extra);break;case"heatmap":var _=this.$refs[i].getParams();this.addHeatmapFeature(n,_,s,a.extra);break;case"batch-extrude":var x=this.$refs[i].params;this.addBatchExtrudeFeature(n,x,a.extra);break;case"billboard":case"annotation":var k={};void 0===t.annotation&&(t.annotation={}),k.annotation=t.annotation;var $=this.$refs[i].pointStyleImg;"custom"!=n[2].radio&&(n[5].checkedType=$.anchorCheck,$.anchorCheck>=0&&(n[5].fileSrc=$.anchor[$.anchorCheck].fileSrc),n[4].checkedType=$.panelCheck),k.annotation.dataList=t.dataList,k.annotation.title=t.title,k.annotation.videoLink=t.videoLink,k.annotation.panelToggleMode=this.$refs[i].panelToggleMode.value,0==n[6].annotationRadio?(this.addAnnotationFeature(n,k,e,s,a.extra),i="annotation"):(this.addBillboardFeature(n,t,a.extra),i="billboard");break;case"pathAnimation":var S=this.$refs[i].params,P=window.scene.mv.tools.draw.getAllData(),E=[];if(P.polyline.length)for(var R=P.polyline[0].points.length,T=0;T<R;T++)E[T]=[P.polyline[0].points[T].x,P.polyline[0].points[T].y,P.polyline[0].points[T].z];if(E.length<2)return this.$parent.loading&&this.$parent.loading.close(),void this.$message.error(this.$t("messageTips.errorPathAnimate"));var O=window.scene.addPathRoaming(this.dragData.id||"");O.name=this.styleFormDatas.name,O.positions=E,O.cameraDistance=S.cameraDistance,O.showLine=S.showLine,O.step=parseFloat(S.step),O.speed=parseFloat(S.speed),O.avatarVisible=S.avatarVisible,O.avatarUrl=S.avatarUrl,O.verticleOffset=S.verticleOffset,O.pitch=S.pitch,O.id=Array.from(window.scene.pathRoamings.keys())[Array.from(window.scene.pathRoamings.keys()).length-1];var j=window.scene.pathRoamings,z=[];j.forEach((function(t){t.isRunning=!1,z.push(t)})),this.$store.commit("setPathAnimationList",z),this.$store.commit("setValidActivedType","pathAnimation");var F="【".concat(this.$t("topToolBarMenu.advanced.children.pathAnimation.name"),"】").concat(this.$t("others.added"));this.$message({showClose:!0,message:F,type:"success"});break;case"waterfall":var I=this.$refs[i].params;this.addWaterfallFeature(I);break;case"projector":var A=this.$refs[i].params,N=this.$refs[i].elementDatas.list[0].value;this.addProjectorFeature(A,N);break;case"snow":this.addSnowFeature({priority:0});break;case"tailline":var M=this.$refs.tailline.params;this.addTailLineFeature(n,M,a.extra);break}if("model"!=i){var G=JSON.parse(JSON.stringify(this.dragData));G.staticType&&(delete G.staticType,this.$store.commit("saveDragOverData",G)),"pathAnimation"!=i&&this.$deepUpdateScene(i),this.$emit("closeSet")}}else{if("advanced_setting"==this.dragData.type){var X={ssao:window.scene.config.ssao,shadow:window.scene.config.shadow,modeRadio:window.scene.mv.renderMode};this.$store.commit("setRenderSettingStorage",{rawData:JSON.stringify(X)})}this.$emit("closeSet")}},onCheckedCoordinate:function(t){var e=[0,0,0],a=window.scene.mv._THREE,s=window.scene.queryPosition(new a.Vector2(t.clientX,t.clientY));""!=s&&void 0!=s?(e=window.scene.mv.tools.coordinate.vector2mercator(s),this.$refs[this.dragData.type].elementDatas.longitude=e[0],this.$refs[this.dragData.type].elementDatas.latitude=e[1],this.$refs[this.dragData.type].elementDatas.altitude=e[2],za.origin=e,za.altitude=e[2]):this.$message.error(this.$t("messageTips.errorCheckCoordinate")),this.offCheckedCoordinate()},offCheckedCoordinate:function(){window.scene.mv.status.selectable=!0,this.elementTransformObj="",window.scene.mv.events.singleSelection.off("default",this.onCheckedCoordinate)},clickCheckedCoordinate:function(){""!=this.elementTransformObj&&"coordinate"!=this.elementTransformObj&&this.offModelTransformChange(),"coordinate"!=this.elementTransformObj?(window.scene.mv.status.selectable=!1,this.$message.success(this.$t("messageTips.checkCoordinate")),this.elementTransformObj="coordinate",window.scene.mv.events.singleSelection.on("default",this.onCheckedCoordinate)):this.offModelTransformChange("coordinate")},eventsContentChange:function(){this.$emit("eventsContentChange","default")},setMonacoEditorAttr:function(t){t&&this.$emit("eventsContentChange","custom");var e=document.querySelector(".annotation-css-custom");null!=e&&t&&this.$notify({title:this.$t("messageTips.tipsTitle"),type:"info",dangerouslyUseHTMLString:!0,message:this.$t("messageTips.customCssTip"),duration:25e3})},setAnchorPanelShowStyle:function(t){var e=this.dragData.type;this.setAnchorPanelLeadWireType(t,e),this.$emit("tabDatasSetting",t)},handleModelTransform:function(t){"coordinate"==this.elementTransformObj&&this.offCheckedCoordinate(),window.scene.mv.tools.transform.currentMode=t,""==this.elementTransformObj&&(window.scene.mv.tools.transform.active(za),window.scene.mv.events.transformChange.on("default",this.onModelTransformChange)),this.elementTransformObj!=t?(this.elementTransformObj=t,setTimeout((function(){window.scene.render()}))):this.offModelTransformChange()},offModelTransformChange:function(t){"coordinate"===t?window.scene.mv.events.singleSelection.off("default",this.onCheckedCoordinate):(window.scene.mv.tools.transform.deactive(),window.scene.mv.events.transformChange.off("default",this.onModelTransformChange),window.scene.forceRepaint=!1),this.elementTransformObj="",window.scene.render(),setTimeout((function(){window.scene.render()}),1e3)},onModelTransformChange:function(t,e,a){t&&(this.$refs[this.dragData.type].elementDatas.offset=za.offset),e&&(this.$set(this.$refs[this.dragData.type].elementDatas.rotation,0,za.rotation[0]),this.$set(this.$refs[this.dragData.type].elementDatas.rotation,1,za.rotation[1]),this.$set(this.$refs[this.dragData.type].elementDatas.rotation,2,za.rotation[2])),a&&(this.$refs[this.dragData.type].elementDatas.scale=za.scale,this.$refs[this.dragData.type].elementDatas.scaleObj.x=za.scale[0],this.$refs[this.dragData.type].elementDatas.scaleObj.y=za.scale[1],this.$refs[this.dragData.type].elementDatas.scaleObj.z=za.scale[2])},inputChangeOrigin:function(t){var e=""==t.value?0:parseFloat(t.value+""),a=za.origin;switch(t.type){case"longitude":a[0]=e;break;case"latitude":a[1]=e;break;case"altitude":za.altitude=e;break}za.origin=a,this.$nextTick((function(){window.scene.render()}))},inputChangeRotation:function(t){var e=""==t.value?0:parseFloat(t.value+""),a=za.rotation;switch(t.type){case"x":a[0]=e;break;case"y":a[1]=e;break;case"z":a[2]=e;break}this.elementTransformObj&&window.scene.mv.tools.transform.syncTargetChange(),za.rotation=a},inputChangeOffset:function(t){var e=""==t.value?0:parseFloat(t.value+""),a=za.offset;switch(t.type){case"x":a[0]=e;break;case"y":a[1]=e;break;case"z":a[2]=e;break}za.offset=a,this.elementTransformObj&&window.scene.mv.tools.transform.syncTargetChange(),setTimeout((function(){window.scene.render()}),300)},inputChangeScale:function(t){var e=za.scale;switch(t.type){case"x":e[0]=t.value;break;case"y":e[1]=t.value;break;case"z":e[2]=t.value;break}this.elementTransformObj&&window.scene.mv.tools.transform.syncTargetChange(),za.scale=e},addCurrentOption:function(t){t[t.optionItem.name].push(t.optionItem.value)},removeCurrentOption:function(t,e){t[t.optionItem.name].splice(e,1)},freeSketchPolygon:function(t){var e=window.scene.features.has("underlay"),a=0;if(window.scene.features.size>0&&window.scene.features.forEach((function(t){"model"==t.type&&(a+=1)})),0==a&&!e)return this.$message({message:this.$t("messageTips.freeSketch.errorMsg"),type:"warning",showClose:!0,duration:8e3}),this.$store.commit("setActivedType",""),this.$parent.$el.style.visibility="visible",!1;window.scene.mv.tools.draw.active(),this.$parent.$el.style.visibility="hidden",this.$store.commit("toogleDrawState",!0),"polygon"==t?window.scene.mv.tools.draw.startDrawPolygon():window.scene.mv.tools.draw.startDrawLine(),this.$message({showClose:!0,message:this.$t("messageTips.freeSketch.defaultMsg"),duration:7e3}),window.scene.mv.tools.draw.finishDrawEvent.on("default",this.handleFreeSketchPolygon),document.addEventListener("keyup",this.onEscKeyUp)},handleFreeSketchPolygon:function(){var t=window.scene.mv.tools.draw.getAllData(),e=this.dragData.type,a=this.styleFormDatas.datas[e].list;if(t.polygon.length>0){var s=t.polygon[0].points.length;if(s-1<=2)this.$message.error(this.$t("messageTips.freeSketch.errorMsg1"));else{for(var i=[],n=0;n<s-1;n++)i[n]=[t.polygon[0].points[n].x,t.polygon[0].points[n].y];a[0].position=i}}else{var l=t.polyline[0].points.length;if(l<=1)this.$message.error(this.$t("messageTips.freeSketch.errorMsg2"));else{for(var o=[],r=0;r<l;r++)o[r]=[t.polyline[0].points[r].x,t.polyline[0].points[r].y,t.polyline[0].points[r].z];a[0].position=o}}this.offDrawEvents()},onEscKeyUp:function(t){27==t.keyCode&&"Escape"===t.key&&this.offDrawEvents()},offDrawEvents:function(){window.scene.mv.tools.draw.deactive(),this.$parent.$el.style.visibility="visible",this.$store.commit("toogleDrawState",!1),window.scene.mv.tools.draw.finishDrawEvent.off("default",this.handleFreeSketchPolygon),document.removeEventListener("keyup",this.onEscKeyUp)},lockBillboradScale:function(t){t.locked=!t.locked,t.locked?t.height="auto":t.height=2},getRelationDatas:function(){var t={};switch(this.dragData.type){case"heatmap":t=this.$refs[this.dragData.type].iotDataSelect;break}return t},postRelationDatas:function(t){var e=null;switch(this.dragData.type){case"heatmap":e=this.$refs[this.dragData.type],e.relationArr=t;break}}},beforeDestroy:function(){za=null,""!=this.elementTransformObj&&("coordinate"==this.elementTransformObj?this.offCheckedCoordinate():this.offModelTransformChange())}},Ia=Fa,Aa=(a("1821"),a("063d"),Object(d["a"])(Ia,s,i,!1,null,"a73f8f16",null));e["a"]=Aa.exports},"25ac":function(t,e,a){},"25e4":function(t,e,a){},"29c1":function(t,e,a){"use strict";a("b4fa")},3001:function(t,e,a){},3254:function(t,e,a){},"3fd7":function(t,e,a){"use strict";a("66af")},"43ef":function(t,e,a){"use strict";a("18af")},"43fd":function(t,e,a){"use strict";a("0a51")},"451c":function(t,e,a){},"48d4":function(t,e,a){"use strict";a("5df0")},"52e1":function(t,e,a){},"5df0":function(t,e,a){},"638c":function(t,e,a){"use strict";a("148b")},"66af":function(t,e,a){},"6e47":function(t,e,a){},"6f6e":function(t,e,a){},"784a":function(t,e,a){"use strict";a("d103")},"802b":function(t,e,a){},"818f":function(t,e,a){"use strict";a("fd31")},8368:function(t,e,a){},"8f1b":function(t,e,a){},"92b1":function(t,e,a){"use strict";a("e3d6")},"981b":function(t,e,a){},"996b":function(t,e,a){"use strict";a("8f1b")},9978:function(t,e,a){"use strict";a("6e47")},"9ecb":function(t,e,a){},a173:function(t,e,a){"use strict";a("25e4")},a4b3:function(t,e,a){"use strict";a("981b")},a84b:function(t,e,a){"use strict";a("3254")},a858:function(t,e,a){"use strict";a("bee8")},a935:function(t,e,a){},a9cb:function(t,e,a){},ab47:function(t,e,a){"use strict";a("8368")},ac5e:function(t,e,a){"use strict";a("3001")},b4fa:function(t,e,a){},bee8:function(t,e,a){},bff6:function(t,e,a){},c0f7:function(t,e,a){},c52d:function(t,e,a){},c75f:function(t,e,a){"use strict";a("d06b")},cbe6:function(t,e,a){"use strict";a("f6a1")},ce07:function(t,e,a){"use strict";a("bff6")},ce7f:function(t,e,a){"use strict";a("a9cb")},cfb7:function(t,e,a){"use strict";a("d961")},d06b:function(t,e,a){},d103:function(t,e,a){},d1ca:function(t,e,a){"use strict";a("451c")},d246:function(t,e,a){"use strict";a("1fd8")},d825:function(t,e,a){"use strict";a("a935")},d961:function(t,e,a){},e3d6:function(t,e,a){},eb3b:function(t,e,a){"use strict";a("52e1")},f02e:function(t,e,a){},f6a1:function(t,e,a){},fa71:function(t,e,a){"use strict";a("0abe")},fb4f:function(t,e,a){"use strict";a("25ac")},fd31:function(t,e,a){}}]);