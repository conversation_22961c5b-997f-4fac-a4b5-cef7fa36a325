(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-065154c0"],{1183:function(t,e,n){"use strict";n.d(e,"e",(function(){return M})),n.d(e,"b",(function(){return S})),n.d(e,"a",(function(){return L})),n.d(e,"c",(function(){return l})),n.d(e,"d",(function(){return H})),n.d(e,"f",(function(){return r}));var r={};function i(t){return Math.min.apply(null,t)}function a(t){return Math.max.apply(null,t)}function o(t,e,n,r){var i=t-n,a=e-r;return Math.sqrt(i*i+a*a)}function s(t,e){return Math.abs(t-e)<.001}function h(t,e){var n=i(t),r=i(e),o=a(t),s=a(e);return{x:n,y:r,width:o-n,height:s-r}}function u(t,e,n,r){return{minX:i([t,n]),maxX:a([t,n]),minY:i([e,r]),maxY:a([e,r])}}function c(t){return(t+2*Math.PI)%(2*Math.PI)}n.r(r),n.d(r,"distance",(function(){return o})),n.d(r,"isNumberEqual",(function(){return s})),n.d(r,"getBBoxByArray",(function(){return h})),n.d(r,"getBBoxRange",(function(){return u})),n.d(r,"piMod",(function(){return c}));var p=n("6711"),l={box:function(t,e,n,r){return h([t,n],[e,r])},length:function(t,e,n,r){return o(t,e,n,r)},pointAt:function(t,e,n,r,i){return{x:(1-i)*t+i*n,y:(1-i)*e+i*r}},pointDistance:function(t,e,n,r,i,a){var s=(n-t)*(i-t)+(r-e)*(a-e);if(s<0)return o(t,e,i,a);var h=(n-t)*(n-t)+(r-e)*(r-e);return s>h?o(n,r,i,a):this.pointToLine(t,e,n,r,i,a)},pointToLine:function(t,e,n,r,i,a){var o=[n-t,r-e];if(p["exactEquals"](o,[0,0]))return Math.sqrt((i-t)*(i-t)+(a-e)*(a-e));var s=[-o[1],o[0]];p["normalize"](s,s);var h=[i-t,a-e];return Math.abs(p["dot"](h,s))},tangentAngle:function(t,e,n,r){return Math.atan2(r-e,n-t)}},f=1e-4;function g(t,e,n,r,i,a){var s,h=1/0,u=[n,r],c=20;a&&a>200&&(c=a/10);for(var p=1/c,l=p/10,g=0;g<=c;g++){var v=g*p,y=[i.apply(null,t.concat([v])),i.apply(null,e.concat([v]))],m=o(u[0],u[1],y[0],y[1]);m<h&&(s=v,h=m)}if(0===s)return{x:t[0],y:e[0]};if(1===s){var d=t.length;return{x:t[d-1],y:e[d-1]}}h=1/0;for(g=0;g<32;g++){if(l<f)break;var x=s-l,b=s+l;y=[i.apply(null,t.concat([x])),i.apply(null,e.concat([x]))],m=o(u[0],u[1],y[0],y[1]);if(x>=0&&m<h)s=x,h=m;else{var M=[i.apply(null,t.concat([b])),i.apply(null,e.concat([b]))],w=o(u[0],u[1],M[0],M[1]);b<=1&&w<h?(s=b,h=w):l*=.5}}return{x:i.apply(null,t.concat([s])),y:i.apply(null,e.concat([s]))}}function v(t,e){for(var n=0,r=t.length,i=0;i<r;i++){var a=t[i],s=e[i],h=t[(i+1)%r],u=e[(i+1)%r];n+=o(a,s,h,u)}return n/2}function y(t,e,n,r){var i=1-r;return i*i*t+2*r*i*e+r*r*n}function m(t,e,n){var r=t+n-2*e;if(s(r,0))return[.5];var i=(t-e)/r;return i<=1&&i>=0?[i]:[]}function d(t,e,n,r){return 2*(1-r)*(e-t)+2*r*(n-e)}function x(t,e,n,r,i,a,o){var s=y(t,n,i,o),h=y(e,r,a,o),u=l.pointAt(t,e,n,r,o),c=l.pointAt(n,r,i,a,o);return[[t,e,u.x,u.y,s,h],[s,h,c.x,c.y,i,a]]}function b(t,e,n,r,i,a,s){if(0===s)return(o(t,e,n,r)+o(n,r,i,a)+o(t,e,i,a))/2;var h=x(t,e,n,r,i,a,.5),u=h[0],c=h[1];return u.push(s-1),c.push(s-1),b.apply(null,u)+b.apply(null,c)}var M={box:function(t,e,n,r,i,a){var o=m(t,n,i)[0],s=m(e,r,a)[0],u=[t,i],c=[e,a];return void 0!==o&&u.push(y(t,n,i,o)),void 0!==s&&c.push(y(e,r,a,s)),h(u,c)},length:function(t,e,n,r,i,a){return b(t,e,n,r,i,a,3)},nearestPoint:function(t,e,n,r,i,a,o,s){return g([t,n,i],[e,r,a],o,s,y)},pointDistance:function(t,e,n,r,i,a,s,h){var u=this.nearestPoint(t,e,n,r,i,a,s,h);return o(u.x,u.y,s,h)},interpolationAt:y,pointAt:function(t,e,n,r,i,a,o){return{x:y(t,n,i,o),y:y(e,r,a,o)}},divide:function(t,e,n,r,i,a,o){return x(t,e,n,r,i,a,o)},tangentAngle:function(t,e,n,r,i,a,o){var s=d(t,n,i,o),h=d(e,r,a,o),u=Math.atan2(h,s);return c(u)}};function w(t,e,n,r,i){var a=1-i;return a*a*a*t+3*e*i*a*a+3*n*i*i*a+r*i*i*i}function C(t,e,n,r,i){var a=1-i;return 3*(a*a*(e-t)+2*a*i*(n-e)+i*i*(r-n))}function A(t,e,n,r){var i,a,o,h=-3*t+9*e-9*n+3*r,u=6*t-12*e+6*n,c=3*e-3*t,p=[];if(s(h,0))s(u,0)||(i=-c/u,i>=0&&i<=1&&p.push(i));else{var l=u*u-4*h*c;s(l,0)?p.push(-u/(2*h)):l>0&&(o=Math.sqrt(l),i=(-u+o)/(2*h),a=(-u-o)/(2*h),i>=0&&i<=1&&p.push(i),a>=0&&a<=1&&p.push(a))}return p}function P(t,e,n,r,i,a,o,s,h){var u=w(t,n,i,o,h),c=w(e,r,a,s,h),p=l.pointAt(t,e,n,r,h),f=l.pointAt(n,r,i,a,h),g=l.pointAt(i,a,o,s,h),v=l.pointAt(p.x,p.y,f.x,f.y,h),y=l.pointAt(f.x,f.y,g.x,g.y,h);return[[t,e,p.x,p.y,v.x,v.y,u,c],[u,c,y.x,y.y,g.x,g.y,o,s]]}function O(t,e,n,r,i,a,o,s,h){if(0===h)return v([t,n,i,o],[e,r,a,s]);var u=P(t,e,n,r,i,a,o,s,.5),c=u[0],p=u[1];return c.push(h-1),p.push(h-1),O.apply(null,c)+O.apply(null,p)}var S={extrema:A,box:function(t,e,n,r,i,a,o,s){for(var u=[t,o],c=[e,s],p=A(t,n,i,o),l=A(e,r,a,s),f=0;f<p.length;f++)u.push(w(t,n,i,o,p[f]));for(f=0;f<l.length;f++)c.push(w(e,r,a,s,l[f]));return h(u,c)},length:function(t,e,n,r,i,a,o,s){return O(t,e,n,r,i,a,o,s,3)},nearestPoint:function(t,e,n,r,i,a,o,s,h,u,c){return g([t,n,i,o],[e,r,a,s],h,u,w,c)},pointDistance:function(t,e,n,r,i,a,s,h,u,c,p){var l=this.nearestPoint(t,e,n,r,i,a,s,h,u,c,p);return o(l.x,l.y,u,c)},interpolationAt:w,pointAt:function(t,e,n,r,i,a,o,s,h){return{x:w(t,n,i,o,h),y:w(e,r,a,s,h)}},divide:function(t,e,n,r,i,a,o,s,h){return P(t,e,n,r,i,a,o,s,h)},tangentAngle:function(t,e,n,r,i,a,o,s,h){var u=C(t,n,i,o,h),p=C(e,r,a,s,h);return c(Math.atan2(p,u))}};function _(t,e){var n=Math.abs(t);return e>0?n:-1*n}var j={box:function(t,e,n,r){return{x:t-n,y:e-r,width:2*n,height:2*r}},length:function(t,e,n,r){return Math.PI*(3*(n+r)-Math.sqrt((3*n+r)*(n+3*r)))},nearestPoint:function(t,e,n,r,i,a){var o=n,s=r;if(0===o||0===s)return{x:t,y:e};for(var h,u,c=i-t,p=a-e,l=Math.abs(c),f=Math.abs(p),g=o*o,v=s*s,y=Math.PI/4,m=0;m<4;m++){h=o*Math.cos(y),u=s*Math.sin(y);var d=(g-v)*Math.pow(Math.cos(y),3)/o,x=(v-g)*Math.pow(Math.sin(y),3)/s,b=h-d,M=u-x,w=l-d,C=f-x,A=Math.hypot(M,b),P=Math.hypot(C,w),O=A*Math.asin((b*C-M*w)/(A*P)),S=O/Math.sqrt(g+v-h*h-u*u);y+=S,y=Math.min(Math.PI/2,Math.max(0,y))}return{x:t+_(h,c),y:e+_(u,p)}},pointDistance:function(t,e,n,r,i,a){var s=this.nearestPoint(t,e,n,r,i,a);return o(s.x,s.y,i,a)},pointAt:function(t,e,n,r,i){var a=2*Math.PI*i;return{x:t+n*Math.cos(a),y:e+r*Math.sin(a)}},tangentAngle:function(t,e,n,r,i){var a=2*Math.PI*i,o=Math.atan2(r*Math.cos(a),-n*Math.sin(a));return c(o)}};function k(t,e,n,r,i,a,o,s){return-1*n*Math.cos(i)*Math.sin(s)-r*Math.sin(i)*Math.cos(s)}function B(t,e,n,r,i,a,o,s){return-1*n*Math.sin(i)*Math.sin(s)+r*Math.cos(i)*Math.cos(s)}function E(t,e,n){return Math.atan(-e/t*Math.tan(n))}function I(t,e,n){return Math.atan(e/(t*Math.tan(n)))}function T(t,e,n,r,i,a){return n*Math.cos(i)*Math.cos(a)-r*Math.sin(i)*Math.sin(a)+t}function X(t,e,n,r,i,a){return n*Math.sin(i)*Math.cos(a)+r*Math.cos(i)*Math.sin(a)+e}function Y(t,e,n,r){var i=Math.atan2(r*t,n*e);return(i+2*Math.PI)%(2*Math.PI)}function D(t,e,n){return{x:t*Math.cos(n),y:e*Math.sin(n)}}function F(t,e,n){var r=Math.cos(n),i=Math.sin(n);return[t*r-e*i,t*i+e*r]}var L={box:function(t,e,n,r,i,a,o){for(var s=E(n,r,i),h=1/0,u=-1/0,c=[a,o],p=2*-Math.PI;p<=2*Math.PI;p+=Math.PI){var l=s+p;a<o?a<l&&l<o&&c.push(l):o<l&&l<a&&c.push(l)}for(p=0;p<c.length;p++){var f=T(t,e,n,r,i,c[p]);f<h&&(h=f),f>u&&(u=f)}var g=I(n,r,i),v=1/0,y=-1/0,m=[a,o];for(p=2*-Math.PI;p<=2*Math.PI;p+=Math.PI){var d=g+p;a<o?a<d&&d<o&&m.push(d):o<d&&d<a&&m.push(d)}for(p=0;p<m.length;p++){var x=X(t,e,n,r,i,m[p]);x<v&&(v=x),x>y&&(y=x)}return{x:h,y:v,width:u-h,height:y-v}},length:function(t,e,n,r,i,a,o){},nearestPoint:function(t,e,n,r,i,a,o,s,h){var u=F(s-t,h-e,-i),c=u[0],p=u[1],l=j.nearestPoint(0,0,n,r,c,p),f=Y(n,r,l.x,l.y);f<a?l=D(n,r,a):f>o&&(l=D(n,r,o));var g=F(l.x,l.y,i);return{x:g[0]+t,y:g[1]+e}},pointDistance:function(t,e,n,r,i,a,s,h,u){var c=this.nearestPoint(t,e,n,r,h,u);return o(c.x,c.y,h,u)},pointAt:function(t,e,n,r,i,a,o,s){var h=(o-a)*s+a;return{x:T(t,e,n,r,i,h),y:X(t,e,n,r,i,h)}},tangentAngle:function(t,e,n,r,i,a,o,s){var h=(o-a)*s+a,u=k(t,e,n,r,i,a,o,h),p=B(t,e,n,r,i,a,o,h);return c(Math.atan2(p,u))}};function N(t){for(var e=0,n=[],r=0;r<t.length-1;r++){var i=t[r],a=t[r+1],s=o(i[0],i[1],a[0],a[1]),h={from:i,to:a,length:s};n.push(h),e+=s}return{segments:n,totalLength:e}}function q(t){if(t.length<2)return 0;for(var e=0,n=0;n<t.length-1;n++){var r=t[n],i=t[n+1];e+=o(r[0],r[1],i[0],i[1])}return e}function R(t,e){if(e>1||e<0||t.length<2)return null;var n=N(t),r=n.segments,i=n.totalLength;if(0===i)return{x:t[0][0],y:t[0][1]};for(var a=0,o=null,s=0;s<r.length;s++){var h=r[s],u=h.from,c=h.to,p=h.length/i;if(e>=a&&e<=a+p){var f=(e-a)/p;o=l.pointAt(u[0],u[1],c[0],c[1],f);break}a+=p}return o}function z(t,e){if(e>1||e<0||t.length<2)return 0;for(var n=N(t),r=n.segments,i=n.totalLength,a=0,o=0,s=0;s<r.length;s++){var h=r[s],u=h.from,c=h.to,p=h.length/i;if(e>=a&&e<=a+p){o=Math.atan2(c[1]-u[1],c[0]-u[0]);break}a+=p}return o}function G(t,e,n){for(var r=1/0,i=0;i<t.length-1;i++){var a=t[i],o=t[i+1],s=l.pointDistance(a[0],a[1],o[0],o[1],e,n);s<r&&(r=s)}return r}var H={box:function(t){for(var e=[],n=[],r=0;r<t.length;r++){var i=t[r];e.push(i[0]),n.push(i[1])}return h(e,n)},length:function(t){return q(t)},pointAt:function(t,e){return R(t,e)},pointDistance:function(t,e,n){return G(t,e,n)},tangentAngle:function(t,e){return z(t,e)}}},"53c8":function(t,e,n){"use strict";n.d(e,"a",(function(){return Ut}));var r={};n.r(r),n.d(r,"Base",(function(){return et})),n.d(r,"Circle",(function(){return rt})),n.d(r,"Ellipse",(function(){return ot})),n.d(r,"Image",(function(){return ut})),n.d(r,"Line",(function(){return ft})),n.d(r,"Marker",(function(){return mt})),n.d(r,"Path",(function(){return It})),n.d(r,"Polygon",(function(){return Yt})),n.d(r,"Polyline",(function(){return Ft})),n.d(r,"Rect",(function(){return Rt})),n.d(r,"Text",(function(){return Gt}));var i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},i(t,e)};function a(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}var o=function(){return o=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var i in e=arguments[n],e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t},o.apply(this,arguments)};function s(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(t);i<r.length;i++)e.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]])}return n}Object.create;Object.create;var h=n("6855"),u=n("8937");function c(){return window?window.devicePixelRatio:1}function p(t,e,n,r){var i=t-n,a=e-r;return Math.sqrt(i*i+a*a)}function l(t,e,n,r,i,a){return i>=t&&i<=t+n&&a>=e&&a<=e+r}function f(t,e){return!(e.minX>t.maxX||e.maxX<t.minX||e.minY>t.maxY||e.maxY<t.minY)}function g(t,e){return t&&e?{minX:Math.min(t.minX,e.minX),minY:Math.min(t.minY,e.minY),maxX:Math.max(t.maxX,e.maxX),maxY:Math.max(t.maxY,e.maxY)}:t||e}function v(t,e){return t[0]===e[0]&&t[1]===e[1]}var y=/^l\s*\(\s*([\d.]+)\s*\)\s*(.*)/i,m=/^r\s*\(\s*([\d.]+)\s*,\s*([\d.]+)\s*,\s*([\d.]+)\s*\)\s*(.*)/i,d=/^p\s*\(\s*([axyn])\s*\)\s*(.*)/i,x=/[\d.]+:(#[^\s]+|[^\)]+\))/gi;function b(t,e){var n=t.match(x);Object(u["each"])(n,(function(t){var n=t.split(":");e.addColorStop(n[0],n[1])}))}function M(t,e,n){var r,i,a=y.exec(n),o=parseFloat(a[1])%360*(Math.PI/180),s=a[2],h=e.getBBox();o>=0&&o<.5*Math.PI?(r={x:h.minX,y:h.minY},i={x:h.maxX,y:h.maxY}):.5*Math.PI<=o&&o<Math.PI?(r={x:h.maxX,y:h.minY},i={x:h.minX,y:h.maxY}):Math.PI<=o&&o<1.5*Math.PI?(r={x:h.maxX,y:h.maxY},i={x:h.minX,y:h.minY}):(r={x:h.minX,y:h.maxY},i={x:h.maxX,y:h.minY});var u=Math.tan(o),c=u*u,p=(i.x-r.x+u*(i.y-r.y))/(c+1)+r.x,l=u*(i.x-r.x+u*(i.y-r.y))/(c+1)+r.y,f=t.createLinearGradient(r.x,r.y,p,l);return b(s,f),f}function w(t,e,n){var r=m.exec(n),i=parseFloat(r[1]),a=parseFloat(r[2]),o=parseFloat(r[3]),s=r[4];if(0===o){var h=s.match(x);return h[h.length-1].split(":")[1]}var u=e.getBBox(),c=u.maxX-u.minX,p=u.maxY-u.minY,l=Math.sqrt(c*c+p*p)/2,f=t.createRadialGradient(u.minX+c*i,u.minY+p*a,0,u.minX+c/2,u.minY+p/2,o*l);return b(s,f),f}function C(t,e,n){if(e.get("patternSource")&&e.get("patternSource")===n)return e.get("pattern");var r,i,a=d.exec(n),o=a[1],s=a[2];function h(){r=t.createPattern(i,o),e.set("pattern",r),e.set("patternSource",n)}switch(o){case"a":o="repeat";break;case"x":o="repeat-x";break;case"y":o="repeat-y";break;case"n":o="no-repeat";break;default:o="no-repeat"}return i=new Image,s.match(/^data:/i)||(i.crossOrigin="Anonymous"),i.src=s,i.complete?h():(i.onload=h,i.src=i.src),r}function A(t,e,n){var r=e.getBBox();if(isNaN(r.x)||isNaN(r.y)||isNaN(r.width)||isNaN(r.height))return n;if(Object(u["isString"])(n)){if("("===n[1]||"("===n[2]){if("l"===n[0])return M(t,e,n);if("r"===n[0])return w(t,e,n);if("p"===n[0])return C(t,e,n)}return n}return n instanceof CanvasPattern?n:void 0}function P(t){var e=0,n=0,r=0,i=0;return Object(u["isArray"])(t)?1===t.length?e=n=r=i=t[0]:2===t.length?(e=r=t[0],n=i=t[1]):3===t.length?(e=t[0],n=i=t[1],r=t[2]):(e=t[0],n=t[1],r=t[2],i=t[3]):e=n=r=i=t,[e,n,r,i]}function O(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])}function S(t,e){return O(t)*O(e)?(t[0]*e[0]+t[1]*e[1])/(O(t)*O(e)):1}function _(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(S(t,e))}function j(t,e){var n=e[1],r=e[2],i=Object(u["mod"])(Object(u["toRadian"])(e[3]),2*Math.PI),a=e[4],o=e[5],s=t[0],h=t[1],c=e[6],p=e[7],l=Math.cos(i)*(s-c)/2+Math.sin(i)*(h-p)/2,f=-1*Math.sin(i)*(s-c)/2+Math.cos(i)*(h-p)/2,g=l*l/(n*n)+f*f/(r*r);g>1&&(n*=Math.sqrt(g),r*=Math.sqrt(g));var y=n*n*(f*f)+r*r*(l*l),m=y?Math.sqrt((n*n*(r*r)-y)/y):1;a===o&&(m*=-1),isNaN(m)&&(m=0);var d=r?m*n*f/r:0,x=n?m*-r*l/n:0,b=(s+c)/2+Math.cos(i)*d-Math.sin(i)*x,M=(h+p)/2+Math.sin(i)*d+Math.cos(i)*x,w=[(l-d)/n,(f-x)/r],C=[(-1*l-d)/n,(-1*f-x)/r],A=_([1,0],w),P=_(w,C);return S(w,C)<=-1&&(P=Math.PI),S(w,C)>=1&&(P=0),0===o&&P>0&&(P-=2*Math.PI),1===o&&P<0&&(P+=2*Math.PI),{cx:b,cy:M,rx:v(t,[c,p])?0:n,ry:v(t,[c,p])?0:r,startAngle:A,endAngle:A+P,xRotation:i,arcFlag:a,sweepFlag:o}}var k=Math.sin,B=Math.cos,E=Math.atan2,I=Math.PI;function T(t,e,n,r,i,a,o){var s=e.stroke,h=e.lineWidth,u=n-i,c=r-a,p=E(c,u),l=new It({type:"path",canvas:t.get("canvas"),isArrowShape:!0,attrs:{path:"M"+10*B(I/6)+","+10*k(I/6)+" L0,0 L"+10*B(I/6)+",-"+10*k(I/6),stroke:s,lineWidth:h}});l.translate(i,a),l.rotateAtPoint(i,a,p),t.set(o?"startArrowShape":"endArrowShape",l)}function X(t,e,n,r,i,a,h){var u=e.startArrow,c=e.endArrow,p=e.stroke,l=e.lineWidth,f=h?u:c,g=f.d,v=f.fill,y=f.stroke,m=f.lineWidth,d=s(f,["d","fill","stroke","lineWidth"]),x=n-i,b=r-a,M=E(b,x);g&&(i-=B(M)*g,a-=k(M)*g);var w=new It({type:"path",canvas:t.get("canvas"),isArrowShape:!0,attrs:o(o({},d),{stroke:y||p,lineWidth:m||l,fill:v})});w.translate(i,a),w.rotateAtPoint(i,a,M),t.set(h?"startArrowShape":"endArrowShape",w)}function Y(t,e,n,r,i){var a=E(r-e,n-t);return{dx:B(a)*i,dy:k(a)*i}}function D(t,e,n,r,i,a){"object"===typeof e.startArrow?X(t,e,n,r,i,a,!0):e.startArrow?T(t,e,n,r,i,a,!0):t.set("startArrowShape",null)}function F(t,e,n,r,i,a){"object"===typeof e.endArrow?X(t,e,n,r,i,a,!1):e.endArrow?T(t,e,n,r,i,a,!1):t.set("startArrowShape",null)}var L={fill:"fillStyle",stroke:"strokeStyle",opacity:"globalAlpha"};function N(t,e){var n=e.attr();for(var r in n){var i=n[r],a=L[r]?L[r]:r;"matrix"===a&&i?t.transform(i[0],i[1],i[3],i[4],i[6],i[7]):"lineDash"===a&&t.setLineDash?Object(u["isArray"])(i)&&t.setLineDash(i):("strokeStyle"===a||"fillStyle"===a?i=A(t,e,i):"globalAlpha"===a&&(i*=t.globalAlpha),t[a]=i)}}function q(t,e,n){for(var r=0;r<e.length;r++){var i=e[r];i.cfg.visible?i.draw(t,n):i.skipDraw()}}function R(t,e,n){var r=t.get("refreshElements");Object(u["each"])(r,(function(e){if(e!==t){var n=e.cfg.parent;while(n&&n!==t&&!n.cfg.refresh)n.cfg.refresh=!0,n=n.cfg.parent}})),r[0]===t?H(e,n):z(e,n)}function z(t,e){for(var n=0;n<t.length;n++){var r=t[n];if(r.cfg.visible)if(r.cfg.hasChanged)r.cfg.refresh=!0,r.isGroup()&&H(r.cfg.children,e);else if(r.cfg.refresh)r.isGroup()&&z(r.cfg.children,e);else{var i=V(r,e);r.cfg.refresh=i,i&&r.isGroup()&&z(r.cfg.children,e)}}}function G(t){for(var e=0;e<t.length;e++){var n=t[e];n.cfg.hasChanged=!1,n.isGroup()&&!n.destroyed&&G(n.cfg.children)}}function H(t,e){for(var n=0;n<t.length;n++){var r=t[n];r.cfg.refresh=!0,r.isGroup()&&H(r.get("children"),e)}}function V(t,e){var n=t.cfg.cacheCanvasBBox,r=t.cfg.isInView&&n&&f(n,e);return r}function W(t,e,n,r){var i=n.path,a=n.startArrow,o=n.endArrow;if(i){var s=[0,0],h=[0,0],u={dx:0,dy:0};e.beginPath();for(var c=0;c<i.length;c++){var p=i[c],l=p[0];if(0===c&&a&&a.d){var f=t.getStartTangent();u=Y(f[0][0],f[0][1],f[1][0],f[1][1],a.d)}else if(c===i.length-2&&"Z"===i[c+1][0]&&o&&o.d){var g=i[c+1];if("Z"===g[0]){f=t.getEndTangent();u=Y(f[0][0],f[0][1],f[1][0],f[1][1],o.d)}}else if(c===i.length-1&&o&&o.d&&"Z"!==i[0]){f=t.getEndTangent();u=Y(f[0][0],f[0][1],f[1][0],f[1][1],o.d)}var v=u.dx,y=u.dy;switch(l){case"M":e.moveTo(p[1]-v,p[2]-y),h=[p[1],p[2]];break;case"L":e.lineTo(p[1]-v,p[2]-y);break;case"Q":e.quadraticCurveTo(p[1],p[2],p[3]-v,p[4]-y);break;case"C":e.bezierCurveTo(p[1],p[2],p[3],p[4],p[5]-v,p[6]-y);break;case"A":var m=void 0;r?(m=r[c],m||(m=j(s,p),r[c]=m)):m=j(s,p);var d=m.cx,x=m.cy,b=m.rx,M=m.ry,w=m.startAngle,C=m.endAngle,A=m.xRotation,P=m.sweepFlag;if(e.ellipse)e.ellipse(d,x,b,M,A,w,C,1-P);else{var O=b>M?b:M,S=b>M?1:b/M,_=b>M?M/b:1;e.translate(d,x),e.rotate(A),e.scale(S,_),e.arc(0,0,O,w,C,1-P),e.scale(1/S,1/_),e.rotate(-A),e.translate(-d,-x)}break;case"Z":e.closePath();break;default:break}if("Z"===l)s=h;else{var k=p.length;s=[p[k-2],p[k-1]]}}}}function Z(t,e){var n=t.get("canvas");n&&("remove"===e&&(t._cacheCanvasBBox=t.get("cacheCanvasBBox")),t.get("hasChanged")||(t.set("hasChanged",!0),t.cfg.parent&&t.cfg.parent.get("hasChanged")||(n.refreshElement(t,e,n),n.get("autoDraw")&&n.draw())))}function Q(t){var e;if(t.destroyed)e=t["_cacheCanvasBBox"];else{var n=t.get("cacheCanvasBBox"),r=n&&!(!n.width||!n.height),i=t.getCanvasBBox(),a=i&&!(!i.width||!i.height);r&&a?e=g(n,i):r?e=n:a&&(e=i)}return e}function U(t){if(!t.length)return null;var e=[],n=[],r=[],i=[];return Object(u["each"])(t,(function(t){var a=Q(t);a&&(e.push(a.minX),n.push(a.minY),r.push(a.maxX),i.push(a.maxY))})),{minX:Object(u["min"])(e),minY:Object(u["min"])(n),maxX:Object(u["max"])(r),maxY:Object(u["max"])(i)}}function J(t,e){return t&&e&&f(t,e)?{minX:Math.max(t.minX,e.minX),minY:Math.max(t.minY,e.minY),maxX:Math.min(t.maxX,e.maxX),maxY:Math.min(t.maxY,e.maxY)}:null}var $=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.onCanvasChange=function(t){Z(this,t)},e.prototype.getShapeBase=function(){return r},e.prototype.getGroupBase=function(){return e},e.prototype._applyClip=function(t,e){e&&(t.save(),N(t,e),e.createPath(t),t.restore(),t.clip(),e._afterDraw())},e.prototype.cacheCanvasBBox=function(){var t=this.cfg.children,e=[],n=[];Object(u["each"])(t,(function(t){var r=t.cfg.cacheCanvasBBox;r&&t.cfg.isInView&&(e.push(r.minX,r.maxX),n.push(r.minY,r.maxY))}));var r=null;if(e.length){var i=Object(u["min"])(e),a=Object(u["max"])(e),o=Object(u["min"])(n),s=Object(u["max"])(n);r={minX:i,minY:o,x:i,y:o,maxX:a,maxY:s,width:a-i,height:s-o};var h=this.cfg.canvas;if(h){var c=h.getViewRange();this.set("isInView",f(r,c))}}else this.set("isInView",!1);this.set("cacheCanvasBBox",r)},e.prototype.draw=function(t,e){var n=this.cfg.children,r=!e||this.cfg.refresh;n.length&&r&&(t.save(),N(t,this),this._applyClip(t,this.getClip()),q(t,n,e),t.restore(),this.cacheCanvasBBox()),this.cfg.refresh=null,this.set("hasChanged",!1)},e.prototype.skipDraw=function(){this.set("cacheCanvasBBox",null),this.set("hasChanged",!1)},e}(h["b"]),K=$,tt=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.getDefaultAttrs=function(){var e=t.prototype.getDefaultAttrs.call(this);return o(o({},e),{lineWidth:1,lineAppendWidth:0,strokeOpacity:1,fillOpacity:1})},e.prototype.getShapeBase=function(){return r},e.prototype.getGroupBase=function(){return K},e.prototype.onCanvasChange=function(t){Z(this,t)},e.prototype.calculateBBox=function(){var t=this.get("type"),e=this.getHitLineWidth(),n=Object(h["g"])(t),r=n(this),i=e/2,a=r.x-i,o=r.y-i,s=r.x+r.width+i,u=r.y+r.height+i;return{x:a,minX:a,y:o,minY:o,width:r.width+e,height:r.height+e,maxX:s,maxY:u}},e.prototype.isFill=function(){return!!this.attrs["fill"]||this.isClipShape()},e.prototype.isStroke=function(){return!!this.attrs["stroke"]},e.prototype._applyClip=function(t,e){e&&(t.save(),N(t,e),e.createPath(t),t.restore(),t.clip(),e._afterDraw())},e.prototype.draw=function(t,e){var n=this.cfg.clipShape;if(e){if(!1===this.cfg.refresh)return void this.set("hasChanged",!1);var r=this.getCanvasBBox();if(!f(e,r))return this.set("hasChanged",!1),void(this.cfg.isInView&&this._afterDraw())}t.save(),N(t,this),this._applyClip(t,n),this.drawPath(t),t.restore(),this._afterDraw()},e.prototype.getCanvasViewBox=function(){var t=this.cfg.canvas;return t?t.getViewRange():null},e.prototype.cacheCanvasBBox=function(){var t=this.getCanvasViewBox();if(t){var e=this.getCanvasBBox(),n=f(e,t);this.set("isInView",n),n?this.set("cacheCanvasBBox",e):this.set("cacheCanvasBBox",null)}},e.prototype._afterDraw=function(){this.cacheCanvasBBox(),this.set("hasChanged",!1),this.set("refresh",null)},e.prototype.skipDraw=function(){this.set("cacheCanvasBBox",null),this.set("isInView",null),this.set("hasChanged",!1)},e.prototype.drawPath=function(t){this.createPath(t),this.strokeAndFill(t),this.afterDrawPath(t)},e.prototype.fill=function(t){t.fill()},e.prototype.stroke=function(t){t.stroke()},e.prototype.strokeAndFill=function(t){var e=this.attrs,n=e.lineWidth,r=e.opacity,i=e.strokeOpacity,a=e.fillOpacity;this.isFill()&&(Object(u["isNil"])(a)||1===a?this.fill(t):(t.globalAlpha=a,this.fill(t),t.globalAlpha=r)),this.isStroke()&&n>0&&(Object(u["isNil"])(i)||1===i||(t.globalAlpha=i),this.stroke(t)),this.afterDrawPath(t)},e.prototype.createPath=function(t){},e.prototype.afterDrawPath=function(t){},e.prototype.isInShape=function(t,e){var n=this.isStroke(),r=this.isFill(),i=this.getHitLineWidth();return this.isInStrokeOrPath(t,e,n,r,i)},e.prototype.isInStrokeOrPath=function(t,e,n,r,i){return!1},e.prototype.getHitLineWidth=function(){if(!this.isStroke())return 0;var t=this.attrs;return t["lineWidth"]+t["lineAppendWidth"]},e}(h["c"]),et=tt,nt=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.getDefaultAttrs=function(){var e=t.prototype.getDefaultAttrs.call(this);return o(o({},e),{x:0,y:0,r:0})},e.prototype.isInStrokeOrPath=function(t,e,n,r,i){var a=this.attr(),o=a.x,s=a.y,h=a.r,u=i/2,c=p(o,s,t,e);return r&&n?c<=h+u:r?c<=h:!!n&&(c>=h-u&&c<=h+u)},e.prototype.createPath=function(t){var e=this.attr(),n=e.x,r=e.y,i=e.r;t.beginPath(),t.arc(n,r,i,0,2*Math.PI,!1),t.closePath()},e}(et),rt=nt;function it(t,e,n,r){return t/(n*n)+e/(r*r)}var at=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.getDefaultAttrs=function(){var e=t.prototype.getDefaultAttrs.call(this);return o(o({},e),{x:0,y:0,rx:0,ry:0})},e.prototype.isInStrokeOrPath=function(t,e,n,r,i){var a=this.attr(),o=i/2,s=a.x,h=a.y,u=a.rx,c=a.ry,p=(t-s)*(t-s),l=(e-h)*(e-h);return r&&n?it(p,l,u+o,c+o)<=1:r?it(p,l,u,c)<=1:!!n&&(it(p,l,u-o,c-o)>=1&&it(p,l,u+o,c+o)<=1)},e.prototype.createPath=function(t){var e=this.attr(),n=e.x,r=e.y,i=e.rx,a=e.ry;if(t.beginPath(),t.ellipse)t.ellipse(n,r,i,a,0,0,2*Math.PI,!1);else{var o=i>a?i:a,s=i>a?1:i/a,h=i>a?a/i:1;t.save(),t.translate(n,r),t.scale(s,h),t.arc(0,0,o,0,2*Math.PI),t.restore(),t.closePath()}},e}(et),ot=at;function st(t){return t instanceof HTMLElement&&Object(u["isString"])(t.nodeName)&&"CANVAS"===t.nodeName.toUpperCase()}var ht=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.getDefaultAttrs=function(){var e=t.prototype.getDefaultAttrs.call(this);return o(o({},e),{x:0,y:0,width:0,height:0})},e.prototype.initAttrs=function(t){this._setImage(t.img)},e.prototype.isStroke=function(){return!1},e.prototype.isOnlyHitBox=function(){return!0},e.prototype._afterLoading=function(){if(!0===this.get("toDraw")){var t=this.get("canvas");t?t.draw():this.createPath(this.get("context"))}},e.prototype._setImage=function(t){var e=this,n=this.attrs;if(Object(u["isString"])(t)){var r=new Image;r.onload=function(){if(e.destroyed)return!1;e.attr("img",r),e.set("loading",!1),e._afterLoading();var t=e.get("callback");t&&t.call(e)},r.crossOrigin="Anonymous",r.src=t,this.set("loading",!0)}else t instanceof Image?(n.width||(n.width=t.width),n.height||(n.height=t.height)):st(t)&&(n.width||(n.width=Number(t.getAttribute("width"))),n.height||(n.height,Number(t.getAttribute("height"))))},e.prototype.onAttrChange=function(e,n,r){t.prototype.onAttrChange.call(this,e,n,r),"img"===e&&this._setImage(n)},e.prototype.createPath=function(t){if(this.get("loading"))return this.set("toDraw",!0),void this.set("context",t);var e=this.attr(),n=e.x,r=e.y,i=e.width,a=e.height,o=e.sx,s=e.sy,h=e.swidth,c=e.sheight,p=e.img;(p instanceof Image||st(p))&&(Object(u["isNil"])(o)||Object(u["isNil"])(s)||Object(u["isNil"])(h)||Object(u["isNil"])(c)?t.drawImage(p,n,r,i,a):t.drawImage(p,o,s,h,c,n,r,i,a))},e}(et),ut=ht,ct=n("1183");function pt(t,e,n,r,i,a,o){var s=Math.min(t,n),h=Math.max(t,n),u=Math.min(e,r),c=Math.max(e,r),p=i/2;return a>=s-p&&a<=h+p&&o>=u-p&&o<=c+p&&ct["c"].pointToLine(t,e,n,r,a,o)<=i/2}var lt=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.getDefaultAttrs=function(){var e=t.prototype.getDefaultAttrs.call(this);return o(o({},e),{x1:0,y1:0,x2:0,y2:0,startArrow:!1,endArrow:!1})},e.prototype.initAttrs=function(t){this.setArrow()},e.prototype.onAttrChange=function(e,n,r){t.prototype.onAttrChange.call(this,e,n,r),this.setArrow()},e.prototype.setArrow=function(){var t=this.attr(),e=t.x1,n=t.y1,r=t.x2,i=t.y2,a=t.startArrow,o=t.endArrow;a&&D(this,t,r,i,e,n),o&&F(this,t,e,n,r,i)},e.prototype.isInStrokeOrPath=function(t,e,n,r,i){if(!n||!i)return!1;var a=this.attr(),o=a.x1,s=a.y1,h=a.x2,u=a.y2;return pt(o,s,h,u,i,t,e)},e.prototype.createPath=function(t){var e=this.attr(),n=e.x1,r=e.y1,i=e.x2,a=e.y2,o=e.startArrow,s=e.endArrow,h={dx:0,dy:0},u={dx:0,dy:0};o&&o.d&&(h=Y(n,r,i,a,e.startArrow.d)),s&&s.d&&(u=Y(n,r,i,a,e.endArrow.d)),t.beginPath(),t.moveTo(n+h.dx,r+h.dy),t.lineTo(i-u.dx,a-u.dy)},e.prototype.afterDrawPath=function(t){var e=this.get("startArrowShape"),n=this.get("endArrowShape");e&&e.draw(t),n&&n.draw(t)},e.prototype.getTotalLength=function(){var t=this.attr(),e=t.x1,n=t.y1,r=t.x2,i=t.y2;return ct["c"].length(e,n,r,i)},e.prototype.getPoint=function(t){var e=this.attr(),n=e.x1,r=e.y1,i=e.x2,a=e.y2;return ct["c"].pointAt(n,r,i,a,t)},e}(et),ft=lt,gt=n("2ef1"),vt={circle:function(t,e,n){return[["M",t-n,e],["A",n,n,0,1,0,t+n,e],["A",n,n,0,1,0,t-n,e]]},square:function(t,e,n){return[["M",t-n,e-n],["L",t+n,e-n],["L",t+n,e+n],["L",t-n,e+n],["Z"]]},diamond:function(t,e,n){return[["M",t-n,e],["L",t,e-n],["L",t+n,e],["L",t,e+n],["Z"]]},triangle:function(t,e,n){var r=n*Math.sin(1/3*Math.PI);return[["M",t-n,e+r],["L",t,e-r],["L",t+n,e+r],["Z"]]},"triangle-down":function(t,e,n){var r=n*Math.sin(1/3*Math.PI);return[["M",t-n,e-r],["L",t+n,e-r],["L",t,e+r],["Z"]]}},yt=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.initAttrs=function(t){this._resetParamsCache()},e.prototype._resetParamsCache=function(){this.set("paramsCache",{})},e.prototype.onAttrChange=function(e,n,r){t.prototype.onAttrChange.call(this,e,n,r),-1!==["symbol","x","y","r","radius"].indexOf(e)&&this._resetParamsCache()},e.prototype.isOnlyHitBox=function(){return!0},e.prototype._getR=function(t){return Object(u["isNil"])(t.r)?t.radius:t.r},e.prototype._getPath=function(){var t,n,r=this.attr(),i=r.x,a=r.y,o=r.symbol||"circle",s=this._getR(r);if(Object(u["isFunction"])(o))t=o,n=t(i,a,s),n=Object(gt["c"])(n);else{if(t=e.Symbols[o],!t)return console.warn(o+" marker is not supported."),null;n=t(i,a,s)}return n},e.prototype.createPath=function(t){var e=this._getPath(),n=this.get("paramsCache");W(this,t,{path:e},n)},e.Symbols=vt,e}(et),mt=yt;function dt(t,e,n){var r=Object(h["h"])();return t.createPath(r),r.isPointInPath(e,n)}var xt=1e-6;function bt(t){return Math.abs(t)<xt?0:t<0?-1:1}function Mt(t,e,n){return(n[0]-t[0])*(e[1]-t[1])===(e[0]-t[0])*(n[1]-t[1])&&Math.min(t[0],e[0])<=n[0]&&n[0]<=Math.max(t[0],e[0])&&Math.min(t[1],e[1])<=n[1]&&n[1]<=Math.max(t[1],e[1])}function wt(t,e,n){var r=!1,i=t.length;if(i<=2)return!1;for(var a=0;a<i;a++){var o=t[a],s=t[(a+1)%i];if(Mt(o,s,[e,n]))return!0;bt(o[1]-n)>0!==bt(s[1]-n)>0&&bt(e-(n-o[1])*(o[0]-s[0])/(o[1]-s[1])-o[0])<0&&(r=!r)}return r}var Ct=n("e897"),At=n("9fe7");function Pt(t,e,n,r,i,a,o,s){var h=(Math.atan2(s-e,o-t)+2*Math.PI)%(2*Math.PI);if(h<r||h>i)return!1;var u={x:t+n*Math.cos(h),y:e+n*Math.sin(h)};return p(u.x,u.y,o,s)<=a/2}var Ot=Ct["a"].transform;function St(t){for(var e=!1,n=t.length,r=0;r<n;r++){var i=t[r],a=i[0];if("C"===a||"A"===a||"Q"===a){e=!0;break}}return e}function _t(t,e,n,r,i){for(var a=!1,o=e/2,s=0;s<t.length;s++){var h=t[s],u=h.currentPoint,c=h.params,p=h.prePoint,f=h.box;if(!f||l(f.x-o,f.y-o,f.width+e,f.height+e,n,r)){switch(h.command){case"L":case"Z":a=pt(p[0],p[1],u[0],u[1],e,n,r);break;case"Q":var g=ct["e"].pointDistance(p[0],p[1],c[1],c[2],c[3],c[4],n,r);a=g<=e/2;break;case"C":var v=ct["b"].pointDistance(p[0],p[1],c[1],c[2],c[3],c[4],c[5],c[6],n,r,i);a=v<=e/2;break;case"A":var y=h.arcParams,m=y.cx,d=y.cy,x=y.rx,b=y.ry,M=y.startAngle,w=y.endAngle,C=y.xRotation,A=[n,r,1],P=x>b?x:b,O=x>b?1:x/b,S=x>b?b/x:1,_=Ot(null,[["t",-m,-d],["r",-C],["s",1/O,1/S]]);At["transformMat3"](A,A,_),a=Pt(0,0,P,M,w,e,A[0],A[1]);break;default:break}if(a)break}}return a}function jt(t){for(var e=t.length,n=[],r=[],i=[],a=0;a<e;a++){var o=t[a],s=o[0];"M"===s?(i.length&&(r.push(i),i=[]),i.push([o[1],o[2]])):"Z"===s?i.length&&(n.push(i),i=[]):i.push([o[1],o[2]])}return i.length>0&&r.push(i),{polygons:n,polylines:r}}var kt=o({hasArc:St,extractPolygons:jt,isPointInStroke:_t},h["e"]);function Bt(t,e,n){for(var r=!1,i=0;i<t.length;i++){var a=t[i];if(r=wt(a,e,n),r)break}return r}var Et=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.getDefaultAttrs=function(){var e=t.prototype.getDefaultAttrs.call(this);return o(o({},e),{startArrow:!1,endArrow:!1})},e.prototype.initAttrs=function(t){this._setPathArr(t.path),this.setArrow()},e.prototype.onAttrChange=function(e,n,r){t.prototype.onAttrChange.call(this,e,n,r),"path"===e&&this._setPathArr(n),this.setArrow()},e.prototype._setPathArr=function(t){this.attrs.path=Object(gt["c"])(t);var e=kt.hasArc(t);this.set("hasArc",e),this.set("paramsCache",{}),this.set("segments",null),this.set("curve",null),this.set("tCache",null),this.set("totalLength",null)},e.prototype.getSegments=function(){var t=this.get("segements");return t||(t=Object(gt["d"])(this.attr("path")),this.set("segments",t)),t},e.prototype.setArrow=function(){var t=this.attr(),e=t.startArrow,n=t.endArrow;if(e){var r=this.getStartTangent();D(this,t,r[0][0],r[0][1],r[1][0],r[1][1])}if(n){r=this.getEndTangent();F(this,t,r[0][0],r[0][1],r[1][0],r[1][1])}},e.prototype.isInStrokeOrPath=function(t,e,n,r,i){var a=this.getSegments(),o=this.get("hasArc"),s=!1;if(n){var h=this.getTotalLength();s=kt.isPointInStroke(a,i,t,e,h)}if(!s&&r)if(o)s=dt(this,t,e);else{var u=this.attr("path"),c=kt.extractPolygons(u);s=Bt(c.polygons,t,e)||Bt(c.polylines,t,e)}return s},e.prototype.createPath=function(t){var e=this.attr(),n=this.get("paramsCache");W(this,t,e,n)},e.prototype.afterDrawPath=function(t){var e=this.get("startArrowShape"),n=this.get("endArrowShape");e&&e.draw(t),n&&n.draw(t)},e.prototype.getTotalLength=function(){var t=this.get("totalLength");return Object(u["isNil"])(t)?(this._calculateCurve(),this._setTcache(),this.get("totalLength")):t},e.prototype.getPoint=function(t){var e,n,r=this.get("tCache");r||(this._calculateCurve(),this._setTcache(),r=this.get("tCache"));var i=this.get("curve");if(!r||0===r.length)return i?{x:i[0][1],y:i[0][2]}:null;Object(u["each"])(r,(function(r,i){t>=r[0]&&t<=r[1]&&(e=(t-r[0])/(r[1]-r[0]),n=i)}));var a=i[n];if(Object(u["isNil"])(a)||Object(u["isNil"])(n))return null;var o=a.length,s=i[n+1];return ct["b"].pointAt(a[o-2],a[o-1],s[1],s[2],s[3],s[4],s[5],s[6],e)},e.prototype._calculateCurve=function(){var t=this.attr().path;this.set("curve",kt.pathToCurve(t))},e.prototype._setTcache=function(){var t,e,n,r,i=0,a=0,o=[],s=this.get("curve");s&&(Object(u["each"])(s,(function(t,e){n=s[e+1],r=t.length,n&&(i+=ct["b"].length(t[r-2],t[r-1],n[1],n[2],n[3],n[4],n[5],n[6])||0)})),this.set("totalLength",i),0!==i?(Object(u["each"])(s,(function(h,u){n=s[u+1],r=h.length,n&&(t=[],t[0]=a/i,e=ct["b"].length(h[r-2],h[r-1],n[1],n[2],n[3],n[4],n[5],n[6]),a+=e||0,t[1]=a/i,o.push(t))})),this.set("tCache",o)):this.set("tCache",[]))},e.prototype.getStartTangent=function(){var t,e=this.getSegments();if(e.length>1){var n=e[0].currentPoint,r=e[1].currentPoint,i=e[1].startTangent;t=[],i?(t.push([n[0]-i[0],n[1]-i[1]]),t.push([n[0],n[1]])):(t.push([r[0],r[1]]),t.push([n[0],n[1]]))}return t},e.prototype.getEndTangent=function(){var t,e=this.getSegments(),n=e.length;if(n>1){var r=e[n-2].currentPoint,i=e[n-1].currentPoint,a=e[n-1].endTangent;t=[],a?(t.push([i[0]-a[0],i[1]-a[1]]),t.push([i[0],i[1]])):(t.push([r[0],r[1]]),t.push([i[0],i[1]]))}return t},e}(et),It=Et;function Tt(t,e,n,r,i){var a=t.length;if(a<2)return!1;for(var o=0;o<a-1;o++){var s=t[o][0],h=t[o][1],u=t[o+1][0],c=t[o+1][1];if(pt(s,h,u,c,e,n,r))return!0}if(i){var p=t[0],l=t[a-1];if(pt(p[0],p[1],l[0],l[1],e,n,r))return!0}return!1}var Xt=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.isInStrokeOrPath=function(t,e,n,r,i){var a=this.attr().points,o=!1;return n&&(o=Tt(a,i,t,e,!0)),!o&&r&&(o=wt(a,t,e)),o},e.prototype.createPath=function(t){var e=this.attr(),n=e.points;if(!(n.length<2)){t.beginPath();for(var r=0;r<n.length;r++){var i=n[r];0===r?t.moveTo(i[0],i[1]):t.lineTo(i[0],i[1])}t.closePath()}},e}(et),Yt=Xt,Dt=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.getDefaultAttrs=function(){var e=t.prototype.getDefaultAttrs.call(this);return o(o({},e),{startArrow:!1,endArrow:!1})},e.prototype.initAttrs=function(t){this.setArrow()},e.prototype.onAttrChange=function(e,n,r){t.prototype.onAttrChange.call(this,e,n,r),this.setArrow(),-1!==["points"].indexOf(e)&&this._resetCache()},e.prototype._resetCache=function(){this.set("totalLength",null),this.set("tCache",null)},e.prototype.setArrow=function(){var t=this.attr(),e=this.attrs,n=e.points,r=e.startArrow,i=e.endArrow,a=n.length,o=n[0][0],s=n[0][1],h=n[a-1][0],u=n[a-1][1];r&&D(this,t,n[1][0],n[1][1],o,s),i&&F(this,t,n[a-2][0],n[a-2][1],h,u)},e.prototype.isFill=function(){return!1},e.prototype.isInStrokeOrPath=function(t,e,n,r,i){if(!n||!i)return!1;var a=this.attr().points;return Tt(a,i,t,e,!1)},e.prototype.isStroke=function(){return!0},e.prototype.createPath=function(t){var e=this.attr(),n=e.points,r=e.startArrow,i=e.endArrow,a=n.length;if(!(n.length<2)){var o=n[0][0],s=n[0][1],h=n[a-1][0],u=n[a-1][1];if(r&&r.d){var c=Y(o,s,n[1][0],n[1][1],r.d);o+=c.dx,s+=c.dy}if(i&&i.d){c=Y(n[a-2][0],n[a-2][1],h,u,i.d);h-=c.dx,u-=c.dy}t.beginPath(),t.moveTo(o,s);for(var p=0;p<a-1;p++){var l=n[p];t.lineTo(l[0],l[1])}t.lineTo(h,u)}},e.prototype.afterDrawPath=function(t){var e=this.get("startArrowShape"),n=this.get("endArrowShape");e&&e.draw(t),n&&n.draw(t)},e.prototype.getTotalLength=function(){var t=this.attr().points,e=this.get("totalLength");return Object(u["isNil"])(e)?(this.set("totalLength",ct["d"].length(t)),this.get("totalLength")):e},e.prototype.getPoint=function(t){var e,n,r=this.attr().points,i=this.get("tCache");return i||(this._setTcache(),i=this.get("tCache")),Object(u["each"])(i,(function(r,i){t>=r[0]&&t<=r[1]&&(e=(t-r[0])/(r[1]-r[0]),n=i)})),ct["c"].pointAt(r[n][0],r[n][1],r[n+1][0],r[n+1][1],e)},e.prototype._setTcache=function(){var t=this.attr().points;if(t&&0!==t.length){var e=this.getTotalLength();if(!(e<=0)){var n,r,i=0,a=[];Object(u["each"])(t,(function(o,s){t[s+1]&&(n=[],n[0]=i/e,r=ct["c"].length(o[0],o[1],t[s+1][0],t[s+1][1]),i+=r,n[1]=i/e,a.push(n))})),this.set("tCache",a)}}},e.prototype.getStartTangent=function(){var t=this.attr().points,e=[];return e.push([t[1][0],t[1][1]]),e.push([t[0][0],t[0][1]]),e},e.prototype.getEndTangent=function(){var t=this.attr().points,e=t.length-1,n=[];return n.push([t[e-1][0],t[e-1][1]]),n.push([t[e][0],t[e][1]]),n},e}(et),Ft=Dt;function Lt(t,e,n,r,i,a,o){var s=i/2;return l(t-s,e-s,n,i,a,o)||l(t+n-s,e-s,i,r,a,o)||l(t+s,e+r-s,n,i,a,o)||l(t-s,e+s,i,r,a,o)}function Nt(t,e,n,r,i,a,o,s){return pt(t+i,e,t+n-i,e,a,o,s)||pt(t+n,e+i,t+n,e+r-i,a,o,s)||pt(t+n-i,e+r,t+i,e+r,a,o,s)||pt(t,e+r-i,t,e+i,a,o,s)||Pt(t+n-i,e+i,i,1.5*Math.PI,2*Math.PI,a,o,s)||Pt(t+n-i,e+r-i,i,0,.5*Math.PI,a,o,s)||Pt(t+i,e+r-i,i,.5*Math.PI,Math.PI,a,o,s)||Pt(t+i,e+i,i,Math.PI,1.5*Math.PI,a,o,s)}var qt=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.getDefaultAttrs=function(){var e=t.prototype.getDefaultAttrs.call(this);return o(o({},e),{x:0,y:0,width:0,height:0,radius:0})},e.prototype.isInStrokeOrPath=function(t,e,n,r,i){var a=this.attr(),o=a.x,s=a.y,h=a.width,u=a.height,c=a.radius;if(c){var p=!1;return n&&(p=Nt(o,s,h,u,c,i,t,e)),!p&&r&&(p=dt(this,t,e)),p}var f=i/2;return r&&n?l(o-f,s-f,h+f,u+f,t,e):r?l(o,s,h,u,t,e):n?Lt(o,s,h,u,i,t,e):void 0},e.prototype.createPath=function(t){var e=this.attr(),n=e.x,r=e.y,i=e.width,a=e.height,o=e.radius;if(t.beginPath(),0===o)t.rect(n,r,i,a);else{var s=P(o),h=s[0],u=s[1],c=s[2],p=s[3];t.moveTo(n+h,r),t.lineTo(n+i-u,r),0!==u&&t.arc(n+i-u,r+u,u,-Math.PI/2,0),t.lineTo(n+i,r+a-c),0!==c&&t.arc(n+i-c,r+a-c,c,0,Math.PI/2),t.lineTo(n+p,r+a),0!==p&&t.arc(n+p,r+a-p,p,Math.PI/2,Math.PI),t.lineTo(n,r+h),0!==h&&t.arc(n+h,r+h,h,Math.PI,1.5*Math.PI),t.closePath()}},e}(et),Rt=qt,zt=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.getDefaultAttrs=function(){var e=t.prototype.getDefaultAttrs.call(this);return o(o({},e),{x:0,y:0,text:null,fontSize:12,fontFamily:"sans-serif",fontStyle:"normal",fontWeight:"normal",fontVariant:"normal",textAlign:"start",textBaseline:"bottom"})},e.prototype.isOnlyHitBox=function(){return!0},e.prototype.initAttrs=function(t){this._assembleFont(),t.text&&this._setText(t.text)},e.prototype._assembleFont=function(){var t=this.attrs;t.font=Object(h["f"])(t)},e.prototype._setText=function(t){var e=null;Object(u["isString"])(t)&&-1!==t.indexOf("\n")&&(e=t.split("\n")),this.set("textArr",e)},e.prototype.onAttrChange=function(e,n,r){t.prototype.onAttrChange.call(this,e,n,r),e.startsWith("font")&&this._assembleFont(),"text"===e&&this._setText(n)},e.prototype._getSpaceingY=function(){var t=this.attrs,e=t.lineHeight,n=1*t.fontSize;return e?e-n:.14*n},e.prototype._drawTextArr=function(t,e,n){var r,i=this.attrs,a=i.textBaseline,o=i.x,s=i.y,c=1*i.fontSize,p=this._getSpaceingY(),l=Object(h["i"])(i.text,i.fontSize,i.lineHeight);Object(u["each"])(e,(function(e,i){r=s+i*(p+c)-l+c,"middle"===a&&(r+=l-c-(l-c)/2),"top"===a&&(r+=l-c),Object(u["isNil"])(e)||(n?t.fillText(e,o,r):t.strokeText(e,o,r))}))},e.prototype._drawText=function(t,e){var n=this.attr(),r=n.x,i=n.y,a=this.get("textArr");if(a)this._drawTextArr(t,a,e);else{var o=n.text;Object(u["isNil"])(o)||(e?t.fillText(o,r,i):t.strokeText(o,r,i))}},e.prototype.strokeAndFill=function(t){var e=this.attrs,n=e.lineWidth,r=e.opacity,i=e.strokeOpacity,a=e.fillOpacity;this.isStroke()&&n>0&&(Object(u["isNil"])(i)||1===i||(t.globalAlpha=r),this.stroke(t)),this.isFill()&&(Object(u["isNil"])(a)||1===a?this.fill(t):(t.globalAlpha=a,this.fill(t),t.globalAlpha=r)),this.afterDrawPath(t)},e.prototype.fill=function(t){this._drawText(t,!0)},e.prototype.stroke=function(t){this._drawText(t,!1)},e}(et),Gt=zt;function Ht(t,e){if(e){var n=Object(h["j"])(e);return Object(h["l"])(n,t)}return t}function Vt(t,e,n){var r=t.getTotalMatrix();if(r){var i=Ht([e,n,1],r),a=i[0],o=i[1];return[a,o]}return[e,n]}function Wt(t,e,n){if(t.isCanvas&&t.isCanvas())return!0;if(!Object(h["k"])(t)||!1===t.cfg.isInView)return!1;if(t.cfg.clipShape){var r=Vt(t,e,n),i=r[0],a=r[1];if(t.isClipped(i,a))return!1}var o=t.cfg.cacheCanvasBBox||t.getCanvasBBox();return e>=o.minX&&e<=o.maxX&&n>=o.minY&&n<=o.maxY}function Zt(t,e,n){if(!Wt(t,e,n))return null;for(var r=null,i=t.getChildren(),a=i.length,o=a-1;o>=0;o--){var s=i[o];if(s.isGroup())r=Zt(s,e,n);else if(Wt(s,e,n)){var h=s,u=Vt(s,e,n),c=u[0],p=u[1];h.isInShape(c,p)&&(r=s)}if(r)break}return r}var Qt=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return a(e,t),e.prototype.getDefaultCfg=function(){var e=t.prototype.getDefaultCfg.call(this);return e["renderer"]="canvas",e["autoDraw"]=!0,e["localRefresh"]=!0,e["refreshElements"]=[],e["clipView"]=!0,e["quickHit"]=!1,e},e.prototype.onCanvasChange=function(t){"attr"!==t&&"sort"!==t&&"changeSize"!==t||(this.set("refreshElements",[this]),this.draw())},e.prototype.getShapeBase=function(){return r},e.prototype.getGroupBase=function(){return K},e.prototype.getPixelRatio=function(){var t=this.get("pixelRatio")||c();return t>=1?Math.ceil(t):1},e.prototype.getViewRange=function(){return{minX:0,minY:0,maxX:this.cfg.width,maxY:this.cfg.height}},e.prototype.createDom=function(){var t=document.createElement("canvas"),e=t.getContext("2d");return this.set("context",e),t},e.prototype.setDOMSize=function(e,n){t.prototype.setDOMSize.call(this,e,n);var r=this.get("context"),i=this.get("el"),a=this.getPixelRatio();i.width=a*e,i.height=a*n,a>1&&r.scale(a,a)},e.prototype.clear=function(){t.prototype.clear.call(this),this._clearFrame();var e=this.get("context"),n=this.get("el");e.clearRect(0,0,n.width,n.height)},e.prototype.getShape=function(e,n){var r;return r=this.get("quickHit")?Zt(this,e,n):t.prototype.getShape.call(this,e,n,null),r},e.prototype._getRefreshRegion=function(){var t,e=this.get("refreshElements"),n=this.getViewRange();if(e.length&&e[0]===this)t=n;else if(t=U(e),t){t.minX=Math.floor(t.minX),t.minY=Math.floor(t.minY),t.maxX=Math.ceil(t.maxX),t.maxY=Math.ceil(t.maxY),t.maxY+=1;var r=this.get("clipView");r&&(t=J(t,n))}return t},e.prototype.refreshElement=function(t){var e=this.get("refreshElements");e.push(t)},e.prototype._clearFrame=function(){var t=this.get("drawFrame");t&&(Object(u["clearAnimationFrame"])(t),this.set("drawFrame",null),this.set("refreshElements",[]))},e.prototype.draw=function(){var t=this.get("drawFrame");this.get("autoDraw")&&t||this._startDraw()},e.prototype._drawAll=function(){var t=this.get("context"),e=this.get("el"),n=this.getChildren();t.clearRect(0,0,e.width,e.height),N(t,this),q(t,n),this.set("refreshElements",[])},e.prototype._drawRegion=function(){var t=this.get("context"),e=this.get("refreshElements"),n=this.getChildren(),r=this._getRefreshRegion();r?(t.clearRect(r.minX,r.minY,r.maxX-r.minX,r.maxY-r.minY),t.save(),t.beginPath(),t.rect(r.minX,r.minY,r.maxX-r.minX,r.maxY-r.minY),t.clip(),N(t,this),R(this,n,r),q(t,n,r),t.restore()):e.length&&G(e),Object(u["each"])(e,(function(t){t.get("hasChanged")&&t.set("hasChanged",!1)})),this.set("refreshElements",[])},e.prototype._startDraw=function(){var t=this,e=this.get("drawFrame");e||(e=Object(u["requestAnimationFrame"])((function(){t.get("localRefresh")?t._drawRegion():t._drawAll(),t.set("drawFrame",null)})),this.set("drawFrame",e))},e.prototype.skipDraw=function(){},e.prototype.removeDom=function(){var t=this.get("el");t.width=0,t.height=0,t.parentNode.removeChild(t)},e}(h["a"]),Ut=Qt},6855:function(t,e,n){"use strict";n.d(e,"d",(function(){return z})),n.d(e,"a",(function(){return Zt})),n.d(e,"b",(function(){return Ut})),n.d(e,"c",(function(){return $t})),n.d(e,"e",(function(){return r})),n.d(e,"g",(function(){return ee})),n.d(e,"i",(function(){return pe})),n.d(e,"f",(function(){return ge})),n.d(e,"k",(function(){return U})),n.d(e,"l",(function(){return nt})),n.d(e,"j",(function(){return rt})),n.d(e,"h",(function(){return ce}));var r={};n.r(r),n.d(r,"catmullRomToBezier",(function(){return u})),n.d(r,"fillPath",(function(){return T})),n.d(r,"fillPathByDiff",(function(){return F})),n.d(r,"formatPath",(function(){return q})),n.d(r,"intersection",(function(){return k})),n.d(r,"parsePathArray",(function(){return m})),n.d(r,"parsePathString",(function(){return h})),n.d(r,"pathToAbsolute",(function(){return p})),n.d(r,"pathToCurve",(function(){return v})),n.d(r,"rectPath",(function(){return C}));var i=n("8937"),a="\t\n\v\f\r   ᠎             　\u2028\u2029",o=new RegExp("([a-z])["+a+",]*((-?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?["+a+"]*,?["+a+"]*)+)","ig"),s=new RegExp("(-?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?)["+a+"]*,?["+a+"]*","ig"),h=function(t){if(!t)return null;if(Object(i["isArray"])(t))return t;var e={a:7,c:6,o:2,h:1,l:2,m:2,r:4,q:4,s:4,t:2,v:1,u:3,z:0},n=[];return String(t).replace(o,(function(r,i,a){var o=[],h=i.toLowerCase();if(a.replace(s,(function(t,e){e&&o.push(+e)})),"m"===h&&o.length>2&&(n.push([i].concat(o.splice(0,2))),h="l",i="m"===i?"l":"L"),"o"===h&&1===o.length&&n.push([i,o[0]]),"r"===h)n.push([i].concat(o));else while(o.length>=e[h])if(n.push([i].concat(o.splice(0,e[h]))),!e[h])break;return t})),n},u=function(t,e){for(var n=[],r=0,i=t.length;i-2*!e>r;r+=2){var a=[{x:+t[r-2],y:+t[r-1]},{x:+t[r],y:+t[r+1]},{x:+t[r+2],y:+t[r+3]},{x:+t[r+4],y:+t[r+5]}];e?r?i-4===r?a[3]={x:+t[0],y:+t[1]}:i-2===r&&(a[2]={x:+t[0],y:+t[1]},a[3]={x:+t[2],y:+t[3]}):a[0]={x:+t[i-2],y:+t[i-1]}:i-4===r?a[3]=a[2]:r||(a[0]={x:+t[r],y:+t[r+1]}),n.push(["C",(-a[0].x+6*a[1].x+a[2].x)/6,(-a[0].y+6*a[1].y+a[2].y)/6,(a[1].x+6*a[2].x-a[3].x)/6,(a[1].y+6*a[2].y-a[3].y)/6,a[2].x,a[2].y])}return n},c=function(t,e,n,r,i){var a=[];if(null===i&&null===r&&(r=n),t=+t,e=+e,n=+n,r=+r,null!==i){var o=Math.PI/180,s=t+n*Math.cos(-r*o),h=t+n*Math.cos(-i*o),u=e+n*Math.sin(-r*o),c=e+n*Math.sin(-i*o);a=[["M",s,u],["A",n,n,0,+(i-r>180),0,h,c]]}else a=[["M",t,e],["m",0,-r],["a",n,r,0,1,1,0,2*r],["a",n,r,0,1,1,0,-2*r],["z"]];return a},p=function(t){if(t=h(t),!t||!t.length)return[["M",0,0]];var e,n,r=[],i=0,a=0,o=0,s=0,p=0;"M"===t[0][0]&&(i=+t[0][1],a=+t[0][2],o=i,s=a,p++,r[0]=["M",i,a]);for(var l=3===t.length&&"M"===t[0][0]&&"R"===t[1][0].toUpperCase()&&"Z"===t[2][0].toUpperCase(),f=void 0,g=void 0,v=p,y=t.length;v<y;v++){if(r.push(f=[]),g=t[v],e=g[0],e!==e.toUpperCase())switch(f[0]=e.toUpperCase(),f[0]){case"A":f[1]=g[1],f[2]=g[2],f[3]=g[3],f[4]=g[4],f[5]=g[5],f[6]=+g[6]+i,f[7]=+g[7]+a;break;case"V":f[1]=+g[1]+a;break;case"H":f[1]=+g[1]+i;break;case"R":n=[i,a].concat(g.slice(1));for(var m=2,d=n.length;m<d;m++)n[m]=+n[m]+i,n[++m]=+n[m]+a;r.pop(),r=r.concat(u(n,l));break;case"O":r.pop(),n=c(i,a,g[1],g[2]),n.push(n[0]),r=r.concat(n);break;case"U":r.pop(),r=r.concat(c(i,a,g[1],g[2],g[3])),f=["U"].concat(r[r.length-1].slice(-2));break;case"M":o=+g[1]+i,s=+g[2]+a;break;default:for(m=1,d=g.length;m<d;m++)f[m]=+g[m]+(m%2?i:a)}else if("R"===e)n=[i,a].concat(g.slice(1)),r.pop(),r=r.concat(u(n,l)),f=["R"].concat(g.slice(-2));else if("O"===e)r.pop(),n=c(i,a,g[1],g[2]),n.push(n[0]),r=r.concat(n);else if("U"===e)r.pop(),r=r.concat(c(i,a,g[1],g[2],g[3])),f=["U"].concat(r[r.length-1].slice(-2));else for(var x=0,b=g.length;x<b;x++)f[x]=g[x];if(e=e.toUpperCase(),"O"!==e)switch(f[0]){case"Z":i=+o,a=+s;break;case"H":i=f[1];break;case"V":a=f[1];break;case"M":o=f[f.length-2],s=f[f.length-1];break;default:i=f[f.length-2],a=f[f.length-1]}}return r},l=function(t,e,n,r){return[t,e,n,r,n,r]},f=function(t,e,n,r,i,a){var o=1/3,s=2/3;return[o*t+s*n,o*e+s*r,o*i+s*n,o*a+s*r,i,a]},g=function(t,e,n,r,i,a,o,s,h,u){n===r&&(n+=1);var c,p,l,f,v,y=120*Math.PI/180,m=Math.PI/180*(+i||0),d=[],x=function(t,e,n){var r=t*Math.cos(n)-e*Math.sin(n),i=t*Math.sin(n)+e*Math.cos(n);return{x:r,y:i}};if(u)p=u[0],l=u[1],f=u[2],v=u[3];else{c=x(t,e,-m),t=c.x,e=c.y,c=x(s,h,-m),s=c.x,h=c.y,t===s&&e===h&&(s+=1,h+=1);var b=(t-s)/2,M=(e-h)/2,w=b*b/(n*n)+M*M/(r*r);w>1&&(w=Math.sqrt(w),n*=w,r*=w);var C=n*n,A=r*r,P=(a===o?-1:1)*Math.sqrt(Math.abs((C*A-C*M*M-A*b*b)/(C*M*M+A*b*b)));f=P*n*M/r+(t+s)/2,v=P*-r*b/n+(e+h)/2,p=Math.asin(((e-v)/r).toFixed(9)),l=Math.asin(((h-v)/r).toFixed(9)),p=t<f?Math.PI-p:p,l=s<f?Math.PI-l:l,p<0&&(p=2*Math.PI+p),l<0&&(l=2*Math.PI+l),o&&p>l&&(p-=2*Math.PI),!o&&l>p&&(l-=2*Math.PI)}var O=l-p;if(Math.abs(O)>y){var S=l,_=s,j=h;l=p+y*(o&&l>p?1:-1),s=f+n*Math.cos(l),h=v+r*Math.sin(l),d=g(s,h,n,r,i,0,o,_,j,[l,S,f,v])}O=l-p;var k=Math.cos(p),B=Math.sin(p),E=Math.cos(l),I=Math.sin(l),T=Math.tan(O/4),X=4/3*n*T,Y=4/3*r*T,D=[t,e],F=[t+X*B,e-Y*k],L=[s+X*I,h-Y*E],N=[s,h];if(F[0]=2*D[0]-F[0],F[1]=2*D[1]-F[1],u)return[F,L,N].concat(d);d=[F,L,N].concat(d).join().split(",");for(var q=[],R=0,z=d.length;R<z;R++)q[R]=R%2?x(d[R-1],d[R],m).y:x(d[R],d[R+1],m).x;return q},v=function(t,e){var n,r=p(t),i=e&&p(e),a={x:0,y:0,bx:0,by:0,X:0,Y:0,qx:null,qy:null},o={x:0,y:0,bx:0,by:0,X:0,Y:0,qx:null,qy:null},s=[],h=[],u="",c="",v=function(t,e,n){var r,i;if(!t)return["C",e.x,e.y,e.x,e.y,e.x,e.y];switch(!(t[0]in{T:1,Q:1})&&(e.qx=e.qy=null),t[0]){case"M":e.X=t[1],e.Y=t[2];break;case"A":t=["C"].concat(g.apply(0,[e.x,e.y].concat(t.slice(1))));break;case"S":"C"===n||"S"===n?(r=2*e.x-e.bx,i=2*e.y-e.by):(r=e.x,i=e.y),t=["C",r,i].concat(t.slice(1));break;case"T":"Q"===n||"T"===n?(e.qx=2*e.x-e.qx,e.qy=2*e.y-e.qy):(e.qx=e.x,e.qy=e.y),t=["C"].concat(f(e.x,e.y,e.qx,e.qy,t[1],t[2]));break;case"Q":e.qx=t[1],e.qy=t[2],t=["C"].concat(f(e.x,e.y,t[1],t[2],t[3],t[4]));break;case"L":t=["C"].concat(l(e.x,e.y,t[1],t[2]));break;case"H":t=["C"].concat(l(e.x,e.y,t[1],e.y));break;case"V":t=["C"].concat(l(e.x,e.y,e.x,t[1]));break;case"Z":t=["C"].concat(l(e.x,e.y,e.X,e.Y));break;default:break}return t},y=function(t,e){if(t[e].length>7){t[e].shift();var a=t[e];while(a.length)s[e]="A",i&&(h[e]="A"),t.splice(e++,0,["C"].concat(a.splice(0,6)));t.splice(e,1),n=Math.max(r.length,i&&i.length||0)}},m=function(t,e,a,o,s){t&&e&&"M"===t[s][0]&&"M"!==e[s][0]&&(e.splice(s,0,["M",o.x,o.y]),a.bx=0,a.by=0,a.x=t[s][1],a.y=t[s][2],n=Math.max(r.length,i&&i.length||0))};n=Math.max(r.length,i&&i.length||0);for(var d=0;d<n;d++){r[d]&&(u=r[d][0]),"C"!==u&&(s[d]=u,d&&(c=s[d-1])),r[d]=v(r[d],a,c),"A"!==s[d]&&"C"===u&&(s[d]="C"),y(r,d),i&&(i[d]&&(u=i[d][0]),"C"!==u&&(h[d]=u,d&&(c=h[d-1])),i[d]=v(i[d],o,c),"A"!==h[d]&&"C"===u&&(h[d]="C"),y(i,d)),m(r,i,a,o,d),m(i,r,o,a,d);var x=r[d],b=i&&i[d],M=x.length,w=i&&b.length;a.x=x[M-2],a.y=x[M-1],a.bx=parseFloat(x[M-4])||a.x,a.by=parseFloat(x[M-3])||a.y,o.bx=i&&(parseFloat(b[w-4])||o.x),o.by=i&&(parseFloat(b[w-3])||o.y),o.x=i&&b[w-2],o.y=i&&b[w-1]}return i?[r,i]:r},y=/,?([a-z]),?/gi,m=function(t){return t.join(",").replace(y,"$1")},d=function(t,e,n,r,i){var a=-3*e+9*n-9*r+3*i,o=t*a+6*e-12*n+6*r;return t*o-3*e+3*n},x=function(t,e,n,r,i,a,o,s,h){null===h&&(h=1),h=h>1?1:h<0?0:h;for(var u=h/2,c=12,p=[-.1252,.1252,-.3678,.3678,-.5873,.5873,-.7699,.7699,-.9041,.9041,-.9816,.9816],l=[.2491,.2491,.2335,.2335,.2032,.2032,.1601,.1601,.1069,.1069,.0472,.0472],f=0,g=0;g<c;g++){var v=u*p[g]+u,y=d(v,t,n,i,o),m=d(v,e,r,a,s),x=y*y+m*m;f+=l[g]*Math.sqrt(x)}return u*f},b=function(t,e,n,r,i,a,o,s){for(var h,u,c,p,l=[],f=[[],[]],g=0;g<2;++g)if(0===g?(u=6*t-12*n+6*i,h=-3*t+9*n-9*i+3*o,c=3*n-3*t):(u=6*e-12*r+6*a,h=-3*e+9*r-9*a+3*s,c=3*r-3*e),Math.abs(h)<1e-12){if(Math.abs(u)<1e-12)continue;p=-c/u,p>0&&p<1&&l.push(p)}else{var v=u*u-4*c*h,y=Math.sqrt(v);if(!(v<0)){var m=(-u+y)/(2*h);m>0&&m<1&&l.push(m);var d=(-u-y)/(2*h);d>0&&d<1&&l.push(d)}}var x,b=l.length,M=b;while(b--)p=l[b],x=1-p,f[0][b]=x*x*x*t+3*x*x*p*n+3*x*p*p*i+p*p*p*o,f[1][b]=x*x*x*e+3*x*x*p*r+3*x*p*p*a+p*p*p*s;return f[0][M]=t,f[1][M]=e,f[0][M+1]=o,f[1][M+1]=s,f[0].length=f[1].length=M+2,{min:{x:Math.min.apply(0,f[0]),y:Math.min.apply(0,f[1])},max:{x:Math.max.apply(0,f[0]),y:Math.max.apply(0,f[1])}}},M=function(t,e,n,r,i,a,o,s){if(!(Math.max(t,n)<Math.min(i,o)||Math.min(t,n)>Math.max(i,o)||Math.max(e,r)<Math.min(a,s)||Math.min(e,r)>Math.max(a,s))){var h=(t*r-e*n)*(i-o)-(t-n)*(i*s-a*o),u=(t*r-e*n)*(a-s)-(e-r)*(i*s-a*o),c=(t-n)*(a-s)-(e-r)*(i-o);if(c){var p=h/c,l=u/c,f=+p.toFixed(2),g=+l.toFixed(2);if(!(f<+Math.min(t,n).toFixed(2)||f>+Math.max(t,n).toFixed(2)||f<+Math.min(i,o).toFixed(2)||f>+Math.max(i,o).toFixed(2)||g<+Math.min(e,r).toFixed(2)||g>+Math.max(e,r).toFixed(2)||g<+Math.min(a,s).toFixed(2)||g>+Math.max(a,s).toFixed(2)))return{x:p,y:l}}}},w=function(t,e,n){return e>=t.x&&e<=t.x+t.width&&n>=t.y&&n<=t.y+t.height},C=function(t,e,n,r,i){if(i)return[["M",+t+ +i,e],["l",n-2*i,0],["a",i,i,0,0,1,i,i],["l",0,r-2*i],["a",i,i,0,0,1,-i,i],["l",2*i-n,0],["a",i,i,0,0,1,-i,-i],["l",0,2*i-r],["a",i,i,0,0,1,i,-i],["z"]];var a=[["M",t,e],["l",n,0],["l",0,r],["l",-n,0],["z"]];return a.parsePathArray=m,a},A=function(t,e,n,r){return null===t&&(t=e=n=r=0),null===e&&(e=t.y,n=t.width,r=t.height,t=t.x),{x:t,y:e,width:n,w:n,height:r,h:r,x2:t+n,y2:e+r,cx:t+n/2,cy:e+r/2,r1:Math.min(n,r)/2,r2:Math.max(n,r)/2,r0:Math.sqrt(n*n+r*r)/2,path:C(t,e,n,r),vb:[t,e,n,r].join(" ")}},P=function(t,e){return t=A(t),e=A(e),w(e,t.x,t.y)||w(e,t.x2,t.y)||w(e,t.x,t.y2)||w(e,t.x2,t.y2)||w(t,e.x,e.y)||w(t,e.x2,e.y)||w(t,e.x,e.y2)||w(t,e.x2,e.y2)||(t.x<e.x2&&t.x>e.x||e.x<t.x2&&e.x>t.x)&&(t.y<e.y2&&t.y>e.y||e.y<t.y2&&e.y>t.y)},O=function(t,e,n,r,a,o,s,h){Object(i["isArray"])(t)||(t=[t,e,n,r,a,o,s,h]);var u=b.apply(null,t);return A(u.min.x,u.min.y,u.max.x-u.min.x,u.max.y-u.min.y)},S=function(t,e,n,r,i,a,o,s,h){var u=1-h,c=Math.pow(u,3),p=Math.pow(u,2),l=h*h,f=l*h,g=c*t+3*p*h*n+3*u*h*h*i+f*o,v=c*e+3*p*h*r+3*u*h*h*a+f*s,y=t+2*h*(n-t)+l*(i-2*n+t),m=e+2*h*(r-e)+l*(a-2*r+e),d=n+2*h*(i-n)+l*(o-2*i+n),x=r+2*h*(a-r)+l*(s-2*a+r),b=u*t+h*n,M=u*e+h*r,w=u*i+h*o,C=u*a+h*s,A=90-180*Math.atan2(y-d,m-x)/Math.PI;return{x:g,y:v,m:{x:y,y:m},n:{x:d,y:x},start:{x:b,y:M},end:{x:w,y:C},alpha:A}},_=function(t,e,n){var r=O(t),i=O(e);if(!P(r,i))return n?0:[];for(var a=x.apply(0,t),o=x.apply(0,e),s=~~(a/8),h=~~(o/8),u=[],c=[],p={},l=n?0:[],f=0;f<s+1;f++){var g=S.apply(0,t.concat(f/s));u.push({x:g.x,y:g.y,t:f/s})}for(f=0;f<h+1;f++){g=S.apply(0,e.concat(f/h));c.push({x:g.x,y:g.y,t:f/h})}for(f=0;f<s;f++)for(var v=0;v<h;v++){var y=u[f],m=u[f+1],d=c[v],b=c[v+1],w=Math.abs(m.x-y.x)<.001?"y":"x",C=Math.abs(b.x-d.x)<.001?"y":"x",A=M(y.x,y.y,m.x,m.y,d.x,d.y,b.x,b.y);if(A){if(p[A.x.toFixed(4)]===A.y.toFixed(4))continue;p[A.x.toFixed(4)]=A.y.toFixed(4);var _=y.t+Math.abs((A[w]-y[w])/(m[w]-y[w]))*(m.t-y.t),j=d.t+Math.abs((A[C]-d[C])/(b[C]-d[C]))*(b.t-d.t);_>=0&&_<=1&&j>=0&&j<=1&&(n?l+=1:l.push({x:A.x,y:A.y,t1:_,t2:j}))}}return l},j=function(t,e,n){var r,i,a,o,s,h,u,c,p,l;t=v(t),e=v(e);for(var f=n?0:[],g=0,y=t.length;g<y;g++){var m=t[g];if("M"===m[0])r=s=m[1],i=h=m[2];else{"C"===m[0]?(p=[r,i].concat(m.slice(1)),r=p[6],i=p[7]):(p=[r,i,r,i,s,h,s,h],r=s,i=h);for(var d=0,x=e.length;d<x;d++){var b=e[d];if("M"===b[0])a=u=b[1],o=c=b[2];else{"C"===b[0]?(l=[a,o].concat(b.slice(1)),a=l[6],o=l[7]):(l=[a,o,a,o,u,c,u,c],a=u,o=c);var M=_(p,l,n);if(n)f+=M;else{for(var w=0,C=M.length;w<C;w++)M[w].segment1=g,M[w].segment2=d,M[w].bez1=p,M[w].bez2=l;f=f.concat(M)}}}}}return f},k=function(t,e){return j(t,e)};function B(t,e){var n=[],r=[];function i(t,e){if(1===t.length)n.push(t[0]),r.push(t[0]);else{for(var a=[],o=0;o<t.length-1;o++)0===o&&n.push(t[0]),o===t.length-2&&r.push(t[o+1]),a[o]=[(1-e)*t[o][0]+e*t[o+1][0],(1-e)*t[o][1]+e*t[o+1][1]];i(a,e)}}return t.length&&i(t,e),{left:n,right:r.reverse()}}function E(t,e,n){var r=[[t[1],t[2]]];n=n||2;var i=[];"A"===e[0]?(r.push(e[6]),r.push(e[7])):"C"===e[0]?(r.push([e[1],e[2]]),r.push([e[3],e[4]]),r.push([e[5],e[6]])):"S"===e[0]||"Q"===e[0]?(r.push([e[1],e[2]]),r.push([e[3],e[4]])):r.push([e[1],e[2]]);for(var a=r,o=1/n,s=0;s<n-1;s++){var h=o/(1-o*s),u=B(a,h);i.push(u.left),a=u.right}i.push(a);var c=i.map((function(t){var e=[];return 4===t.length&&(e.push("C"),e=e.concat(t[2])),t.length>=3&&(3===t.length&&e.push("Q"),e=e.concat(t[1])),2===t.length&&e.push("L"),e=e.concat(t[t.length-1]),e}));return c}var I=function(t,e,n){if(1===n)return[[].concat(t)];var r=[];if("L"===e[0]||"C"===e[0]||"Q"===e[0])r=r.concat(E(t,e,n));else{var i=[].concat(t);"M"===i[0]&&(i[0]="L");for(var a=0;a<=n-1;a++)r.push(i)}return r},T=function(t,e){if(1===t.length)return t;var n=t.length-1,r=e.length-1,i=n/r,a=[];if(1===t.length&&"M"===t[0][0]){for(var o=0;o<r-n;o++)t.push(t[0]);return t}for(o=0;o<r;o++){var s=Math.floor(i*o);a[s]=(a[s]||0)+1}var h=a.reduce((function(e,r,i){return i===n?e.concat(t[n]):e.concat(I(t[i],t[i+1],r))}),[]);return h.unshift(t[0]),"Z"!==e[r]&&"z"!==e[r]||h.push("Z"),h},X=function(t,e){if(t.length!==e.length)return!1;var n=!0;return Object(i["each"])(t,(function(t,r){if(t!==e[r])return n=!1,!1})),n};function Y(t,e,n){var r=null,i=n;return e<i&&(i=e,r="add"),t<i&&(i=t,r="del"),{type:r,min:i}}var D=function(t,e){var n,r,i=t.length,a=e.length,o=0;if(0===i||0===a)return null;for(var s=[],h=0;h<=i;h++)s[h]=[],s[h][0]={min:h};for(var u=0;u<=a;u++)s[0][u]={min:u};for(h=1;h<=i;h++){n=t[h-1];for(u=1;u<=a;u++){r=e[u-1],o=X(n,r)?0:1;var c=s[h-1][u].min+1,p=s[h][u-1].min+1,l=s[h-1][u-1].min+o;s[h][u]=Y(c,p,l)}}return s},F=function(t,e){var n=D(t,e),r=t.length,i=e.length,a=[],o=1,s=1;if(n[r][i].min!==r){for(var h=1;h<=r;h++){var u=n[h][h].min;s=h;for(var c=o;c<=i;c++)n[h][c].min<u&&(u=n[h][c].min,s=c);o=s,n[h][o].type&&a.push({index:h-1,type:n[h][o].type})}for(h=a.length-1;h>=0;h--)o=a[h].index,"add"===a[h].type?t.splice(o,0,[].concat(t[o])):t.splice(o,1)}r=t.length;var p=i-r;if(r<i)for(h=0;h<p;h++)"z"===t[r-1][0]||"Z"===t[r-1][0]?t.splice(r-2,0,t[r-2]):t.push(t[r-1]),r+=1;return t};function L(t,e,n){for(var r,i=[].concat(t),a=1/(n+1),o=N(e)[0],s=1;s<=n;s++)a*=s,r=Math.floor(t.length*a),0===r?i.unshift([o[0]*a+t[r][0]*(1-a),o[1]*a+t[r][1]*(1-a)]):i.splice(r,0,[o[0]*a+t[r][0]*(1-a),o[1]*a+t[r][1]*(1-a)]);return i}function N(t){var e=[];switch(t[0]){case"M":e.push([t[1],t[2]]);break;case"L":e.push([t[1],t[2]]);break;case"A":e.push([t[6],t[7]]);break;case"Q":e.push([t[3],t[4]]),e.push([t[1],t[2]]);break;case"T":e.push([t[1],t[2]]);break;case"C":e.push([t[5],t[6]]),e.push([t[1],t[2]]),e.push([t[3],t[4]]);break;case"S":e.push([t[3],t[4]]),e.push([t[1],t[2]]);break;case"H":e.push([t[1],t[1]]);break;case"V":e.push([t[1],t[1]]);break;default:}return e}var q=function(t,e){if(t.length<=1)return t;for(var n,r=0;r<e.length;r++)if(t[r][0]!==e[r][0])switch(n=N(t[r]),e[r][0]){case"M":t[r]=["M"].concat(n[0]);break;case"L":t[r]=["L"].concat(n[0]);break;case"A":t[r]=[].concat(e[r]),t[r][6]=n[0][0],t[r][7]=n[0][1];break;case"Q":if(n.length<2){if(!(r>0)){t[r]=e[r];break}n=L(n,t[r-1],1)}t[r]=["Q"].concat(n.reduce((function(t,e){return t.concat(e)}),[]));break;case"T":t[r]=["T"].concat(n[0]);break;case"C":if(n.length<3){if(!(r>0)){t[r]=e[r];break}n=L(n,t[r-1],2)}t[r]=["C"].concat(n.reduce((function(t,e){return t.concat(e)}),[]));break;case"S":if(n.length<2){if(!(r>0)){t[r]=e[r];break}n=L(n,t[r-1],1)}t[r]=["S"].concat(n.reduce((function(t,e){return t.concat(e)}),[]));break;default:t[r]=e[r]}return t},R=function(){function t(t,e){this.bubbles=!0,this.target=null,this.currentTarget=null,this.delegateTarget=null,this.delegateObject=null,this.defaultPrevented=!1,this.propagationStopped=!1,this.shape=null,this.fromShape=null,this.toShape=null,this.propagationPath=[],this.type=t,this.name=t,this.originalEvent=e,this.timeStamp=e.timeStamp}return t.prototype.preventDefault=function(){this.defaultPrevented=!0,this.originalEvent.preventDefault&&this.originalEvent.preventDefault()},t.prototype.stopPropagation=function(){this.propagationStopped=!0},t.prototype.toString=function(){var t=this.type;return"[Event (type="+t+")]"},t.prototype.save=function(){},t.prototype.restore=function(){},t}(),z=R,G=function(t,e){return G=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},G(t,e)};function H(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}G(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}Object.create;Object.create;var V=n("7fa2");function W(t,e){var n=t.indexOf(e);-1!==n&&t.splice(n,1)}var Z="undefined"!==typeof window&&"undefined"!==typeof window.document;function Q(t,e){if(t.isCanvas())return!0;var n=e.getParent(),r=!1;while(n){if(n===t){r=!0;break}n=n.getParent()}return r}function U(t){return t.cfg.visible&&t.cfg.capture}var J=function(t){function e(e){var n=t.call(this)||this;n.destroyed=!1;var r=n.getDefaultCfg();return n.cfg=Object(i["mix"])(r,e),n}return H(e,t),e.prototype.getDefaultCfg=function(){return{}},e.prototype.get=function(t){return this.cfg[t]},e.prototype.set=function(t,e){this.cfg[t]=e},e.prototype.destroy=function(){this.cfg={destroyed:!0},this.off(),this.destroyed=!0},e}(V["a"]),$=J,K=n("7b46"),tt=n("e897");function et(t,e){var n=[],r=t[0],i=t[1],a=t[2],o=t[3],s=t[4],h=t[5],u=t[6],c=t[7],p=t[8],l=e[0],f=e[1],g=e[2],v=e[3],y=e[4],m=e[5],d=e[6],x=e[7],b=e[8];return n[0]=l*r+f*o+g*u,n[1]=l*i+f*s+g*c,n[2]=l*a+f*h+g*p,n[3]=v*r+y*o+m*u,n[4]=v*i+y*s+m*c,n[5]=v*a+y*h+m*p,n[6]=d*r+x*o+b*u,n[7]=d*i+x*s+b*c,n[8]=d*a+x*h+b*p,n}function nt(t,e){var n=[],r=e[0],i=e[1];return n[0]=t[0]*r+t[3]*i+t[6],n[1]=t[1]*r+t[4]*i+t[7],n}function rt(t){var e=[],n=t[0],r=t[1],i=t[2],a=t[3],o=t[4],s=t[5],h=t[6],u=t[7],c=t[8],p=c*o-s*u,l=-c*a+s*h,f=u*a-o*h,g=n*p+r*l+i*f;return g?(g=1/g,e[0]=p*g,e[1]=(-c*r+i*u)*g,e[2]=(s*r-i*o)*g,e[3]=l*g,e[4]=(c*n-i*h)*g,e[5]=(-s*n+i*a)*g,e[6]=f*g,e[7]=(-u*n+r*h)*g,e[8]=(o*n-r*a)*g,e):null}var it=tt["a"].transform,at="matrix",ot=["zIndex","capture","visible","type"],st=["repeat"],ht=":",ut="*";function ct(t){for(var e=[],n=0;n<t.length;n++)Object(i["isArray"])(t[n])?e.push([].concat(t[n])):e.push(t[n]);return e}function pt(t,e){var n={},r=e.attrs;for(var i in t)n[i]=r[i];return n}function lt(t,e){var n={},r=e.attr();return Object(i["each"])(t,(function(t,e){-1!==st.indexOf(e)||Object(i["isEqual"])(r[e],t)||(n[e]=t)})),n}function ft(t,e){if(e.onFrame)return t;var n=e.startTime,r=e.delay,a=e.duration,o=Object.prototype.hasOwnProperty;return Object(i["each"])(t,(function(t){n+r<t.startTime+t.delay+t.duration&&a>t.delay&&Object(i["each"])(e.toAttrs,(function(e,n){o.call(t.toAttrs,n)&&(delete t.toAttrs[n],delete t.fromAttrs[n])}))})),t}var gt=function(t){function e(e){var n=t.call(this,e)||this;n.attrs={};var r=n.getDefaultAttrs();return Object(i["mix"])(r,e.attrs),n.attrs=r,n.initAttrs(r),n.initAnimate(),n}return H(e,t),e.prototype.getDefaultCfg=function(){return{visible:!0,capture:!0,zIndex:0}},e.prototype.getDefaultAttrs=function(){return{matrix:this.getDefaultMatrix(),opacity:1}},e.prototype.onCanvasChange=function(t){},e.prototype.initAttrs=function(t){},e.prototype.initAnimate=function(){this.set("animable",!0),this.set("animating",!1)},e.prototype.isGroup=function(){return!1},e.prototype.getParent=function(){return this.get("parent")},e.prototype.getCanvas=function(){return this.get("canvas")},e.prototype.attr=function(){for(var t,e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];var r=e[0],a=e[1];if(!r)return this.attrs;if(Object(i["isObject"])(r)){for(var o in r)this.setAttr(o,r[o]);return this.afterAttrsChange(r),this}return 2===e.length?(this.setAttr(r,a),this.afterAttrsChange((t={},t[r]=a,t)),this):this.attrs[r]},e.prototype.isClipped=function(t,e){var n=this.getClip();return n&&!n.isHit(t,e)},e.prototype.setAttr=function(t,e){var n=this.attrs[t];n!==e&&(this.attrs[t]=e,this.onAttrChange(t,e,n))},e.prototype.onAttrChange=function(t,e,n){"matrix"===t&&this.set("totalMatrix",null)},e.prototype.afterAttrsChange=function(t){if(this.cfg.isClipShape){var e=this.cfg.applyTo;e&&e.onCanvasChange("clip")}else this.onCanvasChange("attr")},e.prototype.show=function(){return this.set("visible",!0),this.onCanvasChange("show"),this},e.prototype.hide=function(){return this.set("visible",!1),this.onCanvasChange("hide"),this},e.prototype.setZIndex=function(t){this.set("zIndex",t);var e=this.getParent();return e&&e.sort(),this},e.prototype.toFront=function(){var t=this.getParent();if(t){var e=t.getChildren(),n=(this.get("el"),e.indexOf(this));e.splice(n,1),e.push(this),this.onCanvasChange("zIndex")}},e.prototype.toBack=function(){var t=this.getParent();if(t){var e=t.getChildren(),n=(this.get("el"),e.indexOf(this));e.splice(n,1),e.unshift(this),this.onCanvasChange("zIndex")}},e.prototype.remove=function(t){void 0===t&&(t=!0);var e=this.getParent();e?(W(e.getChildren(),this),e.get("clearing")||this.onCanvasChange("remove")):this.onCanvasChange("remove"),t&&this.destroy()},e.prototype.resetMatrix=function(){this.attr(at,this.getDefaultMatrix()),this.onCanvasChange("matrix")},e.prototype.getMatrix=function(){return this.attr(at)},e.prototype.setMatrix=function(t){this.attr(at,t),this.onCanvasChange("matrix")},e.prototype.getTotalMatrix=function(){var t=this.cfg.totalMatrix;if(!t){var e=this.attr("matrix"),n=this.cfg.parentMatrix;t=n&&e?et(n,e):e||n,this.set("totalMatrix",t)}return t},e.prototype.applyMatrix=function(t){var e=this.attr("matrix"),n=null;n=t&&e?et(t,e):e||t,this.set("totalMatrix",n),this.set("parentMatrix",t)},e.prototype.getDefaultMatrix=function(){return null},e.prototype.applyToMatrix=function(t){var e=this.attr("matrix");return e?nt(e,t):t},e.prototype.invertFromMatrix=function(t){var e=this.attr("matrix");if(e){var n=rt(e);if(n)return nt(n,t)}return t},e.prototype.setClip=function(t){var e=this.getCanvas(),n=null;if(t){var r=this.getShapeBase(),a=Object(i["upperFirst"])(t.type),o=r[a];o&&(n=new o({type:t.type,isClipShape:!0,applyTo:this,attrs:t.attrs,canvas:e}))}return this.set("clipShape",n),this.onCanvasChange("clip"),n},e.prototype.getClip=function(){var t=this.cfg.clipShape;return t||null},e.prototype.clone=function(){var t=this,e=this.attrs,n={};Object(i["each"])(e,(function(t,r){Object(i["isArray"])(e[r])?n[r]=ct(e[r]):n[r]=e[r]}));var r=this.constructor,a=new r({attrs:n});return Object(i["each"])(ot,(function(e){a.set(e,t.get(e))})),a},e.prototype.destroy=function(){var e=this.destroyed;e||(this.attrs={},t.prototype.destroy.call(this))},e.prototype.isAnimatePaused=function(){return this.get("_pause").isPaused},e.prototype.animate=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];if(this.get("timeline")||this.get("canvas")){this.set("animating",!0);var n=this.get("timeline");n||(n=this.get("canvas").get("timeline"),this.set("timeline",n));var r=this.get("animations")||[];n.timer||n.initTimer();var a,o,s,h,u,c=t[0],p=t[1],l=t[2],f=void 0===l?"easeLinear":l,g=t[3],v=void 0===g?i["noop"]:g,y=t[4],m=void 0===y?0:y;Object(i["isFunction"])(c)?(a=c,c={}):Object(i["isObject"])(c)&&c.onFrame&&(a=c.onFrame,o=c.repeat),Object(i["isObject"])(p)?(u=p,p=u.duration,f=u.easing||"easeLinear",m=u.delay||0,o=u.repeat||o||!1,v=u.callback||i["noop"],s=u.pauseCallback||i["noop"],h=u.resumeCallback||i["noop"]):(Object(i["isNumber"])(v)&&(m=v,v=null),Object(i["isFunction"])(f)?(v=f,f="easeLinear"):f=f||"easeLinear");var d=lt(c,this),x={fromAttrs:pt(d,this),toAttrs:d,duration:p,easing:f,repeat:o,callback:v,pauseCallback:s,resumeCallback:h,delay:m,startTime:n.getTime(),id:Object(i["uniqueId"])(),onFrame:a,pathFormatted:!1};r.length>0?r=ft(r,x):n.addAnimator(this),r.push(x),this.set("animations",r),this.set("_pause",{isPaused:!1})}},e.prototype.stopAnimate=function(t){var e=this;void 0===t&&(t=!0);var n=this.get("animations");Object(i["each"])(n,(function(n){t&&(n.onFrame?e.attr(n.onFrame(1)):e.attr(n.toAttrs)),n.callback&&n.callback()})),this.set("animating",!1),this.set("animations",[])},e.prototype.pauseAnimate=function(){var t=this.get("timeline"),e=this.get("animations"),n=t.getTime();return Object(i["each"])(e,(function(t){t._paused=!0,t._pauseTime=n,t.pauseCallback&&t.pauseCallback()})),this.set("_pause",{isPaused:!0,pauseTime:n}),this},e.prototype.resumeAnimate=function(){var t=this.get("timeline"),e=t.getTime(),n=this.get("animations"),r=this.get("_pause").pauseTime;return Object(i["each"])(n,(function(t){t.startTime=t.startTime+(e-r),t._paused=!1,t._pauseTime=null,t.resumeCallback&&t.resumeCallback()})),this.set("_pause",{isPaused:!1}),this.set("animations",n),this},e.prototype.emitDelegation=function(t,e){var n,r=this,a=e.propagationPath;this.getEvents();"mouseenter"===t?n=e.fromShape:"mouseleave"===t&&(n=e.toShape);for(var o=function(t){var o=a[t],h=o.get("name");if(h){if((o.isGroup()||o.isCanvas&&o.isCanvas())&&n&&Q(o,n))return"break";Object(i["isArray"])(h)?Object(i["each"])(h,(function(t){r.emitDelegateEvent(o,t,e)})):s.emitDelegateEvent(o,h,e)}},s=this,h=0;h<a.length;h++){var u=o(h);if("break"===u)break}},e.prototype.emitDelegateEvent=function(t,e,n){var r=this.getEvents(),i=e+ht+n.type;(r[i]||r[ut])&&(n.name=i,n.currentTarget=t,n.delegateTarget=this,n.delegateObject=t.get("delegateObject"),this.emit(i,n))},e.prototype.translate=function(t,e){void 0===t&&(t=0),void 0===e&&(e=0);var n=this.getMatrix(),r=it(n,[["t",t,e]]);return this.setMatrix(r),this},e.prototype.move=function(t,e){var n=this.attr("x")||0,r=this.attr("y")||0;return this.translate(t-n,e-r),this},e.prototype.moveTo=function(t,e){return this.move(t,e)},e.prototype.scale=function(t,e){var n=this.getMatrix(),r=it(n,[["s",t,e||t]]);return this.setMatrix(r),this},e.prototype.rotate=function(t){var e=this.getMatrix(),n=it(e,[["r",t]]);return this.setMatrix(n),this},e.prototype.rotateAtStart=function(t){var e=this.attr(),n=e.x,r=e.y,i=this.getMatrix(),a=it(i,[["t",-n,-r],["r",t],["t",n,r]]);return this.setMatrix(a),this},e.prototype.rotateAtPoint=function(t,e,n){var r=this.getMatrix(),i=it(r,[["t",-t,-e],["r",n],["t",t,e]]);return this.setMatrix(i),this},e}($),vt=gt,yt={},mt="_INDEX";function dt(t,e){if(t.set("canvas",e),t.isGroup()){var n=t.get("children");n.length&&n.forEach((function(t){dt(t,e)}))}}function xt(t,e){if(t.set("timeline",e),t.isGroup()){var n=t.get("children");n.length&&n.forEach((function(t){xt(t,e)}))}}function bt(t,e,n){void 0===n&&(n=!0),n?e.destroy():(e.set("parent",null),e.set("canvas",null)),W(t.getChildren(),e)}function Mt(t){return function(e,n){var r=t(e,n);return 0===r?e[mt]-n[mt]:r}}var wt=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return H(e,t),e.prototype.isCanvas=function(){return!1},e.prototype.getBBox=function(){var t=1/0,e=-1/0,n=1/0,r=-1/0,a=[],o=[],s=this.getChildren().filter((function(t){return t.get("visible")&&(!t.isGroup()||t.isGroup()&&t.getChildren().length>0)}));s.length>0?(Object(i["each"])(s,(function(t){var e=t.getBBox();a.push(e.minX,e.maxX),o.push(e.minY,e.maxY)})),t=Object(i["min"])(a),e=Object(i["max"])(a),n=Object(i["min"])(o),r=Object(i["max"])(o)):(t=0,e=0,n=0,r=0);var h={x:t,y:n,minX:t,minY:n,maxX:e,maxY:r,width:e-t,height:r-n};return h},e.prototype.getCanvasBBox=function(){var t=1/0,e=-1/0,n=1/0,r=-1/0,a=[],o=[],s=this.getChildren().filter((function(t){return t.get("visible")&&(!t.isGroup()||t.isGroup()&&t.getChildren().length>0)}));s.length>0?(Object(i["each"])(s,(function(t){var e=t.getCanvasBBox();a.push(e.minX,e.maxX),o.push(e.minY,e.maxY)})),t=Object(i["min"])(a),e=Object(i["max"])(a),n=Object(i["min"])(o),r=Object(i["max"])(o)):(t=0,e=0,n=0,r=0);var h={x:t,y:n,minX:t,minY:n,maxX:e,maxY:r,width:e-t,height:r-n};return h},e.prototype.getDefaultCfg=function(){var e=t.prototype.getDefaultCfg.call(this);return e["children"]=[],e},e.prototype.onAttrChange=function(e,n,r){if(t.prototype.onAttrChange.call(this,e,n,r),"matrix"===e){var i=this.getTotalMatrix();this._applyChildrenMarix(i)}},e.prototype.applyMatrix=function(e){var n=this.getTotalMatrix();t.prototype.applyMatrix.call(this,e);var r=this.getTotalMatrix();r!==n&&this._applyChildrenMarix(r)},e.prototype._applyChildrenMarix=function(t){var e=this.getChildren();Object(i["each"])(e,(function(e){e.applyMatrix(t)}))},e.prototype.addShape=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var n=t[0],r=t[1];Object(i["isObject"])(n)?r=n:r["type"]=n;var a=yt[r.type];a||(a=Object(i["upperFirst"])(r.type),yt[r.type]=a);var o=this.getShapeBase(),s=new o[a](r);return this.add(s),s},e.prototype.addGroup=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var n,r=t[0],a=t[1];if(Object(i["isFunction"])(r))n=new r(a||{parent:this});else{var o=r||{},s=this.getGroupBase();n=new s(o)}return this.add(n),n},e.prototype.getCanvas=function(){var t;return t=this.isCanvas()?this:this.get("canvas"),t},e.prototype.getShape=function(t,e,n){if(!U(this))return null;var r,i=this.getChildren();if(this.isCanvas())r=this._findShape(i,t,e,n);else{var a=[t,e,1];a=this.invertFromMatrix(a),this.isClipped(a[0],a[1])||(r=this._findShape(i,a[0],a[1],n))}return r},e.prototype._findShape=function(t,e,n,r){for(var i=null,a=t.length-1;a>=0;a--){var o=t[a];if(U(o)&&(o.isGroup()?i=o.getShape(e,n,r):o.isHit(e,n)&&(i=o)),i)break}return i},e.prototype.add=function(t){var e=this.getCanvas(),n=this.getChildren(),r=this.get("timeline"),i=t.getParent();i&&bt(i,t,!1),t.set("parent",this),e&&dt(t,e),r&&xt(t,r),n.push(t),t.onCanvasChange("add"),this._applyElementMatrix(t)},e.prototype._applyElementMatrix=function(t){var e=this.getTotalMatrix();e&&t.applyMatrix(e)},e.prototype.getChildren=function(){return this.get("children")},e.prototype.sort=function(){var t=this.getChildren();Object(i["each"])(t,(function(t,e){return t[mt]=e,t})),t.sort(Mt((function(t,e){return t.get("zIndex")-e.get("zIndex")}))),this.onCanvasChange("sort")},e.prototype.clear=function(){if(this.set("clearing",!0),!this.destroyed){for(var t=this.getChildren(),e=t.length-1;e>=0;e--)t[e].destroy();this.set("children",[]),this.onCanvasChange("clear"),this.set("clearing",!1)}},e.prototype.destroy=function(){this.get("destroyed")||(this.clear(),t.prototype.destroy.call(this))},e.prototype.getFirst=function(){return this.getChildByIndex(0)},e.prototype.getLast=function(){var t=this.getChildren();return this.getChildByIndex(t.length-1)},e.prototype.getChildByIndex=function(t){var e=this.getChildren();return e[t]},e.prototype.getCount=function(){var t=this.getChildren();return t.length},e.prototype.contain=function(t){var e=this.getChildren();return e.indexOf(t)>-1},e.prototype.removeChild=function(t,e){void 0===e&&(e=!0),this.contain(t)&&t.remove(e)},e.prototype.findAll=function(t){var e=[],n=this.getChildren();return Object(i["each"])(n,(function(n){t(n)&&e.push(n),n.isGroup()&&(e=e.concat(n.findAll(t)))})),e},e.prototype.find=function(t){var e=null,n=this.getChildren();return Object(i["each"])(n,(function(n){if(t(n)?e=n:n.isGroup()&&(e=n.find(t)),e)return!1})),e},e.prototype.findById=function(t){return this.find((function(e){return e.get("id")===t}))},e.prototype.findByClassName=function(t){return this.find((function(e){return e.get("className")===t}))},e.prototype.findAllByName=function(t){return this.findAll((function(e){return e.get("name")===t}))},e}(vt),Ct=wt,At=n("74f4"),Pt=n("ea1d"),Ot=n("6730"),St=n("fb77"),_t={};function jt(t){return _t[t.toLowerCase()]||St[t]}var kt=function(t){return["fill","stroke","fillStyle","strokeStyle"].includes(t)},Bt=function(t){return/^[r,R,L,l]{1}[\s]*\(/.test(t)},Et=[1,0,0,0,1,0,0,0,1];function It(t,e,n){var r={},a=e.fromAttrs,o=e.toAttrs;if(!t.destroyed){var s;for(var u in o)if(!Object(i["isEqual"])(a[u],o[u]))if("path"===u){var c=o[u],p=a[u];c.length>p.length?(c=h(o[u]),p=h(a[u]),p=F(p,c),p=q(p,c),e.fromAttrs.path=p,e.toAttrs.path=c):e.pathFormatted||(c=h(o[u]),p=h(a[u]),p=q(p,c),e.fromAttrs.path=p,e.toAttrs.path=c,e.pathFormatted=!0),r[u]=[];for(var l=0;l<c.length;l++){for(var f=c[l],g=p[l],v=[],y=0;y<f.length;y++)Object(i["isNumber"])(f[y])&&g&&Object(i["isNumber"])(g[y])?(s=Object(Pt["a"])(g[y],f[y]),v.push(s(n))):v.push(f[y]);r[u].push(v)}}else if("matrix"===u){var m=Object(Ot["a"])(a[u]||Et,o[u]||Et),d=m(n);r[u]=d}else kt(u)&&Bt(o[u])?r[u]=o[u]:Object(i["isFunction"])(o[u])||(s=Object(Pt["a"])(a[u],o[u]),r[u]=s(n));t.attr(r)}}function Tt(t,e,n){var r,i=e.startTime,a=e.delay;if(n<i+a||e._paused)return!1;var o=e.duration,s=e.easing,h=jt(s);if(n=n-i-e.delay,e.repeat)r=n%o/o,r=h(r);else{if(r=n/o,!(r<1))return e.onFrame?t.attr(e.onFrame(1)):t.attr(e.toAttrs),!0;r=h(r)}if(e.onFrame){var u=e.onFrame(r);t.attr(u)}else It(t,e,r);return!1}var Xt=function(){function t(t){this.animators=[],this.current=0,this.timer=null,this.canvas=t}return t.prototype.initTimer=function(){var t,e,n,r=this,i=!1;this.timer=At["a"]((function(a){if(r.current=a,r.animators.length>0){for(var o=r.animators.length-1;o>=0;o--)if(t=r.animators[o],t.destroyed)r.removeAnimator(o);else{if(!t.isAnimatePaused()){e=t.get("animations");for(var s=e.length-1;s>=0;s--)n=e[s],i=Tt(t,n,a),i&&(e.splice(s,1),i=!1,n.callback&&n.callback())}0===e.length&&r.removeAnimator(o)}var h=r.canvas.get("autoDraw");h||r.canvas.draw()}}))},t.prototype.addAnimator=function(t){this.animators.push(t)},t.prototype.removeAnimator=function(t){this.animators.splice(t,1)},t.prototype.isAnimating=function(){return!!this.animators.length},t.prototype.stop=function(){this.timer&&this.timer.stop()},t.prototype.stopAllAnimations=function(t){void 0===t&&(t=!0),this.animators.forEach((function(e){e.stopAnimate(t)})),this.animators=[],this.canvas.draw()},t.prototype.getTime=function(){return this.current},t}(),Yt=Xt,Dt=40,Ft=0,Lt=["mousedown","mouseup","dblclick","mouseout","mouseover","mousemove","mouseleave","mouseenter","touchstart","touchmove","touchend","dragenter","dragover","dragleave","drop","contextmenu","mousewheel"];function Nt(t,e,n){n.name=e,n.target=t,n.currentTarget=t,n.delegateTarget=t,t.emit(e,n)}function qt(t,e,n){if(n.bubbles){var r=void 0,i=!1;if("mouseenter"===e?(r=n.fromShape,i=!0):"mouseleave"===e&&(i=!0,r=n.toShape),t.isCanvas()&&i)return;if(r&&Q(t,r))return void(n.bubbles=!1);n.name=e,n.currentTarget=t,n.delegateTarget=t,t.emit(e,n)}}var Rt=function(){function t(t){var e=this;this.draggingShape=null,this.dragging=!1,this.currentShape=null,this.mousedownShape=null,this.mousedownPoint=null,this._eventCallback=function(t){var n=t.type;e._triggerEvent(n,t)},this._onDocumentMove=function(t){var n=e.canvas,r=n.get("el");if(r!==t.target&&(e.dragging||e.currentShape)){var i=e._getPointInfo(t);e.dragging&&e._emitEvent("drag",t,i,e.draggingShape)}},this._onDocumentMouseUp=function(t){var n=e.canvas,r=n.get("el");if(r!==t.target&&e.dragging){var i=e._getPointInfo(t);e.draggingShape&&e._emitEvent("drop",t,i,null),e._emitEvent("dragend",t,i,e.draggingShape),e._afterDrag(e.draggingShape,i,t)}},this.canvas=t.canvas}return t.prototype.init=function(){this._bindEvents()},t.prototype._bindEvents=function(){var t=this,e=this.canvas.get("el");Object(i["each"])(Lt,(function(n){e.addEventListener(n,t._eventCallback)})),document&&(document.addEventListener("mousemove",this._onDocumentMove),document.addEventListener("mouseup",this._onDocumentMouseUp))},t.prototype._clearEvents=function(){var t=this,e=this.canvas.get("el");Object(i["each"])(Lt,(function(n){e.removeEventListener(n,t._eventCallback)})),document&&(document.removeEventListener("mousemove",this._onDocumentMove),document.removeEventListener("mouseup",this._onDocumentMouseUp))},t.prototype._getEventObj=function(t,e,n,r,i,a){var o=new z(t,e);return o.fromShape=i,o.toShape=a,o.x=n.x,o.y=n.y,o.clientX=n.clientX,o.clientY=n.clientY,o.propagationPath.push(r),o},t.prototype._getShape=function(t,e){return this.canvas.getShape(t.x,t.y,e)},t.prototype._getPointInfo=function(t){var e=this.canvas,n=e.getClientByEvent(t),r=e.getPointByEvent(t);return{x:r.x,y:r.y,clientX:n.x,clientY:n.y}},t.prototype._triggerEvent=function(t,e){var n=this._getPointInfo(e),r=this._getShape(n,e),i=this["_on"+t],a=!1;if(i)i.call(this,n,r,e);else{var o=this.currentShape;"mouseenter"===t||"dragenter"===t||"mouseover"===t?(this._emitEvent(t,e,n,null,null,r),r&&this._emitEvent(t,e,n,r,null,r),"mouseenter"===t&&this.draggingShape&&this._emitEvent("dragenter",e,n,null)):"mouseleave"===t||"dragleave"===t||"mouseout"===t?(a=!0,o&&this._emitEvent(t,e,n,o,o,null),this._emitEvent(t,e,n,null,o,null),"mouseleave"===t&&this.draggingShape&&this._emitEvent("dragleave",e,n,null)):this._emitEvent(t,e,n,r,null,null)}if(a||(this.currentShape=r),r&&!r.get("destroyed")){var s=this.canvas,h=s.get("el");h.style.cursor=r.attr("cursor")||s.get("cursor")}},t.prototype._onmousedown=function(t,e,n){n.button===Ft&&(this.mousedownShape=e,this.mousedownPoint=t,this.mousedownTimeStamp=n.timeStamp),this._emitEvent("mousedown",n,t,e,null,null)},t.prototype._emitMouseoverEvents=function(t,e,n,r){var i=this.canvas.get("el");n!==r&&(n&&(this._emitEvent("mouseout",t,e,n,n,r),this._emitEvent("mouseleave",t,e,n,n,r),r&&!r.get("destroyed")||(i.style.cursor=this.canvas.get("cursor"))),r&&(this._emitEvent("mouseover",t,e,r,n,r),this._emitEvent("mouseenter",t,e,r,n,r)))},t.prototype._emitDragoverEvents=function(t,e,n,r,i){r?(r!==n&&(n&&this._emitEvent("dragleave",t,e,n,n,r),this._emitEvent("dragenter",t,e,r,n,r)),i||this._emitEvent("dragover",t,e,r)):n&&this._emitEvent("dragleave",t,e,n,n,r),i&&this._emitEvent("dragover",t,e,r)},t.prototype._afterDrag=function(t,e,n){t&&(t.set("capture",!0),this.draggingShape=null),this.dragging=!1;var r=this._getShape(e,n);r!==t&&this._emitMouseoverEvents(n,e,t,r),this.currentShape=r},t.prototype._onmouseup=function(t,e,n){if(n.button===Ft){var r=this.draggingShape;this.dragging?(r&&this._emitEvent("drop",n,t,e),this._emitEvent("dragend",n,t,r),this._afterDrag(r,t,n)):(this._emitEvent("mouseup",n,t,e),e===this.mousedownShape&&this._emitEvent("click",n,t,e),this.mousedownShape=null,this.mousedownPoint=null)}},t.prototype._ondragover=function(t,e,n){n.preventDefault();var r=this.currentShape;this._emitDragoverEvents(n,t,r,e,!0)},t.prototype._onmousemove=function(t,e,n){var r=this.canvas,i=this.currentShape,a=this.draggingShape;if(this.dragging)a&&this._emitDragoverEvents(n,t,i,e,!1),this._emitEvent("drag",n,t,a);else{var o=this.mousedownPoint;if(o){var s=this.mousedownShape,h=n.timeStamp,u=h-this.mousedownTimeStamp,c=o.clientX-t.clientX,p=o.clientY-t.clientY,l=c*c+p*p;u>120||l>Dt?s&&s.get("draggable")?(a=this.mousedownShape,a.set("capture",!1),this.draggingShape=a,this.dragging=!0,this._emitEvent("dragstart",n,t,a),this.mousedownShape=null,this.mousedownPoint=null):!s&&r.get("draggable")?(this.dragging=!0,this._emitEvent("dragstart",n,t,null),this.mousedownShape=null,this.mousedownPoint=null):(this._emitMouseoverEvents(n,t,i,e),this._emitEvent("mousemove",n,t,e)):(this._emitMouseoverEvents(n,t,i,e),this._emitEvent("mousemove",n,t,e))}else this._emitMouseoverEvents(n,t,i,e),this._emitEvent("mousemove",n,t,e)}},t.prototype._emitEvent=function(t,e,n,r,i,a){var o=this._getEventObj(t,e,n,r,i,a);if(r){o.shape=r,Nt(r,t,o);var s=r.getParent();while(s)s.emitDelegation(t,o),o.propagationStopped||qt(s,t,o),o.propagationPath.push(s),s=s.getParent()}else{var h=this.canvas;Nt(h,t,o)}},t.prototype.destroy=function(){this._clearEvents(),this.canvas=null,this.currentShape=null,this.draggingShape=null,this.mousedownPoint=null,this.mousedownShape=null,this.mousedownTimeStamp=null},t}(),zt=Rt,Gt="px",Ht=Object(K["a"])(),Vt=Ht&&"firefox"===Ht.name,Wt=function(t){function e(e){var n=t.call(this,e)||this;return n.initContainer(),n.initDom(),n.initEvents(),n.initTimeline(),n}return H(e,t),e.prototype.getDefaultCfg=function(){var e=t.prototype.getDefaultCfg.call(this);return e["cursor"]="default",e["supportCSSTransform"]=!1,e},e.prototype.initContainer=function(){var t=this.get("container");Object(i["isString"])(t)&&(t=document.getElementById(t),this.set("container",t))},e.prototype.initDom=function(){var t=this.createDom();this.set("el",t);var e=this.get("container");e.appendChild(t),this.setDOMSize(this.get("width"),this.get("height"))},e.prototype.initEvents=function(){var t=new zt({canvas:this});t.init(),this.set("eventController",t)},e.prototype.initTimeline=function(){var t=new Yt(this);this.set("timeline",t)},e.prototype.setDOMSize=function(t,e){var n=this.get("el");Z&&(n.style.width=t+Gt,n.style.height=e+Gt)},e.prototype.changeSize=function(t,e){this.setDOMSize(t,e),this.set("width",t),this.set("height",e),this.onCanvasChange("changeSize")},e.prototype.getRenderer=function(){return this.get("renderer")},e.prototype.getCursor=function(){return this.get("cursor")},e.prototype.setCursor=function(t){this.set("cursor",t);var e=this.get("el");Z&&e&&(e.style.cursor=t)},e.prototype.getPointByEvent=function(t){var e=this.get("supportCSSTransform");if(e){if(Vt&&!Object(i["isNil"])(t.layerX)&&t.layerX!==t.offsetX)return{x:t.layerX,y:t.layerY};if(!Object(i["isNil"])(t.offsetX))return{x:t.offsetX,y:t.offsetY}}var n=this.getClientByEvent(t),r=n.x,a=n.y;return this.getPointByClient(r,a)},e.prototype.getClientByEvent=function(t){var e=t;return t.touches&&(e="touchend"===t.type?t.changedTouches[0]:t.touches[0]),{x:e.clientX,y:e.clientY}},e.prototype.getPointByClient=function(t,e){var n=this.get("el"),r=n.getBoundingClientRect();return{x:t-r.left,y:e-r.top}},e.prototype.getClientByPoint=function(t,e){var n=this.get("el"),r=n.getBoundingClientRect();return{x:t+r.left,y:e+r.top}},e.prototype.draw=function(){},e.prototype.removeDom=function(){var t=this.get("el");t.parentNode.removeChild(t)},e.prototype.clearEvents=function(){var t=this.get("eventController");t.destroy()},e.prototype.isCanvas=function(){return!0},e.prototype.getParent=function(){return null},e.prototype.destroy=function(){var e=this.get("timeline");this.get("destroyed")||(this.clear(),e&&e.stop(),this.clearEvents(),this.removeDom(),t.prototype.destroy.call(this))},e}(Ct),Zt=Wt,Qt=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return H(e,t),e.prototype.isGroup=function(){return!0},e.prototype.isEntityGroup=function(){return!1},e.prototype.clone=function(){for(var e=t.prototype.clone.call(this),n=this.getChildren(),r=0;r<n.length;r++){var i=n[r];e.add(i.clone())}return e},e}(Ct),Ut=Qt,Jt=function(t){function e(e){return t.call(this,e)||this}return H(e,t),e.prototype._isInBBox=function(t,e){var n=this.getBBox();return n.minX<=t&&n.maxX>=t&&n.minY<=e&&n.maxY>=e},e.prototype.afterAttrsChange=function(e){t.prototype.afterAttrsChange.call(this,e),this.clearCacheBBox()},e.prototype.getBBox=function(){var t=this.cfg.bbox;return t||(t=this.calculateBBox(),this.set("bbox",t)),t},e.prototype.getCanvasBBox=function(){var t=this.cfg.canvasBBox;return t||(t=this.calculateCanvasBBox(),this.set("canvasBBox",t)),t},e.prototype.applyMatrix=function(e){t.prototype.applyMatrix.call(this,e),this.set("canvasBBox",null)},e.prototype.calculateCanvasBBox=function(){var t=this.getBBox(),e=this.getTotalMatrix(),n=t.minX,r=t.minY,i=t.maxX,a=t.maxY;if(e){var o=nt(e,[t.minX,t.minY]),s=nt(e,[t.maxX,t.minY]),h=nt(e,[t.minX,t.maxY]),u=nt(e,[t.maxX,t.maxY]);n=Math.min(o[0],s[0],h[0],u[0]),i=Math.max(o[0],s[0],h[0],u[0]),r=Math.min(o[1],s[1],h[1],u[1]),a=Math.max(o[1],s[1],h[1],u[1])}var c=this.attrs;if(c.shadowColor){var p=c.shadowBlur,l=void 0===p?0:p,f=c.shadowOffsetX,g=void 0===f?0:f,v=c.shadowOffsetY,y=void 0===v?0:v,m=n-l+g,d=i+l+g,x=r-l+y,b=a+l+y;n=Math.min(n,m),i=Math.max(i,d),r=Math.min(r,x),a=Math.max(a,b)}return{x:n,y:r,minX:n,minY:r,maxX:i,maxY:a,width:i-n,height:a-r}},e.prototype.clearCacheBBox=function(){this.set("bbox",null),this.set("canvasBBox",null)},e.prototype.isClipShape=function(){return this.get("isClipShape")},e.prototype.isInShape=function(t,e){return!1},e.prototype.isOnlyHitBox=function(){return!1},e.prototype.isHit=function(t,e){var n=this.get("startArrowShape"),r=this.get("endArrowShape"),i=[t,e,1];i=this.invertFromMatrix(i);var a=i[0],o=i[1],s=this._isInBBox(a,o);if(this.isOnlyHitBox())return s;if(s&&!this.isClipped(a,o)){if(this.isInShape(a,o))return!0;if(n&&n.isHit(a,o))return!0;if(r&&r.isHit(a,o))return!0}return!1},e}(vt),$t=Jt,Kt=new Map;function te(t,e){Kt.set(t,e)}function ee(t){return Kt.get(t)}var ne=function(t){var e=t.attr(),n=e.x,r=e.y,i=e.width,a=e.height;return{x:n,y:r,width:i,height:a}},re=function(t){var e=t.attr(),n=e.x,r=e.y,i=e.r;return{x:n-i,y:r-i,width:2*i,height:2*i}},ie=n("1183");function ae(t,e){return t&&e?{minX:Math.min(t.minX,e.minX),minY:Math.min(t.minY,e.minY),maxX:Math.max(t.maxX,e.maxX),maxY:Math.max(t.maxY,e.maxY)}:t||e}function oe(t,e){var n=t.get("startArrowShape"),r=t.get("endArrowShape"),i=null,a=null;return n&&(i=n.getCanvasBBox(),e=ae(e,i)),r&&(a=r.getCanvasBBox(),e=ae(e,a)),e}var se=function(t){for(var e=t.attr(),n=e.points,r=[],i=[],a=0;a<n.length;a++){var o=n[a];r.push(o[0]),i.push(o[1])}var s=ie["f"].getBBoxByArray(r,i),h=s.x,u=s.y,c=s.width,p=s.height,l={minX:h,minY:u,maxX:h+c,maxY:u+p};return l=oe(t,l),{x:l.minX,y:l.minY,width:l.maxX-l.minX,height:l.maxY-l.minY}},he=function(t){for(var e=t.attr(),n=e.points,r=[],i=[],a=0;a<n.length;a++){var o=n[a];r.push(o[0]),i.push(o[1])}return ie["f"].getBBoxByArray(r,i)},ue=null;function ce(){if(!ue){var t=document.createElement("canvas");t.width=1,t.height=1,ue=t.getContext("2d")}return ue}function pe(t,e,n){var r=1;if(Object(i["isString"])(t)&&(r=t.split("\n").length),r>1){var a=le(e,n);return e*r+a*(r-1)}return e}function le(t,e){return e?e-t:.14*t}function fe(t,e){var n=ce(),r=0;if(Object(i["isNil"])(t)||""===t)return r;if(n.save(),n.font=e,Object(i["isString"])(t)&&t.includes("\n")){var a=t.split("\n");Object(i["each"])(a,(function(t){var e=n.measureText(t).width;r<e&&(r=e)}))}else r=n.measureText(t).width;return n.restore(),r}function ge(t){var e=t.fontSize,n=t.fontFamily,r=t.fontWeight,i=t.fontStyle,a=t.fontVariant;return[i,a,r,e+"px",n].join(" ").trim()}var ve=function(t){var e=t.attr(),n=e.x,r=e.y,i=e.text,a=e.fontSize,o=e.lineHeight,s=e.font;s||(s=ge(e));var h,u=fe(i,s);if(u){var c=e.textAlign,p=e.textBaseline,l=pe(i,a,o),f={x:n,y:r-l};c&&("end"===c||"right"===c?f.x-=u:"center"===c&&(f.x-=u/2)),p&&("top"===p?f.y+=l:"middle"===p&&(f.y+=l/2)),h={x:f.x,y:f.y,width:u,height:l}}else h={x:n,y:r,width:0,height:0};return h},ye=n("2ef1");function me(t,e){for(var n=[],r=[],a=[],o=0;o<t.length;o++){var s=t[o],h=s.currentPoint,u=s.params,c=s.prePoint,p=void 0;switch(s.command){case"Q":p=ie["e"].box(c[0],c[1],u[1],u[2],u[3],u[4]);break;case"C":p=ie["b"].box(c[0],c[1],u[1],u[2],u[3],u[4],u[5],u[6]);break;case"A":var l=s.arcParams;p=ie["a"].box(l.cx,l.cy,l.rx,l.ry,l.xRotation,l.startAngle,l.endAngle);break;default:n.push(h[0]),r.push(h[1]);break}p&&(s.box=p,n.push(p.x,p.x+p.width),r.push(p.y,p.y+p.height)),e&&("L"===s.command||"M"===s.command)&&s.prePoint&&s.nextPoint&&a.push(s)}n=n.filter((function(t){return!Number.isNaN(t)&&t!==1/0&&t!==-1/0})),r=r.filter((function(t){return!Number.isNaN(t)&&t!==1/0&&t!==-1/0}));var f=Object(i["min"])(n),g=Object(i["min"])(r),v=Object(i["max"])(n),y=Object(i["max"])(r);if(0===a.length)return{x:f,y:g,width:v-f,height:y-g};for(o=0;o<a.length;o++){s=a[o],h=s.currentPoint;var m=void 0;h[0]===f?(m=de(s,e),f-=m.xExtra):h[0]===v&&(m=de(s,e),v+=m.xExtra),h[1]===g?(m=de(s,e),g-=m.yExtra):h[1]===y&&(m=de(s,e),y+=m.yExtra)}return{x:f,y:g,width:v-f,height:y-g}}function de(t,e){var n=t.prePoint,r=t.currentPoint,a=t.nextPoint,o=Math.pow(r[0]-n[0],2)+Math.pow(r[1]-n[1],2),s=Math.pow(r[0]-a[0],2)+Math.pow(r[1]-a[1],2),h=Math.pow(n[0]-a[0],2)+Math.pow(n[1]-a[1],2),u=Math.acos((o+s-h)/(2*Math.sqrt(o)*Math.sqrt(s)));if(!u||0===Math.sin(u)||Object(i["isNumberEqual"])(u,0))return{xExtra:0,yExtra:0};var c=Math.abs(Math.atan2(a[1]-r[1],a[0]-r[0])),p=Math.abs(Math.atan2(a[0]-r[0],a[1]-r[1]));c=c>Math.PI/2?Math.PI-c:c,p=p>Math.PI/2?Math.PI-p:p;var l={xExtra:Math.cos(u/2-c)*(e/2*(1/Math.sin(u/2)))-e/2||0,yExtra:Math.cos(p-u/2)*(e/2*(1/Math.sin(u/2)))-e/2||0};return l}var xe=function(t){var e=t.attr(),n=e.path,r=e.stroke,i=r?e.lineWidth:0,a=t.get("segments")||Object(ye["d"])(n),o=me(a,i),s=o.x,h=o.y,u=o.width,c=o.height,p={minX:s,minY:h,maxX:s+u,maxY:h+c};return p=oe(t,p),{x:p.minX,y:p.minY,width:p.maxX-p.minX,height:p.maxY-p.minY}},be=function(t){var e=t.attr(),n=e.x1,r=e.y1,i=e.x2,a=e.y2,o=Math.min(n,i),s=Math.max(n,i),h=Math.min(r,a),u=Math.max(r,a),c={minX:o,maxX:s,minY:h,maxY:u};return c=oe(t,c),{x:c.minX,y:c.minY,width:c.maxX-c.minX,height:c.maxY-c.minY}},Me=function(t){var e=t.attr(),n=e.x,r=e.y,i=e.rx,a=e.ry;return{x:n-i,y:r-a,width:2*i,height:2*a}};te("rect",ne),te("image",ne),te("circle",re),te("marker",re),te("polyline",se),te("polygon",he),te("text",ve),te("path",xe),te("line",be),te("ellipse",Me)}}]);