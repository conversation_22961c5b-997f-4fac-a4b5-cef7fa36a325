(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5b8eff3c"],{"2e3b":function(t,e,i){"use strict";i("aabf")},aabf:function(t,e,i){},d7b8:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("dialogComp",{attrs:{hideHeader:!1,needClose:"true",zIndex:"4",draw:!0,right:10,drag:!0,title:t.$t("dialog.attribute.name"),icon:"icon-details",width:360,dragTopOffset:t.dragTopOffset,height:t.attrHeight,type:"detailInfo",top:t.attrTop},on:{close:t.closeDialog},scopedSlots:t._u([{key:"center",fn:function(){return[i("div",{staticClass:"attribute-container"},[i("div",{directives:[{name:"show",rawName:"v-show",value:0===t.detailsData.length,expression:"detailsData.length === 0"}],staticClass:"text-center no-select"},[t._v(t._s(t.$t("dialog.attribute.emptyText")))]),t.detailsData.length>0?i("el-table",{staticStyle:{width:"100%"},attrs:{"header-row-class-name":"attribute-first-row","empty-text":t.$t("dialog.attribute.emptyText"),border:"","span-method":t.arraySpanMethod,"row-class-name":t.arraySpanClass,data:t.detailsData}},[i("el-table-column",{staticStyle:{"text-align":"center"},attrs:{prop:"key",label:t.$t("dialog.attribute.table.label")}}),i("el-table-column",{staticStyle:{"text-align":"center"},attrs:{prop:"val",label:t.$t("dialog.attribute.table.label1")}})],1):t._e()],1)]},proxy:!0}])})},n=[],s=(i("4de4"),i("d3b7"),i("6062"),i("3ca3"),i("ddb0"),i("159b"),i("b0c0"),{name:"Attribute",components:{},data:function(){return{detailsData:[],attrHeight:500,attrTop:170}},created:function(){this.setDialogSize(),window.addEventListener("resize",this.setDialogSize),this.$bus.on("viewshedShowAttribute",this.getSelectedObjects),this.$bus.on("ElementTreeNodeClick",this.getAttributeOfHierarchies)},computed:{dragTopOffset:function(){return this.$store.state.scene.topToolBarHeight}},mounted:function(){this.getAttributeOnmounted(),window.scene.mv.events.pickFinished.on("default",this.getSelectedObjects)},methods:{setDialogSize:function(){var t=document.body.clientHeight,e=document.querySelector(".desktop-view").clientHeight;this.attrHeight=.65*t,this.attrTop=0==e?50:e+10},getAttributeOnmounted:function(){var t=this.$store.state.sundry.attrHierarchiesData;if(null!=t)this.getSelectedObjects(t.id);else{var e=window.scene.getSelection().filter((function(t){return"element"==t.type||"space"==t.type}));if(e.length>0){var i=e[e.length-1];this.getSelectedObjects(i.id)}}},closeDialog:function(){this.$store.commit("toggleActiveDialog","attribute"),this.$store.commit("toggleMenuActive","attribute")},cybtest:function(){},getSelectedObjects:function(t){var e=this;if(void 0!=t){var i=window.scene.findObject(t);i.getProperty().then((function(t){e.setSelectedObjectsProperty(t)}))}},setSelectedObjectsProperty:function(t){var e=this;this.detailsData=[];var i=this.$i18n.locale,a=this.$i18n.getLocaleMessage(i).dialog.attribute.table.labels,n=new Set(a),s=new Set;t.forEach((function(t){s.add(t.group),n.add(t.group)})),n.forEach((function(t){s.has(t)||n.delete(t)})),n.forEach((function(i){e.detailsData.push({key:i,val:"",children:[]}),t.forEach((function(t){t.group===i&&e.detailsData.push({id:t.id,key:t.name,val:t.value,group:t.group})}))}))},arraySpanMethod:function(t){if(!t.row.id)return[1,2]},arraySpanClass:function(t){if(!t.row.id)return"table-title"},getAttributeOfHierarchies:function(t){this.getSelectedObjects(t.id)}},beforeDestroy:function(){window.removeEventListener("resize",this.setDialogSize),window.scene.mv.events.pickFinished.off("default",this.getSelectedObjects),this.$bus.off("viewshedShowAttribute",this.getSelectedObjects),this.$bus.off("ElementTreeNodeClick",this.getAttributeOfHierarchies)}}),o=s,r=(i("2e3b"),i("2877")),l=Object(r["a"])(o,a,n,!1,null,"1f2506fd",null);e["default"]=l.exports}}]);