import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'

Vue.config.productionTip = false
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
Vue.use(ElementUI)

import * as signalR from '@microsoft/signalr'
Vue.prototype.$signalR = signalR;

import api from './api'
Vue.prototype.$api = api

import qs from 'qs'
Vue.prototype.$qs = qs;

import axios from 'axios'
Vue.prototype.$axios = axios;

import md5 from 'js-md5'
Vue.prototype.$md5 = md5;


import 'animate.css';
require('./style/reset.scss')

Vue.prototype.$ip = baseURL => {
    if (process.env.NODE_ENV === 'production') {
        return window.IP_CONFIG[baseURL]
    } else {
        return require('./config/proxyIP')[baseURL]
    }
}
new Vue({
    router,
    store,
    render: h => h(App)
}).$mount('#app')



