import Vue from 'vue'
import Vuex from 'vuex'
import createPersistedState from 'vuex-persistedstate'

Vue.use(Vuex)

export default new Vuex.Store({
    state: {
        userName: '你好',
        activeRouter:'/earlyManage/projectPanel',
        activeLayout:'',
        tabIndex: '',
        tableArray:[
            {
                name: '建设期综合展示',
                index: 1,
                active: true,
                router:'/buildingPanel'
            },
            {
                name: '前期管理',
                index: 2,
                active: false,
                router:'/earlyManage'
            },
            {
                name: '建设管理',
                index: 3,
                active: false,
                router:'/buildManage'
            }
        ],
        auList:[],
        panelAuList:[],
        earlyAuList:[],
        taskAuList:[],
        earlyActiveIndex:{},
        projectId:'',
        account:'',
        userId:''
    },
    mutations: {
        setUserName(state,userName) {
            state.userName = userName
        },
        setActiveRouter(state,activeRouter) {
            state.activeRouter = activeRouter
      },
        setActiveLayout(state,activeLayout) {
            state.activeLayout = activeLayout
        },
        setTabIndex(state,tabIndex) {
            state.tabIndex = tabIndex
        },
        setTableArray(state,tableArray) {
            state.tableArray = tableArray
        },
        setAuList(state,auList) {
            state.auList = auList
        },
        setPanelAuList(state,panelAuList) {
            state.panelAuList = panelAuList
        },
        setEarlyAuList(state,earlyAuList) {
            state.earlyAuList = earlyAuList
        },
        setTaskAuList(state,taskAuList) {
            state.taskAuList = taskAuList
        },
        setEarlyActiveIndex(state,earlyActiveIndex) {
            state.earlyActiveIndex = earlyActiveIndex
        },
        setProjectId(state,projectId) {
            state.projectId = projectId
        },
        setAccount(state,account) {
            state.account = account
        },
        setUserId(state, userId) {
            state.userId = userId
        }
    },
    actions: {
        updateUserName({ commit }, userName) {
          commit('setUserName', userName)
        },
        updateActiveRouter({ commit },activeRouter) {
          commit('setActiveRouter', activeRouter)
      },
        updateActiveLayout({ commit }, activeLayout) {
            commit('setActiveLayout', activeLayout)
        },
        updateTabIndex({ commit }, tabIndex) {
            commit('setTabIndex', tabIndex)
        },
        updateTableArray({ commit }, tableArray) {
            commit('setTableArray', tableArray)
        },
        updateAuList({ commit }, auList) {
            commit('setAuList', auList)
        },
        updatePanelAuList({ commit }, panelAuList) {
            commit('setPanelAuList', panelAuList)
        },
        updateEarlyAuList({ commit }, earlyAuList) {
            commit('setEarlyAuList', earlyAuList)
        },
        updateTaskAuList({ commit }, taskAuList) {
            commit('setTaskAuList', taskAuList)
        },
        updateEarlyActiveIndex({ commit }, earlyActiveIndex) {
            commit('setEarlyActiveIndex', earlyActiveIndex)
        },
        updateProjectId({ commit }, projectId) {
            commit('setProjectId', projectId)
        },
        updateAccount({ commit }, account) {
            commit('setAccount', account)
        },
        updateUserId({ commit }, userId) {
            commit('setUserId', userId)
        }
    },
    plugins: [createPersistedState({
        storage: window.sessionStorage
    })]
})
