import axios from 'axios'
import {getToken, removeToken} from './token.js'

// 允许携带cookie
// axios.defaults.withCredentials = true

// create an axios instance
const http = axios.create()

const pending = []
const removePending = config => {
  for (const p in pending) {
    if (pending[p].u === config.url + '&' + config.method) {
      pending[p].fn()
      pending.splice(p, 1)
    }
  }
}

// request interceptor
http.interceptors.request.use(
  config => {
    if (config.isToken) {
      config.headers.Token = `${getToken('token')}`
    }
    if (!config.isLoop) {
      removePending(config)
      config.cancelToken = new axios.CancelToken(fn => {
        pending.push({ u: config.url + '&' + config.method, fn })
      })
    }
    let url = config.url
    url += url.indexOf('?') >= 0 ? '&' : '?'
    url += 'r=' + (Math.random() * 100000 + 1)
    config.url = url
    return config
  },
  err => {
    return Promise.reject(err)
  }
)

// respone interceptor
http.interceptors.response.use(
  response => {
    if (!response.config.isLoop) {
      removePending(response.config)
    }
    return response
  },
  error => {
    return Promise.reject(error)
  }
)

/**
 * @param {String} url 请求地址
 * @param {String} method 请求方式
 * @param {Object} params 参数
 * @param {String} baseURL 请求头
 * @param {Object} headers 请求头配置
 * @param {Number} timeout 请求时间
 * @param {Boolean} isToken 请求是否携带token
 * @param {Boolean} isLoop 接口是否轮询而不被取消
 * @param {Boolean} isBlob 接口是否为BLOB
 * @return {AxiosPromise}
 */
export const request = async ({ url, method = 'get', params, baseURL = 'BASE_URL', headers = {}, timeout, isToken = url.indexOf('/Vault') !== 0, isLoop, isError = false, isBlob = false }) => {
  return new Promise((resolve, reject) => {
    try {
      const result = http({
        method,
        url: process.env.NODE_ENV === 'production' ? isError ? url : `${window.IP_CONFIG[baseURL]}${url}` : `${window.IP_CONFIG[baseURL]}${url}`,
        params: (method === 'get' || method === 'delete') && params,
        data: ((method === 'post' || method === 'put') && params) || null,
        headers: {
          ...headers
        },
        responseType: isBlob ? 'blob' : '',
        timeout: timeout || 30000,
        isToken: isToken,
        isLoop: isLoop || false
      })
        result.then((res) => {
           if (res.status === 200) {
               if (res.data.Ret === -2) {
                   removeToken()
                   window.location.href = 'http://**************:8080/hfzss_mh/#/home'
               }else if (res.data.Ret === -1 && res.data.Msg === 'Token错误'){
                   removeToken()
                   window.location.href = 'http://**************:8080/hfzss_mh/#/home'
               }
           }
        })
      resolve(result)
    } catch (error) {
      reject(error)
    }
  })
}
