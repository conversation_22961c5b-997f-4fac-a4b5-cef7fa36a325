module.exports = {
  plugins: {
    'postcss-px-to-viewport': {
        exclude: [/node_modules/],
        unitToConvert: 'px',
        viewportWidth: 1920,
        unitPrecision: 3,
        selectorBlackList: ['.ignore'],
        viewportUnit: 'vw',
        fontViewportUnit: 'vw',
        minPixelValue: 1,
        mediaQuery: true,
        propList: ['*'] //默认情况下 postcss-px-to-viewport 只会转换纯像素值,如果要转换计算值,需要使用 propList 选项进行额外配置
      }
    
  }
}
