const path = require('path')
const resolve = dir => path.join(__dirname, dir)
const isProduction = process.env.NODE_ENV === 'production'
const proxyIp = require('./src/config/proxyIP')
const proxy = {}
for (const key in proxyIp) {
    proxy[`/${key}`] = {
      target: proxyIp[key],
      changeOrigin: true,
      secure: true,
      pathRewrite: {
        [`^/${key}`]: ''
      }
    }
  }
const setVersion = process.env.npm_package_config_buildVersion
module.exports = {
    publicPath: './',
    lintOnSave: !isProduction,
    devServer: {
        open: true,
        host: '0.0.0.0',
        port: 8081,
    },
    configureWebpack: {
        devtool: 'source-map',
        output: {
            filename: `js/[name].${setVersion}.js`,
            chunkFilename: `js/[name].${setVersion}.js`
        },
        optimization: {
            runtimeChunk: 'single',
            splitChunks: {
                chunks: 'all',
                minSize: 20000,
                maxSize: 244000,
                cacheGroups: {
                    vendor: {
                        test: /[\\/]node_modules[\\/]/,
                        name: 'vendors',
                        chunks: 'all',
                    },
                },
            },
        },
    },
    css: {
        extract: {
            filename: `css/[name].${setVersion}.css`,
            chunkFilename: `css/[name].${setVersion}.css`
        }
    }
};