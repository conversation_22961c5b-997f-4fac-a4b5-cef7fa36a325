{"name": "huanbei-dashboard", "version": "1.1.0", "config": {"buildVersion": "20250818.1f9693b"}, "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@antv/g6": "^4.8.24", "@antv/hierarchy": "^0.6.11", "@antv/x6": "^2.18.1", "@microsoft/signalr": "6.0.1", "animate.css": "^4.1.1", "axios": "1.6.8", "core-js": "^3.6.5", "crypto-js": "^4.2.0", "cryptojs": "^2.5.3", "echarts": "^5.5.0", "element-ui": "^2.15.14", "js-md5": "^0.8.3", "qs": "^6.13.0", "vue": "^2.7.16", "vue-router": "^3.6.5", "vuex": "^3.6.2", "vuex-persistedstate": "^4.1.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.13", "@vue/cli-plugin-eslint": "~4.5.13", "@vue/cli-plugin-router": "~4.5.13", "@vue/cli-plugin-vuex": "~4.5.13", "@vue/cli-service": "~4.5.13", "@vue/compiler-sfc": "^3.0.0", "autoprefixer": "^8.0.0", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^7.0.0", "postcss": "8.4.38", "postcss-loader": "^6.1.1", "postcss-px-to-viewport": "1.1.1", "postcss-scss": "4.0.4", "sass": "^1.26.5", "sass-loader": "^8.0.2", "svg-sprite-loader": "^6.0.11", "unplugin-vue-components": "^0.25.2"}}